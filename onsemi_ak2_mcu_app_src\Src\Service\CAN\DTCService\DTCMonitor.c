/**********************************************************************
*   版权所有    : 2019 深圳市豪恩汽车电子装备有限公司
*     项目名称  : LVDS 长城F7 AVM
*     M      C       U         : R5F10BBF
*     编译环境  : IAR
*   文件名称    : DTCMonitor.c
*   其它说明    : 系统故障检测
*   当前版本    : V0.1
*   作    者  : 20460
*   完成日期    :
*   内容摘要    :
*   修改记录1   :
*   修改日期    :
*   版 本 号 :
*   修 改 人 :
*   修改内容    :
*   修改记录2   :

***********************************************************************/
/**********************************************************************
* Includes 
**********************************************************************/
#include "DTCMonitor.h"
#include "DTC_Cfg.h"
#include "DTCRecordManage.h"
#include "CAN_UDS.h"
#include "CAN_COM.h"
#include "CAN_IL.h"
#include "DID.h"
#include "TimerManage.h"
#include "ODO_AppSignalManage.h"
#include "PowerSingalManage.h"
//#include "PM_Cfg.h"
#include "SnsDiag.h"
#include "DSI3_COM.h"
#include "DSI3_SPI.h"
#include "PAS_MAP_SignalManage.h"
/*********************************************************************
* Global objects 
**********************************************************************/


static uint8 GcHighVoltageTestResult;       /*高压错误监测结果，传给PowerDTCJudge判断电压DTC*/
static uint8 GcLowVoltageTestResult;       /*低压错误监测结果，传给PowerDTCJudge判断电压DTC*/

volatile uint8 GcBusOffTestResult;          /*总线错误监测结果，外部接口，记录DTC*/
bool LblRCM_WATCH_DOGFlag=FALSE;

uint8 Gu8SendDTC_Buffer[TOTAL_DTC_RECORD_NUMBER86] = {0};
uint16 Gu16EPSMonitorDelayTime=0;
/*Tx message 0x665 will send the DTC number as the DTC list defined in LEEA_X01 project*/
const uint8 Gu8SendDTCNumOffset[TOTAL_DTC_RECORD_NUMBER86] =
{
    1, 2, 3,
    6, 7, 8, 9, 10,11,12,
    13,14,15,16,//17,
    18,
    19,20,21,22,23,24,
    25,26,27,28,29,30,

    31,32,33,36,//35,34,
    37,38,39,42,//41,44,
    43,44,45,48,//47,46,
    49,50,51,54,//53,52
    55,56,57,60,//59,58,
    61,62,63,66,//65,64,
    67,68,69,72,//71,70,
    73,74,75,78,//77,76,
    79,80,81,84,//83,82,
    85,86,87,90,//89,88,
    91,92,93,96,//95,94,
    97,98,99,102,//101,100,
    //103,104,105,106,
    107,108,109,
    4, 5,103,104,105,106,122,123
};

uint8 Gu8SendDTC_Cnt = 0;
uint8 Gu8DTC_send_complete_flag = 0;

/*延迟诊断定时器，在上电之初或者从异常电压，busoff恢复之后，需要延时记录DTC*/
uint16 GwWaitStartDiagDTCTimer = DELAY_1500MS;
uint32 Gu32WaitStartDiagNodeDTCTimer = 0;
uint32 Gu32WaitStartBusoffDTCTimer = 0;

sint8 DataOffSet[12]={8,-1,-1,-1,-1,4,5,0,-2,-4,-6,-1};

/**********************************************************************
*   函数名称:   SetDTCStatusFunction
*
*   功能描述:   设置DTC的状态
*
*   输入参考:   无
*
*   输出参数:   无
*
*   返 回 值:    无
*
*   其它说明:
*
*   修改日期        版本号       修改人       修改内容
*
**********************************************************************/
uint8 SetDTCStatusFunction(uint8 DTCNumber,DTC_StatusType LcDTCStatus)
{
    uint8 LcState = DTC_INIT_00;
    
    if(DTCNumber < TOTAL_DTC_RECORD_NUMBER86)
    {

        GcDTCDetectionStatus[DTCNumber] = LcDTCStatus;

    }
    else
    {
        LcState = DTC_INPUTE_PARA_ERROR_01;
    }

    return LcState;
}
/*********************************************************************
* 函数名称: Missing_ACU_DTC
*
* 功能描述: 
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:  
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
void Missing_ACU_DTC(void)
{

    if((NCS_TST_BIT_SET(GcFrameStatus[ID236Idx],COM_FRAME_STATUS_RX_TIMEOUT,uint8))||\
        (NCS_TST_BIT_SET(GcFrameStatus[ID237Idx],COM_FRAME_STATUS_RX_TIMEOUT,uint8)))
    {
        SetDTCStatusFunction(DTC_MISSING_ACU,TESTFAIL);       
    }
    else 
    {
        SetDTCStatusFunction(DTC_MISSING_ACU,TESTPASS);           
    }
}
/*********************************************************************
* 函数名称: Missing_ADAS_DTC
*
* 功能描述: 
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:  
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
void Missing_ADAS_DTC(void)
{

    if((NCS_TST_BIT_SET(GcFrameStatus[ID395Idx],COM_FRAME_STATUS_RX_TIMEOUT,uint8))||\
        (NCS_TST_BIT_SET(GcFrameStatus[ID2D4Idx],COM_FRAME_STATUS_RX_TIMEOUT,uint8)))
    {
        SetDTCStatusFunction(DTC_MISSING_ADAS,TESTFAIL);       
    }
    else
    {
        SetDTCStatusFunction(DTC_MISSING_ADAS,TESTPASS);           
    }
}
/*********************************************************************
* 函数名称: Missing_FBCM_DTC
*
* 功能描述: 
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:  
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
void Missing_FBCM_DTC(void)
{

    if((NCS_TST_BIT_SET(GcFrameStatus[ID209Idx],COM_FRAME_STATUS_RX_TIMEOUT,uint8)))
    {
        SetDTCStatusFunction(DTC_MISSING_FBCM,TESTFAIL);       
    }
    else 
    {
        SetDTCStatusFunction(DTC_MISSING_FBCM,TESTPASS);           
    }
}
/*********************************************************************
* 函数名称: Missing_EPS_DTC
*
* 功能描述: 
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:  
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
void Missing_EPS_DTC(void)
{

    if((NCS_TST_BIT_SET(GcFrameStatus[ID176Idx],COM_FRAME_STATUS_RX_TIMEOUT,uint8)))
    {
        SetDTCStatusFunction(DTC_MISSING_EPS,TESTFAIL);       
    }
    else
    {
        SetDTCStatusFunction(DTC_MISSING_EPS,TESTPASS);           
    }
}
/*********************************************************************
* 函数名称: Missing_ESP_DTC
*
* 功能描述: 
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:  
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
void Missing_ESP_DTC(void)
{

    if((NCS_TST_BIT_SET(GcFrameStatus[ID086Idx],COM_FRAME_STATUS_RX_TIMEOUT,uint8))||\
        (NCS_TST_BIT_SET(GcFrameStatus[ID096Idx],COM_FRAME_STATUS_RX_TIMEOUT,uint8))||\
        (NCS_TST_BIT_SET(GcFrameStatus[ID0D6Idx],COM_FRAME_STATUS_RX_TIMEOUT,uint8)))
    {
        SetDTCStatusFunction(DTC_MISSING_ESP,TESTFAIL);       
    }
    else
    {
        SetDTCStatusFunction(DTC_MISSING_ESP,TESTPASS);           
    }

}
/*********************************************************************
* 函数名称: Missing_HU_DTC
*
* 功能描述: 
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:  
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
void Missing_HU_DTC(void)
{

    if((NCS_TST_BIT_SET(GcFrameStatus[ID39DIdx],COM_FRAME_STATUS_RX_TIMEOUT,uint8))||\
        (NCS_TST_BIT_SET(GcFrameStatus[ID09EIdx],COM_FRAME_STATUS_RX_TIMEOUT,uint8)))
    {
        SetDTCStatusFunction(DTC_MISSING_HU,TESTFAIL);       
    }
    else
    {
        SetDTCStatusFunction(DTC_MISSING_HU,TESTPASS);           
    }

}
/*********************************************************************
* 函数名称: Missing_XCU_DTC
*
* 功能描述: 
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:  
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
void Missing_XCU_DTC(void)
{

    if((NCS_TST_BIT_SET(GcFrameStatus[ID397Idx],COM_FRAME_STATUS_RX_TIMEOUT,uint8))||\
        (NCS_TST_BIT_SET(GcFrameStatus[ID0C2Idx],COM_FRAME_STATUS_RX_TIMEOUT,uint8)))
    {
        SetDTCStatusFunction(DTC_MISSING_XCU,TESTFAIL);       
    }
    else
    {
        SetDTCStatusFunction(DTC_MISSING_XCU,TESTPASS);           
    }
}

/*********************************************************************
* 函数名称: CAN_Invalid_ACUData 
*
* 功能描述: ACU数据无效
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:  无
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
void CAN_Invalid_ACUData(void)
{
    if((GstrNodeErrCnt[ID236Idx].Signal_ValidCnt >= 5)&&(GstrNodeErrCnt[ID237Idx].Signal_ValidCnt >= 5))
    {
        SetDTCStatusFunction(DTC_INVALID_ACU_DATA,TESTPASS );     
    }
    else if((GstrNodeErrCnt[ID236Idx].Signal_InvalidCnt >= 10) ||\
            (GstrNodeErrCnt[ID237Idx].Signal_InvalidCnt >= 10))
    {
        SetDTCStatusFunction(DTC_INVALID_ACU_DATA,TESTFAIL);         
    }  
    else
    {

    }
}
/*********************************************************************
* 函数名称: CAN_Invalid_ADASData 
*
* 功能描述: 
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:  无
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
void CAN_Invalid_ADASData (void)
{
    if(GstrNodeErrCnt[ID2D4Idx].Signal_ValidCnt >= 5)
    {
        SetDTCStatusFunction(DTC_INVALID_ADAS_DATA,TESTPASS );     
    }
    else if(GstrNodeErrCnt[ID2D4Idx].Signal_InvalidCnt >= 10)
    {
        SetDTCStatusFunction(DTC_INVALID_ADAS_DATA,TESTFAIL);         
    }  
    else
    {

    }
}
/*********************************************************************
* 函数名称: CAN_Invalid_ADASData 
*
* 功能描述: 
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:  无
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
void CAN_Invalid_EPSData  (void)
{
    if(GstrNodeErrCnt[ID176Idx].Signal_ValidCnt >= 5)
    {
        SetDTCStatusFunction(DTC_INVALID_EPS_DATA,TESTPASS );     
    }
    else if(GstrNodeErrCnt[ID176Idx].Signal_InvalidCnt >= 10)
    {
        SetDTCStatusFunction(DTC_INVALID_EPS_DATA,TESTFAIL);         
    }  
    else
    {

    }
}
/*********************************************************************
* 函数名称: CAN_Invalid_ESPData  
*
* 功能描述: 
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:  无
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
void CAN_Invalid_ESPData  (void)
{
    if((GstrNodeErrCnt[ID086Idx].Signal_ValidCnt >= 5)&&\
       (GstrNodeErrCnt[ID096Idx].Signal_ValidCnt >= 5)&&\
       (GstrNodeErrCnt[ID0D6Idx].Signal_ValidCnt >= 5))
    {
        SetDTCStatusFunction(DTC_INVALID_ESP_DATA,TESTPASS );     
    }
    else if((GstrNodeErrCnt[ID086Idx].Signal_InvalidCnt >= 10)||\
        (GstrNodeErrCnt[ID096Idx].Signal_InvalidCnt >= 10)||\
        (GstrNodeErrCnt[ID0D6Idx].Signal_InvalidCnt >= 10))
    {
        SetDTCStatusFunction(DTC_INVALID_ESP_DATA,TESTFAIL);         
    } 
    else
    {

    }
}
/*********************************************************************
* 函数名称: CAN_Invalid_HUData  
*
* 功能描述: 
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:  无
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
#if 0
void CAN_Invalid_HUData(void)
{
    if((GstrNodeErrCnt[ID39DIdx].Signal_ValidCnt >= 5)&&(GstrNodeErrCnt[ID09EIdx].Signal_ValidCnt >= 5))
    {
        SetDTCStatusFunction(DTC_INVALID_HU_DATA,TESTPASS );     
    }
    else if((GstrNodeErrCnt[ID39DIdx].Signal_InvalidCnt >= 10)||(GstrNodeErrCnt[ID09EIdx].Signal_InvalidCnt >= 10))
    {
        SetDTCStatusFunction(DTC_INVALID_HU_DATA,TESTFAIL);         
    }
    else
    {

    }
}
#endif
/*********************************************************************
* 函数名称: CAN_Invalid_XCUData  
*
* 功能描述: 
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:  无
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
void CAN_Invalid_XCUData(void)
{
    if((GstrNodeErrCnt[ID397Idx].Signal_ValidCnt >= 5)&&(GstrNodeErrCnt[ID0C2Idx].Signal_ValidCnt >= 5))
    {
        SetDTCStatusFunction(DTC_INVALID_XCU_DATA,TESTPASS );     
    }
    else if((GstrNodeErrCnt[ID397Idx].Signal_InvalidCnt >= 10)||(GstrNodeErrCnt[ID0C2Idx].Signal_InvalidCnt >= 10))
    {
        SetDTCStatusFunction(DTC_INVALID_XCU_DATA,TESTFAIL);         
    }  
    else
    {

    }
}

/*********************************************************************
* 函数名称: CAN_Invalid_FBCMChecksum  
*
* 功能描述: 
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:  无
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
void CAN_Invalid_FBCMChecksum(void)
{
    if(GstrNodeErrCnt[ID209Idx].Checksum_ValidCnt >= 3)
    {
        SetDTCStatusFunction(DTC_INVALID_FBCM_CHECK,TESTPASS );     
    }
    else if(GstrNodeErrCnt[ID209Idx].Checksum_InvalidCnt >= 5)
    {
        SetDTCStatusFunction(DTC_INVALID_FBCM_CHECK,TESTFAIL);         
    }
    else
    {

    }
}
/*********************************************************************
* 函数名称: CAN_Invalid_FBCMCounter  
*
* 功能描述: 
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:  无
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
void CAN_Invalid_FBCMCounter(void)
{
    if((GstrNodeErrCnt[ID209Idx].Counter_ValidCnt >= 3))
    {
        SetDTCStatusFunction(DTC_INVALID_FBCM_COUNTER,TESTPASS );     
    }
    else if(GstrNodeErrCnt[ID209Idx].Counter_InvalidCnt >= 5)
    {
        SetDTCStatusFunction(DTC_INVALID_FBCM_COUNTER,TESTFAIL);         
    }  
    else
    {

    }
}
/*********************************************************************
* 函数名称: CAN_Invalid_ACUChecksum  
*
* 功能描述: 
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:  无
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
void CAN_Invalid_ACUChecksum(void)
{
    if((GstrNodeErrCnt[ID236Idx].Checksum_ValidCnt >= 3)&&(GstrNodeErrCnt[ID237Idx].Checksum_ValidCnt >= 3))
    {
        SetDTCStatusFunction(DTC_INVALID_ACU_CHECK,TESTPASS );     
    }
    else if((GstrNodeErrCnt[ID236Idx].Checksum_InvalidCnt >= 5)||(GstrNodeErrCnt[ID237Idx].Checksum_InvalidCnt >= 5))
    {
        SetDTCStatusFunction(DTC_INVALID_ACU_CHECK,TESTFAIL);         
    }  
    else
    {

    }
}
/*********************************************************************
* 函数名称: CAN_Invalid_ACUCounter  
*
* 功能描述: 
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:  无
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
void CAN_Invalid_ACUCounter(void)
{
    if((GstrNodeErrCnt[ID236Idx].Counter_ValidCnt >= 3)&&(GstrNodeErrCnt[ID237Idx].Counter_ValidCnt >= 3))
    {
        SetDTCStatusFunction(DTC_INVALID_ACU_COUNTER,TESTPASS );     
    }
    else if((GstrNodeErrCnt[ID236Idx].Counter_InvalidCnt >= 5)||(GstrNodeErrCnt[ID237Idx].Counter_InvalidCnt >= 5))
    {
        SetDTCStatusFunction(DTC_INVALID_ACU_COUNTER,TESTFAIL);         
    }  
    else
    {

    }
}
/*********************************************************************
* 函数名称: CAN_Invalid_ADASChecksum  
*
* 功能描述: 
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:  无
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
void CAN_Invalid_ADASChecksum(void)
{
    if((GstrNodeErrCnt[ID395Idx].Checksum_ValidCnt >= 3)&&(GstrNodeErrCnt[ID2D4Idx].Checksum_ValidCnt >= 3))
    {
        SetDTCStatusFunction(DTC_INVALID_ADAS_CHECK,TESTPASS );     
    }
    else if((GstrNodeErrCnt[ID395Idx].Checksum_InvalidCnt >= 5)||(GstrNodeErrCnt[ID2D4Idx].Checksum_InvalidCnt >= 5))
    {
        SetDTCStatusFunction(DTC_INVALID_ADAS_CHECK,TESTFAIL);         
    } 
    else
    {

    }
}
/*********************************************************************
* 函数名称: CAN_Invalid_ADASCounter  
*
* 功能描述: 
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:  无
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
void CAN_Invalid_ADASCounter(void)
{
    if((GstrNodeErrCnt[ID395Idx].Counter_ValidCnt >= 3)&&(GstrNodeErrCnt[ID2D4Idx].Counter_ValidCnt >= 3))
    {
        SetDTCStatusFunction(DTC_INVALID_ADAS_COUNTER,TESTPASS );     
    }
    else if((GstrNodeErrCnt[ID395Idx].Counter_InvalidCnt >= 5)||(GstrNodeErrCnt[ID2D4Idx].Counter_InvalidCnt >= 5))
    {
        SetDTCStatusFunction(DTC_INVALID_ADAS_COUNTER,TESTFAIL);         
    }  
}
/*********************************************************************
* 函数名称: CAN_Invalid_EPSChecksum  
*
* 功能描述: 
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:  无
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
void CAN_Invalid_EPSChecksum(void)
{
    if(GstrNodeErrCnt[ID176Idx].Checksum_ValidCnt >= 3)
    {
        SetDTCStatusFunction(DTC_INVALID_EPS_CHECK,TESTPASS );     
    }
    else if(GstrNodeErrCnt[ID176Idx].Checksum_InvalidCnt >= 5)
    {
        SetDTCStatusFunction(DTC_INVALID_EPS_CHECK,TESTFAIL);         
    }  
    else
    {

    }
}
/*********************************************************************
* 函数名称: CAN_Invalid_ADASCounter  
*
* 功能描述: 
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:  无
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
void CAN_Invalid_EPSCounter(void)
{
    if(GstrNodeErrCnt[ID176Idx].Counter_ValidCnt >= 3)
    {
        SetDTCStatusFunction(DTC_INVALID_EPS_COUNTER,TESTPASS );     
    }
    else if(GstrNodeErrCnt[ID176Idx].Counter_InvalidCnt >= 5)
    {
        SetDTCStatusFunction(DTC_INVALID_EPS_COUNTER,TESTFAIL);         
    }
    else
    {

    }
}

/*********************************************************************
* 函数名称: CAN_Invalid_ESPChecksum  
*
* 功能描述: 
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:  无
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
void CAN_Invalid_ESPChecksum(void)
{
    if((GstrNodeErrCnt[ID086Idx].Checksum_ValidCnt >= 3)&&\
        (GstrNodeErrCnt[ID096Idx].Checksum_ValidCnt >= 3)&&\
        (GstrNodeErrCnt[ID0D6Idx].Checksum_ValidCnt >= 3))
    {
        SetDTCStatusFunction(DTC_INVALID_ESP_CHECK,TESTPASS );     
    }
    else if((GstrNodeErrCnt[ID086Idx].Checksum_InvalidCnt >= 5)||\
        (GstrNodeErrCnt[ID096Idx].Checksum_InvalidCnt >= 5)||\
        (GstrNodeErrCnt[ID0D6Idx].Checksum_InvalidCnt >= 5))
    {
        SetDTCStatusFunction(DTC_INVALID_ESP_CHECK,TESTFAIL);         
    }  
    else
    {

    }
}
/*********************************************************************
* 函数名称: CAN_Invalid_ESPCounter  
*
* 功能描述: 
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:  无
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
void CAN_Invalid_ESPCounter(void)
{
    if((GstrNodeErrCnt[ID086Idx].Counter_ValidCnt >= 3)&&\
        (GstrNodeErrCnt[ID096Idx].Counter_ValidCnt >= 3)&&\
        (GstrNodeErrCnt[ID0D6Idx].Counter_ValidCnt >= 3))
    {
        SetDTCStatusFunction(DTC_INVALID_ESP_COUNTER,TESTPASS );     
    }
    else if((GstrNodeErrCnt[ID086Idx].Counter_InvalidCnt >= 5)||\
        (GstrNodeErrCnt[ID096Idx].Counter_InvalidCnt >= 5)||\
        (GstrNodeErrCnt[ID0D6Idx].Counter_InvalidCnt >= 5))
    {
        SetDTCStatusFunction(DTC_INVALID_ESP_COUNTER,TESTFAIL);         
    }
    else
    {

    }
}
/*********************************************************************
* 函数名称: CAN_Invalid_XCUChecksum  
*
* 功能描述: 
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:  无
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
void CAN_Invalid_XCUChecksum(void)
{
    if(GstrNodeErrCnt[ID0C2Idx].Checksum_ValidCnt >= 3)
    {
        SetDTCStatusFunction(DTC_INVALID_XCU_CHECK,TESTPASS );     
    }
    else if(GstrNodeErrCnt[ID0C2Idx].Checksum_InvalidCnt >= 5)
    {
        SetDTCStatusFunction(DTC_INVALID_XCU_CHECK,TESTFAIL);         
    } 
    else
    {

    }
}
/*********************************************************************
* 函数名称: CAN_Invalid_XCUCounter  
*
* 功能描述: 
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:  无
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
void CAN_Invalid_XCUCounter(void)
{

    if((GstrNodeErrCnt[ID0C2Idx].Counter_ValidCnt >= 3))
    {
        SetDTCStatusFunction(DTC_INVALID_XCU_COUNTER,TESTPASS );     
    }
    else if(GstrNodeErrCnt[ID0C2Idx].Counter_InvalidCnt >= 5)
    {
        SetDTCStatusFunction(DTC_INVALID_XCU_COUNTER,TESTFAIL);         
    }
    else
    {

    }
}

/*********************************************************************
* 函数名称: CANBusoffDTC
*
* 功能描述: 总线关闭DTC
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:  无
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
void CANBusoffDTC(void)
{
    if(GcBusOffTestResult == TESTFAIL)
    {
        SetDTCStatusFunction(DTC_CAN_BUSOFF_FAIL,TESTFAIL); 
        Gu32WaitStartDiagNodeDTCTimer = DELAY_1500MS;
    }
    else if(GcBusOffTestResult == TESTPASS)
    {
        SetDTCStatusFunction(DTC_CAN_BUSOFF_FAIL,TESTPASS);         
    }
    else
    {
        ;
    }
}

//bool GetRCM_WATCH_DOG(void)
//{
//   return POWER_SYS_GetResetSrcStatusCmd(RCM, RCM_WATCH_DOG);
//}
/*********************************************************************
* 函数名称: ODOErrorDTC
*
* 功能描述: 读取ODO模块相关故障
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:  无
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/

void ODOErrorDTC(void)
{
    ODOCoorInfo LstrODOInfo;
    Car_SpeedType LpvCar_Speed;
    static uint8 Lu8OdoTaskCnt = 0;
    static uint8 Lu8OdoErrorCnt = 0;
    static float LfOdo_X_Backup = 0;
    static float LfOdo_Y_Backup = 0;
    float LfX_Sub,LfY_Sub;
	static uint32 ODO_GdSystemMsTimer = 0;
	static uint8 DTCValidFlag = VALID;
	Car_GearType LenuCar_Gear = CAN_CAR_GEAR_NONE;
	uint8 CarMoveDirect = CAR_WHEEL_DIR_STOP;
	/*针对ODO――DTC报109问题新增策略，P、N档下不进行DTC故障检测，D、R档报DTC故障后车辆停止5秒，故障消失*/
	ReadCAN_AppSignal_Car_Gear(&LenuCar_Gear);
	CarMoveDirect = CPOSSgnMag_ReadCarMoveDirect();
    if(LenuCar_Gear != CAN_CAR_GEAR_P && LenuCar_Gear != CAN_CAR_GEAR_N)
    {
    Lu8OdoTaskCnt++;
    if(Lu8OdoTaskCnt == 10)
    {
        Lu8OdoTaskCnt = 0;
        ReadAPASignal_ODOInfo(&LstrODOInfo);
        ReadCAN_AppSignal_Car_Speed(&LpvCar_Speed);
        if(LpvCar_Speed > 120)/**/
        {
            LfX_Sub = ABS(LfOdo_X_Backup,LstrODOInfo.fODO_X);
            LfY_Sub = ABS(LfOdo_Y_Backup,LstrODOInfo.fODO_Y);
            if((LfX_Sub < 1)&&(LfY_Sub < 1))
            {
                if(Lu8OdoErrorCnt < 20)
                {
						if(DTCValidFlag == VALID)
						{
                    Lu8OdoErrorCnt++;
                }
            }
				}
            else
            {
					DTCValidFlag = VALID;
                Lu8OdoErrorCnt = 0;
            }
        }
        LfOdo_X_Backup = LstrODOInfo.fODO_X;
        LfOdo_Y_Backup = LstrODOInfo.fODO_Y;
    }

    if(Lu8OdoErrorCnt > 10)
    {
        SetDTCStatusFunction(DTC_Missing_CAL, TESTFAIL);
		}
		else 
		{
			SetDTCStatusFunction(DTC_Missing_CAL, TESTPASS);
		}
		if(LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC1_State == 1)
		{
			if(ODO_GdSystemMsTimer > 0){
				if(CarMoveDirect == CAR_WHEEL_DIR_STOP){
					if(GdSystemMsTimer - ODO_GdSystemMsTimer >= 5000){
						SetDTCStatusFunction(DTC_Missing_CAL, TESTPASS);
						DTCValidFlag = INVALID;
						Lu8OdoErrorCnt = 0;
					}
    }
    else 
    {
					ODO_GdSystemMsTimer = GdSystemMsTimer;
				}
			}	
		}
		else{
			ODO_GdSystemMsTimer = GdSystemMsTimer;
		}
	}
	else{
        SetDTCStatusFunction(DTC_Missing_CAL, TESTPASS);
    }
}
/*********************************************************************
* 函数名称: RadarErrorDTC
*
* 功能描述: 读取雷达相关故障
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:  无
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
void RadarErrorDTC(void)
{
    SnsErrInfo_t LstrUltSensorErr;
    SnsID_en i;
    SnsPowerDiagIdx_e LenuIdx;
    SnsPwrDiagStatus_e LpLenuPwrSts;
    uint8 Lu8SnsBeCoveredFlag;
    for(i= SNS_FL_S;i< SNSNum;i++)
    {
        SnsDiag_ReadSnsErrSignal(&LstrUltSensorErr,i);
        PdcSignal_ReadSnsBeCoveredFlagToDTC(&Lu8SnsBeCoveredFlag, i);
        if ((TRUE != LstrUltSensorErr.eErrFlag) && (Lu8SnsBeCoveredFlag == 0x01))/*底层雷达无故障时才判断车衣检测*/
        {
           LstrUltSensorErr.eErrFlag = TRUE;
           LstrUltSensorErr.eErrType = SnsBlindness;
        }
        if(TRUE == LstrUltSensorErr.eErrFlag)
        {/*故障*/
            /*对电源短路*/
            if((DSIShortToPwr == LstrUltSensorErr.eErrType))
            {
                SetDTCStatusFunction((DTC_FLS_SHORT_UBATOR+i*NUMBER_OF_GROUP_DTC),TESTFAIL);
                SetDTCStatusFunction((DTC_FLS_SHORT_UBATOR+i*NUMBER_OF_GROUP_DTC +1),TESTPASS);
                SetDTCStatusFunction((DTC_FLS_SHORT_UBATOR+i*NUMBER_OF_GROUP_DTC +2),TESTPASS);
                SetDTCStatusFunction((DTC_FLS_SHORT_UBATOR+i*NUMBER_OF_GROUP_DTC +3),TESTPASS);
                /* EROR  */
            }
            /*余震异常*/
            else if(SnsInternalFailure ==LstrUltSensorErr.eErrType)
            {
                SetDTCStatusFunction((DTC_FLS_SHORT_UBATOR+i*NUMBER_OF_GROUP_DTC),TESTPASS);
                SetDTCStatusFunction((DTC_FLS_SHORT_UBATOR+i*NUMBER_OF_GROUP_DTC +1),TESTPASS);
                SetDTCStatusFunction((DTC_FLS_SHORT_UBATOR+i*NUMBER_OF_GROUP_DTC +2),TESTFAIL);
                SetDTCStatusFunction((DTC_FLS_SHORT_UBATOR+i*NUMBER_OF_GROUP_DTC +3),TESTPASS);
            }
            /*失聪*/
            else if (SnsBlindness == LstrUltSensorErr.eErrType)//增加DE检测车衣失聪故障
            {
                SetDTCStatusFunction((DTC_FLS_SHORT_UBATOR+i*NUMBER_OF_GROUP_DTC),TESTPASS);
                SetDTCStatusFunction((DTC_FLS_SHORT_UBATOR+i*NUMBER_OF_GROUP_DTC +1),TESTPASS);
                SetDTCStatusFunction((DTC_FLS_SHORT_UBATOR+i*NUMBER_OF_GROUP_DTC +2),TESTPASS);
                SetDTCStatusFunction((DTC_FLS_SHORT_UBATOR+i*NUMBER_OF_GROUP_DTC +3),TESTFAIL);
            }
            /*其他雷达故障->2022.10.24讨论达成一致，其他错误全归为对地短路或开路  讨论人员：常玉毛，方乐运，冯帅，刘宁宁*/
            else
            {
                SetDTCStatusFunction((DTC_FLS_SHORT_UBATOR + i * NUMBER_OF_GROUP_DTC), TESTPASS);
                SetDTCStatusFunction((DTC_FLS_SHORT_UBATOR + i * NUMBER_OF_GROUP_DTC + 1), TESTFAIL);
                SetDTCStatusFunction((DTC_FLS_SHORT_UBATOR + i * NUMBER_OF_GROUP_DTC + 2), TESTPASS);
                SetDTCStatusFunction((DTC_FLS_SHORT_UBATOR + i * NUMBER_OF_GROUP_DTC + 3), TESTPASS);
            }
        }
        else
        {
            SetDTCStatusFunction((DTC_FLS_SHORT_UBATOR+i*NUMBER_OF_GROUP_DTC),TESTPASS);
            SetDTCStatusFunction((DTC_FLS_SHORT_UBATOR+i*NUMBER_OF_GROUP_DTC +1),TESTPASS);
            SetDTCStatusFunction((DTC_FLS_SHORT_UBATOR+i*NUMBER_OF_GROUP_DTC +2),TESTPASS);
            SetDTCStatusFunction((DTC_FLS_SHORT_UBATOR+i*NUMBER_OF_GROUP_DTC +3),TESTPASS);
        }
    }

    for (LenuIdx = FrontSnsGroupPwrDiagIdx;LenuIdx < SnsGroupPwrDiagNum;LenuIdx++)
    {
        ReadPwrManage_SnsPwrDiagStatus(LenuIdx,&LpLenuPwrSts);
        if(LpLenuPwrSts == PWR_PIN_SHORT_TO_PWR)
        {
            SetDTCStatusFunction((DTC_FSC_SHORT_TO_BAT + LenuIdx*2), TESTFAIL);
            SetDTCStatusFunction((DTC_FSC_SHORT_TO_GND + LenuIdx * 2), TESTPASS);
        }
        else if (LpLenuPwrSts == PWR_PIN_SHORT_TO_GND)
        {
            SetDTCStatusFunction((DTC_FSC_SHORT_TO_BAT + LenuIdx * 2), TESTPASS);
            SetDTCStatusFunction((DTC_FSC_SHORT_TO_GND + LenuIdx * 2), TESTFAIL);
        }
        else//(LpLenuPwrSts == PWR_PIN_NORMAL)
        {
            SetDTCStatusFunction((DTC_FSC_SHORT_TO_BAT + LenuIdx * 2), TESTPASS);
            SetDTCStatusFunction((DTC_FSC_SHORT_TO_GND + LenuIdx * 2), TESTPASS);
        }
    }

	if((TRUE == GetDSIMasterErrSts()) || (eDSISPI_Abnormal == DSI_SPIWorkSts()))
	{
		SetDTCStatusFunction(DTC_ECU_INTERNAL_FAIL,TESTFAIL);
	}
	else
	{
		SetDTCStatusFunction(DTC_ECU_INTERNAL_FAIL,TESTPASS);
	}

}

/*********************************************************************
* 函数名称: PowerDTCJudge
*
* 功能描述: 电压状态判定
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明: 无
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
void PowerDTCJudge(void)
{
    static uint16 LcOverVoltageCnt = 0;
    static uint16 LcLowVoltageCnt = 0;
    static uint16 LcNormalVoltageCnt = 100;
    Signal_VoltageStatusType LenuVoltageStatus;
    static uint32 Ld_Last = 0;
    uint32 Ld_Prresent = GetSystemTimeCnt_Ms();
    uint16 Ld_TimeLen = 0;

    if(Ld_Prresent >= Ld_Last)
    {
        Ld_TimeLen = (uint16)(Ld_Prresent- Ld_Last);
    }
    else
    {
        Ld_TimeLen = (uint16)(0xFFFFFFFF + Ld_Prresent- Ld_Last);
    }

    Ld_Last = Ld_Prresent;

    /*读取当前电压状态*/  
    ReadPwrManage_GroupStatus(PM_CAN_UDS,&LenuVoltageStatus); 
    if(LenuVoltageStatus == UNDER_VOLTAGE)
    {
        if(LcLowVoltageCnt < LOWVotageConfimDTCTime_2000MS)         /* 低压超过2000ms记录低压DTC */
        {
            LcLowVoltageCnt+=Ld_TimeLen;
        }
        else
        {
            GcHighVoltageTestResult = TESTPASS;
            GcLowVoltageTestResult  = TESTFAIL;

            LcNormalVoltageCnt = 0;
            LcOverVoltageCnt = 0;
            /*电压异常下，busoff,雷达、节点诊断延时时间重置为500ms*/

            Gu32WaitStartBusoffDTCTimer = DELAY_500MS;
        }
    }
    else if(LenuVoltageStatus == NORMAL_VOLTAGE)
    {
        if (GcLowVoltageTestResult == TESTFAIL)/*如果是欠压恢复，则恢复时间为2000ms*/
        {
            if (LcNormalVoltageCnt < LOWVotageConfimDTCTime_2000MS)     /*10ms*20=200ms*//* 电压正常，清除高低压计数及故障状态 */
            {
                LcNormalVoltageCnt += Ld_TimeLen;
            }
            else
            {
                GcHighVoltageTestResult = TESTPASS;
                GcLowVoltageTestResult = TESTPASS;

                LcLowVoltageCnt = 0;
                LcOverVoltageCnt = 0;
            }
        }
        else/*非欠压故障，恢复时间为200ms*/
        {
            if (LcNormalVoltageCnt < VotageConfimDTCTime_200MS)     /*10ms*20=200ms*//* 电压正常，清除高低压计数及故障状态 */
            {
                LcNormalVoltageCnt += Ld_TimeLen;
            }
            else
            {
                GcHighVoltageTestResult = TESTPASS;
                GcLowVoltageTestResult = TESTPASS;

                LcLowVoltageCnt = 0;
                LcOverVoltageCnt = 0;
            }
        }

    }
    else if(LenuVoltageStatus == OVER_VOLTAGE)
    {
        if(LcOverVoltageCnt < VotageConfimDTCTime_200MS)       /* 高压超过10ms*20=200ms，记录高压DTC */
        {
            LcOverVoltageCnt+=Ld_TimeLen;
        }
        else
        {
            GcHighVoltageTestResult = TESTFAIL;
            GcLowVoltageTestResult  = TESTPASS;

            LcLowVoltageCnt = 0;
            LcNormalVoltageCnt = 0;
            /*电压异常下，busoff,雷达、节点诊断延时时间重置为500ms*/

            Gu32WaitStartBusoffDTCTimer = DELAY_500MS;
        }
    }
}
/*********************************************************************
 * 函数名称:  HighVoltageDTC
 *
 * 功能描述:  高压故障
 *
 * 入口参数:  void
 *
 * 输出参数:  无
 *
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 ********************************************************************/
void HighVoltageDTC(void)
{
    if(GcHighVoltageTestResult == TESTFAIL)        
    {
        SetDTCStatusFunction(DTC_VOLTAGE_HIGH_FAIL,TESTFAIL);
    }
    else if(GcHighVoltageTestResult == TESTPASS)
    {
        SetDTCStatusFunction(DTC_VOLTAGE_HIGH_FAIL,TESTPASS);
    }
    else
    {
        ;
    }
}
/*********************************************************************
 * 函数名称:  LowVoltageDTC
 *
 * 功能描述:  低压故障
 *
 * 入口参数:  void
 *
 * 输出参数:  无
 *
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 ********************************************************************/
void LowVoltageDTC(void)
{
    if(GcLowVoltageTestResult == TESTFAIL)         
    {
        SetDTCStatusFunction(DTC_VOLTAGE_LOW_FAIL,TESTFAIL);                    
    }
    else if(GcLowVoltageTestResult == TESTPASS)
    {
        SetDTCStatusFunction(DTC_VOLTAGE_LOW_FAIL,TESTPASS);
    }
    else
    {
        ;
    }
}
/*********************************************************************
 * 函数名称:  WatchdogDTC
 *
 * 功能描述:  看门狗故障
 *
 * 入口参数:  void
 *
 * 输出参数:  无
 *
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 ********************************************************************/
void WatchdogDTC(void)
{
    SetDTCStatusFunction(DTC_WDG_SA_UC_FAIL, TESTPASS);
#if 0
    /*确认boot版本为0008之后的版本才诊断看门狗故障，否则不诊断*/
    if((DID_F14A_Buff[0] == 0x00)&&((DID_F14A_Buff[1] <= 0x08)))
    {
        SetDTCStatusFunction(DTC_WDG_SA_UC_FAIL,TESTPASS);
    }
    else/*0009以上版本boot，包含0009版本，可以检测看门狗故障*/
    {
        if(TRUE == LblRCM_WATCH_DOGFlag)
        {
            SetDTCStatusFunction(DTC_WDG_SA_UC_FAIL,TESTFAIL);
            LblRCM_WATCH_DOGFlag = FALSE;
        }
        else 
        {
            SetDTCStatusFunction(DTC_WDG_SA_UC_FAIL,TESTPASS);
        }
    }
#endif
}
/*********************************************************************
 * 函数名称:  DTC_ADAS_SYNCTime
 *
 * 功能描述:  
 *
 * 入口参数:  无
 *
 * 输出参数:  无
 *
 * 其它说明:  
 *
 * 修改日期     版本号       修改人       修改内容

**********************************************************************/
void DTC_ADAS_SYNCTime (void)
{
    if((ENABLEDETECT5A4 ==  Gu8StartDetect5A4Flag)||(Gu16DelayDetect5A4Timer_60S>=DELAYDETECT5A4TIME60S))
    {
        /*上电后检查是否收到该报文，如果60s内收到该报文且没有任何错误，
          则在收到报文时刻开始监控；
          如果60s内没有接收到该报文，或收到的报文有错，则在60s时开始监控。*/
        if((ADAS_SYNCTime_FailedCnt >= 3)||(NCS_TST_BIT_SET(GcFrameStatus[ID5A4Idx],COM_FRAME_STATUS_RX_TIMEOUT,uint8)))//||(ADAS_SYNCTime_FailedCnt < 3))  
        {
            SetDTCStatusFunction(DTC_ADAS_SYNCTIME_FAILED,TESTFAIL );     
        }
        else if((ADAS_SYNCTime_PassCnt >= 3))
        {
            SetDTCStatusFunction(DTC_ADAS_SYNCTIME_FAILED,TESTPASS);         
        }
        else
        {

        } 
    }
    else/*否则清除同步错误与通过次数；清除5A4超时标志*/
    {
        ADAS_SYNCTime_FailedCnt =0;
        ADAS_SYNCTime_PassCnt =0;
        GwComFramePeriodCnt[ID5A4Idx]=0;
        NCS_RESET_BIT(GcFrameStatus[ID5A4Idx],COM_FRAME_STATUS_RX_TIMEOUT,uint8);
    }
}
/*********************************************************************
 * 函数名称:  DTC_Config_Uncomplete
 *
 * 功能描述:  
 *
 * 入口参数:  无
 *
 * 输出参数:  无
 *
 * 其它说明:  
 *
 * 修改日期     版本号       修改人       修改内容

**********************************************************************/
void DTC_Config_Uncomplete (void)
{
    uint8 Lu8CigType;
    Lu8CigType = EOLEndECUConfigByteStatus();
#if(DIDTYPE == DIDTYPE_X02)
    if((VALID == Lu8CigType)&&(DID_F1A1_Buff[1]&0x1F) <= 3)/*这里车型只有三款：X01:0;X02:1;X03:2;X04:3*/
#else
    if ((VALID == Lu8CigType) && ((DID_F1A1_Buff[1] & 0x1F) >=0x10)&&((DID_F1A1_Buff[1] & 0x1F) <= 0x14))/*W01,W02,W03,W04,W05*/
#endif
    {
        SetDTCStatusFunction(DTC_UNCOMPLETE_CONFIG,TESTPASS );     
    }
    else
    {
        SetDTCStatusFunction(DTC_UNCOMPLETE_CONFIG,TESTFAIL);         
    }
    /*F1A1配置字*/
}
/*********************************************************************
 * 函数名称:  DTC_CAL_missing
 *
 * 功能描述:  
 *
 * 入口参数:  无
 *
 * 输出参数:  无
 *
 * 其它说明:  
 *
 * 修改日期     版本号       修改人       修改内容

**********************************************************************/
void DTC_CAL_missing (void)
{
    /*待完善*/
}

/*********************************************************************
 * 函数名称:  NodeMonitor_ACU
 *
 * 功能描述:  ACU节点相关DTC监控
 *
 * 入口参数:  无
 *
 * 输出参数:  无
 *
 * 其它说明:  
 *
 * 修改日期     版本号       修改人       修改内容

**********************************************************************/
void NodeMonitor_ACU(void)
{
    /*node missing detect*/
    Missing_ACU_DTC();
    /* if node not missing；detect checksum and counter*/
    if(TESTPASS == GcDTCDetectionStatus[DTC_MISSING_ACU])
    {
        CAN_Invalid_ACUChecksum();
        CAN_Invalid_ACUCounter();
    }
    else
    {
        GcDTCDetectionStatus[DTC_INVALID_ACU_CHECK]= TESTPASS;
        GcDTCDetectionStatus[DTC_INVALID_ACU_COUNTER]= TESTPASS;
    }
    /* if node not missing&& checksum pass&&counter pass，detect data validity */

    if((TESTPASS == GcDTCDetectionStatus[DTC_MISSING_ACU])&&\
        (TESTPASS == GcDTCDetectionStatus[DTC_INVALID_ACU_CHECK])&&\
        (TESTPASS == GcDTCDetectionStatus[DTC_INVALID_ACU_COUNTER]))
    {
        CAN_Invalid_ACUData();
    }
    else
    {
        GcDTCDetectionStatus[DTC_INVALID_ACU_DATA] = TESTPASS;
    }

}
/*********************************************************************
 * 函数名称:  NodeMonitor_ADAS
 *
 * 功能描述:  ADAS节点故障检测
 *
 * 入口参数:  无
 *
 * 输出参数:  无
 *
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 * 
 ********************************************************************/
void NodeMonitor_ADAS(void)
{

    Missing_ADAS_DTC();
    if(TESTPASS == GcDTCDetectionStatus[DTC_MISSING_ADAS])
    {
        CAN_Invalid_ADASChecksum();
        CAN_Invalid_ADASCounter();
    }
    else
    {
        GcDTCDetectionStatus[DTC_INVALID_ADAS_CHECK]= TESTPASS;
        GcDTCDetectionStatus[DTC_INVALID_ADAS_COUNTER]= TESTPASS;
    }
    if((TESTPASS == GcDTCDetectionStatus[DTC_MISSING_ADAS])&&\
        (TESTPASS == GcDTCDetectionStatus[DTC_INVALID_ADAS_CHECK])&&\
        (TESTPASS == GcDTCDetectionStatus[DTC_INVALID_ADAS_COUNTER]))
    {
        CAN_Invalid_ADASData();
    }
    else
    {
        GcDTCDetectionStatus[DTC_INVALID_ADAS_DATA] = TESTPASS;
    }

}
/*********************************************************************
 * 函数名称:  NodeMonitor_FBCM
 *
 * 功能描述:  FBCM节点故障检测
 *
 * 入口参数:  无
 *
 * 输出参数:  无
 *
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 * 
 ********************************************************************/
void NodeMonitor_FBCM(void)
{

    Missing_FBCM_DTC();
    if(TESTPASS == GcDTCDetectionStatus[DTC_MISSING_FBCM])
    {
        CAN_Invalid_FBCMChecksum();
        CAN_Invalid_FBCMCounter();
    }
    else
    {
        GcDTCDetectionStatus[DTC_INVALID_FBCM_CHECK]= TESTPASS;
        GcDTCDetectionStatus[DTC_INVALID_FBCM_COUNTER]= TESTPASS;
    }
}
/*********************************************************************
 * 函数名称:  NodeMonitor_EPS
 *
 * 功能描述:  EPS节点故障检测
 *
 * 入口参数:  无
 *
 * 输出参数:  无
 *
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 * 
 ********************************************************************/
void NodeMonitor_EPS(void)
{
    LowVolPwrMdType LenuLowVolPwrMd = POWER_OFF;
    ReadCAN_AppSignal_LowVolPwrMd_DTCDiag(&LenuLowVolPwrMd);
    if((LenuLowVolPwrMd == POWER_ON))//ON档且计时达到1.5S后才监控EPS节点；
    {
        Missing_EPS_DTC();
        if(TESTPASS == GcDTCDetectionStatus[DTC_MISSING_EPS])
        {
            CAN_Invalid_EPSChecksum();
            CAN_Invalid_EPSCounter();
        }
        else
        {
            GcDTCDetectionStatus[DTC_INVALID_EPS_CHECK]= TESTPASS;
            GcDTCDetectionStatus[DTC_INVALID_EPS_COUNTER]= TESTPASS;
        }
        
        if((TESTPASS == GcDTCDetectionStatus[DTC_MISSING_EPS])&&\
            (TESTPASS == GcDTCDetectionStatus[DTC_INVALID_EPS_CHECK])&&\
            (TESTPASS == GcDTCDetectionStatus[DTC_INVALID_EPS_COUNTER]))
        {   
            CAN_Invalid_EPSData();
        }
        else
        {
            GcDTCDetectionStatus[DTC_INVALID_EPS_DATA] = TESTPASS;
        }
    }
}
/*********************************************************************
 * 函数名称:  NodeMonitor_ESP
 *
 * 功能描述:  ESP节点故障检测
 *
 * 入口参数:  无
 *
 * 输出参数:  无
 *
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 * 
 ********************************************************************/
void NodeMonitor_ESP(void)
{

    Missing_ESP_DTC();
    if(TESTPASS == GcDTCDetectionStatus[DTC_MISSING_ESP])
    {
        CAN_Invalid_ESPChecksum();
        CAN_Invalid_ESPCounter();
    }
    else
    {
        GcDTCDetectionStatus[DTC_INVALID_ESP_CHECK]= TESTPASS;
        GcDTCDetectionStatus[DTC_INVALID_ESP_COUNTER]= TESTPASS;
    }
    
    if((TESTPASS == GcDTCDetectionStatus[DTC_MISSING_ESP])&&\
        (TESTPASS == GcDTCDetectionStatus[DTC_INVALID_ESP_CHECK])&&\
        (TESTPASS == GcDTCDetectionStatus[DTC_INVALID_ESP_COUNTER]))
    {   
        CAN_Invalid_ESPData();
    }
    else
    {
        GcDTCDetectionStatus[DTC_INVALID_ESP_DATA] = TESTPASS;
    }

}
/*********************************************************************
 * 函数名称:  NodeMonitor_HU
 *
 * 功能描述:  HU节点故障检测
 *
 * 入口参数:  无
 *
 * 输出参数:  无
 *
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 * 
 ********************************************************************/
void NodeMonitor_HU(void)
{

    Missing_HU_DTC();
#if 0/*不需要监控HU信号无效故障*/
    if((TESTPASS == GcDTCDetectionStatus[DTC_MISSING_HU]))
    {   
        CAN_Invalid_HUData();
    }
    else
    {
        GcDTCDetectionStatus[DTC_INVALID_HU_DATA] = TESTPASS;
    }
#endif
}
/*********************************************************************
 * 函数名称:  NodeMonitor_XCU
 *
 * 功能描述:  XCU节点故障检测
 *
 * 入口参数:  无
 *
 * 输出参数:  无
 *
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 * 
 ********************************************************************/
void NodeMonitor_XCU(void)
{
    Missing_XCU_DTC();
    if(TESTPASS == GcDTCDetectionStatus[DTC_MISSING_XCU])
    {
        CAN_Invalid_XCUChecksum();
        CAN_Invalid_XCUCounter();
    }
    else
    {
        GcDTCDetectionStatus[DTC_INVALID_XCU_CHECK] = TESTPASS;
        GcDTCDetectionStatus[DTC_INVALID_XCU_COUNTER]= TESTPASS;
    }
    /* if node not missing&& checksum pass&&counter pass，detect data validity */
    if((TESTPASS == GcDTCDetectionStatus[DTC_MISSING_XCU])&&\
        (TESTPASS == GcDTCDetectionStatus[DTC_INVALID_XCU_CHECK])&&\
        (TESTPASS == GcDTCDetectionStatus[DTC_INVALID_XCU_COUNTER]))
    {
        CAN_Invalid_XCUData();
    }
    else
    {
        GcDTCDetectionStatus[DTC_INVALID_XCU_DATA] = TESTPASS;
    }
}
/*********************************************************************
 * 函数名称:  AllNodeMonitor
 *
 * 功能描述:  所有节点故障检测
 *
 * 入口参数:  无
 *
 * 输出参数:  无
 *
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 * 
 ********************************************************************/
void AllNodeMonitor(void)
{

    NodeMonitor_XCU();/*XCU节点监控*/
    NodeMonitor_HU();/*HU节点监控*/
    NodeMonitor_ESP();/*ESP节点监控*/
    // NodeMonitor_EPS();/*EPS节点监控*/
    NodeMonitor_FBCM();/*FBCM节点监控*/
    NodeMonitor_ADAS();/*ADAS节点监控*/
    NodeMonitor_ACU();/*ACU节点监控*/
}
/*********************************************************************
 * 函数名称:  DTCMonitor
 *
 * 功能描述:  故障检测
 *
 * 入口参数:  无
 *
 * 输出参数:  无
 *
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 * 
 ********************************************************************/
void DTCMonitor(uint16 Ld_TimeLen)
{
    PowerDTCJudge();
    LowVoltageDTC();
    HighVoltageDTC();  

    /* 过欠压之后，需要等待500ms之后开始监控Busoff、硬件故障、CAN网络故障 */
    if(Gu32WaitStartBusoffDTCTimer == 0)
    {
        if((GcHighVoltageTestResult == TESTPASS)&&(GcLowVoltageTestResult == TESTPASS))   /* 上电后电压正常之后开始监控 */
        {
            WatchdogDTC();
            CANBusoffDTC();        
            RadarErrorDTC();
            ODOErrorDTC();
            if(Gu32WaitStartDiagNodeDTCTimer == 0 )
            {
                AllNodeMonitor();
                DTC_ADAS_SYNCTime();
            }  
            else if(Gu32WaitStartDiagNodeDTCTimer > Ld_TimeLen)
            {
                Gu32WaitStartDiagNodeDTCTimer -= Ld_TimeLen;
            }
            else
            {
                Gu32WaitStartDiagNodeDTCTimer =0;
                COMSigMgr_RxMonitorInit();
            }
        }
    }
    else if(Gu32WaitStartBusoffDTCTimer > Ld_TimeLen)
    {
        Gu32WaitStartBusoffDTCTimer-=Ld_TimeLen;
    }
    else
    {
        Gu32WaitStartBusoffDTCTimer = 0;
        COMSigMgr_RxMonitorInit();
    }
}
/*********************************************************************
 * 函数名称:  Updata_SendDTC
 *
 * 功能描述:  故障检测
 *
 * 入口参数:  无
 *
 * 输出参数:  无
 *
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 * 
 ********************************************************************/
void Updata_SendDTC(void)
{    
    uint8 i;
    uint8 j=0;
    if(Gu8DTC_send_complete_flag  == 1)
    {
        /*Only 79 DTCs will not be recorded in LEEA_X01 project*/
        for(i=0; i < TOTAL_DTC_RECORD_NUMBER86; i++)
        {
           Gu8SendDTC_Buffer[i]=0; 
        }
        
        for(i=0; i < TOTAL_DTC_RECORD_NUMBER86; i++)
        {
            if(((GsDTCInfomationStr[i].DTCSTATUS.GcDTCStatus & 0x01) != 0))
            {
                Gu8SendDTC_Cnt++;
                /*Only 79 DTCs will not be recorded in LEEA_X01 project*/
                /*And need to send the number as the LEEA_X01 DTC list defined*/
                /*Adjust the data to send*/
                Gu8SendDTC_Buffer[j++] = Gu8SendDTCNumOffset[i];
            }
        }
        Gu8DTC_send_complete_flag = 0;
    }
}
/*********************************************************************
 * 函数名称:  Updata_DTCWindow
 *
 * 功能描述:  更新DTC弹窗状态
 *
 * 入口参数:  无
 *
 * 输出参数:  无
 *
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 *
 ********************************************************************/
void Updata_DTCWindow(void)
{
    uint8 i;
    for (i = 0; i < DTC_WINDOW_NUM; i++)
    {
        if ((GsDTCInfomationStr[Gu8DTCWindow_List[i]].DTCSTATUS.GcDTCStatus & 0x01) != 0)
        {
            break;
        }
    }

    if (i >= DTC_WINDOW_NUM)
    {
        /*没有故障*/
        Write_DTCWindow_Enable(FALSE);
    }
    else
    {
        Write_DTCWindow_Enable(TRUE);
    }
}
/*********************************************************************
 * 函数名称:  Juge_FunctionDowngrade
 *
 * 功能描述:  判断功能降级条件，更新到对应接口
 *
 * 入口参数:  无
 *
 * 输出参数:  无
 *
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 *
 ********************************************************************/
void Juge_FunctionDowngrade(void)
{
    uint8 i;
    uint8 j;
    /*诊断PAS跟PSL共有DTC*/
    for (i = 0; i < PASPSLDTCNUM; i++)
    {

        if (((GsDTCInfomationStr[Gu8PAS_PSLFuncAssocDTC[i]].DTCSTATUS.GcDTCStatus & 0x01) != 0))/*判断是否有DTC*/
        {
          /*recode PAS &PSL FunctionDowngrade*/
            break;
        }
    }
    /*诊断PSL独有DTC*/
    for (j = 0; j < PSLDTCNUM; j++)
    {

        if (((GsDTCInfomationStr[Gu8PSLFuncAssocDTC[j]].DTCSTATUS.GcDTCStatus & 0x01) != 0))/*判断是否有DTC*/
        {
            /*recodePSL FunctionDowngrade*/
            break;
        }
    }

    if (i >= PASPSLDTCNUM)
    {
        Write_PASFunctionDGFlag(FALSE);    /*清0PAS降级标志*/
    }
    else
    {
        /*recode PAS &PSL FunctionDowngrade*/
        Write_PASFunctionDGFlag(TRUE);    /*记录PAS降级标志*/
    }

    if ((i + j) == (PSLALLDTCNUM))
    {
        Write_PSLFunctionDGFlag(FALSE);/*清0PAS降级标志*/
    }
    else
    {
        /*recode PAS &PSL FunctionDowngrade*/
        Write_PSLFunctionDGFlag(TRUE);    /*记录PAS降级标志*/
    }
}
/**********************************************************************
*   函数名称:   RecodeDTCSnapshotData
*
*   功能描述:   记录快照信息
*
*   输入参考:   无
*
*   输出参数:   无
*
*   返 回 值:    无
*
*   其它说明:
*
*   修改日期        版本号       修改人       修改内容
*
**********************************************************************/
void RecodeDTCSnapshotData(void)
{
    uint8 Lu8Cnt;
    static uint8 LcRecordDTCStatus[TOTAL_DTC_RECORD_NUMBER86];

    for(Lu8Cnt = 0;Lu8Cnt < TOTAL_DTC_RECORD_NUMBER86;Lu8Cnt++)
    {      
        if(GcDTCDetectionInitStatusFlag == FALSE)
        {
            LcRecordDTCStatus[Lu8Cnt] = TESTINIT;
        }
        else                
        {
            if(LcRecordDTCStatus[Lu8Cnt] != GcDTCDetectionStatus[Lu8Cnt])
            {
                DTCTestingResultManage(Lu8Cnt,GcDTCDetectionStatus[Lu8Cnt]);

                LcRecordDTCStatus[Lu8Cnt] = GcDTCDetectionStatus[Lu8Cnt];
            }                   
        }
    }
    GcDTCDetectionInitStatusFlag = TRUE;
}


/**********************************************************************
*   函数名称:   SetDTCStatusManage
*
*   功能描述:   设置DTC的状态管理
*
*   输入参考:   无
*
*   输出参数:   无
*
*   返 回 值:    无
*
*   其它说明:
*
*   修改日期        版本号       修改人       修改内容
*
**********************************************************************/
void DTCStatusManage_Task(void)/*10ms调一次*/
{
    static uint8 LcDTCVariableInitFlag = DTC_INIT_UNFINISH;
    static uint32 Ld_Last = 0;
    uint32 Ld_Prresent = GetSystemTimeCnt_Ms();
    uint16 Ld_TimeLen = 0;
    LowVolPwrMdType LenuLowVolPwrMd = POWER_OFF;
    ReadCAN_AppSignal_LowVolPwrMd_DTCDiag(&LenuLowVolPwrMd);
    /********************系统上次运行与本次运行时间差*************/
    if(Ld_Prresent >= Ld_Last)
    {
        Ld_TimeLen = (uint16)(Ld_Prresent- Ld_Last);
    }
    else
    {
        Ld_TimeLen = (uint16)(0xFFFFFFFF + Ld_Prresent- Ld_Last);
    }

    Ld_Last = Ld_Prresent;
    /************************************************************/
    /*本次上电或者复位后，第一次运行执行*/
    if(LcDTCVariableInitFlag == DTC_INIT_UNFINISH)
    {   
        LcDTCVariableInitFlag = DTC_INIT_FINISH;
        /*标定相关上电即检测*/
        //
        DTC_Config_Uncomplete();/*车型配置字检测*/
        Gu8DTC_send_complete_flag = 1;
        LblRCM_WATCH_DOGFlag = 0;//GetRCM_WATCH_DOG();
        Gu16DelayDetect5A4Timer_60S = 0;/*复位初始化5A4监控时间为0*/
    }
    else/*非首次调用该Task*/
    {
        if(Gu16DelayDetect5A4Timer_60S < DELAYDETECT5A4TIME60S)
        {
            Gu16DelayDetect5A4Timer_60S += Ld_TimeLen;
        }
        
        if((POWER_ON != LenuLowVolPwrMd)&&((POWER_ACC != LenuLowVolPwrMd)))/*非IG on/acc状态重新1.5s*/
        {
            GwWaitStartDiagDTCTimer = DELAY_1500MS;
            Gu16DelayDetect5A4Timer_60S = 0;
        }
        else/*IG ON状态下执行*/
        {
            if(GwWaitStartDiagDTCTimer == 0)/*上电延时1.5s计时到达*/
            {
                if(GsUdsService.DTCSettingType == DTC_SETTING_ON)/*允许记录DTC状态*/
                {
                    DTCMonitor(Ld_TimeLen);/*监控DTC*/
                    RecodeDTCSnapshotData();/*更新故障状态及快照信息*/
                }
            }
            else if(GwWaitStartDiagDTCTimer > Ld_TimeLen)/*上电延时1.5s计时未到达*/
            {
                GwWaitStartDiagDTCTimer-=Ld_TimeLen;
            }
            else
            {
                GwWaitStartDiagDTCTimer =0;/*计数到达，下一周期开始监控DTC*/
                COMSigMgr_RxMonitorInit();/*清除节点相关故障DTC及缓存状态*/
            }
            if((POWER_ON != LenuLowVolPwrMd))
            {
                Gu16EPSMonitorDelayTime=DELAY_1500MS;
                COMSigMgr_EPSRxMonitorInit();
            }
            else//ON档
            {
                if(Gu16EPSMonitorDelayTime == 0)/*上电延时1.5s计时到达*/
                {
                    if(GsUdsService.DTCSettingType == DTC_SETTING_ON)/*允许记录DTC状态*/
                    {
                        NodeMonitor_EPS();
                    }
                }
                else if(Gu16EPSMonitorDelayTime > Ld_TimeLen)
                {
                    Gu16EPSMonitorDelayTime-=Ld_TimeLen;
                }
                else//计时未到
                {
                    Gu16EPSMonitorDelayTime=0;
                    COMSigMgr_EPSRxMonitorInit();
                }
            }
        }
    }
    Updata_SendDTC();/*更新发送到CAN总线上的DTC信息*/
    Juge_FunctionDowngrade();
}





