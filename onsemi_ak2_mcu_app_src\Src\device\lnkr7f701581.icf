//-------------------------------------------------------------------------
//      ILINK command file template for RH850 microcontroller R7F701581.
//
//      This file can be used to link object files from the RH850
//      Assembler, IASMRH850, and the C/C++ compiler ICCRH850.
//
//          This file is generated from the device file:
//          dr7f701581.dvf
//          Copyright(C) 2016 Renesas
//          Format version 1.30, File version 1.20 
//-------------------------------------------------------------------------

define symbol _LOCAL_RAM_SELF_BEGIN   = 0xFEDD0000;  // SELF_AREA start address
define symbol _LOCAL_RAM_SELF_SIZE    = 0x00020000;  // SELF_AREA size
define symbol _RETENTION_RAM_SIZE     = 0x00010000;  // RETENTION_RAM size

define memory mem with size = 4G;                                        // Section name    Memory type
                                                                         // ------------    -----------

define region ROM_near       = mem:[from 0x00000000 to 0x001FFFFF];      // <code default>  Code FLASH
define region ROM_1ST_region = mem:[from 0x00000000 to 0x001FFFFF];      // <code default>  Code FLASH
define region ROM_2ND_region = mem:[from 0x01000000 to 0x01007FFF];      // SECOND_ROM      Code FLASH
define region ROM_3RD_region = mem:[from 0x00000000 size 0];
define region ROM_4TH_region = mem:[from 0x00000000 size 0];
define region ROM_5TH_region = mem:[from 0x00000000 size 0];

// Define LOCAL RAM PE1 region for DMA access
define exported symbol _RAM_PE1_BEG = 0xFEBD0000;
define exported symbol _RAM_PE1_SIZE = 0x1000;
define exported symbol _RAM_PE1_END = _RAM_PE1_BEG + _RAM_PE1_SIZE;
define region RAM_PE1_region = mem:[from _RAM_PE1_BEG to _RAM_PE1_END];  // <data default>  Local RAM self/mirror area

define region RAM_near       = mem:[from 0x00000000 size 0];
define exported symbol _RAM_1ST_BEG = _LOCAL_RAM_SELF_BEGIN + _RAM_PE1_SIZE;
define exported symbol _RAM_1ST_END = _LOCAL_RAM_SELF_BEGIN + _LOCAL_RAM_SELF_SIZE + _RETENTION_RAM_SIZE - 1;
define region RAM_1ST_region = mem:[from _RAM_1ST_BEG to _RAM_1ST_END];  // <data default>  Local RAM self/mirror area
define exported symbol _RAM_2ND_BEG = 0x00000000;
define exported symbol _RAM_2ND_END = 0x00000000;
define region RAM_2ND_region = mem:[from 0x00000000 size 0];
define exported symbol _RAM_3RD_BEG = 0x00000000;
define exported symbol _RAM_3RD_END = 0x00000000;
define region RAM_3RD_region = mem:[from 0x00000000 size 0];
define exported symbol _RAM_4TH_BEG = 0x00000000;
define exported symbol _RAM_4TH_END = 0x00000000;
define region RAM_4TH_region = mem:[from 0x00000000 size 0];
define exported symbol _RAM_5TH_BEG = 0x00000000;
define exported symbol _RAM_5TH_END = 0x00000000;
define region RAM_5TH_region = mem:[from 0x00000000 size 0];

define exported symbol _SELF_AREA_COMMON_BEG = _LOCAL_RAM_SELF_BEGIN;
define exported symbol _SELF_AREA_COMMON_END = _LOCAL_RAM_SELF_BEGIN + _LOCAL_RAM_SELF_SIZE - 1;
define region SELF_AREA_region =
  mem:[from _SELF_AREA_COMMON_BEG to _SELF_AREA_COMMON_END];             // SELF_AREA       Local RAM self/mirror area

define region SELF_AREA_1_region = mem:[from 0xFEDD0000 to 0xFEDEFFFF];

reserve region "Reserved by local RAM CPU-specific access" =
  (SELF_AREA_1_region - SELF_AREA_region);

//define exported symbol _RAM_RET_BEG = 0xFEDF0000;
//define exported symbol _RAM_RET_END = 0xFEDFFFFF;
//define region RAM_RET_region = mem:[from _RAM_RET_BEG to _RAM_RET_END];  // RETENTION_RAM   Retention RAM

//Uncomment and adapt the following section if you want to place the interrupt tables at specific addresses.
// define block .interrupt_core1 with alignment = 8 {
//   ro section .table.interrupt_core_1 };
//
// "INTERRUPT":place at address mem:0x200
// {
//   block .interrupt_core1
// };

include "layout.icf";

//-------------------------------------------------------------------------
//      End of file
//-------------------------------------------------------------------------
