/******************************************************************************
 * @file      RdumRdusAddr.h
 * @brief     安森美超声波探头地址定义
 * <AUTHOR>
 * @date      2025-05-13
 * @note      
 *****************************************************************************/

#ifndef __RDUM_RDUS_ADDR_H__
#define __RDUM_RDUS_ADDR_H__
/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "types.h"



/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/
/* 寄存器地址定义 */
#define ADDR_TIMING_CONTROL         0x00    /* 时序控制寄存器 */
#define ADDR_SYSTEM_CONTROL         0x01    /* 系统控制寄存器 */
#define ADDR_CRM_CONTROL            0x02    /* CRM控制寄存器 */
#define ADDR_CRM_DATA_0             0x03    /* CRM数据寄存器0 */
#define ADDR_CRM_DATA_1             0x04    /* CRM数据寄存器1 */
#define ADDR_CRM_DATA_2             0x05    /* CRM数据寄存器2 */
#define ADDR_CRM_DATA_3             0x06    /* CRM数据寄存器3 */
#define ADDR_CRM_DATA_4             0x07    /* CRM数据寄存器4 */
#define ADDR_PDCM_CONTROL           0x08    /* PDCM控制寄存器 */
#define ADDR_PDCM_CONFIG            0x09    /* PDCM配置寄存器 */
#define ADDR_PDCM_STATUS            0x0A    /* PDCM状态寄存器 */
#define ADDR_SPI_RCVR_STATUS        0x0B    /* SPI接收状态寄存器 */
#define ADDR_INTERRUPT_STATUS       0x0C    /* 中断状态寄存器 */
#define ADDR_INTERRUPT_MASK         0x0D    /* 中断掩码寄存器 */
#define ADDR_SYSTEM_STATUS          0x0E    /* 系统状态寄存器 */
#define ADDR_ERROR_STATUS           0x0F    /* 错误状态寄存器 */
#define ADDR_DIAGNOSTIC_CONTROL     0x10    /* 诊断控制寄存器 */
#define ADDR_DIAGNOSTIC_STATUS      0x11    /* 诊断状态寄存器 */
#define ADDR_TEMPERATURE_SENSOR     0x12    /* 温度传感器寄存器 */
#define ADDR_VOLTAGE_SENSOR         0x13    /* 电压传感器寄存器 */
#define ADDR_CURRENT_SENSOR         0x14    /* 电流传感器寄存器 */
#define ADDR_PIEZO_CONTROL          0x15    /* 压电控制寄存器 */
#define ADDR_PIEZO_STATUS           0x16    /* 压电状态寄存器 */
#define ADDR_ECHO_CONTROL           0x17    /* 回波控制寄存器 */
#define ADDR_ECHO_STATUS            0x18    /* 回波状态寄存器 */
#define ADDR_ECHO_DATA              0x19    /* 回波数据寄存器 */
#define ADDR_ECHO_THRESHOLD         0x1A    /* 回波阈值寄存器 */
#define ADDR_ECHO_FILTER            0x1B    /* 回波滤波寄存器 */
#define ADDR_ECHO_GAIN              0x1C    /* 回波增益寄存器 */
#define ADDR_ECHO_TIMING            0x1D    /* 回波时序寄存器 */
#define ADDR_DEVICE_ID_1            0x1E    /* 设备ID寄存器1 */
#define ADDR_DEVICE_ID_2            0x1F    /* 设备ID寄存器2 */
#define ADDR_FIRMWARE_VERSION       0x20    /* 固件版本寄存器 */
#define ADDR_HARDWARE_VERSION       0x21    /* 硬件版本寄存器 */
#define ADDR_EEPROM_CONTROL         0x22    /* EEPROM控制寄存器 */
#define ADDR_EEPROM_STATUS          0x23    /* EEPROM状态寄存器 */
#define ADDR_EEPROM_DATA            0x24    /* EEPROM数据寄存器 */
#define ADDR_EEPROM_ADDRESS         0x25    /* EEPROM地址寄存器 */
#define ADDR_CALIBRATION_CONTROL    0x26    /* 校准控制寄存器 */
#define ADDR_CALIBRATION_STATUS     0x27    /* 校准状态寄存器 */
#define ADDR_CALIBRATION_DATA       0x28    /* 校准数据寄存器 */
#define ADDR_SELF_TEST_CONTROL      0x29    /* 自检控制寄存器 */
#define ADDR_SELF_TEST_STATUS       0x2A    /* 自检状态寄存器 */
#define ADDR_SELF_TEST_RESULT       0x2B    /* 自检结果寄存器 */

/* 特殊映射地址 */
#define ADDR_CRM_MAPPING_START ADDR_CRM_DATA_0 /* CRM映射地址起始 */
#define ADDR_CRM_MAPPING_END ADDR_CRM_DATA_4   /* CRM映射地址结束 */

/* CRM命令定义 */
#define CRM_CMD_SOFTWARE_RESET      0x02    /* 软件复位 */
#define CRM_CMD_ADDRESS_DISCOVERY   0x03    /* 地址发现 */
#define CRM_CMD_WRITE_BLOCK         0x04    /* 块写入 */
#define CRM_CMD_WRITE_PIPELINED     0x05    /* 流水线写入 */
#define CRM_CMD_WRITE               0x06    /* 写入 */
#define CRM_CMD_WRITE_8BIT          0x07    /* 8位写入 */
#define CRM_CMD_WRITE_READ_POINTERS 0x08    /* 写读指针 */
#define CRM_CMD_READ                0x09    /* 读取 */
#define CRM_CMD_PROGRAM_MEMORY      0x0A    /* 编程存储器 */
#define CRM_CMD_START_MEASUREMENT   0x0F    /* 开始测量 */

/* 测量模式定义,最多只支持预存6种模式 */
#define MEAS_MODE0          0x00    /* 默认模式 */
#define MEAS_MODE2          0x01
#define MEAS_MODE3          0x02
#define MEAS_MODE4          0x03
#define MEAS_MODE5          0x04
#define MEAS_MODE6          0x05

/* EEPROM操作定义 */
#define EEPROM_OP_RAM_TO_EEPROM     0x01    /* RAM到EEPROM */
#define EEPROM_OP_ENABLE_PROG       0x02    /* 使能编程 */
#define EEPROM_OP_EEPROM_TO_RAM     0x04    /* EEPROM到RAM */
#define EEPROM_OP_PROG_SLAVE_ID     0x08    /* 编程从机ID */

/* 状态读取地址定义 */
#define STATUS_ADDR_PUDIN_H         0x00    /* PUDIN高字节 */
#define STATUS_ADDR_PUDIN_L         0x01    /* PUDIN低字节 */
#define STATUS_ADDR_HW_ERR_INF2     0x02    /* 硬件错误信息2 */
#define STATUS_ADDR_HW_ERR_INF1     0x03    /* 硬件错误信息1 */
#define STATUS_ADDR_COM_ERR_INF     0x04    /* 通信错误信息 */
#define STATUS_ADDR_VSUP            0x06    /* 电源电压 */
#define STATUS_ADDR_VTANK           0x07    /* 储能电压 */
#define STATUS_ADDR_VDDD            0x08    /* 数字电源电压 */
#define STATUS_ADDR_VREF            0x09    /* 参考电压 */
#define STATUS_ADDR_VTEMP           0x0A    /* 温度电压 */
#define STATUS_ADDR_TEMP            0x0B    /* 温度 */
#define STATUS_ADDR_CVDDA           0x0C    /* 模拟电源电流 */
#define STATUS_ADDR_CVDDD           0x0D    /* 数字电源电流 */
#define STATUS_ADDR_DIG_SIG_PATH    0x0E    /* 数字信号路径检查 */
#define STATUS_ADDR_INT_SIG_PATH    0x0F    /* 内部信号路径检查 */
#define STATUS_ADDR_RING_FREQ       0x12    /* 振铃频率 */
#define STATUS_ADDR_RING_TIME       0x13    /* 振铃时间 */
#define STATUS_ADDR_DSI3_CLK_1US    0x14    /* DSI3时钟1us */
#define STATUS_ADDR_DSI3_SYNC_CNT   0x15    /* DSI3同步计数 */
#define STATUS_ADDR_PDCM_INTERVAL   0x16    /* PDCM间隔 */
#define STATUS_ADDR_HW_VERSION      0x1E    /* 硬件版本 */
#define STATUS_ADDR_SW_VERSION      0x1F    /* 软件版本 */

/* IC模式定义 */
#define IC_MODE_SELF_TEST           0x02    /* 自检 */
#define IC_MODE_SOFTWARE_RESET      0x04    /* 软件复位 */
#define IC_MODE_STANDBY             0x08    /* 待机 */
#define IC_MODE_WAKE_UP             0x10    /* 唤醒 */
#define IC_MODE_DSP_ON              0x20    /* DSP开启 */
#define IC_MODE_DSP_OFF             0x40    /* DSP关闭 */

/* 调整选择定义 */
#define ADJ_SEL_GAIN                0x00    /* 增益调整 */
#define ADJ_SEL_IDRV                0x01    /* 驱动电流调整 */

/* 调整符号定义 */
#define ADJ_SYMBOL_POSITIVE         0x00    /* 正调整 */
#define ADJ_SYMBOL_NEGATIVE         0x01    /* 负调整 */



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/



/******************************************************************************
 * @Const Declaration
 *****************************************************************************/



/******************************************************************************
 * @Variable Declaration
 *****************************************************************************/



/******************************************************************************
 * @Function Declaration
 *****************************************************************************/



#endif /* __RDUM_RDUS_ADDR_H__ */
