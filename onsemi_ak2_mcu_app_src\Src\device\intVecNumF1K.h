/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: intVecNumF1K.h
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/
#ifndef __VEC_NUM_F1K_H
#define __VEC_NUM_F1K_H

/***********************************宏定义*************************************/
/* 定义各中断对应的中断向量位置 */
#define NUMINTRCANGRECC0                23u
#define NUMINTRCAN0ERR                  24u
#define NUMINTRCAN0TRX                  26u

//#define NUMINTRCAN3REC                  221u
#define NUMINTRCAN4ERR                  272u
#define NUMINTRCAN4TRX                  274u

#define NUMINTRLIN32UR0                 165u
#define NUMINTRLIN32UR1                 166u
#define NUMINTRLIN32UR2                 167u
#define NUMINTP0                        37u
#define NUMINTP3                        43u
#define NUMINTOSTM0                     84u
#define NUMINTADC0SG1                   18u
#define NUMINTINTP2                     39u
#define NUMINTINTP7                     129u
#define NUMINTINTP8                     130u
#define NUMINTINTP11                    47u

#define NUMINTDMA1                  	61u
#define NUMINTDMA3                  	63u
#define NUMINTDMA5                  	65u
#define NUMINTCSIH0IC                   29u
#define NUMINTCSIH0IR                   30u
#define NUMINTCSIH0RE                   31u
#define NUMINTCSIH2RE                   134u
#define NUMINTCSIH3RE                   160u
// #define NUMINTTAUD0I15                  55u
#define NUMINTP2                		39u
#define NUMINTP7                		129u
#define NUMINTP8                		130u
#define NUMINTP11                 		47u

#endif
