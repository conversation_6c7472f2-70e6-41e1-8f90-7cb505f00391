/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * F_R_ObjMapCal_Type: 
 * Created on: 2023-02-16 15:39
 * Original designer: AntonyFang
 ******************************************************************************/

#ifndef MapBuild_Type_H
#define MapBuild_Type_H

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "types.h"
#include "SnsRawData_Type.h"
#include "Sns_install_Coordinate_Types.h"
#include "CAN_AppSignal_CommonTypes.h"



/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/
#define MAP_TYPE_BUF_CNT        10
#define MAP_TYPE_CNT_BUF_CNT    10

#define MAP_COOR_BUF_CNT        3
#define MAP_BIG_WALL_BUF_CNT    5
#define MAP_LINE_COOR_BUF_CNT   5

#define MAP_FUSION_LINE_CNT       3
#define MAP_LINE_FUSION_POINT_CNT 3
#define MAP_TO_BUMPER_HEIGHT_STEP_DIS  200
#define MAP_TO_BUMPER_HEIGHT_MAX_DIS   3000

/* MAP 精度打印及输出开关，仅在仿真调试时使用 */
#define MAP_PRECISION_PRINT_SWITCH     0


/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/

typedef enum
{
    PDC_AREA_LINE0_1 = 0,
    PDC_AREA_LINE1_2,
    PDC_AREA_LINE2_3,
    PDC_AREA_LINE3_4,
    PDC_AREA_LINE4_5,
    PDC_AREA_LINE_NUM
}ObjPDCAreaLineType;


/* 定义探头测量类型 */
typedef enum
{
    OBJ_PDC_AREA_NONE  = -1,
    OBJ_PDC_AREA0  = 0,
    OBJ_PDC_AREA1,
    OBJ_PDC_AREA2, 
    OBJ_PDC_AREA3,
    OBJ_PDC_AREA_NUM
}ObjPDCAreaType;


/***********************以下定义和Map相关的类型及数据结构*****************************************  */

/******************************************************************************
* 设计描述 : 前后 MAP地图障碍物ID，最多追踪20个障碍物
* 设计索引 : 
*******************************************************************************/
typedef enum
{
    MAP_OBJ_0 = 0,
    MAP_OBJ_1,
    MAP_OBJ_2,
    MAP_OBJ_3,
    MAP_OBJ_4,
    MAP_OBJ_5,
    MAP_OBJ_6,
    MAP_OBJ_7,
    MAP_OBJ_8,
    MAP_OBJ_9,
    MAP_OBJ_10,
    MAP_OBJ_11,
    MAP_OBJ_12,
    MAP_OBJ_13,
    MAP_OBJ_14,
    MAP_OBJ_15,
    MAP_OBJ_16,
    MAP_OBJ_17,
    MAP_OBJ_18,
    MAP_OBJ_19,
    MAP_OBJ_TO_CAN_MAX,
    MAP_OBJ_NUM = 20,
    MAP_NONE = 31
}eMapObjIdType;

/******************************************************************************
* 设计描述 : 障碍物类型
* 设计索引 : 
*******************************************************************************/
typedef enum
{
    OBJ_TYPE_NONE = 0,
    OBJ_TYPE_POINT,
    OBJ_TYPE_STRAIGHT0_CORNER,  /** @brief: 查不到头和尾 */
    OBJ_TYPE_STRAIGHT1_CORNER,  /** @brief: 有一个头，找不到尾 */
    OBJ_TYPE_STRAIGHT2_CORNER,  /** @brief: 普通障碍物，有头和尾 */
    OBJ_TYPE_LEFT_SIDE,         /** @brief: 左侧侧边Map */
    OBJ_TYPE_RIGHT_SIDE         /** @brief: 右侧侧边Map */
}eMapObjShapeType;

/******************************************************************************
* 设计描述 : 障碍物置信度等级
* 设计索引 : 
*******************************************************************************/
typedef enum
{
    OBJ_PROB_NONE = 0,
    OBJ_PROB_LV1,     /* 置信度16.6% */
    OBJ_PROB_LV2,     /* 置信度33.2% */
    OBJ_PROB_LV3,     /* 置信度49.9% */
    OBJ_PROB_LV4,     /* 置信度66.6% */
    OBJ_PROB_LV5,     /* 置信度83.3% */
    OBJ_PROB_LV6,     /* 置信度99.9% */
    OBJ_PROB_LV_NUM
}eMapProbLevelType;


/******************************************************************************
* 设计描述 : 障碍物高度
* 设计索引 : 
*******************************************************************************/
typedef enum
{
    OBJ_LOW = 0,
    OBJ_HIGH,
    OBJ_TRAVERSIBLE,
    OBJ_UNKNOW,
    OBJ_HEIGHT_HIGH_CUEB,
}eMapObjHeightType;


/******************************************************************************
* 设计描述 : Map所在的大的区域划分
* 设计索引 : 
*******************************************************************************/
typedef enum
{
    OBJ_NONE_BUMP = -1,
    OBJ_FRONT_BUMP = 0,
    OBJ_REAR_BUMP,
    OBJ_LEFT_SIDE_BUMP,
    OBJ_RIGHT_SIDE_BUMP,
}eMapBumperAreaType;

/******************************************************************************
* 设计描述 : Map相对于车辆的运动趋势
* 设计索引 : 
*******************************************************************************/
typedef enum
{
    OBJ_TO_CAR_STOP_STOP = 0,               /* 车辆静止，Map相对于车辆移动距离在5cm以内 */
    OBJ_TO_CAR_STOP_CLOSE,                  /* 车辆静止，Map相对于靠近 */
    OBJ_TO_CAR_STOP_FARAWAY,                /* 车辆静止，Map相对于远离 */
    OBJ_TO_CAR_MOVE_CLOSE,                  /* 车辆运动，靠近障碍物 */
    OBJ_TO_CAR_MOVE_FARAWAY,                /* 车辆运动，远离障碍物 */
    OBJ_IN_SIDE_MOVE
}eMapObjMoveStsType;

/******************************************************************************
* 设计描述 : Map所属区域的探测划分，用于区分更新和跟踪
* 设计索引 : 
*******************************************************************************/
typedef enum
{
    OBJ_NONE_FOLLOW = -1,
    OBJ_BLIND_FOLLOW = 0,
    OBJ_DET_UPDATE,
    OBJ_OUT_DET_FOLLOW,
    OBJ_SIDE_FOLLOW,
}eMapObjFollowStsType;

/******************************************************************************
* 设计描述 : Map动态跟踪过程中，不同阶段回波高度的变化，从200cm开始到20cm，每间隔20cm记录一个回波高度到缓存
* 设计索引 : 
*******************************************************************************/
typedef enum
{
    MAP_TO_BUMPER_DIS_0_CM = 0,
    MAP_TO_BUMPER_DIS_20_CM,
    MAP_TO_BUMPER_DIS_40_CM,
    MAP_TO_BUMPER_DIS_60_CM,
    MAP_TO_BUMPER_DIS_80_CM,
    MAP_TO_BUMPER_DIS_100_CM,
    MAP_TO_BUMPER_DIS_120_CM,
    MAP_TO_BUMPER_DIS_140_CM,
    MAP_TO_BUMPER_DIS_160_CM,
    MAP_TO_BUMPER_DIS_180_CM,
    MAP_TO_BUMPER_DIS_200_CM,
    MAP_TO_BUMPER_DIS_220_CM,
    MAP_TO_BUMPER_DIS_240_CM,
    MAP_TO_BUMPER_DIS_260_CM,
    MAP_TO_BUMPER_DIS_280_CM,
    MAP_TO_BUMPER_DIS_300_CM,
    MAP_TO_BUMPER_DIS_NUM
}eMapToBumperDisIndexType;

/******************************************************************************
* 设计描述 :定义侧边Map的更新状态
* 设计索引 : 
*******************************************************************************/
typedef enum
{
    SIDE_MAP_UPDATE_NONE = 0,
    SIDE_MAP_UPDATE_P1_P2,                        /* P1 P2点均动态更新 */
    SIDE_MAP_MEMORY_P1_UPDATE_P2,                 /* P1点Odo坐标记忆，P2点更新 */
    SIDE_MAP_MEMORY_P2_UPDATE_P1,                 /* P2点Odo坐标记忆，P1点更新 */
    SIDE_MAP_MEMORY_P1_MEMORY_P2,                 /* P1 P2点均Odo坐标记忆 */
}eSideMapUpdateStsType;

/* 定义不同类型障碍物的计数 */
typedef struct
{
    uint32                u32HeightSum;             /* 一个区域段中最近两个回波高度的和 */
    uint16                u16HeightMax;             /* 一个区域段中回波高度的最大值 */
    uint8                 u8HeightCnt;              /* 一个区域段中Map Height 的个数 */
}MapHeightType;

typedef struct
{
    uint16 u16MasterDist;
    uint16 u16MasterHeight;
}MapDistHeight;

/******************************************************************************
* 设计描述 : 前后雷达Map数据结构
* 设计索引 : 
*******************************************************************************/
typedef struct 
{   
    /********************************Map结构体的必须参数类型******************************************/
    ObjCoorType               P1_OdoCoorBuf[MAP_COOR_BUF_CNT];/* P1 点的世界坐标缓存 */
    ObjCoorType               P2_OdoCoorBuf[MAP_COOR_BUF_CNT];/* P2 点的世界坐标缓存 */
    ObjCoorType               P1_OdoCoor;              /* P1 点的世界坐标 */
    ObjCoorType               P2_OdoCoor;              /* P2 点的世界坐标 */
    ObjCoorType               P1_CarCoor;              /* P1 点的车辆坐标 */
    ObjCoorType               P2_CarCoor;              /* P1 点的车辆坐标 */
    eMapObjIdType             eMapObjId;               /* Map对应的ID */
    eMapObjShapeType          eMapObjType;             /* 障碍物类型 */
    eMapProbLevelType         eObjExistProb;           /* 障碍物存在可能性 */
    eMapObjHeightType         eMapObjHeight;           /* 障碍物的高度 */
    eMapProbLevelType         eObjHeightProb;          /* 障碍物高度置信度 */
    eMapObjHeightType         eMapSigGroupHeight;      /* 通过信号组识别的障碍物高低类型 */
    eMapObjHeightType         eMapHeightTrendHeight;   /* 通过信号组识别的障碍物回波高度的变化趋势识别的高低类型 */
    eMapObjHeightType         eMapDE_NoMatchHeight;    /* 通过DE 和Map到保杠距离的匹配性识别的Map高低类型，主要用于PRO车型 */

    /********************************Map结构体的辅助参数类型*****************************************/
    float                     fP1_P2_Dis;              /* P1 P2两点间距离 */
    float                     fP1_P2ToCarX_Angle;      /* P1 P2构成直线和X轴的夹角 */
    float                     fMapToBumper_Dis;        /* Map到保杠的距离 */
    float                     fP1_ToBumper_Dis;        /* P1点到保杠的距离 */
    float                     fP2_ToBumper_Dis;        /* P2点到保杠的距离 */ 
    float                     fSnsFLS_DeNoMatchMapMovDis;  /* 侧边Map和探头不匹配时车辆移动的连续距离 */
    float                     fSnsFRS_DeNoMatchMapMovDis;  /* 侧边Map和探头不匹配时车辆移动的连续距离 */
    float                     fSnsRLS_DeNoMatchMapMovDis;  /* 侧边Map和探头不匹配时车辆移动的连续距离 */
    float                     fSnsRRS_DeNoMatchMapMovDis;  /* 侧边Map和探头不匹配时车辆移动的连续距离 */
    float                     fSideMapToSelfSnsDis;        /* 侧边Map和其产生探头之间的距离 */
    float                     fNearestDE;                  /* Map 能够匹配到的最近的DE值 */
    eMapBumperAreaType        eObjBumperArea;          /* 障碍物所属的区域--前保、后保或左边侧保、右边侧保 */
    ObjMAPAreaType            eP1_DetailArea;          /* P1点所属的详细分区，分别沿着探头的安装位置分为7个区域 */
    ObjMAPAreaType            eP2_DetailArea;          /* P2点所属的详细分区，分别沿着探头的安装位置分为7个区域 */
    eMapObjMoveStsType        eObjMoveSts;             /* 障碍物相对于车辆的运动趋势 */
    eMapObjFollowStsType      eObjFollowSts;           /* 障碍物跟踪状态 */
    OriginalObjType           eMapObjDetailType;       /* 定义Map的详细类型，区分路砍、大墙、PVC管 */
    SideSnsIndexType          enuSideMapSns;           /* 侧边侧边定位Map对应的索引值 */
    eSideMapUpdateStsType     enuSideMapUpdateSts;     /* 侧边Map对应的更新状态 */
    SideSnsIndexType          enuMapPointInSideSnsFov; /* 侧边Map P1 P2点更新到探头正对的位置，用于柱子入侵更新 */
    SideSnsIndexType          enuShortMapInSideSnsFov; /* 侧边短Mpa在探头FOV内的标志 */
    ADAS_APAStatusType        eMapCreatAdasSts;        /* Map创建时对应的泊车状态，用于后续的Map删除、融合处理 */
    ObjTypeCntType            ObjTypeTotalCnt;         /* 该Map缓存中合计的不同类型 的计数 */
    ObjTypeCntType            ObjTypeCntBuf[MAP_TYPE_CNT_BUF_CNT]; /* Map点对应的不同类型的计数缓存 */
    MapDistHeight             MapHeightBuf[MAP_TYPE_CNT_BUF_CNT];
    SnsCarMoveDirType         SideMapCarMovDir;        /* 侧边专用：用于记录构建侧边Map时车辆的运动方向 */
    uint32                    u32CreatMapTime;                             /* 创建Map时对应的系统时间，用于Map缓存满的时候，删除最早创建的点 */
    uint32                    u32UpdateSysTime;                            /* Map更新时对应的系统时间 */
    uint32                    u32BlindNoObjTime;                           /* 盲区内无障碍物累计时间 */
    uint32                    u32BlindWaitDisappearTime;                   /* 盲区等待消失时间 */
    uint32                    u32MapToCarStopTime;                         /* 用于计时Map处于静止状态 */
    uint32                    u32FirstNearDeMatchedTime;                   /* 用于在兜底置低时，记录两帧才置高*/
    uint16                    u16SideMapUpdateMasterDis;                   /* 侧边Map更新时对应的主发距离 */    
    uint16                    u16NoUpdateHoldTime;                         /* Map未被更新持续的时间 */
    uint8                     u8MapExistFlag;          /* Map存在标志位 */
    uint8                     u8MapDisplayFlag;        /* Map显示标志位 */
    uint8                     u8HighCurbCnt;           /* 高路沿特征累计计数 */
    uint8                     u8LowCurbCnt;            /* 低路沿特征计数 */
    uint8                     u8PVC_Cnt;               /* PVC管类型计数 */
    uint8                     u8BigWallCnt;            /* 前后Map专用，大墙类型计数 */
    uint8                     u8SideMapBigWallCnt;     /* 侧边专用：侧边Map大墙计数--用于平行车位入库侧边大墙至高判断 */
    uint8                     u8TotalTypeCnt;          /* 当前Map中总共的类型个数 */
    uint8                     u8BlindNoObjFlag;        /* 盲区内无障碍物标志 */
    uint8                     u8CarCloseToMapBlindFlag;/* 车辆靠近障碍物至盲区的标志 */
    uint8                     u8MapContinueMatchCnt;   /* Map连续匹配的计数 */
    uint8                     u8CarMoveCloseMapFlag;   /* 车辆靠近Map标志 */    
    uint8                     u8CarMoveCloseMapCnt;    /* 车辆靠近Map累计计数 */
	uint8                     u8P1OdoCoorBufCnt;
    uint8                     u8P2OdoCoorBufCnt;
    uint8                     u8LineOdoCoorBufCnt;
    uint8                     u8LineMatchLineCnt;      /*线map和线map的匹配计数*/
    uint8                     u8PointMatchPointCnt;    /*点map和点map的匹配计数*/
    uint8                     u8P_P_MatchContiCnt;     /*点map和点map连续匹配计数 */
    uint8                     u8MapGrenerateType;      /* 前后 Map 生成的来源类型 -- 0：init 1：单主发 2：双主发 3：点云聚类*/
    uint8                     u8MapCreateCarSts;       /* 创建map时，车辆的状态（静止或非静止）-- 0：init 1：静止 2：运动 */

    uint8                     u8HeightLockFlag;        /* 障碍物高度锁住标志位 */
    uint8                     u8ObjTypeUpdateFlag;     /* 障碍物高度类型更新标志位 */
    uint8                     u8Map_DE_NoMatchCnt;     /* Map DE和对应的距离不匹配计数 */
    uint8                     u8MiddleMapToSideFlag;   /* 中间区域Map运动至侧边的标志位 */
    uint8                     u8SideMapAndNewPointNomatchCnt;/* 侧边未结束的Map与新点云不匹配的计数 */
    uint8                     u8MemoryMapDeNoMatchCnt;       /* 中间区域Map运动至侧边记忆的Map与DE 不匹配的计数 */
    uint8                     u8SideShortMapFovNoMatchCnt;     /* 侧边短Map不在FOV内的删除策略 */
    uint8                     u8CarStopFLS_MapDeNoMatchCnt;    /* 用于侧边Map在侧边探头FOV内时，且车辆静止的删除策略 */
    uint8                     u8CarStopFRS_MapDeNoMatchCnt;    /* 用于侧边Map在侧边探头FOV内时，且车辆静止的删除策略 */
    uint8                     u8CarStopRLS_MapDeNoMatchCnt;    /* 用于侧边Map在侧边探头FOV内时，且车辆静止的删除策略 */
    uint8                     u8CarStopRRS_MapDeNoMatchCnt;    /* 用于侧边Map在侧边探头FOV内时，且车辆静止的删除策略 */
    uint8                     u8CreatMapInRearBumperInnerFlag; /* 创建Map时，Map在后保区域的标志位 */
    uint8                     u8SideMapToF_R_MapFlag;          /* 侧边Map进入到前后Map的标志位 */
    uint8                     u8MapToLowLockFlag;              /* Map置低的标志位 */
    uint8                     u8MapBeCutFlag;                  /* 入侵Map被切割的标志位 */
    uint8                     u8MapBeCutCnt;                   /* 入侵Map被切割的次数 */
    uint8                     u8Map_DE_MatchNearHighFlag;      /* Map 近距离DE 匹配快速置高标志位 */
    uint8                     u8StdHeightStartInx;
    uint8                     u8ChirpHeightStartInx;
    uint8                     u8Map_DE_MatchNearHighFlagClearCnt;  /* Map 近距离DE 匹配快速置高标志位清零的计数 */
    uint8                     u8SideMapHeightLockFlag;             /* 侧边Map高低锁住标志，在结束Map时需要锁住高低属性 */
    uint8                     u8Map_SFR_AreaFlag;                  /* Map生成初期所在位置标志位 0:init 1:side 2:front-rear */
    uint8                     u8MapTooLongNeedEndFlag;             /* 侧边Map专用：侧边Map太长，需要结束标志 */
    uint8                     u8SideMapToCarInnerDeNoMatchCnt;     /* 侧边专用：用于侧Map进入到车身内部区域后的删除使用 */
    uint8                     u8SideMapCoverCarDeNoMatchCnt;       /* 侧边专用：用于侧Map 覆盖住车身后的DE 匹配删除使用 */
    uint8                     u8SideMapToCarEdgeDeNoMatchCnt;      /* 侧边专用：用于侧Map进入到车身边沿处的删除策略 */
    uint8                     u8SideMapLowTagFlag;                 /* 侧边专用：用于处理闭合地锁置低的场景 */
    uint8                     u8SideLowMapDeNoMatchCnt;            /* 侧边Map专用：用于处理闭合地锁置低使用 */
    uint8                     u8SideLowMapDeMatchOKCnt;            /* 侧边Map专用：用于处理闭合地锁置低使用 */
    uint8                     u8DEMapStdCnt;                       /* 针对DEmap 记录探头的定频帧数 */
    uint8                     u8DEMapChripCnt;                     /* 针对DEmap 记录探头的扫频帧数 */
    uint8                     u8MapInSideAreaDisplayFlag;          /** @brief:针对客户新增需求，需要把侧边Map单独进行显示，添加对应的标志 */
    uint8                     u8TwoEchoDeMatchedCnt;               /* Map生命周期内有两个二次回波的匹配次数 */
}ObjMapCoorType;

/* 定义有效范围的Obj排序，按照障碍物距离后轴中心的距离进行排序，取最近的点输出到CAN上 */
typedef struct 
{    
    uint16                   u16MapToBumperDis;        /* Map到障碍物的距离 */
    eMapObjIdType            eMapId;                   /* Map到障碍物的ID */
}ObjDisSortType;

typedef struct
{
    uint8                   u8MapNum;                  /* 已经创建Map的计数 */
    eMapObjIdType           eSideMapFusionId;         /* 侧边Map需要融合的Map ID */
    ObjMapCoorType          ObjMapCoor[MAP_OBJ_NUM];   /* 障碍物Map坐标信息 */
}ObjMapInfoType;


/******************************************************************************
* 设计描述 : Map输出到CAN的数据结构
* 设计索引 : 
*******************************************************************************/
typedef struct 
{    
    eMapObjIdType          eMapObjId;                  /* Map ID */
    eMapObjShapeType       eMapObjType;                /* 障碍物类型 */
    eMapProbLevelType      eObjExistProb;              /* 障碍物存在可能性 */
    eMapObjHeightType      eMapObjHeight;              /* 障碍物的高度 */
    eMapProbLevelType      eObjHeightProb;             /* 障碍物高度可能性 */
    sint16                 s16ObjMapP1_X;              /* 障碍物Point1 X坐标 */
    sint16                 s16ObjMapP1_Y;              /* 障碍物Point1 X坐标 */
    sint16                 s16ObjMapP2_X;              /* 障碍物Point1 X坐标 */
    sint16                 s16ObjMapP2_Y;              /* 障碍物Point1 X坐标 */
    uint32                 u32MapObjTimestamp;         /* 时间戳 */
}ObjMapToCANType;


/******************************************************************************
* 设计描述 : 前后雷达PDC分区的数据类型
* 设计索引 : 
*******************************************************************************/
typedef struct 
{    
    uint16                u16MinDis[OBJ_PDC_AREA_NUM]; /* 通过Map计算PDC 分区的距离值 */
}PDC_AreaObjType;


/* 定义大墙类型Map点单元类型 */
typedef struct 
{   
    eMapObjIdType             eMapObjId;
    uint8                     u8BeFusionLineFlag;
}MapBigWallUnitType;

/* 定义大墙类型Map点，用于后续的Map点融合成线 */
typedef struct 
{   
    MapBigWallUnitType      BigWallUnit[MAP_BIG_WALL_BUF_CNT];
    uint8                   u8BigWallPointCnt;
}MapBigWallBufType;


/* 定义线状Map融合单元类型 */
typedef struct 
{   
    float                     fLineMapX_Coor;
    float                     fLineLeftY_Coor;
    float                     fLineRightY_Coor;
    eMapObjIdType             eLineMapId;
    eMapObjIdType             eBeFusionMapId[MAP_LINE_FUSION_POINT_CNT];
    uint8                     u8BeFusionMapCnt;
    uint8                     u8LineMapWaitFusionFlag;
}MapLineFusionUnitType;


typedef struct 
{   
    MapLineFusionUnitType     LineMapUnit[MAP_FUSION_LINE_CNT];
    uint8                     u8WaitFusionLineCnt;
}MapLineBufType;


/******************************************************************************
* 设计描述 :定义侧边Map融合时两直线的关系
* 设计索引 : 
*******************************************************************************/
typedef enum
{
    WAIT_MAP_NEW_MAP_NONE = 0,
    NEW_MAP_P1_WITHIN_WAIT_MAP,        /* 新Map P1点在Wait Map中间*/ 
    NEW_MAP_P2_WITHIN_WAIT_MAP,        /* 新Map P2点在Wait Map中间*/ 
    NEW_MAP_P1_P2_WITHIN_WAIT_MAP,     /* 新Map P1 P2点在Wait Map中间*/ 
    NEW_MAP_P1_BIG_THAN_WAIT_MAP_P2,   /* 新Map P1 点大于Wait MapP2点*/ 
    NEW_MAP_P2_SMALL_THAN_WAIT_MAP_P1, /* 新Map P1 点大于Wait MapP2点*/ 
    WAIT_MAP_P1_P2_WITHIN_NEW_MAP,     /* WAITMap P1 P2点在New Map中间*/
}eSideMapLineRelationType;

#if MAP_PRECISION_PRINT_SWITCH
typedef struct 
{   
    float       P1_Point_X_Real;
    float       P2_Point_X_Real;
    float       P3_Point_X_Real;
    float       P4_Point_X_Real;
    
    float       P1_Point_X_Measure;
    float       P2_Point_X_Measure;
    float       P3_Point_X_Measure;
    float       P4_Point_X_Measure;

    float       P1_Bias;
    float       P2_Bias;
    float       P3_Bias;
    float       P4_Bias;

    float       Obj1_Min_X;
    float       Obj1_Max_X;
    float       Obj2_Min_X;
    float       Obj2_Max_X;

    uint8       u8PrintfFlag;
}MapPrecisionPrintType;

#endif

/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/




#endif /* end of F_R_ObjMapCal_Type_H */

