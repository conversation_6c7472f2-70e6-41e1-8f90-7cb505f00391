/*
 * QUEUE_DSI_SPI_DATA.h
 *
 *  Created on: 2021
 *      Author: 6000021992
 */

#ifndef QUEUE_DSI_SPI_DATA_H_
#define QUEUE_DSI_SPI_DATA_H_

#include "DSI_Queue.h"

/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/
#define DSISPI_RXBuf_NUM 20
#define DSI_SPI_TX_RX_BufLen 72  /*两个通道，三个探头 6+(2+10*3)*2+2*/

typedef struct
{  
    uint8 DSIMasterID;
    uint8 TransSeqID;
    uint16 DataLen;
	uint32 RecTime;
	uint32 ADASSYNTime;
    uint8 SPIResData[DSI_SPI_TX_RX_BufLen];
}DSI_SPI_RX_Data_str;

/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
extern void InitDSI_SPI_RX_Que();
extern uint8 SetDSI_SPI_RX_Data_Ready(uint16 DSI_SPI_RX_QueNum);
extern DSI_SPI_RX_Data_str * GetDSI_SPI_RX_IdleBufAddr(uint16 BufLen,uint8 *pOutBufNum);
extern uint16 GetDSI_SPI_RxReadyData(DSI_SPI_RX_Data_str *pOutData);
extern uint16 GetDSI_SPI_RxReadyCnt();

#endif /* QUEUE_DSI_SPI_DATA_H_ */

