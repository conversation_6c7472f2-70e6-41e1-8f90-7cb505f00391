/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * ApaCalCarCoorSignalManage.c: 
 * Created on: 2020-12-07 14:03
 * Original designer: 22554
 *
 * 获取车身坐标步骤：
 * 1.运行一次函数（注意只运行一次）                            id = CPOSSgnMag_ReadGetCalId(); //记录返回的id填入下一步骤的形参二中。
 * 2.周期调用函数获取车身坐标                                    CPOSSgnMag_ReadCarCoorData(CurCarPointDataType *LpstrCurCarPointData,uint8 id);
 * 3.不需要车身坐标定位时调用清除函数，形参填入id                         CPOSSgnMag_WriteClrCalId(id);
 ******************************************************************************/
/* Includes ------------------------------------------------------------------*/
#include "ODO_AppSignalManage.h"
#include "TimerManage.h"

/******************************************************************************/
/******************************************************************************/
/****************************** Private Definition ****************************/
/******************************************************************************/
/******************************************************************************/
static CPOS_CurCarPointDataType GstrCurCarPointData_Sgn[RELATION_MOD_NUM_COOR];
static uint32 Gu32CarTurnRadian;
static uint64 Gu64CalTimeStamp; /* 车身坐标更新的时间戳 */
static uint8 Gu8CarMoveDirect;



/* ODO信息 */
static ODOCoorInfo GstrODOInfo = 
{
    .fODO_X = 0,
    .fODO_Y = 0,
    .fODO_YawAngle = 0,
    .fODO_SHA = 0,
    .u32ODO_TurnRadian = 5000000u,
    .u32ODO_CalTimeStamp = 0u,
};


/******************************************************************************/
/******************************************************************************/
/****************************** Public Definition *****************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************************
 * 设计描述 : GetEnableCalFlgId
 * 设计索引 : 
 *******************************************************************************/
uint8 CPOSSgnMag_ReadGetCalId(void)
{
    uint8 id;
    id = CPOS_SetEnableCalFlg();
    return id;
}


/******************************************************************************
 * 设计描述 : ClrEnableCalFlgId
 * 设计索引 : 
 *******************************************************************************/
void CPOSSgnMag_WriteClrCalId(uint8 id)
{
    CPOS_CurCarPointDataType LpstrCurCarPointData;
    CPOS_ClrEnableCalFlg(id);
    
    LpstrCurCarPointData.fX = 0;
    LpstrCurCarPointData.fY = 0;
    LpstrCurCarPointData.fAngle = 0;
    CPOSSgnMag_WriteCarCoorData(LpstrCurCarPointData,id);/* Clear ID 的同时需要清零对应的坐标接口 */  
}


/******************************************************************************
 * 设计描述 : 获取车身坐标数据
 * 设计索引 : 
 *******************************************************************************/
void CPOSSgnMag_ReadCarCoorData(CPOS_CurCarPointDataType *LpstrCurCarPointData,uint8 id)
{
    if(id >= RELATION_MOD_NUM_COOR)
    {
        LpstrCurCarPointData->fAngle = 0;
        LpstrCurCarPointData->fCosAngle = 1;
        LpstrCurCarPointData->fSinAngle = 0;
        LpstrCurCarPointData->fDrvDis = 0;
        LpstrCurCarPointData->fX = 0;
        LpstrCurCarPointData->fY = 0;
        LpstrCurCarPointData->TurnRadius = 5000000u;
        return;
    }
    
    *LpstrCurCarPointData = GstrCurCarPointData_Sgn[id];
}

void CPOSSgnMag_WriteCarCoorData(CPOS_CurCarPointDataType LpstrCurCarPointData,uint8 id)
{
    GstrCurCarPointData_Sgn[id] = LpstrCurCarPointData;
}


/******************************************************************************
 * 设计描述 : 获取车身移动过方向
 * 设计索引 : 
 *******************************************************************************/
uint8 CPOSSgnMag_ReadCarMoveDirect(void)
{
    return Gu8CarMoveDirect;
}

void CPOSSgnMag_WriteCarMoveDirect(uint8 Lu8Direct)
{
    Gu8CarMoveDirect = Lu8Direct;
}



/******************************************************************************
 * 设计描述 : 获取车身当前转弯半径
 * 设计索引 : 
 *******************************************************************************/
void CPOSSgnMag_ReadCarTurnRadian(uint32 *LpCarTurnRadian)
{
    *LpCarTurnRadian = Gu32CarTurnRadian;
}

void CPOSSgnMag_WriteCarTurnRadian(uint32 LpCarTurnRadian)
{
    Gu32CarTurnRadian = LpCarTurnRadian;
}

/******************************************************************************
 * 设计描述 : 获取车身坐标更新时间
 * 设计索引 : 
 *******************************************************************************/
void CPOSSgnMag_ReadCalTimeStamp(uint64 *LpTimeStamp)
{
    *LpTimeStamp = Gu64CalTimeStamp;
}

void CPOSSgnMag_WriteCalTimeStamp(uint64 Lu64TimeStamp)
{
    Gu64CalTimeStamp = Lu64TimeStamp;
}


//static uint8 Gu8ODO_CPOSIdx = 0xFFu;
uint8 Gu8ODO_CPOSIdx = 0xFFu;


/******************************************************************
* 函数名称: WriteAPASignal_ODOInfo
*
* 功能描述: 更新ODO信息，ODO输出车身坐标及斜率
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 无
*
* 修改日期      版本号      修改人      修改内容
**********************************************************************/ 

void WriteAPASignal_ODOInfo(ODOCoorInfo *LpstrOdoCoorInfo)
{
    GstrODOInfo.fODO_X = LpstrOdoCoorInfo->fODO_X;
    GstrODOInfo.fODO_Y = LpstrOdoCoorInfo->fODO_Y;
    GstrODOInfo.fODO_YawAngle = LpstrOdoCoorInfo->fODO_YawAngle;
    GstrODOInfo.fODO_SHA = LpstrOdoCoorInfo->fODO_SHA;
    GstrODOInfo.u32ODO_TurnRadian = LpstrOdoCoorInfo->u32ODO_TurnRadian;
    GstrODOInfo.u32ODO_CalTimeStamp = LpstrOdoCoorInfo->u32ODO_CalTimeStamp;
}


/******************************************************************
* 函数名称: ReadAPASignal_ODOInfo
*
* 功能描述: 获取ODO信息，ODO输出车身坐标及斜率
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 无
*
* 修改日期      版本号      修改人      修改内容
**********************************************************************/ 

void ReadAPASignal_ODOInfo(ODOCoorInfo *LpstrOdoCoorInfo)
{
    LpstrOdoCoorInfo->fODO_X = GstrODOInfo.fODO_X;
	LpstrOdoCoorInfo->fODO_Y = GstrODOInfo.fODO_Y;
	LpstrOdoCoorInfo->fODO_YawAngle = GstrODOInfo.fODO_YawAngle;
	LpstrOdoCoorInfo->fODO_SHA = GstrODOInfo.fODO_SHA;
	LpstrOdoCoorInfo->u32ODO_TurnRadian = GstrODOInfo.u32ODO_TurnRadian;
	LpstrOdoCoorInfo->u32ODO_CalTimeStamp = GstrODOInfo.u32ODO_CalTimeStamp;

}

/******************************************************************
* 函数名称: CalODOCoordinate
*
* 功能描述: 计算ODO坐标
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 无
*
* 修改日期      版本号      修改人      修改内容
**********************************************************************/
void CalODOCoordinate(void)
{
    CPOS_CurCarPointDataType LstrCarPointData;
    ODOCoorInfo LstrODOInfo;
    uint32 Lu32Radian;
    uint64 Lu64TimeStamp;

    if(Gu8ODO_CPOSIdx == 0xFFu) /* 初始化为0m 0° */
    {
        Gu8ODO_CPOSIdx = CPOSSgnMag_ReadGetCalId();
        LstrODOInfo.fODO_X = 0;
        LstrODOInfo.fODO_Y = 0;
        LstrODOInfo.fODO_YawAngle = 0;
        LstrODOInfo.fODO_SHA = 0;
        LstrODOInfo.u32ODO_TurnRadian = 5000000u;
        LstrODOInfo.u32ODO_CalTimeStamp = GetCANTimeSysn_GlobalTimeBase();
    }
    else
    {
        CPOSSgnMag_ReadCarCoorData(&LstrCarPointData, Gu8ODO_CPOSIdx);
        CPOSSgnMag_ReadCarTurnRadian(&Lu32Radian);
        CPOSSgnMag_ReadCalTimeStamp(&Lu64TimeStamp);
        LstrODOInfo.fODO_X = LstrCarPointData.fX;
        LstrODOInfo.fODO_Y = LstrCarPointData.fY;
        LstrODOInfo.fODO_YawAngle = LstrCarPointData.fAngle;
        LstrODOInfo.fODO_SHA = LstrCarPointData.fDrvDis;
        LstrODOInfo.u32ODO_TurnRadian = Lu32Radian;
        LstrODOInfo.u32ODO_CalTimeStamp = Lu64TimeStamp;
    }
    WriteAPASignal_ODOInfo(&LstrODOInfo);
}

/******************************************************************************/
/******************************************************************************/
/****************************** Private Function ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/******************************* Public Function ******************************/
/******************************************************************************/
/******************************************************************************/

