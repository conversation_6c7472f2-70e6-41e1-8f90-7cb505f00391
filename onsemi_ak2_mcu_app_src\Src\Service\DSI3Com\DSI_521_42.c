/*
 * DSI3_521_42.c
 *
 *  Created on: 2021年4月1日
 *      Author: 6000021992
 */
#include "AK2_MCU_Drv.h"
#include "DSI_521_42.h"
#include "DSI3_SPI.h"

#pragma location = "R_DMA_DATA"
uint8 DSIRegTxBuf[46] = {0};

#pragma location = "R_DMA_DATA"
uint8 DSIRegRxBuf[46] = {0};

/*读写 521.42Reg CMD + Addr*/
const uint8 GWRRegCMDGroup[WR_RegNum][2]=
{
        [W1C_IRQ_STAT]            = {0xC0,0x60},
        [W1C_SPI_IRQ_STAT]        = {0xC1,0x0E},
        [W1C_DSI1_IRQ_STAT]       = {0xC1,0x8E},
        [W1C_DSI2_IRQ_STAT]       = {0xC1,0xCE},

        [W_VDSI_CTRL]             = {0xC0,0x2E},
        [W_IRQ_EN]                = {0xC0,0x61},
        [W_SPI_IRQ_EN]            = {0xC1,0x0F},

        [W_DSI1_MODE]             = {0xC1,0x80},
        [W_DSI1_CFG]              = {0xC1,0x81},
        [W_DSI1_IRQ_EN]           = {0xC1,0x8F},
        [W_DSI1_TIMING_OFFSET]    = {0xC1,0xA0},
        [W_DSI1_DM_START_TIME_BC] = {0xC1,0xA1},

        [W_DSI2_MODE]             = {0xC1,0xC0},
        [W_DSI2_CFG]              = {0xC1,0xC1},
        [W_DSI2_IRQ_EN]           = {0xC1,0xCF},
        [W_DSI2_TIMING_OFFSET]    = {0xC1,0xE0},
        [W_DSI2_DM_START_TIME_BC] = {0xC1,0xE1},

        [W_WAVE_FALL]             = {0xC8,0x00},
        [W_WAVE_RISE]             = {0xC8,0x48},


        [R_VDSI_CTRL]             = {0x80,0x2E},
        [R_IRQ_EN]                = {0x80,0x61},
        [R_SPI_IRQ_EN]            = {0x81,0x0F},

        [R_DSI1_MODE]             = {0x81,0x80},
        [R_DSI1_CFG]              = {0x81,0x81},
        [R_DSI1_IRQ_EN]           = {0x81,0x8F},
        [R_DSI1_TIMING_OFFSET]    = {0x81,0xA0},
        [R_DSI1_DM_START_TIME_BC] = {0x81,0xA1},

        [R_DSI2_MODE]             = {0x81,0xC0},
        [R_DSI2_CFG]              = {0x81,0xC1},
        [R_DSI2_IRQ_EN]           = {0x81,0xCF},
        [R_DSI2_TIMING_OFFSET]    = {0x81,0xE0},
        [R_DSI2_DM_START_TIME_BC] = {0x81,0xE1},

        [R_WAVE_FALL]             = {0x88,0x00},
        [R_WAVE_RISE]             = {0x88,0x48},

        [R_IRQ_STAT]              = {0x80,0x60},
        [R_SPI_IRQ_STAT]          = {0x81,0x0E},
        [R_DSI1_IRQ_STAT]         = {0x81,0x8E},
        [R_DSI2_IRQ_STAT]         = {0x81,0xCE},

        [R_MAIN_STAT]             = {0x80,0x21},
        [R_VDSI_STAT]             = {0x80,0x2D},
        [R_IC_REVISION]           = {0x80,0x3C},
        [R_CHIP_ID_LOW]           = {0x80,0x3D},
        [R_CHIP_ID_HIGH]          = {0x80,0x3E},


        [R_STATUS_WORD]           = {0x81,0x18},

        [R_DSI1_PDCM_PERIOD]      = {0x81,0x82},
        [R_DSI1_STAT]             = {0x81,0x83},
        [R_DSI1_CMD_LOW]          = {0x81,0x90},
        [R_DSI1_CMD_HIGH]         = {0x81,0x91},
        [R_DSI1_STAT2]            = {0x81,0x9F},

        [R_DSI2_PDCM_PERIOD]      = {0x81,0xC2},
        [R_DSI2_STAT]             = {0x81,0xC3},
        [R_DSI2_CMD_LOW]          = {0x81,0xD0},
        [R_DSI2_CMD_HIGH]         = {0x81,0xD1},
        [R_DSI2_STAT2]            = {0x81,0xDF},

        [checkAdr1]               = {0x82,0x3d},
        [checkAdr2]               = {0x84,0x37},
        [checkAdr3]               = {0x82,0x48},
        [checkAdr4]               = {0x82,0xdc},
        [checkAdr5]               = {0x83,0x70},
        [checkAdr6]               = {0x84,0x42},
        [checkAdr7]               = {0x84,0xd6},
        [checkAdr8]               = {0x85,0x6a},        
        [checkAdr9]               = {0x82,0x00},
        [checkAdr10]              = {0x82,0x01},
        [checkAdr11]              = {0x82,0x02},
        [checkAdr12]              = {0x82,0x03},
        [checkAdr13]              = {0x82,0x04},
        [checkAdr14]              = {0x82,0x05},
        [checkAdr15]              = {0x82,0x06},
        [checkAdr16]              = {0x82,0x07}, 
        [checkAdr17]              = {0x82,0x08},
        [checkAdr18]              = {0x82,0x09},
        [checkAdr19]              = {0x82,0x0a},

};

const uint16 RegINIT_Check[19]=
{
        [checkAdr1 - checkAdr1]               = 0x0000,
        [checkAdr2 - checkAdr1]               = 0x0000,
        [checkAdr3 - checkAdr1]               = 0x0000,
        [checkAdr4 - checkAdr1]               = 0x0000,
        [checkAdr5 - checkAdr1]               = 0x0000,
        [checkAdr6 - checkAdr1]               = 0x0000,
        [checkAdr7 - checkAdr1]               = 0x0000,
        [checkAdr8 - checkAdr1]               = 0x0000,        
        [checkAdr9 - checkAdr1]               = 0x004F,
        [checkAdr10 - checkAdr1]              = 0x006A,
        [checkAdr11 - checkAdr1]              = 0x0084,
        [checkAdr12 - checkAdr1]              = 0x009F,
        [checkAdr13 - checkAdr1]              = 0x00B9,
        [checkAdr14 - checkAdr1]              = 0x00D4,
        [checkAdr15 - checkAdr1]              = 0x00EF,
        [checkAdr16 - checkAdr1]              = 0x0109, 
        [checkAdr17 - checkAdr1]              = 0x0136,
        [checkAdr18 - checkAdr1]              = 0x026C,
        [checkAdr19 - checkAdr1]              = 0x04D8,
};

/***************************************************************************//**
 * @brief
 * 传输写521.42 寄存器数据
 *
 * @param       DSIMASTERID   521.42 索引ID
 * @param       WRegCmdID     写521.42 寄存器索引ID
 * @param       wData              写入的数据
 * @return      0xFF          无空闲SPI_Seq或输入参数错误、写入数据与返回数据不一致
 * @return      0xFD          写入数据与返回数据不一致
 *              SeqID
 *
 * ****************************************************************************/
DSIReturnType_en TransWMasterDSIReg(DSIMasterID_en DSIMasterID,WRReg_en WRegCmdID,uint16 WData)
{
    uint16 wCrc = 0;
    uint8 Lcnt;
    DSISPI_SeqID SeqID = DSISPI_SEQ_NULL;
    if(WRegCmdID >= W_RegNum)
    {
        return eDSIReturnType_InData_ERR;
    }

	memset((void*)&DSIRegTxBuf[0],0,8);
	memset((void*)&DSIRegRxBuf[0],0,8);

    DSIRegTxBuf[0] = GWRRegCMDGroup[WRegCmdID][0];
    DSIRegTxBuf[1] = GWRRegCMDGroup[WRegCmdID][1];
    DSIRegTxBuf[2] = (WData >> 8);
    DSIRegTxBuf[3] = (WData & 0xFF);
    wCrc = DSI_SPI_CRC16Calculate(DSIRegTxBuf,4);
    DSIRegTxBuf[4] = wCrc >> 8;
    DSIRegTxBuf[5] = wCrc & 0xFF;

    /*SPI 发送*/
    SeqID = DSI_SPI_SyncTransfer ((uint8)DSIMasterID,DSIRegTxBuf,8,DSIRegRxBuf,5);
    if(SeqID != DSISPI_SEQ_NULL)
    {
        for(Lcnt=0;Lcnt<6;Lcnt++)
        {
            if(DSIRegTxBuf[Lcnt] != DSIRegRxBuf[Lcnt + 2])
            {
                /** @brief 写返回不一致 */
                return eDSIReturnType_Resp_Data_N_OK;
            }
        }
    }
    else
    {
        return eDSIReturnType_N_OK;
    }
    
    return eDSIReturnType_OK;
}

/***************************************************************************//**
 * @brief
 * 传输读521.42 寄存器
 *
 * @param       DSIMASTERID   521.42 索引ID
 * @param       RRegCmdID     读521.42 寄存器索引ID
 * @param       pRBuff             读数据保存
 * @return      0xFF          无空闲SPI_Seq或输入参数错误
 * @return      0xFE          接收数据CRC错误
 * @return      0xFD          接收数据指令错误
 *              SeqID
 *
 * ****************************************************************************/
DSIReturnType_en TransRMasterDSIReg(DSIMasterID_en DSIMasterID,WRReg_en RRegCmdID,uint16 *pOutRegData)
{
    DSISPI_SeqID SeqID = DSISPI_SEQ_NULL;
    uint16 CalcCRC16,RxCrc16;
    if((RRegCmdID < W_RegNum)
    || (RRegCmdID >= WR_RegNum)
    || (pOutRegData == NULL))
    {
        return eDSIReturnType_InData_ERR;
    }

	memset((void*)&DSIRegTxBuf[0],0,8);
	memset((void*)&DSIRegRxBuf[0],0,8);
	
    DSIRegTxBuf[0] = GWRRegCMDGroup[RRegCmdID][0];
    DSIRegTxBuf[1] = GWRRegCMDGroup[RRegCmdID][1] ;
    
    *pOutRegData = ReadRegInvalidValue;
    
    /*SPI 发送*/
    SeqID = DSI_SPI_SyncTransfer ((uint8)DSIMasterID,DSIRegTxBuf,8,DSIRegRxBuf,5);
    if(SeqID != DSISPI_SEQ_NULL)
    {
        CalcCRC16 = DSI_SPI_CRC16Calculate(&DSIRegRxBuf[2],4);
        RxCrc16 = ((DSIRegRxBuf[6] << 8) | DSIRegRxBuf[7]);
        /** @brief CRC校验 */
        if(CalcCRC16 == RxCrc16)
        {
            if((DSIRegTxBuf[0] == DSIRegRxBuf[2]) && (DSIRegTxBuf[1] == DSIRegRxBuf[3]))
            {
                /** @brief 返回寄存器指令正确,提取寄存器值 */
                *pOutRegData = (DSIRegRxBuf[4] << 8) | DSIRegRxBuf[5];
            }
            else
            {
                /** @brief 返回读寄存器指令错误 */
                return eDSIReturnType_Resp_Data_N_OK;
            }
        }
        else
        {
            return eDSIReturnType_Resp_CRC_N_OK;
        }
    }
    else
    {
        return eDSIReturnType_N_OK;
    }
    return eDSIReturnType_OK;
}

/***************************************************************************//**
 * @brief
 * 传输读521.42 IC状态
 *
 * @param       DSIMASTERID   521.42 索引ID
 * @param       pOutICStatus  IC状态保存地址
 * @return      0xFF          无空闲SPI_Seq或输入参数错误
 *              SeqID
 *
 * ****************************************************************************/
DSIReturnType_en TransRMasterICStatus(DSIMasterID_en DSIMasterID,uint16 *pOutICStatus)
{
    
    DSISPI_SeqID SeqID = DSISPI_SEQ_NULL;
    if(pOutICStatus == NULL)
    {
        return eDSIReturnType_InData_ERR;
    }

	memset((void*)&DSIRegTxBuf[0],0,2);
	memset((void*)&DSIRegRxBuf[0],0,2);
	
    DSIRegTxBuf[0] = 0;
    DSIRegTxBuf[1] = 0;

    /*SPI 发送  */
    SeqID = DSI_SPI_SyncTransfer ((uint8)DSIMasterID,DSIRegTxBuf,2,DSIRegRxBuf,5);
    if(SeqID != DSISPI_SEQ_NULL)
    {
        *pOutICStatus = (DSIRegRxBuf[0] << 8) | DSIRegRxBuf[1];
    }
    else
    {
        return eDSIReturnType_N_OK;
    }
    return eDSIReturnType_OK;
}

/***************************************************************************//**
 * @brief
 * 传输写入TDMA 配置
 *
 * @param[in]        DSICfg
 * @param[in]        *pFRAME_TDMA_SCHEME
 * @return      0xFF          无空闲SPI_Seq或输入参数错误
 * @return      0xFD          写入数据与返回数据不一致
 *              SeqID
 *
 * ****************************************************************************/
DSIReturnType_en TransUpload_DSI_TDMA_SCHEME(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL,FRAME_TDMA_SCHEME_str *pFRAME_TDMA_SCHEME)
{
    DSISPI_SeqID SeqID = DSISPI_SEQ_NULL;
    uint8 PacketCount;
    uint16 wCrc = 0;
    uint8 LcCrcSize = 42;
    uint8 LcTxSize = 46;
    uint8 Lcnt;
    uint8 DataOffset;
	
    if(pFRAME_TDMA_SCHEME == NULL)
    {
        return eDSIReturnType_InData_ERR;
    }

    memset((void*)&DSIRegTxBuf[0],0,sizeof(DSIRegTxBuf));
    memset((void*)&DSIRegRxBuf[0],0,sizeof(DSIRegRxBuf));
    
    DSIRegTxBuf[0] = UpLoadTDMA_CMD_Code;
    DSIRegTxBuf[1] = DSIChlSEL & 0x03;

    PacketCount = pFRAME_TDMA_SCHEME->PacketCount;

    if(PacketCount > 6 || PacketCount == 0)
    {
        return eDSIReturnType_InData_ERR;
    }

    /** @brief CRC计算长度及SPI传送数据长度 */
    LcCrcSize = 6 + PacketCount * 6;
    LcTxSize  = 4 + LcCrcSize;

    /** @brief 提取TDMA数据 */
    DSIRegTxBuf[2] = pFRAME_TDMA_SCHEME->PDCM_PERIOD >> 8;
    DSIRegTxBuf[3] = pFRAME_TDMA_SCHEME->PDCM_PERIOD & 0xFF;
    DSIRegTxBuf[4] = pFRAME_TDMA_SCHEME->BP & 0xC0;
    DSIRegTxBuf[5] = pFRAME_TDMA_SCHEME->PacketCount & 0x1F;

    for(Lcnt=0;Lcnt<PacketCount;Lcnt++)
    {
        DataOffset = Lcnt * 6;
        DSIRegTxBuf[6 + DataOffset] = pFRAME_TDMA_SCHEME->PacketSCHEME[Lcnt].EarliestStartTime >> 8;
        DSIRegTxBuf[7 + DataOffset] = pFRAME_TDMA_SCHEME->PacketSCHEME[Lcnt].EarliestStartTime & 0xFF;
        DSIRegTxBuf[8 + DataOffset] = pFRAME_TDMA_SCHEME->PacketSCHEME[Lcnt].LatestStartTime >> 8;
        DSIRegTxBuf[9 + DataOffset] = pFRAME_TDMA_SCHEME->PacketSCHEME[Lcnt].LatestStartTime & 0xFF;
        DSIRegTxBuf[10+ DataOffset] = pFRAME_TDMA_SCHEME->PacketSCHEME[Lcnt].ID & 0xC0;
        DSIRegTxBuf[11+ DataOffset] = pFRAME_TDMA_SCHEME->PacketSCHEME[Lcnt].SymbolCount & 0xFF;
    }
    
    /** @brief 计数CRC */
    wCrc = DSI_SPI_CRC16Calculate(DSIRegTxBuf,LcCrcSize);
    DSIRegTxBuf[LcCrcSize] = wCrc >> 8;
    DSIRegTxBuf[LcCrcSize+1] = wCrc & 0xFF;
    
    /** @brief SPI传输 */
    SeqID = DSI_SPI_SyncTransfer ((uint8)DSIMasterID,DSIRegTxBuf,LcTxSize,DSIRegRxBuf,5);

    if(SeqID != DSISPI_SEQ_NULL)
    {
        LcTxSize -= 2;/** @brief 比较返回数据长度=传输数据长度-2         */
        for(Lcnt=0;Lcnt<LcTxSize;Lcnt++)
        {
            if(DSIRegTxBuf[Lcnt] != DSIRegRxBuf[Lcnt + 2])
            {
                /** @brief 写返回不一致 */
                return eDSIReturnType_Resp_Data_N_OK;
            }
        }
    }
    else
    {
        return eDSIReturnType_N_OK;
    }
    return eDSIReturnType_OK;
}

