/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * PDC_MEB_CalibPara: 
 * Created on: 2023-02-28 09:16
 * Original designer: 22996
 ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "SDW_CalibPara.h"
#include "CAN_AppSignalManage.h"

#define X02_FLS_FL_SDWBumperDIS  243
#define X02_RLS_RL_SDWBumperDIS  372

#define X02_FRONT_SDWANGLE_COS  0.6541
#define X02_FRONT_SDWANGLE_SIN  0.7564
#define X02_REAR_SDWANGLE_COS  0.5256
#define X02_REAR_SDWANGLE_SIN  0.8507


#define X03_FLS_FL_SDWBumperDIS  248
#define X03_RLS_RL_SDWBumperDIS  455

#define X03_FRONT_SDWANGLE_COS  0.6756
#define X03_FRONT_SDWANGLE_SIN  0.7373
#define X03_REAR_SDWANGLE_COS  0.4885
#define X03_REAR_SDWANGLE_SIN  0.8725

#define X04_FLS_FL_SDWBumperDIS  374
#define X04_RLS_RL_SDWBumperDIS  391

#define X04_FRONT_SDWANGLE_COS  0.6960
#define X04_FRONT_SDWANGLE_SIN  0.7180
#define X04_REAR_SDWANGLE_COS  0.5157
#define X04_REAR_SDWANGLE_SIN  0.8568

#define W02_FLS_FL_SDWBumperDIS  281
#define W02_RLS_RL_SDWBumperDIS  448

#define W02_FRONT_SDWANGLE_COS  0.6898
#define W02_FRONT_SDWANGLE_SIN  0.7241
#define W02_REAR_SDWANGLE_COS  0.4118
#define W02_REAR_SDWANGLE_SIN  0.9113

/******************************************************************************
* 车型配置 : X02
*******************************************************************************/
const SDW_Dis_CarParameterType GstrSDW_DisCarPara_X02 = 
{
    .LfVehicleHalfWidth = 999,
    .LfFrontSideToCarCenter = 954.3,
    .LfRearSideToCarCenter = 926.2,
    .LfFrontSideToRearAxleDis = 3531.2,
    .LfRearSideToRearAxleDis = 692,
    .LfArea1EndDis = 3003,
    .LfArea2EndDis = 2475,
    .LfArea3EndDis = 1947,
    .LfArea4EndDis = 1420,
    .LfArea5EndDis = 892,
    .LfArea6EndDis = 365,
    .LfArea7EndDis = -163,
    .LfRearWheelToCarTailDis = -1110,
    .LfRearWheelToCarHeadDis = 3950,
    .u16StartFollowDis = 2000,
    .u8ObjBufferMaxCnt = 43,
	.LfFLS_FLBumperDis = X02_FLS_FL_SDWBumperDIS,
	.LfRLS_RLBumperDis = X02_RLS_RL_SDWBumperDIS, 
	.LfFrontSDWAngleCos = X02_FRONT_SDWANGLE_COS, 
	.LfFrontSDWAngleSin = X02_FRONT_SDWANGLE_SIN, 
	.LfRearSDWAngleCos = X02_REAR_SDWANGLE_COS,	
	.LfRearSDWAngleSin = X02_REAR_SDWANGLE_SIN
};

const SDW_CompDisance_Type GstrSDW_CompDisPara_X02 =
{
    .FLS_CompDisance =0,
    .FRS_CompDisance =0,
    .RRS_CompDisance =0,
    .RLS_CompDisance =0
};


/******************************************************************************
* 车型配置 : X03
*******************************************************************************/
const SDW_Dis_CarParameterType GstrSDW_DisCarPara_X03 = 
{
    .LfVehicleHalfWidth = 999,
    .LfFrontSideToCarCenter = 962.8,
    .LfRearSideToCarCenter = 939.9,
    .LfFrontSideToRearAxleDis = 3535.8,
    .LfRearSideToRearAxleDis = 556.9,
    .LfArea1EndDis = 3024.2,
    .LfArea2EndDis = 2512.6,
    .LfArea3EndDis = 2001,
    .LfArea4EndDis = 1489.44,
    .LfArea5EndDis = 977.85,
    .LfArea6EndDis = 466.26,
    .LfArea7EndDis = -45.33,
    .LfRearWheelToCarTailDis = -1055,
    .LfRearWheelToCarHeadDis = 3945,
    .u16StartFollowDis = 2000,
    .u8ObjBufferMaxCnt = 41,
    .LfFLS_FLBumperDis = X03_FLS_FL_SDWBumperDIS,
	.LfRLS_RLBumperDis = X03_RLS_RL_SDWBumperDIS, 
	.LfFrontSDWAngleCos = X03_FRONT_SDWANGLE_COS, 
	.LfFrontSDWAngleSin = X03_FRONT_SDWANGLE_SIN, 
	.LfRearSDWAngleCos = X03_REAR_SDWANGLE_COS,	
	.LfRearSDWAngleSin = X03_REAR_SDWANGLE_SIN
};

const SDW_CompDisance_Type GstrSDW_CompDisPara_X03 =
{
    .FLS_CompDisance =0,
    .FRS_CompDisance =0,
    .RRS_CompDisance =0,
    .RLS_CompDisance =0
};

/******************************************************************************
* 车型配置 : X04
*******************************************************************************/
const SDW_Dis_CarParameterType GstrSDW_DisCarPara_X04 = 
{
    .LfVehicleHalfWidth = 980,
    .LfFrontSideToCarCenter = 943.1,
    .LfRearSideToCarCenter = 926.4,
    .LfFrontSideToRearAxleDis = 3414.8,
    .LfRearSideToRearAxleDis = 594.8,
    .LfArea1EndDis = 2913.6,
    .LfArea2EndDis = 2412.4,
    .LfArea3EndDis = 1911.2,
    .LfArea4EndDis = 1410,
    .LfArea5EndDis = 908.8,
    .LfArea6EndDis = 407.6,
    .LfArea7EndDis = -93.6,
    .LfRearWheelToCarTailDis = -1040,
    .LfRearWheelToCarHeadDis = 3860,
    .u16StartFollowDis = 2000,
    .u8ObjBufferMaxCnt = 40,
    .LfFLS_FLBumperDis = X04_FLS_FL_SDWBumperDIS,
	.LfRLS_RLBumperDis = X04_RLS_RL_SDWBumperDIS, 
	.LfFrontSDWAngleCos = X04_FRONT_SDWANGLE_COS, 
	.LfFrontSDWAngleSin = X04_FRONT_SDWANGLE_SIN, 
	.LfRearSDWAngleCos = X04_REAR_SDWANGLE_COS,	
	.LfRearSDWAngleSin = X04_REAR_SDWANGLE_SIN
};

const SDW_CompDisance_Type GstrSDW_CompDisPara_X04 =
{
    .FLS_CompDisance =0,
    .FRS_CompDisance =0,
    .RRS_CompDisance =0,
    .RLS_CompDisance =0
};

/******************************************************************************
* 车型配置 : W02
*******************************************************************************/
const SDW_Dis_CarParameterType GstrSDW_DisCarPara_W02 = 
{
    .LfVehicleHalfWidth = 973,
    .LfFrontSideToCarCenter = 939.9,
    .LfRearSideToCarCenter = 917,
    .LfFrontSideToRearAxleDis = 3561.8,
    .LfRearSideToRearAxleDis = 616.2,
    .LfArea1EndDis = 3039.55,
    .LfArea2EndDis = 2517.3,
    .LfArea3EndDis = 1995.05,
    .LfArea4EndDis = 1472.8,
    .LfArea5EndDis = 950.55,
    .LfArea6EndDis = 428.3,
    .LfArea7EndDis = -93.95,
    .LfRearWheelToCarTailDis = -1149,
    .LfRearWheelToCarHeadDis = 3931,
    .u16StartFollowDis = 2000,
    .u8ObjBufferMaxCnt = 40,
    .LfFLS_FLBumperDis = W02_FLS_FL_SDWBumperDIS,
    .LfRLS_RLBumperDis = W02_RLS_RL_SDWBumperDIS,
    .LfFrontSDWAngleCos = W02_FRONT_SDWANGLE_COS,
    .LfFrontSDWAngleSin = W02_FRONT_SDWANGLE_SIN,
    .LfRearSDWAngleCos = W02_REAR_SDWANGLE_COS,
    .LfRearSDWAngleSin = W02_REAR_SDWANGLE_SIN
};

const SDW_CompDisance_Type GstrSDW_CompDisPara_W02 =
{
    .FLS_CompDisance =0,
    .FRS_CompDisance =0,
    .RRS_CompDisance =0,
    .RLS_CompDisance =0
};

/*根据不同距离下的回波高度收窄侧探头FOV，用于没有侦听值时输出距离*/
const SDW_HeightThreType SDW_HeightThreTable[SDW_SNS_DIS_HEIGHT_TOTAL_NUM] = 
{
    [SDW_SNS_DIS_HEIGHT_100mm] = 
    {
		.u16StdHeight = 0,
    	.u16AdvHeight = 0,
    },
	[SDW_SNS_DIS_HEIGHT_200mm] = 
	{
		.u16StdHeight = 35000,//50000
		.u16AdvHeight = 0,
	},
    [SDW_SNS_DIS_HEIGHT_300mm] = 
    {
		.u16StdHeight = 16800,//24000
    	.u16AdvHeight = 10500,//15000
    },
	[SDW_SNS_DIS_HEIGHT_400mm] = 
	{
		.u16StdHeight = 11200,//16000
		.u16AdvHeight = 9800,//14000
	},
	[SDW_SNS_DIS_HEIGHT_500mm] = 
    {
		.u16StdHeight = 11200,//16000
    	.u16AdvHeight = 7000,//10000
    },
	[SDW_SNS_DIS_HEIGHT_600mm] = 
	{
		.u16StdHeight = 6300,//9000
		.u16AdvHeight = 4900,//7000
	},
	[SDW_SNS_DIS_HEIGHT_700mm] = 
	{
		.u16StdHeight = 4900,//7000
		.u16AdvHeight = 4200,//6000
	},
	[SDW_SNS_DIS_HEIGHT_800mm] = 
	{
		.u16StdHeight = 3500,//5000
		.u16AdvHeight = 3500,//5000
	},
	[SDW_SNS_DIS_HEIGHT_900mm] = 
	{
		.u16StdHeight = 2800,//4000
		.u16AdvHeight = 2800,//4000
	},
	[SDW_SNS_DIS_HEIGHT_1000mm] = 
	{
		.u16StdHeight = 2100,//3000
		.u16AdvHeight = 2100,//3000
	},
	[SDW_SNS_DIS_HEIGHT_1100mm] = 
	{
		.u16StdHeight = 1800,//
		.u16AdvHeight = 1800,//
	},
	[SDW_SNS_DIS_HEIGHT_1200mm] = 
	{
		.u16StdHeight = 1500,//
		.u16AdvHeight = 1500,//
	},	
	[SDW_SNS_DIS_HEIGHT_1300mm] = 
	{
		.u16StdHeight = 1300,//
		.u16AdvHeight = 1300,//
	},	
	[SDW_SNS_DIS_HEIGHT_1400mm] = 
	{
		.u16StdHeight = 1100,//
		.u16AdvHeight = 1100,//
	},	
	[SDW_SNS_DIS_HEIGHT_1500mm] = 
	{
		.u16StdHeight = 1000,//
		.u16AdvHeight = 1000,//
	},	
	[SDW_SNS_DIS_HEIGHT_1600mm] = 
	{
		.u16StdHeight = 900,//
		.u16AdvHeight = 900,//
	},	
	[SDW_SNS_DIS_HEIGHT_1700mm] = 
	{
		.u16StdHeight = 800,//
		.u16AdvHeight = 800,//
	},	
	[SDW_SNS_DIS_HEIGHT_1800mm] = 
	{
		.u16StdHeight = 700,//
		.u16AdvHeight = 700,//
	},	
	[SDW_SNS_DIS_HEIGHT_1900mm] = 
	{
		.u16StdHeight = 600,//
		.u16AdvHeight = 600,//
	},	
	[SDW_SNS_DIS_HEIGHT_2000mm] = 
	{
		.u16StdHeight = 500,//
		.u16AdvHeight = 500,//
	},	
};

/******************************SDW标定参数_存储在Ram中--*********************************************/
SDW_Dis_CarParameterType GstrSDW_DisCarPara_Ram;
SDW_CompDisance_Type     GstrSDW_CompDisPara_Ram;




/******************************************************************************
* 设计描述 : SDW静态参数区分
* 设计索引 : 
*******************************************************************************/
void SDW_CalibPara_Config_Init(void)
{
    VehicleTypeType LpenuVEH_Type;
    
    ReadCAN_AppSignal_VEH_Type(&LpenuVEH_Type);
    if(LpenuVEH_Type == VEHTYPE_X02)
    {
        memcpy(&GstrSDW_DisCarPara_Ram,&GstrSDW_DisCarPara_X02,sizeof(SDW_Dis_CarParameterType));
        memcpy(&GstrSDW_CompDisPara_Ram,&GstrSDW_CompDisPara_X02,sizeof(SDW_CompDisance_Type));
    }
    else if(LpenuVEH_Type == VEHTYPE_X03)
    {
        memcpy(&GstrSDW_DisCarPara_Ram,&GstrSDW_DisCarPara_X03,sizeof(SDW_Dis_CarParameterType));
        memcpy(&GstrSDW_CompDisPara_Ram,&GstrSDW_CompDisPara_X03,sizeof(SDW_CompDisance_Type));
    }
    else if(LpenuVEH_Type == VEHTYPE_X04)
    {
        memcpy(&GstrSDW_DisCarPara_Ram,&GstrSDW_DisCarPara_X04,sizeof(SDW_Dis_CarParameterType));
        memcpy(&GstrSDW_CompDisPara_Ram,&GstrSDW_CompDisPara_X04,sizeof(SDW_CompDisance_Type));
    }
    else if(LpenuVEH_Type == VEHTYPE_W02)
    {
        memcpy(&GstrSDW_DisCarPara_Ram,&GstrSDW_DisCarPara_W02,sizeof(SDW_Dis_CarParameterType));
        memcpy(&GstrSDW_CompDisPara_Ram,&GstrSDW_CompDisPara_W02,sizeof(SDW_CompDisance_Type));
    }
    else
    {
        /* 车型错误时，默认使用X02的参数 */
        memcpy(&GstrSDW_DisCarPara_Ram,&GstrSDW_DisCarPara_X02,sizeof(SDW_Dis_CarParameterType));
        memcpy(&GstrSDW_CompDisPara_Ram,&GstrSDW_CompDisPara_X02,sizeof(SDW_CompDisance_Type));
    }
}



