/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * MapRawDataCalib: 
 * Created on: 2023-12-22 20:28
 * Original designer: AntonyFang
 ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "MapRawDataCalib.h"
#include "CAN_AppSignalManage.h"
#include "eel_cfg.h"
#include "types.h"


/******************************************************************************/
/******************************************************************************/
/****************************** Public Definition *****************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************前雷达ECU端定频阈值筛选*****************************************************  */

/* 定频情况下，FLS和FRS Map阈值表，用于筛选有效障碍物回波，定频模式修改为14bit后，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16FLS_FRS_MasStdThresTableForMap[SNS_MAP_DIS_TOTAL_NUM] = 
{
#if Demo_Threshold_Set
    /* 项目竞标，临时修改 */
    10000u,   /* [0,10cm] */
    10000u,   /* (10,20cm] */
    6000u,    /* (20,30cm] */
    2500u,    /* (30,40cm] */
    2500u,    /* (40,50cm] */
#else
    50000u,   /* [0,10cm] */
    35000u,   /* (10,20cm] */
    10000u,   /* (20,30cm] */
    6000u,    /* (30,40cm] */
    3500u,    /* (40,50cm] */
#endif

    2000u,    /* (50,60cm] */
    1800u,    /* (60,70cm] */
    1600u,    /* (70,80cm] */
    1400u,    /* (80,90cm] */
    1200u,    /* (90,100cm] */

    1100u,    /* (100,110cm] */
    1000u,    /* (110,120cm] */
    900u,     /* (120,130cm] */
    800u,     /* (130,140cm] */
    750u,     /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    650u,    /* (170,180cm] */
    620u,    /* (180,190cm] */
    600u,    /* (190,200cm] */

    600u,    /* (200,210cm] */
    600u,    /* (210,220cm] */
    550u,    /* (220,230cm] */
    550u,    /* (230,240cm] */
    500u,    /* (240,250cm] */

    500u,    /* (250,260cm] */
    500u,    /* (260,270cm] */
    500u,    /* (270,280cm] */
    500u,    /* (280,290cm] */
    500u,    /* (290300cm] */

	500u,    /* (300,310cm] */
    500u,    /* (310,320cm] */
    500u,    /* (320,330cm] */
    500u,    /* (330,340cm] */
    500u,    /* (340,350cm] */

    500u,    /* (350,360cm] */
    500u,    /* (360,370cm] */
    500u,    /* (370,380cm] */
    500u,    /* (380,390cm] */
    500u,    /* (390,400cm] */
};

/* 扫频情况下，FLS和FRS Map阈值表，用于筛选有效障碍物回波，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16FLS_FRS_MasChirpThresTableForMap[SNS_MAP_DIS_TOTAL_NUM] = 
{
#if Demo_Threshold_Set
    /* 项目竞标，临时修改 */
    20000u,   /* [0,10cm] */
    20000u,   /* (10,20cm] */
    18000u,   /* (20,30cm] */
    13000u,   /* (30,40cm] */
    4500u,    /* (40,50cm] */
#else
    65535u,   /* [0,10cm] */
    65535u,   /* (10,20cm] */
    22000u,   /* (20,30cm] */
    13000u,   /* (30,40cm] */
    10000u,   /* (40,50cm] */
#endif

    3000u,    /* (50,60cm] */
    2500u,    /* (60,70cm] */
    2500u,    /* (70,80cm] */
    1500u,    /* (80,90cm] */
    1000u,    /* (90,100cm] */

    900u,    /* (100,110cm] */
    900u,    /* (110,120cm] */
    850u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    600u,    /* (170,180cm] */
    550u,    /* (180,190cm] */
    500u,    /* (190,200cm] */

    450u,    /* (200,210cm] */
    400u,    /* (210,220cm] */
    350u,    /* (220,230cm] */
    330u,    /* (230,240cm] */
    300u,    /* (240,250cm] */

    250u,    /* (250,260cm] */
    250u,    /* (260,270cm] */
    250u,    /* (270,280cm] */
    250u,    /* (280,290cm] */
    250u,    /* (290,300cm] */

    250u,    /* (300,310cm] */
    250u,    /* (310,320cm] */
    260u,    /* (320,330cm] */
    250u,    /* (330,340cm] */
    250u,    /* (340,350cm] */

    250u,    /* (350,360cm] */
    300u,    /* (360,370cm] */
    300u,    /* (370,380cm] */
    300u,    /* (380,390cm] */
    300u,    /* (390,400cm] */
};

/* 定频情况下，FL和FR Map阈值表，用于筛选有效障碍物回波，定频模式修改为14bit后，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16FL_FR_MasStdThresTableForMap[SNS_MAP_DIS_TOTAL_NUM] = 
{
#if Demo_Threshold_Set
    /* 项目竞标，临时修改 */
    10000u,   /* [0,10cm] */
    10000u,   /* (10,20cm] */
    6000u,    /* (20,30cm] */
    2500u,    /* (30,40cm] */
    2500u,    /* (40,50cm] */
#else
    50000u,   /* [0,10cm] */
    35000u,   /* (10,20cm] */
    10000u,   /* (20,30cm] */
    6000u,    /* (30,40cm] */
    3500u,    /* (40,50cm] */
#endif

    2000u,    /* (50,60cm] */
    1800u,    /* (60,70cm] */
    1600u,    /* (70,80cm] */
    1400u,    /* (80,90cm] */
    1200u,    /* (90,100cm] */

    1100u,    /* (100,110cm] */
    1000u,    /* (110,120cm] */
    900u,     /* (120,130cm] */
    800u,     /* (130,140cm] */
    750u,     /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    650u,    /* (170,180cm] */
    620u,    /* (180,190cm] */
    600u,    /* (190,200cm] */

    600u,    /* (200,210cm] */
    600u,    /* (210,220cm] */
    550u,    /* (220,230cm] */
    550u,    /* (230,240cm] */
    500u,    /* (240,250cm] */

    500u,    /* (250,260cm] */
    500u,    /* (260,270cm] */
    500u,    /* (270,280cm] */
    500u,    /* (280,290cm] */
    500u,    /* (290300cm] */

	500u,    /* (300,310cm] */
    500u,    /* (310,320cm] */
    500u,    /* (320,330cm] */
    500u,    /* (330,340cm] */
    500u,    /* (340,350cm] */

    500u,    /* (350,360cm] */
    500u,    /* (360,370cm] */
    500u,    /* (370,380cm] */
    500u,    /* (380,390cm] */
    500u,    /* (390,400cm] */
};

/* 扫频情况下，FL和FR Map阈值表，用于筛选有效障碍物回波，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16FL_FR_MasChirpThresTableForMap[SNS_MAP_DIS_TOTAL_NUM] = 
{
#if Demo_Threshold_Set
    /* 项目竞标，临时修改 */
    20000u,   /* [0,10cm] */
    20000u,   /* (10,20cm] */
    18000u,   /* (20,30cm] */
    13000u,   /* (30,40cm] */
    4500u,    /* (40,50cm] */
#else
    65535u,   /* [0,10cm] */
    65535u,   /* (10,20cm] */
    22000u,   /* (20,30cm] */
    13000u,   /* (30,40cm] */
    10000u,   /* (40,50cm] */
#endif
    
    3000u,    /* (50,60cm] */
    2500u,    /* (60,70cm] */
    2500u,    /* (70,80cm] */
    1500u,    /* (80,90cm] */
    1000u,    /* (90,100cm] */

    900u,    /* (100,110cm] */
    900u,    /* (110,120cm] */
    850u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    600u,    /* (170,180cm] */
    550u,    /* (180,190cm] */
    500u,    /* (190,200cm] */

    450u,    /* (200,210cm] */
    400u,    /* (210,220cm] */
    350u,    /* (220,230cm] */
    330u,    /* (230,240cm] */
    300u,    /* (240,250cm] */

    250u,    /* (250,260cm] */
    220u,    /* (260,270cm] */
    200u,    /* (270,280cm] */
    200u,    /* (280,290cm] */
    200u,    /* (290,300cm] */

    180u,    /* (300,310cm] */
    170u,    /* (310,320cm] */
    160u,    /* (320,330cm] */
    150u,    /* (330,340cm] */
    150u,    /* (340,350cm] */

    150u,    /* (350,360cm] */
    300u,    /* (360,370cm] */
    300u,    /* (370,380cm] */
    300u,    /* (380,390cm] */
    300u,    /* (390,400cm] */
};

/* 定频情况下，FML和FMR Map阈值表，用于筛选有效障碍物回波，定频模式修改为14bit后，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16FML_FMR_MasStdThresTableForMap[SNS_MAP_DIS_TOTAL_NUM] = 
{
#if Demo_Threshold_Set
    /* 项目竞标，临时修改 */
    10000u,   /* [0,10cm] */
    10000u,   /* (10,20cm] */
    6000u,    /* (20,30cm] */
    2500u,    /* (30,40cm] */
    2500u,    /* (40,50cm] */
#else
    50000u,   /* [0,10cm] */
    35000u,   /* (10,20cm] */
    10000u,   /* (20,30cm] */
    6000u,    /* (30,40cm] */
    3500u,    /* (40,50cm] */
#endif

    2000u,    /* (50,60cm] */
    1700u,    /* (60,70cm] */
    1500u,    /* (70,80cm] */
    1400u,    /* (80,90cm] */
    1200u,    /* (90,100cm] */

    1100u,    /* (100,110cm] */
    1000u,    /* (110,120cm] */
    900u,     /* (120,130cm] */
    800u,     /* (130,140cm] */
    750u,     /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    650u,    /* (170,180cm] */
    620u,    /* (180,190cm] */
    600u,    /* (190,200cm] */

    600u,    /* (200,210cm] */
    600u,    /* (210,220cm] */
    550u,    /* (220,230cm] */
    550u,    /* (230,240cm] */
    500u,    /* (240,250cm] */

    500u,    /* (250,260cm] */
    500u,    /* (260,270cm] */
    500u,    /* (270,280cm] */
    500u,    /* (280,290cm] */
    500u,    /* (290300cm] */

	500u,    /* (300,310cm] */
    500u,    /* (310,320cm] */
    500u,    /* (320,330cm] */
    500u,    /* (330,340cm] */
    500u,    /* (340,350cm] */

    500u,    /* (350,360cm] */
    500u,    /* (360,370cm] */
    500u,    /* (370,380cm] */
    500u,    /* (380,390cm] */
    500u,    /* (390,400cm] */
};

/* 扫频情况下，FML和FMR Map阈值表，用于筛选有效障碍物回波，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16FML_FMR_MasChirpThresTableForMap[SNS_MAP_DIS_TOTAL_NUM] = 
{
#if Demo_Threshold_Set
    /* 项目竞标，临时修改 */
    20000u,   /* [0,10cm] */
    20000u,   /* (10,20cm] */
    18000u,   /* (20,30cm] */
    13000u,   /* (30,40cm] */
    4500u,    /* (40,50cm] */
#else
    65535u,   /* [0,10cm] */
    65535u,   /* (10,20cm] */
    22000u,   /* (20,30cm] */
    13000u,   /* (30,40cm] */
    10000u,   /* (40,50cm] */
#endif

    3000u,    /* (50,60cm] */
    2500u,    /* (60,70cm] */
    2500u,    /* (70,80cm] */
    1500u,    /* (80,90cm] */
    1000u,    /* (90,100cm] */

    900u,    /* (100,110cm] */
    900u,    /* (110,120cm] */
    850u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    600u,    /* (170,180cm] */
    550u,    /* (180,190cm] */
    500u,    /* (190,200cm] */

    450u,    /* (200,210cm] */
    400u,    /* (210,220cm] */
    350u,    /* (220,230cm] */
    330u,    /* (230,240cm] */
    300u,    /* (240,250cm] */

    250u,    /* (250,260cm] */
    220u,    /* (260,270cm] */
    200u,    /* (270,280cm] */
    200u,    /* (280,290cm] */
    200u,    /* (290,300cm] */

    180u,    /* (300,310cm] */
    170u,    /* (310,320cm] */
    160u,    /* (320,330cm] */
    150u,    /* (330,340cm] */
    150u,    /* (340,350cm] */

    150u,    /* (350,360cm] */
    300u,    /* (360,370cm] */
    300u,    /* (370,380cm] */
    300u,    /* (380,390cm] */
    300u,    /* (390,400cm] */
};

/* 定频情况下，RLS和RRS Map阈值表，用于筛选有效障碍物回波，定频模式修改为14bit后，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16RLS_RRS_MasStdThresTableForMap[SNS_MAP_DIS_TOTAL_NUM] = 
{
#if Demo_Threshold_Set
    /* 项目竞标，临时修改 */
    10000u,   /* [0,10cm] */
    10000u,   /* (10,20cm] */
    6000u,    /* (20,30cm] */
    2500u,    /* (30,40cm] */
    2500u,    /* (40,50cm] */
#else
    50000u,   /* [0,10cm] */
    35000u,   /* (10,20cm] */
    10000u,   /* (20,30cm] */
    6000u,    /* (30,40cm] */
    3500u,    /* (40,50cm] */
#endif

    2000u,    /* (50,60cm] */
    1800u,    /* (60,70cm] */
    1600u,    /* (70,80cm] */
    1400u,    /* (80,90cm] */
    1200u,    /* (90,100cm] */

    1100u,    /* (100,110cm] */
    1000u,    /* (110,120cm] */
    900u,     /* (120,130cm] */
    800u,     /* (130,140cm] */
    750u,     /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    650u,    /* (170,180cm] */
    620u,    /* (180,190cm] */
    600u,    /* (190,200cm] */

    600u,    /* (200,210cm] */
    600u,    /* (210,220cm] */
    550u,    /* (220,230cm] */
    550u,    /* (230,240cm] */
    500u,    /* (240,250cm] */

    500u,    /* (250,260cm] */
    500u,    /* (260,270cm] */
    500u,    /* (270,280cm] */
    500u,    /* (280,290cm] */
    500u,    /* (290300cm] */

	500u,    /* (300,310cm] */
    500u,    /* (310,320cm] */
    500u,    /* (320,330cm] */
    500u,    /* (330,340cm] */
    500u,    /* (340,350cm] */

    500u,    /* (350,360cm] */
    500u,    /* (360,370cm] */
    500u,    /* (370,380cm] */
    500u,    /* (380,390cm] */
    500u,    /* (390,400cm] */
};

/* 扫频情况下，RLS和RRS Map阈值表，用于筛选有效障碍物回波，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16RLS_RRS_MasChirpThresTableForMap[SNS_MAP_DIS_TOTAL_NUM] = 
{
#if Demo_Threshold_Set
    /* 项目竞标，临时修改 */
    20000u,   /* [0,10cm] */
    20000u,   /* (10,20cm] */
    18000u,   /* (20,30cm] */
    13000u,   /* (30,40cm] */
    4500u,    /* (40,50cm] */
#else
    65535u,   /* [0,10cm] */
    65535u,   /* (10,20cm] */
    22000u,   /* (20,30cm] */
    13000u,   /* (30,40cm] */
    10000u,   /* (40,50cm] */
#endif

    3000u,    /* (50,60cm] */
    2500u,    /* (60,70cm] */
    2500u,    /* (70,80cm] */
    1500u,    /* (80,90cm] */
    1000u,    /* (90,100cm] */

    900u,    /* (100,110cm] */
    900u,    /* (110,120cm] */
    850u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    600u,    /* (170,180cm] */
    550u,    /* (180,190cm] */
    500u,    /* (190,200cm] */

    450u,    /* (200,210cm] */
    400u,    /* (210,220cm] */
    350u,    /* (220,230cm] */
    330u,    /* (230,240cm] */
    300u,    /* (240,250cm] */

    250u,    /* (250,260cm] */
    220u,    /* (260,270cm] */
    200u,    /* (270,280cm] */
    200u,    /* (280,290cm] */
    200u,    /* (290,300cm] */

    180u,    /* (300,310cm] */
    170u,    /* (310,320cm] */
    160u,    /* (320,330cm] */
    150u,    /* (330,340cm] */
    150u,    /* (340,350cm] */

    150u,    /* (350,360cm] */
    300u,    /* (360,370cm] */
    300u,    /* (370,380cm] */
    300u,    /* (380,390cm] */
    300u,    /* (390,400cm] */
};

/* 定频情况下，RL和RR Map阈值表，用于筛选有效障碍物回波，定频模式修改为14bit后，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16RL_RR_MasStdThresTableForMap[SNS_MAP_DIS_TOTAL_NUM] = 
{
#if Demo_Threshold_Set
    /* 项目竞标，临时修改 */
    10000u,   /* [0,10cm] */
    10000u,   /* (10,20cm] */
    6000u,    /* (20,30cm] */
    2500u,    /* (30,40cm] */
    2500u,    /* (40,50cm] */
#else
    50000u,   /* [0,10cm] */
    35000u,   /* (10,20cm] */
    10000u,   /* (20,30cm] */
    6000u,    /* (30,40cm] */
    3500u,    /* (40,50cm] */
#endif

    2000u,    /* (50,60cm] */
    1800u,    /* (60,70cm] */
    1600u,    /* (70,80cm] */
    1400u,    /* (80,90cm] */
    1200u,    /* (90,100cm] */

    1100u,    /* (100,110cm] */
    1000u,    /* (110,120cm] */
    900u,     /* (120,130cm] */
    800u,     /* (130,140cm] */
    750u,     /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    650u,    /* (170,180cm] */
    620u,    /* (180,190cm] */
    600u,    /* (190,200cm] */

    600u,    /* (200,210cm] */
    600u,    /* (210,220cm] */
    550u,    /* (220,230cm] */
    550u,    /* (230,240cm] */
    500u,    /* (240,250cm] */

    500u,    /* (250,260cm] */
    500u,    /* (260,270cm] */
    500u,    /* (270,280cm] */
    500u,    /* (280,290cm] */
    500u,    /* (290300cm] */

	500u,    /* (300,310cm] */
    500u,    /* (310,320cm] */
    500u,    /* (320,330cm] */
    500u,    /* (330,340cm] */
    500u,    /* (340,350cm] */

    500u,    /* (350,360cm] */
    500u,    /* (360,370cm] */
    500u,    /* (370,380cm] */
    500u,    /* (380,390cm] */
    500u,    /* (390,400cm] */
};

/* 扫频情况下，RL和RR Map阈值表，用于筛选有效障碍物回波，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16RL_RR_MasChirpThresTableForMap[SNS_MAP_DIS_TOTAL_NUM] = 
{
#if Demo_Threshold_Set
    /* 项目竞标，临时修改 */
    20000u,   /* [0,10cm] */
    20000u,   /* (10,20cm] */
    18000u,   /* (20,30cm] */
    13000u,   /* (30,40cm] */
    4500u,    /* (40,50cm] */
#else
    65535u,   /* [0,10cm] */
    65535u,   /* (10,20cm] */
    22000u,   /* (20,30cm] */
    13000u,   /* (30,40cm] */
    10000u,   /* (40,50cm] */
#endif


    3000u,    /* (50,60cm] */
    2500u,    /* (60,70cm] */
    2500u,    /* (70,80cm] */
    1500u,    /* (80,90cm] */
    1000u,    /* (90,100cm] */

    900u,    /* (100,110cm] */
    900u,    /* (110,120cm] */
    850u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    600u,    /* (170,180cm] */
    550u,    /* (180,190cm] */
    500u,    /* (190,200cm] */

    450u,    /* (200,210cm] */
    400u,    /* (210,220cm] */
    350u,    /* (220,230cm] */
    330u,    /* (230,240cm] */
    300u,    /* (240,250cm] */

    250u,    /* (250,260cm] */
    220u,    /* (260,270cm] */
    200u,    /* (270,280cm] */
    200u,    /* (280,290cm] */
    200u,    /* (290,300cm] */

    180u,    /* (300,310cm] */
    170u,    /* (310,320cm] */
    160u,    /* (320,330cm] */
    150u,    /* (330,340cm] */
    150u,    /* (340,350cm] */

    150u,    /* (350,360cm] */
    300u,    /* (360,370cm] */
    300u,    /* (370,380cm] */
    300u,    /* (380,390cm] */
    300u,    /* (390,400cm] */
};

/* 定频情况下，RML和RMR Map阈值表，用于筛选有效障碍物回波，定频模式修改为14bit后，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16RML_RMR_MasStdThresTableForMap[SNS_MAP_DIS_TOTAL_NUM] = 
{
#if Demo_Threshold_Set
    /* 项目竞标，临时修改 */
    10000u,   /* [0,10cm] */
    10000u,   /* (10,20cm] */
    6000u,    /* (20,30cm] */
    2500u,    /* (30,40cm] */
    2500u,    /* (40,50cm] */
#else
    50000u,   /* [0,10cm] */
    35000u,   /* (10,20cm] */
    10000u,   /* (20,30cm] */
    6000u,    /* (30,40cm] */
    3500u,    /* (40,50cm] */
#endif

    2000u,    /* (50,60cm] */
    1800u,    /* (60,70cm] */
    1600u,    /* (70,80cm] */
    1400u,    /* (80,90cm] */
    1200u,    /* (90,100cm] */

    1100u,    /* (100,110cm] */
    1000u,    /* (110,120cm] */
    900u,     /* (120,130cm] */
    800u,     /* (130,140cm] */
    750u,     /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    650u,    /* (170,180cm] */
    620u,    /* (180,190cm] */
    600u,    /* (190,200cm] */

    600u,    /* (200,210cm] */
    600u,    /* (210,220cm] */
    550u,    /* (220,230cm] */
    550u,    /* (230,240cm] */
    500u,    /* (240,250cm] */

    500u,    /* (250,260cm] */
    500u,    /* (260,270cm] */
    500u,    /* (270,280cm] */
    500u,    /* (280,290cm] */
    500u,    /* (290300cm] */

	500u,    /* (300,310cm] */
    500u,    /* (310,320cm] */
    500u,    /* (320,330cm] */
    500u,    /* (330,340cm] */
    500u,    /* (340,350cm] */

    500u,    /* (350,360cm] */
    500u,    /* (360,370cm] */
    500u,    /* (370,380cm] */
    500u,    /* (380,390cm] */
    500u,    /* (390,400cm] */
};

/* 扫频情况下，RML和RMR Map阈值表，用于筛选有效障碍物回波，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16RML_RMR_MasChirpThresTableForMap[SNS_MAP_DIS_TOTAL_NUM] = 
{
#if Demo_Threshold_Set
    /* 项目竞标，临时修改 */
    20000u,   /* [0,10cm] */
    20000u,   /* (10,20cm] */
    18000u,   /* (20,30cm] */
    13000u,   /* (30,40cm] */
    4500u,    /* (40,50cm] */
#else
    65535u,   /* [0,10cm] */
    65535u,   /* (10,20cm] */
    22000u,   /* (20,30cm] */
    13000u,   /* (30,40cm] */
    10000u,   /* (40,50cm] */
#endif

    3000u,    /* (50,60cm] */
    2500u,    /* (60,70cm] */
    2500u,    /* (70,80cm] */
    1500u,    /* (80,90cm] */
    1000u,    /* (90,100cm] */
    
    900u,    /* (100,110cm] */
    900u,    /* (110,120cm] */
    850u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    600u,    /* (170,180cm] */
    550u,    /* (180,190cm] */
    500u,    /* (190,200cm] */

    450u,    /* (200,210cm] */
    400u,    /* (210,220cm] */
    350u,    /* (220,230cm] */
    330u,    /* (230,240cm] */
    300u,    /* (240,250cm] */

    250u,    /* (250,260cm] */
    220u,    /* (260,270cm] */
    200u,    /* (270,280cm] */
    200u,    /* (280,290cm] */
    200u,    /* (290,300cm] */

    180u,    /* (300,310cm] */
    170u,    /* (310,320cm] */
    160u,    /* (320,330cm] */
    150u,    /* (330,340cm] */
    150u,    /* (340,350cm] */

    150u,    /* (350,360cm] */
    300u,    /* (360,370cm] */
    300u,    /* (370,380cm] */
    300u,    /* (380,390cm] */
    300u,    /* (390,400cm] */
};

/******************************************************************侦听相关频阈值筛选*****************************************************  */

/* 定频情况下，FLS和FRS Map侦听阈值表，用于筛选有效障碍物回波，定频模式修改为14bit后，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16FLS_FRS_LisStdThresTableForMap[SNS_MAP_DIS_TOTAL_NUM] = 
{
    2000u,   /* [0,10cm] */
    2000u,   /* (10,20cm] */
    1800u,   /* (20,30cm] */
    1800u,   /* (30,40cm] */
    1800u,   /* (40,50cm] */

    1200u,    /* (50,60cm] */
    1100u,    /* (60,70cm] */
    1000u,    /* (70,80cm] */
    1000u,    /* (80,90cm] */
    1000u,    /* (90,100cm] */

    1000u,    /* (100,110cm] */
    900u,     /* (110,120cm] */
    800u,     /* (120,130cm] */
    700u,     /* (130,140cm] */
    650u,     /* (140,150cm] */

    600u,    /* (150,160cm] */
    550u,    /* (160,170cm] */
    550u,    /* (170,180cm] */
    500u,    /* (180,190cm] */
    500u,    /* (190,200cm] */

    450u,    /* (200,210cm] */
    450u,    /* (210,220cm] */
    550u,    /* (220,230cm] */
    550u,    /* (230,240cm] */
    500u,    /* (240,250cm] */

    500u,    /* (250,260cm] */
    500u,    /* (260,270cm] */
    500u,    /* (270,280cm] */
    500u,    /* (280,290cm] */
    500u,    /* (290300cm] */

	500u,    /* (300,310cm] */
    500u,    /* (310,320cm] */
    500u,    /* (320,330cm] */
    500u,    /* (330,340cm] */
    500u,    /* (340,350cm] */

    500u,    /* (350,360cm] */
    500u,    /* (360,370cm] */
    500u,    /* (370,380cm] */
    500u,    /* (380,390cm] */
    500u,    /* (390,400cm] */
};

/* 扫频情况下，FLS和FRS Map侦听阈值表，用于筛选有效障碍物回波，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16FLS_FRS_LisChirpThresTableForMap[SNS_MAP_DIS_TOTAL_NUM] = 
{
    3000u,   /* [0,10cm] */
    3000u,   /* (10,20cm] */
    3000u,   /* (20,30cm] */
    3000u,   /* (30,40cm] */
    2000u,   /* (40,50cm] */

    2000u,   /* (50,60cm] */
    1400u,   /* (60,70cm] */
    1200u,   /* (70,80cm] */
    1100u,   /* (80,90cm] */
    600u,    /* (90,100cm] */

    500u,    /* (100,110cm] */
    500u,    /* (110,120cm] */
    450u,    /* (120,130cm] */
    400u,    /* (130,140cm] */
    350u,    /* (140,150cm] */

    350u,    /* (150,160cm] */
    350u,    /* (160,170cm] */
    350u,    /* (170,180cm] */
    300u,    /* (180,190cm] */
    300u,    /* (190,200cm] */

    250u,    /* (200,210cm] */
    200u,    /* (210,220cm] */
    200u,    /* (220,230cm] */
    200u,    /* (230,240cm] */
    200u,    /* (240,250cm] */

    200u,    /* (250,260cm] */
    200u,    /* (260,270cm] */
    200u,    /* (270,280cm] */
    200u,    /* (280,290cm] */
    200u,    /* (290,300cm] */

    180u,    /* (300,310cm] */
    170u,    /* (310,320cm] */
    160u,    /* (320,330cm] */
    150u,    /* (330,340cm] */
    150u,    /* (340,350cm] */

    150u,    /* (350,360cm] */
    300u,    /* (360,370cm] */
    300u,    /* (370,380cm] */
    300u,    /* (380,390cm] */
    300u,    /* (390,400cm] */
};


/* 定频情况下，FL和FR Map侦听阈值表，用于筛选有效障碍物回波，定频模式修改为14bit后，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16FL_FR_LisStdThresTableForMap[SNS_MAP_DIS_TOTAL_NUM] = 
{
    2000u,   /* [0,10cm] */
    2000u,   /* (10,20cm] */
    1800u,   /* (20,30cm] */
    1800u,   /* (30,40cm] */
    1800u,   /* (40,50cm] */

    1200u,    /* (50,60cm] */
    1100u,    /* (60,70cm] */
    1000u,    /* (70,80cm] */
    1000u,    /* (80,90cm] */
    1000u,    /* (90,100cm] */

    1000u,    /* (100,110cm] */
    900u,     /* (110,120cm] */
    800u,     /* (120,130cm] */
    700u,     /* (130,140cm] */
    650u,     /* (140,150cm] */

    600u,    /* (150,160cm] */
    550u,    /* (160,170cm] */
    550u,    /* (170,180cm] */
    500u,    /* (180,190cm] */
    500u,    /* (190,200cm] */

    450u,    /* (200,210cm] */
    450u,    /* (210,220cm] */
    550u,    /* (220,230cm] */
    550u,    /* (230,240cm] */
    500u,    /* (240,250cm] */

    500u,    /* (250,260cm] */
    500u,    /* (260,270cm] */
    500u,    /* (270,280cm] */
    500u,    /* (280,290cm] */
    500u,    /* (290300cm] */

	500u,    /* (300,310cm] */
    500u,    /* (310,320cm] */
    500u,    /* (320,330cm] */
    500u,    /* (330,340cm] */
    500u,    /* (340,350cm] */

    500u,    /* (350,360cm] */
    500u,    /* (360,370cm] */
    500u,    /* (370,380cm] */
    500u,    /* (380,390cm] */
    500u,    /* (390,400cm] */
};

/* 扫频情况下，FL和FR Map侦听阈值表，用于筛选有效障碍物回波，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16FL_FR_LisChirpThresTableForMap[SNS_MAP_DIS_TOTAL_NUM] = 
{
    3000u,   /* [0,10cm] */
    3000u,   /* (10,20cm] */
    3000u,   /* (20,30cm] */
    3000u,   /* (30,40cm] */
    2000u,   /* (40,50cm] */

    1200u,   /* (50,60cm] */
    1200u,   /* (60,70cm] */
    1200u,   /* (70,80cm] */
    1100u,   /* (80,90cm] */
    600u,    /* (90,100cm] */

    500u,    /* (100,110cm] */
    500u,    /* (110,120cm] */
    450u,    /* (120,130cm] */
    400u,    /* (130,140cm] */
    350u,    /* (140,150cm] */

    350u,    /* (150,160cm] */
    350u,    /* (160,170cm] */
    350u,    /* (170,180cm] */
    300u,    /* (180,190cm] */
    300u,    /* (190,200cm] */

    250u,    /* (200,210cm] */
    200u,    /* (210,220cm] */
    200u,    /* (220,230cm] */
    200u,    /* (230,240cm] */
    200u,    /* (240,250cm] */

    200u,    /* (250,260cm] */
    200u,    /* (260,270cm] */
    200u,    /* (270,280cm] */
    200u,    /* (280,290cm] */
    200u,    /* (290,300cm] */

    180u,    /* (300,310cm] */
    170u,    /* (310,320cm] */
    160u,    /* (320,330cm] */
    150u,    /* (330,340cm] */
    150u,    /* (340,350cm] */

    150u,    /* (350,360cm] */
    300u,    /* (360,370cm] */
    300u,    /* (370,380cm] */
    300u,    /* (380,390cm] */
    300u,    /* (390,400cm] */
};

/* 定频情况下，FML和FMR Map侦听阈值表，用于筛选有效障碍物回波，定频模式修改为14bit后，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16FML_FMR_LisStdThresTableForMap[SNS_MAP_DIS_TOTAL_NUM] = 
{
    2000u,   /* [0,10cm] */
    2000u,   /* (10,20cm] */
    1800u,   /* (20,30cm] */
    1800u,   /* (30,40cm] */
    1800u,   /* (40,50cm] */

    1200u,    /* (50,60cm] */
    1100u,    /* (60,70cm] */
    1000u,    /* (70,80cm] */
    1000u,    /* (80,90cm] */
    1000u,    /* (90,100cm] */

    1000u,    /* (100,110cm] */
    900u,     /* (110,120cm] */
    800u,     /* (120,130cm] */
    700u,     /* (130,140cm] */
    650u,     /* (140,150cm] */

    600u,    /* (150,160cm] */
    550u,    /* (160,170cm] */
    550u,    /* (170,180cm] */
    500u,    /* (180,190cm] */
    500u,    /* (190,200cm] */

    450u,    /* (200,210cm] */
    450u,    /* (210,220cm] */
    550u,    /* (220,230cm] */
    550u,    /* (230,240cm] */
    500u,    /* (240,250cm] */

    500u,    /* (250,260cm] */
    500u,    /* (260,270cm] */
    500u,    /* (270,280cm] */
    500u,    /* (280,290cm] */
    500u,    /* (290300cm] */

	500u,    /* (300,310cm] */
    500u,    /* (310,320cm] */
    500u,    /* (320,330cm] */
    500u,    /* (330,340cm] */
    500u,    /* (340,350cm] */

    500u,    /* (350,360cm] */
    500u,    /* (360,370cm] */
    500u,    /* (370,380cm] */
    500u,    /* (380,390cm] */
    500u,    /* (390,400cm] */
};

/* 扫频情况下，FML和F,R Map侦听阈值表，用于筛选有效障碍物回波，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16FML_FMR_LisChirpThresTableForMap[SNS_MAP_DIS_TOTAL_NUM] = 
{
    3000u,   /* [0,10cm] */
    3000u,   /* (10,20cm] */
    3000u,   /* (20,30cm] */
    3000u,   /* (30,40cm] */
    2000u,   /* (40,50cm] */

    1200u,   /* (50,60cm] */
    1200u,   /* (60,70cm] */
    1200u,   /* (70,80cm] */
    1100u,   /* (80,90cm] */
    600u,    /* (90,100cm] */

    500u,    /* (100,110cm] */
    500u,    /* (110,120cm] */
    450u,    /* (120,130cm] */
    400u,    /* (130,140cm] */
    350u,    /* (140,150cm] */

    350u,    /* (150,160cm] */
    350u,    /* (160,170cm] */
    350u,    /* (170,180cm] */
    300u,    /* (180,190cm] */
    300u,    /* (190,200cm] */

    250u,    /* (200,210cm] */
    200u,    /* (210,220cm] */
    200u,    /* (220,230cm] */
    200u,    /* (230,240cm] */
    200u,    /* (240,250cm] */

    200u,    /* (250,260cm] */
    200u,    /* (260,270cm] */
    200u,    /* (270,280cm] */
    200u,    /* (280,290cm] */
    200u,    /* (290,300cm] */

    180u,    /* (300,310cm] */
    170u,    /* (310,320cm] */
    160u,    /* (320,330cm] */
    150u,    /* (330,340cm] */
    150u,    /* (340,350cm] */

    150u,    /* (350,360cm] */
    300u,    /* (360,370cm] */
    300u,    /* (370,380cm] */
    300u,    /* (380,390cm] */
    300u,    /* (390,400cm] */
};


/* 定频情况下，RLS和RRS Map侦听阈值表，用于筛选有效障碍物回波，定频模式修改为14bit后，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16RLS_RRS_LisStdThresTableForMap[SNS_MAP_DIS_TOTAL_NUM] = 
{
    2000u,   /* [0,10cm] */
    2000u,   /* (10,20cm] */
    1800u,   /* (20,30cm] */
    1800u,   /* (30,40cm] */
    1800u,   /* (40,50cm] */

    1200u,    /* (50,60cm] */
    1100u,    /* (60,70cm] */
    1000u,    /* (70,80cm] */
    1000u,    /* (80,90cm] */
    1000u,    /* (90,100cm] */

    1000u,    /* (100,110cm] */
    900u,     /* (110,120cm] */
    800u,     /* (120,130cm] */
    700u,     /* (130,140cm] */
    650u,     /* (140,150cm] */

    600u,    /* (150,160cm] */
    550u,    /* (160,170cm] */
    550u,    /* (170,180cm] */
    500u,    /* (180,190cm] */
    500u,    /* (190,200cm] */

    450u,    /* (200,210cm] */
    450u,    /* (210,220cm] */
    550u,    /* (220,230cm] */
    550u,    /* (230,240cm] */
    500u,    /* (240,250cm] */

    500u,    /* (250,260cm] */
    500u,    /* (260,270cm] */
    500u,    /* (270,280cm] */
    500u,    /* (280,290cm] */
    500u,    /* (290300cm] */

	500u,    /* (300,310cm] */
    500u,    /* (310,320cm] */
    500u,    /* (320,330cm] */
    500u,    /* (330,340cm] */
    500u,    /* (340,350cm] */

    500u,    /* (350,360cm] */
    500u,    /* (360,370cm] */
    500u,    /* (370,380cm] */
    500u,    /* (380,390cm] */
    500u,    /* (390,400cm] */
};

/* 扫频情况下，RLS和RRS Map侦听阈值表，用于筛选有效障碍物回波，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16RLS_RRS_LisChirpThresTableForMap[SNS_MAP_DIS_TOTAL_NUM] = 
{
    3000u,   /* [0,10cm] */
    3000u,   /* (10,20cm] */
    3000u,   /* (20,30cm] */
    3000u,   /* (30,40cm] */
    2000u,   /* (40,50cm] */

    2000u,   /* (50,60cm] */
    1400u,   /* (60,70cm] */
    1200u,   /* (70,80cm] */
    1100u,   /* (80,90cm] */
    600u,    /* (90,100cm] */

    500u,    /* (100,110cm] */
    500u,    /* (110,120cm] */
    450u,    /* (120,130cm] */
    400u,    /* (130,140cm] */
    350u,    /* (140,150cm] */

    350u,    /* (150,160cm] */
    350u,    /* (160,170cm] */
    350u,    /* (170,180cm] */
    300u,    /* (180,190cm] */
    300u,    /* (190,200cm] */

    250u,    /* (200,210cm] */
    200u,    /* (210,220cm] */
    200u,    /* (220,230cm] */
    200u,    /* (230,240cm] */
    200u,    /* (240,250cm] */

    200u,    /* (250,260cm] */
    200u,    /* (260,270cm] */
    200u,    /* (270,280cm] */
    200u,    /* (280,290cm] */
    200u,    /* (290,300cm] */

    180u,    /* (300,310cm] */
    170u,    /* (310,320cm] */
    160u,    /* (320,330cm] */
    150u,    /* (330,340cm] */
    150u,    /* (340,350cm] */

    150u,    /* (350,360cm] */
    300u,    /* (360,370cm] */
    300u,    /* (370,380cm] */
    300u,    /* (380,390cm] */
    300u,    /* (390,400cm] */
};


/* 定频情况下，RL和RR Map侦听阈值表，用于筛选有效障碍物回波，定频模式修改为14bit后，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16RL_RR_LisStdThresTableForMap[SNS_MAP_DIS_TOTAL_NUM] = 
{
    2000u,   /* [0,10cm] */
    2000u,   /* (10,20cm] */
    1800u,   /* (20,30cm] */
    1800u,   /* (30,40cm] */
    1800u,   /* (40,50cm] */

    1200u,    /* (50,60cm] */
    1100u,    /* (60,70cm] */
    1000u,    /* (70,80cm] */
    1000u,    /* (80,90cm] */
    1000u,    /* (90,100cm] */

    1000u,    /* (100,110cm] */
    900u,     /* (110,120cm] */
    800u,     /* (120,130cm] */
    700u,     /* (130,140cm] */
    650u,     /* (140,150cm] */

    600u,    /* (150,160cm] */
    550u,    /* (160,170cm] */
    550u,    /* (170,180cm] */
    500u,    /* (180,190cm] */
    500u,    /* (190,200cm] */

    450u,    /* (200,210cm] */
    450u,    /* (210,220cm] */
    550u,    /* (220,230cm] */
    550u,    /* (230,240cm] */
    500u,    /* (240,250cm] */

    500u,    /* (250,260cm] */
    500u,    /* (260,270cm] */
    500u,    /* (270,280cm] */
    500u,    /* (280,290cm] */
    500u,    /* (290300cm] */

	500u,    /* (300,310cm] */
    500u,    /* (310,320cm] */
    500u,    /* (320,330cm] */
    500u,    /* (330,340cm] */
    500u,    /* (340,350cm] */

    500u,    /* (350,360cm] */
    500u,    /* (360,370cm] */
    500u,    /* (370,380cm] */
    500u,    /* (380,390cm] */
    500u,    /* (390,400cm] */
};

/* 扫频情况下，RL和RR Map侦听阈值表，用于筛选有效障碍物回波，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16RL_RR_LisChirpThresTableForMap[SNS_MAP_DIS_TOTAL_NUM] = 
{
    3000u,   /* [0,10cm] */
    3000u,   /* (10,20cm] */
    3000u,   /* (20,30cm] */
    3000u,   /* (30,40cm] */
    2000u,   /* (40,50cm] */

    1200u,   /* (50,60cm] */
    1200u,   /* (60,70cm] */
    1000u,   /* (70,80cm] */
    1000u,   /* (80,90cm] */
    600u,    /* (90,100cm] */

    500u,    /* (100,110cm] */
    500u,    /* (110,120cm] */
    450u,    /* (120,130cm] */
    400u,    /* (130,140cm] */
    350u,    /* (140,150cm] */

    350u,    /* (150,160cm] */
    350u,    /* (160,170cm] */
    350u,    /* (170,180cm] */
    300u,    /* (180,190cm] */
    300u,    /* (190,200cm] */

    250u,    /* (200,210cm] */
    200u,    /* (210,220cm] */
    200u,    /* (220,230cm] */
    200u,    /* (230,240cm] */
    200u,    /* (240,250cm] */

    200u,    /* (250,260cm] */
    200u,    /* (260,270cm] */
    200u,    /* (270,280cm] */
    200u,    /* (280,290cm] */
    200u,    /* (290,300cm] */

    180u,    /* (300,310cm] */
    170u,    /* (310,320cm] */
    160u,    /* (320,330cm] */
    150u,    /* (330,340cm] */
    150u,    /* (340,350cm] */

    150u,    /* (350,360cm] */
    300u,    /* (360,370cm] */
    300u,    /* (370,380cm] */
    300u,    /* (380,390cm] */
    300u,    /* (390,400cm] */
};

/* 定频情况下，RML和RMR Map侦听阈值表，用于筛选有效障碍物回波，定频模式修改为14bit后，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16RML_RMR_LisStdThresTableForMap[SNS_MAP_DIS_TOTAL_NUM] = 
{
    2000u,   /* [0,10cm] */
    2000u,   /* (10,20cm] */
    1800u,   /* (20,30cm] */
    1800u,   /* (30,40cm] */
    1800u,   /* (40,50cm] */

    1200u,    /* (50,60cm] */
    1100u,    /* (60,70cm] */
    1000u,    /* (70,80cm] */
    1000u,    /* (80,90cm] */
    1000u,    /* (90,100cm] */

    1000u,    /* (100,110cm] */
    900u,     /* (110,120cm] */
    800u,     /* (120,130cm] */
    700u,     /* (130,140cm] */
    650u,     /* (140,150cm] */

    600u,    /* (150,160cm] */
    550u,    /* (160,170cm] */
    550u,    /* (170,180cm] */
    500u,    /* (180,190cm] */
    500u,    /* (190,200cm] */

    450u,    /* (200,210cm] */
    450u,    /* (210,220cm] */
    550u,    /* (220,230cm] */
    550u,    /* (230,240cm] */
    500u,    /* (240,250cm] */

    500u,    /* (250,260cm] */
    500u,    /* (260,270cm] */
    500u,    /* (270,280cm] */
    500u,    /* (280,290cm] */
    500u,    /* (290300cm] */

	500u,    /* (300,310cm] */
    500u,    /* (310,320cm] */
    500u,    /* (320,330cm] */
    500u,    /* (330,340cm] */
    500u,    /* (340,350cm] */

    500u,    /* (350,360cm] */
    500u,    /* (360,370cm] */
    500u,    /* (370,380cm] */
    500u,    /* (380,390cm] */
    500u,    /* (390,400cm] */
};

/* 扫频情况下，RML和RMR Map侦听阈值表，用于筛选有效障碍物回波，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16RML_RMR_LisChirpThresTableForMap[SNS_MAP_DIS_TOTAL_NUM] = 
{
    3000u,   /* [0,10cm] */
    3000u,   /* (10,20cm] */
    3000u,   /* (20,30cm] */
    3000u,   /* (30,40cm] */
    2000u,   /* (40,50cm] */

    1200u,   /* (50,60cm] */
    1200u,   /* (60,70cm] */
    1200u,   /* (70,80cm] */
    1100u,   /* (80,90cm] */
    600u,    /* (90,100cm] */

    500u,    /* (100,110cm] */
    500u,    /* (110,120cm] */
    450u,    /* (120,130cm] */
    400u,    /* (130,140cm] */
    350u,    /* (140,150cm] */

    350u,    /* (150,160cm] */
    350u,    /* (160,170cm] */
    350u,    /* (170,180cm] */
    300u,    /* (180,190cm] */
    300u,    /* (190,200cm] */

    250u,    /* (200,210cm] */
    200u,    /* (210,220cm] */
    200u,    /* (220,230cm] */
    200u,    /* (230,240cm] */
    200u,    /* (240,250cm] */

    200u,    /* (250,260cm] */
    200u,    /* (260,270cm] */
    200u,    /* (270,280cm] */
    200u,    /* (280,290cm] */
    200u,    /* (290,300cm] */

    180u,    /* (300,310cm] */
    170u,    /* (310,320cm] */
    160u,    /* (320,330cm] */
    150u,    /* (330,340cm] */
    150u,    /* (340,350cm] */

    150u,    /* (350,360cm] */
    300u,    /* (360,370cm] */
    300u,    /* (370,380cm] */
    300u,    /* (380,390cm] */
    300u,    /* (390,400cm] */
};


/* 定频模式下障碍物回波高度判断障碍物类型的阈值 */
const SnsMapCalibHeightType GStrMapObjJudgeStdThresTable[SNS_MAP_DIS_HIGH_TOTAL_NUM] = 
{
    /* 需要根据路砍等低矮障碍物的高度过滤10~80cm的PVC阈值 */
    [SNS_MAP_DIS_HIGH_10cm] =
    {
        .u16PVC_PileHeight = 7900,
        .u16BigWallHeight = 60000,
        .u16SecondWallHeight = 12846,
        .u16CurbHeight = 4000,
    },
    [SNS_MAP_DIS_HIGH_20cm] =
    {
        .u16PVC_PileHeight = 7900,
        .u16BigWallHeight = 60000,
        .u16SecondWallHeight = 12846,
        .u16CurbHeight = 4000,
    },
    [SNS_MAP_DIS_HIGH_30cm] =
    {
        .u16PVC_PileHeight = 7900,
        .u16BigWallHeight = 60000,
        .u16SecondWallHeight = 12846,
        .u16CurbHeight = 4000,
    },
    [SNS_MAP_DIS_HIGH_40cm] =
    {
        .u16PVC_PileHeight = 7900,
        .u16BigWallHeight = 60000,
        .u16SecondWallHeight = 12846,
        .u16CurbHeight = 4000,
    },
    [SNS_MAP_DIS_HIGH_50cm] =
    {
        .u16PVC_PileHeight = 7900,
        .u16BigWallHeight = 60000,
        .u16SecondWallHeight = 12846,
        .u16CurbHeight = 4000,
    },
    [SNS_MAP_DIS_HIGH_60cm] =
    {
        .u16PVC_PileHeight = 7151,
        .u16BigWallHeight = 54156,
        .u16SecondWallHeight = 11623,
        .u16CurbHeight = 3723,
    },
    [SNS_MAP_DIS_HIGH_70cm] =
    {
        .u16PVC_PileHeight = 6470,
        .u16BigWallHeight = 49003,
        .u16SecondWallHeight = 10517,
        .u16CurbHeight = 3471,
    },
    [SNS_MAP_DIS_HIGH_80cm] =
    {
        .u16PVC_PileHeight = 5854,
        .u16BigWallHeight = 44339,
        .u16SecondWallHeight = 9516,
        .u16CurbHeight = 3236,
    },
    [SNS_MAP_DIS_HIGH_90cm] =
    {
        .u16PVC_PileHeight = 5297,
        .u16BigWallHeight = 40120,
        .u16SecondWallHeight = 8861,
        .u16CurbHeight = 3018,
    },
    [SNS_MAP_DIS_HIGH_100cm] =
    {
        .u16PVC_PileHeight = 4793,
        .u16BigWallHeight = 36302,
        .u16SecondWallHeight = 7791,
        .u16CurbHeight = 2814,
    },
    [SNS_MAP_DIS_HIGH_110cm] =
    {
        .u16PVC_PileHeight = 4337,
        .u16BigWallHeight = 32847,
        .u16SecondWallHeight = 7050,
        .u16CurbHeight = 2623,
    },
    [SNS_MAP_DIS_HIGH_120cm] =
    {
        .u16PVC_PileHeight = 3924,
        .u16BigWallHeight = 29721,
        .u16SecondWallHeight = 6379,
        .u16CurbHeight = 2446,
    },
    [SNS_MAP_DIS_HIGH_130cm] =
    {
        .u16PVC_PileHeight = 3551,
        .u16BigWallHeight = 26893,
        .u16SecondWallHeight = 5772,
        .u16CurbHeight = 2281,
    },
    [SNS_MAP_DIS_HIGH_140cm] =
    {
        .u16PVC_PileHeight = 3213,
        .u16BigWallHeight = 24334,
        .u16SecondWallHeight = 5222,
        .u16CurbHeight = 2126,
    },
    [SNS_MAP_DIS_HIGH_150cm] =
    {
        .u16PVC_PileHeight = 2907,
        .u16BigWallHeight = 22018,
        .u16SecondWallHeight = 4725,
        .u16CurbHeight = 1983,
    },
    [SNS_MAP_DIS_HIGH_160cm] =
    {
        .u16PVC_PileHeight = 2630,
        .u16BigWallHeight = 19923,
        .u16SecondWallHeight = 4276,
        .u16CurbHeight = 1848,
    },
    [SNS_MAP_DIS_HIGH_170cm] =
    {
        .u16PVC_PileHeight = 2380,
        .u16BigWallHeight = 18027,
        .u16SecondWallHeight = 3869,
        .u16CurbHeight = 1723,
    },
    [SNS_MAP_DIS_HIGH_180cm] =
    {
        .u16PVC_PileHeight = 2153,
        .u16BigWallHeight = 16311,
        .u16SecondWallHeight = 3501,
        .u16CurbHeight = 1607,
    },
    [SNS_MAP_DIS_HIGH_190cm] =
    {
        .u16PVC_PileHeight = 1948,
        .u16BigWallHeight = 14759,
        .u16SecondWallHeight = 3167,
        .u16CurbHeight = 1498,
    },
    [SNS_MAP_DIS_HIGH_200cm] =
    {
        .u16PVC_PileHeight = 1763,
        .u16BigWallHeight = 13354,
        .u16SecondWallHeight = 2866,
        .u16CurbHeight = 1397,
    },
    [SNS_MAP_DIS_HIGH_210cm] =
    {
        .u16PVC_PileHeight = 1595,
        .u16BigWallHeight = 12084,
        .u16SecondWallHeight = 2593,
        .u16CurbHeight = 1302,
    },
    [SNS_MAP_DIS_HIGH_220cm] =
    {
        .u16PVC_PileHeight = 1443,
        .u16BigWallHeight = 10934,
        .u16SecondWallHeight = 2346,
        .u16CurbHeight = 1214,
    },
    [SNS_MAP_DIS_HIGH_230cm] =
    {
        .u16PVC_PileHeight = 1443,
        .u16BigWallHeight = 10934,
        .u16SecondWallHeight = 2346,
        .u16CurbHeight = 1214,
    },
    [SNS_MAP_DIS_HIGH_240cm] =
    {
        .u16PVC_PileHeight = 1443,
        .u16BigWallHeight = 10934,
        .u16SecondWallHeight = 2346,
        .u16CurbHeight = 1214,
    },
    [SNS_MAP_DIS_HIGH_250cm] =
    {
        .u16PVC_PileHeight = 1443,
        .u16BigWallHeight = 10934,
        .u16SecondWallHeight = 2346,
        .u16CurbHeight = 1214,
    },
    [SNS_MAP_DIS_HIGH_260cm] =
    {
        .u16PVC_PileHeight = 1443,
        .u16BigWallHeight = 10934,
        .u16SecondWallHeight = 2346,
        .u16CurbHeight = 1214,
    },
    [SNS_MAP_DIS_HIGH_270cm] =
    {
        .u16PVC_PileHeight = 1443,
        .u16BigWallHeight = 10934,
        .u16SecondWallHeight = 2346,
        .u16CurbHeight = 1214,
    },
    [SNS_MAP_DIS_HIGH_280cm] =
    {
        .u16PVC_PileHeight = 1443,
        .u16BigWallHeight = 10934,
        .u16SecondWallHeight = 2346,
        .u16CurbHeight = 1214,
    },
    [SNS_MAP_DIS_HIGH_290cm] =
    {
        .u16PVC_PileHeight = 1443,
        .u16BigWallHeight = 10934,
        .u16SecondWallHeight = 2346,
        .u16CurbHeight = 1214,
    },
    [SNS_MAP_DIS_HIGH_300cm] =
    {
        .u16PVC_PileHeight = 1443,
        .u16BigWallHeight = 10934,
        .u16SecondWallHeight = 2346,
        .u16CurbHeight = 1214,
    },
    [SNS_MAP_DIS_HIGH_310cm] =
    {
        .u16PVC_PileHeight = 1443,
        .u16BigWallHeight = 10934,
        .u16SecondWallHeight = 2346,
        .u16CurbHeight = 1214,
    },
    [SNS_MAP_DIS_HIGH_320cm] =
    {
        .u16PVC_PileHeight = 1443,
        .u16BigWallHeight = 10934,
        .u16SecondWallHeight = 2346,
        .u16CurbHeight = 1214,
    },
    [SNS_MAP_DIS_HIGH_330cm] =
    {
        .u16PVC_PileHeight = 1443,
        .u16BigWallHeight = 10934,
        .u16SecondWallHeight = 2346,
        .u16CurbHeight = 1214,
    },
    [SNS_MAP_DIS_HIGH_340cm] =
    {
        .u16PVC_PileHeight = 1443,
        .u16BigWallHeight = 10934,
        .u16SecondWallHeight = 2346,
        .u16CurbHeight = 1214,
    },
    [SNS_MAP_DIS_HIGH_350cm] =
    {
        .u16PVC_PileHeight = 1443,
        .u16BigWallHeight = 10934,
        .u16SecondWallHeight = 2346,
        .u16CurbHeight = 1214,
    },
    [SNS_MAP_DIS_HIGH_360cm] =
    {
        .u16PVC_PileHeight = 1443,
        .u16BigWallHeight = 10934,
        .u16SecondWallHeight = 2346,
        .u16CurbHeight = 1214,
    },
    [SNS_MAP_DIS_HIGH_370cm] =
    {
        .u16PVC_PileHeight = 1443,
        .u16BigWallHeight = 10934,
        .u16SecondWallHeight = 2346,
        .u16CurbHeight = 1214,
    },
    [SNS_MAP_DIS_HIGH_380cm] =
    {
        .u16PVC_PileHeight = 1443,
        .u16BigWallHeight = 10934,
        .u16SecondWallHeight = 2346,
        .u16CurbHeight = 1214,
    },
    [SNS_MAP_DIS_HIGH_390cm] =
    {
        .u16PVC_PileHeight = 1443,
        .u16BigWallHeight = 10934,
        .u16SecondWallHeight = 2346,
        .u16CurbHeight = 1214,
    },
    [SNS_MAP_DIS_HIGH_400cm] =
    {
        .u16PVC_PileHeight = 1443,
        .u16BigWallHeight = 10934,
        .u16SecondWallHeight = 2346,
        .u16CurbHeight = 1214,
    },

};


/* 扫频模式下障碍物回波高度判断障碍物类型的阈值 */
const SnsMapCalibHeightType GStrMapObjJudgeChirpThresTable[SNS_MAP_DIS_HIGH_TOTAL_NUM] = 
{
    [SNS_MAP_DIS_HIGH_10cm] =
    {
        .u16PVC_PileHeight = 6863,
        .u16BigWallHeight = 35881,
        .u16SecondWallHeight = 14839,
        .u16CurbHeight = 2576,
    },
    [SNS_MAP_DIS_HIGH_20cm] =
    {
        .u16PVC_PileHeight = 6863,
        .u16BigWallHeight = 35881,
        .u16SecondWallHeight = 14839,
        .u16CurbHeight = 2576,
    },
    [SNS_MAP_DIS_HIGH_30cm] =
    {
        .u16PVC_PileHeight = 6863,
        .u16BigWallHeight = 35881,
        .u16SecondWallHeight = 14839,
        .u16CurbHeight = 2576,
    },
    [SNS_MAP_DIS_HIGH_40cm] =
    {
        .u16PVC_PileHeight = 6863,
        .u16BigWallHeight = 35881,
        .u16SecondWallHeight = 14839,
        .u16CurbHeight = 2576,
    },
    [SNS_MAP_DIS_HIGH_50cm] =
    {
        .u16PVC_PileHeight = 6863,
        .u16BigWallHeight = 35881,
        .u16SecondWallHeight = 14839,
        .u16CurbHeight = 2576,
    },
    [SNS_MAP_DIS_HIGH_60cm] =
    {
        .u16PVC_PileHeight = 6210,
        .u16BigWallHeight = 32467,
        .u16SecondWallHeight = 13427,
        .u16CurbHeight = 2426,
    },
    [SNS_MAP_DIS_HIGH_70cm] =
    {
        .u16PVC_PileHeight = 5619,
        .u16BigWallHeight = 29377,
        .u16SecondWallHeight = 12149,
        .u16CurbHeight = 2284,
    },
    [SNS_MAP_DIS_HIGH_80cm] =
    {
        .u16PVC_PileHeight = 5084,
        .u16BigWallHeight = 26581,
        .u16SecondWallHeight = 10993,
        .u16CurbHeight = 2151,
    },
    [SNS_MAP_DIS_HIGH_90cm] =
    {
        .u16PVC_PileHeight = 4600,
        .u16BigWallHeight = 24052,
        .u16SecondWallHeight = 9947,
        .u16CurbHeight = 2026,
    },
    [SNS_MAP_DIS_HIGH_100cm] =
    {
        .u16PVC_PileHeight = 4162,
        .u16BigWallHeight = 21763,
        .u16SecondWallHeight = 9000,
        .u16CurbHeight = 1908,
    },
    [SNS_MAP_DIS_HIGH_110cm] =
    {
        .u16PVC_PileHeight = 3766,
        .u16BigWallHeight = 19692,
        .u16SecondWallHeight = 8144,
        .u16CurbHeight = 1797,
    },
    [SNS_MAP_DIS_HIGH_120cm] =
    {
        .u16PVC_PileHeight = 3408,
        .u16BigWallHeight = 17818,
        .u16SecondWallHeight = 7369,
        .u16CurbHeight = 1692,
    },
    [SNS_MAP_DIS_HIGH_130cm] =
    {
        .u16PVC_PileHeight = 3083,
        .u16BigWallHeight = 16122,
        .u16SecondWallHeight = 6667,
        .u16CurbHeight = 1594,
    },
    [SNS_MAP_DIS_HIGH_140cm] =
    {
        .u16PVC_PileHeight = 2790,
        .u16BigWallHeight = 14588,
        .u16SecondWallHeight = 6033,
        .u16CurbHeight = 1501,
    },
    [SNS_MAP_DIS_HIGH_150cm] =
    {
        .u16PVC_PileHeight = 2524,
        .u16BigWallHeight = 13200,
        .u16SecondWallHeight = 5459,
        .u16CurbHeight = 1413,
    },
    [SNS_MAP_DIS_HIGH_160cm] =
    {
        .u16PVC_PileHeight = 2284,
        .u16BigWallHeight = 11943,
        .u16SecondWallHeight = 4939,
        .u16CurbHeight = 1331,
    },
    [SNS_MAP_DIS_HIGH_170cm] =
    {
        .u16PVC_PileHeight = 2067,
        .u16BigWallHeight = 10807,
        .u16SecondWallHeight = 4469,
        .u16CurbHeight = 1254,
    },
    [SNS_MAP_DIS_HIGH_180cm] =
    {
        .u16PVC_PileHeight = 1870,
        .u16BigWallHeight = 9778,
        .u16SecondWallHeight = 4044,
        .u16CurbHeight = 1180,
    },
    [SNS_MAP_DIS_HIGH_190cm] =
    {
        .u16PVC_PileHeight = 1692,
        .u16BigWallHeight = 8848,
        .u16SecondWallHeight = 3659,
        .u16CurbHeight = 1112,
    },
    [SNS_MAP_DIS_HIGH_200cm] =
    {
        .u16PVC_PileHeight = 1531,
        .u16BigWallHeight = 8006,
        .u16SecondWallHeight = 3311,
        .u16CurbHeight = 1047,
    },
    [SNS_MAP_DIS_HIGH_210cm] =
    {
        .u16PVC_PileHeight = 1385,
        .u16BigWallHeight = 7244,
        .u16SecondWallHeight = 2996,
        .u16CurbHeight = 986,
    },
    [SNS_MAP_DIS_HIGH_220cm] =
    {
        .u16PVC_PileHeight = 1253,
        .u16BigWallHeight = 6555,
        .u16SecondWallHeight = 2710,
        .u16CurbHeight = 928,
    },
    [SNS_MAP_DIS_HIGH_230cm] =
    {
        .u16PVC_PileHeight = 1134,
        .u16BigWallHeight = 5931,
        .u16SecondWallHeight = 2452,
        .u16CurbHeight = 874,
    },
    [SNS_MAP_DIS_HIGH_240cm] =
    {
        .u16PVC_PileHeight = 1026,
        .u16BigWallHeight = 5366,
        .u16SecondWallHeight = 2219,
        .u16CurbHeight = 823,
    },
    [SNS_MAP_DIS_HIGH_250cm] =
    {
        .u16PVC_PileHeight = 928,
        .u16BigWallHeight = 4856,
        .u16SecondWallHeight = 2008,
        .u16CurbHeight = 775,
    },
    [SNS_MAP_DIS_HIGH_260cm] =
    {
        .u16PVC_PileHeight = 840,
        .u16BigWallHeight = 4393,
        .u16SecondWallHeight = 1817,
        .u16CurbHeight = 730,
    },
    [SNS_MAP_DIS_HIGH_270cm] =
    {
        .u16PVC_PileHeight = 760,
        .u16BigWallHeight = 3975,
        .u16SecondWallHeight = 1644,
        .u16CurbHeight = 688,
    },
    [SNS_MAP_DIS_HIGH_280cm] =
    {
        .u16PVC_PileHeight = 688,
        .u16BigWallHeight = 3597,
        .u16SecondWallHeight = 1487,
        .u16CurbHeight = 648,
    },
    [SNS_MAP_DIS_HIGH_290cm] =
    {
        .u16PVC_PileHeight = 622,
        .u16BigWallHeight = 3255,
        .u16SecondWallHeight = 1346,
        .u16CurbHeight = 610,
    },
    [SNS_MAP_DIS_HIGH_300cm] =
    {
        .u16PVC_PileHeight = 563,
        .u16BigWallHeight = 2945,
        .u16SecondWallHeight = 1218,
        .u16CurbHeight = 574,
    },
    /* 待补充 */
    [SNS_MAP_DIS_HIGH_310cm] =
    {
        .u16PVC_PileHeight = 509,
        .u16BigWallHeight = 2665,
        .u16SecondWallHeight = 1102,
        .u16CurbHeight = 541,
    },
    [SNS_MAP_DIS_HIGH_320cm] =
    {
        .u16PVC_PileHeight = 461,
        .u16BigWallHeight = 2411,
        .u16SecondWallHeight = 997,
        .u16CurbHeight = 509,
    },
    [SNS_MAP_DIS_HIGH_330cm] =
    {
        .u16PVC_PileHeight = 417,
        .u16BigWallHeight = 2181,
        .u16SecondWallHeight = 902,
        .u16CurbHeight = 480,
    },
    [SNS_MAP_DIS_HIGH_340cm] =
    {
        .u16PVC_PileHeight = 377,
        .u16BigWallHeight = 1974,
        .u16SecondWallHeight = 816,
        .u16CurbHeight = 452,
    },
    [SNS_MAP_DIS_HIGH_350cm] =
    {
        .u16PVC_PileHeight = 341,
        .u16BigWallHeight = 1786,
        .u16SecondWallHeight = 738,
        .u16CurbHeight = 425,
    },
    [SNS_MAP_DIS_HIGH_360cm] =
    {
        .u16PVC_PileHeight = 309,
        .u16BigWallHeight = 1616,
        .u16SecondWallHeight = 668,
        .u16CurbHeight = 401,
    },
    [SNS_MAP_DIS_HIGH_370cm] =
    {
        .u16PVC_PileHeight = 279,
        .u16BigWallHeight = 1462,
        .u16SecondWallHeight = 604,
        .u16CurbHeight = 377,
    },
    [SNS_MAP_DIS_HIGH_380cm] =
    {
        .u16PVC_PileHeight = 253,
        .u16BigWallHeight = 1323,
        .u16SecondWallHeight = 547,
        .u16CurbHeight = 355,
    },
    [SNS_MAP_DIS_HIGH_390cm] =
    {
        .u16PVC_PileHeight = 229,
        .u16BigWallHeight = 1197,
        .u16SecondWallHeight = 495,
        .u16CurbHeight = 334,
    },
    [SNS_MAP_DIS_HIGH_400cm] =
    {
        .u16PVC_PileHeight = 207,
        .u16BigWallHeight = 1083,
        .u16SecondWallHeight = 448,
        .u16CurbHeight = 315,
    },
};

/* 新增侧边探头区分障碍物类型的阈值，和前后探头的区分阈值分开来 */
const SnsMapCalibHeightType GStrMapObjJudgeStdThresTableForSideMap[SNS_MAP_DIS_HIGH_TOTAL_NUM] = 
{
    /* 需要根据路砍等低矮障碍物的高度过滤10~80cm的PVC阈值 */
    [SNS_MAP_DIS_HIGH_10cm] = 
    {
        .u16PVC_PileHeight = 2500,
        .u16BigWallHeight = 8000,
        .u16SecondWallHeight = 2000,
        .u16CurbHeight = 3000,
    },
    [SNS_MAP_DIS_HIGH_20cm] = 
    {
        .u16PVC_PileHeight = 2500,
        .u16BigWallHeight = 8000,
        .u16SecondWallHeight = 2000,
        .u16CurbHeight = 3000,
    },
    [SNS_MAP_DIS_HIGH_30cm] = 
    {
        .u16PVC_PileHeight = 2500,
        .u16BigWallHeight = 8000,
        .u16SecondWallHeight = 2000,
        .u16CurbHeight = 3000,
    },
    [SNS_MAP_DIS_HIGH_40cm] = 
    {
        .u16PVC_PileHeight = 2500,
        .u16BigWallHeight = 8000,
        .u16SecondWallHeight = 2000,
        .u16CurbHeight = 3000,
    },
    [SNS_MAP_DIS_HIGH_50cm] = 
    {
        .u16PVC_PileHeight = 3000,
        .u16BigWallHeight = 8000,
        .u16SecondWallHeight = 2000,
        .u16CurbHeight = 3000,
    },
    [SNS_MAP_DIS_HIGH_60cm] = 
    {
        .u16PVC_PileHeight = 2000,
        .u16BigWallHeight = 8000,
        .u16SecondWallHeight = 1800,
        .u16CurbHeight = 3000,
    },
    [SNS_MAP_DIS_HIGH_70cm] = 
    {
        .u16PVC_PileHeight = 1800,
        .u16BigWallHeight = 8000,
        .u16SecondWallHeight = 1600,
        .u16CurbHeight = 3000,
    },
    [SNS_MAP_DIS_HIGH_80cm] = 
    {
        .u16PVC_PileHeight = 1600,
        .u16BigWallHeight = 8000,
        .u16SecondWallHeight = 1500,
        .u16CurbHeight = 3000,
    },
    [SNS_MAP_DIS_HIGH_90cm] = 
    {
        .u16PVC_PileHeight = 1400,
        .u16BigWallHeight = 10000,
        .u16SecondWallHeight = 1500,
        .u16CurbHeight = 3000,
    },
    [SNS_MAP_DIS_HIGH_100cm] = 
    {
        .u16PVC_PileHeight = 1200,
        .u16BigWallHeight = 10000,
        .u16SecondWallHeight = 1450,
        .u16CurbHeight = 3000,
    },
    [SNS_MAP_DIS_HIGH_110cm] = 
    {
        .u16PVC_PileHeight = 1100,
        .u16BigWallHeight = 12000,
        .u16SecondWallHeight = 1400,
        .u16CurbHeight = 3000,
    },
    [SNS_MAP_DIS_HIGH_120cm] = 
    {
        .u16PVC_PileHeight = 1000,
        .u16BigWallHeight = 12000,
        .u16SecondWallHeight = 1300,
        .u16CurbHeight = 3000,
    },
    [SNS_MAP_DIS_HIGH_130cm] = 
    {
        .u16PVC_PileHeight = 900,
        .u16BigWallHeight = 11500,
        .u16SecondWallHeight = 1300,
        .u16CurbHeight = 3000,
    },
    [SNS_MAP_DIS_HIGH_140cm] = 
    {
        .u16PVC_PileHeight = 900,
        .u16BigWallHeight = 11000,
        .u16SecondWallHeight = 1300,
        .u16CurbHeight = 3000,
    },
    [SNS_MAP_DIS_HIGH_150cm] = 
    {
        .u16PVC_PileHeight = 800,
        .u16BigWallHeight = 10500,
        .u16SecondWallHeight = 1300,
        .u16CurbHeight = 3000,
    },
    [SNS_MAP_DIS_HIGH_160cm] = 
    {
        .u16PVC_PileHeight = 750,
        .u16BigWallHeight = 10000,
        .u16SecondWallHeight = 1300,
        .u16CurbHeight = 2900,
    },
    [SNS_MAP_DIS_HIGH_170cm] = 
    {
        .u16PVC_PileHeight = 700,
        .u16BigWallHeight = 9000,
        .u16SecondWallHeight = 1300,
        .u16CurbHeight = 2800,
    },
    [SNS_MAP_DIS_HIGH_180cm] = 
    {
        .u16PVC_PileHeight = 650,
        .u16BigWallHeight = 8500,
        .u16SecondWallHeight = 1300,
        .u16CurbHeight = 2700,
    },
    [SNS_MAP_DIS_HIGH_190cm] = 
    {
        .u16PVC_PileHeight = 620,
        .u16BigWallHeight = 8000,
        .u16SecondWallHeight = 1250,
        .u16CurbHeight = 2600,
    },
    [SNS_MAP_DIS_HIGH_200cm] = 
    {
        .u16PVC_PileHeight = 600,
        .u16BigWallHeight = 7000,
        .u16SecondWallHeight = 1200,
        .u16CurbHeight = 2500,
    },
    [SNS_MAP_DIS_HIGH_210cm] = 
    {
        .u16PVC_PileHeight = 600,
        .u16BigWallHeight = 6500,
        .u16SecondWallHeight = 1150,
        .u16CurbHeight = 2400,
    },
    [SNS_MAP_DIS_HIGH_220cm] = 
    {
        .u16PVC_PileHeight = 600,
        .u16BigWallHeight = 5800,
        .u16SecondWallHeight = 1100,
        .u16CurbHeight = 2300,
    },
    [SNS_MAP_DIS_HIGH_230cm] = 
    {
        .u16PVC_PileHeight = 550,
        .u16BigWallHeight = 5400,
        .u16SecondWallHeight = 1050,
        .u16CurbHeight = 2200,
    },
    [SNS_MAP_DIS_HIGH_240cm] = 
    {
        .u16PVC_PileHeight = 550,
        .u16BigWallHeight = 5200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 2100,
    },
    [SNS_MAP_DIS_HIGH_250cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 5000,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 2000,
    },
    [SNS_MAP_DIS_HIGH_260cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 5000,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1900,
    },
    [SNS_MAP_DIS_HIGH_270cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 4800,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1800,
    },
    [SNS_MAP_DIS_HIGH_280cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 4500,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1700,
    },
    [SNS_MAP_DIS_HIGH_290cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 4250,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1600,
    },
    [SNS_MAP_DIS_HIGH_300cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 4000,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },    
    [SNS_MAP_DIS_HIGH_310cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 3800,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1450,
    },
    [SNS_MAP_DIS_HIGH_320cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 3600,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1400,
    },
    [SNS_MAP_DIS_HIGH_330cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 3500,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1300,
    },
    [SNS_MAP_DIS_HIGH_340cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 3500,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1200,
    },
    [SNS_MAP_DIS_HIGH_350cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 3500,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1100,
    },
    [SNS_MAP_DIS_HIGH_360cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 3500,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1000,
    },
    [SNS_MAP_DIS_HIGH_370cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 3500,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 900,
    },
    [SNS_MAP_DIS_HIGH_380cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 3500,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 900,
    },
    [SNS_MAP_DIS_HIGH_390cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 3500,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 900,
    },
    [SNS_MAP_DIS_HIGH_400cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 3500,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 900,
    }, 

};


/* 扫频模式下障碍物回波高度判断障碍物类型的阈值 */
const SnsMapCalibHeightType GStrMapObjJudgeChirpThresTableForSideMap[SNS_MAP_DIS_HIGH_TOTAL_NUM] = 
{
    [SNS_MAP_DIS_HIGH_10cm] = 
    {
        .u16PVC_PileHeight = 4500,
        .u16BigWallHeight = 7000,
        .u16SecondWallHeight = 1200,
        .u16CurbHeight = 3000,
    },
    [SNS_MAP_DIS_HIGH_20cm] = 
    {
        .u16PVC_PileHeight = 4500,
        .u16BigWallHeight = 7000,
        .u16SecondWallHeight = 1200,
        .u16CurbHeight = 3000,
    },
    [SNS_MAP_DIS_HIGH_30cm] = 
    {
        .u16PVC_PileHeight = 4500,
        .u16BigWallHeight = 7000,
        .u16SecondWallHeight = 1200,
        .u16CurbHeight = 3000,
    },
    [SNS_MAP_DIS_HIGH_40cm] = 
    {
        .u16PVC_PileHeight = 4500,
        .u16BigWallHeight = 7000,
        .u16SecondWallHeight = 1200,
        .u16CurbHeight = 6000,
    },
    [SNS_MAP_DIS_HIGH_50cm] = 
    {
        .u16PVC_PileHeight = 4500,
        .u16BigWallHeight = 7000,
        .u16SecondWallHeight = 1200,
        .u16CurbHeight = 6000,
    },
    [SNS_MAP_DIS_HIGH_60cm] = 
    {
        .u16PVC_PileHeight = 3000,
        .u16BigWallHeight = 7000,
        .u16SecondWallHeight = 1200,
        .u16CurbHeight = 6000,
    },
    [SNS_MAP_DIS_HIGH_70cm] = 
    {
        .u16PVC_PileHeight = 2500,
        .u16BigWallHeight = 7500,
        .u16SecondWallHeight = 1100,
        .u16CurbHeight = 6000,
    },
    [SNS_MAP_DIS_HIGH_80cm] = 
    {
        .u16PVC_PileHeight = 1500,
        .u16BigWallHeight = 8000,
        .u16SecondWallHeight = 1100,
        .u16CurbHeight = 6000,
    },
    [SNS_MAP_DIS_HIGH_90cm] = 
    {
        .u16PVC_PileHeight = 1000,
        .u16BigWallHeight = 9000,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 6000,
    },
    [SNS_MAP_DIS_HIGH_100cm] = 
    {
        .u16PVC_PileHeight = 900,
        .u16BigWallHeight = 10000,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 6000,
    },
    [SNS_MAP_DIS_HIGH_110cm] = 
    {
        .u16PVC_PileHeight = 900,
        .u16BigWallHeight = 10000,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 5000,
    },
    [SNS_MAP_DIS_HIGH_120cm] = 
    {
        .u16PVC_PileHeight = 850,
        .u16BigWallHeight = 10500,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 5000,
    },
    [SNS_MAP_DIS_HIGH_130cm] = 
    {
        .u16PVC_PileHeight = 800,
        .u16BigWallHeight = 10000,
        .u16SecondWallHeight = 1100,
        .u16CurbHeight = 5000,
    },
    [SNS_MAP_DIS_HIGH_140cm] = 
    {
        .u16PVC_PileHeight = 800,
        .u16BigWallHeight = 9000,
        .u16SecondWallHeight = 1100,
        .u16CurbHeight = 5000,
    },
    [SNS_MAP_DIS_HIGH_150cm] = 
    {
        .u16PVC_PileHeight = 750,
        .u16BigWallHeight = 6000,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 5000,
    },
    [SNS_MAP_DIS_HIGH_160cm] = 
    {
        .u16PVC_PileHeight = 700,
        .u16BigWallHeight = 5500,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 4500,
    },
    [SNS_MAP_DIS_HIGH_170cm] = 
    {
        .u16PVC_PileHeight = 650,
        .u16BigWallHeight = 5000,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 4500,
    },
    [SNS_MAP_DIS_HIGH_180cm] = 
    {
        .u16PVC_PileHeight = 600,
        .u16BigWallHeight = 5000,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 4000,
    },
    [SNS_MAP_DIS_HIGH_190cm] = 
    {
        .u16PVC_PileHeight = 550,
        .u16BigWallHeight = 6500,
        .u16SecondWallHeight = 900,
        .u16CurbHeight = 3500,
    },
    [SNS_MAP_DIS_HIGH_200cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 6000,
        .u16SecondWallHeight = 900,
        .u16CurbHeight = 3000,
    },
    [SNS_MAP_DIS_HIGH_210cm] = 
    {
        .u16PVC_PileHeight = 450,
        .u16BigWallHeight = 4000,
        .u16SecondWallHeight = 900,
        .u16CurbHeight = 3000,
    },
    [SNS_MAP_DIS_HIGH_220cm] = 
    {
        .u16PVC_PileHeight = 400,
        .u16BigWallHeight = 3500,
        .u16SecondWallHeight = 900,
        .u16CurbHeight = 3000,
    },
    [SNS_MAP_DIS_HIGH_230cm] = 
    {
        .u16PVC_PileHeight = 350,
        .u16BigWallHeight = 3500,
        .u16SecondWallHeight = 900,
        .u16CurbHeight = 3000,
    },
    [SNS_MAP_DIS_HIGH_240cm] = 
    {
        .u16PVC_PileHeight = 330,
        .u16BigWallHeight = 3500,
        .u16SecondWallHeight = 900,
        .u16CurbHeight = 3000,
    },
    [SNS_MAP_DIS_HIGH_250cm] = 
    {
        .u16PVC_PileHeight = 300,
        .u16BigWallHeight = 3500,
        .u16SecondWallHeight = 900,
        .u16CurbHeight = 3000,
    },
    [SNS_MAP_DIS_HIGH_260cm] = 
    {
        .u16PVC_PileHeight = 250,
        .u16BigWallHeight = 3200,
        .u16SecondWallHeight = 800,
        .u16CurbHeight = 2000,
    },
    [SNS_MAP_DIS_HIGH_270cm] = 
    {
        .u16PVC_PileHeight = 250,
        .u16BigWallHeight = 3000,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 1800,
    },
    [SNS_MAP_DIS_HIGH_280cm] = 
    {
        .u16PVC_PileHeight = 200,
        .u16BigWallHeight = 3000,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 1700,
    },
    [SNS_MAP_DIS_HIGH_290cm] = 
    {
        .u16PVC_PileHeight = 200,
        .u16BigWallHeight = 3000,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 1600,
    },
    [SNS_MAP_DIS_HIGH_300cm] = 
    {
        .u16PVC_PileHeight = 180,
        .u16BigWallHeight = 3000,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 1500,
    },
    /* 待补充 */
    [SNS_MAP_DIS_HIGH_310cm] = 
    {
        .u16PVC_PileHeight = 170,
        .u16BigWallHeight = 3000,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 1450,
    },
    [SNS_MAP_DIS_HIGH_320cm] = 
    {
        .u16PVC_PileHeight = 160,
        .u16BigWallHeight = 3000,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 1400,
    },
    [SNS_MAP_DIS_HIGH_330cm] = 
    {
        .u16PVC_PileHeight = 150,
        .u16BigWallHeight = 3000,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 1300,
    },
    [SNS_MAP_DIS_HIGH_340cm] = 
    {
        .u16PVC_PileHeight = 150,
        .u16BigWallHeight = 3000,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 1200,
    },
    [SNS_MAP_DIS_HIGH_350cm] = 
    {
        .u16PVC_PileHeight = 150,
        .u16BigWallHeight = 3000,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 1100,
    },
    [SNS_MAP_DIS_HIGH_360cm] = 
    {
        .u16PVC_PileHeight = 150,
        .u16BigWallHeight = 2500,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 1000,
    },
    [SNS_MAP_DIS_HIGH_370cm] = 
    {
        .u16PVC_PileHeight = 300,
        .u16BigWallHeight = 2500,
        .u16SecondWallHeight = 650,
        .u16CurbHeight = 900,
    },
    [SNS_MAP_DIS_HIGH_380cm] = 
    {
        .u16PVC_PileHeight = 300,
        .u16BigWallHeight = 2500,
        .u16SecondWallHeight = 600,
        .u16CurbHeight = 900,
    },
    [SNS_MAP_DIS_HIGH_390cm] = 
    {
        .u16PVC_PileHeight = 300,
        .u16BigWallHeight = 2500,
        .u16SecondWallHeight = 550,
        .u16CurbHeight = 900,
    },
    [SNS_MAP_DIS_HIGH_400cm] = 
    {
        .u16PVC_PileHeight = 300,
        .u16BigWallHeight = 2500,
        .u16SecondWallHeight = 500,
        .u16CurbHeight = 900,
    }, 
};



/* 在RAM中存储的数据，用于实际应用使用 */
uint16 Gu16FLS_FRS_MasStdThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
uint16 Gu16FLS_FRS_MasChirpThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
uint16 Gu16FL_FR_MasStdThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
uint16 Gu16FL_FR_MasChirpThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
uint16 Gu16FML_FMR_MasStdThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
uint16 Gu16FML_FMR_MasChirpThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];

uint16 Gu16RLS_RRS_MasStdThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
uint16 Gu16RLS_RRS_MasChirpThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
uint16 Gu16RL_RR_MasStdThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
uint16 Gu16RL_RR_MasChirpThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
uint16 Gu16RML_RMR_MasStdThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
uint16 Gu16RML_RMR_MasChirpThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];

/* 在RAM中定义用于障碍物类型区分的阈值数据 */
SnsMapCalibHeightType GStrMapObjJudgeStdThresTableInRAM[SNS_MAP_DIS_HIGH_TOTAL_NUM];
SnsMapCalibHeightType GStrMapObjJudgeChirpThresTableInRAM[SNS_MAP_DIS_HIGH_TOTAL_NUM];

/* 新增侧边Map的障碍物区分阈值 */
SnsMapCalibHeightType GStrMapObjJudgeStdThresTableForSideMapInRAM[SNS_MAP_DIS_HIGH_TOTAL_NUM];
SnsMapCalibHeightType GStrMapObjJudgeChirpThresTableForSideMapInRAM[SNS_MAP_DIS_HIGH_TOTAL_NUM];

const uint16 *Gpu16SnsMapThresholdTable[PDC_SNS_GROUP_NUM][6] = 
{
    [PDC_SNS_GROUP_FRONT] = 
    {
        Gu16FLS_FRS_MasStdThresTableForMapInRAM,
        Gu16FLS_FRS_MasChirpThresTableForMapInRAM,
        Gu16FL_FR_MasStdThresTableForMapInRAM,
        Gu16FL_FR_MasChirpThresTableForMapInRAM,
        Gu16FML_FMR_MasStdThresTableForMapInRAM,
        Gu16FML_FMR_MasChirpThresTableForMapInRAM,
    },
    
    [PDC_SNS_GROUP_REAR] = 
    {
        Gu16RLS_RRS_MasStdThresTableForMapInRAM,
        Gu16RLS_RRS_MasChirpThresTableForMapInRAM,
        Gu16RL_RR_MasStdThresTableForMapInRAM,
        Gu16RL_RR_MasChirpThresTableForMapInRAM,
        Gu16RML_RMR_MasStdThresTableForMapInRAM,
        Gu16RML_RMR_MasChirpThresTableForMapInRAM,
    }
};

uint16 Gu16FLS_FRS_LisStdThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
uint16 Gu16FLS_FRS_LisChirpThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
uint16 Gu16FL_FR_LisStdThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
uint16 Gu16FL_FR_LisChirpThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
uint16 Gu16FML_FMR_LisStdThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
uint16 Gu16FML_FMR_LisChirpThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];

uint16 Gu16RLS_RRS_LisStdThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
uint16 Gu16RLS_RRS_LisChirpThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
uint16 Gu16RL_RR_LisStdThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
uint16 Gu16RL_RR_LisChirpThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
uint16 Gu16RML_RMR_LisStdThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
uint16 Gu16RML_RMR_LisChirpThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
MapVheiCfgLevelType GenuMapVheiCfgLevel;

const uint16 *Gpu16SnsMapLisThresholdTable[PDC_SNS_GROUP_NUM][6] = 
{
    [PDC_SNS_GROUP_FRONT] = 
    {
        Gu16FLS_FRS_LisStdThresTableForMapInRAM,
        Gu16FLS_FRS_LisChirpThresTableForMapInRAM,
        Gu16FL_FR_LisStdThresTableForMapInRAM,
        Gu16FL_FR_LisChirpThresTableForMapInRAM,
        Gu16FML_FMR_LisStdThresTableForMapInRAM,
        Gu16FML_FMR_LisChirpThresTableForMapInRAM,
    },
    
    [PDC_SNS_GROUP_REAR] = 
    {
        Gu16RLS_RRS_LisStdThresTableForMapInRAM,
        Gu16RLS_RRS_LisChirpThresTableForMapInRAM,
        Gu16RL_RR_LisStdThresTableForMapInRAM,
        Gu16RL_RR_LisChirpThresTableForMapInRAM,
        Gu16RML_RMR_LisStdThresTableForMapInRAM,
        Gu16RML_RMR_LisChirpThresTableForMapInRAM,
    }
};



/******************************************************************************/
/******************************************************************************/
/****************************** Private Function ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/******************************* Public Function ******************************/
/******************************************************************************/
/******************************************************************************/
void CopySnsMapCaliThresToRam(void)
{
  #if (CALIBRATION_EN == STD_ON)
        /* 在DID_Calibration初始化 */
  #else
    memcpy(&Gu16FLS_FRS_MasStdThresTableForMapInRAM[0],&Gu16FLS_FRS_MasStdThresTableForMap[0],SNS_MAP_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16FLS_FRS_MasChirpThresTableForMapInRAM[0],&Gu16FLS_FRS_MasChirpThresTableForMap[0],SNS_MAP_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16FL_FR_MasStdThresTableForMapInRAM[0],&Gu16FL_FR_MasStdThresTableForMap[0],SNS_MAP_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16FL_FR_MasChirpThresTableForMapInRAM[0],&Gu16FL_FR_MasChirpThresTableForMap[0],SNS_MAP_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16FML_FMR_MasStdThresTableForMapInRAM[0],&Gu16FML_FMR_MasStdThresTableForMap[0],SNS_MAP_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16FML_FMR_MasChirpThresTableForMapInRAM[0],&Gu16FML_FMR_MasChirpThresTableForMap[0],SNS_MAP_DIS_TOTAL_NUM*sizeof(uint16));

    memcpy(&Gu16RLS_RRS_MasStdThresTableForMapInRAM[0],&Gu16RLS_RRS_MasStdThresTableForMap[0],SNS_MAP_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16RLS_RRS_MasChirpThresTableForMapInRAM[0],&Gu16RLS_RRS_MasChirpThresTableForMap[0],SNS_MAP_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16RL_RR_MasStdThresTableForMapInRAM[0],&Gu16RL_RR_MasStdThresTableForMap[0],SNS_MAP_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16RL_RR_MasChirpThresTableForMapInRAM[0],&Gu16RL_RR_MasChirpThresTableForMap[0],SNS_MAP_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16RML_RMR_MasStdThresTableForMapInRAM[0],&Gu16RML_RMR_MasStdThresTableForMap[0],SNS_MAP_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16RML_RMR_MasChirpThresTableForMapInRAM[0],&Gu16RML_RMR_MasChirpThresTableForMap[0],SNS_MAP_DIS_TOTAL_NUM*sizeof(uint16));
  #endif     
    memcpy(&GStrMapObjJudgeStdThresTableInRAM[0],&GStrMapObjJudgeStdThresTable[0],SNS_MAP_DIS_HIGH_TOTAL_NUM*sizeof(SnsMapCalibHeightType));
    memcpy(&GStrMapObjJudgeChirpThresTableInRAM[0],&GStrMapObjJudgeChirpThresTable[0],SNS_MAP_DIS_HIGH_TOTAL_NUM*sizeof(SnsMapCalibHeightType));
  #if (CALIBRATION_EN == STD_ON)
        /* 在DID_Calibration初始化 */
  #else
    memcpy(&Gu16FLS_FRS_LisStdThresTableForMapInRAM[0],&Gu16FLS_FRS_LisStdThresTableForMap[0],SNS_MAP_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16FLS_FRS_LisChirpThresTableForMapInRAM[0],&Gu16FLS_FRS_LisChirpThresTableForMap[0],SNS_MAP_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16FL_FR_LisStdThresTableForMapInRAM[0],&Gu16FL_FR_LisStdThresTableForMap[0],SNS_MAP_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16FL_FR_LisChirpThresTableForMapInRAM[0],&Gu16FL_FR_LisChirpThresTableForMap[0],SNS_MAP_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16FML_FMR_LisStdThresTableForMapInRAM[0],&Gu16FML_FMR_LisStdThresTableForMap[0],SNS_MAP_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16FML_FMR_LisChirpThresTableForMapInRAM[0],&Gu16FML_FMR_LisChirpThresTableForMap[0],SNS_MAP_DIS_TOTAL_NUM*sizeof(uint16));

    memcpy(&Gu16RLS_RRS_LisStdThresTableForMapInRAM[0],&Gu16RLS_RRS_LisStdThresTableForMap[0],SNS_MAP_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16RLS_RRS_LisChirpThresTableForMapInRAM[0],&Gu16RLS_RRS_LisChirpThresTableForMap[0],SNS_MAP_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16RL_RR_LisStdThresTableForMapInRAM[0],&Gu16RL_RR_LisStdThresTableForMap[0],SNS_MAP_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16RL_RR_LisChirpThresTableForMapInRAM[0],&Gu16RL_RR_LisChirpThresTableForMap[0],SNS_MAP_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16RML_RMR_LisStdThresTableForMapInRAM[0],&Gu16RML_RMR_LisStdThresTableForMap[0],SNS_MAP_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16RML_RMR_LisChirpThresTableForMapInRAM[0],&Gu16RML_RMR_LisChirpThresTableForMap[0],SNS_MAP_DIS_TOTAL_NUM*sizeof(uint16));
  #endif 

    memcpy(&GStrMapObjJudgeStdThresTableForSideMapInRAM[0],&GStrMapObjJudgeStdThresTableForSideMap[0],SNS_MAP_DIS_HIGH_TOTAL_NUM*sizeof(SnsMapCalibHeightType));
    memcpy(&GStrMapObjJudgeChirpThresTableForSideMapInRAM[0],&GStrMapObjJudgeChirpThresTableForSideMap[0],SNS_MAP_DIS_HIGH_TOTAL_NUM*sizeof(SnsMapCalibHeightType));


    VehicleCfgLevelType LenuVehicleCfgLevel;
    ReadCAN_AppSignal_VehicleCfgLevel(&LenuVehicleCfgLevel);
    
    GenuMapVheiCfgLevel = (MapVheiCfgLevelType)LenuVehicleCfgLevel;

}

