/******************************************************************************
 * Copyright (c) Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * All Rights Reserved.
 *
 * FileName : Power_ManageCfg.h:
 * Date	    : 2020-08-11 09:44
 * Author   : 22446
 ******************************************************************************/
#ifndef   __POWER_MANAGE_CFG_H__
#define   __POWER_MANAGE_CFG_H__

/******************************************************************************
 * Inclusion of private header files
 *****************************************************************************/
#include "LhRule.h"
#include "AdcHal.h"
#include "PublicCalAlgorithm_Int.h"

/*****************************************************************************/

/**
 * \defgroup   SW_TIMER
 * @{
 */
#ifdef __cplusplus
extern "C"{
#endif


/**********************************************************************
 * Macros
 **********************************************************************/
#define AD_CALIBRATION_1_5V						(130U)
#define AD_CALIBRATION_2_5V						(330U)
#define AD_CALIBRATION_6V						(1040U) 	
#define AD_CALIBRATION_6_5V					    (1125U)	
#define AD_CALIBRATION_7V					    (1225U)	
#define AD_CALIBRATION_7_5V					    (1330U)	
#define AD_CALIBRATION_8V						(1435U)		
#define AD_CALIBRATION_8_5V					    (1535U)	
#define AD_CALIBRATION_8_8V					    (1590U)	

#define AD_CALIBRATION_9V						(1658U)
#define AD_CALIBRATION_9_2V					    (1698U)	
#define AD_CALIBRATION_9_5V					    (1760U)	


#define AD_CALIBRATION_10V					    (1860U)
#define AD_CALIBRATION_11V					    (2065U)
#define AD_CALIBRATION_12V					    (2265U)	
#define AD_CALIBRATION_13V					    (2468U)	
#define AD_CALIBRATION_14V					    (2675U)
#define AD_CALIBRATION_15V					    (2880U)

#define AD_CALIBRATION_15_5V					(2980U)		
#define AD_CALIBRATION_15_8V					(3045U)		

#define AD_CALIBRATION_16V					    (3140U)
#define AD_CALIBRATION_16_2V					(3180U)	
#define AD_CALIBRATION_16_5V					(3200U)		
#define AD_CALIBRATION_17V					    (3300U)		 
#define AD_CALIBRATION_17_5V					(3410U)		
#define AD_CALIBRATION_18V					    (3510U)
#define AD_CALIBRATION_18_5V					(3600U)
#define AD_CALIBRATION_19V					    (3700U)
#define AD_CALIBRATION_19_5V					(3800U)
#define AD_CALIBRATION_20V					    (3900U)


/**********************************************************************
 * Struct Define
 **********************************************************************/
typedef uint8 (*ReadPinSts)(void);

 typedef enum
{
    PM_APP = 0,
    PM_CAN_TX_RX,
    PM_CAN_UDS,
    PM_TOTAL,
}Signal_PowerManageGroupType;
	
typedef enum
{
    UNDER_VOLTAGE = 0,          
    NORMAL_VOLTAGE,             
    OVER_VOLTAGE,               
}Signal_VoltageStatusType;    /* 各组所需的APP电压类型 */

typedef enum
{
    PWR_PIN_NORMAL = 0,          
    PWR_PIN_SHORT_TO_PWR,             
    PWR_PIN_SHORT_TO_GND,               
}SnsPwrDiagStatus_e;

typedef enum
{
	SupplyPwrAdcIdx = 0,
	FrontSnsGroupPwrAdcIdx,
	RearSnsGroupPwrAdcIdx,
	SideSnsGroupPwrAdcIdx,
	PowerAdcIdxNum
}PowerAdcIdx_e;
	
typedef struct
{
	AdcDete_GroupType AdcUint;
	uint8 AdcChannel;
}PowerAdcCfg_t;

typedef enum
{
	FrontSnsGroupPwrDiagIdx = 0,
	RearSnsGroupPwrDiagIdx,
	SideSnsGroupPwrDiagIdx,
	SnsGroupPwrDiagNum
}SnsPowerDiagIdx_e;

typedef struct
{
	PowerAdcIdx_e 		PwrAdcValIdx;
	Pub_FilterCtrl_t    ShortToGndFilCtrl;	
	Pub_FilterCtrl_t    ShortToPwrFilCtrl;
	ReadPinSts          ReadPwrEnPinSts;
}SnsPowerDiagCtrl_t;

typedef struct
{	
	uint16 u16Normal2LowVoltage;
	uint16 u16Low2NormalVoltage;
	uint16 u16Normal2HighVoltage;
	uint16 u16High2NormalVoltage;
}PowerMonitorCfg_t;

/**********************************************************************
 * Extern Define
 **********************************************************************/
extern PowerMonitorCfg_t GstrPM_Config[PM_TOTAL];
extern PowerAdcCfg_t GstrPwrAdcCfg[PowerAdcIdxNum];
extern SnsPowerDiagCtrl_t SnsPwrPinDiagCtrl[SnsGroupPwrDiagNum];
/**********************************************************************
 * Function Define
 **********************************************************************/

/**
 * end of group   
 * @}
 */
#ifdef __cplusplus
}
#endif
/*****************************************************************************/

#endif /* end of __POWER_MANAGE_CFG_H__ */
