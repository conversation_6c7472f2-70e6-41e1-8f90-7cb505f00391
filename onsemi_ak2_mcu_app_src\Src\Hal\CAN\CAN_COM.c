/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: CAN_COM.c
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/
#include "CAN_COM.h"
#include "CAN_IL.h"
#include "CANDrv.h"
#include "DTCMonitor.h"
#include "TimerManage.h"
#include "Debug.h"
#include "PowerSingalManage.h"
#if(DEBUG_FRAME_TX_EN==STD_ON)
#include "DebugSignalManage.h"
#endif
/********************************数据类型定义**********************************/

/******************************************************************************/
/**
 *  @brief CAN Err类型
 */
/******************************************************************************/
typedef struct
{
    uint32 BusOffErr :1;
    uint32 Reserved0 :3;
    uint32 BusRecovery :1;
    uint32 Reserved1 :5;
    uint32 AckErr :1;
    uint32 Reserved2 :21;
}CANErrType;

/**********************************变量定义************************************/
uint8 GcComDataBuffer[RXANDTXFRAMEMAXNUM][COM_DATA_MAX];
uint8 GcComFrameID[RXANDTXFRAMEMAXNUM];
uint8 GcFrameStatus[RXANDTXFRAMEMAXNUM];                    /* 每一帧报文的状态 */
uint8 Gu8FrameNMCtrlStatus[RXANDTXFRAMEMAXNUM];             /* 网络管理控制报文的状态 */
uint16 GwComFramePeriodCnt[RXANDTXFRAMEMAXNUM];
uint16 GwContinueRxCnt[RXANDTXFRAMEMAXNUM];

uint8 GcTxFrameIdx[TXMAILBOXNUM];
uint8 GcTxMailboxStatus[TXMAILBOXNUM];                      /* 每一发送帧的状态 */

volatile uint8 GcCANBusState = 0;                           /* busoff状态 */
volatile uint16 GwBusoffRecoverTimer=0;                     /* Busoff恢复时间       */
volatile uint16 GwRxMonitorRecoverTimer = 0u;               /* Busoff回复节点监控定时 */
volatile uint8 GcBusoffContinueCnt=0;                       /* 连续发生Busoff的次数，发送成功函数对其清零 */
volatile uint8 Gu8BusoffRecoverCnt = 0;                      /* 连续发生Busoff的次数，发送成功函数对其清零 */

uint8 Gu8ComDataDLC[RXANDTXFRAMEMAXNUM] = { 0 };

uint16 Gu16DelayDetect5A4Timer_60S = 0;
uint8 Gu8StartDetect5A4Flag = DISABLEDETECT5A4;
volatile uint32 Gu32CANTimeSysn_RxInt = 0;
const uint8 Gu8CANTXBufferIdx[TXMAILBOXNUM] = {0,3,6,9,12,15};

//static uint16 Gu16DebugMsgID[DEBUG_MASSAGE_NUM] = 0u;              /* 调试报文发送ID */
//static uint8 Gu8DebugMsgDLC[DEBUG_MASSAGE_NUM] = 0u;               /* 调试报文DLC */
//uint8 Gu8DebugMsg[DEBUG_MASSAGE_NUM][64];                /* 调试报文内容指针 */
//static uint8 Gu8Debug_Massage_Sts[DEBUG_MASSAGE_NUM];/* 调试报文发送使能状态 */
uint8 Gu8CycleTime = 0;
uint8 Gu8CycleQueueIdx = 0;
uint8 Gu8TxQueueStartFlag[RXANDTXFRAMEMAXNUM] = { 0 };/*队列报文互锁标志；发送数据填充完则清0*/
/********************************静态函数声明**********************************/
static void COM_TxFrame(uint8 LcMailboxIdx, uint8 LcFrameIdx);
static void CAN_BusOffInd(void);
//static void COM_TxDebugFrame(void);
/********************************全局函数定义**********************************/

/******************************************************************************/
/**<pre>
 *函数名称: COM_Init
 *功能描述: COM层初始化
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void COM_Init(void)
{
    uint8 LcFrameIdx;

    for(LcFrameIdx = 0U; LcFrameIdx < RXANDTXFRAMEMAXNUM; LcFrameIdx ++)
    {
        GwComFramePeriodCnt[LcFrameIdx] = 0u;

        /* 对于周期性报文使能帧状态为使能位 */
        if(GwComFramePeriod[LcFrameIdx] != 0u)
        {
            NCS_SET_BIT(GcFrameStatus[LcFrameIdx],COM_FRAME_STATUS_PERIOD_EN,uint8);     /* 周期性发送报文 */
        }
        else
        {   /* 21/06/29 参考日产在发现诊断发送请求仍在发送时重新初始化请求信号 */
            if((LcFrameIdx == ID7DAIdx)&&((GcFrameStatus[LcFrameIdx]&COM_FRAME_STATUS_TX_PROGRESS) == COM_FRAME_STATUS_TX_PROGRESS))
            {
                GcFrameStatus[LcFrameIdx] = COM_FRAME_STATUS_TX_REQ;
            }
            else
            {
                GcFrameStatus[LcFrameIdx] = 0U;      /* 每一个接收发送帧的状态初始化 */
            }
        }
        Gu8TxQueueStartFlag[LcFrameIdx] = 0;
    }
    Gu8CycleTime = 0;
    Gu8CycleQueueIdx = 0;
}

/******************************************************************************/
/**<pre>
 *函数名称: COM_Activate
 *功能描述: 激活CAN通讯模块
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void COM_Activate(void)
{
    uint8 LcFrameIdx;
    uint8 LcMailboxIdx;

    for (LcFrameIdx = 0U; LcFrameIdx < RXANDTXFRAMEMAXNUM; LcFrameIdx ++)            /* 初始化报文帧的状态 */
    {
        if((LcFrameIdx == ID7DAIdx)&&(GcFrameStatus[LcFrameIdx] == COM_FRAME_STATUS_TX_REQ))
        {
            continue;
        }
        else
        {
            NCS_RESET_BIT(GcFrameStatus[LcFrameIdx], (COM_FRAME_STATUS_TX_REQ
                                                 | COM_FRAME_STATUS_TX_PROGRESS
                                                 | COM_FRAME_STATUS_TX_DATA), uint8);
        }
    }
    /*mailbox数量需要根据实际情况调整*/
    for (LcMailboxIdx = 0U; LcMailboxIdx < TXMAILBOXNUM; LcMailboxIdx++)
    {
        GcTxMailboxStatus[LcMailboxIdx] = COM_MAILBOX_FREE;
        GcTxFrameIdx[LcMailboxIdx] = 0U;
    }

    for (LcFrameIdx = RXFRAMEMAXNUM; LcFrameIdx < RXANDTXFRAMEMAXNUM; LcFrameIdx++)
    {
        GvCanMessageProcessFlag[LcFrameIdx] = TRUE;
    }
}
/*********************************************************************
* 函数名称: COMSigMgr_RxMonitorInit
*
* 功能描述: 清除节点超时标志
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/
void COMSigMgr_RxMonitorInit(void)
{

    uint8 LcIdx;
    /*清空节点超时故障缓存*/
    for (LcIdx = 0; LcIdx < RXFRAMEMAXNUM; LcIdx++)
    {
        NCS_RESET_BIT(GcFrameStatus[LcIdx], COM_FRAME_STATUS_RX_TIMEOUT, uint8);
        GwComFramePeriodCnt[LcIdx] = 0;
    }
    /*清空节点信号故障缓存*/
    for (LcIdx = RXFRAMEMAXNUM;LcIdx < RXANDTXFRAMEMAXNUM;LcIdx++)
    {
        GstrNodeErrCnt[LcIdx].Signal_InvalidCnt = 0;
        GstrNodeErrCnt[LcIdx].Checksum_InvalidCnt = 0;
        GstrNodeErrCnt[LcIdx].Counter_InvalidCnt = 0;
    }
}

/*********************************************************************
* 函数名称: COMSigMgr_EPSRxMonitorInit
*
* 功能描述: 清除节点超时标志
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/
void COMSigMgr_EPSRxMonitorInit(void)
{
    /*清空节点超时故障缓存*/
    NCS_RESET_BIT(GcFrameStatus[ID176Idx], COM_FRAME_STATUS_RX_TIMEOUT, uint8);
    GwComFramePeriodCnt[ID176Idx] = 0;
    /*清空节点信号故障缓存*/
    GstrNodeErrCnt[ID176Idx].Signal_InvalidCnt = 0;
    GstrNodeErrCnt[ID176Idx].Checksum_InvalidCnt = 0;
    GstrNodeErrCnt[ID176Idx].Counter_InvalidCnt = 0;
}
/******************************************************************************/
/**<pre>
 *函数名称: InitCAN
 *功能描述: 用于busoff后恢复CANIL
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void InitCAN(void)
{
    static uint8 Flag = 0;
    if ((GcBusOffTestResult == TESTFAIL) && (Flag == 0))
    {
        Flag = 1;
        CanIL_Init();
    }
    else if ((GcBusOffTestResult == TESTPASS) && (Flag == 1))
    {
        Flag = 0;
    }

}
/******************************************************************************/
/**<pre>
 *函数名称: COM_TxManage
 *功能描述: 报文发送时机管理
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void COM_TxManage(void)
{
    uint8 LcFrameIdx;

    static uint32 Ld_Last = 0;
    uint32 Ld_Prresent = GetSystemTimeCnt_Ms();
    uint16 Ld_TimeLen = 0;

    if (Ld_Prresent >= Ld_Last)
    {
        Ld_TimeLen = (uint16)(Ld_Prresent - Ld_Last);
    }
    else
    {
        Ld_TimeLen = (uint16)(0xFFFFFFFF + Ld_Prresent - Ld_Last);
    }

    Ld_Last = Ld_Prresent;

    for(LcFrameIdx = RXFRAMEMAXNUM; LcFrameIdx <= ID085Idx;LcFrameIdx++)
    {
        if((NCS_TST_BIT_SET(GcFrameStatus[LcFrameIdx], COM_FRAME_STATUS_PERIOD_EN, uint8)))
        {
            /* 查询周期性报文发送时间到，则使能发送请求 */
            if(GwComFramePeriodCnt[LcFrameIdx] > Ld_TimeLen)
            {
                GwComFramePeriodCnt[LcFrameIdx] -= Ld_TimeLen;;
            }
            else
            {
                /* 对于没有在发送中断中确认的，下一个发送周期到，强制执行清除发送处理中的标志位 */
                if ((NCS_TST_BIT_SET(GcFrameStatus[LcFrameIdx], COM_FRAME_STATUS_TX_PROGRESS, uint8)))
                {
                    NCS_RESET_BIT(GcFrameStatus[LcFrameIdx], (COM_FRAME_STATUS_TX_PROGRESS
                                                 | COM_FRAME_STATUS_TX_DATA), uint8);
                }

                NCS_SET_BIT(GcFrameStatus[LcFrameIdx], COM_FRAME_STATUS_TX_REQ, uint8);
            }
        }
    }


    /* 20ms周期报文队列计时*/
    if (Gu8CycleTime > Ld_TimeLen)
    {
        Gu8CycleTime -= Ld_TimeLen;;
    }
    else/*计时到则切换成队列首帧报文发送*/
    {
        Gu8CycleQueueIdx = ID238Idx;
        Gu8TxQueueStartFlag[ID238Idx] = 1;
        for (LcFrameIdx = ID238Idx; LcFrameIdx < ID394Idx;LcFrameIdx++)
        {
            if ((NCS_TST_BIT_SET(GcFrameStatus[LcFrameIdx], COM_FRAME_STATUS_PERIOD_EN, uint8)))
            {

                /* 对于没有在发送中断中确认的，下一个发送周期到，强制执行清除发送处理中的标志位 */
                if ((NCS_TST_BIT_SET(GcFrameStatus[LcFrameIdx], COM_FRAME_STATUS_TX_PROGRESS, uint8)))
                {
                    NCS_RESET_BIT(GcFrameStatus[LcFrameIdx], (COM_FRAME_STATUS_TX_PROGRESS
                        | COM_FRAME_STATUS_TX_DATA), uint8);
                }

                NCS_SET_BIT(GcFrameStatus[LcFrameIdx], COM_FRAME_STATUS_TX_REQ, uint8);
            }
        }
    }

    for (LcFrameIdx = ID394Idx; LcFrameIdx < RXANDTXFRAMEMAXNUM;LcFrameIdx++)
    {
        if((NCS_TST_BIT_SET(GcFrameStatus[LcFrameIdx], COM_FRAME_STATUS_PERIOD_EN, uint8)))
        {
            /* 查询周期性报文发送时间到，则使能发送请求 */
            if(GwComFramePeriodCnt[LcFrameIdx] > Ld_TimeLen)
            {
                GwComFramePeriodCnt[LcFrameIdx] -= Ld_TimeLen;;
            }
            else
            {
                /* 对于没有在发送中断中确认的，下一个发送周期到，强制执行清除发送处理中的标志位 */
                if ((NCS_TST_BIT_SET(GcFrameStatus[LcFrameIdx], COM_FRAME_STATUS_TX_PROGRESS, uint8)))
                {
                    NCS_RESET_BIT(GcFrameStatus[LcFrameIdx], (COM_FRAME_STATUS_TX_PROGRESS
                                                 | COM_FRAME_STATUS_TX_DATA), uint8);
                }

                NCS_SET_BIT(GcFrameStatus[LcFrameIdx], COM_FRAME_STATUS_TX_REQ, uint8);
            }
        }
    }
}
/******************************************************************************/
/**<pre>
 *函数名称: CAN_SendManage(
 *功能描述: 报文发送
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void CAN_SendManage_Queueleader(void)
{
    uint8 LcTxMailboxIdx = 0u;
    /*Gu8CycleQueueIdx满足发送队列首帧ID则使能发送*/
    if ((Gu8CycleQueueIdx == ID238Idx) && (Gu8TxQueueStartFlag[ID238Idx] == 1))
    {
        if ((NCS_TST_BIT_SET(GcFrameStatus[ID238Idx], COM_FRAME_STATUS_TX_REQ, uint8)) &&        /* 当前有报文发送请求 */
            (NCS_TST_BIT_RESET(GcFrameStatus[ID238Idx], COM_FRAME_STATUS_TX_PROGRESS, uint8)) &&    /* 当前报文发送没有在发送缓冲区中 */
            (NCS_TST_BIT_RESET(GcFrameStatus[ID238Idx], COM_FRAME_STATUS_TX_DISABLE, uint8)))      /* 允许发送 */    /* 允许发送 */
        {
            while ((LcTxMailboxIdx < TXMAILBOXNUM) && (GcTxMailboxStatus[LcTxMailboxIdx] != COM_MAILBOX_FREE))
            {
                LcTxMailboxIdx++;      /* 判断是否有空闲的发送通道 */
            }

            if (LcTxMailboxIdx < TXMAILBOXNUM)        /* 有空闲的发送通道 */
            {
                /* The frame transmission is memorised */
                NCS_SET_BIT(GcFrameStatus[Gu8CycleQueueIdx], COM_FRAME_STATUS_TX_PROGRESS, uint8);    /* 设置当前有报文在发送 */

                /* 21/06/30 填进去报文信息去除发送请求位 */
                NCS_RESET_BIT(GcFrameStatus[Gu8CycleQueueIdx], COM_FRAME_STATUS_TX_REQ, uint8);

                GcTxMailboxStatus[LcTxMailboxIdx] = COM_MAILBOX_BUSY;      /* 设置当前发送通道忙 */

                IL_CanDataInd(Gu8CycleQueueIdx);
                IL_MessageCombine_TX(Gu8CycleQueueIdx);/*发送前立即调数据填充*/
                COM_TxFrame(LcTxMailboxIdx, Gu8CycleQueueIdx);                    /* 发送报文 */
                Gu8TxQueueStartFlag[ID238Idx] = 0;/*首帧发完将flag清0；确保不重复发送首帧*/
                /*队首发送时，重启报文周期计时，此处忽略20ms内未发送完成队尾报文*/
                Gu8CycleTime = GwComFramePeriod[Gu8CycleQueueIdx];/* 使能发送报文后重启周期发送定时 */
            }
            /*break;*/
            return;
        }
    }
    else
    {
        /*发送时基没到，不发送*/
    }
}
void CAN_SendManage(void)
{
    uint8 LcFrameIdx;
    uint8 LcTxMailboxIdx = 0u;

    if (NCS_TST_BIT_RESET(GcCANBusState, CAN_BUS_OFF, uint8))//&&((ReadPwrMonitorSignal_ADMomentaryValue() >= AD_CALIBRATION_7V))) //调用函数之前已经判断过电压
    {  /* 没有busoff 的情况下且电源正常才发送数据 */
        for (LcFrameIdx = RXFRAMEMAXNUM; LcFrameIdx <= ID085Idx;LcFrameIdx++)
        {
            if ((NCS_TST_BIT_SET(GcFrameStatus[LcFrameIdx], COM_FRAME_STATUS_TX_REQ, uint8)) &&        /* 当前有报文发送请求 */
                (NCS_TST_BIT_RESET(GcFrameStatus[LcFrameIdx], COM_FRAME_STATUS_TX_PROGRESS, uint8)) &&    /* 当前报文发送没有在发送缓冲区中 */
                (NCS_TST_BIT_RESET(GcFrameStatus[LcFrameIdx], COM_FRAME_STATUS_TX_DISABLE, uint8)))      /* 允许发送 */    /* 允许发送 */
            {
                while ((LcTxMailboxIdx < TXMAILBOXNUM) && (GcTxMailboxStatus[LcTxMailboxIdx] != COM_MAILBOX_FREE))
                {
                    LcTxMailboxIdx++;      /* 判断是否有空闲的发送通道 */
                }

                if (LcTxMailboxIdx < TXMAILBOXNUM)        /* 有空闲的发送通道 */
                {
                    /* The frame transmission is memorised */
                    NCS_SET_BIT(GcFrameStatus[LcFrameIdx], COM_FRAME_STATUS_TX_PROGRESS, uint8);    /* 设置当前有报文在发送 */

                    /* 21/06/30 填进去报文信息去除发送请求位 */
                    NCS_RESET_BIT(GcFrameStatus[LcFrameIdx], COM_FRAME_STATUS_TX_REQ, uint8);

                    GcTxMailboxStatus[LcTxMailboxIdx] = COM_MAILBOX_BUSY;      /* 设置当前发送通道忙 */

                    IL_CanDataInd(LcFrameIdx);
                    IL_MessageCombine_TX(LcFrameIdx);/*发送前立即调数据填充*/
                    COM_TxFrame(LcTxMailboxIdx, LcFrameIdx);                    /* 发送报文 */

                    GwComFramePeriodCnt[LcFrameIdx] = GwComFramePeriod[LcFrameIdx];/* 使能发送报文后重启周期发送定时 */
                    /*break;*/
                    return;
                }
            }
        }
        //Send the frist Frame of queue; 0x238
        CAN_SendManage_Queueleader();

        for (LcFrameIdx = ID394Idx; LcFrameIdx < RXANDTXFRAMEMAXNUM;LcFrameIdx++)
        {
            if ((NCS_TST_BIT_SET(GcFrameStatus[LcFrameIdx], COM_FRAME_STATUS_TX_REQ, uint8)) &&        /* 当前有报文发送请求 */
                (NCS_TST_BIT_RESET(GcFrameStatus[LcFrameIdx], COM_FRAME_STATUS_TX_PROGRESS, uint8)) &&    /* 当前报文发送没有在发送缓冲区中 */
                (NCS_TST_BIT_RESET(GcFrameStatus[LcFrameIdx], COM_FRAME_STATUS_TX_DISABLE, uint8)))      /* 允许发送 */    /* 允许发送 */
            {
                while ((LcTxMailboxIdx < TXMAILBOXNUM) && (GcTxMailboxStatus[LcTxMailboxIdx] != COM_MAILBOX_FREE))
                {
                    LcTxMailboxIdx++;      /* 判断是否有空闲的发送通道 */
                }

                if (LcTxMailboxIdx < TXMAILBOXNUM)        /* 有空闲的发送通道 */
                {
                    /* The frame transmission is memorised */
                    NCS_SET_BIT(GcFrameStatus[LcFrameIdx], COM_FRAME_STATUS_TX_PROGRESS, uint8);    /* 设置当前有报文在发送 */

                    /* 21/06/30 填进去报文信息去除发送请求位 */
                    NCS_RESET_BIT(GcFrameStatus[LcFrameIdx], COM_FRAME_STATUS_TX_REQ, uint8);

                    GcTxMailboxStatus[LcTxMailboxIdx] = COM_MAILBOX_BUSY;      /* 设置当前发送通道忙 */

                    IL_CanDataInd(LcFrameIdx);
                    IL_MessageCombine_TX(LcFrameIdx);/*发送前立即调数据填充*/
                    COM_TxFrame(LcTxMailboxIdx, LcFrameIdx);                    /* 发送报文 */

                    GwComFramePeriodCnt[LcFrameIdx] = GwComFramePeriod[LcFrameIdx];/* 使能发送报文后重启周期发送定时 */
                    return;
                }
            }

        }

    }
}
/******************************************************************************/
/**<pre>
 *函数名称: CAN_SendManage_Queue
 *功能描述: 队列报文发送
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void CAN_SendManage_Queue(void)/*除首帧之外的，队列报文发送*/
{
    uint8 LcTxMailboxIdx = 0u;
    uint8 Mbi = 0;

    if (NCS_TST_BIT_RESET(GcCANBusState, CAN_BUS_OFF, uint8)&&(ReadPwrMonitorSignal_ADMomentaryValue() >= AD_CALIBRATION_7V))/*Busoff及电压不满足不发送*/
    {  /* 没有busoff 的情况下且电源正常才发送数据 */
        /*Gu8CycleQueueIdx满足发送队列ID且使能标志置起才使能发送*/
        if ((Gu8CycleQueueIdx > ID238Idx) && (Gu8CycleQueueIdx < ID394Idx)&&(Gu8TxQueueStartFlag[Gu8CycleQueueIdx]==1))
        {
            if ((NCS_TST_BIT_SET(GcFrameStatus[Gu8CycleQueueIdx], COM_FRAME_STATUS_TX_REQ, uint8)) &&        /* 当前有报文发送请求 */
                (NCS_TST_BIT_RESET(GcFrameStatus[Gu8CycleQueueIdx], COM_FRAME_STATUS_TX_PROGRESS, uint8)) &&    /* 当前报文发送没有在发送缓冲区中 */
                (NCS_TST_BIT_RESET(GcFrameStatus[Gu8CycleQueueIdx], COM_FRAME_STATUS_TX_DISABLE, uint8)))      /* 允许发送 */    /* 允许发送 */
            {
                while ((LcTxMailboxIdx < TXMAILBOXNUM) && (GcTxMailboxStatus[LcTxMailboxIdx] != COM_MAILBOX_FREE))
                {
                    LcTxMailboxIdx++;      /* 判断是否有空闲的发送通道 */
                }

                if (LcTxMailboxIdx < TXMAILBOXNUM)        /* 有空闲的发送通道 */
                {
                    Mbi++;
                    /* The frame transmission is memorised */
                    NCS_SET_BIT(GcFrameStatus[Gu8CycleQueueIdx], COM_FRAME_STATUS_TX_PROGRESS, uint8);    /* 设置当前有报文在发送 */

                    /* 21/06/30 填进去报文信息去除发送请求位 */
                    NCS_RESET_BIT(GcFrameStatus[Gu8CycleQueueIdx], COM_FRAME_STATUS_TX_REQ, uint8);

                    GcTxMailboxStatus[LcTxMailboxIdx] = COM_MAILBOX_BUSY;      /* 设置当前发送通道忙 */

                    IL_CanDataInd(Gu8CycleQueueIdx);
                    IL_MessageCombine_TX(Gu8CycleQueueIdx);/*发送前立即调数据填充*/
                    COM_TxFrame(LcTxMailboxIdx, Gu8CycleQueueIdx);                    /* 发送报文 */
                    Gu8TxQueueStartFlag[Gu8CycleQueueIdx] = 0;/*数据填充到邮箱后，立马清除标志，避免重复发送*/
                }
            }
        }
        else
        {
            /*发送时基没到，不发送*/
        }
    }
}
/*********************************************************************
* 函数名称: COM_SendDebugFrame
*
* 功能描述: 设置CAN模块发送CAN调试报文
*
* 输入参数: Lu16FrameID:调试报文ID   Lu8DLC:调试报文DLC LptrDebugMsg 调试报文内容
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/
//void COM_SendDebugFrame(uint16 Lu16FrameID, uint8 Lu8DLC, uint8* LptrDebugMsg, uint8 DebugIdx)
//{
//    uint8 Lu8TxMailboxIdx = 0u;
//
//    Gu8DebugMsgDLC[DebugIdx] = Lu8DLC;
//    Gu16DebugMsgID[DebugIdx] = Lu16FrameID;
//    Gu8Debug_Massage_Sts[DebugIdx] = 1;
//}

/*********************************************************************
* 函数名称:  CANBusOffProcess
*
* 功能描述:  CAN总线Busoff处理
*
* 入口参数:  无
*
* 输出参数:  无
*
* 其它说明:  无
*
* 修改日期		版本号		修改人		修改内容
*
********************************************************************/
void CANBusOffProcess(void)
{
    /*BUSOFF处理，取消当前CAN帧发送；停止CAN模块，初始化CAN模块，初始化COM层，激活COM层*/
    CAN_BusOffInd();

    NCS_SET_BIT(GcCANBusState,CAN_BUS_OFF,uint8);

    Gu8BusoffRecoverCnt = 0u;/*产生一次busoff就将之前成功发送次数清0*/

    if(GcBusoffContinueCnt < 0xffu)   /* 连续Busoff 中断产生计数 */
    {
        GcBusoffContinueCnt++;
    }

    if(GcBusoffContinueCnt <= BUSOFF_ERROR_LIMIT)/*5次快恢复之后为慢恢复*/
    {
        GwBusoffRecoverTimer = BUSOFF_QUICK_RECOVER_TIME;            /* Busoff尝试恢复时间,快恢复100ms 快恢复5次都没有恢复记录故障码*/
    }
    else
    {
        GwBusoffRecoverTimer = BUSOFF_SLOW_RECOVER_TIME;            /* Busoff尝试恢复时间,慢恢复每次1000ms */

    }

    if (GcBusoffContinueCnt >= BUSOFF_ERROR_LIMIT)/*连续Busoff 5次记录DTC*/
    {
        GcBusOffTestResult = TESTFAIL;
    }
}
/*********************************************************************
 * 函数名称:  BusOffRecoverMonitor
 *                                  
 * 功能描述:  总线关闭恢复检测
 *                  
 * 入口参数:  无
 *
 * 输出参数:  无
 *          
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 * 
 ********************************************************************/
void BusOffRecoverMonitor(void)
{
    static uint32 Ld_Last = 0;
    uint32 Ld_Prresent = GetSystemTimeCnt_Ms();
    uint16 Ld_TimeLen = 0;

    if (Ld_Prresent >= Ld_Last)
    {
        Ld_TimeLen = (uint16)(Ld_Prresent - Ld_Last);
    }
    else
    {
        Ld_TimeLen = (uint16)(0xFFFFFFFF + Ld_Prresent - Ld_Last);
    }

    Ld_Last = Ld_Prresent;

    if (GwBusoffRecoverTimer > Ld_TimeLen)
    {
        GwBusoffRecoverTimer -= Ld_TimeLen;
    }
    else
    {
        GwBusoffRecoverTimer = 0;
        NCS_RESET_BIT(GcCANBusState, CAN_BUS_OFF, uint8);
    }
    /* 成功发送一帧报文，则认为总线恢复正常，清除DTC错误 */
    if ((Gu8BusoffRecoverCnt >= 1))
    {
        GcBusOffTestResult = TESTPASS;
        GcBusoffContinueCnt = 0;
    }
} 

/******************************************************************************/
/**<pre>
 *函数名称: COM_RxMonitor
 *功能描述: 报文接收监控，主要用于周期性报文接收超时的判断
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void COM_RxMonitor(void)
{
    uint8 LcFrameIdx;
    static uint32 Ld_Last = 0;
    uint32 Ld_Prresent = GetSystemTimeCnt_Ms();
    uint16 Ld_TimeLen = 0;

    if (Ld_Prresent >= Ld_Last)
    {
        Ld_TimeLen = (uint16)(Ld_Prresent - Ld_Last);
    }
    else
    {
        Ld_TimeLen = (uint16)(0xFFFFFFFF + Ld_Prresent - Ld_Last);
    }

    Ld_Last = Ld_Prresent;
    if(NCS_TST_BIT_RESET(GcCANBusState,CAN_BUS_OFF,uint8))
    {/*CAN总线状态正常，才接收并监控消息状态*/

        for(LcFrameIdx = 0u;LcFrameIdx < RXFRAMEMAXNUM;LcFrameIdx++)
        {
            //if(CONFIGURED == GwComFrameCfgSts[LcFrameIdx])
            {
                /* 非周期性报文，发送状态非禁止情况下判断周期性报文是否超时 */
                if((GwComFramePeriod[LcFrameIdx] != 0u)&&(NCS_TST_BIT_RESET(GcFrameStatus[LcFrameIdx], COM_FRAME_STATUS_RX_DISABLE, uint8))
                    &&(NCS_TST_BIT_RESET(Gu8FrameNMCtrlStatus[LcFrameIdx], COM_FRAME_NMCTRL_STATUS_RX_DISABLE, uint8))) /* 周期性报文,没有禁止接收 */
                {
                    /* 周期性报文监控其超时周期,超时周期根据诊断调查表定义的周期 */
                    if(GwComFramePeriodCnt[LcFrameIdx] < GwComFrameTimeout[LcFrameIdx])
                    {
                        GwComFramePeriodCnt[LcFrameIdx]+= Ld_TimeLen;;
                    }
                    else/*周期报文超时*/
                    {
                        NCS_SET_BIT(GcFrameStatus[LcFrameIdx],COM_FRAME_STATUS_RX_TIMEOUT,uint8);
                        NCS_RESET_BIT(GcFrameStatus[LcFrameIdx],COM_FRAME_STATUS_RX_OK,uint8);
                        GwContinueRxCnt[LcFrameIdx] = 0u;      /* 监控到超时则恢复计时清零 */
                    }
                    /* CAN 接收中断中置位接收完成标志位 */
                    if(NCS_TST_BIT_SET(GcFrameStatus[LcFrameIdx],COM_FRAME_STATUS_RX_FLAG,uint8))
                    {
                        NCS_RESET_BIT(GcFrameStatus[LcFrameIdx],COM_FRAME_STATUS_RX_FLAG,uint8);
                        GwComFramePeriodCnt[LcFrameIdx] = 0u;    /* 接收到正常报文将超时计数清零 */
                    }
                    /* 监控报文恢复,恢复时间根据诊断调查表定义的周期 */
                    if(GwContinueRxCnt[LcFrameIdx] < (5 * GwComFramePeriod[LcFrameIdx]))
                    {
                        GwContinueRxCnt[LcFrameIdx] += Ld_TimeLen;
                    }
                    else
                    {
                        NCS_SET_BIT(GcFrameStatus[LcFrameIdx],COM_FRAME_STATUS_RX_OK,uint8);
                        NCS_RESET_BIT(GcFrameStatus[LcFrameIdx],COM_FRAME_STATUS_RX_TIMEOUT,uint8);
                    }
                }
            }

        }

    }
}


/*********************************************************************
* 函数名称: COM_NMAndAPPMsgControl
*
* 功能描述: CAN通信层网络管理报文和应用报文管理
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/
void COM_NMAndAPPMsgControl_RX(uint8 LcControlType, uint8 LcMsgType)
{
    uint8 LcFrameIdx;

    for (LcFrameIdx = 0;LcFrameIdx < RXFRAMEMAXNUM;LcFrameIdx++)
    {
        if ((LcControlType == ENABLE_RX_TX) || (LcControlType == ENABLE_RX_DISABLE_TX))      /* 使能接收 */
        {
            if (LcMsgType == APP_MSG)                /* 应用报文 */
            {
                if (GcComFrameType[LcFrameIdx] == APP_MSG_RX)
                {
                    NCS_RESET_BIT(GcFrameStatus[LcFrameIdx], COM_FRAME_STATUS_RX_DISABLE, uint8);
                }
            }
            else        /* 应用和网络管理报文 */
            {
                if ((GcComFrameType[LcFrameIdx] == APP_MSG_RX) || (GcComFrameType[LcFrameIdx] == NM_MSG_RX))
                {
                    NCS_RESET_BIT(GcFrameStatus[LcFrameIdx], COM_FRAME_STATUS_RX_DISABLE, uint8);
                }
            }

        }
        else if ((LcControlType == DISABLE_RX_ENABLE_TX) || (LcControlType == DISABLE_RX_TX))      /* 禁止接收 */
        {
            if (LcMsgType == APP_MSG)                /* 应用报文 */
            {
                if (GcComFrameType[LcFrameIdx] == APP_MSG_RX)
                {
                    NCS_SET_BIT(GcFrameStatus[LcFrameIdx], COM_FRAME_STATUS_RX_DISABLE, uint8);
                }
            }
            else
            {
                if ((GcComFrameType[LcFrameIdx] == APP_MSG_RX) || (GcComFrameType[LcFrameIdx] == NM_MSG_RX))
                {
                    NCS_SET_BIT(GcFrameStatus[LcFrameIdx], COM_FRAME_STATUS_RX_DISABLE, uint8);
                }
            }
        }
        else
        {
            ;
        }
    }
}

void COM_NMAndAPPMsgControl_TX(uint8 LcControlType, uint8 LcMsgType)
{
    uint8 LcFrameIdx;

    for (LcFrameIdx = RXFRAMEMAXNUM;LcFrameIdx < RXANDTXFRAMEMAXNUM;LcFrameIdx++)
    {
        if ((LcControlType == ENABLE_RX_TX) || (LcControlType == DISABLE_RX_ENABLE_TX))      /* 使能发送 */
        {
            if (LcMsgType == APP_MSG)                /* 应用报文 */
            {
                if (GcComFrameType[LcFrameIdx] == APP_MSG_TX)
                {
                    NCS_RESET_BIT(GcFrameStatus[LcFrameIdx], COM_FRAME_STATUS_TX_DISABLE, uint8);
                }
            }
            else
            {
                if ((GcComFrameType[LcFrameIdx] == APP_MSG_TX) || (GcComFrameType[LcFrameIdx] == NM_MSG_TX))
                {
                    NCS_RESET_BIT(GcFrameStatus[LcFrameIdx], COM_FRAME_STATUS_TX_DISABLE, uint8);
                }
            }
        }
        else if ((LcControlType == ENABLE_RX_DISABLE_TX) || (LcControlType == DISABLE_RX_TX))    /* 禁止发送 */
        {
            if (LcMsgType == APP_MSG)                /* 应用报文 */
            {
                if (GcComFrameType[LcFrameIdx] == APP_MSG_TX)
                {
                    NCS_SET_BIT(GcFrameStatus[LcFrameIdx], COM_FRAME_STATUS_TX_DISABLE, uint8);
                }
            }
            else
            {
                if ((GcComFrameType[LcFrameIdx] == APP_MSG_TX) || (GcComFrameType[LcFrameIdx] == NM_MSG_TX))
                {
                    NCS_SET_BIT(GcFrameStatus[LcFrameIdx], COM_FRAME_STATUS_TX_DISABLE, uint8);
                }
            }
        }
        else
        {
            ;
        }
    }
}

/******************************************************************************/
/**<pre>
 *函数名称: COM_TxFrame
 *功能描述: 设置CAN模块发送CAN报文 需要调用CAN Driver提供的发送函数接口，将Data ID DLC等填充到寄存器并请求发送
 *输入参数: LcMailboxIdx:发送缓冲区索引号   LcFrameIdx:CAN报文索引号，对应配置文件的定义顺序
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
static void COM_TxFrame(uint8 LcMailboxIdx, uint8 LcFrameIdx)
{
    NCS_SET_BIT(GcFrameStatus[LcFrameIdx],COM_FRAME_STATUS_TX_DATA, uint8); /* 记住当前报文为发送状态 */

    GcTxFrameIdx[LcMailboxIdx] = LcFrameIdx;       /* 记住当前报文为发送状态， 此处是将邮箱的记录和帧记录相匹配 */

    CANTransmitFrame(Gu8CANTXBufferIdx[LcMailboxIdx], &GcComDataBuffer[LcFrameIdx][0], GcComFrameDLC[LcFrameIdx], GcComFrameIdent[LcFrameIdx]);
}

/******************************************************************************/
/**<pre>
 *函数名称: CAN_BusOffInd
 *功能描述: 发生BusOff处理
 *输入参数: 
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
static void CAN_BusOffInd(void)
{
    CANDrvInit();
    COM_Init();
    COM_Activate();
}

/******************************************************************************/
/**<pre>
 *函数名称: COM_CancelDiagTx
 *功能描述: 取消诊断发送
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void COM_CancelDiagTx(void)
{
    /* 已经发出去的或者在下一帧发出去的都不能取消发送,因此直接初始化 */
    CANDrvReset();
    /* 取消诊断报文应答的状态 */
    GcFrameStatus[ID7DAIdx] = 0u;
    COM_Activate();
}
/*********************************************************************
* 函数名称: COMSigMgr_InitSYNCMomiter
*
* 功能描述: 清除时间同步相关故障记录条件
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/
void COMSigMgr_InitSYNCMomiter(void)
{
    ADAS_SYNCTime_FailedCnt = 0;
    ADAS_SYNCTime_PassCnt = 0;
}

/******************************************************************************/
/**<pre>
 *函数名称: COM_RxMonitorInit
 *功能描述: COM层监控初始化
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void COM_RxMonitorInit(void)
{
    uint8 LcFrameIdx;

    for(LcFrameIdx = 0U; LcFrameIdx < RXFRAMEMAXNUM; LcFrameIdx ++)
    {
        GwComFramePeriodCnt[LcFrameIdx] = 0u;
    }
}

/******************************************************************************/
/**<pre>
 *函数名称: CAN4ErrIntCallBack
 *功能描述: CAN4错误中断处理函数
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
 *其它说明:
*********************************************************************** </pre>*/
void CAN4ErrIntCallBack(void)
{
    volatile CANErrType *LpErr;
    LpErr = (volatile CANErrType *)&(RSCFD0CFDC4ERFL);
    if(LpErr->AckErr)
    {
    
    }
    
    if(LpErr->BusOffErr)
    {
        CANBusOffProcess();
    }
    else if(LpErr->BusRecovery)
    {
    
    }
    else{}
}

/******************************************************************************/
/**<pre>
 *函数名称: CAN4TxIntCallBack
 *功能描述: CAN4发送完成中断处理函数
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
 *其它说明: 每发送成功一帧报文，则触发一次中断
*********************************************************************** </pre>*/
void CAN4TxIntCallBack(void)
{
    uint8 LcTxFrameIdx;

    uint8 Lu8MailBoxIdx = CANGetTMCIdx();

    Lu8MailBoxIdx = Lu8MailBoxIdx / 3;/*因为目前为CANFD模式，返回的邮箱id号为0,3,6,9，12,15，需要做一次转换，转换成0~5的连续ID*/
    if (Lu8MailBoxIdx < TXMAILBOXNUM)       /*形参检查*/
    {
        if (GcTxMailboxStatus[Lu8MailBoxIdx] == COM_MAILBOX_BUSY)   /* 判断是否是之前请求的发送 */
        {
            LcTxFrameIdx = GcTxFrameIdx[Lu8MailBoxIdx];    /* 将邮箱对应的帧ID转换过来 */
            if (LcTxFrameIdx == Gu8CycleQueueIdx)/*确认发送成功报文为队列报文，指向下一个报文*/
            {
                Gu8CycleQueueIdx++;/*若是队尾报文发送成功，指向下一个无效队列索引，此处没有风险，在发送时会对报文索引做判断，判断不过则不会启动发送*/
                Gu8TxQueueStartFlag[Gu8CycleQueueIdx] = 1;/*将允许填充发送数据*/

            }
            GcTxMailboxStatus[Lu8MailBoxIdx] = COM_MAILBOX_FREE;/*将该邮箱状态切换为空闲状态*/

            /* 21/06/30 只复位发送状态和发送过程中 */
            NCS_RESET_BIT(GcFrameStatus[LcTxFrameIdx], (COM_FRAME_STATUS_TX_PROGRESS
                | COM_FRAME_STATUS_TX_DATA), uint8);

            NCS_SET_BIT(GcFrameStatus[LcTxFrameIdx], COM_FRAME_STATUS_TX_CONF, uint8);
            if (Gu8BusoffRecoverCnt < 5u)/*连续发送成功计数，用于busoff恢复条件*/
            {
                Gu8BusoffRecoverCnt++;
            }
        }
        CAN_SendManage_Queue();/*判断队列报文是否需要发送*/
    }
    else
    {

    }
}

/******************************************************************************/
/**<pre>
 *函数名称: CANRxIntCallBack
 *功能描述: 处理接收到的CAN总线报文，并设置相应的标志位
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
 *其它说明: 接收中断调用，将接收的寄存器数据信息存储到相应的COM层buff中去 GcComDataBuffer
*********************************************************************** </pre>*/
void CANRxIntCallBack(void)
{
    uint16 Lu16RxIdx = 0u;
    uint8 Sts;
    uint8 Lu8Cnt=0;
    do
    {
        Lu16RxIdx = CANGetRMCIdx();//RSCFD0CFDRFFDSTS0L
        if (Lu16RxIdx < RXFRAMEMAXNUM)
        {
            if ((NCS_TST_BIT_RESET(GcFrameStatus[Lu16RxIdx], COM_FRAME_STATUS_RX_DISABLE, uint8)) &&
                (NCS_TST_BIT_RESET(Gu8FrameNMCtrlStatus[Lu16RxIdx], COM_FRAME_NMCTRL_STATUS_RX_DISABLE, uint8)))   /* Determine whether reception is disabled */
            {
                Sts = CANReceiveFrame(GcComDataBuffer[Lu16RxIdx], GcComFrameDLC[Lu16RxIdx], (uint8)Lu16RxIdx);
                if (Sts == E_OK)
                {
                    if (ID5A4Idx == Lu16RxIdx)/* Get the timestamp of the currently received time synchronisation message */
                    {
                        Gu32CANTimeSysn_RxInt = GetSystemTimeCnt_Ms();
                    }
#if(DEBUG_FRAME_TX_EN==STD_ON)
                    else if (ID6F0Idx == Lu16RxIdx)
                    {
                        Com_RxCvt_AK2Upper_Pc2Mcu_6F0_ecu0_0();
                        /*Other messages, no additional processing required*/
                    }
#endif // 



                    IL_CanDataInd(Lu16RxIdx);     /* Enable corresponding CAN message reception completion flag */

                    NCS_SET_BIT(GcFrameStatus[Lu16RxIdx], COM_FRAME_STATUS_RX_FLAG, uint8);
                }
                else
                {
                    /* not handled in case of bottom register exceptions */
                }
            }
            Lu8Cnt++;/*Receive processing counts cumulative*/
        }
        /*Increase the processing exception exit mechanism : 
        when the number of processing times exceeds 3 times, 
        exitand wait for the next interrupt to come in for processing; 
        1. Avoid processing too many messages at once, resulting in the interrupt occupying too long;
        2. Avoid the underlying register failure; fall into a dead cycle;*/
    } while ((CAN_RX_BUF_NOT_NULL == CANGetRxBufStatus()) && (Lu8Cnt < 3u));
}