/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: CANDrv.c
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/
#include "CANDrv.h"
#include "CANCfg.h"
#include "IODrv.h"
#include "CAN_COM.h"
/**********************************变量定义************************************/
static Canfd_FrameType GstrCANTxFrame;
/********************************函数声明定义**********************************/
static void CANSetTxRxPin(void);
static void CANBusInit(void);
static void CANSetRxRule(void);
static void CANSetTxRxBuffer(void);
static void CANInterruptSetting(void);
static void CANGlobalStart(void);

/******************************************************************************/
/**<pre>
 *函数名称: CANSetTxRxPin
 *功能描述: 设置CANTxRx端口
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
static void CANSetTxRxPin(void)
{

    //R_PORT_SetAltFunc(Port1, 2, Alt1, Input);   /* CAN3 RX */
    //R_PORT_SetAltFunc(Port1, 3, Alt1, Output);   /* CAN3 TX*/
    R_PORT_SetAltFunc(Port0, 9, Alt5, Input);  /* CAN4 RX*/
    R_PORT_SetAltFunc(Port0, 10, Alt5, Output);  /* CAN4 TX */
    
    /* Enable Transceiver CAN0 */
    //R_PORT_SetGpioOutput(Port1, 1, High);
    
    /* Enable Transceiver CAN1 */
    //R_PORT_SetGpioOutput(Port2, 6, High);
}


/******************************************************************************/
/**<pre>
 *函数名称: CANBusInit
 *功能描述: CAN总线相关配置,设置波特率、通讯模式经典CAN等
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
static void CANBusInit(void)
{
    //RSCFD0CFDGCTR = 0x00000005;
    //RSCFD0CFDGAFLCFG0 = 0x00000000;
    /**<pre> 检测CAN模块RAM初始化结束与否 </pre>*/
    while((RSCFD0CFDGSTS & 0x00000008ul))
    {
        NOP();
    }

    /**<pre> 切换CAN模块的运行模式,从全局停止模式切换到全局复位模式 </pre>*/
    RSCFD0CFDGCTR&= 0xFFFFFFFBul;
    RSCFD0CFDGCTR |= 0x00000001ul;
    while(RSCFD0CFDGCTR != 0x00000001ul)
    {
        NOP();
    }

    /**<pre> 切换通道运行模式,从通道停止模式切换到通道复位模式 </pre>*/
    RSCFD0CFDCmCTR(CAN_CHANNEL_USE) &= 0xFFFFFFFBul;
    /**<pre> 清除通道模式选择位,下一项配置模式 </pre>*/
    //RSCFD0CFDCmFDCFG(CAN_CHANNEL_USE) &= ~(0x50000000);
    RSCFD0CFDGRMCFG = 0x01;	//set to CAN FD mode
    /*ID priority,DLC check is disabled,clkc as CAN clock--PPLLCLK2, overflow stored, */
    RSCFD0CFDGCFG = 0x00009020;   //0B 0001 0000 0010 0000

    /**<pre> 选择clkc  --PPLLCLK2 作为时钟源, clkc=PPLLCLK2=40M   </pre>*/
    /**<pre> DLC 替代使能, DLC 检测使能 </pre>*/
    //    RSCFD0CFDGCFG = 0x00000006ul;
    /**<pre> 设置CAN波特率 </pre>*/
    RSCFD0CFDCmNCFG(CAN_CHANNEL_USE) = _CFG_RCFDCnCFDCmNCFG;
    RSCFD0CFDCmDCFG(CAN_CHANNEL_USE) = _CFG_RCFDCnCFDCmDCFG;


    CANSetRxRule();
    CANSetTxRxBuffer();
    RSCFD0CFDC4FDCFG |= 0x080E0200;//Transmit buffer merge mode is enabled
    /* Set CMPOFIE,THLEIE,MEIE,DEIE disabled*/
    RSCFD0CFDGCTR &= 0xfffff0ff;/*后面考虑是否打开*/

    /* If GlobalChannel in halt or reset mode */
    if (RSCFD0CFDGSTS & 0x03)
    {
        RSCFD0CFDGCTR &= 0xfffffffc; //Switch to communication mode
        while ((RSCFD0CFDGSTS & 0x02) == 2) {
            /* While halt mode */
        }
        while ((RSCFD0CFDGSTS & 0x01) == 1) {
            /* While reset mode */
        }
    }
}

/******************************************************************************/
/**<pre>
 *函数名称: CANSetRxRule
 *功能描述: 设置所有CAN接收规则
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
static void CANSetRxRule(void)
{
    uint16 LuRxRuleIdx;
    uint8 Lu8PageRxRuleIdx;
    volatile CANFDRxRuleType* LpstrReg;
    /**<pre> 设置CAN通道的接收规则总数 </pre>*/
    RSCFD0CFDGAFLCFG1 |= ((uint32)RXFRAMEMAXNUM << 24u);

    /**<pre> 使能编写接收规则列表 </pre>*/
    RSCFD0CFDGAFLECTR = 0x00000100ul;

    /**<pre> 配置每个接收规则 </pre>*/
    LpstrReg = (volatile CANFDRxRuleType *)&(RSCFD0CFDGAFLID0);
    for(LuRxRuleIdx = 0u; LuRxRuleIdx < RXFRAMEMAXNUM; LuRxRuleIdx++)
    {
        Lu8PageRxRuleIdx = (uint8)(LuRxRuleIdx & 0x0Fu);
        /**<pre>  IF (接收规则下标%16) == 0 </pre>*/
        if(Lu8PageRxRuleIdx == 0x0u)
        {
            /**<pre>   更换下一页(每页接收规则为16条) </pre>*/
            RSCFD0CFDGAFLECTR = 0x00000100ul|((uint32)LuRxRuleIdx >> 4u);
        }

        /**<pre>  设置一个接收规则 </pre>*/
        LpstrReg[Lu8PageRxRuleIdx].bit.GAFLID = GcComFrameIdent[LuRxRuleIdx];
        LpstrReg[Lu8PageRxRuleIdx].bit.GAFLIDE = GcComFrameTypeCfg[LuRxRuleIdx]; /* Standard ID or Extended ID */
        LpstrReg[Lu8PageRxRuleIdx].bit.GAFLRTR = 0ul; /* Data frame */
        LpstrReg[Lu8PageRxRuleIdx].bit.GAFLLB = 0ul; /* When a message transmitted from another CAN node is received */

        LpstrReg[Lu8PageRxRuleIdx].bit.GAFLIDM = GcComRegisterID[LuRxRuleIdx];
        LpstrReg[Lu8PageRxRuleIdx].bit.GAFLRTRM = 1ul;/* The RTR bit is compared. */
        LpstrReg[Lu8PageRxRuleIdx].bit.GAFLIDEM = 1ul;/* The IDE bit is compared. */

        LpstrReg[Lu8PageRxRuleIdx].bit.GAFLDLC = GcComFrameDLC[LuRxRuleIdx];
        LpstrReg[Lu8PageRxRuleIdx].bit.GAFLRMDP = 0ul;
        LpstrReg[Lu8PageRxRuleIdx].bit.GAFLRMV = 0ul; /* No receive buffer is used */
        LpstrReg[Lu8PageRxRuleIdx].bit.GAFLPTR = LuRxRuleIdx;

        LpstrReg[Lu8PageRxRuleIdx].bit.GAFLFDPx = 1u; /* target receive FIFO buffer number x = 0 */
    }

    /**<pre> 禁止编写接收规则列表 </pre>*/
    RSCFD0CFDGAFLECTR = 0x0ul;
}

/******************************************************************************/
/**<pre>
 *函数名称: CANSetTxRxBuffer
 *功能描述: 设置CAN接收缓存区
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
static void CANSetTxRxBuffer(void)
{
    /**<pre> 设置接收缓存数量 </pre>*/
    RSCFD0CFDRMNB = 0xFFul&CANCFG_RBNUM;

    /**<pre> 设置接收FIFO缓存 缓存深度128 数据长度64 FIFO中断触发帧数1 </pre>*/
    RSCFD0CFDRFCC0 = 0x00001773ul;
    RSCFD0CFDRFCC1 = 0x00001773ul;
    RSCFD0CFDRFCC2 = 0x00001773;
    RSCFD0CFDRFCC3 = 0x00001773;
    
    RSCFD0CFDRFCC4 = 0x00001773;
    RSCFD0CFDRFCC5 = 0x00001773;
    RSCFD0CFDRFCC6 = 0x00001773;
    RSCFD0CFDRFCC7 = 0x00001773;
    /**<pre> 关闭发送FIFO缓存 </pre>*/
    SET_TX_FIFO_BUFFER_EN(CAN_CHANNEL_USE, FALSE);

    ///* Set Tx buffer */
    ///* Enable Tx buffer */
    SET_TXBUFFER_INT_ALL_EN(CAN_CHANNEL_USE);

    ///**<pre> 关闭发送队列 </pre>*/
    RSCFD0CFDTXQCCm(CAN_CHANNEL_USE) = 0x0ul;

}

/******************************************************************************/
/**<pre>
 *函数名称: CANInterruptSetting
 *功能描述: 设置CAN中断
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
static void CANInterruptSetting(void)
{
    /* Set CMPOFIE,THLEIE,MEIE,DEIE disabled */
    RSCFD0CFDGCTR &= 0x00000006ul;

    /**<pre> 配置通道功能 </pre>*/
    RSCFD0CFDCmCTR(CAN_CHANNEL_USE) &= 0x00000006ul;
    RSCFD0CFDCmCTR(CAN_CHANNEL_USE) |= CANCFG_CH_CTR;

    /**<pre> 设置关闭CAN全局错误中断 </pre>*/
     INTC1MKRCANGERR0 = _INT_PROCESSING_ENABLED;  /* 关闭中断 */
     INTC1RFRCANGERR0 = _INT_REQUEST_NOT_OCCUR;
     INTC1TBRCANGERR0 = _INT_TABLE_VECTOR;
     INTC1ICRCANGERR0 &= _INT_PRIORITY_LEVEL3;
    // /**<pre> 设置打开CAN接收FIFO中断 </pre>*/
     INTC1MKRCANGRECC0 = _INT_PROCESSING_ENABLED;
     INTC1RFRCANGRECC0 = _INT_REQUEST_NOT_OCCUR;
     INTC1TBRCANGRECC0 = _INT_TABLE_VECTOR;
     INTC1ICRCANGRECC0 &= _INT_PRIORITY_LEVEL1;
    // /**<pre> 配置打开CAN4通道错误中断 </pre>*/
     INTC2MKRCAN4ERR = _INT_PROCESSING_ENABLED;
     INTC2RFRCAN4ERR = _INT_REQUEST_NOT_OCCUR;
     INTC2TBRCAN4ERR = _INT_TABLE_VECTOR;
     INTC2ICRCAN4ERR &= _INT_PRIORITY_LEVEL3;
    // /**<pre> 配置关闭CAN4通道接收/发送FIFO接收完成中断 </pre>*/
     INTC2MKRCAN4REC = _INT_PROCESSING_DISABLED;
     INTC2RFRCAN4REC = _INT_REQUEST_NOT_OCCUR;
     INTC2TBRCAN4REC = _INT_TABLE_VECTOR;
     INTC2ICRCAN4REC &= _INT_PRIORITY_LEVEL3;
    /**<pre> 配置开启CAN4通道发送中断 </pre>*/
     INTC2MKRCAN4TRX = _INT_PROCESSING_ENABLED;
     INTC2RFRCAN4TRX = _INT_REQUEST_NOT_OCCUR;
     INTC2TBRCAN4TRX = _INT_TABLE_VECTOR;
     INTC2ICRCAN4TRX &= _INT_PRIORITY_LEVEL2;

}

/******************************************************************************/
/**<pre>
 *函数名称: CANGlobalStart
 *功能描述: CAN-切换到全局操作模式,通道通讯模式并使能Tx队列和RxBuffer
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
static void CANGlobalStart(void)
{
    /**<pre> IF 全局通道模式 == 挂起或复位模式 </pre>*/
    if (RSCFD0CFDGSTS & 0x03ul) 
    {
        /**<pre>  切换模式至全局操作模式 </pre>*/
        RSCFD0CFDGCTR &= 0xFFFFFFFCul;
        /**<pre>  全局模式退出挂起或复位模式与否 </pre>*/
        while((RSCFD0CFDGSTS & 0x03ul) != 0ul)
        {
            NOP();
        }
    }

    /**<pre> IF 通道0 == 挂起或复位模式 </pre>*/
    if (RSCFD0CFDCmSTS(CAN_CHANNEL_USE) & 0x03ul) 
    {
        /**<pre>  切换至通讯模式 </pre>*/
        RSCFD0CFDCmCTR(CAN_CHANNEL_USE) &= 0xFFFFFFFCul;
        /**<pre>  通道0退出挂起或复位模式与否 </pre>*/
        while((RSCFD0CFDCmSTS(CAN_CHANNEL_USE) & 0x03ul) != 0ul)
        {
            NOP();
        }
    }
    /**<pre> 通道0切换至通讯模式与否 </pre>*/
    while(!(RSCFD0CFDCmSTS(CAN_CHANNEL_USE) & 0x00000080ul))
    {
        NOP();
    }

    /**<pre> 使能接收FIFO </pre>*/
    RSCFD0CFDRFCC0 |= 0x3ul;
    RSCFD0CFDRFCC1 |= 0x3ul;
    RSCFD0CFDRFCC2 |= 0x3ul;
    RSCFD0CFDRFCC3 |= 0x3ul;
    RSCFD0CFDRFCC4 |= 0x3ul;
    RSCFD0CFDRFCC5 |= 0x3ul;
    RSCFD0CFDRFCC6 |= 0x3ul;
    RSCFD0CFDRFCC7 |= 0x3ul;
    /**<pre> 使能发送队列 </pre>*/
    RSCFD0CFDTXQCCm(CAN_CHANNEL_USE) |= 0x1ul;
}

/******************************************************************************/
/**<pre>
 *函数名称: CanTransceiverClose
 *功能描述: CAN收发器关闭
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
//static void CanTransceiverClose(void)
//{
//    SetGpioPinLvl(CAN_STB_PINNUM, PORT_HIGH_LEVEL);
//    SetGpioPinLvl(CAN_5V_EN_PINNUM, PORT_LOW_LEVEL);
//}

/******************************************************************************/
/**<pre>
 *函数名称: CanTransceiverClose
 *功能描述: CAN收发器打开
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
//static void CanTransceiverOpen(void)
//{
//    SetGpioPinLvl(CAN_STB_PINNUM, PORT_LOW_LEVEL);
//    SetGpioPinLvl(CAN_5V_EN_PINNUM, PORT_HIGH_LEVEL);
//}
/******************************************************************************/
/**<pre>
 *函数名称: CANDrvInit
 *功能描述: CAN初始化配置
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void CANDrvInit(void)
{
    /* Set Tx&Rx Pin */
    CANSetTxRxPin();
    /* CANBus Initial */
    CANBusInit(); 
    CANInterruptSetting();
    CANGlobalStart();
}

/******************************************************************************/
/**<pre>
 *函数名称: CANDrvClose
 *功能描述: CAN驱动关闭
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void CANDrvClose(void)
{
   /* Transition from channel communication mode to channel reset mode */
   RSCFD0CFDCmCTR(CAN_CHANNEL_USE) |= 0x01ul;
   while((RSCFD0CFDCmSTS(CAN_CHANNEL_USE) & 0x03ul) != 0x01ul)
   {
       NOP();
   }

   /* Transition from global operating mode to global reset mode */
   RSCFD0CFDGCTR |= 0x01ul;
   while((RSCFD0CFDGSTS & 0x03ul) != 0x01ul)
   {
       NOP();
   }

   /* Transition from channel reset mode to channel stop mode */
   RSCFD0CFDCmCTR(CAN_CHANNEL_USE) |= 0x4ul;

   /* Transition from global reset mode to global stop mode */
   RSCFD0CFDGCTR |= 0x4ul;

   //CanTransceiverClose();
}

/******************************************************************************/
/**<pre>
 *函数名称: CANDrvReset
 *功能描述: CAN驱动复位
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void CANDrvReset(void)
{
   /* Transition from channel communication mode to channel reset mode */
   RSCFD0CFDCmCTR(CAN_CHANNEL_USE) |= 0x01ul;
   while((RSCFD0CFDCmSTS(CAN_CHANNEL_USE) & 0x03ul) != 0x01ul)
   {
       NOP();
   }

   /* Transition from global operating mode to global reset mode */
   RSCFD0CFDGCTR |= 0x01ul;
   while((RSCFD0CFDGSTS & 0x03ul) != 0x01ul)
   {
       NOP();
   }

   CANDrvInit();
}

/*********************************************************************
* 函数名称: TxComfirmProcess
*
* 功能描述: 轮询确认发送是否完成
*
* 输入参数:
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/
void TxComfirmProcess(void)
{
    uint8 Lu8MailboxIdx;

    for (Lu8MailboxIdx = 0u; Lu8MailboxIdx < 16; Lu8MailboxIdx++)
    {
/*从RSCFD0CFDTMSTS48状态开始查询；查询到发送完成状态，则清除该buffer中断；并确认发送*/
    }
    RSCFD0CFDTMSTS48 = 0;
}

static inline uint8 CAN_GetBufLenByDLC(uint8 DLC)
{
    uint8 DataLenght[16] = { 0,1,2,3,4,5,6,7,8,12,16,20,24,32,48,64 };
    uint8 Rst = 0u;
    Rst = DataLenght[DLC];
    return Rst;
}
/******************************************************************************/
/**<pre>
 *函数名称: CANTransmitFrame
 *功能描述: CAN帧发送
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void CANTransmitFrame(uint8 Lu8TxBufNum, uint8* pData, uint8 Lu8DLC, uint32 Lu32ID)
{
    uint8 i = 0u;
    uint8 Lu8BufLen = 0u;
    Canfd_FrameType * pTxBuffer;
    /*校验当前发送队列通道*/
    if ((Lu8TxBufNum == 0) || (Lu8TxBufNum == 3) || (Lu8TxBufNum == 6) || \
        (Lu8TxBufNum == 9) || (Lu8TxBufNum == 12) || (Lu8TxBufNum == 15))
    {
        GstrCANTxFrame.bit.DLC = (uint32)Lu8DLC;
        GstrCANTxFrame.bit.ID = Lu32ID;
        GstrCANTxFrame.bit.FDF = 1u;
        GstrCANTxFrame.bit.BRS = 1u;
        GstrCANTxFrame.bit.ESI = 1u;
        Lu8BufLen = CAN_GetBufLenByDLC(Lu8DLC);
        for (i = 0u; i < Lu8BufLen; i++)
        {
            GstrCANTxFrame.bit.DB[i] = pData[i];
        }
        pTxBuffer = (Canfd_FrameType*)GetAddress_RCFDC0CFDTMIDp(CAN_CHANNEL_USE, Lu8TxBufNum);//(0xFFD04000 + 0x20*((Lu8TxBufNum)+(3)*16u))
        *pTxBuffer = GstrCANTxFrame;
        // RCFDC0CFDTMIDp(CAN_CHANNEL_USE, Lu8TxBufNum) = GstrCANTxFrame.Reg[0];
        // RCFDC0CFDTMPTRp(CAN_CHANNEL_USE, Lu8TxBufNum) = GstrCANTxFrame.Reg[1];
        // RCFDC0CFDTMFDCTRp(CAN_CHANNEL_USE, Lu8TxBufNum) = GstrCANTxFrame.Reg[2];
        // Lu8BufLen >>= 2u;
        // for(i = 0u; i < Lu8BufLen; i++)
        // {
        //     RCFDC0CFDTMDFb_p(CAN_CHANNEL_USE, i, Lu8TxBufNum) = GstrCANTxFrame.Reg[3+i];
        // }
        RCFDC0CFDTMCp(CAN_CHANNEL_USE, Lu8TxBufNum) = 0x1ul;//(*(volatile uint32 *)(0xFFD00250 + ((Lu8TxBufNum)+(3)*16u)))
    }
}

//
///*****************************************************************************
//** Function:    Canfd_ReadRxFIFOBuffer
//** Description: This code shows how to read message from Rx FIFO buffer
//** Parameter:   Canfd_FrameType* pFrame - the data will be stored to
//** Return:      void  
//******************************************************************************/
//void R_RSCANFD_ReadRxFIFOBuffer(Canfd_FrameType* pFrame)
//{
//    Canfd_FrameType* pRxFIFOBuffer ;
//    /*check data to RX FIFO buffer 0 */
//    if((RSCFD0CFDRFSTS0 & 0x00000001)==0x0)	//
//    {
//	pRxFIFOBuffer = (Canfd_FrameType*) &(RSCFD0CFDRFID0);
//	*pFrame = pRxFIFOBuffer[0];
//	RSCFD0CFDRFPCTR0 |=0xFF;
//    }
//}


/******************************************************************************/
/**<pre>
 *函数名称: CANReceiveFrame
 *功能描述: CAN帧接收
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
uint8 CANReceiveFrame(uint8 *LptrData, uint8 Lu8Dlc,uint8 FrameIdx)
{
    uint8 i;
    uint8 Lu8Len = CAN_GetBufLenByDLC(Lu8Dlc);
    uint8 Res = E_OK;
    volatile Canfd_FrameType *LpReg;
    /**<pre> IF 接收FIFO有数据 </pre>*/
    if(RSCFD0CFDRFSTS0 & 0xFF00ul)
    {
        /**<pre>  获取FIFO内的帧数据缓存 </pre>*/
        LpReg = (volatile Canfd_FrameType *)&(RSCFD0CFDRFID0);
        if (LpReg->bit.DLC >= 8)
        {
            Gu8ComDataDLC[FrameIdx] = LpReg->bit.DLC;


            for (i = 0u; i < Lu8Len; i++)
            {
                LptrData[i] = LpReg->bit.DB[i];

            }
        }
        else
        {
            Res = E_NOT_OK;
        }
        // RSCFD0CFDRFPCTR0 = 0xFFul;    /**<pre>  操控寄存器指向下个FIFO缓存 </pre>*/
    }
    return Res;
}

/******************************************************************************/
/**<pre>
 *函数名称: CANGetTMCIdx
 *功能描述: 获取CAN发送完成下标
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
uint8 CANGetTMCIdx(void)
{
    uint8 u8FrameIdx = 0u;
    while(((RSCFD0CFDTMTCSTSy(CAN_CHANNEL_USE, u8FrameIdx)&1ul) == 0x0ul)&&(u8FrameIdx < 16u))
    {
        u8FrameIdx++;
    }
    RCFDC0CFDTMSTSp(CAN_CHANNEL_USE, u8FrameIdx - 1u) = 0x00ul;
    return u8FrameIdx;
}

/******************************************************************************/
/**<pre>
 *函数名称: CANGetRMCIdx
 *功能描述: 获取CAN接收下标
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
uint16 CANGetRMCIdx(void)
{
    return (uint16)(RSCFD0CFDRFPTR0H & 0x0fff);
}


/******************************************************************************/
/**<pre>
 *函数名称: CANGetRxBufStatus
 *功能描述: 获取CAN接收缓存状态
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
uint8 CANGetRxBufStatus(void)
{
    uint8 Lu8Rtn = CAN_RX_BUF_NULL;    /**<pre> 初始化返回值 = 接收缓存为空 </pre>*/

    /**<pre> 获取下个接收缓存状态 </pre>*/
    RSCFD0CFDRFPCTR0 = 0xFFul;              // 可能导致丢帧 20221025
    if(RSCFD0CFDRFSTS0 & 0xFF00ul)    /**<pre> IF 接收缓存状态 == 接收完成 </pre>*/
    {
        Lu8Rtn = CAN_RX_BUF_NOT_NULL;    /**<pre>  返回值 = 接收缓存非空 </pre>*/
    }
    else
    {
        RSCFD0CFDRFSTS0 &= 0xFFFFFFF7ul;    /**<pre> ELSE 清除接收中断标志 </pre>*/
    }

    return Lu8Rtn;    /**<pre> RETURN 接收缓存状态 </pre>*/

}

