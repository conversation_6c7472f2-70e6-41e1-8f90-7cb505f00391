/**********************************************************************
*	版权所有	: 2021 深圳市豪恩汽车电子装备股份有限公司
*	项目名称	: 
*	M  C  U     : R7F701695
*	编译环境	: IAR 2.10
*	文件名称	: 
*	其它说明	: 
*	当前版本	: V0.1
*	作    者	: 22557
*	完成日期	: 	
*	内容摘要	: 
*	修改记录	:
*	修改日期	:
*	版 本 号	:
***********************************************************************/

#include "r_typedefs.h"
#include "fcl_cfg.h"
#include "r_fcl.h"
#include "fcl_user.h"
#include "types.h"
#include "fls.h"
#include "fcl_descriptor.h"


/************************************************************************************************************
Macro definitions
************************************************************************************************************/
#define FLMD0_PROTECTION_OFF    (0x01u)
#define FLMD0_PROTECTION_ON     (0x00u)

/************************************************************************************************************
Typedef definitions
************************************************************************************************************/


/************************************************************************************************************
Exported global variables and functions (to be accessed by other files)
************************************************************************************************************/

/* This array reserves the copy area in the device RAM */
#define FCL_RAM_EXECUTION_AREA_SIZE 0x2000//原先是0x8000 32k，因为整个芯片只有32k所以改小为0x2000 8k

#if R_FCL_COMPILER == R_FCL_COMP_GHS
    #pragma ghs startdata
    #pragma ghs section bss = ".FCL_RESERVED"
    #define R_FCL_NOINIT
#elif R_FCL_COMPILER == R_FCL_COMP_IAR
    #pragma segment = "FCL_RESERVED"
    #pragma location = "FCL_RESERVED"
    #define R_FCL_NOINIT __no_init
#elif R_FCL_COMPILER == R_FCL_COMP_REC
    #pragma section r0_disp32 "FCL_RESERVED"
    #define R_FCL_NOINIT
#endif

R_FCL_NOINIT uint8 FCL_Copy_area[FCL_RAM_EXECUTION_AREA_SIZE];


/*******************************************************************************
* 函数名称: Fls_Init
* 功能描述: 初始化Flash
* 输入参数: 无
* 输出参数: 无
* 返 回 值:  无
* 其它说明:
* 修改日期      版本号       修改人       修改内容

*******************************************************************************/
void Fls_Init(void)
{
    r_fcl_status_t ret;
    r_fcl_request_t  myRequest;

    ret = R_FCL_Init ((r_fcl_descriptor_t *)&fclConfig_enu);
    if (R_FCL_OK == ret)
    {
        ret = R_FCL_CopySections ();
    }    
    if(R_FCL_OK != ret);/*如果初始化不成功进行看门狗复位*/
    {
#ifdef DEBUG_SW
        printf("Fcl Init ret=%d\r\n",ret);
#endif
    }

    FCLUser_Open ();/* prepare environment */
    myRequest.command_enu = R_FCL_CMD_PREPARE_ENV;
    myRequest.status_enu = R_FCL_BUSY;
    R_FCL_Execute (&myRequest);
    
    if(R_FCL_OK != myRequest.status_enu);
    {
#ifdef DEBUG_SW
        printf("Fcl Prepare status_enu=%d\r\n",myRequest.status_enu);
#endif
    }
}


/******************************************************************************* 
* 函数名称    :    Fls_Erase
* 功能描述    :    擦Flash
* 输出参数    :    
* 输入参数  :      LcStartBlockNum    : 擦Flash的起始块数字
*                      LcBlockLen      : 需要擦除的块长度
* 返 回 值     fsl_status : 返回擦Flash的状态
* 其它说明    :    
* 修改日期        版本号        修改人        修改内容
*******************************************************************************/
ErrorType Fls_Erase(uint32 LcStartBlockNum,uint16 LcBlockLen)
{
    ErrorType LeRetStatus = ERR_INIT;
    r_fcl_request_t myRequest;  
    DI();
    FCLUser_Open ();
    myRequest.command_enu = R_FCL_CMD_DISABLE_LOCKBITS;/*擦除过程中断电测试出现lockbit，导致后续一直不能擦除成功*/
    myRequest.idx_u32     = 0;
    myRequest.cnt_u16     = 0;	
    R_FCL_Execute (&myRequest);
    /* erase block 68 */
    myRequest.command_enu = R_FCL_CMD_ERASE;
    myRequest.idx_u32     = LcStartBlockNum;                 /* erased range = 010000 to 0x1fffff   512k*/
    myRequest.cnt_u16     = LcBlockLen;					 /* total block 14 */  
    R_FCL_Execute (&myRequest);
    if(myRequest.status_enu == R_FCL_OK)
    {
        LeRetStatus = IS_OK;
    }
    else
    {
        LeRetStatus = ERR_FLASH_ERASE;
#ifdef DEBUG_SW
        printf("Fcl FlashErase failed %d\r\n",myRequest.status_enu);
#endif
    }
    FCLUser_Close();
    EI();
    return LeRetStatus;
}


/*******************************************************************************
* 函数名称: Fls_Write
* 功能描述: 写Flash
* 输入参数: addr起始地址, data指针, size固定为，256对齐
* 输出参数: 无
* 返 回 值:  无
* 其它说明: This function writes the specified number of words (each word 
*           consists of 4 bytes) to a specified address.
* 修改日期      版本号       修改人       修改内容

*******************************************************************************/
ErrorType Fls_Write(uint32 addr, uint8* data, uint16 size)
{
    ErrorType LeRetStatus = ERR_INIT;
    r_fcl_request_t myRequest;
    DI();
    FCLUser_Open ();    
    if((data != NULL)&&(size != 0u))
    {
        myRequest.command_enu = R_FCL_CMD_WRITE;
        myRequest.bufferAdd_u32 = (uint32)&data[0];
        myRequest.idx_u32 = addr;
        myRequest.cnt_u16 = size;             /* written bytes = 256 * cnt_u16 */
        
        R_FCL_Execute (&myRequest);
        
        if(myRequest.status_enu == R_FCL_OK)
        {
            LeRetStatus = IS_OK;
        }
        else
        {
            LeRetStatus = ERR_FLASH_WRITE;
#ifdef DEBUG_SW
            printf("Fcl FlashWrite failed %d\r\n",myRequest.status_enu);
#endif
        }
    }
    else
    {
         LeRetStatus = ERR_PARAMETER;
    } 
    FCLUser_Close();
    EI();
    return LeRetStatus;
}


