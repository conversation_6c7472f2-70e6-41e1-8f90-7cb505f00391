/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * APASignalManage.c: 
 * Created on: 2022-11-25 08:21
 * Original designer: 22866
 ******************************************************************************/

/* ============================================================================================== */
/* Header files																				      */
/* ============================================================================================== */
#include "PSL_State_Manage.h"
#include "SnsDiag.h"
#include "PAS_MAP_SignalManage.h"







/***************************************************************************/
/* local data definitions                                                  */
/***************************************************************************/

PSLWorkManage_TrigDataType GstrPSLWorkMagTrigData;
APAWorkManage_error_record GstrApaWorkrecorderrordata;


/*******************************************************************
***********************Private Function****************************
*********************************************************************/


/* ============================================================================================== */
/* Functions																													    */
/* ============================================================================================== */
/* ---------------------------------------------------------------------------------------------- */

/******************************************************************
* 函数名称: PSLWorkStatusManageINIT
*
* 功能描述: APS 状态机INIT
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 
*
* 修改日期      版本号      修改人      修改内容

*********************************************************************/
void PSLWorkStatusManageInit(void)
{
    GstrPSLWorkMagTrigData.enuPSL_APA_Status = APA_STANDBY;
    GstrPSLWorkMagTrigData.u8BCMpowermode = POWER_OFF;
    GstrPSLWorkMagTrigData.u8VehicleValidValue = 0;
    GstrPSLWorkMagTrigData.u8ADASslotIDSelected = 0;
    GstrPSLWorkMagTrigData.vPSLFuncDG = Read_PSLFunctionDGFlag();
}


/******************************************************************
* 函数名称: ApaWorkManage_Privated_SysRelativeFault
*
* 功能描述: 判断关联性故障
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 有故障：非0  无故障：0
*
* 其它说明: 
*
* 修改日期      版本号      修改人      修改内容

*********************************************************************/
uint8 ApaWorkManage_Privated_SysRelativeFault(void)
{
    uint8 Lu8RelativeFaultResult = FALSE;
    uint8 i;
    uint8 Lu8SnsBeCoveredFlag;
    
    if(SnsDiag_ReadSysSnsGroupErrSts() == TRUE)
    {
        Lu8RelativeFaultResult = TRUE;
    }
    /* 添加车衣检测故障，同步对PSL的降级处理，不再搜索车位 */
    if(Lu8RelativeFaultResult == FALSE)
    {
        for(i = SNS_INX_01; i < SNS_INX_NUM; i++)
        {
            PdcSignal_ReadSnsBeCoveredFlagToDTC(&Lu8SnsBeCoveredFlag,(PdcSiagnal_SnsInxType)i);
            if(Lu8SnsBeCoveredFlag)
            {
                Lu8RelativeFaultResult = TRUE;
                break;
            }
        }
    }
    
    return Lu8RelativeFaultResult;
}





/**********************************************************
*描述： 获取PSL控制相关CAN信号
*输入参数：无
*输出参数：无
*返回值：无
************************************************************/
void PSL_GetCANSignal(void)
{
	/*读取低压模式*/
	ReadCAN_AppSignal_LowVolPwrMd(&GstrPSLWorkMagTrigData.u8BCMpowermode);

	/*读取速度*/
	ReadCAN_AppSignal_Car_Speed(&GstrPSLWorkMagTrigData.u16VehicleSpeedValue);
    /*读取速度有效性*/
    ReadSysSignal_Car_SpeedVild(&GstrPSLWorkMagTrigData.u8VehicleValidValue);
    /*读取PSL使能标志*/
    ReadCAN_AppSignal_ADAS_PSL_EnableSts(&GstrPSLWorkMagTrigData.ADAS_PSL_ENABLE_FLAG);
	/*读取倒车雷达功能是否开启*/
//	ReadSysSignal_PAS_Cmd(&GstrPDCInfoFromCan.u8PAS_Enable);

    /*读取上层所选车位信息*/
    ReadCAN_AppSignal_ADAS_slot_ID_Selected(&GstrPSLWorkMagTrigData.u8ADASslotIDSelected);
    
    /*读取PSL工作状态*/
	ReadCAN_AppSignal_ADAS_APAStatus(&GstrPSLWorkMagTrigData.enuPSL_APA_Status);

    /*读取PSL功能降级标志*/
    GstrPSLWorkMagTrigData.vPSLFuncDG = Read_PSLFunctionDGFlag();  
}


/******************************************************************
* 函数名称: PSLWorkStatusManage_Privated
*
* 功能描述: APS 状态机逻辑，周期20ms
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 
*
* 修改日期      版本号      修改人      修改内容

*********************************************************************/
void PSLWorkStatusManage_Privated(void)
{
    VehicleTypeType  LenuVEH_Type;
    ReadCAN_AppSignal_VEH_Type(&LenuVEH_Type);
    if(GstrPSLWorkMagTrigData.u8BCMpowermode != POWER_ON)
    {
        GstrPSLWorkMagTrigData.u8APACantx_PSLWorkstatus = PSL_Work_OFF;
        PSLSignal_WritePSLWorkState(GstrPSLWorkMagTrigData.u8APACantx_PSLWorkstatus);
        return;
    }

     /*s 进入Failure条件 */
    if((ApaWorkManage_Privated_SysRelativeFault() == TRUE) || (GstrPSLWorkMagTrigData.enuPSL_APA_Status == APA_FAILURE)\
        ||(LenuVEH_Type == 0xff) || (GstrPSLWorkMagTrigData.vPSLFuncDG == TRUE))
    {
        GstrPSLWorkMagTrigData.u8APACantx_PSLWorkstatus = PSL_Work_Failure;     
    }
    
    switch(GstrPSLWorkMagTrigData.u8APACantx_PSLWorkstatus)
    {
        case PSL_Work_OFF:       
        
            GstrPSLWorkMagTrigData.u8APACantx_PSLWorkstatus = PSL_Work_Standby;  
             
        break;
            
        case PSL_Work_Standby:
    
            if((GstrPSLWorkMagTrigData.u16VehicleSpeedValue <= SPEED_25KMH)
                && (GstrPSLWorkMagTrigData.u8VehicleValidValue == 0)
                && (GstrPSLWorkMagTrigData.ADAS_PSL_ENABLE_FLAG == PSL_ENABLE)
                && (GstrPSLWorkMagTrigData.enuPSL_APA_Status == APA_SEARCH))
            {
                GstrPSLWorkMagTrigData.u8APACantx_PSLWorkstatus = PSL_Work_Searching; 
            }
            
        break;
    

        case PSL_Work_Searching:
                 
            if((GstrPSLWorkMagTrigData.u16VehicleSpeedValue <= SPEED_10KMH)
                && (GstrPSLWorkMagTrigData.u8VehicleValidValue == 0)
                && (GstrPSLWorkMagTrigData.enuPSL_APA_Status == APA_GUIDANCE))
            {
               GstrPSLWorkMagTrigData.u8APACantx_PSLWorkstatus = PSL_Work_Guidance;
               break;
            }

             if(((GstrPSLWorkMagTrigData.u16VehicleSpeedValue > SPEED_30KMH)
                && (GstrPSLWorkMagTrigData.u8VehicleValidValue == 0))
                || (GstrPSLWorkMagTrigData.ADAS_PSL_ENABLE_FLAG == PSL_DISABLE)
                || (GstrPSLWorkMagTrigData.enuPSL_APA_Status != APA_SEARCH))
            {
                GstrPSLWorkMagTrigData.u8APACantx_PSLWorkstatus = PSL_Work_Standby;
            }
        break;


        case PSL_Work_Guidance:
           
            if(((GstrPSLWorkMagTrigData.u16VehicleSpeedValue > SPEED_10KMH) &&(GstrPSLWorkMagTrigData.u8VehicleValidValue == 0))
                || (GstrPSLWorkMagTrigData.ADAS_PSL_ENABLE_FLAG == PSL_DISABLE)
                || (GstrPSLWorkMagTrigData.enuPSL_APA_Status == APA_STANDBY))
            {
                GstrPSLWorkMagTrigData.u8APACantx_PSLWorkstatus = PSL_Work_Standby;
                break;
            }

            if(GstrPSLWorkMagTrigData.enuPSL_APA_Status == APA_SEARCH)
            {
                GstrPSLWorkMagTrigData.u8APACantx_PSLWorkstatus = PSL_Work_Searching;
                break;
            }
    
        break;

        case PSL_Work_Failure:

            if((ApaWorkManage_Privated_SysRelativeFault() == FALSE)
                && (GstrPSLWorkMagTrigData.enuPSL_APA_Status != APA_FAILURE)
                && (GstrPSLWorkMagTrigData.vPSLFuncDG == FALSE)
                && (LenuVEH_Type != 0xFF))
            {
                GstrPSLWorkMagTrigData.u8APACantx_PSLWorkstatus = PSL_Work_Standby;  
            }
        break;
        
        default:
        
           GstrPSLWorkMagTrigData.u8APACantx_PSLWorkstatus = PSL_Work_Standby;
        break;
        
    }
    /*发送PSL工作状态*/
    PSLSignal_WritePSLWorkState(GstrPSLWorkMagTrigData.u8APACantx_PSLWorkstatus);
}

void PSLStateManageMain(void)
{
    PSL_GetCANSignal();
    PSLWorkStatusManage_Privated();
}


/*******************************************************************************
*描述：PSL工作状态函数接口
*输入参数：无
*输出参数：无
*返回值：无
********************************************************************************/
static ApaSignal_PSLWorkStatusType GenuPSLSignal_PSLStatus;
void PSLSignal_WritePSLWorkState(ApaSignal_PSLWorkStatusType LenuPSLWorkStatus)
{
	GenuPSLSignal_PSLStatus = LenuPSLWorkStatus;
}

void PSLSignal_ReadPSLWorkState(ApaSignal_PSLWorkStatusType *LenuPSLWorkStatus)
{
	*LenuPSLWorkStatus = GenuPSLSignal_PSLStatus;
}




