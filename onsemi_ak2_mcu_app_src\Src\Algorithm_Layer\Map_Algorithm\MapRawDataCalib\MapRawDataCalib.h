/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * MapRawDataCalib: 
 * Created on: 2023-12-22 20:29
 * Original designer: AntonyFang
 ******************************************************************************/
#ifndef MapRawDataCalib_H
#define MapRawDataCalib_H

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "types.h"
#include "SnsRawData_Type.h"

/******************************************************************************
 * Compiler option used to distinguish inclusion done by the owner
 *****************************************************************************/

/******************************************************************************
 * Inclusion of private header files
 *****************************************************************************/

/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/

/* 宏开关，用于控制是否启用Demo模式下设置低阈值 */
#define Demo_Threshold_Set     0

/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/

/* 定义原始回波筛选的高度类型 */
typedef struct
{
    uint16 u16PVC_PileHeight;
    uint16 u16BigWallHeight;
    uint16 u16SecondWallHeight;
    uint16 u16CurbHeight;
}SnsMapCalibHeightType;

/* 定义ECU端阈值的梯度，5cm一个阶梯*/
typedef enum
{
    SNS_MAP_DIS_10cm = 0,
    SNS_MAP_DIS_20cm, 
    SNS_MAP_DIS_30cm,
    SNS_MAP_DIS_40cm,
    SNS_MAP_DIS_50cm,

    SNS_MAP_DIS_60cm,
    SNS_MAP_DIS_70cm, 
    SNS_MAP_DIS_80cm,
    SNS_MAP_DIS_90cm,
    SNS_MAP_DIS_100cm,
    
    SNS_MAP_DIS_110cm,
    SNS_MAP_DIS_120cm, 
    SNS_MAP_DIS_130cm,
    SNS_MAP_DIS_140cm,
    SNS_MAP_DIS_150cm,

    SNS_MAP_DIS_160cm,
    SNS_MAP_DIS_170cm, 
    SNS_MAP_DIS_180cm,
    SNS_MAP_DIS_190cm,
    SNS_MAP_DIS_200cm,

    SNS_MAP_DIS_210cm,
    SNS_MAP_DIS_220cm, 
    SNS_MAP_DIS_230cm,
    SNS_MAP_DIS_240cm,
    SNS_MAP_DIS_250cm,

    SNS_MAP_DIS_260cm,
    SNS_MAP_DIS_270cm, 
    SNS_MAP_DIS_280cm,
    SNS_MAP_DIS_290cm,
    SNS_MAP_DIS_300cm,

    SNS_MAP_DIS_310cm,
    SNS_MAP_DIS_320cm, 
    SNS_MAP_DIS_330cm,
    SNS_MAP_DIS_340cm,
    SNS_MAP_DIS_350cm,

    SNS_MAP_DIS_360cm,
    SNS_MAP_DIS_370cm, 
    SNS_MAP_DIS_380cm,
    SNS_MAP_DIS_390cm,
    SNS_MAP_DIS_400cm,
    SNS_MAP_DIS_TOTAL_NUM
}MapSnsDisStepType;

/* 重新定义ECU端判断高度阈值的梯度，10cm一个阶梯*/
typedef enum
{
    SNS_MAP_DIS_HIGH_10cm =0, 
    SNS_MAP_DIS_HIGH_20cm,
    SNS_MAP_DIS_HIGH_30cm,
    SNS_MAP_DIS_HIGH_40cm, 
    SNS_MAP_DIS_HIGH_50cm,

    SNS_MAP_DIS_HIGH_60cm, 
    SNS_MAP_DIS_HIGH_70cm,
    SNS_MAP_DIS_HIGH_80cm,
    SNS_MAP_DIS_HIGH_90cm, 
    SNS_MAP_DIS_HIGH_100cm,

    SNS_MAP_DIS_HIGH_110cm, 
    SNS_MAP_DIS_HIGH_120cm,
    SNS_MAP_DIS_HIGH_130cm,
    SNS_MAP_DIS_HIGH_140cm, 
    SNS_MAP_DIS_HIGH_150cm,

    SNS_MAP_DIS_HIGH_160cm, 
    SNS_MAP_DIS_HIGH_170cm,
    SNS_MAP_DIS_HIGH_180cm,
    SNS_MAP_DIS_HIGH_190cm, 
    SNS_MAP_DIS_HIGH_200cm,

    SNS_MAP_DIS_HIGH_210cm, 
    SNS_MAP_DIS_HIGH_220cm,
    SNS_MAP_DIS_HIGH_230cm,
    SNS_MAP_DIS_HIGH_240cm, 
    SNS_MAP_DIS_HIGH_250cm,
    
    SNS_MAP_DIS_HIGH_260cm, 
    SNS_MAP_DIS_HIGH_270cm,
    SNS_MAP_DIS_HIGH_280cm,
    SNS_MAP_DIS_HIGH_290cm, 
    SNS_MAP_DIS_HIGH_300cm,

    SNS_MAP_DIS_HIGH_310cm, 
    SNS_MAP_DIS_HIGH_320cm,
    SNS_MAP_DIS_HIGH_330cm,
    SNS_MAP_DIS_HIGH_340cm, 
    SNS_MAP_DIS_HIGH_350cm,
    
    SNS_MAP_DIS_HIGH_360cm, 
    SNS_MAP_DIS_HIGH_370cm,
    SNS_MAP_DIS_HIGH_380cm,
    SNS_MAP_DIS_HIGH_390cm, 
    SNS_MAP_DIS_HIGH_400cm,
    SNS_MAP_DIS_HIGH_TOTAL_NUM
}MapSnsDisHighStepType;

/******************************************************************************
* 设计描述 : 车型配置等级
* 设计索引 :
*******************************************************************************/
typedef enum
{
    MAP_VEH_NO_MAX = 0,
    MAP_VEH_MAX,
}MapVheiCfgLevelType;



/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
extern const uint16 Gu16FLS_FRS_MasStdThresTableForMap[SNS_MAP_DIS_TOTAL_NUM];
extern const uint16 Gu16FLS_FRS_MasChirpThresTableForMap[SNS_MAP_DIS_TOTAL_NUM];
extern const uint16 Gu16FL_FR_MasStdThresTableForMap[SNS_MAP_DIS_TOTAL_NUM];
extern const uint16 Gu16FL_FR_MasChirpThresTableForMap[SNS_MAP_DIS_TOTAL_NUM];
extern const uint16 Gu16FML_FMR_MasStdThresTableForMap[SNS_MAP_DIS_TOTAL_NUM];
extern const uint16 Gu16FML_FMR_MasChirpThresTableForMap[SNS_MAP_DIS_TOTAL_NUM];

extern const uint16 Gu16RLS_RRS_MasStdThresTableForMap[SNS_MAP_DIS_TOTAL_NUM];
extern const uint16 Gu16RLS_RRS_MasChirpThresTableForMap[SNS_MAP_DIS_TOTAL_NUM];
extern const uint16 Gu16RL_RR_MasStdThresTableForMap[SNS_MAP_DIS_TOTAL_NUM];
extern const uint16 Gu16RL_RR_MasChirpThresTableForMap[SNS_MAP_DIS_TOTAL_NUM];
extern const uint16 Gu16RML_RMR_MasStdThresTableForMap[SNS_MAP_DIS_TOTAL_NUM];
extern const uint16 Gu16RML_RMR_MasChirpThresTableForMap[SNS_MAP_DIS_TOTAL_NUM];

extern const SnsMapCalibHeightType GStrMapObjJudgeStdThresTable[SNS_MAP_DIS_HIGH_TOTAL_NUM]; 
extern const SnsMapCalibHeightType GStrMapObjJudgeChirpThresTable[SNS_MAP_DIS_HIGH_TOTAL_NUM]; 

extern const SnsMapCalibHeightType GStrMapObjJudgeStdThresTableForSideMap[SNS_MAP_DIS_HIGH_TOTAL_NUM];
extern const SnsMapCalibHeightType GStrMapObjJudgeChirpThresTableForSideMap[SNS_MAP_DIS_HIGH_TOTAL_NUM];


extern const uint16 *Gpu16SnsMapThresholdTable[PDC_SNS_GROUP_NUM][6];


/* 侦听相关阈值 */
extern const uint16 Gu16FLS_FRS_LisStdThresTableForMap[SNS_MAP_DIS_TOTAL_NUM];
extern const uint16 Gu16FLS_FRS_LisChirpThresTableForMap[SNS_MAP_DIS_TOTAL_NUM];
extern const uint16 Gu16FL_FR_LisStdThresTableForMap[SNS_MAP_DIS_TOTAL_NUM];
extern const uint16 Gu16FL_FR_LisChirpThresTableForMap[SNS_MAP_DIS_TOTAL_NUM];
extern const uint16 Gu16FML_FMR_LisStdThresTableForMap[SNS_MAP_DIS_TOTAL_NUM];
extern const uint16 Gu16FML_FMR_LisChirpThresTableForMap[SNS_MAP_DIS_TOTAL_NUM];

extern const uint16 Gu16RLS_RRS_LisStdThresTableForMap[SNS_MAP_DIS_TOTAL_NUM];
extern const uint16 Gu16RLS_RRS_LisChirpThresTableForMap[SNS_MAP_DIS_TOTAL_NUM];
extern const uint16 Gu16RL_RR_LisStdThresTableForMap[SNS_MAP_DIS_TOTAL_NUM];
extern const uint16 Gu16RL_RR_LisChirpThresTableForMap[SNS_MAP_DIS_TOTAL_NUM];
extern const uint16 Gu16RML_RMR_LisStdThresTableForMap[SNS_MAP_DIS_TOTAL_NUM];
extern const uint16 Gu16RML_RMR_LisChirpThresTableForMap[SNS_MAP_DIS_TOTAL_NUM];
extern const uint16 *Gpu16SnsMapLisThresholdTable[PDC_SNS_GROUP_NUM][6];



/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
extern  uint16 Gu16FLS_FRS_MasStdThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
extern  uint16 Gu16FLS_FRS_MasChirpThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
extern  uint16 Gu16FL_FR_MasStdThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
extern  uint16 Gu16FL_FR_MasChirpThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
extern  uint16 Gu16FML_FMR_MasStdThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
extern  uint16 Gu16FML_FMR_MasChirpThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];

extern  uint16 Gu16RLS_RRS_MasStdThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
extern  uint16 Gu16RLS_RRS_MasChirpThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
extern  uint16 Gu16RL_RR_MasStdThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
extern  uint16 Gu16RL_RR_MasChirpThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
extern  uint16 Gu16RML_RMR_MasStdThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
extern  uint16 Gu16RML_RMR_MasChirpThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];

extern  SnsMapCalibHeightType GStrMapObjJudgeStdThresTableInRAM[SNS_MAP_DIS_HIGH_TOTAL_NUM]; 
extern  SnsMapCalibHeightType GStrMapObjJudgeChirpThresTableInRAM[SNS_MAP_DIS_HIGH_TOTAL_NUM]; 

extern SnsMapCalibHeightType GStrMapObjJudgeStdThresTableForSideMapInRAM[SNS_MAP_DIS_HIGH_TOTAL_NUM];
extern SnsMapCalibHeightType GStrMapObjJudgeChirpThresTableForSideMapInRAM[SNS_MAP_DIS_HIGH_TOTAL_NUM];

extern  uint16 Gu16FLS_FRS_LisStdThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
extern  uint16 Gu16FLS_FRS_LisChirpThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
extern  uint16 Gu16FL_FR_LisStdThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
extern  uint16 Gu16FL_FR_LisChirpThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
extern  uint16 Gu16FML_FMR_LisStdThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
extern  uint16 Gu16FML_FMR_LisChirpThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];

extern  uint16 Gu16RLS_RRS_LisStdThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
extern  uint16 Gu16RLS_RRS_LisChirpThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
extern  uint16 Gu16RL_RR_LisStdThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
extern  uint16 Gu16RL_RR_LisChirpThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
extern  uint16 Gu16RML_RMR_LisStdThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
extern  uint16 Gu16RML_RMR_LisChirpThresTableForMapInRAM[SNS_MAP_DIS_TOTAL_NUM];
extern MapVheiCfgLevelType GenuMapVheiCfgLevel;


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
void CopySnsMapCaliThresToRam(void);


#endif /* end of SnsRawDataCalib_H */

