#ifndef __TP_MANAGE_H__
#define __TP_MANAGE_H__


/*******************************************************************************
* Includes
********************************************************************************/
#include "Types.h"
#include "TP_Config.h"

/******************************************************************************
* Constants and macros
*******************************************************************************/
#define TP_MSG_STATUS_OK                0U      /* brief Status OK */
#define TP_MSG_STATUS_NBS               1U      /* brief N_Bs (delay until reception of the next FC) timeout error status */
#define TP_MSG_STATUS_FS                2U      /* brief CF received with wrong FS */
#define TP_MSG_STATUS_WFT_OVRN          3U      /* brief Error with number of FC wait allowed */
#define TP_MSG_STATUS_OVFLOW            4U      /* brief Reception buffer size not sufficient to receive this message */
#define TP_MSG_STATUS_COM_NOK           5U      /* brief COM layer rejects the transmission request */

/* Addressing mode */
#define TP_ADRESSING_MODE               0x10U
#define TP_ADRESSING_PHYSICAL           0x10U
#define TP_ADRESSING_FUNCTIONAL         0x00U


/* Frame reception authorization for a TP channel */
#define TP_RX_ENABLE_ON                 0x00U
#define TP_RX_ENABLE_OFF                0x01U
#define TP_RX_ENABLE_MAX                0x02U

/* TP messages number managed by this software layer */
#define TP_CHANNEL_MAX                  2U

/* TP functions status */
#define TP_ERR_BASE                     0x50U
#define TP_ERR_OK                       0x00
#define TP_ERR_IDX                      ( TP_ERR_BASE + 0x00U )
#define TP_ERR_LEN                      ( TP_ERR_BASE + 0x01U )
#define TP_ERR_OVFLOW                   ( TP_ERR_BASE + 0x03U )

/*************************{Frame N_PCI definition}*************************/
#define TP_NPCI                         0xF0U
#define TP_NPCI_SINGLE_FRAME            0x00U
#define TP_NPCI_FIRST_FRAME             0x10U
#define TP_NPCI_CONSECUTIVE_FRAME       0x20U
#define TP_NPCI_FLOW_CONTROL            0x30U

/* Frame length definition */
#define TP_LEN_SINGLE_FRAME             8U
#define TP_LEN_FIRST_FRAME              8U
#define TP_LEN_MIN_CONSECUTIVE_FRAME    2U
#define TP_LEN_MAX_CONSECUTIVE_FRAME    64U
#define TP_LEN_FLOW_CONTROL             3U

/* Data length definition */
#define TP_DATA_MAX_SINGLE_FRAME           (7U)
#define TP_DATA_MAX_SINGLE_FRAME_DL64      (62U)
#define TP_DATA_MAX_FIRST_FRAME_FFDL4096         58U

#define TP_DATA_MAX_FIRST_FRAME            (62U)
//#define TP_DATA_MAX_FIRST_FRAME_DL64   (58U)

#define TP_DATA_MAX_CONSECUTIVE_FRAME      (7U)
#define TP_DATA_MAX_FLOW_CONTROL           (0U)

/* Flow Control Flow Status definition */
#define TP_FS_CLEAR_TO_SEND             0U
#define TP_FS_WAIT                      1U
#define TP_FS_OVERFLOW                  2U

/* TP message internal status */
#define TP_MSG_STATUS_NONE              0U

#define TP_MSG_STATUS_TX_SF             1U
#define TP_MSG_STATUS_TX_FF             2U
#define TP_MSG_STATUS_TX_CF             3U
#define TP_MSG_STATUS_TX_FC             4U

#define TP_MSG_STATUS_RX_SF             5U
#define TP_MSG_STATUS_RX_FF             6U
#define TP_MSG_STATUS_RX_CF             7U
#define TP_MSG_STATUS_RX_FC             8U

#define TP_MSG_STATUS_TX_FC_OVERFLOW    9U
#define TP_MSG_STATUS_TX_CF_STMIN       10U

#define TP_CHANNEL_UNLOCK               (uint8)0
#define TP_CHANNEL_LOCK                 (uint8)1

#define PHYSICALCHANNEL                 0U
#define FUNCTIONALCHANNEL               1U

/******************************************************************************
* typedef define
*******************************************************************************/

/* Transport Protocol message configuration, stored in Read-Only memory */
typedef struct
{ 
    uint8          GcRxStatus;          /* TP层通道接收状态 */ 
    volatile uint8 *pRxData;            /* Pointer on the reception data buffer    */
    uint16          GwDataMax;           /* 通道接收最大的数据个数 */
    uint16          GwRxLen;             /* 诊断接收数据包的长度 */
    uint16          GwRxLenCnt;          /* 诊断数据包当前已接收的数据计数 */
    uint16          GwuNAr;              /* 接收方发送流控制帧的时间 */
    uint16          GwuNArCnt;           /* 接收方等待完成发送流控制帧的时间计数 */
    uint16          GwuNCr;              /* 接收方等待接收连续帧的时间     */
    uint16          GwuNCrCnt;           /* 等待连续帧的时间计数 */     
    uint16          GwRxBs;              /* 服务器允许连续接收后续帧的帧数 */      
    uint16          GwRxBsCnt;           /* 接收连续后续帧计数 */ 
    uint8          GcRxSnCnt;
    uint8          GcRxSTmin;
    uint8          GcLockState;         /* 通道锁状态 */
} TpRxMsg;

typedef struct
{
    uint8          GcTxStatus;           /* TP层通道发送状态 */
    volatile uint8 *pTxData;             /* Pointer on the transmission data buffer */
    uint16          GwTxLen;              /* 诊断响应的数据长度 */
    uint16          GwTxLenCnt;           /* 诊断响应当前已发送的字节计数 */  
    uint16          GwuNAs;              /* 发送方发送诊断帧(首帧/连续帧)的时间 */
    uint16          GwTxuNAsCnt;         /* 发送方发送诊断帧的时间计数 */
    uint16          GwuNBs;               /* 发送方等待流控制帧的时间 */
    uint16          GwTxuNBsCnt;          /* 等待流控制帧的时间 */
    uint8          GcTxSnCnt;
    uint8          GcTxBs;
    uint8          GcTxBsCnt;
    uint8          GcTxSTmin;
    uint8          GcTxFCWaitMax;
    uint8          GcTxFCWaitCnt;       /* Number of FCWait frame received in a row */
    uint8          GcPaddingByte;
}TpTxMsg;

#define INIT_OK                    0U   
#define TP_INIT_FAIL               1U
#define TP_COM_INIT_FAIL           2U
#define TP_UDS_INIT_FAIL           3U

/******************************************************************************
* External objects
********************************************************************************/
#ifdef TP_OBJ
  TpRxMsg GsTpRxMsg[2];                      /* 用于记录功能寻址和物理寻址的TP协议状态及数据 */
  TpTxMsg GsTpTxMsg;                         /* 用于记录TP层发送状态及数据                   */
  TPCOMStr GsTpComStr;                       /* Tp层与COM层的接口 */
  
#else
  extern TpRxMsg GsTpRxMsg[2];                      /* 用于记录功能寻址和物理寻址的TP协议状态及数据 */
  extern TpTxMsg GsTpTxMsg;                         /* 用于记录TP层发送状态及数据                   */
  extern TPCOMStr GsTpComStr;                       /* Tp层与COM层的接口 */
  
#endif

extern uint8 UDS_TX_OK  ;

/*******************************************************************************
* Global Functions
********************************************************************************/
uint8 TP_SendMsg (void);
uint8 TP_Init(TpCfgStr* LsTpMsgCfg, TPCOMStr* LsTPCOMParaCfg, TPUDSStr* LsTPUDSParaCfg);
void TP_ComTxConfirm (void);
void TP_TimeSequenceManage (void);
extern uint8 TP_RxEnable (uint8 LcChannel, uint8 LcRxState);
void TP_Manage(void);


#endif

