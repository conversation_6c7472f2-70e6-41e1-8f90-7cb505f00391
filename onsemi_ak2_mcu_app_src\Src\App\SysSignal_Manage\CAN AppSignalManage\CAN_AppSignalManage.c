/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * CAN_AppSignalManage:
 * Created on: 2022-11-22 16:31
 * Original designer: AntonyFang
 ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "CAN_AppSignalManage.h"
#include "ODO_CalibPara_Types.h"
#include "TimerManage.h"
#include "TAU_COM.h"
#include "debug.h"

/******************************************************************************/
/******************************************************************************/
/****************************** Private Definition ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Public Definition *****************************/
/******************************************************************************/
/******************************************************************************/

/*************************************************************平台级信号1：档位****************************************** 
   说明：IL解析地方做200ms回执，以避免手动档杆在换挡过程中，连续划过好几个档；
         信号初始值是CAN_CAR_GEAR_P，无信号接收时为该值
         信号故障、CRC错误等都发送CAR_GEAR_NONE，应用层使用时，只要获取到是CAR_GEAR_NONE就当做有问题进行处理
*/
static Car_GearType GenuCar_Gear = CAN_CAR_GEAR_P;
void ReadCAN_AppSignal_Car_Gear(Car_GearType *LpvCar_Gear)
{
    *LpvCar_Gear = GenuCar_Gear;
}
void WriteCAN_AppSignal_Car_Gear(Car_GearType *LpvCar_Gear)
{
    SysSigna_Assert(CAN_CAR_GEAR_NUM > *LpvCar_Gear);
    GenuCar_Gear = *LpvCar_Gear;
}

/*************************************************************平台级信号2：车速 ****************************************** 
   说明：立即发送，信号初始值是0，无信号接收时为该值，单位0.01KM/H，
         信号故障、CRC错误等都发送 CAN_SIG_INVALID_SPD_VALUE
*/
static Car_SpeedType Gu16Car_Speed = 0;
static Car_SpeedVildType Gu16Car_SpeedVild = 0;
static Car_SpeedType_Phy GfCar_Speed_Phy = 0;
static Car_SnapshotSpeedType Gu16Car_SpeedForSnapshotData = 0;

void ReadCAN_AppSignal_Car_Speed(Car_SpeedType *LpvCar_Speed)
{
    *LpvCar_Speed = Gu16Car_Speed;
}
void WriteCAN_AppSignal_Car_Speed(Car_SpeedType *LpvCar_Speed)
{
    Gu16Car_Speed = *LpvCar_Speed;
}

void ReadSysSignal_Car_SpeedVild(Car_SpeedVildType *LpvCar_SpeedVild)
{
    *LpvCar_SpeedVild = Gu16Car_SpeedVild;
}
void WriteSysSignal_Car_SpeedVild(Car_SpeedVildType *LpvCar_SpeedVild)
{
    Gu16Car_SpeedVild = *LpvCar_SpeedVild;
}

void ReadSysSignal_Car_Speed_phy(Car_SpeedType_Phy *LpvCar_Speed)
{
    *LpvCar_Speed =GfCar_Speed_Phy ;
}
void WriteSysSignal_Car_Speed_phy(Car_SpeedType_Phy *LpvCar_Speed)
{
    GfCar_Speed_Phy = *LpvCar_Speed;
}

void ReadSysSignal_Car_SpeedForSnapshotData(Car_SnapshotSpeedType *LpvCar_SpeedForSnapshotData)
{
    *LpvCar_SpeedForSnapshotData = Gu16Car_SpeedForSnapshotData;
}
void WriteSysSignal_Car_SpeedForSnapshotData(Car_SnapshotSpeedType *LpvCar_SpeedForSnapshotData)
{
    Gu16Car_SpeedForSnapshotData = *LpvCar_SpeedForSnapshotData;
}



/*************************************************************平台级信号3：轮速脉冲方向及个数 ************************************ 
   说明：包含轮速脉冲计数和轮速方向，立即发送；轮速方向无效替代值为CAR_WHEEL_DIR_INVALID，
         轮速脉冲值无效替代值为CAN_SIG_INVALID_PULSE_CNT = 65535；应用层根据无效信号做相关处理即可
*/
static Car_WheelPulseType GstrCar_WheelPulseInf[CAR_WHEEL_NUM] = 
{
    [CAR_FL_WHEEL] = 
    {
        .enuCar_WheelPulseDir = CAR_WHEEL_DIR_INVALID,
        .u16Car_WheelPulseCnt = CAN_SIG_INVALID_PULSE_CNT,
    },
    
    [CAR_FR_WHEEL] = 
    {
        .enuCar_WheelPulseDir = CAR_WHEEL_DIR_INVALID,
        .u16Car_WheelPulseCnt = CAN_SIG_INVALID_PULSE_CNT,
    },

    [CAR_RL_WHEEL] = 
    {
        .enuCar_WheelPulseDir = CAR_WHEEL_DIR_INVALID,
        .u16Car_WheelPulseCnt = CAN_SIG_INVALID_PULSE_CNT,
    },
    
    [CAR_RR_WHEEL] = 
    {
        .enuCar_WheelPulseDir = CAR_WHEEL_DIR_INVALID,
        .u16Car_WheelPulseCnt = CAN_SIG_INVALID_PULSE_CNT,
    }
};
    
/*通过参数LenuWheelInx选择读取哪一个轮子的轮速脉冲信息  */
void ReadCAN_AppSignal_Car_WheelPulseAndDir(Car_WheelPulseType *LpstrCar_WheelInf,Car_WheelIndexType LenuWheelInx)
{
    SysSigna_Assert(CAR_WHEEL_NUM > LenuWheelInx);
    *LpstrCar_WheelInf = GstrCar_WheelPulseInf[LenuWheelInx];
}

/*1.通过参数LenuWheelInx选择写入哪一个轮子的轮速脉冲信息  
* 2.轮速脉冲和方向在同一条报文通过该函数写入*/
void WriteCAN_AppSignal_Car_WheelPulseAndDir(Car_WheelPulseType *LpstrCar_WheelInf,Car_WheelIndexType LenuWheelInx)
{
    GstrCar_WheelPulseInf[LenuWheelInx] = *LpstrCar_WheelInf;
}


/*1.通过参数LenuWheelInx选择写入哪一个轮子的轮速脉冲信息  
* 2.单独写入轮速脉冲值*/
void WriteCAN_AppSignal_Car_WheelPulse(Car_WheelPulseCntType *Lpu16WheelPulseCnt,Car_WheelIndexType LenuWheelInx)
{
    GstrCar_WheelPulseInf[LenuWheelInx].u16Car_WheelPulseCnt = *Lpu16WheelPulseCnt;
}

/*1.通过参数LenuWheelInx选择写入哪一个轮子的轮速脉冲信息  
* 2.单独写入轮速脉冲方向*/
void WriteCAN_AppSignal_Car_WheelDir(Car_WheelPulseDirType *LpenuWheelPulseDir,Car_WheelIndexType LenuWheelInx)
{
    GstrCar_WheelPulseInf[LenuWheelInx].enuCar_WheelPulseDir = *LpenuWheelPulseDir;
}

/*************************************************************平台级信号4：方向盘角度 ***************************************** 
    说明：信号初始值是0，无信号接收时为该值
          无效替代值为CAN_SIG_INVALID_STR_ANGLE = 32767，信号故障、CRC错误等都发送 CAN_SIG_INVALID_STR_ANGLE，
          应用层使用时，只要获取到是 CAN_SIG_INVALID_STR_ANGLE， 就当做有问题进行处理
*/
static Car_StrAngleType Gs16Car_StrAngle = 0;
void ReadCAN_AppSignal_Car_StrAngle(Car_StrAngleType *LpvCar_StrAngle)
{
    *LpvCar_StrAngle = Gs16Car_StrAngle;
}
void WriteCAN_AppSignal_Car_StrAngle(Car_StrAngleType *LpvCar_StrAngle)
{
    Gs16Car_StrAngle = *LpvCar_StrAngle;
}

/*************************************************************平台级信号5：车外温度信息 ***************************************** 
    说明：无效替代值25℃
*/
static Car_AmbientTempType Gs8Car_AmbientTemp = CAN_SIG_DEFAULT_TEM;
void ReadCAN_AppSignal_Car_AmbientTempType(Car_AmbientTempType *LpvCar_AmbientTemp)
{
    *LpvCar_AmbientTemp = Gs8Car_AmbientTemp;
}
void WriteCAN_AppSignal_Car_AmbientTempType(Car_AmbientTempType *LpvCar_AmbientTemp)
{
    Gs8Car_AmbientTemp = *LpvCar_AmbientTemp;
}

/*************************************************************平台级信号6：车辆日期、时间 ***************************************** 
    说明：
*/
static Car_TimeType GstrCar_Time = 
{
    .u16Year  = TIEM_YEAR_MIN,
    .u8Month  = TIEM_MONTH_MIN,
    .u8Date   = TIEM_DATE_MIN,
    .u8Hour   = TIEM_HOUR_MIN,
    .u8Minute = TIEM_MINUTE_MIN,
    .u8Second = TIEM_SECONDS_MIN,
    .u8Week = TIEM_WEEK_MIN,
    .enuHourMod = TIME_HOUR_NONE
};

void ReadCAN_AppSignal_Car_Time(Car_TimeType *LpvCar_Time)
{
    *LpvCar_Time = GstrCar_Time;
}
void WriteCAN_AppSignal_Car_Time(Car_TimeType *LpvCar_Time)
{
    /* 年份校验 必须大于开发年2020 */
    SysSigna_Assert(TIEM_YEAR_MIN <= LpvCar_Time->u16Year);
    /* 月份校验 */
    SysSigna_Assert(TIEM_MONTH_MIN <= LpvCar_Time->u8Month);
    SysSigna_Assert(TIEM_MONTH_MAX >= LpvCar_Time->u8Month);
    /* 日校验 */
    SysSigna_Assert(TIEM_DATE_MIN <= LpvCar_Time->u8Date);
    SysSigna_Assert(TIEM_DATE_MAX >= LpvCar_Time->u8Date);
    /* 小时校验 */
    SysSigna_Assert(TIEM_HOUR_MIN <= LpvCar_Time->u8Hour);
    SysSigna_Assert(TIEM_HOUR_MAX >= LpvCar_Time->u8Hour);
    /* 分钟校验 */
    SysSigna_Assert(TIEM_MINUTE_MIN <= LpvCar_Time->u8Minute);
    SysSigna_Assert(TIEM_MINUTE_MAX >= LpvCar_Time->u8Minute);
    /* 秒校验 */
    SysSigna_Assert(TIEM_SECONDS_MIN <= LpvCar_Time->u8Second);
    SysSigna_Assert(TIEM_SECONDS_MAX >= LpvCar_Time->u8Second);
    /*星期校验*/
    SysSigna_Assert(TIEM_WEEK_MIN <= LpvCar_Time->u8Week);
    SysSigna_Assert(TIEM_WEEK_MAX >= LpvCar_Time->u8Week);

    GstrCar_Time = *LpvCar_Time;
}

/*************************************************************平台级信号7：EPB状态****************************************** 
   说明：立即发送，信号初始值是0，无信号接收时为该值，单位0.01KM/H信号故障、CRC错误等都发送 CAR_EPB_NONE ，应用层使用时，只要获取到是CAR_EPB_NONE就当做有问题进行处理
*/
static Car_EPB_StsType GenuCar_EPB_Sts = CAR_EPB_NONE;
void ReadCAN_AppSignal_Car_EPB_Sts(Car_EPB_StsType *LpvEPB_Sts)
{
    *LpvEPB_Sts = GenuCar_EPB_Sts;
}
void WriteCAN_AppSignal_Car_EPB_Sts(Car_EPB_StsType *LpvEPB_Sts)
{
    GenuCar_EPB_Sts = *LpvEPB_Sts;
}

/*************************************************************平台级信号8：轮速 ************************************ 
   说明：立即发送，信号初始值是0，无信号接收时为该值，单位0.01KM/H，
         信号故障、CRC错误等都发送CAN_SIG_INVALID_SPD_VALUE
*/
static Wheel_SpeedType Gu16Wheel_Speed[CAR_WHEEL_NUM] = 
{
    [CAR_FL_WHEEL] = 0,
        
    [CAR_FR_WHEEL] = 0,

    [CAR_RL_WHEEL] = 0,
        
    [CAR_RR_WHEEL] = 0
};

/*通过参数LenuWheelInx选择读取哪一个轮子的轮速  */
void ReadCAN_AppSignal_Wheel_Speed(Wheel_SpeedType *LpvWheel_Speed,Car_WheelIndexType LenuWheelInx)
{
    *LpvWheel_Speed = Gu16Wheel_Speed[LenuWheelInx];
}

void WriteCAN_AppSignal_Wheel_Speed(Wheel_SpeedType *LpvWheel_Speed,Car_WheelIndexType LenuWheelInx)
{
	Gu16Wheel_Speed[LenuWheelInx] = *LpvWheel_Speed;
}

/*************************************************************平台级信号9：车辆横摆角速度****************************************** 
      说明：立即发送，无效替代值0，单位°/s，信号故障、CRC错误等都发送0
*/
static Car_Yaw_RateType GfCar_YawRate = 0.0;
static Car_Yaw_AngleType GfCar_YawAngle = 0.0;
/*后验估计协方差*/
static float GfP_cor[2][2] = { {1, 0}, {0, 1} };
/*后验估计输出*/
static float Gfx_cor[2][1] = { {0}, {0} };
static Kalmanfilter_Params GstrKalmanFilterParams;

// 矩阵相乘的C函数
// 参数：
// A: 第一个矩阵，2行2列
// B: 第二个矩阵，2行2列
// C: 待赋值的矩阵，2行2列
void matrix_multiply2x2_2x2(float LfA[2][2], float LfB[2][2], float LfC[2][2])
{
	uint8 i, j, k;
	float Lfsum;
	// 遍历第一个矩阵的每一行
	for (i = 0; i < 2; i++)
	{
		// 遍历第二个矩阵的每一列
		for (j = 0; j < 2; j++)
		{
			Lfsum = 0.0;
			// 计算两个矩阵对应行和列的元素乘积之和
			for (k = 0; k < 2; k++)
			{
				Lfsum += LfA[i][k] * LfB[k][j];
			}
			LfC[i][j] = Lfsum; // 将结果赋值给待赋值的矩阵
		}
	}
}

// 矩阵相乘的C函数
// 参数：
// A: 第一个矩阵，2行2列
// B: 第二个矩阵，2行1列
// C: 待赋值的矩阵，2行1列
void matrix_multiply2x2_2x1(float LfA[2][2], float LfB[2][1], float LfC[2][1])
{
	uint8 i, j, k;
	float Lfsum;
	// 遍历第一个矩阵的每一行
	for (i = 0; i < 2; i++)
	{
		// 遍历第二个矩阵的每一列
		for (j = 0; j < 1; j++)
		{
			Lfsum = 0.0;
			// 计算两个矩阵对应行和列的元素乘积之和
			for (k = 0; k < 2; k++)
			{
				Lfsum += LfA[i][k] * LfB[k][j];
			}
			LfC[i][j] = Lfsum; // 将结果赋值给待赋值的矩阵
		}
	}
}

void KalmanFilter_Car_Yaw_Rate(Car_Yaw_RateType *LpfCar_YawRate, float Lfu, float Lfinterval)
{
	/*先验估计协方差*/
	float LfP_pre[2][2] = { {0, 0}, {0, 0} };
	///*后验估计协方差*/
	//static float LfP_cor[2][2] = { {1, 0}, {0, 1} };
	///*后验估计输出*/
	//static float Lfx_cor[2][1] = { {0}, {0} };
	/*先验估计输出*/
	float Lfx_pre[2][1] = { {0}, {0} };
	/*卡尔曼增益*/
	float LfK[2][1] = { {0}, {0} };
	/*过程噪声协方差*/
	float LfQ[2][2] = { {GstrAPA_OdomCarPara_Ram.fYawRateVar, GstrAPA_OdomCarPara_Ram.fYaw_YawrateCov}, {GstrAPA_OdomCarPara_Ram.fYaw_YawrateCov, GstrAPA_OdomCarPara_Ram.fYawRateVar} };
	/*观测噪声协方差*/
	float LfR = 1;
	/*状态转移矩阵*/
	float LfA[2][2] = { {1, Lfinterval}, {0, 0} };
    //float LfA[2][2] = { {1, 0.02}, {0, 0} };
	/*控制矩阵*/
	float LfB[2][1] = { {0}, {0.01 / GstrAPA_OdomCarPara_Ram.fRear_wheel_axle_length / 3.6} };
	/*观测矩阵*/
	float LfH[1][2] = { {0, 1} };
	/*矩阵计算的临时变量*/
	float LfTemp2x2_1[2][2] = { 0 };
	float LfTemp2x2_2[2][2] = { 0 };
	float LfTemp2x1[2][1] = { 0 };

	if ((GstrCar_WheelPulseInf[CAR_RR_WHEEL].enuCar_WheelPulseDir == CAR_WHEEL_DIR_BACKWARD) && (GstrCar_WheelPulseInf[CAR_RL_WHEEL].enuCar_WheelPulseDir == CAR_WHEEL_DIR_BACKWARD))
	{
		LfB[1][0] = -LfB[1][0];
		if (*LpfCar_YawRate > 0)
		{
			LfR = GstrAPA_OdomCarPara_Ram.fRear_left_R;
		}
		else
		{
			LfR = GstrAPA_OdomCarPara_Ram.fRear_right_R;
		}
	}
	else
	{
		if (*LpfCar_YawRate > 0)
		{
			LfR = GstrAPA_OdomCarPara_Ram.fFront_left_R;
		}
		else
		{
			LfR = GstrAPA_OdomCarPara_Ram.fFront_right_R;
		}
	}

	if (*LpfCar_YawRate > 30 || *LpfCar_YawRate < -30)
	{
		LfR = 100000;
	}

	/*预测方程*/
	/*状态变量预测方程*/
	matrix_multiply2x2_2x1(LfA, Gfx_cor, Lfx_pre);
	Lfx_pre[1][0] = LfB[1][0] * Lfu;
	/*预测协方差方程：k时刻系统估算协方差 = k - 1时刻的系统协方差 + 过程噪声协方差*/
	matrix_multiply2x2_2x2(LfA, GfP_cor, LfTemp2x2_1);
	LfTemp2x2_2[0][0] = LfA[0][0];
	LfTemp2x2_2[1][0] = LfA[0][1];
	LfTemp2x2_2[0][1] = LfA[1][0];
	LfTemp2x2_2[1][1] = LfA[1][0];
	matrix_multiply2x2_2x2(LfTemp2x2_1, LfTemp2x2_2, LfP_pre);
	LfP_pre[0][0] += LfQ[0][0];
	LfP_pre[0][1] += LfQ[0][1];
	LfP_pre[1][0] += LfQ[1][0];
	LfP_pre[1][1] += LfQ[1][1];
	/*更新方程*/
	/*卡尔曼增益方程：卡尔曼增益 = k时刻系统估算协方差 / （k时刻系统估算协方差 + 观测噪声协方差）*/
	LfTemp2x1[0][0] = LfH[0][0];
	LfTemp2x1[1][0] = LfH[0][1];
	matrix_multiply2x2_2x1(LfP_pre, LfTemp2x1, LfK);
	LfK[0][0] /= (LfP_pre[1][1] + LfR);
	LfK[1][0] /= (LfP_pre[1][1] + LfR);
	/*更新最优值方程：k时刻状态变量的最优值 = 状态变量的预测值 + 卡尔曼增益 * （测量值 - 状态变量的预测值）*/
	Gfx_cor[0][0] = Lfx_pre[0][0] + LfK[0][0] * (*LpfCar_YawRate - Lfx_pre[1][0]);
	Gfx_cor[1][0] = Lfx_pre[1][0] + LfK[1][0] * (*LpfCar_YawRate - Lfx_pre[1][0]);
	/*更新协方差方程: 本次的系统协方差付给P_cor为下一次运算准备。*/
	LfTemp2x2_1[0][0] = 1;
	LfTemp2x2_1[0][1] = -LfK[0][0];
	LfTemp2x2_1[1][0] = 0;
	LfTemp2x2_1[1][1] = 1 - LfK[1][0];
	matrix_multiply2x2_2x2(LfTemp2x2_1, LfP_pre, GfP_cor);
}

void KalmanFilter_Init(VehicleTypeType LenuVEH_Type, VehicleWheelType LenuVehicleWheel)
{
	GfP_cor[0][0] = 1;
	GfP_cor[0][1] = 0;
	GfP_cor[1][0] = 0;
	GfP_cor[1][1] = 1;
	Gfx_cor[0][0] = 0;
	Gfx_cor[1][0] = 0;
    if (LenuVEH_Type == VEHTYPE_X02 && (LenuVehicleWheel == VEH_WHEEL_20INCH))
    {
        GstrAPA_OdomCarPara_Ram.fRear_wheel_axle_length = 1.75;
    }
    else if (LenuVEH_Type == VEHTYPE_X02 && (LenuVehicleWheel == VEH_WHEEL_21INCH))
    {
        GstrAPA_OdomCarPara_Ram.fRear_wheel_axle_length = 1.70;
    }
    else if (LenuVEH_Type == VEHTYPE_X03 && (LenuVehicleWheel == VEH_WHEEL_20INCH))
    {
        GstrAPA_OdomCarPara_Ram.fRear_wheel_axle_length = 1.75;
    }
    else if (LenuVEH_Type == VEHTYPE_X04 && (LenuVehicleWheel == VEH_WHEEL_20INCH))
    {
        GstrAPA_OdomCarPara_Ram.fRear_wheel_axle_length = 1.70;
    }
    else if (LenuVEH_Type == VEHTYPE_X04 && (LenuVehicleWheel == VEH_WHEEL_21INCH))
    {
        GstrAPA_OdomCarPara_Ram.fRear_wheel_axle_length = 1.70;
    }
    else if (LenuVEH_Type == VEHTYPE_W02 && (LenuVehicleWheel == VEH_WHEEL_20INCH))
    {
        GstrAPA_OdomCarPara_Ram.fRear_wheel_axle_length = 1.75;
    }
    else
    {
        GstrAPA_OdomCarPara_Ram.fRear_wheel_axle_length = 1.75;
    }
}

float GetBias(Car_Yaw_RateType *LpfCar_YawRate)
{
	static float LfSum = 0;
	static uint16 Lu16Cnt = 0;
	static float LfRes = 0;

	if (Gu16Wheel_Speed[CAR_RL_WHEEL] == 0 && Gu16Wheel_Speed[CAR_RR_WHEEL] == 0)
	{
		LfSum += *LpfCar_YawRate;
		Lu16Cnt++;
	}
	else if (Lu16Cnt > 1500)
	{
		LfRes = LfSum / Lu16Cnt;
		LfSum = 0;
		Lu16Cnt = 0;
	}
	else
	{
		LfSum = 0;
		Lu16Cnt = 0;
	}
	if (((GstrCar_WheelPulseInf[CAR_RR_WHEEL].enuCar_WheelPulseDir == CAR_WHEEL_DIR_BACKWARD) && (GstrCar_WheelPulseInf[CAR_RL_WHEEL].enuCar_WheelPulseDir == CAR_WHEEL_DIR_BACKWARD)) || (GenuCar_Gear == CAN_CAR_GEAR_R))
	{
		return LfRes;
	}
	else
	{
		return LfRes * -1;
	}
}

void ReadCAN_AppSignal_Car_Yaw_Angle(Car_Yaw_AngleType *LpfCar_YawAngle)
{
	*LpfCar_YawAngle = GfCar_YawAngle;
}

void ReadCAN_AppSignal_Car_Yaw_Rate(Car_Yaw_RateType *LpfCar_YawRate)
{
	*LpfCar_YawRate = GfCar_YawRate;
}

void WriteCAN_AppSignal_Car_Yaw_Rate(Car_Yaw_RateType *LpfCar_YawRate)
{
	float Lfu, Lfinterval;
    static uint16 Lu16YawRateTime[2] = {0}, Lu16Wheel_Speed_RL_Last, Lu16Wheel_Speed_RR_Last;

    Lu16YawRateTime[0] = READTAUDCnt_1600ns();

	// *LpfCar_YawRate = (*LpfCar_YawRate + GetBias(LpfCar_YawRate)) / 57.2957795;
    *LpfCar_YawRate = *LpfCar_YawRate / 57.2957795;
	if (((GstrCar_WheelPulseInf[CAR_RR_WHEEL].enuCar_WheelPulseDir == CAR_WHEEL_DIR_FORWARD) || (GstrCar_WheelPulseInf[CAR_RL_WHEEL].enuCar_WheelPulseDir == CAR_WHEEL_DIR_FORWARD)
		|| (GstrCar_WheelPulseInf[CAR_RR_WHEEL].enuCar_WheelPulseDir == CAR_WHEEL_DIR_BACKWARD) || (GstrCar_WheelPulseInf[CAR_RL_WHEEL].enuCar_WheelPulseDir == CAR_WHEEL_DIR_BACKWARD)
		|| (Gu16Car_Speed != 0))
		&& (Gs16Car_StrAngle < -3 || Gs16Car_StrAngle > 3))
	{
        if (ABS(Lu16Wheel_Speed_RR_Last, Lu16Wheel_Speed_RL_Last) > 310)
		{
			Lfu = 0;
		}
		else
		{
			Lfu = (float)(Lu16Wheel_Speed_RR_Last - Lu16Wheel_Speed_RL_Last);
		}

        if (Lu16YawRateTime[0] > Lu16YawRateTime[1])
        {
            Lfinterval = (float)(Lu16YawRateTime[0] - Lu16YawRateTime[1]) * 1.6 / 1000000;
        }
        else
        {
            Lfinterval = (float)(1 + Lu16YawRateTime[0] + 65535 - Lu16YawRateTime[1]) * 1.6 / 1000000;
        }

		if(Lfinterval > 0.01){
		KalmanFilter_Car_Yaw_Rate(LpfCar_YawRate, Lfu, Lfinterval);
		}
		else{
			KalmanFilter_Car_Yaw_Rate(LpfCar_YawRate, Lfu, 0.02);
		}
		GfCar_YawAngle = Gfx_cor[0][0];
		GfCar_YawRate = Gfx_cor[1][0];
	}
    Lu16YawRateTime[1] = Lu16YawRateTime[0];
    Lu16Wheel_Speed_RL_Last = Gu16Wheel_Speed[CAR_RL_WHEEL];
	Lu16Wheel_Speed_RR_Last = Gu16Wheel_Speed[CAR_RR_WHEEL];
}
/*************************************************************比亚迪项目级信号1：系统工作状态 ***************************************** 
*/
static ADCU_WorkModReqType GenuADCU_WorkModReq = WORK_MODE_NONE;
void ReadCAN_AppSignal_ADCU_WorkModReq(ADCU_WorkModReqType *LpenuADCU_WorkModReq)
{
    *LpenuADCU_WorkModReq = GenuADCU_WorkModReq;
}
void WriteCAN_AppSignal_ADCU_WorkModReq(ADCU_WorkModReqType LenuADCU_WorkModReq)
{
    GenuADCU_WorkModReq = LenuADCU_WorkModReq;

}

/*************************************************************比亚迪项目级信号2： APA功能状态反馈 ***************************************** 
*/
static APA_APA_StatmsType GenuAPA_APA_Statms = APA_STS_NONE;
void ReadCAN_AppSignal_APA_APA_Statms(APA_APA_StatmsType *LpenuAPA_APA_Statms)
{
    *LpenuAPA_APA_Statms = GenuAPA_APA_Statms;
}
void WriteCAN_AppSignal_APA_APA_Statms(APA_APA_StatmsType LenuAPA_APA_Statms)
{
    GenuAPA_APA_Statms = LenuAPA_APA_Statms;
}

/*************************************************************比亚迪项目级信号3： APA PAS功能开关***************************************** 
*/
static APA_PAS_Function_ReqType GenuAPA_PAS_Function_Req = APA_PAS_OFF;
void ReadCAN_AppSignal_APA_PAS_Function_Req(APA_PAS_Function_ReqType *LpenuAPA_PAS_Function_Req)
{
    *LpenuAPA_PAS_Function_Req = GenuAPA_PAS_Function_Req;
}
void WriteCAN_AppSignal_APA_PAS_Function_Req(APA_PAS_Function_ReqType LenuAPA_PAS_Function_Req)
{
    GenuAPA_PAS_Function_Req = LenuAPA_PAS_Function_Req;
}

/*************************************************************比亚迪项目级信号4： 泊车车位ID选择，0x0: No Space,0xFF: Invalid***************************************** 
*/
static ADCU_PSL_ParkingSlotID_SelectedType Gu8ADCU_PSL_ParkingSlotID_Selected = 0x0;
void ReadCAN_AppSignal_ADCU_PSL_ParkingSlotID_Selected(ADCU_PSL_ParkingSlotID_SelectedType *Lpu8SlotID_Selected)
{
    *Lpu8SlotID_Selected = Gu8ADCU_PSL_ParkingSlotID_Selected;
}
void WriteCAN_AppSignal_ADCU_PSL_ParkingSlotID_Selected(ADCU_PSL_ParkingSlotID_SelectedType Lu8SlotID_Selected)
{
    Gu8ADCU_PSL_ParkingSlotID_Selected = Lu8SlotID_Selected;
}

/*************************************************************比亚迪项目级信号5： 控制器时间同步信号（TBD）***************************************** 
*/
static ADCU_Work_TimeStampType Gu64ADCU_Work_TimeStamp = 0x0;
void ReadCAN_AppSignal_ADCU_Work_TimeStamp(ADCU_Work_TimeStampType *Lpu64ADCU_Work_TimeStamp)
{
    *Lpu64ADCU_Work_TimeStamp = Gu64ADCU_Work_TimeStamp;
}
void WriteCAN_AppSignal_ADCU_Work_TimeStamp(ADCU_Work_TimeStampType Lu64ADCU_Work_TimeStamp)
{
    Gu64ADCU_Work_TimeStamp = Lu64ADCU_Work_TimeStamp;
}

/*************************************************************比亚迪项目级信号6：电源档位***************************************** 
*/
static BCMPower_GearType GenuBCMPower_Gear = POWER_GEAR_INVALID;
void ReadCAN_AppSignal_BCMPower_Gear(BCMPower_GearType *LpenuBCMPower_Gear)
{
    *LpenuBCMPower_Gear = GenuBCMPower_Gear;
}
void WriteCAN_AppSignal_BCMPower_Gear(BCMPower_GearType LenuBCMPower_Gear)
{
    GenuBCMPower_Gear = LenuBCMPower_Gear;
}

/*************************************************************比亚迪项目级信号7：电源档位***************************************** 
*/
static SDWReq_Switch_StateType GenuSDWReq_Switch_State = SDW_SWITCH_ON;/* 按照信号矩阵，默认值是0x2 */
void ReadCAN_AppSignal_SDWReq_Switch_State(SDWReq_Switch_StateType *LpenuSDWReq_Switch_State)
{
    *LpenuSDWReq_Switch_State = GenuSDWReq_Switch_State;
}
void WriteCAN_AppSignal_SDWReq_Switch_State(SDWReq_Switch_StateType LenuSDWReq_Switch_State)
{
    GenuSDWReq_Switch_State = LenuSDWReq_Switch_State;
}

/*************************************************************比亚迪项目级信号8：OTA进入模式请求***************************************** 
*/
static Medium_Enter_OTA_Mode_ReqType GenuEnter_OTA_Mode_Req = NO_OTA_MODE_REQ;
void ReadCAN_AppSignal_Enter_OTA_Mode_Req(Medium_Enter_OTA_Mode_ReqType *LpenuEnter_OTA_Mode_Req)
{
    *LpenuEnter_OTA_Mode_Req = GenuEnter_OTA_Mode_Req;
}
void WriteCAN_AppSignal_Enter_OTA_Mode_Req(Medium_Enter_OTA_Mode_ReqType LenuEnter_OTA_Mode_Req)
{
    GenuEnter_OTA_Mode_Req = LenuEnter_OTA_Mode_Req;
}

/*************************************************************比亚迪项目级信号9：域控发出时间同步主时基***************************************** 
*/
static ADAS_SYNC_TimeType GstrADAS_SYNC_Time =
{
    .enuADAS_SYNC_Type = ADAS_SYNC_NONE,
    .u8ADAS_SYNC_TimeDomain = 0,
    .u8ADAS_SYNC_OVS = 0,
    .enuADAS_SYNC_SGW = ADAS_SYNC_TO_GTM,
    .u8ADAS_SYNC_Reserved = 0,
    .u32ADAS_SYNC_SyncTime = 0
};
    
void ReadCAN_AppSignal_ADAS_SYNC_Time(ADAS_SYNC_TimeType *LpvADAS_SYNC_Time)
{
    *LpvADAS_SYNC_Time = GstrADAS_SYNC_Time;
}
void WriteCAN_AppSignal_ADAS_SYNC_Time(ADAS_SYNC_TimeType *LpvADAS_SYNC_Time)
{
    GstrADAS_SYNC_Time = *LpvADAS_SYNC_Time;
}

/*************************************************************比亚迪项目级信号10：里程信息*****************************************
*/
static uint32 Gu32Total_Distance = 0;
void ReadCAN_AppSignal_Total_Distance(uint32* LpvTotal_Distance)
{
    *LpvTotal_Distance = Gu32Total_Distance;
}
void WriteCAN_AppSignal_Total_Distance(uint32* LpvTotal_Distance)
{
    Gu32Total_Distance = *LpvTotal_Distance;
}

/*************************************************************比亚迪项目级信号11：同步时间*****************************************
*/
void ReadCAN_AppSignal_ADAS_CAN_SYNC_Time(uint32* LpvADAS_SYNC_Time)
{
    *LpvADAS_SYNC_Time = Gu32CANTimeSysn_GlobalTimeBase;
}
void WriteCAN_AppSignal_ADAS_CAN_SYNC_Time(uint32* LpvADAS_SYNC_Time)
{
    Gu32CANTimeSysn_GlobalTimeBase = *LpvADAS_SYNC_Time;
}

/*************************************************************比亚迪项目级信号12：驻车辅助开关软按键***************************************** 
*/
static Park_Auxi_Swch_Soft_KeyType GenuPark_Auxi_Swch_Soft_Key = SOFT_KEY_INVALID;
void ReadCAN_AppSignal_Park_Auxi_Swch_Soft_Key(Park_Auxi_Swch_Soft_KeyType *LpenuPark_Auxi_Swch_Soft_Key)
{
    *LpenuPark_Auxi_Swch_Soft_Key = GenuPark_Auxi_Swch_Soft_Key;
}
void WriteCAN_AppSignal_Park_Auxi_Swch_Soft_Key(Park_Auxi_Swch_Soft_KeyType LenuPark_Auxi_Swch_Soft_Key)
{
    GenuPark_Auxi_Swch_Soft_Key = LenuPark_Auxi_Swch_Soft_Key;
}

/*************************************************************!!!!!! 以下是理想项目级信号接口!!!!!!*****************************************/

/*************************************************************理想项目级信号1：PSL使能工作状态 ***************************************** 
*/
static ADAS_PSL_EnableStsType GenuADAS_PSL_EnableSts = PSL_ENABLE;
void ReadCAN_AppSignal_ADAS_PSL_EnableSts(ADAS_PSL_EnableStsType *LpenuADAS_PSL_EnableSts)
{
    *LpenuADAS_PSL_EnableSts = GenuADAS_PSL_EnableSts;
}
void WriteCAN_AppSignal_ADAS_PSL_EnableSts(ADAS_PSL_EnableStsType LenuADAS_PSL_EnableSts)
{
    GenuADAS_PSL_EnableSts = LenuADAS_PSL_EnableSts;
}

/*************************************************************理想项目级信号2：PAS使能工作状态 ***************************************** 
*/
static ADAS_PAS_EnableStsType GenuADAS_PAS_EnableSts = PAS_ENABLE;
void ReadCAN_AppSignal_ADAS_PAS_EnableSts(ADAS_PAS_EnableStsType *LpenuADAS_PAS_EnableSts)
{
    *LpenuADAS_PAS_EnableSts = GenuADAS_PAS_EnableSts;
}
void WriteCAN_AppSignal_ADAS_PAS_EnableSts(ADAS_PAS_EnableStsType LenuADAS_PAS_EnableSts)
{
    GenuADAS_PAS_EnableSts = LenuADAS_PAS_EnableSts;
}

/*************************************************************理想项目级信号3：ADAS APA的工作状态 ***************************************** 
*/
static ADAS_APAStatusType GenuADAS_APAStatus = APA_NONE;
void ReadCAN_AppSignal_ADAS_APAStatus(ADAS_APAStatusType *LpenuADAS_APAStatus)
{
    *LpenuADAS_APAStatus = GenuADAS_APAStatus;
}
void WriteCAN_AppSignal_ADAS_APAStatus(ADAS_APAStatusType LenuADAS_APAStatus)
{
    GenuADAS_APAStatus = LenuADAS_APAStatus;
}

/*************************************************************理想项目级信号4：ADAS_slot_ID_Selected ***************************************** 
*/
static ADAS_slot_ID_SelectedType Gu8Slot_ID_Selected = 0x00;
void ReadCAN_AppSignal_ADAS_slot_ID_Selected(ADAS_slot_ID_SelectedType *Lpu8Slot_ID_Selected)
{
    *Lpu8Slot_ID_Selected = Gu8Slot_ID_Selected;
}
void WriteCAN_AppSignal_ADAS_slot_ID_Selected(ADAS_slot_ID_SelectedType Lu8Slot_ID_Selected)
{
    Gu8Slot_ID_Selected = Lu8Slot_ID_Selected;
}

/*************************************************************理想项目级信号5：LowVolPwrMdFlag ***************************************** 
*/
static LowVolPwrMdFlagType GenuLowVolPwrMdFlag = LOCAL_MODE;
void ReadCAN_AppSignal_LowVolPwrMdFlag(LowVolPwrMdFlagType *LpenuLowVolPwrMdFlag)
{
    *LpenuLowVolPwrMdFlag = GenuLowVolPwrMdFlag;
}
void WriteCAN_AppSignal_LowVolPwrMdFlag(LowVolPwrMdFlagType LenuLowVolPwrMdFlag)
{
    GenuLowVolPwrMdFlag = LenuLowVolPwrMdFlag;
}


/*************************************************************理想项目级信号6：GenuLowVolPwrMd ***************************************** 
*/
static LowVolPwrMdType GenuLowVolPwrMd = POWER_OFF;
void ReadCAN_AppSignal_LowVolPwrMd(LowVolPwrMdType *LpLowVolPwrMd)
{
    *LpLowVolPwrMd = GenuLowVolPwrMd;
}
void WriteCAN_AppSignal_LowVolPwrMd(LowVolPwrMdType LenuLowVolPwrMd)
{
    GenuLowVolPwrMd = LenuLowVolPwrMd;
}

static LowVolPwrMdType GenuLowVolPwrMd_DTCDiag = POWER_OFF;
void ReadCAN_AppSignal_LowVolPwrMd_DTCDiag(LowVolPwrMdType* LpLowVolPwrMd_DTCDiag)
{
    *LpLowVolPwrMd_DTCDiag = GenuLowVolPwrMd_DTCDiag;
}
void WriteCAN_AppSignal_LowVolPwrMd_DTCDiag(LowVolPwrMdType LenuLowVolPwrMd_DTCDiag)
{
    GenuLowVolPwrMd_DTCDiag = LenuLowVolPwrMd_DTCDiag;
}
/*************************************************************理想项目级信号7：车型信息***************************************** 
*/
static VehicleTypeType  GenuVEH_Type = VEHTYPE_X02;    /* 未配置，默认X02 */
void ReadCAN_AppSignal_VEH_Type(VehicleTypeType *LpenuVEH_Type)
{
    *LpenuVEH_Type = GenuVEH_Type;
}
void WriteCAN_AppSignal_VEH_Type(VehicleTypeType LenuVEH_Type)
{
    GenuVEH_Type = LenuVEH_Type;
}

/*************************************************************理想项目级信号8：车型等级信息***************************************** 
*/
static VehicleCfgLevelType  GenuVehicleCfgLevel = VEH_NO_MAX;    /* 未配置，默认非MAX车型 */
void ReadCAN_AppSignal_VehicleCfgLevel(VehicleCfgLevelType *LpenuVehicleCfgLevel)
{
    *LpenuVehicleCfgLevel = GenuVehicleCfgLevel;
}
void WriteCAN_AppSignal_VehicleCfgLevel(VehicleCfgLevelType LenuVehicleCfgLevel)
{
    GenuVehicleCfgLevel = LenuVehicleCfgLevel;
}

/*************************************************************理想项目级信号9：轮胎大小信息***************************************** 
*/
static VehicleWheelType  GenuVehicleWheel = VEH_WHEEL_20INCH;/*20英寸全涂轮毂*/
void ReadCAN_AppSignal_VehicleWheel(VehicleWheelType *LpenuVehicleWheel)
{
    *LpenuVehicleWheel = GenuVehicleWheel;
}
void WriteCAN_AppSignal_VehicleWheel(VehicleWheelType LenuVehicleWheel)
{
    GenuVehicleWheel = LenuVehicleWheel;
}

/*************************************************************理想项目级信号10： DTC PAS功能降级接口***************************************** 
*/
static Boolean GblPASFuncDG = FALSE;
Boolean Read_PASFunctionDGFlag(void)
{
    return GblPASFuncDG;
}
void Write_PASFunctionDGFlag(Boolean Value)
{
    GblPASFuncDG = Value;
}

/*************************************************************理想项目级信号11： DTC PSL功能降级接口***************************************** 
*/
static Boolean GblPSLFuncDG = FALSE;

Boolean Read_PSLFunctionDGFlag(void)
{
    return GblPSLFuncDG;
}

void Write_PSLFunctionDGFlag(Boolean Value)
{
    GblPSLFuncDG = Value;
}
/*************************************************************理想项目级信号12： 使用电源模式标志位2来替代召唤模式的接口***************************************** 
*/

LowVolPwrMdFlag2_Type GLowVolPwrMdFlag2 = LOWPOWER_MODFLAG2_LOCAL;
void ReadComSignal_LowVolPwrMdFlag2(LowVolPwrMdFlag2_Type* LpvSts)
{
    *LpvSts = GLowVolPwrMdFlag2;
}

void WriteComSignal_LowVolPwrMdFlag2(LowVolPwrMdFlag2_Type* LpvSts)
{
    GLowVolPwrMdFlag2 = *LpvSts;
}

/*************************************************************理想项目级信号13： DTC使能弹窗的接口***************************************** 
*/
Boolean GblDTCWindow_Enable = FALSE;

Boolean Read_DTCWindow_Enable(void)
{
    return GblDTCWindow_Enable;
}

void Write_DTCWindow_Enable(Boolean Value)
{
    GblDTCWindow_Enable = Value;
}
/*************************************************************理想项目级信号14：车型平台*****************************************
*/
static VehiclePlatformType  GenuVehiclePlatform = VEH_X_SERIES;    /* 未配置，默认非MAX车型 */
void ReadCAN_AppSignal_VehiclePlatform(VehiclePlatformType* LpenuVehiclePlatform)
{
    *LpenuVehiclePlatform = GenuVehiclePlatform;
}
void WriteCAN_AppSignal_VehiclePlatform(VehiclePlatformType LenuVehiclePlatform)
{
    GenuVehiclePlatform = LenuVehiclePlatform;
}
/******************************************************************************/
/******************************************************************************/
/****************************** Private Function ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/******************************* Public Function ******************************/
/******************************************************************************/
/******************************************************************************/



