#include "DSI3_COM.h"
#include "DSI3_COM.h"

#include "AK2_MCU_Drv.h"

#define TDMA_PACKETCOUNT DSISlaveSnsNum

const FRAME_TDMA_SCHEME_str FRAME_TDMA_SCHEME = 
{
    .PDCM_PERIOD = TDMA_PDCM_PERIOD,

    .BP           =0,
    .PacketCount  =TDMA_PACKETCOUNT,

    .PacketSCHEME =
    {
            [0] ={
                    .EarliestStartTime = TDMA_PDCM_S1_ET,
                    .LatestStartTime   = TDMA_PDCM_S1_LT,
                    .ID          = TDMA_ID,
                    .SymbolCount = TDMA_PDCMSymbCnt,
            },
            [1] ={
                    .EarliestStartTime = TDMA_PDCM_S2_ET,
                    .LatestStartTime   = TDMA_PDCM_S2_LT,
                    .ID          = TDMA_ID,
                    .SymbolCount = TDMA_PDCMSymbCnt,
            },
            [2] ={
                    .EarliestStartTime = TDMA_PDCM_S3_ET,
                    .LatestStartTime   = TDMA_PDCM_S3_LT,
                    .ID          = TDMA_ID,
                    .SymbolCount = TDMA_PDCMSymbCnt,
            },
    },

};

const DSICOM_SetParam_st DSICOM_Param[DSIMasterNum] = 
{
    
   [DSIMaster0]={.VDSI= e_VDSI_5_1V,
			    .Wave_Fall = 0,
			    .Wave_Rise = 0,
			    
			    .DSIShl_SetParam[DSIChl1] = {
			        .FCCBitTime = e_FCCBitTime_8us,
			        .RCCChipTime = SnsRCCChipTime,
			        .DSI_Timing_Offset = 5,
			        .DM_Start_Time_BC = 60,
			        .pFRAME_TDMA_SCHEME = (FRAME_TDMA_SCHEME_str *)&FRAME_TDMA_SCHEME,
			    },

			    .DSIShl_SetParam[DSIChl2] = {
			        .FCCBitTime = e_FCCBitTime_8us,
			        .RCCChipTime = SnsRCCChipTime,
			        .DSI_Timing_Offset = 5,
			        .DM_Start_Time_BC = 60,
			        .pFRAME_TDMA_SCHEME = (FRAME_TDMA_SCHEME_str *)&FRAME_TDMA_SCHEME,
			    },

			    .EnDCR1BFallIntFunc = DSIMaster0_EnDCR1BFallInt,
			    .EnDCR2BFallIntFunc = DSIMaster0_EnDCR2BFallInt,
			    .DisDCR1BIntFunc    = DSIMaster0_DisDCR1BInt,
			    .DisDCR2BIntFunc    = DSIMaster0_DisDCR2BInt,
			    .EnRFCFallIntFunc   = DSIMaster0_EnRFCInt,
			    .EnINTBFallIntFunc  = DSIMaster0_EnINTBInt,

			    .DisRFCFallIntFunc   = DSIMaster0_DisRFCInt,
			    .DisINTBFallIntFunc  = DSIMaster0_DisINTBInt,

			    .ReadINTB_PinFunc   = DSIMaster0_ReadINTB_Pin,
			    .ReadRFC_PinFunc    = DSIMaster0_ReadRFC_Pin,
			        
			    .SetRESB_PinFunc    = DSIMaster0_SetRESB_Pin,
			    .ClearRESB_PinFunc  = DSIMaster0_ClearRESB_Pin,
			    .DSI_SPI_Interface  = {.DSI_Spi_AsyncTransmitFunc = DSIMaster0_Spi_AsyncTransmitFunc,}
			},

	[DSIMaster1]={.VDSI= e_VDSI_5_1V,
			    .Wave_Fall = 0,
			    .Wave_Rise = 0,
			    
			    .DSIShl_SetParam[DSIChl1] = {
			        .FCCBitTime = e_FCCBitTime_8us,
			        .RCCChipTime = SnsRCCChipTime,
			        .DSI_Timing_Offset = 5,
			        .DM_Start_Time_BC = 60,
			        .pFRAME_TDMA_SCHEME = (FRAME_TDMA_SCHEME_str *)&FRAME_TDMA_SCHEME,
			    },

			    .DSIShl_SetParam[DSIChl2] = {
			        .FCCBitTime = e_FCCBitTime_8us,
			        .RCCChipTime = SnsRCCChipTime,
			        .DSI_Timing_Offset = 5,
			        .DM_Start_Time_BC = 60,
			        .pFRAME_TDMA_SCHEME = (FRAME_TDMA_SCHEME_str *)&FRAME_TDMA_SCHEME,
			    },

			    .EnDCR1BFallIntFunc = DSIMaster1_EnDCR1BFallInt,
			    .EnDCR2BFallIntFunc = DSIMaster1_EnDCR2BFallInt,
			    .DisDCR1BIntFunc    = DSIMaster1_DisDCR1BInt,
			    .DisDCR2BIntFunc    = DSIMaster1_DisDCR2BInt,
			    .EnRFCFallIntFunc   = DSIMaster1_EnRFCInt,
			    .EnINTBFallIntFunc  = DSIMaster1_EnINTBInt,

			    .DisRFCFallIntFunc   = DSIMaster1_DisRFCInt,
			    .DisINTBFallIntFunc  = DSIMaster1_DisINTBInt,

			    .ReadINTB_PinFunc   = DSIMaster1_ReadINTB_Pin,
			    .ReadRFC_PinFunc    = DSIMaster1_ReadRFC_Pin,
			        
			    .SetRESB_PinFunc    = DSIMaster1_SetRESB_Pin,
			    .ClearRESB_PinFunc  = DSIMaster1_ClearRESB_Pin,
			    .DSI_SPI_Interface  = {.DSI_Spi_AsyncTransmitFunc = DSIMaster1_Spi_AsyncTransmitFunc,}
			}
};




