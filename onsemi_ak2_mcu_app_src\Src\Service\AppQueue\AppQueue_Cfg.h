#include "types.h"

typedef enum
{
    QUEUE_UART_PRINT = 0u,
    QUEUE_UART_DEBUG_RX,
    QUEUE_NUM,
} QueueIdxType;

typedef struct sQueueInfoType
{
    uint16              BufSize;
    uint16              TailIdx;
    uint16              HeadIdx;
    uint16              QueueSize;
    uint8*              pDataBuf;
} QueueInfoType;

#define QUEUE_UART_PRINT_BUF_SIZE           1024u
#define QUEUE_UART_DEBUG_RX_BUF_SIZE        100u

extern QueueInfoType GstraQueueInfo[QUEUE_NUM];

#define GET_QUEUE_INFO_PTR(Idx)             &GstraQueueInfo[(Idx)]
