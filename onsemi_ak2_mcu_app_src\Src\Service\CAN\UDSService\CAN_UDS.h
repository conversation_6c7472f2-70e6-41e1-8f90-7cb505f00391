
#ifndef __UDSPUB_H__
#define __UDSPUB_H__

/******************************************************************************* 
* Includes 
********************************************************************************/ 
#include "Types.h"

#if (CAN_UDS_DEBUG_PRINT == (STD_ON))
#define CAN_UDS_DEBUG_PRINTF                Lhprintf
#else
#define CAN_UDS_DEBUG_PRINTF(...)
#endif



/****************************************************************************** 
* Constants and macros 
*******************************************************************************/
#define UDS_FALSE                   (uint8) 0x00U
#define UDS_TRUE                    (uint8) 0x01U

#define UDS_MANAGE_PERIOD           1U
#define UDS_P2CAN_Server            50         
#define UDS_P2_78_CAN_Server        5000 
#define UDS_P2                      0U                              /* 第一次响应78忙的等待时间 */
#define UDS_P2STAR                  (3000/UDS_MANAGE_PERIOD)        /* 周期响应的78时间控制     */
#define UDS_SESS_TIMEOUT            (5000U / UDS_MANAGE_PERIOD)

#define MAXBuffCnt                  258U        /* 传输一包数据最大的字节数为258 */

/* 诊断通信控制 */
#define NormalMessages              0x01
#define NetworkMessages             0x02
#define NormalANDNetworkMessages    0x03

#define ENABLERXANDTX               0x00
#define ENABLERXDISABLETX           0x01
#define DISABLERXENABLETX           0x02
#define DISABLERXANDTX              0x03


/* NRC78请求服务 */
#define NRC78_NONE                      SDF_U8_VALUE0
#define NRC78_ECURESET                  SDF_U8_VALUE1
#define NRC78_WRITEDATABYDIDS           SDF_U8_VALUE6
#define NRC78_PROGRAMSEESION            SDF_U8_VALUE10
#define NRC78_CLEARDTCINFO              SDF_U8_VALUE9
#define NRC78_CHECKPROCONDITION			SDF_U8_VALUE2


#define UDS_LEN_NRC                     3U
#define UDS_NRC                         (uint8)0x7FU
#define UDS_ACK                         (uint8) 0x40U    /* Positive answer */
#define UDS_NRC_11                      (uint8) 0x11U    /* Non-supported service */
#define UDS_NRC_12                      (uint8) 0x12U    /* Non-supported sub-service */
#define UDS_NRC_13                      (uint8) 0x13U    /* Invalid request length or invalid format */
#define UDS_NRC_22                      (uint8) 0x22U    /* Unsatisfied conditions */
#define UDS_NRC_24                      (uint8) 0x24U    /* Links not respected */
#define UDS_NRC_31                      (uint8) 0x31U    /* Invalid parameters */
#define UDS_NRC_33                      (uint8) 0x33U    /* ECU is locked */
#define UDS_NRC_35                      (uint8) 0x35U    /* Invalid key */
#define UDS_NRC_36                      (uint8) 0x36U    /* Unlocked attempt overrun */
#define UDS_NRC_37                      (uint8) 0x37U    /* delay between 2 unlocked attempt is elapsed */
#define UDS_NRC_72                      (uint8) 0x72U    /* Erasing or writing error volatil memory */
#define UDS_NRC_73                      (uint8) 0x73U    /* Error in erasing or writing sequence  */
#define UDS_NRC_78                      (uint8) 0x78U    /* Request well received / Answer constitution in progress */
#define UDS_NRC_7E                      (uint8) 0x7EU    /* Sub-service supported by the ECU but not in the active session */
#define UDS_NRC_7F                      (uint8) 0x7FU    /* Service supported by the ECU but not in the active session */
#define UDS_NRC_92                      (uint8) 0x92U    /* Voltage Not Satisfied*/
#define UDS_ERR_COHE                    (uint8) 0xFFU    /* SDF UDS error */

/* Routine control type */
#define NONE_ROUTINE_REQUEST            (uint8) 0x00U                 
#define START_ROUTINE                   (uint8) 0x01U
#define STOP_ROUTINE                    (uint8) 0x02U
#define REQUEST_ROUTINE_RESULT          (uint8) 0x03U

/* Routine status */
#define ROUTINE_INIT                    0x00U
#define ROUTINE_IN_PROGRESS             0x01U
#define ROUTINE_FINISHED_OK             0x02U
#define ROUTINE_FINISHED_NOK            0x03U
#define ROUTINE_STOPPED                 0x04U
#define ROUTINE_NOT_STARTED             0x05U
#define ROUTINE_NOT_SUPPORTED           0x0FU

/* Addressing mode (functionnal or physical) */
#define UDS_ADDR_PHYSICAL               0x01U
#define UDS_ADDR_FUNCTIONAL             0x02U

/* Long treatment enumeration steps */
#define UDS_LONG_REQ_OFF                0U    /* No NACK_78 running */
#define UDS_LONG_REQ_ON                 1U    /* NACK_78 is sent every UDS_NRC78_PERIOD ms */
#define UDS_LONG_REQ_END                2U    /* The real response of the service has been received from the application */

/* DTC 设置 */
#define DTC_SETTING_ON                  0
#define DTC_SETTING_OFF                 1

/* 安全等级 */
#define SAFE_LEVEL_0                    0
#define SAFE_LEVEL_1                    1
#define LH_SAFE_LEVEL                   0x55

/* 诊断会话模式 */
#define UDS_SESSION_DEFAULT             0x01u
#define UDS_SESSION_PROGRAMMING         0x02u
#define UDS_SESSION_EXTENDED            0x03u
#define UDS_SESSION_MNNUFACTURER        0x40u
#define UDS_SESSION_SYSSUPPLIER         0x60u

#define MASKDTC                         SDF_U8_VALUE1
#define SUPPORTDTC                      SDF_U8_VALUE2

/********************************************************************************/
/* Long request behavior enabling */
typedef struct
{
    uint8 GcNRC78Event;                      /* 忙响应事件       */
    uint8 GcSuppresPosRspMsgIndicationBit;   /* 抑制肯定响应标志 */
    uint8 m_eState;                          /* Sequence step    */
    uint16 CycleTimer;                        /* Counter for periodic transmissions */
    uint8 m_aubDiagNack78Data[UDS_LEN_NRC];  /* Data buffer for NRC78 only */
} UdsLongRequestStr;

typedef struct
{
    uint8 UdsAddrMode;
    uint8 DTCSettingType;
    uint8 SessionMode;
    uint16 KeepSessionTimeCnt;
    uint8 SecurityLevel;
    uint16 AccessLockTime;
    uint16 UDSTxLen;
    uint8 *pUDSTxBuff;
}UdsServiceStr;

/****************************************************************************** 
* External objects 
********************************************************************************/ 
extern UdsLongRequestStr GsLongRequest;
extern UdsServiceStr GsUdsService;

extern uint8 GcUdsData[500];                                  /* 保存写入的DID的数据 */
extern uint8 GcErrorKeyCnt;
extern uint16 Gu16RandSeedCnt;
/******************************************************************************* 
* Global Functions 
********************************************************************************/ 
void CAN_UDSInit (void);
void UDS_Manage (void);
uint8 UDS_NegativeAnswer (uint16 *pLwLen, uint8 *pUdsData, uint8 NRCStatus);
void UDS_LongRequestEnd (uint16 LwLen,uint8 *pUdsData, uint8 LcRespondStatus);
void DiagServiceManage(uint16 LwLen, uint8 *pUdsData);
void HandleNRC78Event(void);
void UDS_ServiceManage(void);
#if (CALIBRATION_EN == STD_ON)
void generate_crc8_table(void);
#endif
#endif      /* UDS_PUB_H */
