/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: CrcRegCfg.h
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/
#ifndef __CRCCFG_H
#define __CRCCFG_H

/***********************************************************************************************************************
Macro definitions (Register bit)
***********************************************************************************************************************/
#define _DCRA_INIT_DATA                           (0xFFFFU)
#define _DCRA_CLEAR_DATA                          (0x0U)
/*
    CRC control register (DCRAnCTL)
*/
/* Input data width selection (DCRA0ISZ[2-1]) */
#define _DCRA_INPUT_32BIT                         (0x00U) /* set input data width to 32 bits */
#define _DCRA_INPUT_16BIT                         (0x02U) /* set input data width to 16 bits */
#define _DCRA_INPUT_8BIT                          (0x04U) /* set input data width to 8 bits */
/* CRC generating polynomial selection (DCRA0POL) */
#define _DCRA_32_ETHERNET                         (0x00U)
#define _DCRA_16_CCITT                            (0x01U)


#endif
