import sys

def reverse_bits(value, width=8):
    """反转位顺序"""
    result = 0
    for i in range(width):
        if (value >> i) & 1:
            result |= 1 << (width - 1 - i)
    return result

def calculate_crc8_variants(data, polynomial, init_value, reflect_in=False, reflect_out=False, xor_out=0):
    """计算CRC8，支持不同的变体"""
    crc = init_value
    
    # 初始化CRC表
    crc_table = []
    for i in range(256):
        crc_value = i if not reflect_in else reverse_bits(i)
        for j in range(8):
            if crc_value & 0x80:
                crc_value = (crc_value << 1) ^ polynomial
            else:
                crc_value <<= 1
            crc_value &= 0xFF  # 确保结果是8位
        crc_table.append(crc_value)
    
    # 计算CRC
    for byte in data:
        byte_processed = byte if not reflect_in else reverse_bits(byte)
        crc = crc_table[(crc ^ byte_processed) & 0xFF]
    
    # 最终处理
    if reflect_out:
        crc = reverse_bits(crc)
    
    crc ^= xor_out
    return crc & 0xFF

def find_matching_variants(data, expected_crc, init_value):
    """查找匹配的多项式和变体"""
    matching_configs = []
    
    # 尝试所有可能的多项式值和变体
    for polynomial in range(1, 256):
        for reflect_in in [False, True]:
            for reflect_out in [False, True]:
                for xor_out in [0, 0xFF]:  # 常见的异或输出值
                    crc = calculate_crc8_variants(data, polynomial, init_value, reflect_in, reflect_out, xor_out)
                    if crc == expected_crc:
                        matching_configs.append((polynomial, reflect_in, reflect_out, xor_out))
    
    return matching_configs

def polynomial_to_expression(polynomial):
    """将多项式转换为表达式"""
    binary = bin(polynomial)[2:].zfill(8)
    
    terms = []
    for i, bit in enumerate(binary):
        if bit == '1':
            power = 7 - i
            if power == 0:
                terms.append('1')
            elif power == 1:
                terms.append('x')
            else:
                terms.append(f'x^{power}')
    
    return ' + '.join(terms)

# 已知数据、CRC8结果和初始值
data = [0xF8, 0x09, 0x30]
expected_crc = 0x1F
init_value = 0xFF

print('尝试推导CRC8多项式值和变体...')
print(f'数据: {[f"0x{b:02X}" for b in data]}')
print(f'预期CRC8: 0x{expected_crc:02X}')
print(f'初始值: 0x{init_value:02X}')
print()

# 推导匹配的配置
matching_configs = find_matching_variants(data, expected_crc, init_value)

if matching_configs:
    print(f'找到 {len(matching_configs)} 个匹配的配置:')
    
    # 检查是否有标准多项式
    standard_polynomials = {
        0x07: "CRC-8",
        0x31: "CRC-8/MAXIM",
        0x39: "CRC-8/WCDMA",
        0x1D: "CRC-8/ROHC",
        0x2F: "CRC-8/DARC",
        0x9B: "CRC-8/I-CODE",
        0xD5: "CRC-8/EBU"
    }
    
    # 显示前10个匹配的配置
    for i, (polynomial, reflect_in, reflect_out, xor_out) in enumerate(matching_configs[:10]):
        poly_name = standard_polynomials.get(polynomial, "自定义")
        print(f'配置 {i+1}:')
        print(f'  多项式: 0x{polynomial:02X} - {polynomial_to_expression(polynomial)} ({poly_name})')
        print(f'  反转输入: {reflect_in}')
        print(f'  反转输出: {reflect_out}')
        print(f'  异或输出: 0x{xor_out:02X}')
        
        # 验证
        verification_crc = calculate_crc8_variants(data, polynomial, init_value, reflect_in, reflect_out, xor_out)
        print(f'  验证CRC8: 0x{verification_crc:02X} ({"成功" if verification_crc == expected_crc else "失败"})')
        print()
    
    if len(matching_configs) > 10:
        print(f'还有 {len(matching_configs) - 10} 个匹配的配置未显示...')
    
    # 测试第一个配置
    polynomial, reflect_in, reflect_out, xor_out = matching_configs[0]
    
    # 测试另一个示例
    print('\n测试另一个示例:')
    test_data = [0xF8, 0x08, 0x0A]
    test_crc = calculate_crc8_variants(test_data, polynomial, init_value, reflect_in, reflect_out, xor_out)
    print(f'数据: {[f"0x{b:02X}" for b in test_data]}')
    print(f'计算CRC8: 0x{test_crc:02X}')
    
    # 测试第二个示例
    print('\n测试第二个示例:')
    test_data2 = [0xF4, 0x6F, 0x34, 0xD7, 0x34, 0x3D, 0xEF, 0x3D, 0xEF, 0x1F, 0xBF, 0x00, 0x28]
    test_crc2 = calculate_crc8_variants(test_data2, polynomial, init_value, reflect_in, reflect_out, xor_out)
    print(f'数据: {[f"0x{b:02X}" for b in test_data2]}')
    print(f'计算CRC8: 0x{test_crc2:02X}')
else:
    print('未找到匹配的配置')
