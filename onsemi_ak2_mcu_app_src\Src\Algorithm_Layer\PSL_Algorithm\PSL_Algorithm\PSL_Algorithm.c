/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * PSL_Algorithm.c: 
 * Created on: 2023-2-22 17:30
 * Original designer: 22866
 ******************************************************************************/

/* ============================================================================================== */
/* Header files																				      */
/* ============================================================================================== */
#include "PSL_Algorithm.h"
#include "Vehicle_Geometry_Parameter.h"
#include "PSL_Algorithm_Cfg.h"
#include <math.h>
#include "PSL_Calibration.h"
#include "ODO_AppSignalManage.h"
#include "PSL_AppSignalManage.h"
#include "PSL_AppSignalManage_Types.h"
#include "CAN_AppSignalManage.h"
#include "PSL_Output_Manage.h"
#include "Sns_install_Coordinate.h"
#include "PSL_State_Manage.h"
#include "TimerManage.h"
#include "PublicCalAlgorithm_Int.h"






/******************************************************************************/
/******************************************************************************/
/****************************** Private Definition ****************************/
/******************************************************************************/
/******************************************************************************/

PSLRecordObjCoorType GstrPSLRecordObjCoor;
PSLDetermineDataType GstrPSLDeteData;
PSLUseOdoType GstrPSLUseOdoData;
uint8 u8PSLHaveObjOne[PSL_SNS_CH_NUM] = {0};
#if PSL_FILL_OBJ2_DATA
PSLObjCoorType PSLRecordOdoCoor[PSL_SNS_CH_NUM][3];
float PSLRecordOdoAngle[PSL_SNS_CH_NUM][3];
#endif
/*在找到OBJ1时，0.5~2.45米静态标定数据*/
const   uint16  u16ObjOneCalibration[PSL_RECORD_OBJ_MAX] =
{
    /*5.0~8.5*/
    200 , 200 , 200 , 200 , 250 , 250 , 250 , 250,
    /*9.0~12.5*/        
    300 , 300, 300 , 300, 300, 350 , 350, 350,
    /*13.0~16.5*/
    400 , 400, 420 , 420 , 430, 430 , 430 , 430,
    /*17.0~20.5*/
    440, 440, 440 , 440 , 450, 450 , 450 , 450 , 
    /*21.0~24.5*/
    450, 450, 450 , 450 , 450, 450 , 450 , 450  
};

/*在找到OBJ2时，0.5~2.45米静态标定数据*/
const   uint16  u16ObjTwoCalibration[PSL_RECORD_OBJ_MAX] =
{
    /*5.0~8.5*/
    50 , 50 , 50 , 50 , 50 , 50 , 50 , 50,
    /*9.0~12.5*/
    70 , 80 , 90 , 100 , 110 , 120 , 130 , 140,
    /*13.0~16.5*/
    150 , 150 , 150 , 150 , 150 , 150 , 150 , 150,
    /*17.0~20.5*/
    160 , 160  , 160 , 180 , 180 , 200 , 200 ,200,
    /*21.0~24.5*/
    200 , 200  , 200 , 200 , 200 , 200 , 200 ,200

};




/******************************************************************************/
/******************************************************************************/
/****************************** Public Definition *****************************/
/******************************************************************************/
/******************************************************************************/

void PSLAnalysisSlopeData(PSLSnsChannelType LenuSnsch,PSLCalSlopeType LenuSlopeType);
void PSLCalCurbDepth(PSLSnsChannelType LenuSnsch);
void PSLCalObjAndCurbSlope(PSLSnsChannelType LenuSnsch,PSLCalSlopeType LenuSlopeType);
void PSLCalObjSlope(PSLSnsChannelType LenuSnsch,PSLCalSlopeType LenuSlopeType);
void PSLRecordCurbData(PSLSnsChannelType LenuSnsch);
float PSLCalSlopeDec(float LfSlope1,float LfSlope2);
void PSLUpdateValidSlot(PSLSnsChannelType LenuSnsch);
void PSLCalObj1DataMin(PSLSnsChannelType LenuSnsch);
void PSLOutputSlotInit(void);



/******************************************************************************/
/******************************************************************************/
/******************************* ****** Function ******************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************************
 * 函数名称: PSLCarOdoDataInit
 * 
 * 功能描述: 
 * 
 * 输入参数:
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-3-1 16:28     V0.1      22866        初次发布
 ******************************************************************************/

void PSLCarOdoDataInit(void)
{
    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;

    LpStrPSLUseOdo->u8PSLUseOdoID = 0xFF;
    LpStrPSLUseOdo->u8PSLUseOdoInit = FALSE;
    LpStrPSLUseOdo->fCarCoorX = PSL_ODO_INVALID_DATA;
    LpStrPSLUseOdo->fCarCoorY = PSL_ODO_INVALID_DATA;
    LpStrPSLUseOdo->fCarYawAngle = PSL_ODO_INVALID_DATA;
    LpStrPSLUseOdo->fCarSinYawAngle = PSL_ODO_INVALID_DATA;
    LpStrPSLUseOdo->fCarCosYawAngle = PSL_ODO_INVALID_DATA;
}

/******************************************************************************
 * 函数名称: PSLRecordObjDataInit
 * 
 * 功能描述: 
 * 
 * 输入参数:
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-3-1 16:28     V0.1      22866        初次发布
 ******************************************************************************/

void PSLRecordObjDataInit(PSLSnsChannelType LenuPSLSnsch)
{
    PSLRecordObjCoorType *LpPSLRecordObj;
    LpPSLRecordObj = &GstrPSLRecordObjCoor;
    PSLDeteDataType temp;
    for(temp = PSL_DETE_ONE;temp < PSL_DETE_NUMBER;temp++)
    {
        LpPSLRecordObj->strSnsLeftObj[LenuPSLSnsch][temp].fObjX = PSL_DET_INVALID_COOR;
        LpPSLRecordObj->strSnsLeftObj[LenuPSLSnsch][temp].fObjY = PSL_DET_INVALID_COOR;
        LpPSLRecordObj->strSnsMasterObj[LenuPSLSnsch][temp].fObjX = PSL_DET_INVALID_COOR;
        LpPSLRecordObj->strSnsMasterObj[LenuPSLSnsch][temp].fObjY = PSL_DET_INVALID_COOR;
        LpPSLRecordObj->strSnsRightObj[LenuPSLSnsch][temp].fObjX = PSL_DET_INVALID_COOR;
        LpPSLRecordObj->strSnsRightObj[LenuPSLSnsch][temp].fObjY = PSL_DET_INVALID_COOR;
        LpPSLRecordObj->u16MasterDis[LenuPSLSnsch][temp] = PSL_ECHO_INVALID_DATA;
        LpPSLRecordObj->u16MasterHeight[LenuPSLSnsch][temp] = PSL_HEIGHT_INVALID;
        LpPSLRecordObj->enuMeasMode[LenuPSLSnsch][temp] = PSL_SNS_MEAS_IDLE;
    }
     
}

/******************************************************************************
 * 函数名称: PSLDeteSlotDataInit
 * 
 * 功能描述: 
 * 
 * 输入参数:
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-2-25 16:28    V0.1      22866        初次发布
 ******************************************************************************/
 void PSLDeteSlotDataInit(void)
{
    uint8 i;
    PSLSnsChannelType LenuPSLSnsch;
    for(LenuPSLSnsch = PSL_SNS_CH_FLS;LenuPSLSnsch < PSL_SNS_CH_NUM;LenuPSLSnsch++)
    {
        GstrPSLDeteData.enuPSLDetSts[LenuPSLSnsch] = PSL_DETE_INIT;
        GstrPSLDeteData.LenuSlotType[LenuPSLSnsch] = PSL_SLOT_NONE;
        GstrPSLDeteData.fFirstObjTrailX[LenuPSLSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fFirstObjTrailY[LenuPSLSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fFirstObjMinDisX[LenuPSLSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fFirstObjMinDisY[LenuPSLSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fSecObjMinDisX[LenuPSLSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fSecObjMinDisY[LenuPSLSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.strPslStartCoor[LenuPSLSnsch].fObjX = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.strPslStartCoor[LenuPSLSnsch].fObjY = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.LenuSlotObjType[LenuPSLSnsch] = PSL_SLOT_TWOOBJ;
        GstrPSLDeteData.fCurEchoAverage[LenuPSLSnsch] = PSL_ECHO_INVALID_DATA;
        GstrPSLDeteData.fFirstObjMinDis[LenuPSLSnsch] = PSL_ECHO_INVALID_DATA;
        GstrPSLDeteData.fSecObjMinDis[LenuPSLSnsch] = PSL_ECHO_INVALID_DATA;
        GstrPSLDeteData.fFirstObjCornerX[LenuPSLSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fFirstObjCornerY[LenuPSLSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fSecObjCornerX[LenuPSLSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fSecObjCornerY[LenuPSLSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.u8FirObjInvalidcnt[LenuPSLSnsch] = PSL_DET_INVALID_CNT;        
        GstrPSLDeteData.u8PSLSlotDetedObj[LenuPSLSnsch] = SLOT_HAVE_NONE;
        for(i = 0;i < PSL_RECORD_OBJ_MAX;i++)
        {
            GstrPSLDeteData.fRecObj1EchoDis[LenuPSLSnsch][i] = PSL_ECHO_INVALID_DATA;
            GstrPSLDeteData.strRecObj1Odo[LenuPSLSnsch][i].fObjX = PSL_DET_INVALID_COOR;
            GstrPSLDeteData.strRecObj1Odo[LenuPSLSnsch][i].fObjY = PSL_DET_INVALID_COOR;
            GstrPSLDeteData.fRecObj1OdoAngle[LenuPSLSnsch][i] = APA_360_ANGLE;
            GstrPSLDeteData.strObj1Data[LenuPSLSnsch][i].fObjX = PSL_DET_INVALID_COOR;
            GstrPSLDeteData.strObj1Data[LenuPSLSnsch][i].fObjY = PSL_DET_INVALID_COOR;
            GstrPSLDeteData.fSlopeData[LenuPSLSnsch][i] = PSL_DET_INVALID_DATA;
            GstrPSLDeteData.fRecObj2EchoDis[LenuPSLSnsch][i] = PSL_ECHO_INVALID_DATA;
            GstrPSLDeteData.strObj2Data[LenuPSLSnsch][i].fObjX = PSL_DET_INVALID_COOR;
            GstrPSLDeteData.strObj2Data[LenuPSLSnsch][i].fObjY = PSL_DET_INVALID_COOR;
            GstrPSLDeteData.strObj2OdoCoor[LenuPSLSnsch][i].fObjX = PSL_DET_INVALID_COOR;
            GstrPSLDeteData.strObj2OdoCoor[LenuPSLSnsch][i].fObjY = PSL_DET_INVALID_COOR;
            GstrPSLDeteData.fObj2RawData[LenuPSLSnsch][i] = PSL_ECHO_INVALID_DATA;
        }
        for(i = 0;i < PSL_CAL_SLOPE_MAX;i++)
        {
            GstrPSLDeteData.fRecordCarSlope[LenuPSLSnsch][i] = PSL_DET_INVALID_DATA;
        }
        GstrPSLDeteData.u8RecObj2RawCnt[LenuPSLSnsch] = PSL_DET_INVALID_CNT;
        GstrPSLDeteData.u8SlopeCnt[LenuPSLSnsch] = PSL_DET_INVALID_CNT;
        GstrPSLDeteData.fObj1Slope[LenuPSLSnsch] = PSL_DET_INVALID_DATA;
        GstrPSLDeteData.fObj2Slope[LenuPSLSnsch] = PSL_DET_INVALID_DATA;
	#if PSL_FILL_OBJ2_DATA
        for(i = 0;i < 3;i++)
        {
            PSLRecordOdoCoor[LenuPSLSnsch][i].fObjX = PSL_DET_INVALID_COOR;
            PSLRecordOdoCoor[LenuPSLSnsch][i].fObjY = PSL_DET_INVALID_COOR;
            PSLRecordOdoAngle[LenuPSLSnsch][i] = PSL_DET_INVALID_DATA;
        }
	#endif
        for(i = 0;i < PSL_RECORD_CURB_MAX;i++)
        {
            GstrPSLDeteData.u16CurbDepthData[LenuPSLSnsch][i] = PSL_INVALID_VALUE;
            GstrPSLDeteData.strCurbCoorData[LenuPSLSnsch][i].fObjX = PSL_DET_INVALID_COOR;
            GstrPSLDeteData.strCurbCoorData[LenuPSLSnsch][i].fObjY = PSL_DET_INVALID_COOR;
            GstrPSLDeteData.strCurbOdoCoor[LenuPSLSnsch][i].fObjX = PSL_DET_INVALID_COOR; 
            GstrPSLDeteData.strCurbOdoCoor[LenuPSLSnsch][i].fObjY = PSL_DET_INVALID_COOR;         
        }
        GstrPSLDeteData.u8RecordObjCnt[LenuPSLSnsch] = PSL_DET_INVALID_CNT;
        GstrPSLDeteData.u8ObjMinIndex[LenuPSLSnsch] = PSL_DET_INVALID_CNT;
        GstrPSLDeteData.u8RecordObj2Cnt[LenuPSLSnsch] = PSL_DET_INVALID_CNT;
        GstrPSLDeteData.u8Obj2MinIndex[LenuPSLSnsch] = PSL_DET_INVALID_CNT;
        GstrPSLDeteData.fObj1BackUpCarCoorx[LenuPSLSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fObj1BackUpCarCoory[LenuPSLSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fRecObj1CornerOdox[LenuPSLSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fRecObj1CornerOdoy[LenuPSLSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fBackUpCarCoorx[LenuPSLSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fBackUpCarCoory[LenuPSLSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.strPslStartCoor[LenuPSLSnsch].fObjX = PSL_DET_INVALID_COOR; 
        GstrPSLDeteData.strPslStartCoor[LenuPSLSnsch].fObjY = PSL_DET_INVALID_COOR; 
        GstrPSLDeteData.u8CurbDataCnt[LenuPSLSnsch] = PSL_DET_INVALID_CNT;
        GstrPSLDeteData.u16CurbDepth[LenuPSLSnsch] = PSL_CURB_DEPTH_INVALID;
        GstrPSLDeteData.fCurbSlope[LenuPSLSnsch] = PSL_DET_INVALID_DATA;
        GstrPSLDeteData.fCurbDataInitFlag[LenuPSLSnsch] = FALSE;
        GstrPSLDeteData.u16SlotWidth[LenuPSLSnsch] = PSL_INVALID_VALUE;
        GstrPSLDeteData.LenuFirObjType[LenuPSLSnsch] = PSL_OBJ_TYPE_NONE;
        GstrPSLDeteData.LenuSecObjType[LenuPSLSnsch] = PSL_OBJ_TYPE_NONE;
        GstrPSLDeteData.LenuFirObjShape[LenuPSLSnsch] = OBJ_SHAPE_NONE;
        GstrPSLDeteData.LenuSecObjShape[LenuPSLSnsch] = OBJ_SHAPE_NONE;        
        u8PSLHaveObjOne[LenuPSLSnsch] = 0;
        PSLRecordObjDataInit(LenuPSLSnsch);
    }
    PSLDisFollowDataInit();
    PSLObjPositionCoorInit();
    PSLCarOdoDataInit();
    PSLOutputSlotInit();
}

 /******************************************************************************
  * 函数名称: PSLDeteSingleChannelInit
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-2-25 16:28    V0.1      22866        初次发布
  ******************************************************************************/
  void PSLDeteSingleChannelInit(PSLSnsChannelType LenuSnsch)
 {
        uint8 i;
        PSLUseOdoType *LpStrPSLUseOdo;
        LpStrPSLUseOdo = &GstrPSLUseOdoData;
        GstrPSLDeteData.enuPSLDetSts[LenuSnsch] = PSL_DETE_IDLE;
        GstrPSLDeteData.LenuSlotObjType[LenuSnsch] = PSL_SLOT_TWOOBJ;
        GstrPSLDeteData.LenuSlotType[LenuSnsch] = PSL_SLOT_NONE;
        GstrPSLDeteData.fFirstObjTrailX[LenuSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fFirstObjTrailY[LenuSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fFirstObjMinDisX[LenuSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fFirstObjMinDisY[LenuSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fSecObjMinDisX[LenuSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fSecObjMinDisY[LenuSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fCurEchoAverage[LenuSnsch] = PSL_ECHO_INVALID_DATA;
        GstrPSLDeteData.fFirstObjMinDis[LenuSnsch] = PSL_ECHO_INVALID_DATA;
        GstrPSLDeteData.fSecObjMinDis[LenuSnsch] = PSL_ECHO_INVALID_DATA;
        GstrPSLDeteData.fFirstObjCornerX[LenuSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fFirstObjCornerY[LenuSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fSecObjCornerX[LenuSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fSecObjCornerY[LenuSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.u8FirObjInvalidcnt[LenuSnsch] = PSL_DET_INVALID_CNT;
        GstrPSLDeteData.u8PSLSlotDetedObj[LenuSnsch] = SLOT_HAVE_NONE;
        for(i = 0;i < PSL_RECORD_OBJ_MAX;i++)
        {
            GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i] = PSL_ECHO_INVALID_DATA;
            GstrPSLDeteData.strRecObj1Odo[LenuSnsch][i].fObjX = PSL_DET_INVALID_COOR;
            GstrPSLDeteData.strRecObj1Odo[LenuSnsch][i].fObjY = PSL_DET_INVALID_COOR;
            GstrPSLDeteData.fRecObj1OdoAngle[LenuSnsch][i] = APA_360_ANGLE;
            GstrPSLDeteData.strObj1Data[LenuSnsch][i].fObjX = PSL_DET_INVALID_COOR;
            GstrPSLDeteData.strObj1Data[LenuSnsch][i].fObjY = PSL_DET_INVALID_COOR;
            GstrPSLDeteData.fSlopeData[LenuSnsch][i] = PSL_DET_INVALID_DATA;
            GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][i] = PSL_ECHO_INVALID_DATA;
            GstrPSLDeteData.strObj2Data[LenuSnsch][i].fObjX = PSL_DET_INVALID_COOR;
            GstrPSLDeteData.strObj2Data[LenuSnsch][i].fObjY = PSL_DET_INVALID_COOR;
            GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][i].fObjX = PSL_DET_INVALID_COOR;
            GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][i].fObjY = PSL_DET_INVALID_COOR;
            GstrPSLDeteData.fObj2RawData[LenuSnsch][i] = PSL_ECHO_INVALID_DATA;
        }
        GstrPSLDeteData.u8RecObj2RawCnt[LenuSnsch] = PSL_DET_INVALID_CNT;
        GstrPSLDeteData.u8SlopeCnt[LenuSnsch] = PSL_DET_INVALID_CNT;
        GstrPSLDeteData.fObj1Slope[LenuSnsch] = PSL_DET_INVALID_DATA;
        GstrPSLDeteData.fObj2Slope[LenuSnsch] = PSL_DET_INVALID_DATA;
        GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] = PSL_DET_INVALID_CNT;
        GstrPSLDeteData.u8ObjMinIndex[LenuSnsch] = PSL_DET_INVALID_CNT;
        GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch] = PSL_DET_INVALID_CNT;
        GstrPSLDeteData.u8Obj2MinIndex[LenuSnsch] = PSL_DET_INVALID_CNT;
        GstrPSLDeteData.fObj1BackUpCarCoorx[LenuSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fObj1BackUpCarCoory[LenuSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fRecObj1CornerOdox[LenuSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fRecObj1CornerOdoy[LenuSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fBackUpCarCoorx[LenuSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fBackUpCarCoory[LenuSnsch] = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.strPslStartCoor[LenuSnsch].fObjX = LpStrPSLUseOdo->fCarCoorX;
        GstrPSLDeteData.strPslStartCoor[LenuSnsch].fObjY = LpStrPSLUseOdo->fCarCoorY;
        GstrPSLDeteData.u8CurbDataCnt[LenuSnsch] = PSL_DET_INVALID_CNT;
        GstrPSLDeteData.u16CurbDepth[LenuSnsch] = PSL_CURB_DEPTH_INVALID;
        GstrPSLDeteData.fCurbSlope[LenuSnsch] = PSL_DET_INVALID_DATA;
        GstrPSLDeteData.fCurbDataInitFlag[LenuSnsch] = FALSE;
        GstrPSLDeteData.u16SlotWidth[LenuSnsch] = PSL_INVALID_VALUE;
        GstrPSLDeteData.LenuFirObjType[LenuSnsch] = PSL_OBJ_TYPE_NONE;
        GstrPSLDeteData.LenuSecObjType[LenuSnsch] = PSL_OBJ_TYPE_NONE;
        GstrPSLDeteData.LenuFirObjShape[LenuSnsch] = OBJ_SHAPE_NONE;
        GstrPSLDeteData.LenuSecObjShape[LenuSnsch] = OBJ_SHAPE_NONE;  

        for(i = 0;i < PSL_RECORD_CURB_MAX;i++)
        {
            GstrPSLDeteData.u16CurbDepthData[LenuSnsch][i] = PSL_INVALID_VALUE;
            GstrPSLDeteData.strCurbCoorData[LenuSnsch][i].fObjX = PSL_DET_INVALID_COOR;
            GstrPSLDeteData.strCurbCoorData[LenuSnsch][i].fObjY = PSL_DET_INVALID_COOR;
            GstrPSLDeteData.strCurbOdoCoor[LenuSnsch][i].fObjX = PSL_DET_INVALID_COOR; 
            GstrPSLDeteData.strCurbOdoCoor[LenuSnsch][i].fObjY = PSL_DET_INVALID_COOR;         
        }
        for(i = 0;i < PSL_CAL_SLOPE_MAX;i++)
        {
            GstrPSLDeteData.fRecordCarSlope[LenuSnsch][i] = PSL_DET_INVALID_DATA;
        }
        u8PSLHaveObjOne[LenuSnsch] = 0;
        GstrPSLDeteData.enuPSLDetSts[LenuSnsch] = PSL_DETE_IDLE;
        PSL_DEBUG_PRINTF("ch:%d Init\r\n",LenuSnsch);
 }
/******************************************************************************
   * 函数名称: PSLOutputSlotInit
   * 
   * 功能描述: 
   * 
   * 输入参数:
   * 
   * 输出参数:无 
   * 
   * 返回值:无 
   * 
   * 其它说明:无 
   * 
   * 修改日期              版本号        修改人          修改内容  
   * 2023-2-25 16:28    V0.1      22866        初次发布
   ******************************************************************************/
 void PSLOutputSlotInit(void)
 {
    uint8 LcTemp;
 
    for(LcTemp = 0; LcTemp < OUTPUT_SLOT_MAX_NUM; LcTemp++)
    {
        GsPSLOutputSlotData.dFirobjDis[LcTemp] = DEPTH_NULL;
        GsPSLOutputSlotData.dSecobjDis[LcTemp] = DEPTH_NULL;
        GsPSLOutputSlotData.dThrObjDis[LcTemp] = DEPTH_NULL;
        GsPSLOutputSlotData.cObjState[LcTemp] = PSL_Objste_None;
        GsPSLOutputSlotData.cFirstObjType[LcTemp] = PSL_Objtype_None;
        GsPSLOutputSlotData.cSecondObjType[LcTemp] = PSL_Objtype_None;
        GsPSLOutputSlotData.cSlotType[LcTemp] = PSL_SLOT_NONE;
        GsPSLOutputSlotData.cSlotDir[LcTemp] = PSL_Slotdir_None;
        GsPSLOutputSlotData.wSlotWidth[LcTemp] = DIS_NULL;
        GsPSLOutputSlotData.dFirstObjWx[LcTemp] = DIS_NULL;
        GsPSLOutputSlotData.dFirstObjWy[LcTemp] = DIS_NULL;
        GsPSLOutputSlotData.dSecObjWx[LcTemp] = DIS_NULL;
        GsPSLOutputSlotData.dSecObjWy[LcTemp] = DIS_NULL;
        GsPSLOutputSlotData.dThrObjWx[LcTemp] = DIS_NULL;
        GsPSLOutputSlotData.dThrObjWy[LcTemp] = DIS_NULL;
        GsPSLOutputSlotData.dSlotDepthDis[LcTemp] = DEPTH_NULL; 
        GsPSLOutputSlotData.dSlotDepthWx[LcTemp] = DIS_NULL;    
        GsPSLOutputSlotData.dSlotDepthWy[LcTemp] = DIS_NULL;   
        GsPSLOutputSlotData.dObj1Slope[LcTemp] = APA_360_ANGLE;
        GsPSLOutputSlotData.dObj2Slope[LcTemp] = APA_360_ANGLE;             
        GsPSLOutputSlotData.dCurbSlope[LcTemp] = APA_360_ANGLE;
        GsPSLOutputSlotData.dOutputSlope[LcTemp] = APA_360_ANGLE;
        GsPSLOutputSlotData.Type_T_Slot_flag[LcTemp] = FALSE;
        GsPSLOutputSlotData.dInSlotObjWx[LcTemp] = DIS_NULL;
        GsPSLOutputSlotData.dInSlotObjWy[LcTemp] = DIS_NULL;
        GsPSLOutputSlotData.fRecordCarSlope[LcTemp] = APA_360_ANGLE;  
        GsPSLOutputSlotData.cSlotdepthRef[LcTemp] = DEPTH_REF_NONE;
        GsPSLOutputSlotData.cFirObjType[LcTemp] = PSL_OBJ_TYPE_NONE;
        GsPSLOutputSlotData.cSecObjType[LcTemp] = PSL_OBJ_TYPE_NONE;
        GsPSLOutputSlotData.Lu32slotsynctime[LcTemp] = 0;                     
    }
 }

/******************************************************************************
 * 函数名称: PSLDeteCurbDataInit
 * 
 * 功能描述: 
 * 
 * 输入参数:
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-3-10 16:28    V0.1      22866        初次发布
 ******************************************************************************/
void PSLDeteCurbDataInit(PSLSnsChannelType LenuSnsch)
{
    uint8 i;
    if(GstrPSLDeteData.fCurbDataInitFlag[LenuSnsch] == TRUE)
    {
        GstrPSLDeteData.u8CurbDataCnt[LenuSnsch] = PSL_DET_INVALID_CNT;
        GstrPSLDeteData.u16CurbDepth[LenuSnsch] = PSL_CURB_DEPTH_INVALID;
        GstrPSLDeteData.fCurbDataInitFlag[LenuSnsch] = FALSE;
        for(i = 0;i < PSL_RECORD_CURB_MAX;i++)
        {
            GstrPSLDeteData.u16CurbDepthData[LenuSnsch][i] = PSL_INVALID_VALUE;
            GstrPSLDeteData.strCurbCoorData[LenuSnsch][i].fObjX = PSL_DET_INVALID_COOR;
            GstrPSLDeteData.strCurbCoorData[LenuSnsch][i].fObjY = PSL_DET_INVALID_COOR;
            GstrPSLDeteData.strCurbOdoCoor[LenuSnsch][i].fObjX = PSL_DET_INVALID_COOR; 
            GstrPSLDeteData.strCurbOdoCoor[LenuSnsch][i].fObjY = PSL_DET_INVALID_COOR;
        }
    }
}

  
/******************************************************************************
  * 函数名称: PSLDeteSlotWorkStsIdle
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-2-25 16:28    V0.1      22866        初次发布
  ******************************************************************************/

PSLDeteDataType PSLCalMinDisCoor(float cPointone,float cPointTwo,float cPointThird)
{
    float cTempDis;
    PSLDeteDataType lenuDeteMinData;
    cTempDis = MIN_VALUE(cPointone,cPointTwo);
    cTempDis = MIN_VALUE(cTempDis,cPointThird);
    if(cTempDis == cPointone)
    {
        lenuDeteMinData = PSL_DETE_FIFTH;
    }
    else if(cTempDis == cPointTwo)
    {
        lenuDeteMinData = PSL_DETE_FOURTH;
    }
    else 
    {
        lenuDeteMinData = PSL_DETE_THIRD;
    }

    return lenuDeteMinData;
}

 

 /******************************************************************************
  * 函数名称: PSLDeteSlotWorkStsIdle
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-2-25 16:28    V0.1      22866        初次发布
  ******************************************************************************/
void PSLDeteSlotWorkStsIdle(PSLSnsChannelType LenuSnsch)
{
    PSLRecordObjCoorType *LpPSLRecordObj;
    LpPSLRecordObj = &GstrPSLRecordObjCoor;
    float fTwoPointDis[3];
    float fOnePointx = 0.0;
    float fOnePointy = 0.0;
    float fTwoPointx = 0.0;
    float fTwoPointy = 0.0;
    float fThirdPointx = 0.0;
    float fThirdPointy = 0.0;
    float fAveragePointx = 0;
    float fAverageDepth = 0;
    float fAverageEchoDis = 0;      
    float fDetSlotStartDis = 0.0;
    uint8 i;
    //PSLSnsChannelType LenuDetech;
    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;
    PSLDetermineDataType *LpStrPSLDetData;
    LpStrPSLDetData = &GstrPSLDeteData;
    uint8 u8DetObj1Flag = 0;
    uint16 u16CurbDepth = 0;
    uint8 u8DetObj2Flag = 0;
    if((LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_FIFTH].fObjX != PSL_DET_INVALID_COOR)
        &&((LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_FOURTH].fObjX != PSL_DET_INVALID_COOR)
        &&(LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_THIRD].fObjX != PSL_DET_INVALID_COOR)))
    {
        if(LpPSLRecordObj->enuMeasMode[LenuSnsch][PSL_DETE_FIFTH] == PSL_SNS_MEAS_STD
            &&LpPSLRecordObj->enuMeasMode[LenuSnsch][PSL_DETE_FOURTH] == PSL_SNS_MEAS_STD)
        {
            if((LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] < PSL_SNS_MEAS_CHIRP_MIN)
                && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] + PSL_ECHO_20CM_VALUE))
                && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] + PSL_ECHO_20CM_VALUE))
                && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_ONE] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] + PSL_ECHO_20CM_VALUE)))
            {
                PSL_DEBUG_PRINTF("u8DetObj1Flag == 1\r\n");
                u8DetObj1Flag = 1;
            }
        }
        
        fOnePointx = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_FIFTH].fObjX;
        fOnePointy = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_FIFTH].fObjY;
        fTwoPointx = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_FOURTH].fObjX;
        fTwoPointy = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_FOURTH].fObjY;
        fThirdPointx = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_THIRD].fObjX;
        fThirdPointy = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_THIRD].fObjY;

        if(((LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] + PSL_ECHO_25CM_VALUE))
        && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] + PSL_ECHO_25CM_VALUE))
        && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD] + PSL_ECHO_25CM_VALUE))
        && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] + PSL_ECHO_25CM_VALUE))
        && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] + PSL_ECHO_30CM_VALUE)))
        || (u8DetObj1Flag == 1))		
        {
            if(u8DetObj1Flag != 1)
            {
                fAveragePointx = (fOnePointx + fTwoPointx + fThirdPointx) / 3;
                fAverageDepth = (fOnePointy + fTwoPointy + fThirdPointy) / 3;
                fAverageEchoDis = (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] 
                    + LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH]
                    + LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD]) / 3;
            }
            else
            {
                u8DetObj1Flag = 0;
                fAveragePointx = (fOnePointx + fTwoPointx) / 2;
                fAverageDepth = (fOnePointy + fTwoPointy) / 2;
                fAverageEchoDis = (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] 
                    + LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH]) / 2;
            }
			PSL_DEBUG_PRINTF("fAverageEchoDis:%.1f DetObjDisMax:%d,CH:%d\r\n", fAverageEchoDis, GstrPSL_APPPara_Ram.u16DetObjDisMax,LenuSnsch);
            if(fAverageEchoDis < GstrPSL_APPPara_Ram.u16DetObjDisMax)
            {
                fOnePointx = LpStrPSLUseOdo->fCarCoorX;
                fOnePointy = LpStrPSLUseOdo->fCarCoorY;
                fTwoPointx = GstrPSLDeteData.strPslStartCoor[LenuSnsch].fObjX;
                fTwoPointy = GstrPSLDeteData.strPslStartCoor[LenuSnsch].fObjY;
                fDetSlotStartDis = PubAI_CalTwoPointDis(fOnePointx,fOnePointy,fTwoPointx,fTwoPointy);
                if(fDetSlotStartDis < GstrPSL_APPPara_Ram.u16SlotWidthMin_H)
                {
                    GstrPSLDeteData.fFirstObjMinDis[LenuSnsch] = fAverageEchoDis;
                    GstrPSLDeteData.fFirstObjTrailX[LenuSnsch] = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_FIFTH].fObjX;
                    GstrPSLDeteData.fFirstObjTrailY[LenuSnsch] = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_FIFTH].fObjY;
                    GstrPSLDeteData.fFirstObjMinDisX[LenuSnsch] = fAveragePointx;
                    GstrPSLDeteData.fFirstObjMinDisY[LenuSnsch] = fAverageDepth;                             
                    GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] = 0;
                    GstrPSLDeteData.strObj1Data[LenuSnsch][0].fObjX = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_FIFTH].fObjX;
                    GstrPSLDeteData.strObj1Data[LenuSnsch][0].fObjY = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_FIFTH].fObjY;
                    GstrPSLDeteData.fRecordCarSlope[LenuSnsch][PSL_CAL_SLOPE_OBJ1] = LpStrPSLUseOdo->fCarYawAngle;
                    GstrPSLDeteData.enuPSLDetSts[LenuSnsch] = PSL_DETE_OBJ1;
                    
                     for(i = 0;i < 3;i++)
                    {
                        GstrPSLDeteData.strObj1Data[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch]].fObjX = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_THIRD + i].fObjX;
                        GstrPSLDeteData.strObj1Data[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch]].fObjY = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_THIRD + i].fObjY;
                        GstrPSLDeteData.strRecObj1Odo[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch]].fObjX = PSLRecordOdoCoor[LenuSnsch][i].fObjX;
                        GstrPSLDeteData.strRecObj1Odo[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch]].fObjY = PSLRecordOdoCoor[LenuSnsch][i].fObjY;
                        GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch]] = LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD + i];
                        GstrPSLDeteData.fRecObj1OdoAngle[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch]] = PSLRecordOdoAngle[LenuSnsch][i];
                        GstrPSLDeteData.u8RecordObjCnt[LenuSnsch]++;
                        
                    }
                    PSLCalObj1DataMin(LenuSnsch);
                }
                else
                {
                    //PSLCalObjAndCurbSlope(LenuSnsch,PSL_CAL_SLOPE_CURB);
                    //PSLAnalysisSlopeData(LenuSnsch,PSL_CAL_SLOPE_CURB);
                    PSLCalObjSlope(LenuSnsch,PSL_CAL_SLOPE_CURB);
                    PSLCalCurbDepth(LenuSnsch);
                    if(LpStrPSLDetData->u16CurbDepth[LenuSnsch] > LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH])
                    {
                        u16CurbDepth = LpStrPSLDetData->u16CurbDepth[LenuSnsch] - LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH];
                    }
                    else
                    {
                        u8DetObj2Flag = 0;
                    }
                    if(u16CurbDepth > GstrPSL_APPPara_Ram.u16SlotDepthMin_H - 200)
                    {
                        u8DetObj2Flag = 1;
                    }
                    if((LpStrPSLDetData->u16CurbDepth[LenuSnsch] < PSL_CURB_DEPTH_INVALID) &&  (u8DetObj2Flag == 1))
                    {
                        u8DetObj2Flag = 0;
                        GstrPSLDeteData.enuPSLDetSts[LenuSnsch] = PSL_DETE_OBJ2;
                        GstrPSLDeteData.LenuSlotObjType[LenuSnsch] = PSL_SLOT_ONLYOBJ2;
                        GstrPSLDeteData.fBackUpCarCoorx[LenuSnsch] = LpStrPSLUseOdo->fCarCoorX;
                        GstrPSLDeteData.fBackUpCarCoory[LenuSnsch] = LpStrPSLUseOdo->fCarCoorY;
                        GstrPSLDeteData.fRecordCarSlope[LenuSnsch][PSL_CAL_SLOPE_OBJ2] = LpStrPSLUseOdo->fCarYawAngle;
                        GstrPSLDeteData.fRecordCarSlope[LenuSnsch][PSL_CAL_SLOPE_OBJ1] = LpStrPSLUseOdo->fCarYawAngle;
                        for(i = 0;i < 3;i++)
                        {
                            GstrPSLDeteData.strObj2Data[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]].fObjX = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_THIRD + i].fObjX;
                            GstrPSLDeteData.strObj2Data[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]].fObjY = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_THIRD + i].fObjY;
                            GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]].fObjX = PSLRecordOdoCoor[LenuSnsch][i].fObjX;
                            GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]].fObjY = PSLRecordOdoCoor[LenuSnsch][i].fObjY;
                            GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]] = LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD + i];
                            GstrPSLDeteData.fRecObj2OdoAngle[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]] = PSLRecordOdoAngle[LenuSnsch][i];
                            GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]++;
                        }
                    }
                    else
                    {
                        uint8 i;
                         for(i = 0;i < 3;i++)
                        {
                            GstrPSLDeteData.strObj1Data[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch]].fObjX = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_THIRD + i].fObjX;
                            GstrPSLDeteData.strObj1Data[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch]].fObjY = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_THIRD + i].fObjY;
                            GstrPSLDeteData.strRecObj1Odo[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch]].fObjX = PSLRecordOdoCoor[LenuSnsch][i].fObjX;
                            GstrPSLDeteData.strRecObj1Odo[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch]].fObjY = PSLRecordOdoCoor[LenuSnsch][i].fObjY;
                            GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch]] = LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD + i];
                            GstrPSLDeteData.fRecObj1OdoAngle[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch]] = PSLRecordOdoAngle[LenuSnsch][i];
                            GstrPSLDeteData.u8RecordObjCnt[LenuSnsch]++;
                      
                        }
                        PSLCalObj1DataMin(LenuSnsch);
                        GstrPSLDeteData.fFirstObjTrailX[LenuSnsch] = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_FIFTH].fObjX;
                        GstrPSLDeteData.fFirstObjTrailY[LenuSnsch] = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_FIFTH].fObjY;
                        GstrPSLDeteData.enuPSLDetSts[LenuSnsch] = PSL_DETE_OBJ1;
                    }
                }
            }
            else
            {

            }
        }
        
    }

    PSLRecordCurbData(LenuSnsch);
}



/******************************************************************************
  * 函数名称: PSLCalCurEchoAverage
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-3-2 14:20     V0.1      22866        初次发布
  ******************************************************************************/
boolean PSLCalCurEchoAverage(PSLSnsChannelType LenuSnsch)
{
    boolean Lu8rtn = FALSE;
    float fCurEchoTemp[PSL_AVERAGE_ECHO_MAX];
    float fCurEchoAverage = 0.0;
    uint8 i,j;
    PSLRecordObjCoorType *LpPSLRecordObj;
    LpPSLRecordObj = &GstrPSLRecordObjCoor;

    
    for(i = 0;i < PSL_AVERAGE_ECHO_MAX;i++)
    {
        fCurEchoTemp[i] = LpPSLRecordObj->u16MasterDis[LenuSnsch][i + 1];
        if(fCurEchoTemp[i] > 5000)
        {
            return Lu8rtn;
        }
    }

    for(i = 0 ; i < PSL_AVERAGE_ECHO_MAX - 1; i++)
    {
        for(j = i+1 ; j < PSL_AVERAGE_ECHO_MAX; j++)
        {
            if(fCurEchoTemp[j] < fCurEchoTemp[i])
            {
                fCurEchoAverage = fCurEchoTemp[i] ;
                fCurEchoTemp[i] = fCurEchoTemp[j] ;
                fCurEchoTemp[j] = fCurEchoAverage;
            }
        }
    }
    fCurEchoAverage = (fCurEchoTemp[1] + fCurEchoTemp[2]) / 2;
    GstrPSLDeteData.fCurEchoAverage[LenuSnsch] = fCurEchoAverage;
    Lu8rtn = TRUE;
    return Lu8rtn;
}

/******************************************************************************
  * 函数名称: PSLRecordCurbData
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-3-10 10:20     V0.1      22866        初次发布
  ******************************************************************************/
void PSLRecordCurbData(PSLSnsChannelType LenuSnsch)
{
    PSLRecordObjCoorType *LpPSLRecordObj;
    LpPSLRecordObj = &GstrPSLRecordObjCoor;
    float fCurCarCoorx = 0.0;
    float fCurCarCoory = 0.0;
    float fBackUpCarCoorx= 0.0;
    float fBackUpCarCoory= 0.0;
    float fCarMoveDis = 0.0;
    float fListenCoorx = 0.0;
    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;
    
    uint8 i,u8ValidCnt = 0;
    for(i = PSL_DETE_THIRD;i < PSL_DETE_NUMBER;i++)
    {
        if(LpPSLRecordObj->strSnsMasterObj[LenuSnsch][i].fObjX != PSL_DET_INVALID_COOR)
        {
            u8ValidCnt++;
        }
    }
    if((LenuSnsch == PSL_SNS_CH_FLS) || LenuSnsch == PSL_SNS_CH_RLS)
    {
        fListenCoorx = LpPSLRecordObj->strSnsRightObj[LenuSnsch][PSL_DETE_FIFTH].fObjX;
    }
    else
    {
        fListenCoorx = LpPSLRecordObj->strSnsLeftObj[LenuSnsch][PSL_DETE_FIFTH].fObjX;
    }
    if((LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_FIFTH].fObjX != PSL_DET_INVALID_COOR && fListenCoorx != PSL_DET_INVALID_COOR)
        ||(u8PSLHaveObjOne[LenuSnsch] == 1 && LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_FIFTH].fObjX != PSL_DET_INVALID_COOR))
    {
        fCurCarCoorx = LpStrPSLUseOdo->fCarCoorX;
        fCurCarCoory = LpStrPSLUseOdo->fCarCoorY;
        fBackUpCarCoorx = GstrPSLDeteData.fBackUpCarCoorx[LenuSnsch];
        fBackUpCarCoory = GstrPSLDeteData.fBackUpCarCoory[LenuSnsch];
		fCarMoveDis = PubAI_CalTwoPointDis(fCurCarCoorx, fCurCarCoory, fBackUpCarCoorx, fBackUpCarCoory);
        if(fCarMoveDis > 25
			&& LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] > 1500)
        {
			GstrPSLDeteData.fBackUpCarCoorx[LenuSnsch] = fCurCarCoorx;
			GstrPSLDeteData.fBackUpCarCoory[LenuSnsch] = fCurCarCoory;
            GstrPSLDeteData.u16CurbDepthData[LenuSnsch][GstrPSLDeteData.u8CurbDataCnt[LenuSnsch]] = LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH];
            GstrPSLDeteData.strCurbCoorData[LenuSnsch][GstrPSLDeteData.u8CurbDataCnt[LenuSnsch]].fObjX = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_FIFTH].fObjX;
            GstrPSLDeteData.strCurbCoorData[LenuSnsch][GstrPSLDeteData.u8CurbDataCnt[LenuSnsch]].fObjY = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_FIFTH].fObjY;
            GstrPSLDeteData.strCurbOdoCoor[LenuSnsch][GstrPSLDeteData.u8CurbDataCnt[LenuSnsch]].fObjX = fCurCarCoorx; 
            GstrPSLDeteData.strCurbOdoCoor[LenuSnsch][GstrPSLDeteData.u8CurbDataCnt[LenuSnsch]].fObjY = fCurCarCoory; 
            GstrPSLDeteData.u8CurbDataCnt[LenuSnsch]++;

            if(GstrPSLDeteData.u8CurbDataCnt[LenuSnsch] >= PSL_RECORD_CURB_MAX)
            {
                for(i = 0;i < PSL_RECORD_CURB_MAX - 1;i++) 
                {
                    GstrPSLDeteData.u16CurbDepthData[LenuSnsch][i] = GstrPSLDeteData.u16CurbDepthData[LenuSnsch][i + 1];
                    GstrPSLDeteData.strCurbCoorData[LenuSnsch][i].fObjX = GstrPSLDeteData.strCurbCoorData[LenuSnsch][i + 1].fObjX;
                    GstrPSLDeteData.strCurbCoorData[LenuSnsch][i].fObjY = GstrPSLDeteData.strCurbCoorData[LenuSnsch][i + 1].fObjY;
                    GstrPSLDeteData.strCurbOdoCoor[LenuSnsch][i].fObjX = GstrPSLDeteData.strCurbOdoCoor[LenuSnsch][i + 1].fObjX;
                    GstrPSLDeteData.strCurbOdoCoor[LenuSnsch][i].fObjY = GstrPSLDeteData.strCurbOdoCoor[LenuSnsch][i + 1].fObjY;
                }
                GstrPSLDeteData.u8CurbDataCnt[LenuSnsch] = PSL_RECORD_CURB_MAX - 1;             
            }
        }    
    }
}

/******************************************************************************
  * 函数名称: PSLFillObj1Data
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-8-16 19:20     V0.1      22866        初次发布
  ******************************************************************************/
void PSLFillObj1Data(PSLSnsChannelType LenuSnsch)
{
    float fEchoSum = 0.0;
    float fEchoTemp1 = 0.0;
    float fEchoTemp2 = 0.0;
    float fFillSnsCoorX = 0.0;
    float fFillSnsCoorY = 0.0;
    float fFillCarCoorX = 0.0;
    float fFillCarCoorY = 0.0;
    float fFillOdoCoorX = 0.0;
    float fFillOdoCoorY = 0.0;
    float fFillOdoX = 0.0;
    float fFillOdoY = 0.0;
    float fFillAngle = 0.0;
    eSnsChannelType enuSnsCh = SNS_CH_FLS;
    SnsInstallGroupType enuSnsGroup = SNS_INSTALL_GROUP_FRONT;
    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;
	float fCarCos = 0.0;
    float fCarSin = 0.0;
    PSLRecordObjCoorType *LpPSLRecordObj;
    LpPSLRecordObj = &GstrPSLRecordObjCoor;
    if(GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] >= 5)
    {
        if(LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] > GstrPSLDeteData.fFirstObjMinDis[LenuSnsch] + PSL_ECHO_20CM_VALUE)
        {

            if(LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD] == GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 2] \
                && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_SECOND] ==  GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 3])
//                && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_ONE] ==  GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 4])
                && ((LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] == PSL_ECHO_INVALID_DATA) || (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] > LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH])))
            {                           
                if(LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD] < GstrPSLDeteData.fFirstObjMinDis[LenuSnsch] + PSL_ECHO_20CM_VALUE)
                {
                    fEchoTemp1 = ABS(LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD],LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_SECOND]);
                    fEchoTemp2 = ABS(LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH],LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD]);
                    fEchoSum = (fEchoTemp1 + fEchoTemp2) / 2;   
                    
                    GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch]] = GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 1];
                    GstrPSLDeteData.strObj1Data[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch]].fObjX = GstrPSLDeteData.strObj1Data[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 1].fObjX;
                    GstrPSLDeteData.strObj1Data[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch]].fObjY = GstrPSLDeteData.strObj1Data[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 1].fObjY;
                    GstrPSLDeteData.strRecObj1Odo[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch]].fObjX = GstrPSLDeteData.strRecObj1Odo[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 1].fObjX;
                    GstrPSLDeteData.strRecObj1Odo[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch]].fObjY = GstrPSLDeteData.strRecObj1Odo[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 1].fObjY;
                    GstrPSLDeteData.fRecObj1OdoAngle[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch]] = GstrPSLDeteData.fRecObj1OdoAngle[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 1];
                    if(LenuSnsch == PSL_SNS_CH_FLS || LenuSnsch == PSL_SNS_CH_FRS)
                    {
                        fFillSnsCoorX = LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD] + fEchoSum;
                        enuSnsGroup = SNS_INSTALL_GROUP_FRONT;
                    }
                #if 0
                    else if(LenuSnsch == PSL_SNS_CH_RLS || LenuSnsch == PSL_SNS_CH_RRS)
                    {
                        fFillSnsCoorX = -(LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD] + fEchoSum);
                        enuSnsGroup = SNS_INSTALL_GROUP_REAR;
                    }
                #endif
                    fFillSnsCoorY = 0;

                    if(LenuSnsch == PSL_SNS_CH_FLS/* || LenuSnsch == PSL_SNS_CH_RLS*/)
                    {
                        enuSnsCh = SNS_CH_FRLS;
                    }               
                    else if(LenuSnsch == PSL_SNS_CH_FRS/* || LenuSnsch == PSL_SNS_CH_RRS*/)
                    {
                        enuSnsCh = SNS_CH_FRRS;
                    }

                    PubAI_UpdateSnsInCarCoor(GstrRamSnsCoor[enuSnsGroup][enuSnsCh].cRadarX,GstrRamSnsCoor[enuSnsGroup][enuSnsCh].cRadarY,\
                    GstrSnsOutAngle[enuSnsGroup].fSinA[enuSnsCh],GstrSnsOutAngle[enuSnsGroup].fCosA[enuSnsCh]);

                    PubAI_TransObjSnsCoorToCarCoor(&fFillSnsCoorX,&fFillSnsCoorY,&fFillCarCoorX,&fFillCarCoorY);

                    fFillOdoX = (GstrPSLDeteData.strRecObj1Odo[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 2].fObjX + LpStrPSLUseOdo->fCarCoorX) / 2;
                    fFillOdoY = (GstrPSLDeteData.strRecObj1Odo[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 2].fObjY + LpStrPSLUseOdo->fCarCoorY) / 2;
                    fFillAngle = (LpStrPSLUseOdo->fCarYawAngle + GstrPSLDeteData.fRecObj1OdoAngle[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 2]) / 2;
                    fCarCos = cosf(fFillAngle);
                    fCarSin = sinf(fFillAngle);

                    PubAI_UpdateCarOdoCoor(fFillOdoX,fFillOdoY,fCarSin,fCarCos);
                    PubAI_TransObjCarCoorToOdoCoor(&fFillCarCoorX,&fFillCarCoorY,&fFillOdoCoorX,&fFillOdoCoorY);
                    
                    GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 1] = LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD] + fEchoSum;
                    GstrPSLDeteData.strObj1Data[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 1].fObjX = fFillOdoCoorX;
                    GstrPSLDeteData.strObj1Data[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 1].fObjY = fFillOdoCoorY;
                    GstrPSLDeteData.strRecObj1Odo[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 1].fObjX = fFillOdoX;
                    GstrPSLDeteData.strRecObj1Odo[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 1].fObjY = fFillOdoY;
                    GstrPSLDeteData.fRecObj1OdoAngle[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 1] = fFillAngle;
                    GstrPSLDeteData.u8RecordObjCnt[LenuSnsch]++;
                }

            }

        }
    }
}

/******************************************************************************
  * 函数名称: PSLCalObj1DataMin
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-12-18 14:20   V0.1      22866        初次发布
  ******************************************************************************/
void PSLCalObj1DataMin(PSLSnsChannelType LenuSnsch)
{
    uint8 i = 0;
    uint8 u8StartCnt = 0;
    float fOnePointx = 0;
    float fOnePointy = 0;
    float fTwoPointx = 0;
    float fTwoPointy = 0;
    float fObj1MoveDis = 0;
    if(GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] <= PSL_RECORD_OBJ_MAX && GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] > 2)
    {
        fOnePointx = GstrPSLDeteData.strRecObj1Odo[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 1].fObjX;
        fOnePointy = GstrPSLDeteData.strRecObj1Odo[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 1].fObjY;
        for(i = GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 2;i > 0;i--)
        {
            if(i >= 0)
            {
                fTwoPointx = GstrPSLDeteData.strRecObj1Odo[LenuSnsch][i].fObjX;
                fTwoPointy = GstrPSLDeteData.strRecObj1Odo[LenuSnsch][i].fObjY;
                fObj1MoveDis = PubAI_CalTwoPointDis(fOnePointx,fOnePointy,fTwoPointx,fTwoPointy);
                //printf("fObj1MoveDis:%.1f,CurEcho:%.1f\r\n",fObj1MoveDis,GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i]);
                if(fObj1MoveDis > 2500)
                {
                    u8StartCnt = i;
                    //printf("Obj1 data Over 2.5m cnt:%d,Echo:%.1f\r\n",u8StartCnt,GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][u8StartCnt]);
                    break;
                }
            }
        }
    }
    GstrPSLDeteData.fFirstObjMinDis[LenuSnsch] = GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][u8StartCnt];
    GstrPSLDeteData.fFirstObjMinDisX[LenuSnsch] = GstrPSLDeteData.strObj1Data[LenuSnsch][u8StartCnt].fObjX;
    GstrPSLDeteData.fFirstObjMinDisY[LenuSnsch] = GstrPSLDeteData.strObj1Data[LenuSnsch][u8StartCnt].fObjY;
    GstrPSLDeteData.fRecObj1CornerOdox[LenuSnsch] = GstrPSLDeteData.strRecObj1Odo[LenuSnsch][u8StartCnt].fObjX;
    GstrPSLDeteData.fRecObj1CornerOdoy[LenuSnsch] = GstrPSLDeteData.strRecObj1Odo[LenuSnsch][u8StartCnt].fObjY;
    GstrPSLDeteData.u8ObjMinIndex[LenuSnsch] = u8StartCnt;
    
    for(i = u8StartCnt + 1;i < GstrPSLDeteData.u8RecordObjCnt[LenuSnsch];i++)
    {
        if(i < PSL_RECORD_OBJ_MAX)
        {
            if(GstrPSLDeteData.fFirstObjMinDis[LenuSnsch] > GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i])
            {
                GstrPSLDeteData.fFirstObjMinDis[LenuSnsch] = GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i];
                GstrPSLDeteData.fFirstObjMinDisX[LenuSnsch] = GstrPSLDeteData.strObj1Data[LenuSnsch][i].fObjX;
                GstrPSLDeteData.fFirstObjMinDisY[LenuSnsch] = GstrPSLDeteData.strObj1Data[LenuSnsch][i].fObjY;
                GstrPSLDeteData.fRecObj1CornerOdox[LenuSnsch] = GstrPSLDeteData.strRecObj1Odo[LenuSnsch][i].fObjX;
                GstrPSLDeteData.fRecObj1CornerOdoy[LenuSnsch] = GstrPSLDeteData.strRecObj1Odo[LenuSnsch][i].fObjY;
                GstrPSLDeteData.u8ObjMinIndex[LenuSnsch] = i;
            }
        }
    }
}
/******************************************************************************
  * 函数名称: PSLRecordOdoAndObj1Data
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-3-2 14:20     V0.1      22866        初次发布
  ******************************************************************************/
void PSLRecordOdoAndObj1Data(PSLSnsChannelType LenuSnsch)
{
    float fCurCarCoorx = 0.0;
    float fCurCarCoory = 0.0;
    float fBackUpCarCoorx= 0.0;
    float fBackUpCarCoory= 0.0;
    float fCurMasCoorx = 0.0;
    float fCurMasCoory = 0.0;
	float fTwoPointDis = 0.0;
    
    uint8 i = 0;
    uint8 u8RecordObj1Flag = 0;
 
    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;

    PSLRecordObjCoorType *LpPSLRecordObj;
    LpPSLRecordObj = &GstrPSLRecordObjCoor;
    float fCurEchoAverage = 0.0;
    float fSlopeSum = 0.0;
    
    fSlopeSum = PSLCalSlopeDec(GstrPSLDeteData.fRecordCarSlope[LenuSnsch][PSL_CAL_SLOPE_OBJ1],LpStrPSLUseOdo->fCarYawAngle);
    if(fSlopeSum < 0)
    {
        fSlopeSum = -fSlopeSum;
    }

    if(fSlopeSum > APA_30_ANGLE)
    {      
        GstrPSLDeteData.enuPSLDetSts[LenuSnsch] = PSL_DETE_INIT;
        return;
    }
    if(LpPSLRecordObj->enuMeasMode[LenuSnsch][PSL_DETE_FIFTH] == PSL_SNS_MEAS_STD && LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] < PSL_SNS_MEAS_CHIRP_MIN + 150)
    {
        if(((LpPSLRecordObj->enuMeasMode[LenuSnsch][PSL_DETE_THIRD] == PSL_SNS_MEAS_STD) 
            &&(LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD] + PSL_ECHO_20CM_VALUE))
            && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] + PSL_ECHO_20CM_VALUE)))
            || ((LpPSLRecordObj->enuMeasMode[LenuSnsch][PSL_DETE_SECOND] == PSL_SNS_MEAS_STD && LpPSLRecordObj->enuMeasMode[LenuSnsch][PSL_DETE_FOURTH] != PSL_SNS_MEAS_STD)
                && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_SECOND] + PSL_ECHO_20CM_VALUE))
                && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_SECOND] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] + PSL_ECHO_20CM_VALUE)))
            || ((LpPSLRecordObj->enuMeasMode[LenuSnsch][PSL_DETE_FIFTH] == PSL_SNS_MEAS_STD && LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] < PSL_SNS_MEAS_CHIRP_MIN + 150)
            && (LpPSLRecordObj->enuMeasMode[LenuSnsch][PSL_DETE_FOURTH] == PSL_SNS_MEAS_STD)
            && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] + PSL_ECHO_20CM_VALUE))
            && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] + PSL_ECHO_20CM_VALUE))))
        {
            u8RecordObj1Flag = 1;
        }
    }

    
    
        
    if(((LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] + PSL_ECHO_20CM_VALUE))
        && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] + PSL_ECHO_20CM_VALUE))
        && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD] + PSL_ECHO_20CM_VALUE))
        && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] + PSL_ECHO_20CM_VALUE)))
        || ((LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD] + PSL_ECHO_25CM_VALUE))
        && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] + PSL_ECHO_25CM_VALUE))
        && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_SECOND] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD] + PSL_ECHO_20CM_VALUE))
        && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_SECOND] + PSL_ECHO_20CM_VALUE)))
        ||((LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] + PSL_ECHO_20CM_VALUE))
        && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] + PSL_ECHO_20CM_VALUE))
        && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_SECOND] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] + PSL_ECHO_20CM_VALUE))
        && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_SECOND] + PSL_ECHO_20CM_VALUE)))
        || (u8RecordObj1Flag == 1))
    {
        u8RecordObj1Flag = 0;
        fCurMasCoorx = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_FIFTH].fObjX;
        fCurMasCoory = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_FIFTH].fObjY;
        //if(PSLCalCurEchoAverage(LenuSnsch) == TRUE)
        //    fCurEchoAverage = GstrPSLDeteData.fCurEchoAverage[LenuSnsch];
        //else
        
        fCurEchoAverage = LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH];
        
        if(fCurEchoAverage < GstrPSL_APPPara_Ram.u16DetObjDisMax + PSL_ECHO_20CM_VALUE)
        { 
            fCurCarCoorx = LpStrPSLUseOdo->fCarCoorX;
            fCurCarCoory = LpStrPSLUseOdo->fCarCoorY;
            fBackUpCarCoorx = GstrPSLDeteData.fObj1BackUpCarCoorx[LenuSnsch];
            fBackUpCarCoory = GstrPSLDeteData.fObj1BackUpCarCoory[LenuSnsch];
    		fTwoPointDis = PubAI_CalTwoPointDis(fCurCarCoorx, fCurCarCoory, fBackUpCarCoorx, fBackUpCarCoory);
            if(fTwoPointDis > PSL_RECORD_MIN_DOS)
            {
                GstrPSLDeteData.fObj1BackUpCarCoorx[LenuSnsch] = fCurCarCoorx;
                GstrPSLDeteData.fObj1BackUpCarCoory[LenuSnsch] = fCurCarCoory;
                GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch]] = fCurEchoAverage;
                GstrPSLDeteData.strObj1Data[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch]].fObjX = fCurMasCoorx;
                GstrPSLDeteData.strObj1Data[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch]].fObjY = fCurMasCoory;
                GstrPSLDeteData.strRecObj1Odo[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch]].fObjX = fCurCarCoorx;
                GstrPSLDeteData.strRecObj1Odo[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch]].fObjY = fCurCarCoory;
                GstrPSLDeteData.fRecObj1OdoAngle[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch]] = LpStrPSLUseOdo->fCarYawAngle;
                GstrPSLDeteData.u8RecordObjCnt[LenuSnsch]++;
                if(GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] >= PSL_RECORD_OBJ_MAX)
                {
                    for(i = 0;i < PSL_RECORD_OBJ_MAX - 1;i++) /* 最少保存2米OBJ数据 */
                    {
                        GstrPSLDeteData.strObj1Data[LenuSnsch][i].fObjX = GstrPSLDeteData.strObj1Data[LenuSnsch][i + 1].fObjX;
                        GstrPSLDeteData.strObj1Data[LenuSnsch][i].fObjY = GstrPSLDeteData.strObj1Data[LenuSnsch][i + 1].fObjY;
                        GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i] = GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i + 1];
                        GstrPSLDeteData.strRecObj1Odo[LenuSnsch][i].fObjX = GstrPSLDeteData.strRecObj1Odo[LenuSnsch][i + 1].fObjX;
                        GstrPSLDeteData.strRecObj1Odo[LenuSnsch][i].fObjY = GstrPSLDeteData.strRecObj1Odo[LenuSnsch][i + 1].fObjY;
                        GstrPSLDeteData.fRecObj1OdoAngle[LenuSnsch][i] = GstrPSLDeteData.fRecObj1OdoAngle[LenuSnsch][i + 1];
                    }
                    GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] = PSL_RECORD_OBJ_MAX - 1;

                }
                PSLFillObj1Data(LenuSnsch);
                PSLCalObj1DataMin(LenuSnsch);
            }
            
        }
        else
        {
            PSLRecordCurbData(LenuSnsch);
        }
    }
}


/******************************************************************************
  * 函数名称: PSLAnalysisTableData
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-3-2 14:20     V0.1      22866        初次发布
  ******************************************************************************/
void PSLAnalysisTableData(PSLSnsChannelType LenuSnsch)
{
    uint8 i,j,u8delCnt = 0;
    float fTwoMinDis;
    uint8 u8Obj1Start = 0;

    float fOnePointx = 0.0;
    float fOnePointy = 0.0;
    float fTwoPointx = 0.0;
    float fTwoPointy = 0.0;

    float fObj1MoveDis = 0.0;
    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;
    uint8 u8DelFlag = 0;

    
    if(GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] > 3)
    {
        fOnePointx = GstrPSLDeteData.strRecObj1Odo[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 1].fObjX;
        fOnePointy = GstrPSLDeteData.strRecObj1Odo[LenuSnsch][GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 1].fObjY;
        for(i = GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 2;i > 0;i--)
        {
            fTwoPointx = GstrPSLDeteData.strRecObj1Odo[LenuSnsch][i].fObjX;
            fTwoPointy = GstrPSLDeteData.strRecObj1Odo[LenuSnsch][i].fObjY;
            fObj1MoveDis = PubAI_CalTwoPointDis(fOnePointx,fOnePointy,fTwoPointx,fTwoPointy);
            if(fObj1MoveDis > 2000)
            {
                break;
            }
        }
        u8Obj1Start = i;
        if(GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - u8Obj1Start > 3)
        {
            for(i = u8Obj1Start;i < GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 1;i++)
            {      
                fTwoMinDis = ABS(GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i],GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i + 1]);
                //if(i > GstrPSLDeteData.u8ObjMinIndex[LenuSnsch])
                {
                    if(fTwoMinDis > PSL_ECHO_20CM_VALUE)
                    {
                        if((i + 2) <= (GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 1))
                        {
                            if(GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i + 1] > (GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i + 2] + PSL_ECHO_20CM_VALUE))
                            {
                                u8DelFlag = 1;
                                u8delCnt = i + 1;
                            }
                            else
                            {
                                if((ABS(GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i],GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i + 2]) > PSL_ECHO_20CM_VALUE))
                                {
                                    u8DelFlag = 1;
                                    u8delCnt = i;
                                }
                                                        
                            }
                        }
                    }
                }
                if(fTwoMinDis > PSL_ECHO_25CM_VALUE || u8DelFlag == 1)
                {
                    u8DelFlag = 0;
             
                    for(j = u8delCnt;j < GstrPSLDeteData.u8RecordObjCnt[LenuSnsch];j++)
                    {
                        GstrPSLDeteData.strObj1Data[LenuSnsch][j].fObjX = GstrPSLDeteData.strObj1Data[LenuSnsch][j + 1].fObjX;
                        GstrPSLDeteData.strObj1Data[LenuSnsch][j].fObjY = GstrPSLDeteData.strObj1Data[LenuSnsch][j + 1].fObjY;
                        GstrPSLDeteData.strRecObj1Odo[LenuSnsch][j].fObjX = GstrPSLDeteData.strRecObj1Odo[LenuSnsch][j + 1].fObjX;
                        GstrPSLDeteData.strRecObj1Odo[LenuSnsch][j].fObjY = GstrPSLDeteData.strRecObj1Odo[LenuSnsch][j + 1].fObjY;
                        GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][j] = GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][j + 1];
                        GstrPSLDeteData.fRecObj1OdoAngle[LenuSnsch][j] = GstrPSLDeteData.fRecObj1OdoAngle[LenuSnsch][j + 1];
                    }
                    GstrPSLDeteData.u8RecordObjCnt[LenuSnsch]--;
                    if(i > 0)
                        i--;
                }
            }        	
        }
        
        GstrPSLDeteData.fFirstObjMinDis[LenuSnsch] = GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][u8Obj1Start];
        GstrPSLDeteData.u8ObjMinIndex[LenuSnsch] = u8Obj1Start;
        GstrPSLDeteData.fFirstObjMinDisX[LenuSnsch] = GstrPSLDeteData.strObj1Data[LenuSnsch][u8Obj1Start].fObjX;
        GstrPSLDeteData.fFirstObjMinDisY[LenuSnsch] = GstrPSLDeteData.strObj1Data[LenuSnsch][u8Obj1Start].fObjY;
        GstrPSLDeteData.fRecObj1CornerOdox[LenuSnsch] = GstrPSLDeteData.strRecObj1Odo[LenuSnsch][u8Obj1Start].fObjX;
        GstrPSLDeteData.fRecObj1CornerOdoy[LenuSnsch] = GstrPSLDeteData.strRecObj1Odo[LenuSnsch][u8Obj1Start].fObjY;
        
        i = u8Obj1Start + 1;
       
        for(;i < GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 1;i++)
        {
            //if(i < PSL_RECORD_OBJ_MAX - 1)
            {
                if(GstrPSLDeteData.fFirstObjMinDis[LenuSnsch] > GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i])
                {
                    GstrPSLDeteData.fFirstObjMinDis[LenuSnsch] = GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i];
                    GstrPSLDeteData.fFirstObjMinDisX[LenuSnsch] = GstrPSLDeteData.strObj1Data[LenuSnsch][i].fObjX;
                    GstrPSLDeteData.fFirstObjMinDisY[LenuSnsch] = GstrPSLDeteData.strObj1Data[LenuSnsch][i].fObjY;
                    GstrPSLDeteData.fRecObj1CornerOdox[LenuSnsch] = GstrPSLDeteData.strRecObj1Odo[LenuSnsch][i].fObjX;
                    GstrPSLDeteData.fRecObj1CornerOdoy[LenuSnsch] = GstrPSLDeteData.strRecObj1Odo[LenuSnsch][i].fObjY;
                    GstrPSLDeteData.u8ObjMinIndex[LenuSnsch] = i;
                }
            }
        }
        
        
    }
    else
    {

    }
}


/******************************************************************************
  * 函数名称: PSLCalObjAndCurbSlope
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-3-9 14:20     V0.1      22866        初次发布
  ******************************************************************************/
void PSLCalObjAndCurbSlope(PSLSnsChannelType LenuSnsch,PSLCalSlopeType LenuSlopeType)
{
    uint8 i;
    float fPointOnex= 0.0;
    float fPointOney= 0.0;
    float fPointTwox= 0.0;
    float fPointTwoy= 0.0;
    uint8 u8StartCnt = 0;
    uint8 u8endCnt = 0;
    
    if(LenuSlopeType == PSL_CAL_SLOPE_OBJ1)
    {
        u8StartCnt = GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] / 3;
        u8endCnt = 2 * u8StartCnt;
        for(i = u8StartCnt;i <= u8endCnt;i++)
        {
            fPointOnex = GstrPSLDeteData.strObj1Data[LenuSnsch][i].fObjX;
            fPointOney = GstrPSLDeteData.strObj1Data[LenuSnsch][i].fObjY;
            fPointTwox = GstrPSLDeteData.strObj1Data[LenuSnsch][i + 1].fObjX;
            fPointTwoy = GstrPSLDeteData.strObj1Data[LenuSnsch][i + 1].fObjY;

            GstrPSLDeteData.fSlopeData[LenuSnsch][GstrPSLDeteData.u8SlopeCnt[LenuSnsch]] = atan2f((fPointTwoy - fPointOney),(fPointTwox - fPointOnex));
            GstrPSLDeteData.u8SlopeCnt[LenuSnsch]++;
        }       
    }
    else if(LenuSlopeType == PSL_CAL_SLOPE_CURB)
    {
        u8StartCnt = GstrPSLDeteData.u8CurbDataCnt[LenuSnsch] / 3;
        u8endCnt = 2 * u8StartCnt;
        for(i = u8StartCnt;i <= u8endCnt;i++)
        {
            fPointOnex = GstrPSLDeteData.strCurbCoorData[LenuSnsch][i].fObjX;
            fPointOney = GstrPSLDeteData.strCurbCoorData[LenuSnsch][i].fObjY;
            fPointTwox = GstrPSLDeteData.strCurbCoorData[LenuSnsch][i + 1].fObjX;
            fPointTwoy = GstrPSLDeteData.strCurbCoorData[LenuSnsch][i + 1].fObjY;

            GstrPSLDeteData.fSlopeData[LenuSnsch][GstrPSLDeteData.u8SlopeCnt[LenuSnsch]] = atan2f((fPointTwoy - fPointOney),(fPointTwox - fPointOnex));
            GstrPSLDeteData.u8SlopeCnt[LenuSnsch]++;
        }      
    }
    else if(LenuSlopeType == PSL_CAL_SLOPE_OBJ2)
    {
        u8StartCnt = GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch] / 3;
        u8endCnt = 2 * u8StartCnt;
        for(i = u8StartCnt;i <= u8endCnt;i++)
        {
            fPointOnex = GstrPSLDeteData.strObj2Data[LenuSnsch][i].fObjX;
            fPointOney = GstrPSLDeteData.strObj2Data[LenuSnsch][i].fObjY;
            fPointTwox = GstrPSLDeteData.strObj2Data[LenuSnsch][i + 1].fObjX;
            fPointTwoy = GstrPSLDeteData.strObj2Data[LenuSnsch][i + 1].fObjY;

            GstrPSLDeteData.fSlopeData[LenuSnsch][GstrPSLDeteData.u8SlopeCnt[LenuSnsch]] = atan2f((fPointTwoy - fPointOney),(fPointTwox - fPointOnex));
            GstrPSLDeteData.u8SlopeCnt[LenuSnsch]++;
        }
    }

     PubAI_BubbleSortFor_float_Type(GstrPSLDeteData.u8SlopeCnt[LenuSnsch],&GstrPSLDeteData.fSlopeData[LenuSnsch][0],SORT_ASCENDING);
}


/******************************************************************************
  * 函数名称: SubFunc_Transposed_matrix
  * 
  * 功能描述: 矩阵转置
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  *
  ******************************************************************************/
static void SubFunc_Transposed_matrix(double (*A)[FITS_N_ST_ORDER + 1], uint16 m, uint16 n, double (*B)[MAX_FITS_CNT])
{
	uint16 i, j;
	for (i = 0; i < m; i++)
	{
		for (j = 0; j < n; j++)
		{
			B[j][i] = A[i][j];
		}
	}
}

/******************************************************************************
  * 函数名称: SubFunc_Matrix_multiplication1
  * 
  * 功能描述: 矩阵相乘
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  *
  ******************************************************************************/

static void SubFunc_Matrix_multiplication1(double(*A)[MAX_FITS_CNT], uint16 m, uint16 n, double(*B)[FITS_N_ST_ORDER+1], uint16 p, uint16 q, double(*C)[FITS_N_ST_ORDER+1])
{
	if (n != p)
	{
		return;
	}
	uint16 i, j, k;

	for (i = 0; i < m; i++)
	{
		for (j = 0; j < q; j++)
		{
			C[i][j] = 0;
			for (k = 0; k < n; k++)
			{
				C[i][j] += A[i][k] * B[k][j];
			}
		}
	}
}

/******************************************************************************
  * 函数名称: SubFunc_Matrix_multiplication2
  * 
  * 功能描述: 矩阵相乘
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  *
  ******************************************************************************/

static void SubFunc_Matrix_multiplication2(double (*A)[FITS_N_ST_ORDER + 1], uint16 m, uint16 n, double (*B)[FITS_N_ST_ORDER], uint16 p, uint16 q, double (*C)[FITS_N_ST_ORDER])
{
	if (n != p)
	{
		return;
	}
	uint16 i, j, k;

	for (i = 0; i < m; i++)
	{
		for (j = 0; j < q; j++)
		{
			C[i][j] = 0;
			for (k = 0; k < n; k++)
			{
				C[i][j] += A[i][k] * B[k][j];
			}
		}
	}

	//for (i = 0; i < m; i++)
	//{
	//	for (j = 0; j < q; j++)
	//	{
	//		printf("%f,", C[i][j]);
	//	}
	//	printf("\r\n");
	//}
}

/******************************************************************************
  * 函数名称: SubFunc_Inverse_of_a_matrix
  * 
  * 功能描述: 矩阵的逆
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  *
  ******************************************************************************/

static void SubFunc_Inverse_of_a_matrix(double (*A)[FITS_N_ST_ORDER + 1], sint16 n, double (*B)[FITS_N_ST_ORDER + 1])
{
	sint16 i, j, k;
	double t[1][4];
	double ratio = 0;
	double temp[2][4];

	for (i = 0; i < n; i++)
	{
		for (j = 0; j < n; j++)
		{
			temp[i][j] = A[i][j];
		}
		for (j = n; j < 2 * n; j++)
		{
			if (i == j - n)
			{
				temp[i][j] = 1;
			}
			else
			{
				temp[i][j] = 0;
			}
		}
	}

	/* 高斯消元 */
	for (i = 0; i < n - 1; i++)
	{
		if (temp[i][i] == 0)
		{
			for (j = i + 1; j < n; j++)
			{
				if (temp[j][i] != 0)
				{
					memcpy(&t[0][0], &temp[i][0], 4);
					memcpy(&temp[i][0], &temp[j][0], 4);
					memcpy(&temp[i][0], &t[0][0], 4);
					break;
				}
			}
		}

		if (temp[i][i] == 0)
		{
			return;
		}
		for (j = i + 1; j < n; j++)
		{
			ratio = temp[j][i] / temp[i][i];
			for (k = i; k < 2 * n; k++)
			{
				temp[j][k] -= ratio * temp[i][k];
			}
		}
	}

	/* 高斯消元 */
	for (i = n - 1; i > 0; i--)
	{
		if (temp[i][i] == 0)
		{
			return;
		}
		for (j = i - 1; j >= 0; j--)
		{
			ratio = temp[j][i] / temp[i][i];
			for (k = i; k < 2 * n; k++)
			{
				temp[j][k] -= ratio * temp[i][k];
			}
		}
	}

	/* 单位矩阵 */
	for (i = 0; i < n; i++)
	{
		ratio = temp[i][i];
		for (j = i; j < 2 * n; j++)
		{
			temp[i][j] /= ratio;
		}
	}

	for (i = 0; i < n; i++)
	{
		for (j = 0; j < n; j++)
		{
			B[i][j] = temp[i][j + n];
		}
	}
}

/******************************************************************************
  * 函数名称: SubFunc_Polyval
  * 
  * 功能描述: 最小误差
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  *
  ******************************************************************************/

static double SubFunc_Polyval(double *p, uint16 n, double x)
{
	uint16 i;
	double y = 0;
	for (i = 0; i < n; i++)
	{
		y += p[i] * pow(x, n - i - 1);
	}
	return y;
}

/******************************************************************************
  * 函数名称: SlotPubFunc_1stOrderPolyfit
  * 
  * 功能描述: 拟合最大点数 需与 MAX_FITS_CNT 一致
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  *
  ******************************************************************************/

SlotAlgorPolyfit_strType SlotPubFunc_1stOrderPolyfit(double *x, double *y, sint32 m)
{
	sint32 i, j;
	double f;
	SlotAlgorPolyfit_strType LstrResult;

	for (i = 0; i < FITS_N_ST_ORDER; i++)
	{
		LstrResult.coeff[i] = 0;
	}
	LstrResult.error = 0;

	if (m > MAX_FITS_CNT)
	{
		for (i = 0; i < FITS_N_ST_ORDER; i++)
		{
			LstrResult.coeff[i] = 0xFFFFFFFF;
		}
		LstrResult.error = 0xFFFFFFFF;
		return LstrResult;
	}

	double X[MAX_FITS_CNT][FITS_N_ST_ORDER + 1];
	double XT[FITS_N_ST_ORDER + 1][MAX_FITS_CNT];
	double XTX[FITS_N_ST_ORDER + 1][FITS_N_ST_ORDER + 1];
	double XTXI[FITS_N_ST_ORDER + 1][FITS_N_ST_ORDER + 1];
	double XTy[FITS_N_ST_ORDER + 1][FITS_N_ST_ORDER];
	double p[FITS_N_ST_ORDER + 1][FITS_N_ST_ORDER];

	for (i = 0; i < m; i++)
	{
		for (j = 0; j < FITS_N_ST_ORDER + 1; j++)
		{
			X[i][j] = pow(x[i], FITS_N_ST_ORDER - j);
		}
	}

	SubFunc_Transposed_matrix(X, m, FITS_N_ST_ORDER + 1, XT);
	SubFunc_Matrix_multiplication1(XT, FITS_N_ST_ORDER + 1, m, X, m, FITS_N_ST_ORDER + 1, XTX);
	SubFunc_Inverse_of_a_matrix(XTX, FITS_N_ST_ORDER + 1, XTXI);

	for (i = 0; i < FITS_N_ST_ORDER + 1; i++)
	{
		XTy[i][0] = 0;
		for (j = 0; j < m; j++)
		{
			XTy[i][0] += XT[i][j] * y[j];
		}
	}

	SubFunc_Matrix_multiplication2(XTXI, FITS_N_ST_ORDER + 1, FITS_N_ST_ORDER + 1, XTy, FITS_N_ST_ORDER + 1, 1, p);

	for (i = 0; i < FITS_N_ST_ORDER + 1; i++)
	{
		LstrResult.coeff[i] = p[i][0];
	}

	for (i = 0; i < m; i++)
	{
		f = SubFunc_Polyval(&LstrResult.coeff[0], FITS_N_ST_ORDER + 1, x[i]);
		LstrResult.error += pow(y[i] - f, 2);
	}

	return LstrResult;
}
/******************************************************************************
  * 函数名称: PSLCalCarPosition
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2024-01-12 08:10     V0.1      22866        初次发布
  ******************************************************************************/
PSLCarPositionType PSLCalCarPosition(PSLSnsChannelType LenuSnsch)
{
    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;
    PSLCarPositionType enuCarPosition = PSL_CAR_POSITION_1;
    PSL_DEBUG_PRINTF("fCarYawAngle:%.5f\r\n",LpStrPSLUseOdo->fCarYawAngle);
    if(LpStrPSLUseOdo->fCarYawAngle > APA_90_ANGLE && LpStrPSLUseOdo->fCarYawAngle < APA_180_ANGLE)
    {
        enuCarPosition = PSL_CAR_POSITION_2;
    }
    else if(LpStrPSLUseOdo->fCarYawAngle > _APA_180_ANGLE && LpStrPSLUseOdo->fCarYawAngle < _APA_90_ANGLE)
    {
        enuCarPosition = PSL_CAR_POSITION_3;
    }
    else if(LpStrPSLUseOdo->fCarYawAngle > _APA_90_ANGLE && LpStrPSLUseOdo->fCarYawAngle < APA_0_ANGLE)
    {
        enuCarPosition = PSL_CAR_POSITION_4;
    }
    else
    {
        enuCarPosition = PSL_CAR_POSITION_1;
    }

    return enuCarPosition;
}

/******************************************************************************
  * 函数名称: PSLCalObj1Slope
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-11-15 19:00     V0.1      22866        初次发布
  ******************************************************************************/
void PSLCalObj1Slope(PSLSnsChannelType LenuSnsch)
{
    uint8 i = 0,j = 0;
    uint8 u8RecdObjCnt = 0;
    
    uint8 u8TargetStartCnt = 0;
    uint8 u8TargetEndCnt = 0;
    uint8 u8TargetFlag = 0;
    uint8 u8TargetLen = 0;
    float fFirstEcho = 0;
    float fSecondEcho = 0;
    float fThirdEcho = 0;
    double fObjCoorx[40] = {0};
    double fObjCoory[40] = {0};
    float fOnePointx = 0.0;
    float fOnePointy = 0.0;
    float fTwoPointx = 0.0;
    float fTwoPointy = 0.0;
    float fObj1MoveDis = 0.0;
    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;
    uint8 u8OffectFlag = 0;
    PSLCarPositionType enuCarPosition = PSL_CAR_POSITION_1;
    SlotAlgorPolyfit_strType result1;
    SlotAlgorPolyfit_strType result2;

    u8RecdObjCnt = GstrPSLDeteData.u8RecordObjCnt[LenuSnsch];
    if(u8RecdObjCnt > 5)
    {
        for(i = 0;i < u8RecdObjCnt - 2;i++)
        {
            fFirstEcho = GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i];
            fSecondEcho = GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i + 1];
            fThirdEcho = GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i + 2];

            if(((fFirstEcho + PSL_ECHO_6CM_VALUE) >= fSecondEcho)
                && ((fSecondEcho + PSL_ECHO_6CM_VALUE) >= fFirstEcho)
                && ((fSecondEcho + PSL_ECHO_6CM_VALUE) >= fThirdEcho)
                && ((fThirdEcho + PSL_ECHO_6CM_VALUE) >= fSecondEcho)
                && ((fThirdEcho + PSL_ECHO_6CM_VALUE) >= fFirstEcho)
                && ((fFirstEcho + PSL_ECHO_6CM_VALUE) >= fThirdEcho)
                && u8TargetFlag == 0)
            {
                u8TargetStartCnt = i;
                u8TargetFlag = 1;               
            }
            if(u8TargetFlag == 1 && i < u8RecdObjCnt)
            {
                u8TargetEndCnt = u8RecdObjCnt - 1;               
                break;
            }
        }
        fOnePointx = GstrPSLDeteData.strRecObj1Odo[LenuSnsch][u8TargetStartCnt].fObjX;
        fOnePointy = GstrPSLDeteData.strRecObj1Odo[LenuSnsch][u8TargetStartCnt].fObjY;
        for(i = u8TargetStartCnt + 1;i < u8RecdObjCnt;i++)
        {
            fTwoPointx = GstrPSLDeteData.strRecObj1Odo[LenuSnsch][i].fObjX;
            fTwoPointy = GstrPSLDeteData.strRecObj1Odo[LenuSnsch][i].fObjY;
            fObj1MoveDis = PubAI_CalTwoPointDis(fOnePointx,fOnePointy,fTwoPointx,fTwoPointy);
            if(fObj1MoveDis > 2500)
            {

                break;
            }
        }
        if(u8TargetEndCnt > i)
        {
            u8TargetEndCnt = i;
        }


        if((LpStrPSLUseOdo->fCarYawAngle > APA_80_ANGLE && LpStrPSLUseOdo->fCarYawAngle < APA_100_ANGLE)
            ||(LpStrPSLUseOdo->fCarYawAngle > _APA_100_ANGLE && LpStrPSLUseOdo->fCarYawAngle < _APA_80_ANGLE))
        {
            u8OffectFlag = 1;
        }
        for(i = u8TargetStartCnt;i< u8TargetEndCnt;i++)
        {
            if(u8OffectFlag == 1)
            {
                fObjCoorx[j] = (double)(-GstrPSLDeteData.strObj1Data[LenuSnsch][i].fObjY);
                fObjCoory[j] = (double)GstrPSLDeteData.strObj1Data[LenuSnsch][i].fObjX;

            }
            else
            {
                fObjCoorx[j] = (double)GstrPSLDeteData.strObj1Data[LenuSnsch][i].fObjX;
                fObjCoory[j] = (double)GstrPSLDeteData.strObj1Data[LenuSnsch][i].fObjY;
            }
            j++;
            u8TargetLen = j;
        }
        

        if(u8TargetLen >= 4)
        {

            result1 = SlotPubFunc_1stOrderPolyfit(&fObjCoorx[0],&fObjCoory[0],u8TargetLen);
            GstrPSLDeteData.fObj1Slope[LenuSnsch] = atan(result1.coeff[0]);
            if(u8OffectFlag == 1)
            {
                GstrPSLDeteData.fObj1Slope[LenuSnsch] -= 1.5708;
            }
            enuCarPosition = PSLCalCarPosition(LenuSnsch);
            PSL_DEBUG_PRINTF("fObj1Slope:%.5f\r\n",GstrPSLDeteData.fObj1Slope[LenuSnsch]);
            PSL_DEBUG_PRINTF("enuCarPosition:%d\r\n",enuCarPosition);
            switch(enuCarPosition)
            {
                case PSL_CAR_POSITION_1:
                    break;

                case PSL_CAR_POSITION_2:
                    GstrPSLDeteData.fObj1Slope[LenuSnsch] = -(GstrPSLDeteData.fObj1Slope[LenuSnsch] - APA_90_ANGLE);
                    break;

                case PSL_CAR_POSITION_3:
                    GstrPSLDeteData.fObj1Slope[LenuSnsch] -= APA_180_ANGLE;
                    break;

                case PSL_CAR_POSITION_4:
                        //GstrPSLDeteData.fObj1Slope[LenuSnsch] = _APA_90_ANGLE - GstrPSLDeteData.fObj1Slope[LenuSnsch];
                    break;
                    
                default:
                    break;
            }
            
            GstrPSLDeteData.fObj1Slope[LenuSnsch] -= LpStrPSLUseOdo->fCarYawAngle;
            
            PSL_DEBUG_PRINTF("GstrPSLDeteData.fObj1Slope:%.5f\r\n",GstrPSLDeteData.fObj1Slope[LenuSnsch]);
            PSL_DEBUG_PRINTF("fObj1Slope slope:%.1f\r\n",GstrPSLDeteData.fObj1Slope[LenuSnsch] * PSL_ANGLE_CONVERT_PARAMETER);
        }
        else
        {
            GstrPSLDeteData.fObj1Slope[LenuSnsch] = PSL_DET_INVALID_DATA;
        }
    }
}

/******************************************************************************
  * 函数名称: PSLCalObj2Slope
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-11-15 19:00     V0.1      22866        初次发布
  ******************************************************************************/
void PSLCalObj2Slope(PSLSnsChannelType LenuSnsch)
{
    uint8 i = 0,j = 0;
    uint8 u8RecdObjCnt = 0;
    
    uint8 u8TargetStartCnt = 0;
    uint8 u8TargetEndCnt = 0;
    uint8 u8TargetFlag = 0;
    uint8 u8TargetLen = 0;
    float fFirstEcho = 0;
    float fSecondEcho = 0;
    float fThirdEcho = 0;
    float fOnePointx = 0.0;
    float fOnePointy = 0.0;
    float fTwoPointx = 0.0;
    float fTwoPointy = 0.0;
    float fObj2MoveDis = 0.0;
    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;
    uint8 u8OffectFlag = 0;
    double fObjCoorx[40] = {0};
    double fObjCoory[40] = {0};
    SlotAlgorPolyfit_strType result;
    PSLCarPositionType enuCarPosition = PSL_CAR_POSITION_1;
    u8RecdObjCnt = GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch];

    if(u8RecdObjCnt > 5)
    {
        for(i = 0;i < u8RecdObjCnt - 2;i++)
        {
            fFirstEcho = GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][i];
            fSecondEcho = GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][i + 1];
            fThirdEcho = GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][i + 2];
            if(((fFirstEcho + PSL_ECHO_10CM_VALUE) >= fSecondEcho)
                && ((fSecondEcho + PSL_ECHO_10CM_VALUE) >= fFirstEcho)
                && ((fSecondEcho + PSL_ECHO_10CM_VALUE) >= fThirdEcho)
                && ((fThirdEcho + PSL_ECHO_10CM_VALUE) >= fSecondEcho)
                && ((fFirstEcho + PSL_ECHO_12CM_VALUE) >= fThirdEcho)
                && ((fThirdEcho + PSL_ECHO_12CM_VALUE) >= fFirstEcho)
                && u8TargetFlag == 0)
            {
                u8TargetStartCnt = i;
                u8TargetFlag = 1;
                break;
            }
        }
        fOnePointx = GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][u8RecdObjCnt - 1].fObjX;
        fOnePointy = GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][u8RecdObjCnt - 1].fObjY;
        for(i = u8RecdObjCnt - 2;i > 0;i--)
        {
            fTwoPointx = GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][i].fObjX;
            fTwoPointy = GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][i].fObjY;
            fObj2MoveDis = PubAI_CalTwoPointDis(fOnePointx,fOnePointy,fTwoPointx,fTwoPointy);
            if(fObj2MoveDis > 2500)
            {
                break;
            }
        }

        if(u8TargetStartCnt < i)
        {
            u8TargetStartCnt = i;
        }
        
        if(u8TargetFlag == 1)
        {
            u8TargetEndCnt = u8RecdObjCnt;
        }
    
        if((LpStrPSLUseOdo->fCarYawAngle > APA_80_ANGLE && LpStrPSLUseOdo->fCarYawAngle < APA_100_ANGLE)
            ||(LpStrPSLUseOdo->fCarYawAngle > _APA_100_ANGLE && LpStrPSLUseOdo->fCarYawAngle < _APA_80_ANGLE))
        {
            u8OffectFlag = 1;
        }
        for(i = u8TargetStartCnt;i< u8TargetEndCnt;i++)
        {
            if(u8OffectFlag == 1)
            {
                fObjCoorx[j] = (double)(-GstrPSLDeteData.strObj2Data[LenuSnsch][i].fObjY);
                fObjCoory[j] = (double)GstrPSLDeteData.strObj2Data[LenuSnsch][i].fObjX;

            }
            else
            {
                fObjCoorx[j] = (double)GstrPSLDeteData.strObj2Data[LenuSnsch][i].fObjX;
                fObjCoory[j] = (double)GstrPSLDeteData.strObj2Data[LenuSnsch][i].fObjY;
            }
            j++;
            u8TargetLen = j;
        }

        if(u8TargetLen >= 4)
        {

            result = SlotPubFunc_1stOrderPolyfit(&fObjCoorx[0],&fObjCoory[0],u8TargetLen);
            GstrPSLDeteData.fObj2Slope[LenuSnsch] = atan(result.coeff[0]);            
            if(u8OffectFlag == 1)
            {
                GstrPSLDeteData.fObj2Slope[LenuSnsch] -= 1.5708;
            }
            enuCarPosition = PSLCalCarPosition(LenuSnsch);
            PSL_DEBUG_PRINTF("fObj2Slope:%.5f\r\n",GstrPSLDeteData.fObj2Slope[LenuSnsch]);
            PSL_DEBUG_PRINTF("enuCarPosition:%d\r\n",enuCarPosition);
            switch(enuCarPosition)
            {
                case PSL_CAR_POSITION_1:
                    break;

                case PSL_CAR_POSITION_2:
                    GstrPSLDeteData.fObj2Slope[LenuSnsch] = -(GstrPSLDeteData.fObj2Slope[LenuSnsch] - APA_90_ANGLE);
                    break;

                case PSL_CAR_POSITION_3:
                    GstrPSLDeteData.fObj2Slope[LenuSnsch] -= APA_180_ANGLE;
                    break;

                case PSL_CAR_POSITION_4:
                        //GstrPSLDeteData.fObj2Slope[LenuSnsch] = _APA_90_ANGLE - GstrPSLDeteData.fObj2Slope[LenuSnsch];
                    break;
                    
                default:
                    break;
            }
            PSL_DEBUG_PRINTF("GstrPSLDeteData.fObj2Slope:%.5f\r\n",GstrPSLDeteData.fObj2Slope[LenuSnsch]);
            PSL_DEBUG_PRINTF("fObj2Slope slope:%.1f\r\n",GstrPSLDeteData.fObj2Slope[LenuSnsch] * PSL_ANGLE_CONVERT_PARAMETER);
            GstrPSLDeteData.fObj2Slope[LenuSnsch] -= LpStrPSLUseOdo->fCarYawAngle;
        }
        else
        {
            GstrPSLDeteData.fObj2Slope[LenuSnsch] = PSL_DET_INVALID_DATA;
        }
    }
}

/******************************************************************************
  * 函数名称: PSLCurbRecordDataPro
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-11-16 17:00     V0.1      22866        初次发布
  ******************************************************************************/
void PSLCurbRecordDataPro(PSLSnsChannelType LenuSnsch)
{
    uint8 i = 0,j = 0;
    float fCurbSum = 0;
    float fCurbAverage = 0;

    if(GstrPSLDeteData.u8CurbDataCnt[LenuSnsch] >= 10)
    {
        GstrPSLDeteData.u8CurbDataCnt[LenuSnsch] -= 4;
    }
    else if(GstrPSLDeteData.u8CurbDataCnt[LenuSnsch] >= 2)
    {
        GstrPSLDeteData.u8CurbDataCnt[LenuSnsch] -= 2;
    }
    for(i = 0;i < GstrPSLDeteData.u8CurbDataCnt[LenuSnsch];i++)
    {                                                                                                                                     
        fCurbSum += GstrPSLDeteData.u16CurbDepthData[LenuSnsch][i];
    }
    if(i > 0)
    {
        fCurbAverage = fCurbSum / i;
    }
    for(i = 0;i < GstrPSLDeteData.u8CurbDataCnt[LenuSnsch];i++)
    {
        if(i < PSL_RECORD_CURB_MAX)
        {
            if(ABS(fCurbAverage,GstrPSLDeteData.u16CurbDepthData[LenuSnsch][i]) > 380)
            {               
                for(j = i;j < GstrPSLDeteData.u8CurbDataCnt[LenuSnsch] -1;j++)
                {
                    GstrPSLDeteData.u16CurbDepthData[LenuSnsch][j] = GstrPSLDeteData.u16CurbDepthData[LenuSnsch][j + 1];
                    GstrPSLDeteData.strCurbCoorData[LenuSnsch][j].fObjX = GstrPSLDeteData.strCurbCoorData[LenuSnsch][j + 1].fObjX;
                    GstrPSLDeteData.strCurbCoorData[LenuSnsch][j].fObjY = GstrPSLDeteData.strCurbCoorData[LenuSnsch][j + 1].fObjY;
                    GstrPSLDeteData.strCurbOdoCoor[LenuSnsch][j].fObjX = GstrPSLDeteData.strCurbOdoCoor[LenuSnsch][j + 1].fObjX;
                    GstrPSLDeteData.strCurbOdoCoor[LenuSnsch][j].fObjY = GstrPSLDeteData.strCurbOdoCoor[LenuSnsch][j + 1].fObjY;
               }
               GstrPSLDeteData.u8CurbDataCnt[LenuSnsch]--; 
               if(i > 0)
               {
                   i--;
               }
                
            }
        }
    }
}

/******************************************************************************
  * 函数名称: PSLCalCurbSlope
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-11-16 17:00     V0.1      22866        初次发布
  ******************************************************************************/
void PSLCalCurbSlope(PSLSnsChannelType LenuSnsch)
{
    uint8 i = 0,j = 0;
    uint8 u8RecdObjCnt = 0;
    
    uint8 u8TargetStartCnt = 0;
    uint8 u8TargetEndCnt = 0;
    uint8 u8TargetFlag = 0;
    uint8 u8TargetLen = 0;
    float fFirstEcho = 0;
    float fSecondEcho = 0;
    float fThirdEcho = 0;
    double fObjCoorx[40] = {0};
    double fObjCoory[40] = {0};
    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;
    uint8 u8OffectFlag = 0;
    SlotAlgorPolyfit_strType result;
    
    PSLCarPositionType enuCarPosition = PSL_CAR_POSITION_1;
    
    PSLCurbRecordDataPro(LenuSnsch);

    
    u8RecdObjCnt = GstrPSLDeteData.u8CurbDataCnt[LenuSnsch];
    if(u8RecdObjCnt >= 3)
    {
        for(i = 0;i < u8RecdObjCnt - 2;i++)
        {
            fFirstEcho = GstrPSLDeteData.u16CurbDepthData[LenuSnsch][i];
            fSecondEcho = GstrPSLDeteData.u16CurbDepthData[LenuSnsch][i + 1];
            fThirdEcho = GstrPSLDeteData.u16CurbDepthData[LenuSnsch][i + 2];
            if(((fFirstEcho + PSL_ECHO_10CM_VALUE) >= fSecondEcho)
                && ((fSecondEcho + PSL_ECHO_10CM_VALUE) >= fFirstEcho)
                && ((fSecondEcho + PSL_ECHO_10CM_VALUE) >= fThirdEcho)
                && ((fThirdEcho + PSL_ECHO_10CM_VALUE) >= fSecondEcho)
                && u8TargetFlag == 0)
            {
                u8TargetStartCnt = i;
                u8TargetFlag = 1;
            }
            if(u8TargetFlag == 1)
            {
                if(u8RecdObjCnt - u8TargetStartCnt > 15)
                {
                    u8TargetEndCnt = u8RecdObjCnt - 5;    
                }
                else
                {
                    u8TargetEndCnt = u8RecdObjCnt;
                }
            }
        }
        if((LpStrPSLUseOdo->fCarYawAngle > APA_80_ANGLE && LpStrPSLUseOdo->fCarYawAngle < APA_100_ANGLE)
            ||(LpStrPSLUseOdo->fCarYawAngle > _APA_100_ANGLE && LpStrPSLUseOdo->fCarYawAngle < _APA_80_ANGLE))
        {
            u8OffectFlag = 1;
        }
        j = 0;
        for(i = u8TargetStartCnt;i< u8TargetEndCnt;i++)
        {
            if(u8OffectFlag == 1)
            {
                fObjCoorx[j] = (double)(-GstrPSLDeteData.strCurbCoorData[LenuSnsch][i].fObjY);
                fObjCoory[j] = (double)GstrPSLDeteData.strCurbCoorData[LenuSnsch][i].fObjX;

            }
            else
            {
                fObjCoorx[j] = (double)GstrPSLDeteData.strCurbCoorData[LenuSnsch][i].fObjX;
                fObjCoory[j] = (double)GstrPSLDeteData.strCurbCoorData[LenuSnsch][i].fObjY;
            }
            j++;
            u8TargetLen = j;
        }

        if(u8TargetLen >= 4)
        {
            result = SlotPubFunc_1stOrderPolyfit(&fObjCoorx[0],&fObjCoory[0],u8TargetLen);
            GstrPSLDeteData.fCurbSlope[LenuSnsch] = atan(result.coeff[0]);
            if(u8OffectFlag == 1)
            {
                GstrPSLDeteData.fCurbSlope[LenuSnsch] -= 1.5708;
            }
            enuCarPosition = PSLCalCarPosition(LenuSnsch);
            switch(enuCarPosition)
            {
                case PSL_CAR_POSITION_1:
                    break;

                case PSL_CAR_POSITION_2:
                    GstrPSLDeteData.fCurbSlope[LenuSnsch] = -(GstrPSLDeteData.fCurbSlope[LenuSnsch] - APA_90_ANGLE);
                    break;

                case PSL_CAR_POSITION_3:
                    GstrPSLDeteData.fCurbSlope[LenuSnsch] -= APA_180_ANGLE;
                    break;

                case PSL_CAR_POSITION_4:
                        //GstrPSLDeteData.fCurbSlope[LenuSnsch] = _APA_90_ANGLE - GstrPSLDeteData.fCurbSlope[LenuSnsch];
                    break;
                    
                default:
                    break;
            }
            //GstrPSLDeteData.fCurbSlope[LenuSnsch] -= LpStrPSLUseOdo->fCarYawAngle;
        }
    }
}


/******************************************************************************
  * 函数名称: PSLCalObjSlope
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-11-15 19:00     V0.1      22866        初次发布
  ******************************************************************************/
void PSLCalObjSlope(PSLSnsChannelType LenuSnsch,PSLCalSlopeType LenuSlopeType)
{
  
    if(LenuSlopeType == PSL_CAL_SLOPE_OBJ1)
    {     		
        PSLCalObj1Slope(LenuSnsch);        
    }
    else if(LenuSlopeType == PSL_CAL_SLOPE_OBJ2)
    {
        PSLCalObj2Slope(LenuSnsch);
    }
    else if(LenuSlopeType == PSL_CAL_SLOPE_CURB)
    {
        PSLCalCurbSlope(LenuSnsch);
    }

}

/******************************************************************

* 函数名称: PSLCalSlopeDec
*
* 功能描述:第一个斜率，减去第二个斜率
*
* 输入参数:无
*
* 输出参数: 
*
* 返 回 值: 无
*
* 其它说明: 无
*
* 修改日期              版本号        修改人          修改内容  
* 2023-3-9 14:20     V0.1      22866        初次发布

**********************************************************************/
float PSLCalSlopeDec(float  LfSlope1  , float  LfSlope2 )
{
    float LfReturnSlope;
    LfReturnSlope = LfSlope1 - LfSlope2;
    
    if(LfReturnSlope > APA_180_ANGLE)
    {
       LfReturnSlope -=  APA_360_ANGLE;
    }
    else if(LfReturnSlope <= (-APA_180_ANGLE))
    {
       LfReturnSlope +=  APA_360_ANGLE;    
    }
    else
    {
    }
    return LfReturnSlope;
}

/******************************************************************************
  * 函数名称: PSLAnalysisSlopeData
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-3-9 20:20     V0.1      22866        初次发布
  ******************************************************************************/
void PSLAnalysisSlopeData(PSLSnsChannelType LenuSnsch,PSLCalSlopeType LenuSlopeType)
{
    float fLTempSlope1 = 0.0;
    float fLTempSlope2 = 0.0;
    float fLSumSlope = 0.0;
    uint8 u8ValidSlopCnt = 0;
    uint8 u8ValidSlopCntBackup = 0;
    uint8 u8TempCnt = 0;
    uint8 i;
    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;
    for(i = 0;i < GstrPSLDeteData.u8SlopeCnt[LenuSnsch] - 1;i++)
    {
        fLTempSlope1 = GstrPSLDeteData.fSlopeData[LenuSnsch][i];
        fLTempSlope2 = GstrPSLDeteData.fSlopeData[LenuSnsch][i + 1];
        fLSumSlope = PSLCalSlopeDec(fLTempSlope2,fLTempSlope1);

        if((fLSumSlope < APA_1_5_ANGLE)&&(fLSumSlope > -APA_1_5_ANGLE))
        {
            u8ValidSlopCnt++;

            if(u8ValidSlopCnt > u8ValidSlopCntBackup)
            {
                u8ValidSlopCntBackup = u8ValidSlopCnt;  
                u8TempCnt = i + 1;  
            }
        }
        else
        {
            u8ValidSlopCnt = 0;
        }
    }

    if(u8ValidSlopCntBackup >= 2)
    {
        u8ValidSlopCnt = u8ValidSlopCntBackup / 2;
       
        u8TempCnt -= u8ValidSlopCnt;
        
    

		if (LenuSlopeType == PSL_CAL_SLOPE_OBJ1)
		{
			GstrPSLDeteData.fObj1Slope[LenuSnsch] = GstrPSLDeteData.fSlopeData[LenuSnsch][u8TempCnt] - LpStrPSLUseOdo->fCarYawAngle;
		}
		else if (LenuSlopeType == PSL_CAL_SLOPE_CURB)
		{
			GstrPSLDeteData.fCurbSlope[LenuSnsch] = GstrPSLDeteData.fSlopeData[LenuSnsch][u8TempCnt] - LpStrPSLUseOdo->fCarYawAngle;
		}
		else if (LenuSlopeType == PSL_CAL_SLOPE_OBJ2)
		{
			GstrPSLDeteData.fObj2Slope[LenuSnsch] = GstrPSLDeteData.fSlopeData[LenuSnsch][u8TempCnt] - LpStrPSLUseOdo->fCarYawAngle;
		}
    }
    else
    {
    }

    
}

/******************************************************************************
  * 函数名称: PSLCalCurbDepth
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-3-15 10:20     V0.1      22866        初次发布
  ******************************************************************************/
void PSLCalCurbDepth(PSLSnsChannelType LenuSnsch)
{
    uint8 i,j,k;
    float LfP1_X = 0.0;
    float LfP1_Y = 0.0;
    float LfP2_X = 0.0;
    float LfP2_Y = 0.0;
    float fCarMoveDis = 0.0;
    uint16 u16CurbDepth = 0;
    uint8 u8CurbDepthCnt = 0;
    PSLDetermineDataType *LpStrPSLDetData;
    LpStrPSLDetData = &GstrPSLDeteData;
    if(LpStrPSLDetData->u8CurbDataCnt[LenuSnsch] >= 3)
    {
        for(i = 0;i < LpStrPSLDetData->u8CurbDataCnt[LenuSnsch] - 2;i++)
        {
            for(j = i + 1;j < LpStrPSLDetData->u8CurbDataCnt[LenuSnsch] - 1;j++)
            {
                LfP1_X = LpStrPSLDetData->strCurbOdoCoor[LenuSnsch][i].fObjX;
                LfP1_Y = LpStrPSLDetData->strCurbOdoCoor[LenuSnsch][i].fObjY;
                LfP2_X = LpStrPSLDetData->strCurbOdoCoor[LenuSnsch][j].fObjX;
                LfP2_Y = LpStrPSLDetData->strCurbOdoCoor[LenuSnsch][j].fObjY;
                fCarMoveDis = PubAI_CalTwoPointDis(LfP1_X,LfP1_Y,LfP2_X,LfP2_Y);

                if((fCarMoveDis > PSL_MVOE_CURB_MIN_DIS)
                    &&(LpStrPSLDetData->u16CurbDepthData[LenuSnsch][i] < (LpStrPSLDetData->u16CurbDepthData[LenuSnsch][j] + PSL_ECHO_15CM_VALUE))
                    &&(LpStrPSLDetData->u16CurbDepthData[LenuSnsch][j] < (LpStrPSLDetData->u16CurbDepthData[LenuSnsch][i] + PSL_ECHO_15CM_VALUE)))
                {
                    for(k = j + 1;k < LpStrPSLDetData->u8CurbDataCnt[LenuSnsch];k++)
                    {
                        LfP1_X = LpStrPSLDetData->strCurbOdoCoor[LenuSnsch][j].fObjX;
                        LfP1_Y = LpStrPSLDetData->strCurbOdoCoor[LenuSnsch][j].fObjY;
                        LfP2_X = LpStrPSLDetData->strCurbOdoCoor[LenuSnsch][k].fObjX;
                        LfP2_Y = LpStrPSLDetData->strCurbOdoCoor[LenuSnsch][k].fObjY;
                        fCarMoveDis = PubAI_CalTwoPointDis(LfP1_X,LfP1_Y,LfP2_X,LfP2_Y);

                        if((fCarMoveDis > PSL_MVOE_CURB_MIN_DIS)
                            &&(LpStrPSLDetData->u16CurbDepthData[LenuSnsch][j] < (LpStrPSLDetData->u16CurbDepthData[LenuSnsch][k] + PSL_ECHO_15CM_VALUE))
                            &&(LpStrPSLDetData->u16CurbDepthData[LenuSnsch][k] < (LpStrPSLDetData->u16CurbDepthData[LenuSnsch][j] + PSL_ECHO_15CM_VALUE)))
                        {
                            u16CurbDepth = MIN_VALUE(LpStrPSLDetData->u16CurbDepthData[LenuSnsch][i],LpStrPSLDetData->u16CurbDepthData[LenuSnsch][j]);
                            u16CurbDepth = MIN_VALUE(u16CurbDepth,LpStrPSLDetData->u16CurbDepthData[LenuSnsch][k]);
                            u8CurbDepthCnt++;
                        
                            if(u8CurbDepthCnt > 1)
                            {
                                LpStrPSLDetData->u16CurbDepth[LenuSnsch] = u16CurbDepth;
                            }
                        }
                    }
                }
            }
            u8CurbDepthCnt = 0;
        }
    }
}
/******************************************************************************
  * 函数名称: PSLCalRoundCenter 
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-3-3 10:20     V0.1      22866        初次发布
  ******************************************************************************/
void PSLCalRoundCenter(float x1,float y1,float x2,float y2,float x3,float y3,PSLObjCoorType *strRound, float *fRoundR)
{
    float a = 0.0;
    float b = 0.0;
    float c = 0.0;
    float d = 0.0;
    float e = 0.0;
    float f = 0.0;
    a = x1 - x2;
    b = y1 - y2;
    c = x1 - x3;
    d = y1 - y3;
    e = ((x1 * x1 - x2 * x2) - (y2 * y2 -y1 * y1)) / 2;
    f = ((x1 * x1 - x3 * x3) - (y3 * y3 - y1 * y1)) / 2;
    strRound->fObjX = (e * d - b * f) / (a * d - b * c);
    strRound->fObjY = (a * f - e * c) / (a * d - b * c);
    *fRoundR = sqrt((strRound->fObjX - x1) * (strRound->fObjX - x1) + (strRound->fObjY - y1) * (strRound->fObjY - y1));
}

/******************************************************************************
  * 函数名称: PSLCalLineCircleCornerCoor
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-3-28 09:30    V0.1      22866        初次发布
  ******************************************************************************/
uint8 PSLCalLineCircleCornerCoor(float fCir_x,float fCir_y,float fCir_r,float fLine_k,float fLine_b,uint8 u8NumFlag,PSLObjCoorType *strCoor)
{
    float a = 0.0;
    float b = 0.0;
    float c = 0.0;
    float d = 0.0;
    float e = 0.0;
    uint8 LcLineCirFlag = TRUE;
    d = fLine_b - fCir_y;
    a = 1 + fLine_k * fLine_k;
    b = 2 * fLine_k * d - 2 * fCir_x;
    c = fCir_x * fCir_x + d * d - fCir_r * fCir_r;
    e = b * b - 4 * a * c;
    if(e >= 0)
    {
        if(u8NumFlag < 1)
        {
            strCoor->fObjX = (-b - sqrt(e))/(2 * a);
        }
        else
        {
            strCoor->fObjX = (-b + sqrt(e))/(2 * a);
        }

        strCoor->fObjY = fLine_k * strCoor->fObjX + fLine_b;
    }
    else
    {
        LcLineCirFlag = FALSE;
    }
    

    return LcLineCirFlag;
}
/******************************************************************************
  * 函数名称: PSLCalSnsCoor
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-3-28 16:00     V0.1      22866        初次发布
  ******************************************************************************/
void PSLCalSnsCoor(PSLSnsChannelType LenuSnsch,PSLObjCoorType *fLpSnsCoor)
{
    switch(LenuSnsch)
    {
        case PSL_SNS_CH_FLS:
            fLpSnsCoor->fObjX = GstrRamSnsCoor[SNS_INSTALL_GROUP_FRONT][SNS_CH_FLS].cRadarX;
            fLpSnsCoor->fObjY = GstrRamSnsCoor[SNS_INSTALL_GROUP_FRONT][SNS_CH_FLS].cRadarY;
        break;

        case PSL_SNS_CH_FRS:
            fLpSnsCoor->fObjX = GstrRamSnsCoor[SNS_INSTALL_GROUP_FRONT][SNS_CH_FRS].cRadarX;
            fLpSnsCoor->fObjY = GstrRamSnsCoor[SNS_INSTALL_GROUP_FRONT][SNS_CH_FRS].cRadarY;
        break;

        case PSL_SNS_CH_RLS:
            fLpSnsCoor->fObjX = GstrRamSnsCoor[SNS_INSTALL_GROUP_REAR][SNS_CH_RLS].cRadarX;
            fLpSnsCoor->fObjY = GstrRamSnsCoor[SNS_INSTALL_GROUP_REAR][SNS_CH_RLS].cRadarY;
        break;

        case PSL_SNS_CH_RRS:
            fLpSnsCoor->fObjX = GstrRamSnsCoor[SNS_INSTALL_GROUP_REAR][SNS_CH_RRS].cRadarX;
            fLpSnsCoor->fObjY = GstrRamSnsCoor[SNS_INSTALL_GROUP_REAR][SNS_CH_RRS].cRadarY;
        break;

        default:

        break;
    }
}
 
/******************************************************************************
* 函数名称: PSLCalSnsOdoCoor
* 
* 功能描述: 
* 
* 输入参数:
* 
* 输出参数:无 
* 
* 返回值:无 
* 
* 其它说明:无 
* 
* 修改日期              版本号        修改人          修改内容  
* 2023-7-20 09:20     V0.1      22866        初次发布
******************************************************************************/
void PSLCalSnsOdoCoor(PSLSnsChannelType LenuSnsch,PSLObjCoorType * SnsOdoCoor)
{
    float fCarCos = 0.0;
    float fCarSin = 0.0;
    uint8 i = 0;
    PSLObjCoorType fCarSnsCoor;
    
    PSLCalSnsCoor(LenuSnsch,&fCarSnsCoor);
    for(i = GstrPSLDeteData.u8ObjMinIndex[LenuSnsch];i < GstrPSLDeteData.u8RecordObjCnt[LenuSnsch];i++)
    {
        fCarCos = cosf(GstrPSLDeteData.fRecObj1OdoAngle[LenuSnsch][i]);
        fCarSin = sinf(GstrPSLDeteData.fRecObj1OdoAngle[LenuSnsch][i]);
        PubAI_UpdateCarOdoCoor(GstrPSLDeteData.strRecObj1Odo[LenuSnsch][i].fObjX,GstrPSLDeteData.strRecObj1Odo[LenuSnsch][i].fObjY,fCarSin,fCarCos);                
        PubAI_TransObjCarCoorToOdoCoor(&fCarSnsCoor.fObjX,&fCarSnsCoor.fObjY,&SnsOdoCoor[i].fObjX,&SnsOdoCoor[i].fObjY);                    
    }
}

/******************************************************************************
        * 函数名称: PSLCalProcessObj1Data
        * 
        * 功能描述: 
        * 
        * 输入参数:
        * 
        * 输出参数:无 
        * 
        * 返回值:无 
        * 
        * 其它说明:无 
        * 
        * 修改日期              版本号        修改人          修改内容  
        * 2023-9-18 11:00    V0.1      22866        初次发布
        ******************************************************************************/
void PSLCalProcessObj1Data(PSLSnsChannelType LenuSnsch)
{
    uint8 i = 0;

    for(i = GstrPSLDeteData.u8ObjMinIndex[LenuSnsch] + 1;i < GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 1;i++)
    {
        if(GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i] > GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i + 1])
        {
            if(GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i] > GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i - 1])
            {
               
                GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i] = (GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i - 1] + GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i + 1]) / 2;
            }
            
        }
    }    
    i = GstrPSLDeteData.u8ObjMinIndex[LenuSnsch];
    if(GstrPSLDeteData.u8ObjMinIndex[LenuSnsch] > 0 && i < GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 1)
    {
        if((i - 1 > 0) && (i + 1 < PSL_RECORD_OBJ_MAX))
        {
        #if 0
            if((GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i - 1] - GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i] > PSL_ECHO_6CM_VALUE)
                && (GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i + 1] - GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i] > PSL_ECHO_6CM_VALUE))
            {
                GstrPSLDeteData.fFirstObjMinDis[LenuSnsch] = (GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i - 1] + GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i] + GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i + 1]) / 3;
                printf("First Obj EchoMin:%.1f\r\n",GstrPSLDeteData.fFirstObjMinDis[LenuSnsch]);
            }
        #else
            if((GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i - 1] - GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i]) < PSL_ECHO_20CM_VALUE)
            {
                if((GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i - 1] - GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i] > 20)
                    && (GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i + 1] - GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i] > 20))
                {
                    GstrPSLDeteData.fFirstObjMinDis[LenuSnsch] = (GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i - 1] + GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i] + GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i + 1]) / 3;
                }
            }

        #endif
        }
    }
    else
    {
    }
}    

/******************************************************************************
      * 函数名称: PSLCalSlotCornerStart
      * 
      * 功能描述: 
      * 
      * 输入参数:
      * 
      * 输出参数:无 
      * 
      * 返回值:无 
      * 
      * 其它说明:无 
      * 
      * 修改日期              版本号        修改人          修改内容  
      * 2023-6-19 09:20     V0.1      22866        初次发布
      ******************************************************************************/
void PSLCalSlotCornerStart(PSLSnsChannelType LenuSnsch)
{
    uint8 i = 0;
    float fOnePointx = 0.0;
    float fOnePointy = 0.0;
    float fTwoPointx = 0.0;
    float fTwoPointy = 0.0;
    float fTwoPointDis = 0.0;
    uint8 u8TargetCnt = 0;
    uint8 u8CalibrationCnt = 0;
    float fCalibrationEcho = 0.0;
    PSLObjCoorType strSnsOdoCoor[PSL_RECORD_OBJ_MAX];
    float fEchoSum = 0.0;
    float fObj1EndDis = 0.0;
    float fObj1EndDisCos = 0.0;
    float fObj1EndDisSin = 0.0;
    float fObjEchoDisCos = 0.0;
    float fObjEchoDisSin = 0.0;
    float fObj1SnsCoorx = 0.0;
    float fObj1SnsCoory = 0.0;
    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;

    
    if(GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] < 2)
    {
        GstrPSLDeteData.enuPSLDetSts[LenuSnsch] = PSL_DETE_INIT;       
        return;
    }
    PSLCalSnsOdoCoor(LenuSnsch,&strSnsOdoCoor[0]);
    PSLCalProcessObj1Data(LenuSnsch);
    fCalibrationEcho = GstrPSLDeteData.fFirstObjMinDis[LenuSnsch] + 200;

    if(GstrPSLDeteData.fFirstObjMinDis[LenuSnsch] < 500)
    {
        u8CalibrationCnt = 0;
    }
    else
    {
        u8CalibrationCnt = (GstrPSLDeteData.fFirstObjMinDis[LenuSnsch] - 500) / 50;
        if(u8CalibrationCnt > PSL_RECORD_OBJ_MAX - 1)
        {
            u8CalibrationCnt = PSL_RECORD_OBJ_MAX - 1;
        }
    }
    
    for(i = GstrPSLDeteData.u8ObjMinIndex[LenuSnsch];i < GstrPSLDeteData.u8RecordObjCnt[LenuSnsch];i++)
    {
        if(GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i] > fCalibrationEcho)
        {
            u8TargetCnt = i;
            break;
        }
    }

    if(i == GstrPSLDeteData.u8RecordObjCnt[LenuSnsch])
    {
        u8TargetCnt = GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] - 1;
    }
  
    if(u8TargetCnt > 1)
    {
        if((GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][u8TargetCnt] + GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][u8TargetCnt - 1]) > (fCalibrationEcho * 2))
        {
            u8TargetCnt --;
        }
    }

    if(GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][u8TargetCnt] > fCalibrationEcho)
    {
        fEchoSum = GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][u8TargetCnt] - fCalibrationEcho;
        fObj1EndDis = fEchoSum + u16ObjOneCalibration[u8CalibrationCnt];     
    }
    else
    {
        if(GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][u8TargetCnt] > (GstrPSLDeteData.fFirstObjMinDis[LenuSnsch] + 130))
        {
            fEchoSum = fCalibrationEcho - GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][u8TargetCnt];       

            if(u16ObjOneCalibration[u8CalibrationCnt] > 2 * fEchoSum)
            {
                fObj1EndDis = u16ObjOneCalibration[u8CalibrationCnt] - 2 * fEchoSum;
            }
            else
            {          
                if(u16ObjOneCalibration[u8CalibrationCnt] > fEchoSum)
                {
                    fObj1EndDis = u16ObjOneCalibration[u8CalibrationCnt] - fEchoSum;
                }
                else
                {
                    fObj1EndDis = u16ObjOneCalibration[u8CalibrationCnt];
                }
            } 
            
        }
        else if(GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][u8TargetCnt] > (GstrPSLDeteData.fFirstObjMinDis[LenuSnsch] + 65))
        {
            fEchoSum = fCalibrationEcho - GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][u8TargetCnt];
 
            if(u16ObjOneCalibration[u8CalibrationCnt] > 2 * fEchoSum)
            {
                fObj1EndDis = u16ObjOneCalibration[u8CalibrationCnt] - 2 * fEchoSum;
            }
            else
            {
                if(u16ObjOneCalibration[u8CalibrationCnt] > fEchoSum)
                {
                    fObj1EndDis = u16ObjOneCalibration[u8CalibrationCnt] - fEchoSum;
                }
                else
                {
                    fObj1EndDis = u16ObjOneCalibration[u8CalibrationCnt];
                }
            }
        }
        else
        {
            fEchoSum = fCalibrationEcho - GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][u8TargetCnt];
      
            if(u16ObjOneCalibration[u8CalibrationCnt] > fEchoSum)
            {
                fObj1EndDis = u16ObjOneCalibration[u8CalibrationCnt] - fEchoSum;
            }
            else
            {
                fEchoSum = fEchoSum / 2;
                if(u16ObjOneCalibration[u8CalibrationCnt] > fEchoSum)
                {
                    fObj1EndDis = u16ObjOneCalibration[u8CalibrationCnt] - fEchoSum;
                }
                else
                {
                    fObj1EndDis = u16ObjOneCalibration[u8CalibrationCnt];
                }
            }
 
        }
    }

    fObj1EndDisCos = fObj1EndDis * cosf(GstrPSLDeteData.fRecObj1OdoAngle[LenuSnsch][u8TargetCnt]);
    fObj1EndDisSin = fObj1EndDis * sinf(GstrPSLDeteData.fRecObj1OdoAngle[LenuSnsch][u8TargetCnt]);

    GstrPSLDeteData.fFirstObjCornerX[LenuSnsch] = GstrPSLDeteData.strObj1Data[LenuSnsch][u8TargetCnt].fObjX - fObj1EndDisCos;
    GstrPSLDeteData.fFirstObjCornerY[LenuSnsch] = GstrPSLDeteData.strObj1Data[LenuSnsch][u8TargetCnt].fObjY - fObj1EndDisSin;

    fOnePointx = GstrPSLDeteData.strObj1Data[LenuSnsch][GstrPSLDeteData.u8ObjMinIndex[LenuSnsch]].fObjX;
    fOnePointy = GstrPSLDeteData.strObj1Data[LenuSnsch][GstrPSLDeteData.u8ObjMinIndex[LenuSnsch]].fObjY;
    fTwoPointx = GstrPSLDeteData.fFirstObjCornerX[LenuSnsch];
    fTwoPointy = GstrPSLDeteData.fFirstObjCornerY[LenuSnsch];
	fTwoPointDis = PubAI_CalTwoPointDis(fOnePointx, fOnePointy, fTwoPointx, fTwoPointy);
  
    GstrPSLDeteData.fRecObj1CornerOdox[LenuSnsch] += fTwoPointDis * cosf(GstrPSLDeteData.fRecObj1OdoAngle[LenuSnsch][u8TargetCnt]); 
    GstrPSLDeteData.fRecObj1CornerOdoy[LenuSnsch] += fTwoPointDis * sinf(GstrPSLDeteData.fRecObj1OdoAngle[LenuSnsch][u8TargetCnt]);

    PSL_DEBUG_PRINTF("Obj1CornerX:%d,Obj1CornerY:%d,MinDis:%d\r\n", (uint32)GstrPSLDeteData.fFirstObjCornerX[LenuSnsch], (uint32)GstrPSLDeteData.fFirstObjCornerY[LenuSnsch],(uint32)GstrPSLDeteData.fFirstObjMinDis[LenuSnsch]);
    PSL_DEBUG_PRINTF("Odox:%d Odoy:%d MinDis:%d \r\n",(uint32)LpStrPSLUseOdo->fCarCoorX,(uint32)LpStrPSLUseOdo->fCarCoorY,(uint32)GstrPSLDeteData.fFirstObjMinDis[LenuSnsch]);
    PSL_DEBUG_PRINTF("CornerOdox:%d CornerOdoy:d ch:%d\r\n",(uint32)GstrPSLDeteData.fRecObj1CornerOdox[LenuSnsch],(uint32)GstrPSLDeteData.fRecObj1CornerOdoy[LenuSnsch],LenuSnsch);
    
}
/******************************************************************************
      * 函数名称: PSLClearObj1RecordData
      * 
      * 功能描述: 
      * 
      * 输入参数:
      * 
      * 输出参数:无 
      * 
      * 返回值:无 
      * 
      * 其它说明:无 
      * 
      * 修改日期              版本号        修改人          修改内容  
      * 2023-3-2 14:20     V0.1      22866        初次发布
      ******************************************************************************/
void PSLClearObj1RecordData(PSLSnsChannelType LenuSnsch)
{
    uint8 i;
    for(i = 0;i < PSL_RECORD_OBJ_MAX;i++)
    {
        GstrPSLDeteData.strObj1Data[LenuSnsch][i].fObjX = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.strObj1Data[LenuSnsch][i].fObjY = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fSlopeData[LenuSnsch][i] = PSL_DET_INVALID_DATA;
		GstrPSLDeteData.fRecObj1EchoDis[LenuSnsch][i] = PSL_ECHO_INVALID_DATA;
		GstrPSLDeteData.strRecObj1Odo[LenuSnsch][i].fObjX = PSL_DET_INVALID_COOR;
		GstrPSLDeteData.strRecObj1Odo[LenuSnsch][i].fObjY = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fRecObj1OdoAngle[LenuSnsch][i] = APA_360_ANGLE;
	}
    GstrPSLDeteData.fFirstObjTrailX[LenuSnsch] = PSL_DET_INVALID_COOR;
    GstrPSLDeteData.fFirstObjTrailY[LenuSnsch] = PSL_DET_INVALID_COOR;
    GstrPSLDeteData.u8RecordObjCnt[LenuSnsch] = PSL_DET_INVALID_CNT;
    GstrPSLDeteData.u8ObjMinIndex[LenuSnsch] = PSL_DET_INVALID_CNT;
    GstrPSLDeteData.u8SlopeCnt[LenuSnsch] = PSL_DET_INVALID_CNT;
    GstrPSLDeteData.fFirstObjMinDis[LenuSnsch] = PSL_ECHO_INVALID_DATA;
    GstrPSLDeteData.u8FirObjInvalidcnt[LenuSnsch] = PSL_DET_INVALID_CNT;
}

/******************************************************************************
      * 函数名称: PSLClearObj2RecordData
      * 
      * 功能描述: 
      * 
      * 输入参数:
      * 
      * 输出参数:无 
      * 
      * 返回值:无 
      * 
      * 其它说明:无 
      * 
      * 修改日期              版本号        修改人          修改内容  
      * 2023-3-18 08:20     V0.1      22866        初次发布
      ******************************************************************************/
void PSLClearObj2RecordData(PSLSnsChannelType LenuSnsch)
{
    uint8 i;

    GstrPSLDeteData.LenuSlotObjType[LenuSnsch] = PSL_SLOT_TWOOBJ;
    GstrPSLDeteData.LenuSlotType[LenuSnsch] = PSL_SLOT_NONE;

    GstrPSLDeteData.fSecObjMinDisX[LenuSnsch] = PSL_DET_INVALID_COOR;
    GstrPSLDeteData.fSecObjMinDisY[LenuSnsch] = PSL_DET_INVALID_COOR;
    GstrPSLDeteData.fSecObjMinDis[LenuSnsch] = PSL_ECHO_INVALID_DATA;
    GstrPSLDeteData.fFirstObjCornerX[LenuSnsch] = PSL_DET_INVALID_COOR;
    GstrPSLDeteData.fFirstObjCornerY[LenuSnsch] = PSL_DET_INVALID_COOR;
    GstrPSLDeteData.LenuSecObjShape[LenuSnsch] = OBJ_SHAPE_NONE;
    GstrPSLDeteData.u8PSLSlotDetedObj[LenuSnsch] = SLOT_HAVE_NONE;
    for(i = 0;i < PSL_RECORD_OBJ_MAX;i++)
    {
        GstrPSLDeteData.fSlopeData[LenuSnsch][i] = PSL_DET_INVALID_DATA;
        GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][i] = PSL_ECHO_INVALID_DATA;
        GstrPSLDeteData.strObj2Data[LenuSnsch][i].fObjX = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.strObj2Data[LenuSnsch][i].fObjY = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][i].fObjX = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][i].fObjY = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.fObj2RawData[LenuSnsch][i] = PSL_ECHO_INVALID_DATA;
    }
    GstrPSLDeteData.u8RecObj2RawCnt[LenuSnsch] = PSL_DET_INVALID_CNT;
    GstrPSLDeteData.fObj2Slope[LenuSnsch] = PSL_DET_INVALID_DATA;
    GstrPSLDeteData.u8ObjMinIndex[LenuSnsch] = PSL_DET_INVALID_CNT;
    GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch] = PSL_DET_INVALID_CNT;
    GstrPSLDeteData.u8Obj2MinIndex[LenuSnsch] = PSL_DET_INVALID_CNT;
    GstrPSLDeteData.fObj1BackUpCarCoorx[LenuSnsch] = PSL_DET_INVALID_COOR;
    GstrPSLDeteData.fObj1BackUpCarCoory[LenuSnsch] = PSL_DET_INVALID_COOR;

    GstrPSLDeteData.fBackUpCarCoorx[LenuSnsch] = PSL_DET_INVALID_COOR;
    GstrPSLDeteData.fBackUpCarCoory[LenuSnsch] = PSL_DET_INVALID_COOR;

    GstrPSLDeteData.u8CurbDataCnt[LenuSnsch] = PSL_DET_INVALID_CNT;
    GstrPSLDeteData.u16CurbDepth[LenuSnsch] = PSL_CURB_DEPTH_INVALID;
    GstrPSLDeteData.fCurbSlope[LenuSnsch] = PSL_DET_INVALID_DATA;
    GstrPSLDeteData.fCurbDataInitFlag[LenuSnsch] = FALSE;
    GstrPSLDeteData.u16SlotWidth[LenuSnsch] = PSL_INVALID_VALUE;
    GstrPSLDeteData.fRecordCarSlope[LenuSnsch][PSL_CAL_SLOPE_OBJ2] = PSL_DET_INVALID_DATA;
    for(i = 0;i < PSL_RECORD_CURB_MAX;i++)
    {
        GstrPSLDeteData.u16CurbDepthData[LenuSnsch][i] = PSL_INVALID_VALUE;
        GstrPSLDeteData.strCurbCoorData[LenuSnsch][i].fObjX = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.strCurbCoorData[LenuSnsch][i].fObjY = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.strCurbOdoCoor[LenuSnsch][i].fObjX = PSL_DET_INVALID_COOR;
        GstrPSLDeteData.strCurbOdoCoor[LenuSnsch][i].fObjY = PSL_DET_INVALID_COOR;
    }
}

/******************************************************************************
  * 函数名称: PSLTrailObj1Coor
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-6-15 16:20     V0.1      22866        初次发布
  ******************************************************************************/
void PSLTrailObj1Coor(PSLSnsChannelType LenuSnsch)
{
    float fMasterCoorx = 0.0;
    float fMasterCoory = 0.0;
    float fMasCoorDis = 0.0;
    float fListenCoorx = 0.0;
    float fListenCoory = 0.0;
    float fLisCoorDis = 0.0;
    
    PSLRecordObjCoorType *LpPSLRecordObj;
      LpPSLRecordObj = &GstrPSLRecordObjCoor;
      
    if((LenuSnsch == PSL_SNS_CH_FLS) || (LenuSnsch == PSL_SNS_CH_RLS))
    {
        fListenCoorx = LpPSLRecordObj->strSnsRightObj[LenuSnsch][PSL_DETE_FIFTH].fObjX;
        fListenCoory = LpPSLRecordObj->strSnsRightObj[LenuSnsch][PSL_DETE_FIFTH].fObjY;
    }
    else
    {
        fListenCoorx = LpPSLRecordObj->strSnsLeftObj[LenuSnsch][PSL_DETE_FIFTH].fObjX;
        fListenCoory = LpPSLRecordObj->strSnsLeftObj[LenuSnsch][PSL_DETE_FIFTH].fObjY;
    }
 
    fMasterCoorx = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_FIFTH].fObjX;
    fMasterCoory = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_FIFTH].fObjY; 
    if(fMasterCoorx != 0 && fListenCoorx != 0)
    {
        fMasCoorDis = PubAI_CalTwoPointDis(GstrPSLDeteData.fFirstObjTrailX[LenuSnsch],GstrPSLDeteData.fFirstObjTrailY[LenuSnsch],fMasterCoorx,fMasterCoory);
        fLisCoorDis = PubAI_CalTwoPointDis(GstrPSLDeteData.fFirstObjTrailX[LenuSnsch],GstrPSLDeteData.fFirstObjTrailY[LenuSnsch],fListenCoorx,fListenCoory);
        if(fMasCoorDis <= PSL_RECORD_TRAIL_DIS && fLisCoorDis <= PSL_RECORD_TRAIL_DIS)
        {
            GstrPSLDeteData.fFirstObjTrailX[LenuSnsch] += fMasterCoorx;
            GstrPSLDeteData.fFirstObjTrailY[LenuSnsch] += fMasterCoory;
            GstrPSLDeteData.fFirstObjTrailX[LenuSnsch] += fListenCoorx;
            GstrPSLDeteData.fFirstObjTrailY[LenuSnsch] += fListenCoory;
            GstrPSLDeteData.fFirstObjTrailX[LenuSnsch] /= 3;
            GstrPSLDeteData.fFirstObjTrailY[LenuSnsch] /= 3;
        }
        else if(fMasCoorDis <= PSL_RECORD_TRAIL_DIS)
        {
            GstrPSLDeteData.fFirstObjTrailX[LenuSnsch] += fMasterCoorx;
            GstrPSLDeteData.fFirstObjTrailY[LenuSnsch] += fMasterCoory;           
            GstrPSLDeteData.fFirstObjTrailX[LenuSnsch] /= 2;
            GstrPSLDeteData.fFirstObjTrailY[LenuSnsch] /= 2;
        }
        else if(fLisCoorDis <= PSL_RECORD_TRAIL_DIS)
        {
            GstrPSLDeteData.fFirstObjTrailX[LenuSnsch] += fListenCoorx;
            GstrPSLDeteData.fFirstObjTrailY[LenuSnsch] += fListenCoory;           
            GstrPSLDeteData.fFirstObjTrailX[LenuSnsch] /= 2;
            GstrPSLDeteData.fFirstObjTrailY[LenuSnsch] /= 2;
        }
        else
        {

        }
    }
    else if(fMasterCoorx != 0)
    {
        fMasCoorDis = PubAI_CalTwoPointDis(GstrPSLDeteData.fFirstObjTrailX[LenuSnsch],GstrPSLDeteData.fFirstObjTrailY[LenuSnsch],fMasterCoorx,fMasterCoory);
        if(fMasCoorDis <= PSL_RECORD_TRAIL_DIS)
        {
            GstrPSLDeteData.fFirstObjTrailX[LenuSnsch] += fMasterCoorx;
            GstrPSLDeteData.fFirstObjTrailY[LenuSnsch] += fMasterCoory;           
            GstrPSLDeteData.fFirstObjTrailX[LenuSnsch] /= 2;
            GstrPSLDeteData.fFirstObjTrailY[LenuSnsch] /= 2;
        }
    }
    else
    {
        
    }
}

/******************************************************************************
  * 函数名称: PSLDeteObj1Status
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-3-2 14:20     V0.1      22866        初次发布
  ******************************************************************************/
void PSLDeteObj1Status(PSLSnsChannelType LenuSnsch)
{
      PSLRecordObjCoorType *LpPSLRecordObj;
      LpPSLRecordObj = &GstrPSLRecordObjCoor;
      float fListenCoorx = 0.0;
      float fListenCoory = 0.0;
      float fListernDis = 0.0;
      uint16 Lu16CarSpd = 0;
      uint8 u8Obj1OverFlag = 0;
//    PSLUseOdoType *LpStrPSLUseOdo;
//    LpStrPSLUseOdo = &GstrPSLUseOdoData;
//    float fCarOdoToObjDis = 0.0;
//    LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_FIFTH]
#if 0
    if(((LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] >= GstrPSL_APPPara_Ram.u16DetObjDisMin)
        && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] <= (GstrPSLDeteData.fFirstObjMinDis[LenuSnsch] + 600)))
        || (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] <= GstrPSL_APPPara_Ram.u16SlotDepthMin_H + 500))
    {
        GstrPSLDeteData.u8FirObjInvalidcnt[LenuSnsch] = PSL_DET_INVALID_CNT;
    }
    else
    {
        GstrPSLDeteData.u8FirObjInvalidcnt[LenuSnsch]++;
    }
#else
    PSLTrailObj1Coor(LenuSnsch);
    if((LenuSnsch == PSL_SNS_CH_FLS) || (LenuSnsch == PSL_SNS_CH_RLS))
    {
        fListenCoorx = LpPSLRecordObj->strSnsRightObj[LenuSnsch][PSL_DETE_FIFTH].fObjX;
        fListenCoory = LpPSLRecordObj->strSnsRightObj[LenuSnsch][PSL_DETE_FIFTH].fObjY;
    }
    else
    {
        fListenCoorx = LpPSLRecordObj->strSnsLeftObj[LenuSnsch][PSL_DETE_FIFTH].fObjX;
        fListenCoory = LpPSLRecordObj->strSnsLeftObj[LenuSnsch][PSL_DETE_FIFTH].fObjY;
    }
    if(fListenCoorx != 0.0)
        fListernDis = PubAI_CalTwoPointDis(GstrPSLDeteData.fFirstObjTrailX[LenuSnsch],GstrPSLDeteData.fFirstObjTrailY[LenuSnsch],fListenCoorx,fListenCoory);
    
    if(GstrPSLDeteData.u8FirObjInvalidcnt[LenuSnsch] != PSL_DET_INVALID_CNT)
    {
        PSL_DEBUG_PRINTF("FirstObjTrailX Y:%.1f,%.1f\r\n",GstrPSLDeteData.fFirstObjTrailX[LenuSnsch],GstrPSLDeteData.fFirstObjTrailY[LenuSnsch]);
        PSL_DEBUG_PRINTF("FisMinDis:%.1f\r\n",GstrPSLDeteData.fFirstObjMinDis[LenuSnsch]);
        PSL_DEBUG_PRINTF("fListenCoorX :%.1f,fListernTrailDis:%.1f\r\n",fListenCoorx,fListernDis);
        PSL_DEBUG_PRINTF("MasterDis:%d\r\n",LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH]);
    }
    
    if(((LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] >= (GstrPSLDeteData.fFirstObjMinDis[LenuSnsch] + 500))
        && (fListenCoorx == 0.0 || fListernDis > PSL_lIS_TRAIL_MAX_DIS)
        && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] >= 1500))
        ||(LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] >= (GstrPSLDeteData.fFirstObjMinDis[LenuSnsch] + 600)))
    {
        GstrPSLDeteData.u8FirObjInvalidcnt[LenuSnsch]++;
        PSL_DEBUG_PRINTF("FirObjInvalidcnt[%d]++:%d\r\n",LenuSnsch,GstrPSLDeteData.u8FirObjInvalidcnt[LenuSnsch]);
    }
    else
    {
        GstrPSLDeteData.u8FirObjInvalidcnt[LenuSnsch] = PSL_DET_INVALID_CNT;
    }
#endif
    ReadCAN_AppSignal_Car_Speed(&Lu16CarSpd);
    if(Lu16CarSpd < PSL_DET_CAR_SPEED && GstrPSLDeteData.u8FirObjInvalidcnt[LenuSnsch] >= 4)
    {
        u8Obj1OverFlag = 1;
    }
    else if(Lu16CarSpd >= PSL_DET_CAR_SPEED && GstrPSLDeteData.u8FirObjInvalidcnt[LenuSnsch] >= 3)
    {
        u8Obj1OverFlag = 2;
    }
    else
    {
        u8Obj1OverFlag = 0;
    }
    
    if(u8Obj1OverFlag > 0)
    {
        GstrPSLDeteData.enuPSLDetSts[LenuSnsch] = PSL_DETE_CURB;
        u8PSLHaveObjOne[LenuSnsch] = 1;
        GstrPSLDeteData.fCurbDataInitFlag[LenuSnsch] = TRUE;
        PSLAnalysisTableData(LenuSnsch);
        if(GstrPSLDeteData.fFirstObjMinDis[LenuSnsch] < GstrPSL_APPPara_Ram.u16DetObjDisMin)
        {
            GstrPSLDeteData.enuPSLDetSts[LenuSnsch] = PSL_DETE_INIT;
        }
        //PSLCalObjAndCurbSlope(LenuSnsch,PSL_CAL_SLOPE_OBJ1);
        //PSLAnalysisSlopeData(LenuSnsch,PSL_CAL_SLOPE_OBJ1); 
        PSLCalObjSlope(LenuSnsch,PSL_CAL_SLOPE_OBJ1);
        PSLCalCurbDepth(LenuSnsch);
        PSLCalSlotCornerStart(LenuSnsch);
        PSLClearObj1RecordData(LenuSnsch);
		PSLDeteCurbDataInit(LenuSnsch);
        PSL_DEBUG_PRINTF("ch:%d,PSL_DETE_CURB\r\n",LenuSnsch);
    }
    
}

/******************************************************************************
  * 函数名称: PSLDeteSlotWorkStsObj1
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-2-25 16:28    V0.1      22866        初次发布
  ******************************************************************************/
void PSLDeteSlotWorkStsObj1(PSLSnsChannelType LenuSnsch)
{
   PSLRecordObjCoorType *LpPSLRecordObj;
   LpPSLRecordObj = &GstrPSLRecordObjCoor;
   static uint8 u8backupSTDCnt = 0;
   
   if(LpPSLRecordObj->enuMeasMode[LenuSnsch][PSL_DETE_FIFTH] == PSL_SNS_MEAS_STD)
   {
        if(LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] < PSL_SNS_MEAS_CHIRP_MIN)
        {
            u8backupSTDCnt++;
            if(u8backupSTDCnt >= 0xAA)
            {
               u8backupSTDCnt = 0xAA;
            }
        }
        else
        {
            u8backupSTDCnt = 0;
        }
   }
   else
   {
        if(u8backupSTDCnt >= 2)
        {
            return;
        }
   }
   PSLRecordOdoAndObj1Data(LenuSnsch);
   PSLDeteObj1Status(LenuSnsch);
}

/******************************************************************************
  * 函数名称: PSLDetObj1ToCurbHandle
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-3-10 15:30    V0.1      22866        初次发布
  ******************************************************************************/
void PSLDetObj1ToCurbHandle(PSLSnsChannelType LenuSnsch)
{
    float fCurEchoDis = 0.0;
    float fOneEchoDis = 0.0;
    float fTwoEchoDis = 0.0;
    float fListenCoorX[3];
    uint8 u8ListenCnt = 0;
    uint8 i;
    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;
    
    PSLRecordObjCoorType *LpPSLRecordObj;
    LpPSLRecordObj = &GstrPSLRecordObjCoor;
      
    if((LenuSnsch == PSL_SNS_CH_FLS)/* || (LenuSnsch == PSL_SNS_CH_RLS)*/)
    {
        fListenCoorX[0] = LpPSLRecordObj->strSnsRightObj[LenuSnsch][PSL_DETE_FIFTH].fObjX; 
        fListenCoorX[1] = LpPSLRecordObj->strSnsRightObj[LenuSnsch][PSL_DETE_FOURTH].fObjX;    
        fListenCoorX[2] = LpPSLRecordObj->strSnsRightObj[LenuSnsch][PSL_DETE_THIRD].fObjX;
      
    }
    else
    {
        fListenCoorX[0] = LpPSLRecordObj->strSnsLeftObj[LenuSnsch][PSL_DETE_FIFTH].fObjX; 
        fListenCoorX[1] = LpPSLRecordObj->strSnsLeftObj[LenuSnsch][PSL_DETE_FOURTH].fObjX;   
        fListenCoorX[2] = LpPSLRecordObj->strSnsLeftObj[LenuSnsch][PSL_DETE_THIRD].fObjX;  
    }
    for(i = 0;i < 3;i++)
    {
        if(fListenCoorX[i] != 0)
            u8ListenCnt++;
    }
    
    fCurEchoDis = LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH];
    fOneEchoDis = LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH];
    fTwoEchoDis = LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD];
    if(PSLCalCurEchoAverage(LenuSnsch) == TRUE)
    {
        if(GstrPSLDeteData.fCurEchoAverage[LenuSnsch] < GstrPSL_APPPara_Ram.u16DetObjDisMax)
        {
            if((((fCurEchoDis + PSL_ECHO_12CM_VALUE) > fOneEchoDis) && ((fOneEchoDis + PSL_ECHO_12CM_VALUE) > fCurEchoDis))
            && (((fCurEchoDis + PSL_ECHO_12CM_VALUE) > fTwoEchoDis) && ((fTwoEchoDis + PSL_ECHO_12CM_VALUE) > fCurEchoDis))
            && (((fOneEchoDis + PSL_ECHO_12CM_VALUE) > fTwoEchoDis) && ((fTwoEchoDis + PSL_ECHO_12CM_VALUE) > fOneEchoDis))
            && (u8ListenCnt >= 2))
            {
                
				u8PSLHaveObjOne[LenuSnsch] = 0;
                GstrPSLDeteData.enuPSLDetSts[LenuSnsch] = PSL_DETE_OBJ1;
                GstrPSLDeteData.fFirstObjTrailX[LenuSnsch] = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_FIFTH].fObjX;
                GstrPSLDeteData.fFirstObjTrailY[LenuSnsch] = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_FIFTH].fObjY;
                GstrPSLDeteData.fRecordCarSlope[LenuSnsch][PSL_CAL_SLOPE_OBJ1] = LpStrPSLUseOdo->fCarYawAngle;
            }
        }
        else
        {
            if((((fCurEchoDis + PSL_ECHO_12CM_VALUE) > fOneEchoDis) && ((fOneEchoDis + PSL_ECHO_12CM_VALUE) > fCurEchoDis))
            && (((fCurEchoDis + PSL_ECHO_12CM_VALUE) > fTwoEchoDis) && ((fTwoEchoDis + PSL_ECHO_12CM_VALUE) > fCurEchoDis))
            && (((fOneEchoDis + PSL_ECHO_12CM_VALUE) > fTwoEchoDis) && ((fTwoEchoDis + PSL_ECHO_12CM_VALUE) > fOneEchoDis))
            && (u8ListenCnt >= 2))
            {
                GstrPSLDeteData.u8PSLSlotDetedObj[LenuSnsch] = SLOT_HAVE_OBJ;               
            }
        }
    }
}
/******************************************************************************
  * 函数名称: PSLDeteCurbHandle
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2024-1-27 08:40    V0.1      22866        初次发布
  ******************************************************************************/
bool PSLSnsListencheck(PSLSnsChannelType LenuSnsch)
{
    uint8 i;
    bool bListenFlag = FALSE;
    float fListenCoorX[3];
    uint8 u8ListenCnt = 0;
    PSLRecordObjCoorType *LpPSLRecordObj;
    LpPSLRecordObj = &GstrPSLRecordObjCoor;
    
    if(LenuSnsch == PSL_SNS_CH_FLS)
    {
        fListenCoorX[0] = LpPSLRecordObj->strSnsRightObj[LenuSnsch][PSL_DETE_FIFTH].fObjX; 
        fListenCoorX[1] = LpPSLRecordObj->strSnsRightObj[LenuSnsch][PSL_DETE_FOURTH].fObjX;    
        fListenCoorX[2] = LpPSLRecordObj->strSnsRightObj[LenuSnsch][PSL_DETE_THIRD].fObjX;
      
    }
    else
    {
        fListenCoorX[0] = LpPSLRecordObj->strSnsLeftObj[LenuSnsch][PSL_DETE_FIFTH].fObjX; 
        fListenCoorX[1] = LpPSLRecordObj->strSnsLeftObj[LenuSnsch][PSL_DETE_FOURTH].fObjX;   
        fListenCoorX[2] = LpPSLRecordObj->strSnsLeftObj[LenuSnsch][PSL_DETE_THIRD].fObjX;  
    }
    for(i = 0;i < 3;i++)
    {
        if(fListenCoorX[i] != 0)
        {
            u8ListenCnt++;
            bListenFlag = TRUE;
        }
    }
    return bListenFlag;
}

/******************************************************************************
  * 函数名称: PSLDeteCurbHandle
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-3-10 16:40    V0.1      22866        初次发布
  ******************************************************************************/
void PSLDeteCurbHandle(PSLSnsChannelType LenuSnsch)
{
    
    uint8 i,u8ObjSquareFlag = 0;
    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;
    
    PSLRecordObjCoorType *LpPSLRecordObj;
    LpPSLRecordObj = &GstrPSLRecordObjCoor;
      
    
    if(GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] < (GstrPSL_APPPara_Ram.u16DetObjDisMax + PSL_ECHO_25CM_VALUE))
    {
        PSL_DEBUG_PRINTF("%d,%d,%d\r\n",GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FIFTH],GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FOURTH],GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_THIRD]); 
        if(((GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] + PSL_ECHO_10CM_VALUE) > GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FOURTH])
            &&((GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] + PSL_ECHO_10CM_VALUE) > GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FIFTH])
            && ((GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] + PSL_ECHO_10CM_VALUE) > GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_THIRD])
            && ((GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_THIRD] + PSL_ECHO_10CM_VALUE) > GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FOURTH])
            && ((GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] + PSL_ECHO_10CM_VALUE) > GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_THIRD])
            && ((GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_THIRD] + PSL_ECHO_10CM_VALUE) > GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FIFTH])
            && (PSLSnsListencheck(LenuSnsch) == TRUE))
        {
            u8ObjSquareFlag = 1;
            GstrPSLDeteData.LenuSecObjShape[LenuSnsch] = OBJ_SHAPE_SQUARE;  
        }
            
		if((((GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] + PSL_ECHO_25CM_VALUE) > GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FOURTH]) 
            && ((GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] + PSL_ECHO_25CM_VALUE) > GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_THIRD])
            && ((GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] + PSL_ECHO_25CM_VALUE) > GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_THIRD])
            && ((GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_THIRD]) > GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FIFTH])
            && (PSLSnsListencheck(LenuSnsch) == TRUE))
            || (u8ObjSquareFlag == 1))
		{
            u8ObjSquareFlag = 0;
            u8PSLHaveObjOne[LenuSnsch] = 0;
            GstrPSLDeteData.enuPSLDetSts[LenuSnsch] = PSL_DETE_OBJ2;
            PSL_DEBUG_PRINTF("Goto PSL_DETE_OBJ2,ch%d\r\n",LenuSnsch);
            GstrPSLDeteData.fBackUpCarCoorx[LenuSnsch] = LpStrPSLUseOdo->fCarCoorX;
            GstrPSLDeteData.fBackUpCarCoory[LenuSnsch] = LpStrPSLUseOdo->fCarCoorY;
            GstrPSLDeteData.fRecordCarSlope[LenuSnsch][PSL_CAL_SLOPE_OBJ2] = LpStrPSLUseOdo->fCarYawAngle;
            //PSLCalObjAndCurbSlope(LenuSnsch,PSL_CAL_SLOPE_CURB);
            //PSLAnalysisSlopeData(LenuSnsch,PSL_CAL_SLOPE_CURB);
            PSLCalObjSlope(LenuSnsch,PSL_CAL_SLOPE_CURB);
            PSLCalCurbDepth(LenuSnsch);
            for(i = 0;i < 3;i++)
            {
                GstrPSLDeteData.strObj2Data[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]].fObjX = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_THIRD + i].fObjX;
                GstrPSLDeteData.strObj2Data[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]].fObjY = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_THIRD + i].fObjY;
                GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]].fObjX = PSLRecordOdoCoor[LenuSnsch][i].fObjX;
                GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]].fObjY = PSLRecordOdoCoor[LenuSnsch][i].fObjY;
                GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]] = LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD + i];
                GstrPSLDeteData.fRecObj2OdoAngle[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]] = PSLRecordOdoAngle[LenuSnsch][i];
                GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]++;
            }
		}
        else
        {
            if((LpPSLRecordObj->enuMeasMode[LenuSnsch][PSL_DETE_FIFTH] == PSL_SNS_MEAS_STD && GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] < PSL_SNS_MEAS_CHIRP_MIN)
                && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD] + PSL_ECHO_20CM_VALUE))
                && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] + PSL_ECHO_20CM_VALUE)))
            {
                u8PSLHaveObjOne[LenuSnsch] = 0;
                GstrPSLDeteData.enuPSLDetSts[LenuSnsch] = PSL_DETE_OBJ2;
                GstrPSLDeteData.fBackUpCarCoorx[LenuSnsch] = LpStrPSLUseOdo->fCarCoorX;
                GstrPSLDeteData.fBackUpCarCoory[LenuSnsch] = LpStrPSLUseOdo->fCarCoorY;
                GstrPSLDeteData.fRecordCarSlope[LenuSnsch][PSL_CAL_SLOPE_OBJ2] = LpStrPSLUseOdo->fCarYawAngle;
                //PSLCalObjAndCurbSlope(LenuSnsch,PSL_CAL_SLOPE_CURB);
                //PSLAnalysisSlopeData(LenuSnsch,PSL_CAL_SLOPE_CURB);
                PSLCalObjSlope(LenuSnsch,PSL_CAL_SLOPE_CURB);
                PSLCalCurbDepth(LenuSnsch);
              
                GstrPSLDeteData.strObj2Data[LenuSnsch][0].fObjX = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_THIRD].fObjX;
                GstrPSLDeteData.strObj2Data[LenuSnsch][0].fObjY = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_THIRD].fObjY;
                GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][0].fObjX = PSLRecordOdoCoor[LenuSnsch][0].fObjX;
                GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][0].fObjY = PSLRecordOdoCoor[LenuSnsch][0].fObjY;
                GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][0] = LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD];
                GstrPSLDeteData.fRecObj2OdoAngle[LenuSnsch][0] = PSLRecordOdoAngle[LenuSnsch][0];
                GstrPSLDeteData.strObj2Data[LenuSnsch][1].fObjX = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_FIFTH].fObjX;
                GstrPSLDeteData.strObj2Data[LenuSnsch][1].fObjY = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_FIFTH].fObjY;
                GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][1].fObjX = PSLRecordOdoCoor[LenuSnsch][2].fObjX;
                GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][1].fObjY = PSLRecordOdoCoor[LenuSnsch][2].fObjY;
                GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][1] = LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH];
                GstrPSLDeteData.fRecObj2OdoAngle[LenuSnsch][1] = PSLRecordOdoAngle[LenuSnsch][2];
                GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch] = 2;                
            }
        }
    }
    else
    {
        if(((GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] + PSL_ECHO_10CM_VALUE) > GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FOURTH])
            &&((GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] + PSL_ECHO_10CM_VALUE) > GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FIFTH])
            && ((GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] + PSL_ECHO_10CM_VALUE) > GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_THIRD])
            && ((GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_THIRD] + PSL_ECHO_10CM_VALUE) > GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FOURTH])
            && ((GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] + PSL_ECHO_10CM_VALUE) > GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_THIRD])
            && ((GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_THIRD] + PSL_ECHO_10CM_VALUE) > GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FIFTH])
            && ((GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FIFTH]) < 2500)
            && (PSLSnsListencheck(LenuSnsch) == TRUE))
            {
                GstrPSLDeteData.u8PSLSlotDetedObj[LenuSnsch] = SLOT_HAVE_OBJ;               
            }

    }

    
}

/******************************************************************************
  * 函数名称: PSLDeteSlotWorkStsCurb
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-3-10 11:30    V0.1      22866        初次发布
  ******************************************************************************/

void PSLDeteSlotWorkStsCurb(PSLSnsChannelType LenuSnsch)
{
    PSLRecordObjCoorType *LpPSLRecordObj;
    LpPSLRecordObj = &GstrPSLRecordObjCoor;
    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;
    PSLDetermineDataType *LpStrPSLDetData;
    LpStrPSLDetData = &GstrPSLDeteData;
    float fCarMoveDis = 0.0;
    float fCarCurOdox = 0.0;
    float fCarCurOdoy = 0.0;
    uint16 u16CurEchoDis = 0u;
    uint16 u16OneEchoDis = 0u;
    uint16 u16TwoEchoDis = 0u;
    float fCornerOdox = 0.0;
    float fCornerOdoy = 0.0;
    uint8 u8InvalidCnt = 0;
    uint8 i;
    u16CurEchoDis = LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH];
    u16OneEchoDis = LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH];
    u16TwoEchoDis = LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD];

    fCarCurOdox = LpStrPSLUseOdo->fCarCoorX;
    fCarCurOdoy = LpStrPSLUseOdo->fCarCoorY;
	fCornerOdox = GstrPSLDeteData.fRecObj1CornerOdox[LenuSnsch];
    fCornerOdoy = GstrPSLDeteData.fRecObj1CornerOdoy[LenuSnsch];
    fCarMoveDis = PubAI_CalTwoPointDis(fCarCurOdox,fCarCurOdoy, fCornerOdox, fCornerOdoy);
    if(((((u16CurEchoDis + PSL_ECHO_DIFF_MAX) > u16OneEchoDis) && ((u16OneEchoDis + PSL_ECHO_DIFF_MAX) > u16CurEchoDis))
        || (((u16CurEchoDis + PSL_ECHO_DIFF_MAX) > u16TwoEchoDis) && ((u16TwoEchoDis + PSL_ECHO_DIFF_MAX) > u16CurEchoDis)))
        && u16CurEchoDis != PSL_ECHO_INVALID_DATA)
    {
        PSLRecordCurbData(LenuSnsch);
        if(fCarMoveDis < PSL_MVOE_OBJ_MIN_DIS)
        {
            PSLDetObj1ToCurbHandle(LenuSnsch);
        }
        else if(fCarMoveDis >= PSL_MVOE_OBJ_MIN_DIS && fCarMoveDis < GstrPSL_APPPara_Ram.u16SlotWidthMax_H)
        {
            PSLDeteCurbHandle(LenuSnsch);
        }
        else
        {
            
            if(GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] < GstrPSL_APPPara_Ram.u16DetObjDisMax)
            {

        		if(((GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] + PSL_ECHO_25CM_VALUE) > GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FOURTH]) 
                    && ((GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] + PSL_ECHO_25CM_VALUE) > GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_THIRD])
                    && ((GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] + PSL_ECHO_25CM_VALUE) > GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_THIRD])
                    && ((GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_THIRD]) > GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FIFTH])
                    && (PSLSnsListencheck(LenuSnsch) == TRUE))
        		{
        
                    u8PSLHaveObjOne[LenuSnsch] = 0;
                    GstrPSLDeteData.fBackUpCarCoorx[LenuSnsch] = LpStrPSLUseOdo->fCarCoorX;
                    GstrPSLDeteData.fBackUpCarCoory[LenuSnsch] = LpStrPSLUseOdo->fCarCoorY;
                    GstrPSLDeteData.fRecordCarSlope[LenuSnsch][PSL_CAL_SLOPE_OBJ2] = LpStrPSLUseOdo->fCarYawAngle;
                    //PSLCalObjAndCurbSlope(LenuSnsch,PSL_CAL_SLOPE_CURB);
                    //PSLAnalysisSlopeData(LenuSnsch,PSL_CAL_SLOPE_CURB);
                    PSLCalObjSlope(LenuSnsch,PSL_CAL_SLOPE_CURB);
                    PSLCalCurbDepth(LenuSnsch);     
                    if(LpStrPSLDetData->u16CurbDepth[LenuSnsch] < PSL_CURB_DEPTH_INVALID)
                    {
                        GstrPSLDeteData.enuPSLDetSts[LenuSnsch] = PSL_DETE_OBJ2;
                        GstrPSLDeteData.LenuSlotObjType[LenuSnsch] = PSL_SLOT_ONLYOBJ2;
                        for(i = 0;i < 3;i++)
                        {
                            GstrPSLDeteData.strObj2Data[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]].fObjX = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_THIRD + i].fObjX;
                            GstrPSLDeteData.strObj2Data[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]].fObjY = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_THIRD + i].fObjY;
                            GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]].fObjX = PSLRecordOdoCoor[LenuSnsch][i].fObjX;
                            GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]].fObjY = PSLRecordOdoCoor[LenuSnsch][i].fObjY;
                            GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]] = LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD + i];
                            GstrPSLDeteData.fRecObj2OdoAngle[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]] = PSLRecordOdoAngle[LenuSnsch][i];
                            GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]++;

                        }
                    }
                    else
                    {
                        GstrPSLDeteData.enuPSLDetSts[LenuSnsch] = PSL_DETE_OBJ1;
                    }
                    PSLClearObj1RecordData(LenuSnsch);  
                }
            }
        }
    }
    else
    {
        if(u16CurEchoDis < PSL_ECHO_INVALID_DATA && u16CurEchoDis > 2700)
        {
            if(fCarMoveDis < GstrPSL_APPPara_Ram.u16SlotWidthMax_H)
            {
                PSLRecordCurbData(LenuSnsch);
            }
        }
        if(fCarMoveDis < PSL_MVOE_OBJ_MIN_DIS)
        {
            //PSLDeteCurbDataInit(LenuSnsch);
        }
        else
        {
            for(i = PSL_DETE_THIRD;i <= PSL_DETE_FIFTH;i++)
            {
                if(LpPSLRecordObj->u16MasterDis[LenuSnsch][i] == PSL_ECHO_INVALID_DATA)
                    u8InvalidCnt++;
            }
            if((fCarMoveDis > GstrPSL_APPPara_Ram.u16SlotWidthMax_H)
                && (u8InvalidCnt >= 2))
            {
                PSLClearObj1RecordData(LenuSnsch);
            }
        }
    }
    
}

/******************************************************************************
  * 函数名称: PSLRecordObjInvalidData
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-3-14 9:30    V0.1      22866        初次发布
  ******************************************************************************/
void PSLRecordObjInvalidData(PSLSnsChannelType LenuSnsch)
{
    PSLRecordObjCoorType *LpPSLRecordObj;
    LpPSLRecordObj = &GstrPSLRecordObjCoor;

    if(LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] >= GstrPSL_APPPara_Ram.u16DetObjDisMin
        && LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] <= (GstrPSL_APPPara_Ram.u16DetObjDisMax))
    {
        GstrPSLDeteData.u8FirObjInvalidcnt[LenuSnsch] = PSL_DET_INVALID_CNT;
    }
    else
    {
        GstrPSLDeteData.u8FirObjInvalidcnt[LenuSnsch]++;
        if(GstrPSLDeteData.u8FirObjInvalidcnt[LenuSnsch] > PSL_RECORD_OBJ_MAX)
            GstrPSLDeteData.u8FirObjInvalidcnt[LenuSnsch] = PSL_RECORD_OBJ_MAX;
    }
}

/******************************************************************************
  * 函数名称: PSLCalSlotType
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-3-17 11:20    V0.1      22866        初次发布
  ******************************************************************************/
void PSLCalSlotType(PSLSnsChannelType LenuSnsch)
{
    
    if(((GstrPSLDeteData.u16SlotWidth[LenuSnsch] >= GstrPSL_APPPara_Ram.u16SlotWidthMin_V) 
        && (GstrPSLDeteData.u16SlotWidth[LenuSnsch] <= GstrPSL_APPPara_Ram.u16SlotWidthMax_V)
        &&(GstrPSLDeteData.u16CurbDepth[LenuSnsch] >= PSL_CURB_DEPTH_INVALID)))
    {
        if(LenuSnsch == PSL_SNS_CH_FLS/* || LenuSnsch == PSL_SNS_CH_RLS*/)
        {
            GstrPSLDeteData.LenuSlotType[LenuSnsch] = PSL_SLOT_LEFT_VERTICAL;
        }
        else if(LenuSnsch == PSL_SNS_CH_FRS/* || LenuSnsch == PSL_SNS_CH_RRS*/)
        {
            GstrPSLDeteData.LenuSlotType[LenuSnsch] = PSL_SLOT_RIGHT_VERTICAL;
        }
    }
    else if(((GstrPSLDeteData.u16SlotWidth[LenuSnsch] >= GstrPSL_APPPara_Ram.u16SlotWidthMin_H) 
        && (GstrPSLDeteData.u16SlotWidth[LenuSnsch] <= GstrPSL_APPPara_Ram.u16SlotWidthMax_H)
        &&(GstrPSLDeteData.u16CurbDepth[LenuSnsch] >= GstrPSL_APPPara_Ram.u16SlotDepthMin_H)))
   {
        if(LenuSnsch == PSL_SNS_CH_FLS/* || LenuSnsch == PSL_SNS_CH_RLS*/)
        {
            GstrPSLDeteData.LenuSlotType[LenuSnsch] = PSL_SLOT_LEFT_HORIZONTAL;
        }
        else if(LenuSnsch == PSL_SNS_CH_FRS/* || LenuSnsch == PSL_SNS_CH_RRS*/)
        {
            GstrPSLDeteData.LenuSlotType[LenuSnsch] = PSL_SLOT_RIGHT_HORIZONTAL;
        }
    }
    else
    {
        GstrPSLDeteData.LenuSlotType[LenuSnsch] = PSL_SLOT_NONE;
    }
}

/******************************************************************************
  * 函数名称: PSLSingleSnsOutputSlot
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-3-14 10:40    V0.1      22866        初次发布
  ******************************************************************************/
void PSLSingleSnsOutputSlot(PSLSnsChannelType LenuSnsch)
{
    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;
    
    GsPSLOutputSlotData.wSlotWidth[GsPSLOutputSlotData.wSlotCnt] = GstrPSLDeteData.u16SlotWidth[LenuSnsch];
    GsPSLOutputSlotData.dSlotDepthDis[GsPSLOutputSlotData.wSlotCnt] = GstrPSLDeteData.u16CurbDepth[LenuSnsch];
    GsPSLOutputSlotData.cSlotType[GsPSLOutputSlotData.wSlotCnt] = GstrPSLDeteData.LenuSlotType[LenuSnsch];
    GsPSLOutputSlotData.dFirstObjWx[GsPSLOutputSlotData.wSlotCnt] = GstrPSLDeteData.fFirstObjCornerX[LenuSnsch];
    GsPSLOutputSlotData.dFirstObjWy[GsPSLOutputSlotData.wSlotCnt] = GstrPSLDeteData.fFirstObjCornerY[LenuSnsch];
    GsPSLOutputSlotData.dSecObjWx[GsPSLOutputSlotData.wSlotCnt] = GstrPSLDeteData.fSecObjCornerX[LenuSnsch];
    GsPSLOutputSlotData.dSecObjWy[GsPSLOutputSlotData.wSlotCnt] = GstrPSLDeteData.fSecObjCornerY[LenuSnsch];
    GsPSLOutputSlotData.dObj1Slope[GsPSLOutputSlotData.wSlotCnt] = GstrPSLDeteData.fObj1Slope[LenuSnsch];
    GsPSLOutputSlotData.dObj2Slope[GsPSLOutputSlotData.wSlotCnt] = GstrPSLDeteData.fObj2Slope[LenuSnsch];
    GsPSLOutputSlotData.fRecordCarSlope[GsPSLOutputSlotData.wSlotCnt] = LpStrPSLUseOdo->fCarYawAngle;
    GsPSLOutputSlotData.cFirObjType[GsPSLOutputSlotData.wSlotCnt] = GstrPSLDeteData.LenuFirObjType[LenuSnsch];
    GsPSLOutputSlotData.cSecObjType[GsPSLOutputSlotData.wSlotCnt] = GstrPSLDeteData.LenuSecObjType[LenuSnsch];
    if(LenuSnsch == PSL_SNS_CH_FLS)
    {
        GsPSLOutputSlotData.cSlotDir[GsPSLOutputSlotData.wSlotCnt] = PSL_Slotdir_AtLeft;
    }
    else if(LenuSnsch == PSL_SNS_CH_FRS)
    {
        GsPSLOutputSlotData.cSlotDir[GsPSLOutputSlotData.wSlotCnt] = PSL_Slotdir_AtRight;
    }
    PSL_DEBUG_PRINTF("FirstObjxy:%.1f,%.1f SecObjxy:%.1f,%.1f\r\n",
        GsPSLOutputSlotData.dFirstObjWx[GsPSLOutputSlotData.wSlotCnt],
        GsPSLOutputSlotData.dFirstObjWy[GsPSLOutputSlotData.wSlotCnt],
        GsPSLOutputSlotData.dSecObjWx[GsPSLOutputSlotData.wSlotCnt],
        GsPSLOutputSlotData.dSecObjWy[GsPSLOutputSlotData.wSlotCnt]);
    PSL_DEBUG_PRINTF("Odox:%.1f Odoy:%.1f\r\n",LpStrPSLUseOdo->fCarCoorX,LpStrPSLUseOdo->fCarCoorY);
    PSL_DEBUG_PRINTF("wSlotCnt++ ch:%d\r\n",LenuSnsch);
    GsPSLOutputSlotData.wSlotCnt++;
    PSLCalSlotcoor[GsPSLOutputSlotData.wSlotCnt - 1].dObj1Slope = GsPSLOutputSlotData.dObj1Slope[GsPSLOutputSlotData.wSlotCnt - 1];
    PSLCalSlotcoor[GsPSLOutputSlotData.wSlotCnt - 1].dObj2Slope = GsPSLOutputSlotData.dObj2Slope[GsPSLOutputSlotData.wSlotCnt - 1];
    PSL_DEBUG_PRINTF("\r\n\r\n");
	PSL_DEBUG_PRINTF("==============================================================================================");
	PSL_DEBUG_PRINTF("\r\n\r\n");
}
/******************************************************************************
  * 函数名称: PSLCalPointDis
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2024-1-11 08:40    V0.1      22866        初次发布
  ******************************************************************************/

float PSLCalPointDis(PSLCalPointBetweenDisType * LsPointSdata)
{
    float LdTempdata1 , LdTempdata2 , LdTempdata3 , LdTempdata4 ,LdTempdata5;
    float LdCarObjSlope ;
    float LdPointDis;
    
    /*点一为起点，点二为终点*/
    LdTempdata1 = LsPointSdata->dPointTwoWx - LsPointSdata->dPointOneWx ;
    LdTempdata2 = LsPointSdata->dPointTwoWy - LsPointSdata->dPointOneWy ;
    
    if(LdTempdata1 < 0.0)
    {
       LdTempdata3 = - LdTempdata1;
    }
    else
    {
       LdTempdata3 = LdTempdata1;
    }
    
    if(LdTempdata2 < 0.0)
    {
       LdTempdata4 = - LdTempdata2;
    }
    else
    {
       LdTempdata4 = LdTempdata2;
    }
    
    if((LdTempdata3 < 10.0)
        &&(LdTempdata4 < 10.0))
    {
        LdPointDis = 0.0;
    }
    else 
    {
        if(LdTempdata3 >= 10.0)
        {
            LdTempdata5 = LdTempdata4/LdTempdata3;
            LdTempdata5 = atanf(LdTempdata5);
            LdCarObjSlope = LdTempdata5;
            LdTempdata5 = cosf(LdTempdata5);
            LdPointDis = LdTempdata3/LdTempdata5;
        }
        else
        {
            LdTempdata5 = LdTempdata3/LdTempdata4;
            LdTempdata5 = atanf(LdTempdata5);
            LdCarObjSlope = LdTempdata5;
            LdCarObjSlope = APA_90_ANGLE  - LdCarObjSlope;
            LdTempdata5 = cosf(LdTempdata5);
            LdPointDis = LdTempdata4/LdTempdata5;
        }
    
        if((LdTempdata1 >= 0)
            && (LdTempdata2 >= 0))
        {
        }
        else if((LdTempdata1 <= 0)
            && (LdTempdata2 >= 0))
        {
            LdCarObjSlope = APA_180_ANGLE  - LdCarObjSlope;
        }
        else if((LdTempdata1 >= 0)
            && (LdTempdata2 <= 0))
        {
            LdCarObjSlope = -LdCarObjSlope;
        }
        else
        {
            LdCarObjSlope = LdCarObjSlope - APA_180_ANGLE;
        }
    
        LdTempdata5 = PSLCalSlopeDec(LdCarObjSlope ,LsPointSdata->dSlope);
        LdTempdata5 = cosf(LdTempdata5);
        LdPointDis = LdTempdata5*LdPointDis;
    }
    
    return LdPointDis;
}
/******************************************************************************
  * 函数名称: PSLSlotOutCheck
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2024-1-11 09:45    V0.1      22866        初次发布
  ******************************************************************************/
bool PSLSlotOutCheck(PSLSnsChannelType LenuSnsch)
{
    eSnsChannelType enuSnsCh = SNS_CH_FLS;
    SnsInstallGroupType enuSnsGroup = SNS_INSTALL_GROUP_FRONT;
    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;
    PSLCalPointBetweenDisType GsPSLCalPointDisData;
    float fSnsInSnsCoorX = 0.0;
    float fSnsInSnsCoorY = 0.0;
    float fSnsInCarCoorX = 0.0;
    float fSnsInCarCoorY = 0.0;
    float fSnsInODOCoorX = 0.0;
    float fSnsInODOCoorY = 0.0;
    float fCarCos = 0.0;
    float fCarSin = 0.0;
    float LdTwoSlotFrontPointDis = 0.0;
    bool LcCoordinateFlg = FALSE;
    
    if(LenuSnsch == PSL_SNS_CH_FLS/* || LenuSnsch == PSL_SNS_CH_RLS*/)
    {
        enuSnsCh = SNS_CH_FRLS;
    }               
    else if(LenuSnsch == PSL_SNS_CH_FRS/* || LenuSnsch == PSL_SNS_CH_RRS*/)
    {
        enuSnsCh = SNS_CH_FRRS;
    }
    
    PubAI_UpdateSnsInCarCoor(GstrRamSnsCoor[enuSnsGroup][enuSnsCh].cRadarX,GstrRamSnsCoor[enuSnsGroup][enuSnsCh].cRadarY,\
                    GstrSnsOutAngle[enuSnsGroup].fSinA[enuSnsCh],GstrSnsOutAngle[enuSnsGroup].fCosA[enuSnsCh]);    

    PubAI_TransObjSnsCoorToCarCoor(&fSnsInSnsCoorX,&fSnsInSnsCoorX,&fSnsInCarCoorX,&fSnsInCarCoorY);
    PSL_DEBUG_PRINTF("SnsCar X Y:%.1f,%.1f\r\n",fSnsInCarCoorX,fSnsInCarCoorY);
    fCarCos = LpStrPSLUseOdo->fCarCosYawAngle;
    fCarSin = LpStrPSLUseOdo->fCarSinYawAngle;
  
    PubAI_UpdateCarOdoCoor(LpStrPSLUseOdo->fCarCoorX,LpStrPSLUseOdo->fCarCoorY,fCarSin,fCarCos); 
    PubAI_TransObjCarCoorToOdoCoor(&fSnsInCarCoorX,&fSnsInCarCoorY,&fSnsInODOCoorX,&fSnsInODOCoorY);
    PSL_DEBUG_PRINTF("SnsCar X Y:%.1f,%.1f\r\n",fSnsInODOCoorX,fSnsInODOCoorY);
    
    GsPSLCalPointDisData.dSlope = LpStrPSLUseOdo->fCarYawAngle;
    GsPSLCalPointDisData.dPointOneWx = GstrPSLDeteData.fSecObjCornerX[LenuSnsch];
    GsPSLCalPointDisData.dPointOneWy = GstrPSLDeteData.fSecObjCornerY[LenuSnsch];
    GsPSLCalPointDisData.dPointTwoWx = fSnsInODOCoorX;
    GsPSLCalPointDisData.dPointTwoWy = fSnsInODOCoorY;
    PSL_DEBUG_PRINTF("Sns ODO X Y:%.1f,%.1f\r\n",fSnsInODOCoorX,fSnsInODOCoorY);
   
    LdTwoSlotFrontPointDis = PSLCalPointDis(&GsPSLCalPointDisData);
    PSL_DEBUG_PRINTF("LdTwoSlotFrontPointDis:%ld\r\n",(sint32)LdTwoSlotFrontPointDis);
    if(LdTwoSlotFrontPointDis < -800)
    {
        LcCoordinateFlg = TRUE;
        PSL_DEBUG_PRINTF("LcCoordinateOverlap = TRUE!!\r\n");
    }
    return LcCoordinateFlg;
}
/******************************************************************************
  * 函数名称: PSLOBJCoordinateCheck
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2024-1-10 17:40    V0.1      22866        初次发布
  ******************************************************************************/
bool PSLOBJCoordinateCheck(PSLSnsChannelType LenuSnsch)
{
    uint8 LcSlotNum = 0;
    PSLSlotDirection LcSlotSidePosition = PSL_Slotdir_None;
    bool LcCoordinateFlg = FALSE;
    float LdTwoSlotFrontPointDis = 0.0;
    
    
    
    if((PSL_SNS_CH_FLS == LenuSnsch)/*||(APA_RLS_CHANNEL == RadioNumber)*/)
    {
        LcSlotSidePosition = PSL_Slotdir_AtLeft;
    }
    else if(PSL_SNS_CH_FRS == LenuSnsch)
    {
        LcSlotSidePosition = PSL_Slotdir_AtRight;
    }
    
    for(LcSlotNum = 0;LcSlotNum < GsPSLOutputSlotData.wSlotCnt;LcSlotNum++)
    {   
        if(GsPSLOutputSlotData.cSlotDir[LcSlotNum] == LcSlotSidePosition)
        {
            if(((GstrPSLDeteData.fFirstObjCornerX[LenuSnsch] >= GsPSLOutputSlotData.dFirstObjWx[LcSlotNum])
                && (GstrPSLDeteData.fFirstObjCornerX[LenuSnsch] <= GsPSLOutputSlotData.dSecObjWx[LcSlotNum]))
                ||((GstrPSLDeteData.fSecObjCornerX[LenuSnsch] >= GsPSLOutputSlotData.dFirstObjWx[LcSlotNum])
                && (GstrPSLDeteData.fSecObjCornerX[LenuSnsch] <= GsPSLOutputSlotData.dSecObjWx[LcSlotNum]))
                ||((GstrPSLDeteData.fFirstObjCornerX[LenuSnsch] <= GsPSLOutputSlotData.dFirstObjWx[LcSlotNum])
                && (GstrPSLDeteData.fSecObjCornerX[LenuSnsch] >= GsPSLOutputSlotData.dSecObjWx[LcSlotNum])))
            {
                PSL_DEBUG_PRINTF("CoordinateOverlap!!\r\n");
                LcCoordinateFlg = TRUE;
                return LcCoordinateFlg;
            }
        }
    }

    if(PSLSlotOutCheck(LenuSnsch) == TRUE)
    {
        LcCoordinateFlg = TRUE;
    }
    
    return LcCoordinateFlg;
}

/******************************************************************************
  * 函数名称: PSLOBJTypeCheck
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2024-1-17 10:40    V0.1      22866        初次发布
******************************************************************************/

void PSLOBJTypeCheck(PSLSnsChannelType LenuSnsch)
{
    if(GstrPSLDeteData.LenuSlotObjType[LenuSnsch] == PSL_SLOT_ONLYOBJ1)
    {
        GstrPSLDeteData.LenuFirObjType[LenuSnsch] = PSL_OBJ_TYPE_REAL;
        GstrPSLDeteData.LenuSecObjType[LenuSnsch] = PSL_OBJ_TYPE_VIRTUAL;
    }
    else if(GstrPSLDeteData.LenuSlotObjType[LenuSnsch] == PSL_SLOT_ONLYOBJ2)
    {
        GstrPSLDeteData.LenuFirObjType[LenuSnsch] = PSL_OBJ_TYPE_VIRTUAL;
        GstrPSLDeteData.LenuSecObjType[LenuSnsch] = PSL_OBJ_TYPE_REAL;
    }
    else if(GstrPSLDeteData.LenuSlotObjType[LenuSnsch] == PSL_SLOT_TWOOBJ)
    {
        GstrPSLDeteData.LenuFirObjType[LenuSnsch] = PSL_OBJ_TYPE_REAL;
        GstrPSLDeteData.LenuSecObjType[LenuSnsch] = PSL_OBJ_TYPE_REAL;
    }
}

/******************************************************************************
  * 函数名称: PSLUpdateDetermineSlot
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-3-14 10:40    V0.1      22866        初次发布
  ******************************************************************************/
void PSLUpdateDetermineSlot(PSLSnsChannelType LenuSnsch)  
{
    float fCalObjCoorX = 0.0;
    float fCalObjCoorY = 0.0;
    float LfP1_X = 0.0;
    float LfP1_Y = 0.0;
    float LfP2_X = 0.0;
    float LfP2_Y = 0.0;
    float fSlopeSum = 0.0;
    
    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;
    PSLDetermineDataType *LpGstrPSLDeteData;
    LpGstrPSLDeteData = &GstrPSLDeteData;
    if(GstrPSLDeteData.LenuSlotObjType[LenuSnsch] == PSL_SLOT_ONLYOBJ2)
    {
        fSlopeSum = PSLCalSlopeDec(GstrPSLDeteData.fRecordCarSlope[LenuSnsch][PSL_CAL_SLOPE_OBJ2],LpStrPSLUseOdo->fCarYawAngle);
        if(fSlopeSum < 0)
        {
            fSlopeSum = -fSlopeSum;
        }
        PSL_DEBUG_PRINTF("sum obj2 slope:%.1f\r\n",fSlopeSum * PSL_ANGLE_CONVERT_PARAMETER);
        if(fSlopeSum > APA_10_ANGLE)
        {
            PSL_DEBUG_PRINTF("OnlyObj2 Car Slope is big  ch:%d\r\n",LenuSnsch);
            return;
        }
    #if 0
        fSlopeSum = LpGstrPSLDeteData->fObj2Slope[LenuSnsch];
        if(fSlopeSum < 0)
        {
            fSlopeSum = -fSlopeSum;
        }
        if(fSlopeSum > APA_10_ANGLE)
        {
            PSL_DEBUG_PRINTF("OnlyObj2 Obj2 Slope is big  ch:%d\r\n",LenuSnsch);
            return;
        }
    #endif
        PSL_DEBUG_PRINTF("Curb Angle:%.2f\r\n",GstrPSLDeteData.fCurbSlope[LenuSnsch] * PSL_ANGLE_CONVERT_PARAMETER);
        GstrPSLDeteData.u16SlotWidth[LenuSnsch] = GstrPSL_APPPara_Ram.u16SlotWidthMax_H;
        fSlopeSum = PSLCalSlopeDec(GstrPSLDeteData.fCurbSlope[LenuSnsch],LpStrPSLUseOdo->fCarYawAngle);
        if(fSlopeSum < 0)
        {
            fSlopeSum = -fSlopeSum;
        }
        
        if(fSlopeSum < APA_7_ANGLE)
        {
            fCalObjCoorX = GstrPSLDeteData.u16SlotWidth[LenuSnsch] * cosf(GstrPSLDeteData.fCurbSlope[LenuSnsch]);
            fCalObjCoorY = GstrPSLDeteData.u16SlotWidth[LenuSnsch] * sinf(GstrPSLDeteData.fCurbSlope[LenuSnsch]);
        }
        else
        {
            fCalObjCoorX = GstrPSLDeteData.u16SlotWidth[LenuSnsch] * cosf(LpStrPSLUseOdo->fCarYawAngle);
            fCalObjCoorY = GstrPSLDeteData.u16SlotWidth[LenuSnsch] * sinf(LpStrPSLUseOdo->fCarYawAngle);
        }
        //fCalObjCoorX = GstrPSLDeteData.u16SlotWidth[LenuSnsch] * LpStrPSLUseOdo->fCarCosYawAngle;
        //fCalObjCoorY = GstrPSLDeteData.u16SlotWidth[LenuSnsch] * LpStrPSLUseOdo->fCarSinYawAngle;
        PSL_DEBUG_PRINTF("SlotWidth:%d Coorx:%.1f Coory:%.1f\r\n",GstrPSLDeteData.u16SlotWidth[LenuSnsch],
            fCalObjCoorX,
            fCalObjCoorY);
        PSL_DEBUG_PRINTF("Second Cornerx:%.1f Cornery:%.1f\r\n",GstrPSLDeteData.fSecObjCornerX[LenuSnsch],GstrPSLDeteData.fSecObjCornerY[LenuSnsch]);
        GstrPSLDeteData.fFirstObjCornerX[LenuSnsch] = GstrPSLDeteData.fSecObjCornerX[LenuSnsch] - fCalObjCoorX;
        GstrPSLDeteData.fFirstObjCornerY[LenuSnsch] = GstrPSLDeteData.fSecObjCornerY[LenuSnsch] - fCalObjCoorY;
        PSL_DEBUG_PRINTF("First Cornerx:%.1f Cornery:%.1f\r\n",GstrPSLDeteData.fFirstObjCornerX[LenuSnsch],GstrPSLDeteData.fFirstObjCornerY[LenuSnsch]);
        if(GstrPSLDeteData.u16CurbDepth[LenuSnsch] < PSL_CURB_DEPTH_INVALID)
        {
            GstrPSLDeteData.u16CurbDepth[LenuSnsch] -= (uint16)(GstrPSLDeteData.fSecObjMinDis[LenuSnsch] + 120);
        }
        else
        {
            return;
        }
    }
    else if(GstrPSLDeteData.LenuSlotObjType[LenuSnsch] == PSL_SLOT_TWOOBJ)
    {
        LfP1_X = GstrPSLDeteData.fFirstObjCornerX[LenuSnsch];
        LfP1_Y = GstrPSLDeteData.fFirstObjCornerY[LenuSnsch];
        LfP2_X = GstrPSLDeteData.fSecObjCornerX[LenuSnsch];
        LfP2_Y = GstrPSLDeteData.fSecObjCornerY[LenuSnsch]; 
        GstrPSLDeteData.u16SlotWidth[LenuSnsch] = (uint16)PubAI_CalTwoPointDis(LfP1_X,LfP1_Y,LfP2_X,LfP2_Y);
        if(GstrPSLDeteData.u16CurbDepth[LenuSnsch] < PSL_CURB_DEPTH_INVALID)
        {         
            GstrPSLDeteData.u16CurbDepth[LenuSnsch] -= (uint16)(GstrPSLDeteData.fSecObjMinDis[LenuSnsch] + 120);
        }

        PSLCalSlotType(LenuSnsch);
        fSlopeSum = PSLCalSlopeDec(GstrPSLDeteData.fRecordCarSlope[LenuSnsch][PSL_CAL_SLOPE_OBJ1],LpStrPSLUseOdo->fCarYawAngle);
        if(fSlopeSum < 0)
        {
            fSlopeSum = -fSlopeSum;
        }
        PSL_DEBUG_PRINTF("Obj1 car slope:%.1f  CurCar Slope:%.1f\r\n",GstrPSLDeteData.fRecordCarSlope[LenuSnsch][PSL_CAL_SLOPE_OBJ1] * PSL_ANGLE_CONVERT_PARAMETER,
        LpStrPSLUseOdo->fCarYawAngle * PSL_ANGLE_CONVERT_PARAMETER);
        PSL_DEBUG_PRINTF("sum obj1 curcar slope:%.1f\r\n",fSlopeSum * PSL_ANGLE_CONVERT_PARAMETER);
        if(fSlopeSum > APA_15_ANGLE)
        {
            PSL_DEBUG_PRINTF("Two obj Obj1 Car Slope is big  ch:%d\r\n",LenuSnsch);
            return;
        }

        fSlopeSum = PSLCalSlopeDec(GstrPSLDeteData.fRecordCarSlope[LenuSnsch][PSL_CAL_SLOPE_OBJ2],LpStrPSLUseOdo->fCarYawAngle);
        if(fSlopeSum < 0)
        {
            fSlopeSum = -fSlopeSum;
        }
        PSL_DEBUG_PRINTF("sum obj2 curcar slope:%.1f\r\n",fSlopeSum * PSL_ANGLE_CONVERT_PARAMETER);
        if(fSlopeSum > APA_15_ANGLE)
        {
            PSL_DEBUG_PRINTF("Two obj Obj2 Car Slope is big  ch:%d\r\n",LenuSnsch);
            return;
        }
        if(GstrPSLDeteData.LenuSlotType[LenuSnsch] == PSL_SLOT_LEFT_HORIZONTAL \
            || GstrPSLDeteData.LenuSlotType[LenuSnsch] == PSL_SLOT_RIGHT_HORIZONTAL)
        {
            PSL_DEBUG_PRINTF("oBJ1 SLOPE:%.5f;Obj2slope:%.5f\r\n",LpGstrPSLDeteData->fObj1Slope[LenuSnsch],LpGstrPSLDeteData->fObj2Slope[LenuSnsch]);
            fSlopeSum = PSLCalSlopeDec(LpGstrPSLDeteData->fObj1Slope[LenuSnsch],LpGstrPSLDeteData->fObj2Slope[LenuSnsch]);
            PSL_DEBUG_PRINTF("1sum obj2 slope:%.1f\r\n",fSlopeSum * PSL_ANGLE_CONVERT_PARAMETER);
             if(fSlopeSum < 0)
            {
                fSlopeSum = -fSlopeSum;
            }
            PSL_DEBUG_PRINTF("2sum obj2 slope:%.1f\r\n",fSlopeSum * PSL_ANGLE_CONVERT_PARAMETER);
            if(fSlopeSum > APA_20_ANGLE)
            {
                PSL_DEBUG_PRINTF("Two obj Slope is big  ch:%d\r\n",LenuSnsch);
                return;
            }
            fSlopeSum = LpGstrPSLDeteData->fObj1Slope[LenuSnsch];
            PSL_DEBUG_PRINTF("fObj1Slope slope:%.1f\r\n",fSlopeSum * PSL_ANGLE_CONVERT_PARAMETER);
            if(fSlopeSum < 0)
            {
                fSlopeSum = -fSlopeSum;
            }
            
            if(fSlopeSum > APA_15_ANGLE)
            {
                PSL_DEBUG_PRINTF("TwoObj Obj1 Slope is big  ch:%d\r\n",LenuSnsch);
                return;
            }

            fSlopeSum = LpGstrPSLDeteData->fObj2Slope[LenuSnsch];
            PSL_DEBUG_PRINTF("fObj2Slope slope:%.1f\r\n",fSlopeSum * PSL_ANGLE_CONVERT_PARAMETER);
            if(fSlopeSum < 0)
            {
                fSlopeSum = -fSlopeSum;
            }
            if(fSlopeSum > APA_15_ANGLE)
            {
                PSL_DEBUG_PRINTF("TwoObj Obj2 Slope is big slope:%.4f ch:%d\r\n",fSlopeSum,LenuSnsch);
                return;
            }
        }
    }
    
    if(PSLOBJCoordinateCheck(LenuSnsch) == TRUE)
    {
        return;
    }
    PSLOBJTypeCheck(LenuSnsch);
    if(GstrPSLDeteData.u16SlotWidth[LenuSnsch] <= GstrPSL_APPPara_Ram.u16SlotWidthMax_V)
    {
        if(GstrPSLDeteData.u8PSLSlotDetedObj[LenuSnsch] == SLOT_HAVE_OBJ)
        {
            GstrPSLDeteData.u8PSLSlotDetedObj[LenuSnsch] = SLOT_HAVE_NONE;
            return;
        }
    }
    PSL_DEBUG_PRINTF("Car SlotWidth:%d SlotDepth:%d\r\n",GstrPSLDeteData.u16SlotWidth[LenuSnsch],GstrPSLDeteData.u16CurbDepth[LenuSnsch]);
    if(((GstrPSLDeteData.u16SlotWidth[LenuSnsch] >= GstrPSL_APPPara_Ram.u16SlotWidthMin_V) 
        && (GstrPSLDeteData.u16SlotWidth[LenuSnsch] <= GstrPSL_APPPara_Ram.u16SlotWidthMax_V)
        &&(GstrPSLDeteData.u16CurbDepth[LenuSnsch] >= PSL_CURB_DEPTH_INVALID))
        ||((GstrPSLDeteData.u16SlotWidth[LenuSnsch] >= GstrPSL_APPPara_Ram.u16SlotWidthMin_H) 
        && (GstrPSLDeteData.u16SlotWidth[LenuSnsch] <= GstrPSL_APPPara_Ram.u16SlotWidthMax_H)
        && (GstrPSLDeteData.u16CurbDepth[LenuSnsch] >= GstrPSL_APPPara_Ram.u16SlotDepthMin_H)))
    {
        PSLUpdateValidSlot(LenuSnsch);      
    }
    else
    {
        if((GstrPSLDeteData.u16SlotWidth[LenuSnsch] > GstrPSL_APPPara_Ram.u16SlotWidthMax_H)
            && (GstrPSLDeteData.u16CurbDepth[LenuSnsch] >= GstrPSL_APPPara_Ram.u16SlotDepthMin_H))
        {
            GstrPSLDeteData.LenuSlotObjType[LenuSnsch] = PSL_SLOT_ONLYOBJ2;
            GstrPSLDeteData.u16SlotWidth[LenuSnsch] = GstrPSL_APPPara_Ram.u16SlotWidthMax_H;
            fCalObjCoorX = GstrPSLDeteData.u16SlotWidth[LenuSnsch] * LpStrPSLUseOdo->fCarCosYawAngle;
            fCalObjCoorY = GstrPSLDeteData.u16SlotWidth[LenuSnsch] * LpStrPSLUseOdo->fCarSinYawAngle;
            GstrPSLDeteData.fFirstObjCornerX[LenuSnsch] = GstrPSLDeteData.fSecObjCornerX[LenuSnsch] - fCalObjCoorX;
            GstrPSLDeteData.fFirstObjCornerY[LenuSnsch] = GstrPSLDeteData.fSecObjCornerY[LenuSnsch] - fCalObjCoorY;
            PSLUpdateValidSlot(LenuSnsch); 
        }
        else
        {
            PSL_DEBUG_PRINTF("SlotWidth or SlotDepth error\r\n");
        }
    }
    
  
}

/******************************************************************************
  * 函数名称: PSLUpdateOnlyObj1Slot
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-3-14 10:40    V0.1      22866        初次发布
  ******************************************************************************/
void PSLUpdateOnlyObj1Slot(PSLSnsChannelType LenuSnsch)  
{
    float fCalObjCoorX = 0.0;
    float fCalObjCoorY = 0.0;
    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;
    
    GstrPSLDeteData.enuPSLDetSts[LenuSnsch] = PSL_DETE_END;
    GstrPSLDeteData.u16SlotWidth[LenuSnsch] = GstrPSL_APPPara_Ram.u16SlotWidthMax_H;
    fCalObjCoorX = GstrPSLDeteData.u16SlotWidth[LenuSnsch] * LpStrPSLUseOdo->fCarCosYawAngle;
    fCalObjCoorY = GstrPSLDeteData.u16SlotWidth[LenuSnsch] * LpStrPSLUseOdo->fCarSinYawAngle;
    GstrPSLDeteData.fSecObjCornerX[LenuSnsch] = GstrPSLDeteData.fFirstObjCornerX[LenuSnsch] + fCalObjCoorX;
    GstrPSLDeteData.fSecObjCornerY[LenuSnsch] = GstrPSLDeteData.fFirstObjCornerY[LenuSnsch] + fCalObjCoorY;
    PSLUpdateDetermineSlot(LenuSnsch);
}

/******************************************************************************
  * 函数名称: PSLRecordOObj2Data
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-3-2 14:20     V0.1      22866        初次发布
  ******************************************************************************/
void PSLRecordOObj2Data(PSLSnsChannelType LenuSnsch,float *fEchoAverage)
{
    float fCurCarCoorx = 0.0;
    float fCurCarCoory = 0.0;
    float fBackUpCarCoorx= 0.0;
    float fBackUpCarCoory= 0.0;
    float fCurMasCoorx = 0.0;
    float fCurMasCoory = 0.0;
	float fTwoPointDis = 0.0;
    
    uint8 i = 0;
    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;

    PSLRecordObjCoorType *LpPSLRecordObj;
    LpPSLRecordObj = &GstrPSLRecordObjCoor;
    float fCurEchoAverage = 0.0;
    float fBackUpEcho = PSL_ECHO_INVALID_DATA;
    float fSlopeSum = 0.0;
    uint8 u8SnsSTDRecordFlag = 0;
    uint16 u16PreEcho = 0;
#if PSL_FILL_OBJ2_DATA
    uint8 u8Obj2TempCnt = 0;
#endif
    fSlopeSum = PSLCalSlopeDec(GstrPSLDeteData.fRecordCarSlope[LenuSnsch][PSL_CAL_SLOPE_OBJ2],LpStrPSLUseOdo->fCarYawAngle);
    if(fSlopeSum < 0)
    {
        fSlopeSum = -fSlopeSum;
    }
    if(fSlopeSum > APA_30_ANGLE)
    {
        GstrPSLDeteData.enuPSLDetSts[LenuSnsch] = PSL_DETE_INIT;
        return;
    }

    fSlopeSum = PSLCalSlopeDec(GstrPSLDeteData.fRecordCarSlope[LenuSnsch][PSL_CAL_SLOPE_OBJ2],GstrPSLDeteData.fRecordCarSlope[LenuSnsch][PSL_CAL_SLOPE_OBJ1]);
    if(fSlopeSum < 0)
    {
        fSlopeSum = -fSlopeSum;
    }
    if(fSlopeSum > APA_30_ANGLE)
    {
        GstrPSLDeteData.enuPSLDetSts[LenuSnsch] = PSL_DETE_INIT;
        return;
    }
    fCurMasCoorx = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_FIFTH].fObjX;
    fCurMasCoory = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_FIFTH].fObjY;
    if(LpPSLRecordObj->enuMeasMode[LenuSnsch][PSL_DETE_FIFTH] == PSL_SNS_MEAS_STD && (GstrPSLRecordObjCoor.u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] < (PSL_SNS_MEAS_CHIRP_MIN + 150)))
    {    
        if(((LpPSLRecordObj->enuMeasMode[LenuSnsch][PSL_DETE_THIRD] == PSL_SNS_MEAS_STD) 
            &&(LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD] + PSL_ECHO_20CM_VALUE))
            && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] + PSL_ECHO_20CM_VALUE)))
            || ((LpPSLRecordObj->enuMeasMode[LenuSnsch][PSL_DETE_SECOND] == PSL_SNS_MEAS_STD && LpPSLRecordObj->enuMeasMode[LenuSnsch][PSL_DETE_FOURTH] != PSL_SNS_MEAS_STD)
                && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_SECOND] + PSL_ECHO_20CM_VALUE))
                && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_SECOND] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] + PSL_ECHO_20CM_VALUE)))
            || ((LpPSLRecordObj->enuMeasMode[LenuSnsch][PSL_DETE_FIFTH] == PSL_SNS_MEAS_STD && LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] < PSL_SNS_MEAS_CHIRP_MIN + 150)
            && (LpPSLRecordObj->enuMeasMode[LenuSnsch][PSL_DETE_FOURTH] == PSL_SNS_MEAS_STD)
            && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] + PSL_ECHO_25CM_VALUE))
            && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] + PSL_ECHO_25CM_VALUE))))
        {
            u8SnsSTDRecordFlag = 1;
        }
    }
        
    if(((LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] + PSL_ECHO_25CM_VALUE))
        && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] + PSL_ECHO_25CM_VALUE))
        && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD] + PSL_ECHO_25CM_VALUE))
        && (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD] < (LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] + PSL_ECHO_25CM_VALUE)))
        || u8SnsSTDRecordFlag == 1)
    {
	#if !PSL_FILL_OBJ2_DATA
        if(PSLCalCurEchoAverage(LenuSnsch) == TRUE)
            fCurEchoAverage = GstrPSLDeteData.fCurEchoAverage[LenuSnsch];
        else
	#endif
            fCurEchoAverage = LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH];

        *fEchoAverage = fCurEchoAverage;
    	if(GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch] >= 1)
    		fBackUpEcho = GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch] - 1];

        if(fCurEchoAverage < GstrPSL_APPPara_Ram.u16DetObjDisMax/* && fCurEchoAverage < fBackUpEcho*/)
        { 
            fBackUpEcho = fCurEchoAverage;
            fCurCarCoorx = LpStrPSLUseOdo->fCarCoorX;
            fCurCarCoory = LpStrPSLUseOdo->fCarCoorY;
            fBackUpCarCoorx = GstrPSLDeteData.fBackUpCarCoorx[LenuSnsch];
            fBackUpCarCoory = GstrPSLDeteData.fBackUpCarCoory[LenuSnsch];
    		fTwoPointDis = PubAI_CalTwoPointDis(fCurCarCoorx, fCurCarCoory, fBackUpCarCoorx, fBackUpCarCoory);
            if(fTwoPointDis > PSL_RECORD_MIN_DOS)
            {
			#if 0
                u8Obj2TempCnt = GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch];
                if(u8Obj2TempCnt > 0)
                {
                    u8Obj2TempCnt--;
                }
                u16PreEcho = (uint16)GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][u8Obj2TempCnt];

                if(u16PreEcho == LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] || u8SnsSTDRecordFlag == 1)
                {
                    u8SnsSTDRecordFlag = 0;
                    GstrPSLDeteData.strObj2Data[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]].fObjX = fCurMasCoorx;
                    GstrPSLDeteData.strObj2Data[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]].fObjY = fCurMasCoory;
                    GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]].fObjX = fCurCarCoorx;
                    GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]].fObjY = fCurCarCoory;
                    GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]] = fCurEchoAverage;
                    GstrPSLDeteData.fRecObj2OdoAngle[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]] = LpStrPSLUseOdo->fCarYawAngle; 
                    GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]++;
                }
                else
                {
                    if(GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch] < PSL_RECORD_OBJ_MAX - 2)
                    {
                        for(i = 0;i < 3;i++)
                        {
                            GstrPSLDeteData.strObj2Data[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]].fObjX = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_THIRD + i].fObjX;
                            GstrPSLDeteData.strObj2Data[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]].fObjY = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_THIRD + i].fObjY;
                            GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]].fObjX = PSLRecordOdoCoor[LenuSnsch][i].fObjX;
                            GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]].fObjY = PSLRecordOdoCoor[LenuSnsch][i].fObjY;
                            GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]] = LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD + i];
                            GstrPSLDeteData.fRecObj2OdoAngle[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]] = PSLRecordOdoAngle[LenuSnsch][i];
                         
                            GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]++;
                        }
                    }
                    else
                    {
                        GstrPSLDeteData.strObj2Data[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]].fObjX = fCurMasCoorx;
                        GstrPSLDeteData.strObj2Data[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]].fObjY = fCurMasCoory;
                        GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]].fObjX = fCurCarCoorx;
                        GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]].fObjY = fCurCarCoory;
                        GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]] = fCurEchoAverage;
                        GstrPSLDeteData.fRecObj2OdoAngle[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]] = LpStrPSLUseOdo->fCarYawAngle;
                        GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]++;
                    }

                }
                GstrPSLDeteData.fBackUpCarCoorx[LenuSnsch] = fCurCarCoorx;
                GstrPSLDeteData.fBackUpCarCoory[LenuSnsch] = fCurCarCoory;
           #else			   
		        GstrPSLDeteData.strObj2Data[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]].fObjX = fCurMasCoorx;
		        GstrPSLDeteData.strObj2Data[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]].fObjY = fCurMasCoory;
		        GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]].fObjX = fCurCarCoorx;
		        GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]].fObjY = fCurCarCoory;
		        GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]] = fCurEchoAverage;
		        GstrPSLDeteData.fRecObj2OdoAngle[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]] = LpStrPSLUseOdo->fCarYawAngle;
		        GstrPSLDeteData.fBackUpCarCoorx[LenuSnsch] = fCurCarCoorx;
		        GstrPSLDeteData.fBackUpCarCoory[LenuSnsch] = fCurCarCoory;
		        GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch]++;
		   #endif
            if(GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch] >= PSL_RECORD_OBJ_MAX)
            {
                for(i = 0;i < PSL_RECORD_OBJ_MAX - 1;i++)
                {
                    GstrPSLDeteData.strObj2Data[LenuSnsch][i].fObjX = GstrPSLDeteData.strObj1Data[LenuSnsch][i + 1].fObjX;
                    GstrPSLDeteData.strObj2Data[LenuSnsch][i].fObjY = GstrPSLDeteData.strObj1Data[LenuSnsch][i + 1].fObjY;
                        GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][i].fObjX = GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][i + 1].fObjX;
                        GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][i].fObjY = GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][i + 1].fObjY;
                        GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][i] = GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][i + 1];
                        GstrPSLDeteData.fRecObj2OdoAngle[LenuSnsch][i] = GstrPSLDeteData.fRecObj2OdoAngle[LenuSnsch][i + 1];            
                    }
                    GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch] = PSL_RECORD_OBJ_MAX - 1;
                }
            }
        }
    }
        
    fCurCarCoorx = LpStrPSLUseOdo->fCarCoorX;
    fCurCarCoory = LpStrPSLUseOdo->fCarCoorY;
    fBackUpCarCoorx = GstrPSLDeteData.fBackUpCarCoorx[LenuSnsch];
    fBackUpCarCoory = GstrPSLDeteData.fBackUpCarCoory[LenuSnsch];
	fTwoPointDis = PubAI_CalTwoPointDis(fCurCarCoorx, fCurCarCoory, fBackUpCarCoorx, fBackUpCarCoory);

    if(fTwoPointDis > PSL_OBJ_INVALID_DIS)
    {
        GstrPSLDeteData.enuPSLDetSts[LenuSnsch] = PSL_DETE_END;
        if((LpPSLRecordObj->enuMeasMode[LenuSnsch][PSL_DETE_FIFTH] == PSL_SNS_MEAS_STD && LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] < PSL_SNS_MEAS_CHIRP_MIN)
            || (LpPSLRecordObj->enuMeasMode[LenuSnsch][PSL_DETE_FOURTH] == PSL_SNS_MEAS_STD && LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FOURTH] < PSL_SNS_MEAS_CHIRP_MIN)
            || (LpPSLRecordObj->enuMeasMode[LenuSnsch][PSL_DETE_THIRD] == PSL_SNS_MEAS_STD && LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_THIRD] < PSL_SNS_MEAS_CHIRP_MIN))
        {
            GstrPSLDeteData.LenuSecObjShape[LenuSnsch] = OBJ_SHAPE_NONE; 
            GstrPSLDeteData.enuPSLDetSts[LenuSnsch] = PSL_DETE_OBJ1;
        }
    }
}

/******************************************************************************
* 函数名称: PSLCalObj2SnsOdoCoor
* 
* 功能描述: 
* 
* 输入参数:
* 
* 输出参数:无 
* 
* 返回值:无 
* 
* 其它说明:无 
* 
* 修改日期              版本号        修改人          修改内容  
* 2023-7-20 09:20     V0.1      22866        初次发布
******************************************************************************/
void PSLCalObj2SnsOdoCoor(PSLSnsChannelType LenuSnsch,PSLObjCoorType * SnsOdoCoor)
{
    float fCarCos = 0.0;
    float fCarSin = 0.0;
    uint8 i = 0;
    PSLObjCoorType fCarSnsCoor;
    
    PSLCalSnsCoor(LenuSnsch,&fCarSnsCoor);
    for(i = 0;i <= GstrPSLDeteData.u8Obj2MinIndex[LenuSnsch];i++)
    {
        fCarCos = cosf(GstrPSLDeteData.fRecObj2OdoAngle[LenuSnsch][i]);
        fCarSin = sinf(GstrPSLDeteData.fRecObj2OdoAngle[LenuSnsch][i]);
        PubAI_UpdateCarOdoCoor(GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][i].fObjX,GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][i].fObjY,fCarSin,fCarCos);                
        PubAI_TransObjCarCoorToOdoCoor(&fCarSnsCoor.fObjX,&fCarSnsCoor.fObjY,&SnsOdoCoor[i].fObjX,&SnsOdoCoor[i].fObjY);                    
    }
}
/******************************************************************************
* 函数名称: PSLProObj2MinData
* 
* 功能描述: 
* 
* 输入参数:
* 
* 输出参数:无 
* 
* 返回值:无 
* 
* 其它说明:无 
* 
* 修改日期              版本号        修改人          修改内容  
* 2023-9-18 11:00    V0.1      22866        初次发布
******************************************************************************/
void PSLProObj2MinData(PSLSnsChannelType LenuSnsch)
{
    uint8 i = 0;
    float fOnePointx = 0.0;
    float fOnePointy = 0.0;
    float fTwoPointx = 0.0;
    float fTwoPointy = 0.0;
    float fObj2MoveDis = 0.0;
    uint8 u8Obj2CntEnd = GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch];
    if(GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch] >= 3)
    {
        fOnePointx = GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][0].fObjX;
        fOnePointy = GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][0].fObjY;
        for(i = 1;i < GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch];i++)
        {
            fTwoPointx = GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][i].fObjX;
            fTwoPointy = GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][i].fObjY;
            fObj2MoveDis = PubAI_CalTwoPointDis(fOnePointx,fOnePointy,fTwoPointx,fTwoPointy);
            if(fObj2MoveDis > 3000)
            {
                u8Obj2CntEnd = i;
                break;
            }
        }

        GstrPSLDeteData.fSecObjMinDis[LenuSnsch] = GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][0];
        GstrPSLDeteData.u8Obj2MinIndex[LenuSnsch] = 0;
        for(i = 1;i < u8Obj2CntEnd;i++)
        {
           if(GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][i] < GstrPSLDeteData.fSecObjMinDis[LenuSnsch])
           {
                if(i + 1 < u8Obj2CntEnd)
                {
                    if(ABS(GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][i],GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][i + 1]) <= PSL_ECHO_12CM_VALUE)
                    {
                        GstrPSLDeteData.fSecObjMinDis[LenuSnsch] = GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][i];
                        GstrPSLDeteData.fSecObjMinDisX[LenuSnsch] = GstrPSLDeteData.strObj2Data[LenuSnsch][i].fObjX;
                        GstrPSLDeteData.fSecObjMinDisY[LenuSnsch] = GstrPSLDeteData.strObj2Data[LenuSnsch][i].fObjY;
                        GstrPSLDeteData.u8Obj2MinIndex[LenuSnsch] = i;
                    }
                }
                else
                {
                    GstrPSLDeteData.fSecObjMinDis[LenuSnsch] = GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][i];
                    GstrPSLDeteData.fSecObjMinDisX[LenuSnsch] = GstrPSLDeteData.strObj2Data[LenuSnsch][i].fObjX;
                    GstrPSLDeteData.fSecObjMinDisY[LenuSnsch] = GstrPSLDeteData.strObj2Data[LenuSnsch][i].fObjY;
                    GstrPSLDeteData.u8Obj2MinIndex[LenuSnsch] = i;
                }

            }

        }
       
        if(GstrPSLDeteData.u8Obj2MinIndex[LenuSnsch] >= 1)
        {
            i = GstrPSLDeteData.u8Obj2MinIndex[LenuSnsch] - 1;
          
            if(i >= PSL_RECORD_OBJ_MAX - 2)
            {
                i = PSL_RECORD_OBJ_MAX - 2;
            }
            
           
            for(;i >= 1;i--)
            {
                if(i + 1 < u8Obj2CntEnd)
                {
                    if(GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][i] > GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][i - 1])
                    {
                        GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][i] = (GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][i - 1] + GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][i + 1]) / 2;
                    }
                }
            }
            i = GstrPSLDeteData.u8Obj2MinIndex[LenuSnsch];
            if(i + 1 < PSL_RECORD_OBJ_MAX)
            {
                if(i + 1 < u8Obj2CntEnd)
                {
                    GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][i] = (GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][i - 1] + GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][i + 1] + GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][i]) / 3;
                    GstrPSLDeteData.fSecObjMinDis[LenuSnsch] = GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][i];
                }
                else
                {
                    //GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][i] = (GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][i - 1] + GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][i]) / 2;
                    GstrPSLDeteData.fSecObjMinDis[LenuSnsch] = GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][i];
                }
            }
        }
    }
}
bool PSLCalObjSquareShapeType(float *Rawvalue,uint8 u8len)
{
    uint8 i = 0;
    for(i = 0;i < u8len - 1;i++)
    {
        if(ABS(Rawvalue[i],Rawvalue[i + 1]) > PSL_ECHO_10CM_VALUE)
        {
            return FALSE;
        }
    }
    
    if(ABS(Rawvalue[0],Rawvalue[u8len - 1]) < PSL_ECHO_10CM_VALUE && u8len < 10)
    {
        return TRUE;
    }
    return FALSE;
}
/******************************************************************************
* 函数名称: PSLCalSlotCornerEnd
* 
* 功能描述: 
* 
* 输入参数:
* 
* 输出参数:无 
* 
* 返回值:无 
* 
* 其它说明:无 
* 
* 修改日期              版本号        修改人          修改内容  
* 2023-6-20 08:00    V0.1      22866        初次发布
******************************************************************************/
void PSLCalSlotCornerEnd(PSLSnsChannelType LenuSnsch)
{
    uint8 i = 0;
    uint8 u8TargetCnt = 0;
    float fCarCos = 0.0;
    float fCarSin = 0.0;
    uint8 u8CalibrationCnt = 0;    
    float fCalibrationEcho = 0.0;
    float fEchoSum = 0.0;
    float fObj2StartDis = 0.0;
    float fObj2StartDisCos = 0.0;
    float fObj2StartDisSin = 0.0;
    float fObjEchoDisCos = 0.0;
    float fObjEchoDisSin = 0.0;
    float fObj2SnsCoorx = 0.0;
    float fObj2SnsCoory = 0.0;
    
    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;
    PSLObjCoorType fCarSnsCoor;
    PSLObjCoorType strSnsOdoCoor[PSL_RECORD_OBJ_MAX];

 
    if(GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch] > 3)
    {
        if(PSLCalObjSquareShapeType(&GstrPSLDeteData.fObj2RawData[LenuSnsch][2],GstrPSLDeteData.u8RecObj2RawCnt[LenuSnsch] - 2) == TRUE)
        {
            GstrPSLDeteData.LenuSecObjShape[LenuSnsch] = OBJ_SHAPE_SQUARE;
        }
        else
        {
            GstrPSLDeteData.LenuSecObjShape[LenuSnsch] = OBJ_SHAPE_ROUND;
        }
    }
    PSLProObj2MinData(LenuSnsch);
    if(GstrPSLDeteData.u8Obj2MinIndex[LenuSnsch] >= 1)
    {     
        PSLCalObj2SnsOdoCoor(LenuSnsch,&strSnsOdoCoor[0]);

		fCalibrationEcho = GstrPSLDeteData.fSecObjMinDis[LenuSnsch] + 200;

        if(GstrPSLDeteData.fSecObjMinDis[LenuSnsch] < 500)
        {
            u8CalibrationCnt = 0;
        }
        else
        {
            u8CalibrationCnt = (GstrPSLDeteData.fSecObjMinDis[LenuSnsch] - 500) / 50;
            if(u8CalibrationCnt > PSL_RECORD_OBJ_MAX - 1)
            {
                u8CalibrationCnt = PSL_RECORD_OBJ_MAX - 1;
            }
        }
        for(i = GstrPSLDeteData.u8Obj2MinIndex[LenuSnsch];i > 0 ;i--)
        {
            if(GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][i] > fCalibrationEcho)
            {
                u8TargetCnt = i;
                break;
            }
        }
        
        if(i == 0)
        {
            u8TargetCnt = 0;
            if((GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][0] + GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][1]) > (fCalibrationEcho * 2))
            {
                u8TargetCnt ++;
            }
        }
        else
        {
            if(u8TargetCnt < PSL_RECORD_OBJ_MAX - 1)
            {
                if((GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][u8TargetCnt] + GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][u8TargetCnt + 1]) > (fCalibrationEcho * 2))
                {
                    u8TargetCnt ++;
                }
            }
        }
        
        if(GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][u8TargetCnt] > fCalibrationEcho)
        {
            fEchoSum = GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][u8TargetCnt] - fCalibrationEcho;
            fObj2StartDis = u16ObjTwoCalibration[u8CalibrationCnt] + fEchoSum;

        }
        else
        {   

			if(GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][u8TargetCnt] > (GstrPSLDeteData.fSecObjMinDis[LenuSnsch] + 130))	
            {
                fEchoSum = fCalibrationEcho - GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][u8TargetCnt];
                            
              
                   
                if(u16ObjTwoCalibration[u8CalibrationCnt] > fEchoSum)
                {
                    fObj2StartDis = -(u16ObjTwoCalibration[u8CalibrationCnt] - fEchoSum);
                }
            }
            else if(GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][u8TargetCnt] > (GstrPSLDeteData.fSecObjMinDis[LenuSnsch] + 65))
            {
                fEchoSum = fCalibrationEcho - GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][u8TargetCnt];                   
                if(u16ObjTwoCalibration[u8CalibrationCnt] > fEchoSum)
                {
                    fObj2StartDis = -(u16ObjTwoCalibration[u8CalibrationCnt] - fEchoSum);
                }
                else
                {
                    fObj2StartDis = -fEchoSum;
                }                                                      
            }
            else
            {
                fEchoSum = fCalibrationEcho - GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][u8TargetCnt];                           
                fObj2StartDis =  - fEchoSum;
            }
            if(GstrPSLDeteData.fSecObjMinDis[LenuSnsch] > 1100)
            {
                fObj2StartDis += 100;
            }
        }

        fObj2StartDisCos = fObj2StartDis * cosf(GstrPSLDeteData.fRecObj2OdoAngle[LenuSnsch][u8TargetCnt]);
        fObj2StartDisSin = fObj2StartDis * sinf(GstrPSLDeteData.fRecObj2OdoAngle[LenuSnsch][u8TargetCnt]);
       
       
        GstrPSLDeteData.fSecObjCornerX[LenuSnsch] = GstrPSLDeteData.strObj2Data[LenuSnsch][u8TargetCnt].fObjX + fObj2StartDisCos;
        GstrPSLDeteData.fSecObjCornerY[LenuSnsch] = GstrPSLDeteData.strObj2Data[LenuSnsch][u8TargetCnt].fObjY + fObj2StartDisSin;
        if(GstrPSLDeteData.LenuSecObjShape[LenuSnsch] == OBJ_SHAPE_SQUARE)
        {
            if(GstrPSLDeteData.fSecObjMinDis[LenuSnsch] > 1100)
            {
                GstrPSLDeteData.fSecObjCornerX[LenuSnsch] += 200 * cosf(GstrPSLDeteData.fRecObj2OdoAngle[LenuSnsch][u8TargetCnt]);
                GstrPSLDeteData.fSecObjCornerY[LenuSnsch] += 200 * sinf(GstrPSLDeteData.fRecObj2OdoAngle[LenuSnsch][u8TargetCnt]);
            }
            else
            {
                GstrPSLDeteData.fSecObjCornerX[LenuSnsch] += 250 * cosf(GstrPSLDeteData.fRecObj2OdoAngle[LenuSnsch][u8TargetCnt]);
                GstrPSLDeteData.fSecObjCornerY[LenuSnsch] += 250 * sinf(GstrPSLDeteData.fRecObj2OdoAngle[LenuSnsch][u8TargetCnt]); 
            }
        }
        PSL_DEBUG_PRINTF("Obj2CornerX:%.1f,Obj2CornerY:%.1f,MinDis:%.1f\r\n", GstrPSLDeteData.fSecObjCornerX[LenuSnsch], GstrPSLDeteData.fSecObjCornerY[LenuSnsch],GstrPSLDeteData.fSecObjMinDis[LenuSnsch]);
        PSL_DEBUG_PRINTF("Odox:%.1f Odoy:%.1f ch:%d\r\n",LpStrPSLUseOdo->fCarCoorX,LpStrPSLUseOdo->fCarCoorY,LenuSnsch);
        PSLUpdateDetermineSlot(LenuSnsch);
    }
    else
    {
        if((GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch] >= 3 && GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch] <= 8) \
            || ((ABS(GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][0],GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][1]) < PSL_ECHO_10CM_VALUE)
            && (ABS(GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][0],GstrPSLDeteData.fRecObj2EchoDis[LenuSnsch][2]) < PSL_ECHO_10CM_VALUE))
            || (GstrPSLDeteData.LenuSlotObjType[LenuSnsch] == PSL_SLOT_ONLYOBJ2 && GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch] >= 5))
        {
          
            GstrPSLDeteData.fSecObjCornerX[LenuSnsch] = GstrPSLDeteData.strObj2Data[LenuSnsch][0].fObjX + 200 * cosf(GstrPSLDeteData.fRecObj2OdoAngle[LenuSnsch][0]);
            GstrPSLDeteData.fSecObjCornerY[LenuSnsch] = GstrPSLDeteData.strObj2Data[LenuSnsch][0].fObjY + 200 * sinf(GstrPSLDeteData.fRecObj2OdoAngle[LenuSnsch][0]);

            PSLUpdateDetermineSlot(LenuSnsch);
        }
        else
        {
        }
    }
}
/******************************************************************************
* 函数名称: PSLAnalysisObj2TableData
* 
* 功能描述: 
* 
* 输入参数:
* 
* 输出参数:无 
* 
* 返回值:无 
* 
* 其它说明:无 
* 
* 修改日期              版本号        修改人          修改内容  
* 2023-11-29 11:00    V0.1      22866        初次发布
******************************************************************************/
void PSLAnalysisObj2TableData(PSLSnsChannelType LenuSnsch)
{
    PSLDetermineDataType *LpPSLDetData;
    LpPSLDetData = &GstrPSLDeteData;
    uint8 i,j;
    float fFirstEcho = 0.0;
    float fSecondEcho = 0.0;
    float fThirdEcho = 0.0;
    float fSumEcho = 0.0;
    uint8 u8DelCnt = 0xFF;
    for(i = 0;i < LpPSLDetData->u8RecordObj2Cnt[LenuSnsch];i++)
    {        
        GstrPSLDeteData.fObj2RawData[LenuSnsch][i] = LpPSLDetData->fRecObj2EchoDis[LenuSnsch][i];
    }
    GstrPSLDeteData.u8RecObj2RawCnt[LenuSnsch] = LpPSLDetData->u8RecordObj2Cnt[LenuSnsch];
    if(LpPSLDetData->u8RecordObj2Cnt[LenuSnsch] >= 5)
    {
        for(i = 0;i < LpPSLDetData->u8RecordObj2Cnt[LenuSnsch] - 1;i++)
        {
            fFirstEcho = LpPSLDetData->fRecObj2EchoDis[LenuSnsch][i];
            fSecondEcho  = LpPSLDetData->fRecObj2EchoDis[LenuSnsch][i + 1];
            fSumEcho = ABS(fFirstEcho,fSecondEcho);
            if(fSumEcho > PSL_ECHO_20CM_VALUE)
            {
                if(fSumEcho > PSL_ECHO_50CM_VALUE)
                {
                    u8DelCnt = i + 1;
                }
                else if(i + 2  < LpPSLDetData->u8RecordObj2Cnt[LenuSnsch])
                {
                    fThirdEcho =  LpPSLDetData->fRecObj2EchoDis[LenuSnsch][i + 2];
                    if(fFirstEcho > fSecondEcho)
                    {                            
                        if(fSecondEcho < fThirdEcho)
                        {
                            u8DelCnt = i + 1;
                        }
              
                    }
                    else
                    {
                        if(fSecondEcho > (fThirdEcho + PSL_ECHO_20CM_VALUE))
                        {
                            u8DelCnt = i + 1;
                        }
                        else
                        {
                            if(i == 0)
                            {
                                u8DelCnt = i;
                            }
                                                        
                        }
                    }
                }

                if(u8DelCnt != 0xFF)
                {
                    for(j = u8DelCnt;j < LpPSLDetData->u8RecordObj2Cnt[LenuSnsch];j++)
                    {
                        LpPSLDetData->strObj2Data[LenuSnsch][j].fObjX = LpPSLDetData->strObj2Data[LenuSnsch][j + 1].fObjX;
                        LpPSLDetData->strObj2Data[LenuSnsch][j].fObjY = LpPSLDetData->strObj2Data[LenuSnsch][j + 1].fObjY;
                        LpPSLDetData->strObj2OdoCoor[LenuSnsch][j].fObjX = LpPSLDetData->strObj2OdoCoor[LenuSnsch][j + 1].fObjX;
                        LpPSLDetData->strObj2OdoCoor[LenuSnsch][j].fObjY = LpPSLDetData->strObj2OdoCoor[LenuSnsch][j + 1].fObjY;
                        LpPSLDetData->fRecObj2EchoDis[LenuSnsch][j] = LpPSLDetData->fRecObj2EchoDis[LenuSnsch][j + 1];
                        LpPSLDetData->fRecObj2OdoAngle[LenuSnsch][j] = LpPSLDetData->fRecObj2OdoAngle[LenuSnsch][j + 1];                                       
                    }
                    LpPSLDetData->u8RecordObj2Cnt[LenuSnsch]--;
                    u8DelCnt = 0xFF;
                }
                
            }
        }
    }
}

/******************************************************************************
  * 函数名称: PSLUpdateHaveObj2Slot
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-3-14 10:40    V0.1      22866        初次发布
  ******************************************************************************/
void PSLUpdateHaveObj2Slot(PSLSnsChannelType LenuSnsch)
{
    uint8 u8CalObj2Flag = 0;
    float LfP1_X = 0.0;
    float LfP1_Y = 0.0;
    float LfP2_X = 0.0;
    float LfP2_Y = 0.0;
    float LfP3_X = 0.0;
    float LfP3_Y = 0.0;
    float fObj2StartDis = 0.0;
    float fObj2EndDis = 0.0;
    float fCurEchoDis = 0.0;
    static uint8 u8Obj2InvalidCnt = 0;
    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;
    PSLRecordObjCoorType *LpPSLRecordObj;
    LpPSLRecordObj = &GstrPSLRecordObjCoor;
    
    PSLRecordOObj2Data(LenuSnsch,&fCurEchoDis);
    

    if(fCurEchoDis > (GstrPSLDeteData.fSecObjMinDis[LenuSnsch] + 600))
    {
        u8Obj2InvalidCnt++;
    }
    else
    {
        u8Obj2InvalidCnt = 0;
    }
    LfP1_X = LpStrPSLUseOdo->fCarCoorX;
    LfP1_Y = LpStrPSLUseOdo->fCarCoorY;
    LfP2_X = GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch] - 1].fObjX;
    LfP2_Y = GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch] - 1].fObjY;
    LfP3_X = GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][0].fObjX;
    LfP3_Y = GstrPSLDeteData.strObj2OdoCoor[LenuSnsch][0].fObjY;
    fObj2EndDis = PubAI_CalTwoPointDis(LfP2_X,LfP2_Y,LfP1_X,LfP1_Y);
    fObj2StartDis = PubAI_CalTwoPointDis(LfP3_X,LfP3_Y,LfP1_X,LfP1_Y);
    if(GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch] >= PSL_RECORD_OBJ_MAX - 1)
    {
        u8CalObj2Flag = 1;
    }
    else if((fObj2EndDis > PSL_DET_OBJ2_END_DIS) && (GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch] >= 3))
    {
        u8CalObj2Flag = 2;
    }
    else if((fObj2StartDis > PSL_DET_OBJ2_START_DIS) && (GstrPSLDeteData.u8RecordObj2Cnt[LenuSnsch] >= 8))
    {
        u8CalObj2Flag = 3;
    }
    else if(u8Obj2InvalidCnt >= 3)
    {
        u8CalObj2Flag = 4;
    }

    if(u8CalObj2Flag > 0)
    {
        //PSLCalObjAndCurbSlope(LenuSnsch,PSL_CAL_SLOPE_OBJ2);
        //PSLAnalysisSlopeData(LenuSnsch,PSL_CAL_SLOPE_OBJ2);
        PSLAnalysisObj2TableData(LenuSnsch);
        PSLCalObjSlope(LenuSnsch,PSL_CAL_SLOPE_OBJ2);
        PSLCalSlotCornerEnd(LenuSnsch);
        GstrPSLDeteData.enuPSLDetSts[LenuSnsch] = PSL_DETE_OBJ1;
        if(fCurEchoDis != 0)
        {
            GstrPSLDeteData.fFirstObjTrailX[LenuSnsch] = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_FIFTH].fObjX;
            GstrPSLDeteData.fFirstObjTrailY[LenuSnsch] = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][PSL_DETE_FIFTH].fObjY;
        }
        else
        {
            GstrPSLDeteData.fFirstObjTrailX[LenuSnsch] = GstrPSLDeteData.fSecObjMinDisX[LenuSnsch];
            GstrPSLDeteData.fFirstObjTrailY[LenuSnsch] = GstrPSLDeteData.fSecObjMinDisY[LenuSnsch];
        }
        PSLClearObj2RecordData(LenuSnsch);
        GstrPSLDeteData.fRecordCarSlope[LenuSnsch][PSL_CAL_SLOPE_OBJ1] = LpStrPSLUseOdo->fCarYawAngle;
        
    }
}

/******************************************************************************
  * 函数名称: PSLDeteObj2Handle
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-3-14 10:40    V0.1      22866        初次发布
  ******************************************************************************/
void PSLDeteObj2Handle(PSLSnsChannelType LenuSnsch)
{
    if(GstrPSLDeteData.LenuSlotObjType[LenuSnsch] == PSL_SLOT_ONLYOBJ1)
    {
        //PSLUpdateOnlyObj1Slot(LenuSnsch);
        GstrPSLDeteData.enuPSLDetSts[LenuSnsch] = PSL_DETE_END;
    }
    else
    {
        PSLUpdateHaveObj2Slot(LenuSnsch);
    }
}
/******************************************************************************
  * 函数名称: PSLDeteSlotWorkStsObj2
  * 
  * 功能描述: 
  * 
  * 输入参数:
  * 
  * 输出参数:无 
  * 
  * 返回值:无 
  * 
  * 其它说明:无 
  * 
  * 修改日期              版本号        修改人          修改内容  
  * 2023-3-14 9:30    V0.1      22866        初次发布
  ******************************************************************************/
void PSLDeteSlotWorkStsObj2(PSLSnsChannelType LenuSnsch)
{
   PSLRecordObjCoorType *LpPSLRecordObj;
   LpPSLRecordObj = &GstrPSLRecordObjCoor;
   static uint8 u8backSTDCnt = 0;
   
   if(LpPSLRecordObj->enuMeasMode[LenuSnsch][PSL_DETE_FIFTH] == PSL_SNS_MEAS_STD)
   {
        if(LpPSLRecordObj->u16MasterDis[LenuSnsch][PSL_DETE_FIFTH] < PSL_SNS_MEAS_CHIRP_MIN)
        {
            u8backSTDCnt++;
            if(u8backSTDCnt >= 0xAA)
            {
               u8backSTDCnt = 0xAA;
            }
        }
        else
        {
            u8backSTDCnt = 0;
        }
   }
   else
   {
        if(u8backSTDCnt >= 2)
        {

            return;
        }
   }
    if(GstrPSLDeteData.LenuSlotObjType[LenuSnsch] != PSL_SLOT_ONLYOBJ1)
    {
        PSLRecordOdoAndObj1Data(LenuSnsch);
        PSLRecordObjInvalidData(LenuSnsch);
    }
 
    PSLDeteObj2Handle(LenuSnsch);         
}

/******************************************************************************
 * 函数名称: PSLDetermineSlot
 * 
 * 功能描述: 
 * 
 * 输入参数:
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-2-25 16:28    V0.1      22866        初次发布
 ******************************************************************************/
void PSLDetermineSlot(PSLSnsChannelType LenuSnsch)
{
    //PSL_DEBUG_PRINTF("Det:%d\r\n",GstrPSLDeteData.enuPSLDetSts[LenuSnsch]);
    switch(GstrPSLDeteData.enuPSLDetSts[LenuSnsch])
    {
        case PSL_DETE_INIT:
            PSLDeteSingleChannelInit(LenuSnsch);
        break;
        case PSL_DETE_IDLE:
            PSLDeteSlotWorkStsIdle(LenuSnsch);
        break;

        case PSL_DETE_OBJ1:
            PSLDeteSlotWorkStsObj1(LenuSnsch);
        break;

        case PSL_DETE_CURB:
            PSLDeteSlotWorkStsCurb(LenuSnsch);
        break;

        case PSL_DETE_OBJ2:
            PSLDeteSlotWorkStsObj2(LenuSnsch);
        break;

        case PSL_DETE_END:
            GstrPSLDeteData.enuPSLDetSts[LenuSnsch] = PSL_DETE_INIT;
        break;

        default:

        break;

    }
}


/******************************************************************************
 * 函数名称: PSLAlgorithmHandle
 * 
 * 功能描述: 
 * 
 * 输入参数:
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-2-24 11:28    V0.1      22866        初次发布
 ******************************************************************************/

void PSLAlgorithmHandle(PSLSnsChannelType LenuSnsch)
{
    PSLObjPositionCoorType *LpPSLOBjData;
    LpPSLOBjData = &GstrPSLObjCoorData;

    PSLRecordObjCoorType *LpPSLRecordObj;
    LpPSLRecordObj = &GstrPSLRecordObjCoor;

    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;
    uint8 i;
	float fCarCos = 0.0;
    float fCarSin = 0.0;
    float fPSLOdoLeftObjx = 0.0;
    float fPSLOdoLeftObjy = 0.0;
    float fPSLOdoMasterObjx = 0.0;
    float fPSLOdoMasterObjy = 0.0;
    float fPSLOdoRightObjx = 0.0;
    float fPSLOdoRightObjy = 0.0;
    if(LpPSLOBjData->u8SnsActiveFlag[LenuSnsch] == 1)
    {
        LpPSLOBjData->u8SnsActiveFlag[LenuSnsch] = 0;
        fCarCos = LpStrPSLUseOdo->fCarCosYawAngle;
        fCarSin = LpStrPSLUseOdo->fCarSinYawAngle;
        PubAI_UpdateCarOdoCoor(LpStrPSLUseOdo->fCarCoorX,LpStrPSLUseOdo->fCarCoorY,fCarSin,fCarCos);
        if(LpPSLOBjData->strSnsLeftObj[LenuSnsch].fObjX != 0)
        {
            PubAI_TransObjCarCoorToOdoCoor(&LpPSLOBjData->strSnsLeftObj[LenuSnsch].fObjX,&LpPSLOBjData->strSnsLeftObj[LenuSnsch].fObjY,&fPSLOdoLeftObjx,&fPSLOdoLeftObjy);
        }
        else
        {
            fPSLOdoLeftObjx = 0.0;
            fPSLOdoLeftObjy = 0.0;
        }
        
        if(LpPSLOBjData->strSnsMasterObj[LenuSnsch].fObjX != 0)
        {
            PubAI_TransObjCarCoorToOdoCoor(&LpPSLOBjData->strSnsMasterObj[LenuSnsch].fObjX,&LpPSLOBjData->strSnsMasterObj[LenuSnsch].fObjY,&fPSLOdoMasterObjx,&fPSLOdoMasterObjy);
        }
        else
        {
            fPSLOdoMasterObjx = 0.0;
            fPSLOdoMasterObjy = 0.0;
        }

        if(LpPSLOBjData->strSnsRightObj[LenuSnsch].fObjX != 0)
        {
            PubAI_TransObjCarCoorToOdoCoor(&LpPSLOBjData->strSnsRightObj[LenuSnsch].fObjX,&LpPSLOBjData->strSnsRightObj[LenuSnsch].fObjY,&fPSLOdoRightObjx,&fPSLOdoRightObjy);
        }
        else
        {
            fPSLOdoRightObjx = 0.0;
            fPSLOdoRightObjy = 0.0;
        }
        
        for(i = 0;i < PSL_DETE_FIFTH;i++)
        {
            LpPSLRecordObj->strSnsLeftObj[LenuSnsch][i].fObjX = LpPSLRecordObj->strSnsLeftObj[LenuSnsch][i + 1].fObjX;
            LpPSLRecordObj->strSnsLeftObj[LenuSnsch][i].fObjY = LpPSLRecordObj->strSnsLeftObj[LenuSnsch][i + 1].fObjY;
            LpPSLRecordObj->strSnsMasterObj[LenuSnsch][i].fObjX = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][i + 1].fObjX;
            LpPSLRecordObj->strSnsMasterObj[LenuSnsch][i].fObjY = LpPSLRecordObj->strSnsMasterObj[LenuSnsch][i + 1].fObjY;
            LpPSLRecordObj->strSnsRightObj[LenuSnsch][i].fObjX = LpPSLRecordObj->strSnsRightObj[LenuSnsch][i + 1].fObjX;
            LpPSLRecordObj->strSnsRightObj[LenuSnsch][i].fObjY = LpPSLRecordObj->strSnsRightObj[LenuSnsch][i + 1].fObjY;
            LpPSLRecordObj->u16MasterDis[LenuSnsch][i] = LpPSLRecordObj->u16MasterDis[LenuSnsch][i + 1];
            LpPSLRecordObj->u16MasterHeight[LenuSnsch][i] = LpPSLRecordObj->u16MasterHeight[LenuSnsch][i + 1];
            LpPSLRecordObj->enuMeasMode[LenuSnsch][i] = LpPSLRecordObj->enuMeasMode[LenuSnsch][i + 1];
        }
        LpPSLRecordObj->strSnsLeftObj[LenuSnsch][i].fObjX = fPSLOdoLeftObjx;
        LpPSLRecordObj->strSnsLeftObj[LenuSnsch][i].fObjY = fPSLOdoLeftObjy;
        LpPSLRecordObj->strSnsMasterObj[LenuSnsch][i].fObjX = fPSLOdoMasterObjx;
        LpPSLRecordObj->strSnsMasterObj[LenuSnsch][i].fObjY = fPSLOdoMasterObjy;
        LpPSLRecordObj->strSnsRightObj[LenuSnsch][i].fObjX = fPSLOdoRightObjx;
        LpPSLRecordObj->strSnsRightObj[LenuSnsch][i].fObjY = fPSLOdoRightObjy;
        LpPSLRecordObj->u16MasterDis[LenuSnsch][i] = LpPSLOBjData->u16MasterDis[LenuSnsch];
        LpPSLRecordObj->u16MasterHeight[LenuSnsch][i] = LpPSLOBjData->u16MasterHeight[LenuSnsch];
        LpPSLRecordObj->enuMeasMode[LenuSnsch][i] = LpPSLOBjData->enuMeasMode[LenuSnsch];
	#if PSL_FILL_OBJ2_DATA
        for(i = 0;i < 2;i++)
        {
            PSLRecordOdoCoor[LenuSnsch][i].fObjX = PSLRecordOdoCoor[LenuSnsch][i + 1].fObjX;
            PSLRecordOdoCoor[LenuSnsch][i].fObjY = PSLRecordOdoCoor[LenuSnsch][i + 1].fObjY;
            PSLRecordOdoAngle[LenuSnsch][i] = PSLRecordOdoAngle[LenuSnsch][i + 1];
        }
        PSLRecordOdoCoor[LenuSnsch][i].fObjX = LpStrPSLUseOdo->fCarCoorX;
        PSLRecordOdoCoor[LenuSnsch][i].fObjY = LpStrPSLUseOdo->fCarCoorY;
        PSLRecordOdoAngle[LenuSnsch][i] = LpStrPSLUseOdo->fCarYawAngle;
     #endif   
        PSLDetermineSlot(LenuSnsch);
    }
  
}



/******************************************************************************
 * 函数名称: PSLUpdateValidSlot
 * 
 * 功能描述: PSL更新有效车位
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-6-15 14:25    V0.1       22866        初次发布
 ******************************************************************************/
void PSLUpdateValidSlot(PSLSnsChannelType LenuSnsch)
{
    PSLCalSlotType(LenuSnsch);
    if((GstrPSLDeteData.LenuSlotType[LenuSnsch] == PSL_SLOT_RIGHT_HORIZONTAL
        || GstrPSLDeteData.LenuSlotType[LenuSnsch] == PSL_SLOT_LEFT_HORIZONTAL)
        && (GstrPSLDeteData.u16SlotWidth[LenuSnsch] == GstrPSL_APPPara_Ram.u16SlotWidthMax_H)
        && GstrPSLDeteData.u16CurbDepth[LenuSnsch] > PSL_SLOT_DEPTH_ERROR)
    {
        return;
    }
    if((GstrPSLDeteData.LenuSlotType[LenuSnsch] == PSL_SLOT_RIGHT_HORIZONTAL
        || GstrPSLDeteData.LenuSlotType[LenuSnsch] == PSL_SLOT_LEFT_HORIZONTAL)
        && GstrPSLDeteData.u16CurbDepth[LenuSnsch] > PSL_SLOT_DEPTH_MAX)
    {
        GstrPSLDeteData.u16CurbDepth[LenuSnsch] = PSL_SLOT_DEPTH_MAX;
    }

    if(GstrPSLDeteData.LenuSlotType[LenuSnsch] == PSL_SLOT_LEFT_VERTICAL || GstrPSLDeteData.LenuSlotType[LenuSnsch] == PSL_SLOT_RIGHT_VERTICAL)
    {
        GstrPSLDeteData.fObj1Slope[LenuSnsch] = 0;
        GstrPSLDeteData.fObj2Slope[LenuSnsch] = 0;
    }
    
    if(GstrPSLDeteData.LenuSlotObjType[LenuSnsch] == PSL_SLOT_ONLYOBJ2)
    {
        GstrPSLDeteData.fObj1Slope[LenuSnsch] = 0;
    }
    
    if(GstrPSLDeteData.u16SlotWidth[LenuSnsch] == GstrPSL_APPPara_Ram.u16SlotWidthMax_H
        && GstrPSLDeteData.u16CurbDepth[LenuSnsch] == PSL_SLOT_DEPTH_MAX)
    {
        return;
    }
        
    if(GsPSLOutputSlotData.wSlotCnt < PSL_OUTPUT_SLOT_MAX && GstrPSLDeteData.LenuSlotType[LenuSnsch] != PSL_SLOT_NONE)    
        PSLSingleSnsOutputSlot(LenuSnsch);
}


/******************************************************************************
 * 函数名称: PSLUpdateCarCoor
 * 
 * 功能描述: PSL更新车辆当前Odo数据
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-3-1 17:25    V0.1       22866        初次发布
 ******************************************************************************/
void PSLUpdateCarCoor(void)
{
    PSLUseOdoType *LpStrPSLUseOdo;
    uint16 Lu16CarSpd;
    CPOS_CurCarPointDataType LstrCurCarPointData;
    
    LpStrPSLUseOdo = &GstrPSLUseOdoData;

    if(LpStrPSLUseOdo->u8PSLUseOdoID == 0xFF)
    {
        LpStrPSLUseOdo->u8PSLUseOdoID = CPOSSgnMag_ReadGetCalId();
        if(LpStrPSLUseOdo->u8PSLUseOdoID != 0xFF)
        {
            PSL_DEBUG_PRINTF("PSL Get ID Success,ID:%d\r\n",LpStrPSLUseOdo->u8PSLUseOdoID);
        }
    }
    else
    {
        LpStrPSLUseOdo->u8PSLUseOdoInit = TRUE;
        CPOSSgnMag_ReadCarCoorData(&LstrCurCarPointData,LpStrPSLUseOdo->u8PSLUseOdoID);
        LpStrPSLUseOdo->fCarCoorX = LstrCurCarPointData.fX;
        LpStrPSLUseOdo->fCarCoorY = LstrCurCarPointData.fY;
        LpStrPSLUseOdo->fCarYawAngle = LstrCurCarPointData.fAngle;
        LpStrPSLUseOdo->fCarSinYawAngle = sinf(LpStrPSLUseOdo->fCarYawAngle);
        LpStrPSLUseOdo->fCarCosYawAngle = cosf(LpStrPSLUseOdo->fCarYawAngle);
    }  
}


/******************************************************************************
 * 函数名称: PSLResetOdoCoor
 * 
 * 功能描述: PSL重置ODO坐标系
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-6-12 11:25    V0.1       22866        初次发布
 ******************************************************************************/
void PSLResetOdoCoor(void)
{
    PSLUseOdoType *LpStrPSLUseOdo;
	LpStrPSLUseOdo = &GstrPSLUseOdoData;
    //PSL_DEBUG_PRINTF("Coor Reset\r\n");
    CPOSSgnMag_WriteClrCalId(LpStrPSLUseOdo->u8PSLUseOdoID);
    PSLDeteSlotDataInit();
}


/******************************************************************************
 * 函数名称: PSLAlgorithm_Task
 * 
 * 功能描述: 
 * 
 * 输入参数:
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-2-24 11:28    V0.1      22866        初次发布
 ******************************************************************************/
//uint32 SumTimer = 0;

void PSLAlgorithm_Task(void)
{
#if 1
    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;
    PSLSnsChannelType LenuPSLSnsch = PSL_SNS_CH_FLS;
    Car_GearType LenuCarGear = CAN_CAR_GEAR_P;
#if 0
    uint32 Lu32synctimeStart = 0;
    uint32 Lu32synctimeEnd = 0;
    
    Lu32synctimeStart = GetSystemTimeCnt_Ms();
    SumTimerStart = Lu32synctimeStart;
#endif
    ReadCAN_AppSignal_Car_Gear(&LenuCarGear);

    ApaSignal_PSLWorkStatusType LenuPSL_WorkState;
    /*读取PSL工作状态*/
	PSLSignal_ReadPSLWorkState(&LenuPSL_WorkState);

    
    PSLUpdateCarCoor();
    PSLUpdateRawCoor();
    if(LenuCarGear == CAN_CAR_GEAR_D && LpStrPSLUseOdo->u8PSLUseOdoInit == TRUE)
    {
        if(LenuPSL_WorkState == PSL_Work_Searching)
        {
            for(LenuPSLSnsch = PSL_SNS_CH_FLS;LenuPSLSnsch < PSL_SNS_CH_RLS;LenuPSLSnsch++)
            {
                PSLAlgorithmHandle(LenuPSLSnsch);
            }
        }
    }
    else if(LenuCarGear == CAN_CAR_GEAR_R)
    {
        for(LenuPSLSnsch = PSL_SNS_CH_FLS;LenuPSLSnsch < PSL_SNS_CH_NUM;LenuPSLSnsch++)
        {
            GstrPSLDeteData.enuPSLDetSts[LenuPSLSnsch] = PSL_DETE_INIT;
        }
    }
    PSLSlotOutputManage();
    PSLCoordinateSwitch();
    PSLStateManageMain();
#if 0
    Lu32synctimeEnd = GetSystemTimeCnt_Ms();
    SumTimerEnd = Lu32synctimeEnd;

    SumTimer = Lu32synctimeEnd - Lu32synctimeStart;
    PSL_DEBUG_PRINTF("%d\r\n",SumTimer);
#endif
#endif
}
