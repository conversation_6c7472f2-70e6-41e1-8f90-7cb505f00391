/*
 * AK2_MCU_Drv.h
 *
 *  Created on: 2023年11月29日
 *      Author: 6000023281
 */

#ifndef AK2_MCU_DRV_H_
#define AK2_MCU_DRV_H_

#include "DSI3_SPI.h"
/* 外部头文件*/
#include "IOHal.h"
#include "CrcDrv.h"
#include "DMADrv.h"
#include "TimerManage.h"
#include "CAN_AppSignalManage.h"
#include "PowerSingalManage.h"
#include "debug.h"

#define _LITTLE_ENDIAN_
/**
 * \defgroup   DSISPI_CFG
 * @{
 */
#ifdef __cplusplus
extern "C"{
#endif

/******************************************************************************/
/**
 * @brief              开发错误检测
 */
/*****************************************************************************/
#if (DSISPI_ERROR_DETECT==STD_ON)
    #define DSISPI_ASSERT(x) DEV_ASSERT(x)
#else
    #define DSISPI_ASSERT(x) ((void)0)
#endif

/******************************************************************************/
/**
 * @brief   打印
 */
/******************************************************************************/
#if (DSISPI_USE_DEBUG_PRINT==STD_ON)
#define DSISPI_DEBUG_PRINTF              PRINTF
#else
#define DSISPI_DEBUG_PRINTF(...)         (void)0
#endif

/*************************************宏定义***********************************/
#define DSI_MASTER0_RESB_PIN_SET \
	do{R_PORT_SetGpioOutput(Port9, 6, High);}while(0)
		
#define DSI_MASTER0_RESB_PIN_CLEAR \
	do{R_PORT_SetGpioOutput(Port9, 6, Low);}while(0)
		
#define DSI_MASTER0_INTB_IN_PIN \
	R_PORT_GetLevel(Port10, 9);
		
#define DSI_MASTER0_RFC_IN_PIN \
	R_PORT_GetLevel(Port8, 6);
		
#define DSI_MASTER0_DCR1B_EnInt	\
	do{INTC2.ICP2.BIT.MKP2 = _INT_PROCESSING_ENABLED;}while(0)

#define DSI_MASTER0_DCR2B_EnInt	\
	do{INTC2.ICP8.BIT.MKP8 = _INT_PROCESSING_ENABLED;}while(0)

#define DSI_MASTER0_DCR1B_DisInt \
	do{INTC2.ICP2.BIT.MKP2 = _INT_PROCESSING_DISABLED;}while(0)
		
#define DSI_MASTER0_DCR2B_DisInt \
	do{INTC2.ICP8.BIT.MKP8 = _INT_PROCESSING_DISABLED;}while(0)
		
#define DSI_MASTER1_RESB_PIN_SET \
	do{R_PORT_SetGpioOutput(Port9, 5, High);}while(0)
		
#define DSI_MASTER1_RESB_PIN_CLEAR \
	do{R_PORT_SetGpioOutput(Port9, 5, Low);}while(0)

#define DSI_MASTER1_INTB_IN_PIN \
	R_PORT_GetLevel(Port10, 13);
		
#define DSI_MASTER1_RFC_IN_PIN \
	R_PORT_GetLevel(Port8, 7);

#define DSI_MASTER1_DCR1B_EnInt	\
    do{INTC2.ICP7.BIT.MKP7 = _INT_PROCESSING_ENABLED;}while(0)

#define DSI_MASTER1_DCR2B_EnInt	\
	do{INTC2.ICP11.BIT.MKP11 = _INT_PROCESSING_ENABLED;}while(0)
		
#define DSI_MASTER1_DCR1B_DisInt \
    do{INTC2.ICP7.BIT.MKP7 = _INT_PROCESSING_DISABLED;}while(0)
		
#define DSI_MASTER1_DCR2B_DisInt \
	do{INTC2.ICP11.BIT.MKP11 = _INT_PROCESSING_DISABLED;}while(0)

#define AK2_PORT_SET(a, b) R_PORT_SetGpioOutput(a, b)

/**********************************************************************
 * Macros
 **********************************************************************/
#ifndef AD_CALIBRATION_1_5V
    #define AD_CALIBRATION_1_5V						(130U)
    #define AD_CALIBRATION_2_5V						(330U)
    #define AD_CALIBRATION_6V						(1040U) 
    #define AD_CALIBRATION_6_5V					    (1125U)	
    #define AD_CALIBRATION_7V					    (1225U)	
    #define AD_CALIBRATION_7_5V					    (1330U)	
    #define AD_CALIBRATION_8V						(1435U)		
    #define AD_CALIBRATION_8_5V					    (1535U)	
    #define AD_CALIBRATION_8_8V					    (1590U)	
    #define AD_CALIBRATION_9V						(1658U)
    #define AD_CALIBRATION_9_2V					    (1698U)	
    #define AD_CALIBRATION_9_5V					    (1760U)	
    #define AD_CALIBRATION_10V					    (1860U)
    #define AD_CALIBRATION_11V					    (2065U)
    #define AD_CALIBRATION_12V					    (2265U)	
    #define AD_CALIBRATION_13V					    (2468U)	
    #define AD_CALIBRATION_14V					    (2675U)
    #define AD_CALIBRATION_15V					    (2880U)
    #define AD_CALIBRATION_15_5V					(2980U)		
    #define AD_CALIBRATION_15_8V					(3045U)		
    #define AD_CALIBRATION_16V					    (3140U)
    #define AD_CALIBRATION_16_2V					(3180U)	
    #define AD_CALIBRATION_16_5V					(3200U)		
    #define AD_CALIBRATION_17V					    (3300U)		 
    #define AD_CALIBRATION_17_5V					(3410U)		
    #define AD_CALIBRATION_18V					    (3510U)
    #define AD_CALIBRATION_18_5V					(3600U)
    #define AD_CALIBRATION_19V					    (3700U)
    #define AD_CALIBRATION_19_5V					(3800U)
    #define AD_CALIBRATION_20V					    (3900U)
#endif

/* 外部结构体定义 */
typedef Signal_VoltageStatusType AK2_Signal_VoltageStatusType;
typedef Signal_PowerManageGroupType AK2_Signal_PowerManageGroupType;
typedef Car_AmbientTempType AK2_Car_AmbientTempType;
typedef SnsPwrDiagStatus_e AK2_SnsPwrDiagStatus_e;
typedef SnsPowerDiagIdx_e AK2_SnsPowerDiagIdx_e;

/******************************************************************************
* 设计描述 : 定义滤波上限、下限，滤波步进
*******************************************************************************/
typedef struct
{
	uint32 UpperLimit;
	uint32 LowerLimit;
	uint32 UpStep;
	uint32 DownStep;
	uint32 Count;
	bool   Input;
	bool   Output;
}AK2_Pub_FilterCtrl_t;

/******************************************************************************/
/**
 *  @brief IO配置
 */
/******************************************************************************/
typedef struct
{
    enum port_t  port;
    uint8   pin;
}IO_Ctrl_Str;

/******************************************************************************/
/**
 *  @brief 探头电源管脚状态
 */
/******************************************************************************/
typedef enum
{
    eSNS_GROUP1_POWER_EN_PIN = 0,
    eSNS_GROUP2_POWER_EN_PIN,
    eSNS_GROUP3_POWER_EN_PIN, 
    eCTRL_PIN_Num,
}eIOPin_Ctrl;

/*************************************函数声明***********************************/
/** @brief DSI Master0 SPI异步传输接口 */
extern DSISPI_TransReturn_en DSIMaster0_Spi_AsyncTransmitFunc(const uint8 * sendBuffer,uint8 * receiveBuffer,uint16 transferByteCount);
/** @brief DSI Master1 SPI异步传输接口 */
extern DSISPI_TransReturn_en DSIMaster1_Spi_AsyncTransmitFunc(const uint8 * sendBuffer,uint8 * receiveBuffer,uint16 transferByteCount);
/** @brief DSI521.42 CRC16计算 */
extern uint16 DSI_SPI_CRC16Calculate(const uint8 *LcDataPtr,uint16 LwDataLen);
/** @brief 获取系统1ms时钟 */
extern uint32 AK2_GetSys1msTickFunc(void);
/** @brief 获取系统1.6us时钟 */
extern uint32 AK2_GetSys1p6usTickFunc(void);
/** @brief 获取CAN车外部温度 */
extern AK2_Car_AmbientTempType AK2_ReadCAN_AppSignal_Car_AmbientTempType(void);
/** @brief 获取供电电压状态 */
extern AK2_Signal_VoltageStatusType AK2_ReadPwrManage_GroupStatus(void);
/** @brief 读Sensor电压诊断状态 */
extern void AK2_ReadPwrManage_SnsPwrDiagStatus(AK2_SnsPowerDiagIdx_e LenuIdx, AK2_SnsPwrDiagStatus_e *pLenuPwrSts);
/** @brief 滤波函数 */
extern void AK2_PubAI_Filter(AK2_Pub_FilterCtrl_t *pCtrl);
/** @brief 清除滤波数据 */
extern void AK2_PubAI_ClrFilterData(AK2_Pub_FilterCtrl_t *pCtrl);
/** @brief 探头电源控制接口 */
extern void AK2_WriteEnSnsPin(uint8 value);

extern uint8 DSIMaster0_EnDCR1BFallInt(void);
extern uint8 DSIMaster0_EnDCR2BFallInt(void);
extern uint8 DSIMaster0_DisDCR1BInt(void);
extern uint8 DSIMaster0_DisDCR2BInt(void);
extern uint8 DSIMaster0_EnRFCInt(void);
extern uint8 DSIMaster0_EnINTBInt(void);
extern uint8 DSIMaster0_DisRFCInt(void);
extern uint8 DSIMaster0_DisINTBInt(void);
extern uint8 DSIMaster0_ReadINTB_Pin(void);
extern uint8 DSIMaster0_ReadRFC_Pin(void);
extern uint8 DSIMaster0_SetRESB_Pin(void);
extern uint8 DSIMaster0_ClearRESB_Pin(void);
extern uint8 DSIMaster1_EnDCR1BFallInt(void);
extern uint8 DSIMaster1_EnDCR2BFallInt(void);
extern uint8 DSIMaster1_DisDCR1BInt(void);
extern uint8 DSIMaster1_DisDCR2BInt(void);
extern uint8 DSIMaster1_EnRFCInt(void);
extern uint8 DSIMaster1_EnINTBInt(void);
extern uint8 DSIMaster1_DisRFCInt(void);
extern uint8 DSIMaster1_DisINTBInt(void);
extern uint8 DSIMaster1_ReadINTB_Pin(void);
extern uint8 DSIMaster1_ReadRFC_Pin(void);
extern uint8 DSIMaster1_SetRESB_Pin(void);
extern uint8 DSIMaster1_ClearRESB_Pin(void);

/******************************************************************************/
/**
 * @brief   临界区开关中断
 */
/******************************************************************************/
#define DSI_DisableInterrupts()     DI()
#define DSI_EnableInterrupts()      EI()


#endif /* DSI3_SPI_CFG_H_ */
