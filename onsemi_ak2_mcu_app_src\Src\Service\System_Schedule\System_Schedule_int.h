/******************************************************************************
 * @file       System_Schedule_int.h
 * @brief      
 * @date       2025-03-04 13:42:45
 * <AUTHOR>
 * @copyright  Longhorn Automotive Electronics Technology (Xiamen) Co., Ltd.
 *****************************************************************************/


#ifndef __SYSTEM_SCHEDULE_INT__
#define __SYSTEM_SCHEDULE_INT__
/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "System_Schedule_types.h"



/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/
#define SCHED_MAX_TASK_DURATION       10u



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/



/******************************************************************************
 * @Const Declaration
 *****************************************************************************/



/******************************************************************************
 * @Variable Declaration
 *****************************************************************************/
extern volatile uint8 u8TickCounter;



/******************************************************************************
 * @Function Declaration
 *****************************************************************************/
void System_Sched_vidInit(void);
void System_Sched_Management(void);
void Sched_TickCounterHandler(void);



#endif 
