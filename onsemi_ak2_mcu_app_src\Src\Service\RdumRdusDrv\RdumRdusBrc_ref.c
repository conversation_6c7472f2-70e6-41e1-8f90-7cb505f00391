/******************************************************************************
 * @file      RdumRdusBrc.c
 * @brief     安森美超声波探头BRC命令实现
 * <AUTHOR>
 * @date      2025-05-16
 * @note
 *****************************************************************************/

/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include <string.h>
#include <stdio.h>
#include "RdumRdusBrc.h"
#include "SpiCmd.h"
#include "BaseDrv.h"



/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/
#define GET_ALL_BRC_CMD             0x07    /* 获取所有BRC命令 */
#define GET_PDCM_RESP_CMD           0x05    /* 获取PDCM响应命令 */
#define SPI_HEADER_SIZE             7       /* SPI头大小 */
#define SLOT_PACKAGE_WITH_CRC_SIZE  19      /* slot包数据大小（包含CRC） */
#define SLOT_DURATION_MS            4       /* 单个slot持续时间（毫秒），实际约为3.6毫秒，这里略微增加以确保足够的间隔 */



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/



/******************************************************************************
 * @Const Declaration
 *****************************************************************************/



/******************************************************************************
 * @Variable Declaration
 *****************************************************************************/



/******************************************************************************
 * @Function Definitions
 *****************************************************************************/

/******************************************************************************
 * @brief      获取所有BRC
 * @param[in]  Dsi3Ch DSI3通道
 * @param[in]  Dsi3Id 从机ID
 * @param[in]  SlotCount slot数量，有几个探头有回波数据就读取几个slot
 * @param[out] Result BRC结果
 * @return     BRC状态
 * <AUTHOR>
 * @date       2025-05-16
 * @note
 *****************************************************************************/
BrcStatus_t RdumRdusBrc_GetAllBrc(Dsi3Channel_t Dsi3Ch, uint8 SlotCount, BrcResult_t *Result)
{
    uint8 TxData[SPI_HEADER_SIZE + MAX_SLOT_COUNT * SLOT_DATA_SIZE + 1];  /* SPI头 + slot数据 + CRC */
    uint8 RxData[SPI_HEADER_SIZE + MAX_SLOT_COUNT * SLOT_DATA_SIZE + 1];  /* SPI头 + slot数据 + CRC */
    uint8 RetVal;
    uint8 i;
    uint8 TotalSize;
    SpiProtocol_t SpiTxData;
    SlotData_t TxSlotData[MAX_SLOT_COUNT];
    uint8 AllSlotDuration[MAX_SLOT_COUNT * 2];

    /* 参数检查 */
    if (Result == NULL)
    {
        return BRC_STATUS_INVALID;
    }

    /* 检查slot数量 */
    if (SlotCount == 0 || SlotCount > MAX_SLOT_COUNT)
    {
        return BRC_STATUS_INVALID;
    }

    /* 初始化结果 */
    memset(Result, 0, sizeof(BrcResult_t));
    Result->SlotCount = SlotCount;
    Result->Status = BRC_STATUS_OK;

    /* 计算总大小 */
    TotalSize = SPI_HEADER_SIZE + SlotCount * SLOT_DATA_SIZE + 1;

    /* 构建GET_ALL_BRC命令 */
    SpiTxData.ReadAndWrite_t.Kac = 0;
    SpiTxData.ReadAndWrite_t.Channel = Dsi3Ch;
    SpiTxData.ReadAndWrite_t.Command = GET_ALL_BRC_CMD;
    SpiTxData.ReadAndWrite_t.Addr = 0;
    // 以下值为固定，意义暂时未知
    SpiTxData.ReadAndWrite_t.ReadAndWrite_t.Payload.Bytes[3] = 0x07;
    SpiTxData.ReadAndWrite_t.ReadAndWrite_t.Payload.Bytes[2] = 0x38;
    SpiTxData.ReadAndWrite_t.ReadAndWrite_t.Payload.Bytes[1] = 0xA5;
    SpiTxData.ReadAndWrite_t.ReadAndWrite_t.Payload.Bytes[0] = 0xA1;
    /* 计算SPI头CRC */
    TxData[SPI_HEADER_SIZE - 1] = BaseDrv_Crc8Calculate(TxData, 6, CRC8_C2_INIT_SPI_AND_CRC_SD);

    /* 填充slot数据（随机数据），默认发送数据不异或 */
    for (i = 0; i < SlotCount; i++)
    {
        TxSlotData[i].Kac = 0;
        TxSlotData[i].Status = 0;
        TxSlotData[i].SlotDuration[0] = 0xA6;
        TxSlotData[i].SlotDuration[1] = 0x0C;
        memset(TxSlotData[i].SlotData, 0, sizeof(TxSlotData[i].SlotData));
        TxSlotData[i].SlotCrc = BaseDrv_Crc8Calculate(&TxSlotData[i], SLOT_DATA_SIZE - sizeof(TxSlotData[i].SlotDuration) - 1, CRC8_C2_INIT_TABLE[i]);
        memcpy(&TxData[SPI_HEADER_SIZE + i * SLOT_DATA_SIZE], &TxSlotData[i], SLOT_DATA_SIZE);
        AllSlotDuration[i * 2] = TxSlotData[i].SlotDuration[0];
        AllSlotDuration[i * 2 + 1] = TxSlotData[i].SlotDuration[1];
    }

    /* 计算最后的CRC_SD:为所有SlotCount中的SlotDuration一起计算*/
    TxData[TotalSize - 1] = BaseDrv_Crc8Calculate(AllSlotDuration, SlotCount * 2, CRC8_C2_INIT_SPI_AND_CRC_SD);

    /* 发送命令 */
    RetVal = SpiCom_AsyncTransferEx(TxData, TotalSize, 100, 0, 3, NULL, RxData);
    if (RetVal == SPI_TX_SEQ_NULL)
    {
        Result->Status = BRC_STATUS_ERROR;
        PRINTF_RDUM("发送SPI数据失败！\n");
        return BRC_STATUS_ERROR;
    }

#ifdef DEBUG_RDUM_RDUS
    PRINTF_RDUM("发送的SPI数据:\n");
    for (i = 0; i < TotalSize; i++)
    {
        PRINTF_RDUM("0x%02X ", TxData[i]);
    }
    PRINTF_RDUM("\n");

    PRINTF_RDUM("接收的SPI数据:\n");
    for (i = 0; i < TotalSize; i++)
    {
        PRINTF_RDUM("0x%02X ", RxData[i]);
    }
    PRINTF_RDUM("\n");
#endif

    /* 验证返回的前7个字节返回的系统状态是否正常 */
    // SpiRespDef_ParseNonReadResp(RxData, &Result->NonReadResp);
    // if (Result->NonReadResp.Bit.SPIERR)
    // {
    //     Result->Status = BRC_STATUS_ERROR;
    //     return BRC_STATUS_ERROR;
    // }

    /* 复制slot数据 */
    for (i = 0; i < SlotCount; i++)
    {
        memcpy(&Result->SlotData[i], &RxData[SPI_HEADER_SIZE + i * SLOT_DATA_SIZE], SLOT_DATA_SIZE);
        AllSlotDuration[i * 2] = Result->SlotData[i].SlotDuration[0];
        AllSlotDuration[i * 2 + 1] = Result->SlotData[i].SlotDuration[1];
    }

    /* 复制SlotSD数据CRC */
    Result->SlotSDCrc = RxData[TotalSize - 1];

    /* 验证SlotSD数据CRC */
    if (Result->SlotSDCrc != BaseDrv_Crc8Calculate(AllSlotDuration, SlotCount * 2, CRC8_C2_INIT_SPI_AND_CRC_SD))
    {
        Result->Status = BRC_STATUS_ERROR;
        PRINTF_RDUM("SlotSD数据CRC验证失败！\n");
        return BRC_STATUS_ERROR;
    }

    Result->SlotCount = SlotCount;
    Result->Status = BRC_STATUS_OK;

    /* 解码BRC结果 */
    return RdumRdusBrc_DecodeBrcResult(Result);
}

/******************************************************************************
 * @brief      解码BRC结果
 * @param[in,out] Result BRC结果
 * @return     BRC状态
 * <AUTHOR>
 * @date       2025-05-16
 * @note
 *****************************************************************************/
BrcStatus_t RdumRdusBrc_DecodeBrcResult(BrcResult_t *Result)
{
    uint8 i;
    uint8 DecodedData[SLOT_PACKAGE_SIZE];
    uint8 RetVal;

    /* 参数检查 */
    if (Result == NULL)
    {
        return BRC_STATUS_INVALID;
    }

    /* 解码每个slot */
    for (i = 0; i < Result->SlotCount; i++)
    {
        /* 解码slot数据 */
        RetVal = BrcSlotDef_DecodeSlot(i, (uint8 *)&Result->SlotData[i], DecodedData);
        if (RetVal != 0)
        {
            Result->Status = BRC_STATUS_ERROR;
            PRINTF_RDUM("解码slot%d数据失败！\n", i);
            return BRC_STATUS_ERROR;
        }

#ifdef DEBUG_RDUM_RDUS
        PRINTF_RDUM("解码后slot%d数据:\n", i);
        for (uint8 j = 0; j < SLOT_PACKAGE_SIZE; j++)
        {
            PRINTF_RDUM("0x%02X ", DecodedData[j]);
        }
        PRINTF_RDUM("\n");
#endif

        /* 把解码后数据存入slot数据 */
        memcpy((uint8 *)&Result->SlotData[i], DecodedData, SLOT_PACKAGE_SIZE);
        // Result->SlotData[i].Kac = DecodedData[0] >> 4;
        // Result->SlotData[i].Status = DecodedData[0] & 0x0F;
        // Result->SlotData[i].SlotCrc = DecodedData[SLOT_PACKAGE_SIZE - 1];
        if (Result->SlotData[i].Status != 0)
        {
            PRINTF_RDUM("slot%d状态错误！\n", i);
            return BRC_STATUS_ERROR;
        }

        /* 解压缩包络点位(这里不解压缩，需要后面接收所有数据后再解压缩) */
        // RetVal = BrcSlotDef_DecompressEnvelope(&DecodedData[1], &Result->DecompressedEnvelopes[i]);
        // if (RetVal != 0)
        // {
        //     Result->Status = BRC_STATUS_ERROR;
        //     return BRC_STATUS_ERROR;
        // }
    }

    return BRC_STATUS_OK;
}

/******************************************************************************
 * @brief      获取解压缩后的包络
 * @param[in]  Result BRC结果
 * @param[in]  SlotIndex slot索引
 * @param[out] DecompressedEnvelope 解压缩后的包络
 * @return     BRC状态
 * <AUTHOR>
 * @date       2025-05-16
 * @note
 *****************************************************************************/
BrcStatus_t RdumRdusBrc_GetDecompressedEnvelope(BrcResult_t *Result, uint8 SlotIndex, DecompressedEnvelope_t *DecompressedEnvelope)
{
    /* 参数检查 */
    if (Result == NULL || DecompressedEnvelope == NULL)
    {
        return BRC_STATUS_INVALID;
    }

    /* 检查slot索引 */
    if (SlotIndex >= Result->SlotCount)
    {
        return BRC_STATUS_INVALID;
    }

    /* 复制解压缩后的包络 */
    // memcpy(DecompressedEnvelope, &Result->DecompressedEnvelopes[SlotIndex], sizeof(DecompressedEnvelope_t));

    return BRC_STATUS_OK;
}

/******************************************************************************
 * @brief      打印BRC结果
 * @param[in]  Result BRC结果
 * <AUTHOR>
 * @date       2025-05-16
 * @note
 *****************************************************************************/
void RdumRdusBrc_PrintBrcResult(BrcResult_t *Result)
{
    uint8 i, j;

    /* 参数检查 */
    if (Result == NULL)
    {
        return;
    }

    /* 打印基本信息 */
    PRINTF_RDUM("BRC结果:\n");
    PRINTF_RDUM("slot数量: %d\n", Result->SlotCount);
    PRINTF_RDUM("状态: %d\n", Result->Status);

    /* 打印每个slot的信息 */
    for (i = 0; i < Result->SlotCount; i++)
    {
        PRINTF_RDUM("Slot %d:\n", i + 1);
        PRINTF_RDUM("  KAC: %d\n", Result->SlotData[i].Kac);
        PRINTF_RDUM("  状态: %d\n", Result->SlotData[i].Status);
        PRINTF_RDUM("  slot持续时间: 0x%02X%02X\n", Result->SlotData[i].SlotDuration[0], Result->SlotData[i].SlotDuration[1]);
        PRINTF_RDUM("  slot数据:\n");
        for (j = 0; j < SLOT_PACKAGE_SIZE - 2; j++)
        {
            PRINTF_RDUM("    0x%02X ", Result->SlotData[i].SlotData[j]);
            if ((j + 1) % 8 == 0)
            {
                PRINTF_RDUM("\n");
            }
        }
        PRINTF_RDUM("\n");
    }
}

/******************************************************************************
 * @brief      获取单个PDCM响应
 * @param[in]  Dsi3Ch DSI3通道
 * @param[in]  Dsi3Id 从机ID
 * @param[in]  SlotIndex slot索引（0-7）
 * @param[out] DecompressedEnvelope 解压缩后的包络
 * @return     BRC状态
 * <AUTHOR>
 * @date       2025-05-16
 * @note       GET_PDCM_RESP命令用于读取单探头单个回波slot
 *****************************************************************************/
BrcStatus_t RdumRdusBrc_GetPdcmResp(Dsi3Channel_t Dsi3Ch, uint8 SlotIndex, DecompressedEnvelope_t *DecompressedEnvelope)
{
    uint8 TxData[SPI_HEADER_SIZE + SLOT_PACKAGE_SIZE + 1];  /* SPI头 + slot包数据 + CRC */
    uint8 RxData[SPI_HEADER_SIZE + SLOT_PACKAGE_SIZE + 1];  /* SPI头 + slot包数据 + CRC */
    uint8 DecodedData[SLOT_PACKAGE_SIZE];
    uint8 RetVal;
    uint8 TotalSize;
    SpiNonReadResp_t NonReadResp;

    /* 参数检查 */
    if (DecompressedEnvelope == NULL)
    {
        return BRC_STATUS_INVALID;
    }

    /* 检查slot索引 */
    if (SlotIndex >= MAX_SLOT_COUNT)
    {
        return BRC_STATUS_INVALID;
    }

    /* 计算总大小 */
    TotalSize = SPI_HEADER_SIZE + SLOT_PACKAGE_SIZE + 1;

    /* 构建GET_PDCM_RESP命令 */
    TxData[0] = Dsi3Ch | GET_PDCM_RESP_CMD;
    TxData[1] = SlotIndex;  /* Addr字段复用为需要读取的slot编号 */
    TxData[2] = 0x00;
    TxData[3] = 0x00;
    TxData[4] = 0x00;
    TxData[5] = 0x00;

    /* 计算SPI头CRC */
    TxData[6] = BaseDrv_Crc8Calculate(TxData, 6);

    /* 填充slot包数据（随机数据） */
    memset(&TxData[SPI_HEADER_SIZE], 0, SLOT_PACKAGE_SIZE);

    /* 计算slot包数据CRC */
    TxData[TotalSize - 1] = BaseDrv_Crc8Calculate(&TxData[SPI_HEADER_SIZE], SLOT_PACKAGE_SIZE);

    /* 发送命令 */
    RetVal = SpiCom_AsyncTransferEx(TxData, TotalSize, 100, 0, 3, NULL, RxData);
    if (RetVal == SPI_TX_SEQ_NULL)
    {
        return BRC_STATUS_ERROR;
    }

    /* 解析非读操作响应 */
    SpiRespDef_ParseNonReadResp(RxData, &NonReadResp);

    /* 检查响应状态 */

    /* 解码slot数据（使用slot1的异或掩码） */
    RetVal = BrcSlotDef_DecodeSlot(0, &RxData[SPI_HEADER_SIZE], DecodedData);
    if (RetVal != 0)
    {
        return BRC_STATUS_ERROR;
    }

    /* 解压缩包络点位 */
    RetVal = BrcSlotDef_DecompressEnvelope(&DecodedData[1], DecompressedEnvelope);
    if (RetVal != 0)
    {
        return BRC_STATUS_ERROR;
    }

    return BRC_STATUS_OK;
}