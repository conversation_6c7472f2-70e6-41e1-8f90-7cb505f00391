#include "string.h"
#include <stdarg.h>
#include <stdio.h>
#include "debug.h"
#include "AppQueue.h"
#include "DebugCommand.h"
#include "UartHal.h"

typedef enum
{
    UART_DEBUG_IDLE = 0u,
    UART_DEBUGING,
}eUartDebugType;

#define PRINT_LOCK_WAIT_TIME_US             (20)
#define PRINT_LOCK_CNT_MAX                  (PRINT_LOCK_WAIT_TIME_US*100u)

/**************************************Typedef*************************************/
typedef struct
{
    eUartDebugType DebugState;
    uint16 OutIdx;
    uint16 InIdx;
    uint8 DataBuf[200];
} DebugRxInfoType;

#if(DEBUG_PRINT_EN == STD_ON)
/********************************Static Function Define*****************************/
static void DebugInfoHandle(uint8 * data, uint16 len);
static inline boolean Debug_IsModuleEn(uint16 ModuleIdx);

/**********************************Global Vailable**********************************/

static uint8 Gu8DebugRxBuf[150]={0};
static DebugRxInfoType GstrRxInfo = 
{
    .DebugState = UART_DEBUG_IDLE,
    .OutIdx = 0u,
    .InIdx = 0u,
};

static uint16 Gu16aModulePrintEnArray[8] = 
{
    0xFFFFu, 0xFFFFu, 0xFFFFu, 0xFFFFu, 0xFFFFu, 0xFFFFu, 0xFFFFu, 0xFFFFu
};

/*************************Module Debug Print Define******************************/
const char BackSpaceStr[] = {'\b',' ','\b'};
#define BACKSPACE_LEN                 (sizeof(BackSpaceStr)/sizeof(BackSpaceStr[0]))
#define SYS_TIME_STR_LEN                        8u
/**********************************Global Function**********************************/

void debug_log(uint16 ModuleID, const char *ModuleName, const char *log, ...)
{
    char printf_buf[512]={0};
    char log_str[512] = {0};
    va_list valog;
    int printed;
    if(FALSE == Debug_IsModuleEn(ModuleID))
    {
        return;
    }
    va_start(valog, log);
    vsnprintf(printf_buf, sizeof(printf_buf), log, valog);
    va_end(valog);
    printed = snprintf(log_str, sizeof(log_str), "[%s]%s", ModuleName, printf_buf);
    if(printed > 0)
    {
        (void)AppQueue_Enqueue(QUEUE_UART_PRINT, (uint8 *)log_str, (uint16)printed);
        UartTriggerTx();
    }
}

void ScanDebugRx(void)
{
    uint8 RxLen;
    RxLen = (uint8)AppQueue_GetQueueSize(QUEUE_UART_DEBUG_RX);
    if(RxLen > 0u)
    {
        AppQueue_Dequeue(QUEUE_UART_DEBUG_RX, Gu8DebugRxBuf, RxLen);
        DebugInfoHandle(Gu8DebugRxBuf, RxLen);
    }
}

void DebugExitFunc(void)
{
    GstrRxInfo.DebugState = UART_DEBUG_IDLE;
    GstrRxInfo.OutIdx = 0u;
    GstrRxInfo.InIdx = 0u;
}

void Debug_SetModulePrintEn(uint16 ModuleIdx, boolean en)
{
    uint16 BytePos = 0u;
    uint16 BitPos = 0u;
    if(ModuleIdx < 128u)
    {
        BytePos = (ModuleIdx >> 4u);
        BitPos = (ModuleIdx&0xFu);
        if(en)
        {
            Gu16aModulePrintEnArray[BytePos] |= (1u << BitPos);
        }
        else
        {
            Gu16aModulePrintEnArray[BytePos] &= (~(1u << BitPos));
        }
    }
}

/**********************************Local Function**********************************/
static inline boolean Debug_IsModuleEn(uint16 ModuleIdx)
{
    boolean Rst = FALSE;
    uint16 BytePos = 0u;
    uint16 BitPos = 0u;
    if(ModuleIdx < 128u)
    {
        BytePos = (ModuleIdx >> 4u);
        BitPos = (ModuleIdx&0xF);
        Rst = ((Gu16aModulePrintEnArray[BytePos] >> BitPos)&0x01u);
        if(GstrRxInfo.DebugState == UART_DEBUGING)
        {
            Rst = FALSE;
        }
    }
    else
    {
        Rst = TRUE;
    }
    
    return Rst;
}

static void DebugInfoHandle(uint8 * data, uint16 len)
{
    uint16 i = 0u;
    boolean DebugStartFlag = FALSE;
    uint16 LenForSearch = 0u;
    /* Is ECU enter into debug mode */
    if(UART_DEBUG_IDLE == GstrRxInfo.DebugState)
    {
        if(len >= 2u)
        {
            for(i = 1u; i < len; i++)
            {
                if((0x0D == data[i-1])&&(0x0A == data[i]))
                {
                    GstrRxInfo.DebugState = UART_DEBUGING;
                    GstrRxInfo.InIdx = 0u;
                    GstrRxInfo.OutIdx = 0u;
                    DebugStartFlag = TRUE;
                    break;
                }
            }
        }
    }
    /* Handle data in debug mode */
    if(UART_DEBUGING == GstrRxInfo.DebugState)
    {
        /* copy data */
        if(FALSE == DebugStartFlag)
        {
            /* handle backspace */
            if(('\b' == data[0])&&(1u == len))
            {
                if(GstrRxInfo.InIdx > GstrRxInfo.OutIdx)
                {
                    GstrRxInfo.DataBuf[GstrRxInfo.InIdx] = '\0';
                    GstrRxInfo.InIdx--;
                }
                (void)AppQueue_Enqueue(QUEUE_UART_PRINT, (void *)BackSpaceStr, BACKSPACE_LEN);
            }
            else
            {
                for(; i < len; i++, GstrRxInfo.InIdx++)
                {
                    GstrRxInfo.DataBuf[GstrRxInfo.InIdx] = data[i];
                }
                /* info display back */
                (void)AppQueue_Enqueue(QUEUE_UART_PRINT, (uint8 *)data, (uint16)len);
            }
        }
        /* handle data to search related function */
        if((GstrRxInfo.InIdx - GstrRxInfo.OutIdx) >= 2u)
        {
            for(i = GstrRxInfo.OutIdx; i < GstrRxInfo.InIdx; i++)
            {
                if((0x0Du == GstrRxInfo.DataBuf[i-1])&&(0x0Au == GstrRxInfo.DataBuf[i]))
                {
                    LenForSearch = i - 1u - GstrRxInfo.OutIdx;
                    DebugCommandFind((char *)&GstrRxInfo.DataBuf[GstrRxInfo.OutIdx], LenForSearch);
                    GstrRxInfo.OutIdx = i+1u;
                }
            }
        }
    }
}

#endif

