/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * PDC_MEB_CalibPara: 
 * Created on: 2021-08-16 19:24
 * Original designer: <PERSON><PERSON><PERSON>
 ******************************************************************************/
#ifndef ODO_CalibPara_Types_H
#define ODO_CalibPara_Types_H
#include "types.h"
#include "CAN_AppSignal_CommonTypes.h"
#define ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM   10000      


/* 定义计算坐标所需要的数据 */
typedef struct
{
    float fOneWheelPulseDisAtRRForward;
    float fOneWheelPulseDisAtRLForward;
    float fOneWheelPulseDisAtRRBackward;
    float fOneWheelPulseDisAtRLBackward;
	float fFront_left_R;//左前进时的传感器方差
	float fFront_right_R;//右前进时的传感器方差
	float fRear_left_R;//左后退时的传感器方差
	float fRear_right_R;//右后退时的传感器方差
	float fYawVar;//横摆角的过程处理方差
	float fYawRateVar;//角速度的过程处理方差
	float fYaw_YawrateCov;//横摆角和角速度的过程处理协方差
	float fRear_wheel_axle_length;
}APA_Odom_CarParameterType;

extern APA_Odom_CarParameterType GstrAPA_OdomCarPara_Ram;


#endif /* end of ODO_CalibPara_Types_H */

