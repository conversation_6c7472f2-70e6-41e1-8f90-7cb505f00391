/******************************************************************************
 * @file      RdumRdusInitProcess.c
 * @brief     安森美超声波探头初始化流程实现
 * <AUTHOR>
 * @date      2025-05-20
 * @note      重构版本，使用表驱动方式实现
 *****************************************************************************/

/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "RdumRdusInitProcess.h"
#include "SpiCmd.h"
#include "BaseDrv.h"
#include "string.h"

/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/
#define INIT_PROCESS_TIMEOUT        1000    /* 初始化流程超时时间(ms) */
#define MAX_INIT_STEPS              500     /* 最大初始化步骤数 */
#define MAX_RETRY_COUNT             3       /* 最大重试次数 */

/* 命令类型定义 */
#define CMD_TYPE_NONE               0       /* 无命令 */
#define CMD_TYPE_WRITE              1       /* 写命令 */
#define CMD_TYPE_READ               2       /* 读命令 */
#define CMD_TYPE_NO_OPERATION       3       /* 空操作命令 */
#define CMD_TYPE_GET_CRM_RESP       4       /* 获取CRM响应命令 */

/******************************************************************************
 * @Type Definitions
 *****************************************************************************/
/* 初始化步骤类型 */
typedef enum
{
    STEP_TYPE_NONE = 0,                    /* 无操作 */
    STEP_TYPE_RDUM,                        /* RDUM操作 */
    STEP_TYPE_RDUS,                        /* RDUS操作 */
    STEP_TYPE_COMPLETE,                    /* 完成 */
} InitStepType_t;

/* 初始化步骤结构体 */
typedef struct
{
    InitStepType_t StepType;               /* 步骤类型 */
    uint8 CmdType;                         /* 命令类型 */
    uint8 Channel;                         /* 通道 */
    uint8 Address;                         /* 地址 */
    uint8 TxData[4];                       /* 发送数据 */
    uint8 DataLen;                         /* 数据长度 */
    const char* Description;               /* 描述 */
} InitStep_t;

/* 初始化流程控制结构体 */
typedef struct
{
    uint16 CurrentStepIndex;              /* 当前步骤索引 */
    RdumRdusInitProcessStatus_t Status;   /* 状态 */
    RdumRdusInitType_t InitType;          /* 初始化类型 */
    uint8 RetryCount;                     /* 重试次数 */
    uint32 StartTime;                     /* 开始时间 */
    uint32 StepStartTime;                 /* 步骤开始时间 */
    uint8 LoopCount;                      /* 循环计数 */
    uint8 RegValues[16];                  /* 寄存器值缓存 */
    uint8 Progress;                       /* 进度(0-100) */
} InitProcessControl_t;

/******************************************************************************
 * @Global Variables
 *****************************************************************************/
static InitProcessControl_t InitProcessCtrl;  /* 初始化流程控制结构体 */

/* RDUM初始化步骤表 */
static const InitStep_t RdumInitSteps[] = {
    /* 步骤类型        命令类型              通道          地址    数据                  数据长度  描述 */
    {STEP_TYPE_RDUM,   CMD_TYPE_NONE,        0,            0,      {0},                  0,        "RDUM初始化开始"},
    {STEP_TYPE_RDUM,   CMD_TYPE_WRITE,       DSI3_CHANNEL_A, 0x50, {0xFF, 0xFF, 0xFF, 0xFF}, 4,   "清除硬件故障标志"},
    {STEP_TYPE_RDUM,   CMD_TYPE_WRITE,       DSI3_CHANNEL_A, 0x08, {0x00, 0x00, 0x80, 0x00}, 4,   "电源控制寄存器"},
    {STEP_TYPE_RDUM,   CMD_TYPE_WRITE,       DSI3_CHANNEL_A, 0x51, {0x00, 0x75, 0x00, 0x00}, 4,   "清除状态标志"},
    {STEP_TYPE_RDUM,   CMD_TYPE_READ,        DSI3_CHANNEL_A, 0x11, {0},                  0,        "读取硬件故障标志寄存器"},
    {STEP_TYPE_RDUM,   CMD_TYPE_NO_OPERATION, DSI3_CHANNEL_A, 0x11, {0},                 0,        "获取硬件故障标志"},
    {STEP_TYPE_RDUM,   CMD_TYPE_READ,        DSI3_CHANNEL_A, 0x12, {0},                  0,        "读取状态寄存器"},
    {STEP_TYPE_RDUM,   CMD_TYPE_NO_OPERATION, DSI3_CHANNEL_A, 0x12, {0},                 0,        "获取状态"},
    {STEP_TYPE_RDUM,   CMD_TYPE_READ,        DSI3_CHANNEL_A, 0x13, {0},                  0,        "读取ADC值"},
    {STEP_TYPE_RDUM,   CMD_TYPE_NO_OPERATION, DSI3_CHANNEL_A, 0x13, {0},                 0,        "获取ADC值"},
    {STEP_TYPE_RDUM,   CMD_TYPE_READ,        DSI3_CHANNEL_A, 0x14, {0},                  0,        "读取寄存器14"},
    {STEP_TYPE_RDUM,   CMD_TYPE_NO_OPERATION, DSI3_CHANNEL_A, 0x14, {0},                 0,        "获取寄存器14值"},
    {STEP_TYPE_RDUM,   CMD_TYPE_READ,        DSI3_CHANNEL_A, 0x15, {0},                  0,        "读取寄存器15"},
    {STEP_TYPE_RDUM,   CMD_TYPE_NO_OPERATION, DSI3_CHANNEL_A, 0x15, {0},                 0,        "获取寄存器15值"},
    {STEP_TYPE_RDUM,   CMD_TYPE_READ,        DSI3_CHANNEL_A, 0x16, {0},                  0,        "读取寄存器16"},
    {STEP_TYPE_RDUM,   CMD_TYPE_NO_OPERATION, DSI3_CHANNEL_A, 0x16, {0},                 0,        "获取寄存器16值"},
    {STEP_TYPE_RDUM,   CMD_TYPE_READ,        DSI3_CHANNEL_A, 0x17, {0},                  0,        "读取设备ID"},
    {STEP_TYPE_RDUM,   CMD_TYPE_NO_OPERATION, DSI3_CHANNEL_A, 0x17, {0},                 0,        "获取设备ID"},
    {STEP_TYPE_RDUM,   CMD_TYPE_READ,        DSI3_CHANNEL_A, 0x18, {0},                  0,        "读取寄存器18"},
    {STEP_TYPE_RDUM,   CMD_TYPE_NO_OPERATION, DSI3_CHANNEL_A, 0x18, {0},                 0,        "获取寄存器18值"},
    {STEP_TYPE_RDUM,   CMD_TYPE_WRITE,       DSI3_CHANNEL_A, 0x00, {0x18, 0x01, 0x20, 0x32}, 4,   "配置寄存器0"},
    {STEP_TYPE_RDUM,   CMD_TYPE_WRITE,       DSI3_CHANNEL_A, 0x01, {0xC0, 0x38, 0x11, 0x03}, 4,   "配置寄存器1"},
    {STEP_TYPE_RDUM,   CMD_TYPE_WRITE,       DSI3_CHANNEL_A, 0x09, {0x00, 0x40, 0x00, 0x00}, 4,   "配置寄存器9"},
    {STEP_TYPE_RDUM,   CMD_TYPE_WRITE,       DSI3_CHANNEL_A, 0x02, {0x37, 0xF1, 0x00, 0x1A}, 4,   "配置寄存器2"},
    {STEP_TYPE_RDUM,   CMD_TYPE_WRITE,       DSI3_CHANNEL_A, 0x0C, {0x00, 0x00, 0x00, 0x00}, 4,   "配置寄存器C"},
    {STEP_TYPE_RDUM,   CMD_TYPE_WRITE,       DSI3_CHANNEL_A, 0x0E, {0x00, 0x00, 0x00, 0x00}, 4,   "配置寄存器E"},
    {STEP_TYPE_RDUM,   CMD_TYPE_WRITE,       DSI3_CHANNEL_A, 0x0B, {0x00, 0x00, 0x00, 0x00}, 4,   "配置寄存器B"}
};

/* RDUS初始化步骤表 - 基本配置 */
static const InitStep_t RdusInitSteps[] = {
    /* 步骤类型        命令类型              通道          地址    数据                  数据长度  描述 */
    {STEP_TYPE_RDUS,   CMD_TYPE_NONE,        0,            0,      {0},                  0,        "RDUS初始化开始"},
    {STEP_TYPE_RDUS,   CMD_TYPE_WRITE,       DSI3_CHANNEL_A, 0x03, {0xF8, 0x0A, 0x00, 0xD6}, 4,   "写读写指针"},
    {STEP_TYPE_RDUS,   CMD_TYPE_WRITE,       DSI3_CHANNEL_A, 0x50, {0xFF, 0xFF, 0xFF, 0xFF}, 4,   "清除硬件故障标志"},
    {STEP_TYPE_RDUS,   CMD_TYPE_WRITE,       DSI3_CHANNEL_A, 0x51, {0x00, 0x75, 0x00, 0x00}, 4,   "清除状态标志"},
    {STEP_TYPE_RDUS,   CMD_TYPE_WRITE,       DSI3_CHANNEL_A, 0x09, {0x00, 0x40, 0x00, 0x00}, 4,   "配置寄存器9"},
    {STEP_TYPE_RDUS,   CMD_TYPE_WRITE,       DSI3_CHANNEL_A, 0x02, {0x97, 0xF1, 0x00, 0x11}, 4,   "配置寄存器2"},
    {STEP_TYPE_RDUS,   CMD_TYPE_READ,        DSI3_CHANNEL_A, 0x12, {0},                  0,        "读取状态寄存器"},
    {STEP_TYPE_RDUS,   CMD_TYPE_NO_OPERATION, DSI3_CHANNEL_A, 0x12, {0},                 0,        "获取状态"},
    {STEP_TYPE_RDUS,   CMD_TYPE_WRITE,       DSI3_CHANNEL_A, 0x03, {0xF4, 0x00, 0x00, 0x00}, 4,   "写块1"},
    {STEP_TYPE_RDUS,   CMD_TYPE_WRITE,       DSI3_CHANNEL_A, 0x04, {0x00, 0x00, 0x00, 0x00}, 4,   "配置寄存器4(1)"},
    {STEP_TYPE_RDUS,   CMD_TYPE_WRITE,       DSI3_CHANNEL_A, 0x05, {0x00, 0x00, 0x00, 0x00}, 4,   "配置寄存器5(1)"},
    {STEP_TYPE_RDUS,   CMD_TYPE_WRITE,       DSI3_CHANNEL_A, 0x06, {0x00, 0xF8, 0x00, 0x00}, 4,   "配置寄存器6(1)"},
    {STEP_TYPE_RDUS,   CMD_TYPE_WRITE,       DSI3_CHANNEL_A, 0x09, {0x00, 0x40, 0x00, 0x00}, 4,   "配置寄存器9(1)"},
    {STEP_TYPE_RDUS,   CMD_TYPE_WRITE,       DSI3_CHANNEL_A, 0x02, {0xB7, 0xF1, 0x00, 0x1E}, 4,   "配置寄存器2(1)"},
    {STEP_TYPE_RDUS,   CMD_TYPE_READ,        DSI3_CHANNEL_A, 0x12, {0},                  0,        "读取状态寄存器(1)"},
    {STEP_TYPE_RDUS,   CMD_TYPE_NO_OPERATION, DSI3_CHANNEL_A, 0x12, {0},                 0,        "获取状态(1)"},
    {STEP_TYPE_RDUS,   CMD_TYPE_WRITE,       DSI3_CHANNEL_A, 0x03, {0xF8, 0x08, 0x0A, 0x22}, 4,   "最终写读写指针"},
    {STEP_TYPE_RDUS,   CMD_TYPE_WRITE,       DSI3_CHANNEL_A, 0x04, {0xF6, 0xFF, 0xFF, 0x00}, 4,   "最终配置寄存器4"},
    {STEP_TYPE_RDUS,   CMD_TYPE_WRITE,       DSI3_CHANNEL_A, 0x50, {0xFF, 0xFF, 0xFF, 0xFF}, 4,   "最终清除硬件故障标志"},
    {STEP_TYPE_RDUS,   CMD_TYPE_WRITE,       DSI3_CHANNEL_A, 0x51, {0x00, 0x75, 0x00, 0x00}, 4,   "最终清除状态标志"},
    {STEP_TYPE_RDUS,   CMD_TYPE_WRITE,       DSI3_CHANNEL_A, 0x09, {0x00, 0x40, 0x00, 0x00}, 4,   "最终配置寄存器9"},
    {STEP_TYPE_RDUS,   CMD_TYPE_WRITE,       DSI3_CHANNEL_A, 0x02, {0x97, 0xF1, 0x00, 0x12}, 4,   "最终配置寄存器2"},
    {STEP_TYPE_RDUS,   CMD_TYPE_READ,        DSI3_CHANNEL_A, 0x12, {0},                  0,        "最终读取状态寄存器"},
    {STEP_TYPE_RDUS,   CMD_TYPE_NO_OPERATION, DSI3_CHANNEL_A, 0x12, {0},                 0,        "最终获取状态"},
    {STEP_TYPE_RDUS,   CMD_TYPE_WRITE,       DSI3_CHANNEL_A, 0x03, {0xF3, 0x00, 0x00, 0xCC}, 4,   "地址发现"}
};

/* 完成标志步骤 */
static const InitStep_t CompleteStep = {
    STEP_TYPE_COMPLETE, CMD_TYPE_NONE, 0, 0, {0}, 0, "初始化完成"
};

/* 步骤表大小定义 */
#define RDUM_INIT_STEPS_SIZE      (sizeof(RdumInitSteps) / sizeof(InitStep_t))
#define RDUS_INIT_STEPS_SIZE      (sizeof(RdusInitSteps) / sizeof(InitStep_t))

/* 步骤表指针 */
static const InitStep_t* CurrentStepTable = NULL;
static uint16 CurrentStepTableSize = 0;

/******************************************************************************
 * @Function Declarations
 *****************************************************************************/
static SpiTransReturn_t ExecuteStep(const InitStep_t* Step);
static void NextStep(void);
static void UpdateProgress(void);

/******************************************************************************
 * @Function Implementation
 *****************************************************************************/

/**
 * @brief 执行初始化步骤
 * @param Step 步骤指针
 * @return SPI传输结果
 */
static SpiTransReturn_t ExecuteStep(const InitStep_t* Step)
{
    SpiTransReturn_t Result = SPI_TRANS_OK;

    if (Step == NULL)
    {
        return SPI_TRANS_FAIL;
    }

    switch (Step->CmdType)
    {
        case CMD_TYPE_NONE:
            /* 无命令，直接返回成功 */
            Result = SPI_TRANS_OK;
            break;

        case CMD_TYPE_WRITE:
            /* 写命令 */
            Result = SpiCmd_WriteByAddress(Step->Channel, Step->Address, (uint8*)Step->TxData);
            break;

        case CMD_TYPE_READ:
            /* 读命令 */
            Result = SpiCmd_ReadByAddress(Step->Channel, Step->Address);
            break;

        case CMD_TYPE_NO_OPERATION:
            /* 空操作命令 */
            Result = SpiCmd_NoOperation(Step->Channel, Step->Address, InitProcessCtrl.RegValues);
            break;

        case CMD_TYPE_GET_CRM_RESP:
            /* 获取CRM响应命令 */
            Result = SpiCmd_GetCrmResp(Step->Channel, Step->DataLen, InitProcessCtrl.RegValues);
            break;

        default:
            /* 未知命令类型 */
            Result = SPI_TRANS_FAIL;
            break;
    }

    return Result;
}

/**
 * @brief 更新进度
 */
static void UpdateProgress(void)
{
    if (CurrentStepTableSize > 0)
    {
        InitProcessCtrl.Progress = (uint8)((InitProcessCtrl.CurrentStepIndex * 100) / CurrentStepTableSize);
    }
    else
    {
        InitProcessCtrl.Progress = 0;
    }
}

/**
 * @brief 进入下一步
 */
static void NextStep(void)
{
    /* 增加步骤索引 */
    InitProcessCtrl.CurrentStepIndex++;

    /* 更新进度 */
    UpdateProgress();

    /* 检查是否完成 */
    if (InitProcessCtrl.CurrentStepIndex >= CurrentStepTableSize)
    {
        /* 切换到完成状态 */
        InitProcessCtrl.Status = RDUM_RDUS_INIT_PROCESS_COMPLETE;
    }

    /* 重置重试次数 */
    InitProcessCtrl.RetryCount = 0;

    /* 记录步骤开始时间 */
    InitProcessCtrl.StepStartTime = BaseDrv_GetSys1MsTick();
}

/******************************************************************************
 * @brief      初始化流程模块初始化
 * <AUTHOR>
 * @date       2025-05-20
 * @note       初始化流程控制结构体
 *****************************************************************************/
void RdumRdusInitProcess_Init(void)
{
    /* 初始化流程控制结构体 */
    memset(&InitProcessCtrl, 0, sizeof(InitProcessControl_t));
    InitProcessCtrl.Status = RDUM_RDUS_INIT_PROCESS_IDLE;

    /* 重置步骤表指针 */
    CurrentStepTable = NULL;
    CurrentStepTableSize = 0;
}

/******************************************************************************
 * @brief      启动初始化流程
 * @param[in]  InitType 初始化类型
 * @return     启动结果
 * <AUTHOR>
 * @date       2025-05-20
 * @note       启动初始化流程
 *****************************************************************************/
uint8 RdumRdusInitProcess_Start(RdumRdusInitType_t InitType)
{
    /* 检查当前状态 */
    if (InitProcessCtrl.Status != RDUM_RDUS_INIT_PROCESS_IDLE)
    {
        return 0; /* 当前不是空闲状态，无法启动 */
    }

    /* 初始化控制结构体 */
    memset(&InitProcessCtrl, 0, sizeof(InitProcessControl_t));
    InitProcessCtrl.Status = RDUM_RDUS_INIT_PROCESS_BUSY;
    InitProcessCtrl.InitType = InitType;
    InitProcessCtrl.StartTime = BaseDrv_GetSys1MsTick();
    InitProcessCtrl.StepStartTime = InitProcessCtrl.StartTime;

    /* 根据初始化类型选择步骤表 */
    switch (InitType)
    {
        case RDUM_RDUS_INIT_TYPE_RDUM:
            CurrentStepTable = RdumInitSteps;
            CurrentStepTableSize = RDUM_INIT_STEPS_SIZE;
            break;

        case RDUM_RDUS_INIT_TYPE_RDUS:
            CurrentStepTable = RdusInitSteps;
            CurrentStepTableSize = RDUS_INIT_STEPS_SIZE;
            break;

        case RDUM_RDUS_INIT_TYPE_BOTH:
            /* 先执行RDUM初始化 */
            CurrentStepTable = RdumInitSteps;
            CurrentStepTableSize = RDUM_INIT_STEPS_SIZE;
            break;

        default:
            /* 未知初始化类型 */
            InitProcessCtrl.Status = RDUM_RDUS_INIT_PROCESS_ERROR;
            return 0;
    }

    /* 初始化步骤索引 */
    InitProcessCtrl.CurrentStepIndex = 0;

    /* 更新进度 */
    UpdateProgress();

    /* 执行第一步 */
    if (CurrentStepTable != NULL && CurrentStepTableSize > 0)
    {
        ExecuteStep(&CurrentStepTable[0]);
    }

    return 1;
}

/******************************************************************************
 * @brief      处理初始化流程
 * <AUTHOR>
 * @date       2025-05-20
 * @note       周期性调用此函数处理初始化流程
 *****************************************************************************/
void RdumRdusInitProcess_Process(void)
{
    SpiTransReturn_t Result;
    uint32 CurrentTime;

    /* 检查当前状态 */
    if (InitProcessCtrl.Status != RDUM_RDUS_INIT_PROCESS_BUSY)
    {
        return; /* 当前不是忙状态，无需处理 */
    }

    /* 检查步骤表和索引 */
    if (CurrentStepTable == NULL || InitProcessCtrl.CurrentStepIndex >= CurrentStepTableSize)
    {
        /* 步骤表为空或索引超出范围，进入完成状态 */
        InitProcessCtrl.Status = RDUM_RDUS_INIT_PROCESS_COMPLETE;
        return;
    }

    /* 获取当前时间 */
    // CurrentTime = BaseDrv_GetSys1MsTick();

    /* 检查超时 */
    // if ((CurrentTime - InitProcessCtrl.StartTime) > INIT_PROCESS_TIMEOUT)
    // {
    //     InitProcessCtrl.Status = RDUM_RDUS_INIT_PROCESS_TIMEOUT;
    //     return;
    // }

    /* 检查步骤超时 */
    // if ((CurrentTime - InitProcessCtrl.StepStartTime) > 100)
    // {
    //     /* 步骤超时，重试 */
    //     InitProcessCtrl.RetryCount++;

    //     /* 检查重试次数 */
    //     if (InitProcessCtrl.RetryCount > MAX_RETRY_COUNT)
    //     {
    //         /* 重试次数超过限制，进入错误状态 */
    //         InitProcessCtrl.Status = RDUM_RDUS_INIT_PROCESS_ERROR;
    //         return;
    //     }

    //     /* 重置步骤开始时间 */
    //     InitProcessCtrl.StepStartTime = CurrentTime;
    // }

    /* 获取当前步骤 */
    const InitStep_t* CurrentStep = &CurrentStepTable[InitProcessCtrl.CurrentStepIndex];

    /* 执行步骤 */
    Result = ExecuteStep(CurrentStep);

    /* 检查结果 */
    if (Result == SPI_TRANS_OK)
    {
        /* 执行成功，进入下一步 */
        NextStep();

        /* 检查是否需要切换步骤表 */
        if (InitProcessCtrl.InitType == RDUM_RDUS_INIT_TYPE_BOTH &&
            InitProcessCtrl.CurrentStepIndex >= CurrentStepTableSize &&
            CurrentStepTable == RdumInitSteps)
        {
            /* RDUM初始化完成，切换到RDUS初始化 */
            CurrentStepTable = RdusInitSteps;
            CurrentStepTableSize = RDUS_INIT_STEPS_SIZE;
            InitProcessCtrl.CurrentStepIndex = 0;
            UpdateProgress();
        }
    }
    else
    {
        /* 执行失败，重试 */
        InitProcessCtrl.RetryCount++;

        /* 检查重试次数 */
        if (InitProcessCtrl.RetryCount > MAX_RETRY_COUNT)
        {
            /* 重试次数超过限制，进入错误状态 */
            InitProcessCtrl.Status = RDUM_RDUS_INIT_PROCESS_ERROR;
            return;
        }
    }
}

/******************************************************************************
 * @brief      获取初始化流程状态
 * @return     初始化状态
 * <AUTHOR>
 * @date       2025-05-20
 * @note       获取当前初始化流程状态
 *****************************************************************************/
RdumRdusInitProcessStatus_t RdumRdusInitProcess_GetStatus(void)
{
    return InitProcessCtrl.Status;
}

/******************************************************************************
 * @brief      获取初始化流程进度
 * @return     进度(0-100)
 * <AUTHOR>
 * @date       2025-05-20
 * @note       获取当前初始化流程进度
 *****************************************************************************/
uint8 RdumRdusInitProcess_GetProgress(void)
{
    return InitProcessCtrl.Progress;
}

/******************************************************************************
 * @brief      获取初始化流程类型
 * @return     初始化类型
 * <AUTHOR>
 * @date       2025-05-20
 * @note       获取当前初始化流程类型
 *****************************************************************************/
RdumRdusInitType_t RdumRdusInitProcess_GetType(void)
{
    return InitProcessCtrl.InitType;
}

/******************************************************************************
 * @brief      重置初始化流程
 * <AUTHOR>
 * @date       2025-05-20
 * @note       重置初始化流程到空闲状态
 *****************************************************************************/
void RdumRdusInitProcess_Reset(void)
{
    /* 重置初始化流程控制结构体 */
    InitProcessCtrl.Status = RDUM_RDUS_INIT_PROCESS_IDLE;
    InitProcessCtrl.CurrentStepIndex = 0;
    InitProcessCtrl.RetryCount = 0;
    InitProcessCtrl.Progress = 0;

    /* 重置步骤表指针 */
    CurrentStepTable = NULL;
    CurrentStepTableSize = 0;
}
