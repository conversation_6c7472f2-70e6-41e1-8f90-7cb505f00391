/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * PDC_MEB_CalibPara: 
 * Created on: 2023-02-28 09:16
 * Original designer: 22996
 ******************************************************************************/
#ifndef SDW_CalibPara_H
#define SDW_CalibPara_H

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "types.h"
#include "SDW_CalibPara_Config.h"

/******************************************************************************
* 设计描述 : SDW静态参数获取
* 设计索引 : 
*******************************************************************************/
typedef struct
{
    float LfVehicleHalfWidth;         /* 车辆半宽  */
    float LfFrontSideToCarCenter;     /* 前雷达到车中心的距离  */
    float LfRearSideToCarCenter;      /* 后雷达到车中心的距离  */
    float LfFrontSideToRearAxleDis;   /* 前雷达到后轴中心的距离 */
    float LfRearSideToRearAxleDis;    /* 后雷达到后轴中心的距离 */
    float LfArea1EndDis;              /* 区域1的结束距离 */
    float LfArea2EndDis;
    float LfArea3EndDis;
    float LfArea4EndDis;
    float LfArea5EndDis;
    float LfArea6EndDis;
    float LfArea7EndDis;
    float LfRearWheelToCarTailDis;        /* 后轮到后保的距离 */
    float LfRearWheelToCarHeadDis;        /* 后轮到前保的距离 */
    uint16 u16StartFollowDis;
    uint8 u8ObjBufferMaxCnt;
	float LfFLS_FLBumperDis;
	float LfRLS_RLBumperDis; 
	float LfFrontSDWAngleCos; 
	float LfFrontSDWAngleSin; 
	float LfRearSDWAngleCos;  
	float LfRearSDWAngleSin; 
}SDW_Dis_CarParameterType;



typedef struct
{
   sint8 FLS_CompDisance;
   sint8 FRS_CompDisance;
   sint8 RRS_CompDisance;
   sint8 RLS_CompDisance;
   
    
}SDW_CompDisance_Type;

/* 定义收窄侧探头FOV不同发波类型的高度阈值 */
typedef struct
{
    uint16 u16StdHeight;
    uint16 u16AdvHeight;
}SDW_HeightThreType;

typedef enum
{
    SDW_SNS_DIS_HEIGHT_100mm = 0, 
    SDW_SNS_DIS_HEIGHT_200mm,
    SDW_SNS_DIS_HEIGHT_300mm,
    SDW_SNS_DIS_HEIGHT_400mm, 
    SDW_SNS_DIS_HEIGHT_500mm,

    SDW_SNS_DIS_HEIGHT_600mm, 
    SDW_SNS_DIS_HEIGHT_700mm,
    SDW_SNS_DIS_HEIGHT_800mm,
    SDW_SNS_DIS_HEIGHT_900mm, 
    SDW_SNS_DIS_HEIGHT_1000mm,
    
    SDW_SNS_DIS_HEIGHT_1100mm,
    SDW_SNS_DIS_HEIGHT_1200mm,
    SDW_SNS_DIS_HEIGHT_1300mm,
    SDW_SNS_DIS_HEIGHT_1400mm,
    SDW_SNS_DIS_HEIGHT_1500mm,
    
    SDW_SNS_DIS_HEIGHT_1600mm,
    SDW_SNS_DIS_HEIGHT_1700mm,
    SDW_SNS_DIS_HEIGHT_1800mm,
    SDW_SNS_DIS_HEIGHT_1900mm,
    SDW_SNS_DIS_HEIGHT_2000mm,
    
    SDW_SNS_DIS_HEIGHT_TOTAL_NUM
}SDW_SnsDisHeightType;

/* 三个车型的数据存储于Flash中 */
extern const SDW_Dis_CarParameterType GstrSDW_DisCarPara_X02;
extern const SDW_CompDisance_Type GstrSDW_CompDisPara_X02;

extern const SDW_Dis_CarParameterType GstrSDW_DisCarPara_X03;
extern const SDW_CompDisance_Type GstrSDW_CompDisPara_X03;

extern const SDW_Dis_CarParameterType GstrSDW_DisCarPara_X04;
extern const SDW_CompDisance_Type GstrSDW_CompDisPara_X04;


/* 应用层从Ram中加载数据 */
extern SDW_Dis_CarParameterType GstrSDW_DisCarPara_Ram;
extern SDW_CompDisance_Type     GstrSDW_CompDisPara_Ram;

extern const SDW_HeightThreType SDW_HeightThreTable[SDW_SNS_DIS_HEIGHT_TOTAL_NUM];

/* funtion */
extern void SDW_CalibPara_Config_Init(void);

#endif /* end of SDW_CalibPara_H */

