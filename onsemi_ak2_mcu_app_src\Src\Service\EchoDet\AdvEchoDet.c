/*
 * AdvEchoDet.c
 *
 *  Created on: 2021年6月26日
 *      Author: 6000021992
 */
#include "math.h"
#include "AdvEchoDet.h"
#include "Elmos_524_17_SnsCtrl.h"
#include "Elmos_524_17_Private.h"
#include "AK2_MCU_Drv.h"

 typedef enum
{
	NFD1DeltaEcho2Idx = 0,
	NFD1DeltaEcho3Idx,
	NFD2ResultIdx,
	NFDTypeNum
}NFDType_e;

typedef struct
{	
	uint16 FirstNegTimeStampNFD2Assert;
	uint16 FirstNegTimeStamp;
	uint16 DynamicPreOutputDis;
	uint16 PreDisD0;
	NFDOutput_t NFDOutput;
}NFDCtrl_t;

typedef struct
{	
	uint8 NoiseAbnormalCnt;
	uint8 NoiseNormalPeriod;
}NoiseDetect_t;

#define NFD_DATA_BUF_NUM          (5U)
#define FIRST_NOISE_THRESHOLD     (3000U)
#define SECOND_NOISE_THRESHOLD    (4000U)


NFDCtrl_t NFDCtrl[SNSNum] = {0};
NoiseDetect_t NoiseDetect[SNSNum] = {0};


/* 后续直接采用浮点数计算，提升精度 2021-02-19 */
const float GfTemperatureAndDistance[32]=
{
    /*温度范围-40~115*/
    153.10,    /* -40 ℃*/
    154.74,    /* -35 ℃*/
    156.35,    /* -30 ℃*/
    157.95,    /* -25 ℃*/
    159.54,    /* -20 ℃*/
    161.11,    /* -15 ℃*/
    162.66,    /* -10 ℃*/
    164.20,    /* -5  ℃*/
    165.73,    /*  0  ℃*/
    167.24,    /*  5  ℃*/
    168.73,    /*  10 ℃*/
    170.22,    /*  15 ℃*/
    171.69,    /*  20 ℃*/
    173.15,    /*  25 ℃*/
    174.59,    /*  30 ℃*/
    176.03,    /*  35 ℃*/
    177.45,    /*  40 ℃*/
    178.86,    /*  45 ℃*/
    180.26,    /*  50 ℃*/
    181.65,    /*  55 ℃*/
    183.03,    /*  60 ℃*/
    184.40,    /*  65 ℃*/
    185.76,    /*  70 ℃*/
    187.11,    /*  75 ℃*/
    188.45,    /*  80 ℃*/
    189.78,    /*  85 ℃*/
    191.10,    /*  90 ℃*/
    192.41,    /*  95 ℃*/
    193.71,    /*  100℃*/
    195.01,    /*  105℃*/
    196.29,    /*  110℃*/
    197.57     /*  115℃*/    
};

float GfTepAndDisTemp = 171.69;    /*默认值20度*/

void UpdateTemperatureDistance(void)
{
    static uint8 Lu8UpdateTime = 0;
    AK2_Car_AmbientTempType LenuCar_AmbientTemp = 0;
    uint8 LcTempArry;
    static uint8 LcOldTempArry = 0xFF;

    Lu8UpdateTime++;
    /* 200ms 更新一次 */
    if(Lu8UpdateTime > 200)
    {
        Lu8UpdateTime = 0;
        LenuCar_AmbientTemp = AK2_ReadCAN_AppSignal_Car_AmbientTempType();
        //取值范围变成索引表
        LcTempArry = LenuCar_AmbientTemp+40;
        LcTempArry = LcTempArry/5u;
        if(LcTempArry != LcOldTempArry)
        {
            if(LcTempArry>31)
            {
                LcTempArry =  31;
            }
            //不同温度对应不同距离
            GfTepAndDisTemp = GfTemperatureAndDistance[LcTempArry];
            LcOldTempArry = LcTempArry;
        }
    }
}


#define CalcDisCoff 29

uint16 EchoDet_CalcEchoDistance(uint16 Time)
{
    uint16 distance = 0;
    distance = (uint16)((int16)(Time * GfTepAndDisTemp * 2 / 1000));
    return distance;
}

/***************************************************************************//**
 * @brief
 * 对数组去掉最大值和最小值，求平均值
 *
 * @param       pInData    数组指针 
 * @param       DataNum    数组大小
 * @return      ReturnData 数组平均值
 *
 * ****************************************************************************/
uint16 EchoDet_AvgCalc(uint16 *pInData,uint8 DataNum)
{
	uint8 i = 0;
	uint8 j = 0;
	uint16 tempData = 0;
	uint16 ReturnData = 0;
	
	for(i = 0;i < (DataNum - 1);i++)
	{
		for(j = 0;j < (DataNum - 1 - i);j++)
		{
			if(pInData[j] > pInData[j + 1])
			{
				tempData = pInData[j];
				pInData[j] = pInData[j + 1];
				pInData[j + 1] = tempData;
			}
		}
	}

	for(i = 1; i < (DataNum - 1);i++)
	{
		ReturnData += pInData[i];
	}

	if((DataNum - 2) != 0)
	{
		ReturnData = ReturnData / (DataNum - 2);
	}
	else
	{
		ReturnData = 0;
	}
	
	return ReturnData;
}

/***************************************************************************//**
 * @brief
 * 动态情况下NFD处理
 *
 * @param       pNFDAvg    NFD结果平均值 
 * @param       pDistDat   距离数据
 * @param       pNFDCtrl   NFD数据处理结构体
 * @return      
 *
 * ****************************************************************************/
void EchoDet_DynamicNFD(uint16 *pNFDAvg,Sns_DetDist_Str *pDistDat,NFDCtrl_t *pNFDCtrl)
{
	uint16 DisD0 = pDistDat->Dis[0] >> 1;
	uint16 OutputDis = 0;
		
	if(DisD0 < 250)
	{
		if((pNFDCtrl->FirstNegTimeStampNFD2Assert < pNFDCtrl->FirstNegTimeStamp)
		&& ((pNFDCtrl->FirstNegTimeStamp - pNFDCtrl->FirstNegTimeStampNFD2Assert) > 50)
		&&(DisD0 <= 150))
		{
			OutputDis = EchoDet_CalcEchoDistance(pNFDCtrl->FirstNegTimeStamp - 100) >> 1;
		}
		else
		{
			if((DisD0 < 115) || (DisD0 > 225))
			{
				OutputDis = 100;
			}
			else
			{
				OutputDis = DisD0 >> 1; /*输出为 D0/2*/
			}
		}
		
		if(((ABS(pNFDCtrl->DynamicPreOutputDis,OutputDis)) > 50) && (0 != pNFDCtrl->DynamicPreOutputDis))
		{
			OutputDis = 100;
		}
	
		pNFDCtrl->DynamicPreOutputDis = OutputDis;
		
	}
	else
	{
		OutputDis = 100;
	}

	pNFDCtrl->NFDOutput.NFDDis = OutputDis;
}

/***************************************************************************//**
 * @brief
 * 静态情况下NFD处理
 *
 * @param       pNFDAvg    NFD结果平均值 
 * @param       pDistDat   距离数据
 * @param       pNFDCtrl   NFD数据处理结构体
 * @return     
 *
 * ****************************************************************************/
void EchoDet_StaticNFD(uint16 *pNFDAvg,Sns_DetDist_Str *pDistDat,NFDCtrl_t *pNFDCtrl)
{
	uint16 NFD1Delta1Dis = 0;
	uint16 DisD0 = pDistDat->Dis[0] >> 1;
	uint16 OutputDis = 0;
				
	if(DisD0 <= 115)
	{
		OutputDis = 100;
	}
	else if((DisD0 > 115) && (DisD0 <= 230))
	{
		NFD1Delta1Dis = EchoDet_CalcEchoDistance(pNFDAvg[NFD1DeltaEcho2Idx]) >> 1;
		if(ABS(NFD1Delta1Dis,DisD0) <= 50)
		{
			OutputDis = DisD0;
		}
		else
		{
			OutputDis = 100;
		}
	}
	else if((DisD0 > 230) && (DisD0 <= 300))
	{
		OutputDis = DisD0 >> 1; /*输出D/2*/
	}
	else
	{
		OutputDis = 100; 
	}

	NFD1Delta1Dis = EchoDet_CalcEchoDistance(pNFDAvg[NFD1DeltaEcho2Idx]) >> 1;
	if(NFD1Delta1Dis < 110)
	{
		OutputDis = 100;
	}

	pNFDCtrl->NFDOutput.NFDDis = OutputDis;
}

/***************************************************************************//**
 * @brief
 * 单个Sns 回波数据处理，计算距离
 *
 * @param       LeSnsID      	 探头ID 
 * @param       SNS_MEAS_TYPE    探头测量类型 
 * @param       pThresholdSet    探头测量类型
 * @param       pInECHOData      回波信息
 * @param       pOutDistDat      距离信息
 * @param       pNFDData         NFD信息
 * @return      0                无超过阈值的回波
 *              有效回波个数                                else
 *
 * ****************************************************************************/
uint8 EchoDet_SnsEchoHandle(SnsID_en LeSnsID,SNS_MEAS_TYPE_en SNS_MEAS_TYPE,ThresholdSet_str * pThresholdSet,Sns_ECHOMsg_Str *pInECHOData,Sns_DetDist_Str *pOutDistDat,int32 TimeCompensation,Sns_PDCM_NFDData *pNFDData)
{
    uint16 LcCnt;

    uint16 Flg = INVALID;
    Sns_ECHOData_un *pECHOData = NULL;
    
    DistanceType_en DisType = DistanceType_Num; /*距离类型*/
    uint16 EchoTimeStamp = 0;
	uint16 EchoStartTimeStamp = 0;
    uint16 Dis = 0;
    uint16 Amp = 0;
    uint16 WidthOrConf = 0;
    uint8 filterNum = 0;
    uint16 Threshold = 0;
    uint8 StdNegFlg = 0;
	NFDCtrl_t *pNFDCtrl = &NFDCtrl[LeSnsID];
	uint8 AdvValidFlg = 0;
	
    memset(pOutDistDat,0,sizeof(Sns_DetDist_Str));

    pOutDistDat->SNS_MEAS_TYPE = SNS_MEAS_TYPE;
    pOutDistDat->SaveDisDataCnt = 0;

    for(LcCnt = 0; LcCnt < pInECHOData->SaveECHODataCnt; LcCnt ++)
    {
        Flg = INVALID;
        pECHOData = &pInECHOData->ECHOBuf[LcCnt];
		EchoTimeStamp = 0;
        Dis = 0;
		Amp = 0;
		WidthOrConf = 0;
		filterNum = 0;
		AdvValidFlg = 0;
		
        if((SNS_MEAS_TYPE == MEAS_STD_EMIT) || (SNS_MEAS_TYPE == MEAS_STD_LISTEN))
        {
            EchoStartTimeStamp = 0;

            if(pECHOData->StdECHOData.ECHO_Event == STD_ECHO_Positive_Thres_Event)
            {   
                /*取上升沿*/
                LcCnt ++;
				EchoStartTimeStamp = pECHOData->StdECHOData.TimeStamp;
                
                for(; LcCnt < pInECHOData->SaveECHODataCnt; LcCnt ++)
                {
                    /*搜索波峰/下降事件*/
                
                    pECHOData = &pInECHOData->ECHOBuf[LcCnt];
                    
                    if(pECHOData->StdECHOData.ECHO_Event == STD_ECHO_MAX_Above_Thres_Event)
                    {
                        /*取波峰*/
                        if(pECHOData->StdECHOData.EchoHeightAmpd_RAW >  Amp)
                        {
                            Amp = pECHOData->StdECHOData.EchoHeightAmpd_RAW;
							EchoTimeStamp = pECHOData->StdECHOData.TimeStamp;
	                        Flg = VALID;
                        }
                    }

					if(pECHOData->StdECHOData.ECHO_Event == STD_ECHO_MIN_Above_Thres_Event)
                    {
                        WidthOrConf = pECHOData->StdECHOData.TimeStamp - EchoStartTimeStamp;
	                    break;  
                    }
                    
                    /*取下降沿，一个完整回波*/
                    if(pECHOData->StdECHOData.ECHO_Event == STD_ECHO_Negative_Thres_Event)
                    {
                        WidthOrConf = pECHOData->StdECHOData.TimeStamp - EchoStartTimeStamp;
                        StdNegFlg = 1; 
                        break;
                    }
                }
            }
            else if(pECHOData->StdECHOData.ECHO_Event == STD_ECHO_MAX_Above_Thres_Event)
            {   
                if(StdNegFlg == 0)
                {
                    /*定频下降沿事件之前，出现波谷/波峰*/
                    /*可能为近距离障碍物或余震分叉 */

                }
                else
                {
                    /*下降沿事件出现后进入该分支，则可能为回波数据接收丢帧或探头回波数据顺序错误*/
                
                }
                
                #if 1
                /*探头不分叉时处理*/
                
                /*取波峰*/
                EchoTimeStamp = pECHOData->StdECHOData.TimeStamp;
                Amp = pECHOData->StdECHOData.EchoHeightAmpd_RAW;
                Flg = VALID;
                
                LcCnt ++;
                for(; LcCnt < pInECHOData->SaveECHODataCnt; LcCnt ++)
                {
                    pECHOData = &pInECHOData->ECHOBuf[LcCnt];
                    if(pECHOData->StdECHOData.ECHO_Event == STD_ECHO_MIN_Above_Thres_Event)
                    {
                        WidthOrConf = (pECHOData->StdECHOData.TimeStamp - EchoTimeStamp) << 1;/*由于最大值为起始点，故乘2*/
                        break;
                    }
                    
                    /*取下降沿*/
                    if(pECHOData->StdECHOData.ECHO_Event == STD_ECHO_Negative_Thres_Event)
                    {
                        WidthOrConf = (pECHOData->StdECHOData.TimeStamp - EchoTimeStamp) << 1;
                        StdNegFlg = 1;
                        break;
                    }
                }
                #endif                
            }
			else if(pECHOData->StdECHOData.ECHO_Event == STD_ECHO_MIN_Above_Thres_Event)
            {                
                /*取波谷*/
                EchoStartTimeStamp = pECHOData->StdECHOData.TimeStamp;
                
                LcCnt ++;
                for(; LcCnt < pInECHOData->SaveECHODataCnt; LcCnt ++)
                {
                    pECHOData = &pInECHOData->ECHOBuf[LcCnt];
                    if(pECHOData->StdECHOData.ECHO_Event == STD_ECHO_MAX_Above_Thres_Event)
                    {
                        /*取波峰*/
                        if(pECHOData->StdECHOData.EchoHeightAmpd_RAW >  Amp)
                        {
                        	EchoTimeStamp = pECHOData->StdECHOData.TimeStamp;
                            Amp = pECHOData->StdECHOData.EchoHeightAmpd_RAW;
                            Flg = VALID;
                        }
                    }

                    if(pECHOData->StdECHOData.ECHO_Event == STD_ECHO_MIN_Above_Thres_Event)
                    {
                        WidthOrConf = pECHOData->StdECHOData.TimeStamp - EchoStartTimeStamp;
                        break;
                    }
					
                    /*取下降沿*/
                    if(pECHOData->StdECHOData.ECHO_Event == STD_ECHO_Negative_Thres_Event)
                    {
                        WidthOrConf = pECHOData->StdECHOData.TimeStamp - EchoStartTimeStamp;
                        StdNegFlg = 1;
                        break;
                    }
                }
            }
            else
            {
                /*下降沿*/
                StdNegFlg = 1;
                if(LcCnt == 0)
                {
                    /*首个事件为下降沿事件*/
					if((pNFDData->NFD2_Result * 2) > 120)
					{
						pNFDCtrl->FirstNegTimeStamp = pECHOData->StdECHOData.TimeStamp;
						if(0 == pNFDCtrl->FirstNegTimeStampNFD2Assert)
						{
							pNFDCtrl->FirstNegTimeStampNFD2Assert = pECHOData->StdECHOData.TimeStamp;
						}
					} 
                }
                else
                {
                    /*下降沿事件重复或回波数据接收丢帧或探头回波数据顺序错误*/
                
                }
            }
        }
        else
        {
			if(((MEAS_ADV_EMIT_ChirpDown == SNS_MEAS_TYPE) && (ADV_ECHO_Filter2_ChirpUp == pECHOData->AdvECHOData.filterNum))
				|| ((MEAS_ADV_EMIT_ChirpUp == SNS_MEAS_TYPE) && (ADV_ECHO_Filter1_ChirpDown == pECHOData->AdvECHOData.filterNum)))
			{
				AdvValidFlg = 0;
			}
			else	
			{
				AdvValidFlg = 1;
			}

			if(1 == AdvValidFlg)
			{
	            filterNum = pECHOData->AdvECHOData.filterNum;
				if(pECHOData->AdvECHOData.filterNum == ADV_ECHO_Filter2_ChirpUp)/*测试发现障碍物在同样距离下上扫频比下扫频距离大，须减小补偿*/
				{
					EchoTimeStamp = pECHOData->AdvECHOData.TimeStamp - 95;   /*uint:us*/ 
				}
				else
				{
					EchoTimeStamp = pECHOData->AdvECHOData.TimeStamp;
				}
	            Amp = pECHOData->AdvECHOData.FilterAmpd;
	            WidthOrConf = pECHOData->AdvECHOData.Confidence;
	            
	            Flg = VALID;
			}
        }

		/*时间补偿*/
		if((TimeCompensation > 0) || (EchoTimeStamp > ABS_VALUE(TimeCompensation)))
		{
			EchoTimeStamp += TimeCompensation;
		}
		else
		{
			EchoTimeStamp = 0;
		}
		
        if(Flg == VALID)
        {
            DisType = Elmos17_GetDisType(SNS_MEAS_TYPE, filterNum);
            
            Dis = EchoDet_CalcEchoDistance(EchoTimeStamp);

            /*计算阈值*/
            if(pThresholdSet == NULL)
            {
                Threshold = 0;
            }
            else
            {
                Threshold = 0;//CalcDisThreshold(pThresholdSet,Dis);
            }
            if(DisType > STD_Lis )
            {
                /*扫频回波幅值需要大于阈值*/
                if(Threshold > Amp)
                {
                    Flg = INVALID;
                }
            }
            else
            {
                /*定频回波宽度需要大于阈值*/
                if(Threshold > WidthOrConf)
                {
                    Flg = INVALID;
                }
            }
        }


        if(Flg != INVALID)
        {
            
            pOutDistDat->DisType[pOutDistDat->SaveDisDataCnt] = DisType;
            pOutDistDat->EchoTimeStamp[pOutDistDat->SaveDisDataCnt] = EchoTimeStamp;
            pOutDistDat->Dis[pOutDistDat->SaveDisDataCnt] = Dis;
            pOutDistDat->Amp[pOutDistDat->SaveDisDataCnt] = Amp;
            pOutDistDat->WidthOrConf[pOutDistDat->SaveDisDataCnt] = WidthOrConf;

            pOutDistDat->SaveDisDataCnt ++;

            if(pOutDistDat->SaveDisDataCnt >= DisMsgMaxNum)
            {
                break;
            }
        }
        else
        {

        }
    }

    return pOutDistDat->SaveDisDataCnt;
}

/***************************************************************************//**
 * @brief
 * 单个Sns 回波数据处理，计算距离
 *
 * @param       LeSnsID      	 探头ID 
 * @param       pNFDData         NFD信息
 * @param       pOutDistDat      距离信息
 * @return      0                
 *
 * ****************************************************************************/
void EchoDet_SnsSTDNFDHandle(SnsID_en LeSnsID,Sns_PDCM_NFDData *pNFDData,Sns_DetDist_Str *pOutDistDat)
{
    uint16 LcCnt;
	uint16 NFDData[NFD_DATA_BUF_NUM] = {0};
	NFDCtrl_t *LpNFDCtrl = &NFDCtrl[LeSnsID];
	uint16 DisD0 = pOutDistDat->Dis[0] >> 1;
	bool DynamicFlg = FALSE;
	static uint16 NFDDataBuf[SNSNum][NFDTypeNum][NFD_DATA_BUF_NUM] = {{{0}}};
	static uint8 NFDDataCnt[SNSNum] = {0};
	static uint8 NFDDataIdx[SNSNum] = {0};
	static uint16 NFDAvg[SNSNum][NFDTypeNum] = {0};

	NFDDataBuf[LeSnsID][NFD1DeltaEcho2Idx][NFDDataIdx[LeSnsID]] = pNFDData->NFD1_Delta_ECHO2 * 8;
	NFDDataBuf[LeSnsID][NFD1DeltaEcho3Idx][NFDDataIdx[LeSnsID]] = pNFDData->NFD1_Delta_ECHO3 * 8;
	NFDDataBuf[LeSnsID][NFD2ResultIdx][NFDDataIdx[LeSnsID]] = pNFDData->NFD2_Result * 2;

	NFDDataIdx[LeSnsID]++;
	NFDDataCnt[LeSnsID]++;

	if(NFDDataCnt[LeSnsID] >= NFD_DATA_BUF_NUM)
	{
		NFDDataCnt[LeSnsID] = NFD_DATA_BUF_NUM;
	}

	if(NFDDataIdx[LeSnsID] == NFD_DATA_BUF_NUM)
	{
		NFDDataIdx[LeSnsID] = 0;
	}
	#if	0
	Lhprintf("%d NFD2 is %d \n",LeSnsID,pNFDData->NFD2_Result * 2);
	#endif
	if(NFD_DATA_BUF_NUM == NFDDataCnt[LeSnsID])
	{
		for(LcCnt = 0;LcCnt < NFDTypeNum;LcCnt++)
		{
			memcpy((void*)&NFDData[0],(const void*)&NFDDataBuf[LeSnsID][LcCnt][0],sizeof(NFDData));
			NFDAvg[LeSnsID][LcCnt] = EchoDet_AvgCalc(&NFDData[0],NFD_DATA_BUF_NUM);
		}
	}

	if((NFDAvg[LeSnsID][NFD2ResultIdx] > 120) && (NFD_DATA_BUF_NUM == NFDDataCnt[LeSnsID]))
	{
		if(ABS(LpNFDCtrl->PreDisD0,DisD0) > 50)
		{
			DynamicFlg = TRUE;
		}

		LpNFDCtrl->PreDisD0 = DisD0;
		
		if(TRUE == DynamicFlg)
		{
			EchoDet_DynamicNFD(&NFDAvg[LeSnsID][0],pOutDistDat,LpNFDCtrl);
		}
		else
		{
			EchoDet_StaticNFD(&NFDAvg[LeSnsID][0],pOutDistDat,LpNFDCtrl);
		}
		LpNFDCtrl->NFDOutput.NFDFlg = TRUE;
	}
	else
	{
		/*NFDCtrl[LeSnsID] 复位*/
		memset((void*)LpNFDCtrl,0,sizeof(NFDCtrl_t));
	}
}

/***************************************************************************//**
 * @brief
 * 单个Sns 噪声数据处理
 *
 * @param       LeSnsID      	 探头ID 
 * @param       pDiagData        探头诊断信息
 * @return      0                
 *
 * ****************************************************************************/
uint8 EchoDet_NoiseHandle(SnsID_en LeSnsID,const Sns_DiagData_Str *pDiagData)
{
	NoiseDetect_t* pNoiseDetect= &NoiseDetect[LeSnsID];
	
	if(pDiagData->NoiseSum < FIRST_NOISE_THRESHOLD)
	{
		if(pNoiseDetect->NoiseNormalPeriod < UINT8_MAX)
		{
			pNoiseDetect->NoiseNormalPeriod++;
		}
	}
	else
	{
		pNoiseDetect->NoiseNormalPeriod = 0;

		if(pDiagData->NoiseSum < SECOND_NOISE_THRESHOLD)
		{
			if(pNoiseDetect->NoiseAbnormalCnt <= (UINT8_MAX-5))
			{
				pNoiseDetect->NoiseAbnormalCnt += 5;
			}
			else
			{
				pNoiseDetect->NoiseAbnormalCnt = UINT8_MAX;
			}
		}
		else
		{
			pNoiseDetect->NoiseAbnormalCnt = UINT8_MAX;
		}
	}
	
	if(pNoiseDetect->NoiseNormalPeriod > 100)
	{
		if(pNoiseDetect->NoiseAbnormalCnt >= 5)
		{
			pNoiseDetect->NoiseAbnormalCnt -= 5;
		}
		else
		{
			pNoiseDetect->NoiseAbnormalCnt = 0;
		}
	}
    
    return pNoiseDetect->NoiseAbnormalCnt;
}

/***************************************************************************//**
 * @brief
 * 侦听直达波识别
 *
 * @param       ListenDis    输入侦听距离
 * @param       BUMPER_DIST  输入探头间距
 * @param       ShortDistTolerance   容差
 * @return      VALID    直达波
 *              INVALID  else
 *
 * ****************************************************************************/
uint8 EchoDet_Listen_Direct_Wave(uint16 ListenDis,uint16 BUMPER_DIST,uint16 ShortDistTolerance)
{
    uint16 DisSum = ListenDis << 1;
    /*直达波识别*/
    if(DisSum < (BUMPER_DIST + ShortDistTolerance))
    {
        return VALID;
    }
    else
    {
        return INVALID;
    }
}

/***************************************************************************//**
 * @brief
 * 有效三角形识别
 *
 * @param       MasterDis    输入主发距离
 * @param       ListenDis    输入侦听距离
 * @param       BUMPER_DIST  输入探头间距
 * @param       ShortDistTolerance   容差
 * @return      VALID    三角形有效
 *              INVALID  else
 *
 * ****************************************************************************/
uint8 EchoDet_CheckTriangle(uint16 MasterDis,uint16 ListenDis,uint16 BUMPER_DIST,uint16 ShortDistTolerance)
{
    uint16 DisSum = ListenDis << 1;

    uint16 DisSub ;
    if(DisSum > MasterDis)
    {
        DisSub = DisSum - MasterDis;

        if((DisSum < BUMPER_DIST)
        || ((DisSub + BUMPER_DIST) < MasterDis)
        || ((DisSub + MasterDis) < BUMPER_DIST))
        {
            return INVALID;
        }
        else
        {
            /*直达波识别*/
            if(DisSum < (BUMPER_DIST + ShortDistTolerance))
            {
                return INVALID;
            }
            else
            {
                return VALID;
            }
        }
    }
    else
    {
        return INVALID;
    }
}



#define FML_FMR_BumperDIS_NUM 5
#define FL_FML_BumperDIS_NUM  4
#define FLS_FL_BumperDIS_NUM  3

#define RML_RMR_BumperDIS_NUM 2
#define RL_RML_BumperDIS_NUM  1
#define RLS_RL_BumperDIS_NUM  0

uint8 SnsBUMPER_DIST[6] = {RLS_RL_BumperDIS,RL_RML_BumperDIS,RML_RMR_BumperDIS,FLS_FL_BumperDIS,FL_FML_BumperDIS,FML_FMR_BumperDIS};

uint8 EchoGet_GetLRSnsBUMPER_DIST(SnsID_en SnsID,ListenDist_LR_en LR)
{
    uint8 Bumper_dist = 0xFF;
    if(LR == Listen_Left)
    {

        if((SnsID == SNS_RL) || (SnsID == SNS_RR_S))
        {
            Bumper_dist = SnsBUMPER_DIST[RLS_RL_BumperDIS_NUM];
        }
        if((SnsID == SNS_RML) || (SnsID == SNS_RR))
        {
            Bumper_dist = SnsBUMPER_DIST[RL_RML_BumperDIS_NUM];
        }
        if(SnsID == SNS_RMR)
        {
            Bumper_dist = SnsBUMPER_DIST[RML_RMR_BumperDIS_NUM];
        }
        if((SnsID == SNS_FL) || (SnsID == SNS_FR_S))
        {
            Bumper_dist = SnsBUMPER_DIST[FLS_FL_BumperDIS_NUM];
        }
        if((SnsID == SNS_FML) || (SnsID == SNS_FR))
        {
            Bumper_dist = SnsBUMPER_DIST[FL_FML_BumperDIS_NUM];
        }
        if(SnsID == SNS_FMR)
        {
            Bumper_dist = SnsBUMPER_DIST[FML_FMR_BumperDIS_NUM];
        }

    }
    else
    {
        if((SnsID == SNS_RL_S) || (SnsID == SNS_RR))
        {
            Bumper_dist = SnsBUMPER_DIST[RLS_RL_BumperDIS_NUM];
        }
        if((SnsID == SNS_RMR) || (SnsID == SNS_RL))
        {
            Bumper_dist = SnsBUMPER_DIST[RL_RML_BumperDIS_NUM];
        }
        if(SnsID == SNS_RMR)
        {
            Bumper_dist = SnsBUMPER_DIST[RML_RMR_BumperDIS_NUM];
        }
        if((SnsID == SNS_FL) || (SnsID == SNS_FL_S))
        {
            Bumper_dist = SnsBUMPER_DIST[FLS_FL_BumperDIS_NUM];
        }
        if((SnsID == SNS_FMR) || (SnsID == SNS_FL))
        {
            Bumper_dist = SnsBUMPER_DIST[FL_FML_BumperDIS_NUM];
        }
        if(SnsID == SNS_FML)
        {
            Bumper_dist = SnsBUMPER_DIST[FML_FMR_BumperDIS_NUM];
        }

    }
    return Bumper_dist;
}





/*************************************************
*函数名称:  CalcDisAmpThreshold
* 功能描述:  切割阈值的计算
* 输入参数:  pThresholdSet:起始距离,步长的设置 , distance:测量距离
* 输出参数:  *****************
* 返 回 值:   切割阈值
***************************************************/

uint16 CalcDisThreshold(ThresholdSet_str * pThresholdSet,uint16 distance)
{
    uint16 cnt,size = pThresholdSet->Size - 1;
    uint16 disR;
    float k,b,y = 0;
    
    /* 距离小于StartDis*/
    if(distance < pThresholdSet->StartDis)
    {
        return pThresholdSet->pthreshold[0];
    }
    /*  距离大于设置的最大切割范围，取最大步径的切割阈值  */
    if(distance >= pThresholdSet->StartDis + (pThresholdSet->DisStep * size))
    {
        return pThresholdSet->pthreshold[size];
    }

    cnt = (distance - pThresholdSet->StartDis) / pThresholdSet->DisStep;
    
    /* 距离右值  = 距离左值加一个步长   */
    disR = pThresholdSet->StartDis + (pThresholdSet->DisStep * (cnt + 1));

    k = ((float)pThresholdSet->pthreshold[cnt+1] - (float)pThresholdSet->pthreshold[cnt]) /((float)pThresholdSet->DisStep) ;
    b = (float)pThresholdSet->pthreshold[cnt+1] - k * (float)disR;

    y = k * (float)distance + b;

//    if(y < 0.0)
//    {
//        y = 0.0;
//    }

    if(y > 65535.0)
    {
        y = 65535.0;
    }
    
    return (uint16)y;


}

const NFDOutput_t *EchoDet_NFDResult(SnsID_en LeSnsID)
{
	return (const NFDOutput_t *)&NFDCtrl[LeSnsID].NFDOutput;
}


