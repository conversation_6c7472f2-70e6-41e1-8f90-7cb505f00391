# RdumRdusDrv.c 完善说明

## 概述

根据参考文件 `RdumRdusDrv_SnsCtrl_ref.c` 和 `RdumRdusWaveProcess_ref.c`，完善了 `RdumRdusDrv.c` 中的功能实现，实现了异步SPI通信架构，其中命令发送和数据处理分离。

## 主要完善内容

### 1. 架构设计

- **异步处理**: 所有SPI命令只负责发送，数据处理统一在 `RdumRdus_SpiCmdCallback` 回调函数中异步处理
- **状态机管理**: 实现了完整的驱动状态机，包括初始化、就绪、发波流程和错误状态
- **分层设计**: 初始化流程 → 探头控制 → 发波流程的分层管理

### 2. 新增的类型定义

```c
/* DSI3通道枚举 */
typedef enum {
    DSI3_CHANNEL_A = 0,
    DSI3_CHANNEL_B,
    DSI3_CHANNEL_NUM_MAX
} Dsi3Channel_t;

/* 探头控制组状态 */
typedef enum {
    RdumRdusGroupStatus_SNS_DM = 0,         /* 探头发现模式 */
    RdumRdusGroupStatus_SNS_SELFTEST,       /* 探头自检 */
    RdumRdusGroupStatus_START_MEAS,         /* 开始测量 */
    // ... 其他状态
} RdumRdusGroupStatus_en;

/* 探头控制组配置 */
typedef struct {
    RdumRdusGroupStatus_en GroupStatus;
    uint32 Time1msCnt;
    uint32 TempCompTime1msCnt;
    // ... 其他字段
} RdumRdusGroup_Cfg;
```

### 3. 完善的函数功能

#### 3.1 初始化流程 (`RdumRdusDrv_Init`)
- 初始化驱动控制结构体
- 初始化SPI通信并注册回调函数
- 初始化探头控制组
- 启动初始化流程
- 添加调试日志输出

#### 3.2 1ms周期任务 (`RdumRdusDrv_1msTASK`)
- 更新时间计数
- 处理SPI数据
- 根据驱动状态执行不同流程：
  - `RDUM_RDUS_DRV_INIT`: 处理初始化流程
  - `RDUM_RDUS_DRV_READY`: 准备启动发波流程
  - `RDUM_RDUS_DRV_WAVE_PROCESS`: 处理发波流程
  - `RDUM_RDUS_DRV_ERROR`: 错误状态处理

#### 3.3 探头控制主函数 (`RdumRdusDrv_SnsCtrlMainFunc`)
- 实现完整的探头控制状态机
- 包括探头发现、自检、测量、温度补偿等状态
- 专注于命令发送，不等待响应

#### 3.4 SPI回调函数 (`RdumRdus_SpiCmdCallback`)
- 统一处理所有SPI命令的响应
- 根据命令类型分别处理：
  - `NO_OPERATION`: 读命令回调
  - `WRITE_BY_ADDRESS`: 非读命令回调
  - `GET_ALL_BRC`: BRC数据处理和解压缩
  - `GET_CRM_RESP`: CRM响应数据处理
- 包含完整的数据解压缩和打印功能

### 4. 新增的内部函数

#### 4.1 探头控制相关
- `RdumRdusDrv_InitSnsCtrlGroup()`: 初始化探头控制组
- `RdumRdusDrv_DiscoveryMode()`: 探头发现模式实现
- `RdumRdusDrv_UpdateTimeCnt()`: 更新时间计数
- `RdumRdusDrv_GetNextSnsCtrlGroupStatus()`: 获取下一个状态

#### 4.2 SPI回调处理
- `ReadCommandCallback()`: 读命令回调处理
- `NonReadCommandCallback()`: 非读命令回调处理

#### 4.3 DSI3命令封装
- `Dsi3Cmd_InitRdumRdus()`: DSI3初始化命令
- `Dsi3Cmd_RdumRdusStartMeasurement()`: 开始测量命令
- `Dsi3Cmd_RdumRdusIcModeSet()`: IC模式设置命令
- `Dsi3Cmd_RdumRdusReadStatus()`: 读取状态命令

### 5. 异步处理架构

#### 5.1 命令发送
- 所有命令通过SPI异步发送
- 不等待响应，立即返回
- 通过状态机管理命令序列

#### 5.2 数据处理
- 统一在 `RdumRdus_SpiCmdCallback` 中处理
- 根据命令类型调用相应的处理函数
- 包含错误处理和重试机制

#### 5.3 状态管理
- 驱动级别状态管理
- 探头控制组状态管理
- 发波流程状态管理
- 时间计数和超时处理

### 6. 调试和日志

- 添加了完整的调试日志输出
- 使用 `debug_log` 函数记录关键操作
- 包含状态转换、命令发送、数据处理等日志

### 7. 错误处理

- 完善的错误检测和处理机制
- SPI传输超时处理
- 命令执行失败处理
- 状态异常恢复机制

## 使用方式

### 初始化
```c
/* 系统启动时调用 */
RdumRdusDrv_Init();
```

### 周期性调用
```c
/* 在1ms定时器中调用 */
RdumRdusDrv_1msTASK();

/* 在非周期任务中调用 */
RdumRdusDrv_NoPeriod_TASK();
```

### 状态查询
```c
/* 获取驱动状态 */
uint8 status = RdumRdusDrv_GetStatus();

/* 获取发波流程标志 */
uint8 flag = RdumRdusDrv_GetWaveProcessFlag();
```

## 特点

1. **异步架构**: 命令发送和数据处理完全分离
2. **状态驱动**: 基于状态机的流程控制
3. **模块化**: 功能模块清晰分离
4. **可扩展**: 易于添加新的命令和处理逻辑
5. **调试友好**: 完整的日志输出和状态跟踪

## 注意事项

1. 确保SPI通信模块正确初始化
2. 定期调用1ms任务以保证状态机正常运行
3. 监控驱动状态，及时处理错误情况
4. 根据实际硬件调整时间参数和重试次数
