/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: UartDrv.c
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/
#include "types.h"
#include "UartRegCfg.h"
#include "UartDrv.h"

/******************************************************************************/
/**<pre>
 *函数名称: UartDrvInit
 *功能描述: 串口驱动初始化
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void UartDrvInit(void)
{
    /**<pre> 设置UART复位模式 </pre>*/
    RLN32.LCUC = _UART_LIN_RESET_MODE_CAUSED;
    /**<pre> 关闭ICRLIN32UR0中断并清除标志 </pre>*/
    INTC2.ICRLIN32UR0.BIT.MKRLIN32UR0 = _INT_PROCESSING_DISABLED;
    INTC2.ICRLIN32UR0.BIT.RFRLIN32UR0 = _INT_REQUEST_NOT_OCCUR;
    /**<pre> 关闭ICRLIN32UR1中断并清除标志 </pre>*/
    INTC2.ICRLIN32UR1.BIT.MKRLIN32UR1 = _INT_PROCESSING_DISABLED;
    INTC2.ICRLIN32UR1.BIT.RFRLIN32UR1 = _INT_REQUEST_NOT_OCCUR;
    /**<pre> 关闭ICRLIN32UR2中断并清除标志 </pre>*/
    INTC2.ICRLIN32UR2.BIT.MKRLIN32UR2 = _INT_PROCESSING_DISABLED;
    INTC2.ICRLIN32UR2.BIT.RFRLIN32UR2 = _INT_REQUEST_NOT_OCCUR;
    /**<pre> 设置ICRLIN32UR0中断映射及中断优先级 </pre>*/
    INTC2.ICRLIN32UR0.BIT.TBRLIN32UR0 = _INT_TABLE_VECTOR;
    INTC2.ICRLIN32UR0.UINT16 &= _INT_PRIORITY_LOWEST;
    /**<pre> 设置ICRLIN32UR1中断映射及中断优先级 </pre>*/
    INTC2.ICRLIN32UR1.BIT.TBRLIN32UR1 = _INT_TABLE_VECTOR;
    INTC2.ICRLIN32UR1.UINT16 &= _INT_PRIORITY_LOWEST;
    /**<pre> 设置ICRLIN32UR2中断映射及中断优先级 </pre>*/
    INTC2.ICRLIN32UR2.BIT.TBRLIN32UR2 = _INT_TABLE_VECTOR;
    INTC2.ICRLIN32UR2.UINT16 &= _INT_PRIORITY_LOWEST;
    /**<pre> 设置UART波特率及协议格式 </pre>*/
    RLN32.LWBR = _UART_6_SAMPLING | _UART_PRESCALER_CLOCK_SELECT_2;
    RLN32.LBRP01.UINT16 = _UART2_BAUD_RATE_PRESCALER;
    RLN32.LMD = _UART_NOISE_FILTER_ENABLED | _UART_MODE_SELECT;
    RLN32.LEDE = _UART_DISABLE_ERROR_DETECTED;
    RLN32.LBFC = _UART_TRANSMISSION_NORMAL | _UART_RECEPTION_NORMAL | _UART_PARITY_PROHIBITED | _UART_STOP_BIT_1 | 
                 _UART_LSB | _UART_LENGTH_8;
    RLN32.LCUC = _UART_LIN_RESET_MODE_CANCELED;
    RLN30.LUOR1 = _UART_INT_TRANSMISSION_END;

    /**<pre> 配置UART接收管脚 </pre>*/
    PORT.PIBC0 &= _PORT_CLEAR_BIT13;
    PORT.PBDC0 &= _PORT_CLEAR_BIT13;
    PORT.PM0 |= _PORT_SET_BIT13;
    PORT.PMC0 &= _PORT_CLEAR_BIT13;
    PORT.PIPC0 &= _PORT_CLEAR_BIT13;
    PORT.PFC0 |= _PORT_SET_BIT13;
    PORT.PFCE0 &= _PORT_CLEAR_BIT13;
    PORT.PFCAE0 |= _PORT_SET_BIT13;
    PORT.PMC0 |= _PORT_SET_BIT13;
    /**<pre> 配置UART发送管脚 </pre>*/
    PORT.PIBC0 &= _PORT_CLEAR_BIT14;
    PORT.PBDC0 &= _PORT_CLEAR_BIT14;
    PORT.PM0 |= _PORT_SET_BIT14;
    PORT.PMC0 &= _PORT_CLEAR_BIT14;
    PORT.PIPC0 &= _PORT_CLEAR_BIT14;
    PORT.PFC0 &= _PORT_CLEAR_BIT14;
    PORT.PFCE0 &= _PORT_CLEAR_BIT14;
    PORT.PFCAE0 &= _PORT_CLEAR_BIT14;
    PORT.PMC0 |= _PORT_SET_BIT14;
    PORT.PM0 &= _PORT_CLEAR_BIT14;

    /**<pre> 使能UART操作 </pre>*/
    RLN32.LUOER |= _UART_RECEPTION_ENABLED | _UART_TRABSMISSION_ENABLED;
    /**<pre> 清除ICRLIN32UR0中断并使能中断 </pre>*/
    INTC2.ICRLIN32UR0.BIT.RFRLIN32UR0 = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICRLIN32UR0.BIT.MKRLIN32UR0 = _INT_PROCESSING_ENABLED;
    /**<pre> 清除ICRLIN32UR1中断并使能中断 </pre>*/
    INTC2.ICRLIN32UR1.BIT.RFRLIN32UR1 = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICRLIN32UR1.BIT.MKRLIN32UR1 = _INT_PROCESSING_ENABLED;
    /**<pre> 清除ICRLIN32UR2中断并使能中断 </pre>*/
    INTC2.ICRLIN32UR2.BIT.RFRLIN32UR2 = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICRLIN32UR2.BIT.MKRLIN32UR2 = _INT_PROCESSING_ENABLED;
}

/******************************************************************************/
/**<pre>
 *函数名称: UartDrvClose
 *功能描述: 串口驱动关闭
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void UartDrvClose(void)
{
    /**<pre> 关闭ICRLIN32UR0中断 </pre>*/
    INTC2.ICRLIN32UR0.BIT.MKRLIN32UR0 = _INT_PROCESSING_DISABLED;
    /**<pre> 关闭ICRLIN32UR1中断 </pre>*/
    INTC2.ICRLIN32UR1.BIT.MKRLIN32UR1 = _INT_PROCESSING_DISABLED;
    /**<pre> 关闭ICRLIN32UR2中断 </pre>*/
    INTC2.ICRLIN32UR2.BIT.MKRLIN32UR2 = _INT_PROCESSING_DISABLED;
    /**<pre> 关闭UART模块 </pre>*/
    RLN32.LUOER &= (uint8) ~(_UART_RECEPTION_ENABLED | _UART_TRABSMISSION_ENABLED);
    /**<pre> 清除ICRLIN32UR0中断标志 </pre>*/
    INTC2.ICRLIN32UR0.BIT.RFRLIN32UR0 = _INT_REQUEST_NOT_OCCUR;
    /**<pre> 清除ICRLIN32UR1中断标志 </pre>*/
    INTC2.ICRLIN32UR1.BIT.RFRLIN32UR1 = _INT_REQUEST_NOT_OCCUR;
    /**<pre> 清除ICRLIN32UR2中断标志 </pre>*/
    INTC2.ICRLIN32UR2.BIT.RFRLIN32UR2 = _INT_REQUEST_NOT_OCCUR;
}

/******************************************************************************/
/**<pre>
 *函数名称: UartGetRxData
 *功能描述: 获取串口接收数据
 *输入参数: 无
 *输出参数: 无
 *返回数据: 反馈接收到的UART数据
 *修改记录: 无
*********************************************************************** </pre>*/
uint8 UartGetRxData(void)
{
    return RLN32LURDRL;    /**<pre> RETURN 接收到的UART数据字节 </pre>*/
}

/******************************************************************************/
/**<pre>
 *函数名称: UartSetTxData
 *功能描述: 设置串口发送数据
 *输入参数: Lu8Data 发送数据
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void UartSetTxData(uint8 Lu8Data)
{
    /**<pre> 清除ICRLIN32UR0中断并关闭中断 </pre>*/
    INTC2.ICRLIN32UR0.BIT.MKRLIN32UR0 = _INT_PROCESSING_DISABLED;
    INTC2.ICRLIN32UR0.BIT.RFRLIN32UR0 = _INT_REQUEST_NOT_OCCUR;
    RLN32LUTDRL = Lu8Data;
    /**<pre> 清除ICRLIN32UR0中断并打开中断 </pre>*/
    INTC2.ICRLIN32UR0.BIT.RFRLIN32UR0 = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICRLIN32UR0.BIT.MKRLIN32UR0 = _INT_PROCESSING_ENABLED;
}

/******************************************************************************/
/**<pre>
 *函数名称: UartGetTxStatus
 *功能描述: 获取发送状态
 *输入参数: 无
 *输出参数: 无
 *返回数据: 0:停止发送  1:正在发送
 *修改记录: 无
*********************************************************************** </pre>*/
uint8 UartGetTxStatus(void)
{
    return ((RLN32LST&_UART_TRANSMISSION_OPERATED) >> 4u);    /**<pre> RETURN 发送状态 </pre>*/
}

