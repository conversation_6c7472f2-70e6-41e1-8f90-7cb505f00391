/**********************************************************************
*   版权所有    : 2019 深圳市豪恩汽车电子装备有限公司
*     项目名称  : LVDS 长城F7 AVM
*     M      C       U         : R5F10BBF
*     编译环境  : IAR
*   文件名称    : DTC_Cfg.c
*   其它说明    : 
*   当前版本    : V0.1
*   作    者  : 21657
*   完成日期    :   
*   内容摘要    :
*   修改记录1   :
*   修改日期    :
*   版 本 号 :
*   修 改 人 :
*   修改内容    :
*   修改记录2   :

***********************************************************************/
/**********************************************************************
* Includes 
**********************************************************************/
#include "DTC_Cfg.h"
#include "EELHal.h"
#include "DTCRecordManage.h"
#include "DID.h"
#include "DID_Calibration.h"

/*********************************************************************
* Global objects 
**********************************************************************/

volatile uint8 GcDTCDetectionStatus[TOTAL_DTC_RECORD_NUMBER86];
boolean GcDTCDetectionInitStatusFlag = FALSE;
DTCInfomationStruct GsDTCInfomationStr[TOTAL_DTC_RECORD_NUMBER86];
DTCInfomationStruct GsDTCInfomationStrBak[TOTAL_DTC_RECORD_NUMBER86];
uint8 GcDTCAgingCntEnaFlg[TOTAL_DTC_RECORD_NUMBER86];     /* 故障老化计数使能 */


/* DTC 码定义 */

volatile uint32 GwDTCNumberCfg[TOTAL_DTC_NUMBER] =
{
    0xD02088,0xF00316,0xF00317,

    0xC07987,0xC12A87,//C12A87:FSD1 Missing--240222
    0xC14287,0xC13087,0xC12187,
    0xC24587,0xC07487,
    0xD4B081,0xD4B181,0xD4B381,
    0xD4B481,0xD4B681,
    0xD4B283,0xD4B282,0xD4B083,
    0xD4B082,0xD4B183,0xD4B182,
    0xD4B383,0xD4B382,
    0xD4B483,0xD4B482,0xD4B683,0xD4B682,

    0x510012,0x510014,0x510035,0x510097,
    0x510112,0x510114,0x510135,0x510197,
    0x510212,0x510214,0x510235,0x510297,
    0x510312,0x510314,0x510335,0x510397,
    0x510412,0x510414,0x510435,0x510497,
    0x510512,0x510514,0x510535,0x510597,
    0x510612,0x510614,0x510635,0x510697,
    0x510712,0x510714,0x510735,0x510797,
    0x510812,0x510814,0x510835,0x510897,
    0x510912,0x510914,0x510935,0x510997,
    0x510A12,0x510A14,0x510A35,0x510A97,
    0x510B12,0x510B14,0x510B35,0x510B97,

    0xC07562,0x50C955,0x50C854,
    0x50D042,0x50D147,

    0x510C12,0x510C14,
    0x510D12,0x510D14,
    0x510E12,0x510E14,
    /*Below DTC will not be recorded due to EEP memory limitation and those DTC will not be monitored in LEEA_X01 project*/
    0x510096,0x510054,
    0x510196,0x510154,
    0x510296,0x510254,
    0x510396,0x510354,
    0x510496,0x510454,
    0x510596,0x510554,
    0x510696,0x510654,
    0x510796,0x510754,
    0x510896,0x510854,
    0x510996,0x510954,
    0x510A96,0x510A54,
    0x510B96,0x510B54,

   /*新增12个DTC*/
    0x510098,0x510198,
    0x510298,0x510398,
    0x510498,0x510598,
    0x510698,0x510798,
    0x510898,0x510998,
    0x510A98,0x510B98,
};

uint8 Gu8PAS_PSLFuncAssocDTC[PASPSLDTCNUM] =
{
    DTC_CAN_BUSOFF_FAIL ,
    DTC_VOLTAGE_LOW_FAIL,//1
    DTC_VOLTAGE_HIGH_FAIL,
    DTC_MISSING_ACU,
    DTC_MISSING_ADAS,
    DTC_MISSING_FBCM,
    DTC_MISSING_EPS,
    DTC_MISSING_ESP,

    DTC_INVALID_ACU_DATA,
    DTC_INVALID_ADAS_DATA,
    DTC_INVALID_EPS_DATA,
    DTC_INVALID_ESP_DATA,

    DTC_INVALID_FBCM_CHECK,
    DTC_INVALID_FBCM_COUNTER,
    DTC_INVALID_ACU_CHECK,
    DTC_INVALID_ACU_COUNTER,
    DTC_INVALID_ADAS_CHECK,
    DTC_INVALID_ADAS_COUNTER,
    DTC_INVALID_EPS_CHECK,
    DTC_INVALID_EPS_COUNTER,
    DTC_INVALID_ESP_CHECK,
    DTC_INVALID_ESP_COUNTER,

    DTC_ADAS_SYNCTIME_FAILED ,
    DTC_UNCOMPLETE_CONFIG,

    DTC_ECU_INTERNAL_FAIL,
    DTC_FSC_SHORT_TO_BAT,
    DTC_FSC_SHORT_TO_GND,
    DTC_RSC_SHORT_TO_BAT,
    DTC_RSC_SHORT_TO_GND,
    DTC_SSC_SHORT_TO_BAT,
    DTC_SSC_SHORT_TO_GND,
};


uint8 Gu8PSLFuncAssocDTC[PSLDTCNUM] =
{

    DTC_MISSING_XCU,/*9*/
    DTC_INVALID_XCU_DATA,/*15*/
    DTC_INVALID_XCU_CHECK,
    DTC_INVALID_XCU_COUNTER,/*27*/
};

uint8 Gu8DTCWindow_List[DTC_WINDOW_NUM] =
{
    DTC_VOLTAGE_LOW_FAIL,
    DTC_VOLTAGE_HIGH_FAIL,
    DTC_ECU_INTERNAL_FAIL,
    DTC_WDG_SA_UC_FAIL,

    DTC_FLS_SHORT_UBATOR,/*USS01*/
    DTC_FLS_SHORT_GND_OR_OC,
    DTC_FLS_RINGTIME_FAILED,
    DTC_FLS_TYPE_MISMATCH,

    DTC_FL_SHORT_UBATOR,/*USS02*/
    DTC_FL_SHORT_GND_OR_OC,
    DTC_FL_RINGTIME_FAILED,
    DTC_FL_TYPE_MISMATCH,

    DTC_FML_SHORT_UBATOR,/*USS03*/
    DTC_FML_SHORT_GND_OR_OC,
    DTC_FML_RINGTIME_FAILED,
    DTC_FML_TYPE_MISMATCH,

    DTC_FMR_SHORT_UBATOR,/*USS04*/
    DTC_FMR_SHORT_GND_OR_OC,
    DTC_FMR_RINGTIME_FAILED,
    DTC_FMR_TYPE_MISMATCH,

    DTC_FR_SHORT_UBATOR,/*USS05*/
    DTC_FR_SHORT_GND_OR_OC,
    DTC_FR_RINGTIME_FAILED,
    DTC_FR_TYPE_MISMATCH,

    DTC_FRS_SHORT_UBATOR,/*USS06*/
    DTC_FRS_SHORT_GND_OR_OC,
    DTC_FRS_RINGTIME_FAILED,
    DTC_FRS_TYPE_MISMATCH,

    DTC_RRS_SHORT_UBATOR,/*USS07*/
    DTC_RRS_SHORT_GND_OR_OC,
    DTC_RRS_RINGTIME_FAILED,
    DTC_RRS_TYPE_MISMATCH,

    DTC_RR_SHORT_UBATOR,/*USS08*/
    DTC_RR_SHORT_GND_OR_OC,
    DTC_RR_RINGTIME_FAILED,
    DTC_RR_TYPE_MISMATCH,

    DTC_RMR_SHORT_UBATOR,/*USS09*/
    DTC_RMR_SHORT_GND_OR_OC,
    DTC_RMR_RINGTIME_FAILED,
    DTC_RMR_TYPE_MISMATCH,

    DTC_RML_SHORT_UBATOR,/*USS10*/
    DTC_RML_SHORT_GND_OR_OC,
    DTC_RML_RINGTIME_FAILED,
    DTC_RML_TYPE_MISMATCH,

    DTC_RL_SHORT_UBATOR,/*USS11*/
    DTC_RL_SHORT_GND_OR_OC,
    DTC_RL_RINGTIME_FAILED,
    DTC_RL_TYPE_MISMATCH,

    DTC_RLS_SHORT_UBATOR,/*USS12*/
    DTC_RLS_SHORT_GND_OR_OC,
    DTC_RLS_RINGTIME_FAILED,
    DTC_RLS_TYPE_MISMATCH,
};
/*********************************************************************
 * 函数名称:  DTCVariableInit
 *                                  
 * 功能描述:  DTC变量初始化     
 *                  
 * 入口参数:  无
 *
 * 输出参数:  无      
 *          
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 * 
 ********************************************************************/
 void DTCVariableInit(void)
{
    uint8 LcI;

    for(LcI = 0;LcI < TOTAL_DTC_RECORD_NUMBER86; LcI++)/*DTC variable initialisation*/
    {
        GcDTCAgingCntEnaFlg[LcI] = ENABLE;  /* Initialised to ENABLE at each power-up */
    }
}
/*********************************************************************
 * 函数名称:  SaveDTCInformationToDataFlash
 *                                  
 * 功能描述:  保存故障信息至DataFlash     
 *                  
 * 入口参数:  无
 *
 * 输出参数:  无      
 *          
 * 其它说明: 无
 *
 * 修改日期     版本号       修改人       修改内容
 * 
 ********************************************************************/
void SaveDTCInformationToDataFlash(void)
{   
    uint8 LcI;
    for(LcI=0;LcI< TOTAL_DTC_RECORD_NUMBER82;LcI++)
    {
        if((GsDTCInfomationStrBak[LcI].DTCSTATUS.GcDTCStatus != GsDTCInfomationStr[LcI].DTCSTATUS.GcDTCStatus)||/* DTC status has updata?*/
           (GsDTCInfomationStrBak[LcI].GcAgingCounter != GsDTCInfomationStr[LcI].GcAgingCounter)||/* Aging counter has updata?*/
           (GsDTCInfomationStrBak[LcI].GcDTCOccurrenceCounter != GsDTCInfomationStr[LcI].GcDTCOccurrenceCounter))/*occurrence counter has updata?*/
        {
            /*If the above conditions are updated, the new data will be written to EEL.*/
            (void)EELWriteDataImmediate(GsDTCInfomationStr[LcI].GcSnapshotData, (DTC_RECORD_1_ADDRESS  + LcI * ONE_DTC_STORGE_LEN), ONE_DTC_STORGE_LEN);
            GsDTCInfomationStrBak[LcI].DTCSTATUS.GcDTCStatus = GsDTCInfomationStr[LcI].DTCSTATUS.GcDTCStatus;/*Updata Dtc status to backup buffer*/
            GsDTCInfomationStrBak[LcI].GcAgingCounter = GsDTCInfomationStr[LcI].GcAgingCounter;/*Updata Aging counter to backup buffer*/
            GsDTCInfomationStrBak[LcI].GcDTCOccurrenceCounter = GsDTCInfomationStr[LcI].GcDTCOccurrenceCounter;/*Updata occurrence counter to backup buffer*/
            break;
         }
    }

    LcI = DTC_RSC_SHORT_TO_BAT;
    if ((GsDTCInfomationStrBak[LcI].DTCSTATUS.GcDTCStatus != GsDTCInfomationStr[LcI].DTCSTATUS.GcDTCStatus) || /* DTC status has updata?*/
        (GsDTCInfomationStrBak[LcI].GcAgingCounter != GsDTCInfomationStr[LcI].GcAgingCounter) || /* Aging counter has updata?*/
        (GsDTCInfomationStrBak[LcI].GcDTCOccurrenceCounter != GsDTCInfomationStr[LcI].GcDTCOccurrenceCounter))/*occurrence counter has updata?*/
    {
        /*If the above conditions are updated, the new data will be written to EEL.*/
        (void)EELWriteDataImmediate(GsDTCInfomationStr[LcI].GcSnapshotData, (DTC_RECORD_83_ADDRESS), ONE_DTC_STORGE_LEN);
        GsDTCInfomationStrBak[LcI].DTCSTATUS.GcDTCStatus = GsDTCInfomationStr[LcI].DTCSTATUS.GcDTCStatus;/*Updata Dtc status to backup buffer*/
        GsDTCInfomationStrBak[LcI].GcAgingCounter = GsDTCInfomationStr[LcI].GcAgingCounter;/*Updata Aging counter to backup buffer*/
        GsDTCInfomationStrBak[LcI].GcDTCOccurrenceCounter = GsDTCInfomationStr[LcI].GcDTCOccurrenceCounter;/*Updata occurrence counter to backup buffer*/
    }
    LcI = DTC_RSC_SHORT_TO_GND;
    if ((GsDTCInfomationStrBak[LcI].DTCSTATUS.GcDTCStatus != GsDTCInfomationStr[LcI].DTCSTATUS.GcDTCStatus) || /* DTC status has updata?*/
        (GsDTCInfomationStrBak[LcI].GcAgingCounter != GsDTCInfomationStr[LcI].GcAgingCounter) || /* Aging counter has updata?*/
        (GsDTCInfomationStrBak[LcI].GcDTCOccurrenceCounter != GsDTCInfomationStr[LcI].GcDTCOccurrenceCounter))/*occurrence counter has updata?*/
    {
        /*If the above conditions are updated, the new data will be written to EEL.*/
        (void)EELWriteDataImmediate(GsDTCInfomationStr[LcI].GcSnapshotData, (DTC_RECORD_84_ADDRESS), ONE_DTC_STORGE_LEN);
        GsDTCInfomationStrBak[LcI].DTCSTATUS.GcDTCStatus = GsDTCInfomationStr[LcI].DTCSTATUS.GcDTCStatus;/*Updata Dtc status to backup buffer*/
        GsDTCInfomationStrBak[LcI].GcAgingCounter = GsDTCInfomationStr[LcI].GcAgingCounter;/*Updata Aging counter to backup buffer*/
        GsDTCInfomationStrBak[LcI].GcDTCOccurrenceCounter = GsDTCInfomationStr[LcI].GcDTCOccurrenceCounter;/*Updata occurrence counter to backup buffer*/
    }
    LcI = DTC_SSC_SHORT_TO_BAT;
    if ((GsDTCInfomationStrBak[LcI].DTCSTATUS.GcDTCStatus != GsDTCInfomationStr[LcI].DTCSTATUS.GcDTCStatus) || /* DTC status has updata?*/
        (GsDTCInfomationStrBak[LcI].GcAgingCounter != GsDTCInfomationStr[LcI].GcAgingCounter) || /* Aging counter has updata?*/
        (GsDTCInfomationStrBak[LcI].GcDTCOccurrenceCounter != GsDTCInfomationStr[LcI].GcDTCOccurrenceCounter))/*occurrence counter has updata?*/
    {
        /*If the above conditions are updated, the new data will be written to EEL.*/
        (void)EELWriteDataImmediate(GsDTCInfomationStr[LcI].GcSnapshotData, (DTC_RECORD_85_ADDRESS), ONE_DTC_STORGE_LEN);
        GsDTCInfomationStrBak[LcI].DTCSTATUS.GcDTCStatus = GsDTCInfomationStr[LcI].DTCSTATUS.GcDTCStatus;/*Updata Dtc status to backup buffer*/
        GsDTCInfomationStrBak[LcI].GcAgingCounter = GsDTCInfomationStr[LcI].GcAgingCounter;/*Updata Aging counter to backup buffer*/
        GsDTCInfomationStrBak[LcI].GcDTCOccurrenceCounter = GsDTCInfomationStr[LcI].GcDTCOccurrenceCounter;/*Updata occurrence counter to backup buffer*/
    }
    LcI = DTC_SSC_SHORT_TO_GND;
    if ((GsDTCInfomationStrBak[LcI].DTCSTATUS.GcDTCStatus != GsDTCInfomationStr[LcI].DTCSTATUS.GcDTCStatus) || /* DTC status has updata?*/
        (GsDTCInfomationStrBak[LcI].GcAgingCounter != GsDTCInfomationStr[LcI].GcAgingCounter) || /* Aging counter has updata?*/
        (GsDTCInfomationStrBak[LcI].GcDTCOccurrenceCounter != GsDTCInfomationStr[LcI].GcDTCOccurrenceCounter))/*occurrence counter has updata?*/
    {
        /*If the above conditions are updated, the new data will be written to EEL.*/
        (void)EELWriteDataImmediate(GsDTCInfomationStr[LcI].GcSnapshotData, (DTC_RECORD_86_ADDRESS), ONE_DTC_STORGE_LEN);
        GsDTCInfomationStrBak[LcI].DTCSTATUS.GcDTCStatus = GsDTCInfomationStr[LcI].DTCSTATUS.GcDTCStatus;/*Updata Dtc status to backup buffer*/
        GsDTCInfomationStrBak[LcI].GcAgingCounter = GsDTCInfomationStr[LcI].GcAgingCounter;/*Updata Aging counter to backup buffer*/
        GsDTCInfomationStrBak[LcI].GcDTCOccurrenceCounter = GsDTCInfomationStr[LcI].GcDTCOccurrenceCounter;/*Updata occurrence counter to backup buffer*/
    }
}
/*********************************************************************
 * 函数名称:  SaveALLDTCImmediately
 *                                  
 * 功能描述:  立即保存所有故障信息     
 *                  
 * 入口参数:  无
 *
 * 输出参数:  无      
 *          
 * 其它说明: 无
 *
 * 修改日期     版本号       修改人       修改内容
 * 
 ********************************************************************/
void SaveALLDTCImmediately(void)
{   
    uint8 LcI;
    for(LcI=0;LcI< TOTAL_DTC_RECORD_NUMBER82;LcI++)
    {
        if((GsDTCInfomationStrBak[LcI].DTCSTATUS.GcDTCStatus != GsDTCInfomationStr[LcI].DTCSTATUS.GcDTCStatus)||/* DTC status has updata?*/
           (GsDTCInfomationStrBak[LcI].GcAgingCounter != GsDTCInfomationStr[LcI].GcAgingCounter)||/* Aging counter has updata?*/
           (GsDTCInfomationStrBak[LcI].GcDTCOccurrenceCounter != GsDTCInfomationStr[LcI].GcDTCOccurrenceCounter))/*occurrence counter has updata?*/
        {
            /*If the above conditions are updated, the new data will be written to EEL.*/
            (void)EELWriteDataImmediate(GsDTCInfomationStr[LcI].GcSnapshotData, (DTC_RECORD_1_ADDRESS  + LcI * ONE_DTC_STORGE_LEN), ONE_DTC_STORGE_LEN);
            GsDTCInfomationStrBak[LcI].DTCSTATUS.GcDTCStatus = GsDTCInfomationStr[LcI].DTCSTATUS.GcDTCStatus;/*Updata Dtc status to backup buffer*/
            GsDTCInfomationStrBak[LcI].GcAgingCounter = GsDTCInfomationStr[LcI].GcAgingCounter;/*Updata Aging counter to backup buffer*/
            GsDTCInfomationStrBak[LcI].GcDTCOccurrenceCounter = GsDTCInfomationStr[LcI].GcDTCOccurrenceCounter;/*Updata occurrence counter to backup buffer*/
         }
    }
    LcI = DTC_RSC_SHORT_TO_BAT;
    if ((GsDTCInfomationStrBak[LcI].DTCSTATUS.GcDTCStatus != GsDTCInfomationStr[LcI].DTCSTATUS.GcDTCStatus) || /* DTC status has updata?*/
        (GsDTCInfomationStrBak[LcI].GcAgingCounter != GsDTCInfomationStr[LcI].GcAgingCounter) || /* Aging counter has updata?*/
        (GsDTCInfomationStrBak[LcI].GcDTCOccurrenceCounter != GsDTCInfomationStr[LcI].GcDTCOccurrenceCounter))/*occurrence counter has updata?*/
    {
        /*If the above conditions are updated, the new data will be written to EEL.*/
        (void)EELWriteDataImmediate(GsDTCInfomationStr[LcI].GcSnapshotData, (DTC_RECORD_83_ADDRESS), ONE_DTC_STORGE_LEN);
        GsDTCInfomationStrBak[LcI].DTCSTATUS.GcDTCStatus = GsDTCInfomationStr[LcI].DTCSTATUS.GcDTCStatus;/*Updata Dtc status to backup buffer*/
        GsDTCInfomationStrBak[LcI].GcAgingCounter = GsDTCInfomationStr[LcI].GcAgingCounter;/*Updata Aging counter to backup buffer*/
        GsDTCInfomationStrBak[LcI].GcDTCOccurrenceCounter = GsDTCInfomationStr[LcI].GcDTCOccurrenceCounter;/*Updata occurrence counter to backup buffer*/
    }
    LcI = DTC_RSC_SHORT_TO_GND;
    if ((GsDTCInfomationStrBak[LcI].DTCSTATUS.GcDTCStatus != GsDTCInfomationStr[LcI].DTCSTATUS.GcDTCStatus) || /* DTC status has updata?*/
        (GsDTCInfomationStrBak[LcI].GcAgingCounter != GsDTCInfomationStr[LcI].GcAgingCounter) || /* Aging counter has updata?*/
        (GsDTCInfomationStrBak[LcI].GcDTCOccurrenceCounter != GsDTCInfomationStr[LcI].GcDTCOccurrenceCounter))/*occurrence counter has updata?*/
    {
        /*If the above conditions are updated, the new data will be written to EEL.*/
        (void)EELWriteDataImmediate(GsDTCInfomationStr[LcI].GcSnapshotData, (DTC_RECORD_84_ADDRESS), ONE_DTC_STORGE_LEN);
        GsDTCInfomationStrBak[LcI].DTCSTATUS.GcDTCStatus = GsDTCInfomationStr[LcI].DTCSTATUS.GcDTCStatus;/*Updata Dtc status to backup buffer*/
        GsDTCInfomationStrBak[LcI].GcAgingCounter = GsDTCInfomationStr[LcI].GcAgingCounter;/*Updata Aging counter to backup buffer*/
        GsDTCInfomationStrBak[LcI].GcDTCOccurrenceCounter = GsDTCInfomationStr[LcI].GcDTCOccurrenceCounter;/*Updata occurrence counter to backup buffer*/
    }
    LcI = DTC_SSC_SHORT_TO_BAT;
    if ((GsDTCInfomationStrBak[LcI].DTCSTATUS.GcDTCStatus != GsDTCInfomationStr[LcI].DTCSTATUS.GcDTCStatus) || /* DTC status has updata?*/
        (GsDTCInfomationStrBak[LcI].GcAgingCounter != GsDTCInfomationStr[LcI].GcAgingCounter) || /* Aging counter has updata?*/
        (GsDTCInfomationStrBak[LcI].GcDTCOccurrenceCounter != GsDTCInfomationStr[LcI].GcDTCOccurrenceCounter))/*occurrence counter has updata?*/
    {
        /*If the above conditions are updated, the new data will be written to EEL.*/
        (void)EELWriteDataImmediate(GsDTCInfomationStr[LcI].GcSnapshotData, (DTC_RECORD_85_ADDRESS), ONE_DTC_STORGE_LEN);
        GsDTCInfomationStrBak[LcI].DTCSTATUS.GcDTCStatus = GsDTCInfomationStr[LcI].DTCSTATUS.GcDTCStatus;/*Updata Dtc status to backup buffer*/
        GsDTCInfomationStrBak[LcI].GcAgingCounter = GsDTCInfomationStr[LcI].GcAgingCounter;/*Updata Aging counter to backup buffer*/
        GsDTCInfomationStrBak[LcI].GcDTCOccurrenceCounter = GsDTCInfomationStr[LcI].GcDTCOccurrenceCounter;/*Updata occurrence counter to backup buffer*/
    }
    LcI = DTC_SSC_SHORT_TO_GND;
    if ((GsDTCInfomationStrBak[LcI].DTCSTATUS.GcDTCStatus != GsDTCInfomationStr[LcI].DTCSTATUS.GcDTCStatus) || /* DTC status has updata?*/
        (GsDTCInfomationStrBak[LcI].GcAgingCounter != GsDTCInfomationStr[LcI].GcAgingCounter) || /* Aging counter has updata?*/
        (GsDTCInfomationStrBak[LcI].GcDTCOccurrenceCounter != GsDTCInfomationStr[LcI].GcDTCOccurrenceCounter))/*occurrence counter has updata?*/
    {
        /*If the above conditions are updated, the new data will be written to EEL.*/
        (void)EELWriteDataImmediate(GsDTCInfomationStr[LcI].GcSnapshotData, (DTC_RECORD_86_ADDRESS), ONE_DTC_STORGE_LEN);
        GsDTCInfomationStrBak[LcI].DTCSTATUS.GcDTCStatus = GsDTCInfomationStr[LcI].DTCSTATUS.GcDTCStatus;/*Updata Dtc status to backup buffer*/
        GsDTCInfomationStrBak[LcI].GcAgingCounter = GsDTCInfomationStr[LcI].GcAgingCounter;/*Updata Aging counter to backup buffer*/
        GsDTCInfomationStrBak[LcI].GcDTCOccurrenceCounter = GsDTCInfomationStr[LcI].GcDTCOccurrenceCounter;/*Updata occurrence counter to backup buffer*/
    }
}

/*********************************************************************
 * 函数名称:  SaveDTCInforToDataFlash
 *                                  
 * 功能描述:  保存故障信息至DataFlash     
 *                  
 * 入口参数:  无
 *
 * 输出参数:  无      
 *          
 * 其它说明: 无
 *
 * 修改日期     版本号       修改人       修改内容
 * 
 ********************************************************************/
void SaveDTCInforToDataFlash(uint8 LcDTC_INDEX)
{
    if(LcDTC_INDEX < TOTAL_DTC_RECORD_NUMBER82)
    {
        /* written to EEL*/
        (void)EELWriteDataImmediate(GsDTCInfomationStr[LcDTC_INDEX].GcSnapshotData, (DTC_RECORD_1_ADDRESS  + LcDTC_INDEX * ONE_DTC_STORGE_LEN), ONE_DTC_STORGE_LEN);
        GsDTCInfomationStrBak[LcDTC_INDEX].DTCSTATUS.GcDTCStatus = GsDTCInfomationStr[LcDTC_INDEX].DTCSTATUS.GcDTCStatus;/*Updata Dtc status to backup buffer*/
        GsDTCInfomationStrBak[LcDTC_INDEX].GcAgingCounter = GsDTCInfomationStr[LcDTC_INDEX].GcAgingCounter;/*Updata Aging counter to backup buffer*/
        GsDTCInfomationStrBak[LcDTC_INDEX].GcDTCOccurrenceCounter = GsDTCInfomationStr[LcDTC_INDEX].GcDTCOccurrenceCounter;/*Updata occurrence counter to backup buffer*/
    }
    else if (LcDTC_INDEX == DTC_RSC_SHORT_TO_BAT)
    {
        /* written to EEL*/
        (void)EELWriteDataImmediate(GsDTCInfomationStr[LcDTC_INDEX].GcSnapshotData, (DTC_RECORD_83_ADDRESS ), ONE_DTC_STORGE_LEN);
        GsDTCInfomationStrBak[LcDTC_INDEX].DTCSTATUS.GcDTCStatus = GsDTCInfomationStr[LcDTC_INDEX].DTCSTATUS.GcDTCStatus;/*Updata Dtc status to backup buffer*/
        GsDTCInfomationStrBak[LcDTC_INDEX].GcAgingCounter = GsDTCInfomationStr[LcDTC_INDEX].GcAgingCounter;/*Updata Aging counter to backup buffer*/
        GsDTCInfomationStrBak[LcDTC_INDEX].GcDTCOccurrenceCounter = GsDTCInfomationStr[LcDTC_INDEX].GcDTCOccurrenceCounter;/*Updata occurrence counter to backup buffer*/
    }
    else if (LcDTC_INDEX == DTC_RSC_SHORT_TO_GND)
    {
        /* written to EEL*/
        (void)EELWriteDataImmediate(GsDTCInfomationStr[LcDTC_INDEX].GcSnapshotData, (DTC_RECORD_84_ADDRESS), ONE_DTC_STORGE_LEN);
        GsDTCInfomationStrBak[LcDTC_INDEX].DTCSTATUS.GcDTCStatus = GsDTCInfomationStr[LcDTC_INDEX].DTCSTATUS.GcDTCStatus;/*Updata Dtc status to backup buffer*/
        GsDTCInfomationStrBak[LcDTC_INDEX].GcAgingCounter = GsDTCInfomationStr[LcDTC_INDEX].GcAgingCounter;/*Updata Aging counter to backup buffer*/
        GsDTCInfomationStrBak[LcDTC_INDEX].GcDTCOccurrenceCounter = GsDTCInfomationStr[LcDTC_INDEX].GcDTCOccurrenceCounter;/*Updata occurrence counter to backup buffer*/
    }
    else if (LcDTC_INDEX == DTC_SSC_SHORT_TO_BAT)
    {
        /* written to EEL*/
        (void)EELWriteDataImmediate(GsDTCInfomationStr[LcDTC_INDEX].GcSnapshotData, (DTC_RECORD_85_ADDRESS), ONE_DTC_STORGE_LEN);
        GsDTCInfomationStrBak[LcDTC_INDEX].DTCSTATUS.GcDTCStatus = GsDTCInfomationStr[LcDTC_INDEX].DTCSTATUS.GcDTCStatus;/*Updata Dtc status to backup buffer*/
        GsDTCInfomationStrBak[LcDTC_INDEX].GcAgingCounter = GsDTCInfomationStr[LcDTC_INDEX].GcAgingCounter;/*Updata Aging counter to backup buffer*/
        GsDTCInfomationStrBak[LcDTC_INDEX].GcDTCOccurrenceCounter = GsDTCInfomationStr[LcDTC_INDEX].GcDTCOccurrenceCounter;/*Updata occurrence counter to backup buffer*/
    }
    else if (LcDTC_INDEX == DTC_SSC_SHORT_TO_GND)
    {
        /* written to EEL*/
        (void)EELWriteDataImmediate(GsDTCInfomationStr[LcDTC_INDEX].GcSnapshotData, (DTC_RECORD_86_ADDRESS), ONE_DTC_STORGE_LEN);
        GsDTCInfomationStrBak[LcDTC_INDEX].DTCSTATUS.GcDTCStatus = GsDTCInfomationStr[LcDTC_INDEX].DTCSTATUS.GcDTCStatus;/*Updata Dtc status to backup buffer*/
        GsDTCInfomationStrBak[LcDTC_INDEX].GcAgingCounter = GsDTCInfomationStr[LcDTC_INDEX].GcAgingCounter;/*Updata Aging counter to backup buffer*/
        GsDTCInfomationStrBak[LcDTC_INDEX].GcDTCOccurrenceCounter = GsDTCInfomationStr[LcDTC_INDEX].GcDTCOccurrenceCounter;/*Updata occurrence counter to backup buffer*/
    }
    else
    {
        //目前支持存储的DTC,仅存86个DTC
    }
}
