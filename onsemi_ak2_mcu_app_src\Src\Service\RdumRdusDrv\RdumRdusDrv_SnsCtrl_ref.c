/******************************************************************************
 * @file      RdumRdusDrv_SnsCtrl.c
 * @brief     安森美超声波探头控制实现
 * <AUTHOR>
 * @date      2025-05-14
 * @note      
 *****************************************************************************/

/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include <string.h>
#include "RdumRdusDrv_SnsCtrl.h"
#include "SpiCom_Prg.h"
#include "SpiCmd.h"
#include "BaseDrv.h"



/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/
/* 探头重新发现检查控制 */
typedef struct
{
    RdumRdusDMSeq_en RdumRdusDMSeq;         /* 探头发现状态机 */
    bool StartTime1msCnt;                    /* 开始计时标志 */
    uint32 ReDMCheckTime1msCnt;              /* 重新发现检查时间计数 */
} RdumRdusReDMCheckCtrl;



/******************************************************************************
 * @Const Declaration
 *****************************************************************************/



/******************************************************************************
 * @Variable Declaration
 *****************************************************************************/
/* 探头控制组 */
static RdumRdusGroup_Cfg RdumRdusGroup;

/* 探头重新发现检查控制 */
static RdumRdusReDMCheckCtrl GstrRdumRdusReDMCheckCtrl = 
{
    .RdumRdusDMSeq = eRdumRdusDMSeq_Init,
    .StartTime1msCnt = FALSE,
    .ReDMCheckTime1msCnt = 0,
};

/* 探头发现状态机 */
static RdumRdusDMSeq_en RdumRdusDMSeq = eRdumRdusDMSeq_Init;

/* 探头发现重试计数 */
static uint8 DMRetryCnt = RDUM_RDUS_SNS_DM_RETRY_CNT;

/* 待机标志 */
static uint8 StandbyFlg = 0;

/* 探头发现完成标志 */
static bool DMDone = FALSE;



/******************************************************************************
 * @Function Definitions
 *****************************************************************************/

/******************************************************************************
 * @brief      初始化探头控制组
 * <AUTHOR>
 * @date       2025-05-14
 * @note       
 *****************************************************************************/
void RdumRdusDrv_InitSnsCtrlGroup(void)
{
    Dsi3Channel_t Dsi3Ch;
    
    memset(&RdumRdusGroup, 0, sizeof(RdumRdusGroup_Cfg));
    RdumRdusGroup.GroupStatus = RdumRdusGroupStatus_SNS_DM;
    RdumRdusGroup.Time1msCnt = 0; 
    RdumRdusGroup.TempCompTime1msCnt = RDUM_RDUS_SNS_TEMPCOMP_PERIOD;
    RdumRdusGroup.StartMeasFlg = TRUE;
    
    for (Dsi3Ch = DSI3_CHANNEL_A; Dsi3Ch < DSI3_CHANNEL_NUM; Dsi3Ch++)
    {
        RdumRdusGroup.SnsGroupMeasStatus[Dsi3Ch] = RdumRdusGroupMeas_IDLE;
        RdumRdusGroup.SnsGroupMeasTime[Dsi3Ch] = 0;
    }
}

/******************************************************************************
 * @brief      探头控制主函数
 * <AUTHOR>
 * @date       2025-05-14
 * @note       
 *****************************************************************************/
void RdumRdusDrv_SnsCtrlMainFunc(void)
{
    RdumRdusGroup_Cfg *pRdumRdusGroup = &RdumRdusGroup;
    Dsi3Channel_t Dsi3Ch;
    static uint8 SelfTestFlg = 0;
    
    switch (pRdumRdusGroup->GroupStatus)
    {
        case RdumRdusGroupStatus_SNS_DM:
        {
            /* 探头发现模式 */
            if (DMDone == TRUE)
            {
                /* 探头发现完成，进入自检状态 */
                pRdumRdusGroup->GroupStatus = RdumRdusGroupStatus_SNS_SELFTEST;
                pRdumRdusGroup->Time1msCnt = 0;
                SelfTestFlg = 1;
            }
            else
            {
                /* 执行探头发现 */
                RdumRdusDrv_DiscoveryMode();
            }
            break;
        }
        
        case RdumRdusGroupStatus_SNS_SELFTEST:
        {
            /* 探头自检 */
            if (SelfTestFlg == 1)
            {
                /* 发送自检命令 */
                for (Dsi3Ch = DSI3_CHANNEL_A; Dsi3Ch < DSI3_CHANNEL_NUM; Dsi3Ch++)
                {
                    Dsi3Cmd_InitRdumRdus(Dsi3Ch, DSI3_ID_ALL);
                }
                
                SelfTestFlg = 0;
                pRdumRdusGroup->GroupStatus = RdumRdusGroupStatus_WAIT_SELFTEST;
                pRdumRdusGroup->Time1msCnt = 0;
            }
            break;
        }
        
        case RdumRdusGroupStatus_WAIT_SELFTEST:
        {
            /* 等待自检完成 */
            if (pRdumRdusGroup->Time1msCnt > RDUM_RDUS_SNS_WAIT_SELFTEST_TIME)
            {
                /* 自检完成，进入开始测量状态 */
                pRdumRdusGroup->GroupStatus = RdumRdusGroupStatus_START_MEAS;
                pRdumRdusGroup->Time1msCnt = 0;
            }
            break;
        }
        
        case RdumRdusGroupStatus_START_MEAS:
        {
            /* 开始测量 */
            for (Dsi3Ch = DSI3_CHANNEL_A; Dsi3Ch < DSI3_CHANNEL_NUM; Dsi3Ch++)
            {
                /* 发送开始测量命令 */
                Dsi3Cmd_RdumRdusStartMeasurement(Dsi3Ch, DSI3_ID_ALL, MEAS_MODE_NEAR_RANGE_2PERIOD);
                
                /* 更新测量状态 */
                pRdumRdusGroup->SnsGroupMeasStatus[Dsi3Ch] = RdumRdusGroupMeas_BUSY_MEAS;
                pRdumRdusGroup->SnsGroupMeasTime[Dsi3Ch] = 0;
            }
            
            pRdumRdusGroup->GroupStatus = RdumRdusGroupStatus_WAIT_MEAS_COMPLETE;
            pRdumRdusGroup->Time1msCnt = 0;
            break;
        }
        
        case RdumRdusGroupStatus_WAIT_MEAS_COMPLETE:
        {
            /* 等待测量完成 */
            if (pRdumRdusGroup->Time1msCnt > RDUM_RDUS_SNS_WAIT_MEAS_TIMEOUT)
            {
                /* 测量超时，停止测量 */
                for (Dsi3Ch = DSI3_CHANNEL_A; Dsi3Ch < DSI3_CHANNEL_NUM; Dsi3Ch++)
                {
                    pRdumRdusGroup->SnsGroupMeasStatus[Dsi3Ch] = RdumRdusGroupMeas_IDLE;
                }
                
                /* 检查是否需要温度补偿 */
                if (pRdumRdusGroup->TempCompTime1msCnt >= RDUM_RDUS_SNS_TEMPCOMP_PERIOD)
                {
                    pRdumRdusGroup->GroupStatus = RdumRdusGroupStatus_TEMP_COMP;
                    pRdumRdusGroup->TempCompTime1msCnt = 0;
                }
                else
                {
                    /* 继续测量 */
                    pRdumRdusGroup->GroupStatus = RdumRdusGroupStatus_START_MEAS;
                }
                
                pRdumRdusGroup->Time1msCnt = 0;
            }
            break;
        }
        
        case RdumRdusGroupStatus_TEMP_COMP:
        {
            /* 温度补偿 */
            for (Dsi3Ch = DSI3_CHANNEL_A; Dsi3Ch < DSI3_CHANNEL_NUM; Dsi3Ch++)
            {
                /* 读取温度状态 */
                Dsi3Cmd_RdumRdusReadStatus(Dsi3Ch, DSI3_ID_ALL, STATUS_ADDR_TEMP);
            }
            
            pRdumRdusGroup->GroupStatus = RdumRdusGroupStatus_WAIT_TEMP_COMP;
            pRdumRdusGroup->Time1msCnt = 0;
            break;
        }
        
        case RdumRdusGroupStatus_WAIT_TEMP_COMP:
        {
            /* 等待温度补偿完成 */
            if (pRdumRdusGroup->Time1msCnt > 10)
            {
                /* 温度补偿完成，继续测量 */
                pRdumRdusGroup->GroupStatus = RdumRdusGroupStatus_START_MEAS;
                pRdumRdusGroup->Time1msCnt = 0;
            }
            break;
        }
        
        case RdumRdusGroupStatus_STANDBY:
        {
            /* 待机模式 */
            if (StandbyFlg == 0)
            {
                /* 发送待机命令 */
                for (Dsi3Ch = DSI3_CHANNEL_A; Dsi3Ch < DSI3_CHANNEL_NUM; Dsi3Ch++)
                {
                    Dsi3Cmd_RdumRdusIcModeSet(Dsi3Ch, DSI3_ID_ALL, IC_MODE_STANDBY);
                }
                
                StandbyFlg = 1;
                pRdumRdusGroup->GroupStatus = RdumRdusGroupStatus_WAIT_STANDBY;
                pRdumRdusGroup->Time1msCnt = 0;
            }
            break;
        }
        
        case RdumRdusGroupStatus_WAIT_STANDBY:
        {
            /* 等待进入待机 */
            if (pRdumRdusGroup->Time1msCnt > RDUM_RDUS_SNS_WAIT_STANDBY_TIME)
            {
                /* 待机完成 */
                pRdumRdusGroup->GroupStatus = RdumRdusDrv_GetNextSnsCtrlGroupStatus();
                pRdumRdusGroup->Time1msCnt = 0;
            }
            break;
        }
        
        case RdumRdusGroupStatus_WAKEUP:
        {
            /* 唤醒模式 */
            if (StandbyFlg == 1)
            {
                /* 发送唤醒命令 */
                for (Dsi3Ch = DSI3_CHANNEL_A; Dsi3Ch < DSI3_CHANNEL_NUM; Dsi3Ch++)
                {
                    Dsi3Cmd_RdumRdusIcModeSet(Dsi3Ch, DSI3_ID_ALL, IC_MODE_WAKE_UP);
                }
                
                StandbyFlg = 0;
                pRdumRdusGroup->GroupStatus = RdumRdusGroupStatus_WAIT_WAKEUP;
                pRdumRdusGroup->Time1msCnt = 0;
            }
            break;
        }
        
        case RdumRdusGroupStatus_WAIT_WAKEUP:
        {
            /* 等待唤醒完成 */
            if (pRdumRdusGroup->Time1msCnt > RDUM_RDUS_SNS_WAIT_WAKEUP_TIME)
            {
                /* 唤醒完成 */
                pRdumRdusGroup->GroupStatus = RdumRdusDrv_GetNextSnsCtrlGroupStatus();
                pRdumRdusGroup->Time1msCnt = 0;
            }
            break;
        }
        
        default:
            break;
    }
}

/******************************************************************************
 * @brief      探头发现模式
 * @return     1: 发现完成, 0: 发现中
 * <AUTHOR>
 * @date       2025-05-14
 * @note       
 *****************************************************************************/
uint8 RdumRdusDrv_DiscoveryMode(void)
{
    RdumRdusGroup_Cfg *pRdumRdusGroup = &RdumRdusGroup;
    Dsi3Channel_t Dsi3Ch;
    static uint8 ScIOCnt = 0;
    
    switch (RdumRdusDMSeq)
    {
        case eRdumRdusDMSeq_Init:
        {
            /* 初始化 */
            ScIOCnt = 0;
            RdumRdusDMSeq = eRdumRdusDMSeq_StartAutoDM;
            break;
        }
        
        case eRdumRdusDMSeq_StartAutoDM:
        {
            /* 开始自动发现 */
            for (Dsi3Ch = DSI3_CHANNEL_A; Dsi3Ch < DSI3_CHANNEL_NUM; Dsi3Ch++)
            {
                /* 发送地址发现命令 */
                Dsi3Cmd_InitRdumRdus(Dsi3Ch, DSI3_ID_ALL);
            }
            
            RdumRdusDMSeq = eRdumRdusDMSeq_TransChangeID;
            pRdumRdusGroup->Time1msCnt = 0;
            break;
        }
        
        case eRdumRdusDMSeq_TransChangeID:
        {
            /* 传输改变ID命令 */
            if (pRdumRdusGroup->Time1msCnt > 10)
            {
                /* 为每个探头分配ID */
                for (Dsi3Ch = DSI3_CHANNEL_A; Dsi3Ch < DSI3_CHANNEL_NUM; Dsi3Ch++)
                {
                    /* 发送EEPROM访问命令，编程从机ID */
                    Dsi3Cmd_RdumRdusEepromAccess(Dsi3Ch, ScIOCnt + 1, EEPROM_OP_PROG_SLAVE_ID);
                }
                
                RdumRdusDMSeq = eRdumRdusDMSeq_TransReadEEPROMID;
                pRdumRdusGroup->Time1msCnt = 0;
            }
            break;
        }
        
        case eRdumRdusDMSeq_TransReadEEPROMID:
        {
            /* 读取EEPROM ID */
            if (pRdumRdusGroup->Time1msCnt > 10)
            {
                /* 读取每个探头的ID */
                for (Dsi3Ch = DSI3_CHANNEL_A; Dsi3Ch < DSI3_CHANNEL_NUM; Dsi3Ch++)
                {
                    /* 发送EEPROM访问命令，从EEPROM到RAM */
                    Dsi3Cmd_RdumRdusEepromAccess(Dsi3Ch, ScIOCnt + 1, EEPROM_OP_EEPROM_TO_RAM);
                }
                
                RdumRdusDMSeq = eRdumRdusDMSeq_PrepareNextDM;
                pRdumRdusGroup->Time1msCnt = 0;
            }
            break;
        }
        
        case eRdumRdusDMSeq_PrepareNextDM:
        {
            /* 准备下一次发现 */
            if (pRdumRdusGroup->Time1msCnt > 10)
            {
                ScIOCnt++;
                if (ScIOCnt >= 4) /* 假设最多支持4个探头 */
                {
                    ScIOCnt = 0;
                    RdumRdusDMSeq = eRdumRdusDMSeq_End;
                }
                else
                {
                    RdumRdusDMSeq = eRdumRdusDMSeq_StartAutoDM;
                }
                
                pRdumRdusGroup->Time1msCnt = 0;
            }
            break;
        }
        
        case eRdumRdusDMSeq_End:
        {
            /* 结束发现 */
            RdumRdusDMSeq = eRdumRdusDMSeq_Init;
            ScIOCnt = 0;
            DMDone = TRUE;
            return 1;
        }
        
        default:
            break;
    }
    
    return 0;
}

/******************************************************************************
 * @brief      更新时间计数
 * <AUTHOR>
 * @date       2025-05-14
 * @note       
 *****************************************************************************/
void RdumRdusDrv_UpdateTimeCnt(void)
{
    Dsi3Channel_t Dsi3Ch;
    
    RdumRdusGroup.Time1msCnt++;
    RdumRdusGroup.TempCompTime1msCnt++;
    GstrRdumRdusReDMCheckCtrl.ReDMCheckTime1msCnt++;
    
    for (Dsi3Ch = DSI3_CHANNEL_A; Dsi3Ch < DSI3_CHANNEL_NUM; Dsi3Ch++)
    {
        if (RdumRdusGroup.SnsGroupMeasStatus[Dsi3Ch] == RdumRdusGroupMeas_BUSY_MEAS)
        {
            RdumRdusGroup.SnsGroupMeasTime[Dsi3Ch]++;
        }
    }
}

/******************************************************************************
 * @brief      设置下一个控制组状态
 * @param[in]  NextStatus 下一个状态
 * <AUTHOR>
 * @date       2025-05-14
 * @note       
 *****************************************************************************/
void RdumRdusDrv_SetNextSnsCtrlGroupStatus(RdumRdusGroupStatus_en NextStatus)
{
    RdumRdusGroup.NextGroupStatus = NextStatus;
}

/******************************************************************************
 * @brief      获取下一个控制组状态
 * @return     下一个状态
 * <AUTHOR>
 * @date       2025-05-14
 * @note       
 *****************************************************************************/
RdumRdusGroupStatus_en RdumRdusDrv_GetNextSnsCtrlGroupStatus(void)
{
    return RdumRdusGroup.NextGroupStatus;
}
