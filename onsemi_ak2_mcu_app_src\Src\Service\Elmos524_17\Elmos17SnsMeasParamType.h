/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * Elmos17SnsMeasParamType.h:
 * Created on: 2021-12-27 15:04
 * Original designer:
 ******************************************************************************/
#ifndef __Elmos17SnsMeasParamType_H__
#define __Elmos17SnsMeasParamType_H__

/* Includes ------------------------------------------------------------------*/
#include "Elmos_524_17_SnsCtrl.h"

#if 0
#include "TimerManage.h"

#define PRINTF_ELMOS17Param    Lhprintf
#else
#define PRINTF_ELMOS17Param(...)
#endif

/***************************************************************************** */
/***************************************************************************** */
/****************************** Type Definitions ***************************** */
/***************************************************************************** */
/***************************************************************************** */

#define WR_SNS_MEAS_PARAM_SUCCESS  (0x80)

typedef enum
{
    WRSnsMeasParamStatus_IDLE = 0,


    WRSnsMeasParamStatus_WriteParam ,

    WRSnsMeasParamStatus_ReadParam ,
    
    WRSnsMeasParamStatus_ReadSelfTestStatus ,
    WRSnsMeasParamStatus_DONE ,
    
    WRSnsMeasParamStatus_ABORT ,
    WRSnsMeasParamStatus_NUM
}WRSnsMeasParamStatus_e;

typedef enum
{
    
    eRSnsMeasParam = 0,
    eWSnsMeasParam ,
    eRALLSnsSelfTestResult ,
    eWRSnsMeasParamType_NULL ,
    
}WRSnsMeasParamType_e;

typedef enum
{
    
    eRSnsMeasParamStatus_Finish = 0,
    eRSnsMeasParamStatus_TransSPI_ERR ,
    eRSnsMeasParamStatus_SnsResp_ERR ,
    eRSnsMeasParamStatus_Sns_NoResp_ERR ,
    
    eRSnsMeasParamStatus_ABORT,
    eWSnsMeasParamStatus_NULL ,
    
}WRSnsMeasParamERRStatus_e;

typedef void (*WRSnsMeasParamCallBakFunc)(WRSnsMeasParamERRStatus_e eWRSnsParamErrStatus);


typedef struct
{
    SnsID_en SnsID;
    WRSnsMeasParamType_e eWRFlg;
    uint8 ElmosMeasParam_StartAddr ;
    uint16 u16Len;
    uint16 *pInOutDataBuf;
    uint16 *pResultflg;
    WRSnsMeasParamCallBakFunc WRSnsMeasParamCallBak;    
}WRSnsMeasParam_str;


typedef enum
{
    Elmos17_ITdr_190mA = 0,
        
    Elmos17_ITdr_200mA ,
    Elmos17_ITdr_210mA ,
    Elmos17_ITdr_220mA ,
    Elmos17_ITdr_230mA ,
    Elmos17_ITdr_240mA ,
    Elmos17_ITdr_250mA ,
    Elmos17_ITdr_260mA ,
    Elmos17_ITdr_270mA ,
    Elmos17_ITdr_280mA ,
    Elmos17_ITdr_290mA ,
    
    Elmos17_ITdr_300mA ,
    Elmos17_ITdr_310mA ,
    Elmos17_ITdr_320mA ,
    Elmos17_ITdr_330mA ,
    Elmos17_ITdr_340mA ,
    Elmos17_ITdr_350mA ,
    Elmos17_ITdr_360mA ,
    Elmos17_ITdr_370mA ,
    Elmos17_ITdr_380mA ,
    Elmos17_ITdr_390mA ,

    
    Elmos17_ITdr_400mA ,
    Elmos17_ITdr_410mA ,
    Elmos17_ITdr_420mA ,
    Elmos17_ITdr_430mA ,
    Elmos17_ITdr_440mA ,
    Elmos17_ITdr_450mA ,
    Elmos17_ITdr_460mA ,
    Elmos17_ITdr_470mA ,
    Elmos17_ITdr_480mA ,
    Elmos17_ITdr_490mA ,
    
    Elmos17_ITdr_500mA ,
}Elmos17_TdrCurrent_e;
typedef enum
{
    Elmos17_GANA_42_2dB = 0 ,
    Elmos17_GANA_44_6dB  ,
    Elmos17_GANA_47_0dB  ,
    Elmos17_GANA_49_4dB  ,
    Elmos17_GANA_51_8dB  ,
    Elmos17_GANA_54_2dB  ,
    Elmos17_GANA_56_6dB  ,
    Elmos17_GANA_59_0dB  ,

}Elmos17_AnalogGain_e;
    
typedef enum
{
    Elmos17_LowPass_Normal = 0 ,   /** @brief < f-3dB_LP */
    Elmos17_LowPass_High ,         /** @brief > f-3dB_LP */
}Elmos17_AnalogLp_e;

typedef enum
{
    Elmos17_GCal_0_0dB = 0,
    Elmos17_GCal_0_38dB  ,
    Elmos17_Gcal_0_76dB  ,
    Elmos17_Gcal_1_14dB  ,
    Elmos17_Gcal_1_52dB  ,
    Elmos17_Gcal_1_9dB   ,
    Elmos17_Gcal_2_28dB  ,
    Elmos17_Gcal_2_66dB  ,
    Elmos17_Gcal_3_04dB  ,
    Elmos17_Gcal_3_42dB  ,
    Elmos17_Gcal_3_8dB   ,
    Elmos17_Gcal_4_18dB  ,
    Elmos17_Gcal_4_56dB  ,
    Elmos17_Gcal_4_94dB  ,
    Elmos17_Gcal_5_32dB  ,
    Elmos17_Gcal_5_7dB   ,
    Elmos17_Gcal_6_08dB  ,
    Elmos17_Gcal_6_46dB  ,
    Elmos17_Gcal_6_84dB  ,
    Elmos17_Gcal_7_22dB  ,
    Elmos17_Gcal_7_6dB   ,
    Elmos17_Gcal_7_98dB  ,
    Elmos17_Gcal_8_36dB  ,
    Elmos17_Gcal_8_74dB  ,
    Elmos17_Gcal_9_12dB  ,
    Elmos17_Gcal_9_5dB   ,
    Elmos17_Gcal_9_88dB  ,
    Elmos17_Gcal_10_26dB ,
    Elmos17_Gcal_10_64dB ,
    Elmos17_Gcal_11_02dB ,
    Elmos17_Gcal_11_4dB  ,
    Elmos17_Gcal_11_78dB ,

}Elmos17_CalibrationGain_e;

typedef enum
{
    Elmos17_DacCtrl_Off = 0 ,
    Elmos17_DacCtrl_Reg  ,
    Elmos17_DacCtrl_AMPD_Env  ,
    Elmos17_DacCtrl_MaxStgAtg ,
    
}Elmos17_DacCtrl_e;

typedef enum
{
    Elmos17_Envp_BandWidth_0_3751_fc = 0 ,
    Elmos17_Envp_BandWidth_0_1735_fc = 3 ,
    Elmos46_Envp_BandWidth_0_0838_fc ,
    Elmos46_Envp_BandWidth_0_0412_fc ,
    Elmos46_Envp_BandWidth_0_0204_fc ,
    Elmos46_Envp_BandWidth_0_0102_fc ,

}Elmos17_Envp_BandWidth_e;
    
typedef enum
{
    Elmos46_Envp_Ds_Off = 0 ,
    Elmos46_Envp_Ds_On ,

}Elmos17_ENVP_Ds_e;

typedef enum
{
    Elmos17_Envp_FtcCfg_0_Fastest = 0 ,
    Elmos17_Envp_FtcCfg_1 ,
    Elmos17_Envp_FtcCfg_2 ,
    Elmos17_Envp_FtcCfg_3 ,
    Elmos17_Envp_FtcCfg_4 ,
    Elmos17_Envp_FtcCfg_5 ,
    Elmos17_Envp_FtcCfg_6 ,
    Elmos17_Envp_FtcCfg_7_Slowest ,

}Elmos17_ENVP_FtcCfg_e;

typedef enum
{
    Elmos17_Envp_FtcEn_Off = 0 ,
    
    Elmos17_Envp_FtcEn_On ,

}Elmos17_ENVP_FtcEn_e;

typedef enum
{
    Elmos17_Envp_StcMod_Lin = 0 ,
    
    Elmos17_Envp_StcMod_Log ,

}Elmos17_ENVP_StcMod_e;
typedef enum
{
    Elmos17_Envp_AtgTau_0_Slow = 0 ,
    Elmos17_Envp_AtgTau_1 ,
    Elmos17_Envp_AtgTau_2 ,
    Elmos17_Envp_AtgTau_3 ,
    Elmos17_Envp_AtgTau_4 ,
    Elmos17_Envp_AtgTau_5 ,
    Elmos17_Envp_AtgTau_6_fast ,
    Elmos17_Envp_AtgTau_7_fast ,

}Elmos17_ENVP_AtgTau_e;
    
typedef enum
{
    Elmos17_Envp_AtgIni_NoInitValue = 0 ,
    Elmos17_Envp_AtgIni_32 ,
    Elmos17_Envp_AtgIni_64 ,
    Elmos17_Envp_AtgIni_80 ,
    Elmos17_Envp_AtgIni_96 ,
    Elmos17_Envp_AtgIni_112 ,
    Elmos17_Envp_AtgIni_128 ,
    Elmos17_Envp_AtgIni_144 ,
        
}Elmos17_ENVP_AtgIni_e;

typedef enum
{
    Elmos17_Envp_AtgAlpha_2_0 = 0 ,
    Elmos17_Envp_AtgAlpha_2_5 ,
    Elmos17_Envp_AtgAlpha_3_0 ,
    Elmos17_Envp_AtgAlpha_3_5 ,
    Elmos17_Envp_AtgAlpha_4_0 ,
    Elmos17_Envp_AtgAlpha_4_5 ,
    Elmos17_Envp_AtgAlpha_5_0 ,
    Elmos17_Envp_AtgAlpha_6_0 ,
        
}Elmos17_ENVP_AtgAlpha_e;

typedef enum
{
    Elmos17_Envp_AtgOff = 0 ,
    Elmos17_Envp_AtgOn ,
        
}Elmos17_ENVP_AtgEn_e;
    

typedef enum
{
    Elmos17_ENVP_EevalSens_1diff = 0 ,
    Elmos17_ENVP_EevalSens_2diff ,
    Elmos17_ENVP_EevalSens_3diff ,
    Elmos17_ENVP_EevalSens_4diff ,
    Elmos17_ENVP_EevalSens_5diff ,
    Elmos17_ENVP_EevalSens_6diff ,
    Elmos17_ENVP_EevalSens_7diff ,
    Elmos17_ENVP_EevalSens_8diff ,
            
}Elmos17_ENVP_EevalSens_e;

typedef enum
{
    Elmos17_Envp_EevalVal_ENV_RAW = 0 ,
    Elmos17_Envp_EevalVal_AMPD_ENV ,
        
}Elmos17_ENVP_EevalVal_e;

typedef enum
{
    eElmos17_SpxStcSeg_0 = 0 ,
    eElmos17_SpxStcSeg_Sub8 ,
    eElmos17_SpxStcSeg_Sub12 ,
    eElmos17_SpxStcSeg_Sub16 ,
    eElmos17_SpxStcSeg_Add8 ,
    eElmos17_SpxStcSeg_Add12 ,
    eElmos17_SpxStcSeg_Add16 ,
    eElmos17_SpxStcSeg_Add20 ,
    eElmos17_SpxStcSeg_Add24 ,
    eElmos17_SpxStcSeg_Add28 ,
    eElmos17_SpxStcSeg_Add32 ,
    eElmos17_SpxStcSeg_Add36 ,
    eElmos17_SpxStcSeg_Add40 ,
    eElmos17_SpxStcSeg_Add44 ,
    eElmos17_SpxStcSeg_Add48 ,
    eElmos17_SpxStcSeg_Add52 ,
        
}Elmos17_SpxStcSeg_e;

typedef enum
{
    eElmos17_SpxStcSegPos0_128us = 0 ,
    eElmos17_SpxStcSegPos0_256us ,
    eElmos17_SpxStcSegPos0_512 ,
    eElmos17_SpxStcSegPos0_1024 ,
    eElmos17_SpxStcSegPos0_1280 ,
    eElmos17_SpxStcSegPos0_1664 ,
    eElmos17_SpxStcSegPos0_2048 ,
    eElmos17_SpxStcSegPos0_2560 ,
}Elmos17_SpxPos0Seg_e;

typedef enum
{
    eElmos17_SpxStcSegPos1x_128us = 0 ,
    eElmos17_SpxStcSegPos1x_256us ,
    eElmos17_SpxStcSegPos1x_512 ,
    eElmos17_SpxStcSegPos1x_1024 ,
    eElmos17_SpxStcSegPos1x_2048 ,
    eElmos17_SpxStcSegPos1x_4096 ,
    eElmos17_SpxStcSegPos1x_6144 ,
    eElmos17_SpxStcSegPos1x_8192 ,
}Elmos17_SpxPos1xSeg_e;


typedef enum
{
    eElmos17_SpxStgSegThreshold_0 = 0 ,
    eElmos17_SpxStgSegThreshold_5 ,
    eElmos17_SpxStgSegThreshold_6 ,
    eElmos17_SpxStgSegThreshold_8 ,
    eElmos17_SpxStgSegThreshold_9 ,
    eElmos17_SpxStgSegThreshold_11 ,
    eElmos17_SpxStgSegThreshold_13 ,
    eElmos17_SpxStgSegThreshold_15 ,
    eElmos17_SpxStgSegThreshold_17 ,
    eElmos17_SpxStgSegThreshold_19 ,
    eElmos17_SpxStgSegThreshold_22 ,
    eElmos17_SpxStgSegThreshold_25 ,
    eElmos17_SpxStgSegThreshold_28 ,
    eElmos17_SpxStgSegThreshold_31 ,
    eElmos17_SpxStgSegThreshold_34 ,
    eElmos17_SpxStgSegThreshold_38 ,
    eElmos17_SpxStgSegThreshold_42 ,
    eElmos17_SpxStgSegThreshold_46 ,
    eElmos17_SpxStgSegThreshold_51 ,
    eElmos17_SpxStgSegThreshold_56 ,
    eElmos17_SpxStgSegThreshold_61 ,
    eElmos17_SpxStgSegThreshold_67 ,
    eElmos17_SpxStgSegThreshold_73 ,
    eElmos17_SpxStgSegThreshold_79 ,
    eElmos17_SpxStgSegThreshold_86 ,
    eElmos17_SpxStgSegThreshold_94 ,
    eElmos17_SpxStgSegThreshold_102 ,
    eElmos17_SpxStgSegThreshold_111 ,
    eElmos17_SpxStgSegThreshold_121 ,
    eElmos17_SpxStgSegThreshold_131 ,
    eElmos17_SpxStgSegThreshold_142 ,
    eElmos17_SpxStgSegThreshold_155 ,
}Elmos17_SpxStgSeg_e;

typedef enum
{
    eElmos17_SPxRecv_Thres_DS_0_5 = 0 ,
    eElmos17_SPxRecv_Thres_DS_0_625 ,
    eElmos17_SPxRecv_Thres_DS_0_8125 ,
    eElmos17_SPxRecv_Thres_DS_1 ,
}Elmos17_SPxRecv_Thres_DS_e;

typedef enum
{
    eElmos17_SPxRecv_Thres_DS_Disable = 0 ,
    eElmos17_SPxRecv_Thres_DS_Ensable ,
}Elmos17_SPxRecv_Thres_DS_En_e;

typedef enum
{
    eElmos17_SpxSeg_StcSet_LogOrLine = 0 ,
    eElmos17_SpxSeg_StcSet_4Seg ,
}Elmos17_SpxSeg_StcSet_e;

typedef enum
{
    eElmos17_SpxSeg_StgSet_4Seg = 0 ,
    eElmos17_SpxSeg_StgSet_8Seg ,
}Elmos17_SpxSeg_StgSet_e;


/* measurement parameters - general setting - struct and union */
typedef struct
{
  uint16                          SamplingFrequency                   ;        /**< BRG Carrier frequency for the burst ,frequency = SamplingFrequency * 5.722 Hz */

  Elmos17_TdrCurrent_e              TdrCurrent           ;        /**< TDR Transducer driver current 0-31, 0=190mA,Step = 10mA*/
  Elmos17_AnalogGain_e              AnalogGain           ;        /**< ASP Analog gain configuration 0-7, 0=42.2dB ,Step = 2.4dB*/
  Elmos17_AnalogLp_e                AnalogLp             ;        /**< ASP low pass filter configuration 0-1 ,0 :normal < f-3dB_LP ,1:High > f-3dB_LP */
  Elmos17_CalibrationGain_e         CalibrationGain      ;        /**< ENVP calibration gain configuration 0-31, 0=0dB ,Step = 0.38dB*/

  uint8                           RingFreqStart                       ;        /**< RFM start of measurement window 0-31 */
  uint8                           RingFreqWidth                       ;        /**< RFM Width of measurement window 0-31*/

  Elmos17_DacCtrl_e                 Dac1Ctrl             ;        /**< DAC1 output configuration */
  Elmos17_DacCtrl_e                 Dac0Ctrl             ;        /**< DAC0 output configuration */

  uint16                          UnderVoltageThreshold               ;        /**< Threshold for VTANK measurement during burst phase */
} Meas_sGeneralSettings;

/* measurement parameters - noise measurement - struct and union */
typedef struct
{
  uint8                           NoiseMeasTime                       ;        /**< Noise samples to be acquired whilst diagnostic measurement */
  uint8                           NoiseMeasThreshold                  ;        /**< Threshold which defines noise samples that have to be counted by the noise counter */

  uint8                           NoiseMeasGain                       ;        /**< AMPD signal gain of digital amplifier 0-127, 0=0dB ,Step = 0.38dB*/
  Elmos17_Envp_BandWidth_e          NoiseMeasFilterBw  ;        /**< ENVP digital filter bandwidth configuration */
  uint8                           NoiseAtgThreshold                   ;        /**< Threshold for noise measurement while echo measurement */
} Meas_sNoiseMeasurement;

/* measurement parameters - standard path profile - struct and union */
typedef struct
{
  uint16                      SpxBurstFreq                        ;        /**< BRG burst base frequency ,frequency = SpxBurstFreq * 5.722 Hz */

  uint8                       SpxBurstPulses                      ;        /**< BRG number of burst pulses 0-127*/
  Elmos17_Envp_BandWidth_e      SpxMeasFilterBw                     ;        /**< ENVP digital filter bandwidth configuration */
  Elmos17_ENVP_Ds_e             SpxDs                               ;        /**< ENVP down sampling rate select ,0 : fs = fc ,1: fs = fc/2 */
  Elmos17_ENVP_FtcCfg_e         SpxFtcCfg                           ;        /**< FTC configuration */
  Elmos17_ENVP_FtcEn_e          SpxFtcEn                            ;        /**< FTC enable flag */

  uint8                       SpxStcGain                          ;        /**< STC target value at which it will stop to increase/decrease AMPD_CTRL.g_dig  0-127, 0=0dB ,Step = 0.38dB */
  Elmos17_ENVP_StcMod_e         SpxStcMod                           ;        /**< STC autoincrement mode (lin or log) */
  uint8                       SpxStcStart                         ;        /**< STC time between start of meas and start of STC function  ,1 times 32*/

  uint16                      SpxStcTb                            ;        /**< STC autoincrement time base 0-4095 us*/

  uint8                       SpxStgHeight1                       ;        /**< STG target value in interval 1 ,0-255*/
  uint8                       SpxStgInterval1                     ;        /**< STG duration of interval 1 times 32us*/

  uint8                       SpxStgHeight2                       ;        /**< STG target value in interval 2 ,0-255*/
  uint8                       SpxStgInterval2                     ;        /**< STG duration of interval 2 times 32us*/

  uint8                       SpxStgHeight3                       ;        /**< STG target value in interval 3 ,0-255*/
  uint8                       SpxStgInterval3                     ;        /**< STG duration of interval 3 times 32us*/

  uint8                       SpxStgHeight4                       ;        /**< STG target value in interval 4 ,0-255*/
  uint8                       SpxStgInterval4                     ;        /**< STG duration of interval 4 times 32us*/

  Elmos17_ENVP_AtgTau_e         SpxAtgTau                           ;        /**< ATG time response configuration */
  Elmos17_ENVP_AtgAlpha_e       SpxAtgAlpha                         ;        /**< ATG sensitivity configuration */
  Elmos17_ENVP_AtgIni_e         SpxAtgIni                           ;        /**< ATG initial value */
  Elmos17_ENVP_AtgEn_e		    SpxAtgCfg                           ;        /**< ATG configuration */

  Elmos17_SpxSeg_StgSet_e		SpxStgSet  			  				;        /**< STG SET 0:use Default 4 Step STG 1:use 8 Step STG */

  uint8                       SpxEevalSel                         ;        /**< EEVAL data source selection bit0:TLH ,bit1: THL,bit2:MAX, bit3 MIN*/
  Elmos17_ENVP_EevalSens_e      SpxEevalSens                        ;        /**< EEVAL echo minimum/maximum evaluation sensitivity */
  Elmos17_ENVP_EevalVal_e       SpxEevalVal                         ;        /**< EEVAL select value to be stored via DMA */
  uint8                       SpxMeasGain                         ;        /**< AMPD digital amplifier gain configuration 0-127, 0=0dB ,Step = 0.38dB*/

  uint8                       SpxMeasMaskDirect                   ;        /**< Period at which echo measurement results should be ignored in direct mode ,times 32us*/
  uint8                       SpxMeasMaskIndirect                 ;        /**< Period at which echo measurement results should be ignored in indirect mode ,times 32us*/
} Meas_sStandardPathProfile;

typedef struct
{
  uint16                      SpxBurstFreq                        ;        /**< BRG burst base frequency ,frequency = SpxBurstFreq * 5.722 Hz */
  
  uint8                       SpxBurstPulses                      ;        /**< BRG number of burst pulses 0-127*/
  Elmos17_Envp_BandWidth_e      SpxMeasFilterBw                     ;        /**< ENVP digital filter bandwidth configuration */
  Elmos17_ENVP_Ds_e             SpxDs                               ;        /**< ENVP down sampling rate select ,0 : fs = fc ,1: fs = fc/2 */
  Elmos17_ENVP_FtcCfg_e         SpxFtcCfg                           ;        /**< FTC configuration */
  Elmos17_ENVP_FtcEn_e          SpxFtcEn                            ;        /**< FTC enable flag */
  Elmos17_SpxSeg_StcSet_e       SpxStcSet                           ;        /**< STC SET 0:use line/log Stc. 1:use Step STC */

  Elmos17_SpxPos0Seg_e          SpxStcSeg_Pos0                      ;        /**< Step STC Position 0(Delta) */
  Elmos17_SpxPos1xSeg_e         SpxStcSeg_Pos1                      ;        /**< Step STC Position 1(Delta) */
  Elmos17_SpxPos1xSeg_e         SpxStcSeg_Pos2                      ;        /**< Step STC Position 2(Delta) */
  Elmos17_SpxPos1xSeg_e         SpxStcSeg_Pos3                      ;        /**< Step STC Position 3(Delta) */
  Elmos17_SpxPos1xSeg_e         SpxStcSeg_Pos4                      ;        /**< Step STC Position 4(Delta) */

  Elmos17_SpxStcSeg_e           SpxStcSeg_Gain1                     ;        /**< Step STC Gain 1(Delta) */
  Elmos17_SpxStcSeg_e           SpxStcSeg_Gain2                     ;        /**< Step STC Gain 2(Delta) */
  Elmos17_SpxStcSeg_e           SpxStcSeg_Gain3                     ;        /**< Step STC Gain 3(Delta) */
  Elmos17_SpxStcSeg_e           SpxStcSeg_Gain4                     ;        /**< Step STC Gain 4(Delta) */


  Elmos17_SpxStgSeg_e           SpxStgSeg_Height1                   ;        /**< STG target value in interval 1 */
  Elmos17_SpxPos0Seg_e          SpxStgSeg_Interval1                 ;        /**< STG duration of interval 1 times 32*/

  Elmos17_SpxStgSeg_e           SpxStgSeg_Height2                   ;        /**< STG target value in interval 2 */
  Elmos17_SpxPos1xSeg_e         SpxStgSeg_Interval2                 ;        /**< STG duration of interval 2 times 32*/

  Elmos17_SpxStgSeg_e           SpxStgSeg_Height3                   ;        /**< STG target value in interval 3 */
  Elmos17_SpxPos1xSeg_e         SpxStgSeg_Interval3                 ;        /**< STG duration of interval 3 times 32*/

  Elmos17_SpxStgSeg_e           SpxStgSeg_Height4                   ;        /**< STG target value in interval 4 */
  Elmos17_SpxPos1xSeg_e         SpxStgSeg_Interval4                 ;        /**< STG duration of interval 4 times 32*/

  Elmos17_SpxStgSeg_e           SpxStgSeg_Height5                   ;        /**< STG target value in interval 1 */
  Elmos17_SpxPos1xSeg_e         SpxStgSeg_Interval5                 ;        /**< STG duration of interval 1 times 32*/

  Elmos17_SpxStgSeg_e           SpxStgSeg_Height6                   ;        /**< STG target value in interval 2 */
  Elmos17_SpxPos1xSeg_e         SpxStgSeg_Interval6                 ;        /**< STG duration of interval 2 times 32*/

  Elmos17_SpxStgSeg_e           SpxStgSeg_Height7                   ;        /**< STG target value in interval 3 */
  Elmos17_SpxPos1xSeg_e         SpxStgSeg_Interval7                 ;        /**< STG duration of interval 3 times 32*/

  Elmos17_SpxStgSeg_e           SpxStgSeg_Height8                   ;        /**< STG target value in interval 4 */
  Elmos17_SpxPos1xSeg_e         SpxStgSeg_Interval8                 ;        /**< STG duration of interval 4 times 32*/

  Elmos17_ENVP_AtgTau_e         SpxAtgTau                           ;        /**< ATG time response configuration */
  Elmos17_ENVP_AtgAlpha_e       SpxAtgAlpha                         ;        /**< ATG sensitivity configuration */
  Elmos17_ENVP_AtgIni_e         SpxAtgIni                           ;        /**< ATG initial value */
  Elmos17_ENVP_AtgEn_e          SpxAtgCfg                           ;        /**< ATG configuration */
  Elmos17_SPxRecv_Thres_DS_e    SPxSTG_recv_Thres_DS                ;        /**< STG Down scaling in receive mode for Threshold */
  Elmos17_SpxSeg_StgSet_e       SpxStgSet                           ;        /**< STG SET 0:use Default 4 Step STG 1:use 8 Step STG */
  Elmos17_SPxRecv_Thres_DS_En_e SPxSTG_recv_Thres_DS_EN             ;        /**< STG Down scaling in receive mode for Threshold 0:disable 1:enable */
  uint8                       SpxEevalSel                         ;        /**< EEVAL data source selection */
  Elmos17_ENVP_EevalSens_e      SpxEevalSens                        ;        /**< EEVAL echo minimum/maximum evaluation sensitivity */
  Elmos17_ENVP_EevalVal_e       SpxEevalVal                         ;        /**< EEVAL select value to be stored via DMA */
  uint8                       SpxMeasGain                         ;        /**< AMPD digital amplifier gain configuration 0-127, 0=0dB ,Step = 0.38dB */

  uint8                       SpxMeasMaskDirect                   ;        /**< Period at which echo measurement results should be ignored in direct mode */
  uint8                       SpxMeasMaskIndirect                 ;        /**< Period at which echo measurement results should be ignored in indirect mode */
} Meas_sStandardPathProfile_STC_STG;


/* measurement parameters - advanced path profile - struct and union */
typedef struct
{
  uint16         ApxBurstFreqLo                      ;        /**< BRG chirp low frequency ,frequency = ApxBurstFreqLo * 5.722 Hz*/

  uint16         ApxBurstFreqHi                      ;        /**< BRG chirp high frequency ,frequency = ApxBurstFreqHi * 5.722 Hz*/

  uint8          ApxBurstFreqDelta                   ;        /**< BRG chirp frequency step width */
  uint8          ApxFilterLength                     ;        /**< ENVP number of filter stages */

  uint8          ApxBurstPulses                      ;        /**< BRG number of burst pulses */
  uint8          ApxCodeLength                       ;        /**< BRG length of the code */
  uint8          ApxCode                             ;        /**< BRG code word to send */
  uint8          ApxDs                               ;        /**< ENVP down sampling rate select */

  uint8         ApxEnvpCoeff0;                                /**< ENVP filter coefficients in the advanced signal path -0  */
  uint8         ApxEnvpCoeff1;                                /**< ENVP filter coefficients in the advanced signal path -1 */
  uint8         ApxEnvpCoeff2;                                /**< ENVP filter coefficients in the advanced signal path -2 */
  uint8         ApxEnvpCoeff3;                                /**< ENVP filter coefficients in the advanced signal path -3 */
  uint8         ApxEnvpCoeff4;                                /**< ENVP filter coefficients in the advanced signal path -4 */
  uint8         ApxEnvpCoeff5;                                /**< ENVP filter coefficients in the advanced signal path -5 */
  uint8         ApxEnvpCoeff6;                                /**< ENVP filter coefficients in the advanced signal path -6 */
  uint8         ApxEnvpCoeff7;                                /**< ENVP filter coefficients in the advanced signal path -7 */
  uint8         ApxEnvpCoeff8;                                /**< ENVP filter coefficients in the advanced signal path -8 */
  uint8         ApxEnvpCoeff9;                                /**< ENVP filter coefficients in the advanced signal path -9 */
  uint8         ApxEnvpCoeff10;                               /**< ENVP filter coefficients in the advanced signal path -10 */
  uint8         ApxEnvpCoeff11;                               /**< ENVP filter coefficients in the advanced signal path -11 */
  uint8         ApxEnvpCoeff12;                               /**< ENVP filter coefficients in the advanced signal path -12 */
  uint8         ApxEnvpCoeff13;                               /**< ENVP filter coefficients in the advanced signal path -13 */
  uint8         ApxEnvpCoeff14;                               /**< ENVP filter coefficients in the advanced signal path -14 */
  uint8         ApxEnvpCoeff15;                               /**< ENVP filter coefficients in the advanced signal path -15 */
  uint8         ApxEnvpCoeff16;                               /**< ENVP filter coefficients in the advanced signal path -16 */
  uint8         ApxEnvpCoeff17;                               /**< ENVP filter coefficients in the advanced signal path -17 */
  uint8         ApxEnvpCoeff18;                               /**< ENVP filter coefficients in the advanced signal path -18 */
  uint8         ApxEnvpCoeff19;                               /**< ENVP filter coefficients in the advanced signal path -19 */
  uint8         ApxEnvpCoeff20;                               /**< ENVP filter coefficients in the advanced signal path -20 */
  uint8         ApxEnvpCoeff21;                               /**< ENVP filter coefficients in the advanced signal path -21 */
  uint8         ApxEnvpCoeff22;                               /**< ENVP filter coefficients in the advanced signal path -22 */
  uint8         ApxEnvpCoeff23;                               /**< ENVP filter coefficients in the advanced signal path -23 */
  uint8         ApxEnvpCoeff24;                               /**< ENVP filter coefficients in the advanced signal path -24 */
  uint8         ApxEnvpCoeff25;                               /**< ENVP filter coefficients in the advanced signal path -25 */
  uint8         ApxEnvpCoeff26;                               /**< ENVP filter coefficients in the advanced signal path -26 */
  uint8         ApxEnvpCoeff27;                               /**< ENVP filter coefficients in the advanced signal path -27 */
  uint8         ApxEnvpCoeff28;                               /**< ENVP filter coefficients in the advanced signal path -28 */
  uint8         ApxEnvpCoeff29;                               /**< ENVP filter coefficients in the advanced signal path -29 */
  uint8         ApxEnvpCoeff30;                               /**< ENVP filter coefficients in the advanced signal path -30 */
  uint8         ApxEnvpCoeff31;                               /**< ENVP filter coefficients in the advanced signal path -31 */


  uint8          ApxAatgCn                           ;        /**< AATG cell number configuration */
  uint8          ApxAatgCw                           ;        /**< AATG cell width configuration */
  uint8          ApxAatgAlpha                        ;        /**< AATG sensitivity configuration */
  uint8          ApxAatgOff                          ;        /**< AATG on/off flag */
  uint8          ApxEevalSens                        ;        /**< EEVAL echo minimum/maximum evaluation sensitivity */
  uint8          ApxMinConf                          ;        /**< Min confidence level for advanced measurement samples to be transmitted over dsi3 */

  uint8          ApxMeasMaskDirect                   ;        /**< Period at which echo measurement results should be ignored in direct mode */
  uint8          ApxMeasMaskIndirect                 ;        /**< Period at which echo measurement results should be ignored in indirect mode */
  uint8          Apx_FtcCfg                          ;        /**< unused memory space */
} Meas_sAdvancedPathProfile;



/* measurement parameters - nfd settings - struct and union */
typedef struct
{
  uint8          NfdCtrlSens                         ;        /**< NFD_CTRL: Echo evaluation sensitivity */
  uint8          NfdCtrlIrq                          ;        /**< NFD_CTRL: NFD interrupt configuration */
  uint8          Nfd1_Mode                           ;        /**< Nfd1_Mode Îª1Ê±ï¿½ï¿½Nfd1_TimeScope ÎªNFD ï¿½Ð¶ï¿½Ê±ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½Î§ï¿½ï¿½Ê±ï¿½ï¿½ï¿½ï¿½32usÎªï¿½ï¿½Î» */

  uint8          Nfd1_TimeScope                      ;        /**< Nfd1_TimeScope Nfd1_Mode = 1Ê±ï¿½ï¿½Ê±ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½Î§ */

  uint16         NfdThreshold                        ;           /**< NFD minimum detection threshold */

  uint8          Nfd2Start                           ;        /**< NFD start of NFD window */
  uint8          Nfd2Length                          ;        /**< NFD length of NFD window */

  uint16         Nfd2Thres                           ;        /**< NFD threshold value */
} Meas_sNfdSettings;



/* measurement parameters - dsi settings - struct and union */
typedef struct
{
  uint8          ChipTime                            ;        /**< DSI3 transmitter chip time */
  uint8          Dsi3Debounce                        ;        /**< DSI3 debouncer -> t_debounce = (2^Dsi3Debounce)*1/24Mhz */
  uint8          SyncDsi3                            ;        /**< DSI3 mode of synchronization */
  uint8          SyncFreq                            ;        /**< Measurement burst and sampling frequency compensation enabled flag */
  uint8          RCCCurrentOffset                    ;        /**< DSI3 RCC current offset configuration */

  uint16         CrmFccBlankTime                     ;        /**< DSI3 blanking time after crm command */
  uint8          RCCSlopeSel                         ;        /**< DSI3 RCCSlope configuration */

  uint16         PdcmFccBlankTime                    ;        /**< DSI3 blanking time after pdcm command */
  uint8          FCC_t_bit                           ;        /**< DSI3 FCC bit time configuration */
  uint8          RCCCurveSel                         ;        /**< DSI3 RCC curve selection configuration */

  uint16         PdcmPeriod                          ;        /**< DSI3 period of one pdcm cycle */

  uint16         PdcmRespSensor1                     ;        /**< DSI3 start time of sensor 1 pdcm response */

  uint16         PdcmRespSensor2                     ;        /**< DSI3 start time of sensor 2 pdcm response */

  uint16         PdcmRespSensor3                     ;        /**< DSI3 start time of sensor 3 pdcm response */

  uint16         PdcmRespSensor4                     ;        /**< DSI3 start time of sensor 4 pdcm response */

  uint16         PdcmRespSensor5                     ;        /**< DSI3 start time of sensor 5 pdcm response */

  uint16         PdcmRespSensor6                     ;        /**< DSI3 start time of sensor 6 pdcm response */

  uint16         PdcmRespSensor7                     ;        /**< DSI3 start time of sensor 7 pdcm response */
} Meas_sDsiSettings;


/* measurement parameters - monitoring - struct and union */
typedef struct
{
  uint16         MonStart                            ;        /**< Monitoring time to start */
  uint8          MonDs                               ;        /**< Monitoring down sampling */

  uint8          MonCh0                              ;        /**< Monitoring signals to be monitored on channel 0 */
  uint8          MonCh1                              ;        /**< Monitoring signals to be monitored on channel 1 */
  uint8          MonCh2                              ;        /**< Monitoring signals to be monitored on channel 2 */
  uint8          MonCh3                              ;        /**< Monitoring signals to be monitored on channel 3 */
} Meas_sMonitoring;

/* measurement parameters - data for self test - struct and union */
typedef struct
{
  uint16         SelfTestSel                         ;       /**< Perform self test mask - Perform CRC check on OTP */
  uint8          CapVdddMin                          ;       /**<  */
  uint8          CapVdddMax                          ;       /**<  */
  uint8          CapVddaMin                          ;       /**<  */
  uint8          CapVddaMax                          ;       /**<  */

  uint8          RingingFregLim                       ;      /**< Relative limit in percent for signal self test ringing frequency */
  uint8          RingingTimeLim                       ;      /**< Relative limit in percent for signal self test ringing time */
  uint8          HeightLim                            ;      /**< Relative limit in percent for signal self test echo height */
  uint8          TofLim                               ;      /**< Relative limit in percent for signal self test echo time of flight */
  uint8          ConfLim                              ;      /**< Relative limit in percent for signal self test confidence level */

} Meas_sDataForSelfTest;


/* measurement parameters - data for time of flight compensation */
typedef struct
{
  uint8          SPA_TOF_compensation_time           ;        /**< Datafield for customer to write time of flight compensation for standard path profile a in 32us steps */
  uint8          SPA_meas_time                       ;        /**< Datafield for customer to write time of flight compensation for standard path profile a in 256us steps */
  uint8          SPB_TOF_compensation_time           ;        /**< Datafield for customer to write time of flight compensation for standard path profile b in 32us steps */
  uint8          SPB_meas_time                       ;        /**< Datafield for customer to write time of flight compensation for standard path profile b in 256us steps */
  uint8          SPC_TOF_compensation_time           ;        /**< Datafield for customer to write time of flight compensation for standard path profile c in slave */
  uint8          SPC_meas_time                       ;        /**< Datafield for customer to write time of flight compensation for standard path profile c in 256us steps */
  uint8          APA_TOF_compensation_time           ;        /**< Datafield for customer to write time of flight compensation for advanced path profile a in 32us steps */
  uint8          APA_meas_time                       ;        /**< Datafield for customer to write time of flight compensation for advanced path profile a in 256us steps */
  uint8          APB_TOF_compensation_time           ;        /**< Datafield for customer to write time of flight compensation for advanced path profile b in slave */
  uint8          APB_meas_time                       ;        /**< Datafield for customer to write time of flight compensation for advanced path profile b in 256us steps */



} Meas_sTofCompensation;

/* measurement parameters - data for time of flight compensation */
typedef struct
{
  uint8          AdvAtgTau                           ;        /**< ATG time response configuration */
  uint8          AdvAtgAlpha                         ;        /**< ATG sensitivity configuration */
  uint8          AdvAtgIni                           ;        /**< ATG initial value */
  uint8          AdvAMPD_dig                         ;        /**< ATG configuration */

} Meas_sADV_ATG;


typedef struct
{
  Meas_sGeneralSettings                 GeneralSettings;                        /**< meas parameters - general settings field */
  Meas_sNoiseMeasurement                NoiseMeasurement;                       /**< meas parameters - noise measurement field */
  Meas_sStandardPathProfile             StandardPathProfileA;                   /**< meas parameters - standard path profile A field */
  Meas_sStandardPathProfile             StandardPathProfileB;                   /**< meas parameters - standard path profile B field */
  Meas_sStandardPathProfile             StandardPathProfileC;                   /**< meas parameters - standard path profile C field */
  Meas_sAdvancedPathProfile             AdvancedPathProfileA;                   /**< meas parameters - advanced path profile A field */
  Meas_sAdvancedPathProfile             AdvancedPathProfileB;                   /**< meas parameters - advanced path profile B field */
  Meas_sNfdSettings                     NfdSettings;                            /**< meas parameters - nfd settings field */

  Meas_sDsiSettings                     DsiSettings;                            /**< meas parameters - dsi settings field */
  Meas_sMonitoring                      Monitoring;                             /**< meas parameters - monitoring field */
  Meas_sDataForSelfTest                 SelfTest;                        /**< meas parameters - data for self test field */

  Meas_sTofCompensation                 TOFComp;               /**< meas parameters - data for time of flight compensation */
  Meas_sADV_ATG                         ADV_STG;

} Meas_sMeasurementParameters_t;

typedef struct
{
  uint16                          SamplingFrequency                   ;        /**< BRG Carrier frequency for the burst ,frequency = SamplingFrequency * 5.722 Hz */

  Elmos17_TdrCurrent_e              TdrCurrent           ;        /**< TDR Transducer driver current 0-31, 0=190mA,Step = 10mA*/
  Elmos17_AnalogGain_e              AnalogGain           ;        /**< ASP Analog gain configuration 0-7, 0=42.2dB ,Step = 2.4dB*/
  Elmos17_AnalogLp_e                AnalogLp             ;        /**< ASP low pass filter configuration 0-1 ,0 :normal < f-3dB_LP ,1:High > f-3dB_LP */
  Elmos17_CalibrationGain_e         CalibrationGain      ;        /**< ENVP calibration gain configuration 0-31, 0=0dB ,Step = 0.38dB*/

  uint8                           RingFreqStart                       ;        /**< RFM start of measurement window 0-31 */
  uint8                           RingFreqWidth                       ;        /**< RFM Width of measurement window 0-31*/

  Elmos17_DacCtrl_e                 Dac1Ctrl             ;        /**< DAC1 output configuration */
  Elmos17_DacCtrl_e                 Dac0Ctrl             ;        /**< DAC0 output configuration */

  uint16                          UnderVoltageThreshold               ;        /**< Threshold for VTANK measurement during burst phase */
} CANCom_Meas_GeneralSettings;

typedef struct
{
  uint16                      SpxBurstFreq                        ;        /**< BRG burst base frequency ,frequency = SpxBurstFreq * 5.722 Hz */

  uint8                       SpxBurstPulses                      ;        /**< BRG number of burst pulses 0-127*/
  Elmos17_Envp_BandWidth_e      SpxMeasFilterBw                     ;        /**< ENVP digital filter bandwidth configuration */
  Elmos17_ENVP_Ds_e             SpxDs                               ;        /**< ENVP down sampling rate select ,0 : fs = fc ,1: fs = fc/2 */
  Elmos17_ENVP_FtcCfg_e         SpxFtcCfg                           ;        /**< FTC configuration */
  Elmos17_ENVP_FtcEn_e          SpxFtcEn                            ;        /**< FTC enable flag */

  uint8                       SpxStcGain                          ;        /**< STC target value at which it will stop to increase/decrease AMPD_CTRL.g_dig  0-127, 0=0dB ,Step = 0.38dB */
  Elmos17_ENVP_StcMod_e         SpxStcMod                           ;        /**< STC autoincrement mode (lin or log) */
  uint8                       SpxStcStart                         ;        /**< STC time between start of meas and start of STC function  ,1 times 32*/

  uint16                      SpxStcTb                            ;        /**< STC autoincrement time base 0-4095 us*/

  uint8                       SpxStgHeight1                       ;        /**< STG target value in interval 1 ,0-255*/
  uint8                       SpxStgInterval1                     ;        /**< STG duration of interval 1 times 32us*/

  uint8                       SpxStgHeight2                       ;        /**< STG target value in interval 2 ,0-255*/
  uint8                       SpxStgInterval2                     ;        /**< STG duration of interval 2 times 32us*/

  uint8                       SpxStgHeight3                       ;        /**< STG target value in interval 3 ,0-255*/
  uint8                       SpxStgInterval3                     ;        /**< STG duration of interval 3 times 32us*/

  uint8                       SpxStgHeight4                       ;        /**< STG target value in interval 4 ,0-255*/
  uint8                       SpxStgInterval4                     ;        /**< STG duration of interval 4 times 32us*/

  Elmos17_ENVP_AtgTau_e         SpxAtgTau                           ;        /**< ATG time response configuration */
  Elmos17_ENVP_AtgAlpha_e       SpxAtgAlpha                         ;        /**< ATG sensitivity configuration */
  Elmos17_ENVP_AtgIni_e         SpxAtgIni                           ;        /**< ATG initial value */
  Elmos17_ENVP_AtgEn_e          SpxAtgCfg                           ;        /**< ATG configuration */

  uint8                       SpxEevalSel                         ;        /**< EEVAL data source selection bit0:TLH ,bit1: THL,bit2:MAX, bit3 MIN*/
  Elmos17_ENVP_EevalSens_e      SpxEevalSens                        ;        /**< EEVAL echo minimum/maximum evaluation sensitivity */
  Elmos17_ENVP_EevalVal_e       SpxEevalVal                         ;        /**< EEVAL select value to be stored via DMA */
  uint8                       SpxMeasGain                         ;        /**< AMPD digital amplifier gain configuration 0-127, 0=0dB ,Step = 0.38dB*/

  uint8                       SpxMeasMaskDirect                   ;        /**< Period at which echo measurement results should be ignored in direct mode ,times 32us*/
  uint8                       SpxMeasMaskIndirect                 ;        /**< Period at which echo measurement results should be ignored in indirect mode ,times 32us*/
  uint8          				SPxTOF_compensation_time           ;        /**< Datafield for customer to write time of flight compensation for standard path profile a in 32us steps */
  uint8          				SPxmeas_time                       ;        /**< Datafield for customer to write time of flight compensation for standard path profile a in 256us steps */
}CANCom_Meas_StdProfile;

typedef struct
{
  uint16         ApxBurstFreqLo                      ;        /**< BRG chirp low frequency ,frequency = ApxBurstFreqLo * 5.722 Hz*/

  uint16         ApxBurstFreqHi                      ;        /**< BRG chirp high frequency ,frequency = ApxBurstFreqHi * 5.722 Hz*/

  uint8          ApxBurstFreqDelta                   ;        /**< BRG chirp frequency step width */
  uint8          ApxFilterLength                     ;        /**< ENVP number of filter stages */

  uint8          ApxBurstPulses                      ;        /**< BRG number of burst pulses */
  uint8          ApxCodeLength                       ;        /**< BRG length of the code */
  uint8          ApxCode                             ;        /**< BRG code word to send */
  uint8          ApxDs                               ;        /**< ENVP down sampling rate select */

  uint8         ApxEnvpCoeff0;                                /**< ENVP filter coefficients in the advanced signal path -0  */
  uint8         ApxEnvpCoeff1;                                /**< ENVP filter coefficients in the advanced signal path -1 */
  uint8         ApxEnvpCoeff2;                                /**< ENVP filter coefficients in the advanced signal path -2 */
  uint8         ApxEnvpCoeff3;                                /**< ENVP filter coefficients in the advanced signal path -3 */
  uint8         ApxEnvpCoeff4;                                /**< ENVP filter coefficients in the advanced signal path -4 */
  uint8         ApxEnvpCoeff5;                                /**< ENVP filter coefficients in the advanced signal path -5 */
  uint8         ApxEnvpCoeff6;                                /**< ENVP filter coefficients in the advanced signal path -6 */
  uint8         ApxEnvpCoeff7;                                /**< ENVP filter coefficients in the advanced signal path -7 */
  uint8         ApxEnvpCoeff8;                                /**< ENVP filter coefficients in the advanced signal path -8 */
  uint8         ApxEnvpCoeff9;                                /**< ENVP filter coefficients in the advanced signal path -9 */
  uint8         ApxEnvpCoeff10;                               /**< ENVP filter coefficients in the advanced signal path -10 */
  uint8         ApxEnvpCoeff11;                               /**< ENVP filter coefficients in the advanced signal path -11 */
  uint8         ApxEnvpCoeff12;                               /**< ENVP filter coefficients in the advanced signal path -12 */
  uint8         ApxEnvpCoeff13;                               /**< ENVP filter coefficients in the advanced signal path -13 */
  uint8         ApxEnvpCoeff14;                               /**< ENVP filter coefficients in the advanced signal path -14 */
  uint8         ApxEnvpCoeff15;                               /**< ENVP filter coefficients in the advanced signal path -15 */
  uint8         ApxEnvpCoeff16;                               /**< ENVP filter coefficients in the advanced signal path -16 */
  uint8         ApxEnvpCoeff17;                               /**< ENVP filter coefficients in the advanced signal path -17 */
  uint8         ApxEnvpCoeff18;                               /**< ENVP filter coefficients in the advanced signal path -18 */
  uint8         ApxEnvpCoeff19;                               /**< ENVP filter coefficients in the advanced signal path -19 */
  uint8         ApxEnvpCoeff20;                               /**< ENVP filter coefficients in the advanced signal path -20 */
  uint8         ApxEnvpCoeff21;                               /**< ENVP filter coefficients in the advanced signal path -21 */
  uint8         ApxEnvpCoeff22;                               /**< ENVP filter coefficients in the advanced signal path -22 */
  uint8         ApxEnvpCoeff23;                               /**< ENVP filter coefficients in the advanced signal path -23 */
  uint8         ApxEnvpCoeff24;                               /**< ENVP filter coefficients in the advanced signal path -24 */
  uint8         ApxEnvpCoeff25;                               /**< ENVP filter coefficients in the advanced signal path -25 */
  uint8         ApxEnvpCoeff26;                               /**< ENVP filter coefficients in the advanced signal path -26 */
  uint8         ApxEnvpCoeff27;                               /**< ENVP filter coefficients in the advanced signal path -27 */
  uint8         ApxEnvpCoeff28;                               /**< ENVP filter coefficients in the advanced signal path -28 */
  uint8         ApxEnvpCoeff29;                               /**< ENVP filter coefficients in the advanced signal path -29 */
  uint8         ApxEnvpCoeff30;                               /**< ENVP filter coefficients in the advanced signal path -30 */
  uint8         ApxEnvpCoeff31;                               /**< ENVP filter coefficients in the advanced signal path -31 */


  uint8          ApxAatgCn                           ;        /**< AATG cell number configuration */
  uint8          ApxAatgCw                           ;        /**< AATG cell width configuration */
  uint8          ApxAatgAlpha                        ;        /**< AATG sensitivity configuration */
  uint8          ApxAatgOff                          ;        /**< AATG on/off flag */
  uint8          ApxEevalSens                        ;        /**< EEVAL echo minimum/maximum evaluation sensitivity */
  uint8          ApxMinConf                          ;        /**< Min confidence level for advanced measurement samples to be transmitted over dsi3 */

  uint8          ApxMeasMaskDirect                   ;        /**< Period at which echo measurement results should be ignored in direct mode */
  uint8          ApxMeasMaskIndirect                 ;        /**< Period at which echo measurement results should be ignored in indirect mode */
  
  uint8          APxTOF_compensation_time           ;        /**< Datafield for customer to write time of flight compensation for advanced path profile a in 32us steps */
  uint8          APxmeas_time                       ;        /**< Datafield for customer to write time of flight compensation for advanced path profile a in 256us steps */
} CANCom_Meas_AdvProfile;

typedef uint16 (*ConvMeasU16DataToStructFunc_t)(uint16 *pInU16Data,void *pOutConvertStruct);

typedef uint16 (*ConvMeasStructToU16DataFunc_t)(void *pInConvertStruct ,uint16 *pOutU16Data);


typedef struct
{
    uint8 ParamAddr;
    uint8 ParamU16Len;
    uint16 Structu8Len;
	void *pCfgStr[SNSNum];
    ConvMeasU16DataToStructFunc_t ConvU16DataToStructFunc;
    ConvMeasStructToU16DataFunc_t ConvStructToU16DataFunc; /** @brief elmos17 ²âÁ¿µØÖ·¼°Êý¾Ý×ª»»±í*/
}Elmos17MeasParamCfg_str;

typedef enum
{
    
    eWRSnsDataType_GeneralSet = 0,
    eWRSnsDataType_NoiseMeasSet,
    eWRSnsDataType_Std_ProfA ,
    eWRSnsDataType_Std_ProfB ,
    eWRSnsDataType_Std_ProfC ,
    eWRSnsDataType_Adv_ProfA ,
    eWRSnsDataType_Adv_ProfB ,
    eWRSnsDataType_TOFComp,
    eWRSnsDataType_Std_SEG_ProfA ,
    eWRSnsDataType_Std_SEG_ProfB ,
    eWRSnsDataType_Std_SEG_ProfC ,
    eWRSnsDataType_NULL,
    
    eWRSnsDataType_NUM = eWRSnsDataType_NULL,
}WRSnsDataType_e;

/***************************************************************************** */
/***************************************************************************** */
/****************************** Macro Definitions **************************** */
/***************************************************************************** */
/***************************************************************************** */

#define Elmos17MEAS_GeneralSettings_StartAddr     0u
#define Elmos17MEAS_NoiseMeasurement_StartAddr    10u
#define Elmos17MEAS_STDProf_A_StartAddr           14u
#define Elmos17MEAS_STDProf_B_StartAddr           36u
#define Elmos17MEAS_STDProf_C_StartAddr           58u
#define Elmos17MEAS_ADVProf_A_StartAddr           80u
#define Elmos17MEAS_ADVProf_B_StartAddr           108u
#define Elmos17MEAS_NfdSettings_StartAddr         136u
#define Elmos17MEAS_DsiSettings_StartAddr         144u
#define Elmos17MEAS_Monitoring_StartAddr          166u
#define Elmos17MEAS_DataForSelfTest_StartAddr     170u
#define Elmos17MEAS_TOFCompensation_StartAddr     178u
#define Elmos17MEAS_ADV_STG_StartAddr             188u

#define Elmos17MEAS_SnsGain_StartAddr             2u
#define Elmos17MEAS_SnsParamID_StartAddr          190u
#define Elmos17MEAS_CALVer_StartAddr              192u

#define Elmos17MEAS_GeneralSettings_u16Len        5u
#define Elmos17MEAS_NoiseMeasurement_u16Len       2u
#define Elmos17MEAS_STDProf_u16Len                11u
#define Elmos17MEAS_ADVProf_u16Len                14u
#define Elmos17MEAS_NfdSettings_u16Len            4u
#define Elmos17MEAS_DsiSettings_u16Len            11u
#define Elmos17MEAS_Monitoring_u16Len             2u
#define Elmos17MEAS_DataForSelfTest_u16Len        4u
#define Elmos17MEAS_TOFCompensation_u16Len        5u
#define Elmos17MEAS_ADV_STG_u16Len                1u

#define Elmos17MEAS_Param_Check_u16Len			  99u
#define Elmos17MEAS_ALLParam_u16Len               102u

/***************************************************************************** */
/***************************************************************************** */
/***************************** Symbol Definitions **************************** */
/***************************************************************************** */
/***************************************************************************** */

/***************************************************************************** */
/***************************************************************************** */
/*************************** Constants Declaration *************************** */
/***************************************************************************** */
/***************************************************************************** */


/***************************************************************************** */
/***************************************************************************** */
/*************************** Variables Declaration *************************** */
/***************************************************************************** */
/***************************************************************************** */



/***************************************************************************** */
/***************************************************************************** */
/*************************** Functions Declaration *************************** */
/***************************************************************************** */
/***************************************************************************** */



extern uint8 WRSnsMeasParamTask(DSIMasterID_en LeDSIMasterID);
extern WRSnsMeasParamStatus_e GetWRSnsMeasParamStatus(DSIMasterID_en LeDSIMasterID);
extern SnsID_en GetWRSnsMeasParamSnsID(DSIMasterID_en LeDSIMasterID);
extern Elmos17RetType_en InitWRSnsMeasParam_str(WRSnsMeasParam_str *pWRSnsMeasParam);
extern Elmos17RetType_en TriggerWRSnsMsg(DSIMasterID_en LeDSIMasterID,SnsCtrlGroup_Cfg *pSnsCtrlGroup,WRSnsMeasParam_str *pWRSnsMeasParam);
extern Elmos17RetType_en AbortWRMeasParam(DSIMasterID_en LeDSIMasterID);
extern void PrintfCRMRes(CRM_RESPONSE_Data_str *pResOutData);
extern void WriteCalAndAnaGain(uint16 InData,Meas_sGeneralSettings *pOutGeneralSet);
extern const Elmos17MeasParamCfg_str* GetMeasParamCfg(void);
extern const uint8* GetMeasParamDefaultVal(void);

#endif /** @brief  __Elmos17SnsMeasParamType_H__  */

