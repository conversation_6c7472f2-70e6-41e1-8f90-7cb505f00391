/******************************************************************************
 * @file       System_Schedule_prg.c
 * @brief      
 * @date       2025-03-04 11:40:35
 * <AUTHOR>
 * @copyright  Longhorn Automotive Electronics Technology (Xiamen) Co., Ltd.
 *****************************************************************************/


/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "System_Schedule_cfg.h"



/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/



/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/



/******************************************************************************
 * @Function Declaration
 *****************************************************************************/



/******************************************************************************
 * @Const Definitions
 *****************************************************************************/



/******************************************************************************
 * @Variable Definitions
 *****************************************************************************/
static TaskManageTypes astrTCB[SCHED_TASK_ID_NUM];

volatile uint8 u8TickCounter;




/******************************************************************************
 * @Function Definitions
 *****************************************************************************/


/******************************************************************************
 * @brief      任务切换时钟计数
 * <AUTHOR>
 * @date       2025-03-04 11:40:52
 * @note       
 *****************************************************************************/
void Sched_TickCounterHandler(void)
{
    u8TickCounter++;
}


/******************************************************************************
 * @brief      初始化系统任务的时间，其中u8TaskDelay指起始时间
 * <AUTHOR>
 * @date       2025-03-04 11:41:09
 * @note       
 *****************************************************************************/
void System_Sched_vidInit(void)
{
    uint8 u8Index;
    for(u8Index=0; u8Index<SCHED_TASK_ID_NUM; u8Index++)
    {
        astrTCB[u8Index].u16TaskCounter = 
                                       GastrPeriodSchedConfig[u8Index].u16TaskDelay;
        astrTCB[u8Index].enuTaskStatus = SCHED_TASK_ACTIVE;
    }
}


/******************************************************************************
 * @brief      任务调度
 * <AUTHOR>
 * @date       2025-03-04 11:41:33
 * @note       
 *****************************************************************************/
void System_Sched_Management(void)
{
    uint8 u8Index=0;
    for(;;)
    {
        /** @note 非周期任务调度 */
        NoperiodFunc();
        /** @note 周期任务调度 */
        if(u8TickCounter > BTY_u8ZERO)
        {
            /** @note Acknowledge the system tick by decrementing the counter */
            if(u8TickCounter<2)
            {
                u8TickCounter--;
            }
            else
            {
                u8TickCounter=0;
            }
            for(u8Index=0; u8Index<SCHED_TASK_ID_NUM; u8Index++)
            {
                if(astrTCB[u8Index].enuTaskStatus == SCHED_TASK_ACTIVE)
                {
                    if(astrTCB[u8Index].u16TaskCounter > BTY_u8ONE)
                    {
                        astrTCB[u8Index].u16TaskCounter--;
                    }
                    else
                    {
                        astrTCB[u8Index].u16TaskCounter = 
                                              GastrPeriodSchedConfig[u8Index].u16TaskPeriod;
                        
                        (*GastrPeriodSchedConfig[u8Index].pfTask)();
                        
                    }
                }
                else
                {
                    /** @note Do nothing */
                }
            }
        }
        else
        {
            /** @note Do nothing */
        }
    }
}
