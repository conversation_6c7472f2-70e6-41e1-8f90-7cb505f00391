/******************************************************************************
 * @file       SnsPPCalculate_Type.h
 * @brief      
 * @date       2025-03-04 14:41:47
 * <AUTHOR>
 * @copyright  Longhorn Automotive Electronics Technology (Xiamen) Co., Ltd.
 *****************************************************************************/


#ifndef SnsPPCalculate_Type_H
#define SnsPPCalculate_Type_H

/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "types.h"
#include "SnsRawData_Type.h"



/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/
#define NO_OBJECT               0xFB

#define NO_LISTEN_MAX_DIS       500u	/* 50cm内有侦听 */

#define WARNING_MAX_DIS         2500u

#define MASTER_DIATANCE_COORD   500u

#define MIN_WARNING             300
#define BLIND_KEEP_DIS          500

#define CENTER_SEN_X_MAX        600u
#define CORNER_SEN_X_MAX        600u

#define OBJ_FAR_CNT             3
#define OBJ_KEEP_CNT            2
#define OBJ_NEAR_CNT            2
#define OBJ_CORNER_FADE_CNT     4
#define OBJ_BLIND_FADE_CNT      25

#define MID_HYSTERESIS_DIS      50.0
#define AREA1_Y_DIS             650
#define AREA2_Y_DIS             0
#define AREA3_Y_DIS             -650
#define CORNER_TOLERANCE_VALUE  150
#define MIDDLE_TOLERANCE_VALUE  100
#define PLATE_WIDTH             300
#define PLATE_COMPENSATE        30
#define AREA_UPDATE_CNT         4

#define BUMPER_LEFT_Y           650
#define BUMPER_RINHT_Y          -650
#define CAR_WIDTH               1000

#define NO_LISTEN_DISTANCE              650     /* 双通道单主发限制距离 */
#define NO_LISTEN_DISTANCE_ONLY         650     /* 单通道单主发限制距离 */
#define ADJACENT_SNS_DISTANCE           1300    /* 双通道限制距离 */
#define BLIND_NO_LISTEN_DIS             500     /* 盲区内无侦听距离 */

#define STANDARD_SLOW_COMFIRM_DIS       500     /* 50cm内多次确认过滤同频干扰 */



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/
typedef enum
{
    SENSOR_MASTER_BURST = 0,
    SENSOR_LISTEN_LEFT = 1,
    SENSOR_LISTEN_RIGHT = 2,

    SENSOR_LISTEN_NUMBER = 3,
}tenu_SensorListenType;

typedef enum
{
    OBJ_MOVE_KEEP = 0,
    OBJ_MOVE_FAR,
    OBJ_MOVE_NEAR,
    OBJ_MOVE_FADE,

    OBJ_MOVE_TOTAL_STATUS,
}tenu_ObjMoveStatusType;

typedef enum
{
    PP_LINE0_1 = 0,
    PP_LINE1_2,
    PP_LINE2_3,
    PP_LINE3_4,
    PP_LINE4_5,
    PP_LINE_NUM
}ObjPPAreaLineType;

#if 0
typedef enum
{
    AREA_0 = 0,
    AREA_1 = 1,
    AREA_2 = 2,
    AREA_3 = 3,

    AREA_NUMBER =4,
    AREA_NONE = 0XFF,
}tenu_ObjZoneAera;
#else
typedef enum
{
    PP_AREA1,
    PP_AREA2,
    PP_AREA3, 
    PP_AREA4,
    PP_AREA_NUMBER,

    PP_AREA_NONE = 0xFF,
}ObjAreaType;
#endif

typedef struct
{
    uint16 u16MasterDis;
    uint16 u16LeftListenDis;
    uint16 u16RightListenDis;
    uint16 u16MasterEchoHeight;
    uint16 u16LeftLisEchoHeight;
    uint16 u16RightLisEchoHeight;
    uint16 u16MasterActualDis;
    uint16 u16LeftLisActualDis;
    uint16 u16RightLisActualDis;
}SnsDisDataType;

typedef struct
{
    uint8  u8Area;
    uint8  u8AreaUpdateCnt;
    uint8  u8ObjListenFlag;
    uint8  u8SnsCoordValidFlag;
    uint8  u8MasterBurstValidFlag;
    float fObjTOBumperDis;
    ObjCoorType strObjSnsCoord;
    ObjCoorType strObjCarCoord;
}SystemObjInfoTypedef;

typedef struct
{
    uint16 u16L1_Distance;
    uint16 u16L2_Distance;

    uint16 u16ML1_Distance;
    uint16 u16ML2_Distance;

    uint16 u16MR1_Distance;
    uint16 u16MR2_Distance;

    uint16 u16R1_Distance;
    uint16 u16R2_Distance;
}PPDis_ZONETye;

typedef struct
{
    uint8 u8MoveSta;
    uint8 u8CarDiret;
    uint8 u8BlindKeepFlag;
    uint8 u8SysDisUpdateCnt[OBJ_MOVE_TOTAL_STATUS];
    uint16 u16SysDistance;
    uint16 u16MoveStaCnt;
}SysDisInfoType;



/******************************************************************************
 * @Const Declaration
 *****************************************************************************/



/******************************************************************************
 * @Variable Declaration
 *****************************************************************************/



/******************************************************************************
 * @Function Declaration
 *****************************************************************************/



#endif
