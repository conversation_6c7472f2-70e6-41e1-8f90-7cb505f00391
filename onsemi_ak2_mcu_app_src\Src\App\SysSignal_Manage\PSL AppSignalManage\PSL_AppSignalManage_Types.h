/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * CAN_AppSignalManage:
 * Created on: 2022-11-25 14:19
 * Original designer: 22866
 ******************************************************************************/


#ifndef __PSL_APPSIGNALMANAGE_TYPES_H__
#define __PSL_APPSIGNALMANAGE_TYPES_H__


/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
//#include "Std_Types.h"
#include "Types.h"


/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/
#define SPEED_10KMH              (uint16)1000
#define SPEED_12KMH              (uint16)1200
#define SPEED_15KMH              (uint16)1500
#define SPEED_20KMH              (uint16)2000
#define SPEED_25KMH              (uint16)2500
#define SPEED_30KMH              (uint16)3000


#define APA_360_ANGLE                                            6.280//4553 
#define APA_270_ANGLE                                            4.710//4553 
#define APA_180_ANGLE                                            3.140//4553 
#define APA_150_ANGLE                                            2.61666//4553 
#define APA_135_ANGLE                                            2.355//4553 
#define APA_120_ANGLE                                            2.093//4553 
#define APA_110_ANGLE                                            1.919//4553 
#define APA_100_ANGLE                                            1.744//4553 
#define APA_95_ANGLE                                             1.6572//4553 
#define APA_90_ANGLE                                             1.57//4553 
#define APA_89_ANGLE                                             1.553//4553 
#define APA_87_ANGLE                                             1.5177//4553 
#define APA_85_ANGLE                                             1.4828//4553
#define APA_80_ANGLE                                             1.3956//4553
#define APA_70_ANGLE                                             1.2211//4553 
#define APA_60_ANGLE                                             1.04668//4553 
#define APA_55_ANGLE                                             0.95946//4553 
#define APA_50_ANGLE                                             0.872665//4553 
#define APA_46_ANGLE                                             0.8024//4553
#define APA_45_ANGLE                                             0.7850//4553
#define APA_30_ANGLE                                             0.5233//4553
#define APA_26_ANGLE                                             0.4535//4553
#define APA_25_ANGLE                                             0.43611//4553
#define APA_20_ANGLE                                             0.3489//4553
#define APA_15_ANGLE                                             0.2617//4553
#define APA_12_ANGLE                                             0.2093//4553
#define APA_10_ANGLE                                             0.1744//4553 
#define APA_8_ANGLE                                              0.1396//4553 
#define APA_7_5_ANGLE                                            0.131//4553 
#define APA_7_ANGLE                                              0.122//4553 
#define APA_6_ANGLE                                              0.10466//4553
#define APA_5_5_ANGLE                                            0.0959//4553 
#define APA_5_ANGLE                                              0.0872//4553 
#define APA_4_ANGLE                                              0.0678//degree
#define APA_3_ANGLE                                              0.05233 //degree
#define APA_2_5_ANGLE                                            0.0436 //4553
#define APA_2_ANGLE                                              0.0349//4553
#define APA_1_5_ANGLE                                            0.0262//4553
#define APA_1_ANGLE                                              0.0174//4553 
#define APA_0_7_5_ANGLE                                          0.01305//4553 
#define APA_0_5_ANGLE                                            0.0087//4553 
#define APA_0_2_5_ANGLE                                          0.00435//4553 
#define APA_0_ANGLE                                              0
#define _APA_80_ANGLE                                            -1.3956
#define _APA_100_ANGLE                                           -1.744
#define _APA_90_ANGLE                                            -1.57
#define _APA_180_ANGLE                                           -3.140
/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/

typedef struct
{
    uint8 u8ApaSlotID;
    uint16 u16ApaSlotLength;
    uint16 u16ApaSlotDepth;
    uint8 u8SlotdepthRef;
    uint8 u8ApaSlotType;
    uint8 u8ApaSlotStatus;
    sint16 dFirstObjWx;
    sint16 dFirstObjWy;
    sint16 dSecondObjWx;
    sint16 dSecondObjWy;
    sint16 s16Obj1Alpha;
    sint16 s16Obj2Alpha;
    uint8  u8FirstObjType;
    uint8  u8SecObjType;
    uint8  u8CurbType;
    uint32 u32slotsynctime;
    bool   bPSLWriteLockFlag;
}PSLOutputinfoType;




#endif/* end of __PSL_SIGNALMANAGE_TYPES_H__ */