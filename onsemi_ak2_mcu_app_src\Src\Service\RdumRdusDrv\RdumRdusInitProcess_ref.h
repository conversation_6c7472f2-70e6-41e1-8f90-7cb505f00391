/******************************************************************************
 * @file      RdumRdusInitProcess.h
 * @brief     安森美超声波探头初始化流程接口
 * <AUTHOR>
 * @date      2025-05-20
 * @note      
 *****************************************************************************/

#ifndef __RDUM_RDUS_INIT_PROCESS_H__
#define __RDUM_RDUS_INIT_PROCESS_H__
/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "types.h"

/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/

/******************************************************************************
 * @Type Definitions
 *****************************************************************************/

/******************************************************************************
 * @Function Declaration
 *****************************************************************************/
/* 初始化流程模块初始化 */
void RdumRdusInitProcess_Init(void);

/* 启动初始化流程 */
uint8 RdumRdusInitProcess_Start(RdumRdusInitType_t InitType);

/* 处理初始化流程 */
void RdumRdusInitProcess_Process(void);

/* 获取初始化流程状态 */
RdumRdusInitProcessStatus_t RdumRdusInitProcess_GetStatus(void);

/* 获取初始化流程类型 */
RdumRdusInitType_t RdumRdusInitProcess_GetType(void);

/* 获取初始化流程进度 */
uint8 RdumRdusInitProcess_GetProgress(void);

#endif /* __RDUM_RDUS_INIT_PROCESS_H__ */
