
#ifndef _DID_CBK_H_
#define _DID_CBK_H_

#include "Types.h"
#include "EELHal.h"
#include "UserFlash.h"


#if (CALIBRATION_EN == STD_ON)
#define DID_CFG_HANDLE   &CalibrationDIDDefine[u16Handle]

#include "Sns_install_Coordinate_Types.h"
#include "Sns_install_Coordinate.h"
#include "MapRawDataCalib.h"


extern uint8_t Did_ReadData(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);


extern uint8_t Did_ReadData_FDC0(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FDC0(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD01(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_ReadData_FD02(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_ReadData_FD03(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_ReadData_FD04(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_ReadData_FD05(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_ReadData_FD06(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_ReadData_FD07(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_ReadData_FD08(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_ReadData_FD09(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_ReadData_FD0A(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD0A(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD0B(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_ReadData_FD0C(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_ReadData_FD0D(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_ReadData_FD0E(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD0E(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD00(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD00(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD20(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_ReadData_FD21(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD21(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD22(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD22(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD23(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD23(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD24(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD24(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD25(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD25(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD26(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD26(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD27(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD27(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD28(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD28(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD29(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD29(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD2A(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD2A(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD2B(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD2B(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD2C(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD2C(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD2D(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD2D(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD2E(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD2E(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD2F(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD2F(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD40(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD40(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD41(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD41(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD42(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD42(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD43(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD43(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD44(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD44(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD45(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD45(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD46(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD46(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD47(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD47(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD48(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD48(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD49(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD49(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD50(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD50(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD51(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD51(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD52(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD52(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD53(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD53(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD54(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD54(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD55(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD55(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD56(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD56(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD57(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD57(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD58(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD58(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD59(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD59(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD5A(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD5A(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD5B(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD5B(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD5C(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD5C(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD5D(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD5D(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD5E(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD5E(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD5F(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD5F(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD60(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD60(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD61(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD61(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD62(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD62(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD63(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD63(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD64(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD64(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD65(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD65(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD66(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD66(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD67(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD67(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FD70(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FD70(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FDA0(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FDA0(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FDA1(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FDA1(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FDA2(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FDA2(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FDA3(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FDA3(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);
extern uint8_t Did_ReadData_FDA4(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
extern uint8_t Did_WriteData_FDA4(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);

#endif // (CALIBRATION_EN == STD_ON)
#endif /*_DID_CBK_H_*/


/* =================================================================================================================
 * UDS_CFG_GEN_V4.5.exe 4.5.0.0
 * Vehicle CAN DBC file: Byd_SF&HC&UF_10V_PAS_CANFD_Matrix_V1.5-20221108.dbc | 
 * Excel Configuration file: UDS_CFG_V001_BYD_SF.xlsx | 448169F7B51E0D8E1021EFEE64FB449A
 * ================================================================================================================= */

