/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: UartDrv.h
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/
#ifndef __UART_DRV_H
#define __UART_DRV_H
#include "types.h"

/**********************************宏定义**************************************/
#define _UART2_BAUD_RATE_PRESCALER                      (0x001CU)    /**< 波特率115200对应的分频值 */

/*******************************全局函数声明***********************************/
extern void UartDrvInit(void);
extern void UartDrvClose(void);
extern uint8 UartGetRxData(void);
extern void UartSetTxData(uint8 Lu8Data);
extern uint8 UartGetTxStatus(void);

#endif

