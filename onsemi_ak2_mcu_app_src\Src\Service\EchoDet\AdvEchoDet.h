/*
 * AdvEchoDet.h
 *
 *  Created on: 2021年7月1日
 *      Author: 6000021992
 */

#ifndef ECHODET_ADVECHODET_H_
#define ECHODET_ADVECHODET_H_

#include "Elmos_524_17SnsMeasCfg.h"

#define FML_FMR_BumperDIS 58
#define FL_FML_BumperDIS  35
#define FLS_FL_BumperDIS  45

#define RML_RMR_BumperDIS 63
#define RL_RML_BumperDIS  54
#define RLS_RL_BumperDIS  28

#define FML_FMR_BumperOffsetDIS 2
#define FL_FML_BumperOffsetDIS  6
#define FLS_FL_BumperOffsetDIS  8

#define RML_RMR_BumperOffsetDIS 2
#define RL_RML_BumperOffsetDIS  7
#define RLS_RL_BumperOffsetDIS  4


typedef enum
{
    Listen_Left = 0,
    Listen_Right,
    ListenDist_LR_Num,
}ListenDist_LR_en;

typedef struct
{
    DistanceType_en DisType; /*距离类型*/
    uint16 EchoTimeStamp;
    uint16 Dis;
    uint16 Amp;
    uint16 WidthOrConf;
    
}ECHO_DisMsg_Str;

#define DisMsgMaxNum 10
typedef struct
{
    SNS_MEAS_TYPE_en   SNS_MEAS_TYPE;
    uint8 SaveDisDataCnt;
        
    DistanceType_en DisType[DisMsgMaxNum]; /*距离类型*/
    uint16 EchoTimeStamp[DisMsgMaxNum];
    uint16 Dis[DisMsgMaxNum];
    uint16 Amp[DisMsgMaxNum];
    uint16 WidthOrConf[DisMsgMaxNum];


}Sns_DetDist_Str;

typedef struct
{
	uint16 RTM;                              /** @brief 余震时间 */   
	uint16 RFreq;							 /** @brief 余震频率 */
	uint16 ImpAndImpIdx[2];                  /** @brief  data0 阻抗频率索引 ,data1 阻抗峰值*/  
	uint16 NoiseSum;    					 /** @brief  噪音累加值  */
    uint8 NoiseAbnormalCnt;                        
}Sns_DiagData_Str;

typedef struct
{
    MeasType_en MeasType;
    CMDSeq_en MeasSeq;
    SnsMeasProfile_en MeasProfile;           /** @brief profile  */
    Sns_DetDist_Str SnsDistMsg;
	Sns_DiagData_Str SnsDiagData;

    uint32 TransMeasCmdTime;
    uint32 UpdateTime;
	uint32 ADASSYNTime;
}SnsMeasDistData_Str;

typedef struct
{
    uint16 StartDis;
    uint16 DisStep;
    uint16 Size;
    uint16 *pthreshold;
    
}ThresholdSet_str;

typedef struct
{
	bool NFDFlg;		/** @brief NFD标志位 */ 
	uint16 NFDDis;    /** @brief NFD标志置位后输出的距离,uint:mm,距离无法计算时,直接输出100*/
}NFDOutput_t;

extern uint16 CalcDisThreshold(ThresholdSet_str * pThresholdSet,uint16 distance);
extern uint8 EchoDet_SnsEchoHandle(SnsID_en LeSnsID,SNS_MEAS_TYPE_en SNS_MEAS_TYPE,ThresholdSet_str * pThresholdSet,Sns_ECHOMsg_Str *pInECHOData,Sns_DetDist_Str *pOutDistDat,int32 TimeCompensation,Sns_PDCM_NFDData *pNFDData);
extern void EchoDet_SnsSTDNFDHandle(SnsID_en LeSnsID,Sns_PDCM_NFDData *pNFDData,Sns_DetDist_Str *pOutDistDat);
extern const NFDOutput_t *EchoDet_NFDResult(SnsID_en LeSnsID);
extern uint8 EchoDet_NoiseHandle(SnsID_en LeSnsID,const Sns_DiagData_Str *pDiagData);
extern void UpdateTemperatureDistance(void);

#endif /* ECHODET_ADVECHODET_H_ */
