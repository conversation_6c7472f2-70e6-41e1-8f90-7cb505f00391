"""
创建发布包
"""

import os
import shutil
import zipfile

def create_release():
    """创建发布包"""
    print("开始创建发布包...")
    
    # 创建发布目录
    release_dir = "release"
    if os.path.exists(release_dir):
        shutil.rmtree(release_dir)
    os.makedirs(release_dir)
    
    # 复制可执行文件
    shutil.copy("dist/命令解析工具.exe", os.path.join(release_dir, "命令解析工具.exe"))
    
    # 复制README文件
    shutil.copy("README.md", os.path.join(release_dir, "README.md"))
    
    # 创建一个空的历史记录文件
    with open(os.path.join(release_dir, "command_history.json"), "w", encoding="utf-8") as f:
        f.write("[]")
    
    # 创建ZIP文件
    with zipfile.ZipFile("命令解析工具_发布包.zip", "w", zipfile.ZIP_DEFLATED) as zipf:
        for root, _, files in os.walk(release_dir):
            for file in files:
                file_path = os.path.join(root, file)
                zipf.write(file_path, os.path.relpath(file_path, release_dir))
    
    print("发布包创建完成！")
    print(f"发布包位于: {os.path.abspath('命令解析工具_发布包.zip')}")

if __name__ == "__main__":
    create_release()
