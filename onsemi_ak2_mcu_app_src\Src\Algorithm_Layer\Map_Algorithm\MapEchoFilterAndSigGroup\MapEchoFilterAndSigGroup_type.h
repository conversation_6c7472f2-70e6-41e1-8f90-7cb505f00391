/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * MapEchoFilterAndSigGroup_type: 
 * Created on: 2023-12-22 20:37
 * Original designer: AntonyFang
 ******************************************************************************/
#ifndef MapEchoFilterAndSigGroup_type_H
#define MapEchoFilterAndSigGroup_type_H

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "SnsRawData_Type.h"
#include "CAN_AppSignal_CommonTypes.h"


/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/
#define MAP_MASTER_COMPEN_DIS                0u
#define MAP_LISTEN_COMPEN_DIS                0u

#define MAP_NFD_ECHO_HEIGHT                  2000

#define MAP_STD_SECOND_THAN_FIRST_HEIGHT     850      /* 定频回波高度修改为14bit后,此处同样修改为350 */
#define MAP_CHIRP_SECOND_THAN_FIRST_HEIGHT   400      /* 扫频灵敏度提升后，该参数同样需要修改 */

#define MAP_STD_MASTER_CMP_THRES_1KM         5
#define MAP_STD_MASTER_CMP_THRES_3KM         10
#define MAP_STD_MASTER_CMP_THRES_5KM         15
#define MAP_STD_MASTER_CMP_THRES_7KM         20
#define MAP_STD_MASTER_CMP_THRES_MORE_7KM    25

#define MAP_CHIRP_MASTER_CMP_THRES_1KM       5
#define MAP_CHIRP_MASTER_CMP_THRES_3KM       10
#define MAP_CHIRP_MASTER_CMP_THRES_5KM       15
#define MAP_CHIRP_MASTER_CMP_THRES_7KM       20
#define MAP_CHIRP_MASTER_CMP_THRES_MORE_7KM  25

#define MAX_NUM_OF_MAP_SIG_GROUP     3u
#define MAP_TABLE_STEP           100   /* 单位mm，实际10cm */
#define MAP_HIGH_TABLE_STEP      100   /* 单位mm，实际10cm，障碍物高度分类按照10cm步进进行处理 */
#define MAP_TABLE_MAX_DIS        4000  /* 单位mm，实际300cm,感度表和阈值暂设置到4000以内，以外统一使用3000的值 */

#define MAP_MASTER_SECOND_ECHO_DIS  200  /* 第二回波主发距离差值--调整主发和侦听的匹配阈值，防止错误匹配--2024-3-25 */
#define MAP_MASTER_SECOND_ECHO_DIS2 250  /* 第二回波主发距离差值-远距离 */

#define MAP_LISTEN_SECOND_ECHO_DIS  200  /* 第二回波侦听距离 */
#define MAP_LISTEN_SECOND_ECHO_DIS2 250  /* 第二回波侦听距离 */
#define MAP_SIG_GROUP_INVALID_DIS   65535

#define MAP_REAL_TIME_INVALID_DIS   65535
#define MAP_NEAR_REAL_TIME_DIS      500
#define MAP_MIDDLE_REAL_TIME_DIS    2300
#define MAP_FAR_REAL_TIME_DIS       4000
#define MAP_REAL_TIME_LISTEN_DIS    2500    /** @brief: 1000 修改为2500 */

#define MAP_SAME_FREQ_HOLD_TIME     10000   /* 一旦判断为同频后，维持同频标志10S  */


#define MAP_SIDE_NEAR_ALL_HIGH_DIS  500    /* 侧边近距离Map小于50cm，默认设置为高 */

#define MAP_SIG_SECOND_DIS_ADD      50      /* 按5cm的偏差值进行补偿 */

/* 定义车衣检测相关的宏及配置 */
#define SNS_COVER_DIS_BUF_CNT           3
#define SNS_COVER_JUDGE_ACTIVE_LOW_SPD  400
#define SNS_COVER_JUDGE_ACTIVE_HIGH_SPD 1500
#define SNS_EXIT_SPD                    800
/* 距离判断和回波高度分阶段进行 */
#define SNS_COVER_STD_NEAR_DIS_0_20     200
#define SNS_COVER_STD_NEAR_DIS_20_25    250
#define SNS_COVER_STD_NEAR_DIS_25_30    300

#define SNS_COVER_STD_NEAR_HEIGHT_0_20  35000
#define SNS_COVER_STD_NEAR_HEIGHT_20_25 10000
#define SNS_COVER_STD_NEAR_HEIGHT_25_30 8000

/* 前后探头： 设置1m
*  侧边探头低速：20cm以内--2m；30cm以内 4m
*  侧边探头高速：20cm以内--5m；30cm以内 10m
*/
#define SNS_LOW_SPD_F_R_SNS_DIS         1000
#define SNS_LOW_SPD_SIDE_SNS_DIS_20CM   2000
#define SNS_LOW_SPD_SIDE_SNS_DIS_30CM   4000
#define SNS_HIGH_SPD_SNS_DIS_20_CM      5000
#define SNS_HIGH_SPD_SNS_DIS_30_CM      10000
#define SNS_COVER_HIGH_SPD_EXIT_CNT     10
#define SNS_COVER_LOW_SPD_EXIT_CNT      20

#define SNS_COVER_CHIRP_NEAR_DIS      500
#define SNS_COVER_CHIRP_NEAR_HEIGHT   10000


/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/


/*****************************************Map信号组缓存定义********************************************  */
/******************************************************************************
* 设计描述 : 保留探头过去3个周期的缓存数据
* 设计索引 : 
*******************************************************************************/
typedef enum
{    
    MAP_CYCLE_0 = 0u,
        
    MAP_CYCLE_1,   
    
    MAP_CYCLE_2,      
    
    MAP_CYCLE_NUM,    
}Map_CycleType; 


/******************************************************************************/
/**
 * @brief  信号组数据类型定义
 */
/******************************************************************************/
typedef struct
{
    uint8                   u8SigGroupCnt;
    uint8                   u8GroupEchoCnt[MAX_NUM_OF_MAP_SIG_GROUP];      /* 回波信号组中的回波个数,1~2个 */
    uint16                  u16FirstEchoDis[MAX_NUM_OF_MAP_SIG_GROUP];     /* 第一回波距离 */
	uint16                  u16FirstEchoHeight[MAX_NUM_OF_MAP_SIG_GROUP];  /* 第一回波高度 */
    uint16                  u16SecondEchoDis[MAX_NUM_OF_MAP_SIG_GROUP];    /* 第二回波距离 */
	uint16                  u16SecondEchoHeight[MAX_NUM_OF_MAP_SIG_GROUP]; /* 第二回波高度 */
    uint16                  u16ActualDis[MAX_NUM_OF_MAP_SIG_GROUP];        /* 该回波信号组的实际使用的距离 */
    uint16                  u16FirstDisBackup[MAX_NUM_OF_MAP_SIG_GROUP];   /* 第一回波距离的备份，用于过滤第一回波前的噪点组成的高路沿 */
	uint16                  u16MaxHeight[MAX_NUM_OF_MAP_SIG_GROUP];        /* 该回波信号组的最大回波高度 */
#if VS_SIMULATE_ENABLE
    uint32                  enuOriObjType[MAX_NUM_OF_MAP_SIG_GROUP];       /* 通过回波高度初步判定的障碍物类型，主要用于DE CE置信度初始值选取 */
#else
    OriginalObjType         enuOriObjType[MAX_NUM_OF_MAP_SIG_GROUP];       /* 通过回波高度初步判定的障碍物类型，主要用于DE CE置信度初始值选取 */
#endif
}MapSigGroupDataUnitType;


/******************************************************************************/
/**
 * @brief   探头原始数据对应系统的参数，含Odo移动距离及系统定时器时间
 */
/******************************************************************************/
typedef struct
{
    uint32                 u32SysTime;
    float                  fCarMoveDisSub;
#if VS_SIMULATE_ENABLE
    uint32                 eCarDir;
    uint32                 eMeasType;
#else
    SnsCarMoveDirType      eCarDir;
    PDCSnsMeasTypeType     eMeasType;
#endif
    uint8                  u8SnsNoiseCnt;
}MapOrigSystemDataType;

/******************************************************************************/
/**
 * @brief  定义每一个通道的回波信号组数据缓存
 */
/******************************************************************************/
typedef struct
{
    uint8                   u8SigGroupUpdateFlag;                      /* 探头原始信号组数据更新标志 */
    uint8                   u8RecordCnt;                               /* 已经记录的缓存数据个数 */
#if VS_SIMULATE_ENABLE
    uint32                  enuCurIndex;                               /* 当前的索引 */
#else
    Map_CycleType           enuCurIndex;                               /* 当前的索引 */
#endif
    MapOrigSystemDataType   SysDataBuf[MAP_CYCLE_NUM];
    MapSigGroupDataUnitType MasterBuf[MAP_CYCLE_NUM];
    MapSigGroupDataUnitType LeftBuf[MAP_CYCLE_NUM];
    MapSigGroupDataUnitType RightBuf[MAP_CYCLE_NUM];
}MapSigGroupDataCacheType;


/******************************************************************************/
/**
 * @brief   Map模块中，探头的实时距离
 */
/******************************************************************************/
typedef struct
{
    PDCSnsMeasTypeType eMeasType;
    uint16 u16MasterHeight;
    uint16 u16MasDis;
    uint16 u16LeftDis;
    uint16 u16RightDis;
    uint16 u16ChirpMasDis;
    uint16 u16ChirpLeftDis;
    uint16 u16ChirpRightDis;
    uint8  u8RealDisUpdateFlag;
    uint8  u8MasSecondFlag;
    uint8  u8LeftSecondFlag;
    uint8  u8RightSecondFlag;
}MapSnsRealTimeDisType;


typedef enum
{    
    PARKING_STANDBY = 0u,        /* 泊车处于待机模式 */
    PARKING_SEARCH,              /* 泊车处于找车位模式 */
    PARKING_APA,                 /* 泊车处于泊车状态 */
    PARKING_RPA,                 /* 泊车处于RPA状态 */
    PARKING_SUMMON               /* 泊车处于直线召唤状态 */
}Parking_StsType; 

typedef enum
{    
    SLOT_TYPE_NONE = 0u,         /* 未知车位类型 */
    SLOT_PARALLEL,               /* 平行泊车 */
    SLOT_VERTICAL                /* 垂直泊车 */
}Parking_SlotType; 

typedef enum
{    
    PARK_DIR_NONE = 0u,         /* 未知泊车方向 */
    PARK_DIR_LEFT,              /* 左侧泊车 */
    PARK_DIR_RIGHT              /* 右侧泊车 */
}Parking_DirType;


/*  计算车辆在Guidence状态下处理变量*/
typedef struct
{
    ADAS_APAStatusType eCurrentAdasSts;
    ADAS_APAStatusType eLastAdasSts;
    LowVolPwrMdFlag2_Type eLowVolPwrMdFlag2;
    Parking_StsType    eParking_Sts;
    Parking_SlotType   eSlotType;
    Parking_DirType    eParkDir;
    float fSummonSideDis;
    float fSearchToGuidenceCarAngle;
    float fCarAngleSubBackup;
    float fCarAngleSub;
    float fParkingFinishDrvDis;
    float fFrontSideStartMapDis;        /* 前侧Map开始画的距离 */
    float fRearSideStartMapDis;         /* 后侧雷达Map开始画的距离 */
    uint32 u32SameFreqNoiseUpdateTime[2];
    uint32 u32LeftSideNoiseUpdateTime[2];
    uint32 u32RightSideNoiseUpdateTime[2];
    uint8 u8ParkingFinishFlag;
    uint8 u8DeleAllMapFlag;
    uint8 u8SameFreqNoiseFlag[2];    /* 前后保同频标志位 */
    uint8 u8LeftSideFreqNoiseFlag[2];
    uint8 u8RightSideFreqNoiseFlag[2];
    uint8 u8ParkingStsLockFlag;
    uint8 u8SlotTypeLockFlag;          /* 车位类型锁住标志 */
    uint8 u8CarForwardVerticalSlotLockFlag;
}ParkingGuidenceDataType;


/* 车衣检测相关结构定义 */
typedef struct
{
    float                      fCarDrvDisStart;
    float                      fCarMoveDis;
    uint16                     u16StdDis[SNS_COVER_DIS_BUF_CNT];
    uint16                     u16ChirpDis[SNS_COVER_DIS_BUF_CNT];
    uint16                     u16OutRangeCnt;
    uint8                      u8CarCoverFlag;
    uint8                      u8ContinueChirpCnt;
}SnsCoverDataType;


/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/








#endif /* end of SnsEchoFilterAndSigGroup_type_H */

