/******************************************************************************
 * @file       BaseDrv.h
 * @brief      
 * @date       2025-03-22 20:38:29
 * <AUTHOR>
 * @copyright  Longhorn Automotive Electronics Technology (Xiamen) Co., Ltd.
 *****************************************************************************/


#ifndef __BASE_DRV_H__
#define __BASE_DRV_H__
/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "SpiCom_Prg.h"
#include "types.h"



/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/



/******************************************************************************
 * @Const Declaration
 *****************************************************************************/



/******************************************************************************
 * @Variable Declaration
 *****************************************************************************/



/******************************************************************************
 * @Function Declaration
 *****************************************************************************/
uint8 BaseDrv_Crc8Calculate(const uint8 *Data, uint32 Len, uint8 crc_init);

uint32 BaseDrv_GetSys1MsTick(void);

SpiTransReturn_t BaseDrv_Master0SpiAsyncTransmit(const uint8 *SendBuffer, uint8 *ReceiveBuffer, uint16 TransferByteCnt);
SpiTransReturn_t BaseDrv_Master1SpiAsyncTransmit(const uint8 *SendBuffer, uint8 *ReceiveBuffer, uint16 TransferByteCnt);



#endif
