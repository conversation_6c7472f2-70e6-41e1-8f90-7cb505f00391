/*
 * CAN_IL_V07.h
 *
 *  Created on: 2021年3月25日
 *      Author: 6000022648
 */
#ifndef __CAN_IL_H__
#define __CAN_IL_H__

/******************************************************************************* 
* Includes 
********************************************************************************/ 
#include "Types.h"
#include "CANDrv.h"
#include "CANCfg.h"
/****************************************************************************** 
* Constants and macros 
*******************************************************************************/
#define INPUT_PARAM_NUM     95u

#define MAPOBJECT_OFFSET   (uint16)1022u
typedef union
{
    uint8 byte[64];
    struct
    {
        uint8 Obj00P1XH8Bit : 8;
        uint8 Obj00P1YH7Bit : 7;
        uint8 Obj00P1XL1Bit : 1;
        uint8 Obj00P2XH6Bit : 6;
        uint8 Obj00P1YL2Bit : 2;
        uint8 Obj00P2YH5Bit : 5;
        uint8 Obj00P2XL3Bit : 3;
        uint8 Obj00TypeH4Bit : 4;
        uint8 Obj00P2YL4Bit : 4;
        uint8 Obj01P1XH7Bit : 7;
        uint8 Obj00TypeL1Bit : 1;
        uint8 Obj01P1YH6Bit : 6;
        uint8 Obj01P1XL2Bit : 2;
        uint8 Obj01P2XH5Bit : 5;
        uint8 Obj01P1YL3Bit : 3;
        uint8 Obj01P2YH4Bit : 4;
        uint8 Obj01P2XL4Bit : 4;
        uint8 Obj01TypeH3Bit : 3;
        uint8 Obj01P2YL5Bit : 5;
        uint8 Obj02P1XH6Bit : 6;
        uint8 Obj01TypeL2Bit : 2;
        uint8 Obj02P1YH5Bit : 5;
        uint8 Obj02P1XL3Bit : 3;
        uint8 Obj02P2XH4Bit : 4;
        uint8 Obj02P1YL4Bit : 4;
        uint8 Obj02P2YH3Bit : 3;
        uint8 Obj02P2XL5Bit : 5;
        uint8 Obj02TypeH2Bit : 2;
        uint8 Obj02P2YL6Bit : 6;
        uint8 Obj03P1XH5Bit : 5;
        uint8 Obj02TypeL3Bit : 3;
        uint8 Obj03P1YH4Bit : 4;
        uint8 Obj03P1XL4Bit : 4;
        uint8 Obj03P2XH3Bit : 3;
        uint8 Obj03P1YL5Bit : 5;
        uint8 Obj03P2YH2Bit : 2;
        uint8 Obj03P2XL6Bit : 6;
        uint8 Obj03TypeH1Bit : 1;
        uint8 Obj03P2YL7Bit : 7;
        uint8 Obj04P1XH4Bit : 4;
        uint8 Obj03TypeL4Bit : 4;
        uint8 Obj04P1YH3Bit : 3;
        uint8 Obj04P1XL5Bit : 5;
        uint8 Obj04P2XH2Bit : 2;
        uint8 Obj04P1YL6Bit : 6;
        uint8 Obj04P2YH1Bit : 1;
        uint8 Obj04P2XL7Bit : 7;
        uint8 Obj04P2YL8Bit : 8;
        uint8 Obj05P1XH3Bit : 3;
        uint8 Obj04Type5Bit : 5;
        uint8 Obj05P1YH2Bit : 2;
        uint8 Obj05P1XL6Bit : 6;
        uint8 Obj05P2XH1Bit : 1;
        uint8 Obj05P1YL7Bit : 7;
        uint8 Obj05P2XL8Bit : 8;
        uint8 Obj05P2YH8Bit : 8;
        uint8 Obj06P1XH2Bit : 2;
        uint8 Obj05Type5Bit : 5;
        uint8 Obj05P2YL1Bit : 1;
        uint8 Obj06P1YH1Bit : 1;
        uint8 Obj06P1XL7Bit : 7;
        uint8 Obj06P1YL8Bit : 8;
        uint8 Obj06P2XH8Bit : 8;
        uint8 Obj06P2YH7Bit : 7;
        uint8 Obj06P2XL1Bit : 1;
        uint8 Obj07P1XH1Bit : 1;
        uint8 Obj06Type5Bit : 5;
        uint8 Obj06P2YL2Bit : 2;
        uint8 Obj07P1XL8Bit : 8;
        uint8 Obj07P1YH8Bit : 8;
        uint8 Obj07P2XH7Bit : 7;
        uint8 Obj07P1YL1Bit : 1;
        uint8 Obj07P2YH6Bit : 6;
        uint8 Obj07P2XL2Bit : 2;
        uint8 Obj07Type5Bit : 5;
        uint8 Obj07P2YL3Bit : 3;
        uint8 Obj08P1XH8Bit : 8;
        uint8 Obj08P1YH7Bit : 7;
        uint8 Obj08P1XL1Bit : 1;
        uint8 Obj08P2XH6Bit : 6;
        uint8 Obj08P1YL2Bit : 2;
        uint8 Obj08P2YH5Bit : 5;
        uint8 Obj08P2XL3Bit : 3;
        uint8 Obj08TypeH4Bit : 4;
        uint8 Obj08P2YL4Bit : 4;
        uint8 Obj09P1XH7Bit : 7;
        uint8 Obj08TypeL1Bit : 1;
        uint8 Obj09P1YH6Bit : 6;
        uint8 Obj09P1XL2Bit : 2;
        uint8 Obj09P2XH5Bit : 5;
        uint8 Obj09P1YL3Bit : 3;
        uint8 Obj09P2YH4Bit : 4;
        uint8 Obj09P2XL4Bit : 4;
        uint8 Obj09TypeH3Bit : 3;
        uint8 Obj09P2YL5Bit : 5;
        uint8 Obj10P1XH6Bit : 6;
        uint8 Obj09TypeL2Bit : 2;
        uint8 Obj10P1YH5Bit : 5;
        uint8 Obj10P1XL3Bit : 3;
        uint8 Obj10P2XH4Bit : 4;
        uint8 Obj10P1YL4Bit : 4;
        uint8 Obj10P2YH3Bit : 3;
        uint8 Obj10P2XL5Bit : 5;
        uint8 Obj10TypeH2Bit : 2;
        uint8 Obj10P2YL6Bit : 6;
        uint8 Obj11P1XH5Bit : 5;
        uint8 Obj10TypeL3Bit : 3;
        uint8 Obj11P1YH4Bit : 4;
        uint8 Obj11P1XL4Bit : 4;
        uint8 Obj11P2XH3Bit : 3;
        uint8 Obj11P1YL5Bit : 5;
        uint8 Obj11P2YH2Bit : 2;
        uint8 Obj11P2XL6Bit : 6;
        uint8 Obj11TypeH1Bit : 1;
        uint8 Obj11P2YL7Bit : 7;
        uint8 : 4;
        uint8 Obj11TypeL4Bit : 4;
        uint8 : 8;
        uint8 : 8;
    }bit;
}CAN_0x388_100_Type;

typedef union
{
    uint8 byte[64];
    struct
    {
        uint8 SigGroup_0x2D5_ChkSm8Bit : 8;
        uint8 SigGroup_0x2D5_RlngCtr4Bit : 4;
        uint8 : 1;
        uint8 PointCloud0HeightProb3Bit : 3;
        uint8 PointCloud0P1XH8Bit : 8;
        uint8 PointCloud0P1YH4Bit : 4;
        uint8 PointCloud0Height2Bit : 2;
        uint8 PointCloud0P1XL2Bit : 2;
        uint8 PointCloud0ProbH2Bit : 2;
        uint8 PointCloud0P1YL6Bit : 6;
        uint8 PointCloud1P1XH4Bit : 4;
        uint8 PointCloud0Type3Bit : 3;
        uint8 PointCloud0ProbL1Bit : 1;
        uint8 PointCloud1HeightProbH2Bit : 2;
        uint8 PointCloud1P1XL6Bit : 6;
        uint8 PointCloud1P1YH5Bit : 5;
        uint8 PointCloud1Height2Bit : 2;
        uint8 PointCloud1HeightProbL1Bit : 1;
        uint8 PointCloud1Prob3Bit : 3;
        uint8 PointCloud1P1YL5Bit : 5;
        uint8 PointCloud2P1XH5Bit : 5;
        uint8 PointCloud1Type3Bit : 3;
        uint8 PointCloud2HeightProb3Bit : 3;
        uint8 PointCloud2P1XL5Bit : 5;
        uint8 PointCloud2P1YH6Bit : 6;
        uint8 PointCloud2Height2Bit : 2;
        uint8 PointCloud2TypeH1Bit : 1;
        uint8 PointCloud2Prob3Bit : 3;
        uint8 PointCloud2P1YL4Bit : 4;
        uint8 PointCloud3P1XH6Bit : 6;
        uint8 PointCloud2TypeL2Bit : 2;
        uint8 PointCloud3HeightH1Bit : 1;
        uint8 PointCloud3HeightProb3Bit : 3;
        uint8 PointCloud3P1XL4Bit : 4;
        uint8 PointCloud3P1YH7Bit : 7;
        uint8 PointCloud3HeightL1Bit : 1;
        uint8 PointCloud3TypeH2Bit : 2;
        uint8 PointCloud3Prob3Bit : 3;
        uint8 PointCloud3P1YL3Bit : 3;
        uint8 PointCloud4P1XH7Bit : 7;
        uint8 PointCloud3TypeL1Bit : 1;
        uint8 PointCloud4Height2Bit : 2;
        uint8 PointCloud4HeightProb3Bit : 3;
        uint8 PointCloud4P1XL3Bit : 3;
        uint8 PointCloud4P1YH8Bit : 8;
        uint8 PointCloud4Type3Bit : 3;
        uint8 PointCloud4Prob3Bit : 3;
        uint8 PointCloud4P1YL2Bit : 2;
        uint8 PointCloud5P1XH8Bit : 8;
        uint8 PointCloud5P1YH1Bit : 1;
        uint8 PointCloud5Height2Bit : 2;
        uint8 PointCloud5HeightProb3Bit : 3;
        uint8 PointCloud5P1XL2Bit : 2;
        uint8 PointCloud5P1YM8Bit : 8;
        uint8 PointCloud6P1XH1Bit : 1;
        uint8 PointCloud5Type3Bit : 3;
        uint8 PointCloud5Prob3Bit : 3;
        uint8 PointCloud5P1YL1Bit : 1;
        uint8 PointCloud6P1XM8Bit : 8;
        uint8 PointCloud6P1YH2Bit : 2;
        uint8 PointCloud6Height2Bit : 2;
        uint8 PointCloud6HeightProb3Bit : 3;
        uint8 PointCloud6P1XL1Bit : 1;
        uint8 PointCloud6P1YL8Bit : 8;
        uint8 PointCloud7P1XH2Bit : 2;
        uint8 PointCloud6Type3Bit : 3;
        uint8 PointCloud6Prob3Bit : 3;
        uint8 PointCloud7P1XL8Bit : 8;
        uint8 PointCloud7P1YH3Bit : 3;
        uint8 PointCloud7Height2Bit : 2;
        uint8 PointCloud7HeightProb3Bit : 3;
        uint8 PointCloud7ProbH1Bit : 1;
        uint8 PointCloud7P1YL7Bit : 7;
        uint8 PointCloud8P1XH3Bit : 3;
        uint8 PointCloud7Type3Bit : 3;
        uint8 PointCloud7ProbL2Bit : 2;
        uint8 PointCloud8HeightProbH1Bit : 1;
        uint8 PointCloud8P1XL7Bit : 7;
        uint8 PointCloud8P1YH4Bit : 4;
        uint8 PointCloud8Height2Bit : 2;
        uint8 PointCloud8HeightProbL2Bit : 2;
        uint8 PointCloud8ProbH2Bit : 2;
        uint8 PointCloud8P1YL6Bit : 6;
        uint8 PointCloud9P1XH4Bit : 4;
        uint8 PointCloud8Type3Bit : 3;
        uint8 PointCloud8ProbL1Bit : 1;
        uint8 PointCloud9HeightProbH2Bit : 2;
        uint8 PointCloud9P1XL6Bit : 6;
        uint8 PointCloud9P1YH5Bit : 5;
        uint8 PointCloud9Height2Bit : 2;
        uint8 PointCloud9HeightProbL1Bit : 1;
        uint8 PointCloud9Prob3Bit : 3;
        uint8 PointCloud9P1YL5Bit : 5;
        uint8 PointCloud10P1XH5Bit : 5;
        uint8 PointCloud9Type3Bit : 3;
        uint8 PointCloud10HeightProb3Bit : 3;
        uint8 PointCloud10P1XL5Bit : 5;
        uint8 PointCloud10P1YH6Bit : 6;
        uint8 PointCloud10Height2Bit : 2;
        uint8 PointCloud10TypeH1Bit : 1;
        uint8 PointCloud10Prob3Bit : 3;
        uint8 PointCloud10P1YL4Bit : 4;
        uint8 PointCloud11P1XH6Bit : 6;
        uint8 PointCloud10TypeL2Bit : 2;
        uint8 PointCloud11HeightH1Bit : 1;
        uint8 PointCloud11HeightProb3Bit : 3;
        uint8 PointCloud11P1XL4Bit : 4;
        uint8 PointCloud11P1YH7Bit : 7;
        uint8 PointCloud11HeightL1Bit : 1;
        uint8 PointCloud11TypeH2Bit : 2;
        uint8 PointCloud11Prob3Bit : 3;
        uint8 PointCloud11P1YL3Bit : 3;
        uint8 : 4;
        uint8 VolAbnormaFlg: 3;
        uint8 PointCloud11TypeL1Bit : 1;
        uint8 RingTime1_2 : 4;
        uint8 SnsIdx1 : 4;
        uint8 RingTime1_1 : 8;
        uint8 ImpedanceIdx1 : 7;
        uint8 : 1;
        uint8 RingFre1_2 : 8;
        uint8 RingFre1_1 : 8;
        uint8 RingTime2_2 : 4;
        uint8 SnsIdx2 : 4;
        uint8 RingTime2_1 : 8;
        uint8 ImpedanceIdx2 : 7;
        uint8 : 1;
        uint8 RingFre2_2 : 8;
        uint8 RingFre2_1 : 8;
        uint8 RingTime3_2 : 4;
        uint8 SnsIdx3 : 4;
        uint8 RingTime3_1 : 8;
        uint8 ImpedanceIdx3 : 7;
        uint8 : 1;
        uint8 RingFre3_2 : 8;
        uint8 RingFre3_1 : 8;
    }bit;
}CAN_0x2D5_20_Type;

typedef union
{
    uint8 byte[8];
    struct
    {
        uint8 SigGroup_122_ChkSm: 8;
        
        uint8 SigGroup_122_RlngCtr: 4;
        uint8 : 2;
        uint8 ACUYawRateSnsrSta: 2;
        
        uint8 ACUYawRateH8bit: 8;
        
        uint8 ACUYawRateL8bit: 8;
        
        uint8 : 8;
        
        uint8 : 8;
        
        uint8 : 8;
        
        uint8 : 8;
    }bit;
}CAN_0x236_20_Type;

typedef union
{
    uint8 byte[8];
    struct
    {
        uint8 SigGroup_110_ChkSm: 8;
        
        uint8 SigGroup_110_RlngCtr: 4;
        uint8 : 2;
        uint8 ACULatSnsrState: 2;

        uint8 ACULatAccH8bit: 8;
        
        uint8 ACULatAccL8bit: 8;
        
        uint8 ACULongdAccH8bit: 8;
        
        uint8 ACULongdAccL8bit: 8;
        
        uint8 : 6;
        uint8 ACULongdSnsrState: 2;
        
        uint8 : 8;
    }bit;
}CAN_0x237_20_Type;

typedef union
{
    uint8 byte[8];
    struct
    {

        uint8 ADAS_CMD_100ms_ChkSm: 8;
        
        uint8 ADAS_CMD_100ms_RlngCtr: 4;
        uint8 ADAS_PSL_enable: 1;
        uint8 ADAS_PAS_enable: 1;
        uint8 : 2;
        
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
    }bit;
}CAN_0x395_100_Type;

typedef union
{
    uint8 byte[8];
    struct
    {
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 3;
        uint8 FSD1_RearLoadModeSts: 2;
        uint8 : 3;
    }bit;
}CAN_0x384_100_Type;

typedef union
{
    uint8 byte[8];
    struct
    {
        uint8 ADAS_STS1_20ms_ChkSm: 8;
        uint8 ADAS_STS1_20ms_RlngCtr: 4;
        uint8 : 4;
        uint8 ADAS_slot_ID_Selected: 8;
        uint8 : 5;
        uint8 ADAS_APAStatus: 3;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
    }bit;
}CAN_0x2D4_20_Type;

typedef union
{
    uint8 byte[8];
    struct
    {
        uint8 SYNCTime_SYNC_RF_Type: 8;
        
        uint8 SYNCTime_SYNC_RF_CRC: 8;
        
        uint8 SYNCTime_SYNC_RF_SequenceCnt: 4;
        uint8 SYNCTime_SYNC_RF_TimeDomain: 4;
        
        uint8 SYNCTime_SYNC_RF_OVS: 2;      
        uint8 SYNCTime_SYNC_RF_SGW: 1;
        uint8 SYNCTime_SYNC_RF_Reserved: 5;
       
        uint8 SYNCTime_SYNC_RF_SyncTimeHHH8bit: 8;
        
        uint8 SYNCTime_SYNC_RF_SyncTimeHHL8bit: 8;
        uint8 SYNCTime_SYNC_RF_SyncTimeHLL8bit: 8;
        uint8 SYNCTime_SYNC_RF_SyncTimeLLL8bit: 8;
    }bit;
}CAN_0x5A4_500_Type;

typedef union
{
    uint8 byte[8];
    struct
    {
        uint8 : 8;
        uint8 : 8;
        uint8 : 1;
        uint8 FSD2_RearLoadModeSts: 2;
        uint8 : 5;
        
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
    }bit;
}CAN_0x504_100_Type;

typedef union
{
    uint8 byte[32];
    struct
    {
        uint8 SigGroup_0x176_ChkSm: 8;
        uint8 SigGroup_0x176_RlngCtr: 4;
        uint8 : 4;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;

        uint8 EPS_StrgAngValid: 1;
        uint8 : 7;
        
        uint8 EPS_StrgAngH8bit: 8;
        uint8 EPS_StrgAngL8bit: 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
    }bit;
}CAN_0x176_10_Type;

typedef union
{
    uint8 byte[32];
    struct
    {
        uint8 SigGroup_61_ChkSm: 8;
        
        uint8 SigGroup_61_RlngCtr: 4;
        uint8 : 4;
        
        uint8 FLWhlSpdPlssCterH8bit: 8;
        uint8 FLWhlSpdPlssCterL8bit: 8;
        
        uint8 FRWhlSpdPlssCterH8bit: 8;
        uint8 FRWhlSpdPlssCterL8bit: 8;
        
        uint8 RLWhlSpdPlssCterH8bit: 8;
        uint8 RLWhlSpdPlssCterL8bit: 8;
        
        uint8 RRWhlSpdPlssCterH8bit: 8;
        uint8 RRWhlSpdPlssCterL8bit: 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        
        uint8 RRWhlRotDircn: 2;
        uint8 RLWhlRotDircn: 2;
        uint8 FRWhlRotDircn: 2;
        uint8 FLWhlRotDircn: 2;
        
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
    }bit;
}CAN_0x86_10_Type;

typedef union
{
    uint8 byte[8];
    struct
    {
        uint8 VSigGroup_206_ChkSm: 8;
        uint8 SigGroup_206_RlngCtr: 4;
        uint8 : 4;
        
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;

        uint8 VehSpdH8bit: 8;
        
        uint8 : 2;
        uint8 VehSpdValid: 1;
        uint8 VehSpdL5bit: 5;
    }bit;
}CAN_0x96_10_Type;

typedef union
{
    uint8 byte[32];
    struct
    {
        uint8 SigGroup_229_ChkSm: 8;
        uint8 SigGroup_229_RlngCtr: 4;
        uint8 : 4;
        uint8 FLWhlSpdH8bit: 8;
        
        uint8 : 2;
        uint8 FLWhlSpdValid: 1;
        uint8 FLWhlSpd5bit: 5;
        
        uint8 FRWhlSpdH8bit: 8;
        
        uint8 : 2;
        uint8 FRWhlSpdValid: 1;
        uint8 FRWhlSpd5bit: 5;

        uint8 RLWhlSpdH8bit: 8;
        
        uint8 : 2;
        uint8 RLWhlSpdValid: 1;
        uint8 RLWhlSpd5bit: 5;
     
        uint8 RRWhlSpdH8bit: 8;
        
        uint8 : 2;
        uint8 RRWhlSpdValid: 1;
        uint8 RRWhlSpd5bit: 5;


        uint32 : 32;

        uint32 : 32;

        uint32 : 32;

        uint32 : 32;

        uint32 : 32;

        uint8 : 8;
        uint8 : 8;
    }bit;
}CAN_0xD6_10_Type;

typedef union
{
    uint8 byte[8];
    struct
    {
        uint8 SigGroup_0x209_ChkSm: 8;
        
        uint8 SigGroup_0x209_RlngCtr: 4;
        uint8 : 1;
        uint8 LowVolPwrMd: 2;
        uint8 LowVolPwrMdFlag: 1;
        uint8 : 8;
        uint8 : 8;

        uint8 : 4;
        uint8 LowVolPwrMdFlag2 : 2;
        uint8 : 2;

        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
    }bit;
}CAN_0x209_20_Type;

typedef union
{
    uint8 byte[8];
    struct
    {
        uint8 SigGroup_112_ChkSm: 8;
        uint8 SigGroup_112_RlngCtr: 4;
        uint8 : 4;
        uint8 TotalOdometerH8bit: 8;
        uint8 TotalOdometerM8bit: 8;
        uint8 TotalOdometerL8bit: 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
    }bit;
}CAN_0x39D_100_Type;

typedef union
{
    uint8 byte[8];
    struct
    {
        uint8 EastOrWestTimeZone: 2;
        uint8 UTCTimeValidFlag: 2;
        uint8 TimeZoneNum: 4;
        
        uint8 UTCTimeHHHHH8BIT: 8;
        uint8 UTCTimeHHHHL8BIT: 8;
        uint8 UTCTimeHHHLL8BIT: 8;
        uint8 UTCTimeHHLLL8BIT: 8;
        uint8 UTCTimeHLLLL8BIT: 8;
        uint8 UTCTimeLLLLL8BIT: 8;
        uint8 : 8;
    }bit;
}CAN_0x9E_10_Type;
typedef union
{
    uint8 byte[8];
    struct
    {
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        
        uint8 DspOtsdTempH8BIT: 8;
        
        uint8 : 4;
        uint8 OtsdTempV: 1;
        uint8 DspOtsdTempL3BIT: 3;
        
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
    }bit;
}CAN_0x397_100_Type;

typedef union
{
    uint8 byte[8];
    struct
    {
        uint8 SigGroup_58_ChkSm: 8;
        uint8 SigGroup_58_RlngCtr: 4;
        uint8 : 4;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        
        uint8 ActuGearShiftPos: 8;
    }bit;
}CAN_0xC2_10_Type;
/************TX*******************/
typedef union
{
    uint8 byte[8];
    struct
    {
        uint8 PAS_DTC1_Num_H8BIT: 8;
        
        uint8 PAS_DTC1_State: 1;
        uint8 : 5;
        uint8 PAS_DTC1_Num_L2BIT: 2;

        uint8 PAS_DTC2_Num_H8BIT: 8;
        
        uint8 PAS_DTC2_State: 1;
        uint8 : 5;
        uint8 PAS_DTC2_Num_L2BIT: 2;
        
        uint8 PAS_DTC3_Num_H8BIT: 8;
        
        uint8 PAS_DTC3_State: 1;
        uint8 : 5;
        uint8 PAS_DTC3_Num_L2BIT: 2;


        uint8 PAS_DTC4_Num_H8BIT: 8;
        
        uint8 PAS_DTC4_State: 1;
        uint8 : 5;
        uint8 PAS_DTC4_Num_L2BIT: 2;
    }bit;
}CAN_0x665_1000_Type;

extern CAN_0x665_1000_Type LstrTX_APS_0x665_1000_MSG; 
typedef union
{
    uint8 byte[32];
    struct
    {
        uint8 SigGroup_125_ChkSm: 8;
        uint8 SigGroup_125_RlngCtr: 4;
        uint8 : 1;
        uint8 MapObj00HeightProb: 3;
        
        uint8 MapObj00P1XH2BIT: 2;
        uint8 : 4;
        uint8 MapObj00Height: 2;
        
        uint8 MapObj00P1XL8BIT: 8;
        
        uint8 MapObj00P1YH2BIT: 2;
        uint8 MapObj00Type: 3;
        uint8 MapObj00Prob: 3;
        
        uint8 MapObj00P1YL8BIT: 8;
        
        uint8 MapObj00P2XH8BIT: 8;
        
        uint8 MapObj00P2YH2BIT: 2;
        uint8 MapObj01Prob: 3;
        uint8 : 1;
        uint8 MapObj00P2XL2BIT: 2;
        
        uint8 MapObj00P2YL8BIT: 8;
        
        uint8 MapObj01P1XH2BIT: 2;
        uint8 : 1;
        uint8 MapObj01HeightProb: 3;
        uint8 MapObj01Height: 2;
        
        uint8 MapObj01P1XL8BIT: 8;
        
        uint8 MapObj01P1YH8BIT: 8;
        
        uint8 MapObj01P2XH2BIT: 2;
        uint8 MapObj01Type: 3;
        uint8 : 1;
        uint8 MapObj01P1YL2BIT: 2;
        
        uint8 MapObj01P2XL8BIT: 8; 
        
        uint8 MapObj01P2YH8BIT: 8;
        
        uint8 MapObj02HeightProb: 3;
        uint8 MapObj02Height: 2;
        uint8 : 1;
        uint8 MapObj01P2YL2BIT: 2;



        uint8 MapObj02P1XH8BIT: 8;
        
        uint8 MapObj02P1YH2BIT: 2;
        uint8 MapObj02Prob: 3;
        uint8 : 1;
        uint8 MapObj02P1XL2BIT: 2;
        uint8 MapObj02P1YL8BIT: 8;


        uint8 MapObj02P2XH8BIT: 8;
        
        uint8 MapObj02P2YH2BIT: 2;
        uint8 MapObj02Type: 3;
        uint8 : 1;
        uint8 MapObj02P2XL2BIT: 2;
        uint8 MapObj02P2YL8BIT: 8;

        uint8 MapObj03P1XH2BIT: 2;
        uint8 : 1;
        uint8 MapObj03HeightProb: 3;
        uint8 MapObj03Height: 2;
        uint8 MapObj03P1XL8BIT: 8;


        uint8 MapObj03P1YH8BIT: 8;
        
        uint8 MapObj03P2XH2BIT: 2;
        uint8 MapObj03Prob: 3;
        uint8 : 1;
        uint8 MapObj03P1YL2BIT: 2;
        uint8 MapObj03P2XL8BIT: 8;
        
        uint8 MapObj03P2YH8BIT: 8;
        uint8 : 3;
        uint8 MapObj03Type: 3;
        uint8 MapObj03P2YL2BIT: 2;
        
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
    }bit;
}CAN_0x2F4_20_Type;

typedef union
{
    uint8 byte[32];
    struct
    {
        uint8 SigGroup_126_ChkSm: 8;
        
        uint8 SigGroup_126_RlngCtr: 4;
        uint8 : 1;
        uint8 MapObj04HeightProb: 3;
        
        uint8 MapObj04P1XH2BIT: 2;
        uint8 : 4;
        uint8 MapObj04Height: 2;
        
        uint8 MapObj04P1XL8BIT: 8;
        
        uint8 MapObj04P1YH2BIT: 2;
        uint8 MapObj04Type: 3;
        uint8 MapObj04Prob: 3;
        
        uint8 MapObj04P1YL8BIT: 8;

        uint8 MapObj04P2XH8BIT: 8;
        
        uint8 MapObj04P2YH2BIT: 2;
        uint8 MapObj05Prob: 3;
        uint8 : 1;
        uint8 MapObj04P2XL2BIT: 2;
        
        uint8 MapObj04P2YL8BIT: 8;
        
        uint8 MapObj05P1XH2BIT: 2;
        uint8 : 1;
        uint8 MapObj05HeightProb: 3;
        uint8 MapObj05Height: 2;
        
        uint8 MapObj05P1XL8BIT: 8;
        
        uint8 MapObj05P1YH8BIT: 8;
        
        uint8 MapObj05P2XH2BIT: 2;
        uint8 MapObj05Type: 3;
        uint8 : 1;
        uint8 MapObj05P1YL2BIT: 2;
        
        uint8 MapObj05P2XL8BIT: 8; 
        
        uint8 MapObj05P2YH8BIT: 8;
        
        uint8 MapObj06HeightProb: 3;
        uint8 MapObj06Height: 2;
        uint8 : 1;
        uint8 MapObj05P2YL2BIT: 2;



        uint8 MapObj06P1XH8BIT: 8;
        
        uint8 MapObj06P1YH2BIT: 2;
        uint8 MapObj06Prob: 3;
        uint8 : 1;
        uint8 MapObj06P1XL2BIT: 2;
        uint8 MapObj06P1YL8BIT: 8;


        uint8 MapObj06P2XH8BIT: 8;
        
        uint8 MapObj06P2YH2BIT: 2;
        uint8 MapObj06Type: 3;
        uint8 : 1;
        uint8 MapObj06P2XL2BIT: 2;
        uint8 MapObj06P2YL8BIT: 8;

        uint8 MapObj07P1XH2BIT: 2;
        uint8 : 1;
        uint8 MapObj07HeightProb: 3;
        uint8 MapObj07Height: 2;
        uint8 MapObj07P1XL8BIT: 8;


        uint8 MapObj07P1YH8BIT: 8;
        
        uint8 MapObj07P2XH2BIT: 2;
        uint8 MapObj07Prob: 3;
        uint8 : 1;
        uint8 MapObj07P1YL2BIT: 2;
        uint8 MapObj07P2XL8BIT: 8;
        
        uint8 MapObj07P2YH8BIT: 8;
        uint8 : 3;
        uint8 MapObj07Type: 3;
        uint8 MapObj07P2YL2BIT: 2;
        
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
    }bit;
}CAN_0x2E4_20_Type;

typedef union
{
    uint8 byte[32];
    struct
    {
        uint8 PAS_Info4_100ms_ChkSm: 8;
        uint8 PAS_Info4_100ms_RlngCtr: 4;
        uint8 : 2;
        uint8 PAS_TrailerHitchDetected: 2;
        
        uint8 FL1_DistanceH8bit: 8;
        
        uint8 FL2_DistanceH7bit: 7;
        uint8 FL1_DistanceL1bit: 1;
        
        uint8 FR1_DistanceH6BIT: 6;
        uint8 FL2_DistanceL2bit: 2;
        
        uint8 FR2_DistanceH5BIT: 5;
        uint8 FR1_DistanceL3BIT: 3;
        
        uint8 FML1_DistanceH4BIT: 4;
        uint8 FR2_DistanceL4BIT: 4;
        
        uint8 FML2_DistanceH3BIT: 3;
        uint8 FML1_DistanceL5BIT: 5;
        
        uint8 FMR1_DistanceH2BIT: 2;
        uint8 FML2_DistanceL6BIT: 6;
        
        uint8 FMR2_DistanceH1BIT: 1;
        uint8 FMR1_DistanceL7BIT: 7;
        
        uint8 FMR2_DistanceL8BIT: 8;
        
        uint8 RL1_DistanceH8BIT: 8;

        uint8 RL2_DistanceH7BIT: 7;
        uint8 RL1_DistanceL1BIT: 1;
        
        uint8 RR1_DistanceH6BIT: 6;
        uint8 RL2_DistanceL2BIT: 2;
        
        uint8 RR2_DistanceH5BIT: 5;
        uint8 RR1_DistanceL3BIT: 3;
        
        uint8 LSF1_DistanceH4BIT: 4;
        uint8 RR2_DistanceL4BIT: 4;
        
        uint8 LSF2_DistanceH3BIT: 3;
        uint8 LSF1_DistanceL5BIT: 5;
        
        uint8 LSR1_DistanceH2BIT: 2;
        uint8 LSF2_DistanceL6BIT: 6;
        
        uint8 LSR2_DistanceH1BIT: 1;
        uint8 LSR1_DistanceL7BIT: 7;
        
        uint8 LSR2_DistanceL8BIT: 8;
        
        uint8 LSFM1_DistanceH8BIT: 8;

        uint8 LSFM2_DistanceH7BIT: 7;
        uint8 LSFM1_DistanceL1BIT: 1;
        
        uint8 LSRM1_DistanceH6BIT: 6;
        uint8 LSFM2_DistanceL2BIT: 2;
        
        uint8 LSRM2_DistanceH5BIT: 5;
        uint8 LSRM1_DistanceL3BIT: 3;
        
        uint8 RML1_DistanceH4BIT: 4;
        uint8 LSRM2_DistanceL4BIT: 4;
        
        uint8 RML2_DistanceH3BIT: 3;
        uint8 RML1_DistanceL5BIT: 5;
        
        uint8 RMR1_DistanceH2BIT: 2;
        uint8 RML2_DistanceL6BIT: 6;
        
        uint8 RMR2_DistanceH1BIT: 1;
        uint8 RMR1_DistanceL7BIT: 7;
        
        uint8 RMR2_DistanceL8BIT: 8;

        uint8 RSF1_DistanceH8BIT: 8;
        
        uint8 RSF2_DistanceH7BIT: 7;
        uint8 RSF1_DistanceL1BIT: 1;

        uint8 PSL_Status: 3;
        uint8 PAS_Status: 3;
        uint8 RSF2_DistanceL2BIT: 2;
    }bit;
}CAN_0x394_100_Type;

typedef union
{
    uint8 byte[32];
    struct
    {
        uint8 SigGroup_0x244_ChkSm: 8;
        uint8 SigGroup_0x244_RlngCtr: 4;
        uint8 : 1;
        uint8 MapObj08HeightProb: 3;
        
        uint8 MapObj08P1XH2BIT: 2;
        uint8 : 4;
        uint8 MapObj08Height: 2;
        uint8 MapObj08P1XL8BIT: 8;
        
        uint8 MapObj08P1YH2BIT: 2;
        uint8 MapObj08Type: 3;
        uint8 MapObj08Prob: 3;
        uint8 MapObj08P1YL8BIT: 8;
        
        uint8 MapObj08P2XH8BIT: 8;
        uint8 MapObj08P2YH2BIT: 2;
        uint8 MapObj09Prob: 3;
        uint8 : 1;
        uint8 MapObj08P2XL2BIT: 2;
        
        uint8 MapObj08P2YL8BIT: 8;
        uint8 MapObj09P1XH2BIT: 2;
        uint8 : 1;
        uint8 MapObj09HeightProb: 3;
        uint8 MapObj09Height: 2;
        uint8 MapObj09P1XL8BIT: 8;
        
        uint8 MapObj09P1YH8BIT: 8;
        uint8 MapObj09P2XH2BIT: 2;
        uint8 MapObj09Type: 3;
        uint8 : 1;
        uint8 MapObj09P1YL2BIT: 2;

        uint8 MapObj09P2XL8BIT: 8;
        
        uint8 MapObj09P2YH8BIT: 8;
        
        uint8 MapObj10HeightProb: 3;
        uint8 MapObj10Height: 2;
        uint8 : 1;
        uint8 MapObj09P2YL2BIT: 2;
        
        uint8 MapObj10P1XH8BIT: 8;
        uint8 MapObj10P1YH2BIT: 2;
        uint8 MapObj10Prob: 3;
        uint8 : 1;
        uint8 MapObj10P1XL2BIT: 2;

        uint8 MapObj10P1YL8BIT: 8;
        uint8 MapObj10P2XH8BIT: 8;
        uint8 MapObj10P2YH2BIT: 2;
        uint8 MapObj10Type: 3;
        uint8 : 1;
        uint8 MapObj10P2XL2BIT: 2;
        
        uint8 MapObj10P2YL8BIT: 8;
        uint8 MapObj11P1XH2BIT: 2;
        uint8 : 1;
        uint8 MapObj11HeightProb: 3;
        uint8 MapObj11HeightL2BIT: 2;
        
        uint8 MapObj11P1XL8BIT: 8;
        uint8 MapObj11P1YH8BIT: 8;
        uint8 MapObj11P2XH2BIT: 2;
        uint8 MapObj11Prob: 3;
        uint8 : 1;
        uint8 MapObj11P1YL2BIT: 2;

        uint8 MapObj11P2XL8BIT: 8;
        uint8 MapObj11P2YH8BIT: 8;
        uint8 : 3;
        uint8 MapObj11Type: 3;
        uint8 MapObj11P2YL2BIT: 2;
        
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;

    }bit;
}CAN_0x244_20_Type;

typedef union
{
    uint8 byte[32];
    struct
    {
        uint8 SigGroup_4_ChkSm: 8;
        uint8 SigGroup_4_RlngCtr: 4;
        uint8 : 4;
        uint8 ODO_KappaH8BIT: 8;
        uint8 ODO_KappaL8BIT: 8;
        uint8 ODO_sHAH8BIT: 8;
        uint8 ODO_sHAL8BIT: 8;
        uint8 : 8;
        uint8 : 8;

        uint8 ODO_XH8BIT: 8;
        uint8 ODO_XL8BIT: 8;
        uint8 : 8;
        uint8 : 8;

        uint8 ODO_YawAngleH8BIT: 8;
        uint8 ODO_YawAngleL8BIT: 8;
        uint8 : 8;
        uint8 : 8;
        uint8 ODO_TimeStampH8BIT: 8;
        uint8 ODO_TimeStampL8BIT: 8;
        uint8 ODO_YH8BIT: 8;
        uint8 ODO_YL8BIT: 8;
        uint8 ODO_CalTimeStampHHH8BIT: 8;
        uint8 ODO_CalTimeStampHH8BIT: 8;
        uint8 ODO_CalTimeStampH8BIT: 8;
        uint8 : 5;
        uint8 ODO_CalTimeStampL3BIT: 3;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
    }bit;
}CAN_0x85_10_Type;

typedef union
{
    uint8 byte[32];
    struct
    {
        uint8 SigGroup_0x128_ChkSm: 8;
        uint8 SigGroup_0x128_RlngCtr: 4;
        uint8 : 1;
        uint8 MapObj12HeightProb: 3;
        
        uint8 MapObj12P1XH2BIT: 2;
        uint8 : 4;
        uint8 MapObj12Height: 2;
        uint8 MapObj12P1XL8BIT: 8;
        
        uint8 MapObj12P1YH2BIT: 2;
        uint8 MapObj12Type: 3;
        uint8 MapObj12Prob: 3;
        uint8 MapObj12P1YL8BIT: 8;
        
        uint8 MapObj12P2XH8BIT: 8;
        uint8 MapObj12P2YH2BIT: 2;
        uint8 MapObj13Prob: 3;
        uint8 : 1;
        uint8 MapObj13P2XL2BIT: 2;
        
        uint8 MapObj12P2YL8BIT: 8;
        uint8 MapObj13P1XH2BIT: 2;
        uint8 : 1;
        uint8 MapObj13HeightProb: 3;
        uint8 MapObj13Height: 2;
        uint8 MapObj13P1XL8BIT: 8;
        
        uint8 MapObj13P1YH8BIT: 8;
        uint8 MapObj13P2XH2BIT: 2;
        uint8 MapObj13Type: 3;
        uint8 : 1;
        uint8 MapObj13P1YL2BIT: 2;

        uint8 MapObj13P2XL8BIT: 8;
        
        uint8 MapObj13P2YH8BIT: 8;
        
        uint8 MapObj14HeightProb: 3;
        uint8 MapObj14Height: 2;
        uint8 : 1;
        uint8 MapObj13P2YL2BIT: 2;
        
        uint8 MapObj14P1XH8BIT: 8;
        uint8 MapObj14P1YH2BIT: 2;
        uint8 MapObj14Prob: 3;
        uint8 : 1;
        uint8 MapObj14P1XL2BIT: 2;

        uint8 MapObj14P1YL8BIT: 8;
        uint8 MapObj14P2XH8BIT: 8;
        uint8 MapObj14P2YH2BIT: 2;
        uint8 MapObj14Type: 3;
        uint8 : 1;
        uint8 MapObj14P2XL2BIT: 2;
        
        uint8 MapObj14P2YL8BIT: 8;
        uint8 MapObj15P1XH2BIT: 2;
        uint8 : 1;
        uint8 MapObj15HeightProb: 3;
        uint8 MapObj15HeightL2BIT: 2;
        
        uint8 MapObj15P1XL8BIT: 8;
        uint8 MapObj15P1YH8BIT: 8;
        uint8 MapObj15P2XH2BIT: 2;
        uint8 MapObj15Prob: 3;
        uint8 : 1;
        uint8 MapObj15P1YL2BIT: 2;

        uint8 MapObj15P2XL8BIT: 8;
        uint8 MapObj15P2YH8BIT: 8;
        uint8 : 3;
        uint8 MapObj15Type: 3;
        uint8 MapObj15P2YL2BIT: 2;
        
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;

    }bit;
}CAN_0x245_20_Type;

typedef union
{
    uint8 byte[32];
    struct
    {
        uint8 SigGroup_0x128_ChkSm: 8;
        uint8 SigGroup_0x128_RlngCtr: 4;
        uint8 : 1;
        uint8 MapObj16HeightProb: 3;
        
        uint8 MapObj16P1XH2BIT: 2;
        uint8 : 4;
        uint8 MapObj16Height: 2;
        uint8 MapObj16P1XL8BIT: 8;
        
        uint8 MapObj16P1YH2BIT: 2;
        uint8 MapObj16Type: 3;
        uint8 MapObj16Prob: 3;
        uint8 MapObj16P1YL8BIT: 8;
        
        uint8 MapObj16P2XH8BIT: 8;
        uint8 MapObj16P2YH2BIT: 2;
        uint8 MapObj17Prob: 3;
        uint8 : 1;
        uint8 MapObj16P2XL2BIT: 2;
        
        uint8 MapObj16P2YL8BIT: 8;
        uint8 MapObj17P1XH2BIT: 2;
        uint8 : 1;
        uint8 MapObj17HeightProb: 3;
        uint8 MapObj17Height: 2;
        uint8 MapObj17P1XL8BIT: 8;
        
        uint8 MapObj17P1YH8BIT: 8;
        uint8 MapObj17P2XH2BIT: 2;
        uint8 MapObj17Type: 3;
        uint8 : 1;
        uint8 MapObj17P1YL2BIT: 2;

        uint8 MapObj17P2XL8BIT: 8;
        
        uint8 MapObj17P2YH8BIT: 8;
        
        uint8 MapObj18HeightProb: 3;
        uint8 MapObj18Height: 2;
        uint8 : 1;
        uint8 MapObj17P2YL2BIT: 2;
        
        uint8 MapObj18P1XH8BIT: 8;
        uint8 MapObj18P1YH2BIT: 2;
        uint8 MapObj18Prob: 3;
        uint8 : 1;
        uint8 MapObj18P1XL2BIT: 2;

        uint8 MapObj18P1YL8BIT: 8;
        uint8 MapObj18P2XH8BIT: 8;
        uint8 MapObj18P2YH2BIT: 2;
        uint8 MapObj18Type: 3;
        uint8 : 1;
        uint8 MapObj18P2XL2BIT: 2;
        
        uint8 MapObj18P2YL8BIT: 8;
        uint8 MapObj19P1XH2BIT: 2;
        uint8 : 1;
        uint8 MapObj19HeightProb: 3;
        uint8 MapObj19HeightL2BIT: 2;
        
        uint8 MapObj19P1XL8BIT: 8;
        uint8 MapObj19P1YH8BIT: 8;
        uint8 MapObj19P2XH2BIT: 2;
        uint8 MapObj19Prob: 3;
        uint8 : 1;
        uint8 MapObj19P1YL2BIT: 2;

        uint8 MapObj19P2XL8BIT: 8;
        uint8 MapObj19P2YH8BIT: 8;
        uint8 : 3;
        uint8 MapObj19Type: 3;
        uint8 MapObj19P2YL2BIT: 2;
        
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;

    }bit;
}CAN_0x254_20_Type;

typedef union
{
    uint8 byte[32];
    struct
    {
        uint8 SigGroup_0x387_ChkSm: 8;
        uint8 SigGroup_0x387_RlngCtr: 4;
        uint8 : 3;
        uint8 DTCWindow_Enable : 1;

        uint8 RSR1_DistanceH8bit: 8;
        
        uint8 RSR2_DistanceH7bit: 7;
        uint8 RSR1_DistanceL1bit: 1;
        
        uint8 RSRM1_DistanceH6BIT: 6;
        uint8 RSR2_DistanceL2bit: 2;
        
        uint8 RSRM2_DistanceH5BIT: 5;
        uint8 RSRM1_DistanceL3BIT: 3;
        
        uint8 RSFM1_DistanceH4BIT: 4;
        uint8 RSRM2_DistanceL4BIT: 4;
        
        uint8 RSFM2_DistanceH3BIT: 3;
        uint8 RSFM1_DistanceL5BIT: 5;
        
        uint8 PAS_Localtime_afterSyncHHH2BIT: 2;
        uint8 RSFM2_DistanceL6BIT: 6;
        
        uint8 PAS_Localtime_afterSyncHH8BIT: 8;
        uint8 PAS_Localtime_afterSyncH8BIT: 8;
        
        uint8 InterFail_Event1_Num : 6;        
        uint8 PAS_Localtime_afterSyncL2BIT: 2;

        
        uint8 InterFail_Event3_NumH2BIT: 2;
        uint8 InterFail_Event2_Num : 6;

        uint8 InterFail_Event4_NumH4BIT : 4;
        uint8 InterFail_Event3_NumL4BIT: 4;

        uint8 InterFail_Event5_Num : 6;
        uint8 InterFail_Event4_NumL2BIT: 2;

        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
    }bit;
}CAN_0x387_100_Type;

typedef union
{
    uint8 byte[32];
    struct
    {
        uint8 USS_DE_1H8BIT: 8;
        
        uint8 USS_DE_2H6BIT: 6;
        uint8 USS_DE_1L2BIT: 2;
        
        uint8 USS_DE_3H4BIT: 4;
        uint8 USS_DE_2L4BIT: 4;
        
        uint8 USS_DE_4H2BIT: 2;
        uint8 USS_DE_3L6BIT: 6;
        
        uint8 USS_DE_4L8BIT: 8;
        
        uint8 USS_DE_5H8BIT: 8;
        
        uint8 USS_DE_6H6BIT: 6;
        uint8 USS_DE_5L2BIT: 2;
        
        uint8 USS_DE_7H4BIT: 4;
        uint8 USS_DE_6L4BIT: 4;
        
        uint8 USS_DE_8H2BIT: 2;
        uint8 USS_DE_7L6BIT: 6;
        
        uint8 USS_DE_8L8BIT: 8;
        
        uint8 USS_DE_9H8BIT: 8;

        uint8 USS_DE_10H6BIT: 6;
        uint8 USS_DE_9L2BIT: 2;
        
        uint8 USS_DE_11H4BIT: 4;
        uint8 USS_DE_10L4BIT: 4;
        
        uint8 USS_DE_12H2BIT: 2;
        uint8 USS_DE_11L6BIT: 6;
        
        uint8 USS_DE_12L8BIT: 8;
        
        
        uint8 USS_DE_1_2H8BIT: 8;
        
        uint8 USS_DE_2_3H6BIT: 6;
        uint8 USS_DE_1_2L2BIT: 2;
        
        uint8 USS_DE_3_4H4BIT: 4;
        uint8 USS_DE_2_3L4BIT: 4;
        
        uint8 USS_DE_4_5H2BIT: 2;
        uint8 USS_DE_3_4L6BIT: 6;
        
        uint8 USS_DE_4_5L8BIT: 8;
        
        uint8 USS_DE_5_6H8BIT: 8;
        
        uint8 : 6;
        uint8 USS_DE_5_6L2BIT: 2;
        

        uint8 USS_DE_7_8H4BIT: 4;
        uint8 : 4;
        
        uint8 USS_DE_8_9H2BIT: 2;
        uint8 USS_DE_7_8L6BIT: 6;
        
        uint8 USS_DE_8_9L8BIT: 8;
        
        uint8 USS_DE_9_10H8BIT: 8;

        uint8 USS_DE_10_11H6BIT: 6;
        uint8 USS_DE_9_10L2BIT: 2;
        
        uint8 USS_DE_11_12H4BIT: 4;
        uint8 USS_DE_10_11L4BIT: 4;
        
        uint8 : 2;
        uint8 USS_DE_11_12L6BIT: 6;

        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
    }bit;
}CAN_0x238_20_Type;

typedef union
{
    uint8 byte[32];
    struct
    {
        uint8 PAS_PSLInfo1_20ms_ChkSm: 8;
        uint8 PAS_PSLInfo1_20ms_RlngCtr: 4;
        uint8 : 4;
        uint8 ApaSlot1ID: 8;
        
        uint8 ApaSlot1DepthRef: 3;
        uint8 ApaSlot1Type: 3;
        uint8 ApaSlot1Status: 2;
        
        uint8 ApaSlot1Obj1XH8bit: 8;
        uint8 ApaSlot1Obj1YH3BIT: 3;
        uint8 ApaSlot1Obj1XL5bit: 5;
        
        uint8 ApaSlot1Obj1YM8BIT: 8;
        uint8 ApaSlot1Obj1AlphaH4BIT: 4;
        uint8 ApaSlot1Obj1Type: 2;
        uint8 ApaSlot1Obj1YL2BIT: 2;
        
        uint8 ApaSlot1Obj2XH5BIT: 5;
        uint8 ApaSlot1Obj1AlphaL3BIT: 3;
        uint8 ApaSlot1Obj2XL8BIT: 8;
        
        uint8 ApaSlot1Obj2YH8BIT: 8;
        
        uint8 ApaSlot1Obj2AlphaH1BIT: 1;
        uint8 ApaSlot1Obj2Type: 2;
        uint8 ApaSlot1Obj2YL5BIT: 5;
        
        uint8 ApaSlot1LengthH2BIT: 2;
        uint8 ApaSlot1Obj2AlphaL6BIT: 6;
        
        uint8 ApaSlot1LengthM8BIT: 8;
        
        uint8 ApaSlot1DepthH7BIT: 7;
        uint8 ApaSlot1LengthL1BIT: 1;

        
        uint8 ApaSlot2IDH5BIT: 5;
        uint8 ApaSlot1DepthL3BIT: 3;
        
        uint8 ApaSlot2Type: 3;
        uint8 ApaSlot2Status: 2;
        uint8 ApaSlot2IDL3BIT: 3;
        
        uint8 ApaSlot2Obj1XH5BIT: 5;
        uint8 ApaSlot2DepthRef: 3;
        uint8 ApaSlot2Obj1XL8BIT: 8;

        uint8 ApaSlot2Obj1YH8BIT: 8;
        
        uint8 ApaSlot2Obj1AlphaH1BIT: 1;
        uint8 ApaSlot2Obj1Type: 2;
        uint8 ApaSlot2Obj1YL5BIT: 5;

        uint8 ApaSlot2Obj2XH2BIT: 2;
        uint8 ApaSlot2Obj1AlphaL6BIT: 6;
        
        uint8 ApaSlot2Obj2XM8BIT: 8;
        uint8 ApaSlot2Obj2YH5BIT: 5;
        uint8 ApaSlot2Obj2XL3BIT: 3;

        uint8 ApaSlot2Obj2YL8BIT: 8;
        
        uint8 ApaSlot2Obj2AlphaH6BIT: 6;
        uint8 ApaSlot2Obj2Type: 2;
        
        uint8 ApaSlot2LengthH7BIT: 7;
        uint8 ApaSlot2Obj2AlphaL1BIT: 1; 

        uint8 ApaSlot2DepthH4BIT: 4;
        uint8 ApaSlot2LengthL4BIT: 4;
        
        uint8 ApaSlot3IDH2BIT: 2;
        uint8 ApaSlot2DepthL6BIT: 6; 
        
        uint8 ApaSlot3Status: 2;
        uint8 ApaSlot3IDL6BIT: 6; 
        uint8 ApaSlot3Obj1XH5BIT: 5; 
        uint8 ApaSlot3Type: 3;
        uint8 ApaSlot3Obj1XL8BIT: 8; 
   
    }bit;
}CAN_0x284_20_Type;

typedef union
{
    uint8 byte[32];
    struct
    {
        uint8 PAS_PSLInfo2_20ms_ChkSm: 8;
        uint8 PAS_PSLInfo2_20ms_RlngCtr: 4;
        uint8 : 4;
        uint8 ApaSlot5ID: 8;
        
        uint8 ApaSlot5DepthRef: 3;
        uint8 ApaSlot5Type: 3;
        uint8 ApaSlot5Status: 2;
        
        uint8 ApaSlot5Obj1XH8bit: 8;
        uint8 ApaSlot5Obj1YH3BIT: 3;
        uint8 ApaSlot5Obj1XL5bit: 5;
        
        uint8 ApaSlot5Obj1YM8BIT: 8;
        uint8 ApaSlot5Obj1AlphaH4BIT: 4;
        uint8 ApaSlot5Obj1Type: 2;
        uint8 ApaSlot5Obj1YL2BIT: 2;
        
        uint8 ApaSlot5Obj2XH5BIT: 5;
        uint8 ApaSlot5Obj1AlphaL3BIT: 3;
        uint8 ApaSlot5Obj2XL8BIT: 8;
        
        uint8 ApaSlot5Obj2YH8BIT: 8;
        
        uint8 ApaSlot5Obj2AlphaH1BIT: 1;
        uint8 ApaSlot5Obj2Type: 2;
        uint8 ApaSlot5Obj2YL5BIT: 5;
        
        uint8 ApaSlot5LengthH2BIT: 2;
        uint8 ApaSlot5Obj2AlphaL6BIT: 6;
        
        uint8 ApaSlot5LengthM8BIT: 8;
        
        uint8 ApaSlot5DepthH7BIT: 7;
        uint8 ApaSlot5LengthL1BIT: 1;

        
        uint8 ApaSlot6IDH5BIT: 5;
        uint8 ApaSlot5DepthL3BIT: 3;
        
        uint8 ApaSlot6Type: 3;
        uint8 ApaSlot6Status: 2;
        uint8 ApaSlot6IDL3BIT: 3;
        
        uint8 ApaSlot6Obj1XH5BIT: 5;
        uint8 ApaSlot6DepthRef: 3;
        uint8 ApaSlot6Obj1XL8BIT: 8;

        uint8 ApaSlot6Obj1YH8BIT: 8;
        
        uint8 ApaSlot6Obj1AlphaH1BIT: 1;
        uint8 ApaSlot6Obj1Type: 2;
        uint8 ApaSlot6Obj1YL5BIT: 5;

        uint8 ApaSlot6Obj2XH2BIT: 2;
        uint8 ApaSlot6Obj1AlphaL6BIT: 6;
        
        uint8 ApaSlot6Obj2XM8BIT: 8;
        uint8 ApaSlot6Obj2YH5BIT: 5;
        uint8 ApaSlot6Obj2XL3BIT: 3;

        uint8 ApaSlot6Obj2YL8BIT: 8;
        
        uint8 ApaSlot6Obj2AlphaH6BIT: 6;
        uint8 ApaSlot6Obj2Type: 2;
        
        uint8 ApaSlot6LengthH7BIT: 7;
        uint8 ApaSlot6Obj2AlphaL1BIT: 1; 

        uint8 ApaSlot6DepthH4BIT: 4;
        uint8 ApaSlot6LengthL4BIT: 4;
        
        uint8 ApaSlot7IDH2BIT: 2;
        uint8 ApaSlot6DepthL6BIT: 6; 
        
        uint8 ApaSlot7Status: 2;
        uint8 ApaSlot7IDL6BIT: 6; 
        uint8 : 8; 
        uint8 : 8; 
   
    }bit;
}CAN_0x285_20_Type;

typedef union
{
    uint8 byte[32];
    struct
    {
        uint8 SigGroup_0x286_ChkSm: 8;
        
        uint8 SigGroup_0x286_RlngCtr: 4;
        uint8 ApaSlot3Obj2Type: 2;
        uint8 ApaSlot3Obj1Type: 2;
        uint8 ApaSlot3Obj1Y_High5bit: 5;
        uint8 ApaSlot3DepthRef: 3;

        uint8 ApaSlot3Obj1Y_Low8bit: 8;
        uint8 ApaSlot3Obj2X_HH1bit: 1;
        uint8 ApaSlot3Obj1Alpha: 7;

        uint8 ApaSlot3Obj2X_Mid8bit: 8;
        
        uint8 ApaSlot3Obj2Y_High4bit: 4;
        uint8 ApaSlot3Obj2X_Low4bit: 4;

        uint8 ApaSlot3Obj2Y_Mid8BIT: 8;
        uint8 ApaSlot3Obj2Alpha: 7;
        uint8 ApaSlot3Obj2Y_Low1bit: 1;

        uint8 ApaSlot3Length_H8bit: 8;
        uint8 ApaSlot3Depth_H5bit: 5;
        uint8 ApaSlot3Length_L3bit: 3;
        
        uint8 ApaSlot4ID_H3bit: 3;
        uint8 ApaSlot3Depth_L5bit: 5;
        
        uint8 ApaSlot4DepthRef: 3;
        uint8 ApaSlot4ID_L5bit: 5;
        
        uint8 ApaSlot4Obj1X_H3bit: 3;
        uint8 ApaSlot4Status: 2;
        uint8 ApaSlot4Type: 3;


        uint8 ApaSlot4Obj1X_Mid8bit: 8;
        uint8 ApaSlot4Obj1Y_H6bit: 6;
        uint8 ApaSlot4Obj1X_Low2bit: 2;
        uint8 ApaSlot4Obj1Alpha_H1bit: 1;
        uint8 ApaSlot4Obj1Y_L7bit: 7;
        uint8 ApaSlot4Obj1Type: 2;
        uint8 ApaSlot4Obj1Alpha_L6bit: 6;

        uint8 ApaSlot4Obj2X_H8bit: 8;
        uint8 ApaSlot4Obj2Y_H3bit: 3;
        uint8 ApaSlot4Obj2X_L5bit: 5;

        uint8 ApaSlot4Obj2Y_Mid8bit: 8;
        
        uint8 ApaSlot4Length_H4bit: 4;        

        uint8 ApaSlot4Obj2Type: 2;
        uint8 ApaSlot4Obj2Y_L2bit: 2;
        
        uint8 ApaSlot4Obj2Alpha_H1bit: 1;
        uint8 ApaSlot4LengthL7bit: 7;
        
        uint8 ApaSlot4Depth_H2bit: 2;
        uint8 ApaSlot4Obj2Alpha_L6bit: 6;
        uint8 ApaSlot4Depth_L8bit: 8;
        
        uint8 PAS_MapObj18_Timestamp_HHH8BIT: 8;
        uint8 PAS_MapObj18_Timestamp_HHL8BIT: 8;
        uint8 PAS_MapObj18_Timestamp_HLL8BIT: 8;
        uint8 PAS_MapObj19_Timestamp_HHH5BIT: 5;
        uint8 PAS_MapObj18_Timestamp_LLL3BIT: 3;
        

        uint8 PAS_MapObj19_Timestamp_HHL8BIT: 8;
        uint8 PAS_MapObj19_Timestamp_HLL8BIT: 8;
        uint8 : 2;
        uint8 PAS_MapObj19_Timestamp_LLL6BIT: 6;

    }bit;
}CAN_0x286_20_Type;

typedef union
{
    uint8 byte[32];
    struct
    {
        uint8 SigGroup_0x287_ChkSm: 8;
        
        uint8 SigGroup_0x287_RlngCtr: 4;
        uint8 : 1;
        uint8 ApaSlot7DepthRef: 3;

        uint8 ApaSlot7Obj1X_H8BIT: 8;
        
        uint8 ApaSlot7Type: 3;
        uint8 ApaSlot7Obj1X_L5BIT: 5;
        
        uint8 ApaSlot7Obj1Y_H8BIT: 8;
        
        uint8 ApaSlot7Obj1Alpha_H3BIT: 3; 
        uint8 ApaSlot7Obj1Y_L5BIT: 5;
        
        uint8 ApaSlot7Obj2X_H2BIT: 2;        
        uint8 ApaSlot7Obj1Type: 2;
        uint8 ApaSlot7Obj1Alpha_L4BIT: 4;
        
        uint8 ApaSlot7Obj2X_M8BIT: 8;
        
        uint8 ApaSlot7Obj2Y_H5BIT: 5;
        uint8 ApaSlot7Obj2X_L3BIT: 3;
        
        uint8 ApaSlot7Obj2Y_L8BIT: 8;
        
        uint8 ApaSlot7Obj2Type_H1BIT: 1;       
        uint8 ApaSlot7Obj2Alpha: 7;
        
        uint8 ApaSlot7Length_H7BIT: 7;
        uint8 ApaSlot7Obj2Type_L1BIT: 1;
        
        uint8 ApaSlot7Depth_H4BIT: 4;
        uint8 ApaSlot7Length_L4BIT: 4;
        
        uint8 ApaSlot8ID_H2BIT: 2;
        uint8 ApaSlot7Depth_L6BIT: 6;
        
        uint8 ApaSlot8DepthRef_H2BIT: 2;
        uint8 ApaSlot8ID_L6BIT: 6;
        
        uint8 ApaSlot8Obj1X_H2BIT: 2;
        uint8 ApaSlot8Status: 2;
        uint8 ApaSlot8Type: 3;
        uint8 ApaSlot8DepthRef_L1BIT: 1;
        
        uint8 ApaSlot8Obj1X_Mid8BIT: 8;
        
        uint8 ApaSlot8Obj1Y_H5BIT: 5;
        uint8 ApaSlot8Obj1X_L3BIT: 3;
        
        uint8 ApaSlot8Obj1Y_L8BIT: 8;
        
        uint8 ApaSlot8Obj1Type_H1BIT: 1;
        uint8 ApaSlot8Obj1Alpha: 7;
        
        uint8 ApaSlot8Obj2X_H7BIT: 7;
        uint8 ApaSlot8Obj1Type_L1BIT: 1;
        
        uint8 ApaSlot8Obj2Y_H2BIT: 2;
        uint8 ApaSlot8Obj2X_L6BIT: 6;
        
        uint8 ApaSlot8Obj2Y_MID8BIT: 8;
        
        uint8 ApaSlot8Length_H3BIT: 3;
        uint8 ApaSlot8Obj2Type: 2;
        uint8 ApaSlot8Obj2Y_L3BIT: 3;
        
        uint8 ApaSlot8Length_L8BIT: 8;
        
        uint8 ApaSlot8Depth_H1BIT: 1;
        uint8 ApaSlot8Obj2Alpha: 7;
        
        uint8 ApaSlot8Depth_MID8BIT: 8;
        
        uint8 : 7;
        uint8 ApaSlot8Depth_L1BIT: 1;
        uint16 :16;
        uint16 :16;

    }bit;
}CAN_0x287_20_Type;

typedef union
{
    uint8 byte[32];
    struct
    {
        uint8 SigGroup_0x2A4_ChkSm: 8;
        uint8 SigGroup_0x2A4_RlngCtr: 4;
        uint8 : 4;

        
        uint8 PAS_PSL_Slot1_Timestamp_HHH8BIT: 8;
        uint8 PAS_PSL_Slot1_Timestamp_HHL8BIT: 8;
        uint8 PAS_PSL_Slot1_Timestamp_HLL8BIT: 8;
        
        uint8 PAS_PSL_Slot2_Timestamp_HHH5BIT: 5;
        uint8 PAS_PSL_Slot1_Timestamp_LLL3BIT: 3;

        uint8 PAS_PSL_Slot2_Timestamp_HHL8BIT: 8;
        uint8 PAS_PSL_Slot2_Timestamp_HLL8BIT: 8;
        uint8 PAS_PSL_Slot3_Timestamp_HHHH2BIT: 2;
        uint8 PAS_PSL_Slot2_Timestamp_LLL6BIT: 6;

        uint8 PAS_PSL_Slot3_Timestamp_HHHL8BIT: 8;
        uint8 PAS_PSL_Slot3_Timestamp_HHLL8BIT: 8;
        uint8 PAS_PSL_Slot3_Timestamp_HLLL8BIT: 8;
        uint8 PAS_PSL_Slot4_Timestamp__HHH7BIT: 7;
        uint8 PAS_PSL_Slot3_Timestamp_LLLL1BIT: 1;

        uint8 PAS_PSL_Slot4_Timestamp_HHL8BIT: 8;
        uint8 PAS_PSL_Slot4_Timestamp_HLL8BIT: 8;
        uint8 PAS_PSL_Slot5_Timestamp__HHH4BIT: 4;
        uint8 PAS_PSL_Slot4_Timestamp_LLL4BIT: 4;

        uint8 PAS_PSL_Slot5_Timestamp_HHL8BIT: 8;
        uint8 PAS_PSL_Slot5_Timestamp_HLL8BIT: 8;
        uint8 PAS_PSL_Slot6_Timestamp_HHHH1BIT: 1;

        uint8 PAS_PSL_Slot5_Timestamp_LLL7BIT: 7;

        
        uint8 PAS_PSL_Slot6_Timestamp_HHHL8BIT: 8;
        uint8 PAS_PSL_Slot6_Timestamp_HHLL8BIT: 8;
        uint8 PAS_PSL_Slot6_Timestamp_HLLL8BIT: 8;
        uint8 PAS_PSL_Slot7_Timestamp_HHH6BIT: 6;

        uint8 PAS_PSL_Slot6_Timestamp_LLLL2BIT: 2;

        
        uint8 PAS_PSL_Slot7_Timestamp_HHL8BIT: 8;
        uint8 PAS_PSL_Slot7_Timestamp_HLL8BIT: 8;
        uint8 PAS_PSL_Slot8_Timestamp_HHH3BIT: 3;
        uint8 PAS_PSL_Slot7_Timestamp_LLL5BIT: 5;
        uint8 PAS_PSL_Slot8_Timestamp_HHL8BIT: 8;
        uint8 PAS_PSL_Slot8_Timestamp_HLL8BIT: 8;
        uint8 PAS_PSL_Slot8_Timestamp_LLL8BIT: 8;
        uint16 :16;
        uint8 : 8;
    }bit;
}CAN_0x2A4_20_Type;

typedef union
{
    uint8 byte[32];
    struct
    {
        uint8 SigGroup_0x2A5_ChkSm: 8;
        uint8 SigGroup_0x2A5_RlngCtr: 4;
        uint8 : 4;

        
        uint8 PAS_MapObj00_Timestamp_HHH8BIT: 8;
        uint8 PAS_MapObj00_Timestamp_HHL8BIT: 8;
        uint8 PAS_MapObj00_Timestamp_HLL8BIT: 8;
        
        uint8 PAS_MapObj01_Timestamp_HHH5BIT: 5;
        uint8 PAS_MapObj00_Timestamp_LLL3BIT: 3;

        uint8 PAS_MapObj01_Timestamp_HHL8BIT: 8;
        uint8 PAS_MapObj01_Timestamp_HLL8BIT: 8;
        uint8 PAS_MapObj02_Timestamp_HHHH2BIT: 2;
        uint8 PAS_MapObj01_Timestamp_LLL6BIT: 6;

        uint8 PAS_MapObj02_Timestamp_HHHL8BIT: 8;
        uint8 PAS_MapObj02_Timestamp_HHLL8BIT: 8;
        uint8 PAS_MapObj02_Timestamp_HLLL8BIT: 8;
        uint8 PAS_MapObj03_Timestamp__HHH7BIT: 7;
        uint8 PAS_MapObj02_Timestamp_LLLL1BIT: 1;

        uint8 PAS_MapObj03_Timestamp_HHL8BIT: 8;
        uint8 PAS_MapObj03_Timestamp_HLL8BIT: 8;
        uint8 PAS_MapObj04_Timestamp__HHH4BIT: 4;
        uint8 PAS_MapObj03_Timestamp_LLL4BIT: 4;

        uint8 PAS_MapObj04_Timestamp_HHL8BIT: 8;
        uint8 PAS_MapObj04_Timestamp_HLL8BIT: 8;
        uint8 PAS_MapObj05_Timestamp_HHHH1BIT: 1;

        uint8 PAS_MapObj04_Timestamp_LLL7BIT: 7;

        
        uint8 PAS_MapObj05_Timestamp_HHHL8BIT: 8;
        uint8 PAS_MapObj05_Timestamp_HHLL8BIT: 8;
        uint8 PAS_MapObj05_Timestamp_HLLL8BIT: 8;
        uint8 PAS_MapObj06_Timestamp_HHH6BIT: 6;

        uint8 PAS_MapObj05_Timestamp_LLLL2BIT: 2;

        
        uint8 PAS_MapObj06_Timestamp_HHL8BIT: 8;
        uint8 PAS_MapObj06_Timestamp_HLL8BIT: 8;
        uint8 PAS_MapObj07_Timestamp_HHH3BIT: 3;
        uint8 PAS_MapObj06_Timestamp_LLL5BIT: 5;
        uint8 PAS_MapObj07_Timestamp_HHL8BIT: 8;
        uint8 PAS_MapObj07_Timestamp_HLL8BIT: 8;
        uint8 PAS_MapObj07_Timestamp_LLL8BIT: 8;
        uint16 :16;
        uint8 : 8;
    }bit;
}CAN_0x2A5_20_Type;

typedef union
{
    uint8 byte[32];
    struct
    {
        uint8 SigGroup_0x2B4_ChkSm: 8;
        uint8 SigGroup_0x2B4_RlngCtr: 4;
        uint8 : 4;

        
        uint8 PAS_MapObj08_Timestamp_HHH8BIT: 8;
        uint8 PAS_MapObj08_Timestamp_HHL8BIT: 8;
        uint8 PAS_MapObj08_Timestamp_HLL8BIT: 8;
        
        uint8 PAS_MapObj09_Timestamp_HHH5BIT: 5;
        uint8 PAS_MapObj08_Timestamp_LLL3BIT: 3;

        uint8 PAS_MapObj09_Timestamp_HHL8BIT: 8;
        uint8 PAS_MapObj09_Timestamp_HLL8BIT: 8;
        uint8 PAS_MapObj10_Timestamp_HHHH2BIT: 2;
        uint8 PAS_MapObj09_Timestamp_LLL6BIT: 6;

        uint8 PAS_MapObj10_Timestamp_HHHL8BIT: 8;
        uint8 PAS_MapObj10_Timestamp_HHLL8BIT: 8;
        uint8 PAS_MapObj10_Timestamp_HLLL8BIT: 8;
        uint8 PAS_MapObj11_Timestamp__HHH7BIT: 7;
        uint8 PAS_MapObj10_Timestamp_LLLL1BIT: 1;

        uint8 PAS_MapObj11_Timestamp_HHL8BIT: 8;
        uint8 PAS_MapObj11_Timestamp_HLL8BIT: 8;
        uint8 PAS_MapObj12_Timestamp__HHH4BIT: 4;
        uint8 PAS_MapObj11_Timestamp_LLL4BIT: 4;

        uint8 PAS_MapObj12_Timestamp_HHL8BIT: 8;
        uint8 PAS_MapObj12_Timestamp_HLL8BIT: 8;
        uint8 PAS_MapObj13_Timestamp_HHHH1BIT: 1;

        uint8 PAS_MapObj12_Timestamp_LLL7BIT: 7;

        
        uint8 PAS_MapObj13_Timestamp_HHHL8BIT: 8;
        uint8 PAS_MapObj13_Timestamp_HHLL8BIT: 8;
        uint8 PAS_MapObj13_Timestamp_HLLL8BIT: 8;
        uint8 PAS_MapObj14_Timestamp_HHH6BIT: 6;

        uint8 PAS_MapObj13_Timestamp_LLLL2BIT: 2;

        
        uint8 PAS_MapObj14_Timestamp_HHL8BIT: 8;
        uint8 PAS_MapObj14_Timestamp_HLL8BIT: 8;
        uint8 PAS_MapObj15_Timestamp_HHH3BIT: 3;
        uint8 PAS_MapObj14_Timestamp_LLL5BIT: 5;
        uint8 PAS_MapObj15_Timestamp_HHL8BIT: 8;
        uint8 PAS_MapObj15_Timestamp_HLL8BIT: 8;
        uint8 PAS_MapObj15_Timestamp_LLL8BIT: 8;
        uint16 :16;
        uint8 : 8;
    }bit;
}CAN_0x2B4_20_Type;

typedef union
{
    uint8 byte[32];
    struct
    {
        uint8 SigGroup_0x2B5_ChkSm: 8;
        uint8 SigGroup_0x2B5_RlngCtr: 4;
        uint8 : 4;

        uint8 PAS_MapObj16_Timestamp_HHH8BIT: 8;
        uint8 PAS_MapObj16_Timestamp_HHL8BIT: 8;
        uint8 PAS_MapObj16_Timestamp_HLL8BIT: 8;
        
        uint8 PAS_MapObj17_Timestamp_HHH5BIT: 5;
        uint8 PAS_MapObj16_Timestamp_LLL3BIT: 3;

        uint8 PAS_MapObj17_Timestamp_HHL8BIT: 8;
        uint8 PAS_MapObj17_Timestamp_HLL8BIT: 8;
        uint8 MapObj0IndexH2BIT: 2;
        uint8 PAS_MapObj17_Timestamp_LLL6BIT: 6;

        uint8 MapObj1Index: 5;
        uint8 MapObj0IndexL3BIT: 3;
        
        uint8 MapObj3IndexH3BIT: 3;
        uint8 MapObj2Index: 5;
        
        uint8 MapObj5IndexH1BIT: 1;

        uint8 MapObj4Index: 5;
        uint8 MapObj3IndexL2BIT: 2;
        
        uint8 MapObj6IndexH4BIT: 4;
        uint8 MapObj5IndexL4BIT: 4;
        
        uint8 MapObj8IndexH2BIT: 2;
        uint8 MapObj7Index: 5;
        uint8 MapObj6IndexL1BIT: 1;
        
        uint8 MapObj9Index: 5;
        uint8 MapObj8IndexL3BIT: 3;
        
        uint8 MapObj11IndexH3BIT: 3;
        uint8 MapObj10Index: 5;
        
        uint8 MapObj13IndexH1BIT: 1;

        uint8 MapObj12Index: 5;
        uint8 MapObj11IndexL2BIT: 2;
        
        uint8 MapObj14IndexH4BIT: 4;
        uint8 MapObj13IndexL4BIT: 4;
        
        uint8 MapObj16IndexH2BIT: 2;
        uint8 MapObj15Index: 5;
        uint8 MapObj14IndexL1BIT: 1;
        
        uint8 MapObj17Index: 5;
        uint8 MapObj16IndexL3BIT: 3;
        uint8 MapObj19IndexH3BIT: 3;
        uint8 MapObj18Index: 5;
        uint8 : 6;
        uint8 MapObj19IndexL2BIT: 2;
        
        uint32 :32;
        uint32 :32;
        uint8 : 8;
        uint8 : 8;
    }bit;
}CAN_0x2B5_20_Type;


typedef union
{
    uint8 byte[8];
    struct
    {
        uint8 PAS_BTVerNum_H8BIT: 8;
        uint8 PAS_BTVerNum_L8BIT: 8;
        uint8 PAS_HWVerNum_H8BIT: 8;
        uint8 PAS_HWVerNum_L8BIT: 8;
        uint8 PAS_SWVerNum_H8BIT: 8;
        uint8 PAS_SWVerNum_L8BIT: 8;
        uint8 PAS_SWID_H8BIT: 8;
        uint8 PAS_SWID_L8BIT: 8;
    }bit;
}CAN_0x685_5000_Type;




/***********************************************************************************/
/***********************************************************************************/
/***********************************************************************************/
/***********************************************************************************/
/***APS开关请求状态类型枚举***/
typedef enum 
{
	Request_NoAction =0,
	Request_OFF,
	Request_ON,
	Reserved,
}eAPSSwtReq_VR;
/***HUTAPS泊车模式选择类型枚举***/
typedef enum 
{
	APS_NO_SELECT =0,
	APS_PARKING_IN,
	APS_PARKING_OUT,
	APS_RESERVED,
}eHUTAPSPrkgMod_Seln;
/***APS泊车类型枚举***/
typedef enum 
{
	No_Select =0,
	Parallel_Packing,
	Vertical_Packing,
	APS_Reserved,
}eAPSPRKGTYPSELN;	
/***发送机启停状态类型枚举***/
typedef enum 
{
	Non_StartStop_Mode = 0x0,	  
	EngineStandby,			  
	EngineStopped,
	StarterRestart,
	EngineRestart,
	EngineOperation,
	EngineAuto_Stopping,
	Invalid,
}eEngineStartStopSts;
/***刹车踏板状态***/
typedef enum 
{
    PEDAL_NotPressed = 0x0,     
    PEDAL_Pressed,            
    PEDAL_Reserved,
    PEDAL_Error,
}eBrkPedalSts;
/***发动机状态类型枚举***/
typedef enum 
{
	Engine_Stopped = 0x0,
	Engine_Cranking,
	Engine_Running,
	Engine_Stalling,
}eEngineSts;
/***实车档位类型枚举***/
typedef enum 
{
	TGS_LEVER_P = 0x0,	   /*P档*/
	TGS_LEVER_D = 0x5,			  
	TGS_LEVER_N,
	TGS_LEVER_R,
	TGS_LEVER_M,
}eTGS_Lever;
/***轮速状态信息枚举***/
enum eWheelInfo
{
    WHEEL_FL = 0x0,     /*轮子位置*/
    WHEEL_FR,
    WHEEL_RL,
    WHEEL_RR,
    WHEEL_NUM,
};
typedef enum 
{
    WHEEL_DIR_Invalid = 0x0,    /*轮速方向*/
    WHEEL_DIR_Forward,
    WHEEL_DIR_Backward,
    WHEEL_DIR_Stop,
}eWheelDir;
/***EPB状态信息枚举***/
typedef enum 
{
    EPB_ERROR_Undefined = 0x0,  /*EPB错误状态*/
    EPB_ERROR_NoError,
    EPB_ERROR_Error,
    EPB_ERROR_Diagnosis,
}eEPBErrorSts; 
typedef enum 
{
    EPB_STS_Released = 0x0,     /*EPB状态*/
    EPB_STS_Closed,
    EPB_STS_InProgress,
    EPB_STS_unknown,
}eEPBSts;
typedef enum 
{
    EPB_POS_Neutral = 0x0,      /*EPB位置*/   
    EPB_POS_Release,
    EPB_POS_Apply,
    EPB_POS_Reserved,
}eEPBPos;
/***EPS状态信息枚举***/
typedef enum 
{
    EPS_TemporaryInhibit = 0x0,
    EPS_AvailableForControl,
    EPS_Active,
    EPS_PermanentlyFailed,
}eEPSSts;
/***纵向控制器错误状态类型枚举***/	
typedef enum
{
	NO_ERROR = 0,
	VEH_BLOCKED,
	UNEXPECTED_GEAR_POSITION,
	UNEXPECTED_EPB_ACTION,
	LGT_RESERVED,
	UNEXPECTED_GEARINTERVENTION,
	ACTIVE_BRAKING_FAILURE,
	LGT_CTRLR_FAILR_INVALID,
}eLGT_CTRLR_FAILR;
/***纵向控制器状态类型枚举***/
typedef enum
{
	LGT_OFF = 0,
	LGT_STANDBY = 1,
	LGT_ACTIVE_AUTOMATIC_PARK =4,
	LGT_INVALID=7,
}eLGT_CTRLR_STS;
/***电源系统状态类型枚举***/
typedef enum
{
	APS_POWER_OFF = 0,
	APS_POWER_ACC,
	APS_POWER_ON,
	APS_POWER_CRANK,
}eSYS_POWER_MODE;
/***默认泊车设置***/
typedef enum
{
    DFT_ParkingIn = 0x0,
    DFT_ParkingOut,
}eDefaultPakSet;
/***泊车请求(语音)***/
typedef enum
{
    P_REQ_NoAction = 0x0,
    P_REQ_off,
    P_REQ_on,
    P_REQ_Reserved,
}eParkingReqVR;
/***泊车模式选择***/
typedef enum
{
    P_MODE_NoSelect = 0x0,
    P_MODE_ParkingIn,
    P_MODE_ParkingOut,
    P_MODE_Reserved,
}eParkingMode;  
/***泊车类型选择***/
typedef enum
{
    P_TYPE_NoSelect = 0x0,
    P_TYPE_ParallelParking,
    P_TYPE_VerticalParking,
    P_TYPE_Reserved,
}eParkingType;  
/***方向盘角度 速度 方向***/
typedef enum
{
    SIGN_LEFT = 0x0,        /*(Positive)*/
    SIGN_Right,             /*(Negative)*/
}eSWAANDSWSSign; 
/***安全带状态***/
typedef enum
{
    NoReminder = 0x0,       
    Reminder,           
}eSeatBelt; 

typedef enum
{
    /*SEND*/
    DataId_2F4 = 0x0,       
    DataId_2E4,
    DataId_394,
    DataId_085,
    DataId_245,
    DataId_254,
    DataId_387,
    DataId_284,
    DataId_285,
    DataId_286,
    DataId_287,
    DataId_2A4,
    DataId_2A5,
    DataId_2B4,
    DataId_2B5,

    /*Receive*/
    DataId_236,
    DataId_237,
    DataId_395,
    DataId_2D4,
    DataId_176,
    DataId_086,
    DataId_096,
    DataId_0D6,
    DataId_209,
    DataId_0C2, 
    DataId_5A4,
    DataIdNum
}eDataIdNumType; 

/*************************************************************************/
/*************************************************************************/
/*************************************************************************/
/*************************************************************************/
/*************************************************************************/
/*************************************************************************/
/*************************************************************************/
/*************************************************************************/

typedef union
{
    uint8 GcTimeset[7];
    struct
    {
        uint8 UTCTime0:8; 
        uint8 UTCTime1:8;              
        uint8 UTCTime2:8;                
       
        uint8 UTCTime3:8;           
        uint8 UTCTime4:8;                    
        uint8 UTCTime5:8;  
              
        uint8 TimeZone: 2;
        uint8 UTCTimeValidFlag: 2;
        uint8 TimeZoneNum: 4;          
    }Bits;
}HUT7SetTime;
/******车门信息******/
typedef union
{
    uint8 GcDoorstatus[1];
    struct
    {
        uint8 TrunkSts:1;         /*0 后备箱状态 */
        uint8 DrvDoorSts:1;       /*1 主驾驶室门状态*/ 
        uint8 LRDoorSts:1;        /*2 左后门状态*/
        uint8 PassengerDoorSts:1; /*3 副驾驶门状态*/
		uint8 RRDoorSts:1;	      /*4 右后门状态*/
        uint8 MirrorSts:1;        /*5 后视镜状态*/
        uint8 :2;                 /*6~7 unused*/
    }Bits;
}DoorSts;
/***人工干扰方向盘         EPS状态***/   
typedef struct
{
    eEPSSts EPS_AvailSts;
    uint8 EPS_InterferDetd;
}InterferSAS;
/***ESP和APS状态信息***/   
typedef struct
{   
    uint8 ESPFailSts;
    uint8 VDCActv;
    uint8 PTCActv;
    uint8 BTCActv; 
    float MasterCylBrkPress;
    
    uint8 VehStandstill;
    uint8 AEBIBActv;
    uint8 AEBBAActv;
    
    uint8 ESP_MEBBrkAvail;
    uint8 LgtCtrlrAvail;
    uint8 LgtCtrlrFailr;
    uint8 LgtCtrlrSts;
    uint16 FourWhlsBrkTrq;    
}ESPANDAPSSts;
/***轮速   方向   脉冲信息***/   
typedef struct
{
    float WheelSpd;
    eWheelDir WheelDirection;
    uint8 EdgesSum;   
}WheelInfo;
/***EPB状态***/   
typedef struct
{
    eEPBErrorSts EPBErrSts;
    eEPBSts EPBSts;
    eEPBPos EPB_SwtPosn;     
}EPBSts;
/***APS泊车请求***/   
typedef struct
{
    uint8  APSSwtReq;
    eParkingReqVR APSSwtReqVR;
    eParkingMode APSParkingMod;
    eParkingType APSParkingType;     
}APSParkingReqinf;
/***转向灯开关***/   
typedef struct
{
    uint8  LeftTurnSwtSts;
    uint8  RightTurnSwtSts;   
}TurnSwtSts;
/***方向盘信息***/   
typedef struct
{
    eSWAANDSWSSign  SteerWheelAngSign;
    float   SteerWheelAng;
    
    eSWAANDSWSSign  SteerWheelSpdSign;
    float   SteerWheelSpd;      
}SteerWheelInf;
/***车身加速度信息***/   
typedef struct
{
    float   LongitudinalAcc;      /*纵向加速度*/
    float   LateralAcc;           /*横向加速度*/
    float   VehicleYawRate;       /*偏航率*/
}VehAccel;

/* 枚举定义 */
typedef enum 
{

    NM_OFF_IGNINE_ON = 0u,
    NM_OFF_DUE_VC,              /* 变体码故障关闭网络监控 */
    NM_OFF_DUE_T50_ESS,
    NM_OFF_DUE_UV,              /* */
    NM_OFF_DUE_OV,
    NM_OFF_DUE_BUSOFF,          /* 未使用，Busoff状态发不出报文 */
    NM_OFF_DUE_IGNINE_OFF,
    NM_ACTIVE,

    TEST_DEFAULT_STATE = 0x0,
    CURRENT_STATE,
    HEALED_STATE,
}NM_Test_Sts;

typedef enum
{
    CHECK_CNT_DLC = 0u,
	CHECK_CNT_CRC,
    CHECK_CNT_ALC,
    CHECK_CNT_TOTAL,
    
}CanRxContent_CheckCntType;

/* 结构体定义 */
typedef struct
{
	uint8 cIgnitionState;
	NM_Test_Sts cVariantCodeState;
	NM_Test_Sts cVINState;					/* 0x01 为VIN故障状态 0x02为VIN治愈状态 */
	NM_Test_Sts cSignalContentFailureState;
	NM_Test_Sts cFeatureCodestate;
	NM_Test_Sts cMessageTimeOutState;
	NM_Test_Sts cNodeAbsentState;
	uint8 cUnderVoltageState;			/* 0x00 为默认 0x01  状态激活 */
	uint8 cOverVoltageState;			/* 0x00 为默认 0x01  状态激活 */
	uint8 cBatteryVoltage;
	uint8 cSoftwareVersion;
	NM_Test_Sts cNetworkManageActiveState;
	NM_Test_Sts cMessageContentFailureState;
	uint8 cVINCheckState;				    /* 0x00 未检查    0x01已检查*/

}NetworkTestMessage_DataType;


typedef struct
{   
    uint8 Signal_InvalidCnt;
    uint8 Signal_ValidCnt;
    uint8 Checksum_InvalidCnt;
    uint8 Checksum_ValidCnt;
    uint8 Counter_InvalidCnt; 
    uint8 Counter_ValidCnt;   
}NodeErrCntType;

/***********************************************************************/
/***********************************************************************************/
/***********************************************************************************/
/***********************************************************************************/
/******************接收的CAN信号内容集合********************************************/
typedef union 
{
    uint8 GcInputParaBuff[INPUT_PARAM_NUM];

    struct
    {
        float               GfEngSpd;                   /*发动机速度 物理值*/
        uint16               GwEngSpdVal;                /*发动机速度 信号值*/
        HUT7SetTime         GsSettingTime;        		/*时间设置信息 47*/
        WheelInfo           GsWheelInfo[WHEEL_NUM];     /*轮速   方向 脉冲 24*/
        uint16               GwMaxEngTrqNorm;            /*最大扭矩常量*/
        uint32              GdwVehTotDistanceVal;       /*里程*/
        uint16               GwVehSpdVal;                /*实车速度 信号值*/
    }Bytes;
}IL_InputParaDataStr;

#define CAN_SYNC_TYPE   (0X20U)
#define CAN_FUP_TYPE    (0X28U)

typedef enum
{
    CAN_SYSN_TO_GTM = 0,
    CAN_SYSN_TO_SUBDOMAIN,
    CAN_SYSN_DOMAIN_NUM,
}CAN_SYSN_SGW_Type;
    
typedef struct
{
    uint8   Gu8CAN_Ma_Type;              /* CAN同步报文类型 */
    uint32  Gu32Type_SYNC_RXInt_Time;    /* 接收到SYSN的中断时间*/
    uint8   TS_CAN_SYNC_TimeDomain;      /* 接收到SYSN的时间阈*/
    uint8   TS_CAN_SYNC_CRC;             /* 接收到SYSN的CRC*/
    uint8   TS_CAN_SYNC_SequenceCounter; /* 接收到SYSN的计数*/
    CAN_SYSN_SGW_Type   TS_CAN_SYNC_SGW;             /* 接收到SYSN的SGW状态*/
    uint32  TS_CAN_SYNC_SyncTimeSec;     /* 接收到SYSN的T0时间*/

    uint32  Gu32Type_FUP_RXInt_Time;     /* 接收到FUP的中断时间*/
    uint8   TS_CAN_FUP_CRC;              /* 接收到FUP的CRC*/
    uint8   TS_CAN_FUP_TimeDomain;       /* 接收到FUP的时间阈*/
    uint8   TS_CAN_FUP_SequenceCounter;  /* 接收到FUP的计数*/
    CAN_SYSN_SGW_Type   TS_CAN_FUP_SGW;              /* 接收到FUP的SGW状态*/
    uint8   TS_CAN_FUP_OVS;              /* 接收到FUP的时间差值（s）*/
    uint32  TS_CAN_FUP_SyncTimeNSec;     /* 接收到FUP的T0时间*/
}CANTimeSysn;



typedef struct RollingCountType
{
    uint8 Value :4;
    uint8 Res :4;
}RollingCount_ST;


/****************************************************************************** 
* External objects 
********************************************************************************/

extern IL_InputParaDataStr GsIntputParaStruct;
extern NetworkTestMessage_DataType GsNMTestData;	    /* 网络管理测试数据 报文ID6BD发出去的数据 */

extern uint8 GvCanMessageProcessFlag[RXANDTXFRAMEMAXNUM];  

extern NodeErrCntType GstrNodeErrCnt[RXANDTXFRAMEMAXNUM];

extern uint8 ADAS_SYNCTime_FailedCnt;  
extern uint8 ADAS_SYNCTime_PassCnt;

/******************************************************************************* 
* Global Functions 
********************************************************************************/ 

void IL_CanDataInd(uint8 LcFrameIdx);
void IL_MessageCombine_RX(void);
void CanIL_Init(void);
void IL_MessageCombine_TX(uint8 Index);
#endif /* MIDDLE_LAYER_CANCOM_CANIL_CAN_IL_V07_H_ */
