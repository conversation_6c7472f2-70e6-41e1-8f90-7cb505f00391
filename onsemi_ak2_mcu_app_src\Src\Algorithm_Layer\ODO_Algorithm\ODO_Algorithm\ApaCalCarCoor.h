/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * ApaCalCarCoor.c: 
 * Created on: 2020-12-07 19:09
 * Original designer: 22554
 ******************************************************************************/

#ifndef __APACALCARCOOR_H__
#define __APACALCARCOOR_H__

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
//#include "Config_McuResource.h"
#include "ApaCalCarCoor_Types.h"
#include "ApaCalCarCoor_Cfg.h"

/******************************************************************************
 * Compiler option used to distinguish inclusion done by the owner
 *****************************************************************************/
//#ifdef   SYS_USE_XXX
#if 1

/******************************************************************************
 * Inclusion of private header files
 *****************************************************************************/

#ifdef __cplusplus
extern "C"{
#endif

/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
extern void CPOS_Init(void);
extern void CPOS_Task(void);
extern uint8 CPOS_SetEnableCalFlg(void);
extern void CPOS_ClrEnableCalFlg(uint8 id);


#ifdef __cplusplus
}
#endif

#endif /* end of SYS_USE_XXX */

#endif /* end of __XXX_H__ */

