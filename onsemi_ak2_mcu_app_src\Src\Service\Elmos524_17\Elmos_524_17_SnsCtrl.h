/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * Elmos_524_17_SnsCtrl.h:
 * Created on: 2019-11-01 15:05
 * Original designer: Lvqv Lin
 ******************************************************************************/
#ifndef __ELMOS_524_17_SNSCTRL__
#define __ELMOS_524_17_SNSCTRL__

/* Includes ------------------------------------------------------------------*/
#include "AdvEchoDet.h"

#if 0


#define PRINTF_Elmos17SnsCtrl Lhprintf

#else


#define PRINTF_Elmos17SnsCtrl(...)
#endif




/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/
typedef enum
{

    GroupStatus_SNS_DM = 0,
    GroupStatus_SNSINIT,
    GroupStatus_READSNSGAIN,
    GroupStatus_SNS_DIAG,
    
    GroupStatus_SNSSelfTest,
    GroupStatus_SNSStandby,
    GroupStatus_SNSWakeUp,

    GroupStatus_START_MEAS,

    GroupStatus_READY_MEAS,
    GroupStatus_BUSY_MEAS,
    GroupStatus_END_MEAS,

	GroupStatus_SNS_TEMPCOMP,
	GroupStatus_SNS_REDMCHECK,
	GroupStatus_SNS_WaitPwrRec,
    GroupStatus_WriteSnsGain,
    GroupStatus_BUSY_RWSNSStatus,
	GroupStatus_IDLE,

    GroupStatus_Num,
    GroupStatus_NULL,

}SnsGroupStatus_en;

typedef enum
{
    SnsGroupMeas_IDLE,
    SnsGroupMeas_BUSY_MEAS,
    SnsGroupMeas_NotReady,
    SnsGroupMeas_NULL,

}SnsGroupMeasStatus_en;



typedef struct
{
    SnsGroupStatus_en GroupStatus;
    SnsGroupStatus_en NextGroupStatus;
    
    uint32 Time1msCnt;
	uint32 TempCompTime1msCnt;
    uint32 MeasTimeOut[DSIMasterNum][DSIChlNum];
    uint32 SnsGroupMeasTime[DSIMasterNum][DSIChlNum];
    SnsGroupMeasStatus_en SnsGroupMeasStatus[DSIMasterNum][DSIChlNum];
	uint8 MeasSeqFinish;
	uint8 StartMeasFlg;
}SnsCtrlGroup_Cfg;



/******************************************************************************/
/* \Description :                                                       */
/******************************************************************************/


/******************************************************************************/
/* \Description :                                                    */
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/





/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


extern void InitSnsCtrlGroup(void);
extern SnsGroupStatus_en GetSnsCtrlGroupStatus();

extern void Elmos17SnsCtrl_1msTASK();
extern void Elmos17SnsCtrl_NoPeriod_TASK();
extern void Elmos17SnsCtrl_UpdateTimeCnt();
extern const SnsMeasDistData_Str* Elmos17SnsCtrl_GetSnsMeasDistData(void);
extern uint8 Elmos17SnsCtrl_GetSeqFinishFlg(void);
extern void Elmos17SnsCtrl_ClearSeqFinishFlg(void);
extern void Elmos17SnsCtrl_SetStartMeasFlg(uint8 Flag);
extern uint16 Elmos17SnsCtrl_GetDMFlg(void);
extern uint16 Elmos17SnsCtrl_GetSnsParamCfgFlg(void);
extern void Elmos17SnsCtrl_SetDVWorkFlg(uint8 u8WorkMode);
extern uint8 Elmos17SnsCtrl_GetDVWorkFlg(void);
extern void GetWriteGainData(void);
extern uint8 Elmos17SnsCtrl_InterFail_Event5_Num(void);

/******************************************************************************
* 函数名称: TriggerSnsGroupStartMeas
* 
* 功能描述: 触发前后雷达启动发波
* 
* 输入参数: MEAS_Profile  探测profile
* 输入参数: R_SeqID       后雷达发波序列ID
* 输入参数: F_SeqID   前雷达发波序列ID
* 
* 输出参数: 无
* 
* 返 回 值: 0  OK  else 条件不满足或SPI 传输错误
* 
* 修改日期      版本号       修改人       修改内容
* 
*******************************************************************************/
extern uint8 TriggerSnsGroupStartMeas(DSIMasterID_en LeDSIMasterID,SnsMeasProfile_en MEAS_Profile ,CMDSeq_en R_SeqID,CMDSeq_en F_SeqID);

extern SnsGroupMeasStatus_en GetSnsGroupMeasStstus(DSIMasterID_en LeDSIMasterID,DSIChlID_en LeDSIChlID);

#endif /* __SVC_INTERRUPT_INT_H__ */
/*****************************************************END Sns_int.h*********************************************************/

