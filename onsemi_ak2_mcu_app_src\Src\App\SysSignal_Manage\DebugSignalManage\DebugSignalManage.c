/******************************************************************************
* MODIFICATION LOG :                                                           
*******************************************************************************
*                                                                              
* !Designed by     :             
* !Coded by        :                  
*                                                                              
*                           
*******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "DebugSignalManage.h"
#include "CAN_COM.h"
#include "PSL_AppSignalManage.h"
#include "PSL_State_Manage.h"
#include "ODO_AppSignalManage.h"
#include "SnsRawData_Int.h"
#if(DEBUG_FRAME_TX_EN==STD_ON)
Radar_EchoData_6F2_ecu0_0_MSGType  Radar_EchoData_6F2_ecu0_0_pool = { 0 };
VehicelInf_6F3_ecu0_0_MSGType  VehicelInf_6F3_ecu0_0_pool = { 0 };
FLS_EchoData_6F4_ecu0_0_MSGType  FLS_EchoData_6F4_ecu0_0_pool = { 0 };
FL_EchoData_6F5_ecu0_0_MSGType  FL_EchoData_6F5_ecu0_0_pool = { 0 };
FML_EchoData_6F6_ecu0_0_MSGType  FML_EchoData_6F6_ecu0_0_pool = { 0 };
FMR_EchoData_6F7_ecu0_0_MSGType  FMR_EchoData_6F7_ecu0_0_pool = { 0 };
FR_EchoData_6F8_ecu0_0_MSGType  FR_EchoData_6F8_ecu0_0_pool = { 0 };
FRS_EchoData_6F9_ecu0_0_MSGType  FRS_EchoData_6F9_ecu0_0_pool = { 0 };
RRS_EchoData_6FA_ecu0_0_MSGType  RRS_EchoData_6FA_ecu0_0_pool = { 0 };
RR_EchoData_6FB_ecu0_0_MSGType  RR_EchoData_6FB_ecu0_0_pool = { 0 };
RMR_EchoData_6FC_ecu0_0_MSGType  RMR_EchoData_6FC_ecu0_0_pool = { 0 };
RML_EchoData_6FD_ecu0_0_MSGType  RML_EchoData_6FD_ecu0_0_pool = { 0 };
RL_EchoData_6FE_ecu0_0_MSGType  RL_EchoData_6FE_ecu0_0_pool = { 0 };
RLS_EchoData_6FF_ecu0_0_MSGType  RLS_EchoData_6FF_ecu0_0_pool = { 0 };
//雷达新增发送底层数据报文
Sns_ImpData_6EE_ecu0_0_MSGType  Sns_ImpData_6EE_ecu0_0_pool = { 0 };
AK2Upper_LabData_6EF_ecu0_0_MSGType  AK2Upper_LabData_6EF_ecu0_0_pool = { 0 };
AK2Upper_Mcu2Pc_6F1_ecu0_0_MSGType  AK2Upper_Mcu2Pc_6F1_ecu0_0_pool = { 0 };
//雷达新增接收报文、即触发指令
AK2Upper_Pc2Mcu_6F0_ecu0_0_MSGType  AK2Upper_Pc2Mcu_6F0_ecu0_0_pool = { 0 };

/*PAS::AK2Upper_Pc2Mcu 0x6F0 signal access interface:
   GetRxAK2Upper_Command_6F0_ecu0_0()
   GetRxAK2Upper_WorfState_6F0_ecu0_0()
   GetRxAK2Upper_ShakehandFlg_6F0_ecu0_0()
   GetRxAK2Upper_CH0_6F0_ecu0_0()
   GetRxAK2Upper_CH1_6F0_ecu0_0()
   GetRxAK2Upper_BurstWaveMode_6F0_ecu0_0()
   GetRxAK2Upper_CH2_6F0_ecu0_0()
   GetRxAK2Upper_WorkMode_6F0_ecu0_0()
   GetRxAK2Upper_CH3_6F0_ecu0_0()
   GetRxAK2Upper_MaesMode_6F0_ecu0_0()
   GetRxAK2Upper_Profile_6F0_ecu0_0()
*/
/*PAS::AK2Upper_Pc2Mcu 0x6F0 received convert function*/
void Com_RxCvt_AK2Upper_Pc2Mcu_6F0_ecu0_0(void)
{
	/*message buffer variable is AK2Upper_Pc2Mcu_6F0_ecu0_0_pool*/
	/*message access handle is COM_RX_FRM_AK2UPPER_PC2MCU_6F0_ECU0_0_HANDLE*/

	//置标志；触发雷达底层数据报文发送；注意事件帧报文；可能只发一帧报文；

	return;
}
/*PAS::Sns_ImpData 0x6EE signal access interface:
   SetTxFLS_ImpData_6EE_ecu0_0(v)
   SetTxFL_ImpData_6EE_ecu0_0(v)
   SetTxFML_ImpData_6EE_ecu0_0(v)
   SetTxFMR_ImpData_6EE_ecu0_0(v)
   SetTxFR_ImpData_6EE_ecu0_0(v)
   SetTxFRS_ImpData_6EE_ecu0_0(v)
   SetTxRRS_ImpData_6EE_ecu0_0(v)
   SetTxRR_ImpData_6EE_ecu0_0(v)
   SetTxRMR_ImpData_6EE_ecu0_0(v)
   SetTxRML_ImpData_6EE_ecu0_0(v)
   SetTxRL_ImpData_6EE_ecu0_0(v)
   SetTxRLS_ImpData_6EE_ecu0_0(v)
*/
/*PAS::Sns_ImpData 0x6EE transmit convert function*/
void Com_TxCvt_Sns_ImpData_6EE_ecu0_0(void)
{
	/*message buffer variable is Sns_ImpData_6EE_ecu0_0_pool*/
	/*message access handle is COM_TX_FRM_SNS_IMPDATA_6EE_ECU0_0_HANDLE*/
	uint8 i;
	//TODO: synchronize application signal value



   //填充到发送buffer
	for (i = 0;i < 24;i++)
	{
		GcComDataBuffer[ID6EEIdx][i] = Sns_ImpData_6EE_ecu0_0_pool.byte[i];
	}
	return;
}

/*PAS::AK2Upper_LabData 0x6EF signal access interface:
   SetTxAK2Upper_SnsId_6EF_ecu0_0(v)
   SetTxAK2Upper_LabData0_6EF_ecu0_0(v)
   SetBytesAK2Upper_LabData0_6EF_ecu0_0(buf)
   SetTxAK2Upper_LabData1_6EF_ecu0_0(v)
   SetBytesAK2Upper_LabData1_6EF_ecu0_0(buf)
   SetTxAK2Upper_LabData2_6EF_ecu0_0(v)
   SetBytesAK2Upper_LabData2_6EF_ecu0_0(buf)
   SetTxAK2Upper_LabData3_6EF_ecu0_0(v)
   SetBytesAK2Upper_LabData3_6EF_ecu0_0(buf)
   SetTxAK2Upper_LabData4_6EF_ecu0_0(v)
   SetBytesAK2Upper_LabData4_6EF_ecu0_0(buf)
   SetTxAK2Upper_LabData5_6EF_ecu0_0(v)
   SetBytesAK2Upper_LabData5_6EF_ecu0_0(buf)
   SetTxAK2Upper_LabData6_6EF_ecu0_0(v)
   SetBytesAK2Upper_LabData6_6EF_ecu0_0(buf)
*/
/*PAS::AK2Upper_LabData 0x6EF transmit convert function*/
void Com_TxCvt_AK2Upper_LabData_6EF_ecu0_0(void)
{
	/*message buffer variable is AK2Upper_LabData_6EF_ecu0_0_pool*/
	/*message access handle is COM_TX_FRM_AK2UPPER_LABDATA_6EF_ECU0_0_HANDLE*/
	uint8 i;
	//TODO: synchronize application signal value


   //填充到发送buffer
	for (i = 0;i < 64;i++)
	{
		GcComDataBuffer[ID6EFIdx][i] = AK2Upper_LabData_6EF_ecu0_0_pool.byte[i];
	}
	return;
}

/*PAS::AK2Upper_Mcu2Pc 0x6F1 signal access interface:
   SetTxAK2Upper_Command_6F1_ecu0_0(v)
   SetTxAK2Upper_03_ACK_6F1_ecu0_0(v)
   SetTxAK2Upper_02_Ack_6F1_ecu0_0(v)
   SetTxAK2Upper_Version_6F1_ecu0_0(v)
   SetTxAK2Upper_DM_6F1_ecu0_0(v)
*/
/*PAS::AK2Upper_Mcu2Pc 0x6F1 transmit convert function*/
void Com_TxCvt_AK2Upper_Mcu2Pc_6F1_ecu0_0(void)
{
	/*message buffer variable is AK2Upper_Mcu2Pc_6F1_ecu0_0_pool*/
	/*message access handle is COM_TX_FRM_AK2UPPER_MCU2PC_6F1_ECU0_0_HANDLE*/
	uint8 i;
	//TODO: synchronize application signal value




   //填充到发送buffer
	for (i = 0;i < 8;i++)
	{
		GcComDataBuffer[ID6F1Idx][i] = AK2Upper_Mcu2Pc_6F1_ecu0_0_pool.byte[i];
	}
	return;
}
/*PAS::Radar_EchoData 0x6F2 signal access interface:
   SetTxFLS_Distance_6F2_ecu0_0(v)
   SetTxFL_Distance_6F2_ecu0_0(v)
   SetTxFML_Distance_6F2_ecu0_0(v)
   SetTxFMR_Distance_6F2_ecu0_0(v)
   SetTxFR_Distance_6F2_ecu0_0(v)
   SetTxFRS_Distance_6F2_ecu0_0(v)
   SetTxRRS_Distance_6F2_ecu0_0(v)
   SetTxRR_Distance_6F2_ecu0_0(v)
   SetTxRMR_Distance_6F2_ecu0_0(v)
   SetTxRML_Distance_6F2_ecu0_0(v)
   SetTxRL_Distance_6F2_ecu0_0(v)
   SetTxRLS_Distance_6F2_ecu0_0(v)
   SetTxFMR_HeightType_6F2_ecu0_0(v)
   SetTxFML_HeightType_6F2_ecu0_0(v)
   SetTxFL_HeightType_6F2_ecu0_0(v)
   SetTxFLS_HeightType_6F2_ecu0_0(v)
   SetTxRR_HeightType_6F2_ecu0_0(v)
   SetTxRRS_HeightType_6F2_ecu0_0(v)
   SetTxFRS_HeightType_6F2_ecu0_0(v)
   SetTxFR_HeightType_6F2_ecu0_0(v)
   SetTxRLS_HeightType_6F2_ecu0_0(v)
   SetTxRL_HeightType_6F2_ecu0_0(v)
   SetTxRML_HeightType_6F2_ecu0_0(v)
   SetTxRMR_HeightType_6F2_ecu0_0(v)
*/
/*PAS::Radar_EchoData 0x6F2 transmit convert function*/
void Com_TxCvt_Radar_EchoData_6F2_ecu0_0(void)
{
	/*message buffer variable is Radar_EchoData_6F2_ecu0_0_pool*/
	/*message access handle is COM_TX_FRM_RADAR_ECHODATA_6F2_ECU0_0_HANDLE*/
	uint8 i;
	//TODO: synchronize application signal value
	for (i = 0;i < 32;i++)
	{
		GcComDataBuffer[ID6F2Idx][i] = Radar_EchoData_6F2_ecu0_0_pool.byte[i];
	}
	return;
}
/*PAS::VehicelInf 0x6F3 signal access interface:
   SetTxCar_Odo_X_6F3_ecu0_0(v)
   SetTxCar_Odo_Y_6F3_ecu0_0(v)
   SetTxCar_Odo_YawAngle_6F3_ecu0_0(v)
   SetTxVehicle_Type_6F3_ecu0_0(v)
   SetTxVehicle_Cfg_6F3_ecu0_0(v)
   SetTxVehicle_Platform_6F3_ecu0_0(v)
   SetTxCar_Dirve_Dis_6F3_ecu0_0(v)
*/
void Com_TxCvt_VehicelInf_6F3_ecu0_0(void)
{
	/*message buffer variable is VehicelInf_6F3_ecu0_0_pool*/
	/*message access handle is COM_TX_FRM_VEHICELINF_6F3_ECU0_0_HANDLE*/
	uint8 i;
	//TODO: synchronize application signal value
	uint16 Lu16Data;
	VehicleTypeType LenuVEH_Type;
	VehicleCfgLevelType LenuVehicleCfgLevel;
	VehiclePlatformType LpenuVehiclePlatform;
	SetTxCar_Odo_X_6F3_ecu0_0((sint32)(GstrPDCSnsUseOdo.fCar_X_Coor));
	SetTxCar_Odo_Y_6F3_ecu0_0((sint32)(GstrPDCSnsUseOdo.fCar_Y_Coor));
	Lu16Data = (uint16)(12868u + GstrPDCSnsUseOdo.fCar_YawAngle * 4096);
	SetTxCar_Odo_YawAngle_6F3_ecu0_0(Lu16Data);

	ReadCAN_AppSignal_VEH_Type(&LenuVEH_Type);
	SetTxVehicle_Type_6F3_ecu0_0(LenuVEH_Type);
	ReadCAN_AppSignal_VehicleCfgLevel(&LenuVehicleCfgLevel);
	SetTxVehicle_Cfg_6F3_ecu0_0(LenuVehicleCfgLevel);
	ReadCAN_AppSignal_VehiclePlatform(&LpenuVehiclePlatform);
	SetTxVehicle_Platform_6F3_ecu0_0(LpenuVehiclePlatform);
	SetTxCar_Dirve_Dis_6F3_ecu0_0((uint32)(GstrPDCSnsUseOdo.fDrvDis))
	for (i = 0;i < 16;i++)
	{
		GcComDataBuffer[ID6F3Idx][i] = VehicelInf_6F3_ecu0_0_pool.byte[i];
	}
	return;
}

void Com_TxCvt_FLS_EchoData_6F4_ecu0_0(void)
{
	/*message buffer variable is FLS_EchoData_6F4_ecu0_0_pool*/
	/*message access handle is COM_TX_FRM_FLS_ECHODATA_6F4_ECU0_0_HANDLE*/
	uint8 i;
	static uint8 FLSImpOutputCnt = 0;
	const SnsMeasDistData_Str* pSnsMeasData = Elmos17SnsCtrl_GetSnsMeasDistData();
	//TODO: synchronize application signal value
	SetTxUpdateFlg_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8UpdateFlg[PDC_SNS_CH_FLS]);
	SetTxSnsWorkSts_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].enuPDCSnsWorkSts[PDC_SNS_CH_FLS]);
	SetTxMaster_Meas_Type_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].enuPDCSnsMeasType[PDC_SNS_CH_FLS]);
	//SetTxEepromErrFlg_6F4_ecu0_0(EepromErrFlg[SNS_FL_S]);
	SetTxMaster_Rtm_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RingTime[PDC_SNS_CH_FLS]);

	SetTxMaster_Distance_0_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterDis[PDC_SNS_CH_FLS][0]);
	SetTxMaster_Distance_1_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterDis[PDC_SNS_CH_FLS][1]);
	SetTxMaster_Distance_2_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterDis[PDC_SNS_CH_FLS][2]);
	SetTxMaster_Distance_3_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterDis[PDC_SNS_CH_FLS][3]);

	SetTxMaster_EchoHigh_0_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterHeight[PDC_SNS_CH_FLS][0]);
	SetTxMaster_EchoHigh_1_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterHeight[PDC_SNS_CH_FLS][1]);
	SetTxMaster_EchoHigh_2_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterHeight[PDC_SNS_CH_FLS][2]);
	SetTxMaster_EchoHigh_3_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterHeight[PDC_SNS_CH_FLS][3]);

	SetTxLeftListen_Distance_0_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenDis[PDC_SNS_CH_FLS][0]);
	SetTxLeftListen_Distance_1_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenDis[PDC_SNS_CH_FLS][1]);
	SetTxLeftListen_Distance_2_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenDis[PDC_SNS_CH_FLS][2]);
	SetTxLeftListen_Distance_3_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenDis[PDC_SNS_CH_FLS][3]);

	SetTxLeftListen_EchoHigh_0_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenHeight[PDC_SNS_CH_FLS][0]);
	SetTxLeftListen_EchoHigh_1_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenHeight[PDC_SNS_CH_FLS][1]);
	SetTxLeftListen_EchoHigh_2_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenHeight[PDC_SNS_CH_FLS][2]);
	SetTxLeftListen_EchoHigh_3_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenHeight[PDC_SNS_CH_FLS][3]);

	SetTxRightListen_Distance_0_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenDis[PDC_SNS_CH_FLS][0]);
	SetTxRightListen_Distance_1_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenDis[PDC_SNS_CH_FLS][1]);
	SetTxRightListen_Distance_2_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenDis[PDC_SNS_CH_FLS][2]);
	SetTxRightListen_Distance_3_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenDis[PDC_SNS_CH_FLS][3]);


	SetTxRightListen_EchoHigh_0_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenHeight[PDC_SNS_CH_FLS][0]);
	SetTxRightListen_EchoHigh_1_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenHeight[PDC_SNS_CH_FLS][1]);
	SetTxRightListen_EchoHigh_2_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenHeight[PDC_SNS_CH_FLS][2]);
	SetTxRightListen_EchoHigh_3_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenHeight[PDC_SNS_CH_FLS][3]);


	SetTxMasterEchoCnt_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8MasterEchoCnt[PDC_SNS_CH_FLS]);
	SetTxLeftEchoCnt_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8LeftListenEchoCnt[PDC_SNS_CH_FLS]);
	SetTxRightEchoCnt_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8RightListenEchoCnt[PDC_SNS_CH_FLS]);
	SetTxNFD_Flag_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8NFD_Flag[PDC_SNS_CH_FLS]);

	FLSImpOutputCnt++;
	if(FLSImpOutputCnt > 10)
	{
		FLSImpOutputCnt = 0;
	}

	if(FLSImpOutputCnt < 6)
	{
		SetTxNFD_Dis_6F4_ecu0_0(pSnsMeasData[SNS_FL_S].SnsDiagData.ImpAndImpIdx[0]);
	}
	else
	{
		SetTxNFD_Dis_6F4_ecu0_0(pSnsMeasData[SNS_FL_S].SnsDiagData.ImpAndImpIdx[1]);
	}
	//SetTxNFD_Dis_6F4_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16NFD_Dis[PDC_SNS_CH_FLS]);
	SetTxRing_Fre_6F4_ecu0_0(pSnsMeasData[SNS_FL_S].SnsDiagData.RFreq);
 	SetTxNoiseCnt_6F4_ecu0_0(pSnsMeasData[SNS_FL_S].SnsDiagData.NoiseAbnormalCnt);
	SetTxNoiseSum_6F4_ecu0_0(pSnsMeasData[SNS_FL_S].SnsDiagData.NoiseSum); 
	
	for (i = 0;i < 64;i++)
	{
		GcComDataBuffer[ID6F4Idx][i] = FLS_EchoData_6F4_ecu0_0_pool.byte[i];
	}
	return;
}

void Com_TxCvt_FL_EchoData_6F5_ecu0_0(void)
{
	/*message buffer variable is FL_EchoData_6F5_ecu0_0_pool*/
	/*message access handle is COM_TX_FRM_FL_ECHODATA_6F5_ECU0_0_HANDLE*/
	uint8 i;
	static uint8 FLImpOutputCnt = 0;
	const SnsMeasDistData_Str* pSnsMeasData = Elmos17SnsCtrl_GetSnsMeasDistData();
	//TODO: synchronize application signal value
	SetTxUpdateFlg_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8UpdateFlg[PDC_SNS_CH_FL]);
	SetTxSnsWorkSts_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].enuPDCSnsWorkSts[PDC_SNS_CH_FL]);
	SetTxMaster_Meas_Type_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].enuPDCSnsMeasType[PDC_SNS_CH_FL]);
	//SetTxEepromErrFlg_6F5_ecu0_0(EepromErrFlg[SNS_FL]);
	SetTxMaster_Rtm_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RingTime[PDC_SNS_CH_FL]);

	SetTxMaster_Distance_0_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterDis[PDC_SNS_CH_FL][0]);
	SetTxMaster_Distance_1_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterDis[PDC_SNS_CH_FL][1]);
	SetTxMaster_Distance_2_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterDis[PDC_SNS_CH_FL][2]);
	SetTxMaster_Distance_3_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterDis[PDC_SNS_CH_FL][3]);

	SetTxMaster_EchoHigh_0_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterHeight[PDC_SNS_CH_FL][0]);
	SetTxMaster_EchoHigh_1_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterHeight[PDC_SNS_CH_FL][1]);
	SetTxMaster_EchoHigh_2_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterHeight[PDC_SNS_CH_FL][2]);
	SetTxMaster_EchoHigh_3_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterHeight[PDC_SNS_CH_FL][3]);


	SetTxLeftListen_Distance_0_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenDis[PDC_SNS_CH_FL][0]);
	SetTxLeftListen_Distance_1_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenDis[PDC_SNS_CH_FL][1]);
	SetTxLeftListen_Distance_2_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenDis[PDC_SNS_CH_FL][2]);
	SetTxLeftListen_Distance_3_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenDis[PDC_SNS_CH_FL][3]);

	SetTxLeftListen_EchoHigh_0_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenHeight[PDC_SNS_CH_FL][0]);
	SetTxLeftListen_EchoHigh_1_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenHeight[PDC_SNS_CH_FL][1]);
	SetTxLeftListen_EchoHigh_2_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenHeight[PDC_SNS_CH_FL][2]);
	SetTxLeftListen_EchoHigh_3_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenHeight[PDC_SNS_CH_FL][3]);



	SetTxRightListen_Distance_0_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenDis[PDC_SNS_CH_FL][0]);
	SetTxRightListen_Distance_1_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenDis[PDC_SNS_CH_FL][1]);
	SetTxRightListen_Distance_2_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenDis[PDC_SNS_CH_FL][2]);
	SetTxRightListen_Distance_3_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenDis[PDC_SNS_CH_FL][3]);


	SetTxRightListen_EchoHigh_0_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenHeight[PDC_SNS_CH_FL][0]);
	SetTxRightListen_EchoHigh_1_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenHeight[PDC_SNS_CH_FL][1]);
	SetTxRightListen_EchoHigh_2_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenHeight[PDC_SNS_CH_FL][2]);
	SetTxRightListen_EchoHigh_3_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenHeight[PDC_SNS_CH_FL][3]);


	SetTxMasterEchoCnt_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8MasterEchoCnt[PDC_SNS_CH_FL]);
	SetTxLeftEchoCnt_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8LeftListenEchoCnt[PDC_SNS_CH_FL]);
	SetTxRightEchoCnt_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8RightListenEchoCnt[PDC_SNS_CH_FL]);

	SetTxNFD_Flag_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8NFD_Flag[PDC_SNS_CH_FL]);

	FLImpOutputCnt++;
	if(FLImpOutputCnt > 10)
	{
		FLImpOutputCnt = 0;
	}

	if(FLImpOutputCnt < 6)
	{
		SetTxNFD_Dis_6F5_ecu0_0(pSnsMeasData[SNS_FL].SnsDiagData.ImpAndImpIdx[0]);
	}
	else
	{
		SetTxNFD_Dis_6F5_ecu0_0(pSnsMeasData[SNS_FL].SnsDiagData.ImpAndImpIdx[1]);
	}
	//SetTxNFD_Dis_6F5_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16NFD_Dis[PDC_SNS_CH_FL]);
	SetTxRing_Fre_6F5_ecu0_0(pSnsMeasData[SNS_FL].SnsDiagData.RFreq);
	SetTxNoiseCnt_6F5_ecu0_0(pSnsMeasData[SNS_FL].SnsDiagData.NoiseAbnormalCnt);
	SetTxNoiseSum_6F5_ecu0_0(pSnsMeasData[SNS_FL].SnsDiagData.NoiseSum); 
	
	for (i = 0;i < 64;i++)
	{
		GcComDataBuffer[ID6F5Idx][i] = FL_EchoData_6F5_ecu0_0_pool.byte[i];
	}
	return;
}
void Com_TxCvt_FML_EchoData_6F6_ecu0_0(void)
{
	/*message buffer variable is FML_EchoData_6F6_ecu0_0_pool*/
	/*message access handle is COM_TX_FRM_FML_ECHODATA_6F6_ECU0_0_HANDLE*/
	uint8 i;	
	static uint8 FMLImpOutputCnt = 0;
	const SnsMeasDistData_Str* pSnsMeasData = Elmos17SnsCtrl_GetSnsMeasDistData();
	//TODO: synchronize application signal value
	SetTxUpdateFlg_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8UpdateFlg[PDC_SNS_CH_FML]);
	SetTxSnsWorkSts_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].enuPDCSnsWorkSts[PDC_SNS_CH_FML]);
	SetTxMaster_Meas_Type_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].enuPDCSnsMeasType[PDC_SNS_CH_FML]);
	//SetTxEepromErrFlg_6F6_ecu0_0(EepromErrFlg[SNS_FML]);
	SetTxMaster_Rtm_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RingTime[PDC_SNS_CH_FML]);

	SetTxMaster_Distance_0_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterDis[PDC_SNS_CH_FML][0]);
	SetTxMaster_Distance_1_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterDis[PDC_SNS_CH_FML][1]);
	SetTxMaster_Distance_2_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterDis[PDC_SNS_CH_FML][2]);
	SetTxMaster_Distance_3_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterDis[PDC_SNS_CH_FML][3]);



	SetTxMaster_EchoHigh_0_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterHeight[PDC_SNS_CH_FML][0]);
	SetTxMaster_EchoHigh_1_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterHeight[PDC_SNS_CH_FML][1]);
	SetTxMaster_EchoHigh_2_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterHeight[PDC_SNS_CH_FML][2]);
	SetTxMaster_EchoHigh_3_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterHeight[PDC_SNS_CH_FML][3]);


	SetTxLeftListen_Distance_0_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenDis[PDC_SNS_CH_FML][0]);
	SetTxLeftListen_Distance_1_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenDis[PDC_SNS_CH_FML][1]);
	SetTxLeftListen_Distance_2_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenDis[PDC_SNS_CH_FML][2]);
	SetTxLeftListen_Distance_3_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenDis[PDC_SNS_CH_FML][3]);



	SetTxLeftListen_EchoHigh_0_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenHeight[PDC_SNS_CH_FML][0]);
	SetTxLeftListen_EchoHigh_1_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenHeight[PDC_SNS_CH_FML][1]);
	SetTxLeftListen_EchoHigh_2_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenHeight[PDC_SNS_CH_FML][2]);
	SetTxLeftListen_EchoHigh_3_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenHeight[PDC_SNS_CH_FML][3]);

	SetTxRightListen_Distance_0_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenDis[PDC_SNS_CH_FML][0]);
	SetTxRightListen_Distance_1_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenDis[PDC_SNS_CH_FML][1]);
	SetTxRightListen_Distance_2_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenDis[PDC_SNS_CH_FML][2]);
	SetTxRightListen_Distance_3_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenDis[PDC_SNS_CH_FML][3]);


	SetTxRightListen_EchoHigh_0_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenHeight[PDC_SNS_CH_FML][0]);
	SetTxRightListen_EchoHigh_1_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenHeight[PDC_SNS_CH_FML][1]);
	SetTxRightListen_EchoHigh_2_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenHeight[PDC_SNS_CH_FML][2]);
	SetTxRightListen_EchoHigh_3_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenHeight[PDC_SNS_CH_FML][3]);

	SetTxMasterEchoCnt_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8MasterEchoCnt[PDC_SNS_CH_FML]);
	SetTxLeftEchoCnt_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8LeftListenEchoCnt[PDC_SNS_CH_FML]);
	SetTxRightEchoCnt_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8RightListenEchoCnt[PDC_SNS_CH_FML]);

	SetTxNFD_Flag_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8NFD_Flag[PDC_SNS_CH_FML]);

	FMLImpOutputCnt++;
	if(FMLImpOutputCnt > 10)
	{
		FMLImpOutputCnt = 0;
	}

	if(FMLImpOutputCnt < 6)
	{
		SetTxNFD_Dis_6F6_ecu0_0(pSnsMeasData[SNS_FML].SnsDiagData.ImpAndImpIdx[0]);
	}
	else
	{
		SetTxNFD_Dis_6F6_ecu0_0(pSnsMeasData[SNS_FML].SnsDiagData.ImpAndImpIdx[1]);
	}
	//SetTxNFD_Dis_6F6_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16NFD_Dis[PDC_SNS_CH_FML]);
	SetTxRing_Fre_6F6_ecu0_0(pSnsMeasData[SNS_FML].SnsDiagData.RFreq);
	SetTxNoiseCnt_6F6_ecu0_0(pSnsMeasData[SNS_FML].SnsDiagData.NoiseAbnormalCnt);
	SetTxNoiseSum_6F6_ecu0_0(pSnsMeasData[SNS_FML].SnsDiagData.NoiseSum); 
	
	for (i = 0;i < 64;i++)
	{
		GcComDataBuffer[ID6F6Idx][i] = FML_EchoData_6F6_ecu0_0_pool.byte[i];
	}
	return;
}
void Com_TxCvt_FMR_EchoData_6F7_ecu0_0(void)
{
	/*message buffer variable is FMR_EchoData_6F7_ecu0_0_pool*/
	/*message access handle is COM_TX_FRM_FMR_ECHODATA_6F7_ECU0_0_HANDLE*/
	uint8 i;
	static uint8 FMRImpOutputCnt = 0;
	const SnsMeasDistData_Str* pSnsMeasData = Elmos17SnsCtrl_GetSnsMeasDistData();
	//TODO: synchronize application signal value
	SetTxUpdateFlg_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8UpdateFlg[PDC_SNS_CH_FMR]);
	SetTxSnsWorkSts_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].enuPDCSnsWorkSts[PDC_SNS_CH_FMR]);
	SetTxMaster_Meas_Type_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].enuPDCSnsMeasType[PDC_SNS_CH_FMR]);
	//SetTxEepromErrFlg_6F7_ecu0_0(EepromErrFlg[SNS_FMR]);
	SetTxMaster_Rtm_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RingTime[PDC_SNS_CH_FMR]);

	SetTxMaster_Distance_0_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterDis[PDC_SNS_CH_FMR][0]);
	SetTxMaster_Distance_1_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterDis[PDC_SNS_CH_FMR][1]);
	SetTxMaster_Distance_2_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterDis[PDC_SNS_CH_FMR][2]);
	SetTxMaster_Distance_3_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterDis[PDC_SNS_CH_FMR][3]);

	SetTxMaster_EchoHigh_0_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterHeight[PDC_SNS_CH_FMR][0]);
	SetTxMaster_EchoHigh_1_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterHeight[PDC_SNS_CH_FMR][1]);
	SetTxMaster_EchoHigh_2_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterHeight[PDC_SNS_CH_FMR][2]);
	SetTxMaster_EchoHigh_3_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterHeight[PDC_SNS_CH_FMR][3]);


	SetTxLeftListen_Distance_0_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenDis[PDC_SNS_CH_FMR][0]);
	SetTxLeftListen_Distance_1_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenDis[PDC_SNS_CH_FMR][1]);
	SetTxLeftListen_Distance_2_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenDis[PDC_SNS_CH_FMR][2]);
	SetTxLeftListen_Distance_3_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenDis[PDC_SNS_CH_FMR][3]);

	SetTxLeftListen_EchoHigh_0_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenHeight[PDC_SNS_CH_FMR][0]);
	SetTxLeftListen_EchoHigh_1_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenHeight[PDC_SNS_CH_FMR][1]);
	SetTxLeftListen_EchoHigh_2_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenHeight[PDC_SNS_CH_FMR][2]);
	SetTxLeftListen_EchoHigh_3_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenHeight[PDC_SNS_CH_FMR][3]);

	SetTxRightListen_Distance_0_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenDis[PDC_SNS_CH_FMR][0]);
	SetTxRightListen_Distance_1_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenDis[PDC_SNS_CH_FMR][1]);
	SetTxRightListen_Distance_2_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenDis[PDC_SNS_CH_FMR][2]);
	SetTxRightListen_Distance_3_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenDis[PDC_SNS_CH_FMR][3]);

	SetTxRightListen_EchoHigh_0_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenHeight[PDC_SNS_CH_FMR][0]);
	SetTxRightListen_EchoHigh_1_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenHeight[PDC_SNS_CH_FMR][1]);
	SetTxRightListen_EchoHigh_2_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenHeight[PDC_SNS_CH_FMR][2]);
	SetTxRightListen_EchoHigh_3_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenHeight[PDC_SNS_CH_FMR][3]);

	SetTxMasterEchoCnt_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8MasterEchoCnt[PDC_SNS_CH_FMR]);
	SetTxLeftEchoCnt_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8LeftListenEchoCnt[PDC_SNS_CH_FMR]);
	SetTxRightEchoCnt_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8RightListenEchoCnt[PDC_SNS_CH_FMR]);

	SetTxNFD_Flag_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8NFD_Flag[PDC_SNS_CH_FMR]);

	FMRImpOutputCnt++;
	if(FMRImpOutputCnt > 10)
	{
		FMRImpOutputCnt = 0;
	}

	if(FMRImpOutputCnt < 6)
	{
		SetTxNFD_Dis_6F7_ecu0_0(pSnsMeasData[SNS_FMR].SnsDiagData.ImpAndImpIdx[0]);
	}
	else
	{
		SetTxNFD_Dis_6F7_ecu0_0(pSnsMeasData[SNS_FMR].SnsDiagData.ImpAndImpIdx[1]);
	}
	//SetTxNFD_Dis_6F7_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16NFD_Dis[PDC_SNS_CH_FMR]);
	SetTxRing_Fre_6F7_ecu0_0(pSnsMeasData[SNS_FMR].SnsDiagData.RFreq);
	SetTxNoiseCnt_6F7_ecu0_0(pSnsMeasData[SNS_FMR].SnsDiagData.NoiseAbnormalCnt);
	SetTxNoiseSum_6F7_ecu0_0(pSnsMeasData[SNS_FMR].SnsDiagData.NoiseSum); 
	
	for (i = 0;i < 64;i++)
	{
		GcComDataBuffer[ID6F7Idx][i] = FMR_EchoData_6F7_ecu0_0_pool.byte[i];
	}
	return;
}
void Com_TxCvt_FR_EchoData_6F8_ecu0_0(void)
{
	/*message buffer variable is FR_EchoData_6F8_ecu0_0_pool*/
	/*message access handle is COM_TX_FRM_FR_ECHODATA_6F8_ECU0_0_HANDLE*/
	uint8 i;	
	static uint8 FRImpOutputCnt = 0;
	const SnsMeasDistData_Str* pSnsMeasData = Elmos17SnsCtrl_GetSnsMeasDistData();
	//TODO: synchronize application signal value
	SetTxUpdateFlg_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8UpdateFlg[PDC_SNS_CH_FR]);
	SetTxSnsWorkSts_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].enuPDCSnsWorkSts[PDC_SNS_CH_FR]);
	SetTxMaster_Meas_Type_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].enuPDCSnsMeasType[PDC_SNS_CH_FR]);
	//SetTxEepromErrFlg_6F8_ecu0_0(EepromErrFlg[SNS_FR]);
	SetTxMaster_Rtm_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RingTime[PDC_SNS_CH_FR]);

	SetTxMaster_Distance_0_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterDis[PDC_SNS_CH_FR][0]);
	SetTxMaster_Distance_1_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterDis[PDC_SNS_CH_FR][1]);
	SetTxMaster_Distance_2_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterDis[PDC_SNS_CH_FR][2]);
	SetTxMaster_Distance_3_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterDis[PDC_SNS_CH_FR][3]);

	SetTxMaster_EchoHigh_0_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterHeight[PDC_SNS_CH_FR][0]);
	SetTxMaster_EchoHigh_1_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterHeight[PDC_SNS_CH_FR][1]);
	SetTxMaster_EchoHigh_2_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterHeight[PDC_SNS_CH_FR][2]);
	SetTxMaster_EchoHigh_3_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterHeight[PDC_SNS_CH_FR][3]);

	SetTxLeftListen_Distance_0_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenDis[PDC_SNS_CH_FR][0]);
	SetTxLeftListen_Distance_1_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenDis[PDC_SNS_CH_FR][1]);
	SetTxLeftListen_Distance_2_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenDis[PDC_SNS_CH_FR][2]);
	SetTxLeftListen_Distance_3_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenDis[PDC_SNS_CH_FR][3]);

	SetTxLeftListen_EchoHigh_0_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenHeight[PDC_SNS_CH_FR][0]);
	SetTxLeftListen_EchoHigh_1_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenHeight[PDC_SNS_CH_FR][1]);
	SetTxLeftListen_EchoHigh_2_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenHeight[PDC_SNS_CH_FR][2]);
	SetTxLeftListen_EchoHigh_3_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenHeight[PDC_SNS_CH_FR][3]);

	SetTxRightListen_Distance_0_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenDis[PDC_SNS_CH_FR][0]);
	SetTxRightListen_Distance_1_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenDis[PDC_SNS_CH_FR][1]);
	SetTxRightListen_Distance_2_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenDis[PDC_SNS_CH_FR][2]);
	SetTxRightListen_Distance_3_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenDis[PDC_SNS_CH_FR][3]);

	SetTxRightListen_EchoHigh_0_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenHeight[PDC_SNS_CH_FR][0]);
	SetTxRightListen_EchoHigh_1_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenHeight[PDC_SNS_CH_FR][1]);
	SetTxRightListen_EchoHigh_2_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenHeight[PDC_SNS_CH_FR][2]);
	SetTxRightListen_EchoHigh_3_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenHeight[PDC_SNS_CH_FR][3]);

	SetTxMasterEchoCnt_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8MasterEchoCnt[PDC_SNS_CH_FR]);
	SetTxLeftEchoCnt_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8LeftListenEchoCnt[PDC_SNS_CH_FR]);
	SetTxRightEchoCnt_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8RightListenEchoCnt[PDC_SNS_CH_FR]);

	SetTxNFD_Flag_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8NFD_Flag[PDC_SNS_CH_FR]);

	FRImpOutputCnt++;
	if(FRImpOutputCnt > 10)
	{
		FRImpOutputCnt = 0;
	}

	if(FRImpOutputCnt < 6)
	{
		SetTxNFD_Dis_6F8_ecu0_0(pSnsMeasData[SNS_FR].SnsDiagData.ImpAndImpIdx[0]);
	}
	else
	{
		SetTxNFD_Dis_6F8_ecu0_0(pSnsMeasData[SNS_FR].SnsDiagData.ImpAndImpIdx[1]);
	}
	//SetTxNFD_Dis_6F8_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16NFD_Dis[PDC_SNS_CH_FR]);
	SetTxRing_Fre_6F8_ecu0_0(pSnsMeasData[SNS_FR].SnsDiagData.RFreq);
	SetTxNoiseCnt_6F8_ecu0_0(pSnsMeasData[SNS_FR].SnsDiagData.NoiseAbnormalCnt);
	SetTxNoiseSum_6F8_ecu0_0(pSnsMeasData[SNS_FR].SnsDiagData.NoiseSum); 
	
	for (i = 0;i < 64;i++)
	{
		GcComDataBuffer[ID6F8Idx][i] = FR_EchoData_6F8_ecu0_0_pool.byte[i];
	}
	return;
}
void Com_TxCvt_FRS_EchoData_6F9_ecu0_0(void)
{
	/*message buffer variable is FRS_EchoData_6F9_ecu0_0_pool*/
	/*message access handle is COM_TX_FRM_FRS_ECHODATA_6F9_ECU0_0_HANDLE*/
	uint8 i;	
	static uint8 FRSImpOutputCnt = 0;
	const SnsMeasDistData_Str* pSnsMeasData = Elmos17SnsCtrl_GetSnsMeasDistData();
	//TODO: synchronize application signal value
	SetTxUpdateFlg_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8UpdateFlg[PDC_SNS_CH_FRS]);
	SetTxSnsWorkSts_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].enuPDCSnsWorkSts[PDC_SNS_CH_FRS]);
	SetTxMaster_Meas_Type_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].enuPDCSnsMeasType[PDC_SNS_CH_FRS]);
	//SetTxEepromErrFlg_6F9_ecu0_0(EepromErrFlg[SNS_FR_S]);
	SetTxMaster_Rtm_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RingTime[PDC_SNS_CH_FRS]);

	SetTxMaster_Distance_0_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterDis[PDC_SNS_CH_FRS][0]);
	SetTxMaster_Distance_1_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterDis[PDC_SNS_CH_FRS][1]);
	SetTxMaster_Distance_2_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterDis[PDC_SNS_CH_FRS][2]);
	SetTxMaster_Distance_3_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterDis[PDC_SNS_CH_FRS][3]);

	SetTxMaster_EchoHigh_0_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterHeight[PDC_SNS_CH_FRS][0]);
	SetTxMaster_EchoHigh_1_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterHeight[PDC_SNS_CH_FRS][1]);
	SetTxMaster_EchoHigh_2_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterHeight[PDC_SNS_CH_FRS][2]);
	SetTxMaster_EchoHigh_3_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16MasterHeight[PDC_SNS_CH_FRS][3]);

	SetTxLeftListen_Distance_0_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenDis[PDC_SNS_CH_FRS][0]);
	SetTxLeftListen_Distance_1_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenDis[PDC_SNS_CH_FRS][1]);
	SetTxLeftListen_Distance_2_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenDis[PDC_SNS_CH_FRS][2]);
	SetTxLeftListen_Distance_3_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenDis[PDC_SNS_CH_FRS][3]);

	SetTxLeftListen_EchoHigh_0_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenHeight[PDC_SNS_CH_FRS][0]);
	SetTxLeftListen_EchoHigh_1_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenHeight[PDC_SNS_CH_FRS][1]);
	SetTxLeftListen_EchoHigh_2_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenHeight[PDC_SNS_CH_FRS][2]);
	SetTxLeftListen_EchoHigh_3_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16LeftListenHeight[PDC_SNS_CH_FRS][3]);

	SetTxRightListen_Distance_0_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenDis[PDC_SNS_CH_FRS][0]);
	SetTxRightListen_Distance_1_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenDis[PDC_SNS_CH_FRS][1]);
	SetTxRightListen_Distance_2_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenDis[PDC_SNS_CH_FRS][2]);
	SetTxRightListen_Distance_3_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenDis[PDC_SNS_CH_FRS][3]);

	SetTxRightListen_EchoHigh_0_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenHeight[PDC_SNS_CH_FRS][0]);
	SetTxRightListen_EchoHigh_1_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenHeight[PDC_SNS_CH_FRS][1]);
	SetTxRightListen_EchoHigh_2_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenHeight[PDC_SNS_CH_FRS][2]);
	SetTxRightListen_EchoHigh_3_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16RightListenHeight[PDC_SNS_CH_FRS][3]);

	SetTxMasterEchoCnt_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8MasterEchoCnt[PDC_SNS_CH_FRS]);
	SetTxLeftEchoCnt_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8LeftListenEchoCnt[PDC_SNS_CH_FRS]);
	SetTxRightEchoCnt_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8RightListenEchoCnt[PDC_SNS_CH_FRS]);

	SetTxNFD_Flag_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u8NFD_Flag[PDC_SNS_CH_FRS]);

	FRSImpOutputCnt++;
	if(FRSImpOutputCnt > 10)
	{
		FRSImpOutputCnt = 0;
	}

	if(FRSImpOutputCnt < 6)
	{
		SetTxNFD_Dis_6F9_ecu0_0(pSnsMeasData[SNS_FR_S].SnsDiagData.ImpAndImpIdx[0]);
	}
	else
	{
		SetTxNFD_Dis_6F9_ecu0_0(pSnsMeasData[SNS_FR_S].SnsDiagData.ImpAndImpIdx[1]);
	}
	//SetTxNFD_Dis_6F9_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].u16NFD_Dis[PDC_SNS_CH_FRS]);
	SetTxRing_Fre_6F9_ecu0_0(pSnsMeasData[SNS_FR_S].SnsDiagData.RFreq);
	SetTxNoiseCnt_6F9_ecu0_0(pSnsMeasData[SNS_FR_S].SnsDiagData.NoiseAbnormalCnt);
	SetTxNoiseSum_6F9_ecu0_0(pSnsMeasData[SNS_FR_S].SnsDiagData.NoiseSum); 
	
	for (i = 0;i < 64;i++)
	{
		GcComDataBuffer[ID6F9Idx][i] = FRS_EchoData_6F9_ecu0_0_pool.byte[i];
	}
	return;
}
void Com_TxCvt_RRS_EchoData_6FA_ecu0_0(void)
{
	/*message buffer variable is RRS_EchoData_6FA_ecu0_0_pool*/
	/*message access handle is COM_TX_FRM_RRS_ECHODATA_6FA_ECU0_0_HANDLE*/
	uint8 i;	
	static uint8 RRSImpOutputCnt = 0;
	const SnsMeasDistData_Str* pSnsMeasData = Elmos17SnsCtrl_GetSnsMeasDistData();
	//TODO: synchronize application signal value
	SetTxUpdateFlg_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8UpdateFlg[PDC_SNS_CH_RRS]);
	SetTxSnsWorkSts_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].enuPDCSnsWorkSts[PDC_SNS_CH_RRS]);
	SetTxMaster_Meas_Type_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].enuPDCSnsMeasType[PDC_SNS_CH_RRS]);
	//SetTxEepromErrFlg_6FA_ecu0_0(EepromErrFlg[SNS_RR_S]);
	SetTxMaster_Rtm_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RingTime[PDC_SNS_CH_RRS]);

	SetTxMaster_Distance_0_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterDis[PDC_SNS_CH_RRS][0]);
	SetTxMaster_Distance_1_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterDis[PDC_SNS_CH_RRS][1]);
	SetTxMaster_Distance_2_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterDis[PDC_SNS_CH_RRS][2]);
	SetTxMaster_Distance_3_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterDis[PDC_SNS_CH_RRS][3]);

	SetTxMaster_EchoHigh_0_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterHeight[PDC_SNS_CH_RRS][0]);
	SetTxMaster_EchoHigh_1_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterHeight[PDC_SNS_CH_RRS][1]);
	SetTxMaster_EchoHigh_2_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterHeight[PDC_SNS_CH_RRS][2]);
	SetTxMaster_EchoHigh_3_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterHeight[PDC_SNS_CH_RRS][3]);

	SetTxLeftListen_Distance_0_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenDis[PDC_SNS_CH_RRS][0]);
	SetTxLeftListen_Distance_1_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenDis[PDC_SNS_CH_RRS][1]);
	SetTxLeftListen_Distance_2_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenDis[PDC_SNS_CH_RRS][2]);
	SetTxLeftListen_Distance_3_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenDis[PDC_SNS_CH_RRS][3]);


	SetTxLeftListen_EchoHigh_0_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenHeight[PDC_SNS_CH_RRS][0]);
	SetTxLeftListen_EchoHigh_1_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenHeight[PDC_SNS_CH_RRS][1]);
	SetTxLeftListen_EchoHigh_2_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenHeight[PDC_SNS_CH_RRS][2]);
	SetTxLeftListen_EchoHigh_3_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenHeight[PDC_SNS_CH_RRS][3]);



	SetTxRightListen_Distance_0_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenDis[PDC_SNS_CH_RRS][0]);
	SetTxRightListen_Distance_1_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenDis[PDC_SNS_CH_RRS][1]);
	SetTxRightListen_Distance_2_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenDis[PDC_SNS_CH_RRS][2]);
	SetTxRightListen_Distance_3_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenDis[PDC_SNS_CH_RRS][3]);


	SetTxRightListen_EchoHigh_0_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenHeight[PDC_SNS_CH_RRS][0]);
	SetTxRightListen_EchoHigh_1_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenHeight[PDC_SNS_CH_RRS][1]);
	SetTxRightListen_EchoHigh_2_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenHeight[PDC_SNS_CH_RRS][2]);
	SetTxRightListen_EchoHigh_3_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenHeight[PDC_SNS_CH_RRS][3]);


	SetTxMasterEchoCnt_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8MasterEchoCnt[PDC_SNS_CH_RRS]);
	SetTxLeftEchoCnt_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8LeftListenEchoCnt[PDC_SNS_CH_RRS]);
	SetTxRightEchoCnt_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8RightListenEchoCnt[PDC_SNS_CH_RRS]);

	SetTxNFD_Flag_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8NFD_Flag[PDC_SNS_CH_RRS]);

	RRSImpOutputCnt++;
	if(RRSImpOutputCnt > 10)
	{
		RRSImpOutputCnt = 0;
	}

	if(RRSImpOutputCnt < 6)
	{
		SetTxNFD_Dis_6FA_ecu0_0(pSnsMeasData[SNS_RR_S].SnsDiagData.ImpAndImpIdx[0]);
	}
	else
	{
		SetTxNFD_Dis_6FA_ecu0_0(pSnsMeasData[SNS_RR_S].SnsDiagData.ImpAndImpIdx[1]);
	}
	//SetTxNFD_Dis_6FA_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16NFD_Dis[PDC_SNS_CH_RRS]);
	SetTxRing_Fre_6FA_ecu0_0(pSnsMeasData[SNS_RR_S].SnsDiagData.RFreq);
	SetTxNoiseCnt_6FA_ecu0_0(pSnsMeasData[SNS_RR_S].SnsDiagData.NoiseAbnormalCnt);
	SetTxNoiseSum_6FA_ecu0_0(pSnsMeasData[SNS_RR_S].SnsDiagData.NoiseSum); 
	
	for (i = 0;i < 64;i++)
	{
		GcComDataBuffer[ID6FAIdx][i] = RRS_EchoData_6FA_ecu0_0_pool.byte[i];
	}
	return;
}
void Com_TxCvt_RR_EchoData_6FB_ecu0_0(void)
{
	/*message buffer variable is RR_EchoData_6FB_ecu0_0_pool*/
	/*message access handle is COM_TX_FRM_RR_ECHODATA_6FB_ECU0_0_HANDLE*/
	uint8 i;	
	static uint8 RRImpOutputCnt = 0;
	const SnsMeasDistData_Str* pSnsMeasData = Elmos17SnsCtrl_GetSnsMeasDistData();
	//TODO: synchronize application signal value
	SetTxUpdateFlg_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8UpdateFlg[PDC_SNS_CH_RR]);
	SetTxSnsWorkSts_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].enuPDCSnsWorkSts[PDC_SNS_CH_RR]);
	SetTxMaster_Meas_Type_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].enuPDCSnsMeasType[PDC_SNS_CH_RR]);
	//SetTxEepromErrFlg_6FB_ecu0_0(EepromErrFlg[SNS_RR]);
	SetTxMaster_Rtm_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RingTime[PDC_SNS_CH_RR]);

	SetTxMaster_Distance_0_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterDis[PDC_SNS_CH_RR][0]);
	SetTxMaster_Distance_1_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterDis[PDC_SNS_CH_RR][1]);
	SetTxMaster_Distance_2_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterDis[PDC_SNS_CH_RR][2]);
	SetTxMaster_Distance_3_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterDis[PDC_SNS_CH_RR][3]);

	SetTxMaster_EchoHigh_0_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterHeight[PDC_SNS_CH_RR][0]);
	SetTxMaster_EchoHigh_1_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterHeight[PDC_SNS_CH_RR][1]);
	SetTxMaster_EchoHigh_2_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterHeight[PDC_SNS_CH_RR][2]);
	SetTxMaster_EchoHigh_3_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterHeight[PDC_SNS_CH_RR][3]);


	SetTxLeftListen_Distance_0_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenDis[PDC_SNS_CH_RR][0]);
	SetTxLeftListen_Distance_1_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenDis[PDC_SNS_CH_RR][1]);
	SetTxLeftListen_Distance_2_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenDis[PDC_SNS_CH_RR][2]);
	SetTxLeftListen_Distance_3_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenDis[PDC_SNS_CH_RR][3]);


	SetTxLeftListen_EchoHigh_0_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenHeight[PDC_SNS_CH_RR][0]);
	SetTxLeftListen_EchoHigh_1_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenHeight[PDC_SNS_CH_RR][1]);
	SetTxLeftListen_EchoHigh_2_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenHeight[PDC_SNS_CH_RR][2]);
	SetTxLeftListen_EchoHigh_3_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenHeight[PDC_SNS_CH_RR][3]);



	SetTxRightListen_Distance_0_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenDis[PDC_SNS_CH_RR][0]);
	SetTxRightListen_Distance_1_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenDis[PDC_SNS_CH_RR][1]);
	SetTxRightListen_Distance_2_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenDis[PDC_SNS_CH_RR][2]);
	SetTxRightListen_Distance_3_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenDis[PDC_SNS_CH_RR][3]);



	SetTxRightListen_EchoHigh_0_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenHeight[PDC_SNS_CH_RR][0]);
	SetTxRightListen_EchoHigh_1_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenHeight[PDC_SNS_CH_RR][1]);
	SetTxRightListen_EchoHigh_2_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenHeight[PDC_SNS_CH_RR][2]);
	SetTxRightListen_EchoHigh_3_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenHeight[PDC_SNS_CH_RR][3]);


	SetTxMasterEchoCnt_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8MasterEchoCnt[PDC_SNS_CH_RR]);
	SetTxLeftEchoCnt_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8LeftListenEchoCnt[PDC_SNS_CH_RR]);
	SetTxRightEchoCnt_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8RightListenEchoCnt[PDC_SNS_CH_RR]);

	SetTxNFD_Flag_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8NFD_Flag[PDC_SNS_CH_RR]);

	RRImpOutputCnt++;
	if(RRImpOutputCnt > 10)
	{
		RRImpOutputCnt = 0;
	}

	if(RRImpOutputCnt < 6)
	{
		SetTxNFD_Dis_6FB_ecu0_0(pSnsMeasData[SNS_RR].SnsDiagData.ImpAndImpIdx[0]);
	}
	else
	{
		SetTxNFD_Dis_6FB_ecu0_0(pSnsMeasData[SNS_RR].SnsDiagData.ImpAndImpIdx[1]);
	}
	//SetTxNFD_Dis_6FB_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16NFD_Dis[PDC_SNS_CH_RR]);
	SetTxRing_Fre_6FB_ecu0_0(pSnsMeasData[SNS_RR].SnsDiagData.RFreq);
	SetTxNoiseCnt_6FB_ecu0_0(pSnsMeasData[SNS_RR].SnsDiagData.NoiseAbnormalCnt);
	SetTxNoiseSum_6FB_ecu0_0(pSnsMeasData[SNS_RR].SnsDiagData.NoiseSum);
	
	for (i = 0;i < 64;i++)
	{
		GcComDataBuffer[ID6FBIdx][i] = RR_EchoData_6FB_ecu0_0_pool.byte[i];
	}
	return;
}
void Com_TxCvt_RMR_EchoData_6FC_ecu0_0(void)
{
	/*message buffer variable is RMR_EchoData_6FC_ecu0_0_pool*/
	/*message access handle is COM_TX_FRM_RMR_ECHODATA_6FC_ECU0_0_HANDLE*/
	uint8 i;	
	static uint8 RMRImpOutputCnt = 0;
	const SnsMeasDistData_Str* pSnsMeasData = Elmos17SnsCtrl_GetSnsMeasDistData();
	//TODO: synchronize application signal value
	SetTxUpdateFlg_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8UpdateFlg[PDC_SNS_CH_RMR]);
	SetTxSnsWorkSts_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].enuPDCSnsWorkSts[PDC_SNS_CH_RMR]);
	SetTxMaster_Meas_Type_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].enuPDCSnsMeasType[PDC_SNS_CH_RMR]);
	//SetTxEepromErrFlg_6FC_ecu0_0(EepromErrFlg[SNS_RMR]);
	SetTxMaster_Rtm_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RingTime[PDC_SNS_CH_RMR]);

	SetTxMaster_Distance_0_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterDis[PDC_SNS_CH_RMR][0]);
	SetTxMaster_Distance_1_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterDis[PDC_SNS_CH_RMR][1]);
	SetTxMaster_Distance_2_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterDis[PDC_SNS_CH_RMR][2]);
	SetTxMaster_Distance_3_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterDis[PDC_SNS_CH_RMR][3]);

	SetTxMaster_EchoHigh_0_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterHeight[PDC_SNS_CH_RMR][0]);
	SetTxMaster_EchoHigh_1_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterHeight[PDC_SNS_CH_RMR][1]);
	SetTxMaster_EchoHigh_2_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterHeight[PDC_SNS_CH_RMR][2]);
	SetTxMaster_EchoHigh_3_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterHeight[PDC_SNS_CH_RMR][3]);


	SetTxLeftListen_Distance_0_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenDis[PDC_SNS_CH_RMR][0]);
	SetTxLeftListen_Distance_1_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenDis[PDC_SNS_CH_RMR][1]);
	SetTxLeftListen_Distance_2_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenDis[PDC_SNS_CH_RMR][2]);
	SetTxLeftListen_Distance_3_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenDis[PDC_SNS_CH_RMR][3]);

	SetTxLeftListen_EchoHigh_0_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenHeight[PDC_SNS_CH_RMR][0]);
	SetTxLeftListen_EchoHigh_1_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenHeight[PDC_SNS_CH_RMR][1]);
	SetTxLeftListen_EchoHigh_2_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenHeight[PDC_SNS_CH_RMR][2]);
	SetTxLeftListen_EchoHigh_3_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenHeight[PDC_SNS_CH_RMR][3]);

	SetTxRightListen_Distance_0_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenDis[PDC_SNS_CH_RMR][0]);
	SetTxRightListen_Distance_1_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenDis[PDC_SNS_CH_RMR][1]);
	SetTxRightListen_Distance_2_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenDis[PDC_SNS_CH_RMR][2]);
	SetTxRightListen_Distance_3_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenDis[PDC_SNS_CH_RMR][3]);

	SetTxRightListen_EchoHigh_0_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenHeight[PDC_SNS_CH_RMR][0]);
	SetTxRightListen_EchoHigh_1_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenHeight[PDC_SNS_CH_RMR][1]);
	SetTxRightListen_EchoHigh_2_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenHeight[PDC_SNS_CH_RMR][2]);
	SetTxRightListen_EchoHigh_3_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenHeight[PDC_SNS_CH_RMR][3]);

	SetTxMasterEchoCnt_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8MasterEchoCnt[PDC_SNS_CH_RMR]);
	SetTxLeftEchoCnt_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8LeftListenEchoCnt[PDC_SNS_CH_RMR]);
	SetTxRightEchoCnt_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8RightListenEchoCnt[PDC_SNS_CH_RMR]);

	SetTxNFD_Flag_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8NFD_Flag[PDC_SNS_CH_RMR]);

	RMRImpOutputCnt++;
	if(RMRImpOutputCnt > 10)
	{
		RMRImpOutputCnt = 0;
	}

	if(RMRImpOutputCnt < 6)
	{
		SetTxNFD_Dis_6FC_ecu0_0(pSnsMeasData[SNS_RMR].SnsDiagData.ImpAndImpIdx[0]);
	}
	else
	{
		SetTxNFD_Dis_6FC_ecu0_0(pSnsMeasData[SNS_RMR].SnsDiagData.ImpAndImpIdx[1]);
	}
	//SetTxNFD_Dis_6FC_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16NFD_Dis[PDC_SNS_CH_RMR]);
	SetTxRing_Fre_6FC_ecu0_0(pSnsMeasData[SNS_RMR].SnsDiagData.RFreq);
	SetTxNoiseCnt_6FC_ecu0_0(pSnsMeasData[SNS_RMR].SnsDiagData.NoiseAbnormalCnt);
	SetTxNoiseSum_6FC_ecu0_0(pSnsMeasData[SNS_RMR].SnsDiagData.NoiseSum);
	
	for (i = 0;i < 64;i++)
	{
		GcComDataBuffer[ID6FCIdx][i] = RMR_EchoData_6FC_ecu0_0_pool.byte[i];
	}
	return;
}
void Com_TxCvt_RML_EchoData_6FD_ecu0_0(void)
{
	/*message buffer variable is RML_EchoData_6FD_ecu0_0_pool*/
	/*message access handle is COM_TX_FRM_RML_ECHODATA_6FD_ECU0_0_HANDLE*/
	uint8 i;	
	static uint8 RMLImpOutputCnt = 0;
	const SnsMeasDistData_Str* pSnsMeasData = Elmos17SnsCtrl_GetSnsMeasDistData();
	//TODO: synchronize application signal value
	SetTxUpdateFlg_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8UpdateFlg[PDC_SNS_CH_RML]);
	SetTxSnsWorkSts_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].enuPDCSnsWorkSts[PDC_SNS_CH_RML]);
	SetTxMaster_Meas_Type_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].enuPDCSnsMeasType[PDC_SNS_CH_RML]);
	//SetTxEepromErrFlg_6FD_ecu0_0(EepromErrFlg[SNS_RML]);
	SetTxMaster_Rtm_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RingTime[PDC_SNS_CH_RML]);

	SetTxMaster_Distance_0_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterDis[PDC_SNS_CH_RML][0]);
	SetTxMaster_Distance_1_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterDis[PDC_SNS_CH_RML][1]);
	SetTxMaster_Distance_2_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterDis[PDC_SNS_CH_RML][2]);
	SetTxMaster_Distance_3_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterDis[PDC_SNS_CH_RML][3]);


	SetTxMaster_EchoHigh_0_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterHeight[PDC_SNS_CH_RML][0]);
	SetTxMaster_EchoHigh_1_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterHeight[PDC_SNS_CH_RML][1]);
	SetTxMaster_EchoHigh_2_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterHeight[PDC_SNS_CH_RML][2]);
	SetTxMaster_EchoHigh_3_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterHeight[PDC_SNS_CH_RML][3]);


	SetTxLeftListen_Distance_0_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenDis[PDC_SNS_CH_RML][0]);
	SetTxLeftListen_Distance_1_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenDis[PDC_SNS_CH_RML][1]);
	SetTxLeftListen_Distance_2_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenDis[PDC_SNS_CH_RML][2]);
	SetTxLeftListen_Distance_3_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenDis[PDC_SNS_CH_RML][3]);


	SetTxLeftListen_EchoHigh_0_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenHeight[PDC_SNS_CH_RML][0]);
	SetTxLeftListen_EchoHigh_1_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenHeight[PDC_SNS_CH_RML][1]);
	SetTxLeftListen_EchoHigh_2_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenHeight[PDC_SNS_CH_RML][2]);
	SetTxLeftListen_EchoHigh_3_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenHeight[PDC_SNS_CH_RML][3]);



	SetTxRightListen_Distance_0_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenDis[PDC_SNS_CH_RML][0]);
	SetTxRightListen_Distance_1_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenDis[PDC_SNS_CH_RML][1]);
	SetTxRightListen_Distance_2_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenDis[PDC_SNS_CH_RML][2]);
	SetTxRightListen_Distance_3_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenDis[PDC_SNS_CH_RML][3]);


	SetTxRightListen_EchoHigh_0_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenHeight[PDC_SNS_CH_RML][0]);
	SetTxRightListen_EchoHigh_1_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenHeight[PDC_SNS_CH_RML][1]);
	SetTxRightListen_EchoHigh_2_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenHeight[PDC_SNS_CH_RML][2]);
	SetTxRightListen_EchoHigh_3_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenHeight[PDC_SNS_CH_RML][3]);


	SetTxMasterEchoCnt_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8MasterEchoCnt[PDC_SNS_CH_RML]);
	SetTxLeftEchoCnt_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8LeftListenEchoCnt[PDC_SNS_CH_RML]);
	SetTxRightEchoCnt_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8RightListenEchoCnt[PDC_SNS_CH_RML]);

	SetTxNFD_Flag_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8NFD_Flag[PDC_SNS_CH_RML]);

	RMLImpOutputCnt++;
	if(RMLImpOutputCnt > 10)
	{
		RMLImpOutputCnt = 0;
	}

	if(RMLImpOutputCnt < 6)
	{
		SetTxNFD_Dis_6FD_ecu0_0(pSnsMeasData[SNS_RML].SnsDiagData.ImpAndImpIdx[0]);
	}
	else
	{
		SetTxNFD_Dis_6FD_ecu0_0(pSnsMeasData[SNS_RML].SnsDiagData.ImpAndImpIdx[1]);
	}
	//SetTxNFD_Dis_6FD_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16NFD_Dis[PDC_SNS_CH_RML]);
	SetTxRing_Fre_6FD_ecu0_0(pSnsMeasData[SNS_RML].SnsDiagData.RFreq);
	SetTxNoiseCnt_6FD_ecu0_0(pSnsMeasData[SNS_RML].SnsDiagData.NoiseAbnormalCnt);
	SetTxNoiseSum_6FD_ecu0_0(pSnsMeasData[SNS_RML].SnsDiagData.NoiseSum);
	
	for (i = 0;i < 64;i++)
	{
		GcComDataBuffer[ID6FDIdx][i] = RML_EchoData_6FD_ecu0_0_pool.byte[i];
	}
	return;
}
void Com_TxCvt_RL_EchoData_6FE_ecu0_0(void)
{
	/*message buffer variable is RL_EchoData_6FE_ecu0_0_pool*/
	/*message access handle is COM_TX_FRM_RL_ECHODATA_6FE_ECU0_0_HANDLE*/
	uint8 i;	
	static uint8 RLImpOutputCnt = 0;
	const SnsMeasDistData_Str* pSnsMeasData = Elmos17SnsCtrl_GetSnsMeasDistData();
	//TODO: synchronize application signal value
	SetTxUpdateFlg_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8UpdateFlg[PDC_SNS_CH_RL]);
	SetTxSnsWorkSts_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].enuPDCSnsWorkSts[PDC_SNS_CH_RL]);
	SetTxMaster_Meas_Type_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].enuPDCSnsMeasType[PDC_SNS_CH_RL]);
	//SetTxEepromErrFlg_6FE_ecu0_0(EepromErrFlg[SNS_RL]);
	SetTxMaster_Rtm_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RingTime[PDC_SNS_CH_RL]);

	SetTxMaster_Distance_0_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterDis[PDC_SNS_CH_RL][0]);
	SetTxMaster_Distance_1_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterDis[PDC_SNS_CH_RL][1]);
	SetTxMaster_Distance_2_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterDis[PDC_SNS_CH_RL][2]);
	SetTxMaster_Distance_3_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterDis[PDC_SNS_CH_RL][3]);


	SetTxMaster_EchoHigh_0_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterHeight[PDC_SNS_CH_RL][0]);
	SetTxMaster_EchoHigh_1_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterHeight[PDC_SNS_CH_RL][1]);
	SetTxMaster_EchoHigh_2_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterHeight[PDC_SNS_CH_RL][2]);
	SetTxMaster_EchoHigh_3_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterHeight[PDC_SNS_CH_RL][3]);


	SetTxLeftListen_Distance_0_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenDis[PDC_SNS_CH_RL][0]);
	SetTxLeftListen_Distance_1_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenDis[PDC_SNS_CH_RL][1]);
	SetTxLeftListen_Distance_2_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenDis[PDC_SNS_CH_RL][2]);
	SetTxLeftListen_Distance_3_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenDis[PDC_SNS_CH_RL][3]);

	SetTxLeftListen_EchoHigh_0_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenHeight[PDC_SNS_CH_RL][0]);
	SetTxLeftListen_EchoHigh_1_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenHeight[PDC_SNS_CH_RL][1]);
	SetTxLeftListen_EchoHigh_2_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenHeight[PDC_SNS_CH_RL][2]);
	SetTxLeftListen_EchoHigh_3_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenHeight[PDC_SNS_CH_RL][3]);

	SetTxRightListen_Distance_0_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenDis[PDC_SNS_CH_RL][0]);
	SetTxRightListen_Distance_1_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenDis[PDC_SNS_CH_RL][1]);
	SetTxRightListen_Distance_2_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenDis[PDC_SNS_CH_RL][2]);
	SetTxRightListen_Distance_3_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenDis[PDC_SNS_CH_RL][3]);

	SetTxRightListen_EchoHigh_0_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenHeight[PDC_SNS_CH_RL][0]);
	SetTxRightListen_EchoHigh_1_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenHeight[PDC_SNS_CH_RL][1]);
	SetTxRightListen_EchoHigh_2_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenHeight[PDC_SNS_CH_RL][2]);
	SetTxRightListen_EchoHigh_3_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenHeight[PDC_SNS_CH_RL][3]);

	SetTxMasterEchoCnt_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8MasterEchoCnt[PDC_SNS_CH_RL]);
	SetTxLeftEchoCnt_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8LeftListenEchoCnt[PDC_SNS_CH_RL]);
	SetTxRightEchoCnt_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8RightListenEchoCnt[PDC_SNS_CH_RL]);

	SetTxNFD_Flag_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8NFD_Flag[PDC_SNS_CH_RL]);

	RLImpOutputCnt++;
	if(RLImpOutputCnt > 10)
	{
		RLImpOutputCnt = 0;
	}

	if(RLImpOutputCnt < 6)
	{
		SetTxNFD_Dis_6FE_ecu0_0(pSnsMeasData[SNS_RL].SnsDiagData.ImpAndImpIdx[0]);
	}
	else
	{
		SetTxNFD_Dis_6FE_ecu0_0(pSnsMeasData[SNS_RL].SnsDiagData.ImpAndImpIdx[1]);
	}
	//SetTxNFD_Dis_6FE_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16NFD_Dis[PDC_SNS_CH_RL]);
	SetTxRing_Fre_6FE_ecu0_0(pSnsMeasData[SNS_RL].SnsDiagData.RFreq);
	SetTxNoiseCnt_6FE_ecu0_0(pSnsMeasData[SNS_RL].SnsDiagData.NoiseAbnormalCnt);
	SetTxNoiseSum_6FE_ecu0_0(pSnsMeasData[SNS_RL].SnsDiagData.NoiseSum);
	
	for (i = 0;i < 64;i++)
	{
		GcComDataBuffer[ID6FEIdx][i] = RL_EchoData_6FE_ecu0_0_pool.byte[i];
	}
	return;
}
void Com_TxCvt_RLS_EchoData_6FF_ecu0_0(void)
{
	/*message buffer variable is RLS_EchoData_6FF_ecu0_0_pool*/
	/*message access handle is COM_TX_FRM_RLS_ECHODATA_6FF_ECU0_0_HANDLE*/
	uint8 i;	
	static uint8 RLSImpOutputCnt = 0;
	const SnsMeasDistData_Str* pSnsMeasData = Elmos17SnsCtrl_GetSnsMeasDistData();
	//TODO: synchronize application signal value
	SetTxUpdateFlg_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8UpdateFlg[PDC_SNS_CH_RLS]);
	SetTxSnsWorkSts_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].enuPDCSnsWorkSts[PDC_SNS_CH_RLS]);
	SetTxMaster_Meas_Type_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].enuPDCSnsMeasType[PDC_SNS_CH_RLS]);
	//SetTxEepromErrFlg_6FF_ecu0_0(EepromErrFlg[SNS_RL_S]);
	SetTxMaster_Rtm_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RingTime[PDC_SNS_CH_RLS]);

	SetTxMaster_Distance_0_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterDis[PDC_SNS_CH_RLS][0]);
	SetTxMaster_Distance_1_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterDis[PDC_SNS_CH_RLS][1]);
	SetTxMaster_Distance_2_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterDis[PDC_SNS_CH_RLS][2]);
	SetTxMaster_Distance_3_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterDis[PDC_SNS_CH_RLS][3]);


	SetTxMaster_EchoHigh_0_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterHeight[PDC_SNS_CH_RLS][0]);
	SetTxMaster_EchoHigh_1_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterHeight[PDC_SNS_CH_RLS][1]);
	SetTxMaster_EchoHigh_2_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterHeight[PDC_SNS_CH_RLS][2]);
	SetTxMaster_EchoHigh_3_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16MasterHeight[PDC_SNS_CH_RLS][3]);


	SetTxLeftListen_Distance_0_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenDis[PDC_SNS_CH_RLS][0]);
	SetTxLeftListen_Distance_1_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenDis[PDC_SNS_CH_RLS][1]);
	SetTxLeftListen_Distance_2_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenDis[PDC_SNS_CH_RLS][2]);
	SetTxLeftListen_Distance_3_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenDis[PDC_SNS_CH_RLS][3]);


	SetTxLeftListen_EchoHigh_0_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenHeight[PDC_SNS_CH_RLS][0]);
	SetTxLeftListen_EchoHigh_1_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenHeight[PDC_SNS_CH_RLS][1]);
	SetTxLeftListen_EchoHigh_2_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenHeight[PDC_SNS_CH_RLS][2]);
	SetTxLeftListen_EchoHigh_3_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16LeftListenHeight[PDC_SNS_CH_RLS][3]);




	SetTxRightListen_Distance_0_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenDis[PDC_SNS_CH_RLS][0]);
	SetTxRightListen_Distance_1_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenDis[PDC_SNS_CH_RLS][1]);
	SetTxRightListen_Distance_2_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenDis[PDC_SNS_CH_RLS][2]);
	SetTxRightListen_Distance_3_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenDis[PDC_SNS_CH_RLS][3]);



	SetTxRightListen_EchoHigh_0_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenHeight[PDC_SNS_CH_RLS][0]);
	SetTxRightListen_EchoHigh_1_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenHeight[PDC_SNS_CH_RLS][1]);
	SetTxRightListen_EchoHigh_2_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenHeight[PDC_SNS_CH_RLS][2]);
	SetTxRightListen_EchoHigh_3_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16RightListenHeight[PDC_SNS_CH_RLS][3]);


	SetTxMasterEchoCnt_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8MasterEchoCnt[PDC_SNS_CH_RLS]);
	SetTxLeftEchoCnt_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8LeftListenEchoCnt[PDC_SNS_CH_RLS]);
	SetTxRightEchoCnt_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8RightListenEchoCnt[PDC_SNS_CH_RLS]);

	SetTxNFD_Flag_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u8NFD_Flag[PDC_SNS_CH_RLS]);

	RLSImpOutputCnt++;
	if(RLSImpOutputCnt > 10)
	{
		RLSImpOutputCnt = 0;
	}

	if(RLSImpOutputCnt < 6)
	{
		SetTxNFD_Dis_6FF_ecu0_0(pSnsMeasData[SNS_RL_S].SnsDiagData.ImpAndImpIdx[0]);
	}
	else
	{
		SetTxNFD_Dis_6FF_ecu0_0(pSnsMeasData[SNS_RL_S].SnsDiagData.ImpAndImpIdx[1]);
	}
	//SetTxNFD_Dis_6FF_ecu0_0(GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].u16NFD_Dis[PDC_SNS_CH_RLS]);
	SetTxRing_Fre_6FF_ecu0_0(pSnsMeasData[SNS_RL_S].SnsDiagData.RFreq);
	SetTxNoiseCnt_6FF_ecu0_0(pSnsMeasData[SNS_RL_S].SnsDiagData.NoiseAbnormalCnt);
	SetTxNoiseSum_6FF_ecu0_0(pSnsMeasData[SNS_RL_S].SnsDiagData.NoiseSum);
	
	for (i = 0;i < 64;i++)
	{
		GcComDataBuffer[ID6FFIdx][i] = RLS_EchoData_6FF_ecu0_0_pool.byte[i];
	}
	return;
}

/************************************************************************/
void DebugMsgTxTrigger_6EE(void)
{
	Com_TxCvt_Sns_ImpData_6EE_ecu0_0();
	NCS_SET_BIT(GcFrameStatus[ID6EEIdx], COM_FRAME_STATUS_TX_REQ, uint8);
}


/************************************************************************/
void DebugMsgTxTrigger_6EF(void)
{
	Com_TxCvt_AK2Upper_LabData_6EF_ecu0_0();
	NCS_SET_BIT(GcFrameStatus[ID6EFIdx], COM_FRAME_STATUS_TX_REQ, uint8);
}

/************************************************************************/
void DebugMsgTxTrigger_6F1(void)
{
	Com_TxCvt_AK2Upper_Mcu2Pc_6F1_ecu0_0();
	NCS_SET_BIT(GcFrameStatus[ID6F1Idx], COM_FRAME_STATUS_TX_REQ, uint8);
}
/************************************************************************/
void DebugMsgTxTrigger_6F2(void)
{
	Com_TxCvt_Radar_EchoData_6F2_ecu0_0();
	NCS_SET_BIT(GcFrameStatus[ID6F2Idx], COM_FRAME_STATUS_TX_REQ, uint8);
	//COM_SendDebugFrame(DEBUG_FRAME_ID_6F2, 0x0Du, Radar_EchoData_6F2_ecu0_0_pool.byte, FRAME_ID_6F2);
}
/************************************************************************/
void DebugMsgTxTrigger_6F3(void)
{
	Com_TxCvt_VehicelInf_6F3_ecu0_0();
	//COM_SendDebugFrame(DEBUG_FRAME_ID_6F3, 0x9u, VehicelInf_6F3_ecu0_0_pool.byte, FRAME_ID_6F3);
	NCS_SET_BIT(GcFrameStatus[ID6F3Idx], COM_FRAME_STATUS_TX_REQ, uint8);
}
/************************************************************************/
void DebugMsgTxTrigger_6F4(void)
{
	Com_TxCvt_FLS_EchoData_6F4_ecu0_0();
	//COM_SendDebugFrame(DEBUG_FRAME_ID_6F4, 0xFu, FLS_EchoData_6F4_ecu0_0_pool.byte,FRAME_ID_6F4);
	NCS_SET_BIT(GcFrameStatus[ID6F4Idx], COM_FRAME_STATUS_TX_REQ, uint8);
}
/***********************************************************************/
void DebugMsgTxTrigger_6F5(void)
{
	Com_TxCvt_FL_EchoData_6F5_ecu0_0();
	//COM_SendDebugFrame(DEBUG_FRAME_ID_6F5, 0xFu, FL_EchoData_6F5_ecu0_0_pool.byte, FRAME_ID_6F5);
	NCS_SET_BIT(GcFrameStatus[ID6F5Idx], COM_FRAME_STATUS_TX_REQ, uint8);
}
/***********************************************************************/
void DebugMsgTxTrigger_6F6(void)
{
	Com_TxCvt_FML_EchoData_6F6_ecu0_0();
	//COM_SendDebugFrame(DEBUG_FRAME_ID_6F6, 0xFu, FML_EchoData_6F6_ecu0_0_pool.byte, FRAME_ID_6F6);
	NCS_SET_BIT(GcFrameStatus[ID6F6Idx], COM_FRAME_STATUS_TX_REQ, uint8);
}
/***********************************************************************/
void DebugMsgTxTrigger_6F7(void)
{
	Com_TxCvt_FMR_EchoData_6F7_ecu0_0();
	//COM_SendDebugFrame(DEBUG_FRAME_ID_6F7, 0xFu, FMR_EchoData_6F7_ecu0_0_pool.byte, FRAME_ID_6F7);
	NCS_SET_BIT(GcFrameStatus[ID6F7Idx], COM_FRAME_STATUS_TX_REQ, uint8);
}
/***********************************************************************/
void DebugMsgTxTrigger_6F8(void)
{
	Com_TxCvt_FR_EchoData_6F8_ecu0_0();
	//COM_SendDebugFrame(DEBUG_FRAME_ID_6F8, 0xFu, FR_EchoData_6F8_ecu0_0_pool.byte, FRAME_ID_6F8);
	NCS_SET_BIT(GcFrameStatus[ID6F8Idx], COM_FRAME_STATUS_TX_REQ, uint8);
}
/***********************************************************************/
void DebugMsgTxTrigger_6F9(void)
{
	Com_TxCvt_FRS_EchoData_6F9_ecu0_0();
	//COM_SendDebugFrame(DEBUG_FRAME_ID_6F9, 0xFu, FRS_EchoData_6F9_ecu0_0_pool.byte, FRAME_ID_6F9);
	NCS_SET_BIT(GcFrameStatus[ID6F9Idx], COM_FRAME_STATUS_TX_REQ, uint8);
}
/***********************************************************************/
void DebugMsgTxTrigger_6FA(void)
{
	Com_TxCvt_RRS_EchoData_6FA_ecu0_0();
	//COM_SendDebugFrame(DEBUG_FRAME_ID_6FA, 0xFu, RRS_EchoData_6FA_ecu0_0_pool.byte, FRAME_ID_6FA);
	NCS_SET_BIT(GcFrameStatus[ID6FAIdx], COM_FRAME_STATUS_TX_REQ, uint8);
}
/***********************************************************************/
void DebugMsgTxTrigger_6FB(void)
{
	Com_TxCvt_RR_EchoData_6FB_ecu0_0();
	//COM_SendDebugFrame(DEBUG_FRAME_ID_6FB, 0xFu, RR_EchoData_6FB_ecu0_0_pool.byte, FRAME_ID_6FB);
	NCS_SET_BIT(GcFrameStatus[ID6FBIdx], COM_FRAME_STATUS_TX_REQ, uint8);
}
/***********************************************************************/
void DebugMsgTxTrigger_6FC(void)
{
	Com_TxCvt_RMR_EchoData_6FC_ecu0_0();
	//COM_SendDebugFrame(DEBUG_FRAME_ID_6FC, 0xFu, RMR_EchoData_6FC_ecu0_0_pool.byte, FRAME_ID_6FC);
	NCS_SET_BIT(GcFrameStatus[ID6FCIdx], COM_FRAME_STATUS_TX_REQ, uint8);
}
/***********************************************************************/
void DebugMsgTxTrigger_6FD(void)
{
	Com_TxCvt_RML_EchoData_6FD_ecu0_0();
	//COM_SendDebugFrame(DEBUG_FRAME_ID_6FD, 0xFu, RML_EchoData_6FD_ecu0_0_pool.byte, FRAME_ID_6FD);
	NCS_SET_BIT(GcFrameStatus[ID6FDIdx], COM_FRAME_STATUS_TX_REQ, uint8);
}
/***********************************************************************/
void DebugMsgTxTrigger_6FE(void)
{
	Com_TxCvt_RL_EchoData_6FE_ecu0_0();
	//COM_SendDebugFrame(DEBUG_FRAME_ID_6FE, 0xFu, RL_EchoData_6FE_ecu0_0_pool.byte, FRAME_ID_6FE);
	NCS_SET_BIT(GcFrameStatus[ID6FEIdx], COM_FRAME_STATUS_TX_REQ, uint8);
}
/***********************************************************************/
void DebugMsgTxTrigger_6FF(void)
{
	Com_TxCvt_RLS_EchoData_6FF_ecu0_0();
	//COM_SendDebugFrame(DEBUG_FRAME_ID_6FF, 0xFu, RLS_EchoData_6FF_ecu0_0_pool.byte, FRAME_ID_6FF);
	NCS_SET_BIT(GcFrameStatus[ID6FFIdx], COM_FRAME_STATUS_TX_REQ, uint8);
}
#endif
/*END DebugSignalManage.c******************************************************/
