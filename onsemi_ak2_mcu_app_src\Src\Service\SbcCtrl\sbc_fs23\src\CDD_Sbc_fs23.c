/*==================================================================================================
*   Project              : RTD AUTOSAR 4.4
*   Platform             : CORTEXM
*   Peripheral           : Sbc_fs23
*   Dependencies         : 
*
*   Autosar Version      : 4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : 0.8.0
*   Build Version        : S32K3_RTD_0_8_0_D2303_ASR_REL_4_4_REV_0000_20230310
*
*   (c) Copyright 2020 - 2022 NXP Semiconductors
*   All Rights Reserved.
*
*   NXP Confidential. This software is owned or controlled by NXP and may only be
*   used strictly in accordance with the applicable license terms. By expressly
*   accepting such terms or by downloading, installing, activating and/or otherwise
*   using the software, you are agreeing that you have read, and that you agree to
*   comply with and are bound by, such license terms. If you do not agree to be
*   bound by the applicable license terms, then you may not retain, install,
*   activate or otherwise use the software.
==================================================================================================*/

/**
*   @file CDD_Sbc_fs23.c
*
*   @addtogroup CDD_Sbc_fs23 driver
*   @{
*/

#ifdef __cplusplus
extern "C"{
#endif


/*==================================================================================================
*                                          INCLUDE FILES
* 1) system and project includes
* 2) needed interfaces from external units
* 3) internal and external interfaces from this unit
==================================================================================================*/
#include "CDD_Sbc_fs23.h"
//#include "SchM_Sbc_fs23.h"
#include "TimerManage.h"
#include "DMADrv.h"
#include "CDD_Sbc_fs23_BOARD_InitPeripherals_PBcfg.h"

/*==================================================================================================
*                           LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/
/**
* @brief Command type.
*/
typedef enum
{
    SBC_FS23_COM_READ  = 0x0U, /**< Register Read */
    SBC_FS23_COM_WRITE = 0x1U  /**< Register Write */
} Sbc_fs23_CommandType;

/**
* @brief Structure representing transmit Data frame.
*/
typedef struct
{
    uint8 RegisterAddress;
    /** @brief Command type (R/W). */
    Sbc_fs23_CommandType CommandType;
    /** @brief Data to be written to the register.
    *   If CommandType is "read", this value will be ignored. */
    uint16 WriteData;
} Sbc_fs23_TxFrameType;

/*==================================================================================================
*                                          LOCAL MACROS
==================================================================================================*/
/* Data frame macros. */
/* CRC macros used for communication. */
/**
* @brief Size of CRC table.
*/
#define SBC_FS23_CRC_TBL_SIZE                    (256U)
/**
* @brief CRC initial value.
*/
#define SBC_FS23_COM_CRC_INIT                    ((uint8)0xFFU)

/**
* @brief CRC initial value.
*/
#define SBC_FS23_COM_CRC_POLY                    ((uint8)0x1DU)

/**
* @brief Register address mask.
*/
#define SBC_FS23_REG_ADDR_MASK                   ((uint8)0x7FU)
/**
* @brief Command type field mask.
*/
#define SBC_FS23_COMMAND_TYPE_MASK               ((uint8)0x01U)

#if (SBC_FS23_SPI_TRANSMIT == SBC_FS23_COMM_METHOD)
/**
* @brief Length of the communication frame.
*/
#define SBC_FS23_COMM_FRAME_SIZE                 ((uint8)0x04U)
/**
* @brief SPI register address shift.
*/
#define SBC_FS23_REG_ADDR_SHIFT                  ((uint8)0x01U)

#else
/**
* @brief Length of the communication frame.
*/
#define SBC_FS23_COMM_FRAME_SIZE                 ((uint8)0x05U)
/**
* @brief SPI register address shift.
*/
#define SBC_FS23_REG_ADDR_SHIFT                  ((uint8)0x00U)

#define SBC_FS23_I2C_WRITE_FRAME_DATA_SIZE       ((uint8)0x04U)

#define SBC_FS23_I2C_READ_TX_FRAME_DATA_SIZE     ((uint8)0x01U)

#define SBC_FS23_I2C_READ_RX_FRAME_DATA_SIZE     ((uint8)0x03U)

#endif

#define SBC_FS23_RWBITS_MASK                     ((uint16)0xFFFFU)

#define SBC_FS23_INIT_REGISTERS_CRC_LENGTH       ((uint8)8U)

#define SBC_FS23_SPI_TIMEOUT				     ((uint8)5U)

/*==================================================================================================
*                                         LOCAL CONSTANTS
==================================================================================================*/
#define SBC_FS23_START_SEC_CONST_8
/**
* @brief Include Memory mapping specification
*/
//#include "Sbc_fs23_MemMap.h"

/**
* @brief CRC lookup table.
*/
static const uint8 SBC_FS23_CRC_TABLE[SBC_FS23_CRC_TBL_SIZE] =
{
    0x00U, 0x1DU, 0x3AU, 0x27U, 0x74U, 0x69U, 0x4EU, 0x53U, 0xE8U, 0xF5U, 0xD2U, 0xCFU, 0x9CU,
    0x81U, 0xA6U, 0xBBU, 0xCDU, 0xD0U, 0xF7U, 0xEAU, 0xB9U, 0xA4U, 0x83U, 0x9EU, 0x25U, 0x38U,
    0x1FU, 0x02U, 0x51U, 0x4CU, 0x6BU, 0x76U, 0x87U, 0x9AU, 0xBDU, 0xA0U, 0xF3U, 0xEEU, 0xC9U,
    0xD4U, 0x6FU, 0x72U, 0x55U, 0x48U, 0x1BU, 0x06U, 0x21U, 0x3CU, 0x4AU, 0x57U, 0x70U, 0x6DU,
    0x3EU, 0x23U, 0x04U, 0x19U, 0xA2U, 0xBFU, 0x98U, 0x85U, 0xD6U, 0xCBU, 0xECU, 0xF1U, 0x13U,
    0x0EU, 0x29U, 0x34U, 0x67U, 0x7AU, 0x5DU, 0x40U, 0xFBU, 0xE6U, 0xC1U, 0xDCU, 0x8FU, 0x92U,
    0xB5U, 0xA8U, 0xDEU, 0xC3U, 0xE4U, 0xF9U, 0xAAU, 0xB7U, 0x90U, 0x8DU, 0x36U, 0x2BU, 0x0CU,
    0x11U, 0x42U, 0x5FU, 0x78U, 0x65U, 0x94U, 0x89U, 0xAEU, 0xB3U, 0xE0U, 0xFDU, 0xDAU, 0xC7U,
    0x7CU, 0x61U, 0x46U, 0x5BU, 0x08U, 0x15U, 0x32U, 0x2FU, 0x59U, 0x44U, 0x63U, 0x7EU, 0x2DU,
    0x30U, 0x17U, 0x0AU, 0xB1U, 0xACU, 0x8BU, 0x96U, 0xC5U, 0xD8U, 0xFFU, 0xE2U, 0x26U, 0x3BU,
    0x1CU, 0x01U, 0x52U, 0x4FU, 0x68U, 0x75U, 0xCEU, 0xD3U, 0xF4U, 0xE9U, 0xBAU, 0xA7U, 0x80U,
    0x9DU, 0xEBU, 0xF6U, 0xD1U, 0xCCU, 0x9FU, 0x82U, 0xA5U, 0xB8U, 0x03U, 0x1EU, 0x39U, 0x24U,
    0x77U, 0x6AU, 0x4DU, 0x50U, 0xA1U, 0xBCU, 0x9BU, 0x86U, 0xD5U, 0xC8U, 0xEFU, 0xF2U, 0x49U,
    0x54U, 0x73U, 0x6EU, 0x3DU, 0x20U, 0x07U, 0x1AU, 0x6CU, 0x71U, 0x56U, 0x4BU, 0x18U, 0x05U,
    0x22U, 0x3FU, 0x84U, 0x99U, 0xBEU, 0xA3U, 0xF0U, 0xEDU, 0xCAU, 0xD7U, 0x35U, 0x28U, 0x0FU,
    0x12U, 0x41U, 0x5CU, 0x7BU, 0x66U, 0xDDU, 0xC0U, 0xE7U, 0xFAU, 0xA9U, 0xB4U, 0x93U, 0x8EU,
    0xF8U, 0xE5U, 0xC2U, 0xDFU, 0x8CU, 0x91U, 0xB6U, 0xABU, 0x10U, 0x0DU, 0x2AU, 0x37U, 0x64U,
    0x79U, 0x5EU, 0x43U, 0xB2U, 0xAFU, 0x88U, 0x95U, 0xC6U, 0xDBU, 0xFCU, 0xE1U, 0x5AU, 0x47U,
    0x60U, 0x7DU, 0x2EU, 0x33U, 0x14U, 0x09U, 0x7FU, 0x62U, 0x45U, 0x58U, 0x0BU, 0x16U, 0x31U,
    0x2CU, 0x97U, 0x8AU, 0xADU, 0xB0U, 0xE3U, 0xFEU, 0xD9U, 0xC4U
};

#define SBC_FS23_STOP_SEC_CONST_8
/**
* @brief Include Memory mapping specification
*/
//#include "Sbc_fs23_MemMap.h"
/*==================================================================================================
*                                         LOCAL VARIABLES
==================================================================================================*/
#define SBC_FS23_START_SEC_VAR_CLEARED_UNSPECIFIED
//#include "Sbc_fs23_MemMap.h"
/**
* @brief Local copy of the pointer to the configuration Data.
*/
static const Sbc_fs23_ConfigType* Sbc_fs23_pConfigPtr = NULL_PTR;

#if (STD_OFF == SBC_FS23_DISABLE_DEM_REPORT_ERROR_STATUS)
/**
* @brief          DEM errors configuration pointer.
* @details        This is used to report DEM errors in the Wdg external driver.
*/
const Sbc_fs23_DemConfigType * Sbc_fs23_pDemConfigPtr;
#endif/*STD_OFF == SBC_FS23_DISABLE_DEM_REPORT_ERROR_STATUS*/

#define SBC_FS23_STOP_SEC_VAR_CLEARED_UNSPECIFIED
//#include "Sbc_fs23_MemMap.h"

#define SBC_FS23_START_SEC_VAR_CLEARED_BOOLEAN
//#include "Sbc_fs23_MemMap.h"
/**
* @brief Initialization flag, FALSE = Sbc_fs23_Init function was not called yet.
*/
static boolean Sbc_fs23_IsInitialized = FALSE;

/**
* @brief    Variable used to check watchdog trigger in progress or not.
*           When Wdg_trigger is in-progress, variable will set true.
*/
static boolean Sbc_fs23_bIsWdgTrigger = FALSE;

#define SBC_FS23_STOP_SEC_VAR_CLEARED_BOOLEAN
//#include "Sbc_fs23_MemMap.h"

#define SBC_FS23_START_SEC_VAR_INIT_BOOLEAN
//#include "Sbc_fs23_MemMap.h"
/**
* @brief    Variable used to indicate watchdog trigger will be forwarded to the device
*/
static boolean Sbc_fs23_bGateWdgTrigger = TRUE;
#define SBC_FS23_STOP_SEC_VAR_INIT_BOOLEAN
//#include "Sbc_fs23_MemMap.h"
/*==================================================================================================
*                                        GLOBAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                        GLOBAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                    LOCAL FUNCTION PROTOTYPES
==================================================================================================*/
#define SBC_FS23_START_SEC_CODE
//#include "Sbc_fs23_MemMap.h"
static uint8 Sbc_fs23_CalcCrc(const uint8* Data, uint8 DataLen);

static uint8 Sbc_fs23_CalcInitRegistersCrc(const uint64 Data);

static Std_ReturnType Sbc_fs23_CheckCrc(const uint8* Data, uint8 DataLen);

static void Sbc_fs23_CreateSendFrame(const Sbc_fs23_TxFrameType* TxData, uint8* TxFrame);

static Std_ReturnType Sbc_fs23_TransferData(const Sbc_fs23_TxFrameType* TxData, uint16* RxData);

static Std_ReturnType Sbc_fs23_CheckParameterFsxbRelease(const Sbc_fs23_FsOutputType FsOutput);

static Std_ReturnType Sbc_fs23_CheckParameterRequestOutput(const Sbc_fs23_FsRequestType FsRequest);

static Std_ReturnType Sbc_fs23_WriteInitCrcRegister(void);

static Std_ReturnType Sbc_fs23_CheckInitState(void);

static Std_ReturnType Sbc_fs23_InitMain(void);

static Std_ReturnType Sbc_fs23_InitFailSafe(void);

static Std_ReturnType Sbc_fs23_TimeWaitRelease(Sbc_fs23_FsOutputType FsOutput);

static Std_ReturnType Sbc_fs23_WdReadToken(uint16* WdToken);

static Std_ReturnType Sbc_fs23_DebugModeExit(void);

static Std_ReturnType Sbc_fs23_CalculateReleaseFsxb(Sbc_fs23_FsOutputType FsOutput, uint16 *OutputData);

static Std_ReturnType Sbc_fs23_TimeWaitClearFault(const uint32 TimeoutValueUs);

static Std_ReturnType Sbc_fs23_ClearFaultErrorCounter(void);

static Std_ReturnType Sbc_fs23_WaitInitClosed(void);

static Std_ReturnType Sbc_fs23_CheckWdgRefresh(void);

static Std_ReturnType Sbc_fs23_GetIoWuStatus(Sbc_fs23_WakeupReasonType * WakeupReason);

static Std_ReturnType Sbc_fs23_GetWu1Status(Sbc_fs23_WakeupReasonType * WakeupReason);

static Std_ReturnType Sbc_fs23_CheckWdWindowDuration(const Sbc_fs23_WdWindowType WdWindow);

#if (STD_ON == SBC_FS23_SET_AMUX_API)
static Std_ReturnType Sbc_fs23_CheckParameterSetAmux(const Sbc_fs23_AmuxChannelType AmuxChannel, const Sbc_fs23_AmuxDivType AmuxDiv);
#endif
/*==================================================================================================
*                                         LOCAL FUNCTIONS
==================================================================================================*/
/* This function calculates CRC value of passed data array. */
static uint8 Sbc_fs23_CalcCrc(const uint8* Data, uint8 DataLen)
{
    uint8 u8Crc;      /* Result. */
    uint8 u8TableIdx; /* Index to the CRC table. */
    uint8 u8DataIdx;  /* Index to the data array (memory). */

    /* Set CRC token value. */
    u8Crc = SBC_FS23_COM_CRC_INIT;

    /* Calculate CRC. */
    for (u8DataIdx = (DataLen-1U); u8DataIdx > 0; u8DataIdx--)
    {
        u8TableIdx = u8Crc ^ Data[u8DataIdx];
        u8Crc = SBC_FS23_CRC_TABLE[u8TableIdx];
    }

    return u8Crc;
}

static uint8 Sbc_fs23_CalcInitRegistersCrc(const uint64 Data)
{
    uint8 u8Crc = SBC_FS23_COM_CRC_INIT;              /* Result. */
    uint8 u8CountValue = 0U;
    uint64 u64Temp = 0U;

    /*Reverse data bits*/
    for (u8CountValue = 0U; u8CountValue < 64U; u8CountValue++)
    {
        u64Temp |= (uint64)(((Data >> (63U - u8CountValue)) & 1U) << u8CountValue);
    }
    /* Perform CRC calculation*/
    for (u8CountValue = 0U; u8CountValue < 58U; u8CountValue++)
    {
        if ((((uint64)u8Crc ^ (u64Temp << 7U)) & (uint64)0x80U) > 0U)
        {
            u8Crc <<= 1U;
            u8Crc ^= SBC_FS23_COM_CRC_POLY;
        }
        else
        {
            u8Crc <<= 1U;
        }
        u64Temp >>= 1U;
    }

    return u8Crc;
}

/* Performs CRC check of the data array. */
static Std_ReturnType Sbc_fs23_CheckCrc(const uint8* Data, uint8 DataLen)
{
    Std_ReturnType ReturnValue = E_OK;
    uint8 u8FrameCrc; /* CRC value from resp. */
    uint8 u8CompCrc;  /* Computed CRC value. */

    if(DataLen > 0U)
    {
        /* Check CRC. */
        u8FrameCrc = Data[0];
        u8CompCrc = Sbc_fs23_CalcCrc(&Data[0], DataLen);
        if (u8CompCrc != u8FrameCrc)
        {
            ReturnValue = (Std_ReturnType)E_NOT_OK;
        }
    }
    else
    {
        ReturnValue = (Std_ReturnType)E_NOT_OK;
    }

    return ReturnValue;
}

/* Creates a raw frame for SPI/I2c transfer. */
static void Sbc_fs23_CreateSendFrame(const Sbc_fs23_TxFrameType* TxData, uint8* TxFrame)
{
    /* Sets address of the register. */
    TxFrame[3] = (uint8)((TxData->RegisterAddress) << 1U);
    /* check if register is r or w */
    switch (TxData->CommandType)
    {
        case SBC_FS23_COM_READ:
            TxFrame[3] &= (~SBC_FS23_COMMAND_TYPE_MASK);
            TxFrame[2] = 0x00;
            TxFrame[1] = 0x00;
            break;

        case SBC_FS23_COM_WRITE:
            /* Creates write command. */
            TxFrame[3] |= SBC_FS23_COMMAND_TYPE_MASK;
            TxFrame[2] = (uint8)(TxData->WriteData >> 8U);
            TxFrame[1] = (uint8)(TxData->WriteData & 0xFFU);
            break;
        default:
            /* Command not valid. */
            break;
    }
    /* Sets CRC. */
    TxFrame[0] = Sbc_fs23_CalcCrc(&TxFrame[0], SBC_FS23_COMM_FRAME_SIZE);
}

/* Performs SPI transfer. */
#pragma location = "R_DMA_DATA"
uint8 u8TxFrameTmp[SBC_FS23_COMM_FRAME_SIZE] = {0};

#pragma location = "R_DMA_DATA"
uint8 u8RxFrameTmp[SBC_FS23_COMM_FRAME_SIZE] = {0};

static Std_ReturnType Sbc_fs23_TransferData(const Sbc_fs23_TxFrameType* TxData, uint16* RxData)
{
    uint8 u8RxFrame[SBC_FS23_COMM_FRAME_SIZE] = {0};
    Std_ReturnType ReturnValue = E_OK; /* Status. */
    const Sbc_fs23_InterfaceConfigType * TempInterfaceUnit = Sbc_fs23_pConfigPtr->InterfaceConfig;
    /* Create the Tx frame */

    uint8 u8TxFrame[SBC_FS23_COMM_FRAME_SIZE] = {0};
    Sbc_fs23_CreateSendFrame(TxData, u8TxFrame);

	/* 数据转码 */
    u8TxFrameTmp[0] = u8TxFrame[3];
    u8TxFrameTmp[1] = u8TxFrame[2];
    u8TxFrameTmp[2] = u8TxFrame[1];
    u8TxFrameTmp[3] = u8TxFrame[0];

    /* Trigger the SPI transfer */
    ReturnValue = SBC_MasterSendReceive((const uint8_t*)(&u8TxFrameTmp[0]), (uint8_t*)(&u8RxFrameTmp[0]),SBC_FS23_COMM_FRAME_SIZE,SBC_FS23_SPI_TIMEOUT);
	/* 数据转码 */
    u8RxFrame[0] = u8RxFrameTmp[3];
    u8RxFrame[1] = u8RxFrameTmp[2];
    u8RxFrame[2] = u8RxFrameTmp[1];
    u8RxFrame[3] = u8RxFrameTmp[0];

    // ReturnValue = LPSPI_DRV_MasterTransferBlocking(TempInterfaceUnit->SpiChannel,(const uint8_t*)(&u8TxFrame[0]), (uint8_t*)(&u8RxFrame[0]),SBC_FS23_COMM_FRAME_SIZE,SBC_FS23_SPI_TIMEOUT);
    if ((Std_ReturnType)E_OK == ReturnValue)
    {
        /* Check the CRC of the retrieved frame */
        ReturnValue = Sbc_fs23_CheckCrc(u8RxFrame, SBC_FS23_COMM_FRAME_SIZE);
        if (((Std_ReturnType)E_OK == ReturnValue) && (NULL_PTR != RxData))
        {
            *RxData = ((uint16)u8RxFrame[2] << 8U) | (uint16)u8RxFrame[1];
        }
    }

    return ReturnValue;
}

/* Reads challenge token (next generated TOKEN state) from the SBC. */
static Std_ReturnType Sbc_fs23_WdReadToken(uint16* WdToken)
{
    uint16 RxData;        /* Response to the command. */
    Std_ReturnType ReturnValue = E_OK; /* Status. */

    ReturnValue = Sbc_fs23_ReadRegister(SBC_FS23_FS_WD_TOKEN_ADDR, &RxData);
    if ((Std_ReturnType)E_OK == ReturnValue)
    {
        /* Store the watchdog token from the SBC device. */
        *WdToken = (uint16)(RxData & SBC_FS23_FS_WD_TOKEN_MASK);
    }

    return ReturnValue;
}

/* Check parameter for FsxbRelease*/
static Std_ReturnType Sbc_fs23_CheckParameterFsxbRelease(const Sbc_fs23_FsOutputType FsOutput)
{
    uint16 u16RxData = 0U;      /* Response to the command. */
    Std_ReturnType ReturnValue = (Std_ReturnType)E_OK; /* Status. */

    if ((SBC_FS23_FS_FS0B != FsOutput) && (SBC_FS23_FS_LIMP0 != FsOutput) && (SBC_FS23_FS_BOTH != FsOutput))
    {
        ReturnValue = (Std_ReturnType)E_NOT_OK;
    }
    else
    {
        ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_M_DEV_CFG_ADDR, &u16RxData);
        if(((FsOutput == SBC_FS23_FS_FS0B)||(FsOutput == SBC_FS23_FS_BOTH)) && ((u16RxData & SBC_FS23_M_FS0B_EN_MASK) != SBC_FS23_M_FS0B_EN_ENABLED))
        {
            ReturnValue |= (Std_ReturnType)E_NOT_OK;
        }
        if(((FsOutput == SBC_FS23_FS_LIMP0)||(FsOutput == SBC_FS23_FS_BOTH)) && ((u16RxData & SBC_FS23_M_LIMP0_EN_MASK) != SBC_FS23_M_LIMP0_EN_ENABLED))
        {
            ReturnValue |= (Std_ReturnType)E_NOT_OK;
        }
    }

    return ReturnValue;
}

/* Check parameter for RequestOutput*/
static Std_ReturnType Sbc_fs23_CheckParameterRequestOutput(const Sbc_fs23_FsRequestType FsRequest)
{
    uint16 u16RxData = 0U;      /* Response to the command. */
    Std_ReturnType ReturnValue = (Std_ReturnType)E_OK; /* Status. */

    ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_M_DEV_CFG_ADDR, &u16RxData);
    if(((FsRequest.Fs0bRequest == TRUE) && ((u16RxData & SBC_FS23_M_FS0B_EN_MASK) != SBC_FS23_M_FS0B_EN_ENABLED)) || ((FsRequest.Limp0Request == TRUE) && ((u16RxData & SBC_FS23_M_LIMP0_EN_MASK) != SBC_FS23_M_LIMP0_EN_ENABLED)))
    {
        ReturnValue |= (Std_ReturnType)E_NOT_OK;
    }

    return ReturnValue;
}

#if (STD_ON == SBC_FS23_SET_AMUX_API)
/* Check parameter for SetAmux*/
static Std_ReturnType Sbc_fs23_CheckParameterSetAmux(const Sbc_fs23_AmuxChannelType AmuxChannel, const Sbc_fs23_AmuxDivType AmuxDiv)
{
    Std_ReturnType ReturnValue = (Std_ReturnType)E_OK; /* Status. */

    if(((AmuxChannel != SBC_FS23_AMUX_CHANNEL0) && (AmuxChannel != SBC_FS23_AMUX_CHANNEL1) && (AmuxChannel != SBC_FS23_AMUX_CHANNEL2) && (AmuxChannel != SBC_FS23_AMUX_CHANNEL3) &&
       (AmuxChannel != SBC_FS23_AMUX_CHANNEL4) && (AmuxChannel != SBC_FS23_AMUX_CHANNEL5) && (AmuxChannel != SBC_FS23_AMUX_CHANNEL6) && (AmuxChannel != SBC_FS23_AMUX_CHANNEL7) &&
       (AmuxChannel != SBC_FS23_AMUX_CHANNEL8) && (AmuxChannel != SBC_FS23_AMUX_CHANNEL9) && (AmuxChannel != SBC_FS23_AMUX_CHANNEL10) && (AmuxChannel != SBC_FS23_AMUX_CHANNEL11) &&
       (AmuxChannel != SBC_FS23_AMUX_CHANNEL12)&& (AmuxChannel != SBC_FS23_AMUX_CHANNEL13) && (AmuxChannel != SBC_FS23_AMUX_CHANNEL14) && (AmuxChannel != SBC_FS23_AMUX_CHANNEL15) &&
       (AmuxChannel != SBC_FS23_AMUX_CHANNEL16)) || ((AmuxDiv != SBC_FS23_AMUX_DIV_LOW) && (AmuxDiv != SBC_FS23_AMUX_DIV_HIGH)))
    {
        ReturnValue = (Std_ReturnType)E_NOT_OK;
    }

    return ReturnValue;

}
#endif /*STD_ON == SBC_FS23_SET_AMUX_API*/

/* Check parameter for Sbc_fs23_WdChangeWindowDuration*/
static Std_ReturnType Sbc_fs23_CheckWdWindowDuration(const Sbc_fs23_WdWindowType WdWindow)
{
    Std_ReturnType ReturnValue = (Std_ReturnType)E_OK; /* Status. */

    if((WdWindow != SBC_FS23_WD_INFINITE) && (WdWindow != SBC_FS23_WD_1MS) && (WdWindow != SBC_FS23_WD_2MS) && (WdWindow != SBC_FS23_WD_3MS) &&
       (WdWindow != SBC_FS23_WD_4MS) && (WdWindow != SBC_FS23_WD_6MS) && (WdWindow != SBC_FS23_WD_8MS) && (WdWindow != SBC_FS23_WD_12MS) &&
       (WdWindow != SBC_FS23_WD_16MS)&& (WdWindow != SBC_FS23_WD_24MS) && (WdWindow != SBC_FS23_WD_32MS) && (WdWindow != SBC_FS23_WD_64MS) &&
       (WdWindow != SBC_FS23_WD_128MS) && (WdWindow != SBC_FS23_WD_256MS) && (WdWindow != SBC_FS23_WD_512MS) && (WdWindow != SBC_FS23_WD_1024MS))
    {
        ReturnValue = (Std_ReturnType)E_NOT_OK;
    }

    return ReturnValue;
}

/* Calculate and write CRC value to CRC register*/
static Std_ReturnType Sbc_fs23_WriteInitCrcRegister(void)
{
    Std_ReturnType ReturnValue = (Std_ReturnType)E_OK;
    uint64 CrcData = 0;
    uint16 OvUvCfg1Data = 0;        /* Response to the command. */
    uint16 OvUvCfg2Data = 0;
    uint16 FccuData = 0;
    uint16 FssmData = 0;
    uint16 WdCfgData = 0;
    uint16 u16TxData = 0;

    ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_FS_I_OVUV_CFG1_ADDR, &OvUvCfg1Data);
    ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_FS_I_OVUV_CFG2_ADDR, &OvUvCfg2Data);
    ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_FS_I_FCCU_CFG_ADDR, &FccuData);
    ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_FS_I_FSSM_CFG_ADDR, &FssmData);
    ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_FS_I_WD_CFG_ADDR, &WdCfgData);
    if((Std_ReturnType)E_OK == ReturnValue)
    {
        CrcData = ((uint64)(((uint64)OvUvCfg1Data & (uint64)0x1F80U) >> 7U) << 58U) |
              ((uint64)((uint64)OvUvCfg1Data & (uint64)0x003FU) << 52U) |
              ((uint64)(((uint64)OvUvCfg2Data & (uint64)0x1F80U) >> 7U) << 46U) |
              ((uint64)((uint64)OvUvCfg2Data & (uint64)0x003FU) << 40U) |
              ((uint64)((uint64)FccuData & (uint64)0x7FFFU) << 25U) |
              ((uint64)(((uint64)FssmData & (uint64)0x7FF0U) >> 4U) << 14U) |
              ((uint64)(((uint64)WdCfgData & (uint64)0x7F80U) >> 7U) << 6U);
        u16TxData = (uint16)Sbc_fs23_CalcInitRegistersCrc(CrcData);
        u16TxData |= (uint16)(Sbc_fs23_pConfigPtr->InitConfig->FailsafeConfig->InitCrcReg & (~SBC_FS23_FS_CRC_VALUE_MASK));
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_FS_CRC_ADDR, u16TxData);
    }

    return ReturnValue;
}

/* Trigger a compute of INIT CRC */
static Std_ReturnType Sbc_fs23_TriggerComputeCrcInit(void)
{
    Std_ReturnType ReturnValue = (Std_ReturnType)E_OK;
    uint16 u16RxData = 0U;

    ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_M_STATUS_ADDR,&u16RxData);
    if(((Std_ReturnType)E_OK == ReturnValue) && ((u16RxData & SBC_FS23_M_INIT_S_MASK) == SBC_FS23_M_INIT_S_INIT))
        {
        ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_FS_CRC_ADDR,&u16RxData);
        if((Std_ReturnType)E_OK == ReturnValue)
        {
            ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_FS_CRC_ADDR,u16RxData | SBC_FS23_FS_INIT_CRC_INIT_CRC_REQ_COMPUTE);          /* Trigger the INIT CRC compute */
            ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_FS_SAFETY_FLG_ADDR,&u16RxData);       /* Read the INIT_CRC_NOK_I flag */
            if(((Std_ReturnType)E_NOT_OK == ReturnValue) || ((u16RxData & SBC_FS23_FS_INIT_CRC_NOK_I_MASK) == SBC_FS23_FS_INIT_CRC_NOK_I_ERROR))
            {
                ReturnValue = (Std_ReturnType)E_NOT_OK;
            }
        }
        else
        {
            ReturnValue = (Std_ReturnType)E_NOT_OK;
        }
    }
    else
    {
        ReturnValue = (Std_ReturnType)E_NOT_OK;
    }

    return ReturnValue;
}

/* Check if device is in INIT*/
static Std_ReturnType Sbc_fs23_CheckInitState(void)
{
    uint16 RxData;        /* Response to the command. */
    Std_ReturnType ReturnValue = E_OK; /* Status. */

    ReturnValue = Sbc_fs23_ReadRegister(SBC_FS23_M_STATUS_ADDR, &RxData);

    if (((Std_ReturnType)E_OK != ReturnValue) || (SBC_FS23_M_INIT_S_INIT != (RxData & SBC_FS23_M_INIT_S_MASK)))
    {
        ReturnValue = E_NOT_OK;
    }

    return ReturnValue;
}

/* Initialize Main state machine*/
static Std_ReturnType Sbc_fs23_InitMain(void)
{
    Std_ReturnType ReturnValue = E_OK; /* Status. */
    uint16 RxData = 0U;
    const Sbc_fs23_MainConfigType *TempMainConfig = Sbc_fs23_pConfigPtr->InitConfig->MainConfig;
    ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_M_STATUS_ADDR,&RxData);
    if(((Std_ReturnType)E_OK == ReturnValue) && ((RxData & SBC_FS23_M_NORMAL_S_MASK) == SBC_FS23_M_NORMAL_S_NORMAL))
    {
        /* Configure MAIN registers*/
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_SYS_CFG_ADDR,TempMainConfig->InitSysCfgReg);
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_REG_CTRL_ADDR,TempMainConfig->InitRegCtrlReg);
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_CAN_LIN_MSK_ADDR,TempMainConfig->InitCanLinMskReg);
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_CS_FLG_MSK_ADDR,TempMainConfig->InitCsFlgMskReg);
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_HSX_MSK_ADDR,TempMainConfig->InitHsxMskReg);
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_IO_TIMER_MSK_ADDR,TempMainConfig->InitIoTimerMskReg);
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_IOWU_EN_ADDR,TempMainConfig->InitIoWuEnReg);
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_REG_MSK_ADDR,TempMainConfig->InitRegMskReg);
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_REG1_MSK_ADDR,TempMainConfig->InitReg1MskReg);
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_VSUP_COM_MSK_ADDR,TempMainConfig->InitVsupComMskReg);
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_WU1_EN_ADDR,TempMainConfig->InitWu1EnReg);
    }
    return ReturnValue;
}

/* Initialize Failsafe state machine*/
static Std_ReturnType Sbc_fs23_InitFailSafe(void)
{
    Std_ReturnType ReturnValue = E_OK; /* Status. */
    uint16 u16RxData = 0U;
    uint16 ExternalWindowValue = 0U;
    const Sbc_fs23_FailsafeConfigType *TempFailsafeConfig = Sbc_fs23_pConfigPtr->InitConfig->FailsafeConfig;
    /* Configure INIT_FS registers*/
    ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_FS_I_FCCU_CFG_ADDR,TempFailsafeConfig->InitFccuCfgReg);
    ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_FS_I_FSSM_CFG_ADDR,TempFailsafeConfig->InitFssmCfgReg);
    ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_FS_I_OVUV_CFG1_ADDR,TempFailsafeConfig->InitOvUvCfg1Reg);
    ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_FS_I_OVUV_CFG2_ADDR,TempFailsafeConfig->InitOvUvCfg2Reg);
#if (STD_OFF == SBC_FS23_EXTERNAL_WATCHDOG_API)
    ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_FS_I_WD_CFG_ADDR,TempFailsafeConfig->InitWdCfgReg);
    /*Disable Watchdog window*/
    ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_FS_WDW_CFG_ADDR,(TempFailsafeConfig->WdWindowReg & (~SBC_FS23_FS_WDW_PERIOD_MASK)));
    (void)ExternalWindowValue;
#else
    /*Save watchdog window configuration from External watchdog driver*/
    ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_FS_WDW_CFG_ADDR,&u16RxData);
    ExternalWindowValue = (uint16)(u16RxData & SBC_FS23_FS_WDW_PERIOD_MASK);
    /*Disable Watchdog window*/
    ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_FS_WDW_CFG_ADDR,(u16RxData & (~SBC_FS23_FS_WDW_PERIOD_MASK)));
#endif /*STD_OFF == SBC_FS23_EXTERNAL_WATCHDOG_API*/
    /* Write all Flag registers to clear*/
    ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_REG_FLG_ADDR,SBC_FS23_RWBITS_MASK);
    ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_REG1_FLG_ADDR,SBC_FS23_RWBITS_MASK);
    ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_IO_TIMER_FLG_ADDR,SBC_FS23_RWBITS_MASK);
    ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_VSUP_COM_FLG_ADDR,SBC_FS23_RWBITS_MASK);
    ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_IOWU_FLG_ADDR,SBC_FS23_RWBITS_MASK);
    ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_WU1_FLG_ADDR,SBC_FS23_RWBITS_MASK);
    ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_FS_SAFETY_FLG_ADDR,SBC_FS23_RWBITS_MASK);

    /* Execute ABIST 2 and check if it passed*/
    ReturnValue |= Sbc_fs23_WriteInitCrcRegister();
    /* Triggers CRC calculation */
    ReturnValue |= Sbc_fs23_TriggerComputeCrcInit();
    /* Set LOCK_INT bit to exit INIT phase */
    ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_M_SYS_CFG_ADDR,&u16RxData);
    if(ReturnValue == (Std_ReturnType)E_OK)
    {
        u16RxData |= SBC_FS23_M_LOCK_INIT_ACTION;
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_SYS_CFG_ADDR,u16RxData);
    }

    ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_FS_WDW_CFG_ADDR,&u16RxData);

    if(SBC_FS23_FS_WDW_PERIOD_INFINITE == (u16RxData & SBC_FS23_FS_WDW_PERIOD_MASK))
    {
        /* Allow watchdog trigger*/
        Sbc_fs23_bGateWdgTrigger = FALSE;
        /* Close initialization phase by sending first watchdog refresh. */
        ReturnValue |= Sbc_fs23_WdRefresh();
#if (STD_ON == SBC_FS23_EXTERNAL_WATCHDOG_API)
        /* Prevent watchdog trigger*/
        Sbc_fs23_bGateWdgTrigger = TRUE;
#endif /*STD_ON == SBC_FS23_EXTERNAL_WATCHDOG_API*/
    }

    /* Wait for Watchdog trigger the first time to close INIT phase*/
    ReturnValue |= Sbc_fs23_WaitInitClosed();
    if(ReturnValue == (Std_ReturnType)E_OK)
    {
		#if 0
        /* Exit Debug mode*/
        ReturnValue |= Sbc_fs23_DebugModeExit();
        /* Release FSxB pins*/
        if(ReturnValue == (Std_ReturnType)E_OK)
		#endif
        {
			#if 0
            ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_M_DEV_CFG_ADDR, &u16RxData);
            if(((u16RxData & SBC_FS23_M_FS0B_EN_MASK) == SBC_FS23_M_FS0B_EN_ENABLED) && ((u16RxData & SBC_FS23_M_LIMP0_EN_MASK) == SBC_FS23_M_LIMP0_EN_ENABLED))
            {
                /* Release both FS0B and LIMP0*/
                ReturnValue |= Sbc_fs23_FsOutputRelease(SBC_FS23_FS_BOTH);
            }
            else if((u16RxData & SBC_FS23_M_FS0B_EN_MASK) == SBC_FS23_M_FS0B_EN_ENABLED)
            {
                /* Release FS0B*/
                ReturnValue |= Sbc_fs23_FsOutputRelease(SBC_FS23_FS_FS0B);
            }
            else if((u16RxData & SBC_FS23_M_LIMP0_EN_MASK) == SBC_FS23_M_LIMP0_EN_ENABLED)
            {
                /* Release FS0B*/
                ReturnValue |= Sbc_fs23_FsOutputRelease(SBC_FS23_FS_LIMP0);
            }
            else
            {
                /* Do nothing*/
            }
            if(ReturnValue == (Std_ReturnType)E_OK)
			#endif
            {
#if (STD_OFF == SBC_FS23_EXTERNAL_WATCHDOG_API)
                if((TempFailsafeConfig->WdWindowReg & SBC_FS23_FS_WDW_PERIOD_MASK) != SBC_FS23_FS_WDW_PERIOD_INFINITE)
                {
					Sbc_fs23_bGateWdgTrigger = FALSE;
                    ReturnValue |= Sbc_fs23_WdChangeWindowDuration((Sbc_fs23_WdWindowType)(TempFailsafeConfig->WdWindowReg & SBC_FS23_FS_WDW_PERIOD_MASK));
					ReturnValue |= Sbc_fs23_WdRefresh();/*refresh to start WD after change period*/ 
                }
#else
                if(ExternalWindowValue!= SBC_FS23_FS_WDW_PERIOD_INFINITE)
                {
                    ReturnValue |= Sbc_fs23_WdChangeWindowDuration((Sbc_fs23_WdWindowType)ExternalWindowValue);
                }
                Sbc_fs23_bGateWdgTrigger = FALSE;
#endif /*STD_OFF == SBC_FS23_EXTERNAL_WATCHDOG_API*/
            }
        }
    }
    return ReturnValue;
}

/* Wait for fail-safe pin to be released */
static Std_ReturnType Sbc_fs23_TimeWaitRelease(Sbc_fs23_FsOutputType FsOutput)
{
    Std_ReturnType ReturnValue = (Std_ReturnType)E_OK;
    uint16 u16RxData = 0U;
    volatile uint32 u32TimeOut = SBC_FS23_TIMEOUT_DURATION;

    UserTimerDelayMs(u32TimeOut); /* lrs */

    ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_FS_SAFETY_OUTPUTS_ADDR,&u16RxData);
    switch(FsOutput)
    {
        case SBC_FS23_FS_FS0B:
            if((u16RxData & SBC_FS23_FS_FS0B_SNS_MASK ) != SBC_FS23_FS_FS0B_SNS_HIGH)
            {
                ReturnValue = (Std_ReturnType)E_NOT_OK;
            }
            break;
        case SBC_FS23_FS_LIMP0:
            if((u16RxData & SBC_FS23_FS_LIMP0_SNS_MASK ) != SBC_FS23_FS_LIMP0_SNS_HIGH)
            {
                ReturnValue = (Std_ReturnType)E_NOT_OK;
            }
            break;
        case SBC_FS23_FS_BOTH:
            if((u16RxData & (SBC_FS23_FS_FS0B_SNS_MASK | SBC_FS23_FS_LIMP0_SNS_MASK)) != (SBC_FS23_FS_FS0B_SNS_HIGH | SBC_FS23_FS_LIMP0_SNS_HIGH))
            {
                ReturnValue = (Std_ReturnType)E_NOT_OK;
            }
            break;
        default:
            ReturnValue = (Std_ReturnType)E_NOT_OK;
            break;
    }
#if (STD_OFF == SBC_FS23_DISABLE_DEM_REPORT_ERROR_STATUS)
    if ((uint32)STD_ON == Sbc_fs23_pDemConfigPtr->Sbc_fs23_E_Timeout.state)
    {
        if(E_NOT_OK == ReturnValue)
        {
            /* Report Dem failed status */
            (void) Dem_SetEventStatus((Dem_EventIdType)Sbc_fs23_pDemConfigPtr->Sbc_fs23_E_Timeout.id, DEM_EVENT_STATUS_FAILED);
        }
        else
        {
            /* Report Dem passed status */
            (void) Dem_SetEventStatus((Dem_EventIdType)Sbc_fs23_pDemConfigPtr->Sbc_fs23_E_Timeout.id, DEM_EVENT_STATUS_PASSED);
        }
    }
#endif /* (STD_OFF == SBC_FS23_DISABLE_DEM_REPORT_ERROR_STATUS) */
    return ReturnValue;
}

/* Calulate Data to be written to RELEASExB register*/
static Std_ReturnType Sbc_fs23_CalculateReleaseFsxb(Sbc_fs23_FsOutputType FsOutput, uint16 *OutputData)
{
    uint16 u16RxData = 0U;
    uint16 u16Temp =0U;
    uint16 u16Result =0U;
    uint8 CountValue;
    Std_ReturnType ReturnValue = E_OK; /*Status*/

    ReturnValue = Sbc_fs23_ReadRegister(SBC_FS23_FS_WD_TOKEN_ADDR, &u16RxData);
    if ((Std_ReturnType)E_OK == ReturnValue)
    {
        u16Temp = (uint16)(u16RxData & SBC_FS23_FS_WD_TOKEN_MASK);
        for (CountValue = 0U; CountValue < 16U; CountValue++)
        {
            u16Result |= (uint16)(((u16Temp >> (15U - CountValue)) & 1U) << CountValue);
        }
        u16Result = ~u16Result;
        switch (FsOutput)
        {
        case SBC_FS23_FS_FS0B:
            u16Result = (uint16)((u16Result & 0xFFF8U) >> 3U);
            *OutputData = (uint16)(SBC_FS23_FS_RELEASE_FS0B_LIMP0_FS0B  | u16Result);
            break;

        case SBC_FS23_FS_LIMP0:
            u16Result = (uint16)(u16Result & 0x1FFFU);
            *OutputData = (uint16)(SBC_FS23_FS_RELEASE_FS0B_LIMP0_LIMP0 | u16Result);
            break;

        case SBC_FS23_FS_BOTH:
            u16Result = (uint16)(((u16Result & 0xFE00U) >> 3U) | (u16Result & 0x003FU));
            *OutputData = (uint16)(SBC_FS23_FS_RELEASE_FS0B_LIMP0_BOTH | u16Result);
            break;

        default:
            ReturnValue = E_NOT_OK;
            break;
        }
    }
    return ReturnValue;
}

/* Wait for Fault error counter =0 */
static Std_ReturnType Sbc_fs23_TimeWaitClearFault(const uint32 TimeoutValueUs)
{
    Std_ReturnType ReturnValue = (Std_ReturnType)E_OK; /* Status. */
    uint8 u8ErrorCount = 0U;
    volatile uint32 u32TimeOut = SBC_FS23_TIMEOUT_DURATION;
    (void)TimeoutValueUs;

    UserTimerDelayMs(u32TimeOut);/* lrs */

    ReturnValue = Sbc_fs23_GetFaultErrorCounter(&u8ErrorCount);
    if (0U != u8ErrorCount)
    {
        ReturnValue |= (Std_ReturnType)E_NOT_OK;
    }

    return ReturnValue;
}

/* Clear fault error counter*/
static Std_ReturnType Sbc_fs23_ClearFaultErrorCounter(void)
{
    Std_ReturnType ReturnValue = (Std_ReturnType)E_OK;
    uint16 WdgRfrRequest = 0U;                    /* Expect number wdg refresh for decreasing fault error */
    uint8 WdgRfrLimit = 0U;                        /* Number of wdg refresh limit */
    uint16 u16RxData = 0U;                      /* Response to the command. */
    const uint16 ArrayRfrLimitValue[4U] = {6U, 4U, 2U, 1U};

    const uint32 ArrayWindowPeriodToUs[16U] =
    {
        0U,
        1000U,
        2000U,
        3000U,
        4000U,
        6000U,
        8000U,
        12000U,
        16000U,
        24000U,
        32000U,
        64000U,
        128000U,
        256000U,
        512000U,
        1024000U
    };
    /* Get current fault error counter */
    ReturnValue = Sbc_fs23_GetFaultErrorCounter((uint8*)&WdgRfrRequest);

    if ((WdgRfrRequest > 0U) && ((Std_ReturnType)E_OK == ReturnValue))
    {
        /* Get wdg refresh limit  */
        ReturnValue = Sbc_fs23_ReadRegister(SBC_FS23_FS_I_WD_CFG_ADDR, &u16RxData);
        WdgRfrLimit = (uint8)ArrayRfrLimitValue[(u16RxData & SBC_FS23_FS_WD_RFR_LIMIT_MASK) >> SBC_FS23_FS_WD_RFR_LIMIT_SHIFT];
        if ((Std_ReturnType)E_OK == ReturnValue)
        {
            /* Wdg refresh time = (fault error counter * (max wdg refresh limit + 1) */
            WdgRfrRequest = WdgRfrRequest * ((uint16)WdgRfrLimit + 1U);
            ReturnValue = Sbc_fs23_ReadRegister(SBC_FS23_FS_WDW_CFG_ADDR, &u16RxData);
            if (((Std_ReturnType)E_OK == ReturnValue) && ((u16RxData & SBC_FS23_FS_WDW_PERIOD_MASK) == SBC_FS23_FS_WDW_PERIOD_INFINITE))
            {
                /* Execute Wdg refresh with WdgRfrRequest time */
                while ((WdgRfrRequest > 0U) && ((Std_ReturnType)E_OK == ReturnValue))
                {
                    /* Allow watchdog trigger*/
                    Sbc_fs23_bGateWdgTrigger = FALSE;
                    ReturnValue |= Sbc_fs23_WdRefresh();
                    WdgRfrRequest--;
                }
            }
            else
            {
                ReturnValue |= Sbc_fs23_TimeWaitClearFault(WdgRfrRequest * ArrayWindowPeriodToUs[(u16RxData & SBC_FS23_FS_WDW_PERIOD_MASK) >> SBC_FS23_FS_WDW_PERIOD_SHIFT]);
            }
        }
    }

    return ReturnValue;
}

/* Wait Sbc device exit INIT*/
static Std_ReturnType Sbc_fs23_WaitInitClosed(void)
{
    Std_ReturnType ReturnValue = (Std_ReturnType)E_OK;
    uint32 u32Timeout = SBC_FS23_TIMEOUT_DURATION;
    uint16 u16RxData = 0U;

    UserTimerDelayMs(u32Timeout); /* lrs */

    ReturnValue = Sbc_fs23_ReadRegister(SBC_FS23_M_STATUS_ADDR,&u16RxData);
    if ((SBC_FS23_M_INIT_S_NOT_INIT != (u16RxData & SBC_FS23_M_INIT_S_MASK)))
    {
        ReturnValue = (Std_ReturnType)E_NOT_OK;
    }
	
    return ReturnValue;
}

/* Exit Debug Mode*/
static Std_ReturnType Sbc_fs23_DebugModeExit(void)
{
    Std_ReturnType ReturnValue = (Std_ReturnType)E_OK;
    uint16 u16RxData = 0U;
    /* Is Device in debug mode */
    ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_M_SYS1_CFG_ADDR, &u16RxData);
    /* Exit Debug Mode */
    if (SBC_FS23_M_DBG_MODE_DEBUG == (u16RxData & SBC_FS23_M_DBG_MODE_MASK))
    {
        u16RxData = ((u16RxData & (~SBC_FS23_M_DBG_EXIT_MASK)) | SBC_FS23_M_DBG_EXIT_LEAVE);
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_SYS1_CFG_ADDR, u16RxData);
        ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_M_SYS1_CFG_ADDR, &u16RxData);
        if (SBC_FS23_M_DBG_MODE_NOT_DEBUG != (u16RxData & SBC_FS23_M_DBG_MODE_MASK))
        {
            ReturnValue |= (Std_ReturnType)E_NOT_OK;
        }
    }

    return ReturnValue;
}

/* Disable Watchdog window period*/
Std_ReturnType Sbc_fs23_DisableWatchdog(void)
{
    Std_ReturnType ReturnValue = (Std_ReturnType)E_OK;
    uint16 u16RxData = 0U;
    uint16 u16TxData;
    /* Is Device in Init State? */
    ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_M_STATUS_ADDR, &u16RxData);
    if (SBC_FS23_M_INIT_S_INIT != (u16RxData & SBC_FS23_M_INIT_S_MASK))
    {
        ReturnValue |= Sbc_fs23_GoToInitState();
    }
    if ((Std_ReturnType)E_OK == ReturnValue)
    {
        /* Retreive data from FS_WDW_CFG register*/
        ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_FS_WDW_CFG_ADDR, &u16RxData);
        u16TxData = (u16RxData & (~SBC_FS23_FS_WDW_PERIOD_MASK));
        /* Write new value to WD_WINDOW_PERIOD and check it was written correctly. */
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_FS_WDW_CFG_ADDR,u16TxData);

#if 1 /* B型号SBC必须设置该寄存器，否则将无法退出INIT模式 */
        /* Set LOCK_INT bit to exit INIT phase */
        ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_M_SYS_CFG_ADDR,&u16RxData);
        if(ReturnValue == (Std_ReturnType)E_OK)
        {
            u16RxData |= SBC_FS23_M_LOCK_INIT_ACTION;
            ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_SYS_CFG_ADDR,u16RxData);
        }
#endif   

        /* Allow to trigger watchdog again */
        Sbc_fs23_bGateWdgTrigger = FALSE;
        ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_FS_WDW_CFG_ADDR, &u16RxData);
        if (((Std_ReturnType)E_OK == ReturnValue) && (SBC_FS23_FS_WDW_PERIOD_INFINITE == (u16RxData & SBC_FS23_FS_WDW_PERIOD_MASK)))
        {
            /* Close INIT phase by sending first watchdog refresh. */
            ReturnValue = Sbc_fs23_WdRefresh();
        }
        ReturnValue |= Sbc_fs23_WaitInitClosed();
        /* Clear fault error counter*/
        ReturnValue |= Sbc_fs23_ClearFaultErrorCounter();
		#if 0
        /* Release FSxB pins*/
        if(ReturnValue == (Std_ReturnType)E_OK)
        {
            ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_M_DEV_CFG_ADDR, &u16RxData);
            if(((u16RxData & SBC_FS23_M_FS0B_EN_MASK) == SBC_FS23_M_FS0B_EN_ENABLED) && ((u16RxData & SBC_FS23_M_LIMP0_EN_MASK) == SBC_FS23_M_LIMP0_EN_ENABLED))
            {
                /* Release both FS0B and LIMP0*/
                ReturnValue |= Sbc_fs23_FsOutputRelease(SBC_FS23_FS_BOTH);
            }
            else if((u16RxData & SBC_FS23_M_FS0B_EN_MASK) == SBC_FS23_M_FS0B_EN_ENABLED)
            {
                /* Release FS0B*/
                ReturnValue |= Sbc_fs23_FsOutputRelease(SBC_FS23_FS_FS0B);
            }
            else if((u16RxData & SBC_FS23_M_LIMP0_EN_MASK) == SBC_FS23_M_LIMP0_EN_ENABLED)
            {
                /* Release FS0B*/
                ReturnValue |= Sbc_fs23_FsOutputRelease(SBC_FS23_FS_LIMP0);
            }
            else
            {
                /* Do nothing*/
            }
        }
		#endif
    }

    return ReturnValue;
}

/* Check watchdog refresh is good*/
static Std_ReturnType Sbc_fs23_CheckWdgRefresh(void)
{
    uint16 u16RxData = 0U;        /* Response to the command. */
    Std_ReturnType ReturnValue = E_OK; /* Status. */

    ReturnValue = Sbc_fs23_ReadRegister(SBC_FS23_FS_SAFETY_FLG_ADDR, &u16RxData);
    if ((SBC_FS23_FS_WD_NOK_I_NOT_OK == (uint16)(u16RxData & SBC_FS23_FS_WD_NOK_I_MASK)) && ((Std_ReturnType)E_OK == ReturnValue))
    {
        u16RxData &= (SBC_FS23_FS_FCCU2_M_MASK | SBC_FS23_FS_FCCU1_M_MASK | SBC_FS23_FS_FCCU12_M_MASK | SBC_FS23_FS_WD_NOK_M_MASK | SBC_FS23_FS_INIT_CRC_NOK_M_MASK);
        /*Clear watchdog error flag*/
        (void)Sbc_fs23_WriteRegister(SBC_FS23_FS_SAFETY_FLG_ADDR, (u16RxData | SBC_FS23_FS_WD_NOK_I_MASK));
        ReturnValue = (Std_ReturnType)E_NOT_OK;
    }

    return ReturnValue;
}

/* Retrieve wake up reason from IOs pin if possible*/
static Std_ReturnType Sbc_fs23_GetIoWuStatus(Sbc_fs23_WakeupReasonType * WakeupReason)
{
    uint16 u16RxData = 0U;        /* Response to the command. */
    Std_ReturnType ReturnValue = E_OK; /* Status. */

    if(WakeupReason != NULL_PTR)
    {
        ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_M_IOWU_FLG_ADDR,&u16RxData);
        if ((u16RxData & SBC_FS23_M_WK1_WU_I_MASK) == SBC_FS23_M_WK1_WU_I_WAKEUP)
        {
            *WakeupReason = SBC_FS23_WK1_WU;
        }
        else if ((u16RxData & SBC_FS23_M_WK2_WU_I_MASK) == SBC_FS23_M_WK2_WU_I_WAKEUP)
        {
            *WakeupReason = SBC_FS23_WK2_WU;
        }
        else if ((u16RxData & SBC_FS23_M_HVIO1_WU_I_MASK) == SBC_FS23_M_HVIO1_WU_I_WAKEUP)
        {
            *WakeupReason = SBC_FS23_HVIO1_WU;
        }
        else if ((u16RxData & SBC_FS23_M_HVIO2_WU_I_MASK) == SBC_FS23_M_HVIO2_WU_I_WAKEUP)
        {
            *WakeupReason = SBC_FS23_HVIO2_WU;
        }
        else if ((u16RxData & SBC_FS23_M_LVIO3_WU_I_MASK) == SBC_FS23_M_LVIO3_WU_I_WAKEUP)
        {
            *WakeupReason = SBC_FS23_LVIO3_WU;
        }
        else if ((u16RxData & SBC_FS23_M_LVIO4_WU_I_MASK) == SBC_FS23_M_LVIO4_WU_I_WAKEUP)
        {
            *WakeupReason = SBC_FS23_LVIO4_WU;
        }
        else if ((u16RxData & SBC_FS23_M_LVI5_WU_I_MASK) == SBC_FS23_M_LVI5_WU_I_WAKEUP)
        {
            *WakeupReason = SBC_FS23_LVIO5_WU;
        }
        else
        {
            *WakeupReason = SBC_FS23_NO_WU_EVENT;
        }
    }
    else
    {
        ReturnValue = (Std_ReturnType)E_NOT_OK;
    }

    return ReturnValue;
}

/* Retrieve wake up reason from other sources if possible*/
static Std_ReturnType Sbc_fs23_GetWu1Status(Sbc_fs23_WakeupReasonType * WakeupReason)
{
    uint16 u16RxData = 0U;        /* Response to the command. */
    Std_ReturnType ReturnValue = E_OK; /* Status. */

    if(WakeupReason != NULL_PTR)
    {
        ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_M_WU1_FLG_ADDR,&u16RxData);
        if ((u16RxData & SBC_FS23_M_CAN_WU_I_MASK) == SBC_FS23_M_CAN_WU_I_WAKEUP)
        {
            *WakeupReason = SBC_FS23_CAN_WU;
        }
        else if ((u16RxData & SBC_FS23_M_LIN_WU_I_MASK) == SBC_FS23_M_LIN_WU_I_WAKEUP)
        {
            *WakeupReason = SBC_FS23_LIN_WU;
        }
        else if ((u16RxData & SBC_FS23_M_LDT_WU_I_MASK) == SBC_FS23_M_LDT_WU_I_WAKEUP)
        {
            *WakeupReason = SBC_FS23_LDT_WU;
        }
        else if ((u16RxData & SBC_FS23_M_GO2NORMAL_WU_MASK) == SBC_FS23_M_GO2NORMAL_WU_WAKEUP)
        {
            *WakeupReason = SBC_FS23_GO_TO_NORMAL_WU;
        }
        else if ((u16RxData & SBC_FS23_M_INT_TO_WU_MASK) == SBC_FS23_M_INT_TO_WU_WAKEUP)
        {
            *WakeupReason = SBC_FS23_GO_INT_TO_WU;
        }
        else if ((u16RxData & SBC_FS23_M_V1_UVLP_WU_MASK) == SBC_FS23_M_V1_UVLP_WU_WAKEUP)
        {
            *WakeupReason = SBC_FS23_V1_UVLP_WU;
        }
        else if ((u16RxData & SBC_FS23_M_WD_OFL_WU_MASK) == SBC_FS23_M_WD_OFL_WU_WAKEUP)
        {
            *WakeupReason = SBC_FS23_WD_OFL_WU;
        }
        else if ((u16RxData & SBC_FS23_M_EXT_RSTB_WU_MASK) == SBC_FS23_M_EXT_RSTB_WU_WAKEUP)
        {
            *WakeupReason = SBC_FS23_EXT_RSTB_WU;
        }
        else
        {
            *WakeupReason = SBC_FS23_NO_WU_EVENT;
        }
    }
    else
    {
        ReturnValue = (Std_ReturnType)E_NOT_OK;
    }

    return ReturnValue;
}

/*==================================================================================================
*                                        GLOBAL FUNCTIONS
==================================================================================================*/
/**
* @brief        Initializes the SBC driver. SID is 0x01.
* @details      Internally stores the run-time configuration.
*
* @param[in]    ConfigPtr         Pointer to SBC driver configuration set.
*
* @return       void.
*
* @api
*/
/** @implements   Sbc_fs23_InitDriver_Activity */
void Sbc_fs23_InitDriver(const Sbc_fs23_ConfigType* ConfigPtr)
{
    /*Check if driver has been initialized*/
	if (FALSE == Sbc_fs23_IsInitialized)
    {
        /* Get a local copy of the driver initialization structure. */
        if (NULL_PTR == ConfigPtr)
        {
            Sbc_fs23_pConfigPtr = &Sbc_fs23_Config;
            Sbc_fs23_IsInitialized = TRUE;
        }

        Sbc_fs23_bGateWdgTrigger = FALSE;
        Sbc_fs23_bIsWdgTrigger = FALSE;
    }
}

/**
* @brief        Configures the SBC device. SID is 0x02.
* @details      Performs initialization checks (LBIST, ABIST1 are pass),
*               writes all INIT_MAIN, INIT_FS register and watchdog configuration,
*               sends first watchdog refresh, clears fault error counter.
*               Closes initialization phase by sending FS0B releases command.
*
*               NOTE: If external watchdog is used, writing watchdog related registers
*               is skipped in order to allow watchdog configured by the external watchdog.
*
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
/** @implements   Sbc_fs23_InitDevice_Activity */
Std_ReturnType Sbc_fs23_InitDevice(void)
{
    Std_ReturnType ReturnValue = (Std_ReturnType)E_OK;
	uint16 u16RxData = 0;
	
    if (FALSE == Sbc_fs23_IsInitialized)
    {
        /* Driver not initialized. */
        ReturnValue = (Std_ReturnType)E_NOT_OK;
    }
    else
    {

		ReturnValue = Sbc_fs23_ReadRegister(SBC_FS23_M_STATUS_ADDR, &u16RxData);
		if(((Std_ReturnType)E_OK == ReturnValue) && (SBC_FS23_M_INIT_S_INIT != (u16RxData & SBC_FS23_M_INIT_S_MASK)))
		{
			ReturnValue |= Sbc_fs23_GoToInitState();
		}
		
		if(((Std_ReturnType)E_OK == ReturnValue))
        {
            ReturnValue |= Sbc_fs23_InitFailSafe();
            ReturnValue |= Sbc_fs23_InitMain();
			ReturnValue |= Sbc_fs23_SetWakeup(0);
			ReturnValue |= Sbc_fs23_SetAmux(SBC_FS23_AMUX_CHANNEL12,SBC_FS23_AMUX_DIV_LOW);
        }
    }

    return ReturnValue;
}

/*================================================================================================*/
/**
* @brief        Performs a read from a single FS23 register. SID is 0x05.
* @details      Performs a single read register based on provided address.
*               The response is returned in Sbc_fs23_RxFrameType structure.
*
* @param[in]    Address         Register address.
* @param[out]   RxData         Pointer to structure holding the response from SBC.
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
/** @implements   Sbc_fs23_ReadRegister_Activity */

Std_ReturnType Sbc_fs23_ReadRegister(uint8 Address, uint16* RxData)
{
    Std_ReturnType ReturnValue = E_OK;
    Sbc_fs23_TxFrameType TxData =
    {
        Address,
        SBC_FS23_COM_READ,
        0U
    };
    if(Sbc_fs23_IsInitialized == FALSE)
    {
        /*Driver was not initialized*/
        ReturnValue = (Std_ReturnType)E_NOT_OK;
    }
    else if(NULL_PTR == RxData)
    {
        /*RxData was NULL*/
        ReturnValue = (Std_ReturnType)E_NOT_OK;
    }
    else if(SBC_FS23_REG_ADDR_MASK < Address)
    {
        /*Register address out of range*/
        ReturnValue = (Std_ReturnType)E_NOT_OK;
    }
    else
    {
        ReturnValue = Sbc_fs23_TransferData(&TxData,RxData);
    }

    return ReturnValue;
}

/*================================================================================================*/
/**
* @brief        Sends write command to the SBC. SID is 0x04.
* @details      Performs a single write register based on provided address.
* @param[in]    Address         Register address.
* @param[in]    WriteData       Register write value.

*
* @return       Std_ReturnType  Return code.
*
* @api
*/
/** @implements   Sbc_fs23_WriteRegister_Activity */
Std_ReturnType Sbc_fs23_WriteRegister(uint8 Address, uint16 WriteData)
{
    Std_ReturnType ReturnValue = E_OK;
    Sbc_fs23_TxFrameType TxData =
    {
        Address,
        SBC_FS23_COM_WRITE,
        WriteData
    };

    if(Sbc_fs23_IsInitialized == FALSE)
    {
        /*Driver was not initialized*/
        ReturnValue = E_NOT_OK;
    }
    else if(SBC_FS23_REG_ADDR_MASK < Address)
    {
        /*Register address out of range*/
        ReturnValue = E_NOT_OK;
    }
    else
    {
        ReturnValue |= Sbc_fs23_TransferData(&TxData,NULL_PTR);
    }

    return ReturnValue;
}

/*================================================================================================*/
/**
* @brief        Performs the watchdog refresh. SID is 0x06.
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
/** @implements   Sbc_fs23_WdRefresh_Activity */
Std_ReturnType Sbc_fs23_WdRefresh(void)
{
    Std_ReturnType ReturnValue = E_OK;
    uint16 WdToken = 0U;
    if(Sbc_fs23_IsInitialized == FALSE)
    {
        /*Driver was not initialized*/
        ReturnValue = E_NOT_OK;
#if (SBC_FS23_DEV_ERROR_DETECT == STD_ON)
        (void)Det_ReportError((uint16)CDD_SBC_FS23_MODULE_ID, (uint8)0, (uint8)SBC_FS23_SID_WD_REFRESH, (uint8)SBC_FS23_E_UNINIT);
#endif /* SBC_FS23_DEV_ERROR_DETECT == STD_ON */
    }
    else if (FALSE == Sbc_fs23_bGateWdgTrigger)
    {
        if (FALSE == Sbc_fs23_bIsWdgTrigger)
        {
            Sbc_fs23_bIsWdgTrigger = TRUE;

            /* Synchronize with TOKEN generator on the device. */
            //SchM_Enter_Sbc_fs23_SBC_FS23_EXCLUSIVE_AREA_00();
            ReturnValue |= Sbc_fs23_WdReadToken(&WdToken);
            if ((Std_ReturnType)E_OK == ReturnValue)
            {
                ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_FS_WD_ANSWER_ADDR, WdToken);
            }
            Sbc_fs23_bIsWdgTrigger = FALSE;
            //SchM_Exit_Sbc_fs23_SBC_FS23_EXCLUSIVE_AREA_00();
            /* Check if watchdog refresh was successful. */
            if (((Std_ReturnType)E_OK == Sbc_fs23_CheckWdgRefresh()) && ((Std_ReturnType)E_OK == ReturnValue))
            {
#if (STD_OFF == SBC_FS23_DISABLE_DEM_REPORT_ERROR_STATUS)
                if ((uint32)STD_ON == Sbc_fs23_pDemConfigPtr->Sbc_fs23_E_Watchdog.state)
                {
                    /* Report Dem failed status */
                    (void) Dem_SetEventStatus((Dem_EventIdType)Sbc_fs23_pDemConfigPtr->Sbc_fs23_E_Watchdog.id, DEM_EVENT_STATUS_PASSED);
                }
#endif /* (STD_OFF == SBC_FS23_DISABLE_DEM_REPORT_ERROR_STATUS) */
            }
            else
            {
                ReturnValue = (Std_ReturnType)E_NOT_OK;
#if (STD_OFF == SBC_FS23_DISABLE_DEM_REPORT_ERROR_STATUS)
                if ((uint32)STD_ON == Sbc_fs23_pDemConfigPtr->Sbc_fs23_E_Watchdog.state)
                {
                    /* Report Dem failed status */
                    (void) Dem_SetEventStatus((Dem_EventIdType)Sbc_fs23_pDemConfigPtr->Sbc_fs23_E_Watchdog.id, DEM_EVENT_STATUS_FAILED);
                }
#endif /* (STD_OFF == SBC_FS23_DISABLE_DEM_REPORT_ERROR_STATUS) */
            }
        }
    }
    else
    {
        /*Do nothing*/
    }

    return ReturnValue;
}

/*================================================================================================*/
/**
* @brief        Reads actual Fault Error Counter value. SID is 0x07.
*
* @param[out]   pFaultErrorCounterValue  Pointer to Fault Error counter value storage.
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
/** @implements   Sbc_fs23_GetFaultErrorCounter_Activity */
Std_ReturnType Sbc_fs23_GetFaultErrorCounter(uint8* FaultErrorCounterValue)
{
    uint16 u16RxData;        /* Register received Data. */
    Std_ReturnType ReturnValue = E_OK; /* Status. */

    if (NULL_PTR == FaultErrorCounterValue)
    {
        ReturnValue = E_NOT_OK;
#if (SBC_FS23_DEV_ERROR_DETECT == STD_ON)
        (void)Det_ReportError((uint16)CDD_SBC_FS23_MODULE_ID, (uint8)0, (uint8)SBC_FS23_SID_GET_FAULT_ERROR_COUNTER, (uint8)SBC_FS23_E_PARAM_POINTER);
#endif /* SBC_FS23_DEV_ERROR_DETECT == STD_ON */
    }
    else if (FALSE == Sbc_fs23_IsInitialized)
    {
        /* Driver not initialized. */
        ReturnValue = E_NOT_OK;
#if (SBC_FS23_DEV_ERROR_DETECT == STD_ON)
        (void)Det_ReportError((uint16)CDD_SBC_FS23_MODULE_ID, (uint8)0, (uint8)SBC_FS23_SID_GET_FAULT_ERROR_COUNTER, (uint8)SBC_FS23_E_UNINIT);
#endif /* SBC_FS23_DEV_ERROR_DETECT == STD_ON */
    }
    else
    {
        /*Retrieve fault error counter value*/
        ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_FS_I_FSSM_CFG_ADDR, &u16RxData);
        if ((Std_ReturnType)E_OK == ReturnValue)
        {
            *FaultErrorCounterValue = (uint8)((u16RxData & SBC_FS23_FS_FLT_ERR_CNT_MASK) >> SBC_FS23_FS_FLT_ERR_CNT_SHIFT);
        }
    }

    return ReturnValue;
}

/*================================================================================================*/
/**
* @brief        Changes watchdog window duration used for watchdog. SID is 0x08.
* @details
*
* @param[in]    WindowDuration        Duration of watch dog window.
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
/** @implements   Sbc_fs23_WdChangeWindowDuration_Activity */
Std_ReturnType Sbc_fs23_WdChangeWindowDuration(const Sbc_fs23_WdWindowType WindowDuration)
{

    Std_ReturnType ReturnValue = (Std_ReturnType)E_OK; /* Status. */
    uint16 u16RxData = 0U;
    uint16 u16TxData;
    if (FALSE == Sbc_fs23_IsInitialized)
    {
        /* Driver not initialized. */
        ReturnValue = (Std_ReturnType)E_NOT_OK;
#if (SBC_FS23_DEV_ERROR_DETECT == STD_ON)
        (void)Det_ReportError((uint16)CDD_SBC_FS23_MODULE_ID, (uint8)0, (uint8)SBC_FS23_SID_WD_CHANGE_WINDOW_DURATION, (uint8)SBC_FS23_E_UNINIT);
#endif /* SBC_FS23_DEV_ERROR_DETECT == STD_ON */
    }
    else if ((Std_ReturnType)E_OK != Sbc_fs23_CheckWdWindowDuration(WindowDuration))
    {
        /*WindowDuration out of range*/
        ReturnValue = (Std_ReturnType)E_NOT_OK;
#if (SBC_FS23_DEV_ERROR_DETECT == STD_ON)
        (void)Det_ReportError((uint16)CDD_SBC_FS23_MODULE_ID, (uint8)0, (uint8)SBC_FS23_SID_WD_CHANGE_WINDOW_DURATION, (uint8)SBC_FS23_E_PARAM_RANGE);
#endif
    }
    else if (WindowDuration == SBC_FS23_WD_INFINITE)
    {
        /* Disable watchdog window period */
        ReturnValue |= Sbc_fs23_DisableWatchdog();
    }
    else
    {
        /* Retreive data from FS_WDW_CFG register*/
        ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_FS_WDW_CFG_ADDR, &u16RxData);
        u16TxData = (u16RxData & (~SBC_FS23_FS_WDW_PERIOD_MASK)) | (uint16)WindowDuration;
        /* Write new value to WD_WINDOW_PERIOD and check it was written correctly. */
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_FS_WDW_CFG_ADDR,u16TxData);
        ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_FS_WDW_CFG_ADDR, &u16RxData);

        if ((uint16)WindowDuration != (u16RxData & SBC_FS23_FS_WDW_PERIOD_MASK))
        {
            /*New watchdog window value not written correctly.*/
            ReturnValue |= (Std_ReturnType)E_NOT_OK;
        }
        else
        {
#if (SBC_FS23_EXTERNAL_WATCHDOG_API == STD_OFF)
            if(Sbc_fs23_pConfigPtr->Sbc_fs23_pWdgTaskNotification != NULL_PTR)
            {
                Sbc_fs23_pConfigPtr->Sbc_fs23_pWdgTaskNotification((uint16)((u16RxData & SBC_FS23_FS_WDW_PERIOD_MASK) >> SBC_FS23_FS_WDW_PERIOD_SHIFT));
            }
#endif
        }
    }
    return ReturnValue;
}

#if (STD_ON == SBC_FS23_SET_AMUX_API)
/*================================================================================================*/
/**
* @brief        Configure Analog channel delivered to MUX_OUT pin. SID is 0x09.
* @details
*
* @param[in]    AmuxChannel        Analog channel selected.
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
/** @implements   Sbc_fs23_SetAmux_Activity */
Std_ReturnType Sbc_fs23_SetAmux(const Sbc_fs23_AmuxChannelType AmuxChannel, const Sbc_fs23_AmuxDivType AmuxDiv)
{

    Std_ReturnType ReturnValue = (Std_ReturnType)E_OK; /* Status. */
    uint16 u16RxData = 0U;
    uint16 u16TxData = 0U;
    if (FALSE == Sbc_fs23_IsInitialized)
    {
        /* Driver not initialized. */
        ReturnValue = (Std_ReturnType)E_NOT_OK;
#if (SBC_FS23_DEV_ERROR_DETECT == STD_ON)
        (void)Det_ReportError((uint16)CDD_SBC_FS23_MODULE_ID, (uint8)0, (uint8)SBC_FS23_SID_SET_AMUX, (uint8)SBC_FS23_E_UNINIT);
#endif /* SBC_FS23_DEV_ERROR_DETECT == STD_ON */
    }
    else if ((Std_ReturnType)E_OK != Sbc_fs23_CheckParameterSetAmux(AmuxChannel,AmuxDiv))
    {
        /*AmuxChannel out of range*/
        ReturnValue = (Std_ReturnType)E_NOT_OK;
#if (SBC_FS23_DEV_ERROR_DETECT == STD_ON)
        (void)Det_ReportError((uint16)CDD_SBC_FS23_MODULE_ID, (uint8)0, (uint8)SBC_FS23_SID_SET_AMUX, (uint8)SBC_FS23_E_PARAM_RANGE);
#endif
    }
    else
    {
        u16TxData = SBC_FS23_M_AMUX_CTRL_DEFAULT | SBC_FS23_M_AMUX_EN_ENABLED | (uint16)AmuxChannel | (uint16)AmuxDiv;
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_AMUX_CTRL_ADDR,u16TxData);
        ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_M_AMUX_CTRL_ADDR, &u16RxData);

        if (((uint16)AmuxChannel != (u16RxData & SBC_FS23_M_AMUX_MASK)) || ((uint16)AmuxDiv != (u16RxData & SBC_FS23_M_AMUX_DIV_MASK)))
        {
            /*Amux channel configuration not written correctly.*/
            ReturnValue |= (Std_ReturnType)E_NOT_OK;
        }
    }
    return ReturnValue;
}
#endif/*STD_ON == SBC_FS23_SET_AMUX_API*/

#if (STD_ON == SBC_FS23_LDT_API)
/*================================================================================================*/
/**
* @brief        Configure Long time duration timer. SID is 0x0A.
* @details      Set operation mode, function, after run and wake up value of
*               Long duration timer
*
* @param[in]    LdtSettingId       The ID of Ldt setting.
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
/** @implements   Sbc_fs23_ConfigureLdt_Activity */
Std_ReturnType Sbc_fs23_ConfigureLdt(const uint8 LdtSettingId)
{

    Std_ReturnType ReturnValue = (Std_ReturnType)E_OK; /* Status. */

    if (FALSE == Sbc_fs23_IsInitialized)
    {
        /* Driver not initialized. */
        ReturnValue = (Std_ReturnType)E_NOT_OK;
#if (SBC_FS23_DEV_ERROR_DETECT == STD_ON)
        (void)Det_ReportError((uint16)CDD_SBC_FS23_MODULE_ID, (uint8)0, (uint8)SBC_FS23_SID_CONFIG_LDT, (uint8)SBC_FS23_E_UNINIT);
#endif /* SBC_FS23_DEV_ERROR_DETECT == STD_ON */
    }
    else if (LdtSettingId >= Sbc_fs23_pConfigPtr->LdtConfigNum)
    {
        /*LdtSettingId out of range*/
        ReturnValue = (Std_ReturnType)E_NOT_OK;
#if (SBC_FS23_DEV_ERROR_DETECT == STD_ON)
        (void)Det_ReportError((uint16)CDD_SBC_FS23_MODULE_ID, (uint8)0, (uint8)SBC_FS23_SID_CONFIG_LDT, (uint8)SBC_FS23_E_PARAM_RANGE);
#endif
    }
    else
    {
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_LDT_CFG1_ADDR,(*Sbc_fs23_pConfigPtr->Sbc_fs23_LdtConfig)[LdtSettingId].LdtCfg1Reg);
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_LDT_CFG2_ADDR,(*Sbc_fs23_pConfigPtr->Sbc_fs23_LdtConfig)[LdtSettingId].LdtCfg2Reg);
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_LDT_CFG3_ADDR,(*Sbc_fs23_pConfigPtr->Sbc_fs23_LdtConfig)[LdtSettingId].LdtCfg3Reg);
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_LDT_CTRL_ADDR,(*Sbc_fs23_pConfigPtr->Sbc_fs23_LdtConfig)[LdtSettingId].LdtCtrlReg);
    }
    return ReturnValue;
}

/*================================================================================================*/
/**
* @brief        Trigger Long time duration timer to run. SID is 0x0B.
* @details
*
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
/** @implements   Sbc_fs23_StartLdt_Activity */
Std_ReturnType Sbc_fs23_StartLdt(void)
{

    Std_ReturnType ReturnValue = (Std_ReturnType)E_OK; /* Status. */
    uint16 u16RxData = 0U;
    uint16 u16TxData = 0U;

    if (FALSE == Sbc_fs23_IsInitialized)
    {
        /* Driver not initialized. */
        ReturnValue = (Std_ReturnType)E_NOT_OK;
#if (SBC_FS23_DEV_ERROR_DETECT == STD_ON)
        (void)Det_ReportError((uint16)CDD_SBC_FS23_MODULE_ID, (uint8)0, (uint8)SBC_FS23_SID_START_LDT, (uint8)SBC_FS23_E_UNINIT);
#endif /* SBC_FS23_DEV_ERROR_DETECT == STD_ON */
    }
    else
    {
        ReturnValue = Sbc_fs23_ReadRegister(SBC_FS23_M_LDT_CTRL_ADDR,&u16RxData);
        if (ReturnValue == (Std_ReturnType)E_OK)
        {
            /*Set LDT_EN bit*/
            u16TxData = ((u16RxData & (SBC_FS23_M_LDT_MODE_MASK|SBC_FS23_M_LDT_SEL_MASK|SBC_FS23_M_LDT_FNCT_MASK|SBC_FS23_M_LDT2LP_MASK)) | SBC_FS23_M_LDT_EN_ENABLED);
            ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_LDT_CTRL_ADDR,u16TxData);
            ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_M_LDT_CTRL_ADDR, &u16RxData);

            if (SBC_FS23_M_LDT_EN_ENABLED != (u16RxData & SBC_FS23_M_LDT_EN_MASK))
            {
                /*Long during timer fail to start*/
                ReturnValue = (Std_ReturnType)E_NOT_OK;
            }
        }
    }
    return ReturnValue;
}

/*================================================================================================*/
/**
* @brief        Stop Long time duration timer and get the counter value. SID is 0x0C.
* @details
*
* @param[out]   CounterValue       Pointer to Long duration timer value storage.
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
/** @implements  Sbc_fs23_GetLdtValue_Activity */
Std_ReturnType Sbc_fs23_GetLdtValue(uint32 *CounterValue)
{

    Std_ReturnType ReturnValue = (Std_ReturnType)E_OK; /* Status. */
    uint32 u32TempValue = 0U;
    uint16 u16RxData1 = 0U;
    uint16 u16RxData2 = 0U;
    uint16 u16TxData = 0U;
    if (NULL_PTR == CounterValue)
    {
        ReturnValue = E_NOT_OK;
#if (SBC_FS23_DEV_ERROR_DETECT == STD_ON)
        (void)Det_ReportError((uint16)CDD_SBC_FS23_MODULE_ID, (uint8)0, (uint8)SBC_FS23_SID_GET_LDT_COUNTER, (uint8)SBC_FS23_E_PARAM_POINTER);
#endif /* SBC_FS23_DEV_ERROR_DETECT == STD_ON */
    }
    else if (FALSE == Sbc_fs23_IsInitialized)
    {
        /* Driver not initialized. */
        ReturnValue = E_NOT_OK;
#if (SBC_FS23_DEV_ERROR_DETECT == STD_ON)
        (void)Det_ReportError((uint16)CDD_SBC_FS23_MODULE_ID, (uint8)0, (uint8)SBC_FS23_SID_GET_LDT_COUNTER, (uint8)SBC_FS23_E_UNINIT);
#endif /* SBC_FS23_DEV_ERROR_DETECT == STD_ON */
    }
    else
    {
        ReturnValue = Sbc_fs23_ReadRegister(SBC_FS23_M_LDT_CTRL_ADDR,&u16RxData1);
        if (ReturnValue == (Std_ReturnType)E_OK)
        {
            /*Stop Long duration timer by clearing LDT_EN bit*/
            u16TxData = (uint8)(u16RxData1 & (SBC_FS23_M_LDT_MODE_MASK|SBC_FS23_M_LDT_SEL_MASK|SBC_FS23_M_LDT_FNCT_MASK|SBC_FS23_M_LDT2LP_MASK|(~SBC_FS23_M_LDT_EN_MASK)));
            //SchM_Enter_Sbc_fs23_SBC_FS23_EXCLUSIVE_AREA_02();
            ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_LDT_CTRL_ADDR,u16TxData);
            ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_M_LDT_CTRL_ADDR, &u16RxData1);

            if (SBC_FS23_M_LDT_RUN_IDLE != (u16RxData1 & SBC_FS23_M_LDT_RUN_MASK))
            {
                /*Long during timer fail to stop*/
                ReturnValue = (Std_ReturnType)E_NOT_OK;
            }
            else
            {
                /*Set LDT_SEL bit to read real time value of LDT*/
                u16TxData = (uint8)((u16RxData1 & (SBC_FS23_M_LDT_MODE_MASK|SBC_FS23_M_LDT_FNCT_MASK|SBC_FS23_M_LDT2LP_MASK|SBC_FS23_M_LDT_EN_MASK)) | SBC_FS23_M_LDT_SEL_REALTIME);
                ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_LDT_CTRL_ADDR,u16TxData);
                /*Read Ldt value*/
                ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_M_LDT_CFG2_ADDR,&u16RxData1);
                ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_M_LDT_CFG3_ADDR,&u16RxData2);
            }
            //SchM_Exit_Sbc_fs23_SBC_FS23_EXCLUSIVE_AREA_02();
            if ((Std_ReturnType)E_OK == ReturnValue)
            {
                u32TempValue = (uint32)(((uint32)u16RxData2 & SBC_FS23_M_LDT_WUP_H_MASK) << 16U);
                u32TempValue = (uint32)(u32TempValue | ((uint32)u16RxData1 & SBC_FS23_M_LDT_WUP_L_MASK));
                *CounterValue = (uint32)u32TempValue;
            }
        }
    }
    return ReturnValue;
}
#endif/*STD_ON = SBC_FS23_LDT_API*/

#if (STD_ON == SBC_FS23_HSX_API)
/**
* @brief        Configure High-side driver. SID is 0x13.
* @details      Select source for HSx pins, configure timer and pwm values
*
* @param[in]    HSxSettingId    The ID of Hsx setting.
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
/** @implements   Sbc_fs23_ConfigureHSx_Activity */
Std_ReturnType Sbc_fs23_ConfigureHSx(const uint8 HSxSettingId)
{
    Std_ReturnType ReturnValue = (Std_ReturnType)E_OK; /* Status. */

    if (FALSE == Sbc_fs23_IsInitialized)
    {
        /* Driver not initialized. */
        ReturnValue = (Std_ReturnType)E_NOT_OK;
#if (SBC_FS23_DEV_ERROR_DETECT == STD_ON)
        (void)Det_ReportError((uint16)CDD_SBC_FS23_MODULE_ID, (uint8)0, (uint8)SBC_FS23_SID_CONFIG_HSX, (uint8)SBC_FS23_E_UNINIT);
#endif /* SBC_FS23_DEV_ERROR_DETECT == STD_ON */
    }
    else if (HSxSettingId >= Sbc_fs23_pConfigPtr->HsxConfigNum)
    {
        /*LdtSettingId out of range*/
        ReturnValue = (Std_ReturnType)E_NOT_OK;
#if (SBC_FS23_DEV_ERROR_DETECT == STD_ON)
        (void)Det_ReportError((uint16)CDD_SBC_FS23_MODULE_ID, (uint8)0, (uint8)SBC_FS23_SID_CONFIG_HSX, (uint8)SBC_FS23_E_PARAM_RANGE);
#endif
    }
    else
    {
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_TIMER1_CFG_ADDR,(*Sbc_fs23_pConfigPtr->Sbc_fs23_HsxConfig)[HSxSettingId].Timer1Reg);
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_TIMER2_CFG_ADDR,(*Sbc_fs23_pConfigPtr->Sbc_fs23_HsxConfig)[HSxSettingId].Timer2Reg);
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_TIMER3_CFG_ADDR,(*Sbc_fs23_pConfigPtr->Sbc_fs23_HsxConfig)[HSxSettingId].Timer3Reg);
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_PWM1_CFG_ADDR,(*Sbc_fs23_pConfigPtr->Sbc_fs23_HsxConfig)[HSxSettingId].Pwm1Reg);
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_PWM2_CFG_ADDR,(*Sbc_fs23_pConfigPtr->Sbc_fs23_HsxConfig)[HSxSettingId].Pwm2Reg);
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_PWM3_CFG_ADDR,(*Sbc_fs23_pConfigPtr->Sbc_fs23_HsxConfig)[HSxSettingId].Pwm3Reg);
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_HSX_SRC_CFG_ADDR,(*Sbc_fs23_pConfigPtr->Sbc_fs23_HsxConfig)[HSxSettingId].HsxSrcReg);
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_HSX_CTRL_ADDR,(*Sbc_fs23_pConfigPtr->Sbc_fs23_HsxConfig)[HSxSettingId].HsxCtrlReg);
    }
    return ReturnValue;
}

/**
* @brief        Enable/Disable High-side driver sources. SID is 0x14.
* @details
*
* @param[in]    SourceEnable    High-side driver sources to be enabled/disable.
* @return       Std_ReturnType  Return code.
*
* @api
*/
/** @implements   Sbc_fs23_SetHSxSource_Activity */
Std_ReturnType Sbc_fs23_SetHSxSource(Sbc_fs23_HSxSourceType SourceEnable)
{
    Std_ReturnType ReturnValue = (Std_ReturnType)E_OK; /* Status. */
    uint16 u16TxData = 0U;
    if (FALSE == Sbc_fs23_IsInitialized)
    {
        /* Driver not initialized. */
        ReturnValue = (Std_ReturnType)E_NOT_OK;
#if (SBC_FS23_DEV_ERROR_DETECT == STD_ON)
        (void)Det_ReportError((uint16)CDD_SBC_FS23_MODULE_ID, (uint8)0, (uint8)SBC_FS23_SID_SET_HSX_SOURCE, (uint8)SBC_FS23_E_UNINIT);
#endif /* SBC_FS23_DEV_ERROR_DETECT == STD_ON */
    }
    else
    {
        u16TxData |= ((TRUE == SourceEnable.Timer1En) ? SBC_FS23_M_TIM1_EN_ENABLED : SBC_FS23_M_TIM1_EN_DISABLED);
        u16TxData |= ((TRUE == SourceEnable.Timer2En) ? SBC_FS23_M_TIM2_EN_ENABLED : SBC_FS23_M_TIM2_EN_DISABLED);
        u16TxData |= ((TRUE == SourceEnable.Timer3En) ? SBC_FS23_M_TIM3_EN_ENABLED : SBC_FS23_M_TIM3_EN_DISABLED);
        u16TxData |= ((TRUE == SourceEnable.Pwm1En) ? SBC_FS23_M_PWM1_EN_ENABLED : SBC_FS23_M_PWM1_EN_DISABLED);
        u16TxData |= ((TRUE == SourceEnable.Pwm2En) ? SBC_FS23_M_PWM2_EN_ENABLED : SBC_FS23_M_PWM2_EN_DISABLED);
        u16TxData |= ((TRUE == SourceEnable.Pwm3En) ? SBC_FS23_M_PWM3_EN_ENABLED : SBC_FS23_M_PWM3_EN_DISABLED);
        ReturnValue = Sbc_fs23_WriteRegister(SBC_FS23_M_TIMER_PWM_CTRL_ADDR,u16TxData);
    }
    return ReturnValue;
}
#endif/* STD_ON == SBC_FS23_HSX_API */

#if (STD_ON == SBC_FS23_WAKEUP_API)
/*================================================================================================*/
/**
* @brief        Configure Long time duration timer. SID is 0x0D.
* @details      Set operation mode, function, after run and wake up value of
*               Long duration timer
*
* @param[in]    WakeupSettingId       The ID of Wakeup setting.
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
/** @implements   Sbc_fs23_SetWakeup_Activity */
Std_ReturnType Sbc_fs23_SetWakeup(const uint8 WakeupSettingId)
{

    Std_ReturnType ReturnValue = (Std_ReturnType)E_OK; /* Status. */
    uint16 u16TxData;
    uint16 u16RxData = 0U;
    if (FALSE == Sbc_fs23_IsInitialized)
    {
        /* Driver not initialized. */
        ReturnValue = (Std_ReturnType)E_NOT_OK;
#if (SBC_FS23_DEV_ERROR_DETECT == STD_ON)
        (void)Det_ReportError((uint16)CDD_SBC_FS23_MODULE_ID, (uint8)0, (uint8)SBC_FS23_SID_SET_WAKEUP, (uint8)SBC_FS23_E_UNINIT);
#endif /* SBC_FS23_DEV_ERROR_DETECT == STD_ON */
    }
    else if (WakeupSettingId >= Sbc_fs23_pConfigPtr->WakeupConfigNum)
    {
        /*LdtSettingId out of range*/
        ReturnValue = (Std_ReturnType)E_NOT_OK;
#if (SBC_FS23_DEV_ERROR_DETECT == STD_ON)
        (void)Det_ReportError((uint16)CDD_SBC_FS23_MODULE_ID, (uint8)0, (uint8)SBC_FS23_SID_SET_WAKEUP, (uint8)SBC_FS23_E_PARAM_RANGE);
#endif
    }
    else
    {
        /* Configure IOWWU_CFG register */
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_IOWU_CFG_ADDR,(*Sbc_fs23_pConfigPtr->Sbc_fs23_WuConfig)[WakeupSettingId].IoWuCfgReg);
        /* Configure IOWWU_EN register */
        ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_M_IOWU_EN_ADDR,&u16RxData);
        u16TxData = (u16RxData & (~(SBC_FS23_M_WK1_WUEN_WAKEUP_MASK | SBC_FS23_M_WK2_WUEN_WAKEUP_MASK | SBC_FS23_M_HVIO1_WUEN_WAKEUP_MASK | SBC_FS23_M_HVIO2_WUEN_WAKEUP_MASK | SBC_FS23_M_LVIO3_WUEN_WAKEUP_MASK | SBC_FS23_M_LVIO4_WUEN_WAKEUP_MASK | SBC_FS23_M_LVI5_WUEN_WAKEUP_MASK))) | (*Sbc_fs23_pConfigPtr->Sbc_fs23_WuConfig)[WakeupSettingId].IoWuEnReg;
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_IOWU_EN_ADDR,u16TxData);
        /* Configure WU1_EN register */
        ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_M_WU1_EN_ADDR,&u16RxData);
        u16TxData = (u16RxData & (~(SBC_FS23_M_CAN_WUEN_WAKEUP_MASK | SBC_FS23_M_LIN_WUEN_WAKEUP_MASK | SBC_FS23_M_LDT_WUEN_WAKEUP_MASK))) | (*Sbc_fs23_pConfigPtr->Sbc_fs23_WuConfig)[WakeupSettingId].Wu1EnReg;
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_WU1_EN_ADDR,u16TxData);
        /* Configure CS_CFG register */
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_CS_CFG_ADDR,(*Sbc_fs23_pConfigPtr->Sbc_fs23_WuConfig)[WakeupSettingId].CsCfgReg);
    }
    return ReturnValue;
}

/*================================================================================================*/
/**
* @brief        Reads device wake up flags. SID is 0x0E.
* @details      This function reads device wake up flags.
*               The device has dedicated wake up flags for IO pins, LDT, CAN or LIN sources.
*
* @param[out]   WakeupReason   Status of wake up flags.
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
/** @implements   Sbc_fs23_GetWakeup_Activity */
Std_ReturnType Sbc_fs23_GetWakeup(Sbc_fs23_WakeupReasonType* WakeupReason)
{
    Std_ReturnType ReturnValue = (Std_ReturnType)E_OK; /* Status. */
    Sbc_fs23_WakeupReasonType TempWuReason = SBC_FS23_NO_WU_EVENT;
    if (FALSE == Sbc_fs23_IsInitialized)
    {
        /* Driver not initialized. */
        ReturnValue = (Std_ReturnType)E_NOT_OK;
#if (SBC_FS23_DEV_ERROR_DETECT == STD_ON)
        (void)Det_ReportError((uint16)CDD_SBC_FS23_MODULE_ID, (uint8)0, (uint8)SBC_FS23_SID_GET_WAKEUP, (uint8)SBC_FS23_E_UNINIT);
#endif /* SBC_FS23_DEV_ERROR_DETECT == STD_ON */
    }
    else if (NULL_PTR == WakeupReason)
    {
        ReturnValue = (Std_ReturnType)E_NOT_OK;
#if (SBC_FS23_DEV_ERROR_DETECT == STD_ON)
        (void)Det_ReportError((uint16)CDD_SBC_FS23_MODULE_ID, (uint8)0, (uint8)SBC_FS23_SID_GET_WAKEUP, (uint8)SBC_FS23_E_PARAM_POINTER);
#endif /* SBC_FS23_DEV_ERROR_DETECT == STD_ON */
    }
    else
    {
        ReturnValue |= Sbc_fs23_GetIoWuStatus(&TempWuReason);
        if(TempWuReason == SBC_FS23_NO_WU_EVENT)
        {
            ReturnValue |= Sbc_fs23_GetWu1Status(&TempWuReason);
        }
        if((Std_ReturnType)E_OK == ReturnValue)
        {
            *WakeupReason = TempWuReason;
        }
    }
    return ReturnValue;
}
#endif/*STD_ON = SBC_FS23_WAKEUP_API*/

/*================================================================================================*/
/**
* @brief        Switch SBC device back to Init mode. SID is 0x03.
* @details      This function is used to switch device back to Init state and then users can re-configure
*               the Fail-safe register. To make sure every register is set before close the Init phase,
*               the requesting watchdog refresh must be ignore.
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
/** @implements   Sbc_fs23_GoToInitState_Activity */
Std_ReturnType Sbc_fs23_GoToInitState(void)
{

    Std_ReturnType ReturnValue = (Std_ReturnType)E_OK;            /* Status. */
    uint16 u16RxData = 0U;
    if (FALSE == Sbc_fs23_IsInitialized)
    {
        /* Driver not initialized. */
        ReturnValue = (Std_ReturnType)E_NOT_OK;
#if (SBC_FS23_DEV_ERROR_DETECT == STD_ON)
        (void)Det_ReportError((uint16)CDD_SBC_FS23_MODULE_ID, (uint8)0, (uint8)SBC_FS23_SID_GOTO_INIT, (uint8)SBC_FS23_E_UNINIT);
#endif /* SBC_FS23_DEV_ERROR_DETECT == STD_ON */
    }
    else
    {
        /* Check if watchdog infinite or not */
        ReturnValue = Sbc_fs23_ReadRegister(SBC_FS23_FS_WDW_CFG_ADDR, &u16RxData);
        if (((Std_ReturnType)E_OK == ReturnValue) && ((uint16)(u16RxData & SBC_FS23_FS_WDW_PERIOD_MASK) != SBC_FS23_FS_WDW_PERIOD_INFINITE))
        {
            /* Prevent external watchdog trigger closing INIT immediately. */
            Sbc_fs23_bGateWdgTrigger = TRUE;
        }

        /* Come back to INIT_FS */
        ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_M_SYS_CFG_ADDR, &u16RxData);
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_SYS_CFG_ADDR, (u16RxData | SBC_FS23_M_GO2INIT_MASK));
        ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_M_STATUS_ADDR, &u16RxData);
        if (SBC_FS23_M_INIT_S_INIT != (u16RxData & SBC_FS23_M_INIT_S_MASK))
        {
            ReturnValue |= (Std_ReturnType)E_NOT_OK;
        }
    }
    return ReturnValue;
}

#if(STD_ON == SBC_FS23_SET_REGULATOR_API)
/*================================================================================================*/
/**
* @brief        Sets state (enable/disable) of the selected voltage regulator. SID is 0x0F.
*
* @param[in]    Vreg                Voltage regulator.
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
/** @implements   Sbc_fs23_SetRegulatorState_Activity */
Std_ReturnType Sbc_fs23_SetRegulatorState(Sbc_fs23_RegOutputType Vreg)
{

    Std_ReturnType ReturnValue = (Std_ReturnType)E_OK;            /* Status. */
    uint16 u16TxData = 0U;
    uint16 u16RxData = 0U;
    if (FALSE == Sbc_fs23_IsInitialized)
    {
        /* Driver not initialized. */
        ReturnValue = (Std_ReturnType)E_NOT_OK;
#if (SBC_FS23_DEV_ERROR_DETECT == STD_ON)
        (void)Det_ReportError((uint16)CDD_SBC_FS23_MODULE_ID, (uint8)0, (uint8)SBC_FS23_SID_SET_REGULATOR_STATE, (uint8)SBC_FS23_E_UNINIT);
#endif /* SBC_FS23_DEV_ERROR_DETECT == STD_ON */
    }
    else
    {
        ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_M_REG_CTRL_ADDR, &u16RxData);
        /*Enable or Disable V1/V2 */
        u16TxData = u16RxData;
        u16TxData |= (uint8)((TRUE == Vreg.V2Enable) ? SBC_FS23_M_V2EN_MASK : SBC_FS23_M_V2DIS_MASK);
        u16TxData |= (uint8)((TRUE == Vreg.V3Enable) ? SBC_FS23_M_V3EN_MASK : SBC_FS23_M_V3DIS_MASK);
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_M_REG_CTRL_ADDR, u16TxData);
        /*Re-check state of regulators*/
        ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_M_STATUS_ADDR, &u16RxData);
        if(((TRUE == Vreg.V2Enable) && ((u16RxData & SBC_FS23_M_V2_S_MASK) != SBC_FS23_M_V2_S_ENABLED))
        || ((TRUE == Vreg.V3Enable) && ((u16RxData & SBC_FS23_M_V3_S_MASK) != SBC_FS23_M_V3_S_ENABLED)))
        {
            ReturnValue |= (Std_ReturnType)E_NOT_OK;
        }
    }
    return ReturnValue;
}
#endif/*STD_ON == SBC_FS23_SET_REGULATOR_API */

#if(STD_ON == SBC_FS23_SET_OPMODE_API)
/*================================================================================================*/
/**
* @brief        Device enter Low Power Mode. SID is 0x12.
*
* @param[in]    OpMode          Operating mode of the device (NORMAL / LPON / LPOFF).
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
/** @implements   Sbc_fs23_SetOperatingMode_Activity */
Std_ReturnType Sbc_fs23_SetOperatingMode(Sbc_fs23_OpModeType OpMode)
{
    Std_ReturnType ReturnValue = (Std_ReturnType)E_OK;           /* Status. */
    uint16 u16RxData = 0U;
    if (FALSE == Sbc_fs23_IsInitialized)
    {
        /* Driver not initialized. */
        ReturnValue = (Std_ReturnType)E_NOT_OK;
#if (SBC_FS23_DEV_ERROR_DETECT == STD_ON)
        (void)Det_ReportError((uint16)CDD_SBC_FS23_MODULE_ID, (uint8)0, (uint8)SBC_FS23_SID_SET_OP_MODE, (uint8)SBC_FS23_E_UNINIT);
#endif /* SBC_FS23_DEV_ERROR_DETECT == STD_ON */
    }
    else if ((SBC_FS23_OPMODE_NORMAL != OpMode) && (SBC_FS23_OPMODE_LPON != OpMode) && (SBC_FS23_OPMODE_LPOFF != OpMode))
    {
        /* Parameter out of range. */
        ReturnValue = (Std_ReturnType)E_NOT_OK;
#if (SBC_FS23_DEV_ERROR_DETECT == STD_ON)
        (void)Det_ReportError((uint16)CDD_SBC_FS23_MODULE_ID, (uint8)0, (uint8)SBC_FS23_SID_SET_OP_MODE, (uint8)SBC_FS23_E_PARAM_RANGE);
#endif /* SBC_FS23_DEV_ERROR_DETECT == STD_ON */
    }
    else
    {
        /* Enter critical section. */
        //SchM_Enter_Sbc_fs23_SBC_FS23_EXCLUSIVE_AREA_03();
        ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_M_STATUS_ADDR,&u16RxData);
        switch (OpMode)
        {
            case SBC_FS23_OPMODE_NORMAL:
                /* Switch to normal mode from LPON*/
                if ((u16RxData & SBC_FS23_M_LPON_S_MASK) == SBC_FS23_M_LPON_S_LPON)
                {
                    ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_M_SYS_CFG_ADDR,&u16RxData);
                    ReturnValue |=  Sbc_fs23_WriteRegister(SBC_FS23_M_SYS_CFG_ADDR, (u16RxData | SBC_FS23_M_GO2NORMAL_NORMAL));
                }
                else
                {
                    ReturnValue |= (Std_ReturnType)E_NOT_OK;
                }
                break;
            case SBC_FS23_OPMODE_LPON:
                /* Switch to LPON mode from NORMMAL*/
                if ((u16RxData & SBC_FS23_M_NORMAL_S_MASK) == SBC_FS23_M_NORMAL_S_NORMAL)
                {
                    ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_M_SYS_CFG_ADDR,&u16RxData);
                    ReturnValue |=  Sbc_fs23_WriteRegister(SBC_FS23_M_SYS_CFG_ADDR,(u16RxData | SBC_FS23_M_GO2LPON_LPON));
                }
                else
                {
                    ReturnValue |= (Std_ReturnType)E_NOT_OK;
                }
                break;
            case SBC_FS23_OPMODE_LPOFF:
                /* Switch to LPOFF mode from NORMAL*/
                if ((u16RxData & SBC_FS23_M_NORMAL_S_MASK) == SBC_FS23_M_NORMAL_S_NORMAL)
                {
                    ReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_M_SYS_CFG_ADDR,&u16RxData);
                    ReturnValue |=  Sbc_fs23_WriteRegister(SBC_FS23_M_SYS_CFG_ADDR,(u16RxData | SBC_FS23_M_GO2LPOFF_LPOFF));
                }
                else
                {
                    ReturnValue |= (Std_ReturnType)E_NOT_OK;
                }
                break;
            default:
                /*Invalid input parameter*/
                ReturnValue |= (Std_ReturnType)E_NOT_OK;
                break;
        }
        /* Exit critical section. */
        //SchM_Exit_Sbc_fs23_SBC_FS23_EXCLUSIVE_AREA_03();
    }
    return ReturnValue;
}
#endif/*STD_ON == SBC_FS23_SET_OPMODE_API */

/*================================================================================================*/
/**
* @brief        Requests a low level on the selected fail-safe output. SID is 0x10.
*
* @param[in]    FsRequest           FS output to be asserted low.
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
/** @implements   Sbc_fs23_FsOutputRequest_Activity */
Std_ReturnType Sbc_fs23_FsOutputRequest(Sbc_fs23_FsRequestType FsRequest)
{
    Std_ReturnType ReturnValue = E_OK;            /* Status. */
    uint16 u16TxData = 0U;
    if (FALSE == Sbc_fs23_IsInitialized)
    {
        /* Driver not initialized. */
        ReturnValue = E_NOT_OK;
#if (SBC_FS23_DEV_ERROR_DETECT == STD_ON)
        (void)Det_ReportError((uint16)CDD_SBC_FS23_MODULE_ID, (uint8)0, (uint8)SBC_FS23_SID_OUTPUT_REQUEST, (uint8)SBC_FS23_E_UNINIT);
#endif /* SBC_FS23_DEV_ERROR_DETECT == STD_ON */
    }
    else if ((Std_ReturnType)E_OK != Sbc_fs23_CheckParameterRequestOutput(FsRequest))
    {
        /* Parameter out of range. */
        ReturnValue = E_NOT_OK;
#if (SBC_FS23_DEV_ERROR_DETECT == STD_ON)
        (void)Det_ReportError((uint16)CDD_SBC_FS23_MODULE_ID, (uint8)0, (uint8)SBC_FS23_SID_OUTPUT_REQUEST, (uint8)SBC_FS23_E_PARAM_RANGE);
#endif /* SBC_FS23_DEV_ERROR_DETECT == STD_ON */
    }
    else
    {
        u16TxData |= ((TRUE == FsRequest.Fs0bRequest) ? SBC_FS23_FS_FS0B_REQ_ASSERT : SBC_FS23_FS_FS0B_REQ_NO_ACTION);
        u16TxData |= ((TRUE == FsRequest.Limp0Request) ? SBC_FS23_FS_LIMP0_REQ_ASSERT : SBC_FS23_FS_LIMP0_REQ_NO_ACTION);
        u16TxData |= ((TRUE == FsRequest.RstbRequest) ? SBC_FS23_FS_RSTB_REQ_ASSERT : SBC_FS23_FS_RSTB_REQ_NO_ACTION);
        ReturnValue = Sbc_fs23_WriteRegister(SBC_FS23_FS_SAFETY_OUTPUTS_ADDR,u16TxData);
    }
    return ReturnValue;
}

/*================================================================================================*/
/**
* @brief         Fail-safe output release routine. SID is 0x11.
*
* @param[in]    FsOutput       FS output to be released.
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
/** @implements   Sbc_fs23_FsOutputRelease_Activity */
Std_ReturnType Sbc_fs23_FsOutputRelease(Sbc_fs23_FsOutputType FsOutput)
{
    uint16 ReleaseWrite = 0U;               /* Value to be written to the FS_FS0B_LIMP0_REL register. */
    uint8 ErrorCounter = 0U;               /* Fault Error Counter value. */
    Std_ReturnType ReturnValue = (Std_ReturnType)E_OK;     /* Status. */

    if (FALSE == Sbc_fs23_IsInitialized)
    {
        /* Driver not initialized. */
        ReturnValue = (Std_ReturnType)E_NOT_OK;
    }
    else if ((Std_ReturnType)E_OK != Sbc_fs23_CheckParameterFsxbRelease(FsOutput))
    {
        /* Parameter out of range. */
        ReturnValue = (Std_ReturnType)E_NOT_OK;
    }
    else
    {
        /* Fault Error Counter = 0 */
        ReturnValue = Sbc_fs23_GetFaultErrorCounter(&ErrorCounter);
        if (((Std_ReturnType)E_OK == ReturnValue) && (ErrorCounter != (uint8)0U))
        {
            ReturnValue = Sbc_fs23_ClearFaultErrorCounter();
        }
        /* RELEASE_FS0B_LIMP0 register filled with ongoing WD_TOKEN reversed and complemented */
        //SchM_Enter_Sbc_fs23_SBC_FS23_EXCLUSIVE_AREA_01();
        ReturnValue |= Sbc_fs23_CalculateReleaseFsxb(FsOutput, &ReleaseWrite);
        ReturnValue |= Sbc_fs23_WriteRegister(SBC_FS23_FS_FS0B_LIMP0_REL_ADDR,ReleaseWrite);
        //SchM_Exit_Sbc_fs23_SBC_FS23_EXCLUSIVE_AREA_01();
        /*Delay to wait for FSxB released*/
        ReturnValue |= Sbc_fs23_TimeWaitRelease(FsOutput);
    }

    return ReturnValue;
}

/*================================================================================================*/
/**
* @brief        Deinitializes the FS23 driver. SID is 0x13.
* @details      Clears the internal run-time configuration.
*
* @return       void
*
* @api
*/
/** @implements   Sbc_fs23_Deinit_Activity */
void Sbc_fs23_Deinit(void)
{
    if (FALSE == Sbc_fs23_IsInitialized)
    {
#if (SBC_FS23_DEV_ERROR_DETECT == STD_ON)
        (void)Det_ReportError((uint16)CDD_SBC_FS23_MODULE_ID, (uint8)0, (uint8)SBC_FS23_SID_DEINIT, (uint8)SBC_FS23_E_UNINIT);
#endif /* SBC_FS23_DEV_ERROR_DETECT == STD_ON */
    }
    else
    {
        /* Clear the local copy of the driver initialization structure. */
        Sbc_fs23_pConfigPtr = NULL_PTR;

        /* Driver not initialized. */
        Sbc_fs23_IsInitialized = FALSE;
    }
}
#if (SBC_FS23_VERSION_INFO_API == STD_ON)
/*================================================================================================*/
/**
* @brief        FS23 driver get version info function. SID is 0x1F.
* @details      Returns the version information of this module.
*
* @param[out]   VersionInfo    Pointer to where to store the version information of this module.
*
* @return       void
*
* @api
*/
/** @implements   Sbc_fs23_GetVersionInfo_Activity */
void Sbc_fs23_GetVersionInfo(Std_VersionInfoType* VersionInfo)
{
#if (SBC_FS23_DEV_ERROR_DETECT == STD_ON)
    if (NULL_PTR == VersionInfo)
    {
        (void)Det_ReportError((uint16)CDD_SBC_FS23_MODULE_ID, (uint8)0, (uint8)SBC_FS23_SID_GET_VERSIONINFO, (uint8)SBC_FS23_E_PARAM_POINTER);
    }
    else
    {
#endif /* SBC_FS23_DEV_ERROR_DETECT == STD_ON */
    VersionInfo->vendorID = (uint16)CDD_SBC_FS23_VENDOR_ID;
    VersionInfo->moduleID = (uint8)CDD_SBC_FS23_MODULE_ID;
    VersionInfo->sw_major_version = (uint8)CDD_SBC_FS23_SW_MAJOR_VERSION;
    VersionInfo->sw_minor_version = (uint8)CDD_SBC_FS23_SW_MINOR_VERSION;
    VersionInfo->sw_patch_version = (uint8)CDD_SBC_FS23_SW_PATCH_VERSION;
#if(SBC_FS23_DEV_ERROR_DETECT == STD_ON)
    }
#endif /* SBC_FS23_DEV_ERROR_DETECT == STD_ON */
}
#endif /* SBC_FS23_VERSION_INFO_API == STD_ON */
#define SBC_FS23_STOP_SEC_CODE
//#include "Sbc_fs23_MemMap.h"

#ifdef __cplusplus
}
#endif

/** @} */
