/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * APA_CalibPara: 
 * Created on: 2022-11-25 17:01
 * Original designer: 22866
 ******************************************************************************/
#ifndef __VEHICLE_GEOMETRY_PARAMETER_H__
#define __VEHICLE_GEOMETRY_PARAMETER_H__

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "Vehicle_Geometry_Parameter_Types.h"


/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/




/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/* 两个车型的数据存储于Flash中 */


extern const Vehicl_Geometry_ParameterType Vehicl_Geometry_CarPara_X02;

/******************************Vehicl标定参数_存储在Ram中--*********************************************/
extern Vehicl_Geometry_ParameterType Vehicl_Geometry_Para_Ram;

void APP_Car_Geometry_Config_Init(void);



#endif
