/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * PDCSignalManage.h: 
 * Created on: 2020-10-22 10:20
 * Original designer: 21059
 ******************************************************************************/

#ifndef __PAS_MAP_SIGNALMANAGE_H__
#define __PAS_MAP_SIGNALMANAGE_H__

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "PAS_MAP_SignalManage_Types.h"

/******************************************************************************
 * Compiler option used to distinguish inclusion done by the owner
 *****************************************************************************/

/******************************************************************************
 * Inclusion of private header files
 *****************************************************************************/

void PdcSignal_MapObjInit(void);
void PdcSignal_USS_DE_Distace_Init(PDCSignal_SnsGroupType LenuGroup);
void PdcSignal_USS_CE_Distace_Init(PDCSignal_SnsGroupType LenuGroup);
void PdcSignal_ZoneDistanceInit(void);
void PdcPointCloudObjInit(void);



/******************* SDW工作状态函数接口 ***********/
void WriteSDWStatus(PdcSignal_SDWStatusType LenuSDWStatus);
void ReadSDWStatus(PdcSignal_SDWStatusType *LenuSDWStatus);


/*******************PDC工作状态函数接口*************/
void PdcSignal_WritePDCWorkStatus(PdcSignal_PDCWorkStatusType LenuPDCWorkStatus);
void PdcSignal_ReadPDCWorkStatus(PdcSignal_PDCWorkStatusType *LenuPDCWorkStatus);

/****************   距离分区         *************/
void PdcSignal_WriteZoneDistance(PDCSignal_ZoneDisType* LstrZoneDis);
void PdcSignal_ReadZoneDistance(PDCSignal_ZoneDisType *LstrZoneDis);

/* DE函数接口 */
void PdcSignal_WriteUSS_DE_Distace(PDCSignal_DEStructType LstrDE_Distance,PDCSignal_SnsGroupType LenuGroup);
void PdcSignal_ReadUSS_DE_Distace(PDCSignal_DEStructType *LstrDE_Distance,PDCSignal_SnsGroupType LenuGroup);

/* CE函数接口 */
void PdcSignal_WriteUSS_CE_Distace(PDCSignal_CEStructType LstrCE_Distance,PDCSignal_SnsGroupType LenuGroup);
void PdcSignal_ReadUSS_CE_Distace(PDCSignal_CEStructType *LstrCE_Distance,PDCSignal_SnsGroupType LenuGroup);

/* 障碍物MAP函数接口 */
void PdcSignal_WriteMapObj(PdcSignal_MapObjInfoType *LstrMapObjInfo,uint8 ObjIndex);
void PdcSignal_ReadMapObj(PdcSignal_MapObjInfoType *LstrMapObjInfo,uint8 ObjIndex);


/*拖车钩信号*/
void TrailerHitchDetected_init(void);
void WriteTrailerHitchDetected(PdcSignal_TrailerHitchDetectedType LenuTrailerHitchDetected);
void ReadTrailerHitchDetected(PdcSignal_TrailerHitchDetectedType *LenuTrailerHitchDetected);

/* 时间同步信号 */
void WriteLocaltime_afterSync(uint32 Lu32Localtime_afterSync);
void ReadLocaltime_afterSync(uint32*Lu32Localtime_afterSync);

/*点云数据信号*/
void PdcSignal_WritePointCloudObj(PdcPointCloud_ObjInfoType *LstrPointCloudObjInfo,uint8 ObjIndex);
void PdcSignal_ReadPointCloudMapObj(PdcPointCloud_ObjInfoType *LstrPointCloudObjInfo,uint8 ObjIndex);


/* 通过给车衣覆盖DTC的接口 */
void PdcSignal_WriteSnsBeCoveredFlagToDTC(uint8 Lu8SnsBeCoveredFlag,PdcSiagnal_SnsInxType LenuSnsInx);
void PdcSignal_ReadSnsBeCoveredFlagToDTC(uint8* Lpu8SnsBeCoveredFlag,PdcSiagnal_SnsInxType LenuSnsInx);



uint8 GetCarCoverErrorFlag(void);

extern uint8 Gu8PointCloudObjUpdateFlag[12];


#endif /* end of __PAS_MAP_SIGNALMANAGE_H__ */

