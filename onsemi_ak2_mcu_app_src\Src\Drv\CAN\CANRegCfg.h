/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: CANRegCfg.h
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/
#ifndef __CANCFG_H
#define __CANCFG_H

/***********************************宏定义*************************************/
/* Enable */
#define CAN_ENABLE                       (1U)
#define CAN_DISABLE                      (0U)

/* ---- Error display mode ---- */
#define CAN_ERRDISP_FIRST_ERR_ONLY       (0U)
#define CAN_ERRDISP_ALL_ERR              (1U)

/* ---- Bus off recovery mode ---- */
#define CAN_BOFF_RECOVERY_ISO11898       (0U)
#define CAN_BOFF_RECOVERY_START_ENTRY    (1U)
#define CAN_BOFF_RECOVERY_END_ENTRY      (2U)
#define CAN_BOFF_RECOVERY_MANUAL_ENTRY   (3U)




#endif
