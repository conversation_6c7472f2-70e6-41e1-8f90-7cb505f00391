"""
CRC8计算模块，用于计算和验证CRC8校验码
"""

class CRC8:
    """CRC8校验码计算类"""

    def __init__(self, polynomial=0x07, init_value=0xFF, reflect_in=False, reflect_out=False, xor_out=0x00):
        """
        初始化CRC8计算器

        Args:
            polynomial: 多项式，默认为0x07 (CRC-8-CCITT)
            init_value: 初始值，默认为0xFF
            reflect_in: 是否反转输入，默认为False
            reflect_out: 是否反转输出，默认为False
            xor_out: 输出异或值，默认为0x00
        """
        self.polynomial = polynomial
        self.init_value = init_value
        self.reflect_in = reflect_in
        self.reflect_out = reflect_out
        self.xor_out = xor_out
        self._generate_table()

    def _generate_table(self):
        """生成CRC8查找表，提高计算效率"""
        self.table = []
        for i in range(256):
            crc = i
            for _ in range(8):
                if crc & 0x80:
                    crc = (crc << 1) ^ self.polynomial
                else:
                    crc = crc << 1
                crc &= 0xFF
            self.table.append(crc)

    def _reflect_byte(self, byte):
        """
        反转字节的位序

        Args:
            byte: 待反转的字节

        Returns:
            反转后的字节
        """
        result = 0
        for i in range(8):
            if byte & (1 << i):
                result |= (1 << (7 - i))
        return result

    def calculate(self, data):
        """
        计算数据的CRC8校验码

        Args:
            data: 字节列表或字节数组

        Returns:
            计算得到的CRC8校验码
        """
        crc = self.init_value
        for byte in data:
            if self.reflect_in:
                byte = self._reflect_byte(byte)
            crc = self.table[(crc ^ byte) & 0xFF]

        if self.reflect_out:
            crc = self._reflect_byte(crc)

        return (crc ^ self.xor_out) & 0xFF

    def verify(self, data, crc_value):
        """
        验证数据的CRC8校验码是否正确

        Args:
            data: 字节列表或字节数组
            crc_value: 待验证的CRC8校验码

        Returns:
            校验结果，True表示校验通过，False表示校验失败
        """
        return self.calculate(data) == crc_value

    @staticmethod
    def create_crc8_c2():
        """
        创建CRC8-C2计算器，用于计算SPI响应的CRC8校验码

        Returns:
            CRC8计算器对象
        """
        return CRC8(polynomial=0x07, init_value=0xFF, reflect_in=False, reflect_out=False, xor_out=0x00)

    @staticmethod
    def create_crc8_custom():
        """
        创建自定义CRC8计算器，用于计算命令的CRC8校验码

        Returns:
            CRC8计算器对象
        """
        # 根据实际情况调整参数
        return CRC8(polynomial=0x07, init_value=0xFF, reflect_in=True, reflect_out=True, xor_out=0x00)

    @staticmethod
    def calculate_simple(data):
        """
        简单的CRC8计算方法，用于快速计算CRC8校验码

        Args:
            data: 字节列表或字节数组

        Returns:
            计算得到的CRC8校验码
        """
        crc = 0
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 0x80:
                    crc = ((crc << 1) ^ 0x07) & 0xFF
                else:
                    crc = (crc << 1) & 0xFF
        return crc
