"""
测试响应解析器
"""

import json
from command_parser.response_parser import ReadResponseParser

def parse_hex_string(hex_string):
    """
    解析十六进制字符串为字节列表

    Args:
        hex_string: 十六进制字符串，如"02 50 ff ff ff ff 7a"

    Returns:
        字节列表
    """
    # 移除所有空白字符
    hex_string = "".join(hex_string.split())

    # 检查字符串长度是否为偶数
    if len(hex_string) % 2 != 0:
        raise ValueError("十六进制字符串长度必须为偶数")

    # 将字符串转换为字节列表
    return [int(hex_string[i:i+2], 16) for i in range(0, len(hex_string), 2)]

def test_read_response_parser():
    """测试读操作响应解析器"""
    print("\n=== 测试读操作响应解析器 ===")

    # 创建读操作响应解析器
    parser = ReadResponseParser(0x11)  # 地址为0x11（REG17）

    # 测试用例1：读操作响应
    response_str = "00 83 00 00 00 00 92"
    response_bytes = parse_hex_string(response_str)

    # 解析响应
    result = parser.parse(response_bytes)

    print(f"读操作响应: {response_str}")
    print("解析结果:")
    print(json.dumps(result, indent=2, ensure_ascii=False))

    # 测试用例2：读操作响应，不同的数据值
    response_str2 = "00 83 12 34 56 78 92"
    response_bytes2 = parse_hex_string(response_str2)

    # 解析响应
    result2 = parser.parse(response_bytes2)

    print(f"\n读操作响应2: {response_str2}")
    print("解析结果2:")
    print(json.dumps(result2, indent=2, ensure_ascii=False))

    # 打印位字段描述
    if "bit_fields_desc" in result2:
        print("\n位字段描述:")
        for field_name, field_desc in result2["bit_fields_desc"].items():
            print(f"{field_name}: {field_desc}")

    # 打印位字段值和十六进制表示
    if "bit_fields" in result2 and "bit_fields_hex" in result2:
        print("\n位字段值和十六进制表示:")
        for field_name in result2["bit_fields"]:
            # 跳过内部字段
            if field_name.startswith("_"):
                continue

            field_value = result2["bit_fields"][field_name]
            field_hex = result2["bit_fields_hex"][field_name]
            field_desc = result2["bit_fields_desc"].get(field_name, "")
            if isinstance(field_value, dict) and "value" in field_value:
                print(f"{field_name}: 值={field_value['value']} ({field_hex}), 描述={field_desc}")
            else:
                print(f"{field_name}: 值={field_value} ({field_hex}), 描述={field_desc}")

if __name__ == "__main__":
    test_read_response_parser()
