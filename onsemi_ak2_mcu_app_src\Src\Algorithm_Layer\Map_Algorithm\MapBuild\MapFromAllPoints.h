/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * SnsBasicDataStru:
 * Created on: 2023-12-04 08:55
 * Original designer: 23374
 ******************************************************************************/

#ifndef MapFromAllPoints_H
#define MapFromAllPoints_H
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <float.h>
#include <math.h>
#include "SnsRawData_Type.h"
#include "MapCoorCalculate_Type.h"
#include "PublicCalAlgorithm_Int.h"
#include "MapBuild_Int.h"
#include "MapEchoFilterAndSigGroup_int.h"
//#include "debug.h"

#define DEBUG_PRINT_ON      0

#if DEBUG_PRINT_ON
#define DEBUG_PRINT         printf
#define DEBUG_HIGH_LOW      1      // 调试障碍物高低属性
#define DEBUG_DELETE_MAP    1      // 调试障碍物删除逻辑
#else
#define DEBUG_HIGH_LOW      0      // 调试障碍物高低属性
#define DEBUG_DELETE_MAP    0      // 调试障碍物删除逻辑
#endif

#define STACK_INIT_SIZE     101
#define LIST_INIT_SIZE      101
#define debug_print         0


#define HEIGHT_LOCK_LEVEL0  0
#define HEIGHT_LOCK_LEVEL1  1
#define HEIGHT_LOCK_LEVEL2  2
#define HEIGHT_LOCK_LEVEL3  3

/*高低属性兜底阈值*/
#define FRONT_MRL_HEIGHT_DIS                    560         //前保两个中间探头兜底置高阈值
#define FRONT_RL_HEIGHT_DIS                     350         //前保两个角探头兜底置高阈值
#define REAR_MRL_HEIGHT_DIS                     580         //后保两个中间探头兜底置高阈值
#define REAR_RL_HEIGHT_DIS                      350         //后保两个角探头兜底置高阈值
#define FRONT_BUMP_LOW_DIS                      560         //前保兜底置低阈值
#define REAR_BUMP_LOW_DIS                       580         //后保兜底置低阈值

typedef struct
{
    uint16 real_dist;
    uint32 u32Timestamp;
}SnsRealDist;

typedef struct
{
    float fObjX;            // 点云对应的 x 坐标
    float fObjY;            // 点云对应的 y 坐标
    uint32 u32Timestamp;    // 点云生成时对应的时间戳
    uint32 enuObjType;      // 点云对应障碍物类型（通过回波高度判定）
    uint32 eMeasType;       /* 点云产生对应的工作模式 */
    sint32 enuSnsCh;        // 点云所产生的对应探头
    uint16 u16MasterDis;    // 点云产生时，对应的主发距离
    uint16 u16MasterHeight; // 点云产生时，对应的回波高度
}ObjCoorInfoType;

typedef struct
{
    ObjCoorInfoType Buf[STACK_INIT_SIZE];
    int             u8BufCnt;
    uint8           u8StackSize;
}StackElem;

typedef struct
{
    ObjCoorInfoType Buf[LIST_INIT_SIZE];
    int             u8BufCnt;
    uint8           u8ListSize;
}ListElem;

typedef struct
{
    float A;
    float B;
    float C;
}Line;

/* 初始化 Stack 数据结构 */
void InitStackElem(void);

/* 往 Stack 添加数据 */
int StackElemPush(ObjCoorInfoType elem);

/* 判定 Stack 是否为空 */
int StackElemEmpty(void);

/* 获取 Stack 顶部元素 */
void GetStackElemTop(ObjCoorInfoType *elem);

/* 删除 Stack 顶部元素 */
int StackElemPop(void);

/* 初始化 List 数据结构 */
void InitListElem(void);

/* 往 List 添加数据 */
int ListElemInsert(ObjCoorInfoType elem);

/* 获取 List 长度 */
int ListElemLength(void);

/* 获取 List 指定位置数据 */
int GetListElemDate(int index, ObjCoorInfoType *elem);

/* 通过 PCA 计算点云所对应的线段 */
void PCADirCal(ObjCoorInfoType *points, SnsAreaMapType *mapInfo);

/* 拷贝全局点云 odo 坐标数据到临时 buffer */
int GetVechBufferOdoCoorData(ObjCoorInfoType *buffer_data, uint32 *time_stamp);

/* 拷贝全局点云 car 坐标数据到临时 buffer */
int GetVechBufferCarCoorData(ObjCoorType *buffer_data, int front_rear);

/* 查找 map 缓存里面距离车身最远的map所对应的索引 */
int FindFarthestMapIndex(int *is_replace);

/* 近距离仅有DE的情况下，生成对应的点 map 信息 */
uint8 DEGenerateMap(SnsAreaMapType *temp_map, PDCSnsGroupType group, PDCSnsChannelType channle, float dist_threshold, float *real_DE_dist);

/* 由前后全局点云聚类生成对应的 map 信息 */
void AllPointCloudGenerateMap(void);

/* 更新 map 缓存中障碍物的车辆坐标位置 */
void UpdateMapCarCoor(void);

/* 计算两个线段之间的距离 */
float CalTwoSegLineDist(ObjCoorType line1_p1, ObjCoorType line1_p2, ObjCoorType line2_p1, ObjCoorType line2_p2);

/* 计算两个 map 之间的距离 */
float CalTwoMapDist(ObjCoorType map1_p1, ObjCoorType map1_p2, ObjCoorType map2_p1, ObjCoorType map2_p2);

/* 判定map是否有对应的DE值与之相匹配 */
int JudgeMapMatchedSnsDe(ObjMapCoorType *LpstrMapCoor, float *real_de_dist, int *matched_channel);

/* 更新全局 global_map_buffer 的信息，map 的更新策略（是否删除及保留）*/
void UpdateMapBufferInfo(void);

/* 最新一帧 map 与匹配上的历史 map 做信息融合 */
void FusionFrontRearMap(int matched_id, SnsAreaMapType *new_map, int is_de_map);

/* 生成新的全局 map 信息 */
void CreateNewGlobalMap(SnsAreaMapType *new_map, uint8 generate_type);

/* 近距离情况下，单 DE 生成 map 信息 */
void Fusion_DE_Map(void);

/* map 融合算法 */
void VheMapFusion(void);

#endif /* MapFromAllPoints_H */