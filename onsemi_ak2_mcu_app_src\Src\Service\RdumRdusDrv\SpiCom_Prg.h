/******************************************************************************
 * @file       SpiCom_Prg.h
 * @brief
 * @date       2025-03-21 10:55:00
 * <AUTHOR>
 * @copyright  Longhorn Automotive Electronics Technology (Xiamen) Co., Ltd.
 *****************************************************************************/
#ifndef __SPI_COM_PRG_H__
#define __SPI_COM_PRG_H__
/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "types.h"
#include "debug.h"

/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/
#define DEBUG_RDUM_RDUS // 是否输出调试信息
#define PRINTF_RDUM                 RDUM_DEBUG_PRINT

/* 数据缓冲区相关定义 */
#define SPI_DATA_BUFF_MAX_NUM       170      /** @brief 数据缓冲区最大长度 */
#define SPI_DATA_QUEUE_NUM          20      /** @brief 数据队列大小 */

/******************************************************************************
 * @Type Definitions
 *****************************************************************************/
typedef enum
{
    SPI_TRANS_SUCC = 0u, /** @brief SPI传输成功 */
    SPI_TRANS_DELAY,     /** @brief SPI传输等待延时中 */
    SPI_TRANS_BUSY,      /** @brief SPI传输忙 */
    SPI_TRANS_ERROR,     /** @brief SPI传输失败 */
    SPI_TRANS_TIMEOUT,   /** @brief SPI传输超时 */
    SPI_TRANS_INVALID,   /** @brief SPI传输无效 */
} SpiTransReturn_t;

typedef enum
{
    SPI_TX_SEQ_START = 0, /** @brief SPI传输序号初始化 */
    SPI_TX_SEQ_1,         /** @brief SPI传输序号1 */
    SPI_TX_SEQ_2,         /** @brief SPI传输序号2 */
    SPI_TX_SEQ_3,         /** @brief SPI传输序号3 */
    SPI_TX_SEQ_4,         /** @brief SPI传输序号4 */
    SPI_TX_SEQ_5,         /** @brief SPI传输序号5 */
    SPI_TX_SEQ_6,         /** @brief SPI传输序号6 */
    SPI_TX_SEQ_7,         /** @brief SPI传输序号7 */
    SPI_TX_SEQ_8,         /** @brief SPI传输序号8 */
    SPI_TX_SEQ_9,         /** @brief SPI传输序号9 */
    SPI_TX_SEQ_10,        /** @brief SPI传输序号10 */
    SPI_TX_SEQ_11,        /** @brief SPI传输序号11 */
    SPI_TX_SEQ_12,        /** @brief SPI传输序号12 */
    SPI_TX_SEQ_13,        /** @brief SPI传输序号13 */
    SPI_TX_SEQ_NUM,       /** @brief SPI传输序号总数 */

    SPI_TX_SEQ_NULL = 0xFF, /** @brief SPI传输序号为空 */
} SpiTxSeqId_t;

typedef enum
{
    SPI_TX_IDLE = 0u, /** @brief SPI传输空闲 */
    SPI_TX_WAIT,      /** @brief SPI传输等待 */
    SPI_TX_BUSY,      /** @brief SPI传输进行中 */
    SPI_TX_TIMEOUT,   /** @brief SPI传输超时 */
    SPI_TX_ERROR,     /** @brief SPI传输错误 */
    SPI_TX_CANCELED,  /** @brief SPI传输被取消 */
    SPI_TX_READY,     /** @brief SPI传输完成 */
} SpiTxStatus_t;

/** @brief 回调函数返回值定义 */
typedef enum
{
    SPI_CALLBACK_OK = 0u,        /** @brief 回调处理成功，继续执行 */
    SPI_CALLBACK_RETRY,          /** @brief 需要重试当前命令 */
    SPI_CALLBACK_ABORT,          /** @brief 中止当前命令和后续命令 */
    SPI_CALLBACK_CONTINUE,       /** @brief 当前命令失败但继续执行后续命令 */
}SpiCallbackReturn_t;

/* 数据缓冲区结构体 */
typedef struct
{
    uint8 TxBuff[SPI_DATA_BUFF_MAX_NUM]; /** @brief 发送数据缓冲区 */
    uint8 RxBuff[SPI_DATA_BUFF_MAX_NUM]; /** @brief 接收数据缓冲区 */
    uint8 DataLens;                      /** @brief 数据长度 */
    SpiTxStatus_t DataBuffStatus;        /** @brief 队列状态(分配数据缓存区给SPI传输序号后变成SPI_TX_WAIT，完成DMA传输变成SPI_TX_READY，释放数据缓存区后变成SPI_TX_IDLE) */
    SpiTxSeqId_t SpiTxSeqId;             /** @brief SPI发送序号 */
} SpiDataBuffer_t;

typedef struct
{
    uint8 RetryCount;      /** @brief 重试次数 */
    uint8 MaxRetryCount;   /** @brief 最大重试次数 */
    uint16 TimeoutMs;      /** @brief 超时时间(毫秒) */
    uint16 DelayMs;        /** @brief 命令延时时间(毫秒) */
    uint32 TxCompleteTime; /** @brief SPI传输完成时间 */
    uint32 TxStartTime;    /** @brief SPI传输开始时间 */
    /** @brief SPI传输状态(数据开始DMA传输变成SPI_TX_BUSY，完成DMA传输并且没有以下条件限制则变成SPI_TX_IDLE,
     * 数据需要延时、需要重试、需要回复处理变成SPI_TX_WAIT， 数据传输取消变成SPI_TX_CANCELED)
     * 数据传输超时变成SPI_TX_TIMEOUT，数据传输错误变成SPI_TX_ERROR */
    SpiTxStatus_t TxStatus;
    // SpiComCallback_t Callback;  /** @brief 完成回调函数 */
    void *UserData;              /** @brief 用户数据 */
    uint8 IsResponseNeeded;      /** @brief 是否需要响应 */
    uint8 IsResponseValid;       /** @brief 响应有效标志 */
    SpiDataBuffer_t *DataBuffer; /** @brief 数据缓冲区指针 */
}SpiCom_t;

/** @brief SPI命令回调函数类型 */
typedef SpiCallbackReturn_t (SpiComCallback_t)(SpiCom_t *SpiComData);

typedef struct
{
    SpiTxSeqId_t NextIndex;             /** @brief 下一个传输序号 */
    SpiTxSeqId_t CurrentIndex;          /** @brief 当前传输序号 */
    SpiTxStatus_t Status;               /** @brief 当前传输状态(数据开始DMA传输变成SPI_TX_BUSY，完成DMA传输变成SPI_TX_IDLE) */
    uint16 DefaultTimeoutMs;            /** @brief 默认超时时间(毫秒) */
    uint16 DefaultDelayMs;              /** @brief 默认命令延时时间(毫秒) */
    uint8 DefaultMaxRetryCount;         /** @brief 默认最大重试次数 */
    uint8 EnableRetry;                  /** @brief 启用重试功能 */
    uint8 EnableDelay;                  /** @brief 启用延时功能 */
    uint8 AbortRemaining;               /** @brief 中止剩余命令标志 */
    SpiComCallback_t *Callback;          /** @brief 完成回调函数 */

    /* 数据缓冲区池 */
    SpiDataBuffer_t DataBuffers[SPI_DATA_QUEUE_NUM]; /** @brief 数据缓冲区池 */
    uint8 DataBufferReadIndex;          /** @brief 数据缓冲区读取索引 */
    uint8 DataBufferWriteIndex;         /** @brief 数据缓冲区写入索引 */
    uint8 DataBufferCount;              /** @brief 已使用的数据缓冲区数量 */

    SpiCom_t ComMsg[SPI_TX_SEQ_NUM];    /** @brief 当前传输信息 */
}SpiComSeq_t;

/******************************************************************************
 * @Const Declaration
 *****************************************************************************/



/******************************************************************************
 * @Variable Declaration
 *****************************************************************************/



/******************************************************************************
 * @Function Declaration
 *****************************************************************************/
void SpiCom_Init(SpiComCallback_t *Callback);
void SpiCom_DataHandle(void);
void SpiCom_DSIMaster0_CompleteTransfer_Cbk(void);
void SpiCom_DSIMaster1_CompleteTransfer_Cbk(void);

/* SPI异步传输函数 */
SpiTxSeqId_t SpiCom_AsyncTransferEx(uint8 *TxData, uint16 TxLens,
                                   uint16 TimeoutMs, uint16 DelayMs,
                                   uint8 MaxRetryCount, uint8 IsResponseNeeded, void* UserData);

/* 命令管理函数 */
SpiTxStatus_t SpiCom_GetTransferStatus(SpiTxSeqId_t TxSeqId);
SpiTransReturn_t SpiCom_SetResponseValidation(SpiTxSeqId_t TxSeqId);

/* 配置函数 */
void SpiCom_SetDefaultTimeout(uint16 TimeoutMs);
void SpiCom_SetDefaultDelay(uint16 DelayMs);
void SpiCom_SetDefaultRetryCount(uint8 MaxRetryCount);
void SpiCom_EnableRetry(uint8 Enable);
void SpiCom_EnableDelay(uint8 Enable);

#endif
