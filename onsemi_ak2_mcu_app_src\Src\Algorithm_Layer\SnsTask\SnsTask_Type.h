/******************************************************************************
 * @file       SnsTask_Type.h
 * @brief      
 * @date       2025-03-04 14:28:24
 * <AUTHOR>
 * @copyright  Longhorn Automotive Electronics Technology (Xiamen) Co., Ltd.
 *****************************************************************************/


#ifndef SnsTask_Type_H
#define SnsTask_Type_H
/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "types.h"



/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/
#define   SNS_TASK_DATA_PRINT_SWITCH              (STD_OFF)


#if (SNS_TASK_DATA_PRINT_SWITCH == STD_ON)
#define SNS_TASK_DATA_PRINT       Lhprintf
#else
#define SNS_TASK_DATA_PRINT(...)  
#endif

/* 根据理想状态逻辑转换，修改不同的车速逻辑
   PSL 大于30km/h关闭，小于25km/h开启
   PAS MAP大于15km/h关闭，小于10km/h开启
   */
#define SNS_SYS_FRONT_REAR_SIDE_ACTIVE_SPD_START    1200     /* 前后侧均激活 的车速是10km/h */
#define SNS_SYS_FRONT_REAR_SIDE_ACTIVE_SPD_END      1500     /* 前后关闭的车速是15km/h */
#define SNS_SYS_ONLY_SIDE_ACTIVE_SPD_START          2500     /* 侧边激活的车速是25km/h */
#define SNS_SYS_ONLY_SIDE_ACTIVE_SPD_END            2800     /* 侧边关闭的车速是30km/h */



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/
/* 探头任务调度的状态机 */
typedef enum
{
    SNS_TASK_IDLE = 0,
    SNS_TASK_RAW_DATA_GET,
    SNS_TASK_DIS_FILTER_FOLLOW,
    SNS_TASK_F_R_MAP_BUILD,
}eSnsTaskStepType;

/* 探头系统工作状态，主要用于Map、DE CE发送逻辑的处理 */
typedef enum
{
    SNS_SYS_STANDBY = 0,              /* 车速高于30km/h或电压不满足工作条件，所有探头都不工作，进入待机模式 */
    SNS_SYS_FRONT_REAR_SIDE_ACTIVE,   /* 前后侧都发波，探头均OK，车速在12km/h以下 */
    SNS_SYS_ONLY_SIDE_ACTIVE,         /* 仅侧雷达发波，探头均OK，车速在12~30km/h */
    SNS_SYS_ALL_FAILURE,              /* 前后都故障，或者进入到功能降级模式 */
}eSnsSysWorkStsType;



/******************************************************************************
 * @Public Const Declaration
 *****************************************************************************/



/******************************************************************************
 * @Public Variable Declaration
 *****************************************************************************/



/******************************************************************************
 * @Public Function Declaration
 *****************************************************************************/



#endif
