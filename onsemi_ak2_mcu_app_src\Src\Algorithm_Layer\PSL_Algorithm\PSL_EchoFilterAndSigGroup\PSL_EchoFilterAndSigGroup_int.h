/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * SnsEchoFilterAndSigGroup_int: 
 * Created on: 2024-02-23 14:29
 * Original designer: 22866
 ******************************************************************************/

#ifndef PSL_EchoFilterAndSigGroup_int_H
#define PSL_EchoFilterAndSigGroup_int_H

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "PSL_EchoFilterAndSigGroup_type.h"
#include "SnsRawData_Type.h"


/******************************************************************************
 * Compiler option used to distinguish inclusion done by the owner
 *****************************************************************************/


/******************************************************************************
 * Inclusion of private header files
 *****************************************************************************/
 

/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
//extern SnsSigGroupDataCacheType GstrSnsSigGroupDataCache[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
void PSLSnsSigGroupDataPowerOnInit(void);
void PSLSnsEchoFilterAndSigGroupDataGet(PDCSnsGroupType LeGroup,PDCSnsChannelType LePDCSnsCh);
void PSLClearSnsSigGroupDataUpdateFlag(PDCSnsGroupType LeGroup,PDCSnsChannelType LePDCSnsCh);
void PSLSnsSigGroupDataCacheInit(PDCSnsGroupType LeGroup);





#endif /* end of SnsEchoFilterAndSigGroup_int_H */

