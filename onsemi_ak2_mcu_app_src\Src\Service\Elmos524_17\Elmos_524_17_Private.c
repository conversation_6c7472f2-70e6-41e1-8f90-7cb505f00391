/******************************************************************************
 *  Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 *  Module_Cfg.c
 *  Created on:
 *  Implementation of the Class Elmos_524_17
 *  Original designer:
 ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include <string.h>
#include "Elmos_524_17_Private.h"
#include "Elmos_524_17_SnsCtrl.h"
#include "AK2_MCU_Drv.h"

MeasChlECHOData_Str MeasECHOMsg[DSIMasterNum][DSIChlNum];

SnsSelfTestStatusMsg_str SnsSelfTestStatusMsg[DSIMasterNum][DSIChlNum][DSISlaveSnsNum];

Sns_PDCMMsg_Status_Str SnsPDCMMsg_Status[DSIMasterNum][DSIChlNum][DSISlaveSnsNum];


void Clear_SnsSelfTestStatusMsg(SnsID_en SnsID)
{
	DSIMasterID_en DSIMasterID;
	DSIChlID_en DSIChlID;
	uint8 SlotID;

	DSIMasterID = SnsAddCfg[SnsID].DSIMasterID;
    DSIChlID   = SnsAddCfg[SnsID].DSIChlSEL - 1U;
    SlotID = SnsAddCfg[SnsID].cSnsPhysAddr - 1U;
	
    if((DSIMasterID < DSIMasterNum) && (DSIChlID < DSIChlNum) && (SlotID < DSISlaveSnsNum))
	{
		memset(&SnsSelfTestStatusMsg[DSIMasterID][DSIChlID][SlotID],0,sizeof(SnsSelfTestStatusMsg_str));
  	}
	else if(DSIChlSEL1And2 == SnsAddCfg[SnsID].DSIChlSEL)
	{
		memset(&SnsSelfTestStatusMsg[DSIMasterID][0][0],0,(DSIChlNum * DSISlaveSnsNum * sizeof(SnsSelfTestStatusMsg_str)));
	}
	else
	{

	}
		
}

void Clear_SnsPDCMMsg_Status(SnsID_en SnsID)
{

	DSIMasterID_en DSIMasterID;
	DSIChlID_en DSIChlID;
	uint8 SlotID;

	DSIMasterID = SnsAddCfg[SnsID].DSIMasterID;
    DSIChlID = SnsAddCfg[SnsID].DSIChlSEL - 1U;
    SlotID = SnsAddCfg[SnsID].cSnsPhysAddr - 1U;
	
    if((DSIMasterID < DSIMasterNum) && (DSIChlID < DSIChlNum) && (SlotID < DSISlaveSnsNum))
	{
		memset(&SnsPDCMMsg_Status[DSIMasterID][DSIChlID][SlotID],0,sizeof(Sns_PDCMMsg_Status_Str));
  	}  
}


Sns_PDCM_MEASMsg_Str * GetMeasMsgBufAddr(DSIMasterID_en LeDSIMasterID,DSIChlID_en LeDSIChlID,uint8 LeSlotID)
{
    if((LeDSIMasterID < DSIMasterNum) && (LeDSIChlID < DSIChlNum) && (LeSlotID < DSISlaveSnsNum))
    {
		return (Sns_PDCM_MEASMsg_Str *)&MeasECHOMsg[LeDSIMasterID][LeDSIChlID].ECHOMsg[LeSlotID];
    }
	else
	{
		return NULL;
	}
}

Sns_PDCMMsg_Status_Str * GetPDCMMsg_StatusAddr(DSIMasterID_en LeDSIMasterID,DSIChlID_en LeDSIChlID,uint8 LeSlotID)
{    
    if((LeDSIMasterID < DSIMasterNum) && (LeDSIChlID < DSIChlNum) && (LeSlotID < DSISlaveSnsNum))
    {
        return (Sns_PDCMMsg_Status_Str *)&SnsPDCMMsg_Status[LeDSIMasterID][LeDSIChlID][LeSlotID];
    }
    else
	{
    	return NULL;
	}
}

SnsSelfTestStatusMsg_str * GetSnsSelfTestStatusAddr(DSIMasterID_en LeDSIMasterID,DSIChlID_en LeDSIChlID,uint8 LeSlotID)
{
    if((LeDSIMasterID < DSIMasterNum) && (LeDSIChlID < DSIChlNum) && (LeSlotID < DSISlaveSnsNum))
    {
        return (SnsSelfTestStatusMsg_str *)&SnsSelfTestStatusMsg[LeDSIMasterID][LeDSIChlID][LeSlotID];
    }
	else
	{
		return NULL;
	}

}

void Clear_ECHOMsgMEAS(DSIMasterID_en LeDSIMasterID,DSIChlID_en LeDSIChlID)
{
    memset(&MeasECHOMsg[LeDSIMasterID][LeDSIChlID],0,sizeof(MeasChlECHOData_Str));
}

uint8 SetECHOMsgMEAS_Mode(DSIMasterID_en LeDSIMasterID,DSIChlID_en LeDSIChlID,MeasType_en MeasType,SnsMeasProfile_en MeasProfile,CMDSeq_en MeasSeq)
{
    MeasECHOMsg[LeDSIMasterID][LeDSIChlID].MeasType = MeasType;
    MeasECHOMsg[LeDSIMasterID][LeDSIChlID].MeasProfile = MeasProfile;
    MeasECHOMsg[LeDSIMasterID][LeDSIChlID].MeasSeq = MeasSeq;

    return 0;
}

uint8 SetSns_ECHOMsgSnsMEAS_TYPE(DSIMasterID_en LeDSIMasterID,DSIChlID_en LeDSIChlID,uint8 LeSlotID,SNS_MEAS_TYPE_en SNS_MEAS_TYPE)
{
    if((LeDSIMasterID < DSIMasterNum) && (LeDSIChlID < DSIChlNum) && (LeSlotID < DSISlaveSnsNum))
    {
		MeasECHOMsg[LeDSIMasterID][LeDSIChlID].ECHOMsg[LeSlotID].SNS_MEAS_TYPE = SNS_MEAS_TYPE;
	    return LeSlotID;
    }
	else
	{
		return 0xFF;
	}
}

void SetTransMeasCmdTime(DSIMasterID_en LeDSIMasterID,DSIChlID_en LeDSIChlID,uint32 TransTime)
{
 	MeasECHOMsg[LeDSIMasterID][LeDSIChlID].TransMeasCmdTime = TransTime;
}

void SetMeasECHOMsgUpdateTimeTime(DSIMasterID_en LeDSIMasterID,DSIChlID_en LeDSIChlID)
{
    MeasECHOMsg[LeDSIMasterID][LeDSIChlID].UpdateTime = AK2_GetSys1p6usTickFunc(); 
	MeasECHOMsg[LeDSIMasterID][LeDSIChlID].ConsumptTime = MeasECHOMsg[LeDSIMasterID][LeDSIChlID].UpdateTime - MeasECHOMsg[LeDSIMasterID][LeDSIChlID].TransMeasCmdTime; 
}

void SetDataRecEndTime(DSIMasterID_en LeDSIMasterID,DSIChlID_en LeDSIChlID,uint32 TransTime)
{
 	MeasECHOMsg[LeDSIMasterID][LeDSIChlID].DataRecEndTime = TransTime;
}

void SetADASYNTime(DSIMasterID_en LeDSIMasterID,DSIChlID_en LeDSIChlID,uint32 SYNTime)
{
 	MeasECHOMsg[LeDSIMasterID][LeDSIChlID].ADASSYNTime = SYNTime;
}

void DetSnsNFD_Data(Sns_PDCM_NFDData *pNFDData,uint8 pData[8])
{
    pNFDData->NFD1_2Flg = pData[2] & 0x03;
    pNFDData->NFD1_ECHO1 = pData[3];
    pNFDData->NFD1_Delta_ECHO2 = pData[4];
    pNFDData->NFD1_Delta_ECHO3 = pData[5];
    pNFDData->NFD2_Result = pData[6];


}

void DetSnsDiag_Data1(Sns_PDCM_DiagData1 *pDiagData1,uint8 pData[8])
{
    pDiagData1->Temperature = pData[2];
    pDiagData1->VSUP = (((uint16)pData[3] & 0x7F)<< 4) |((uint16)pData[4]>> 4);
    pDiagData1->NoiseSum = (((uint16)pData[4] & 0x0F) << 8) | (uint16)pData[5];
    pDiagData1->NoiseCount = pData[6];

}

void DetSnsDiag_Data2(Sns_PDCM_DiagData2 *pDiagData2,uint8 pData[8])
{
    pDiagData2->BurstLength = pData[3];
    pDiagData2->VTANK2 = (((uint16)pData[4] & 0x0F)<< 6) |((uint16)pData[5]>> 2);
    pDiagData2->VTANK1 = (((uint16)pData[5] & 0x03) << 8) | (uint16)pData[6];

}

void DetSnsRT_Data(Sns_PDCM_RingTimeData *pRTData,uint8 pData[8])
{
    pRTData->RingingCount = pData[3] >> 2;
    pRTData->RingingSamples = (((uint16)pData[3] & 0x03)<< 8) |((uint16)pData[4]);
    pRTData->RTM = ((uint16)pData[5]  << 8) | (uint16)pData[6];

}

uint8 DetSnsSTD_RAW_ECHO_Data(Sns_ECHOMsg_Str *pECHOData,uint8 pData[8])
{
    Sns_ECHOData_un *pECHOMsg;
    if(pECHOData->SaveECHODataCnt < ECHO_MAXNUM)
    {

        pECHOMsg = &pECHOData->ECHOBuf[pECHOData->SaveECHODataCnt];

        pECHOMsg->StdECHOData.EchoHeightType = 1;  /*0:8位峰值，1 ：16位峰值*/
        pECHOMsg->StdECHOData.ECHO_Event = (STD_ECHO_Event_en)(pData[3] >> 6);   /*回波事件*/
        pECHOMsg->StdECHOData.EchoHeightAmpd_RAW =  (((uint16)pData[3] & 0x7F) << 10) | ((uint16)pData[4] << 2); /*回波峰值*/
        pECHOMsg->StdECHOData.TimeStamp = ((uint16)pData[5] << 8) | (uint16)pData[6];

        pECHOData->SaveECHODataCnt++;
    }
    pECHOData->TotalECHODataCnt++;
    return (pData[2] & 0x01);
}

uint8 DetSnsSTD_AMPD_ECHO_Data(Sns_ECHOMsg_Str *pECHOData,uint8 pData[8])
{
    Sns_ECHOData_un *pECHOMsg;
    if(pECHOData->SaveECHODataCnt < ECHO_MAXNUM)
    {

        pECHOMsg = &pECHOData->ECHOBuf[pECHOData->SaveECHODataCnt];


        pECHOMsg->StdECHOData.EchoHeightType = 0; /*0:8位峰值，1 ：16位峰值*/
        pECHOMsg->StdECHOData.ECHO_Event = (STD_ECHO_Event_en)(pData[3] >> 6);  /*回波事件*/
        pECHOMsg->StdECHOData.EchoHeightAmpd_RAW =  (uint16)pData[4];   /*回波峰值*/
        pECHOMsg->StdECHOData.TimeStamp = ((uint16)pData[5] << 8) | (uint16)pData[6];

        pECHOData->SaveECHODataCnt++;
    }
    pECHOData->TotalECHODataCnt++;
    return (pData[2] & 0x01);
}

uint8 DetSnsADV_ECHO_Data(Sns_ECHOMsg_Str *pECHOData,uint8 pData[8])
{
    Sns_ECHOData_un *pECHOMsg;
    if(pECHOData->SaveECHODataCnt < ECHO_MAXNUM)
    {

        pECHOMsg = &pECHOData->ECHOBuf[pECHOData->SaveECHODataCnt];


        pECHOMsg->AdvECHOData.filterNum = (ADV_ECHO_Filter_en)((pData[2]>> 5) & 0x01); /*0:滤波器1， 1 ：滤波器2*/
        pECHOMsg->AdvECHOData.Confidence = pData[2] & 0x0F;   /*置信度*/
        pECHOMsg->AdvECHOData.FilterAmpd = ((uint16)pData[3] << 8) | (uint16)pData[4];  /*回波峰值*/
        pECHOMsg->AdvECHOData.TimeStamp = ((uint16)pData[5] << 8) | (uint16)pData[6];

        pECHOData->SaveECHODataCnt++;
    }
    pECHOData->TotalECHODataCnt++;

    return ((pData[2] >> 4) & 0x01);
}


/***************************************************************************//**
 * @brief
 * 解析回波 PDCM帧数据
 *
 * @param       pInSnsPDCMData         PDCM帧数据
 * @param       pSns_PDCM_MEASMsg      回波数据
 * @return      0                     OK
 *              else                 ERROR
 *
 * ****************************************************************************/

uint8 DetSnsMeasECHOMSg(SnsPDCMRES_Data_str *pInSnsPDCMData,Sns_PDCM_MEASMsg_Str *pSns_PDCM_MEASMsg)
{
   
    uint8 FrameCount = (pInSnsPDCMData->PhysAddr_FrameCount & 0x0F );

    PDCMDataType_en PDCMDataType;

    ECHODataSubType_en ECHODataSubType;
    
    
    uint8 *pData;
    uint8 returnFlg = 0xFF;

    if((pInSnsPDCMData == NULL) 
        || (pSns_PDCM_MEASMsg == NULL))
    {
        return 0xFF;
    }

    PDCMDataType = DetPDCM_DataType(pInSnsPDCMData->Status_Type);
    
    ECHODataSubType = DetPDCM_DataSubType(pInSnsPDCMData->Status_Type);
    
    pSns_PDCM_MEASMsg->EchoBufFlg = 0;
    
    /** @brief 回波数据类型 */
    if((PDCMDataType == ePDCMDataType_Echo))
    {
        
        
        pData = &pInSnsPDCMData->PhysAddr_FrameCount;
        
        /** @brief 解析数据 */
        switch(ECHODataSubType)
        {
        
            case eECHODataSubType_NOInfo:
            {
        
                break;
            }
        
            case eECHODataSubType_DiagData1:
            {
                DetSnsDiag_Data1(&pSns_PDCM_MEASMsg->DiagData1, pData);
                pSns_PDCM_MEASMsg->DiagDatUpDateFlg |= DiagData1UpDateFlgMask;
                break;
            }
            case eECHODataSubType_DiagData2:
            {
        
                DetSnsDiag_Data2(&pSns_PDCM_MEASMsg->DiagData2, pData);
                pSns_PDCM_MEASMsg->DiagDatUpDateFlg |= DiagData2UpDateFlgMask;
                break;
            }
            case eECHODataSubType_RTData:
            {
                DetSnsRT_Data(&pSns_PDCM_MEASMsg->RTData, pData);
                pSns_PDCM_MEASMsg->DiagDatUpDateFlg |= DiagRTDataUpDateFlgMask;
                break;
            }
            case eECHODataSubType_STD_RAW:
            {
                pSns_PDCM_MEASMsg->EchoBufFlg = DetSnsSTD_RAW_ECHO_Data(&pSns_PDCM_MEASMsg->ECHOMsgData, pData);
        
                break;
            }
            case eECHODataSubType_STD_AMPD:
            {
                pSns_PDCM_MEASMsg->EchoBufFlg = DetSnsSTD_AMPD_ECHO_Data(&pSns_PDCM_MEASMsg->ECHOMsgData, pData);
                break;
            }
            case eECHODataSubType_ADV:
            {
                pSns_PDCM_MEASMsg->EchoBufFlg = DetSnsADV_ECHO_Data(&pSns_PDCM_MEASMsg->ECHOMsgData, pData);
        
                break;
            }
            case eECHODataSubType_NFDData:
            {
        
                DetSnsNFD_Data(&pSns_PDCM_MEASMsg->NFDData, pData);
                pSns_PDCM_MEASMsg->DiagDatUpDateFlg |= NFDDataUpDateFlgMask;
                break;
            }
            default:
            {
            
                /** @brief ERR */
                break;
            }
        }

        if((pInSnsPDCMData->Status_Type & 0x80) != 0)
        {
            /** @brief DSI通信错误标志有效 */
            if(pSns_PDCM_MEASMsg->ComHwErrorCnt[0] < 255)
            {
                pSns_PDCM_MEASMsg->ComHwErrorCnt[0]++;
            }
        }
        if((pInSnsPDCMData->Status_Type & 0x40) != 0)
        {
            /** @brief 硬件错误标志有效 */
            if(pSns_PDCM_MEASMsg->ComHwErrorCnt[1] < 255)
            {
                pSns_PDCM_MEASMsg->ComHwErrorCnt[1]++;
            }
        }
        
        if(FrameCount != pSns_PDCM_MEASMsg->FrameRollCnt)
        {
            /** @brief 帧循环计数错误 */
            if(pSns_PDCM_MEASMsg->RollCntErr < 255)
            {
                pSns_PDCM_MEASMsg->RollCntErr++;
            }
            pSns_PDCM_MEASMsg->FrameRollCnt = FrameCount;
        }
        pSns_PDCM_MEASMsg->FrameRollCnt++;
        /** @brief 接收帧计数 */
        pSns_PDCM_MEASMsg->RecvFrameCnt++;
        

        /** @brief 循环计数 */
        if(pSns_PDCM_MEASMsg->FrameRollCnt > 0x0F)
        {
            pSns_PDCM_MEASMsg->FrameRollCnt = 0;
        }

        returnFlg = 0;

    }
    else
    {
    
        returnFlg = 0xFF;
    }

    return returnFlg;
}




/***************************************************************************//**
 * @brief
 * 解析状态 PDCM帧数据
 *
 * @param       pInSnsPDCMData         PDCM帧数据
 * @param       pOutSnsStatusMsg       状态数据
 * @return      0                     OK
 *              else                 ERROR
 *
 * ****************************************************************************/

uint8 DetSnsStatusMSg(SnsPDCMRES_Data_str *pInSnsPDCMData,SnsSelfTestStatusMsg_str *pOutSnsStatusMsg)
{
    uint8 returnFlg = 0;
 
    uint8 FrameCount = (pInSnsPDCMData->PhysAddr_FrameCount & 0x0F );
    
    PDCMDataType_en PDCMDataType;
    uint8 PDCMDataIndex;
    uint8 *pData;
    
    if((pInSnsPDCMData == NULL) 
    || (pOutSnsStatusMsg == NULL))
    {
        return 0xFF;
    }
    SnsSelfTestStatusMsg_str *pSnsStatusMsg = pOutSnsStatusMsg;
    uint16 *pU16SnsStatusMsg = (uint16*)pOutSnsStatusMsg;
    
    PDCMDataType = DetPDCM_DataType(pInSnsPDCMData->Status_Type);
    
    
    /** @brief  解析状态数据 */
    if(PDCMDataType == ePDCMDataType_Status)
    {
        /** @brief Packet数据有效 */
    
        pData = &pInSnsPDCMData->PhysAddr_FrameCount;

        if((pInSnsPDCMData->Status_Type & 0x80) != 0)
        {
            /** @brief 通信错误标志 */
            if(pSnsStatusMsg->ComHwErrorCnt[0] < 255)
            {
                pSnsStatusMsg->ComHwErrorCnt[0]++;
            }
        }
        if((pInSnsPDCMData->Status_Type & 0x40) != 0)
        {
            /** @brief 硬件错误标志 */
            if(pSnsStatusMsg->ComHwErrorCnt[1] < 255)
            {
                pSnsStatusMsg->ComHwErrorCnt[1]++;
            }
        }

        if(FrameCount != pOutSnsStatusMsg->FrameRollCnt)
        {
            /** @brief 帧计数错误，丢帧 */
        
            if(pOutSnsStatusMsg->RollCntErr < 255)
            {
                pOutSnsStatusMsg->RollCntErr++;
            }
            pOutSnsStatusMsg->FrameRollCnt = FrameCount;
        }
        /** @brief 总接收帧计数 */
        pOutSnsStatusMsg->RecvFrameCnt++;

        /** @brief 帧循环计数 */
        pOutSnsStatusMsg->FrameRollCnt++;

        if(pOutSnsStatusMsg->FrameRollCnt > 0x0F)
        {
            pOutSnsStatusMsg->FrameRollCnt = 0;
        }
        else
        {

        }
        

        PDCMDataIndex =  (pInSnsPDCMData->Status_Type & 0x0F) * 2;
        
        /** @brief 提取状态数据 */
        pU16SnsStatusMsg[PDCMDataIndex] = (pData[3] << 8) |  pData[4];

        pU16SnsStatusMsg[PDCMDataIndex+1] = (pData[5] << 8) |  pData[6];

        /** @brief 数据更新标志 */
        pSnsStatusMsg->DataUpdataFlg |= 0x01 << (pInSnsPDCMData->Status_Type & 0x0F);
    }
    else
    {
        

        returnFlg = 0xFF;
      

    }

        
    return returnFlg;
}


uint8 DetPDCMResFrameMSg(DSIMasterID_en DSIMasterID,DSIChlID_en DSIChlID,PDCM_DSIChlRES_str *pPDCMResData)
{
    uint8 LcCnt=0;
    PDCM_FRAME_STATUS_Str FRAME_STATUS;    
    Sns_PDCM_MEASMsg_Str *pSns_PDCM_MEASMsg = NULL;
    SnsSelfTestStatusMsg_str *pOutSnsStatusMsg = NULL;
    PDCM_RES_Packet_str *pPDCM_RES_Packet;
    PDCMDataType_en PDCMDataType;
    
    Sns_PDCMMsg_Status_Str *pSnsPDCMMsg_Status;
    uint8 RecPhysAddr;
    uint8 SnsPhysAddr;
    uint16 ExpectBRCNum;
    FRAME_STATUS = pPDCMResData->FRAME_STATUS;
    
    if(FRAME_STATUS.Status != 0)
    {
        /** @brief PDCM接收出现异常 */
    
    }
    
    for(LcCnt=0;LcCnt<DSISlaveSnsNum;LcCnt++)
    {
        pPDCM_RES_Packet = &pPDCMResData->Data[LcCnt];
        
        RecPhysAddr = (pPDCM_RES_Packet->Data.PhysAddr_FrameCount >> 4 );
        SnsPhysAddr = LcCnt + 1;

        /** @brief Packet状态信息 */
        pSnsPDCMMsg_Status = GetPDCMMsg_StatusAddr(DSIMasterID, DSIChlID,LcCnt);
        
        if(((pPDCM_RES_Packet->DSIStatus.Status & DSI_SC_CR_SE_ErrMASK) == 0)
         && (pPDCM_RES_Packet->DSIStatus.SymbolCount == TDMA_PDCMSymbCnt)
         && (RecPhysAddr == SnsPhysAddr))
        {
            /** @brief  Packet CRC正确 *//** @brief Symbol计数有效 *//** @brief 地址有效 */
            
            PDCMDataType = DetPDCM_DataType(pPDCM_RES_Packet->Data.Status_Type);
            
            switch(PDCMDataType)
            {
                case ePDCMDataType_Echo:
                {
                    /** @brief 获取回波数据保存地址 */
                    pSns_PDCM_MEASMsg = GetMeasMsgBufAddr(DSIMasterID, DSIChlID,LcCnt);
                    
                    if(pSns_PDCM_MEASMsg != NULL)
                    {
                        /*解析回波数据*/
                        DetSnsMeasECHOMSg(&pPDCM_RES_Packet->Data,pSns_PDCM_MEASMsg);
                        
                    }
                    break;
                }
                
                case ePDCMDataType_Lab_Imp:
                {
                
                    break;
                }
                case ePDCMDataType_Status:
                {
                    /** @brief SelfTest状态数据保存地址 */
                    pOutSnsStatusMsg = GetSnsSelfTestStatusAddr( DSIMasterID, DSIChlID,LcCnt);
                    
                    if(pOutSnsStatusMsg != NULL)
                    {   
                        /** @brief 解析状态数据 */
                        DetSnsStatusMSg(&pPDCM_RES_Packet->Data,pOutSnsStatusMsg);
                    }
                    break;
                }
                
                default:
                {
                    break;
                }
            }
            pSnsPDCMMsg_Status->RecvValidFrameCnt++;
        }
        else
        {
            /** @brief ERR */
            
            if((pPDCM_RES_Packet->DSIStatus.Status & DSI_SC_CR_SE_ErrMASK) != 0)
            {
                /** @brief CRC SC SE ERR */
                pSnsPDCMMsg_Status->CRC_SC_SE_Err++;
                
                PRINTF_Elmos17SnsCtrl("CHL %d FRAME_STATUS = %d PacketCount %d \n",DSIChlID,FRAME_STATUS.Status,FRAME_STATUS.PacketCount); 

            }
            else
            {
                if(RecPhysAddr != SnsPhysAddr)
                {
                    /** @brief 地址错误 */
                    pSnsPDCMMsg_Status->PhyAddErr++;
                
                }
                
            }
            
            if(pPDCM_RES_Packet->DSIStatus.SymbolCount != TDMA_PDCMSymbCnt)
            {
                /** @brief Symbol ERR */
                if(pPDCM_RES_Packet->DSIStatus.SymbolCount == 0)
                {
                    pSnsPDCMMsg_Status->NoSymbolErr++;
                
                }
                else
                {
                    pSnsPDCMMsg_Status->SymbolCntErr++;
                }
            }
        }

        /** @brief 记录DSI状态 */
        pSnsPDCMMsg_Status->PacketDsiErrStatus |= pPDCM_RES_Packet->DSIStatus.Status;

        /** @brief 获取预期接收帧个数 */
        ExpectBRCNum = GetExpectBRCNum(DSIMasterID,DSIChlID);

        /** @brief 计算有效帧丢失个数 */
        if(ExpectBRCNum >= pSnsPDCMMsg_Status->RecvValidFrameCnt)
        {
            pSnsPDCMMsg_Status->LostValidFrameCnt = ExpectBRCNum - pSnsPDCMMsg_Status->RecvValidFrameCnt;
        }
        else
        {
            pSnsPDCMMsg_Status->LostValidFrameCnt = 0;
        }
    }

    return FRAME_STATUS.Status;
}
/*  */



