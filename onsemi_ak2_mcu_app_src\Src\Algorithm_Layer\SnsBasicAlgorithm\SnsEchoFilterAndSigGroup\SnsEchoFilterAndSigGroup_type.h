/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * SnsEchoFilterAndSigGroup_type: 
 * Created on: 2023-07-11 10:30
 * Original designer: AntonyFang
 ******************************************************************************/
#ifndef SnsEchoFilterAndSigGroup_type_H
#define SnsEchoFilterAndSigGroup_type_H

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "types.h"




/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/
//#define MAX_NUM_OF_SIG_GROUP     3u
#define SNS_HIGH_CURB_USE_FIRST_DIS_ENABLE   0u
#define SNS_MASTER_COMPEN_DIS                0u
#define SNS_LISTEN_COMPEN_DIS                0u

#define SNS_NFD_ECHO_HEIGHT                  2000

#define SNS_STD_LISTEN_COMPEN_HEIGHT         8
#define SNS_STD_NEAR_LISTEN_COMPEN_HEIGHT    8
#define SNS_CHIRP_LISTEN_COMPEN_HEIGHT       25
#define SNS_STD_SECOND_THAN_FIRST_HEIGHT     850      /* 定频回波高度修改为14bit后,此处同样修改为350 */
#define SNS_CHIRP_SECOND_THAN_FIRST_HEIGHT   400      /* 扫频灵敏度提升后，该参数同样需要修改 */

#define SNS_STD_MASTER_CMP_THRES_1KM         1
#define SNS_STD_MASTER_CMP_THRES_3KM         2
#define SNS_STD_MASTER_CMP_THRES_5KM         4
#define SNS_STD_MASTER_CMP_THRES_7KM         6
#define SNS_STD_MASTER_CMP_THRES_MORE_7KM    8

#define SNS_CHIRP_MASTER_CMP_THRES_1KM       5
#define SNS_CHIRP_MASTER_CMP_THRES_3KM       8
#define SNS_CHIRP_MASTER_CMP_THRES_5KM       12
#define SNS_CHIRP_MASTER_CMP_THRES_7KM       16
#define SNS_CHIRP_MASTER_CMP_THRES_MORE_7KM  20

#define SNS_MAX_DE_CE_DIS                    (uint16)5110
#define SNS_MAX_DE_CE_DIS_TO_CAN             (uint16)511
#define SNS_INIT_DE_CE_DIS_TO_CAN            (uint16)0

#define CLOSE_RANGE_STD_DE_FILTER_START_DIS  500U     /*uint:mm*/

/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/





/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/








#endif /* end of SnsEchoFilterAndSigGroup_type_H */

