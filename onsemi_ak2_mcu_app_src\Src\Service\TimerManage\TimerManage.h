/******************************************************************************
 * @file       TimerManage.h
 * @brief      
 * @date       2025-03-04 14:00:14
 * <AUTHOR>
 * @copyright  Longhorn Automotive Electronics Technology (Xiamen) Co., Ltd.
 *****************************************************************************/


#ifndef __TIMERMANAGE_H__
#define __TIMERMANAGE_H__
/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "Types.h"



/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/



/******************************************************************************
 * @Const Declaration
 *****************************************************************************/



/******************************************************************************
 * @Variable Declaration
 *****************************************************************************/
extern uint32 GdSystemMsTimer;
extern uint32 Gu32CANTimeSysn_GlobalTimeBase;



/******************************************************************************
 * @Function Declaration
 *****************************************************************************/
void UserTimerSet_Ms(void);
void UserTimerSet_Ms_ISR(void);
void UserTimerDelayMs(const uint32 Ld_Delay);
extern void SystemSoftwareReset(void);
extern void SystemJumpToSBL(void);



/******************************************************************************
 * @brief      获取CAN同步时间计数器ms
 * @return     
 * <AUTHOR>
 * @date       2025-03-04 14:03:53
 * @note       
 *****************************************************************************/
LOCAL_INLINE uint32 GetCANTimeSysn_GlobalTimeBase(void)
{
    uint32 Lu32TimeCnt_Us = 0;
    Lu32TimeCnt_Us = Gu32CANTimeSysn_GlobalTimeBase;
    return Lu32TimeCnt_Us;
}


/******************************************************************************
 * @brief      获取系统毫秒计数器
 * @return     
 * <AUTHOR>
 * @date       2025-03-04 14:03:70
 * @note       
 *****************************************************************************/
LOCAL_INLINE uint32 GetSystemTimeCnt_Ms(void)
{
    uint32 Lu32TimeCnt_Ms = 0;
    Lu32TimeCnt_Ms = GdSystemMsTimer;
    return Lu32TimeCnt_Ms;    
}



#endif 
