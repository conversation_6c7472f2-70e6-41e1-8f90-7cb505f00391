/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * F_R_ObjMapCal_Int: 
 * Created on: 2023-02-16 15:43
 * Original designer: AntonyFang
 ******************************************************************************/

#ifndef MapBuild_Int_H
#define MapBuild_Int_H

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "MapBuild_Cfg.h"



/******************************************************************************
 * Compiler option used to distinguish inclusion done by the owner
 *****************************************************************************/
 

/******************************************************************************
 * Inclusion of private header files
 *****************************************************************************/




/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
extern ObjMapToCANType GstrObjMapToCAN[MAP_OBJ_TO_CAN_MAX];
extern ObjMapInfoType  GstrObjMap;
extern PDC_AreaObjType GstrPDC_AreaObj[PDC_SNS_GROUP_NUM];
extern uint16 Gu16FrontArea4MapToBumperDis;

#if MAP_PRECISION_PRINT_SWITCH
MapPrecisionPrintType GstrMapPrecisionPrint;
#endif


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
void ObjMapPowerOnInit(void);
void ObjMapBuild(PDCSnsGroupType LenuGroup);
void TransMapOdoCoorToCarCoor(void);
void ObjMapBuildAndUpdate(PDCSnsGroupType LenuGroup);
void UpdateMapInforAndDele(void);
void ObjMapToCAN(void);
void ObjMapInit(void);
void ObjMapToCANInit(void);
void ParkingGuidenceDataUpdate(void);
void SideMapFusionHandle(void);


#endif /* end of F_R_ObjMapCal_Int_H */

