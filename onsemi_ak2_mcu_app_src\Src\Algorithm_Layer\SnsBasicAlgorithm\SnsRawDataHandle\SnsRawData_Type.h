/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * SnsRawData_Type: 
 * Created on: 2022-12-16 10:42
 * Original designer: AntonyFang
 ******************************************************************************/

#ifndef SnsRawData_Type_H
#define SnsRawData_Type_H

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "types.h"


/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/
#define VS_SIMULATE_ENABLE       0    /* 用于选择是VS仿真还是MCU实际运行 */

#define NEAR_MASTER_DIS_CNT      3
#define SNS_ECHO_GROUP_NUM       2    /* 每个探头的信号组个数 */
#define SNS_HEIGHT_BUFFER        2


#define MAX_NUM_OF_SIG_GROUP     3u

#if 0
#define VS_PRINT     printf    
#else
#define VS_PRINT(...)
#endif

#define STD_DE_FILTER_NUM  		 3U     

/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/
typedef void (*DebugSendToCANFunPtr)(void);



/* 定义探头分组类型--前后组 */
typedef enum
{
    PDC_SNS_GROUP_FRONT = 0, 
    PDC_SNS_GROUP_REAR  , 
    PDC_SNS_GROUP_NUM   ,    
}PDCSnsGroupType;

/* 定义探头通道类型 */
typedef enum
{
    PDC_SNS_CH_NONE  = -1,
    /** @brief: 左侧雷达 */
    PDC_SNS_CH_FLS  = 0,
    PDC_SNS_CH_RLS  = 0,
    PDC_SNS_CH_FLS_RLS = 0,
    /** @brief: 左角雷达 */
    PDC_SNS_CH_FL  = 1,
    PDC_SNS_CH_RL  = 1,
    PDC_SNS_CH_FL_RL = 1,
    /** @brief: 左中雷达 */
    PDC_SNS_CH_FML  = 2,
    PDC_SNS_CH_RML  = 2,
    PDC_SNS_CH_FML_RML = 2,
    /** @brief: 右中雷达 */
    PDC_SNS_CH_FMR  = 3,
    PDC_SNS_CH_RMR  = 3,
    PDC_SNS_CH_FMR_RMR = 3,
    /** @brief: 右角雷达 */
    PDC_SNS_CH_FR  = 4,
    PDC_SNS_CH_RR  = 4,
    PDC_SNS_CH_FR_RR = 4,
    /** @brief: 右侧雷达 */
    PDC_SNS_CH_FRS  = 5,
    PDC_SNS_CH_RRS  = 5,
    PDC_SNS_CH_FRS_RRS = 5,
    
    PDC_SNS_CH_NUM = 6,
}PDCSnsChannelType;

typedef enum
{
    SIDE_SNS_NONE = -1,
    SIDE_SNS_FLS = 0,
    SIDE_SNS_FRS,
    SIDE_SNS_RLS,
    SIDE_SNS_RRS,
    SIDE_SNS_NUM,
}SideSnsIndexType;


/* 定义探头回波个数类型--暂定最近4个障碍物的回波 */
typedef enum
{
    PDC_SNS_ECHO_NONE  = -1,
    PDC_SNS_ECHO_FIRST     , 
    PDC_SNS_ECHO_SECOND    ,
    PDC_SNS_ECHO_THIRD     , 
    PDC_SNS_ECHO_FOURTH    ,
    //PDC_SNS_ECHO_FIFTH     ,
    PDC_SNS_ECHO_NUM       ,    
}PDCSnsEchoNumType;

/* 定义探头回波个数类型--暂定最近4个障碍物的回波 */
typedef enum
{
    PDC_SNS_STS_NORMAL  = 0,
    PDC_SNS_STS_FAILED     , 
    PDC_SNS_STS_BLINDNESS  
}PDCSnsWorkStsType;

/* 定义探头测量类型 */
typedef enum
{
    PDC_SNS_MEAS_STD  = 0,
    PDC_SNS_MEAS_ADV_CHIRP_DOWN, 
    PDC_SNS_MEAS_ADV_CHIRP_UP,
    PDC_SNS_MEAS_IMPEDANCE = 0x5,
    PDC_SNS_MEAS_IDLE = 0x6
}PDCSnsMeasTypeType;

/* 定义探头原始回波信息 */
typedef struct
{
    PDCSnsMeasTypeType enuPDCSnsMeasType[PDC_SNS_CH_NUM];
    PDCSnsWorkStsType enuPDCSnsWorkSts[PDC_SNS_CH_NUM];
    uint8  u8UpdateFlg[PDC_SNS_CH_NUM];
    uint16 u16RingTime[PDC_SNS_CH_NUM];

    uint8  u8MasterEchoCnt[PDC_SNS_CH_NUM];
    uint8  u8LeftListenEchoCnt[PDC_SNS_CH_NUM];
    uint8  u8RightListenEchoCnt[PDC_SNS_CH_NUM];
    
    uint16 u16MasterDis[PDC_SNS_CH_NUM][PDC_SNS_ECHO_NUM];
    uint16 u16MasterWidth[PDC_SNS_CH_NUM][PDC_SNS_ECHO_NUM];
    uint16 u16MasterHeight[PDC_SNS_CH_NUM][PDC_SNS_ECHO_NUM];
    uint8  u8MasterConfidence[PDC_SNS_CH_NUM][PDC_SNS_ECHO_NUM];
    
    uint16 u16LeftListenDis[PDC_SNS_CH_NUM][PDC_SNS_ECHO_NUM];
    uint16 u16LeftListenWidth[PDC_SNS_CH_NUM][PDC_SNS_ECHO_NUM];
    uint16 u16LeftListenHeight[PDC_SNS_CH_NUM][PDC_SNS_ECHO_NUM];
    uint8  u8LeftListenConfidence[PDC_SNS_CH_NUM][PDC_SNS_ECHO_NUM];

    uint16 u16RightListenDis[PDC_SNS_CH_NUM][PDC_SNS_ECHO_NUM];
    uint16 u16RightListenWidth[PDC_SNS_CH_NUM][PDC_SNS_ECHO_NUM];
    uint16 u16RightListenHeight[PDC_SNS_CH_NUM][PDC_SNS_ECHO_NUM];
    uint8  u8RightListenConfidence[PDC_SNS_CH_NUM][PDC_SNS_ECHO_NUM];
    uint8  u8NFD_Flag[PDC_SNS_CH_NUM];
    uint16 u16NFD_Dis[PDC_SNS_CH_NUM];
	uint32 u32DataSynTime[PDC_SNS_CH_NUM];
	uint8 u8NoiseAbnormalCnt[PDC_SNS_CH_NUM];
}PDCSnsRawDataType;



/* 定义原始数据处理中用到的Odo坐标 */
typedef struct
{
    uint8 u8SnsRawDataUseID;
    float fCar_X_Coor;
    float fCar_Y_Coor;
    float fCar_YawAngle;
    float fCar_SinYawAngle;
    float fCar_CosYawAngle;
    float fDrvDis;
}PDCSnsUseOdoType;


/* 定义探头回波个数类型--暂定最近4个障碍物的回波 */
typedef enum
{
    PDC_SNS_OBJ_NONE  = -1,
    PDC_SNS_OBJ_FIRST     , 
    PDC_SNS_OBJ_SECOND    ,
    //PDC_SNS_OBJ_THIRD     ,   //从源头开始就只保留2个障碍物，后面也只处理2个障碍物
    PDC_SNS_OBJ_NUM       ,    
}PDCSnsObjNumType;

/* 定义障碍物坐标类型 */
typedef struct
{
    float fObjX;
    float fObjY;
}ObjCoorType;

/* 定义探头回波个数类型--暂定最近4个障碍物的回波 */
typedef enum
{
    SNS_TRIANGLE_INVALID  = 0,
    SNS_TRIANGLE_VALID       ,    
}SnsTriangleValidType;

/* 定义DE CE 的置信度 */
typedef enum
{
    SNS_PROB_NONE  = 0,
    SNS_PROB_LOW,
    SNS_PROB_UNKNOWN,
    SNS_PROB_MEDIUM,
    SNS_PROB_HIGH,
    SNS_PROB_VERY_HIGH,
    SNS_PROB_RESERVED,
    SNS_PROB_INVALID,
}SnsProbType;

/* 定义探头DE CE数据类型 */
typedef struct
{
    uint16 u16DE_Dis;
    uint16 u16Left_CE_Dis;
    uint16 u16Right_CE_Dis;
}PDCSnsDE_CE_DataType;

typedef enum
{
    SNS_CAR_STOP  = 0,
    SNS_CAR_FORWARD,
    SNS_CAR_BACKWARD,
    SNS_CAR_INVALID,
}SnsCarMoveDirType;


/* 定义探头DE CE数据类型 */
typedef struct
{
    float fCar_X_Coor;
    float fCar_Y_Coor;
    uint16 u16CarStopCnt;
    SnsCarMoveDirType eCarDir;
    SnsCarMoveDirType eCarDirBackup;
    uint8 u8CarFowardToBackwardFlag;
    uint8 u8CarBackwardToFowardFlag;
    float fSnsCarMovDisSub;
}PDCSnsCarMovStsType;

typedef enum
{
    CLOUD_OBJ_TYPE_NONE = 0,
    CLOUD_OBJ_TYPE_POINT,
}eCloud_MapObjShapeType;

typedef enum
{
    CLOUD_OBJ_PROB_NONE = 0,
    CLOUD_OBJ_PROB_LV1,
    CLOUD_OBJ_PROB_LV2,
    CLOUD_OBJ_PROB_LV3,    
    CLOUD_OBJ_PROB_LV4,
    CLOUD_OBJ_PROB_LV5,
    CLOUD_OBJ_PROB_LV6,
    CLOUD_OBJ_PROB_LV_NUM
}eCloudObjProbLevelType;

typedef enum
{
    CLOUD_OBJ_HEIGHT_LOW = 0,
    CLOUD_OBJ_HEIGHT_HIGNT,
    CLOUD_OBJ_HEIGHT_TRAVERSIBLE,
    CLOUD_OBJ_HEIGHT_UNKNOW
}eCloudMapObjHeightType;

/******************************************************************************
* 设计描述 : 雷达点云Map输出到CAN的数据结构
* 设计索引 : 
*******************************************************************************/
typedef struct 
{    
    /* 障碍物类型 */
    eCloud_MapObjShapeType eMapObjType;

    /* 障碍物存在可能性 */
    eCloudObjProbLevelType eObjExistProb;

    /* 障碍物的高度 */
    eCloudMapObjHeightType eMapObjHeight;

    /* 障碍物高度可能性 */
    eCloudObjProbLevelType eObjHeightProb;

    /** @brief: 障碍物Point1 X坐标 */
    sint16 s16ObjMapP1_X;
    /** @brief: 障碍物Point1 Y坐标 */
    sint16 s16ObjMapP1_Y;

    /** 点云数据更新的标志 */
    uint8 u8PointCloudUpdate;
}Cloud_ObjMapToCANType;

/*********************************************以下是信号组的类型定义，从原始数据中提取信号组  *****************************/

/* 障碍物高度区分障碍物类型 */
typedef enum
{
    OBJ_NONE_TYPE = 0,
    OBJ_GRAVEL_TYPE,             /* 碎石类型，主要用于归类低矮单体的障碍物 */
    OBJ_PVC_PIPE_TYPE,           /* PVC管类型，主要用于归类立体单点障碍物 */
    OBJ_HIGH_CURB_TYPE,          /* 高路砍类型，主要用于归类高的路砍线型障碍物 */
    OBJ_LOW_CURB_TYPE,           /* 低路砍类型，主要用于归类低矮的线型障碍物 */
    OBJ_BIGWALL_TYPE,            /* 大墙类型，主要用于归类立体的线型障碍物 */
    OBJ_HIGH_HIGH_CUEB,          /* 针对40cm路砍单独设置类型 */
}OriginalObjType;


/* 定义探头通道类型 */
typedef enum
{
    SNS_OBJ_DIS_20CM  = 0,
    SNS_OBJ_DIS_40CM,
    SNS_OBJ_DIS_60CM,
    SNS_OBJ_DIS_80CM,
    SNS_OBJ_DIS_100CM,
    
    SNS_OBJ_DIS_120CM,
    SNS_OBJ_DIS_140CM,
    SNS_OBJ_DIS_160CM,
    SNS_OBJ_DIS_180CM,
    SNS_OBJ_DIS_200CM,
    
    SNS_OBJ_DIS_220CM,
    SNS_OBJ_DIS_240CM,
    SNS_OBJ_DIS_260CM,
    SNS_OBJ_DIS_280CM,
    SNS_OBJ_DIS_300CM,
    
    SNS_OBJ_DIS_320CM,
    SNS_OBJ_DIS_340CM,
    SNS_OBJ_DIS_360CM,
    SNS_OBJ_DIS_380CM,
    SNS_OBJ_DIS_400CM,
    
    SNS_OBJ_DIS_NUM,
}SnsObjDisType;


/* 根据主发和探头间距，计算DE CE的有效性 */
typedef struct
{
    uint16 u16MaxDis;
    uint16 u16MinDis;
}DE_CE_ValidListenDisType;

/*****************************************原始信号组缓存定义********************************************  */
/******************************************************************************
* 设计描述 : 保留探头过去3个周期的缓存数据
* 设计索引 : 
*******************************************************************************/
typedef enum
{    
    SNS_CYCLE_0 = 0u,
        
    SNS_CYCLE_1,   
    
    SNS_CYCLE_2,      
    
    SNS_CYCLE_NUM,    
} Sns_CycleType; 


/******************************************************************************/
/**
 * @brief  信号组数据类型定义
 */
/******************************************************************************/
#if VS_SIMULATE_ENABLE
typedef struct
{
    uint8                   u8SigGroupCnt;
    uint8                   u8GroupEchoCnt[MAX_NUM_OF_SIG_GROUP];      /* 回波信号组中的回波个数,1~2个 */
    uint16                  u16FirstEchoDis[MAX_NUM_OF_SIG_GROUP];     /* 第一回波距离 */
	uint16                  u16FirstEchoHeight[MAX_NUM_OF_SIG_GROUP];  /* 第一回波高度 */
    uint16                  u16SecondEchoDis[MAX_NUM_OF_SIG_GROUP];    /* 第二回波距离 */
	uint16                  u16SecondEchoHeight[MAX_NUM_OF_SIG_GROUP]; /* 第二回波高度 */
    uint16                  u16ActualDis[MAX_NUM_OF_SIG_GROUP];        /* 该回波信号组的实际使用的距离 */
    uint16                  u16FirstDisBackup[MAX_NUM_OF_SIG_GROUP];   /* 第一回波距离的备份，用于过滤第一回波前的噪点组成的高路沿 */
	uint16                  u16MaxHeight[MAX_NUM_OF_SIG_GROUP];        /* 该回波信号组的最大回波高度 */
    uint8                   enuOriObjType[MAX_NUM_OF_SIG_GROUP];       /* 通过回波高度初步判定的障碍物类型，主要用于DE CE置信度初始值选取 */
}SnsSigGroupDataUnitType;
#else
typedef struct
{
    uint8                   u8SigGroupCnt;
    uint8                   u8GroupEchoCnt[MAX_NUM_OF_SIG_GROUP];      /* 回波信号组中的回波个数,1~2个 */
    uint16                  u16FirstEchoDis[MAX_NUM_OF_SIG_GROUP];     /* 第一回波距离 */
	uint16                  u16FirstEchoHeight[MAX_NUM_OF_SIG_GROUP];  /* 第一回波高度 */
    uint16                  u16SecondEchoDis[MAX_NUM_OF_SIG_GROUP];    /* 第二回波距离 */
	uint16                  u16SecondEchoHeight[MAX_NUM_OF_SIG_GROUP]; /* 第二回波高度 */
    uint16                  u16ActualDis[MAX_NUM_OF_SIG_GROUP];        /* 该回波信号组的实际使用的距离 */
    uint16                  u16FirstDisBackup[MAX_NUM_OF_SIG_GROUP];   /* 第一回波距离的备份，用于过滤第一回波前的噪点组成的高路沿 */
    uint16                  u16MaxHeight[MAX_NUM_OF_SIG_GROUP];        /* 该回波信号组的最大回波高度 */
    OriginalObjType         enuOriObjType[MAX_NUM_OF_SIG_GROUP];       /* 通过回波高度初步判定的障碍物类型，主要用于DE CE置信度初始值选取 */
}SnsSigGroupDataUnitType;
#endif

/******************************************************************************/
/**
 * @brief   探头原始数据对应系统的参数，含Odo移动距离及系统定时器时间
 */
/******************************************************************************/
#if VS_SIMULATE_ENABLE
typedef struct
{
    uint32                 u32SysTime;
    float                  fCarMoveDisSub;
    uint8                  eCarDir;
    uint8                  eMeasType;
}SnsOrigSystemDataType;
#else
typedef struct
{
    uint32                 u32SysTime;
    float                  fCarMoveDisSub;
    SnsCarMoveDirType      eCarDir;
    PDCSnsMeasTypeType     eMeasType;
}SnsOrigSystemDataType;

#endif

/******************************************************************************/
/**
 * @brief  定义每一个通道的回波信号组数据缓存
 */
/******************************************************************************/
#if VS_SIMULATE_ENABLE
typedef struct
{
    uint8                   u8SigGroupUpdateFlag;                      /* 探头原始信号组数据更新标志 */
    uint8                   u8RecordCnt;                               /* 已经记录的缓存数据个数 */
    uint8                   enuCurIndex;                               /* 当前的索引 */
    SnsOrigSystemDataType   SysDataBuf[SNS_CYCLE_NUM];
    SnsSigGroupDataUnitType MasterBuf[SNS_CYCLE_NUM];
    SnsSigGroupDataUnitType LeftBuf[SNS_CYCLE_NUM];
    SnsSigGroupDataUnitType RightBuf[SNS_CYCLE_NUM];
}SnsSigGroupDataCacheType;
#else
typedef struct
{
    uint8                   u8SigGroupUpdateFlag;                      /* 探头原始信号组数据更新标志 */
    uint8                   u8RecordCnt;                               /* 已经记录的缓存数据个数 */
    Sns_CycleType           enuCurIndex;                               /* 当前的索引 */
    SnsOrigSystemDataType   SysDataBuf[SNS_CYCLE_NUM];
    SnsSigGroupDataUnitType MasterBuf[SNS_CYCLE_NUM];
    SnsSigGroupDataUnitType LeftBuf[SNS_CYCLE_NUM];
    SnsSigGroupDataUnitType RightBuf[SNS_CYCLE_NUM];
}SnsSigGroupDataCacheType;
#endif


typedef struct
{
    uint8 u8DisFollowFlag;          /* 跟踪有效标志位 */
    uint8 u8DisFollowInvalidCnt;    /* 连续跟踪无效计数 */
    uint8 u8FollowUpdateFlag;       /* 定义更新标志位--主要用于坐标定位中，不使用假设跟踪的距离，以免障碍物定位坐标差异较大 */
    uint8 u8ParallelWallCnt;        /* 平行墙场景的大墙计数 */
    uint8 u8ParallelWallFlag;       /* 平行墙场景的大墙计数 */
    
    uint8 u8BlindFollowFlag;        /* 盲区跟踪标志位 */
    uint8 u8BlindFollowCardir;      /* 盲区跟踪时的车的方向 */
    uint8 u8KeepBeepFlag;           /* 盲区跟踪距离消失保持长鸣 */

    uint8 u8FollowNoiseCnt;         /* 跟踪噪点数 */
    uint8 u8FollowNoiseFlag;        /* 跟踪噪点标志位 */
    uint8 u8FollowCnt;              /* 跟踪次数 */
    
    uint16 u16FollowDis;            /* 跟踪的实时距离 */
    uint16 u16DisDetect_Last;       /* 上一次通过探测更新的距离，用于后续匹配不上时做缓存依据以及根据车辆移动距离做跟踪的锚定距离 */  
    
    uint16 u16DisDetect_New;        /* 最新通过探头实际探测的距离 */
    uint16 u16EchoHeightDetect_New; /* 最新通过探头实际探测的回波高度 */
    uint16 u16FirstDis_New;
    uint16 u16FirstHeight_New;
    uint16 u16SecondDis_New;
    uint16 u16SecondHeight_New;
    
    uint16 u16StableOutputDis;      /* 稳定输出的距离，主要用于稳定DE CE输出 */
    uint16 u16StableOutputDisBackup[5];
    float fCarMoveLastDetectToNow;  /* 上一次探测到本次回波更新车辆移动的距离 */
#if VS_SIMULATE_ENABLE
    uint8       enuFollowDisPro;    /* 对应距离的置信度 */
#else
    SnsProbType enuFollowDisPro;    /* 对应距离的置信度 */
#endif
}SnsSigGroupDisFollowPointType;

/* 跟踪的障碍物类型，主要用于区分特殊场景，比如平行墙、石球、单点PVC、直立方管等 */
typedef enum
{
    FOLLOW_OBJ_NONE_TYPE = 0,
    FOLLOW_OBJ_POINT_TYPE,                   /* 点状障碍物，主要针对PVC管类型 */
    FOLLOW_OBJ_OPPOSITE_TYPE,                /* 正对大墙类型，主要是回波高度大于大墙的场景--大墙、路砍 */
    FOLLOW_OBJ_PARALLEL_WALL_TYPE,           /* 平行墙类型，主要针对角雷达、侧雷达而言；典型特征是车辆运动DE 不变化，侧雷达侦听回波很强 */
    FOLLOW_OBJ_ROUND_BALL_TYPE,              /* 石球类型，主要在远距离有典型的扫频数据，定频无数据 */
    FOLLOW_OBJ_SQUARE_PIPE_TYPE,             /* 直立方管类型，主要数据特征是无DE，但CE很强 */
}FolllowObjType;



typedef struct
{
    uint8 u8StandCurbFlag;          /* 直立方柱确认有效标志位 */
    uint8 u8StandCurbValidCnt;      /* 直立方柱确认有效计数 */
    uint8 u8StandCurbInvalidCnt;    /* 直立方柱无效计数 */   
    uint16 u16StandCurbMasterDis;   /* 直立方柱虚拟出来的距离 */
}SnsStandCurbType;


/* 定义PDC数据类型 */
typedef struct
{
	SnsSigGroupDisFollowPointType MasDisData;
	SnsSigGroupDisFollowPointType LeftLisDisData;
	SnsSigGroupDisFollowPointType RightLisDisData;
    #if VS_SIMULATE_ENABLE
    uint8                         eMeasType;    /* 添加探头的工作模式 */
    uint8                         eFolllowObjType;
    #else
    PDCSnsMeasTypeType            eMeasType;    /* 添加探头的工作模式 */
    FolllowObjType                eFolllowObjType;
    #endif
    uint8                         u8ObjParallelWallFlag;    /* 通过主发、侦听确认大墙场景的概率 */
    SnsStandCurbType              LeftStandCurb;
    SnsStandCurbType              RightStandCurb;
    
}SnsSigGroupDisFollowType;


/* 定义卡尔曼更新的变量参数 */
typedef struct
{
	float fCurrentK;       /* 当前周期的卡尔曼增益 */
    float fBestEstimateDis;/* 当前周期的最优估计距离值 */
    float fCurrentP;       /* 当前周期的协方差 */
}KalmanUpdateDataType;

/* 定义卡尔曼更新的变量参数 */
typedef struct
{
	float fBestEstimateDis;   /* 当前周期的最优估计距离值 */
    float fEstimateP;         /* 当前周期的预估协方差 */
}KalmanPredictDataType;

typedef struct
{
	uint8 u8WriteIdx;         				  /* 写索引 */
    uint16 u16DataBuf[STD_DE_FILTER_NUM];     /* DE值 */
}StdDEFilterCtrlType;

/*********************************************以下是障碍物坐标计算的类型定义***************** */
/* 定义第一、第二回波信号组关系 */
typedef enum
{
    FIRST_SECOND_NONE  = 0,
    ONLY_FIRST_ECHO       ,     /* 仅有第一回波 */
    FIRST_BIG_SECOND_ECHO ,     /* 存在第二回波，第一回波较大 */
    FIRST_SMALL_SECOND_ECHO ,   /* 存在第一回波，第二回波较大 */
}SigGroupRelationType;


/* 探头端置信度定义 */
typedef enum
{
    SNS_MAP_PROB_NONE = 0,
    SNS_MAP_PROB_LV1,
    SNS_MAP_PROB_LV2,
    SNS_MAP_PROB_LV3,    
    SNS_MAP_PROB_LV4,
    SNS_MAP_PROB_LV5,
    SNS_MAP_PROB_LV6,
    SNS_MAP_PROB_LV_NUM
}eSnsMapProbLevelType;

/* 探头端Map形成使用的坐标类型 */
typedef enum
{
    SNS_MAP_USE_NONE = 0,
    SNS_MAP_USE_LEFT,
    SNS_MAP_USE_RIGHT,
    SNS_MAP_USE_MASTER,    
    SNS_MAP_USE_LEFT_RIGHT,
}eSnsMapUseCoorType;

/* 定义卡尔曼更新Odo坐标的的变量参数 */
typedef struct
{
	float fCurrentK[2];          /* 当前周期的卡尔曼增益 */
    ObjCoorType BestEstiCoor;    /* 当前周期的最优估计坐标 */
    float fCurrentP[2];          /* 当前周期的协方差 */
    uint8 u8KalmanStartFlag;     /* 开始卡尔曼滤波的标志 */
    uint8 u8KalmanNoMatchCnt;
    uint8 u8KalmanMatchOKCnt;
    uint8 u8CoorNoSameCnt[2];    /* 用于判断坐标不匹配时计数，从而更新P */
}KalmanCoorType;

#if VS_SIMULATE_ENABLE
/* 定义每一个探头跟踪的障碍物坐标数据单元 */
typedef struct
{
    uint8                u8MatchMapId;                                     /* 匹配到的障碍物ID */
    uint8                u8CoorValidFlag;                                  /* 障碍物坐标有效标志 */
    ObjCoorType          strObjSnsCoor;                                    /* 探测障碍物相对于探头的坐标 */
    ObjCoorType          strObjCarCoor;                                    /* 探测障碍物的车辆坐标系坐标 */
    ObjCoorType          strObjOdoCoor;                                    /* 探测障碍物的Odo坐标系坐标 */
    ObjCoorType          strObjOdoFilterCoor;                              /* 经过卡尔曼滤波后的障碍物Odo坐标系坐标 */
    ObjCoorType          strObjCarFilterCoor;                              /* 经过卡尔曼滤波后的障碍物Car坐标系坐标 */
    KalmanCoorType       strKalmanCoor;                                    /* 卡尔曼算法中存储的坐标 */
    uint16               u16ObjHeight;                                     /* 主发第一、第二回波中障碍物的最大回波高度 */
    uint8                enuEchoRelation;                                  /* 探头的回波信号组类型 */
}SnsObjCoorUnitType;
#else
/* 定义每一个探头跟踪的障碍物坐标数据单元 */
typedef struct
{
    uint8                u8MatchMapId;                                     /* 匹配到的障碍物ID */
    uint8                u8CoorValidFlag;                                  /* 障碍物坐标有效标志 */
    ObjCoorType          strObjSnsCoor;                                    /* 探测障碍物相对于探头的坐标 */
    ObjCoorType          strObjCarCoor;                                    /* 探测障碍物的车辆坐标系坐标 */
    ObjCoorType          strObjOdoCoor;                                    /* 探测障碍物的Odo坐标系坐标 */
    ObjCoorType          strObjOdoFilterCoor;                              /* 经过卡尔曼滤波后的障碍物Odo坐标系坐标 */
    ObjCoorType          strObjCarFilterCoor;                              /* 经过卡尔曼滤波后的障碍物Car坐标系坐标 */
    KalmanCoorType       strKalmanCoor;                                    /* 卡尔曼算法中存储的坐标 */
    uint16               u16ObjHeight;                                     /* 主发第一、第二回波中障碍物的最大回波高度 */
    SigGroupRelationType enuEchoRelation;                                  /* 探头的回波信号组类型 */
}SnsObjCoorUnitType;
#endif


/* 根据主发和探头间距，计算有效侦听距离的范围 */
typedef struct
{
    uint16 u16MaxDis;
    uint16 u16MinDis;
}ObjValidListenDisType;

/* 定义一维卡尔曼滤波通用变量类型 */
typedef struct
{
	float fMeasureValue;                                                    /* 当前周期的测量值 */
    float fLastTimePredictValue;                                            /* 上一个周期的最优估计值 */
    float fLastTimePredict_P;                                               /* 上一个周期的方差 */
    float fCurrentTimePredictValue;                                         /* 当前周期的最优估计值 */
    float fCurrentTimePredict_P;                                            /* 当前周期的方差 */
    float fCurrent_K;                                                       /* 当前周期的卡尔曼增益 */
}KalmanVarType;


/* 定义不同类型障碍物的计数 */
typedef struct
{
    uint8                 u8StdPVC_PointCnt;                               /* 定频PVC点个数 */
    uint8                 u8ChirpPVC_PointCnt;                             /* 扫频PVC点的个数 */
    
    uint8                 u8StdLowCurb_PointCnt;                           /* 定频低路沿点个数 */
    uint8                 u8ChirpLowCurb_PointCnt;                         /* 扫频低路沿的个数 */
    
    uint8                 u8StdBigWall_PointCnt;                           /* 定频大墙的个数 */
    uint8                 u8ChirpBigWall_PointCnt;                         /* 扫频大墙的个数 */
    
    uint8                 u8StdHigh_HighCurb_PointCnt;                     /* 定频高路沿个数 */
    uint8                 u8ChirpHigh_HighCurb_PointCnt;                   /* 扫频高路沿个数 */
    
    uint8                 u8StdTotalPointCnt;                              /* 定频模式下总共点的个数 */
    uint8                 u8ChirpTotalPointCnt;                            /* 扫频模式下总共点的个数 */
}ObjTypeCntType;





/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/








#endif /* end of SnsRawData_Type_H */

