/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: IOHal.c
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/
#include "IODrv.h"
#include "IOHal.h"

/*************************************宏定义***********************************/

/********************************数据类型定义**********************************/

/**********************************变量定义************************************/

/******************************************************************************/
/**<pre>
 *函数名称: IOHalInit
 *功能描述: IO模块初始化
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void IOHalInit(void)
{
	R_PORT_SetGpioOutput(Port8, 0, Low);  		/* PowerEn*/
	R_PORT_SetGpioOutput(Port8, 1, Low);  		/* PowerEn-1*/
    R_PORT_SetGpioOutput(Port8, 12, Low);  		/* PowerEn-2*/
	R_PORT_SetGpioOutput(Port8, 11, Low);  		/* PowerEn-3*/

	R_PORT_SetGpioOutput(Port9, 6, Low);  		/* MT_RESB*/
    R_PORT_SetGpioInput(Port8, 6);  			/* MT_RFC*/
	//R_PORT_SetAltFunc(Port0, 6, Alt1, Input);  	/* MT_DCR1B INTP2*/
	//R_PORT_SetAltFunc(Port8, 4, Alt3, Input);  	/* MT_DCR2B INTP8*/
    //R_PORT_SetAltFunc(Port10, 9, , Input);  	/* MT_INTB INTP10 */
	R_PORT_SetGpioInput(Port10, 9); 			/* MT_INTB */

	R_PORT_SetGpioOutput(Port9, 5, Low);  		/* MT1_RESB*/
    R_PORT_SetGpioInput(Port8, 7);  			/* MT1_RFC*/
	//R_PORT_SetAltFunc(Port8, 3, Alt3, Input);  	/* MT1_DCR1B INTP7*/
	//R_PORT_SetAltFunc(Port9, 1, Alt1, Input);  	/* MT1_DCR2B INTP11*/
    //R_PORT_SetAltFunc(Port10, 13, , Input); 	/* MT1_INTB INTP12 */
	R_PORT_SetGpioInput(Port10, 13); 			/* MT1_INTB */

	/* Disable INTP2 operation and clear request */
    INTC2.ICP2.BIT.MKP2 = _INT_PROCESSING_DISABLED;
    INTC2.ICP2.BIT.RFP2 = _INT_REQUEST_NOT_OCCUR;
    /* Disable INTP7 operation and clear request */
    INTC2.ICP7.BIT.MKP7 = _INT_PROCESSING_DISABLED;
    INTC2.ICP7.BIT.RFP7 = _INT_REQUEST_NOT_OCCUR;
    /* Disable INTP8 operation and clear request */
    INTC2.ICP8.BIT.MKP8 = _INT_PROCESSING_DISABLED;
    INTC2.ICP8.BIT.RFP8 = _INT_REQUEST_NOT_OCCUR;
    /* Disable INTP11 operation and clear request */
    INTC2.ICP11.BIT.MKP11 = _INT_PROCESSING_DISABLED;
    INTC2.ICP11.BIT.RFP11 = _INT_REQUEST_NOT_OCCUR;
    /* Set INTP2 setting */
    INTC2.ICP2.BIT.TBP2 = _INT_TABLE_VECTOR;
    INTC2.ICP2.UINT16 &= _INT_PRIORITY_HIGHEST;
    FCLA0.CTL2_INTPL = _INTC_EDGE_FALLING;
    /* Set INTP7 setting */
    INTC2.ICP7.BIT.TBP7 = _INT_TABLE_VECTOR;
    INTC2.ICP7.UINT16 &= _INT_PRIORITY_HIGHEST;
    FCLA0.CTL7_INTPL = _INTC_EDGE_FALLING;
    /* Set INTP8 setting */
    INTC2.ICP8.BIT.TBP8 = _INT_TABLE_VECTOR;
    INTC2.ICP8.UINT16 &= _INT_PRIORITY_HIGHEST;
    FCLA0.CTL0_INTPH = _INTC_EDGE_FALLING;
    /* Set INTP11 setting */
    INTC2.ICP11.BIT.TBP11 = _INT_TABLE_VECTOR;
    INTC2.ICP11.UINT16 &= _INT_PRIORITY_HIGHEST;
    FCLA0.CTL3_INTPH = _INTC_EDGE_FALLING;

    /* Set INTP2 pin */   /* MT_DCR1B INTP2*/
    PORT.PIBC0 &= _PORT_CLEAR_BIT6;
    PORT.PBDC0 &= _PORT_CLEAR_BIT6;
    PORT.PM0 |= _PORT_SET_BIT6;  
    PORT.PMC0 &= _PORT_CLEAR_BIT6;
    PORT.PIPC0 &= _PORT_CLEAR_BIT6;
    PORT.PFC0 &= _PORT_CLEAR_BIT6;
    PORT.PFCE0 &= _PORT_CLEAR_BIT6;
    PORT.PFCAE0 &= _PORT_CLEAR_BIT6;
    PORT.PMC0 |= _PORT_SET_BIT6;  
    /* Set INTP7 pin */  /* MT1_DCR1B INTP7*/
    PORT.PIBC8 &= _PORT_CLEAR_BIT3;
    PORT.PBDC8 &= _PORT_CLEAR_BIT3;
    PORT.PM8 |= _PORT_SET_BIT3;  
    PORT.PMC8 &= _PORT_CLEAR_BIT3;
    PORT.PFC8 &= _PORT_CLEAR_BIT3;
    PORT.PFCE8 |= _PORT_SET_BIT3;  
    PORT.PMC8 |= _PORT_SET_BIT3;  
    /* Set INTP8 pin */  /* MT_DCR2B INTP8*/
    PORT.PIBC8 &= _PORT_CLEAR_BIT4;
    PORT.PBDC8 &= _PORT_CLEAR_BIT4;
    PORT.PM8 |= _PORT_SET_BIT4;  
    PORT.PMC8 &= _PORT_CLEAR_BIT4;
    PORT.PFC8 &= _PORT_CLEAR_BIT4;
    PORT.PFCE8 |= _PORT_SET_BIT4;  
    PORT.PMC8 |= _PORT_SET_BIT4;  
    /* Set INTP11 pin */  /* MT1_DCR2B INTP11*/
    PORT.PIBC9 &= _PORT_CLEAR_BIT1;
    PORT.PBDC9 &= _PORT_CLEAR_BIT1;
    PORT.PM9 |= _PORT_SET_BIT1;  
    PORT.PMC9 &= _PORT_CLEAR_BIT1;
    PORT.PFC9 &= _PORT_CLEAR_BIT1;
    PORT.PFCE9 &= _PORT_CLEAR_BIT1;
    PORT.PMC9 |= _PORT_SET_BIT1; 

#if 0
    /* Set 测试引脚 pin */
    PORT.PIBC10 &= _PORT_CLEAR_BIT2;
    PORT.PBDC10 &= _PORT_CLEAR_BIT2;
    PORT.PM10 |= _PORT_SET_BIT2;  
    PORT.PMC10 &= _PORT_CLEAR_BIT2;
    PORT.PFC10 |= _PORT_SET_BIT2;
    PORT.PFCE10 &= _PORT_CLEAR_BIT2;  
    PORT.PM10 &= _PORT_CLEAR_BIT2;
    // R_PORT_SetGpioOutput(Port10, 2, High);
#endif
}

/******************************************************************************/
/**<pre>
 *函数名称: IOHalClose
 *功能描述: IO模块关闭
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void IOHalClose(void)
{
	/* Disable INTP2 operation and clear request */
    INTC2.ICP2.BIT.MKP2 = _INT_PROCESSING_DISABLED;
    INTC2.ICP2.BIT.RFP2 = _INT_REQUEST_NOT_OCCUR;
    /* Disable INTP7 operation and clear request */
    INTC2.ICP7.BIT.MKP7 = _INT_PROCESSING_DISABLED;
    INTC2.ICP7.BIT.RFP7 = _INT_REQUEST_NOT_OCCUR;
    /* Disable INTP8 operation and clear request */
    INTC2.ICP8.BIT.MKP8 = _INT_PROCESSING_DISABLED;
    INTC2.ICP8.BIT.RFP8 = _INT_REQUEST_NOT_OCCUR;
    /* Disable INTP11 operation and clear request */
    INTC2.ICP11.BIT.MKP11 = _INT_PROCESSING_DISABLED;
    INTC2.ICP11.BIT.RFP11 = _INT_REQUEST_NOT_OCCUR;
}
