/***********************************************************************************************************************
* DISCLAIMER
* This software is supplied by Renesas Electronics Corporation and is only 
* intended for use with Renesas products. No other uses are authorized. This 
* software is owned by Renesas Electronics Corporation and is protected under 
* all applicable laws, including copyright laws.
* THIS SOFTWARE IS PROVIDED "AS IS" AND R<PERSON><PERSON>AS MAKES NO WARRANTIES REGARDING 
* THIS SOFTWARE, WHETHER EXPRESS, IMPLIED OR STATUTORY, INCLUDING BUT NOT 
* LIMITED TO WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE 
* AND NON-INFRINGEMENT.  ALL SUCH WARRANTIES ARE EXPRESSLY DISCLAIMED.
* TO THE MAXIMUM EXTENT PERMITTED NOT PROHIBITED BY LAW, NEITHER RENESAS 
* ELECTRONICS CORPORATION NOR ANY OF ITS AFFILIATED COMPANIES SHALL BE LIABLE 
* FOR ANY DIRECT, INDIRECT, SPECIAL, INCIDENTAL OR CONSEQUENTIAL DAMAGES FOR 
* ANY REASON RELATED TO THIS SOFTWARE, EVEN IF RENESAS OR ITS AFFILIATES HAVE 
* BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.
* Renesas reserves the right, without notice, to make changes to this software 
* and to discontinue the availability of this software.  By using this software, 
* you agree to the additional terms and conditions found by accessing the 
* following link:
* http://www.renesas.com/disclaimer
*
* Copyright (C) 2015, 2018 Renesas Electronics Corporation. All rights reserved.
***********************************************************************************************************************/

/***********************************************************************************************************************
* File Name    : r_cg_dmac_user.c
* Version      : Code Generator for RH850/F1K V1.01.02.02 [08 May 2018]
* Device(s)    : R7F701587(LQFP176pin)
* Tool-Chain   : CCRH
* Description  : This file implements device driver for DMA module.
* Creation Date: 2022/4/2
***********************************************************************************************************************/

/***********************************************************************************************************************
Pragma directive
***********************************************************************************************************************/
/* Start user code for pragma. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */

/***********************************************************************************************************************
Includes
***********************************************************************************************************************/
#include "INTCDrv.h"
#include "INTC_COM.h"
#include "IODrv.h"
/* Start user code for include. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */
/***********************************************************************************************************************
Global variables and functions
***********************************************************************************************************************/
/* Start user code for global. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */

/***********************************************************************************************************************
* Function Name: r_intp10_interrupt
* Description  : None
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
// #pragma interrupt r_intp10_interrupt(enable=false, channel=46, fpu=true, callt=false)
void INTP2IntCallBack(void)
{
    /* Start user code. Do not edit comment generated here */
    /* End user code. Do not edit comment generated here */
    INTC2.ICP2.BIT.RFP2 = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICP2.BIT.MKP2 = _INT_PROCESSING_ENABLED;
}

void INTP7IntCallBack(void)
{
    /* Start user code. Do not edit comment generated here */
    /* End user code. Do not edit comment generated here */
    INTC2.ICP7.BIT.RFP7 = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICP7.BIT.MKP7 = _INT_PROCESSING_ENABLED;
}

void INTP8IntCallBack(void)
{
    /* Start user code. Do not edit comment generated here */
    /* End user code. Do not edit comment generated here */
    INTC2.ICP8.BIT.RFP8 = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICP8.BIT.MKP8 = _INT_PROCESSING_ENABLED;
}

void INTP10IntCallBack(void)
{
    /* Start user code. Do not edit comment generated here */
    /* End user code. Do not edit comment generated here */
    INTC2.ICP10.BIT.RFP10 = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICP10.BIT.MKP10 = _INT_PROCESSING_ENABLED;
}

void INTP11IntCallBack(void)
{
    /* Start user code. Do not edit comment generated here */
    /* End user code. Do not edit comment generated here */
    INTC2.ICP11.BIT.RFP11 = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICP11.BIT.MKP11 = _INT_PROCESSING_ENABLED;
}

void INTP12IntCallBack(void)
{
    /* Start user code. Do not edit comment generated here */
    /* End user code. Do not edit comment generated here */
    /**<pre> 清除中断标志并使能中断 </pre>*/
    INTC2.ICP12.BIT.RFP12 = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICP12.BIT.MKP12 = _INT_PROCESSING_ENABLED;
}

