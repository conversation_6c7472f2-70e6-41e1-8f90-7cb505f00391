<?xml version="1.0" encoding="UTF-8"?>
<project>
    <fileVersion>3</fileVersion>
    <configuration>
        <name>Debug</name>
        <toolchain>
            <name>RH850</name>
        </toolchain>
        <debug>1</debug>
        <settings>
            <name>C-SPY</name>
            <archiveVersion>4</archiveVersion>
            <data>
                <version>2</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>CSPYInput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CSPYProcessor</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYDynDriver</name>
                    <state>E2RH850</state>
                </option>
                <option>
                    <name>CSPYRunToEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CSPYRunoToName</name>
                    <state>main</state>
                </option>
                <option>
                    <name>CSPYMacOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYMacFile</name>
                    <state></state>
                </option>
                <option>
                    <name>CSPYMemOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYMemFile</name>
                    <state>$TOOLKIT_DIR$\config\debugger\ior7f701581.ddf</state>
                </option>
                <option>
                    <name>CSPYMandatory</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CSPYDDFileSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CSPYImagesSuppressCheck1</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYImagesPath1</name>
                    <state></state>
                </option>
                <option>
                    <name>CSPYImagesSuppressCheck2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYImagesPath2</name>
                    <state></state>
                </option>
                <option>
                    <name>CSPYImagesSuppressCheck3</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYImagesPath3</name>
                    <state></state>
                </option>
                <option>
                    <name>CSPYExtraOptionsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>CSPYImagesOffset1</name>
                    <state></state>
                </option>
                <option>
                    <name>CSPYImagesOffset2</name>
                    <state></state>
                </option>
                <option>
                    <name>CSPYImagesOffset3</name>
                    <state></state>
                </option>
                <option>
                    <name>CSPYImagesUse1</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYImagesUse2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYImagesUse3</name>
                    <state>0</state>
                </option>
                <option>
                    <name>DebuggerDoubleSize</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>E1RH850</name>
            <archiveVersion>4</archiveVersion>
            <data>
                <version>3</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>E1Mandatory</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E1SuppressLoad</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E1VerifyLoad</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E1DoLogfile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E1LogFile</name>
                    <state>$PROJ_DIR$\cspycomm.log</state>
                </option>
                <option>
                    <name>E1_LPD</name>
                    <state>1</state>
                </option>
                <option>
                    <name>E1_OverrideDefBaud</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E1_OverrideDefClock</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E1_BaudRate</name>
                    <state></state>
                </option>
                <option>
                    <name>E1_ClockFrequency</name>
                    <state></state>
                </option>
                <option>
                    <name>E1SerialNo</name>
                    <state></state>
                </option>
                <option>
                    <name>E1UseSerialNo</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>E2RH850</name>
            <archiveVersion>4</archiveVersion>
            <data>
                <version>3</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>E2Mandatory</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E2SuppressLoad</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E2VerifyLoad</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E2DoLogfile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E2LogFile</name>
                    <state>$PROJ_DIR$\cspycomm.log</state>
                </option>
                <option>
                    <name>E2_LPD</name>
                    <state>1</state>
                </option>
                <option>
                    <name>E2_OverrideDefBaud</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E2_OverrideDefClock</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E2_BaudRate</name>
                    <state></state>
                </option>
                <option>
                    <name>E2_ClockFrequency</name>
                    <state></state>
                </option>
                <option>
                    <name>E2SerialNo</name>
                    <state></state>
                </option>
                <option>
                    <name>E2UseSerialNo</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>E20RH850</name>
            <archiveVersion>4</archiveVersion>
            <data>
                <version>3</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>E20Mandatory</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E20SuppressLoad</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E20VerifyLoad</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E20DoLogfile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E20LogFile</name>
                    <state>$PROJ_DIR$\cspycomm.log</state>
                </option>
                <option>
                    <name>E20_LPD</name>
                    <state>1</state>
                </option>
                <option>
                    <name>E20_OverrideDefBaud</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E20_OverrideDefClock</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E20_BaudRate</name>
                    <state></state>
                </option>
                <option>
                    <name>E20_ClockFrequency</name>
                    <state></state>
                </option>
                <option>
                    <name>E20SerialNo</name>
                    <state></state>
                </option>
                <option>
                    <name>E20UseSerialNo</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>SIMRH850</name>
            <archiveVersion>4</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>SIMMandatory</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <debuggerPlugins>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\embOS\embOSPlugin.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\OpenRTOS\OpenRTOSPlugin.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\SafeRTOS\SafeRTOSPlugin.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$EW_DIR$\common\plugins\CodeCoverage\CodeCoverage.ENU.ewplugin</file>
                <loadFlag>1</loadFlag>
            </plugin>
            <plugin>
                <file>$EW_DIR$\common\plugins\Orti\Orti.ENU.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$EW_DIR$\common\plugins\TargetAccessServer\TargetAccessServer.ENU.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$EW_DIR$\common\plugins\uCProbe\uCProbePlugin.ENU.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
        </debuggerPlugins>
    </configuration>
    <configuration>
        <name>Release</name>
        <toolchain>
            <name>RH850</name>
        </toolchain>
        <debug>0</debug>
        <settings>
            <name>C-SPY</name>
            <archiveVersion>4</archiveVersion>
            <data>
                <version>2</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>CSPYInput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CSPYProcessor</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYDynDriver</name>
                    <state>SIMRH850</state>
                </option>
                <option>
                    <name>CSPYRunToEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CSPYRunoToName</name>
                    <state>main</state>
                </option>
                <option>
                    <name>CSPYMacOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYMacFile</name>
                    <state></state>
                </option>
                <option>
                    <name>CSPYMemOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYMemFile</name>
                    <state></state>
                </option>
                <option>
                    <name>CSPYMandatory</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CSPYDDFileSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CSPYImagesSuppressCheck1</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYImagesPath1</name>
                    <state></state>
                </option>
                <option>
                    <name>CSPYImagesSuppressCheck2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYImagesPath2</name>
                    <state></state>
                </option>
                <option>
                    <name>CSPYImagesSuppressCheck3</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYImagesPath3</name>
                    <state></state>
                </option>
                <option>
                    <name>CSPYExtraOptionsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>CSPYImagesOffset1</name>
                    <state></state>
                </option>
                <option>
                    <name>CSPYImagesOffset2</name>
                    <state></state>
                </option>
                <option>
                    <name>CSPYImagesOffset3</name>
                    <state></state>
                </option>
                <option>
                    <name>CSPYImagesUse1</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYImagesUse2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYImagesUse3</name>
                    <state>0</state>
                </option>
                <option>
                    <name>DebuggerDoubleSize</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>E1RH850</name>
            <archiveVersion>4</archiveVersion>
            <data>
                <version>3</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>E1Mandatory</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E1SuppressLoad</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E1VerifyLoad</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E1DoLogfile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E1LogFile</name>
                    <state>$PROJ_DIR$\cspycomm.log</state>
                </option>
                <option>
                    <name>E1_LPD</name>
                    <state>1</state>
                </option>
                <option>
                    <name>E1_OverrideDefBaud</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E1_OverrideDefClock</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E1_BaudRate</name>
                    <state></state>
                </option>
                <option>
                    <name>E1_ClockFrequency</name>
                    <state></state>
                </option>
                <option>
                    <name>E1SerialNo</name>
                    <state></state>
                </option>
                <option>
                    <name>E1UseSerialNo</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>E2RH850</name>
            <archiveVersion>4</archiveVersion>
            <data>
                <version>3</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>E2Mandatory</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E2SuppressLoad</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E2VerifyLoad</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E2DoLogfile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E2LogFile</name>
                    <state>$PROJ_DIR$\cspycomm.log</state>
                </option>
                <option>
                    <name>E2_LPD</name>
                    <state>1</state>
                </option>
                <option>
                    <name>E2_OverrideDefBaud</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E2_OverrideDefClock</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E2_BaudRate</name>
                    <state></state>
                </option>
                <option>
                    <name>E2_ClockFrequency</name>
                    <state></state>
                </option>
                <option>
                    <name>E2SerialNo</name>
                    <state></state>
                </option>
                <option>
                    <name>E2UseSerialNo</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>E20RH850</name>
            <archiveVersion>4</archiveVersion>
            <data>
                <version>3</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>E20Mandatory</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E20SuppressLoad</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E20VerifyLoad</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E20DoLogfile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E20LogFile</name>
                    <state>$PROJ_DIR$\cspycomm.log</state>
                </option>
                <option>
                    <name>E20_LPD</name>
                    <state>1</state>
                </option>
                <option>
                    <name>E20_OverrideDefBaud</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E20_OverrideDefClock</name>
                    <state>0</state>
                </option>
                <option>
                    <name>E20_BaudRate</name>
                    <state></state>
                </option>
                <option>
                    <name>E20_ClockFrequency</name>
                    <state></state>
                </option>
                <option>
                    <name>E20SerialNo</name>
                    <state></state>
                </option>
                <option>
                    <name>E20UseSerialNo</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>SIMRH850</name>
            <archiveVersion>4</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>SIMMandatory</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <debuggerPlugins>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\embOS\embOSPlugin.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\OpenRTOS\OpenRTOSPlugin.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\SafeRTOS\SafeRTOSPlugin.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$EW_DIR$\common\plugins\CodeCoverage\CodeCoverage.ENU.ewplugin</file>
                <loadFlag>1</loadFlag>
            </plugin>
            <plugin>
                <file>$EW_DIR$\common\plugins\Orti\Orti.ENU.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$EW_DIR$\common\plugins\TargetAccessServer\TargetAccessServer.ENU.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$EW_DIR$\common\plugins\uCProbe\uCProbePlugin.ENU.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
        </debuggerPlugins>
    </configuration>
</project>
