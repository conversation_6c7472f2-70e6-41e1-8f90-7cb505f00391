/*==================================================================================================
*   Project              : RTD AUTOSAR 4.4
*   Platform             : CORTEXM
*   Peripheral           : 
*   Dependencies         : 
*
*   Autosar Version      : 4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : 0.8.0
*   Build Version        : S32K3_RTD_0_8_0_D2303_ASR_REL_4_4_REV_0000_20230310
*
*   (c) Copyright 2020 - 2022 NXP Semiconductors
*   All Rights Reserved.
*
*   NXP Confidential. This software is owned or controlled by NXP and may only be
*   used strictly in accordance with the applicable license terms. By expressly
*   accepting such terms or by downloading, installing, activating and/or otherwise
*   using the software, you are agreeing that you have read, and that you agree to
*   comply with and are bound by, such license terms. If you do not agree to be
*   bound by the applicable license terms, then you may not retain, install,
*   activate or otherwise use the software.
==================================================================================================*/

#ifndef CANTRCV_FS23_IP_H
#define CANTRCV_FS23_IP_H

/**
* @file       CanTrcv_fs23_Ip.h
*
* @addtogroup CANTRCV_FS23_DRIVER
* @{
*/

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                                        INCLUDE FILES
* 1) system and project includes
* 2) needed interfaces from external units
* 3) internal and external interfaces from this unit
==================================================================================================*/
//#include "Can_GeneralTypes.h"
#include "types.h"
#include "CDD_Sbc_fs23.h"

/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/

/*==================================================================================================
*                                             ENUMS
==================================================================================================*/
/**
* @brief          CAN Transceiver modes.
* @details        Operating modes of the CAN Transceiver Driver.
*
*/
typedef enum
{
    CANTRCV_TRCVMODE_NORMAL = 0U, /**< @brief Transceiver mode NORMAL */
    CANTRCV_TRCVMODE_STANDBY,     /**< @brief Transceiver mode STANDBY */
    CANTRCV_TRCVMODE_SLEEP        /**< @brief Transceiver mode SLEEP */
} CanTrcv_TrcvModeType;


/**
* @brief Possible operating modes.
*/
typedef enum
{
    /** @brief Sleep mode without wake-up. */
    CANTRCV_FS23_TRCVMODE_OFF = 0U,
    /** @brief Listen-only mode. */
    CANTRCV_FS23_TRCVMODE_LISTENONLY,
    /** @brief Normal mode. */
    CANTRCV_FS23_TRCVMODE_NORMAL,
    /** @brief Normal mode. */
    CANTRCV_FS23_TRCVMODE_INVALID
} CanTrcv_fs23_TrcvModeType;

/**
* @brief Wake up source.
*/
typedef enum
{
    /** @brief No wake up event occurred. */
    CANTRCV_FS23_WU_NO_EVENT = 0U,
    /** @brief IO pin event. */
    CANTRCV_FS23_WU_PIN_WAKEUP,
    /** @brief CAN bus wake up event. */
    CANTRCV_FS23_WU_CAN_BUS_WAKEUP,
    /** @brief Wake-up event from long duration timer. */
    CANTRCV_FS23_WU_INTERNAL_WAKEUP
} CanTrcv_fs23_WuReasonType;


/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/
/**
* @brief This data structure contains a configuration of the FS23 device.
*        This structure is used as a parameter for CanTrcv_fs23_Init function.
*/
/** @implements    CanTrcv_fs23_ConfigurationDataType_Struct */
typedef struct
{
    /** @brief CAN bus wake up functionality mode (enabled/disabled) */
    boolean canBusWuEnable;
    /** @brief Disable the CAN when RSTB or LIMP0 or FS0B is activated. */
    boolean canFailsafeDisable;
    /** @brief Initial state. */
    CanTrcv_TrcvModeType initState;
} CanTrcv_fs23_ConfigurationDataType;

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/

#define CANTRCV_43_FS23_START_SEC_CODE
//#include "CanTrcv_43_fs23_MemMap.h"

/**
* @brief        Initializes the CanTrcv FS23 driver.
*
* @param        Transceiver     Which device to initialize.
* @param        ConfigData      Pointer to configuration of the device and SPI.
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
Std_ReturnType CanTrcv_fs23_Init(uint8 Transceiver, const CanTrcv_fs23_ConfigurationDataType* ConfigData);

/**
* @brief        Sets the mode of the Transceiver to the value OpMode.
*
* @param        Transceiver     Which device to address.
* @param        OpMode          Desired operational mode.
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
Std_ReturnType CanTrcv_fs23_SetMode(uint8 Transceiver, CanTrcv_fs23_TrcvModeType OpMode);

/**
* @brief        Reads the mode of the Transceiver from 'Mode control register'
*               and returns it in OpMode parameter.
*
* @param        Transceiver     Which device to address.
* @param        OpMode          Stores the mode of the Transceiver.
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
Std_ReturnType CanTrcv_fs23_GetMode(uint8 Transceiver, CanTrcv_fs23_TrcvModeType* OpMode);

/**
* @brief        Retrieves the wake up reason.
*
* @param        Transceiver     Which device to address.
* @param        WuReason        Stores the Wake-up reason of the Transceiver.
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
Std_ReturnType CanTrcv_fs23_GetWuReason(uint8 Transceiver, CanTrcv_fs23_WuReasonType* WuReason);

/**
* @brief        Clears all wake up flag.
*
* @param        Transceiver     Which device to address.
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
Std_ReturnType CanTrcv_fs23_ClearWuFlags(uint8 Transceiver);

/**
* @brief        Retrieves the status of the CAN Transceiver.
*
* @param[in]    Transceiver     Which Transceiver to address.
* @param[in]    CanStatus       Status data of the Transceiver.
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
Std_ReturnType CanTrcv_fs23_GetCanStatus(uint8 Transceiver, uint16* CanStatus);

/**
* @brief        Deinitializes the FS23 driver.
*               Clears the internal run-time configuration and puts the device to standby mode.
*
* @param        Transceiver     Which device to address.
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
Std_ReturnType CanTrcv_fs23_Deinit(uint8 Transceiver);

#define CANTRCV_43_FS23_STOP_SEC_CODE
//#include "CanTrcv_43_fs23_MemMap.h"

#ifdef __cplusplus
}
#endif

#endif /*CANTRCV_FS23_IP_H*/

/** @} */
