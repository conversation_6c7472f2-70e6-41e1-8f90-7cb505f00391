/***********************************************************************************************************************
* DISCLAIMER
* This software is supplied by Renesas Electronics Corporation and is only 
* intended for use with Renesas products. No other uses are authorized. This 
* software is owned by Renesas Electronics Corporation and is protected under 
* all applicable laws, including copyright laws.
* THIS SOFTWARE IS PROVIDED "AS IS" AND R<PERSON><PERSON>AS MAKES NO WARRANTIES REGARDING 
* THIS SOFTWARE, WHETHER EXPRESS, IMPLIED OR STATUTORY, INCLUDING BUT NOT 
* LIMITED TO WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE 
* AND NON-INFRINGEMENT.  ALL SUCH WARRANTIES ARE EXPRESSLY DISCLAIMED.
* TO THE MAXIMUM EXTENT PERMITTED NOT PROHIBITED BY LAW, NEITHER RENESAS 
* ELECTRONICS CORPORATION NOR ANY OF ITS AFFILIATED COMPANIES SHALL BE LIABLE 
* FOR ANY DIRECT, INDIRECT, SPECIAL, INCIDENTAL OR CONSEQUENTIAL DAMAGES FOR 
* ANY REASON RELATED TO THIS SOFTWARE, EVEN IF RENESAS OR ITS AFFILIATES HAVE 
* BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.
* Renesas reserves the right, without notice, to make changes to this software 
* and to discontinue the availability of this software.  By using this software, 
* you agree to the additional terms and conditions found by accessing the 
* following link:
* http://www.renesas.com/disclaimer
*
* Copyright (C) 2015, 2018 Renesas Electronics Corporation. All rights reserved.
***********************************************************************************************************************/

/***********************************************************************************************************************
* File Name    : r_cg_dmac.c
* Version      : Code Generator for RH850/F1K V1.01.02.02 [08 May 2018]
* Device(s)    : R7F701587(LQFP176pin)
* Tool-Chain   : CCRH
* Description  : This file implements device driver for DMA module.
* Creation Date: 2022/4/2
***********************************************************************************************************************/

/***********************************************************************************************************************
Pragma directive
***********************************************************************************************************************/
/* Start user code for pragma. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */

/***********************************************************************************************************************
Includes
***********************************************************************************************************************/
#include "DMADrv.h"
#include "CSIHDrv.h"
/* Start user code for include. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */
static void DMA_PEGInit(void);
/***********************************************************************************************************************
Global variables and functions
***********************************************************************************************************************/
extern volatile uint32 g_cg_sync_read;
extern volatile uint32 g_cg_sync_read;

extern CSIHTxRxTYPE GstrCSIH2TxRxFrame;
extern CSIHTxRxTYPE GstrCSIH3TxRxFrame;

static uint8 SBC_DmaTransmitFinishFlg = 0;

/* Start user code for global. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */

/******************************************************************************/
/**<pre>
 *函数名称: DMADrvInit
 *功能描述: DMA外设初始化
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void DMADrvInit(void)
{
    #if CSIH_DMA_START_ENABLE
    R_DMAC00_Create();
    R_DMAC01_Create();
    R_DMAC02_Create();
    R_DMAC03_Create();
    R_DMAC04_Create();
    R_DMAC05_Create();
	DMA_PEGInit();
    #endif
}
/******************************************************************************/
/**<pre>
 *函数名称: DMADrvStop
 *功能描述: 关闭DMA外设
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void DMADrvStop(void)
{
    R_DMAC00_Stop();
    R_DMAC01_Stop();
    R_DMAC02_Stop();
    R_DMAC03_Stop();
    R_DMAC04_Stop();
    R_DMAC05_Stop();
}
/******************************************************************************/
/**<pre>
 *函数名称: DMA_PEGInit
 *功能描述: 配置PEG，否则不能配置DMA
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
static void DMA_PEGInit(void)
{
    PEG.SP.UINT32=0x00000001;
    PEG.G1MK.UINT32=0xFFFFF000;
    //PEG.G1BA.UINT32=0xFEBC0027;
    PEG.G1BA.UINT32=0x00000027;
}

/***********************************************************************************************************************
* Function Name: R_DMAC_Suspend
* Description  : This function Suspends the DMAC module.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC_Suspend(void)
{
    DMAC.TL = _DMAC_SUSPEND_REQUEST;
}
/***********************************************************************************************************************
* Function Name: R_DMAC_Resume
* Description  : This function Resumes the DMAC module.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC_Resume(void)
{
    DMAC.TL = _DMAC_SUSPEND_CLEARED;
}
/***********************************************************************************************************************
* Function Name: R_DMAC00_Create
* Description  : This function initializes DMAC00 channel.发送
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC00_Create(void)
{
    /* Disable DMAC00 operation */
    DMAC.DCEN0 = _DMAC_CHANNEL_OPERATION_DISABLED;
    /* Disable DMAC00 operation and clear request */
    INTC2.ICDMA0.BIT.MKDMA0 = _INT_PROCESSING_DISABLED;
    INTC2.ICDMA0.BIT.RFDMA0 = _INT_REQUEST_NOT_OCCUR;
    /* Set DMAC00 interrupt setting */
    INTC2.ICDMA0.BIT.TBDMA0 = _INT_TABLE_VECTOR;
    INTC2.ICDMA0.UINT16 &= _INT_PRIORITY_LOWEST;
    /* Set DMAC00 global setting */
    DMAC.DM00CM = _DMAC_PE1_SETTING | _DMAC_SPID1_SETTING | _DMAC_SUPERVISON_MODE;
    /* Set DMAC00 transfer setting */
    DMAC.DSA0 = _DMAC0_SOURCE_ADDRESS;
    DMAC.DDA0 = _DMAC0_DESTINATION_ADDRESS;
    DMAC.DTC0 = _DMAC0_TRANSFER_COUNT - 1;
    DMAC.DTCT0 = _DMAC_CYCLES_EXECUTED | _DMAC_TRANSFER_HARDWARE | _DMAC_SELECT_CHAIN_0 | _DMAC_CHAIN_DISABLE | 
                 _DMAC_TRANSFER_COMPLETION_INTERRUPT_ENABLE | _DMAC_CONTINUOUS_TRANSFER_DISABLE | 
                 _DMAC_RELOAD2_DISABLE | _DMAC_RELOAD1_ALL_CONTROL | _DMAC_DESTINATION_FIXED | _DMAC_SOURCE_INCREMENT | 
                 _DMAC_TRANSFER_DATA_8BITS | _DMAC_SINGLE_TRANSFER;
    DMAC.DTFR0 = _DMAC_TRIGGER_SOURCE_89 | _DMAC_HARDWARE_TRIGGER_ENABLE;

    DMAC.DRSA0 = _DMAC0_SOURCE_ADDRESS;
    DMAC.DRDA0 = _DMAC0_DESTINATION_ADDRESS;
    DMAC.DRTC0 = _DMAC0_TRANSFER_COUNT -1;
}
/***********************************************************************************************************************
* Function Name: R_DMAC00_Start
* Description  : This function enable DMAC00 activation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC00_Start(void)
{
    /* Clear DMAC00 complete interrupt (INTDMA0) request and enable operation */
    INTC2.ICDMA0.BIT.RFDMA0 = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICDMA0.BIT.MKDMA0 = _INT_PROCESSING_DISABLED;
    /* Enable DMAC00 operation */
    DMAC.DCSTC0 |= 0x91;
    DMAC.DTFRRQC0 = 0x01;

    DMAC.DCEN0 = _DMAC_CHANNEL_OPERATION_ENABLED;
}
/***********************************************************************************************************************
* Function Name: R_DMAC00_Stop
* Description  : This function disable DMAC00 activation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC00_Stop(void)
{
    /* Disable DMAC00 operation */
    DMAC.DCEN0 = _DMAC_CHANNEL_OPERATION_DISABLED;
    /* Disable DMAC00 operation and clear request */
    INTC2.ICDMA0.BIT.MKDMA0 = _INT_PROCESSING_DISABLED;
    INTC2.ICDMA0.BIT.RFDMA0 = _INT_REQUEST_NOT_OCCUR;
    /* Synchronization processing */
    g_cg_sync_read = DMAC.DCEN0;
}
/***********************************************************************************************************************
* Function Name: R_DMAC00_Set_SoftwareTrigger
* Description  : This function set DMAC00 software trigger.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC00_Set_SoftwareTrigger(void)
{
    /* Set DMAC00 software trigger */
    DMAC.DCSTS0 = _DMAC_SET_REQUEST_FLAG;
}
/***********************************************************************************************************************
* Function Name: R_DMAC00_Suspend
* Description  : This function Suspends DMAC00 channel operation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC00_Suspend(void)
{
    /* Enable DMAC00 operation */
    DMAC.DCEN0 = _DMAC_CHANNEL_OPERATION_DISABLED;
}
/***********************************************************************************************************************
* Function Name: R_DMAC00_Resume
* Description  : This function Resumes DMAC00 channel operation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC00_Resume(void)
{
    /* Disable DMAC00 operation */ 
    DMAC.DCEN0 = _DMAC_CHANNEL_OPERATION_ENABLED;
}
/***********************************************************************************************************************
* Function Name: R_DMAC01_Create
* Description  : This function initializes DMAC01 channel.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC01_Create(void)
{
    /* Disable DMAC01 operation */
    DMAC.DCEN1 = _DMAC_CHANNEL_OPERATION_DISABLED;
    /* Disable DMAC01 operation and clear request */
    INTC2.ICDMA1.BIT.MKDMA1 = _INT_PROCESSING_DISABLED;
    INTC2.ICDMA1.BIT.RFDMA1 = _INT_REQUEST_NOT_OCCUR;
    /* Set DMAC01 interrupt setting */
    INTC2.ICDMA1.BIT.TBDMA1 = _INT_TABLE_VECTOR;
    INTC2.ICDMA1.UINT16 &= _INT_PRIORITY_LOWEST;
    /* Set DMAC01 global setting */
    DMAC.DM01CM = _DMAC_PE1_SETTING | _DMAC_SPID1_SETTING | _DMAC_SUPERVISON_MODE;
    /* Set DMAC01 transfer setting */
    DMAC.DSA1 = _DMAC1_SOURCE_ADDRESS;
    DMAC.DDA1 = _DMAC1_DESTINATION_ADDRESS;
    DMAC.DTC1 = _DMAC1_TRANSFER_COUNT;
    DMAC.DTCT1 = _DMAC_CYCLES_EXECUTED | _DMAC_TRANSFER_HARDWARE | _DMAC_SELECT_CHAIN_0| _DMAC_CHAIN_DISABLE | 
                 _DMAC_TRANSFER_COMPLETION_INTERRUPT_ENABLE | _DMAC_CONTINUOUS_TRANSFER_DISABLE | 
                 _DMAC_RELOAD2_DISABLE | _DMAC_RELOAD1_ALL_CONTROL | _DMAC_DESTINATION_INCREMENT | _DMAC_SOURCE_FIXED | 
                 _DMAC_TRANSFER_DATA_8BITS | _DMAC_SINGLE_TRANSFER;
    DMAC.DTFR1 = _DMAC_TRIGGER_SOURCE_90 | _DMAC_HARDWARE_TRIGGER_ENABLE;

    /* Set DMAC01 reload setting */
    DMAC.DRSA1 = _DMAC1_SOURCE_ADDRESS;
    DMAC.DRDA1 = _DMAC1_DESTINATION_ADDRESS;
    DMAC.DRTC1 = _DMAC1_TRANSFER_COUNT;
}
/***********************************************************************************************************************
* Function Name: R_DMAC01_Start
* Description  : This function enable DMAC01 activation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC01_Start(void)
{
    /* Clear DMAC00 complete interrupt (INTDMA0) request and enable operation */
    INTC2.ICDMA1.BIT.RFDMA1 = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICDMA1.BIT.MKDMA1 = _INT_PROCESSING_ENABLED;
    /* Enable DMAC00 operation */
    DMAC.DCSTC1 |= 0x91;
    DMAC.DTFRRQC1 = 0x01;

    DMAC.DCEN1 = _DMAC_CHANNEL_OPERATION_ENABLED;
}
/***********************************************************************************************************************
* Function Name: R_DMAC01_Stop
* Description  : This function disable DMAC01 activation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC01_Stop(void)
{
    /* Disable DMAC01 operation */
    DMAC.DCEN1 = _DMAC_CHANNEL_OPERATION_DISABLED;
    /* Disable DMAC01 operation and clear request */
    INTC2.ICDMA1.BIT.MKDMA1 = _INT_PROCESSING_DISABLED;
    INTC2.ICDMA1.BIT.RFDMA1 = _INT_REQUEST_NOT_OCCUR;
    /* Synchronization processing */
    g_cg_sync_read = DMAC.DCEN1;
}
/***********************************************************************************************************************
* Function Name: R_DMAC01_Set_SoftwareTrigger
* Description  : This function set DMAC01 software trigger.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC01_Set_SoftwareTrigger(void)
{
    /* Set DMAC01 software trigger */
    DMAC.DCSTS1 = _DMAC_SET_REQUEST_FLAG;
}
/***********************************************************************************************************************
* Function Name: R_DMAC01_Suspend
* Description  : This function Suspends DMAC01 channel operation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC01_Suspend(void)
{
    /* Enable DMAC01 operation */
    DMAC.DCEN1 = _DMAC_CHANNEL_OPERATION_DISABLED;
}
/***********************************************************************************************************************
* Function Name: R_DMAC01_Resume
* Description  : This function Resumes DMAC01 channel operation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC01_Resume(void)
{
    /* Disable DMAC01 operation */ 
    DMAC.DCEN1 = _DMAC_CHANNEL_OPERATION_ENABLED;
}

/***********************************************************************************************************************
* Function Name: R_DMAC00_Create
* Description  : This function initializes DMAC00 channel.发送
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC02_Create(void)
{
    DMAC.DCEN2 = _DMAC_CHANNEL_OPERATION_DISABLED;
    INTC2.ICDMA2.BIT.MKDMA2 = _INT_PROCESSING_DISABLED;
    INTC2.ICDMA2.BIT.RFDMA2 = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICDMA2.BIT.TBDMA2 = _INT_TABLE_VECTOR;
    INTC2.ICDMA2.UINT16 &= _INT_PRIORITY_LOWEST;
    DMAC.DM02CM = _DMAC_PE1_SETTING | _DMAC_SPID1_SETTING | _DMAC_SUPERVISON_MODE;
    DMAC.DSA2 = _DMAC2_SOURCE_ADDRESS;
    DMAC.DDA2 = _DMAC2_DESTINATION_ADDRESS;
    DMAC.DTC2 = _DMAC2_TRANSFER_COUNT - 1;
    DMAC.DTCT2 = _DMAC_CYCLES_EXECUTED | _DMAC_TRANSFER_HARDWARE | _DMAC_SELECT_CHAIN_0 | _DMAC_CHAIN_DISABLE | 
                 _DMAC_TRANSFER_COMPLETION_INTERRUPT_ENABLE | _DMAC_CONTINUOUS_TRANSFER_DISABLE | 
                 _DMAC_RELOAD2_DISABLE | _DMAC_RELOAD1_ALL_CONTROL | _DMAC_DESTINATION_FIXED | _DMAC_SOURCE_INCREMENT | 
                 _DMAC_TRANSFER_DATA_8BITS | _DMAC_SINGLE_TRANSFER;
    DMAC.DTFR2 = _DMAC_TRIGGER_SOURCE_41 | _DMAC_HARDWARE_TRIGGER_ENABLE;
    DMAC.DRSA2 = _DMAC0_SOURCE_ADDRESS;
    DMAC.DRDA2 = _DMAC0_DESTINATION_ADDRESS;
    DMAC.DRTC2 = _DMAC0_TRANSFER_COUNT -1;

    /* Set DMAC02 reload setting */
    DMAC.DRSA2 = _DMAC2_SOURCE_ADDRESS;
    DMAC.DRDA2 = _DMAC2_DESTINATION_ADDRESS;
    DMAC.DRTC2 = _DMAC2_TRANSFER_COUNT;
}
/***********************************************************************************************************************
* Function Name: R_DMAC00_Start
* Description  : This function enable DMAC00 activation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC02_Start(void)
{
    /* Clear DMAC00 complete interrupt (INTDMA0) request and enable operation */
    INTC2.ICDMA2.BIT.RFDMA2 = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICDMA2.BIT.MKDMA2 = _INT_PROCESSING_DISABLED;
    /* Enable DMAC00 operation */
    DMAC.DCSTC2 |= 0x91;
    DMAC.DTFRRQC2 = 0x01;
    DMAC.DCEN2 = _DMAC_CHANNEL_OPERATION_ENABLED;
}
/***********************************************************************************************************************
* Function Name: R_DMAC00_Stop
* Description  : This function disable DMAC00 activation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC02_Stop(void)
{
    /* Disable DMAC00 operation */
    DMAC.DCEN2 = _DMAC_CHANNEL_OPERATION_DISABLED;
    /* Disable DMAC00 operation and clear request */
    INTC2.ICDMA2.BIT.MKDMA2 = _INT_PROCESSING_DISABLED;
    INTC2.ICDMA2.BIT.RFDMA2 = _INT_REQUEST_NOT_OCCUR;
    /* Synchronization processing */
    g_cg_sync_read = DMAC.DCEN2;
}
/***********************************************************************************************************************
* Function Name: R_DMAC00_Set_SoftwareTrigger
* Description  : This function set DMAC00 software trigger.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC02_Set_SoftwareTrigger(void)
{
    /* Set DMAC00 software trigger */
    DMAC.DCSTS2 = _DMAC_SET_REQUEST_FLAG;
}
/***********************************************************************************************************************
* Function Name: R_DMAC00_Suspend
* Description  : This function Suspends DMAC00 channel operation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC02_Suspend(void)
{
    /* Enable DMAC00 operation */
    DMAC.DCEN2 = _DMAC_CHANNEL_OPERATION_DISABLED;
}
/***********************************************************************************************************************
* Function Name: R_DMAC00_Resume
* Description  : This function Resumes DMAC00 channel operation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC02_Resume(void)
{
    /* Disable DMAC00 operation */ 
    DMAC.DCEN2 = _DMAC_CHANNEL_OPERATION_ENABLED;
}
/***********************************************************************************************************************
* Function Name: R_DMAC01_Create
* Description  : This function initializes DMAC01 channel.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC03_Create(void)
{
    DMAC.DCEN3 = _DMAC_CHANNEL_OPERATION_DISABLED;
    INTC2.ICDMA3.BIT.MKDMA3 = _INT_PROCESSING_DISABLED;
    INTC2.ICDMA3.BIT.RFDMA3 = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICDMA3.BIT.TBDMA3 = _INT_TABLE_VECTOR;
    INTC2.ICDMA3.UINT16 &= _INT_PRIORITY_LOWEST;
    DMAC.DM03CM = _DMAC_PE1_SETTING | _DMAC_SPID1_SETTING | _DMAC_SUPERVISON_MODE;
    DMAC.DSA3 = _DMAC2_SOURCE_ADDRESS;
    DMAC.DDA3 = _DMAC2_DESTINATION_ADDRESS;
    DMAC.DTC3 = _DMAC2_TRANSFER_COUNT - 1;
    DMAC.DTCT3 = _DMAC_CYCLES_EXECUTED | _DMAC_TRANSFER_HARDWARE | _DMAC_SELECT_CHAIN_0 | _DMAC_CHAIN_DISABLE | 
                 _DMAC_TRANSFER_COMPLETION_INTERRUPT_ENABLE | _DMAC_CONTINUOUS_TRANSFER_DISABLE | 
                 _DMAC_RELOAD2_DISABLE | _DMAC_RELOAD1_ALL_CONTROL | _DMAC_DESTINATION_INCREMENT | _DMAC_SOURCE_FIXED | 
                 _DMAC_TRANSFER_DATA_8BITS | _DMAC_SINGLE_TRANSFER;
    DMAC.DTFR3 = _DMAC_TRIGGER_SOURCE_42 | _DMAC_HARDWARE_TRIGGER_ENABLE;
    DMAC.DRSA3 = _DMAC0_SOURCE_ADDRESS;
    DMAC.DRDA3 = _DMAC0_DESTINATION_ADDRESS;
    DMAC.DRTC3 = _DMAC0_TRANSFER_COUNT;

    /* Set DMAC03 reload setting */
    DMAC.DRSA3 = _DMAC3_SOURCE_ADDRESS;
    DMAC.DRDA3 = _DMAC3_DESTINATION_ADDRESS;
    DMAC.DRTC3 = _DMAC3_TRANSFER_COUNT;
}
/***********************************************************************************************************************
* Function Name: R_DMAC01_Start
* Description  : This function enable DMAC01 activation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC03_Start(void)
{
    /* Clear DMAC00 complete interrupt (INTDMA0) request and enable operation */
    INTC2.ICDMA3.BIT.RFDMA3 = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICDMA3.BIT.MKDMA3 = _INT_PROCESSING_ENABLED;
    /* Enable DMAC00 operation */
    DMAC.DCSTC3 |= 0x91;
    DMAC.DTFRRQC3 = 0x01;
    DMAC.DCEN3 = _DMAC_CHANNEL_OPERATION_ENABLED;
}
/***********************************************************************************************************************
* Function Name: R_DMAC01_Stop
* Description  : This function disable DMAC01 activation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC03_Stop(void)
{
    /* Disable DMAC01 operation */
    DMAC.DCEN3 = _DMAC_CHANNEL_OPERATION_DISABLED;
    /* Disable DMAC01 operation and clear request */
    INTC2.ICDMA3.BIT.MKDMA3 = _INT_PROCESSING_DISABLED;
    INTC2.ICDMA3.BIT.RFDMA3 = _INT_REQUEST_NOT_OCCUR;
    /* Synchronization processing */
    g_cg_sync_read = DMAC.DCEN3;
}
/***********************************************************************************************************************
* Function Name: R_DMAC01_Set_SoftwareTrigger
* Description  : This function set DMAC01 software trigger.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC03_Set_SoftwareTrigger(void)
{
    /* Set DMAC01 software trigger */
    DMAC.DCSTS3 = _DMAC_SET_REQUEST_FLAG;
}
/***********************************************************************************************************************
* Function Name: R_DMAC01_Suspend
* Description  : This function Suspends DMAC01 channel operation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC03_Suspend(void)
{
    /* Enable DMAC01 operation */
    DMAC.DCEN3 = _DMAC_CHANNEL_OPERATION_DISABLED;
}
/***********************************************************************************************************************
* Function Name: R_DMAC01_Resume
* Description  : This function Resumes DMAC01 channel operation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC03_Resume(void)
{
    /* Disable DMAC01 operation */ 
    DMAC.DCEN3 = _DMAC_CHANNEL_OPERATION_ENABLED;
}

/***********************************************************************************************************************
* Function Name: R_DMAC04_Create
* Description  : This function initializes DMAC04 channel.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC04_Create(void)
{
    /* Disable DMAC04 operation */
    DMAC.DCEN4 = _DMAC_CHANNEL_OPERATION_DISABLED;
    /* Disable DMAC04 operation and clear request */
    INTC2.ICDMA4.BIT.MKDMA4 = _INT_PROCESSING_DISABLED;
    INTC2.ICDMA4.BIT.RFDMA4 = _INT_REQUEST_NOT_OCCUR;
    /* Set DMAC04 interrupt setting */
    INTC2.ICDMA4.BIT.TBDMA4 = _INT_TABLE_VECTOR;
    INTC2.ICDMA4.UINT16 &= _INT_PRIORITY_LOWEST;
    /* Set DMAC04 global setting */
    DMAC.DM04CM = _DMAC_PE1_SETTING | _DMAC_SPID1_SETTING | _DMAC_SUPERVISON_MODE;
    /* Set DMAC04 transfer setting */
    DMAC.DSA4 = _DMAC4_SOURCE_ADDRESS;
    DMAC.DDA4 = _DMAC4_DESTINATION_ADDRESS;
    DMAC.DTC4 = _DMAC4_ADDRESS_RELOAD_COUNT | _DMAC4_TRANSFER_COUNT;
    DMAC.DTCT4 = _DMAC_CYCLES_EXECUTED | _DMAC_TRANSFER_HARDWARE | _DMAC_CHAIN_DISABLE | 
                 _DMAC_TRANSFER_COMPLETION_INTERRUPT_ENABLE | _DMAC_CONTINUOUS_TRANSFER_DISABLE | 
                 _DMAC_RELOAD2_DISABLE | _DMAC_RELOAD1_ALL_CONTROL | _DMAC_DESTINATION_FIXED | 
                 _DMAC_SOURCE_INCREMENT | _DMAC_TRANSFER_DATA_8BITS | _DMAC_SINGLE_TRANSFER;
    DMAC.DTFR4 = _DMAC_TRIGGER_SOURCE_70 | _DMAC_HARDWARE_TRIGGER_ENABLE;
}
/***********************************************************************************************************************
* Function Name: R_DMAC04_Start
* Description  : This function enable DMAC04 activation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC04_Start(void)
{
    /* Clear DMAC04 request and enable operation */
    INTC2.ICDMA4.BIT.RFDMA4 = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICDMA4.BIT.MKDMA4 = _INT_PROCESSING_ENABLED;    
    /* Enable DMAC04 operation */
    DMAC.DCSTC4 |= 0x91;
    DMAC.DTFRRQC4 = 0x01;
    DMAC.DCEN4 = _DMAC_CHANNEL_OPERATION_ENABLED;
}
/***********************************************************************************************************************
* Function Name: R_DMAC04_Stop
* Description  : This function disable DMAC04 activation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC04_Stop(void)
{
    /* Disable DMAC04 operation */
    DMAC.DCEN4 = _DMAC_CHANNEL_OPERATION_DISABLED;
    /* Disable DMAC04 operation and clear request */
    INTC2.ICDMA4.BIT.MKDMA4 = _INT_PROCESSING_DISABLED;
    INTC2.ICDMA4.BIT.RFDMA4 = _INT_REQUEST_NOT_OCCUR;
}
/***********************************************************************************************************************
* Function Name: R_DMAC04_Set_SoftwareTrigger
* Description  : This function set DMAC04 software trigger.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC04_Set_SoftwareTrigger(void)
{
    /* Set DMAC04 software trigger */
    DMAC.DCSTS4 = _DMAC_SET_REQUEST_FLAG;
}
/***********************************************************************************************************************
* Function Name: R_DMAC04_Suspend
* Description  : This function Suspends DMAC04 channel operation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC04_Suspend(void)
{
    /* Enable DMAC04 operation */
    DMAC.DCEN4 = _DMAC_CHANNEL_OPERATION_DISABLED;
}
/***********************************************************************************************************************
* Function Name: R_DMAC04_Resume
* Description  : This function Resumes DMAC04 channel operation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC04_Resume(void)
{
    /* Disable DMAC04 operation */ 
    DMAC.DCEN4 = _DMAC_CHANNEL_OPERATION_ENABLED;
}
/***********************************************************************************************************************
* Function Name: R_DMAC05_Create
* Description  : This function initializes DMAC05 channel.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC05_Create(void)
{
    /* Disable DMAC05 operation */
    DMAC.DCEN5 = _DMAC_CHANNEL_OPERATION_DISABLED;
    /* Disable DMAC05 operation and clear request */
    INTC2.ICDMA5.BIT.MKDMA5 = _INT_PROCESSING_DISABLED;
    INTC2.ICDMA5.BIT.RFDMA5 = _INT_REQUEST_NOT_OCCUR;
    /* Set DMAC05 interrupt setting */
    INTC2.ICDMA5.BIT.TBDMA5 = _INT_TABLE_VECTOR;
    INTC2.ICDMA5.UINT16 &= _INT_PRIORITY_LOWEST;
    /* Set DMAC05 global setting */
    DMAC.DM05CM = _DMAC_PE1_SETTING | _DMAC_SPID1_SETTING | _DMAC_SUPERVISON_MODE;
    /* Set DMAC05 transfer setting */
    DMAC.DSA5 = _DMAC5_SOURCE_ADDRESS;
    DMAC.DDA5 = _DMAC5_DESTINATION_ADDRESS;
    DMAC.DTC5 = _DMAC5_ADDRESS_RELOAD_COUNT | _DMAC5_TRANSFER_COUNT;
    DMAC.DTCT5 = _DMAC_CYCLES_EXECUTED | _DMAC_TRANSFER_HARDWARE | _DMAC_CHAIN_DISABLE | 
                 _DMAC_TRANSFER_COMPLETION_INTERRUPT_ENABLE | _DMAC_CONTINUOUS_TRANSFER_DISABLE | 
                 _DMAC_RELOAD2_DISABLE | _DMAC_RELOAD1_ALL_CONTROL | _DMAC_DESTINATION_INCREMENT | 
                 _DMAC_SOURCE_FIXED | _DMAC_TRANSFER_DATA_8BITS | _DMAC_SINGLE_TRANSFER;
    DMAC.DTFR5 = _DMAC_TRIGGER_SOURCE_71 | _DMAC_HARDWARE_TRIGGER_ENABLE;
}
/***********************************************************************************************************************
* Function Name: R_DMAC05_Start
* Description  : This function enable DMAC05 activation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC05_Start(void)
{
    /* Clear DMAC05 request and enable operation */
    INTC2.ICDMA5.BIT.RFDMA5 = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICDMA5.BIT.MKDMA5 = _INT_PROCESSING_ENABLED;    
    /* Enable DMAC05 operation */
    DMAC.DCSTC5 |= 0x91;
    DMAC.DTFRRQC5 = 0x01;
    DMAC.DCEN5 = _DMAC_CHANNEL_OPERATION_ENABLED;
}
/***********************************************************************************************************************
* Function Name: R_DMAC05_Stop
* Description  : This function disable DMAC05 activation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC05_Stop(void)
{
    /* Disable DMAC05 operation */
    DMAC.DCEN5 = _DMAC_CHANNEL_OPERATION_DISABLED;
    /* Disable DMAC05 operation and clear request */
    INTC2.ICDMA5.BIT.MKDMA5 = _INT_PROCESSING_DISABLED;
    INTC2.ICDMA5.BIT.RFDMA5 = _INT_REQUEST_NOT_OCCUR;
}
/***********************************************************************************************************************
* Function Name: R_DMAC05_Set_SoftwareTrigger
* Description  : This function set DMAC05 software trigger.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC05_Set_SoftwareTrigger(void)
{
    /* Set DMAC05 software trigger */
    DMAC.DCSTS5 = _DMAC_SET_REQUEST_FLAG;
}
/***********************************************************************************************************************
* Function Name: R_DMAC05_Suspend
* Description  : This function Suspends DMAC05 channel operation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC05_Suspend(void)
{
    /* Enable DMAC05 operation */
    DMAC.DCEN5 = _DMAC_CHANNEL_OPERATION_DISABLED;
}
/***********************************************************************************************************************
* Function Name: R_DMAC05_Resume
* Description  : This function Resumes DMAC05 channel operation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_DMAC05_Resume(void)
{
    /* Disable DMAC05 operation */ 
    DMAC.DCEN5 = _DMAC_CHANNEL_OPERATION_ENABLED;
}

void SBC_SetDmaTransmitFlg(void)
{
    SBC_DmaTransmitFinishFlg = 1;
}

/******************************************************************************/
/**<pre>
 *函数名称: CSIH_DRV_MasterSendReceive
 *功能描述: CSIH DMA 发送接口
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
uint16 CSIH_DRV_MasterSendReceive(const uint8 *txDataBuf, uint8 *rxDataBuf, uint16 length, CSIHMaterCfg numMaster)
{
    uint32 regValue = _CSIH_TRANSMIT_SETTING_INIT;

    if(numMaster == CSIH0_MASTER)
    {
		if(length > 1)
		{
	        DMAC.DSA4 = ((uint32)(&txDataBuf[1]));
	        DMAC.DDA4 = ((uint32)(&(CSIH0.TX0H)));
	        DMAC.DTC4 = _DMAC4_ADDRESS_RELOAD_COUNT | (length-1);
		}
        /* RX DMA*/
        /* Set DMAC05 transfer setting */
        DMAC.DSA5 = ((uint32)(&(CSIH0.RX0H)));
        DMAC.DDA5 = ((uint32)(rxDataBuf));
        DMAC.DTC5 = _DMAC5_ADDRESS_RELOAD_COUNT | length;
        /* CSIH master dma recrive */
        R_DMAC05_Start();
        /* CSIH master dma send */
		if(length > 1)
		{
        	R_DMAC04_Start();
		}
        regValue &= ~(_CSIH_SELECT_CHIP_0);
        regValue |= txDataBuf[0];
        CSIH0.TX0W = regValue;

        /* 等待传输完成 */
        SBC_DmaTransmitFinishFlg = 0;
        while(!SBC_DmaTransmitFinishFlg)
        {
            ;
        }
    }
    else if(numMaster == CSIH2_MASTER)
    {
		if(length > 1)
		{
	        DMAC.DSA0 = ((uint32)(&txDataBuf[1]));
	        DMAC.DDA0 = ((uint32)(&(CSIH2.TX0H)));
	        DMAC.DTC0 = _DMAC0_ADDRESS_RELOAD_COUNT | (length-1);
		}
        /* RX DMA*/
        /* Set DMAC02 transfer setting */
        DMAC.DSA1 = ((uint32)(&(CSIH2.RX0H)));
        DMAC.DDA1 = ((uint32)(rxDataBuf));
        DMAC.DTC1 = _DMAC1_ADDRESS_RELOAD_COUNT | length;
        /* CSIH master dma recrive */
        R_DMAC01_Start();
        /* CSIH master dma send */
		if(length > 1)
		{
        	R_DMAC00_Start();
		}
        regValue &= ~(_CSIH_SELECT_CHIP_0);
        regValue |= txDataBuf[0];
        CSIH2.TX0W = regValue;
    }
    else if(numMaster == CSIH3_MASTER)
    {
		if(length > 1)
		{
	        DMAC.DSA2 = ((uint32)(&txDataBuf[1]));
	        DMAC.DDA2 = ((uint32)(&(CSIH3.TX0H)));
	        DMAC.DTC2 = _DMAC2_ADDRESS_RELOAD_COUNT | (length-1);
		}
        /* RX DMA*/
        /* Set DMAC02 transfer setting */
        DMAC.DSA3 = ((uint32)(&(CSIH3.RX0H)));
        DMAC.DDA3 = ((uint32)(rxDataBuf));
        DMAC.DTC3 = _DMAC3_ADDRESS_RELOAD_COUNT | length;
        /* CSIH master dma recrive */
        R_DMAC03_Start();
        /* CSIH master dma send */
		if(length > 1)
		{
        	R_DMAC02_Start();
		}

         regValue &= ~(_CSIH_SELECT_CHIP_3);
         regValue |= txDataBuf[0];
         CSIH3.TX0W = regValue;
    }
    else
    {

    }

    return 0;
}

/******************************************************************************/
/**<pre>
 *函数名称: CSIH_DRV_MasterSendReceive
 *功能描述: CSIH DMA 发送接口
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
uint16 SBC_MasterSendReceive(const uint8 *txDataBuf, uint8 *rxDataBuf, uint16 length, uint16 timeout)
{
    uint16 status = MD_OK;
    uint32 regValue = _CSIH_TRANSMIT_SETTING_INIT;
    static uint16 timeout_init, timeout_cur = 0;

    if(length > 1)
    {
        DMAC.DSA4 = ((uint32)(&txDataBuf[1]));
        DMAC.DDA4 = ((uint32)(&(CSIH0.TX0H)));
        DMAC.DTC4 = _DMAC4_ADDRESS_RELOAD_COUNT | (length-1);
    }
    /* RX DMA*/
    /* Set DMAC05 transfer setting */
    DMAC.DSA5 = ((uint32)(&(CSIH0.RX0H)));
    DMAC.DDA5 = ((uint32)(rxDataBuf));
    DMAC.DTC5 = _DMAC5_ADDRESS_RELOAD_COUNT | length;
    /* CSIH master dma recrive */
    R_DMAC05_Start();
    /* CSIH master dma send */
    if(length > 1)
    {
        R_DMAC04_Start();
    }
    regValue &= ~(_CSIH_SELECT_CHIP_0);
    regValue |= txDataBuf[0];
    CSIH0.TX0W = regValue;

    /* 等待传输完成 */
    SBC_DmaTransmitFinishFlg = 0;
    timeout_init = GetSystemUseTimerMsCnt();
    while(!SBC_DmaTransmitFinishFlg)
    {
        timeout_cur = GetSystemUseTimerMsCnt();
        if((timeout_cur-timeout_init) > timeout)
        {
            return MD_OVERRUN;
        }
    }
    return status;
}

/* Start user code for adding. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */
