
/**********************************************************************
* Includes
***********************************************************************/
#include "PAS_MAP_StateHandle.h"
#include "SDW_Int.h"
#include "SnsEchoFilterAndSigGroup_int.h"
#include "MapBuild_Int.h"
#include "MapCoorCalculate_Int.h"
#include "SnsDiag.h"
#include "SnsPPCalculate_Int.h"
#include "PowerSingalManage.h"
#include "PAS_MAP_SignalManage.h"


/**********************************************************************
* Global objects 
***********************************************************************/
uint16 Gu16SideDis[4];

static PDCInfoFromCanType  GstrPDCInfoFromCan;
PDCToCAN_PASDataType 	GstrPDCToCAN_PASData;

uint8 Gu8StandbySystemFailure =0;

Boolean GblPASFalureFlg = FALSE;
static PDCSignal_ZoneDisType GstrPP_SDW_ZoneDis;

/*******************************************************************
***********************Private Function****************************
*********************************************************************/
static void PAS_GetCANSignal(void);
static void PAS_WorkLogic(void);

static void PAS_GetCANSignal(void)
{
    Signal_VoltageStatusType LenuWorkVolSts = NORMAL_VOLTAGE;

    ReadPwrManage_GroupStatus(PM_APP,&LenuWorkVolSts);
    /* 电压异常，强制设置电源状态为OFF，让系统进入到OFF状态 */
	if(LenuWorkVolSts == NORMAL_VOLTAGE)
	{
        GstrPDCToCAN_PASData.u8VoltageNormalFlag = 1;
    }
    else
    {
        GstrPDCToCAN_PASData.u8VoltageNormalFlag = 0;
    }
    
    ReadCAN_AppSignal_LowVolPwrMd(&GstrPDCToCAN_PASData.enuLowPowerMode);
    /* 理想OTA 6.5新增ACC状态不监控EPS的信号，不报EPS相关的DTC，在上ON电后功能正常，ACC状态下车辆静止，档位为P，仅使用Map和PP功能
       软件修改为ACC当ON使用，保证PAS状态逻辑的正常转换*/
    if(GstrPDCToCAN_PASData.enuLowPowerMode == POWER_ACC)
    {
        GstrPDCToCAN_PASData.enuLowPowerMode = POWER_ON;
    }
    
	ReadSysSignal_Car_SpeedVild(&GstrPDCInfoFromCan.u8VehicleValid);
	ReadCAN_AppSignal_Car_Speed(&GstrPDCInfoFromCan.u8VehicleSpeed);
	ReadCAN_AppSignal_ADAS_PAS_EnableSts(&GstrPDCToCAN_PASData.u8PAS_Enable);
	ReadCAN_AppSignal_ADAS_APAStatus(&GstrPDCToCAN_PASData.enuAPAWorkStatus);
}

PDCErrorFlagType PAS_GetErrorFlag(void)
{
	PDCErrorFlagType LstrRadarErrorFlag;
	uint8 FrontSystem_FailureFlag = 0;
	uint8 RearSystem_FailureFlag = 0;
	uint8 Front_and_RearSystem_FailureFlag = 0;
    uint8 Lu8SnsBeCoveredFlag;
    uint8 i;
#if 1
    FrontSystem_FailureFlag = SnsDiag_ReadFrontSnsGroupErrSts();
    RearSystem_FailureFlag = SnsDiag_ReadRearSnsGroupErrSts();
#endif

    /* 添加车衣检测的故障至PAS逻辑，同步做功能降级 */
    if(FrontSystem_FailureFlag == 0)
    {
        for(i = SNS_INX_01; i < SNS_INX_07; i++)
        {
            PdcSignal_ReadSnsBeCoveredFlagToDTC(&Lu8SnsBeCoveredFlag,(PdcSiagnal_SnsInxType)i);
            if(Lu8SnsBeCoveredFlag)
            {
                FrontSystem_FailureFlag = 1;
                break;
            }
        }
    }

    if(RearSystem_FailureFlag == 0)
    {
        for(i = SNS_INX_07; i < SNS_INX_NUM; i++)
        {
            PdcSignal_ReadSnsBeCoveredFlagToDTC(&Lu8SnsBeCoveredFlag,(PdcSiagnal_SnsInxType)i);
            if(Lu8SnsBeCoveredFlag)
            {
                RearSystem_FailureFlag = 1;
                break;
            }
        }
    }
    
	if((FrontSystem_FailureFlag == 1) && (RearSystem_FailureFlag == 1))
	{
		Front_and_RearSystem_FailureFlag = 1;
	}
	else
	{
		Front_and_RearSystem_FailureFlag = 0;
	}
    
	LstrRadarErrorFlag.u8FrontSystem_FailureFlag=FrontSystem_FailureFlag;
	LstrRadarErrorFlag.u8RearSystem_FailureFlag=RearSystem_FailureFlag;
	LstrRadarErrorFlag.u8Front_and_RearSystem_FailureFlag=Front_and_RearSystem_FailureFlag;

	return LstrRadarErrorFlag;
}

PDCErrorFlagType PAS_GetErrorFlagForTask(void)
{
	PDCErrorFlagType LstrRadarErrorFlag;
	uint8 FrontSystem_FailureFlag = 0;
	uint8 RearSystem_FailureFlag = 0;
	uint8 Front_and_RearSystem_FailureFlag = 0;

#if 1
    FrontSystem_FailureFlag = SnsDiag_ReadFrontSnsGroupErrSts();
    RearSystem_FailureFlag = SnsDiag_ReadRearSnsGroupErrSts();
#endif

	if((FrontSystem_FailureFlag == 1) && (RearSystem_FailureFlag == 1))
	{
		Front_and_RearSystem_FailureFlag = 1;
	}
	else
	{
		Front_and_RearSystem_FailureFlag = 0;
	}
    
	LstrRadarErrorFlag.u8FrontSystem_FailureFlag=FrontSystem_FailureFlag;
	LstrRadarErrorFlag.u8RearSystem_FailureFlag=RearSystem_FailureFlag;
	LstrRadarErrorFlag.u8Front_and_RearSystem_FailureFlag=Front_and_RearSystem_FailureFlag;

	return LstrRadarErrorFlag;
}




static void PAS_WorkLogic(void)
{
	uint8 FrontSystem_FailureFlag;
	uint8 RearSystem_FailureFlag;
	uint8 Front_and_RearSystem_FailureFlag;
	static uint8 Lu8SpeedOver15Flag;
	PDCErrorFlagType GstrRadarErrorFlag;
	
	GstrRadarErrorFlag =PAS_GetErrorFlag();
	FrontSystem_FailureFlag=GstrRadarErrorFlag.u8FrontSystem_FailureFlag;
	RearSystem_FailureFlag=GstrRadarErrorFlag.u8RearSystem_FailureFlag;
	Front_and_RearSystem_FailureFlag=GstrRadarErrorFlag.u8Front_and_RearSystem_FailureFlag;
    /* 暂时屏蔽 */
#if 0
    GblPASFalureFlg = 0;
#else
	GblPASFalureFlg =Read_PASFunctionDGFlag();
#endif
	switch(GstrPDCToCAN_PASData.enuPAS_WorkStatus)
	{
		case Work_off:
		{
			if(GstrPDCToCAN_PASData.enuLowPowerMode == POWER_ON)
			{
				GstrPDCToCAN_PASData.enuPAS_WorkStatus = Work_Standby;
			}
			break;
		}
		case Work_Standby:
		{
            #if 1
            Gu8StandbySystemFailure=0;
            if(GstrPDCToCAN_PASData.enuLowPowerMode    != POWER_ON)
			{
				GstrPDCToCAN_PASData.enuPAS_WorkStatus = Work_off;
			}
			else if((Front_and_RearSystem_FailureFlag) ||(GblPASFalureFlg ==TRUE))
			{
                 
                 /*降级标志清零*/
                 GstrPDCToCAN_PASData.enuPAS_WorkStatus = Work_SystemFailure;  
                 
			}
            #endif
			if(Lu8SpeedOver15Flag == 1)
			{
				 if(GstrPDCToCAN_PASData.enuAPAWorkStatus == APA_GUIDANCE \
                    ||((GstrPDCInfoFromCan.u8VehicleValid ==Vehicle_Valid) && (GstrPDCInfoFromCan.u8VehicleSpeed <=  PAS_HIGH_TO_LOW_ACTIVE_SPD)
				    &&(GstrPDCToCAN_PASData.u8PAS_Enable == PAS_ENABLE)))
				 {
					GstrPDCToCAN_PASData.enuPAS_WorkStatus = Work_Front_And_Rear_Active;
					Lu8SpeedOver15Flag = 0;
				 }
                 /*增加条件不成立，检测探头是否有故障*/
                 else
                 {
                    if((FrontSystem_FailureFlag) || (RearSystem_FailureFlag)||(GblPASFalureFlg ==TRUE))
        		    {
                        /*降级标志清零*/
                        Gu8StandbySystemFailure=1;
                        GstrPDCToCAN_PASData.enuPAS_WorkStatus = Work_SystemFailure;  
        			}
                 }
                   
			}
			else
			{
				if(GstrPDCToCAN_PASData.enuAPAWorkStatus == APA_GUIDANCE \
                    ||((GstrPDCInfoFromCan.u8VehicleValid == Vehicle_Valid) && (GstrPDCInfoFromCan.u8VehicleSpeed <=  PAS_LOW_TO_HIGH_CLOSE_SPD)
				 &&(GstrPDCToCAN_PASData.u8PAS_Enable == PAS_ENABLE)))
				{
					GstrPDCToCAN_PASData.enuPAS_WorkStatus = Work_Front_And_Rear_Active;
				}
                 /*增加条件不成立，检测探头是否有故障*/
                else
                {
                    if((FrontSystem_FailureFlag) || (RearSystem_FailureFlag)||(GblPASFalureFlg ==TRUE))
        		    {

                        /*降级标志清零*/
                        Gu8StandbySystemFailure=1;
                        GstrPDCToCAN_PASData.enuPAS_WorkStatus = Work_SystemFailure;  
     
        			}

                }
			}


			break;
		}
		case Work_Front_And_Rear_Active:
		{
			if(GstrPDCToCAN_PASData.enuAPAWorkStatus != APA_GUIDANCE \
                &&((GstrPDCInfoFromCan.u8VehicleValid == Vehicle_Valid && GstrPDCInfoFromCan.u8VehicleSpeed > PAS_LOW_TO_HIGH_CLOSE_SPD)
			   ||(GstrPDCToCAN_PASData.u8PAS_Enable == PAS_DISABLE)))
			{
				GstrPDCToCAN_PASData.enuPAS_WorkStatus = Work_Standby;
                if(GstrPDCInfoFromCan.u8VehicleSpeed > PAS_LOW_TO_HIGH_CLOSE_SPD)
                {
                    Lu8SpeedOver15Flag = 1;
                }

			}
			if(GstrPDCToCAN_PASData.enuLowPowerMode    != POWER_ON)
			{
				GstrPDCToCAN_PASData.enuPAS_WorkStatus = Work_off;
			}
			else if(Front_and_RearSystem_FailureFlag == 1 ||(GblPASFalureFlg ==TRUE))
			{
                /*降级标志清零*/
                GstrPDCToCAN_PASData.enuPAS_WorkStatus = Work_SystemFailure;  
			}
			else if(FrontSystem_FailureFlag == 1)
			{
				GstrPDCToCAN_PASData.enuPAS_WorkStatus = Work_FrontFailure_and_RearActive;
			}
			else if(RearSystem_FailureFlag == 1)
			{
				GstrPDCToCAN_PASData.enuPAS_WorkStatus = Work_FrontActive_and_RearFailure;
			}
			break;
		}
		case Work_FrontActive_and_RearFailure:
		{
			if(RearSystem_FailureFlag == 0)
			{
				GstrPDCToCAN_PASData.enuPAS_WorkStatus = Work_Front_And_Rear_Active;
			}
			if(GstrPDCToCAN_PASData.enuLowPowerMode    != POWER_ON)
			{
				GstrPDCToCAN_PASData.enuPAS_WorkStatus = Work_off;
			}
			else if(Front_and_RearSystem_FailureFlag == 1 || (GblPASFalureFlg ==TRUE))
			{
                /*降级标志清零*/
                GstrPDCToCAN_PASData.enuPAS_WorkStatus = Work_SystemFailure;  

			}
			break;
		}
		case Work_FrontFailure_and_RearActive:
		{
			if(FrontSystem_FailureFlag == 0)
			{
				GstrPDCToCAN_PASData.enuPAS_WorkStatus = Work_Front_And_Rear_Active;
			}
			if(GstrPDCToCAN_PASData.enuLowPowerMode    != POWER_ON)
			{
				GstrPDCToCAN_PASData.enuPAS_WorkStatus = Work_off;
			}
			else if(Front_and_RearSystem_FailureFlag == 1 || GblPASFalureFlg ==TRUE)
			{
                /*降级标志清零*/
                GstrPDCToCAN_PASData.enuPAS_WorkStatus = Work_SystemFailure;  

			}
			break;
		}
		case Work_SystemFailure:
		{
            
            if(FrontSystem_FailureFlag==1)
			{
				if (RearSystem_FailureFlag==0)
				{
                    if (Gu8StandbySystemFailure==0)
                    {
                        
                        if(GblPASFalureFlg ==FALSE)
                        {
                            GstrPDCToCAN_PASData.enuPAS_WorkStatus = Work_FrontFailure_and_RearActive;
                        }
                        
                    }
                }
			    
			}else if (FrontSystem_FailureFlag==0) 
			{
                if(RearSystem_FailureFlag==1)
                {
                    if (Gu8StandbySystemFailure==0)
                    {
                        if(GblPASFalureFlg ==FALSE)
                        {
                            GstrPDCToCAN_PASData.enuPAS_WorkStatus = Work_FrontActive_and_RearFailure;
                        }

                    }
                }
                else if(RearSystem_FailureFlag==0)
                {
                    if(GblPASFalureFlg ==FALSE)
                    {
                        GstrPDCToCAN_PASData.enuPAS_WorkStatus = Work_Standby;
                    }

                }

            }
            
			if(GstrPDCToCAN_PASData.enuLowPowerMode    != POWER_ON)
			{
				GstrPDCToCAN_PASData.enuPAS_WorkStatus = Work_off;
			}
            
            
			break;
		}
		default:
		{
			break;
		}
	}
    /* 车型配置 */
#if 0
	if(Gu8APPVEH_Type ==0xff)
	{
	    GstrPDCToCAN_PASData.enuPAS_WorkStatus = Work_SystemFailure;
	}
#endif

}

void UpdateFrontPP_Dis(void)
#if 1
{
    uint16 Lu16DisTemp;
    PPDis_ZONETye * LpPPZoneInfo;
    LpPPZoneInfo = &GstrPPZone[PDC_SNS_GROUP_FRONT];
    /* 前保PP */
    GstrPP_SDW_ZoneDis.FL1_Distance = LpPPZoneInfo->u16L1_Distance;
    GstrPP_SDW_ZoneDis.FL2_Distance = LpPPZoneInfo->u16L2_Distance;

    GstrPP_SDW_ZoneDis.FML1_Distance = LpPPZoneInfo->u16ML1_Distance;
    GstrPP_SDW_ZoneDis.FML2_Distance = LpPPZoneInfo->u16ML2_Distance;

    GstrPP_SDW_ZoneDis.FMR1_Distance = LpPPZoneInfo->u16MR1_Distance;
    GstrPP_SDW_ZoneDis.FMR2_Distance = LpPPZoneInfo->u16MR2_Distance;

    GstrPP_SDW_ZoneDis.FR1_Distance = LpPPZoneInfo->u16R1_Distance;
    GstrPP_SDW_ZoneDis.FR2_Distance = LpPPZoneInfo->u16R2_Distance;
}
#else
{
    uint16 Lu16DisTemp;

    /* 前保PP */
    if(GstrF_R_PDC_AreaObj[FRONT_GROUP].u16MinDis[0] <= MAX_PP_DIS_MM)
    {
        Lu16DisTemp = GstrF_R_PDC_AreaObj[FRONT_GROUP].u16MinDis[0]/10;
        GstrPP_SDW_ZoneDis.FL1_Distance = Lu16DisTemp;
        GstrPP_SDW_ZoneDis.FL2_Distance = Lu16DisTemp;
    }
    else
    {
        GstrPP_SDW_ZoneDis.FL1_Distance = INIT_PP_DIS;
        GstrPP_SDW_ZoneDis.FL2_Distance = INIT_PP_DIS;
    }

    if(GstrF_R_PDC_AreaObj[FRONT_GROUP].u16MinDis[1] <= MAX_PP_DIS_MM)
    {
        Lu16DisTemp = GstrF_R_PDC_AreaObj[FRONT_GROUP].u16MinDis[1]/10;
        GstrPP_SDW_ZoneDis.FML1_Distance = Lu16DisTemp;
        GstrPP_SDW_ZoneDis.FML2_Distance = Lu16DisTemp;
    }
    else
    {
        GstrPP_SDW_ZoneDis.FML1_Distance = INIT_PP_DIS;
        GstrPP_SDW_ZoneDis.FML2_Distance = INIT_PP_DIS;
    }

    if(GstrF_R_PDC_AreaObj[FRONT_GROUP].u16MinDis[2] <= MAX_PP_DIS_MM)
    {
        Lu16DisTemp = GstrF_R_PDC_AreaObj[FRONT_GROUP].u16MinDis[2]/10;
        GstrPP_SDW_ZoneDis.FMR1_Distance = Lu16DisTemp;
        GstrPP_SDW_ZoneDis.FMR2_Distance = Lu16DisTemp;
    }
    else
    {
        GstrPP_SDW_ZoneDis.FMR1_Distance = INIT_PP_DIS;
        GstrPP_SDW_ZoneDis.FMR2_Distance = INIT_PP_DIS;
    }
    
    if(GstrF_R_PDC_AreaObj[FRONT_GROUP].u16MinDis[3] <= MAX_PP_DIS_MM)
    {
        Lu16DisTemp = GstrF_R_PDC_AreaObj[FRONT_GROUP].u16MinDis[3]/10;
        GstrPP_SDW_ZoneDis.FR1_Distance = Lu16DisTemp;
        GstrPP_SDW_ZoneDis.FR2_Distance = Lu16DisTemp;
    }
    else
    {
        GstrPP_SDW_ZoneDis.FR1_Distance = INIT_PP_DIS;
        GstrPP_SDW_ZoneDis.FR2_Distance = INIT_PP_DIS;
    }
}
#endif


void UpdateRearPP_Dis(void)
#if 1
{
    PPDis_ZONETye * LpPPZoneInfo;
    LpPPZoneInfo = &GstrPPZone[PDC_SNS_GROUP_REAR];
    /* 后保PP */
    GstrPP_SDW_ZoneDis.RL1_Distance = LpPPZoneInfo->u16L1_Distance;
    GstrPP_SDW_ZoneDis.RL2_Distance = LpPPZoneInfo->u16L2_Distance;

    GstrPP_SDW_ZoneDis.RML1_Distance = LpPPZoneInfo->u16ML1_Distance;
    GstrPP_SDW_ZoneDis.RML2_Distance = LpPPZoneInfo->u16ML2_Distance;

    GstrPP_SDW_ZoneDis.RMR1_Distance = LpPPZoneInfo->u16MR1_Distance;
    GstrPP_SDW_ZoneDis.RMR2_Distance = LpPPZoneInfo->u16MR2_Distance;

    GstrPP_SDW_ZoneDis.RR1_Distance = LpPPZoneInfo->u16R1_Distance;
    GstrPP_SDW_ZoneDis.RR2_Distance = LpPPZoneInfo->u16R2_Distance;
}
#else
{
    uint16 Lu16DisTemp;
    /* 后保PP */
    if(GstrF_R_PDC_AreaObj[REAR_GROUP].u16MinDis[0] <= MAX_PP_DIS_MM)
    {
        Lu16DisTemp = GstrF_R_PDC_AreaObj[REAR_GROUP].u16MinDis[0]/10;
        GstrPP_SDW_ZoneDis.RL1_Distance = Lu16DisTemp;
        GstrPP_SDW_ZoneDis.RL2_Distance = Lu16DisTemp;
    }
    else
    {
        GstrPP_SDW_ZoneDis.RL1_Distance = INIT_PP_DIS;
        GstrPP_SDW_ZoneDis.RL2_Distance = INIT_PP_DIS;
    }

    if(GstrF_R_PDC_AreaObj[REAR_GROUP].u16MinDis[1] <= MAX_PP_DIS_MM)
    {
        Lu16DisTemp = GstrF_R_PDC_AreaObj[REAR_GROUP].u16MinDis[1]/10;
        GstrPP_SDW_ZoneDis.RML1_Distance = Lu16DisTemp;
        GstrPP_SDW_ZoneDis.RML2_Distance = Lu16DisTemp;
    }
    else
    {
        GstrPP_SDW_ZoneDis.RML1_Distance = INIT_PP_DIS;
        GstrPP_SDW_ZoneDis.RML2_Distance = INIT_PP_DIS;
    }

    if(GstrF_R_PDC_AreaObj[REAR_GROUP].u16MinDis[2] <= MAX_PP_DIS_MM)
    {
        Lu16DisTemp = GstrF_R_PDC_AreaObj[REAR_GROUP].u16MinDis[2]/10;
        GstrPP_SDW_ZoneDis.RMR1_Distance = Lu16DisTemp;
        GstrPP_SDW_ZoneDis.RMR2_Distance = Lu16DisTemp;
    }
    else
    {
        GstrPP_SDW_ZoneDis.RMR1_Distance = INIT_PP_DIS;
        GstrPP_SDW_ZoneDis.RMR2_Distance = INIT_PP_DIS;
    }
    
    if(GstrF_R_PDC_AreaObj[REAR_GROUP].u16MinDis[3] <= MAX_PP_DIS_MM)
    {
        Lu16DisTemp = GstrF_R_PDC_AreaObj[REAR_GROUP].u16MinDis[3]/10;
        GstrPP_SDW_ZoneDis.RR1_Distance = Lu16DisTemp;
        GstrPP_SDW_ZoneDis.RR2_Distance = Lu16DisTemp;
    }
    else
    {
        GstrPP_SDW_ZoneDis.RR1_Distance = INIT_PP_DIS;
        GstrPP_SDW_ZoneDis.RR2_Distance = INIT_PP_DIS;
    }
}
#endif


void UpdateSDW_Dis(void)
{
    uint16 Lu16DisTemp;
    
    /* 左侧边SDW */
    Lu16DisTemp = ReturnSDWSideVirtualDis(LEFT_SIDE_OBJ,SDW_AREA_1);
    if(Lu16DisTemp <= MAX_PP_DIS_MM)
    {
        GstrPP_SDW_ZoneDis.LSF1_Distance = Lu16DisTemp;
    }
    else
    {
        GstrPP_SDW_ZoneDis.LSF1_Distance = INIT_PP_DIS;
    }

    Lu16DisTemp = ReturnSDWSideVirtualDis(LEFT_SIDE_OBJ,SDW_AREA_2);
    if(Lu16DisTemp <= MAX_PP_DIS_MM)
    {
        GstrPP_SDW_ZoneDis.LSF2_Distance = Lu16DisTemp;
    }
    else
    {
        GstrPP_SDW_ZoneDis.LSF2_Distance = INIT_PP_DIS;
    }

    Lu16DisTemp = ReturnSDWSideVirtualDis(LEFT_SIDE_OBJ,SDW_AREA_3);
    if(Lu16DisTemp <= MAX_PP_DIS_MM)
    {
        GstrPP_SDW_ZoneDis.LSFM1_Distance = Lu16DisTemp;
    }
    else
    {
        GstrPP_SDW_ZoneDis.LSFM1_Distance = INIT_PP_DIS;
    }

    Lu16DisTemp = ReturnSDWSideVirtualDis(LEFT_SIDE_OBJ,SDW_AREA_4);
    if(Lu16DisTemp <= MAX_PP_DIS_MM)
    {
        GstrPP_SDW_ZoneDis.LSFM2_Distance = Lu16DisTemp;
    }
    else
    {
        GstrPP_SDW_ZoneDis.LSFM2_Distance = INIT_PP_DIS;
    }

    Lu16DisTemp = ReturnSDWSideVirtualDis(LEFT_SIDE_OBJ,SDW_AREA_5);
    if(Lu16DisTemp <= MAX_PP_DIS_MM)
    {
        GstrPP_SDW_ZoneDis.LSRM2_Distance = Lu16DisTemp;
    }
    else
    {
        GstrPP_SDW_ZoneDis.LSRM2_Distance = INIT_PP_DIS;
    }

    Lu16DisTemp = ReturnSDWSideVirtualDis(LEFT_SIDE_OBJ,SDW_AREA_6);
    if(Lu16DisTemp <= MAX_PP_DIS_MM)
    {
        GstrPP_SDW_ZoneDis.LSRM1_Distance = Lu16DisTemp;
    }
    else
    {
        GstrPP_SDW_ZoneDis.LSRM1_Distance = INIT_PP_DIS;
    }

    Lu16DisTemp = ReturnSDWSideVirtualDis(LEFT_SIDE_OBJ,SDW_AREA_7);
    if(Lu16DisTemp <= MAX_PP_DIS_MM)
    {
        GstrPP_SDW_ZoneDis.LSR2_Distance = Lu16DisTemp;
    }
    else
    {
        GstrPP_SDW_ZoneDis.LSR2_Distance = INIT_PP_DIS;
    }

    Lu16DisTemp = ReturnSDWSideVirtualDis(LEFT_SIDE_OBJ,SDW_AREA_8);
    if(Lu16DisTemp <= MAX_PP_DIS_MM)
    {
        GstrPP_SDW_ZoneDis.LSR1_Distance = Lu16DisTemp;
    }
    else
    {
        GstrPP_SDW_ZoneDis.LSR1_Distance = INIT_PP_DIS;
    }

    /* 右侧边SDW */
    Lu16DisTemp = ReturnSDWSideVirtualDis(RIGHT_SIDE_OBJ,SDW_AREA_1);
    if(Lu16DisTemp <= MAX_PP_DIS_MM)
    {
        GstrPP_SDW_ZoneDis.RSF1_Distance = Lu16DisTemp;
    }
    else
    {
        GstrPP_SDW_ZoneDis.RSF1_Distance = INIT_PP_DIS;
    }

    Lu16DisTemp = ReturnSDWSideVirtualDis(RIGHT_SIDE_OBJ,SDW_AREA_2);
    if(Lu16DisTemp <= MAX_PP_DIS_MM)
    {
        GstrPP_SDW_ZoneDis.RSF2_Distance = Lu16DisTemp;
    }
    else
    {
        GstrPP_SDW_ZoneDis.RSF2_Distance = INIT_PP_DIS;
    }

    Lu16DisTemp = ReturnSDWSideVirtualDis(RIGHT_SIDE_OBJ,SDW_AREA_3);
    if(Lu16DisTemp <= MAX_PP_DIS_MM)
    {
        GstrPP_SDW_ZoneDis.RSFM1_Distance = Lu16DisTemp;
    }
    else
    {
        GstrPP_SDW_ZoneDis.RSFM1_Distance = INIT_PP_DIS;
    }

    Lu16DisTemp = ReturnSDWSideVirtualDis(RIGHT_SIDE_OBJ,SDW_AREA_4);
    if(Lu16DisTemp <= MAX_PP_DIS_MM)
    {
        GstrPP_SDW_ZoneDis.RSFM2_Distance = Lu16DisTemp;
    }
    else
    {
        GstrPP_SDW_ZoneDis.RSFM2_Distance = INIT_PP_DIS;
    }

    Lu16DisTemp = ReturnSDWSideVirtualDis(RIGHT_SIDE_OBJ,SDW_AREA_5);
    if(Lu16DisTemp <= MAX_PP_DIS_MM)
    {
        GstrPP_SDW_ZoneDis.RSRM2_Distance = Lu16DisTemp;
    }
    else
    {
        GstrPP_SDW_ZoneDis.RSRM2_Distance = INIT_PP_DIS;
    }

    Lu16DisTemp = ReturnSDWSideVirtualDis(RIGHT_SIDE_OBJ,SDW_AREA_6);
    if(Lu16DisTemp <= MAX_PP_DIS_MM)
    {
        GstrPP_SDW_ZoneDis.RSRM1_Distance = Lu16DisTemp;
    }
    else
    {
        GstrPP_SDW_ZoneDis.RSRM1_Distance = INIT_PP_DIS;
    }

    Lu16DisTemp = ReturnSDWSideVirtualDis(RIGHT_SIDE_OBJ,SDW_AREA_7);
    if(Lu16DisTemp <= MAX_PP_DIS_MM)
    {
        GstrPP_SDW_ZoneDis.RSR2_Distance = Lu16DisTemp;
    }
    else
    {
        GstrPP_SDW_ZoneDis.RSR2_Distance = INIT_PP_DIS;
    }

    Lu16DisTemp = ReturnSDWSideVirtualDis(RIGHT_SIDE_OBJ,SDW_AREA_8);
    if(Lu16DisTemp <= MAX_PP_DIS_MM)
    {
        GstrPP_SDW_ZoneDis.RSR1_Distance = Lu16DisTemp;
    }
    else
    {
        GstrPP_SDW_ZoneDis.RSR1_Distance = INIT_PP_DIS;
    }
}

void UpdateFrontPP_InvalidDis(void)
{
    GstrPP_SDW_ZoneDis.FL1_Distance = INVALID_PP_DIS;
    GstrPP_SDW_ZoneDis.FL2_Distance = INVALID_PP_DIS;
    GstrPP_SDW_ZoneDis.FML1_Distance = INVALID_PP_DIS;
    GstrPP_SDW_ZoneDis.FML2_Distance = INVALID_PP_DIS;
    GstrPP_SDW_ZoneDis.FMR1_Distance = INVALID_PP_DIS;
    GstrPP_SDW_ZoneDis.FMR2_Distance = INVALID_PP_DIS;
    GstrPP_SDW_ZoneDis.FR1_Distance = INVALID_PP_DIS;
    GstrPP_SDW_ZoneDis.FR2_Distance = INVALID_PP_DIS;
}

void UpdateRearPP_InvalidDis(void)
{
    GstrPP_SDW_ZoneDis.RL1_Distance = INVALID_PP_DIS;
    GstrPP_SDW_ZoneDis.RL2_Distance = INVALID_PP_DIS;
    GstrPP_SDW_ZoneDis.RML1_Distance = INVALID_PP_DIS;
    GstrPP_SDW_ZoneDis.RML2_Distance = INVALID_PP_DIS;
    GstrPP_SDW_ZoneDis.RMR1_Distance = INVALID_PP_DIS;
    GstrPP_SDW_ZoneDis.RMR2_Distance = INVALID_PP_DIS;
    GstrPP_SDW_ZoneDis.RR1_Distance = INVALID_PP_DIS;
    GstrPP_SDW_ZoneDis.RR2_Distance = INVALID_PP_DIS;
}

void UpdateSDW_InvalidDis(void)
{
    GstrPP_SDW_ZoneDis.LSF1_Distance = INVALID_PP_DIS;
    GstrPP_SDW_ZoneDis.LSF2_Distance = INVALID_PP_DIS;
    GstrPP_SDW_ZoneDis.LSFM1_Distance = INVALID_PP_DIS;
    GstrPP_SDW_ZoneDis.LSFM2_Distance = INVALID_PP_DIS;
    GstrPP_SDW_ZoneDis.LSRM2_Distance = INVALID_PP_DIS;
    GstrPP_SDW_ZoneDis.LSRM1_Distance = INVALID_PP_DIS;
    GstrPP_SDW_ZoneDis.LSR2_Distance = INVALID_PP_DIS;
    GstrPP_SDW_ZoneDis.LSR1_Distance = INVALID_PP_DIS;
    
    GstrPP_SDW_ZoneDis.RSF1_Distance = INVALID_PP_DIS;
    GstrPP_SDW_ZoneDis.RSF2_Distance = INVALID_PP_DIS;
    GstrPP_SDW_ZoneDis.RSFM1_Distance = INVALID_PP_DIS;
    GstrPP_SDW_ZoneDis.RSFM2_Distance = INVALID_PP_DIS;
    GstrPP_SDW_ZoneDis.RSRM2_Distance = INVALID_PP_DIS;
    GstrPP_SDW_ZoneDis.RSRM1_Distance = INVALID_PP_DIS;
    GstrPP_SDW_ZoneDis.RSR2_Distance = INVALID_PP_DIS;
    GstrPP_SDW_ZoneDis.RSR1_Distance = INVALID_PP_DIS; 
}

/******************************************************************************
 * 函数名称: WritePPDisToCan
 * 
 * 功能描述: 写PP距离到CAN
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-10-19 20:04   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void WritePP_SDW_DisToCan(void)
{
	if((GstrPDCToCAN_PASData.enuPAS_WorkStatus == Work_off )
	||(GstrPDCToCAN_PASData.enuPAS_WorkStatus == Work_Standby)
	||(GstrPDCToCAN_PASData.enuPAS_WorkStatus == Work_SystemFailure)
	||(!GstrPDCToCAN_PASData.u8VoltageNormalFlag))
	{
        UpdateFrontPP_InvalidDis();
        UpdateRearPP_InvalidDis();
        UpdateSDW_InvalidDis();
	}
	else if(GstrPDCToCAN_PASData.enuPAS_WorkStatus == Work_FrontFailure_and_RearActive )
	{
        UpdateFrontPP_InvalidDis();
        UpdateSDW_InvalidDis();
        UpdateRearPP_Dis();
	}
	else if(GstrPDCToCAN_PASData.enuPAS_WorkStatus == Work_FrontActive_and_RearFailure)
	{
        UpdateFrontPP_Dis();
        UpdateRearPP_InvalidDis();
        UpdateSDW_InvalidDis();
	}
    else
    {
        UpdateFrontPP_Dis();
        UpdateRearPP_Dis();
        UpdateSDW_Dis();
    }

	PdcSignal_WriteZoneDistance(&GstrPP_SDW_ZoneDis);
}

/******************************************************************************
  * WriteDE_DistanceToCan
*******************************************************************************/
void WriteDE_DistanceToCan(void)
{
	PDCSignal_DEStructType LstrDE_FrontDistance;
	PDCSignal_DEStructType LstrDE_RearDistance;
	
	LstrDE_FrontDistance.u16PDCSignal_DE1 = GstrSnsDE_CE_Data[FRONT_GROUP][0].u16DE_Dis;
	LstrDE_FrontDistance.u16PDCSignal_DE2 = GstrSnsDE_CE_Data[FRONT_GROUP][1].u16DE_Dis;
	LstrDE_FrontDistance.u16PDCSignal_DE3 = GstrSnsDE_CE_Data[FRONT_GROUP][2].u16DE_Dis;
	LstrDE_FrontDistance.u16PDCSignal_DE4 = GstrSnsDE_CE_Data[FRONT_GROUP][3].u16DE_Dis;
	LstrDE_FrontDistance.u16PDCSignal_DE5 = GstrSnsDE_CE_Data[FRONT_GROUP][4].u16DE_Dis;
	LstrDE_FrontDistance.u16PDCSignal_DE6 = GstrSnsDE_CE_Data[FRONT_GROUP][5].u16DE_Dis;
	if((GstrPDCToCAN_PASData.enuPAS_WorkStatus == Work_off)
		||(GstrPDCToCAN_PASData.enuPAS_WorkStatus == Work_Standby)
		||(GstrPDCToCAN_PASData.enuPAS_WorkStatus == Work_SystemFailure)
		||(!GstrPDCToCAN_PASData.u8VoltageNormalFlag))
	{
        LstrDE_FrontDistance.u16PDCSignal_DE1 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrDE_FrontDistance.u16PDCSignal_DE2 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrDE_FrontDistance.u16PDCSignal_DE3 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrDE_FrontDistance.u16PDCSignal_DE4 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrDE_FrontDistance.u16PDCSignal_DE5 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrDE_FrontDistance.u16PDCSignal_DE6 = SNS_INIT_DE_CE_DIS_TO_CAN;	
	}
	else if(GstrPDCToCAN_PASData.enuPAS_WorkStatus == Work_FrontFailure_and_RearActive )
	{
        LstrDE_FrontDistance.u16PDCSignal_DE1 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrDE_FrontDistance.u16PDCSignal_DE2 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrDE_FrontDistance.u16PDCSignal_DE3 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrDE_FrontDistance.u16PDCSignal_DE4 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrDE_FrontDistance.u16PDCSignal_DE5 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrDE_FrontDistance.u16PDCSignal_DE6 = SNS_INIT_DE_CE_DIS_TO_CAN;	
	}
    
	PdcSignal_WriteUSS_DE_Distace(LstrDE_FrontDistance,FRONT_GROUP);

    /* 后雷达DE */
	LstrDE_RearDistance.u16PDCSignal_DE1 = GstrSnsDE_CE_Data[REAR_GROUP][0].u16DE_Dis;
	LstrDE_RearDistance.u16PDCSignal_DE2 = GstrSnsDE_CE_Data[REAR_GROUP][1].u16DE_Dis;
	LstrDE_RearDistance.u16PDCSignal_DE3 = GstrSnsDE_CE_Data[REAR_GROUP][2].u16DE_Dis;
	LstrDE_RearDistance.u16PDCSignal_DE4 = GstrSnsDE_CE_Data[REAR_GROUP][3].u16DE_Dis;
	LstrDE_RearDistance.u16PDCSignal_DE5 = GstrSnsDE_CE_Data[REAR_GROUP][4].u16DE_Dis;
	LstrDE_RearDistance.u16PDCSignal_DE6 = GstrSnsDE_CE_Data[REAR_GROUP][5].u16DE_Dis;
	
	if((GstrPDCToCAN_PASData.enuPAS_WorkStatus == Work_off )
		||(GstrPDCToCAN_PASData.enuPAS_WorkStatus == Work_Standby)
		||(GstrPDCToCAN_PASData.enuPAS_WorkStatus == Work_SystemFailure)
		||(!GstrPDCToCAN_PASData.u8VoltageNormalFlag))
	{	
        LstrDE_RearDistance.u16PDCSignal_DE1 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrDE_RearDistance.u16PDCSignal_DE2 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrDE_RearDistance.u16PDCSignal_DE3 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrDE_RearDistance.u16PDCSignal_DE4 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrDE_RearDistance.u16PDCSignal_DE5 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrDE_RearDistance.u16PDCSignal_DE6 = SNS_INIT_DE_CE_DIS_TO_CAN;	
	}
	else if(GstrPDCToCAN_PASData.enuPAS_WorkStatus == Work_FrontActive_and_RearFailure)
	{
        LstrDE_RearDistance.u16PDCSignal_DE1 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrDE_RearDistance.u16PDCSignal_DE2 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrDE_RearDistance.u16PDCSignal_DE3 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrDE_RearDistance.u16PDCSignal_DE4 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrDE_RearDistance.u16PDCSignal_DE5 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrDE_RearDistance.u16PDCSignal_DE6 = SNS_INIT_DE_CE_DIS_TO_CAN;	
	
	}
	PdcSignal_WriteUSS_DE_Distace(LstrDE_RearDistance,REAR_GROUP);
}

/******************************************************************************
  * WriteCE_DistanceToCan
*******************************************************************************/
void WriteCE_DistanceToCan(void)
{

	PDCSignal_CEStructType LstrCE_FrontDistance;
	PDCSignal_CEStructType LstrCE_RearDistance;

	LstrCE_FrontDistance.u16PDCSignal_CE1_2 = GstrSnsDE_CE_Data[FRONT_GROUP][0].u16Right_CE_Dis;
	LstrCE_FrontDistance.u16PDCSignal_CE2_3 = GstrSnsDE_CE_Data[FRONT_GROUP][1].u16Right_CE_Dis;
	LstrCE_FrontDistance.u16PDCSignal_CE3_4 = GstrSnsDE_CE_Data[FRONT_GROUP][2].u16Right_CE_Dis;
	LstrCE_FrontDistance.u16PDCSignal_CE4_5 = GstrSnsDE_CE_Data[FRONT_GROUP][3].u16Right_CE_Dis;
	LstrCE_FrontDistance.u16PDCSignal_CE5_6 = GstrSnsDE_CE_Data[FRONT_GROUP][4].u16Right_CE_Dis;

	if((GstrPDCToCAN_PASData.enuPAS_WorkStatus == Work_off )
		||(GstrPDCToCAN_PASData.enuPAS_WorkStatus == Work_Standby)
		||(GstrPDCToCAN_PASData.enuPAS_WorkStatus == Work_SystemFailure)
		||(!GstrPDCToCAN_PASData.u8VoltageNormalFlag))
	{
        LstrCE_FrontDistance.u16PDCSignal_CE1_2 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrCE_FrontDistance.u16PDCSignal_CE2_3 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrCE_FrontDistance.u16PDCSignal_CE3_4 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrCE_FrontDistance.u16PDCSignal_CE4_5 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrCE_FrontDistance.u16PDCSignal_CE5_6 = SNS_INIT_DE_CE_DIS_TO_CAN;		
	}
	else if(GstrPDCToCAN_PASData.enuPAS_WorkStatus == Work_FrontFailure_and_RearActive )
	{
        LstrCE_FrontDistance.u16PDCSignal_CE1_2 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrCE_FrontDistance.u16PDCSignal_CE2_3 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrCE_FrontDistance.u16PDCSignal_CE3_4 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrCE_FrontDistance.u16PDCSignal_CE4_5 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrCE_FrontDistance.u16PDCSignal_CE5_6 = SNS_INIT_DE_CE_DIS_TO_CAN;		
	}
	PdcSignal_WriteUSS_CE_Distace(LstrCE_FrontDistance,FRONT_GROUP);

    /* 后保CE */
	LstrCE_RearDistance.u16PDCSignal_CE1_2 = GstrSnsDE_CE_Data[REAR_GROUP][1].u16Left_CE_Dis;
	LstrCE_RearDistance.u16PDCSignal_CE2_3 = GstrSnsDE_CE_Data[REAR_GROUP][2].u16Left_CE_Dis;
	LstrCE_RearDistance.u16PDCSignal_CE3_4 = GstrSnsDE_CE_Data[REAR_GROUP][3].u16Left_CE_Dis;
	LstrCE_RearDistance.u16PDCSignal_CE4_5 = GstrSnsDE_CE_Data[REAR_GROUP][4].u16Left_CE_Dis;
	LstrCE_RearDistance.u16PDCSignal_CE5_6 = GstrSnsDE_CE_Data[REAR_GROUP][5].u16Left_CE_Dis;
	if((GstrPDCToCAN_PASData.enuPAS_WorkStatus == Work_off )
		||(GstrPDCToCAN_PASData.enuPAS_WorkStatus == Work_Standby)
		||(GstrPDCToCAN_PASData.enuPAS_WorkStatus == Work_SystemFailure)
		||(!GstrPDCToCAN_PASData.u8VoltageNormalFlag))
	{
        LstrCE_RearDistance.u16PDCSignal_CE1_2 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrCE_RearDistance.u16PDCSignal_CE2_3 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrCE_RearDistance.u16PDCSignal_CE3_4 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrCE_RearDistance.u16PDCSignal_CE4_5 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrCE_RearDistance.u16PDCSignal_CE5_6 = SNS_INIT_DE_CE_DIS_TO_CAN;			
	}
	else if(GstrPDCToCAN_PASData.enuPAS_WorkStatus == Work_FrontFailure_and_RearActive )
	{
        LstrCE_RearDistance.u16PDCSignal_CE1_2 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrCE_RearDistance.u16PDCSignal_CE2_3 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrCE_RearDistance.u16PDCSignal_CE3_4 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrCE_RearDistance.u16PDCSignal_CE4_5 = SNS_INIT_DE_CE_DIS_TO_CAN;
        LstrCE_RearDistance.u16PDCSignal_CE5_6 = SNS_INIT_DE_CE_DIS_TO_CAN;			
	}
	
	PdcSignal_WriteUSS_CE_Distace(LstrCE_RearDistance,REAR_GROUP);
}

/******************************************************************************
  * WriteMapObjToCan
*******************************************************************************/
void WriteMapObjToCan(void)
{
    uint8 i;
    PdcSignal_MapObjInfoType LstrMapObjInfo;
    
    uint32 Lu32ADAS_SYNC_Tim;
    ReadCAN_AppSignal_ADAS_CAN_SYNC_Time(&Lu32ADAS_SYNC_Tim);
	if((GstrPDCToCAN_PASData.enuPAS_WorkStatus == Work_off)
	||(GstrPDCToCAN_PASData.enuPAS_WorkStatus == Work_Standby)
	||(GstrPDCToCAN_PASData.enuPAS_WorkStatus == Work_SystemFailure)
	||(!GstrPDCToCAN_PASData.u8VoltageNormalFlag))
	{
        PdcSignal_MapObjInit();
    }
    else
    {
        for(i=0 ; i< MAP_OBJ_TO_CAN_MAX ;i++)
        {
            LstrMapObjInfo.s16PDCSignal_MapObjP1X = GstrObjMapToCAN[i].s16ObjMapP1_X;
            LstrMapObjInfo.s16PDCSignal_MapObjP1Y = GstrObjMapToCAN[i].s16ObjMapP1_Y;
            LstrMapObjInfo.s16PDCSignal_MapObjP2X = GstrObjMapToCAN[i].s16ObjMapP2_X;
            LstrMapObjInfo.s16PDCSignal_MapObjP2Y = GstrObjMapToCAN[i].s16ObjMapP2_Y;
            LstrMapObjInfo.u32PDCSignal_PAS_MapObjTimestamp = Lu32ADAS_SYNC_Tim;
            LstrMapObjInfo.u8PDCSignal_MapObjHeight = GstrObjMapToCAN[i].eMapObjHeight;
            LstrMapObjInfo.u8PDCSignal_MapObjHeightProb = GstrObjMapToCAN[i].eObjHeightProb;
            LstrMapObjInfo.u8PDCSignal_MapObjIndex = GstrObjMapToCAN[i].eMapObjId;
            LstrMapObjInfo.u8PDCSignal_MapObjProb = GstrObjMapToCAN[i].eObjExistProb;
            LstrMapObjInfo.u8PDCSignal_MapObjType = GstrObjMapToCAN[i].eMapObjType;
            PdcSignal_WriteMapObj(&LstrMapObjInfo,i);
        }
    }
}



void WritePointCloudToCan(void)
{
    uint8 i;
    uint8 unIndex =0;
    PdcPointCloud_ObjInfoType LstrPointCloudInfo;
    static uint8 u8SpeedOverFlag =0;
    uint8 u8StartPointCloudFlag=0;
    uint8 Lu8Group;
    uint8 Lu8SnsIndex;

    uint16 Lu16CarSpd;
    ReadCAN_AppSignal_Car_Speed(&Lu16CarSpd);

    if(Lu16CarSpd >= 2300)
    {
        u8SpeedOverFlag = VALID;
    } 

    if(u8SpeedOverFlag == VALID)
    {
        if(Lu16CarSpd <= 2000)
        {
            u8SpeedOverFlag = INVALID;
            u8StartPointCloudFlag = VALID;
        }
    }
    else
    {
        u8StartPointCloudFlag = VALID;
    }
    
    if(u8StartPointCloudFlag)
    {
        for (unIndex=0;unIndex<POINTCLOUD_MAX_SIZE;unIndex++)
        {
            if(unIndex < 6)
            {
                Lu8Group = 0;
                Lu8SnsIndex = unIndex;
            }
            else
            {
                Lu8Group = 1;
                Lu8SnsIndex = POINTCLOUD_MAX_SIZE - unIndex-1;
            }
            if(GstrCloud_ObjMap[Lu8Group][Lu8SnsIndex].u8PointCloudUpdate)
            {
                LstrPointCloudInfo.u8PointCloud_ObjHeight = GstrCloud_ObjMap[Lu8Group][Lu8SnsIndex].eMapObjHeight;
                LstrPointCloudInfo.u8PointCloud_ObjHeightProb = GstrCloud_ObjMap[Lu8Group][Lu8SnsIndex].eObjHeightProb;
                LstrPointCloudInfo.u8PointCloud_ObjProb = GstrCloud_ObjMap[Lu8Group][Lu8SnsIndex].eObjExistProb;
                LstrPointCloudInfo.u8PointCloud_ObjType = GstrCloud_ObjMap[Lu8Group][Lu8SnsIndex].eMapObjType;
                LstrPointCloudInfo.s16PointCloud_ObjPX = GstrCloud_ObjMap[Lu8Group][Lu8SnsIndex].s16ObjMapP1_X;
                LstrPointCloudInfo.s16PointCloud_ObjPY = GstrCloud_ObjMap[Lu8Group][Lu8SnsIndex].s16ObjMapP1_Y;
                PdcSignal_WritePointCloudObj(&LstrPointCloudInfo,unIndex);
                GstrCloud_ObjMap[Lu8Group][Lu8SnsIndex].u8PointCloudUpdate = 0;
            }
        }
    }
    else
    {
        LstrPointCloudInfo.s16PointCloud_ObjPX = 0;
        LstrPointCloudInfo.s16PointCloud_ObjPY = 0;
        LstrPointCloudInfo.u8PointCloud_ObjHeight = 3;
        LstrPointCloudInfo.u8PointCloud_ObjHeightProb = 0;
        LstrPointCloudInfo.u8PointCloud_ObjProb = 0;
        LstrPointCloudInfo.u8PointCloud_ObjType = 0;
        for(i=0; i<POINTCLOUD_MAX_SIZE;i++)
        {
            PdcSignal_WritePointCloudObj(&LstrPointCloudInfo,i);
        }
    }    
}

void PAS_MAP_To_CANInit(void)
{
	PdcSignal_USS_DE_Distace_Init(FRONT_GROUP);
	PdcSignal_USS_DE_Distace_Init(REAR_GROUP);
	PdcSignal_USS_CE_Distace_Init(FRONT_GROUP);
	PdcSignal_USS_CE_Distace_Init(REAR_GROUP);
	PdcSignal_ZoneDistanceInit();
	PdcSignal_MapObjInit();
	TrailerHitchDetected_init();
}


/******************************************************************************
 * 函数名称: PAS_MAP_StateUpdateAndToCan_Task
 * 
 * 功能描述: PAS MAP状态信号更新以及更新数据到CAN；由于Map周期为20ms，因此该函数建议调度周期为10ms
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-10-19 20:54   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PAS_MAP_StateUpdateAndToCan_Task(void)
{
    PAS_GetCANSignal();
    PAS_WorkLogic();
    PdcSignal_WritePDCWorkStatus(GstrPDCToCAN_PASData.enuPAS_WorkStatus);
    WritePP_SDW_DisToCan();
    WriteDE_DistanceToCan();
    WriteCE_DistanceToCan();
    WriteMapObjToCan();
    WritePointCloudToCan();
}






/*****************************************END PDCToCan.c***********************************************/


