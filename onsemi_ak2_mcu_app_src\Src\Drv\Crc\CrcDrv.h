/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: CrcDrv.h
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/
#ifndef __CRCDRV_H
#define __CRCDRV_H

/********************************数据类型定义**********************************/



/*******************************全局函数声明***********************************/
extern void CrcDrvInit(void);
extern uint16_t CRC16CalculateByMCU(const uint8_t * data, uint32_t data_num);

void CRC8_C2_InitTable(void);
uint8 CRC8_C2_CalculateBySw(const uint8 *Data, uint32 Len, uint8 crc_init);

#endif
