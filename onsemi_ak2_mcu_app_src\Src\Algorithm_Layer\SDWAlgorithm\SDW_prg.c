/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * SDW_prg: 
 * Created on: 2022 -12 08 
 * Original designer: 22996
 ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "SDW_Cfg.h"
#include "SDW_Int.h"
#include "TimerManage.h"
#include "SDW_CalibPara.h"
#include "PublicCalAlgorithm_Int.h"
#include "Sns_install_Coordinate.h"
#include "Vehicle_Geometry_Parameter.h"
//#include "PASStateManage.h"
#include "SnsRawData_Int.h"
#include "PAS_MAP_SignalManage_Types.h"
#include "CAN_AppSignalManage.h"
#include "PowerSingalManage.h"

#ifdef SYS_USE_SDW

/******************************************************************************/
/******************************************************************************/
/****************************** Private Definition ****************************/
/******************************************************************************/
/******************************************************************************/
SDW_ObjCoorType GstrObjCoorBuffer[SIDE_OBJ_NUM][SDW_MAX_BUFFER_CNT];
static SDW_SnsCoorType GstrSDW_SnsCoor[SDW_SNS_CHANNEL_NUM];
static SDW_SnsCoorBackupType GstrSDW_SnsCoorBackup[SDW_SNS_CHANNEL_NUM];
uint16 Gu16SDW_VirtualDis[SIDE_OBJ_NUM][SIDE_AREA_NUM];
SDW_CarCoorType GstrCarCoor; 
SDW_SnsOriginalDataType GstrSDWSnsOriginalData;
SDW_SnsBufferDataType GstrSDWSnsBufferData;
SDW_ValidObjCoorType GstrSDWValidObjCoor;
uint8 Gu8CarStop60S_ClearFlag = 0;
SDWOriginalDataType GstrSDWOriginalData[3];
uint8 GstrSDWOriDataIndex[SDW_SNS_CHANNEL_NUM]={0};
uint8 u8StatusDataFlg[SDW_SNS_CHANNEL_NUM] ={0};
static uint8 u8FristFlg =0;


/******************************************************************************/
/******************************************************************************/
/****************************** Public Definition *****************************/
/******************************************************************************/
/******************************************************************************/    


/******************************************************************************/
/******************************************************************************/
/****************************** Private Function ******************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************
* 函数名称: ClearSDWOriginalData
*
* 功能描述:
*
* 输入参数:无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
void SDWOriginalDataInit(void)
{
    uint8 i=0;
    uint8 j=0;
    uint8 z=0;
    for(i = 0; i < 3; i++)
    {
        SDWOriginalDataType *LpstrSDWOriginalData;
        LpstrSDWOriginalData = &GstrSDWOriginalData[i];
        for(j = 0; j < SDW_SNS_CHANNEL_NUM; j++)
        {
            for(z=0;z<SDW_COOR_MAX_BUFFER_CNT;z++)
            {
                LpstrSDWOriginalData->u16MasterDis[j][z] =65535;
                LpstrSDWOriginalData->u16MasterHeight[j][z] =0;
                LpstrSDWOriginalData->u16ListenDis[j][z] =65535;
                LpstrSDWOriginalData->u16ListenHeight[j][z] =0;
                
            }
            LpstrSDWOriginalData->u8MasterObjCnt[j] =0;
            LpstrSDWOriginalData->u8ListenObjCnt[j] =0;
            
        }
        
    }    
}

/******************************************************************
* 函数名称: ClearSDWOriginalData
*
* 功能描述:
*
* 输入参数:无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
void ClearSDWOriginalData(uint8 u8CurIndex,SDW_tenuSnsChannel LeSDWSnsChannel)
{

    uint8 z=0;
    SDWOriginalDataType *LpstrSDWOriginalData;
    LpstrSDWOriginalData = &GstrSDWOriginalData[u8CurIndex];
    for(z=0;z<SDW_COOR_MAX_BUFFER_CNT;z++)
    {
        LpstrSDWOriginalData->u16MasterDis[LeSDWSnsChannel][z] =65535;
        LpstrSDWOriginalData->u16MasterHeight[LeSDWSnsChannel][z] =0;
        LpstrSDWOriginalData->u16ListenDis[LeSDWSnsChannel][z] =65535;
        LpstrSDWOriginalData->u16ListenHeight[LeSDWSnsChannel][z] =0;
        
    }
    LpstrSDWOriginalData->u8MasterObjCnt[LeSDWSnsChannel] =0;
    LpstrSDWOriginalData->u8ListenObjCnt[LeSDWSnsChannel] =0;
            
}



/******************************************************************
* 函数名称: ClearSidsSnsBufferData
*
* 功能描述:
*
* 输入参数:无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
void ClearSidsSnsBufferData(void)
{
    uint8 i,j;

    for(i = 0; i < SDW_SNS_CHANNEL_NUM; i++)
    {
        for(j = 0; j < SIDE_SNS_BUFFER_CNT; j++)
        {
            GstrSDWSnsBufferData.u16SnsDis[i][j] = 65535;
            GstrSDWSnsBufferData.u16SnsHeight[i][j] = 0;
            GstrSDWSnsBufferData.u8ValidCnt[i][j] = 0;
            GstrSDWSnsBufferData.fSnsObjX[i][j] = 0;
            GstrSDWSnsBufferData.fSnsObjY[i][j] = 0;
            GstrSDWSnsBufferData.fSnsCosAngle[i][j] = 1;
            GstrSDWSnsBufferData.fSnsSinAngle[i][j] = 0;
        }
        GstrSDWSnsBufferData.u16RealTimeDis[i] = 65535;
    }    
}

/******************************************************************
* 函数名称: ClearSDWValidObjCoor
*
* 功能描述:
*
* 输入参数:无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
void ClearSDWValidObjCoor(uint8 Lu8Ch)
{
    GstrSDWValidObjCoor.fPosX[Lu8Ch] = 0; 
    GstrSDWValidObjCoor.fPosY[Lu8Ch] = 0; 
    GstrSDWValidObjCoor.u16Dis[Lu8Ch] = 65535; 
    GstrSDWValidObjCoor.u8ValidFlag[Lu8Ch] = 0; 
    GstrSDWValidObjCoor.fCosAngle[Lu8Ch] = 1; 
    GstrSDWValidObjCoor.fSinAngle[Lu8Ch] = 0; 
}

/******************************************************************
* 函数名称: SDWObjBufferInit
*
* 功能描述: SDW 侧边记录障碍物初始化
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
static void SDWObjBufferInit(void)
{
    uint8 i,j;
    for(i = 0; i < SIDE_OBJ_NUM; i++)
    {
        for(j = 0; j < SDW_MAX_BUFFER_CNT; j++)
        {
            GstrObjCoorBuffer[i][j].fPosX = 0;
            GstrObjCoorBuffer[i][j].fPosY = 0;
            GstrObjCoorBuffer[i][j].u8ValidFlag = 0;

            GstrObjCoorBuffer[i][j].fFrontSnsCosAngle = 0;
            GstrObjCoorBuffer[i][j].fRearSnsCosAngle = 0;
            GstrObjCoorBuffer[i][j].u16LateralDis = SDW_INVALID_DIS;
            GstrObjCoorBuffer[i][j].u16LongitudiDis = SDW_INVALID_DIS;
            GstrObjCoorBuffer[i][j].fObjInCraX = 0;
            GstrObjCoorBuffer[i][j].fObjInCraY = 0;
        }
    }
}


/******************************************************************
* 函数名称: SDWObjBufferInit
*
* 功能描述: SDW 侧边记录障碍物初始化
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
static void SDWSideVirtualDisInit(void)
{
    uint8 i,j;
    for(i = 0; i < SIDE_OBJ_NUM; i++)
    {
        for(j = 0; j < SIDE_AREA_NUM; j++)
        {
            Gu16SDW_VirtualDis[i][j] = SDW_INVALID_DIS;
        }
    }
}

/******************************************************************
* 函数名称: SDWObjBufferNoObj
*
* 功能描述: SDW 侧边记录障碍物无障碍物
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
static void SDWObjBufferNoObj(void)
{
    uint8 i,j;
    for(i = 0; i < SIDE_OBJ_NUM; i++)
    {
        for(j = 0; j < SIDE_AREA_NUM; j++)
        {
            Gu16SDW_VirtualDis[i][j] = SDW_SIDE_NO_OBJ_DISTANCE_TO_CAN;
        }
    }

}



/******************************************************************
* 函数名称: SDWWheelDataInit
*
* 功能描述: SDW轮速信息初始化
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 无
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
static void SDWWheelDataInit(void)
{
    GstrSDW_InputWheel.enuRLWheelDir = SDW_WHEEL_DIR_STOP;
    GstrSDW_InputWheel.enuRRWheelDir = SDW_WHEEL_DIR_STOP;
}

/******************************************************************
* 函数名称: SDWCarCoorDataInit
*
* 功能描述: SDW车身坐标数据初始化
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 无
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
static void SDWCarCoorDataInit(void)
{
    GstrCarCoor.fFrontMoveDis = 0;
    GstrCarCoor.fRearMoveDis = 0;
    GstrCarCoor.u8FrontMoveDis20cmFlag = 0;
    GstrCarCoor.u8RearMoveDis20cmFlag = 0;
    GstrCarCoor.fPosX = 0;
    GstrCarCoor.fPosY = 0;
    GstrCarCoor.fPosAngle = 0;
    GstrCarCoor.fsinAngle = 0;
    GstrCarCoor.fcosAngle = 1.0;

    GstrCarCoor.enuCarMovingStatus = SDW_WHEEL_DIR_STOP;
    GstrCarCoor.u16CarContinueStopCnt = 0;
}

/******************************************************************
* 函数名称: SDWSnsCoorInit
*
* 功能描述: SDW探头坐标数据初始化
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 无
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
static void SDWSnsCoorInit(void)
{
    uint8 i;

    for(i = 0; i < 4; i++)
    {
        GstrSDW_SnsCoor[i].fPosX = 0;
        GstrSDW_SnsCoor[i].fPosY = 0;
        GstrSDW_SnsCoorBackup[i].fMovingDis = 0;
        GstrSDW_SnsCoorBackup[i].u8Move10cmFlg = 0;
    }
    
    /*  分别计算FLS FRS RLS RRS 四个点的坐标 */
    GstrSDW_SnsCoorBackup[SDW_FLS_CHANNEL].fPosX = 0 + GstrSDW_DisCarPara_Ram.LfFrontSideToRearAxleDis;
    GstrSDW_SnsCoorBackup[SDW_FLS_CHANNEL].fPosY = 0 + GstrSDW_DisCarPara_Ram.LfFrontSideToCarCenter;

    GstrSDW_SnsCoorBackup[SDW_RLS_CHANNEL].fPosX = 0 - GstrSDW_DisCarPara_Ram.LfRearSideToRearAxleDis;
    GstrSDW_SnsCoorBackup[SDW_RLS_CHANNEL].fPosY = 0 + GstrSDW_DisCarPara_Ram.LfRearSideToCarCenter;

    GstrSDW_SnsCoorBackup[SDW_FRS_CHANNEL].fPosX = 0 + GstrSDW_DisCarPara_Ram.LfFrontSideToRearAxleDis;
    GstrSDW_SnsCoorBackup[SDW_FRS_CHANNEL].fPosY = 0 - GstrSDW_DisCarPara_Ram.LfFrontSideToCarCenter;

    GstrSDW_SnsCoorBackup[SDW_RRS_CHANNEL].fPosX = 0 - GstrSDW_DisCarPara_Ram.LfRearSideToRearAxleDis;
    GstrSDW_SnsCoorBackup[SDW_RRS_CHANNEL].fPosY = 0 - GstrSDW_DisCarPara_Ram.LfRearSideToCarCenter; 
}


/******************************************************************
* 函数名称: SDWValidObjCoorInit
*
* 功能描述: SDW坐标数据初始化
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 无
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
static void SDWValidObjCoorInit(void)
{
    uint8 i;
    for(i = 0; i < SDW_SNS_CHANNEL_NUM; i++)
    {
        ClearSDWValidObjCoor(i);
        GstrSDWValidObjCoor.u16MinDisBackup[i] = 0xFFFF;
    }
}

static uint8 CheckTriangle(float n1 , float n2 , float n3)
{
    uint8 flg=0;
    if(((n1+n2) > n3)&& ((n1+n3)>n2) && ((n3+n2)>n1))
    {
    	flg=1;
    }
    else
    {
    	flg=0;
    }
    return flg;
}

/******************************************************************
* 函数名称: SDWCalcObjDis
*
* 功能描述: 计算障碍物在侧探头Y方向的距离
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 无
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
uint16 SDWCalcObjDis(SDW_tenuSnsChannel SDWSnsChl,const SDW_SnsOriginalDataType *pstrOriData,const SDW_Dis_CarParameterType *pstrCfgParam)
{
	float fp32DE_Dis = pstrOriData->u16Dis[SDWSnsChl][0];
	float fp32CE_Dis = (pstrOriData->u16ListenDis[SDWSnsChl] << 1) - fp32DE_Dis;
	float BumperDis = 0;
	float cosAngle1 = 0;
	float sinAngle1 = 0;
    float cosAngle2=0;
    float sinAngle2=0;
    float cosAngle1and2=0;
	float sinAngle1and2 = 0;
    float SDWOppositeDistance=65535;
    uint16 u16SDWOppositeDistance=0xFFFF; 
	uint8 Lu8TableIdx = 0;
	uint16 XCoorDisThre = 0;
	const SDW_HeightThreType *pHeightThreTable = &SDW_HeightThreTable[0];
	float compensationFactor = 1;
	
	switch(SDWSnsChl)
    {
        case SDW_FLS_CHANNEL:
        case SDW_FRS_CHANNEL:	
        BumperDis = pstrCfgParam->LfFLS_FLBumperDis;
        cosAngle1 = pstrCfgParam->LfFrontSDWAngleCos;
        sinAngle1 = pstrCfgParam->LfFrontSDWAngleSin;
        break;
		
        case SDW_RLS_CHANNEL:
		case SDW_RRS_CHANNEL:
        BumperDis = pstrCfgParam->LfRLS_RLBumperDis;
        cosAngle1 = pstrCfgParam->LfRearSDWAngleCos;
        sinAngle1 = pstrCfgParam->LfRearSDWAngleSin;     
        break;

        default:
        fp32DE_Dis = 0;
		fp32CE_Dis = 0;
        BumperDis = 0;
        cosAngle1 = 0;
        sinAngle1 = 0;            
        break;
    }

    if((fp32DE_Dis >= 5000)||(fp32DE_Dis <= 0))
    {
        fp32DE_Dis = 0;
    }
    if((fp32CE_Dis >= 5000)||(fp32CE_Dis <= 0))
    {
        fp32CE_Dis = 0;
    }
		
    if(fp32DE_Dis>0)
    {
        if((CheckTriangle(fp32DE_Dis,fp32CE_Dis,BumperDis)) && (ABS(fp32DE_Dis,fp32CE_Dis) < SDW_MASTER_LISTEN_SUB))
        {
            cosAngle2=(pow(fp32DE_Dis,2)+pow(BumperDis,2)-pow(fp32CE_Dis,2))/(2*fp32CE_Dis*BumperDis);
            sinAngle2=sqrt(1-pow(cosAngle2,2));
            cosAngle1and2=cosAngle2*cosAngle1-sinAngle2*sinAngle1;
            cosAngle1and2=-cosAngle1and2;
            if(cosAngle1and2>=0)
            {
				sinAngle1and2 = sqrt(1-pow(cosAngle1and2,2));

				/*根据不同的车速，设置提前告警的距离*/
				if(Gu16SDW_Speed < 400)
				{
					XCoorDisThre = 300;
				}
				else if(Gu16SDW_Speed < 600)
				{
					XCoorDisThre = 500;
				}
				else
				{
					XCoorDisThre = 700;
				}
				
				if(sinAngle1and2*fp32DE_Dis < XCoorDisThre)
				{
                	SDWOppositeDistance=cosAngle1and2*fp32DE_Dis;
					u16SDWOppositeDistance=(uint16)(SDWOppositeDistance + 0.5);
				}
            }
        }
        else
        {
              /*有可能没有CE值，收窄FOV*/

			  if(fp32DE_Dis < 950)
			  {
				  compensationFactor = 1.45;
			  }
			  else
			  {
				  compensationFactor = 1;
			  }

              Lu8TableIdx = fp32DE_Dis / SDW_SNS_HIGH_TABLE_STEP;

			  if(Lu8TableIdx >= SDW_SNS_DIS_HEIGHT_TOTAL_NUM)
			  {
				  Lu8TableIdx = SDW_SNS_DIS_HEIGHT_2000mm;
			  }
			  
              if(PDC_SNS_MEAS_STD == pstrOriData->eMeasType[SDWSnsChl]) 
              {
                  if(pstrOriData->u16Height[SDWSnsChl][0] > pHeightThreTable[Lu8TableIdx].u16StdHeight*compensationFactor)
                  {
                      SDWOppositeDistance = fp32DE_Dis;
                      u16SDWOppositeDistance=(uint16)(SDWOppositeDistance);
                  }
              }
              else if((PDC_SNS_MEAS_ADV_CHIRP_DOWN == pstrOriData->eMeasType[SDWSnsChl])
                      || (PDC_SNS_MEAS_ADV_CHIRP_UP == pstrOriData->eMeasType[SDWSnsChl]))
              {
                  if(pstrOriData->u16Height[SDWSnsChl][0] > pHeightThreTable[Lu8TableIdx].u16AdvHeight*compensationFactor)
                  {
                      SDWOppositeDistance = fp32DE_Dis;						
                      u16SDWOppositeDistance=(uint16)(SDWOppositeDistance);
                  }
              }
              else
              {}
        }
    }
	
    return u16SDWOppositeDistance;
}


/******************************************************************************
 * 函数名称: SDW_GetOriginalData
 * 
 * 功能描述: 从底层获取SDW的原始数据
 * 
 * 输入参数:无 
 * 
 * 输处参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-07-02 16:11   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SDW_GetOriginalData(PDCSnsGroupType LeGroup,PDCSnsChannelType LenuSnsCh)
{
    SDW_tenuSnsChannel LeSDWSnsChannel;
    static uint8 enuCurIndex[4]={0};
    uint8 enuLastIndex[4] ={0};
    uint8 enuLastTwoIndex[4]={0};
    uint8 i,j;
    uint16 Lu16DisSub;
    uint8 Lu8FindVlaidDisFlag = 0;
	uint8 Lu8TableHighIdx = 0;
	const SDW_HeightThreType *pSDW_HeightThreTable = &SDW_HeightThreTable[0];

    /* 通道转换 */
    if(LeGroup == PDC_SNS_GROUP_FRONT)
    {
        if(LenuSnsCh == PDC_SNS_CH_FLS)
        {
            LeSDWSnsChannel = SDW_FLS_CHANNEL;
			GstrSDWSnsOriginalData.eMeasType[LeSDWSnsChannel] = GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh].eMeasType;
            GstrSDWSnsOriginalData.u16MasterDis[LeSDWSnsChannel] = GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh].MasDisData.u16FollowDis;
			if(0 != GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh].MasDisData.u16EchoHeightDetect_New)
			{
				/*当follow数据没有更新时，回波高度为0*/
            	GstrSDWSnsOriginalData.u16MasterHeight[LeSDWSnsChannel] = GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh].MasDisData.u16EchoHeightDetect_New;
			}
            GstrSDWSnsOriginalData.u16ListenDis[LeSDWSnsChannel] = GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh].RightLisDisData.u16FollowDis;
			if(0 != GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh].RightLisDisData.u16EchoHeightDetect_New)
			{
        		GstrSDWSnsOriginalData.u16ListenHeight[LeSDWSnsChannel] = GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh].RightLisDisData.u16EchoHeightDetect_New;
			}
        }
        else
        {
            LeSDWSnsChannel = SDW_FRS_CHANNEL;
			GstrSDWSnsOriginalData.eMeasType[LeSDWSnsChannel] = GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh].eMeasType;
            GstrSDWSnsOriginalData.u16MasterDis[LeSDWSnsChannel] = GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh].MasDisData.u16FollowDis;
			if(0 != GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh].MasDisData.u16EchoHeightDetect_New)
			{
        		GstrSDWSnsOriginalData.u16MasterHeight[LeSDWSnsChannel] = GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh].MasDisData.u16EchoHeightDetect_New;
			}
            GstrSDWSnsOriginalData.u16ListenDis[LeSDWSnsChannel] = GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh].LeftLisDisData.u16FollowDis;
			if(0 != GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh].LeftLisDisData.u16EchoHeightDetect_New)
			{
            	GstrSDWSnsOriginalData.u16ListenHeight[LeSDWSnsChannel] = GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh].LeftLisDisData.u16EchoHeightDetect_New;
			}
        }
    }
    else
    {
        if(LenuSnsCh == PDC_SNS_CH_RLS)
        {
            LeSDWSnsChannel = SDW_RLS_CHANNEL;
			GstrSDWSnsOriginalData.eMeasType[LeSDWSnsChannel] = GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh].eMeasType;
            GstrSDWSnsOriginalData.u16MasterDis[LeSDWSnsChannel] = GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh].MasDisData.u16FollowDis;
			if(0 != GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh].MasDisData.u16EchoHeightDetect_New)
			{
            	GstrSDWSnsOriginalData.u16MasterHeight[LeSDWSnsChannel] = GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh].MasDisData.u16EchoHeightDetect_New;
			}
            GstrSDWSnsOriginalData.u16ListenDis[LeSDWSnsChannel] = GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh].RightLisDisData.u16FollowDis;
			if(0 != GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh].RightLisDisData.u16EchoHeightDetect_New)
			{
        		GstrSDWSnsOriginalData.u16ListenHeight[LeSDWSnsChannel] = GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh].RightLisDisData.u16EchoHeightDetect_New;
			}
        }
        else
        {
            LeSDWSnsChannel = SDW_RRS_CHANNEL;
			GstrSDWSnsOriginalData.eMeasType[LeSDWSnsChannel] = GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh].eMeasType;
            GstrSDWSnsOriginalData.u16MasterDis[LeSDWSnsChannel] = GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh].MasDisData.u16FollowDis;
			if(0 != GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh].MasDisData.u16EchoHeightDetect_New)
			{
            	GstrSDWSnsOriginalData.u16MasterHeight[LeSDWSnsChannel] = GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh].MasDisData.u16EchoHeightDetect_New;
			}
            GstrSDWSnsOriginalData.u16ListenDis[LeSDWSnsChannel] = GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh].LeftLisDisData.u16FollowDis;
			if(0 != GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh].LeftLisDisData.u16EchoHeightDetect_New)
			{
            	GstrSDWSnsOriginalData.u16ListenHeight[LeSDWSnsChannel] = GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh].LeftLisDisData.u16EchoHeightDetect_New;
			}
        }
    }    

    /* 同步数据到SDW PP */
    /* 从跟踪模块获取数据，直接供SDW模块使用 */
    GstrSDWSnsOriginalData.u8ValidCnt[LeSDWSnsChannel] = GstrSDWSnsOriginalData.u8ObjCnt[LeSDWSnsChannel];
    GstrSDWSnsOriginalData.u16Dis[LeSDWSnsChannel][0] = GstrSDWSnsOriginalData.u16MasterDis[LeSDWSnsChannel];
    GstrSDWSnsOriginalData.u16Height[LeSDWSnsChannel][0] = GstrSDWSnsOriginalData.u16MasterHeight[LeSDWSnsChannel];
    /* 50cm外必须有侦听，否则无效--目的是滤除同频干扰以及碎石误报 */
    #if 0
    uint16 Lu16MasterListenSub;
    if((GstrSDWSnsOriginalData.u16Dis[LeSDWSnsChannel][0] > 500)&&(GstrSDWSnsOriginalData.u16Dis[LeSDWSnsChannel][0] < GstrSDW_DisCarPara_Ram.u16StartFollowDis))
    {
        Lu16MasterListenSub = ABS(GstrSDWSnsOriginalData.u16MasterDis[LeSDWSnsChannel], GstrSDWSnsOriginalData.u16ListenDis[LeSDWSnsChannel]);
        if(Lu16MasterListenSub > SDW_MASTER_LISTEN_SUB)
        {
            GstrSDWSnsOriginalData.u8ValidCnt[LeSDWSnsChannel] = 0;
            GstrSDWSnsOriginalData.u16Dis[LeSDWSnsChannel][0] = 65535;
            GstrSDWSnsOriginalData.u16Height[LeSDWSnsChannel][0] = 0;
        }
    }
    #endif

    /* 添加针对水波纹路面的处理，通过回波宽度进行过滤--前雷达发32个波后，整体能量变强 */
#if 0
    if(((GstrSDWSnsOriginalData.u16Dis[LeSDWSnsChannel][0] > 750)&&(GstrSDWSnsOriginalData.u16Dis[LeSDWSnsChannel][0] < GstrSDW_DisCarPara_Ram.u16StartFollowDis))&&\
        (GstrSDWSnsOriginalData.u16Width[LeSDWSnsChannel][0] < 350))
    {
        /* 超过80cm的地方，必须回波宽度大于530，以避开水波纹路面误报 */
        GstrSDWSnsOriginalData.u16Dis[LeSDWSnsChannel][0] = 0xFFFF;
        GstrSDWSnsOriginalData.u16Width[LeSDWSnsChannel][0] = 0;
    }
#endif
    
    for(j = (SIDE_SNS_BUFFER_CNT-1); j > 0;j--)
    {
        GstrSDWSnsBufferData.u16SnsDis[LeSDWSnsChannel][j] = GstrSDWSnsBufferData.u16SnsDis[LeSDWSnsChannel][j-1];
        GstrSDWSnsBufferData.u16SnsHeight[LeSDWSnsChannel][j] = GstrSDWSnsBufferData.u16SnsHeight[LeSDWSnsChannel][j-1];
        GstrSDWSnsBufferData.u8ValidCnt[LeSDWSnsChannel][j] = GstrSDWSnsBufferData.u8ValidCnt[LeSDWSnsChannel][j-1];        
        GstrSDWSnsBufferData.fSnsObjX[LeSDWSnsChannel][j] = GstrSDWSnsBufferData.fSnsObjX[LeSDWSnsChannel][j-1];
        GstrSDWSnsBufferData.fSnsObjY[LeSDWSnsChannel][j] = GstrSDWSnsBufferData.fSnsObjY[LeSDWSnsChannel][j-1];
        GstrSDWSnsBufferData.fSnsCosAngle[LeSDWSnsChannel][j] = GstrSDWSnsBufferData.fSnsCosAngle[LeSDWSnsChannel][j-1];
        GstrSDWSnsBufferData.fSnsSinAngle[LeSDWSnsChannel][j] = GstrSDWSnsBufferData.fSnsSinAngle[LeSDWSnsChannel][j-1];
    }
    
    if(GstrSDWSnsOriginalData.u16Dis[LeSDWSnsChannel][0] < GstrSDW_DisCarPara_Ram.u16StartFollowDis)
    {		
        GstrSDWSnsBufferData.u16SnsDis[LeSDWSnsChannel][0] = SDWCalcObjDis(LeSDWSnsChannel,&GstrSDWSnsOriginalData,&GstrSDW_DisCarPara_Ram);
        GstrSDWSnsBufferData.u16SnsHeight[LeSDWSnsChannel][0] = GstrSDWSnsOriginalData.u16Height[LeSDWSnsChannel][0];
        GstrSDWSnsBufferData.u8ValidCnt[LeSDWSnsChannel][0] = GstrSDWSnsOriginalData.u8ValidCnt[LeSDWSnsChannel];
        GstrSDWSnsBufferData.fSnsObjX[LeSDWSnsChannel][0] = GstrCarCoor.fPosX;
        GstrSDWSnsBufferData.fSnsObjY[LeSDWSnsChannel][0] = GstrCarCoor.fPosY;
        GstrSDWSnsBufferData.fSnsCosAngle[LeSDWSnsChannel][0] = GstrCarCoor.fcosAngle;
        GstrSDWSnsBufferData.fSnsSinAngle[LeSDWSnsChannel][0] = GstrCarCoor.fsinAngle;
    }
    else
    {
        GstrSDWSnsBufferData.u16SnsDis[LeSDWSnsChannel][0] = 0xFFFF;
        GstrSDWSnsBufferData.u16SnsHeight[LeSDWSnsChannel][0] = 0;
        GstrSDWSnsBufferData.u8ValidCnt[LeSDWSnsChannel][0] = 0;
        GstrSDWSnsBufferData.fSnsObjX[LeSDWSnsChannel][0] = 0;
        GstrSDWSnsBufferData.fSnsObjY[LeSDWSnsChannel][0] = 0;
        GstrSDWSnsBufferData.fSnsCosAngle[LeSDWSnsChannel][0] = 1;
        GstrSDWSnsBufferData.fSnsSinAngle[LeSDWSnsChannel][0] = 0;
    }
    /* 针对FLS 和FRS 需要实时距离进行添加，以保证水平泊车入库的正常报警 */
    uint8 Lu8ValidCnt = 0;
    uint16 Lu16MinDis = 0xFFFF;
    uint16 Lu16MaxDis = 0;
    uint16 Lu16MinMaxDisSub;
    GstrSDWSnsBufferData.u16RealTimeDis[LeSDWSnsChannel] = 0xFFFF;
    for(j = 0; j < SIDE_SNS_BUFFER_CNT;j++)
    {
        //if(GstrSDWSnsBufferData.u16SnsDis[LeSDWSnsChannel][j] < GstrSDW_DisCarPara_Ram.u16StartFollowDis)
        if(GstrSDWSnsBufferData.u16SnsDis[LeSDWSnsChannel][j] < 700)
        {
            if(GstrSDWSnsBufferData.u16SnsDis[LeSDWSnsChannel][j] < Lu16MinDis)
            {
                Lu16MinDis = GstrSDWSnsBufferData.u16SnsDis[LeSDWSnsChannel][j];
            }
            if(GstrSDWSnsBufferData.u16SnsDis[LeSDWSnsChannel][j] > Lu16MaxDis)
            {
                Lu16MaxDis = GstrSDWSnsBufferData.u16SnsDis[LeSDWSnsChannel][j];
            } 
            Lu8ValidCnt++;
        }
    }

    /* 又有效数据才处理  */
    if(Lu8ValidCnt > 0)
    {
        Lu16MinMaxDisSub = ABS(Lu16MinDis,Lu16MaxDis);
        if(Lu16MinMaxDisSub < 150)
        {
			if(SDW_WHEEL_DIR_STOP == GstrCarCoor.enuCarMovingStatus)//停止的时候才用DE值替代SDW值
            {
                GstrSDWSnsBufferData.u16RealTimeDis[LeSDWSnsChannel] = Lu16MinDis;
            }
			else
			{
				/*解决障碍物起始位在中间位置，没有侦听而无法报警的问题*/
	    		Lu8TableHighIdx = Lu16MinDis / SDW_SNS_HIGH_TABLE_STEP;

				if(PDC_SNS_MEAS_STD == GstrSDWSnsOriginalData.eMeasType[LeSDWSnsChannel]) 
				{
					if(GstrSDWSnsOriginalData.u16Height[LeSDWSnsChannel][0] > pSDW_HeightThreTable[Lu8TableHighIdx].u16StdHeight)
					{
						GstrSDWSnsBufferData.u16RealTimeDis[LeSDWSnsChannel] = Lu16MinDis;
					}
				}
				else if((PDC_SNS_MEAS_ADV_CHIRP_DOWN == GstrSDWSnsOriginalData.eMeasType[LeSDWSnsChannel])
						|| (PDC_SNS_MEAS_ADV_CHIRP_UP == GstrSDWSnsOriginalData.eMeasType[LeSDWSnsChannel]))
				{
					if(GstrSDWSnsOriginalData.u16Height[LeSDWSnsChannel][0] > pSDW_HeightThreTable[Lu8TableHighIdx].u16AdvHeight)
					{
						GstrSDWSnsBufferData.u16RealTimeDis[LeSDWSnsChannel] = Lu16MinDis;
					}
				}
				else
				{}
			}
        }
    }
}

uint16 Lu16MinMaxDisThresOpt(uint16 u16VehicleSpeed,uint8 Lu8ValidCnt)
{

    uint16 Lu16MinMaxDisThres =0;
    SDW_Speed_StsVal enuSDWSpeedVal=CAR_SPEED_NONE;
    if (u16VehicleSpeed > 900)
    {
        enuSDWSpeedVal=CAR_SPEED_GREATER_9KM;
    }
    else if ((u16VehicleSpeed > 700)&&(u16VehicleSpeed <= 900))
    {
        enuSDWSpeedVal=CAR_SPEED_7KM_9KM;
    }
    else if ((u16VehicleSpeed > 500)&&(u16VehicleSpeed <= 700))
    {
        enuSDWSpeedVal=CAR_SPEED_5KM_7KM;
    }
    else if ((u16VehicleSpeed > 300)&&(u16VehicleSpeed <= 500))
    {
        enuSDWSpeedVal=CAR_SPEED_3KM_5KM;
    }
    else if ((u16VehicleSpeed > 100)&&(u16VehicleSpeed <= 300))
    {
        enuSDWSpeedVal=CAR_SPEED_1KM_3KM;
    }
    else
    {
        enuSDWSpeedVal=CAR_SPEED_LESS_1KM;
    }
    
    
    switch(Lu8ValidCnt)
    {
        case 5:
            switch(enuSDWSpeedVal)
            {
                case CAR_SPEED_GREATER_9KM:
                    Lu16MinMaxDisThres=320;
                break;
                case CAR_SPEED_7KM_9KM:
                    Lu16MinMaxDisThres=180;
                break;
                case CAR_SPEED_5KM_7KM:
                    Lu16MinMaxDisThres=180;
                break;
                case CAR_SPEED_3KM_5KM:
                    Lu16MinMaxDisThres=150;
                break;
                case CAR_SPEED_1KM_3KM:
                    Lu16MinMaxDisThres=80;
                break;
                default:
                    Lu16MinMaxDisThres=30;
                break;
            }
        case 4:
            switch(enuSDWSpeedVal)
            {
                case CAR_SPEED_GREATER_9KM:
                    Lu16MinMaxDisThres=100;
                break;
                case CAR_SPEED_7KM_9KM:
                    Lu16MinMaxDisThres=80;
                break;
                case CAR_SPEED_5KM_7KM:
                    Lu16MinMaxDisThres=80;
                break;
                case CAR_SPEED_3KM_5KM:
                    Lu16MinMaxDisThres=80;
                break;
                case CAR_SPEED_1KM_3KM:
                    Lu16MinMaxDisThres=100;
                break;
                default:
                    Lu16MinMaxDisThres=0;
                break;
            }
            break;
        case 3:
            switch(enuSDWSpeedVal)
            {
                case CAR_SPEED_GREATER_9KM:
                    Lu16MinMaxDisThres=320;
                break;
                case CAR_SPEED_7KM_9KM:
                    Lu16MinMaxDisThres=180;
                break;
                case CAR_SPEED_5KM_7KM:
                    Lu16MinMaxDisThres=130;
                break;
                case CAR_SPEED_3KM_5KM:
                    Lu16MinMaxDisThres=120;
                break;
                case CAR_SPEED_1KM_3KM:
                    Lu16MinMaxDisThres=30;
                break;
                default:
                    Lu16MinMaxDisThres=0;
                break;
            }
            break;
        case 2:
            switch(enuSDWSpeedVal)
            {
                case CAR_SPEED_GREATER_9KM:
                    Lu16MinMaxDisThres=180;
                break;
                case CAR_SPEED_7KM_9KM:
                    Lu16MinMaxDisThres=180;
                break;
                case CAR_SPEED_5KM_7KM:
                    Lu16MinMaxDisThres=130;
                break;
                case CAR_SPEED_3KM_5KM:
                    Lu16MinMaxDisThres=120;
                break;
                case CAR_SPEED_1KM_3KM:
                    Lu16MinMaxDisThres=50;
                break;
                default:
                    Lu16MinMaxDisThres=0;
                break;
            }
            break;
    
        default:
            break;
    }
return Lu16MinMaxDisThres;
}

/******************************************************************************
 * 函数名称: SDWCalVirtualDisByCoorTrans
 * 
 * 功能描述: 通过坐标系转换计算障碍物的纵向和横向距离，以便进行各个分区的距离显示
 * 
 * 输入参数:无 
 * 
 * 输处参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-06-26 13:47   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void SDWCalVirtualDisByCoorTrans(SDW_tenuSIDE_INDEX LenuSideIndex)
{
    uint8 i;
    float LfDisTemp;
    uint16 Lu16DisTemp;
    uint8 Lu8ObjValidFlag = 0;

    Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_1] = SDW_INVALID_DIS;
    Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_2] = SDW_INVALID_DIS;
    Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_3] = SDW_INVALID_DIS;
    Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_4] = SDW_INVALID_DIS;
    Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_5] = SDW_INVALID_DIS;
    Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_6] = SDW_INVALID_DIS;
    Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_7] = SDW_INVALID_DIS;
    Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_8] = SDW_INVALID_DIS;

    for(i = 0; i < GstrSDW_DisCarPara_Ram.u8ObjBufferMaxCnt; i++)
    {
        if(GstrObjCoorBuffer[LenuSideIndex][i].u8ValidFlag)
        {
            /* 将障碍物的世界坐标转换到此时此刻的车辆坐标  */
            /* P1点世界坐标不动，车辆坐标根据先平移后旋转的公式进行转换   */
            GstrObjCoorBuffer[LenuSideIndex][i].fObjInCraX  = (GstrObjCoorBuffer[LenuSideIndex][i].fPosX - GstrCarCoor.fPosX)*GstrCarCoor.fcosAngle +\
                                       (GstrObjCoorBuffer[LenuSideIndex][i].fPosY - GstrCarCoor.fPosY)*GstrCarCoor.fsinAngle;

            GstrObjCoorBuffer[LenuSideIndex][i].fObjInCraY  = (GstrObjCoorBuffer[LenuSideIndex][i].fPosY - GstrCarCoor.fPosY)*GstrCarCoor.fcosAngle -\
                                       (GstrObjCoorBuffer[LenuSideIndex][i].fPosX - GstrCarCoor.fPosX)*GstrCarCoor.fsinAngle;
            if(LenuSideIndex == LEFT_SIDE_OBJ)
            {
                /* 左边的障碍物X都必须大于1000mm，否则认为进入到车内，赋值无效值 */
                if(GstrObjCoorBuffer[LenuSideIndex][i].fObjInCraY < 1000)
                {
                    Lu8ObjValidFlag = 0;
                }
                else
                {
                    Lu8ObjValidFlag = 1;
                }
            }
            else 
            {
                /* 左边的障碍物X都必须小于-1000mm，否则认为进入到车内，赋值无效值 */
                if(GstrObjCoorBuffer[LenuSideIndex][i].fObjInCraY > -1000)
                {
                    Lu8ObjValidFlag = 0;
                }
                else
                {
                    Lu8ObjValidFlag = 1;
                }
            }
        }
        else
        {
            Lu8ObjValidFlag = 0;
        }

        /* 取对应区域的的最小值给到虚拟计算值,合计8个分区，起始位置3531，结束位置-691，总长4222，一段长527.75 */
        if(Lu8ObjValidFlag)
        {
            if((GstrObjCoorBuffer[LenuSideIndex][i].fObjInCraX <= GstrSDW_DisCarPara_Ram.LfFrontSideToRearAxleDis)\
                &&(GstrObjCoorBuffer[LenuSideIndex][i].fObjInCraX >= -GstrSDW_DisCarPara_Ram.LfRearSideToRearAxleDis))
            {
                LfDisTemp = ABS_VALUE(GstrObjCoorBuffer[LenuSideIndex][i].fObjInCraY);
                if(LfDisTemp > Vehicl_Geometry_Para_Ram.u16CarHalfWidth)
                {
                    LfDisTemp = LfDisTemp - Vehicl_Geometry_Para_Ram.u16CarHalfWidth;    /* 减去车的半宽  */
                    if(LfDisTemp < GstrSDW_DisCarPara_Ram.u16StartFollowDis)
                    {
                        Lu16DisTemp = (uint16)LfDisTemp;
                    }
                    else
                    {
                        Lu16DisTemp = SDW_INVALID_DIS;
                    }   
                }
                else
                {
                    Lu16DisTemp = SDW_INVALID_DIS;
                }
                
                if(GstrObjCoorBuffer[LenuSideIndex][i].fObjInCraX > GstrSDW_DisCarPara_Ram.LfArea1EndDis)
                {
                    if(Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_1] > Lu16DisTemp)
                    {
                        Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_1] = Lu16DisTemp;
                    }
                }
                else if(GstrObjCoorBuffer[LenuSideIndex][i].fObjInCraX > GstrSDW_DisCarPara_Ram.LfArea2EndDis)
                {
                    if(Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_2] > Lu16DisTemp)
                    {
                        Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_2] = Lu16DisTemp;
                    }
                }
                else if(GstrObjCoorBuffer[LenuSideIndex][i].fObjInCraX > GstrSDW_DisCarPara_Ram.LfArea3EndDis)
                {
                    if(Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_3] > Lu16DisTemp)
                    {
                        Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_3] = Lu16DisTemp;
                    }
                }
                else if(GstrObjCoorBuffer[LenuSideIndex][i].fObjInCraX > GstrSDW_DisCarPara_Ram.LfArea4EndDis)
                {
                    if(Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_4] > Lu16DisTemp)
                    {
                        Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_4] = Lu16DisTemp;
                    }
                }
                else if(GstrObjCoorBuffer[LenuSideIndex][i].fObjInCraX > GstrSDW_DisCarPara_Ram.LfArea5EndDis)
                {
                    if(Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_5] > Lu16DisTemp)
                    {
                        Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_5] = Lu16DisTemp;
                    }
                }
                else if(GstrObjCoorBuffer[LenuSideIndex][i].fObjInCraX > GstrSDW_DisCarPara_Ram.LfArea6EndDis)
                {
                    if(Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_6] > Lu16DisTemp)
                    {
                        Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_6] = Lu16DisTemp;
                    }
                }
                else if(GstrObjCoorBuffer[LenuSideIndex][i].fObjInCraX > GstrSDW_DisCarPara_Ram.LfArea7EndDis)
                {
                    if(Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_7] > Lu16DisTemp)
                    {
                        Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_7] = Lu16DisTemp;
                    }
                }
                else
                {
                    if(Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_8] > Lu16DisTemp)
                    {
                        Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_8] = Lu16DisTemp;
                    }
                }
            }
        }
    }

#if 1
	if(LenuSideIndex == LEFT_SIDE_OBJ)
	{
		if(Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_1] > GstrSDWSnsBufferData.u16RealTimeDis[0])
		{
			Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_1] = GstrSDWSnsBufferData.u16RealTimeDis[0];
		}

		if(Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_8] > GstrSDWSnsBufferData.u16RealTimeDis[2])
		{
			Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_8] = GstrSDWSnsBufferData.u16RealTimeDis[2];
		}
	}
	else
	{
		if(Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_1] > GstrSDWSnsBufferData.u16RealTimeDis[1])
		{
			Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_1] = GstrSDWSnsBufferData.u16RealTimeDis[1];
		}
		if(Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_8] > GstrSDWSnsBufferData.u16RealTimeDis[3])
		{
			Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_8] = GstrSDWSnsBufferData.u16RealTimeDis[3];
		}
	}
#endif
	
    for(i = 0; i < SIDE_AREA_NUM; i++)
    {
        if(Gu16SDW_VirtualDis[LenuSideIndex][i] <= GstrSDW_DisCarPara_Ram.u16StartFollowDis)
        {
            Gu16SDW_VirtualDis[LenuSideIndex][i] = Gu16SDW_VirtualDis[LenuSideIndex][i]/10;
        }
        else
        {
            Gu16SDW_VirtualDis[LenuSideIndex][i] = SDW_SIDE_NO_OBJ_DISTANCE_TO_CAN;
        }
    }
}




/******************************************************************
* 函数名称: SDWFindValidObjCoorInBuffer
*
* 功能描述: 记录障碍物的坐标
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 在过去车子行驶10cm内的5个Buffer中找到有效的障碍物及其探测瞬间的坐标
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
static void SDWFindValidObjCoorInBuffer(uint8 Lu8Ch)
{	
    uint8 i = 0;
    uint8 j = 0;
    uint8 Lu8ValidCnt = 0;
    uint8 Lu8MinObjIndex = 0xFF;
    //uint8 Lu8MinObjIndex = 0;
    uint16 Lu16MinDis = 0xFFFF;
    uint16 Lu16SecondMinDis = 0;
    uint16 Lu16DisAverage = 0;
    uint16 Lu16DisAverNoMax = 0; /*去除最大值后求的平均值 */
    uint16 Lu16MinMaxDisSub;

    uint16 Lu16MinMaxDisThres =0;
	uint16 tempData = 0;
	uint16 databuf[SIDE_SNS_BUFFER_CNT] = {0};
	
    /* 进入前先清除之前的数据 */
    ClearSDWValidObjCoor(Lu8Ch);
    
    for(j = 0; j < SIDE_SNS_BUFFER_CNT;j++)
    {
        if(GstrSDWSnsBufferData.u16SnsDis[Lu8Ch][j] < (GstrSDW_DisCarPara_Ram.u16StartFollowDis))
        {
            if(GstrSDWSnsBufferData.u16SnsDis[Lu8Ch][j] < Lu16MinDis)
            {
                Lu16MinDis = GstrSDWSnsBufferData.u16SnsDis[Lu8Ch][j];
                Lu8MinObjIndex = j;
            }
			databuf[j] = GstrSDWSnsBufferData.u16SnsDis[Lu8Ch][j];
			Lu8ValidCnt++;
        }
    }

	
    /* 又有效数据才处理  */
    if(Lu8ValidCnt > 0)
    {		
        if(Lu8ValidCnt >= 2)
        {
			for(i = 0;i < (Lu8ValidCnt - 1);i++)
			{
				for(j = 0;j < (Lu8ValidCnt - 1 - i);j++)
				{
					if(databuf[j] > databuf[j + 1])
					{
						tempData = databuf[j];
						databuf[j] = databuf[j + 1];
						databuf[j + 1] = tempData;
					}
				}
			}
			Lu16SecondMinDis = databuf[1];
				
			Lu16MinMaxDisSub = Lu16SecondMinDis - Lu16MinDis;
        
        	Lu16MinMaxDisThres =0;
		
            Lu16MinMaxDisThres = Lu16MinMaxDisThresOpt(Gu16SDW_Speed,Lu8ValidCnt);

            if(Lu16MinMaxDisSub > Lu16MinMaxDisThres)
            {
                ClearSDWValidObjCoor(Lu8Ch);
            }
            else
            {
                GstrSDWValidObjCoor.fPosX[Lu8Ch] = GstrSDWSnsBufferData.fSnsObjX[Lu8Ch][Lu8MinObjIndex]; 
                GstrSDWValidObjCoor.fPosY[Lu8Ch] = GstrSDWSnsBufferData.fSnsObjY[Lu8Ch][Lu8MinObjIndex]; 
                GstrSDWValidObjCoor.fCosAngle[Lu8Ch] = GstrSDWSnsBufferData.fSnsCosAngle[Lu8Ch][Lu8MinObjIndex]; 
                GstrSDWValidObjCoor.fSinAngle[Lu8Ch] = GstrSDWSnsBufferData.fSnsSinAngle[Lu8Ch][Lu8MinObjIndex]; 
                GstrSDWValidObjCoor.u16Dis[Lu8Ch] = Lu16MinDis; 
                GstrSDWValidObjCoor.u8ValidFlag[Lu8Ch] = 1;
        
            }

        }
        else 
        {
			#if 0
            GstrSDWValidObjCoor.fPosX[Lu8Ch] = GstrSDWSnsBufferData.fSnsObjX[Lu8Ch][Lu8MinObjIndex]; 
            GstrSDWValidObjCoor.fPosY[Lu8Ch] = GstrSDWSnsBufferData.fSnsObjY[Lu8Ch][Lu8MinObjIndex]; 
            GstrSDWValidObjCoor.fCosAngle[Lu8Ch] = GstrSDWSnsBufferData.fSnsCosAngle[Lu8Ch][Lu8MinObjIndex]; 
            GstrSDWValidObjCoor.fSinAngle[Lu8Ch] = GstrSDWSnsBufferData.fSnsSinAngle[Lu8Ch][Lu8MinObjIndex]; 
            GstrSDWValidObjCoor.u16Dis[Lu8Ch] = Lu16MinDis; 
            GstrSDWValidObjCoor.u8ValidFlag[Lu8Ch] = 1; 
			#else
			ClearSDWValidObjCoor(Lu8Ch);
			#endif
        }        
    }
	else
	{
		ClearSDWValidObjCoor(Lu8Ch);
	}   
}


/******************************************************************
* 函数名称: SDWRecordObjCoordinate
*
* 功能描述: 记录障碍物的坐标
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 队列遍历最多执行时间60us,入库最多情况下，执行该函数需要980us
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
static void SDWRecordObjCoordinate(void)
/* 采用探头坐标的方式 */
{
    float LfDisTemp;
    uint8 i;
    uint8 Lu8SnsCh;

    if(GstrSDW_SnsCoorBackup[SDW_FLS_CHANNEL].u8Move10cmFlg)
    {
        /* 左边队列数据往后排 */
        for(i = GstrSDW_DisCarPara_Ram.u8ObjBufferMaxCnt-1; i > 0;i--)
        {
            GstrObjCoorBuffer[LEFT_SIDE_OBJ][i].fPosX = GstrObjCoorBuffer[LEFT_SIDE_OBJ][i-1].fPosX;
            GstrObjCoorBuffer[LEFT_SIDE_OBJ][i].fPosY = GstrObjCoorBuffer[LEFT_SIDE_OBJ][i-1].fPosY;
            GstrObjCoorBuffer[LEFT_SIDE_OBJ][i].u8ValidFlag = GstrObjCoorBuffer[LEFT_SIDE_OBJ][i-1].u8ValidFlag;
        }

        Lu8SnsCh = SDW_FLS_CHANNEL;
        SDWFindValidObjCoorInBuffer(Lu8SnsCh);

        if(GstrSDWValidObjCoor.u8ValidFlag[Lu8SnsCh])
        {
            LfDisTemp = GstrSDWValidObjCoor.u16Dis[Lu8SnsCh];  
            /* 计算障碍物在世界坐标系中的位置 */
            GstrObjCoorBuffer[LEFT_SIDE_OBJ][0].fPosX = GstrSDWValidObjCoor.fPosX[Lu8SnsCh] + GstrSDW_DisCarPara_Ram.LfFrontSideToRearAxleDis*GstrSDWValidObjCoor.fCosAngle[Lu8SnsCh] - (GstrSDW_DisCarPara_Ram.LfFrontSideToCarCenter+ LfDisTemp)*GstrSDWValidObjCoor.fSinAngle[Lu8SnsCh];
            GstrObjCoorBuffer[LEFT_SIDE_OBJ][0].fPosY = GstrSDWValidObjCoor.fPosY[Lu8SnsCh] + GstrSDW_DisCarPara_Ram.LfFrontSideToRearAxleDis*GstrSDWValidObjCoor.fSinAngle[Lu8SnsCh] + (GstrSDW_DisCarPara_Ram.LfFrontSideToCarCenter+ LfDisTemp)*GstrSDWValidObjCoor.fCosAngle[Lu8SnsCh];
            GstrObjCoorBuffer[LEFT_SIDE_OBJ][0].u8ValidFlag = 1;
        }
        else
        {
            GstrObjCoorBuffer[LEFT_SIDE_OBJ][0].fPosX = 0;
            GstrObjCoorBuffer[LEFT_SIDE_OBJ][0].fPosY = 0;
            GstrObjCoorBuffer[LEFT_SIDE_OBJ][0].u8ValidFlag = 0;
        }

        GstrSDW_SnsCoorBackup[SDW_FLS_CHANNEL].u8Move10cmFlg = 0;
    }
    else if(GstrSDW_SnsCoorBackup[SDW_RLS_CHANNEL].u8Move10cmFlg)
    {
        /* 队列数据往前排 */
        for(i = 0; i < GstrSDW_DisCarPara_Ram.u8ObjBufferMaxCnt-1;i++)
        {
            GstrObjCoorBuffer[LEFT_SIDE_OBJ][i].fPosX = GstrObjCoorBuffer[LEFT_SIDE_OBJ][i+1].fPosX;
            GstrObjCoorBuffer[LEFT_SIDE_OBJ][i].fPosY = GstrObjCoorBuffer[LEFT_SIDE_OBJ][i+1].fPosY;
            GstrObjCoorBuffer[LEFT_SIDE_OBJ][i].u8ValidFlag = GstrObjCoorBuffer[LEFT_SIDE_OBJ][i+1].u8ValidFlag;
        }

        Lu8SnsCh = SDW_RLS_CHANNEL;
        SDWFindValidObjCoorInBuffer(Lu8SnsCh);

        if(GstrSDWValidObjCoor.u8ValidFlag[Lu8SnsCh])
        {        
            LfDisTemp = GstrSDWValidObjCoor.u16Dis[Lu8SnsCh];
            GstrObjCoorBuffer[LEFT_SIDE_OBJ][GstrSDW_DisCarPara_Ram.u8ObjBufferMaxCnt-1].fPosX = GstrSDWValidObjCoor.fPosX[Lu8SnsCh] - GstrSDW_DisCarPara_Ram.LfRearSideToRearAxleDis*GstrSDWValidObjCoor.fCosAngle[Lu8SnsCh] - (GstrSDW_DisCarPara_Ram.LfRearSideToCarCenter+ LfDisTemp)*GstrSDWValidObjCoor.fSinAngle[Lu8SnsCh];
            GstrObjCoorBuffer[LEFT_SIDE_OBJ][GstrSDW_DisCarPara_Ram.u8ObjBufferMaxCnt-1].fPosY = GstrSDWValidObjCoor.fPosY[Lu8SnsCh] - GstrSDW_DisCarPara_Ram.LfRearSideToRearAxleDis*GstrSDWValidObjCoor.fSinAngle[Lu8SnsCh] + (GstrSDW_DisCarPara_Ram.LfRearSideToCarCenter+ LfDisTemp)*GstrSDWValidObjCoor.fCosAngle[Lu8SnsCh];
            GstrObjCoorBuffer[LEFT_SIDE_OBJ][GstrSDW_DisCarPara_Ram.u8ObjBufferMaxCnt-1].u8ValidFlag = 1;
        }
        else
        {
            GstrObjCoorBuffer[LEFT_SIDE_OBJ][GstrSDW_DisCarPara_Ram.u8ObjBufferMaxCnt-1].fPosX = 0;
            GstrObjCoorBuffer[LEFT_SIDE_OBJ][GstrSDW_DisCarPara_Ram.u8ObjBufferMaxCnt-1].fPosY = 0;
            GstrObjCoorBuffer[LEFT_SIDE_OBJ][GstrSDW_DisCarPara_Ram.u8ObjBufferMaxCnt-1].u8ValidFlag = 0;
        }
        GstrSDW_SnsCoorBackup[SDW_RLS_CHANNEL].u8Move10cmFlg = 0;
    }

    if(GstrSDW_SnsCoorBackup[SDW_FRS_CHANNEL].u8Move10cmFlg)
    {
        /* 右边队列数据往后排 */
        for(i = GstrSDW_DisCarPara_Ram.u8ObjBufferMaxCnt-1; i > 0;i--)
        {
            GstrObjCoorBuffer[RIGHT_SIDE_OBJ][i].fPosX = GstrObjCoorBuffer[RIGHT_SIDE_OBJ][i-1].fPosX;
            GstrObjCoorBuffer[RIGHT_SIDE_OBJ][i].fPosY = GstrObjCoorBuffer[RIGHT_SIDE_OBJ][i-1].fPosY;
            GstrObjCoorBuffer[RIGHT_SIDE_OBJ][i].u8ValidFlag = GstrObjCoorBuffer[RIGHT_SIDE_OBJ][i-1].u8ValidFlag;

            /* 不需要判断障碍物是否在矩形内，后面计算坐标若是在车内则直接删除，但始终不删除世界坐标系的障碍物 */
        }
        
        Lu8SnsCh = SDW_FRS_CHANNEL;
        SDWFindValidObjCoorInBuffer(Lu8SnsCh);
        if(GstrSDWValidObjCoor.u8ValidFlag[Lu8SnsCh])
        { 
            LfDisTemp = GstrSDWValidObjCoor.u16Dis[Lu8SnsCh];
            GstrObjCoorBuffer[RIGHT_SIDE_OBJ][0].fPosX = GstrSDWValidObjCoor.fPosX[Lu8SnsCh] + GstrSDW_DisCarPara_Ram.LfFrontSideToRearAxleDis*GstrSDWValidObjCoor.fCosAngle[Lu8SnsCh] + (GstrSDW_DisCarPara_Ram.LfFrontSideToCarCenter+ LfDisTemp)*GstrSDWValidObjCoor.fSinAngle[Lu8SnsCh];
            GstrObjCoorBuffer[RIGHT_SIDE_OBJ][0].fPosY = GstrSDWValidObjCoor.fPosY[Lu8SnsCh] + GstrSDW_DisCarPara_Ram.LfFrontSideToRearAxleDis*GstrSDWValidObjCoor.fSinAngle[Lu8SnsCh] - (GstrSDW_DisCarPara_Ram.LfFrontSideToCarCenter+ LfDisTemp)*GstrSDWValidObjCoor.fCosAngle[Lu8SnsCh];
            GstrObjCoorBuffer[RIGHT_SIDE_OBJ][0].u8ValidFlag = 1; 
        }
        else
        {
            GstrObjCoorBuffer[RIGHT_SIDE_OBJ][0].fPosX = 0;
            GstrObjCoorBuffer[RIGHT_SIDE_OBJ][0].fPosY = 0;
            GstrObjCoorBuffer[RIGHT_SIDE_OBJ][0].u8ValidFlag = 0;            
        }
        GstrSDW_SnsCoorBackup[SDW_FRS_CHANNEL].u8Move10cmFlg = 0;
    }
    else if(GstrSDW_SnsCoorBackup[SDW_RRS_CHANNEL].u8Move10cmFlg)
    {
        /* 队列数据往前排 */
        for(i = 0; i < GstrSDW_DisCarPara_Ram.u8ObjBufferMaxCnt-1;i++)
        {
            GstrObjCoorBuffer[RIGHT_SIDE_OBJ][i].fPosX = GstrObjCoorBuffer[RIGHT_SIDE_OBJ][i+1].fPosX;
            GstrObjCoorBuffer[RIGHT_SIDE_OBJ][i].fPosY = GstrObjCoorBuffer[RIGHT_SIDE_OBJ][i+1].fPosY;
            GstrObjCoorBuffer[RIGHT_SIDE_OBJ][i].u8ValidFlag = GstrObjCoorBuffer[RIGHT_SIDE_OBJ][i+1].u8ValidFlag;
        }

        Lu8SnsCh = SDW_RRS_CHANNEL;
        SDWFindValidObjCoorInBuffer(Lu8SnsCh);
        if(GstrSDWValidObjCoor.u8ValidFlag[Lu8SnsCh])
        {        
            LfDisTemp = GstrSDWValidObjCoor.u16Dis[Lu8SnsCh];
            GstrObjCoorBuffer[RIGHT_SIDE_OBJ][GstrSDW_DisCarPara_Ram.u8ObjBufferMaxCnt-1].fPosX = GstrSDWValidObjCoor.fPosX[Lu8SnsCh] - GstrSDW_DisCarPara_Ram.LfRearSideToRearAxleDis*GstrSDWValidObjCoor.fCosAngle[Lu8SnsCh] + (GstrSDW_DisCarPara_Ram.LfRearSideToCarCenter+ LfDisTemp)*GstrSDWValidObjCoor.fSinAngle[Lu8SnsCh];
            GstrObjCoorBuffer[RIGHT_SIDE_OBJ][GstrSDW_DisCarPara_Ram.u8ObjBufferMaxCnt-1].fPosY = GstrSDWValidObjCoor.fPosY[Lu8SnsCh] - GstrSDW_DisCarPara_Ram.LfRearSideToRearAxleDis*GstrSDWValidObjCoor.fSinAngle[Lu8SnsCh] - (GstrSDW_DisCarPara_Ram.LfRearSideToCarCenter+ LfDisTemp)*GstrSDWValidObjCoor.fCosAngle[Lu8SnsCh];
            GstrObjCoorBuffer[RIGHT_SIDE_OBJ][GstrSDW_DisCarPara_Ram.u8ObjBufferMaxCnt-1].u8ValidFlag = 1;
        }
        else
        {
            GstrObjCoorBuffer[RIGHT_SIDE_OBJ][GstrSDW_DisCarPara_Ram.u8ObjBufferMaxCnt-1].fPosX = 0;
            GstrObjCoorBuffer[RIGHT_SIDE_OBJ][GstrSDW_DisCarPara_Ram.u8ObjBufferMaxCnt-1].fPosY = 0;
            GstrObjCoorBuffer[RIGHT_SIDE_OBJ][GstrSDW_DisCarPara_Ram.u8ObjBufferMaxCnt-1].u8ValidFlag = 0;
        }
        GstrSDW_SnsCoorBackup[SDW_RRS_CHANNEL].u8Move10cmFlg = 0;
    }
}

/******************************************************************
* 函数名称: SDWCalMoveDisForPark
*
* 功能描述: OPS中更新车位移动距离
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 无
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
static void SDWCalMoveDisForPark(void)
{
    float LfCarDisTemp;
    /* 发生突然计算两点坐标变大的可能原因:
       1.切换坐标时，SDW坐标已经初始化，但是整车坐标模块未更新 
       2. 浮点数做减法出现一个异常很大的值 
       若是发生了，则可以在第一次做减法的情况下进行处理，在实车车速低于15km/h时，20ms车子移动83mm，所以单次进入该函数计算出来的 LfCarDisTemp不会超过10cm*/
    LfCarDisTemp = PubAI_CalTwoPointDis(GstrSDWCurCarPointData.fX,GstrSDWCurCarPointData.fY,GstrCarCoor.fPosX,GstrCarCoor.fPosY);
    /* 处理坐标切换过程中的坐标不匹配问题 */
    if(LfCarDisTemp > 150)
    {
        SDW_DEBUG_PRINTF("Car Dis More than 150,Dis:%d,Last_X:%ld,Y:%ld,Current_X:%ld,Y:%ld\r\n",(uint16)LfCarDisTemp,(sint32)GstrCarCoor.fPosX,\
            (sint32)GstrCarCoor.fPosY,(sint32)GstrSDWCurCarPointData.fX,(sint32)GstrSDWCurCarPointData.fY);
        LfCarDisTemp = 0;
    }

    GstrCarCoor.fPosX = GstrSDWCurCarPointData.fX;
    GstrCarCoor.fPosY = GstrSDWCurCarPointData.fY;
    GstrCarCoor.fPosAngle = GstrSDWCurCarPointData.fAngle;
    GstrCarCoor.fsinAngle = sinf(GstrCarCoor.fPosAngle);
    GstrCarCoor.fcosAngle = cosf(GstrCarCoor.fPosAngle);



    if((GstrSDW_InputWheel.enuRLWheelDir == SDW_WHEEL_DIR_FORWARD)||(GstrSDW_InputWheel.enuRRWheelDir == SDW_WHEEL_DIR_FORWARD)||\
        ((GenuSDW_Gear == CAN_CAR_GEAR_D)&&(GstrSDW_InputWheel.enuRLWheelDir != SDW_WHEEL_DIR_BACKWARD)&&(GstrSDW_InputWheel.enuRRWheelDir != SDW_WHEEL_DIR_BACKWARD)))
    {
        GstrCarCoor.fFrontMoveDis += LfCarDisTemp;
        GstrCarCoor.fRearMoveDis = 0;
        if((GstrCarCoor.fFrontMoveDis >= SDW_OBJ_RECORD_DIS)&&(!GstrCarCoor.u8FrontMoveDis20cmFlag))
        {
            GstrCarCoor.u8FrontMoveDis20cmFlag = 1;
            GstrCarCoor.fFrontMoveDis -= SDW_OBJ_RECORD_DIS;
        }
        GstrCarCoor.enuCarMovingStatus = SDW_WHEEL_DIR_FORWARD;
    }
    else if((GstrSDW_InputWheel.enuRLWheelDir == SDW_WHEEL_DIR_BACKWARD)||(GstrSDW_InputWheel.enuRRWheelDir == SDW_WHEEL_DIR_BACKWARD)||\
        ((GenuSDW_Gear == CAN_CAR_GEAR_R)&&(GstrSDW_InputWheel.enuRLWheelDir != SDW_WHEEL_DIR_FORWARD)&&(GstrSDW_InputWheel.enuRRWheelDir != SDW_WHEEL_DIR_FORWARD)))
    {
        GstrCarCoor.fRearMoveDis += LfCarDisTemp;
        GstrCarCoor.fFrontMoveDis = 0;
        if((GstrCarCoor.fRearMoveDis >= SDW_OBJ_RECORD_DIS)&&(!GstrCarCoor.u8RearMoveDis20cmFlag))
        {
            GstrCarCoor.u8RearMoveDis20cmFlag = 1;
            GstrCarCoor.fRearMoveDis -= SDW_OBJ_RECORD_DIS;
        }
        GstrCarCoor.enuCarMovingStatus = SDW_WHEEL_DIR_BACKWARD;
    }
    else
    { 

    }
    
    /* 通过移动距离判定车子是静止还是运动状态*/
    if(LfCarDisTemp < 1)
    {
        if(GstrCarCoor.u16CarContinueStopCnt > SDW_STOP_CLEAR_CNT) /* 参照一轨，取消静止延时消失策略，但此处用于后续计算距离是否需要更新 */
        {
            /* 临时屏蔽静止60S 消失策略 */
            Gu8CarStop60S_ClearFlag = 1;
            GstrCarCoor.enuCarMovingStatus = SDW_WHEEL_DIR_STOP;
        }
        else if(GstrCarCoor.u16CarContinueStopCnt > SDW_STOP_WAIT_CNT) /* 参照一轨，取消静止延时消失策略，但此处用于后续计算距离是否需要更新 */
        {
            GstrCarCoor.enuCarMovingStatus = SDW_WHEEL_DIR_STOP;
            GstrCarCoor.u16CarContinueStopCnt++;
        }
        else
        {
            GstrCarCoor.u16CarContinueStopCnt++;
        }
    }
    else
    {
        GstrCarCoor.u16CarContinueStopCnt = 0;
        Gu8CarStop60S_ClearFlag = 0;
    }    
}

/******************************************************************
* 函数名称: SDWCalSnsMoveDis
*
* 功能描述: 计算侧边四个探头移动的距离
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 无
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
void SDWCalSnsMoveDis(void)
{
    uint8 i;
    float LfCarDisTemp[4];
    
    for(i = 0; i < 4; i++)
    {
        LfCarDisTemp[i] = PubAI_CalTwoPointDis(GstrSDW_SnsCoor[i].fPosX,GstrSDW_SnsCoor[i].fPosY,GstrSDW_SnsCoorBackup[i].fPosX,GstrSDW_SnsCoorBackup[i].fPosY);
        /* 处理坐标切换过程中的坐标不匹配问题 */
        if(LfCarDisTemp[i] > 150)
        {  
            SDW_DEBUG_PRINTF("Sns Dis More than 150,Ch:%d,Dis:%d,Last_X:%ld,Y:%ld,Current_X:%ld,Y:%ld\r\n",i,(uint16)LfCarDisTemp[i],(sint32)GstrSDW_SnsCoorBackup[i].fPosX,\
                (sint32)GstrSDW_SnsCoorBackup[i].fPosY,(sint32)GstrSDW_SnsCoor[i].fPosX,(sint32)GstrSDW_SnsCoor[i].fPosY);
            LfCarDisTemp[i] = 0;
        }
    }

    if((GstrSDW_InputWheel.enuRLWheelDir == SDW_WHEEL_DIR_FORWARD)||(GstrSDW_InputWheel.enuRRWheelDir == SDW_WHEEL_DIR_FORWARD)||\
        ((GenuSDW_Gear == CAN_CAR_GEAR_D)&&(GstrSDW_InputWheel.enuRLWheelDir != SDW_WHEEL_DIR_BACKWARD)&&(GstrSDW_InputWheel.enuRRWheelDir != SDW_WHEEL_DIR_BACKWARD)))
    {
        GstrSDW_SnsCoorBackup[0].fMovingDis += LfCarDisTemp[0];
        GstrSDW_SnsCoorBackup[1].fMovingDis += LfCarDisTemp[1];
        GstrSDW_SnsCoorBackup[2].fMovingDis = 0;
        GstrSDW_SnsCoorBackup[3].fMovingDis = 0;
        
        if((GstrSDW_SnsCoorBackup[0].fMovingDis >= SDW_OBJ_RECORD_DIS)&&(!GstrSDW_SnsCoorBackup[0].u8Move10cmFlg))
        {
#if 0
            SDW_DEBUG_PRINTF("0_MovDisDis:%d,Car_X:%ld,Car_Y:%ld\r\n",(uint16)GstrSDW_SnsCoorBackup[0].fMovingDis,\
            (sint32)GstrCarCoor.fPosX,(sint32)GstrCarCoor.fPosY);
#endif
            GstrSDW_SnsCoorBackup[0].u8Move10cmFlg = 1;
            GstrSDW_SnsCoorBackup[0].fMovingDis -= SDW_OBJ_RECORD_DIS;
        }

        if((GstrSDW_SnsCoorBackup[1].fMovingDis >= SDW_OBJ_RECORD_DIS)&&(!GstrSDW_SnsCoorBackup[1].u8Move10cmFlg))
        {
            GstrSDW_SnsCoorBackup[1].u8Move10cmFlg = 1;
            GstrSDW_SnsCoorBackup[1].fMovingDis -= SDW_OBJ_RECORD_DIS;
        } 
        
    }
    else if((GstrSDW_InputWheel.enuRLWheelDir == SDW_WHEEL_DIR_BACKWARD)||(GstrSDW_InputWheel.enuRRWheelDir == SDW_WHEEL_DIR_BACKWARD)||\
        ((GenuSDW_Gear == CAN_CAR_GEAR_R)&&(GstrSDW_InputWheel.enuRLWheelDir != SDW_WHEEL_DIR_FORWARD)&&(GstrSDW_InputWheel.enuRRWheelDir != SDW_WHEEL_DIR_FORWARD)))
    {
        GstrSDW_SnsCoorBackup[0].fMovingDis = 0;
        GstrSDW_SnsCoorBackup[1].fMovingDis = 0;
        GstrSDW_SnsCoorBackup[2].fMovingDis += LfCarDisTemp[2];
        GstrSDW_SnsCoorBackup[3].fMovingDis += LfCarDisTemp[3];
        
        if((GstrSDW_SnsCoorBackup[2].fMovingDis >= SDW_OBJ_RECORD_DIS)&&(!GstrSDW_SnsCoorBackup[2].u8Move10cmFlg))
        {
            
            GstrSDW_SnsCoorBackup[2].u8Move10cmFlg = 1;
            GstrSDW_SnsCoorBackup[2].fMovingDis -= SDW_OBJ_RECORD_DIS;
        }

        if((GstrSDW_SnsCoorBackup[3].fMovingDis >= SDW_OBJ_RECORD_DIS)&&(!GstrSDW_SnsCoorBackup[3].u8Move10cmFlg))
        {
            GstrSDW_SnsCoorBackup[3].u8Move10cmFlg = 1;
            GstrSDW_SnsCoorBackup[3].fMovingDis -= SDW_OBJ_RECORD_DIS;
        }
    }
    else
    { 

    }

    /* 备份本次的探头坐标 */
    for(i = 0; i < 4; i++)
    {
        GstrSDW_SnsCoorBackup[i].fPosX = GstrSDW_SnsCoor[i].fPosX;
        GstrSDW_SnsCoorBackup[i].fPosY = GstrSDW_SnsCoor[i].fPosY;
    }
}



/******************************************************************
* 函数名称: SDWCalSnsCoordinate
*
* 功能描述: 计算侧边四个探头位置的坐标
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 无
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
static void SDWCalSnsCoordinate(void)
{ 
    /*  分别计算FLS FRS RLS RRS 四个点的坐标 */
    GstrSDW_SnsCoor[SDW_FLS_CHANNEL].fPosX = GstrCarCoor.fPosX + GstrSDW_DisCarPara_Ram.LfFrontSideToRearAxleDis*GstrCarCoor.fcosAngle - GstrSDW_DisCarPara_Ram.LfFrontSideToCarCenter*GstrCarCoor.fsinAngle;
    GstrSDW_SnsCoor[SDW_FLS_CHANNEL].fPosY = GstrCarCoor.fPosY + GstrSDW_DisCarPara_Ram.LfFrontSideToRearAxleDis*GstrCarCoor.fsinAngle + GstrSDW_DisCarPara_Ram.LfFrontSideToCarCenter*GstrCarCoor.fcosAngle;

    GstrSDW_SnsCoor[SDW_RLS_CHANNEL].fPosX = GstrCarCoor.fPosX - GstrSDW_DisCarPara_Ram.LfRearSideToRearAxleDis*GstrCarCoor.fcosAngle - GstrSDW_DisCarPara_Ram.LfRearSideToCarCenter*GstrCarCoor.fsinAngle;
    GstrSDW_SnsCoor[SDW_RLS_CHANNEL].fPosY = GstrCarCoor.fPosY - GstrSDW_DisCarPara_Ram.LfRearSideToRearAxleDis*GstrCarCoor.fsinAngle + GstrSDW_DisCarPara_Ram.LfRearSideToCarCenter*GstrCarCoor.fcosAngle;

    GstrSDW_SnsCoor[SDW_FRS_CHANNEL].fPosX = GstrCarCoor.fPosX + GstrSDW_DisCarPara_Ram.LfFrontSideToRearAxleDis*GstrCarCoor.fcosAngle + GstrSDW_DisCarPara_Ram.LfFrontSideToCarCenter*GstrCarCoor.fsinAngle;
    GstrSDW_SnsCoor[SDW_FRS_CHANNEL].fPosY = GstrCarCoor.fPosY + GstrSDW_DisCarPara_Ram.LfFrontSideToRearAxleDis*GstrCarCoor.fsinAngle - GstrSDW_DisCarPara_Ram.LfFrontSideToCarCenter*GstrCarCoor.fcosAngle;

    GstrSDW_SnsCoor[SDW_RRS_CHANNEL].fPosX = GstrCarCoor.fPosX - GstrSDW_DisCarPara_Ram.LfRearSideToRearAxleDis*GstrCarCoor.fcosAngle + GstrSDW_DisCarPara_Ram.LfRearSideToCarCenter*GstrCarCoor.fsinAngle;
    GstrSDW_SnsCoor[SDW_RRS_CHANNEL].fPosY = GstrCarCoor.fPosY - GstrSDW_DisCarPara_Ram.LfRearSideToRearAxleDis*GstrCarCoor.fsinAngle - GstrSDW_DisCarPara_Ram.LfRearSideToCarCenter*GstrCarCoor.fcosAngle;
}

/******************************************************************
* 函数名称: SDWCoordinateRecord
*
* 功能描述: OPS坐标记录,实时记录后轴中心点的坐标
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 无
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
static void SDWCoordinateRecord(void)
{
    SDWCalMoveDisForPark();
    SDWCalSnsCoordinate();
    SDWCalSnsMoveDis();

}

/******************************************************************************
 * 函数名称: SDWRealTimeDisUpdate
 * 
 * 功能描述: SDW 实时距离更新
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-04-20 14:56   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void SDWRealTimeDisUpdate(SDW_tenuSIDE_INDEX LenuSideIndex)
{
#if 1
    if(LenuSideIndex == LEFT_SIDE_OBJ)
    {
        if(GstrSDWSnsBufferData.u16RealTimeDis[0] <= GstrSDW_DisCarPara_Ram.u16StartFollowDis)
        {
            Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_1] = GstrSDWSnsBufferData.u16RealTimeDis[0]/10;
        }
        else
        {
            Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_1] = SDW_SIDE_NO_OBJ_DISTANCE_TO_CAN;
        }

        if(GstrSDWSnsBufferData.u16RealTimeDis[2] <= GstrSDW_DisCarPara_Ram.u16StartFollowDis)
        {
            Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_8] = GstrSDWSnsBufferData.u16RealTimeDis[2]/10;
        }
        else
        {
            Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_8] = SDW_SIDE_NO_OBJ_DISTANCE_TO_CAN;
        }
    }
    else
    {
        if(GstrSDWSnsBufferData.u16RealTimeDis[1] <= GstrSDW_DisCarPara_Ram.u16StartFollowDis)
        {
            Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_1] = GstrSDWSnsBufferData.u16RealTimeDis[1]/10;
        }
        else
        {
            Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_1] = SDW_SIDE_NO_OBJ_DISTANCE_TO_CAN;
        }

        if(GstrSDWSnsBufferData.u16RealTimeDis[3] <= GstrSDW_DisCarPara_Ram.u16StartFollowDis)
        {
            Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_8] = GstrSDWSnsBufferData.u16RealTimeDis[3]/10;
        }
        else
        {
            Gu16SDW_VirtualDis[LenuSideIndex][SDW_AREA_8] = SDW_SIDE_NO_OBJ_DISTANCE_TO_CAN;
        }
    }
#endif
}



/******************************************************************************/
/******************************************************************************/
/******************************* Public Function ******************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************
* 函数名称: SDWDataInit
*
* 功能描述: SDW坐标数据初始化
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 无
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
void SDWDataInit(void)
{
	SDW_CalibPara_Config_Init();
    SDWOriginalDataInit();
    SDWWheelDataInit();
    SDWObjBufferInit();
    SDWSideVirtualDisInit();
    SDWCarCoorDataInit();
    SDWSnsCoorInit();
    ClearSidsSnsBufferData();
    SDWValidObjCoorInit();
    Gu8CarStop60S_ClearFlag = 0;
    u8FristFlg =0;
}


/******************************************************************
* 函数名称: SDWAlgorithm_Task
*
* 功能描述: 侧边虚拟障碍物计算
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明:
*
* 修改日期      版本号      修改人      修改内容
**********************************************************************/
void SDWAlgorithm_Task(void)
{
    static uint8 Lu8SDW_DataInit = 0;
    GetSDW_CAN_Signal();
    GetSDW_WorkStatus();
    if(GenuSDWWorkStatus == SDW_OPEN)
    {
        GetSDWCurCarPointData();
        SDWCoordinateRecord();
        SDWRecordObjCoordinate();

        if(Gu8CarStop60S_ClearFlag)
        {
            if(!Lu8SDW_DataInit)
            {
                Lu8SDW_DataInit = 1;
                SDWObjBufferInit();
                SDWObjBufferNoObj();
            }
            
            SDWRealTimeDisUpdate(LEFT_SIDE_OBJ);
            SDWRealTimeDisUpdate(RIGHT_SIDE_OBJ);
        }
        else
        {
            Lu8SDW_DataInit = 0;
            /* 参照竞品，取消实时显示;车辆静止无需更新计算，降低负载 */
            if(SDW_WHEEL_DIR_STOP != GstrCarCoor.enuCarMovingStatus)
            {
                SDWCalVirtualDisByCoorTrans(LEFT_SIDE_OBJ);
                SDWCalVirtualDisByCoorTrans(RIGHT_SIDE_OBJ);
            }
            else
            {
                SDWRealTimeDisUpdate(LEFT_SIDE_OBJ);
                SDWRealTimeDisUpdate(RIGHT_SIDE_OBJ);
            }
            
        }
    }
    else
    {
        if(!Lu8SDW_DataInit)
        {
            Lu8SDW_DataInit = 1;
            SDWDataInit();
        }
    }  
}

/******************************************************************
* 函数名称: ReadSDWSideVirtualDis
*
* 功能描述: 提供对外输出的虚拟计算距离
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
uint16 ReturnSDWSideVirtualDis(SDW_tenuSIDE_INDEX LenuSideIndex,SDW_tenuSIDE_AREA LenuSideArea)
{
    return Gu16SDW_VirtualDis[LenuSideIndex][LenuSideArea];
}

#endif/* end */


