/******************************************************************************
 * @file      RdumRdusCrm.h
 * @brief     安森美超声波探头CRM命令定义
 * <AUTHOR>
 * @date      2025-05-13
 * @note
 *****************************************************************************/

#ifndef __RDUM_RDUS_CRM_H__
#define __RDUM_RDUS_CRM_H__
/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "types.h"
#include "SpiPayloadDef.h"
#include "RdumRdusAddr.h"
#include "SpiCmd.h"
#include "RdumRdusPageIndex.h"

/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/
#define CRM_MAX_COMMANDS 5                                      /* 最大CRM命令数 */
#define CRM_COMMAND_SIZE 4                                      /*每个CRM命令可以存储4个字节*/
#define CRM_MAX_DATA_SIZE (CRM_MAX_COMMANDS * CRM_COMMAND_SIZE) /* 最大CRM数据大小 (5 * 4 bytes) */

/******************************************************************************
 * @Type Definitions
 *****************************************************************************/

/* CRM命令映射配置 */
typedef struct
{
    uint8 CommandCount;               /* 命令数量 */
    uint8 TotalDataSize;              /* 总数据大小 */
    uint8 CrmData[CRM_MAX_DATA_SIZE]; /* CRM数据 */
} CrmMapConfig_t;

/* TX_DATA映射结构 */
typedef struct
{
    SpiPayloadData_t TxData0;        /* TX_DATA0 (地址0x03) */
    SpiPayloadData_t TxData1;        /* TX_DATA1 (地址0x04) */
    SpiPayloadData_t TxData2;        /* TX_DATA2 (地址0x05) */
    SpiPayloadData_t TxData3;        /* TX_DATA3 (地址0x06) */
    SpiPayloadData_t TxData4;        /* TX_DATA4 (地址0x07) */
} TxDataMapping_t;

/* CRM命令类型 */
typedef enum
{
    CRM_TYPE_SOFTWARE_RESET = CRM_CMD_SOFTWARE_RESET,           /* 软件复位 */
    CRM_TYPE_ADDRESS_DISCOVERY = CRM_CMD_ADDRESS_DISCOVERY,     /* 地址发现 */
    CRM_TYPE_WRITE_BLOCK = CRM_CMD_WRITE_BLOCK,                 /* 块写入 */
    CRM_TYPE_WRITE_PIPELINED = CRM_CMD_WRITE_PIPELINED,         /* 流水线写入 */
    CRM_TYPE_WRITE = CRM_CMD_WRITE,                             /* 写入 */
    CRM_TYPE_WRITE_8BIT = CRM_CMD_WRITE_8BIT,                   /* 8位写入 */
    CRM_TYPE_WRITE_READ_POINTERS = CRM_CMD_WRITE_READ_POINTERS, /* 写读指针 */
    CRM_TYPE_READ = CRM_CMD_READ,                               /* 读取 */
    CRM_TYPE_PROGRAM_MEMORY = CRM_CMD_PROGRAM_MEMORY,           /* 编程存储器 */
    CRM_TYPE_START_MEASUREMENT = CRM_CMD_START_MEASUREMENT,     /* 开始测量 */
    CRM_TYPE_PIPELINE_WRITE = CRM_CMD_WRITE_PIPELINED,          /* 流水线写入 */
} RdumRdusCrmType_t;

/* PDCM读更新模式 */
typedef enum
{
    PDCM_UPDATE_DO_NOT_MODIFY,     /* 不修改 */
    PDCM_UPDATE_DO_NOT_MODIFY_TOO, /* 不修改 */
    PDCM_UPDATE_NEW_PAGE,          /* 读新页面，索引不修改 */
    PDCM_UPDATE_NEW_PAGE_INDEX,    /* 读新页面，索引为0 */
} RdumRdusPdcmUpdateMode_t;

/* CRM写更新模式 */
typedef enum
{
    CRM_UPDATE_DO_NOT_MODIFY,       /* 不修改 */
    CRM_UPDATE_NEW,                 /* 写入新CRM页面和索引 */
} RdumRdusCrmUpdateMode_t;

/* 软件复位帧 (CRM命令2) */
typedef union
{
    uint8 ResetData[CRM_COMMAND_SIZE];
    struct
    {
        uint8 Command : 4; /* [27:24] 命令代码 - 0x2h (SW_RST) 备注：unlock_sw_rst需要设置为1解锁才能使用软件复位*/
        uint8 Sid : 4;     /* [31:28] 从机ID */

        uint8 Data[2];              /* [23:8] 16位数据 - 0x0000h */
        uint8 Crc;                  /* [7:0] 8位CRC校验，保护[31:8]位 */
    } Bit;
} RdumRdusCrm_ResetFrame_t;

/* 地址发现帧 (CRM命令3) */
typedef union
{
    uint8 DiscoveryData[CRM_COMMAND_SIZE];
    struct
    {
        uint8 Command : 4; /* [27:24] 命令代码 - 0x3h (DISCOVER) */
        uint8 Sid : 4;     /* [31:28] 从机ID - 0xFh (广播) */

        uint8 Data[2];              /* [23:8] 16位数据 - 0x0000h */
        uint8 Crc;                  /* [7:0] 8位CRC校验，保护[31:8]位 */
    } Bit;
} RdumRdusCrm_DiscoveryFrame_t;

/* 块写入帧 (CRM命令4) */
typedef union
{
    uint8 WriteBlockData[14];
    struct
    {
        uint8 Command : 4; /* [107:104] 命令代码 - 0x4h (WRITE_BLK_P) */
        uint8 Sid : 4;     /* [111:108] 从机ID */

        uint8 Data[12];             /* [103:8] 6x16位数据，写入内存 */
        uint8 Crc;                  /* [7:0] 8位CRC校验，保护[MSB:8]位，CRC初始值为0xFF */
    } Bit;
} RdumRdusCrm_WriteBlockFrame_t;

/* 流水线写入帧 (CRM命令5) */
typedef union
{
    uint8 WritePipelinedData[CRM_COMMAND_SIZE];
    struct
    {
        uint8 Command : 4; /* [27:24] 命令代码 - 0x5h (WRITE_P) */
        uint8 Sid : 4;     /* [31:28] 从机ID */

        uint8 Data[2];              /* [23:8] 16位数据，写入内存 */
        uint8 Crc;                  /* [7:0] 8位CRC校验，保护[31:8]位 */
    } Bit;
} RdumRdusCrm_WritePipelinedFrame_t;

/* 写入帧 (CRM命令6) */
typedef union
{
    uint8 WriteData[CRM_COMMAND_SIZE];
    struct
    {
        uint8 Command : 4; /* [27:24] 命令代码 - 0x6h (WRITE) */
        uint8 Sid : 4;     /* [31:28] 从机ID */

        uint8 Data[2];              /* [23:8] 16位数据，写入内存 */
        uint8 Crc;                  /* [7:0] 8位CRC校验，保护[31:8]位 */
    } Bit;
} RdumRdusCrm_WriteFrame_t;

/* 8位写入帧 (CRM命令7) */
typedef union
{
    uint8 Write8BitData[CRM_COMMAND_SIZE];
    struct
    {
        uint8 Command : 4; /* [27:24] 命令代码 - 0x7h (WRITE_8) */
        uint8 Sid : 4;     /* [31:28] 从机ID */

        uint8 Address;              /* [23:16] 内存索引 - 任何字节对齐的内存地址 */
        uint8 Data;                 /* [15:8] 8位数据，写入内存 */
        uint8 Crc;                  /* [7:0] 8位CRC校验，保护[31:8]位 */
    } Bit;
} RdumRdusCrm_Write8BitFrame_t;

/* 写读指针帧 (CRM命令8) */
typedef union
{
    uint8 WriteRwpData[CRM_COMMAND_SIZE];
    struct
    {
        uint8 Command : 4; /* [27:24] 命令代码 - 0x8h (WRITE_RWP) */
        uint8 Sid : 4;     /* [31:28] 从机ID */

        uint8 NewCrmPage : 2;       /* [17:16] CRM写指针页面值 */
        uint8 Reserved : 1;         /* [18] 未使用 */
        uint8 CrmUpdate : 1;        /* [19] CRM写指针更新模式 */
        uint8 NewPdcmPage : 2;      /* [21:20] PDCM读指针页面值 */
        uint8 PdcmUpdate : 2;       /* [23:22] PDCM读指针更新模式 */
           
        uint8 NewCrmPageIdx;        /* [15:8] CRM写指针索引值 - 字对齐的内存地址索引 */
        uint8 Crc;                  /* [7:0] 8位CRC校验，保护[31:8]位 */
    } Bit;
} RdumRdusCrm_WriteRwpFrame_t;

/* 读取帧 (CRM命令9) */
typedef union
{
    uint8 ReadData[CRM_COMMAND_SIZE];
    struct
    {
        uint8 Command : 4; /* [27:24] 命令代码 - 0x9h (READ) */
        uint8 Sid : 4;     /* [31:28] 从机ID */

        uint8 ReadPage : 2; /* [17:16] 要读取的内存页面 */
        uint8 Reserved : 6; /* [23:18] 未使用 */

        uint8 ReadIdx; /* [15:8] 页面内的内存索引 */
        uint8 Crc;     /* [7:0] 8位CRC校验，保护[31:8]位 */
    } Bit;
} RdumRdusCrm_ReadFrame_t;

/* 编程帧 (CRM命令A) */
typedef union
{
    uint8 ProgramData[CRM_COMMAND_SIZE];
    struct
    {
        uint8 Command : 4;          /* [27:24] 命令代码 - 0xAh (PROGRAM) */
        uint8 Sid : 4;              /* [31:28] 从机ID */
        
        uint8 Reserved[2];          /* [23:8] 保留 */
        uint8 Crc;                  /* [7:0] 8位CRC校验，保护[31:8]位 */
    } Bit;
} RdumRdusCrm_ProgramFrame_t;

/* 开始测量帧 (CRM命令F) */
typedef union
{
    uint8 MeasureData[10];
    struct
    {
        uint8 Command : 4; /* [75:72] 命令代码 - 0xFh (MEASURE) */
        uint8 Sid : 4;     /* [79:76] 从机ID */

        uint8 Reserved1 : 4; /* [67:64] 未使用 */
        uint8 Mode : 3;      /* [70:68] 模式[2:0] */
        uint8 Reserved2 : 1; /* [71] 未使用 */

        uint8 SID2_BEH_CH_H : 2; /* [57:56] SID2行为 - 高通道行为[1:0] */
        uint8 SID2_BEH_CH_L : 2; /* [59:58] SID2行为 - 低通道行为[1:0] */
        uint8 SID1_BEH_CH_H : 2; /* [61:60] SID1行为 - 高通道行为[1:0] */
        uint8 SID1_BEH_CH_L : 2; /* [63:62] SID1行为 - 低通道行为[1:0] */

        uint8 SID4_BEH_CH_H : 2; /* [49:48] SID4行为 - 高通道行为[1:0] */
        uint8 SID4_BEH_CH_L : 2; /* [51:50] SID4行为 - 低通道行为[1:0] */
        uint8 SID3_BEH_CH_H : 2; /* [53:52] SID3行为 - 高通道行为[1:0] */
        uint8 SID3_BEH_CH_L : 2; /* [55:54] SID3行为 - 低通道行为[1:0] */

        uint8 SID6_BEH_CH_H : 2; /* [41:40] SID6行为 - 高通道行为[1:0] */
        uint8 SID6_BEH_CH_L : 2; /* [43:42] SID6行为 - 低通道行为[1:0] */
        uint8 SID5_BEH_CH_H : 2; /* [45:44] SID5行为 - 高通道行为[1:0] */
        uint8 SID5_BEH_CH_L : 2; /* [47:46] SID5行为 - 低通道行为[1:0] */

        uint8 DOPPLER_RX_OUTER : 2; /* [33:32] 外侧多普勒接收[1:0] */
        uint8 DOPPLER_TX_INNER : 4; /* [37:34] 内侧多普勒发送[3:0] */
        uint8 DOPPLER_RX_INNER : 2; /* [39:38] 内侧多普勒接收[1:0] */

        uint8 DOPPLER_TX_SIDE : 2;  /* [25:24] 侧面多普勒发送[1:0] */
        uint8 DOPPLER_RX_SIDE : 2;  /* [27:26] 侧面多普勒接收[1:0] */
        uint8 DOPPLER_TX_OUTER : 4; /* [31:28] 外侧多普勒发送[3:0] */

        uint8 Reserved3 : 5;  /* [20:16] 未使用 */
        uint8 REV_SH : 2;     /* [22:21] 反向移位 */
        uint8 REV_SH_ENA : 1; /* [23] 反向移位使能 */

        uint8 Reserved4; /* [15:8] 未使用 */

        uint8 Crc; /* [7:0] 8位CRC校验，保护整个消息 */
    } Bit;
} RdumRdusCrm_MeasureFrame_t;


/******************************************************************************
 * @Const Declaration
 *****************************************************************************/



/******************************************************************************
 * @Variable Declaration
 *****************************************************************************/



/******************************************************************************
 * @Function Declaration
 *****************************************************************************/
/* 软件复位 - CRM命令2 */
SpiTransReturn_t RdumRdusCrm_SoftwareReset(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id);

/* 地址发现 - CRM命令3 */
SpiTransReturn_t RdumRdusCrm_AddressDiscovery(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id);

/* 块写入 - CRM命令4 */
SpiTransReturn_t RdumRdusCrm_BlockWrite(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, uint8 *Data, uint8 DataSize);

/* 流水线写入 - CRM命令5 */
SpiTransReturn_t RdumRdusCrm_PipelineWrite(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, uint16 Data);

/* 写入 - CRM命令6 */
SpiTransReturn_t RdumRdusCrm_Write(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, uint16 Data, CrmMapConfig_t *Config);

/* 8位写入 - CRM命令7 */
SpiTransReturn_t RdumRdusCrm_Write8Bit(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, uint8 Address, uint8 Data);

/* 写读指针  - CRM命令8 */
SpiTransReturn_t RdumRdusCrm_WriteRwp(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id,
                                    RdumRdusPdcmUpdateMode_t PdcmUpdate,
                                    RdumRdusCrmUpdateMode_t CrmUpdate,
                                    uint8 NewCrmPage,
                                    uint8 NewCrmPageIdx,
                                    uint8 NewPdcmPage);

/* 读取 - CRM命令9 */
SpiTransReturn_t RdumRdusCrm_Read(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, uint8 ReadPage, uint8 ReadIdx);

/* 编程 - CRM命令A */
SpiTransReturn_t RdumRdusCrm_Program(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id);

/* 开始测量 - CRM命令F */
SpiTransReturn_t RdumRdusCrm_StartMeasurement(Dsi3Channel_t Dsi3Ch, RdumRdusCrm_MeasureFrame_t *MeasureFrame);

/* 通过页面索引写入 */
SpiTransReturn_t RdumRdusCrm_WriteByPageIndex(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, RdumRdusPageIndex_t PageIndex, uint8 *Data);

// /* 通过页面索引8位写入 */
// SpiTransReturn_t RdumRdusCrm_Write8BitByPageIndex(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, RdumRdusPageIndex_t PageIndex, uint8 Data);

// /* 通过页面索引读取 */
// SpiTransReturn_t RdumRdusCrm_ReadByPageIndex(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, RdumRdusPageIndex_t PageIndex);

// /* 读取传感器ID */
// SpiTransReturn_t RdumRdusCrm_ReadSensorId(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id);

// /* 写入测量模式参数 */
// SpiTransReturn_t RdumRdusCrm_WriteModeConfig(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, uint8 Mode, uint8 *Data, uint8 DataSize);

// /* 读取测量模式参数 */
// SpiTransReturn_t RdumRdusCrm_ReadModeConfig(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, uint8 Mode);

// /* 特殊读操作 - 发送读请求 */
// SpiTransReturn_t RdumRdusCrm_ReadRequest(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, uint8 Address, SpiNonReadResp_t *NonReadResp);

#endif /* __RDUM_RDUS_CRM_H__ */
