/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * PDCSignalManage.c: 
 * Created on: 2020-10-22 10:14
 * Original designer: 21059
 ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "PAS_MAP_SignalManage.h"
#include "MapEchoFilterAndSigGroup_int.h"


/******************************************************************************/
/******************************************************************************/
/****************************** Private Definition ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Public Definition *****************************/
/******************************************************************************/
/******************************************************************************/
uint8  Gu8PointCloudObjUpdateFlag[12] = {1,1,1,1,1,1, 1,1,1,1,1,1};

/******************************************************************************/
/******************************************************************************/
/****************************** Private Function ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/******************************* Public Function ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************
 * 描    述:写PDC工作状态函数接口
 * 输入参数:LenuPDCWorkState--PDC的工作状态；LenuGroup--前/后雷达组别
 * 输出参数:无
 * 返 回 值:无
 *****************************************************************************/
static PdcSignal_PDCWorkStatusType GenuPdcSignal_PDCWorkStatus;
void PdcSignal_WritePDCWorkStatus(PdcSignal_PDCWorkStatusType LenuPDCWorkStatus)
{
	GenuPdcSignal_PDCWorkStatus = LenuPDCWorkStatus;
}

void PdcSignal_ReadPDCWorkStatus(PdcSignal_PDCWorkStatusType *LenuPDCWorkStatus)
{
	*LenuPDCWorkStatus = GenuPdcSignal_PDCWorkStatus ;
}



/*******************************************************************************
*描述：分区距离函数接口
*输入参数：无
*输出参数：无
*返回值：无
********************************************************************************/
static PDCSignal_ZoneDisType Gu16PdcSignal_ZoneDis;


void PdcSignal_ZoneDistanceInit(void)
{
    Gu16PdcSignal_ZoneDis.FL1_Distance  = 0xFB;
    Gu16PdcSignal_ZoneDis.FL2_Distance  = 0xFB;
    Gu16PdcSignal_ZoneDis.FML1_Distance = 0xFB;
    Gu16PdcSignal_ZoneDis.FML2_Distance = 0xFB;
    Gu16PdcSignal_ZoneDis.FMR1_Distance = 0xFB;
    Gu16PdcSignal_ZoneDis.FMR2_Distance = 0xFB;
    Gu16PdcSignal_ZoneDis.FR1_Distance  = 0xFB;
    Gu16PdcSignal_ZoneDis.FR2_Distance  = 0xFB;

    Gu16PdcSignal_ZoneDis.RL1_Distance  = 0xFB;
    Gu16PdcSignal_ZoneDis.RL2_Distance  = 0xFB;
    Gu16PdcSignal_ZoneDis.RML1_Distance = 0xFB;
    Gu16PdcSignal_ZoneDis.RML2_Distance = 0xFB;
    Gu16PdcSignal_ZoneDis.RMR1_Distance = 0xFB;
    Gu16PdcSignal_ZoneDis.RMR2_Distance = 0xFB;
    Gu16PdcSignal_ZoneDis.RR1_Distance  = 0xFB;
    Gu16PdcSignal_ZoneDis.RR2_Distance  = 0xFB;

    Gu16PdcSignal_ZoneDis.LSF1_Distance = 0xFB;
    Gu16PdcSignal_ZoneDis.LSF2_Distance = 0xFB;
    Gu16PdcSignal_ZoneDis.LSFM1_Distance = 0xFB;
    Gu16PdcSignal_ZoneDis.LSFM2_Distance = 0xFB;
    Gu16PdcSignal_ZoneDis.LSRM1_Distance = 0xFB;
    Gu16PdcSignal_ZoneDis.LSRM2_Distance = 0xFB;
    Gu16PdcSignal_ZoneDis.LSR1_Distance  = 0xFB;
    Gu16PdcSignal_ZoneDis.LSR2_Distance  = 0xFB;

    Gu16PdcSignal_ZoneDis.RSF1_Distance  = 0xFB;
    Gu16PdcSignal_ZoneDis.RSF2_Distance  = 0xFB;
    Gu16PdcSignal_ZoneDis.RSFM1_Distance = 0xFB;
    Gu16PdcSignal_ZoneDis.RSFM2_Distance = 0xFB;
    Gu16PdcSignal_ZoneDis.RSRM1_Distance = 0xFB;
    Gu16PdcSignal_ZoneDis.RSRM2_Distance = 0xFB;
    Gu16PdcSignal_ZoneDis.RSR1_Distance  = 0xFB;
    Gu16PdcSignal_ZoneDis.RSR2_Distance  = 0xFB;
}


void PdcSignal_WriteZoneDistance(PDCSignal_ZoneDisType *LstrZoneDis)
{
    Gu16PdcSignal_ZoneDis.FL1_Distance = LstrZoneDis->FL1_Distance;
    Gu16PdcSignal_ZoneDis.FL2_Distance = LstrZoneDis->FL2_Distance ;
    Gu16PdcSignal_ZoneDis.FML1_Distance = LstrZoneDis->FML1_Distance;
    Gu16PdcSignal_ZoneDis.FML2_Distance = LstrZoneDis->FML2_Distance;
    Gu16PdcSignal_ZoneDis.FMR1_Distance = LstrZoneDis->FMR1_Distance;
    Gu16PdcSignal_ZoneDis.FMR2_Distance = LstrZoneDis->FMR2_Distance;
    Gu16PdcSignal_ZoneDis.FR1_Distance = LstrZoneDis->FR1_Distance;
    Gu16PdcSignal_ZoneDis.FR2_Distance = LstrZoneDis->FR2_Distance;

    Gu16PdcSignal_ZoneDis.RL1_Distance = LstrZoneDis->RL1_Distance;
    Gu16PdcSignal_ZoneDis.RL2_Distance = LstrZoneDis->RL2_Distance;
    Gu16PdcSignal_ZoneDis.RML1_Distance = LstrZoneDis->RML1_Distance;
    Gu16PdcSignal_ZoneDis.RML2_Distance = LstrZoneDis->RML2_Distance;
    Gu16PdcSignal_ZoneDis.RMR1_Distance = LstrZoneDis->RMR1_Distance;
    Gu16PdcSignal_ZoneDis.RMR2_Distance = LstrZoneDis->RMR2_Distance;
    Gu16PdcSignal_ZoneDis.RR1_Distance = LstrZoneDis->RR1_Distance;
    Gu16PdcSignal_ZoneDis.RR2_Distance = LstrZoneDis->RR2_Distance;

    Gu16PdcSignal_ZoneDis.LSF1_Distance = LstrZoneDis->LSF1_Distance;
    Gu16PdcSignal_ZoneDis.LSF2_Distance = LstrZoneDis->LSF2_Distance;
    Gu16PdcSignal_ZoneDis.LSFM1_Distance = LstrZoneDis->LSFM1_Distance;
    Gu16PdcSignal_ZoneDis.LSFM2_Distance = LstrZoneDis->LSFM2_Distance;
    Gu16PdcSignal_ZoneDis.LSRM1_Distance = LstrZoneDis->LSRM1_Distance;
    Gu16PdcSignal_ZoneDis.LSRM2_Distance = LstrZoneDis->LSRM2_Distance;
    Gu16PdcSignal_ZoneDis.LSR1_Distance = LstrZoneDis->LSR1_Distance;
    Gu16PdcSignal_ZoneDis.LSR2_Distance = LstrZoneDis->LSR2_Distance;

	Gu16PdcSignal_ZoneDis.RSF1_Distance = LstrZoneDis->RSF1_Distance;
	Gu16PdcSignal_ZoneDis.RSF2_Distance = LstrZoneDis->RSF2_Distance;
	Gu16PdcSignal_ZoneDis.RSFM1_Distance = LstrZoneDis->RSFM1_Distance;
	Gu16PdcSignal_ZoneDis.RSFM2_Distance = LstrZoneDis->RSFM2_Distance;
	Gu16PdcSignal_ZoneDis.RSRM1_Distance = LstrZoneDis->RSRM1_Distance;
	Gu16PdcSignal_ZoneDis.RSRM2_Distance = LstrZoneDis->RSRM2_Distance;
	Gu16PdcSignal_ZoneDis.RSR1_Distance = LstrZoneDis->RSR1_Distance;
	Gu16PdcSignal_ZoneDis.RSR2_Distance = LstrZoneDis->RSR2_Distance;
}

void PdcSignal_ReadZoneDistance(PDCSignal_ZoneDisType *LstrZoneDis)
{
    LstrZoneDis->FL1_Distance = Gu16PdcSignal_ZoneDis.FL1_Distance;
    LstrZoneDis->FL2_Distance = Gu16PdcSignal_ZoneDis.FL2_Distance;
    LstrZoneDis->FML1_Distance = Gu16PdcSignal_ZoneDis.FML1_Distance;
    LstrZoneDis->FML2_Distance = Gu16PdcSignal_ZoneDis.FML2_Distance;
    LstrZoneDis->FMR1_Distance = Gu16PdcSignal_ZoneDis.FMR1_Distance;
    LstrZoneDis->FMR2_Distance = Gu16PdcSignal_ZoneDis.FMR2_Distance;
    LstrZoneDis->FR1_Distance = Gu16PdcSignal_ZoneDis.FR1_Distance;
    LstrZoneDis->FR2_Distance = Gu16PdcSignal_ZoneDis.FR2_Distance;

    LstrZoneDis->RL1_Distance = Gu16PdcSignal_ZoneDis.RL1_Distance;
    LstrZoneDis->RL2_Distance = Gu16PdcSignal_ZoneDis.RL2_Distance;
    LstrZoneDis->RML1_Distance = Gu16PdcSignal_ZoneDis.RML1_Distance;
    LstrZoneDis->RML2_Distance = Gu16PdcSignal_ZoneDis.RML2_Distance;
    LstrZoneDis->RMR1_Distance = Gu16PdcSignal_ZoneDis.RMR1_Distance;
    LstrZoneDis->RMR2_Distance = Gu16PdcSignal_ZoneDis.RMR2_Distance;
    LstrZoneDis->RR1_Distance = Gu16PdcSignal_ZoneDis.RR1_Distance;
    LstrZoneDis->RR2_Distance = Gu16PdcSignal_ZoneDis.RR2_Distance;

    LstrZoneDis->LSF1_Distance = Gu16PdcSignal_ZoneDis.LSF1_Distance;
    LstrZoneDis->LSF2_Distance = Gu16PdcSignal_ZoneDis.LSF2_Distance;
    LstrZoneDis->LSFM1_Distance = Gu16PdcSignal_ZoneDis.LSFM1_Distance;
    LstrZoneDis->LSFM2_Distance = Gu16PdcSignal_ZoneDis.LSFM2_Distance;
    LstrZoneDis->LSRM1_Distance = Gu16PdcSignal_ZoneDis.LSRM1_Distance;
    LstrZoneDis->LSRM2_Distance = Gu16PdcSignal_ZoneDis.LSRM2_Distance;
    LstrZoneDis->LSR1_Distance = Gu16PdcSignal_ZoneDis.LSR1_Distance;
    LstrZoneDis->LSR2_Distance = Gu16PdcSignal_ZoneDis.LSR2_Distance;

	LstrZoneDis->RSF1_Distance = Gu16PdcSignal_ZoneDis.RSF1_Distance;
	LstrZoneDis->RSF2_Distance = Gu16PdcSignal_ZoneDis.RSF2_Distance;
	LstrZoneDis->RSFM1_Distance = Gu16PdcSignal_ZoneDis.RSFM1_Distance;
	LstrZoneDis->RSFM2_Distance = Gu16PdcSignal_ZoneDis.RSFM2_Distance;
	LstrZoneDis->RSRM1_Distance = Gu16PdcSignal_ZoneDis.RSRM1_Distance;
	LstrZoneDis->RSRM2_Distance = Gu16PdcSignal_ZoneDis.RSRM2_Distance;
	LstrZoneDis->RSR1_Distance = Gu16PdcSignal_ZoneDis.RSR1_Distance;
	LstrZoneDis->RSR2_Distance = Gu16PdcSignal_ZoneDis.RSR2_Distance;
}

/*******************************************************************************
*描述：探头的DE距离函数接口
*输入参数：无
*输出参数：无
*返回值：无
********************************************************************************/
static PDCSignal_DEStructType GstrDE_Distance[PDC_GROUP_NUM];


void PdcSignal_USS_DE_Distace_Init(PDCSignal_SnsGroupType LenuGroup)
{
	 GstrDE_Distance[LenuGroup].u16PDCSignal_DE1 = 0;
	 GstrDE_Distance[LenuGroup].u16PDCSignal_DE2 = 0;
	 GstrDE_Distance[LenuGroup].u16PDCSignal_DE3 = 0;
	 GstrDE_Distance[LenuGroup].u16PDCSignal_DE4 = 0;
	 GstrDE_Distance[LenuGroup].u16PDCSignal_DE5 = 0;
	 GstrDE_Distance[LenuGroup].u16PDCSignal_DE6 = 0;
}


void PdcSignal_WriteUSS_DE_Distace(PDCSignal_DEStructType LstrDE_Distance,PDCSignal_SnsGroupType LenuGroup)
{
	 GstrDE_Distance[LenuGroup].u16PDCSignal_DE1 = LstrDE_Distance.u16PDCSignal_DE1;
	 GstrDE_Distance[LenuGroup].u16PDCSignal_DE2 = LstrDE_Distance.u16PDCSignal_DE2;
	 GstrDE_Distance[LenuGroup].u16PDCSignal_DE3 = LstrDE_Distance.u16PDCSignal_DE3;
	 GstrDE_Distance[LenuGroup].u16PDCSignal_DE4 = LstrDE_Distance.u16PDCSignal_DE4;
	 GstrDE_Distance[LenuGroup].u16PDCSignal_DE5 = LstrDE_Distance.u16PDCSignal_DE5;
	 GstrDE_Distance[LenuGroup].u16PDCSignal_DE6 = LstrDE_Distance.u16PDCSignal_DE6;
}

void PdcSignal_ReadUSS_DE_Distace(PDCSignal_DEStructType *LstrDE_Distance,PDCSignal_SnsGroupType LenuGroup)
{
	 LstrDE_Distance->u16PDCSignal_DE1 = GstrDE_Distance[LenuGroup].u16PDCSignal_DE1;
	 LstrDE_Distance->u16PDCSignal_DE2 = GstrDE_Distance[LenuGroup].u16PDCSignal_DE2;
	 LstrDE_Distance->u16PDCSignal_DE3 = GstrDE_Distance[LenuGroup].u16PDCSignal_DE3;
	 LstrDE_Distance->u16PDCSignal_DE4 = GstrDE_Distance[LenuGroup].u16PDCSignal_DE4;
	 LstrDE_Distance->u16PDCSignal_DE5 = GstrDE_Distance[LenuGroup].u16PDCSignal_DE5;
	 LstrDE_Distance->u16PDCSignal_DE6 = GstrDE_Distance[LenuGroup].u16PDCSignal_DE6;
}


/*******************************************************************************
*描述：探头的CE距离函数接口
*输入参数：无
*输出参数：无
*返回值：无
********************************************************************************/
static PDCSignal_CEStructType GstrCE_Distance[PDC_GROUP_NUM];

void PdcSignal_USS_CE_Distace_Init(PDCSignal_SnsGroupType LenuGroup)
{
	 GstrCE_Distance[LenuGroup].u16PDCSignal_CE1_2 = 0;
	 GstrCE_Distance[LenuGroup].u16PDCSignal_CE2_3 = 0;
	 GstrCE_Distance[LenuGroup].u16PDCSignal_CE3_4 = 0;
	 GstrCE_Distance[LenuGroup].u16PDCSignal_CE4_5 = 0;
	 GstrCE_Distance[LenuGroup].u16PDCSignal_CE5_6 = 0;	 
}


void PdcSignal_WriteUSS_CE_Distace(PDCSignal_CEStructType LstrCE_Distance,PDCSignal_SnsGroupType LenuGroup)
{
	 GstrCE_Distance[LenuGroup].u16PDCSignal_CE1_2 = LstrCE_Distance.u16PDCSignal_CE1_2;
	 GstrCE_Distance[LenuGroup].u16PDCSignal_CE2_3 = LstrCE_Distance.u16PDCSignal_CE2_3;
	 GstrCE_Distance[LenuGroup].u16PDCSignal_CE3_4 = LstrCE_Distance.u16PDCSignal_CE3_4;
	 GstrCE_Distance[LenuGroup].u16PDCSignal_CE4_5 = LstrCE_Distance.u16PDCSignal_CE4_5;
	 GstrCE_Distance[LenuGroup].u16PDCSignal_CE5_6 = LstrCE_Distance.u16PDCSignal_CE5_6;	 
}

void PdcSignal_ReadUSS_CE_Distace(PDCSignal_CEStructType *LstrCE_Distance,PDCSignal_SnsGroupType LenuGroup)
{
	 LstrCE_Distance->u16PDCSignal_CE1_2 = GstrCE_Distance[LenuGroup].u16PDCSignal_CE1_2;
	 LstrCE_Distance->u16PDCSignal_CE2_3 = GstrCE_Distance[LenuGroup].u16PDCSignal_CE2_3;
	 LstrCE_Distance->u16PDCSignal_CE3_4 = GstrCE_Distance[LenuGroup].u16PDCSignal_CE3_4;
	 LstrCE_Distance->u16PDCSignal_CE4_5 = GstrCE_Distance[LenuGroup].u16PDCSignal_CE4_5;
	 LstrCE_Distance->u16PDCSignal_CE5_6 = GstrCE_Distance[LenuGroup].u16PDCSignal_CE5_6;	 
}

/*******************************************************************************
*描述：障碍物MAP函数接口
*输入参数：无
*输出参数：无
*返回值：无
********************************************************************************/
static PdcSignal_MapObjInfoType GstrMapObjInfo[20];

void PdcSignal_MapObjInit(void)
{
	uint8 ObjIndex;
	for(ObjIndex=0;ObjIndex<20;ObjIndex++)
	{
	    GstrMapObjInfo[ObjIndex].bObjWriteLockFlg = FALSE;
		GstrMapObjInfo[ObjIndex].u32PDCSignal_PAS_MapObjTimestamp = 0;
		GstrMapObjInfo[ObjIndex].u8PDCSignal_MapObjIndex = 0x1E;
		GstrMapObjInfo[ObjIndex].u8PDCSignal_MapObjHeight = 0;
		GstrMapObjInfo[ObjIndex].u8PDCSignal_MapObjHeightProb = 0;
		GstrMapObjInfo[ObjIndex].u8PDCSignal_MapObjProb = 0;
		GstrMapObjInfo[ObjIndex].u8PDCSignal_MapObjType = 0;
		GstrMapObjInfo[ObjIndex].s16PDCSignal_MapObjP1X = 0;
		GstrMapObjInfo[ObjIndex].s16PDCSignal_MapObjP1Y = 0;
		GstrMapObjInfo[ObjIndex].s16PDCSignal_MapObjP2X = 0;
		GstrMapObjInfo[ObjIndex].s16PDCSignal_MapObjP2Y = 0;
	}
}




void PdcSignal_WriteMapObj(PdcSignal_MapObjInfoType *LstrMapObjInfo,uint8 ObjIndex)
{
    if(GstrMapObjInfo[ObjIndex].u8PDCSignal_MapObjIndex != LstrMapObjInfo->u8PDCSignal_MapObjIndex)
    {
        GstrMapObjInfo[ObjIndex].bObjWriteLockFlg =TRUE; 
        
    }
    else
    {
        GstrMapObjInfo[ObjIndex].bObjWriteLockFlg =FALSE; 
    }
    GstrMapObjInfo[ObjIndex].u8PDCSignal_MapObjIndex = LstrMapObjInfo->u8PDCSignal_MapObjIndex;
	GstrMapObjInfo[ObjIndex].u32PDCSignal_PAS_MapObjTimestamp = LstrMapObjInfo->u32PDCSignal_PAS_MapObjTimestamp;
	GstrMapObjInfo[ObjIndex].u8PDCSignal_MapObjHeight = LstrMapObjInfo->u8PDCSignal_MapObjHeight;
	GstrMapObjInfo[ObjIndex].u8PDCSignal_MapObjHeightProb = LstrMapObjInfo->u8PDCSignal_MapObjHeightProb;
	GstrMapObjInfo[ObjIndex].u8PDCSignal_MapObjProb = LstrMapObjInfo->u8PDCSignal_MapObjProb;
	GstrMapObjInfo[ObjIndex].u8PDCSignal_MapObjType = LstrMapObjInfo->u8PDCSignal_MapObjType;
	GstrMapObjInfo[ObjIndex].s16PDCSignal_MapObjP1X = LstrMapObjInfo->s16PDCSignal_MapObjP1X;
	GstrMapObjInfo[ObjIndex].s16PDCSignal_MapObjP1Y = LstrMapObjInfo->s16PDCSignal_MapObjP1Y;
	GstrMapObjInfo[ObjIndex].s16PDCSignal_MapObjP2X = LstrMapObjInfo->s16PDCSignal_MapObjP2X;
	GstrMapObjInfo[ObjIndex].s16PDCSignal_MapObjP2Y = LstrMapObjInfo->s16PDCSignal_MapObjP2Y;
    GstrMapObjInfo[ObjIndex].bObjWriteLockFlg =FALSE;
}

void PdcSignal_ReadMapObj(PdcSignal_MapObjInfoType *LstrMapObjInfo,uint8 ObjIndex)
{
    LstrMapObjInfo->bObjWriteLockFlg = GstrMapObjInfo[ObjIndex].bObjWriteLockFlg;
    LstrMapObjInfo->u8PDCSignal_MapObjIndex = GstrMapObjInfo[ObjIndex].u8PDCSignal_MapObjIndex;
    LstrMapObjInfo->u32PDCSignal_PAS_MapObjTimestamp = GstrMapObjInfo[ObjIndex].u32PDCSignal_PAS_MapObjTimestamp;
	LstrMapObjInfo->u8PDCSignal_MapObjHeight = GstrMapObjInfo[ObjIndex].u8PDCSignal_MapObjHeight;
	LstrMapObjInfo->u8PDCSignal_MapObjHeightProb = GstrMapObjInfo[ObjIndex].u8PDCSignal_MapObjHeightProb;
	LstrMapObjInfo->u8PDCSignal_MapObjProb = GstrMapObjInfo[ObjIndex].u8PDCSignal_MapObjProb;
	LstrMapObjInfo->u8PDCSignal_MapObjType = GstrMapObjInfo[ObjIndex].u8PDCSignal_MapObjType;
	LstrMapObjInfo->s16PDCSignal_MapObjP1X = GstrMapObjInfo[ObjIndex].s16PDCSignal_MapObjP1X;
	LstrMapObjInfo->s16PDCSignal_MapObjP1Y = GstrMapObjInfo[ObjIndex].s16PDCSignal_MapObjP1Y;
	LstrMapObjInfo->s16PDCSignal_MapObjP2X = GstrMapObjInfo[ObjIndex].s16PDCSignal_MapObjP2X;
	LstrMapObjInfo->s16PDCSignal_MapObjP2Y = GstrMapObjInfo[ObjIndex].s16PDCSignal_MapObjP2Y;
    
    
    
	
}

/*******************************************************************************
*描述：SDW状态函数接口
*输入参数：无
*输出参数：无
*返回值：无
********************************************************************************/
static PdcSignal_SDWStatusType			GenuPdcSignal_SDWWorkStatus;
void WriteSDWStatus(PdcSignal_SDWStatusType LenuSDWStatus)
{
	GenuPdcSignal_SDWWorkStatus = LenuSDWStatus;
}

void ReadSDWStatus(PdcSignal_SDWStatusType *LenuSDWStatus)
{
	*LenuSDWStatus = GenuPdcSignal_SDWWorkStatus;
}

/*******************************************************************************
*描述: 拖车钩信号
*输入参数：无
*输出参数：无
*返回值：无
********************************************************************************/
static PdcSignal_TrailerHitchDetectedType GenuTrailerHitchDetected=TrailerHitchDetecte_Unknow;

void TrailerHitchDetected_init(void)
{
	 	GenuTrailerHitchDetected=TrailerHitchDetecte_Unknow;
}


void WriteTrailerHitchDetected(PdcSignal_TrailerHitchDetectedType LenuTrailerHitchDetected)
{
	GenuTrailerHitchDetected = LenuTrailerHitchDetected;
}

void ReadTrailerHitchDetected(PdcSignal_TrailerHitchDetectedType *LenuTrailerHitchDetected)
{
	*LenuTrailerHitchDetected = GenuTrailerHitchDetected;
}


/*******************************************************************************
*描述:时间同步信号
*输入参数：无
*输出参数：无
*返回值：无
*说明：PAS发出的时间同步信号,表征年月日
********************************************************************************/
static uint32 Gu32Localtime_afterSync;
void WriteLocaltime_afterSync(uint32 Lu32Localtime_afterSync)
{
	Gu32Localtime_afterSync = Lu32Localtime_afterSync;
}

void ReadLocaltime_afterSync(uint32*Lu32Localtime_afterSync)
{
	*Lu32Localtime_afterSync = Gu32Localtime_afterSync;
}

/****************************************end of LiXiang*****************************************/

/******************************************************************
* 函数名称: 车衣故障检测
*
* 功能描述:
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 执行调度周期--0ms
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
uint8 GetCarCoverErrorFlag(void)
{
    uint8 Lu8CarCoverFailure=0;
    uint8 i,j;

    for(i = 0; i < 2; i++)
    {
        for(j = 0; j < 6; j++)
        {
            if(Gu8SnsBeCoveredFlag[i][j])
            {
                Lu8CarCoverFailure = 1;
                break;
            }
        }
        if(Lu8CarCoverFailure == 1)
        {
            break;
        }
    }
    
    return Lu8CarCoverFailure;
}


/*******************************************************************************
*描述：点云障碍物数据接口
*输入参数：无
*输出参数：无
*返回值：无
********************************************************************************/
static PdcPointCloud_ObjInfoType GstrPointCloudObjInfo[POINTCLOUD_MAX_SIZE];


void PdcPointCloudObjInit(void)
{
	uint8 ObjIndex;
	for(ObjIndex=0;ObjIndex<POINTCLOUD_MAX_SIZE;ObjIndex++)
	{
	    GstrPointCloudObjInfo[ObjIndex].bPointCloudLockFLg = FALSE;
		GstrPointCloudObjInfo[ObjIndex].u8PointCloud_ObjHeightProb = 0;
		GstrPointCloudObjInfo[ObjIndex].u8PointCloud_ObjHeight = 0;
		GstrPointCloudObjInfo[ObjIndex].u8PointCloud_ObjType = 0;
		GstrPointCloudObjInfo[ObjIndex].u8PointCloud_ObjProb = 0;
		GstrPointCloudObjInfo[ObjIndex].s16PointCloud_ObjPX = 0;
		GstrPointCloudObjInfo[ObjIndex].s16PointCloud_ObjPY = 0;
		
	}
}

void PdcSignal_WritePointCloudObj(PdcPointCloud_ObjInfoType *LstrPointCloudObjInfo,uint8 ObjIndex)
{
    
    GstrPointCloudObjInfo[ObjIndex].bPointCloudLockFLg =TRUE;
	GstrPointCloudObjInfo[ObjIndex].u8PointCloud_ObjHeightProb = LstrPointCloudObjInfo->u8PointCloud_ObjHeightProb;
	GstrPointCloudObjInfo[ObjIndex].u8PointCloud_ObjHeight = LstrPointCloudObjInfo->u8PointCloud_ObjHeight;
	GstrPointCloudObjInfo[ObjIndex].u8PointCloud_ObjType = LstrPointCloudObjInfo->u8PointCloud_ObjType;
	GstrPointCloudObjInfo[ObjIndex].u8PointCloud_ObjProb = LstrPointCloudObjInfo->u8PointCloud_ObjProb;
	GstrPointCloudObjInfo[ObjIndex].s16PointCloud_ObjPX = LstrPointCloudObjInfo->s16PointCloud_ObjPX;
	GstrPointCloudObjInfo[ObjIndex].s16PointCloud_ObjPY = LstrPointCloudObjInfo->s16PointCloud_ObjPY;
    GstrPointCloudObjInfo[ObjIndex].bPointCloudLockFLg =FALSE;
    Gu8PointCloudObjUpdateFlag[ObjIndex] = 1;
}

void PdcSignal_ReadPointCloudMapObj(PdcPointCloud_ObjInfoType *LstrPointCloudObjInfo,uint8 ObjIndex)
{
    LstrPointCloudObjInfo->bPointCloudLockFLg = GstrPointCloudObjInfo[ObjIndex].bPointCloudLockFLg;
	LstrPointCloudObjInfo->u8PointCloud_ObjHeightProb = GstrPointCloudObjInfo[ObjIndex].u8PointCloud_ObjHeightProb;
	LstrPointCloudObjInfo->u8PointCloud_ObjHeight = GstrPointCloudObjInfo[ObjIndex].u8PointCloud_ObjHeight;
	LstrPointCloudObjInfo->u8PointCloud_ObjType = GstrPointCloudObjInfo[ObjIndex].u8PointCloud_ObjType;
	LstrPointCloudObjInfo->u8PointCloud_ObjProb = GstrPointCloudObjInfo[ObjIndex].u8PointCloud_ObjProb;
	LstrPointCloudObjInfo->s16PointCloud_ObjPX = GstrPointCloudObjInfo[ObjIndex].s16PointCloud_ObjPX;
	LstrPointCloudObjInfo->s16PointCloud_ObjPY = GstrPointCloudObjInfo[ObjIndex].s16PointCloud_ObjPY;
	
}


/*******************************************************************************
*描述：探头被车衣覆盖标志接口
*输入参数：无
*输出参数：无
*返回值：无
********************************************************************************/
static uint8 Gu8SnsBeCoveredFlagToDTC[SNS_INX_NUM];

void PdcSignal_WriteSnsBeCoveredFlagToDTC(uint8 Lu8SnsBeCoveredFlag,PdcSiagnal_SnsInxType LenuSnsInx)
{
	Gu8SnsBeCoveredFlagToDTC[LenuSnsInx] = Lu8SnsBeCoveredFlag;
}

void PdcSignal_ReadSnsBeCoveredFlagToDTC(uint8* Lpu8SnsBeCoveredFlag,PdcSiagnal_SnsInxType LenuSnsInx)
{
	*Lpu8SnsBeCoveredFlag = Gu8SnsBeCoveredFlagToDTC[LenuSnsInx];
}



/*END PDCSignalManage.c********************************************************/


