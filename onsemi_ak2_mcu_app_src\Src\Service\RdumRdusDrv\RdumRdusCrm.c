/******************************************************************************
 * @file      RdumRdusCrm.c
 * @brief     安森美超声波探头CRM命令实现
 * <AUTHOR>
 * @date      2025-05-13
 * @note
 *****************************************************************************/

/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include <string.h>
#include "RdumRdusCrm.h"
#include "BaseDrv.h"
#include "RdumRdusPageIndex.h"

/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/



/******************************************************************************
 * @Const Declaration
 *****************************************************************************/



/******************************************************************************
 * @Variable Declaration
 *****************************************************************************/



/******************************************************************************
 * @Function Definitions
 *****************************************************************************/

/******************************************************************************
 * @brief      将CRM命令映射到TX_DATA
 * @param[in]  Config CRM映射配置
 * @param[out] TxDataMap TX_DATA映射
 * @return     0: 成功, 非0: 失败
 * <AUTHOR>
 * @date       2025-05-15
 * @note
 *****************************************************************************/
uint8 CrmSpiMapping_MapToTxData(CrmMapConfig_t *Config, TxDataMapping_t *TxDataMap)
{
    uint8 i;
    uint8 *pTxData;

    if (Config == NULL || TxDataMap == NULL)
    {
        return 1;
    }

    /* 清空TX_DATA映射 */
    memset(TxDataMap, 0, sizeof(TxDataMapping_t));

    /* 将CRM数据映射到TX_DATA */
    pTxData = (uint8 *)TxDataMap;
    for (i = 0; i < Config->TotalDataSize; i++)
    {
        pTxData[i] = Config->CrmData[i];
    }

    return 0;
}

/******************************************************************************
 * @brief      将TX_DATA映射到SPI协议
 * @param[in]  Dsi3Ch 通道
 * @param[in]  TxDataMap TX_DATA映射
 * @param[out] SpiCmd SPI命令
 * @param[in]  StartAddr 起始地址
 * @return     0: 成功, 非0: 失败
 * <AUTHOR>
 * @date       2025-05-15
 * @note
 *****************************************************************************/
uint8 CrmSpiMapping_MapToSpiProtocol(Dsi3Channel_t Dsi3Ch, TxDataMapping_t *TxDataMap, SpiProtocol_t *SpiCmd, uint8 StartAddr)
{
    if (TxDataMap == NULL || SpiCmd == NULL)
    {
        return 1;
    }

    if (StartAddr < ADDR_CRM_MAPPING_START || StartAddr > ADDR_CRM_MAPPING_END)
    {
        return 2;
    }

    /* 设置SPI命令 */
    SpiCmd->ReadAndWrite_t.Channel = Dsi3Ch;
    SpiCmd->ReadAndWrite_t.Command = WRITE_BY_ADDRESS;
    SpiCmd->ReadAndWrite_t.Addr = StartAddr;

    /* 根据起始地址设置TX_DATA */
    switch (StartAddr)
    {
    case ADDR_CRM_DATA_0:
        SpiCmd->ReadAndWrite_t.Payload.Value = TxDataMap->TxData0.Value;
        break;

    case ADDR_CRM_DATA_1:
        SpiCmd->ReadAndWrite_t.Payload.Value = TxDataMap->TxData1.Value;
        break;

    case ADDR_CRM_DATA_2:
        SpiCmd->ReadAndWrite_t.Payload.Value = TxDataMap->TxData2.Value;
        break;

    case ADDR_CRM_DATA_3:
        SpiCmd->ReadAndWrite_t.Payload.Value = TxDataMap->TxData3.Value;
        break;

    case ADDR_CRM_DATA_4:
        SpiCmd->ReadAndWrite_t.Payload.Value = TxDataMap->TxData4.Value;
        break;

    default:
        return 3;
    }

    /* 计算SPI的CRC由发送函数统一计算 */  

    return 0;
}

/******************************************************************************
 * @brief      软件复位 CRM命令2
 * @param[in]  Dsi3Ch 通道
 * @param[in]  Dsi3Id 从机ID
 * @return     传输状态
 * <AUTHOR>
 * @date       2025-05-13
 * @note       从设备不传输任何响应
 *****************************************************************************/
SpiTransReturn_t RdumRdusCrm_SoftwareReset(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id)
{
    RdumRdusCrm_ResetFrame_t TxData;

    /* unlock_sw_rst需要设置为1解锁才能使用软件复位 */
    TxData.Bit.Command = CRM_TYPE_SOFTWARE_RESET;
    TxData.Bit.Sid = Dsi3Id;
    TxData.Bit.Crc = BaseDrv_Crc8Calculate(TxData.ResetData, 3, CRC8_C2_INIT_DSI3);

    return SpiCmd_WriteByAddress(Dsi3Ch, ADDR_CRM_MAPPING_START, TxData.ResetData);
}

/******************************************************************************
 * @brief      地址发现 CRM命令3
 * @param[in]  Dsi3Ch 通道
 * @param[in]  Dsi3Id 从机ID
 * @return     传输状态
 * <AUTHOR>
 * @date       2025-05-13
 * @note       从设备不传输任何响应
 *****************************************************************************/
SpiTransReturn_t RdumRdusCrm_AddressDiscovery(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id)
{
    RdumRdusCrm_DiscoveryFrame_t TxData;

    TxData.Bit.Command = CRM_TYPE_ADDRESS_DISCOVERY;
    TxData.Bit.Sid = Dsi3Id;
    TxData.Bit.Crc = BaseDrv_Crc8Calculate(TxData.DiscoveryData, 3, CRC8_C2_INIT_DSI3);

    return SpiCmd_WriteByAddress(Dsi3Ch, ADDR_CRM_MAPPING_START, TxData.DiscoveryData);
}

/******************************************************************************
 * @brief      块写入 CRM命令4
 * @param[in]  Dsi3Ch 通道
 * @param[in]  Dsi3Id 从机ID
 * @param[in]  Data 数据指针
 * @param[in]  DataSize 数据大小，最多12字节
 * @return     传输状态
 * <AUTHOR>
 * @date       2025-05-13
 * @note       从设备不传输任何响应
 *****************************************************************************/
SpiTransReturn_t RdumRdusCrm_BlockWrite(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, uint8 *Data, uint8 DataSize)
{
    RdumRdusCrm_WriteBlockFrame_t TxData;
    CrmMapConfig_t CrmConfig = {0};
    TxDataMapping_t TxDataMap;
    SpiProtocol_t SpiCmd[CRM_MAX_COMMANDS]; /* 开始测量命令需要多个TX_DATA地址 */
    uint8 RetVal;
    uint8 i;
    uint8 TxDataCount;

    if (Data == NULL || DataSize > sizeof(TxData.Bit.Data))
    {
        return SPI_TRANS_INVALID;
    }

    TxData.Bit.Command = CRM_TYPE_WRITE_BLOCK;
    TxData.Bit.Sid = Dsi3Id;
    memcpy(TxData.Bit.Data, Data, DataSize);
    TxData.Bit.Crc = BaseDrv_Crc8Calculate(TxData.WriteBlockData, sizeof(TxData.WriteBlockData) - 1, CRC8_C2_INIT_DSI3);

    memcpy(&CrmConfig.CrmData[CrmConfig.TotalDataSize], TxData.WriteBlockData, sizeof(TxData.WriteBlockData));
    CrmConfig.TotalDataSize += sizeof(TxData.WriteBlockData);
    CrmConfig.CommandCount++;

    /* 将CRM命令映射到TX_DATA */
    RetVal = CrmSpiMapping_MapToTxData(&CrmConfig, &TxDataMap);
    if (RetVal != 0)
    {
        return SPI_TRANS_INVALID;
    }

    /* 计算需要的TX_DATA地址数量 */
    if (CrmConfig.TotalDataSize % CRM_COMMAND_SIZE == 0)
    {
        /* 每个TX_DATA可以存储4个字节 */
        TxDataCount = CrmConfig.TotalDataSize / CRM_COMMAND_SIZE;
    }
    else
    {
        /* 每个TX_DATA可以存储4个字节 */
        TxDataCount = CrmConfig.TotalDataSize / CRM_COMMAND_SIZE + 1;
    }

    /* 将TX_DATA映射到SPI协议 */
    for (i = 0; i < TxDataCount; i++)
    {
        RetVal = CrmSpiMapping_MapToSpiProtocol(Dsi3Ch, &TxDataMap, &SpiCmd[i], ADDR_CRM_MAPPING_START + i);
        if (RetVal != 0)
        {
            return SPI_TRANS_INVALID;
        }
    }

    /* 发送SPI命令 */
    for (i = 0; i < TxDataCount; i++)
    {
        // RetVal = SpiCom_AsyncTransferEx(SpiCmd[i].SpiCmdData, SPI_CMD_LENS, 1, 0, 3, NonReadCommandCallback, NULL);
        if (SpiCmd_WriteByAddress(SpiCmd[i].ReadAndWrite_t.Channel, SpiCmd[i].ReadAndWrite_t.Addr, SpiCmd[i].ReadAndWrite_t.Payload.Bytes) != SPI_TRANS_SUCC)
        {
            return SPI_TRANS_INVALID;
        }
    }

    return SPI_TRANS_SUCC;
}

/******************************************************************************
 * @brief      流水线写入 CRM命令5
 * @param[in]  Dsi3Ch 通道
 * @param[in]  Dsi3Id 从机ID
 * @param[in]  Data 数据
 * @param[in]  DataSize 数据大小
 * @return     传输状态
 * <AUTHOR>
 * @date       2025-05-13
 * @note
 *****************************************************************************/
SpiTransReturn_t RdumRdusCrm_PipelineWrite(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, uint16 Data)
{
    RdumRdusCrm_WritePipelinedFrame_t TxData;

    TxData.Bit.Command = CRM_TYPE_WRITE_PIPELINED;
    TxData.Bit.Sid = Dsi3Id;
    TxData.Bit.Data[0] = Data >> 8;
    TxData.Bit.Data[1] = Data & 0xFF;
    TxData.Bit.Crc = BaseDrv_Crc8Calculate(TxData.WritePipelinedData, 3, CRC8_C2_INIT_DSI3);

    return SpiCmd_WriteByAddress(Dsi3Ch, ADDR_CRM_MAPPING_START, TxData.WritePipelinedData);
}


/******************************************************************************
 * @brief      写入 CRM命令6
 * @param[in]  Dsi3Ch 通道
 * @param[in]  Dsi3Id 从机ID
 * @param[in]  Data 数据
 * @param[in]  DataSize 数据大小
 * @return     传输状态
 * <AUTHOR>
 * @date       2025-05-13
 * @note
 *****************************************************************************/
SpiTransReturn_t RdumRdusCrm_Write(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, uint16 Data, CrmMapConfig_t *Config)
{
    RdumRdusCrm_WriteFrame_t TxData;
    uint8 CommandCount = 0;

    TxData.Bit.Command = CRM_TYPE_WRITE;
    TxData.Bit.Sid = Dsi3Id;
    TxData.Bit.Data[0] = Data >> 8;
    TxData.Bit.Data[1] = Data & 0xFF;
    TxData.Bit.Crc = BaseDrv_Crc8Calculate(TxData.WriteData, 3, CRC8_C2_INIT_DSI3);

    if  (Config != NULL)
    {
        /* 写入模式配置, 如果上一条命令为CRM命令8 指定写入地址 */
        CommandCount = Config->CommandCount;
    }

    return SpiCmd_WriteByAddress(Dsi3Ch, ADDR_CRM_MAPPING_START + CommandCount, TxData.WriteData);
}

/******************************************************************************
 * @brief      8位写入 CRM命令7
 * @param[in]  Dsi3Ch 通道
 * @param[in]  Dsi3Id 从机ID
 * @param[in]  Address 地址
 * @param[in]  Data 数据
 * @return     传输状态
 * <AUTHOR>
 * @date       2025-05-13
 * @note
 *****************************************************************************/
SpiTransReturn_t RdumRdusCrm_Write8Bit(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, uint8 Address, uint8 Data)
{
    RdumRdusCrm_Write8BitFrame_t TxData;

    TxData.Bit.Command = CRM_TYPE_WRITE_8BIT;
    TxData.Bit.Sid = Dsi3Id;
    TxData.Bit.Address = Address;
    TxData.Bit.Data = Data;
    TxData.Bit.Crc = BaseDrv_Crc8Calculate(TxData.Write8BitData, 3, CRC8_C2_INIT_DSI3);

    return SpiCmd_WriteByAddress(Dsi3Ch, ADDR_CRM_MAPPING_START, TxData.Write8BitData);
}

/******************************************************************************
 * @brief      写读指针 CRM命令8
 * @param[in]  Dsi3Ch 通道
 * @param[in]  Dsi3Id 从机ID
 * @param[in]  PdcmUpdate 读PDCM更新模式
 * @param[in]  CrmUpdate 写CRM更新模式
 * @param[in]  NewCrmPage 写新CRM页面
 * @param[in]  NewCrmPageIdx 写新CRM页面索引
 * @param[in]  NewPdcmPage 读新PDCM页面 (总共4页)
 * @return     传输状态
 * <AUTHOR>
 * @date       2025-05-13
 * @note       一般情况只会写入ADDR_CRM_MAPPING_START
 *****************************************************************************/
SpiTransReturn_t RdumRdusCrm_WriteRwp(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id,
                                   RdumRdusPdcmUpdateMode_t PdcmUpdate,
                                   RdumRdusCrmUpdateMode_t CrmUpdate,
                                   uint8 NewCrmPage,
                                   uint8 NewCrmPageIdx,
                                   uint8 NewPdcmPage)
{
    RdumRdusCrm_WriteRwpFrame_t TxData;

    TxData.Bit.Command = CRM_TYPE_WRITE_READ_POINTERS;
    TxData.Bit.Sid = Dsi3Id;
    TxData.Bit.PdcmUpdate = PdcmUpdate;
    TxData.Bit.CrmUpdate = CrmUpdate;
    TxData.Bit.NewCrmPage = NewCrmPage;
    TxData.Bit.NewCrmPageIdx = NewCrmPageIdx;
    TxData.Bit.NewPdcmPage = NewPdcmPage;
    TxData.Bit.Crc = BaseDrv_Crc8Calculate(TxData.WriteRwpData, 3, CRC8_C2_INIT_DSI3);

    return SpiCmd_WriteByAddress(Dsi3Ch, ADDR_CRM_MAPPING_START, TxData.WriteRwpData);
}

/******************************************************************************
 * @brief      读取 CRM命令9
 * @param[in]  Dsi3Ch 通道
 * @param[in]  Dsi3Id 从机ID
 * @param[in]  ReadPage 读取页面
 * @param[in]  ReadIdx 读取索引
 * @return     传输状态
 * <AUTHOR>
 * @date       2025-05-13
 * @note       发送该命令后，再使用GET_CRM_RESP获取对应的数据
 *****************************************************************************/
SpiTransReturn_t RdumRdusCrm_Read(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, uint8 ReadPage, uint8 ReadIdx)
{
    RdumRdusCrm_ReadFrame_t TxData;

    TxData.Bit.Command = CRM_TYPE_READ;
    TxData.Bit.Sid = Dsi3Id;
    TxData.Bit.ReadPage = ReadPage;
    TxData.Bit.ReadIdx = ReadIdx;
    TxData.Bit.Crc = BaseDrv_Crc8Calculate(TxData.ReadData, 3, CRC8_C2_INIT_DSI3);

    return SpiCmd_WriteByAddress(Dsi3Ch, ADDR_CRM_MAPPING_START, TxData.ReadData);
}

/******************************************************************************
 * @brief      编程  CRM命令A
 * @param[in]  Dsi3Ch 通道
 * @param[in]  Dsi3Id 从机ID
 * @return     传输状态
 * <AUTHOR>
 * @date       2025-05-13
 * @note
 *****************************************************************************/
SpiTransReturn_t RdumRdusCrm_Program(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id)
{
    RdumRdusCrm_ProgramFrame_t TxData;

    TxData.Bit.Command = CRM_TYPE_PROGRAM_MEMORY;
    TxData.Bit.Sid = Dsi3Id;
    TxData.Bit.Crc = BaseDrv_Crc8Calculate(TxData.ProgramData, 3, CRC8_C2_INIT_DSI3);

    return SpiCmd_WriteByAddress(Dsi3Ch, ADDR_CRM_MAPPING_START, TxData.ProgramData);
}

/******************************************************************************
 * @brief      添加开始测量命令0x0F
 * @param[in,out] Config CRM映射配置
 * @param[in]  Dsi3Id 从机ID
 * @param[in]  Mode 测量模式
 * @return     0: 成功, 非0: 失败
 * <AUTHOR>
 * @date       2025-05-15
 * @note       待增加参数，通过该参数控制测量模式(谁主发谁侦听)
 *****************************************************************************/
uint8 CrmSpiMapping_AddStartMeasurement(CrmMapConfig_t *Config, RdumRdusCrm_MeasureFrame_t *MeasureFrame)
{
    if (Config == NULL)
    {
        return 1;
    }

    if (Config->CommandCount >= CRM_MAX_COMMANDS)
    {
        return 2;
    }

    if (Config->TotalDataSize + sizeof(RdumRdusCrm_MeasureFrame_t) > CRM_MAX_DATA_SIZE)
    {
        return 3;
    }

    MeasureFrame->Bit.Command = CRM_TYPE_START_MEASUREMENT;
    /* 计算CRC */
    MeasureFrame->Bit.Crc = BaseDrv_Crc8Calculate(MeasureFrame->MeasureData, sizeof(MeasureFrame->MeasureData) - 1, CRC8_C2_INIT_DSI3);
    memcpy(&Config->CrmData[Config->TotalDataSize], MeasureFrame->MeasureData, sizeof(RdumRdusCrm_MeasureFrame_t));
    Config->TotalDataSize += sizeof(RdumRdusCrm_MeasureFrame_t);
    Config->CommandCount++;

    return 0;
}

/******************************************************************************
 * @brief      开始测量
 * @param[in]  Dsi3Ch 通道
 * @param[in]  Dsi3Id 从机ID
 * @param[in]  Mode 测量模式
 * @return     传输状态
 * <AUTHOR>
 * @date       2025-05-13
 * @note       待增加参数，通过该参数控制测量模式(谁主发谁侦听)
 *****************************************************************************/
SpiTransReturn_t RdumRdusCrm_StartMeasurement(Dsi3Channel_t Dsi3Ch, RdumRdusCrm_MeasureFrame_t *MeasureFrame)
{
    CrmMapConfig_t CrmConfig = {0};
    TxDataMapping_t TxDataMap = {0};
    SpiProtocol_t SpiCmd[CRM_MAX_COMMANDS] = {0}; /* 开始测量命令需要多个TX_DATA地址 */
    uint8 RetVal;
    uint8 i;
    uint8 TxDataCount;

    /* 添加开始测量命令 */
    RetVal = CrmSpiMapping_AddStartMeasurement(&CrmConfig, MeasureFrame);
    if (RetVal != 0)
    {
        return SPI_TRANS_INVALID;
    }

    /* 将CRM命令映射到TX_DATA */
    RetVal = CrmSpiMapping_MapToTxData(&CrmConfig, &TxDataMap);
    if (RetVal != 0)
    {
        return SPI_TRANS_INVALID;
    }

    /* 计算需要的TX_DATA地址数量 */
    if (CrmConfig.TotalDataSize % CRM_COMMAND_SIZE == 0)
    {
        /* 每个TX_DATA可以存储4个字节 */
        TxDataCount = CrmConfig.TotalDataSize / CRM_COMMAND_SIZE;
    }
    else
    {
        /* 每个TX_DATA可以存储4个字节 */
        TxDataCount = CrmConfig.TotalDataSize / CRM_COMMAND_SIZE + 1;
    }

    /* 将TX_DATA映射到SPI协议 */
    for (i = 0; i < TxDataCount; i++)
    {
        RetVal = CrmSpiMapping_MapToSpiProtocol(Dsi3Ch, &TxDataMap, &SpiCmd[i], ADDR_CRM_DATA_0 + i);
        if (RetVal != 0)
        {
            return SPI_TRANS_INVALID;
        }
    }

    /* 发送SPI命令 */
    for (i = 0; i < TxDataCount; i++)
    {
        // RetVal = SpiCom_AsyncTransferEx(SpiCmd[i].SpiCmdData, SPI_CMD_LENS, 1, 0, 3, NonReadCommandCallback, NULL);
        if (SpiCmd_WriteByAddress(SpiCmd[i].ReadAndWrite_t.Channel, SpiCmd[i].ReadAndWrite_t.Addr, SpiCmd[i].ReadAndWrite_t.Payload.Bytes) != SPI_TRANS_SUCC)
        {
            return SPI_TRANS_INVALID;
        }
    }

    return SPI_TRANS_SUCC;
}

/******************************************************************************
 * @brief      通过页面索引写入
 * @param[in]  Dsi3Ch DSI3通道
 * @param[in]  Dsi3Id 从机ID
 * @param[in]  PageIndex 页面索引
 * @param[in]  Data 数据
 * @return     传输状态
 * <AUTHOR>
 * @date       2025-05-15
 * @note       
 *****************************************************************************/
SpiTransReturn_t RdumRdusCrm_WriteByPageIndex(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, RdumRdusPageIndex_t PageIndex, uint8 *Data)
{
    CrmMapConfig_t CrmConfig = {0};
    uint8 RetVal;
    uint16 Data16;

    RetVal = RdumRdusCrm_WriteRwp(Dsi3Ch, Dsi3Id, PDCM_UPDATE_DO_NOT_MODIFY, CRM_UPDATE_NEW, PageIndex.Page, PageIndex.Index, 0);
    if (RetVal != SPI_TRANS_SUCC)
    {
        return SPI_TRANS_INVALID;
    }

    CrmConfig.CommandCount++;

    Data16 = (uint16)Data[0] << 8 | (uint16)Data[1];

    RetVal = RdumRdusCrm_Write(Dsi3Ch, Dsi3Id, Data16, &CrmConfig);
    if (RetVal != SPI_TRANS_SUCC)
    {
        return SPI_TRANS_INVALID;
    }

    return SPI_TRANS_SUCC;
}