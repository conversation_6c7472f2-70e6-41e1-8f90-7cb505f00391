/******************************************************************************
 * @file       SpiCmd.h
 * @brief
 * @date       2025-03-21 14:20:04
 * <AUTHOR>
 * @copyright  Longhorn Automotive Electronics Technology (Xiamen) Co., Ltd.
 *****************************************************************************/


#ifndef __SPI_CMD_H__
#define __SPI_CMD_H__
/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "types.h"
#include "SpiCom_Prg.h"
#include "SpiPayloadDef.h"
#include "BrcSlotDef.h"

/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/
#define SPI_CMD_LENS 7                      /** @brief SPI命令长度 */
#define SPI_CMD_CRC_LENS (SPI_CMD_LENS - 1) /** @brief 需要CRC校验长度 */

#define CRC8_C2_POLYNOMIAL 0x2F
#define CRC8_C2_INIT_SPI_AND_CRC_SD 0xFF
#define CRC8_C2_INIT_DSI3 0x42
#define CRC8_C2_INIT_SLOT1 0x2F
#define CRC8_C2_INIT_SLOT2 0x5E
#define CRC8_C2_INIT_SLOT3 0x42
#define CRC8_C2_INIT_SLOT4 0x42
#define CRC8_C2_INIT_SLOT5 0x2F
#define CRC8_C2_INIT_SLOT6 0x5E
#define CRC8_C2_INIT_SLOT7 0x42
#define CRC8_C2_INIT_SLOT8 0x42
/******************************************************************************
 * @Type Definitions
 *****************************************************************************/
typedef enum {
    RDUM_MASTER0 = 0,
    RDUM_MASTER1,
    RDUM_MASTER_NUM,
} RdumMaster_t;

/* SPI命令类型 */
typedef enum
{
    NO_OPERATION = 0x01,              /* 无操作,用于读取返回数据 */
    WRITE_BY_ADDRESS = 0x02,          /* 按地址写入 */
    READ_BY_ADDRESS = 0x03,           /* 按地址读取 */
    GET_CRM_RESP = 0x04,              /* 获取CRM响应 */
    GET_PDCM_RESP = 0x05,             /* 获取PDCM响应 */
    RUN_PAUSE_CYCLE = 0x06,           /* 运行/暂停周期 */
    GET_ALL_BRC = 0x07,               /* 获取所有BRC */
} SpiCommand_t;

/* SPI通道 */
typedef enum
{
    DSI3_CHANNEL_A = 0,  /* DSI3通道A */
    DSI3_CHANNEL_B = 1,  /* DSI3通道B */
    DSI3_CHANNEL_NUM,    /* DSI3通道数量 */
} Dsi3Channel_t;

#pragma pack(push, 1) // 设置为 1 字节对齐
/* SPI协议（高字节在前：整字节定义的时候高字节在前，当时bit位定义的时候低位在前） */
// typedef union
// {
//     uint8 SpiCmdData[SPI_CMD_LENS]; /* SPI命令数据 */
    
//     struct
//     {
//         SpiCommand_t Command : 5;  /* [52:48] SPI命令 */
//         uint8 Channel : 1;         /* [53] 0: ChannelA 1: ChannelB */
//         uint8 Kac : 2;             /* [55:54] Keep-Alive Counter */

//         uint8 Addr;                /* [47:40] 命令参数地址 */
//         SpiPayloadData_t Payload;  /* [39:8] 命令参数定义 */
//         uint8 Crc;                 /* [7:0]CRC校验 */
//     } ReadAndWrite_t;

//     struct
//     {
//         SpiCommand_t Command : 5; /* [52:48] SPI命令 */
//         uint8 Channel : 1;        /* [53] 0: ChannelA 1: ChannelB */
//         uint8 Kac : 2;            /* [55:54] Keep-Alive Counter */

//         uint8 SlotIndex;          /* [47:40] slot槽索引 */
//         uint8 Reserved[4];        /* [39:8] 保留 */
//         uint8 Crc;                /* [7:0]CRC校验 */
//     } GetPdcmResp_t;

//     struct
//     {
//         SpiCommand_t Command : 5; /* [52:48] SPI命令 */
//         uint8 Channel : 1;        /* [53] 0: ChannelA 1: ChannelB */
//         uint8 Kac : 2;            /* [55:54] Keep-Alive Counter */

//         uint8 NewTimerHigh;       /* [47:40] NEW_TIMER高位 */
        
//         uint8 Reserved : 7;       /* [38:32] 保留 */
//         uint8 NewTimerLow : 1;    /* [39] NEW_TIMER低位 */
        
//         uint8 Reserved2[3];       /* [31:8] 保留 */
//         uint8 Crc;                /* [7:0]CRC校验 */
//     } RunPauseCycle_t;
// } SpiProtocol_t;
typedef union
{
    uint8 SpiCmdData[SPI_CMD_LENS]; /* SPI命令数据 */

    struct
    {
        uint8 Crc;                /* [7:0]CRC校验 */
        SpiPayloadData_t Payload; /* [39:8] 命令参数定义 */
        uint8 Addr;               /* [47:40] 命令参数地址 */
        SpiCommand_t Command : 5; /* [52:48] SPI命令 */
        uint8 Channel : 1;        /* [53] 0: ChannelA 1: ChannelB */
        uint8 Kac : 2;            /* [55:54] Keep-Alive Counter */
                // SpiCommand_t Command : 5;  /* [52:48] SPI命令 */
                // uint8 Channel : 1;         /* [53] 0: ChannelA 1: ChannelB */
                // uint8 Kac : 2;             /* [55:54] Keep-Alive Counter */

                // uint8 Addr;                /* [47:40] 命令参数地址 */
                // SpiPayloadData_t Payload;  /* [39:8] 命令参数定义 */
                // uint8 Crc;                 /* [7:0]CRC校验 */
    } ReadAndWrite_t;

    struct
    {
        SpiCommand_t Command : 5; /* [52:48] SPI命令 */
        uint8 Channel : 1;        /* [53] 0: ChannelA 1: ChannelB */
        uint8 Kac : 2;            /* [55:54] Keep-Alive Counter */

        uint8 SlotIndex;   /* [47:40] slot槽索引 */
        uint8 Reserved[4]; /* [39:8] 保留 */
        uint8 Crc;         /* [7:0]CRC校验 */
    } GetPdcmResp_t;

    struct
    {
        SpiCommand_t Command : 5; /* [52:48] SPI命令 */
        uint8 Channel : 1;        /* [53] 0: ChannelA 1: ChannelB */
        uint8 Kac : 2;            /* [55:54] Keep-Alive Counter */

        uint8 NewTimerHigh; /* [47:40] NEW_TIMER高位 */

        uint8 Reserved : 7;    /* [38:32] 保留 */
        uint8 NewTimerLow : 1; /* [39] NEW_TIMER低位 */

        uint8 Reserved2[3]; /* [31:8] 保留 */
        uint8 Crc;          /* [7:0]CRC校验 */
    } RunPauseCycle_t;
} SpiProtocol_t;
#pragma pack(pop) // 恢复之前的对齐设置

/******************************************************************************
 * @Const Declaration
 *****************************************************************************/



/******************************************************************************
 * @Variable Declaration
 *****************************************************************************/
extern const uint8 CRC8_C2_INIT_TABLE[MAX_SLOT_COUNT];

/******************************************************************************
 * @Function Declaration
 *****************************************************************************/
SpiTransReturn_t SpiCmd_NoOperation(Dsi3Channel_t Dsi3Ch, uint8 Addr, uint8 *UserData);
SpiTransReturn_t SpiCmd_WriteByAddress(Dsi3Channel_t Dsi3Ch, uint8 Addr, uint8 *TxData);
SpiTransReturn_t test_SpiCmd_WriteByAddress(Dsi3Channel_t Dsi3Ch, uint8 Addr, uint32 test);
SpiTransReturn_t SpiCmd_ReadByAddress(Dsi3Channel_t Dsi3Ch, uint8 Addr);
SpiTransReturn_t SpiCmd_GetCrmResp(Dsi3Channel_t Dsi3Ch, uint8 IsResponseNeeded, uint8 DataLen, uint8 *UserData);
SpiTransReturn_t SpiCmd_GetPdcmResp(Dsi3Channel_t Dsi3Ch, uint8 SlotIndex, uint16 DelayMs, uint8 *UserData);
SpiTransReturn_t SpiCmd_RunPauseCycle(Dsi3Channel_t Dsi3Ch, uint16 NewTime);
SpiTransReturn_t SpiCmd_GetAllBrc(Dsi3Channel_t Dsi3Ch, uint8 SlotCount, uint16 DelayMs, uint8 *UserData);

#endif
