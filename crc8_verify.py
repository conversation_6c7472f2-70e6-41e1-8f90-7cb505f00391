import sys

def calculate_crc8(data, polynomial, init_value):
    crc = init_value
    
    # 初始化CRC表
    crc_table = []
    for i in range(256):
        crc_value = i
        for j in range(8):
            if crc_value & 0x80:
                crc_value = (crc_value << 1) ^ polynomial
            else:
                crc_value <<= 1
            crc_value &= 0xFF  # 确保结果是8位
        crc_table.append(crc_value)
    
    # 计算CRC
    for byte in data:
        crc = crc_table[crc ^ byte]
    
    return crc

# 已知多项式和初始值
polynomial = 0x2F  # x8+x5+x3+x2+x+1
init_value = 0x42  # 推导出的初始值

# 解析示例数据
def parse_hex_string(hex_string):
    # 移除所有空白字符
    hex_string = hex_string.strip()
    
    # 将字符串转换为字节列表
    return [int(hex_string[i:i+2], 16) for i in range(0, len(hex_string), 2)]

# 示例数据
examples = [
    'F809301F',
    'F46F34D7343DEF3DEF1FBF002879',
    'F8080A22'
]

print('验证CRC8初始值...')
print(f'多项式: 0x{polynomial:02X} (x8+x5+x3+x2+x+1)')
print(f'初始值: 0x{init_value:02X}')
print()

for example in examples:
    data_with_crc = parse_hex_string(example)
    data = data_with_crc[:-1]
    expected_crc = data_with_crc[-1]
    
    # 计算CRC8
    calculated_crc = calculate_crc8(data, polynomial, init_value)
    
    print(f'示例: {example}')
    print(f'数据: {[f"0x{b:02X}" for b in data]}')
    print(f'预期CRC8: 0x{expected_crc:02X}')
    print(f'计算CRC8: 0x{calculated_crc:02X}')
    print(f'验证结果: {"成功" if calculated_crc == expected_crc else "失败"}')
    print()

# 测试一个新的例子
new_example = 'F8080A'  # 不包含CRC8
new_data = parse_hex_string(new_example)
new_crc = calculate_crc8(new_data, polynomial, init_value)

print(f'新示例: {new_example}')
print(f'数据: {[f"0x{b:02X}" for b in new_data]}')
print(f'计算CRC8: 0x{new_crc:02X}')
print(f'完整数据+CRC8: {new_example}{new_crc:02X}')
