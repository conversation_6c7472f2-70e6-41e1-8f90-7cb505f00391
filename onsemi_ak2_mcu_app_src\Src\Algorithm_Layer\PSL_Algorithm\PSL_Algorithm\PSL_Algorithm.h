/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * PSL_Algorithm.h: 
 * Created on: 2023-2-22 17:30
 * Original designer: 22866
 ******************************************************************************/


#ifndef PSL_Algorithm_H
#define PSL_Algorithm_H



/* ============================================================================================== */
/* Header files																				      */
/* ============================================================================================== */
#include "PSL_Algorithm_Types.h"
#include "PSL_Algorithm_Callback.h"
#include "PSL_Algorithm_Cfg.h"
#include "CAN_AppSignalManage.h"


/******************************************************************************/
/******************************************************************************/
/********************************* 调试打印 ***********************************/
/******************************************************************************/
/******************************************************************************/

#define   PSL_DATA_PRINT_SWITCH              (STD_OFF)




/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/

#define PSL_OBJ_CLUSTER_NUM     30
#define PSL_CLUSTER_VALID_CNT   3
#define PSL_CLUSTER_INVALID_CNT 3
#define PSL_RECORD_OBJ_MAX      40
#define PSL_AVERAGE_ECHO_MAX    4
#define PSL_RECORD_CURB_MAX     40
#define PSL_RECORD_MIN_DOS      50.0
#define PSL_ECHO_DIFF_MAX       300
#define PSL_MVOE_OBJ_MIN_DIS    1500
#define PSL_DET_INVALID_CNT     0
#define PSL_DET_VALID_CNT       2
#define PSL_MVOE_CURB_MIN_DIS   250
#define PSL_DET_OBJ2_START_DIS  3500
#define PSL_DET_OBJ2_END_DIS    1500
#define PSL_INVALID_VALUE       0
#define PSL_SLOT_DEPTH_ERROR    2800
#define PSL_SLOT_DEPTH_MAX      2500
#define PSL_ECHO_50CM_VALUE     500
#define PSL_ECHO_30CM_VALUE     300
#define PSL_ECHO_25CM_VALUE     250
#define PSL_ECHO_20CM_VALUE     200
#define PSL_ECHO_15CM_VALUE     150
#define PSL_ECHO_12CM_VALUE     120
#define PSL_ECHO_10CM_VALUE     100
#define PSL_ECHO_8CM_VALUE      80
#define PSL_ECHO_6CM_VALUE      60
#define PSL_OBJ_INVALID_DIS     1500
#define PSL_RECORD_TRAIL_DIS    600.0
#define PSL_lIS_TRAIL_MAX_DIS   2500.0
#define PSL_DET_CAR_SPEED       800
#define PSL_FILL_OBJ2_DATA      1
#define PSL_SNS_MEAS_CHIRP_MIN  600
#define MAX_FITS_CNT			40
#define FITS_N_ST_ORDER			1

/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/

/* 定义PSL检测状态 */
typedef enum
{
    PSL_DETE_INIT = 0,
    PSL_DETE_IDLE,
    PSL_DETE_OBJ1,
    PSL_DETE_CURB,
    PSL_DETE_OBJ2,    
    PSL_DETE_END
}PSLDeteStatusType;

/* 定义PSL检测数据记录 */
typedef enum
{
    PSL_DETE_ONE = 0,
    PSL_DETE_SECOND,
    PSL_DETE_THIRD,
    PSL_DETE_FOURTH,    
    PSL_DETE_FIFTH,
    PSL_DETE_NUMBER
}PSLDeteDataType;


/* 定义PSL计算斜率类型 */
typedef enum
{
    PSL_CAL_SLOPE_OBJ1 = 0,
    PSL_CAL_SLOPE_OBJ2,
    PSL_CAL_SLOPE_CURB,
    PSL_CAL_SLOPE_MAX
}PSLCalSlopeType;

/* 定义PSL SLOT OBJ类型 */
typedef enum
{
    PSL_SLOT_TWOOBJ = 0,
    PSL_SLOT_ONLYOBJ1,
    PSL_SLOT_ONLYOBJ2
}PSLSlotObjType;
    
/* 定义车位拐点OBJ类型         */
typedef enum
{
    PSL_OBJ_TYPE_NONE = 0,
    PSL_OBJ_TYPE_VIRTUAL,
    PSL_OBJ_TYPE_REAL,
    PSL_OBJ_TYPE_RESERVED
}SLOT_OBJ_TYPE;

/* 定义PSL SLOT 类型 */
typedef enum
{
    PSL_SLOT_NONE = 0,
    PSL_SLOT_LEFT_HORIZONTAL,
    PSL_SLOT_RIGHT_HORIZONTAL,
    PSL_SLOT_LEFT_VERTICAL,
    PSL_SLOT_RIGHT_VERTICAL,
    PSL_SLOT_RESERVE
}PSLDetSlotType;

/* 定义PSL SLOT Depth 类型 */
typedef enum
{
    PSL_DEPTH_NONE = 0,
    PSL_DEPTH_CURB,
    PSL_DEPTH_WALL,
    PSL_DEPTH_VIRTUAL,
    PSL_DEPTH_LOW,
    PSL_SLOT_HIGH,
    PSL_SLOT_UNKNOW,
    PSL_SLOT_MAX
}PSLDetSlotDepthType;
/* 定义OBJ   TYPE 类型 */
typedef enum
{
    OBJ_SHAPE_NONE = 0,
    OBJ_SHAPE_SQUARE,
    OBJ_SHAPE_ROUND,
    OBJ_TSHAPE_MAX
}PSLDetObjShape;

typedef enum
{
    SLOT_HAVE_NONE = 0,
    SLOT_HAVE_OBJ
}PSLSlotHaveObjType;

/* 定义PSL车辆位置 */
typedef enum
{
    PSL_CAR_POSITION_1 = 0,
    PSL_CAR_POSITION_2,
    PSL_CAR_POSITION_3,
    PSL_CAR_POSITION_4
}PSLCarPositionType;

/* 拟合数据 */
typedef struct
{
	double coeff[FITS_N_ST_ORDER+1];
	double error;									
} 
SlotAlgorPolyfit_strType;

/* 定义PSL缓存OBJ及回波数据 */
typedef struct
{
    PSLObjCoorType strSnsLeftObj[PSL_SNS_CH_NUM][PSL_DETE_NUMBER];
    PSLObjCoorType strSnsMasterObj[PSL_SNS_CH_NUM][PSL_DETE_NUMBER];
    PSLObjCoorType strSnsRightObj[PSL_SNS_CH_NUM][PSL_DETE_NUMBER];
    uint16 u16MasterDis[PSL_SNS_CH_NUM][PSL_DETE_NUMBER];
    uint16 u16MasterHeight[PSL_SNS_CH_NUM][PSL_DETE_NUMBER];
    PSLSnsMeasTypeType enuMeasMode[PSL_SNS_CH_NUM][PSL_DETE_NUMBER];
}PSLRecordObjCoorType;

/* 定义PSL检测 Struct     */
typedef struct
{
    PSLDeteStatusType enuPSLDetSts[PSL_SNS_CH_NUM]; /* PSL检测状态 */
    PSLSlotObjType LenuSlotObjType[PSL_SNS_CH_NUM];
    PSLDetSlotType LenuSlotType[PSL_SNS_CH_NUM];
    SLOT_OBJ_TYPE  LenuFirObjType[PSL_SNS_CH_NUM];
    SLOT_OBJ_TYPE  LenuSecObjType[PSL_SNS_CH_NUM];
    PSLDetObjShape LenuFirObjShape[PSL_SNS_CH_NUM];
    PSLDetObjShape LenuSecObjShape[PSL_SNS_CH_NUM];
    float fCurEchoAverage[PSL_SNS_CH_NUM]; /* OBJ1当前回波均值 */
    float fFirstObjMinDisX[PSL_SNS_CH_NUM]; /* OBJ1最近点坐标 */
    float fFirstObjMinDisY[PSL_SNS_CH_NUM]; /* OBJ1最近点坐标 */
    float fSecObjMinDisX[PSL_SNS_CH_NUM]; /* OBJ2最近点坐标 */
    float fSecObjMinDisY[PSL_SNS_CH_NUM]; /* OBJ2最近点坐标 */
    float fFirstObjMinDis[PSL_SNS_CH_NUM]; /* OBJ1最近距离 */
    float fSecObjMinDis[PSL_SNS_CH_NUM]; /* OBJ2最近距离 */
    float fFirstObjCornerX[PSL_SNS_CH_NUM]; /* OBJ1拐点坐标 */
    float fFirstObjCornerY[PSL_SNS_CH_NUM]; /* OBJ1拐点坐标 */
    float fSecObjCornerX[PSL_SNS_CH_NUM]; /* OBJ2拐点坐标 */
    float fSecObjCornerY[PSL_SNS_CH_NUM]; /* OBJ2拐点坐标 */
    uint8 u8FirObjInvalidcnt[PSL_SNS_CH_NUM]; /* OBJ1无效计数 */
    float fRecObj1EchoDis[PSL_SNS_CH_NUM][PSL_RECORD_OBJ_MAX];
    float fRecObj2EchoDis[PSL_SNS_CH_NUM][PSL_RECORD_OBJ_MAX];    
    float fObj2RawData[PSL_SNS_CH_NUM][PSL_RECORD_OBJ_MAX];
    uint8 u8RecObj2RawCnt[PSL_SNS_CH_NUM];
    PSLObjCoorType strRecObj1Odo[PSL_SNS_CH_NUM][PSL_RECORD_OBJ_MAX];
    float fRecObj1OdoAngle[PSL_SNS_CH_NUM][PSL_RECORD_OBJ_MAX];
    float fRecObj2OdoAngle[PSL_SNS_CH_NUM][PSL_RECORD_OBJ_MAX];
    PSLObjCoorType strObj1Data[PSL_SNS_CH_NUM][PSL_RECORD_OBJ_MAX];/* 用于计算拐点及斜率OBJ1数据 */
    PSLObjCoorType strObj2Data[PSL_SNS_CH_NUM][PSL_RECORD_OBJ_MAX];/* 用于计算拐点及斜率OBJ1数据 */
    PSLObjCoorType strObj2OdoCoor[PSL_SNS_CH_NUM][PSL_RECORD_OBJ_MAX];
    float fSlopeData[PSL_SNS_CH_NUM][PSL_RECORD_OBJ_MAX];/* 用于计算斜率数据 */
    uint8 u8SlopeCnt[PSL_SNS_CH_NUM];/* 用于计算斜率数据 */
    float fObj1Slope[PSL_SNS_CH_NUM];/* OBJ1斜率 */
    float fObj2Slope[PSL_SNS_CH_NUM];/* OBJ2斜率 */
    PSLObjCoorType strCurbOdoCoor[PSL_SNS_CH_NUM][PSL_RECORD_CURB_MAX];
    uint8 u8RecordObjCnt[PSL_SNS_CH_NUM];
    uint8 u8ObjMinIndex[PSL_SNS_CH_NUM];
    uint8 u8RecordObj2Cnt[PSL_SNS_CH_NUM];
    uint8 u8Obj2MinIndex[PSL_SNS_CH_NUM];
    float fObj1BackUpCarCoorx[PSL_SNS_CH_NUM];
    float fObj1BackUpCarCoory[PSL_SNS_CH_NUM];
    float fRecObj1CornerOdox[PSL_SNS_CH_NUM];
    float fRecObj1CornerOdoy[PSL_SNS_CH_NUM];
    float fBackUpCarCoorx[PSL_SNS_CH_NUM];
    float fBackUpCarCoory[PSL_SNS_CH_NUM];
    PSLObjCoorType strPslStartCoor[PSL_SNS_CH_NUM];/* 本次找车位的起始坐标 */
    PSLObjCoorType strCurbCoorData[PSL_SNS_CH_NUM][PSL_RECORD_CURB_MAX];/* 用于计算CURB深度的数据 */
    uint16 u16CurbDepthData[PSL_SNS_CH_NUM][PSL_RECORD_CURB_MAX];/* 用于计算CURB深度的数据 */
    uint8 u8CurbDataCnt[PSL_SNS_CH_NUM];
    uint16 u16CurbDepth[PSL_SNS_CH_NUM];
    float fCurbSlope[PSL_SNS_CH_NUM];/* CURB斜率 */
    uint8 fCurbDataInitFlag[PSL_SNS_CH_NUM];
    uint16 u16SlotWidth[PSL_SNS_CH_NUM];
    float fRecordCarSlope[PSL_SNS_CH_NUM][PSL_CAL_SLOPE_MAX];
    float fFirstObjTrailX[PSL_SNS_CH_NUM]; 
    float fFirstObjTrailY[PSL_SNS_CH_NUM]; 
    PSLSlotHaveObjType u8PSLSlotDetedObj[PSL_SNS_CH_NUM]; 
}PSLDetermineDataType;
/* 定义原始数据处理中用到的Odo坐标 */
typedef struct
{
    uint8 u8PSLUseOdoID;
    uint8 u8PSLUseOdoInit;
    float fCarCoorX;
    float fCarCoorY;
    float fCarYawAngle;
    float fCarSinYawAngle;
    float fCarCosYawAngle;
}PSLUseOdoType;

typedef struct
{
    float    dPointOneWx;
    float    dPointOneWy;
    float    dPointTwoWx;
    float    dPointTwoWy;
    float    dSlope;
    float    dDis;
}PSLCalPointBetweenDisType;

/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
extern PSLUseOdoType GstrPSLUseOdoData;


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
extern void PSLDeteSlotDataInit(void);
extern void PSLAlgorithm_Task();
extern void PSLResetOdoCoor(void);
extern float PSLCalSlopeDec(float LfSlope1,float LfSlope2);
#endif

