#include "AppQueue.h"

uint8 Gu8aDataBuf_UartPrnt[QUEUE_UART_PRINT_BUF_SIZE];
uint8 Gu8aDataBuf_UartDebugRx[QUEUE_UART_DEBUG_RX_BUF_SIZE];

QueueInfoType GstraQueueInfo[QUEUE_NUM] = {
    {
        QUEUE_UART_PRINT_BUF_SIZE,
        0u,
        0u,
        0u,
        Gu8aDataBuf_UartPrnt
    },
    {
        QUEUE_UART_DEBUG_RX_BUF_SIZE,
        0u,
        0u,
        0u,
        Gu8aDataBuf_UartDebugRx
    }
};