/******************************************************************************
 * @file       System_Schedule_cfg.h
 * @brief      
 * @date       2025-03-04 13:40:06
 * <AUTHOR>
 * @copyright  Longhorn Automotive Electronics Technology (Xiamen) Co., Ltd.
 *****************************************************************************/


#ifndef __SYSTEM_SCHEDULE_CFG__
#define __SYSTEM_SCHEDULE_CFG__
/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "System_Schedule_types.h"



/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/
typedef struct
{
    SCHED_tpfTask   pfTask;
    uint16          u16TaskDelay;
    uint16          u16TaskPeriod;
} SCHED_tstrTaskControlBlockConf;



/******************************************************************************
 * @Const Declaration
 *****************************************************************************/
extern const SCHED_tstrTaskControlBlockConf GastrPeriodSchedConfig[SCHED_TASK_ID_NUM];



/******************************************************************************
 * @Variable Declaration
 *****************************************************************************/



/******************************************************************************
 * @Function Declaration
 *****************************************************************************/
extern void NoperiodFunc(void);



#endif
