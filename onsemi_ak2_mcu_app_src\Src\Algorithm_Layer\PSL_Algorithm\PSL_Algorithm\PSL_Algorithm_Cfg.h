/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * PSL_Algorithm.h: 
 * Created on: 2023-3-1 16:30
 * Original designer: 22866
 ******************************************************************************/


#ifndef PSL_Algorithm_Cfg_H
#define PSL_Algorithm_Cfg_H



/* ============================================================================================== */
/* Header files																				      */
/* ============================================================================================== */
#include "PSL_Algorithm_Types.h"
#include "debug.h"





/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/

#define PSL_DET_INVALID_COOR    0.0
#define PSL_DET_INVALID_DATA    0.0
#define PSL_ODO_INVALID_DATA    0.0
#define PSL_ECHO_INVALID_DATA   0xFFFF

#define PSL_CURB_DEPTH_MIN      2500
#define PSL_CURB_DEPTH_INVALID  5000
#define PSL_HEIGHT_INVALID      0

#define PSL_OUTPUT_SLOT_MAX     8

#define PSL_ANGLE_CONVERT_PARAMETER 57.29578

/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/
/******************************************************************************/
/**
 * @brief   µ÷ÊÔ´òÓ¡
 */
/******************************************************************************/
#define   PSL_USE_DEBUG_PRINT              (STD_OFF)

#if(PSL_USE_DEBUG_PRINT == STD_ON)
#define PSL_DEBUG_PRINTF	            DEBUG_PRINT
#else
#define PSL_DEBUG_PRINTF(...)   
#endif






/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/




/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/

#endif
