/******************************************************************************
 * @file      example.c
 * @brief     安森美超声波探头异步数据采集示例
 * <AUTHOR> @date      2025-05-20
 * @note      实现异步方式的参数配置、数据读取和收发波流程
 *****************************************************************************/

/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include <stdio.h>
#include <string.h>
#include "BaseDrv.h"
#include "RdumRdusCrm.h"
#include "RdumRdusBrc.h"
#include "SpiCom_Prg.h"
#include "BrcSlotDef.h"
#include "SpiCmd.h"

/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/
#define MAX_CYCLE_SLOTS             11      /* 一个完整周期最大slot数 */
#define MEAS_DURATION_VALUE         60      /* 测量持续时间配置值 (60*512us = 30.72ms) */
#define FIRST_BRC_DELAY_MS          5       /* 第一次BRC命令延迟时间(ms) */
#define BRC_INTERVAL_MS             4       /* BRC命令间隔时间(ms) */
#define DIAGNOSTIC_SLOTS            1       /* 诊断slot数量 */
#define TOTAL_BRC_COMMANDS          11      /* 总共需要的BRC命令数 */

/* 探头行为定义 */
#define PROBE_SELF_TRANSMIT         0       /* 自发自收 */
#define PROBE_LEFT_LISTEN           1       /* 左侦听 */
#define PROBE_RIGHT_LISTEN          2       /* 右侦听 */
#define PROBE_NO_ACTION             3       /* 无动作 */

/******************************************************************************
 * @Type Definitions
 *****************************************************************************/
/* 探头配置结构体 */
typedef struct {
    uint8 SID1_BEH_CH_H;
    uint8 SID1_BEH_CH_L;
    uint8 SID2_BEH_CH_H;
    uint8 SID2_BEH_CH_L;
    uint8 SID3_BEH_CH_H;
    uint8 SID3_BEH_CH_L;
    uint8 SID4_BEH_CH_H;
    uint8 SID4_BEH_CH_L;
    uint8 SID5_BEH_CH_H;
    uint8 SID5_BEH_CH_L;
    uint8 SID6_BEH_CH_H;
    uint8 SID6_BEH_CH_L;
} ProbeConfig_t;

/* 回调函数上下文 */
typedef struct {
    uint8 CommandIndex;                     /* 命令索引 */
    uint8 ExpectedKac;                      /* 期望的KAC值 */
    uint8 SlotCount;                        /* 当前命令的slot数量 */
    uint8 TotalSlots;                       /* 总slot数量 */
    uint8 CompletedCommands;                /* 已完成的命令数量 */
    BrcResult_t Results[TOTAL_BRC_COMMANDS]; /* 所有BRC结果 */
    uint8 IsComplete;                       /* 是否完成所有命令 */
} BrcCommandContext_t;

/******************************************************************************
 * @Variable Definitions
 *****************************************************************************/
/* 探头配置 */
static ProbeConfig_t g_ProbeConfig = {
    .SID1_BEH_CH_H = PROBE_RIGHT_LISTEN,    /* 探头1高通道是右侦听 */
    .SID1_BEH_CH_L = PROBE_NO_ACTION,       /* 探头1低通道无动作 */
    .SID2_BEH_CH_H = PROBE_SELF_TRANSMIT,   /* 探头2高通道是自发自收 */
    .SID2_BEH_CH_L = PROBE_RIGHT_LISTEN,    /* 探头2低通道是右侦听 */
    .SID3_BEH_CH_H = PROBE_LEFT_LISTEN,     /* 探头3高通道是左侦听 */
    .SID3_BEH_CH_L = PROBE_SELF_TRANSMIT,   /* 探头3低通道是自发自收 */
    .SID4_BEH_CH_H = PROBE_NO_ACTION,       /* 探头4高通道无动作 */
    .SID4_BEH_CH_L = PROBE_LEFT_LISTEN,     /* 探头4低通道是左侦听 */
    .SID5_BEH_CH_H = PROBE_RIGHT_LISTEN,    /* 探头5高通道是右侦听 */
    .SID5_BEH_CH_L = PROBE_NO_ACTION,       /* 探头5低通道无动作 */
    .SID6_BEH_CH_H = PROBE_SELF_TRANSMIT,   /* 探头6高通道是自发自收 */
    .SID6_BEH_CH_L = PROBE_NO_ACTION,       /* 探头6低通道无动作 */
};

/* 全局BRC命令上下文 */
static BrcCommandContext_t g_BrcContext;

/* 有效的slot索引数组 */
static uint8 g_ValidSlots[MAX_SLOT_COUNT];
static uint8 g_ValidSlotCount = 0;

/******************************************************************************
 * @Function Declarations
 *****************************************************************************/
static void BrcCommandCallback(SpiTxSeqId_t TxSeqId, SpiTxStatus_t Status, void* UserData);
static uint8 ConfigureMeasurementParameters(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id);
static uint8 StartMeasurement(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id);
static uint8 CollectCompleteCycleData(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id);
static void AnalyzeProbeConfiguration(void);
static void ProcessBrcResults(void);
static void DecompressAndAnalyzeEnvelopes(void);
static void PrintDiagnosticData(void);
static void PrintSlotData(SlotData_t* SlotData, uint8 SlotIndex);

/******************************************************************************
 * @Function Definitions
 *****************************************************************************/

/******************************************************************************
 * @brief      BRC命令回调函数
 * @param[in]  TxSeqId 传输序号
 * @param[in]  Status 传输状态
 * @param[in]  UserData 用户数据
 * <AUTHOR> @date       2025-05-20
 * @note       处理BRC命令完成事件
 *****************************************************************************/
static void BrcCommandCallback(SpiTxSeqId_t TxSeqId, SpiTxStatus_t Status, void* UserData)
{
    BrcCommandContext_t* Context = (BrcCommandContext_t*)UserData;
    BrcResult_t* Result = &Context->Results[Context->CommandIndex];
    uint8 i;

    if (Status == SPI_TX_READY) {
        /* 检查KAC是否符合预期 */
        for (i = 0; i < Context->SlotCount; i++) {
            uint8 Kac = Result->SlotData[i].Kac;
            if (Kac != Context->ExpectedKac) {
                printf("警告: 命令%d的Slot%d KAC值不符合预期，预期:%d，实际:%d\n",
                       Context->CommandIndex, i, Context->ExpectedKac, Kac);
            }
        }

        /* 更新下一个命令的预期KAC值 */
        Context->ExpectedKac = (Context->ExpectedKac + 2) % 16;

        /* 更新已完成的命令数量 */
        Context->CompletedCommands++;

        /* 检查是否完成所有命令 */
        if (Context->CompletedCommands >= TOTAL_BRC_COMMANDS) {
            Context->IsComplete = 1;
            printf("所有BRC命令已完成，共%d个命令\n", Context->CompletedCommands);
        }
    } else {
        printf("BRC命令%d失败，状态: %d\n", Context->CommandIndex, Status);
    }
}

/******************************************************************************
 * @brief      配置测量参数
 * @param[in]  Dsi3Ch 通道
 * @param[in]  Dsi3Id 从机ID
 * @return     0: 成功, 非0: 失败
 * <AUTHOR> @date       2025-05-20
 * @note       配置超声波探头的测量参数
 *****************************************************************************/
static uint8 ConfigureMeasurementParameters(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id)
{
    uint8 RetVal;
    uint16 Data;

    printf("配置测量参数...\n");

    /* 配置测量持续时间 */
    Data = MEAS_DURATION_VALUE;
    RetVal = RdumRdusCrm_Write8BitByPageIndex(Dsi3Ch, Dsi3Id, PAGE0_IDX_MEAS_DURATION, Data);
    if (RetVal != DIS3_TRANS_SUCC) {
        printf("配置测量持续时间失败，错误码: %d\n", RetVal);
        return RetVal;
    }
    printf("配置测量持续时间: %d (%.2fms)\n", MEAS_DURATION_VALUE, MEAS_DURATION_VALUE * 0.512);

    /* 等待配置完成 */
    BaseDrv_DelayMs(10);

    return 0;
}

/******************************************************************************
 * @brief      开始测量
 * @param[in]  Dsi3Ch 通道
 * @param[in]  Dsi3Id 从机ID
 * @return     0: 成功, 非0: 失败
 * <AUTHOR> @date       2025-05-20
 * @note       发送开始测量命令
 *****************************************************************************/
static uint8 StartMeasurement(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id)
{
    CrmMapConfig_t CrmConfig;
    TxDataMapping_t TxDataMap;
    SpiProtocol_t SpiCmd[4];
    uint8 RetVal;
    uint8 i;
    uint8 TxDataCount;
    SpiTxSeqId_t TxSeqId;

    printf("开始测量...\n");

    /* 初始化CRM配置 */
    memset(&CrmConfig, 0, sizeof(CrmConfig));

    /* 添加开始测量命令 */
    RetVal = CrmSpiMapping_AddStartMeasurement(&CrmConfig, Dsi3Id, MEAS_MODE0);
    if (RetVal != 0) {
        printf("添加开始测量命令失败，错误码: %d\n", RetVal);
        return RetVal;
    }

    /* 修改探头配置 */
    RdumRdusCrm_MeasureFrame_t* Frame = (RdumRdusCrm_MeasureFrame_t*)&CrmConfig.CrmData[0];
    Frame->Bit.SID1_BEH_CH_H = g_ProbeConfig.SID1_BEH_CH_H;
    Frame->Bit.SID1_BEH_CH_L = g_ProbeConfig.SID1_BEH_CH_L;
    Frame->Bit.SID2_BEH_CH_H = g_ProbeConfig.SID2_BEH_CH_H;
    Frame->Bit.SID2_BEH_CH_L = g_ProbeConfig.SID2_BEH_CH_L;
    Frame->Bit.SID3_BEH_CH_H = g_ProbeConfig.SID3_BEH_CH_H;
    Frame->Bit.SID3_BEH_CH_L = g_ProbeConfig.SID3_BEH_CH_L;
    Frame->Bit.SID4_BEH_CH_H = g_ProbeConfig.SID4_BEH_CH_H;
    Frame->Bit.SID4_BEH_CH_L = g_ProbeConfig.SID4_BEH_CH_L;
    Frame->Bit.SID5_BEH_CH_H = g_ProbeConfig.SID5_BEH_CH_H;
    Frame->Bit.SID5_BEH_CH_L = g_ProbeConfig.SID5_BEH_CH_L;
    Frame->Bit.SID6_BEH_CH_H = g_ProbeConfig.SID6_BEH_CH_H;
    Frame->Bit.SID6_BEH_CH_L = g_ProbeConfig.SID6_BEH_CH_L;

    /* 重新计算CRC */
    Frame->Bit.Crc = BaseDrv_Crc8Calculate(Frame->MeasureData, sizeof(Frame->MeasureData) - 1);

    /* 将CRM命令映射到TX_DATA */
    RetVal = CrmSpiMapping_MapToTxData(&CrmConfig, &TxDataMap);
    if (RetVal != 0) {
        printf("映射CRM命令到TX_DATA失败，错误码: %d\n", RetVal);
        return RetVal;
    }

    /* 计算需要的TX_DATA地址数量 */
    TxDataCount = (CrmConfig.TotalDataSize / 4) + 1;
    if (TxDataCount > CRM_MAX_COMMANDS) {
        printf("TX_DATA地址数量超出限制\n");
        return 1;
    }

    /* 将TX_DATA映射到SPI协议 */
    for (i = 0; i < TxDataCount; i++) {
        RetVal = CrmSpiMapping_MapToSpiProtocol(Dsi3Ch, &TxDataMap, &SpiCmd[i], 0x03 + i);
        if (RetVal != 0) {
            printf("映射TX_DATA到SPI协议失败，错误码: %d\n", RetVal);
            return RetVal;
        }
    }

    /* 发送SPI命令 */
    for (i = 0; i < TxDataCount; i++) {
        TxSeqId = SpiCom_AsyncTransfer(SpiCmd[i].SpiCmdData, SPI_CMD_LENS);
        if (TxSeqId == SPI_TX_SEQ_NULL) {
            printf("发送SPI命令失败\n");
            return 1;
        }

        /* 等待命令完成 */
        while (SpiCom_GetTransferStatus(TxSeqId) != SPI_TX_READY &&
               SpiCom_GetTransferStatus(TxSeqId) != SPI_TX_ERROR &&
               SpiCom_GetTransferStatus(TxSeqId) != SPI_TX_TIMEOUT) {
            BaseDrv_DelayMs(1);
        }

        if (SpiCom_GetTransferStatus(TxSeqId) != SPI_TX_READY) {
            printf("SPI命令执行失败，状态: %d\n", SpiCom_GetTransferStatus(TxSeqId));
            return 1;
        }
    }

    printf("开始测量命令发送成功\n");

    return 0;
}

/******************************************************************************
 * @brief      采集完整周期数据
 * @param[in]  Dsi3Ch 通道
 * @param[in]  Dsi3Id 从机ID
 * @return     0: 成功, 非0: 失败
 * <AUTHOR> @date       2025-05-20
 * @note       采集一个完整探测周期的所有slot数据
 *****************************************************************************/
static uint8 CollectCompleteCycleData(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id)
{
    uint8 i;
    SpiTxSeqId_t TxSeqId;

    printf("开始采集完整周期数据...\n");

    /* 初始化BRC命令上下文 */
    memset(&g_BrcContext, 0, sizeof(g_BrcContext));
    g_BrcContext.ExpectedKac = 2; /* 第一个KAC值为2 */

    /* 等待第一次BRC命令的延迟时间 */
    printf("等待%dms后开始采集数据...\n", FIRST_BRC_DELAY_MS);
    BaseDrv_DelayMs(FIRST_BRC_DELAY_MS);

    /* 发送多个BRC命令，每个命令获取8个slot */
    for (i = 0; i < TOTAL_BRC_COMMANDS; i++) {
        /* 设置当前命令索引 */
        g_BrcContext.CommandIndex = i;
        g_BrcContext.SlotCount = MAX_SLOT_COUNT;

        printf("发送BRC命令%d，获取8个slot...\n", i);

        /* 异步发送BRC命令 */
        TxSeqId = SpiCom_AsyncTransferEx(
            (uint8*)&Dsi3Ch, /* 这里简化处理，实际应该构造完整的SPI命令 */
            sizeof(Dsi3Ch),
            100, /* 超时时间100ms */
            BRC_INTERVAL_MS, /* 命令间隔时间 */
            3, /* 最大重试次数 */
            BrcCommandCallback, /* 回调函数 */
            &g_BrcContext /* 用户数据 */
        );

        if (TxSeqId == SPI_TX_SEQ_NULL) {
            printf("发送BRC命令%d失败\n", i);
            return 1;
        }

        /* 等待命令完成 */
        while (SpiCom_GetTransferStatus(TxSeqId) != SPI_TX_READY &&
               SpiCom_GetTransferStatus(TxSeqId) != SPI_TX_ERROR &&
               SpiCom_GetTransferStatus(TxSeqId) != SPI_TX_TIMEOUT) {
            BaseDrv_DelayMs(1);
        }

        if (SpiCom_GetTransferStatus(TxSeqId) != SPI_TX_READY) {
            printf("BRC命令%d执行失败，状态: %d\n", i, SpiCom_GetTransferStatus(TxSeqId));
            return 1;
        }

        /* 等待下一个命令的间隔时间 */
        if (i < TOTAL_BRC_COMMANDS - 1) {
            BaseDrv_DelayMs(BRC_INTERVAL_MS);
        }
    }

    printf("完整周期数据采集完成\n");

    return 0;
}

/******************************************************************************
 * @brief      分析探头配置
 * <AUTHOR> @date       2025-05-20
 * @note       分析探头配置，确定有效的slot
 *****************************************************************************/
static void AnalyzeProbeConfiguration(void)
{
    printf("分析探头配置...\n");

    /* 打印探头配置 */
    printf("探头配置:\n");
    printf("  SID1_BEH_CH_H = 0x%02X, SID1_BEH_CH_L = 0x%02X\n",
           g_ProbeConfig.SID1_BEH_CH_H, g_ProbeConfig.SID1_BEH_CH_L);
    printf("  SID2_BEH_CH_H = 0x%02X, SID2_BEH_CH_L = 0x%02X\n",
           g_ProbeConfig.SID2_BEH_CH_H, g_ProbeConfig.SID2_BEH_CH_L);
    printf("  SID3_BEH_CH_H = 0x%02X, SID3_BEH_CH_L = 0x%02X\n",
           g_ProbeConfig.SID3_BEH_CH_H, g_ProbeConfig.SID3_BEH_CH_L);
    printf("  SID4_BEH_CH_H = 0x%02X, SID4_BEH_CH_L = 0x%02X\n",
           g_ProbeConfig.SID4_BEH_CH_H, g_ProbeConfig.SID4_BEH_CH_L);
    printf("  SID5_BEH_CH_H = 0x%02X, SID5_BEH_CH_L = 0x%02X\n",
           g_ProbeConfig.SID5_BEH_CH_H, g_ProbeConfig.SID5_BEH_CH_L);
    printf("  SID6_BEH_CH_H = 0x%02X, SID6_BEH_CH_L = 0x%02X\n",
           g_ProbeConfig.SID6_BEH_CH_H, g_ProbeConfig.SID6_BEH_CH_L);
    printf("（0为主发，1为左侦听，2为右侦听，3为不动作）\n\n");

    /* 分析有效的slot */
    g_ValidSlotCount = 0;

    /* 检查SID1高通道 */
    if (g_ProbeConfig.SID1_BEH_CH_H != PROBE_NO_ACTION) {
        g_ValidSlots[g_ValidSlotCount++] = 0; /* slot1 */
    }

    /* 检查SID2高通道 */
    if (g_ProbeConfig.SID2_BEH_CH_H != PROBE_NO_ACTION) {
        g_ValidSlots[g_ValidSlotCount++] = 1; /* slot2 */
    }

    /* 检查SID2低通道 */
    if (g_ProbeConfig.SID2_BEH_CH_L != PROBE_NO_ACTION) {
        g_ValidSlots[g_ValidSlotCount++] = 2; /* slot3 */
    }

    /* 检查SID3高通道 */
    if (g_ProbeConfig.SID3_BEH_CH_H != PROBE_NO_ACTION) {
        g_ValidSlots[g_ValidSlotCount++] = 3; /* slot4 */
    }

    /* 检查SID3低通道 */
    if (g_ProbeConfig.SID3_BEH_CH_L != PROBE_NO_ACTION) {
        g_ValidSlots[g_ValidSlotCount++] = 4; /* slot5 */
    }

    /* 检查SID4低通道 */
    if (g_ProbeConfig.SID4_BEH_CH_L != PROBE_NO_ACTION) {
        g_ValidSlots[g_ValidSlotCount++] = 5; /* slot6 */
    }

    /* 检查SID5高通道 */
    if (g_ProbeConfig.SID5_BEH_CH_H != PROBE_NO_ACTION) {
        g_ValidSlots[g_ValidSlotCount++] = 6; /* slot7 */
    }

    /* 检查SID6高通道 */
    if (g_ProbeConfig.SID6_BEH_CH_H != PROBE_NO_ACTION) {
        g_ValidSlots[g_ValidSlotCount++] = 7; /* slot8 */
    }

    printf("有效的slot数量: %d\n", g_ValidSlotCount);
    printf("有效的slot索引: ");
    for (uint8 i = 0; i < g_ValidSlotCount; i++) {
        printf("slot%d ", g_ValidSlots[i] + 1);
    }
    printf("\n\n");
}

/******************************************************************************
 * @brief      处理BRC结果
 * <AUTHOR> @date       2025-05-20
 * @note       处理所有BRC命令的结果
 *****************************************************************************/
static void ProcessBrcResults(void)
{
    uint8 i, j;

    printf("处理BRC结果...\n");

    /* 遍历所有BRC命令 */
    for (i = 0; i < g_BrcContext.CompletedCommands; i++) {
        BrcResult_t* Result = &g_BrcContext.Results[i];

        printf("BRC命令%d结果:\n", i);

        /* 检查命令状态 */
        if (Result->Status != BRC_STATUS_OK) {
            printf("  命令状态: %d (错误)\n", Result->Status);
            continue;
        }

        printf("  命令状态: %d (成功)\n", Result->Status);
        printf("  Slot数量: %d\n", Result->SlotCount);

        /* 打印每个slot的数据 */
        for (j = 0; j < Result->SlotCount; j++) {
            /* 检查是否是有效的slot */
            uint8 IsValidSlot = 0;
            for (uint8 k = 0; k < g_ValidSlotCount; k++) {
                if (g_ValidSlots[k] == j) {
                    IsValidSlot = 1;
                    break;
                }
            }

            if (IsValidSlot) {
                printf("  Slot%d数据:\n", j + 1);
                PrintSlotData(&Result->SlotData[j], j);
            }
        }
    }
}

/******************************************************************************
 * @brief      解压缩并分析包络数据
 * <AUTHOR> @date       2025-05-20
 * @note       解压缩并分析所有slot的包络数据
 *****************************************************************************/
static void DecompressAndAnalyzeEnvelopes(void)
{
    uint8 i, j, k;
    DecompressedEnvelope_t Envelope;

    printf("解压缩并分析包络数据...\n");

    /* 遍历所有BRC命令 */
    for (i = 0; i < g_BrcContext.CompletedCommands; i++) {
        BrcResult_t* Result = &g_BrcContext.Results[i];

        /* 检查命令状态 */
        if (Result->Status != BRC_STATUS_OK) {
            continue;
        }

        /* 解压缩每个有效slot的包络数据 */
        for (j = 0; j < Result->SlotCount; j++) {
            /* 检查是否是有效的slot */
            uint8 IsValidSlot = 0;
            for (k = 0; k < g_ValidSlotCount; k++) {
                if (g_ValidSlots[k] == j) {
                    IsValidSlot = 1;
                    break;
                }
            }

            if (IsValidSlot) {
                /* 解压缩包络数据 */
                uint8 RetVal = BrcSlotDef_DecompressEnvelope(Result->SlotData[j].SlotData, &Envelope);
                if (RetVal != 0) {
                    printf("  Slot%d包络数据解压缩失败，错误码: %d\n", j + 1, RetVal);
                    continue;
                }

                printf("  Slot%d包络数据解压缩成功，共%d个点位\n", j + 1, TOTAL_ENVELOPE_POINTS);

                /* 分析包络数据 */
                uint16 MaxValue = 0;
                uint8 MaxIndex = 0;

                for (k = 0; k < TOTAL_ENVELOPE_POINTS; k++) {
                    if (Envelope.Points[k] > MaxValue) {
                        MaxValue = Envelope.Points[k];
                        MaxIndex = k;
                    }
                }

                printf("  最大值: %d，位置: %d (%.2fus)\n",
                       MaxValue, MaxIndex, MaxIndex * POINT_INTERVAL_US);
            }
        }
    }
}

/******************************************************************************
 * @brief      打印诊断数据
 * <AUTHOR> @date       2025-05-20
 * @note       打印诊断数据
 *****************************************************************************/
static void PrintDiagnosticData(void)
{
    uint8 i;

    printf("打印诊断数据...\n");

    /* 遍历所有BRC命令 */
    for (i = 0; i < g_BrcContext.CompletedCommands; i++) {
        BrcResult_t* Result = &g_BrcContext.Results[i];

        /* 检查命令状态 */
        if (Result->Status != BRC_STATUS_OK) {
            continue;
        }

        /* 打印非读响应信息 */
        printf("BRC命令%d非读响应:\n", i);
        printf("  BRC_RECEIVED_CHA: 0x%02X\n", Result->NonReadResp.Bit.BRC_RECEIVED_CHA);
        printf("  BRC_RECEIVED_CHB: 0x%02X\n", Result->NonReadResp.Bit.BRC_RECEIVED_CHB);
        printf("  SPI_RCVR_STATUS: 0x%02X\n", Result->NonReadResp.Bit.SPI_RCVR_STATUS);
        printf("  CONTROL_BYTE: 0x%02X\n", Result->NonReadResp.Bit.CONTROL_BYTE);
        printf("  PDCM_RESP_BYTE_1: 0x%02X\n", Result->NonReadResp.Bit.PDCM_RESP_BYTE_1);
        printf("  PDCM_RESP_BYTE_2: 0x%02X\n", Result->NonReadResp.Bit.PDCM_RESP_BYTE_2);
    }
}

/******************************************************************************
 * @brief      打印Slot数据
 * @param[in]  SlotData Slot数据
 * @param[in]  SlotIndex Slot索引
 * <AUTHOR> @date       2025-05-20
 * @note       打印Slot数据
 *****************************************************************************/
static void PrintSlotData(SlotData_t* SlotData, uint8 SlotIndex)
{
    uint8 i;

    printf("    KAC: %d, Status: %d\n", SlotData->Kac, SlotData->Status);
    printf("    数据: ");

    for (i = 0; i < SLOT_PACKAGE_SIZE - 2; i++) {
        printf("%02X ", SlotData->SlotData[i]);
        if ((i + 1) % 8 == 0) {
            printf("\n           ");
        }
    }

    printf("\n    CRC: 0x%02X\n", SlotData->SlotCrc);
    printf("    持续时间: 0x%02X%02X\n", SlotData->SlotDuration[0], SlotData->SlotDuration[1]);
}

/******************************************************************************
 * @brief      主函数
 * @return     0: 成功, 非0: 失败
 * <AUTHOR> @date       2025-05-20
 * @note
 *****************************************************************************/
int main(void)
{
    uint8 RetVal;
    Dsi3Channel_t Dsi3Ch = DSI3_CHANNEL_A;
    uint8 Dsi3Id = 0x01;

    printf("安森美超声波探头异步数据采集示例\n");
    printf("==================================\n\n");

    /* 初始化SPI通信模块 */
    SpiCom_Init();

    /* 配置SPI通信参数 */
    SpiCom_SetDefaultTimeout(100);          /* 设置默认超时时间为100ms */
    SpiCom_SetDefaultDelay(5);              /* 设置默认命令间延时为5ms */
    SpiCom_SetDefaultRetryCount(3);         /* 设置默认最大重试次数为3次 */
    SpiCom_EnableRetry(1);                  /* 启用重试功能 */
    SpiCom_EnableDelay(1);                  /* 启用延时功能 */

    /* 分析探头配置 */
    AnalyzeProbeConfiguration();

    /* 配置测量参数 */
    RetVal = ConfigureMeasurementParameters(Dsi3Ch, Dsi3Id);
    if (RetVal != 0) {
        printf("配置测量参数失败，错误码: %d\n", RetVal);
        return RetVal;
    }

    /* 开始测量 */
    RetVal = StartMeasurement(Dsi3Ch, Dsi3Id);
    if (RetVal != 0) {
        printf("开始测量失败，错误码: %d\n", RetVal);
        return RetVal;
    }

    /* 采集完整周期数据 */
    RetVal = CollectCompleteCycleData(Dsi3Ch, Dsi3Id);
    if (RetVal != 0) {
        printf("采集完整周期数据失败，错误码: %d\n", RetVal);
        return RetVal;
    }

    /* 处理BRC结果 */
    ProcessBrcResults();

    /* 解压缩并分析包络数据 */
    DecompressAndAnalyzeEnvelopes();

    /* 打印诊断数据 */
    PrintDiagnosticData();

    printf("\n数据采集和分析完成\n");

    return 0;
}
