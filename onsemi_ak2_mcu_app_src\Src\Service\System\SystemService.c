/******************************************************************************
 * @file       SystemService.c
 * @brief      
 * @date       2025-03-04 11:32:24
 * <AUTHOR>
 * @copyright  Longhorn Automotive Electronics Technology (Xiamen) Co., Ltd.
 *****************************************************************************/


/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "types.h"
#include "ClkDrv.h"
#include "TimerDrv.h"
#include "IOHal.h"
#include "AdcHal.h"
#include "CANDrv.h"
#include "CANStack.h"
#include "UartHal.h"
#include "EELHal.h"
#include "DMA_COM.h"
#include "TAUBDrv.h"
#include "TAUDDrv.h"
#include "CSIHDrv.h"
#include "DMADrv.h"
#include "fls.h"
#include "PSL_Algorithm.h"
#include "Power_Manage.h"
#include "PAS_MAP_StateHandle.h"
#include "Elmos_524_17_SnsCtrl.h"
#include "SnsRawData_Int.h"
#include "SnsEchoFilterAndSigGroup_int.h"
#include "SnsDisFollow_Int.h"
#include "MapEchoFilterAndSigGroup_int.h"
#include "MapCoorCalculate_Int.h"
#include "MapBuild_Int.h"
#include "SDW_Int.h"
#include "ApaCalCarCoor.h"
#include "Sns_install_Coordinate.h"
#include "Vehicle_Geometry_Parameter.h"
#include "PSL_State_Manage.h"
#include "PSL_AppSignalManage.h"
#include "PSL_Calibration.h"
#include "PSL_EchoFilterAndSigGroup_int.h"
#include "SbcCtrl.h"
#include "SnsPPCalculate_Int.h"
#include "Memory.h"
#include "CrcDrv.h"
#include "RdumRdusDrv.h"



/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/



/******************************************************************************
 * @Function Declaration
 *****************************************************************************/



/******************************************************************************
 * @Const Definitions
 *****************************************************************************/



/******************************************************************************
 * @Variable Definitions
 *****************************************************************************/



/******************************************************************************
 * @Function Definitions
 *****************************************************************************/


/******************************************************************************
 * @brief      
 * <AUTHOR>
 * @date       2025-03-04 11:32:47
 * @note       
 *****************************************************************************/
void SystemDrvInit(void)
{
    DI();
    ClkDrvInit();
    TimerDrvInit();
    IOHalInit();
    AdcHalInit();
    CANDrvInit();
    UartHalInit();
    EELHalInit();
    CANStackInit();
    TAUBInit();
    TAUDInit();
    CSIHDrvInit();
    DMADrvInit();
    EI();
}


/******************************************************************************
 * @brief      
 * <AUTHOR>
 * @date       2025-03-04 11:33:08
 * @note       
 *****************************************************************************/
void SystemAppInit(void)
{
#if 0
    Memory_Init();
#endif
    CRC8_C2_InitTable();
    SbcCtrl_Init();
    PowerManage_Init();
    InitSnsCtrlGroup();
    PAS_MAP_To_CANInit();
    PSLWorkStatusManageInit();
    PSLOutputtoCANSlotinfoInit();
    RdumRdusDrv_Init();
}

/******************************************************************************
 * @brief      
 * <AUTHOR>
 * @date       2025-03-04 11:33:19
 * @note       
 *****************************************************************************/
void SystemAlgorithmInit(void)
{
    APP_Car_Sns_Config_Init();
    APP_Car_Geometry_Config_Init();
    CPOS_Init();
    PDCSnsRawDataPowerOnInit();
    SnsSigGroupDataPowerOnInit();
    PSLSnsSigGroupDataPowerOnInit();
    MapSigGroupDataPowerOnInit();
    SnsSigGroupFollowDataPowerOnInit();
    SnsObjCoorDataPowerOnInit();
    ObjMapPowerOnInit();
    SDWDataInit();
    PSLDeteSlotDataInit();
    PSL_APPPara_Init();
    PPDataPowerOnInit();
}
