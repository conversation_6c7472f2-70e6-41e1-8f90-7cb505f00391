#include "R_TypeDefs.h"
#include "r_eel_types.h"


#define FEE_EEL_ERR_HOOK(err)                       /* TODO */
#define FEE_FDL_ERR_HOOK(err)                       /* TODO */

extern void DL_EELInit(void);
extern r_eel_status_t DL_EELShutDown(void);
extern r_eel_status_t DL_EELWrite(uint8_t* pData, uint16_t blockId);
extern r_eel_status_t DL_EELRead(uint8_t* pData, uint16_t blockId,  uint16_t len);
