/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * CAN_AppSignalManage:
 * Created on: 2022-11-24 19:43
 * Original designer: 22866
 ******************************************************************************/


/* ============================================================================================== */
/* Header files																				      */
/* ============================================================================================== */

#include "PSL_AppSignalManage.h"
#include "PSL_Output_Manage.h"
#include "PSL_Algorithm.h"


/***************************************************************************/
/* local data definitions                                                  */
/***************************************************************************/

PSLCalSlotCoorType PSLCalSlotcoor[PSLSLOTNUM];
PSLOutputinfoType GstrAPAslotInfo[PSLSLOTNUM];





/******************************************************************
* 函数名称: PSLOutputtoCANSlotinfoInit
*
* 功能描述: 输出车位信息Init
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 
*
* 修改日期      版本号      修改人      修改内容

*********************************************************************/
void PSLOutputtoCANSlotinfoInit(void)
{
    uint8 i;
    for(i = 0; i < PSLSLOTNUM; i++)
    {
        GstrAPAslotInfo[i].u8ApaSlotID = 0;
        GstrAPAslotInfo[i].u16ApaSlotLength = 0;
        GstrAPAslotInfo[i].u16ApaSlotDepth = 0;
        GstrAPAslotInfo[i].u8SlotdepthRef = 0;
        GstrAPAslotInfo[i].u8ApaSlotType = SLOT_TYPE_RESERVED_INVALID;
        GstrAPAslotInfo[i].u8ApaSlotStatus = SLOT_STATUS_PSNONE;
        GstrAPAslotInfo[i].dFirstObjWx = 0x1FFF;
        GstrAPAslotInfo[i].dFirstObjWy = 0x1FFF;
        GstrAPAslotInfo[i].dSecondObjWx = 0x1FFF;
        GstrAPAslotInfo[i].dSecondObjWy = 0x1FFF;
        GstrAPAslotInfo[i].s16Obj1Alpha = 0;
        GstrAPAslotInfo[i].s16Obj2Alpha = 0;
        GstrAPAslotInfo[i].u8FirstObjType = 0;
        GstrAPAslotInfo[i].u8SecObjType = 0;
        GstrAPAslotInfo[i].bPSLWriteLockFlag = TRUE;
    }
}


/******************************************************************************
* 设计描述 : X01:APA 输出车位信息
* 设计索引 : 
*******************************************************************************/



void PSLOutputReadinfo(PSLOutputinfoType *LstrAPAslotInfo,uint8 wslotcnt)
{
    LstrAPAslotInfo->u8ApaSlotID = GstrAPAslotInfo[wslotcnt].u8ApaSlotID;
    LstrAPAslotInfo->u16ApaSlotLength = GstrAPAslotInfo[wslotcnt].u16ApaSlotLength;
    LstrAPAslotInfo->u16ApaSlotDepth = GstrAPAslotInfo[wslotcnt].u16ApaSlotDepth;
    LstrAPAslotInfo->u8SlotdepthRef = GstrAPAslotInfo[wslotcnt].u8SlotdepthRef;
    LstrAPAslotInfo->u8ApaSlotType = GstrAPAslotInfo[wslotcnt].u8ApaSlotType;
    LstrAPAslotInfo->u8ApaSlotStatus = GstrAPAslotInfo[wslotcnt].u8ApaSlotStatus;
    LstrAPAslotInfo->dFirstObjWx = GstrAPAslotInfo[wslotcnt].dFirstObjWx;
    LstrAPAslotInfo->dFirstObjWy = GstrAPAslotInfo[wslotcnt].dFirstObjWy;
    LstrAPAslotInfo->dSecondObjWx = GstrAPAslotInfo[wslotcnt].dSecondObjWx;
    LstrAPAslotInfo->dSecondObjWy = GstrAPAslotInfo[wslotcnt].dSecondObjWy;
    LstrAPAslotInfo->s16Obj1Alpha = GstrAPAslotInfo[wslotcnt].s16Obj1Alpha;
    LstrAPAslotInfo->s16Obj2Alpha = GstrAPAslotInfo[wslotcnt].s16Obj2Alpha;
    LstrAPAslotInfo->u8FirstObjType = GstrAPAslotInfo[wslotcnt].u8FirstObjType;
    LstrAPAslotInfo->u8SecObjType = GstrAPAslotInfo[wslotcnt].u8SecObjType;
    LstrAPAslotInfo->u8CurbType = 0;
    LstrAPAslotInfo->u32slotsynctime = GstrAPAslotInfo[wslotcnt].u32slotsynctime;
    LstrAPAslotInfo->bPSLWriteLockFlag = GstrAPAslotInfo[wslotcnt].bPSLWriteLockFlag;
}

/******************************************************************
* 函数名称: PSLChangeSlotType
*
* 功能描述: 输出车位类型检测
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 无
*
* 修改日期      版本号      修改人      修改内容
**********************************************************************/ 
PSL_SLOT_TYPE PSLChangeSlotType(PSLDetSlotType cSlottype)
{
    PSL_SLOT_TYPE cCalslottype = SLOT_TYPE_RESERVED_INVALID;
  
        if(cSlottype == PSL_SLOT_LEFT_HORIZONTAL)
        {
            cCalslottype = SLOT_TYPE_LEFT_H;
        }
        else if(cSlottype == PSL_SLOT_RIGHT_HORIZONTAL)
        {
            cCalslottype = SLOT_TYPE_RIGHT_H;
        }
        else if(cSlottype == PSL_SLOT_LEFT_VERTICAL)
        {
            cCalslottype = SLOT_TYPE_LEFT_V;
        }
        else if(cSlottype == PSL_SLOT_RIGHT_VERTICAL)
        {
            cCalslottype = SLOT_TYPE_RIGHT_V;
        }
    

    return cCalslottype;
}

/******************************************************************
* 函数名称: PSLSendRadarSlotinfoTOcan
*
* 功能描述: 输出车位信息到总线
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 无
*
* 修改日期      版本号      修改人      修改内容
**********************************************************************/ 
uint8 Gu8SerialNumCntBck = 0;
uint8 Gu8SlotCntBck = 0;

void PSLSendRadarSlotinfoTOcan(uint8 wslotcnt)
{

    sint16 s16Obj1Alpha = 0;
    sint16 s16Obj2Alpha = 0;
    if(wslotcnt < 1 || wslotcnt > OUTPUT_SLOT_MAX_NUM)
    {        
        return;
    }
    
    if(PSLCalSlotcoor[wslotcnt - 1].dObj1Slope > 3.141592)
    {
        PSLCalSlotcoor[wslotcnt - 1].dObj1Slope -= 6.283184;
    }
    else if(PSLCalSlotcoor[wslotcnt - 1].dObj1Slope < -3.141592)
    {
        PSLCalSlotcoor[wslotcnt - 1].dObj1Slope += 6.283184;
    }
    else
    {
    }

    if(PSLCalSlotcoor[wslotcnt - 1].dObj2Slope > 3.141592)
    {
        PSLCalSlotcoor[wslotcnt - 1].dObj2Slope -= 6.283184;
    }
    else if(PSLCalSlotcoor[wslotcnt - 1].dObj2Slope < -3.141592)
    {
        PSLCalSlotcoor[wslotcnt - 1].dObj2Slope += 6.283184;
    }
    else
    {
    }
    
    if(PSLCalSlotcoor[wslotcnt - 1].dObj1Slope > 1.082104)
    {
        PSLCalSlotcoor[wslotcnt - 1].dObj1Slope -= 3.1415926;
    }
    else if(PSLCalSlotcoor[wslotcnt - 1].dObj1Slope < -1.1170107)
    {
        PSLCalSlotcoor[wslotcnt - 1].dObj1Slope += 3.1415926;
    }

    if(PSLCalSlotcoor[wslotcnt - 1].dObj2Slope > 1.082104)
    {
        PSLCalSlotcoor[wslotcnt - 1].dObj2Slope -= 3.1415926;
    }
    else if(PSLCalSlotcoor[wslotcnt - 1].dObj2Slope < -1.1170107)
    {
        PSLCalSlotcoor[wslotcnt - 1].dObj2Slope += 3.1415926;
    }
    
    s16Obj1Alpha = (sint16)(PSLCalSlotcoor[wslotcnt - 1].dObj1Slope * 57.29578);
    s16Obj2Alpha = (sint16)(PSLCalSlotcoor[wslotcnt - 1].dObj2Slope * 57.29578);
  
    if(s16Obj1Alpha > 124)
        s16Obj1Alpha = 124;

    if(s16Obj1Alpha < -127)
        s16Obj1Alpha = -127;
    
    if(s16Obj2Alpha > 124)
        s16Obj2Alpha = 124;

    if(s16Obj2Alpha < -127)
        s16Obj2Alpha = -127;

    if(GsPSLOutputSlotData.cSlotType[wslotcnt - 1] == PSL_SLOT_LEFT_VERTICAL
        || GsPSLOutputSlotData.cSlotType[wslotcnt - 1] == PSL_SLOT_RIGHT_VERTICAL)
    {
        s16Obj1Alpha = 0;
        s16Obj2Alpha = 0;
    }
    GstrAPAslotInfo[wslotcnt - 1].u8ApaSlotID = Gu8SerialNumCntBck-GsPSLOutputSlotData.wSlotCnt+wslotcnt;

    GstrAPAslotInfo[wslotcnt - 1].u16ApaSlotLength = GsPSLOutputSlotData.wSlotWidth[wslotcnt - 1] / 10;
    GstrAPAslotInfo[wslotcnt - 1].u16ApaSlotDepth = (uint16)GsPSLOutputSlotData.dSlotDepthDis[wslotcnt - 1] / 10;
    GstrAPAslotInfo[wslotcnt - 1].u8SlotdepthRef = GsPSLOutputSlotData.cSlotdepthRef[wslotcnt - 1];
    GstrAPAslotInfo[wslotcnt - 1].u8ApaSlotType = PSLChangeSlotType(GsPSLOutputSlotData.cSlotType[wslotcnt - 1]);
    GstrAPAslotInfo[wslotcnt - 1].u8ApaSlotStatus = SLOT_STATUS_PSOK;
    GstrAPAslotInfo[wslotcnt - 1].dFirstObjWx = ((sint16)PSLCalSlotcoor[wslotcnt - 1].dFirstObjWx / 10);
    GstrAPAslotInfo[wslotcnt - 1].dFirstObjWy = ((sint16)PSLCalSlotcoor[wslotcnt - 1].dFirstObjWy / 10);
    GstrAPAslotInfo[wslotcnt - 1].dSecondObjWx = ((sint16)PSLCalSlotcoor[wslotcnt - 1].dSecObjWx / 10);
    GstrAPAslotInfo[wslotcnt - 1].dSecondObjWy = ((sint16)PSLCalSlotcoor[wslotcnt - 1].dSecObjWy / 10);
    GstrAPAslotInfo[wslotcnt - 1].s16Obj1Alpha = s16Obj1Alpha;
    GstrAPAslotInfo[wslotcnt - 1].s16Obj2Alpha = s16Obj2Alpha;
    GstrAPAslotInfo[wslotcnt - 1].u8FirstObjType = GsPSLOutputSlotData.cFirObjType[wslotcnt - 1];
    GstrAPAslotInfo[wslotcnt - 1].u8SecObjType = GsPSLOutputSlotData.cSecObjType[wslotcnt - 1];
    //GstrAPAslotInfo[wslotcnt - 1].u32slotsynctime = GsPSLOutputSlotData.Lu32slotsynctime[wslotcnt - 1];   
}

/******************************************************************
* 函数名称: APALeftSlotCoordinateYSwitch
*
* 功能描述: 已输出车位信息坐标系转换
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 无
*
* 修改日期      版本号      修改人      修改内容
**********************************************************************/ 

static void ObjOdoCoorToCarCoor(float *LfObj1CoorX,float *LfObj1CoorY,uint8 dslotcnt,float *LfObj2CoorX,float *LfObj2CoorY)
{
    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;
    
    /* P1点世界坐标不动，车辆坐标根据先平移后旋转的公式进行转换   */
    PSLCalSlotcoor[dslotcnt].dFirstObjWx  = (*LfObj1CoorX - LpStrPSLUseOdo->fCarCoorX)*LpStrPSLUseOdo->fCarCosYawAngle +\
                               (*LfObj1CoorY - LpStrPSLUseOdo->fCarCoorY)*LpStrPSLUseOdo->fCarSinYawAngle;
    PSLCalSlotcoor[dslotcnt].dFirstObjWy = (*LfObj1CoorY - LpStrPSLUseOdo->fCarCoorY)*LpStrPSLUseOdo->fCarCosYawAngle -\
                               (*LfObj1CoorX - LpStrPSLUseOdo->fCarCoorX)*LpStrPSLUseOdo->fCarSinYawAngle;

   PSLCalSlotcoor[dslotcnt].dSecObjWx  = (*LfObj2CoorX - LpStrPSLUseOdo->fCarCoorX)*LpStrPSLUseOdo->fCarCosYawAngle +\
   (*LfObj2CoorY - LpStrPSLUseOdo->fCarCoorY)*LpStrPSLUseOdo->fCarSinYawAngle;
    PSLCalSlotcoor[dslotcnt].dSecObjWy = (*LfObj2CoorY - LpStrPSLUseOdo->fCarCoorY)*LpStrPSLUseOdo->fCarCosYawAngle -\
                               (*LfObj2CoorX - LpStrPSLUseOdo->fCarCoorX)*LpStrPSLUseOdo->fCarSinYawAngle;
}

/******************************************************************
* 函数名称: PSLCoordinateSwitch
*
* 功能描述: 已输出车位信息坐标系转换
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 无
*
* 修改日期      版本号      修改人      修改内容
**********************************************************************/ 

void PSLCoordinateSwitch(void)
{
    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;
    static float fBackupOdox = 0.0;
    static float fBackupOdoy = 0.0;
    static float fbackupOdoSlope = 0.0;
    uint8 Lu8Temp = 0;
    float   dCurSlopetemp = 0;
    
    if(fBackupOdox != LpStrPSLUseOdo->fCarCoorX 
        || fBackupOdoy != LpStrPSLUseOdo->fCarCoorY)
    {
        PSLOutputtoCANSlotinfoInit();
        
        
        if(GsPSLOutputSlotData.wSlotCnt>Gu8SlotCntBck)
        {
            Gu8SerialNumCntBck += GsPSLOutputSlotData.wSlotCnt - Gu8SlotCntBck;
            if(Gu8SerialNumCntBck >= 255)
                Gu8SerialNumCntBck = 1;
        }
        Gu8SlotCntBck = GsPSLOutputSlotData.wSlotCnt;
        
        dCurSlopetemp = LpStrPSLUseOdo->fCarYawAngle - fbackupOdoSlope;
        dCurSlopetemp = -dCurSlopetemp;
       
        for(Lu8Temp = 0; Lu8Temp < GsPSLOutputSlotData.wSlotCnt; Lu8Temp++)
        {               
            ObjOdoCoorToCarCoor(&GsPSLOutputSlotData.dFirstObjWx[Lu8Temp],&GsPSLOutputSlotData.dFirstObjWy[Lu8Temp],Lu8Temp,&GsPSLOutputSlotData.dSecObjWx[Lu8Temp],&GsPSLOutputSlotData.dSecObjWy[Lu8Temp]);              
            PSLCalSlotcoor[Lu8Temp].dObj1Slope = PSLCalSlotcoor[Lu8Temp].dObj1Slope + dCurSlopetemp;
            PSLCalSlotcoor[Lu8Temp].dObj2Slope = PSLCalSlotcoor[Lu8Temp].dObj2Slope + dCurSlopetemp;
            GstrAPAslotInfo[Lu8Temp].bPSLWriteLockFlag = FALSE;
            PSLSendRadarSlotinfoTOcan(Lu8Temp + 1);
            GstrAPAslotInfo[Lu8Temp].bPSLWriteLockFlag = TRUE;
        }

        fBackupOdox = LpStrPSLUseOdo->fCarCoorX;
        fBackupOdoy = LpStrPSLUseOdo->fCarCoorY;
        fbackupOdoSlope= LpStrPSLUseOdo->fCarYawAngle;
              
    }
}



