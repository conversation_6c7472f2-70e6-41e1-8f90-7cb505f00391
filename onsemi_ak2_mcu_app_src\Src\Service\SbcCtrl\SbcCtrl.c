/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * All Rights Reserved.
 *
 * FileName : SbcCtrl.c
 * Date     : 2021-07-06 09:01:05
 * Author   :
 ******************************************************************************/
/*Includes -------------------------------------------------------------------*/
#include "SbcCtrl.h"
#include "CDD_Sbc_fs23.h"
#include "CanTrcv_fs23_Ip.h"
#include "AdcHal.h"
#include "PublicCalAlgorithm_Int.h"
#include "debug.h"
#include "TimerManage.h"

/* Macro ----------------------------------------------------------------------*/
#if 0
    #define SBC_CTRL_PRINTF	            DEBUG_PRINT
#else
    #define SBC_CTRL_PRINTF(...)   
#endif
#define ADC_RESOLUTION                  (5.0f / 4096.0f)
#define SBC_VTEMP25                     (1.38f)  /*uint:V*/
#define SBC_VTEMP_COEFF                 (-3.88f) /*uint:mV/摄氏度*/
#define SBC_NOR_TO_OVER_TEMPERATURE     (145)   /*uint:摄氏度*/
#define SBC_OVER_TO_NOR_TEMPERATURE		(142)   /*uint:摄氏度*/
#define FEED_WATCHWDG_PERIOD            (300)  /*uint:ms*/ 

/* Type Definitions -----------------------------------------------------------*/
typedef struct
{	
	Pub_FilterCtrl_t FilterCtrl;
	SbcTempSts_e SbcTempSts;
}SbcTempDiag_t;

/******************************************************************************/
CanTrcv_fs23_ConfigurationDataType Fs23CanCfg = 
{
   .canBusWuEnable = TRUE,
   .canFailsafeDisable = FALSE,
   .initState = CANTRCV_TRCVMODE_NORMAL,
};

SbcTempDiag_t SbcTempDiag = {
	.SbcTempSts = Sbc_Temperature_Normal,
	.FilterCtrl.UpperLimit = 3,
	.FilterCtrl.LowerLimit = 0,
	.FilterCtrl.UpStep = 1,
	.FilterCtrl.DownStep = 3,
	.FilterCtrl.Count = 0,
	.FilterCtrl.Input = FALSE,
	.FilterCtrl.Output = FALSE,
};

static uint8_t WDErrFlg = FALSE;
Sbc_WakeupReasonType GeunWakeupReason = SBC_NO_WU_EVENT;
uint32 FeedWdLastTime = 0;
	
/******************************************************************************
* 函数名称: SBC_Init
* 设计作者: 
* 设计日期: 2020-07-08 09:01
* 功能描述: 初始化SBC
* 参数描述: 无
* 返回类型: void
* 修改人员: 无
* 修改日期: 无
* 修改内容: 无
*******************************************************************************/
void SbcCtrl_Init(void)
{	
    Std_ReturnType eReturnValue = E_NOT_OK;
	Sbc_fs23_WakeupReasonType WakeupReason = SBC_FS23_NO_WU_EVENT;

	/* Initialize Sbc driver */
	Sbc_fs23_InitDriver(NULL_PTR);
		
	eReturnValue = Sbc_fs23_GetWakeup(&WakeupReason);

	GeunWakeupReason = (Sbc_WakeupReasonType)WakeupReason;
	
	/* Call function Sbc_fs23_InitDevice */
	eReturnValue = Sbc_fs23_InitDevice();

	if(eReturnValue != E_OK)
	{
		SBC_CTRL_PRINTF("Fail:Sbc_fs23_InitDevice");
	}

	FeedWdLastTime = GetSystemTimeCnt_Ms();
	
	eReturnValue = CanTrcv_fs23_Init(0,&Fs23CanCfg);

	if(eReturnValue != E_OK)
	{
		SBC_CTRL_PRINTF("Fail:CanTrcv_fs23_Init");
	}
}


/******************************************************************************
* 函数名称: SbcCtrl_NormalFeedWatchdog
* 设计作者: 
* 设计日期: 2020-07-08 09:01
* 功能描述: SBC喂狗
* 参数描述: 无
* 返回类型: void
* 修改人员: 无
* 修改日期: 无
* 修改内容: 无
*******************************************************************************/
void SbcCtrl_NormalFeedWatchdog(void)
{	
	Std_ReturnType eReturnValue = E_NOT_OK;

	eReturnValue = Sbc_fs23_WdRefresh();
    /* 喂狗状态检测 */
    if(eReturnValue != E_OK)
    {
        SBC_CTRL_PRINTF("Fail:SbcCtrl_NormalFeedWatchdog");
    } 
	
#if 0
	uint16_t u16RxData = 0;
	eReturnValue = Sbc_fs23_ReadRegister(SBC_FS23_M_STATUS_ADDR, &u16RxData);
	SBC_CTRL_PRINTF("app m_status:0x%x\r\n",u16RxData);

    eReturnValue = Sbc_fs23_ReadRegister(SBC_FS23_FS_I_WD_CFG_ADDR, &u16RxData);
	SBC_CTRL_PRINTF("app i wd cfg:0x%x\r\n",u16RxData);

	eReturnValue |= Sbc_fs23_ReadRegister(SBC_FS23_FS_WDW_CFG_ADDR, &u16RxData);
	SBC_CTRL_PRINTF("app wd cfg:0x%x\r\n",u16RxData);
#endif

}

/******************************************************************************
* 函数名称: SbcCtrl_DisableWatchdog
* 设计作者: 
* 设计日期: 2020-07-08 09:01
* 功能描述: 关闭SBC看门狗
* 参数描述: 无
* 返回类型: Std_ReturnType
* 修改人员: 无
* 修改日期: 无
* 修改内容: 无
*******************************************************************************/
Std_ReturnType SbcCtrl_DisableWatchdog(void)
{	
	Std_ReturnType eReturnValue = E_NOT_OK;
    uint16_t u16RxData = 0U;
	
	eReturnValue = Sbc_fs23_ReadRegister(SBC_FS23_M_STATUS_ADDR, &u16RxData);
	if(((Std_ReturnType)E_OK == eReturnValue) && ((u16RxData & SBC_FS23_M_NORMAL_S_MASK) == SBC_FS23_M_NORMAL_S_NORMAL))
	{
		eReturnValue |= Sbc_fs23_DisableWatchdog();
	}

	if(E_NOT_OK == eReturnValue)
	{
		SBC_CTRL_PRINTF("Fail:SbcCtrl_DisableWatchdog");
	}
	
	return eReturnValue;
}

/******************************************************************************
* 函数名称: SbcCtrl_TempDiag
* 设计作者: 
* 设计日期: 2020-07-08 09:01
* 功能描述: 
* 参数描述: 无
* 返回类型: 无
* 修改人员: 无
* 修改日期: 无
* 修改内容: 无
*******************************************************************************/
void SbcCtrl_TempDiag(void)
{	
	uint16_t Lu16AdcRawValue = 0;
	float Sbctemp = 0.0f;
		
	Lu16AdcRawValue = AdcHal_GetAdcResult(ADCDETE_GROUP_ADC0_GROUP1, ADC0_SBC_AMUX_TEMP);

	if(0 != Lu16AdcRawValue)
	{
		/*T =(Vamux - Vtemp25) / VTEMP_COEFF + 25 摄氏度*/  
		Sbctemp = (((Lu16AdcRawValue * ADC_RESOLUTION) - SBC_VTEMP25) * 1000.0f) / SBC_VTEMP_COEFF + 25.0f;

		SBC_CTRL_PRINTF("sbc temperature:%d\n",(uint16_t)((Sbctemp + 40)*100));
		
		if(Sbctemp > SBC_NOR_TO_OVER_TEMPERATURE)
		{
			SbcTempDiag.FilterCtrl.Input = TRUE;
		}
		else if(Sbctemp < SBC_OVER_TO_NOR_TEMPERATURE)
		{	
			SbcTempDiag.FilterCtrl.Input = FALSE;
		}
		else
		{}
		
		PubAI_Filter(&SbcTempDiag.FilterCtrl);

		if(TRUE == SbcTempDiag.FilterCtrl.Output)
		{
			SbcTempDiag.SbcTempSts = Sbc_Temperature_Over;
		}
		else
		{
			SbcTempDiag.SbcTempSts = Sbc_Temperature_Normal;
		}
	}
}

/******************************************************************************
* 函数名称: SbcCtrl_Task
* 设计作者: 
* 设计日期: 2020-07-08 09:01
* 功能描述: 
* 参数描述: 无
* 返回类型: 无
* 修改人员: 无
* 修改日期: 无
* 修改内容: 无
*******************************************************************************/
void SbcCtrl_Task(void)
{	
	uint32 FeedWdCurrentTime = GetSystemTimeCnt_Ms();
	uint32 TimeInterval = 0;
	
	SbcCtrl_TempDiag();

	if(FeedWdCurrentTime > FeedWdLastTime)
	{
		TimeInterval = FeedWdCurrentTime - FeedWdLastTime;
	}
	else
	{
		TimeInterval = UINT32_MAX - FeedWdLastTime + FeedWdCurrentTime;
	}
	
	if(TimeInterval >= FEED_WATCHWDG_PERIOD)
	{
		SbcCtrl_NormalFeedWatchdog();
		FeedWdLastTime = GetSystemTimeCnt_Ms();
	}
}

/******************************************************************************
* 函数名称: SbcCtrl_SetSbcGoToLPOFF
* 设计作者: 
* 设计日期: 2020-07-08 09:01
* 功能描述: 
* 参数描述: 无
* 返回类型: 无
* 修改人员: 无
* 修改日期: 无
* 修改内容: 无
*******************************************************************************/
void SbcCtrl_SetSbcGoToLPOFF(void)
{
	Std_ReturnType eReturnValue = E_NOT_OK;

	/*清除唤醒标志，不然休眠后会被重新唤醒*/
	eReturnValue = CanTrcv_fs23_ClearWuFlags(0);
	
	if(eReturnValue != E_OK)
	{
		SBC_CTRL_PRINTF("Fail:CanTrcv_fs23_ClearWuFlags");
	}
	
	eReturnValue = Sbc_fs23_SetOperatingMode(SBC_FS23_OPMODE_LPOFF);

	if(eReturnValue != E_OK)
    {
        SBC_CTRL_PRINTF("Fail:SbcCtrl_SetSbcGoToLPOFF");
       return;
    } 
}

/******************************************************************************
* 函数名称: SbcCtrl_SetCANMode
* 设计作者: 
* 设计日期: 2020-07-08 09:01
* 功能描述: 
* 参数描述: 无
* 返回类型: 无
* 修改人员: 无
* 修改日期: 无
* 修改内容: 无
*******************************************************************************/
void SbcCtrl_SetCANMode(Sbc_TrcvModeType TrcvMode)
{
	Std_ReturnType eReturnValue = E_NOT_OK;
		
	switch(TrcvMode)
    {
        case SBC_TRCVMODE_OFF:
            eReturnValue = CanTrcv_fs23_SetMode(0,CANTRCV_FS23_TRCVMODE_OFF);
			break;
		
        case SBC_TRCVMODE_LISTENONLY:
            eReturnValue = CanTrcv_fs23_SetMode(0,CANTRCV_FS23_TRCVMODE_LISTENONLY);
            break;

        case SBC_TRCVMODE_NORMAL:
            eReturnValue = CanTrcv_fs23_SetMode(0,CANTRCV_FS23_TRCVMODE_NORMAL);
            break;

        default:
            eReturnValue = (Std_ReturnType)E_NOT_OK;
            break;
    }

	if(eReturnValue != E_OK)
    {
        SBC_CTRL_PRINTF("Fail:SbcCtrl_SetCANMode");
    } 
}

/******************************************************************************
* 函数名称: SbcCtrl_GetCANMode
* 设计作者: 
* 设计日期: 2020-07-08 09:01
* 功能描述: 获取Can收发器状态
* 参数描述: 无
* 返回类型: 无
* 修改人员: 无
* 修改日期: 无
* 修改内容: 无
*******************************************************************************/
Sbc_TrcvModeType SbcCtrl_GetCANMode(void)
{
	Std_ReturnType eReturnValue = E_NOT_OK;
	CanTrcv_fs23_TrcvModeType OpMode = CANTRCV_FS23_TRCVMODE_INVALID;
	Sbc_TrcvModeType SbcCANOpMode = SBC_TRCVMODE_INVALID;

	eReturnValue = CanTrcv_fs23_GetMode(0,&OpMode);
	
	if(eReturnValue == E_OK)
	{			
		switch(OpMode)
		{
	        case CANTRCV_FS23_TRCVMODE_OFF:
				SbcCANOpMode = SBC_TRCVMODE_OFF;
	            break;

	        case CANTRCV_FS23_TRCVMODE_LISTENONLY:	
				SbcCANOpMode = SBC_TRCVMODE_LISTENONLY;
	            break;

	        case CANTRCV_FS23_TRCVMODE_NORMAL:
				SbcCANOpMode = SBC_TRCVMODE_NORMAL;
	            break;
	
			case CANTRCV_FS23_TRCVMODE_INVALID:
				SbcCANOpMode = SBC_TRCVMODE_INVALID;
				break;
			
	        default:
	            SbcCANOpMode = SBC_TRCVMODE_INVALID;
	            break;
    	}	
	
	}
	else
    {
        SBC_CTRL_PRINTF("Fail:SbcCtrl_GetCANMode");
    } 

	return SbcCANOpMode;
}

/******************************************************************************
* 函数名称: SbcCtrl_GetWakeUpReason
* 设计作者: 
* 设计日期: 2020-07-08 09:01
* 功能描述: 
* 参数描述: 无
* 返回类型: 无
* 修改人员: 无
* 修改日期: 无
* 修改内容: 无
*******************************************************************************/
Sbc_WakeupReasonType SbcCtrl_GetWakeUpReason(void)
{
	return GeunWakeupReason;
}

/******************************************************************************
* 函数名称: SbcCtrl_GetWake1IOSts
* 设计作者: 
* 设计日期: 2020-07-08 09:01
* 功能描述: 
* 参数描述: 无
* 返回类型: 无
* 修改人员: 无
* 修改日期: 无
* 修改内容: 无
*******************************************************************************/
Sbc_IOSts SbcCtrl_GetWake1IOSts(void)
{
	uint16 RxData = 0;
	Std_ReturnType eReturnValue = E_NOT_OK;        

    eReturnValue = Sbc_fs23_ReadRegister(SBC_FS23_M_STATUS_ADDR, &RxData);

    if((Std_ReturnType)E_OK == eReturnValue)
    {
      if(SBC_FS23_M_WK1_S_HIGH == (RxData & SBC_FS23_M_WK1_S_MASK))
      {
          return SBC_IO_HIGH;
      }
      else
      {
          return SBC_IO_LOW;
      }
    }
    else
    {
        return SBC_IO_LOW;
        SBC_CTRL_PRINTF("Fail:SbcCtrl_GetWake1IOSts");
    } 
}

/******************************************************************************
* 函数名称: SbcCtrl_ReadTempSts
* 设计作者: 
* 设计日期: 2020-07-08 09:01
* 功能描述: 
* 参数描述: 
* 返回类型: 无
* 修改人员: 无
* 修改日期: 无
* 修改内容: 无
*******************************************************************************/
void SbcCtrl_ReadTempSts(SbcTempSts_e *pSts)
{	
	if(Sbc_Temperature_Over == SbcTempDiag.SbcTempSts)
	{	
		*pSts = Sbc_Temperature_Over;
	}
	else
	{
		*pSts = Sbc_Temperature_Normal;
	}
}

/******************************************************************************
* 函数名称: SbcCtrl_ReadWDErrFlg
* 设计作者: 
* 设计日期: 2020-07-08 09:01
* 功能描述: 
* 参数描述: 
* 返回类型: 无
* 修改人员: 无
* 修改日期: 无
* 修改内容: 无
*******************************************************************************/
void SbcCtrl_ReadWDErrFlg(uint8_t* Lu8WDErrFlg)
{	
	*Lu8WDErrFlg = WDErrFlg;
}

/*END SbcCtrl.c*/

