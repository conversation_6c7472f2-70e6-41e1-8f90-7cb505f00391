"""
测试读操作响应解析器的位字段解析功能
"""

from command_parser import (
    CommandParserFactory,
    ResponseParserFactory
)
import json

def parse_hex_string(hex_string):
    """
    解析十六进制字符串为字节列表
    
    Args:
        hex_string: 十六进制字符串，如"02 50 ff ff ff ff 7a"
        
    Returns:
        字节列表
    """
    # 移除所有空白字符
    hex_string = "".join(hex_string.split())
    
    # 检查字符串长度是否为偶数
    if len(hex_string) % 2 != 0:
        raise ValueError("十六进制字符串长度必须为偶数")
    
    # 将字符串转换为字节列表
    return [int(hex_string[i:i+2], 16) for i in range(0, len(hex_string), 2)]

def test_read_response_parser():
    """测试读操作响应解析器"""
    print("\n=== 测试读操作响应解析器 ===")
    
    # 测试用例1：读操作响应，地址为0x11（REG17，包含多个位字段）
    command_str = "03 11 00 00 00 00 E8"
    response_str = "00 83 80 00 00 01 92"
    
    # 解析命令
    command_bytes = parse_hex_string(command_str)
    command_parser = CommandParserFactory.create_parser(command_bytes)
    command_result = command_parser.parse(command_bytes)
    
    print("命令解析结果:")
    print(json.dumps(command_result, indent=2))
    
    # 解析响应
    response_bytes = parse_hex_string(response_str)
    response_parser = ResponseParserFactory.create_parser(command_result["type"], command_result)
    response_result = response_parser.parse(response_bytes, command_result)
    
    print("\n响应解析结果:")
    print(json.dumps(response_result, indent=2))
    
    # 打印位字段定义
    if "bit_fields" in response_result:
        print("\n位字段定义:")
        print(json.dumps(response_result["bit_fields"], indent=2))
    
    # 打印位字段定义的十六进制表示
    if "bit_fields_hex" in response_result:
        print("\n位字段定义的十六进制表示:")
        print(json.dumps(response_result["bit_fields_hex"], indent=2))
    
    # 测试用例2：读操作响应，地址为0x02（REG2，包含多个位字段）
    command_str = "03 02 00 00 00 00 E8"
    response_str = "00 83 80 00 00 01 92"
    
    # 解析命令
    command_bytes = parse_hex_string(command_str)
    command_parser = CommandParserFactory.create_parser(command_bytes)
    command_result = command_parser.parse(command_bytes)
    
    print("\n命令解析结果:")
    print(json.dumps(command_result, indent=2))
    
    # 解析响应
    response_bytes = parse_hex_string(response_str)
    response_parser = ResponseParserFactory.create_parser(command_result["type"], command_result)
    response_result = response_parser.parse(response_bytes, command_result)
    
    print("\n响应解析结果:")
    print(json.dumps(response_result, indent=2))
    
    # 打印位字段定义
    if "bit_fields" in response_result:
        print("\n位字段定义:")
        print(json.dumps(response_result["bit_fields"], indent=2))
    
    # 打印位字段定义的十六进制表示
    if "bit_fields_hex" in response_result:
        print("\n位字段定义的十六进制表示:")
        print(json.dumps(response_result["bit_fields_hex"], indent=2))

if __name__ == "__main__":
    test_read_response_parser()
