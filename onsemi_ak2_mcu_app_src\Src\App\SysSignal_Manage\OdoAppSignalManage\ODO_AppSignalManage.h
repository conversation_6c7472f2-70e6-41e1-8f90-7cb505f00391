/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * ApaCalCarCoorSignalManage.h: 
 * Created on: 2020-12-07 13:51
 * Original designer: 22554
 ******************************************************************************/

#ifndef __APACALCARCOORSIGNALMANAGE_H__
#define __APACALCARCOORSIGNALMANAGE_H__

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "types.h"
#include "ApaCalCarCoor.h"

/******************************************************************************
 * Compiler option used to distinguish inclusion done by the owner
 *****************************************************************************/
#if   1

/******************************************************************************
 * Inclusion of private header files
 *****************************************************************************/


#ifdef __cplusplus
extern "C"{
#endif

/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/
/******************************************************************************
* 设计描述 : ODOCoorInfo
* 设计索引 : 89
*******************************************************************************/
typedef struct
{
    float fODO_X;               /* ODO X坐标 */
    float fODO_Y;               /* ODO Y坐标 */
    float fODO_YawAngle;        /* 航向角 */
    float fODO_SHA;             /* 行驶距离 */
    uint32 u32ODO_TurnRadian;   /* 转弯半径 */
    uint32 u32ODO_CalTimeStamp; /* ODO更新的时间戳 */
}ODOCoorInfo;


/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/

uint8 CPOSSgnMag_ReadGetCalId(void);
void CPOSSgnMag_WriteClrCalId(uint8 id);
void CPOSSgnMag_ReadCarCoorData(CPOS_CurCarPointDataType *LpstrCurCarPointData,uint8 id);
void CPOSSgnMag_WriteCarCoorData(CPOS_CurCarPointDataType LpstrCurCarPointData,uint8 id);
void CPOSSgnMag_ReadCarTurnRadian(uint32 *LpCarTurnRadian);
void CPOSSgnMag_WriteCarTurnRadian(uint32 LpCarTurnRadian);
void CPOSSgnMag_ReadCalTimeStamp(uint64 *LpTimeStamp);
void CPOSSgnMag_WriteCalTimeStamp(uint64 Lu64TimeStamp);

uint8 CPOSSgnMag_ReadCarMoveDirect(void);
void CPOSSgnMag_WriteCarMoveDirect(uint8 Lu8Direct);


void CalODOCoordinate(void);
void ReadAPASignal_ODOInfo(ODOCoorInfo *LpstrOdoCoorInfo);
void WriteAPASignal_ODOInfo(ODOCoorInfo *LpstrOdoCoorInfo);




#ifdef __cplusplus
}
#endif

#endif /* end of SYS_USE_XXX */

#endif /* end of SYS_USE_XXX */

