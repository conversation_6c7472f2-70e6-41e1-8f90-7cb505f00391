/*
 * DSI3_SPI.c
 *
 *  Created on: 2021年4月1日
 *      Author: 6000021992
 */
#include "DSI3_SPI.h"
#include <DSI_521_42.h>
#include "Queue_DSI_SPI_Data.h"
#include "AK2_MCU_Drv.h"

typedef struct
{
    DSISPI_SeqID ActiveSeq;
    DSISPI_SeqID NextSeq;
    DSISPI_TransmitStateType TransmitStateType;
    DsiSpi_WorkType DSISPISeq[DSISPI_SEQ_NUM];
    DSI_SPI_Interface_str DSI_SPI_Interface;
}Spi_SeqWorkType_str;

Spi_SeqWorkType_str Spi_SeqWorkType[DSIMasterNum];

uint8 SPITxData[DSIMasterNum][DSISPI_RXBuf_NUM][DSI_SPI_TX_RX_BufLen];

#pragma location = "R_DMA_DATA"
uint8 GDSI_SPITxBuf[DSIMasterNum][DSISPI_SEQ_NUM][DSI_SPI_TX_RX_BufLen];

AK2_Pub_FilterCtrl_t SpiTimeOutFilCtrl[DSIMasterNum] = 
{	
	[DSIMaster0] = {
					   .UpperLimit = 5,
	                   .LowerLimit = 0,
					   .UpStep = 1,
	                   .DownStep = 5,
	                   .Count = 0,
	                   .Input = FALSE,
	                   .Output = FALSE,
				   },

	[DSIMaster1] = {
					   .UpperLimit = 5,
				       .LowerLimit = 0,
					   .UpStep = 1,
				       .DownStep = 5,
				       .Count = 0,
				       .Input = FALSE,
				       .Output = FALSE,
				   },
};

/******************************************************************************/
/**
 * @brief            初始化DSI SPI工作序列
 * @param[in]        DSIMasterID，pDSI_SPI_Interface
 * @param[Out]       
 *
 * @return           0xFF : 
 *                   0    :
 */
/*****************************************************************************/
uint8 InitDSISPISeq(uint8 DSIMasterID,DSI_SPI_Interface_str *pDSI_SPI_Interface)
{
    uint8 LcCnt = 0;
    Spi_SeqWorkType_str *pSpi_SeqWorkDat;
    
    if((DSIMasterID >= DSIMasterNum) 
        || (pDSI_SPI_Interface == NULL)
        || (pDSI_SPI_Interface->DSI_Spi_AsyncTransmitFunc == NULL))
    {
        return 0xFF;
    }
    pSpi_SeqWorkDat = &Spi_SeqWorkType[DSIMasterID];
    
    memcpy(&pSpi_SeqWorkDat->DSI_SPI_Interface,pDSI_SPI_Interface,sizeof(DSI_SPI_Interface_str));

    memset(&pSpi_SeqWorkDat->DSISPISeq[0],0,sizeof(DsiSpi_WorkType)*DSISPI_SEQ_NUM);
    
    for(LcCnt = 0;LcCnt < DSISPI_SEQ_NUM;LcCnt++)
    {
        pSpi_SeqWorkDat->DSISPISeq[LcCnt].vTransmitState = DSISPI_TRANS_IDLE;
        pSpi_SeqWorkDat->DSISPISeq[LcCnt].u16SendTimeOut = 5;
        pSpi_SeqWorkDat->DSISPISeq[LcCnt].pDSI_SPITxBuf = &GDSI_SPITxBuf[DSIMasterID][LcCnt][0];
        pSpi_SeqWorkDat->DSISPISeq[LcCnt].pDSI_SPIRxBuf = NULL;
    }
    pSpi_SeqWorkDat->ActiveSeq = DSISPI_SEQ_NULL;

    pSpi_SeqWorkDat->TransmitStateType = DSISPI_TRANS_IDLE;
    
    return 0;
}

/******************************************************************************/
/**
 * @brief           获取空闲SeqID
 * @param[in]        DSIMasterID
 * @param[Out]       DSISPI_SeqID
 *
 * @return           0xFF : 无效SeqID
 *                   else   SeqID
 */
/*****************************************************************************/
DSISPI_SeqID GetDSI_SPI_IdleSeq(uint8 DSIMasterID)
{
    Spi_SeqWorkType_str *pSpi_SeqWorkDat;
    if(DSIMasterID >= DSIMasterNum)
    {
        return DSISPI_SEQ_NULL;
    }
    pSpi_SeqWorkDat = &Spi_SeqWorkType[DSIMasterID];


    DSISPI_SeqID LcCnt = DSISPI_Start_SEQ;
    for(LcCnt = DSISPI_Start_SEQ;LcCnt < DSISPI_SEQ_NUM;LcCnt++)
    {
        if((pSpi_SeqWorkDat->DSISPISeq[LcCnt].vTransmitState == DSISPI_TRANS_IDLE) 
            || (pSpi_SeqWorkDat->DSISPISeq[LcCnt].vTransmitState == DSISPI_TRANS_DONE))
        {
            pSpi_SeqWorkDat->DSISPISeq[LcCnt].vTransmitState = DSISPI_TRANS_SEL;
            return LcCnt;
        }
    }
    return DSISPI_SEQ_NULL;
}


/******************************************************************************/
/**
 * @brief            设置DSI SPI序列工作状态
 * @param[in]         SeqID   序列ID
 * @param[in]         TransmitState 传输状态
 *
 * @return           0xFF : 无效SeqID
 *                   else   SeqID
 */
/*****************************************************************************/
DSISPI_SeqID SetDSI_SPI_SeqTransmitState(uint8 DSIMasterID,DSISPI_SeqID SeqID,DSISPI_TransmitStateType TransmitState)
{
    Spi_SeqWorkType_str *pSpi_SeqWorkDat;
    if((DSIMasterID >= DSIMasterNum)||(SeqID >= DSISPI_SEQ_NUM))
    {
        return DSISPI_SEQ_NULL;
    }
    pSpi_SeqWorkDat = &Spi_SeqWorkType[DSIMasterID];

    pSpi_SeqWorkDat->DSISPISeq[SeqID].vTransmitState = TransmitState;
    pSpi_SeqWorkDat->DSISPISeq[SeqID].u16SendTimeOutCnt = 0;
    return SeqID;
}


/******************************************************************************/
/**
 * @brief            获取DSI SPI序列传输状态
 * @param[in]         SeqID   序列ID
 *
 * @return           序列传输状态
 */
/*****************************************************************************/
DSISPI_TransmitStateType GetDSI_SPI_SeqTransmitState(uint8 DSIMasterID,DSISPI_SeqID SeqID)
{
    DSISPI_TransmitStateType TransmitState;
    Spi_SeqWorkType_str *pSpi_SeqWorkDat;
    if((DSIMasterID >= DSIMasterNum)||(SeqID >= DSISPI_SEQ_NUM))
    {
        return DSISPI_TRANS_NULL;
    }
    pSpi_SeqWorkDat = &Spi_SeqWorkType[DSIMasterID];
    
    TransmitState = pSpi_SeqWorkDat->DSISPISeq[SeqID].vTransmitState;

    /*传输状态为DSISPI_TRANS_DONE 时，读取状态置为DSISPI_TRANS_IDLE*/
    if(TransmitState == DSISPI_TRANS_DONE)
    {
        pSpi_SeqWorkDat->DSISPISeq[SeqID].vTransmitState = DSISPI_TRANS_IDLE;
        TransmitState = DSISPI_TRANS_DONE;

    }
    return  TransmitState;
}

/******************************************************************************/
/**
 * @brief            记录SPI传输结束时间
 * @param[in]         DSIMasterID  
 * @param[in]         SeqID   序列ID
 *
 */
/*****************************************************************************/
void SetDSI_SPI_SeqTransCompleteTime(uint8 DSIMasterID, DSISPI_SeqID SeqID)
{
	Spi_SeqWorkType_str *pSpi_SeqWorkDat;
	if((DSIMasterID < DSIMasterNum) && (SeqID < DSISPI_SEQ_NUM))
    {
		pSpi_SeqWorkDat = &Spi_SeqWorkType[DSIMasterID];

		pSpi_SeqWorkDat->DSISPISeq[SeqID].u32TransCompleteTime = AK2_GetSys1p6usTickFunc();
    }
}

/******************************************************************************/
/**
 * @brief             获取SPI传输结束时间
 * @param[in]         DSIMasterID  
 * @param[in]         SeqID   序列ID
 *
 * @return            传输结束时间(us)
 */
/*****************************************************************************/
uint32 GetDSI_SPI_SeqTransCompleteTime(uint8 DSIMasterID, DSISPI_SeqID SeqID)
{
	Spi_SeqWorkType_str *pSpi_SeqWorkDat;
	if((DSIMasterID >= DSIMasterNum)||(SeqID >= DSISPI_SEQ_NUM))
    {
        return 0;
    }
    pSpi_SeqWorkDat = &Spi_SeqWorkType[DSIMasterID];

	return pSpi_SeqWorkDat->DSISPISeq[SeqID].u32TransCompleteTime;
}

/******************************************************************************/
/**
 * @brief             获取SPI传输数据
 * @param[in]           
 *
 * @return            
 */
/*****************************************************************************/
const uint8* GetDSI_SPI_SeqTransData(uint8 DSIMasterID,uint16 DataBufIdx)
{
	if((DataBufIdx >= DSISPI_RXBuf_NUM) || (DSIMasterID >= DSIMasterNum))
    {
        return 0;
    }

	return (const uint8*)&SPITxData[DSIMasterID][DataBufIdx][0];
}

/******************************************************************************/
/**
 * @brief            传输DSI SPI数据
 * @param[in]         TxBuf   传输数据地址
 * @param[in]         TxSize  传输数据长度
 *
 * @return           0xFF : 无空闲SeqID
 *                   else   SeqID
 */
/*****************************************************************************/
DSISPI_SeqID DSI_SPI_AsyncTransfer (uint8 DSIMasterID,uint8 *TxBuf,uint16 TxSize)
{
    Spi_SeqWorkType_str *pSpi_SeqWorkDat;
    DSISPI_SeqID SeqID;
    DSI_SPI_RX_Data_str *pDSI_SPI_RX_Data;
    uint8 *pRxBuf = NULL;
    uint8 *pTxBuf = NULL;
    uint8 RxBufNum = 0xFF;
    DSISPI_TransReturn_en SpiTransFlg;
    if(DSIMasterID >= DSIMasterNum)
    {
        return DSISPI_SEQ_NULL;
    }
    pSpi_SeqWorkDat = &Spi_SeqWorkType[DSIMasterID];
    
    DSI_DisableInterrupts();

    SeqID = GetDSI_SPI_IdleSeq(DSIMasterID);
    
    DSI_EnableInterrupts();
    
    if((SeqID >= DSISPI_SEQ_NUM) || (TxSize > DSI_SPI_TX_RX_BufLen) || (TxBuf == NULL) )
    {
        return DSISPI_SEQ_NULL;
    }
    else
    {
    
        DSI_DisableInterrupts();
        
        pDSI_SPI_RX_Data= GetDSI_SPI_RX_IdleBufAddr(TxSize,&RxBufNum);
        
        DSI_EnableInterrupts();
        
        pTxBuf = pSpi_SeqWorkDat->DSISPISeq[SeqID].pDSI_SPITxBuf;
        
        if((RxBufNum == 0xFF) || (pDSI_SPI_RX_Data == NULL) || (pTxBuf == NULL))
        {
            return DSISPI_SEQ_NULL;
        }
        else
        {
            
        }
        pRxBuf = &pDSI_SPI_RX_Data->SPIResData[0];
        
        pDSI_SPI_RX_Data->DataLen = TxSize;                
        pDSI_SPI_RX_Data->TransSeqID = SeqID;
        pDSI_SPI_RX_Data->DSIMasterID = DSIMasterID;   
        memcpy((void *)&SPITxData[DSIMasterID][RxBufNum][0],(const void*)TxBuf,TxSize);
				
        pSpi_SeqWorkDat->DSISPISeq[SeqID].u8RxBufNum = RxBufNum;
        pSpi_SeqWorkDat->DSISPISeq[SeqID].pDSI_SPIRxBuf = pRxBuf;
        pSpi_SeqWorkDat->DSISPISeq[SeqID].u16TransmitLen = TxSize;
        
        memcpy(pTxBuf,TxBuf,TxSize);
        
        DSI_DisableInterrupts();

        if(pSpi_SeqWorkDat->TransmitStateType == DSISPI_TRANS_IDLE)
        {
            pSpi_SeqWorkDat->TransmitStateType = DSISPI_TRANS_BUSY;
            pSpi_SeqWorkDat->ActiveSeq = SeqID;
            
            DSI_EnableInterrupts();
            
            SetDSI_SPI_SeqTransmitState( DSIMasterID, SeqID,DSISPI_TRANS_BUSY);
            
            /** @brief SPI传输 */
            if(pSpi_SeqWorkDat->DSI_SPI_Interface.DSI_Spi_AsyncTransmitFunc != NULL)
            {
                SpiTransFlg = pSpi_SeqWorkDat->DSI_SPI_Interface.DSI_Spi_AsyncTransmitFunc(pTxBuf, pRxBuf,TxSize);
                if(SpiTransFlg != eDSISPI_TransReturn_OK)
                {
                    /** @brief SPI传输请求ERR */
                    return DSISPI_SEQ_NULL;
                }
            }
            else
            {
                /** @brief ERR */
            
                return DSISPI_SEQ_NULL;
            }
            pSpi_SeqWorkDat->NextSeq = (DSISPI_SeqID)(SeqID + 1);

            if(pSpi_SeqWorkDat->NextSeq >= DSISPI_SEQ_NUM)
            {
                pSpi_SeqWorkDat->NextSeq = DSISPI_Start_SEQ;
            }
            else
            {

            }
        }
        else
        {
            SetDSI_SPI_SeqTransmitState(DSIMasterID,SeqID,DSISPI_TRANS_WAIT);
        }
        DSI_EnableInterrupts();
    }
    return SeqID;
}

DSISPI_SeqID DSI_SPI_SyncTransfer (uint8 DSIMasterID,uint8 *TxBuf,uint16 TxSize,uint8 *RxBuf,uint8 timeOut_ms)
{
    Spi_SeqWorkType_str *pSpi_SeqWorkDat;
    DSISPI_SeqID SeqID;
    
    uint8 *pRxBuf = NULL;
    uint8 *pTxBuf = NULL;
    
    DSISPI_TransReturn_en SpiTransFlg;
    uint32 Sys1msTickBak = 0;
    uint8 cTimeOut_ms = timeOut_ms + 1;
    if((DSIMasterID >= DSIMasterNum) || (TxBuf == NULL) || (RxBuf == NULL))
    {
        return DSISPI_SEQ_NULL;
    }
    pSpi_SeqWorkDat = &Spi_SeqWorkType[DSIMasterID];
    DSI_DisableInterrupts();

    SeqID = GetDSI_SPI_IdleSeq(DSIMasterID);
    
    DSI_EnableInterrupts();
    if(SeqID >= DSISPI_SEQ_NUM || TxSize > DSI_SPI_TX_RX_BufLen)
    {
        return DSISPI_SEQ_NULL;
    }
    else
    {
        pTxBuf = pSpi_SeqWorkDat->DSISPISeq[SeqID].pDSI_SPITxBuf;
        
        if(pTxBuf == NULL)
        {
            return DSISPI_SEQ_NULL;
        }
        else
        {
            
        }
        memcpy(pTxBuf,TxBuf,TxSize);
        pRxBuf = RxBuf;
        
        pSpi_SeqWorkDat->DSISPISeq[SeqID].u8RxBufNum = 0xFF;
        pSpi_SeqWorkDat->DSISPISeq[SeqID].pDSI_SPIRxBuf = pRxBuf;
        pSpi_SeqWorkDat->DSISPISeq[SeqID].u16TransmitLen = TxSize;
        
        
        /** @brief 临界区关中断 */
        DSI_DisableInterrupts();

        /** @brief 当前SPI空闲时，启动传输 */
        if(pSpi_SeqWorkDat->TransmitStateType == DSISPI_TRANS_IDLE)
        {
            pSpi_SeqWorkDat->TransmitStateType = DSISPI_TRANS_BUSY;
            pSpi_SeqWorkDat->ActiveSeq = SeqID;
            
            DSI_EnableInterrupts();
            
            SetDSI_SPI_SeqTransmitState(DSIMasterID, SeqID,DSISPI_TRANS_BUSY);

            /** @brief SPI传输 */
            if(pSpi_SeqWorkDat->DSI_SPI_Interface.DSI_Spi_AsyncTransmitFunc != NULL)
            {
                SpiTransFlg = pSpi_SeqWorkDat->DSI_SPI_Interface.DSI_Spi_AsyncTransmitFunc(pTxBuf,pRxBuf,TxSize);
                if(SpiTransFlg != eDSISPI_TransReturn_OK)
                {
                    /** @brief SPI传输请求ERR */
                    return DSISPI_SEQ_NULL;
                }
            }
            else
            {
                /** @brief ERR */
            
                return DSISPI_SEQ_NULL;
            }
            pSpi_SeqWorkDat->NextSeq = (DSISPI_SeqID)(SeqID + 1);

            if(pSpi_SeqWorkDat->NextSeq >= DSISPI_SEQ_NUM)
            {
                pSpi_SeqWorkDat->NextSeq = DSISPI_Start_SEQ;
            }
            else
            {

            }
        }
        else
        {
            SetDSI_SPI_SeqTransmitState(DSIMasterID,SeqID,DSISPI_TRANS_WAIT);
        }
        
        DSI_EnableInterrupts();

    }
    Sys1msTickBak = AK2_GetSys1msTickFunc();
    
    while(GetDSI_SPI_SeqTransmitState(DSIMasterID,SeqID) != DSISPI_TRANS_IDLE)
    {
        /** @brief 等待传输完成 */
    
        /** @brief 检查超时*/
        if(ABS(Sys1msTickBak, AK2_GetSys1msTickFunc()) > cTimeOut_ms)
        {
			SpiTimeOutFilCtrl[DSIMasterID].Input = TRUE;
			AK2_PubAI_Filter(&SpiTimeOutFilCtrl[DSIMasterID]);
            return DSISPI_SEQ_NULL;
        }
    }
	
	SpiTimeOutFilCtrl[DSIMasterID].Input = FALSE;
	AK2_PubAI_Filter(&SpiTimeOutFilCtrl[DSIMasterID]);
	
    return SeqID;
}

/******************************************************************************/
/**
 * @brief            DSI SPI序列传输完成回调
 * @param[in]         SeqID   序列ID

 *
 * @return           
 */
/*****************************************************************************/
void DSI_SPI_CompleteTransfer_HandleCbk(uint8 DSIMasterID)
{
    DSISPI_SeqID SeqID = DSISPI_Start_SEQ;
    DSISPI_SeqID SeqIDCnt = DSISPI_Start_SEQ;
    uint8 LcCnt = 0;
    uint8 LcWaitCnt = 0;
    uint16 TxSize = 0;
    DSISPI_SeqID WaitID[DSISPI_SEQ_NUM];
    
    DSISPI_TransReturn_en SpiTransFlg;
    uint8 *pRxBuf = NULL;
    uint8 *pTxBuf = NULL;
    
    Spi_SeqWorkType_str *pSpi_SeqWorkDat;
    
    pSpi_SeqWorkDat = &Spi_SeqWorkType[DSIMasterID];
    
    pSpi_SeqWorkDat->TransmitStateType = DSISPI_TRANS_IDLE;

	SpiTimeOutFilCtrl[DSIMasterID].Input = FALSE;
	AK2_PubAI_Filter(&SpiTimeOutFilCtrl[DSIMasterID]);

    if(pSpi_SeqWorkDat->ActiveSeq < DSISPI_SEQ_NUM)
    {
        SetDSI_SPI_RX_Data_Ready(pSpi_SeqWorkDat->DSISPISeq[pSpi_SeqWorkDat->ActiveSeq].u8RxBufNum);

		SetDSI_SPI_SeqTransCompleteTime(DSIMasterID,pSpi_SeqWorkDat->ActiveSeq);
		
        SetDSI_SPI_SeqTransmitState(DSIMasterID,pSpi_SeqWorkDat->ActiveSeq,DSISPI_TRANS_IDLE);
    }
    
    for(SeqIDCnt = DSISPI_Start_SEQ ;SeqIDCnt < DSISPI_SEQ_NUM;SeqIDCnt++)
    {
        if(pSpi_SeqWorkDat->DSISPISeq[SeqIDCnt].vTransmitState == DSISPI_TRANS_WAIT)
        {
            WaitID[LcWaitCnt] = SeqIDCnt;
            LcWaitCnt++;
        }
    }
    if(LcWaitCnt != 0)
    {
        for(SeqIDCnt = DSISPI_Start_SEQ ;SeqIDCnt < DSISPI_SEQ_NUM;SeqIDCnt++)
        {
            /** @brief 遍历查询待传输序列，且按顺序传输 */
            for(LcCnt = 0 ;LcCnt < LcWaitCnt;LcCnt++)
            {
                /** @brief 顺序传输序列 */
                if(WaitID[LcCnt] == pSpi_SeqWorkDat->NextSeq)
                {
                    SeqID = WaitID[LcCnt];
                    pSpi_SeqWorkDat->TransmitStateType = DSISPI_TRANS_BUSY;
                    pSpi_SeqWorkDat->ActiveSeq = SeqID;
                    
                    SetDSI_SPI_SeqTransmitState(DSIMasterID, SeqID,DSISPI_TRANS_BUSY);

                    TxSize = pSpi_SeqWorkDat->DSISPISeq[SeqID].u16TransmitLen;
                    
                    pRxBuf = pSpi_SeqWorkDat->DSISPISeq[SeqID].pDSI_SPIRxBuf;
                    pTxBuf = pSpi_SeqWorkDat->DSISPISeq[SeqID].pDSI_SPITxBuf;

                    /** @brief SPI传输 */
                    if(pSpi_SeqWorkDat->DSI_SPI_Interface.DSI_Spi_AsyncTransmitFunc != NULL)
                    {
                        SpiTransFlg = pSpi_SeqWorkDat->DSI_SPI_Interface.DSI_Spi_AsyncTransmitFunc(pTxBuf, pRxBuf,TxSize);
                        
                        if(SpiTransFlg != eDSISPI_TransReturn_OK)
                        {
                            /** @brief SPI传输请求ERR */
                            return ;
                        }
                    }    
                    else
                    {
                        /** @brief ERR */

                        return ;
                    }
                    
                    pSpi_SeqWorkDat->NextSeq ++;

                    if(pSpi_SeqWorkDat->NextSeq >= DSISPI_SEQ_NUM)
                    {
                        pSpi_SeqWorkDat->NextSeq = DSISPI_Start_SEQ;
                    }

                    return;
                }
                else
                {

                }
            }

            pSpi_SeqWorkDat->NextSeq ++;

            if(pSpi_SeqWorkDat->NextSeq >= DSISPI_SEQ_NUM)
            {
                pSpi_SeqWorkDat->NextSeq = DSISPI_Start_SEQ;
            }
        }
    }
}

uint8 DSI_SPITimeOutMonitor(uint8 DSIMasterID)
{
    Spi_SeqWorkType_str *pSpi_SeqWorkDat;
    if(DSIMasterID >= DSIMasterNum)
    {
        return 0xFF;
    }
    pSpi_SeqWorkDat = &Spi_SeqWorkType[DSIMasterID];

    DSISPI_SeqID SeqID;
    for(SeqID= DSISPI_Start_SEQ; SeqID < DSISPI_SEQ_NUM;SeqID++)
    {
        if((pSpi_SeqWorkDat->DSISPISeq[SeqID].vTransmitState != DSISPI_TRANS_IDLE) && (pSpi_SeqWorkDat->DSISPISeq[SeqID].vTransmitState != DSISPI_TRANS_DONE))
        {
            pSpi_SeqWorkDat->DSISPISeq[SeqID].u16SendTimeOutCnt++;
            if(pSpi_SeqWorkDat->DSISPISeq[SeqID].u16SendTimeOut < pSpi_SeqWorkDat->DSISPISeq[SeqID].u16SendTimeOutCnt)
            {   
                pSpi_SeqWorkDat->DSISPISeq[SeqID].vTransmitState = DSISPI_TRANS_IDLE;
                pSpi_SeqWorkDat->DSISPISeq[SeqID].u8SendTimeOutFlgCnt++;
                /*SPI 传输超时*/
                pSpi_SeqWorkDat->DSISPISeq[SeqID].u16Cnt_Failure++;
				SpiTimeOutFilCtrl[DSIMasterID].Input = TRUE;
				AK2_PubAI_Filter(&SpiTimeOutFilCtrl[DSIMasterID]);
            }
        }
    }
    return 0;
}

DSISPI_WorkSts_en DSI_SPIWorkSts(void)
{
	if((TRUE == SpiTimeOutFilCtrl[DSIMaster0].Output)
		||(TRUE == SpiTimeOutFilCtrl[DSIMaster1].Output))
	{
		return eDSISPI_Abnormal;
	}
	else
	{
		return eDSISPI_Normal;
	}
}
