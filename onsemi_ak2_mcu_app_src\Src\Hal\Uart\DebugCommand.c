#include "debug.h"

typedef void (*DebugFuncPtr)(char *args);
typedef char SWCStrType[20];

typedef struct
{
    uint16 moduleIdx;
    const char SWCStr[20];
} SWCType_s;


typedef struct
{
    const char DebugInfo[20];
    DebugFuncPtr Func;
}DebugFuncType;

const char DebugCommand[] = "debug -";

#define DEBUG_CMD_NUM                           ((sizeof(DebugCommand)/sizeof(DebugCommand[0]))-1u)
#define DEBUG_FUNC_NUM                          (sizeof(GstrDebugFunc)/sizeof(GstrDebugFunc[0]))
#define SWC_LIST_NUM                            (sizeof(GstrSWCList)/sizeof(GstrSWCList[0]))
static void DebugHelpFunc(void);
static void DebugSWCListFunc(void);
static void DebugSWCPrintDisFunc(char *args);
static void DebugSWCPrintEnFunc(char *args);

static DebugFuncType GstrDebugFunc[] = 
{
    {"help",        (DebugFuncPtr)&DebugHelpFunc},
    {"exit",        (DebugFuncPtr)&DebugExitFunc},
    {"swc list",    (DebugFuncPtr)&DebugSWCListFunc},
    {"dis ",        (DebugFuncPtr)&DebugSWCPrintDisFunc},
    {"en ",         (DebugFuncPtr)&DebugSWCPrintEnFunc},
};
static SWCType_s GstrSWCList[] = 
{
    {SPI_MODULE_IDX,         "SPI"},
    {APA_MANAGE_MODULE_IDX,  "ApaManage"},
    {CAN_MODULE_IDX,         "CAN"},
    {ODO_MODULE_IDX,         "ODO"},
    {PDC_MODULE_IDX,         "PDC"},
    {APA_SOLT_MODULE_IDX,    "ApaSolt"},
    {PLFM_MODULE_IDX,        "PLFM"},
    {MCAL_MODULE_IDX,        "MCAL"},
    {SBC_MODULE_IDX,         "SBC"},
};

static inline uint8 DebugStrCmp(const uint8 * str1, const uint8 * str2)
{
    uint8 idx = 0u;
    while((str1[idx] != '\0')&&(str2[idx] != '\0'))
    {
        if(str1[idx] != str2[idx])
        {
            break;
        }
        idx++;
    }
    return idx;
}

void DebugCommandFind(char *args, uint16 len)
{
    char ArgsIn[100];
    uint16 index = 0u;
    uint16 matchNum = 0u;
    uint16 matchMax = 0u;
    DebugFuncPtr Command = NULL_PTR;

    if((len > 95) || (len <= DEBUG_CMD_NUM))
    {
        return;
    }

    if(DebugStrCmp((const uint8*)args, (const uint8*)DebugCommand) == DEBUG_CMD_NUM)
    {
        len -= DEBUG_CMD_NUM;
        for(index = 0u; index < len; index++)
        {
            ArgsIn[index] = args[index + DEBUG_CMD_NUM];
        }
        ArgsIn[len] = '\0';

        for (index = 0; index < DEBUG_FUNC_NUM; index++)
        {
            matchNum = DebugStrCmp((const uint8*)GstrDebugFunc[index].DebugInfo, (const uint8*)ArgsIn);
            if(matchNum > matchMax)
            {
                matchMax = matchNum;
                Command = GstrDebugFunc[index].Func;
            }
        }

        if((matchMax > 0u)&&(Command != NULL_PTR))
        {
            Command(ArgsIn);
        }
        else
        {
            /* print help */
            DebugHelpFunc();
        }
    }
    else
    {
        /* print help */
        DebugHelpFunc();
    }
}

static void DebugHelpFunc(void)
{
    DEBUG_PRINT("-----Command list-----\r\n");
    DEBUG_PRINT("1.debug -help<Open help content>\r\n");
    DEBUG_PRINT("2.debug -exit<Exit uart debug mode>\r\n");
    DEBUG_PRINT("3.debug -close XXX<Close related print by SWC name(XXX)>\r\n");
    DEBUG_PRINT("4.debug -swc list<List current support SWCs>\r\n");
}

static void DebugSWCListFunc(void)
{
    uint8 i = 0u;
    DEBUG_PRINT("  -----SWC list-----\r\n");
    for(i = 0u; i < SWC_LIST_NUM; i++)
    {
        DEBUG_PRINT("  %d.%s\r\n",i+1u,GstrSWCList[i]);
    }
}

static inline uint16 DebugSWCIdxSearch(uint16 offset, char *args)
{
    uint16 index = 0u;
    uint16 matchNum = 0u;
    uint16 matchMax = 0u;
    uint16 moduleIdx = 0xFFu;

    for (index = 0; index < SWC_LIST_NUM; index++)
    {
        matchNum = DebugStrCmp((const uint8*)GstrSWCList[index].SWCStr, (const uint8*)&args[offset]);
        if(matchNum > matchMax)
        {
            matchMax = matchNum;
            moduleIdx = index;
        }
    }

    if((0u == matchMax)||(moduleIdx >= SWC_LIST_NUM))
    {
        moduleIdx = 0xFFu;
    }

    return moduleIdx;
}

static void DebugSWCPrintDisFunc(char *args)
{
    uint16 SWCIdx = 0xFFu;
    SWCIdx = DebugSWCIdxSearch(4u, args);
    if(SWCIdx < SWC_LIST_NUM)
    {
        Debug_SetModulePrintEn(SWCIdx, FALSE);
    }
    else
    {
        DebugSWCListFunc();
    }
}

static void DebugSWCPrintEnFunc(char *args)
{
    uint16 SWCIdx = 0xFFu;
    SWCIdx = DebugSWCIdxSearch(3u, args);
    if(SWCIdx < SWC_LIST_NUM)
    {
        Debug_SetModulePrintEn(SWCIdx, TRUE);
    }
    else
    {
        DebugSWCListFunc();
    }
}