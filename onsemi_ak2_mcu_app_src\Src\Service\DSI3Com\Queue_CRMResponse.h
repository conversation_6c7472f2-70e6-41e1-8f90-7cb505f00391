/*
 * Queue_CRMResponse.h
 *
 *  Created on: 2021Äê3ÔÂ30ÈÕ
 *      Author: 6000021992
 */

#ifndef QUEUE_CRMRESPONSE_H_
#define QUEUE_CRMRESPONSE_H_

#include "DSI3_COM.h"

#define CRM_RES_QUEUQ_SIZE 20

typedef struct
{
    uint16 wQueSize;
    uint16 WriteCnt;
    uint16 ReadCnt;
    CRM_RESPONSE_Data_str QueueDataBuff[CRM_RES_QUEUQ_SIZE];
    QueueDataStatus_en QueDataStatus[CRM_RES_QUEUQ_SIZE];
    uint16 QueueTimeOutCnt[CRM_RES_QUEUQ_SIZE];

}CRM_RES_Que_Str;

extern CRM_RES_Que_Str CRM_RES_Que[DSIMasterNum];

extern uint16 InitCRMRESQueue(DSIMasterID_en LeDSIMasterID);
extern uint16 PutCRMRESData(DSIMasterID_en DSIMasterID,DSIChlID_en DSIChlID,CRM_DSIChlRES_str *pInData);
extern uint16 GetCRMRESData(DSIMasterID_en LeDSIMasterID,CRM_RESPONSE_Data_str *pOutData);
#endif /* QUEUE_CRMRESPONSE_H_ */
