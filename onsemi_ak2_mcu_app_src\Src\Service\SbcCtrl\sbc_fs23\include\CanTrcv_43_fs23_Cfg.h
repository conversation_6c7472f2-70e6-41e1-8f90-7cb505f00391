/*==================================================================================================
*   Project              : RTD AUTOSAR 4.4
*   Platform             : CORTEXM
*   Peripheral           : 
*   Dependencies         : 
*
*   Autosar Version      : 4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : 0.8.0
*   Build Version        : S32K3_RTD_0_8_0_D2303_ASR_REL_4_4_REV_0000_20230310
*
*   (c) Copyright 2020 - 2022 NXP Semiconductors
*   All Rights Reserved.
*
*   NXP Confidential. This software is owned or controlled by NXP and may only be
*   used strictly in accordance with the applicable license terms. By expressly
*   accepting such terms or by downloading, installing, activating and/or otherwise
*   using the software, you are agreeing that you have read, and that you agree to
*   comply with and are bound by, such license terms. If you do not agree to be
*   bound by the applicable license terms, then you may not retain, install,
*   activate or otherwise use the software.
==================================================================================================*/
#ifndef CANTRCV_43_FS23_CFG_H
#define CANTRCV_43_FS23_CFG_H

/**
*   @file    CanTrcv_43_fs23_Cfg.h
*
*   @addtogroup CANTRCV_DRIVER
*   @{
*/

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                                        INCLUDE FILES
* 1) system and project includes
* 2) needed interfaces from external units
* 3) internal and external interfaces from this unit
==================================================================================================*/
//#include "CanTrcv_43_fs23_BOARD_InitPeripherals_PBcfg.h"

/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/
/**
*   @brief      Enable/Disable user mode Support
*/
#define CANTRCV_43_FS23_ENABLE_USER_MODE_SUPPORT            (STD_OFF)

/**
*   @brief      Enable/Disable Precompile Support
*/
#define CANTRCV_43_FS23_PRECOMPILE_SUPPORT                     (STD_ON)

/**
*   @brief      The definition represents number of configured ECUC partitions
*/
#define CANTRCV_43_FS23_MAX_PARTITIONS                         (1U)

/**
*   @brief      Enable/Disable GetCoreID/Multicore
*/
#define CANTRCV_43_FS23_MULTICORE_ENABLED                      (STD_OFF)

/**
*   @brief      Wake up support
*/
#define CANTRCV_43_FS23_WAKEUP_SUPPORT                        (STD_ON)

/**
*   @brief      Enable/Disable Development error detection and notification
*/
#define CANTRCV_43_FS23_DEV_ERROR_DETECT                       (STD_OFF)

/**
*   @brief      Enable/Disable Diagnostic event manager
*/
#define CANTRCV_43_FS23_DISABLE_DEM_REPORT_ERROR_STATUS        (STD_OFF)

/**
*   @brief      Enable/Disable support CanTrcv_GetVersionInfo API
*/
#define CANTRCV_43_FS23_VERSION_INFO_API                       (STD_OFF)

/**
*   @brief      Instance of the CanTrcv Hw unit
*/
#define CANTRCV_43_FS23_INSTANCE                               ((uint8)0U)

/**
*   @brief      Maximum time allowed to the CanTrcv for replying to an SPI command
*/
#define CANTRCV_43_FS23_SPI_COM_TIMEOUT                        ((uint8)0U)

/**
*   @brief      Maximum number of communication retries in case of a failed SPI communication
*/
#define CANTRCV_43_FS23_SPI_COM_RETRIES                        ((uint16)0U)

/**
*   @brief      Enable/Disable synchronous SPI communication
*/
#define CANTRCV_43_FS23_SPI_COM_SYNCHRONOUS                    (true)


/**
*    @brief        SPI level - synchronous transmission
*/
#define CANTRCV_43_FS23_SPI_LEVEL_DELIVERED                    (0U)

/**
*    @brief        Number of connected devices
*/
#define CANTRCV_43_FS23_MAX_DEVICES_NUM                        ((uint32)1U)

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* _CANTRCV_43_FS23_CFG_H_ */

