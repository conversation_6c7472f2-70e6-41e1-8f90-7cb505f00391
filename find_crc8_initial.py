
def crc8_2f(data):
    crc = 0xff  # 假设初始值为0xff，我们将会调整这个值
    for byte in data:
        crc ^= byte
        for _ in range(8):
            if crc & 0x80:
                crc = (crc << 1) ^ 0x2f
            else:
                crc <<= 1
            crc &= 0xff
    return crc


def find_initial_value():
    # 已知的数据和它们对应的CRC8值
    known_data_crc_pairs = [
        (bytes.fromhex("404E7E17B604824001D6C4B6C1DB6D6DC1"), 0x31),
        (bytes.fromhex("200000001F7400AB56C9FF6ED5A5CBD4D6"), 0x7B)
    ]

    # 尝试不同的初始值
    for initial_value in range(0x100):
        correct_for_all = True
        for data, expected_crc in known_data_crc_pairs:
            calculated_crc = crc8_with_initial(data, initial_value)
            if calculated_crc != expected_crc:
                correct_for_all = False
                break

        if correct_for_all:
            print(f"找到可能的初始值: {initial_value:02x}")
            return initial_value

    print("没有找到合适的初始值")
    return None


def crc8_with_initial(data, initial_value):
    crc = initial_value
    for byte in data:
        crc ^= byte
        for _ in range(8):
            if crc & 0x80:
                crc = (crc << 1) ^ 0x2f
            else:
                crc <<= 1
            crc &= 0xff
    return crc


if __name__ == "__main__":
    find_initial_value()