/******************************************************************************
 * @Copyright
 *****************************************************************************/

#ifndef __RDUM_RDUS_WAVE_PROCESS_H__
#define __RDUM_RDUS_WAVE_PROCESS_H__

/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "types.h"

/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/

/******************************************************************************
 * @Type Definitions
 *****************************************************************************/


/******************************************************************************
 * @Function Declaration
 *****************************************************************************/
void RdumRdusWaveProcess_Init(void);
uint8 RdumRdusWaveProcess_Start(void);
void RdumRdusWaveProcess_Process(void);
RdumRdusWaveProcessStatus_t RdumRdusWaveProcess_GetStatus(void);
uint8 RdumRdusWaveProcess_GetBrcResults(BrcResult_t *Results);
void RdumRdusWaveProcess_PrintDecompressedEnvelopes(void);

#endif /* __RDUM_RDUS_WAVE_PROCESS_H__ */
