/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * PSL_Calibration.c: 
 * Created on: 2023-2-27 19:30
 * Original designer: 22866
 ******************************************************************************/

/* ============================================================================================== */
/* Header files																				      */
/* ============================================================================================== */
#include "PSL_Calibration.h"





/******************************************************************************/
/******************************************************************************/
/****************************** Private Definition ****************************/
/******************************************************************************/
/******************************************************************************/





/******************************************************************************/
/******************************************************************************/
/****************************** Public Definition *****************************/
/******************************************************************************/
/******************************************************************************/

const PSL_APP_PARA_TYPE GstrPSLAppPara = 
{
    .u8OutSlotMaxNum = 8,
    .u8SideSlotMaxNum = 8,
    .u16SlotWidthMin_H = 5400,
    .u16SlotWidthMax_H = 7999,
    .u16SlotWidthMin_V = 2450,
    .u16SlotWidthMax_V = 5000,
    .u16SlotDepthMin_H = 1600,
    .u16DetObjDisMin = 400,
    .u16DetObjDisMax = 1900,
    .u16RomoveSlotMoveDis_H = 20000,
    .u16RomoveSlotMoveDis_V = 15000,
    .u8CarSlopeoffset = 45,
};

/******************************PSL标定参数_存储在Ram中--*********************************************/

PSL_APP_PARA_TYPE GstrPSL_APPPara_Ram;

/******************************************************************************/
/******************************************************************************/
/******************************* ****** Function ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************
* 设计描述 : PSL APP 标定参数区分
* 设计索引 : 
*******************************************************************************/


void PSL_APPPara_Init(void)
{
    memcpy(&GstrPSL_APPPara_Ram,&GstrPSLAppPara,sizeof(GstrPSLAppPara));
}
