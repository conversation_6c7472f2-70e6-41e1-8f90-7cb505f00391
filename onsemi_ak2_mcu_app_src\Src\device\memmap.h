/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: memmap.h
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/

/**
 * @brief demo
 * #define R_DMA_START_SEC_VAR
 * #include "memmap.h"
 * XXX vvariable
 * #define R_DMA_STOP_SEC_VAR
 * #include "memmap.h"
 * 
 */


#if defined R_DMA_START_SEC_VAR
    #pragma location = "R_DMA_DATA"
#elif defined R_DMA_STOP_SEC_VAR
    /* location only for one variable, so no default required */
#else
    #error "memmap.h: No valid section define found"
#endif /* if defined R_FDL_START_SEC_PUBLIC_CODE */