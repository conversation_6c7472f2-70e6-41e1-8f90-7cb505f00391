/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * SnsRawData_Prg: 
 * Created on: 2022-12-16 10:42
 * Original designer: AntonyFang
 ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "SnsRawData_Int.h"
//#include "Com_Api.h"
#include "CAN_AppSignalManage.h"
#include "ODO_AppSignalManage.h"
#include "Sns_install_Coordinate.h"
#include "PublicCalAlgorithm_Int.h"
#include "SnsRawDataCalib.h"
#include "SnsDisFollow_Int.h"
#include "SnsEchoFilterAndSigGroup_int.h"
#include "DebugSignalManage.h"
#include "AdvEchoDet.h"
#include "SnsDiag.h"
#include "IODrv.h"
#include "eel_cfg.h"
#include "PSL_RawDataCalib.h"

/******************************************************************************/
/******************************************************************************/
/****************************** Public Definition *****************************/
/******************************************************************************/
/******************************************************************************/
PDCSnsRawDataType GstrPDCSnsRawData[PDC_SNS_GROUP_NUM];
PDCSnsUseOdoType  GstrPDCSnsUseOdo;
PDCSnsDE_CE_DataType GstrSnsDE_CE_Data[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];
StdDEFilterCtrlType StdDEFilterCtrl[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];

PDCSnsCarMovStsType  GstrSnsCarMovSts[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];
#if VS_SIMULATE_ENABLE
float GfMessageTime;
/* 系统ms定时时间，用于点云定时窗口使用 */
uint32 GdSystemMsTimer = 0; 
#endif
SnsSigGroupDataCacheType GstrSnsSigGroupDataCache[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];
SnsSigGroupDisFollowType GstrSnsSigGroupDisFollow[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];

uint16 Gu16CarSpdForSnsUse;         /* 整个sns数据处理中多个地方使用到车速，此处做一个归一化处理 */
sint16 Gs16CarStrAngleForSnsUse;    /* 整个sns数据处理中多个地方使用到方向盘角度，此处做一个归一化处理 */

#if (SNS_DEBUG_DATA_SEND_SWITCH == STD_ON)
const DebugSendToCANFunPtr CanTxDebugMsgHandle[13] = 
{
	[0] = &DebugMsgTxTrigger_6F3,
    [1] = &DebugMsgTxTrigger_6F4,
    [2] = &DebugMsgTxTrigger_6F5,
    [3] = &DebugMsgTxTrigger_6F6,
    [4] = &DebugMsgTxTrigger_6F7,
    [5] = &DebugMsgTxTrigger_6F8,
    [6] = &DebugMsgTxTrigger_6F9,
    [7] = &DebugMsgTxTrigger_6FA,
    [8] = &DebugMsgTxTrigger_6FB,
    [9] = &DebugMsgTxTrigger_6FC,
    [10] = &DebugMsgTxTrigger_6FD,
    [11] = &DebugMsgTxTrigger_6FE,
    [12] = &DebugMsgTxTrigger_6FF,
};
#endif

/******************************************************************************/
/******************************************************************************/
/****************************** Private Function ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/******************************* Public Function ******************************/
/******************************************************************************/
/******************************************************************************/


#if 0
uint32 GetSystemTimeCnt_Ms(void)
{
    uint32 Lu32TimeCnt_Ms = 0;
    Lu32TimeCnt_Ms = (uint32)(GfMessageTime*1000);
    return Lu32TimeCnt_Ms;    
}
#endif


/******************************************************************************
 * 函数名称: PDCSnsRawDataClear
 * 
 * 功能描述: 清零某一通道的RawData数据
 * 
 * 输入参数:LenuSnsGroup--探头分组信息；LenuSnsCh--探头通道信息
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-12-16 11:31   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PDCSnsRawDataClear(PDCSnsGroupType LenuSnsGroup,PDCSnsChannelType LenuSnsCh)
{
    PDCSnsEchoNumType LenuPDCSnsEchoNum;
    
    PDCSnsRawDataType *LpStrPDCSnsRawData;
    LpStrPDCSnsRawData = &GstrPDCSnsRawData[LenuSnsGroup];

    if((LenuSnsGroup == PDC_SNS_GROUP_FRONT)||(LenuSnsGroup == PDC_SNS_GROUP_REAR))
    {
        if(LenuSnsCh < PDC_SNS_CH_NUM)
        {
            LpStrPDCSnsRawData->enuPDCSnsMeasType[LenuSnsCh] = PDC_SNS_MEAS_IDLE;
            //LpStrPDCSnsRawData->u8UpdateFlg[LenuSnsCh] = 0;
            LpStrPDCSnsRawData->u16RingTime[LenuSnsCh] = SNS_INVALID_RING_TIME;

            LpStrPDCSnsRawData->u8MasterEchoCnt[LenuSnsCh] = 0;
            LpStrPDCSnsRawData->u8LeftListenEchoCnt[LenuSnsCh] = 0;
            LpStrPDCSnsRawData->u8RightListenEchoCnt[LenuSnsCh] = 0;
            
            LpStrPDCSnsRawData->u8NFD_Flag[LenuSnsCh] = 0;
            LpStrPDCSnsRawData->u16NFD_Dis[LenuSnsCh] = SNS_INVALID_DIS;
            for(LenuPDCSnsEchoNum = PDC_SNS_ECHO_FIRST; LenuPDCSnsEchoNum < PDC_SNS_ECHO_NUM; LenuPDCSnsEchoNum++)
            {
                LpStrPDCSnsRawData->u16MasterDis[LenuSnsCh][LenuPDCSnsEchoNum] = SNS_INVALID_DIS;
                LpStrPDCSnsRawData->u16MasterWidth[LenuSnsCh][LenuPDCSnsEchoNum] = SNS_INVALID_WIDTH;
                LpStrPDCSnsRawData->u16MasterHeight[LenuSnsCh][LenuPDCSnsEchoNum] = SNS_INVALID_HEIGHT;
                LpStrPDCSnsRawData->u8MasterConfidence[LenuSnsCh][LenuPDCSnsEchoNum] = SNS_INVALID_CONFIDENCE; 

                LpStrPDCSnsRawData->u16LeftListenDis[LenuSnsCh][LenuPDCSnsEchoNum] = SNS_INVALID_DIS;
                LpStrPDCSnsRawData->u16LeftListenWidth[LenuSnsCh][LenuPDCSnsEchoNum] = SNS_INVALID_WIDTH;
                LpStrPDCSnsRawData->u16LeftListenHeight[LenuSnsCh][LenuPDCSnsEchoNum] = SNS_INVALID_HEIGHT;
                LpStrPDCSnsRawData->u8LeftListenConfidence[LenuSnsCh][LenuPDCSnsEchoNum] = SNS_INVALID_CONFIDENCE;

                LpStrPDCSnsRawData->u16RightListenDis[LenuSnsCh][LenuPDCSnsEchoNum] = SNS_INVALID_DIS;
                LpStrPDCSnsRawData->u16RightListenWidth[LenuSnsCh][LenuPDCSnsEchoNum] = SNS_INVALID_WIDTH;
                LpStrPDCSnsRawData->u16RightListenHeight[LenuSnsCh][LenuPDCSnsEchoNum] = SNS_INVALID_HEIGHT;
                LpStrPDCSnsRawData->u8RightListenConfidence[LenuSnsCh][LenuPDCSnsEchoNum] = SNS_INVALID_CONFIDENCE; 
            }
			LpStrPDCSnsRawData->u32DataSynTime[LenuSnsCh] = 0;
            LpStrPDCSnsRawData->u8NoiseAbnormalCnt[LenuSnsCh] = 0;
        }
    }
}


/******************************************************************************
 * 函数名称: PDCSnsClearUpdateFlag
 * 
 * 功能描述: 清除更新标志，主要用于其他模块进行调用清除
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-02-17 09:59   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PDCSnsClearUpdateFlag(PDCSnsGroupType LenuSnsGroup,PDCSnsChannelType LenuSnsCh)
{
    PDCSnsRawDataType *LpStrPDCSnsRawData;
    LpStrPDCSnsRawData = &GstrPDCSnsRawData[LenuSnsGroup];
    LpStrPDCSnsRawData->u8UpdateFlg[LenuSnsCh] = 0;
}



/******************************************************************************
 * 函数名称: ReadPDCSnsUpdateFlag
 * 
 * 功能描述: 读取探头数据更新标志
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-02-17 10:06   V0.1      AntonyFang   初次发布
 ******************************************************************************/
uint8 ReadPDCSnsUpdateFlag(PDCSnsGroupType LenuSnsGroup,PDCSnsChannelType LenuSnsCh)
{
    PDCSnsRawDataType *LpStrPDCSnsRawData;
    LpStrPDCSnsRawData = &GstrPDCSnsRawData[LenuSnsGroup];
    
    return LpStrPDCSnsRawData->u8UpdateFlg[LenuSnsCh];
}


/******************************************************************************
 * 函数名称: PDCSnsUseOdoDataInit
 * 
 * 功能描述: PDC原始数据处理模块使用的Odo信息初始化
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-12-23 15:09   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PDCSnsUseOdoDataInit(void)
{
    PDCSnsUseOdoType *LpStrPDCSnsUseOdo;

    LpStrPDCSnsUseOdo = &GstrPDCSnsUseOdo;

    LpStrPDCSnsUseOdo->u8SnsRawDataUseID = 0xFF;
    LpStrPDCSnsUseOdo->fCar_X_Coor = 0.0;
    LpStrPDCSnsUseOdo->fCar_Y_Coor = 0.0;
    LpStrPDCSnsUseOdo->fCar_YawAngle = 0.0;
    LpStrPDCSnsUseOdo->fCar_SinYawAngle = 0.0;
    LpStrPDCSnsUseOdo->fCar_CosYawAngle = 1.0;  
    LpStrPDCSnsUseOdo->fDrvDis = 0;
}


/******************************************************************************
 * 函数名称: PDCSnsCarMovStsInit
 * 
 * 功能描述: 车辆运动状态初始化
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-04-04 14:59   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PDCSnsCarMovStsInit(PDCSnsGroupType LenuSnsGroup)
{
    uint8 j;

    for(j = PDC_SNS_CH_FLS_RLS; j < PDC_SNS_CH_NUM; j++)
    {
        GstrSnsCarMovSts[LenuSnsGroup][j].fCar_X_Coor = 0;
        GstrSnsCarMovSts[LenuSnsGroup][j].fCar_Y_Coor = 0;
        GstrSnsCarMovSts[LenuSnsGroup][j].u16CarStopCnt = 0;
        GstrSnsCarMovSts[LenuSnsGroup][j].eCarDir = SNS_CAR_STOP;
        GstrSnsCarMovSts[LenuSnsGroup][j].eCarDirBackup = SNS_CAR_STOP;
        GstrSnsCarMovSts[LenuSnsGroup][j].u8CarFowardToBackwardFlag = 0;
        GstrSnsCarMovSts[LenuSnsGroup][j].u8CarBackwardToFowardFlag = 0;  
        GstrSnsCarMovSts[LenuSnsGroup][j].fSnsCarMovDisSub = 0;
    }    
}



/******************************************************************************
 * 函数名称: PDCSnsCarMovSts_PDC_Sns_Init
 * 
 * 功能描述: 清除PDC 探头的运动状态数据
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-07 10:48   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PDCSnsCarMovSts_PDC_Sns_Init(PDCSnsGroupType LenuSnsGroup)
{
    uint8 j;

    for(j = PDC_SNS_CH_FL_RL; j < PDC_SNS_CH_FRS_RRS; j++)
    {
        GstrSnsCarMovSts[LenuSnsGroup][j].fCar_X_Coor = 0;
        GstrSnsCarMovSts[LenuSnsGroup][j].fCar_Y_Coor = 0;
        GstrSnsCarMovSts[LenuSnsGroup][j].u16CarStopCnt = 0;
        GstrSnsCarMovSts[LenuSnsGroup][j].eCarDir = SNS_CAR_STOP;
        GstrSnsCarMovSts[LenuSnsGroup][j].eCarDirBackup = SNS_CAR_STOP;
        GstrSnsCarMovSts[LenuSnsGroup][j].u8CarFowardToBackwardFlag = 0;
        GstrSnsCarMovSts[LenuSnsGroup][j].u8CarBackwardToFowardFlag = 0;  
        GstrSnsCarMovSts[LenuSnsGroup][j].fSnsCarMovDisSub = 0;
    }    
}


/******************************************************************************
 * 函数名称: PDCSnsRawDataInit
 * 
 * 功能描述: 初始化探头Raw Data数据
 * 
 * 输入参数:LenuSnsGroup--雷达组别
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-12-16 11:28   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PDCSnsRawDataInit(PDCSnsGroupType LenuSnsGroup)
{
    PDCSnsChannelType LenuSnsCh;
    
    PDCSnsRawDataType *LpStrPDCSnsRawData;
    LpStrPDCSnsRawData = &GstrPDCSnsRawData[LenuSnsGroup];
    for(LenuSnsCh = PDC_SNS_CH_FLS_RLS; LenuSnsCh < PDC_SNS_CH_NUM; LenuSnsCh++)
    {
        LpStrPDCSnsRawData->enuPDCSnsWorkSts[LenuSnsCh] = PDC_SNS_STS_NORMAL;
        PDCSnsRawDataClear(LenuSnsGroup,LenuSnsCh);
        PDCSnsClearUpdateFlag(LenuSnsGroup,LenuSnsCh);
    }
}


/******************************************************************************
 * 函数名称: PDCSnsRawDataPowerOnInit
 * 
 * 功能描述: 上电初始化探头Raw Data数据
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-12-16 13:47   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PDCSnsRawDataPowerOnInit(void)
{
    PDCSnsRawDataInit(PDC_SNS_GROUP_FRONT);
    PDCSnsRawDataInit(PDC_SNS_GROUP_REAR);
    PDCSnsUseOdoDataInit();
    PDCSnsCarMovStsInit(PDC_SNS_GROUP_FRONT);
    PDCSnsCarMovStsInit(PDC_SNS_GROUP_REAR);
    CopySnsMapCaliThresToRam();
    CopySnsPSLCalibWidthAndThresholdToRam();
#if (CALIBRATION_EN == STD_ON)
    /* 在DID_Calibration初始化 */
#else
    CopySnsCalibWidthAndThresholdToRam();
#endif
}


/******************************************************************************
 * 函数名称: JudgeCarMovSts
 * 
 * 功能描述: 判断车辆运动状态
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-04-04 15:02   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void JudgeCarMovSts(PDCSnsGroupType LenuSnsGroup,PDCSnsChannelType LenuSnsCh)
{
    Car_GearType LenuCar_Gear;
    Car_WheelPulseType LenuRL_Wheel;
    Car_WheelPulseType LenuRR_Wheel;
    float LfCarDisTemp;
    
    ReadCAN_AppSignal_Car_Gear(&LenuCar_Gear);
    ReadCAN_AppSignal_Car_WheelPulseAndDir(&LenuRL_Wheel,CAR_RL_WHEEL);
    ReadCAN_AppSignal_Car_WheelPulseAndDir(&LenuRR_Wheel,CAR_RR_WHEEL);

    LfCarDisTemp = PubAI_CalTwoPointDis(GstrPDCSnsUseOdo.fCar_X_Coor,GstrPDCSnsUseOdo.fCar_Y_Coor,\
    GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh].fCar_X_Coor,GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh].fCar_Y_Coor);
    
    if((LenuRL_Wheel.enuCar_WheelPulseDir == CAR_WHEEL_DIR_FORWARD)||(LenuRR_Wheel.enuCar_WheelPulseDir == CAR_WHEEL_DIR_FORWARD)\
        ||((LenuCar_Gear == CAN_CAR_GEAR_D)&&(LenuRL_Wheel.enuCar_WheelPulseDir != CAR_WHEEL_DIR_BACKWARD)&&(LenuRR_Wheel.enuCar_WheelPulseDir != CAR_WHEEL_DIR_BACKWARD)))
    {
        GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh].eCarDir = SNS_CAR_FORWARD;
        GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh].fSnsCarMovDisSub = LfCarDisTemp;
    }
    else if((LenuRL_Wheel.enuCar_WheelPulseDir == CAR_WHEEL_DIR_BACKWARD)||(LenuRR_Wheel.enuCar_WheelPulseDir == CAR_WHEEL_DIR_BACKWARD)\
        ||((LenuCar_Gear == CAN_CAR_GEAR_R)&&(LenuRL_Wheel.enuCar_WheelPulseDir != CAR_WHEEL_DIR_FORWARD)&&(LenuRR_Wheel.enuCar_WheelPulseDir != CAR_WHEEL_DIR_FORWARD)))
    {
        GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh].eCarDir = SNS_CAR_BACKWARD;
        GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh].fSnsCarMovDisSub = -LfCarDisTemp;
    }
    else
    {
        GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh].eCarDir = SNS_CAR_STOP;
        GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh].fSnsCarMovDisSub = 0;
    } 

    if(LfCarDisTemp < 5)
    {
        if(GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh].u16CarStopCnt < 5)/* 160ms 进入1次， 5*160 = 800 ms */
        {
            GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh].u16CarStopCnt++;
        }
        else
        {
            GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh].eCarDir = SNS_CAR_STOP;
        }
    }
    else
    {
        GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh].u16CarStopCnt = 0;
    }

    if(GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh].eCarDirBackup != GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh].eCarDir)
    {
        if(GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh].eCarDir == SNS_CAR_FORWARD)
        {
            GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh].u8CarBackwardToFowardFlag = 1;
        }
        
        if(GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh].eCarDir == SNS_CAR_BACKWARD)
        {
            GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh].u8CarFowardToBackwardFlag = 1;
        }
    }
    GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh].fCar_X_Coor = GstrPDCSnsUseOdo.fCar_X_Coor;
    GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh].fCar_Y_Coor = GstrPDCSnsUseOdo.fCar_Y_Coor;
    GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh].eCarDirBackup = GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh].eCarDir;
#if 0
    if((LenuSnsGroup == 0x00)&&(LenuSnsCh == 0x00))
    {
        printf("Time:%.3f,FLS Sns Move,DisSub:%.3f,Dr:%d\r\n",GfMessageTime,\
            GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh].fSnsCarMovDisSub,GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh].eCarDir);
    }
#endif
}




/******************************************************************************
 * 函数名称: UpdateSnsOdoCoor
 * 
 * 功能描述: 更新Odo坐标
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-03-10 15:49   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void UpdateSnsOdoCoor(void)
{
    PDCSnsUseOdoType *LpStrPDCSnsUseOdo;
    uint16 Lu16CarSpd;
    CPOS_CurCarPointDataType LstrCurCarPointData;
    
    LpStrPDCSnsUseOdo = &GstrPDCSnsUseOdo;
    ReadCAN_AppSignal_Car_Speed(&Lu16CarSpd);
    if(Lu16CarSpd < SNS_ODO_ACTIVE_SPD)
    {
        if(LpStrPDCSnsUseOdo->u8SnsRawDataUseID == 0xFF)
        {
            LpStrPDCSnsUseOdo->u8SnsRawDataUseID = CPOSSgnMag_ReadGetCalId();
            if(LpStrPDCSnsUseOdo->u8SnsRawDataUseID != 0xFF)
            {
                SNS_RAW_DATA_PRINT("SnsRawData Get ID Success,ID:%d,Car_Spd:%d\r\n",LpStrPDCSnsUseOdo->u8SnsRawDataUseID,Lu16CarSpd);
            }
        }
        else
        {
            CPOSSgnMag_ReadCarCoorData(&LstrCurCarPointData,LpStrPDCSnsUseOdo->u8SnsRawDataUseID);
            LpStrPDCSnsUseOdo->fCar_X_Coor = LstrCurCarPointData.fX;
            LpStrPDCSnsUseOdo->fCar_Y_Coor = LstrCurCarPointData.fY;
            LpStrPDCSnsUseOdo->fCar_YawAngle = LstrCurCarPointData.fAngle;
            LpStrPDCSnsUseOdo->fCar_SinYawAngle = sinf(LpStrPDCSnsUseOdo->fCar_YawAngle);
            LpStrPDCSnsUseOdo->fCar_CosYawAngle = cosf(LpStrPDCSnsUseOdo->fCar_YawAngle);
            LpStrPDCSnsUseOdo->fDrvDis = LstrCurCarPointData.fDrvDis;
        }
    }
    else if(Lu16CarSpd > (SNS_ODO_ACTIVE_SPD+100))
    {
        if(LpStrPDCSnsUseOdo->u8SnsRawDataUseID != 0xFF)
        {
            CPOSSgnMag_WriteClrCalId(LpStrPDCSnsUseOdo->u8SnsRawDataUseID);
            SNS_RAW_DATA_PRINT("SnsRawData Use Clear ID,ID:%d,Car_Spd:%d\r\n",LpStrPDCSnsUseOdo->u8SnsRawDataUseID,Lu16CarSpd);
            PDCSnsUseOdoDataInit();
        }
    }
}


/******************************************************************************
 * 函数名称: PDCSnsOdoSendToCAN
 * 
 * 功能描述: PDC 原始数据模块周期性发送Odo数据至CAN总线--20ms发送一次
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-12-23 17:25   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PDCSnsOdoSendToCAN(void)
{
#if (SNS_DEBUG_DATA_SEND_SWITCH == STD_ON)
    (*CanTxDebugMsgHandle[0])();
#endif
}

/******************************************************************************
 * 函数名称: PDCSnsGetOdoInf
 * 
 * 功能描述: 
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-01-02 09:31   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PDCSnsGetOdoInf(float LfX_Coor, float LfY_Coor, float LfYawAngle,float LfDrvDis)
{
    PDCSnsUseOdoType *LpStrPDCSnsUseOdo;
#if 0
    static ObjCoorType LstrOdoCoorBackup;
    float LfOdoMoveDis;
#endif
    
    LpStrPDCSnsUseOdo = &GstrPDCSnsUseOdo;

    LpStrPDCSnsUseOdo->u8SnsRawDataUseID = 0;
    LpStrPDCSnsUseOdo->fCar_X_Coor = LfX_Coor;
    LpStrPDCSnsUseOdo->fCar_Y_Coor = LfY_Coor;
    LpStrPDCSnsUseOdo->fCar_YawAngle = LfYawAngle;
    LpStrPDCSnsUseOdo->fCar_SinYawAngle = sinf(LpStrPDCSnsUseOdo->fCar_YawAngle);
    LpStrPDCSnsUseOdo->fCar_CosYawAngle = cosf(LpStrPDCSnsUseOdo->fCar_YawAngle);
    LpStrPDCSnsUseOdo->fDrvDis = LfDrvDis;

#if 0
    LfOdoMoveDis = PubAI_CalTwoPointDis(LpStrPDCSnsUseOdo->fCar_X_Coor,LpStrPDCSnsUseOdo->fCar_Y_Coor,LstrOdoCoorBackup.fObjX,LstrOdoCoorBackup.fObjY);
    LpStrPDCSnsUseOdo->fDrvDis += LfOdoMoveDis;
    LstrOdoCoorBackup.fObjX = LpStrPDCSnsUseOdo->fCar_X_Coor;
    LstrOdoCoorBackup.fObjY = LpStrPDCSnsUseOdo->fCar_Y_Coor;
#endif
#if 0
    printf("Get VS Inf,Time:%.3f,X_Coor:%.2f,Y_Coor:%.2f,YawAngle:%.2f,DriverDis:%.3f\r\n",GfMessageTime,\
        LpStrPDCSnsUseOdo->fCar_X_Coor,LpStrPDCSnsUseOdo->fCar_Y_Coor,\
        LpStrPDCSnsUseOdo->fCar_YawAngle,LpStrPDCSnsUseOdo->fDrvDis);
#endif

}

/******************************************************************************
 * 函数名称: KalmanFilter
 * 
 * 功能描述: 卡尔曼滤波
 * 
 * 输入参数:LpstrKalmanUpdateData--卡尔曼更新后的结果指针；LpstrKalmanPredictData--卡尔曼预测值；LfMeasureDisInput--当前周期的测量值
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-07-31 19:48   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void KalmanFilter(KalmanUpdateDataType *LpstrKalmanUpdateData,KalmanPredictDataType LpstrLastPredictData,uint16 Lu16MeasureDisInput)
{
    float LfMeasureDis;
    float LfCurrentEstimateDis;
    float LfCurrentEstimate_P;
    float LfCurrentK;
    float LfBestEstimateDis;
    float LfCurrent_P;
    KalmanUpdateDataType LstrKalmanDataUpdate;

    if(Lu16MeasureDisInput == SNS_INVALID_DIS)
    {
        LfMeasureDis = LpstrLastPredictData.fBestEstimateDis;/* 无效测量值使用上一周期的最佳估计值替代 */
    }
    else
    {
        LfMeasureDis = (float)Lu16MeasureDisInput;
    }
    
    LfCurrentEstimateDis = LpstrLastPredictData.fBestEstimateDis;
    LfCurrentEstimate_P = LpstrLastPredictData.fEstimateP;

    /* 计算当前周期的卡尔曼增益 */
    LfCurrentK = LfCurrentEstimate_P/(LfCurrentEstimate_P+KALMAN_R);

    /* 更新最佳估计距离 */
    LfBestEstimateDis = LfCurrentEstimateDis + LfCurrentK*(LfMeasureDis - LfCurrentEstimateDis);

    /* 更新协方差矩阵 */
    LfCurrent_P = (1.0 - LfCurrentK)*LfCurrentEstimate_P;

    LstrKalmanDataUpdate.fBestEstimateDis = LfBestEstimateDis;
    LstrKalmanDataUpdate.fCurrentK = LfCurrentK;
    LstrKalmanDataUpdate.fCurrentP = LfCurrent_P;
#if 0
    printf("MeasureDis:%.3f,BestEstimateDis:%.3f,CurrentK:%.3f,CurrentP:%.3f\r\n",LfMeasureDis,LstrKalmanDataUpdate.fBestEstimateDis,\
        LstrKalmanDataUpdate.fCurrentK,LstrKalmanDataUpdate.fCurrentP);
#endif
    
    *LpstrKalmanUpdateData = LstrKalmanDataUpdate;
}

/******************************************************************************
 * 函数名称: PDCSnsRawDataUpdate
 * 
 * 功能描述: 更新探头原始数据
 * 
 * 输入参数: 探头原始数据
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-12-16 14:09   V0.1      AntonyFang   初次发布
 ******************************************************************************/
#if 0
void SDW_GetOriginalData(PDCSnsGroupType LeGroup,PDCSnsChannelType LenuSnsCh);
void SDWAlgorithm_Task(void);
void PDCSnsRawDataUpdate(PDCSnsGroupType LenuSnsGroup,PDCSnsChannelType LenuSnsCh, uint8 *Lu8DataPtr)
{
    PDCSnsEchoNumType LenuPDCSnsEchoNum;
    PDCSnsRawDataType *LpStrPDCSnsRawData;
    LpStrPDCSnsRawData = &GstrPDCSnsRawData[LenuSnsGroup];

    ReadCAN_AppSignal_Car_Speed(&Gu16CarSpdForSnsUse);
    

	/* 清除探头通道所有数据 */
	PDCSnsRawDataClear(LenuSnsGroup, LenuSnsCh);
    
    LpStrPDCSnsRawData->u8UpdateFlg[LenuSnsCh] = Lu8DataPtr[0]&0x01;
	LpStrPDCSnsRawData->enuPDCSnsMeasType[LenuSnsCh] = (Lu8DataPtr[0]>>3)&0x07;
			

	LpStrPDCSnsRawData->enuPDCSnsWorkSts[LenuSnsCh] = (Lu8DataPtr[0]>>1)&0x03;
    LpStrPDCSnsRawData->u16RingTime[LenuSnsCh] = (Lu8DataPtr[1]<<8) + (Lu8DataPtr[2]);

    LpStrPDCSnsRawData->u8MasterEchoCnt[LenuSnsCh] = ((Lu8DataPtr[51] >> 0) & 0x07);
	if(LpStrPDCSnsRawData->u8MasterEchoCnt[LenuSnsCh] > SNS_MAX_ECHO_CNT)
    {
        LpStrPDCSnsRawData->u8MasterEchoCnt[LenuSnsCh] = SNS_MAX_ECHO_CNT;
    }
    
    LpStrPDCSnsRawData->u16MasterDis[LenuSnsCh][0] = (Lu8DataPtr[3]<<8) + (Lu8DataPtr[4]);
    LpStrPDCSnsRawData->u16MasterDis[LenuSnsCh][1] = (Lu8DataPtr[5]<<8) + (Lu8DataPtr[6]);
    LpStrPDCSnsRawData->u16MasterDis[LenuSnsCh][2] = (Lu8DataPtr[7]<<8) + (Lu8DataPtr[8]);
    LpStrPDCSnsRawData->u16MasterDis[LenuSnsCh][3] = (Lu8DataPtr[9]<<8) + (Lu8DataPtr[10]);

    LpStrPDCSnsRawData->u16MasterHeight[LenuSnsCh][0] = (Lu8DataPtr[11]<<8) + (Lu8DataPtr[12]);
    LpStrPDCSnsRawData->u16MasterHeight[LenuSnsCh][1] = (Lu8DataPtr[13]<<8) + (Lu8DataPtr[14]);
    LpStrPDCSnsRawData->u16MasterHeight[LenuSnsCh][2] = (Lu8DataPtr[15]<<8) + (Lu8DataPtr[16]);
    LpStrPDCSnsRawData->u16MasterHeight[LenuSnsCh][3] = (Lu8DataPtr[17]<<8) + (Lu8DataPtr[18]);


    LpStrPDCSnsRawData->u8LeftListenEchoCnt[LenuSnsCh] = ((Lu8DataPtr[51] >> 3) & 0x07);
	if(LpStrPDCSnsRawData->u8LeftListenEchoCnt[LenuSnsCh] > SNS_MAX_ECHO_CNT)
	{
		LpStrPDCSnsRawData->u8LeftListenEchoCnt[LenuSnsCh] = SNS_MAX_ECHO_CNT;
	}

    LpStrPDCSnsRawData->u16LeftListenDis[LenuSnsCh][0] = (Lu8DataPtr[19]<<8) + (Lu8DataPtr[20]);
    LpStrPDCSnsRawData->u16LeftListenDis[LenuSnsCh][1] = (Lu8DataPtr[21]<<8) + (Lu8DataPtr[22]);
    LpStrPDCSnsRawData->u16LeftListenDis[LenuSnsCh][2] = (Lu8DataPtr[23]<<8) + (Lu8DataPtr[24]);
    LpStrPDCSnsRawData->u16LeftListenDis[LenuSnsCh][3] = (Lu8DataPtr[25]<<8) + (Lu8DataPtr[26]);

    LpStrPDCSnsRawData->u16LeftListenHeight[LenuSnsCh][0] = (Lu8DataPtr[27]<<8) + (Lu8DataPtr[28]);
    LpStrPDCSnsRawData->u16LeftListenHeight[LenuSnsCh][1] = (Lu8DataPtr[29]<<8) + (Lu8DataPtr[30]);
    LpStrPDCSnsRawData->u16LeftListenHeight[LenuSnsCh][2] = (Lu8DataPtr[31]<<8) + (Lu8DataPtr[32]);
    LpStrPDCSnsRawData->u16LeftListenHeight[LenuSnsCh][3] = (Lu8DataPtr[33]<<8) + (Lu8DataPtr[34]);

    LpStrPDCSnsRawData->u8RightListenEchoCnt[LenuSnsCh] = ((Lu8DataPtr[52] >> 0) & 0x07);
	if(LpStrPDCSnsRawData->u8RightListenEchoCnt[LenuSnsCh] > SNS_MAX_ECHO_CNT)
	{
		LpStrPDCSnsRawData->u8RightListenEchoCnt[LenuSnsCh] = SNS_MAX_ECHO_CNT;
	}
    LpStrPDCSnsRawData->u16RightListenDis[LenuSnsCh][0] = (Lu8DataPtr[35]<<8) + (Lu8DataPtr[36]);
    LpStrPDCSnsRawData->u16RightListenDis[LenuSnsCh][1] = (Lu8DataPtr[37]<<8) + (Lu8DataPtr[38]);
    LpStrPDCSnsRawData->u16RightListenDis[LenuSnsCh][2] = (Lu8DataPtr[39]<<8) + (Lu8DataPtr[40]);
    LpStrPDCSnsRawData->u16RightListenDis[LenuSnsCh][3] = (Lu8DataPtr[41]<<8) + (Lu8DataPtr[42]);

    LpStrPDCSnsRawData->u16RightListenHeight[LenuSnsCh][0] = (Lu8DataPtr[43]<<8) + (Lu8DataPtr[44]);
    LpStrPDCSnsRawData->u16RightListenHeight[LenuSnsCh][1] = (Lu8DataPtr[45]<<8) + (Lu8DataPtr[46]);
    LpStrPDCSnsRawData->u16RightListenHeight[LenuSnsCh][2] = (Lu8DataPtr[47]<<8) + (Lu8DataPtr[48]);
    LpStrPDCSnsRawData->u16RightListenHeight[LenuSnsCh][3] = (Lu8DataPtr[49]<<8) + (Lu8DataPtr[50]);

    LpStrPDCSnsRawData->u8NFD_Flag[LenuSnsCh] = ((Lu8DataPtr[52] >> 3) & 0x01);
    if(LpStrPDCSnsRawData->u8NFD_Flag[LenuSnsCh])
    {
        LpStrPDCSnsRawData->u16NFD_Dis[LenuSnsCh] = (Lu8DataPtr[53]<<8) + (Lu8DataPtr[54]);
    }
    else
    {
        LpStrPDCSnsRawData->u16NFD_Dis[LenuSnsCh] = SNS_INVALID_DIS;
    }
    LpStrPDCSnsRawData->u8NoiseAbnormalCnt[LenuSnsCh] = Lu8DataPtr[57];

    JudgeCarMovSts(LenuSnsGroup,LenuSnsCh);
    SnsEchoFilterAndSigGroupDataGet(LenuSnsGroup,LenuSnsCh);
    MapEchoFilterAndSigGroupDataGet(LenuSnsGroup,LenuSnsCh);
#if 1
    SnsSigGroupDisFollowAndOutputHandle(LenuSnsGroup,LenuSnsCh);
    ObjSnsCoorCalAndFollow(LenuSnsGroup,LenuSnsCh);
    ObjMapBuild(LenuSnsGroup);
    if((LenuSnsCh == 0x00)||(LenuSnsCh == 0x05))
    {
        SDW_GetOriginalData(LenuSnsGroup,LenuSnsCh);
    }
    SDWAlgorithm_Task();
#endif

#if 0
    /* 测试PP 分区距离值和障碍物坐标的关系 */
    float LfMapPointX,LfMapPointY;
    float LfLineStartX,LfLineStartY;
    float LfLineEndX,LfLineEndY;
    float LfPointToLineDis;

#if 0
    LfMapPointX = 4040;
    LfMapPointY = 1480;
#endif

    LfMapPointX = 4520;
    LfMapPointY = 960;

#if 0
    /* FL1分区 */
    LfLineStartX = 3440;
    LfLineStartY = 860;
    LfLineEndX = 3645;
    LfLineEndY = 700;
#endif

#if 1
    /* FL2分区 */
    LfLineStartX = 3645;
    LfLineStartY = 700;
    LfLineEndX = 3775;
    LfLineEndY = 520;
#endif

#if 0
        /* FML1分区 */
        LfLineStartX = 3775;
        LfLineStartY = 520;
        LfLineEndX = 3835;
        LfLineEndY = 300;
#endif
    LfPointToLineDis = PubAI_CalOnePointToSegmentDis(LfMapPointX,LfMapPointY,LfLineStartX,LfLineStartY,LfLineEndX,LfLineEndY);
    printf("PointToLineDis:%.3f,MapPointX:%.3f,MapPointY:%.3f,LineStartX:%.3f,LineStartY:%.3f,LineEndX:%.3f,LineEndY:%.3f\r\n",LfPointToLineDis,LfMapPointX,LfMapPointY,\
        LfLineStartX,LfLineStartY,LfLineEndX,LfLineEndY);
#endif

}

#else
uint8  Gu8CompleteFirsRoundDataFlg=0;//=1表示完成首次数据采集
void PDCSnsRawDataUpdate(void)
{
    PDCSnsEchoNumType LenuPDCSnsEchoNum;
    PDCSnsRawDataType *LpStrPDCSnsRawData;
	PDCSnsGroupType LenuSnsGroup;
	PDCSnsChannelType LenuSnsCh;
	const SnsMeasDistData_Str *LpStrSnsMeasDistData = Elmos17SnsCtrl_GetSnsMeasDistData();
	SnsID_en LenuSNSIDIdx = SNSID_NONE;
	SnsID_en LenuLeLisSNSID = SNSID_NONE;
	SnsID_en LenuRigLisSNSID = SNSID_NONE;
	SnsErrInfo_t SnsErrInfo = {FALSE,SnsFaultNone};
    const NFDOutput_t *LpstrNFDOutput;
	SNS_MEAS_TYPE_en LenuMasterMeasType = SNS_MEAS_TYPE_NONE;
	DistanceType_en LenuLisDisType = DistanceType_NULL;
	PDCSnsEchoNumType Lu8WrBufInx = PDC_SNS_ECHO_FIRST;
	uint8 Lu8EchoIdx = 0;
	
	for(LenuSNSIDIdx = SNSID_Start; LenuSNSIDIdx < SNSNum; LenuSNSIDIdx++)
	{
		LenuSnsGroup = SnsLocCfg[LenuSNSIDIdx].enuPDCSnsGroup;
		LenuSnsCh = SnsLocCfg[LenuSNSIDIdx].enuSnsChannel;
	    LpStrPDCSnsRawData = &GstrPDCSnsRawData[LenuSnsGroup];
		
		if(LpStrSnsMeasDistData[LenuSNSIDIdx].SnsDistMsg.SNS_MEAS_TYPE < MEAS_STD_LISTEN)
		{
			LenuLeLisSNSID = SNSID_NONE;
			LenuRigLisSNSID = SNSID_NONE;

			if(PDC_SNS_GROUP_FRONT == LenuSnsGroup)
			{
				if(SNS_FL_S == LenuSNSIDIdx)
				{
					LenuLeLisSNSID = SNSID_NONE;
					LenuRigLisSNSID = LenuSNSIDIdx + 1;
				}
				else if(SNS_FR_S == LenuSNSIDIdx) 
				{
					LenuLeLisSNSID = LenuSNSIDIdx - 1;
					LenuRigLisSNSID = SNSID_NONE;
				}
				else
				{
					LenuLeLisSNSID = LenuSNSIDIdx - 1;
					LenuRigLisSNSID = LenuSNSIDIdx + 1;
				}
			}
			else if(PDC_SNS_GROUP_REAR == LenuSnsGroup)
			{
				if(SNS_RR_S == LenuSNSIDIdx)
				{
					LenuLeLisSNSID = LenuSNSIDIdx + 1;
					LenuRigLisSNSID = SNSID_NONE;
				}
				else if(SNS_RL_S == LenuSNSIDIdx)
				{
					LenuLeLisSNSID = SNSID_NONE;
					LenuRigLisSNSID = LenuSNSIDIdx - 1;
				}
				else
				{
					LenuLeLisSNSID = LenuSNSIDIdx + 1;
					LenuRigLisSNSID = LenuSNSIDIdx - 1;
				}
			}
			else
			{
			}

			/* 清除探头通道所有数据 */
			PDCSnsRawDataClear(LenuSnsGroup, LenuSnsCh);
		    LpStrPDCSnsRawData->u8UpdateFlg[LenuSnsCh] = 1;
			LpStrPDCSnsRawData->enuPDCSnsMeasType[LenuSnsCh] = LpStrSnsMeasDistData[LenuSNSIDIdx].SnsDistMsg.SNS_MEAS_TYPE;
			LenuMasterMeasType = LpStrSnsMeasDistData[LenuSNSIDIdx].SnsDistMsg.SNS_MEAS_TYPE;
			SnsDiag_ReadSnsErrSignal(&SnsErrInfo,LenuSNSIDIdx);
				
			if(FALSE == SnsErrInfo.eErrFlag)
			{
                LpstrNFDOutput = EchoDet_NFDResult(LenuSNSIDIdx);

#if 0
                if(LpstrNFDOutput->NFDFlg)
                {
                    LpStrPDCSnsRawData->u8NFD_Flag[LenuSnsCh] = 1;
                    LpStrPDCSnsRawData->u16NFD_Dis[LenuSnsCh] = LpstrNFDOutput->NFDDis;
                }
                else
                {
                    LpStrPDCSnsRawData->u8NFD_Flag[LenuSnsCh] = 0;
                    LpStrPDCSnsRawData->u16NFD_Dis[LenuSnsCh] = SNS_INVALID_DIS;
                }
#endif
				LpStrPDCSnsRawData->u32DataSynTime[LenuSnsCh] = LpStrSnsMeasDistData[LenuSNSIDIdx].ADASSYNTime;
				LpStrPDCSnsRawData->enuPDCSnsWorkSts[LenuSnsCh] = PDC_SNS_STS_NORMAL;
			    LpStrPDCSnsRawData->u16RingTime[LenuSnsCh] = LpStrSnsMeasDistData[LenuSNSIDIdx].SnsDiagData.RTM;
				LpStrPDCSnsRawData->u8NoiseAbnormalCnt[LenuSnsCh] = LpStrSnsMeasDistData[LenuSNSIDIdx].SnsDiagData.NoiseAbnormalCnt;
					
				if(LpStrPDCSnsRawData->u16RingTime[LenuSnsCh] > SNS_MAX_RING_TIME)
			    {
			        LpStrPDCSnsRawData->u16RingTime[LenuSnsCh] = SNS_MAX_RING_TIME;
			    }

			    LpStrPDCSnsRawData->u8MasterEchoCnt[LenuSnsCh] = LpStrSnsMeasDistData[LenuSNSIDIdx].SnsDistMsg.SaveDisDataCnt;
				if(LpStrPDCSnsRawData->u8MasterEchoCnt[LenuSnsCh] > SNS_MAX_ECHO_CNT)
			    {
			        LpStrPDCSnsRawData->u8MasterEchoCnt[LenuSnsCh] = SNS_MAX_ECHO_CNT;
			    }
				
			    /* 遍历查询主发通道数据 */
			    for(LenuPDCSnsEchoNum = PDC_SNS_ECHO_FIRST; LenuPDCSnsEchoNum < LpStrPDCSnsRawData->u8MasterEchoCnt[LenuSnsCh]; LenuPDCSnsEchoNum++)
			    {
			        LpStrPDCSnsRawData->u16MasterDis[LenuSnsCh][LenuPDCSnsEchoNum] = LpStrSnsMeasDistData[LenuSNSIDIdx].SnsDistMsg.Dis[LenuPDCSnsEchoNum] >> 1;/*dis = v*t/2 */
			        LpStrPDCSnsRawData->u16MasterHeight[LenuSnsCh][LenuPDCSnsEchoNum] = LpStrSnsMeasDistData[LenuSNSIDIdx].SnsDistMsg.Amp[LenuPDCSnsEchoNum];
					if(STANDARD_MEAS == LpStrSnsMeasDistData[LenuSNSIDIdx].MeasType)
					{
						LpStrPDCSnsRawData->u16MasterWidth[LenuSnsCh][LenuPDCSnsEchoNum] = LpStrSnsMeasDistData[LenuSNSIDIdx].SnsDistMsg.WidthOrConf[LenuPDCSnsEchoNum];
						LpStrPDCSnsRawData->u8MasterConfidence[LenuSnsCh][LenuPDCSnsEchoNum] = SNS_INVALID_CONFIDENCE; 
					}
					else if(ADVANCED_MEAS == LpStrSnsMeasDistData[LenuSNSIDIdx].MeasType)
					{
			        	LpStrPDCSnsRawData->u8MasterConfidence[LenuSnsCh][LenuPDCSnsEchoNum] = LpStrSnsMeasDistData[LenuSNSIDIdx].SnsDistMsg.WidthOrConf[LenuPDCSnsEchoNum];	
						LpStrPDCSnsRawData->u16MasterWidth[LenuSnsCh][LenuPDCSnsEchoNum] = SNS_INVALID_WIDTH;
					}
					else
					{
						LpStrPDCSnsRawData->u16MasterWidth[LenuSnsCh][LenuPDCSnsEchoNum] = SNS_INVALID_WIDTH;
						LpStrPDCSnsRawData->u8MasterConfidence[LenuSnsCh][LenuPDCSnsEchoNum] = SNS_INVALID_CONFIDENCE; 
					}
			    }

				if(LenuLeLisSNSID < SNSNum)
				{
				    LpStrPDCSnsRawData->u8LeftListenEchoCnt[LenuSnsCh] = 0;	
					Lu8WrBufInx = PDC_SNS_ECHO_FIRST;
				    /* 遍历查询左侦听通道数据 */
				    for(Lu8EchoIdx = 0; Lu8EchoIdx < LpStrSnsMeasDistData[LenuLeLisSNSID].SnsDistMsg.SaveDisDataCnt; Lu8EchoIdx++)
				    {
						LenuLisDisType = LpStrSnsMeasDistData[LenuLeLisSNSID].SnsDistMsg.DisType[Lu8EchoIdx];
						
						if(((MEAS_ADV_EMIT_ChirpDown == LenuMasterMeasType) && (ADV_Lis_A == LenuLisDisType)) 
							|| ((MEAS_ADV_EMIT_ChirpUp == LenuMasterMeasType) && (ADV_Lis_B == LenuLisDisType))
							|| (MEAS_STD_EMIT == LenuMasterMeasType))
						{
					        LpStrPDCSnsRawData->u16LeftListenDis[LenuSnsCh][Lu8WrBufInx] = LpStrSnsMeasDistData[LenuLeLisSNSID].SnsDistMsg.Dis[Lu8EchoIdx];
					        LpStrPDCSnsRawData->u16LeftListenHeight[LenuSnsCh][Lu8WrBufInx] = LpStrSnsMeasDistData[LenuLeLisSNSID].SnsDistMsg.Amp[Lu8EchoIdx]; 
							if(STANDARD_MEAS == LpStrSnsMeasDistData[LenuLeLisSNSID].MeasType)
							{
								LpStrPDCSnsRawData->u16LeftListenWidth[LenuSnsCh][Lu8WrBufInx] = LpStrSnsMeasDistData[LenuLeLisSNSID].SnsDistMsg.WidthOrConf[Lu8EchoIdx];
								LpStrPDCSnsRawData->u8LeftListenConfidence[LenuSnsCh][Lu8WrBufInx] = SNS_INVALID_CONFIDENCE; 
							}
							else if(ADVANCED_MEAS == LpStrSnsMeasDistData[LenuLeLisSNSID].MeasType)
							{
					        	LpStrPDCSnsRawData->u16LeftListenWidth[LenuSnsCh][Lu8WrBufInx] = SNS_INVALID_WIDTH;	
								LpStrPDCSnsRawData->u8LeftListenConfidence[LenuSnsCh][Lu8WrBufInx] = LpStrSnsMeasDistData[LenuLeLisSNSID].SnsDistMsg.WidthOrConf[Lu8EchoIdx];
							}
							else
							{
								LpStrPDCSnsRawData->u16LeftListenWidth[LenuSnsCh][Lu8WrBufInx] = SNS_INVALID_WIDTH;
								LpStrPDCSnsRawData->u8LeftListenConfidence[LenuSnsCh][Lu8WrBufInx] = SNS_INVALID_CONFIDENCE; 
							}
							Lu8WrBufInx++;

							if(Lu8WrBufInx >= SNS_MAX_ECHO_CNT)
							{
								break;
							}
						}
				    }
					LpStrPDCSnsRawData->u8LeftListenEchoCnt[LenuSnsCh] = Lu8WrBufInx;
				}
		
				if(LenuRigLisSNSID < SNSNum)
				{
				    LpStrPDCSnsRawData->u8RightListenEchoCnt[LenuSnsCh] = 0;
					Lu8WrBufInx = PDC_SNS_ECHO_FIRST;
				    /* 遍历查询右侦听通道数据 */
				    for(Lu8EchoIdx = 0; Lu8EchoIdx < LpStrSnsMeasDistData[LenuRigLisSNSID].SnsDistMsg.SaveDisDataCnt; Lu8EchoIdx++)
				    {
						LenuLisDisType = LpStrSnsMeasDistData[LenuRigLisSNSID].SnsDistMsg.DisType[Lu8EchoIdx];

						if(((MEAS_ADV_EMIT_ChirpDown == LenuMasterMeasType) && (ADV_Lis_A == LenuLisDisType)) 
							|| ((MEAS_ADV_EMIT_ChirpUp == LenuMasterMeasType) && (ADV_Lis_B == LenuLisDisType))
							|| (MEAS_STD_EMIT == LenuMasterMeasType))
						{
					        LpStrPDCSnsRawData->u16RightListenDis[LenuSnsCh][Lu8WrBufInx] = LpStrSnsMeasDistData[LenuRigLisSNSID].SnsDistMsg.Dis[Lu8EchoIdx];
					        LpStrPDCSnsRawData->u16RightListenHeight[LenuSnsCh][Lu8WrBufInx] = LpStrSnsMeasDistData[LenuRigLisSNSID].SnsDistMsg.Amp[Lu8EchoIdx];
							if(STANDARD_MEAS == LpStrSnsMeasDistData[LenuRigLisSNSID].MeasType)
							{
					        	LpStrPDCSnsRawData->u16RightListenWidth[LenuSnsCh][Lu8WrBufInx] = LpStrSnsMeasDistData[LenuRigLisSNSID].SnsDistMsg.WidthOrConf[Lu8EchoIdx];
					        	LpStrPDCSnsRawData->u8RightListenConfidence[LenuSnsCh][Lu8WrBufInx] = SNS_INVALID_CONFIDENCE; 
							}
							else if(ADVANCED_MEAS == LpStrSnsMeasDistData[LenuRigLisSNSID].MeasType)
							{
								LpStrPDCSnsRawData->u16RightListenWidth[LenuSnsCh][Lu8WrBufInx] = SNS_INVALID_WIDTH;
					        	LpStrPDCSnsRawData->u8RightListenConfidence[LenuSnsCh][Lu8WrBufInx] = LpStrSnsMeasDistData[LenuRigLisSNSID].SnsDistMsg.WidthOrConf[Lu8EchoIdx];
							}
							else
							{
								LpStrPDCSnsRawData->u16RightListenWidth[LenuSnsCh][Lu8WrBufInx] = SNS_INVALID_WIDTH;
					        	LpStrPDCSnsRawData->u8RightListenConfidence[LenuSnsCh][Lu8WrBufInx] = SNS_INVALID_CONFIDENCE; 
							}
							Lu8WrBufInx++;
							
							if(Lu8WrBufInx >= SNS_MAX_ECHO_CNT)
							{
								break;
							}
						}
				    }
					LpStrPDCSnsRawData->u8RightListenEchoCnt[LenuSnsCh] = Lu8WrBufInx;
				}
			}
			else
			{
				if(SnsBlindness == SnsErrInfo.eErrType)
				{
					LpStrPDCSnsRawData->enuPDCSnsWorkSts[LenuSnsCh] = PDC_SNS_STS_BLINDNESS;
				}
				else
				{
					LpStrPDCSnsRawData->enuPDCSnsWorkSts[LenuSnsCh] = PDC_SNS_STS_FAILED;
				}
			}
            /* 发送Debug报文至CAN  */
            #if (SNS_DEBUG_DATA_SEND_SWITCH == STD_ON)
            (*CanTxDebugMsgHandle[1+LenuSNSIDIdx])();

                #if 0
                R_PORT_SetGpioOutput(Port10, 2, Low);
                #endif
            #endif
		}
	}
    Gu8CompleteFirsRoundDataFlg=1;
}
#endif


