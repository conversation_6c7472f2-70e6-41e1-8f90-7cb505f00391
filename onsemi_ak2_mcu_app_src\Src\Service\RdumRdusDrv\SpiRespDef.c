/******************************************************************************
 * @file      SpiRespDef.c
 * @brief     SPI响应定义实现
 * <AUTHOR>
 * @date      2025-05-16
 * @note      
 *****************************************************************************/

/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include <string.h>
#include "SpiRespDef.h"



/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/



/******************************************************************************
 * @Const Declaration
 *****************************************************************************/



/******************************************************************************
 * @Variable Declaration
 *****************************************************************************/



/******************************************************************************
 * @Function Definitions
 *****************************************************************************/

/******************************************************************************
 * @brief      解析非读操作响应
 * @param[in]  RespData 响应数据
 * @param[out] NonReadResp 非读操作响应结构
 * <AUTHOR>
 * @date       2025-05-16
 * @note       
 *****************************************************************************/
void SpiRespDef_ParseNonReadResp(uint8 *RespData, SpiNonReadResp_t *NonReadResp)
{
    if (RespData == NULL || NonReadResp == NULL)
    {
        return;
    }
    
    /* 复制原始响应数据 */
    memcpy(NonReadResp->RespData, RespData, 7);
    
    /* 解析BRC_RECEIVED_CHA */
    NonReadResp->Bit.BRC_RECEIVED_CHA = RespData[0];
    
    /* 解析BRC_RECEIVED_CHB */
    NonReadResp->Bit.BRC_RECEIVED_CHB = RespData[1];
    
    /* 解析状态位 */
    NonReadResp->Bit.RETRAN_DONE = (RespData[2] >> 7) & 0x01;
    NonReadResp->Bit.SPIERR = (RespData[2] >> 6) & 0x01;
    NonReadResp->Bit.HW_ERR = (RespData[2] >> 5) & 0x01;
    NonReadResp->Bit.KAC_MASTER = (RespData[2] >> 3) & 0x03;
    NonReadResp->Bit.KAC_SLAVE = (RespData[2] >> 1) & 0x03;
    NonReadResp->Bit.Reserved = RespData[2] & 0x01;
    
    /* 解析CLKIN_CNT */
    NonReadResp->Bit.CLKIN_CNT = RespData[3];
    
    /* 解析SPI_RCVR_STATUS_CHA */
    NonReadResp->Bit.SPI_RCVR_STATUS_CHA.Value = RespData[4];
    
    /* 解析SPI_RCVR_STATUS_CHB */
    NonReadResp->Bit.SPI_RCVR_STATUS_CHB.Value = RespData[5];
    
    /* 解析CRC */
    NonReadResp->Bit.Crc = RespData[6];
}

/******************************************************************************
 * @brief      解析读操作响应
 * @param[in]  RespData 响应数据
 * @param[out] ReadResp 读操作响应结构
 * <AUTHOR>
 * @date       2025-05-16
 * @note       
 *****************************************************************************/
void SpiRespDef_ParseReadResp(uint8 *RespData, SpiReadResp_t *ReadResp)
{
    if (RespData == NULL || ReadResp == NULL)
    {
        return;
    }
    
    /* 复制原始响应数据 */
    memcpy(ReadResp->RespData, RespData, 7);
    
    /* 解析BRC_RECEIVED */
    ReadResp->Bit.BRC_RECEIVED = RespData[0];
    
    /* 解析状态位 */
    ReadResp->Bit.CRM_RCVD = (RespData[1] >> 7) & 0x01;
    ReadResp->Bit.TIMER_INT = (RespData[1] >> 6) & 0x01;
    ReadResp->Bit.SPIERR = (RespData[1] >> 5) & 0x01;
    ReadResp->Bit.HW_ERR = (RespData[1] >> 4) & 0x01;
    ReadResp->Bit.KAC_MASTER = (RespData[1] >> 2) & 0x03;
    ReadResp->Bit.KAC_SLAVE = RespData[1] & 0x03;
    
    /* 解析数据 */
    ReadResp->Bit.Data.Bytes[0] = RespData[2];
    ReadResp->Bit.Data.Bytes[1] = RespData[3];
    ReadResp->Bit.Data.Bytes[2] = RespData[4];
    ReadResp->Bit.Data.Bytes[3] = RespData[5];

    /* 解析CRC */
    ReadResp->Bit.Crc = RespData[6];
}
