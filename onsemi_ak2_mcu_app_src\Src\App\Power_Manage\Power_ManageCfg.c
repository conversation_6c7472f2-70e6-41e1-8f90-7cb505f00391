/******************************************************************************
 * Copyright (c) Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * All Rights Reserved.
 *
 * FileName : Sw_Timer.c:
 * Date	    : 2020-08-11 09:43
 * Author   : 22446
 ******************************************************************************/

/******************************************************************************
 * Includes
 *****************************************************************************/
#include "Power_ManageCfg.h"
#include "AdcHal.h"
#include "IOHal.h"

/**********************************************************************
*
*
* Global define
*
*
 **********************************************************************/
uint8 ReadChipSupplyPwrEnSts(void);
uint8 ReadSnsGroup1PwrEnSts(void);
uint8 ReadSnsGroup2PwrEnSts(void);
uint8 ReadSnsGroup3PwrEnSts(void);

/******************************************************************************/
/**
* @brief: 电源管理配置
**/
/******************************************************************************/
PowerMonitorCfg_t GstrPM_Config[PM_TOTAL] = 
{
	[PM_APP]       = {
						.u16Normal2LowVoltage = AD_CALIBRATION_8_8V,
						.u16Low2NormalVoltage = AD_CALIBRATION_9V,
						.u16Normal2HighVoltage = AD_CALIBRATION_16_5V,
						.u16High2NormalVoltage = AD_CALIBRATION_16V,
					 },
						
    [PM_CAN_TX_RX] = {
						.u16Normal2LowVoltage = AD_CALIBRATION_7_5V,
						.u16Low2NormalVoltage = AD_CALIBRATION_8V,
						.u16Normal2HighVoltage = AD_CALIBRATION_19_5V,
						.u16High2NormalVoltage = AD_CALIBRATION_19V,
					 },
					 	
    [PM_CAN_UDS]   = {
						.u16Normal2LowVoltage = AD_CALIBRATION_8_8V,
						.u16Low2NormalVoltage = AD_CALIBRATION_9_2V,
						.u16Normal2HighVoltage = AD_CALIBRATION_16_2V,
						.u16High2NormalVoltage = AD_CALIBRATION_15_8V,
					 },
};

PowerAdcCfg_t GstrPwrAdcCfg[PowerAdcIdxNum] = 
{
	[SupplyPwrAdcIdx] = {.AdcUint = ADCDETE_GROUP_ADC0_GROUP1, .AdcChannel = ADC0_GROUP1_SUPPLY_POWER},
	[FrontSnsGroupPwrAdcIdx]  = {.AdcUint = ADCDETE_GROUP_ADC0_GROUP1, .AdcChannel = ADC0_GROUP1_FRONT_SNS_POWER},
	[RearSnsGroupPwrAdcIdx]  = {.AdcUint = ADCDETE_GROUP_ADC0_GROUP1, .AdcChannel = ADC0_GROUP1_REAR_SNS_POWER},
	[SideSnsGroupPwrAdcIdx]  = {.AdcUint = ADCDETE_GROUP_ADC0_GROUP1, .AdcChannel = ADC0_GROUP1_SIDE_SNS_POWER}
};

SnsPowerDiagCtrl_t SnsPwrPinDiagCtrl[SnsGroupPwrDiagNum] = 
{
		[FrontSnsGroupPwrDiagIdx]  = {
									.PwrAdcValIdx = FrontSnsGroupPwrAdcIdx,
	        						.ShortToGndFilCtrl.UpperLimit = 20,
	        						.ShortToGndFilCtrl.LowerLimit = 0,
	        						.ShortToGndFilCtrl.UpStep = 4,
	        						.ShortToGndFilCtrl.DownStep = 1,
	        						.ShortToGndFilCtrl.Count = 0,
	        						.ShortToGndFilCtrl.Input = FALSE,
	        						.ShortToGndFilCtrl.Output = FALSE,
	        						.ShortToPwrFilCtrl.UpperLimit = 20,
	        						.ShortToPwrFilCtrl.LowerLimit = 0,
	        						.ShortToPwrFilCtrl.UpStep = 4,
	        						.ShortToPwrFilCtrl.DownStep = 1,
	        						.ShortToPwrFilCtrl.Count = 0,
	        						.ShortToPwrFilCtrl.Input = FALSE,
	        						.ShortToPwrFilCtrl.Output = FALSE,
	        						.ReadPwrEnPinSts = ReadSnsGroup1PwrEnSts,
								 },
		[RearSnsGroupPwrDiagIdx]  = {
									.PwrAdcValIdx = RearSnsGroupPwrAdcIdx,
	        						.ShortToGndFilCtrl.UpperLimit = 20,
	        						.ShortToGndFilCtrl.LowerLimit = 0,
	        						.ShortToGndFilCtrl.UpStep = 4,
	        						.ShortToGndFilCtrl.DownStep = 1,
	        						.ShortToGndFilCtrl.Count = 0,
	        						.ShortToGndFilCtrl.Input = FALSE,
	        						.ShortToGndFilCtrl.Output = FALSE,
	        						.ShortToPwrFilCtrl.UpperLimit = 20,
	        						.ShortToPwrFilCtrl.LowerLimit = 0,
	        						.ShortToPwrFilCtrl.UpStep = 4,
	        						.ShortToPwrFilCtrl.DownStep = 1,
	        						.ShortToPwrFilCtrl.Count = 0,
	        						.ShortToPwrFilCtrl.Input = FALSE,
	        						.ShortToPwrFilCtrl.Output = FALSE,	        						
	        						.ReadPwrEnPinSts = ReadSnsGroup2PwrEnSts,
								 },
		[SideSnsGroupPwrDiagIdx]  = {
									.PwrAdcValIdx = SideSnsGroupPwrAdcIdx,
	        						.ShortToGndFilCtrl.UpperLimit = 20,
	        						.ShortToGndFilCtrl.LowerLimit = 0,
	        						.ShortToGndFilCtrl.UpStep = 4,
	        						.ShortToGndFilCtrl.DownStep = 1,
	        						.ShortToGndFilCtrl.Count = 0,
	        						.ShortToGndFilCtrl.Input = FALSE,
	        						.ShortToGndFilCtrl.Output = FALSE,
	        						.ShortToPwrFilCtrl.UpperLimit = 20,
	        						.ShortToPwrFilCtrl.LowerLimit = 0,
	        						.ShortToPwrFilCtrl.UpStep = 4,
	        						.ShortToPwrFilCtrl.DownStep = 1,
	        						.ShortToPwrFilCtrl.Count = 0,
	        						.ShortToPwrFilCtrl.Input = FALSE,
	        						.ShortToPwrFilCtrl.Output = FALSE,	        						
	        						.ReadPwrEnPinSts = ReadSnsGroup3PwrEnSts,
								 }
};

/******************************************************************************/
/**
* @brief: 读取使能电源IO状态
**/
/******************************************************************************/
uint8 ReadChipSupplyPwrEnSts(void)
{     
	return READ_CHIP_SUPPLY_POWER_EN_PIN;
}

uint8 ReadSnsGroup1PwrEnSts(void)
{
	return READ_SNS_GROUP1_POWER_EN_PIN;
}

uint8 ReadSnsGroup2PwrEnSts(void)
{
	return READ_SNS_GROUP2_POWER_EN_PIN;
}

uint8 ReadSnsGroup3PwrEnSts(void)
{
	return READ_SNS_GROUP3_POWER_EN_PIN;
}

