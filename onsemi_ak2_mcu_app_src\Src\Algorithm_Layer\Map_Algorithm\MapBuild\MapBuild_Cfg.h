/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * F_R_ObjMapCal_Cfg: 
 * Created on: 2023-02-16 15:38
 * Original designer: AntonyFang
 ******************************************************************************/

#ifndef MapBuild_Cfg_H
#define MapBuild_Cfg_H

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "MapBuild_Type.h"


/******************************************************************************/
/**
 * @brief   调试打印
 */
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/

/* 定义角度值 */
#define  F_R_MAP_360_ANGLE                       6.2832
#define  F_R_MAP_180_ANGLE                       3.1416

#define  F_R_MAP_100_ANGLE                       1.745
#define  F_R_MAP_91_ANGLE                        1.588
#define  F_R_MAP_90_ANGLE                        1.571
#define  F_R_MAP_89_8_ANGLE                      1.567
#define  F_R_MAP_89_ANGLE                        1.553
#define  F_R_MAP_85_ANGLE                        1.4835
#define  F_R_MAP_80_ANGLE                        1.3963
#define  F_R_MAP_75_ANGLE                        1.309
#define  F_R_MAP_70_ANGLE                        1.2217
#define  F_R_MAP_65_ANGLE                        1.1345
#define  F_R_MAP_60_ANGLE                        1.0472
#define  F_R_MAP_55_ANGLE                        0.9599
#define  F_R_MAP_50_ANGLE                        0.8727
#define  F_R_MAP_45_ANGLE                        0.7854
#define  F_R_MAP_40_ANGLE                        0.6981
#define  F_R_MAP_35_ANGLE                        0.6109
#define  F_R_MAP_30_ANGLE                        0.5236
#define  F_R_MAP_25_ANGLE                        0.4363
#define  F_R_MAP_20_ANGLE                        0.3491
#define  F_R_MAP_15_ANGLE                        0.2618
#define  F_R_MAP_10_ANGLE                        0.1745
#define  F_R_MAP_8_ANGLE                         0.14
#define  F_R_MAP_7_ANGLE                         0.1222
#define  F_R_MAP_6_ANGLE                         0.1047
#define  F_R_MAP_5_ANGLE                         0.0873
#define  F_R_MAP_4_ANGLE                         0.07
#define  F_R_MAP_3_ANGLE                         0.0524
#define  F_R_MAP_2_ANGLE                         0.0349
#define  F_R_MAP_1_ANGLE                         0.0175
#define  F_R_MAP_0_5_ANGLE                       0.0087
#define  F_R_MAP_0_25_ANGLE                      0.0045

/* 定义无效Map 到保杠的距离值 */
#define  MAP_TO_BUMPER_INVALID_DIS               65535u

#define  MAP_NO_UPDATE_DELETE_FAR_TIME           1200
#define  MAP_NO_UPDATE_DELETE_MIDDLE_TIME        800
#define  MAP_NO_UPDATE_DELETE_NEAR_TIME          1000
#define  MAP_NO_UPDATE_DELETE_SIDE_TIME          1000

#define  MAP_NO_UPDATE_DELETE_NEAR_MOVE_TIME     1200
#define  MAP_NO_UPDATE_DELETE_NEAR_STOP_TIME     2000

#define  MAP_NO_UPDATE_DELETE_MIDDLE_MOVE_TIME   1000
#define  MAP_NO_UPDATE_DELETE_MIDDLE_STOP_TIME   1600

#define  MAP_NO_UPDATE_DELETE_FAR_MOVE_TIME      1200
#define  MAP_NO_UPDATE_DELETE_FAR_STOP_TIME      1800


#define  MAP_POINT_UPDATE_X_LIMIT                30
#define  MAP_POINT_UPDATE_Y_LIMIT                50

#define  MAP_SIDE_POINT_UPDATE_X_LIMIT           80
#define  MAP_SIDE_POINT_UPDATE_Y_LIMIT           40


#define  MAP_LINE_UPDATE_X_MIN_LIMIT             50
#define  MAP_LINE_UPDATE_Y_MIN_LIMIT             50
#define  MAP_LINE_UPDATE_X_MAX_LIMIT             100
#define  MAP_LINE_UPDATE_Y_MAX_LIMIT             160

#define  MAP_LINE_ONE_POINT_X_LIMIT              80
#define  MAP_LINE_ONE_POINT_Y_LIMIT              150
#define  MAP_LINE_ON_LINE_DIS_LIMIT              100

#define  MAP_JUDGE_F_BUMPER_AREA_X_LINE_LIMIT    600   /* 允许侧边探头的FOV，沿保杠X的位置 */
#define  MAP_JUDGE_R_BUMPER_AREA_X_LINE_LIMIT    400   /* 允许侧边探头的FOV，沿保杠X的位置 */

#define  MAP_JUDGE_BUMPER_AREA_X_FAR_LIMIT       600   /* 前保超过FLS 前部区域判定为前保的X限制值 */

#define  MAP_JUDGE_BUMPER_AREA_Y_LINE_LIMIT      150   /* 允许侧边探头的FOV，沿保杠Y的位置 */

#define  MAP_OUT_RANGE_DELE_DIS_COMPENSET        50
#define  MAP_OUT_RANGE_DELE_DIS_COMPENSET_LOW    1000


/* 同一个探头的左右侦听中临值比较要大于坐标点的范围 */
#define F_R_LEFT_RIGHT_NEAR_X_SUB       180
#define F_R_LEFT_RIGHT_NEAR_Y_SUB       180

#define GAUSSIAN_BANDWIDTH              30
#define PI                              3.14159
#define MEAN_SHIFT_MIN_DIS              0.001


#define MAP_WALL_CURB_HEIGHT            150
#define MAP_INVALID_DIS                 65535
#define MAP_VALID_MAX_DIS               2000

#define F_R_SIDE_ONLY_MASTR_Y_LIMIT     2000

#define F_R_OBJ_MAP_NO_MOVE             50
#define F_R_OBJ_MAP_MATCH               200

#define F_R_OBJ_MAP_X_SUB               120
#define F_R_OBJ_MAP_Y_SUB               300

#define F_R_SIDE_OBJ_MAP_X_SUB          300
#define F_R_SIDE_OBJ_MAP_Y_SUB          150

#define SIDE_MAP_OBJ_Y_SUB              200
#define SIDE_MAP_OBJ_X_SUB              800
#define SIDE_MAP_POINT_TO_LINE_X_SUB    300


#define F_R_BUMPER_ENGE_DIS             100   

#define MAP_TO_CAN_X_LIMIT_MAX          10200     /* 10220--10210 */
#define MAP_TO_CAN_Y_LIMIT_MAX          10200     /* 10220--10210 */
#define MAP_TO_CAN_MAX_VALUE            1020


/* 以下是划分跟踪区域的距离参数 */
#define MAP_BLIND_STOP_ENTER_DIS        200

#define MAP_BLIND_CORNER_ENTER_DIS      320
#define MAP_BLIND_CORNER_EXIT_DIS       450


#define MAP_BLIND_MIDDLE_ENTER_DIS      450
#define MAP_BLIND_MIDDLE_EXIT_DIS       550

#define MAP_BLIND_AREA4_ENTER_DIS       500
#define MAP_BLIND_AREA4_EXIT_DIS        600

#define MAP_BLIND_SUMMON_AREA4_ENTER_DIS  450
#define MAP_BLIND_SUMMON_AREA4_EXIT_DIS   500

#define MAP_BLIND_SUMMON_MID_ENTER_DIS  350
#define MAP_BLIND_SUMMON_MID_EXIT_DIS   400
#define MAP_BLIND_SUMMON_CORNER_ENTER_DIS  300
#define MAP_BLIND_SUMMON_CORNER_EXIT_DIS   380

#define MAP_BLIND_NO_OBJ_TIME           500
#define MAP_BLIND_DISAPPERA_WAIT_TIME   1000



#define MAP_SIDE_NEW_MAP_DIS            3550
#define MAP_CREAT_Y_LIMIT               2000  /* 前后雷达创建Map时限制Y的范围 */
#define MAP_DETECT_ENTER_DIS            3400
#define MAP_FAR_AWAY_NO_UPDATE_DIS      400  /* 后退阶段暂时不删除Map */
#define MAP_CLOSE_DETECT_ENTER_DIS      2000
#define MAP_BLIND_FARAWAY_DETECT_DIS    400
#define MAP_SIDE_DETECT_ENTER_DIS       2500

#define MAP_FAR_AWAY_DELETE_DIS         8000  /* 8000改为10000 */
#define SIDE_MAP_FAR_AWAY_DELETE_DIS    10000  /* 8000改为10000 */

#define MAP_FAR_AWAY_NEW_DIS            1800
#define MAP_MEMORY_TO_BUMPER_DIS        10000   /* Map后台记忆的距离，最远记忆距离保杠10m的Map，超出后就删除 */
#define MAP_NEW_MAP_SUB_DIS             50      /* 进入距离缓存5cm  */

#define MAP_NO_MATCH_DELETE_CNT         45
#define MAP_FUSION_TO_LINE_SUB          100     /* X坐标差值在10cm以内的都进行合并 */
#define MAP_FUSION_NUM                  3

#define MAP_FAST_HIGH_DIS               650
#define MAP_LOW_OBJ_UPDATE_HEIGHT_DIS   800     /* 对于低矮障碍物的高低属性更新策略，距离小于800mm就不再更新其低的属性 */
#define MAP_FUSION_POINT_TO_LINE_Y_SUB  200

/* 前后Map遍历的起始索引值 */
#define F_R_MAP_START_INDEX             1
#define F_R_MAP_END_INDEX               5
#define F_R_MATCH_MAX_OBJ_NUM           8      /* 单个Map最多匹配的Obj坐标个数 */
#define F_R_MAP_Y_COMPARE_DIS           80
#define F_R_NEAR_X_SUB                  150
#define F_R_NEAR_Y_SUB                  150
#define F_R_MAP_POINT_UPDATE_X_SUB      50
#define F_R_MAP_POINT_UPDATE_Y_SUB      80
#define F_R_MAP_POINT_TO_LINE_Y_SUB     150
#define F_R_MAP_LINE_SHORT_TO_POINT_Y   130

#define F_R_MAP_LINE_ENDPOINT_X_SUB     80
#define F_R_MAP_LINE_ENDPOINT_Y_SUB     80


#define F_R_MAP_POINT_UPDATE_CNT        3
#define F_R_MAP_POINT_TO_LINE_CNT       3
#define F_R_MAP_LINE_TO_POINT_CNT       3
#define F_R_MAP_LINE_ENDPOINT_CNT       3
#define F_R_MAP_LINE_SHORT_TO_POINT_CNT 4

#define F_R_MAP_DISAPPEAR_DELE_CNT      16      /* 障碍物消失删除的计数--12次，三个大轮询，合计480ms */
#define F_R_MAP_TO_NEAR_CURB_DIS        900     /* 路砍靠近过程中，近距离快速更新为低 */
#define F_R_MAP_TO_NEAR_CURB_MIN_DIS    400     /* 路砍靠近过程中，近距离快速更新为低 */


#define F_R_MAP_CAR_CLOSE_TO_STOP_CNT   30     /* 车辆从靠近障碍物到停止就快速停止，停止状态暂不更新Map坐标及其高度属性 */
#define F_R_MAP_CAR_FARAWAY_TO_STOP_CNT 150    /* 一个探头平均40ms更新一次，从运动到静止6S才更新 */

#define F_R_MAP_CAR_STOP_CLOSE_DIS      80
#define F_R_MAP_CAR_STOP_FARAWAY_DIS    (-80)
#define F_R_MAP_CAR_STOP_NO_MOVE_DIS    40

#define F_R_MAP_CAR_STOP_OBJ_CLOSE_CNT   20
#define F_R_MAP_CAR_STOP_OBJ_FARAWAY_CNT 20
#define F_R_MAP_CAR_STOP_OBJ_STOP_CNT    30
#define F_R_MAP_CAR_MOVE_CLOSE_CNT       10
#define F_R_MAP_WAIT_UPDATE_CNT          250   /* 一个探头平均40ms更新一次，等待时间 = 250*40 = 10S */
#define F_R_MAP_BLIND_WAIT_UPDATE_CNT    100    /* 一个探头平均40ms更新一次，等待时间 = 50*40 = 2 */

#define F_R_LINE_FUSION_DIS_SUB            100
#define F_R_LOW_OBJ_NO_UPDATE_DIS          900   /* 对于低矮障碍物，小于90cm不再更新坐标 */
#define F_R_HIGH_CURB_OBJ_NO_UPDATE_DIS    1100  /* 对于高路沿，小于130cm不再更新坐标 */

#define F_R_POINT_USE_SNS_TO_HIGH_DIS      2200
#define F_R_GUIDANCE_USE_SNS_TO_HIGH_DIS   900


#define F_R_POINT_FUSION_DIS_SUB        80
#define F_R_FUSION_POINT_Y_ABS_LIMIT    800   /* 对于线的融合中，Y值的限制 */
#define F_R_FUSION_POINT_Y_SUB_LIMIT    750   /* 对于线的融合中，两点Y差值的限制 */
#define F_R_FUSION_POINT_BUMPER_DIS_LIMIT  300

#define F_R_MAP_JUDGE_EXIST_OBJ_DIS            450
#define F_R_MAP_JUDGE_EXIST_OBJ_LISTEN_DIS     550

#define F_R_MAP_NO_OBJ_CNT              200

#define F_R_MAP_LINE_SHORT_WAIT_CNT     50





#define F_R_MAP_HIGH_CURB_CNT_SET         2 
#define F_R_MAP_HIGH_CURB_CONTINUE_CNT    10 
#define F_R_CREAT_FAST_HIGH_DIS           500    /* 对于50cm以内产生的Map，快速置高 */
#define F_R_CREAT_FAST_Y_LIMIT            1000   /* 对于Y值在1000以内产生的Map，快速置高 */

#define FRONT_MAP_TO_BUMPER_DIS_CMP_CONNER      20     /* 角雷达区域的距离补偿 */
#define REAR_MAP_TO_BUMPER_DIS_CMP_CONNER       50     /* 角雷达区域的距离补偿 */


#define SIDE_SNS_BUFFER_NUM      3
#define SIDE_START_FOLLOW_DIS    3600
#define SIDE_INVALID_DIS         65535
#define SIDE_MAP_DELETE_DIS      5000
#define SIDE_MAP_Y_LIMIT_MIN_ABS 900
#define SDW_JUDGE_X_OFFSET                   50
#define SDW_JUDGE_Y_OFFSET                   100
#define SDW_CHANGE_HOLD_DIS                  300
#define SDW_SAMPLE_POINT_DIS                 30  /* 暂定3cm */
#define SDW_SNS_Y_LIMIT_DIS                  900

#define SIDE_MAP_POINT_IN_FOV_DIS     80
#define SIDE_MAP_OUT_ANGLE_DELE_Y     3500
#define SIDE_MAP_POINT_OUT_ANGLE_DELE_Y 3000

#define SIDE_MAP_LINE_INVADE_Y_SUB    (-150)
#define SIDE_MAP_JUDGE_EXIST_Y_LIMIT  3500      /* 用于侧边已经生成的Map和DE进行比较，判断探头附近是否有已经存在的障碍物 */
#define SIDE_MAP_JUDGE_SNS_DIS_ON_LINE  (-200)

#define SIDE_MAP_DIS_NO_MATCH_DELE_Y_250_CM   420
#define SIDE_MAP_DIS_NO_MATCH_DELE_Y_200_CM   320
#define SIDE_MAP_DIS_NO_MATCH_DELE_Y_150_CM   220
#define SIDE_MAP_DIS_NO_MATCH_DELE_Y_100_CM   180
#define SIDE_MAP_DIS_NO_MATCH_DELE_Y_50_CM    150

#define SIDE_MAP_DIS_NO_MATCH_DELE_DIVID_2    2
#define SIDE_MAP_DIS_NO_MATCH_DELE_DIVID_3    3
#define SIDE_MAP_DIS_NO_MATCH_DELE_DIVID_4    4



#define FRONR_AREA4_FAST_HIGH_DIS     580
#define FRONR_MAP_FAST_HIGH_DIS       500
#define REAR_MAP_FAST_HIGH_DIS        400

#define MAP_EXIST_PROB_SUB_NEAR_DIS   500    /* 小于50cm，置信度不递减，只递增 */
#define MAP_EXIST_PROB_SUB_MIDDLE_DIS 1800   /* 50~180cm范围，定扫均可以探测到，置信度每间隔160ms递减一次，超过180cm范围，只有扫频可以探测到，置信度每间隔320ms递减一次 */
#define MAP_EXIST_PROB_SUB_HIGH_SPD   350    /* 超过3km，只有一直发扫频，同样间隔160ms递减一次 */

#define SIDE_MAP_FUSION_Y_SUB         150
#define SIDE_MAP_FUSION_NEAR_POINT_X  250

#define MAP_MOVE_TO_STOP_HOLD_TIME    1000

#define MAP_DE_MATCH_START_DIS        420
#define MAP_DE_MATCH_END_DIS          1500
#define MAP_DE_HOLD_MAP_DIS           1000

#define SIDE_MAP_DELE_Y_ADD_DIS       400

#define SIDE_SHORT_MAP_IN_FOV_DIS     80
#define SIDE_SHORT_MAP_DIS_SUB        500
#define SIDE_SHORT_MAP_DE_NO_MATCH_CNT 3
#define SIDE_MAP_STOP_DE_NO_MATCH_CNT 3

#define LINE_MAP_FUSION_X_COOR_SUB    150
#define LINE_MAP_FUSION_Y_COOR_SUB    600
#define F_R_MAP_OUT_Y_DELE_DIS        3000

#define F_R_MAP_CUT_MIN_P1_P2_DIS     260
#define F_R_MAP_CUT_LONG_P1_P2_DIS    1100
#define F_R_MAP_CUT_MIN_Y_ABS_DIS0    1100
#define F_R_MAP_CUT_MIN_Y_ABS_DIS1    900
#define F_R_MAP_CUT_MIN_Y_ABS_DIS2    700
#define F_R_MAP_CUT_MIN_Y_ABS_DIS3    500

#define HEIGHT_INX_CHIRP_SUB          4
#define HEIGHT_INX_STD_SUB            3

#define NEAR_HEIGHT_FAST_HIGH_MIDDLE_DIS 500
#define NEAR_HEIGHT_FAST_HIGH_CORNER_DIS 400

#define SIDE_MAP_DEGE_DELE_BUMPER_DIS02      500

#define SIDE_MAP_DEGE_DELE_BUMPER_DIS        1500    /** @brief: 900重新修改为1500，优先保证提前删除 */
#define SIDE_MAP_INNER_DELE_BUMPER_DIS       900
#define SIDE_MAP_COVER_CAR_DELE_BUMPER_DIS   1500
#define SIDE_MAP_LOW_MATCH_BUMPER_MIN_DIS    200
#define SIDE_MAP_LOW_MATCH_BUMPER_MAX_DIS    1600
#define SIDE_MAP_LOW_MATCH_BUMPER_OK_DIS     750

#define SIDE_MAP_EDGE_DELE_DIS_SUB           180
#define SIDE_MAP_INNER_DELE_DIS_SUB          150
#define SIDE_MAP_COVER_CAR_DELE_DIS_SUB      300
#define SIDE_LOW_MAP_JUDGE_DIS_SUB           250

#define SIDE_MAP_EDGE_DE_NO_MATCH_DELE_CNT        2
#define SIDE_MAP_INNER_DE_NO_MATCH_DELE_CNT       2
#define SIDE_MAP_COVER_CAR_DE_NO_MATCH_DELE_CNT   6

#define SIDE_MAP_ONE_POINT_CUT_DIS          900
#define SIDE_MAP_LINE_ONE_POINT_IN_FOV_DIS  40
#define SIDE_MAP_ONE_POINT_CUT_Y_SUB        40

/* 针对侧边Map区分不同泊车模式下的开始画Map距离 */
#define FRONT_MAP_SEARCH_START_DIS          3900
#define FRONT_MAP_SEARCH_LOW_START_DIS      2200

#define FRONT_MAP_GUIDANCE_FORWARD_DIS      2500
#define FRONT_MAP_GUIDANCE_FORWARD_PARA_DIS 500
#define FRONT_MAP_GUIDANCE_BACKWARD_DIS     800

#define REAR_MAP_SEARCH_START_DIS           2500
#define REAR_MAP_GUIDANCE_FORWARD_DIS       800
#define REAR_MAP_GUIDANCE_BACKWARD_01_DIS   1500
#define REAR_MAP_GUIDANCE_BACKWARD_02_DIS   600
#define REAR_MAP_GUIDANCE_BACKWARD_03_DIS   2500
#define SIDE_MAP_REAR_SNS_EXTEND_DIS        100

#define SIDE_MAP_POINT_DE_NO_MATCH_DELE_DIS 2500


/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/








#endif /* end of F_R_ObjMapCal_Cfg_H */

