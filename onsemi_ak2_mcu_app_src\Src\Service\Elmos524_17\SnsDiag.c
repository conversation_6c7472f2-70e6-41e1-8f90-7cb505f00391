/*
 * SnsDiag.c
 *
 *  Created on: 2021年6月30日
 *      Author: 6000021992
 */
#include "Elmos_524_17_Private.h"
#include "SnsDiag.h"
#include "Elmos_524_17_SnsCtrl.h"
#include "AK2_MCU_Drv.h"

#define MIN_RINGING_TIME          (530) 
#define BURST_FREQUENCY      	  (48000) /*uint:Hz*/
#define HWERR1_EXCEPT_IMPEDANCE_PARITY_ABNORMAL_MASK	(0x7EFF)
#define HWERR2_EXCEPT_TEMP_ABNORMAL_MASK	            (0xFFFD)
#define HWERR1_EEPROM_PARAM_ABNORMAL_MASK	    (0x0040)
#define IMPEDANCE_UPDATE_MASK                   (0x0100)

typedef struct
{
	uint8  NormalCntMax;
	uint8  ErrCntMax;
}ErrStatCheckCnt_str;

typedef struct
{
	uint8 Cnt;
	uint8 ErrFlg;
	uint8 ErrDTCFlg;
}ErrStat_str;


typedef struct
{
	uint16 ErrorTime;

	ErrStatCheckCnt_str ErrCheckCnt;
}RingTimeRang_str;



typedef struct
{
	ErrStat_str RTErrStat[2];/*[0]:TooLong ,[1]:TooShort*/
}RingTimeDiagResult_str;

typedef struct
{
	RingTimeRang_str RTDiagRang[2];/*[0]:TooLong ,[1]:TooShort*/
}RingTimeDiagRang_str;

static RingTimeDiagRang_str RingTimeDiagRang[3];
static SnsErrInfo_t SnsErrInfo[SNSNum] = {0};
static bool ImpFreqAbnorFlg[SNSNum] = {0};
static bool RingFreqAbnorFlg[SNSNum] = {0};
static bool InternalFailure[SNSNum] = {0};
static AK2_Pub_FilterCtrl_t SnsSigInvalidFilCtrl[SNSNum] = {0};
static AK2_Pub_FilterCtrl_t SnsNoSigFilCtrl[SNSNum] = {0};
/*filter according to temperature*/
static AK2_Pub_FilterCtrl_t SnsRFreqAbnorFilLess0[SNSNum] = {0};
static AK2_Pub_FilterCtrl_t SnsRFreqAbnorFil0To10[SNSNum] = {0};
static AK2_Pub_FilterCtrl_t SnsRFreqAbnorFil10To40[SNSNum] = {0};
static AK2_Pub_FilterCtrl_t SnsRFreqAbnorFilMore40[SNSNum] = {0};
static AK2_Pub_FilterCtrl_t SnsRingTimeAbnorFilCtrl[SNSNum] = {0};
static AK2_Pub_FilterCtrl_t SnsBlindnessFilCtrl[SNSNum] = {0};
uint8 EepromErrFlg[SNSNum] = {0};
static SnsErrCtrl_t SnsErrCtrl = {0,SnsErrHandleNone};
static TransducerErrType_e TransducerErr[SNSNum] = {TransducerErrNone};
static Sns_PDCM_RingTimeData enuRTData[SNSNum] = {0};

void SnsDiag_Init(void)
{
	uint32 Idx = 0;

	for(Idx = SNS_FL_S;Idx < SNSNum; Idx++)
	{
		memset((void *)&SnsSigInvalidFilCtrl[Idx],0,sizeof(AK2_Pub_FilterCtrl_t));
		SnsSigInvalidFilCtrl[Idx].UpperLimit = 5;
		SnsSigInvalidFilCtrl[Idx].LowerLimit = 0;
		SnsSigInvalidFilCtrl[Idx].UpStep = 1;
		SnsSigInvalidFilCtrl[Idx].DownStep = 1;

		memset((void *)&SnsNoSigFilCtrl[Idx],0,sizeof(AK2_Pub_FilterCtrl_t));
		SnsNoSigFilCtrl[Idx].UpperLimit = 5;
		SnsNoSigFilCtrl[Idx].LowerLimit = 0;
		SnsNoSigFilCtrl[Idx].UpStep = 1;
		SnsNoSigFilCtrl[Idx].DownStep = 1;

		memset((void *)&SnsRFreqAbnorFilLess0[Idx],0,sizeof(AK2_Pub_FilterCtrl_t));
		SnsRFreqAbnorFilLess0[Idx].UpperLimit = 20;
		SnsRFreqAbnorFilLess0[Idx].LowerLimit = 0;
		SnsRFreqAbnorFilLess0[Idx].UpStep = 1;
		SnsRFreqAbnorFilLess0[Idx].DownStep = 8;

		memset((void *)&SnsRFreqAbnorFil0To10[Idx],0,sizeof(AK2_Pub_FilterCtrl_t));
		SnsRFreqAbnorFil0To10[Idx].UpperLimit = 20;
		SnsRFreqAbnorFil0To10[Idx].LowerLimit = 0;
		SnsRFreqAbnorFil0To10[Idx].UpStep = 1;
		SnsRFreqAbnorFil0To10[Idx].DownStep = 8;

		memset((void *)&SnsRFreqAbnorFil10To40[Idx],0,sizeof(AK2_Pub_FilterCtrl_t));
		SnsRFreqAbnorFil10To40[Idx].UpperLimit = 20;
		SnsRFreqAbnorFil10To40[Idx].LowerLimit = 0;
		SnsRFreqAbnorFil10To40[Idx].UpStep = 1;
		SnsRFreqAbnorFil10To40[Idx].DownStep = 8;
		
		memset((void *)&SnsRFreqAbnorFilMore40[Idx],0,sizeof(AK2_Pub_FilterCtrl_t));
		SnsRFreqAbnorFilMore40[Idx].UpperLimit = 20;
		SnsRFreqAbnorFilMore40[Idx].LowerLimit = 0;
		SnsRFreqAbnorFilMore40[Idx].UpStep = 1;
		SnsRFreqAbnorFilMore40[Idx].DownStep = 8;

		memset((void *)&SnsRingTimeAbnorFilCtrl[Idx],0,sizeof(AK2_Pub_FilterCtrl_t));
		SnsRingTimeAbnorFilCtrl[Idx].UpperLimit = 3;
		SnsRingTimeAbnorFilCtrl[Idx].LowerLimit = 0;
		SnsRingTimeAbnorFilCtrl[Idx].UpStep = 1;
		SnsRingTimeAbnorFilCtrl[Idx].DownStep = 1;

		memset((void *)&SnsBlindnessFilCtrl[Idx],0,sizeof(AK2_Pub_FilterCtrl_t));
		SnsBlindnessFilCtrl[Idx].UpperLimit = 3;
		SnsBlindnessFilCtrl[Idx].LowerLimit = 0;
		SnsBlindnessFilCtrl[Idx].UpStep = 1;
		SnsBlindnessFilCtrl[Idx].DownStep = 1;

	}
}

uint8 CheckTestResult(uint8 TestErrFlg,ErrStatCheckCnt_str *pErrCheckCnt,ErrStat_str *pErrStat)
{
	pErrStat->ErrFlg = TestErrFlg;
	if(pErrStat->ErrDTCFlg == VALID)
	{
		if(TestErrFlg == VALID)
		{
			pErrStat->Cnt = 0;
		}
		else
		{
			pErrStat->Cnt++;
			if(pErrStat->Cnt >= pErrCheckCnt->NormalCntMax)
			{
				pErrStat->ErrDTCFlg = INVALID;
				pErrStat->Cnt = 0;
			}
		}
	}
	else
	{
		if(TestErrFlg == INVALID)
		{
			pErrStat->Cnt = 0;
		}
		else
		{
			pErrStat->Cnt++;
			if(pErrStat->Cnt >= pErrCheckCnt->ErrCntMax)
			{
				pErrStat->ErrDTCFlg = VALID;
				pErrStat->Cnt = 0;
			}
		}
	}
	return E_OK;
}

uint8 SnsRingTimeDiag(uint16 RingTime,RingTimeDiagRang_str *pRingTimeDiagRang,RingTimeDiagResult_str *pRingTimeDiagData)
{

	uint8 TestErrFlg;

	/*RingTime Too Long*/
	TestErrFlg = (RingTime >= pRingTimeDiagRang->RTDiagRang[0].ErrorTime) ? VALID : INVALID;
	CheckTestResult(TestErrFlg,&pRingTimeDiagRang->RTDiagRang[0].ErrCheckCnt,&pRingTimeDiagData->RTErrStat[0]);

	/*RingTime Too Short*/
	TestErrFlg = (RingTime <= pRingTimeDiagRang->RTDiagRang[1].ErrorTime) ? VALID : INVALID;
	CheckTestResult(TestErrFlg,&pRingTimeDiagRang->RTDiagRang[1].ErrCheckCnt,&pRingTimeDiagData->RTErrStat[1]);

	return E_OK;
}
uint16 CalcRingFreq(uint16 SampleFreq,Sns_PDCM_RingTimeData *pRTData)
{
	uint16 RTFreq = 0;
	uint16 RingingCount = pRTData->RingingCount;
	uint16 RingingSamples = pRTData->RingingSamples;
	if(RingingSamples != 0)
	{
		RTFreq = (8 * SampleFreq * RingingCount) / RingingSamples;
	}
	else
	{
		RTFreq = 0;
	}
	return RTFreq;
}

void TransducerErrDiag(SnsID_en SnsID,uint16 RTime,uint16 RFreq,uint8 HwErrCnt)
{
	static uint16 LastRFreq[SNSIDNum] = {0};

	/*以下判定上下限为实测所得*/
	if((0 == RTime) && (0 == RFreq) && (0 != HwErrCnt))
	{
		TransducerErr[SnsID] = TransducerPosAndNegShort;
	}
	else if((0 == RTime) && (0 == RFreq) && (0 == HwErrCnt))
	{
		TransducerErr[SnsID] = TransducerPosOpen;
	}
	else if((RTime < 550) && (RTime > 500) && (0 == HwErrCnt))
	{
		/*探芯负极开路偶尔会出现频率为64k*/
		if(((RFreq > 54000) && (RFreq < 55000))
		  || ((LastRFreq[SnsID] > 54000) && (LastRFreq[SnsID] < 55000) && (RFreq >= 64000)))
		{
			TransducerErr[SnsID] = TransducerNegOpen;
		}
		else if(RFreq >= 64000)
		{
			TransducerErr[SnsID] = TransducerPosAndNegOpen;
		}
		else
		{}
	}
	else
	{
		TransducerErr[SnsID] = TransducerErrNone;
	}

	LastRFreq[SnsID] = RFreq;
}

void SnsDiag_SnsSelfTestResultHandle(void)
{
	/*只有读取探头status才能被更新*/
    SnsID_en SnsIDIdx = SNSID_NONE;	
	SnsSelfTestStatusMsg_str *pSnsSelfTestStatusMsg = NULL;
	DSIMasterID_en DSIMasterID = DSIMaster0;
	DSIChlID_en DSIChlID = DSIChl1;
	uint8 SlotID = 0;
	SnsErrCtrl_t* pSnsErrCtrl = &SnsErrCtrl;
	
	for(SnsIDIdx = SNS_FL_S;SnsIDIdx < SNSNum; SnsIDIdx++)
	{	
		DSIMasterID = SnsAddCfg[SnsIDIdx].DSIMasterID;
    	DSIChlID = SnsAddCfg[SnsIDIdx].DSIChlSEL - 1U;
    	SlotID = SnsAddCfg[SnsIDIdx].cSnsPhysAddr - 1U;
		
		pSnsSelfTestStatusMsg = GetSnsSelfTestStatusAddr(DSIMasterID,DSIChlID,SlotID);

		if((0 != (pSnsSelfTestStatusMsg->HWErrStatus1 & HWERR1_EXCEPT_IMPEDANCE_PARITY_ABNORMAL_MASK))
		   || (0 != (pSnsSelfTestStatusMsg->HWErrStatus2 & HWERR2_EXCEPT_TEMP_ABNORMAL_MASK))
		   || (0 != pSnsSelfTestStatusMsg->ComErrStatus))
		{
			InternalFailure[SnsIDIdx] = TRUE;
			PRINTF_SNSDIAG("sns %d HWErrStatus1 %d  HWErrStatus2 %d ComErrStatus %d \n",SnsIDIdx,\
				pSnsSelfTestStatusMsg->HWErrStatus1,pSnsSelfTestStatusMsg->HWErrStatus2,\
				pSnsSelfTestStatusMsg->ComErrStatus);
		}
		else
		{
			InternalFailure[SnsIDIdx] = FALSE;
		}
		
		/*EEPROM参数错误*/
		if(pSnsSelfTestStatusMsg->HWErrStatus1 & HWERR1_EEPROM_PARAM_ABNORMAL_MASK)
		{ 
			if(0 == EepromErrFlg[SnsIDIdx])/*在同一次上电过程中，只重新启动1次*/
			{
				pSnsErrCtrl->SnsErrMask |= (uint16_t)1 << SnsIDIdx;
				pSnsErrCtrl->SnsErrHandle = SnsReWrParam;
			}
			EepromErrFlg[SnsIDIdx] = 1;
		}

		if(pSnsSelfTestStatusMsg->DataUpdataFlg & IMPEDANCE_UPDATE_MASK)
		{
			if(pSnsSelfTestStatusMsg->Resverd2[0] == 0) 
			{
				ImpFreqAbnorFlg[SnsIDIdx] = TRUE;
			}
			else
			{
				ImpFreqAbnorFlg[SnsIDIdx] = FALSE;
			}
		}
	}
}

uint8 SnsDiag_SnsDiagHandle(SnsID_en SnsIDIdx)
{
	Sns_PDCMMsg_Status_Str * pPDCMMsg_StatusAddr = NULL;
    Sns_PDCM_MEASMsg_Str *pSns_PDCM_MEASMsg = NULL;
	DSIMasterID_en DSIMasterID = DSIMaster0;
	DSIChlID_en DSIChlID = DSIChl1;
	uint8 SlotID = 0;
	uint16 RingFreq = 0;
	AK2_Car_AmbientTempType LenuCar_AmbientTemp = 25;
	uint16 DMFlg = 0;
    uint16 ParamCfgFlg = 0;
	bool ParamCfgFail = FALSE; 
	uint32_t SPIRxCrcErrCnt = 0;
	AK2_Pub_FilterCtrl_t *pstrRFreqAbnorFilLess0 = &SnsRFreqAbnorFilLess0[SnsIDIdx];
 	AK2_Pub_FilterCtrl_t *pstrRFreqAbnorFil0To10 = &SnsRFreqAbnorFil0To10[SnsIDIdx];
 	AK2_Pub_FilterCtrl_t *pstrRFreqAbnorFil10To40 = &SnsRFreqAbnorFil10To40[SnsIDIdx];
 	AK2_Pub_FilterCtrl_t *pstrRFreqAbnorFilMore40 = &SnsRFreqAbnorFilMore40[SnsIDIdx];

	Signal_VoltageStatusType LenuWorkVolSts = NORMAL_VOLTAGE;
	static uint32 VolErrTimer[2] = {0};
	static uint8 VolErrFlg = 0; 

	DMFlg = Elmos17SnsCtrl_GetDMFlg();
    ParamCfgFlg = Elmos17SnsCtrl_GetSnsParamCfgFlg();
	LenuCar_AmbientTemp = AK2_ReadCAN_AppSignal_Car_AmbientTempType();
	
	DSIMasterID = SnsAddCfg[SnsIDIdx].DSIMasterID;
	DSIChlID = SnsAddCfg[SnsIDIdx].DSIChlSEL - 1U;
	SlotID = SnsAddCfg[SnsIDIdx].cSnsPhysAddr - 1U;
	
	pPDCMMsg_StatusAddr = GetPDCMMsg_StatusAddr(DSIMasterID,DSIChlID,SlotID);
	pSns_PDCM_MEASMsg = GetMeasMsgBufAddr(DSIMasterID, DSIChlID,SlotID);
	SPIRxCrcErrCnt = GetSPIRxCrcErrCnt(DSIMasterID);

	if((NULL == pPDCMMsg_StatusAddr) || (NULL == pSns_PDCM_MEASMsg))
	{
		return 1;
	}

	/*save ring data to monitor*/
	if(pSns_PDCM_MEASMsg->SNS_MEAS_TYPE < MEAS_STD_LISTEN)
	{
		memcpy((void*)&enuRTData[SnsIDIdx],(const void *)&pSns_PDCM_MEASMsg->RTData,sizeof(Sns_PDCM_RingTimeData));
	}
		
	/*参数是否配置判断*/
	if((0 != (DMFlg & (0x0001 << SnsIDIdx))) && (0 == (ParamCfgFlg & (0x0001 << SnsIDIdx))))
	{
		ParamCfgFail = TRUE;
	}

	if((pSns_PDCM_MEASMsg->ComHwErrorCnt[0] != 0)
	||(pSns_PDCM_MEASMsg->ComHwErrorCnt[1] != 0))
	{
		SnsErrCtrl.SnsErrHandle = SnsReadStatus;
	}
	else
	{
		InternalFailure[SnsIDIdx] = FALSE;
	}

	/*communication status*/
	if(pPDCMMsg_StatusAddr->NoSymbolErr > 10)/*5个周期没有接收到PDCM数据,10 = BRCcnt/2*/
	{
		SnsNoSigFilCtrl[SnsIDIdx].Input = TRUE;
	}
	else
	{
		SnsNoSigFilCtrl[SnsIDIdx].Input = FALSE;
	}
	AK2_PubAI_Filter(&SnsNoSigFilCtrl[SnsIDIdx]);

	/*CRC status*/
	if(((pPDCMMsg_StatusAddr->CRC_SC_SE_Err > 10) &&((pPDCMMsg_StatusAddr->PacketDsiErrStatus & 0x10) != 0))
		 || (SPIRxCrcErrCnt > 10))
	{
		SnsSigInvalidFilCtrl[SnsIDIdx].Input = TRUE;
	}
	else
	{
		SnsSigInvalidFilCtrl[SnsIDIdx].Input = FALSE;
	}
	AK2_PubAI_Filter(&SnsSigInvalidFilCtrl[SnsIDIdx]);

	/*ring data status*/
	if((pSns_PDCM_MEASMsg->SNS_MEAS_TYPE == MEAS_STD_EMIT) 
		&& (0 == pPDCMMsg_StatusAddr->NoSymbolErr)
		&& (pSns_PDCM_MEASMsg->DiagDatUpDateFlg & DiagRTDataUpDateFlgMask))
	{
		RingFreq = CalcRingFreq(SAMPLE_FREQUENCY,&pSns_PDCM_MEASMsg->RTData);

		if(pSns_PDCM_MEASMsg->RTData.RTM < MIN_RINGING_TIME)
		{
			SnsRingTimeAbnorFilCtrl[SnsIDIdx].Input = TRUE;
		}
		else
		{
			SnsRingTimeAbnorFilCtrl[SnsIDIdx].Input = FALSE;
		}
		AK2_PubAI_Filter(&SnsRingTimeAbnorFilCtrl[SnsIDIdx]);
	
		/*blindness*/
		if((TRUE == ImpFreqAbnorFlg[SnsIDIdx])
			&& (pSns_PDCM_MEASMsg->RTData.RTM > MIN_RINGING_TIME))
		{
			SnsBlindnessFilCtrl[SnsIDIdx].Input = TRUE;
		}
		else
		{
			SnsBlindnessFilCtrl[SnsIDIdx].Input = FALSE;
		}
		AK2_PubAI_Filter(&SnsBlindnessFilCtrl[SnsIDIdx]);

		/*ring fre*/
		if(LenuCar_AmbientTemp < 0)
		{
			if((RingFreq > (BURST_FREQUENCY + 10000)) || (RingFreq < (BURST_FREQUENCY - 10000)))
			{
				pstrRFreqAbnorFilLess0->Input = TRUE;
			}
			else
			{
				pstrRFreqAbnorFilLess0->Input = FALSE;
			}
			AK2_PubAI_Filter(pstrRFreqAbnorFilLess0);
			AK2_PubAI_ClrFilterData(pstrRFreqAbnorFil0To10);
			AK2_PubAI_ClrFilterData(pstrRFreqAbnorFil10To40);
			AK2_PubAI_ClrFilterData(pstrRFreqAbnorFilMore40);
		}
		else if((LenuCar_AmbientTemp >= 0) && (LenuCar_AmbientTemp < 10))
		{
			if((RingFreq > (BURST_FREQUENCY + 7000)) || (RingFreq < (BURST_FREQUENCY - 7000)))
			{
				pstrRFreqAbnorFil0To10->Input = TRUE;
			}
			else
			{
				pstrRFreqAbnorFil0To10->Input = FALSE;
			}
			AK2_PubAI_Filter(pstrRFreqAbnorFil0To10);
			AK2_PubAI_ClrFilterData(pstrRFreqAbnorFilLess0);
			AK2_PubAI_ClrFilterData(pstrRFreqAbnorFil10To40);
			AK2_PubAI_ClrFilterData(pstrRFreqAbnorFilMore40);
		}
		else if((LenuCar_AmbientTemp >= 10) && (LenuCar_AmbientTemp < 40))
		{
			if((RingFreq > (BURST_FREQUENCY + 5000)) || (RingFreq < (BURST_FREQUENCY - 5000)))
			{
				pstrRFreqAbnorFil10To40->Input = TRUE;
			}
			else
			{
				pstrRFreqAbnorFil10To40->Input = FALSE;
			}
			AK2_PubAI_Filter(pstrRFreqAbnorFil10To40);
			AK2_PubAI_ClrFilterData(pstrRFreqAbnorFilLess0);
			AK2_PubAI_ClrFilterData(pstrRFreqAbnorFil0To10);
			AK2_PubAI_ClrFilterData(pstrRFreqAbnorFilMore40);
		}
		else if(LenuCar_AmbientTemp >= 40)
		{
			if((RingFreq > (BURST_FREQUENCY + 6000)) || (RingFreq < (BURST_FREQUENCY - 6000)))
			{
				pstrRFreqAbnorFilMore40->Input = TRUE;
			}
			else
			{
				pstrRFreqAbnorFilMore40->Input = FALSE;
			}
			AK2_PubAI_Filter(pstrRFreqAbnorFilMore40);
			AK2_PubAI_ClrFilterData(pstrRFreqAbnorFilLess0);
			AK2_PubAI_ClrFilterData(pstrRFreqAbnorFil0To10);
			AK2_PubAI_ClrFilterData(pstrRFreqAbnorFil10To40);
		}
		else
		{

		}
				
		if((TRUE == pstrRFreqAbnorFilLess0->Output)
			||(TRUE == pstrRFreqAbnorFil0To10->Output)
			||(TRUE == pstrRFreqAbnorFil10To40->Output)
			||(TRUE == pstrRFreqAbnorFilMore40->Output)
			)
		{
			RingFreqAbnorFlg[SnsIDIdx] = TRUE;
		}
		else
		{
			RingFreqAbnorFlg[SnsIDIdx] = FALSE;
		}

		/*探芯异常类型判断*/
		TransducerErrDiag(SnsIDIdx,pSns_PDCM_MEASMsg->RTData.RTM,RingFreq,pSns_PDCM_MEASMsg->ComHwErrorCnt[1]);

	}

	if(TRUE == SnsNoSigFilCtrl[SnsIDIdx].Output)
	{
		SnsErrInfo[SnsIDIdx].eErrType = SnsNoSignal;
		SnsErrCtrl.SnsErrMask |= (uint16_t)1 << SnsIDIdx;
		SnsErrCtrl.SnsErrHandle = SnsReDM;
	}
	else if(TRUE == SnsSigInvalidFilCtrl[SnsIDIdx].Output)
	{
		SnsErrInfo[SnsIDIdx].eErrType = SnsSignalInvalid;
	}
	else if((TRUE == SnsBlindnessFilCtrl[SnsIDIdx].Output)
		    ||(TRUE == RingFreqAbnorFlg[SnsIDIdx]))
	{
		SnsErrInfo[SnsIDIdx].eErrType = SnsBlindness;
		SnsErrCtrl.SnsErrHandle = SnsSelfTest;
		/*清空余震异常*/
		SnsRingTimeAbnorFilCtrl[SnsIDIdx].Count = 0;
		SnsRingTimeAbnorFilCtrl[SnsIDIdx].Output = FALSE;
	}
	else if((TRUE == InternalFailure[SnsIDIdx]) 
			|| (TRUE == SnsRingTimeAbnorFilCtrl[SnsIDIdx].Output)
		 	|| (TRUE == ParamCfgFail)
		    || (TRUE == EepromErrFlg[SnsIDIdx]))
	{
		SnsErrInfo[SnsIDIdx].eErrType = SnsInternalFailure;
	}
	else
	{
		SnsErrInfo[SnsIDIdx].eErrType = SnsFaultNone;
	}

	if(SnsFaultNone == SnsErrInfo[SnsIDIdx].eErrType)
	{
		SnsErrInfo[SnsIDIdx].eErrFlag = FALSE;
	}
	else
	{	
		SnsErrInfo[SnsIDIdx].eErrFlag = TRUE;
	}

	/* 恢复正常电压2s后才进行故障诊断 */
	ReadPwrManage_GroupStatus(PM_APP, &LenuWorkVolSts);
	if(LenuWorkVolSts == NORMAL_VOLTAGE)
	{
		VolErrTimer[1] = AK2_GetSys1msTickFunc();
		if((VolErrTimer[1] - VolErrTimer[0]) >= 2000)
		{
			VolErrFlg = 2;
		}
		else 
		{
			if(VolErrFlg == 0)
			{
				VolErrFlg = 1;
			}
			else if(VolErrFlg == 1)
			{
				/* 清空故障 */
				SnsDiag_Init();
				memset(SnsErrInfo, 0, sizeof(SnsErrInfo));
			}
			else
			{
				/* DO NOTHING */
			}
		}
	}
	else
	{
		VolErrFlg = 0;
		/* 恢复正常电压，记录计时器 */
		VolErrTimer[0] = AK2_GetSys1msTickFunc();
		/* 清空故障 */
		SnsDiag_Init();
		memset(SnsErrInfo, 0, sizeof(SnsErrInfo));
	}

	return 0;
}

void SnsDiag_SnsIdleDiagHandle(void)
{
    SnsID_en SnsIDIdx = SNSID_NONE;	
	DSIMasterID_en DSIMasterID = DSIMaster0;
	DSIChlID_en DSIChlID = DSIChl1;
	uint8 SlotID = 0;
	DSIPinSts_en DsiPinSts = DSIPinNor; 
	AK2_SnsPwrDiagStatus_e SnsPwrSts = PWR_PIN_NORMAL;
	SnsPowerDiagIdx_e SnsPwrIdx = FrontSnsGroupPwrDiagIdx;
		
	AK2_ReadPwrManage_SnsPwrDiagStatus(FrontSnsGroupPwrDiagIdx,&SnsPwrSts);

	
	for(SnsIDIdx = SNS_FL_S;SnsIDIdx < SNSNum; SnsIDIdx++)
	{	
		DSIMasterID = SnsAddCfg[SnsIDIdx].DSIMasterID;
    	DSIChlID = SnsAddCfg[SnsIDIdx].DSIChlSEL - 1U;
    	SlotID = SnsAddCfg[SnsIDIdx].cSnsPhysAddr - 1U;

		if((SNS_FL == SnsIDIdx) || (SNS_FML == SnsIDIdx) || (SNS_FMR == SnsIDIdx) || (SNS_FR == SnsIDIdx))
		{
			SnsPwrIdx = FrontSnsGroupPwrDiagIdx;
		}
		else if((SNS_RL == SnsIDIdx) || (SNS_RML == SnsIDIdx) || (SNS_RMR == SnsIDIdx) || (SNS_RR == SnsIDIdx))
		{
			SnsPwrIdx = RearSnsGroupPwrDiagIdx;
		}
		else
		{
			SnsPwrIdx = SideSnsGroupPwrDiagIdx;
		}
		
		AK2_ReadPwrManage_SnsPwrDiagStatus(SnsPwrIdx,&SnsPwrSts);
		DsiPinSts = GetDSIPinSts(DSIMasterID,DSIChlID);

		if(DSIPinShortToGnd == DsiPinSts)
		{
			SnsErrInfo[SnsIDIdx].eErrType = DSIShortToGnd;
		}
		else if(DSIPinShortToPwr == DsiPinSts)
		{
			SnsErrInfo[SnsIDIdx].eErrType = DSIShortToPwr;
		}
		else if(PWR_PIN_SHORT_TO_GND == SnsPwrSts)
		{
			//SnsErrInfo[SnsIDIdx].eErrType = PwrPinShortToGnd;
		}
		else if(PWR_PIN_SHORT_TO_PWR == SnsPwrSts)
		{
			//SnsErrInfo[SnsIDIdx].eErrType = PwrPinShortToPwr;
		}
		else
		{}

		if(SnsFaultNone == SnsErrInfo[SnsIDIdx].eErrType)
		{
			SnsErrInfo[SnsIDIdx].eErrFlag = FALSE;
		}
		else
		{	
			SnsErrInfo[SnsIDIdx].eErrFlag = TRUE;
		}
	}

}

void SnsDiag_ReadSnsErrSignal(SnsErrInfo_t* LpSnsErr,SnsID_en LeSnsID)
{
    memcpy((void*)LpSnsErr,(const void*)&SnsErrInfo[LeSnsID],sizeof(SnsErrInfo_t));
}

bool SnsDiag_ReadFrontSnsGroupErrSts(void)
{
	bool flg = FALSE;
	
	if((TRUE == SnsErrInfo[SNS_FL_S].eErrFlag)
		||(TRUE == SnsErrInfo[SNS_FL].eErrFlag)
		||(TRUE == SnsErrInfo[SNS_FML].eErrFlag)
		||(TRUE == SnsErrInfo[SNS_FMR].eErrFlag)
		||(TRUE == SnsErrInfo[SNS_FR].eErrFlag)
		||(TRUE == SnsErrInfo[SNS_FR_S].eErrFlag))
	{
		flg = TRUE;
	}

	return flg;
}

bool SnsDiag_ReadRearSnsGroupErrSts(void)
{
	bool flg = FALSE;
	
	if((TRUE == SnsErrInfo[SNS_RR_S].eErrFlag)
		||(TRUE == SnsErrInfo[SNS_RR].eErrFlag)
		||(TRUE == SnsErrInfo[SNS_RMR].eErrFlag)
		||(TRUE == SnsErrInfo[SNS_RML].eErrFlag)
		||(TRUE == SnsErrInfo[SNS_RL].eErrFlag)
		||(TRUE == SnsErrInfo[SNS_RL_S].eErrFlag))
	{
		flg = TRUE;
	}

	return flg;
}

bool SnsDiag_ReadSysSnsGroupErrSts(void)
{
	bool flg = FALSE;
	
	if(  ((TRUE == SnsErrInfo[SNS_FL_S].eErrFlag)
		   ||(TRUE == SnsErrInfo[SNS_FL].eErrFlag)
		   ||(TRUE == SnsErrInfo[SNS_FML].eErrFlag)
		   ||(TRUE == SnsErrInfo[SNS_FMR].eErrFlag)
		   ||(TRUE == SnsErrInfo[SNS_FR].eErrFlag)
		   ||(TRUE == SnsErrInfo[SNS_FR_S].eErrFlag)
		  )
		||((TRUE == SnsErrInfo[SNS_RR_S].eErrFlag)
			||(TRUE == SnsErrInfo[SNS_RR].eErrFlag)
			||(TRUE == SnsErrInfo[SNS_RMR].eErrFlag)
			||(TRUE == SnsErrInfo[SNS_RML].eErrFlag)
			||(TRUE == SnsErrInfo[SNS_RL].eErrFlag)
			||(TRUE == SnsErrInfo[SNS_RL_S].eErrFlag)
		   )
	  )
	{
		flg = TRUE;
	}

	return flg;
}

SnsErrCtrl_t* SnsDiag_GetSnsErrCtrl(void)
{
	return (SnsErrCtrl_t*)&SnsErrCtrl;
}

void SnsDiag_ClearSnsErrCtrl(void)
{
	SnsErrCtrl.SnsErrMask = 0;
	SnsErrCtrl.SnsErrHandle = SnsErrHandleNone;
}

void SnsDiag_ClearEepromErrFlg(SnsID_en SnsIDIdx)
{
	EepromErrFlg[SnsIDIdx] = 0;
}

uint8 SnsDiag_InterFail_Event1_Num(void)
{
	DSIMasterID_en DSIMasterID = DSIMaster0;
	DSIChlID_en DSIChlID = DSIChl1;
	uint8 SlotID = 0;
	DSIPinSts_en DsiPinSts = DSIPinNor; 
	uint8 returnVal = 0;
	static uint8 ErrShowCnt = 0;
	uint16 DMFlg = Elmos17SnsCtrl_GetDMFlg();
    uint16 ParamCfgFlg = Elmos17SnsCtrl_GetSnsParamCfgFlg();
	bool ParamCfgFail = FALSE; 
	uint8 IDIdx = 0;

	IDIdx = ErrShowCnt / 3;/*每个探头显示3个周期*/

	ErrShowCnt++;
	if(ErrShowCnt > 8)
	{
		ErrShowCnt = 0;
	}
	
	DSIMasterID = SnsAddCfg[SNS_FL_S+IDIdx].DSIMasterID;
    DSIChlID = SnsAddCfg[SNS_FL_S+IDIdx].DSIChlSEL - 1U;
    SlotID = SnsAddCfg[SNS_FL_S+IDIdx].cSnsPhysAddr - 1U;
	
	/*参数是否配置判断*/
	if((0 != (DMFlg & (0x0001 << (SNS_FL_S+IDIdx)))) && (0 == (ParamCfgFlg & (0x0001 << (SNS_FL_S+IDIdx)))))
	{
		ParamCfgFail = TRUE;
	}
	DsiPinSts = GetDSIPinSts(DSIMasterID,DSIChlID);

	if(DSIPinShortToPwr == DsiPinSts)
	{
		returnVal = 1 + IDIdx * 10;
	}
	else if(DSIPinShortToGnd == DsiPinSts)
	{
		returnVal = 2 + IDIdx * 10;
	}
	else if(TRUE == SnsNoSigFilCtrl[SNS_FL_S+IDIdx].Output)
	{
		returnVal = 3 + IDIdx * 10;
	}
	else if(TRUE == SnsSigInvalidFilCtrl[SNS_FL_S+IDIdx].Output)
	{
		returnVal = 4 + IDIdx * 10;
	}
	else if(TRUE == ImpFreqAbnorFlg[SNS_FL_S+IDIdx])
	{
		returnVal = 5 + IDIdx * 10;
	}
	else if(TRUE == RingFreqAbnorFlg[SNS_FL_S+IDIdx])
	{
		returnVal = 6 + IDIdx * 10;
	}
	else if(TRUE == SnsRingTimeAbnorFilCtrl[SNS_FL_S+IDIdx].Output)
	{
		returnVal = 7 + IDIdx * 10;
	}
	else if(TRUE == ParamCfgFail)
	{
		returnVal = 8 + IDIdx * 10;
	}
	else if(TRUE == InternalFailure[SNS_FL_S+IDIdx])
	{
		returnVal = 9 + IDIdx * 10;
	}
	else
	{}

	if((1 == GetDSIMasterErrSts())
		|| (eDSISPI_Abnormal == DSI_SPIWorkSts()))
	{
		returnVal = 63;
	}
		
	return returnVal; 
}

uint8 SnsDiag_InterFail_Event2_Num(void)
{
	DSIMasterID_en DSIMasterID = DSIMaster0;
	DSIChlID_en DSIChlID = DSIChl1;
	uint8 SlotID = 0;
	DSIPinSts_en DsiPinSts = DSIPinNor; 
	uint8 returnVal = 0;
	static uint8 ErrShowCnt = 0;
	uint16 DMFlg = Elmos17SnsCtrl_GetDMFlg();
    uint16 ParamCfgFlg = Elmos17SnsCtrl_GetSnsParamCfgFlg();
	bool ParamCfgFail = FALSE; 
	uint8 IDIdx = 0;

	IDIdx = ErrShowCnt / 3;/*每个探头显示3个周期*/

	ErrShowCnt++;
	if(ErrShowCnt > 8)
	{
		ErrShowCnt = 0;
	}
	
	DSIMasterID = SnsAddCfg[SNS_FMR+IDIdx].DSIMasterID;
    DSIChlID = SnsAddCfg[SNS_FMR+IDIdx].DSIChlSEL - 1U;
    SlotID = SnsAddCfg[SNS_FMR+IDIdx].cSnsPhysAddr - 1U;
	
	/*参数是否配置判断*/
	if((0 != (DMFlg & (0x0001 << (SNS_FMR+IDIdx)))) && (0 == (ParamCfgFlg & (0x0001 << (SNS_FMR+IDIdx)))))
	{
		ParamCfgFail = TRUE;
	}
	DsiPinSts = GetDSIPinSts(DSIMasterID,DSIChlID);

	if(DSIPinShortToPwr == DsiPinSts)
	{
		returnVal = 1 + IDIdx * 10;
	}
	else if(DSIPinShortToGnd == DsiPinSts)
	{
		returnVal = 2 + IDIdx * 10;
	}
	else if(TRUE == SnsNoSigFilCtrl[SNS_FMR+IDIdx].Output)
	{
		returnVal = 3 + IDIdx * 10;
	}
	else if(TRUE == SnsSigInvalidFilCtrl[SNS_FMR+IDIdx].Output)
	{
		returnVal = 4 + IDIdx * 10;
	}
	else if(TRUE == ImpFreqAbnorFlg[SNS_FMR+IDIdx])
	{
		returnVal = 5 + IDIdx * 10;
	}
	else if(TRUE == RingFreqAbnorFlg[SNS_FMR+IDIdx])
	{
		returnVal = 6 + IDIdx * 10;
	}
	else if(TRUE == SnsRingTimeAbnorFilCtrl[SNS_FMR+IDIdx].Output)
	{
		returnVal = 7 + IDIdx * 10;
	}
	else if(TRUE == ParamCfgFail)
	{
		returnVal = 8 + IDIdx * 10;
	}
	else if(TRUE == InternalFailure[SNS_FMR+IDIdx])
	{
		returnVal = 9 + IDIdx * 10;
	}
	else
	{}
		
	return returnVal; 
}

uint8 SnsDiag_InterFail_Event3_Num(void)
{
	DSIMasterID_en DSIMasterID = DSIMaster0;
	DSIChlID_en DSIChlID = DSIChl1;
	uint8 SlotID = 0;
	DSIPinSts_en DsiPinSts = DSIPinNor; 
	uint8 returnVal = 0;
	static uint8 ErrShowCnt = 0;
	uint16 DMFlg = Elmos17SnsCtrl_GetDMFlg();
    uint16 ParamCfgFlg = Elmos17SnsCtrl_GetSnsParamCfgFlg();
	bool ParamCfgFail = FALSE; 
	uint8 IDIdx = 0;

	IDIdx = ErrShowCnt / 3;/*每个探头显示3个周期*/

	ErrShowCnt++;
	if(ErrShowCnt > 8)
	{
		ErrShowCnt = 0;
	}
	
	DSIMasterID = SnsAddCfg[SNS_RR_S+IDIdx].DSIMasterID;
    DSIChlID = SnsAddCfg[SNS_RR_S+IDIdx].DSIChlSEL - 1U;
    SlotID = SnsAddCfg[SNS_RR_S+IDIdx].cSnsPhysAddr - 1U;
	
	/*参数是否配置判断*/
	if((0 != (DMFlg & (0x0001 << (SNS_RR_S+IDIdx)))) && (0 == (ParamCfgFlg & (0x0001 << (SNS_RR_S+IDIdx)))))
	{
		ParamCfgFail = TRUE;
	}
	DsiPinSts = GetDSIPinSts(DSIMasterID,DSIChlID);

	if(DSIPinShortToPwr == DsiPinSts)
	{
		returnVal = 1 + IDIdx * 10;
	}
	else if(DSIPinShortToGnd == DsiPinSts)
	{
		returnVal = 2 + IDIdx * 10;
	}
	else if(TRUE == SnsNoSigFilCtrl[SNS_RR_S+IDIdx].Output)
	{
		returnVal = 3 + IDIdx * 10;
	}
	else if(TRUE == SnsSigInvalidFilCtrl[SNS_RR_S+IDIdx].Output)
	{
		returnVal = 4 + IDIdx * 10;
	}
	else if(TRUE == ImpFreqAbnorFlg[SNS_RR_S+IDIdx])
	{
		returnVal = 5 + IDIdx * 10;
	}
	else if(TRUE == RingFreqAbnorFlg[SNS_RR_S+IDIdx])
	{
		returnVal = 6 + IDIdx * 10;
	}
	else if(TRUE == SnsRingTimeAbnorFilCtrl[SNS_RR_S+IDIdx].Output)
	{
		returnVal = 7 + IDIdx * 10;
	}
	else if(TRUE == ParamCfgFail)
	{
		returnVal = 8 + IDIdx * 10;
	}
	else if(TRUE == InternalFailure[SNS_RR_S+IDIdx])
	{
		returnVal = 9 + IDIdx * 10;
	}
	else
	{}
		
	return returnVal; 
}

uint8 SnsDiag_InterFail_Event4_Num(void)
{
	DSIMasterID_en DSIMasterID = DSIMaster0;
	DSIChlID_en DSIChlID = DSIChl1;
	uint8 SlotID = 0;
	DSIPinSts_en DsiPinSts = DSIPinNor; 
	uint8 returnVal = 0;
	static uint8 ErrShowCnt = 0;
	uint16 DMFlg = Elmos17SnsCtrl_GetDMFlg();
    uint16 ParamCfgFlg = Elmos17SnsCtrl_GetSnsParamCfgFlg();
	bool ParamCfgFail = FALSE; 
	uint8 IDIdx = 0;

	IDIdx = ErrShowCnt / 3;/*每个探头显示3个周期*/

	ErrShowCnt++;
	if(ErrShowCnt > 8)
	{
		ErrShowCnt = 0;
	}
	
	DSIMasterID = SnsAddCfg[SNS_RML+IDIdx].DSIMasterID;
    DSIChlID = SnsAddCfg[SNS_RML+IDIdx].DSIChlSEL - 1U;
    SlotID = SnsAddCfg[SNS_RML+IDIdx].cSnsPhysAddr - 1U;
	
	/*参数是否配置判断*/
	if((0 != (DMFlg & (0x0001 << (SNS_RML+IDIdx)))) && (0 == (ParamCfgFlg & (0x0001 << (SNS_RML+IDIdx)))))
	{
		ParamCfgFail = TRUE;
	}
	DsiPinSts = GetDSIPinSts(DSIMasterID,DSIChlID);

	if(DSIPinShortToPwr == DsiPinSts)
	{
		returnVal = 1 + IDIdx * 10;
	}
	else if(DSIPinShortToGnd == DsiPinSts)
	{
		returnVal = 2 + IDIdx * 10;
	}
	else if(TRUE == SnsNoSigFilCtrl[SNS_RML+IDIdx].Output)
	{
		returnVal = 3 + IDIdx * 10;
	}
	else if(TRUE == SnsSigInvalidFilCtrl[SNS_RML+IDIdx].Output)
	{
		returnVal = 4 + IDIdx * 10;
	}
	else if(TRUE == ImpFreqAbnorFlg[SNS_RML+IDIdx])
	{
		returnVal = 5 + IDIdx * 10;
	}
	else if(TRUE == RingFreqAbnorFlg[SNS_RML+IDIdx])
	{
		returnVal = 6 + IDIdx * 10;
	}
	else if(TRUE == SnsRingTimeAbnorFilCtrl[SNS_RML+IDIdx].Output)
	{
		returnVal = 7 + IDIdx * 10;
	}
	else if(TRUE == ParamCfgFail)
	{
		returnVal = 8 + IDIdx * 10;
	}
	else if(TRUE == InternalFailure[SNS_RML+IDIdx])
	{
		returnVal = 9 + IDIdx * 10;
	}
	else
	{}
		
	return returnVal; 
}

void SnsDiag_SnsMonitorData(SnsMonitorDataType_t *pstrOut)
{	
	SnsMonitorDataType_t strSnsMonitorData;
	SnsSelfTestStatusMsg_str *pSnsSelfTestStatusMsg = NULL;
	SnsID_en eIDIdx = 0;
	DSIMasterID_en DSIMasterID = DSIMaster0;
	DSIChlID_en DSIChlID = DSIChl1;
	uint8 SlotID = 0;
	static uint8 u8CycIdx = 0;

	/*snsIdx1*/
	eIDIdx = u8CycIdx * 3;
	DSIMasterID = SnsAddCfg[eIDIdx].DSIMasterID;
	DSIChlID = SnsAddCfg[eIDIdx].DSIChlSEL - 1U;
	SlotID = SnsAddCfg[eIDIdx].cSnsPhysAddr - 1U;

	pSnsSelfTestStatusMsg = GetSnsSelfTestStatusAddr(DSIMasterID,DSIChlID,SlotID);
	
	strSnsMonitorData.eSnsIdx1 = eIDIdx;
	strSnsMonitorData.u8ImpedanceIdx1 = pSnsSelfTestStatusMsg->Resverd2[0];
	strSnsMonitorData.u16RingTime1 = enuRTData[eIDIdx].RTM;
	strSnsMonitorData.u16RingFre1 = CalcRingFreq(SAMPLE_FREQUENCY,&enuRTData[eIDIdx]);

	/*snsIdx2*/
	eIDIdx = u8CycIdx * 3 + 1;
	DSIMasterID = SnsAddCfg[eIDIdx].DSIMasterID;
	DSIChlID = SnsAddCfg[eIDIdx].DSIChlSEL - 1U;
	SlotID = SnsAddCfg[eIDIdx].cSnsPhysAddr - 1U;

	pSnsSelfTestStatusMsg = GetSnsSelfTestStatusAddr(DSIMasterID,DSIChlID,SlotID);
	
	strSnsMonitorData.eSnsIdx2 = eIDIdx;
	strSnsMonitorData.u8ImpedanceIdx2 = pSnsSelfTestStatusMsg->Resverd2[0];
	strSnsMonitorData.u16RingTime2 = enuRTData[eIDIdx].RTM;
	strSnsMonitorData.u16RingFre2 = CalcRingFreq(SAMPLE_FREQUENCY,&enuRTData[eIDIdx]);

	/*snsIdx3*/
	eIDIdx = u8CycIdx * 3 + 2;
	DSIMasterID = SnsAddCfg[eIDIdx].DSIMasterID;
	DSIChlID = SnsAddCfg[eIDIdx].DSIChlSEL - 1U;
	SlotID = SnsAddCfg[eIDIdx].cSnsPhysAddr - 1U;

	pSnsSelfTestStatusMsg = GetSnsSelfTestStatusAddr(DSIMasterID,DSIChlID,SlotID);
	
	strSnsMonitorData.eSnsIdx3 = eIDIdx;
	strSnsMonitorData.u8ImpedanceIdx3 = pSnsSelfTestStatusMsg->Resverd2[0];
	strSnsMonitorData.u16RingTime3 = enuRTData[eIDIdx].RTM;
	strSnsMonitorData.u16RingFre3 = CalcRingFreq(SAMPLE_FREQUENCY,&enuRTData[eIDIdx]);

	u8CycIdx++;
	if(u8CycIdx > 3)
	{
		u8CycIdx = 0;
	}

	if(NULL != pstrOut)
	{
		memcpy((void *)pstrOut,(const void*)&strSnsMonitorData,sizeof(SnsMonitorDataType_t));
	}
}

uint8 SnsDiag_VolAbnormaFlg(void)
{
	uint8 returnVal = 0;
	
 	/*FrontSnsGroupPwrAdcIdx(1)
   	  RearSnsGroupPwrAdcIdx(2) 
      SideSnsGroupPwrAdcIdx(3)*/
	   
    if((ReadSnsPwrADVal(1) > AD_CALIBRATION_13V) || (ReadSnsPwrADVal(1) < AD_CALIBRATION_11V))
    {
		returnVal |= (uint16)1 << 0;
	}

	if((ReadSnsPwrADVal(2) > AD_CALIBRATION_13V) || (ReadSnsPwrADVal(2) < AD_CALIBRATION_11V))
	{
		returnVal |= (uint16)1 << 1;
	}

	if((ReadSnsPwrADVal(3) > AD_CALIBRATION_13V) || (ReadSnsPwrADVal(3) < AD_CALIBRATION_11V))
	{
		returnVal |= (uint16)1 << 2;
	}

	return returnVal;
}

