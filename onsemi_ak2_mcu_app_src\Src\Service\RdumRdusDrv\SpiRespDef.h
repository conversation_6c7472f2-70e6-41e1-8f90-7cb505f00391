/******************************************************************************
 * @file      SpiRespDef.h
 * @brief     SPI响应定义
 * <AUTHOR>
 * @date      2025-05-16
 * @note      
 *****************************************************************************/

#ifndef __SPI_RESP_DEF_H__
#define __SPI_RESP_DEF_H__
/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "types.h"
#include "SpiPayloadDef.h"


/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/
/* SPI接收器状态位定义 */
typedef union
{
    uint8 Value;
    struct
    {
        uint8 CRM_RCVD : 1;          /* [0] 对应通道的CRM_RCVD寄存器值 */
        uint8 TIMER_INT : 1;         /* [1] 对应通道的定时器已过期标志 */
        uint8 BURST_DONE : 1;        /* [2] 对应通道的BURST_DONE寄存器值 */
        uint8 HW_ERR : 1;            /* [3] 仅与对应通道相关的硬件错误标志的OR */
        uint8 RX_TO : 1;             /* [4] 对应通道的RX_TO寄存器值 */
        uint8 FRAME_TO : 1;          /* [5] 对应通道的FRAME_TO寄存器值 */
        uint8 SYMBOL_ERR : 1;        /* [6] 对应通道的SYMBOL_ERR寄存器值 */
        uint8 BUS_AVAIL : 1;         /* [7] 对应通道的BUS_AVAIL寄存器值 */
    } Bits;
} SpiRcvrStatus_t;

/* 非读操作响应定义 */
typedef union
{
    uint8 RespData[7];
    struct
    {
        uint8 BRC_RECEIVED_CHA;      /* [55:48] 通道A的BRC_RECEIVED寄存器值 */
        uint8 BRC_RECEIVED_CHB;      /* [47:40] 通道B的BRC_RECEIVED寄存器值 */

        uint8 Reserved : 1;          /* [32] 保留 */
        uint8 KAC_SLAVE : 2;         /* [34:33] 传出SPI帧的KAC值 */
        uint8 KAC_MASTER : 2;        /* [36:35] 前一个传入SPI帧的KAC值 */
        uint8 HW_ERR : 1;            /* [37] 两个通信通道共有的硬件错误标志的逻辑OR */
        uint8 SPIERR : 1;            /* [38] SPIERR标志的值 */
        uint8 RETRAN_DONE : 1;       /* [39] RETRAN_DONE或EXTRA_BRC_DONE */
        
        uint8 CLKIN_CNT;             /* [31:24] CLKIN_CNT寄存器的值 */
        SpiRcvrStatus_t SPI_RCVR_STATUS_CHA; /* [23:16] 通道CHA的状态位值 */
        SpiRcvrStatus_t SPI_RCVR_STATUS_CHB; /* [15:8] 通道CHB的状态位值 */
        uint8 Crc;                   /* [7:0] CRC8-C2校验 */
    } Bit;
} SpiNonReadResp_t;

/* 读操作响应定义 */
typedef union
{
    uint8 RespData[7];
    struct
    {
        uint8 BRC_RECEIVED;          /* [55:48] 由前一个SPI命令选择的通道的BRC_RECEIVED寄存器值 */

        uint8 KAC_SLAVE : 2;         /* [41:40] 传出SPI帧的KAC值 */
        uint8 KAC_MASTER : 2;        /* [43:42] 前一个传入SPI帧的KAC值 */
        uint8 HW_ERR : 1;            /* [44] HW_ERR寄存器的值 */
        uint8 SPIERR : 1;            /* [45] SPIERR标志的值 */
        uint8 TIMER_INT : 1;         /* [46] 由前一个SPI命令选择的通道的定时器已过期标志 */
        uint8 CRM_RCVD : 1;          /* [47] 由前一个SPI命令选择的通道的CRM_RCVD寄存器值 */
        
        SpiPayloadData_t Data;       /* [39:8] 从内存读取的数据 */
        uint8 Crc;                   /* [7:0] CRC8-C2校验 */
    } Bit;
} SpiReadResp_t;



/******************************************************************************
 * @Const Declaration
 *****************************************************************************/



/******************************************************************************
 * @Variable Declaration
 *****************************************************************************/



/******************************************************************************
 * @Function Declaration
 *****************************************************************************/
/* 解析非读操作响应 */
void SpiRespDef_ParseNonReadResp(uint8 *RespData, SpiNonReadResp_t *NonReadResp);

/* 解析读操作响应 */
void SpiRespDef_ParseReadResp(uint8 *RespData, SpiReadResp_t *ReadResp);



#endif /* __SPI_RESP_DEF_H__ */
