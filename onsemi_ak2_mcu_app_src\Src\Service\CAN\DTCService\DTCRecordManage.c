/**********************************************************************
*   版权所有    : 2017 深圳市豪恩汽车电子装备股份有限公司
*   项目名称    : JAC iEV7T AVM
*   M C U       : R5F10BBG
*   编译环境    : IAR
*   文件名称    : DTCRecordManage.c
*   其它说明    : 系统故障记录管理
*   当前版本    : V0.1
*   作    者  : 20460
*   完成日期    :   
*   内容摘要    :
*   修改记录1   :
*   修改日期    :
*   版 本 号 :
*   修 改 人 :
*   修改内容    :
*   修改记录2   :

***********************************************************************/
/**********************************************************************
* Includes 
***********************************************************************/
#include "DTCRecordManage.h"
#include "EELHal.h"
#include "DTC_Cfg.h"
#include "DID.h"
#include "CAN_COM.h"
#include "PowerSingalManage.h"
#include "CAN_IL.h"
//#include "ApaCalCarCoor.h"
/********************************************************************** 
* Global objects 
***********************************************************************/

/**********************************************************************
*   函数名称:   InitDTCStatus
*
*   功能描述:   系统第一次上电开始工作,初始化DTC状态位信息
*
*   输入参考:   无
*           
*   输出参数:   无
*
*   返 回 值:    无
*
*   其它说明:   无
*
*   修改日期        版本号       修改人       修改内容
*
**********************************************************************/
void InitDTCStatus(void)
{
    uint8 LcI;
    uint8 LcJ;

    for(LcI = 0;LcI < TOTAL_DTC_RECORD_NUMBER86; LcI++)/*只初始化需要记录的故障信息*/
    {                        
        for(LcJ = 0;LcJ < SNAPSHOT_BYTE;LcJ++)
        {
            GsDTCInfomationStr[LcI].GcSnapshotData[LcJ] = 0x00;  /*02组快照初始化为0*/
            GsDTCInfomationStr[LcI].GcSnapshotData_First[LcJ] = 0x00;/*01组快照初始化为0*/  
        }

        GsDTCInfomationStr[LcI].GcAgingCounter = 0x00;
        GsDTCInfomationStr[LcI].GcDTCOccurrenceCounter = 0x00;      
        GsDTCInfomationStr[LcI].DTCSTATUS.GcDTCStatus = 0x00;

        GcDTCDetectionStatus[LcI] = TESTINIT;
    }
    GcDTCDetectionInitStatusFlag = FALSE;
}
/**********************************************************************
*   函数名称:   IGNONDTCStatus
*
*   功能描述:   Ignition ON(KL15)
*
*   输入参考:   无
*           
*   输出参数:   无
*
*   返 回 值:    无
*
*   其它说明:   无
*
*   修改日期        版本号       修改人       修改内容
*
**********************************************************************/
void IGNONDTCStatus(void)/*每次上电初始化*/
{
    uint8 LcI;
    uint8 LcJ;
    for(LcI = 0;LcI< TOTAL_DTC_RECORD_NUMBER82; LcI++)
    {
        (void)EELReadData((uint8 *)&GsDTCInfomationStr[LcI], (DTC_RECORD_1_ADDRESS + LcI * ONE_DTC_STORGE_LEN), ONE_DTC_STORGE_LEN);
        GsDTCInfomationStrBak[LcI].DTCSTATUS = GsDTCInfomationStr[LcI].DTCSTATUS;
        GsDTCInfomationStrBak[LcI].GcAgingCounter = GsDTCInfomationStr[LcI].GcAgingCounter;
        GsDTCInfomationStrBak[LcI].GcDTCOccurrenceCounter = GsDTCInfomationStr[LcI].GcDTCOccurrenceCounter;
        for(LcJ = 0;LcJ < SNAPSHOT_BYTE;LcJ++)
        {
            GsDTCInfomationStrBak[LcI].GcSnapshotData[LcJ] = GsDTCInfomationStr[LcI].GcSnapshotData[LcJ];
            GsDTCInfomationStrBak[LcI].GcSnapshotData_First[LcJ] = GsDTCInfomationStr[LcI].GcSnapshotData_First[LcJ];
        }
        /* 清零当前故障标志 */
        GsDTCInfomationStr[LcI].DTCSTATUS.DTCSTATUSbits.b0 = 0;
    }

    LcI = DTC_RSC_SHORT_TO_BAT;
    (void)EELReadData((uint8*)&GsDTCInfomationStr[LcI], (DTC_RECORD_83_ADDRESS), ONE_DTC_STORGE_LEN);
    GsDTCInfomationStrBak[LcI].DTCSTATUS = GsDTCInfomationStr[LcI].DTCSTATUS;
    GsDTCInfomationStrBak[LcI].GcAgingCounter = GsDTCInfomationStr[LcI].GcAgingCounter;
    GsDTCInfomationStrBak[LcI].GcDTCOccurrenceCounter = GsDTCInfomationStr[LcI].GcDTCOccurrenceCounter;
    for (LcJ = 0;LcJ < SNAPSHOT_BYTE;LcJ++)
    {
        GsDTCInfomationStrBak[LcI].GcSnapshotData[LcJ] = GsDTCInfomationStr[LcI].GcSnapshotData[LcJ];
        GsDTCInfomationStrBak[LcI].GcSnapshotData_First[LcJ] = GsDTCInfomationStr[LcI].GcSnapshotData_First[LcJ];
    }
    /* 清零当前故障标志 */
    GsDTCInfomationStr[LcI].DTCSTATUS.DTCSTATUSbits.b0 = 0;

    LcI = DTC_RSC_SHORT_TO_GND;
    (void)EELReadData((uint8*)&GsDTCInfomationStr[LcI], (DTC_RECORD_84_ADDRESS), ONE_DTC_STORGE_LEN);
    GsDTCInfomationStrBak[LcI].DTCSTATUS = GsDTCInfomationStr[LcI].DTCSTATUS;
    GsDTCInfomationStrBak[LcI].GcAgingCounter = GsDTCInfomationStr[LcI].GcAgingCounter;
    GsDTCInfomationStrBak[LcI].GcDTCOccurrenceCounter = GsDTCInfomationStr[LcI].GcDTCOccurrenceCounter;
    for (LcJ = 0;LcJ < SNAPSHOT_BYTE;LcJ++)
    {
        GsDTCInfomationStrBak[LcI].GcSnapshotData[LcJ] = GsDTCInfomationStr[LcI].GcSnapshotData[LcJ];
        GsDTCInfomationStrBak[LcI].GcSnapshotData_First[LcJ] = GsDTCInfomationStr[LcI].GcSnapshotData_First[LcJ];
    }
    /* 清零当前故障标志 */
    GsDTCInfomationStr[LcI].DTCSTATUS.DTCSTATUSbits.b0 = 0;
    LcI = DTC_SSC_SHORT_TO_BAT;
    (void)EELReadData((uint8*)&GsDTCInfomationStr[LcI], (DTC_RECORD_85_ADDRESS), ONE_DTC_STORGE_LEN);
    GsDTCInfomationStrBak[LcI].DTCSTATUS = GsDTCInfomationStr[LcI].DTCSTATUS;
    GsDTCInfomationStrBak[LcI].GcAgingCounter = GsDTCInfomationStr[LcI].GcAgingCounter;
    GsDTCInfomationStrBak[LcI].GcDTCOccurrenceCounter = GsDTCInfomationStr[LcI].GcDTCOccurrenceCounter;
    for (LcJ = 0;LcJ < SNAPSHOT_BYTE;LcJ++)
    {
        GsDTCInfomationStrBak[LcI].GcSnapshotData[LcJ] = GsDTCInfomationStr[LcI].GcSnapshotData[LcJ];
        GsDTCInfomationStrBak[LcI].GcSnapshotData_First[LcJ] = GsDTCInfomationStr[LcI].GcSnapshotData_First[LcJ];
    }
    /* 清零当前故障标志 */
    GsDTCInfomationStr[LcI].DTCSTATUS.DTCSTATUSbits.b0 = 0;

    LcI = DTC_SSC_SHORT_TO_GND;
    (void)EELReadData((uint8*)&GsDTCInfomationStr[LcI], (DTC_RECORD_86_ADDRESS), ONE_DTC_STORGE_LEN);
    GsDTCInfomationStrBak[LcI].DTCSTATUS = GsDTCInfomationStr[LcI].DTCSTATUS;
    GsDTCInfomationStrBak[LcI].GcAgingCounter = GsDTCInfomationStr[LcI].GcAgingCounter;
    GsDTCInfomationStrBak[LcI].GcDTCOccurrenceCounter = GsDTCInfomationStr[LcI].GcDTCOccurrenceCounter;
    for (LcJ = 0;LcJ < SNAPSHOT_BYTE;LcJ++)
    {
        GsDTCInfomationStrBak[LcI].GcSnapshotData[LcJ] = GsDTCInfomationStr[LcI].GcSnapshotData[LcJ];
        GsDTCInfomationStrBak[LcI].GcSnapshotData_First[LcJ] = GsDTCInfomationStr[LcI].GcSnapshotData_First[LcJ];
    }
    /* 清零当前故障标志 */
    GsDTCInfomationStr[LcI].DTCSTATUS.DTCSTATUSbits.b0 = 0;
}

/*********************************************************************
* 函数名称:  SettingDTCStatus
*                                  
* 功能描述:  设置DTC的状态位     
*                  
* 入口参数:  uint8 LcDTC,uint8 LcTestResult;  LcDTC为序号，即DTC在DTC列表中的序号
*
* 输出参数:  无      
*          
* 其它说明: 一个周期测试完成调用此函数
*           GsDTCStatusFlag[LcDTCNum].DTCSTATUS.DTCSTATUSbits.b0   //TEST FAIL  该DTC最近的测试结果为失败
*           GsDTCStatusFlag[LcDTCNum].DTCSTATUS.DTCSTATUSbits.b1   //testFailedThisOperationCycle 当前监视循环报告该DTC处于故障状态
*           GsDTCStatusFlag[LcDTCNum].DTCSTATUS.DTCSTATUSbits.b2   //pendingDTC   DTC在当前或前一个监视循环处于故障状态            
*           GsDTCStatusFlag[LcDTCNum].DTCSTATUS.DTCSTATUSbits.b3   //confirmedDTC DTC在请求时经过确认
*           GsDTCStatusFlag[LcDTCNum].DTCSTATUS.DTCSTATUSbits.b4   //testNotCompletedSinceLastClear     自从上一次故障码清除后测试尚未完成
*           GsDTCStatusFlag[LcDTCNum].DTCSTATUS.DTCSTATUSbits.b5   //testFailedSinceLastClear 自从上一次故障码清除后测试至少失败一次
*           GsDTCStatusFlag[LcDTCNum].DTCSTATUS.DTCSTATUSbits.b6   //testNotCompletedThisOperationCycle 本监视循环测试未完成
*           GsDTCStatusFlag[LcDTCNum].DTCSTATUS.DTCSTATUSbits.b7   //warningIndicatorRequested与该DTC相关的报警指示灯的状态(仪表显示)
*           GW HAP 只支持 bit0&bit3
* 修改日期      版本号       修改人       修改内容    
* 
********************************************************************/
void DTCTestingResultManage(uint8 LcDTCNum,uint8 LcTestResult)
{
    uint8 LcWriteDTCFlg = INVALID;
    Car_SpeedType LwVehicleSpeedTem;
    Car_SpeedVildType LvCar_SpeedVild;
    uint32 LdwTotalDistanceTem;
    Car_GearType LvCar_Gear;
    LowVolPwrMdType LenuLowVolPwrMd = POWER_OFF;
    uint8 Lu8VoltageValue;
    LowVolPwrMdFlagType LenuLowVolPwrMdFlag = LOCAL_MODE;
    
    ReadSysSignal_Car_SpeedForSnapshotData(&LwVehicleSpeedTem);
    ReadSysSignal_Car_SpeedVild(&LvCar_SpeedVild);
    ReadCAN_AppSignal_Car_Gear(&LvCar_Gear);
    ReadCAN_AppSignal_LowVolPwrMdFlag(&LenuLowVolPwrMdFlag);
    ReadCAN_AppSignal_LowVolPwrMd(&LenuLowVolPwrMd);
    Lu8VoltageValue = ReadPwrMonitorSing_VoltageValue();

    if(Lu8VoltageValue < 30)
    {
        Lu8VoltageValue=30;
    }
    else
    {
        Lu8VoltageValue -=30;
    }

    LdwTotalDistanceTem = (uint32)GsIntputParaStruct.Bytes.GdwVehTotDistanceVal;
	LcWriteDTCFlg = INVALID;
    /*下电前无故障*/
    if((LcTestResult == TESTFAIL) && (GsDTCInfomationStr[LcDTCNum].DTCSTATUS.GcDTCStatus != 0x09))
    {
        if(GsDTCInfomationStr[LcDTCNum].DTCSTATUS.DTCSTATUSbits.b3 == 0)/*第一次产生故障*/
        {
            /*车速*/ 
            GsDTCInfomationStr[LcDTCNum].GcSnapshotData_First[0] = (uint8)(LwVehicleSpeedTem >> 8); 
            GsDTCInfomationStr[LcDTCNum].GcSnapshotData_First[1] = (uint8)(LwVehicleSpeedTem); 
            /*车速有效性*/
            GsDTCInfomationStr[LcDTCNum].GcSnapshotData_First[2] = (uint8)(LvCar_SpeedVild);  
            /*工作电压*/
            GsDTCInfomationStr[LcDTCNum].GcSnapshotData_First[3] = 0; 
            GsDTCInfomationStr[LcDTCNum].GcSnapshotData_First[4] = Lu8VoltageValue;
            /*电源模式*/

            GsDTCInfomationStr[LcDTCNum].GcSnapshotData_First[5] = (uint8)((LenuLowVolPwrMd&0x03)|(LenuLowVolPwrMdFlag << 2));
            /*总里程*/
            GsDTCInfomationStr[LcDTCNum].GcSnapshotData_First[6] = (uint8)(LdwTotalDistanceTem >> 16);
            GsDTCInfomationStr[LcDTCNum].GcSnapshotData_First[7] = (uint8)(LdwTotalDistanceTem >> 8);
            GsDTCInfomationStr[LcDTCNum].GcSnapshotData_First[8] = (uint8)(LdwTotalDistanceTem);
            /*时间*/
            GsDTCInfomationStr[LcDTCNum].GcSnapshotData_First[9]  = GsIntputParaStruct.Bytes.GsSettingTime.GcTimeset[0];
            GsDTCInfomationStr[LcDTCNum].GcSnapshotData_First[10] = GsIntputParaStruct.Bytes.GsSettingTime.GcTimeset[1];
            GsDTCInfomationStr[LcDTCNum].GcSnapshotData_First[11] = GsIntputParaStruct.Bytes.GsSettingTime.GcTimeset[2];
            GsDTCInfomationStr[LcDTCNum].GcSnapshotData_First[12] = GsIntputParaStruct.Bytes.GsSettingTime.GcTimeset[3];
            GsDTCInfomationStr[LcDTCNum].GcSnapshotData_First[13] = GsIntputParaStruct.Bytes.GsSettingTime.GcTimeset[4];
            GsDTCInfomationStr[LcDTCNum].GcSnapshotData_First[14] = GsIntputParaStruct.Bytes.GsSettingTime.GcTimeset[5];
            /*“TimeValidFlg/时间有效性标志”和“EastOrWestTimeZone/东/西时区"的两个信号和DBC上的不一致，需要按照快照数据的定义进行转换*/
            GsDTCInfomationStr[LcDTCNum].GcSnapshotData_First[15] = (GsIntputParaStruct.Bytes.GsSettingTime.Bits.TimeZoneNum << 4) | (GsIntputParaStruct.Bytes.GsSettingTime.Bits.TimeZone << 2) | (GsIntputParaStruct.Bytes.GsSettingTime.Bits.UTCTimeValidFlag); 
            /*逻辑档位*/
            GsDTCInfomationStr[LcDTCNum].GcSnapshotData_First[16] = (uint8)LvCar_Gear;

            if (LcDTCNum == DTC_Missing_CAL)
            {
            	DID_FEFF_Buff[0] = 0;//GeCaliParamValidSts;
            }
        }
        else/*非第一次产生故障*/
        {

        }

        /*车速*/ 
        GsDTCInfomationStr[LcDTCNum].GcSnapshotData[0] = (uint8)(LwVehicleSpeedTem >> 8); 
        GsDTCInfomationStr[LcDTCNum].GcSnapshotData[1] = (uint8)(LwVehicleSpeedTem); 
        /*车速有效性*/
        GsDTCInfomationStr[LcDTCNum].GcSnapshotData[2] = (uint8)(LvCar_SpeedVild);  
        /*工作电压*/
        GsDTCInfomationStr[LcDTCNum].GcSnapshotData[3] = 0; 
        GsDTCInfomationStr[LcDTCNum].GcSnapshotData[4] = Lu8VoltageValue;
        /*电源模式*/

        GsDTCInfomationStr[LcDTCNum].GcSnapshotData[5] = (uint8)((LenuLowVolPwrMd&0x03)|(LenuLowVolPwrMdFlag << 2));
        /*总里程*/
        GsDTCInfomationStr[LcDTCNum].GcSnapshotData[6] = (uint8)(LdwTotalDistanceTem >> 16);
        GsDTCInfomationStr[LcDTCNum].GcSnapshotData[7] = (uint8)(LdwTotalDistanceTem >> 8);
        GsDTCInfomationStr[LcDTCNum].GcSnapshotData[8] = (uint8)(LdwTotalDistanceTem);
        /*时间*/
        GsDTCInfomationStr[LcDTCNum].GcSnapshotData[9]  = GsIntputParaStruct.Bytes.GsSettingTime.GcTimeset[0];
        GsDTCInfomationStr[LcDTCNum].GcSnapshotData[10] = GsIntputParaStruct.Bytes.GsSettingTime.GcTimeset[1];
        GsDTCInfomationStr[LcDTCNum].GcSnapshotData[11] = GsIntputParaStruct.Bytes.GsSettingTime.GcTimeset[2];
        GsDTCInfomationStr[LcDTCNum].GcSnapshotData[12] = GsIntputParaStruct.Bytes.GsSettingTime.GcTimeset[3];
        GsDTCInfomationStr[LcDTCNum].GcSnapshotData[13] = GsIntputParaStruct.Bytes.GsSettingTime.GcTimeset[4];  
        GsDTCInfomationStr[LcDTCNum].GcSnapshotData[14] = GsIntputParaStruct.Bytes.GsSettingTime.GcTimeset[5];    
        /*“TimeValidFlg/时间有效性标志”和“EastOrWestTimeZone/东/西时区"的两个信号和DBC上的不一致，需要按照快照数据的定义进行转换*/
        GsDTCInfomationStr[LcDTCNum].GcSnapshotData[15] = (GsIntputParaStruct.Bytes.GsSettingTime.Bits.TimeZoneNum << 4) | (GsIntputParaStruct.Bytes.GsSettingTime.Bits.TimeZone << 2) | (GsIntputParaStruct.Bytes.GsSettingTime.Bits.UTCTimeValidFlag);  
        /*逻辑档位*/
        GsDTCInfomationStr[LcDTCNum].GcSnapshotData[16] = (uint8)LvCar_Gear;

        if (LcDTCNum == DTC_Missing_CAL)
        {
        	DID_FEFF_Buff[1] = 0;//GeCaliParamValidSts;
        }
        
        GcDTCAgingCntEnaFlg[LcDTCNum] = DISABLE;     /* 上电后有故障，老化计数器自加使能状态清除 */
        if(DTC_WDG_SA_UC_FAIL==LcDTCNum)/*看门狗故障为历史故障，不记录当前故障*/
        {
            GsDTCInfomationStr[LcDTCNum].DTCSTATUS.GcDTCStatus = 0x08;
        }
        else
        {
            GsDTCInfomationStr[LcDTCNum].DTCSTATUS.GcDTCStatus = 0x09;
        }

        GsDTCInfomationStr[LcDTCNum].GcAgingCounter = 0;
        if(GsDTCInfomationStr[LcDTCNum].GcDTCOccurrenceCounter < 254)
        {
            GsDTCInfomationStr[LcDTCNum].GcDTCOccurrenceCounter++;          
        }
        LcWriteDTCFlg = VALID;
        if (LcDTCNum == DTC_Missing_CAL)
        {
            //UserEepromWrite(DTC_ODO_FLAG_ADDRESS, (uint8*)&DID_FEFF_Buff[0], DTC_ODO_FLAG_EEPROM_LEN);
            (void)EELWriteDataImmediate(&DID_FEFF_Buff[0], DTC_ODO_FLAG_ADDRESS, DTC_ODO_FLAG_EEPROM_LEN);

        }

    }
    else if((LcTestResult == TESTPASS) &&
            ((GsDTCInfomationStr[LcDTCNum].DTCSTATUS.DTCSTATUSbits.b0 != 0) || (GcDTCAgingCntEnaFlg[LcDTCNum] == ENABLE)))
    {
        GsDTCInfomationStr[LcDTCNum].DTCSTATUS.DTCSTATUSbits.b0 = 0;     /* TEST FAIL  该DTC最近的测试结果没有失败*/
        GcDTCAgingCntEnaFlg[LcDTCNum] = DISABLE;     /* 上电后如果无故障，老化计数器加1 */


        if( GsDTCInfomationStr[LcDTCNum].DTCSTATUS.DTCSTATUSbits.b3 )      /* 存在历史故障，执行下面步骤 */
        {
            GsDTCInfomationStr[LcDTCNum].GcAgingCounter += 1;
            
            if(GsDTCInfomationStr[LcDTCNum].GcAgingCounter >= 40)          /*老化周期100 */
            {          
                GsDTCInfomationStr[LcDTCNum].DTCSTATUS.DTCSTATUSbits.b3 = 0;  
                GsDTCInfomationStr[LcDTCNum].GcAgingCounter = 0;
            }
            LcWriteDTCFlg = VALID;
        }
    }
    if(LcWriteDTCFlg == VALID)
    {
        SaveDTCInforToDataFlash(LcDTCNum);
    }


}
