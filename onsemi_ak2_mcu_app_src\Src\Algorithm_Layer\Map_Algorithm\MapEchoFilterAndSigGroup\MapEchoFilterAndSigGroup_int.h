/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * SnsEchoFilterAndSigGroup_int: 
 * Created on: 2023-07-11 10:29
 * Original designer: AntonyFang
 ******************************************************************************/

#ifndef MapEchoFilterAndSigGroup_int_H
#define MapEchoFilterAndSigGroup_int_H

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "MapEchoFilterAndSigGroup_type.h"
#include "SnsRawData_Type.h"


/******************************************************************************
 * Compiler option used to distinguish inclusion done by the owner
 *****************************************************************************/


/******************************************************************************
 * Inclusion of private header files
 *****************************************************************************/
 

/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
extern MapSigGroupDataCacheType GstrMapSigGroupDataCache[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];
extern MapSnsRealTimeDisType    GstrMapSnsRealTimeDis[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];
extern ParkingGuidenceDataType GstrParkingGuidenceData;
extern uint8 Gu8SnsBeCoveredFlag[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
void MapSigGroupDataPowerOnInit(void);
void MapEchoFilterAndSigGroupDataGet(PDCSnsGroupType LeGroup,PDCSnsChannelType LePDCSnsCh);
void ClearMapSigGroupDataUpdateFlag(PDCSnsGroupType LeGroup,PDCSnsChannelType LePDCSnsCh);
void MapSigGroupDataCacheInit(PDCSnsGroupType LeGroup);
void MapSigGroupDataCache_PDC_Sns_Init(PDCSnsGroupType LeGroup);
void MapSnsRealTimeDisInit(PDCSnsGroupType LeGroup);
void MapSnsRealTimeDis_PDC_Sns_Init(PDCSnsGroupType LeGroup);
void SnsCarCovrDataInit(void);

#endif /* end of SnsEchoFilterAndSigGroup_int_H */

