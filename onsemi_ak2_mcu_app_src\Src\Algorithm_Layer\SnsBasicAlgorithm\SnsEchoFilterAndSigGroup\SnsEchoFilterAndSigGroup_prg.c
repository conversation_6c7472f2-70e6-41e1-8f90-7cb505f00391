/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * SnsEchoFilterAndSigGroup_prg: 原始回波阈值过滤及信号组提取处理
 * Created on: 2023-07-11 10:28
 * Original designer: AntonyFang
 ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "SnsEchoFilterAndSigGroup_int.h"
#include "SnsRawData_cfg.h"
#include "TimerManage.h"
#include "SnsRawData_Int.h"
#include "SnsRawDataCalib.h"
#include "CAN_AppSignalManage.h"
#include "MapEchoFilterAndSigGroup_int.h"
#include "PAS_MAP_StateHandle.h"


/******************************************************************************/
/******************************************************************************/
/****************************** Private Definition ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Public Definition *****************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/****************************** Private Function ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/******************************* Public Function ******************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************************
 * 函数名称: SnsSigGroupDataClear
 * 
 * 功能描述: 信号组数据清零
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-07-11 13:49   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void SnsSigGroupDataClear(PDCSnsGroupType LeGroup,PDCSnsChannelType LePDCSnsCh,Sns_CycleType LenuCurIndex)
{
    uint8 i;
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LePDCSnsCh];

    LpstrSnsSigGroupDataCache->u8SigGroupUpdateFlag = 0;
    LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub = 0;
    LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].u32SysTime = 0;
    LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType = PDC_SNS_MEAS_IDLE;

    LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8SigGroupCnt = 0;
    LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8SigGroupCnt = 0;
    LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8SigGroupCnt = 0;

    for(i = 0; i < MAX_NUM_OF_SIG_GROUP; i++)
    {
        LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8GroupEchoCnt[i] = 0;
        LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[i] = SNS_INVALID_DIS;
        LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[i] = SNS_INVALID_HEIGHT;
        LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[i] = SNS_INVALID_DIS;
        LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[i] = SNS_INVALID_HEIGHT;
        LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[i] = SNS_INVALID_DIS;
        LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[i] = SNS_INVALID_HEIGHT;
        LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[i] = OBJ_NONE_TYPE;
        
        LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8GroupEchoCnt[i] = 0;
        LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[i] = SNS_INVALID_DIS;
        LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[i] = SNS_INVALID_HEIGHT;
        LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[i] = SNS_INVALID_DIS;
        LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[i] = SNS_INVALID_HEIGHT;
        LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[i] = SNS_INVALID_DIS;
        LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[i] = SNS_INVALID_HEIGHT;
        LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[i] = OBJ_NONE_TYPE;

        LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8GroupEchoCnt[i] = 0;
        LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[i] = SNS_INVALID_DIS;
        LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[i] = SNS_INVALID_HEIGHT;
        LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[i] = SNS_INVALID_DIS;
        LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[i] = SNS_INVALID_HEIGHT;
        LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[i] = SNS_INVALID_DIS;
        LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[i] = SNS_INVALID_HEIGHT;
        LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[i] = OBJ_NONE_TYPE;
    }
}


/******************************************************************************
 * 函数名称: SnsSigGroupDataCacheInit
 * 
 * 功能描述: 探头信号缓存数据初始化
 * 
 * 输入参数:LeGroup--探头分组
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-07-11 11:28   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDataCacheInit(PDCSnsGroupType LeGroup)
{
    PDCSnsChannelType LePDCSnsCh;
    for(LePDCSnsCh = PDC_SNS_CH_FLS_RLS;LePDCSnsCh < PDC_SNS_CH_NUM;LePDCSnsCh++)
    {
        GstrSnsSigGroupDataCache[LeGroup][LePDCSnsCh].enuCurIndex = SNS_CYCLE_2;      /* 初始化时Index设置最大值，后续更新时直接到SNS_CYCLE_0 */
        GstrSnsSigGroupDataCache[LeGroup][LePDCSnsCh].u8RecordCnt = 0;
        GstrSnsSigGroupDataCache[LeGroup][LePDCSnsCh].u8SigGroupUpdateFlag = INVALID;
        SnsSigGroupDataClear(LeGroup,LePDCSnsCh,SNS_CYCLE_0);
        SnsSigGroupDataClear(LeGroup,LePDCSnsCh,SNS_CYCLE_1);
        SnsSigGroupDataClear(LeGroup,LePDCSnsCh,SNS_CYCLE_2);
    }
}


/******************************************************************************
 * 函数名称: SnsSigGroupDataCache_PDC_Sns_Init
 * 
 * 功能描述: 清除PDC探头的信号组数据
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-07 10:47   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDataCache_PDC_Sns_Init(PDCSnsGroupType LeGroup)
{
    PDCSnsChannelType LePDCSnsCh;
    for(LePDCSnsCh = PDC_SNS_CH_FL_RL;LePDCSnsCh < PDC_SNS_CH_FRS_RRS;LePDCSnsCh++)
    {
        GstrSnsSigGroupDataCache[LeGroup][LePDCSnsCh].enuCurIndex = SNS_CYCLE_2;      /* 初始化时Index设置最大值，后续更新时直接到SNS_CYCLE_0 */
        GstrSnsSigGroupDataCache[LeGroup][LePDCSnsCh].u8RecordCnt = 0;
        GstrSnsSigGroupDataCache[LeGroup][LePDCSnsCh].u8SigGroupUpdateFlag = INVALID;
        SnsSigGroupDataClear(LeGroup,LePDCSnsCh,SNS_CYCLE_0);
        SnsSigGroupDataClear(LeGroup,LePDCSnsCh,SNS_CYCLE_1);
        SnsSigGroupDataClear(LeGroup,LePDCSnsCh,SNS_CYCLE_2);
    }
}



/******************************************************************************
 * 函数名称: SnsSigGroupDataPowerOnInit
 * 
 * 功能描述: 探头信号缓存数据上电初始化
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-07-11 13:39   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDataPowerOnInit(void)
{
    SnsSigGroupDataCacheInit(PDC_SNS_GROUP_FRONT);
    SnsSigGroupDataCacheInit(PDC_SNS_GROUP_REAR);
    PDCSnsDE_CE_DataInit(PDC_SNS_GROUP_FRONT);
    PDCSnsDE_CE_DataInit(PDC_SNS_GROUP_REAR);
}

/******************************************************************************
 * 函数名称: SetSnsSigGroupDataUpdateFlag
 * 
 * 功能描述: 置位回波信号组更新标志，用于侧雷达等模块调度使用
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-08-28 08:25   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SetSnsSigGroupDataUpdateFlag(PDCSnsGroupType LeGroup,PDCSnsChannelType LePDCSnsCh)
{
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;

    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LePDCSnsCh];
    LpstrSnsSigGroupDataCache->u8SigGroupUpdateFlag = 1;
}



/******************************************************************************
 * 函数名称: ClearSnsSigGroupDataUpdateFlag
 * 
 * 功能描述: 置位回波信号组更新标志，用于侧雷达等模块调度使用
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-08-28 08:26   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void ClearSnsSigGroupDataUpdateFlag(PDCSnsGroupType LeGroup,PDCSnsChannelType LePDCSnsCh)
{
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;

    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LePDCSnsCh];
    LpstrSnsSigGroupDataCache->u8SigGroupUpdateFlag = 0;
}

/******************************************************************************
 * 函数名称: PDCSnsDE_CE_DataInit
 * 
 * 功能描述: DE CE值初始化
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-07-12 13:56   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PDCSnsDE_CE_DataInit(PDCSnsGroupType LenuSnsGroup)
{
    uint8 i;
    for(i = PDC_SNS_CH_FLS_RLS; i < PDC_SNS_CH_NUM; i++)
    {
        GstrSnsDE_CE_Data[LenuSnsGroup][i].u16DE_Dis = SNS_INIT_DE_CE_DIS_TO_CAN;
        GstrSnsDE_CE_Data[LenuSnsGroup][i].u16Left_CE_Dis = SNS_INIT_DE_CE_DIS_TO_CAN;
        GstrSnsDE_CE_Data[LenuSnsGroup][i].u16Right_CE_Dis = SNS_INIT_DE_CE_DIS_TO_CAN;
		StdDEFilterCtrl[LenuSnsGroup][i].u8WriteIdx = 0;
		StdDEFilterCtrl[LenuSnsGroup][i].u16DataBuf[0] = SNS_MAX_DE_CE_DIS;
		StdDEFilterCtrl[LenuSnsGroup][i].u16DataBuf[1] = SNS_MAX_DE_CE_DIS;
		StdDEFilterCtrl[LenuSnsGroup][i].u16DataBuf[2] = SNS_MAX_DE_CE_DIS;
    }
}


/******************************************************************************
 * 函数名称: PDCSnsDE_CE_DataPDC_Sns_Init
 * 
 * 功能描述: PDC探头的DE CE初始化
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-07 10:51   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PDCSnsDE_CE_DataPDC_Sns_Init(PDCSnsGroupType LenuSnsGroup)
{
    uint8 i;
    for(i = PDC_SNS_CH_FL_RL; i < PDC_SNS_CH_FRS_RRS; i++)
    {
        GstrSnsDE_CE_Data[LenuSnsGroup][i].u16DE_Dis = SNS_INIT_DE_CE_DIS_TO_CAN;
        GstrSnsDE_CE_Data[LenuSnsGroup][i].u16Left_CE_Dis = SNS_INIT_DE_CE_DIS_TO_CAN;
        GstrSnsDE_CE_Data[LenuSnsGroup][i].u16Right_CE_Dis = SNS_INIT_DE_CE_DIS_TO_CAN;
    }
}


/******************************************************************************
 * 函数名称: UpdateDE_CE_Dis
 * 
 * 功能描述: 理想项目DE CE是原始数据经过感度表处理后，不需要进行跟踪、比对处理
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-03-31 11:25   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void UpdateDE_CE_Dis(PDCSnsGroupType LenuSnsGroup,PDCSnsChannelType LenuSnsCh)
{
    PDCSnsDE_CE_DataType *LpstrSnsDE_CE_Data;
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    Sns_CycleType LenuCurIndex;
    StdDEFilterCtrlType *LpStdDEFilterCtrl = NULL;
	uint16 u16MasterDis = SNS_MAX_DE_CE_DIS;

    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LenuSnsGroup][LenuSnsCh];
    LpstrSnsDE_CE_Data = &GstrSnsDE_CE_Data[LenuSnsGroup][LenuSnsCh];
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
	LpStdDEFilterCtrl = &StdDEFilterCtrl[LenuSnsGroup][LenuSnsCh];

	u16MasterDis = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[0];
		
	/*定频且是中间的两个探头，压入缓存*/
	if((PDC_SNS_MEAS_STD == LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType)
		&& ((PDC_SNS_CH_FML_RML == LenuSnsCh) || (PDC_SNS_CH_FMR_RMR == LenuSnsCh)))
	{
		if(LpStdDEFilterCtrl->u8WriteIdx >= STD_DE_FILTER_NUM)
		{
			LpStdDEFilterCtrl->u8WriteIdx = 0;
		}
		LpStdDEFilterCtrl->u16DataBuf[LpStdDEFilterCtrl->u8WriteIdx] = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[0];
		LpStdDEFilterCtrl->u8WriteIdx++;

		/*同频且距离小于50cm,连续三帧定频小于50cm才输出*/
		if((LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[0] < CLOSE_RANGE_STD_DE_FILTER_START_DIS)
			&& (GstrParkingGuidenceData.u8SameFreqNoiseFlag[LenuSnsGroup]))
		{
			if((LpStdDEFilterCtrl->u16DataBuf[0] >= CLOSE_RANGE_STD_DE_FILTER_START_DIS)
				|| (LpStdDEFilterCtrl->u16DataBuf[1] >= CLOSE_RANGE_STD_DE_FILTER_START_DIS)
				|| (LpStdDEFilterCtrl->u16DataBuf[2] >= CLOSE_RANGE_STD_DE_FILTER_START_DIS))
			{
				u16MasterDis = SNS_MAX_DE_CE_DIS;
			}
		}
	}
	
    if(u16MasterDis < SNS_MAX_DE_CE_DIS)
    {
        LpstrSnsDE_CE_Data->u16DE_Dis = u16MasterDis/10;
    }
    else
    {
        LpstrSnsDE_CE_Data->u16DE_Dis = SNS_INIT_DE_CE_DIS_TO_CAN;
    }

    if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[0] < SNS_MAX_DE_CE_DIS)
    {
        LpstrSnsDE_CE_Data->u16Left_CE_Dis = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[0]/10;
    }
    else
    {
        LpstrSnsDE_CE_Data->u16Left_CE_Dis = SNS_INIT_DE_CE_DIS_TO_CAN;
    }

    if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[0] < SNS_MAX_DE_CE_DIS)
    {
        LpstrSnsDE_CE_Data->u16Right_CE_Dis = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[0]/10;
    }
    else
    {
        LpstrSnsDE_CE_Data->u16Right_CE_Dis = SNS_INIT_DE_CE_DIS_TO_CAN;
    }
#if 0
    if((LenuSnsGroup == 0x01)&&(LenuSnsCh == 0x02))
    {
        printf("Time:%.3f,RML DE CE,%d,%d,%d\r\n",GfMessageTime,LpstrSnsDE_CE_Data->u16DE_Dis,LpstrSnsDE_CE_Data->u16Left_CE_Dis,LpstrSnsDE_CE_Data->u16Right_CE_Dis);
    }
#endif
}

/******************************************************************************
 * 函数名称: UpdateDE_CE_Dis_forDV
 * 
 * 功能描述: DV需要稳定的DE CE输出
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-03-31 11:25   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void UpdateDE_CE_Dis_forDV(PDCSnsGroupType LenuSnsGroup,PDCSnsChannelType LenuSnsCh)
{
    PDCSnsDE_CE_DataType *LpstrSnsDE_CE_Data;
	SnsSigGroupDisFollowType *LpstrSnsSigGroupDisFollow;
	
	LpstrSnsSigGroupDisFollow = &GstrSnsSigGroupDisFollow[LenuSnsGroup][LenuSnsCh];
    LpstrSnsDE_CE_Data = &GstrSnsDE_CE_Data[LenuSnsGroup][LenuSnsCh];

    
    if(LpstrSnsSigGroupDisFollow->MasDisData.u16StableOutputDis < SNS_MAX_DE_CE_DIS)
    {
        LpstrSnsDE_CE_Data->u16DE_Dis = LpstrSnsSigGroupDisFollow->MasDisData.u16StableOutputDis/10;
    }
    else
    {
        LpstrSnsDE_CE_Data->u16DE_Dis = SNS_INIT_DE_CE_DIS_TO_CAN;
    }

    if(LpstrSnsSigGroupDisFollow->LeftLisDisData.u16StableOutputDis < SNS_MAX_DE_CE_DIS)
    {
        LpstrSnsDE_CE_Data->u16Left_CE_Dis = LpstrSnsSigGroupDisFollow->LeftLisDisData.u16StableOutputDis/10;
    }
    else
    {
        LpstrSnsDE_CE_Data->u16Left_CE_Dis = SNS_INIT_DE_CE_DIS_TO_CAN;
    }

    if(LpstrSnsSigGroupDisFollow->RightLisDisData.u16StableOutputDis < SNS_MAX_DE_CE_DIS)
    {
        LpstrSnsDE_CE_Data->u16Right_CE_Dis = LpstrSnsSigGroupDisFollow->RightLisDisData.u16StableOutputDis/10;
    }
    else
    {
        LpstrSnsDE_CE_Data->u16Right_CE_Dis = SNS_INIT_DE_CE_DIS_TO_CAN;
    }
#if 0
    if((LenuSnsGroup == 0x01)&&(LenuSnsCh == 0x02))
    {
        printf("Time:%.3f,RML DE CE,%d,%d,%d\r\n",GfMessageTime,LpstrSnsDE_CE_Data->u16DE_Dis,LpstrSnsDE_CE_Data->u16Left_CE_Dis,LpstrSnsDE_CE_Data->u16Right_CE_Dis);
    }
#endif
}

/******************************************************************************
 * 函数名称: SnsEchoFilterAndSigGroupDataGet
 * 
 * 功能描述: 对原始数据做初级滤波(过滤噪点)，并提取原始信号组(即对能够组成第一第二信号的信号组合并在一起)
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-07-11 13:50   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsEchoFilterAndSigGroupDataGet(PDCSnsGroupType LeGroup,PDCSnsChannelType LePDCSnsCh)
#if 1
{
    uint8 i;
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    Sns_CycleType LenuCurIndex;
    float LfCarMoveSub = 0;
    PDCSnsRawDataType *LpStrPDCSnsRawData;
    uint8 Lu8MasterEchoCnt,Lu8ListenEchoCnt;
    uint16 Lu16MasterDis,Lu16MasterHeight,Lu16MasterDisSub;
    uint16 Lu16ListenDis,Lu16ListenSecondDis,Lu16ListenHeight,Lu16ListenDisSub;
    uint16 Lu16FirstSecondEchoSub;
    uint8 Lu8SigGroupCnt;
    uint8 Lu8TableIndex;
    uint8 Lu8TableHighIndex;
    const uint16 *Lpu16SnsMasEchoThresholdTable;
    const uint16 *Lpu16SnsLisEchoThresholdTable;
    const SnsCalibHeightType *LptrSnsJudgeObjTypeThresholdTable;
    uint8 Lu8FindValidObjFlag = 0;
    uint16 Lu16ThresCompenset = 0;
    uint16 Lu16MasterThresCompenset = 0;    /* 主发探头的阈值补偿，主要考虑车速较高时，适当降低阈值，以保证高速的探测稳定性 */
    uint16 Lu16SnsSecondThanFirstHeight;
    uint16 Lu16FirstDisToBackupSub,Lu16SecondDisToBackupSub;
    uint8 Lu8FirstDisIsNoise;
    uint16 Lu16NoiseHeight = 1000;
    uint8 Lu8ChirpModeFlag = 0;
    uint16 Lu16FirstSecondHeightSub = 0;

    LpStrPDCSnsRawData = &GstrPDCSnsRawData[LeGroup];
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LePDCSnsCh];

    if((LePDCSnsCh == PDC_SNS_CH_FLS_RLS)||(LePDCSnsCh == PDC_SNS_CH_FRS_RRS))
    {
        if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
        {
            Lpu16SnsMasEchoThresholdTable = Gpu16SnsMasThresholdTable[LeGroup][0];
        }
        else
        {
            Lpu16SnsMasEchoThresholdTable = Gpu16SnsMasThresholdTable[LeGroup][1];
        }
    }
    else if((LePDCSnsCh == PDC_SNS_CH_FL_RL)||(LePDCSnsCh == PDC_SNS_CH_FR_RR))
    {
        if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
        {
            Lpu16SnsMasEchoThresholdTable = Gpu16SnsMasThresholdTable[LeGroup][2];
        }
        else
        {
            Lpu16SnsMasEchoThresholdTable = Gpu16SnsMasThresholdTable[LeGroup][3];
        }
    }
    else
    {
        if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
        {
            Lpu16SnsMasEchoThresholdTable = Gpu16SnsMasThresholdTable[LeGroup][4];
        }
        else
        {
            Lpu16SnsMasEchoThresholdTable = Gpu16SnsMasThresholdTable[LeGroup][5];
        }
    }

    if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
    {
        Lu8ChirpModeFlag = 0;
        LptrSnsJudgeObjTypeThresholdTable = &GStrObjJudgeStandardThresholdTableInRAM[0];
        Lu16SnsSecondThanFirstHeight = SNS_STD_SECOND_THAN_FIRST_HEIGHT;
        if(Gu16CarSpdForSnsUse < 50)
        {
            Lu16MasterThresCompenset = 0;
        }
        else if(Gu16CarSpdForSnsUse < 100)
        {
            Lu16MasterThresCompenset = SNS_STD_MASTER_CMP_THRES_1KM;
        }
        else if(Gu16CarSpdForSnsUse < 300)
        {
            Lu16MasterThresCompenset = SNS_STD_MASTER_CMP_THRES_3KM;
        }
        else if(Gu16CarSpdForSnsUse < 500)
        {
            Lu16MasterThresCompenset = SNS_STD_MASTER_CMP_THRES_5KM;
        }
        else if(Gu16CarSpdForSnsUse < 700)
        {
            Lu16MasterThresCompenset = SNS_STD_MASTER_CMP_THRES_7KM;
        }
        else
        {
            Lu16MasterThresCompenset = SNS_STD_MASTER_CMP_THRES_MORE_7KM;
        }
    }
    else
    {
        Lu8ChirpModeFlag = 1;
        LptrSnsJudgeObjTypeThresholdTable = &GStrObjJudgeChirpThresholdTableInRAM[0];
        Lu16SnsSecondThanFirstHeight = SNS_CHIRP_SECOND_THAN_FIRST_HEIGHT;
        if(Gu16CarSpdForSnsUse < 50)
        {
            Lu16MasterThresCompenset = 0;
        }
        else if(Gu16CarSpdForSnsUse < 100)
        {
            Lu16MasterThresCompenset = SNS_CHIRP_MASTER_CMP_THRES_1KM;
        }
        else if(Gu16CarSpdForSnsUse < 300)
        {
            Lu16MasterThresCompenset = SNS_CHIRP_MASTER_CMP_THRES_3KM;
        }
        else if(Gu16CarSpdForSnsUse < 500)
        {
            Lu16MasterThresCompenset = SNS_CHIRP_MASTER_CMP_THRES_5KM;
        }
        else if(Gu16CarSpdForSnsUse < 700)
        {
            Lu16MasterThresCompenset = SNS_CHIRP_MASTER_CMP_THRES_7KM;
        }
        else
        {
            Lu16MasterThresCompenset = SNS_CHIRP_MASTER_CMP_THRES_MORE_7KM;
        }
    }



    /* 更新当前索引值 */
    LpstrSnsSigGroupDataCache->enuCurIndex++;
    if(LpstrSnsSigGroupDataCache->enuCurIndex >= SNS_CYCLE_NUM)
    {
        LpstrSnsSigGroupDataCache->enuCurIndex = SNS_CYCLE_0;
    }
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    /*开始索引技术，方便后续累计到3个周期后进行数据跟踪处理*/
    if(LpstrSnsSigGroupDataCache->u8RecordCnt < SNS_CYCLE_NUM)
    {
        LpstrSnsSigGroupDataCache->u8RecordCnt++;
    }

    SnsSigGroupDataClear(LeGroup,LePDCSnsCh,LenuCurIndex);

    /* 获取上一轮到本轮探头探测车辆移动的距离及对应的系统时间 */
    if(LpstrSnsSigGroupDataCache->u8RecordCnt > 1)
    {
        LfCarMoveSub = GstrSnsCarMovSts[LeGroup][LePDCSnsCh].fSnsCarMovDisSub;
    }
    if(LeGroup == PDC_SNS_GROUP_FRONT)
    {
        /** @brief: 对于前探头，车子前进是靠近障碍物 */
        LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub = -LfCarMoveSub;
    }
    else
    {
        LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub = LfCarMoveSub;
    }
    //LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].u32SysTime = GetSystemTimeCnt_Ms();
    LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].u32SysTime = 0;
    LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eCarDir = GstrSnsCarMovSts[LeGroup][LePDCSnsCh].eCarDir;
    LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType = LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh];

    /* Step1:通过阈值过滤和提取主发回波信号组 */
    Lu8MasterEchoCnt = LpStrPDCSnsRawData->u8MasterEchoCnt[LePDCSnsCh];
    Lu8SigGroupCnt = 0;
    for(i = 0; i < Lu8MasterEchoCnt; i++)
    {
        /* 先判断是否有第二回波进行匹配，再进行查询滤波；避免把回波信号组的隐藏信息过滤掉 */
        if(i == 0)
        {
            if(LpStrPDCSnsRawData->u16NFD_Dis[LePDCSnsCh] < LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i])
            {
                Lu16MasterDis = LpStrPDCSnsRawData->u16NFD_Dis[LePDCSnsCh];
                Lu16MasterHeight = SNS_NFD_ECHO_HEIGHT;
            }
            else
            {
                Lu16MasterDis = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i];
                Lu16MasterHeight = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i];
            }
        }
        else
        {
            Lu16MasterDis = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i];
            Lu16MasterHeight = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i];
        }
        
        if(Lu16MasterDis < SNS_TABLE_MAX_DIS)
        {
            Lu8TableIndex = Lu16MasterDis/SNS_TABLE_STEP;
            /* 针对10~20cm需要进一步细化，用于解决余震分叉的问题；即10~15cm用[0,10]的阈值；15~20用[10,20]的阈值 */
            if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
            {
                if(Lu8TableIndex == SNS_DIS_20cm)
                {
                    if(Lu16MasterDis < 150)
                    {
                        Lu8TableIndex = SNS_DIS_10cm;
                    }
                }
            }
            Lu8TableHighIndex = Lu16MasterDis/SNS_HIGH_TABLE_STEP;
        }
        else
        {
            Lu8TableIndex = SNS_DIS_400cm;
            Lu8TableHighIndex = SNS_DIS_HIGH_400cm;
        }

        if(Lu8TableIndex < SNS_DIS_100cm)
        {
            Lu16NoiseHeight = 1500;
        }
        else if(Lu8TableIndex < SNS_DIS_150cm)
        {
            Lu16NoiseHeight = 1200;
        }
        else if(Lu8TableIndex < SNS_DIS_200cm)
        {
            Lu16NoiseHeight = 1100;
        }
        else 
        {
            Lu16NoiseHeight = 900;
        }

        /* 第一回波必须大于PVC的阈值，否则直接当做噪点过滤掉--2023-12-01 */
        if(Lu16MasterHeight < (Lpu16SnsMasEchoThresholdTable[Lu8TableIndex]-Lu16MasterThresCompenset))
        {
            continue;
        }

        if(Lu16MasterDis < 2000)
        {
            Lu16FirstSecondEchoSub = SNS_MASTER_SECOND_ECHO_DIS;
        }
        else
        {
            Lu16FirstSecondEchoSub = SNS_MASTER_SECOND_ECHO_DIS2;
        }
        Lu8FindValidObjFlag = 0;
        if((i+1) < Lu8MasterEchoCnt)
        {
            Lu16MasterDisSub = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1] - Lu16MasterDis;
            if(Lu16MasterDisSub < Lu16FirstSecondEchoSub)
            {
                /* 此处存在风险，即一个大墙的20cm附近存在小的障碍物，可能存在距离不对问题,因此仅针对70cm以外的使用该策略 */
                if(((Lu16MasterHeight+Lu16SnsSecondThanFirstHeight) < LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1])&&\
                    (Lu16MasterDis > 700))
                {
                    if(LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1] < SNS_TABLE_MAX_DIS)
                    {
                        Lu8TableIndex = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1]/SNS_TABLE_STEP;
                        Lu8TableHighIndex = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1]/SNS_HIGH_TABLE_STEP;
                    }
                    else
                    {
                        Lu8TableIndex = SNS_DIS_400cm;
                        Lu8TableHighIndex = SNS_DIS_HIGH_400cm;
                    }
                    
                    if(LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1] > (Lpu16SnsMasEchoThresholdTable[Lu8TableIndex]-Lu16MasterThresCompenset))
                    {
                        /* 第一第二回波构成信号组，且第二回波大于第一回波的处理；
                           由于探头扫频模式下的灵敏度提升，导致较多噪点和正常回波进行误配属，需要在此处进行过滤处理*/
                        if(Lu8ChirpModeFlag)
                        {
                            Lu8FirstDisIsNoise = 0;
                            Lu16FirstSecondHeightSub = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1] - Lu16MasterHeight;
                            if(Lu16FirstSecondHeightSub > 500)
                            {
                                if(Lu16MasterHeight < Lu16NoiseHeight)
                                {
                                    Lu8FirstDisIsNoise = 1;
                                }
                            }
                            if(Lu8FirstDisIsNoise == 0)
                            {
                                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16MasterDis - SNS_MASTER_COMPEN_DIS;
                                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1] - SNS_MASTER_COMPEN_DIS;
                                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1];
                                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1] - SNS_MASTER_COMPEN_DIS;
                                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1];
                                Lu8FindValidObjFlag = 1;
                                i++;
                            }
                            else
                            {
                                /* 不做处理，继续下一个循环 */
                            }
                        }
                        else
                        {
                            /* 定频使用第一回距离作为实际使用的距离更接近真实效果 */
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16MasterDis - SNS_MASTER_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1] - SNS_MASTER_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1];
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16MasterDis - SNS_MASTER_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1];
                            Lu8FindValidObjFlag = 1;
                            i++;
                        }
                    }
                }
                else
                {
                    /* 第一回波高度大于第二回波高度，不进行后续的查询，直接配属到信号组中 */
                    if(Lu16MasterHeight > (Lpu16SnsMasEchoThresholdTable[Lu8TableIndex]-Lu16MasterThresCompenset))
                    {
                        /* 添加对于第二回波有效性的判断，第二回波高度同样需要通过阈值表过滤 */
                        //if(LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1] > (Lu16MasterHeight/3))
                        if(1)
                        {
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16MasterDis - SNS_MASTER_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1] - SNS_MASTER_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1];
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16MasterDis - SNS_MASTER_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                        }
                        else
                        {
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16MasterDis - SNS_MASTER_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16MasterDis - SNS_MASTER_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                        }
                        Lu8FindValidObjFlag = 1;
                        i++;
                    }
                }
            }
            else
            {
                /* 第一、第二回波构不成信号组，直接将第一回波单独构建一个信号组 */
                if(Lu16MasterHeight > (Lpu16SnsMasEchoThresholdTable[Lu8TableIndex]-Lu16MasterThresCompenset))
                {
                    LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                    LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16MasterDis - SNS_MASTER_COMPEN_DIS;
                    LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                    LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16MasterDis - SNS_MASTER_COMPEN_DIS;
                    LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                    Lu8FindValidObjFlag = 1;
                }
            }
        }
        else
        {
            /* 无后续回波，和设定阈值对比后直接配属到一个信号组中 */
            if(Lu16MasterHeight > (Lpu16SnsMasEchoThresholdTable[Lu8TableIndex]-Lu16MasterThresCompenset))
            {
                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16MasterDis - SNS_MASTER_COMPEN_DIS;
                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16MasterDis - SNS_MASTER_COMPEN_DIS;
                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                Lu8FindValidObjFlag = 1;
            }
        }
        if(Lu8FindValidObjFlag)
        {
            if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] > LptrSnsJudgeObjTypeThresholdTable[Lu8TableHighIndex].u16BigWallHeight)
            {
                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt] = OBJ_BIGWALL_TYPE;
            }
            else
            {
                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt] = OBJ_PVC_PIPE_TYPE;
            }

            /* 只保留小障碍物后的大障碍物逻辑处理--2023-11-13 */
            if(Lu8SigGroupCnt > 0)
            {
                if((LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt]-5) < LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt-1])
                {
                    break;
                }
            }
            Lu8SigGroupCnt++;
        }
        if(Lu8SigGroupCnt == MAX_NUM_OF_SIG_GROUP)
        {
            break;
        }
    }
    LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8SigGroupCnt = Lu8SigGroupCnt;
    /* 保留备份值 */
    for(i = 0; i < MAX_NUM_OF_SIG_GROUP; i++)
    {
        LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstDisBackup[i] = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[i];
    }

    /* 侦听阈值获取 */
    if((LePDCSnsCh == PDC_SNS_CH_FLS_RLS)||(LePDCSnsCh == PDC_SNS_CH_FRS_RRS))
    {
        if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
        {
            Lpu16SnsLisEchoThresholdTable = Gpu16SnsLisThresholdTable[LeGroup][0];
        }
        else
        {
            Lpu16SnsLisEchoThresholdTable = Gpu16SnsLisThresholdTable[LeGroup][1];
        }
    }
    else if((LePDCSnsCh == PDC_SNS_CH_FL_RL)||(LePDCSnsCh == PDC_SNS_CH_FR_RR))
    {
        if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
        {
            Lpu16SnsLisEchoThresholdTable = Gpu16SnsLisThresholdTable[LeGroup][2];
        }
        else
        {
            Lpu16SnsLisEchoThresholdTable = Gpu16SnsLisThresholdTable[LeGroup][3];
        }
    }
    else
    {
        if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
        {
            Lpu16SnsLisEchoThresholdTable = Gpu16SnsLisThresholdTable[LeGroup][4];
        }
        else
        {
            Lpu16SnsLisEchoThresholdTable = Gpu16SnsLisThresholdTable[LeGroup][5];
        }
    }

    /* Step2:通过阈值过滤和提取左侦听回波信号组 */
    Lu16ThresCompenset = 0;
    Lu8ListenEchoCnt = LpStrPDCSnsRawData->u8LeftListenEchoCnt[LePDCSnsCh];
    Lu8SigGroupCnt = 0;
    for(i = 0; i < Lu8ListenEchoCnt; i++)
    {
        /* 先判断是否有第二回波进行匹配，再进行查询滤波；避免把回波信号组的隐藏信息过滤掉 */
        Lu16ListenDis = (LpStrPDCSnsRawData->u16LeftListenDis[LePDCSnsCh][i])>>1;
        Lu16ListenHeight = LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i];
        if((Lu16ListenDis > 1500)&&(Lu16ListenDis < 3600))
        {
            if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
            {
                Lu16ThresCompenset = SNS_STD_LISTEN_COMPEN_HEIGHT;
            }
            else
            {
                Lu16ThresCompenset = SNS_CHIRP_LISTEN_COMPEN_HEIGHT;
            }
        }
        else if(Lu16ListenDis < 900)
        {
            if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
            {
                Lu16ThresCompenset = SNS_STD_NEAR_LISTEN_COMPEN_HEIGHT;
            }
        }
        if(Lu16ListenDis < SNS_TABLE_MAX_DIS)
        {
            Lu8TableIndex = Lu16ListenDis/SNS_TABLE_STEP;
            Lu8TableHighIndex = Lu16ListenDis/SNS_HIGH_TABLE_STEP;
        }
        else
        {
            Lu8TableIndex = SNS_DIS_400cm;
            Lu8TableHighIndex = SNS_DIS_HIGH_400cm;
        }

        if(Lu16ListenDis < 2000)
        {
            Lu16FirstSecondEchoSub = SNS_LISTEN_SECOND_ECHO_DIS;
        }
        else
        {
            Lu16FirstSecondEchoSub = SNS_LISTEN_SECOND_ECHO_DIS2;
        }

        if(Lu8TableIndex < SNS_DIS_100cm)
        {
            Lu16NoiseHeight = 1500;
        }
        else if(Lu8TableIndex < SNS_DIS_150cm)
        {
            Lu16NoiseHeight = 1200;
        }
        else if(Lu8TableIndex < SNS_DIS_200cm)
        {
            Lu16NoiseHeight = 1100;
        }
        else 
        {
            Lu16NoiseHeight = 900;
        }

        /* 第一回波必须大于PVC的阈值，否则直接当做噪点过滤掉--2023-12-01 */
        if(Lu16ListenHeight < (Lpu16SnsLisEchoThresholdTable[Lu8TableIndex] - Lu16ThresCompenset))
        {
            continue;
        }
        
        Lu8FindValidObjFlag = 0;
        if((i+1) < Lu8ListenEchoCnt)
        {
            Lu16ListenSecondDis = (LpStrPDCSnsRawData->u16LeftListenDis[LePDCSnsCh][i+1])>>1;
            Lu16ListenDisSub = Lu16ListenSecondDis - Lu16ListenDis;
            if(Lu16ListenDisSub < Lu16FirstSecondEchoSub)
            {
                /* 此处存在风险，即一个大墙的20cm附近存在小的障碍物，可能存在距离不对问题,因此仅针对70cm以外的使用该策略 */
                if(((Lu16ListenHeight+Lu16SnsSecondThanFirstHeight) < LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1])&&\
                    (Lu16ListenDis > 700))
                {
                    if(Lu16ListenSecondDis < SNS_TABLE_MAX_DIS)
                    {
                        Lu8TableIndex = Lu16ListenSecondDis/SNS_TABLE_STEP;
                        Lu8TableHighIndex = Lu16ListenSecondDis/SNS_HIGH_TABLE_STEP;
                    }
                    else
                    {
                        Lu8TableIndex = SNS_DIS_400cm;
                        Lu8TableHighIndex = SNS_DIS_HIGH_400cm;
                    }
                    if(LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1] > (Lpu16SnsLisEchoThresholdTable[Lu8TableIndex] - Lu16ThresCompenset))
                    {   
                        /* 第一第二回波构成信号组，且第二回波大于第一回波的处理；
                           由于探头扫频模式下的灵敏度提升，导致较多噪点和正常回波进行误配属，需要在此处进行过滤处理*/
                        if(Lu8ChirpModeFlag)
                        {
                            Lu8FirstDisIsNoise = 0;
                            Lu16FirstSecondHeightSub = LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1] - Lu16ListenHeight;
                            if(Lu16FirstSecondHeightSub > 500)
                            {
                                if(Lu16ListenHeight < Lu16NoiseHeight)
                                {
                                    Lu8FirstDisIsNoise = 1;
                                }
                            }
                            
                            if(Lu8FirstDisIsNoise == 0)
                            {
                                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - SNS_LISTEN_COMPEN_DIS;
                                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1];
                                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - SNS_LISTEN_COMPEN_DIS;
                                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1];
                                Lu8FindValidObjFlag = 1;
                                i++;
                            }
                            else
                            {
                                /* 继续后续的循环 */
                            }
                        }
                        else
                        {
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - SNS_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1];
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1];
                            Lu8FindValidObjFlag = 1;
                            i++;
                        }
                    }
                }
                else
                {
                    if(Lu16ListenHeight > (Lpu16SnsLisEchoThresholdTable[Lu8TableIndex] - Lu16ThresCompenset))
                    {
                        /* 添加对于第二回波有效性的判断，第二回波高度同样需要通过阈值表过滤 */
                        //if(LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1] > (Lu16ListenHeight/3))
                        if(1)
                        {
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - SNS_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1];
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                        }
                        else
                        {
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                        }
                        Lu8FindValidObjFlag = 1;
                        i++;
                    }
                }
            }
            else
            {
                if(Lu16ListenHeight > (Lpu16SnsLisEchoThresholdTable[Lu8TableIndex] - Lu16ThresCompenset))
                {
                    LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                    LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                    LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                    LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                    LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                    Lu8FindValidObjFlag = 1;
                }
            }
        }
        else
        {
            if(Lu16ListenHeight > (Lpu16SnsLisEchoThresholdTable[Lu8TableIndex] - Lu16ThresCompenset))
            {
                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                Lu8FindValidObjFlag = 1;
            }
        }
        if(Lu8FindValidObjFlag)
        {
            if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] > LptrSnsJudgeObjTypeThresholdTable[Lu8TableHighIndex].u16BigWallHeight)
            {
                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt] = OBJ_BIGWALL_TYPE;
            }
            else
            {
                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt] = OBJ_PVC_PIPE_TYPE;
            }
#if 0
            /* 只保留小障碍物后的大障碍物逻辑处理--2023-11-13 */
            if(Lu8SigGroupCnt > 0)
            {
                if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] < LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt-1])
                {
                    break;
                }
            }
#endif
            Lu8SigGroupCnt++;
        }
        
        if(Lu8SigGroupCnt == MAX_NUM_OF_SIG_GROUP)
        {
            break;
        }
    }
    LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8SigGroupCnt = Lu8SigGroupCnt;
    /* 保留备份值 */
    for(i = 0; i < MAX_NUM_OF_SIG_GROUP; i++)
    {
        LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstDisBackup[i] = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[i];
    }

    /* Step3:通过阈值过滤和提取右侦听回波信号组 */
    Lu8ListenEchoCnt = LpStrPDCSnsRawData->u8RightListenEchoCnt[LePDCSnsCh];
    Lu8SigGroupCnt = 0;
    Lu16ThresCompenset = 0;
    for(i = 0; i < Lu8ListenEchoCnt; i++)
    {
        /* 先判断是否有第二回波进行匹配，再进行查询滤波；避免把回波信号组的隐藏信息过滤掉 */
        Lu16ListenDis = (LpStrPDCSnsRawData->u16RightListenDis[LePDCSnsCh][i])>>1;
        Lu16ListenHeight = LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i];
        if((Lu16ListenDis > 1500)&&(Lu16ListenDis < 3600))
        {
            if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
            {
                Lu16ThresCompenset = SNS_STD_LISTEN_COMPEN_HEIGHT;
            }
            else
            {
                Lu16ThresCompenset = SNS_CHIRP_LISTEN_COMPEN_HEIGHT;
            }
        }
        else if(Lu16ListenDis < 900)
        {
            if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
            {
                Lu16ThresCompenset = SNS_STD_NEAR_LISTEN_COMPEN_HEIGHT;
            }
        }

        if(Lu16ListenDis < SNS_TABLE_MAX_DIS)
        {
            Lu8TableIndex = Lu16ListenDis/SNS_TABLE_STEP;
            Lu8TableHighIndex = Lu16ListenDis/SNS_HIGH_TABLE_STEP;
        }
        else
        {
            Lu8TableIndex = SNS_DIS_400cm;
            Lu8TableHighIndex = SNS_DIS_HIGH_400cm;
        }


        if(Lu16ListenDis < 2000)
        {
            Lu16FirstSecondEchoSub = SNS_LISTEN_SECOND_ECHO_DIS;
        }
        else
        {
            Lu16FirstSecondEchoSub = SNS_LISTEN_SECOND_ECHO_DIS2;
        }

        if(Lu8TableIndex < SNS_DIS_100cm)
        {
            Lu16NoiseHeight = 1500;
        }
        else if(Lu8TableIndex < SNS_DIS_150cm)
        {
            Lu16NoiseHeight = 1200;
        }
        else if(Lu8TableIndex < SNS_DIS_200cm)
        {
            Lu16NoiseHeight = 1100;
        }
        else 
        {
            Lu16NoiseHeight = 900;
        }

        /* 第一回波必须大于PVC的阈值，否则直接当做噪点过滤掉--2023-12-01 */
        if(Lu16ListenHeight < (Lpu16SnsLisEchoThresholdTable[Lu8TableIndex] - Lu16ThresCompenset))
        {
            continue;
        }
        Lu8FindValidObjFlag = 0;
        if((i+1) < Lu8ListenEchoCnt)
        {
            Lu16ListenSecondDis = (LpStrPDCSnsRawData->u16RightListenDis[LePDCSnsCh][i+1])>>1;
            Lu16ListenDisSub = Lu16ListenSecondDis - Lu16ListenDis;
            if(Lu16ListenDisSub < Lu16FirstSecondEchoSub)
            {
                /* 此处存在风险，即一个大墙的20cm附近存在小的障碍物，可能存在距离不对问题,因此仅针对70cm以外的使用该策略 */
                if(((Lu16ListenHeight+Lu16SnsSecondThanFirstHeight) < LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1])&&\
                    (Lu16ListenDis > 700))
                {
                    if(Lu16ListenSecondDis < SNS_TABLE_MAX_DIS)
                    {
                        Lu8TableIndex = Lu16ListenSecondDis/SNS_TABLE_STEP;
                        Lu8TableHighIndex = Lu16ListenSecondDis/SNS_HIGH_TABLE_STEP;
                    }
                    else
                    {
                        Lu8TableIndex = SNS_DIS_400cm;
                        Lu8TableHighIndex = SNS_DIS_HIGH_400cm;
                    }

                    if(LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1] > (Lpu16SnsLisEchoThresholdTable[Lu8TableIndex] - Lu16ThresCompenset))
                    {
                        /* 第一第二回波构成信号组，且第二回波大于第一回波的处理；
                           由于探头扫频模式下的灵敏度提升，导致较多噪点和正常回波进行误配属，需要在此处进行过滤处理*/
                        if(Lu8ChirpModeFlag)
                        {
                            Lu8FirstDisIsNoise = 0;
                            Lu16FirstSecondHeightSub = LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1] - Lu16ListenHeight;
                            if(Lu16FirstSecondHeightSub > 500)
                            {
                                if(Lu16ListenHeight < Lu16NoiseHeight)
                                {
                                    Lu8FirstDisIsNoise = 1;
                                }
                            }
                            if(Lu8FirstDisIsNoise == 0)
                            {
                                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - SNS_LISTEN_COMPEN_DIS;
                                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1];
                                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - SNS_LISTEN_COMPEN_DIS;
                                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1];
                                Lu8FindValidObjFlag = 1;
                                i++;
                            }
                            else
                            {
                                /* 继续后续的查询 */
                            }
                        }
                        else
                        {
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - SNS_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1];
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1];
                            Lu8FindValidObjFlag = 1;
                            i++;
                        }
                    }
                }
                else
                {
                    if(Lu16ListenHeight > (Lpu16SnsLisEchoThresholdTable[Lu8TableIndex] - Lu16ThresCompenset))
                    {
                        /* 添加对于第二回波有效性的判断，第二回波高度同样需要通过阈值表过滤 */
                        //if(LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1] > (Lu16ListenHeight/3))
                        if(1)
                        {
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - SNS_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1];
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                        }
                        else
                        {
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                        }
                        Lu8FindValidObjFlag = 1;
                        i++;
                    }
                }
            }
            else
            {
                if(Lu16ListenHeight > (Lpu16SnsLisEchoThresholdTable[Lu8TableIndex] - Lu16ThresCompenset))
                {
                    LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                    LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                    LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                    LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                    LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                    Lu8FindValidObjFlag = 1;
                }
            }
        }
        else
        {
            if(Lu16ListenHeight > (Lpu16SnsLisEchoThresholdTable[Lu8TableIndex] - Lu16ThresCompenset))
            {
                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                Lu8FindValidObjFlag = 1;
            }
        }
        if(Lu8FindValidObjFlag)
        {
            if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] > LptrSnsJudgeObjTypeThresholdTable[Lu8TableHighIndex].u16BigWallHeight)
            {
                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt] = OBJ_BIGWALL_TYPE;
            }
            else
            {
                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt] = OBJ_PVC_PIPE_TYPE;
            }
#if 0
            /* 只保留小障碍物后的大障碍物逻辑处理--2023-11-13 */
            if(Lu8SigGroupCnt > 0)
            {
                if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] < LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt-1])
                {
                    break;
                }
            }
#endif
            Lu8SigGroupCnt++;
        }

        
        if(Lu8SigGroupCnt == MAX_NUM_OF_SIG_GROUP)
        {
            break;
        }
    }
    LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8SigGroupCnt = Lu8SigGroupCnt;
    /* 保留备份值 */
    for(i = 0; i < MAX_NUM_OF_SIG_GROUP; i++)
    {
        LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstDisBackup[i] = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[i];
    }

    SetSnsSigGroupDataUpdateFlag(LeGroup,LePDCSnsCh);
	//if(0 == Elmos17SnsCtrl_GetDVWorkFlg())
	if(1)
	{
    	UpdateDE_CE_Dis(LeGroup,LePDCSnsCh);
	}
	else
	{
		UpdateDE_CE_Dis_forDV(LeGroup,LePDCSnsCh);
	}
}

#else
{
    uint8 i;
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    Sns_CycleType LenuCurIndex;
    float LfCarMoveSub = 0;
    PDCSnsRawDataType *LpStrPDCSnsRawData;
    uint8 Lu8MasterEchoCnt,Lu8ListenEchoCnt;
    uint16 Lu16MasterDis,Lu16MasterHeight,Lu16MasterDisSub;
    uint16 Lu16ListenDis,Lu16ListenSecondDis,Lu16ListenHeight,Lu16ListenDisSub;
    uint16 Lu16FirstSecondEchoSub;
    uint8 Lu8SigGroupCnt;
    uint8 Lu8TableIndex;
    uint8 Lu8TableHighIndex;
    const uint16 *Lpu16SnsEchoThresholdTable;
    const SnsCalibHeightType *LptrSnsJudgeObjTypeThresholdTable;
    uint8 Lu8FindValidObjFlag = 0;
    uint16 Lu16ThresCompenset = 0;
    uint16 Lu16MasterThresCompenset = 0;    /* 主发探头的阈值补偿，主要考虑车速较高时，适当降低阈值，以保证高速的探测稳定性 */
    uint16 Lu16SnsSecondThanFirstHeight;
    uint16 Lu16FirstDisToBackupSub,Lu16SecondDisToBackupSub;
    uint8 Lu8FirstDisIsNoise;

    LpStrPDCSnsRawData = &GstrPDCSnsRawData[LeGroup];
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LePDCSnsCh];

    if((LePDCSnsCh == PDC_SNS_CH_FLS_RLS)||(LePDCSnsCh == PDC_SNS_CH_FRS_RRS))
    {
        if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
        {
            Lpu16SnsEchoThresholdTable = Gpu16SnsThresholdTable[LeGroup][0];
        }
        else
        {
            Lpu16SnsEchoThresholdTable = Gpu16SnsThresholdTable[LeGroup][1];
        }
    }
    else if((LePDCSnsCh == PDC_SNS_CH_FL_RL)||(LePDCSnsCh == PDC_SNS_CH_FR_RR))
    {
        if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
        {
            Lpu16SnsEchoThresholdTable = Gpu16SnsThresholdTable[LeGroup][2];
        }
        else
        {
            Lpu16SnsEchoThresholdTable = Gpu16SnsThresholdTable[LeGroup][3];
        }
    }
    else
    {
        if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
        {
            Lpu16SnsEchoThresholdTable = Gpu16SnsThresholdTable[LeGroup][4];
        }
        else
        {
            Lpu16SnsEchoThresholdTable = Gpu16SnsThresholdTable[LeGroup][5];
        }
    }

    if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
    {
        LptrSnsJudgeObjTypeThresholdTable = &GStrObjJudgeStandardThresholdTableInRAM[0];
        Lu16SnsSecondThanFirstHeight = SNS_STD_SECOND_THAN_FIRST_HEIGHT;
        if(Gu16CarSpdForSnsUse < 50)
        {
            Lu16MasterThresCompenset = 0;
        }
        else if(Gu16CarSpdForSnsUse < 100)
        {
            Lu16MasterThresCompenset = SNS_STD_MASTER_CMP_THRES_1KM;
        }
        else if(Gu16CarSpdForSnsUse < 300)
        {
            Lu16MasterThresCompenset = SNS_STD_MASTER_CMP_THRES_3KM;
        }
        else if(Gu16CarSpdForSnsUse < 500)
        {
            Lu16MasterThresCompenset = SNS_STD_MASTER_CMP_THRES_5KM;
        }
        else if(Gu16CarSpdForSnsUse < 700)
        {
            Lu16MasterThresCompenset = SNS_STD_MASTER_CMP_THRES_7KM;
        }
        else
        {
            Lu16MasterThresCompenset = SNS_STD_MASTER_CMP_THRES_MORE_7KM;
        }
    }
    else
    {
        LptrSnsJudgeObjTypeThresholdTable = &GStrObjJudgeChirpThresholdTableInRAM[0];
        Lu16SnsSecondThanFirstHeight = SNS_CHIRP_SECOND_THAN_FIRST_HEIGHT;
        if(Gu16CarSpdForSnsUse < 50)
        {
            Lu16MasterThresCompenset = 0;
        }
        else if(Gu16CarSpdForSnsUse < 100)
        {
            Lu16MasterThresCompenset = SNS_CHIRP_MASTER_CMP_THRES_1KM;
        }
        else if(Gu16CarSpdForSnsUse < 300)
        {
            Lu16MasterThresCompenset = SNS_CHIRP_MASTER_CMP_THRES_3KM;
        }
        else if(Gu16CarSpdForSnsUse < 500)
        {
            Lu16MasterThresCompenset = SNS_CHIRP_MASTER_CMP_THRES_5KM;
        }
        else if(Gu16CarSpdForSnsUse < 700)
        {
            Lu16MasterThresCompenset = SNS_CHIRP_MASTER_CMP_THRES_7KM;
        }
        else
        {
            Lu16MasterThresCompenset = SNS_CHIRP_MASTER_CMP_THRES_MORE_7KM;
        }
    }



    /* 更新当前索引值 */
    LpstrSnsSigGroupDataCache->enuCurIndex++;
    if(LpstrSnsSigGroupDataCache->enuCurIndex >= SNS_CYCLE_NUM)
    {
        LpstrSnsSigGroupDataCache->enuCurIndex = SNS_CYCLE_0;
    }
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    /*开始索引技术，方便后续累计到3个周期后进行数据跟踪处理*/
    if(LpstrSnsSigGroupDataCache->u8RecordCnt < SNS_CYCLE_NUM)
    {
        LpstrSnsSigGroupDataCache->u8RecordCnt++;
    }

    SnsSigGroupDataClear(LeGroup,LePDCSnsCh,LenuCurIndex);

    /* 获取上一轮到本轮探头探测车辆移动的距离及对应的系统时间 */
    if(LpstrSnsSigGroupDataCache->u8RecordCnt > 1)
    {
        LfCarMoveSub = GstrSnsCarMovSts[LeGroup][LePDCSnsCh].fSnsCarMovDisSub;
    }
    if(LeGroup == PDC_SNS_GROUP_FRONT)
    {
        /** @brief: 对于前探头，车子前进是靠近障碍物 */
        LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub = -LfCarMoveSub;
    }
    else
    {
        LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub = LfCarMoveSub;
    }
    //LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].u32SysTime = GetSystemTimeCnt_Ms();
    LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].u32SysTime = 0;
    LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eCarDir = GstrSnsCarMovSts[LeGroup][LePDCSnsCh].eCarDir;
    LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType = LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh];

    /* Step1:通过阈值过滤和提取主发回波信号组 */
    Lu8MasterEchoCnt = LpStrPDCSnsRawData->u8MasterEchoCnt[LePDCSnsCh];
    Lu8SigGroupCnt = 0;
    for(i = 0; i < Lu8MasterEchoCnt; i++)
    {
        /* 先判断是否有第二回波进行匹配，再进行查询滤波；避免把回波信号组的隐藏信息过滤掉 */
        if(i == 0)
        {
            if(LpStrPDCSnsRawData->u16NFD_Dis[LePDCSnsCh] < LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i])
            {
                Lu16MasterDis = LpStrPDCSnsRawData->u16NFD_Dis[LePDCSnsCh];
                Lu16MasterHeight = SNS_NFD_ECHO_HEIGHT;
            }
            else
            {
                Lu16MasterDis = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i];
                Lu16MasterHeight = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i];
            }
        }
        else
        {
            Lu16MasterDis = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i];
            Lu16MasterHeight = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i];
        }
        
        if(Lu16MasterDis < SNS_TABLE_MAX_DIS)
        {
            Lu8TableIndex = Lu16MasterDis/SNS_TABLE_STEP;
            Lu8TableHighIndex = Lu16MasterDis/SNS_HIGH_TABLE_STEP;
        }
        else
        {
            Lu8TableIndex = SNS_DIS_400cm;
            Lu8TableHighIndex = SNS_DIS_HIGH_400cm;
        }

        if(Lu16MasterDis < 2000)
        {
            Lu16FirstSecondEchoSub = SNS_MASTER_SECOND_ECHO_DIS;
        }
        else
        {
            Lu16FirstSecondEchoSub = SNS_MASTER_SECOND_ECHO_DIS2;
        }
        Lu8FindValidObjFlag = 0;
        if((i+1) < Lu8MasterEchoCnt)
        {
            Lu16MasterDisSub = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1] - Lu16MasterDis;
            if(Lu16MasterDisSub < Lu16FirstSecondEchoSub)
            {
                /* 此处存在风险，即一个大墙的20cm附近存在小的障碍物，可能存在距离不对问题,因此仅针对70cm以外的使用该策略 */
                if(((Lu16MasterHeight+Lu16SnsSecondThanFirstHeight) < LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1])&&\
                    (Lu16MasterDis > 700))
                {
                    if(LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1] < SNS_TABLE_MAX_DIS)
                    {
                        Lu8TableIndex = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1]/SNS_TABLE_STEP;
                        Lu8TableHighIndex = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1]/SNS_HIGH_TABLE_STEP;
                    }
                    else
                    {
                        Lu8TableIndex = SNS_DIS_400cm;
                        Lu8TableHighIndex = SNS_DIS_HIGH_400cm;
                    }
                    
                    if(LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1] > (Lpu16SnsEchoThresholdTable[Lu8TableIndex]-Lu16MasterThresCompenset))
                    {
                        Lu8FirstDisIsNoise = 0;
                        if((LePDCSnsCh < PDC_SNS_CH_FML_RML)||(LePDCSnsCh > PDC_SNS_CH_FMR_RMR))
                        {
                            Lu16FirstDisToBackupSub = ABS(Lu16MasterDis,LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstDisBackup[Lu8SigGroupCnt]);
                            Lu16SecondDisToBackupSub = ABS(LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1],LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstDisBackup[Lu8SigGroupCnt]);
                            if(Lu16FirstDisToBackupSub > (Lu16SecondDisToBackupSub+8))
                            {
                                Lu8FirstDisIsNoise = 1;
                            }
                        }
                        if(Lu8FirstDisIsNoise == 0)
                        {
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16MasterDis - SNS_MASTER_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1] - SNS_MASTER_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1];
#if SNS_HIGH_CURB_USE_FIRST_DIS_ENABLE
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i] - SNS_MASTER_COMPEN_DIS;
#else
                            if((LePDCSnsCh == PDC_SNS_CH_FLS_RLS)||(LePDCSnsCh == PDC_SNS_CH_FRS_RRS))
                            {
                                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i] - SNS_MASTER_COMPEN_DIS;
                            }
                            else
                            {
                                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1] - SNS_MASTER_COMPEN_DIS;
                            }
#endif
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1];
                        }
                        else
                        {
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1] - SNS_MASTER_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1];
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt];
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt];
                        }
                        Lu8FindValidObjFlag = 1;
                        i++;
                    }
                }
                else
                {
                    if(Lu16MasterHeight > (Lpu16SnsEchoThresholdTable[Lu8TableIndex]-Lu16MasterThresCompenset))
                    {
                        LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                        LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16MasterDis - SNS_MASTER_COMPEN_DIS;
                        LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                        LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1] - SNS_MASTER_COMPEN_DIS;
                        LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1];
                        LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16MasterDis - SNS_MASTER_COMPEN_DIS;
                        LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                        Lu8FindValidObjFlag = 1;
                        i++;
                    }
                }
            }
            else
            {
                if(Lu16MasterHeight > (Lpu16SnsEchoThresholdTable[Lu8TableIndex]-Lu16MasterThresCompenset))
                {
                    LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                    LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16MasterDis - SNS_MASTER_COMPEN_DIS;
                    LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                    LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16MasterDis - SNS_MASTER_COMPEN_DIS;
                    LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                    Lu8FindValidObjFlag = 1;
                }
            }
        }
        else
        {
            if(Lu16MasterHeight > (Lpu16SnsEchoThresholdTable[Lu8TableIndex]-Lu16MasterThresCompenset))
            {
                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16MasterDis - SNS_MASTER_COMPEN_DIS;
                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16MasterDis - SNS_MASTER_COMPEN_DIS;
                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                Lu8FindValidObjFlag = 1;
            }
        }
        if(Lu8FindValidObjFlag)
        {
            if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] > LptrSnsJudgeObjTypeThresholdTable[Lu8TableHighIndex].u16BigWallHeight)
            {
                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt] = OBJ_BIGWALL_TYPE;
            }
            else
            {
                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt] = OBJ_PVC_PIPE_TYPE;
            }

            /* 只保留小障碍物后的大障碍物逻辑处理--2023-11-13 */
            if(Lu8SigGroupCnt > 0)
            {
                if((LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt]-5) < LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt-1])
                {
                    break;
                }
            }
            Lu8SigGroupCnt++;
        }
        if(Lu8SigGroupCnt == MAX_NUM_OF_SIG_GROUP)
        {
            break;
        }
    }
    LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8SigGroupCnt = Lu8SigGroupCnt;
    /* 保留备份值 */
    for(i = 0; i < MAX_NUM_OF_SIG_GROUP; i++)
    {
        LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstDisBackup[i] = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[i];
    }

    /* Step2:通过阈值过滤和提取左侦听回波信号组 */
    Lu16ThresCompenset = 0;
    Lu8ListenEchoCnt = LpStrPDCSnsRawData->u8LeftListenEchoCnt[LePDCSnsCh];
    Lu8SigGroupCnt = 0;
    for(i = 0; i < Lu8ListenEchoCnt; i++)
    {
        /* 先判断是否有第二回波进行匹配，再进行查询滤波；避免把回波信号组的隐藏信息过滤掉 */
        Lu16ListenDis = (LpStrPDCSnsRawData->u16LeftListenDis[LePDCSnsCh][i])>>1;
        Lu16ListenHeight = LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i];
        if((Lu16ListenDis > 1500)&&(Lu16ListenDis < 3600))
        {
            if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
            {
                Lu16ThresCompenset = SNS_STD_LISTEN_COMPEN_HEIGHT;
            }
            else
            {
                Lu16ThresCompenset = SNS_CHIRP_LISTEN_COMPEN_HEIGHT;
            }
        }
        else if(Lu16ListenDis < 900)
        {
            if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
            {
                Lu16ThresCompenset = SNS_STD_NEAR_LISTEN_COMPEN_HEIGHT;
            }
        }
        if(Lu16ListenDis < SNS_TABLE_MAX_DIS)
        {
            Lu8TableIndex = Lu16ListenDis/SNS_TABLE_STEP;
            Lu8TableHighIndex = Lu16ListenDis/SNS_HIGH_TABLE_STEP;
        }
        else
        {
            Lu8TableIndex = SNS_DIS_400cm;
            Lu8TableHighIndex = SNS_DIS_HIGH_400cm;
        }

        if(Lu16ListenDis < 2000)
        {
            Lu16FirstSecondEchoSub = SNS_LISTEN_SECOND_ECHO_DIS;
        }
        else
        {
            Lu16FirstSecondEchoSub = SNS_LISTEN_SECOND_ECHO_DIS2;
        }
        Lu8FindValidObjFlag = 0;
        if((i+1) < Lu8ListenEchoCnt)
        {
            Lu16ListenSecondDis = (LpStrPDCSnsRawData->u16LeftListenDis[LePDCSnsCh][i+1])>>1;
            Lu16ListenDisSub = Lu16ListenSecondDis - Lu16ListenDis;
            if(Lu16ListenDisSub < Lu16FirstSecondEchoSub)
            {
                /* 此处存在风险，即一个大墙的20cm附近存在小的障碍物，可能存在距离不对问题,因此仅针对70cm以外的使用该策略 */
                if(((Lu16ListenHeight+Lu16SnsSecondThanFirstHeight) < LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1])&&\
                    (Lu16ListenDis > 700))
                {
                    if(Lu16ListenSecondDis < SNS_TABLE_MAX_DIS)
                    {
                        Lu8TableIndex = Lu16ListenSecondDis/SNS_TABLE_STEP;
                        Lu8TableHighIndex = Lu16ListenSecondDis/SNS_HIGH_TABLE_STEP;
                    }
                    else
                    {
                        Lu8TableIndex = SNS_DIS_400cm;
                        Lu8TableHighIndex = SNS_DIS_HIGH_400cm;
                    }
                    if(LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1] > (Lpu16SnsEchoThresholdTable[Lu8TableIndex] - Lu16ThresCompenset))
                    {
                        Lu8FirstDisIsNoise = 0;
                        if((LePDCSnsCh < PDC_SNS_CH_FML_RML)||(LePDCSnsCh > PDC_SNS_CH_FMR_RMR))
                        {
                            Lu16FirstDisToBackupSub = ABS(Lu16ListenDis,LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstDisBackup[Lu8SigGroupCnt]);
                            Lu16SecondDisToBackupSub = ABS(Lu16ListenSecondDis,LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstDisBackup[Lu8SigGroupCnt]);
                            if(Lu16FirstDisToBackupSub > (Lu16SecondDisToBackupSub+8))
                            {
                                Lu8FirstDisIsNoise = 1;
                            }
#if 0
                            if((LePDCSnsCh == PDC_SNS_CH_FRS)&&(LeGroup == 0x00))
                            {
                                printf("FirstDisToBackupSub:%d,SecondDisToBackupSub:%d\r\n",Lu16FirstDisToBackupSub,Lu16SecondDisToBackupSub);
                            }
#endif
                        }
                        if(Lu8FirstDisIsNoise == 0)
                        {
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - SNS_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1];
#if SNS_HIGH_CURB_USE_FIRST_DIS_ENABLE
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
#else
                            if((LePDCSnsCh == PDC_SNS_CH_FLS_RLS)||(LePDCSnsCh == PDC_SNS_CH_FRS_RRS))
                            {
                                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                            }
                            else
                            {
                                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - SNS_LISTEN_COMPEN_DIS;
                            }
#endif
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1];
                        }
                        else
                        {
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - SNS_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1];
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt];
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1];
                        }
                        Lu8FindValidObjFlag = 1;
                        i++;
                    }
                }
                else
                {
                    if(Lu16ListenHeight > (Lpu16SnsEchoThresholdTable[Lu8TableIndex] - Lu16ThresCompenset))
                    {
                        LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                        LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                        LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                        LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - SNS_LISTEN_COMPEN_DIS;
                        LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1];
                        LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                        LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                        Lu8FindValidObjFlag = 1;
                        i++;
                    }
                }
            }
            else
            {
                if(Lu16ListenHeight > (Lpu16SnsEchoThresholdTable[Lu8TableIndex] - Lu16ThresCompenset))
                {
                    LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                    LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                    LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                    LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                    LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                    Lu8FindValidObjFlag = 1;
                }
            }
        }
        else
        {
            if(Lu16ListenHeight > (Lpu16SnsEchoThresholdTable[Lu8TableIndex] - Lu16ThresCompenset))
            {
                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                Lu8FindValidObjFlag = 1;
            }
        }
        if(Lu8FindValidObjFlag)
        {
            if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] > LptrSnsJudgeObjTypeThresholdTable[Lu8TableHighIndex].u16BigWallHeight)
            {
                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt] = OBJ_BIGWALL_TYPE;
            }
            else
            {
                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt] = OBJ_PVC_PIPE_TYPE;
            }
#if 0
            /* 只保留小障碍物后的大障碍物逻辑处理--2023-11-13 */
            if(Lu8SigGroupCnt > 0)
            {
                if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] < LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt-1])
                {
                    break;
                }
            }
#endif
            Lu8SigGroupCnt++;
        }
        
        if(Lu8SigGroupCnt == MAX_NUM_OF_SIG_GROUP)
        {
            break;
        }
    }
    LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8SigGroupCnt = Lu8SigGroupCnt;
    /* 保留备份值 */
    for(i = 0; i < MAX_NUM_OF_SIG_GROUP; i++)
    {
        LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstDisBackup[i] = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[i];
    }

    /* Step2:通过阈值过滤和提取左侦听回波信号组 */
    Lu8ListenEchoCnt = LpStrPDCSnsRawData->u8RightListenEchoCnt[LePDCSnsCh];
    Lu8SigGroupCnt = 0;
    Lu16ThresCompenset = 0;
    for(i = 0; i < Lu8ListenEchoCnt; i++)
    {
        /* 先判断是否有第二回波进行匹配，再进行查询滤波；避免把回波信号组的隐藏信息过滤掉 */
        Lu16ListenDis = (LpStrPDCSnsRawData->u16RightListenDis[LePDCSnsCh][i])>>1;
        Lu16ListenHeight = LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i];
        if((Lu16ListenDis > 1500)&&(Lu16ListenDis < 3600))
        {
            if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
            {
                Lu16ThresCompenset = SNS_STD_LISTEN_COMPEN_HEIGHT;
            }
            else
            {
                Lu16ThresCompenset = SNS_CHIRP_LISTEN_COMPEN_HEIGHT;
            }
        }
        else if(Lu16ListenDis < 900)
        {
            if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
            {
                Lu16ThresCompenset = SNS_STD_NEAR_LISTEN_COMPEN_HEIGHT;
            }
        }

        if(Lu16ListenDis < SNS_TABLE_MAX_DIS)
        {
            Lu8TableIndex = Lu16ListenDis/SNS_TABLE_STEP;
            Lu8TableHighIndex = Lu16ListenDis/SNS_HIGH_TABLE_STEP;
        }
        else
        {
            Lu8TableIndex = SNS_DIS_400cm;
            Lu8TableHighIndex = SNS_DIS_HIGH_400cm;
        }


        if(Lu16ListenDis < 2000)
        {
            Lu16FirstSecondEchoSub = SNS_LISTEN_SECOND_ECHO_DIS;
        }
        else
        {
            Lu16FirstSecondEchoSub = SNS_LISTEN_SECOND_ECHO_DIS2;
        }
        Lu8FindValidObjFlag = 0;
        if((i+1) < Lu8ListenEchoCnt)
        {
            Lu16ListenSecondDis = (LpStrPDCSnsRawData->u16RightListenDis[LePDCSnsCh][i+1])>>1;
            Lu16ListenDisSub = Lu16ListenSecondDis - Lu16ListenDis;
            if(Lu16ListenDisSub < Lu16FirstSecondEchoSub)
            {
                /* 此处存在风险，即一个大墙的20cm附近存在小的障碍物，可能存在距离不对问题,因此仅针对70cm以外的使用该策略 */
                if(((Lu16ListenHeight+Lu16SnsSecondThanFirstHeight) < LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1])&&\
                    (Lu16ListenDis > 700))
                {
                    if(Lu16ListenSecondDis < SNS_TABLE_MAX_DIS)
                    {
                        Lu8TableIndex = Lu16ListenSecondDis/SNS_TABLE_STEP;
                        Lu8TableHighIndex = Lu16ListenSecondDis/SNS_HIGH_TABLE_STEP;
                    }
                    else
                    {
                        Lu8TableIndex = SNS_DIS_400cm;
                        Lu8TableHighIndex = SNS_DIS_HIGH_400cm;
                    }

                    if(LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1] > (Lpu16SnsEchoThresholdTable[Lu8TableIndex] - Lu16ThresCompenset))
                    {
                        Lu8FirstDisIsNoise = 0;
                        if((LePDCSnsCh < PDC_SNS_CH_FML_RML)||(LePDCSnsCh > PDC_SNS_CH_FMR_RMR))
                        {
                            Lu16FirstDisToBackupSub = ABS(Lu16ListenDis,LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstDisBackup[Lu8SigGroupCnt]);
                            Lu16SecondDisToBackupSub = ABS(Lu16ListenSecondDis,LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstDisBackup[Lu8SigGroupCnt]);
                            if(Lu16FirstDisToBackupSub > (Lu16SecondDisToBackupSub+8))
                            {
                                Lu8FirstDisIsNoise = 1;
                            }
                        }
                        if(Lu8FirstDisIsNoise == 0)
                        {
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - SNS_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1];
#if SNS_HIGH_CURB_USE_FIRST_DIS_ENABLE
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
#else
                            if((LePDCSnsCh == PDC_SNS_CH_FLS_RLS)||(LePDCSnsCh == PDC_SNS_CH_FRS_RRS))
                            {
                                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                            }
                            else
                            {
                                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - SNS_LISTEN_COMPEN_DIS;
                            }
#endif
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1];
                        }
                        else
                        {
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - SNS_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1];
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt];
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1];
                        }
                        Lu8FindValidObjFlag = 1;
                        i++;
                    }
                }
                else
                {
                    if(Lu16ListenHeight > (Lpu16SnsEchoThresholdTable[Lu8TableIndex] - Lu16ThresCompenset))
                    {
                        LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                        LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                        LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                        LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - SNS_LISTEN_COMPEN_DIS;
                        LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1];
                        LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                        LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                        Lu8FindValidObjFlag = 1;
                        i++;
                    }
                }
            }
            else
            {
                if(Lu16ListenHeight > (Lpu16SnsEchoThresholdTable[Lu8TableIndex] - Lu16ThresCompenset))
                {
                    LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                    LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                    LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                    LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                    LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                    Lu8FindValidObjFlag = 1;
                }
            }
        }
        else
        {
            if(Lu16ListenHeight > (Lpu16SnsEchoThresholdTable[Lu8TableIndex] - Lu16ThresCompenset))
            {
                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - SNS_LISTEN_COMPEN_DIS;
                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                Lu8FindValidObjFlag = 1;
            }
        }
        if(Lu8FindValidObjFlag)
        {
            if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] > LptrSnsJudgeObjTypeThresholdTable[Lu8TableHighIndex].u16BigWallHeight)
            {
                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt] = OBJ_BIGWALL_TYPE;
            }
            else
            {
                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt] = OBJ_PVC_PIPE_TYPE;
            }
#if 0
            /* 只保留小障碍物后的大障碍物逻辑处理--2023-11-13 */
            if(Lu8SigGroupCnt > 0)
            {
                if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] < LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt-1])
                {
                    break;
                }
            }
#endif
            Lu8SigGroupCnt++;
        }

        
        if(Lu8SigGroupCnt == MAX_NUM_OF_SIG_GROUP)
        {
            break;
        }
    }
    LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8SigGroupCnt = Lu8SigGroupCnt;
    /* 保留备份值 */
    for(i = 0; i < MAX_NUM_OF_SIG_GROUP; i++)
    {
        LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstDisBackup[i] = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[i];
    }

    SetSnsSigGroupDataUpdateFlag(LeGroup,LePDCSnsCh);
	if(0 == Elmos17SnsCtrl_GetDVWorkFlg())
	//if(1)
	{
    	UpdateDE_CE_Dis(LeGroup,LePDCSnsCh);
	}
	else
	{
		UpdateDE_CE_Dis_forDV(LeGroup,LePDCSnsCh);
	}
}
#endif



