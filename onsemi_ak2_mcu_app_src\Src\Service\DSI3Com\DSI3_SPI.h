/*
 * DSI3_SPI.h
 *
 *  Created on: 2021年4月1日
 *      Author: 6000021992
 */

#ifndef DSI3_SPI_H_
#define DSI3_SPI_H_

#include "Queue_DSI_SPI_Data.h"

#if 0
    #define PRINTF_DSISPI Lhprintf
#else
    #define PRINTF_DSISPI(...)
#endif

/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************************/
/**
 * @brief   DSI SPI
 */
/*****************************************************************************/
typedef enum
{
    DSISPI_Start_SEQ = 0,
    DSISPI_MASTERIC_SEQ = DSISPI_Start_SEQ,
    DSISPI_DSICHL1_SEQ ,
    DSISPI_DSICHL2_SEQ ,
    DSISPI_DSICHL3_SEQ ,
    DSISPI_DSICHL4_SEQ ,
    DSISPI_DSICHL5_SEQ ,
   // DSISPI_DSICHL6_SEQ ,
   // DSISPI_DSICHL7_SEQ ,
    DSISPI_SEQ_NUM ,
    DSISPI_SEQ_NULL = 0xFF,
}DSISPI_SeqID;


/******************************************************************************/
/**
 * @brief   SPI 传输状态
 */
/*****************************************************************************/
typedef enum
{
    DSISPI_TRANS_IDLE = 0u,  /** @brief 传输空闲 */
        
    DSISPI_TRANS_SEL,        /** @brief 传输通道被选中 */
    DSISPI_TRANS_WAIT,       /** @brief 等待传输 */

    DSISPI_TRANS_BUSY,       /** @brief 传输中 */
    DSISPI_TRANS_DONE,       /** @brief 传输完成 */
    
    DSISPI_TRANS_NULL = 0xFF,       /** @brief NULL */
}DSISPI_TransmitStateType;


/******************************************************************************/
/**
 * @brief   SPI传输数据结构
 */
/*****************************************************************************/
typedef struct
{
              
    DSISPI_TransmitStateType  vTransmitState;    
    
    uint16  u16TransmitLen;     
    
    uint8   u8RxBufNum;         
    
    uint8 *pDSI_SPITxBuf;
    uint8 *pDSI_SPIRxBuf;

    uint16  u16IntReqIntervalTime;              
    uint16  u16SendTimeOut;                     
    uint8  u8SendTimeOutFlgCnt;                     
    uint16  u16SendTimeOutCnt;                     

    uint16  u16Cnt_IntTimeOut;                  
    uint16  u16Cnt_LoseFrame;                   
    uint16  u16Cnt_Failure; 

	uint32 u32TransCompleteTime;   /*unit: us*/
}DsiSpi_WorkType;

typedef enum
{
    eDSISPI_TransReturn_OK = 0u,  /** @brief 传输OK */
    eDSISPI_TransReturn_N_OK,      /** @brief 传输NOK */
}DSISPI_TransReturn_en;


typedef DSISPI_TransReturn_en (*DSI_Spi_AsyncTransmitFunc_t)(const uint8 * sendBuffer,uint8 * receiveBuffer,uint16 transferByteCount);


typedef struct
{
    DSI_Spi_AsyncTransmitFunc_t DSI_Spi_AsyncTransmitFunc;
   

}DSI_SPI_Interface_str;

typedef enum
{
    eDSISPI_Normal = 0u,  /** @brief SPI工作正常 */       
    eDSISPI_Abnormal,      /** @brief SPI工作异常 */  
}DSISPI_WorkSts_en;

/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
extern uint8 InitDSISPISeq(uint8 DSIMasterID,DSI_SPI_Interface_str *pDSI_SPI_Interface );
extern DSISPI_SeqID DSI_SPI_AsyncTransfer (uint8 DSIMasterID,uint8 *TxBuf,uint16 TxSize);
extern DSISPI_SeqID DSI_SPI_SyncTransfer (uint8 DSIMasterID,uint8 *TxBuf,uint16 TxSize,uint8 *RxBuf,uint8 timeOut_ms);
extern uint32 GetDSI_SPI_SeqTransCompleteTime(uint8 DSIMasterID, DSISPI_SeqID SeqID);
extern const uint8* GetDSI_SPI_SeqTransData(uint8 DSIMasterID,uint16 DataBufIdx);
extern uint8 DSI_SPITimeOutMonitor(uint8 DSIMasterID);
extern void DSI_SPI_CompleteTransfer_HandleCbk(uint8 DSIMasterID);
extern DSISPI_WorkSts_en DSI_SPIWorkSts(void);

#endif /* DSI3_SPI_H_ */
