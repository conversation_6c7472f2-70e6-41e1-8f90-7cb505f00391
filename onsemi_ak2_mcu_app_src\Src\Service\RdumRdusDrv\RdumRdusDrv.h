/******************************************************************************
 * @file      RdumRdusDrv.h
 * @brief     安森美超声波探头驱动接口
 * <AUTHOR>
 * @date      2025-05-14
 * @note
 *****************************************************************************/

#ifndef __RDUM_RDUS_DRV_H__
#define __RDUM_RDUS_DRV_H__
/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "types.h"
#include "BrcSlotDef.h"

/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/
#define WAVE_PROCESS_BRC_COUNT 13 /* 最大获取回波包络的次数 */

/******************************************************************************
 * @Type Definitions
 *****************************************************************************/
/* BRC命令状态 */
typedef enum
{
    BRC_STATUS_OK = 0,  /* 状态正常 */
    BRC_STATUS_ERROR,   /* 状态错误 */
    BRC_STATUS_TIMEOUT, /* 状态超时 */
    BRC_STATUS_INVALID  /* 状态无效 */
} BrcStatus_t;

/* 驱动状态枚举 */
typedef enum
{
    RDUM_RDUS_DRV_IDLE = 0,         /* 空闲 */
    RDUM_RDUS_DRV_INIT,             /* 初始化 */
    RDUM_RDUS_DRV_READY,            /* 就绪 */
    RDUM_RDUS_DRV_WAVE_PROCESS,     /* 发波流程 */
    RDUM_RDUS_DRV_ERROR,            /* 错误 */
} RdumRdusDrv_Status_t;

/* 发波流程步骤枚举 */
typedef enum
{
    WAVE_STEP_INIT = 0,      /* 初始化 */
    WAVE_STEP_CONFIG_MODE,   /* 配置发波模式 */
    WAVE_STEP_CONFIG_PARAM,  /* 配置发波参数 */
    WAVE_STEP_READ_STATUS,   /* 读取状态 */
    WAVE_STEP_GET_PARAM,     /* 获取发波参数 */
    WAVE_STEP_CONFIG_PARAM1, /* 配置发波参数 */
    WAVE_STEP_CONFIG_PARAM2, /* 配置发波参数 */
    WAVE_STEP_GET_BRC,       /* 获取回波包络 */

    WAVE_STEP_COMPLETE = 0xFF, /* 完成 */
} WaveProcessStep_t;

/* 发波流程状态枚举 */
typedef enum
{
    WAVE_PROCESS_IDLE = 0, /* 空闲 */
    WAVE_PROCESS_BUSY,     /* 忙 */
    WAVE_PROCESS_COMPLETE, /* 完成 */
    WAVE_PROCESS_ERROR,    /* 错误 */
} WaveProcessStatus_t;

/* 发波流程控制结构体 */
typedef struct
{
    WaveProcessStep_t CurrentStep;                                                                                     /* 当前步骤 */
    WaveProcessStatus_t Status;                                                                                        /* 状态 */
    BrcResult_t BrcResults[WAVE_PROCESS_BRC_COUNT];                                                                    /* 回波包络结果 */
    uint16 DecompressedEnvelopes[MAX_SLOT_COUNT][WAVE_PROCESS_BRC_COUNT * ENVELOPE_GROUPS][ENVELOPE_POINTS_PER_GROUP]; /* 存储所有解压缩后的包络点位 */
    uint8 BrcIndex;                                                                                                    /* 当前回波包络索引 */
    uint8 BrcCount;                                                                                                    /* 总共需要接收的回波包络数量 */
    uint8 RegValues[16];                                                                                               /* 寄存器值缓存 */
} WaveProcessControl_t;

/******************************************************************************
 * @Function Declaration
 *****************************************************************************/
/* 初始化驱动 */
void RdumRdusDrv_Init(void);

/* 1ms周期任务 */
void RdumRdusDrv_1msTASK(void);

/* 非周期任务 */
void RdumRdusDrv_NoPeriod_TASK(void);

/* 获取回波包络结果 */
uint8 RdumRdusDrv_GetBrcResults(BrcResult_t *Results);

/* 获取驱动状态 */
uint8 RdumRdusDrv_GetStatus(void);

/* 获取发波流程标志 */
uint8 RdumRdusDrv_GetWaveProcessFlag(void);

#endif /* __RDUM_RDUS_DRV_H__ */
