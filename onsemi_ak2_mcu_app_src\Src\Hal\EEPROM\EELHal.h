/*
 * @Author: <PERSON>.<EMAIL>
 * @Date: 2023-10-14 14:56:40
 * @LastEditors: <PERSON> <EMAIL>
 * @LastEditTime: 2023-10-14 17:06:52
 * @FilePath: \Src\Hal\EEPROM\EELHal.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: EELHal.h
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/
#ifndef __EEL_HAL_H
#define __EEL_HAL_H

#include "types.h"
#include "eel_cfg.h"
#include "r_eel_types.h"
/**********************************宏定义**************************************/

#ifndef NVM_REQ_OK
#define NVM_REQ_OK (0U)
#endif

#ifndef NVM_REQ_NOT_OK
#define NVM_REQ_NOT_OK (1U)
#endif

#ifndef NVM_REQ_PENDING
#define NVM_REQ_PENDING (2U)
#endif

/* BOOT跳转地址 */
#define BOOT_START_FLASH_ADRR                       (0x0000u)   /**< BOOT跳转地址 */

#define FBL_UPDATE_FLAG_CAN                         0xAAAAAAAAul    /**< CAN升级跳转标志 */
#define FBL_UPDATE_FLAG_UART                        0xBBBBBBBBul    /**< UART升级跳转标志 */

#define FBL_UPDATE_FLAG_CAN_HH                      (uint8)(FBL_UPDATE_FLAG_CAN >> 24u)     /**< CAN升级跳转标志bit24~31 */
#define FBL_UPDATE_FLAG_CAN_HL                      (uint8)(FBL_UPDATE_FLAG_CAN >> 16u)     /**< CAN升级跳转标志bit16~23 */
#define FBL_UPDATE_FLAG_CAN_LH                      (uint8)(FBL_UPDATE_FLAG_CAN >> 8u)      /**< CAN升级跳转标志bit8~15 */
#define FBL_UPDATE_FLAG_CAN_LL                      (uint8)(FBL_UPDATE_FLAG_CAN & 0xFFu)    /**< CAN升级跳转标志bit0~7 */

#define FBL_UPDATE_FLAG_UART_HH                     (uint8)(FBL_UPDATE_FLAG_UART >> 24u)    /**< UART升级跳转标志bit24~31 */
#define FBL_UPDATE_FLAG_UART_HL                     (uint8)(FBL_UPDATE_FLAG_UART >> 16u)    /**< UART升级跳转标志bit16~23 */
#define FBL_UPDATE_FLAG_UART_LH                     (uint8)(FBL_UPDATE_FLAG_UART >> 8u)     /**< UART升级跳转标志bit8~15 */
#define FBL_UPDATE_FLAG_UART_LL                     (uint8)(FBL_UPDATE_FLAG_UART & 0xFFu)   /**< UART升级跳转标志bit0~7 */

/********************************数据类型定义**********************************/

/*******************************全局函数声明***********************************/
extern void EELHalInit(void);
extern void EELReadData(uint8 *pData,uint16 u16Address ,uint8 u8Len);
//extern void EELWriteDataBuf(const uint8 *pData,uint16 u16Address ,uint8 u8Len);
extern r_eel_status_t EELWriteDataImmediate(const uint8 *pData,uint16 u16Address ,uint8 u8Len);
extern void EELWriteDataFlash(void);

extern Std_ReturnType NvM_WriteBlock(uint16 BlockId);
extern Std_ReturnType NvM_GetErrorStatus(uint16 BlockId, uint8* RequestResultPtr);
extern Std_ReturnType NvM_SetRamBlockStatus(uint16 BlockId, boolean BlockChanged);

//extern void NvM_MainFunc(uint8 *Lu8TimerCnt);

#endif