/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: Interrupt.h
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/
#ifndef __INTERRUPT_H
#define __INTERRUPT_H

/************************************宏定义************************************/

#define TABLE_ENTRIES 282               /**< 中断向量表的中断源数量 */
#define IRQ_TABLE_START                 0x00000200u//0x00020210u    /**< 中断向量表的起始地址 */

/* 定义各中断源的中断回调函数 */
#define INTRCANGRECC0_CALLBACK                              CANRxIntCallBack    /**< CAN接收中断回调函数定义 */

#define INTRCAN4ERR_CALLBACK                                CAN4ErrIntCallBack    /**< CAN错误中断回调函数定义 */
#define INTRCAN4TRX_CALLBACK                                CAN4TxIntCallBack

#define INTRLIN32UR0_CALLBACK                               UartTxIntCallBack    /**< UART发送中断回调函数定义 */
#define INTRLIN32UR1_CALLBACK                               UartRxIntCallBack    /**< UART接收中断回调函数定义 */
#define INTP0_CALLBACK                                      CANWakeUpIntCallBack    /**< CAN唤醒中断回调函数定义 */
#define INTP3_CALLBACK                                      PowerDownIntCallBack    /**< 电源掉电中断回调函数定义 */
#define INTOSTM0_CALLBACK                                   TimerIntCallBack    /**< 定时中断回调函数定义 */
#define INTADC0SG1_CALLBACK 								AdcHal_Adc0Group1CallBack

#define INTINTP2_CALLBACK 									DSI_Master0_DCR1B_Fall_Cbk
#define INTINTP7_CALLBACK									DSI_Master1_DCR1B_Fall_Cbk
#define INTINTP8_CALLBACK									DSI_Master0_DCR2B_Fall_Cbk
#define INTINTP11_CALLBACK									DSI_Master1_DCR2B_Fall_Cbk
// #define INTCSIH0IC_CALLBACK                                	CSIH0TxIntCallBack    /**< CAN错误中断回调函数定义 */
// #define INTCSIH0IR_CALLBACK                                 CSIH0RxIntCallBack
#define INTCSIH0IRE_CALLBACK                                CSIH0ErrIntCallBack
#define INTCSIH2IRE_CALLBACK                                CSIH2ErrIntCallBack
#define INTCSIH3IRE_CALLBACK                                CSIH3ErrIntCallBack

// #define INTTAUD0I15_CALLBACK                                TAUD0I15IntCallBack

#define INTDMA1_CALLBACK                                	DMA1IntCallBack
#define INTDMA3_CALLBACK                                	DMA3IntCallBack
#define INTDMA5_CALLBACK                                	DMA5IntCallBack
#define INTP2_CALLBACK 									    DSI_Master0_DCR1B_Fall_Cbk
#define INTP7_CALLBACK										DSI_Master1_DCR1B_Fall_Cbk
#define INTP8_CALLBACK										DSI_Master0_DCR2B_Fall_Cbk
#define INTP11_CALLBACK										DSI_Master1_DCR2B_Fall_Cbk

/*******************************全局函数声明***********************************/
/* 回调函数声明 */
extern void CANRxIntCallBack(void);

extern void UartTxIntCallBack(void);
extern void UartRxIntCallBack(void);
extern void CANWakeUpIntCallBack(void);
extern void PowerDownIntCallBack(void);
extern void TimerIntCallBack(void);
extern void AdcHal_Adc0Group1CallBack(void);

extern void CAN4RxIntCallBack(void);
extern void CAN4ErrIntCallBack(void);
extern void CAN4TxIntCallBack(void);

extern void DSI_Master0_DCR1B_Fall_Cbk(void);
extern void DSI_Master1_DCR1B_Fall_Cbk(void);
extern void DSI_Master0_DCR2B_Fall_Cbk(void);
extern void DSI_Master1_DCR2B_Fall_Cbk(void);

extern void CSIH0ErrIntCallBack(void);
extern void CSIH2ErrIntCallBack(void);
extern void CSIH3ErrIntCallBack(void);

//extern void TAUD0I15IntCallBack(void);

extern void DMA1IntCallBack(void);
extern void DMA3IntCallBack(void);
extern void DMA5IntCallBack(void);

/* 中断函数声明 */
extern __interrupt void INTCSIH0IC(void);
extern __interrupt void INTCSIH0IR(void);
extern __interrupt void INTCSIH0IRE(void);
extern __interrupt void INTCSIH2IRE(void);
extern __interrupt void INTCSIH3IRE(void);

extern __interrupt void INTDMA1(void);
extern __interrupt void INTDMA3(void);
extern __interrupt void INTDMA5(void);

extern __interrupt void INTP0(void);
extern __interrupt void INTP3(void);


extern __interrupt void INTRCANGRECC0(void);

//extern __interrupt void INTRCAN3REC(void);
extern __interrupt void INTRCAN4ERR(void);
extern __interrupt void INTRCAN4TRX(void);
extern __interrupt void INTRLIN32UR0(void);
extern __interrupt void INTRLIN32UR1(void);
extern __interrupt void INTOSTM0(void);
extern __interrupt void INTADC0SG1(void);
extern __interrupt void INTINTP2(void); 								
extern __interrupt void INTINTP7(void);
extern __interrupt void INTINTP8(void);
extern __interrupt void INTINTP11(void);

#endif
