/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * CAN_AppSignal_CommonTypes:
 * Created on: 2022-11-22 16:29
 * Original designer: AntonyFang
 ******************************************************************************/

#ifndef CAN_AppSignal_CommonTypes_H
#define CAN_AppSignal_CommonTypes_H

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "types.h"

/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************************
* 设计描述 : CAN信号档位输入定义，
* 信号级别 : 平台级
*******************************************************************************/
typedef enum
{
    CAN_CAR_GEAR_NONE = -1,

    CAN_CAR_GEAR_P,

    CAN_CAR_GEAR_R,

    CAN_CAR_GEAR_N,

    CAN_CAR_GEAR_D,

    CAN_CAR_GEAR_NUM
    
}Car_GearType;


/******************************************************************************
* 设计描述 : 车速输入定义
* 信号级别 : 平台级
*******************************************************************************/
typedef uint16 Car_SpeedType;     /* 应用使用的车速信号，单位0.01km/h */
typedef uint8  Car_SpeedVildType; /* 车速有效性标志 */
typedef float  Car_SpeedType_Phy; /* 车速的物理值，浮点型，单位km/h */
typedef uint16 Car_SnapshotSpeedType;     /* 快照记录使用的车速信号，原始值直接发送 */

/******************************************************************************
* 设计描述 : 轮速脉冲定义，
* 信号级别 : 平台级
*******************************************************************************/
typedef uint16 Car_WheelPulseCntType; 
typedef enum
{
    CAR_WHEEL_DIR_STOP = 0u,

    CAR_WHEEL_DIR_FORWARD,

    CAR_WHEEL_DIR_BACKWARD,

    CAR_WHEEL_DIR_INVALID,    
}Car_WheelPulseDirType;

typedef enum
{
    CAR_FL_WHEEL = 0u,

    CAR_FR_WHEEL,

    CAR_RL_WHEEL,

    CAR_RR_WHEEL,

    CAR_WHEEL_NUM,
}Car_WheelIndexType;

typedef struct
{
    Car_WheelPulseDirType      enuCar_WheelPulseDir;
    Car_WheelPulseCntType      u16Car_WheelPulseCnt;    
}Car_WheelPulseType;


/******************************************************************************
* 设计描述 : 方向盘角度，
* 信号级别 : 平台级
*******************************************************************************/
typedef sint16 Car_StrAngleType; /*单位：deg*/

/******************************************************************************
* 设计描述 : 车身外温度，用于温度补偿
* 信号级别 : 平台级
*******************************************************************************/
typedef sint8 Car_AmbientTempType;/*单位：℃*/



typedef uint16 Car_Time_YearType;
typedef uint8  Car_Time_MonthType;
typedef uint8  Car_Time_DateType;
typedef uint8  Car_Time_HourType;
typedef uint8  Car_Time_MinuteType;
typedef uint8  Car_Time_SecondType;
typedef uint8  Car_Time_WeeksType;
typedef enum
{
    TIME_HOUR_NONE = -1,

    TIME_HOUR_12_H,

    TIME_HOUR_24_H,

    TIME_HOUR_NUM,
}TimeHourModeType;


typedef struct
{
    Car_Time_YearType      u16Year;

    Car_Time_MonthType     u8Month;
    
    Car_Time_DateType      u8Date;

    Car_Time_HourType      u8Hour;

    Car_Time_MinuteType    u8Minute;

    Car_Time_SecondType    u8Second;

    Car_Time_WeeksType     u8Week;
    
    TimeHourModeType       enuHourMod;
        
}Car_TimeType;

/******************************************************************************
* 设计描述 : EPB信号档位输入定义，
* 信号级别 : 平台级
*******************************************************************************/
typedef enum
{
    CAR_EPB_NONE = -1,

    CAR_EPB_RELESED,
    
    CAR_EPB_APPLYED
    
}Car_EPB_StsType;


/******************************************************************************
* 设计描述 : 轮速输入定义
* 信号级别 : 平台级
*******************************************************************************/
typedef uint16 Wheel_SpeedType;

/******************************************************************************
* 设计描述 : 横摆角速度，输入定义
* 信号级别 : 平台级
*******************************************************************************/
typedef float Car_Yaw_RateType;
typedef float Car_Yaw_AngleType;
typedef struct 
{
    /*后轮轴距*/
    float fRear_wheel_axle_length;
    /*前左测量方差*/
    float fFront_left_R;
    /*前右测量方差*/
    float fFront_right_R;
    /*后左测量方差*/
    float fRear_left_R;
    /*后右测量方差*/
    float fRear_right_R;
}Kalmanfilter_Params;

/*************************以下为项目级信号，仅适用于比亚迪APA项目*********************************  */

/******************************************************************************
* 设计描述 : 系统工作模式请求
* 信号级别 : 项目级
*******************************************************************************/
typedef enum
{
    WORK_MODE_NONE = 0u,
    WORK_MODE_CRUISING_REQUEST,
    WORK_MODE_PARKING_REQUEST,
    WORK_MODE_RESERVED
}ADCU_WorkModReqType;

/******************************************************************************
* 设计描述 : APA功能状态反馈
* 信号级别 : 项目级
*******************************************************************************/
typedef enum
{
    APA_STS_NONE = 0u,
    APA_STS_STANDBY,
    APA_STS_SEARCH,
    APA_STS_GUIDANCE,
    APA_STS_ABORT,
    APA_STS_RESERVED
}APA_APA_StatmsType;

/******************************************************************************
* 设计描述 : APA PAS功能开关
* 信号级别 : 项目级
*******************************************************************************/
typedef enum
{
    APA_PAS_OFF = 0u,
    APA_PAS_ON
}APA_PAS_Function_ReqType;

/******************************************************************************
* 设计描述 : 泊车车位ID选择，0x0: No Space,0xFF: Invalid
* 信号级别 : 项目级
*******************************************************************************/
typedef uint8 ADCU_PSL_ParkingSlotID_SelectedType;


/******************************************************************************
* 设计描述 : 控制器时间同步信号（TBD）
* 信号级别 : 项目级
*******************************************************************************/
typedef uint64 ADCU_Work_TimeStampType;


/******************************************************************************
* 设计描述 : 电源档位
* 信号级别 : 项目级
*******************************************************************************/
typedef enum
{
    POWER_GEAR_INVALID = 0u,
    POWER_GEAR_OFF,
    POWER_GEAR_ACC,
    POWER_GEAR_ON,
    POWER_GEAR_RESERVED
}BCMPower_GearType;

/******************************************************************************
* 设计描述 : 侧边距离报警开关状态
* 信号级别 : 项目级
*******************************************************************************/
typedef enum
{
    SDW_SWITCH_INVALID = 0u,
    SDW_SWITCH_OFF,
    SDW_SWITCH_ON,
    SDW_SWITCH_RESERVED
}SDWReq_Switch_StateType;


/******************************************************************************
* 设计描述 : 进入OTA模式需求
* 信号级别 : 项目级
*******************************************************************************/
typedef enum
{
    NO_OTA_MODE_REQ = 0u,
    OTA_MODE_REQ
}Medium_Enter_OTA_Mode_ReqType;

/******************************************************************************
* 设计描述 : ADAS_SYNC_Type
* 信号级别 : 项目级
*******************************************************************************/
typedef enum
{
    ADAS_SYNC_NONE = 0,
    ADAS_SYNC_TSYNC = 0x20,
    ADAS_SYNC_TSYNC_FUP = 0x28
}ADAS_SYNC_TypeType;

/******************************************************************************
* 设计描述 : 时域
* 信号级别 : 项目级
*******************************************************************************/
typedef uint8 ADAS_SYNC_TimeDomainType;

/******************************************************************************
* 设计描述 : 同步溢出时间
* 信号级别 : 项目级
*******************************************************************************/
typedef uint8 ADAS_SYNC_OVSType;

/******************************************************************************
* 设计描述 : 时间网关同步状态
* 信号级别 : 项目级
*******************************************************************************/
typedef enum
{
    ADAS_SYNC_TO_GTM = 0u,
    ADAS_SYNC_TO_SUB_DOMAIN
}ADAS_SYNC_SGWType;

/******************************************************************************
* 设计描述 : ADAS_SYNC_Reserved
* 信号级别 : 项目级
*******************************************************************************/
typedef uint8 ADAS_SYNC_ReservedType;

/******************************************************************************
* 设计描述 : ADAS_SYNC_SyncTime
* 信号级别 : 项目级
*******************************************************************************/
typedef uint32 ADAS_SYNC_SyncTimeType;



/******************************************************************************
* 设计描述 : 域控发出时间同步主时基
* 信号级别 : 项目级
*******************************************************************************/
typedef struct
{
    ADAS_SYNC_TypeType enuADAS_SYNC_Type;
    ADAS_SYNC_TimeDomainType u8ADAS_SYNC_TimeDomain;
    ADAS_SYNC_OVSType u8ADAS_SYNC_OVS;
    ADAS_SYNC_SGWType enuADAS_SYNC_SGW;
    ADAS_SYNC_ReservedType u8ADAS_SYNC_Reserved;
    ADAS_SYNC_SyncTimeType u32ADAS_SYNC_SyncTime;
}ADAS_SYNC_TimeType;


/******************************************************************************
* 设计描述 : 驻车辅助开关软按键
* 信号级别 : 项目级
*******************************************************************************/
typedef enum
{
    SOFT_KEY_INVALID = 0u,
    SOFT_KEY_VALID,
}Park_Auxi_Swch_Soft_KeyType;


/******************************************!!!!!! 以下是理想项目级信号类型定义!!!!!!*******************/

/******************************************************************************
* 设计描述 : PSL 使能信号
* 信号级别 : 项目级
*******************************************************************************/
typedef enum
{
    PSL_ENABLE = 0u,
    PSL_DISABLE,
}ADAS_PSL_EnableStsType;

/******************************************************************************
* 设计描述 : PAS 使能信号
* 信号级别 : 项目级
*******************************************************************************/
typedef enum
{
    PAS_ENABLE = 0u,
    PAS_DISABLE,
}ADAS_PAS_EnableStsType;

/******************************************************************************
* 设计描述 : ADAS_APAStatus信号
* 信号级别 : 项目级
*******************************************************************************/
typedef enum
{
    APA_NONE = 0u,
    APA_STANDBY,
    APA_SEARCH,
    APA_GUIDANCE,
    APA_FAILURE,
    APA_SUMMON,
    APA_RESERVED
}ADAS_APAStatusType;

/******************************************************************************
* 设计描述 : slot_ID选择信号
* 信号级别 : 项目级
*******************************************************************************/
typedef uint8 ADAS_slot_ID_SelectedType;

/******************************************************************************
* 设计描述 : LowVolPwrMdFlag信号
* 信号级别 : 项目级
*******************************************************************************/
typedef enum
{
    LOCAL_MODE = 0u,
    REMOTE_MODE,
}LowVolPwrMdFlagType;

/******************************************************************************
* 设计描述 : LowVolPwrMd 信号
* 信号级别 : 项目级
*******************************************************************************/
typedef enum
{
    POWER_OFF = 0u,
    POWER_ACC,
    POWER_ON,
    POWER_RESERVED,
}LowVolPwrMdType;


/******************************************************************************
* 设计描述 : 车型信息 信号
* 信号级别 : 项目级
*******************************************************************************/
typedef enum
{
    VEHTYPE_X01 = 0u,
    VEHTYPE_X02,
    VEHTYPE_X03,
    VEHTYPE_X04,
    VEHTYPE_W01,
    VEHTYPE_W02,
    VEHTYPE_W03,
    VEHTYPE_W04,
    VEHTYPE_W05,
    VEHTYPE_TYPE_NUM,
    VEHTYPE_NOT_CFG = 0xFF
}VehicleTypeType;

/******************************************************************************
* 设计描述 : 车轮信息
* 设计索引 :
*******************************************************************************/
typedef enum
{
    VEH_WHEEL_21INCH = 0,/*21英寸轮毂*/
    VEH_WHEEL_20INCH,    /*20英寸轮毂*/
    VEH_WHEEL_18INCH,    /*18英寸轮毂*/
    VEH_WHEEL_NONE,
}VehicleWheelType;

/******************************************************************************
* 设计描述 : 车型配置等级
* 设计索引 :
*******************************************************************************/
typedef enum
{
    VEH_NO_MAX = 0,   /*非MAX配置*/
    VEH_MAX,          /*MAX配置*/
}VehicleCfgLevelType;

/******************************************************************************
* 设计描述 : 车型平台
* 设计索引 :
*******************************************************************************/
typedef enum
{
    VEH_X_SERIES = 0,   /*X Platform*/
    VEH_W_SERIES,          /*W Platform*/
}VehiclePlatformType;
typedef enum
{
    LOWPOWER_MODFLAG2_LOCAL = 0u,
    LOWPOWER_MODFLAG2_REMOTE,
    LOWPOWER_MODFLAG2_HOLDING,
    LOWPOWER_MODFLAG2_ADAS,
}LowVolPwrMdFlag2_Type;


/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/
#if 0
    #define SysSigna_Assert(x) LhDevAssert(x)
#else
    #define SysSigna_Assert(x) ((void)0)
#endif

#define CAN_SIG_SPEED_MAX_VALUE     30000u
#define CAN_SIG_INVALID_SPD_VALUE   65535u
#define CAN_SIG_INVALID_PULSE_CNT   65535u
#define CAN_SIG_MAX_PULSE_CNT       65535u  /* 轮速脉冲最大值，超过改值后重新从0计数--理想最大值是65535*/
#define CAN_SIG_INVALID_STR_ANGLE   32767
#define CAN_SIG_DEFAULT_TEM         25     /* 默认温度为25℃ */

#define TIEM_YEAR_MIN               2000
#define TIEM_YEAR_MAX          

#define TIEM_MONTH_MIN              1
#define TIEM_MONTH_MAX              12

#define TIEM_DATE_MIN               1
#define TIEM_DATE_MAX               31

#define TIEM_HOUR_MIN               0
#define TIEM_HOUR_MAX               23

#define TIEM_MINUTE_MIN             0
#define TIEM_MINUTE_MAX             59

#define TIEM_SECONDS_MIN            0
#define TIEM_SECONDS_MAX            59

#define TIEM_WEEK_MIN               1
#define TIEM_WEEK_MAX               7


/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/








#endif /* end of CANSignal_CommonTypes_H */

