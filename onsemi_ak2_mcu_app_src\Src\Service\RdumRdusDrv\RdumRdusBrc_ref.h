/******************************************************************************
 * @file      RdumRdusBrc.h
 * @brief     安森美超声波探头BRC命令
 * <AUTHOR>
 * @date      2025-05-16
 * @note
 *****************************************************************************/

#ifndef __RDUM_RDUS_BRC_H__
#define __RDUM_RDUS_BRC_H__
/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "types.h"
#include "RdumRdusAddr.h"
#include "SpiCom_Cfg.h"
#include "BrcSlotDef.h"
#include "SpiRespDef.h"


/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/
/* BRC命令状态 */
typedef enum
{
    BRC_STATUS_OK = 0,              /* 状态正常 */
    BRC_STATUS_ERROR,               /* 状态错误 */
    BRC_STATUS_TIMEOUT,             /* 状态超时 */
    BRC_STATUS_INVALID              /* 状态无效 */
} BrcStatus_t;

/* BRC命令结果 */
typedef struct
{
    SpiNonReadResp_t NonReadResp;     /* 非读响应 */
    uint8 SlotCount;                 /* slot数量 */
    SlotData_t SlotData[MAX_SLOT_COUNT]; /* slot数据 */
    uint8 SlotSDCrc;              /* 包的CRC8校验值 */
    BrcStatus_t Status;             /* 状态 */
} BrcResult_t;



/******************************************************************************
 * @Const Declaration
 *****************************************************************************/



/******************************************************************************
 * @Variable Declaration
 *****************************************************************************/



/******************************************************************************
 * @Function Declaration
 *****************************************************************************/
/* 获取所有BRC */
BrcStatus_t RdumRdusBrc_GetAllBrc(Dsi3Channel_t Dsi3Ch, uint8 SlotCount, BrcResult_t *Result);

/* 解码BRC结果 */
BrcStatus_t RdumRdusBrc_DecodeBrcResult(BrcResult_t *Result);

/* 获取解压缩后的包络 */
BrcStatus_t RdumRdusBrc_GetDecompressedEnvelope(BrcResult_t *Result, uint8 SlotIndex, DecompressedEnvelope_t *DecompressedEnvelope);

/* 打印BRC结果 */
void RdumRdusBrc_PrintBrcResult(BrcResult_t *Result);

/* 获取单个PDCM响应 */
BrcStatus_t RdumRdusBrc_GetPdcmResp(Dsi3Channel_t Dsi3Ch, uint8 SlotIndex, DecompressedEnvelope_t *DecompressedEnvelope);



#endif /* __RDUM_RDUS_BRC_H__ */
