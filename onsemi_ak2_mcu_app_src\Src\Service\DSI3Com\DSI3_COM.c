/*
 * DSI3_COM.c
 *
 *  Created on: 2021 
 *      Author: 6000021992
 */
#include "DSI3_COM.h"
#include "Queue_CRMResponse.h"
#include "AK2_MCU_Drv.h"

#define TRANS_CRMCMD_SPI_SYNC 1

#pragma location = "R_DMA_DATA"
uint8 CmdTxBuf[14] = {0};

#pragma location = "R_DMA_DATA"
uint8 CmdRxBuf[14] = {0};

typedef DSIReturnType_en (*DSI_MASTERCallBakFunc)(DSI_MASTER_Cfg_str *pG_DSI_MASTER);

typedef struct
{	
	AK2_Pub_FilterCtrl_t FilterCtrl;
	DSIMasterICTempSts_e DSIMasterICTempSts;
}DSIMasterICTempDiag_t;

typedef struct
{
	AK2_Pub_FilterCtrl_t ShortToPwrFilterCtrl;
	AK2_Pub_FilterCtrl_t ShortToGndFilterCtrl;
	DSIPinSts_en DSIPinSts;
}DSIPinStsDiag_t;

DSI_MASTER_Cfg_str G_DSI_MASTER[DSIMasterNum];

#define AUTOBRC_MAXNum 1000u
#define DSI_MASTER_IC_STATE_REG_ERR_MASK (0xA288)
#define DSI_MASTER_IC_STATE_REG_TDMA_MASK (0x0410)

/*CRM 命令头*/
const uint8 GwCRM_CMD_title[CRMCMD_Type_Num]=
{
        [CRM2PDCM]         = 0x1A,
        [CRM_NResponse]    = 0x1A,
        [CRM2Response]     = 0x18,
        [DM]               = 0x48, /** @brief 非广播寻址，第一个寻址脉冲时间间隔 = TbitTime * 32 ,保证所有探头都已经进入寻址模式，寻址脉冲都可接收                  */
        [AUTO_BRC]         = 0x60,
        [ReadCRMResponse]  = 0x20,
        [ReadPDCMResponse] = 0x70,

};


const DSICHLDCRB_SubStatus_en GwCRM_CMD_DCRBSubStatus[CRMCMD_Type_Num]=
{
        [CRM2PDCM]         = DSICHL_WaiteDCRB2AUTOBRC,
        [CRM_NResponse]    = DSICHL_WaiteDCRB2CRMTXEND,
        [CRM2Response]     = DSICHL_WaiteDCRB2READCRMRes,
        [DM]               = DSICHL_WaiteDCRB2DMEND,
        [AUTO_BRC]         = DSICHL_WaiteDCRB2READPDCM,
        [ReadCRMResponse]  = DSICHL_WaiteDCRB2READCRMRes,
        [ReadPDCMResponse] = DSICHL_WaiteDCRB2READPDCM,

};

const uint16 GwDCRBTimeOut[DSICHL_SubStatus_NUM]=
{
        [DSICHL_WaiteDCRB_NULL]         = 0,
        [DSICHL_WaiteDCRB2READCRMRes]   = TimeOutDCRB2READCRMRes,
        [DSICHL_WaiteDCRB2AUTOBRC]      = TimeOutDCRB2AUTOBRC,
        [DSICHL_WaiteDCRB2READPDCM]     = TimeOutDCRB2READPDCM,
        [DSICHL_WaiteDCRB2CRMTXEND]     = TimeOutDCRB2CRMTXEND,
        [DSICHL_WaiteDCRB2DMEND]        = TimeOutDCRB2DMEND,


};



const uint16 GwDSIWorkTimeOut[DSICHL_WorkStatus_NUM]=
{
        [DSICHL_INIT]         = 0,
        [DSICHL_DIAG]         = 0,
        [DSICHL_DM]           = TimeOutDSI_DM,
        [DSICHL_IDLE]         = 0,
        [DSICHL_CRM]          = TimeOutDSI_CRM,
        [DSICHL_PDCM]         = TimeOutDSI_PDCM,
        [DSICHL_Sleep]        = 0,

        [DSICHL_CMD_STOP2IDLE]    = TimeOutDSI_STOP2IDLE,
        [DSICHL_CMD_DONE]    = TimeOutDSI_DONE,


};
extern const uint16 RegINIT_Check[19];



/*读写 521.42 保存地址 */
uint16 GWMasterRegData[DSIMasterNum][WR_RegNum]={0};
DSIMasterICTempDiag_t DSIMasterICTempDiag[DSIMasterNum] = {0};
DSIPinStsDiag_t DSIPinStsDiag[DSIMasterNum][DSIChlNum] = {0};
AK2_Pub_FilterCtrl_t DCRBTimeOutFilterCtrl[DSIMasterNum][DSIChlNum] = {0};

/***************************************************************************//**
 * @brief
 * 设置DSI DCRB事件 状态
 *
 * @param[in]        DSIMasterID
 * @param[in]        DSIChlSEL
 * @param       DSICHL_WorkStatus   DSI工作模式
 * @return      eDSIReturnType_OK          数据有效，且写入成功
 *              eDSIReturnType_N_OK               else
 *
 * ****************************************************************************/
DSIReturnType_en SetDSICHLWorkStatus(DSIMasterID_en DSIMasterID,DSIChlID_en DSIChlID,DSICHL_WorkStatus_en DSICHL_WorkStatus)
{
    DSI_Chl_Ctrl_STATUS_Str *pDSICHLCtrlStatus;

    if((DSIMasterID >= DSIMasterNum) || (DSIChlID >= DSIChlNum))
    {
        return eDSIReturnType_InData_ERR;
    }
    pDSICHLCtrlStatus = &G_DSI_MASTER[DSIMasterID].MasterCtrl.DSICHLCtrlStatus[DSIChlID];
    
    pDSICHLCtrlStatus->DSICHLWorkStatus = DSICHL_WorkStatus;
    pDSICHLCtrlStatus->DSIWorkTimeOutCnt = 0;

    return eDSIReturnType_OK;
}


/***************************************************************************//**
 * @brief
 * 读取DSI CHL工作状态,如果当前状态为DSICHL_CMD_DONE，调用该函数后工作状态置为DSICHL_IDLE
 *
 * @param[in]        DSIMasterID
 * @param[in]        DSIChlSEL
 * @param       DSICHL_WorkStatus   DSI工作模式
 * @return      DSICHL_WorkStatus   返回当前DSICHL工作状态，
 *
 *
 * ****************************************************************************/
DSICHL_WorkStatus_en GetDSICHLWorkStatus(DSIMasterID_en DSIMasterID,DSIChlID_en DSIChlID)
{
    DSI_Chl_Ctrl_STATUS_Str *pDSICHLCtrlStatus;
    DSICHL_WorkStatus_en DSICHL_WorkStatus;

    if((DSIMasterID >= DSIMasterNum) || (DSIChlID >= DSIChlNum))
    {
        return DSICHL_WorkStatus_NULL;
    }
    pDSICHLCtrlStatus = &G_DSI_MASTER[DSIMasterID].MasterCtrl.DSICHLCtrlStatus[DSIChlID];
    

    
    DSICHL_WorkStatus = pDSICHLCtrlStatus->DSICHLWorkStatus;
    if(DSICHL_WorkStatus == DSICHL_CMD_DONE)
    {
        pDSICHLCtrlStatus->DSICHLWorkStatus = DSICHL_IDLE;
        DSICHL_WorkStatus = DSICHL_IDLE;
    }
    return DSICHL_WorkStatus;
}

/***************************************************************************//**
 * @brief
 * 设置DSI DCRB事件 状态
 *
 * @param[in]        DSIMasterID
 * @param[in]        DSIChlSEL
 * @param       DCRB_SubStatus   DCRB事件 状态
 * @return      eDSIReturnType_OK          数据有效，且写入成功
 *              eDSIReturnType_N_OK               else
 *
 * ****************************************************************************/
DSIReturnType_en SetDSIDCRBSubStatus(DSIMasterID_en DSIMasterID,DSIChlID_en DSIChlID,DSICHLDCRB_SubStatus_en DCRB_SubStatus)
{
    DSI_Chl_Ctrl_STATUS_Str *pDSICHLCtrlStatus;
    DSICOM_SetParam_st *pDSICOM_SetParam;

    if((DSIMasterID >= DSIMasterNum) || (DSIChlID >= DSIChlNum))
    {
        return eDSIReturnType_N_OK;
    }
    pDSICHLCtrlStatus = &G_DSI_MASTER[DSIMasterID].MasterCtrl.DSICHLCtrlStatus[DSIChlID];
    pDSICOM_SetParam = &G_DSI_MASTER[DSIMasterID].DSICOM_SetParam;

    pDSICHLCtrlStatus->DSIDCRBSubStatus = DCRB_SubStatus;
    pDSICHLCtrlStatus->DCRBTimeOutCnt = 0;
    if(DCRB_SubStatus == DSICHL_WaiteDCRB_NULL)
    {
        if(DSIChlID == DSIChl1)
        {
            pDSICOM_SetParam->DisDCR1BIntFunc();
        }
        else
        {
            pDSICOM_SetParam->DisDCR2BIntFunc();
        }
    }
    return eDSIReturnType_OK;
}

/***************************************************************************//**
 * @brief
 * 获取DSI DCRB事件 状态
 *
 * @param[in]        DSIMasterID
 * @param[in]        DSIChlSEL
 * @return      DCRBSubStatus
 *
 *
 * ****************************************************************************/
DSICHLDCRB_SubStatus_en GetDSIDCRBSubStatus(DSIMasterID_en DSIMasterID,DSIChlID_en DSIChlID)
{
    DSI_Chl_Ctrl_STATUS_Str *pDSICHLCtrlStatus;

    if((DSIMasterID >= DSIMasterNum) || (DSIChlID >= DSIChlNum))
    {
        return DSICHL_WaiteDCRB_NULL;
    }
    pDSICHLCtrlStatus = &G_DSI_MASTER[DSIMasterID].MasterCtrl.DSICHLCtrlStatus[DSIChlID];

    return pDSICHLCtrlStatus->DSIDCRBSubStatus;
}

/***************************************************************************//**
 * @brief
 * 获取IC故障状态
 *
 * @param   DSIMasterID_en    
 * @return  IC_ERRStatus_str     
 *
 *
 * ****************************************************************************/
IC_ERRStatus_str GetICErrStatus(DSIMasterID_en DSIMasterID)
{
    return G_DSI_MASTER[DSIMasterID].IC_ERRStatus; 
}

/***************************************************************************//**
 * @brief
 * 清除IC故障状态
 *
 * @param     DSIMasterID_en  
 * @return    DSIReturnType_en   
 *
 *
 * ****************************************************************************/
DSIReturnType_en ClearICErrStatus(DSIMasterID_en DSIMasterID)
{
  
    memset(G_DSI_MASTER[DSIMasterID].IC_ERRStatus.DSI_ChlErr,0,sizeof(DSI_ChlErr_str) * DSIChlNum);

    return eDSIReturnType_OK;
}

/***************************************************************************//**
 * @brief
 * 设置DSI CRM指令
 *
 * @param[in]        DSIMasterID
 * @param[in]        DSIChlSEL
 * @param       DCRB_SubStatus   DCRB事件 状态
 * @return      eDSIReturnType_OK          数据有效，且写入成功
 *              eDSIReturnType_N_OK               else
 *
 * ****************************************************************************/
DSIReturnType_en SetDSIActiveCRM_CMD(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL,CRMCMD_Type_en CMD_type)
{
    ActiveCRM_Msg_str *pActiveCRM_Msg;
    DSIChlID_en LcDSIChlID;
    
    DSI_MASTER_CTRL_Str *pDSI_MASTER_CTRL;

    if((DSIMasterID >= DSIMasterNum))
    {
        return eDSIReturnType_InData_ERR;
    }
    pDSI_MASTER_CTRL = &G_DSI_MASTER[DSIMasterID].MasterCtrl;
    
    for(LcDSIChlID=DSIChl1;LcDSIChlID<DSIChlNum;LcDSIChlID++)
    {
        if((DSIChlSEL >> LcDSIChlID) & 0x01)
        {
            pActiveCRM_Msg = &pDSI_MASTER_CTRL->DSICHLCtrlStatus[LcDSIChlID].ActiveCRM_Msg;
            pActiveCRM_Msg->CMD_type = CMD_type;
            pActiveCRM_Msg->DSIMasterID = DSIMasterID;
            pActiveCRM_Msg->DSIChlSEL = DSIChlSEL;
        }
    }

    return eDSIReturnType_OK;
}


/******************************************************************************/
/**
 * @brief            CRM命令组包,SPI数据传输
 * @param[in]        DSIMasterID
 * @param[in]        DSIChlSEL
 * @param[in]        CMD_type
 * @param[in]        *pInCRMData
 *
 * @return      0xFF          无空闲SPI_Seq或输入参数错误/返回数据与传输数据不一致
 *              SeqID
 */
/*****************************************************************************/

DSIReturnType_en TransCRMCmd(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL,CRMCMD_Type_en CMD_type,TRANS_CRM_DM_Str *pInCRMData,uint32 *pTransCompleteTime)
{
    uint16 wCrc = 0;
    uint8 LcCrcSize = 10;
    uint8 LcTxSize = 14;
    uint8 LcICFlg = 0x04;
    uint8 DSICHl2DatOffset = 0;
    DSISPI_SeqID SeqID = DSISPI_SEQ_NULL;
    DSIReturnType_en Lreturn = eDSIReturnType_OK;
    
    DSICOM_SetParam_st *pDSICOM_SetParam;
	
    memset((void*)&CmdTxBuf[0],0,14);
#if (TRANS_CRMCMD_SPI_SYNC==1)
        /** @brief CRMCMD 使用SPI同步传输 */
        uint8 Lcnt;
		memset((void*)&CmdRxBuf[0],0,14);

#endif

    if((pInCRMData == NULL)
        || (CMD_type > DM)
        || (DSIChlSEL > DSIChlSEL1And2)
        || (DSIChlSEL == DSI_CHL_NONE))
    {
        return eDSIReturnType_InData_ERR;
    }
        
    pDSICOM_SetParam = &G_DSI_MASTER[DSIMasterID].DSICOM_SetParam;

    /** @brief 处理数据长度 */
    if(DSIChlSEL == DSIChlSEL1And2)
    {
        LcCrcSize = 10;
        LcTxSize = 14;
        LcICFlg = 0x04;
        DSICHl2DatOffset = 4;
    }
    else
    {
        LcCrcSize = 6;
        LcTxSize = 10;
        LcICFlg = 0x00;
        DSICHl2DatOffset = 0;
    }
    
    /** @brief 填充SPI数据 */
    
    CmdTxBuf[0] = GwCRM_CMD_title[CMD_type] | LcICFlg;

    CmdTxBuf[1] = DSIChlSEL;

    if(DSIChlSEL & 0x01)
    {
        /** @brief DSI非空闲返回则错误 */
        if(GetDSICHLWorkStatus(DSIMasterID,DSIChl1) != DSICHL_IDLE)
        {
            return eDSIReturnType_N_OK;
        }

        memcpy(&CmdTxBuf[2],&pInCRMData->CRMData[0],sizeof(CRM_CMD_Str));
        
        /** @brief DSI及DCRB工作状态  */
        SetDSICHLWorkStatus(DSIMasterID,DSIChl1,DSICHL_CRM);
        SetDSIDCRBSubStatus(DSIMasterID,DSIChl1,GwCRM_CMD_DCRBSubStatus[CMD_type]);
        
        /*使用DCRB 1中断，处理FCC传输完成中断*/
        pDSICOM_SetParam->EnDCR1BFallIntFunc();
    }
    
    if(DSIChlSEL & 0x02)
    {
        /** @brief DSI非空闲返回则错误 */
        if (GetDSICHLWorkStatus(DSIMasterID,DSIChl2) != DSICHL_IDLE)
        {
            return eDSIReturnType_N_OK;
        }
        
        memcpy(&CmdTxBuf[2 + DSICHl2DatOffset],&pInCRMData->CRMData[1],sizeof(CRM_CMD_Str));

        /** @brief DSI及DCRB工作状态  */
        SetDSICHLWorkStatus(DSIMasterID,DSIChl2,DSICHL_CRM);
        SetDSIDCRBSubStatus(DSIMasterID,DSIChl2,GwCRM_CMD_DCRBSubStatus[CMD_type]);
        
        /*使用DCRB 2中断，处理FCC传输完成中断*/
        pDSICOM_SetParam->EnDCR2BFallIntFunc();
    }
    

    /** @brief 计算CRC */

    wCrc = DSI_SPI_CRC16Calculate(CmdTxBuf,LcCrcSize);

    CmdTxBuf[LcCrcSize] = wCrc >> 8;
    CmdTxBuf[LcCrcSize + 1] = wCrc & 0xFF;


    /** @brief 设置当前CRM指令 */
    SetDSIActiveCRM_CMD(DSIMasterID,DSIChlSEL, CMD_type);
    
    
    /** @brief 发送SPI */

#if (TRANS_CRMCMD_SPI_SYNC==1)
        
    /** @brief CRMCMD 使用SPI同步传输 */
	
    SeqID = DSI_SPI_SyncTransfer ((uint8)DSIMasterID,CmdTxBuf,LcTxSize,CmdRxBuf,2);

    /** @brief CRMCMD 使用SPI同步传输 */
    if(SeqID != DSISPI_SEQ_NULL)
    {

        LcTxSize -= 2;/** @brief 比较返回数据长度=传输数据长度-2         */
        
        for(Lcnt=0;Lcnt<LcTxSize;Lcnt++)
        {
            if(CmdTxBuf[Lcnt] != CmdRxBuf[Lcnt + 2])
            {
                /** @brief 写返回不一致 */
                Lreturn = eDSIReturnType_Resp_Data_N_OK;
                PRINTF_DSI3COM("TransCRMCmd NG Resp_Data_N_OK \n");
                break;
            }
        }
    }

	if(pTransCompleteTime != NULL)
	{
		*pTransCompleteTime = GetDSI_SPI_SeqTransCompleteTime(DSIMasterID,SeqID);
	}
#else
    
    SeqID = DSI_SPI_AsyncTransfer ((uint8)DSIMasterID,CmdTxBuf,LcTxSize);

#endif


    if(SeqID == DSISPI_SEQ_NULL)
    {
        Lreturn = eDSIReturnType_N_OK;
    }

    return Lreturn;
}

/******************************************************************************/
/**
 * @brief            CRM命令组包,SPI数据异步传输
 * @param[in]        DSIMasterID
 * @param[in]        DSIChlSEL
 * @param[in]        CMD_type
 * @param[in]        *pInCRMData
 *
 * @return      0xFF          无空闲SPI_Seq或输入参数错误/返回数据与传输数据不一致
 *              SeqID
 */
/*****************************************************************************/
DSIReturnType_en TransCRMCmdSPIAsyn(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL,CRMCMD_Type_en CMD_type,TRANS_CRM_DM_Str *pInCRMData)
{
    uint16 wCrc = 0;
    uint8 TxBuf[14] = {0,0,0,0,0,0,0,0,0,0,0,0,0,0};
    uint8 LcCrcSize = 10;
    uint8 LcTxSize = 14;
    uint8 LcICFlg = 0x04;
    uint8 DSICHl2DatOffset = 0;
    DSISPI_SeqID SeqID = DSISPI_SEQ_NULL;
    DSIReturnType_en Lreturn = eDSIReturnType_OK;
    
    DSICOM_SetParam_st *pDSICOM_SetParam;

    if((pInCRMData == NULL)
        || (CMD_type > DM)
        || (DSIChlSEL > DSIChlSEL1And2)
        || (DSIChlSEL == DSI_CHL_NONE))
    {
        return eDSIReturnType_InData_ERR;
    }
        
    pDSICOM_SetParam = &G_DSI_MASTER[DSIMasterID].DSICOM_SetParam;

    /** @brief 处理数据长度 */
    if(DSIChlSEL == DSIChlSEL1And2)
    {
        LcCrcSize = 10;
        LcTxSize = 14;
        LcICFlg = 0x04;
        DSICHl2DatOffset = 4;
    }
    else
    {
        LcCrcSize = 6;
        LcTxSize = 10;
        LcICFlg = 0x00;
        DSICHl2DatOffset = 0;
    }
    
    /** @brief 填充SPI数据 */
    
    TxBuf[0] = GwCRM_CMD_title[CMD_type] | LcICFlg;

    TxBuf[1] = DSIChlSEL;

    if(DSIChlSEL & 0x01)
    {
        /** @brief DSI非空闲返回则错误 */
        if(GetDSICHLWorkStatus(DSIMasterID,DSIChl1) != DSICHL_IDLE)
        {
            return eDSIReturnType_N_OK;
        }

        memcpy(&TxBuf[2],&pInCRMData->CRMData[0],sizeof(CRM_CMD_Str));
        
        /** @brief DSI及DCRB工作状态  */
        SetDSICHLWorkStatus(DSIMasterID,DSIChl1,DSICHL_CRM);
        SetDSIDCRBSubStatus(DSIMasterID,DSIChl1,GwCRM_CMD_DCRBSubStatus[CMD_type]);
        
        /*使用DCRB 1中断，处理FCC传输完成中断*/
        pDSICOM_SetParam->EnDCR1BFallIntFunc();
    }
    
    if(DSIChlSEL & 0x02)
    {
        /** @brief DSI非空闲返回则错误 */
        if (GetDSICHLWorkStatus(DSIMasterID,DSIChl2) != DSICHL_IDLE)
        {
            return eDSIReturnType_N_OK;
        }
        
        memcpy(&TxBuf[2 + DSICHl2DatOffset],&pInCRMData->CRMData[1],sizeof(CRM_CMD_Str));

        /** @brief DSI及DCRB工作状态  */
        SetDSICHLWorkStatus(DSIMasterID,DSIChl2,DSICHL_CRM);
        SetDSIDCRBSubStatus(DSIMasterID,DSIChl2,GwCRM_CMD_DCRBSubStatus[CMD_type]);
        
        /*使用DCRB 2中断，处理FCC传输完成中断*/
        pDSICOM_SetParam->EnDCR2BFallIntFunc();
    }
    

    /** @brief 计算CRC */

    wCrc = DSI_SPI_CRC16Calculate(TxBuf,LcCrcSize);

    TxBuf[LcCrcSize] = wCrc >> 8;
    TxBuf[LcCrcSize + 1] = wCrc & 0xFF;


    /** @brief 设置当前CRM指令 */
    SetDSIActiveCRM_CMD(DSIMasterID,DSIChlSEL, CMD_type);
    
    /** @brief 发送SPI */ 
    SeqID = DSI_SPI_AsyncTransfer ((uint8)DSIMasterID,TxBuf,LcTxSize);

    if(SeqID == DSISPI_SEQ_NULL)
    {
        Lreturn = eDSIReturnType_N_OK;
    }

    return Lreturn;
}

/******************************************************************************/
/**
 * @brief            ReadCRM命令组包,SPI数据传输
 * @param[in]        DSIMasterID
 * @param[in]        DSIChlSEL
 *
 * @return           0xFF          无空闲SPI_Seq或输入参数错误
 *                   SeqID
 */
/*****************************************************************************/
DSIReturnType_en TransReadCRMResp(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL)
{
    uint8 TxBuf[14] = {0,0,0,0,0,0,0,0,0,0,0,0,0,0};
    uint8 LcTxSize = 18;
    DSISPI_SeqID SeqID = DSISPI_SEQ_NULL;
    if((DSIChlSEL > DSIChlSEL1And2) || DSIChlSEL == DSI_CHL_NONE)
    {
        return eDSIReturnType_InData_ERR;
    }
    
    /** @brief 填充SPI指令 */

    TxBuf[0] = GwCRM_CMD_title[ReadCRMResponse];

    TxBuf[1] = DSIChlSEL;
    
    /** @brief 计算读CRM Response数据长度 */

    if(DSIChlSEL != DSIChlSEL1And2)
    {
        LcTxSize = 12;
    }
    else
    {
        LcTxSize = 18;

    }

    /*发送SPI*/
    SeqID = DSI_SPI_AsyncTransfer ((uint8)DSIMasterID,TxBuf,LcTxSize);

    
    if(SeqID == DSISPI_SEQ_NULL)
    {
        return eDSIReturnType_N_OK;
    }
    else
    {
        return eDSIReturnType_OK;

    }
    
}

/******************************************************************************/
/**
 * @brief            ReadPDCMResp命令组包,SPI数据传输
 * @param[in]        DSIMasterID
 * @param[in]        DSIChlSEL
 *
 * @return           0xFF          无空闲SPI_Seq或输入参数错误
 *                   SeqID
 */
/*****************************************************************************/
DSIReturnType_en TransReadPDCMResp(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL)
{
    uint8 TxBuf[2] = {0,0};
    uint8 LcTxSize = 14;

    uint8 cPdcmSize = 0;
    DSI_MASTER_Cfg_str *pDSI_MASTER;

    pDSI_MASTER = &G_DSI_MASTER[DSIMasterID];

    DSISPI_SeqID SeqID = DSISPI_SEQ_NULL;
    if((DSIChlSEL > DSIChlSEL1And2) || DSIChlSEL == DSI_CHL_NONE)
    {
        return eDSIReturnType_InData_ERR;
    }
    
    /** @brief 填充SPI指令 */

    TxBuf[0] = GwCRM_CMD_title[ReadPDCMResponse];

    TxBuf[1] = DSIChlSEL;

    /** @brief 计算读PDCM数据长度 */
    if(DSIChlSEL == DSIChlSEL1And2)
    {
        cPdcmSize = pDSI_MASTER->DSICfg[0].cPdcmFrameSize + pDSI_MASTER->DSICfg[1].cPdcmFrameSize;

    }
    else
    {
        if(DSIChlSEL == DSIChlSEL1)
        {
            cPdcmSize = pDSI_MASTER->DSICfg[0].cPdcmFrameSize ;
        }
        else
        {
            cPdcmSize = pDSI_MASTER->DSICfg[1].cPdcmFrameSize ;
        }

    }

    LcTxSize = cPdcmSize + 8;


    /*发送SPI*/
    SeqID = DSI_SPI_AsyncTransfer ((uint8)DSIMasterID,TxBuf,LcTxSize);
    
    if(SeqID == DSISPI_SEQ_NULL)
    {
        return eDSIReturnType_N_OK;
    }
    else
    {
        return eDSIReturnType_OK;

    }

}

/******************************************************************************/
/**
 * @brief            清除SPI CRC错误次数
 * @param[in]        DSIMasterID
 *
 * @return           None
 */
/*****************************************************************************/
void ClearSPIRxCrcErrCnt(DSIMasterID_en DSIMasterID)
{
	DSI_MASTER_Cfg_str *pDSI_MASTER;

    pDSI_MASTER = &G_DSI_MASTER[DSIMasterID];

	pDSI_MASTER->MasterCtrl.SPIRxCrcErrCnt = 0;
}

/******************************************************************************/
/**
 * @brief            获取SPI CRC错误次数
 * @param[in]        DSIMasterID
 *
 * @return           CRC错误次数错误次数
 */
/*****************************************************************************/
uint32 GetSPIRxCrcErrCnt(DSIMasterID_en DSIMasterID)
{
	DSI_MASTER_Cfg_str *pDSI_MASTER;

    pDSI_MASTER = &G_DSI_MASTER[DSIMasterID];

	return pDSI_MASTER->MasterCtrl.SPIRxCrcErrCnt;
}

/******************************************************************************/
/**
 * @brief            设置AutoBRC个数
 * @param[in]        DSIMasterID
 * @param[in]        DSIChlSEL
 * @param[in]        MaxBRCNum
 *
 * @return           DSIChlSEL
 */
/*****************************************************************************/

DSIChlSel_en SetAutoBRCPart(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL,uint16 MaxBRCNum)
{
    DSIChlID_en LcDSIChlID;
    DSI_MASTER_Cfg_str *pDSI_MASTER;
    uint16 BRCNum = AUTOBRC_MAXNum;

    pDSI_MASTER = &G_DSI_MASTER[DSIMasterID];

    for(LcDSIChlID=DSIChl1;LcDSIChlID<DSIChlNum;LcDSIChlID++)
    {
        if((DSIChlSEL >> LcDSIChlID) & 0x01)
        {
            if(MaxBRCNum == 0)
            {
                BRCNum = AUTOBRC_MAXNum;
            }
            else
            {
                BRCNum = MaxBRCNum;
            }
            pDSI_MASTER->MasterCtrl.DSICHLCtrlStatus[LcDSIChlID].ActiveCRMCMDStatus.AutoBRCMaxCnt = BRCNum;
            pDSI_MASTER->MasterCtrl.DSICHLCtrlStatus[LcDSIChlID].ActiveCRMCMDStatus.ExpectBRCTotal = BRCNum;
            pDSI_MASTER->MasterCtrl.DSICHLCtrlStatus[LcDSIChlID].ActiveCRMCMDStatus.wFrameCnt = 0;

        }
    }
    return DSIChlSEL;
}

/******************************************************************************/
/**
 * @brief            获取预期BRC个数
 * @param[in]        DSIMasterID
 * @param[in]        DSIChlID
 *
 * @return           ExpectBRCTotal
 */
/*****************************************************************************/

uint16 GetExpectBRCNum(DSIMasterID_en DSIMasterID,DSIChlID_en DSIChlID)
{
    DSI_MASTER_Cfg_str *pDSI_MASTER;
    
    pDSI_MASTER = &G_DSI_MASTER[DSIMasterID];

    return pDSI_MASTER->MasterCtrl.DSICHLCtrlStatus[DSIChlID].ActiveCRMCMDStatus.ExpectBRCTotal;
}
/******************************************************************************/
/**
 * @brief            AutoBRC命令组包,SPI数据传输
 * @param[in]        DSIMasterID
 * @param[in]        DSIChlSEL
 * @param[in]        LcBRCNum
 *
 * @return      0xFF          无空闲SPI_Seq或输入参数错误
 *              SeqID
 */
/*****************************************************************************/
DSIReturnType_en TransStartAutoBRC(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL,uint8 LcBRCNum)
{
    uint16 wCrc = 0;
    uint8 TxBuf[8] = {0,0,0,0,0,0,0,0};

    uint8 LcTxSize = 8;
    DSISPI_SeqID SeqID = DSISPI_SEQ_NULL;

    DSICOM_SetParam_st *pDSICOM_SetParam;

    if((DSIChlSEL > DSIChlSEL1And2) || DSIChlSEL == DSI_CHL_NONE)
    {
        return eDSIReturnType_InData_ERR;
    }
    
    pDSICOM_SetParam = &G_DSI_MASTER[DSIMasterID].DSICOM_SetParam;

    if(DSIChlSEL & 0x01)
    {
        /** @brief DSI及DCRB工作状态  */
        SetDSICHLWorkStatus(DSIMasterID,DSIChl1,DSICHL_PDCM);
        SetDSIDCRBSubStatus(DSIMasterID,DSIChl1,GwCRM_CMD_DCRBSubStatus[AUTO_BRC]);
        
        /*使用DCRB 1中断，处理PDCM 中断*/
        pDSICOM_SetParam->EnDCR1BFallIntFunc();
    }
    
    if(DSIChlSEL & 0x02)
    {
    
        /** @brief DSI及DCRB工作状态  */
        SetDSICHLWorkStatus(DSIMasterID,DSIChl2,DSICHL_PDCM);
        SetDSIDCRBSubStatus(DSIMasterID,DSIChl2,GwCRM_CMD_DCRBSubStatus[AUTO_BRC]);
        
        /*使用DCRB 2中断，处理PDCM 中断*/
        pDSICOM_SetParam->EnDCR2BFallIntFunc();
    }

    TxBuf[0] = GwCRM_CMD_title[AUTO_BRC];

    TxBuf[1] = DSIChlSEL;

    TxBuf[3] = LcBRCNum;

    wCrc = DSI_SPI_CRC16Calculate(TxBuf,4);

    TxBuf[4] = wCrc >> 8;
    TxBuf[5] = wCrc & 0xFF;

    /*发送SPI*/
    SeqID = DSI_SPI_AsyncTransfer ((uint8)DSIMasterID,TxBuf,LcTxSize);

    if(SeqID == DSISPI_SEQ_NULL)
    {
        return eDSIReturnType_N_OK;
    }
    else
    {
        return eDSIReturnType_OK;

    }

}




/******************************************************************************/
/**
 * @brief            传输新增的AutoBRC个数
 * @param[in]        DSIMasterID
 * @param[in]        DSIChlSEL
 * @param[in]        LwAddBRCNum
 *
 * @return      0xFF          无空闲SPI_Seq或输入参数错误
 *              SeqID
 */
/*****************************************************************************/
DSIReturnType_en TransAddAutoBRCNum(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL,uint16 LwAddBRCNum)
{

    DSIChlID_en LcDSIChlID;
    DSI_MASTER_Cfg_str *pDSI_MASTER;
    uint16 BRCNum = 0;
    DSIReturnType_en ReturnType = eDSIReturnType_OK;

    pDSI_MASTER = &G_DSI_MASTER[DSIMasterID];

    for(LcDSIChlID=DSIChl1;LcDSIChlID<DSIChlNum;LcDSIChlID++)
    {
        if((DSIChlSEL >> LcDSIChlID) & 0x01)
        {

            if(pDSI_MASTER->MasterCtrl.DSICHLCtrlStatus[LcDSIChlID].ActiveCRMCMDStatus.AutoBRCMaxCnt 
                >= pDSI_MASTER->MasterCtrl.DSICHLCtrlStatus[LcDSIChlID].ActiveCRMCMDStatus.wFrameCnt)
            {
                BRCNum = pDSI_MASTER->MasterCtrl.DSICHLCtrlStatus[LcDSIChlID].ActiveCRMCMDStatus.AutoBRCMaxCnt 
                    - pDSI_MASTER->MasterCtrl.DSICHLCtrlStatus[LcDSIChlID].ActiveCRMCMDStatus.wFrameCnt;

            }
            else
            {
                BRCNum = 0;
            }
                
            if(LwAddBRCNum == 0)
            {
                BRCNum += AUTOBRC_MAXNum;
                
                pDSI_MASTER->MasterCtrl.DSICHLCtrlStatus[LcDSIChlID].ActiveCRMCMDStatus.ExpectBRCTotal += AUTOBRC_MAXNum;
            }
            else
            {
                BRCNum += LwAddBRCNum;
                
                pDSI_MASTER->MasterCtrl.DSICHLCtrlStatus[LcDSIChlID].ActiveCRMCMDStatus.ExpectBRCTotal += LwAddBRCNum;
            }
            
            pDSI_MASTER->MasterCtrl.DSICHLCtrlStatus[LcDSIChlID].ActiveCRMCMDStatus.AutoBRCMaxCnt = BRCNum;
            pDSI_MASTER->MasterCtrl.DSICHLCtrlStatus[LcDSIChlID].ActiveCRMCMDStatus.wFrameCnt = 0;
            

            if(BRCNum > 255)
            {
                BRCNum = 0;
            }
            else
            {

            }
            
            /*发送SPI*/
            ReturnType |= TransStartAutoBRC(DSIMasterID, (DSIChlSel_en)(LcDSIChlID + 1),BRCNum);
        }
    }

    return ReturnType;

}


/******************************************************************************/
/**
 * @brief            StopDSI命令,SPI数据传输
 * @param[in]        DSIMasterID
 * @param[in]        DSIChlSEL
 *
 * @return      0xFF          无空闲SPI_Seq或输入参数错误/521.42 SPI返回数据与传输数据不一致
 *              SeqID
 */
/*****************************************************************************/
DSIReturnType_en TransStopDSI(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL)
{
    uint16 TxBuf = 0x0001;

    DSIReturnType_en ReturnType = eDSIReturnType_OK;
    if((DSIChlSEL > DSIChlSEL1And2) || DSIChlSEL == DSI_CHL_NONE)
    {
        return eDSIReturnType_InData_ERR;
    }
    /*传输SPI*/

    if(DSIChlSEL & 0x01)
    {
        /** @brief 传输STOP DSI指令 */
        ReturnType = TransWMasterDSIReg(DSIMasterID,W_DSI1_MODE,TxBuf);
        SetDSIDCRBSubStatus(DSIMasterID,DSIChl1,DSICHL_WaiteDCRB_NULL);
        SetDSICHLWorkStatus(DSIMasterID,DSIChl1,DSICHL_CMD_STOP2IDLE);
        
        if(ReturnType != eDSIReturnType_OK)
        {
            return ReturnType;
        }
        else
        {
        
        
        }

    }
    if(DSIChlSEL & 0x02)
    {
    
        /** @brief 传输STOP DSI指令 */
        ReturnType = TransWMasterDSIReg(DSIMasterID,W_DSI2_MODE,TxBuf);
        SetDSIDCRBSubStatus(DSIMasterID,DSIChl2,DSICHL_WaiteDCRB_NULL);
        SetDSICHLWorkStatus(DSIMasterID,DSIChl2,DSICHL_CMD_STOP2IDLE);
        
        if(ReturnType != eDSIReturnType_OK)
        {
            return ReturnType;
        }
        else
        {
        
        
        }

    }



    return ReturnType;

}

void DSIMasterDiagInit(DSIMasterID_en DSIMasterID)
{
    DSIChlID_en DSICHlID = DSIChl1;
	
	DSIMasterICTempDiag[DSIMasterID].DSIMasterICTempSts = DSIMasterIC_Temperature_Normal;
	DSIMasterICTempDiag[DSIMasterID].FilterCtrl.UpperLimit = 3;
	DSIMasterICTempDiag[DSIMasterID].FilterCtrl.LowerLimit = 0;
	DSIMasterICTempDiag[DSIMasterID].FilterCtrl.UpStep = 1;
	DSIMasterICTempDiag[DSIMasterID].FilterCtrl.DownStep = 3;
	DSIMasterICTempDiag[DSIMasterID].FilterCtrl.Count = 0;
	DSIMasterICTempDiag[DSIMasterID].FilterCtrl.Input = FALSE;
	DSIMasterICTempDiag[DSIMasterID].FilterCtrl.Output = FALSE;

	for(DSICHlID = DSIChl1;DSICHlID < DSIChlNum;DSICHlID++)
	{
		DSIPinStsDiag[DSIMasterID][DSICHlID].DSIPinSts = DSIPinNor;
		DSIPinStsDiag[DSIMasterID][DSICHlID].ShortToPwrFilterCtrl.UpperLimit = 20;
		DSIPinStsDiag[DSIMasterID][DSICHlID].ShortToPwrFilterCtrl.LowerLimit = 0;
		DSIPinStsDiag[DSIMasterID][DSICHlID].ShortToPwrFilterCtrl.UpStep = 20;
		DSIPinStsDiag[DSIMasterID][DSICHlID].ShortToPwrFilterCtrl.DownStep = 1;
		DSIPinStsDiag[DSIMasterID][DSICHlID].ShortToPwrFilterCtrl.Count = 0;
		DSIPinStsDiag[DSIMasterID][DSICHlID].ShortToPwrFilterCtrl.Input = FALSE;
		DSIPinStsDiag[DSIMasterID][DSICHlID].ShortToPwrFilterCtrl.Output = FALSE;
		DSIPinStsDiag[DSIMasterID][DSICHlID].ShortToGndFilterCtrl.UpperLimit = 20;
		DSIPinStsDiag[DSIMasterID][DSICHlID].ShortToGndFilterCtrl.LowerLimit = 0;
		DSIPinStsDiag[DSIMasterID][DSICHlID].ShortToGndFilterCtrl.UpStep = 20;
		DSIPinStsDiag[DSIMasterID][DSICHlID].ShortToGndFilterCtrl.DownStep = 1;
		DSIPinStsDiag[DSIMasterID][DSICHlID].ShortToGndFilterCtrl.Count = 0;
		DSIPinStsDiag[DSIMasterID][DSICHlID].ShortToGndFilterCtrl.Input = FALSE;
		DSIPinStsDiag[DSIMasterID][DSICHlID].ShortToGndFilterCtrl.Output = FALSE;
	
		memset((void *)&DCRBTimeOutFilterCtrl[DSIMasterID][DSICHlID],0,sizeof(AK2_Pub_FilterCtrl_t));
		DCRBTimeOutFilterCtrl[DSIMasterID][DSICHlID].UpperLimit = 5;
		DCRBTimeOutFilterCtrl[DSIMasterID][DSICHlID].LowerLimit = 0;
		DCRBTimeOutFilterCtrl[DSIMasterID][DSICHlID].UpStep = 1;
		DCRBTimeOutFilterCtrl[DSIMasterID][DSICHlID].DownStep = 1;
	}
}

/******************************************************************************/
/**
 * @brief            DSI工作任务初始化
 * @param[in]        DSIMasterID
 * @param[in]        pDSICfgParam
 *
 * @return      eDSIReturnType_OK
 *
 */
/*****************************************************************************/
DSIReturnType_en InitDSIWork(DSIMasterID_en DSIMasterID,DSICOM_SetParam_st * pDSICfgParam)
{
    uint8 PacketCount = 0;
    uint8 SymbolCount = 0;
    uint8 Lcnt=0;
    uint8 DSIChlCnt;
    uint8 DSIOff = W_DSI2_MODE - W_DSI1_MODE;/* 两个DSI通道的偏移大小 */
    DSI_MASTER_Cfg_str *pDSI_MASTER;
    FRAME_TDMA_SCHEME_str *pFRAME_TDMA_SCHEME;
    if(DSIMasterID >= DSIMasterNum || pDSICfgParam == NULL)
    {
        return eDSIReturnType_N_OK;
    }

	DSIMasterDiagInit(DSIMasterID);
	
    pDSI_MASTER = &G_DSI_MASTER[DSIMasterID];

    pDSI_MASTER->DSI3_CfgStep = 0u;
    pDSI_MASTER->DSI3_CfgWRRegCnt = WR_RegNum_Start;

    memcpy(&pDSI_MASTER->DSICOM_SetParam,pDSICfgParam,sizeof(DSICOM_SetParam_st));
    
    pDSI_MASTER->DSIMasterID = DSIMasterID;
    
    pDSI_MASTER->pWRRegDataGroup = &GWMasterRegData[DSIMasterID][0];

    /** @brief 521.42配置参数 */
    pDSI_MASTER->pWRRegDataGroup[W1C_IRQ_STAT]            = 0xDF03;/*初始化时清除中断标志*/
    pDSI_MASTER->pWRRegDataGroup[W1C_SPI_IRQ_STAT]        = 0x001F;/*初始化时清除中断标志*/
    pDSI_MASTER->pWRRegDataGroup[W1C_DSI1_IRQ_STAT]       = 0x2B2A;/*初始化时清除中断标志*/
    pDSI_MASTER->pWRRegDataGroup[W1C_DSI2_IRQ_STAT]       = 0x2B2A;/*初始化时清除中断标志*/
    
    pDSI_MASTER->pWRRegDataGroup[W_VDSI_CTRL]             = 0x0001 | ((pDSICfgParam->VDSI & 0x07) << 1);/*VDSI 使能*/
    pDSI_MASTER->pWRRegDataGroup[W_IRQ_EN]                = 0xDF07;/*中断 使能*/
    pDSI_MASTER->pWRRegDataGroup[W_SPI_IRQ_EN]            = 0x003F;/*SPI 中断 使能*/
    pDSI_MASTER->pWRRegDataGroup[W_WAVE_FALL]             = pDSICfgParam->Wave_Fall & 0x1F;/**/
    pDSI_MASTER->pWRRegDataGroup[W_WAVE_RISE]             = pDSICfgParam->Wave_Rise & 0x1F;/**/
    
    for(DSIChlCnt = 0;DSIChlCnt<DSIChlNum;DSIChlCnt++)
    {
        /** @brief 计算PDCM 帧大小 */
        pFRAME_TDMA_SCHEME = pDSICfgParam->DSIShl_SetParam[DSIChlCnt].pFRAME_TDMA_SCHEME;
        
        PacketCount = pFRAME_TDMA_SCHEME->PacketCount;
        SymbolCount = 0;
        for(Lcnt=0;Lcnt<PacketCount;Lcnt++)
        {
            SymbolCount += pFRAME_TDMA_SCHEME->PacketSCHEME[Lcnt].SymbolCount;

        }
        
        pDSI_MASTER->DSICfg[DSIChlCnt].SlaveSnsNum = PacketCount;
        pDSI_MASTER->DSICfg[DSIChlCnt].cPdcmFrameSize = 2 + PacketCount * 2 + (SymbolCount / 2);

        
        /** @brief 521.42 DSI通道参数 */
        pDSI_MASTER->pWRRegDataGroup[W_DSI1_MODE + (DSIChlCnt * DSIOff)]             = 0x0000;
 
                           
        pDSI_MASTER->pWRRegDataGroup[W_DSI1_CFG + (DSIChlCnt * DSIOff)]              = 0x0008 
                                                                                  | ((pDSICfgParam->DSIShl_SetParam[DSIChlCnt].FCCBitTime & 0x03) << 5)
                                                                                  | (pDSICfgParam->DSIShl_SetParam[DSIChlCnt].RCCChipTime & 0x07);/*Bit Time 8us,Chip Time 3us ,使能DSI3*/

        pDSI_MASTER->pWRRegDataGroup[W_DSI1_IRQ_EN + (DSIChlCnt * DSIOff)]           = 0x3A00;/*DSI3 硬件相关错误中断 使能*/
        pDSI_MASTER->pWRRegDataGroup[W_DSI1_TIMING_OFFSET + (DSIChlCnt * DSIOff)]    = pDSICfgParam->DSIShl_SetParam[DSIChlCnt].DSI_Timing_Offset & 0x1F;/*需测试*/
        pDSI_MASTER->pWRRegDataGroup[W_DSI1_DM_START_TIME_BC + (DSIChlCnt * DSIOff)] = pDSICfgParam->DSIShl_SetParam[DSIChlCnt].DM_Start_Time_BC & 0x3F;  /*广播模式第一个寻址脉冲时间60us，DM_START_TIME_BC*/
    }
    
    memset(&pDSI_MASTER->MasterCtrl,0,sizeof(DSI_MASTER_CTRL_Str));
    memset(&pDSI_MASTER->ERR42Flg,0,sizeof(Elmos42ERR_Flg_str));
    memset(&pDSI_MASTER->IC_ERRStatus,0,sizeof(IC_ERRStatus_str));
    memset(&pDSI_MASTER->ERR_Cnt,0,sizeof(Elmos42ERR_Cnt_str));

    pDSI_MASTER->MasterCtrl.TaskStatus = DSI_MASTER_Init_Check;
	pDSI_MASTER->SPITransCtrl.SPIDataTransSts = NoSPIDataTrans;
	pDSI_MASTER->SPITransCtrl.u16TransTimeOutCnt = 0;
    pDSI_MASTER->SPITransCtrl.u16TransTimeOut = 5;

    InitDSISPISeq(DSIMasterID,&pDSICfgParam->DSI_SPI_Interface);

    InitCRMRESQueue(DSIMasterID);

    if((pDSI_MASTER->DSICOM_SetParam.EnDCR1BFallIntFunc == NULL)
        ||(pDSI_MASTER->DSICOM_SetParam.EnDCR2BFallIntFunc == NULL)
        ||(pDSI_MASTER->DSICOM_SetParam.EnRFCFallIntFunc == NULL)
        ||(pDSI_MASTER->DSICOM_SetParam.EnINTBFallIntFunc == NULL)
        ||(pDSI_MASTER->DSICOM_SetParam.DisDCR1BIntFunc == NULL)
        ||(pDSI_MASTER->DSICOM_SetParam.DisDCR2BIntFunc == NULL)
        ||(pDSI_MASTER->DSICOM_SetParam.DisRFCFallIntFunc == NULL)
        ||(pDSI_MASTER->DSICOM_SetParam.DisINTBFallIntFunc == NULL)
        ||(pDSI_MASTER->DSICOM_SetParam.ReadINTB_PinFunc == NULL)
        ||(pDSI_MASTER->DSICOM_SetParam.ReadRFC_PinFunc == NULL)
        ||(pDSI_MASTER->DSICOM_SetParam.SetRESB_PinFunc == NULL)
        ||(pDSI_MASTER->DSICOM_SetParam.ClearRESB_PinFunc == NULL))
    {
        return eDSIReturnType_N_OK;
    }
    pDSI_MASTER->DSICOM_SetParam.DisDCR1BIntFunc();
    pDSI_MASTER->DSICOM_SetParam.DisDCR2BIntFunc();
    pDSI_MASTER->DSICOM_SetParam.DisRFCFallIntFunc();
    pDSI_MASTER->DSICOM_SetParam.DisINTBFallIntFunc();
    return eDSIReturnType_OK;
}


/******************************************************************************/
/**
 * @brief            DSI 超时监控
 * @param[in]        DSIMasterID
 *
 * @return      无
 *
 */
/*****************************************************************************/
void DSITimeOutMonitor(DSIMasterID_en DSIMasterID)
{
    DSIChlID_en DSIChlID;

    DSI_Chl_Ctrl_STATUS_Str *pDSICHLCtrlStatus;
    DSICHLDCRB_SubStatus_en DSIDCRBSubStatus;
    DSICHL_WorkStatus_en DSICHLWorkStatus;
    DSIChlSel_en DSIChlSEL;
    uint8 TimeOutFlg = INVALID;
    AK2_Pub_FilterCtrl_t *pstrDCRBTimeOutFilterCtrl = NULL; 
		
    DSI_MASTER_Cfg_str *pDSI_MASTER;
    if(DSIMasterID >= DSIMasterNum)
    {
        return;
    }
    pDSI_MASTER = &G_DSI_MASTER[DSIMasterID];
    
    for(DSIChlID=DSIChl1;DSIChlID<DSIChlNum;DSIChlID++)
    {
		pstrDCRBTimeOutFilterCtrl = &DCRBTimeOutFilterCtrl[DSIMasterID][DSIChlID]; 
        TimeOutFlg = INVALID;
        
        pDSICHLCtrlStatus = &pDSI_MASTER->MasterCtrl.DSICHLCtrlStatus[DSIChlID];

        DSIDCRBSubStatus = pDSICHLCtrlStatus->DSIDCRBSubStatus;
        DSICHLWorkStatus = pDSICHLCtrlStatus->DSICHLWorkStatus;

        /*DCRB 事件超时*/
        if((DSIDCRBSubStatus != DSICHL_WaiteDCRB_NULL) && (DSIDCRBSubStatus < DSICHL_SubStatus_NUM))
        {
            pDSICHLCtrlStatus->DCRBTimeOutCnt++;

            if(GwDCRBTimeOut[DSIDCRBSubStatus] < pDSICHLCtrlStatus->DCRBTimeOutCnt)
            {
                pDSICHLCtrlStatus->DCRBTimeOutCnt = 0;
                /*超时*/
                pDSICHLCtrlStatus->DCRBTimeOutFlgCnt++;
                TimeOutFlg = VALID;

				if(NULL != pstrDCRBTimeOutFilterCtrl)
				{
					pstrDCRBTimeOutFilterCtrl->Input = TRUE;
					AK2_PubAI_Filter(pstrDCRBTimeOutFilterCtrl);
				}
                
                PRINTF_DSI3COM("DCRB %d Timeout \n\r",DSIChlID);
            }
        }


        /*DSI CHL 工作事件超时*/
        if((DSICHLWorkStatus > DSICHL_Sleep) && (DSICHLWorkStatus < DSICHL_WorkStatus_NUM))
        {

            pDSICHLCtrlStatus->DSIWorkTimeOutCnt++;

            if(GwDSIWorkTimeOut[DSICHLWorkStatus] < pDSICHLCtrlStatus->DSIWorkTimeOutCnt)
            {
                /*超时*/

                

                if(DSICHLWorkStatus == DSICHL_CMD_DONE || DSICHLWorkStatus == DSICHL_CMD_STOP2IDLE)
                {
                    SetDSICHLWorkStatus(DSIMasterID,DSIChlID,DSICHL_IDLE);
                }
                else
                {
                    pDSICHLCtrlStatus->DSIWorkTimeOutFlgCnt++;
                    TimeOutFlg = VALID;


                    PRINTF_DSI3COM("DSICHLWork %d Timeout \n\r",DSIChlID);

                }
                pDSICHLCtrlStatus->DSIWorkTimeOutCnt = 0;

            }

        }

        if(TimeOutFlg == VALID)
        {
            
            DSIChlSEL = (DSIChlSel_en)(DSIChlID + 1);
            /*停止DSI, 进入IDLE模式*/
            TransStopDSI(DSIMasterID,DSIChlSEL);
        }
    }
}

/******************************************************************************/
/**
 * @brief            DSI DCRB 中断服务程序
 * @param[in]        DSIMasterID
 * @param[in]        DSIChlID
 *
 * @return           void
 */
/*****************************************************************************/

void DSI_Master_DCRB_ISR(DSIMasterID_en DSIMasterID,DSIChlID_en DSIChlID)
{

    

    DSICHLDCRB_SubStatus_en DSIDCRBSubStatus;
    DSIChlSel_en DSIChlSEL;
    DSI_Chl_Ctrl_STATUS_Str *pDSICHLCtrlStatus;
    DSIReturnType_en Flg;
	AK2_Pub_FilterCtrl_t *pstrDCRBTimeOutFilterCtrl = &DCRBTimeOutFilterCtrl[DSIMasterID][DSIChlID]; 
	
    if(DSIMasterID >= DSIMasterNum)
    {
        return;
    }
    pDSICHLCtrlStatus = &G_DSI_MASTER[DSIMasterID].MasterCtrl.DSICHLCtrlStatus[DSIChlID];
    

    pDSICHLCtrlStatus->DCRBIntCnt++;
	
	pstrDCRBTimeOutFilterCtrl->Input = FALSE;
	AK2_PubAI_Filter(pstrDCRBTimeOutFilterCtrl);	

    DSIDCRBSubStatus = GetDSIDCRBSubStatus(DSIMasterID,DSIChlID);

    DSIChlSEL =  (DSIChlSel_en)(DSIChlID + 1);

    switch(DSIDCRBSubStatus)
    {
        case DSICHL_WaiteDCRB2READCRMRes:
        {
            /* SPI传输读CRM */
            Flg = TransReadCRMResp(DSIMasterID,DSIChlSEL);
            if(Flg != eDSIReturnType_N_OK)
            {
                //PRINTF_DSI3COM("TransReadCRMResp  OK \n");
            }
            else
            {
                PRINTF_DSI3COM("TransReadCRMResp  NOK \n");
            }
            
            SetDSIDCRBSubStatus(DSIMasterID,DSIChlID,DSICHL_WaiteDCRB_NULL);
            break;
        }
        case DSICHL_WaiteDCRB2AUTOBRC:
        {
            /** @brief 等待启动AUTOBRC，DCRB 标志置1，延时等待探头首个数据更新后，启动AUTOBRC,用于接收有效数据 */
            pDSICHLCtrlStatus->DCRBFlg = eDCRBFlg_StartBRC;
            pDSICHLCtrlStatus->StartBRCTimeCnt = 0u;
            break;
        }
        case DSICHL_WaiteDCRB2READPDCM:
        {	

			G_DSI_MASTER[DSIMasterID].MasterCtrl.WaiteDCRB2READPDCMFlg |= BIT_MASK_U8(DSIChlID);
				
			if((BIT_MASK_U8(DSIChlNum) - 1U) == G_DSI_MASTER[DSIMasterID].MasterCtrl.WaiteDCRB2READPDCMFlg)
			{
				G_DSI_MASTER[DSIMasterID].MasterCtrl.WaiteDCRB2READPDCMFlg = 0;
	            if(pDSICHLCtrlStatus->ActiveCRMCMDStatus.wFrameCnt   \
	            >= pDSICHLCtrlStatus->ActiveCRMCMDStatus.AutoBRCMaxCnt)
	            {

	                /*帧计数达到最大值*/
	                SetDSICHLWorkStatus(DSIMasterID,DSIChl1,DSICHL_CMD_DONE);
	                SetDSIDCRBSubStatus(DSIMasterID,DSIChl1,DSICHL_WaiteDCRB_NULL);
					SetDSICHLWorkStatus(DSIMasterID,DSIChl2,DSICHL_CMD_DONE);
	                SetDSIDCRBSubStatus(DSIMasterID,DSIChl2,DSICHL_WaiteDCRB_NULL);
	                /** @brief 请求STOPBRC */
					G_DSI_MASTER[DSIMasterID].MasterCtrl.DSICHLCtrlStatus[DSIChl1].DCRBFlg = eDCRBFlg_StopBRC;
					G_DSI_MASTER[DSIMasterID].MasterCtrl.DSICHLCtrlStatus[DSIChl2].DCRBFlg = eDCRBFlg_StopBRC;
	            }
	            else
	            {

	                /* SPI传输读PDCM */
	                Flg = TransReadPDCMResp(DSIMasterID,DSIChlSEL1And2);
	                if(Flg != eDSIReturnType_N_OK)
	                {
	                    //PRINTF_DSI3COM("TransReadPDCMResp  OK \n");
	                }
	                else
	                {
	                    PRINTF_DSI3COM("TransReadPDCMResp  NOK \n");
	                }

	                SetDSIDCRBSubStatus(DSIMasterID,DSIChl1,DSICHL_WaiteDCRB2READPDCM);
	                SetDSIDCRBSubStatus(DSIMasterID,DSIChl2,DSICHL_WaiteDCRB2READPDCM);
	            }
			}

            break;
        }
        case DSICHL_WaiteDCRB2CRMTXEND:
        {
            SetDSICHLWorkStatus(DSIMasterID,DSIChlID,DSICHL_CMD_DONE);
            SetDSIDCRBSubStatus(DSIMasterID,DSIChlID,DSICHL_WaiteDCRB_NULL);

            break;
        }
        case DSICHL_WaiteDCRB2DMEND:
        {
            SetDSICHLWorkStatus(DSIMasterID,DSIChlID,DSICHL_CMD_DONE);
            SetDSIDCRBSubStatus(DSIMasterID,DSIChlID,DSICHL_WaiteDCRB_NULL);
            break;
        }

        default :
        {
        
            break;
        }
    }

}

/******************************************************************************/
/**
 * @brief            DSI DCRB 1ms任务,处理启动AUTOBRC及STOPBRC
 * @param[in]        DSIMasterID
 *
 * @return           void
 */
/*****************************************************************************/

void DSI_Master_DCRB_1msTask(DSIMasterID_en DSIMasterID)
{
    DSIChlID_en DSIChlID;
    uint8 LcBRCNum;
    DSIReturnType_en Flg;
    DSICHLDCRB_SubStatus_en DSIDCRBSubStatus;
   
    DSI_Chl_Ctrl_STATUS_Str *pDSICHLCtrlStatus;
    DSIChlSel_en DSIChlSEL;
    if(DSIMasterID >= DSIMasterNum)
    {
        return;
    }
    
    pDSICHLCtrlStatus = &G_DSI_MASTER[DSIMasterID].MasterCtrl.DSICHLCtrlStatus[0];
    
    for(DSIChlID=DSIChl1;DSIChlID<DSIChlNum;DSIChlID++)
    {
        
        /** @brief 启动AUTOBRC 中断标志有效 */
        if(pDSICHLCtrlStatus[DSIChlID].DCRBFlg == eDCRBFlg_StartBRC)
        {
            DSIDCRBSubStatus = GetDSIDCRBSubStatus(DSIMasterID,DSIChlID);
            DSIChlSEL = pDSICHLCtrlStatus[DSIChlID].ActiveCRM_Msg.DSIChlSEL;

            if(DSICHL_WaiteDCRB2AUTOBRC != DSIDCRBSubStatus)
            {
                pDSICHLCtrlStatus[DSIChlID].DCRBFlg = eDCRBFlg_NONE;
            }
            else
            {
                /* SPI传输AUTOBRC */
                pDSICHLCtrlStatus[DSIChlID].StartBRCTimeCnt ++;
                /*延迟2ms 发送AUTOBRC 启动PDCM,保证PDCM数据为当次测量值*/
                if(pDSICHLCtrlStatus[DSIChlID].StartBRCTimeCnt > 1)
                {
                    
                    pDSICHLCtrlStatus[DSIChlID].StartBRCTimeCnt = 0;
                    
                    if(pDSICHLCtrlStatus[DSIChlID].ActiveCRMCMDStatus.AutoBRCMaxCnt > 255)
                    {
                        LcBRCNum = 0;
                    }
                    else
                    {
                        LcBRCNum = pDSICHLCtrlStatus[DSIChlID].ActiveCRMCMDStatus.AutoBRCMaxCnt;
                    }
                    
                    if(DSIChlSEL == DSIChlSEL1And2)
                    {
                        uint16 AutoBRCMaxCntCh1,AutoBRCMaxCntCh2;
                        AutoBRCMaxCntCh1 = pDSICHLCtrlStatus[DSIChl1].ActiveCRMCMDStatus.AutoBRCMaxCnt;
                        AutoBRCMaxCntCh2 = pDSICHLCtrlStatus[DSIChl2].ActiveCRMCMDStatus.AutoBRCMaxCnt;
                        
                        if(AutoBRCMaxCntCh1 != AutoBRCMaxCntCh2)
                        {
                            if((AutoBRCMaxCntCh1 > 255) && (AutoBRCMaxCntCh2 > 255))
                            {

                            }
                            else
                            {
                                DSIChlSEL = DSIChlID + 1;
                            }
                        }
                        else
                        {

                        }
                    }
                    else
                    {
                        /**/
                    }

                    Flg = TransStartAutoBRC(DSIMasterID,DSIChlSEL,LcBRCNum);
                    if(Flg != eDSIReturnType_N_OK)
                    {
                        /** @brief  SPI传输请求OK*/

                        
                        if(DSIChlSEL == DSIChlSEL1And2)
                        {   
                            /** @brief 同时启动PDCM 则清除标志 */
                            pDSICHLCtrlStatus[DSIChl1].DCRBFlg = eDCRBFlg_NONE;
                            pDSICHLCtrlStatus[DSIChl2].DCRBFlg = eDCRBFlg_NONE;
                        }
                        else
                        {
                            pDSICHLCtrlStatus[DSIChlID].DCRBFlg = eDCRBFlg_NONE;
                        }
                    }
                    else
                    {
                        /** @brief SPI传输请求ERROR */
                        PRINTF_DSI3COM("TransStartAutoBRC  NOK \n");
                    }
                }
                else
                {
                    /** @brief none */
                }


            }
        }
        else
        {
            if(pDSICHLCtrlStatus[DSIChlID].StartBRCTimeCnt != 0)
            {
                pDSICHLCtrlStatus[DSIChlID].StartBRCTimeCnt = 0;
            }
        }

        
        if(pDSICHLCtrlStatus[DSIChlID].DCRBFlg == eDCRBFlg_StopBRC)
        {
        
            DSIChlSEL = (DSIChlSel_en)(DSIChlID + 1);
            Flg = TransStopDSI(DSIMasterID,DSIChlSEL);
            
            if(Flg != 0xFF)
            {
                /** @brief 清除标志 */
                pDSICHLCtrlStatus[DSIChlID].DCRBFlg = eDCRBFlg_NONE;
            }
            else
            {
                /** @brief SPI传输请求ERROR */
            
            }

        }
    }
}



/******************************************************************************/
/**
 * @brief            Elmos521.42 预初始化
 * @param[in]        pDSI_MASTER
 *
 * @return           eDSIReturnType_OK     
 */
/*****************************************************************************/

DSIReturnType_en DSI_MASTER_InitCheckFunc(DSI_MASTER_Cfg_str *pDSI_MASTER)
{
    DSIMasterID_en DSIMasterID = pDSI_MASTER->DSIMasterID;
    DSICOM_SetParam_st *pDSICOM_SetParam = &pDSI_MASTER->DSICOM_SetParam;

    pDSI_MASTER->MasterCtrl.TaskStatus = DSI_MASTER_Init_Active;

    SetDSICHLWorkStatus(DSIMasterID,DSIChl1,DSICHL_INIT);
    SetDSICHLWorkStatus(DSIMasterID,DSIChl2,DSICHL_INIT);
    /*SPI正常、521.42 通信电压有效，进入InitActive*/

    pDSICOM_SetParam->ClearRESB_PinFunc();

    memset(&pDSI_MASTER->IC_ERRStatus,0,sizeof(IC_ERRStatus_str));
    
    pDSI_MASTER->DSI3_CfgStep = 0u;
    pDSI_MASTER->DSI3_CfgWRRegCnt = WR_RegNum_Start;
    return eDSIReturnType_OK;
}


/******************************************************************************/
/**
 * @brief            Elmos521.42 初始化
 * @param[in]        pDSI_MASTER
 *
 * @return           eDSIReturnType_OK     
 */
/*****************************************************************************/

DSIReturnType_en DSI_MASTER_InitActiveFunc(DSI_MASTER_Cfg_str *pDSI_MASTER)
{

    WRReg_en LcWRRegCnt = WR_RegNum_Start;
   
    uint8 DSI3_CfgStep = 0;
    DSIReturnType_en flg;
    uint8 CheckResult = 0;
    DSIMasterID_en DSIMasterID = pDSI_MASTER->DSIMasterID;
    DSICOM_SetParam_st *pDSICOM_SetParam = &pDSI_MASTER->DSICOM_SetParam;
    Elmos42ERR_Cnt_str  *pERR_Cnt;
    
    /** @brief 设置42RESB 引脚为高电平 */
    pDSICOM_SetParam->SetRESB_PinFunc();
    pERR_Cnt = &pDSI_MASTER->ERR_Cnt;
    
    /** @brief 等待42复位初始化完成 */
    if(pDSICOM_SetParam->ReadRFC_PinFunc() == 1)
    {
        DSI3_CfgStep = pDSI_MASTER->DSI3_CfgStep;
        
        LcWRRegCnt = pDSI_MASTER->DSI3_CfgWRRegCnt;

        /*复位后等待RFC 有效，进行 521.42寄存器配置*/

        switch(DSI3_CfgStep)
        {
            case 0:
            {
                if(LcWRRegCnt < W_RegNum)
                {
                    flg = TransWMasterDSIReg(DSIMasterID,LcWRRegCnt,pDSI_MASTER->pWRRegDataGroup[LcWRRegCnt]);
                    if(flg != eDSIReturnType_OK)
                    {
                        /** @brief ERR */
                        PRINTF_DSI3COM("TransWMasterDSIReg %d NG RetFlg %d \n",LcWRRegCnt,flg);
						if(pDSI_MASTER->MasterCtrl.SPITxErrCnt < UINT32_MAX)
						{
                        	pDSI_MASTER->MasterCtrl.SPITxErrCnt++;
						}
                    }					
                    LcWRRegCnt++;
                }
                else
                {
                    LcWRRegCnt = R_VDSI_CTRL;
                    DSI3_CfgStep ++;
                }
                break;
            }
            case 1:
            {
                flg = TransUpload_DSI_TDMA_SCHEME(DSIMasterID,DSIChlSEL1,pDSI_MASTER->DSICOM_SetParam.DSIShl_SetParam[DSIChl1].pFRAME_TDMA_SCHEME);
                if(flg != eDSIReturnType_OK)
                {
                    /** @brief ERR */
                    PRINTF_DSI3COM("TransUpload_DSI_TDMA_SCHEME DSIChlSEL1 NG RetFlg %d \n",flg);
					if(pDSI_MASTER->MasterCtrl.SPITxErrCnt < UINT32_MAX)
					{
                    	pDSI_MASTER->MasterCtrl.SPITxErrCnt++;
					}
                }
                flg = TransUpload_DSI_TDMA_SCHEME(DSIMasterID,DSIChlSEL2,pDSI_MASTER->DSICOM_SetParam.DSIShl_SetParam[DSIChl2].pFRAME_TDMA_SCHEME);
                if(flg != eDSIReturnType_OK)
                {
                    /** @brief ERR */
                    PRINTF_DSI3COM("TransUpload_DSI_TDMA_SCHEME DSIChlSEL2 NG RetFlg %d \n",flg);
					if(pDSI_MASTER->MasterCtrl.SPITxErrCnt < UINT32_MAX)
					{
                    	pDSI_MASTER->MasterCtrl.SPITxErrCnt++;
					}
                }
                DSI3_CfgStep ++;
                break;
            }
            case 2:
            {
                if(LcWRRegCnt < WR_RegNum)
                {
                    flg = TransRMasterDSIReg(DSIMasterID,LcWRRegCnt,&pDSI_MASTER->pWRRegDataGroup[LcWRRegCnt]);
                        
                    if(flg != eDSIReturnType_OK)
                    {
						if(pDSI_MASTER->MasterCtrl.SPITxErrCnt < UINT32_MAX)
						{
                        	pDSI_MASTER->MasterCtrl.SPITxErrCnt++;
						}
                        /** @brief ERR */
                        PRINTF_DSI3COM("TransRMasterDSIReg %d  NG  RetFlg %d \n",LcWRRegCnt,flg);
                    }
                    else
                    {
                        PRINTF_DSI3COM("TransRMasterDSIReg %d  = 0x%x \n",LcWRRegCnt,pDSI_MASTER->pWRRegDataGroup[LcWRRegCnt]);
                    }
                    
                    LcWRRegCnt++;
                }
                else
                {
                    LcWRRegCnt = WR_RegNum_Start;
                    DSI3_CfgStep ++;

                    CheckResult = 0;
                    
                    /* 回读确认中断标志位是否清除成功 */
                    for(LcWRRegCnt = 0;LcWRRegCnt < W_RegNum;LcWRRegCnt++)
                    {
                        if(LcWRRegCnt < W_VDSI_CTRL)
                        {
                            /* 确认中断标志位是否清除成功 */
                            /*当前无故障而存在中断标志处理？ */
                            if(pDSI_MASTER->pWRRegDataGroup[LcWRRegCnt + R_IRQ_STAT] != 0)
                            {
                                CheckResult |= 1;
                            }
                        }
                        if(LcWRRegCnt < W_DSI2_TIMING_OFFSET)
                        {
                             /* 确认寄存器配置是否正确 */
                            if(pDSI_MASTER->pWRRegDataGroup[LcWRRegCnt + W_VDSI_CTRL] 
                            != pDSI_MASTER->pWRRegDataGroup[LcWRRegCnt + R_VDSI_CTRL])
                            {
                                CheckResult |= 2;
                            }
                        }
                        
                        if(LcWRRegCnt < W_RegNum)
                        {
                            /* 确认初始化是否发生异常 */
                             if(pDSI_MASTER->pWRRegDataGroup[LcWRRegCnt + checkAdr1] != RegINIT_Check[LcWRRegCnt])
                             {
                                 CheckResult |= 4;
                             }
                        }
                        

                    }
                    
                    
                     if(CheckResult)
                     { 
					 	 if(pERR_Cnt->GINIT_ErrCnt < UINT16_MAX)
					 	 {
                         	pERR_Cnt->GINIT_ErrCnt ++;
					 	 }	
							
						 if(((pDSI_MASTER->pWRRegDataGroup[R_STATUS_WORD] & DSI_MASTER_IC_STATE_REG_ERR_MASK) != 0)
						 	|| (pDSICOM_SetParam->ReadINTB_PinFunc() == 0))
						 {
							pDSI_MASTER->MasterCtrl.TaskStatus = DSI_MASTER_DIAG;
							SetDSICHLWorkStatus(DSIMasterID,DSIChl1,DSICHL_DIAG);
                    		SetDSICHLWorkStatus(DSIMasterID,DSIChl2,DSICHL_DIAG);
						 }
						 else
						 {						 
                         	/* 数据回读不一致,初始化发生异常，重新进行初始化 */
                         	pDSI_MASTER->MasterCtrl.TaskStatus = DSI_MASTER_Init_Check;
						 }

                         PRINTF_DSI3COM("data call_read unlike\n");
                         
                         if(CheckResult & 0x01)
                         {
                            /* 中断标志位清除失败 */
                            PRINTF_DSI3COM("INT Clear Err\n");
                         }

                         if(CheckResult & 0x02)
                         {
                            /*  */
                            PRINTF_DSI3COM("CfgData Err\n");
                         }

                         if(CheckResult & 0x04)
                         {
                            /* CheckAddr 数据异常 */
                            PRINTF_DSI3COM("INIT CheckData Err\n");
                         }
                         
                         /* 寄存器回读数据异常 */
                         pDSI_MASTER->IC_ERRStatus.RegCfg_Flg = 1;

                         if(pERR_Cnt->GINIT_ErrCnt > 10)
                         {
                             /* 初始化无法完成，数据回读不一致，重新初始化仍未解决数据回读故障 */
                             pDSI_MASTER->IC_ERRStatus.ERR_ResumeFlg = NOT_Resume;

                             PRINTF_DSI3COM("Call_Read Resume Fail\n");
                         }
                         else
                         {
                            pDSI_MASTER->IC_ERRStatus.ERR_ResumeFlg = IN_Resume;
                         }
                     }
                     else
                     {
                           /* 数据回读正常 */
                            pERR_Cnt->GINIT_ErrCnt =0;
                     }
                            LcWRRegCnt = 0;

                }
                
                //DSI3_CfgStep = 2;
                break;
            }

            default:
            {
                PRINTF_DSI3COM("Status = 0x%04x\n",pDSI_MASTER->pWRRegDataGroup[R_STATUS_WORD]);

                if((pDSI_MASTER->pWRRegDataGroup[R_STATUS_WORD]  != 0x0410)|| (pDSICOM_SetParam->ReadINTB_PinFunc() == 0))
                {
                    pDSI_MASTER->MasterCtrl.TaskStatus = DSI_MASTER_DIAG;
                    
                    SetDSICHLWorkStatus(DSIMasterID,DSIChl1,DSICHL_DIAG);
                    SetDSICHLWorkStatus(DSIMasterID,DSIChl2,DSICHL_DIAG);
                    PRINTF_DSI3COM("E521.42 Init  NG  ICStatus 0x%x   \n",pDSI_MASTER->MasterCtrl.ICStatusReg.u16Data);
                }
                else
                {
                    /* 未发生故障或故障已恢复正常、清除故障标志位及故障计数 */
				
					memset(&pDSI_MASTER->ERR_Cnt,0,sizeof(Elmos42ERR_Cnt_str));
					pDSI_MASTER->MasterCtrl.SPITxErrCnt = 0;
                    //pDSI_MASTER->IC_ERRStatus.ERR_ResumeFlg = OK_Resume;
                                     
                    PRINTF_DSI3COM("INIT finish,Call_Read OK\n");
                    pDSI_MASTER->MasterCtrl.TaskStatus = DSI_MASTER_Init_Finish;
                }
                //pDSI_MASTER->MasterCtrl.TaskStatus = DSI_MASTER_Init_Finish;
                DSI3_CfgStep  = 0;
            }
        }
        
        pDSI_MASTER->DSI3_CfgStep = DSI3_CfgStep;
        pDSI_MASTER->DSI3_CfgWRRegCnt = LcWRRegCnt;

    }
    else
    {
        /** @brief 等待RFC超时处理 */
		if(pERR_Cnt->GWait_RFCCnt < UINT16_MAX)
		{
        	pERR_Cnt->GWait_RFCCnt++;
		}
		
        if(pERR_Cnt->GWait_RFCCnt > 200)
        {
            /* 等待RFC拉高失败，IC重新上电初始化 */
			if(pERR_Cnt->RFCErrCnt < UINT16_MAX)
            {
				pERR_Cnt->RFCErrCnt++;
            }
			
			pERR_Cnt->GWait_RFCCnt = 0;
            pDSI_MASTER->IC_ERRStatus.RFC_TimeOut = 1;
            pDSI_MASTER->IC_ERRStatus.ERR_ResumeFlg = IN_Resume;
            
            pDSI_MASTER->MasterCtrl.TaskStatus = DSI_MASTER_Init_Check;           
        }

        if(pERR_Cnt->GWait_RFCCnt > 500)
        {
            /* 检查RFC为低时，IC通信是否正常，如果通信正常则进入42初始化 */
            //pERR_Cnt->GWait_RFCCnt = 0;
            pDSI_MASTER->IC_ERRStatus.ERR_ResumeFlg = NOT_Resume;
        }
        

    }


    /*配置完成，无故障进入InitFinish*/
    return eDSIReturnType_OK;
}

/******************************************************************************/
/**
 * @brief            Elmos521.42 初始化完成处理
 * @param[in]        pDSI_MASTER
 *
 * @return           eDSIReturnType_OK     
 */
/*****************************************************************************/

DSIReturnType_en DSI_MASTER_InitFinishFunc(DSI_MASTER_Cfg_str *pDSI_MASTER)
{
    DSIMasterID_en DSIMasterID = pDSI_MASTER->DSIMasterID;

    SetDSICHLWorkStatus(DSIMasterID,DSIChl1,DSICHL_IDLE);
    SetDSICHLWorkStatus(DSIMasterID,DSIChl2,DSICHL_IDLE);


    pDSI_MASTER->MasterCtrl.TaskStatus = DSI_MASTER_READY;
    /* 进入Ready模式*/
    /* 读取IC状态位 */
    TransRMasterICStatus(DSIMasterID,&pDSI_MASTER->MasterCtrl.ICStatusReg.u16Data);
    pDSI_MASTER->DSI3_CfgStep = 0;
    return eDSIReturnType_OK;
}

/******************************************************************************/
/**
 * @brief            Elmos521.42 通信Ready状态处理
 * @param[in]        pDSI_MASTER
 *
 * @return           eDSIReturnType_OK     
 */
/*****************************************************************************/

DSIReturnType_en DSI_MASTER_ReadyFunc(DSI_MASTER_Cfg_str *pDSI_MASTER)
{
    DSICOM_SetParam_st *pDSICOM_SetParam = &pDSI_MASTER->DSICOM_SetParam;
    DSIMasterID_en DSIMasterID = pDSI_MASTER->DSIMasterID;
    Elmos42ERR_Cnt_str  *pERR_Cnt;
    
    pERR_Cnt = &pDSI_MASTER->ERR_Cnt;
    DSIReturnType_en flg = 0;
    DSIChlID_en RRegID = 0;
	DSIChlID_en DSICHlID = DSIChl1;
	DSIPinStsDiag_t *pDSIPinDiag = NULL;
	
	if(pERR_Cnt->Check_Time < UINT16_MAX)
	{
		pERR_Cnt->Check_Time ++;
	}
	
    /* DSI为空闲状态 */
    if((pDSI_MASTER->MasterCtrl.DSICHLCtrlStatus[DSIChl1].DSICHLWorkStatus == DSICHL_IDLE)
     &&(pDSI_MASTER->MasterCtrl.DSICHLCtrlStatus[DSIChl2].DSICHLWorkStatus == DSICHL_IDLE))
    {
        /* 每50ms进行一次IC状态检测 */
        if(pERR_Cnt->Check_Time > 50)
        {
            /* 读取IC状态位 */
            flg = TransRMasterICStatus(DSIMasterID,&pDSI_MASTER->MasterCtrl.ICStatusReg.u16Data);
            if(flg != eDSIReturnType_OK)
            {
                pDSI_MASTER->MasterCtrl.SPITxErrCnt++;
                /** @brief ERR */
                PRINTF_DSI3COM("TransRMasterICStatus   NG  RetFlg %d \n",flg);
            }
            else
            {
                PRINTF_DSI3COM("TransRMasterICStatus  = 0x%x \n",pDSI_MASTER->MasterCtrl.ICStatusReg.u16Data);
            }
            pERR_Cnt->Check_Time = 0;
         }
    }

	 /* 521.42 IC状态监控 及INTB 引脚监控*/
    if(((pDSI_MASTER->MasterCtrl.ICStatusReg.u16Data & DSI_MASTER_IC_STATE_REG_ERR_MASK) != 0) 
     ||((pDSI_MASTER->MasterCtrl.ICStatusReg.u16Data & DSI_MASTER_IC_STATE_REG_TDMA_MASK) != DSI_MASTER_IC_STATE_REG_TDMA_MASK) 
     || (pDSICOM_SetParam->ReadINTB_PinFunc() == 0))
    {	
        /*异常进入DIAG模式*/
        pDSI_MASTER->MasterCtrl.TaskStatus = DSI_MASTER_DIAG;

    }
    else
    {
        /* 未发生故障或故障已恢复正常、清除42故障标志位,清除故障计数 */
        if(pDSI_MASTER->IC_ERRStatus.ERR_ResumeFlg != OK_Resume)
        {
            memset(&pDSI_MASTER->ERR_Cnt,0,sizeof(Elmos42ERR_Cnt_str));

            ClearICErrStatus(pDSI_MASTER->DSIMasterID);
            pDSI_MASTER->IC_ERRStatus.ERR_ResumeFlg = OK_Resume;
        }

    }
	
    return eDSIReturnType_OK;
}

/******************************************************************************/
/**
 * @brief            Elmos521.42 诊断状态处理
 * @param[in]        pDSI_MASTER
 *
 * @return           eDSIReturnType_OK     
 */
/*****************************************************************************/

DSIReturnType_en DSI_MASTER_DIAGFunc(DSI_MASTER_Cfg_str *pDSI_MASTER)
{
    DSIReturnType_en flg;
    WRReg_en LcWRRegCnt = 0;

    DSIChlID_en DSICHlID;
    uint8 Test_DIAG_Cnt = 0;
    uint8 DSIOff = W_DSI2_MODE - W_DSI1_MODE;/* 两个DSI通道的偏移大小 */


    Elmos42ERR_Cnt_str  *pERR_Cnt;          /* 故障计数       */
    Elmos42ERR_Flg_str  *pERR_42_Flg;      /* 故障处理标志     */
    IC_ERRStatus_str    *pICErr_Status;   /* IC故障状态     */
    DSIMasterICTempSts_e*pICTempSts;
	DSIPinStsDiag_t *pDSI1PinDiag;
	DSIPinStsDiag_t *pDSI2PinDiag;
	DSIPinStsDiag_t *pDSIPinDiag = NULL;
	
    pERR_Cnt      = &pDSI_MASTER->ERR_Cnt;      
    pERR_42_Flg   = &pDSI_MASTER->ERR42Flg;
    pICErr_Status = &pDSI_MASTER->IC_ERRStatus;
    
 
    PRINTF_DSI3COM("DIAG STAT\n");


    E521_42WRRegData_un IRQ_ERR;              /* IRQ_STAT_ERR标志       */
    E521_42WRRegData_un SPI_ERR;             /* SPI_IRQ_STAT_ERR标志   */
    E521_42WRRegData_un DSI_ERR[DSIChlNum]; /* DSI_STAT ERR标志       */
    E521_42WRRegData_un IC_STATUS;         /* IC状态                 */


    DSIMasterID_en DSIMasterID = pDSI_MASTER->DSIMasterID;
	DSICOM_SetParam_st *pDSICOM_SetParam = &pDSI_MASTER->DSICOM_SetParam;
	pICTempSts    = &DSIMasterICTempDiag[DSIMasterID].DSIMasterICTempSts;
	pDSI1PinDiag   = &DSIPinStsDiag[DSIMasterID][DSIChl1];
 	pDSI2PinDiag   = &DSIPinStsDiag[DSIMasterID][DSIChl2];

	/*更新ICStatus*/
	flg = TransRMasterICStatus(DSIMasterID,&pDSI_MASTER->MasterCtrl.ICStatusReg.u16Data);
    if(flg != eDSIReturnType_OK)
    {
        pDSI_MASTER->MasterCtrl.SPITxErrCnt++;
        /** @brief ERR */
        PRINTF_DSI3COM("TransRMasterICStatus   NG  RetFlg %d \n",flg);
    }
    else
    {
        PRINTF_DSI3COM("TransRMasterICStatus  = 0x%x \n",pDSI_MASTER->MasterCtrl.ICStatusReg.u16Data);
    }

	if(eDSIReturnType_OK == flg)
	{
	    if(((pDSI_MASTER->MasterCtrl.ICStatusReg.u16Data & DSI_MASTER_IC_STATE_REG_ERR_MASK) != 0) 
	     ||((pDSI_MASTER->MasterCtrl.ICStatusReg.u16Data & DSI_MASTER_IC_STATE_REG_TDMA_MASK) != DSI_MASTER_IC_STATE_REG_TDMA_MASK) 
	     || (pDSICOM_SetParam->ReadINTB_PinFunc() == 0))
	    {
#if 1
	        /*异常DIAG模式*/
	        /** @brief DSI1/2 进入诊断模式  */
	        if(GetDSICHLWorkStatus(DSIMasterID,DSIChl1) == DSICHL_IDLE)
	        {
	            SetDSICHLWorkStatus(DSIMasterID,DSIChl1,DSICHL_DIAG);
	            
	        }
	        
	        if(GetDSICHLWorkStatus(DSIMasterID,DSIChl2) == DSICHL_IDLE)
	        {
	            SetDSICHLWorkStatus(DSIMasterID,DSIChl2,DSICHL_DIAG);
	            
	        }
	        if((GetDSICHLWorkStatus(DSIMasterID,DSIChl1) == DSICHL_DIAG) && (GetDSICHLWorkStatus(DSIMasterID,DSIChl2) == DSICHL_DIAG))
#endif
	        {
	            /* 读取故障状态位 */
	            TransRMasterICStatus(DSIMasterID,&pDSI_MASTER->MasterCtrl.ICStatusReg.u16Data);
	            for(LcWRRegCnt = R_IRQ_STAT;LcWRRegCnt < R_IC_REVISION;LcWRRegCnt++)
	            {
	                flg = TransRMasterDSIReg(DSIMasterID,LcWRRegCnt,&pDSI_MASTER->pWRRegDataGroup[LcWRRegCnt]);
	                if(flg != eDSIReturnType_OK)
	                {
	                    /** @brief ERR */
	                    PRINTF_DSI3COM("TransRMasterDSIReg %d NG RetFlg %d \n",LcWRRegCnt,flg);
						if(pDSI_MASTER->MasterCtrl.SPITxErrCnt < UINT32_MAX)
						{
							pDSI_MASTER->MasterCtrl.SPITxErrCnt++;
						}
	                }
	            }


	            /* 故障信息位的填充 */

	            IRQ_ERR.u16Data = pDSI_MASTER->pWRRegDataGroup[R_IRQ_STAT];
	            SPI_ERR.u16Data = pDSI_MASTER->pWRRegDataGroup[R_SPI_IRQ_STAT];
	            IC_STATUS.u16Data = pDSI_MASTER->pWRRegDataGroup[R_STATUS_WORD];
	            DSI_ERR[DSIChl1].u16Data = pDSI_MASTER->pWRRegDataGroup[R_DSI1_IRQ_STAT];
	            DSI_ERR[DSIChl2].u16Data = pDSI_MASTER->pWRRegDataGroup[R_DSI2_IRQ_STAT];


	            /**** 故障诊断 ****/

#if 1
	            if(IC_STATUS.IC_STATUS.OT || IRQ_ERR.IRQ_STAT.OT)
	            {
	                /* IC发生过温 */

					if(pERR_Cnt->OT_Cnt < UINT16_MAX)
					{
	                	pERR_Cnt->OT_Cnt ++;
					}
	                pERR_42_Flg->OT_Flg   = 1;   
	                pICErr_Status->OT_Flg = 1;
					*pICTempSts = DSIMasterIC_Temperature_Over;
	            }
	            else
	            {
	                pERR_Cnt->OT_Cnt      = 0;
	                pERR_42_Flg->OT_Flg   = 0;   
	                pICErr_Status->OT_Flg = 0;     
	                *pICTempSts = DSIMasterIC_Temperature_Normal;
	            }
#endif            

#if 1  /* 该类故障开启TRE尝试恢复 */

				/*通道1对地或电源短路检测*/
				if((DSI_ERR[DSIChl1].DSI_IRQ_STAT.DSI_UV) && (DSI_ERR[DSIChl1].DSI_IRQ_STAT.DSI_OC))
				{
					/* 使能TRE */
		            flg = TransWMasterDSIReg(DSIMasterID,W_DSI1_CFG,pDSI_MASTER->pWRRegDataGroup[W_DSI1_CFG]);
	                if(flg != eDSIReturnType_OK)
	                {
	                    /** @brief ERR */
	                    PRINTF_DSI3COM("TransWMasterDSIReg  NG RetFlg %d \n",flg);
	                }

					/* 读取IC状态位 */
		            flg = TransRMasterDSIReg(DSIMasterID,R_STATUS_WORD,&pDSI_MASTER->pWRRegDataGroup[R_STATUS_WORD]);
		            if(flg != eDSIReturnType_OK)
		            {
		                pDSI_MASTER->MasterCtrl.SPITxErrCnt++;
		                /** @brief ERR */
		                PRINTF_DSI3COM("TransRMasterICStatus   NG  RetFlg %d \n",flg);
		            }
					else
					{
						IC_STATUS.u16Data = pDSI_MASTER->pWRRegDataGroup[R_STATUS_WORD];
					}
					
					if(IC_STATUS.IC_STATUS.DSI_0_UV)
					{
						pDSI1PinDiag->ShortToGndFilterCtrl.Input = TRUE;
						AK2_PubAI_Filter(&pDSI1PinDiag->ShortToGndFilterCtrl);
						
						if(TRUE == pDSI1PinDiag->ShortToGndFilterCtrl.Output)
						{
							pDSI1PinDiag->ShortToPwrFilterCtrl.Count = 0;
							pDSI1PinDiag->DSIPinSts = DSIPinShortToGnd;
						}
						else
						{
							pDSI1PinDiag->DSIPinSts = DSIPinNor;
						}
						
					}
					else
					{
						pDSI1PinDiag->ShortToPwrFilterCtrl.Input = TRUE;
						AK2_PubAI_Filter(&pDSI1PinDiag->ShortToPwrFilterCtrl);
						
						if(TRUE == pDSI1PinDiag->ShortToPwrFilterCtrl.Output)
						{
							pDSI1PinDiag->ShortToGndFilterCtrl.Count = 0;
							pDSI1PinDiag->DSIPinSts = DSIPinShortToPwr;
						}
						else
						{
							pDSI1PinDiag->DSIPinSts = DSIPinNor;
						}
					}
				}
				
				/*通道2对地或电源短路检测*/
				if((DSI_ERR[DSIChl2].DSI_IRQ_STAT.DSI_UV) && (DSI_ERR[DSIChl2].DSI_IRQ_STAT.DSI_OC))
				{
					/* 使能TRE */
		            flg = TransWMasterDSIReg(DSIMasterID,W_DSI2_CFG,pDSI_MASTER->pWRRegDataGroup[W_DSI2_CFG]);
	                if(flg != eDSIReturnType_OK)
	                {
	                    /** @brief ERR */
	                    PRINTF_DSI3COM("TransWMasterDSIReg  NG RetFlg %d \n",flg);
	                }

					/* 读取IC状态位 */
		            flg = TransRMasterDSIReg(DSIMasterID,R_STATUS_WORD,&pDSI_MASTER->pWRRegDataGroup[R_STATUS_WORD]);
		            if(flg != eDSIReturnType_OK)
		            {
		                pDSI_MASTER->MasterCtrl.SPITxErrCnt++;
		                /** @brief ERR */
		                PRINTF_DSI3COM("TransRMasterICStatus   NG  RetFlg %d \n",flg);
		            }
					else
					{
						IC_STATUS.u16Data = pDSI_MASTER->pWRRegDataGroup[R_STATUS_WORD];
					}
					
					if(IC_STATUS.IC_STATUS.DSI_1_UV)
					{
						pDSI2PinDiag->ShortToGndFilterCtrl.Input = TRUE;
						AK2_PubAI_Filter(&pDSI2PinDiag->ShortToGndFilterCtrl);
						
						if(TRUE == pDSI2PinDiag->ShortToGndFilterCtrl.Output)
						{
							pDSI2PinDiag->ShortToPwrFilterCtrl.Count = 0;
							pDSI2PinDiag->DSIPinSts = DSIPinShortToGnd;
						}
						else
						{
							pDSI2PinDiag->DSIPinSts = DSIPinNor;
						}
						
					}
					else
					{
						pDSI2PinDiag->ShortToPwrFilterCtrl.Input = TRUE;
						AK2_PubAI_Filter(&pDSI2PinDiag->ShortToPwrFilterCtrl);
						
						if(TRUE == pDSI2PinDiag->ShortToPwrFilterCtrl.Output)
						{
							pDSI2PinDiag->ShortToGndFilterCtrl.Count = 0;
							pDSI2PinDiag->DSIPinSts = DSIPinShortToPwr;
						}
						else
						{
							pDSI2PinDiag->DSIPinSts = DSIPinNor;
						}
					}
				}

				
	            if(IC_STATUS.IC_STATUS.DSI_0_UV || DSI_ERR[DSIChl1].DSI_IRQ_STAT.DSI_UV)
	            {
	                /* DSI1发生欠压 */
	                pICErr_Status->DSI_ChlErr[DSIChl1].DSI_UV_Flg = 1;
	            }
	            else
	            {
	                pICErr_Status->DSI_ChlErr[DSIChl1].DSI_UV_Flg = 0;
	            }


	            if(IC_STATUS.IC_STATUS.DSI_1_UV || DSI_ERR[DSIChl2].DSI_IRQ_STAT.DSI_UV)
	            {
	                /* DSI2发生欠压 */
	                pICErr_Status->DSI_ChlErr[DSIChl2].DSI_UV_Flg = 1;
	                
	            }
	            else
	            {
	                pICErr_Status->DSI_ChlErr[DSIChl2].DSI_UV_Flg = 0;
	            }

	        

	            if(pICErr_Status->DSI_ChlErr[DSIChl1].DSI_UV_Flg
	             ||pICErr_Status->DSI_ChlErr[DSIChl2].DSI_UV_Flg)
	            {
	                pERR_42_Flg->DSI_UV_Flg =1;
	            }
	            else
	            {
	                pERR_42_Flg->DSI_UV_Flg =0;
	            }


	            if(IRQ_ERR.IRQ_STAT.VDSI_UV)
	            {
	                /* VDSI欠压 */
	            
	                pERR_42_Flg->VDSI_UV_Flg   = 1;
	                pICErr_Status->VDSI_UV_Flg = 1;  
	            }
	            else
	            {
	                pERR_42_Flg->VDSI_UV_Flg   = 0;
	                pICErr_Status->VDSI_UV_Flg = 0;  
	            }


	            for(DSICHlID = DSIChl1;DSICHlID < DSIChlNum;DSICHlID++)
	            {
	               if(DSI_ERR[DSICHlID].DSI_IRQ_STAT.DSI_OC)
	               {
	                   /* DSI通道DSICHlID发生过流 */
	               
	                   pICErr_Status->DSI_ChlErr[DSICHlID].DSI_OC_Flg = 1;

	               }
	               else
	               {               
	                   pICErr_Status->DSI_ChlErr[DSICHlID].DSI_OC_Flg = 0;

	               }
	            }


	            if((pICErr_Status->DSI_ChlErr[DSIChl1].DSI_OC_Flg)
	             ||(pICErr_Status->DSI_ChlErr[DSIChl2].DSI_OC_Flg))
	            {
	                   pERR_42_Flg->DSI_OC_Flg = 1;
	            }
	            else
	            {
	                   pERR_42_Flg->DSI_OC_Flg = 0;
	            }

	            
	            for(DSICHlID = DSIChl1;DSICHlID < DSIChlNum;DSICHlID++)
	            {
	               if(pICErr_Status->DSI_ChlErr[DSICHlID].DSI_OC_Flg
	                   /*||pICErr_Status->DSI_ChlErr[DSICHlID].DSI_UV_Flg*/)
	               {
	                   /* DSI通道发生故障(过压/过流) */
	                   pICErr_Status->DSI_ChlErr[DSICHlID].DSIChl_ErrFlg = 1;
	                   
	               }
	               else
	               {
	                   pICErr_Status->DSI_ChlErr[DSICHlID].DSIChl_ErrFlg = 0;
	               }
	            }   


	            /* 记录欠压、过流的次数 */
	            if((pICErr_Status->DSI_ChlErr[DSIChl1].DSIChl_ErrFlg == 1)
	             ||(pICErr_Status->DSI_ChlErr[DSIChl2].DSIChl_ErrFlg == 1))
	            {
					 if(pERR_Cnt->DSIUV_OC_Cnt < UINT16_MAX)
					 {
	                 	pERR_Cnt->DSIUV_OC_Cnt ++;
					 }
	            }
	            else
	            {
	                 pERR_Cnt->DSIUV_OC_Cnt = 0;
	            }

	            
#endif

#if 1  /* 该类故障均做复位操作 */

	            if(IRQ_ERR.IRQ_STAT.VCC_UV)
	            {
	                /* VCC发生欠压 */
	            	if(pERR_Cnt->VCC_UV_Cnt < UINT16_MAX)
	            	{
	                	pERR_Cnt->VCC_UV_Cnt ++;
	            	}
	                pERR_42_Flg->VCC_UV_Flg   = 1;
	                pICErr_Status->VCC_UV_Flg = 1;

	                Test_DIAG_Cnt = 1;
	            }
	            else
	            { 
	                pERR_Cnt->VCC_UV_Cnt      = 0;
	                pERR_42_Flg->VCC_UV_Flg   = 0;
	                pICErr_Status->VCC_UV_Flg = 0;
	               
	            }

	            
	            if((IRQ_ERR.IRQ_STAT.FSM_RESET)
	             ||(IRQ_ERR.IRQ_STAT.Pin_RESET))
	            {
					if(pERR_Cnt->RESET_ERR_Cnt < UINT16_MAX)
					{
                          pERR_Cnt->RESET_ERR_Cnt ++;
					}
	                
	                Test_DIAG_Cnt = 2;
	            }
	            else
	            {
	                pERR_Cnt->RESET_ERR_Cnt  = 0;
	            }


	            if(DSI_ERR[DSIChl1].DSI_IRQ_STAT.DSI_CRC_BIST
	             ||DSI_ERR[DSIChl2].DSI_IRQ_STAT.DSI_CRC_BIST
	             ||SPI_ERR.SPI_IRQ_STAT.SPI_CRC_BIST
	             ||IRQ_ERR.IRQ_STAT.RAM_BIST)
	            {
	                /* 发生自检错误 */
	            	if(pERR_Cnt->SLEF_TEST_Cnt < UINT16_MAX)
	            	{
	                	pERR_Cnt->SLEF_TEST_Cnt ++;
	            	}
	                pICErr_Status->Self_Test_Flg = 1;
	                
	                Test_DIAG_Cnt = 5;
	             }
	             else
	             {
	                pERR_Cnt->SLEF_TEST_Cnt      = 0;
	                pICErr_Status->Self_Test_Flg = 0;
	             }


	             if((pERR_Cnt->VCC_UV_Cnt + pERR_Cnt->RESET_ERR_Cnt + pERR_Cnt->SLEF_TEST_Cnt) > 0)
	             {
	                pERR_42_Flg->RESET_Flg = 1;
	             }
	             else
	             {
	                pERR_42_Flg->RESET_Flg = 0;
	             }


#endif

#if 1 /* IC参考时钟故障 */

	            if(IC_STATUS.IC_STATUS.CLK_ERR || IRQ_ERR.IRQ_STAT.CLK_ERR)
	            {
					if(pERR_Cnt->CLKERR_Cnt < UINT16_MAX)
					{
	                	pERR_Cnt->CLKERR_Cnt ++;
					}
	                pERR_42_Flg->CLK_ERR_Flg   = 1;
	                pICErr_Status->CLK_ERR_Flg = 1;
	                
	                Test_DIAG_Cnt = 7;
	                
	            }
	            else
	            {
	                pERR_Cnt->CLKERR_Cnt       = 0;                
	                pERR_42_Flg->CLK_ERR_Flg   = 0;
	                pICErr_Status->CLK_ERR_Flg = 0;               
	            }
#endif

#if 0
	           /* CMD_ERR_Flg和CRC之类的通信相关故障在17通信过程处理，不在此处理*/

#endif

	            /* 故障处理 */

	            if(pERR_42_Flg->OT_Flg)
	            {
	                /* IC过温，停止工作，进入休眠状态 */

	                if(pERR_Cnt->OT_Cnt > 5)
	                {
	                     pICErr_Status->ERR_ResumeFlg = NOT_Resume;
	                }
	                else
	                {
	                    /** @brief */
	                }

	            }

	            if(pERR_42_Flg->RESET_Flg)
	            {
	                /* IC复位，重新做初始化 */
	                if((pERR_Cnt->RESET_ERR_Cnt > 5) || (pERR_Cnt->SLEF_TEST_Cnt > 5))
	                {
	                    pICErr_Status->ERR_ResumeFlg       = IN_Resume;
	                    pDSI_MASTER->MasterCtrl.TaskStatus = DSI_MASTER_Init_Check;
	                }
	                else
	                {
	                    /** @brief */
	                }
	                
	            }
	            

	            if(pERR_42_Flg->CLK_ERR_Flg)
	            {
	                /* 42参考时钟发生故障 */
	                PRINTF_DSI3COM("ClK_ERR\n");
	                if(pERR_Cnt->CLKERR_Cnt > 5)
	                {
	                    pDSI_MASTER->MasterCtrl.TaskStatus = DSI_MASTER_Init_Check;
	                }
	                else
	                {
	                    /** @brief */
	                }
	                
	            }
	            else
	            {
	                pERR_Cnt->CLKERR_Cnt = 0;
	            }

	            if(pERR_42_Flg->VDSI_UV_Flg || pERR_42_Flg->DSI_UV_Flg || pERR_42_Flg->DSI_OC_Flg)
	            {
	                
	                /* 使能DSI通道，尝试恢复DSI电压正常 */
	                
	                if((pERR_Cnt->DSIUV_OC_Cnt > 1) || (1 == pERR_42_Flg->DSI_UV_Flg))
	                {
	                    pERR_Cnt->GUV_Resume_Cnt++;
	                    
	                    for(DSICHlID = DSIChl1;DSICHlID < DSIChlNum;DSICHlID++)
	                    {
	                        flg = TransWMasterDSIReg(DSIMasterID,W_DSI1_CFG + (DSICHlID * DSIOff),
	                              pDSI_MASTER->pWRRegDataGroup[W_DSI1_CFG + (DSICHlID * DSIOff)]);
	                        if(flg != eDSIReturnType_OK)
	                        {
	                            /** @brief ERR */
	                            PRINTF_DSI3COM("TransWMasterDSIReg  NG RetFlg %d \n",flg);
	                        }
	                    }
	                    
	                    PRINTF_DSI3COM("DSI_UV\n");
	                    
	                    pICErr_Status->ERR_ResumeFlg = IN_Resume;

	                    if(pERR_Cnt->GUV_Resume_Cnt > 10)
	                    {
	                        pICErr_Status->ERR_ResumeFlg = NOT_Resume;
	                        /* 使能TRE未能解决欠压、过流故障。通知应用层 */
	                        /* 过压、欠压、过流状态仍存在时，使能TRE也无法恢复DSI电压正常。 */
	                        PRINTF_DSI3COM("UV Resume NOT_OK\n");
	                    
	                    }
	                }
	                else
	                {
	                    /** @brief */
	                
	                }


	            }
	    
	            /* 清除IRQ故障标志 */
	            for(LcWRRegCnt =  W1C_IRQ_STAT;LcWRRegCnt < W_VDSI_CTRL;LcWRRegCnt++)
	            {
	                flg = TransWMasterDSIReg(DSIMasterID,LcWRRegCnt,pDSI_MASTER->pWRRegDataGroup[LcWRRegCnt]);
	                if(flg != eDSIReturnType_OK)
	                {
	                    /** @brief ERR */
	                    PRINTF_DSI3COM("TransWMasterDSIReg  NG RetFlg %d \n",flg);

						if(pDSI_MASTER->MasterCtrl.SPITxErrCnt < UINT32_MAX)
						{
							pDSI_MASTER->MasterCtrl.SPITxErrCnt++;
						}
	                }
	            }
	        }

	        
	    }
	    else
	    {
			for(DSICHlID = DSIChl1;DSICHlID < DSIChlNum;DSICHlID++)
			{
				pDSIPinDiag = &DSIPinStsDiag[DSIMasterID][DSICHlID];
				
				if(DSIPinShortToGnd == pDSIPinDiag->DSIPinSts)
				{
					pDSIPinDiag->ShortToGndFilterCtrl.Input = FALSE;
					AK2_PubAI_Filter(&pDSIPinDiag->ShortToGndFilterCtrl);
					
					if(FALSE == pDSIPinDiag->ShortToGndFilterCtrl.Output)
					{
						pDSIPinDiag->DSIPinSts = DSIPinNor;
					}
				}
				else if(DSIPinShortToPwr == pDSIPinDiag->DSIPinSts)
				{
					pDSIPinDiag->ShortToPwrFilterCtrl.Input = FALSE;
					AK2_PubAI_Filter(&pDSIPinDiag->ShortToPwrFilterCtrl);
					
					if(FALSE == pDSIPinDiag->ShortToPwrFilterCtrl.Output)
					{
						pDSIPinDiag->DSIPinSts = DSIPinNor;
					}
				}
				else
				{
					pDSIPinDiag->ShortToGndFilterCtrl.Count = 0;
					pDSIPinDiag->ShortToPwrFilterCtrl.Count = 0;
				}
			}
			
			if((DSIPinNor == pDSI1PinDiag->DSIPinSts) && (DSIPinNor == pDSI2PinDiag->DSIPinSts))
			{
		        SetDSICHLWorkStatus(DSIMasterID,DSIChl1,DSICHL_IDLE);
		        SetDSICHLWorkStatus(DSIMasterID,DSIChl2,DSICHL_IDLE);
		        /* 故障解除，恢复正常工作 */
				memset(&pDSI_MASTER->ERR_Cnt,0,sizeof(Elmos42ERR_Cnt_str));
				ClearICErrStatus(pDSI_MASTER->DSIMasterID);
		        pICErr_Status->ERR_ResumeFlg       = OK_Resume;
		        pDSI_MASTER->MasterCtrl.TaskStatus = DSI_MASTER_READY;
				pDSI_MASTER->MasterCtrl.SPITxErrCnt = 0;


		        PRINTF_DSI3COM("Fail Resume OK\n");
			}
	        
	    }
	}
	
	/* 检测INTB脚是否断开,短路的情况下不报内部故障 */
	if((pDSICOM_SetParam->ReadINTB_PinFunc() == 0) 
		&& (DSIPinNor == pDSI1PinDiag->DSIPinSts)
		&& (DSIPinNor == pDSI2PinDiag->DSIPinSts))
	{
		if(pERR_Cnt->INTB_LowCnt < UINT16_MAX)
		{
			pERR_Cnt->INTB_LowCnt++;
		}
	}
	else
	{
		pERR_Cnt->INTB_LowCnt = 0;
	}
	
     PRINTF_DSI3COM("Test_DIAG_Cnt = %d\n",Test_DIAG_Cnt);

     PRINTF_DSI3COM("DIAG END\n");
    return eDSIReturnType_OK;
}

/******************************************************************************/
/**
 * @brief            Elmos521.42 请求进入休眠状态处理
 * @param[in]        pDSI_MASTER
 *
 * @return           eDSIReturnType_OK     
 */
/*****************************************************************************/

DSIReturnType_en DSI_MASTER_GoToSleepFunc(DSI_MASTER_Cfg_str *pDSI_MASTER)
{

    pDSI_MASTER->MasterCtrl.TaskStatus = DSI_MASTER_Sleep;
    return eDSIReturnType_OK;
}

/******************************************************************************/
/**
 * @brief            Elmos521.42 休眠状态处理
 * @param[in]        pDSI_MASTER
 *
 * @return           eDSIReturnType_OK     
 */
/*****************************************************************************/

DSIReturnType_en DSI_MASTER_SleepFunc(DSI_MASTER_Cfg_str *pDSI_MASTER)
{


    pDSI_MASTER->MasterCtrl.TaskStatus = DSI_MASTER_Init_Check;
    return eDSIReturnType_OK;
}

/** @brief 521.42 状态处理函数 */
const DSI_MASTERCallBakFunc GDSI_MASTER_Func[DSI_MASTER_StatusNum]=
{
        [DSI_MASTER_Init_Check]  = DSI_MASTER_InitCheckFunc,
        [DSI_MASTER_Init_Active] = DSI_MASTER_InitActiveFunc,
        [DSI_MASTER_Init_Finish] = DSI_MASTER_InitFinishFunc,
        [DSI_MASTER_READY]       = DSI_MASTER_ReadyFunc,
        [DSI_MASTER_DIAG]        = DSI_MASTER_DIAGFunc,
        [DSI_MASTER_ToSleep]     = DSI_MASTER_GoToSleepFunc,
        [DSI_MASTER_Sleep]       = DSI_MASTER_SleepFunc,

};

/******************************************************************************/
/**
 * @brief            设置Elmos521.42 进行初始化
 * @param[in]        DSIMasterID
 *
 * @return           521.42 通信任务状态
 */
/*****************************************************************************/

DSI_MASTER_Status SetDSIComWorkStatusInit(DSIMasterID_en DSIMasterID)
{
    DSI_MASTER_Cfg_str *pDSI_MASTER;
    pDSI_MASTER = &G_DSI_MASTER[DSIMasterID];

    pDSI_MASTER->MasterCtrl.TaskStatus = DSI_MASTER_Init_Check;
    return pDSI_MASTER->MasterCtrl.TaskStatus;
}

/******************************************************************************/
/**
 * @brief            设置Elmos521.42 进入Sleep
 * @param[in]        DSIMasterID
 *
 * @return           521.42 通信任务状态
 */
/*****************************************************************************/

DSI_MASTER_Status SetDSIComWorkStatusToSleep(DSIMasterID_en DSIMasterID)
{
    DSI_MASTER_Cfg_str *pDSI_MASTER;
    pDSI_MASTER = &G_DSI_MASTER[DSIMasterID];

    pDSI_MASTER->MasterCtrl.TaskStatus = DSI_MASTER_ToSleep;
    return pDSI_MASTER->MasterCtrl.TaskStatus;
}

/******************************************************************************/
/**
 * @brief            获取521.42 通信任务状态
 * @param[in]        DSIMasterID
 *
 * @return           521.42 通信任务状态
 */
/*****************************************************************************/

DSI_MASTER_Status GetDSIComWorkStatus(DSIMasterID_en DSIMasterID)
{

    DSI_MASTER_Cfg_str *pDSI_MASTER;
    pDSI_MASTER = &G_DSI_MASTER[DSIMasterID];

    return pDSI_MASTER->MasterCtrl.TaskStatus;
}


void DSI_Master_SPITransStsMonitor(DSIMasterID_en DSIMasterID)
{
    DSI_MASTER_Cfg_str *pDSI_MASTER;
	pDSI_MASTER = &G_DSI_MASTER[DSIMasterID];

	if(WaitSPIDataTransDone == pDSI_MASTER->SPITransCtrl.SPIDataTransSts)
	{
		pDSI_MASTER->SPITransCtrl.u16TransTimeOutCnt++;
		if(pDSI_MASTER->SPITransCtrl.u16TransTimeOutCnt > pDSI_MASTER->SPITransCtrl.u16TransTimeOut)
		{
			pDSI_MASTER->SPITransCtrl.SPIDataTransSts = NoSPIDataTrans;
			pDSI_MASTER->SPITransCtrl.u16TransTimeOutCnt = 0;
		}
	}
}

/******************************************************************************/
/**
 * @brief            DSI通信任务主函数
 * @param[in]        
 *
 * @return           eDSIReturnType_OK  
 */
/*****************************************************************************/

DSIReturnType_en DSIComWorkMainFunc()
{
    DSIReturnType_en LReturn = eDSIReturnType_OK;
    DSIMasterID_en DSIMasterID = DSIMaster0;

    DSI_MASTER_Cfg_str *pDSI_MASTER;

    DSI_MASTER_Status MasterWorkStatus;
    
    for(DSIMasterID = DSIMaster0; DSIMasterID < DSIMasterNum;DSIMasterID++)
    {
        pDSI_MASTER = &G_DSI_MASTER[DSIMasterID];

        MasterWorkStatus = pDSI_MASTER->MasterCtrl.TaskStatus;
        
        if(MasterWorkStatus >= DSI_MASTER_StatusNum)
        {
            LReturn = eDSIReturnType_N_OK;
        }
        else
        {
            /** @brief DSI工作状态机 */
            GDSI_MASTER_Func[MasterWorkStatus](pDSI_MASTER);
        }
        
        DSI_Master_DCRB_1msTask(DSIMasterID);
        DSITimeOutMonitor(DSIMasterID);
        DSI_SPITimeOutMonitor(DSIMasterID);
		//DSI_Master_SPITransStsMonitor(DSIMasterID);
    }
    
    return LReturn;
}

void ReadDSIMasterICTempSts(DSIMasterICTempSts_e *pSts)
{
	if((DSIMasterIC_Temperature_Over == DSIMasterICTempDiag[DSIMaster0].DSIMasterICTempSts)
		||(DSIMasterIC_Temperature_Over == DSIMasterICTempDiag[DSIMaster1].DSIMasterICTempSts))
	{	
		*pSts = DSIMasterIC_Temperature_Over;
	}
	else
	{
		*pSts = DSIMasterIC_Temperature_Normal;
	}
}

DSIPinSts_en GetDSIPinSts(DSIMasterID_en LeDSIMasterID,DSIChlID_en LeDSIChlID)
{
	return DSIPinStsDiag[LeDSIMasterID][LeDSIChlID].DSIPinSts;
}

bool GetDSIMasterErrSts(void)
{
	Elmos42ERR_Cnt_str   *pIC1ErrCnt;   /* IC故障计数     */
	Elmos42ERR_Cnt_str   *pIC2ErrCnt;   /* IC故障计数     */

    pIC1ErrCnt = &G_DSI_MASTER[DSIMaster0].ERR_Cnt;
    pIC2ErrCnt = &G_DSI_MASTER[DSIMaster1].ERR_Cnt;
	
	if((pIC1ErrCnt->OT_Cnt > 5)
      || (pIC1ErrCnt->CLKERR_Cnt > 5)
      || (pIC1ErrCnt->DSIUV_OC_Cnt > 5)
      || (pIC1ErrCnt->RESET_ERR_Cnt > 5)
      || (pIC1ErrCnt->VCC_UV_Cnt > 5)
      || (pIC1ErrCnt->VDSI_UV_Cnt > 5)
      || (pIC1ErrCnt->SLEF_TEST_Cnt > 5)
      || (pIC1ErrCnt->RFCErrCnt > 10)
	  || (pIC1ErrCnt->GINIT_ErrCnt > 10)
	  || (pIC1ErrCnt->INTB_LowCnt > 20)
	  || (pIC2ErrCnt->OT_Cnt > 5)
      || (pIC2ErrCnt->CLKERR_Cnt > 5)
      || (pIC2ErrCnt->DSIUV_OC_Cnt > 5)
      || (pIC2ErrCnt->RESET_ERR_Cnt > 5)
      || (pIC2ErrCnt->VCC_UV_Cnt > 5)
      || (pIC2ErrCnt->VDSI_UV_Cnt > 5)
      || (pIC2ErrCnt->SLEF_TEST_Cnt > 5)
      || (pIC2ErrCnt->RFCErrCnt > 10)
	  || (pIC2ErrCnt->GINIT_ErrCnt > 10)
      || (pIC2ErrCnt->INTB_LowCnt > 20)
	  || (TRUE == DCRBTimeOutFilterCtrl[DSIMaster0][DSIChl1].Output)
	  || (TRUE == DCRBTimeOutFilterCtrl[DSIMaster0][DSIChl2].Output)
	  || (TRUE == DCRBTimeOutFilterCtrl[DSIMaster1][DSIChl1].Output)
	  || (TRUE == DCRBTimeOutFilterCtrl[DSIMaster1][DSIChl2].Output)
	  || (G_DSI_MASTER[DSIMaster0].MasterCtrl.SPITxErrCnt > 10)
	  || (G_DSI_MASTER[DSIMaster1].MasterCtrl.SPITxErrCnt > 10))
	{
		return TRUE;
	}
	else
	{
		return FALSE;
	}
}


