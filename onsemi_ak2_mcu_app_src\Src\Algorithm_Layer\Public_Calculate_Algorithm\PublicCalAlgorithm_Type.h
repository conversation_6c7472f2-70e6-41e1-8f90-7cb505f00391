/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * PublicCalAlgorithm_Type: 
 * Created on: 2022-11-26 09:59
 * Original designer: AntonyFang
 ******************************************************************************/
#ifndef PublicCalAlgorithm_Type_H
#define PublicCalAlgorithm_Type_H

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "types.h"






/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************************
* 设计描述 : 定义公共算法内部使用的车辆Odo坐标类型
*******************************************************************************/
typedef struct
{
    float fCarRearAxle_X;
    float fCarRearAxle_Y;
    float fCarRearAxle_Angle;
    float fCarRearAxle_SinAngle;
    float fCarRearAxle_CosAngle;
}Pub_CalAiCarOdoType;


typedef enum
{
    SORT_DESCENDING  = 0u,/* 降序排列,值大的在前 */

    SORT_ASCENDING,       /* 升序排列,值小的在前 */
}Pub_SortTypeType;


/******************************************************************************
* 设计描述 : 定义公共算法内部使用的探头在车辆坐标系中的坐标类型
*******************************************************************************/
typedef struct
{
    float fSnsCoorX;
    float fSnsCoorY;
    float fSnsCoorSinAngle;
    float fSnsCoorCosAngle;
}Pub_CalAiSnsCarType;


/******************************************************************************
* 设计描述 : 定义公共算法对一组数的处理,求数据的和，平均值、最大值、最小值、中位值
*******************************************************************************/
typedef struct
{
    float fSumValue;
    float fAverageValue;
    float fMaxValue;
    float fMinValue;
    float fMiddleValue;
}Pub_CalAiArrayDataHandleType;

typedef enum
{
    PUB_POINT_ON_LINE  = 0u,    /* 点在直线上 */
    PUB_POINT_ON_LINE_LEFT,
    PUB_POINT_ON_LINE_RIGHT
}Pub_Point2LineType;

/******************************************************************************
* 设计描述 : 定义滤波上限、下限，滤波步进
*******************************************************************************/
typedef struct
{
	uint32 UpperLimit;
	uint32 LowerLimit;
	uint32 UpStep;
	uint32 DownStep;
	uint32 Count;
	bool   Input;
	bool   Output;
}Pub_FilterCtrl_t;


/******************************************************************************
* 设计描述 : 定义公共算法点状障碍物类型
*******************************************************************************/
typedef struct
{
    float fPoint_X;
    float fPoint_Y;
}Pub_CalAiPointType;


/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/








#endif /* end of PublicCalAlgorithm_Type_H */

