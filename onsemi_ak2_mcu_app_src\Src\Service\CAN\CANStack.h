
#ifndef __CANSTACK_H__
#define __CANSTACK_H__

/******************************************************************************* 
* Includes 
********************************************************************************/ 
#include "LhRule.h"
#include "EELHal.h"
/****************************************************************************** 
* Constants and macros 
*******************************************************************************/
#define NO_NRC_78        0xFFU

#define EB_TRUE          1U
#define EB_FALSE         0U

/****************************************************************************** 
* External objects 
********************************************************************************/

/******************************************************************************* 
* Global Functions 
********************************************************************************/ 
void CANStackInit (void);
void CANStackManage (void);
void CAN_Task50ms(void);
#endif  

