/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * SnsCoorCalculate_Prg: 
 * Created on: 2023-08-02 14:55
 * Original designer: AntonyFang
 ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "MapCoorCalculate_Int.h"
#include "MapCoorCalculate_Type.h"
#include "MapEchoFilterAndSigGroup_int.h"
#include "MapRawDataCalib.h"
#include "SnsRawData_Int.h"
#include "PublicCalAlgorithm_Int.h"
#if !VS_SIMULATE_ENABLE
#include "TimerManage.h"
#include "debug.h"
#endif

#if VHE_POINT_CLOUD_SIMULATE
#include "MapFromAllPoints.h"
#endif

/******************************************************************************/
/******************************************************************************/
/****************************** Public Definition *****************************/
/******************************************************************************/
/******************************************************************************/
Cloud_ObjMapToCANType GstrCloud_ObjMap[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];
SnsObjCoorPointType   GstrObjCoorPoint[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];
SnsPointCloudBufType  GstrPointCloudBuf[PDC_SNS_GROUP_NUM][POINT_CLOUD_AREA_NUM];
AreaClusterBufType    GstrAreaClusterBuf;
SideAreaMapType       GstrSideAreaMap[SIDE_SNS_NUM];
SideAreaPointBufType  GstrSideAreaPointBuf[SIDE_SNS_NUM];


VehiclePointCloudBufType GstrVehiclePointCloudBuf;


/******************************************************************************/
/******************************************************************************/
/****************************** Private Definition ****************************/
/******************************************************************************/
/******************************************************************************/
const ObjCoorType GstrObjCoorInit = 
{
    .fObjX = 0,
    .fObjY = 0
};

const ObjCoorPointUnitType GstrObjCoorPointUnitInit = 
{
    .strObjSnsCoor.fObjX = 0,
    .strObjSnsCoor.fObjY = 0,    
    .strObjCarCoor.fObjX = 0,
    .strObjCarCoor.fObjY = 0,
    .strObjOdoCoor.fObjX = 0,
    .strObjOdoCoor.fObjY = 0,
    .fObjCarCoor_Sub = 0,
    .fObjToBumperDis = 0,
    .u16MasterDis    = OBJ_COOR_INVALID_DIS,
    .u16ListenDis    = OBJ_COOR_INVALID_DIS,
    .u16MasterHeight = OBJ_COOR_INVALID_HEIGHT,
    .u16ListenHeight = OBJ_COOR_INVALID_HEIGHT,
    .u16MasterSecondHeight = OBJ_COOR_INVALID_HEIGHT,
    .u16ListenSecondHeight = OBJ_COOR_INVALID_HEIGHT,
    .enuObjType      = OBJ_NONE_TYPE,
    .enuObjArea      = MAP_AREA_NONE,
    .u8CoorValidFlag = 0,
    .u8SnsNoiseCnt = 0
};
const SnsPointCloudUnitType GstrPointCloudUnitInit = 
{
	.strObjCarCoor = {0,0},
    .strObjOdoCoor = {0,0},
    .enuObjType = OBJ_NONE_TYPE,
    .enuDetType = POINT_CLOUD_DET_NONE,
    .enuSnsCh = PDC_SNS_CH_NONE,
    .eMeasType = PDC_SNS_MEAS_IDLE,
    .u32DetSysTime = 0,
    .u16ExistTime = 0,
    .u16MasterDis = OBJ_COOR_INVALID_DIS,
    .u16MasterHeight = OBJ_COOR_INVALID_HEIGHT,
    .u8ValidFlag = 0,
    .u8TypeAlredayBeUseFlag = 0,
    .u8CoorAlredayBeUseFlag = 0,
    .u8SnsNoiseCnt = 0
};


const SnsAreaMapType  GstrAreaMapInit = 
{
    .ObjTypeCnt.u8StdPVC_PointCnt = 0,
    .ObjTypeCnt.u8ChirpPVC_PointCnt = 0,
    .ObjTypeCnt.u8StdLowCurb_PointCnt = 0,
    .ObjTypeCnt.u8ChirpLowCurb_PointCnt = 0,
    .ObjTypeCnt.u8StdBigWall_PointCnt = 0,
    .ObjTypeCnt.u8ChirpBigWall_PointCnt = 0,
    .ObjTypeCnt.u8StdHigh_HighCurb_PointCnt = 0,
    .ObjTypeCnt.u8ChirpHigh_HighCurb_PointCnt = 0,
    .ObjTypeCnt.u8StdTotalPointCnt = 0,
    .ObjTypeCnt.u8ChirpTotalPointCnt = 0,
    .P1_CarCoor.fObjX = 0,
    .P1_CarCoor.fObjY = 0,
    .P2_CarCoor.fObjX = 0,
    .P2_CarCoor.fObjY = 0,
    .P1_OdoCoor.fObjX = 0,
    .P1_OdoCoor.fObjY = 0,
    .P2_OdoCoor.fObjX = 0,
    .P2_OdoCoor.fObjY = 0,
    .MapSharp = AREA_MAP_SHAPE_NONE,
    .MapId = AREA_MAP_ID_NONE,
    .enuMapType = OBJ_NONE_TYPE,
    .enuSideMapSns = SIDE_SNS_NONE,
    .u32MapTime = 0,
    .u16MasterDis = OBJ_COOR_INVALID_DIS,
    .u16StdMasterHeight = OBJ_COOR_INVALID_HEIGHT,
    .u16ChirpMasterHeight = OBJ_COOR_INVALID_HEIGHT,
    .u8BeFusionToRealMapFlag = 0,
    .enuMeasType = PDC_SNS_MEAS_IDLE,
};


const ClusterType GstrClusterInit = 
{
    .ObjTypeCnt.u8StdPVC_PointCnt = 0,
    .ObjTypeCnt.u8ChirpPVC_PointCnt = 0,
    .ObjTypeCnt.u8StdLowCurb_PointCnt = 0,
    .ObjTypeCnt.u8ChirpLowCurb_PointCnt = 0,
    .ObjTypeCnt.u8StdBigWall_PointCnt = 0,
    .ObjTypeCnt.u8ChirpBigWall_PointCnt = 0,
    .ObjTypeCnt.u8StdHigh_HighCurb_PointCnt = 0,
    .ObjTypeCnt.u8ChirpHigh_HighCurb_PointCnt = 0,
    .ObjTypeCnt.u8StdTotalPointCnt = 0,
    .ObjTypeCnt.u8ChirpTotalPointCnt = 0,
    .ClusterCoor.fObjX = 0,
    .ClusterCoor.fObjY = 0,
    .enuClusterType = OBJ_NONE_TYPE,
    .u32ClusterTime = 0,
    .u16MasterDis = OBJ_COOR_INVALID_DIS,
    .u16StdMasterHeight = OBJ_COOR_INVALID_HEIGHT,
    .u16ChirpMasterHeight = OBJ_COOR_INVALID_HEIGHT
};

const ObjTypeCntType GstrObjTypeCntInit = 
{
    .u8StdPVC_PointCnt = 0,
    .u8ChirpPVC_PointCnt = 0,
    .u8StdLowCurb_PointCnt = 0,
    .u8ChirpLowCurb_PointCnt = 0,
    .u8StdBigWall_PointCnt = 0,
    .u8ChirpBigWall_PointCnt = 0,
    .u8StdHigh_HighCurb_PointCnt = 0,
    .u8ChirpHigh_HighCurb_PointCnt = 0,
    .u8StdTotalPointCnt = 0,
    .u8ChirpTotalPointCnt = 0
};

const SnsObjCoorLimitType GstrSnsObjCoorLimit[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM] = 
{
    [PDC_SNS_GROUP_FRONT] = 
    {
        [PDC_SNS_CH_FLS] =
        {
            .s16LeftListenObj_Y_Min   = 0,
            .s16LeftListenObj_Y_Max   = 0,
            .s16RightListenObj_Y_Min   = -850,
            .s16RightListenObj_Y_Max   = 1000,  /* 1100 修改为1900 */
        },
        
        [PDC_SNS_CH_FL] =
        {
            .s16LeftListenObj_Y_Min   = -500,
            .s16LeftListenObj_Y_Max   = 850,
            .s16RightListenObj_Y_Min   = -860,
            .s16RightListenObj_Y_Max   = 500,
        },
    
        [PDC_SNS_CH_FML] =
        {
            .s16LeftListenObj_Y_Min   = -620,
            .s16LeftListenObj_Y_Max   = 750,
            .s16RightListenObj_Y_Min   = -650,
            .s16RightListenObj_Y_Max   = 300,
        },
    
        [PDC_SNS_CH_FMR] =
        {
            .s16LeftListenObj_Y_Min   = -300,
            .s16LeftListenObj_Y_Max   = 650,
            .s16RightListenObj_Y_Min   = -750,
            .s16RightListenObj_Y_Max   = 620,
        },
    
        [PDC_SNS_CH_FR] =
        {
            .s16LeftListenObj_Y_Min   = -500,
            .s16LeftListenObj_Y_Max   = 860,
            .s16RightListenObj_Y_Min   = -850,
            .s16RightListenObj_Y_Max   = 500,
        },
    
        [PDC_SNS_CH_FRS] =
        {
            .s16LeftListenObj_Y_Min   = -1000,
            .s16LeftListenObj_Y_Max   = 850,
            .s16RightListenObj_Y_Min   = 0,
            .s16RightListenObj_Y_Max   = 0,
        }
    },
        
    [PDC_SNS_GROUP_REAR] = 
    {
        [PDC_SNS_CH_FLS] =
        {
            .s16LeftListenObj_Y_Min   = 0,
            .s16LeftListenObj_Y_Max   = 0,
            .s16RightListenObj_Y_Min   = -850,
            .s16RightListenObj_Y_Max   = 1000,
        },
        
        [PDC_SNS_CH_FL] =
        {
            .s16LeftListenObj_Y_Min   = -500,
            .s16LeftListenObj_Y_Max   = 850,
            .s16RightListenObj_Y_Min   = -750,
            .s16RightListenObj_Y_Max   = 450,
        },
    
        [PDC_SNS_CH_FML] =
        {
            .s16LeftListenObj_Y_Min   = -450,
            .s16LeftListenObj_Y_Max   = 750,
            .s16RightListenObj_Y_Min   = -650,
            .s16RightListenObj_Y_Max   = 300,
        },
    
        [PDC_SNS_CH_FMR] =
        {
            .s16LeftListenObj_Y_Min   = -300,
            .s16LeftListenObj_Y_Max   = 650,
            .s16RightListenObj_Y_Min   = -750,
            .s16RightListenObj_Y_Max   = 450,
        },
    
        [PDC_SNS_CH_FR] =
        {
            .s16LeftListenObj_Y_Min   = -450,
            .s16LeftListenObj_Y_Max   = 750,
            .s16RightListenObj_Y_Min   = -850,
            .s16RightListenObj_Y_Max   = 500,
        },
    
        [PDC_SNS_CH_FRS] =
        {
            .s16LeftListenObj_Y_Min   = -1000,
            .s16LeftListenObj_Y_Max   = 850,
            .s16RightListenObj_Y_Min   = 0,
            .s16RightListenObj_Y_Max   = 0,
        }
    }
};

const uint8* Gu8ObjTypeString[7] = 
{
    [OBJ_NONE_TYPE] = (const uint8*)"Obj None",

    [OBJ_GRAVEL_TYPE] = (const uint8*)"Obj_Gravel",

    [OBJ_PVC_PIPE_TYPE] = (const uint8*)"Obj_PVC",

    [OBJ_HIGH_CURB_TYPE] = (const uint8*)"Obj_High_Curb",

    [OBJ_LOW_CURB_TYPE] = (const uint8*)"Obj_Low_Curb",

    [OBJ_BIGWALL_TYPE] = (const uint8*)"Obj_BigWall",

    [OBJ_HIGH_HIGH_CUEB] = (const uint8*)"Obj_High_High_Curb",
};


/******************************************************************************
 * 函数名称: SideAreaPointBufInit
 * 
 * 功能描述: 侧边区域Map点缓存的初始化
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-01-17 15:55   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SideAreaPointBufInit(void)
{
    uint8 i,j;

    for(i = SIDE_SNS_FLS; i < SIDE_SNS_NUM; i++)
    {
        GstrSideAreaPointBuf[i].u8StartInx = 0;
        GstrSideAreaPointBuf[i].u8EndInx = 0;
        GstrSideAreaPointBuf[i].u8MapStartInx = 255;
        GstrSideAreaPointBuf[i].u8MapEndInx = 255;
        GstrSideAreaPointBuf[i].u8ValidBufCnt = 0;
        GstrSideAreaPointBuf[i].u8AfterStartCnt = 0;
        GstrSideAreaPointBuf[i].u8MapEndInxNoUpdateCnt = 0;

        GstrSideAreaPointBuf[i].u8FirstUseMasterInx = 255;
        GstrSideAreaPointBuf[i].u8ContinueUseMasterCnt = 0;
        
        for(j = 0; j < AREA_POINT_BUF_CNT; j++)
        {
            GstrSideAreaPointBuf[i].Buf[j].PointOdoCoor = GstrObjCoorInit;
            GstrSideAreaPointBuf[i].Buf[j].u32DetSysTime = 0;
            GstrSideAreaPointBuf[i].Buf[j].enuObjType = OBJ_NONE_TYPE;
            GstrSideAreaPointBuf[i].Buf[j].enuDetType = POINT_CLOUD_DET_NONE;

            
            GstrSideAreaPointBuf[i].Buf[j].u16MasterDis = OBJ_COOR_INVALID_DIS;
            GstrSideAreaPointBuf[i].Buf[j].u16ListenDis = OBJ_COOR_INVALID_DIS;
            GstrSideAreaPointBuf[i].Buf[j].u16MasterHeight = 0;
            GstrSideAreaPointBuf[i].Buf[j].u16ListenHeight = 0;
            GstrSideAreaPointBuf[i].Buf[j].u8CloudValidFlag = 0;
            GstrSideAreaPointBuf[i].Buf[j].u8TypeBeUsedFlag = 0;
        }
        for(j = 0; j < AREA_POINT_BUF_ID_CNT; j++)
        {
            GstrSideAreaPointBuf[i].u8IdBuf[j] = 255;
        }
    }
}


/******************************************************************************
 * 函数名称: AddOneSideAreaPointToBuf
 * 
 * 功能描述: 添加侧边新的点云单元数据至缓存
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-04-17 09:35   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void AddOneSideAreaPointToBuf(SideAreaPointUnit *LpstrSideAreaPointUnit, SideSnsIndexType LenuSideSnsIndex)
{
    uint8 Lu8EndInxRem;
    SideAreaPointBufType  *LpstrSideAreaPointBuf;
    uint8 Lu8CurInx;

    LpstrSideAreaPointBuf = &GstrSideAreaPointBuf[LenuSideSnsIndex];
    Lu8CurInx = LpstrSideAreaPointBuf->u8EndInx;

    /* 首先判断队列是否已满 */
    Lu8EndInxRem = LpstrSideAreaPointBuf->u8EndInx+1;
    Lu8EndInxRem = Lu8EndInxRem%AREA_POINT_BUF_CNT;
    
    if(Lu8EndInxRem == LpstrSideAreaPointBuf->u8StartInx)
    {
        /* 队列已满,则把开始索引值同步往后移，保留最新时间的点云 */
        LpstrSideAreaPointBuf->Buf[Lu8CurInx] = *LpstrSideAreaPointUnit;
        LpstrSideAreaPointBuf->u8EndInx++;
        if(LpstrSideAreaPointBuf->u8EndInx == AREA_POINT_BUF_CNT)
        {
            LpstrSideAreaPointBuf->u8EndInx = 0;
        }
        LpstrSideAreaPointBuf->u8StartInx++;
        if(LpstrSideAreaPointBuf->u8StartInx == AREA_POINT_BUF_CNT)
        {
            LpstrSideAreaPointBuf->u8StartInx = 0;
        }
    }
    else
    {
        /* 队列未满 */
        LpstrSideAreaPointBuf->Buf[Lu8CurInx] = *LpstrSideAreaPointUnit;
        LpstrSideAreaPointBuf->u8EndInx++;
        if(LpstrSideAreaPointBuf->u8EndInx == AREA_POINT_BUF_CNT)
        {
            LpstrSideAreaPointBuf->u8EndInx = 0;
        }
    }  

    if(LpstrSideAreaPointBuf->u8MapEndInx != 255)
    {
        LpstrSideAreaPointBuf->u8MapEndInxNoUpdateCnt++;
    }

    if(LpstrSideAreaPointBuf->u8FirstUseMasterInx == 255)
    {
        if(LpstrSideAreaPointUnit->enuDetType == POINT_CLOUD_DET_BY_MASTER)
        {
            LpstrSideAreaPointBuf->u8FirstUseMasterInx = Lu8CurInx;
            LpstrSideAreaPointBuf->u8ContinueUseMasterCnt++;    
        }
    }
    else
    {
        if(LpstrSideAreaPointUnit->enuDetType == POINT_CLOUD_DET_BY_MASTER)
        {
            LpstrSideAreaPointBuf->u8ContinueUseMasterCnt++;    
        }
        else
        {
            LpstrSideAreaPointBuf->u8FirstUseMasterInx = 255;
            LpstrSideAreaPointBuf->u8ContinueUseMasterCnt = 0;    
        }
    }
    if(LpstrSideAreaPointBuf->u8AfterStartCnt > 0)
    {
        LpstrSideAreaPointBuf->u8AfterStartCnt++;
    }
}



/******************************************************************************
 * 函数名称: SideAreaPointBufUpdate
 * 
 * 功能描述: 侧边区域点的Buf数据更新
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-04-17 10:00   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SideAreaPointBufUpdate(PDCSnsGroupType LenuSnsGroup,PDCSnsChannelType LenuSnsCh)
{
    SnsObjCoorPointType *LpstrSnsObjCoorPoint;
    MapSigGroupDataCacheType *LpstrMapSigGroup;
    Sns_CycleType LenuCurInx;
    SideAreaPointUnit LstrSideAreaPointUnit;
    SideSnsIndexType LenuSideSnsIndex;

    if((LenuSnsCh == PDC_SNS_CH_FLS_RLS)||(LenuSnsCh == PDC_SNS_CH_FRS_RRS))
    {
        if(LenuSnsGroup == 0x00)
        {
            if(LenuSnsCh == PDC_SNS_CH_FLS_RLS)
            {
                LenuSideSnsIndex = SIDE_SNS_FLS;
            }
            else
            {
                LenuSideSnsIndex = SIDE_SNS_FRS;
            }
        }
        else
        {
            if(LenuSnsCh == PDC_SNS_CH_FLS_RLS)
            {
                LenuSideSnsIndex = SIDE_SNS_RLS;
            }
            else
            {
                LenuSideSnsIndex = SIDE_SNS_RRS;
            }
        }
    }
    else
    {
        return;
    }

    LpstrMapSigGroup = &GstrMapSigGroupDataCache[LenuSnsGroup][LenuSnsCh];
    LenuCurInx = LpstrMapSigGroup->enuCurIndex;
    LpstrSnsObjCoorPoint = &GstrObjCoorPoint[LenuSnsGroup][LenuSnsCh];
    
    LstrSideAreaPointUnit.u32DetSysTime = LpstrMapSigGroup->SysDataBuf[LenuCurInx].u32SysTime;
    if(LenuSnsCh == PDC_SNS_CH_FLS_RLS)
    {
        if(LpstrSnsObjCoorPoint->u8RightObjCoorCnt > 0)
        {
            //LstrSideAreaPointUnit.PointOdoCoor = LpstrSnsObjCoorPoint->RightData[0].strObjOdoCoor;
            LstrSideAreaPointUnit.PointOdoCoor = LpstrSnsObjCoorPoint->MasterData[0].strObjOdoCoor;
            LstrSideAreaPointUnit.enuObjType = LpstrSnsObjCoorPoint->RightData[0].enuObjType;
            LstrSideAreaPointUnit.enuDetType = POINT_CLOUD_DET_BY_RIGHT;
            LstrSideAreaPointUnit.u16MasterDis = LpstrSnsObjCoorPoint->RightData[0].u16MasterDis;
            LstrSideAreaPointUnit.u16ListenDis = LpstrSnsObjCoorPoint->RightData[0].u16ListenDis;
            LstrSideAreaPointUnit.u16MasterHeight = LpstrSnsObjCoorPoint->RightData[0].u16MasterHeight;
            LstrSideAreaPointUnit.u16ListenHeight = LpstrSnsObjCoorPoint->RightData[0].u16ListenHeight;
            LstrSideAreaPointUnit.u8CloudValidFlag = 1;
            LstrSideAreaPointUnit.u8TypeBeUsedFlag = 0;
        }
        else if(LpstrSnsObjCoorPoint->u8MasterObjCoorCnt > 0)
        {
            LstrSideAreaPointUnit.PointOdoCoor = LpstrSnsObjCoorPoint->MasterData[0].strObjOdoCoor;
            LstrSideAreaPointUnit.enuObjType = LpstrSnsObjCoorPoint->MasterData[0].enuObjType;
            LstrSideAreaPointUnit.enuDetType = POINT_CLOUD_DET_BY_MASTER;
            LstrSideAreaPointUnit.u16MasterDis = LpstrSnsObjCoorPoint->MasterData[0].u16MasterDis;
            LstrSideAreaPointUnit.u16ListenDis = LpstrSnsObjCoorPoint->MasterData[0].u16ListenDis;
            LstrSideAreaPointUnit.u16MasterHeight = LpstrSnsObjCoorPoint->MasterData[0].u16MasterHeight;
            LstrSideAreaPointUnit.u16ListenHeight = LpstrSnsObjCoorPoint->MasterData[0].u16ListenHeight;
            LstrSideAreaPointUnit.u8CloudValidFlag = 1;
            LstrSideAreaPointUnit.u8TypeBeUsedFlag = 0;
        }
        else
        {
            LstrSideAreaPointUnit.PointOdoCoor = GstrObjCoorInit;
            LstrSideAreaPointUnit.enuObjType = LpstrSnsObjCoorPoint->MasterData[0].enuObjType;;
            LstrSideAreaPointUnit.enuDetType = POINT_CLOUD_DET_NONE;
            LstrSideAreaPointUnit.u16MasterDis = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16ActualDis[0];
            LstrSideAreaPointUnit.u16ListenDis = LpstrMapSigGroup->RightBuf[LenuCurInx].u16ActualDis[0];
            LstrSideAreaPointUnit.u16MasterHeight = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16FirstEchoHeight[0]+LpstrMapSigGroup->MasterBuf[LenuCurInx].u16SecondEchoHeight[0];
            LstrSideAreaPointUnit.u16ListenHeight = LpstrMapSigGroup->RightBuf[LenuCurInx].u16FirstEchoHeight[0];
            LstrSideAreaPointUnit.u8CloudValidFlag = 0;
            LstrSideAreaPointUnit.u8TypeBeUsedFlag = 0;
        }
    }
    else
    {
        if(LpstrSnsObjCoorPoint->u8LeftObjCoorCnt > 0)
        {
            //LstrSideAreaPointUnit.PointOdoCoor = LpstrSnsObjCoorPoint->LeftData[0].strObjOdoCoor;
            LstrSideAreaPointUnit.PointOdoCoor = LpstrSnsObjCoorPoint->MasterData[0].strObjOdoCoor;
            LstrSideAreaPointUnit.enuObjType = LpstrSnsObjCoorPoint->LeftData[0].enuObjType;
            LstrSideAreaPointUnit.enuDetType = POINT_CLOUD_DET_BY_LEFT;
            LstrSideAreaPointUnit.u16MasterDis = LpstrSnsObjCoorPoint->LeftData[0].u16MasterDis;
            LstrSideAreaPointUnit.u16ListenDis = LpstrSnsObjCoorPoint->LeftData[0].u16ListenDis;
            LstrSideAreaPointUnit.u16MasterHeight = LpstrSnsObjCoorPoint->LeftData[0].u16MasterHeight;
            LstrSideAreaPointUnit.u16ListenHeight = LpstrSnsObjCoorPoint->LeftData[0].u16ListenHeight;
            LstrSideAreaPointUnit.u8CloudValidFlag = 1;
            LstrSideAreaPointUnit.u8TypeBeUsedFlag = 0;
        }
        else if(LpstrSnsObjCoorPoint->u8MasterObjCoorCnt > 0)
        {
            LstrSideAreaPointUnit.PointOdoCoor = LpstrSnsObjCoorPoint->MasterData[0].strObjOdoCoor;
            LstrSideAreaPointUnit.enuObjType = LpstrSnsObjCoorPoint->MasterData[0].enuObjType;
            LstrSideAreaPointUnit.enuDetType = POINT_CLOUD_DET_BY_MASTER;
            LstrSideAreaPointUnit.u16MasterDis = LpstrSnsObjCoorPoint->MasterData[0].u16MasterDis;
            LstrSideAreaPointUnit.u16ListenDis = LpstrSnsObjCoorPoint->MasterData[0].u16ListenDis;
            LstrSideAreaPointUnit.u16MasterHeight = LpstrSnsObjCoorPoint->MasterData[0].u16MasterHeight;
            LstrSideAreaPointUnit.u16ListenHeight = LpstrSnsObjCoorPoint->MasterData[0].u16ListenHeight;
            LstrSideAreaPointUnit.u8CloudValidFlag = 1;
            LstrSideAreaPointUnit.u8TypeBeUsedFlag = 0;
        }
        else
        {
            LstrSideAreaPointUnit.PointOdoCoor = GstrObjCoorInit;
            LstrSideAreaPointUnit.enuObjType = LpstrSnsObjCoorPoint->MasterData[0].enuObjType;;
            LstrSideAreaPointUnit.enuDetType = POINT_CLOUD_DET_NONE;
            LstrSideAreaPointUnit.u16MasterDis = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16ActualDis[0];
            LstrSideAreaPointUnit.u16ListenDis = LpstrMapSigGroup->RightBuf[LenuCurInx].u16ActualDis[0];
            LstrSideAreaPointUnit.u16MasterHeight = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16FirstEchoHeight[0]+LpstrMapSigGroup->MasterBuf[LenuCurInx].u16SecondEchoHeight[0];
            LstrSideAreaPointUnit.u16ListenHeight = LpstrMapSigGroup->RightBuf[LenuCurInx].u16FirstEchoHeight[0];
            LstrSideAreaPointUnit.u8CloudValidFlag = 0;
            LstrSideAreaPointUnit.u8TypeBeUsedFlag = 0;
        }
    }
    /* 低速下，针对定频无效数据不加入缓存--防止远距离定扫策略无效数据进入缓存，影响后续的回波高度趋势分析 */
    uint8 Lu8NeedDataToBufFlag = 1;
    if(Gu16CarSpdForSnsUse < 450)
    {
        if(LpstrMapSigGroup->SysDataBuf[LenuCurInx].eMeasType == PDC_SNS_MEAS_STD)
        {
            if(LstrSideAreaPointUnit.u16MasterHeight == 0)
            {
                Lu8NeedDataToBufFlag = 0;
            }
        }
    }
    if(Lu8NeedDataToBufFlag)
    {
        AddOneSideAreaPointToBuf(&LstrSideAreaPointUnit,LenuSideSnsIndex);
    }
}



/******************************************************************************
 * 函数名称: GetSideAreaBufId
 * 
 * 功能描述: 按照点云的先后顺序将ID提取出来
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-04-17 14:09   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void GetSideAreaBufId(SideAreaPointBufType  *LpstrSideAreaPointBuf)
{
    LpstrSideAreaPointBuf->u8ValidBufCnt = 0;
    uint8 Lu8StartInx,Lu8EndInx;
    sint8 i;
    uint8 Lu8FindStartInx;

    Lu8StartInx = LpstrSideAreaPointBuf->u8StartInx;
    Lu8EndInx = LpstrSideAreaPointBuf->u8EndInx;

    if(Lu8StartInx != Lu8EndInx)
    {
        if(Lu8StartInx < Lu8EndInx)
        {
            Lu8FindStartInx = Lu8EndInx - 1;
            for(i = Lu8FindStartInx; i >= Lu8StartInx; i--)
            {
                LpstrSideAreaPointBuf->u8IdBuf[LpstrSideAreaPointBuf->u8ValidBufCnt] = i;
                LpstrSideAreaPointBuf->u8ValidBufCnt++;
            }
        }
        else
        {
            if(Lu8EndInx > 0)
            {
                Lu8FindStartInx = Lu8EndInx - 1;
                for(i = Lu8FindStartInx; i >= 0; i--)
                {
                    LpstrSideAreaPointBuf->u8IdBuf[LpstrSideAreaPointBuf->u8ValidBufCnt] = i;
                    LpstrSideAreaPointBuf->u8ValidBufCnt++;
                }
            }
            Lu8FindStartInx = AREA_POINT_BUF_CNT - 1;
            for(i = Lu8FindStartInx; i >= Lu8StartInx; i--)
            {
                LpstrSideAreaPointBuf->u8IdBuf[LpstrSideAreaPointBuf->u8ValidBufCnt] = i;
                LpstrSideAreaPointBuf->u8ValidBufCnt++;
            }
        }
    }
}


/******************************************************************************
 * 函数名称: AnalysisSideAreaBufData
 * 
 * 功能描述: 分析侧边区域Map的数据,并打印
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-04-17 14:24   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void AnalysisSideAreaBufDataAndPrintf(SideAreaPointBufType  *LpstrSideAreaPointBuf,uint8 Lu8MapId)
{
    uint8 i;
    uint8 Lu8Id;
    GetSideAreaBufId(LpstrSideAreaPointBuf);

    /* Map创建时打印前10个数据 */
#if 0
    if((Lu8MapId == 0x02)&&(GfMessageTime > 9)&&(GfMessageTime < 11))
    {
        for(i = 0; i < LpstrSideAreaPointBuf->u8ValidBufCnt; i++)
        {
            Lu8Id = LpstrSideAreaPointBuf->u8IdBuf[i];
            VS_PRINT("Time:%.3f,i:%d,Id:%d,PointTime:%ld,PointMasDis:%d,PointMasHeight:%d,ObjType:%s\r\n",GfMessageTime,i,Lu8Id,\
                LpstrSideAreaPointBuf->Buf[Lu8Id].u32DetSysTime,LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterDis,\
                LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterHeight,Gu8ObjTypeString[LpstrSideAreaPointBuf->Buf[Lu8Id].enuObjType]);
        }
        VS_PRINT("\r\n");
    }
#endif
}

/******************************************************************************
 * 函数名称: UpdateSideAreaPointMapEndInx
 * 
 * 功能描述: 更新Map结束点对应的缓存ID，需要实时更新，最后一次更新即为结束点
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-04-17 20:51   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void UpdateSideAreaPointMapEndInx(SideAreaPointBufType  *LpstrSideAreaPointBuf)
{
    uint8 Lu8StartInx,Lu8EndInx;

    Lu8StartInx = LpstrSideAreaPointBuf->u8StartInx;
    Lu8EndInx = LpstrSideAreaPointBuf->u8EndInx;

    if(Lu8StartInx != Lu8EndInx)
    {
        if(Lu8StartInx < Lu8EndInx)
        {
            LpstrSideAreaPointBuf->u8MapEndInx = Lu8EndInx - 1;
        }
        else
        {
            if(Lu8EndInx > 0)
            {
                LpstrSideAreaPointBuf->u8MapEndInx = Lu8EndInx - 1;
            }
            else
            {
                LpstrSideAreaPointBuf->u8MapEndInx = AREA_POINT_BUF_CNT - 1;
            }
        }
    }
    LpstrSideAreaPointBuf->u8MapEndInxNoUpdateCnt = 0;
}



/******************************************************************************
 * 函数名称: AnalysisSideAreaBufDataWhenMapUpdate
 * 
 * 功能描述: 创建Map时，分之前5个点的回波高度变化趋势，并计算打分值
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-04-17 15:37   V0.1      AntonyFang   初次发布
 ******************************************************************************/
uint8 AnalysisSideAreaBufDataWhenMapUpdate(SideAreaPointBufType  *LpstrSideAreaPointBuf,uint8 Lu8MapId,uint16 Lu16MapMasterDis)
#if 1
{
    uint8 Lu8CreatMapLockHighFlag = 0;
    uint8 i;
    uint8 Lu8Id;
    uint16 Lu16DisSub;
    uint16 Lu16DisSubLimit;
    uint8 Lu8PrintfId = 0x3;

    LpstrSideAreaPointBuf->u8NoUpdateCnt = 0;

    GetSideAreaBufId(LpstrSideAreaPointBuf);
    if(LpstrSideAreaPointBuf->u8ValidBufCnt <= 5)
    {
        return 0;
    }
    Lu16DisSubLimit = 500;

    for(i = 0; i < 5; i++)
    {
        Lu8Id = LpstrSideAreaPointBuf->u8IdBuf[i];
        if(!LpstrSideAreaPointBuf->Buf[Lu8Id].u8TypeBeUsedFlag)
        {
            LpstrSideAreaPointBuf->Buf[Lu8Id].u8TypeBeUsedFlag = 1;
            Lu16DisSub = ABS(LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterDis,Lu16MapMasterDis);
            if(Lu16DisSub < Lu16DisSubLimit)
            {
                LpstrSideAreaPointBuf->u8TotalTypeCnt++;
                if((LpstrSideAreaPointBuf->Buf[Lu8Id].enuObjType >= OBJ_BIGWALL_TYPE)||\
                    (LpstrSideAreaPointBuf->Buf[Lu8Id].enuObjType == OBJ_PVC_PIPE_TYPE))
                {
                    LpstrSideAreaPointBuf->u8HighTypeCnt++;
                }
#if 0
                if(Lu8MapId == Lu8PrintfId)
                {
                    VS_PRINT("Update MapId:%d,Time:%.3f,i:%d,Dis:%d,Height:%d,Type:%s\r\n",Lu8MapId,GfMessageTime,i,\
                        LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterDis,LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterHeight,\
                        Gu8ObjTypeString[LpstrSideAreaPointBuf->Buf[Lu8Id].enuObjType]);
                }
#endif
            }
        }
    }
    if(LpstrSideAreaPointBuf->u8HighTypeCnt > 2)
    {
        Lu8CreatMapLockHighFlag = 1;
    }
#if 0
    if(Lu8MapId == Lu8PrintfId)
    {
        VS_PRINT("Update MapId:%d,Time:%.3f,TotalTypeCnt:%d,HighTypeCnt:%d,LockHighFlag:%d\r\n",Lu8MapId,GfMessageTime,LpstrSideAreaPointBuf->u8TotalTypeCnt,\
                    LpstrSideAreaPointBuf->u8HighTypeCnt,Lu8CreatMapLockHighFlag);
    }
#endif

    
    return Lu8CreatMapLockHighFlag;
}

#else
{
    uint8 Lu8CreatMapLockHighFlag = 0;
    uint8 i;
    uint8 Lu8Id,Lu8Id2;
    uint8 Lu8Socre = 0;
    uint16 Lu16NewestPointDis;
    uint16 Lu16NewestPointHeight;
    uint16 Lu16DisSub;
    uint16 Lu16MaxHeight;
    uint8 Lu8BigWallCnt = 0;
    uint16 Lu16DisSubLimit;
    uint8 Lu8MasterHeightIncreaseFlag = 0;


    GetSideAreaBufId(LpstrSideAreaPointBuf);
    if(LpstrSideAreaPointBuf->u8ValidBufCnt <= 5)
    {
        return 0;
    }
    
    Lu16DisSubLimit = 220;

    Lu8Id = LpstrSideAreaPointBuf->u8IdBuf[0];
    Lu16NewestPointDis = LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterDis;
    Lu16NewestPointHeight = LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterHeight;
    Lu16MaxHeight = Lu16NewestPointHeight;
    if(LpstrSideAreaPointBuf->Buf[Lu8Id].enuObjType >= OBJ_BIGWALL_TYPE)
    {
        Lu8BigWallCnt++;
    }

    /* 最新点和该点前的第4个点比较 */
    Lu8Id = LpstrSideAreaPointBuf->u8IdBuf[5];
    Lu16DisSub = ABS(Lu16NewestPointDis,LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterDis);
    if(Lu16DisSub < Lu16DisSubLimit)
    {
        if(Lu16NewestPointHeight > (LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterHeight*3))
        {
            Lu8Socre += 2;
        }
        if(Lu16MaxHeight < LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterHeight)
        {
            Lu16MaxHeight = LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterHeight;
        }
        if(LpstrSideAreaPointBuf->Buf[Lu8Id].enuObjType >= OBJ_BIGWALL_TYPE)
        {
            Lu8BigWallCnt++;
        }
    }

    /* 最新点和该点前的第3个点比较 */
    Lu8Id = LpstrSideAreaPointBuf->u8IdBuf[4];
    Lu16DisSub = ABS(Lu16NewestPointDis,LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterDis);
    if(Lu16DisSub < Lu16DisSubLimit)
    {
        if(Lu16NewestPointHeight > (LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterHeight*3))
        {
            Lu8Socre += 2;
        }
        else if(Lu16NewestPointHeight > (LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterHeight*2))
        {
            Lu8Socre += 1;
        }
        if(Lu16MaxHeight < LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterHeight)
        {
            Lu16MaxHeight = LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterHeight;
        }
        if(LpstrSideAreaPointBuf->Buf[Lu8Id].enuObjType >= OBJ_BIGWALL_TYPE)
        {
            Lu8BigWallCnt++;
        }
    }
    
    /* 最新点和该点前的第2个点比较 */
    Lu8Id = LpstrSideAreaPointBuf->u8IdBuf[3];
    Lu16DisSub = ABS(Lu16NewestPointDis,LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterDis);
    if(Lu16DisSub < Lu16DisSubLimit)
    {
        if(Lu16NewestPointHeight > (LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterHeight*2))
        {
            Lu8Socre += 2;
        }
        else if(Lu16NewestPointHeight > (LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterHeight+100))
        {
            Lu8Socre += 1;
        }
        if(Lu16MaxHeight < LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterHeight)
        {
            Lu16MaxHeight = LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterHeight;
        }
        if(LpstrSideAreaPointBuf->Buf[Lu8Id].enuObjType >= OBJ_BIGWALL_TYPE)
        {
            Lu8BigWallCnt++;
        }
    }

    /* 最新点和该点前的第1个点比较 */
    Lu8Id = LpstrSideAreaPointBuf->u8IdBuf[2];
    Lu16DisSub = ABS(Lu16NewestPointDis,LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterDis);
    if(Lu16DisSub < Lu16DisSubLimit)
    {
        if(Lu16NewestPointHeight > (LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterHeight+100))
        {
            Lu8Socre += 1;
        }
        if(Lu16MaxHeight < LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterHeight)
        {
            Lu16MaxHeight = LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterHeight;
        }
        if(LpstrSideAreaPointBuf->Buf[Lu8Id].enuObjType >= OBJ_BIGWALL_TYPE)
        {
            Lu8BigWallCnt++;
        }
    }

    /* 最新点和该点前的第1个点比较 */
    Lu8Id = LpstrSideAreaPointBuf->u8IdBuf[1];
    Lu16DisSub = ABS(Lu16NewestPointDis,LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterDis);
    if(Lu16DisSub < Lu16DisSubLimit)
    {
        if(Lu16NewestPointHeight > (LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterHeight+100))
        {
            Lu8Socre += 1;
        }
        if(Lu16MaxHeight < LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterHeight)
        {
            Lu16MaxHeight = LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterHeight;
        }
        if(LpstrSideAreaPointBuf->Buf[Lu8Id].enuObjType >= OBJ_BIGWALL_TYPE)
        {
            Lu8BigWallCnt++;
        }
    }

    if((Lu8BigWallCnt > 4)||((Lu8BigWallCnt > 1)&&(Lu8Socre > 5)))
    {
        Lu8CreatMapLockHighFlag = 1;
    }

    Lu16DisSubLimit = 120;

    if(!Lu8CreatMapLockHighFlag)
    {
        for(i = 0; i < 5; i++)
        {
            Lu8Id = LpstrSideAreaPointBuf->u8IdBuf[i];
            Lu8Id2 = LpstrSideAreaPointBuf->u8IdBuf[i+1];
            Lu16DisSub = ABS(LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterDis,LpstrSideAreaPointBuf->Buf[Lu8Id2].u16MasterDis);
            if(Lu16DisSub < Lu16DisSubLimit)
            {
                if(LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterHeight > (LpstrSideAreaPointBuf->Buf[Lu8Id2].u16MasterHeight+10))
                {
                    Lu8MasterHeightIncreaseFlag = 1;
                }
                else
                {
                    Lu8MasterHeightIncreaseFlag = 0;
                    break;
                }
            }
            else
            {
                Lu8MasterHeightIncreaseFlag = 0;
                break;
            }
        }
        if(Lu8MasterHeightIncreaseFlag)
        {
            Lu8CreatMapLockHighFlag = 1;
        }
    }
#if 0
    if(Lu8MapId == 0x01)
    {
        VS_PRINT("MapId:%d,Time:%.3f,NewestPointDis:%d,NewestPointHeight:%d,MaxHeight:%d,Socre:%d,BigWallCnt:%d,HeightIncreaseFlag:%d\r\n",Lu8MapId,GfMessageTime,Lu16NewestPointDis,Lu16NewestPointHeight,\
                    Lu16MaxHeight,Lu8Socre,Lu8BigWallCnt,Lu8MasterHeightIncreaseFlag);
    }
#endif
    
    return Lu8CreatMapLockHighFlag;
}
#endif



/******************************************************************************
 * 函数名称: AnalysisSideAreaBufDataWhenMapNoUpdate
 * 
 * 功能描述: Map 非更新时的缓存查询，进一步提升Map置高的概率
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-05-11 16:17   V0.1      AntonyFang   初次发布
 ******************************************************************************/
uint8 AnalysisSideAreaBufDataWhenMapNoUpdate(SideAreaPointBufType  *LpstrSideAreaPointBuf,uint8 Lu8MapId,SideSnsIndexType LenuSideSnsIndex, uint16 Lu16MapMasterDis)
{
    uint8 Lu8CreatMapLockHighFlag = 0;
    uint8 i;
    uint8 Lu8Id;
    uint16 Lu16DisSub;
    uint16 Lu16DisSubLimit;
    uint8 Lu8Group,Lu8Ch;
    uint8 Lu8PrintfId = 0x2;

    if(LenuSideSnsIndex == SIDE_SNS_FLS)
    {
        Lu8Group = 0x0;
        Lu8Ch = 0x0;
    }
    else if(LenuSideSnsIndex == SIDE_SNS_FRS)
    {
        Lu8Group = 0x0;
        Lu8Ch = 0x5;
    }
    else if(LenuSideSnsIndex == SIDE_SNS_RLS)
    {
        Lu8Group = 0x1;
        Lu8Ch = 0x0;
    }
    else if(LenuSideSnsIndex == SIDE_SNS_RRS)
    {
        Lu8Group = 0x1;
        Lu8Ch = 0x5;
    }
    else
    {
        return 0;
    }

    if(GstrMapSnsRealTimeDis[Lu8Group][Lu8Ch].u8RealDisUpdateFlag)
    {
        LpstrSideAreaPointBuf->u8NoUpdateCnt++;
    }

    if(LpstrSideAreaPointBuf->u8NoUpdateCnt > 2)
    {
        GetSideAreaBufId(LpstrSideAreaPointBuf);
        if(LpstrSideAreaPointBuf->u8ValidBufCnt <= 5)
        {
            return 0;
        }
        Lu16DisSubLimit = 500;
    
        for(i = 0; i < 5; i++)
        {
            Lu8Id = LpstrSideAreaPointBuf->u8IdBuf[i];
            if(!LpstrSideAreaPointBuf->Buf[Lu8Id].u8TypeBeUsedFlag)
            {
                LpstrSideAreaPointBuf->Buf[Lu8Id].u8TypeBeUsedFlag = 1;
                Lu16DisSub = ABS(LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterDis,Lu16MapMasterDis);
                if(Lu16DisSub < Lu16DisSubLimit)
                {
                    LpstrSideAreaPointBuf->u8TotalTypeCnt++;
                    if((LpstrSideAreaPointBuf->Buf[Lu8Id].enuObjType >= OBJ_BIGWALL_TYPE)||\
                        (LpstrSideAreaPointBuf->Buf[Lu8Id].enuObjType == OBJ_PVC_PIPE_TYPE))
                    {
                        LpstrSideAreaPointBuf->u8HighTypeCnt++;
                    }
#if 0
                    if(Lu8MapId == Lu8PrintfId)
                    {
                        VS_PRINT("No Update,MapId:%d,Time:%.3f,i:%d,Dis:%d,Height:%d,Type:%s\r\n",Lu8MapId,GfMessageTime,i,\
                            LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterDis,LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterHeight,\
                            Gu8ObjTypeString[LpstrSideAreaPointBuf->Buf[Lu8Id].enuObjType]);
                    }
#endif
                }
            }
        }
        if(LpstrSideAreaPointBuf->u8HighTypeCnt > 2)
        {
            Lu8CreatMapLockHighFlag = 1;
        }
#if 0
        if(Lu8MapId == Lu8PrintfId)
        {
            VS_PRINT("No Update,MapId:%d,Time:%.3f,TotalTypeCnt:%d,HighTypeCnt:%d,LockHighFlag:%d\r\n",Lu8MapId,GfMessageTime,LpstrSideAreaPointBuf->u8TotalTypeCnt,\
                        LpstrSideAreaPointBuf->u8HighTypeCnt,Lu8CreatMapLockHighFlag);
        }
#endif
    }

    return Lu8CreatMapLockHighFlag;
}




/******************************************************************************
 * 函数名称: CreatNewMapSideAreaPointBufInit
 * 
 * 功能描述: 首次创建Map时，对侧边的Map点数据进行初始化
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-04-19 09:25   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void CreatNewMapSideAreaPointBufInit(SideAreaPointBufType  *LpstrSideAreaPointBuf)
{
    LpstrSideAreaPointBuf->u8MapStartInx = LpstrSideAreaPointBuf->u8IdBuf[0];
    LpstrSideAreaPointBuf->u8MapEndInx = LpstrSideAreaPointBuf->u8IdBuf[0];

    LpstrSideAreaPointBuf->u8AfterStartCnt = 1;
    LpstrSideAreaPointBuf->u8MapEndInxNoUpdateCnt = 0;
    LpstrSideAreaPointBuf->u8FirstUseMasterInx = 255;
    LpstrSideAreaPointBuf->u8ContinueUseMasterCnt = 0;
    LpstrSideAreaPointBuf->u8StartModifyFlag = 0;
    LpstrSideAreaPointBuf->u8TotalTypeCnt = 0;
    LpstrSideAreaPointBuf->u8HighTypeCnt = 0;
    LpstrSideAreaPointBuf->u8NoUpdateCnt = 0;
}
/******************************************************************************
 * 函数名称: SideAreaMapInit
 * 
 * 功能描述: 侧边单独区域Map初始化
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-07 17:17   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SideAreaMapInit(void)
{
    uint8 i;

    for(i = SIDE_SNS_FLS; i < SIDE_SNS_NUM; i++)
    {
        GstrSideAreaMap[i].ObjTypeCnt = GstrObjTypeCntInit;
        GstrSideAreaMap[i].P1_CarCoor = GstrObjCoorInit;
        GstrSideAreaMap[i].P2_CarCoor = GstrObjCoorInit;
        GstrSideAreaMap[i].P1_OdoCoor = GstrObjCoorInit;
        GstrSideAreaMap[i].P2_OdoCoor = GstrObjCoorInit;
        GstrSideAreaMap[i].MapSharp = AREA_MAP_SHAPE_NONE;
        GstrSideAreaMap[i].enuMapType = OBJ_NONE_TYPE;
        GstrSideAreaMap[i].eSnsMovDir = SNS_CAR_STOP;
        GstrSideAreaMap[i].u32SideUpdateTime = 0;
        GstrSideAreaMap[i].u16ValidMasterDis = OBJ_COOR_INVALID_DIS;
        GstrSideAreaMap[i].u16MasterStdHeight = 0;
        GstrSideAreaMap[i].u16MasterChirpHeight = 0;
        GstrSideAreaMap[i].u8NearExistMapFlag = 0;
        GstrSideAreaMap[i].u8ValidObjCoorFlag = 0;
        GstrSideAreaMap[i].u8CurbNeedMasterCoorFlag = 0;
        GstrSideAreaMap[i].u8FarDisNoNeedMasterCoorFlag = 0;
        GstrSideAreaMap[i].u8NearstMapId = 31;
        GstrSideAreaMap[i].fNearestMapToSnsDis = 65535;
        GstrSideAreaMap[i].u8MasterDisNoMatchCnt = 0;
        GstrSideAreaMap[i].u8NewObjCoorFlag = 0;
        GstrSideAreaMap[i].u8SnsFarawayLineFlag = 0;
    }
}

/******************************************************************************
 * 函数名称: VehiclePointCloudBufInit
 * 
 * 功能描述: 整车点云合集初始化
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-30 19:32   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void VehiclePointCloudBufInit(void)
{
    uint8 i,j;
    
    GstrVehiclePointCloudBuf.u16ExistTimeMax = 1000;
    GstrVehiclePointCloudBuf.u8BufCnt = 0;
    GstrVehiclePointCloudBuf.u8StartInx = 0;
    GstrVehiclePointCloudBuf.u8EndInx = 0;
    GstrVehiclePointCloudBuf.u8VheMapNum = 0;

    for(i = 0; i < VHE_POINT_CLOUD_BUF_NUM; i++)
    {
        GstrVehiclePointCloudBuf.Buf[i] = GstrPointCloudUnitInit;
    }

    for(i = 0; i < VHE_MAX_MAP_NUM; i++)
    {
        GstrVehiclePointCloudBuf.VheMap[i] = GstrAreaMapInit;
    } 
}

/******************************************************************************
 * 函数名称: AddOnePointCloudToVheBuf
 * 
 * 功能描述: 添加一个点云至全局Buf缓存
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-30 19:42   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void AddOnePointCloudToVheBuf(SnsPointCloudUnitType *LptrPointCloudUnit)
{
    uint8 i;
    uint8 Lu8EndInxRem;
    VehiclePointCloudBufType *LpstrVehiclePointCloudBuf;
    uint8 Lu8CurInx;
    
    LpstrVehiclePointCloudBuf = &GstrVehiclePointCloudBuf;
    Lu8CurInx = LpstrVehiclePointCloudBuf->u8EndInx;

    /* 首先判断队列是否已满 */
    Lu8EndInxRem = LpstrVehiclePointCloudBuf->u8EndInx+1;
    Lu8EndInxRem = Lu8EndInxRem%VHE_POINT_CLOUD_BUF_NUM;
    
    if(Lu8EndInxRem == LpstrVehiclePointCloudBuf->u8StartInx)
    {
        /* 队列已满,则把开始索引值同步往后移，保留最新时间的点云 */
        LpstrVehiclePointCloudBuf->Buf[Lu8CurInx] = *LptrPointCloudUnit;
        LpstrVehiclePointCloudBuf->u8EndInx++;
        if(LpstrVehiclePointCloudBuf->u8EndInx == VHE_POINT_CLOUD_BUF_NUM)
        {
            LpstrVehiclePointCloudBuf->u8EndInx = 0;
        }
        LpstrVehiclePointCloudBuf->u8StartInx++;
        if(LpstrVehiclePointCloudBuf->u8StartInx == VHE_POINT_CLOUD_BUF_NUM)
        {
            LpstrVehiclePointCloudBuf->u8StartInx = 0;
        }
    }
    else
    {
        /* 队列未满 */
        LpstrVehiclePointCloudBuf->Buf[Lu8CurInx] = *LptrPointCloudUnit;
        LpstrVehiclePointCloudBuf->u8EndInx++;
        if(LpstrVehiclePointCloudBuf->u8EndInx == VHE_POINT_CLOUD_BUF_NUM)
        {
            LpstrVehiclePointCloudBuf->u8EndInx = 0;
        }
        LpstrVehiclePointCloudBuf->u8BufCnt++;
    }
    
#if 0
    if((GfMessageTime > 70.6)&&(GfMessageTime < 70.7))
    {
        VS_PRINT("Time:%.3f,After Add, Vhe Buf Cnt:%d,StartInx:%d,EndInx:%d,ExistTimeMax:%d\r\n",GfMessageTime,LpstrVehiclePointCloudBuf->u8BufCnt,LpstrVehiclePointCloudBuf->u8StartInx,\
        LpstrVehiclePointCloudBuf->u8EndInx,LpstrVehiclePointCloudBuf->u16ExistTimeMax);
        for(i = LpstrVehiclePointCloudBuf->u8StartInx; i < LpstrVehiclePointCloudBuf->u8EndInx; i++)
        {
            VS_PRINT("Buf Data,i:%d,SnsCh:%ld,MasterDis:%d,MasterHeight:%d,DetTime:%ld,ExistTime:%d\r\n",i,LpstrVehiclePointCloudBuf->Buf[i].enuSnsCh,\
                LpstrVehiclePointCloudBuf->Buf[i].u16MasterDis,LpstrVehiclePointCloudBuf->Buf[i].u16MasterHeight,LpstrVehiclePointCloudBuf->Buf[i].u32DetSysTime,\
                LpstrVehiclePointCloudBuf->Buf[i].u16ExistTime);
        }
        VS_PRINT("\r\n");
    }
#endif
}


/******************************************************************************
 * 函数名称: DeleteOnePointCloudFromVheBuf
 * 
 * 功能描述: 从整车区域缓存中删除一个点
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-30 19:43   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void DeleteOnePointCloudFromVheBuf(void)
{
    uint8 i;
    VehiclePointCloudBufType *LpstrVehiclePointCloudBuf;
    uint8 Lu8DeleteInx;
    
    LpstrVehiclePointCloudBuf = &GstrVehiclePointCloudBuf;
    Lu8DeleteInx = LpstrVehiclePointCloudBuf->u8StartInx;

    /* 判断队列是否为空，为空则无需删除 */
    if(LpstrVehiclePointCloudBuf->u8StartInx != LpstrVehiclePointCloudBuf->u8EndInx)
    {
        LpstrVehiclePointCloudBuf->Buf[Lu8DeleteInx] = GstrPointCloudUnitInit;
        LpstrVehiclePointCloudBuf->u8BufCnt--;
        LpstrVehiclePointCloudBuf->u8StartInx++;
        if(LpstrVehiclePointCloudBuf->u8StartInx == VHE_POINT_CLOUD_BUF_NUM)
        {
            LpstrVehiclePointCloudBuf->u8StartInx = 0;
        }
    }
}



/******************************************************************************
 * 函数名称: UpdateOldPointAndDeleteOutTimePointForVheBuf
 * 
 * 功能描述: 转换历史点的车辆坐标(可能不需要，先做)，并删除超过定时周期的点
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-30 19:47   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void UpdateOldPointAndDeleteOutTimePointForVheBuf(uint32 Lu32SysTime)
{
    uint8 Lu8StartInx,Lu8EndInx,Lu8EndInxRem;
    uint8 i;
    uint8 Lu8FindEndInx;
    PointCloudAreaType LenuPointCloudNewArea;
    ObjCoorType LstrObjNewCarCoor;
    
    VehiclePointCloudBufType *LpstrVehiclePointCloudBuf;
    LpstrVehiclePointCloudBuf = &GstrVehiclePointCloudBuf;
    
    Lu8StartInx = LpstrVehiclePointCloudBuf->u8StartInx;
    Lu8EndInx = LpstrVehiclePointCloudBuf->u8EndInx;
    Lu8EndInxRem = Lu8EndInx+1;
    Lu8EndInxRem = Lu8EndInxRem%VHE_POINT_CLOUD_BUF_NUM;

    if(Lu8StartInx != Lu8EndInx)
    {
        if(Lu8StartInx < Lu8EndInx)
        {
            Lu8FindEndInx = Lu8EndInx;
            for(i = Lu8StartInx; i < Lu8FindEndInx; i++)
            {
                LpstrVehiclePointCloudBuf->Buf[i].u16ExistTime = Lu32SysTime - LpstrVehiclePointCloudBuf->Buf[i].u32DetSysTime;
                if(LpstrVehiclePointCloudBuf->Buf[i].u16ExistTime > LpstrVehiclePointCloudBuf->u16ExistTimeMax)
                {
                    DeleteOnePointCloudFromVheBuf();
                }
                else
                {
                    /* 根据车辆运行状态，决定是否需要更新缓存的车辆坐标点，以保证运动时，障碍物的车辆坐标是最新的，且分配在对应的区域 */
                    if(GstrSnsCarMovSts[0][0].eCarDir != SNS_CAR_STOP)
                    {
                        PubAI_TransObjOdoCoorToCarCoor(&LpstrVehiclePointCloudBuf->Buf[i].strObjOdoCoor.fObjX,&LpstrVehiclePointCloudBuf->Buf[i].strObjOdoCoor.fObjY,\
                            &LstrObjNewCarCoor.fObjX,&LstrObjNewCarCoor.fObjY);
                        LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor = LstrObjNewCarCoor;
                    }
                }
            }
        }
        else
        {
            Lu8FindEndInx = VHE_POINT_CLOUD_BUF_NUM;
            for(i = Lu8StartInx; i < Lu8FindEndInx; i++)
            {
                LpstrVehiclePointCloudBuf->Buf[i].u16ExistTime = Lu32SysTime - LpstrVehiclePointCloudBuf->Buf[i].u32DetSysTime;
                if(LpstrVehiclePointCloudBuf->Buf[i].u16ExistTime > LpstrVehiclePointCloudBuf->u16ExistTimeMax)
                {
                    DeleteOnePointCloudFromVheBuf();
                }
                else
                {
                    /* 根据车辆运行状态，决定是否需要更新缓存的车辆坐标点，以保证运动时，障碍物的车辆坐标是最新的，且分配在对应的区域 */
                    if(GstrSnsCarMovSts[0][0].eCarDir != SNS_CAR_STOP)
                    {
                        PubAI_TransObjOdoCoorToCarCoor(&LpstrVehiclePointCloudBuf->Buf[i].strObjOdoCoor.fObjX,&LpstrVehiclePointCloudBuf->Buf[i].strObjOdoCoor.fObjY,\
                            &LstrObjNewCarCoor.fObjX,&LstrObjNewCarCoor.fObjY);
                        LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor = LstrObjNewCarCoor;
                    }
                }
            }
            if(Lu8EndInx > 0)
            {
                Lu8FindEndInx = Lu8EndInx;
                for(i = 0; i < Lu8FindEndInx; i++)
                {
                    LpstrVehiclePointCloudBuf->Buf[i].u16ExistTime = Lu32SysTime - LpstrVehiclePointCloudBuf->Buf[i].u32DetSysTime;
                    if(LpstrVehiclePointCloudBuf->Buf[i].u16ExistTime > LpstrVehiclePointCloudBuf->u16ExistTimeMax)
                    {
                        DeleteOnePointCloudFromVheBuf();
                    }
                    else
                    {
                        /* 根据车辆运行状态，决定是否需要更新缓存的车辆坐标点，以保证运动时，障碍物的车辆坐标是最新的，且分配在对应的区域 */
                        if(GstrSnsCarMovSts[0][0].eCarDir != SNS_CAR_STOP)
                        {
                            PubAI_TransObjOdoCoorToCarCoor(&LpstrVehiclePointCloudBuf->Buf[i].strObjOdoCoor.fObjX,&LpstrVehiclePointCloudBuf->Buf[i].strObjOdoCoor.fObjY,\
                                &LstrObjNewCarCoor.fObjX,&LstrObjNewCarCoor.fObjY);
                            LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor = LstrObjNewCarCoor;
                        }
                    }
                }
            }
        }
    }
    else
    {
        /* 队列为空，不做处理 */
    }
}


/******************************************************************************
 * 函数名称: PointCloudVheBufUpdate
 * 
 * 功能描述: 更新所有缓存数据
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-30 19:57   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PointCloudVheBufUpdate(PDCSnsGroupType LenuGroup,PDCSnsChannelType LenuCh)
{
    /* 更新点云Buf中的数据 */
    SnsObjCoorPointType *LpstrSnsObjCoorPoint;
    SnsPointCloudUnitType LstrPointCloudUnit;
    uint8 i;

    LpstrSnsObjCoorPoint = &GstrObjCoorPoint[LenuGroup][LenuCh];

    /* 左侦听障碍物进点云缓存*/
    if(!LpstrSnsObjCoorPoint->u8LeftAlredayToBufFlag)
    {
        for(i = 0; i < LpstrSnsObjCoorPoint->u8LeftObjCoorCnt; i++)
        {
            if(LpstrSnsObjCoorPoint->LeftData[i].u8CoorValidFlag
                && LpstrSnsObjCoorPoint->LeftData[i].enuObjArea > MAP_AREA1
                && LpstrSnsObjCoorPoint->LeftData[i].enuObjArea < MAP_AREA7)
            {
                LstrPointCloudUnit.strObjCarCoor = LpstrSnsObjCoorPoint->LeftData[i].strObjCarCoor;
                LstrPointCloudUnit.strObjOdoCoor = LpstrSnsObjCoorPoint->LeftData[i].strObjOdoCoor;
                LstrPointCloudUnit.enuObjType = LpstrSnsObjCoorPoint->LeftData[i].enuObjType;
                LstrPointCloudUnit.enuDetType = POINT_CLOUD_DET_BY_LEFT;
                LstrPointCloudUnit.enuSnsCh = LenuCh;
                LstrPointCloudUnit.eMeasType = LpstrSnsObjCoorPoint->eMeasType;
                LstrPointCloudUnit.u32DetSysTime = GdSystemMsTimer;
                LstrPointCloudUnit.u16ExistTime = 0;
                LstrPointCloudUnit.u16MasterDis = LpstrSnsObjCoorPoint->LeftData[i].u16MasterDis;
                LstrPointCloudUnit.u16MasterHeight = LpstrSnsObjCoorPoint->LeftData[i].u16MasterHeight;
                LstrPointCloudUnit.u8ValidFlag = 1;
                LstrPointCloudUnit.u8TypeAlredayBeUseFlag = 0;
                LstrPointCloudUnit.u8CoorAlredayBeUseFlag = 0;
                LstrPointCloudUnit.u8SnsNoiseCnt = LpstrSnsObjCoorPoint->LeftData[i].u8SnsNoiseCnt;
                AddOnePointCloudToVheBuf(&LstrPointCloudUnit);
            }
        }
    }

    /* 右侦听障碍物进点云缓存*/
    if(!LpstrSnsObjCoorPoint->u8RightAlredayToBufFlag)
    {
        for(i = 0; i < LpstrSnsObjCoorPoint->u8RightObjCoorCnt; i++)
        {
            if(LpstrSnsObjCoorPoint->RightData[i].u8CoorValidFlag
                && LpstrSnsObjCoorPoint->RightData[i].enuObjArea > MAP_AREA1
                && LpstrSnsObjCoorPoint->RightData[i].enuObjArea < MAP_AREA7)
            {
                LstrPointCloudUnit.strObjCarCoor = LpstrSnsObjCoorPoint->RightData[i].strObjCarCoor;
                LstrPointCloudUnit.strObjOdoCoor = LpstrSnsObjCoorPoint->RightData[i].strObjOdoCoor;
                LstrPointCloudUnit.enuObjType = LpstrSnsObjCoorPoint->RightData[i].enuObjType;
                LstrPointCloudUnit.enuDetType = POINT_CLOUD_DET_BY_RIGHT;
                LstrPointCloudUnit.enuSnsCh = LenuCh;
                LstrPointCloudUnit.eMeasType = LpstrSnsObjCoorPoint->eMeasType;
                LstrPointCloudUnit.u32DetSysTime = GdSystemMsTimer;
                LstrPointCloudUnit.u16ExistTime = 0;
                LstrPointCloudUnit.u16MasterDis = LpstrSnsObjCoorPoint->RightData[i].u16MasterDis;
                LstrPointCloudUnit.u16MasterHeight = LpstrSnsObjCoorPoint->RightData[i].u16MasterHeight;
                LstrPointCloudUnit.u8ValidFlag = 1;
                LstrPointCloudUnit.u8TypeAlredayBeUseFlag = 0;
                LstrPointCloudUnit.u8CoorAlredayBeUseFlag = 0;
                LstrPointCloudUnit.u8SnsNoiseCnt = LpstrSnsObjCoorPoint->RightData[i].u8SnsNoiseCnt;
                AddOnePointCloudToVheBuf(&LstrPointCloudUnit);
            }
        }
    }

    /* 主发障碍物进点云缓存 */
    if(!LpstrSnsObjCoorPoint->u8MasterAlredayToBufFlag)
    {
        for(i = 0; i < LpstrSnsObjCoorPoint->u8MasterObjCoorCnt; i++)
        {
            if(LpstrSnsObjCoorPoint->MasterData[i].u8CoorValidFlag\
                && LpstrSnsObjCoorPoint->MasterData[i].enuObjArea > MAP_AREA1\
                && LpstrSnsObjCoorPoint->MasterData[i].enuObjArea < MAP_AREA7)
            {
                LstrPointCloudUnit.strObjCarCoor = LpstrSnsObjCoorPoint->MasterData[i].strObjCarCoor;
                LstrPointCloudUnit.strObjOdoCoor = LpstrSnsObjCoorPoint->MasterData[i].strObjOdoCoor;
                LstrPointCloudUnit.enuObjType = LpstrSnsObjCoorPoint->MasterData[i].enuObjType;
                LstrPointCloudUnit.enuDetType = POINT_CLOUD_DET_BY_MASTER;
                LstrPointCloudUnit.enuSnsCh = LenuCh;
                LstrPointCloudUnit.eMeasType = LpstrSnsObjCoorPoint->eMeasType;
                LstrPointCloudUnit.u32DetSysTime = GdSystemMsTimer;
                LstrPointCloudUnit.u16ExistTime = 0;
                LstrPointCloudUnit.u16MasterDis = LpstrSnsObjCoorPoint->MasterData[i].u16MasterDis;
                LstrPointCloudUnit.u16MasterHeight = LpstrSnsObjCoorPoint->MasterData[i].u16MasterHeight;
                LstrPointCloudUnit.u8ValidFlag = 1;
                LstrPointCloudUnit.u8TypeAlredayBeUseFlag = 0;
                LstrPointCloudUnit.u8CoorAlredayBeUseFlag = 0;
                LstrPointCloudUnit.u8SnsNoiseCnt = LpstrSnsObjCoorPoint->MasterData[i].u8SnsNoiseCnt;
                AddOnePointCloudToVheBuf(&LstrPointCloudUnit);
            }
        }
    }
}

/******************************************************************************
 * 函数名称: JudgeTwoPointNear
 * 
 * 功能描述: 判断两个车辆坐标点是否临近
 * 
 * 输入参数:LfP1_X_Coor--P1点X坐标， LfP1_Y_Coor--P1点Y坐标，LfP2_X_Coor--P2点X坐标， LfP2_Y_Coor--P2点Y坐标
 * 
 * 输出参数:无 
 * 
 * 返回值:Lu8TwoPointNearFlag--两点相邻标志位
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-03-17 09:58   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static eTwoPointRelationType JudgeTwoCarPointNear(ObjCoorType LstrP1_Coor,ObjCoorType LstrP2_Coor)
{
    eTwoPointRelationType LenuTwoPointRelation = TWO_POINT_NONE;
    float LfX_Sub,LfY_Sub;

    LfX_Sub = ABS(LstrP1_Coor.fObjX,LstrP2_Coor.fObjX);
    LfY_Sub = ABS(LstrP1_Coor.fObjY,LstrP2_Coor.fObjY);

    if(LfX_Sub < NEAR_CAR_X_SUB)
    {
        if(LfY_Sub < NEAR_CAR_Y_SUB)
        {
            LenuTwoPointRelation = TWO_POINT_SAME;       /* 两个车辆坐标点临近 */
        }
        else
        {
            LenuTwoPointRelation = TWO_POINT_ONE_LINE;    /* 两个车辆坐标点在同一条直线上 */
        }
    }
    else
    {
        LenuTwoPointRelation = TWO_POINT_DIFF;           /* 两个车辆坐标点不同 */
    }
    
    return LenuTwoPointRelation;
}


/******************************************************************************
 * 函数名称: JudgePointToAreaMapNear
 * 
 * 功能描述: 判断新产生的点和已经存在的区域Map点之间的关系
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-22 11:24   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static uint8 JudgePointToAreaMapPointNear(ObjCoorType *LpstrObjPoint, ObjCoorType *LpstrMapPoint)
{
    uint8 Lu8TwoPointNearFlag = 0;
    float LfXSub,LfYSub;

    LfXSub = ABS(LpstrObjPoint->fObjX,LpstrMapPoint->fObjX);
    LfYSub = ABS(LpstrObjPoint->fObjY,LpstrMapPoint->fObjY);

    if(LfXSub < AREA_MAP_POINT_NEAR_X_SUB)
    {
        if(LfYSub < AREA_MAP_POINT_NEAR_Y_SUB)
        {
            Lu8TwoPointNearFlag = 1;
        }
        else
        {
            Lu8TwoPointNearFlag = 0;
        }
    }
    else
    {
        Lu8TwoPointNearFlag = 0;
    }
    return Lu8TwoPointNearFlag;
}



/******************************************************************************
 * 函数名称: PointCloudBufClear
 * 
 * 功能描述: 点云缓存数据清零
 * 
 * 输入参数:LenuSnsGroup--探头分组号； LenuPointCloudArea--点云区域位置
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-18 16:50   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void PointCloudBufClear(PDCSnsGroupType LenuSnsGroup,PointCloudAreaType LenuPointCloudArea)
{
    uint8 i;
    SnsPointCloudBufType *LpstrSnsPointCloudBuf;

    LpstrSnsPointCloudBuf = &GstrPointCloudBuf[LenuSnsGroup][LenuPointCloudArea];
    for(i = 0; i < POINT_CLOUD_BUF_NUM; i++)
    {
        LpstrSnsPointCloudBuf->Buf[i] = GstrPointCloudUnitInit;
    }
    
    for(i = 0; i < MAX_AREA_MAP_NUM; i++)
    {
        LpstrSnsPointCloudBuf->AreaMap[i] = GstrAreaMapInit;
    }
    if((LenuPointCloudArea == POINT_CLOUD_AREA0)||(LenuPointCloudArea == POINT_CLOUD_AREA4))
    {
        /** @brief: 针对侧边，在不同车速下使用不同的阈值 */
        if(LenuSnsGroup == 0x00)
        {
            if(Gu16CarSpdForSnsUse < 500)
            {
                LpstrSnsPointCloudBuf->u16ExistTimeMax = AREA_0_4_HOLD_TIME_LOW_SPD;
            }
            else
            {
                LpstrSnsPointCloudBuf->u16ExistTimeMax = AREA_0_4_HOLD_TIME;
            }
        }
        else
        {
            LpstrSnsPointCloudBuf->u16ExistTimeMax = AREA_0_4_HOLD_TIME;
        }
    }
    else if((LenuPointCloudArea == POINT_CLOUD_AREA1)||(LenuPointCloudArea == POINT_CLOUD_AREA3))
    {
        LpstrSnsPointCloudBuf->u16ExistTimeMax = AREA_1_3_HOLD_TIME;
    }
    else
    {
        LpstrSnsPointCloudBuf->u16ExistTimeMax = AREA_2_HOLD_TIME;
    }
    LpstrSnsPointCloudBuf->u8BufCnt = 0;
    LpstrSnsPointCloudBuf->u8BufValidPointCnt = 0;
    LpstrSnsPointCloudBuf->u8StartInx = 0;
    LpstrSnsPointCloudBuf->u8EndInx = 0;
    LpstrSnsPointCloudBuf->u8AreaMapNum = 0;
}


/******************************************************************************
 * 函数名称: PointCloudBufInit
 * 
 * 功能描述: 初始化点云缓存数据
 * 
 * 输入参数:LenuSnsGroup--探头分组号
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-18 16:52   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PointCloudBufInit(PDCSnsGroupType LenuSnsGroup)
{
    PointCloudAreaType LenuPointCloudArea;

    for(LenuPointCloudArea = POINT_CLOUD_AREA0; LenuPointCloudArea < POINT_CLOUD_AREA_NUM; LenuPointCloudArea++)
    {
        PointCloudBufClear(LenuSnsGroup,LenuPointCloudArea);
    }
}


/******************************************************************************
 * 函数名称: AreaClusterDataInit
 * 
 * 功能描述: 区域聚类数据初始化
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-21 15:12   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void AreaClusterBufInit(void)
{
    uint8 i;

    GstrAreaClusterBuf.TotalPointCnt = 0;
    GstrAreaClusterBuf.TotalClusterCnt = 0;

    for(i = 0; i < AREA_CLUSTER_NUM; i++)
    {
        GstrAreaClusterBuf.Cluster[i] = GstrClusterInit;
    }
    
    for(i = 0; i < AREA_CLUSTER_POINT_NUM; i++)
    {
        GstrAreaClusterBuf.u8PointUnitId[i] = 0;
    }
}


/******************************************************************************
 * 函数名称: AreaClusterDataClear
 * 
 * 功能描述: 清除区域聚类缓存的数据，为提升效率，仅清零相关的计数即可
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-21 15:16   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void AreaClusterBufClear(void)
{
    GstrAreaClusterBuf.TotalPointCnt = 0;
    GstrAreaClusterBuf.TotalClusterCnt = 0;
}

/******************************************************************************
 * 函数名称: AddOnePointCloudToBuf
 * 
 * 功能描述: 添加一个点云数据至区域缓存
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-18 17:08   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void AddOnePointCloudToBuf(PDCSnsGroupType LenuSnsGroup,PointCloudAreaType LenuPointCloudArea,SnsPointCloudUnitType *LptrPointCloudUnit)
{
    uint8 i;
    uint8 Lu8EndInxRem;
    SnsPointCloudBufType *LpstrSnsPointCloudBuf;
    uint8 Lu8CurInx;
    
    LpstrSnsPointCloudBuf = &GstrPointCloudBuf[LenuSnsGroup][LenuPointCloudArea];
    Lu8CurInx = LpstrSnsPointCloudBuf->u8EndInx;

    /* 首先判断队列是否已满 */
    Lu8EndInxRem = LpstrSnsPointCloudBuf->u8EndInx+1;
    Lu8EndInxRem = Lu8EndInxRem%POINT_CLOUD_BUF_NUM;

#if 0
    VS_PRINT("Before Add,Start_Inx:%d,End_Inx:%d,Area:%d,BufCnt:%d,ValidPointCnt:%d,StartFlag:%d,Time:%ld\r\n",LpstrSnsPointCloudBuf->u8StartInx,\
        LpstrSnsPointCloudBuf->u8EndInx,LenuPointCloudArea,LpstrSnsPointCloudBuf->u8BufCnt,\
        LpstrSnsPointCloudBuf->u8BufValidPointCnt,LpstrSnsPointCloudBuf->Buf[LpstrSnsPointCloudBuf->u8StartInx].u8ValidFlag,\
        LptrPointCloudUnit->u32DetSysTime);
#endif
    
    if(Lu8EndInxRem == LpstrSnsPointCloudBuf->u8StartInx)
    {
        /* 队列已满,则把开始索引值同步往后移，保留最新时间的点云 */
        LpstrSnsPointCloudBuf->Buf[Lu8CurInx] = *LptrPointCloudUnit;
        LpstrSnsPointCloudBuf->u8EndInx++;
        if(LpstrSnsPointCloudBuf->u8EndInx == POINT_CLOUD_BUF_NUM)
        {
            LpstrSnsPointCloudBuf->u8EndInx = 0;
        }
        /* 需要判断即将被覆盖的点云是否有效，有效则有效计数相当于删除一个，再增加一个,无需递增；否则则加一个 */
        if(!LpstrSnsPointCloudBuf->Buf[LpstrSnsPointCloudBuf->u8StartInx].u8ValidFlag)
        {
            LpstrSnsPointCloudBuf->u8BufValidPointCnt++;
        }
        LpstrSnsPointCloudBuf->u8StartInx++;
        if(LpstrSnsPointCloudBuf->u8StartInx == POINT_CLOUD_BUF_NUM)
        {
            LpstrSnsPointCloudBuf->u8StartInx = 0;
        }
    }
    else
    {
        /* 队列未满 */
        LpstrSnsPointCloudBuf->Buf[Lu8CurInx] = *LptrPointCloudUnit;
        LpstrSnsPointCloudBuf->u8EndInx++;
        if(LpstrSnsPointCloudBuf->u8EndInx == POINT_CLOUD_BUF_NUM)
        {
            LpstrSnsPointCloudBuf->u8EndInx = 0;
        }
        LpstrSnsPointCloudBuf->u8BufCnt++;
        LpstrSnsPointCloudBuf->u8BufValidPointCnt++;
    }
#if 0
    VS_PRINT("End Add,Start_Inx:%d,End_Inx:%d,Area:%d,BufCnt:%d,ValidPointCnt:%d,Time:%ld\r\n\r\n",LpstrSnsPointCloudBuf->u8StartInx,\
        LpstrSnsPointCloudBuf->u8EndInx,LenuPointCloudArea,LpstrSnsPointCloudBuf->u8BufCnt,\
        LpstrSnsPointCloudBuf->u8BufValidPointCnt,LptrPointCloudUnit->u32DetSysTime);
#endif
}



/******************************************************************************
 * 函数名称: DeleteOnePointCloudFromBuf
 * 
 * 功能描述: 从已经存在的区域缓存中删除点云
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-18 17:09   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void DeleteOnePointCloudFromBuf(PDCSnsGroupType LenuSnsGroup,PointCloudAreaType LenuPointCloudArea)
{
    uint8 i;
    SnsPointCloudBufType *LpstrSnsPointCloudBuf;
    uint8 Lu8DeleteInx;
    
    LpstrSnsPointCloudBuf = &GstrPointCloudBuf[LenuSnsGroup][LenuPointCloudArea];
    Lu8DeleteInx = LpstrSnsPointCloudBuf->u8StartInx;

#if 0
    VS_PRINT("Before Dele,Start_Inx:%d,End_Inx:%d,Area:%d,BufCnt:%d,ValidPointCnt:%d,StartFlag:%d,Time:%ld,ExistTime:%d\r\n",LpstrSnsPointCloudBuf->u8StartInx,\
        LpstrSnsPointCloudBuf->u8EndInx,LenuPointCloudArea,LpstrSnsPointCloudBuf->u8BufCnt,\
        LpstrSnsPointCloudBuf->u8BufValidPointCnt,LpstrSnsPointCloudBuf->Buf[LpstrSnsPointCloudBuf->u8StartInx].u8ValidFlag,\
        GdSystemMsTimer,LpstrSnsPointCloudBuf->Buf[LpstrSnsPointCloudBuf->u8StartInx].u16ExistTime);
#endif

    /* 判断队列是否为空，为空则无需删除 */
    if(LpstrSnsPointCloudBuf->u8StartInx != LpstrSnsPointCloudBuf->u8EndInx)
    {
        if(LpstrSnsPointCloudBuf->Buf[Lu8DeleteInx].u8ValidFlag)
        {
            LpstrSnsPointCloudBuf->u8BufValidPointCnt--;
        }
        LpstrSnsPointCloudBuf->Buf[Lu8DeleteInx] = GstrPointCloudUnitInit;
        LpstrSnsPointCloudBuf->u8BufCnt--;
        LpstrSnsPointCloudBuf->u8StartInx++;
        if(LpstrSnsPointCloudBuf->u8StartInx == POINT_CLOUD_BUF_NUM)
        {
            LpstrSnsPointCloudBuf->u8StartInx = 0;
        }
    }
#if 0
    VS_PRINT("After Dele,Start_Inx:%d,End_Inx:%d,Area:%d,BufCnt:%d,ValidPointCnt:%d,StartFlag:%d,Time:%ld,ExistTime:%d\r\n",LpstrSnsPointCloudBuf->u8StartInx,\
        LpstrSnsPointCloudBuf->u8EndInx,LenuPointCloudArea,LpstrSnsPointCloudBuf->u8BufCnt,\
        LpstrSnsPointCloudBuf->u8BufValidPointCnt,LpstrSnsPointCloudBuf->Buf[LpstrSnsPointCloudBuf->u8StartInx].u8ValidFlag,\
        GdSystemMsTimer,LpstrSnsPointCloudBuf->Buf[LpstrSnsPointCloudBuf->u8StartInx].u16ExistTime);
#endif
}


/******************************************************************************
 * 函数名称: JudgeObjInCloudBufArea
 * 
 * 功能描述: 粗略判断障碍物点所在的点云缓存分区
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-20 09:54   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static PointCloudAreaType JudgeObjInCloudBufArea(PDCSnsGroupType LenuGroup,float LfObjCarCoor_Y)
{
    PointCloudAreaType LenuPointCloudArea = POINT_CLOUD_NONE;

    if(LfObjCarCoor_Y > (GfPointCloudArea_X_Coor_Ram[LenuGroup][0]+GstrParkingGuidenceData.fSummonSideDis))
    {
        LenuPointCloudArea = POINT_CLOUD_AREA0;
    }
    else if(LfObjCarCoor_Y > GfPointCloudArea_X_Coor_Ram[LenuGroup][1])
    {
        LenuPointCloudArea = POINT_CLOUD_AREA1;
    }
    else if(LfObjCarCoor_Y > GfPointCloudArea_X_Coor_Ram[LenuGroup][2])
    {
        LenuPointCloudArea = POINT_CLOUD_AREA2;
    }
    else if(LfObjCarCoor_Y > (GfPointCloudArea_X_Coor_Ram[LenuGroup][3]-GstrParkingGuidenceData.fSummonSideDis))
    {
        LenuPointCloudArea = POINT_CLOUD_AREA3;
    }
    else
    {
        LenuPointCloudArea = POINT_CLOUD_AREA4;
    }
    return LenuPointCloudArea;
}


/******************************************************************************
 * 函数名称: UpdateOldPointAndDeleteOutTimePoint
 * 
 * 功能描述: 更新缓存中的旧点，并删除
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-20 11:22   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void UpdateOldPointAndDeleteOutTimePoint(PDCSnsGroupType LenuGroup,PointCloudAreaType LenuPointCloudArea,uint32 Lu32SysTime)
{
    SnsPointCloudBufType *LpstrPointCloudBuf;
    uint8 Lu8StartInx,Lu8EndInx,Lu8EndInxRem;
    uint8 i;
    uint8 Lu8FindEndInx;
    PointCloudAreaType LenuPointCloudNewArea;
    ObjCoorType LstrObjNewCarCoor;
    
    LpstrPointCloudBuf = &GstrPointCloudBuf[LenuGroup][LenuPointCloudArea];
    Lu8StartInx = LpstrPointCloudBuf->u8StartInx;
    Lu8EndInx = LpstrPointCloudBuf->u8EndInx;
    Lu8EndInxRem = Lu8EndInx+1;
    Lu8EndInxRem = Lu8EndInxRem%POINT_CLOUD_BUF_NUM;

    if(Lu8StartInx != Lu8EndInx)
    {
        if(Lu8StartInx < Lu8EndInx)
        {
            Lu8FindEndInx = Lu8EndInx;
            for(i = Lu8StartInx; i < Lu8FindEndInx; i++)
            {
                LpstrPointCloudBuf->Buf[i].u16ExistTime = Lu32SysTime - LpstrPointCloudBuf->Buf[i].u32DetSysTime;
                if(LpstrPointCloudBuf->Buf[i].u16ExistTime > LpstrPointCloudBuf->u16ExistTimeMax)
                {
                    if(i == Lu8StartInx)
                    {
                        DeleteOnePointCloudFromBuf(LenuGroup,LenuPointCloudArea);
                    }
                    else
                    {
                        if(LpstrPointCloudBuf->Buf[i].u8ValidFlag)
                        {
                            LpstrPointCloudBuf->Buf[i].u8ValidFlag = 0;
                            LpstrPointCloudBuf->u8BufValidPointCnt--;
                        }
                    }
                }
                else
                {
                    /* 根据车辆运行状态，决定是否需要更新缓存的车辆坐标点，以保证运动时，障碍物的车辆坐标是最新的，且分配在对应的区域 */
                    if(GstrSnsCarMovSts[LenuGroup][0].eCarDir != SNS_CAR_STOP)
                    {
                        if(LpstrPointCloudBuf->Buf[i].u8ValidFlag)
                        {
                            PubAI_TransObjOdoCoorToCarCoor(&LpstrPointCloudBuf->Buf[i].strObjOdoCoor.fObjX,&LpstrPointCloudBuf->Buf[i].strObjOdoCoor.fObjY,\
                                &LstrObjNewCarCoor.fObjX,&LstrObjNewCarCoor.fObjY);
                            LpstrPointCloudBuf->Buf[i].strObjCarCoor = LstrObjNewCarCoor;
                            /* 判断分区是否发生变化，变化则需要更新至新的分区,同时删除本分区的点 */
                            LenuPointCloudNewArea = JudgeObjInCloudBufArea(LenuGroup,LstrObjNewCarCoor.fObjY);
                            if(LenuPointCloudNewArea != LenuPointCloudArea)
                            {
                                /* 强制把存在时间改为0，以保证新点进入到新的区域后能够立即进行Map构建
                                   存在时间置0，以保证进入新的区域能够立即更新，但实际探测时间需要保持点云的原始属性不变--2024-04-15 */
                                LpstrPointCloudBuf->Buf[i].u16ExistTime = 0;
                                //LpstrPointCloudBuf->Buf[i].u32DetSysTime = GdSystemMsTimer;
                                AddOnePointCloudToBuf(LenuGroup,LenuPointCloudNewArea,&LpstrPointCloudBuf->Buf[i]);
                                //DeleteOnePointCloudFromBuf(LenuGroup,LenuPointCloudArea);
                                /* 不直接删除，保留在缓存队列中，等待超出历史周期进行消亡处理，只把有效性置为无效 */
                                LpstrPointCloudBuf->Buf[i].u8ValidFlag = 0;
                                LpstrPointCloudBuf->u8BufValidPointCnt--;
                            }
                        }
                    }
                }
            }
        }
        else
        {
            Lu8FindEndInx = POINT_CLOUD_BUF_NUM;
            for(i = Lu8StartInx; i < Lu8FindEndInx; i++)
            {
                LpstrPointCloudBuf->Buf[i].u16ExistTime = Lu32SysTime - LpstrPointCloudBuf->Buf[i].u32DetSysTime;
                if(LpstrPointCloudBuf->Buf[i].u16ExistTime > LpstrPointCloudBuf->u16ExistTimeMax)
                {
                    if(i == Lu8StartInx)
                    {
                        DeleteOnePointCloudFromBuf(LenuGroup,LenuPointCloudArea);
                    }
                    else
                    {
                        if(LpstrPointCloudBuf->Buf[i].u8ValidFlag)
                        {
                            LpstrPointCloudBuf->Buf[i].u8ValidFlag = 0;
                            LpstrPointCloudBuf->u8BufValidPointCnt--;
                        }
                    }
                }
                else
                {
                    /* 根据车辆运行状态，决定是否需要更新缓存的车辆坐标点，以保证运动时，障碍物的车辆坐标是最新的，且分配在对应的区域 */
                    if(GstrSnsCarMovSts[LenuGroup][0].eCarDir != SNS_CAR_STOP)
                    {
                        if(LpstrPointCloudBuf->Buf[i].u8ValidFlag)
                        {
                            PubAI_TransObjOdoCoorToCarCoor(&LpstrPointCloudBuf->Buf[i].strObjOdoCoor.fObjX,&LpstrPointCloudBuf->Buf[i].strObjOdoCoor.fObjY,\
                                &LstrObjNewCarCoor.fObjX,&LstrObjNewCarCoor.fObjY);
                            LpstrPointCloudBuf->Buf[i].strObjCarCoor = LstrObjNewCarCoor;
                            /* 判断分区是否发生变化，变化则需要更新至新的分区,同时删除本分区的点 */
                            LenuPointCloudNewArea = JudgeObjInCloudBufArea(LenuGroup,LstrObjNewCarCoor.fObjY);
                            if(LenuPointCloudNewArea != LenuPointCloudArea)
                            {
                                /* 存在时间置0，以保证进入新的区域能够立即更新，但实际探测时间需要保持点云的原始属性不变--2024-04-15 */
                                LpstrPointCloudBuf->Buf[i].u16ExistTime = 0;
                                //LpstrPointCloudBuf->Buf[i].u32DetSysTime = GdSystemMsTimer;
                                AddOnePointCloudToBuf(LenuGroup,LenuPointCloudNewArea,&LpstrPointCloudBuf->Buf[i]);
                                //DeleteOnePointCloudFromBuf(LenuGroup,LenuPointCloudArea);
                                /* 不直接删除，保留在缓存队列中，等待超出历史周期进行消亡处理，只把有效性置为无效 */
                                LpstrPointCloudBuf->Buf[i].u8ValidFlag = 0;
                                LpstrPointCloudBuf->u8BufValidPointCnt--;
                            }
                        }
                    }
                }
            }
            if(Lu8EndInx > 0)
            {
                Lu8FindEndInx = Lu8EndInx;
                for(i = 0; i < Lu8FindEndInx; i++)
                {
                    LpstrPointCloudBuf->Buf[i].u16ExistTime = Lu32SysTime - LpstrPointCloudBuf->Buf[i].u32DetSysTime;
                    if(LpstrPointCloudBuf->Buf[i].u16ExistTime > LpstrPointCloudBuf->u16ExistTimeMax)
                    {
                        if(i == Lu8StartInx)
                        {
                            DeleteOnePointCloudFromBuf(LenuGroup,LenuPointCloudArea);
                        }
                        else
                        {
                            if(LpstrPointCloudBuf->Buf[i].u8ValidFlag)
                            {
                                LpstrPointCloudBuf->Buf[i].u8ValidFlag = 0;
                                LpstrPointCloudBuf->u8BufValidPointCnt--;
                            }
                        }
                    }
                    else
                    {
                        /* 根据车辆运行状态，决定是否需要更新缓存的车辆坐标点，以保证运动时，障碍物的车辆坐标是最新的，且分配在对应的区域 */
                        if(GstrSnsCarMovSts[LenuGroup][0].eCarDir != SNS_CAR_STOP)
                        {
                            if(LpstrPointCloudBuf->Buf[i].u8ValidFlag)
                            {
                                PubAI_TransObjOdoCoorToCarCoor(&LpstrPointCloudBuf->Buf[i].strObjOdoCoor.fObjX,&LpstrPointCloudBuf->Buf[i].strObjOdoCoor.fObjY,\
                                    &LstrObjNewCarCoor.fObjX,&LstrObjNewCarCoor.fObjY);
                                LpstrPointCloudBuf->Buf[i].strObjCarCoor = LstrObjNewCarCoor;
                                /* 判断分区是否发生变化，变化则需要更新至新的分区,同时删除本分区的点 */
                                LenuPointCloudNewArea = JudgeObjInCloudBufArea(LenuGroup,LstrObjNewCarCoor.fObjY);
                                if(LenuPointCloudNewArea != LenuPointCloudArea)
                                {
                                    /* 存在时间置0，以保证进入新的区域能够立即更新，但实际探测时间需要保持点云的原始属性不变--2024-04-15 */
                                    LpstrPointCloudBuf->Buf[i].u16ExistTime = 0;
                                    //LpstrPointCloudBuf->Buf[i].u32DetSysTime = GdSystemMsTimer;
                                    AddOnePointCloudToBuf(LenuGroup,LenuPointCloudNewArea,&LpstrPointCloudBuf->Buf[i]);
                                    //DeleteOnePointCloudFromBuf(LenuGroup,LenuPointCloudArea);
                                    /* 不直接删除，保留在缓存队列中，等待超出历史周期进行消亡处理，只把有效性置为无效 */
                                    LpstrPointCloudBuf->Buf[i].u8ValidFlag = 0;
                                    LpstrPointCloudBuf->u8BufValidPointCnt--;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    else
    {
        /* 队列为空，不做处理 */
    }
}


/******************************************************************************
 * 函数名称: CalClusterObjTypeCnt
 * 
 * 功能描述: 计算一个聚类群中不同障碍物类型的个数
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-04 14:08   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void CalClusterObjTypeCnt(SnsPointCloudUnitType *LpstrPointUnit,ClusterType *LpstrClusterUnit)
{
    if(LpstrPointUnit->enuObjType == OBJ_PVC_PIPE_TYPE)
    {
        if(LpstrPointUnit->eMeasType == PDC_SNS_MEAS_STD)
        {
            LpstrClusterUnit->ObjTypeCnt.u8StdPVC_PointCnt++;
            LpstrClusterUnit->ObjTypeCnt.u8StdTotalPointCnt++;
        }
        else
        {
            LpstrClusterUnit->ObjTypeCnt.u8ChirpPVC_PointCnt++;
            LpstrClusterUnit->ObjTypeCnt.u8ChirpTotalPointCnt++;
        }
    }
    else if(LpstrPointUnit->enuObjType == OBJ_LOW_CURB_TYPE)
    {
        if(LpstrPointUnit->eMeasType == PDC_SNS_MEAS_STD)
        {
            LpstrClusterUnit->ObjTypeCnt.u8StdLowCurb_PointCnt++;
            LpstrClusterUnit->ObjTypeCnt.u8StdTotalPointCnt++;
        }
        else
        {
            LpstrClusterUnit->ObjTypeCnt.u8ChirpLowCurb_PointCnt++;
            LpstrClusterUnit->ObjTypeCnt.u8ChirpTotalPointCnt++;
        }
    }
    else if(LpstrPointUnit->enuObjType == OBJ_BIGWALL_TYPE)
    {
        if(LpstrPointUnit->eMeasType == PDC_SNS_MEAS_STD)
        {
            LpstrClusterUnit->ObjTypeCnt.u8StdBigWall_PointCnt++;
            LpstrClusterUnit->ObjTypeCnt.u8StdTotalPointCnt++;
        }
        else
        {
            LpstrClusterUnit->ObjTypeCnt.u8ChirpBigWall_PointCnt++;
            LpstrClusterUnit->ObjTypeCnt.u8ChirpTotalPointCnt++;
        }
    }
    else if(LpstrPointUnit->enuObjType == OBJ_HIGH_HIGH_CUEB)
    {
        if(LpstrPointUnit->eMeasType == PDC_SNS_MEAS_STD)
        {
            LpstrClusterUnit->ObjTypeCnt.u8StdHigh_HighCurb_PointCnt++;
            LpstrClusterUnit->ObjTypeCnt.u8StdTotalPointCnt++;
        }
        else
        {
            LpstrClusterUnit->ObjTypeCnt.u8ChirpHigh_HighCurb_PointCnt++;
            LpstrClusterUnit->ObjTypeCnt.u8ChirpTotalPointCnt++;
        }
    }
}


/******************************************************************************
 * 函数名称: SideCalClusterObjTypeCnt
 * 
 * 功能描述: 侧边计算障碍物的类型计数；主要为了处理非侧边探头的远距离点云进入到侧边后，引起侧边Map高低识别不准的问题
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-04-09 08:40   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SideCalClusterObjTypeCnt(SnsPointCloudUnitType *LpstrPointUnit,ClusterType *LpstrClusterUnit,PointCloudAreaType LenuPointCloudArea)
{
    uint8 Lu8PointNoSideSnsFlag = 0;
    uint8 Lu8GuidanceAlwaysHighFlag = 0;
#if 0
    if(LenuPointCloudArea == POINT_CLOUD_AREA0)
    {
        if(LpstrPointUnit->enuSnsCh != PDC_SNS_CH_FLS_RLS)
        {
            if(LpstrPointUnit->u16MasterDis > 800)
            {
                Lu8PointNoSideSnsFlag = 1;
            }
        }
    }
    else if(LenuPointCloudArea == POINT_CLOUD_AREA4)
    {
        if(LpstrPointUnit->enuSnsCh != PDC_SNS_CH_FRS_RRS)
        {
            if(LpstrPointUnit->u16MasterDis > 800)
            {
                Lu8PointNoSideSnsFlag = 1;
            }
        }
    }
#endif

    if(GstrParkingGuidenceData.fCarAngleSub > CAR_SEARCH_TO_GUIDANCE_30_ANGLE)
    {
        if(LpstrPointUnit->u16MasterDis < 2000)
        {
            Lu8GuidanceAlwaysHighFlag = 1;
        }
    }
#if 0
    if(LenuPointCloudArea == POINT_CLOUD_AREA4)
    {
        if((GfMessageTime > 22)&&(GfMessageTime < 28))
        {
            VS_PRINT("Time:%.3f,CarAngleSub:%.3f,MasterDis:%d\r\n",GfMessageTime,GstrParkingGuidenceData.fCarAngleSub*57.3,LpstrPointUnit->u16MasterDis);
        }
    }
#endif


    if(Lu8PointNoSideSnsFlag)
    {
        if(LpstrPointUnit->eMeasType == PDC_SNS_MEAS_STD)
        {
            LpstrClusterUnit->ObjTypeCnt.u8StdLowCurb_PointCnt++;
            LpstrClusterUnit->ObjTypeCnt.u8StdTotalPointCnt++;
        }
        else
        {
            LpstrClusterUnit->ObjTypeCnt.u8ChirpLowCurb_PointCnt++;
            LpstrClusterUnit->ObjTypeCnt.u8ChirpTotalPointCnt++;
        }
    }
    else if(Lu8GuidanceAlwaysHighFlag)
    {
        if(LpstrPointUnit->eMeasType == PDC_SNS_MEAS_STD)
        {
            LpstrClusterUnit->ObjTypeCnt.u8StdBigWall_PointCnt++;
            LpstrClusterUnit->ObjTypeCnt.u8StdTotalPointCnt++;
        }
        else
        {
            LpstrClusterUnit->ObjTypeCnt.u8ChirpBigWall_PointCnt++;
            LpstrClusterUnit->ObjTypeCnt.u8ChirpTotalPointCnt++;
        }
    }
    else
    {
        if(LpstrPointUnit->enuObjType == OBJ_PVC_PIPE_TYPE)
        {
            if(LpstrPointUnit->eMeasType == PDC_SNS_MEAS_STD)
            {
                LpstrClusterUnit->ObjTypeCnt.u8StdPVC_PointCnt++;
                LpstrClusterUnit->ObjTypeCnt.u8StdTotalPointCnt++;
            }
            else
            {
                LpstrClusterUnit->ObjTypeCnt.u8ChirpPVC_PointCnt++;
                LpstrClusterUnit->ObjTypeCnt.u8ChirpTotalPointCnt++;
            }
        }
        else if(LpstrPointUnit->enuObjType == OBJ_LOW_CURB_TYPE)
        {
            if(LpstrPointUnit->eMeasType == PDC_SNS_MEAS_STD)
            {
                LpstrClusterUnit->ObjTypeCnt.u8StdLowCurb_PointCnt++;
                LpstrClusterUnit->ObjTypeCnt.u8StdTotalPointCnt++;
            }
            else
            {
                LpstrClusterUnit->ObjTypeCnt.u8ChirpLowCurb_PointCnt++;
                LpstrClusterUnit->ObjTypeCnt.u8ChirpTotalPointCnt++;
            }
        }
        else if(LpstrPointUnit->enuObjType == OBJ_BIGWALL_TYPE)
        {
            if(LpstrPointUnit->eMeasType == PDC_SNS_MEAS_STD)
            {
                LpstrClusterUnit->ObjTypeCnt.u8StdBigWall_PointCnt++;
                LpstrClusterUnit->ObjTypeCnt.u8StdTotalPointCnt++;
            }
            else
            {
                LpstrClusterUnit->ObjTypeCnt.u8ChirpBigWall_PointCnt++;
                LpstrClusterUnit->ObjTypeCnt.u8ChirpTotalPointCnt++;
            }
        }
        else if(LpstrPointUnit->enuObjType == OBJ_HIGH_HIGH_CUEB)
        {
            if(LpstrPointUnit->eMeasType == PDC_SNS_MEAS_STD)
            {
                LpstrClusterUnit->ObjTypeCnt.u8StdHigh_HighCurb_PointCnt++;
                LpstrClusterUnit->ObjTypeCnt.u8StdTotalPointCnt++;
            }
            else
            {
                LpstrClusterUnit->ObjTypeCnt.u8ChirpHigh_HighCurb_PointCnt++;
                LpstrClusterUnit->ObjTypeCnt.u8ChirpTotalPointCnt++;
            }
        }
    }
}


/******************************************************************************
 * 函数名称: JudgeClusterType
 * 
 * 功能描述: 判断聚类簇对象的障碍物类型
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-04 14:26   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void JudgeClusterType(ClusterType *LpstrClusterUnit)
{
#if 1
    uint8 Lu8Pvc_Cnt,Lu8BigWall_Cnt,Lu8LowCurb_Cnt,Lu8High_HighCurbCnt;
    uint8 Lu8MaxCnt = 0;
    uint8 Lu8MaxCntInx = 0;

    

    Lu8Pvc_Cnt = LpstrClusterUnit->ObjTypeCnt.u8StdPVC_PointCnt + LpstrClusterUnit->ObjTypeCnt.u8ChirpPVC_PointCnt;
    Lu8BigWall_Cnt = LpstrClusterUnit->ObjTypeCnt.u8StdBigWall_PointCnt + LpstrClusterUnit->ObjTypeCnt.u8ChirpBigWall_PointCnt;
    Lu8High_HighCurbCnt = LpstrClusterUnit->ObjTypeCnt.u8StdHigh_HighCurb_PointCnt + LpstrClusterUnit->ObjTypeCnt.u8ChirpHigh_HighCurb_PointCnt;
    Lu8LowCurb_Cnt = LpstrClusterUnit->ObjTypeCnt.u8StdLowCurb_PointCnt + LpstrClusterUnit->ObjTypeCnt.u8ChirpLowCurb_PointCnt;

    if(Lu8Pvc_Cnt >= Lu8MaxCnt)
    {
        Lu8MaxCntInx = 0;
        Lu8MaxCnt = Lu8Pvc_Cnt;
    }

    if(Lu8BigWall_Cnt >= Lu8MaxCnt)
    {
        Lu8MaxCntInx = 1;
        Lu8MaxCnt = Lu8BigWall_Cnt;
    }
    
    if(Lu8High_HighCurbCnt >= Lu8MaxCnt)
    {
        Lu8MaxCntInx = 2;
        Lu8MaxCnt = Lu8High_HighCurbCnt;
    }
    
    if(Lu8LowCurb_Cnt >= Lu8MaxCnt)
    {
        Lu8MaxCntInx = 3;
        Lu8MaxCnt = Lu8LowCurb_Cnt;
    }
    
    if(Lu8MaxCntInx == 0)
    {
        LpstrClusterUnit->enuClusterType = OBJ_PVC_PIPE_TYPE;
    }
    else if(Lu8MaxCntInx == 1)
    {
        LpstrClusterUnit->enuClusterType = OBJ_BIGWALL_TYPE;
    }
    else if(Lu8MaxCntInx == 2)
    {
        LpstrClusterUnit->enuClusterType = OBJ_HIGH_CURB_TYPE;
    }
    else
    {
        LpstrClusterUnit->enuClusterType = OBJ_LOW_CURB_TYPE;
    }

#endif
        
#if 0
    VS_PRINT("Cluster Type:%d,ChirpTotalCnt:%d,PVC:%d,LowCurb:%d,BigWall:%d,High_HighCurb:%d,   StdTotalCnt:%d,PVC:%d,LowCurb:%d,BigWall:%d,High_HighCurb:%d,ChirpMax:%d,StdMax:%d\r\n",LpstrClusterUnit->enuClusterType,\
            LpstrClusterUnit->u8ChirpTotalPointCnt,LpstrClusterUnit->u8ChirpPVC_PointCnt,LpstrClusterUnit->u8ChirpLowCurb_PointCnt,\
            LpstrClusterUnit->u8ChirpBigWall_PointCnt,LpstrClusterUnit->u8ChirpHigh_HighCurb_PointCnt,\
            LpstrClusterUnit->u8StdTotalPointCnt,LpstrClusterUnit->u8StdPVC_PointCnt,LpstrClusterUnit->u8StdLowCurb_PointCnt,\
            LpstrClusterUnit->u8StdBigWall_PointCnt,LpstrClusterUnit->u8StdHigh_HighCurb_PointCnt,\
            Lu8ChirpMaxCntInx,Lu8StdMaxCntInx);
#endif
}



/******************************************************************************
 * 函数名称: JudegeClusterTwoPointNear
 * 
 * 功能描述: 判断聚类两个相邻点临近，根据不同的区域设置不同的条件
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-01-03 15:01   V0.1      AntonyFang   初次发布
 ******************************************************************************/
uint8 JudegeClusterTwoPointNear(ObjCoorType LstrP1_Coor,ObjCoorType LstrP2_Coor,PointCloudAreaType LenuPointCloudArea)
{
    uint8 Lu8TwoPointNearFlag = 0;
    float LfX_Sub,LfY_Sub;

    LfX_Sub = ABS(LstrP1_Coor.fObjX,LstrP2_Coor.fObjX);
    LfY_Sub = ABS(LstrP1_Coor.fObjY,LstrP2_Coor.fObjY);

    if((LenuPointCloudArea == POINT_CLOUD_AREA0)||(LenuPointCloudArea == POINT_CLOUD_AREA4))
    {
        if((LfX_Sub < AREA_CLUSTER_0_4_X_SUB)&&(LfY_Sub < AREA_CLUSTER_0_4_Y_SUB))
        {
            Lu8TwoPointNearFlag = 1;
        }
    }
    else
    {
        if((LfX_Sub < AREA_CLUSTER_1_3_X_SUB)&&(LfY_Sub < AREA_CLUSTER_1_3_Y_SUB))
        {
            Lu8TwoPointNearFlag = 1;
        }
    }
    

    return Lu8TwoPointNearFlag;
}



/******************************************************************************
 * 函数名称: FindClusterPointNearSns
 * 
 * 功能描述: 查找聚类中心点最近的探头索引，以便后续的聚类的噪点过滤
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-01-30 20:25   V0.1      AntonyFang   初次发布
 ******************************************************************************/
PDCSnsChannelType FindClusterPointNearSns(PDCSnsGroupType LenuGroup,float LfMapPoint_X,float LfMapPoint_Y)
{
    PDCSnsChannelType LePDCSnsCh;
    float LfNearSns_Y_ToMap;
    float LfSns_Y_ToMap;
    PDCSnsChannelType LeNearSnsCh = PDC_SNS_CH_NONE;
    
    LfNearSns_Y_ToMap = 5000;        
    for(LePDCSnsCh = PDC_SNS_CH_FL_RL; LePDCSnsCh < PDC_SNS_CH_FRS_RRS; LePDCSnsCh++)
    {
        LfSns_Y_ToMap = ABS(LfMapPoint_Y,GstrRamSnsCoor[LenuGroup][LePDCSnsCh].cRadarY);
        if(LfSns_Y_ToMap < LfNearSns_Y_ToMap)
        {
            LfNearSns_Y_ToMap = LfSns_Y_ToMap;
            LeNearSnsCh = LePDCSnsCh;
        }
    }
    
    return LeNearSnsCh;
}


/******************************************************************************
 * 函数名称: FindClusterPointMostSns
 * 
 * 功能描述: 
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-01-31 09:56   V0.1      AntonyFang   初次发布
 ******************************************************************************/
PDCSnsChannelType FindClusterMostPointSns(uint8 *Lu8SnsData)
{
    uint8 i;
    uint8 Lu8SnsMostPointCnt = 0;
    PDCSnsChannelType LeMostPointSnsCh = PDC_SNS_CH_FLS_RLS;
            
    for(i = 0; i < 6; i++)
    {
        if(Lu8SnsMostPointCnt < Lu8SnsData[i])
        {
            Lu8SnsMostPointCnt = Lu8SnsData[i];
            LeMostPointSnsCh = (PDCSnsChannelType)i;
        }
    }
    
    return LeMostPointSnsCh;
}



/******************************************************************************
 * 函数名称: PointClusterHandle
 * 
 * 功能描述: 对一组点进行聚类的处理
 * 
 * 输入参数:LpstrPointCoor--输入数据的指针； Lu8PointsNum--输入点的个数；
 * 
 * 输出参数:LpstrClusterCoor--输出聚类坐标 
 * 
 * 返回值:Lu8ClusterNum--聚类簇的个数 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-21 16:10   V0.1      AntonyFang   初次发布
 ******************************************************************************/
uint8 PointClusterHandle(PDCSnsGroupType LenuGroup,SnsPointCloudUnitType *LpstrPointCloudUnit,AreaClusterBufType *LstrAreaClusterBuf,PointCloudAreaType LenuPointCloudArea)
{
    uint8 Lu8ClusterNum;
    uint8 i,j,k;
    ClusterType LpstrClusterInf[AREA_CLUSTER_NUM];
    uint8 Lu8FindClusterFlag[AREA_CLUSTER_NUM];
    uint8 Lu8PointInClusterFlag[AREA_CLUSTER_POINT_NUM];
    uint32 Lu32StdHeightSum;
    uint32 Lu32ChirpHeightSum;
    uint32 Lu32MasterDisSum;
    uint8 Lu8StdPointCnt;
    uint8 Lu8ChirpPointCnt;
    uint32 Lu32StdMasterDis;
    uint8 Lu8NoiseFlag;
    uint8 Lu8TwoPointNearFlag = 0;
    uint8 Lu8ClusterByMasterCnt;
    uint8 Lu8RealTimePointFlag;
    uint8 Lu8NoiseUseStdPointCnt;
    uint8 Lu8NoiseUseChirpPointCnt;
    uint8 Lu8ExistTimeLessThan300_ms_Cnt;
    uint8 Lu8ClusterSnsCnt[6];
    

    for(i = 0; i < LstrAreaClusterBuf->TotalPointCnt; i++)
    {
        Lu8PointInClusterFlag[i] = 0;
    }
    Lu8ClusterNum = 0;
    LpstrClusterInf[Lu8ClusterNum] = GstrClusterInit;
    Lu8FindClusterFlag[Lu8ClusterNum] = 0;
    Lu32StdHeightSum = 0;
    Lu32ChirpHeightSum = 0;
    Lu32MasterDisSum = 0;
    Lu8StdPointCnt = 0;
    Lu8ChirpPointCnt = 0;
    Lu32StdMasterDis = 0;
    Lu8NoiseFlag = 0;
    Lu8ClusterByMasterCnt = 0;
    Lu8RealTimePointFlag = 0;
    Lu8NoiseUseStdPointCnt = 0;
    Lu8NoiseUseChirpPointCnt = 0;
    Lu8ExistTimeLessThan300_ms_Cnt = 0;
    for(k = 0; k < 6; k++)
    {
        Lu8ClusterSnsCnt[k] = 0;
    }
    if(LstrAreaClusterBuf->TotalPointCnt >= 2)
    {
        for(i = 0; i < LstrAreaClusterBuf->TotalPointCnt; i++)
        {
            if(!Lu8PointInClusterFlag[i])
            {
                for(j = i+1; j < LstrAreaClusterBuf->TotalPointCnt; j++)
                {
                    if(!Lu8PointInClusterFlag[j])
                    {
                        if(!Lu8FindClusterFlag[Lu8ClusterNum])
                        {
                            Lu8TwoPointNearFlag = JudegeClusterTwoPointNear(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].strObjCarCoor,LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].strObjCarCoor,LenuPointCloudArea);
                            if(Lu8TwoPointNearFlag)
                            {
                                Lu8FindClusterFlag[Lu8ClusterNum] = 1;
                                LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjX = LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].strObjCarCoor.fObjX+LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].strObjCarCoor.fObjX;
                                LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjX = LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjX/2;
                                
                                LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjY = LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].strObjCarCoor.fObjY+LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].strObjCarCoor.fObjY;
                                LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjY = LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjY/2;
                                /* 定扫类型需要同样传递至Map层 */
                                if(!LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].u8TypeAlredayBeUseFlag)
                                {
                                    LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].u8TypeAlredayBeUseFlag = 1;
                                    CalClusterObjTypeCnt(&LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]],&LpstrClusterInf[Lu8ClusterNum]);
                                    if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].eMeasType == PDC_SNS_MEAS_STD)
                                    {
                                        Lu8StdPointCnt++;
                                        Lu32StdHeightSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].u16MasterHeight;
                                    }
                                    else
                                    {
                                        Lu8ChirpPointCnt++;
                                        Lu32ChirpHeightSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].u16MasterHeight;
                                    }
                                }
                                if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].eMeasType == PDC_SNS_MEAS_STD)
                                {
                                    Lu8NoiseUseStdPointCnt++;
                                    Lu32StdMasterDis += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].u16MasterDis;
                                    Lu32MasterDisSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].u16MasterDis;
                                }
                                else
                                {
                                    Lu8NoiseUseChirpPointCnt++;
                                    Lu32MasterDisSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].u16MasterDis;
                                }
                                if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].enuDetType == POINT_CLOUD_DET_BY_MASTER)
                                {
                                    Lu8ClusterByMasterCnt++;
                                }
                                Lu8PointInClusterFlag[i] = 1;
                                

                                if(!LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u8TypeAlredayBeUseFlag)
                                {
                                    LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u8TypeAlredayBeUseFlag = 1;
                                    CalClusterObjTypeCnt(&LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]],&LpstrClusterInf[Lu8ClusterNum]);
                                    if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].eMeasType == PDC_SNS_MEAS_STD)
                                    {
                                        Lu8StdPointCnt++;
                                        Lu32StdHeightSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterHeight;
                                    }
                                    else
                                    {
                                        Lu8ChirpPointCnt++;
                                        Lu32ChirpHeightSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterHeight;
                                    }
                                }
                                if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].eMeasType == PDC_SNS_MEAS_STD)
                                {
                                    Lu8NoiseUseStdPointCnt++;
                                    Lu32StdMasterDis += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterDis;
                                    Lu32MasterDisSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterDis;
                                }
                                else
                                {
                                    Lu8NoiseUseChirpPointCnt++;
                                    Lu32MasterDisSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterDis;
                                }
                                if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].enuDetType == POINT_CLOUD_DET_BY_MASTER)
                                {
                                    Lu8ClusterByMasterCnt++;
                                }
                                Lu8PointInClusterFlag[j] = 1;
                                
                                if((LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].u16ExistTime < REAL_TIME_MIN_VALUE)||(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16ExistTime < REAL_TIME_MIN_VALUE))
                                {
                                    Lu8RealTimePointFlag = 1; 
                                }
                                
                                if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].u16ExistTime < 330)
                                {
                                    Lu8ExistTimeLessThan300_ms_Cnt++;
                                }
                                if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16ExistTime < 330)
                                {
                                    Lu8ExistTimeLessThan300_ms_Cnt++;
                                }
                                for(k = 0; k < 6; k++)
                                {
                                    if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].enuSnsCh == k)
                                    {
                                        Lu8ClusterSnsCnt[k]++;
                                        break;
                                    }
                                }
                                for(k = 0; k < 6; k++)
                                {
                                    if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].enuSnsCh == k)
                                    {
                                        Lu8ClusterSnsCnt[k]++;
                                        break;
                                    }
                                }
                            }
                        }
                        else
                        {
                            Lu8TwoPointNearFlag = JudegeClusterTwoPointNear(LpstrClusterInf[Lu8ClusterNum].ClusterCoor,LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].strObjCarCoor,LenuPointCloudArea);
                            if(Lu8TwoPointNearFlag)
                            {
                                LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjX += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].strObjCarCoor.fObjX;
                                LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjX = LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjX/2;
                                
                                LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjY += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].strObjCarCoor.fObjY;
                                LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjY = LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjY/2;
                                if(!LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u8TypeAlredayBeUseFlag)
                                {
                                    LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u8TypeAlredayBeUseFlag = 1;
                                    CalClusterObjTypeCnt(&LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]],&LpstrClusterInf[Lu8ClusterNum]);
                                    if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].eMeasType == PDC_SNS_MEAS_STD)
                                    {
                                        Lu8StdPointCnt++;
                                        Lu32StdHeightSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterHeight;
                                    }
                                    else
                                    {
                                        Lu8ChirpPointCnt++;
                                        Lu32ChirpHeightSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterHeight;
                                    }
                                }
                                if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].eMeasType == PDC_SNS_MEAS_STD)
                                {
                                    Lu8NoiseUseStdPointCnt++;
                                    Lu32StdMasterDis += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterDis;
                                    Lu32MasterDisSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterDis;
                                }
                                else
                                {
                                    Lu8NoiseUseChirpPointCnt++;
                                    Lu32MasterDisSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterDis;
                                }
                                if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].enuDetType == POINT_CLOUD_DET_BY_MASTER)
                                {
                                    Lu8ClusterByMasterCnt++;
                                }
                                Lu8PointInClusterFlag[j] = 1;
                                if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16ExistTime < REAL_TIME_MIN_VALUE)
                                {
                                    Lu8RealTimePointFlag = 1; 
                                }
                                
                                if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16ExistTime < 330)
                                {
                                    Lu8ExistTimeLessThan300_ms_Cnt++;
                                }
                                for(k = 0; k < 6; k++)
                                {
                                    if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].enuSnsCh == k)
                                    {
                                        Lu8ClusterSnsCnt[k]++;
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if(Lu8FindClusterFlag[Lu8ClusterNum])
            {
                if(Lu8StdPointCnt > 0)
                {
                    LpstrClusterInf[Lu8ClusterNum].u16StdMasterHeight = Lu32StdHeightSum/Lu8StdPointCnt;
                }
                else
                {
                    LpstrClusterInf[Lu8ClusterNum].u16StdMasterHeight = 0;
                }

                if(Lu8NoiseUseStdPointCnt > 0)
                {
                    Lu32StdMasterDis = Lu32StdMasterDis/Lu8NoiseUseStdPointCnt;
                }
                else
                {
                    Lu32StdMasterDis = 0;
                }
                
                if(Lu8ChirpPointCnt > 0)
                {
                    LpstrClusterInf[Lu8ClusterNum].u16ChirpMasterHeight = Lu32ChirpHeightSum/Lu8ChirpPointCnt;
                }
                else
                {
                    LpstrClusterInf[Lu8ClusterNum].u16ChirpMasterHeight = 0;
                }
                if((Lu8NoiseUseStdPointCnt+Lu8NoiseUseChirpPointCnt) > 0)
                {
                    LpstrClusterInf[Lu8ClusterNum].u16MasterDis = Lu32MasterDisSum/(Lu8NoiseUseStdPointCnt+Lu8NoiseUseChirpPointCnt);
                }
                else
                {
                    LpstrClusterInf[Lu8ClusterNum].u16MasterDis = 0;
                }
#if 0
                VS_PRINT("Time:%.3f,StdMasterDis:%d,StdCnt:%d,ChirpCnt:%d\r\n",GfMessageTime,Lu32StdMasterDis,\
                    Lu8StdPointCnt,Lu8ChirpPointCnt);
#endif
                if(Gu16CarSpdForSnsUse < 800)
                {
#if 0
                    PDCSnsChannelType LeClusterNearSnsCh = PDC_SNS_CH_NONE;
                    uint16 Lu16ChirpMasterDisSub,Lu16ChirpLeftDisSub,Lu16ChirpRightDisSub;
                    
                    if((Lu32StdMasterDis > SAME_FREQ_NOISE_MIN_DIS)&&(Lu32StdMasterDis < SAME_FREQ_NOISE_MAX_DIS))
                    {
                        /* 根据当前点的坐标，找到临近的两个探头，分别使用临近探头的主发和对应的侦听的最近扫频距离来进行约束 */
                        LeClusterNearSnsCh = FindClusterMostPointSns(&Lu8ClusterSnsCnt[0]);
                        if(GstrMapSnsRealTimeDis[LenuGroup][LeClusterNearSnsCh].u16ChirpMasDis != 0xFFFF)
                        {
                            Lu16ChirpMasterDisSub = ABS(GstrMapSnsRealTimeDis[LenuGroup][LeClusterNearSnsCh].u16ChirpMasDis,LpstrClusterInf[Lu8ClusterNum].u16MasterDis);
                            if(Lu16ChirpMasterDisSub < 200)
                            {
                                Lu8NoiseFlag = 0;
                            }
                            else
                            {
                                if(LpstrClusterInf[Lu8ClusterNum].u16MasterDis > GstrMapSnsRealTimeDis[LenuGroup][LeClusterNearSnsCh].u16ChirpMasDis)
                                {
                                    Lu8NoiseFlag = 0;
                                }
                                else
                                {
                                    Lu8NoiseFlag = 1;
                                }
                            }
                        }
                        else
                        {
                            Lu8NoiseFlag = 1;
                        }
                        if(Lu8NoiseFlag)
                        {
                            if(GstrMapSnsRealTimeDis[LenuGroup][LeClusterNearSnsCh].u16ChirpLeftDis < GstrMapSnsRealTimeDis[LenuGroup][LeClusterNearSnsCh].u16ChirpRightDis)
                            {
                                if(GstrMapSnsRealTimeDis[LenuGroup][LeClusterNearSnsCh].u16ChirpLeftDis < 1000)
                                {
                                    Lu16ChirpMasterDisSub = ABS(GstrMapSnsRealTimeDis[LenuGroup][LeClusterNearSnsCh].u16ChirpLeftDis,LpstrClusterInf[Lu8ClusterNum].u16MasterDis);
                                    if(Lu16ChirpMasterDisSub < 200)
                                    {
                                        Lu8NoiseFlag = 0;
                                    }
                                    else
                                    {
                                        if(LpstrClusterInf[Lu8ClusterNum].u16MasterDis > GstrMapSnsRealTimeDis[LenuGroup][LeClusterNearSnsCh].u16ChirpLeftDis)
                                        {
                                            Lu8NoiseFlag = 0;
                                        }
                                        else
                                        {
                                            Lu8NoiseFlag = 1;
                                        }
                                    }
                                }
                                else
                                {
                                    Lu8NoiseFlag = 1;
                                }
                            }
                            else
                            {
                                if(GstrMapSnsRealTimeDis[LenuGroup][LeClusterNearSnsCh].u16ChirpRightDis < 1000)
                                {
                                    Lu16ChirpMasterDisSub = ABS(GstrMapSnsRealTimeDis[LenuGroup][LeClusterNearSnsCh].u16ChirpRightDis,LpstrClusterInf[Lu8ClusterNum].u16MasterDis);
                                    if(Lu16ChirpMasterDisSub < 200)
                                    {
                                        Lu8NoiseFlag = 0;
                                    }
                                    else
                                    {
                                        if(LpstrClusterInf[Lu8ClusterNum].u16MasterDis > GstrMapSnsRealTimeDis[LenuGroup][LeClusterNearSnsCh].u16ChirpRightDis)
                                        {
                                            Lu8NoiseFlag = 0;
                                        }
                                        else
                                        {
                                            Lu8NoiseFlag = 1;
                                        }
                                    }
                                }
                                else
                                {
                                    Lu8NoiseFlag = 1;
                                }
                            }
                        }
                        if(Lu8ExistTimeLessThan300_ms_Cnt < 2)
                        {
                            Lu8NoiseFlag = 1;
                        }
                    }

#else
                    uint16 Lu16MinNoiseDis;

                    if(GstrParkingGuidenceData.u8SameFreqNoiseFlag[LenuGroup])
                    {
                        if(GstrParkingGuidenceData.eParking_Sts == PARKING_SUMMON)
                        {
                            Lu16MinNoiseDis = SAME_FREQ_NOISE_MIN_DIS_SUMMON;
                        }
                        else if(GstrParkingGuidenceData.eCurrentAdasSts == APA_GUIDANCE)
                        {
                            Lu16MinNoiseDis = SAME_FREQ_NOISE_MIN_DIS_GUIDANCE;
                        }
                        else
                        {
                            Lu16MinNoiseDis = SAME_FREQ_NOISE_MIN_DIS;
                        }
                        if((Lu32StdMasterDis > Lu16MinNoiseDis)&&(Lu32StdMasterDis < SAME_FREQ_NOISE_MAX_DIS))
                        {
                            if((Lu8NoiseUseChirpPointCnt == 0))
                            {
                                Lu8NoiseFlag = 1;
                            }
                        }
                    }
                    else
                    {
                        if(GstrParkingGuidenceData.eParking_Sts == PARKING_SUMMON)
                        {
                            Lu16MinNoiseDis = SAME_FREQ_NOISE_MIN_DIS_SUMMON;
                        }
                        else if(GstrParkingGuidenceData.eCurrentAdasSts == APA_GUIDANCE)
                        {
                            Lu16MinNoiseDis = SAME_FREQ_NOISE_MIN_DIS_GUIDANCE+200;
                        }
                        else
                        {
                            Lu16MinNoiseDis = SAME_FREQ_NOISE_MIN_DIS+100;
                        }
                        if((Lu32StdMasterDis > Lu16MinNoiseDis)&&(Lu32StdMasterDis < SAME_FREQ_NOISE_MAX_DIS))
                        {
                            if((Lu8NoiseUseChirpPointCnt == 0)&&(Lu8ClusterByMasterCnt == 0))
                            {
                                Lu8NoiseFlag = 1;
                            }
                        }
                    }

#endif
                    /* 添加对于近距离80cm以内的点云，必须包含定频，否则当噪点处理 */
                    if(LpstrClusterInf[Lu8ClusterNum].u16MasterDis < CHIRP_NOISE_MIN_DIS)
                    {
                        if((Lu8NoiseUseStdPointCnt == 0)&&(Lu8ClusterByMasterCnt == 0))
                        {
                            Lu8NoiseFlag = 1;
                        }
                    }
                    /* 针对中间区域的RML RMR 等扫频FOV 过大导致的噪点问题进行修复 */
                    if(LenuPointCloudArea == POINT_CLOUD_AREA2)
                    {
                        if(GstrParkingGuidenceData.eParking_Sts != PARKING_SUMMON)
                        {
                            if((LpstrClusterInf[Lu8ClusterNum].u16MasterDis > 600)&&(LpstrClusterInf[Lu8ClusterNum].u16MasterDis < 1500))
                            {
                                if((Lu8NoiseUseStdPointCnt == 0)&&(Lu8ClusterByMasterCnt == 0))
                                {
                                    Lu8NoiseFlag = 1;
                                }
                            }
                        }
                    }

                    /* 针对RL 定位错误进行过滤 */
                    if(LenuPointCloudArea == POINT_CLOUD_AREA1)
                    {
                        if(GstrParkingGuidenceData.eParking_Sts != PARKING_SUMMON)
                        {
                            if((LpstrClusterInf[Lu8ClusterNum].u16MasterDis > 600)&&(LpstrClusterInf[Lu8ClusterNum].u16MasterDis < 1400))
                            {
                                if(LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjY < 800)
                                {
                                    if(Lu8ClusterSnsCnt[PDC_SNS_CH_FML_RML] == 0)
                                    {
                                        Lu8NoiseFlag = 1;
                                    }
                                }
                            }
                        }
                    }

                    /* 针对RL 定位错误进行过滤 */
                    if(LenuPointCloudArea == POINT_CLOUD_AREA3)
                    {
                        if(GstrParkingGuidenceData.eParking_Sts != PARKING_SUMMON)
                        {
                            if((LpstrClusterInf[Lu8ClusterNum].u16MasterDis > 600)&&(LpstrClusterInf[Lu8ClusterNum].u16MasterDis < 1400))
                            {
                                if(LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjY > (-800))
                                {
                                    if(Lu8ClusterSnsCnt[PDC_SNS_CH_FMR_RMR] == 0)
                                    {
                                        Lu8NoiseFlag = 1;
                                    }
                                }
                            }
                        }
                    }

                    /* 添加Noise在近距离主发定位情况下的非滤波处理 */
                    if(Lu8NoiseFlag)
                    {
                        if(Lu8ClusterByMasterCnt != 0)
                        {
                            Lu8NoiseFlag = 0;
                        }
                    }
                }
               
                //Lu8RealTimePointFlag = 1;
                if((Lu8NoiseFlag == 0)&&(Lu8RealTimePointFlag))
                {
                    Lu8ClusterNum++;
                }
                
                if(Lu8ClusterNum == AREA_CLUSTER_NUM)
                {
                    break;
                }
                LpstrClusterInf[Lu8ClusterNum] = GstrClusterInit;
                Lu8FindClusterFlag[Lu8ClusterNum] = 0;
                Lu32StdHeightSum = 0;
                Lu32ChirpHeightSum = 0;
                Lu32MasterDisSum = 0;
                Lu8StdPointCnt = 0;
                Lu8ChirpPointCnt = 0;
                Lu32StdMasterDis = 0;
                Lu8NoiseFlag = 0;
                Lu8ClusterByMasterCnt = 0;
                Lu8RealTimePointFlag = 0;
                Lu8NoiseUseStdPointCnt = 0;
                Lu8NoiseUseChirpPointCnt = 0;
                Lu8ExistTimeLessThan300_ms_Cnt = 0;
                for(k = 0; k < 6; k++)
                {
                    Lu8ClusterSnsCnt[k] = 0;
                }
            }
        }

        for(i = 0; i < Lu8ClusterNum; i++)
        {
            //JudgeClusterType(&LpstrClusterInf[i]);
            LstrAreaClusterBuf->Cluster[i] = LpstrClusterInf[i];
        }
    }

    return Lu8ClusterNum;
}



/******************************************************************************
 * 函数名称: JudgeSideTwoPointNear
 * 
 * 功能描述: 判断侧边Map点是否在同一条直线上
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-11 18:25   V0.1      AntonyFang   初次发布
 ******************************************************************************/
#if 1
uint8 JudgeSideTwoPointNear(ObjCoorType *LpstrP1_Coor,ObjCoorType *LpstrP2_Coor)
{
    uint8 Lu8TwoPointOnSameLineFlag = 0;
    float LfX_Sub;
    float LfY_Sub;
    float LfSidePointX_SubLimit;

    /* 需要根据车速设置不同的值 */
#if 0
    LfSidePointX_SubLimit = 500;
#else
    if(Gu16CarSpdForSnsUse < 300)
    {
        LfSidePointX_SubLimit = 500;
    }
    else
    {
        LfSidePointX_SubLimit = Gu16CarSpdForSnsUse;
        LfSidePointX_SubLimit = LfSidePointX_SubLimit*0.2778;
        LfSidePointX_SubLimit = LfSidePointX_SubLimit*5;
    }
#endif

    LfX_Sub = ABS(LpstrP1_Coor->fObjX,LpstrP2_Coor->fObjX);
    LfY_Sub = ABS(LpstrP1_Coor->fObjY,LpstrP2_Coor->fObjY);

    if((LfX_Sub < LfSidePointX_SubLimit)&&(LfY_Sub < AREA_SIDE_CLUSTER_Y_SUB))
    {
        Lu8TwoPointOnSameLineFlag = 1;
    }    

    return Lu8TwoPointOnSameLineFlag;
}

#else
uint8 JudgeSideTwoPointNear(float P1_Y_Coor,float P2_Y_Coor)
{
    uint8 Lu8TwoPointOnSameLineFlag = 0;
    float LfY_Sub;

    LfY_Sub = ABS(P1_Y_Coor,P2_Y_Coor);

    if(LfY_Sub < AREA_SIDE_CLUSTER_Y_SUB)
    {
        Lu8TwoPointOnSameLineFlag = 1;
    }    

    return Lu8TwoPointOnSameLineFlag;
}
#endif


/******************************************************************************
 * 函数名称: NoSideSnsInSideAreaValidJudge
 * 
 * 功能描述: 判断点云在侧边区域是否有效
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-04-10 20:18   V0.1      AntonyFang   初次发布
 ******************************************************************************/
uint8 NoSideSnsInSideAreaValidJudge(SnsPointCloudUnitType *LpstrPointCloudUnit, PDCSnsGroupType LenuGroup, PointCloudAreaType LenuPointCloudArea)
{
    uint8 Lu8PointCloudValid = 1;
    float LfValidObjY_Coor;
    float LfCoeffi;

    if(LpstrPointCloudUnit->u16MasterDis < 1500)
    {
        LfCoeffi = 0.4;
    }
    else
    {
        LfCoeffi = 0.5;
    }

    if(LenuPointCloudArea == POINT_CLOUD_AREA0)
    {
        if(LpstrPointCloudUnit->enuSnsCh == PDC_SNS_CH_FL_RL)
        {
            if(LpstrPointCloudUnit->u16MasterDis > 900)
            {
                LfValidObjY_Coor = LpstrPointCloudUnit->u16MasterDis*LfCoeffi;
                LfValidObjY_Coor += GstrRamSnsCoor[LenuGroup][PDC_SNS_CH_FL_RL].cRadarY;
                
                if(LpstrPointCloudUnit->strObjCarCoor.fObjY < LfValidObjY_Coor)
                {
                    Lu8PointCloudValid = 0;
                }
            }
        }
        else if(LpstrPointCloudUnit->enuSnsCh == PDC_SNS_CH_FML_RML)
        {
            if(LpstrPointCloudUnit->u16MasterDis > 800)
            {
                LfValidObjY_Coor = LpstrPointCloudUnit->u16MasterDis*LfCoeffi;
                LfValidObjY_Coor += GstrRamSnsCoor[LenuGroup][PDC_SNS_CH_FML_RML].cRadarY;
                
                if(LpstrPointCloudUnit->strObjCarCoor.fObjY < LfValidObjY_Coor)
                {
                    Lu8PointCloudValid = 0;
                }
            }
        }
    }
    else if(LenuPointCloudArea == POINT_CLOUD_AREA4)
    {
        if(LpstrPointCloudUnit->enuSnsCh == PDC_SNS_CH_FR_RR)
        {
            if(LpstrPointCloudUnit->u16MasterDis > 900)
            {
                LfValidObjY_Coor = LpstrPointCloudUnit->u16MasterDis*LfCoeffi;
                LfValidObjY_Coor = -LfValidObjY_Coor;
                LfValidObjY_Coor += GstrRamSnsCoor[LenuGroup][PDC_SNS_CH_FR_RR].cRadarY;
                
                if(LpstrPointCloudUnit->strObjCarCoor.fObjY > LfValidObjY_Coor)
                {
                    Lu8PointCloudValid = 0;
                }
            }
        }
        else if(LpstrPointCloudUnit->enuSnsCh == PDC_SNS_CH_FMR_RMR)
        {
            if(LpstrPointCloudUnit->u16MasterDis > 800)
            {
                LfValidObjY_Coor = LpstrPointCloudUnit->u16MasterDis*LfCoeffi;
                LfValidObjY_Coor = -LfValidObjY_Coor;
                LfValidObjY_Coor += GstrRamSnsCoor[LenuGroup][PDC_SNS_CH_FMR_RMR].cRadarY;
                
                if(LpstrPointCloudUnit->strObjCarCoor.fObjY > LfValidObjY_Coor)
                {
                    Lu8PointCloudValid = 0;
                }
            }
        }
    }
    return Lu8PointCloudValid;
}


/******************************************************************************
 * 函数名称: RearSideAreaPointValidConfirm
 * 
 * 功能描述: 后侧区域点云的有效性判断，主要为解决倒车后退阶段，点被定位到探头前端的问题
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-04-25 17:18   V0.1      AntonyFang   初次发布
 ******************************************************************************/
uint8 RearSideAreaPointValidConfirm(PDCSnsGroupType LenuGroup,SnsPointCloudUnitType *LpstrPointCloudUnit)
{
    uint8 Lu8PointCloudValid = 1;
    float LfPointX_To_SnsDis;
    float LfCoeffi;

    if(LenuGroup == 0x01)
    {
        if(LpstrPointCloudUnit->strObjCarCoor.fObjX > (GstrRamSnsCoor[1][0].cRadarX+300))
        {
            Lu8PointCloudValid = 0;
        }
    }

    return Lu8PointCloudValid;
}



/******************************************************************************
 * 函数名称: SidePointClusterHandle
 * 
 * 功能描述: 侧边Map的聚类处理，根据车辆运行速度的不同，采用不同等级的聚类阈值
 * 
 * 输入参数:LpstrPointCoor--输入数据的指针； Lu8PointsNum--输入点的个数；
 * 
 * 输出参数:LpstrClusterCoor--输出聚类坐标 
 * 
 * 返回值:Lu8ClusterNum--聚类簇的个数 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-11 18:00   V0.1      AntonyFang   初次发布
 ******************************************************************************/
uint8 SidePointClusterHandle(PDCSnsGroupType LenuGroup,SnsPointCloudUnitType *LpstrPointCloudUnit,AreaClusterBufType *LstrAreaClusterBuf,PointCloudAreaType LenuPointCloudArea,uint32 Lu32Time)
{
    uint8 Lu8ClusterNum;
    uint8 i,j;
    ClusterType LpstrClusterInf[AREA_CLUSTER_NUM];
    uint8 Lu8FindClusterFlag[AREA_CLUSTER_NUM];
    uint8 Lu8PointInClusterFlag[AREA_CLUSTER_POINT_NUM];
    uint8 Lu8OneClusterPointCnt;
    uint32 Lu32StdHeightSum;
    uint32 Lu32ChirpHeightSum;
    uint32 Lu32MasterDisSum;
    uint8 Lu8StdPointCnt;
    uint8 Lu8ChirpPointCnt;
    uint8 Lu8TwoPointOnSameLineFlag = 0;
    uint8 Lu8LineP1_Inx,Lu8LineP2_Inx;
    float LfLine_X_Coor,LfLine_Y_Coor;
    ObjCoorType LstrLineCoor;
    float LfClusterCoorX_Sub;
    uint8 Lu8TwoPointNearFlag = 0;
    uint32 Lu32StdMasterDis;
    uint8 Lu8NoiseFlag;
    uint8 Lu8ClusterByLine;
    uint16 Lu16MinNoiseDis;
    uint8 Lu8RealTimePointFlag;
    uint8 Lu8NoiseUseStdPointCnt;
    uint8 Lu8NoiseUseChirpPointCnt;
    uint8 Lu8ValidPointCnt;
    uint8 Lu8SameFreFlag = 0;

    for(i = 0; i < LstrAreaClusterBuf->TotalPointCnt; i++)
    {
        Lu8PointInClusterFlag[i] = 0;
    }
    Lu8ClusterNum = 0;
    LpstrClusterInf[Lu8ClusterNum] = GstrClusterInit;
    Lu8FindClusterFlag[Lu8ClusterNum] = 0;
    Lu32StdHeightSum = 0;
    Lu32ChirpHeightSum = 0;
    Lu32MasterDisSum = 0;
    Lu8StdPointCnt = 0;
    Lu8ChirpPointCnt = 0;
    Lu32StdMasterDis = 0;
    Lu8NoiseFlag = 0;
    Lu8ClusterByLine = 0;
    Lu8RealTimePointFlag = 0;
    Lu8NoiseUseStdPointCnt = 0;
    Lu8NoiseUseChirpPointCnt = 0;
    Lu8ValidPointCnt = 0;

    if(LenuPointCloudArea == POINT_CLOUD_AREA0)
    {
        if(GstrParkingGuidenceData.u8LeftSideFreqNoiseFlag[LenuGroup])
        {
            if(GstrParkingGuidenceData.eParking_Sts == PARKING_SUMMON)
            {
                Lu16MinNoiseDis = SAME_FREQ_NOISE_MIN_DIS_SUMMON;
            }
            else if(GstrParkingGuidenceData.eCurrentAdasSts == APA_GUIDANCE)
            {
                Lu16MinNoiseDis = SAME_FREQ_NOISE_MIN_DIS_GUIDANCE;
            }
            else
            {
                Lu16MinNoiseDis = SAME_FREQ_NOISE_MIN_DIS;
            }
            Lu8SameFreFlag = 1;
        }
        else
        {
            if(GstrParkingGuidenceData.eParking_Sts == PARKING_SUMMON)
            {
                Lu16MinNoiseDis = SAME_FREQ_NOISE_MIN_DIS_SUMMON;
            }
            else if(GstrParkingGuidenceData.eCurrentAdasSts == APA_GUIDANCE)
            {
                Lu16MinNoiseDis = SAME_FREQ_NOISE_MIN_DIS_GUIDANCE+200;
            }
            else
            {
                Lu16MinNoiseDis = SAME_FREQ_NOISE_MIN_DIS+100;
            }
        }
    }
    else
    {
        if(GstrParkingGuidenceData.u8RightSideFreqNoiseFlag[LenuGroup])
        {
            if(GstrParkingGuidenceData.eParking_Sts == PARKING_SUMMON)
            {
                Lu16MinNoiseDis = SAME_FREQ_NOISE_MIN_DIS_SUMMON;
            }
            else if(GstrParkingGuidenceData.eCurrentAdasSts == APA_GUIDANCE)
            {
                Lu16MinNoiseDis = SAME_FREQ_NOISE_MIN_DIS_GUIDANCE;
            }
            else
            {
                Lu16MinNoiseDis = SAME_FREQ_NOISE_MIN_DIS;
            }
            Lu8SameFreFlag = 1;
        }
        else
        {
            if(GstrParkingGuidenceData.eParking_Sts == PARKING_SUMMON)
            {
                Lu16MinNoiseDis = SAME_FREQ_NOISE_MIN_DIS_SUMMON;
            }
            else if(GstrParkingGuidenceData.eCurrentAdasSts == APA_GUIDANCE)
            {
                Lu16MinNoiseDis = SAME_FREQ_NOISE_MIN_DIS_GUIDANCE+200;
            }
            else
            {
                Lu16MinNoiseDis = SAME_FREQ_NOISE_MIN_DIS+100;
            }
        }
    }

    uint8 Lu8NoSideSnsInSideAreaValidFlag = 0;
    uint8 Lu8RearSideAreaValidFlag = 0;
    uint8 Lu8TotalPointLimit;

    if((Gu16CarSpdForSnsUse < 1000)&&(GstrParkingGuidenceData.eLastAdasSts != APA_GUIDANCE))
    {
        Lu8TotalPointLimit = 3;
    }
    else
    {
        if(Lu8SameFreFlag)
        {
            Lu8TotalPointLimit = 3;
        }
        else
        {
            Lu8TotalPointLimit = 2;
        }
    }

#if 0
    if((LenuPointCloudArea == POINT_CLOUD_AREA0)&&(LenuGroup == 0x00))
    {
        if((GfMessageTime > 19.6)&&(GfMessageTime < 20))
        {
			VS_PRINT("Area0 Map,Time:%.3f,TotalPointLimit:%d,TotalPointCnt:%d,LeftSideFreqNoiseFlag:%d,SameFreFlag:%d\r\n", GfMessageTime, \
				Lu8TotalPointLimit, LstrAreaClusterBuf->TotalPointCnt, \
				GstrParkingGuidenceData.u8LeftSideFreqNoiseFlag[LenuGroup], Lu8SameFreFlag);
        }
    }
#endif

    
    if(LstrAreaClusterBuf->TotalPointCnt >= Lu8TotalPointLimit)
    {
        for(i = 0; i < LstrAreaClusterBuf->TotalPointCnt; i++)
        {
            
            Lu8NoSideSnsInSideAreaValidFlag = NoSideSnsInSideAreaValidJudge(&LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]],LenuGroup,LenuPointCloudArea);
            Lu8RearSideAreaValidFlag = RearSideAreaPointValidConfirm(LenuGroup,&LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]]);

            if((Lu8NoSideSnsInSideAreaValidFlag == 0)||(Lu8RearSideAreaValidFlag == 0))
            {
                Lu8PointInClusterFlag[i] = 1;
            }
            if(!Lu8PointInClusterFlag[i])
            {
                if(LenuGroup == 0x00)
                {
                    if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].strObjCarCoor.fObjX > (GstrRamSnsCoor[0][0].cRadarX+FRONT_SIDE_SNS_CLUSTER_LINE_DIS))
                    {
                        Lu8ClusterByLine = 0;
                    }
                    else
                    {
                        Lu8ClusterByLine = 1;
                    }
                }
                else
                {
#if 0
                    Lu8ClusterByLine = 1;
#else
                    if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].strObjCarCoor.fObjX < (GstrRamSnsCoor[1][0].cRadarX-REAR_SIDE_SNS_CLUSTER_LINE_DIS))
                    {
                        Lu8ClusterByLine = 0;
                    }
                    else
                    {
                        Lu8ClusterByLine = 1;
                    }
#endif
                }

                for(j = i+1; j < LstrAreaClusterBuf->TotalPointCnt; j++)
                {
                    Lu8NoSideSnsInSideAreaValidFlag = NoSideSnsInSideAreaValidJudge(&LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]],LenuGroup,LenuPointCloudArea);
                    Lu8RearSideAreaValidFlag = RearSideAreaPointValidConfirm(LenuGroup,&LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]]);

                    if((Lu8NoSideSnsInSideAreaValidFlag == 0)||(Lu8RearSideAreaValidFlag == 0))
                    {
                        Lu8PointInClusterFlag[j] = 1;
                    }
                    if(!Lu8PointInClusterFlag[j])
                    {
                        if(!Lu8FindClusterFlag[Lu8ClusterNum])
                        {
                            if(Lu8ClusterByLine)
                            {
                                Lu8TwoPointOnSameLineFlag = JudgeSideTwoPointNear(&LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].strObjCarCoor,&LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].strObjCarCoor);
                                if(Lu8TwoPointOnSameLineFlag)
                                {
                                    Lu8FindClusterFlag[Lu8ClusterNum] = 1;
                                    if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].strObjCarCoor.fObjX > LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].strObjCarCoor.fObjX)
                                    {
                                        Lu8LineP1_Inx = LstrAreaClusterBuf->u8PointUnitId[i];
                                        Lu8LineP2_Inx = LstrAreaClusterBuf->u8PointUnitId[j];
                                    }
                                    else
                                    {
                                        Lu8LineP1_Inx = LstrAreaClusterBuf->u8PointUnitId[j];
                                        Lu8LineP2_Inx = LstrAreaClusterBuf->u8PointUnitId[i];
                                    }
                                    LfLine_Y_Coor = LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].strObjCarCoor.fObjY+LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].strObjCarCoor.fObjY;
                                    LfLine_Y_Coor = LfLine_Y_Coor/2;
                                    LfLine_X_Coor = LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].strObjCarCoor.fObjX;
                                    LstrLineCoor.fObjX = LfLine_X_Coor;
                                    LstrLineCoor.fObjY = LfLine_Y_Coor;
                                
                                    /* 定扫类型需要同样传递至Map层 */
                                    if(!LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].u8TypeAlredayBeUseFlag)
                                    {
                                        LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].u8TypeAlredayBeUseFlag = 1;
                                        SideCalClusterObjTypeCnt(&LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]],&LpstrClusterInf[Lu8ClusterNum],LenuPointCloudArea);
                                        if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].eMeasType == PDC_SNS_MEAS_STD)
                                        {
                                            Lu8StdPointCnt++;
                                            Lu32StdHeightSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].u16MasterHeight;
                                        }
                                        else
                                        {
                                            Lu8ChirpPointCnt++;
                                            Lu32ChirpHeightSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].u16MasterHeight;
                                        }
                                    }
                                    if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].eMeasType == PDC_SNS_MEAS_STD)
                                    {
                                        Lu8NoiseUseStdPointCnt++;
                                        Lu32StdMasterDis += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].u16MasterDis;
                                        Lu32MasterDisSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].u16MasterDis;
                                    }
                                    else
                                    {
                                        Lu8NoiseUseChirpPointCnt++;
                                        Lu32MasterDisSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].u16MasterDis;
                                    }
                                    Lu8PointInClusterFlag[i] = 1;

                                    if(!LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u8TypeAlredayBeUseFlag)
                                    {
                                        LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u8TypeAlredayBeUseFlag = 1;
                                        SideCalClusterObjTypeCnt(&LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]],&LpstrClusterInf[Lu8ClusterNum],LenuPointCloudArea);
                                        if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].eMeasType == PDC_SNS_MEAS_STD)
                                        {
                                            Lu8StdPointCnt++;
                                            Lu32StdHeightSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterHeight;
                                        }
                                        else
                                        {
                                            Lu8ChirpPointCnt++;
                                            Lu32ChirpHeightSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterHeight;
                                        }
                                    }
                                    if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].eMeasType == PDC_SNS_MEAS_STD)
                                    {
                                        Lu8NoiseUseStdPointCnt++;
                                        Lu32StdMasterDis += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterDis;
                                        Lu32MasterDisSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterDis;
                                    }
                                    else
                                    {
                                        Lu8NoiseUseChirpPointCnt++;
                                        Lu32MasterDisSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterDis;
                                    }
                                    Lu8PointInClusterFlag[j] = 1;

                                    if((LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].u16ExistTime < REAL_TIME_MIN_VALUE)||(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16ExistTime < REAL_TIME_MIN_VALUE))
                                    {
                                        Lu8RealTimePointFlag = 1; 
                                    }
                                    if(Lu8SameFreFlag)
                                    {
                                        Lu8ValidPointCnt = 2;
                                    }
                                    else
                                    {
                                        if((LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].enuSnsCh == PDC_SNS_CH_FLS_RLS)||\
                                            (LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].enuSnsCh == PDC_SNS_CH_FRS_RRS))
                                        {
                                            Lu8ValidPointCnt = 3;
                                        }
                                        else
                                        {
                                            Lu8ValidPointCnt = 2;
                                        }
                                    }
                                }
                            }
                            else
                            {
                                Lu8TwoPointNearFlag = JudegeClusterTwoPointNear(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].strObjCarCoor,LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].strObjCarCoor,LenuPointCloudArea);
                                if(Lu8TwoPointNearFlag)
                                {
                                    Lu8FindClusterFlag[Lu8ClusterNum] = 1;
                                    LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjX = LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].strObjCarCoor.fObjX+LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].strObjCarCoor.fObjX;
                                    LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjX = LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjX/2;
                                    
                                    LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjY = LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].strObjCarCoor.fObjY+LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].strObjCarCoor.fObjY;
                                    LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjY = LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjY/2;
                                    /* 定扫类型需要同样传递至Map层 */
                                    if(!LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].u8TypeAlredayBeUseFlag)
                                    {
                                        LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].u8TypeAlredayBeUseFlag = 1;
                                        SideCalClusterObjTypeCnt(&LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]],&LpstrClusterInf[Lu8ClusterNum],LenuPointCloudArea);
                                        if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].eMeasType == PDC_SNS_MEAS_STD)
                                        {
                                            Lu8StdPointCnt++;
                                            Lu32StdHeightSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].u16MasterHeight;
                                        }
                                        else
                                        {
                                            Lu8ChirpPointCnt++;
                                            Lu32ChirpHeightSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].u16MasterHeight;
                                        }
                                    }
                                    if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].eMeasType == PDC_SNS_MEAS_STD)
                                    {
                                        Lu8NoiseUseStdPointCnt++;
                                        Lu32StdMasterDis += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].u16MasterDis; 
                                        Lu32MasterDisSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].u16MasterDis;
                                    }
                                    else
                                    {
                                        Lu8NoiseUseChirpPointCnt++;
                                        Lu32MasterDisSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].u16MasterDis;
                                    }
                                    Lu8PointInClusterFlag[i] = 1;

                                    if(!LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u8TypeAlredayBeUseFlag)
                                    {
                                        LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u8TypeAlredayBeUseFlag = 1;
                                        SideCalClusterObjTypeCnt(&LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]],&LpstrClusterInf[Lu8ClusterNum],LenuPointCloudArea);
                                        if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].eMeasType == PDC_SNS_MEAS_STD)
                                        {
                                            Lu8StdPointCnt++;
                                            Lu32StdHeightSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterHeight;
                                        }
                                        else
                                        {
                                            Lu8ChirpPointCnt++;
                                            Lu32ChirpHeightSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterHeight;
                                        }
                                    }
                                    if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].eMeasType == PDC_SNS_MEAS_STD)
                                    {
                                        Lu8NoiseUseStdPointCnt++;
                                        Lu32StdMasterDis += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterDis;
                                        Lu32MasterDisSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterDis;
                                    }
                                    else
                                    {
                                        Lu8NoiseUseChirpPointCnt++;
                                        Lu32MasterDisSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterDis;
                                    }
                                    Lu8PointInClusterFlag[j] = 1;

                                    if((LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].u16ExistTime < REAL_TIME_MIN_VALUE)||(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16ExistTime < REAL_TIME_MIN_VALUE))
                                    {
                                        Lu8RealTimePointFlag = 1; 
                                    }
                                    if(Lu8SameFreFlag)
                                    {
                                        Lu8ValidPointCnt = 2;
                                    }
                                    else
                                    {
                                        if((LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].enuSnsCh == PDC_SNS_CH_FLS_RLS)||\
                                            (LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[i]].enuSnsCh == PDC_SNS_CH_FRS_RRS))
                                        {
                                            Lu8ValidPointCnt = 3;
                                        }
                                        else
                                        {
                                            Lu8ValidPointCnt = 2;
                                        }
                                    }
                                }
                            }
                        }
                        else
                        {
                            if(Lu8ClusterByLine)
                            {
                                Lu8TwoPointOnSameLineFlag = JudgeSideTwoPointNear(&LstrLineCoor,&LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].strObjCarCoor);
                                if(Lu8TwoPointOnSameLineFlag)
                                {
                                    if(LpstrPointCloudUnit[Lu8LineP1_Inx].strObjCarCoor.fObjX < LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].strObjCarCoor.fObjX)
                                    {
                                        Lu8LineP1_Inx = LstrAreaClusterBuf->u8PointUnitId[j];
                                    }
                                    
                                    if(LpstrPointCloudUnit[Lu8LineP2_Inx].strObjCarCoor.fObjX > LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].strObjCarCoor.fObjX)
                                    {
                                        Lu8LineP2_Inx = LstrAreaClusterBuf->u8PointUnitId[j];
                                    }
                                    LfLine_Y_Coor += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].strObjCarCoor.fObjY;
                                    LfLine_Y_Coor = LfLine_Y_Coor/2;
                                    LfLine_X_Coor = LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].strObjCarCoor.fObjX;
                                    LstrLineCoor.fObjX = LfLine_X_Coor;
                                    LstrLineCoor.fObjY = LfLine_Y_Coor;

                                    if(!LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u8TypeAlredayBeUseFlag)
                                    {
                                        LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u8TypeAlredayBeUseFlag = 1;
                                        SideCalClusterObjTypeCnt(&LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]],&LpstrClusterInf[Lu8ClusterNum],LenuPointCloudArea);
                                        if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].eMeasType == PDC_SNS_MEAS_STD)
                                        {
                                            Lu8StdPointCnt++;
                                            Lu32StdHeightSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterHeight;
                                        }
                                        else
                                        {
                                            Lu8ChirpPointCnt++;
                                            Lu32ChirpHeightSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterHeight;
                                        }
                                    }
                                    if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].eMeasType == PDC_SNS_MEAS_STD)
                                    {
                                        Lu8NoiseUseStdPointCnt++;
                                        Lu32StdMasterDis += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterDis;
                                        Lu32MasterDisSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterDis;
                                    }
                                    else
                                    {
                                        Lu8NoiseUseChirpPointCnt++;
                                        Lu32MasterDisSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterDis;
                                    }
                                    Lu8PointInClusterFlag[j] = 1;
                                    if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16ExistTime < REAL_TIME_MIN_VALUE)
                                    {
                                        Lu8RealTimePointFlag = 1; 
                                    }
                                    Lu8ValidPointCnt++;
                                }
                            }
                            else
                            {
                                Lu8TwoPointNearFlag = JudegeClusterTwoPointNear(LpstrClusterInf[Lu8ClusterNum].ClusterCoor,LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].strObjCarCoor,LenuPointCloudArea);
                                if(Lu8TwoPointNearFlag)
                                {
                                    LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjX += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].strObjCarCoor.fObjX;
                                    LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjX = LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjX/2;
                                    
                                    LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjY += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].strObjCarCoor.fObjY;
                                    LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjY = LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjY/2;

                                    if(!LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u8TypeAlredayBeUseFlag)
                                    {
                                        LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u8TypeAlredayBeUseFlag = 1;
                                        SideCalClusterObjTypeCnt(&LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]],&LpstrClusterInf[Lu8ClusterNum],LenuPointCloudArea);
                                        if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].eMeasType == PDC_SNS_MEAS_STD)
                                        {
                                            Lu8StdPointCnt++;
                                            Lu32StdHeightSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterHeight;
                                        }
                                        else
                                        {
                                            Lu8ChirpPointCnt++;
                                            Lu32ChirpHeightSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterHeight;
                                        }
                                    }
                                    if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].eMeasType == PDC_SNS_MEAS_STD)
                                    {
                                        Lu8NoiseUseStdPointCnt++;
                                        Lu32StdMasterDis += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterDis;
                                        Lu32MasterDisSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterDis;
                                    }
                                    else
                                    {
                                        Lu8NoiseUseChirpPointCnt++;
                                        Lu32MasterDisSum += LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16MasterDis;
                                    }
                                    Lu8PointInClusterFlag[j] = 1;
                                    if(LpstrPointCloudUnit[LstrAreaClusterBuf->u8PointUnitId[j]].u16ExistTime < REAL_TIME_MIN_VALUE)
                                    {
                                        Lu8RealTimePointFlag = 1; 
                                    }
                                    Lu8ValidPointCnt++;
                                }
                            }
                        }
                    }
                }
            }

            if(Lu8FindClusterFlag[Lu8ClusterNum])
            {
                if(Lu8ClusterByLine)
                {
                    if(Lu8StdPointCnt > 0)
                    {
                        LpstrClusterInf[Lu8ClusterNum].u16StdMasterHeight = Lu32StdHeightSum/Lu8StdPointCnt;
                    }
                    else
                    {
                        LpstrClusterInf[Lu8ClusterNum].u16StdMasterHeight = 0;
                    }
                    if(Lu8NoiseUseStdPointCnt > 0)
                    {
                        Lu32StdMasterDis = Lu32StdMasterDis/Lu8NoiseUseStdPointCnt;
                    }
                    else
                    {
                        Lu32StdMasterDis = 0;
                    }
                    
                    if(Lu8ChirpPointCnt > 0)
                    {
                        LpstrClusterInf[Lu8ClusterNum].u16ChirpMasterHeight = Lu32ChirpHeightSum/Lu8ChirpPointCnt;
                    }
                    else
                    {
                        LpstrClusterInf[Lu8ClusterNum].u16ChirpMasterHeight = 0;
                    }
                    if((Lu8NoiseUseStdPointCnt+Lu8NoiseUseChirpPointCnt) > 0)
                    {
                        LpstrClusterInf[Lu8ClusterNum].u16MasterDis = Lu32MasterDisSum/(Lu8NoiseUseStdPointCnt+Lu8NoiseUseChirpPointCnt);
                    }
                    else
                    {
                        LpstrClusterInf[Lu8ClusterNum].u16MasterDis = 0;
                    }
                    
                    /* 根据聚类坐标差值，判断输出Map类型 */
                    LfClusterCoorX_Sub = LpstrPointCloudUnit[Lu8LineP1_Inx].strObjCarCoor.fObjX - LpstrPointCloudUnit[Lu8LineP2_Inx].strObjCarCoor.fObjX;
#if 0
                    if(LfLine_Y_Coor > 0)
                    {
                        VS_PRINT("Left Side Map,Time:%.3f,CoorX_Sub:%.3f,P1_X:%.3f,P1_Y:%.3f,P2_X:%.3f,P2_Y:%.3f\r\n",GfMessageTime,\
                            LfClusterCoorX_Sub,LpstrPointUnit[Lu8LineP1_Inx].strObjCarCoor.fObjX,LpstrPointUnit[Lu8LineP1_Inx].strObjCarCoor.fObjY,\
                            LpstrPointUnit[Lu8LineP2_Inx].strObjCarCoor.fObjX,LpstrPointUnit[Lu8LineP2_Inx].strObjCarCoor.fObjY);
                    }
#endif
                    if(LfClusterCoorX_Sub > 100)
                    {
                        if(Gu16CarSpdForSnsUse < 800)
                        {
                            if((Lu32StdMasterDis > Lu16MinNoiseDis)&&(Lu32StdMasterDis < SAME_FREQ_NOISE_MAX_DIS))
                            {
                                if(Lu8NoiseUseChirpPointCnt == 0)
                                {
                                    Lu8NoiseFlag = 1;
                                }
                            }
                        }
#if 0
                        if((LenuGroup == 0x00)&&(LenuPointCloudArea == POINT_CLOUD_AREA0))
                        {
                            VS_PRINT("Time:%.3f,NoiseFlag:%d,RealTimePointFlag:%d,ChirpPointCnt:%d,StdPointCnt:%d\r\n",GfMessageTime,Lu8NoiseFlag,Lu8RealTimePointFlag,\
                                Lu8ChirpPointCnt,Lu8StdPointCnt);
                        }
#endif
                        if((Lu8NoiseFlag == 0)&&(Lu8RealTimePointFlag)&&(Lu8ValidPointCnt >= Lu8TotalPointLimit))
                        {
                            if(Lu8ClusterNum < (AREA_CLUSTER_NUM-1))
                            {
                                LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjX = LpstrPointCloudUnit[Lu8LineP1_Inx].strObjCarCoor.fObjX;
                                LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjY = LfLine_Y_Coor;
                                LpstrClusterInf[Lu8ClusterNum].u32ClusterTime = Lu32Time;
                                Lu8ClusterNum++;
                                /* 对于线状Map，此处是强制给拆分成2个点，因此类型计数不能被传递2次，此处P2点仅更新坐标、主发距离等信息 */
                                LpstrClusterInf[Lu8ClusterNum].ObjTypeCnt = GstrObjTypeCntInit;
                                LpstrClusterInf[Lu8ClusterNum].enuClusterType = LpstrClusterInf[Lu8ClusterNum-1].enuClusterType;
                                LpstrClusterInf[Lu8ClusterNum].u16MasterDis = LpstrClusterInf[Lu8ClusterNum-1].u16MasterDis;
                                LpstrClusterInf[Lu8ClusterNum].u16StdMasterHeight = LpstrClusterInf[Lu8ClusterNum-1].u16StdMasterHeight;
                                LpstrClusterInf[Lu8ClusterNum].u16ChirpMasterHeight = LpstrClusterInf[Lu8ClusterNum-1].u16ChirpMasterHeight;
                                LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjX = LpstrPointCloudUnit[Lu8LineP2_Inx].strObjCarCoor.fObjX;
                                LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjY = LfLine_Y_Coor;
                                LpstrClusterInf[Lu8ClusterNum].u32ClusterTime = Lu32Time;
                                Lu8ClusterNum++;
                            }
                            else
                            {
                                LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjX = LpstrPointCloudUnit[Lu8LineP1_Inx].strObjCarCoor.fObjX + LpstrPointCloudUnit[Lu8LineP2_Inx].strObjCarCoor.fObjX;
                                LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjX = LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjX/2;
                                LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjY = LfLine_Y_Coor;
                                LpstrClusterInf[Lu8ClusterNum].u32ClusterTime = Lu32Time;
                                Lu8ClusterNum++;
                            }
                        }
                    }
                    else
                    {
                        if(Gu16CarSpdForSnsUse < 800)
                        {
                            if((Lu32StdMasterDis > Lu16MinNoiseDis)&&(Lu32StdMasterDis < SAME_FREQ_NOISE_MAX_DIS))
                            {
                                if(Lu8NoiseUseChirpPointCnt == 0)
                                {
                                    Lu8NoiseFlag = 1;
                                }
                            }
                        }
                        
                        if((Lu8NoiseFlag == 0)&&(Lu8RealTimePointFlag)&&(Lu8ValidPointCnt >= Lu8TotalPointLimit))
                        {
                            LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjX = LpstrPointCloudUnit[Lu8LineP1_Inx].strObjCarCoor.fObjX + LpstrPointCloudUnit[Lu8LineP2_Inx].strObjCarCoor.fObjX;
                            LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjX = LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjX/2;
                            LpstrClusterInf[Lu8ClusterNum].ClusterCoor.fObjY = LfLine_Y_Coor;
                            LpstrClusterInf[Lu8ClusterNum].u32ClusterTime = Lu32Time;
                            Lu8ClusterNum++;
                        }
                    }
                }
                else
                {
                    if(Lu8StdPointCnt > 0)
                    {
                        LpstrClusterInf[Lu8ClusterNum].u16StdMasterHeight = Lu32StdHeightSum/Lu8StdPointCnt;
                    }
                    else
                    {
                        LpstrClusterInf[Lu8ClusterNum].u16StdMasterHeight = 0;
                    }
                    if(Lu8NoiseUseStdPointCnt > 0)
                    {
                        Lu32StdMasterDis = Lu32StdMasterDis/Lu8NoiseUseStdPointCnt;
                    }
                    else
                    {
                        Lu32StdMasterDis = 0;
                    }
                    if(Lu8ChirpPointCnt > 0)
                    {
                        LpstrClusterInf[Lu8ClusterNum].u16ChirpMasterHeight = Lu32ChirpHeightSum/Lu8ChirpPointCnt;
                    }
                    else
                    {
                        LpstrClusterInf[Lu8ClusterNum].u16ChirpMasterHeight = 0;
                    }
                    if((Lu8NoiseUseStdPointCnt+Lu8NoiseUseChirpPointCnt) > 0)
                    {
                        LpstrClusterInf[Lu8ClusterNum].u16MasterDis = Lu32MasterDisSum/(Lu8NoiseUseStdPointCnt+Lu8NoiseUseChirpPointCnt);
                    }
                    else
                    {
                        LpstrClusterInf[Lu8ClusterNum].u16MasterDis = 0;
                    }
                    LpstrClusterInf[Lu8ClusterNum].u32ClusterTime = Lu32Time;
                    
#if 0
                    VS_PRINT("Time:%.3f,StdMasterDis:%d,StdCnt:%d,ChirpCnt:%d\r\n",GfMessageTime,Lu32StdMasterDis,\
                        Lu8StdPointCnt,Lu8ChirpPointCnt);
#endif
                    if(Gu16CarSpdForSnsUse < 800)
                    {
                        if((Lu32StdMasterDis > Lu16MinNoiseDis)&&(Lu32StdMasterDis < SAME_FREQ_NOISE_MAX_DIS))
                        {
                            if(Lu8NoiseUseChirpPointCnt == 0)
                            {
                                Lu8NoiseFlag = 1;
                            }
                        }
                    }

                    if((Lu8NoiseFlag == 0)&&(Lu8RealTimePointFlag)&&(Lu8ValidPointCnt >= Lu8TotalPointLimit))
                    {
                        Lu8ClusterNum++;
                    }
                }
                
                if(Lu8ClusterNum == AREA_CLUSTER_NUM)
                {
                    break;
                }
                LpstrClusterInf[Lu8ClusterNum] = GstrClusterInit;
                Lu8FindClusterFlag[Lu8ClusterNum] = 0;
                Lu32StdHeightSum = 0;
                Lu32ChirpHeightSum = 0;
                Lu32MasterDisSum = 0;
                Lu8StdPointCnt = 0;
                Lu8ChirpPointCnt = 0;
                Lu8OneClusterPointCnt = 0;
                Lu32StdMasterDis = 0;
                Lu8NoiseFlag = 0;
                Lu8ClusterByLine = 0;
                Lu8RealTimePointFlag = 0;
                Lu8NoiseUseStdPointCnt = 0;
                Lu8NoiseUseChirpPointCnt = 0;
                Lu8ValidPointCnt = 0;
            }
        }

        for(i = 0; i < Lu8ClusterNum; i++)
        {
            //JudgeClusterType(&LpstrClusterInf[i]);
            LstrAreaClusterBuf->Cluster[i] = LpstrClusterInf[i];
#if 0
            if((LenuGroup == 0x00)&&(LenuPointCloudArea == POINT_CLOUD_AREA4))
            {
                VS_PRINT("FRS MessageTime:%.3f,i:%d,ClusterTime:%ld\r\n",GfMessageTime,i,LstrAreaClusterBuf->Cluster[i].u32ClusterTime);
            }
#endif
        }
    }

    return Lu8ClusterNum;
}


/******************************************************************************
 * 函数名称: AreaDataClassifyAndCluster
 * 
 * 功能描述: 区域点云的数据做初步分类(基于障碍物大小)，并拷贝至聚类缓存,再进行聚类输出
 * 
 * 输入参数: 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-21 15:20   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void AreaDataClassifyAndCluster(PDCSnsGroupType LenuGroup,PointCloudAreaType LenuPointCloudArea)
{
    SnsPointCloudBufType *LpstrPointCloudBuf;
    AreaClusterBufType    *LstrAreaClusterBuf;
    uint8 Lu8StartInx,Lu8EndInx;
    uint8 i,j;
    uint8 Lu8FindEndInx;
    uint8 Lu8NeedCluster = 0;
    uint32 Lu32NewPointTime = 0;
    
    LpstrPointCloudBuf = &GstrPointCloudBuf[LenuGroup][LenuPointCloudArea];
    LstrAreaClusterBuf = &GstrAreaClusterBuf;
    Lu8StartInx = LpstrPointCloudBuf->u8StartInx;
    Lu8EndInx = LpstrPointCloudBuf->u8EndInx;
    
    AreaClusterBufClear();
    if(Lu8StartInx != Lu8EndInx)
    {
        if(Lu8StartInx < Lu8EndInx)
        {
            Lu8FindEndInx = Lu8EndInx;
            for(i = Lu8StartInx; i < Lu8FindEndInx; i++)
            {
                if(LpstrPointCloudBuf->Buf[i].u8ValidFlag)
                {
                    if(LstrAreaClusterBuf->TotalPointCnt< AREA_CLUSTER_POINT_NUM)
                    {
                        LstrAreaClusterBuf->u8PointUnitId[LstrAreaClusterBuf->TotalPointCnt] = i;
                        if(LpstrPointCloudBuf->Buf[i].u16ExistTime == 0)
                        {
                            Lu8NeedCluster = 1;
                            Lu32NewPointTime = LpstrPointCloudBuf->Buf[i].u32DetSysTime;
                        }
                        LstrAreaClusterBuf->TotalPointCnt++;
                    }
                    else
                    {
                        break;
                    }
                }
            }
        }
        else
        {
            Lu8FindEndInx = POINT_CLOUD_BUF_NUM;
            for(i = Lu8StartInx; i < Lu8FindEndInx; i++)
            {
                if(LpstrPointCloudBuf->Buf[i].u8ValidFlag)
                {
                    if(LstrAreaClusterBuf->TotalPointCnt< AREA_CLUSTER_POINT_NUM)
                    {
                        LstrAreaClusterBuf->u8PointUnitId[LstrAreaClusterBuf->TotalPointCnt] = i;
                        if(LpstrPointCloudBuf->Buf[i].u16ExistTime == 0)
                        {
                            Lu8NeedCluster = 1;
                            Lu32NewPointTime = LpstrPointCloudBuf->Buf[i].u32DetSysTime;
                        }
                        LstrAreaClusterBuf->TotalPointCnt++;
                    }
                    else
                    {
                        break;
                    }
                }
            }
            if(Lu8EndInx > 0)
            {
                Lu8FindEndInx = Lu8EndInx;
                for(i = 0; i < Lu8FindEndInx; i++)
                {
                    if(LpstrPointCloudBuf->Buf[i].u8ValidFlag)
                    {
                        if(LstrAreaClusterBuf->TotalPointCnt< AREA_CLUSTER_POINT_NUM)
                        {
                            LstrAreaClusterBuf->u8PointUnitId[LstrAreaClusterBuf->TotalPointCnt] = i;
                            if(LpstrPointCloudBuf->Buf[i].u16ExistTime == 0)
                            {
                                Lu8NeedCluster = 1;
                                Lu32NewPointTime = LpstrPointCloudBuf->Buf[i].u32DetSysTime;
                            }
                            LstrAreaClusterBuf->TotalPointCnt++;
                        }
                        else
                        {
                            break;
                        }
                    }
                }
            }
        }
    }

    /* 对数据进行聚类，并输出对应聚类坐标 */
    if(Lu8NeedCluster)
    {
        if(LenuGroup == 0x00)
        {
            if((LenuPointCloudArea == POINT_CLOUD_AREA0)||(LenuPointCloudArea == POINT_CLOUD_AREA4))
            {
                LstrAreaClusterBuf->TotalClusterCnt = SidePointClusterHandle(LenuGroup,&LpstrPointCloudBuf->Buf[0],LstrAreaClusterBuf,LenuPointCloudArea,Lu32NewPointTime);
            }
            else
            {
                LstrAreaClusterBuf->TotalClusterCnt = PointClusterHandle(LenuGroup,&LpstrPointCloudBuf->Buf[0],LstrAreaClusterBuf,LenuPointCloudArea);
            }
        }
        else
        {
            if((LenuPointCloudArea == POINT_CLOUD_AREA0)||(LenuPointCloudArea == POINT_CLOUD_AREA4))
            {
                LstrAreaClusterBuf->TotalClusterCnt = SidePointClusterHandle(LenuGroup,&LpstrPointCloudBuf->Buf[0],LstrAreaClusterBuf,LenuPointCloudArea,Lu32NewPointTime);
            }
            else
            {
                LstrAreaClusterBuf->TotalClusterCnt = PointClusterHandle(LenuGroup,&LpstrPointCloudBuf->Buf[0],LstrAreaClusterBuf,LenuPointCloudArea);
            }
        }
    }
}



/******************************************************************************
 * 函数名称: UpdateAreaMapCarCoor
 * 
 * 功能描述: 更新区域Map的车辆坐标
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-22 10:27   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void UpdateAreaMapCarCoor(PDCSnsGroupType LenuGroup,PointCloudAreaType LenuPointCloudArea)
{
    SnsPointCloudBufType *LpstrPointCloudBuf;
    uint8 j;
    
    LpstrPointCloudBuf = &GstrPointCloudBuf[LenuGroup][LenuPointCloudArea];
    if(GstrSnsCarMovSts[LenuGroup][0].eCarDir != SNS_CAR_STOP)
    {
        for(j = 0; j < LpstrPointCloudBuf->u8AreaMapNum; j++)
        {
            if(LpstrPointCloudBuf->AreaMap[j].MapSharp == AREA_MAP_SHAPE_POINT)
            {
                PubAI_TransObjOdoCoorToCarCoor(&LpstrPointCloudBuf->AreaMap[j].P1_OdoCoor.fObjX,&LpstrPointCloudBuf->AreaMap[j].P1_OdoCoor.fObjY,\
                    &LpstrPointCloudBuf->AreaMap[j].P1_CarCoor.fObjX,&LpstrPointCloudBuf->AreaMap[j].P1_CarCoor.fObjY);
                LpstrPointCloudBuf->AreaMap[j].P2_CarCoor = LpstrPointCloudBuf->AreaMap[j].P1_CarCoor;
            }
            else
            {
                PubAI_TransObjOdoCoorToCarCoor(&LpstrPointCloudBuf->AreaMap[j].P1_OdoCoor.fObjX,&LpstrPointCloudBuf->AreaMap[j].P1_OdoCoor.fObjY,\
                    &LpstrPointCloudBuf->AreaMap[j].P1_CarCoor.fObjX,&LpstrPointCloudBuf->AreaMap[j].P1_CarCoor.fObjY);
                PubAI_TransObjOdoCoorToCarCoor(&LpstrPointCloudBuf->AreaMap[j].P2_OdoCoor.fObjX,&LpstrPointCloudBuf->AreaMap[j].P2_OdoCoor.fObjY,\
                    &LpstrPointCloudBuf->AreaMap[j].P2_CarCoor.fObjX,&LpstrPointCloudBuf->AreaMap[j].P2_CarCoor.fObjY);
            }
        }
    }
}


/******************************************************************************
 * 函数名称: JudgeSideAreaMapType
 * 
 * 功能描述: 判断侧边区域Map的类型
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-03-28 19:00   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void JudgeSideAreaMapType(SideAreaMapType *LpstrSideAreaMap)
{
#if 1
    uint8 Lu8Pvc_Cnt,Lu8BigWall_Cnt,Lu8LowCurb_Cnt,Lu8High_HighCurbCnt;
    uint8 Lu8MaxCnt = 0;
    uint8 Lu8MaxCntInx = 0;

    Lu8Pvc_Cnt = LpstrSideAreaMap->ObjTypeCnt.u8StdPVC_PointCnt + LpstrSideAreaMap->ObjTypeCnt.u8ChirpPVC_PointCnt;
    Lu8BigWall_Cnt = LpstrSideAreaMap->ObjTypeCnt.u8StdBigWall_PointCnt + LpstrSideAreaMap->ObjTypeCnt.u8ChirpBigWall_PointCnt;
    Lu8High_HighCurbCnt = LpstrSideAreaMap->ObjTypeCnt.u8StdHigh_HighCurb_PointCnt + LpstrSideAreaMap->ObjTypeCnt.u8ChirpHigh_HighCurb_PointCnt;
    Lu8LowCurb_Cnt = LpstrSideAreaMap->ObjTypeCnt.u8StdLowCurb_PointCnt + LpstrSideAreaMap->ObjTypeCnt.u8ChirpLowCurb_PointCnt;

    if(Lu8Pvc_Cnt >= Lu8MaxCnt)
    {
        Lu8MaxCntInx = 0;
        Lu8MaxCnt = Lu8Pvc_Cnt;
    }

    if(Lu8BigWall_Cnt >= Lu8MaxCnt)
    {
        Lu8MaxCntInx = 1;
        Lu8MaxCnt = Lu8BigWall_Cnt;
    }
    
    if(Lu8High_HighCurbCnt >= Lu8MaxCnt)
    {
        Lu8MaxCntInx = 2;
        Lu8MaxCnt = Lu8High_HighCurbCnt;
    }
    
    if(Lu8LowCurb_Cnt >= Lu8MaxCnt)
    {
        Lu8MaxCntInx = 3;
        Lu8MaxCnt = Lu8LowCurb_Cnt;
    }
    
    if(Lu8MaxCntInx == 0)
    {
        LpstrSideAreaMap->enuMapType = OBJ_PVC_PIPE_TYPE;
    }
    else if(Lu8MaxCntInx == 1)
    {
        LpstrSideAreaMap->enuMapType = OBJ_BIGWALL_TYPE;
    }
    else if(Lu8MaxCntInx == 2)
    {
        LpstrSideAreaMap->enuMapType = OBJ_HIGH_CURB_TYPE;
    }
    else
    {
        LpstrSideAreaMap->enuMapType = OBJ_LOW_CURB_TYPE;
    }
#endif

}



/******************************************************************************
 * 函数名称: SideAreaMapUpdate
 * 
 * 功能描述: 更新侧边区域Map
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-07 18:52   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SideAreaMapUpdate(SnsPointCloudBufType *LpstrPointCloudBuf,SideAreaMapType *LpstrSideAreaMap,SideSnsIndexType LeSideSnsInx)
{
    uint8 Lu8FusionLineFlag = 0;
    uint8 Lu8LeftId,Lu8RightId;
    uint8 Lu8BeFusionPointCnt;
    float LfPoint_Y_CoorSub;
    float LfMaxAbs_Y_Coor;
    uint8 i,j;
    uint16 Lu16FusionMasterDis = 0;
    uint32 Lu32FusionStdHeight = 0;
    uint32 Lu32FusionChirpHeight = 0;
    uint16 Lu16MinMasterDis;
    uint8 Lu8MinMasterDisInx;
    uint16 Lu16MasterDisSub;
    uint16 Lu16MasterDisSubLimit;
    uint16 Lu16MinAreaMasterDis = 65535;
    ObjTypeCntType LstrMapObjTypeCnt;
    
    LstrMapObjTypeCnt = GstrObjTypeCntInit;
    
    if(LeSideSnsInx == SIDE_SNS_FLS)
    {
        LpstrSideAreaMap->eSnsMovDir = GstrSnsCarMovSts[0][0].eCarDir;
    }
    else if(LeSideSnsInx == SIDE_SNS_FRS)
    {
        LpstrSideAreaMap->eSnsMovDir = GstrSnsCarMovSts[0][5].eCarDir;
    }
    else if(LeSideSnsInx == SIDE_SNS_RLS)
    {
        LpstrSideAreaMap->eSnsMovDir = GstrSnsCarMovSts[1][0].eCarDir;
    }
    else
    {
        LpstrSideAreaMap->eSnsMovDir = GstrSnsCarMovSts[1][5].eCarDir;
    }
    LpstrSideAreaMap->u8ValidObjCoorFlag = 0;
    
    if(LpstrPointCloudBuf->u8AreaMapNum > 1)
    {
        /* 首先查找最新区域Map中最小的主发距离 */
        for(i = 0; i < LpstrPointCloudBuf->u8AreaMapNum; i++)
        {
            if(Lu16MinAreaMasterDis > LpstrPointCloudBuf->AreaMap[i].u16MasterDis)
            {
                Lu16MinAreaMasterDis = LpstrPointCloudBuf->AreaMap[i].u16MasterDis;
            }
        }
        Lu8FusionLineFlag = 0;
        Lu8BeFusionPointCnt = 0;
        Lu16FusionMasterDis = 0;
        Lu32FusionStdHeight = 0;
        Lu32FusionChirpHeight = 0;
        for(i = 0; i < LpstrPointCloudBuf->u8AreaMapNum; i++)
        {
            for(j = i+1; j < LpstrPointCloudBuf->u8AreaMapNum; j++)
            {
                LfPoint_Y_CoorSub = ABS(LpstrPointCloudBuf->AreaMap[i].P1_CarCoor.fObjY,LpstrPointCloudBuf->AreaMap[j].P1_CarCoor.fObjY);
                Lu16MasterDisSub = ABS(LpstrPointCloudBuf->AreaMap[i].u16MasterDis,LpstrPointCloudBuf->AreaMap[j].u16MasterDis);
#if 0
                if((LeSideSnsInx == SIDE_SNS_RRS)&&(GfMessageTime > 35)&&(GfMessageTime < 40))
                {
                    VS_PRINT("Time:%.3f,RRS Area Map,MasterSub:%d,Y_CoorSub:%.3f,MasterDis:%d,%d\r\n",GfMessageTime,Lu16MasterDisSub,\
                        LfPoint_Y_CoorSub,LpstrPointCloudBuf->AreaMap[0].u16MasterDis,\
                        LpstrPointCloudBuf->AreaMap[1].u16MasterDis);
                }
#endif
                if(LeSideSnsInx < SIDE_SNS_RLS)
                {
                    /* 需要进一步细化，特别后侧雷达 */
                    if(LpstrPointCloudBuf->AreaMap[i].u16MasterDis < 500)
                    {
                        Lu16MasterDisSubLimit = 320;
                    }
                    else if(LpstrPointCloudBuf->AreaMap[i].u16MasterDis < 1000)
                    {
                        Lu16MasterDisSubLimit = 500;
                    }
                    else if(LpstrPointCloudBuf->AreaMap[i].u16MasterDis < 1500)
                    {
                        Lu16MasterDisSubLimit = 650;
                    }
                    else
                    {
                        Lu16MasterDisSubLimit = 800;
                    }
                }
                else
                {
                    if(LpstrPointCloudBuf->AreaMap[i].u16MasterDis < 500)
                    {
                        Lu16MasterDisSubLimit = 300;
                    }
                    else if(LpstrPointCloudBuf->AreaMap[i].u16MasterDis < 1000)
                    {
                        Lu16MasterDisSubLimit = 400;
                    }
                    else if(LpstrPointCloudBuf->AreaMap[i].u16MasterDis < 1500)
                    {
                        Lu16MasterDisSubLimit = 500;
                    }
                    else
                    {
                        Lu16MasterDisSubLimit = 700;
                    }
                }

                if(!Lu8FusionLineFlag)
                {
                    if((LfPoint_Y_CoorSub < 250)&&(Lu16MasterDisSub < Lu16MasterDisSubLimit))
                    {
                        Lu8FusionLineFlag = 1;
                        if(LpstrPointCloudBuf->AreaMap[i].P1_CarCoor.fObjX > LpstrPointCloudBuf->AreaMap[j].P1_CarCoor.fObjX)
                        {
                            Lu8LeftId = i;
                            Lu8RightId = j;
                        }
                        else
                        {
                            Lu8LeftId = j;
                            Lu8RightId = i;
                        }
                        if((LeSideSnsInx == SIDE_SNS_FLS)||(LeSideSnsInx == SIDE_SNS_RLS))
                        {
                            if(LpstrPointCloudBuf->AreaMap[i].P1_CarCoor.fObjY > LpstrPointCloudBuf->AreaMap[j].P1_CarCoor.fObjY)
                            {
                                LfMaxAbs_Y_Coor = LpstrPointCloudBuf->AreaMap[i].P1_CarCoor.fObjY;
                            }
                            else
                            {
                                LfMaxAbs_Y_Coor = LpstrPointCloudBuf->AreaMap[j].P1_CarCoor.fObjY;
                            }
                        }
                        else
                        {
                            if(LpstrPointCloudBuf->AreaMap[i].P1_CarCoor.fObjY < LpstrPointCloudBuf->AreaMap[j].P1_CarCoor.fObjY)
                            {
                                LfMaxAbs_Y_Coor = LpstrPointCloudBuf->AreaMap[i].P1_CarCoor.fObjY;
                            }
                            else
                            {
                                LfMaxAbs_Y_Coor = LpstrPointCloudBuf->AreaMap[j].P1_CarCoor.fObjY;
                            }
                        }
                        Lu8BeFusionPointCnt = 2;
                        Lu16FusionMasterDis = LpstrPointCloudBuf->AreaMap[i].u16MasterDis + LpstrPointCloudBuf->AreaMap[j].u16MasterDis;
                        Lu16FusionMasterDis = Lu16FusionMasterDis>>1;

                        Lu32FusionStdHeight = LpstrPointCloudBuf->AreaMap[i].u16StdMasterHeight + LpstrPointCloudBuf->AreaMap[j].u16StdMasterHeight;
                        Lu32FusionStdHeight = Lu32FusionStdHeight>>1;
                        
                        Lu32FusionChirpHeight = LpstrPointCloudBuf->AreaMap[i].u16ChirpMasterHeight + LpstrPointCloudBuf->AreaMap[j].u16ChirpMasterHeight;
                        Lu32FusionChirpHeight = Lu32FusionChirpHeight>>1;

                        
                        LstrMapObjTypeCnt = LpstrPointCloudBuf->AreaMap[i].ObjTypeCnt;
                        LstrMapObjTypeCnt.u8StdPVC_PointCnt += LpstrPointCloudBuf->AreaMap[j].ObjTypeCnt.u8StdPVC_PointCnt;
                        LstrMapObjTypeCnt.u8ChirpPVC_PointCnt += LpstrPointCloudBuf->AreaMap[j].ObjTypeCnt.u8ChirpPVC_PointCnt;
                        LstrMapObjTypeCnt.u8StdLowCurb_PointCnt += LpstrPointCloudBuf->AreaMap[j].ObjTypeCnt.u8StdLowCurb_PointCnt;
                        LstrMapObjTypeCnt.u8ChirpLowCurb_PointCnt += LpstrPointCloudBuf->AreaMap[j].ObjTypeCnt.u8ChirpLowCurb_PointCnt;
                        LstrMapObjTypeCnt.u8StdBigWall_PointCnt += LpstrPointCloudBuf->AreaMap[j].ObjTypeCnt.u8StdBigWall_PointCnt;
                        LstrMapObjTypeCnt.u8ChirpBigWall_PointCnt += LpstrPointCloudBuf->AreaMap[j].ObjTypeCnt.u8ChirpBigWall_PointCnt;

                        LstrMapObjTypeCnt.u8StdHigh_HighCurb_PointCnt += LpstrPointCloudBuf->AreaMap[j].ObjTypeCnt.u8StdHigh_HighCurb_PointCnt;
                        LstrMapObjTypeCnt.u8ChirpHigh_HighCurb_PointCnt += LpstrPointCloudBuf->AreaMap[j].ObjTypeCnt.u8ChirpHigh_HighCurb_PointCnt;
                        LstrMapObjTypeCnt.u8StdTotalPointCnt += LpstrPointCloudBuf->AreaMap[j].ObjTypeCnt.u8StdTotalPointCnt;
                        LstrMapObjTypeCnt.u8ChirpTotalPointCnt += LpstrPointCloudBuf->AreaMap[j].ObjTypeCnt.u8ChirpTotalPointCnt;
                    }
                }
                else
                {
                    if((LfPoint_Y_CoorSub < 250)&&(Lu16MasterDisSub < Lu16MasterDisSubLimit))
                    {
                        if(LpstrPointCloudBuf->AreaMap[Lu8LeftId].P1_CarCoor.fObjX < LpstrPointCloudBuf->AreaMap[j].P1_CarCoor.fObjX)
                        {
                            Lu8LeftId = j;
                        }
                        if(LpstrPointCloudBuf->AreaMap[Lu8RightId].P1_CarCoor.fObjX > LpstrPointCloudBuf->AreaMap[j].P1_CarCoor.fObjX)
                        {
                            Lu8RightId = j;
                        }
                        if((LeSideSnsInx == SIDE_SNS_FLS)||(LeSideSnsInx == SIDE_SNS_RLS))
                        {
                            if(LpstrPointCloudBuf->AreaMap[j].P1_CarCoor.fObjY > LfMaxAbs_Y_Coor)
                            {
                                LfMaxAbs_Y_Coor = LpstrPointCloudBuf->AreaMap[j].P1_CarCoor.fObjY;
                            }
                        }
                        else
                        {
                            if(LpstrPointCloudBuf->AreaMap[j].P1_CarCoor.fObjY < LfMaxAbs_Y_Coor)
                            {
                                LfMaxAbs_Y_Coor = LpstrPointCloudBuf->AreaMap[j].P1_CarCoor.fObjY;
                            }
                        }
                        Lu8BeFusionPointCnt++;
                        Lu16FusionMasterDis += LpstrPointCloudBuf->AreaMap[j].u16MasterDis;
                        Lu16FusionMasterDis = Lu16FusionMasterDis >> 1;

                        Lu32FusionStdHeight += LpstrPointCloudBuf->AreaMap[j].u16StdMasterHeight;
                        Lu32FusionStdHeight = Lu32FusionStdHeight >> 1;

                        Lu32FusionChirpHeight += LpstrPointCloudBuf->AreaMap[j].u16ChirpMasterHeight;
                        Lu32FusionChirpHeight = Lu32FusionChirpHeight >> 1;

                        
                        LstrMapObjTypeCnt.u8StdPVC_PointCnt += LpstrPointCloudBuf->AreaMap[j].ObjTypeCnt.u8StdPVC_PointCnt;
                        LstrMapObjTypeCnt.u8ChirpPVC_PointCnt += LpstrPointCloudBuf->AreaMap[j].ObjTypeCnt.u8ChirpPVC_PointCnt;
                        LstrMapObjTypeCnt.u8StdLowCurb_PointCnt += LpstrPointCloudBuf->AreaMap[j].ObjTypeCnt.u8StdLowCurb_PointCnt;
                        LstrMapObjTypeCnt.u8ChirpLowCurb_PointCnt += LpstrPointCloudBuf->AreaMap[j].ObjTypeCnt.u8ChirpLowCurb_PointCnt;
                        LstrMapObjTypeCnt.u8StdBigWall_PointCnt += LpstrPointCloudBuf->AreaMap[j].ObjTypeCnt.u8StdBigWall_PointCnt;
                        LstrMapObjTypeCnt.u8ChirpBigWall_PointCnt += LpstrPointCloudBuf->AreaMap[j].ObjTypeCnt.u8ChirpBigWall_PointCnt;

                        LstrMapObjTypeCnt.u8StdHigh_HighCurb_PointCnt += LpstrPointCloudBuf->AreaMap[j].ObjTypeCnt.u8StdHigh_HighCurb_PointCnt;
                        LstrMapObjTypeCnt.u8ChirpHigh_HighCurb_PointCnt += LpstrPointCloudBuf->AreaMap[j].ObjTypeCnt.u8ChirpHigh_HighCurb_PointCnt;
                        LstrMapObjTypeCnt.u8StdTotalPointCnt += LpstrPointCloudBuf->AreaMap[j].ObjTypeCnt.u8StdTotalPointCnt;
                        LstrMapObjTypeCnt.u8ChirpTotalPointCnt += LpstrPointCloudBuf->AreaMap[j].ObjTypeCnt.u8ChirpTotalPointCnt;
                    }
                }
            }
            if(Lu8FusionLineFlag)
            {
                if(Lu16FusionMasterDis > (Lu16MinAreaMasterDis +250))
                {
                    Lu8FusionLineFlag = 0;
                }
                break;
            }
        }

        if(Lu8FusionLineFlag)
        {
            SnsAreaMapType LstrSnsAreaMap;
#if 1
            LstrSnsAreaMap.P1_CarCoor.fObjX = LpstrPointCloudBuf->AreaMap[Lu8LeftId].P1_CarCoor.fObjX;
            LstrSnsAreaMap.P1_CarCoor.fObjY = LpstrPointCloudBuf->AreaMap[Lu8LeftId].P1_CarCoor.fObjY;
            LstrSnsAreaMap.P2_CarCoor.fObjX = LpstrPointCloudBuf->AreaMap[Lu8RightId].P1_CarCoor.fObjX;
            LstrSnsAreaMap.P2_CarCoor.fObjY = LpstrPointCloudBuf->AreaMap[Lu8RightId].P1_CarCoor.fObjY;
#else
            LstrSnsAreaMap.P1_CarCoor.fObjX = LpstrPointCloudBuf->AreaMap[Lu8LeftId].P1_CarCoor.fObjX;
            LstrSnsAreaMap.P1_CarCoor.fObjY = LfMaxAbs_Y_Coor;
            LstrSnsAreaMap.P2_CarCoor.fObjX = LpstrPointCloudBuf->AreaMap[Lu8RightId].P1_CarCoor.fObjX;
            LstrSnsAreaMap.P2_CarCoor.fObjY = LfMaxAbs_Y_Coor;
#endif
            LstrSnsAreaMap.MapSharp = AREA_MAP_SHAPE_LINE;
            LstrSnsAreaMap.MapId = 0;
            LstrSnsAreaMap.enuMapType = OBJ_BIGWALL_TYPE;
            LstrSnsAreaMap.u8BeFusionToRealMapFlag = 0;
            LstrSnsAreaMap.ObjTypeCnt = LstrMapObjTypeCnt;
            
            PubAI_TransObjCarCoorToOdoCoor(&LstrSnsAreaMap.P1_CarCoor.fObjX,&LstrSnsAreaMap.P1_CarCoor.fObjY,\
                &LstrSnsAreaMap.P1_OdoCoor.fObjX,&LstrSnsAreaMap.P1_OdoCoor.fObjY);
            PubAI_TransObjCarCoorToOdoCoor(&LstrSnsAreaMap.P2_CarCoor.fObjX,&LstrSnsAreaMap.P2_CarCoor.fObjY,\
                &LstrSnsAreaMap.P2_OdoCoor.fObjX,&LstrSnsAreaMap.P2_OdoCoor.fObjY);

            LpstrSideAreaMap->u8ValidObjCoorFlag = 1;
            LpstrSideAreaMap->u16ValidMasterDis = Lu16FusionMasterDis;
            LpstrSideAreaMap->u16MasterStdHeight = Lu32FusionStdHeight;
            LpstrSideAreaMap->u16MasterChirpHeight = Lu32FusionChirpHeight;
            
            LpstrSideAreaMap->P1_CarCoor = LstrSnsAreaMap.P1_CarCoor;
            LpstrSideAreaMap->P2_CarCoor = LstrSnsAreaMap.P2_CarCoor;
            LpstrSideAreaMap->P1_OdoCoor = LstrSnsAreaMap.P1_OdoCoor;
            LpstrSideAreaMap->P2_OdoCoor = LstrSnsAreaMap.P2_OdoCoor;
            LpstrSideAreaMap->MapSharp = LstrSnsAreaMap.MapSharp;
            LpstrSideAreaMap->ObjTypeCnt = LstrMapObjTypeCnt;
            LpstrSideAreaMap->u32SideUpdateTime = LpstrPointCloudBuf->AreaMap[Lu8LeftId].u32MapTime;
            /* 用于Excel显示 */
            LpstrPointCloudBuf->AreaMap[0] = LstrSnsAreaMap;
            JudgeSideAreaMapType(LpstrSideAreaMap);
#if 0
            VS_PRINT("Fusion Line OK,Time:%.3f,Left_X:%.3f,Left_Y:%.3f,Right_X:%.3f,Right_Y:%.3f,LeftId:%d,RightId:%d,Num:%d\r\n",GfMessageTime,\
                LpstrPointCloudBuf->AreaMap[Lu8LeftId].P1_CarCoor.fObjX,LfMaxAbs_Y_Coor,\
                LpstrPointCloudBuf->AreaMap[Lu8RightId].P1_CarCoor.fObjX,LfMaxAbs_Y_Coor,\
                Lu8LeftId,Lu8RightId,LpstrPointCloudBuf->u8AreaMapNum);
#endif  
            //LpstrPointCloudBuf->u8AreaMapNum = 0;
        }
        else
        {
            /* 未匹配成功，查询最小主发距离的点 */
            Lu16MinMasterDis = LpstrPointCloudBuf->AreaMap[0].u16MasterDis;
            Lu8MinMasterDisInx = 0;
            for(i = 1; i < LpstrPointCloudBuf->u8AreaMapNum; i++)
            {
                if(Lu16MinMasterDis > LpstrPointCloudBuf->AreaMap[i].u16MasterDis)
                {
                    Lu16MinMasterDis = LpstrPointCloudBuf->AreaMap[i].u16MasterDis;
                    Lu8MinMasterDisInx = i;
                }
            }

            LpstrSideAreaMap->u8ValidObjCoorFlag = 1;
            LpstrSideAreaMap->u16ValidMasterDis = Lu16MinMasterDis;
            LpstrSideAreaMap->u16MasterStdHeight = LpstrPointCloudBuf->AreaMap[Lu8MinMasterDisInx].u16StdMasterHeight;
            LpstrSideAreaMap->u16MasterChirpHeight = LpstrPointCloudBuf->AreaMap[Lu8MinMasterDisInx].u16ChirpMasterHeight;
            LpstrSideAreaMap->P1_CarCoor = LpstrPointCloudBuf->AreaMap[Lu8MinMasterDisInx].P1_CarCoor;
            LpstrSideAreaMap->P2_CarCoor = LpstrPointCloudBuf->AreaMap[Lu8MinMasterDisInx].P2_CarCoor;
            LpstrSideAreaMap->P1_OdoCoor = LpstrPointCloudBuf->AreaMap[Lu8MinMasterDisInx].P1_OdoCoor;
            LpstrSideAreaMap->P2_OdoCoor = LpstrPointCloudBuf->AreaMap[Lu8MinMasterDisInx].P2_OdoCoor;
            LpstrSideAreaMap->MapSharp = LpstrPointCloudBuf->AreaMap[Lu8MinMasterDisInx].MapSharp;
            LpstrSideAreaMap->ObjTypeCnt = LpstrPointCloudBuf->AreaMap[Lu8MinMasterDisInx].ObjTypeCnt;
            LpstrSideAreaMap->u32SideUpdateTime = LpstrPointCloudBuf->AreaMap[Lu8MinMasterDisInx].u32MapTime;
            JudgeSideAreaMapType(LpstrSideAreaMap);
            //LpstrPointCloudBuf->u8AreaMapNum = 0;
        }
    }
    else
    {
        if(LpstrPointCloudBuf->u8AreaMapNum)
        {
            i = 0;
            LpstrSideAreaMap->u8ValidObjCoorFlag = 1;
            LpstrSideAreaMap->u16ValidMasterDis = LpstrPointCloudBuf->AreaMap[i].u16MasterDis;
            LpstrSideAreaMap->u16MasterStdHeight = LpstrPointCloudBuf->AreaMap[i].u16StdMasterHeight;
            LpstrSideAreaMap->u16MasterChirpHeight = LpstrPointCloudBuf->AreaMap[i].u16ChirpMasterHeight;
            LpstrSideAreaMap->P1_CarCoor = LpstrPointCloudBuf->AreaMap[i].P1_CarCoor;
            LpstrSideAreaMap->P2_CarCoor = LpstrPointCloudBuf->AreaMap[i].P2_CarCoor;
            LpstrSideAreaMap->P1_OdoCoor = LpstrPointCloudBuf->AreaMap[i].P1_OdoCoor;
            LpstrSideAreaMap->P2_OdoCoor = LpstrPointCloudBuf->AreaMap[i].P2_OdoCoor;
            LpstrSideAreaMap->MapSharp = AREA_MAP_SHAPE_POINT; 
            LpstrSideAreaMap->ObjTypeCnt = LpstrPointCloudBuf->AreaMap[i].ObjTypeCnt;
            LpstrSideAreaMap->u32SideUpdateTime = LpstrPointCloudBuf->AreaMap[i].u32MapTime;
            JudgeSideAreaMapType(LpstrSideAreaMap);
            LpstrPointCloudBuf->AreaMap[i].u8BeFusionToRealMapFlag = 1;
            //LpstrPointCloudBuf->u8AreaMapNum = 0;
        }
        else
        {
            LpstrSideAreaMap->u8ValidObjCoorFlag = 0;
        }
    }
    LpstrSideAreaMap->u8NewObjCoorFlag = LpstrSideAreaMap->u8ValidObjCoorFlag;
 
#if 0
    if((LeSideSnsInx == SIDE_SNS_RRS)&&(GfMessageTime > 40)&&(GfMessageTime < 44))
    {
        //if((LpstrSideAreaMap->u8NewObjCoorFlag)&&(GstrMapSnsRealTimeDis[1][5].u8RealDisUpdateFlag))
        //if(GstrMapSnsRealTimeDis[1][5].u8RealDisUpdateFlag)
        if(1)
        {
            VS_PRINT("RRS Data,Time:%.3f,MapSharp:%d,P1_X:%.3f,P1_Y:%.3f,P2_X:%.3f,P2_Y:%.3f,NearExistMapFlag:%d,ValidMasterDis:%d,AreaMapNum:%d,UpdateFlag:%d\r\n",GfMessageTime,\
                LpstrSideAreaMap->MapSharp,LpstrSideAreaMap->P1_CarCoor.fObjX,LpstrSideAreaMap->P1_CarCoor.fObjY,\
                LpstrSideAreaMap->P2_CarCoor.fObjX,LpstrSideAreaMap->P2_CarCoor.fObjY,LpstrSideAreaMap->u8NearExistMapFlag,\
                LpstrSideAreaMap->u16ValidMasterDis,LpstrPointCloudBuf->u8AreaMapNum,GstrMapSnsRealTimeDis[1][5].u8RealDisUpdateFlag);
        }
    }
#endif
    #if VS_SIMULATE_ENABLE
    /* 正常输出到Excel，但不构建前后区域Map */
    for(i = 0; i < LpstrPointCloudBuf->u8AreaMapNum; i++)
    {
        LpstrPointCloudBuf->AreaMap[i].u8BeFusionToRealMapFlag = 1;
    }
    #else
    LpstrPointCloudBuf->u8AreaMapNum = 0;
    #endif

    /* 首先判断区域Map是否存在 */
    if(LpstrSideAreaMap->u8NearExistMapFlag)
    {
        LpstrSideAreaMap->u8ValidObjCoorFlag = 0;
    }
}


/******************************************************************************
 * 函数名称: PointCloudClusterAndAreaMapUpdate
 * 
 * 功能描述: 区域点云聚类，并创建、更新区域Map
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-22 10:11   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PointCloudClusterAndAreaMapUpdate(PDCSnsGroupType LenuGroup,PointCloudAreaType LenuPointCloudArea)
{
    SnsPointCloudBufType *LpstrPointCloudBuf;
    AreaClusterBufType    *LstrAreaClusterBuf;
    uint8 i,j;
    uint8 Lu8MapId = 0;
    float LfArea0_Y_Line_limit,LfArea4_Y_Line_limit;
    
    LpstrPointCloudBuf = &GstrPointCloudBuf[LenuGroup][LenuPointCloudArea];
    LstrAreaClusterBuf = &GstrAreaClusterBuf;
#if 0
    LfArea0_Y_Line_limit = GstrRamSnsCoor[LenuGroup][PDC_SNS_CH_FLS].cRadarY;
    LfArea4_Y_Line_limit = GstrRamSnsCoor[LenuGroup][PDC_SNS_CH_FRS].cRadarY;
#else
    

    LfArea0_Y_Line_limit = GfPointCloudArea_X_Coor_Ram[LenuGroup][0]+GstrParkingGuidenceData.fSummonSideDis;
    LfArea4_Y_Line_limit = GfPointCloudArea_X_Coor_Ram[LenuGroup][3]-GstrParkingGuidenceData.fSummonSideDis;
#endif

    SideAreaMapType *LpstrSideAreaMap;
    uint8 Lu8SideMapUpdateFlag = 0;
    SideSnsIndexType LeSideSnsInx;
    uint8 Lu8AreaMapWithSideSnsCnt;
    
    LpstrSideAreaMap = &GstrSideAreaMap[SIDE_SNS_FLS];
    if(LenuGroup == PDC_SNS_GROUP_FRONT)
    {
        if(LenuPointCloudArea == POINT_CLOUD_AREA0)
        {
            LpstrSideAreaMap = &GstrSideAreaMap[SIDE_SNS_FLS];
            LeSideSnsInx = SIDE_SNS_FLS;
            LpstrSideAreaMap->eSnsMovDir = GstrSnsCarMovSts[0][0].eCarDir;
            if(LpstrSideAreaMap->eSnsMovDir == SNS_CAR_FORWARD)
            {
                Lu8SideMapUpdateFlag = 1;
            }
            else
            {
                Lu8SideMapUpdateFlag = 0;
            }
        }
        else if(LenuPointCloudArea == POINT_CLOUD_AREA4)
        {
            LpstrSideAreaMap = &GstrSideAreaMap[SIDE_SNS_FRS];
            LeSideSnsInx = SIDE_SNS_FRS;
            LpstrSideAreaMap->eSnsMovDir = GstrSnsCarMovSts[0][5].eCarDir;
            if(LpstrSideAreaMap->eSnsMovDir == SNS_CAR_FORWARD)
            {
                Lu8SideMapUpdateFlag = 1;
            }
            else
            {
                Lu8SideMapUpdateFlag = 0;
            }
        }
    }
    else
    {
        if(LenuPointCloudArea == POINT_CLOUD_AREA0)
        {
            LpstrSideAreaMap = &GstrSideAreaMap[SIDE_SNS_RLS];
            LeSideSnsInx = SIDE_SNS_RLS;
            LpstrSideAreaMap->eSnsMovDir = GstrSnsCarMovSts[1][0].eCarDir;
            //if(LpstrSideAreaMap->eSnsMovDir == SNS_CAR_BACKWARD)
            if(LpstrSideAreaMap->eSnsMovDir != SNS_CAR_STOP)
            {
                Lu8SideMapUpdateFlag = 1;
            }
            else
            {
                Lu8SideMapUpdateFlag = 0;
            }
        }
        else if(LenuPointCloudArea == POINT_CLOUD_AREA4)
        {
            LpstrSideAreaMap = &GstrSideAreaMap[SIDE_SNS_RRS];
            LeSideSnsInx = SIDE_SNS_RRS;
            LpstrSideAreaMap->eSnsMovDir = GstrSnsCarMovSts[1][5].eCarDir;
            //if(LpstrSideAreaMap->eSnsMovDir == SNS_CAR_BACKWARD)
            if(LpstrSideAreaMap->eSnsMovDir != SNS_CAR_STOP)
            {
                Lu8SideMapUpdateFlag = 1;
            }
            else
            {
                Lu8SideMapUpdateFlag = 0;
            }
        }
    }
    
    AreaDataClassifyAndCluster(LenuGroup,LenuPointCloudArea);

    /* 创建实时Map */
    LpstrPointCloudBuf->u8AreaMapNum = 0;
    Lu8AreaMapWithSideSnsCnt = 0;
    for(i = 0; i < LstrAreaClusterBuf->TotalClusterCnt; i++)
    {
        if(LpstrPointCloudBuf->u8AreaMapNum  < MAX_AREA_MAP_NUM)
        {
            Lu8MapId = LpstrPointCloudBuf->u8AreaMapNum;
            LpstrPointCloudBuf->AreaMap[Lu8MapId].MapId = Lu8MapId;
            LpstrPointCloudBuf->AreaMap[Lu8MapId].MapSharp = AREA_MAP_SHAPE_POINT;
            LpstrPointCloudBuf->AreaMap[Lu8MapId].enuMapType = LstrAreaClusterBuf->Cluster[i].enuClusterType;
            LpstrPointCloudBuf->AreaMap[Lu8MapId].u8BeFusionToRealMapFlag = 0;
            LpstrPointCloudBuf->AreaMap[Lu8MapId].P1_CarCoor = LstrAreaClusterBuf->Cluster[i].ClusterCoor;
            LpstrPointCloudBuf->AreaMap[Lu8MapId].P2_CarCoor = LstrAreaClusterBuf->Cluster[i].ClusterCoor;

            LpstrPointCloudBuf->AreaMap[Lu8MapId].u16MasterDis = LstrAreaClusterBuf->Cluster[i].u16MasterDis;
            LpstrPointCloudBuf->AreaMap[Lu8MapId].u16StdMasterHeight = LstrAreaClusterBuf->Cluster[i].u16StdMasterHeight;
            LpstrPointCloudBuf->AreaMap[Lu8MapId].u16ChirpMasterHeight = LstrAreaClusterBuf->Cluster[i].u16ChirpMasterHeight;

            LpstrPointCloudBuf->AreaMap[Lu8MapId].ObjTypeCnt = LstrAreaClusterBuf->Cluster[i].ObjTypeCnt;
            PubAI_TransObjCarCoorToOdoCoor(&LpstrPointCloudBuf->AreaMap[Lu8MapId].P1_CarCoor.fObjX,&LpstrPointCloudBuf->AreaMap[Lu8MapId].P1_CarCoor.fObjY,\
                &LpstrPointCloudBuf->AreaMap[Lu8MapId].P1_OdoCoor.fObjX,&LpstrPointCloudBuf->AreaMap[Lu8MapId].P1_OdoCoor.fObjY);
            LpstrPointCloudBuf->AreaMap[Lu8MapId].P2_OdoCoor = LpstrPointCloudBuf->AreaMap[Lu8MapId].P1_OdoCoor;
            LpstrPointCloudBuf->AreaMap[Lu8MapId].enuSideMapSns = SIDE_SNS_NONE;
            LpstrPointCloudBuf->AreaMap[Lu8MapId].u32MapTime = LstrAreaClusterBuf->Cluster[i].u32ClusterTime;
#if 0
            if(LenuGroup == PDC_SNS_GROUP_FRONT)
            {
                if(LpstrPointCloudBuf->AreaMap[Lu8MapId].P1_CarCoor.fObjX < (GstrRamSnsCoor[0][0].cRadarX+FRONT_SIDE_SNS_CLUSTER_LINE_DIS))
                {
                    Lu8AreaMapWithSideSnsCnt++;
                }
            }
            else
            {
                if(LpstrPointCloudBuf->AreaMap[Lu8MapId].P1_CarCoor.fObjX > (GstrRamSnsCoor[1][0].cRadarX-REAR_SIDE_SNS_CLUSTER_LINE_DIS))
                {
                    Lu8AreaMapWithSideSnsCnt++;
                }
            }
#endif
            
#if 1
            if(GstrSnsCarMovSts[LenuGroup][2].eCarDir != SNS_CAR_STOP)
            {
                if(LenuPointCloudArea == POINT_CLOUD_AREA0)
                {
                    if(LpstrPointCloudBuf->AreaMap[Lu8MapId].P1_CarCoor.fObjY > LfArea0_Y_Line_limit)
                    {
                        if(LenuGroup == PDC_SNS_GROUP_FRONT)
                        {
                            if(GstrSnsCarMovSts[LenuGroup][0].eCarDir == SNS_CAR_FORWARD)
                            {
                                LpstrPointCloudBuf->AreaMap[Lu8MapId].enuSideMapSns = SIDE_SNS_FLS;
                            }
                            else
                            {
                                LpstrPointCloudBuf->AreaMap[Lu8MapId].u8BeFusionToRealMapFlag = 1;/* 强制在后退时前侧雷达不画Map */
                            }
                        }
                        else
                        {
                            if(GstrSnsCarMovSts[LenuGroup][0].eCarDir == SNS_CAR_BACKWARD)
                            {
                                LpstrPointCloudBuf->AreaMap[Lu8MapId].enuSideMapSns = SIDE_SNS_RLS;
                            }
                            else if(GstrSnsCarMovSts[LenuGroup][0].eCarDir == SNS_CAR_FORWARD)
                            {
                                if(GstrParkingGuidenceData.fCarAngleSub > REAR_GUIDANCE_MAP_ANGLE)
                                {
                                    if(LpstrPointCloudBuf->AreaMap[Lu8MapId].u16MasterDis < REAR_GUIDANCE_MAP_DIS)
                                    {
                                        LpstrPointCloudBuf->AreaMap[Lu8MapId].enuSideMapSns = SIDE_SNS_RLS;
                                    }
                                    else
                                    {
                                        LpstrPointCloudBuf->AreaMap[Lu8MapId].u8BeFusionToRealMapFlag = 1;/* 强制在后退时后侧雷达不画Map */
                                    }
                                }
                                else
                                {
                                    LpstrPointCloudBuf->AreaMap[Lu8MapId].u8BeFusionToRealMapFlag = 1;/* 强制在后退时后侧雷达不画Map */
                                }
                            }
                            else
                            {
                                LpstrPointCloudBuf->AreaMap[Lu8MapId].u8BeFusionToRealMapFlag = 1;/* 强制在后退时后侧雷达不画Map */
                            }
                        }
                    }
                }
                else if(LenuPointCloudArea == POINT_CLOUD_AREA4)
                {
                    if(LpstrPointCloudBuf->AreaMap[Lu8MapId].P1_CarCoor.fObjY < LfArea4_Y_Line_limit)
                    {
                        if(LenuGroup == PDC_SNS_GROUP_FRONT)
                        {
                            if(GstrSnsCarMovSts[LenuGroup][5].eCarDir == SNS_CAR_FORWARD)
                            {
                                LpstrPointCloudBuf->AreaMap[Lu8MapId].enuSideMapSns = SIDE_SNS_FRS;
                            }
                            else
                            {
                                LpstrPointCloudBuf->AreaMap[Lu8MapId].u8BeFusionToRealMapFlag = 1;/* 强制在后退时前侧雷达不画Map */
                            }
                        }
                        else
                        {
                            if(GstrSnsCarMovSts[LenuGroup][5].eCarDir == SNS_CAR_BACKWARD)
                            {
                                LpstrPointCloudBuf->AreaMap[Lu8MapId].enuSideMapSns = SIDE_SNS_RRS;
                            }
                            else if(GstrSnsCarMovSts[LenuGroup][5].eCarDir == SNS_CAR_FORWARD)
                            {
                                if(GstrParkingGuidenceData.fCarAngleSub > REAR_GUIDANCE_MAP_ANGLE)
                                {
                                    if(LpstrPointCloudBuf->AreaMap[Lu8MapId].u16MasterDis < REAR_GUIDANCE_MAP_DIS)
                                    {
                                        LpstrPointCloudBuf->AreaMap[Lu8MapId].enuSideMapSns = SIDE_SNS_RLS;
                                    }
                                    else
                                    {
                                        LpstrPointCloudBuf->AreaMap[Lu8MapId].u8BeFusionToRealMapFlag = 1;/* 强制在后退时后侧雷达不画Map */
                                    }
                                }
                                else
                                {
                                    LpstrPointCloudBuf->AreaMap[Lu8MapId].u8BeFusionToRealMapFlag = 1;/* 强制在后退时后侧雷达不画Map */
                                }
                            }
                            else
                            {
                                LpstrPointCloudBuf->AreaMap[Lu8MapId].u8BeFusionToRealMapFlag = 1;/* 强制在后退时后侧雷达不画Map */
                            }
                        }
                    }
                }
            }
#endif
            if(LpstrPointCloudBuf->AreaMap[Lu8MapId].enuSideMapSns != SIDE_SNS_NONE)
            {
                LpstrPointCloudBuf->u8AreaMapNum++;
            }
        }
        else
        {
            break;
        }
    }
#if 0
    if(Lu8SideMapUpdateFlag)
    {
        if(Lu8AreaMapWithSideSnsCnt == 0)
        {
            Lu8SideMapUpdateFlag = 0;
        }
    }
#endif
    

    /* 对侧雷达和角雷达生成的2个Map点进行融合处理，统一处理到侧雷达Map中--单独处理，便于后续针对Max车型进行裁剪 */
    if(Lu8SideMapUpdateFlag)
    {
        /* 待单独封装函数进行处理 */
        SideAreaMapUpdate(LpstrPointCloudBuf,LpstrSideAreaMap,LeSideSnsInx);
    }
}

/******************************************************************************
 * 函数名称: PointCloudBufUpdate
 * 
 * 功能描述: 更新点云缓存队列中的数据
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-20 09:45   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PointCloudBufUpdate(PDCSnsGroupType LenuGroup,PDCSnsChannelType LenuCh)
{
    /* 更新点云Buf中的数据 */
    SnsObjCoorPointType *LpstrSnsObjCoorPoint;
    PointCloudAreaType LenuPointCloudArea;
    SnsPointCloudUnitType LstrPointCloudUnit;
    uint8 i;

    LpstrSnsObjCoorPoint = &GstrObjCoorPoint[LenuGroup][LenuCh];

#if 1
    /* 左侦听障碍物进点云缓存*/
    if(!LpstrSnsObjCoorPoint->u8LeftAlredayToBufFlag)
    {
        for(i = 0; i < LpstrSnsObjCoorPoint->u8LeftObjCoorCnt; i++)
        {
            if(LpstrSnsObjCoorPoint->LeftData[i].u8CoorValidFlag)
            {
                LenuPointCloudArea = JudgeObjInCloudBufArea(LenuGroup,LpstrSnsObjCoorPoint->LeftData[i].strObjCarCoor.fObjY);
                LstrPointCloudUnit.strObjCarCoor = LpstrSnsObjCoorPoint->LeftData[i].strObjCarCoor;
                LstrPointCloudUnit.strObjOdoCoor = LpstrSnsObjCoorPoint->LeftData[i].strObjOdoCoor;
                LstrPointCloudUnit.enuObjType = LpstrSnsObjCoorPoint->LeftData[i].enuObjType;
                LstrPointCloudUnit.enuDetType = POINT_CLOUD_DET_BY_LEFT;
                LstrPointCloudUnit.enuSnsCh = LenuCh;
                LstrPointCloudUnit.eMeasType = LpstrSnsObjCoorPoint->eMeasType;
                LstrPointCloudUnit.u32DetSysTime = LpstrSnsObjCoorPoint->u32MeasTime;
                LstrPointCloudUnit.u16ExistTime = 0;
                LstrPointCloudUnit.u16MasterDis = LpstrSnsObjCoorPoint->LeftData[i].u16MasterDis;
                LstrPointCloudUnit.u16MasterHeight = LpstrSnsObjCoorPoint->LeftData[i].u16MasterHeight;
                LstrPointCloudUnit.u8ValidFlag = 1;
                LstrPointCloudUnit.u8TypeAlredayBeUseFlag = 0;
                LstrPointCloudUnit.u8CoorAlredayBeUseFlag = 0;
                LstrPointCloudUnit.u8SnsNoiseCnt = LpstrSnsObjCoorPoint->LeftData[i].u8SnsNoiseCnt;
                AddOnePointCloudToBuf(LenuGroup,LenuPointCloudArea,&LstrPointCloudUnit);
            }
        }
        LpstrSnsObjCoorPoint->u8LeftAlredayToBufFlag = 1;
    }


#endif

    /* 右侦听障碍物进点云缓存*/
    if(!LpstrSnsObjCoorPoint->u8RightAlredayToBufFlag)
    {
        for(i = 0; i < LpstrSnsObjCoorPoint->u8RightObjCoorCnt; i++)
        {
            if(LpstrSnsObjCoorPoint->RightData[i].u8CoorValidFlag)
            {
                LenuPointCloudArea = JudgeObjInCloudBufArea(LenuGroup,LpstrSnsObjCoorPoint->RightData[i].strObjCarCoor.fObjY);
                LstrPointCloudUnit.strObjCarCoor = LpstrSnsObjCoorPoint->RightData[i].strObjCarCoor;
                LstrPointCloudUnit.strObjOdoCoor = LpstrSnsObjCoorPoint->RightData[i].strObjOdoCoor;
                LstrPointCloudUnit.enuObjType = LpstrSnsObjCoorPoint->RightData[i].enuObjType;
                LstrPointCloudUnit.enuDetType = POINT_CLOUD_DET_BY_RIGHT;
                LstrPointCloudUnit.enuSnsCh = LenuCh;
                LstrPointCloudUnit.eMeasType = LpstrSnsObjCoorPoint->eMeasType;
                LstrPointCloudUnit.u32DetSysTime = LpstrSnsObjCoorPoint->u32MeasTime;
                LstrPointCloudUnit.u16ExistTime = 0;
                LstrPointCloudUnit.u16MasterDis = LpstrSnsObjCoorPoint->RightData[i].u16MasterDis;
                LstrPointCloudUnit.u16MasterHeight = LpstrSnsObjCoorPoint->RightData[i].u16MasterHeight;
                LstrPointCloudUnit.u8ValidFlag = 1;
                LstrPointCloudUnit.u8TypeAlredayBeUseFlag = 0;
                LstrPointCloudUnit.u8CoorAlredayBeUseFlag = 0;
                LstrPointCloudUnit.u8SnsNoiseCnt = LpstrSnsObjCoorPoint->RightData[i].u8SnsNoiseCnt;
                AddOnePointCloudToBuf(LenuGroup,LenuPointCloudArea,&LstrPointCloudUnit);
            }
        }
        LpstrSnsObjCoorPoint->u8RightAlredayToBufFlag = 1;
    }
    /* 主发障碍物进点云缓存 */
    if(!LpstrSnsObjCoorPoint->u8MasterAlredayToBufFlag)
    {
        for(i = 0; i < LpstrSnsObjCoorPoint->u8MasterObjCoorCnt; i++)
        {
            if(LpstrSnsObjCoorPoint->MasterData[i].u8CoorValidFlag)
            {
                LenuPointCloudArea = JudgeObjInCloudBufArea(LenuGroup,LpstrSnsObjCoorPoint->MasterData[i].strObjCarCoor.fObjY);
                LstrPointCloudUnit.strObjCarCoor = LpstrSnsObjCoorPoint->MasterData[i].strObjCarCoor;
                LstrPointCloudUnit.strObjOdoCoor = LpstrSnsObjCoorPoint->MasterData[i].strObjOdoCoor;
                LstrPointCloudUnit.enuObjType = LpstrSnsObjCoorPoint->MasterData[i].enuObjType;
                LstrPointCloudUnit.enuDetType = POINT_CLOUD_DET_BY_MASTER;
                LstrPointCloudUnit.enuSnsCh = LenuCh;
                LstrPointCloudUnit.eMeasType = LpstrSnsObjCoorPoint->eMeasType;
                LstrPointCloudUnit.u32DetSysTime = LpstrSnsObjCoorPoint->u32MeasTime;
                LstrPointCloudUnit.u16ExistTime = 0;
                LstrPointCloudUnit.u16MasterDis = LpstrSnsObjCoorPoint->MasterData[i].u16MasterDis;
                LstrPointCloudUnit.u16MasterHeight = LpstrSnsObjCoorPoint->MasterData[i].u16MasterHeight;
                LstrPointCloudUnit.u8ValidFlag = 1;
                LstrPointCloudUnit.u8TypeAlredayBeUseFlag = 0;
                LstrPointCloudUnit.u8CoorAlredayBeUseFlag = 0;
                LstrPointCloudUnit.u8SnsNoiseCnt = LpstrSnsObjCoorPoint->MasterData[i].u8SnsNoiseCnt;
                AddOnePointCloudToBuf(LenuGroup,LenuPointCloudArea,&LstrPointCloudUnit);
            }
        }
        LpstrSnsObjCoorPoint->u8MasterAlredayToBufFlag = 1;
    }

    
}

/******************************************************************************
 * 函数名称: ObjCoorPointDataClear
 * 
 * 功能描述: 障碍物坐标点数据清零
 * 
 * 输入参数:LenuSnsGroup--探头分组信息；LenuSnsCh--探头通道信息
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-07 15:24   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void ObjCoorPointDataClear(PDCSnsGroupType LenuSnsGroup,PDCSnsChannelType LenuSnsCh)
{
    SnsObjCoorPointType *LpstrSnsObjCoorPoint;
    
    LpstrSnsObjCoorPoint = &GstrObjCoorPoint[LenuSnsGroup][LenuSnsCh];

    LpstrSnsObjCoorPoint->eMeasType = PDC_SNS_MEAS_IDLE;
    LpstrSnsObjCoorPoint->u32MeasTime = 0;
    LpstrSnsObjCoorPoint->LeftData[0] = GstrObjCoorPointUnitInit;
    LpstrSnsObjCoorPoint->LeftData[1] = GstrObjCoorPointUnitInit;
    LpstrSnsObjCoorPoint->RightData[0] = GstrObjCoorPointUnitInit;
    LpstrSnsObjCoorPoint->RightData[1] = GstrObjCoorPointUnitInit;
    LpstrSnsObjCoorPoint->MasterData[0] = GstrObjCoorPointUnitInit;
    LpstrSnsObjCoorPoint->MasterData[1] = GstrObjCoorPointUnitInit;
    LpstrSnsObjCoorPoint->u8LeftObjCoorCnt = 0;
    LpstrSnsObjCoorPoint->u8RightObjCoorCnt = 0;
    LpstrSnsObjCoorPoint->u8MasterObjCoorCnt = 0;

    LpstrSnsObjCoorPoint->u8LeftAlredayToBufFlag = 1;
    LpstrSnsObjCoorPoint->u8RightAlredayToBufFlag = 1;
    LpstrSnsObjCoorPoint->u8MasterAlredayToBufFlag = 1;
}

/******************************************************************************
 * 函数名称: CloudPointDataInit
 * 
 * 功能描述: 点云Map初始化
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-05-15 10:39   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void CloudPointDataInit(void)
{
    uint8 i,j;
    
    Cloud_ObjMapToCANType *LpstrCloud_ObjMapToCAN;

    for(i = 0; i < PDC_SNS_GROUP_NUM; i++)
    {
        for(j = 0; j < PDC_SNS_GROUP_NUM; j++)
        {
            LpstrCloud_ObjMapToCAN = &GstrCloud_ObjMap[i][j];
            LpstrCloud_ObjMapToCAN->eMapObjType = CLOUD_OBJ_TYPE_NONE;
            LpstrCloud_ObjMapToCAN->eObjExistProb = CLOUD_OBJ_PROB_NONE;
            LpstrCloud_ObjMapToCAN->eMapObjHeight = CLOUD_OBJ_HEIGHT_UNKNOW;
            LpstrCloud_ObjMapToCAN->eObjHeightProb = CLOUD_OBJ_PROB_NONE;
            
            LpstrCloud_ObjMapToCAN->s16ObjMapP1_X = 0;
            LpstrCloud_ObjMapToCAN->s16ObjMapP1_Y = 0;
            LpstrCloud_ObjMapToCAN->u8PointCloudUpdate = 0;
        }
    }
}

/******************************************************************************
 * 函数名称: CloudPointToCANDataUpdate
 * 
 * 功能描述: 点云数据的更新；对于侧边探头，使用主发的点云；对于前后探头，必须有侦听，使用左右侦听中回波强度更大的那一个
 * 
 * 输入参数:LenuSnsGroup--探头分组信息；LenuSnsCh--探头通道信息
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-05-15 10:25   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void CloudPointToCANDataUpdate(PDCSnsGroupType LenuSnsGroup,PDCSnsChannelType LenuSnsCh)
{
    Cloud_ObjMapToCANType *LpstrCloud_ObjMapToCAN;
    SnsObjCoorPointType *LpstrSnsObjCoorPoint;

    LpstrCloud_ObjMapToCAN = &GstrCloud_ObjMap[LenuSnsGroup][LenuSnsCh];    
    LpstrSnsObjCoorPoint = &GstrObjCoorPoint[LenuSnsGroup][LenuSnsCh];

    /* 对标一轨，存在置信度为6、高度置信度为0，高度类型为Unknown，点云存在时类型为Point;点云不存在时为None；
    点云都使用定位来解决，没有定位坐标的不输出*/
    if((LpstrSnsObjCoorPoint->LeftData[0].u8CoorValidFlag)||(LpstrSnsObjCoorPoint->RightData[0].u8CoorValidFlag))
    {
        if((LpstrSnsObjCoorPoint->LeftData[0].u8CoorValidFlag)&&(LpstrSnsObjCoorPoint->RightData[0].u8CoorValidFlag))
        {
            if(LenuSnsGroup == PDC_SNS_GROUP_FRONT)
            {
                if(LpstrSnsObjCoorPoint->LeftData[0].strObjCarCoor.fObjX < LpstrSnsObjCoorPoint->RightData[0].strObjCarCoor.fObjX)
                {
                    LpstrCloud_ObjMapToCAN->s16ObjMapP1_X = LpstrSnsObjCoorPoint->LeftData[0].strObjCarCoor.fObjX/10;
                    LpstrCloud_ObjMapToCAN->s16ObjMapP1_Y = LpstrSnsObjCoorPoint->LeftData[0].strObjCarCoor.fObjY/10;
                }
                else
                {
                    LpstrCloud_ObjMapToCAN->s16ObjMapP1_X = LpstrSnsObjCoorPoint->RightData[0].strObjCarCoor.fObjX/10;
                    LpstrCloud_ObjMapToCAN->s16ObjMapP1_Y = LpstrSnsObjCoorPoint->RightData[0].strObjCarCoor.fObjY/10;
                }
            }
            else
            {
                if(LpstrSnsObjCoorPoint->LeftData[0].strObjCarCoor.fObjX > LpstrSnsObjCoorPoint->RightData[0].strObjCarCoor.fObjX)
                {
                    LpstrCloud_ObjMapToCAN->s16ObjMapP1_X = LpstrSnsObjCoorPoint->LeftData[0].strObjCarCoor.fObjX/10;
                    LpstrCloud_ObjMapToCAN->s16ObjMapP1_Y = LpstrSnsObjCoorPoint->LeftData[0].strObjCarCoor.fObjY/10;
                }
                else
                {
                    LpstrCloud_ObjMapToCAN->s16ObjMapP1_X = LpstrSnsObjCoorPoint->RightData[0].strObjCarCoor.fObjX/10;
                    LpstrCloud_ObjMapToCAN->s16ObjMapP1_Y = LpstrSnsObjCoorPoint->RightData[0].strObjCarCoor.fObjY/10;
                }
            }
        }
        else if(LpstrSnsObjCoorPoint->LeftData[0].u8CoorValidFlag)
        {
            LpstrCloud_ObjMapToCAN->s16ObjMapP1_X = LpstrSnsObjCoorPoint->LeftData[0].strObjCarCoor.fObjX/10;
            LpstrCloud_ObjMapToCAN->s16ObjMapP1_Y = LpstrSnsObjCoorPoint->LeftData[0].strObjCarCoor.fObjY/10;
        }
        else
        {
            LpstrCloud_ObjMapToCAN->s16ObjMapP1_X = LpstrSnsObjCoorPoint->RightData[0].strObjCarCoor.fObjX/10;
            LpstrCloud_ObjMapToCAN->s16ObjMapP1_Y = LpstrSnsObjCoorPoint->RightData[0].strObjCarCoor.fObjY/10;
        }
        LpstrCloud_ObjMapToCAN->eMapObjType = CLOUD_OBJ_TYPE_POINT;
        LpstrCloud_ObjMapToCAN->eObjExistProb = CLOUD_OBJ_PROB_LV6;
        LpstrCloud_ObjMapToCAN->eMapObjHeight = CLOUD_OBJ_HEIGHT_UNKNOW;
        LpstrCloud_ObjMapToCAN->eObjHeightProb = CLOUD_OBJ_PROB_NONE;
    }
    else
    {
        LpstrCloud_ObjMapToCAN->eMapObjType = CLOUD_OBJ_TYPE_NONE;
        LpstrCloud_ObjMapToCAN->eObjExistProb = CLOUD_OBJ_PROB_NONE;
        LpstrCloud_ObjMapToCAN->eMapObjHeight = CLOUD_OBJ_HEIGHT_UNKNOW;
        LpstrCloud_ObjMapToCAN->eObjHeightProb = CLOUD_OBJ_PROB_NONE;
        LpstrCloud_ObjMapToCAN->s16ObjMapP1_X = 0;
        LpstrCloud_ObjMapToCAN->s16ObjMapP1_Y = 0;
    }
    LpstrCloud_ObjMapToCAN->u8PointCloudUpdate = 1;
}

/******************************************************************************/
/******************************************************************************/
/****************************** Private Function ******************************/
/******************************************************************************/
/******************************************************************************/
/******************************************************************************
 * 函数名称: SnsObjCoorUnitDataClear
 * 
 * 功能描述: 清零探头坐标数据单元，不含卡尔曼滤波的参数
 * 
 * 输入参数:LstrpSnsObjCoorUnit--对应探头数据单元的指针 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-08-02 19:26   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void SnsObjCoorUnitDataClear(SnsObjCoorUnitType* LstrpSnsObjCoorUnit)
{
    LstrpSnsObjCoorUnit->u8MatchMapId = 31;
    LstrpSnsObjCoorUnit->u8CoorValidFlag = 0;
    LstrpSnsObjCoorUnit->strObjSnsCoor = GstrObjCoorInit;
    LstrpSnsObjCoorUnit->strObjCarCoor = GstrObjCoorInit;
    LstrpSnsObjCoorUnit->strObjOdoCoor = GstrObjCoorInit;
    LstrpSnsObjCoorUnit->strObjOdoFilterCoor = GstrObjCoorInit;
    LstrpSnsObjCoorUnit->strObjCarFilterCoor = GstrObjCoorInit;
    LstrpSnsObjCoorUnit->u16ObjHeight = 0;
    LstrpSnsObjCoorUnit->enuEchoRelation = FIRST_SECOND_NONE;
}

/******************************************************************************
 * 函数名称: CalculateObjLeftSnsCoor
 * 
 * 功能描述: 计算障碍物左侦听计算相对于探头的坐标
 * 
 * 输入参数:LenuGroup--探头对应的组别；Lu16MasterDis--主发距离；Lu16ListenDis--侦听距离；Lu16SnsSpace--探头间距；
 * 
 * 输出参数:LstrObjSnsCoor--待返回障碍物坐标的指针 
 * 
 * 返回值:Lu8ObjCoorValidFlag--返回障碍物坐标计算有效的标志位
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-08-02 20:18   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static uint8 CalculateObjLeftSnsCoor(PDCSnsGroupType LenuGroup,PDCSnsChannelType LenuCh,uint16 Lu16MasterDis,uint16 Lu16ListenDis,uint16 Lu16SnsSpace,ObjCoorType* LstrObjSnsCoor)
{
    uint8 Lu8ObjCoorValidFlag = 0;
    uint16 Lu16ListenOneSideDis;
    float LfObjCosAngle = 0.0;
    float LfObjSinAngle;
    float LfObjAngle;

    if(Lu16ListenDis > Lu16MasterDis)
    {
        Lu16ListenOneSideDis = Lu16ListenDis - Lu16MasterDis;
        LfObjCosAngle = PubAI_CalObjCosAngle(Lu16ListenOneSideDis,Lu16MasterDis,Lu16SnsSpace);
        if((LfObjCosAngle >= -1.0)&&(LfObjCosAngle <= 1.0))
        {
            LfObjAngle = acosf(LfObjCosAngle);
            LfObjSinAngle = sinf(LfObjAngle);
            if(LenuGroup == PDC_SNS_GROUP_FRONT)
            {
                LstrObjSnsCoor->fObjX = Lu16MasterDis*LfObjSinAngle;
                LstrObjSnsCoor->fObjY = Lu16MasterDis*LfObjCosAngle;
            }
            else
            {
                LstrObjSnsCoor->fObjX = -Lu16MasterDis*LfObjSinAngle;
                LstrObjSnsCoor->fObjY = Lu16MasterDis*LfObjCosAngle;
            }
            Lu8ObjCoorValidFlag = 1;
        }
        else
        {
            Lu8ObjCoorValidFlag = 0;
        }
    }
    else
    {
        Lu8ObjCoorValidFlag = 0;
    }
    if(Lu8ObjCoorValidFlag)
    {
        /* 限制探头的左侦听坐标范围--探头坐标原始端限制 */
        if((LstrObjSnsCoor->fObjY < GstrSnsObjCoorLimit[LenuGroup][LenuCh].s16LeftListenObj_Y_Min)||\
            (LstrObjSnsCoor->fObjY > GstrSnsObjCoorLimit[LenuGroup][LenuCh].s16LeftListenObj_Y_Max))
        {
            Lu8ObjCoorValidFlag = 0;
            /* 打印探头坐标异常限制 */
#if 0
            if((LenuGroup == 0x00)&&(LenuCh == 0x05))
            {
                VS_PRINT("Time:%.3f,FRS Left Sns Coor Limit,MasterDis:%d,ListenDis:%d,X:%.3f,Y:%.3f,Y_Min:%d,Y_Max:%d\r\n",GfMessageTime,Lu16MasterDis,\
                    Lu16ListenDis,LstrObjSnsCoor->fObjX,LstrObjSnsCoor->fObjY,GstrSnsObjCoorLimit[LenuGroup][LenuCh].s16LeftListenObj_Y_Min,\
                    GstrSnsObjCoorLimit[LenuGroup][LenuCh].s16LeftListenObj_Y_Max);
            }
#endif
        }
        if((LenuCh == 0x05)&&(Lu8ObjCoorValidFlag == 0))
        {
            sint16 Ls16Y_Min,Ls16Y_Max;
            Ls16Y_Max = GstrSnsObjCoorLimit[LenuGroup][LenuCh].s16LeftListenObj_Y_Max;
            if(Lu16MasterDis < 500)
            {
                Ls16Y_Min = -500;
            }
            else if(Lu16MasterDis < 1000)
            {
                Ls16Y_Min = -800;
            }
            else if(Lu16MasterDis < 1500)
            {
                Ls16Y_Min = -1100;
            }
            else if(Lu16MasterDis < 2000)
            {
                Ls16Y_Min = -1400;
            }
            else if(Lu16MasterDis < 2500)
            {
                Ls16Y_Min = -1800;
            }
            else if(Lu16MasterDis < 2750)
            {
                Ls16Y_Min = -2100;
            }
            else if(Lu16MasterDis < 3000)
            {
                Ls16Y_Min = -2250;
            }
            else
            {
                Ls16Y_Min = -2300;
            }
            /* 针对FRS探头远离大墙场景画不出Map的场景修改 */
            if(LenuGroup == PDC_SNS_GROUP_FRONT)
            {
                if((Gs16CarStrAngleForSnsUse > 100)||(GstrSideAreaMap[SIDE_SNS_FRS].u8SnsFarawayLineFlag))
                {
                    Ls16Y_Min = Ls16Y_Min - 200;
                }
            }

            if((LstrObjSnsCoor->fObjY > Ls16Y_Min)&&(LstrObjSnsCoor->fObjY < Ls16Y_Max))
            {
                Lu8ObjCoorValidFlag = 1;
            }
            else
            {
#if 0
                if((GfMessageTime > 12.9)&&(GfMessageTime < 15))
                {
                    VS_PRINT("Time:%.3f,FRS Left Coor,SnsX:%.3f,SnsY:%.3f,Y_Min:%d,StrAngle:%d\r\n",GfMessageTime,\
                        LstrObjSnsCoor->fObjX,LstrObjSnsCoor->fObjY,Ls16Y_Min, Gs16CarStrAngleForSnsUse);
                }
#endif
            }
        }    
    }

    return Lu8ObjCoorValidFlag;
}


/******************************************************************************
 * 函数名称: CalculateObjRightSnsCoor
 * 
 * 功能描述: 计算障碍物右侦听计算相对于探头的坐标
 * 
 * 输入参数:LenuGroup--探头对应的组别；Lu16MasterDis--主发距离；Lu16ListenDis--侦听距离；Lu16SnsSpace--探头间距；
 * 
 * 输出参数:LstrObjSnsCoor--待返回障碍物坐标的指针 
 * 
 * 返回值:Lu8ObjCoorValidFlag--返回障碍物坐标计算有效的标志位
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-08-02 20:42   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static uint8 CalculateObjRightSnsCoor(PDCSnsGroupType LenuGroup,PDCSnsChannelType LenuCh,uint16 Lu16MasterDis,uint16 Lu16ListenDis,uint16 Lu16SnsSpace,ObjCoorType* LstrObjSnsCoor)
{
    uint8 Lu8ObjCoorValidFlag = 0;
    uint16 Lu16ListenOneSideDis;
    float LfObjCosAngle = 0.0;
    float LfObjSinAngle;
    float LfObjAngle;
    if(Lu16ListenDis > Lu16MasterDis)
    {
        Lu16ListenOneSideDis = Lu16ListenDis - Lu16MasterDis;
        LfObjCosAngle = PubAI_CalObjCosAngle(Lu16ListenOneSideDis,Lu16MasterDis,Lu16SnsSpace);
        if((LfObjCosAngle >= -1.0)&&(LfObjCosAngle <= 1.0))
        {
            LfObjAngle = acosf(LfObjCosAngle);
            LfObjSinAngle = sinf(LfObjAngle);
            if(LenuGroup == PDC_SNS_GROUP_FRONT)
            {
                LstrObjSnsCoor->fObjX = Lu16MasterDis*LfObjSinAngle;
                LstrObjSnsCoor->fObjY = -Lu16MasterDis*LfObjCosAngle;
            }
            else
            {
                LstrObjSnsCoor->fObjX = -Lu16MasterDis*LfObjSinAngle;
                LstrObjSnsCoor->fObjY = -Lu16MasterDis*LfObjCosAngle;
            }
            Lu8ObjCoorValidFlag = 1;
        }
        else
        {
            Lu8ObjCoorValidFlag = 0;
        }
    }
    else
    {
        Lu8ObjCoorValidFlag = 0;
    }

    if(Lu8ObjCoorValidFlag)
    {
        /* 限制探头的左侦听坐标范围--探头坐标原始端限制 */
        if((LstrObjSnsCoor->fObjY < GstrSnsObjCoorLimit[LenuGroup][LenuCh].s16RightListenObj_Y_Min)||\
            (LstrObjSnsCoor->fObjY > GstrSnsObjCoorLimit[LenuGroup][LenuCh].s16RightListenObj_Y_Max))
        {
            Lu8ObjCoorValidFlag = 0;
            /* 打印探头坐标异常限制 */
#if 0
            if((LenuGroup == 0x00)&&(LenuCh == 0x00))
            {
                VS_PRINT("Time:%.3f,FLS Right Sns Coor Limit,MasterDis:%d,ListenDis:%d,X:%.3f,Y:%.3f,Y_Min:%d,Y_Max:%d\r\n",GfMessageTime,Lu16MasterDis,\
                    Lu16ListenDis,LstrObjSnsCoor->fObjX,LstrObjSnsCoor->fObjY,GstrSnsObjCoorLimit[LenuGroup][LenuCh].s16RightListenObj_Y_Min,\
                    GstrSnsObjCoorLimit[LenuGroup][LenuCh].s16RightListenObj_Y_Max);
            }
#endif
        }
        if((LenuCh == 0x00)&&(Lu8ObjCoorValidFlag == 0))
        {
            sint16 Ls16Y_Min,Ls16Y_Max;
            Ls16Y_Min = GstrSnsObjCoorLimit[LenuGroup][LenuCh].s16RightListenObj_Y_Min;
            if(Lu16MasterDis < 500)
            {
                Ls16Y_Max = 500;
            }
            else if(Lu16MasterDis < 1000)
            {
                Ls16Y_Max = 800;
            }
            else if(Lu16MasterDis < 1500)
            {
                Ls16Y_Max = 1100;
            }
            else if(Lu16MasterDis < 2000)
            {
                Ls16Y_Max = 1400;
            }
            else if(Lu16MasterDis < 2500)
            {
                Ls16Y_Max = 1800;
            }
            else if(Lu16MasterDis < 2750)
            {
                Ls16Y_Max = 2100;
            }
            else if(Lu16MasterDis < 3000)
            {
                Ls16Y_Max = 2250;
            }
            else
            {
                Ls16Y_Max = 2300;
            }
            /* 针对FLS探头远离大墙场景画不出Map的场景修改 */
            if(LenuGroup == PDC_SNS_GROUP_FRONT)
            {
                if((Gs16CarStrAngleForSnsUse < -100)||(GstrSideAreaMap[SIDE_SNS_FLS].u8SnsFarawayLineFlag))
                {
                    Ls16Y_Min = Ls16Y_Min + 200;
                }
            }
            if((LstrObjSnsCoor->fObjY > Ls16Y_Min)&&(LstrObjSnsCoor->fObjY < Ls16Y_Max))
            {
                Lu8ObjCoorValidFlag = 1;
            }
#if 0
            if((GfMessageTime > 8)&&(GfMessageTime < 8.5))
            {
                VS_PRINT("Time:%.3f,FLS Right Coor,SnsX:%.3f,SnsY:%.3f,Y_Min:%d,Y_Max:%d\r\n",GfMessageTime,\
                    LstrObjSnsCoor->fObjX,LstrObjSnsCoor->fObjY,Ls16Y_Min,Ls16Y_Max);
            }
#endif
        }
    }
#if 0
    if((GfMessageTime > 8)&&(GfMessageTime < 8.5))
    {
        VS_PRINT("Time:%.3f,FLS Right Coor,SnsX:%.3f,SnsY:%.3f\r\n",GfMessageTime,\
            LstrObjSnsCoor->fObjX,LstrObjSnsCoor->fObjY);
    }
#endif

    return Lu8ObjCoorValidFlag;
}


/******************************************************************************
 * 函数名称: CalValidListenDisForMiddle
 * 
 * 功能描述: 针对中间雷达，通过主发和探头间距计算有效障碍物的侦听距离，便于主发侦听的匹配
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-06 20:45   V0.1      AntonyFang   初次发布
 ******************************************************************************/
ObjValidListenDisType CalValidListenDis(uint16 Lu16MasterDis, uint16 Lu16SnsSpace,SnsMasterListenPosType LenuMasterListenPos)
{
    ObjValidListenDisType LstrObjValidListenDis;
    float LfCosAngValue;
    float LfMasterSquare;
    float LfSpaceSquare;
    float LfMasterMultSpace;
    float LfTemp;
    if(LenuMasterListenPos == SNS_SIDE_CORNER)
    {
        /* 边角和侧边，探头间距小，允许Sns_X 坐标更大 */
        if(Lu16MasterDis < 700)
        {
            LfCosAngValue = 0.9;/* 0.707 */
        }
        else if(Lu16MasterDis < 1000)
        {
            LfCosAngValue = 1000.0/Lu16MasterDis;
        }
        else if(Lu16MasterDis < 1500)
        {
            LfCosAngValue = 1400.0/Lu16MasterDis;
        }
        else if(Lu16MasterDis < 2000)
        {
            LfCosAngValue = 1650.0/Lu16MasterDis;
        }
        else if(Lu16MasterDis < 2250)
        {
            LfCosAngValue = 1800.0/Lu16MasterDis;
        }
        else if(Lu16MasterDis < 2500)
        {
            LfCosAngValue = 2000.0/Lu16MasterDis;
        }
        else if(Lu16MasterDis < 2750)
        {
            LfCosAngValue = 2150.0/Lu16MasterDis;
        }
        else if(Lu16MasterDis < 3000)
        {
            LfCosAngValue = 2220.0/Lu16MasterDis;
        }
        else if(Lu16MasterDis < 3250)
        {
            LfCosAngValue = 2250.0/Lu16MasterDis;
        }
        else if(Lu16MasterDis < 3500)
        {
            LfCosAngValue = 2300.0/Lu16MasterDis;
        }
        else
        {
            LfCosAngValue = 2350.0/Lu16MasterDis;
        }
    }
    else if(LenuMasterListenPos == SNS_CORNER_MIDDLE)
    {
        /* 边角和中间，尽可能不要往中间定位那么多 */
        if(Lu16MasterDis < 700)
        {
            LfCosAngValue = 0.8;/* 0.707 */
        }
        else if(Lu16MasterDis < 1000)
        {
            LfCosAngValue = 660.0/Lu16MasterDis;
        }
        else if(Lu16MasterDis < 1500)
        {
            LfCosAngValue = 720.0/Lu16MasterDis;
        }
        else if(Lu16MasterDis < 2000)
        {
            LfCosAngValue = 780.0/Lu16MasterDis;
        }
        else if(Lu16MasterDis < 2500)
        {
            LfCosAngValue = 820.0/Lu16MasterDis;
        }
        else if(Lu16MasterDis < 3000)
        {
            LfCosAngValue = 920.0/Lu16MasterDis;
        }
        else
        {
            LfCosAngValue = 1200.0/Lu16MasterDis;
        }
    }
    else
    {
        /* 中间和中间，探头间距大，适当即可 */
        if(Lu16MasterDis < 700)
        {
            LfCosAngValue = 0.8;/* 0.707 */
        }
        else if(Lu16MasterDis < 1000)
        {
            LfCosAngValue = 760.0/Lu16MasterDis;
        }
        else if(Lu16MasterDis < 1500)
        {
            LfCosAngValue = 770.0/Lu16MasterDis;
        }
        else if(Lu16MasterDis < 2000)
        {
            LfCosAngValue = 780.0/Lu16MasterDis;
        }
        else if(Lu16MasterDis < 2500)
        {
            LfCosAngValue = 790.0/Lu16MasterDis;
        }
        else if(Lu16MasterDis < 3000)
        {
            LfCosAngValue = 800.0/Lu16MasterDis;
        }
        else if(Lu16MasterDis < 3500)
        {
            LfCosAngValue = 750.0/Lu16MasterDis;
        }
        else 
        {
            LfCosAngValue = 750.0/Lu16MasterDis;
        }
    }


    LfMasterSquare = (float)(Lu16MasterDis*Lu16MasterDis);
    LfSpaceSquare = (float)(Lu16SnsSpace*Lu16SnsSpace);
    LfMasterMultSpace = (float)(Lu16MasterDis*Lu16SnsSpace*2);
    LfMasterMultSpace = LfMasterMultSpace*LfCosAngValue;

    LfTemp = LfMasterSquare+LfSpaceSquare+LfMasterMultSpace;
    LfTemp = powf(LfTemp,0.5);
    LstrObjValidListenDis.u16MaxDis = (uint16)LfTemp;
    LstrObjValidListenDis.u16MaxDis += Lu16MasterDis;

    
    LfTemp = LfMasterSquare+LfSpaceSquare-LfMasterMultSpace;
    LfTemp = powf(LfTemp,0.5);
    LstrObjValidListenDis.u16MinDis = (uint16)LfTemp;
    LstrObjValidListenDis.u16MinDis += Lu16MasterDis;
    return LstrObjValidListenDis;
}


/******************************************************************************
 * 函数名称: JudgeSnsSigGroupEchoType
 * 
 * 功能描述: 通过回波数据判断回波信号组的类型
 * 
 * 输入参数:LpstrSnsSigGroupDisFollowPoint--回波信号组指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-08-02 20:53   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static SigGroupRelationType JudgeSnsSigGroupEchoType(uint16 Lu16FirstDis,uint16 Lu16FirstHeight,uint16 Lu16SecondDis,uint16 Lu16SecondHeight,PDCSnsMeasTypeType LenuSnsMeasType)
{
    SigGroupRelationType LenuSigGroupRelation = FIRST_SECOND_NONE;
    uint16 Lu16SechodMoreThanFirstEchoHeight;
    
    if(LenuSnsMeasType == PDC_SNS_MEAS_STD)
    {
        Lu16SechodMoreThanFirstEchoHeight = STD_SECOND_MORE_THAN_FIRST_HEIGHT;
    }
    else
    {
        Lu16SechodMoreThanFirstEchoHeight = CHIRP_SECOND_MORE_THAN_FIRST_HEIGHT;
    }

    if(Lu16FirstDis != MAP_SIG_GROUP_INVALID_DIS)
    {
        if(Lu16SecondDis != MAP_SIG_GROUP_INVALID_DIS)
        {
            /* 此处需要考虑扫频的场景，需要分开使用回波宽度 */
            if(Lu16SecondHeight > (Lu16FirstHeight+Lu16SechodMoreThanFirstEchoHeight))
            {
                LenuSigGroupRelation = FIRST_SMALL_SECOND_ECHO;
            }
            else
            {
                LenuSigGroupRelation = FIRST_BIG_SECOND_ECHO;
            }
        }
        else
        {
            LenuSigGroupRelation = ONLY_FIRST_ECHO;
        }
    }
    else
    {
        LenuSigGroupRelation = FIRST_SECOND_NONE;
    }

    return LenuSigGroupRelation;
}


/******************************************************************************/
/******************************************************************************/
/******************************* Public Function ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************
 * 函数名称: SnsObjCoorDataInit
 * 
 * 功能描述: 探头坐标数据初始化
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-07 10:05   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsObjCoorDataInit(PDCSnsGroupType LenuGroup)
{
    PDCSnsChannelType LenuCh;

    for(LenuCh = PDC_SNS_CH_FLS_RLS; LenuCh < PDC_SNS_CH_NUM; LenuCh++)
    {
        ObjCoorPointDataClear(LenuGroup,LenuCh);
    }
}



/******************************************************************************
 * 函数名称: SnsObjCoorDataPowerOnInit
 * 
 * 功能描述: 上电初始化探头坐标数据
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-08-02 19:40   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsObjCoorDataPowerOnInit(void)
{
    PDCSnsGroupType LenuGroup;

    for(LenuGroup = PDC_SNS_GROUP_FRONT; LenuGroup < PDC_SNS_GROUP_NUM; LenuGroup++)
    {
        SnsObjCoorDataInit(LenuGroup);
        PointCloudBufInit(LenuGroup);
    }
    CloudPointDataInit();
    AreaClusterBufInit();
    SideAreaMapInit();
    SideAreaPointBufInit();
    #if VHE_POINT_CLOUD_SIMULATE
    VehiclePointCloudBufInit();
    #endif
}



/******************************************************************************
 * 函数名称: SideObjCoorPointTypeJudge
 * 
 * 功能描述: 侧边探头定位点云的坐标类型
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-04-08 21:17   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static OriginalObjType SideObjCoorPointTypeJudge(ObjCoorPointUnitType *LpstrObjCoorPointUnit,SigGroupRelationType LenuMasterRelation,PDCSnsMeasTypeType eMeasType,PDCSnsGroupType LenuGroup,PDCSnsChannelType LenuSnsCh)
{
    uint8 Lu8HighIndex;
    OriginalObjType LenuOriObjType = OBJ_NONE_TYPE;
    const SnsMapCalibHeightType *LptrThreTable;


    if(LpstrObjCoorPointUnit->u16MasterDis < MAP_TABLE_MAX_DIS)
    {
        Lu8HighIndex = LpstrObjCoorPointUnit->u16MasterDis/MAP_HIGH_TABLE_STEP;
    }
    else
    {
        Lu8HighIndex = SNS_MAP_DIS_HIGH_400cm;
    }

    if(eMeasType == PDC_SNS_MEAS_STD)
    {
        LptrThreTable = &GStrMapObjJudgeStdThresTableForSideMapInRAM[0];
    }
    else
    {
        LptrThreTable = &GStrMapObjJudgeChirpThresTableForSideMapInRAM[0];
    }


    if(LenuMasterRelation == ONLY_FIRST_ECHO)
    {
        /* 根据前后雷达并结合泊车状态，设置不同的高低判断阈值 */
        /* 根据距离设置不同的判断阈值，对于50cm以内的，默认设置为高，50cm以外的根据回波高度区分不同的类型 */
        if(LenuGroup == 0x00)
        {
            if(GstrParkingGuidenceData.eParking_Sts == PARKING_SEARCH)
            {
#if 1
                if(LpstrObjCoorPointUnit->u16MasterDis < 1300)//2600
                {
                    if(LpstrObjCoorPointUnit->u16MasterHeight > LptrThreTable[Lu8HighIndex].u16BigWallHeight)
                    {
                        LenuOriObjType = OBJ_BIGWALL_TYPE;
                    }
                    else
                    {
                        LenuOriObjType = OBJ_PVC_PIPE_TYPE;
                    }
                }
                else
                {
                    if(LpstrObjCoorPointUnit->u16MasterHeight > LptrThreTable[Lu8HighIndex].u16BigWallHeight)
                    {
                        LenuOriObjType = OBJ_BIGWALL_TYPE;
                    }
                    else
                    {
                        LenuOriObjType = OBJ_LOW_CURB_TYPE;
                    }
                }

#else
                if(LpstrObjCoorPointUnit->u16MasterHeight > LptrThreTable[Lu8HighIndex].u16BigWallHeight)
                {
                    LenuOriObjType = OBJ_BIGWALL_TYPE;
                }
                else
                {
                    LenuOriObjType = OBJ_LOW_CURB_TYPE;
                }
#endif
            }
            else
            {
                if(LpstrObjCoorPointUnit->u16MasterDis < 1500)//2500
                {
                    if(LpstrObjCoorPointUnit->u16MasterHeight > LptrThreTable[Lu8HighIndex].u16BigWallHeight)
                    {
                        LenuOriObjType = OBJ_BIGWALL_TYPE;
                    }
                    else
                    {
                        LenuOriObjType = OBJ_PVC_PIPE_TYPE;
                    }
                }
                else
                {
                    if(LpstrObjCoorPointUnit->u16MasterHeight > LptrThreTable[Lu8HighIndex].u16BigWallHeight)
                    {
                        LenuOriObjType = OBJ_BIGWALL_TYPE;
                    }
                    else
                    {
                        LenuOriObjType = OBJ_LOW_CURB_TYPE;
                    }
                }
            }
        }
        else
        {
            if(GstrParkingGuidenceData.fCarAngleSub > CAR_SEARCH_TO_GUIDANCE_30_ANGLE)
            {
                if(LpstrObjCoorPointUnit->u16MasterDis < 2000)
                {
                    if(LpstrObjCoorPointUnit->u16MasterHeight > LptrThreTable[Lu8HighIndex].u16BigWallHeight)
                    {
                        LenuOriObjType = OBJ_BIGWALL_TYPE;
                    }
                    else
                    {
                        LenuOriObjType = OBJ_PVC_PIPE_TYPE;
                    }
                }
                else
                {
                    if(LpstrObjCoorPointUnit->u16MasterHeight > LptrThreTable[Lu8HighIndex].u16BigWallHeight)
                    {
                        LenuOriObjType = OBJ_BIGWALL_TYPE;
                    }
                    else
                    {
                        LenuOriObjType = OBJ_LOW_CURB_TYPE;
                    }
                }
            }
            else
            {
                if(LpstrObjCoorPointUnit->u16MasterDis < MAP_SIDE_NEAR_ALL_HIGH_DIS)
                {
                    if(LpstrObjCoorPointUnit->u16MasterHeight > LptrThreTable[Lu8HighIndex].u16BigWallHeight)
                    {
                        LenuOriObjType = OBJ_BIGWALL_TYPE;
                    }
                    else
                    {
                        LenuOriObjType = OBJ_PVC_PIPE_TYPE;
                    }
                }
                else
                {
                    if(LpstrObjCoorPointUnit->u16MasterHeight > LptrThreTable[Lu8HighIndex].u16BigWallHeight)
                    {
                        LenuOriObjType = OBJ_BIGWALL_TYPE;
                    }
                    else
                    {
                        LenuOriObjType = OBJ_LOW_CURB_TYPE;
                    }
                }
            }
        }
    }
    else
    {
        /* 不再区分高路沿和普通高类型，而是使用高路沿代替既有二次回波，又有大墙特征的障碍物 */
        if(LpstrObjCoorPointUnit->u16MasterHeight > LptrThreTable[Lu8HighIndex].u16BigWallHeight) 
        {
            LenuOriObjType = OBJ_HIGH_HIGH_CUEB;
        }
        else
        {
            LenuOriObjType = OBJ_PVC_PIPE_TYPE;
        }
    }
    

    return LenuOriObjType;
}


/******************************************************************************
 * 函数名称: ObjCoorPointTypeJudge
 * 
 * 功能描述: 通过回波关系及回波高度，初步判断障碍物的类型
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-07 16:30   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static OriginalObjType ObjCoorPointTypeJudge(ObjCoorPointUnitType *LpstrObjCoorPointUnit,SigGroupRelationType LenuMasterRelation,SigGroupRelationType LenuListenRelation,PDCSnsMeasTypeType eMeasType, PDCSnsGroupType LenuSnsGroup, PDCSnsChannelType LenuSnsCh)
{
    uint8 Lu8HighIndex;
    OriginalObjType LenuOriObjType = OBJ_NONE_TYPE;
    const SnsMapCalibHeightType *LptrThreTable;
    float coff = 0.8;

    if(LpstrObjCoorPointUnit->u16MasterDis < MAP_TABLE_MAX_DIS)
    {
        Lu8HighIndex = LpstrObjCoorPointUnit->u16MasterDis/MAP_HIGH_TABLE_STEP;
    }
    else
    {
        Lu8HighIndex = SNS_MAP_DIS_HIGH_400cm;
    }

    if(eMeasType == PDC_SNS_MEAS_STD)
    {
        LptrThreTable = &GStrMapObjJudgeStdThresTableInRAM[0];
    }
    else
    {
        LptrThreTable = &GStrMapObjJudgeChirpThresTableInRAM[0];
    }

    /* 该函数只处理前后Map，侧边单独函数处理 */
    /* 如果存在双回波的情况，则直接认为该点对应的障碍物为高，置为大墙类型 */
    if ((LenuMasterRelation == FIRST_SMALL_SECOND_ECHO) || (LenuListenRelation == FIRST_SMALL_SECOND_ECHO) ||
        (LenuMasterRelation == FIRST_BIG_SECOND_ECHO) || (LenuListenRelation == FIRST_BIG_SECOND_ECHO))
    {
#if 0
        printf("big_wall_type GfMessageTime = %.3u, LenuSnsGroup = %d, LenuSnsCh = %d, u16MasterDis = %.3u, u16MasterHeight = %.3u\n",
            GdSystemMsTimer, LenuSnsGroup, LenuSnsCh, LpstrObjCoorPointUnit->u16MasterDis, LpstrObjCoorPointUnit->u16MasterHeight);
#endif

        LenuOriObjType = OBJ_BIGWALL_TYPE;
        return LenuOriObjType;
    }

    int temp_height_result = 0;
    if ((LenuMasterRelation == ONLY_FIRST_ECHO) || (LenuListenRelation == ONLY_FIRST_ECHO))
    {
        if (LpstrObjCoorPointUnit->u16MasterHeight > LptrThreTable[Lu8HighIndex].u16SecondWallHeight * coff)
        {
            temp_height_result = 1;
            LenuOriObjType = OBJ_HIGH_CURB_TYPE;
        }
        else if(LpstrObjCoorPointUnit->u16MasterHeight < LptrThreTable[Lu8HighIndex].u16CurbHeight * coff)
        {
            temp_height_result = 2;
            LenuOriObjType = OBJ_LOW_CURB_TYPE;
        }
        else
        {
            if ((LptrThreTable[Lu8HighIndex].u16SecondWallHeight * coff - LpstrObjCoorPointUnit->u16MasterHeight) >
                (LpstrObjCoorPointUnit->u16MasterHeight - LptrThreTable[Lu8HighIndex].u16CurbHeight * coff))
            {
                temp_height_result = 3;
                LenuOriObjType = OBJ_LOW_CURB_TYPE;
            }
            else
            {
                temp_height_result = 4;
                LenuOriObjType = OBJ_HIGH_CURB_TYPE;
            }
        }

#if 0 // 调式判断点云高度类型的信息打印 -- 2024-04-22
        printf("GfMessageTime = %.3u, LenuSnsGroup = %d, LenuSnsCh = %d, u16MasterDis = %.3u, u16MasterHeight = %.3u, temp_height_result = %d\n", 
            GdSystemMsTimer, LenuSnsGroup, LenuSnsCh, LpstrObjCoorPointUnit->u16MasterDis, LpstrObjCoorPointUnit->u16MasterHeight, temp_height_result);
#endif

    }
#if 0
    if((LenuMasterRelation == FIRST_SMALL_SECOND_ECHO)||(LenuListenRelation == FIRST_SMALL_SECOND_ECHO))
    {
        LenuOriObjType = OBJ_HIGH_HIGH_CUEB;
    }
    else if((LenuMasterRelation == FIRST_BIG_SECOND_ECHO)||(LenuListenRelation == FIRST_BIG_SECOND_ECHO))
    {
        if((LpstrObjCoorPointUnit->u16MasterHeight > LptrThreTable[Lu8HighIndex].u16BigWallHeight)||\
           (LpstrObjCoorPointUnit->u16ListenHeight > LptrThreTable[Lu8HighIndex].u16BigWallHeight)) 
        {
            LenuOriObjType = OBJ_BIGWALL_TYPE;
        }
        else
        {
            LenuOriObjType = OBJ_PVC_PIPE_TYPE;
        }
    }
    else if((LenuMasterRelation == ONLY_FIRST_ECHO)||(LenuListenRelation == ONLY_FIRST_ECHO))
    {
        /* 仅针对Max车型使用信号组的 关系进行障碍物高低的判断 */
        if((GenuMapVheiCfgLevel == MAP_VEH_MAX)&&(GstrParkingGuidenceData.eLowVolPwrMdFlag2 != LOWPOWER_MODFLAG2_ADAS))
        {
            LenuOriObjType = OBJ_LOW_CURB_TYPE;
        }
        else
        {
            LenuOriObjType = OBJ_PVC_PIPE_TYPE;
        }
    }
    else
    {
        LenuOriObjType = OBJ_NONE_TYPE;
    }
#endif

    return LenuOriObjType;
}

/******************************************************************************
 * 函数名称: SnsCoorPointJudgeObjArea
 * 
 * 功能描述: 判断坐标点详细位置
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-13 16:26   V0.1      AntonyFang   初次发布
 ******************************************************************************/
ObjMAPAreaType SnsCoorPointJudgeObjArea(PDCSnsGroupType LenuSnsGroup,float LfObjX,float LfObjY)
{
    ObjMAPAreaType LenuObjArea = MAP_AREA_NONE;
    Pub_Point2LineType LenuPoint2LineRelation;

    ObjMapAreaLineType LenuStartLine;
    ObjMapAreaLineType LenuEndLine;
    ObjMapAreaLineType LenuLineInx;
    float P1_X;
    float P1_Y;
    float P2_X;
    float P2_Y;
    
    if(LfObjY > 0)
    {   
        /* 障碍物在保杠的左侧，从中间往左边找 */
        LenuStartLine = MAP_LINE3_4;
        LenuEndLine = MAP_LINE0_1;
        for(LenuLineInx = MAP_LINE3_4; LenuLineInx >= MAP_LINE0_1; LenuLineInx--)
        {
            if(LenuSnsGroup == PDC_SNS_GROUP_FRONT)
            {
                /* 此处左右的相对视角是以P1点指向P2点来看的 */
                P2_X = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaEnd_X;
                P2_Y = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaEnd_Y;
                P1_X = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaStart_X;
                P1_Y = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaStart_Y;
            }
            else
            {
                /* 此处左右的相对视角是以P1点指向P2点来看的 */
                P1_X = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaEnd_X;
                P1_Y = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaEnd_Y;
                P2_X = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaStart_X;
                P2_Y = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaStart_Y;
            }
            
            LenuPoint2LineRelation = PubAI_CalPoint2LineRelation(P1_X,P1_Y,P2_X,P2_Y,LfObjX,LfObjY);
#if 0
            VS_PRINT("P1_X:%.3f,P1_Y:%.3f,P2_X:%.3f,P2_Y:%.3f,LfObjX:%.3f,LfObjY:%.3f,Point2LineRelation:%d\r\n",P1_X,P1_Y,P2_X,P2_Y,LfObjX,LfObjY,LenuPoint2LineRelation);
#endif

            if(LenuLineInx == LenuEndLine)
            {
                if((LenuPoint2LineRelation == PUB_POINT_ON_LINE_RIGHT)||(LenuPoint2LineRelation == PUB_POINT_ON_LINE))
                {
                    return MAP_AREA1;
                }
                else
                {
                    return MAP_AREA0;
                }
            }
            else
            {
                if((LenuPoint2LineRelation == PUB_POINT_ON_LINE_RIGHT)||(LenuPoint2LineRelation == PUB_POINT_ON_LINE))
                {
                    LenuObjArea = (ObjMAPAreaType)(LenuLineInx + 1);
                    return LenuObjArea;
                }
            }
        }
    }
    else
    {
        /* 障碍物在保杠的右侧，从中间往右边找 */
        LenuStartLine = MAP_LINE4_5;
        LenuEndLine = MAP_LINE7_8;
        
        for(LenuLineInx = MAP_LINE4_5; LenuLineInx <= MAP_LINE7_8; LenuLineInx++)
        {
            if(LenuSnsGroup == PDC_SNS_GROUP_FRONT)
            {
                /* 此处左右的相对视角是以P1点指向P2点来看的 */
                P2_X = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaEnd_X;
                P2_Y = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaEnd_Y;
                P1_X = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaStart_X;
                P1_Y = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaStart_Y;
            }
            else
            {
                /* 此处左右的相对视角是以P1点指向P2点来看的 */
                P1_X = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaEnd_X;
                P1_Y = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaEnd_Y;
                P2_X = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaStart_X;
                P2_Y = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaStart_Y;
            }

            LenuPoint2LineRelation = PubAI_CalPoint2LineRelation(P1_X,P1_Y,P2_X,P2_Y,LfObjX,LfObjY);

            if(LenuLineInx == LenuEndLine)
            {
                if(LenuPoint2LineRelation < PUB_POINT_ON_LINE_RIGHT)
                {
                    return MAP_AREA7;
                }
                else
                {
                    return MAP_AREA8;
                }
            }
            else
            {
                if(LenuPoint2LineRelation < PUB_POINT_ON_LINE_RIGHT)
                {
                    LenuObjArea = (ObjMAPAreaType)(LenuLineInx);
                    return LenuObjArea;
                }
            }
        }
    }

    return LenuObjArea;
}



/******************************************************************************
 * 函数名称: ObjCoorPointDetailAreaJudge
 * 
 * 功能描述: 判断探头坐标点所对应的分区位置及计算该点到保杠的距离
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-13 11:19   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static ObjMAPAreaType ObjCoorPointDetailAreaJudge(ObjCoorType *LpstrPointCarCoor,float *LpstrPointToBumperDis)
{
    ObjMAPAreaType LenuMapDetailArea = MAP_AREA_NONE;
    ObjCoorType LstrBumperStartPoint;
    ObjCoorType LstrBumperEndPoint;
    float LfMinAreaX;

    if(LpstrPointCarCoor->fObjX > (GstrMapAreaRam[PDC_SNS_GROUP_FRONT][MAP_LINE0_1].fAreaStart_X))
    {
        LenuMapDetailArea = SnsCoorPointJudgeObjArea(PDC_SNS_GROUP_FRONT,LpstrPointCarCoor->fObjX,LpstrPointCarCoor->fObjY);
        if((LenuMapDetailArea < MAP_AREA1))
        {
            *LpstrPointToBumperDis = LpstrPointCarCoor->fObjY - GstrMapAreaRam[PDC_SNS_GROUP_FRONT][MAP_LINE0_1].fAreaStart_Y;
        }
        else if(LenuMapDetailArea > MAP_AREA7)
        {
            *LpstrPointToBumperDis = -(LpstrPointCarCoor->fObjY - GstrMapAreaRam[PDC_SNS_GROUP_FRONT][MAP_LINE7_8].fAreaStart_Y);
        }
        else
        {
            LstrBumperStartPoint.fObjX = GstrMapAreaRam[PDC_SNS_GROUP_FRONT][LenuMapDetailArea-1].fAreaStart_X;
            LstrBumperStartPoint.fObjY = GstrMapAreaRam[PDC_SNS_GROUP_FRONT][LenuMapDetailArea-1].fAreaStart_Y;
            LstrBumperEndPoint.fObjX = GstrMapAreaRam[PDC_SNS_GROUP_FRONT][LenuMapDetailArea].fAreaStart_X;
            LstrBumperEndPoint.fObjY = GstrMapAreaRam[PDC_SNS_GROUP_FRONT][LenuMapDetailArea].fAreaStart_Y;
            *LpstrPointToBumperDis = PubAI_CalOnePointToSegmentDis(LpstrPointCarCoor->fObjX,LpstrPointCarCoor->fObjY,\
                LstrBumperStartPoint.fObjX,LstrBumperStartPoint.fObjY,LstrBumperEndPoint.fObjX,LstrBumperEndPoint.fObjY);
            if((LenuMapDetailArea > MAP_AREA2)&&(LenuMapDetailArea < MAP_AREA6))
            {
                LfMinAreaX = GstrMapAreaRam[PDC_SNS_GROUP_FRONT][LenuMapDetailArea-1].fAreaStart_X;
                if(LfMinAreaX > GstrMapAreaRam[PDC_SNS_GROUP_FRONT][LenuMapDetailArea].fAreaStart_X)
                {
                    LfMinAreaX = GstrMapAreaRam[PDC_SNS_GROUP_FRONT][LenuMapDetailArea].fAreaStart_X;
                }
                if(LpstrPointCarCoor->fObjX < (LfMinAreaX-40))
                {
                    *LpstrPointToBumperDis = 0;
                }
            }
        }
    }
    else if(LpstrPointCarCoor->fObjX < (GstrMapAreaRam[PDC_SNS_GROUP_REAR][MAP_LINE0_1].fAreaStart_X))
    {
        LenuMapDetailArea = SnsCoorPointJudgeObjArea(PDC_SNS_GROUP_REAR,LpstrPointCarCoor->fObjX,LpstrPointCarCoor->fObjY);
#if 0
        VS_PRINT("Rear Area:%d,ObjX:%.3f,ObjY:%.3f\r\n",LenuMapDetailArea,LpstrPointCarCoor->fObjX,LpstrPointCarCoor->fObjY);
#endif
        if((LenuMapDetailArea < MAP_AREA1))
        {
            *LpstrPointToBumperDis = LpstrPointCarCoor->fObjY - GstrMapAreaRam[PDC_SNS_GROUP_REAR][MAP_LINE0_1].fAreaStart_Y;
        }
        else if(LenuMapDetailArea > MAP_AREA7)
        {
            *LpstrPointToBumperDis = -(LpstrPointCarCoor->fObjY - GstrMapAreaRam[PDC_SNS_GROUP_REAR][MAP_LINE7_8].fAreaStart_Y);
        }
        else
        {
            LstrBumperStartPoint.fObjX = GstrMapAreaRam[PDC_SNS_GROUP_REAR][LenuMapDetailArea-1].fAreaStart_X;
            LstrBumperStartPoint.fObjY = GstrMapAreaRam[PDC_SNS_GROUP_REAR][LenuMapDetailArea-1].fAreaStart_Y;
            LstrBumperEndPoint.fObjX = GstrMapAreaRam[PDC_SNS_GROUP_REAR][LenuMapDetailArea].fAreaStart_X;
            LstrBumperEndPoint.fObjY = GstrMapAreaRam[PDC_SNS_GROUP_REAR][LenuMapDetailArea].fAreaStart_Y;
            *LpstrPointToBumperDis = PubAI_CalOnePointToSegmentDis(LpstrPointCarCoor->fObjX,LpstrPointCarCoor->fObjY,\
                LstrBumperStartPoint.fObjX,LstrBumperStartPoint.fObjY,LstrBumperEndPoint.fObjX,LstrBumperEndPoint.fObjY);
            if((LenuMapDetailArea > MAP_AREA2)&&(LenuMapDetailArea < MAP_AREA6))
            {
                LfMinAreaX = GstrMapAreaRam[PDC_SNS_GROUP_REAR][LenuMapDetailArea-1].fAreaStart_X;
                if(LfMinAreaX < GstrMapAreaRam[PDC_SNS_GROUP_REAR][LenuMapDetailArea].fAreaStart_X)
                {
                    LfMinAreaX = GstrMapAreaRam[PDC_SNS_GROUP_REAR][LenuMapDetailArea].fAreaStart_X;
                }
                if(LpstrPointCarCoor->fObjX > (LfMinAreaX+40))
                {
                    *LpstrPointToBumperDis = 0;
                }
            }
            if((LenuMapDetailArea == MAP_AREA3)||(LenuMapDetailArea == MAP_AREA5))
            {
                if(*LpstrPointToBumperDis > 50)
                {
                    *LpstrPointToBumperDis = *LpstrPointToBumperDis - 50;
                }
                else
                {
                    *LpstrPointToBumperDis = 0;
                }
            }
        }
    }
    else
    {
        if(LpstrPointCarCoor->fObjY > 900)
        {
            LenuMapDetailArea = MAP_AREA0;
            *LpstrPointToBumperDis = LpstrPointCarCoor->fObjY - GstrMapAreaRam[PDC_SNS_GROUP_FRONT][MAP_LINE0_1].fAreaStart_Y;
        }
        else if(LpstrPointCarCoor->fObjY < -900)
        {
            LenuMapDetailArea = MAP_AREA8;
            *LpstrPointToBumperDis = -(LpstrPointCarCoor->fObjY - GstrMapAreaRam[PDC_SNS_GROUP_FRONT][MAP_LINE7_8].fAreaStart_Y);
        }
        else
        {
            LenuMapDetailArea = MAP_AREA_NONE;
            *LpstrPointToBumperDis = 0;
        }
    }

    return LenuMapDetailArea;
}

uint16 GetSiseSnsLimitDis(uint16 Lu16MasDis,uint8 Lu8LowSpdLimitFlag)
{
    uint16 Lu16LimitDis;

    if(Lu16MasDis < 500)
    {
        Lu16LimitDis = 380;
    }
    else if(Lu16MasDis < 800)
    {
        if(Lu8LowSpdLimitFlag)
        {
            Lu16LimitDis = 200;
        }
        else
        {
            Lu16LimitDis = 430;
        }
    }
    else if(Lu16MasDis < 1000)
    {
        if(Lu8LowSpdLimitFlag)
        {
            Lu16LimitDis = 220;
        }
        else
        {
            Lu16LimitDis = 470;
        }
    }
    else if(Lu16MasDis < 1200)
    {
        if(Lu8LowSpdLimitFlag)
        {
            Lu16LimitDis = 250;
        }
        else
        {
            Lu16LimitDis = 520;
        }
    }
    else if(Lu16MasDis < 1400)
    {
        if(Lu8LowSpdLimitFlag)
        {
            Lu16LimitDis = 280;
        }
        else
        {
            Lu16LimitDis = 580;
        }
    }
    else if(Lu16MasDis < 1600)
    {
        if(Lu8LowSpdLimitFlag)
        {
            Lu16LimitDis = 300;
        }
        else
        {
            Lu16LimitDis = 730;
        }
    }
    else if(Lu16MasDis < 1800)
    {
        if(Lu8LowSpdLimitFlag)
        {
            Lu16LimitDis = 300;
        }
        else
        {
            Lu16LimitDis = 830;
        }
    }
    else if(Lu16MasDis < 2000)
    {
        if(Lu8LowSpdLimitFlag)
        {
            Lu16LimitDis = 300;
        }
        else
        {
            Lu16LimitDis = 900;
        }
    }
    else
    {
        if(Lu8LowSpdLimitFlag)
        {
            Lu16LimitDis = 320;
        }
        else
        {
            Lu16LimitDis = 950;
        }
    }
    
    return Lu16LimitDis;
}

/******************************************************************************
 * 函数名称: ObjSnsLeftCoorPointTransToCarAndOdoCoor
 * 
 * 功能描述: 左侦听信号组定位障碍物坐标转换到车辆坐标和Odo坐标
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-06 20:06   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static uint8 ObjSnsLeftCoorPointTransToCarAndOdoCoor(ObjCoorPointUnitType *LstrObjCoorPointUnit,PDCSnsGroupType LenuSnsGroup,PDCSnsChannelType LenuSnsCh)
{
    PDCSnsUseOdoType *LpStrPDCSnsUseOdo;
    ObjCoorType LstrObjCarCoor;
    ObjCoorType LstrObjOdoCoor;
    ObjMAPAreaType LenuObjPointDetailArea = MAP_AREA_NONE;
    float LfPointToBumperDis;
    uint8 Lu8CoorValidFlag = 1;
    uint16 Lu16ObjCarCoor_SubLimit;
    sint16 Ls16ObjCarCoor_SubLimitMin;
    uint8 Lu8LowSpdLimitFlag = 0;

    /* 更新探头坐标，用于后续转换探头坐标系到车辆坐标系 */
    PubAI_UpdateSnsInCarCoor(GstrRamSnsCoor[LenuSnsGroup][LenuSnsCh].cRadarX,GstrRamSnsCoor[LenuSnsGroup][LenuSnsCh].cRadarY,\
        GstrSnsCoorConvAngle[LenuSnsGroup].fLeftSinA[LenuSnsCh],GstrSnsCoorConvAngle[LenuSnsGroup].fLeftCosA[LenuSnsCh]);

    PubAI_TransObjSnsCoorToCarCoor(&LstrObjCoorPointUnit->strObjSnsCoor.fObjX,&LstrObjCoorPointUnit->strObjSnsCoor.fObjY,\
        &LstrObjCarCoor.fObjX,&LstrObjCarCoor.fObjY);
    LstrObjCoorPointUnit->strObjCarCoor.fObjX = LstrObjCarCoor.fObjX;
    LstrObjCoorPointUnit->strObjCarCoor.fObjY = LstrObjCarCoor.fObjY;

    /* 计算障碍物坐标点所属区域及到保杠的距离 */
    LenuObjPointDetailArea = ObjCoorPointDetailAreaJudge(&LstrObjCarCoor,&LfPointToBumperDis);
    LstrObjCoorPointUnit->fObjToBumperDis = LfPointToBumperDis;
    LstrObjCoorPointUnit->enuObjArea = LenuObjPointDetailArea;

    PubAI_TransObjCarCoorToOdoCoor(&LstrObjCarCoor.fObjX,&LstrObjCarCoor.fObjY,\
        &LstrObjOdoCoor.fObjX,&LstrObjOdoCoor.fObjY);
    
    LstrObjCoorPointUnit->strObjOdoCoor.fObjX = LstrObjOdoCoor.fObjX;
    LstrObjCoorPointUnit->strObjOdoCoor.fObjY = LstrObjOdoCoor.fObjY;
    if(LfPointToBumperDis > POINT_CLOUD_VALID_TO_BUMPER_DIS)
    {
        Lu8CoorValidFlag = 2;
    }
    else
    {
        if(LenuSnsCh == PDC_SNS_CH_FRS_RRS)
        {
            if(LenuSnsGroup == 0x00)
            {
                LstrObjCoorPointUnit->fObjCarCoor_Sub = LstrObjCoorPointUnit->strObjCarCoor.fObjX - GstrRamSnsCoor[LenuSnsGroup][LenuSnsCh].cRadarX;
                /** @brief: 针对FLS 在车速较低时，降低对应的限制值，以防止超出画多  */
                if(Gu16CarSpdForSnsUse < 450)
                {
                    Lu8LowSpdLimitFlag = 1;
                }
            }
            else
            {
                LstrObjCoorPointUnit->fObjCarCoor_Sub = GstrRamSnsCoor[LenuSnsGroup][LenuSnsCh].cRadarX - LstrObjCoorPointUnit->strObjCarCoor.fObjX;
            }
            
            Lu16ObjCarCoor_SubLimit = GetSiseSnsLimitDis(LstrObjCoorPointUnit->u16MasterDis,Lu8LowSpdLimitFlag);

            if(LenuSnsGroup == 0x00)
            {
                Ls16ObjCarCoor_SubLimitMin = -200;
                /* 针对FRS探头远离大墙场景画不出Map的场景修改 */
                if((Gs16CarStrAngleForSnsUse > 100)||(GstrSideAreaMap[SIDE_SNS_FRS].u8SnsFarawayLineFlag))
                {
                    Ls16ObjCarCoor_SubLimitMin = -600;
                }
            }
            else
            {
                Ls16ObjCarCoor_SubLimitMin = -220;//修改前 -220
            }
            
            if((LstrObjCoorPointUnit->fObjCarCoor_Sub > Lu16ObjCarCoor_SubLimit)||(LstrObjCoorPointUnit->fObjCarCoor_Sub < Ls16ObjCarCoor_SubLimitMin))
            {
                Lu8CoorValidFlag = 0;
#if 0
                if((GfMessageTime > 38)&&(GfMessageTime < 39.5))
                {
                    if((LenuSnsGroup == 0x01)&&(LenuSnsCh == 0x05))
                    {
                        VS_PRINT("Time:%.3f,RRS Left Car Coor Invalid,CarCoor_Sub:%.3f,SubLimit:%d,SubLimitMin:%d\r\n",GfMessageTime,\
                            LstrObjCoorPointUnit->fObjCarCoor_Sub,Lu16ObjCarCoor_SubLimit,Ls16ObjCarCoor_SubLimitMin);
                    }
                }
#endif
            }
            else
            {
                /* 针对侧边雷达正对大墙的场景，主发回波宽度大于侦听回波宽度在3倍以上的，在此处进行修正为障碍物正对探头 */
                if(GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh].eCarDir == SNS_CAR_STOP)
                {
#if 1
                    if((LstrObjCoorPointUnit->u16MasterDis < 2000)&&(LstrObjCoorPointUnit->enuObjType >= OBJ_BIGWALL_TYPE))
                    {
                        if(LstrObjCoorPointUnit->u16MasterHeight > (LstrObjCoorPointUnit->u16ListenHeight*2.5))
                        {
                            LstrObjCarCoor.fObjX = GstrRamSnsCoor[LenuSnsGroup][LenuSnsCh].cRadarX;
                            LstrObjCarCoor.fObjY = GstrRamSnsCoor[LenuSnsGroup][LenuSnsCh].cRadarY-LstrObjCoorPointUnit->u16MasterDis;
                            LstrObjCoorPointUnit->strObjCarCoor.fObjX = LstrObjCarCoor.fObjX;
                            LstrObjCoorPointUnit->strObjCarCoor.fObjY = LstrObjCarCoor.fObjY;
                            PubAI_TransObjCarCoorToOdoCoor(&LstrObjCarCoor.fObjX,&LstrObjCarCoor.fObjY,\
                                &LstrObjOdoCoor.fObjX,&LstrObjOdoCoor.fObjY);
                            LstrObjCoorPointUnit->strObjOdoCoor.fObjX = LstrObjOdoCoor.fObjX;
                            LstrObjCoorPointUnit->strObjOdoCoor.fObjY = LstrObjOdoCoor.fObjY;
                        }
                    }
#endif
                }
                else if(GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh].eCarDir == SNS_CAR_BACKWARD)
                {
#if 0
                    if((LenuSnsGroup == 0x01)&&(GstrParkingGuidenceData.fCarAngleSub > 0.7))
                    {
                        /* 针对后侧雷达倒退阶段的坐标矫正 */
                        if((LstrObjCoorPointUnit->u16MasterDis < 1200)&&(LstrObjCoorPointUnit->enuObjType >= OBJ_BIGWALL_TYPE))
                        {
                            if(LstrObjCoorPointUnit->u16MasterHeight > (LstrObjCoorPointUnit->u16ListenHeight*2.5))
                            {
                                LstrObjCarCoor.fObjX = GstrRamSnsCoor[LenuSnsGroup][LenuSnsCh].cRadarX;
                                LstrObjCarCoor.fObjY = GstrRamSnsCoor[LenuSnsGroup][LenuSnsCh].cRadarY-LstrObjCoorPointUnit->u16MasterDis;
                                LstrObjCoorPointUnit->strObjCarCoor.fObjX = LstrObjCarCoor.fObjX;
                                LstrObjCoorPointUnit->strObjCarCoor.fObjY = LstrObjCarCoor.fObjY;
                                PubAI_TransObjCarCoorToOdoCoor(&LstrObjCarCoor.fObjX,&LstrObjCarCoor.fObjY,\
                                    &LstrObjOdoCoor.fObjX,&LstrObjOdoCoor.fObjY);
                                LstrObjCoorPointUnit->strObjOdoCoor.fObjX = LstrObjOdoCoor.fObjX;
                                LstrObjCoorPointUnit->strObjOdoCoor.fObjY = LstrObjOdoCoor.fObjY;
                            }
                        }
                    }
#endif
                }
            }
            /* 针对垂直泊车，对向有障碍物的情况下，前提一把侧雷达入侵的特殊处理--2024-01-15 */
            if((Lu8CoorValidFlag)&&(LenuSnsGroup == 0x00))
            {
                if(GstrParkingGuidenceData.fCarAngleSub > CAR_SEARCH_TO_GUIDANCE_40_ANGLE)
                {
                    if(LstrObjCoorPointUnit->u16MasterDis > 1300)
                    {
                        Lu8CoorValidFlag = 0;
                    }
                    else
                    {
                        if(GstrMapSnsRealTimeDis[0][3].u16MasDis < 1500)
                        {
                            if(LstrObjCoorPointUnit->u16MasterDis > 600)
                            {
                                Lu8CoorValidFlag = 0;
                            }
                        }
                        else if(GstrMapSnsRealTimeDis[0][3].u16MasDis < 2000)
                        {
                            if(LstrObjCoorPointUnit->u16MasterDis > 1000)
                            {
                                Lu8CoorValidFlag = 0;
                            }
                        }
                    }
                }
            }
        }
    }
    return Lu8CoorValidFlag;
}



/******************************************************************************
 * 函数名称: ObjSnsRightCoorPointTransToCarAndOdoCoor
 * 
 * 功能描述: 右侦听坐标
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-06 20:06   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static uint8 ObjSnsRightCoorPointTransToCarAndOdoCoor(ObjCoorPointUnitType *LstrObjCoorPointUnit,PDCSnsGroupType LenuSnsGroup,PDCSnsChannelType LenuSnsCh)
{
    PDCSnsUseOdoType *LpStrPDCSnsUseOdo;
    ObjCoorType LstrObjCarCoor;
    ObjCoorType LstrObjOdoCoor;
    ObjMAPAreaType LenuObjPointDetailArea = MAP_AREA_NONE;
    float LfPointToBumperDis;
    uint8 Lu8CoorValidFlag = 1;
    uint16 Lu16ObjCarCoor_SubLimit;
    sint16 Ls16ObjCarCoor_SubLimitMin;
    uint8 Lu8LowSpdLimitFlag = 0;

    PubAI_UpdateSnsInCarCoor(GstrRamSnsCoor[LenuSnsGroup][LenuSnsCh].cRadarX,GstrRamSnsCoor[LenuSnsGroup][LenuSnsCh].cRadarY,\
        GstrSnsCoorConvAngle[LenuSnsGroup].fRightSinA[LenuSnsCh],GstrSnsCoorConvAngle[LenuSnsGroup].fRightCosA[LenuSnsCh]);
    PubAI_TransObjSnsCoorToCarCoor(&LstrObjCoorPointUnit->strObjSnsCoor.fObjX,&LstrObjCoorPointUnit->strObjSnsCoor.fObjY,\
        &LstrObjCarCoor.fObjX,&LstrObjCarCoor.fObjY);
    LstrObjCoorPointUnit->strObjCarCoor.fObjX = LstrObjCarCoor.fObjX;
    LstrObjCoorPointUnit->strObjCarCoor.fObjY = LstrObjCarCoor.fObjY;

    /* 计算障碍物坐标点所属区域及到保杠的距离 */
#if 1
    LenuObjPointDetailArea = ObjCoorPointDetailAreaJudge(&LstrObjCarCoor,&LfPointToBumperDis);
    LstrObjCoorPointUnit->fObjToBumperDis = LfPointToBumperDis;
    LstrObjCoorPointUnit->enuObjArea = LenuObjPointDetailArea;
#endif

    PubAI_TransObjCarCoorToOdoCoor(&LstrObjCarCoor.fObjX,&LstrObjCarCoor.fObjY,\
        &LstrObjOdoCoor.fObjX,&LstrObjOdoCoor.fObjY);
    
    LstrObjCoorPointUnit->strObjOdoCoor.fObjX = LstrObjOdoCoor.fObjX;
    LstrObjCoorPointUnit->strObjOdoCoor.fObjY = LstrObjOdoCoor.fObjY;

    if(LfPointToBumperDis > POINT_CLOUD_VALID_TO_BUMPER_DIS)
    {
        Lu8CoorValidFlag = 2;
    }
    else
    {
        if(LenuSnsCh == PDC_SNS_CH_FLS_RLS)
        {
            if(LenuSnsGroup == 0x00)
            {
                LstrObjCoorPointUnit->fObjCarCoor_Sub = LstrObjCoorPointUnit->strObjCarCoor.fObjX - GstrRamSnsCoor[LenuSnsGroup][LenuSnsCh].cRadarX;
                /** @brief: 针对FLS 在车速较低时，降低对应的限制值，以防止超出画多  */
                if(Gu16CarSpdForSnsUse < 450)
                {
                    Lu8LowSpdLimitFlag = 1;
                }
            }
            else
            {
                LstrObjCoorPointUnit->fObjCarCoor_Sub = GstrRamSnsCoor[LenuSnsGroup][LenuSnsCh].cRadarX - LstrObjCoorPointUnit->strObjCarCoor.fObjX;
            }

            Lu16ObjCarCoor_SubLimit = GetSiseSnsLimitDis(LstrObjCoorPointUnit->u16MasterDis,Lu8LowSpdLimitFlag);
            
            if(LenuSnsGroup == 0x00)
            {
                Ls16ObjCarCoor_SubLimitMin = -200;
                /* 针对FLS探头远离大墙场景画不出Map的场景修改 */
                if((Gs16CarStrAngleForSnsUse < -100)||(GstrSideAreaMap[SIDE_SNS_FLS].u8SnsFarawayLineFlag))
                {
                    Ls16ObjCarCoor_SubLimitMin = -600;
                }
            }
            else
            {
                Ls16ObjCarCoor_SubLimitMin = -220;//修改前 -220
            }

#if 0
            if((GfMessageTime > 8)&&(GfMessageTime < 8.8))
            {
                VS_PRINT("Time:%.3f,FLS Right Car Coor Sub:%.3f,CarCoor_SubLimit:%d,CarCoor_SubLimitMin:%d,Sns_X:%.3f,Sns_Y:%.3f,Car_X:%.3f,Car_Y:%.3f\r\n",GfMessageTime,\
                    LstrObjCoorPointUnit->fObjCarCoor_Sub,Lu16ObjCarCoor_SubLimit,Ls16ObjCarCoor_SubLimitMin,\
                    LstrObjCoorPointUnit->strObjSnsCoor.fObjX,LstrObjCoorPointUnit->strObjSnsCoor.fObjY,\
                    LstrObjCoorPointUnit->strObjCarCoor.fObjX,LstrObjCoorPointUnit->strObjCarCoor.fObjY);
            }
#endif

            
            if((LstrObjCoorPointUnit->fObjCarCoor_Sub > Lu16ObjCarCoor_SubLimit)||(LstrObjCoorPointUnit->fObjCarCoor_Sub < Ls16ObjCarCoor_SubLimitMin))
            {
                Lu8CoorValidFlag = 0;
            }
            else
            {
                /* 针对侧边雷达正对大墙的场景，主发回波宽度大于侦听回波宽度在3倍以上的，在此处进行修正为障碍物正对探头 */
                if(GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh].eCarDir == SNS_CAR_STOP)
                {
#if 1
                    if((LstrObjCoorPointUnit->u16MasterDis < 2000)&&(LstrObjCoorPointUnit->enuObjType >= OBJ_BIGWALL_TYPE))
                    {
                        if(LstrObjCoorPointUnit->u16MasterHeight > (LstrObjCoorPointUnit->u16ListenHeight*2.5))
                        {
                            LstrObjCarCoor.fObjX = GstrRamSnsCoor[LenuSnsGroup][LenuSnsCh].cRadarX;
                            LstrObjCarCoor.fObjY = GstrRamSnsCoor[LenuSnsGroup][LenuSnsCh].cRadarY+LstrObjCoorPointUnit->u16MasterDis;
                            LstrObjCoorPointUnit->strObjCarCoor.fObjX = LstrObjCarCoor.fObjX;
                            LstrObjCoorPointUnit->strObjCarCoor.fObjY = LstrObjCarCoor.fObjY;
                            PubAI_TransObjCarCoorToOdoCoor(&LstrObjCarCoor.fObjX,&LstrObjCarCoor.fObjY,\
                                &LstrObjOdoCoor.fObjX,&LstrObjOdoCoor.fObjY);
                            
                            LstrObjCoorPointUnit->strObjOdoCoor.fObjX = LstrObjOdoCoor.fObjX;
                            LstrObjCoorPointUnit->strObjOdoCoor.fObjY = LstrObjOdoCoor.fObjY;
                        }
                    }
#endif
                }
                else if(GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh].eCarDir == SNS_CAR_BACKWARD)
                {
#if 0
                    if((LenuSnsGroup == 0x01)&&(GstrParkingGuidenceData.fCarAngleSub > 0.7))
                    {
                        /* 针对后侧雷达倒退阶段的坐标矫正 */
                        if((LstrObjCoorPointUnit->u16MasterDis < 1200)&&(LstrObjCoorPointUnit->enuObjType >= OBJ_BIGWALL_TYPE))
                        {
                            if(LstrObjCoorPointUnit->u16MasterHeight > (LstrObjCoorPointUnit->u16ListenHeight*2.5))
                            {
                                LstrObjCarCoor.fObjX = GstrRamSnsCoor[LenuSnsGroup][LenuSnsCh].cRadarX;
                                LstrObjCarCoor.fObjY = GstrRamSnsCoor[LenuSnsGroup][LenuSnsCh].cRadarY+LstrObjCoorPointUnit->u16MasterDis;
                                LstrObjCoorPointUnit->strObjCarCoor.fObjX = LstrObjCarCoor.fObjX;
                                LstrObjCoorPointUnit->strObjCarCoor.fObjY = LstrObjCarCoor.fObjY;
                                PubAI_TransObjCarCoorToOdoCoor(&LstrObjCarCoor.fObjX,&LstrObjCarCoor.fObjY,\
                                    &LstrObjOdoCoor.fObjX,&LstrObjOdoCoor.fObjY);
                                
                                LstrObjCoorPointUnit->strObjOdoCoor.fObjX = LstrObjOdoCoor.fObjX;
                                LstrObjCoorPointUnit->strObjOdoCoor.fObjY = LstrObjOdoCoor.fObjY;
                            }
                        }
                    }
#endif
                }

            }

            /* 针对垂直泊车，对向有障碍物的情况下，前提一把侧雷达入侵的特殊处理--2024-01-15 */
            if((Lu8CoorValidFlag)&&(LenuSnsGroup == 0x00))
            {
                if(GstrParkingGuidenceData.fCarAngleSub > CAR_SEARCH_TO_GUIDANCE_40_ANGLE)
                {
                    if(LstrObjCoorPointUnit->u16MasterDis > 1300)
                    {
                        Lu8CoorValidFlag = 0;
                    }
                    else
                    {
                        if(GstrMapSnsRealTimeDis[0][2].u16MasDis < 1500)
                        {
                            if(LstrObjCoorPointUnit->u16MasterDis > 600)
                            {
                                Lu8CoorValidFlag = 0;
                            }
                        }
                        else if(GstrMapSnsRealTimeDis[0][2].u16MasDis < 2000)
                        {
                            if(LstrObjCoorPointUnit->u16MasterDis > 1000)
                            {
                                Lu8CoorValidFlag = 0;
                            }
                        }
                    }
                }
            }
        }
    }
    return Lu8CoorValidFlag;
}


/******************************************************************************
 * 函数名称: ObjSnsMasterCoorPointTransToCarAndOdoCoor
 * 
 * 功能描述: 主发坐标转换
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-06 21:28   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void ObjSnsMasterCoorPointTransToCarAndOdoCoor(ObjCoorPointUnitType *LstrObjCoorPointUnit,PDCSnsGroupType LenuSnsGroup,PDCSnsChannelType LenuSnsCh)
{
    ObjCoorType LstrObjCarCoor;
    ObjCoorType LstrObjOdoCoor;
    ObjMAPAreaType LenuObjPointDetailArea = MAP_AREA_NONE;
    float LfPointToBumperDis;

    PubAI_UpdateSnsInCarCoor(GstrRamSnsCoor[LenuSnsGroup][LenuSnsCh].cRadarX,GstrRamSnsCoor[LenuSnsGroup][LenuSnsCh].cRadarY,\
        GstrSnsOutAngle[LenuSnsGroup].fSinA[LenuSnsCh],GstrSnsOutAngle[LenuSnsGroup].fCosA[LenuSnsCh]);

    PubAI_TransObjSnsCoorToCarCoor(&LstrObjCoorPointUnit->strObjSnsCoor.fObjX,&LstrObjCoorPointUnit->strObjSnsCoor.fObjY,\
        &LstrObjCarCoor.fObjX,&LstrObjCarCoor.fObjY);
    LstrObjCoorPointUnit->strObjCarCoor.fObjX = LstrObjCarCoor.fObjX;
    LstrObjCoorPointUnit->strObjCarCoor.fObjY = LstrObjCarCoor.fObjY;

    /* 计算障碍物坐标点所属区域及到保杠的距离 */
#if 1
    LenuObjPointDetailArea = ObjCoorPointDetailAreaJudge(&LstrObjCarCoor,&LfPointToBumperDis);
    LstrObjCoorPointUnit->fObjToBumperDis = LfPointToBumperDis;
    LstrObjCoorPointUnit->enuObjArea = LenuObjPointDetailArea;
#endif

    PubAI_TransObjCarCoorToOdoCoor(&LstrObjCarCoor.fObjX,&LstrObjCarCoor.fObjY,\
        &LstrObjOdoCoor.fObjX,&LstrObjOdoCoor.fObjY);
    
    LstrObjCoorPointUnit->strObjOdoCoor.fObjX = LstrObjOdoCoor.fObjX;
    LstrObjCoorPointUnit->strObjOdoCoor.fObjY = LstrObjOdoCoor.fObjY;
}

void CalSnsListenDisByCarCoorLimit(void)
{
    float LfMasterDis = 1288;
    float LfSpace = 373;
    float LfCarX_LimitMax = 680;
    float LfCarX_LimitMin = -300;
    float LfMasterDisSquare,LfSpaceSquare,LfMasterMultSpace;
    
    float LfCarX_MaxCoor,LfCarX_MinCoor;
    float LfCarY_MaxCoor,LfCarY_MinCoor;

    
    float LfSnsX_MaxCoor,LfSnsX_MinCoor;
    float LfSnsY_MaxCoor,LfSnsY_MinCoor;

    float LfAngleMax,LfAngleMin;
    float LfListenMaxDis,LfListenMinDis;
    uint8 Lu8Group,Lu8nsCh;

    Lu8Group = 0;
    Lu8nsCh = 0;

    LfMasterDisSquare = LfMasterDis*LfMasterDis;
    LfSpaceSquare = LfSpace*LfSpace;
    LfMasterMultSpace = 2*LfMasterDis*LfSpace;


    LfCarX_MaxCoor = GstrRamSnsCoor[Lu8Group][Lu8nsCh].cRadarX+LfCarX_LimitMax;
    LfCarY_MaxCoor = (LfMasterDis*LfMasterDis) - (LfCarX_LimitMax*LfCarX_LimitMax);
    LfCarY_MaxCoor = powf(LfCarY_MaxCoor,0.5);
#if 0
    LfCarY_MaxCoor = GstrRamSnsCoor[Lu8Group][Lu8nsCh].cRadarY - LfCarY_MaxCoor;
#else
    LfCarY_MaxCoor = LfCarY_MaxCoor + GstrRamSnsCoor[Lu8Group][Lu8nsCh].cRadarY;
#endif

    /* 更新探头坐标，用于后续转换探头坐标系到车辆坐标系 */
#if 0
    PubAI_UpdateSnsInCarCoor(GstrRamSnsCoor[Lu8Group][Lu8nsCh].cRadarX,GstrRamSnsCoor[Lu8Group][Lu8nsCh].cRadarY,\
        GstrSnsCoorConvAngle[Lu8Group].fLeftSinA[Lu8nsCh],GstrSnsCoorConvAngle[Lu8Group].fLeftCosA[Lu8nsCh]);
#else
    PubAI_UpdateSnsInCarCoor(GstrRamSnsCoor[Lu8Group][Lu8nsCh].cRadarX,GstrRamSnsCoor[Lu8Group][Lu8nsCh].cRadarY,\
        GstrSnsCoorConvAngle[Lu8Group].fRightSinA[Lu8nsCh],GstrSnsCoorConvAngle[Lu8Group].fRightCosA[Lu8nsCh]);
#endif

    PubAI_TransObjCarCoorToSnsCoor(&LfCarX_MaxCoor,&LfCarY_MaxCoor,&LfSnsX_MaxCoor,&LfSnsY_MaxCoor);

    VS_PRINT("Car_Max_X_Coor:%.3f,Car_Max_Y_Coor:%.3f,Sns_Max_X_Coor:%.3f,Sns_Max_Y_Coor:%.3f\r\n",LfCarX_MaxCoor,LfCarY_MaxCoor,\
        LfSnsX_MaxCoor,LfSnsY_MaxCoor);
    LfAngleMax = atan2f(LfSnsY_MaxCoor,LfSnsX_MaxCoor);
#if 0
    LfAngleMax = CAR_SEARCH_TO_GUIDANCE_90_ANGLE - LfAngleMax;
#else
    LfAngleMax = LfAngleMax + CAR_SEARCH_TO_GUIDANCE_90_ANGLE;
#endif
    LfAngleMax = acosf(LfCarX_LimitMax/LfMasterDis) + (CAR_SEARCH_TO_GUIDANCE_90_ANGLE - GstrSnsCoorConvAngle[Lu8Group].fAngle[Lu8nsCh]);

    LfMasterMultSpace = LfMasterMultSpace*(cosf(LfAngleMax));
    LfListenMaxDis = LfMasterDisSquare + LfSpaceSquare - LfMasterMultSpace;
    LfListenMaxDis = powf(LfListenMaxDis,0.5);
    LfListenMaxDis += LfMasterDis;




    LfCarX_MinCoor = GstrRamSnsCoor[Lu8Group][Lu8nsCh].cRadarX+LfCarX_LimitMin;
    LfCarY_MinCoor = (LfMasterDis*LfMasterDis) - (LfCarX_LimitMin*LfCarX_LimitMin);
    LfCarY_MinCoor = powf(LfCarY_MinCoor,0.5);
#if 0
    LfCarY_MinCoor = GstrRamSnsCoor[Lu8Group][Lu8nsCh].cRadarY - LfCarY_MinCoor;
#else
    LfCarY_MinCoor = LfCarY_MinCoor + GstrRamSnsCoor[Lu8Group][Lu8nsCh].cRadarY;
#endif
    PubAI_TransObjCarCoorToSnsCoor(&LfCarX_MinCoor,&LfCarY_MinCoor,&LfSnsX_MinCoor,&LfSnsY_MinCoor);
    VS_PRINT("Car_Min_X_Coor:%.3f,Car_Min_Y_Coor:%.3f,Sns_Min_X_Coor:%.3f,Sns_Min_Y_Coor:%.3f\r\n",LfCarX_MinCoor,LfCarY_MinCoor,\
        LfSnsX_MinCoor,LfSnsY_MinCoor);

    LfAngleMin = atan2f(LfSnsY_MinCoor,LfSnsX_MinCoor);
#if 0
    LfAngleMin = CAR_SEARCH_TO_GUIDANCE_90_ANGLE - LfAngleMin;
#else
    LfAngleMin = LfAngleMin + CAR_SEARCH_TO_GUIDANCE_90_ANGLE;
#endif
    LfMasterMultSpace = 2*LfMasterDis*LfSpace;
    LfMasterMultSpace = LfMasterMultSpace*(cosf(LfAngleMin));
    LfListenMinDis = LfMasterDisSquare + LfSpaceSquare - LfMasterMultSpace;
    LfListenMinDis = powf(LfListenMinDis,0.5);
    LfListenMinDis += LfMasterDis;
        
    VS_PRINT("ListenMaxDis:%.3f,MinDis:%.3f\r\n\r\n",LfListenMaxDis,LfListenMinDis);

}


/******************************************************************************
 * 函数名称: FrontSideMinListenDisLimit
 * 
 * 功能描述: 前侧雷达最小侦听距离的限制，在探头原始端就开始限制，防止噪点被引入后导致坐标计算错误
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-03-25 19:51   V0.1      AntonyFang   初次发布
 ******************************************************************************/
uint16 FrontSideMinListenDisLimit(uint16 Lu16MasterDis)
{
    float LfAngle;
    float LfSpace;
    float LfMasterDisSquare,LfSpaceSquare,LfMasterMultSpace;
    float Lu16ObjCarCoor_SubLimit;
    float LfMasterDis;
    float LfMinListenDis;

    LfMasterDis = Lu16MasterDis;
    LfSpace = GstrSnsAdjacentDis[0].fRadarRightDis[0];
    LfMasterDisSquare = Lu16MasterDis*Lu16MasterDis;
    LfSpaceSquare = LfSpace*LfSpace;
    LfMasterMultSpace = 2*Lu16MasterDis*LfSpace;
    if(Lu16MasterDis < 500)
    {
        Lu16ObjCarCoor_SubLimit = 380;
    }
    else if(Lu16MasterDis < 800)
    {
        Lu16ObjCarCoor_SubLimit = 430;
    }
    else if(Lu16MasterDis < 1000)
    {
        Lu16ObjCarCoor_SubLimit = 470;
    }
    else if(Lu16MasterDis < 1200)
    {
        Lu16ObjCarCoor_SubLimit = 520;
    }
    else if(Lu16MasterDis < 1400)
    {
        Lu16ObjCarCoor_SubLimit = 580;
    }
    else if(Lu16MasterDis < 1600)
    {
        Lu16ObjCarCoor_SubLimit = 730;
    }
    else if(Lu16MasterDis < 1800)
    {
        Lu16ObjCarCoor_SubLimit = 830;
    }
    else if(Lu16MasterDis < 2000)
    {
        Lu16ObjCarCoor_SubLimit = 930;
    }
    else
    {
        Lu16ObjCarCoor_SubLimit = 980;
    }

    LfAngle = acosf(Lu16ObjCarCoor_SubLimit/LfMasterDis) + (CAR_SEARCH_TO_GUIDANCE_90_ANGLE - GstrSnsCoorConvAngle[0].fAngle[0]);
    LfMasterMultSpace = LfMasterMultSpace*(cosf(LfAngle));
    LfMinListenDis = LfMasterDisSquare + LfSpaceSquare - LfMasterMultSpace;
    LfMinListenDis = powf(LfMinListenDis,0.5);
    LfMinListenDis += LfMasterDis;

    return (uint16)LfMinListenDis;
}

/******************************************************************************
 * 函数名称: ObjCoorPointUpdate
 * 
 * 功能描述: 障碍物原始坐标点更新，直接使用信号组进行更新
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-07 15:32   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void ObjCoorPointUpdate(PDCSnsGroupType LenuSnsGroup,PDCSnsChannelType LenuSnsCh)
{
    SnsObjCoorPointType *LpstrSnsObjCoorPoint;
    MapSigGroupDataCacheType *LpstrMapSigGroup;
    PDCSnsUseOdoType *LpStrPDCSnsUseOdo;
    Sns_CycleType LenuCurInx;
    uint8 Lu8MasterSigCnt;
    uint8 Lu8LeftSigCnt;
    uint8 Lu8RightSigCnt;
    uint8 Lu8MasterObjCnt;
    uint8 Lu8LeftObjCnt;
    uint8 Lu8RightObjCnt;
    uint8 Lu8ListenStartInx;
    uint16 Lu16SnsSpace;
    uint16 Lu16ListenDis;
    uint16 Lu16ListenFirstHeight;
    uint16 Lu16ListenSecondHeight;
    uint16 Lu16MasterDis;
    uint16 Lu16MasterFirstHeight;
    uint16 Lu16MasterSecondHeight;
    uint16 Lu16SecMinListenDis;
    SigGroupRelationType LenuMasterSigGroupRelation;
    SigGroupRelationType LenuListenSigGroupRelation;
    
    ObjValidListenDisType LstrObjValidListenDis;
    ObjCoorType LstrObjSnsCoor;
    uint8 Lu8CoorValidFlag = 0;
    uint16 Lu16NearMasterDis = 0;

    LpstrMapSigGroup = &GstrMapSigGroupDataCache[LenuSnsGroup][LenuSnsCh];
    LenuCurInx = LpstrMapSigGroup->enuCurIndex;
    LpstrSnsObjCoorPoint = &GstrObjCoorPoint[LenuSnsGroup][LenuSnsCh];
    LpstrSnsObjCoorPoint->eMeasType = LpstrMapSigGroup->SysDataBuf[LenuCurInx].eMeasType;
    LpstrSnsObjCoorPoint->u32MeasTime = LpstrMapSigGroup->SysDataBuf[LenuCurInx].u32SysTime;
#if 0
    if((LenuSnsGroup == 0x00)&&(LenuSnsCh == 0x05))
    {
        VS_PRINT("FRS MessageTime:%.3f,MeasTime:%ld,SysTime:%ld\r\n",GfMessageTime,LpstrSnsObjCoorPoint->u32MeasTime,LpstrMapSigGroup->SysDataBuf[LenuCurInx].u32SysTime);
    }
#endif

    LpStrPDCSnsUseOdo = &GstrPDCSnsUseOdo;
    /* 更新Odo坐标 */
    PubAI_UpdateCarOdoCoor(LpStrPDCSnsUseOdo->fCar_X_Coor,LpStrPDCSnsUseOdo->fCar_Y_Coor,LpStrPDCSnsUseOdo->fCar_SinYawAngle,LpStrPDCSnsUseOdo->fCar_CosYawAngle);

    LpstrSnsObjCoorPoint->u8LeftObjCoorCnt = 0;
    LpstrSnsObjCoorPoint->u8RightObjCoorCnt = 0;
    LpstrSnsObjCoorPoint->u8MasterObjCoorCnt = 0;
    
    LpstrSnsObjCoorPoint->u8LeftAlredayToBufFlag = 0;
    LpstrSnsObjCoorPoint->u8RightAlredayToBufFlag = 0;
    LpstrSnsObjCoorPoint->u8MasterAlredayToBufFlag = 0;
    
    /* 遍历计算左侦听障碍物坐标 */
    Lu8LeftObjCnt = 0;
    Lu8ListenStartInx = 0;
    LpstrSnsObjCoorPoint->LeftData[0].u8CoorValidFlag = 0;
    LpstrSnsObjCoorPoint->LeftData[1].u8CoorValidFlag = 0; 
    LpstrSnsObjCoorPoint->LeftData[0].u8SnsNoiseCnt = LpstrMapSigGroup->SysDataBuf[LenuCurInx].u8SnsNoiseCnt;
    LpstrSnsObjCoorPoint->LeftData[1].u8SnsNoiseCnt = LpstrMapSigGroup->SysDataBuf[LenuCurInx].u8SnsNoiseCnt;
    
    for(Lu8MasterSigCnt = 0; Lu8MasterSigCnt < LpstrMapSigGroup->MasterBuf[LenuCurInx].u8SigGroupCnt;Lu8MasterSigCnt++)
    {
        /* 提取主发探头的距离、回波关系 */
        Lu16MasterDis = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16ActualDis[Lu8MasterSigCnt];        
        Lu16MasterFirstHeight = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16FirstEchoHeight[Lu8MasterSigCnt];
        Lu16MasterSecondHeight = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16SecondEchoHeight[Lu8MasterSigCnt];
        LenuMasterSigGroupRelation = JudgeSnsSigGroupEchoType(LpstrMapSigGroup->MasterBuf[LenuCurInx].u16FirstEchoDis[Lu8MasterSigCnt],LpstrMapSigGroup->MasterBuf[LenuCurInx].u16FirstEchoHeight[Lu8MasterSigCnt],\
            LpstrMapSigGroup->MasterBuf[LenuCurInx].u16SecondEchoDis[Lu8MasterSigCnt],LpstrMapSigGroup->MasterBuf[LenuCurInx].u16SecondEchoHeight[Lu8MasterSigCnt],LpstrMapSigGroup->SysDataBuf[LenuCurInx].eMeasType);
        Lu8CoorValidFlag = 0;
        for(Lu8LeftSigCnt = Lu8ListenStartInx; Lu8LeftSigCnt < LpstrMapSigGroup->LeftBuf[LenuCurInx].u8SigGroupCnt;Lu8LeftSigCnt++)
        {
            Lu16ListenDis = LpstrMapSigGroup->LeftBuf[LenuCurInx].u16ActualDis[Lu8LeftSigCnt]*2;
            Lu16ListenFirstHeight = LpstrMapSigGroup->LeftBuf[LenuCurInx].u16FirstEchoHeight[Lu8LeftSigCnt];
            Lu16ListenSecondHeight = LpstrMapSigGroup->LeftBuf[LenuCurInx].u16SecondEchoHeight[Lu8LeftSigCnt];
            LenuListenSigGroupRelation = JudgeSnsSigGroupEchoType(LpstrMapSigGroup->LeftBuf[LenuCurInx].u16FirstEchoDis[Lu8LeftSigCnt],LpstrMapSigGroup->LeftBuf[LenuCurInx].u16FirstEchoHeight[Lu8LeftSigCnt],\
                LpstrMapSigGroup->LeftBuf[LenuCurInx].u16SecondEchoDis[Lu8LeftSigCnt],LpstrMapSigGroup->LeftBuf[LenuCurInx].u16SecondEchoHeight[Lu8LeftSigCnt],LpstrMapSigGroup->SysDataBuf[LenuCurInx].eMeasType);
            Lu16SnsSpace = (uint16)GstrSnsAdjacentDis[LenuSnsGroup].fRadarLeftDis[LenuSnsCh];
            if((LenuSnsCh == PDC_SNS_CH_FL_RL)||(LenuSnsCh == PDC_SNS_CH_FRS_RRS))
            {
                LstrObjValidListenDis = CalValidListenDis(Lu16MasterDis,Lu16SnsSpace,SNS_SIDE_CORNER);
            }
            else if((LenuSnsCh == PDC_SNS_CH_FML_RML)||(LenuSnsCh == PDC_SNS_CH_FR_RR))
            {
                LstrObjValidListenDis = CalValidListenDis(Lu16MasterDis,Lu16SnsSpace,SNS_CORNER_MIDDLE);
            }
            else
            {
                LstrObjValidListenDis = CalValidListenDis(Lu16MasterDis,Lu16SnsSpace,SNS_MIDDLE_MIDDLE);
            }

            if((Lu16ListenDis > LstrObjValidListenDis.u16MinDis)&&(Lu16ListenDis < LstrObjValidListenDis.u16MaxDis))
            {
                LpstrSnsObjCoorPoint->LeftData[Lu8LeftObjCnt].u8CoorValidFlag = CalculateObjLeftSnsCoor(LenuSnsGroup,LenuSnsCh,Lu16MasterDis,Lu16ListenDis,Lu16SnsSpace,&LstrObjSnsCoor);
                if(LpstrSnsObjCoorPoint->LeftData[Lu8LeftObjCnt].u8CoorValidFlag)
                {
                    LpstrSnsObjCoorPoint->LeftData[Lu8LeftObjCnt].strObjSnsCoor = LstrObjSnsCoor;
                    LpstrSnsObjCoorPoint->LeftData[Lu8LeftObjCnt].u16MasterDis = Lu16MasterDis;
                    LpstrSnsObjCoorPoint->LeftData[Lu8LeftObjCnt].u16ListenDis = LpstrMapSigGroup->LeftBuf[LenuCurInx].u16ActualDis[Lu8LeftSigCnt];
                    if((LenuSnsCh == PDC_SNS_CH_FLS_RLS)||(LenuSnsCh == PDC_SNS_CH_FRS_RRS))
                    {
                        LpstrSnsObjCoorPoint->LeftData[Lu8LeftObjCnt].u16MasterHeight = Lu16MasterFirstHeight+Lu16MasterSecondHeight;
                    }
                    else
                    {
                        LpstrSnsObjCoorPoint->LeftData[Lu8LeftObjCnt].u16MasterHeight = Lu16MasterFirstHeight;
                    }
                    LpstrSnsObjCoorPoint->LeftData[Lu8LeftObjCnt].u16ListenHeight = Lu16ListenFirstHeight;
                    LpstrSnsObjCoorPoint->LeftData[Lu8LeftObjCnt].u16MasterSecondHeight = Lu16MasterSecondHeight;
                    LpstrSnsObjCoorPoint->LeftData[Lu8LeftObjCnt].u16ListenSecondHeight = Lu16ListenSecondHeight;
                    if((LenuSnsCh == PDC_SNS_CH_FLS_RLS)||(LenuSnsCh == PDC_SNS_CH_FRS_RRS))
                    {
                        LpstrSnsObjCoorPoint->LeftData[Lu8LeftObjCnt].enuObjType = SideObjCoorPointTypeJudge(&LpstrSnsObjCoorPoint->LeftData[Lu8LeftObjCnt],LenuMasterSigGroupRelation,LpstrSnsObjCoorPoint->eMeasType,LenuSnsGroup,LenuSnsCh);
                    }
                    else
                    {
                        LpstrSnsObjCoorPoint->LeftData[Lu8LeftObjCnt].enuObjType = ObjCoorPointTypeJudge(&LpstrSnsObjCoorPoint->LeftData[Lu8LeftObjCnt],LenuMasterSigGroupRelation,LenuListenSigGroupRelation,LpstrSnsObjCoorPoint->eMeasType, LenuSnsGroup, LenuSnsCh);
                    }
                    Lu8CoorValidFlag = ObjSnsLeftCoorPointTransToCarAndOdoCoor(&LpstrSnsObjCoorPoint->LeftData[Lu8LeftObjCnt],LenuSnsGroup,LenuSnsCh);
                    if(Lu8CoorValidFlag == 1)
                    {
                        Lu8LeftObjCnt++;
                    }
                    
                    /* 匹配到一个障碍物后，主发、侦听距离都需要跳出 */
                    Lu8ListenStartInx = Lu8LeftSigCnt+1;
                    break;
                }
            }
            else
            {
#if 0
                if((GfMessageTime > 38)&&(GfMessageTime < 39.5))
                {
                    if((LenuSnsGroup == 0x01)&&(LenuSnsCh == 0x05))
                    {
                        VS_PRINT("Time:%.3f,RRS Left,MasterDis:%d,ListenDis:%d,MinDis:%d,MaxDis:%d\r\n",GfMessageTime,Lu16MasterDis,\
                            Lu16ListenDis,LstrObjValidListenDis.u16MinDis,LstrObjValidListenDis.u16MaxDis);
                    }
                }
#endif
            }

        }
        if((Lu8LeftObjCnt == OBJ_COOR_POINT_NUM)||(Lu8CoorValidFlag == 2))
        {
            break;
        }
    }
    LpstrSnsObjCoorPoint->u8LeftObjCoorCnt = Lu8LeftObjCnt;

    /* 遍历计算右侦听障碍物坐标 */
    Lu8RightObjCnt = 0;
    Lu8ListenStartInx = 0;
    LpstrSnsObjCoorPoint->RightData[0].u8CoorValidFlag = 0;
    LpstrSnsObjCoorPoint->RightData[1].u8CoorValidFlag = 0;
    LpstrSnsObjCoorPoint->RightData[0].u8SnsNoiseCnt = LpstrMapSigGroup->SysDataBuf[LenuCurInx].u8SnsNoiseCnt;
    LpstrSnsObjCoorPoint->RightData[1].u8SnsNoiseCnt = LpstrMapSigGroup->SysDataBuf[LenuCurInx].u8SnsNoiseCnt;

    for(Lu8MasterSigCnt = 0; Lu8MasterSigCnt < LpstrMapSigGroup->MasterBuf[LenuCurInx].u8SigGroupCnt;Lu8MasterSigCnt++)
    {
        /* 提取主发探头的距离、回波关系 */
        Lu16MasterDis = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16ActualDis[Lu8MasterSigCnt];
        Lu16MasterFirstHeight = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16FirstEchoHeight[Lu8MasterSigCnt];
        Lu16MasterSecondHeight = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16SecondEchoHeight[Lu8MasterSigCnt];
        LenuMasterSigGroupRelation = JudgeSnsSigGroupEchoType(LpstrMapSigGroup->MasterBuf[LenuCurInx].u16FirstEchoDis[Lu8MasterSigCnt],LpstrMapSigGroup->MasterBuf[LenuCurInx].u16FirstEchoHeight[Lu8MasterSigCnt],\
            LpstrMapSigGroup->MasterBuf[LenuCurInx].u16SecondEchoDis[Lu8MasterSigCnt],LpstrMapSigGroup->MasterBuf[LenuCurInx].u16SecondEchoHeight[Lu8MasterSigCnt],LpstrMapSigGroup->SysDataBuf[LenuCurInx].eMeasType);
        Lu8CoorValidFlag = 0;
        for(Lu8RightSigCnt = Lu8ListenStartInx; Lu8RightSigCnt < LpstrMapSigGroup->RightBuf[LenuCurInx].u8SigGroupCnt;Lu8RightSigCnt++)
        {
            Lu16ListenDis = LpstrMapSigGroup->RightBuf[LenuCurInx].u16ActualDis[Lu8RightSigCnt]*2;
            Lu16ListenFirstHeight = LpstrMapSigGroup->RightBuf[LenuCurInx].u16FirstEchoHeight[Lu8RightSigCnt];
            Lu16ListenSecondHeight = LpstrMapSigGroup->RightBuf[LenuCurInx].u16SecondEchoHeight[Lu8RightSigCnt];
            LenuListenSigGroupRelation = JudgeSnsSigGroupEchoType(LpstrMapSigGroup->RightBuf[LenuCurInx].u16FirstEchoDis[Lu8RightSigCnt],LpstrMapSigGroup->RightBuf[LenuCurInx].u16FirstEchoHeight[Lu8RightSigCnt],\
                LpstrMapSigGroup->RightBuf[LenuCurInx].u16SecondEchoDis[Lu8RightSigCnt],LpstrMapSigGroup->RightBuf[LenuCurInx].u16SecondEchoHeight[Lu8RightSigCnt],LpstrMapSigGroup->SysDataBuf[LenuCurInx].eMeasType);
            Lu16SnsSpace = (uint16)GstrSnsAdjacentDis[LenuSnsGroup].fRadarRightDis[LenuSnsCh];


#if 0
            if((LenuSnsGroup == 0x00)&&(LenuSnsCh == 0x00))
            {
                if((GfMessageTime > 8.32)&&(GfMessageTime < 8.4))
                {
                    uint16 Lu16SecMinListenDis;
                    uint8 i;
                    for(i = 0; i < 40; i++)
                    {
                        Lu16MasterDis = 200+i*100;
                        //Lu16MasterDis = 1288;
                        LstrObjValidListenDis = CalValidListenDis(Lu16MasterDis,Lu16SnsSpace,SNS_SIDE_CORNER);
                        Lu16SecMinListenDis = FrontSideMinListenDisLimit(Lu16MasterDis);
                        VS_PRINT("Time:%.3f,FLS Right,MasterDis:%d,ListenDis:%d,SnsSpace:%d,MinDis:%d,MaxDis:%d,SecMinDis:%d\r\n",GfMessageTime,Lu16MasterDis,\
                            Lu16ListenDis,Lu16SnsSpace,LstrObjValidListenDis.u16MinDis,LstrObjValidListenDis.u16MaxDis,Lu16SecMinListenDis);
                    }
                }
            }
            Lu16MasterDis = 1288;
#endif

            if((LenuSnsCh == PDC_SNS_CH_FLS_RLS)||(LenuSnsCh == PDC_SNS_CH_FR_RR))
            {
                LstrObjValidListenDis = CalValidListenDis(Lu16MasterDis,Lu16SnsSpace,SNS_SIDE_CORNER);
                if((LenuSnsGroup == 0x00)&&(LenuSnsCh == PDC_SNS_CH_FLS_RLS))
                {
                    Lu16SecMinListenDis = FrontSideMinListenDisLimit(Lu16MasterDis);
                    if(LstrObjValidListenDis.u16MinDis < Lu16SecMinListenDis)
                    {
                        LstrObjValidListenDis.u16MinDis = Lu16SecMinListenDis -100;
                    }
                }
            }
            else if((LenuSnsCh == PDC_SNS_CH_FMR_RMR)||(LenuSnsCh == PDC_SNS_CH_FL_RL))
            {
                LstrObjValidListenDis = CalValidListenDis(Lu16MasterDis,Lu16SnsSpace,SNS_CORNER_MIDDLE);
            }
            else
            {
                LstrObjValidListenDis = CalValidListenDis(Lu16MasterDis,Lu16SnsSpace,SNS_MIDDLE_MIDDLE);
            }
            
#if 0
            if((LenuSnsGroup == 0x00)&&(LenuSnsCh == 0x00))
            {
                if((GfMessageTime > 8)&&(GfMessageTime < 8.8))
                {
                    VS_PRINT("Time:%.3f,FLS Right,MasterDis:%d,ListenDis:%d,SnsSpace:%d,MinDis:%d,MaxDis:%d\r\n",GfMessageTime,Lu16MasterDis,\
                        Lu16ListenDis,Lu16SnsSpace,LstrObjValidListenDis.u16MinDis,LstrObjValidListenDis.u16MaxDis);
                }
            }
#endif

            if((Lu16ListenDis > LstrObjValidListenDis.u16MinDis)&&(Lu16ListenDis < LstrObjValidListenDis.u16MaxDis))
            {
                LpstrSnsObjCoorPoint->RightData[Lu8RightObjCnt].u8CoorValidFlag = CalculateObjRightSnsCoor(LenuSnsGroup,LenuSnsCh,Lu16MasterDis,Lu16ListenDis,Lu16SnsSpace,&LstrObjSnsCoor);
                if(LpstrSnsObjCoorPoint->RightData[Lu8RightObjCnt].u8CoorValidFlag)
                {
                    LpstrSnsObjCoorPoint->RightData[Lu8RightObjCnt].strObjSnsCoor = LstrObjSnsCoor;
                    LpstrSnsObjCoorPoint->RightData[Lu8RightObjCnt].u16MasterDis = Lu16MasterDis;
                    LpstrSnsObjCoorPoint->RightData[Lu8RightObjCnt].u16ListenDis = LpstrMapSigGroup->RightBuf[LenuCurInx].u16ActualDis[Lu8RightSigCnt];
                    if((LenuSnsCh == PDC_SNS_CH_FLS_RLS)||(LenuSnsCh == PDC_SNS_CH_FRS_RRS))
                    {
                        LpstrSnsObjCoorPoint->RightData[Lu8RightObjCnt].u16MasterHeight = Lu16MasterFirstHeight+Lu16MasterSecondHeight;
                    }
                    else
                    {
                        LpstrSnsObjCoorPoint->RightData[Lu8RightObjCnt].u16MasterHeight = Lu16MasterFirstHeight;
                    }
                    LpstrSnsObjCoorPoint->RightData[Lu8RightObjCnt].u16ListenHeight = Lu16ListenFirstHeight;
                    LpstrSnsObjCoorPoint->RightData[Lu8RightObjCnt].u16MasterSecondHeight = Lu16MasterSecondHeight;
                    LpstrSnsObjCoorPoint->RightData[Lu8RightObjCnt].u16ListenSecondHeight = Lu16ListenSecondHeight;
                    if((LenuSnsCh == PDC_SNS_CH_FLS_RLS)||(LenuSnsCh == PDC_SNS_CH_FRS_RRS))
                    {
                        LpstrSnsObjCoorPoint->RightData[Lu8RightObjCnt].enuObjType = SideObjCoorPointTypeJudge(&LpstrSnsObjCoorPoint->RightData[Lu8RightObjCnt],LenuMasterSigGroupRelation,LpstrSnsObjCoorPoint->eMeasType,LenuSnsGroup,LenuSnsCh);
                    }
                    else
                    {
                        LpstrSnsObjCoorPoint->RightData[Lu8RightObjCnt].enuObjType = ObjCoorPointTypeJudge(&LpstrSnsObjCoorPoint->RightData[Lu8RightObjCnt],LenuMasterSigGroupRelation,LenuListenSigGroupRelation,LpstrSnsObjCoorPoint->eMeasType, LenuSnsGroup, LenuSnsCh);
                    }
                    Lu8CoorValidFlag = ObjSnsRightCoorPointTransToCarAndOdoCoor(&LpstrSnsObjCoorPoint->RightData[Lu8RightObjCnt],LenuSnsGroup,LenuSnsCh);
                    if(Lu8CoorValidFlag == 1)
                    {
                        Lu8RightObjCnt++;
                    }
                    /* 匹配到一个障碍物后，主发、侦听距离都需要跳出 */
                    Lu8ListenStartInx = Lu8RightSigCnt+1;
                    break;
                }
            }
        }
        
        if((Lu8RightObjCnt == OBJ_COOR_POINT_NUM)||(Lu8CoorValidFlag == 2))
        {
            break;
        }
    }
    LpstrSnsObjCoorPoint->u8RightObjCoorCnt = Lu8RightObjCnt;

    if((LpstrSnsObjCoorPoint->u8LeftObjCoorCnt == 0)&&(LpstrSnsObjCoorPoint->u8RightObjCoorCnt == 0))
    {
        /* 仅有主发有效回波，针对侧边和前后雷达需要分开处理 */
        /* 添加对近距离盲区的处理 */
        uint16 Lu16VirtualListenDis;
        
        if(LenuSnsGroup == PDC_SNS_GROUP_FRONT)
        {
            Lu16NearMasterDis = SNS_FRONT_NEAR_MASTER_DIS;
        }
        else
        {
            Lu16NearMasterDis = SNS_REAR_NEAR_MASTER_DIS;
        }
        /* 只针对FML FMR FR的主发左定位 */
        if((LenuSnsCh > PDC_SNS_CH_FL_RL)&&(LenuSnsCh < PDC_SNS_CH_FRS_RRS))
        {
            Lu16MasterDis = GstrMapSigGroupDataCache[LenuSnsGroup][LenuSnsCh].MasterBuf[LenuCurInx].u16ActualDis[0];
            if(Lu16MasterDis < Lu16NearMasterDis)
            {
                if(GstrMapSigGroupDataCache[LenuSnsGroup][LenuSnsCh-1].MasterBuf[LenuCurInx].u16ActualDis[0] < Lu16NearMasterDis)
                {
                    Lu16VirtualListenDis = Lu16MasterDis+GstrMapSigGroupDataCache[LenuSnsGroup][LenuSnsCh-1].MasterBuf[LenuCurInx].u16ActualDis[0];
                    Lu16SnsSpace = (uint16)GstrSnsAdjacentDis[LenuSnsGroup].fRadarLeftDis[LenuSnsCh];
                    LstrObjValidListenDis = CalValidListenDis(Lu16MasterDis,Lu16SnsSpace,SNS_SIDE_CORNER);
                    if((Lu16VirtualListenDis > LstrObjValidListenDis.u16MinDis)&&(Lu16VirtualListenDis < LstrObjValidListenDis.u16MaxDis))
                    {
                        LpstrSnsObjCoorPoint->LeftData[0].u8CoorValidFlag = CalculateObjLeftSnsCoor(LenuSnsGroup, LenuSnsCh, Lu16MasterDis, Lu16VirtualListenDis, Lu16SnsSpace, &LstrObjSnsCoor);
                        LpstrSnsObjCoorPoint->LeftData[0].strObjSnsCoor = LstrObjSnsCoor;
                        LpstrSnsObjCoorPoint->LeftData[0].u16MasterDis = Lu16MasterDis;
                        LpstrSnsObjCoorPoint->LeftData[0].u16ListenDis = Lu16VirtualListenDis>>1;
                        LpstrSnsObjCoorPoint->LeftData[0].u16MasterHeight = 10000;
                        LpstrSnsObjCoorPoint->LeftData[0].u16ListenHeight = 10000;
                        LpstrSnsObjCoorPoint->LeftData[0].u16MasterSecondHeight = 10000;
                        LpstrSnsObjCoorPoint->LeftData[0].u16ListenSecondHeight = 10000;
                        LpstrSnsObjCoorPoint->LeftData[0].enuObjType = OBJ_BIGWALL_TYPE;
                        Lu8CoorValidFlag = ObjSnsLeftCoorPointTransToCarAndOdoCoor(&LpstrSnsObjCoorPoint->LeftData[0],LenuSnsGroup,LenuSnsCh);
                        LpstrSnsObjCoorPoint->u8LeftObjCoorCnt = 1;
                    }
                }
            }
        }
        /* 只针对FLL FML FMR的主发右定位 */
        if((LenuSnsCh > PDC_SNS_CH_FLS_RLS)&&(LenuSnsCh < PDC_SNS_CH_FR_RR))
        {
            Lu16MasterDis = GstrMapSigGroupDataCache[LenuSnsGroup][LenuSnsCh].MasterBuf[LenuCurInx].u16ActualDis[0];
            if(Lu16MasterDis < Lu16NearMasterDis)
            {
                if(GstrMapSigGroupDataCache[LenuSnsGroup][LenuSnsCh+1].MasterBuf[LenuCurInx].u16ActualDis[0] < Lu16NearMasterDis)
                {
                    Lu16VirtualListenDis = Lu16MasterDis+GstrMapSigGroupDataCache[LenuSnsGroup][LenuSnsCh+1].MasterBuf[LenuCurInx].u16ActualDis[0];
                    Lu16SnsSpace = (uint16)GstrSnsAdjacentDis[LenuSnsGroup].fRadarRightDis[LenuSnsCh];
                    LstrObjValidListenDis = CalValidListenDis(Lu16MasterDis,Lu16SnsSpace,SNS_SIDE_CORNER);
                    if((Lu16VirtualListenDis > LstrObjValidListenDis.u16MinDis)&&(Lu16VirtualListenDis < LstrObjValidListenDis.u16MaxDis))
                    {
                        LpstrSnsObjCoorPoint->RightData[0].u8CoorValidFlag = CalculateObjRightSnsCoor(LenuSnsGroup, LenuSnsCh, Lu16MasterDis, Lu16VirtualListenDis, Lu16SnsSpace, &LstrObjSnsCoor);
                        LpstrSnsObjCoorPoint->RightData[0].strObjSnsCoor = LstrObjSnsCoor;
                        LpstrSnsObjCoorPoint->RightData[0].u16MasterDis = Lu16MasterDis;
                        LpstrSnsObjCoorPoint->RightData[0].u16ListenDis = Lu16VirtualListenDis>>1;
                        LpstrSnsObjCoorPoint->RightData[0].u16MasterHeight = 10000;
                        LpstrSnsObjCoorPoint->RightData[0].u16ListenHeight = 10000;
                        LpstrSnsObjCoorPoint->RightData[0].u16MasterSecondHeight = 10000;
                        LpstrSnsObjCoorPoint->RightData[0].u16ListenSecondHeight = 10000;
                        LpstrSnsObjCoorPoint->RightData[0].enuObjType = OBJ_BIGWALL_TYPE;
                        Lu8CoorValidFlag = ObjSnsRightCoorPointTransToCarAndOdoCoor(&LpstrSnsObjCoorPoint->RightData[0],LenuSnsGroup,LenuSnsCh);
                        LpstrSnsObjCoorPoint->u8RightObjCoorCnt = 1;
                    }
                }
            }
        } 
    }

    LpstrSnsObjCoorPoint->MasterData[0].u8CoorValidFlag = 0;
    LpstrSnsObjCoorPoint->MasterData[1].u8CoorValidFlag = 0;
    LpstrSnsObjCoorPoint->MasterData[0].u8SnsNoiseCnt = LpstrMapSigGroup->SysDataBuf[LenuCurInx].u8SnsNoiseCnt;
    LpstrSnsObjCoorPoint->MasterData[1].u8SnsNoiseCnt = LpstrMapSigGroup->SysDataBuf[LenuCurInx].u8SnsNoiseCnt;
    uint8 Lu8NeedMasterCoorFlag = 0;
    Lu16MasterDis = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16ActualDis[0];

    if((LpstrSnsObjCoorPoint->u8LeftObjCoorCnt == 0)&&(LpstrSnsObjCoorPoint->u8RightObjCoorCnt == 0))
    {
        Lu8NeedMasterCoorFlag = 1;
    }
    else if(LpstrSnsObjCoorPoint->u8LeftObjCoorCnt > 0)
    {
        if(LenuSnsGroup == 0x00)
        {
            if(LpstrSnsObjCoorPoint->LeftData[0].u16MasterDis > Lu16MasterDis)
            {
                Lu8NeedMasterCoorFlag = 1;
            }
        }
    }
    else if(LpstrSnsObjCoorPoint->u8RightObjCoorCnt > 0)
    {
        if(LenuSnsGroup == 0x00)
        {
            if(LpstrSnsObjCoorPoint->RightData[0].u16MasterDis > Lu16MasterDis)
            {
                Lu8NeedMasterCoorFlag = 1;
            }
        }
    }

    /* 单主发定位的单独处理，需要用前置的左右侦听坐标来进行判断，防止远端有障碍物的情况下，近端的单DE定位出不来 */
    if(Lu8NeedMasterCoorFlag)
    {
        const uint16 *Lpu16SnsEchoThresholdTable;
        uint8 Lu8TableIndex;
        uint8 Lu8SideSnsInx;
        uint8 Lu8SideFarOnlyMasterCoorFlag = 0;
        uint16 Lu16SideMasterDisSub;
        Lu16MasterDis = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16ActualDis[0];
        Lu16MasterFirstHeight = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16FirstEchoHeight[0];
        Lu16MasterSecondHeight = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16SecondEchoHeight[0];
        
        LenuMasterSigGroupRelation = JudgeSnsSigGroupEchoType(LpstrMapSigGroup->MasterBuf[LenuCurInx].u16FirstEchoDis[0],LpstrMapSigGroup->MasterBuf[LenuCurInx].u16FirstEchoHeight[0],\
            LpstrMapSigGroup->MasterBuf[LenuCurInx].u16SecondEchoDis[0],LpstrMapSigGroup->MasterBuf[LenuCurInx].u16SecondEchoHeight[0],LpstrMapSigGroup->SysDataBuf[LenuCurInx].eMeasType);
        /* 近距离主发定位Map，不使用扫频的数据 */
        if(GstrParkingGuidenceData.eCurrentAdasSts != APA_GUIDANCE)
        {
            if((LenuSnsCh == PDC_SNS_CH_FLS_RLS)||(LenuSnsCh == PDC_SNS_CH_FRS_RRS))
            {
                if(LenuSnsGroup == PDC_SNS_GROUP_FRONT)
                {
                    if(LenuSnsCh == PDC_SNS_CH_FLS_RLS)
                    {
                        Lu8SideSnsInx = SIDE_SNS_FLS;
                        Lu16ListenDis = LpstrMapSigGroup->RightBuf[LenuCurInx].u16ActualDis[0];
                    }
                    else
                    {
                        Lu8SideSnsInx = SIDE_SNS_FRS;
                        Lu16ListenDis = LpstrMapSigGroup->LeftBuf[LenuCurInx].u16ActualDis[0];
                    }
                    if(!GstrSideAreaMap[Lu8SideSnsInx].u8FarDisNoNeedMasterCoorFlag)
                    {
                        if(GstrSideAreaMap[Lu8SideSnsInx].u8CurbNeedMasterCoorFlag)
                        {
                            Lu16SideMasterDisSub = ABS(GstrSideAreaMap[Lu8SideSnsInx].u16ValidMasterDis,Lu16MasterDis);
                            if(Lu16SideMasterDisSub < 120)
                            {
                                Lu8SideFarOnlyMasterCoorFlag = 1;
                            }
                        }
                        if(!Lu8SideFarOnlyMasterCoorFlag)
                        {
                            if((Lu16MasterDis > 3000)&&(Lu16MasterDis < 4000))
                            {
                                Lu8SideFarOnlyMasterCoorFlag = 1;
                            }
                        }
                    }
                    if(Lu8SideFarOnlyMasterCoorFlag)
                    {
                        Lu16SideMasterDisSub = ABS(Lu16MasterDis,Lu16ListenDis);
                        if(Lu16SideMasterDisSub > 300)
                        {
                            Lu8SideFarOnlyMasterCoorFlag = 0;
                        }
                    }
                }
            }
        }

        if(Lu8SideFarOnlyMasterCoorFlag)
        {
            LpstrSnsObjCoorPoint->MasterData[0].u8CoorValidFlag = 1;
            if(LenuSnsGroup == PDC_SNS_GROUP_FRONT)
            {
                LpstrSnsObjCoorPoint->MasterData[0].strObjSnsCoor.fObjX = Lu16MasterDis;
                LpstrSnsObjCoorPoint->MasterData[0].strObjSnsCoor.fObjY = 0;
            }
            else
            {
                LpstrSnsObjCoorPoint->MasterData[0].strObjSnsCoor.fObjX = Lu16MasterDis;
                LpstrSnsObjCoorPoint->MasterData[0].strObjSnsCoor.fObjX = -LpstrSnsObjCoorPoint->MasterData[0].strObjSnsCoor.fObjX;
            }
            LpstrSnsObjCoorPoint->MasterData[0].u16MasterDis = Lu16MasterDis;
            LpstrSnsObjCoorPoint->MasterData[0].u16ListenDis = 65535;
            LpstrSnsObjCoorPoint->MasterData[0].u16MasterHeight = Lu16MasterFirstHeight;
            LpstrSnsObjCoorPoint->MasterData[0].u16MasterSecondHeight = Lu16MasterSecondHeight;
            LpstrSnsObjCoorPoint->MasterData[0].u16ListenHeight = 0;
            LpstrSnsObjCoorPoint->MasterData[0].u16ListenSecondHeight = 0;
            LpstrSnsObjCoorPoint->MasterData[0].enuObjType = SideObjCoorPointTypeJudge(&LpstrSnsObjCoorPoint->MasterData[0],LenuMasterSigGroupRelation,LpstrMapSigGroup->SysDataBuf[LenuCurInx].eMeasType,LenuSnsGroup,LenuSnsCh);
            //LpstrSnsObjCoorPoint->MasterData[0].enuObjType = OBJ_LOW_CURB_TYPE;
            ObjSnsMasterCoorPointTransToCarAndOdoCoor(&LpstrSnsObjCoorPoint->MasterData[0],LenuSnsGroup,LenuSnsCh);
            LpstrSnsObjCoorPoint->u8MasterObjCoorCnt = 1;
        }
        else
        {
            if((LpstrMapSigGroup->SysDataBuf[LenuCurInx].eMeasType != PDC_SNS_MEAS_STD)&&(Lu16MasterDis < 200))
            {
                Lu16MasterDis = MAP_SIG_GROUP_INVALID_DIS;
            }
            /* 单主发定位，侧边探头和前后的需要分开 */
            if((LenuSnsCh == PDC_SNS_CH_FLS_RLS)||(LenuSnsCh == PDC_SNS_CH_FRS_RRS))
            {
                if(LenuSnsGroup == PDC_SNS_GROUP_FRONT)
                {
                    Lu16NearMasterDis = FRONT_SIDE_NEAR_ONLY_MASTER_CREAT_MAP_DIS; 
                }
                else
                {
                    Lu16NearMasterDis = REAR_SIDE_NEAR_ONLY_MASTER_CREAT_MAP_DIS; 
                }
                if(LpstrMapSigGroup->SysDataBuf[LenuCurInx].eMeasType == PDC_SNS_MEAS_STD)
                {
                    Lpu16SnsEchoThresholdTable = Gpu16SnsMapThresholdTable[LenuSnsGroup][0];
                }
                else
                {
                    Lpu16SnsEchoThresholdTable = Gpu16SnsMapThresholdTable[LenuSnsGroup][1];
                } 
                
            }
            else if((LenuSnsCh == PDC_SNS_CH_FL_RL)||(LenuSnsCh == PDC_SNS_CH_FR_RR))
            {
                if(GstrParkingGuidenceData.eParking_Sts == PARKING_SUMMON)
                {
                    if(LenuSnsGroup == PDC_SNS_GROUP_FRONT)
                    {
                        Lu16NearMasterDis = FRONT_CORNER_SUMMON_ONLY_MASTER_CREAT_MAP_DIS; 
                    }
                    else
                    {
                        Lu16NearMasterDis = FRONT_CORNER_SUMMON_ONLY_MASTER_CREAT_MAP_DIS; 
                        //Lu16NearMasterDis = 0;
                    }
                }
                else
                {
                    if(LenuSnsGroup == PDC_SNS_GROUP_FRONT)
                    {
                        Lu16NearMasterDis = FRONT_CORNER_NEAR_ONLY_MASTER_CREAT_MAP_DIS; 
                    }
                    else
                    {
                        Lu16NearMasterDis = REAR_CORNER_NEAR_ONLY_MASTER_CREAT_MAP_DIS; 
                        //Lu16NearMasterDis = 0;
                    }
                }
            
                if(LpstrMapSigGroup->SysDataBuf[LenuCurInx].eMeasType == PDC_SNS_MEAS_STD)
                {
                    Lpu16SnsEchoThresholdTable = Gpu16SnsMapThresholdTable[LenuSnsGroup][2];
                }
                else
                {
                    Lpu16SnsEchoThresholdTable = Gpu16SnsMapThresholdTable[LenuSnsGroup][3];
                } 
            }
            else
            {
                if(LenuSnsGroup == PDC_SNS_GROUP_FRONT)
                {
                    if(GstrParkingGuidenceData.eLastAdasSts == APA_GUIDANCE)
                    {
                        Lu16NearMasterDis = FRONT_MIDDLE_NEAR_ONLY_MASTER_CREAT_MAP_DIS2;
                    }
                    else
                    {
                        Lu16NearMasterDis = FRONT_MIDDLE_NEAR_ONLY_MASTER_CREAT_MAP_DIS2; //FRONT_MIDDLE_NEAR_ONLY_MASTER_CREAT_MAP_DIS
                    }
                }
                else
                {
                    Lu16NearMasterDis = REAR_MIDDLE_NEAR_ONLY_MASTER_CREAT_MAP_DIS; 
                }
                if(LpstrMapSigGroup->SysDataBuf[LenuCurInx].eMeasType == PDC_SNS_MEAS_STD)
                {
                    Lpu16SnsEchoThresholdTable = Gpu16SnsMapThresholdTable[LenuSnsGroup][4];
                }
                else
                {
                    Lpu16SnsEchoThresholdTable = Gpu16SnsMapThresholdTable[LenuSnsGroup][5];
                } 
            }
            
            if(Lu16MasterDis < Lu16NearMasterDis)
            {
                if(Lu16MasterDis < MAP_TABLE_MAX_DIS)
                {
                    Lu8TableIndex = Lu16MasterDis/MAP_TABLE_STEP;
                }
                else
                {
                    Lu8TableIndex = SNS_MAP_DIS_400cm;
                }
                
                if(Lu16MasterFirstHeight > (Lpu16SnsEchoThresholdTable[Lu8TableIndex]))
                {
                    LpstrSnsObjCoorPoint->MasterData[0].u8CoorValidFlag = 1;
                    if(LenuSnsGroup == PDC_SNS_GROUP_FRONT)
                    {
                        LpstrSnsObjCoorPoint->MasterData[0].strObjSnsCoor.fObjX = Lu16MasterDis;
                        LpstrSnsObjCoorPoint->MasterData[0].strObjSnsCoor.fObjY = 0;
                    }
                    else
                    {
                        LpstrSnsObjCoorPoint->MasterData[0].strObjSnsCoor.fObjX = Lu16MasterDis;
                        LpstrSnsObjCoorPoint->MasterData[0].strObjSnsCoor.fObjX = -LpstrSnsObjCoorPoint->MasterData[0].strObjSnsCoor.fObjX;
                    }
                    LpstrSnsObjCoorPoint->MasterData[0].u16MasterDis = Lu16MasterDis;
                    LpstrSnsObjCoorPoint->MasterData[0].u16ListenDis = 65535;
                    LpstrSnsObjCoorPoint->MasterData[0].u16MasterHeight = Lu16MasterFirstHeight;
                    LpstrSnsObjCoorPoint->MasterData[0].u16MasterSecondHeight = Lu16MasterSecondHeight;
                    LpstrSnsObjCoorPoint->MasterData[0].u16ListenHeight = 0;
                    LpstrSnsObjCoorPoint->MasterData[0].u16ListenSecondHeight = 0;
                    LpstrSnsObjCoorPoint->MasterData[0].enuObjType = OBJ_PVC_PIPE_TYPE;
                    ObjSnsMasterCoorPointTransToCarAndOdoCoor(&LpstrSnsObjCoorPoint->MasterData[0],LenuSnsGroup,LenuSnsCh);
                    LpstrSnsObjCoorPoint->u8MasterObjCoorCnt = 1;
                }
            }
        }
    }
    
    /* 针对侧边雷达，在无任何点云的情况下，仍借助主发的距离及高度，判断回波类型 */
    if((LenuSnsCh == PDC_SNS_CH_FLS_RLS)||(LenuSnsCh == PDC_SNS_CH_FRS_RRS))
    {
        if((LpstrSnsObjCoorPoint->u8LeftObjCoorCnt == 0)&&(LpstrSnsObjCoorPoint->u8RightObjCoorCnt == 0)&&\
            (LpstrSnsObjCoorPoint->u8MasterObjCoorCnt == 0))
        {
            LpstrSnsObjCoorPoint->MasterData[0].u16MasterDis = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16ActualDis[0];
            LpstrSnsObjCoorPoint->MasterData[0].u16MasterHeight = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16FirstEchoHeight[0];
            LpstrSnsObjCoorPoint->MasterData[0].enuObjType = SideObjCoorPointTypeJudge(&LpstrSnsObjCoorPoint->MasterData[0],ONLY_FIRST_ECHO,LpstrMapSigGroup->SysDataBuf[LenuCurInx].eMeasType,LenuSnsGroup,LenuSnsCh);
        }
    }

    /* 针对侧边探头，没有主发定位的坐标时，强制使用主发定位一个坐标，用于侧边Map做裁剪使用 */
    if((LenuSnsCh == PDC_SNS_CH_FLS_RLS)||(LenuSnsCh == PDC_SNS_CH_FRS_RRS))
    {
        if(LpstrSnsObjCoorPoint->u8MasterObjCoorCnt == 0)
        {
            if(Lu16MasterDis < 4000)
            {
                if(LenuSnsGroup == PDC_SNS_GROUP_FRONT)
                {
                    LpstrSnsObjCoorPoint->MasterData[0].strObjSnsCoor.fObjX = Lu16MasterDis;
                    LpstrSnsObjCoorPoint->MasterData[0].strObjSnsCoor.fObjY = 0;
                }
                else
                {
                    LpstrSnsObjCoorPoint->MasterData[0].strObjSnsCoor.fObjX = Lu16MasterDis;
                    LpstrSnsObjCoorPoint->MasterData[0].strObjSnsCoor.fObjX = -LpstrSnsObjCoorPoint->MasterData[0].strObjSnsCoor.fObjX;
                }
                ObjSnsMasterCoorPointTransToCarAndOdoCoor(&LpstrSnsObjCoorPoint->MasterData[0],LenuSnsGroup,LenuSnsCh);
            }
        }
    }

#if 0
    if((LenuSnsGroup == 0x01)&&(LenuSnsCh == 0x02))
    {
        uint8 i;
        for(i = 0; i < LpstrSnsObjCoorPoint->u8RightObjCoorCnt;i++)
        {
            VS_PRINT("Time:%.3f,RML Right Coor[%d],MasterDis:%d,ListenDis:%d,X:%.3f,Y:%.3f,ObjType:%d\r\n",GfMessageTime,i,LpstrSnsObjCoorPoint->RightData[i].u16MasterDis,\
                LpstrSnsObjCoorPoint->RightData[i].u16ListenDis,LpstrSnsObjCoorPoint->RightData[i].strObjSnsCoor.fObjX,\
                LpstrSnsObjCoorPoint->RightData[i].strObjSnsCoor.fObjY,LpstrSnsObjCoorPoint->RightData[i].enuObjType);
        }
        VS_PRINT("\r\n");
    }
#endif
}

/******************************************************************************
 * 函数名称: ObjSnsCoorCalAndFollow
 * 
 * 功能描述: 探头障碍物坐标的计算及跟踪
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-08-02 21:23   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void ObjSnsCoorCalAndFollow(PDCSnsGroupType LenuGroup,PDCSnsChannelType LenuCh)
{
    ObjCoorPointUpdate(LenuGroup,LenuCh);
    SideAreaPointBufUpdate(LenuGroup,LenuCh);

    CloudPointToCANDataUpdate(LenuGroup,LenuCh);

    /* 12个探头一起进点云缓存，用于整体构建Map使用 */
    #if VHE_POINT_CLOUD_SIMULATE
    UpdateOldPointAndDeleteOutTimePointForVheBuf(GdSystemMsTimer);
    PointCloudVheBufUpdate(LenuGroup,LenuCh);
    AllPointCloudGenerateMap();
    UpdateMapCarCoor();
    #endif

    //if((LenuGroup == 0x01)&&((LenuCh >= 0x01)&&(LenuCh <= 0x03)))
    if(1)
    {
        if(LenuCh == 0x00)
        {
            UpdateOldPointAndDeleteOutTimePoint(LenuGroup,POINT_CLOUD_AREA0,GdSystemMsTimer);
            UpdateOldPointAndDeleteOutTimePoint(LenuGroup,POINT_CLOUD_AREA1,GdSystemMsTimer);
            PointCloudBufUpdate(LenuGroup,LenuCh);
            PointCloudClusterAndAreaMapUpdate(LenuGroup,POINT_CLOUD_AREA0);
            PointCloudClusterAndAreaMapUpdate(LenuGroup,POINT_CLOUD_AREA1);
        }
        else if(LenuCh == 0x01)
        {
            UpdateOldPointAndDeleteOutTimePoint(LenuGroup,POINT_CLOUD_AREA0,GdSystemMsTimer);
            UpdateOldPointAndDeleteOutTimePoint(LenuGroup,POINT_CLOUD_AREA1,GdSystemMsTimer);
            PointCloudBufUpdate(LenuGroup,LenuCh);
            PointCloudClusterAndAreaMapUpdate(LenuGroup,POINT_CLOUD_AREA0);
            PointCloudClusterAndAreaMapUpdate(LenuGroup,POINT_CLOUD_AREA1);
        }
        else if(LenuCh == 0x02)
        {
            UpdateOldPointAndDeleteOutTimePoint(LenuGroup,POINT_CLOUD_AREA1,GdSystemMsTimer);
            UpdateOldPointAndDeleteOutTimePoint(LenuGroup,POINT_CLOUD_AREA2,GdSystemMsTimer);
            PointCloudBufUpdate(LenuGroup,LenuCh);
            PointCloudClusterAndAreaMapUpdate(LenuGroup,POINT_CLOUD_AREA1);
            PointCloudClusterAndAreaMapUpdate(LenuGroup,POINT_CLOUD_AREA2);
        }
        else if(LenuCh == 0x03)
        {
            UpdateOldPointAndDeleteOutTimePoint(LenuGroup,POINT_CLOUD_AREA2,GdSystemMsTimer);
            UpdateOldPointAndDeleteOutTimePoint(LenuGroup,POINT_CLOUD_AREA3,GdSystemMsTimer);
            PointCloudBufUpdate(LenuGroup,LenuCh);
            PointCloudClusterAndAreaMapUpdate(LenuGroup,POINT_CLOUD_AREA2);
            PointCloudClusterAndAreaMapUpdate(LenuGroup,POINT_CLOUD_AREA3);
        }
        else if(LenuCh == 0x04)
        {
            UpdateOldPointAndDeleteOutTimePoint(LenuGroup,POINT_CLOUD_AREA3,GdSystemMsTimer);
            UpdateOldPointAndDeleteOutTimePoint(LenuGroup,POINT_CLOUD_AREA4,GdSystemMsTimer);
            PointCloudBufUpdate(LenuGroup,LenuCh);
            PointCloudClusterAndAreaMapUpdate(LenuGroup,POINT_CLOUD_AREA3);
            PointCloudClusterAndAreaMapUpdate(LenuGroup,POINT_CLOUD_AREA4);
        }
        else
        {
            UpdateOldPointAndDeleteOutTimePoint(LenuGroup,POINT_CLOUD_AREA3,GdSystemMsTimer);
            UpdateOldPointAndDeleteOutTimePoint(LenuGroup,POINT_CLOUD_AREA4,GdSystemMsTimer);
            PointCloudBufUpdate(LenuGroup,LenuCh);
            PointCloudClusterAndAreaMapUpdate(LenuGroup,POINT_CLOUD_AREA3);
            PointCloudClusterAndAreaMapUpdate(LenuGroup,POINT_CLOUD_AREA4);
        }
    }
}


