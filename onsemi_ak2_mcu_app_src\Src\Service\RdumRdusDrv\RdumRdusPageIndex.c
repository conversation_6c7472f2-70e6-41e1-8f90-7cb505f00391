/******************************************************************************
 * @file      RdumRdusPageIndex.c
 * @brief     安森美超声波探头页面和索引地址映射实现
 * <AUTHOR>
 * @date      2025-05-15
 * @note      
 *****************************************************************************/

/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "RdumRdusPageIndex.h"



/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/



/******************************************************************************
 * @Const Declaration
 *****************************************************************************/
/* 有效页面索引表 */
static const uint8 ValidPageIndices[4][128] = {
    /* 页面0有效索引 */
    {
        1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, /* 0x00-0x0F */
        1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, /* 0x10-0x1F */
        1, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, /* 0x20-0x2F */
        1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, /* 0x30-0x3F */
        1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, /* 0x40-0x4F */
        1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, /* 0x50-0x5F */
        1, 0, 1, 0, 1, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, /* 0x60-0x6F */
        1, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, /* 0x70-0x7F */
    },
    /* 页面1有效索引 */
    {
        1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, /* 0x00-0x0F */
        1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, /* 0x10-0x1F */
        1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, /* 0x20-0x2F */
        1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, /* 0x30-0x3F */
        1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, /* 0x40-0x4F */
        1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, /* 0x50-0x5F */
        1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, /* 0x60-0x6F */
        1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, /* 0x70-0x7F */
    },
    /* 页面2有效索引 - 通道H循环数据缓冲区 */
    {
        1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, /* 0x00-0x0F */
        1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, /* 0x10-0x1F */
        1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, /* 0x20-0x2F */
        1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, /* 0x30-0x3F */
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, /* 0x40-0x4F */
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, /* 0x50-0x5F */
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, /* 0x60-0x6F */
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, /* 0x70-0x7F */
    },
    /* 页面3有效索引 - 通道L循环数据缓冲区 */
    {
        1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, /* 0x00-0x0F */
        1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, /* 0x10-0x1F */
        1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, /* 0x20-0x2F */
        1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, /* 0x30-0x3F */
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, /* 0x40-0x4F */
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, /* 0x50-0x5F */
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, /* 0x60-0x6F */
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, /* 0x70-0x7F */
    }
};



/******************************************************************************
 * @Variable Declaration
 *****************************************************************************/



/******************************************************************************
 * @Function Definitions
 *****************************************************************************/

/******************************************************************************
 * @brief      获取页面索引结构
 * @param[in]  Page 页面
 * @param[in]  Index 索引
 * @return     页面索引结构
 * <AUTHOR>
 * @date       2025-05-15
 * @note       
 *****************************************************************************/
RdumRdusPageIndex_t RdumRdusPageIndex_Get(uint8 Page, uint8 Index)
{
    RdumRdusPageIndex_t PageIndex;
    
    PageIndex.Page = Page;
    PageIndex.Index = Index;
    
    return PageIndex;
}

/******************************************************************************
 * @brief      检查页面索引是否有效
 * @param[in]  PageIndex 页面索引结构
 * @return     0: 无效, 1: 有效
 * <AUTHOR>
 * @date       2025-05-15
 * @note       
 *****************************************************************************/
uint8 RdumRdusPageIndex_IsValid(RdumRdusPageIndex_t PageIndex)
{
    /* 检查页面是否有效 */
    if (PageIndex.Page >= 4)
    {
        return 0;
    }
    
    /* 检查索引是否有效 */
    if (PageIndex.Index >= 128)
    {
        return 0;
    }
    
    /* 检查页面索引组合是否有效 */
    return ValidPageIndices[PageIndex.Page][PageIndex.Index];
}
