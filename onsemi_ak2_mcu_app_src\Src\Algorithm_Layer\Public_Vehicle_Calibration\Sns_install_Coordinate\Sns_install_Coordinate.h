/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * APA_CalibPara: 
 * Created on: 2022-11-25 17:30
 * Original designer: 22866
 ******************************************************************************/
#ifndef __SNS_INSTALL_COORDINATE_H__
#define __SNS_INSTALL_COORDINATE_H__

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "Sns_install_Coordinate_Types.h"




/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/

#define  APA_ANGLE_CONVERT_PARAMETER                        57.29578

/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/

/* 两个车型的数据存储于Flash中 */


/******************************Sns安装参数_存储在Ram中--*********************************************/
extern SnsCoorSturctType  GstrRamSnsCoor[SNS_INSTALL_GROUP_NUM][SNS_CH_NUM];//探头坐标输出
extern Sns_Install_ParameterType GstrSns_Install_Parameter_Ram;//探头安装参数
extern SnsOutAngleType  GstrSnsOutAngle[SNS_INSTALL_GROUP_NUM];//探头外张角输出
extern SnsCoorConvAngleType GstrSnsCoorConvAngle[SNS_INSTALL_GROUP_NUM];
extern SnsDisSturctType  GstrSnsAdjacentDis[SNS_INSTALL_GROUP_NUM];
extern Sns_Install_PDCZoneCoorType GstrPDCZoneCoor_Ram[SNS_INSTALL_GROUP_NUM][ZONE_BORDER_NUM];
extern SnsAngleSturctType   GstrRamSnsAngle_Ram[SNS_INSTALL_GROUP_NUM][SNS_CH_NUM];
extern MapAreaType GstrMapAreaRam[SNS_INSTALL_GROUP_NUM][MAP_LINE_NUM];
extern float GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_NUM][4];
extern uint16 Gu16SnsInstallHeight_Ram[SNS_INSTALL_GROUP_NUM][SNS_CH_NUM];


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
extern void APP_Car_Sns_Config_Init(void);

#endif
