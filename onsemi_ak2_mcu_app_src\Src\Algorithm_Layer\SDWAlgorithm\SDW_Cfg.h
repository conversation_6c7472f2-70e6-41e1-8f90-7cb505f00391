/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * SDW_cfg: 
 * Created on: 2020-12-30 15:31
 * Original designer: Fangleyun
 ******************************************************************************/

#ifndef SDW_cfg_H
#define SDW_cfg_H

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "SDW_type.h"
//#include "UserModule_Cfg.h" 
#include "CAN_AppSignalManage.h"

/******************************************************************************/
/**
 * @brief   调试打印
 */
/******************************************************************************/

#define   SDW_USE_DEBUG_PRINT            (STD_OFF)

#if (SDW_USE_DEBUG_PRINT==STD_ON)
#define SDW_DEBUG_PRINTF	            Lhprintf
#else
#define SDW_DEBUG_PRINTF(...)   
#endif

/******************************************************************************/
/**
 * @brief   断言开关
 */
/******************************************************************************/
#define SDW_ERROR_DETECT            (STD_OFF)
#if (SDW_ERROR_DETECT==STD_ON)
#define SDW_ASSERT(x) SW_ASSERT(x)
#else
#define SDW_ASSERT(x) 
#endif


/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/
#define SYS_USE_SDW
#define SDW_INVALID_DIS             (uint16)65535   /* 虚拟计算无效的距离值 */
#define SDW_OBJ_RECORD_DIS          (uint16)100    /* 10cm记录一个点，为保证精度，建议采用10cm记录一个点  */
#define SDW_STOP_WAIT_CNT           50u            /* 车辆移动距离在连续时间无变化时，2000ms确认车辆静止 */
#define SDW_STOP_CLEAR_CNT          3000u           /* 车辆静止超过60S需要删除已经记忆的障碍物 */

#define SDW_COOR_MAX_BUFFER_CNT              (4)
#define DEFAULT_NVALID_DISTANCE              (uint16)0xFFFF 
#define SDW_MASTER_LISTEN_SUB                500     /*判定侦听距离有效时，允许主发、侦听的差值 */
#define SDW_TWICE_COMPARE_DIS                170
#define SDW_THIRD_COMPARE_DIS                170

/* 此参数需要根据前后探头的间距进行配置，前后探头的距离/10cm + 1  */
#define SDW_MAX_BUFFER_CNT          (uint8)45      /* 10cm一个点，45个点，合计450cm，留够长度供MPV使用 */
#define SDW_SIDE_NO_OBJ_DISTANCE_TO_CAN      (uint16)0xFFFF 

#define SDW_SNS_HIGH_TABLE_STEP              100

/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/

extern SDW_tenuWorkStatus GenuSDWWorkStatus;
extern SDW_WheelType GstrSDW_InputWheel;
extern SDWCurCarPointDataType GstrSDWCurCarPointData;
extern uint16 Gu16SDW_Speed;
extern Car_GearType GenuSDW_Gear;


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
void GetSDW_WorkStatus(void);
void GetSDW_CAN_Signal(void);
void GetSDWCurCarPointData(void);


#endif /* end of SDW_cfg_H */

