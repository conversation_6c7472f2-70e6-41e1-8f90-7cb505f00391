/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * APASignalManage.c: 
 * Created on: 2022-12-15 14:05
 * Original designer: 22866
 ******************************************************************************/

#ifndef __PSL_Output_MANAGE_H__
#define __PSL_Output_MANAGE_H__


 /******************************************************************************* 
 * Includes 
 ********************************************************************************/ 
#include "CAN_AppSignalManage.h"
#include "PSL_AppSignalManage.h"
//#include "Std_Types.h"
#include "PSL_Algorithm.h"
#include "Types.h"




/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/
#define OUTPUT_SLOT_MAX_NUM 8
#define DEPTH_NULL 5000
#define DIS_NULL 0
#define DIS_PARALLELSLOT 15000
#define DIS_CROSSSLOT 10000
#define GEAR_R_CLEARSLOTDIS  10000

/*OBJ状态*/
typedef enum
{
	PSL_Objste_None,
	PSL_Objste_Onlyobj1,
	PSL_Objste_Onlyobj2,
	PSL_Objste_Havetwoobj,
}PSLObjState;

/*OBJ类型*/
typedef enum
{
	PSL_Objtype_None,
	PSL_Objtype_Square,//方形
	PSL_Objtype_Round,//圆形
	PSL_Objtype_Max,
}PSLObjType;
    
/*Slot方向*/
typedef enum
{
    PSL_Slotdir_None,
    PSL_Slotdir_AtLeft,
    PSL_Slotdir_AtRight,
}PSLSlotDirection;

/* 定义车位深度探测障碍物形状参考                */
typedef enum
{
    DEPTH_REF_NONE,
    DEPTH_REF_CURB,
    DEPTH_REF_WALL,
    DEPTH_REF_VIRTUAL,
    DEPTH_REF_LOW,
    DEPTH_REF_HIGH,
    DEPTH_REF_UNKNOW,
    DEPTH_REF_MAX,
}PSLSlotDepthRef;

typedef struct
{
    uint8 wSlotCnt ;
    uint8 cSlotID[OUTPUT_SLOT_MAX_NUM];
    SLOT_OBJ_TYPE  cFirObjType[OUTPUT_SLOT_MAX_NUM];
    SLOT_OBJ_TYPE  cSecObjType[OUTPUT_SLOT_MAX_NUM];
    PSLObjState cObjState[OUTPUT_SLOT_MAX_NUM];
    PSLObjType cFirstObjType[OUTPUT_SLOT_MAX_NUM];
    PSLObjType cSecondObjType[OUTPUT_SLOT_MAX_NUM];
    PSLDetSlotType cSlotType[OUTPUT_SLOT_MAX_NUM]; 
    PSLSlotDirection  cSlotDir[OUTPUT_SLOT_MAX_NUM];
    uint16 wSlotWidth[OUTPUT_SLOT_MAX_NUM];
    float dFirstObjWx[OUTPUT_SLOT_MAX_NUM];
    float dFirstObjWy[OUTPUT_SLOT_MAX_NUM]; 
    uint16 dFirobjDis[OUTPUT_SLOT_MAX_NUM];
    uint16 dSecobjDis[OUTPUT_SLOT_MAX_NUM];
    float dSecObjWx[OUTPUT_SLOT_MAX_NUM];
    float dSecObjWy[OUTPUT_SLOT_MAX_NUM]; 
    float dThrObjDis[OUTPUT_SLOT_MAX_NUM];    
    float dThrObjWx[OUTPUT_SLOT_MAX_NUM];
    float dThrObjWy[OUTPUT_SLOT_MAX_NUM]; 
    float dSlotDepthDis[OUTPUT_SLOT_MAX_NUM];    
    float dSlotDepthWx[OUTPUT_SLOT_MAX_NUM];
    float dSlotDepthWy[OUTPUT_SLOT_MAX_NUM];
    float dObj1Slope[OUTPUT_SLOT_MAX_NUM];
    float dObj2Slope[OUTPUT_SLOT_MAX_NUM];
    float dCurbSlope[OUTPUT_SLOT_MAX_NUM];
    float dOutputSlope[OUTPUT_SLOT_MAX_NUM];
    uint8 Type_T_Slot_flag[OUTPUT_SLOT_MAX_NUM];
    float dSlotCurMovDis[OUTPUT_SLOT_MAX_NUM];
    
    /*车位内障碍物信息*/
    float dInSlotObjWx[OUTPUT_SLOT_MAX_NUM];
    float dInSlotObjWy[OUTPUT_SLOT_MAX_NUM];

    /* 输出车位时记录车身斜率 */
    float fRecordCarSlope[OUTPUT_SLOT_MAX_NUM];

    PSLSlotDepthRef cSlotdepthRef[OUTPUT_SLOT_MAX_NUM];
    uint32 Lu32slotsynctime[OUTPUT_SLOT_MAX_NUM];
}PSLSlotOutputStructType;







/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
extern PSLSlotOutputStructType GsPSLOutputSlotData;



/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
extern void PSLSlotOutputManage(void);


#endif

