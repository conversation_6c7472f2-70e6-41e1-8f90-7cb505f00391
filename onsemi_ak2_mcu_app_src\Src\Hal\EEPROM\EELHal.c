/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: EELHal.c
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/
#include "types.h"
#include "EELHal.h"
#include "DTCRecordManage.h"
#include "DID.h"
#include "fee.h"
/********************************数据类型定义**********************************/

/**********************************变量定义************************************/
static uint8 Gu8aEepromData[EEPROM_DATA_NUM] = {0u};      /**< EEL数据缓存数组 */
//static uint8 Gu8Section1WriteStatus = NVM_REQ_NOT_OK;   /**< 片段1状态 */
//static uint8 Gu8Section2WriteStatus = NVM_REQ_NOT_OK;   /**< 片段2状态 */
//static uint8 Gu8Section5WriteStatus = NVM_REQ_NOT_OK;   /**< 片段5状态 */

/******************************************************************************/
/**<pre>
 *函数名称: EELHalInit
 *功能描述: EEL初始化,读取存储数据
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
r_eel_status_t ResEELHalInit;
void EELHalInit(void)
{
    uint8 i = 0u;
    uint16 u16StartSectionAdd = EEL_DATA_SECTION_1_START;
    DL_EELInit();
    /**<pre> 调用库函数DL_EELRead()读取EEL片段1的数据并缓存到EEL缓存数组对应片段1的数据段中 </pre>*/
    ResEELHalInit = DL_EELRead(&Gu8aEepromData[EEL_DATA_SECTION_1_START], EEL_DATA_SECTION_1_ID, EEL_DATA_SECTION_1_LEN);
    /**<pre> 调用库函数DL_EELRead()读取EEL片段2的数据并缓存到EEL缓存数组对应片段2的数据段中 </pre>*/
    ResEELHalInit = DL_EELRead(&Gu8aEepromData[EEL_DATA_SECTION_2_START], EEL_DATA_SECTION_2_ID, EEL_DATA_SECTION_2_LEN);
    /**<pre> 调用库函数DL_EELRead()读取EEL片段3的数据并缓存到EEL缓存数组对应片段3的数据段中 </pre>*/
    ResEELHalInit = DL_EELRead(&Gu8aEepromData[EEL_DATA_SECTION_3_START], EEL_DATA_SECTION_3_ID, EEL_DATA_SECTION_3_LEN);
    /**<pre> 调用库函数DL_EELRead()读取EEL片段4的数据并缓存到EEL缓存数组对应片段4的数据段中 </pre>*/
    ResEELHalInit = DL_EELRead(&Gu8aEepromData[EEL_DATA_SECTION_4_START], EEL_DATA_SECTION_4_ID, EEL_DATA_SECTION_4_LEN);
    for (i = EEL_DATA_SECTION_5_ID;i <= EEL_DATA_SECTION_86_ID;i++)/*读取DTC信息*/
    {
        u16StartSectionAdd = (i - EEL_DATA_SECTION_5_ID) * DTC_RECORD_ONESECTION_LEN + EEL_DATA_SECTION_5_START;
        ResEELHalInit = DL_EELRead(&Gu8aEepromData[u16StartSectionAdd], i, DTC_RECORD_ONESECTION_LEN);
    }

    /**<pre> 调用库函数DL_EELRead()读取EEL片段87的数据并缓存到EEL缓存数组对应片段87的数据段中 </pre>*///升级log
    ResEELHalInit = DL_EELRead(&Gu8aEepromData[EEL_DATA_SECTION_87_START], EEL_DATA_SECTION_87_ID, EEL_DATA_SECTION_87_LEN);
    /**<pre> 调用库函数DL_EELRead()读取EEL片段87的数据并缓存到EEL缓存数组对应片段87的数据段中 </pre>*///升级log
    ResEELHalInit = DL_EELRead(&Gu8aEepromData[EEL_DATA_SECTION_88_START], EEL_DATA_SECTION_88_ID, EEL_DATA_SECTION_88_LEN);
    /**<pre> 调用库函数DL_EELRead()读取EEL片段87的数据并缓存到EEL缓存数组对应片段87的数据段中 </pre>*///升级log
    ResEELHalInit = DL_EELRead(&Gu8aEepromData[EEL_DATA_SECTION_89_START], EEL_DATA_SECTION_89_ID, EEL_DATA_SECTION_89_LEN);
    /**<pre> IF EEL缓存数组中的前4个字节 != 0x55AA6996u(即系统第一次上电) </pre>*/
    if((Gu8aEepromData[EEL_START_CHECK_ADDRESS] != 0x55u)||(Gu8aEepromData[EEL_START_CHECK_ADDRESS+1] != 0xAAu)
     ||(Gu8aEepromData[EEL_START_CHECK_ADDRESS+2] != 0x69u)||(Gu8aEepromData[EEL_START_CHECK_ADDRESS+3] != 0x96u))
    {
        /* 系统第一次上电 */
        InitDTCStatus();

        /**<pre>  EEL缓存数组中的前4个字节 = 0x55AA6996u </pre>*/
        Gu8aEepromData[EEL_START_CHECK_ADDRESS] = 0x55u;
        Gu8aEepromData[EEL_START_CHECK_ADDRESS+1] = 0xAAu;
        Gu8aEepromData[EEL_START_CHECK_ADDRESS+2] = 0x69u;
        Gu8aEepromData[EEL_START_CHECK_ADDRESS+3] = 0x96u;
        /**<pre>  FOR 循环初始化EEL缓存数据内诊断配置参数 </pre>*/

        /**<pre>  调用库函数DL_EELWrite()将EEL缓存数组对应片段1的数据写入EEL中 </pre>*/
        ResEELHalInit = DL_EELWrite(&Gu8aEepromData[EEL_DATA_SECTION_1_START],EEL_DATA_SECTION_1_ID);
        //Gu8Section5WriteStatus = NVM_REQ_NOT_OK;
    }
    else
    {
        //Gu8Section5WriteStatus = NVM_REQ_OK;
        IGNONDTCStatus();
        EELReadData(&DID_FEFF_Buff[0], DTC_ODO_FLAG_ADDRESS, DTC_ODO_FLAG_EEPROM_LEN);
    }
}

/******************************************************************************/
/**<pre>
 *函数名称: EELReadData
 *功能描述: 获取存储数据信息
 *输入参数: u16Address 需读取的EEL虚拟地址 u8Len 读取的长度
 *输出参数: pData 指向用于读取的数据缓存
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void EELReadData(uint8 *pData,uint16 u16Address ,uint8 u8Len)
{
    uint8 i = 0u;

    for(i = 0u; i < u8Len; i++) /**<pre> FOR 根据读取长度及EEL虚拟地址循环获取EEL数据缓存数组中的数据 </pre>*/
    {
        pData[i] = Gu8aEepromData[u16Address + i];
    }
}

///******************************************************************************/
///**<pre>
// *函数名称: EELWriteDataBuf
// *功能描述: 设置写数据缓存
// *输入参数: pData 指向需要写入的数据缓存 u16Address 写入的EEL虚拟地址 u8Len 写入的长度
// *输出参数: 无
// *返回数据: 无
// *修改记录: 无
//*********************************************************************** </pre>*/
//void EELWriteDataBuf(const uint8 *pData,uint16 u16Address ,uint8 u8Len)
//{
//    uint8 i = 0u;
//
//    for(i = 0u; i < u8Len; i++) /**<pre> FOR 根据写入长度及EEL虚拟地址循环更新到EEL数据缓存数组中 </pre>*/
//    {
//        Gu8aEepromData[u16Address + i] = pData[i];
//    }
//    /**<pre> IF 写入的EEL虚拟地址 < 片段1的结束地址 </pre>*/
//    if(u16Address < EEL_DATA_SECTION_1_END)
//    {
//        Gu8Section1WriteStatus = NVM_REQ_PENDING;  /**<pre>  片段1状态 = DataFlash写使能 </pre>*/
//    }
//    /**<pre> ELSE IF (写入的EEL虚拟地址 >= 片段2的起始地址)&&(写入的EEL虚拟地址 < 片段2的结束地址) </pre>*/
//    else if((u16Address >= EEL_DATA_SECTION_2_START)&&(u16Address < EEL_DATA_SECTION_2_END))
//    {
//        Gu8Section2WriteStatus = NVM_REQ_PENDING;  /**<pre>  片段2状态 = DataFlash写使能 </pre>*/
//    }
//    /**<pre> ELSE IF (写入的EEL虚拟地址 >= 片段5的起始地址)&&(写入的EEL虚拟地址 < 片段5的结束地址) </pre>*/
//    else if((u16Address >= EEL_DATA_SECTION_5_START)&&(u16Address < EEL_DATA_SECTION_5_END))
//    {
//        Gu8Section5WriteStatus = NVM_REQ_PENDING;  /**<pre>  片段5状态 = DataFlash写使能 </pre>*/
//    }
//    else{}  /**<pre> ELSE 不处理 </pre>*/
//}

/******************************************************************************/
/**<pre>
 *函数名称: EELWriteDataImmediate
 *功能描述: 立即存储数据信息
 *输入参数: pData 指向需要写入的数据缓存 u16Address 写入的EEL虚拟地址 u8Len 写入的长度
 *输出参数: 无
 *返回数据: LenuRst 0:成功 非0:失败
 *修改记录: 无
*********************************************************************** </pre>*/
r_eel_status_t EELWriteDataImmediate(const uint8 *pData,uint16 u16Address ,uint8 u8Len)
{
    uint8 i = 0u;
    r_eel_status_t LenuRst = R_EEL_OK;  /**<pre> 反馈状态默认为成功 </pre>*/
    uint16 Lu16AddOffset = 0;

    for(i = 0u; i < u8Len; i++) /**<pre> FOR 根据写入长度及EEL虚拟地址循环更新到EEL数据缓存数组中 </pre>*/
    {
        Gu8aEepromData[u16Address + i] = pData[i];
    }
    /**<pre> IF 写入的EEL虚拟地址 < 片段1的结束地址 </pre>*/
    if(u16Address < EEL_DATA_SECTION_1_END)
    {
        /**<pre>  调用库函数DL_EELWrite()将EEL缓存数组对应片段1的数据写入EEL中,并将操作结果缓存到反馈状态中 </pre>*/
        LenuRst = DL_EELWrite(&Gu8aEepromData[EEL_DATA_SECTION_1_START],EEL_DATA_SECTION_1_ID);
    }
    /**<pre> ELSE IF (写入的EEL虚拟地址 >= 片段2的起始地址)&&(写入的EEL虚拟地址 < 片段2的结束地址) </pre>*/
    else if((u16Address >= EEL_DATA_SECTION_2_START)&&(u16Address < EEL_DATA_SECTION_2_END))
    {
        /**<pre>  调用库函数DL_EELWrite()将EEL缓存数组对应片段2的数据写入EEL中,并将操作结果缓存到反馈状态中 </pre>*/
        LenuRst = DL_EELWrite(&Gu8aEepromData[EEL_DATA_SECTION_2_START],EEL_DATA_SECTION_2_ID);
    }
    else if((u16Address >= EEL_DATA_SECTION_4_START)&&(u16Address < EEL_DATA_SECTION_4_END))
    {
        LenuRst = DL_EELWrite(&Gu8aEepromData[EEL_DATA_SECTION_4_START],EEL_DATA_SECTION_4_ID);
    }
    else if((u16Address >= EEL_DATA_SECTION_5_START)&&(u16Address < EEL_DATA_SECTION_86_END))
    {
        Lu16AddOffset = u16Address - EEL_DATA_SECTION_5_START;/*获取地址差*/
        Lu16AddOffset = (Lu16AddOffset / DTC_RECORD_ONESECTION_LEN);/*取整，获取是第几个block的下标*/

        LenuRst = DL_EELWrite(&Gu8aEepromData[(EEL_DATA_SECTION_5_START+ Lu16AddOffset * DTC_RECORD_ONESECTION_LEN)],(EEL_DATA_SECTION_5_ID+ Lu16AddOffset));
    }
    else if ((u16Address >= EEL_DATA_SECTION_88_START) && (u16Address < EEL_DATA_SECTION_88_END))
    {
        /**<pre>  调用库函数DL_EELWrite()将EEL缓存数组对应片段2的数据写入EEL中,并将操作结果缓存到反馈状态中 </pre>*/
        LenuRst = DL_EELWrite(&Gu8aEepromData[EEL_DATA_SECTION_88_START], EEL_DATA_SECTION_88_ID);
    }
    else if ((u16Address >= EEL_DATA_SECTION_89_START) && (u16Address < EEL_DATA_SECTION_89_END))
    {
        /**<pre>  调用库函数DL_EELWrite()将EEL缓存数组对应片段2的数据写入EEL中,并将操作结果缓存到反馈状态中 </pre>*/
        LenuRst = DL_EELWrite(&Gu8aEepromData[EEL_DATA_SECTION_89_START], EEL_DATA_SECTION_89_ID);
    }
    else{}  /**<pre> ELSE 不处理 </pre>*/

    return LenuRst; /**<pre> RETURN 反馈状态 </pre>*/
}

///******************************************************************************/
///**<pre>
// *函数名称: EELWriteDataFlash
// *功能描述: 设置存储数据信息-睡眠/复位/跳转Boot前调用
// *输入参数: 无
// *输出参数: 无
// *返回数据: 无
// *修改记录: 无
//*********************************************************************** </pre>*/
//void EELWriteDataFlash(void)
//{
//    r_eel_status_t LenuStatus;
//    if(Gu8Section1WriteStatus == NVM_REQ_PENDING)  /**<pre> IF 片段1写入EEL的使能状态 == DataFlash写使能 </pre>*/
//    {
//        /**<pre>  调用库函数DL_EELWrite()将EEL缓存数组对应片段1的数据写入EEL中 </pre>*/
//        LenuStatus = DL_EELWrite(&Gu8aEepromData[EEL_DATA_SECTION_1_START],EEL_DATA_SECTION_1_ID);
//        /**<pre>  确认是否写成功 </pre>*/
//        if(R_EEL_OK == LenuStatus)
//        {
//            Gu8Section1WriteStatus = NVM_REQ_OK; /**<pre>   片段1写入EEL的使能状态 = DataFlash写成功 </pre>*/
//        }
//        else
//        {
//            Gu8Section1WriteStatus = NVM_REQ_NOT_OK; /**<pre>   片段1写入EEL的使能状态 = DataFlash写失败 </pre>*/
//        }
//    }
//
//    if(Gu8Section2WriteStatus == NVM_REQ_PENDING)  /**<pre> IF 片段2写入EEL的使能状态 == DataFlash写使能 </pre>*/
//    {
//        /**<pre>  调用库函数DL_EELWrite()将EEL缓存数组对应片段2的数据写入EEL中 </pre>*/
//        LenuStatus = DL_EELWrite(&Gu8aEepromData[EEL_DATA_SECTION_2_START],EEL_DATA_SECTION_2_ID);
//        /**<pre>  确认是否写成功 </pre>*/
//        if(R_EEL_OK == LenuStatus)
//        {
//            Gu8Section2WriteStatus = NVM_REQ_OK; /**<pre>   片段2写入EEL的使能状态 = DataFlash写成功 </pre>*/
//        }
//        else
//        {
//            Gu8Section2WriteStatus = NVM_REQ_NOT_OK; /**<pre>   片段2写入EEL的使能状态 = DataFlash写失败 </pre>*/
//        }
//    }
//
//    if(Gu8Section5WriteStatus == NVM_REQ_PENDING)  /**<pre> IF 片段5写入EEL的使能状态 == DataFlash写使能 </pre>*/
//    {
//        /**<pre>  调用库函数DL_EELWrite()将EEL缓存数组对应片段5的数据写入EEL中 </pre>*/
//        LenuStatus = DL_EELWrite(&Gu8aEepromData[EEL_DATA_SECTION_5_START],EEL_DATA_SECTION_5_ID);
//        /**<pre>  确认是否写成功 </pre>*/
//        if(R_EEL_OK == LenuStatus)
//        {
//            Gu8Section5WriteStatus = NVM_REQ_OK; /**<pre>   片段5写入EEL的使能状态 = DataFlash写成功 </pre>*/
//        }
//        else
//        {
//            Gu8Section5WriteStatus = NVM_REQ_NOT_OK; /**<pre>   片段5写入EEL的使能状态 = DataFlash写失败 </pre>*/
//        }
//    }
//}



