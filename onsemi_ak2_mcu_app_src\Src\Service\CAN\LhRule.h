#ifndef __LHRULE_H__
#define __LHRULE_H__

/* Includes ------------------------------------------------------------------*/
#include "types.h"
/******************************************************************************
*!@Description :  typedef       
*******************************************************************************/

/******************************************************************************
*!@Description :  define    TRUE FALSE
*******************************************************************************/
#ifndef TRUE
#define TRUE             1u
#endif

#ifndef FALSE
#define FALSE            0u
#endif
/******************************************************************************/
/*SDF 8 bytes variables values*/
#define SDF_U8_VALUE0                   0x00U
#define SDF_U8_VALUE1                   0x69U
#define SDF_U8_VALUE2                   0x96U
#define SDF_U8_VALUE3                   0xC3U
#define SDF_U8_VALUE4                   0x3CU
#define SDF_U8_VALUE5                   0xAAU
#define SDF_U8_VALUE6                   0x55U
#define SDF_U8_VALUE7                   0x63U
#define SDF_U8_VALUE8                   0x36U
#define SDF_U8_VALUE9                   0x88U
#define SDF_U8_VALUE10                  0x9CU
#define SDF_U8_VALUE11                  0xC9U
#define SDF_U8_VALUE12                  0x6CU
#define SDF_U8_VALUE13                  0xC6U

/*SDF 16 bytes variables values*/
#define SDF_U16_VALUE1                  0x6969U
#define SDF_U16_VALUE2                  0x9669U
#define SDF_U16_VALUE3                  0xC369U

/*CAN Type variables values*/
typedef enum
{   
    CAN_DATA_MAX_LEN_NONE   = 0u,
    CAN_DATA_MAX_LEN_1      = 1u,
    CAN_DATA_MAX_LEN_2      = 2u,
    CAN_DATA_MAX_LEN_3      = 3u,
    CAN_DATA_MAX_LEN_4      = 4u,
    CAN_DATA_MAX_LEN_5      = 5u,
    CAN_DATA_MAX_LEN_6      = 6u,
    CAN_DATA_MAX_LEN_7      = 7u,
    CAN_DATA_MAX_LEN_8      = 8u,
    CAN_DATA_MAX_LEN_12     = 9u,
    CAN_DATA_MAX_LEN_16     = 10u,
    CAN_DATA_MAX_LEN_20     = 11u,
    CAN_DATA_MAX_LEN_24     = 12u,
    CAN_DATA_MAX_LEN_32     = 13u,
    CAN_DATA_MAX_LEN_48     = 14u,
    CAN_DATA_MAX_LEN_64     = 15u,
    CAN_DATA_MAX_NUM,
}CAN_DLC_DATA_BYTE_TYPE;

/******************************************************************************
*!@Description :  Bit Cal
*******************************************************************************/
#define NCS_SET_BIT(Var,BitField,Type)            ((Var) |= (Type)(BitField))
#define NCS_RESET_BIT(Var,BitField,Type)          ((Var) &= (Type)~(Type)(BitField))
#define NCS_TST_BIT_SET(Var,BitField,Type)        (0U != ((Var) & (Type)(BitField)))
#define NCS_TST_BIT_RESET(Var,BitField,Type)      (0U == ((Var) & (Type)(BitField)))

/******************************************************************************
*!@Description :  define    NULL 
*******************************************************************************/

#ifndef NULL
#define NULL            ((void *)0)
#endif

#endif 
/**************************************END __LH_RULE_H__******************************************/
