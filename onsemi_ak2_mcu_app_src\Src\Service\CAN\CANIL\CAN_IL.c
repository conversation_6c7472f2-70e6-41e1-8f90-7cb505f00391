/*
 * CAN_IL_V07.c
 *
 *  Created on: 2021年3月25日
 *      Author: 6000022648
 */

//#ifndef MIDDLE_LAYER_CANCOM_CANIL_CAN_IL_V07_C_
//#define MIDDLE_LAYER_CANCOM_CANIL_CAN_IL_V07_C_
/***********************************************************************
* Includes 
***********************************************************************/
#include "CAN_IL.h"
#include "CAN_COM.h"
#include "DID.h"
#include "CAN_AppSignalManage.h"
#include "PAS_MAP_SignalManage.h"
#include "ODO_AppSignalManage.h"
#include "PSL_AppSignalManage.h"
#include "PSL_State_Manage.h"
#include "MapEchoFilterAndSigGroup_int.h"
#include "SnsDiag.h"
#include "TimerManage.h"
#include "DTCMonitor.h"
#include "Elmos_524_17_SnsCtrl.h"
#include "SnsRawData_Int.h"

/***********************************************************************
* Global objects 
************************************************************************/
/***********************************************************************/
/***********************************************************************/
/***********************************************************************/

//#define DONT_NEED_CAL_CRCANDALC
/*整车输入CAN信号集合*/
IL_InputParaDataStr GsIntputParaStruct;

/*CAN消息接受处理标识，收到报文后置位，解析处理完后清零*/
uint8 GvCanMessageProcessFlag[RXANDTXFRAMEMAXNUM];  
uint32 Gu32CANTimeSysn_GlobalTimeBaseYMD = 0u;/*ms*/
uint32 Gu32CANTimeSysn_GlobalTimeBaseHSS = 0u;/*ms*/


/*函数指针类型定义，将所有的IL解析函数放入函数指针数组中运行*/
typedef void (*tpfCanMessageProcess)(const uint8 *pBuff);

NodeErrCntType GstrNodeErrCnt[RXANDTXFRAMEMAXNUM];
uint8 Gu8NodeALC[RXANDTXFRAMEMAXNUM];

uint8 ADAS_SYNCTime_FailedCnt = 0;  
uint8 ADAS_SYNCTime_PassCnt = 0;

/****************************************************************************** 
* Constants and macros 
*******************************************************************************/
#define CAL_CRC_INIT_VALUE      0xFF      /*CRC初始值*/

/*SEND*/
const uint8 Gu8dataID[DataIdNum][16]=
{
    /*SEND*/
    {200, 43, 80, 237, 97, 63, 209, 125, 225, 75, 28, 241, 159, 99, 23, 130},/*2F4*/
    {204, 92, 25, 9, 246, 113, 123, 47, 41, 35, 210, 115, 157, 97, 39, 37},/*2E4*/
    {146, 66, 18, 112, 62, 74, 95, 181, 178, 98, 228, 126, 132, 205, 227, 157},/*394*/
    {213, 235, 162, 78, 53, 174, 81, 206, 45, 121, 159, 124, 52, 82, 9, 3},/*085*/
    {131, 156, 166, 214, 177, 28, 212, 186, 164, 184, 178, 8, 135, 171, 119, 170},/*245*/
    {46, 91, 193, 168, 235, 5, 57, 167, 210, 78, 232, 217, 104, 13, 174, 80},/*254*/
    {218, 39, 57, 175, 229, 13, 160, 15, 177, 49, 62, 40, 240, 145, 82, 209},/*387*/
    {229, 49, 82, 197, 142, 100, 149, 158, 237, 96, 69, 93, 141, 178, 128, 161},/*284*/
    {21, 69, 214, 239, 40, 186, 188, 249, 8, 107, 126, 170, 117, 204, 223, 55},/*285*/
    {240, 118, 247, 131, 141, 192, 106, 156, 64, 114, 169, 166, 36 ,154, 165, 214},/*286*/
    {248, 236, 83, 196, 48, 88, 134, 246, 55, 185, 65, 218, 98, 233, 148, 35},/*287*/
    {47, 37, 101, 248 ,251, 50, 228, 236, 239, 79, 26, 83, 226, 180, 209, 196},/*2a4*/
    {128, 61, 228, 182, 89, 180, 44, 105, 245, 137, 215, 134, 80, 152, 12, 70},/*2a5*/
    {209, 99, 116, 148, 161, 111, 207, 221, 102, 33, 71, 7, 214, 201, 151, 229},/*2b4*/
    {22, 54, 31, 240, 170, 56, 42, 118, 219, 32, 201, 247, 48, 45, 199, 131},/*2b5*/


    /*Receive*/
    {142, 58, 204, 83, 10, 138, 122, 100, 185, 90, 47, 137, 126, 23, 149, 183},/*236*/
    {206,214, 240, 19, 20, 168, 199, 236, 105, 153, 185, 82, 224, 87, 73, 195},/*237*/
    {196, 215, 93, 116, 223, 107, 40, 155, 195, 59, 183, 146, 208, 38, 194, 66},/*395*/
    {52, 134, 14, 31, 105, 77, 104, 182, 238, 172, 155, 25, 113, 41, 115, 39},/*2d4*/
    {77, 25, 3, 143, 46, 49, 179, 141, 187, 150, 98, 29, 198, 103, 54, 9},/*176*/
    {26, 4, 157, 65, 171, 1, 91, 42, 143, 110, 70, 208, 216, 168, 71, 45},/*086*/
    {68, 115, 48, 46, 167, 174, 64, 150, 51, 88, 132, 54, 241, 67, 191, 102},/*096*/
    {193, 5, 210, 217, 174, 147, 177, 144, 66, 124, 96, 51, 70, 184, 106, 225},/*0d6*/
    {33, 201, 244, 122, 186, 2, 236, 23, 72, 222, 14, 167, 29, 195, 131, 189},/*209*/
    {81, 72, 230, 140, 100, 188, 156, 191, 79, 183, 218, 235, 128, 207, 104, 216},/*0c2*/
    {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0}/*5a4*/
    
};


uint8 Crc8Table[256] = 
{
    0xbd, 0x92, 0xe3, 0xcc, 0x1,  0x2e, 0x5f, 0x70, 0xea, 0xc5,
    0xb4, 0x9b, 0x56, 0x79, 0x8,  0x27, 0x13, 0x3c, 0x4d, 0x62,
    0xaf, 0x80, 0xf1, 0xde, 0x44, 0x6b, 0x1a, 0x35, 0xf8, 0xd7, 
    0xa6, 0x89, 0xce, 0xe1, 0x90, 0xbf, 0x72, 0x5d, 0x2c, 0x3, 
    0x99, 0xb6, 0xc7, 0xe8, 0x25, 0xa,  0x7b, 0x54, 0x60, 0x4f, 
    0x3e, 0x11, 0xdc, 0xf3, 0x82, 0xad, 0x37, 0x18, 0x69, 0x46, 
    0x8b, 0xa4, 0xd5, 0xfa, 0x5b, 0x74, 0x5,  0x2a, 0xe7, 0xc8, 
    0xb9, 0x96, 0xc,  0x23, 0x52, 0x7d, 0xb0, 0x9f, 0xee, 0xc1, 
    0xf5, 0xda, 0xab, 0x84, 0x49, 0x66, 0x17, 0x38, 0xa2, 0x8d, 
    0xfc, 0xd3, 0x1e, 0x31, 0x40, 0x6f, 0x28, 0x7,  0x76, 0x59, 
    0x94, 0xbb, 0xca, 0xe5, 0x7f, 0x50, 0x21, 0xe,  0xc3, 0xec, 
    0x9d, 0xb2, 0x86, 0xa9, 0xd8, 0xf7, 0x3a, 0x15, 0x64, 0x4b, 
    0xd1, 0xfe, 0x8f, 0xa0, 0x6d, 0x42, 0x33, 0x1c, 0x5e, 0x71, 
    0x0,  0x2f, 0xe2, 0xcd, 0xbc, 0x93, 0x9,  0x26, 0x57, 0x78, 
    0xb5, 0x9a, 0xeb, 0xc4, 0xf0, 0xdf, 0xae, 0x81, 0x4c, 0x63, 
    0x12, 0x3d, 0xa7, 0x88, 0xf9, 0xd6, 0x1b, 0x34, 0x45, 0x6a, 
    0x2d, 0x2,  0x73, 0x5c, 0x91, 0xbe, 0xcf, 0xe0, 0x7a, 0x55, 
    0x24, 0xb,  0xc6, 0xe9, 0x98, 0xb7, 0x83, 0xac, 0xdd, 0xf2, 
    0x3f, 0x10, 0x61, 0x4e, 0xd4, 0xfb, 0x8a, 0xa5, 0x68, 0x47, 
    0x36, 0x19, 0xb8, 0x97, 0xe6, 0xc9, 0x4,  0x2b, 0x5a, 0x75, 
    0xef, 0xc0, 0xb1, 0x9e, 0x53, 0x7c, 0xd,  0x22, 0x16, 0x39, 
    0x48, 0x67, 0xaa, 0x85, 0xf4, 0xdb, 0x41, 0x6e, 0x1f, 0x30, 
    0xfd, 0xd2, 0xa3, 0x8c, 0xcb, 0xe4, 0x95, 0xba, 0x77, 0x58, 
    0x29, 0x6,  0x9c, 0xb3, 0xc2, 0xed, 0x20, 0xf,  0x7e, 0x51, 
    0x65, 0x4a, 0x3b, 0x14, 0xd9, 0xf6, 0x87, 0xa8, 0x32, 0x1d, 
    0x6c, 0x43, 0x8e, 0xa1, 0xd0, 0xFF
};

volatile uint32 Gu32CANTimeSysn_T3diff = 0;/*ms*/
CANTimeSysn GstrCANTimeSysnInf;


#if 1


/****************************************************************************** 
* Globe Funtion
*******************************************************************************/

/* RX */
static void CanIL_ACU_Info1_20ms_GW_0x236_Msg_Analysis(const uint8 *pLcMsgType);
static void CanIL_ACU_Info2_20ms_GW_0x237_Msg_Analysis(const uint8 *pLcMsgType);
static void CanIL_ADAS_CMD_100ms_0x395_Msg_Analysis(const uint8 *pLcMsgType);
static void CanIL_ADAS_Info1_100msCE_0x384_Msg_Analysis(const uint8 *pLcMsgType);
static void CanIL_ADAS_STS1_20ms_0x2D4_Msg_Analysis(const uint8 *pLcMsgType);
static void CanIL_ADAS_SYNCTime_500ms_0X5A4_Msg_Analysis(const uint8 *pLcMsgType);
static void CanIL_AP2_STS_100ms_0X504_Msg_Analysis(const uint8 *pLcMsgType);
static void CanIL_EPS_Info_10ms_0X176_Msg_Analysis(const uint8 *pLcMsgType);
static void CanIL_ESP_Info_10ms_0X086_Msg_Analysis(const uint8 *pLcMsgType);
static void CanIL_ESP_Info_10ms_Rt_FD_0X096_Msg_Analysis(const uint8 *pLcMsgType);
static void CanIL_ESP_WhlSpd_10ms_0X0D6_Msg_Analysis(const uint8 *pLcMsgType);
static void CanIL_FBCM_BCM_XCU_Info1_20ms_GW_0X209_Msg_Analysis(const uint8 *pLcMsgType);
static void CanIL_HU_Info1_100ms_E_0X39D_Msg_Analysis(const uint8 *pLcMsgType);
static void CanIL_HU_Info1_10ms_E_0X09E_Msg_Analysis(const uint8 *pLcMsgType);
static void CanIL_XCU_Info_100ms_CH_0X397_Msg_Analysis(const uint8 *pLcMsgType);
static void CanIL_XCU_Info_10ms_FD_0X0C2_Msg_Analysis(const uint8 *pLcMsgType);
static void CanIL_ADAS_Objs1_100ms_FD_0X388_Msg_Analysis(const uint8* pLcMsgType);
/*TX*/

static void CanIL_Tx_PAS_DTC_665_Msg(const uint8 *pBuff);
static void CanIL_Tx_PAS_Info1_20ms_2F4_Msg(const uint8 *pBuff);
static void CanIL_Tx_PAS_Info3_20ms_2E4_Msg(const uint8 *pBuff);
static void CanIL_Tx_PAS_Info4_100ms_394_Msg(const uint8 *pBuff);
static void CanIL_Tx_PAS_Info4_20ms_244_Msg(const uint8 *pBuff);

static void CanIL_Tx_PAS_Info5_10ms_085_Msg(const uint8 *pBuff);
static void CanIL_Tx_PAS_Info5_20ms_245_Msg(const uint8 *pBuff);
static void CanIL_Tx_PAS_Info7_20ms_254_Msg(const uint8 *pBuff);
static void CanIL_Tx_PAS_Info7_100ms_387_Msg(const uint8 *pBuff);
static void CanIL_Tx_PAS_Info8_20ms_2D5_Msg(const uint8* pBuff);
static void CanIL_Tx_PAS_Info_20ms_238_Msg(const uint8 *pBuff);

static void CanIL_Tx_PAS_PSLInfo1_20ms_284_Msg(const uint8 *pBuff);
static void CanIL_Tx_PAS_PSLInfo2_20ms_285_Msg(const uint8 *pBuff);
static void CanIL_Tx_PAS_PSLInfo3_20ms_286_Msg(const uint8 *pBuff);
static void CanIL_Tx_PAS_PSLInfo4_20ms_287_Msg(const uint8 *pBuff);
static void CanIL_Tx_PAS_Timestamp_20ms_2A4_Msg(const uint8 *pBuff);

static void CanIL_Tx_PAS_TmspMapObj1_20ms_2A5_Msg(const uint8 *pBuff);
static void CanIL_Tx_PAS_TmspMapObj2_20ms_2B4_Msg(const uint8 *pBuff);
static void CanIL_Tx_PAS_TmspMapObj3_20ms_2B5_Msg(const uint8 *pBuff);
static void CanIL_Tx_PAS_VerNum_5000ms_685_Msg(const uint8 *pBuff);

#if(DEBUG_FRAME_TX_EN==STD_ON)
const tpfCanMessageProcess GpfCanMessageProcess[RXANDTXFRAMEMAXNUM] =
{
    /* RX */
    [ID7D2Idx]  = NULL,
    [ID7DFIdx]  = NULL,
    
    [ID236Idx]  = CanIL_ACU_Info1_20ms_GW_0x236_Msg_Analysis,
    [ID237Idx]  = CanIL_ACU_Info2_20ms_GW_0x237_Msg_Analysis,
    [ID395Idx]  = CanIL_ADAS_CMD_100ms_0x395_Msg_Analysis,
    [ID384Idx]  = CanIL_ADAS_Info1_100msCE_0x384_Msg_Analysis,
    [ID2D4Idx]  = CanIL_ADAS_STS1_20ms_0x2D4_Msg_Analysis,
    
    [ID5A4Idx]  = CanIL_ADAS_SYNCTime_500ms_0X5A4_Msg_Analysis,
    [ID504Idx]  = CanIL_AP2_STS_100ms_0X504_Msg_Analysis,
    [ID176Idx]  = CanIL_EPS_Info_10ms_0X176_Msg_Analysis,
    [ID086Idx]  = CanIL_ESP_Info_10ms_0X086_Msg_Analysis,
    [ID096Idx]  = CanIL_ESP_Info_10ms_Rt_FD_0X096_Msg_Analysis,
    
    [ID0D6Idx]  = CanIL_ESP_WhlSpd_10ms_0X0D6_Msg_Analysis,
    [ID209Idx]  = CanIL_FBCM_BCM_XCU_Info1_20ms_GW_0X209_Msg_Analysis,
    [ID39DIdx]  = CanIL_HU_Info1_100ms_E_0X39D_Msg_Analysis,
    [ID09EIdx]  = CanIL_HU_Info1_10ms_E_0X09E_Msg_Analysis,
    [ID397Idx]  = CanIL_XCU_Info_100ms_CH_0X397_Msg_Analysis,
    
    [ID0C2Idx]  = CanIL_XCU_Info_10ms_FD_0X0C2_Msg_Analysis,
    [ID388Idx]  = CanIL_ADAS_Objs1_100ms_FD_0X388_Msg_Analysis,  
    [ID6F0Idx]  = NULL,
    /* TX */
    [ID7DAIdx]  = NULL,

    [ID238Idx] = CanIL_Tx_PAS_Info_20ms_238_Msg,
    [ID244Idx] = CanIL_Tx_PAS_Info4_20ms_244_Msg,
    [ID085Idx] = CanIL_Tx_PAS_Info5_10ms_085_Msg,
    [ID245Idx] = CanIL_Tx_PAS_Info5_20ms_245_Msg,

    [ID254Idx] = CanIL_Tx_PAS_Info7_20ms_254_Msg,
    [ID2E4Idx] = CanIL_Tx_PAS_Info3_20ms_2E4_Msg,
    [ID2F4Idx] = CanIL_Tx_PAS_Info1_20ms_2F4_Msg,
    [ID394Idx] = CanIL_Tx_PAS_Info4_100ms_394_Msg,

    [ID387Idx]  = CanIL_Tx_PAS_Info7_100ms_387_Msg,
    [ID2D5Idx]  = CanIL_Tx_PAS_Info8_20ms_2D5_Msg,
    [ID284Idx] = CanIL_Tx_PAS_PSLInfo1_20ms_284_Msg,
    [ID285Idx] = CanIL_Tx_PAS_PSLInfo2_20ms_285_Msg,
    [ID286Idx] = CanIL_Tx_PAS_PSLInfo3_20ms_286_Msg,

    [ID287Idx] = CanIL_Tx_PAS_PSLInfo4_20ms_287_Msg,
    [ID2A4Idx] = CanIL_Tx_PAS_Timestamp_20ms_2A4_Msg,
    [ID2A5Idx] = CanIL_Tx_PAS_TmspMapObj1_20ms_2A5_Msg,
    [ID2B4Idx] = CanIL_Tx_PAS_TmspMapObj2_20ms_2B4_Msg,

    [ID2B5Idx] = CanIL_Tx_PAS_TmspMapObj3_20ms_2B5_Msg,
    [ID665Idx] = CanIL_Tx_PAS_DTC_665_Msg,
    [ID685Idx]  = CanIL_Tx_PAS_VerNum_5000ms_685_Msg,
};
#else
const tpfCanMessageProcess GpfCanMessageProcess[RXANDTXFRAMEMAXNUM] =
{
    /* RX */
    [ID7D2Idx] = NULL,
    [ID7DFIdx] = NULL,

    [ID236Idx] = CanIL_ACU_Info1_20ms_GW_0x236_Msg_Analysis,
    [ID237Idx] = CanIL_ACU_Info2_20ms_GW_0x237_Msg_Analysis,
    [ID395Idx] = CanIL_ADAS_CMD_100ms_0x395_Msg_Analysis,
    [ID384Idx] = CanIL_ADAS_Info1_100msCE_0x384_Msg_Analysis,
    [ID2D4Idx] = CanIL_ADAS_STS1_20ms_0x2D4_Msg_Analysis,

    [ID5A4Idx] = CanIL_ADAS_SYNCTime_500ms_0X5A4_Msg_Analysis,
    [ID504Idx] = CanIL_AP2_STS_100ms_0X504_Msg_Analysis,
    [ID176Idx] = CanIL_EPS_Info_10ms_0X176_Msg_Analysis,
    [ID086Idx] = CanIL_ESP_Info_10ms_0X086_Msg_Analysis,
    [ID096Idx] = CanIL_ESP_Info_10ms_Rt_FD_0X096_Msg_Analysis,

    [ID0D6Idx] = CanIL_ESP_WhlSpd_10ms_0X0D6_Msg_Analysis,
    [ID209Idx] = CanIL_FBCM_BCM_XCU_Info1_20ms_GW_0X209_Msg_Analysis,
    [ID39DIdx] = CanIL_HU_Info1_100ms_E_0X39D_Msg_Analysis,
    [ID09EIdx] = CanIL_HU_Info1_10ms_E_0X09E_Msg_Analysis,
    [ID397Idx] = CanIL_XCU_Info_100ms_CH_0X397_Msg_Analysis,

    [ID0C2Idx] = CanIL_XCU_Info_10ms_FD_0X0C2_Msg_Analysis,
    [ID388Idx] = CanIL_ADAS_Objs1_100ms_FD_0X388_Msg_Analysis,
    /* TX */
    [ID7DAIdx] = NULL,

    [ID238Idx] = CanIL_Tx_PAS_Info_20ms_238_Msg,
    [ID244Idx] = CanIL_Tx_PAS_Info4_20ms_244_Msg,
    [ID085Idx] = CanIL_Tx_PAS_Info5_10ms_085_Msg,
    [ID245Idx] = CanIL_Tx_PAS_Info5_20ms_245_Msg,

    [ID254Idx] = CanIL_Tx_PAS_Info7_20ms_254_Msg,
    [ID2E4Idx] = CanIL_Tx_PAS_Info3_20ms_2E4_Msg,
    [ID2F4Idx] = CanIL_Tx_PAS_Info1_20ms_2F4_Msg,
    [ID394Idx] = CanIL_Tx_PAS_Info4_100ms_394_Msg,

    [ID387Idx] = CanIL_Tx_PAS_Info7_100ms_387_Msg,
    [ID2D5Idx] = CanIL_Tx_PAS_Info8_20ms_2D5_Msg,
    [ID284Idx] = CanIL_Tx_PAS_PSLInfo1_20ms_284_Msg,
    [ID285Idx] = CanIL_Tx_PAS_PSLInfo2_20ms_285_Msg,
    [ID286Idx] = CanIL_Tx_PAS_PSLInfo3_20ms_286_Msg,

    [ID287Idx] = CanIL_Tx_PAS_PSLInfo4_20ms_287_Msg,
    [ID2A4Idx] = CanIL_Tx_PAS_Timestamp_20ms_2A4_Msg,
    [ID2A5Idx] = CanIL_Tx_PAS_TmspMapObj1_20ms_2A5_Msg,
    [ID2B4Idx] = CanIL_Tx_PAS_TmspMapObj2_20ms_2B4_Msg,

    [ID2B5Idx] = CanIL_Tx_PAS_TmspMapObj3_20ms_2B5_Msg,
    [ID665Idx] = CanIL_Tx_PAS_DTC_665_Msg,
    [ID685Idx] = CanIL_Tx_PAS_VerNum_5000ms_685_Msg,
};
#endif
void CanIL_Init(void)
{
    uint8 i;
    for(i=0;i<RXANDTXFRAMEMAXNUM;i++)
    {
        GstrNodeErrCnt[i].Signal_InvalidCnt = 0;
        GstrNodeErrCnt[i].Checksum_InvalidCnt = 0;
        GstrNodeErrCnt[i].Counter_InvalidCnt = 0;
        Gu8NodeALC[i] = 0;
    }    
}
/*********************************************************************
* 函数名称: IL_CanDataInd
*
* 功能描述: IL层确认接收数据
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明: 
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/
void IL_CanDataInd(uint8 LcFrameIdx)
{
    GvCanMessageProcessFlag[LcFrameIdx] = TRUE;
    NCS_SET_BIT(GcFrameStatus[LcFrameIdx],COM_FRAME_STATUS_RX_FLAG, uint8);
}
/*********************************************************************
* 函数名称: IL_MessageCombine_RX
*
* 功能描述: 交互层信号组合-接收报文处理
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明: 无
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/
void IL_MessageCombine_RX(void)
{
    uint8 i;
    for (i = ID7D2Idx; i < RXFRAMEMAXNUM; ++i)
    {
        if(GvCanMessageProcessFlag[i] == TRUE)
        {
            GvCanMessageProcessFlag[i] = FALSE;
            if(GpfCanMessageProcess[i] !=NULL)
            {
                GpfCanMessageProcess[i](GcComDataBuffer[i]);
            }
        }
    }

}
/*********************************************************************
* 函数名称: IL_MessageCombine_TX
*
* 功能描述: 交互层信号组合--处理发送数据填充
*
* 输入参数: uint8 Index：发送函数处理索引下标
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明: 无
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/
void IL_MessageCombine_TX(uint8 Index)
{
    if (GvCanMessageProcessFlag[Index] == TRUE)
    {
        GvCanMessageProcessFlag[Index] = FALSE;
        if (GpfCanMessageProcess[Index] != NULL)
        {
            GpfCanMessageProcess[Index](GcComDataBuffer[Index]);
        }
    }
}
/*********************************************************************
* 函数名称: CanIL_CRCCheckSum_Algorithm
*
* 功能描述: E2E CRC CheckSum校验算法
*
* 输入参数: can com层对应buff，长度，CRC初值
*
* 输出参数: 计算后的CRC值
*
* 返 回 值:  无
*
* 其它说明: 长城A07项目
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/
uint8 CanIL_CRCCheckSum_Algorithm(const uint8 *pmsgData,uint8 dataLen,uint8 crcValue,uint8 msgIdx,uint8 DataID_Idx)
{
    uint8 crc8 = 0;
    uint8 i;
    uint8 Temp;
 
    (void)crcValue;

    //Get Crc form Crc8Table
    for (i = 0; i < dataLen; i++)
    {

        Temp = crc8 ^ pmsgData[i];
        crc8 = Crc8Table[Temp];
    }

    Temp = crc8 ^ Gu8dataID[msgIdx][DataID_Idx];
    crc8 = Crc8Table[Temp];

       
    return crc8;
}


/*********************************************************************
* 函数名称: CheckRollingCount
*
* 功能描述: 
*
* 输入参数: can com层对应buff
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/
static uint8 CheckRollingCount(uint8 preRollingCount,uint8 currRollingCount)
{
    uint8 res = 0;
    RollingCount_ST RollingCountSt;
    RollingCountSt.Value = preRollingCount + 1;

    if(RollingCountSt.Value == currRollingCount)
    {
        return TRUE;
    }
    RollingCountSt.Value ++;

    if(RollingCountSt.Value == currRollingCount)
    {
        return TRUE;
    }
    RollingCountSt.Value ++;

    if(RollingCountSt.Value == currRollingCount)
    {
        return TRUE;
    }

    return res;
}

/*********************************************************************
* 函数名称: CanIL_ACU_Info1_20ms_GW_0x236_Msg_Analysis
*
* 功能描述: 0x236
*
* 输入参数: can com层对应buff
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/
static void CanIL_ACU_Info1_20ms_GW_0x236_Msg_Analysis(const uint8 *pLcMsgType)
{ 
    /*首先将COMbuff指针类型转换为消息类型*/
    const CAN_0x236_20_Type *pLcTyped = (CAN_0x236_20_Type*)pLcMsgType; 
    Car_Yaw_RateType LfCar_Yaw_Rate;
    static uint8 LcAliveCnt = 0;        /*记录上次接收的ALC值，用于与本次接收的ALC判断，初始为0*/

    uint8 LcCheckSum_ABM1Tem = 0;
    uint8 IndexCounter = pLcTyped->bit.SigGroup_122_RlngCtr;
    LcCheckSum_ABM1Tem = pLcTyped->bit.SigGroup_122_ChkSm; /*ABM1校验和*/

#ifndef DONT_NEED_CAL_CRCANDALC 
    if(LcCheckSum_ABM1Tem == CanIL_CRCCheckSum_Algorithm(&(pLcTyped->byte[1]),7,CAL_CRC_INIT_VALUE,DataId_236,IndexCounter))
    {/*CRC校验通过*/

        if(GstrNodeErrCnt[ID236Idx].Checksum_ValidCnt < 3)
        {
            GstrNodeErrCnt[ID236Idx].Checksum_ValidCnt++;
        }

        GstrNodeErrCnt[ID236Idx].Checksum_InvalidCnt = 0;

        //if(((++LcAliveCnt)&0x0F) == pLcTyped->bit.SigGroup_122_RlngCtr)
        if(CheckRollingCount(LcAliveCnt,IndexCounter) == TRUE)
        {/*ALC校验通过,解析相关信号*/
            LcAliveCnt =  IndexCounter; 

            if(GstrNodeErrCnt[ID236Idx].Counter_ValidCnt < 3)
            {
                GstrNodeErrCnt[ID236Idx].Counter_ValidCnt++;
            }

            GstrNodeErrCnt[ID236Idx].Counter_InvalidCnt =0;
#else 
    if(1)
    {
        if(1)
        {
#endif      
            /*0x0:  SIGNOTAVAIL;0x1:  SIG_IN_SPEC;0x2:  SIGFAIL;0x3: Reserved  */ 
            if(3!= pLcTyped->bit.ACUYawRateSnsrSta)/*有效*/
            {

                if(GstrNodeErrCnt[ID236Idx].Signal_ValidCnt < 5)
                {
                    GstrNodeErrCnt[ID236Idx].Signal_ValidCnt++;

                }
                
                GstrNodeErrCnt[ID236Idx].Signal_InvalidCnt = 0;
                uint16 Lu16Temp;
                Lu16Temp = ((pLcTyped->bit.ACUYawRateH8bit << 8) | (pLcTyped->bit.ACUYawRateL8bit));
                LfCar_Yaw_Rate = Lu16Temp*0.01;
                LfCar_Yaw_Rate = LfCar_Yaw_Rate - 300;/* 此时是°/s，需要转化为rad/s */
            }
            else/*无效处理*/
            {

                if(GstrNodeErrCnt[ID236Idx].Signal_InvalidCnt< 10)
                {
                    GstrNodeErrCnt[ID236Idx].Signal_InvalidCnt++;
                }
                GstrNodeErrCnt[ID236Idx].Signal_ValidCnt = 0;
                LfCar_Yaw_Rate = 0;
            }
            WriteCAN_AppSignal_Car_Yaw_Rate(&LfCar_Yaw_Rate);
        }
        else
        {
            if(GstrNodeErrCnt[ID236Idx].Counter_InvalidCnt < 5)
            {
                GstrNodeErrCnt[ID236Idx].Counter_InvalidCnt++;
            }
            else
            {
                LfCar_Yaw_Rate = 0;
                WriteCAN_AppSignal_Car_Yaw_Rate(&LfCar_Yaw_Rate);
            }
            GstrNodeErrCnt[ID236Idx].Counter_ValidCnt = 0;
            LcAliveCnt =  pLcTyped->bit.SigGroup_122_RlngCtr; 
        }
        /*无论ALC校验是否通过，都将接收的ALC作为最新值*/

    }
    else
    {
        if(GstrNodeErrCnt[ID236Idx].Checksum_InvalidCnt < 5)
        {
            GstrNodeErrCnt[ID236Idx].Checksum_InvalidCnt++;
        }
        else
        {
            LfCar_Yaw_Rate = 0;
            WriteCAN_AppSignal_Car_Yaw_Rate(&LfCar_Yaw_Rate);
        }
        GstrNodeErrCnt[ID236Idx].Checksum_ValidCnt = 0; 
        LcAliveCnt =  pLcTyped->bit.SigGroup_122_RlngCtr; 
    }
 

    /**************************InPutPara*****************************/

}
/*********************************************************************
* 函数名称: CanIL_ACU_Info2_20ms_GW_0x237_Msg_Analysis
*
* 功能描述: 0x237
*
* 输入参数: can com层对应buff
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/
static void CanIL_ACU_Info2_20ms_GW_0x237_Msg_Analysis(const uint8 *pLcMsgType)
{
    /*首先将COMbuff指针类型转换为消息类型*/
    const CAN_0x237_20_Type *pLcTyped = (CAN_0x237_20_Type*)pLcMsgType; 
    /* 横纵向加速度暂时未使用，不处理 */
#if 0
    Car_LateralAccType LvCar_LateralAcc;
    Car_LongitudeAccType LvCar_LongitudeAcc;
#endif
	static uint8 LcAliveCnt = 0;   /*记录上次接收的ALC值，用于与本次接收的ALC判断，初始为0*/
#ifndef DONT_NEED_CAL_CRCANDALC 
        

    uint8 LcCheckSum_ABM1Tem = 0;
    uint8 IndexCounter = pLcTyped->bit.SigGroup_110_RlngCtr;

    LcCheckSum_ABM1Tem = pLcTyped->bit.SigGroup_110_ChkSm; /*ABM1校验和*/
    
    if(LcCheckSum_ABM1Tem == CanIL_CRCCheckSum_Algorithm(&(pLcTyped->byte[1]),7,CAL_CRC_INIT_VALUE,DataId_237,IndexCounter))
    {/*CRC校验通过*/

        if(GstrNodeErrCnt[ID237Idx].Checksum_ValidCnt < 3)
        {
            GstrNodeErrCnt[ID237Idx].Checksum_ValidCnt++;
        }

        GstrNodeErrCnt[ID237Idx].Checksum_InvalidCnt = 0;

        if(CheckRollingCount(LcAliveCnt,IndexCounter) == TRUE)
        {/*ALC校验通过,解析相关信号*/
            LcAliveCnt =  pLcTyped->bit.SigGroup_110_RlngCtr; 
            if(GstrNodeErrCnt[ID237Idx].Counter_ValidCnt < 3)
            {
                GstrNodeErrCnt[ID237Idx].Counter_ValidCnt++;
            }

            GstrNodeErrCnt[ID237Idx].Counter_InvalidCnt =0;
#else 
    if(1)
    {
        if(1)
        {
#endif       
#if 0
            /*0x0:  SIGNOTAVAIL;0x1:  SIG_IN_SPEC;0x2:  SIGFAIL;0x3: Reserved  */ 
            if(3 != pLcTyped->bit.ACULatSnsrState)/*有效*/
            {
                LvCar_LateralAcc = (float)(((pLcTyped->bit.ACULatAccH8bit << 8) | (pLcTyped->bit.ACULatAccL8bit))*0.0002-5);
                WriteSysSignal_Car_LateralAcc(&LvCar_LateralAcc);
            }
            else/*无效处理*/
            {
                LvCar_LateralAcc = 0;
                WriteSysSignal_Car_LateralAcc(&LvCar_LateralAcc);
            }
            
            if(3 != pLcTyped->bit.ACULongdSnsrState)/*有效*/
            {
                LvCar_LongitudeAcc = (float)(((pLcTyped->bit.ACULongdAccH8bit << 8) | (pLcTyped->bit.ACULongdAccL8bit))*0.0002-5);
                WriteSysSignal_Car_LongitudeAcc(&LvCar_LongitudeAcc);
            }
            else/*无效处理*/
            {
                LvCar_LongitudeAcc = 0;
                WriteSysSignal_Car_LateralAcc(&LvCar_LongitudeAcc);
            }
#endif
            /*信号无效值判断*/
            if((3 != pLcTyped->bit.ACULatSnsrState)&&(3 != pLcTyped->bit.ACULongdSnsrState))
            {

                if(GstrNodeErrCnt[ID237Idx].Signal_ValidCnt < 5)
                {
                    GstrNodeErrCnt[ID237Idx].Signal_ValidCnt++;
                }
                GstrNodeErrCnt[ID237Idx].Signal_InvalidCnt = 0;
            }
            else/*无效处理*/
            {
                if(GstrNodeErrCnt[ID237Idx].Signal_InvalidCnt< 10)
                {
                    GstrNodeErrCnt[ID237Idx].Signal_InvalidCnt++;
                }
                GstrNodeErrCnt[ID237Idx].Signal_ValidCnt = 0;
            }
        }
        else
        {
            if(GstrNodeErrCnt[ID237Idx].Counter_InvalidCnt < 5)
            {
                GstrNodeErrCnt[ID237Idx].Counter_InvalidCnt++;
            }
            GstrNodeErrCnt[ID237Idx].Counter_ValidCnt = 0;
            LcAliveCnt =  pLcTyped->bit.SigGroup_110_RlngCtr; 
        }
        /*无论ALC校验是否通过，都将接收的ALC作为最新值*/
    }
    else
    {
        if(GstrNodeErrCnt[ID237Idx].Checksum_InvalidCnt < 5)
        {
            GstrNodeErrCnt[ID237Idx].Checksum_InvalidCnt++;
        }
        GstrNodeErrCnt[ID237Idx].Checksum_ValidCnt = 0;
        LcAliveCnt =  pLcTyped->bit.SigGroup_110_RlngCtr; 
    }

    /**************************InPutPara*****************************/

}
/*********************************************************************
* 函数名称: CanIL_ADAS_CMD_100ms_0x395_Msg_Analysis
*
* 功能描述: 0x395
*
* 输入参数: can com层对应buff
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/
static void CanIL_ADAS_CMD_100ms_0x395_Msg_Analysis(const uint8 *pLcMsgType)
{
    /*首先将COMbuff指针类型转换为消息类型*/
    const CAN_0x395_100_Type *pLcTyped = (CAN_0x395_100_Type*)pLcMsgType; 

    static uint8 LcAliveCnt = 0;        /*记录上次接收的ALC值，用于与本次接收的ALC判断，初始为0*/
	
#ifndef DONT_NEED_CAL_CRCANDALC 
    uint8 IndexCounter = pLcTyped->bit.ADAS_CMD_100ms_RlngCtr;

    uint8 LcCheckSum_ABM1Tem = 0;

    LcCheckSum_ABM1Tem = pLcTyped->bit.ADAS_CMD_100ms_ChkSm; /*ABM1校验和*/
    
    if(LcCheckSum_ABM1Tem == CanIL_CRCCheckSum_Algorithm(&(pLcTyped->byte[1]),7,CAL_CRC_INIT_VALUE,DataId_395,IndexCounter))
    {/*CRC校验通过*/
        if(GstrNodeErrCnt[ID395Idx].Checksum_ValidCnt < 3)
        {
            GstrNodeErrCnt[ID395Idx].Checksum_ValidCnt++;
        }
        GstrNodeErrCnt[ID395Idx].Checksum_InvalidCnt = 0;

        if(CheckRollingCount(LcAliveCnt,IndexCounter) == TRUE)
        {/*ALC校验通过,解析相关信号*/
            if(GstrNodeErrCnt[ID395Idx].Counter_ValidCnt < 3)
            {
                GstrNodeErrCnt[ID395Idx].Counter_ValidCnt++;
            }
            GstrNodeErrCnt[ID395Idx].Counter_InvalidCnt =0;
            LcAliveCnt =  pLcTyped->bit.ADAS_CMD_100ms_RlngCtr; 

#else 
    if(1)
    {
        if(1)
        {
#endif        
            WriteCAN_AppSignal_ADAS_PSL_EnableSts((ADAS_PSL_EnableStsType)(pLcTyped->bit.ADAS_PSL_enable));
            WriteCAN_AppSignal_ADAS_PAS_EnableSts((ADAS_PAS_EnableStsType)(pLcTyped->bit.ADAS_PAS_enable));
        }
        else
        {
            if(GstrNodeErrCnt[ID395Idx].Counter_InvalidCnt < 5)
            {
                GstrNodeErrCnt[ID395Idx].Counter_InvalidCnt++;
            }
            else
            {
                WriteCAN_AppSignal_ADAS_PSL_EnableSts(PSL_DISABLE);
                WriteCAN_AppSignal_ADAS_PAS_EnableSts(PAS_DISABLE);
            }
            GstrNodeErrCnt[ID395Idx].Counter_ValidCnt = 0;
            LcAliveCnt =  pLcTyped->bit.ADAS_CMD_100ms_RlngCtr; 
        }
        /*无论ALC校验是否通过，都将接收的ALC作为最新值*/
        
        }
        else
        {
            if(GstrNodeErrCnt[ID395Idx].Checksum_InvalidCnt < 5)
            {
                GstrNodeErrCnt[ID395Idx].Checksum_InvalidCnt++;
            }
            else
            {
                WriteCAN_AppSignal_ADAS_PSL_EnableSts(PSL_DISABLE);
                WriteCAN_AppSignal_ADAS_PAS_EnableSts(PAS_DISABLE);
            }
            GstrNodeErrCnt[ID395Idx].Checksum_ValidCnt = 0;
            LcAliveCnt =  pLcTyped->bit.ADAS_CMD_100ms_RlngCtr; 
        }

    /**************************InPutPara*****************************/

}
/*********************************************************************
* 函数名称: CanIL_ADAS_Info1_100msCE_0x384_Msg_Analysis
*
* 功能描述: 0x384
*
* 输入参数: can com层对应buff
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/
static void CanIL_ADAS_Info1_100msCE_0x384_Msg_Analysis(const uint8 *pLcMsgType)
{
    /*首先将COMbuff指针类型转换为消息类型*/
    const CAN_0x384_100_Type *pLcTyped = (CAN_0x384_100_Type*)pLcMsgType;
    (void) pLcTyped;
    /* 后保载物模式暂未使用，不处理 */
#if 0
    FSD1_RearLoadModeSts_Type LpvSts;
    LpvSts = (FSD1_RearLoadModeSts_Type)pLcTyped->bit.FSD1_RearLoadModeSts;
    WriteComSignal_FSD1_RearLoadModeSts(&LpvSts);
#endif
}
/*********************************************************************
* 函数名称: CanIL_ADAS_STS1_20ms_0x2D4_Msg_Analysis
*
* 功能描述: 0x2D4
*
* 输入参数: can com层对应buff
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/

static void CanIL_ADAS_STS1_20ms_0x2D4_Msg_Analysis(const uint8 *pLcMsgType)
{
    /*首先将COMbuff指针类型转换为消息类型*/
    const CAN_0x2D4_20_Type *pLcTyped = (CAN_0x2D4_20_Type*)pLcMsgType; 
    ADAS_APAStatusType LenuADAS_APAStatus;
    ADAS_slot_ID_SelectedType Lu8Slot_ID_Selected;
    uint8 LuuDataTemp;


    static uint8 LcAliveCnt = 0;        /*记录上次接收的ALC值，用于与本次接收的ALC判断，初始为0*/
#ifndef DONT_NEED_CAL_CRCANDALC 
    uint8 IndexCounter = pLcTyped->bit.ADAS_STS1_20ms_RlngCtr;


    uint8 LcCheckSum_ABM1Tem = 0;

    LcCheckSum_ABM1Tem = pLcTyped->bit.ADAS_STS1_20ms_ChkSm; /*ABM1校验和*/
    
    if(LcCheckSum_ABM1Tem == CanIL_CRCCheckSum_Algorithm(&(pLcTyped->byte[1]),7,CAL_CRC_INIT_VALUE,DataId_2D4,IndexCounter))
    {/*CRC校验通过*/
        if(GstrNodeErrCnt[ID2D4Idx].Checksum_ValidCnt < 3)
        {
            GstrNodeErrCnt[ID2D4Idx].Checksum_ValidCnt++;
        }

        GstrNodeErrCnt[ID2D4Idx].Checksum_InvalidCnt = 0;

        //if(((++LcAliveCnt)&0x0F) == pLcTyped->bit.ADAS_STS1_20ms_RlngCtr)
        if(CheckRollingCount(LcAliveCnt,IndexCounter) == TRUE)
        {/*ALC校验通过,解析相关信号*/
            if(GstrNodeErrCnt[ID2D4Idx].Counter_ValidCnt < 3)
            {
                GstrNodeErrCnt[ID2D4Idx].Counter_ValidCnt++;
            }

            GstrNodeErrCnt[ID2D4Idx].Counter_InvalidCnt =0;
            LcAliveCnt =  pLcTyped->bit.ADAS_STS1_20ms_RlngCtr; 


#else 
    if(1)
    {
        if(1)
        {
#endif      
            LuuDataTemp = pLcTyped->bit.ADAS_APAStatus;
            if(LuuDataTemp < 6)
            {
                LenuADAS_APAStatus = (ADAS_APAStatusType)LuuDataTemp;
            }
            else
            {
                LenuADAS_APAStatus = APA_NONE;
            }
            Lu8Slot_ID_Selected = pLcTyped->bit.ADAS_slot_ID_Selected;

            
            WriteCAN_AppSignal_ADAS_APAStatus(LenuADAS_APAStatus);
            WriteCAN_AppSignal_ADAS_slot_ID_Selected(Lu8Slot_ID_Selected);

            /*判断信号值是否有效*/
            if(0xff != Lu8Slot_ID_Selected)//&&(pLcTyped->bit.ADAS_APAStatus < 5))
            {
                if(GstrNodeErrCnt[ID2D4Idx].Signal_ValidCnt < 5)
                {
                    GstrNodeErrCnt[ID2D4Idx].Signal_ValidCnt++;
                }
                GstrNodeErrCnt[ID2D4Idx].Signal_InvalidCnt = 0;
            }
            else
            {
                if(GstrNodeErrCnt[ID2D4Idx].Signal_InvalidCnt< 10)
                {
                    GstrNodeErrCnt[ID2D4Idx].Signal_InvalidCnt++;
                }
                GstrNodeErrCnt[ID2D4Idx].Signal_ValidCnt = 0;
            }
        }
        else
        {
            if(GstrNodeErrCnt[ID2D4Idx].Counter_InvalidCnt < 5)
            {
                GstrNodeErrCnt[ID2D4Idx].Counter_InvalidCnt++;
            }
            else
            {
                LenuADAS_APAStatus = APA_NONE;
                Lu8Slot_ID_Selected = 0x00;
                WriteCAN_AppSignal_ADAS_APAStatus(LenuADAS_APAStatus);
                WriteCAN_AppSignal_ADAS_slot_ID_Selected(Lu8Slot_ID_Selected);
            }

            GstrNodeErrCnt[ID2D4Idx].Counter_ValidCnt = 0;

            LcAliveCnt =  pLcTyped->bit.ADAS_STS1_20ms_RlngCtr; 

        }
        /*无论ALC校验是否通过，都将接收的ALC作为最新值*/

    }
    else
    {
        if(GstrNodeErrCnt[ID2D4Idx].Checksum_InvalidCnt < 5)
        {
            GstrNodeErrCnt[ID2D4Idx].Checksum_InvalidCnt++;
        }
        else
        {
            LenuADAS_APAStatus = APA_NONE;
            Lu8Slot_ID_Selected = 0x00;
            WriteCAN_AppSignal_ADAS_APAStatus(LenuADAS_APAStatus);
            WriteCAN_AppSignal_ADAS_slot_ID_Selected(Lu8Slot_ID_Selected);
        }

        GstrNodeErrCnt[ID2D4Idx].Checksum_ValidCnt = 0;
 
        LcAliveCnt =  pLcTyped->bit.ADAS_STS1_20ms_RlngCtr; 

    }

    /**************************InPutPara*****************************/

}
/*********************************************************************
* 函数名称: CanIL_ADAS_SYNCTime_500ms_0X5A4_Msg_Analysis
*
* 功能描述: 0X5A4
*
* 输入参数: can com层对应buff
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/
static void CanIL_ADAS_SYNCTime_500ms_0X5A4_Msg_Analysis(const uint8 *pLcMsgType)
{
    /*首先将COMbuff指针类型转换为消息类型*/
    const CAN_0x5A4_500_Type *pLcTyped = (CAN_0x5A4_500_Type*)pLcMsgType; 
    uint8 Lu8CRCLen =8-2;
    uint8 Data[7]={0};
    uint8 i=0;
    static uint8 Lu8CRCError=0;
    //Data[0] = pLcTyped->byte[0];
    for(i=1;i<7;i++)
    {
        Data[i] = pLcTyped->byte[i+1];
    }
    /*首先将COMbuff指针类型转换为消息类型*/
    GstrCANTimeSysnInf.TS_CAN_SYNC_SGW = (CAN_SYSN_SGW_Type)pLcTyped->bit.SYNCTime_SYNC_RF_SGW;
    GstrCANTimeSysnInf.TS_CAN_SYNC_TimeDomain = (uint8)pLcTyped->bit.SYNCTime_SYNC_RF_TimeDomain;/*时间域*/
    if((CAN_SYSN_TO_GTM == GstrCANTimeSysnInf.TS_CAN_SYNC_SGW)&&(0 == GstrCANTimeSysnInf.TS_CAN_SYNC_TimeDomain))/*收到SyncToGTM才处理*/
    {
        
        GstrCANTimeSysnInf.Gu8CAN_Ma_Type = (uint8)pLcTyped->bit.SYNCTime_SYNC_RF_Type;

        if(CAN_SYNC_TYPE == GstrCANTimeSysnInf.Gu8CAN_Ma_Type)/*第一帧同步报文*/
        {
            GstrCANTimeSysnInf.TS_CAN_SYNC_SequenceCounter = (uint8)pLcTyped->bit.SYNCTime_SYNC_RF_SequenceCnt;
            if(pLcTyped->bit.SYNCTime_SYNC_RF_CRC == CanIL_CRCCheckSum_Algorithm(&(Data[1]),Lu8CRCLen,CAL_CRC_INIT_VALUE ,DataId_5A4,GstrCANTimeSysnInf.TS_CAN_SYNC_SequenceCounter ))
            {
                GstrCANTimeSysnInf.Gu32Type_SYNC_RXInt_Time = Gu32CANTimeSysn_RxInt;
                /*获取CANTimeSysn中T0*/
                GstrCANTimeSysnInf.TS_CAN_SYNC_SyncTimeSec =((uint32)(pLcTyped->bit.SYNCTime_SYNC_RF_SyncTimeHHH8bit << 24)) |\
                            ((uint32)(pLcTyped->bit.SYNCTime_SYNC_RF_SyncTimeHHL8bit << 16)) |\
                            ((uint32)(pLcTyped->bit.SYNCTime_SYNC_RF_SyncTimeHLL8bit << 8)) |\
                            ((uint32)(pLcTyped->bit.SYNCTime_SYNC_RF_SyncTimeLLL8bit));
                /*校验信息*/
#if 0
                GstrCANTimeSysnInf.TS_CAN_SYNC_CRC = pLcTyped->bit.SYNCTime_SYNC_RF_CRC;
                GstrCANTimeSysnInf.TS_CAN_SYNC_SequenceCounter = pLcTyped->bit.SYNCTime_SYNC_RF_SequenceCnt;
#endif
                GstrCANTimeSysnInf.TS_CAN_SYNC_SGW = (CAN_SYSN_SGW_Type)pLcTyped->bit.SYNCTime_SYNC_RF_SGW;
                Lu8CRCError=0;

            }
            else
            {
                if(Lu8CRCError < 3)
                {
                    Lu8CRCError++;
                }
                /*CRC校验失败*/
            }
        }
        else if(CAN_FUP_TYPE == GstrCANTimeSysnInf.Gu8CAN_Ma_Type)/*第二帧同步报文*/
        {
            GstrCANTimeSysnInf.TS_CAN_FUP_SequenceCounter = (uint8)pLcTyped->bit.SYNCTime_SYNC_RF_SequenceCnt;
            if(pLcTyped->bit.SYNCTime_SYNC_RF_CRC == CanIL_CRCCheckSum_Algorithm(&(Data[1]),6,CAL_CRC_INIT_VALUE ,DataId_5A4,GstrCANTimeSysnInf.TS_CAN_FUP_SequenceCounter))
            {
                GstrCANTimeSysnInf.Gu32Type_FUP_RXInt_Time = Gu32CANTimeSysn_RxInt;
                /*校验信息*/
#if 0
                GstrCANTimeSysnInf.TS_CAN_FUP_CRC = pLcTyped->bit.SYNCTime_SYNC_RF_CRC;
#endif
                GstrCANTimeSysnInf.TS_CAN_FUP_TimeDomain = (uint8)pLcTyped->bit.SYNCTime_SYNC_RF_TimeDomain;

                GstrCANTimeSysnInf.TS_CAN_FUP_SGW = (CAN_SYSN_SGW_Type)pLcTyped->bit.SYNCTime_SYNC_RF_SGW;
                GstrCANTimeSysnInf.TS_CAN_FUP_OVS = (uint8)pLcTyped->bit.SYNCTime_SYNC_RF_OVS;
                /*计算0x28与0x20两帧数据的时间差*/
                if(GstrCANTimeSysnInf.Gu32Type_FUP_RXInt_Time > GstrCANTimeSysnInf.Gu32Type_SYNC_RXInt_Time)
                {
                    Gu32CANTimeSysn_T3diff = (GstrCANTimeSysnInf.Gu32Type_FUP_RXInt_Time - GstrCANTimeSysnInf.Gu32Type_SYNC_RXInt_Time);
                }
                else
                {
                    Gu32CANTimeSysn_T3diff = (0xFFFFFFFF - GstrCANTimeSysnInf.Gu32Type_SYNC_RXInt_Time +  GstrCANTimeSysnInf.Gu32Type_FUP_RXInt_Time);
                }
                Lu8CRCError=0;
            }
            else
            {
                if(Lu8CRCError < 3)
                {
                    Lu8CRCError++;
                }
                /*CRC校验失败*/
            }
            /*判断是否为一组同步帧*/
            if((GstrCANTimeSysnInf.TS_CAN_SYNC_TimeDomain == GstrCANTimeSysnInf.TS_CAN_FUP_TimeDomain) &&
                (GstrCANTimeSysnInf.TS_CAN_SYNC_SequenceCounter == GstrCANTimeSysnInf.TS_CAN_FUP_SequenceCounter) &&
                (GstrCANTimeSysnInf.TS_CAN_SYNC_SGW == GstrCANTimeSysnInf.TS_CAN_FUP_SGW) &&
                (Gu32CANTimeSysn_T3diff <= 60)&&(Lu8CRCError < 3))/*同步阈相同*//*同步计数相同*//*同步状态相同*//*未超时*//*CRC正确*/
            {
                if(ADAS_SYNCTime_PassCnt < 5)
                {
                    ADAS_SYNCTime_PassCnt++;
                }

                ADAS_SYNCTime_FailedCnt = 0;
                Gu8StartDetect5A4Flag = ENABLEDETECT5A4;/*使能监控5A4*/

                GstrCANTimeSysnInf.TS_CAN_FUP_SyncTimeNSec = ((uint32)(pLcTyped->bit.SYNCTime_SYNC_RF_SyncTimeHHH8bit << 24))|\
                            ((uint32)(pLcTyped->bit.SYNCTime_SYNC_RF_SyncTimeHHL8bit << 16)) |\
                            ((uint32)(pLcTyped->bit.SYNCTime_SYNC_RF_SyncTimeHLL8bit << 8)) |\
                            ((uint32)(pLcTyped->bit.SYNCTime_SYNC_RF_SyncTimeLLL8bit));
                Gu32CANTimeSysn_GlobalTimeBaseYMD = ((GstrCANTimeSysnInf.TS_CAN_SYNC_SyncTimeSec/3600)/24);
                Gu32CANTimeSysn_GlobalTimeBaseHSS = (GstrCANTimeSysnInf.TS_CAN_SYNC_SyncTimeSec%(3600*24));
                /*计算全局时间，单位ms*/
                Gu32CANTimeSysn_GlobalTimeBase = (uint32)(Gu32CANTimeSysn_T3diff + (uint32)(Gu32CANTimeSysn_GlobalTimeBaseHSS* (1000)) +\
                    (uint32)(GstrCANTimeSysnInf.TS_CAN_FUP_OVS *(1000)) + (GstrCANTimeSysnInf.TS_CAN_FUP_SyncTimeNSec/1000000));
            }
            else/*未通过以上校验*/
            {
                if(ADAS_SYNCTime_FailedCnt < 5)
                {
                    ADAS_SYNCTime_FailedCnt++;
                }
                
                ADAS_SYNCTime_PassCnt = 0;

            }
        }
        else
        {

        }

    }
    else
    {

    }
}

/*********************************************************************
* 函数名称: CanIL_AP2_STS_100ms_0X504_Msg_Analysis
*
* 功能描述: 0X504
*
* 输入参数: can com层对应buff
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/
static void CanIL_AP2_STS_100ms_0X504_Msg_Analysis(const uint8 *pLcMsgType)
{
    /*首先将COMbuff指针类型转换为消息类型*/
    const CAN_0x504_100_Type *pLcTyped = (CAN_0x504_100_Type*)pLcMsgType; 
    (void)pLcTyped;
    /* 暂未使用，不处理 */
#if 0
    FSD2_RearLoadModeSts_Type LvSts;
    LvSts =  (FSD2_RearLoadModeSts_Type)pLcTyped->bit.FSD2_RearLoadModeSts;
    WriteComSignal_FSD2_RearLoadModeSts(&LvSts);
#endif
}
/*********************************************************************
* 函数名称: CanIL_EPS_Info_10ms_0X176_Msg_Analysis
*
* 功能描述: 0X176
*
* 输入参数: can com层对应buff
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/ 

static void CanIL_EPS_Info_10ms_0X176_Msg_Analysis(const uint8 *pLcMsgType)
{
    /*首先将COMbuff指针类型转换为消息类型*/
    const CAN_0x176_10_Type *pLcTyped = (CAN_0x176_10_Type*)pLcMsgType; 
    Car_StrAngleType Ls16StrAngle;
    uint16 Lu16DataTemp;

    static uint8 LcAliveCnt = 0;        /*记录上次接收的ALC值，用于与本次接收的ALC判断，初始为0*/
	#ifndef DONT_NEED_CAL_CRCANDALC 
    uint8 IndexCounter = pLcTyped->bit.SigGroup_0x176_RlngCtr;

    uint8 LcCheckSum_ABM1Tem = 0;

    LcCheckSum_ABM1Tem = pLcTyped->bit.SigGroup_0x176_ChkSm; /*ABM1校验和*/
    
    if(LcCheckSum_ABM1Tem == CanIL_CRCCheckSum_Algorithm(&(pLcTyped->byte[1]),31,CAL_CRC_INIT_VALUE,DataId_176,IndexCounter))
    {/*CRC校验通过*/
        if(GstrNodeErrCnt[ID176Idx].Checksum_ValidCnt < 3)
        {
            GstrNodeErrCnt[ID176Idx].Checksum_ValidCnt++;
        }

        GstrNodeErrCnt[ID176Idx].Checksum_InvalidCnt = 0;
        if(CheckRollingCount(LcAliveCnt,IndexCounter) == TRUE)
        {/*ALC校验通过,解析相关信号*/
            if(GstrNodeErrCnt[ID176Idx].Counter_ValidCnt < 3)
            {
                GstrNodeErrCnt[ID176Idx].Counter_ValidCnt++;
            }

            GstrNodeErrCnt[ID176Idx].Counter_InvalidCnt =0;
            LcAliveCnt =  pLcTyped->bit.SigGroup_0x176_RlngCtr;
#else 
    if(1)
    {
        if(1)
        {
#endif         
            if(1 != pLcTyped->bit.EPS_StrgAngValid)
            {
                if(GstrNodeErrCnt[ID176Idx].Signal_ValidCnt < 5)
                {
                    GstrNodeErrCnt[ID176Idx].Signal_ValidCnt++;
                }
                GstrNodeErrCnt[ID176Idx].Signal_InvalidCnt = 0;
                Lu16DataTemp = ((pLcTyped->bit.EPS_StrgAngH8bit<< 8)|(pLcTyped->bit.EPS_StrgAngL8bit));
                Lu16DataTemp = Lu16DataTemp/10;
                Ls16StrAngle = Lu16DataTemp-780;
            }
            else
            {
                if(GstrNodeErrCnt[ID176Idx].Signal_InvalidCnt< 10)
                {
                    GstrNodeErrCnt[ID176Idx].Signal_InvalidCnt++;
                }

                GstrNodeErrCnt[ID176Idx].Signal_ValidCnt = 0;
                Ls16StrAngle = CAN_SIG_INVALID_STR_ANGLE;
            }
            WriteCAN_AppSignal_Car_StrAngle(&Ls16StrAngle);
        }
        else
        {
            if(GstrNodeErrCnt[ID176Idx].Counter_InvalidCnt < 5)
            {
                GstrNodeErrCnt[ID176Idx].Counter_InvalidCnt++;
            }
            else
            {
                Ls16StrAngle = CAN_SIG_INVALID_STR_ANGLE;
                WriteCAN_AppSignal_Car_StrAngle(&Ls16StrAngle);
            }

            GstrNodeErrCnt[ID176Idx].Counter_ValidCnt = 0;
            LcAliveCnt =  pLcTyped->bit.SigGroup_0x176_RlngCtr;
        }
        /*无论ALC校验是否通过，都将接收的ALC作为最新值*/
    }
    else
    {
        if(GstrNodeErrCnt[ID176Idx].Checksum_InvalidCnt < 5)
        {
            GstrNodeErrCnt[ID176Idx].Checksum_InvalidCnt++;
        }
        else
        {
            Ls16StrAngle = CAN_SIG_INVALID_STR_ANGLE;
            WriteCAN_AppSignal_Car_StrAngle(&Ls16StrAngle);
        }

        GstrNodeErrCnt[ID176Idx].Checksum_ValidCnt = 0;
        LcAliveCnt =  pLcTyped->bit.SigGroup_0x176_RlngCtr; 

    }

    /**************************InPutPara*****************************/
}
/*********************************************************************
* 函数名称: CanIL_ESP_Info_10ms_0X086_Msg_Analysis
*
* 功能描述: 0X086
*
* 输入参数: can com层对应buff
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/        
static void CanIL_ESP_Info_10ms_0X086_Msg_Analysis(const uint8 *pLcMsgType)
{
    /*首先将COMbuff指针类型转换为消息类型*/
    const CAN_0x86_10_Type *pLcTyped = (CAN_0x86_10_Type*)pLcMsgType; 

    Car_WheelPulseType LstrCar_WheelInf[CAR_WHEEL_NUM];
    uint8 Lu8DataTemp;

    static uint8 LcAliveCnt = 0;        /*记录上次接收的ALC值，用于与本次接收的ALC判断，初始为0*/
#ifndef DONT_NEED_CAL_CRCANDALC 

    uint8 LcCheckSum_ABM1Tem = 0;
    uint8 IndexCounter = pLcTyped->bit.SigGroup_61_RlngCtr;

    LcCheckSum_ABM1Tem = pLcTyped->bit.SigGroup_61_ChkSm; /*ABM1校验和*/
    
    if(LcCheckSum_ABM1Tem == CanIL_CRCCheckSum_Algorithm(&(pLcTyped->byte[1]),31,CAL_CRC_INIT_VALUE,DataId_086,IndexCounter))
    {/*CRC校验通过*/
        if(GstrNodeErrCnt[ID086Idx].Checksum_ValidCnt < 3)
        {
            GstrNodeErrCnt[ID086Idx].Checksum_ValidCnt++;
        }

        GstrNodeErrCnt[ID086Idx].Checksum_InvalidCnt = 0;

        if(CheckRollingCount(LcAliveCnt,IndexCounter) == TRUE)
        {/*ALC校验通过,解析相关信号*/
            if(GstrNodeErrCnt[ID086Idx].Counter_ValidCnt < 3)
            {
                GstrNodeErrCnt[ID086Idx].Counter_ValidCnt++;
            }

            GstrNodeErrCnt[ID086Idx].Counter_InvalidCnt =0;
            LcAliveCnt =  pLcTyped->bit.SigGroup_61_RlngCtr;
#else 
    if(1)
    {
        if(1)
        {
#endif       

            LstrCar_WheelInf[CAR_FL_WHEEL].u16Car_WheelPulseCnt = pLcTyped->bit.FLWhlSpdPlssCterH8bit<<8 | pLcTyped->bit.FLWhlSpdPlssCterL8bit;
            LstrCar_WheelInf[CAR_FR_WHEEL].u16Car_WheelPulseCnt = pLcTyped->bit.FRWhlSpdPlssCterH8bit<<8 | pLcTyped->bit.FRWhlSpdPlssCterL8bit;
            LstrCar_WheelInf[CAR_RL_WHEEL].u16Car_WheelPulseCnt = pLcTyped->bit.RLWhlSpdPlssCterH8bit<<8 | pLcTyped->bit.RLWhlSpdPlssCterL8bit;
            LstrCar_WheelInf[CAR_RR_WHEEL].u16Car_WheelPulseCnt = pLcTyped->bit.RRWhlSpdPlssCterH8bit<<8 | pLcTyped->bit.RRWhlSpdPlssCterL8bit;

            Lu8DataTemp = pLcTyped->bit.FLWhlRotDircn;
            if(Lu8DataTemp < 3)
            {
                if(Lu8DataTemp == 0x00)
                {
                    LstrCar_WheelInf[CAR_FL_WHEEL].enuCar_WheelPulseDir = CAR_WHEEL_DIR_STOP;
                }
                else if(Lu8DataTemp == 0x01)
                {
                    LstrCar_WheelInf[CAR_FL_WHEEL].enuCar_WheelPulseDir = CAR_WHEEL_DIR_FORWARD;
                }
                else if(Lu8DataTemp == 0x02)
                {
                    LstrCar_WheelInf[CAR_FL_WHEEL].enuCar_WheelPulseDir = CAR_WHEEL_DIR_BACKWARD;
                }
                WriteCAN_AppSignal_Car_WheelPulseAndDir(&LstrCar_WheelInf[CAR_FL_WHEEL],CAR_FL_WHEEL);
            }

            Lu8DataTemp = pLcTyped->bit.FRWhlRotDircn;
            if(Lu8DataTemp < 3)
            {
                if(Lu8DataTemp == 0x00)
                {
                    LstrCar_WheelInf[CAR_FR_WHEEL].enuCar_WheelPulseDir = CAR_WHEEL_DIR_STOP;
                }
                else if(Lu8DataTemp == 0x01)
                {
                    LstrCar_WheelInf[CAR_FR_WHEEL].enuCar_WheelPulseDir = CAR_WHEEL_DIR_FORWARD;
                }
                else if(Lu8DataTemp == 0x02)
                {
                    LstrCar_WheelInf[CAR_FR_WHEEL].enuCar_WheelPulseDir = CAR_WHEEL_DIR_BACKWARD;
                }
                WriteCAN_AppSignal_Car_WheelPulseAndDir(&LstrCar_WheelInf[CAR_FR_WHEEL],CAR_FR_WHEEL);
            }

            Lu8DataTemp = pLcTyped->bit.RLWhlRotDircn;
            if(Lu8DataTemp < 3)
            {
                if(Lu8DataTemp == 0x00)
                {
                    LstrCar_WheelInf[CAR_RL_WHEEL].enuCar_WheelPulseDir = CAR_WHEEL_DIR_STOP;
                }
                else if(Lu8DataTemp == 0x01)
                {
                    LstrCar_WheelInf[CAR_RL_WHEEL].enuCar_WheelPulseDir = CAR_WHEEL_DIR_FORWARD;
                }
                else if(Lu8DataTemp == 0x02)
                {
                    LstrCar_WheelInf[CAR_RL_WHEEL].enuCar_WheelPulseDir = CAR_WHEEL_DIR_BACKWARD;
                }
                WriteCAN_AppSignal_Car_WheelPulseAndDir(&LstrCar_WheelInf[CAR_RL_WHEEL],CAR_RL_WHEEL);
            }

            Lu8DataTemp = pLcTyped->bit.RRWhlRotDircn;
            if(Lu8DataTemp < 3)
            {
                if(Lu8DataTemp == 0x00)
                {
                    LstrCar_WheelInf[CAR_RR_WHEEL].enuCar_WheelPulseDir = CAR_WHEEL_DIR_STOP;
                }
                else if(Lu8DataTemp == 0x01)
                {
                    LstrCar_WheelInf[CAR_RR_WHEEL].enuCar_WheelPulseDir = CAR_WHEEL_DIR_FORWARD;
                }
                else if(Lu8DataTemp == 0x02)
                {
                    LstrCar_WheelInf[CAR_RR_WHEEL].enuCar_WheelPulseDir = CAR_WHEEL_DIR_BACKWARD;
                }
                WriteCAN_AppSignal_Car_WheelPulseAndDir(&LstrCar_WheelInf[CAR_RR_WHEEL],CAR_RR_WHEEL);
            }

            if((pLcTyped->bit.RRWhlRotDircn !=3)&&\
                (pLcTyped->bit.RLWhlRotDircn !=3)&&\
                (pLcTyped->bit.FRWhlRotDircn !=3)&&\
                (pLcTyped->bit.FLWhlRotDircn !=3))
            {
                if(GstrNodeErrCnt[ID086Idx].Signal_ValidCnt < 5)
                {
                    GstrNodeErrCnt[ID086Idx].Signal_ValidCnt++;
                
                }

                GstrNodeErrCnt[ID086Idx].Signal_InvalidCnt = 0;
            }
            else
            {
                if(GstrNodeErrCnt[ID086Idx].Signal_InvalidCnt < 10)
                {
                    GstrNodeErrCnt[ID086Idx].Signal_InvalidCnt++;
                }
                GstrNodeErrCnt[ID086Idx].Signal_ValidCnt = 0;
            }
        }
        else
        {
            if(GstrNodeErrCnt[ID086Idx].Counter_InvalidCnt < 5)
            {
                GstrNodeErrCnt[ID086Idx].Counter_InvalidCnt++;
            }
            else
            {
                LstrCar_WheelInf[CAR_FL_WHEEL].u16Car_WheelPulseCnt = CAN_SIG_INVALID_PULSE_CNT;
                LstrCar_WheelInf[CAR_FR_WHEEL].u16Car_WheelPulseCnt = CAN_SIG_INVALID_PULSE_CNT;
                LstrCar_WheelInf[CAR_RL_WHEEL].u16Car_WheelPulseCnt = CAN_SIG_INVALID_PULSE_CNT;
                LstrCar_WheelInf[CAR_RR_WHEEL].u16Car_WheelPulseCnt = CAN_SIG_INVALID_PULSE_CNT;
                LstrCar_WheelInf[CAR_FL_WHEEL].enuCar_WheelPulseDir = CAR_WHEEL_DIR_INVALID;
                LstrCar_WheelInf[CAR_FR_WHEEL].enuCar_WheelPulseDir = CAR_WHEEL_DIR_INVALID;
                LstrCar_WheelInf[CAR_RL_WHEEL].enuCar_WheelPulseDir = CAR_WHEEL_DIR_INVALID;
                LstrCar_WheelInf[CAR_RR_WHEEL].enuCar_WheelPulseDir = CAR_WHEEL_DIR_INVALID;
                WriteCAN_AppSignal_Car_WheelPulseAndDir(&LstrCar_WheelInf[CAR_FL_WHEEL],CAR_FL_WHEEL);
                WriteCAN_AppSignal_Car_WheelPulseAndDir(&LstrCar_WheelInf[CAR_FR_WHEEL],CAR_FR_WHEEL);
                WriteCAN_AppSignal_Car_WheelPulseAndDir(&LstrCar_WheelInf[CAR_RL_WHEEL],CAR_RL_WHEEL);
                WriteCAN_AppSignal_Car_WheelPulseAndDir(&LstrCar_WheelInf[CAR_RR_WHEEL],CAR_RR_WHEEL);
            }

            GstrNodeErrCnt[ID086Idx].Counter_ValidCnt = 0;

            LcAliveCnt =  pLcTyped->bit.SigGroup_61_RlngCtr;
        }
        /*无论ALC校验是否通过，都将接收的ALC作为最新值*/
    }
    else
    {
        if(GstrNodeErrCnt[ID086Idx].Checksum_InvalidCnt < 5)
        {
            GstrNodeErrCnt[ID086Idx].Checksum_InvalidCnt++;
        }
        else
        {
            LstrCar_WheelInf[CAR_FL_WHEEL].u16Car_WheelPulseCnt = CAN_SIG_INVALID_PULSE_CNT;
            LstrCar_WheelInf[CAR_FR_WHEEL].u16Car_WheelPulseCnt = CAN_SIG_INVALID_PULSE_CNT;
            LstrCar_WheelInf[CAR_RL_WHEEL].u16Car_WheelPulseCnt = CAN_SIG_INVALID_PULSE_CNT;
            LstrCar_WheelInf[CAR_RR_WHEEL].u16Car_WheelPulseCnt = CAN_SIG_INVALID_PULSE_CNT;
            LstrCar_WheelInf[CAR_FL_WHEEL].enuCar_WheelPulseDir = CAR_WHEEL_DIR_INVALID;
            LstrCar_WheelInf[CAR_FR_WHEEL].enuCar_WheelPulseDir = CAR_WHEEL_DIR_INVALID;
            LstrCar_WheelInf[CAR_RL_WHEEL].enuCar_WheelPulseDir = CAR_WHEEL_DIR_INVALID;
            LstrCar_WheelInf[CAR_RR_WHEEL].enuCar_WheelPulseDir = CAR_WHEEL_DIR_INVALID;
            WriteCAN_AppSignal_Car_WheelPulseAndDir(&LstrCar_WheelInf[CAR_FL_WHEEL],CAR_FL_WHEEL);
            WriteCAN_AppSignal_Car_WheelPulseAndDir(&LstrCar_WheelInf[CAR_FR_WHEEL],CAR_FR_WHEEL);
            WriteCAN_AppSignal_Car_WheelPulseAndDir(&LstrCar_WheelInf[CAR_RL_WHEEL],CAR_RL_WHEEL);
            WriteCAN_AppSignal_Car_WheelPulseAndDir(&LstrCar_WheelInf[CAR_RR_WHEEL],CAR_RR_WHEEL);
        }

        GstrNodeErrCnt[ID086Idx].Checksum_ValidCnt = 0; 
        LcAliveCnt =  pLcTyped->bit.SigGroup_61_RlngCtr; 

    }

    /**************************InPutPara*****************************/

}
/*********************************************************************
* 函数名称: CanIL_ESP_Info_10ms_Rt_FD_0X096_Msg_Analysis
*
* 功能描述: 0X096
*
* 输入参数: can com层对应buff
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/        
static void CanIL_ESP_Info_10ms_Rt_FD_0X096_Msg_Analysis(const uint8 *pLcMsgType)
{
    /*首先将COMbuff指针类型转换为消息类型*/
    const CAN_0x96_10_Type *pLcTyped = (CAN_0x96_10_Type*)pLcMsgType; 

    Car_SpeedType Lu16CarSpd = 0;
    Car_SpeedType_Phy LfCar_SpeedPhy;
    Car_SpeedVildType Lu8Car_SpeedVild;
    Car_SnapshotSpeedType Lu16Car_SpeedForSnapshotData;
    
    static uint8 LcAliveCnt = 0;        /*记录上次接收的ALC值，用于与本次接收的ALC判断，初始为0*/	
#ifndef DONT_NEED_CAL_CRCANDALC 
    uint8 IndexCounter = pLcTyped->bit.SigGroup_206_RlngCtr;

    uint8 LcCheckSum_ABM1Tem = 0;

    LcCheckSum_ABM1Tem = pLcTyped->bit.VSigGroup_206_ChkSm; /*ABM1校验和*/
    
    if(LcCheckSum_ABM1Tem == CanIL_CRCCheckSum_Algorithm(&(pLcTyped->byte[1]),7,CAL_CRC_INIT_VALUE,DataId_096,IndexCounter))
    {/*CRC校验通过*/
        if(GstrNodeErrCnt[ID096Idx].Checksum_ValidCnt < 3)
        {
            GstrNodeErrCnt[ID096Idx].Checksum_ValidCnt++;
        }

        GstrNodeErrCnt[ID096Idx].Checksum_InvalidCnt = 0;
 
        //if(((++LcAliveCnt)&0x0F) == pLcTyped->bit.SigGroup_206_RlngCtr)
        if(CheckRollingCount(LcAliveCnt,IndexCounter) == TRUE)
        {/*ALC校验通过,解析相关信号*/
            if(GstrNodeErrCnt[ID096Idx].Counter_ValidCnt < 3)
            {
                GstrNodeErrCnt[ID096Idx].Counter_ValidCnt++;
            }
            GstrNodeErrCnt[ID096Idx].Counter_InvalidCnt =0;
            LcAliveCnt =  pLcTyped->bit.SigGroup_206_RlngCtr;
#else 
    if(1)
    {
        if(1)
        {
#endif          
            if(0 == pLcTyped->bit.VehSpdValid)
            {
                Lu8Car_SpeedVild = 0;
                Lu16Car_SpeedForSnapshotData = (uint16)(((uint16)(pLcTyped->bit.VehSpdH8bit) << 5)) |( (uint16)(pLcTyped->bit.VehSpdL5bit));
                
                if(Lu16Car_SpeedForSnapshotData == 0x1FFF)
                {
                    Lu16CarSpd = CAN_SIG_INVALID_SPD_VALUE;
                    LfCar_SpeedPhy = 0;
                    Lu16Car_SpeedForSnapshotData = 0;
                }
                else
                {
                    Lu16CarSpd = (uint16)(Lu16Car_SpeedForSnapshotData*5.625);
                    LfCar_SpeedPhy = Lu16Car_SpeedForSnapshotData*0.05625;
                }
                /* 待添加原始值，以供快照记录使用 */
            }
            else
            {
                Lu16CarSpd = CAN_SIG_INVALID_SPD_VALUE;
                LfCar_SpeedPhy = 0;
                Lu16Car_SpeedForSnapshotData = 0;
                Lu8Car_SpeedVild = 1;
            }
            /*判断信号有效性*/
            if((0 == pLcTyped->bit.VehSpdValid)&&(CAN_SIG_INVALID_SPD_VALUE !=Lu16CarSpd))
            {
                if(GstrNodeErrCnt[ID096Idx].Signal_ValidCnt < 5)
                {
                    GstrNodeErrCnt[ID096Idx].Signal_ValidCnt++;
                }
                GstrNodeErrCnt[ID096Idx].Signal_InvalidCnt = 0;
            }
            else
            {
                if(GstrNodeErrCnt[ID096Idx].Signal_InvalidCnt< 10)
                {
                    GstrNodeErrCnt[ID096Idx].Signal_InvalidCnt++;
                }
                GstrNodeErrCnt[ID096Idx].Signal_ValidCnt = 0;
            } 
            WriteCAN_AppSignal_Car_Speed(&Lu16CarSpd);
            WriteSysSignal_Car_Speed_phy(&LfCar_SpeedPhy);
            WriteSysSignal_Car_SpeedForSnapshotData(&Lu16Car_SpeedForSnapshotData);
            WriteSysSignal_Car_SpeedVild(&Lu8Car_SpeedVild);
        }
        else
        {
            if(GstrNodeErrCnt[ID096Idx].Counter_InvalidCnt < 5)
            {
                GstrNodeErrCnt[ID096Idx].Counter_InvalidCnt++;
            }
            else
            {
                Lu16CarSpd = CAN_SIG_INVALID_SPD_VALUE;
                LfCar_SpeedPhy = 0;
                Lu8Car_SpeedVild = 0;
                Lu16Car_SpeedForSnapshotData = 0;
                WriteCAN_AppSignal_Car_Speed(&Lu16CarSpd);
                WriteSysSignal_Car_Speed_phy(&LfCar_SpeedPhy);
                WriteSysSignal_Car_SpeedForSnapshotData(&Lu16Car_SpeedForSnapshotData);
                WriteSysSignal_Car_SpeedVild(&Lu8Car_SpeedVild);
            }

            GstrNodeErrCnt[ID096Idx].Counter_ValidCnt = 0;
            LcAliveCnt =  pLcTyped->bit.SigGroup_206_RlngCtr;
        }
        /*无论ALC校验是否通过，都将接收的ALC作为最新值*/
    }
    else
    {
        if(GstrNodeErrCnt[ID096Idx].Checksum_InvalidCnt < 5)
        {
            GstrNodeErrCnt[ID096Idx].Checksum_InvalidCnt++;
        }
        else
        {
            Lu16CarSpd = CAN_SIG_INVALID_SPD_VALUE;
            LfCar_SpeedPhy = 0;
            Lu8Car_SpeedVild = 0;
            Lu16Car_SpeedForSnapshotData = 0;
            WriteCAN_AppSignal_Car_Speed(&Lu16CarSpd);
            WriteSysSignal_Car_Speed_phy(&LfCar_SpeedPhy);
            WriteSysSignal_Car_SpeedForSnapshotData(&Lu16Car_SpeedForSnapshotData);
            WriteSysSignal_Car_SpeedVild(&Lu8Car_SpeedVild);
        }
        
        GstrNodeErrCnt[ID096Idx].Checksum_ValidCnt = 0;
        LcAliveCnt =  pLcTyped->bit.SigGroup_206_RlngCtr; 

    }

    /**************************InPutPara*****************************/

}

/*********************************************************************
* 函数名称: CanIL_ESP_WhlSpd_10ms_0X0D6_Msg_Analysis
*
* 功能描述: 0D6报文消息解析
*
* 输入参数: can com层对应buff
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/  

static void CanIL_ESP_WhlSpd_10ms_0X0D6_Msg_Analysis(const uint8 *pLcMsgType)
{
    /*首先将COMbuff指针类型转换为消息类型*/
    const CAN_0xD6_10_Type *pLcTyped = (CAN_0xD6_10_Type*)pLcMsgType; 

    static uint8 LcAliveCnt = 0;        /*记录上次接收的ALC值，用于与本次接收的ALC判断，初始为0*/
    
    Wheel_SpeedType Lu16Wheel_Speed[CAR_WHEEL_NUM];
    uint16 Lu16DataTemp;
#ifndef DONT_NEED_CAL_CRCANDALC 
    uint8 IndexCounter = pLcTyped->bit.SigGroup_229_RlngCtr;
    uint8 LcCheckSum_ABM1Tem = 0;

    LcCheckSum_ABM1Tem = pLcTyped->bit.SigGroup_229_ChkSm; /*ABM1校验和*/
    
    if(LcCheckSum_ABM1Tem == CanIL_CRCCheckSum_Algorithm(&(pLcTyped->byte[1]),31,CAL_CRC_INIT_VALUE,DataId_0D6,IndexCounter))
    {/*CRC校验通过*/
        if(GstrNodeErrCnt[ID0D6Idx].Checksum_ValidCnt < 3)
        {
            GstrNodeErrCnt[ID0D6Idx].Checksum_ValidCnt++;
        }
        GstrNodeErrCnt[ID0D6Idx].Checksum_InvalidCnt = 0;

        if(CheckRollingCount(LcAliveCnt,IndexCounter) == TRUE)
        {/*ALC校验通过,解析相关信号*/
            if(GstrNodeErrCnt[ID0D6Idx].Counter_ValidCnt < 3)
            {
                GstrNodeErrCnt[ID0D6Idx].Counter_ValidCnt++;
            }
            GstrNodeErrCnt[ID0D6Idx].Counter_InvalidCnt =0;
            LcAliveCnt =  pLcTyped->bit.SigGroup_229_RlngCtr;
#else 
    if(1)
    {
        if(1)
        {
#endif        

            if(pLcTyped->bit.FLWhlSpdValid == 0x00)
            {
                Lu16DataTemp = (pLcTyped->bit.FLWhlSpdH8bit<<5 | pLcTyped->bit.FLWhlSpd5bit);
                if(Lu16DataTemp != 0x1FFF)
                {
                    Lu16Wheel_Speed[CAR_FL_WHEEL] = (uint16)(Lu16DataTemp*5.625);
                }
                else
                {
                    Lu16Wheel_Speed[CAR_FL_WHEEL] = CAN_SIG_INVALID_SPD_VALUE;
                }
            }
            else
            {
                Lu16Wheel_Speed[CAR_FL_WHEEL] = CAN_SIG_INVALID_SPD_VALUE;
            }

            if(pLcTyped->bit.FRWhlSpdValid == 0x00)
            {
                Lu16DataTemp = (pLcTyped->bit.FRWhlSpdH8bit<<5 | pLcTyped->bit.FRWhlSpd5bit);
                if(Lu16DataTemp != 0x1FFF)
                {
                    Lu16Wheel_Speed[CAR_FR_WHEEL] = (uint16)(Lu16DataTemp*5.625);
                }
                else
                {
                    Lu16Wheel_Speed[CAR_FR_WHEEL] = CAN_SIG_INVALID_SPD_VALUE;
                }
            }
            else
            {
                Lu16Wheel_Speed[CAR_FR_WHEEL] = CAN_SIG_INVALID_SPD_VALUE;
            }

            if(pLcTyped->bit.RLWhlSpdValid == 0x00)
            {
                Lu16DataTemp = (pLcTyped->bit.RLWhlSpdH8bit<<5 | pLcTyped->bit.RLWhlSpd5bit);
                if(Lu16DataTemp != 0x1FFF)
                {
                    Lu16Wheel_Speed[CAR_RL_WHEEL] = (uint16)(Lu16DataTemp*5.625);
                }
                else
                {
                    Lu16Wheel_Speed[CAR_RL_WHEEL] = CAN_SIG_INVALID_SPD_VALUE;
                }
            }
            else
            {
                Lu16Wheel_Speed[CAR_RL_WHEEL] = CAN_SIG_INVALID_SPD_VALUE;
            }
            
            if(pLcTyped->bit.RRWhlSpdValid == 0x00)
            {
                Lu16DataTemp = (pLcTyped->bit.RRWhlSpdH8bit<<5 | pLcTyped->bit.RRWhlSpd5bit);
                if(Lu16DataTemp != 0x1FFF)
                {
                    Lu16Wheel_Speed[CAR_RR_WHEEL] = (uint16)(Lu16DataTemp*5.625);
                }
                else
                {
                    Lu16Wheel_Speed[CAR_RR_WHEEL] = CAN_SIG_INVALID_SPD_VALUE;
                }
            }
            else
            {
                Lu16Wheel_Speed[CAR_RR_WHEEL] = CAN_SIG_INVALID_SPD_VALUE;
            }
            WriteCAN_AppSignal_Wheel_Speed(&Lu16Wheel_Speed[CAR_FL_WHEEL],CAR_FL_WHEEL);
            WriteCAN_AppSignal_Wheel_Speed(&Lu16Wheel_Speed[CAR_FR_WHEEL],CAR_FR_WHEEL);
            WriteCAN_AppSignal_Wheel_Speed(&Lu16Wheel_Speed[CAR_RL_WHEEL],CAR_RL_WHEEL);
            WriteCAN_AppSignal_Wheel_Speed(&Lu16Wheel_Speed[CAR_RR_WHEEL],CAR_RR_WHEEL);

            if((Lu16Wheel_Speed[CAR_FL_WHEEL] != CAN_SIG_INVALID_SPD_VALUE)&&\
                (Lu16Wheel_Speed[CAR_FR_WHEEL] != CAN_SIG_INVALID_SPD_VALUE)&&\
                (Lu16Wheel_Speed[CAR_RL_WHEEL] != CAN_SIG_INVALID_SPD_VALUE)&&\
                (Lu16Wheel_Speed[CAR_RR_WHEEL] != CAN_SIG_INVALID_SPD_VALUE))
            {
                if(GstrNodeErrCnt[ID0D6Idx].Signal_ValidCnt < 5)
                {
                    GstrNodeErrCnt[ID0D6Idx].Signal_ValidCnt++;
                }
                GstrNodeErrCnt[ID0D6Idx].Signal_InvalidCnt = 0;
            }
            else
            {
                if(GstrNodeErrCnt[ID0D6Idx].Signal_InvalidCnt< 10)
                {
                    GstrNodeErrCnt[ID0D6Idx].Signal_InvalidCnt++;
                }

                GstrNodeErrCnt[ID0D6Idx].Signal_ValidCnt = 0;
            }
        }
        else
        {
            if(GstrNodeErrCnt[ID0D6Idx].Counter_InvalidCnt < 5)
            {
                GstrNodeErrCnt[ID0D6Idx].Counter_InvalidCnt++;
            }
            else
            {
                Lu16Wheel_Speed[CAR_FL_WHEEL] = CAN_SIG_INVALID_SPD_VALUE;
                Lu16Wheel_Speed[CAR_FR_WHEEL] = CAN_SIG_INVALID_SPD_VALUE;
                Lu16Wheel_Speed[CAR_RL_WHEEL] = CAN_SIG_INVALID_SPD_VALUE;
                Lu16Wheel_Speed[CAR_RR_WHEEL] = CAN_SIG_INVALID_SPD_VALUE;
                WriteCAN_AppSignal_Wheel_Speed(&Lu16Wheel_Speed[CAR_FL_WHEEL],CAR_FL_WHEEL);
                WriteCAN_AppSignal_Wheel_Speed(&Lu16Wheel_Speed[CAR_FR_WHEEL],CAR_FR_WHEEL);
                WriteCAN_AppSignal_Wheel_Speed(&Lu16Wheel_Speed[CAR_RL_WHEEL],CAR_RL_WHEEL);
                WriteCAN_AppSignal_Wheel_Speed(&Lu16Wheel_Speed[CAR_RR_WHEEL],CAR_RR_WHEEL);
            }
            GstrNodeErrCnt[ID0D6Idx].Counter_ValidCnt = 0;
            LcAliveCnt =  pLcTyped->bit.SigGroup_229_RlngCtr;
       }
        /*无论ALC校验是否通过，都将接收的ALC作为最新值*/
    }
    else
    {
        if(GstrNodeErrCnt[ID0D6Idx].Checksum_InvalidCnt < 5)
        {
            GstrNodeErrCnt[ID0D6Idx].Checksum_InvalidCnt++;
        }
        else
        {
            Lu16Wheel_Speed[CAR_FL_WHEEL] = CAN_SIG_INVALID_SPD_VALUE;
            Lu16Wheel_Speed[CAR_FR_WHEEL] = CAN_SIG_INVALID_SPD_VALUE;
            Lu16Wheel_Speed[CAR_RL_WHEEL] = CAN_SIG_INVALID_SPD_VALUE;
            Lu16Wheel_Speed[CAR_RR_WHEEL] = CAN_SIG_INVALID_SPD_VALUE;
            WriteCAN_AppSignal_Wheel_Speed(&Lu16Wheel_Speed[CAR_FL_WHEEL],CAR_FL_WHEEL);
            WriteCAN_AppSignal_Wheel_Speed(&Lu16Wheel_Speed[CAR_FR_WHEEL],CAR_FR_WHEEL);
            WriteCAN_AppSignal_Wheel_Speed(&Lu16Wheel_Speed[CAR_RL_WHEEL],CAR_RL_WHEEL);
            WriteCAN_AppSignal_Wheel_Speed(&Lu16Wheel_Speed[CAR_RR_WHEEL],CAR_RR_WHEEL);
        }

        GstrNodeErrCnt[ID0D6Idx].Checksum_ValidCnt = 0;

        LcAliveCnt =  pLcTyped->bit.SigGroup_229_RlngCtr; 
    }

    /**************************InPutPara*****************************/

}
/*********************************************************************
* 函数名称: CanIL_FBCM_BCM_XCU_Info1_20ms_GW_0X209_Msg_Analysis
*
* 功能描述: 209报文消息解析
*
* 输入参数: can com层对应buff
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/
uint8 Lu8PowerModeTest = 0;
static void CanIL_FBCM_BCM_XCU_Info1_20ms_GW_0X209_Msg_Analysis(const uint8 *pLcMsgType)
{
    /*首先将COMbuff指针类型转换为消息类型*/
    const CAN_0x209_20_Type *pLcTyped = (CAN_0x209_20_Type*)pLcMsgType; 
    LowVolPwrMdFlagType LenuLowVolPwrMdFlag;
    LowVolPwrMdType LenuLowVolPwrMd;
    LowVolPwrMdFlag2_Type LenuLowVolPwrMdFlag2;

    static uint8 LcAliveCnt = 0;
#ifndef DONT_NEED_CAL_CRCANDALC 
       /*记录上次接收的ALC值，用于与本次接收的ALC判断，初始为0*/
    uint8 IndexCounter = pLcTyped->bit.SigGroup_0x209_RlngCtr;

    uint8 LcCheckSum_ABM1Tem = 0;

    LcCheckSum_ABM1Tem = pLcTyped->bit.SigGroup_0x209_ChkSm; /*ABM1校验和*/
    
    if(LcCheckSum_ABM1Tem == CanIL_CRCCheckSum_Algorithm(&(pLcTyped->byte[1]),7,CAL_CRC_INIT_VALUE,DataId_209,IndexCounter))
    {/*CRC校验通过*/
        if(GstrNodeErrCnt[ID209Idx].Checksum_ValidCnt < 3)
        {
            GstrNodeErrCnt[ID209Idx].Checksum_ValidCnt++;
        }

        GstrNodeErrCnt[ID209Idx].Checksum_InvalidCnt = 0;
  
        if(CheckRollingCount(LcAliveCnt,IndexCounter) == TRUE)
        {/*ALC校验通过,解析相关信号*/
            if(GstrNodeErrCnt[ID209Idx].Counter_ValidCnt < 3)
            {
                GstrNodeErrCnt[ID209Idx].Counter_ValidCnt++;
            }
            GstrNodeErrCnt[ID209Idx].Counter_InvalidCnt =0;
            LcAliveCnt =  pLcTyped->bit.SigGroup_0x209_RlngCtr; 
#else 
    if(1)
    {
        if(1)
        {
#endif        
            LenuLowVolPwrMdFlag = (LowVolPwrMdFlagType)pLcTyped->bit.LowVolPwrMdFlag;
            LenuLowVolPwrMd = (LowVolPwrMdType)pLcTyped->bit.LowVolPwrMd;
            LenuLowVolPwrMdFlag2 = (LowVolPwrMdFlag2_Type)pLcTyped->bit.LowVolPwrMdFlag2;
            Lu8PowerModeTest = pLcTyped->bit.LowVolPwrMd;
            WriteCAN_AppSignal_LowVolPwrMdFlag(LenuLowVolPwrMdFlag);
            WriteCAN_AppSignal_LowVolPwrMd(LenuLowVolPwrMd);
            WriteComSignal_LowVolPwrMdFlag2(&LenuLowVolPwrMdFlag2);
            /* Update this signal when both checksum and alivecounter are correct, only used to enable DTC diagnostics */
            WriteCAN_AppSignal_LowVolPwrMd_DTCDiag(LenuLowVolPwrMd);
        }
        else
        {
            if(GstrNodeErrCnt[ID209Idx].Counter_InvalidCnt < 5)
            {
                GstrNodeErrCnt[ID209Idx].Counter_InvalidCnt++;
            }
            else
            {
                LenuLowVolPwrMdFlag = LOCAL_MODE;
                LenuLowVolPwrMd = POWER_OFF;
                LenuLowVolPwrMdFlag2 = LOWPOWER_MODFLAG2_LOCAL;
                WriteCAN_AppSignal_LowVolPwrMdFlag(LenuLowVolPwrMdFlag);
                WriteCAN_AppSignal_LowVolPwrMd(LenuLowVolPwrMd);
                WriteComSignal_LowVolPwrMdFlag2(&LenuLowVolPwrMdFlag2);
            }

            GstrNodeErrCnt[ID209Idx].Counter_ValidCnt = 0;
            LcAliveCnt =  pLcTyped->bit.SigGroup_0x209_RlngCtr; 
        }
        /*无论ALC校验是否通过，都将接收的ALC作为最新值*/
    }
    else
    {
        if(GstrNodeErrCnt[ID209Idx].Checksum_InvalidCnt < 5)
        {
            GstrNodeErrCnt[ID209Idx].Checksum_InvalidCnt++;
        }
        else
        {
            LenuLowVolPwrMdFlag = LOCAL_MODE;
            LenuLowVolPwrMd = POWER_OFF;
            LenuLowVolPwrMdFlag2 = LOWPOWER_MODFLAG2_LOCAL;
            WriteCAN_AppSignal_LowVolPwrMdFlag(LenuLowVolPwrMdFlag);
            WriteCAN_AppSignal_LowVolPwrMd(LenuLowVolPwrMd);
            WriteComSignal_LowVolPwrMdFlag2(&LenuLowVolPwrMdFlag2);
        }
        GstrNodeErrCnt[ID209Idx].Checksum_ValidCnt = 0;
        LcAliveCnt =  pLcTyped->bit.SigGroup_0x209_RlngCtr; 
    }


    /**************************InPutPara*****************************/

}
/*********************************************************************
* 函数名称: CanIL_HU_Info1_100ms_E_0X39D_Msg_Analysis
*
* 功能描述: 39D
*
* 输入参数: can com层对应buff
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/
static void CanIL_HU_Info1_100ms_E_0X39D_Msg_Analysis(const uint8 *pLcMsgType)
{
    /*首先将COMbuff指针类型转换为消息类型*/
    const CAN_0x39D_100_Type *pLcTyped = (CAN_0x39D_100_Type*)pLcMsgType; 

    GsIntputParaStruct.Bytes.GdwVehTotDistanceVal = (uint32)pLcTyped->bit.TotalOdometerH8bit << 16u|\
    (uint32)pLcTyped->bit.TotalOdometerM8bit << 8u |\
    (uint32)pLcTyped->bit.TotalOdometerL8bit;
#if 0/*不需要判断39D报文信号无效*/
    if(0xFFFFFF != GsIntputParaStruct.Bytes.GdwVehTotDistanceVal)
    {
        if(GstrNodeErrCnt[ID39DIdx].Signal_ValidCnt < 5)
        {
            GstrNodeErrCnt[ID39DIdx].Signal_ValidCnt++;
        
        }
        GstrNodeErrCnt[ID39DIdx].Signal_InvalidCnt = 0;
    }
    else/*0xFFFFFF:  Invalid*/
    {
        if(GstrNodeErrCnt[ID39DIdx].Signal_InvalidCnt< 10)
        {
            GstrNodeErrCnt[ID39DIdx].Signal_InvalidCnt++;
        }
        GstrNodeErrCnt[ID39DIdx].Signal_ValidCnt = 0;
    }
#endif
    /**************************InPutPara*****************************/
}
/*********************************************************************
* 函数名称: CanIL_HU_Info1_10ms_E_0X09E_Msg_Analysis
*
* 功能描述: 09E报文消息解析
*
* 输入参数: can com层对应buff
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/

static void CanIL_HU_Info1_10ms_E_0X09E_Msg_Analysis(const uint8 *pLcMsgType)
{
    /*首先将COMbuff指针类型转换为消息类型*/
    const CAN_0x9E_10_Type *pLcTyped = (CAN_0x9E_10_Type*)pLcMsgType; 
    
    GsIntputParaStruct.Bytes.GsSettingTime.Bits.UTCTimeValidFlag = pLcTyped->bit.UTCTimeValidFlag;

    if((1 != GsIntputParaStruct.Bytes.GsSettingTime.Bits.UTCTimeValidFlag)&&(3 != GsIntputParaStruct.Bytes.GsSettingTime.Bits.UTCTimeValidFlag))
    {
        if(3 != pLcTyped->bit.EastOrWestTimeZone)
        {
            GsIntputParaStruct.Bytes.GsSettingTime.Bits.TimeZone = pLcTyped->bit.EastOrWestTimeZone;
        }
        if( pLcTyped->bit.TimeZoneNum <= 0x0C)
        {
            GsIntputParaStruct.Bytes.GsSettingTime.Bits.TimeZoneNum = pLcTyped->bit.TimeZoneNum;
        }
        GsIntputParaStruct.Bytes.GsSettingTime.Bits.UTCTime0 = pLcTyped->bit.UTCTimeHHHHH8BIT;
        GsIntputParaStruct.Bytes.GsSettingTime.Bits.UTCTime1 = pLcTyped->bit.UTCTimeHHHHL8BIT;
        GsIntputParaStruct.Bytes.GsSettingTime.Bits.UTCTime2 = pLcTyped->bit.UTCTimeHHHLL8BIT;
        GsIntputParaStruct.Bytes.GsSettingTime.Bits.UTCTime3 = pLcTyped->bit.UTCTimeHHLLL8BIT;
        GsIntputParaStruct.Bytes.GsSettingTime.Bits.UTCTime4 = pLcTyped->bit.UTCTimeHLLLL8BIT;
        GsIntputParaStruct.Bytes.GsSettingTime.Bits.UTCTime5 = pLcTyped->bit.UTCTimeLLLLL8BIT;
    }
#if 0 /*不需要判断09E报文信号无效*/
    if((1 != GsIntputParaStruct.Bytes.GsSettingTime.Bits.UTCTimeValidFlag)&&\
        (3 != GsIntputParaStruct.Bytes.GsSettingTime.Bits.UTCTimeValidFlag)&&\
        (3 != pLcTyped->bit.EastOrWestTimeZone)&&\
        ( pLcTyped->bit.TimeZoneNum <= 0x0C))
    {
        if(GstrNodeErrCnt[ID09EIdx].Signal_ValidCnt < 5)
        {
            GstrNodeErrCnt[ID09EIdx].Signal_ValidCnt++;
        }
        GstrNodeErrCnt[ID09EIdx].Signal_InvalidCnt = 0;
    }
    else
    {
        if(GstrNodeErrCnt[ID09EIdx].Signal_InvalidCnt< 10)
        {
            GstrNodeErrCnt[ID09EIdx].Signal_InvalidCnt++;
        }
        GstrNodeErrCnt[ID09EIdx].Signal_ValidCnt = 0;
    }
#endif
}
/*********************************************************************
* 函数名称: CanIL_XCU_Info_100ms_CH_0X397_Msg_Analysis
*
* 功能描述: 397报文消息解析
*
* 输入参数: can com层对应buff
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/

static void CanIL_XCU_Info_100ms_CH_0X397_Msg_Analysis(const uint8 *pLcMsgType)
{
    /*首先将COMbuff指针类型转换为消息类型*/
    const CAN_0x397_100_Type *pLcTyped = (CAN_0x397_100_Type*)pLcMsgType;

    Car_AmbientTempType Ls8AmbientTemp;
    uint16 Lu16AmbientTemp_Temp;

    
    Lu16AmbientTemp_Temp = (uint16)((uint16)pLcTyped->bit.DspOtsdTempH8BIT << 3 )|((uint16)pLcTyped->bit.DspOtsdTempL3BIT);

    if((1 == pLcTyped->bit.OtsdTempV)&&(0x7FF != Lu16AmbientTemp_Temp))
    {
        Lu16AmbientTemp_Temp = Lu16AmbientTemp_Temp/10;
        Ls8AmbientTemp = Lu16AmbientTemp_Temp-40;
        if(GstrNodeErrCnt[ID397Idx].Signal_ValidCnt < 5)
        {
            GstrNodeErrCnt[ID397Idx].Signal_ValidCnt++;
        }
        GstrNodeErrCnt[ID397Idx].Signal_InvalidCnt = 0;
    }
    else/*0x0:  Invalid*/
    {
        if(GstrNodeErrCnt[ID397Idx].Signal_InvalidCnt< 10)
        {
            GstrNodeErrCnt[ID397Idx].Signal_InvalidCnt++;
        }
        GstrNodeErrCnt[ID397Idx].Signal_ValidCnt = 0;
        Ls8AmbientTemp = CAN_SIG_DEFAULT_TEM;
    }
    WriteCAN_AppSignal_Car_AmbientTempType(&Ls8AmbientTemp);

}
/*********************************************************************
* 函数名称: CanIL_XCU_Info_10ms_FD_0X0C2_Msg_Analysis
*
* 功能描述: 0C2报文消息解析
*
* 输入参数: can com层对应buff
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/
static void CanIL_XCU_Info_10ms_FD_0X0C2_Msg_Analysis(const uint8 *pLcMsgType)
{
    /*首先将COMbuff指针类型转换为消息类型*/
    const CAN_0xC2_10_Type *pLcTyped = (CAN_0xC2_10_Type*)pLcMsgType; 
    
    static Car_GearType LenuCar_Gear = CAN_CAR_GEAR_NONE;
    static uint8 Lu8P_GearCnt = 0;
    static uint8 Lu8R_GearCnt = 0;
    static uint8 Lu8N_GearCnt = 0;
    static uint8 Lu8D_GearCnt = 0;
    uint8 Lu8DataTemp;


    static uint8 LcAliveCnt = 0;        /*记录上次接收的ALC值，用于与本次接收的ALC判断，初始为0*/
#ifndef DONT_NEED_CAL_CRCANDALC 
    uint8 IndexCounter = pLcTyped->bit.SigGroup_58_RlngCtr;

    uint8 LcCheckSum_ABM1Tem = 0;

    LcCheckSum_ABM1Tem = pLcTyped->bit.SigGroup_58_ChkSm; /*ABM1校验和*/
    
    if(LcCheckSum_ABM1Tem == CanIL_CRCCheckSum_Algorithm(&(pLcTyped->byte[1]),7,CAL_CRC_INIT_VALUE,DataId_0C2,IndexCounter))
    {/*CRC校验通过*/
        if(GstrNodeErrCnt[ID0C2Idx].Checksum_ValidCnt < 3)
        {
            GstrNodeErrCnt[ID0C2Idx].Checksum_ValidCnt++;
        }

        GstrNodeErrCnt[ID0C2Idx].Checksum_InvalidCnt = 0;

        if(CheckRollingCount(LcAliveCnt,IndexCounter) == TRUE)
        {/*ALC校验通过,解析相关信号*/
            if(GstrNodeErrCnt[ID0C2Idx].Counter_ValidCnt < 3)
            {
                GstrNodeErrCnt[ID0C2Idx].Counter_ValidCnt++;
            }
            GstrNodeErrCnt[ID0C2Idx].Counter_InvalidCnt =0;
            LcAliveCnt =  pLcTyped->bit.SigGroup_58_RlngCtr; 
#else 
    if(1)
    {
        if(1)
        {
#endif         
            Lu8DataTemp = pLcTyped->bit.ActuGearShiftPos;
            if(Lu8DataTemp == 0x04)
            {
                if(Lu8P_GearCnt > 9)
                {
                    LenuCar_Gear = CAN_CAR_GEAR_P;
                }
                else
                {
                    Lu8P_GearCnt++;
                }
                Lu8R_GearCnt = 0;
                Lu8N_GearCnt = 0;
                Lu8D_GearCnt = 0;
            }
            else if(Lu8DataTemp == 0x01)
            {
                if(Lu8R_GearCnt > 9)
                {
                    LenuCar_Gear = CAN_CAR_GEAR_R;
                }
                else
                {
                    Lu8R_GearCnt++;
                }
                Lu8P_GearCnt = 0;
                Lu8N_GearCnt = 0;
                Lu8D_GearCnt = 0;
            }
            else if(Lu8DataTemp == 0x02)
            {
                if(Lu8N_GearCnt > 9)
                {
                    LenuCar_Gear = CAN_CAR_GEAR_N;
                }
                else
                {
                    Lu8N_GearCnt++;
                }
                Lu8P_GearCnt = 0;
                Lu8R_GearCnt = 0;
                Lu8D_GearCnt = 0;
            }
            else if(Lu8DataTemp == 0x03)
            {
                if(Lu8D_GearCnt > 9)
                {
                    LenuCar_Gear = CAN_CAR_GEAR_D;
                }
                else
                {
                    Lu8D_GearCnt++;
                }
                Lu8P_GearCnt = 0;
                Lu8R_GearCnt = 0;
                Lu8N_GearCnt = 0;
            }
            else
            {
                Lu8P_GearCnt = 0;
                Lu8R_GearCnt = 0;
                Lu8N_GearCnt = 0;
                Lu8D_GearCnt = 0;
                LenuCar_Gear = CAN_CAR_GEAR_NONE;
            }

            if(pLcTyped->bit.ActuGearShiftPos <=5 )
            {
                if(GstrNodeErrCnt[ID0C2Idx].Signal_ValidCnt < 5)
                {
                    GstrNodeErrCnt[ID0C2Idx].Signal_ValidCnt++;

                }
                GstrNodeErrCnt[ID0C2Idx].Signal_InvalidCnt = 0;
            }
            else
            {
                if(GstrNodeErrCnt[ID0C2Idx].Signal_InvalidCnt< 10)
                {
                    GstrNodeErrCnt[ID0C2Idx].Signal_InvalidCnt++;
                }

                GstrNodeErrCnt[ID0C2Idx].Signal_ValidCnt = 0;
            }
            WriteCAN_AppSignal_Car_Gear(&LenuCar_Gear);
        }
        else
        {
            if(GstrNodeErrCnt[ID0C2Idx].Counter_InvalidCnt < 5)
            {
                GstrNodeErrCnt[ID0C2Idx].Counter_InvalidCnt++;
            }
            else
            {
                Lu8P_GearCnt = 0;
                Lu8R_GearCnt = 0;
                Lu8N_GearCnt = 0;
                Lu8D_GearCnt = 0;
                LenuCar_Gear = CAN_CAR_GEAR_NONE;
                WriteCAN_AppSignal_Car_Gear(&LenuCar_Gear);
            }
            GstrNodeErrCnt[ID0C2Idx].Counter_ValidCnt = 0;
            LcAliveCnt =  pLcTyped->bit.SigGroup_58_RlngCtr; 
        }
            /*无论ALC校验是否通过，都将接收的ALC作为最新值*/
    }
    else
    {
        if(GstrNodeErrCnt[ID0C2Idx].Checksum_InvalidCnt < 5)
        {
            GstrNodeErrCnt[ID0C2Idx].Checksum_InvalidCnt++;
        }
        else
        {
            Lu8P_GearCnt = 0;
            Lu8R_GearCnt = 0;
            Lu8N_GearCnt = 0;
            Lu8D_GearCnt = 0;
            LenuCar_Gear = CAN_CAR_GEAR_NONE;
            WriteCAN_AppSignal_Car_Gear(&LenuCar_Gear);
        }

        GstrNodeErrCnt[ID0C2Idx].Checksum_ValidCnt = 0;
        LcAliveCnt =  pLcTyped->bit.SigGroup_58_RlngCtr;
    }


    /**************************InPutPara*****************************/

}
/*********************************************************************
* 函数名称: CanIL_ADAS_Objs1_100ms_FD_0X388_Msg_Analysis
*
* 功能描述: 388报文消息解析
*
* 输入参数: can com层对应buff
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明:
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/
static void CanIL_ADAS_Objs1_100ms_FD_0X388_Msg_Analysis(const uint8* pLcMsgType)
{
    /*首先将COMbuff指针类型转换为消息类型*/
    const CAN_0x388_100_Type* pLcTyped = (CAN_0x388_100_Type*)pLcMsgType;
    (void)pLcTyped;
    /* 实际未使用，暂时屏蔽 */
#if 0
    OBJInfo_Type  LstrSts;
    /*****************************OBJ1**********************************************************************************/
    LstrSts.OBJ_TYPE = (OBJ_Type)((pLcTyped->bit.Obj00TypeH4Bit << 1u) | pLcTyped->bit.Obj00TypeL1Bit);
    LstrSts.OBJ_P1_X = (sint16)(((pLcTyped->bit.Obj00P1XH8Bit << 1) | pLcTyped->bit.Obj00P1XL1Bit) * 4 - 1000);
    LstrSts.OBJ_P1_Y = (sint16)(((pLcTyped->bit.Obj00P1YH7Bit << 2) | pLcTyped->bit.Obj00P1YL2Bit) * 4 - 600);
    LstrSts.OBJ_P2_X = (sint16)(((pLcTyped->bit.Obj00P2XH6Bit << 3) | pLcTyped->bit.Obj00P2XL3Bit) * 4 - 1000);
    LstrSts.OBJ_P2_Y = (sint16)(((pLcTyped->bit.Obj00P2YH5Bit << 4) | pLcTyped->bit.Obj00P2YL4Bit) * 4 - 600);
    WriteComSignal_OBJInfo(0, &LstrSts);
   /******************************OBJ2*************************************************************************************/
    LstrSts.OBJ_TYPE = (OBJ_Type)((pLcTyped->bit.Obj01TypeH3Bit << 2u)|pLcTyped->bit.Obj01TypeL2Bit );
    LstrSts.OBJ_P1_X = (sint16)(((pLcTyped->bit.Obj01P1XH7Bit << 2) | pLcTyped->bit.Obj01P1XL2Bit) * 4 - 1000);
    LstrSts.OBJ_P1_Y = (sint16)(((pLcTyped->bit.Obj01P1YH6Bit << 3) | pLcTyped->bit.Obj01P1YL3Bit) * 4 - 600);
    LstrSts.OBJ_P2_X = (sint16)(((pLcTyped->bit.Obj01P2XH5Bit << 4) | pLcTyped->bit.Obj01P2XL4Bit) * 4 - 1000);
    LstrSts.OBJ_P2_Y = (sint16)(((pLcTyped->bit.Obj01P2YH4Bit << 5) | pLcTyped->bit.Obj01P2YL5Bit) * 4 - 600);
    WriteComSignal_OBJInfo(1, &LstrSts);
    /******************************OBJ3*************************************************************************************/
    LstrSts.OBJ_TYPE = (OBJ_Type)((pLcTyped->bit.Obj02TypeH2Bit << 3u) | pLcTyped->bit.Obj02TypeL3Bit);
    LstrSts.OBJ_P1_X = (sint16)(((pLcTyped->bit.Obj02P1XH6Bit << 3) | pLcTyped->bit.Obj02P1XL3Bit) * 4 - 1000);
    LstrSts.OBJ_P1_Y = (sint16)(((pLcTyped->bit.Obj02P1YH5Bit << 4) | pLcTyped->bit.Obj02P1YL4Bit) * 4 - 600);
    LstrSts.OBJ_P2_X = (sint16)(((pLcTyped->bit.Obj02P2XH4Bit << 5) | pLcTyped->bit.Obj02P2XL5Bit) * 4 - 1000);
    LstrSts.OBJ_P2_Y = (sint16)(((pLcTyped->bit.Obj02P2YH3Bit << 6) | pLcTyped->bit.Obj02P2YL6Bit) * 4 - 600);
    WriteComSignal_OBJInfo(2, &LstrSts);
    /******************************OBJ4*************************************************************************************/
    LstrSts.OBJ_TYPE = (OBJ_Type)((pLcTyped->bit.Obj03TypeH1Bit << 4u) | pLcTyped->bit.Obj03TypeL4Bit);
    LstrSts.OBJ_P1_X = (sint16)(((pLcTyped->bit.Obj03P1XH5Bit << 4) | pLcTyped->bit.Obj03P1XL4Bit) * 4 - 1000);
    LstrSts.OBJ_P1_Y = (sint16)(((pLcTyped->bit.Obj03P1YH4Bit << 5) | pLcTyped->bit.Obj03P1YL5Bit) * 4 - 600);
    LstrSts.OBJ_P2_X = (sint16)(((pLcTyped->bit.Obj03P2XH3Bit << 6) | pLcTyped->bit.Obj03P2XL6Bit) * 4 - 1000);
    LstrSts.OBJ_P2_Y = (sint16)(((pLcTyped->bit.Obj03P2YH2Bit << 7) | pLcTyped->bit.Obj03P2YL7Bit) * 4 - 600);
    WriteComSignal_OBJInfo(3, &LstrSts);
    /******************************OBJ5*************************************************************************************/
    LstrSts.OBJ_TYPE = (OBJ_Type)(pLcTyped->bit.Obj04Type5Bit);
    LstrSts.OBJ_P1_X = (sint16)(((pLcTyped->bit.Obj04P1XH4Bit << 5) | pLcTyped->bit.Obj04P1XL5Bit) * 4 - 1000);
    LstrSts.OBJ_P1_Y = (sint16)(((pLcTyped->bit.Obj04P1YH3Bit << 6) | pLcTyped->bit.Obj04P1YL6Bit) * 4 - 600);
    LstrSts.OBJ_P2_X = (sint16)(((pLcTyped->bit.Obj04P2XH2Bit << 7) | pLcTyped->bit.Obj04P2XL7Bit) * 4 - 1000);
    LstrSts.OBJ_P2_Y = (sint16)(((pLcTyped->bit.Obj04P2YH1Bit << 8) | pLcTyped->bit.Obj04P2YL8Bit) * 4 - 600);
    WriteComSignal_OBJInfo(4, &LstrSts);
    /******************************OBJ6*************************************************************************************/
    LstrSts.OBJ_TYPE = (OBJ_Type)(pLcTyped->bit.Obj05Type5Bit);
    LstrSts.OBJ_P1_X = (sint16)(((pLcTyped->bit.Obj05P1XH3Bit << 6) | pLcTyped->bit.Obj05P1XL6Bit) * 4 - 1000);
    LstrSts.OBJ_P1_Y = (sint16)(((pLcTyped->bit.Obj05P1YH2Bit << 7) | pLcTyped->bit.Obj05P1YL7Bit) * 4 - 600);
    LstrSts.OBJ_P2_X = (sint16)(((pLcTyped->bit.Obj05P2XH1Bit << 8) | pLcTyped->bit.Obj05P2XL8Bit) * 4 - 1000);
    LstrSts.OBJ_P2_Y = (sint16)(((pLcTyped->bit.Obj05P2YH8Bit << 1) | pLcTyped->bit.Obj05P2YL1Bit) * 4 - 600);
    WriteComSignal_OBJInfo(5, &LstrSts);
    /******************************OBJ7*************************************************************************************/
    LstrSts.OBJ_TYPE = (OBJ_Type)(pLcTyped->bit.Obj06Type5Bit);
    LstrSts.OBJ_P1_X = (sint16)(((pLcTyped->bit.Obj06P1XH2Bit << 7) | pLcTyped->bit.Obj06P1XL7Bit) * 4 - 1000);
    LstrSts.OBJ_P1_Y = (sint16)(((pLcTyped->bit.Obj06P1YH1Bit << 8) | pLcTyped->bit.Obj06P1YL8Bit) * 4 - 600);
    LstrSts.OBJ_P2_X = (sint16)(((pLcTyped->bit.Obj06P2XH8Bit << 1) | pLcTyped->bit.Obj06P2XL1Bit) * 4 - 1000);
    LstrSts.OBJ_P2_Y = (sint16)(((pLcTyped->bit.Obj06P2YH7Bit << 2) | pLcTyped->bit.Obj06P2YL2Bit) * 4 - 600);
    WriteComSignal_OBJInfo(6, &LstrSts);
    /******************************OBJ8*************************************************************************************/
    LstrSts.OBJ_TYPE = (OBJ_Type)(pLcTyped->bit.Obj07Type5Bit);
    LstrSts.OBJ_P1_X = (sint16)(((pLcTyped->bit.Obj07P1XH1Bit << 8) | pLcTyped->bit.Obj07P1XL8Bit) * 4 - 1000);
    LstrSts.OBJ_P1_Y = (sint16)(((pLcTyped->bit.Obj07P1YH8Bit << 1) | pLcTyped->bit.Obj07P1YL1Bit) * 4 - 600);
    LstrSts.OBJ_P2_X = (sint16)(((pLcTyped->bit.Obj07P2XH7Bit << 2) | pLcTyped->bit.Obj07P2XL2Bit) * 4 - 1000);
    LstrSts.OBJ_P2_Y = (sint16)(((pLcTyped->bit.Obj07P2YH6Bit << 3) | pLcTyped->bit.Obj07P2YL3Bit) * 4 - 600);
    WriteComSignal_OBJInfo(7, &LstrSts);
    /******************************OBJ9*************************************************************************************/
    LstrSts.OBJ_TYPE = (OBJ_Type)((pLcTyped->bit.Obj08TypeH4Bit << 1u) | pLcTyped->bit.Obj08TypeL1Bit);
    LstrSts.OBJ_P1_X = (sint16)(((pLcTyped->bit.Obj08P1XH8Bit << 1) | pLcTyped->bit.Obj08P1XL1Bit) * 4 - 1000);
    LstrSts.OBJ_P1_Y = (sint16)(((pLcTyped->bit.Obj08P1YH7Bit << 2) | pLcTyped->bit.Obj08P1YL2Bit) * 4 - 600);
    LstrSts.OBJ_P2_X = (sint16)(((pLcTyped->bit.Obj08P2XH6Bit << 3) | pLcTyped->bit.Obj08P2XL3Bit) * 4 - 1000);
    LstrSts.OBJ_P2_Y = (sint16)(((pLcTyped->bit.Obj08P2YH5Bit << 4) | pLcTyped->bit.Obj08P2YL4Bit) * 4 - 600);
    WriteComSignal_OBJInfo(8, &LstrSts);
    /******************************OBJ10*************************************************************************************/
    LstrSts.OBJ_TYPE = (OBJ_Type)((pLcTyped->bit.Obj09TypeH3Bit << 2u) | pLcTyped->bit.Obj09TypeL2Bit);
    LstrSts.OBJ_P1_X = (sint16)(((pLcTyped->bit.Obj09P1XH7Bit << 2) | pLcTyped->bit.Obj09P1XL2Bit) * 4 - 1000);
    LstrSts.OBJ_P1_Y = (sint16)(((pLcTyped->bit.Obj09P1YH6Bit << 3) | pLcTyped->bit.Obj09P1YL3Bit) * 4 - 600);
    LstrSts.OBJ_P2_X = (sint16)(((pLcTyped->bit.Obj09P2XH5Bit << 4) | pLcTyped->bit.Obj09P2XL4Bit) * 4 - 1000);
    LstrSts.OBJ_P2_Y = (sint16)(((pLcTyped->bit.Obj09P2YH4Bit << 5) | pLcTyped->bit.Obj09P2YL5Bit) * 4 - 600);
    WriteComSignal_OBJInfo(9, &LstrSts);
    /******************************OBJ11*************************************************************************************/
    LstrSts.OBJ_TYPE = (OBJ_Type)((pLcTyped->bit.Obj10TypeH2Bit << 3u) | pLcTyped->bit.Obj10TypeL3Bit);
    LstrSts.OBJ_P1_X = (sint16)(((pLcTyped->bit.Obj10P1XH6Bit << 3) | pLcTyped->bit.Obj10P1XL3Bit) * 4 - 1000);
    LstrSts.OBJ_P1_Y = (sint16)(((pLcTyped->bit.Obj10P1YH5Bit << 4) | pLcTyped->bit.Obj10P1YL4Bit) * 4 - 600);
    LstrSts.OBJ_P2_X = (sint16)(((pLcTyped->bit.Obj10P2XH4Bit << 5) | pLcTyped->bit.Obj10P2XL5Bit) * 4 - 1000);
    LstrSts.OBJ_P2_Y = (sint16)(((pLcTyped->bit.Obj10P2YH3Bit << 6) | pLcTyped->bit.Obj10P2YL6Bit) * 4 - 600);
    WriteComSignal_OBJInfo(10, &LstrSts);
    /******************************OBJ12*************************************************************************************/
    LstrSts.OBJ_TYPE = (OBJ_Type)((pLcTyped->bit.Obj11TypeH1Bit << 4u) | pLcTyped->bit.Obj11TypeL4Bit);
    LstrSts.OBJ_P1_X = (sint16)(((pLcTyped->bit.Obj11P1XH5Bit << 4) | pLcTyped->bit.Obj11P1XL4Bit) * 4 - 1000);
    LstrSts.OBJ_P1_Y = (sint16)(((pLcTyped->bit.Obj11P1YH4Bit << 5) | pLcTyped->bit.Obj11P1YL5Bit) * 4 - 600);
    LstrSts.OBJ_P2_X = (sint16)(((pLcTyped->bit.Obj11P2XH3Bit << 6) | pLcTyped->bit.Obj11P2XL6Bit) * 4 - 1000);
    LstrSts.OBJ_P2_Y = (sint16)(((pLcTyped->bit.Obj11P2YH2Bit << 7) | pLcTyped->bit.Obj11P2YL7Bit) * 4 - 600);
    WriteComSignal_OBJInfo(11, &LstrSts);
#endif
}

/*********************************************************************/
/*********************************************************************/
/*********************************************************************/
/*********************************************************************/
/*********************************************************************/
/************************       发送函数          ************************/
/*********************************************************************/
/*********************************************************************/
/*********************************************************************/
/*********************************************************************/
/*********************************************************************/
/*********************************************************************/

CAN_0x665_1000_Type LstrTX_APS_0x665_1000_MSG; 
static void CanIL_Tx_PAS_DTC_665_Msg(const uint8 *pBuff)
{
    uint8 i;
    (void)pBuff;/*消除警告*/   
    uint8 Lu8SendDTC_CntMod;
    uint8 Lu8SendDTC_Cntremainder; 
    /*信号赋值*/
    Lu8SendDTC_CntMod =  (Gu8SendDTC_Cnt / 4);
    Lu8SendDTC_Cntremainder =  (Gu8SendDTC_Cnt % 4);

    for(i=0;i<8;i++)
    {
        LstrTX_APS_0x665_1000_MSG.byte[i]=0;
    }
    
    if(Lu8SendDTC_CntMod > 0)
    {
        LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC1_Num_H8BIT = Gu8SendDTC_Buffer[0]>>2 ;
        LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC1_State = 1 ;   
        LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC1_Num_L2BIT = Gu8SendDTC_Buffer[0] ;
        
        LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC2_Num_H8BIT = Gu8SendDTC_Buffer[1]>>2 ;
        LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC2_State = 1 ;
        LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC2_Num_L2BIT = Gu8SendDTC_Buffer[1] ;
        
        LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC3_Num_H8BIT = Gu8SendDTC_Buffer[2]>>2 ;
        LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC3_State = 1 ;
        LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC3_Num_L2BIT = Gu8SendDTC_Buffer[2] ;
        
        LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC4_Num_H8BIT = Gu8SendDTC_Buffer[3]>>2 ;
        LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC4_State = 1 ;
        LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC4_Num_L2BIT = Gu8SendDTC_Buffer[3] ;
        Gu8SendDTC_Cnt -= 4;
        for(i=0;i<Gu8SendDTC_Cnt;i++)
        {
            Gu8SendDTC_Buffer[i]=Gu8SendDTC_Buffer[i+4];
        }
        if(0 == Gu8SendDTC_Cnt)
        {
            /*If the number of DTC recorded is a multiple of 4 and and all them have been sent, set the completed falg*/
            Gu8DTC_send_complete_flag  =1;
        }
    }
    else 
    {
        if(Lu8SendDTC_Cntremainder ==0)
        {
            for(i=0;i<8;i++)
            {
                LstrTX_APS_0x665_1000_MSG.byte[i] = 0;
            }
        }
        else if(Lu8SendDTC_Cntremainder ==1)
        {
            LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC1_Num_H8BIT = Gu8SendDTC_Buffer[0]>>2 ;
            LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC1_State = 1 ;   
            LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC1_Num_L2BIT = Gu8SendDTC_Buffer[0] ;
        }
        else if(Lu8SendDTC_Cntremainder ==2)
        {
            LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC1_Num_H8BIT = Gu8SendDTC_Buffer[0]>>2 ;
            LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC1_State = 1 ;   
            LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC1_Num_L2BIT = Gu8SendDTC_Buffer[0] ;
            
            LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC2_Num_H8BIT = Gu8SendDTC_Buffer[1]>>2 ;
            LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC2_State = 1 ;
            LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC2_Num_L2BIT = Gu8SendDTC_Buffer[1] ;
        }
        else //if(Lu8SendDTC_Cntremainder ==3)
        {
            LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC1_Num_H8BIT = Gu8SendDTC_Buffer[0]>>2 ;
            LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC1_State = 1 ;   
            LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC1_Num_L2BIT = Gu8SendDTC_Buffer[0] ;
            
            LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC2_Num_H8BIT = Gu8SendDTC_Buffer[1]>>2 ;
            LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC2_State = 1 ;
            LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC2_Num_L2BIT = Gu8SendDTC_Buffer[1] ;
            
            LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC3_Num_H8BIT = Gu8SendDTC_Buffer[2]>>2 ;
            LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC3_State = 1 ;
            LstrTX_APS_0x665_1000_MSG.bit.PAS_DTC3_Num_L2BIT = Gu8SendDTC_Buffer[2] ;
        }
        Gu8SendDTC_Cnt=0;
        Gu8DTC_send_complete_flag  =1;
    }

    /* 将填充数据赋值给COM层buff*/  
    for(i=0;i< 8;i++) 
    {
       GcComDataBuffer[ID665Idx][i] = LstrTX_APS_0x665_1000_MSG.byte[i];
    }
}
CAN_0x2F4_20_Type LstrTX_APS_0x2F4_20_MSG; 
static void CanIL_Tx_PAS_Info1_20ms_2F4_Msg(const uint8 *pBuff)
{/*52*/
    uint8 i;
    (void)pBuff;/*消除警告*/  
    //static uint8 LcALC = 0;
    uint8 Lu8CRCLen = 32-1;/*报文长度减1*/
    uint8 Lu8CRCResult = 0;
    /*信号赋值*/
    LstrTX_APS_0x2F4_20_MSG.bit.SigGroup_125_RlngCtr = Gu8NodeALC[ID2F4Idx];

    /********************************Start Of Signal Assignment********************************/
    /****************************************************************************************/
    /****************************************************************************************/
    uint8 LMapObjHeightProb=0;
    uint8 LMapObjHeight=0;
    uint8 LMapObjType=0;
    uint8 LMapObjProb=0;
    uint16 LMapObjP1X=0;
    uint16 LMapObjP1Y=0;
    uint16 LMapObjP2X=0;
    uint16 LMapObjP2Y=0;
    bool bMapObjLockFlg=FALSE;

    PdcSignal_MapObjInfoType LstrMapObjInfo;
    PdcSignal_ReadMapObj(&LstrMapObjInfo,0);
    LMapObjHeightProb = LstrMapObjInfo.u8PDCSignal_MapObjHeightProb;
    LMapObjHeight = LstrMapObjInfo.u8PDCSignal_MapObjHeight;
    LMapObjType = LstrMapObjInfo.u8PDCSignal_MapObjType;
    LMapObjProb = LstrMapObjInfo.u8PDCSignal_MapObjProb;
    
    LMapObjP1X = LstrMapObjInfo.s16PDCSignal_MapObjP1X + MAPOBJECT_OFFSET;
    LMapObjP1X = LMapObjP1X >> 1;
    
    LMapObjP1Y = LstrMapObjInfo.s16PDCSignal_MapObjP1Y + MAPOBJECT_OFFSET;
    LMapObjP1Y =LMapObjP1Y >> 1;
    
    LMapObjP2X = LstrMapObjInfo.s16PDCSignal_MapObjP2X + MAPOBJECT_OFFSET;
    LMapObjP2X  = LMapObjP2X >> 1;
    
    LMapObjP2Y = LstrMapObjInfo.s16PDCSignal_MapObjP2Y + MAPOBJECT_OFFSET;
    LMapObjP2Y = LMapObjP2Y >> 1;
    
    bMapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(bMapObjLockFlg==FALSE)
    {
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj00Height     = (LMapObjHeight &0x03);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj00HeightProb = (LMapObjHeightProb&0x07);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj00P1XH2BIT = ((uint8)(LMapObjP1X>>8u) & 0x03);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj00P1XL8BIT = (uint8)(LMapObjP1X & 0xff);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj00P1YH2BIT = ((uint8)(LMapObjP1Y>>8) &0x03);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj00P1YL8BIT = (uint8)(LMapObjP1Y & 0xff);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj00P2XH8BIT = ((uint8)(LMapObjP2X>>2u) &0xff);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj00P2XL2BIT = (uint8)((LMapObjP2X) &0x03);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj00P2YH2BIT = ((uint8)(LMapObjP2Y>>8u) & 0x03);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj00P2YL8BIT = (uint8)((LMapObjP2Y) &0xff);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj00Prob =  ((LMapObjProb)&0X07);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj00Type =  ((LMapObjType)&0X07);
    }
    
    PdcSignal_ReadMapObj(&LstrMapObjInfo,1);
    LMapObjHeightProb = LstrMapObjInfo.u8PDCSignal_MapObjHeightProb;
    LMapObjHeight = LstrMapObjInfo.u8PDCSignal_MapObjHeight;
    LMapObjType = LstrMapObjInfo.u8PDCSignal_MapObjType;
    LMapObjProb = LstrMapObjInfo.u8PDCSignal_MapObjProb;
    
    LMapObjP1X = LstrMapObjInfo.s16PDCSignal_MapObjP1X + MAPOBJECT_OFFSET;
    LMapObjP1X = LMapObjP1X >> 1;
    
    LMapObjP1Y = LstrMapObjInfo.s16PDCSignal_MapObjP1Y + MAPOBJECT_OFFSET;
    LMapObjP1Y = LMapObjP1Y >> 1;
    
    LMapObjP2X = LstrMapObjInfo.s16PDCSignal_MapObjP2X + MAPOBJECT_OFFSET;
    LMapObjP2X = LMapObjP2X >> 1;
    
    LMapObjP2Y = LstrMapObjInfo.s16PDCSignal_MapObjP2Y + MAPOBJECT_OFFSET;
    LMapObjP2Y = LMapObjP2Y >> 1;

    bMapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(bMapObjLockFlg==FALSE)
    {
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj01Height     = (LMapObjHeight &0x03);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj01HeightProb = (LMapObjHeightProb&0x07);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj01P1XH2BIT = ((uint8)(LMapObjP1X>>8u) & 0x03);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj01P1XL8BIT = (uint8)(LMapObjP1X & 0xff);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj01P1YH8BIT = ((uint8)(LMapObjP1Y>>2u) &0xff);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj01P1YL2BIT = (uint8)(LMapObjP1Y & 0x03);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj01P2XH2BIT = ((uint8)(LMapObjP2X>>8u) &0x03);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj01P2XL8BIT = (uint8)((LMapObjP2X) &0xff);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj01P2YH8BIT = ((uint8)(LMapObjP2Y>>2u) & 0xff);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj01P2YL2BIT = (uint8)((LMapObjP2Y) &0x03);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj01Prob =  ((LMapObjProb)&0X07);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj01Type =  ((LMapObjType)&0X07);
    }


    PdcSignal_ReadMapObj(&LstrMapObjInfo,2);
    LMapObjHeightProb = LstrMapObjInfo.u8PDCSignal_MapObjHeightProb;
    LMapObjHeight = LstrMapObjInfo.u8PDCSignal_MapObjHeight;
    LMapObjType = LstrMapObjInfo.u8PDCSignal_MapObjType;
    LMapObjProb = LstrMapObjInfo.u8PDCSignal_MapObjProb;
    
    LMapObjP1X = LstrMapObjInfo.s16PDCSignal_MapObjP1X + MAPOBJECT_OFFSET;
    LMapObjP1X = LMapObjP1X >> 1;
    
    LMapObjP1Y = LstrMapObjInfo.s16PDCSignal_MapObjP1Y + MAPOBJECT_OFFSET;
    LMapObjP1Y = LMapObjP1Y >> 1;
    
    LMapObjP2X = LstrMapObjInfo.s16PDCSignal_MapObjP2X + MAPOBJECT_OFFSET;
    LMapObjP2X = LMapObjP2X >> 1;
    
    LMapObjP2Y = LstrMapObjInfo.s16PDCSignal_MapObjP2Y + MAPOBJECT_OFFSET;
    LMapObjP2Y = LMapObjP2Y >> 1;

    bMapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(bMapObjLockFlg==FALSE)
    {
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj02Height     = (LMapObjHeight &0x03);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj02HeightProb = (LMapObjHeightProb&0x07);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj02P1XH8BIT = ((uint8)(LMapObjP1X>>2u) & 0xff);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj02P1XL2BIT = (uint8)(LMapObjP1X & 0x03);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj02P1YH2BIT = ((uint8)(LMapObjP1Y>>8) &0x03);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj02P1YL8BIT = (uint8)(LMapObjP1Y & 0xff);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj02P2XH8BIT = ((uint8)(LMapObjP2X>>2u) &0xff);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj02P2XL2BIT = (uint8)((LMapObjP2X) &0x03);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj02P2YH2BIT = ((uint8)(LMapObjP2Y>>8u) & 0x03);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj02P2YL8BIT = (uint8)((LMapObjP2Y) &0xff);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj02Prob =  ((LMapObjProb)&0X07);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj02Type =  ((LMapObjType)&0X07);
    }

    PdcSignal_ReadMapObj(&LstrMapObjInfo,3);
    LMapObjHeightProb = LstrMapObjInfo.u8PDCSignal_MapObjHeightProb;
    LMapObjHeight = LstrMapObjInfo.u8PDCSignal_MapObjHeight;
    LMapObjType = LstrMapObjInfo.u8PDCSignal_MapObjType;
    LMapObjProb = LstrMapObjInfo.u8PDCSignal_MapObjProb;
    
    LMapObjP1X = LstrMapObjInfo.s16PDCSignal_MapObjP1X + MAPOBJECT_OFFSET;
    LMapObjP1X = LMapObjP1X >> 1;
    LMapObjP1Y = LstrMapObjInfo.s16PDCSignal_MapObjP1Y + MAPOBJECT_OFFSET;
    LMapObjP1Y = LMapObjP1Y >> 1;
    LMapObjP2X = LstrMapObjInfo.s16PDCSignal_MapObjP2X + MAPOBJECT_OFFSET;
    LMapObjP2X = LMapObjP2X >> 1;
    LMapObjP2Y = LstrMapObjInfo.s16PDCSignal_MapObjP2Y + MAPOBJECT_OFFSET;
    LMapObjP2Y = LMapObjP2Y >> 1;
    bMapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(bMapObjLockFlg==FALSE)
    {
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj03Height     = (LMapObjHeight &0x03);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj03HeightProb = (LMapObjHeightProb&0x07);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj03P1XH2BIT = ((uint8)(LMapObjP1X>>8u) & 0x03);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj03P1XL8BIT = (uint8)(LMapObjP1X & 0xff);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj03P1YH8BIT = ((uint8)(LMapObjP1Y>>2u) &0xff);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj03P1YL2BIT = (uint8)(LMapObjP1Y & 0x03);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj03P2XH2BIT = ((uint8)(LMapObjP2X>>8u) &0x03);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj03P2XL8BIT = (uint8)((LMapObjP2X) &0xff);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj03P2YH8BIT = ((uint8)(LMapObjP2Y>>2u) & 0xff);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj03P2YL2BIT = (uint8)((LMapObjP2Y) &0x03);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj03Prob =  ((LMapObjProb)&0X07);
        LstrTX_APS_0x2F4_20_MSG.bit.MapObj03Type =  ((LMapObjType)&0X07);
    }
    /********************************End Of Signal Assignment********************************/
    /****************************************************************************************/
    /****************************************************************************************/

    Lu8CRCResult  = CanIL_CRCCheckSum_Algorithm(&(LstrTX_APS_0x2F4_20_MSG.byte[1]),Lu8CRCLen,CAL_CRC_INIT_VALUE,DataId_2F4,Gu8NodeALC[ID2F4Idx]);
    LstrTX_APS_0x2F4_20_MSG.bit.SigGroup_125_ChkSm = Lu8CRCResult;

    /* 将填充数据赋值给COM层buff*/  
    for(i=0;i < 32;i++) 
    {
       GcComDataBuffer[ID2F4Idx][i] = LstrTX_APS_0x2F4_20_MSG.byte[i];
    }
    Gu8NodeALC[ID2F4Idx]++;
    /*ALC大于14，清零*/
    if(Gu8NodeALC[ID2F4Idx] > 0x0F)    Gu8NodeALC[ID2F4Idx] = 0; 
}
CAN_0x2E4_20_Type LstrTX_APS_0x2E4_20_MSG; 
static void CanIL_Tx_PAS_Info3_20ms_2E4_Msg(const uint8 *pBuff)
{
    uint8 i;
    (void)pBuff;/*消除警告*/
    //static uint8 LcALC =0;
    uint8 Lu8CRCLen = 32-1;/*报文长度减1*/
    uint8 Lu8CRCResult = 0;
    /*信号赋值*/
    
    LstrTX_APS_0x2E4_20_MSG.bit. SigGroup_126_RlngCtr = Gu8NodeALC[ID2E4Idx] ;

    /********************************Start Of Signal Assignment********************************/
    /****************************************************************************************/
    /****************************************************************************************/

    uint8 LMapObjHeightProb=0;
    uint8 LMapObjHeight=0;
    uint8 LMapObjType=0;
    uint8 LMapObjProb=0;
    uint16 LMapObjP1X=0;
    uint16 LMapObjP1Y=0;
    uint16 LMapObjP2X=0;
    uint16 LMapObjP2Y=0;
    bool bMapObjLockFlg=FALSE;
    
    PdcSignal_MapObjInfoType LstrMapObjInfo;
    PdcSignal_ReadMapObj(&LstrMapObjInfo,4);
    LMapObjHeightProb = LstrMapObjInfo.u8PDCSignal_MapObjHeightProb;
    LMapObjHeight = LstrMapObjInfo.u8PDCSignal_MapObjHeight;
    LMapObjType = LstrMapObjInfo.u8PDCSignal_MapObjType;
    LMapObjProb = LstrMapObjInfo.u8PDCSignal_MapObjProb;
    
    LMapObjP1X = LstrMapObjInfo.s16PDCSignal_MapObjP1X + MAPOBJECT_OFFSET;
    LMapObjP1X = LMapObjP1X >> 1;
    LMapObjP1Y = LstrMapObjInfo.s16PDCSignal_MapObjP1Y + MAPOBJECT_OFFSET;
    LMapObjP1Y = LMapObjP1Y >> 1;
    LMapObjP2X = LstrMapObjInfo.s16PDCSignal_MapObjP2X + MAPOBJECT_OFFSET;
    LMapObjP2X = LMapObjP2X >> 1;
    LMapObjP2Y = LstrMapObjInfo.s16PDCSignal_MapObjP2Y + MAPOBJECT_OFFSET;
    LMapObjP2Y = LMapObjP2Y >> 1;
    bMapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(bMapObjLockFlg==FALSE)
    {
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj04Height     = (LMapObjHeight &0x03);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj04HeightProb = (LMapObjHeightProb&0x07);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj04P1XH2BIT = ((uint8)(LMapObjP1X>>8u) & 0x03);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj04P1XL8BIT = (uint8)(LMapObjP1X & 0xff);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj04P1YH2BIT = ((uint8)(LMapObjP1Y>>8u) &0x03);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj04P1YL8BIT = (uint8)(LMapObjP1Y & 0xff);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj04P2XH8BIT = ((uint8)(LMapObjP2X>>2u) &0xff);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj04P2XL2BIT = (uint8)((LMapObjP2X) &0x03);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj04P2YH2BIT = ((uint8)(LMapObjP2Y>>8u) & 0x03);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj04P2YL8BIT = (uint8)((LMapObjP2Y) &0xff);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj04Prob =  ((LMapObjProb)&0X07);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj04Type =  ((LMapObjType)&0X07);
    }

    PdcSignal_ReadMapObj(&LstrMapObjInfo,5);
    LMapObjHeightProb = LstrMapObjInfo.u8PDCSignal_MapObjHeightProb;
    LMapObjHeight = LstrMapObjInfo.u8PDCSignal_MapObjHeight;
    LMapObjType = LstrMapObjInfo.u8PDCSignal_MapObjType;
    LMapObjProb = LstrMapObjInfo.u8PDCSignal_MapObjProb;

    LMapObjP1X = LstrMapObjInfo.s16PDCSignal_MapObjP1X + MAPOBJECT_OFFSET;
    LMapObjP1X = LMapObjP1X >> 1;
    LMapObjP1Y = LstrMapObjInfo.s16PDCSignal_MapObjP1Y + MAPOBJECT_OFFSET;
    LMapObjP1Y = LMapObjP1Y >> 1;
    LMapObjP2X = LstrMapObjInfo.s16PDCSignal_MapObjP2X + MAPOBJECT_OFFSET;
    LMapObjP2X = LMapObjP2X >> 1;
    LMapObjP2Y = LstrMapObjInfo.s16PDCSignal_MapObjP2Y + MAPOBJECT_OFFSET;
    LMapObjP2Y = LMapObjP2Y >> 1;
    bMapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(bMapObjLockFlg==FALSE)
    {
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj05Height	   = (LMapObjHeight &0x03);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj05HeightProb = (LMapObjHeightProb&0x07);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj05P1XH2BIT = ((uint8)(LMapObjP1X>>8u) & 0x03);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj05P1XL8BIT = (uint8)(LMapObjP1X & 0xff);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj05P1YH8BIT = ((uint8)(LMapObjP1Y>>2u) &0xff);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj05P1YL2BIT = (uint8)(LMapObjP1Y & 0x03);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj05P2XH2BIT = ((uint8)(LMapObjP2X>>8u) &0x03);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj05P2XL8BIT = (uint8)((LMapObjP2X) &0xff);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj05P2YH8BIT = ((uint8)(LMapObjP2Y>>2u) & 0xff);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj05P2YL2BIT = (uint8)((LMapObjP2Y) &0x03);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj05Prob =	((LMapObjProb)&0X07);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj05Type =	((LMapObjType)&0X07);
   }

    PdcSignal_ReadMapObj(&LstrMapObjInfo,6);
    LMapObjHeightProb = LstrMapObjInfo.u8PDCSignal_MapObjHeightProb;
    LMapObjHeight = LstrMapObjInfo.u8PDCSignal_MapObjHeight;
    LMapObjType = LstrMapObjInfo.u8PDCSignal_MapObjType;
    LMapObjProb = LstrMapObjInfo.u8PDCSignal_MapObjProb;

    LMapObjP1X = LstrMapObjInfo.s16PDCSignal_MapObjP1X + MAPOBJECT_OFFSET;
    LMapObjP1X = LMapObjP1X >> 1;
    LMapObjP1Y = LstrMapObjInfo.s16PDCSignal_MapObjP1Y + MAPOBJECT_OFFSET;
    LMapObjP1Y = LMapObjP1Y >> 1;
    LMapObjP2X = LstrMapObjInfo.s16PDCSignal_MapObjP2X + MAPOBJECT_OFFSET;
    LMapObjP2X = LMapObjP2X >> 1;
    LMapObjP2Y = LstrMapObjInfo.s16PDCSignal_MapObjP2Y + MAPOBJECT_OFFSET;
    LMapObjP2Y = LMapObjP2Y >> 1;


    bMapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(bMapObjLockFlg==FALSE)
    {
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj06Height	   = (LMapObjHeight &0x03);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj06HeightProb = (LMapObjHeightProb&0x07);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj06P1XH8BIT = ((uint8)(LMapObjP1X>>2u) & 0xff);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj06P1XL2BIT = (uint8)(LMapObjP1X & 0x03);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj06P1YH2BIT = ((uint8)(LMapObjP1Y>>8u) &0x03);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj06P1YL8BIT = (uint8)(LMapObjP1Y & 0xff);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj06P2XH8BIT = ((uint8)(LMapObjP2X>>2u) &0xff);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj06P2XL2BIT = (uint8)((LMapObjP2X) &0x03);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj06P2YH2BIT = ((uint8)(LMapObjP2Y>>8u) & 0x03);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj06P2YL8BIT = (uint8)((LMapObjP2Y) &0xff);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj06Prob =	((LMapObjProb)&0X07);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj06Type =	((LMapObjType)&0X07);
    }


    PdcSignal_ReadMapObj(&LstrMapObjInfo,7);
    LMapObjHeightProb = LstrMapObjInfo.u8PDCSignal_MapObjHeightProb;
    LMapObjHeight = LstrMapObjInfo.u8PDCSignal_MapObjHeight;
    LMapObjType = LstrMapObjInfo.u8PDCSignal_MapObjType;
    LMapObjProb = LstrMapObjInfo.u8PDCSignal_MapObjProb;

    LMapObjP1X = LstrMapObjInfo.s16PDCSignal_MapObjP1X + MAPOBJECT_OFFSET;
    LMapObjP1X = LMapObjP1X >> 1;
    LMapObjP1Y = LstrMapObjInfo.s16PDCSignal_MapObjP1Y + MAPOBJECT_OFFSET;
    LMapObjP1Y = LMapObjP1Y >> 1;
    LMapObjP2X = LstrMapObjInfo.s16PDCSignal_MapObjP2X + MAPOBJECT_OFFSET;
    LMapObjP2X = LMapObjP2X >> 1;
    LMapObjP2Y = LstrMapObjInfo.s16PDCSignal_MapObjP2Y + MAPOBJECT_OFFSET;
    LMapObjP2Y = LMapObjP2Y >> 1;

    bMapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(bMapObjLockFlg==FALSE)
    {

        LstrTX_APS_0x2E4_20_MSG.bit.MapObj07Height	   = (LMapObjHeight &0x03);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj07HeightProb = (LMapObjHeightProb&0x07);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj07P1XH2BIT = ((uint8)(LMapObjP1X>>8u) & 0x03);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj07P1XL8BIT = (uint8)(LMapObjP1X & 0xff);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj07P1YH8BIT = ((uint8)(LMapObjP1Y>>2u) &0xff);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj07P1YL2BIT = (uint8)(LMapObjP1Y & 0x03);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj07P2XH2BIT = ((uint8)(LMapObjP2X>>8u) &0x03);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj07P2XL8BIT = (uint8)((LMapObjP2X) &0xff);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj07P2YH8BIT = ((uint8)(LMapObjP2Y>>2u) & 0xff);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj07P2YL2BIT = (uint8)((LMapObjP2Y) &0x03);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj07Prob =	((LMapObjProb)&0X07);
        LstrTX_APS_0x2E4_20_MSG.bit.MapObj07Type =	((LMapObjType)&0X07);
    }
    /********************************End Of Signal Assignment********************************/
    /****************************************************************************************/
    /****************************************************************************************/

    Lu8CRCResult  = CanIL_CRCCheckSum_Algorithm(&(LstrTX_APS_0x2E4_20_MSG.byte[1]),Lu8CRCLen,CAL_CRC_INIT_VALUE,DataId_2E4,Gu8NodeALC[ID2E4Idx]);
    LstrTX_APS_0x2E4_20_MSG.bit.SigGroup_126_ChkSm = Lu8CRCResult;

    /* 将填充数据赋值给COM层buff*/  
    for(i=0;i<32;i++) 
    {
       GcComDataBuffer[ID2E4Idx][i] = LstrTX_APS_0x2E4_20_MSG.byte[i];
    }
    Gu8NodeALC[ID2E4Idx]++;
    /*ALC大于14，清零*/
    if(Gu8NodeALC[ID2E4Idx] > 0x0F)    Gu8NodeALC[ID2E4Idx] = 0; 

}
CAN_0x394_100_Type LstrTX_APS_0x394_100_MSG; 
static void CanIL_Tx_PAS_Info4_100ms_394_Msg(const uint8 *pBuff)
{
    uint8 i;
    (void)pBuff;/*消除警告*/ 
    uint8 Lu8CRCLen = 32-1;/*报文长度减1*/
    uint8 Lu8CRCResult = 0;
    uint16  FrontZone=0;
    uint16	RearZone=0;
    uint16  SideZone=0;
    LstrTX_APS_0x394_100_MSG.bit.PAS_Info4_100ms_ChkSm= 0;
    LstrTX_APS_0x394_100_MSG.bit.PAS_Info4_100ms_RlngCtr= Gu8NodeALC[ID394Idx] ;    
    /********************************Start Of Signal Assignment********************************/
    /****************************************************************************************/
    /****************************************************************************************/

    PdcSignal_PDCWorkStatusType LenuPDCWorkStatus;
    ApaSignal_PSLWorkStatusType LenuPSLWorkStatus;
    PDCSignal_ZoneDisType 	  LstrZoneDis;

    PdcSignal_TrailerHitchDetectedType  TrailerHitchDetected;
    PdcSignal_ReadPDCWorkStatus(&LenuPDCWorkStatus);
    PSLSignal_ReadPSLWorkState(&LenuPSLWorkStatus);
    PdcSignal_ReadZoneDistance(&LstrZoneDis);

    ReadTrailerHitchDetected(&TrailerHitchDetected);
    /*信号赋值*/

    LstrTX_APS_0x394_100_MSG.bit.PAS_TrailerHitchDetected= 0 ;



    LstrTX_APS_0x394_100_MSG.bit.PAS_TrailerHitchDetected =(uint8)TrailerHitchDetected;
    FrontZone=LstrZoneDis.FL1_Distance;
    RearZone =LstrZoneDis.RL1_Distance;
    LstrTX_APS_0x394_100_MSG.bit.FL1_DistanceH8bit = (uint8)(FrontZone>>1);
    LstrTX_APS_0x394_100_MSG.bit.FL1_DistanceL1bit = (uint8)(FrontZone & 0x01);
    LstrTX_APS_0x394_100_MSG.bit.RL1_DistanceH8BIT = (uint8)(RearZone>>1);
    LstrTX_APS_0x394_100_MSG.bit.RL1_DistanceL1BIT = (uint8)(RearZone & 0x01);
    FrontZone=LstrZoneDis.FL2_Distance;
    RearZone =LstrZoneDis.RL2_Distance;
    LstrTX_APS_0x394_100_MSG.bit.FL2_DistanceH7bit = (uint8)((FrontZone>>2)&0x7F);
    LstrTX_APS_0x394_100_MSG.bit.FL2_DistanceL2bit = (uint8)(FrontZone & 0x03);
    LstrTX_APS_0x394_100_MSG.bit.RL2_DistanceH7BIT = (uint8)((RearZone >>2)&0x7F);
    LstrTX_APS_0x394_100_MSG.bit.RL2_DistanceL2BIT = (uint8)(RearZone & 0x03);

    FrontZone=LstrZoneDis.FML1_Distance;
    RearZone =LstrZoneDis.RML1_Distance;
    LstrTX_APS_0x394_100_MSG.bit.FML1_DistanceH4BIT = (uint8)((FrontZone>>5)&0x0F);
    LstrTX_APS_0x394_100_MSG.bit.FML1_DistanceL5BIT = (uint8)(FrontZone&0x1F);
    LstrTX_APS_0x394_100_MSG.bit.RML1_DistanceH4BIT = (uint8)((RearZone>>5) & 0x0F);
    LstrTX_APS_0x394_100_MSG.bit.RML1_DistanceL5BIT = (uint8)(RearZone&0x1F);

    FrontZone=LstrZoneDis.FML2_Distance;
    RearZone =LstrZoneDis.RML2_Distance;
    LstrTX_APS_0x394_100_MSG.bit.FML2_DistanceH3BIT = (uint8)((FrontZone >> 6)&0x07);
    LstrTX_APS_0x394_100_MSG.bit.FML2_DistanceL6BIT = (uint8)(FrontZone & 0x3F);
    LstrTX_APS_0x394_100_MSG.bit.RML2_DistanceH3BIT = (uint8)((RearZone>>6) & 0x07);
    LstrTX_APS_0x394_100_MSG.bit.RML2_DistanceL6BIT = (uint8)(RearZone & 0x3F);

    FrontZone=LstrZoneDis.FMR1_Distance;
    RearZone =LstrZoneDis.RMR1_Distance;
    LstrTX_APS_0x394_100_MSG.bit.FMR1_DistanceH2BIT = (uint8)((FrontZone>>7)&0x03);
    LstrTX_APS_0x394_100_MSG.bit.FMR1_DistanceL7BIT = (uint8)(FrontZone & 0x7F);
    LstrTX_APS_0x394_100_MSG.bit.RMR1_DistanceH2BIT = (uint8)((RearZone>>7)&0x03);
    LstrTX_APS_0x394_100_MSG.bit.RMR1_DistanceL7BIT = (uint8)(RearZone & 0x7F);

    FrontZone=LstrZoneDis.FMR2_Distance;
    RearZone =LstrZoneDis.RMR2_Distance;
    LstrTX_APS_0x394_100_MSG.bit.FMR2_DistanceH1BIT = (uint8)((FrontZone>>8)&0x01);
    LstrTX_APS_0x394_100_MSG.bit.FMR2_DistanceL8BIT = (uint8)FrontZone;
    LstrTX_APS_0x394_100_MSG.bit.RMR2_DistanceH1BIT = (uint8)((RearZone>>8)&0x01);
    LstrTX_APS_0x394_100_MSG.bit.RMR2_DistanceL8BIT = (uint8)RearZone;

    FrontZone=LstrZoneDis.FR1_Distance;
    RearZone =LstrZoneDis.RR1_Distance;
    LstrTX_APS_0x394_100_MSG.bit.FR1_DistanceH6BIT = (uint8)((FrontZone>>3)&0x3F);
    LstrTX_APS_0x394_100_MSG.bit.FR1_DistanceL3BIT = (uint8)(FrontZone & 0x07);
    LstrTX_APS_0x394_100_MSG.bit.RR1_DistanceH6BIT = (uint8)((RearZone>>3)&0x3F);
    LstrTX_APS_0x394_100_MSG.bit.RR1_DistanceL3BIT = (uint8)(RearZone & 0x07);

    FrontZone=LstrZoneDis.FR2_Distance;
    RearZone =LstrZoneDis.RR2_Distance;
    LstrTX_APS_0x394_100_MSG.bit.FR2_DistanceH5BIT = (uint8)((FrontZone>>4)&0x1F);
    LstrTX_APS_0x394_100_MSG.bit.FR2_DistanceL4BIT = (uint8)(FrontZone &0x0F);
    LstrTX_APS_0x394_100_MSG.bit.RR2_DistanceH5BIT = (uint8)((RearZone>>4)&0x1F);
    LstrTX_APS_0x394_100_MSG.bit.RR2_DistanceL4BIT = (uint8)(RearZone &0x0F);

    SideZone=LstrZoneDis.LSF1_Distance;
    LstrTX_APS_0x394_100_MSG.bit.LSF1_DistanceH4BIT = (uint8)((SideZone>>5) & 0x0F);
    LstrTX_APS_0x394_100_MSG.bit.LSF1_DistanceL5BIT = (uint8)(SideZone&0x1F);

    SideZone=LstrZoneDis.LSF2_Distance;
    LstrTX_APS_0x394_100_MSG.bit.LSF2_DistanceH3BIT = (uint8)((SideZone>>6) & 0x07);
    LstrTX_APS_0x394_100_MSG.bit.LSF2_DistanceL6BIT = (uint8)(SideZone & 0x3F);

    SideZone=LstrZoneDis.LSFM1_Distance;
    LstrTX_APS_0x394_100_MSG.bit.LSFM1_DistanceH8BIT = (uint8)(SideZone>>1);
    LstrTX_APS_0x394_100_MSG.bit.LSFM1_DistanceL1BIT = (uint8)(SideZone & 0x01);

    SideZone=LstrZoneDis.LSFM2_Distance;
    LstrTX_APS_0x394_100_MSG.bit.LSFM2_DistanceH7BIT = (uint8)((SideZone >>2) & 0x7F);
    LstrTX_APS_0x394_100_MSG.bit.LSFM2_DistanceL2BIT = (uint8)(SideZone & 0x03);

    SideZone=LstrZoneDis.LSRM1_Distance;
    LstrTX_APS_0x394_100_MSG.bit.LSRM1_DistanceH6BIT = (uint8)((SideZone>>3)&0x3F);
    LstrTX_APS_0x394_100_MSG.bit.LSRM1_DistanceL3BIT = (uint8)(SideZone & 0x07);

    SideZone=LstrZoneDis.LSRM2_Distance;
    LstrTX_APS_0x394_100_MSG.bit.LSRM2_DistanceH5BIT = (uint8)((SideZone>>4)&0x1F);
    LstrTX_APS_0x394_100_MSG.bit.LSRM2_DistanceL4BIT = (uint8)(SideZone &0x0F);

    SideZone=LstrZoneDis.LSR1_Distance;
    LstrTX_APS_0x394_100_MSG.bit.LSR1_DistanceH2BIT = (uint8)((SideZone>>7)&0x03);
    LstrTX_APS_0x394_100_MSG.bit.LSR1_DistanceL7BIT = (uint8)(SideZone & 0x7F);

    SideZone=LstrZoneDis.LSR2_Distance;
    LstrTX_APS_0x394_100_MSG.bit.LSR2_DistanceH1BIT = (uint8)((SideZone>>8)&0x01);
    LstrTX_APS_0x394_100_MSG.bit.LSR2_DistanceL8BIT = (uint8)SideZone;

    SideZone=LstrZoneDis.RSF1_Distance;
    LstrTX_APS_0x394_100_MSG.bit.RSF1_DistanceH8BIT = (uint8)(SideZone>>1);
    LstrTX_APS_0x394_100_MSG.bit.RSF1_DistanceL1BIT = (uint8)(SideZone & 0x01);

    SideZone=LstrZoneDis.RSF2_Distance;
    LstrTX_APS_0x394_100_MSG.bit.RSF2_DistanceH7BIT = (uint8)((SideZone>>2)&0x7F);
    LstrTX_APS_0x394_100_MSG.bit.RSF2_DistanceL2BIT = (uint8)(SideZone & 0x03);

#if 1
    /* 新增：将PAS_Info4_100ms报文中PSL_Status置位到0x5:Reserved，且不影响PSL状态机正常工作 */
    uint8 Lu8CarCoverFlag;
    Lu8CarCoverFlag = GetCarCoverErrorFlag();
    if(Lu8CarCoverFlag)
    {
        LenuPSLWorkStatus = 0x05;
    }

    LstrTX_APS_0x394_100_MSG.bit.PSL_Status= LenuPSLWorkStatus ;
#endif
    LstrTX_APS_0x394_100_MSG.bit.PAS_Status = LenuPDCWorkStatus ;
    /********************************End Of Signal Assignment********************************/
    /****************************************************************************************/
    /****************************************************************************************/

    Lu8CRCResult  = CanIL_CRCCheckSum_Algorithm(&(LstrTX_APS_0x394_100_MSG.byte[1]),Lu8CRCLen,CAL_CRC_INIT_VALUE,DataId_394, Gu8NodeALC[ID394Idx]);
    LstrTX_APS_0x394_100_MSG.bit.PAS_Info4_100ms_ChkSm = Lu8CRCResult;
    /* 将填充数据赋值给COM层buff*/  
    for(i=0;i<32;i++) 
    {
       GcComDataBuffer[ID394Idx][i] = LstrTX_APS_0x394_100_MSG.byte[i];
    }
    Gu8NodeALC[ID394Idx]++;
    /*ALC大于14，清零*/
    if( Gu8NodeALC[ID394Idx] > 0x0F)     Gu8NodeALC[ID394Idx] = 0; 
}
CAN_0x244_20_Type LstrTX_APS_0x244_20_MSG; 
static void CanIL_Tx_PAS_Info4_20ms_244_Msg(const uint8 *pBuff)
{
    uint8 i;
    (void)pBuff;/*消除警告*/    
    /*信号赋值*/
#if 1
    uint8 LMapObjHeightProb=0;
    uint8 LMapObjHeight=0;
    uint8 LMapObjType=0;
    uint8 LMapObjProb=0;
    uint16 LMapObjP1X=0;
    uint16 LMapObjP1Y=0;
    uint16 LMapObjP2X=0;
    uint16 LMapObjP2Y=0;
    bool bMapObjLockFlg=FALSE;
    
    PdcSignal_MapObjInfoType LstrMapObjInfo;
    PdcSignal_ReadMapObj(&LstrMapObjInfo,8);
    LMapObjHeightProb = LstrMapObjInfo.u8PDCSignal_MapObjHeightProb;
    LMapObjHeight = LstrMapObjInfo.u8PDCSignal_MapObjHeight;
    LMapObjType = LstrMapObjInfo.u8PDCSignal_MapObjType;
    LMapObjProb = LstrMapObjInfo.u8PDCSignal_MapObjProb;
    
    LMapObjP1X = LstrMapObjInfo.s16PDCSignal_MapObjP1X + MAPOBJECT_OFFSET;
    LMapObjP1X = LMapObjP1X >> 1;
    LMapObjP1Y = LstrMapObjInfo.s16PDCSignal_MapObjP1Y + MAPOBJECT_OFFSET;
    LMapObjP1Y = LMapObjP1Y >> 1;
    LMapObjP2X = LstrMapObjInfo.s16PDCSignal_MapObjP2X + MAPOBJECT_OFFSET;
    LMapObjP2X = LMapObjP2X >> 1;
    LMapObjP2Y = LstrMapObjInfo.s16PDCSignal_MapObjP2Y + MAPOBJECT_OFFSET;
    LMapObjP2Y = LMapObjP2Y >> 1;


    bMapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(bMapObjLockFlg==FALSE)
    {
        LstrTX_APS_0x244_20_MSG.bit.MapObj08Height     = (LMapObjHeight &0x03);
        LstrTX_APS_0x244_20_MSG.bit.MapObj08HeightProb = (LMapObjHeightProb&0x07);
        LstrTX_APS_0x244_20_MSG.bit.MapObj08P1XH2BIT = ((uint8)(LMapObjP1X>>8u) & 0x03);
        LstrTX_APS_0x244_20_MSG.bit.MapObj08P1XL8BIT = (uint8)(LMapObjP1X & 0xff);
        LstrTX_APS_0x244_20_MSG.bit.MapObj08P1YH2BIT = ((uint8)(LMapObjP1Y>>8) &0x03);
        LstrTX_APS_0x244_20_MSG.bit.MapObj08P1YL8BIT = (uint8)(LMapObjP1Y & 0xff);
        LstrTX_APS_0x244_20_MSG.bit.MapObj08P2XH8BIT = ((uint8)(LMapObjP2X>>2u) &0xff);
        LstrTX_APS_0x244_20_MSG.bit.MapObj08P2XL2BIT = (uint8)((LMapObjP2X) &0x03);
        LstrTX_APS_0x244_20_MSG.bit.MapObj08P2YH2BIT = ((uint8)(LMapObjP2Y>>8u) & 0x03);
        LstrTX_APS_0x244_20_MSG.bit.MapObj08P2YL8BIT = (uint8)((LMapObjP2Y) &0xff);
        LstrTX_APS_0x244_20_MSG.bit.MapObj08Prob =  ((LMapObjProb)&0X07);
        LstrTX_APS_0x244_20_MSG.bit.MapObj08Type =  ((LMapObjType)&0X07);
    }

    PdcSignal_ReadMapObj(&LstrMapObjInfo,9);
    LMapObjHeightProb = LstrMapObjInfo.u8PDCSignal_MapObjHeightProb;
    LMapObjHeight = LstrMapObjInfo.u8PDCSignal_MapObjHeight;
    LMapObjType = LstrMapObjInfo.u8PDCSignal_MapObjType;
    LMapObjProb = LstrMapObjInfo.u8PDCSignal_MapObjProb;
    
    LMapObjP1X = LstrMapObjInfo.s16PDCSignal_MapObjP1X + MAPOBJECT_OFFSET;
    LMapObjP1X = LMapObjP1X >> 1;
    LMapObjP1Y = LstrMapObjInfo.s16PDCSignal_MapObjP1Y + MAPOBJECT_OFFSET;
    LMapObjP1Y = LMapObjP1Y >> 1;
    LMapObjP2X = LstrMapObjInfo.s16PDCSignal_MapObjP2X + MAPOBJECT_OFFSET;
    LMapObjP2X = LMapObjP2X >> 1;
    LMapObjP2Y = LstrMapObjInfo.s16PDCSignal_MapObjP2Y + MAPOBJECT_OFFSET;
    LMapObjP2Y = LMapObjP2Y >> 1;

    bMapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(bMapObjLockFlg==FALSE)
    {
        LstrTX_APS_0x244_20_MSG.bit.MapObj09Height = (LMapObjHeight &0x03);
        LstrTX_APS_0x244_20_MSG.bit.MapObj09HeightProb =(LMapObjHeightProb&0x07);
        LstrTX_APS_0x244_20_MSG.bit.MapObj09P1XH2BIT =((uint8)(LMapObjP1X>>8u) & 0x03);
        LstrTX_APS_0x244_20_MSG.bit.MapObj09P1XL8BIT =(uint8)(LMapObjP1X & 0xff);
        LstrTX_APS_0x244_20_MSG.bit.MapObj09P1YH8BIT = ((uint8)(LMapObjP1Y>>2u) &0xff);
        LstrTX_APS_0x244_20_MSG.bit.MapObj09P1YL2BIT =(uint8)(LMapObjP1Y & 0x03);
        LstrTX_APS_0x244_20_MSG.bit.MapObj09P2XL8BIT =((uint8)(LMapObjP2X) &0xff);
        LstrTX_APS_0x244_20_MSG.bit.MapObj09P2XH2BIT =(uint8)((LMapObjP2X>>8u) &0x03);

        LstrTX_APS_0x244_20_MSG.bit.MapObj09P2YH8BIT =(uint8)((LMapObjP2Y>>2u) &0xff);
        LstrTX_APS_0x244_20_MSG.bit.MapObj09P2YL2BIT =((uint8)(LMapObjP2Y) & 0x03);

        LstrTX_APS_0x244_20_MSG.bit.MapObj09Prob=((LMapObjProb)&0X07);
        LstrTX_APS_0x244_20_MSG.bit.MapObj09Type=((LMapObjType)&0X07);
    }

    PdcSignal_ReadMapObj(&LstrMapObjInfo,10);
    LMapObjHeightProb = LstrMapObjInfo.u8PDCSignal_MapObjHeightProb;
    LMapObjHeight = LstrMapObjInfo.u8PDCSignal_MapObjHeight;
    LMapObjType = LstrMapObjInfo.u8PDCSignal_MapObjType;
    LMapObjProb = LstrMapObjInfo.u8PDCSignal_MapObjProb;
    
    LMapObjP1X = LstrMapObjInfo.s16PDCSignal_MapObjP1X + MAPOBJECT_OFFSET;
    LMapObjP1X = LMapObjP1X >> 1;
    LMapObjP1Y = LstrMapObjInfo.s16PDCSignal_MapObjP1Y + MAPOBJECT_OFFSET;
    LMapObjP1Y = LMapObjP1Y >> 1;
    LMapObjP2X = LstrMapObjInfo.s16PDCSignal_MapObjP2X + MAPOBJECT_OFFSET;
    LMapObjP2X = LMapObjP2X >> 1;
    LMapObjP2Y = LstrMapObjInfo.s16PDCSignal_MapObjP2Y + MAPOBJECT_OFFSET;
    LMapObjP2Y = LMapObjP2Y >> 1;

    bMapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(bMapObjLockFlg==FALSE)
    {
        LstrTX_APS_0x244_20_MSG.bit.MapObj10Height = (LMapObjHeight &0x03);
        LstrTX_APS_0x244_20_MSG.bit.MapObj10HeightProb =(LMapObjHeightProb&0x07);
        LstrTX_APS_0x244_20_MSG.bit.MapObj10P1XH8BIT =((uint8)(LMapObjP1X>>2u) & 0xff);
        LstrTX_APS_0x244_20_MSG.bit.MapObj10P1XL2BIT =(uint8)(LMapObjP1X & 0x03);
        LstrTX_APS_0x244_20_MSG.bit.MapObj10P1YH2BIT = ((uint8)(LMapObjP1Y>>8u) &0x03);
        LstrTX_APS_0x244_20_MSG.bit.MapObj10P1YL8BIT =(uint8)(LMapObjP1Y & 0xff);
        LstrTX_APS_0x244_20_MSG.bit.MapObj10P2XH8BIT =((uint8)(LMapObjP2X>>2u) &0xff);
        LstrTX_APS_0x244_20_MSG.bit.MapObj10P2XL2BIT =(uint8)((LMapObjP2X) &0x03);
        LstrTX_APS_0x244_20_MSG.bit.MapObj10P2YH2BIT =((uint8)(LMapObjP2Y>>8u) & 0x03);
        LstrTX_APS_0x244_20_MSG.bit.MapObj10P2YL8BIT =(uint8)((LMapObjP2Y) &0xff);
        LstrTX_APS_0x244_20_MSG.bit.MapObj10Prob=((LMapObjProb)&0X07);
        LstrTX_APS_0x244_20_MSG.bit.MapObj10Type=((LMapObjType)&0X07);
    }

    PdcSignal_ReadMapObj(&LstrMapObjInfo,11);
    LMapObjHeightProb = LstrMapObjInfo.u8PDCSignal_MapObjHeightProb;
    LMapObjHeight = LstrMapObjInfo.u8PDCSignal_MapObjHeight;
    LMapObjType = LstrMapObjInfo.u8PDCSignal_MapObjType;
    LMapObjProb = LstrMapObjInfo.u8PDCSignal_MapObjProb;
    
    LMapObjP1X = LstrMapObjInfo.s16PDCSignal_MapObjP1X + MAPOBJECT_OFFSET;
    LMapObjP1X = LMapObjP1X >> 1;
    LMapObjP1Y = LstrMapObjInfo.s16PDCSignal_MapObjP1Y + MAPOBJECT_OFFSET;
    LMapObjP1Y = LMapObjP1Y >> 1;
    LMapObjP2X = LstrMapObjInfo.s16PDCSignal_MapObjP2X + MAPOBJECT_OFFSET;
    LMapObjP2X = LMapObjP2X >> 1;
    LMapObjP2Y = LstrMapObjInfo.s16PDCSignal_MapObjP2Y + MAPOBJECT_OFFSET;
    LMapObjP2Y = LMapObjP2Y >> 1;

    bMapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(bMapObjLockFlg==FALSE)
    {
        LstrTX_APS_0x244_20_MSG.bit.MapObj11HeightL2BIT = (LMapObjHeight &0x03);
        LstrTX_APS_0x244_20_MSG.bit.MapObj11HeightProb =(LMapObjHeightProb&0x07);
        LstrTX_APS_0x244_20_MSG.bit.MapObj11P1XL8BIT =((uint8)(LMapObjP1X) & 0xff);
        LstrTX_APS_0x244_20_MSG.bit.MapObj11P1XH2BIT =(uint8)((LMapObjP1X>>8u )& 0x03);
        LstrTX_APS_0x244_20_MSG.bit.MapObj11P1YH8BIT = ((uint8)(LMapObjP1Y>>2u) &0xff);
        LstrTX_APS_0x244_20_MSG.bit.MapObj11P1YL2BIT =(uint8)(LMapObjP1Y & 0x03);
        LstrTX_APS_0x244_20_MSG.bit.MapObj11P2XL8BIT =((uint8)(LMapObjP2X) &0xff);
        LstrTX_APS_0x244_20_MSG.bit.MapObj11P2XH2BIT =(uint8)((LMapObjP2X>>8u) &0x03);

        LstrTX_APS_0x244_20_MSG.bit.MapObj11P2YH8BIT =(uint8)((LMapObjP2Y>>2u) &0xff);
        LstrTX_APS_0x244_20_MSG.bit.MapObj11P2YL2BIT =((uint8)(LMapObjP2Y) & 0x03);

        LstrTX_APS_0x244_20_MSG.bit.MapObj11Prob=((LMapObjProb)&0X07);
        LstrTX_APS_0x244_20_MSG.bit.MapObj11Type=((LMapObjType)&0X07);
    }

#endif

    /* 将填充数据赋值给COM层buff*/  
    for(i=0;i<32;i++) 
    {
       GcComDataBuffer[ID244Idx][i] = LstrTX_APS_0x244_20_MSG.byte[i];
    }
}
CAN_0x85_10_Type LstrTX_APS_0x085_10_MSG; 
static void CanIL_Tx_PAS_Info5_10ms_085_Msg(const uint8 *pBuff)
{
    uint8 i;
    (void)pBuff;/*消除警告*/    
    uint8 Lu8CRCLen = 32-1;/*报文长度减1*/
    uint8 Lu8CRCResult = 0;
    ODOCoorInfo LstrODOInfo;
    uint16 Lu16Data;
    Car_StrAngleType LStrSteerAngle;
    LstrTX_APS_0x085_10_MSG.bit.SigGroup_4_RlngCtr = Gu8NodeALC[ID085Idx];
    /********************************Start Of Signal Assignment********************************/
    /****************************************************************************************/
    /****************************************************************************************/

    /*信号赋值*/
    ReadAPASignal_ODOInfo(&LstrODOInfo);
    Lu16Data = (uint16)((32768u + (sint32)(LstrODOInfo.fODO_X/10))%65536);
    LstrTX_APS_0x085_10_MSG.bit.ODO_XH8BIT = (Lu16Data >> 8u);
    LstrTX_APS_0x085_10_MSG.bit.ODO_XL8BIT = (uint8)(Lu16Data & 0xFFu);

    Lu16Data = (uint16)((32768u + (sint32)(LstrODOInfo.fODO_Y/10))%65536);
    LstrTX_APS_0x085_10_MSG.bit.ODO_YH8BIT = (Lu16Data >> 8u);
    LstrTX_APS_0x085_10_MSG.bit.ODO_YL8BIT = (uint8)(Lu16Data & 0xFFu);

    Lu16Data = (uint16)(12868u + LstrODOInfo.fODO_YawAngle*4096);
    LstrTX_APS_0x085_10_MSG.bit.ODO_YawAngleH8BIT = (Lu16Data >> 8u);
    LstrTX_APS_0x085_10_MSG.bit.ODO_YawAngleL8BIT = (uint8)(Lu16Data & 0xFFu);

    Lu16Data = (uint16)((32768u + (sint32)(LstrODOInfo.fODO_SHA/10))%65536);
    LstrTX_APS_0x085_10_MSG.bit.ODO_sHAH8BIT = (Lu16Data >> 8u);
    LstrTX_APS_0x085_10_MSG.bit.ODO_sHAL8BIT = (uint8)(Lu16Data & 0xFFu);

    Lu16Data = (uint16)(16384000/LstrODOInfo.u32ODO_TurnRadian);
    
    ReadCAN_AppSignal_Car_StrAngle(&LStrSteerAngle);
    if(LStrSteerAngle < 0)
    {
        Lu16Data = 65535u - Lu16Data;
    }
    LstrTX_APS_0x085_10_MSG.bit.ODO_KappaH8BIT = (Lu16Data >> 8u);
    LstrTX_APS_0x085_10_MSG.bit.ODO_KappaL8BIT = (uint8)(Lu16Data & 0xFFu);

    LstrTX_APS_0x085_10_MSG.bit.ODO_CalTimeStampHHH8BIT = (uint8)((LstrODOInfo.u32ODO_CalTimeStamp >> 19u) & 0xFFu);
    LstrTX_APS_0x085_10_MSG.bit.ODO_CalTimeStampHH8BIT = (uint8)((LstrODOInfo.u32ODO_CalTimeStamp >> 11u) & 0xFFu);
    LstrTX_APS_0x085_10_MSG.bit.ODO_CalTimeStampH8BIT = (uint8)((LstrODOInfo.u32ODO_CalTimeStamp >> 3u) & 0xFFu);
    LstrTX_APS_0x085_10_MSG.bit.ODO_CalTimeStampL3BIT = (uint8)(LstrODOInfo.u32ODO_CalTimeStamp & 0x7u);

    /********************************End Of Signal Assignment********************************/
    /****************************************************************************************/
    /****************************************************************************************/
    Lu8CRCResult  = CanIL_CRCCheckSum_Algorithm(&(LstrTX_APS_0x085_10_MSG.byte[1]),Lu8CRCLen,CAL_CRC_INIT_VALUE,DataId_085,Gu8NodeALC[ID085Idx]);
    LstrTX_APS_0x085_10_MSG.bit.SigGroup_4_ChkSm = Lu8CRCResult;

    /* 将填充数据赋值给COM层buff*/  
    for(i=0;i<32;i++) 
    {
       GcComDataBuffer[ID085Idx][i] = LstrTX_APS_0x085_10_MSG.byte[i];
    }
    Gu8NodeALC[ID085Idx]++;
    /*ALC大于14，清零*/
    if(Gu8NodeALC[ID085Idx] > 0x0F)    Gu8NodeALC[ID085Idx] = 0; 
}
CAN_0x245_20_Type LstrTX_APS_0x245_20_MSG; 
static void CanIL_Tx_PAS_Info5_20ms_245_Msg(const uint8 *pBuff)
{
    uint8 i;
    (void)pBuff;/*消除警告*/    
   // static uint8 LcALC =0;
    uint8 Lu8CRCLen = 32-1;/*报文长度减1*/
    uint8 Lu8CRCResult = 0;
    /*信号赋值*/
    LstrTX_APS_0x245_20_MSG.bit.SigGroup_0x128_RlngCtr = Gu8NodeALC[ID245Idx];
    /********************************Start Of Signal Assignment********************************/
    /****************************************************************************************/
    /****************************************************************************************/

    uint8 LMapObjHeightProb=0;
    uint8 LMapObjHeight=0;
    uint8 LMapObjType=0;
    uint8 LMapObjProb=0;
    uint16 LMapObjP1X=0;
    uint16 LMapObjP1Y=0;
    uint16 LMapObjP2X=0;
    uint16 LMapObjP2Y=0;
    bool bMapObjLockFlg=FALSE;
    PdcSignal_MapObjInfoType LstrMapObjInfo;
    PdcSignal_ReadMapObj(&LstrMapObjInfo,12);
    LMapObjHeightProb = LstrMapObjInfo.u8PDCSignal_MapObjHeightProb;
    LMapObjHeight = LstrMapObjInfo.u8PDCSignal_MapObjHeight;
    LMapObjType = LstrMapObjInfo.u8PDCSignal_MapObjType;
    LMapObjProb = LstrMapObjInfo.u8PDCSignal_MapObjProb;
    
    LMapObjP1X = LstrMapObjInfo.s16PDCSignal_MapObjP1X + MAPOBJECT_OFFSET;
    LMapObjP1X = LMapObjP1X >> 1;
    LMapObjP1Y = LstrMapObjInfo.s16PDCSignal_MapObjP1Y + MAPOBJECT_OFFSET;
    LMapObjP1Y = LMapObjP1Y >> 1;
    LMapObjP2X = LstrMapObjInfo.s16PDCSignal_MapObjP2X + MAPOBJECT_OFFSET;
    LMapObjP2X = LMapObjP2X >> 1;
    LMapObjP2Y = LstrMapObjInfo.s16PDCSignal_MapObjP2Y + MAPOBJECT_OFFSET;
    LMapObjP2Y = LMapObjP2Y >> 1;
    bMapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(bMapObjLockFlg==FALSE)
    {
        LstrTX_APS_0x245_20_MSG.bit.MapObj12Height     = (LMapObjHeight &0x03);
        LstrTX_APS_0x245_20_MSG.bit.MapObj12HeightProb = (LMapObjHeightProb&0x07);
        LstrTX_APS_0x245_20_MSG.bit.MapObj12P1XH2BIT = ((uint8)(LMapObjP1X>>8u) & 0x03);
        LstrTX_APS_0x245_20_MSG.bit.MapObj12P1XL8BIT = (uint8)(LMapObjP1X & 0xff);
        LstrTX_APS_0x245_20_MSG.bit.MapObj12P1YH2BIT = ((uint8)(LMapObjP1Y>>8) &0x03);
        LstrTX_APS_0x245_20_MSG.bit.MapObj12P1YL8BIT = (uint8)(LMapObjP1Y & 0xff);
        LstrTX_APS_0x245_20_MSG.bit.MapObj12P2XH8BIT = ((uint8)(LMapObjP2X>>2u) &0xff);
        LstrTX_APS_0x245_20_MSG.bit.MapObj13P2XL2BIT = (uint8)((LMapObjP2X) &0x03);
        LstrTX_APS_0x245_20_MSG.bit.MapObj12P2YH2BIT = ((uint8)(LMapObjP2Y>>8u) & 0x03);
        LstrTX_APS_0x245_20_MSG.bit.MapObj12P2YL8BIT = (uint8)((LMapObjP2Y) &0xff);
        LstrTX_APS_0x245_20_MSG.bit.MapObj12Prob =  ((LMapObjProb)&0X07);
        LstrTX_APS_0x245_20_MSG.bit.MapObj12Type =  ((LMapObjType)&0X07);
    }

    PdcSignal_ReadMapObj(&LstrMapObjInfo,13);
    LMapObjHeightProb = LstrMapObjInfo.u8PDCSignal_MapObjHeightProb;
    LMapObjHeight = LstrMapObjInfo.u8PDCSignal_MapObjHeight;
    LMapObjType = LstrMapObjInfo.u8PDCSignal_MapObjType;
    LMapObjProb = LstrMapObjInfo.u8PDCSignal_MapObjProb;

    LMapObjP1X = LstrMapObjInfo.s16PDCSignal_MapObjP1X + MAPOBJECT_OFFSET;
    LMapObjP1X = LMapObjP1X >> 1;
    LMapObjP1Y = LstrMapObjInfo.s16PDCSignal_MapObjP1Y + MAPOBJECT_OFFSET;
    LMapObjP1Y = LMapObjP1Y >> 1;
    LMapObjP2X = LstrMapObjInfo.s16PDCSignal_MapObjP2X + MAPOBJECT_OFFSET;
    LMapObjP2X = LMapObjP2X >> 1;
    LMapObjP2Y = LstrMapObjInfo.s16PDCSignal_MapObjP2Y + MAPOBJECT_OFFSET;
    LMapObjP2Y = LMapObjP2Y >> 1;

    bMapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(bMapObjLockFlg==FALSE)
    {
        LstrTX_APS_0x245_20_MSG.bit.MapObj13Height     = (LMapObjHeight &0x03);
        LstrTX_APS_0x245_20_MSG.bit.MapObj13HeightProb = (LMapObjHeightProb&0x07);
        LstrTX_APS_0x245_20_MSG.bit.MapObj13P1XH2BIT = ((uint8)(LMapObjP1X>>8u) & 0x03);
        LstrTX_APS_0x245_20_MSG.bit.MapObj13P1XL8BIT = (uint8)(LMapObjP1X & 0xff);
        LstrTX_APS_0x245_20_MSG.bit.MapObj13P1YH8BIT = ((uint8)(LMapObjP1Y>>2u) &0xff);
        LstrTX_APS_0x245_20_MSG.bit.MapObj13P1YL2BIT = (uint8)(LMapObjP1Y & 0x03);
        LstrTX_APS_0x245_20_MSG.bit.MapObj13P2XH2BIT = ((uint8)(LMapObjP2X>>8u) &0x03);
        LstrTX_APS_0x245_20_MSG.bit.MapObj13P2XL8BIT = (uint8)((LMapObjP2X) &0xff);
        LstrTX_APS_0x245_20_MSG.bit.MapObj13P2YH8BIT = ((uint8)(LMapObjP2Y>>2u) & 0xff);
        LstrTX_APS_0x245_20_MSG.bit.MapObj13P2YL2BIT = (uint8)((LMapObjP2Y) &0x03);
        LstrTX_APS_0x245_20_MSG.bit.MapObj13Prob =  ((LMapObjProb)&0X07);
        LstrTX_APS_0x245_20_MSG.bit.MapObj13Type =  ((LMapObjType)&0X07);
    }



    PdcSignal_ReadMapObj(&LstrMapObjInfo,14);
    LMapObjHeightProb = LstrMapObjInfo.u8PDCSignal_MapObjHeightProb;
    LMapObjHeight = LstrMapObjInfo.u8PDCSignal_MapObjHeight;
    LMapObjType = LstrMapObjInfo.u8PDCSignal_MapObjType;
    LMapObjProb = LstrMapObjInfo.u8PDCSignal_MapObjProb;

    LMapObjP1X = LstrMapObjInfo.s16PDCSignal_MapObjP1X + MAPOBJECT_OFFSET;
    LMapObjP1X = LMapObjP1X >> 1;
    LMapObjP1Y = LstrMapObjInfo.s16PDCSignal_MapObjP1Y + MAPOBJECT_OFFSET;
    LMapObjP1Y = LMapObjP1Y >> 1;
    LMapObjP2X = LstrMapObjInfo.s16PDCSignal_MapObjP2X + MAPOBJECT_OFFSET;
    LMapObjP2X = LMapObjP2X >> 1;
    LMapObjP2Y = LstrMapObjInfo.s16PDCSignal_MapObjP2Y + MAPOBJECT_OFFSET;
    LMapObjP2Y = LMapObjP2Y >> 1;


    bMapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(bMapObjLockFlg==FALSE)
    {
        LstrTX_APS_0x245_20_MSG.bit.MapObj14Height	   = (LMapObjHeight &0x03);
        LstrTX_APS_0x245_20_MSG.bit.MapObj14HeightProb = (LMapObjHeightProb&0x07);
        LstrTX_APS_0x245_20_MSG.bit.MapObj14P1XH8BIT = ((uint8)(LMapObjP1X>>2u) & 0xff);
        LstrTX_APS_0x245_20_MSG.bit.MapObj14P1XL2BIT = (uint8)(LMapObjP1X & 0x03);
        LstrTX_APS_0x245_20_MSG.bit.MapObj14P1YH2BIT = ((uint8)(LMapObjP1Y>>8) &0x03);
        LstrTX_APS_0x245_20_MSG.bit.MapObj14P1YL8BIT = (uint8)(LMapObjP1Y & 0xff);
        LstrTX_APS_0x245_20_MSG.bit.MapObj14P2XH8BIT = ((uint8)(LMapObjP2X>>2u) &0xff);
        LstrTX_APS_0x245_20_MSG.bit.MapObj14P2XL2BIT = (uint8)((LMapObjP2X) &0x03);
        LstrTX_APS_0x245_20_MSG.bit.MapObj14P2YH2BIT = ((uint8)(LMapObjP2Y>>8u) & 0x03);
        LstrTX_APS_0x245_20_MSG.bit.MapObj14P2YL8BIT = (uint8)((LMapObjP2Y) &0xff);
        LstrTX_APS_0x245_20_MSG.bit.MapObj14Prob =	((LMapObjProb)&0X07);
        LstrTX_APS_0x245_20_MSG.bit.MapObj14Type =	((LMapObjType)&0X07);
    }


    PdcSignal_ReadMapObj(&LstrMapObjInfo,15);
    LMapObjHeightProb = LstrMapObjInfo.u8PDCSignal_MapObjHeightProb;
    LMapObjHeight = LstrMapObjInfo.u8PDCSignal_MapObjHeight;
    LMapObjType = LstrMapObjInfo.u8PDCSignal_MapObjType;
    LMapObjProb = LstrMapObjInfo.u8PDCSignal_MapObjProb;

    LMapObjP1X = LstrMapObjInfo.s16PDCSignal_MapObjP1X + MAPOBJECT_OFFSET;
    LMapObjP1X = LMapObjP1X >> 1;
    LMapObjP1Y = LstrMapObjInfo.s16PDCSignal_MapObjP1Y + MAPOBJECT_OFFSET;
    LMapObjP1Y = LMapObjP1Y >> 1;
    LMapObjP2X = LstrMapObjInfo.s16PDCSignal_MapObjP2X + MAPOBJECT_OFFSET;
    LMapObjP2X = LMapObjP2X >> 1;
    LMapObjP2Y = LstrMapObjInfo.s16PDCSignal_MapObjP2Y + MAPOBJECT_OFFSET;
    LMapObjP2Y = LMapObjP2Y >> 1;


    bMapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(bMapObjLockFlg==FALSE)
    {
        LstrTX_APS_0x245_20_MSG.bit.MapObj15HeightL2BIT     = (LMapObjHeight &0x03);
        LstrTX_APS_0x245_20_MSG.bit.MapObj15HeightProb = (LMapObjHeightProb&0x07);
        LstrTX_APS_0x245_20_MSG.bit.MapObj15P1XH2BIT = ((uint8)(LMapObjP1X>>8u) & 0x03);
        LstrTX_APS_0x245_20_MSG.bit.MapObj15P1XL8BIT = (uint8)(LMapObjP1X & 0xff);
        LstrTX_APS_0x245_20_MSG.bit.MapObj15P1YH8BIT = ((uint8)(LMapObjP1Y>>2u) &0xff);
        LstrTX_APS_0x245_20_MSG.bit.MapObj15P1YL2BIT = (uint8)(LMapObjP1Y & 0x03);
        LstrTX_APS_0x245_20_MSG.bit.MapObj15P2XH2BIT = ((uint8)(LMapObjP2X>>8u) &0x03);
        LstrTX_APS_0x245_20_MSG.bit.MapObj15P2XL8BIT = (uint8)((LMapObjP2X) &0xff);
        LstrTX_APS_0x245_20_MSG.bit.MapObj15P2YH8BIT = ((uint8)(LMapObjP2Y>>2u) & 0xff);
        LstrTX_APS_0x245_20_MSG.bit.MapObj15P2YL2BIT = (uint8)((LMapObjP2Y) &0x03);
        LstrTX_APS_0x245_20_MSG.bit.MapObj15Prob =  ((LMapObjProb)&0X07);
        LstrTX_APS_0x245_20_MSG.bit.MapObj15Type =  ((LMapObjType)&0X07);
    }

    /********************************End Of Signal Assignment********************************/
    /****************************************************************************************/
    /****************************************************************************************/

    Lu8CRCResult  = CanIL_CRCCheckSum_Algorithm(&(LstrTX_APS_0x245_20_MSG.byte[1]),Lu8CRCLen,CAL_CRC_INIT_VALUE,DataId_245,Gu8NodeALC[ID245Idx]);
    LstrTX_APS_0x245_20_MSG.bit.SigGroup_0x128_ChkSm = Lu8CRCResult;

    /* 将填充数据赋值给COM层buff*/  
    for(i=0;i<32;i++) 
    {
       GcComDataBuffer[ID245Idx][i] = LstrTX_APS_0x245_20_MSG.byte[i];
    }
    Gu8NodeALC[ID245Idx]++;
    /*ALC大于14，清零*/
    if(Gu8NodeALC[ID245Idx] > 0x0F)    Gu8NodeALC[ID245Idx] = 0; 
}
CAN_0x254_20_Type LstrTX_APS_0x254_20_MSG; 
static void CanIL_Tx_PAS_Info7_20ms_254_Msg(const uint8 *pBuff)
{
    uint8 i;
    (void)pBuff;/*消除警告*/    
    uint8 Lu8CRCLen = 32-1;/*报文长度减1*/
    uint8 Lu8CRCResult = 0;
    /*信号赋值*/
    LstrTX_APS_0x254_20_MSG.bit.SigGroup_0x128_RlngCtr = Gu8NodeALC[ID254Idx];
    /********************************Start Of Signal Assignment********************************/
    /****************************************************************************************/
    /****************************************************************************************/

    uint8 LMapObjHeightProb=0;
    uint8 LMapObjHeight=0;
    uint8 LMapObjType=0;
    uint8 LMapObjProb=0;
    uint16 LMapObjP1X=0;
    uint16 LMapObjP1Y=0;
    uint16 LMapObjP2X=0;
    uint16 LMapObjP2Y=0;
    bool bMapObjLockFlg=FALSE;
    PdcSignal_MapObjInfoType LstrMapObjInfo;
    PdcSignal_ReadMapObj(&LstrMapObjInfo,16);
    LMapObjHeightProb = LstrMapObjInfo.u8PDCSignal_MapObjHeightProb;
    LMapObjHeight = LstrMapObjInfo.u8PDCSignal_MapObjHeight;
    LMapObjType = LstrMapObjInfo.u8PDCSignal_MapObjType;
    LMapObjProb = LstrMapObjInfo.u8PDCSignal_MapObjProb;
        
    LMapObjP1X = LstrMapObjInfo.s16PDCSignal_MapObjP1X + MAPOBJECT_OFFSET;
    LMapObjP1X = LMapObjP1X >> 1;
    LMapObjP1Y = LstrMapObjInfo.s16PDCSignal_MapObjP1Y + MAPOBJECT_OFFSET;
    LMapObjP1Y = LMapObjP1Y >> 1;
    LMapObjP2X = LstrMapObjInfo.s16PDCSignal_MapObjP2X + MAPOBJECT_OFFSET;
    LMapObjP2X = LMapObjP2X >> 1;
    LMapObjP2Y = LstrMapObjInfo.s16PDCSignal_MapObjP2Y + MAPOBJECT_OFFSET;
    LMapObjP2Y = LMapObjP2Y >> 1;


    bMapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(bMapObjLockFlg==FALSE)
    {
        LstrTX_APS_0x254_20_MSG.bit.MapObj16Height	   = (LMapObjHeight &0x03);
        LstrTX_APS_0x254_20_MSG.bit.MapObj16HeightProb = (LMapObjHeightProb&0x07);
        LstrTX_APS_0x254_20_MSG.bit.MapObj16P1XH2BIT = ((uint8)(LMapObjP1X>>8u) & 0x03);
        LstrTX_APS_0x254_20_MSG.bit.MapObj16P1XL8BIT = (uint8)(LMapObjP1X & 0xff);
        LstrTX_APS_0x254_20_MSG.bit.MapObj16P1YH2BIT = ((uint8)(LMapObjP1Y>>8) &0x03);
        LstrTX_APS_0x254_20_MSG.bit.MapObj16P1YL8BIT = (uint8)(LMapObjP1Y & 0xff);
        LstrTX_APS_0x254_20_MSG.bit.MapObj16P2XH8BIT = ((uint8)(LMapObjP2X>>2u) &0xff);
        LstrTX_APS_0x254_20_MSG.bit.MapObj16P2XL2BIT = (uint8)((LMapObjP2X) &0x03);
        LstrTX_APS_0x254_20_MSG.bit.MapObj16P2YH2BIT = ((uint8)(LMapObjP2Y>>8u) & 0x03);
        LstrTX_APS_0x254_20_MSG.bit.MapObj16P2YL8BIT = (uint8)((LMapObjP2Y) &0xff);
        LstrTX_APS_0x254_20_MSG.bit.MapObj16Prob =  ((LMapObjProb)&0X07);
        LstrTX_APS_0x254_20_MSG.bit.MapObj16Type =  ((LMapObjType)&0X07);
    }

    PdcSignal_ReadMapObj(&LstrMapObjInfo,17);
    LMapObjHeightProb = LstrMapObjInfo.u8PDCSignal_MapObjHeightProb;
    LMapObjHeight = LstrMapObjInfo.u8PDCSignal_MapObjHeight;
    LMapObjType = LstrMapObjInfo.u8PDCSignal_MapObjType;
    LMapObjProb = LstrMapObjInfo.u8PDCSignal_MapObjProb;

    LMapObjP1X = LstrMapObjInfo.s16PDCSignal_MapObjP1X + MAPOBJECT_OFFSET;
    LMapObjP1X = LMapObjP1X >> 1;
    LMapObjP1Y = LstrMapObjInfo.s16PDCSignal_MapObjP1Y + MAPOBJECT_OFFSET;
    LMapObjP1Y = LMapObjP1Y >> 1;
    LMapObjP2X = LstrMapObjInfo.s16PDCSignal_MapObjP2X + MAPOBJECT_OFFSET;
    LMapObjP2X = LMapObjP2X >> 1;
    LMapObjP2Y = LstrMapObjInfo.s16PDCSignal_MapObjP2Y + MAPOBJECT_OFFSET;
    LMapObjP2Y = LMapObjP2Y >> 1;


    bMapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(bMapObjLockFlg==FALSE)
    {
        LstrTX_APS_0x254_20_MSG.bit.MapObj17Height	   = (LMapObjHeight &0x03);
        LstrTX_APS_0x254_20_MSG.bit.MapObj17HeightProb = (LMapObjHeightProb&0x07);
        LstrTX_APS_0x254_20_MSG.bit.MapObj17P1XH2BIT = ((uint8)(LMapObjP1X>>8u) & 0x03);
        LstrTX_APS_0x254_20_MSG.bit.MapObj17P1XL8BIT = (uint8)(LMapObjP1X & 0xff);
        LstrTX_APS_0x254_20_MSG.bit.MapObj17P1YH8BIT = ((uint8)(LMapObjP1Y>>2u) &0xff);
        LstrTX_APS_0x254_20_MSG.bit.MapObj17P1YL2BIT = (uint8)(LMapObjP1Y & 0x03);
        LstrTX_APS_0x254_20_MSG.bit.MapObj17P2XH2BIT = ((uint8)(LMapObjP2X>>8u) &0x03);
        LstrTX_APS_0x254_20_MSG.bit.MapObj17P2XL8BIT = (uint8)((LMapObjP2X) &0xff);
        LstrTX_APS_0x254_20_MSG.bit.MapObj17P2YH8BIT = ((uint8)(LMapObjP2Y>>2u) & 0xff);
        LstrTX_APS_0x254_20_MSG.bit.MapObj17P2YL2BIT = (uint8)((LMapObjP2Y) &0x03);
        LstrTX_APS_0x254_20_MSG.bit.MapObj17Prob =  ((LMapObjProb)&0X07);
        LstrTX_APS_0x254_20_MSG.bit.MapObj17Type =  ((LMapObjType)&0X07);
    }

    PdcSignal_ReadMapObj(&LstrMapObjInfo,18);
    LMapObjHeightProb = LstrMapObjInfo.u8PDCSignal_MapObjHeightProb;
    LMapObjHeight = LstrMapObjInfo.u8PDCSignal_MapObjHeight;
    LMapObjType = LstrMapObjInfo.u8PDCSignal_MapObjType;
    LMapObjProb = LstrMapObjInfo.u8PDCSignal_MapObjProb;

    LMapObjP1X = LstrMapObjInfo.s16PDCSignal_MapObjP1X + MAPOBJECT_OFFSET;
    LMapObjP1X = LMapObjP1X >> 1;
    LMapObjP1Y = LstrMapObjInfo.s16PDCSignal_MapObjP1Y + MAPOBJECT_OFFSET;
    LMapObjP1Y = LMapObjP1Y >> 1;
    LMapObjP2X = LstrMapObjInfo.s16PDCSignal_MapObjP2X + MAPOBJECT_OFFSET;
    LMapObjP2X = LMapObjP2X >> 1;
    LMapObjP2Y = LstrMapObjInfo.s16PDCSignal_MapObjP2Y + MAPOBJECT_OFFSET;
    LMapObjP2Y = LMapObjP2Y >> 1;


    bMapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(bMapObjLockFlg==FALSE)
    {
        LstrTX_APS_0x254_20_MSG.bit.MapObj18Height	   = (LMapObjHeight &0x03);
        LstrTX_APS_0x254_20_MSG.bit.MapObj18HeightProb = (LMapObjHeightProb&0x07);
        LstrTX_APS_0x254_20_MSG.bit.MapObj18P1XH8BIT = ((uint8)(LMapObjP1X>>2u) & 0xff);
        LstrTX_APS_0x254_20_MSG.bit.MapObj18P1XL2BIT = (uint8)(LMapObjP1X & 0x03);
        LstrTX_APS_0x254_20_MSG.bit.MapObj18P1YH2BIT = ((uint8)(LMapObjP1Y>>8u) &0x03);
        LstrTX_APS_0x254_20_MSG.bit.MapObj18P1YL8BIT = (uint8)(LMapObjP1Y & 0xff);
        LstrTX_APS_0x254_20_MSG.bit.MapObj18P2XH8BIT = ((uint8)(LMapObjP2X>>2u) &0xff);
        LstrTX_APS_0x254_20_MSG.bit.MapObj18P2XL2BIT = (uint8)((LMapObjP2X) &0x03);
        LstrTX_APS_0x254_20_MSG.bit.MapObj18P2YH2BIT = ((uint8)(LMapObjP2Y>>8u) & 0x03);
        LstrTX_APS_0x254_20_MSG.bit.MapObj18P2YL8BIT = (uint8)((LMapObjP2Y) &0xff);
        LstrTX_APS_0x254_20_MSG.bit.MapObj18Prob =  ((LMapObjProb)&0X07);
        LstrTX_APS_0x254_20_MSG.bit.MapObj18Type =  ((LMapObjType)&0X07);
    }


    PdcSignal_ReadMapObj(&LstrMapObjInfo,19);
    LMapObjHeightProb = LstrMapObjInfo.u8PDCSignal_MapObjHeightProb;
    LMapObjHeight = LstrMapObjInfo.u8PDCSignal_MapObjHeight;
    LMapObjType = LstrMapObjInfo.u8PDCSignal_MapObjType;
    LMapObjProb = LstrMapObjInfo.u8PDCSignal_MapObjProb;

    LMapObjP1X = LstrMapObjInfo.s16PDCSignal_MapObjP1X + MAPOBJECT_OFFSET;
    LMapObjP1X = LMapObjP1X >> 1;
    LMapObjP1Y = LstrMapObjInfo.s16PDCSignal_MapObjP1Y + MAPOBJECT_OFFSET;
    LMapObjP1Y = LMapObjP1Y >> 1;
    LMapObjP2X = LstrMapObjInfo.s16PDCSignal_MapObjP2X + MAPOBJECT_OFFSET;
    LMapObjP2X = LMapObjP2X >> 1;
    LMapObjP2Y = LstrMapObjInfo.s16PDCSignal_MapObjP2Y + MAPOBJECT_OFFSET;
    LMapObjP2Y = LMapObjP2Y >> 1;

    bMapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(bMapObjLockFlg==FALSE)
    {
        LstrTX_APS_0x254_20_MSG.bit.MapObj19HeightL2BIT	   = (LMapObjHeight &0x03);
        LstrTX_APS_0x254_20_MSG.bit.MapObj19HeightProb = (LMapObjHeightProb&0x07);
        LstrTX_APS_0x254_20_MSG.bit.MapObj19P1XH2BIT = ((uint8)(LMapObjP1X>>8u) & 0x03);
        LstrTX_APS_0x254_20_MSG.bit.MapObj19P1XL8BIT = (uint8)(LMapObjP1X & 0xff);
        LstrTX_APS_0x254_20_MSG.bit.MapObj19P1YH8BIT = ((uint8)(LMapObjP1Y>>2u) &0xff);
        LstrTX_APS_0x254_20_MSG.bit.MapObj19P1YL2BIT = (uint8)(LMapObjP1Y & 0x03);
        LstrTX_APS_0x254_20_MSG.bit.MapObj19P2XH2BIT = ((uint8)(LMapObjP2X>>8u) &0x03);
        LstrTX_APS_0x254_20_MSG.bit.MapObj19P2XL8BIT = (uint8)((LMapObjP2X) &0xff);
        LstrTX_APS_0x254_20_MSG.bit.MapObj19P2YH8BIT = ((uint8)(LMapObjP2Y>>2u) & 0xff);
        LstrTX_APS_0x254_20_MSG.bit.MapObj19P2YL2BIT = (uint8)((LMapObjP2Y) &0x03);
        LstrTX_APS_0x254_20_MSG.bit.MapObj19Prob =  ((LMapObjProb)&0X07);
        LstrTX_APS_0x254_20_MSG.bit.MapObj19Type =  ((LMapObjType)&0X07);
    }
    /********************************End Of Signal Assignment********************************/
    /****************************************************************************************/
    /****************************************************************************************/
    Lu8CRCResult  = CanIL_CRCCheckSum_Algorithm(&(LstrTX_APS_0x254_20_MSG.byte[1]),Lu8CRCLen,CAL_CRC_INIT_VALUE,DataId_254,Gu8NodeALC[ID254Idx]);
    LstrTX_APS_0x254_20_MSG.bit.SigGroup_0x128_ChkSm = Lu8CRCResult;

    /* 将填充数据赋值给COM层buff*/  
    for(i=0;i<32;i++) 
    {
       GcComDataBuffer[ID254Idx][i] = LstrTX_APS_0x254_20_MSG.byte[i];
    }
    Gu8NodeALC[ID254Idx]++;
    /*ALC大于14，清零*/
    if(Gu8NodeALC[ID254Idx] > 0x0F)    Gu8NodeALC[ID254Idx] = 0; 
}
CAN_0x387_100_Type LstrTX_APS_0x387_100_MSG; 
static void CanIL_Tx_PAS_Info7_100ms_387_Msg(const uint8 *pBuff)
{
    uint8 i;
    (void)pBuff;/*消除警告*/    
    //static uint8 LcALC =0;
    uint8 Lu8CRCLen = 32-1;/*报文长度减1*/
    uint8 Lu8CRCResult = 0;
    uint16 SideZone;
	uint8 Lu8TempData = 0;
    LstrTX_APS_0x387_100_MSG.bit.SigGroup_0x387_RlngCtr = Gu8NodeALC[ID387Idx];
    /*信号赋值*/
    /********************************Start Of Signal Assignment********************************/
    /****************************************************************************************/
    /****************************************************************************************/
    PDCSignal_ZoneDisType     LstrZoneDis;
    PdcSignal_ReadZoneDistance(&LstrZoneDis);

    SideZone =LstrZoneDis.RSFM1_Distance;
    LstrTX_APS_0x387_100_MSG.bit.RSFM1_DistanceH4BIT =(uint8)((SideZone>>5)&0x0F);
    LstrTX_APS_0x387_100_MSG.bit.RSFM1_DistanceL5BIT = (uint8)((SideZone & 0x1F));

    SideZone =LstrZoneDis.RSFM2_Distance;
    LstrTX_APS_0x387_100_MSG.bit.RSFM2_DistanceH3BIT = (uint8)((SideZone>>6)&0x07);
    LstrTX_APS_0x387_100_MSG.bit.RSFM2_DistanceL6BIT = (uint8)(SideZone & 0x3F);

    SideZone =LstrZoneDis.RSRM1_Distance;
    LstrTX_APS_0x387_100_MSG.bit.RSRM1_DistanceH6BIT = (uint8)((SideZone >>3) & 0x3F);
    LstrTX_APS_0x387_100_MSG.bit.RSRM1_DistanceL3BIT = (uint8)(SideZone & 0x07);

    SideZone =LstrZoneDis.RSRM2_Distance;
    LstrTX_APS_0x387_100_MSG.bit.RSRM2_DistanceH5BIT = (uint8)((SideZone>>4) & 0x1F);
    LstrTX_APS_0x387_100_MSG.bit.RSRM2_DistanceL4BIT = (uint8)(SideZone & 0x0F);

    SideZone =LstrZoneDis.RSR1_Distance;
    LstrTX_APS_0x387_100_MSG.bit.RSR1_DistanceH8bit = (uint8)(SideZone>>1);
    LstrTX_APS_0x387_100_MSG.bit.RSR1_DistanceL1bit = (uint8)(SideZone & 0x01);

    SideZone =LstrZoneDis.RSR2_Distance;
    LstrTX_APS_0x387_100_MSG.bit.RSR2_DistanceH7bit = (uint8)((SideZone>>2) & 0x7F);
    LstrTX_APS_0x387_100_MSG.bit.RSR2_DistanceL2bit = (uint8)(SideZone & 0x03);

    LstrTX_APS_0x387_100_MSG.bit.DTCWindow_Enable = Read_DTCWindow_Enable();
    LstrTX_APS_0x387_100_MSG.bit.InterFail_Event1_Num = (uint8)SnsDiag_InterFail_Event1_Num();
    LstrTX_APS_0x387_100_MSG.bit.InterFail_Event2_Num = (uint8)SnsDiag_InterFail_Event2_Num();
	Lu8TempData = SnsDiag_InterFail_Event3_Num();
    LstrTX_APS_0x387_100_MSG.bit.InterFail_Event3_NumH2BIT = (uint8)((Lu8TempData >> 4u)&0x03);
    LstrTX_APS_0x387_100_MSG.bit.InterFail_Event3_NumL4BIT = (uint8)(Lu8TempData & 0x0F);
	Lu8TempData = SnsDiag_InterFail_Event4_Num();
    LstrTX_APS_0x387_100_MSG.bit.InterFail_Event4_NumH4BIT = (uint8)((Lu8TempData >> 2u) & 0x0F);
    LstrTX_APS_0x387_100_MSG.bit.InterFail_Event4_NumL2BIT = (uint8)(Lu8TempData & 0x03);
    LstrTX_APS_0x387_100_MSG.bit.InterFail_Event5_Num = (uint8)Elmos17SnsCtrl_InterFail_Event5_Num();
    /*****************************PAS_Localtime_afterSync信号计算及填充*******************************************/
    LstrTX_APS_0x387_100_MSG.bit.PAS_Localtime_afterSyncHHH2BIT=(uint8)((Gu32CANTimeSysn_GlobalTimeBaseYMD>>18)&0x03);
    LstrTX_APS_0x387_100_MSG.bit.PAS_Localtime_afterSyncHH8BIT=(uint8)((Gu32CANTimeSysn_GlobalTimeBaseYMD>>10)&0xFF);
    LstrTX_APS_0x387_100_MSG.bit.PAS_Localtime_afterSyncH8BIT=(uint8)((Gu32CANTimeSysn_GlobalTimeBaseYMD>>2)&0xFF);
    LstrTX_APS_0x387_100_MSG.bit.PAS_Localtime_afterSyncL2BIT=(uint8)((Gu32CANTimeSysn_GlobalTimeBaseYMD)&0x03);
    /********************************End Of Signal Assignment********************************/
    /****************************************************************************************/
    /****************************************************************************************/
    Lu8CRCResult  = CanIL_CRCCheckSum_Algorithm(&(LstrTX_APS_0x387_100_MSG.byte[1]),Lu8CRCLen,CAL_CRC_INIT_VALUE,DataId_387,Gu8NodeALC[ID387Idx]);
    LstrTX_APS_0x387_100_MSG.bit.SigGroup_0x387_ChkSm = Lu8CRCResult;

    /* 将填充数据赋值给COM层buff*/  
    for(i=0;i<32;i++) 
    {
       GcComDataBuffer[ID387Idx][i] = LstrTX_APS_0x387_100_MSG.byte[i];
    }
    Gu8NodeALC[ID387Idx]++;
    /*ALC大于14，清零*/
    if(Gu8NodeALC[ID387Idx] > 0x0F)    Gu8NodeALC[ID387Idx] = 0; 

}
CAN_0x2D5_20_Type LstrTX_APS_0x2D5_20_MSG;
static void CanIL_Tx_PAS_Info8_20ms_2D5_Msg(const uint8* pBuff)
{
    uint8 i;
    (void)pBuff;/*消除警告*/
    /*信号赋值*/
    /********************************Start Of Signal Assignment********************************/
    /****************************************************************************************/
    /****************************************************************************************/
#if 1
    uint8 LPointCloudHeightProb=0;
    uint8 LPointCloudHeight=0;
    uint8 LPointCloudType=0;
    uint8 LPointCloudProb=0;
    uint16 LPointCloudPX=0;
    uint16 LPointCloudPY=0;
    SnsMonitorDataType_t strMonitorData;
    PdcPointCloud_ObjInfoType LstrPointCloudObjInfo;
    
    LstrTX_APS_0x2D5_20_MSG.bit.SigGroup_0x2D5_ChkSm8Bit = 0;
    LstrTX_APS_0x2D5_20_MSG.bit.SigGroup_0x2D5_RlngCtr4Bit = 0;
    bool bPointCloudObjLockFlg=FALSE;
    PdcSignal_ReadPointCloudMapObj(&LstrPointCloudObjInfo,0);
    bPointCloudObjLockFlg = LstrPointCloudObjInfo.bPointCloudLockFLg;
    if(bPointCloudObjLockFlg ==FALSE)
    {
        if(Gu8PointCloudObjUpdateFlag[0] ==1)
        {
            Gu8PointCloudObjUpdateFlag[0] =0;
            LPointCloudHeightProb = LstrPointCloudObjInfo.u8PointCloud_ObjHeightProb;
            LPointCloudHeight = LstrPointCloudObjInfo.u8PointCloud_ObjHeight;
            LPointCloudType = LstrPointCloudObjInfo.u8PointCloud_ObjType;
            LPointCloudProb = LstrPointCloudObjInfo.u8PointCloud_ObjProb;
            LPointCloudPX = LstrPointCloudObjInfo.s16PointCloud_ObjPX+ MAPOBJECT_OFFSET;
            LPointCloudPX = LPointCloudPX >> 1;
            LPointCloudPY = LstrPointCloudObjInfo.s16PointCloud_ObjPY+ MAPOBJECT_OFFSET;
            LPointCloudPY = LPointCloudPY >> 1;
        }
        else
        {
            //PdcSignal_ReadPointCloudMapObj(&LstrPointCloudObjInfo,0);
            LPointCloudHeightProb = 0;
            LPointCloudHeight = 3;
            LPointCloudType = 0;
            LPointCloudProb = 0;
            LPointCloudPX = 0+ MAPOBJECT_OFFSET;
            LPointCloudPX = LPointCloudPX >> 1;
            LPointCloudPY = 0+ MAPOBJECT_OFFSET;
            LPointCloudPY = LPointCloudPY >> 1;

        }
        /* 使用点云高度置信度0来区分车辆配置字，0--非MAX；1--MAX */
        VehicleCfgLevelType LenuVehicleCfgLevel;
        ReadCAN_AppSignal_VehicleCfgLevel(&LenuVehicleCfgLevel);
        LPointCloudHeightProb = (uint8)LenuVehicleCfgLevel;
							
		/* 使用点云高度输出该探头的发波模式，0-定频；1-下扫； 2-上扫；*/
		LPointCloudHeight = GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].enuPDCSnsMeasType[PDC_SNS_CH_FLS];

        /* 使用点云的存在置信度输出该探头是否被车衣覆盖的标志;被覆盖发送0x5 */
        if(Gu8SnsBeCoveredFlag[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FLS])
        {
            LPointCloudProb = 0x5;
        }

        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud0HeightProb3Bit = (LPointCloudHeightProb &0x07);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud0P1XH8Bit = (uint8)((LPointCloudPX>>2)&0xff);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud0P1YH4Bit = (uint8)((LPointCloudPY>>6)&0x0f);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud0Height2Bit = (LPointCloudHeight &0x03);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud0P1XL2Bit = (uint8)(LPointCloudPX&0x03);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud0ProbH2Bit = (uint8)((LPointCloudProb>>1)&0x03);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud0P1YL6Bit = (uint8)(LPointCloudPY&0x3f);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud0Type3Bit = (LPointCloudType &0x07);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud0ProbL1Bit = (uint8)((LPointCloudProb)&0x01);
    }
    


    PdcSignal_ReadPointCloudMapObj(&LstrPointCloudObjInfo,1);
    bPointCloudObjLockFlg = LstrPointCloudObjInfo.bPointCloudLockFLg;
    if(bPointCloudObjLockFlg ==FALSE)
    {
        if(Gu8PointCloudObjUpdateFlag[1] ==1)
        {
            Gu8PointCloudObjUpdateFlag[1] =0;
            LPointCloudHeightProb = LstrPointCloudObjInfo.u8PointCloud_ObjHeightProb;
            LPointCloudHeight = LstrPointCloudObjInfo.u8PointCloud_ObjHeight;
            LPointCloudType = LstrPointCloudObjInfo.u8PointCloud_ObjType;
            LPointCloudProb = LstrPointCloudObjInfo.u8PointCloud_ObjProb;
            LPointCloudPX = LstrPointCloudObjInfo.s16PointCloud_ObjPX+ MAPOBJECT_OFFSET;
            LPointCloudPX = LPointCloudPX >> 1;
            LPointCloudPY = LstrPointCloudObjInfo.s16PointCloud_ObjPY+ MAPOBJECT_OFFSET;
            LPointCloudPY = LPointCloudPY >> 1;

        }
        else
        {
            LPointCloudHeightProb = 0;
            LPointCloudHeight = 3;
            LPointCloudType = 0;
            LPointCloudProb = 0;
            LPointCloudPX = 0+ MAPOBJECT_OFFSET;
            LPointCloudPX = LPointCloudPX >> 1;
            LPointCloudPY = 0+ MAPOBJECT_OFFSET;
            LPointCloudPY = LPointCloudPY >> 1;

        }
        /* 使用点云高度置信度1表示前后系统同频噪音，bit0表示前系统，bit1表示后系统 */
        LPointCloudHeightProb = (GstrParkingGuidenceData.u8SameFreqNoiseFlag[1]<<1) + GstrParkingGuidenceData.u8SameFreqNoiseFlag[0];

		/* 使用点云高度输出该探头的发波模式，0-定频；1-下扫； 2-上扫；*/
		LPointCloudHeight = GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].enuPDCSnsMeasType[PDC_SNS_CH_FL];
        
        /* 使用点云的存在置信度输出该探头是否被车衣覆盖的标志;被覆盖发送0x5 */
        if(Gu8SnsBeCoveredFlag[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FL])
        {
            LPointCloudProb = 0x5;
        }
        
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud1P1XH4Bit = (uint8)((LPointCloudPX>>6)&0x0f);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud1HeightProbH2Bit = (uint8)((LPointCloudHeightProb>>1)&0x03);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud1P1XL6Bit = (uint8)((LPointCloudPX)&0x3f);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud1P1YH5Bit = (uint8)((LPointCloudPY>>5)&0x1f);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud1Height2Bit = (LPointCloudHeight &0x03);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud1HeightProbL1Bit = (uint8)((LPointCloudHeightProb)&0x01);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud1Prob3Bit =  (LPointCloudProb&0x07);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud1P1YL5Bit = (uint8)((LPointCloudPY)&0x1f);;
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud1Type3Bit = (LPointCloudType &0x07);
    }
    
    PdcSignal_ReadPointCloudMapObj(&LstrPointCloudObjInfo,2);
    bPointCloudObjLockFlg = LstrPointCloudObjInfo.bPointCloudLockFLg;
    if(bPointCloudObjLockFlg ==FALSE)
    {
        if(Gu8PointCloudObjUpdateFlag[2] ==1)
        {
            Gu8PointCloudObjUpdateFlag[2] =0;
            LPointCloudHeightProb = LstrPointCloudObjInfo.u8PointCloud_ObjHeightProb;
            LPointCloudHeight = LstrPointCloudObjInfo.u8PointCloud_ObjHeight;
            LPointCloudType = LstrPointCloudObjInfo.u8PointCloud_ObjType;
            LPointCloudProb = LstrPointCloudObjInfo.u8PointCloud_ObjProb;
            LPointCloudPX = LstrPointCloudObjInfo.s16PointCloud_ObjPX+ MAPOBJECT_OFFSET;
            LPointCloudPX = LPointCloudPX >> 1;
            LPointCloudPY = LstrPointCloudObjInfo.s16PointCloud_ObjPY+ MAPOBJECT_OFFSET;
            LPointCloudPY = LPointCloudPY >> 1;

        }
        else
        {
            LPointCloudHeightProb = 0;
            LPointCloudHeight = 3;
            LPointCloudType = 0;
            LPointCloudProb = 0;
            LPointCloudPX = 0+ MAPOBJECT_OFFSET;
            LPointCloudPX = LPointCloudPX >> 1;
            LPointCloudPY = 0+ MAPOBJECT_OFFSET;
            LPointCloudPY = LPointCloudPY >> 1;

        }
        /* 使用点云高度置信度2表示前左侧、后左侧系统同频噪音，bit0表示前系统，bit1表示后系统 */
        LPointCloudHeightProb = (GstrParkingGuidenceData.u8LeftSideFreqNoiseFlag[1]<<1)+GstrParkingGuidenceData.u8LeftSideFreqNoiseFlag[0];

		/* 使用点云高度输出该探头的发波模式，0-定频；1-下扫； 2-上扫；*/
		LPointCloudHeight = GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].enuPDCSnsMeasType[PDC_SNS_CH_FML];

        /* 使用点云的存在置信度输出该探头是否被车衣覆盖的标志;被覆盖发送0x5 */
        if(Gu8SnsBeCoveredFlag[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FML])
        {
            LPointCloudProb = 0x5;
        }
        
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud2P1XH5Bit = (uint8)((LPointCloudPX>>5)&0x1f);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud2HeightProb3Bit = (uint8)((LPointCloudHeightProb)&0x07);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud2P1XL5Bit = (uint8)((LPointCloudPX)&0x1f);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud2P1YH6Bit = (uint8)((LPointCloudPY>>4)&0x3f);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud2Height2Bit = (LPointCloudHeight &0x03);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud2TypeH1Bit = (uint8)((LPointCloudType>>2)&0x01);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud2Prob3Bit = (LPointCloudProb&0x07);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud2P1YL4Bit = (uint8)((LPointCloudPY)&0x0f);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud2TypeL2Bit = (uint8)((LPointCloudType)&0x03);
    }
    

    PdcSignal_ReadPointCloudMapObj(&LstrPointCloudObjInfo,3);
    bPointCloudObjLockFlg = LstrPointCloudObjInfo.bPointCloudLockFLg;
    if(bPointCloudObjLockFlg ==FALSE)
    {
        if(Gu8PointCloudObjUpdateFlag[3] ==1)
        {
            Gu8PointCloudObjUpdateFlag[3] =0;
            LPointCloudHeightProb = LstrPointCloudObjInfo.u8PointCloud_ObjHeightProb;
            LPointCloudHeight = LstrPointCloudObjInfo.u8PointCloud_ObjHeight;
            LPointCloudType = LstrPointCloudObjInfo.u8PointCloud_ObjType;
            LPointCloudProb = LstrPointCloudObjInfo.u8PointCloud_ObjProb;
            LPointCloudPX = LstrPointCloudObjInfo.s16PointCloud_ObjPX+ MAPOBJECT_OFFSET;
            LPointCloudPX = LPointCloudPX >> 1;
            LPointCloudPY = LstrPointCloudObjInfo.s16PointCloud_ObjPY+ MAPOBJECT_OFFSET;
            LPointCloudPY = LPointCloudPY >> 1;

        }
        else
        {
            LPointCloudHeightProb = 0;
            LPointCloudHeight = 3;
            LPointCloudType = 0;
            LPointCloudProb = 0;
            LPointCloudPX = 0+ MAPOBJECT_OFFSET;
            LPointCloudPX = LPointCloudPX >> 1;
            LPointCloudPY = 0+ MAPOBJECT_OFFSET;
            LPointCloudPY = LPointCloudPY >> 1;

        }
        /* 使用点云高度置信度3表示前右侧、后右侧系统同频噪音，bit0表示前系统，bit1表示后系统 */
        LPointCloudHeightProb = (GstrParkingGuidenceData.u8RightSideFreqNoiseFlag[1]<<1)+GstrParkingGuidenceData.u8RightSideFreqNoiseFlag[0];

		
		/* 使用点云高度输出该探头的发波模式，0-定频；1-下扫； 2-上扫；*/
		LPointCloudHeight = GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].enuPDCSnsMeasType[PDC_SNS_CH_FMR];

        /* 使用点云的存在置信度输出该探头是否被车衣覆盖的标志;被覆盖发送0x5 */
        if(Gu8SnsBeCoveredFlag[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FMR])
        {
            LPointCloudProb = 0x5;
        }

        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud3P1XH6Bit = (uint8)((LPointCloudPX>>4)&0x3f);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud3HeightH1Bit = (uint8)((LPointCloudHeight>>1)&0x01);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud3HeightProb3Bit = (uint8)((LPointCloudHeightProb)&0x07);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud3P1XL4Bit = (uint8)((LPointCloudPX)&0x0f);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud3P1YH7Bit = (uint8)((LPointCloudPY>>3)&0x7f);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud3HeightL1Bit = (uint8)((LPointCloudHeight)&0x01);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud3TypeH2Bit = (uint8)((LPointCloudType>>1)&0x03);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud3Prob3Bit = (LPointCloudProb&0x07);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud3P1YL3Bit = (uint8)((LPointCloudPY)&0x07);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud3TypeL1Bit = (uint8)((LPointCloudType)&0x01);
    }
    

    PdcSignal_ReadPointCloudMapObj(&LstrPointCloudObjInfo,4);
    bPointCloudObjLockFlg = LstrPointCloudObjInfo.bPointCloudLockFLg;
    if(bPointCloudObjLockFlg ==FALSE)
    {
        if(Gu8PointCloudObjUpdateFlag[4] ==1)
        {
            Gu8PointCloudObjUpdateFlag[4] =0;
            LPointCloudHeightProb = LstrPointCloudObjInfo.u8PointCloud_ObjHeightProb;
            LPointCloudHeight = LstrPointCloudObjInfo.u8PointCloud_ObjHeight;
            LPointCloudType = LstrPointCloudObjInfo.u8PointCloud_ObjType;
            LPointCloudProb = LstrPointCloudObjInfo.u8PointCloud_ObjProb;
            LPointCloudPX = LstrPointCloudObjInfo.s16PointCloud_ObjPX+ MAPOBJECT_OFFSET;
            LPointCloudPX = LPointCloudPX >> 1;
            LPointCloudPY = LstrPointCloudObjInfo.s16PointCloud_ObjPY+ MAPOBJECT_OFFSET;
            LPointCloudPY = LPointCloudPY >> 1;

        }
        else
        {
            LPointCloudHeightProb = 0;
            LPointCloudHeight = 3;
            LPointCloudType = 0;
            LPointCloudProb = 0;
            LPointCloudPX = 0+ MAPOBJECT_OFFSET;
            LPointCloudPX = LPointCloudPX >> 1;
            LPointCloudPY = 0+ MAPOBJECT_OFFSET;
            LPointCloudPY = LPointCloudPY >> 1;

        }

		
		/* 使用点云高度输出该探头的发波模式，0-定频；1-下扫； 2-上扫；*/
		LPointCloudHeight = GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].enuPDCSnsMeasType[PDC_SNS_CH_FR];
        
        /* 使用点云的存在置信度输出该探头是否被车衣覆盖的标志;被覆盖发送0x5 */
        if(Gu8SnsBeCoveredFlag[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FR])
        {
            LPointCloudProb = 0x5;
        }
        
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud4P1XH7Bit = (uint8)((LPointCloudPX>>3)&0x7f);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud4Height2Bit = (uint8)((LPointCloudHeight)&0x03);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud4HeightProb3Bit = (uint8)((LPointCloudHeightProb)&0x07);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud4P1XL3Bit = (uint8)((LPointCloudPX)&0x07);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud4P1YH8Bit = (uint8)((LPointCloudPY>>2)&0xff);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud4Type3Bit = (uint8)((LPointCloudType)&0x07);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud4Prob3Bit = (LPointCloudProb&0x07);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud4P1YL2Bit = (uint8)((LPointCloudPY)&0x03);
    }
    
    
    PdcSignal_ReadPointCloudMapObj(&LstrPointCloudObjInfo,5);
    bPointCloudObjLockFlg = LstrPointCloudObjInfo.bPointCloudLockFLg;
    if(bPointCloudObjLockFlg ==FALSE)
    {
        if(Gu8PointCloudObjUpdateFlag[5] ==1)
        {
            Gu8PointCloudObjUpdateFlag[5] =0;
            LPointCloudHeightProb = LstrPointCloudObjInfo.u8PointCloud_ObjHeightProb;
            LPointCloudHeight = LstrPointCloudObjInfo.u8PointCloud_ObjHeight;
            LPointCloudType = LstrPointCloudObjInfo.u8PointCloud_ObjType;
            LPointCloudProb = LstrPointCloudObjInfo.u8PointCloud_ObjProb;
            LPointCloudPX = LstrPointCloudObjInfo.s16PointCloud_ObjPX+ MAPOBJECT_OFFSET;
            LPointCloudPX = LPointCloudPX >> 1;
            LPointCloudPY = LstrPointCloudObjInfo.s16PointCloud_ObjPY+ MAPOBJECT_OFFSET;
            LPointCloudPY = LPointCloudPY >> 1;
        }
        else
        {
            LPointCloudHeightProb = 0;
            LPointCloudHeight = 3;
            LPointCloudType = 0;
            LPointCloudProb = 0;
            LPointCloudPX = 0+ MAPOBJECT_OFFSET;
            LPointCloudPX = LPointCloudPX >> 1;
            LPointCloudPY = 0+ MAPOBJECT_OFFSET;
            LPointCloudPY = LPointCloudPY >> 1;

        }

		/* 使用点云高度输出该探头的发波模式，0-定频；1-下扫； 2-上扫；*/
		LPointCloudHeight = GstrPDCSnsRawData[PDC_SNS_GROUP_FRONT].enuPDCSnsMeasType[PDC_SNS_CH_FRS];

        /* 使用点云的存在置信度输出该探头是否被车衣覆盖的标志;被覆盖发送0x5 */
        if(Gu8SnsBeCoveredFlag[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FRS])
        {
            LPointCloudProb = 0x5;
        }

        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud5P1XH8Bit = (uint8)((LPointCloudPX>>2)&0xff);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud5P1YH1Bit = (uint8)((LPointCloudPY>>9)&0x01);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud5Height2Bit = (uint8)((LPointCloudHeight)&0x03);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud5HeightProb3Bit = (uint8)((LPointCloudHeightProb)&0x07);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud5P1XL2Bit = (uint8)((LPointCloudPX)&0x03);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud5P1YM8Bit = (uint8)((LPointCloudPY>>1)&0xff);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud5Type3Bit = (uint8)((LPointCloudType)&0x07);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud5Prob3Bit = (LPointCloudProb&0x07);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud5P1YL1Bit = (uint8)((LPointCloudPY)&0x01);
    }
    

    
    PdcSignal_ReadPointCloudMapObj(&LstrPointCloudObjInfo,6);
    bPointCloudObjLockFlg = LstrPointCloudObjInfo.bPointCloudLockFLg;
    if(bPointCloudObjLockFlg ==FALSE)
    {
        if(Gu8PointCloudObjUpdateFlag[6] ==1)
        {
            Gu8PointCloudObjUpdateFlag[6] =0;
            LPointCloudHeightProb = LstrPointCloudObjInfo.u8PointCloud_ObjHeightProb;
            LPointCloudHeight = LstrPointCloudObjInfo.u8PointCloud_ObjHeight;
            LPointCloudType = LstrPointCloudObjInfo.u8PointCloud_ObjType;
            LPointCloudProb = LstrPointCloudObjInfo.u8PointCloud_ObjProb;
            LPointCloudPX = LstrPointCloudObjInfo.s16PointCloud_ObjPX+ MAPOBJECT_OFFSET;
            LPointCloudPX = LPointCloudPX >> 1;
            LPointCloudPY = LstrPointCloudObjInfo.s16PointCloud_ObjPY+ MAPOBJECT_OFFSET;
            LPointCloudPY = LPointCloudPY >> 1;
        }
        else
        {
            LPointCloudHeightProb = 0;
            LPointCloudHeight = 3;
            LPointCloudType = 0;
            LPointCloudProb = 0;
            LPointCloudPX = 0+ MAPOBJECT_OFFSET;
            LPointCloudPX = LPointCloudPX >> 1;
            LPointCloudPY = 0+ MAPOBJECT_OFFSET;
            LPointCloudPY = LPointCloudPY >> 1;   

        }

		/* 使用点云高度输出该探头的发波模式，0-定频；1-下扫； 2-上扫；*/
		LPointCloudHeight = GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].enuPDCSnsMeasType[PDC_SNS_CH_RRS];

        /* 使用点云的存在置信度输出该探头是否被车衣覆盖的标志;被覆盖发送0x5 */
        if(Gu8SnsBeCoveredFlag[PDC_SNS_GROUP_REAR][PDC_SNS_CH_RRS])
        {
            LPointCloudProb = 0x5;
        }

        
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud6P1XH1Bit = (uint8)((LPointCloudPX>>9)&0x01);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud6P1XM8Bit = (uint8)((LPointCloudPX>>1)&0xff);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud6P1YH2Bit = (uint8)((LPointCloudPY>>8)&0x03);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud6Height2Bit = (uint8)((LPointCloudHeight)&0x03);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud6HeightProb3Bit = (uint8)((LPointCloudHeightProb)&0x07);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud6P1XL1Bit = (uint8)((LPointCloudPX)&0x01);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud6P1YL8Bit = (uint8)((LPointCloudPY)&0xff);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud6Type3Bit = (uint8)((LPointCloudType)&0x07);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud6Prob3Bit = (LPointCloudProb&0x07);
    }
    
    PdcSignal_ReadPointCloudMapObj(&LstrPointCloudObjInfo,7);
    bPointCloudObjLockFlg = LstrPointCloudObjInfo.bPointCloudLockFLg;
    if(bPointCloudObjLockFlg ==FALSE)
    {
        if(Gu8PointCloudObjUpdateFlag[7] ==1)
        {
            Gu8PointCloudObjUpdateFlag[7] =0;
            LPointCloudHeightProb = LstrPointCloudObjInfo.u8PointCloud_ObjHeightProb;
            LPointCloudHeight = LstrPointCloudObjInfo.u8PointCloud_ObjHeight;
            LPointCloudType = LstrPointCloudObjInfo.u8PointCloud_ObjType;
            LPointCloudProb = LstrPointCloudObjInfo.u8PointCloud_ObjProb;
            LPointCloudPX = LstrPointCloudObjInfo.s16PointCloud_ObjPX+ MAPOBJECT_OFFSET;
            LPointCloudPX = LPointCloudPX >> 1;
            LPointCloudPY = LstrPointCloudObjInfo.s16PointCloud_ObjPY+ MAPOBJECT_OFFSET;
            LPointCloudPY = LPointCloudPY >> 1;
        }
        else
        {
            LPointCloudHeightProb = 0;
            LPointCloudHeight = 3;
            LPointCloudType = 0;
            LPointCloudProb = 0;
            LPointCloudPX = 0+ MAPOBJECT_OFFSET;
            LPointCloudPX = LPointCloudPX >> 1;
            LPointCloudPY = 0+ MAPOBJECT_OFFSET;
            LPointCloudPY = LPointCloudPY >> 1;   

        }
		
		/* 使用点云高度输出该探头的发波模式，0-定频；1-下扫； 2-上扫；*/
		LPointCloudHeight = GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].enuPDCSnsMeasType[PDC_SNS_CH_RR];

        /* 使用点云的存在置信度输出该探头是否被车衣覆盖的标志;被覆盖发送0x5 */
        if(Gu8SnsBeCoveredFlag[PDC_SNS_GROUP_REAR][PDC_SNS_CH_RR])
        {
            LPointCloudProb = 0x5;
        }
        
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud7P1XH2Bit = (uint8)((LPointCloudPX>>8)&0x03);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud7P1XL8Bit = (uint8)((LPointCloudPX)&0xff);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud7P1YH3Bit = (uint8)((LPointCloudPY>>7)&0x07);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud7Height2Bit = (uint8)((LPointCloudHeight)&0x03);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud7HeightProb3Bit = (uint8)((LPointCloudHeightProb)&0x07);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud7ProbH1Bit = (uint8)((LPointCloudProb>>2)&0x01);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud7P1YL7Bit = (uint8)((LPointCloudPY)&0x7f);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud7Type3Bit = (uint8)((LPointCloudType)&0x07);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud7ProbL2Bit = (uint8)((LPointCloudProb)&0x02);
    }
    

    PdcSignal_ReadPointCloudMapObj(&LstrPointCloudObjInfo,8);
    bPointCloudObjLockFlg = LstrPointCloudObjInfo.bPointCloudLockFLg;
    if(bPointCloudObjLockFlg ==FALSE)
    {
        if(Gu8PointCloudObjUpdateFlag[8] ==1)
        {
            Gu8PointCloudObjUpdateFlag[8] =0;
            LPointCloudHeightProb = LstrPointCloudObjInfo.u8PointCloud_ObjHeightProb;
            LPointCloudHeight = LstrPointCloudObjInfo.u8PointCloud_ObjHeight;
            LPointCloudType = LstrPointCloudObjInfo.u8PointCloud_ObjType;
            LPointCloudProb = LstrPointCloudObjInfo.u8PointCloud_ObjProb;
            LPointCloudPX = LstrPointCloudObjInfo.s16PointCloud_ObjPX+ MAPOBJECT_OFFSET;
            LPointCloudPX = LPointCloudPX >> 1;
            LPointCloudPY = LstrPointCloudObjInfo.s16PointCloud_ObjPY+ MAPOBJECT_OFFSET;
            LPointCloudPY = LPointCloudPY >> 1;
        }
        else
        {
            LPointCloudHeightProb = 0;
            LPointCloudHeight = 3;
            LPointCloudType = 0;
            LPointCloudProb = 0;
            LPointCloudPX = 0+ MAPOBJECT_OFFSET;
            LPointCloudPX = LPointCloudPX >> 1;
            LPointCloudPY = 0+ MAPOBJECT_OFFSET;
            LPointCloudPY = LPointCloudPY >> 1;   

        }
		
		/* 使用点云高度输出该探头的发波模式，0-定频；1-下扫； 2-上扫；*/
		LPointCloudHeight = GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].enuPDCSnsMeasType[PDC_SNS_CH_RMR];

        /* 使用点云的存在置信度输出该探头是否被车衣覆盖的标志;被覆盖发送0x5 */
        if(Gu8SnsBeCoveredFlag[PDC_SNS_GROUP_REAR][PDC_SNS_CH_RMR])
        {
            LPointCloudProb = 0x5;
        }
        
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud8P1XH3Bit = (uint8)((LPointCloudPX>>7)&0x07);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud8HeightProbH1Bit = (uint8)((LPointCloudHeightProb>>2)&0x01);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud8P1XL7Bit = (uint8)((LPointCloudPX)&0x7f);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud8P1YH4Bit = (uint8)((LPointCloudPY>>6)&0x0f);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud8Height2Bit = (uint8)((LPointCloudHeight)&0x03);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud8HeightProbL2Bit = (uint8)((LPointCloudHeightProb)&0x03);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud8ProbH2Bit = (uint8)((LPointCloudProb>>1)&0x03);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud8P1YL6Bit = (uint8)((LPointCloudPY)&0x3f);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud8Type3Bit = (uint8)((LPointCloudType)&0x07);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud8ProbL1Bit = (uint8)((LPointCloudProb)&0x01);
    }
    

    PdcSignal_ReadPointCloudMapObj(&LstrPointCloudObjInfo,9);
    bPointCloudObjLockFlg = LstrPointCloudObjInfo.bPointCloudLockFLg;
    if(bPointCloudObjLockFlg ==FALSE)
    {
        if(Gu8PointCloudObjUpdateFlag[9] ==1)
        {
            Gu8PointCloudObjUpdateFlag[9] =0;
            LPointCloudHeightProb = LstrPointCloudObjInfo.u8PointCloud_ObjHeightProb;
            LPointCloudHeight = LstrPointCloudObjInfo.u8PointCloud_ObjHeight;
            LPointCloudType = LstrPointCloudObjInfo.u8PointCloud_ObjType;
            LPointCloudProb = LstrPointCloudObjInfo.u8PointCloud_ObjProb;
            LPointCloudPX = LstrPointCloudObjInfo.s16PointCloud_ObjPX+ MAPOBJECT_OFFSET;
            LPointCloudPX = LPointCloudPX >> 1;
            LPointCloudPY = LstrPointCloudObjInfo.s16PointCloud_ObjPY+ MAPOBJECT_OFFSET;
            LPointCloudPY = LPointCloudPY >> 1;
#if 0
            printf("RML Cloud Update,Time:%.3f,X:%d,Y:%d\r\n",GfMessageTime,
                LstrPointCloudObjInfo.s16PointCloud_ObjPX,\
                LstrPointCloudObjInfo.s16PointCloud_ObjPY);
#endif
        }
        else
        {
            LPointCloudHeightProb = 0;
            LPointCloudHeight = 3;
            LPointCloudType = 0;
            LPointCloudProb = 0;
            LPointCloudPX = 0+ MAPOBJECT_OFFSET;
            LPointCloudPX = LPointCloudPX >> 1;
            LPointCloudPY = 0+ MAPOBJECT_OFFSET;
            LPointCloudPY = LPointCloudPY >> 1;   

        }
		
		/* 使用点云高度输出该探头的发波模式，0-定频；1-下扫； 2-上扫；*/
		LPointCloudHeight = GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].enuPDCSnsMeasType[PDC_SNS_CH_RML];

        /* 使用点云的存在置信度输出该探头是否被车衣覆盖的标志;被覆盖发送0x5 */
        if(Gu8SnsBeCoveredFlag[PDC_SNS_GROUP_REAR][PDC_SNS_CH_RML])
        {
            LPointCloudProb = 0x5;
        }

        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud9P1XH4Bit = (uint8)((LPointCloudPX>>6)&0x0f);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud9HeightProbH2Bit = (uint8)((LPointCloudHeightProb>>1)&0x03);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud9P1XL6Bit = (uint8)((LPointCloudPX)&0x3f);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud9P1YH5Bit = (uint8)((LPointCloudPY>>5)&0x1f);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud9Height2Bit = (uint8)((LPointCloudHeight)&0x03);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud9HeightProbL1Bit = (uint8)((LPointCloudHeightProb)&0x01);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud9Prob3Bit = (uint8)((LPointCloudProb)&0x07);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud9P1YL5Bit = (uint8)((LPointCloudPY)&0x1f);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud9Type3Bit = (uint8)((LPointCloudType)&0x07);
    }
    

    PdcSignal_ReadPointCloudMapObj(&LstrPointCloudObjInfo,10);
    bPointCloudObjLockFlg = LstrPointCloudObjInfo.bPointCloudLockFLg;
    if(bPointCloudObjLockFlg ==FALSE)
    {
        if(Gu8PointCloudObjUpdateFlag[10] ==1)
        {
            Gu8PointCloudObjUpdateFlag[10] =0;
            LPointCloudHeightProb = LstrPointCloudObjInfo.u8PointCloud_ObjHeightProb;
            LPointCloudHeight = LstrPointCloudObjInfo.u8PointCloud_ObjHeight;
            LPointCloudType = LstrPointCloudObjInfo.u8PointCloud_ObjType;
            LPointCloudProb = LstrPointCloudObjInfo.u8PointCloud_ObjProb;
            LPointCloudPX = LstrPointCloudObjInfo.s16PointCloud_ObjPX+ MAPOBJECT_OFFSET;
            LPointCloudPX = LPointCloudPX >> 1;
            LPointCloudPY = LstrPointCloudObjInfo.s16PointCloud_ObjPY+ MAPOBJECT_OFFSET;
            LPointCloudPY = LPointCloudPY >> 1;
        }
        else
        {
            LPointCloudHeightProb = 0;
            LPointCloudHeight = 3;
            LPointCloudType = 0;
            LPointCloudProb = 0;
            LPointCloudPX = 0+ MAPOBJECT_OFFSET;
            LPointCloudPX = LPointCloudPX >> 1;
            LPointCloudPY = 0+ MAPOBJECT_OFFSET;
            LPointCloudPY = LPointCloudPY >> 1;   

        }
		
		/* 使用点云高度输出该探头的发波模式，0-定频；1-下扫； 2-上扫；*/
		LPointCloudHeight = GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].enuPDCSnsMeasType[PDC_SNS_CH_RL];

        /* 使用点云的存在置信度输出该探头是否被车衣覆盖的标志;被覆盖发送0x5 */
        if(Gu8SnsBeCoveredFlag[PDC_SNS_GROUP_REAR][PDC_SNS_CH_RL])
        {
            LPointCloudProb = 0x5;
        }
        
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud10P1XH5Bit = (uint8)((LPointCloudPX>>5)&0x1f);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud10HeightProb3Bit = (uint8)((LPointCloudHeightProb)&0x07);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud10P1XL5Bit = (uint8)((LPointCloudPX)&0x1f);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud10P1YH6Bit = (uint8)((LPointCloudPY>>4)&0x3f);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud10Height2Bit = (uint8)((LPointCloudHeight)&0x03);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud10TypeH1Bit = (uint8)((LPointCloudType>>2)&0x01);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud10Prob3Bit = (uint8)((LPointCloudProb)&0x07);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud10P1YL4Bit = (uint8)((LPointCloudPY)&0x0f);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud10TypeL2Bit = (uint8)((LPointCloudType)&0x03);
    }
    

    
    PdcSignal_ReadPointCloudMapObj(&LstrPointCloudObjInfo,11);
    bPointCloudObjLockFlg = LstrPointCloudObjInfo.bPointCloudLockFLg;
    if(bPointCloudObjLockFlg ==FALSE)
    {
        if(Gu8PointCloudObjUpdateFlag[11] ==1)
        {
            Gu8PointCloudObjUpdateFlag[11] =0;
            LPointCloudHeightProb = LstrPointCloudObjInfo.u8PointCloud_ObjHeightProb;
            LPointCloudHeight = LstrPointCloudObjInfo.u8PointCloud_ObjHeight;
            LPointCloudType = LstrPointCloudObjInfo.u8PointCloud_ObjType;
            LPointCloudProb = LstrPointCloudObjInfo.u8PointCloud_ObjProb;
            LPointCloudPX = LstrPointCloudObjInfo.s16PointCloud_ObjPX+ MAPOBJECT_OFFSET;
            LPointCloudPX = LPointCloudPX >> 1;
            LPointCloudPY = LstrPointCloudObjInfo.s16PointCloud_ObjPY+ MAPOBJECT_OFFSET;
            LPointCloudPY = LPointCloudPY >> 1;
        }
        else
        {
            LPointCloudHeightProb = 0;
            LPointCloudHeight = 3;
            LPointCloudType = 0;
            LPointCloudProb = 0;
            LPointCloudPX = 0+ MAPOBJECT_OFFSET;
            LPointCloudPX = LPointCloudPX >> 1;
            LPointCloudPY = 0+ MAPOBJECT_OFFSET;
            LPointCloudPY = LPointCloudPY >> 1;
        }
		
		/* 使用点云高度输出该探头的发波模式，0-定频；1-下扫； 2-上扫；*/
		LPointCloudHeight = GstrPDCSnsRawData[PDC_SNS_GROUP_REAR].enuPDCSnsMeasType[PDC_SNS_CH_RLS];

        /* 使用点云的存在置信度输出该探头是否被车衣覆盖的标志;被覆盖发送0x5 */
        if(Gu8SnsBeCoveredFlag[PDC_SNS_GROUP_REAR][PDC_SNS_CH_RLS])
        {
            LPointCloudProb = 0x5;
        }


        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud11P1XH6Bit = (uint8)((LPointCloudPX>>4)&0x3f);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud11HeightH1Bit = (uint8)((LPointCloudHeight>>1)&0x01);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud11HeightProb3Bit = (uint8)((LPointCloudHeightProb)&0x07);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud11P1XL4Bit = (uint8)((LPointCloudPX)&0x0f);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud11P1YH7Bit = (uint8)((LPointCloudPY>>3)&0x7f);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud11HeightL1Bit = (uint8)((LPointCloudHeight)&0x01);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud11TypeH2Bit = (uint8)((LPointCloudType>>1)&0x03);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud11Prob3Bit = (uint8)((LPointCloudProb)&0x07);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud11P1YL3Bit = (uint8)((LPointCloudPY)&0x07);
        LstrTX_APS_0x2D5_20_MSG.bit.PointCloud11TypeL1Bit = (uint8)((LPointCloudType)&0x01);
    }
    /*Radar diagnostic data update*/

	SnsDiag_SnsMonitorData(&strMonitorData);
    LstrTX_APS_0x2D5_20_MSG.bit.SnsIdx1 = strMonitorData.eSnsIdx1;	
	LstrTX_APS_0x2D5_20_MSG.bit.ImpedanceIdx1 = strMonitorData.u8ImpedanceIdx1;
	LstrTX_APS_0x2D5_20_MSG.bit.RingTime1_2 = strMonitorData.u16RingTime1 >> 8;/*High*/
    LstrTX_APS_0x2D5_20_MSG.bit.RingTime1_1 = strMonitorData.u16RingTime1;/*Low*/
	LstrTX_APS_0x2D5_20_MSG.bit.RingFre1_2 = strMonitorData.u16RingFre1 >> 8;/*High*/
	LstrTX_APS_0x2D5_20_MSG.bit.RingFre1_1 = strMonitorData.u16RingFre1;/*Low*/

	LstrTX_APS_0x2D5_20_MSG.bit.SnsIdx2 = strMonitorData.eSnsIdx2;	
	LstrTX_APS_0x2D5_20_MSG.bit.ImpedanceIdx2 = strMonitorData.u8ImpedanceIdx2;
	LstrTX_APS_0x2D5_20_MSG.bit.RingTime2_2 = strMonitorData.u16RingTime2 >> 8;/*High*/
    LstrTX_APS_0x2D5_20_MSG.bit.RingTime2_1 = strMonitorData.u16RingTime2;/*Low*/
	LstrTX_APS_0x2D5_20_MSG.bit.RingFre2_2 = strMonitorData.u16RingFre2 >> 8;/*High*/
	LstrTX_APS_0x2D5_20_MSG.bit.RingFre2_1 = strMonitorData.u16RingFre2;/*Low*/

	LstrTX_APS_0x2D5_20_MSG.bit.SnsIdx3 = strMonitorData.eSnsIdx3;	
	LstrTX_APS_0x2D5_20_MSG.bit.ImpedanceIdx3 = strMonitorData.u8ImpedanceIdx3;
	LstrTX_APS_0x2D5_20_MSG.bit.RingTime3_2 = strMonitorData.u16RingTime3 >> 8;/*High*/
    LstrTX_APS_0x2D5_20_MSG.bit.RingTime3_1 = strMonitorData.u16RingTime3;/*Low*/
	LstrTX_APS_0x2D5_20_MSG.bit.RingFre3_2 = strMonitorData.u16RingFre3 >> 8;/*High*/
	LstrTX_APS_0x2D5_20_MSG.bit.RingFre3_1 = strMonitorData.u16RingFre3;/*Low*/
/*
    0x01:Front Power great than or equal to 13V;less than or equal to 11V
    0x02:Rear Power great than or equal to 13V;less than or equal to 11V
    0x04:Side Power great than or equal to 13V;less than or equal to 11V*/
    LstrTX_APS_0x2D5_20_MSG.bit.VolAbnormaFlg = (SnsDiag_VolAbnormaFlg() & 0x07);
#endif
    /********************************End Of Signal Assignment********************************/
    /****************************************************************************************/
    /****************************************************************************************/
    /* 将填充数据赋值给COM层buff*/
    for (i = 0;i < 64;i++)
    {
        GcComDataBuffer[ID2D5Idx][i] = LstrTX_APS_0x2D5_20_MSG.byte[i];
    }
}


CAN_0x238_20_Type LstrTX_APS_0x238_20_MSG; 
static void CanIL_Tx_PAS_Info_20ms_238_Msg(const uint8 *pBuff)
{
    uint8 i;
    uint16 Lu16FrontDEDistance,Lu16RearDEDistance;
    uint16 Lu16FrontCEDistance,Lu16RearCEDistance;
    (void)pBuff;/*消除警告*/    

    /*信号赋值*/
    PDCSignal_DEStructType  LstrFrontDEDistace;
    PDCSignal_DEStructType  LstrRearDEDistace;
    PDCSignal_CEStructType  LstrFrontCEDistace;
    PDCSignal_CEStructType  LstrRearCEDistace;
    PdcSignal_ReadUSS_DE_Distace(&LstrFrontDEDistace,FRONT_GROUP);
    PdcSignal_ReadUSS_DE_Distace(&LstrRearDEDistace,REAR_GROUP);
    PdcSignal_ReadUSS_CE_Distace(&LstrFrontCEDistace,FRONT_GROUP);
    PdcSignal_ReadUSS_CE_Distace(&LstrRearCEDistace,REAR_GROUP);
    Lu16FrontDEDistance=LstrFrontDEDistace.u16PDCSignal_DE1;
    Lu16RearDEDistance=LstrRearDEDistace.u16PDCSignal_DE1;
    Lu16FrontCEDistance=LstrFrontCEDistace.u16PDCSignal_CE1_2;
    Lu16RearCEDistance=LstrRearCEDistace.u16PDCSignal_CE1_2;
    /**DE**/
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_1H8BIT = (uint8)(Lu16FrontDEDistance>>2);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_1L2BIT = (uint8)(Lu16FrontDEDistance & 0x03);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_12H2BIT = (uint8)((Lu16RearDEDistance >> 8) & 0x03);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_12L8BIT = (uint8)(Lu16RearDEDistance);

    /**CE**/
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_1_2H8BIT = (uint8)(Lu16FrontCEDistance>>2);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_1_2L2BIT = (uint8)(Lu16FrontCEDistance & 0x03);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_11_12H4BIT = (uint8)((Lu16RearCEDistance>>6)&0x0F);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_11_12L6BIT = (uint8)(Lu16RearCEDistance & 0x3F);	

    Lu16FrontDEDistance=LstrFrontDEDistace.u16PDCSignal_DE2;
    Lu16RearDEDistance=LstrRearDEDistace.u16PDCSignal_DE2;
    Lu16FrontCEDistance=LstrFrontCEDistace.u16PDCSignal_CE2_3;
    Lu16RearCEDistance=LstrRearCEDistace.u16PDCSignal_CE2_3;
    /**DE**/
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_2H6BIT = (uint8)((Lu16FrontDEDistance >>4)&0x3F);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_2L4BIT =(uint8)(Lu16FrontDEDistance & 0x0F);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_11H4BIT = (uint8)((Lu16RearDEDistance >>6)& 0x0F);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_11L6BIT = (uint8)(Lu16RearDEDistance & 0x3F);

    /**CE**/
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_2_3H6BIT = (uint8)((Lu16FrontCEDistance >>4)&0x3F);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_2_3L4BIT = (uint8)(Lu16FrontCEDistance & 0x0F);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_10_11H6BIT = (uint8)((Lu16RearCEDistance >>4)&0x3F);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_10_11L4BIT = (uint8)(Lu16RearCEDistance & 0x0F);

    Lu16FrontDEDistance=LstrFrontDEDistace.u16PDCSignal_DE3;
    Lu16RearDEDistance=LstrRearDEDistace.u16PDCSignal_DE3;
    Lu16FrontCEDistance=LstrFrontCEDistace.u16PDCSignal_CE3_4;
    Lu16RearCEDistance=LstrRearCEDistace.u16PDCSignal_CE3_4;
    /**DE**/
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_3H4BIT =(uint8)((Lu16FrontDEDistance >>6)&0x0F);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_3L6BIT = (uint8)(Lu16FrontDEDistance & 0x3F);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_10H6BIT = (uint8)((Lu16RearDEDistance >>4)&0x3F);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_10L4BIT = (uint8)(Lu16RearDEDistance & 0x0F);
    /**CE**/		
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_3_4H4BIT = (uint8)((Lu16FrontCEDistance >>6)& 0x0F);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_3_4L6BIT = (uint8)(Lu16FrontCEDistance & 0x3F);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_9_10H8BIT = (uint8)(Lu16RearCEDistance>>2);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_9_10L2BIT = (uint8)(Lu16RearCEDistance & 0x03);

    Lu16FrontDEDistance=LstrFrontDEDistace.u16PDCSignal_DE4;
    Lu16RearDEDistance=LstrRearDEDistace.u16PDCSignal_DE4;
    Lu16FrontCEDistance=LstrFrontCEDistace.u16PDCSignal_CE4_5;
    Lu16RearCEDistance=LstrRearCEDistace.u16PDCSignal_CE4_5;
    /**DE**/
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_4H2BIT = (uint8)((Lu16FrontDEDistance >>8) & 0x03);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_4L8BIT = (uint8)Lu16FrontDEDistance ;
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_9H8BIT = (uint8)(Lu16RearDEDistance>>2);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_9L2BIT = (uint8)(Lu16RearDEDistance & 0x03);

    /**CE**/
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_4_5H2BIT = (uint8)((Lu16FrontCEDistance >>8) & 0x03);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_4_5L8BIT = (uint8)Lu16FrontCEDistance ;
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_8_9H2BIT = (uint8)((Lu16RearCEDistance >>8) & 0x03);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_8_9L8BIT = (uint8)Lu16RearCEDistance ;

    Lu16FrontDEDistance=LstrFrontDEDistace.u16PDCSignal_DE5;
    Lu16RearDEDistance=LstrRearDEDistace.u16PDCSignal_DE5;
    Lu16FrontCEDistance=LstrFrontCEDistace.u16PDCSignal_CE5_6;
    Lu16RearCEDistance=LstrRearCEDistace.u16PDCSignal_CE5_6;
    /**DE**/
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_5H8BIT =(uint8)(Lu16FrontDEDistance>>2);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_5L2BIT =(uint8)(Lu16FrontDEDistance & 0x03);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_8H2BIT = (uint8)((Lu16RearDEDistance>>8) & 0x03);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_8L8BIT = (uint8)Lu16RearDEDistance;
    /**CE**/
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_5_6H8BIT =(uint8)(Lu16FrontCEDistance>>2);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_5_6L2BIT = (uint8)(Lu16FrontCEDistance & 0x03);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_7_8H4BIT = (uint8)((Lu16RearCEDistance >>6)&0x0F);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_7_8L6BIT = (uint8)(Lu16RearCEDistance & 0x3F);

    Lu16FrontDEDistance=LstrFrontDEDistace.u16PDCSignal_DE6;
    Lu16RearDEDistance=LstrRearDEDistace.u16PDCSignal_DE6;
    /**DE**/
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_6H6BIT = (uint8)((Lu16FrontDEDistance>>4)&0x3F);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_6L4BIT = (uint8)(Lu16FrontDEDistance &0x0F);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_7H4BIT = (uint8)((Lu16RearDEDistance>>6) & 0x0F);
    LstrTX_APS_0x238_20_MSG.bit.USS_DE_7L6BIT = (uint8)(Lu16RearDEDistance & 0x3F);

    /* 将填充数据赋值给COM层buff*/  
    for(i=0;i<32;i++) 
    {
       GcComDataBuffer[ID238Idx][i] = LstrTX_APS_0x238_20_MSG.byte[i];
    }

}

CAN_0x284_20_Type LstrTX_APS_0x284_20_MSG; 
static void CanIL_Tx_PAS_PSLInfo1_20ms_284_Msg(const uint8 *pBuff)
{
    uint8 i;
    (void)pBuff;/*消除警告*/    
    uint8 Lu8CRCLen = 32-1;/*报文长度减1*/
    uint8 Lu8CRCResult = 0;
    
    
    /*信号赋值*/
    LstrTX_APS_0x284_20_MSG.bit.PAS_PSLInfo1_20ms_RlngCtr = Gu8NodeALC[ID284Idx];
    /****************************************************************************************/
    /****************************************************************************************/
    /********************************Start Of Signal Assignment******************************/

    PSLOutputinfoType strpsldata;   
    uint16 Lu16DataTemp;
    bool bPSLLockFlg = FALSE;
    
    PSLOutputReadinfo(&strpsldata,0);
    bPSLLockFlg = strpsldata.bPSLWriteLockFlag;
    if(bPSLLockFlg == TRUE)
    {
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot1ID = strpsldata.u8ApaSlotID;
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot1DepthRef = strpsldata.u8SlotdepthRef;
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot1Type = strpsldata.u8ApaSlotType;
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot1Status = strpsldata.u8ApaSlotStatus;

        Lu16DataTemp = strpsldata.dFirstObjWx + 4096;
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot1Obj1XH8bit = (uint8)(Lu16DataTemp >> 5);
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot1Obj1XL5bit = (uint8)(Lu16DataTemp&0x1F);

        Lu16DataTemp = strpsldata.dFirstObjWy + 4096;
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot1Obj1YH3BIT = (uint8)(Lu16DataTemp >> 10);
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot1Obj1YM8BIT = (uint8)(Lu16DataTemp >> 2);
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot1Obj1YL2BIT = (uint8)(Lu16DataTemp&0x03);

        Lu16DataTemp = strpsldata.s16Obj1Alpha + 128;
        Lu16DataTemp = Lu16DataTemp>>1;
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot1Obj1AlphaH4BIT = (uint8)(Lu16DataTemp >> 3);
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot1Obj1AlphaL3BIT = (uint8)(Lu16DataTemp&0x07);
        
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot1Obj1Type = strpsldata.u8FirstObjType;

        Lu16DataTemp = strpsldata.dSecondObjWx + 4096;
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot1Obj2XH5BIT = (uint8)(Lu16DataTemp >> 8);
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot1Obj2XL8BIT = (uint8)(Lu16DataTemp&0xFF);

        Lu16DataTemp = strpsldata.dSecondObjWy + 4096;
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot1Obj2YH8BIT = (uint8)(Lu16DataTemp >> 5);
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot1Obj2YL5BIT = (uint8)(Lu16DataTemp&0x1F);

        Lu16DataTemp = strpsldata.s16Obj2Alpha + 128;
        Lu16DataTemp = Lu16DataTemp>>1;
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot1Obj2AlphaH1BIT = (uint8)(Lu16DataTemp >> 6);
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot1Obj2AlphaL6BIT = (uint8)(Lu16DataTemp&0x3F);
        
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot1Obj2Type = strpsldata.u8SecObjType;

        Lu16DataTemp = strpsldata.u16ApaSlotLength;
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot1LengthH2BIT = (uint8)(Lu16DataTemp >> 9);
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot1LengthM8BIT = (uint8)(Lu16DataTemp >> 1);
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot1LengthL1BIT = (uint8)(Lu16DataTemp&0x01);

        Lu16DataTemp = strpsldata.u16ApaSlotDepth;
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot1DepthH7BIT = (uint8)(Lu16DataTemp >> 3);
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot1DepthL3BIT = (uint8)(Lu16DataTemp&0x07);
    }
    /* 车位编号2  */
    PSLOutputReadinfo(&strpsldata,1);
    bPSLLockFlg = strpsldata.bPSLWriteLockFlag;
    if(bPSLLockFlg == TRUE)
    {
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot2Type = strpsldata.u8ApaSlotType;
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot2Status = strpsldata.u8ApaSlotStatus;
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot2DepthRef = strpsldata.u8SlotdepthRef;

        Lu16DataTemp = strpsldata.u8ApaSlotID;
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot2IDH5BIT = (uint8)(Lu16DataTemp >> 3);
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot2IDL3BIT = (uint8)(Lu16DataTemp&0x07);

        Lu16DataTemp = strpsldata.dFirstObjWx + 4096;
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot2Obj1XH5BIT = (uint8)(Lu16DataTemp >> 8);
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot2Obj1XL8BIT = (uint8)(Lu16DataTemp&0xFF);

        Lu16DataTemp = strpsldata.dFirstObjWy + 4096;
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot2Obj1YH8BIT = (uint8)(Lu16DataTemp >> 5);
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot2Obj1YL5BIT = (uint8)(Lu16DataTemp&0x1F);

        
        Lu16DataTemp = strpsldata.s16Obj1Alpha + 128;
        Lu16DataTemp = Lu16DataTemp>>1;
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot2Obj1AlphaH1BIT = (uint8)(Lu16DataTemp >> 6);
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot2Obj1AlphaL6BIT = (uint8)(Lu16DataTemp&0x3F);
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot2Obj1Type = strpsldata.u8FirstObjType;

        Lu16DataTemp = strpsldata.dSecondObjWx + 4096;
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot2Obj2XH2BIT = (uint8)(Lu16DataTemp >> 11);
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot2Obj2XM8BIT = (uint8)(Lu16DataTemp >> 3);
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot2Obj2XL3BIT = (uint8)(Lu16DataTemp&0x07);

        Lu16DataTemp = strpsldata.dSecondObjWy + 4096;
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot2Obj2YH5BIT = (uint8)(Lu16DataTemp >> 8);
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot2Obj2YL8BIT = (uint8)(Lu16DataTemp&0xFF);

        Lu16DataTemp = strpsldata.s16Obj2Alpha + 128;
        Lu16DataTemp = Lu16DataTemp>>1;
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot2Obj2AlphaH6BIT = (uint8)(Lu16DataTemp >> 1);
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot2Obj2AlphaL1BIT = (uint8)(Lu16DataTemp&0x01);

        LstrTX_APS_0x284_20_MSG.bit.ApaSlot2Obj2Type = strpsldata.u8SecObjType;

        Lu16DataTemp = strpsldata.u16ApaSlotLength;
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot2LengthH7BIT = (uint8)(Lu16DataTemp >> 4);
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot2LengthL4BIT = (uint8)(Lu16DataTemp&0x0F);

        Lu16DataTemp = strpsldata.u16ApaSlotDepth;
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot2DepthH4BIT = (uint8)(Lu16DataTemp >> 6);
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot2DepthL6BIT = (uint8)(Lu16DataTemp&0x3F);
    }
    /**************第3个车位************************************/
    PSLOutputReadinfo(&strpsldata,2);
    bPSLLockFlg = strpsldata.bPSLWriteLockFlag;
    if(bPSLLockFlg == TRUE)
    {
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot3Type = strpsldata.u8ApaSlotType;
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot3Status = strpsldata.u8ApaSlotStatus;
        
        Lu16DataTemp = strpsldata.u8ApaSlotID;
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot3IDH2BIT = (uint8)(Lu16DataTemp >> 6);
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot3IDL6BIT = (uint8)(Lu16DataTemp&0x3f);

        Lu16DataTemp = strpsldata.dFirstObjWx + 4096;
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot3Obj1XH5BIT = (uint8)(Lu16DataTemp >> 8);
        LstrTX_APS_0x284_20_MSG.bit.ApaSlot3Obj1XL8BIT = (uint8)(Lu16DataTemp&0xFF);
    }

    /********************************End Of Signal Assignment********************************/
    /****************************************************************************************/
    /****************************************************************************************/
    Lu8CRCResult  = CanIL_CRCCheckSum_Algorithm(&(LstrTX_APS_0x284_20_MSG.byte[1]),Lu8CRCLen,CAL_CRC_INIT_VALUE,DataId_284,Gu8NodeALC[ID284Idx]);
    LstrTX_APS_0x284_20_MSG.bit.PAS_PSLInfo1_20ms_ChkSm = Lu8CRCResult;



    /* 将填充数据赋值给COM层buff*/  
    for(i=0;i<32;i++) 
    {
       GcComDataBuffer[ID284Idx][i] = LstrTX_APS_0x284_20_MSG.byte[i];
    }
    Gu8NodeALC[ID284Idx]++;
    /*ALC大于14，清零*/
    if(Gu8NodeALC[ID284Idx] > 0x0F)    Gu8NodeALC[ID284Idx] = 0; 

}

CAN_0x285_20_Type LstrTX_APS_0x285_20_MSG; 
static void CanIL_Tx_PAS_PSLInfo2_20ms_285_Msg(const uint8 *pBuff)
{
    uint8 i;
    (void)pBuff;/*消除警告*/    
    uint8 Lu8CRCLen = 32-1;/*报文长度减1*/
    uint8 Lu8CRCResult = 0;

    
    /*信号赋值*/
    LstrTX_APS_0x285_20_MSG.bit.PAS_PSLInfo2_20ms_RlngCtr = Gu8NodeALC[ID285Idx];

    /****************************************************************************************/
    /****************************************************************************************/
    /********************************Start Of Signal Assignment******************************/
    
    /**************第5个车位************************************/
 
    PSLOutputinfoType strpsldata;
    uint16 Lu16DataTemp;
    bool bPSLLockFlg = FALSE;
    
    PSLOutputReadinfo(&strpsldata,4);
    bPSLLockFlg = strpsldata.bPSLWriteLockFlag;
    if(bPSLLockFlg == TRUE)
    {
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot5ID = strpsldata.u8ApaSlotID;
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot5DepthRef = strpsldata.u8SlotdepthRef;
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot5Type = strpsldata.u8ApaSlotType;
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot5Status = strpsldata.u8ApaSlotStatus;
        
        Lu16DataTemp = strpsldata.dFirstObjWx + 4096;
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot5Obj1XH8bit = (uint8)(Lu16DataTemp >> 5);
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot5Obj1XL5bit = (uint8)(Lu16DataTemp & 0x1F);
        
        Lu16DataTemp = strpsldata.dFirstObjWy + 4096;
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot5Obj1YH3BIT = (uint8)(Lu16DataTemp >> 10);
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot5Obj1YM8BIT = (uint8)(Lu16DataTemp >> 2);
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot5Obj1YL2BIT = (uint8)(Lu16DataTemp & 0x03);
        
        Lu16DataTemp = strpsldata.s16Obj1Alpha + 128;
        Lu16DataTemp = Lu16DataTemp>>1;
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot5Obj1AlphaH4BIT = (uint8)(Lu16DataTemp >> 3);
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot5Obj1AlphaL3BIT = (uint8)(Lu16DataTemp & 0x07);

        LstrTX_APS_0x285_20_MSG.bit.ApaSlot5Obj1Type = strpsldata.u8FirstObjType;

        Lu16DataTemp = strpsldata.dSecondObjWx + 4096;
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot5Obj2XH5BIT = (uint8)(Lu16DataTemp >> 8);
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot5Obj2XL8BIT = (uint8)(Lu16DataTemp & 0xFF);
        
        Lu16DataTemp = strpsldata.dSecondObjWy + 4096;
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot5Obj2YH8BIT = (uint8)(Lu16DataTemp >> 5);
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot5Obj2YL5BIT = (uint8)(Lu16DataTemp & 0x1F);
            

        LstrTX_APS_0x285_20_MSG.bit.ApaSlot5Obj2Type = strpsldata.u8SecObjType;

        Lu16DataTemp = strpsldata.u16ApaSlotLength;

        LstrTX_APS_0x285_20_MSG.bit.ApaSlot5LengthH2BIT = (uint8)(Lu16DataTemp >> 9);
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot5LengthM8BIT = (uint8)(Lu16DataTemp >> 1);
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot5LengthL1BIT = (uint8)(Lu16DataTemp & 0x01);
        
        Lu16DataTemp = strpsldata.s16Obj2Alpha + 128;
        Lu16DataTemp = Lu16DataTemp>>1;
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot5Obj2AlphaH1BIT = (uint8)(Lu16DataTemp >> 6);
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot5Obj2AlphaL6BIT = (uint8)(Lu16DataTemp & 0x3F);
        
        Lu16DataTemp = strpsldata.u16ApaSlotDepth;
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot5DepthH7BIT = (uint8)(Lu16DataTemp >> 3);
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot5DepthL3BIT = (uint8)(Lu16DataTemp & 0x7);
    }
    /**************第6个车位************************************/
    PSLOutputReadinfo(&strpsldata,5);
    bPSLLockFlg = strpsldata.bPSLWriteLockFlag;
    if(bPSLLockFlg == TRUE)
    {
        Lu16DataTemp = strpsldata.u8ApaSlotID;
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot6IDH5BIT = (uint8)(Lu16DataTemp >> 3);
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot6IDL3BIT = (uint8)(Lu16DataTemp & 0x07);

        LstrTX_APS_0x285_20_MSG.bit.ApaSlot6Type = strpsldata.u8ApaSlotType;
        
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot6Status = strpsldata.u8ApaSlotStatus;
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot6DepthRef = strpsldata.u8SlotdepthRef;
        
        Lu16DataTemp = strpsldata.dFirstObjWx + 4096;
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot6Obj1XH5BIT = (uint8)(Lu16DataTemp >> 8);
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot6Obj1XL8BIT =  (uint8)(Lu16DataTemp & 0xFF);
        
        Lu16DataTemp = strpsldata.dFirstObjWy + 4096;
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot6Obj1YH8BIT = (uint8)(Lu16DataTemp >> 5);
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot6Obj1YL5BIT = (uint8)(Lu16DataTemp & 0X1F);
        
        Lu16DataTemp = strpsldata.s16Obj1Alpha + 128;
        Lu16DataTemp = Lu16DataTemp>>1;
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot6Obj1AlphaH1BIT = (uint8)(Lu16DataTemp >> 6);
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot6Obj1AlphaL6BIT = (uint8)(Lu16DataTemp & 0x3F);

        LstrTX_APS_0x285_20_MSG.bit.ApaSlot6Obj1Type =strpsldata.u8FirstObjType;
        
        Lu16DataTemp = strpsldata.dSecondObjWx + 4096;
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot6Obj2XH2BIT = (uint8)(Lu16DataTemp >> 11);
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot6Obj2XM8BIT = (uint8)(Lu16DataTemp >> 3);
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot6Obj2XL3BIT = (uint8)(Lu16DataTemp & 0x07);
        
        Lu16DataTemp = strpsldata.dSecondObjWy + 4096;
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot6Obj2YH5BIT = (uint8)(Lu16DataTemp >> 8);
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot6Obj2YL8BIT = (uint8)(Lu16DataTemp & 0xFF);
        
        Lu16DataTemp = strpsldata.s16Obj2Alpha + 128;
        Lu16DataTemp = Lu16DataTemp>>1;
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot6Obj2AlphaH6BIT = (uint8)(Lu16DataTemp >> 1);
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot6Obj2AlphaL1BIT = (uint8)(Lu16DataTemp & 0x01); 
        
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot6Obj2Type = strpsldata.u8SecObjType;
        
        Lu16DataTemp = strpsldata.u16ApaSlotLength;
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot6LengthH7BIT = (uint8)(Lu16DataTemp >> 4);
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot6LengthL4BIT = (uint8)(Lu16DataTemp & 0x0F);

        Lu16DataTemp = strpsldata.u16ApaSlotDepth;
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot6DepthH4BIT = (uint8)(Lu16DataTemp >> 6);
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot6DepthL6BIT = (uint8)(Lu16DataTemp & 0x3F); 
    }
    /**************第7个车位************************************/ 
    PSLOutputReadinfo(&strpsldata,6);
    bPSLLockFlg = strpsldata.bPSLWriteLockFlag;
    if(bPSLLockFlg == TRUE)
    {
        Lu16DataTemp = strpsldata.u8ApaSlotID;
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot7IDH2BIT = (uint8)(Lu16DataTemp >> 6);
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot7IDL6BIT = (uint8)(Lu16DataTemp & 0x3F); 
        
        Lu16DataTemp = strpsldata.u8ApaSlotStatus;
        LstrTX_APS_0x285_20_MSG.bit.ApaSlot7Status = Lu16DataTemp;
    }
    /********************************End Of Signal Assignment********************************/
    /****************************************************************************************/
    /****************************************************************************************/
    Lu8CRCResult  = CanIL_CRCCheckSum_Algorithm(&(LstrTX_APS_0x285_20_MSG.byte[1]),Lu8CRCLen,CAL_CRC_INIT_VALUE,DataId_285,Gu8NodeALC[ID285Idx]);
    LstrTX_APS_0x285_20_MSG.bit.PAS_PSLInfo2_20ms_ChkSm = Lu8CRCResult;

    /* 将填充数据赋值给COM层buff*/  
    for(i=0;i<32;i++) 
    {
       GcComDataBuffer[ID285Idx][i] = LstrTX_APS_0x285_20_MSG.byte[i];
    }
    Gu8NodeALC[ID285Idx]++;
    /*ALC大于14，清零*/
    if(Gu8NodeALC[ID285Idx] > 0x0F)    Gu8NodeALC[ID285Idx] = 0;

}

CAN_0x286_20_Type LstrTX_APS_0x286_20_MSG; 
static void CanIL_Tx_PAS_PSLInfo3_20ms_286_Msg(const uint8 *pBuff)
{
    uint8 i;
    (void)pBuff;/*消除警告*/    
    uint8 Lu8CRCLen = 32-1;/*报文长度减1*/
    uint8 Lu8CRCResult = 0;


    /*信号赋值*/
    LstrTX_APS_0x286_20_MSG.bit.SigGroup_0x286_RlngCtr = Gu8NodeALC[ID286Idx];

    /****************************************************************************************/
    /****************************************************************************************/
    /********************************Start Of Signal Assignment******************************/
    //uint32  u32PDCSignalTimestamp=0;
    //PdcSignal_MapObjInfoType LstrMapObjInfo;
    ///**************第3个车位************************************/

    PSLOutputinfoType strpsldata;
    uint16 Lu16DataTemp;
    bool bPSLLockFlg = FALSE;
    
    PSLOutputReadinfo(&strpsldata,2);
    bPSLLockFlg = strpsldata.bPSLWriteLockFlag;
    if(bPSLLockFlg == TRUE)
    {
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot3Obj2Type = strpsldata.u8SecObjType;
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot3Obj1Type = strpsldata.u8FirstObjType;

        LstrTX_APS_0x286_20_MSG.bit.ApaSlot3DepthRef = strpsldata.u8SlotdepthRef;
        
        Lu16DataTemp = strpsldata.dFirstObjWy + 4096;
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot3Obj1Y_High5bit = (uint8)(Lu16DataTemp >> 8);
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot3Obj1Y_Low8bit = (uint8)(Lu16DataTemp & 0xFF);
        
        Lu16DataTemp = strpsldata.dSecondObjWx + 4096;
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot3Obj2X_HH1bit = (uint8)(Lu16DataTemp >> 12);
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot3Obj2X_Mid8bit = (uint8)(Lu16DataTemp >> 4);
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot3Obj2X_Low4bit = (uint8)(Lu16DataTemp & 0x0F);
        
        Lu16DataTemp = strpsldata.dSecondObjWy + 4096;
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot3Obj2Y_High4bit = (uint8)(Lu16DataTemp >> 9);
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot3Obj2Y_Mid8BIT = (uint8)(Lu16DataTemp >> 1);
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot3Obj2Y_Low1bit = (uint8)(Lu16DataTemp & 0x01);
        Lu16DataTemp = strpsldata.s16Obj1Alpha + 128;
        Lu16DataTemp = Lu16DataTemp>>1;
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot3Obj1Alpha = (uint8)Lu16DataTemp;
        
        Lu16DataTemp = strpsldata.s16Obj2Alpha + 128;
        Lu16DataTemp = Lu16DataTemp>>1;
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot3Obj2Alpha = (uint8)Lu16DataTemp;
        
        Lu16DataTemp = strpsldata.u16ApaSlotLength;
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot3Length_H8bit = (uint8)(Lu16DataTemp >> 3);
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot3Length_L3bit = (uint8)(Lu16DataTemp & 0x7);
        
        Lu16DataTemp = strpsldata.u16ApaSlotDepth;
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot3Depth_H5bit = (uint8)(Lu16DataTemp >> 5);
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot3Depth_L5bit = (uint8)(Lu16DataTemp & 0x1F); 
    }
    /**************第4个车位************************************/
    PSLOutputReadinfo(&strpsldata,3);
    bPSLLockFlg = strpsldata.bPSLWriteLockFlag;
    if(bPSLLockFlg == TRUE)
    {
        Lu16DataTemp = strpsldata.u8ApaSlotID;
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot4ID_H3bit = (uint8)(Lu16DataTemp >> 5);
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot4ID_L5bit = (uint8)(Lu16DataTemp & 0x1F);
        
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot4DepthRef = strpsldata.u8SlotdepthRef;

        LstrTX_APS_0x286_20_MSG.bit.ApaSlot4Status = strpsldata.u8ApaSlotStatus;
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot4Type = strpsldata.u8ApaSlotType;
        
        Lu16DataTemp = strpsldata.dFirstObjWx + 4096;
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot4Obj1X_H3bit = (uint8)(Lu16DataTemp >> 10);
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot4Obj1X_Mid8bit = (uint8)(Lu16DataTemp >> 2);
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot4Obj1X_Low2bit = (uint8)(Lu16DataTemp & 0x03);
        
        Lu16DataTemp = strpsldata.dFirstObjWy + 4096;
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot4Obj1Y_H6bit = (uint8)(Lu16DataTemp >> 7);
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot4Obj1Y_L7bit = (uint8)(Lu16DataTemp &0x7F);

        LstrTX_APS_0x286_20_MSG.bit.ApaSlot4Obj1Type = strpsldata.u8FirstObjType;
        
        Lu16DataTemp = strpsldata.s16Obj1Alpha + 128;
        Lu16DataTemp =Lu16DataTemp >> 1;
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot4Obj1Alpha_H1bit = (uint8)(Lu16DataTemp >> 6);
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot4Obj1Alpha_L6bit = (uint8)(Lu16DataTemp & 0x3F);
        
        Lu16DataTemp = strpsldata.dSecondObjWx + 4096;
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot4Obj2X_H8bit = (uint8)(Lu16DataTemp >> 5);
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot4Obj2X_L5bit = (uint8)(Lu16DataTemp & 0x1F);
        
        Lu16DataTemp = strpsldata.dSecondObjWy + 4096;
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot4Obj2Y_H3bit = (uint8)(Lu16DataTemp >> 10);
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot4Obj2Y_Mid8bit = (uint8)(Lu16DataTemp >> 2);
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot4Obj2Y_L2bit = (uint8)(Lu16DataTemp & 0x3);
        
        Lu16DataTemp = strpsldata.u16ApaSlotLength ;
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot4Length_H4bit =  (uint8)(Lu16DataTemp >> 7);        
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot4LengthL7bit  = (uint8)(Lu16DataTemp & 0x7F);
        
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot4Obj2Type = strpsldata.u8SecObjType;
        
        Lu16DataTemp = strpsldata.s16Obj2Alpha + 128;
        Lu16DataTemp =Lu16DataTemp >> 1;
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot4Obj2Alpha_H1bit = (uint8)(Lu16DataTemp >> 6);
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot4Obj2Alpha_L6bit = (uint8)(Lu16DataTemp & 0x3F);
        
        Lu16DataTemp = strpsldata.u16ApaSlotDepth;
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot4Depth_H2bit = (uint8)(Lu16DataTemp >> 8);
        LstrTX_APS_0x286_20_MSG.bit.ApaSlot4Depth_L8bit = (uint8)(Lu16DataTemp &0xFF);
    }
//    PdcSignal_ReadMapObj(&LstrMapObjInfo,18);
//    u32PDCSignalTimestamp = LstrMapObjInfo.u32PDCSignal_PAS_MapObjTimestamp;
//
//    LstrTX_APS_0x286_20_MSG.bit.PAS_MapObj18_Timestamp_HHH8BIT = (uint8)(u32PDCSignalTimestamp >> 19);
//    LstrTX_APS_0x286_20_MSG.bit.PAS_MapObj18_Timestamp_HHL8BIT = (uint8)(u32PDCSignalTimestamp >> 11);
//    LstrTX_APS_0x286_20_MSG.bit.PAS_MapObj18_Timestamp_HLL8BIT = (uint8)(u32PDCSignalTimestamp >> 3);
//    LstrTX_APS_0x286_20_MSG.bit.PAS_MapObj18_Timestamp_LLL3BIT = (uint8)(u32PDCSignalTimestamp &0x07);
//    
//    PdcSignal_ReadMapObj(&LstrMapObjInfo,19);
//    u32PDCSignalTimestamp = LstrMapObjInfo.u32PDCSignal_PAS_MapObjTimestamp;
//    LstrTX_APS_0x286_20_MSG.bit.PAS_MapObj19_Timestamp_HHH5BIT = (uint8)(u32PDCSignalTimestamp >> 22);
//    LstrTX_APS_0x286_20_MSG.bit.PAS_MapObj19_Timestamp_HHL8BIT = (uint8)(u32PDCSignalTimestamp >> 14);
//    LstrTX_APS_0x286_20_MSG.bit.PAS_MapObj19_Timestamp_HLL8BIT = (uint8)(u32PDCSignalTimestamp >> 6);
//    LstrTX_APS_0x286_20_MSG.bit.PAS_MapObj19_Timestamp_LLL6BIT = (uint8)(u32PDCSignalTimestamp &0x3F);

    /********************************End Of Signal Assignment********************************/
    /****************************************************************************************/
    /****************************************************************************************/
    Lu8CRCResult  = CanIL_CRCCheckSum_Algorithm(&(LstrTX_APS_0x286_20_MSG.byte[1]),Lu8CRCLen,CAL_CRC_INIT_VALUE,DataId_286,Gu8NodeALC[ID286Idx]);
    LstrTX_APS_0x286_20_MSG.bit.SigGroup_0x286_ChkSm = Lu8CRCResult;

    /* 将填充数据赋值给COM层buff*/  
    for(i=0;i<32;i++) 
    {
       GcComDataBuffer[ID286Idx][i] = LstrTX_APS_0x286_20_MSG.byte[i];
    }
    Gu8NodeALC[ID286Idx]++;
    /*ALC大于14，清零*/
    if(Gu8NodeALC[ID286Idx] > 0x0F)    Gu8NodeALC[ID286Idx] = 0;
}

CAN_0x287_20_Type LstrTX_APS_0x287_20_MSG; 
static void CanIL_Tx_PAS_PSLInfo4_20ms_287_Msg(const uint8 *pBuff)
{
    uint8 i;
    (void)pBuff;/*消除警告*/    
    uint8 Lu8CRCLen = 32-1;/*报文长度减1*/
    uint8 Lu8CRCResult = 0;


    /*信号赋值*/
    LstrTX_APS_0x287_20_MSG.bit.SigGroup_0x287_RlngCtr = Gu8NodeALC[ID287Idx];

    /****************************************************************************************/
    /****************************************************************************************/
    /********************************Start Of Signal Assignment******************************/
    
    /**************第7个车位************************************/
    PSLOutputinfoType strpsldata;
    uint16 Lu16DataTemp;
    bool bPSLLockFlg = FALSE;
    
    PSLOutputReadinfo(&strpsldata,6);
    bPSLLockFlg = strpsldata.bPSLWriteLockFlag;
    if(bPSLLockFlg == TRUE)
    {
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot7DepthRef = strpsldata.u8SlotdepthRef;
        
        Lu16DataTemp  = strpsldata.dFirstObjWx + 4096 ;
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot7Obj1X_H8BIT = (uint8)(Lu16DataTemp >> 5);
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot7Obj1X_L5BIT = (uint8)(Lu16DataTemp & 0x1F);

        LstrTX_APS_0x287_20_MSG.bit.ApaSlot7Type = strpsldata.u8ApaSlotType;
        
        Lu16DataTemp  = strpsldata.dFirstObjWy + 4096 ;
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot7Obj1Y_H8BIT = (uint8)(Lu16DataTemp >> 5) ;
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot7Obj1Y_L5BIT = (uint8)(Lu16DataTemp & 0x1F);
        
        Lu16DataTemp  = strpsldata.s16Obj1Alpha+128;
        Lu16DataTemp  = Lu16DataTemp >> 1;
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot7Obj1Alpha_H3BIT = (uint8)(Lu16DataTemp >> 4) ; 
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot7Obj1Alpha_L4BIT = (uint8)(Lu16DataTemp & 0x0F) ;
        
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot7Obj1Type = strpsldata.u8FirstObjType;
        
        Lu16DataTemp  = strpsldata.dSecondObjWx + 4096;
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot7Obj2X_H2BIT = (uint8)(Lu16DataTemp >> 11) ; 
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot7Obj2X_M8BIT = (uint8)(Lu16DataTemp >> 3) ;
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot7Obj2X_L3BIT = (uint8)(Lu16DataTemp &0X07) ;
        
        Lu16DataTemp  = strpsldata.dSecondObjWy + 4096;
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot7Obj2Y_H5BIT = (uint8)(Lu16DataTemp >> 8);
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot7Obj2Y_L8BIT = (uint8)(Lu16DataTemp & 0xFF);

        Lu16DataTemp  = strpsldata.u8SecObjType;
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot7Obj2Type_H1BIT = (uint8)(Lu16DataTemp >> 1); 
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot7Obj2Type_L1BIT = (uint8)(Lu16DataTemp & 0x1);
        
        Lu16DataTemp  = strpsldata.s16Obj2Alpha+128;
        Lu16DataTemp  = Lu16DataTemp >> 1;
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot7Obj2Alpha = Lu16DataTemp;
        
        Lu16DataTemp  = strpsldata.u16ApaSlotLength;
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot7Length_H7BIT = (uint8)(Lu16DataTemp >> 4);
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot7Length_L4BIT = (uint8)(Lu16DataTemp & 0x0F);
        
        Lu16DataTemp  = strpsldata.u16ApaSlotDepth;
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot7Depth_H4BIT =  (uint8)(Lu16DataTemp >> 6);
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot7Depth_L6BIT =  (uint8)(Lu16DataTemp & 0x3F);
    }

    /**************第8个车位************************************/
    PSLOutputReadinfo(&strpsldata,7);
    bPSLLockFlg = strpsldata.bPSLWriteLockFlag;
    if(bPSLLockFlg == TRUE)
    {
        Lu16DataTemp  = strpsldata.u8ApaSlotID;
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot8ID_H2BIT = (uint8)(Lu16DataTemp >> 6);
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot8ID_L6BIT = (uint8)(Lu16DataTemp & 0x3F);
        
        Lu16DataTemp  = strpsldata.u8SlotdepthRef;
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot8DepthRef_H2BIT = (uint8)(Lu16DataTemp >> 1);
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot8DepthRef_L1BIT = (uint8)(Lu16DataTemp & 0x01);

        LstrTX_APS_0x287_20_MSG.bit.ApaSlot8Status = strpsldata.u8ApaSlotStatus;
        
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot8Type = strpsldata.u8ApaSlotType;
        
        Lu16DataTemp  = strpsldata.dFirstObjWx + 4096;
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot8Obj1X_H2BIT = (uint8)(Lu16DataTemp >> 11);
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot8Obj1X_Mid8BIT = (uint8)(Lu16DataTemp >> 3);
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot8Obj1X_L3BIT = (uint8)(Lu16DataTemp &0x07);
        
        Lu16DataTemp  = strpsldata.dFirstObjWy + 4096;
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot8Obj1Y_H5BIT = (uint8)(Lu16DataTemp >> 8);
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot8Obj1Y_L8BIT = (uint8)(Lu16DataTemp &0xFF);
        
        Lu16DataTemp  = strpsldata.u8FirstObjType;
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot8Obj1Type_H1BIT = (uint8)(Lu16DataTemp >> 1);
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot8Obj1Type_L1BIT = (uint8)(Lu16DataTemp &0x01);
        
        Lu16DataTemp  = strpsldata.s16Obj1Alpha + 128;
        Lu16DataTemp  = Lu16DataTemp >> 1;
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot8Obj1Alpha = (uint8)Lu16DataTemp;
        
        Lu16DataTemp  = strpsldata.dSecondObjWx + 4096;
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot8Obj2X_H7BIT = (uint8)(Lu16DataTemp >> 6);
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot8Obj2X_L6BIT= (uint8)(Lu16DataTemp &0x3F);
        
        Lu16DataTemp  = strpsldata.dSecondObjWy + 4096;
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot8Obj2Y_H2BIT = (uint8)(Lu16DataTemp >> 11);
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot8Obj2Y_MID8BIT = (uint8)(Lu16DataTemp >> 3);
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot8Obj2Y_L3BIT = (uint8)(Lu16DataTemp & 0x07);
        
        Lu16DataTemp  = strpsldata.u16ApaSlotLength;
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot8Length_H3BIT = (uint8)(Lu16DataTemp >> 8);
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot8Length_L8BIT = (uint8)(Lu16DataTemp &0xFF);

        LstrTX_APS_0x287_20_MSG.bit.ApaSlot8Obj2Type = strpsldata.u8SecObjType;
        
        Lu16DataTemp  = strpsldata.s16Obj2Alpha + 128;
        Lu16DataTemp  = Lu16DataTemp >> 1;
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot8Obj2Alpha = Lu16DataTemp;
        
        Lu16DataTemp  = strpsldata.u16ApaSlotDepth;
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot8Depth_H1BIT = (uint8)(Lu16DataTemp >> 9);
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot8Depth_MID8BIT = (uint8)(Lu16DataTemp >> 1);
        LstrTX_APS_0x287_20_MSG.bit.ApaSlot8Depth_L1BIT = (uint8)(Lu16DataTemp & 0x01);
    }
    /********************************End Of Signal Assignment********************************/
    /****************************************************************************************/
    /****************************************************************************************/
    Lu8CRCResult  = CanIL_CRCCheckSum_Algorithm(&(LstrTX_APS_0x287_20_MSG.byte[1]),Lu8CRCLen,CAL_CRC_INIT_VALUE,DataId_287,Gu8NodeALC[ID287Idx]);
    LstrTX_APS_0x287_20_MSG.bit.SigGroup_0x287_ChkSm = Lu8CRCResult;

    /* 将填充数据赋值给COM层buff*/  
    for(i=0;i<32;i++) 
    {
       GcComDataBuffer[ID287Idx][i] = LstrTX_APS_0x287_20_MSG.byte[i];
    }
    Gu8NodeALC[ID287Idx]++;
    /*ALC大于14，清零*/
    if(Gu8NodeALC[ID287Idx] > 0x0F)    Gu8NodeALC[ID287Idx] = 0;

}

CAN_0x2A4_20_Type LstrTX_APS_0x2A4_20_MSG; 
static void CanIL_Tx_PAS_Timestamp_20ms_2A4_Msg(const uint8 *pBuff)
{
    uint8 i;
    (void)pBuff;/*消除警告*/    
    uint8 Lu8CRCLen = 32-1;/*报文长度减1*/
    uint8 Lu8CRCResult = 0;
    /*信号赋值*/
    LstrTX_APS_0x2A4_20_MSG.bit.SigGroup_0x2A4_RlngCtr = Gu8NodeALC[ID2A4Idx];
    /****************************************************************************************/
    /****************************************************************************************/
    /********************************Start Of Signal Assignment******************************/
    PSLOutputinfoType LstrAPASlotOutputinfo;
    uint32 Lu32DataTemp;
    PSLOutputReadinfo(&LstrAPASlotOutputinfo,0);
    Lu32DataTemp = LstrAPASlotOutputinfo.u32slotsynctime;
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot1_Timestamp_HHH8BIT = (uint8)(Lu32DataTemp >> 19);
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot1_Timestamp_HHL8BIT = (uint8)(Lu32DataTemp >> 11);
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot1_Timestamp_HLL8BIT = (uint8)(Lu32DataTemp >> 3);
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot1_Timestamp_LLL3BIT = (uint8)(Lu32DataTemp &0x07);
    
    PSLOutputReadinfo(&LstrAPASlotOutputinfo,1);
    Lu32DataTemp = LstrAPASlotOutputinfo.u32slotsynctime;
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot2_Timestamp_HHH5BIT = (uint8)(Lu32DataTemp >> 22);
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot2_Timestamp_HHL8BIT = (uint8)(Lu32DataTemp >> 14);
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot2_Timestamp_HLL8BIT = (uint8)(Lu32DataTemp >> 6);
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot2_Timestamp_LLL6BIT = (uint8)(Lu32DataTemp &0x3F);
    
    PSLOutputReadinfo(&LstrAPASlotOutputinfo,2);
    Lu32DataTemp = LstrAPASlotOutputinfo.u32slotsynctime;
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot3_Timestamp_HHHH2BIT = (uint8)(Lu32DataTemp >> 25);
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot3_Timestamp_HHHL8BIT = (uint8)(Lu32DataTemp >> 17);
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot3_Timestamp_HHLL8BIT = (uint8)(Lu32DataTemp >> 9);
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot3_Timestamp_HLLL8BIT = (uint8)(Lu32DataTemp >> 1);
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot3_Timestamp_LLLL1BIT = (uint8)(Lu32DataTemp &0X01);
    
    PSLOutputReadinfo(&LstrAPASlotOutputinfo,3);
    Lu32DataTemp = LstrAPASlotOutputinfo.u32slotsynctime;
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot4_Timestamp__HHH7BIT = (uint8)(Lu32DataTemp >> 20);
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot4_Timestamp_HHL8BIT = (uint8)(Lu32DataTemp >> 12);
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot4_Timestamp_HLL8BIT = (uint8)(Lu32DataTemp >> 4);
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot4_Timestamp_LLL4BIT = (uint8)(Lu32DataTemp &0x0F);
    
    PSLOutputReadinfo(&LstrAPASlotOutputinfo,4);
    Lu32DataTemp = LstrAPASlotOutputinfo.u32slotsynctime;
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot5_Timestamp__HHH4BIT = (uint8)(Lu32DataTemp >> 23);
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot5_Timestamp_HHL8BIT = (uint8)(Lu32DataTemp >> 15);
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot5_Timestamp_HLL8BIT = (uint8)(Lu32DataTemp >> 7);
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot5_Timestamp_LLL7BIT = (uint8)(Lu32DataTemp &0x7F);
    
    PSLOutputReadinfo(&LstrAPASlotOutputinfo,5);
    Lu32DataTemp = LstrAPASlotOutputinfo.u32slotsynctime;
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot6_Timestamp_HHHL8BIT = (uint8)(Lu32DataTemp >> 18);
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot6_Timestamp_HHLL8BIT = (uint8)(Lu32DataTemp >> 10);
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot6_Timestamp_HLLL8BIT = (uint8)(Lu32DataTemp >> 3);
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot6_Timestamp_LLLL2BIT = (uint8)(Lu32DataTemp &0X03);
    PSLOutputReadinfo(&LstrAPASlotOutputinfo,6);
    Lu32DataTemp = LstrAPASlotOutputinfo.u32slotsynctime;
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot7_Timestamp_HHH6BIT = (uint8)(Lu32DataTemp >> 21);
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot7_Timestamp_HHL8BIT = (uint8)(Lu32DataTemp >> 13);
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot7_Timestamp_HLL8BIT = (uint8)(Lu32DataTemp >> 5);
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot7_Timestamp_LLL5BIT = (uint8)(Lu32DataTemp >> 0x1F);
    
    PSLOutputReadinfo(&LstrAPASlotOutputinfo,7);
    Lu32DataTemp = LstrAPASlotOutputinfo.u32slotsynctime;
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot8_Timestamp_HHH3BIT = (uint8)(Lu32DataTemp >> 24);
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot8_Timestamp_HHL8BIT = (uint8)(Lu32DataTemp >> 16);
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot8_Timestamp_HLL8BIT = (uint8)(Lu32DataTemp >> 8);
    LstrTX_APS_0x2A4_20_MSG.bit.PAS_PSL_Slot8_Timestamp_LLL8BIT = (uint8)(Lu32DataTemp &0xFF);

    /********************************End Of Signal Assignment********************************/
    /****************************************************************************************/
    /****************************************************************************************/
    Lu8CRCResult  = CanIL_CRCCheckSum_Algorithm(&(LstrTX_APS_0x2A4_20_MSG.byte[1]),Lu8CRCLen,CAL_CRC_INIT_VALUE,DataId_2A4,Gu8NodeALC[ID2A4Idx]);
    LstrTX_APS_0x2A4_20_MSG.bit.SigGroup_0x2A4_ChkSm = Lu8CRCResult;
    /* 将填充数据赋值给COM层buff*/  
    for(i=0;i<32;i++) 
    {
       GcComDataBuffer[ID2A4Idx][i] = LstrTX_APS_0x2A4_20_MSG.byte[i];
    }
    Gu8NodeALC[ID2A4Idx]++;
    /*ALC大于14，清零*/
    if(Gu8NodeALC[ID2A4Idx] > 0x0F)    Gu8NodeALC[ID2A4Idx] = 0;

}
CAN_0x2A5_20_Type LstrTX_APS_0x2A5_20_MSG; 
static void CanIL_Tx_PAS_TmspMapObj1_20ms_2A5_Msg(const uint8 *pBuff)
{
    uint8 i;
    (void)pBuff;/*消除警告*/    
    /*信号赋值*/
    uint8 Lu8CRCLen = 32-1;/*报文长度减1*/
    uint8 Lu8CRCResult = 0;
    /*信号赋值*/
    LstrTX_APS_0x2A5_20_MSG.bit.SigGroup_0x2A5_RlngCtr = Gu8NodeALC[ID2A5Idx];
    
    uint32  u32PDCSignalTimestamp=0;
    PdcSignal_MapObjInfoType LstrMapObjInfo;
    PdcSignal_ReadMapObj(&LstrMapObjInfo,0);
    u32PDCSignalTimestamp = LstrMapObjInfo.u32PDCSignal_PAS_MapObjTimestamp;
    LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj00_Timestamp_HHH8BIT=(uint8)((u32PDCSignalTimestamp>>19)&0xff);
    LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj00_Timestamp_HHL8BIT=(uint8)((u32PDCSignalTimestamp>>11)&0xFF);
    LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj00_Timestamp_HLL8BIT=(uint8)((u32PDCSignalTimestamp>>3)&0xFF);
    LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj00_Timestamp_LLL3BIT=(uint8)(u32PDCSignalTimestamp&0x07);
	
    PdcSignal_ReadMapObj(&LstrMapObjInfo,1);
    u32PDCSignalTimestamp = LstrMapObjInfo.u32PDCSignal_PAS_MapObjTimestamp;
    LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj01_Timestamp_HHH5BIT=(uint8)((u32PDCSignalTimestamp>>22)&0x1f);
    LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj01_Timestamp_HHL8BIT=(uint8)((u32PDCSignalTimestamp>>14)&0xFF);
    LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj01_Timestamp_HLL8BIT=(uint8)((u32PDCSignalTimestamp>>6)&0xFF);
    LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj01_Timestamp_LLL6BIT=(uint8)(u32PDCSignalTimestamp&0x3f);

	PdcSignal_ReadMapObj(&LstrMapObjInfo,2);
    u32PDCSignalTimestamp = LstrMapObjInfo.u32PDCSignal_PAS_MapObjTimestamp;
	
	LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj02_Timestamp_HHHH2BIT=(uint8)((u32PDCSignalTimestamp>>25)&0x03);
    LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj02_Timestamp_HHHL8BIT=(uint8)((u32PDCSignalTimestamp>>17)&0xff);
    LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj02_Timestamp_HHLL8BIT=(uint8)((u32PDCSignalTimestamp>>9)&0xff);
    LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj02_Timestamp_HLLL8BIT=(uint8)((u32PDCSignalTimestamp>>1)&0xff);
    LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj02_Timestamp_LLLL1BIT=(uint8)(u32PDCSignalTimestamp&0x01);

	PdcSignal_ReadMapObj(&LstrMapObjInfo,3);
    u32PDCSignalTimestamp = LstrMapObjInfo.u32PDCSignal_PAS_MapObjTimestamp;
	
	LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj03_Timestamp__HHH7BIT=(uint8)((u32PDCSignalTimestamp>>20)&0x7f);
    LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj03_Timestamp_HHL8BIT=(uint8)((u32PDCSignalTimestamp>>12)&0xff);
    LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj03_Timestamp_HLL8BIT=(uint8)((u32PDCSignalTimestamp>>4)&0xff);
    LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj03_Timestamp_LLL4BIT=(uint8)((u32PDCSignalTimestamp)&0x0f);

	PdcSignal_ReadMapObj(&LstrMapObjInfo,4);
    u32PDCSignalTimestamp = LstrMapObjInfo.u32PDCSignal_PAS_MapObjTimestamp;
	
	LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj04_Timestamp__HHH4BIT=(uint8)((u32PDCSignalTimestamp>>23)&0x0f);
    LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj04_Timestamp_HHL8BIT=(uint8)((u32PDCSignalTimestamp>>15)&0xff);
    LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj04_Timestamp_HLL8BIT=(uint8)((u32PDCSignalTimestamp>>7)&0xff);
    LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj04_Timestamp_LLL7BIT=(uint8)((u32PDCSignalTimestamp)&0x7f);

	PdcSignal_ReadMapObj(&LstrMapObjInfo,5);
    u32PDCSignalTimestamp = LstrMapObjInfo.u32PDCSignal_PAS_MapObjTimestamp;
	
	LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj05_Timestamp_HHHH1BIT=(uint8)((u32PDCSignalTimestamp>>26)&0x01);
    LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj05_Timestamp_HHHL8BIT=(uint8)((u32PDCSignalTimestamp>>18)&0xff);
    LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj05_Timestamp_HHLL8BIT=(uint8)((u32PDCSignalTimestamp>>10)&0xff);
    LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj05_Timestamp_HLLL8BIT=(uint8)((u32PDCSignalTimestamp>>2)&0xff);
    LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj05_Timestamp_LLLL2BIT=(uint8)((u32PDCSignalTimestamp)&0x03);

	PdcSignal_ReadMapObj(&LstrMapObjInfo,6);
    u32PDCSignalTimestamp = LstrMapObjInfo.u32PDCSignal_PAS_MapObjTimestamp;
	
	LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj06_Timestamp_HHH6BIT=(uint8)((u32PDCSignalTimestamp>>21)&0x3f);
    LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj06_Timestamp_HHL8BIT=(uint8)((u32PDCSignalTimestamp>>13)&0xff);
    LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj06_Timestamp_HLL8BIT=(uint8)((u32PDCSignalTimestamp>>5)&0xff);
    LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj06_Timestamp_LLL5BIT=(uint8)((u32PDCSignalTimestamp)&0x1f);

	PdcSignal_ReadMapObj(&LstrMapObjInfo,7);
    u32PDCSignalTimestamp = LstrMapObjInfo.u32PDCSignal_PAS_MapObjTimestamp;
	
	LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj07_Timestamp_HHH3BIT=(uint8)((u32PDCSignalTimestamp>>24)&0x07);
    LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj07_Timestamp_HHL8BIT=(uint8)((u32PDCSignalTimestamp>>16)&0xff);
    LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj07_Timestamp_HLL8BIT=(uint8)((u32PDCSignalTimestamp>>8)&0xff);
    LstrTX_APS_0x2A5_20_MSG.bit.PAS_MapObj07_Timestamp_LLL8BIT=(uint8)((u32PDCSignalTimestamp)&0xff);
    /********************************End Of Signal Assignment********************************/
    /****************************************************************************************/
    /****************************************************************************************/
    Lu8CRCResult  = CanIL_CRCCheckSum_Algorithm(&(LstrTX_APS_0x2A5_20_MSG.byte[1]),Lu8CRCLen,CAL_CRC_INIT_VALUE,DataId_2A5,Gu8NodeALC[ID2A5Idx]);
    LstrTX_APS_0x2A5_20_MSG.bit.SigGroup_0x2A5_ChkSm = Lu8CRCResult;

    /* 将填充数据赋值给COM层buff*/  
    for(i=0;i<32;i++) 
    {
       GcComDataBuffer[ID2A5Idx][i] = LstrTX_APS_0x2A5_20_MSG.byte[i];
    }
    Gu8NodeALC[ID2A5Idx]++;
    /*ALC大于14，清零*/
    if(Gu8NodeALC[ID2A5Idx] > 0x0F)    Gu8NodeALC[ID2A5Idx] = 0;

}
CAN_0x2B4_20_Type LstrTX_APS_0x2B4_20_MSG; 
static void CanIL_Tx_PAS_TmspMapObj2_20ms_2B4_Msg(const uint8 *pBuff)
{
    uint8 i;
    (void)pBuff;/*消除警告*/    
    uint8 Lu8CRCLen = 32-1;/*报文长度减1*/
    uint8 Lu8CRCResult = 0;
    /*信号赋值*/
    LstrTX_APS_0x2B4_20_MSG.bit.SigGroup_0x2B4_RlngCtr = Gu8NodeALC[ID2B4Idx];
    /********************************Start Of Signal Assignment********************************/
    /****************************************************************************************/
    /****************************************************************************************/

	uint32  u32PDCSignalTimestamp=0;
	PdcSignal_MapObjInfoType LstrMapObjInfo;
	PdcSignal_ReadMapObj(&LstrMapObjInfo,8);
    u32PDCSignalTimestamp = LstrMapObjInfo.u32PDCSignal_PAS_MapObjTimestamp;
    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj08_Timestamp_HHH8BIT=(uint8)((u32PDCSignalTimestamp>>19)&0xff);
    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj08_Timestamp_HHL8BIT=(uint8)((u32PDCSignalTimestamp>>11)&0xFF);
    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj08_Timestamp_HLL8BIT=(uint8)((u32PDCSignalTimestamp>>3)&0xFF);
    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj08_Timestamp_LLL3BIT=(uint8)(u32PDCSignalTimestamp&0x07);

    PdcSignal_ReadMapObj(&LstrMapObjInfo,9);
    u32PDCSignalTimestamp = LstrMapObjInfo.u32PDCSignal_PAS_MapObjTimestamp;
    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj09_Timestamp_HHH5BIT=(uint8)((u32PDCSignalTimestamp>>22)&0x1f);
    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj09_Timestamp_HHL8BIT=(uint8)((u32PDCSignalTimestamp>>14)&0xFF);
    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj09_Timestamp_HLL8BIT=(uint8)((u32PDCSignalTimestamp>>6)&0xFF);
    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj09_Timestamp_LLL6BIT=(uint8)(u32PDCSignalTimestamp&0x3f);

    PdcSignal_ReadMapObj(&LstrMapObjInfo,10);
    u32PDCSignalTimestamp = LstrMapObjInfo.u32PDCSignal_PAS_MapObjTimestamp;

    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj10_Timestamp_HHHH2BIT=(uint8)((u32PDCSignalTimestamp>>25)&0x03);
    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj10_Timestamp_HHHL8BIT=(uint8)((u32PDCSignalTimestamp>>17)&0xff);
    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj10_Timestamp_HHLL8BIT=(uint8)((u32PDCSignalTimestamp>>9)&0xff);
    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj10_Timestamp_HLLL8BIT=(uint8)((u32PDCSignalTimestamp>>1)&0xff);
    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj10_Timestamp_LLLL1BIT=(uint8)(u32PDCSignalTimestamp&0x01);

    PdcSignal_ReadMapObj(&LstrMapObjInfo,11);
    u32PDCSignalTimestamp = LstrMapObjInfo.u32PDCSignal_PAS_MapObjTimestamp;

    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj11_Timestamp__HHH7BIT=(uint8)((u32PDCSignalTimestamp>>20)&0x7f);
    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj11_Timestamp_HHL8BIT=(uint8)((u32PDCSignalTimestamp>>12)&0xff);
    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj11_Timestamp_HLL8BIT=(uint8)((u32PDCSignalTimestamp>>4)&0xff);
    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj11_Timestamp_LLL4BIT=(uint8)((u32PDCSignalTimestamp)&0x0f);

    PdcSignal_ReadMapObj(&LstrMapObjInfo,12);
    u32PDCSignalTimestamp = LstrMapObjInfo.u32PDCSignal_PAS_MapObjTimestamp;

    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj12_Timestamp__HHH4BIT=(uint8)((u32PDCSignalTimestamp>>23)&0x0f);
    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj12_Timestamp_HHL8BIT=(uint8)((u32PDCSignalTimestamp>>15)&0xff);
    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj12_Timestamp_HLL8BIT=(uint8)((u32PDCSignalTimestamp>>7)&0xff);
    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj12_Timestamp_LLL7BIT=(uint8)((u32PDCSignalTimestamp)&0x7f);

    PdcSignal_ReadMapObj(&LstrMapObjInfo,13);
    u32PDCSignalTimestamp = LstrMapObjInfo.u32PDCSignal_PAS_MapObjTimestamp;

    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj13_Timestamp_HHHH1BIT=(uint8)((u32PDCSignalTimestamp>>26)&0x01);
    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj13_Timestamp_HHHL8BIT=(uint8)((u32PDCSignalTimestamp>>18)&0xff);
    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj13_Timestamp_HHLL8BIT=(uint8)((u32PDCSignalTimestamp>>10)&0xff);
    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj13_Timestamp_HLLL8BIT=(uint8)((u32PDCSignalTimestamp>>2)&0xff);
    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj13_Timestamp_LLLL2BIT=(uint8)((u32PDCSignalTimestamp)&0x03);


    PdcSignal_ReadMapObj(&LstrMapObjInfo,14);
    u32PDCSignalTimestamp = LstrMapObjInfo.u32PDCSignal_PAS_MapObjTimestamp;

    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj14_Timestamp_HHH6BIT=(uint8)((u32PDCSignalTimestamp>>21)&0x3f);
    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj14_Timestamp_HHL8BIT=(uint8)((u32PDCSignalTimestamp>>13)&0xff);
    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj14_Timestamp_HLL8BIT=(uint8)((u32PDCSignalTimestamp>>5)&0xff);
    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj14_Timestamp_LLL5BIT=(uint8)((u32PDCSignalTimestamp)&0x1f);

    PdcSignal_ReadMapObj(&LstrMapObjInfo,15);
    u32PDCSignalTimestamp = LstrMapObjInfo.u32PDCSignal_PAS_MapObjTimestamp;

    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj15_Timestamp_HHH3BIT=(uint8)((u32PDCSignalTimestamp>>24)&0x07);
    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj15_Timestamp_HHL8BIT=(uint8)((u32PDCSignalTimestamp>>16)&0xff);
    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj15_Timestamp_HLL8BIT=(uint8)((u32PDCSignalTimestamp>>8)&0xff);
    LstrTX_APS_0x2B4_20_MSG.bit.PAS_MapObj15_Timestamp_LLL8BIT=(uint8)((u32PDCSignalTimestamp)&0xff);
    /********************************End Of Signal Assignment********************************/
    /****************************************************************************************/
    /****************************************************************************************/
    Lu8CRCResult  = CanIL_CRCCheckSum_Algorithm(&(LstrTX_APS_0x2B4_20_MSG.byte[1]),Lu8CRCLen,CAL_CRC_INIT_VALUE,DataId_2B4,Gu8NodeALC[ID2B4Idx]);
    LstrTX_APS_0x2B4_20_MSG.bit.SigGroup_0x2B4_ChkSm = Lu8CRCResult;

    /* 将填充数据赋值给COM层buff*/  
    for(i=0;i<32;i++) 
    {
       GcComDataBuffer[ID2B4Idx][i] = LstrTX_APS_0x2B4_20_MSG.byte[i];
    }
    Gu8NodeALC[ID2B4Idx]++;
    /*ALC大于14，清零*/
    if(Gu8NodeALC[ID2B4Idx] > 0x0F)    Gu8NodeALC[ID2B4Idx] = 0;

}
CAN_0x2B5_20_Type LstrTX_APS_0x2B5_20_MSG; 
static void CanIL_Tx_PAS_TmspMapObj3_20ms_2B5_Msg(const uint8 *pBuff)
{
    uint8 i;
    (void)pBuff;/*消除警告*/    
    uint8 Lu8CRCLen = 32-1;/*报文长度减1*/
    uint8 Lu8CRCResult = 0;

    /*信号赋值*/
    LstrTX_APS_0x2B5_20_MSG.bit.SigGroup_0x2B5_RlngCtr = Gu8NodeALC[ID2B5Idx] ;   
    /********************************Start Of Signal Assignment********************************/
    /****************************************************************************************/
    /****************************************************************************************/

    uint32  u32PDCSignalTimestamp=0;
    uint8   u8PDCSignalMapObjIndex;
    PdcSignal_MapObjInfoType LstrMapObjInfo;
    uint8 Lu8MapObjLockFlg;
    

    PdcSignal_ReadMapObj(&LstrMapObjInfo,0);
    u8PDCSignalMapObjIndex = LstrMapObjInfo.u8PDCSignal_MapObjIndex;
    Lu8MapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(!Lu8MapObjLockFlg)
    {
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj0IndexH2BIT = (uint8)((u8PDCSignalMapObjIndex>>3u) &0x03);
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj0IndexL3BIT = (uint8)((u8PDCSignalMapObjIndex)&0x07);
    }

    PdcSignal_ReadMapObj(&LstrMapObjInfo,1);
    u8PDCSignalMapObjIndex = LstrMapObjInfo.u8PDCSignal_MapObjIndex;
    Lu8MapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(!Lu8MapObjLockFlg)
    {
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj1Index   = (uint8)(u8PDCSignalMapObjIndex);
    }

    PdcSignal_ReadMapObj(&LstrMapObjInfo,2);
    u8PDCSignalMapObjIndex = LstrMapObjInfo.u8PDCSignal_MapObjIndex;
    Lu8MapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(!Lu8MapObjLockFlg)
    {
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj2Index   = (uint8)(u8PDCSignalMapObjIndex);
    }

    PdcSignal_ReadMapObj(&LstrMapObjInfo,3);
    u8PDCSignalMapObjIndex = LstrMapObjInfo.u8PDCSignal_MapObjIndex;
    Lu8MapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(!Lu8MapObjLockFlg)
    {
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj3IndexH3BIT   = (uint8)((u8PDCSignalMapObjIndex>>2u) &0x07);
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj3IndexL2BIT   = (uint8)((u8PDCSignalMapObjIndex)&0x03);
    }

    PdcSignal_ReadMapObj(&LstrMapObjInfo,4);
    u8PDCSignalMapObjIndex = LstrMapObjInfo.u8PDCSignal_MapObjIndex;
    Lu8MapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(!Lu8MapObjLockFlg)
    {
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj4Index   = (uint8)(u8PDCSignalMapObjIndex);
    }

    PdcSignal_ReadMapObj(&LstrMapObjInfo,5);
    u8PDCSignalMapObjIndex = LstrMapObjInfo.u8PDCSignal_MapObjIndex;
    Lu8MapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(!Lu8MapObjLockFlg)
    {
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj5IndexH1BIT   = (uint8)((u8PDCSignalMapObjIndex>>4u) &0x01);
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj5IndexL4BIT   = (uint8)((u8PDCSignalMapObjIndex)&0x0f);
    }
    
    PdcSignal_ReadMapObj(&LstrMapObjInfo,6);
    u8PDCSignalMapObjIndex = LstrMapObjInfo.u8PDCSignal_MapObjIndex;
    Lu8MapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(!Lu8MapObjLockFlg)
    {
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj6IndexH4BIT  =(uint8)((u8PDCSignalMapObjIndex>>1u) &0x0f);
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj6IndexL1BIT  =(uint8)((u8PDCSignalMapObjIndex)&0x01);
    }

    PdcSignal_ReadMapObj(&LstrMapObjInfo,7);
    u8PDCSignalMapObjIndex = LstrMapObjInfo.u8PDCSignal_MapObjIndex;
    Lu8MapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(!Lu8MapObjLockFlg)
    {
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj7Index  =(uint8)((u8PDCSignalMapObjIndex));
    }

    PdcSignal_ReadMapObj(&LstrMapObjInfo,8);
    u8PDCSignalMapObjIndex = LstrMapObjInfo.u8PDCSignal_MapObjIndex;
    Lu8MapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(!Lu8MapObjLockFlg)
    {
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj8IndexH2BIT  =(uint8)((u8PDCSignalMapObjIndex>>3u) &0x03);
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj8IndexL3BIT  =(uint8)((u8PDCSignalMapObjIndex)&0x07);
    }

    PdcSignal_ReadMapObj(&LstrMapObjInfo,9);
    u8PDCSignalMapObjIndex = LstrMapObjInfo.u8PDCSignal_MapObjIndex;
    Lu8MapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(!Lu8MapObjLockFlg)
    {
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj9Index = (uint8)((u8PDCSignalMapObjIndex));
    }
    	
    PdcSignal_ReadMapObj(&LstrMapObjInfo,10);
    u8PDCSignalMapObjIndex = LstrMapObjInfo.u8PDCSignal_MapObjIndex;
    Lu8MapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(!Lu8MapObjLockFlg)
    {
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj10Index = (uint8)((u8PDCSignalMapObjIndex));
    }

    PdcSignal_ReadMapObj(&LstrMapObjInfo,11);
    u8PDCSignalMapObjIndex = LstrMapObjInfo.u8PDCSignal_MapObjIndex;
    Lu8MapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(!Lu8MapObjLockFlg)
    {
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj11IndexH3BIT = (uint8)((u8PDCSignalMapObjIndex>>2u) &0x07);
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj11IndexL2BIT = (uint8)((u8PDCSignalMapObjIndex)&0x03);
    }

    PdcSignal_ReadMapObj(&LstrMapObjInfo,12);
    u8PDCSignalMapObjIndex = LstrMapObjInfo.u8PDCSignal_MapObjIndex;
    Lu8MapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(!Lu8MapObjLockFlg)
    {
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj12Index = (uint8)((u8PDCSignalMapObjIndex));
    }
    
    PdcSignal_ReadMapObj(&LstrMapObjInfo,13);
    u8PDCSignalMapObjIndex = LstrMapObjInfo.u8PDCSignal_MapObjIndex;
    Lu8MapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(!Lu8MapObjLockFlg)
    {
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj13IndexH1BIT = (uint8)((u8PDCSignalMapObjIndex>>4u) &0x01);
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj13IndexL4BIT = (uint8)((u8PDCSignalMapObjIndex)&0x0f);
    }

    PdcSignal_ReadMapObj(&LstrMapObjInfo,14);
    u8PDCSignalMapObjIndex = LstrMapObjInfo.u8PDCSignal_MapObjIndex;
    Lu8MapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(!Lu8MapObjLockFlg)
    {
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj14IndexH4BIT =(uint8)((u8PDCSignalMapObjIndex>>1u) &0x0f);
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj14IndexL1BIT =(uint8)((u8PDCSignalMapObjIndex)&0x01);
    }
    
    PdcSignal_ReadMapObj(&LstrMapObjInfo,15);
    u8PDCSignalMapObjIndex = LstrMapObjInfo.u8PDCSignal_MapObjIndex;
    Lu8MapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(!Lu8MapObjLockFlg)
    {
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj15Index = (uint8)((u8PDCSignalMapObjIndex));
    }


    PdcSignal_ReadMapObj(&LstrMapObjInfo,16);
    u8PDCSignalMapObjIndex = LstrMapObjInfo.u8PDCSignal_MapObjIndex;
    Lu8MapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(!Lu8MapObjLockFlg)
    {
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj16IndexH2BIT = (uint8)((u8PDCSignalMapObjIndex>>3u) &0x03);
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj16IndexL3BIT = (uint8)((u8PDCSignalMapObjIndex)&0x07);
    }
    
    PdcSignal_ReadMapObj(&LstrMapObjInfo,17);
    u8PDCSignalMapObjIndex = LstrMapObjInfo.u8PDCSignal_MapObjIndex;
    Lu8MapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(!Lu8MapObjLockFlg)
    {
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj17Index =(uint8)((u8PDCSignalMapObjIndex));
    }

    PdcSignal_ReadMapObj(&LstrMapObjInfo,18);
    u8PDCSignalMapObjIndex = LstrMapObjInfo.u8PDCSignal_MapObjIndex;
    Lu8MapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(!Lu8MapObjLockFlg)
    {
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj18Index =(uint8)((u8PDCSignalMapObjIndex));
    }
    	
    PdcSignal_ReadMapObj(&LstrMapObjInfo,19);
    u8PDCSignalMapObjIndex = LstrMapObjInfo.u8PDCSignal_MapObjIndex;
    Lu8MapObjLockFlg = LstrMapObjInfo.bObjWriteLockFlg;
    if(!Lu8MapObjLockFlg)
    {
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj19IndexH3BIT =(uint8)((u8PDCSignalMapObjIndex>>2u) &0x07);
        LstrTX_APS_0x2B5_20_MSG.bit.MapObj19IndexL2BIT = (uint8)((u8PDCSignalMapObjIndex)&0x03);
    }

    PdcSignal_ReadMapObj(&LstrMapObjInfo,16);
    u32PDCSignalTimestamp = LstrMapObjInfo.u32PDCSignal_PAS_MapObjTimestamp;
    LstrTX_APS_0x2B5_20_MSG.bit.PAS_MapObj16_Timestamp_HHH8BIT=(uint8)((u32PDCSignalTimestamp>>19)&0xff);
    LstrTX_APS_0x2B5_20_MSG.bit.PAS_MapObj16_Timestamp_HHL8BIT=(uint8)((u32PDCSignalTimestamp>>11)&0xFF);
    LstrTX_APS_0x2B5_20_MSG.bit.PAS_MapObj16_Timestamp_HLL8BIT=(uint8)((u32PDCSignalTimestamp>>3)&0xFF);
    LstrTX_APS_0x2B5_20_MSG.bit.PAS_MapObj16_Timestamp_LLL3BIT=(uint8)(u32PDCSignalTimestamp&0x07);

    PdcSignal_ReadMapObj(&LstrMapObjInfo,17);
    u32PDCSignalTimestamp = LstrMapObjInfo.u32PDCSignal_PAS_MapObjTimestamp;
    LstrTX_APS_0x2B5_20_MSG.bit.PAS_MapObj17_Timestamp_HHH5BIT=(uint8)((u32PDCSignalTimestamp>>22)&0x1f);
    LstrTX_APS_0x2B5_20_MSG.bit.PAS_MapObj17_Timestamp_HHL8BIT=(uint8)((u32PDCSignalTimestamp>>14)&0xFF);
    LstrTX_APS_0x2B5_20_MSG.bit.PAS_MapObj17_Timestamp_HLL8BIT=(uint8)((u32PDCSignalTimestamp>>6)&0xFF);
    LstrTX_APS_0x2B5_20_MSG.bit.PAS_MapObj17_Timestamp_LLL6BIT=(uint8)(u32PDCSignalTimestamp&0x3f);

    /********************************End Of Signal Assignment********************************/
    /****************************************************************************************/
    /****************************************************************************************/

    Lu8CRCResult  = CanIL_CRCCheckSum_Algorithm(&(LstrTX_APS_0x2B5_20_MSG.byte[1]),Lu8CRCLen,CAL_CRC_INIT_VALUE,DataId_2B5,Gu8NodeALC[ID2B5Idx]);
    LstrTX_APS_0x2B5_20_MSG.bit.SigGroup_0x2B5_ChkSm = Lu8CRCResult;

    /* 将填充数据赋值给COM层buff*/  
    for(i=0;i<32;i++) 
    {
       GcComDataBuffer[ID2B5Idx][i] = LstrTX_APS_0x2B5_20_MSG.byte[i];
    }
    Gu8NodeALC[ID2B5Idx]++;
    /*ALC大于14，清零*/
    if(Gu8NodeALC[ID2B5Idx] > 0x0F)    Gu8NodeALC[ID2B5Idx] = 0;

}
CAN_0x685_5000_Type LstrTX_APS_0x685_5000_MSG; 

static void CanIL_Tx_PAS_VerNum_5000ms_685_Msg(const uint8 *pBuff)
{
    uint8 i;
    (void)pBuff;/*消除警告*/    
    /*信号赋值*/

    LstrTX_APS_0x685_5000_MSG.bit.PAS_BTVerNum_H8BIT = DID_F14A_Buff[0];
    LstrTX_APS_0x685_5000_MSG.bit.PAS_BTVerNum_L8BIT = DID_F14A_Buff[1];
    LstrTX_APS_0x685_5000_MSG.bit.PAS_HWVerNum_H8BIT = DID_F193_Buff[0];
    LstrTX_APS_0x685_5000_MSG.bit.PAS_HWVerNum_L8BIT = DID_F193_Buff[1];
    LstrTX_APS_0x685_5000_MSG.bit.PAS_SWVerNum_H8BIT = DID_F195_Buff[0];

    LstrTX_APS_0x685_5000_MSG.bit.PAS_SWVerNum_L8BIT = DID_F195_Buff[1];
    LstrTX_APS_0x685_5000_MSG.bit.PAS_SWID_H8BIT = DID_F026_Buff[0];
    LstrTX_APS_0x685_5000_MSG.bit.PAS_SWID_L8BIT = DID_F026_Buff[1];

    /* 将填充数据赋值给COM层buff*/  
    for(i=0;i<8;i++) 
    {
       GcComDataBuffer[ID685Idx][i] = LstrTX_APS_0x685_5000_MSG.byte[i];
    }
}
#endif
/********************************************************************************************/
/********************************************************************************************/
/********************************************************************************************/
/********************************************************************************************/
/********************************************************************************************/
/****************************************End*************************************************/
/********************************************************************************************/
/********************************************************************************************/
/********************************************************************************************/
/********************************************************************************************/
/********************************************************************************************/
/********************************************************************************************/
/********************************************************************************************/

