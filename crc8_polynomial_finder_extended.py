import sys

def calculate_crc8(data, polynomial, init_value):
    crc = init_value
    
    # 初始化CRC表
    crc_table = []
    for i in range(256):
        crc_value = i
        for j in range(8):
            if crc_value & 0x80:
                crc_value = (crc_value << 1) ^ polynomial
            else:
                crc_value <<= 1
            crc_value &= 0xFF  # 确保结果是8位
        crc_table.append(crc_value)
    
    # 计算CRC
    for byte in data:
        crc = crc_table[crc ^ byte]
    
    return crc

def find_all_polynomials(data, expected_crc, init_value):
    # 尝试所有可能的多项式值
    matching_polynomials = []
    for polynomial in range(1, 256):  # 从1开始，因为0不是有效的多项式
        crc = calculate_crc8(data, polynomial, init_value)
        if crc == expected_crc:
            matching_polynomials.append(polynomial)
    
    return matching_polynomials

def polynomial_to_expression(polynomial):
    # 将多项式转换为二进制表示
    binary = bin(polynomial)[2:].zfill(8)
    
    # 构建多项式表达式
    terms = []
    for i, bit in enumerate(binary):
        if bit == '1':
            power = 7 - i
            if power == 0:
                terms.append('1')
            elif power == 1:
                terms.append('x')
            else:
                terms.append(f'x^{power}')
    
    return ' + '.join(terms)

# 已知数据、CRC8结果和初始值
data = [0xF8, 0x09, 0x30]
expected_crc = 0x1F
init_value = 0xFF

print('尝试推导CRC8多项式值...')
print(f'数据: {[f"0x{b:02X}" for b in data]}')
print(f'预期CRC8: 0x{expected_crc:02X}')
print(f'初始值: 0x{init_value:02X}')
print()

# 推导所有可能的多项式值
matching_polynomials = find_all_polynomials(data, expected_crc, init_value)

if matching_polynomials:
    print(f'找到 {len(matching_polynomials)} 个匹配的多项式值:')
    for polynomial in matching_polynomials:
        print(f'多项式: 0x{polynomial:02X} - {polynomial_to_expression(polynomial)}')
    
    print("\n常见的CRC8多项式:")
    common_polynomials = [
        (0x07, "x^8 + x^2 + x + 1 (CRC-8)"),
        (0x31, "x^8 + x^5 + x^4 + 1 (CRC-8/MAXIM)"),
        (0x39, "x^8 + x^5 + x^4 + x^3 + 1 (CRC-8/WCDMA)"),
        (0x1D, "x^8 + x^4 + x^3 + x^2 + 1 (CRC-8/ROHC)"),
        (0x2F, "x^8 + x^5 + x^3 + x^2 + x + 1 (CRC-8/DARC)"),
        (0x9B, "x^8 + x^7 + x^4 + x^3 + x + 1 (CRC-8/I-CODE)"),
        (0xD5, "x^8 + x^7 + x^6 + x^4 + x^2 + 1 (CRC-8/EBU)")
    ]
    
    for poly, name in common_polynomials:
        if poly in matching_polynomials:
            print(f"找到匹配的标准多项式: 0x{poly:02X} - {name}")
    
    # 选择第一个多项式进行验证
    polynomial = matching_polynomials[0]
    print(f'\n使用多项式 0x{polynomial:02X} 进行验证:')
    verification_crc = calculate_crc8(data, polynomial, init_value)
    print(f'验证CRC8: 0x{verification_crc:02X} ({"成功" if verification_crc == expected_crc else "失败"})')
    
    # 测试另一个示例
    print('\n测试另一个示例:')
    test_data = [0xF8, 0x08, 0x0A]
    test_crc = calculate_crc8(test_data, polynomial, init_value)
    print(f'数据: {[f"0x{b:02X}" for b in test_data]}')
    print(f'计算CRC8: 0x{test_crc:02X}')
    
    # 测试第二个示例
    print('\n测试第二个示例:')
    test_data2 = [0xF4, 0x6F, 0x34, 0xD7, 0x34, 0x3D, 0xEF, 0x3D, 0xEF, 0x1F, 0xBF, 0x00, 0x28]
    test_crc2 = calculate_crc8(test_data2, polynomial, init_value)
    print(f'数据: {[f"0x{b:02X}" for b in test_data2]}')
    print(f'计算CRC8: 0x{test_crc2:02X}')
else:
    print('未找到匹配的多项式值')
