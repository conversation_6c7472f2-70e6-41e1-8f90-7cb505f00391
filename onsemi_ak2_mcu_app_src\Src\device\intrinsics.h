/*-------------------------------------------------------------------------
 *      Declarations of intrinsic functions.
 *
 *      Copyright 2012 IAR Systems AB.
 *
 *      $Revision: 9698 $
 *-------------------------------------------------------------------------*/

#ifndef __INTRINSICS_H__
#define __INTRINSICS_H__

#ifndef _SYSTEM_BUILD
#pragma system_include
#endif

#if !defined(__ICCRH850__)
#error "INTRINSICS.H file for use with ICCRH850 only"
#endif

#pragma language=save
#pragma language=extended

#ifdef __cplusplus
extern "C" {
#endif

/* Type user can use to represent the interrupt state. */
#define __istate_t long

/* ----------------------------------------
 * Intrinsic functions.
 */

__intrinsic void __disable_interrupt(void);
__intrinsic void __enable_interrupt(void);
__intrinsic void __no_operation(void);
__intrinsic void __halt(void);
__intrinsic void __snooze(void);
__intrinsic long __saturated_add(long, long);
__intrinsic long __saturated_sub(long, long);
__intrinsic long __STSR(int reg, int selID);
__intrinsic void __LDSR(int reg, int selID, long val);
__intrinsic void __set_interrupt_state(__istate_t val);
__intrinsic long __upper_mul64(long, long);

__intrinsic long __LDL(long *);
__intrinsic long __STC(long *, long);

__intrinsic long __SCH0L(long);
__intrinsic long __SCH0R(long);
__intrinsic long __SCH1L(long);
__intrinsic long __SCH1R(long);

__intrinsic long __BSH(long);
__intrinsic long __BSW(long);
__intrinsic long __HSW(long);

__intrinsic void __SYNCE(void);
__intrinsic void __SYNCI(void);
__intrinsic void __SYNCM(void);
__intrinsic void __SYNCP(void);
__intrinsic int  __CAXI(int *, int, int);
__intrinsic void __EST(void);
__intrinsic void __DST(void);


__intrinsic signed long long __MAC(signed long l1, signed long l2, signed long long ll);
__intrinsic unsigned long long __MACU(unsigned long l1, unsigned long l2, unsigned long long ll);

/* Intrinsic functions for MMU control */
__intrinsic void __TLBAI(void);
__intrinsic void __TLBR(void);
__intrinsic void __TLBS(void);
__intrinsic void __TLBVI(void);
__intrinsic void __TLBW(void);

__intrinsic void __DBCP(void);
__intrinsic void __DBHVTRAP(void);
__intrinsic void __DBPUSH(long, long);
__intrinsic void __DBTAG(long);
__intrinsic void __DBTRAP(void);
__intrinsic void __RMTRAP(void);
__intrinsic void __DBRET(void);

  /* Intrinsic functions for floating point operations */
#if (   __FPU__ == __FPU_SINGLE__ \
     || __FPU__ == __FPU_DOUBLE__)
 __intrinsic long   __CEILF_SW (float);
 __intrinsic long   __FLOORF_SW(float);
 __intrinsic long   __ROUNDF_SW(float);
 __intrinsic long   __TRNCF_SW (float);
 __intrinsic long long  __CEILF_SL (float);
 __intrinsic long long  __FLOORF_SL(float);
 __intrinsic long long  __ROUNDF_SL(float);
 __intrinsic long long  __TRNCF_SL (float);
 __intrinsic unsigned long __CEILF_SUW (float);
 __intrinsic unsigned long __FLOORF_SUW(float);
 __intrinsic unsigned long __ROUNDF_SUW(float);
 __intrinsic unsigned long __TRNCF_SUW (float);
 __intrinsic unsigned long long __CEILF_SUL (float);
 __intrinsic unsigned long long __FLOORF_SUL(float);
 __intrinsic unsigned long long __ROUNDF_SUL(float);
 __intrinsic unsigned long long __TRNCF_SUL (float);
 __intrinsic float  __RECIPF_S (float);
 __intrinsic float  __RSQRTF_S (float);
 __intrinsic float  __SQRTF_S  (float);
 __intrinsic float  __MADDF_S  (float,float,float);
 __intrinsic float  __MSUBF_S  (float,float,float);
 __intrinsic float  __NMADDF_S (float,float,float);
 __intrinsic float  __NMSUBF_S (float,float,float);
#endif
#if (__FPU__ == __FPU_DOUBLE__)
 __intrinsic long   __CEILF_DW (double);
 __intrinsic long   __FLOORF_DW(double);
 __intrinsic long   __ROUNDF_DW(double);
 __intrinsic long   __TRNCF_DW (double);
 __intrinsic long long  __CEILF_DL (double);
 __intrinsic long long  __FLOORF_DL(double);
 __intrinsic long long  __ROUNDF_DL(double);
 __intrinsic long long  __TRNCF_DL (double);
 __intrinsic unsigned long __CEILF_DUW (double);
 __intrinsic unsigned long __FLOORF_DUW(double);
 __intrinsic unsigned long __ROUNDF_DUW(double);
 __intrinsic unsigned long __TRNCF_DUW (double);
 __intrinsic unsigned long long __CEILF_DUL (double);
 __intrinsic unsigned long long __FLOORF_DUL(double);
 __intrinsic unsigned long long __ROUNDF_DUL(double);
 __intrinsic unsigned long long __TRNCF_DUL (double);
 __intrinsic double __RECIPF_D (double);
 __intrinsic double __RSQRTF_D (double);
 __intrinsic double __SQRTF_D  (double);
#endif

extern unsigned char __low_level_init (int peId);

#ifdef __cplusplus
  }
#endif

#define __asm(/* (const char *) */x) asm(x)


/* ----------------------------------------
 * Function for and restoring the interrupt state.
 *
 * Note, on the V850E2M and higher, the BSEL register is modified to
 * refer to the CPU Main bank.
 */

#define __get_interrupt_state() (__get_processor_register(Reg_CPU_PSW))

/* ----------------------------------------
 * Macros for backward compatiblity.
 */

#define __DI()        __disable_interrupt()
#define __EI()        __enable_interrupt()
#define __NOP()       __no_operation()
#define __HALT()      __halt()
#define __SNOOZE()    __snooze()
#define __SATADD(x,y) __saturated_add((x),(y))
#define __SATSUB(x,y) __saturated_sub((x),(y))
#define __get_processor_register(X)   __STSR(X,0)
#define __set_processor_register(X,Y) __LDSR(X,0,Y)
#define __ASM(x)      __asm(x)
#define __search_zeros_left(X)  __SCH0L(X)
#define __search_zeros_right(X) __SCH0R(X)
#define __search_ones_left(X)   __SCH1L(X)
#define __search_ones_right(X)  __SCH1R(X)
#define __fpu_sqrt_float(X)     __SQRTF_S(X)
#define __fpu_sqrt_double(X)    __SQRTF_D(X)
#define __synchronize_exceptions() __SYNCE()
#define __synchronize_memory()     __SYNCM()
#define __synchronize_pipeline()   __SYNCP()
#define __compare_and_exchange_for_interlock(X,Y,Z) __CAXI(X,Y,Z)

/* ----------------------------------------
 * Register specifications for __get/set_processor_register
 */

/* --------------------
 * Normal processor registers.
 */

#define Reg_R0        32
#define Reg_R1        (Reg_R0+1)
#define Reg_R2        (Reg_R0+2)
#define Reg_R3        (Reg_R0+3)
#define Reg_R4        (Reg_R0+4)
#define Reg_R5        (Reg_R0+5)
#define Reg_R6        (Reg_R0+6)
#define Reg_R7        (Reg_R0+7)
#define Reg_R8        (Reg_R0+8)
#define Reg_R9        (Reg_R0+9)
#define Reg_R10       (Reg_R0+10)
#define Reg_R11       (Reg_R0+11)
#define Reg_R12       (Reg_R0+12)
#define Reg_R13       (Reg_R0+13)
#define Reg_R14       (Reg_R0+14)
#define Reg_R15       (Reg_R0+15)
#define Reg_R16       (Reg_R0+16)
#define Reg_R17       (Reg_R0+17)
#define Reg_R18       (Reg_R0+18)
#define Reg_R19       (Reg_R0+19)
#define Reg_R20       (Reg_R0+20)
#define Reg_R21       (Reg_R0+21)
#define Reg_R22       (Reg_R0+22)
#define Reg_R23       (Reg_R0+23)
#define Reg_R24       (Reg_R0+24)
#define Reg_R25       (Reg_R0+25)
#define Reg_R26       (Reg_R0+26)
#define Reg_R27       (Reg_R0+27)
#define Reg_R28       (Reg_R0+28)
#define Reg_R29       (Reg_R0+29)
#define Reg_R30       (Reg_R0+30)
#define Reg_R31       (Reg_R0+31)

#define Reg_HP        Reg_R2
#define Reg_SP        Reg_R3
#define Reg_GP        Reg_R4
#define Reg_EP        Reg_R30
#define Reg_LP        Reg_R31

/* --------------------
 * The system registers are organized in banks selected by the second
 * operand (selID).
 *
 * Use __STSR and __LDSR intrinsic functions to get and set system
 * registers.
 *
 * Note that the __get/set_processor_register is kept for backward
 * compatibility and can only access system register bank 0 (selID=0).
 */

/* ----------
 * SelID = 0
 */

#define Reg_CPU_EIPIC      0
#define Reg_CPU_EIPSW      1
#define Reg_CPU_FEPC       2
#define Reg_CPU_FEPSW      3
#define Reg_CPU_PSW        5

#if (__CORE__ != __CORE_G3K__)
#define Reg_CPU_FPSR       6
#define Reg_CPU_FPEPC      7
#define Reg_CPU_FPST       8
#define Reg_CPU_FPCC       9
#define Reg_CPU_FPCFG     10
#endif

#if (__CORE__ == __CORE_G3KH__) || (__CORE__ == __CORE_G3M__)
#define Reg_CPU_FPEC      11
#endif

#define Reg_CPU_EIIC      13
#define Reg_CPU_FEIC      14
#define Reg_CPU_CTPC      16
#define Reg_CPU_CTPSW     17
#define Reg_CPU_CTBP      20

#if (__CORE__ == __CORE_G4MH__)
#define Reg_CPU_SNZCFG    21
#endif

#define Reg_CPU_EIWR      28
#define Reg_CPU_FEWR      29


/* ----------
 * SelID = 1
 */

#if (__CORE__ == __CORE_G4MH__)
#define Reg_CPU_SPID       0
#else
#define Reg_CPU_MCFG0      0
#endif

#if (__CORE__ == __CORE_G4MH__)
#define SPIDLIST           1
#endif

#define Reg_CPU_RBASE      2
#define Reg_CPU_EBASE      3
#define Reg_CPU_INTBP      4
#define Reg_CPU_MCTL       5
#define Reg_CPU_PID        6

#if (__CORE__ == __CORE_G3KH__) || (__CORE__ == __CORE_G3M__)
#define Reg_CPU_FPIPR      7
#endif

#if (__CORE__ == __CORE_G4MH__)
#define Reg_CPU_SVLOCK
#endif

#define Reg_CPU_SCCFG     11
#define Reg_CPU_SCBP      12

/* ----------
 * SelID = 2
 */

#if (__CORE__ == __CORE_G4MH__)
#define Reg_CPU_PEID       0
#else
#define Reg_CPU_HTCFG0     0
#endif

#if (__CORE__ == __CORE_G4MH__)
#define Reg_CPU_BMID       1
#endif

#define Reg_CPU_MEA        6

#if (__CORE__ != __CORE_G4MH__)
#define Reg_CPU_ASID       7
#endif

#define Reg_CPU_MEI        8
#define Reg_CPU_ISPR      10

#if (__CORE__ != __CORE_G4MH__)
#define Reg_CPU_PMR       11
#endif

#define Reg_CPU_ICSR      12
#define Reg_CPU_INTCFG    13

#if (__CORE__ == __CORE_G4MH__)
#define Reg_CPU_PLMR      14
#define Reg_CPU_RBCR0     15
#define Reg_CPU_RBCR1     16
#define Reg_CPU_RBNR      17
#define Reg_CPU_RBIP      18
#endif

 /* ----------
 * SelID = 4
 */

#if (__CORE__ == __CORE_G3M__) || (__CORE__ == __CORE_G3MH__) || (__CORE__ == __CORE_G4MH__)
#define Reg_CPU_ICTAGL    16
#define Reg_CPU_ICTAGH    17
#define Reg_CPU_ICDATL    18
#define Reg_CPU_ICDATH    19
#define Reg_CPU_ICCTRL    24
#define Reg_CPU_ICCFG     26
#define Reg_CPU_ICERR     28
#endif

 /* ----------
 * SelID = 5
 */

#define Reg_CPU_MPM        0

#if (__CORE__ != __CORE_G4MH__)
#define Reg_CPU_MPRC       1
#endif

#if (__CORE__ == __CORE_G4MH__)
#define Reg_CPU_MPCFG      2
#endif

#if (__CORE__ != __CORE_G4MH__)
#define Reg_CPU_MPBRGN     4
#define Reg_CPU_MPTRGN     5
#endif

#if (__CORE__ != __CORE_G3K__)
#define Reg_CPU_MCA        8
#define Reg_CPU_MCS        9
#define Reg_CPU_MCC       10
#endif

#define Reg_CPU_MCR       11

#if (__CORE__ == __CORE_G4MH__)
#define Reg_CPU_MCI       12
#define Reg_CPU_MPIDX     16
#define Reg_CPU_MPLA      20
#define Reg_CPU_MPUA      21
#define Reg_CPU_MPAT      22
#define Reg_CPU_MPID0     24
#define Reg_CPU_MPID1     25
#define Reg_CPU_MPID2     26
#define Reg_CPU_MPID3     27
#define Reg_CPU_MPID4     28
#define Reg_CPU_MPID5     29
#define Reg_CPU_MPID6     30
#define Reg_CPU_MPID7     31
#endif

 /* ----------
 * SelID = 6
 */

#if (__CORE__ != __CORE_G4MH__)
#define Reg_CPU_MPLA0      0
#define Reg_CPU_MPUA0      1
#define Reg_CPU_MPAT0      2
#define Reg_CPU_MPLA1      4
#define Reg_CPU_MPUA1      5
#define Reg_CPU_MPAT1      6
#define Reg_CPU_MPLA2      8
#define Reg_CPU_MPUA2      9
#define Reg_CPU_MPAT2     10
#define Reg_CPU_MPLA3     12
#define Reg_CPU_MPUA3     13
#define Reg_CPU_MPAT3     14
#define Reg_CPU_MPLA4     16
#define Reg_CPU_MPUA4     17
#define Reg_CPU_MPAT4     18
#define Reg_CPU_MPLA5     20
#define Reg_CPU_MPUA5     21
#define Reg_CPU_MPAT5     22
#define Reg_CPU_MPLA6     24
#define Reg_CPU_MPUA6     25
#define Reg_CPU_MPAT6     26
#define Reg_CPU_MPLA7     28
#define Reg_CPU_MPUA7     29
#define Reg_CPU_MPAT7     30
#endif

 /* ----------
 * SelID = 7
 */

#if (__CORE__ != __CORE_G4MH__)
#define Reg_CPU_MPLA8      0
#define Reg_CPU_MPUA8      1
#define Reg_CPU_MPAT8      2
#define Reg_CPU_MPLA9      4
#define Reg_CPU_MPUA9      5
#define Reg_CPU_MPAT9      6
#define Reg_CPU_MPLA10     8
#define Reg_CPU_MPUA10     9
#define Reg_CPU_MPAT10    10
#define Reg_CPU_MPLA11    12
#define Reg_CPU_MPUA11    13
#define Reg_CPU_MPAT11    14
#define Reg_CPU_MPLA12    16
#define Reg_CPU_MPUA12    17
#define Reg_CPU_MPAT12    18
#define Reg_CPU_MPLA13    20
#define Reg_CPU_MPUA13    21
#define Reg_CPU_MPAT13    22
#define Reg_CPU_MPLA14    24
#define Reg_CPU_MPUA14    25
#define Reg_CPU_MPAT14    26
#define Reg_CPU_MPLA15    28
#define Reg_CPU_MPUA15    29
#define Reg_CPU_MPAT15    30
#endif

 /* ----------
 * SelID = 10
 */

#if (__CORE__ == __CORE_G4MH__)
#define Reg_CPU_FXSR       6
#define Reg_CPU_FXST       8
#define Reg_CPU_FXINFO     9
#define Reg_CPU_FXCFG     10
#define Reg_CPU_FXXC      12
#define Reg_CPU_FXXP      13
#endif

 /* ----------
 * SelID = 11
 */

#if (__CORE__ == __CORE_G4MH__)
#define Reg_CPU_TSCOUNTL   0
#define Reg_CPU_TSCOUNTH   1
#define Reg_CPU_TSCTRL     2
#define Reg_CPU_PMUMCTRL   8
#define Reg_CPU_PMCOUNT0  16
#define Reg_CPU_PMCTRL0   17
#define Reg_CPU_PMCOUNT1  18
#define Reg_CPU_PMCTRL1   19
#define Reg_CPU_PMCOUNT2  20
#define Reg_CPU_PMCTRL2   21
#define Reg_CPU_PMCOUNT3  22
#define Reg_CPU_PMCTRL3   23
#endif

 /* ----------
 * SelID = 12
 */

#if (__CORE__ == __CORE_G4MH__)
#define Reg_CPU_LSTEST0    0
#define Reg_CPU_LSTEST1    1
#define Reg_CPU_IFCR       5
#define Reg_CPU_BRPCTRL0   8
#endif

 /* ----------
 * SelID = 13
 */

#if (__CORE__ == __CORE_G3M__) || (__CORE__ == __CORE_G3MH__)
#define Reg_CPU_CDBCR     24
#endif

#if (__CORE__ == __CORE_G4MH__)
#define Reg_CPU_RDBCR     24
#endif

#pragma language=restore

#endif /* __INTRINSICS_H__ */
