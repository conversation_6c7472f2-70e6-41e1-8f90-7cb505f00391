/******************************************************************************
 * @file       SnsTask_Int.h
 * @brief      
 * @date       2025-03-04 14:27:17
 * <AUTHOR>
 * @copyright  Longhorn Automotive Electronics Technology (Xiamen) Co., Ltd.
 *****************************************************************************/


#ifndef SnsTask_Int_H
#define SnsTask_Int_H
/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "types.h"
#include "SnsTask_Type.h"



/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/



/******************************************************************************
 * @Public Const Declaration
 *****************************************************************************/



/******************************************************************************
 * @Public Variable Declaration
 *****************************************************************************/
extern eSnsSysWorkStsType GeSnsSysWorkSts;



/******************************************************************************
 * @Public Function Declaration
 *****************************************************************************/
void SnsTask_1ms_StepRun(void);



#endif
