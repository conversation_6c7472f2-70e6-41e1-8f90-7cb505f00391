
#ifndef MEMORYASIGN_H
#define MEMORYASIGN_H

/******************************************************************************/
/*----------------------------------Includes----------------------------------*/
/******************************************************************************/
#include "types.h"

/******************************************************************************/
/*-----------------------------------Macros-----------------------------------*/
/******************************************************************************/
#define MEMORY_BUF_8B_NUM 0u
#define MEMORY_BUF_16B_NUM 0u
#define MEMORY_BUF_32B_NUM 0u
#define MEMORY_BUF_64B_NUM 8u
#define MEMORY_BUF_128B_NUM 8u
#define MEMORY_BUF_256B_NUM 8u
#define MEMORY_BUF_512B_NUM 4u
#define MEMORY_BUF_1024B_NUM 4u
#define MEMORY_BUF_2048B_NUM 2u
#define MEMORY_BUF_4096B_NUM 0u
/******************************************************************************/
/*--------------------------------Enumerations--------------------------------*/
/******************************************************************************/

/******************************************************************************/
/*-----------------------------Data Structures--------------------------------*/
/******************************************************************************/

/******************************************************************************/
/*------------------------------Global variables------------------------------*/
/******************************************************************************/

/******************************************************************************/
/*-------------------------Function Prototypes--------------------------------*/
/******************************************************************************/
extern void Memory_Init(void);
extern void* Memory_malloc(uint32 size);
extern void Memory_free(void* dataPtr);
#endif
