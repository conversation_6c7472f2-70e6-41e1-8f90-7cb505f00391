/***********************************************************************************************************************
* DISCLAIMER
* This software is supplied by Renesas Electronics Corporation and is only 
* intended for use with Renesas products. No other uses are authorized. This 
* software is owned by Renesas Electronics Corporation and is protected under 
* all applicable laws, including copyright laws.
* THIS SOFTWARE IS PROVIDED "AS IS" AND R<PERSON><PERSON>AS MAKES NO WARRANTIES REGARDING 
* THIS SOFTWARE, WHETHER EXPRESS, IMPLIED OR STATUTORY, INCLUDING BUT NOT 
* LIMITED TO WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE 
* AND NON-INFRINGEMENT.  ALL SUCH WARRANTIES ARE EXPRESSLY DISCLAIMED.
* TO THE MAXIMUM EXTENT PERMITTED NOT PROHIBITED BY LAW, NEITHER RENESAS 
* ELECTRONICS CORPORATION NOR ANY OF ITS AFFILIATED COMPANIES SHALL BE LIABLE 
* FOR ANY DIRECT, INDIRECT, SPECIAL, INCIDENTAL OR CONSEQUENTIAL DAMAGES FOR 
* ANY REASON RELATED TO THIS SOFTWARE, EVEN IF RENESAS OR ITS AFFILIATES HAVE 
* BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.
* Renesas reserves the right, without notice, to make changes to this software 
* and to discontinue the availability of this software.  By using this software, 
* you agree to the additional terms and conditions found by accessing the 
* following link:
* http://www.renesas.com/disclaimer
*
* Copyright (C) 2015, 2018 Renesas Electronics Corporation. All rights reserved.
***********************************************************************************************************************/

/***********************************************************************************************************************
* File Name    : r_cg_taub.c
* Version      : Applilet4 for RH850/F1K V1.01.02.02 [08 May 2018]
* Device(s)    : R7F701581(LQFP100pin)
* Tool-Chain   : CCRH
* Description  : This file implements device driver for TAUB module.
* Creation Date: 2023/10/12
***********************************************************************************************************************/

/***********************************************************************************************************************
Pragma directive
***********************************************************************************************************************/
/* Start user code for pragma. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */

/***********************************************************************************************************************
Includes
***********************************************************************************************************************/
#include "TAUBDrv.h"
/* Start user code for include. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */

/***********************************************************************************************************************
Global variables and functions
***********************************************************************************************************************/
extern volatile uint32 g_cg_sync_read;
/* Start user code for global. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */

void TAUBInit(void)
{
    R_TAUB0_Create();
    R_TAUB0_Channel8_Start();
}

/***********************************************************************************************************************
* Function Name: R_TAUB0_Create
* Description  : This function initializes the TAUB0 Bus Interface.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_TAUB0_Create(void)
{
    /* Disable channel0 counter operation */
    TAUB0.TT = _TAUB_CHANNEL15_COUNTER_STOP | _TAUB_CHANNEL14_COUNTER_STOP | _TAUB_CHANNEL13_COUNTER_STOP | 
               _TAUB_CHANNEL12_COUNTER_STOP | _TAUB_CHANNEL11_COUNTER_STOP | _TAUB_CHANNEL10_COUNTER_STOP | 
               _TAUB_CHANNEL9_COUNTER_STOP | _TAUB_CHANNEL8_COUNTER_STOP | _TAUB_CHANNEL7_COUNTER_STOP | 
               _TAUB_CHANNEL6_COUNTER_STOP | _TAUB_CHANNEL5_COUNTER_STOP | _TAUB_CHANNEL4_COUNTER_STOP | 
               _TAUB_CHANNEL3_COUNTER_STOP | _TAUB_CHANNEL2_COUNTER_STOP | _TAUB_CHANNEL1_COUNTER_STOP | 
               _TAUB_CHANNEL0_COUNTER_STOP;
    /* Disable INTTAUB0I8 operation and clear request */
    INTC2.ICTAUB0I8.BIT.MKTAUB0I8 = _INT_PROCESSING_DISABLED;
    INTC2.ICTAUB0I8.BIT.RFTAUB0I8 = _INT_REQUEST_NOT_OCCUR;
    /* Disable INTTAUB0I9 operation and clear request */
    INTC2.ICTAUB0I9.BIT.MKTAUB0I9 = _INT_PROCESSING_DISABLED;
    INTC2.ICTAUB0I9.BIT.RFTAUB0I9 = _INT_REQUEST_NOT_OCCUR;
    /* Set INTTAUB0I9 setting */
    INTC2.ICTAUB0I9.BIT.TBTAUB0I9 = _INT_TABLE_VECTOR;
    INTC2.ICTAUB0I9.UINT16 &= _INT_PRIORITY_LOWEST;
    TAUB0.TPS = _TAUB_CK3_PRE_PCLK_0 | _TAUB_CK2_PRE_PCLK_0 | _TAUB_CK1_PRE_PCLK_0 | _TAUB_CK0_PRE_PCLK_0;
    /* Set channel 8 setting */
    TAUB0.CMOR8 = _TAUB_SELECTION_CK0 | _TAUB_OPERATION_CLOCK | _TAUB_MASTER_CHANNEL | _TAUB_SOFTWARE_TRIGGER | 
                  _TAUB_OVERFLOW_AUTO_CLEAR | _TAUB_INTERVAL_TIMER_MODE | _TAUB_START_INT_GENERATED;
    TAUB0.CMUR8 = _TAUB_INPUT_EDGE_UNUSED;
    TAUB0.CDR8 = 0x13;
    /* Set channel 9 setting */
    TAUB0.CMOR9 = _TAUB_SELECTION_CK0 | _TAUB_OPERATION_CLOCK | _TAUB_SLAVE_CHANNEL | _TAUB_MASTER_START | 
                  _TAUB_OVERFLOW_AUTO_CLEAR | _TAUB_ONE_COUNT_MODE | _TAUB_START_TRIGGER_ENABLE;
    TAUB0.CMUR9 = _TAUB_INPUT_EDGE_UNUSED;
    TAUB0.CDR9 = 0x0A;
    /* Set output mode setting */
    TAUB0.TOE = _TAUB_CHANNEL9_ENABLES_OUTPUT_MODE | _TAUB_CHANNEL8_DISABLES_OUTPUT_MODE;
    TAUB0.TOM = _TAUB_CHANNEL9_SYNCHRONOUS_OUTPUT_MODE;
    TAUB0.TOC = _TAUB_CHANNEL9_OPERATION_MODE1;
    TAUB0.TOL = _TAUB_CHANNEL9_POSITIVE_LOGIC;
    TAUB0.TDE = _TAUB_CHANNEL9_DISABLE_DEAD_TIME_OPERATE;
    TAUB0.TDL = _TAUB_CHANNEL9_POSITIVE_PHASE_PERIOD;
    /* Synchronization processing */
    // g_cg_sync_read = TAUB0.TPS;

    /* Set TAUB0O9 pin */
    PORT.PIBC10 &= _PORT_CLEAR_BIT15;
    PORT.PBDC10 &= _PORT_CLEAR_BIT15;
    PORT.PM10 |= _PORT_SET_BIT15;  
    PORT.PMC10 &= _PORT_CLEAR_BIT15;
    PORT.PFC10 |= _PORT_SET_BIT15;  
    PORT.PFCE10 |= _PORT_SET_BIT15;  
    PORT.PMC10 |= _PORT_SET_BIT15;  
    PORT.PM10 &= _PORT_CLEAR_BIT15;
}
/***********************************************************************************************************************
* Function Name: R_TAUB0_Channel8_Start
* Description  : This function clears TAUB08 interrupt flag and enables interrupt.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_TAUB0_Channel8_Start(void)
{
    /* Clear INTTAUB0I9 request and enable operation */
    INTC2.ICTAUB0I9.BIT.RFTAUB0I9 = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICTAUB0I9.BIT.MKTAUB0I9 = _INT_PROCESSING_DISABLED; 
    /* Enable channel8 counter operation */
    TAUB0.TS |= _TAUB_CHANNEL8_COUNTER_START | _TAUB_CHANNEL9_COUNTER_START;
}
/***********************************************************************************************************************
* Function Name: R_TAUB0_Channel8_Stop
* Description  : This function disables TAUB08 interrupt and clears interrupt flag.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_TAUB0_Channel8_Stop(void)
{
    /* Disable channel8 counter operation */
    TAUB0.TT |= _TAUB_CHANNEL8_COUNTER_STOP | _TAUB_CHANNEL9_COUNTER_STOP;
    /* Disable INTTAUB0I9 operation and clear request */
    INTC2.ICTAUB0I9.BIT.MKTAUB0I9 = _INT_PROCESSING_DISABLED;
    INTC2.ICTAUB0I9.BIT.RFTAUB0I9 = _INT_REQUEST_NOT_OCCUR;
    /* Synchronization processing */
    g_cg_sync_read = TAUB0.TT;
    __syncp();
}

/* Start user code for adding. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */
