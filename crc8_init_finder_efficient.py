import sys

def calculate_crc8(data, polynomial, init_value):
    crc = init_value
    
    # 初始化CRC表
    crc_table = []
    for i in range(256):
        crc_value = i
        for j in range(8):
            if crc_value & 0x80:
                crc_value = (crc_value << 1) ^ polynomial
            else:
                crc_value <<= 1
            crc_value &= 0xFF  # 确保结果是8位
        crc_table.append(crc_value)
    
    # 计算CRC
    for byte in data:
        crc = crc_table[crc ^ byte]
    
    return crc

# 已知多项式
polynomial = 0x2F  # x8+x5+x3+x2+x+1

# 解析示例数据
def parse_hex_string(hex_string):
    # 移除所有空白字符
    hex_string = hex_string.strip()
    
    # 将字符串转换为字节列表
    return [int(hex_string[i:i+2], 16) for i in range(0, len(hex_string), 2)]

# 示例数据
examples = [
    'FF00EFFE',
    'FFC30D00',
    '02120000'
]

# 解析示例数据
parsed_examples = []
for example in examples:
    data_with_crc = parse_hex_string(example)
    data = data_with_crc[:-1]
    expected_crc = data_with_crc[-1]
    parsed_examples.append((data, expected_crc))

print('尝试推导CRC8初始值...')
print(f'多项式: 0x{polynomial:02X} (x8+x5+x3+x2+x+1)')

# 常见的初始值
common_init_values = [0x00, 0xFF, 0x42, 0x55, 0xAA, 0x01, 0x80]
print(f'首先尝试常见的初始值: {[f"0x{v:02X}" for v in common_init_values]}')

# 检查常见初始值
for init_value in common_init_values:
    all_match = True
    
    for data, expected_crc in parsed_examples:
        crc = calculate_crc8(data, polynomial, init_value)
        if crc != expected_crc:
            all_match = False
            break
    
    if all_match:
        print(f'找到匹配的初始值: 0x{init_value:02X}')
        
        # 验证
        print('\n验证所有示例:')
        for i, (data, expected_crc) in enumerate(parsed_examples):
            crc = calculate_crc8(data, polynomial, init_value)
            print(f'示例 {i+1}: {examples[i]}')
            print(f'数据: {[f"0x{b:02X}" for b in data]}')
            print(f'计算CRC8: 0x{crc:02X}')
            print(f'预期CRC8: 0x{expected_crc:02X}')
            print(f'验证结果: {"成功" if crc == expected_crc else "失败"}')
            print()
        
        # 退出循环
        break
else:
    print('常见初始值中没有找到匹配的值，尝试所有可能的初始值...')
    
    # 尝试所有可能的初始值
    for init_value in range(256):
        if init_value in common_init_values:
            continue  # 跳过已经检查过的常见初始值
            
        all_match = True
        
        for data, expected_crc in parsed_examples:
            crc = calculate_crc8(data, polynomial, init_value)
            if crc != expected_crc:
                all_match = False
                break
        
        if all_match:
            print(f'找到匹配的初始值: 0x{init_value:02X}')
            
            # 验证
            print('\n验证所有示例:')
            for i, (data, expected_crc) in enumerate(parsed_examples):
                crc = calculate_crc8(data, polynomial, init_value)
                print(f'示例 {i+1}: {examples[i]}')
                print(f'数据: {[f"0x{b:02X}" for b in data]}')
                print(f'计算CRC8: 0x{crc:02X}')
                print(f'预期CRC8: 0x{expected_crc:02X}')
                print(f'验证结果: {"成功" if crc == expected_crc else "失败"}')
                print()
            
            # 退出循环
            break
    else:
        print('未找到匹配的初始值')
