"""
测试更清晰的位定义解析器
"""

from command_parser import (
    CommandParserFactory,
    ResponseParserFactory
)
import json

def parse_hex_string(hex_string):
    """
    解析十六进制字符串为字节列表
    
    Args:
        hex_string: 十六进制字符串，如"02 50 ff ff ff ff 7a"
        
    Returns:
        字节列表
    """
    # 移除所有空白字符
    hex_string = "".join(hex_string.split())
    
    # 检查字符串长度是否为偶数
    if len(hex_string) % 2 != 0:
        raise ValueError("十六进制字符串长度必须为偶数")
    
    # 将字符串转换为字节列表
    return [int(hex_string[i:i+2], 16) for i in range(0, len(hex_string), 2)]

def test_non_read_response_parser():
    """测试非读操作响应解析器"""
    print("=== 测试非读操作响应解析器 ===")
    
    # 测试用例1：写操作响应
    command_str = "02 50 FF FF FF FF 7A"
    response_str = "E6 A1 A5 7D 03 00 00"
    
    # 解析命令
    command_bytes = parse_hex_string(command_str)
    command_parser = CommandParserFactory.create_parser(command_bytes)
    command_result = command_parser.parse(command_bytes)
    
    # 解析响应
    response_bytes = parse_hex_string(response_str)
    response_parser = ResponseParserFactory.create_parser(command_result["type"])
    response_result = response_parser.parse(response_bytes)
    
    print("响应解析结果:")
    print(json.dumps(response_result, indent=2))
    
    # 打印控制字节的位定义
    print("\n控制字节的位定义:")
    print(json.dumps(response_result["CONTROL_BYTE_BITS"], indent=2))
    
    # 打印SPI_RCVR_STATUS_CHA的位定义
    print("\nSPI_RCVR_STATUS_CHA的位定义:")
    print(json.dumps(response_result["SPI_RCVR_STATUS_CHA_BITS"], indent=2))
    
    # 打印SPI_RCVR_STATUS_CHB的位定义
    print("\nSPI_RCVR_STATUS_CHB的位定义:")
    print(json.dumps(response_result["SPI_RCVR_STATUS_CHB_BITS"], indent=2))

def test_read_response_parser():
    """测试读操作响应解析器"""
    print("\n=== 测试读操作响应解析器 ===")
    
    # 测试用例1：读操作响应
    command_str = "03 11 00 00 00 00 E8"
    response_str = "92 00 00 00 00 83 00"
    
    # 解析命令
    command_bytes = parse_hex_string(command_str)
    command_parser = CommandParserFactory.create_parser(command_bytes)
    command_result = command_parser.parse(command_bytes)
    
    # 解析响应
    response_bytes = parse_hex_string(response_str)
    response_parser = ResponseParserFactory.create_parser(command_result["type"])
    response_result = response_parser.parse(response_bytes)
    
    print("响应解析结果:")
    print(json.dumps(response_result, indent=2))
    
    # 打印控制字节的位定义
    print("\n控制字节的位定义:")
    print(json.dumps(response_result["CONTROL_BYTE_BITS"], indent=2))

if __name__ == "__main__":
    test_non_read_response_parser()
    test_read_response_parser()
