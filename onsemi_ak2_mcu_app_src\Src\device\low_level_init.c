/*===========================================================================*/
/*                                                                           */
/* Project     = F1L StarterKit Sample Software                              */
/* Module      = low_level_init.c                                            */
/* Version     = $Revision: 7295 $                                           */
/*                                                                           */
/*                                                                           */
/*===========================================================================*/
/*                                  COPYRIGHT                                */
/*===========================================================================*/
/* Copyright (c) 2014 by Renesas Electronics Europe GmbH,                    */
/*               a company of the Renesas Electronics Corporation            */
/*===========================================================================*/
/*                                                                           */
/* Warranty Disclaimer                                                       */
/*                                                                           */
/* Because the Product(s) is licensed free of charge, there is no warranty   */
/* of any kind whatsoever and expressly disclaimed and excluded by Renesas,  */
/* either expressed or implied, including but not limited to those for       */
/* non-infringement of intellectual property, merchantability and/or         */
/* fitness for the particular purpose.                                       */
/* Renesas shall not have any obligation to maintain, service or provide bug */
/* fixes for the supplied Product(s) and/or the Application.                 */
/*                                                                           */
/* Each User is solely responsible for determining the appropriateness of    */
/* using the Product(s) and assumes all risks associated with its exercise   */
/* of rights under this Agreement, including, but not limited to the risks   */
/* and costs of program errors, compliance with applicable laws, damage to   */
/* or loss of data, programs or equipment, and unavailability or             */
/* interruption of operations.                                               */
/*                                                                           */
/* Limitation of Liability                                                   */
/*                                                                           */
/* In no event shall Renesas be liable to the User for any incidental,       */
/* consequential, indirect, or punitive damage (including but not limited    */
/* to lost profits) regardless of whether such liability is based on breach  */
/* of contract, tort, strict liability, breach of warranties, failure of     */
/* essential purpose or otherwise and even if advised of the possibility of  */
/* such damages. Renesas shall not be liable for any services or products    */
/* provided by third party vendors, developers or consultants identified or  */
/* referred to the User by Renesas in connection with the Product(s) and/or  */
/* the Application.                                                          */
/*                                                                           */
/*                                                                           */
/*========================================================================== */
/*                                                                           */
/* Source code for the low level init                                       */
/*                                                                           */
/*========================================================================== */
#define EXCEPTION_TABLE_START    (0x000000000)//(0x000020010)

#include <intrinsics.h>
#include "Interrupt.h"

 unsigned char __low_level_init(int peId)
{
//  First set EBASE register address  
//  asm("MOV    _exception_vector_table, R10 ");
//  asm("LDSR   R10, 3, 1 ");
  __LDSR(3,1,EXCEPTION_TABLE_START);   // Set EBASE = EXCEPTION_TABLE_START

// Then set 1 to PSW.EBV due to RBASE!=EBASE  
  asm("STSR   5, R10, 0 ");            // PSW -> R10
  asm("MOV    0x8000, R11 ");          // 
  asm("OR     R11, R10 ");             // PSW.EBV = 1
  asm("LDSR   R10, 5, 0 ");            // PSW <- R10 

  __LDSR(4,1,IRQ_TABLE_START); /* Load IRQ_TABLE_START to INTBP */
  return 1;
}