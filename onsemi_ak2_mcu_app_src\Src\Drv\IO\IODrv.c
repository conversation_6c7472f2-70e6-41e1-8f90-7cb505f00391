/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: IODrv.c
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/
#include "types.h"
#include "IORegCfg.h"
#include "IODrv.h"
#include "ClkDrv.h"
/*===========================================================================*/
/* Functions */
/*===========================================================================*/
/*****************************************************************************
** Function:    R_PORT_SetGpioOutput
** Description: Set Port_Pin to GPIO Output. Initial output level is low.
** Parameter:   Port: Portgroup
**              Pin: Pin Number
**              Level: Output level High or Low
** Return:      None
******************************************************************************/
void R_PORT_SetGpioOutput(enum port_t Port, uint32 Pin, enum level_t Level)
{
    if(Level == Low)
        {
            *PortList[Port].P_Reg &= ~(1u<<Pin);
        }
    else /* Level = High */
        {
            *PortList[Port].P_Reg |= (1u<<Pin);
        }
    *PortList[Port].PM_Reg &= ~(1u<<Pin);
    *PortList[Port].PMC_Reg &= ~(1u<<Pin);
}

/*****************************************************************************
** Function:    R_PORT_ToggleGpioOutput
** Description: Toggles the output level of Port_Pin.
** Parameter:   Port: Portgroup
**              Pin: Pin Number
** Return:      None
******************************************************************************/
void R_PORT_ToggleGpioOutput(enum port_t Port, uint32 Pin)
{
    *PortList[Port].PNOT_Reg |= 1<<Pin;
}

/*****************************************************************************
** Function:    R_PORT_SetGpioInput
** Description: Set Port_Pin to GPIO Input with Port Input Buffer enabled.
** Parameter:   Port: Portgroup
**              Pin: Pin Number
** Return:      None
******************************************************************************/
void R_PORT_SetGpioInput(enum port_t Port, uint32 Pin)
{
    *PortList[Port].PM_Reg |= 1<<Pin;
    *PortList[Port].PIBC_Reg |= 1<<Pin;
    *PortList[Port].PMC_Reg &= ~(1u<<Pin);
}

/*****************************************************************************
** Function:    R_PORT_SetGpioHighZ
** Description: Set Port_Pin to GPIO High Impedant Input (Port Input Buffer disabled).
** Parameter:   Port: Portgroup
**              Pin: Pin Number
** Return:      None
******************************************************************************/
void R_PORT_SetGpioHighZ(enum port_t Port, uint32 Pin)
{
    *PortList[Port].PIBC_Reg &= ~(1<<Pin);
    *PortList[Port].PM_Reg |= 1<<Pin;
}

/*****************************************************************************
** Function:    R_PORT_SetAltFunc
** Description: Configures Port_Pin to the chosen alternative function.
** Parameter:   Port: Portgroup
**              Pin: Pin Number
**              Alt: Alternative Function (Alt1-Alt7)
**              IO: Input/Output direction
** Return:      None
******************************************************************************/
void R_PORT_SetAltFunc(enum port_t Port, uint32 Pin, enum alt_t Alt, enum io_t IO)
{   
    switch(Alt)
    {
        case Alt1:
            *PortList[Port].PFCAE_Reg &= ~(1<<Pin);
            *PortList[Port].PFCE_Reg &= ~(1<<Pin);
            *PortList[Port].PFC_Reg &= ~(1<<Pin);
        break;
        
        case Alt2:
            *PortList[Port].PFCAE_Reg &= ~(1<<Pin);
            *PortList[Port].PFCE_Reg &= ~(1<<Pin);
            *PortList[Port].PFC_Reg |= 1<<Pin;
        break;
        
        case Alt3:
            *PortList[Port].PFCAE_Reg &= ~(1<<Pin);
            *PortList[Port].PFCE_Reg |= 1<<Pin;
            *PortList[Port].PFC_Reg &= ~(1<<Pin);
        break;
        
        case Alt4:
            *PortList[Port].PFCAE_Reg &= ~(1<<Pin);
            *PortList[Port].PFCE_Reg |= 1<<Pin;
            *PortList[Port].PFC_Reg |= 1<<Pin;
        break;
        
        case Alt5:
            *PortList[Port].PFCAE_Reg |= 1<<Pin;
            *PortList[Port].PFCE_Reg &= ~(1<<Pin);
            *PortList[Port].PFC_Reg &= ~(1<<Pin);
        break;
        
        case Alt6:
            *PortList[Port].PFCAE_Reg |= 1<<Pin;
            *PortList[Port].PFCE_Reg &= ~(1<<Pin);
            *PortList[Port].PFC_Reg |= 1<<Pin;
        break;
        
        case Alt7:
            *PortList[Port].PFCAE_Reg |= 1<<Pin;
            *PortList[Port].PFCE_Reg |= 1<<Pin;
            *PortList[Port].PFC_Reg &= ~(1<<Pin);
        break;
        
        default:
        break;
    }
    
    switch(IO)
    {
        case Input:
            *PortList[Port].PM_Reg |= 1<<Pin;
        break;
        
        case Output:
            *PortList[Port].PM_Reg &= ~(1<<Pin);
        break;
        
        default:
        break;
        
    }
    
    *PortList[Port].PMC_Reg |= 1u<<Pin;
}


/*****************************************************************************
** Function:    R_PORT_GetLevel
** Description: Gets the state of a Pin.
** Parameter:   Port: Portgroup
**              Pin: Pin Number
** Return:      0 - Pin is low
**              1 - Pin is high
******************************************************************************/
uint32 R_PORT_GetLevel(enum port_t Port, uint32 Pin)
{
    uint16 PortLevel;
    
    PortLevel = *PortList[Port].PPR_Reg;
    PortLevel &= 1<<Pin;
    
    if(PortLevel == 0)
        {
            return 0;
        }
    else
        {
            return 1;
        }
}

/*****************************************************************************
** Function:    R_PORT_SetOpenDrain
** Description: Sets pin characteristics to open-drain.
** Parameter:   Port: Portgroup
**              Pin: Pin Number
** Return:      None
******************************************************************************/
void R_PORT_SetOpenDrain(enum port_t Port, uint32 Pin)
{
  uint32 PODC_Value;
  PODC_Value = *PortList[Port].PODC_Reg;
  PODC_Value |= 1<<Pin;
  protected_write(PORTPPCMD0,WPROTRPROTS0,*PortList[Port].PODC_Reg,PODC_Value);
}  

/*****************************************************************************
** Function:    R_PORT_SetPushPull
** Description: Sets pin characteristics to push-pull.
** Parameter:   Port: Portgroup
**              Pin: Pin Number
** Return:      None
******************************************************************************/ 
void R_PORT_SetPushPull(enum port_t Port, uint32 Pin)
{
  uint32 PODC_Value;
  PODC_Value = *PortList[Port].PODC_Reg;
  PODC_Value &= ~(1<<Pin);
  protected_write(PORTPPCMD0,WPROTRPROTS0,*PortList[Port].PODC_Reg,PODC_Value);
}

/*****************************************************************************
** Function:    R_PORT_SetAnalogFilter
** Description: Sets analog filter setting.
** Parameter:   fcla_signal_t InputSignal:  R_FCLA_NMI or
**                                          R_FLCA_INTPn (n=0-15)
**
**              FilterSetting
** Return:      None
******************************************************************************/ 
void R_PORT_SetAnalogFilter(enum fcla_signal_t InputSignal, uint8 FilterSetting)
{
    volatile uint8 *loc_FCLA_INTP = (volatile uint8 *)&FCLA0CTL0_INTPL;
    
    if(InputSignal == R_FCLA_NMI)
        {
            FCLA0CTL0_NMI = (FilterSetting&&0x07);
        }
    else
        {
            loc_FCLA_INTP += (InputSignal*4);
            *loc_FCLA_INTP = (FilterSetting&&0x07);
        }
}

/*****************************************************************************
** Function:    R_PORT_EnableIpControl
** Description: Enables Input/Output direction control by IP.
** Parameter:   Port: Portgroup
**              Pin: Pin Number
** Return:      None
******************************************************************************/
void R_PORT_EnableIpControl(enum port_t Port, uint32 Pin)
{
    *PortList[Port].PIPC_Reg |= 1<<Pin;
}

/*****************************************************************************
** Function:    R_PORT_DisableIpControl
** Description: Disables Input/Output direction control by IP.
** Parameter:   Port: Portgroup
**              Pin: Pin Number
** Return:      None
******************************************************************************/
void R_PORT_DisableIpControl(enum port_t Port, uint32 Pin)
{
    *PortList[Port].PIPC_Reg &= ~(1<<Pin);
}


