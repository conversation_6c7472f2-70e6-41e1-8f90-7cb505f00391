/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * MapEchoFilterAndSigGroup_prg: 原始回波阈值过滤及信号组提取处理
 * Created on: 2023-12-22 20:36
 * Original designer: AntonyFang
 ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "MapEchoFilterAndSigGroup_int.h"
#include "SnsRawData_cfg.h"
#if !VS_SIMULATE_ENABLE
#include "TimerManage.h"
#endif
#include "SnsRawData_Int.h"
#include "MapRawDataCalib.h"
#include "CAN_AppSignalManage.h"
#include "Sns_install_Coordinate.h"
#include "PAS_MAP_SignalManage.h"



/******************************************************************************/
/******************************************************************************/
/****************************** Private Definition ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Public Definition *****************************/
/******************************************************************************/
/******************************************************************************/
MapSigGroupDataCacheType GstrMapSigGroupDataCache[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];
MapSnsRealTimeDisType    GstrMapSnsRealTimeDis[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];
ParkingGuidenceDataType GstrParkingGuidenceData;

SnsCoverDataType    GstrSnsCoverData[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];
uint8 Gu8SnsBeCoveredFlag[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];

SnsCoverDataType    GstrSnsCoverDataInit = 
{
    .fCarDrvDisStart = 0.0,
    .fCarMoveDis = 0.0,
    .u16StdDis[0] = 65535,
    .u16StdDis[1] = 65535,
    .u16StdDis[2] = 65535,
    .u16ChirpDis[0] = 65535,
    .u16ChirpDis[1] = 65535,
    .u16ChirpDis[2] = 65535,
    .u16OutRangeCnt = 0,
    .u8CarCoverFlag = 0,
    .u8ContinueChirpCnt = 0
};

/******************************************************************************/
/******************************************************************************/
/****************************** Private Function ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/******************************* Public Function ******************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************
 * 函数名称: SnsCarCovrDataInit
 * 
 * 功能描述: 探头被车衣覆盖数据初始化
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-06-03 14:50   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsCarCovrDataInit(void)
{
    uint8 i,j;

    for(i = 0; i < 2; i++)
    {
        for(j = 0; j < 6; j++)
        {
            GstrSnsCoverData[i][j] = GstrSnsCoverDataInit;
            Gu8SnsBeCoveredFlag[i][j] = 0;
        }
    }
    for(i = SNS_INX_01; i < SNS_INX_NUM; i++)
    {
        PdcSignal_WriteSnsBeCoveredFlagToDTC(0,i);
    } 
}


/******************************************************************************
 * 函数名称: SnsCarCovrJudge
 * 
 * 功能描述: 车衣覆盖的检测
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-06-03 14:55   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsCarCoverJudge(uint8 LeGroup,uint8 LenuPDCSnsCh,PDCSnsRawDataType *LpStrPDCSnsRawData)
{
    SnsCoverDataType *LpstrSnsCoverData;
    uint16 Lu16MasterDis,Lu16MasterHeight,Lu16MasterDisSub;
    uint8 Lu8CoverCnt = 0;
    uint8 i;
    uint16 Lu16CarSnsCoverMovDisSet;
    uint32 Lu32StdDisSum;
    uint16 Lu16StdDisAver;
    uint8 Lu8Cnt;
    uint8 Lu8HighStdDisSubLess5cmFlag = 1;
    uint16 Lu16DisSub;
    uint8 Lu8ExitCntSet;


    LpstrSnsCoverData = &GstrSnsCoverData[LeGroup][LenuPDCSnsCh];

    if(Gu16CarSpdForSnsUse < SNS_EXIT_SPD)
    {
        Lu8ExitCntSet = SNS_COVER_LOW_SPD_EXIT_CNT;
    }
    else
    {
        Lu8ExitCntSet = SNS_COVER_HIGH_SPD_EXIT_CNT;
    }
    

    if(Gu16CarSpdForSnsUse < SNS_COVER_JUDGE_ACTIVE_LOW_SPD)
    {
        if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LenuPDCSnsCh] == PDC_SNS_MEAS_STD)
        {
            LpstrSnsCoverData->u16StdDis[2] = LpstrSnsCoverData->u16StdDis[1];
            LpstrSnsCoverData->u16StdDis[1] = LpstrSnsCoverData->u16StdDis[0];
            Lu16MasterDis = LpStrPDCSnsRawData->u16MasterDis[LenuPDCSnsCh][0];
            Lu16MasterHeight = LpStrPDCSnsRawData->u16MasterHeight[LenuPDCSnsCh][0];
            if(Lu16MasterDis <= SNS_COVER_STD_NEAR_DIS_0_20)
            {
                if(Lu16MasterHeight > SNS_COVER_STD_NEAR_HEIGHT_0_20)
                {
                    LpstrSnsCoverData->u16StdDis[0] = Lu16MasterDis;
                }
                else
                {
                    LpstrSnsCoverData->u16StdDis[0] = MAP_SIG_GROUP_INVALID_DIS;
                }
            }
            else if(Lu16MasterDis <= SNS_COVER_STD_NEAR_DIS_20_25)
            {
                if(Lu16MasterHeight > SNS_COVER_STD_NEAR_HEIGHT_20_25)
                {
                    LpstrSnsCoverData->u16StdDis[0] = Lu16MasterDis;
                }
                else
                {
                    LpstrSnsCoverData->u16StdDis[0] = MAP_SIG_GROUP_INVALID_DIS;
                }
            }
            else if(Lu16MasterDis <= SNS_COVER_STD_NEAR_DIS_25_30)
            {
                if(Lu16MasterHeight > SNS_COVER_STD_NEAR_HEIGHT_25_30)
                {
                    LpstrSnsCoverData->u16StdDis[0] = Lu16MasterDis;
                }
                else
                {
                    LpstrSnsCoverData->u16StdDis[0] = MAP_SIG_GROUP_INVALID_DIS;
                }
            }
            else
            {
                LpstrSnsCoverData->u16StdDis[0] = MAP_SIG_GROUP_INVALID_DIS;
            }
        }
        else
        {
            LpstrSnsCoverData->u16ChirpDis[2] = LpstrSnsCoverData->u16ChirpDis[1];
            LpstrSnsCoverData->u16ChirpDis[1] = LpstrSnsCoverData->u16ChirpDis[0];
            Lu16MasterDis = LpStrPDCSnsRawData->u16MasterDis[LenuPDCSnsCh][0];
            Lu16MasterHeight = LpStrPDCSnsRawData->u16MasterHeight[LenuPDCSnsCh][0];
            if((Lu16MasterDis < SNS_COVER_CHIRP_NEAR_DIS)&&(Lu16MasterHeight > SNS_COVER_CHIRP_NEAR_HEIGHT))
            {
                LpstrSnsCoverData->u16ChirpDis[0] = SNS_COVER_CHIRP_NEAR_DIS;
            }
            else
            {
                LpstrSnsCoverData->u16ChirpDis[0] = MAP_SIG_GROUP_INVALID_DIS;
            }
        }

        Lu32StdDisSum = 0;
        Lu8Cnt = 0;
        for(i = 0; i < SNS_COVER_DIS_BUF_CNT; i++)
        {
            if(LpstrSnsCoverData->u16StdDis[i] <= SNS_COVER_STD_NEAR_DIS_25_30)
            {
                Lu32StdDisSum += LpstrSnsCoverData->u16StdDis[i];
                Lu8Cnt++;
            }
        }
        if(Lu8Cnt == 0)
        {
            Lu16StdDisAver = SNS_COVER_STD_NEAR_DIS_25_30;
        }
        else
        {
            Lu16StdDisAver = Lu32StdDisSum/Lu8Cnt;
        }
        
        if((LenuPDCSnsCh == 0x00)||(LenuPDCSnsCh == 0x05))
        {
            if(Lu16StdDisAver <= SNS_COVER_STD_NEAR_DIS_0_20)
            {
                Lu16CarSnsCoverMovDisSet = SNS_LOW_SPD_SIDE_SNS_DIS_20CM;
            }
            else
            {
                Lu16CarSnsCoverMovDisSet = SNS_LOW_SPD_SIDE_SNS_DIS_30CM;
            }
        }
        else
        {
            Lu16CarSnsCoverMovDisSet = SNS_LOW_SPD_F_R_SNS_DIS;
        }
    }
    else if(Gu16CarSpdForSnsUse > SNS_COVER_JUDGE_ACTIVE_HIGH_SPD)
    {
        if((LenuPDCSnsCh == 0x00)||(LenuPDCSnsCh == 0x05))
        {
            if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LenuPDCSnsCh] == PDC_SNS_MEAS_STD)
            {
                LpstrSnsCoverData->u16StdDis[2] = LpstrSnsCoverData->u16StdDis[1];
                LpstrSnsCoverData->u16StdDis[1] = LpstrSnsCoverData->u16StdDis[0];
                Lu16MasterDis = LpStrPDCSnsRawData->u16MasterDis[LenuPDCSnsCh][0];
                Lu16MasterHeight = LpStrPDCSnsRawData->u16MasterHeight[LenuPDCSnsCh][0];
                if(Lu16MasterDis <= SNS_COVER_STD_NEAR_DIS_0_20)
                {
                    if(Lu16MasterHeight > SNS_COVER_STD_NEAR_HEIGHT_0_20)
                    {
                        LpstrSnsCoverData->u16StdDis[0] = Lu16MasterDis;
                    }
                    else
                    {
                        LpstrSnsCoverData->u16StdDis[0] = MAP_SIG_GROUP_INVALID_DIS;
                    }
                }
                else if(Lu16MasterDis <= SNS_COVER_STD_NEAR_DIS_20_25)
                {
                    if(Lu16MasterHeight > SNS_COVER_STD_NEAR_HEIGHT_20_25)
                    {
                        LpstrSnsCoverData->u16StdDis[0] = Lu16MasterDis;
                    }
                    else
                    {
                        LpstrSnsCoverData->u16StdDis[0] = MAP_SIG_GROUP_INVALID_DIS;
                    }
                }
                else if(Lu16MasterDis <= SNS_COVER_STD_NEAR_DIS_25_30)
                {
                    if(Lu16MasterHeight > SNS_COVER_STD_NEAR_HEIGHT_25_30)
                    {
                        LpstrSnsCoverData->u16StdDis[0] = Lu16MasterDis;
                    }
                    else
                    {
                        LpstrSnsCoverData->u16StdDis[0] = MAP_SIG_GROUP_INVALID_DIS;
                    }
                }
                else
                {
                    LpstrSnsCoverData->u16StdDis[0] = MAP_SIG_GROUP_INVALID_DIS;
                }
            }
            LpstrSnsCoverData->u16ChirpDis[2] = MAP_SIG_GROUP_INVALID_DIS;
            LpstrSnsCoverData->u16ChirpDis[1] = MAP_SIG_GROUP_INVALID_DIS;
            LpstrSnsCoverData->u16ChirpDis[0] = MAP_SIG_GROUP_INVALID_DIS;

        }
        else
        {
            LpstrSnsCoverData->u16StdDis[0] = MAP_SIG_GROUP_INVALID_DIS;
            LpstrSnsCoverData->u16StdDis[1] = MAP_SIG_GROUP_INVALID_DIS;
            LpstrSnsCoverData->u16StdDis[2] = MAP_SIG_GROUP_INVALID_DIS;
            
            LpstrSnsCoverData->u16ChirpDis[0] = MAP_SIG_GROUP_INVALID_DIS;
            LpstrSnsCoverData->u16ChirpDis[1] = MAP_SIG_GROUP_INVALID_DIS;
            LpstrSnsCoverData->u16ChirpDis[2] = MAP_SIG_GROUP_INVALID_DIS;
        }
        
        Lu32StdDisSum = 0;
        Lu8Cnt = 0;
        for(i = 0; i < SNS_COVER_DIS_BUF_CNT; i++)
        {
            if(LpstrSnsCoverData->u16StdDis[i] <= SNS_COVER_STD_NEAR_DIS_25_30)
            {
                Lu32StdDisSum += LpstrSnsCoverData->u16StdDis[i];
                Lu8Cnt++;
            }
        }
        if(Lu8Cnt == 0)
        {
            Lu16StdDisAver = SNS_COVER_STD_NEAR_DIS_25_30;
        }
        else
        {
            Lu16StdDisAver = Lu32StdDisSum/Lu8Cnt;
        }
        
        if(Lu16StdDisAver <= SNS_COVER_STD_NEAR_DIS_0_20)
        {
            Lu16CarSnsCoverMovDisSet = SNS_HIGH_SPD_SNS_DIS_20_CM;
        }
        else
        {
            Lu16CarSnsCoverMovDisSet = SNS_HIGH_SPD_SNS_DIS_30_CM;
        }

        /* 判断两帧之间的差值 */
        if((LpstrSnsCoverData->u16StdDis[0] <= SNS_COVER_STD_NEAR_DIS_25_30)\
            &&(LpstrSnsCoverData->u16StdDis[1] <= SNS_COVER_STD_NEAR_DIS_25_30))
        {
            Lu16DisSub = ABS(LpstrSnsCoverData->u16StdDis[0],LpstrSnsCoverData->u16StdDis[1]);
            if(Lu16DisSub < 50)
            {
                Lu8HighStdDisSubLess5cmFlag = 1;
            }
            else
            {
                Lu8HighStdDisSubLess5cmFlag = 0;
            }
        }
        else
        {
            Lu8HighStdDisSubLess5cmFlag = 0;
        }
    }
    else
    {
        Lu16CarSnsCoverMovDisSet = SNS_HIGH_SPD_SNS_DIS_30_CM;
        LpstrSnsCoverData->u16StdDis[0] = MAP_SIG_GROUP_INVALID_DIS;
        LpstrSnsCoverData->u16StdDis[1] = MAP_SIG_GROUP_INVALID_DIS;
        LpstrSnsCoverData->u16StdDis[2] = MAP_SIG_GROUP_INVALID_DIS;
        
        LpstrSnsCoverData->u16ChirpDis[0] = MAP_SIG_GROUP_INVALID_DIS;
        LpstrSnsCoverData->u16ChirpDis[1] = MAP_SIG_GROUP_INVALID_DIS;
        LpstrSnsCoverData->u16ChirpDis[2] = MAP_SIG_GROUP_INVALID_DIS;
    }

    if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LenuPDCSnsCh] == PDC_SNS_MEAS_STD)
    {
        LpstrSnsCoverData->u8ContinueChirpCnt = 0;
    }
    else
    {
        if(LpstrSnsCoverData->u8ContinueChirpCnt < 4)
        {
            LpstrSnsCoverData->u8ContinueChirpCnt++;
        }
        else
        {
            LpstrSnsCoverData->u16StdDis[0] = MAP_SIG_GROUP_INVALID_DIS;
            LpstrSnsCoverData->u16StdDis[1] = MAP_SIG_GROUP_INVALID_DIS;
            LpstrSnsCoverData->u16StdDis[2] = MAP_SIG_GROUP_INVALID_DIS;
        }
    }



    for(i = 0; i < SNS_COVER_DIS_BUF_CNT; i++)
    {
        if((LpstrSnsCoverData->u16StdDis[i] <= SNS_COVER_STD_NEAR_DIS_25_30)&&(LpstrSnsCoverData->u16ChirpDis[i] == MAP_SIG_GROUP_INVALID_DIS))
        {
            Lu8CoverCnt++;
        }
    }
    
    if(LpstrSnsCoverData->u8CarCoverFlag)
    {
        if(Lu8CoverCnt >= 2)
        {
            LpstrSnsCoverData->u16OutRangeCnt = 0;
        }
        else
        {
            LpstrSnsCoverData->u16OutRangeCnt++;
        }
        if(LpstrSnsCoverData->u16OutRangeCnt > Lu8ExitCntSet)
        {
            LpstrSnsCoverData->u8CarCoverFlag = 0;
            LpstrSnsCoverData->u16OutRangeCnt = 0;
        }
    }
    else
    {
        if((Lu8CoverCnt >= 2)&&(Lu8HighStdDisSubLess5cmFlag))
        {
            LpstrSnsCoverData->fCarMoveDis = GstrPDCSnsUseOdo.fDrvDis - LpstrSnsCoverData->fCarDrvDisStart;
        }
        else
        {
            LpstrSnsCoverData->fCarDrvDisStart = GstrPDCSnsUseOdo.fDrvDis;
            LpstrSnsCoverData->fCarMoveDis = 0;
        }
        if(LpstrSnsCoverData->fCarMoveDis > Lu16CarSnsCoverMovDisSet)
        {
            LpstrSnsCoverData->u8CarCoverFlag = 1;
            LpstrSnsCoverData->u16OutRangeCnt = 0;
        }
    }
    Gu8SnsBeCoveredFlag[LeGroup][LenuPDCSnsCh] = LpstrSnsCoverData->u8CarCoverFlag;

    PdcSiagnal_SnsInxType LenuSnsInx;

    if(LeGroup == 0x00)
    {
        LenuSnsInx = (PdcSiagnal_SnsInxType)LenuPDCSnsCh;
    }
    else
    {
        LenuSnsInx = (PdcSiagnal_SnsInxType)(11 - LenuPDCSnsCh);
    }
    PdcSignal_WriteSnsBeCoveredFlagToDTC(LpstrSnsCoverData->u8CarCoverFlag,LenuSnsInx);

    
#if 0
    if((LeGroup == 0x01)&&(LenuPDCSnsCh == 0x00))
    {
#if 1
        VS_PRINT("RLS Cover,Spd:%d,Time:%.3f,Mode:%d,Dis:%d,Height:%d,CarMoveDis:%.3f,RLS_Flag:%d,OutRangeCnt:%d\r\n",Gu16CarSpdForSnsUse,GfMessageTime,LpStrPDCSnsRawData->enuPDCSnsMeasType[LenuPDCSnsCh],\
            LpStrPDCSnsRawData->u16MasterDis[LenuPDCSnsCh][0],LpStrPDCSnsRawData->u16MasterHeight[LenuPDCSnsCh][0],\
            LpstrSnsCoverData->fCarMoveDis,LpstrSnsCoverData->u8CarCoverFlag,LpstrSnsCoverData->u16OutRangeCnt);
#else
        VS_PRINT("RLS Cover,Spd:%d,Time:%.3f,Mode:%d,Dis:%d,Height:%d,CarMoveDis:%.3f,RLS_Flag:%d,Less5cmFlag:%d,CoverCnt:%d,OutRangeCnt:%d\r\n",Gu16CarSpdForSnsUse,GfMessageTime,LpStrPDCSnsRawData->enuPDCSnsMeasType[LenuPDCSnsCh],\
            LpStrPDCSnsRawData->u16MasterDis[LenuPDCSnsCh][0],LpStrPDCSnsRawData->u16MasterHeight[LenuPDCSnsCh][0],\
            LpstrSnsCoverData->fCarMoveDis,LpstrSnsCoverData->u8CarCoverFlag,Lu8HighStdDisSubLess5cmFlag,Lu8CoverCnt,\
            LpstrSnsCoverData->u16OutRangeCnt);
#endif
    }
#endif
}



/******************************************************************************
 * 函数名称: MapSnsRealTimeDisInit
 * 
 * 功能描述: Map模块探头实时距离初始化
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-28 14:36   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void MapSnsRealTimeDisInit(PDCSnsGroupType LeGroup)
{
    PDCSnsChannelType LePDCSnsCh;
    for(LePDCSnsCh = PDC_SNS_CH_FLS_RLS;LePDCSnsCh < PDC_SNS_CH_NUM;LePDCSnsCh++)
    {
        GstrMapSnsRealTimeDis[LeGroup][LePDCSnsCh].eMeasType = PDC_SNS_MEAS_IDLE;
        GstrMapSnsRealTimeDis[LeGroup][LePDCSnsCh].u16MasterHeight = 0;
        GstrMapSnsRealTimeDis[LeGroup][LePDCSnsCh].u16MasDis = MAP_REAL_TIME_INVALID_DIS;
        GstrMapSnsRealTimeDis[LeGroup][LePDCSnsCh].u16LeftDis = MAP_REAL_TIME_INVALID_DIS;
        GstrMapSnsRealTimeDis[LeGroup][LePDCSnsCh].u16RightDis = MAP_REAL_TIME_INVALID_DIS;
        GstrMapSnsRealTimeDis[LeGroup][LePDCSnsCh].u16ChirpMasDis = MAP_REAL_TIME_INVALID_DIS;
        GstrMapSnsRealTimeDis[LeGroup][LePDCSnsCh].u16ChirpLeftDis = MAP_REAL_TIME_INVALID_DIS;
        GstrMapSnsRealTimeDis[LeGroup][LePDCSnsCh].u16ChirpRightDis = MAP_REAL_TIME_INVALID_DIS;        
        GstrMapSnsRealTimeDis[LeGroup][LePDCSnsCh].u8RealDisUpdateFlag = 0;
        GstrMapSnsRealTimeDis[LeGroup][LePDCSnsCh].u8MasSecondFlag = 0;
        GstrMapSnsRealTimeDis[LeGroup][LePDCSnsCh].u8LeftSecondFlag = 0;
        GstrMapSnsRealTimeDis[LeGroup][LePDCSnsCh].u8RightSecondFlag = 0;        
    }
}


/******************************************************************************
 * 函数名称: MapSnsRealTimeDis_PDC_Sns_Init
 * 
 * 功能描述: Map模块PDC探头实时距离初始化
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-28 14:37   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void MapSnsRealTimeDis_PDC_Sns_Init(PDCSnsGroupType LeGroup)
{
    PDCSnsChannelType LePDCSnsCh;
    for(LePDCSnsCh = PDC_SNS_CH_FL_RL;LePDCSnsCh < PDC_SNS_CH_FRS_RRS;LePDCSnsCh++)
    {
        GstrMapSnsRealTimeDis[LeGroup][LePDCSnsCh].eMeasType = PDC_SNS_MEAS_IDLE;
        GstrMapSnsRealTimeDis[LeGroup][LePDCSnsCh].u16MasterHeight = 0;
        GstrMapSnsRealTimeDis[LeGroup][LePDCSnsCh].u16MasDis = MAP_REAL_TIME_INVALID_DIS;
        GstrMapSnsRealTimeDis[LeGroup][LePDCSnsCh].u16LeftDis = MAP_REAL_TIME_INVALID_DIS;
        GstrMapSnsRealTimeDis[LeGroup][LePDCSnsCh].u16RightDis = MAP_REAL_TIME_INVALID_DIS;
        GstrMapSnsRealTimeDis[LeGroup][LePDCSnsCh].u16ChirpMasDis = MAP_REAL_TIME_INVALID_DIS;
        GstrMapSnsRealTimeDis[LeGroup][LePDCSnsCh].u16ChirpLeftDis = MAP_REAL_TIME_INVALID_DIS;
        GstrMapSnsRealTimeDis[LeGroup][LePDCSnsCh].u16ChirpRightDis = MAP_REAL_TIME_INVALID_DIS;        
        GstrMapSnsRealTimeDis[LeGroup][LePDCSnsCh].u8RealDisUpdateFlag = 0;
        GstrMapSnsRealTimeDis[LeGroup][LePDCSnsCh].u8MasSecondFlag = 0;
        GstrMapSnsRealTimeDis[LeGroup][LePDCSnsCh].u8LeftSecondFlag = 0;
        GstrMapSnsRealTimeDis[LeGroup][LePDCSnsCh].u8RightSecondFlag = 0;   
    }
}


/******************************************************************************
 * 函数名称: MapSigGroupDataClear
 * 
 * 功能描述: Map信号组数据清零
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-22 20:56   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void MapSigGroupDataClear(PDCSnsGroupType LeGroup,PDCSnsChannelType LePDCSnsCh,Map_CycleType LenuCurIndex)
{
    uint8 i;
    MapSigGroupDataCacheType *LpstrMapSigGroupDataCache;
    
    LpstrMapSigGroupDataCache = &GstrMapSigGroupDataCache[LeGroup][LePDCSnsCh];

    LpstrMapSigGroupDataCache->u8SigGroupUpdateFlag = 0;
    LpstrMapSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub = 0;
    LpstrMapSigGroupDataCache->SysDataBuf[LenuCurIndex].u32SysTime = 0;
    LpstrMapSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType = PDC_SNS_MEAS_IDLE;
    LpstrMapSigGroupDataCache->SysDataBuf[LenuCurIndex].u8SnsNoiseCnt = 0;

    LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u8SigGroupCnt = 0;
    LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u8SigGroupCnt = 0;
    LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u8SigGroupCnt = 0;

    for(i = 0; i < MAX_NUM_OF_MAP_SIG_GROUP; i++)
    {
        LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u8GroupEchoCnt[i] = 0;
        LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[i] = SNS_INVALID_DIS;
        LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[i] = SNS_INVALID_HEIGHT;
        LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[i] = SNS_INVALID_DIS;
        LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[i] = SNS_INVALID_HEIGHT;
        LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[i] = SNS_INVALID_DIS;
        LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[i] = SNS_INVALID_HEIGHT;
        LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[i] = OBJ_NONE_TYPE;
        
        LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u8GroupEchoCnt[i] = 0;
        LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[i] = SNS_INVALID_DIS;
        LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[i] = SNS_INVALID_HEIGHT;
        LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[i] = SNS_INVALID_DIS;
        LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[i] = SNS_INVALID_HEIGHT;
        LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[i] = SNS_INVALID_DIS;
        LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[i] = SNS_INVALID_HEIGHT;
        LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[i] = OBJ_NONE_TYPE;

        LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u8GroupEchoCnt[i] = 0;
        LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[i] = SNS_INVALID_DIS;
        LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[i] = SNS_INVALID_HEIGHT;
        LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[i] = SNS_INVALID_DIS;
        LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[i] = SNS_INVALID_HEIGHT;
        LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[i] = SNS_INVALID_DIS;
        LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[i] = SNS_INVALID_HEIGHT;
        LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[i] = OBJ_NONE_TYPE;
    }
}

/******************************************************************************
 * 函数名称: MapSigGroupDataCacheInit
 * 
 * 功能描述: Map信号缓存数据初始化
 * 
 * 输入参数:LeGroup--探头分组
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-22 20:57   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void MapSigGroupDataCacheInit(PDCSnsGroupType LeGroup)
{
    PDCSnsChannelType LePDCSnsCh;
    for(LePDCSnsCh = PDC_SNS_CH_FLS_RLS;LePDCSnsCh < PDC_SNS_CH_NUM;LePDCSnsCh++)
    {
        GstrMapSigGroupDataCache[LeGroup][LePDCSnsCh].enuCurIndex = MAP_CYCLE_2;      /* 初始化时Index设置最大值，后续更新时直接到SNS_CYCLE_0 */
        GstrMapSigGroupDataCache[LeGroup][LePDCSnsCh].u8RecordCnt = 0;
        GstrMapSigGroupDataCache[LeGroup][LePDCSnsCh].u8SigGroupUpdateFlag = INVALID;
        MapSigGroupDataClear(LeGroup,LePDCSnsCh,MAP_CYCLE_0);
        MapSigGroupDataClear(LeGroup,LePDCSnsCh,MAP_CYCLE_1);
        MapSigGroupDataClear(LeGroup,LePDCSnsCh,MAP_CYCLE_2);
    }
}

/******************************************************************************
 * 函数名称: MapSigGroupDataCache_PDC_Sns_Init
 * 
 * 功能描述: 清除Map PDC探头的信号组数据
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-22 20:58   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void MapSigGroupDataCache_PDC_Sns_Init(PDCSnsGroupType LeGroup)
{
    PDCSnsChannelType LePDCSnsCh;
    for(LePDCSnsCh = PDC_SNS_CH_FL_RL;LePDCSnsCh < PDC_SNS_CH_FRS_RRS;LePDCSnsCh++)
    {
        GstrMapSigGroupDataCache[LeGroup][LePDCSnsCh].enuCurIndex = MAP_CYCLE_2;      /* 初始化时Index设置最大值，后续更新时直接到SNS_CYCLE_0 */
        GstrMapSigGroupDataCache[LeGroup][LePDCSnsCh].u8RecordCnt = 0;
        GstrMapSigGroupDataCache[LeGroup][LePDCSnsCh].u8SigGroupUpdateFlag = INVALID;
        MapSigGroupDataClear(LeGroup,LePDCSnsCh,MAP_CYCLE_0);
        MapSigGroupDataClear(LeGroup,LePDCSnsCh,MAP_CYCLE_1);
        MapSigGroupDataClear(LeGroup,LePDCSnsCh,MAP_CYCLE_2);
    }
}

/******************************************************************************
 * 函数名称: MapSigGroupDataPowerOnInit
 * 
 * 功能描述: Map信号缓存数据上电初始化
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-22 21:00   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void MapSigGroupDataPowerOnInit(void)
{
    MapSigGroupDataCacheInit(PDC_SNS_GROUP_FRONT);
    MapSigGroupDataCacheInit(PDC_SNS_GROUP_REAR);
    MapSnsRealTimeDisInit(PDC_SNS_GROUP_FRONT);
    MapSnsRealTimeDisInit(PDC_SNS_GROUP_REAR);
    SnsCarCovrDataInit();
}

/******************************************************************************
 * 函数名称: SetMapSigGroupDataUpdateFlag
 * 
 * 功能描述: 置位回波信号组更新标志，用于侧雷达等模块调度使用
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-22 21:01   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SetMapSigGroupDataUpdateFlag(PDCSnsGroupType LeGroup,PDCSnsChannelType LePDCSnsCh)
{
    MapSigGroupDataCacheType *LpstrMapSigGroupDataCache;

    LpstrMapSigGroupDataCache = &GstrMapSigGroupDataCache[LeGroup][LePDCSnsCh];
    LpstrMapSigGroupDataCache->u8SigGroupUpdateFlag = 1;
}

/******************************************************************************
 * 函数名称: ClearMapSigGroupDataUpdateFlag
 * 
 * 功能描述: 置位回波信号组更新标志，用于侧雷达等模块调度使用
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-22 21:02   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void ClearMapSigGroupDataUpdateFlag(PDCSnsGroupType LeGroup,PDCSnsChannelType LePDCSnsCh)
{
    MapSigGroupDataCacheType *LpstrMapSigGroupDataCache;

    LpstrMapSigGroupDataCache = &GstrMapSigGroupDataCache[LeGroup][LePDCSnsCh];
    LpstrMapSigGroupDataCache->u8SigGroupUpdateFlag = 0;
}

Map_CycleType MapSigGroupGetLastIndex(Map_CycleType LenuCurIndex)
{
    if(LenuCurIndex)
    {
        return (LenuCurIndex - 1u);
    }
    return MAP_CYCLE_NUM-1;
}


/******************************************************************************
 * 函数名称: UpdateMapSnsRealTimeDis
 * 
 * 功能描述: 更新Map模块实时探测距离
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-28 14:46   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void UpdateMapSnsRealTimeDis(PDCSnsGroupType LeGroup,PDCSnsChannelType LePDCSnsCh)
{
    MapSigGroupDataCacheType *LpstrMapSigGroup;
    MapSnsRealTimeDisType *LpstrMapSnsRealTimeDis;
    Map_CycleType LenuCurInx,LenuLastInx,LenuLast2Inx;
    PDCSnsMeasTypeType     LenuMeasType;
    uint8 Lu8AllChirpModeFlag = 0;
    LpstrMapSigGroup = &GstrMapSigGroupDataCache[LeGroup][LePDCSnsCh];
    LpstrMapSnsRealTimeDis = &GstrMapSnsRealTimeDis[LeGroup][LePDCSnsCh];

    LenuCurInx = LpstrMapSigGroup->enuCurIndex;
    LenuLastInx = MapSigGroupGetLastIndex(LenuCurInx);
    LenuLast2Inx = MapSigGroupGetLastIndex(LenuLastInx);


    LenuMeasType = LpstrMapSigGroup->SysDataBuf[LenuCurInx].eMeasType;

    if((LenuMeasType != PDC_SNS_MEAS_STD)&&(LpstrMapSigGroup->SysDataBuf[LenuLastInx].eMeasType != PDC_SNS_MEAS_STD)\
        &&(LpstrMapSigGroup->SysDataBuf[LenuLast2Inx].eMeasType != PDC_SNS_MEAS_STD))
    {
        Lu8AllChirpModeFlag = 1;
    }

    LpstrMapSnsRealTimeDis->eMeasType = LenuMeasType;

    /* 更新信号组最近的一个距离数据到实时探头距离中 */
    if(LpstrMapSnsRealTimeDis->u16MasDis == MAP_REAL_TIME_INVALID_DIS)
    {
        if(Lu8AllChirpModeFlag)
        {
            if(LpstrMapSigGroup->MasterBuf[LenuCurInx].u16ActualDis[0] < MAP_FAR_REAL_TIME_DIS)
            {
                LpstrMapSnsRealTimeDis->u16MasDis = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16ActualDis[0];
                LpstrMapSnsRealTimeDis->u16MasterHeight = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16MaxHeight[0];
                if(LpstrMapSigGroup->MasterBuf[LenuCurInx].u16SecondEchoDis[0] < MAP_FAR_REAL_TIME_DIS)
                {
                    LpstrMapSnsRealTimeDis->u8MasSecondFlag = 1;
                }
                else
                {
                    LpstrMapSnsRealTimeDis->u8MasSecondFlag = 0;
                }
            }
            else
            {
                LpstrMapSnsRealTimeDis->u16MasDis = MAP_REAL_TIME_INVALID_DIS;
                LpstrMapSnsRealTimeDis->u16MasterHeight = 0;
                LpstrMapSnsRealTimeDis->u8MasSecondFlag = 0;
            }
        }
        else
        {
            if(LpstrMapSigGroup->MasterBuf[LenuCurInx].u16ActualDis[0] < MAP_FAR_REAL_TIME_DIS)
            {
                LpstrMapSnsRealTimeDis->u16MasDis = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16ActualDis[0];
                LpstrMapSnsRealTimeDis->u16MasterHeight = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16MaxHeight[0];
                if(LpstrMapSigGroup->MasterBuf[LenuCurInx].u16SecondEchoDis[0] < MAP_FAR_REAL_TIME_DIS)
                {
                    LpstrMapSnsRealTimeDis->u8MasSecondFlag = 1;
                }
                else
                {
                    LpstrMapSnsRealTimeDis->u8MasSecondFlag = 0;
                }
            }
            else
            {
                LpstrMapSnsRealTimeDis->u16MasDis = MAP_REAL_TIME_INVALID_DIS;
                LpstrMapSnsRealTimeDis->u16MasterHeight = 0;
                LpstrMapSnsRealTimeDis->u8MasSecondFlag = 0;
            }
        }
    }
    else
    {
        if(Lu8AllChirpModeFlag)
        {
            if(LpstrMapSigGroup->MasterBuf[LenuCurInx].u16ActualDis[0] < MAP_FAR_REAL_TIME_DIS)
            {
                LpstrMapSnsRealTimeDis->u16MasDis = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16ActualDis[0];
                LpstrMapSnsRealTimeDis->u16MasterHeight = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16MaxHeight[0];
                if(LpstrMapSigGroup->MasterBuf[LenuCurInx].u16SecondEchoDis[0] < MAP_FAR_REAL_TIME_DIS)
                {
                    LpstrMapSnsRealTimeDis->u8MasSecondFlag = 1;
                }
                else
                {
                    LpstrMapSnsRealTimeDis->u8MasSecondFlag = 0;
                }
            }
            else
            {
                LpstrMapSnsRealTimeDis->u16MasDis = MAP_REAL_TIME_INVALID_DIS;
                LpstrMapSnsRealTimeDis->u16MasterHeight = 0;
                LpstrMapSnsRealTimeDis->u8MasSecondFlag = 0;
            }
        }
        else
        {
#if 0
            if(LpstrMapSnsRealTimeDis->u16MasDis < MAP_NEAR_REAL_TIME_DIS)
            {
                /* 近距离模式下，只使用定频的数据进行更新 */
                if(LenuMeasType == PDC_SNS_MEAS_STD)
                {
                    if(LpstrMapSigGroup->MasterBuf[LenuCurInx].u16ActualDis[0] < MAP_FAR_REAL_TIME_DIS)
                    {
                        LpstrMapSnsRealTimeDis->u16MasDis = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16ActualDis[0];
                        LpstrMapSnsRealTimeDis->u16MasterHeight = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16MaxHeight[0];
                    }
                    else
                    {
                        LpstrMapSnsRealTimeDis->u16MasDis = MAP_REAL_TIME_INVALID_DIS;
                        LpstrMapSnsRealTimeDis->u16MasterHeight = 0;
                    }
                }
                else
                {
                    /* 可以维持距离在定频位置，但回波高度设置为0 */
                    if(LpstrMapSigGroup->MasterBuf[LenuCurInx].u16ActualDis[0] < LpstrMapSnsRealTimeDis->u16MasDis)
                    {
                        LpstrMapSnsRealTimeDis->u16MasDis = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16ActualDis[0];
                        LpstrMapSnsRealTimeDis->u16MasterHeight = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16MaxHeight[0];
                    }
                    else
                    {
                        LpstrMapSnsRealTimeDis->u16MasterHeight = 0;
                    }
                }
            }
            else 
#endif
            if(LpstrMapSnsRealTimeDis->u16MasDis < MAP_MIDDLE_REAL_TIME_DIS)
            {
                if(LpstrMapSigGroup->MasterBuf[LenuCurInx].u16ActualDis[0] < MAP_FAR_REAL_TIME_DIS)
                {
                    LpstrMapSnsRealTimeDis->u16MasDis = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16ActualDis[0];
                    LpstrMapSnsRealTimeDis->u16MasterHeight = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16MaxHeight[0];
                    if(LpstrMapSigGroup->MasterBuf[LenuCurInx].u16SecondEchoDis[0] < MAP_FAR_REAL_TIME_DIS)
                    {
                        LpstrMapSnsRealTimeDis->u8MasSecondFlag = 1;
                    }
                    else
                    {
                        LpstrMapSnsRealTimeDis->u8MasSecondFlag = 0;
                    }
                }
                else
                {
                    LpstrMapSnsRealTimeDis->u16MasDis = MAP_REAL_TIME_INVALID_DIS;
                    LpstrMapSnsRealTimeDis->u16MasterHeight = 0;
                    LpstrMapSnsRealTimeDis->u8MasSecondFlag = 0;
                }
            }
            else
            {
                if(LenuMeasType != PDC_SNS_MEAS_STD)
                {
                    if(LpstrMapSigGroup->MasterBuf[LenuCurInx].u16ActualDis[0] < MAP_FAR_REAL_TIME_DIS)
                    {
                        LpstrMapSnsRealTimeDis->u16MasDis = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16ActualDis[0];
                        LpstrMapSnsRealTimeDis->u16MasterHeight = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16MaxHeight[0];
                        if(LpstrMapSigGroup->MasterBuf[LenuCurInx].u16SecondEchoDis[0] < MAP_FAR_REAL_TIME_DIS)
                        {
                            LpstrMapSnsRealTimeDis->u8MasSecondFlag = 1;
                        }
                        else
                        {
                            LpstrMapSnsRealTimeDis->u8MasSecondFlag = 0;
                        }
                    }
                    else
                    {
                        LpstrMapSnsRealTimeDis->u16MasDis = MAP_REAL_TIME_INVALID_DIS;
                        LpstrMapSnsRealTimeDis->u16MasterHeight = 0;
                        LpstrMapSnsRealTimeDis->u8MasSecondFlag = 0;
                    }
                }
                else
                {
                    /* 可以维持距离在扫频位置，但回波高度设置为0 */
                    if(LpstrMapSigGroup->MasterBuf[LenuCurInx].u16ActualDis[0] < LpstrMapSnsRealTimeDis->u16MasDis)
                    {
                        LpstrMapSnsRealTimeDis->u16MasDis = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16ActualDis[0];
                        LpstrMapSnsRealTimeDis->u16MasterHeight = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16MaxHeight[0];
                        LpstrMapSnsRealTimeDis->u8MasSecondFlag = 0;
                    }
                    else
                    {
                        LpstrMapSnsRealTimeDis->u16MasterHeight = 0;
                        LpstrMapSnsRealTimeDis->u8MasSecondFlag = 0;
                    }
                }
            }
        }
    }

    if(LenuMeasType != PDC_SNS_MEAS_STD)
    {
        if(LpstrMapSigGroup->MasterBuf[LenuCurInx].u16ActualDis[0] < MAP_FAR_REAL_TIME_DIS)
        {
            LpstrMapSnsRealTimeDis->u16ChirpMasDis = LpstrMapSigGroup->MasterBuf[LenuCurInx].u16ActualDis[0];
        }
        else
        {
            LpstrMapSnsRealTimeDis->u16ChirpMasDis = MAP_REAL_TIME_INVALID_DIS;
        }

        if(LpstrMapSigGroup->LeftBuf[LenuCurInx].u16ActualDis[0] < MAP_FAR_REAL_TIME_DIS)
        {
            LpstrMapSnsRealTimeDis->u16ChirpLeftDis = LpstrMapSigGroup->LeftBuf[LenuCurInx].u16ActualDis[0];
        }
        else
        {
            LpstrMapSnsRealTimeDis->u16ChirpLeftDis = MAP_REAL_TIME_INVALID_DIS;
        }

        if(LpstrMapSigGroup->RightBuf[LenuCurInx].u16ActualDis[0] < MAP_FAR_REAL_TIME_DIS)
        {
            LpstrMapSnsRealTimeDis->u16ChirpRightDis = LpstrMapSigGroup->RightBuf[LenuCurInx].u16ActualDis[0];
        }
        else
        {
            LpstrMapSnsRealTimeDis->u16ChirpRightDis = MAP_REAL_TIME_INVALID_DIS;
        }
    }
    
    /* 更新左侦听距离 */
    if(LpstrMapSnsRealTimeDis->u16LeftDis == MAP_REAL_TIME_INVALID_DIS)
    {
        if(!Lu8AllChirpModeFlag)
        {
            //if(LenuMeasType == PDC_SNS_MEAS_STD)
            if(1)
            {
                if(LpstrMapSigGroup->LeftBuf[LenuCurInx].u16ActualDis[0] < MAP_REAL_TIME_LISTEN_DIS)
                {
                    LpstrMapSnsRealTimeDis->u16LeftDis = LpstrMapSigGroup->LeftBuf[LenuCurInx].u16ActualDis[0];
                    if(LpstrMapSigGroup->LeftBuf[LenuCurInx].u16SecondEchoDis[0] < MAP_REAL_TIME_LISTEN_DIS)
                    {
                        LpstrMapSnsRealTimeDis->u8LeftSecondFlag = 1;
                    }
                    else
                    {
                        LpstrMapSnsRealTimeDis->u8LeftSecondFlag = 0;
                    }
                }
                else
                {
                    LpstrMapSnsRealTimeDis->u16LeftDis = MAP_REAL_TIME_INVALID_DIS;
                    LpstrMapSnsRealTimeDis->u8LeftSecondFlag = 0;
                }
            }
            else
            {
                LpstrMapSnsRealTimeDis->u16LeftDis = MAP_REAL_TIME_INVALID_DIS;
                LpstrMapSnsRealTimeDis->u8LeftSecondFlag = 0;
            }
        }
        else
        {
            LpstrMapSnsRealTimeDis->u16LeftDis = MAP_REAL_TIME_INVALID_DIS;
            LpstrMapSnsRealTimeDis->u8LeftSecondFlag = 0;
        }
    }
    else
    {
        if(!Lu8AllChirpModeFlag)
        {
            if(LpstrMapSnsRealTimeDis->u16LeftDis <= MAP_REAL_TIME_LISTEN_DIS)
            {
                /* 近距离模式下，只使用定频的数据进行更新 */
                //if(LenuMeasType == PDC_SNS_MEAS_STD)
                if(1)
                {
                    if(LpstrMapSigGroup->LeftBuf[LenuCurInx].u16ActualDis[0] < MAP_REAL_TIME_LISTEN_DIS)
                    {
                        LpstrMapSnsRealTimeDis->u16LeftDis = LpstrMapSigGroup->LeftBuf[LenuCurInx].u16ActualDis[0];
                        if(LpstrMapSigGroup->LeftBuf[LenuCurInx].u16SecondEchoDis[0] < MAP_REAL_TIME_LISTEN_DIS)
                        {
                            LpstrMapSnsRealTimeDis->u8LeftSecondFlag = 1;
                        }
                        else
                        {
                            LpstrMapSnsRealTimeDis->u8LeftSecondFlag = 0;
                        }
                    }
                    else
                    {
                        LpstrMapSnsRealTimeDis->u16LeftDis = MAP_REAL_TIME_INVALID_DIS;
                        LpstrMapSnsRealTimeDis->u8LeftSecondFlag = 0;
                    }
                }
            }
        }
        else
        {
            LpstrMapSnsRealTimeDis->u16LeftDis = MAP_REAL_TIME_INVALID_DIS;
            LpstrMapSnsRealTimeDis->u8LeftSecondFlag = 0;
        }
    }

    /* 更新右侦听距离 */
    if(LpstrMapSnsRealTimeDis->u16RightDis == MAP_REAL_TIME_INVALID_DIS)
    {
        if(!Lu8AllChirpModeFlag)
        {
            //if(LenuMeasType == PDC_SNS_MEAS_STD)
            if(1)
            {
                if(LpstrMapSigGroup->RightBuf[LenuCurInx].u16ActualDis[0] < MAP_REAL_TIME_LISTEN_DIS)
                {
                    LpstrMapSnsRealTimeDis->u16RightDis = LpstrMapSigGroup->RightBuf[LenuCurInx].u16ActualDis[0];
                    if(LpstrMapSigGroup->RightBuf[LenuCurInx].u16SecondEchoDis[0] < MAP_REAL_TIME_LISTEN_DIS)
                    {
                        LpstrMapSnsRealTimeDis->u8RightSecondFlag = 1;
                    }
                    else
                    {
                        LpstrMapSnsRealTimeDis->u8RightSecondFlag = 0;
                    }
                }
                else
                {
                    LpstrMapSnsRealTimeDis->u16RightDis = MAP_REAL_TIME_INVALID_DIS;
                    LpstrMapSnsRealTimeDis->u8RightSecondFlag = 0;
                }
            }
            else
            {
                LpstrMapSnsRealTimeDis->u16RightDis = MAP_REAL_TIME_INVALID_DIS;
                LpstrMapSnsRealTimeDis->u8RightSecondFlag = 0;
            }
        }
        else
        {
            LpstrMapSnsRealTimeDis->u16RightDis = MAP_REAL_TIME_INVALID_DIS;
            LpstrMapSnsRealTimeDis->u8RightSecondFlag = 0;
        }
    }
    else
    {
        if(!Lu8AllChirpModeFlag)
        {
            if(LpstrMapSnsRealTimeDis->u16RightDis <= MAP_REAL_TIME_LISTEN_DIS)
            {
                /* 近距离模式下，只使用定频的数据进行更新 */
                //if(LenuMeasType == PDC_SNS_MEAS_STD)
                if(1)
                {
                    if(LpstrMapSigGroup->RightBuf[LenuCurInx].u16ActualDis[0] < MAP_REAL_TIME_LISTEN_DIS)
                    {
                        LpstrMapSnsRealTimeDis->u16RightDis = LpstrMapSigGroup->RightBuf[LenuCurInx].u16ActualDis[0];
                    }
                    else
                    {
                        LpstrMapSnsRealTimeDis->u16RightDis = MAP_REAL_TIME_INVALID_DIS;
                        LpstrMapSnsRealTimeDis->u8RightSecondFlag = 0;
                    }
                }
            }
        }
        else
        {
            LpstrMapSnsRealTimeDis->u16RightDis = MAP_REAL_TIME_INVALID_DIS;
            LpstrMapSnsRealTimeDis->u8RightSecondFlag = 0;
        }
    }

    LpstrMapSnsRealTimeDis->u8RealDisUpdateFlag = 1;
}




/******************************************************************************
 * 函数名称: MapEchoSameFreqNioseJudge
 * 
 * 功能描述: 根据原始回波，判断前后保系统的同频标志
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-01-31 13:44   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void MapEchoSameFreqNioseJudge(PDCSnsGroupType LeGroup,PDCSnsChannelType LePDCSnsCh)
{
    PDCSnsRawDataType *LpStrPDCSnsRawData;
    LpStrPDCSnsRawData = &GstrPDCSnsRawData[LeGroup];

    if((LePDCSnsCh == PDC_SNS_CH_FLS_RLS)||(LePDCSnsCh == PDC_SNS_CH_FL_RL))
    {
        if(!GstrParkingGuidenceData.u8LeftSideFreqNoiseFlag[LeGroup]) 
        {
            if(LpStrPDCSnsRawData->u8NoiseAbnormalCnt[LePDCSnsCh] > 30)
            {
                GstrParkingGuidenceData.u8LeftSideFreqNoiseFlag[LeGroup] = 1;
                GstrParkingGuidenceData.u32LeftSideNoiseUpdateTime[LeGroup] = GdSystemMsTimer;
            }
        }
        else
        {
            if(LpStrPDCSnsRawData->u8NoiseAbnormalCnt[LePDCSnsCh] > 30)
            {
                GstrParkingGuidenceData.u32LeftSideNoiseUpdateTime[LeGroup] = GdSystemMsTimer;
            }
            else
            {
                if(GdSystemMsTimer > (GstrParkingGuidenceData.u32LeftSideNoiseUpdateTime[LeGroup]+MAP_SAME_FREQ_HOLD_TIME))
                {
                    GstrParkingGuidenceData.u8LeftSideFreqNoiseFlag[LeGroup] = 0;
                }
            }
        }
    }
    
    if((LePDCSnsCh == PDC_SNS_CH_FR_RR)||(LePDCSnsCh == PDC_SNS_CH_FRS_RRS))
    {
        if(!GstrParkingGuidenceData.u8RightSideFreqNoiseFlag[LeGroup]) 
        {
            if(LpStrPDCSnsRawData->u8NoiseAbnormalCnt[LePDCSnsCh] > 30)
            {
                GstrParkingGuidenceData.u8RightSideFreqNoiseFlag[LeGroup] = 1;
                GstrParkingGuidenceData.u32RightSideNoiseUpdateTime[LeGroup] = GdSystemMsTimer;
            }
        }
        else
        {
            if(LpStrPDCSnsRawData->u8NoiseAbnormalCnt[LePDCSnsCh] > 30)
            {
                GstrParkingGuidenceData.u32RightSideNoiseUpdateTime[LeGroup] = GdSystemMsTimer;
            }
            else
            {
                if(GdSystemMsTimer > (GstrParkingGuidenceData.u32RightSideNoiseUpdateTime[LeGroup]+MAP_SAME_FREQ_HOLD_TIME))
                {
                    GstrParkingGuidenceData.u8RightSideFreqNoiseFlag[LeGroup] = 0;
                }
            }
        }
    }

    if((LePDCSnsCh > PDC_SNS_CH_FLS_RLS)&&(LePDCSnsCh < PDC_SNS_CH_FRS_RRS))
    {
        if(!GstrParkingGuidenceData.u8SameFreqNoiseFlag[LeGroup]) 
        {
            if(LpStrPDCSnsRawData->u8NoiseAbnormalCnt[LePDCSnsCh] > 30)
            {
                GstrParkingGuidenceData.u8SameFreqNoiseFlag[LeGroup] = 1;
                GstrParkingGuidenceData.u32SameFreqNoiseUpdateTime[LeGroup] = GdSystemMsTimer;
            }
        }
        else
        {
            if(LpStrPDCSnsRawData->u8NoiseAbnormalCnt[LePDCSnsCh] > 30)
            {
                GstrParkingGuidenceData.u32SameFreqNoiseUpdateTime[LeGroup] = GdSystemMsTimer;
            }
            else
            {
                if(GdSystemMsTimer > (GstrParkingGuidenceData.u32SameFreqNoiseUpdateTime[LeGroup]+MAP_SAME_FREQ_HOLD_TIME))
                {
                    GstrParkingGuidenceData.u8SameFreqNoiseFlag[LeGroup] = 0;
                }
            }
        }
    }
}


/******************************************************************************
 * 函数名称: CalSecondEchoDis
 * 
 * 功能描述: 通过第一回波，并结合探头的安装高度，计算第二回波距离
 * 
 * 输入参数:Lu16FirstEchoDis--第一回波距离； Lu16SnsInstallHeight--探头的安装高度
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-04-03 16:24   V0.1      AntonyFang   初次发布
 ******************************************************************************/
uint16 CalFirstSecondEchoDisSub(uint16 Lu16FirstEchoDis,uint16 Lu16SnsInstallHeight)
{
    uint16 Lu16SecondEchoDis;
    float LfFirstEchoDisSquare,LfSnsInstallHeightSquare;
    float LfSecondEchoDis;

    LfFirstEchoDisSquare = Lu16FirstEchoDis*Lu16FirstEchoDis;
    LfSnsInstallHeightSquare = Lu16SnsInstallHeight*Lu16SnsInstallHeight;
    LfSecondEchoDis = LfFirstEchoDisSquare + LfSnsInstallHeightSquare;
    LfSecondEchoDis = powf(LfSecondEchoDis,0.5);
    Lu16SecondEchoDis = (uint16)LfSecondEchoDis;

    Lu16SecondEchoDis = Lu16SecondEchoDis - Lu16FirstEchoDis;
    Lu16SecondEchoDis += MAP_SIG_SECOND_DIS_ADD;

    return Lu16SecondEchoDis;
}

/******************************************************************************
 * 函数名称: MaoEchoFilterAndSigGroupDataGet
 * 
 * 功能描述: 对原始数据做初级滤波(过滤噪点)，并提取原始信号组(即对能够组成第一第二信号的信号组合并在一起)
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-22 21:03   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void MapEchoFilterAndSigGroupDataGet(PDCSnsGroupType LeGroup,PDCSnsChannelType LePDCSnsCh)
{
    uint8 i;
    MapSigGroupDataCacheType *LpstrMapSigGroupDataCache;
    Sns_CycleType LenuCurIndex;
    float LfCarMoveSub = 0;
    PDCSnsRawDataType *LpStrPDCSnsRawData;
    uint8 Lu8MasterEchoCnt,Lu8ListenEchoCnt;
    uint16 Lu16MasterDis,Lu16MasterHeight,Lu16MasterDisSub;
    uint16 Lu16ListenDis,Lu16ListenSecondDis,Lu16ListenHeight,Lu16ListenDisSub;
    uint16 Lu16FirstSecondEchoSub;
    uint8 Lu8SigGroupCnt;
    uint8 Lu8TableIndex;
    uint8 Lu8TableHighIndex;
    const uint16 *Lpu16SnsEchoThresholdTable;
    const SnsMapCalibHeightType *LptrSnsJudgeObjTypeThresholdTable;
    uint8 Lu8FindValidObjFlag = 0;
    uint16 Lu16ThresCompenset = 0;
    uint16 Lu16MasterThresCompenset = 0;    /* 主发探头的阈值补偿，主要考虑车速较高时，适当降低阈值，以保证高速的探测稳定性 */
	uint16 Lu16ListenThresCompenset = 0; 
    uint16 Lu16SnsSecondThanFirstHeight;
    uint16 Lu16FirstDisToBackupSub,Lu16SecondDisToBackupSub;
    uint8 Lu8FirstDisIsNoise;
    uint16 Lu16NoiseHeight = 1000;
    uint8 Lu8ChirpModeFlag = 0;
    uint16 Lu16FirstSecondHeightSub = 0;
    

    LpStrPDCSnsRawData = &GstrPDCSnsRawData[LeGroup];
    LpstrMapSigGroupDataCache = &GstrMapSigGroupDataCache[LeGroup][LePDCSnsCh];

    /* 主发阈值获取 */
    if((LePDCSnsCh == PDC_SNS_CH_FLS_RLS)||(LePDCSnsCh == PDC_SNS_CH_FRS_RRS))
    {
        if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
        {
            Lpu16SnsEchoThresholdTable = Gpu16SnsMapThresholdTable[LeGroup][0];
        }
        else
        {
            Lpu16SnsEchoThresholdTable = Gpu16SnsMapThresholdTable[LeGroup][1];
        }
    }
    else if((LePDCSnsCh == PDC_SNS_CH_FL_RL)||(LePDCSnsCh == PDC_SNS_CH_FR_RR))
    {
        if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
        {
            Lpu16SnsEchoThresholdTable = Gpu16SnsMapThresholdTable[LeGroup][2];
        }
        else
        {
            Lpu16SnsEchoThresholdTable = Gpu16SnsMapThresholdTable[LeGroup][3];
        }
    }
    else
    {
        if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
        {
            Lpu16SnsEchoThresholdTable = Gpu16SnsMapThresholdTable[LeGroup][4];
        }
        else
        {
            Lpu16SnsEchoThresholdTable = Gpu16SnsMapThresholdTable[LeGroup][5];
        }
    }

    if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
    {
        Lu8ChirpModeFlag = 0;
        LptrSnsJudgeObjTypeThresholdTable = &GStrMapObjJudgeStdThresTableInRAM[0];
        Lu16SnsSecondThanFirstHeight = MAP_STD_SECOND_THAN_FIRST_HEIGHT;
        if(Gu16CarSpdForSnsUse < 50)
        {
            Lu16MasterThresCompenset = 0;
        }
        else if(Gu16CarSpdForSnsUse < 100)
        {
            Lu16MasterThresCompenset = MAP_STD_MASTER_CMP_THRES_1KM;
        }
        else if(Gu16CarSpdForSnsUse < 300)
        {
            Lu16MasterThresCompenset = MAP_STD_MASTER_CMP_THRES_3KM;
        }
        else if(Gu16CarSpdForSnsUse < 500)
        {
            Lu16MasterThresCompenset = MAP_STD_MASTER_CMP_THRES_5KM;
        }
        else if(Gu16CarSpdForSnsUse < 700)
        {
            Lu16MasterThresCompenset = MAP_STD_MASTER_CMP_THRES_7KM;
        }
        else
        {
            Lu16MasterThresCompenset = MAP_STD_MASTER_CMP_THRES_MORE_7KM;
        }
    }
    else
    {
        Lu8ChirpModeFlag = 1;
        LptrSnsJudgeObjTypeThresholdTable = &GStrMapObjJudgeChirpThresTableInRAM[0];
        Lu16SnsSecondThanFirstHeight = MAP_CHIRP_SECOND_THAN_FIRST_HEIGHT;
        if(Gu16CarSpdForSnsUse < 50)
        {
            Lu16MasterThresCompenset = 0;
        }
        else if(Gu16CarSpdForSnsUse < 100)
        {
            Lu16MasterThresCompenset = MAP_CHIRP_MASTER_CMP_THRES_1KM;
        }
        else if(Gu16CarSpdForSnsUse < 300)
        {
            Lu16MasterThresCompenset = MAP_CHIRP_MASTER_CMP_THRES_3KM;
        }
        else if(Gu16CarSpdForSnsUse < 500)
        {
            Lu16MasterThresCompenset = MAP_CHIRP_MASTER_CMP_THRES_5KM;
        }
        else if(Gu16CarSpdForSnsUse < 700)
        {
            Lu16MasterThresCompenset = MAP_CHIRP_MASTER_CMP_THRES_7KM;
        }
        else
        {
            Lu16MasterThresCompenset = MAP_CHIRP_MASTER_CMP_THRES_MORE_7KM;
        }
    }



    /* 更新当前索引值 */
    LpstrMapSigGroupDataCache->enuCurIndex++;
    if(LpstrMapSigGroupDataCache->enuCurIndex >= MAP_CYCLE_NUM)
    {
        LpstrMapSigGroupDataCache->enuCurIndex = MAP_CYCLE_0;
    }
    LenuCurIndex = LpstrMapSigGroupDataCache->enuCurIndex;
    /*开始索引技术，方便后续累计到3个周期后进行数据跟踪处理*/
    if(LpstrMapSigGroupDataCache->u8RecordCnt < MAP_CYCLE_NUM)
    {
        LpstrMapSigGroupDataCache->u8RecordCnt++;
    }

    MapSigGroupDataClear(LeGroup,LePDCSnsCh,LenuCurIndex);

    /* 获取上一轮到本轮探头探测车辆移动的距离及对应的系统时间 */
    if(LpstrMapSigGroupDataCache->u8RecordCnt > 1)
    {
        LfCarMoveSub = GstrSnsCarMovSts[LeGroup][LePDCSnsCh].fSnsCarMovDisSub;
    }
    if(LeGroup == PDC_SNS_GROUP_FRONT)
    {
        /** @brief: 对于前探头，车子前进是靠近障碍物 */
        LpstrMapSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub = -LfCarMoveSub;
    }
    else
    {
        LpstrMapSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub = LfCarMoveSub;
    }
    /* 开启时间，用于后续点云对应的时间戳和距离进行匹配--2024-04-15 */
    LpstrMapSigGroupDataCache->SysDataBuf[LenuCurIndex].u32SysTime = GdSystemMsTimer;
    LpstrMapSigGroupDataCache->SysDataBuf[LenuCurIndex].eCarDir = GstrSnsCarMovSts[LeGroup][LePDCSnsCh].eCarDir;
    LpstrMapSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType = LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh];

    MapEchoSameFreqNioseJudge(LeGroup,LePDCSnsCh);
    if(LePDCSnsCh == PDC_SNS_CH_FLS_RLS)
    {
        LpstrMapSigGroupDataCache->SysDataBuf[LenuCurIndex].u8SnsNoiseCnt = GstrParkingGuidenceData.u8LeftSideFreqNoiseFlag[LeGroup];
    }
    else if(LePDCSnsCh == PDC_SNS_CH_FRS_RRS)
    {
        LpstrMapSigGroupDataCache->SysDataBuf[LenuCurIndex].u8SnsNoiseCnt = GstrParkingGuidenceData.u8RightSideFreqNoiseFlag[LeGroup];
    }
    else
    {
        LpstrMapSigGroupDataCache->SysDataBuf[LenuCurIndex].u8SnsNoiseCnt = GstrParkingGuidenceData.u8SameFreqNoiseFlag[LeGroup];
    }
    
    /* Step1:通过阈值过滤和提取主发回波信号组 */
    Lu8MasterEchoCnt = LpStrPDCSnsRawData->u8MasterEchoCnt[LePDCSnsCh];
    Lu8SigGroupCnt = 0;
    for(i = 0; i < Lu8MasterEchoCnt; i++)
    {
        /* 先判断是否有第二回波进行匹配，再进行查询滤波；避免把回波信号组的隐藏信息过滤掉 */
        Lu16MasterDis = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i];
        Lu16MasterHeight = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i];
        if(i == 0)
        {
            /* 对于扫频的第一个回波，若是在盲区内就过滤掉，防止给后续的Map构建造成干扰 */
            if((GstrMapSnsRealTimeDis[LeGroup][LePDCSnsCh].u16MasDis < 350)&&(LpstrMapSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType!=PDC_SNS_MEAS_STD))
            {
                if(Lu16MasterDis > (GstrMapSnsRealTimeDis[LeGroup][LePDCSnsCh].u16MasDis+100))
                {
                    continue;
                }
            }
        }
        
        if(Lu16MasterDis < MAP_TABLE_MAX_DIS)
        {
            Lu8TableIndex = Lu16MasterDis/MAP_TABLE_STEP;

            /* 针对10~20cm需要进一步细化，用于解决余震分叉的问题；即10~15cm用[10,20]的阈值；15~20用[20,30]的阈值 */
            if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
            {
                if(Lu8TableIndex == SNS_MAP_DIS_20cm)
                {
                    if(Lu16MasterDis < 150)
                    {
                        Lu8TableIndex = SNS_MAP_DIS_10cm;
                    }
                }
            }
            Lu8TableHighIndex = Lu16MasterDis/MAP_HIGH_TABLE_STEP;
        }
        else
        {
            Lu8TableIndex = SNS_MAP_DIS_400cm;
            Lu8TableHighIndex = SNS_MAP_DIS_HIGH_400cm;
        }

        if(Lu8TableIndex < SNS_MAP_DIS_100cm)
        {
            Lu16NoiseHeight = 1500;
        }
        else if(Lu8TableIndex < SNS_MAP_DIS_150cm)
        {
            Lu16NoiseHeight = 1200;
        }
        else if(Lu8TableIndex < SNS_MAP_DIS_200cm)
        {
            Lu16NoiseHeight = 1100;
        }
        else 
        {
            Lu16NoiseHeight = 900;
        }

        /* 第一回波必须大于PVC的阈值，否则直接当做噪点过滤掉--2023-12-01 */
        if(Lu16MasterHeight < (Lpu16SnsEchoThresholdTable[Lu8TableIndex]-Lu16MasterThresCompenset))
        {
            continue;
        }
        
        Lu16FirstSecondEchoSub = CalFirstSecondEchoDisSub(Lu16MasterDis,Gu16SnsInstallHeight_Ram[LeGroup][LePDCSnsCh]);

        Lu8FindValidObjFlag = 0;
        if((i+1) < Lu8MasterEchoCnt)
        {
            Lu16MasterDisSub = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1] - Lu16MasterDis;
            if(Lu16MasterDisSub < Lu16FirstSecondEchoSub)
            {
                /* 此处存在风险，即一个大墙的20cm附近存在小的障碍物，可能存在距离不对问题,因此仅针对70cm以外的使用该策略 */
                if(((Lu16MasterHeight+Lu16SnsSecondThanFirstHeight) < LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1])&&\
                    (Lu16MasterDis > 700))
                {
                    if(LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1] < MAP_TABLE_MAX_DIS)
                    {
                        Lu8TableIndex = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1]/MAP_TABLE_STEP;
                        Lu8TableHighIndex = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1]/MAP_HIGH_TABLE_STEP;
                    }
                    else
                    {
                        Lu8TableIndex = SNS_MAP_DIS_400cm;
                        Lu8TableHighIndex = SNS_MAP_DIS_HIGH_400cm;
                    }
                    
                    if(LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1] > (Lpu16SnsEchoThresholdTable[Lu8TableIndex]-Lu16MasterThresCompenset))
                    {
                        /* 第一第二回波构成信号组，且第二回波大于第一回波的处理；
                           由于探头扫频模式下的灵敏度提升，导致较多噪点和正常回波进行误配属，需要在此处进行过滤处理*/
                        if(Lu8ChirpModeFlag)
                        {
                            Lu8FirstDisIsNoise = 0;
                            Lu16FirstSecondHeightSub = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1] - Lu16MasterHeight;
                            if(Lu16FirstSecondHeightSub > 500)
                            {
                                if(Lu16MasterHeight < Lu16NoiseHeight)
                                {
                                    Lu8FirstDisIsNoise = 1;
                                }
                            }
                            if(Lu8FirstDisIsNoise == 0)
                            {
                                LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                                LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16MasterDis - MAP_MASTER_COMPEN_DIS;
                                LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                                LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1] - MAP_MASTER_COMPEN_DIS;
                                LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1];
                                /* 侧边雷达依然延续使用第一回波，防止错配 */
                                if((LePDCSnsCh == PDC_SNS_CH_FLS_RLS)||(LePDCSnsCh == PDC_SNS_CH_FRS_RRS))
                                {
                                    LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16MasterDis - MAP_MASTER_COMPEN_DIS;
                                    LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1];
                                }
                                else
                                {
                                    LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1] - MAP_MASTER_COMPEN_DIS;
                                    LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1];
                                }
                                Lu8FindValidObjFlag = 1;
                                i++;
                            }
                            else
                            {
                                /* 不做处理，继续下一个循环 */
                            }
                        }
                        else
                        {
                            /* 定频使用第一回距离作为实际使用的距离更接近真实效果 */
                            LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                            LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16MasterDis - MAP_MASTER_COMPEN_DIS;
                            LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                            LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1] - MAP_MASTER_COMPEN_DIS;
                            LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1];
                            LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16MasterDis - MAP_MASTER_COMPEN_DIS;
                            LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1];
                            Lu8FindValidObjFlag = 1;
                            i++;
                        }
                    }
                }
                else
                {
                    /* 第一回波高度大于第二回波高度，不进行后续的查询，直接配属到信号组中 */
                    if(Lu16MasterHeight > (Lpu16SnsEchoThresholdTable[Lu8TableIndex]-Lu16MasterThresCompenset))
                    {
                        /* 添加对于第二回波有效性的判断，第二回波高度同样需要通过阈值表过滤 */
                        //if(LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1] > (Lu16MasterHeight/3))
                        if(1)
                        {
                            LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                            LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16MasterDis - MAP_MASTER_COMPEN_DIS;
                            LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                            LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1] - MAP_MASTER_COMPEN_DIS;
                            LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1];
                            LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16MasterDis - MAP_MASTER_COMPEN_DIS;
                            LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                        }
                        else
                        {
                            LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                            LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16MasterDis - MAP_MASTER_COMPEN_DIS;
                            LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                            LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16MasterDis - MAP_MASTER_COMPEN_DIS;
                            LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                        }
                        Lu8FindValidObjFlag = 1;
                        i++;
                    }
                }
            }
            else
            {
                /* 第一、第二回波构不成信号组，直接将第一回波单独构建一个信号组 */
                if(Lu16MasterHeight > (Lpu16SnsEchoThresholdTable[Lu8TableIndex]-Lu16MasterThresCompenset))
                {
                    LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                    LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16MasterDis - MAP_MASTER_COMPEN_DIS;
                    LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                    LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16MasterDis - MAP_MASTER_COMPEN_DIS;
                    LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                    Lu8FindValidObjFlag = 1;
                }
            }
        }
        else
        {
            /* 无后续回波，和设定阈值对比后直接配属到一个信号组中 */
            if(Lu16MasterHeight > (Lpu16SnsEchoThresholdTable[Lu8TableIndex]-Lu16MasterThresCompenset))
            {
                LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16MasterDis - MAP_MASTER_COMPEN_DIS;
                LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16MasterDis - MAP_MASTER_COMPEN_DIS;
                LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                Lu8FindValidObjFlag = 1;
            }
        }
        if(Lu8FindValidObjFlag)
        {
            if(LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] > LptrSnsJudgeObjTypeThresholdTable[Lu8TableHighIndex].u16BigWallHeight)
            {
                LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt] = OBJ_BIGWALL_TYPE;
            }
            else
            {
                LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt] = OBJ_PVC_PIPE_TYPE;
            }

            /* 只保留小障碍物后的大障碍物逻辑处理--2023-11-13 */
            if(Lu8SigGroupCnt > 0)
            {
                if((LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt]-5) < LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt-1])
                {
                    break;
                }
            }
            Lu8SigGroupCnt++;
        }
        if(Lu8SigGroupCnt == MAX_NUM_OF_MAP_SIG_GROUP)
        {
            break;
        }
    }
    LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u8SigGroupCnt = Lu8SigGroupCnt;
    /* 保留备份值 */
    for(i = 0; i < MAX_NUM_OF_MAP_SIG_GROUP; i++)
    {
        LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstDisBackup[i] = LpstrMapSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[i];
    }

    /* 侦听阈值获取 */
    if((LePDCSnsCh == PDC_SNS_CH_FLS_RLS)||(LePDCSnsCh == PDC_SNS_CH_FRS_RRS))
    {
        if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
        {
            Lpu16SnsEchoThresholdTable = Gpu16SnsMapLisThresholdTable[LeGroup][0];
        }
        else
        {
            Lpu16SnsEchoThresholdTable = Gpu16SnsMapLisThresholdTable[LeGroup][1];
        }
    }
    else if((LePDCSnsCh == PDC_SNS_CH_FL_RL)||(LePDCSnsCh == PDC_SNS_CH_FR_RR))
    {
        if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
        {
            Lpu16SnsEchoThresholdTable = Gpu16SnsMapLisThresholdTable[LeGroup][2];
        }
        else
        {
            Lpu16SnsEchoThresholdTable = Gpu16SnsMapLisThresholdTable[LeGroup][3];
        }
    }
    else
    {
        if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
        {
            Lpu16SnsEchoThresholdTable = Gpu16SnsMapLisThresholdTable[LeGroup][4];
        }
        else
        {
            Lpu16SnsEchoThresholdTable = Gpu16SnsMapLisThresholdTable[LeGroup][5];
        }
    }

    /* Step2:通过阈值过滤和提取左侦听回波信号组 */
    Lu8ListenEchoCnt = LpStrPDCSnsRawData->u8LeftListenEchoCnt[LePDCSnsCh];
    Lu8SigGroupCnt = 0;
    for(i = 0; i < Lu8ListenEchoCnt; i++)
    {
        /* 先判断是否有第二回波进行匹配，再进行查询滤波；避免把回波信号组的隐藏信息过滤掉 */
        Lu16ListenDis = (LpStrPDCSnsRawData->u16LeftListenDis[LePDCSnsCh][i])>>1;
        Lu16ListenHeight = LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i];

        if(Lu16ListenDis < MAP_TABLE_MAX_DIS)
        {
            Lu8TableIndex = Lu16ListenDis/MAP_TABLE_STEP;
            Lu8TableHighIndex = Lu16ListenDis/MAP_HIGH_TABLE_STEP;
        }
        else
        {
            Lu8TableIndex = SNS_MAP_DIS_400cm;
            Lu8TableHighIndex = SNS_MAP_DIS_HIGH_400cm;
        }

        Lu16FirstSecondEchoSub = CalFirstSecondEchoDisSub(Lu16ListenDis,Gu16SnsInstallHeight_Ram[LeGroup][LePDCSnsCh-1]);

        if(Lu8TableIndex < SNS_MAP_DIS_100cm)
        {
            Lu16NoiseHeight = 1500;
        }
        else if(Lu8TableIndex < SNS_MAP_DIS_150cm)
        {
            Lu16NoiseHeight = 1200;
        }
        else if(Lu8TableIndex < SNS_MAP_DIS_200cm)
        {
            Lu16NoiseHeight = 1100;
        }
        else 
        {
            Lu16NoiseHeight = 900;
        }

		/* 速度大于13km/h，且距离大于3.4m,降低侦听阈值，解决15cm路沿画不出点云的问题 */
		if((Gu16CarSpdForSnsUse > 1000) && (Lu16ListenDis > 3400))
        {
            Lu16ListenThresCompenset = 200;
        }
		else
		{
			Lu16ListenThresCompenset = 0;
		}
		
        /* 第一回波必须大于PVC的阈值，否则直接当做噪点过滤掉--2023-12-01 */
        if(Lu16ListenHeight < (Lpu16SnsEchoThresholdTable[Lu8TableIndex] - Lu16ListenThresCompenset))
        {
            continue;
        }
        
        Lu8FindValidObjFlag = 0;
        if((i+1) < Lu8ListenEchoCnt)
        {
            Lu16ListenSecondDis = (LpStrPDCSnsRawData->u16LeftListenDis[LePDCSnsCh][i+1])>>1;
            Lu16ListenDisSub = Lu16ListenSecondDis - Lu16ListenDis;
            if(Lu16ListenDisSub < Lu16FirstSecondEchoSub)
            {
                /* 此处存在风险，即一个大墙的20cm附近存在小的障碍物，可能存在距离不对问题,因此仅针对70cm以外的使用该策略 */
                if(((Lu16ListenHeight+Lu16SnsSecondThanFirstHeight) < LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1])&&\
                    (Lu16ListenDis > 700))
                {
                    if(Lu16ListenSecondDis < MAP_TABLE_MAX_DIS)
                    {
                        Lu8TableIndex = Lu16ListenSecondDis/MAP_TABLE_STEP;
                        Lu8TableHighIndex = Lu16ListenSecondDis/MAP_HIGH_TABLE_STEP;
                    }
                    else
                    {
                        Lu8TableIndex = SNS_MAP_DIS_400cm;
                        Lu8TableHighIndex = SNS_MAP_DIS_HIGH_400cm;
                    }
                    if(LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1] > (Lpu16SnsEchoThresholdTable[Lu8TableIndex] - Lu16ListenThresCompenset))
                    {   
                        /* 第一第二回波构成信号组，且第二回波大于第一回波的处理；
                           由于探头扫频模式下的灵敏度提升，导致较多噪点和正常回波进行误配属，需要在此处进行过滤处理*/
                        if(Lu8ChirpModeFlag)
                        {
                            Lu8FirstDisIsNoise = 0;
                            Lu16FirstSecondHeightSub = LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1] - Lu16ListenHeight;
                            if(Lu16FirstSecondHeightSub > 500)
                            {
                                if(Lu16ListenHeight < Lu16NoiseHeight)
                                {
                                    Lu8FirstDisIsNoise = 1;
                                }
                            }
                            
                            if(Lu8FirstDisIsNoise == 0)
                            {
                                LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                                LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - MAP_LISTEN_COMPEN_DIS;
                                LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                                LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - MAP_LISTEN_COMPEN_DIS;
                                LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1];
                                if((LePDCSnsCh == PDC_SNS_CH_FLS_RLS)||(LePDCSnsCh == PDC_SNS_CH_FRS_RRS))
                                {
                                    LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - MAP_LISTEN_COMPEN_DIS;
                                    LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1];
                                }
                                else
                                {
                                    LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - MAP_LISTEN_COMPEN_DIS;
                                    LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1];
                                }
                                Lu8FindValidObjFlag = 1;
                                i++;
                            }
                            else
                            {
                                /* 继续后续的循环 */
                            }
                        }
                        else
                        {
                            LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                            LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - MAP_LISTEN_COMPEN_DIS;
                            LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                            LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - MAP_LISTEN_COMPEN_DIS;
                            LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1];
                            LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - MAP_LISTEN_COMPEN_DIS;
                            LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1];
                            Lu8FindValidObjFlag = 1;
                            i++;
                        }
                    }
                }
                else
                {
                    if(Lu16ListenHeight > (Lpu16SnsEchoThresholdTable[Lu8TableIndex] - Lu16ListenThresCompenset))
                    {
                        /* 添加对于第二回波有效性的判断，第二回波高度同样需要通过阈值表过滤 */
                        //if(LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1] > (Lu16ListenHeight/3))
                        if(1)
                        {
                            LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                            LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - MAP_LISTEN_COMPEN_DIS;
                            LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                            LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - MAP_LISTEN_COMPEN_DIS;
                            LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1];
                            LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - MAP_LISTEN_COMPEN_DIS;
                            LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                        }
                        else
                        {
                            LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                            LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - MAP_LISTEN_COMPEN_DIS;
                            LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                            LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - MAP_LISTEN_COMPEN_DIS;
                            LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                        }
                        Lu8FindValidObjFlag = 1;
                        i++;
                    }
                }
            }
            else
            {
                if(Lu16ListenHeight > (Lpu16SnsEchoThresholdTable[Lu8TableIndex] - Lu16ListenThresCompenset))
                {
                    LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                    LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - MAP_LISTEN_COMPEN_DIS;
                    LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                    LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - MAP_LISTEN_COMPEN_DIS;
                    LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                    Lu8FindValidObjFlag = 1;
                }
            }
        }
        else
        {
            if(Lu16ListenHeight > (Lpu16SnsEchoThresholdTable[Lu8TableIndex] - Lu16ListenThresCompenset))
            {
                LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - MAP_LISTEN_COMPEN_DIS;
                LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - MAP_LISTEN_COMPEN_DIS;
                LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                Lu8FindValidObjFlag = 1;
            }
        }
        if(Lu8FindValidObjFlag)
        {
            if(LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] > LptrSnsJudgeObjTypeThresholdTable[Lu8TableHighIndex].u16BigWallHeight)
            {
                LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt] = OBJ_BIGWALL_TYPE;
            }
            else
            {
                LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt] = OBJ_PVC_PIPE_TYPE;
            }
#if 1
            /* 只保留小障碍物后的大障碍物逻辑处理--2023-11-13 */
            if(Lu8SigGroupCnt > 0)
            {
                if((LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt]+1000) < LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt-1])
                {
                    break;
                }
            }
#endif
            Lu8SigGroupCnt++;
        }
        
        if(Lu8SigGroupCnt == MAX_NUM_OF_MAP_SIG_GROUP)
        {
            break;
        }
    }
    LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u8SigGroupCnt = Lu8SigGroupCnt;
    /* 保留备份值 */
    for(i = 0; i < MAX_NUM_OF_MAP_SIG_GROUP; i++)
    {
        LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstDisBackup[i] = LpstrMapSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[i];
    }

    /* Step2:通过阈值过滤和提取左侦听回波信号组 */
    Lu8ListenEchoCnt = LpStrPDCSnsRawData->u8RightListenEchoCnt[LePDCSnsCh];
    Lu8SigGroupCnt = 0;
    for(i = 0; i < Lu8ListenEchoCnt; i++)
    {
        /* 先判断是否有第二回波进行匹配，再进行查询滤波；避免把回波信号组的隐藏信息过滤掉 */
        Lu16ListenDis = (LpStrPDCSnsRawData->u16RightListenDis[LePDCSnsCh][i])>>1;
        Lu16ListenHeight = LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i];

        if(Lu16ListenDis < MAP_TABLE_MAX_DIS)
        {
            Lu8TableIndex = Lu16ListenDis/MAP_TABLE_STEP;
            Lu8TableHighIndex = Lu16ListenDis/MAP_HIGH_TABLE_STEP;
        }
        else
        {
            Lu8TableIndex = SNS_MAP_DIS_400cm;
            Lu8TableHighIndex = SNS_MAP_DIS_HIGH_400cm;
        }

        Lu16FirstSecondEchoSub = CalFirstSecondEchoDisSub(Lu16ListenDis,Gu16SnsInstallHeight_Ram[LeGroup][LePDCSnsCh+1]);

        if(Lu8TableIndex < SNS_MAP_DIS_100cm)
        {
            Lu16NoiseHeight = 1500;
        }
        else if(Lu8TableIndex < SNS_MAP_DIS_150cm)
        {
            Lu16NoiseHeight = 1200;
        }
        else if(Lu8TableIndex < SNS_MAP_DIS_200cm)
        {
            Lu16NoiseHeight = 1100;
        }
        else 
        {
            Lu16NoiseHeight = 900;
        }

		/* 速度大于13km/h，且距离大于3.4m,降低侦听阈值，解决15cm路沿画不出点云的问题 */
		if((Gu16CarSpdForSnsUse > 1300) && (Lu16ListenDis > 3400))
        {
            Lu16ListenThresCompenset = 200;
        }
		else
		{
			Lu16ListenThresCompenset = 0;
		}
		
        /* 第一回波必须大于PVC的阈值，否则直接当做噪点过滤掉--2023-12-01 */
        if(Lu16ListenHeight < (Lpu16SnsEchoThresholdTable[Lu8TableIndex] - Lu16ListenThresCompenset))
        {
            continue;
        }
        Lu8FindValidObjFlag = 0;
        if((i+1) < Lu8ListenEchoCnt)
        {
            Lu16ListenSecondDis = (LpStrPDCSnsRawData->u16RightListenDis[LePDCSnsCh][i+1])>>1;
            Lu16ListenDisSub = Lu16ListenSecondDis - Lu16ListenDis;
            if(Lu16ListenDisSub < Lu16FirstSecondEchoSub)
            {
                /* 此处存在风险，即一个大墙的20cm附近存在小的障碍物，可能存在距离不对问题,因此仅针对70cm以外的使用该策略 */
                if(((Lu16ListenHeight+Lu16SnsSecondThanFirstHeight) < LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1])&&\
                    (Lu16ListenDis > 700))
                {
                    if(Lu16ListenSecondDis < MAP_TABLE_MAX_DIS)
                    {
                        Lu8TableIndex = Lu16ListenSecondDis/MAP_TABLE_STEP;
                        Lu8TableHighIndex = Lu16ListenSecondDis/MAP_HIGH_TABLE_STEP;
                    }
                    else
                    {
                        Lu8TableIndex = SNS_MAP_DIS_400cm;
                        Lu8TableHighIndex = SNS_MAP_DIS_HIGH_400cm;
                    }

                    if(LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1] > (Lpu16SnsEchoThresholdTable[Lu8TableIndex] - Lu16ListenThresCompenset))
                    {
                        /* 第一第二回波构成信号组，且第二回波大于第一回波的处理；
                           由于探头扫频模式下的灵敏度提升，导致较多噪点和正常回波进行误配属，需要在此处进行过滤处理*/
                        if(Lu8ChirpModeFlag)
                        {
                            Lu8FirstDisIsNoise = 0;
                            Lu16FirstSecondHeightSub = LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1] - Lu16ListenHeight;
                            if(Lu16FirstSecondHeightSub > 500)
                            {
                                if(Lu16ListenHeight < Lu16NoiseHeight)
                                {
                                    Lu8FirstDisIsNoise = 1;
                                }
                            }
                            if(Lu8FirstDisIsNoise == 0)
                            {
                                LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                                LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - MAP_LISTEN_COMPEN_DIS;
                                LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                                LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - MAP_LISTEN_COMPEN_DIS;
                                LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1];
                                if((LePDCSnsCh == PDC_SNS_CH_FLS_RLS)||(LePDCSnsCh == PDC_SNS_CH_FRS_RRS))
                                {
                                    LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - MAP_LISTEN_COMPEN_DIS;
                                    LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1];
                                }
                                else
                                {
                                    LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - MAP_LISTEN_COMPEN_DIS;
                                    LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1];
                                }
                                Lu8FindValidObjFlag = 1;
                                i++;
                            }
                            else
                            {
                                /* 继续后续的查询 */
                            }
                        }
                        else
                        {
                            LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                            LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - MAP_LISTEN_COMPEN_DIS;
                            LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                            LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - MAP_LISTEN_COMPEN_DIS;
                            LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1];
                            LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - MAP_LISTEN_COMPEN_DIS;
                            LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1];
                            Lu8FindValidObjFlag = 1;
                            i++;
                        }
                    }
                }
                else
                {
                    if(Lu16ListenHeight > (Lpu16SnsEchoThresholdTable[Lu8TableIndex] - Lu16ListenThresCompenset))
                    {
                        /* 添加对于第二回波有效性的判断，第二回波高度同样需要通过阈值表过滤 */
                        //if(LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1] > (Lu16ListenHeight/3))
                        if(1)
                        {
                            LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                            LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - MAP_LISTEN_COMPEN_DIS;
                            LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                            LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - MAP_LISTEN_COMPEN_DIS;
                            LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1];
                            LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - MAP_LISTEN_COMPEN_DIS;
                            LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                        }
                        else
                        {
                            LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                            LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - MAP_LISTEN_COMPEN_DIS;
                            LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                            LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - MAP_LISTEN_COMPEN_DIS;
                            LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                        }
                        Lu8FindValidObjFlag = 1;
                        i++;
                    }
                }
            }
            else
            {
                if(Lu16ListenHeight > (Lpu16SnsEchoThresholdTable[Lu8TableIndex] - Lu16ListenThresCompenset))
                {
                    LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                    LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - MAP_LISTEN_COMPEN_DIS;
                    LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                    LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - MAP_LISTEN_COMPEN_DIS;
                    LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                    Lu8FindValidObjFlag = 1;
                }
            }
        }
        else
        {
            if(Lu16ListenHeight > (Lpu16SnsEchoThresholdTable[Lu8TableIndex] - Lu16ListenThresCompenset))
            {
                LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - MAP_LISTEN_COMPEN_DIS;
                LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - MAP_LISTEN_COMPEN_DIS;
                LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                Lu8FindValidObjFlag = 1;
            }
        }
        if(Lu8FindValidObjFlag)
        {
            if(LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] > LptrSnsJudgeObjTypeThresholdTable[Lu8TableHighIndex].u16BigWallHeight)
            {
                LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt] = OBJ_BIGWALL_TYPE;
            }
            else
            {
                LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt] = OBJ_PVC_PIPE_TYPE;
            }
            
#if 1
            /* 只保留小障碍物后的大障碍物逻辑处理--2023-11-13 */
            if(Lu8SigGroupCnt > 0)
            {
                if((LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt]+1000) < LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt-1])
                {
                    break;
                }
            }
#endif
            Lu8SigGroupCnt++;
        }

        
        if(Lu8SigGroupCnt == MAX_NUM_OF_MAP_SIG_GROUP)
        {
            break;
        }
    }
    LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u8SigGroupCnt = Lu8SigGroupCnt;
    /* 保留备份值 */
    for(i = 0; i < MAX_NUM_OF_MAP_SIG_GROUP; i++)
    {
        LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstDisBackup[i] = LpstrMapSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[i];
    }
    SetMapSigGroupDataUpdateFlag(LeGroup,LePDCSnsCh);
    UpdateMapSnsRealTimeDis(LeGroup,LePDCSnsCh);
    SnsCarCoverJudge(LeGroup,LePDCSnsCh,LpStrPDCSnsRawData);
}




