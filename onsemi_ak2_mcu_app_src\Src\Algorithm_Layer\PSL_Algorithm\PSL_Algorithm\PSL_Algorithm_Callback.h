/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * PSL_Algorithm.h: 
 * Created on: 2023-2-22 17:30
 * Original designer: 22866
 ******************************************************************************/


#ifndef PSL_Algorithm_Callback_H
#define PSL_Algorithm_Callback_H



/* ============================================================================================== */
/* Header files																				      */
/* ============================================================================================== */
#include "SnsRawData_Type.h"
#include "PSL_Algorithm_Types.h"
#include "SnsEchoFilterAndSigGroup_int.h"
#include "SnsRawData_Int.h"





/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/

#define PSL_INVALID_OBJ_COOR     0.0
#define PSL_INVALID_DIS          0x1FFF
#define PSL_INVALID_WIDTH        0u

/* 定义PSL DIS CYSLE类型 */
typedef enum
{
    PSL_DIS_CYSLE0 = 0,
    PSL_DIS_CYSLE1,
    PSL_DIS_CYSLE2,
    PSL_DIS_CYSLE_NUM
}PSLDisCycleType;



typedef enum
{
    PSL_DIS_NONE = -1,
    PSL_DIS_FIRST, 
    PSL_DIS_SECON,
    PSL_DIS_THIRD,
    PSL_DIS_NUM  
}PSL_DisFollowType; 


typedef struct
{
    uint16 u16MasterDis[PSL_SNS_CH_NUM][PSL_DIS_NUM];
    uint16 u16LeftDis[PSL_SNS_CH_NUM][PSL_DIS_NUM];
    uint16 u16RightDis[PSL_SNS_CH_NUM][PSL_DIS_NUM];
    uint8 u8MasterCnt[PSL_SNS_CH_NUM];
    uint8 u8LeftCnt[PSL_SNS_CH_NUM];
    uint8 u8RightCnt[PSL_SNS_CH_NUM];
    uint16 u16MasterOutputDis[PSL_SNS_CH_NUM];
    uint16 u16LeftOutputDis[PSL_SNS_CH_NUM];
    uint16 u16RightOutputDis[PSL_SNS_CH_NUM];
    PSLDisCycleType enuCurrentCycle[PSL_SNS_CH_NUM];
}PSLDisFollowType;

/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
extern PSLObjPositionCoorType GstrPSLObjCoorData;


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
extern void PSLUpdateRawCoor(void);
extern void PSLDisFollowDataInit(void);
extern void PSLObjPositionCoorInit();
#endif

