/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * PSL_Algorithm.c: 
 * Created on: 2023-2-22 17:30
 * Original designer: 22866
 ******************************************************************************/

/* ============================================================================================== */
/* Header files																				      */
/* ============================================================================================== */
#include "PSL_Algorithm_Callback.h"
#include "Sns_install_Coordinate.h"
#include "PSL_Algorithm_Cfg.h"
#include "PSL_EchoFilterAndSigGroup_int.h"






/******************************************************************************/
/******************************************************************************/
/****************************** Private Definition ****************************/
/******************************************************************************/
/******************************************************************************/





/******************************************************************************/
/******************************************************************************/
/****************************** Public Definition *****************************/
/******************************************************************************/
/******************************************************************************/

PSLObjPositionCoorType GstrPSLObjCoorData;
PSLCalObjCoorType GstrPSLCalObjCoor;
PSLDisFollowType GstrPSLDisFollowData[PSL_DIS_CYSLE_NUM];



/******************************************************************************/
/******************************************************************************/
/******************************* ****** Function ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************
 * 函数名称: PSLObjCoorDataClear
 * 
 * 功能描述: PSL模块更新障碍物数据
 * 
 * 输入参数:
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-7-28 11:28    V0.1      22866        初次发布
 ******************************************************************************/

void PSLObjCoorDataClear(PSLSnsChannelType LenuPSLSnsCh)
{
    PSLCalObjCoorType *LpstrPSLObjCoor;
    LpstrPSLObjCoor = &GstrPSLCalObjCoor;
    uint8 i;

    for(i = PSL_OBJ_FIRST;i < PSL_OBJ_NUM;i++)
    {
        LpstrPSLObjCoor->strCarLeftObj[LenuPSLSnsCh][i].fObjX = PSL_DET_INVALID_COOR;
        LpstrPSLObjCoor->strCarLeftObj[LenuPSLSnsCh][i].fObjY = PSL_DET_INVALID_COOR;
        LpstrPSLObjCoor->strCarMasterObj[LenuPSLSnsCh][i].fObjX = PSL_DET_INVALID_COOR;
        LpstrPSLObjCoor->strCarMasterObj[LenuPSLSnsCh][i].fObjY = PSL_DET_INVALID_COOR;
        LpstrPSLObjCoor->strCarRightObj[LenuPSLSnsCh][i].fObjX = PSL_DET_INVALID_COOR;
        LpstrPSLObjCoor->strCarRightObj[LenuPSLSnsCh][i].fObjY = PSL_DET_INVALID_COOR;

        LpstrPSLObjCoor->strSnsLeftObj[LenuPSLSnsCh][i].fObjX = PSL_DET_INVALID_COOR;
        LpstrPSLObjCoor->strSnsLeftObj[LenuPSLSnsCh][i].fObjY = PSL_DET_INVALID_COOR;
        LpstrPSLObjCoor->strSnsMasterObj[LenuPSLSnsCh][i].fObjX = PSL_DET_INVALID_COOR;
        LpstrPSLObjCoor->strSnsMasterObj[LenuPSLSnsCh][i].fObjY = PSL_DET_INVALID_COOR;
        LpstrPSLObjCoor->strSnsRightObj[LenuPSLSnsCh][i].fObjX = PSL_DET_INVALID_COOR;
        LpstrPSLObjCoor->strSnsRightObj[LenuPSLSnsCh][i].fObjY = PSL_DET_INVALID_COOR;

        LpstrPSLObjCoor->u16MasterDis[LenuPSLSnsCh][i] = PSL_ECHO_INVALID_DATA;
        LpstrPSLObjCoor->u16LeftDis[LenuPSLSnsCh][i] = PSL_ECHO_INVALID_DATA;
        LpstrPSLObjCoor->u16RightDis[LenuPSLSnsCh][i] = PSL_ECHO_INVALID_DATA;
        LpstrPSLObjCoor->u16MasterHeight[LenuPSLSnsCh][i] = PSL_DET_INVALID_DATA;
        LpstrPSLObjCoor->u16LeftHeight[LenuPSLSnsCh][i] = PSL_DET_INVALID_DATA;
        LpstrPSLObjCoor->u16RightHeight[LenuPSLSnsCh][i] = PSL_DET_INVALID_DATA;
    }
}

/******************************************************************************
 * 函数名称: PSLDisFollowDataInit
 * 
 * 功能描述: PSL距离跟踪数据初始化
 * 
 * 输入参数:
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-25 11:16    V0.1      22866        初次发布
 ******************************************************************************/
void PSLDisFollowDataInit(void)
{
    PSLDisCycleType LenuPslDisCyce;
    PSLSnsChannelType LenuPSLSnsch;
    PSL_DisFollowType LenuDisNum;
    PSLDisFollowType *LpPSLDisFollow;
    for(LenuPslDisCyce = PSL_DIS_CYSLE0;LenuPslDisCyce < PSL_DIS_CYSLE_NUM;LenuPslDisCyce++)
    {
        LpPSLDisFollow = &GstrPSLDisFollowData[LenuPslDisCyce];
        for(LenuPSLSnsch = PSL_SNS_CH_FLS;LenuPSLSnsch < PSL_SNS_CH_NUM;LenuPSLSnsch++)
        {
            for(LenuDisNum = PSL_DIS_FIRST;LenuDisNum < PSL_DIS_NUM;LenuDisNum++)
            {
                LpPSLDisFollow->u16MasterDis[LenuPSLSnsch][LenuDisNum] = PSL_ECHO_INVALID_DATA;
                LpPSLDisFollow->u16LeftDis[LenuPSLSnsch][LenuDisNum] = PSL_ECHO_INVALID_DATA;
                LpPSLDisFollow->u16RightDis[LenuPSLSnsch][LenuDisNum] = PSL_ECHO_INVALID_DATA;           
            }
            LpPSLDisFollow->u16MasterOutputDis[LenuPSLSnsch] = PSL_ECHO_INVALID_DATA;
            LpPSLDisFollow->u16LeftOutputDis[LenuPSLSnsch] = PSL_ECHO_INVALID_DATA;
            LpPSLDisFollow->u16RightOutputDis[LenuPSLSnsch] = PSL_ECHO_INVALID_DATA;
            LpPSLDisFollow->enuCurrentCycle[LenuPSLSnsch] = PSL_DIS_CYSLE0;
            LpPSLDisFollow->u8MasterCnt[LenuPSLSnsch] = PSL_DIS_FIRST;
            LpPSLDisFollow->u8LeftCnt[LenuPSLSnsch] = PSL_DIS_FIRST;
            LpPSLDisFollow->u8RightCnt[LenuPSLSnsch] = PSL_DIS_FIRST;
        }
    }
}
/******************************************************************************
 * 函数名称: PSLObjPositionCoorInit
 * 
 * 功能描述: PSL模块更新障碍物数据
 * 
 * 输入参数:
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-2-24 11:28    V0.1      22866        初次发布
 ******************************************************************************/

void PSLObjPositionCoorInit(void)
{
    PSLObjPositionCoorType *LpPSLOBjData;
    LpPSLOBjData = &GstrPSLObjCoorData;
    PSLSnsChannelType LenuPSLSnsch;
    for(LenuPSLSnsch = PSL_SNS_CH_FLS;LenuPSLSnsch < PSL_SNS_CH_NUM;LenuPSLSnsch++)
    {
        LpPSLOBjData->strSnsLeftObj[LenuPSLSnsch].fObjX = PSL_INVALID_OBJ_COOR;
        LpPSLOBjData->strSnsLeftObj[LenuPSLSnsch].fObjY = PSL_INVALID_OBJ_COOR;
        LpPSLOBjData->strSnsMasterObj[LenuPSLSnsch].fObjX = PSL_INVALID_OBJ_COOR;
        LpPSLOBjData->strSnsMasterObj[LenuPSLSnsch].fObjY = PSL_INVALID_OBJ_COOR;
        LpPSLOBjData->strSnsRightObj[LenuPSLSnsch].fObjX = PSL_INVALID_OBJ_COOR;
        LpPSLOBjData->strSnsRightObj[LenuPSLSnsch].fObjY = PSL_INVALID_OBJ_COOR;
        LpPSLOBjData->u8SnsActiveFlag[LenuPSLSnsch] = 0;
        LpPSLOBjData->u16MasterDis[LenuPSLSnsch] = PSL_INVALID_DIS;
        LpPSLOBjData->u16MasterHeight[LenuPSLSnsch] = PSL_INVALID_WIDTH;
        LpPSLOBjData->enuMeasMode[LenuPSLSnsch] = PSL_SNS_MEAS_IDLE;
        PSLObjCoorDataClear(LenuPSLSnsch);
    }
}


/******************************************************************************
 * 函数名称: PSLSnsChCal
 * 
 * 功能描述: PSL通道
 * 
 * 输入参数:
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-7-27 14:05    V0.1      22866        初次发布
 ******************************************************************************/
PSLSnsChannelType PSLSnsChCal(PDCSnsGroupType LenuPDCSnsGroup,PDCSnsChannelType LenuPDCSnsCh)        
{
    PSLSnsChannelType PSLSnsCh = PSL_SNS_CH_NUM;
    
    if(LenuPDCSnsGroup == PDC_SNS_GROUP_FRONT)
    {
        if(LenuPDCSnsCh == PDC_SNS_CH_FLS_RLS)
        {
            PSLSnsCh = PSL_SNS_CH_FLS;
        }
        else if(LenuPDCSnsCh == PDC_SNS_CH_FRS_RRS)
        {
            PSLSnsCh = PSL_SNS_CH_FRS;
        }
    }
    else if(LenuPDCSnsGroup == PDC_SNS_GROUP_REAR)
    {
        if(LenuPDCSnsCh == PDC_SNS_CH_FLS_RLS)
        {
            PSLSnsCh = PSL_SNS_CH_RLS;
        }
        else if(LenuPDCSnsCh == PDC_SNS_CH_FRS_RRS)
        {
            PSLSnsCh = PSL_SNS_CH_RRS;
        }
    }

    return PSLSnsCh;
}

/******************************************************************************
 * 函数名称: PublicJudgeTriangleValid
 * 
 * 功能描述: 判定三角形的有效性
 * 
 * 输入参数:Lu16FirstSideDis--三角形的第一个边长，Lu16FirstSideDis--三角形的第二个边长，Lu16ThirdSideDis--三角形的第三个边长
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-12-28 11:01   V0.1      AntonyFang   初次发布
 ******************************************************************************/
bool PublicJudgeTriangleValid(uint16 Lu16FirstSideDis,uint16 Lu16SecondSideDis,uint16 Lu16ThirdSideDis)
{
    if((Lu16FirstSideDis+Lu16SecondSideDis) <= Lu16ThirdSideDis)
    {
        return FALSE;
    }
    else if((Lu16FirstSideDis+Lu16ThirdSideDis) <= Lu16SecondSideDis)
    {
        return FALSE;
    }
    else if((Lu16SecondSideDis+Lu16ThirdSideDis) <= Lu16FirstSideDis)
    {
        return FALSE;
    }
    else
    {
        return TRUE;
    }
}
#if 0
/******************************************************************************
 * 函数名称: PSLMasterDisFollow
 * 
 * 功能描述: 
 * 
 * 输入参数:
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-25 10:30    V0.1      22866        初次发布
 ******************************************************************************/
void PSLRawDisFollow(PSLSnsChannelType LenuPSLSnsCh,SnsSigGroupDataUnitType *LpStrSnsSigGroupMaster,SnsSigGroupDataUnitType *LpStrSnsSigGroupLeft,SnsSigGroupDataUnitType *LpStrSnsSigGroupRight)
{
    uint8 i,j,k;
    uint8 u8MasterSigCnt = 0;
    uint8 u8LeftSigCnt = 0;
    uint8 u8RightSigCnt = 0;
    uint16 Lu16MasterDis = 0;
    uint8 u8FirstEchoValidCnt = 0;
    uint8 u8SecondEchoValidCnt = 0;
    uint8 u8ThirdEchoValidCnt = 0;
    PSLDisCycleType LenuCurCycle = PSL_DIS_CYSLE0;
    PSLDisCycleType LenuPreCycle = PSL_DIS_CYSLE0;
    PSLDisCycleType LenuPrePreCycle = PSL_DIS_CYSLE0;
    PSLDisFollowType *LpPSLDisFollow;
    PSLDisFollowType *LpPSLPreDisFollow;
    PSLDisFollowType *LpPSLPrePreDisFollow;
      
    for(LenuCurCycle = PSL_DIS_CYSLE0;LenuCurCycle < PSL_DIS_CYSLE2;LenuCurCycle++)
    {
        LpPSLDisFollow = &GstrPSLDisFollowData[LenuCurCycle];
        LpPSLPreDisFollow = &GstrPSLDisFollowData[LenuCurCycle + 1];
        LpPSLDisFollow->u8MasterCnt[LenuPSLSnsCh] = LpPSLPreDisFollow->u8MasterCnt[LenuPSLSnsCh];
        LpPSLDisFollow->u8LeftCnt[LenuPSLSnsCh] = LpPSLPreDisFollow->u8LeftCnt[LenuPSLSnsCh];
        LpPSLDisFollow->u8RightCnt[LenuPSLSnsCh] = LpPSLPreDisFollow->u8RightCnt[LenuPSLSnsCh];
        LpPSLDisFollow->u16MasterOutputDis[LenuPSLSnsCh] = LpPSLPreDisFollow->u16MasterOutputDis[LenuPSLSnsCh];
        LpPSLDisFollow->u16LeftOutputDis[LenuPSLSnsCh] = LpPSLPreDisFollow->u16LeftOutputDis[LenuPSLSnsCh];
        LpPSLDisFollow->u16RightOutputDis[LenuPSLSnsCh] = LpPSLPreDisFollow->u16RightOutputDis[LenuPSLSnsCh];
        u8MasterSigCnt = LpPSLDisFollow->u8MasterCnt[LenuPSLSnsCh];
        for(i = 0;i < u8MasterSigCnt;i++)
        {
            LpPSLDisFollow->u16MasterDis[LenuPSLSnsCh][i] = LpPSLPreDisFollow->u16MasterDis[LenuPSLSnsCh][i];
        }
        u8LeftSigCnt = LpPSLDisFollow->u8LeftCnt[LenuPSLSnsCh];
        for(i = 0;i < u8LeftSigCnt;i++)
        {
            LpPSLDisFollow->u16LeftDis[LenuPSLSnsCh][i] = LpPSLPreDisFollow->u16LeftDis[LenuPSLSnsCh][i];
        }
        u8RightSigCnt = LpPSLDisFollow->u8RightCnt[LenuPSLSnsCh];
        for(i = 0;i < u8RightSigCnt;i++)
        {
            LpPSLDisFollow->u16RightDis[LenuPSLSnsCh][i] = LpPSLPreDisFollow->u16RightDis[LenuPSLSnsCh][i];
        }
        LpPSLDisFollow->u16MasterOutputDis[LenuPSLSnsCh] = LpPSLPreDisFollow->u16MasterOutputDis[LenuPSLSnsCh];
        LpPSLDisFollow->u16LeftOutputDis[LenuPSLSnsCh] = LpPSLPreDisFollow->u16LeftOutputDis[LenuPSLSnsCh];
        LpPSLDisFollow->u16RightOutputDis[LenuPSLSnsCh] = LpPSLPreDisFollow->u16RightOutputDis[LenuPSLSnsCh];
    }
    LpPSLDisFollow = &GstrPSLDisFollowData[LenuCurCycle];
    LpPSLDisFollow->u8MasterCnt[LenuPSLSnsCh] = LpStrSnsSigGroupMaster->u8SigGroupCnt;
    LpPSLDisFollow->u8LeftCnt[LenuPSLSnsCh] = LpStrSnsSigGroupLeft->u8SigGroupCnt;
    LpPSLDisFollow->u8RightCnt[LenuPSLSnsCh] = LpStrSnsSigGroupRight->u8SigGroupCnt;
    u8MasterSigCnt = LpPSLDisFollow->u8MasterCnt[LenuPSLSnsCh];
    //printf("MasterCnt:%d,ch:%d\r\n",u8MasterSigCnt,LenuPSLSnsCh);
    for(i = 0;i < u8MasterSigCnt;i++)
    {
        //printf("Master Cnt:%d,Dis:%d\r\n",i,LpStrSnsSigGroupMaster->u16FirstEchoDis[i]);
        LpPSLDisFollow->u16MasterDis[LenuPSLSnsCh][i] = LpStrSnsSigGroupMaster->u16FirstEchoDis[i];
    }
//    u8LeftSigCnt = LpPSLDisFollow->u8LeftCnt[LenuPSLSnsCh];
//    printf("u8LeftSigCnt:%d\r\n",u8LeftSigCnt);
//    for(i = 0;i < u8LeftSigCnt;i++)
//    {
//        printf("Left Cnt:%d,Dis:%d\r\n",i,LpStrSnsSigGroupLeft->u16FirstEchoDis[i]);
//        LpPSLDisFollow->u16LeftDis[LenuPSLSnsCh][i] = LpStrSnsSigGroupLeft->u16FirstEchoDis[i];
//    }
//    u8RightSigCnt = LpPSLDisFollow->u8RightCnt[LenuPSLSnsCh];
//    printf("u8RightSigCnt:%d\r\n",u8RightSigCnt);
//    for(i = 0;i < u8RightSigCnt;i++)
//    {
//        printf("Right Cnt:%d,Dis:%d\r\n",i,LpStrSnsSigGroupRight->u16FirstEchoDis[i]);
//        LpPSLDisFollow->u16RightDis[LenuPSLSnsCh][i] = LpStrSnsSigGroupRight->u16FirstEchoDis[i];
//    }
    for(i = 0;i < u8MasterSigCnt;i++)
    {
        Lu16MasterDis = LpPSLDisFollow->u16MasterDis[LenuPSLSnsCh][i];
        //printf("MasterCnt i=%d,Dis=%d\r\n",i,Lu16MasterDis);
        LpPSLPreDisFollow = &GstrPSLDisFollowData[PSL_DIS_CYSLE1];
        for(j = 0;j < LpPSLPreDisFollow->u8MasterCnt[LenuPSLSnsCh];j++)
        {
            if((ABS(Lu16MasterDis,LpPSLPreDisFollow->u16MasterDis[LenuPSLSnsCh][i]) < 60))
            {
                if(i == 0)
                {
                    u8FirstEchoValidCnt++;
                }
                else if(i == 1)
                {
                    u8SecondEchoValidCnt++;
                }
                else if(i == 2)
                {
                    u8ThirdEchoValidCnt++;
                }
            }
        }

        LpPSLPrePreDisFollow = &GstrPSLDisFollowData[PSL_DIS_CYSLE0];
        for(k = 0;k < LpPSLPrePreDisFollow->u8MasterCnt[LenuPSLSnsCh];k++)
        {
            if((ABS(Lu16MasterDis,LpPSLPrePreDisFollow->u16MasterDis[LenuPSLSnsCh][i]) < 60))
            {
                if(i == 0)
                {
                    u8FirstEchoValidCnt++;
                }
                else if(i == 1)
                {
                    u8SecondEchoValidCnt++;
                }
                else if(i == 2)
                {
                    u8ThirdEchoValidCnt++;
                }
            }
        }
    }

    i = MAX_VALUE(u8FirstEchoValidCnt,u8SecondEchoValidCnt);
    i = MAX_VALUE(i,u8ThirdEchoValidCnt);
    if(i == u8ThirdEchoValidCnt && u8ThirdEchoValidCnt != 0)
    {
        LpPSLDisFollow->u16MasterOutputDis[LenuPSLSnsCh] = LpPSLDisFollow->u16MasterDis[LenuPSLSnsCh][PSL_DIS_THIRD];
    }
    else if(i == u8SecondEchoValidCnt && u8SecondEchoValidCnt != 0)
    {
        LpPSLDisFollow->u16MasterOutputDis[LenuPSLSnsCh] = LpPSLDisFollow->u16MasterDis[LenuPSLSnsCh][PSL_DIS_SECON];
    }
    else 
    {
        if(u8MasterSigCnt == 0)
        {
            LpPSLDisFollow->u16MasterOutputDis[LenuPSLSnsCh] = PSL_ECHO_INVALID_DATA;
        }
        else
        {
            LpPSLDisFollow->u16MasterOutputDis[LenuPSLSnsCh] = LpPSLDisFollow->u16MasterDis[LenuPSLSnsCh][PSL_DIS_FIRST];
        }
    }
    //printf("FirstCnt:%d,FollowMaster:%d\r\n",u8FirstEchoValidCnt,LpPSLDisFollow->u16MasterOutputDis[LenuPSLSnsCh]);
}
#endif
/******************************************************************************
 * 函数名称: PSLCalMasterCoor
 * 
 * 功能描述: PSL模块计算障碍物坐标
 * 
 * 输入参数:
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-7-27 14:40    V0.1      22866        初次发布
 ******************************************************************************/

void PSLCalMasterCoor(PSLSnsChannelType LenuPSLSnsCh,PSLSigGroupDataUnitType *LpStrSnsSigGroupMaster)
{
    uint8 i;
    uint16 Lu16MasterDis;
    uint16 Lu16MasterHeight;
    uint8  u8MasterSigCnt;
    PSLCalObjCoorType *LpstrPSLObjCoor;
    LpstrPSLObjCoor = &GstrPSLCalObjCoor;
    PSLDisFollowType *LpPSLDisFollow;
    LpPSLDisFollow = &GstrPSLDisFollowData[PSL_DIS_CYSLE2];
    u8MasterSigCnt = LpStrSnsSigGroupMaster->u8SigGroupCnt;
    if(u8MasterSigCnt > 2)
    {
        u8MasterSigCnt = 2;
    }
    for(i = 0;i < u8MasterSigCnt;i++)
    {
        Lu16MasterDis = LpStrSnsSigGroupMaster->u16ActualDis[i];
        //Lu16MasterDis = LpPSLDisFollow->u16MasterOutputDis[LenuPSLSnsCh];
        Lu16MasterHeight = LpStrSnsSigGroupMaster->u16FirstEchoHeight[i];
        
        if(LenuPSLSnsCh == PSL_SNS_CH_FLS || LenuPSLSnsCh == PSL_SNS_CH_FRS)
        {
          
            LpstrPSLObjCoor->strSnsMasterObj[LenuPSLSnsCh][i].fObjX = Lu16MasterDis;
            LpstrPSLObjCoor->strSnsMasterObj[LenuPSLSnsCh][i].fObjY = 0;
           
        }
        else if(LenuPSLSnsCh == PSL_SNS_CH_RLS || LenuPSLSnsCh == PSL_SNS_CH_RRS)
        {
           
            LpstrPSLObjCoor->strSnsMasterObj[LenuPSLSnsCh][i].fObjX = -Lu16MasterDis;
            LpstrPSLObjCoor->strSnsMasterObj[LenuPSLSnsCh][i].fObjY = 0;
          
        }
        LpstrPSLObjCoor->u16MasterHeight[LenuPSLSnsCh][i] = Lu16MasterHeight;
        LpstrPSLObjCoor->u16MasterDis[LenuPSLSnsCh][i] = Lu16MasterDis;

             
    }
}
/******************************************************************************
 * 函数名称: PSLCalLeftListenCoor
 * 
 * 功能描述: PSL模块计算障碍物坐标
 * 
 * 输入参数:
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-7-27 16:10    V0.1      22866        初次发布
 ******************************************************************************/

void PSLCalLeftListenCoor(PDCSnsGroupType LenuPDCSnsGroup,PSLSnsChannelType LenuPSLSnsCh,PSLSigGroupDataUnitType *LpStrSnsSigGroupMaster,PSLSigGroupDataUnitType *LpStrSnsSigGroupLeft)
{
    uint8 i,j;
    uint16 Lu16ListenOneSideDis;
    uint16 Lu16MasterDis;
    uint16 Lu16LeftDis;
    uint16 Lu16SnsSpace;
    uint16 Lu16MasterListenSub = 0;
    bool LenuTriangleValidFlag = 0;
    float LfObjCosAngle = 0.0;
    float LfObjSinAngle;
    float LfObjAngle;

    PSLCalObjCoorType *LpstrPSLObjCoor;
    LpstrPSLObjCoor = &GstrPSLCalObjCoor;
    PSLDisFollowType *LpPSLDisFollow;
    LpPSLDisFollow = &GstrPSLDisFollowData[PSL_DIS_CYSLE2];

    uint16 Lu16MasterHeight;
    uint16 Lu16ListenHeight;
    
    uint8 Lu8MatchListenInx = 0;
    uint8 u8MasterEchoCnt;
    uint8 u8LeftEchoCnt;
    eSnsChannelType enuSnsTempCh = SNS_CH_FLS;
    
    u8MasterEchoCnt = LpStrSnsSigGroupMaster->u8SigGroupCnt;
    if(u8MasterEchoCnt > 2)
    {
        u8MasterEchoCnt = 2;
    }

    u8LeftEchoCnt = LpStrSnsSigGroupLeft->u8SigGroupCnt;
    if(u8LeftEchoCnt > 2)
    {
        u8LeftEchoCnt = 2;
    }
    if(LenuPSLSnsCh == PSL_SNS_CH_FLS || LenuPSLSnsCh == PSL_SNS_CH_RLS)
    {
        enuSnsTempCh = SNS_CH_FRLS;
    }
    else if(LenuPSLSnsCh == PSL_SNS_CH_FRS || LenuPSLSnsCh == PSL_SNS_CH_RRS)
    {
        enuSnsTempCh = SNS_CH_FRRS;
    }
    Lu16SnsSpace = (uint16)GstrSnsAdjacentDis[LenuPDCSnsGroup].fRadarLeftDis[enuSnsTempCh];       
    Lu8MatchListenInx = 0;
    for(i = 0; i < u8MasterEchoCnt; i++)
    {
        Lu16MasterDis = LpStrSnsSigGroupMaster->u16ActualDis[i];
        //Lu16MasterDis = LpPSLDisFollow->u16MasterOutputDis[LenuPSLSnsCh];
        Lu16MasterHeight = LpStrSnsSigGroupMaster->u16FirstEchoHeight[i];      
     
        for(j = Lu8MatchListenInx; j < u8LeftEchoCnt; j++)
        {
            Lu16LeftDis = LpStrSnsSigGroupLeft->u16ActualDis[j];
            Lu16LeftDis *= 2; 
            if(Lu16MasterDis < Lu16LeftDis)
            {         
                Lu16ListenOneSideDis = Lu16LeftDis - Lu16MasterDis;
                Lu16MasterListenSub = ABS(Lu16MasterDis,Lu16ListenOneSideDis);
                          
                Lu16ListenHeight = LpStrSnsSigGroupLeft->u16FirstEchoHeight[j];
                               
                if(Lu16MasterListenSub < 800)
                {
                    LenuTriangleValidFlag = PublicJudgeTriangleValid(Lu16MasterDis,Lu16ListenOneSideDis,Lu16SnsSpace);
                    if(LenuTriangleValidFlag == TRUE)
                    {
                        LfObjCosAngle = PubAI_CalObjCosAngle(Lu16ListenOneSideDis,Lu16MasterDis,Lu16SnsSpace);
                        if((LfObjCosAngle >= -1.0)&&(LfObjCosAngle <= 1.0))
                        {
                            LfObjAngle = acosf(LfObjCosAngle);
                            LfObjSinAngle = sinf(LfObjAngle);
                            if(LenuPSLSnsCh == PSL_SNS_CH_FRS)
                            {
                                LpstrPSLObjCoor->strSnsLeftObj[LenuPSLSnsCh][Lu8MatchListenInx].fObjX = Lu16MasterDis*LfObjSinAngle;
                                LpstrPSLObjCoor->strSnsLeftObj[LenuPSLSnsCh][Lu8MatchListenInx].fObjY = Lu16MasterDis*LfObjCosAngle;
                            }
                            else if(LenuPSLSnsCh == PSL_SNS_CH_RRS)
                            {
                                LpstrPSLObjCoor->strSnsLeftObj[LenuPSLSnsCh][Lu8MatchListenInx].fObjX = -Lu16MasterDis*LfObjSinAngle;
                                LpstrPSLObjCoor->strSnsLeftObj[LenuPSLSnsCh][Lu8MatchListenInx].fObjY = Lu16MasterDis*LfObjCosAngle;  
                            }       
                            LpstrPSLObjCoor->u16LeftHeight[LenuPSLSnsCh][i] = Lu16MasterHeight;
                            LpstrPSLObjCoor->u16LeftDis[LenuPSLSnsCh][i] = Lu16ListenOneSideDis;

                            
                            Lu8MatchListenInx++;
                            break;   /* 本次主发匹配到就跳出 */
                        }
                    }
                }
            }
        }
    
    }
    
}

/******************************************************************************
 * 函数名称: PSLCalRightListenCoor
 * 
 * 功能描述: PSL模块计算障碍物坐标
 * 
 * 输入参数:
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-7-27 16:10    V0.1      22866        初次发布
 ******************************************************************************/

void PSLCalRightListenCoor(PDCSnsGroupType LenuPDCSnsGroup,PSLSnsChannelType LenuPSLSnsCh,PSLSigGroupDataUnitType *LpStrSnsSigGroupMaster,PSLSigGroupDataUnitType *LpStrSnsSigGroupRight)
{
    uint8 i,j;
    uint16 Lu16ListenOneSideDis;
    uint16 Lu16MasterDis;
    uint16 Lu16RightDis;
    uint16 Lu16SnsSpace;
    uint16 Lu16MasterListenSub = 0;
    bool LenuTriangleValidFlag = 0;
    float LfObjCosAngle = 0.0;
    float LfObjSinAngle;
    float LfObjAngle;

    PSLCalObjCoorType *LpstrPSLObjCoor;
    LpstrPSLObjCoor = &GstrPSLCalObjCoor;
    PSLDisFollowType *LpPSLDisFollow;
    LpPSLDisFollow = &GstrPSLDisFollowData[PSL_DIS_CYSLE2];

    uint16 Lu16MasterHeight;
    uint16 Lu16ListenHeight;
    
    uint8 Lu8MatchListenInx = 0;
    uint8 u8MasterEchoCnt;
    uint8 u8RightEchoCnt;
    eSnsChannelType enuSnsTempCh = SNS_CH_FLS;
    u8MasterEchoCnt = LpStrSnsSigGroupMaster->u8SigGroupCnt;
    if(u8MasterEchoCnt > 2)
    {
        u8MasterEchoCnt = 2;
    }

    u8RightEchoCnt = LpStrSnsSigGroupRight->u8SigGroupCnt;
    if(u8RightEchoCnt > 2)
    {
        u8RightEchoCnt = 2;
    }
    if(LenuPSLSnsCh == PSL_SNS_CH_FLS || LenuPSLSnsCh == PSL_SNS_CH_RLS)
    {
        enuSnsTempCh = SNS_CH_FRLS;
    }
    else if(LenuPSLSnsCh == PSL_SNS_CH_FRS || LenuPSLSnsCh == PSL_SNS_CH_RRS)
    {
        enuSnsTempCh = SNS_CH_FRRS;
    }
    Lu16SnsSpace = (uint16)GstrSnsAdjacentDis[LenuPDCSnsGroup].fRadarRightDis[enuSnsTempCh];       
    Lu8MatchListenInx = 0;

    for(i = 0; i < u8MasterEchoCnt; i++)
    {
        Lu16MasterDis = LpStrSnsSigGroupMaster->u16ActualDis[i];
        //Lu16MasterDis = LpPSLDisFollow->u16MasterOutputDis[LenuPSLSnsCh];
        Lu16MasterHeight = LpStrSnsSigGroupMaster->u16FirstEchoHeight[i];      
       
        for(j = Lu8MatchListenInx; j < u8RightEchoCnt; j++)
        {
            
            Lu16RightDis = LpStrSnsSigGroupRight->u16ActualDis[j];
            Lu16RightDis *= 2;
       
            if(Lu16MasterDis < Lu16RightDis)
            {
                
                
                Lu16ListenOneSideDis = Lu16RightDis - Lu16MasterDis;
                Lu16MasterListenSub = ABS(Lu16MasterDis,Lu16ListenOneSideDis);
                
            
                Lu16ListenHeight = LpStrSnsSigGroupRight->u16FirstEchoHeight[j];
                
    
                if(Lu16MasterListenSub < 800)
                {
                    LenuTriangleValidFlag = PublicJudgeTriangleValid(Lu16MasterDis,Lu16ListenOneSideDis,Lu16SnsSpace);
              
                    if(LenuTriangleValidFlag == TRUE)
                    {
                        LfObjCosAngle = PubAI_CalObjCosAngle(Lu16ListenOneSideDis,Lu16MasterDis,Lu16SnsSpace);
                 
                        if((LfObjCosAngle >= -1.0)&&(LfObjCosAngle <= 1.0))
                        {
                            LfObjAngle = acosf(LfObjCosAngle);
                            LfObjSinAngle = sinf(LfObjAngle);
                            if(LenuPSLSnsCh == PSL_SNS_CH_FLS)
                            {
                                LpstrPSLObjCoor->strSnsRightObj[LenuPSLSnsCh][Lu8MatchListenInx].fObjX = Lu16MasterDis*LfObjSinAngle;
                                LpstrPSLObjCoor->strSnsRightObj[LenuPSLSnsCh][Lu8MatchListenInx].fObjY = -Lu16MasterDis*LfObjCosAngle;
                            }
                            else if(LenuPSLSnsCh == PSL_SNS_CH_RLS)
                            {
                                LpstrPSLObjCoor->strSnsRightObj[LenuPSLSnsCh][Lu8MatchListenInx].fObjX = -Lu16MasterDis*LfObjSinAngle;
                                LpstrPSLObjCoor->strSnsRightObj[LenuPSLSnsCh][Lu8MatchListenInx].fObjY = -Lu16MasterDis*LfObjCosAngle;  
                            }       
                            LpstrPSLObjCoor->u16RightHeight[LenuPSLSnsCh][i] = Lu16MasterHeight;
                            LpstrPSLObjCoor->u16RightDis[LenuPSLSnsCh][i] = Lu16ListenOneSideDis;

                            
                            Lu8MatchListenInx++;
                            break;   /* 本次主发匹配到就跳出 */
                        }
                    }
                }
            }
        }
    
    }
    
}

/******************************************************************************
 * 函数名称: PSLObjCoorCal
 * 
 * 功能描述: PSL模块计算障碍物坐标
 * 
 * 输入参数:
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-7-27 13:55    V0.1      22866        初次发布
 ******************************************************************************/
void PSLObjCoorCal(PDCSnsGroupType LenuPDCSnsGroup,PSLSnsChannelType LenuPSLSnsCh,PSLSigGroupDataUnitType *LpStrSnsSigGroupMaster,PSLSigGroupDataUnitType *LpStrSnsSigGroupLeft,PSLSigGroupDataUnitType *LpStrSnsSigGroupRight)
{
    //PSLRawDisFollow(LenuPSLSnsCh,LpStrSnsSigGroupMaster,LpStrSnsSigGroupLeft,LpStrSnsSigGroupRight);
    if(LenuPSLSnsCh == PSL_SNS_CH_FLS || LenuPSLSnsCh == PSL_SNS_CH_RLS)
    {
            PSLCalMasterCoor(LenuPSLSnsCh,LpStrSnsSigGroupMaster);
            PSLCalRightListenCoor(LenuPDCSnsGroup,LenuPSLSnsCh,LpStrSnsSigGroupMaster,LpStrSnsSigGroupRight);
    }
    else if(LenuPSLSnsCh == PSL_SNS_CH_FRS || LenuPSLSnsCh == PSL_SNS_CH_RRS)
    {
            PSLCalMasterCoor(LenuPSLSnsCh,LpStrSnsSigGroupMaster);
            PSLCalLeftListenCoor(LenuPDCSnsGroup,LenuPSLSnsCh,LpStrSnsSigGroupMaster,LpStrSnsSigGroupLeft);
    }
}


/******************************************************************************
 * 函数名称: PSLSnsToCoorConversion
 * 
 * 功能描述: PSL模块更新障碍物数据
 * 
 * 输入参数:
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-2-24 11:28    V0.1      22866        初次发布
 ******************************************************************************/

void PSLSnsToCoorConversion(PDCSnsGroupType LenuPDCSnsGroup,PSLSnsChannelType LenuPDCSnsCh,PSLSigGroupDataUnitType *LpStrSnsSigGroupMaster,PSLSigGroupDataUnitType *LpStrSnsSigGroupLeft,PSLSigGroupDataUnitType *LpStrSnsSigGroupRight)
{
    PSLCalObjCoorType *LpstrPSLObjCoor;
    LpstrPSLObjCoor = &GstrPSLCalObjCoor;
    
    sint32 s32ObjSnsCoorX,s32ObjSnsCoorY;
    sint32 s32ObjCarCoorX,s32ObjCarCoorY;
    sint32 s32ObjOdoCoorX,s32ObjOdoCoorY;
    uint8 i;
    
    PSLSnsChannelType enuPSLSnsCh = PSL_SNS_CH_FLS;
    enuPSLSnsCh = PSLSnsChCal(LenuPDCSnsGroup,LenuPDCSnsCh);
 
    for(i = 0;i < LpStrSnsSigGroupMaster->u8GroupEchoCnt[0];i++)
    {
        
        PubAI_UpdateSnsInCarCoor(GstrRamSnsCoor[LenuPDCSnsGroup][LenuPDCSnsCh].cRadarX,GstrRamSnsCoor[LenuPDCSnsGroup][LenuPDCSnsCh].cRadarY,\
            GstrSnsOutAngle[LenuPDCSnsGroup].fSinA[LenuPDCSnsCh],GstrSnsOutAngle[LenuPDCSnsGroup].fCosA[LenuPDCSnsCh]);

        PubAI_TransObjSnsCoorToCarCoor(&LpstrPSLObjCoor->strSnsMasterObj[enuPSLSnsCh][i].fObjX,&LpstrPSLObjCoor->strSnsMasterObj[enuPSLSnsCh][i].fObjY,\
            &LpstrPSLObjCoor->strCarMasterObj[enuPSLSnsCh][i].fObjX,&LpstrPSLObjCoor->strCarMasterObj[enuPSLSnsCh][i].fObjY);

    }

    if(LpStrSnsSigGroupMaster->u8GroupEchoCnt[0] != 0)
    {
        for(i = 0;i < LpStrSnsSigGroupLeft->u8GroupEchoCnt[0];i++)
        {
            PubAI_UpdateSnsInCarCoor(GstrRamSnsCoor[LenuPDCSnsGroup][LenuPDCSnsCh].cRadarX,GstrRamSnsCoor[LenuPDCSnsGroup][LenuPDCSnsCh].cRadarY,\
                GstrSnsCoorConvAngle[LenuPDCSnsGroup].fLeftSinA[LenuPDCSnsCh],GstrSnsCoorConvAngle[LenuPDCSnsGroup].fLeftCosA[LenuPDCSnsCh]);

            PubAI_TransObjSnsCoorToCarCoor(&LpstrPSLObjCoor->strSnsLeftObj[enuPSLSnsCh][i].fObjX,&LpstrPSLObjCoor->strSnsLeftObj[enuPSLSnsCh][i].fObjY,\
                &LpstrPSLObjCoor->strCarLeftObj[enuPSLSnsCh][i].fObjX,&LpstrPSLObjCoor->strCarLeftObj[enuPSLSnsCh][i].fObjY);

        }

        for(i = 0;i < LpStrSnsSigGroupRight->u8GroupEchoCnt[0];i++)
        {
            PubAI_UpdateSnsInCarCoor(GstrRamSnsCoor[LenuPDCSnsGroup][LenuPDCSnsCh].cRadarX,GstrRamSnsCoor[LenuPDCSnsGroup][LenuPDCSnsCh].cRadarY,\
                GstrSnsCoorConvAngle[LenuPDCSnsGroup].fRightSinA[LenuPDCSnsCh],GstrSnsCoorConvAngle[LenuPDCSnsGroup].fRightCosA[LenuPDCSnsCh]);

            PubAI_TransObjSnsCoorToCarCoor(&LpstrPSLObjCoor->strSnsRightObj[enuPSLSnsCh][i].fObjX,&LpstrPSLObjCoor->strSnsRightObj[enuPSLSnsCh][i].fObjY,\
                &LpstrPSLObjCoor->strCarRightObj[enuPSLSnsCh][i].fObjX,&LpstrPSLObjCoor->strCarRightObj[enuPSLSnsCh][i].fObjY);

        }
    }
}

/******************************************************************************
 * 函数名称: UpdatePSLObjOdoCoor
 * 
 * 功能描述: PSL模块更新障碍物数据
 * 
 * 输入参数:
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-2-24 11:28    V0.1      22866        初次发布
 ******************************************************************************/

void UpdatePSLObjOdoCoor(PDCSnsGroupType LenuPDCSnsGroup,PDCSnsChannelType LenuPDCSnsCh,PDCSnsMeasTypeType enuMeasType,PSLSigGroupDataUnitType *LpStrSnsSigGroupMaster,PSLSigGroupDataUnitType *LpStrSnsSigGroupLeft,PSLSigGroupDataUnitType *LpStrSnsSigGroupRight)
{
    PSLCalObjCoorType *LpstrPSLObjCoor;
    LpstrPSLObjCoor = &GstrPSLCalObjCoor;
    
    PSLObjPositionCoorType *LpPSLOBjData;
    LpPSLOBjData = &GstrPSLObjCoorData;
    PSLSnsChannelType enuPSLSnsCh;
    PSLDisFollowType *LpPSLDisFollow;
    LpPSLDisFollow = &GstrPSLDisFollowData[PSL_DIS_CYSLE2];
    
    if(LenuPDCSnsCh > PDC_SNS_CH_FLS_RLS && LenuPDCSnsCh < PDC_SNS_CH_FRS_RRS)
        return;

#if 0
	if((LenuPDCSnsCh == 0) && LenuPDCSnsGroup == 0)
		PSL_DEBUG_PRINTF("Group:%d Ch:%d  MeasType:%d Master:%d LeftListen:%d RightListen:%d\r\n", LenuPDCSnsGroup, LenuPDCSnsCh, enuMeasType,LpStrSnsSigGroupMaster->u16ActualDis[0],
		    LpStrSnsSigGroupLeft->u16ActualDis[0],LpStrSnsSigGroupRight->u16ActualDis[0]);
#endif
    enuPSLSnsCh = PSLSnsChCal(LenuPDCSnsGroup,LenuPDCSnsCh);
    if(enuPSLSnsCh < PSL_SNS_CH_FLS || enuPSLSnsCh > PSL_SNS_CH_RRS)
        return;
#if 1    

    PSLObjCoorDataClear(enuPSLSnsCh);
    PSLObjCoorCal(LenuPDCSnsGroup,enuPSLSnsCh,LpStrSnsSigGroupMaster,LpStrSnsSigGroupLeft,LpStrSnsSigGroupRight);
    PSLSnsToCoorConversion(LenuPDCSnsGroup,LenuPDCSnsCh,LpStrSnsSigGroupMaster,LpStrSnsSigGroupLeft,LpStrSnsSigGroupRight);


#if 0
    if(LenuPDCSnsCh == 0 && LenuPDCSnsGroup == 0)
    {
        PSL_DEBUG_PRINTF("CarMasterX:%.1f,CarMasterY:%.1f\r\n",LpstrPSLObjCoor->strCarMasterObj[enuPSLSnsCh][PSL_OBJ_FIRST].fObjX,LpstrPSLObjCoor->strCarMasterObj[enuPSLSnsCh][PSL_OBJ_FIRST].fObjY);
        PSL_DEBUG_PRINTF("CarRightX:%.1f,CarRightY:%.1f\r\n",LpstrPSLObjCoor->strCarRightObj[enuPSLSnsCh][PSL_OBJ_FIRST].fObjX,LpstrPSLObjCoor->strCarRightObj[enuPSLSnsCh][PSL_OBJ_FIRST].fObjY);
    }
#endif
    if(LenuPDCSnsGroup == PDC_SNS_GROUP_FRONT)
    {
        if(LenuPDCSnsCh == PDC_SNS_CH_FLS_RLS)
        {
            LpPSLOBjData->strSnsLeftObj[PSL_SNS_CH_FLS].fObjX = LpstrPSLObjCoor->strCarLeftObj[enuPSLSnsCh][PSL_OBJ_FIRST].fObjX;
            LpPSLOBjData->strSnsLeftObj[PSL_SNS_CH_FLS].fObjY = LpstrPSLObjCoor->strCarLeftObj[enuPSLSnsCh][PSL_OBJ_FIRST].fObjY;
            LpPSLOBjData->strSnsMasterObj[PSL_SNS_CH_FLS].fObjX = LpstrPSLObjCoor->strCarMasterObj[enuPSLSnsCh][PSL_OBJ_FIRST].fObjX;
            LpPSLOBjData->strSnsMasterObj[PSL_SNS_CH_FLS].fObjY = LpstrPSLObjCoor->strCarMasterObj[enuPSLSnsCh][PSL_OBJ_FIRST].fObjY;
            LpPSLOBjData->strSnsRightObj[PSL_SNS_CH_FLS].fObjX = LpstrPSLObjCoor->strCarRightObj[enuPSLSnsCh][PSL_OBJ_FIRST].fObjX;
            LpPSLOBjData->strSnsRightObj[PSL_SNS_CH_FLS].fObjY = LpstrPSLObjCoor->strCarRightObj[enuPSLSnsCh][PSL_OBJ_FIRST].fObjY;
            LpPSLOBjData->u8SnsActiveFlag[PSL_SNS_CH_FLS] = 1;
            LpPSLOBjData->u16MasterDis[PSL_SNS_CH_FLS] = LpstrPSLObjCoor->u16MasterDis[enuPSLSnsCh][PSL_OBJ_FIRST];
            LpPSLOBjData->u16MasterHeight[PSL_SNS_CH_FLS] = LpstrPSLObjCoor->u16MasterHeight[enuPSLSnsCh][PSL_OBJ_FIRST];
            LpPSLOBjData->enuMeasMode[PSL_SNS_CH_FLS] = (PSLSnsMeasTypeType)enuMeasType;           
        }
        else if(LenuPDCSnsCh == PDC_SNS_CH_FRS_RRS)
        {
            LpPSLOBjData->strSnsLeftObj[PSL_SNS_CH_FRS].fObjX = LpstrPSLObjCoor->strCarLeftObj[enuPSLSnsCh][PSL_OBJ_FIRST].fObjX;
            LpPSLOBjData->strSnsLeftObj[PSL_SNS_CH_FRS].fObjY = LpstrPSLObjCoor->strCarLeftObj[enuPSLSnsCh][PSL_OBJ_FIRST].fObjY;
            LpPSLOBjData->strSnsMasterObj[PSL_SNS_CH_FRS].fObjX = LpstrPSLObjCoor->strCarMasterObj[enuPSLSnsCh][PSL_OBJ_FIRST].fObjX;
            LpPSLOBjData->strSnsMasterObj[PSL_SNS_CH_FRS].fObjY = LpstrPSLObjCoor->strCarMasterObj[enuPSLSnsCh][PSL_OBJ_FIRST].fObjY;
            LpPSLOBjData->strSnsRightObj[PSL_SNS_CH_FRS].fObjX = LpstrPSLObjCoor->strCarRightObj[enuPSLSnsCh][PSL_OBJ_FIRST].fObjX;
            LpPSLOBjData->strSnsRightObj[PSL_SNS_CH_FRS].fObjY = LpstrPSLObjCoor->strCarRightObj[enuPSLSnsCh][PSL_OBJ_FIRST].fObjY;
            LpPSLOBjData->u8SnsActiveFlag[PSL_SNS_CH_FRS] = 1;
            LpPSLOBjData->u16MasterDis[PSL_SNS_CH_FRS] = LpstrPSLObjCoor->u16MasterDis[enuPSLSnsCh][PSL_OBJ_FIRST];
            LpPSLOBjData->u16MasterHeight[PSL_SNS_CH_FRS] = LpstrPSLObjCoor->u16MasterHeight[enuPSLSnsCh][PSL_OBJ_FIRST];
            LpPSLOBjData->enuMeasMode[PSL_SNS_CH_FRS] = (PSLSnsMeasTypeType)enuMeasType;
        }
    }
    else if(LenuPDCSnsGroup == PDC_SNS_GROUP_REAR)
    {
        if(LenuPDCSnsCh == PDC_SNS_CH_FLS_RLS)
        {
            LpPSLOBjData->strSnsLeftObj[PSL_SNS_CH_RLS].fObjX = LpstrPSLObjCoor->strCarLeftObj[enuPSLSnsCh][PSL_OBJ_FIRST].fObjX;
            LpPSLOBjData->strSnsLeftObj[PSL_SNS_CH_RLS].fObjY = LpstrPSLObjCoor->strCarLeftObj[enuPSLSnsCh][PSL_OBJ_FIRST].fObjY;
            LpPSLOBjData->strSnsMasterObj[PSL_SNS_CH_RLS].fObjX = LpstrPSLObjCoor->strCarMasterObj[enuPSLSnsCh][PSL_OBJ_FIRST].fObjX;
            LpPSLOBjData->strSnsMasterObj[PSL_SNS_CH_RLS].fObjY = LpstrPSLObjCoor->strCarMasterObj[enuPSLSnsCh][PSL_OBJ_FIRST].fObjY;
            LpPSLOBjData->strSnsRightObj[PSL_SNS_CH_RLS].fObjX = LpstrPSLObjCoor->strCarRightObj[enuPSLSnsCh][PSL_OBJ_FIRST].fObjX;
            LpPSLOBjData->strSnsRightObj[PSL_SNS_CH_RLS].fObjY = LpstrPSLObjCoor->strCarRightObj[enuPSLSnsCh][PSL_OBJ_FIRST].fObjY;
            LpPSLOBjData->u8SnsActiveFlag[PSL_SNS_CH_RLS] = 1;
            LpPSLOBjData->u16MasterDis[PSL_SNS_CH_RLS] = LpstrPSLObjCoor->u16MasterDis[enuPSLSnsCh][PSL_OBJ_FIRST];
            LpPSLOBjData->u16MasterHeight[PSL_SNS_CH_RLS] = LpstrPSLObjCoor->u16MasterHeight[enuPSLSnsCh][PSL_OBJ_FIRST];
            LpPSLOBjData->enuMeasMode[PSL_SNS_CH_RLS] = (PSLSnsMeasTypeType)enuMeasType;
        }
        else if(LenuPDCSnsCh == PDC_SNS_CH_FRS_RRS)
        {
            LpPSLOBjData->strSnsLeftObj[PSL_SNS_CH_RRS].fObjX = LpstrPSLObjCoor->strCarLeftObj[enuPSLSnsCh][PSL_OBJ_FIRST].fObjX;
            LpPSLOBjData->strSnsLeftObj[PSL_SNS_CH_RRS].fObjY = LpstrPSLObjCoor->strCarLeftObj[enuPSLSnsCh][PSL_OBJ_FIRST].fObjY;
            LpPSLOBjData->strSnsMasterObj[PSL_SNS_CH_RRS].fObjX = LpstrPSLObjCoor->strCarMasterObj[enuPSLSnsCh][PSL_OBJ_FIRST].fObjX;
            LpPSLOBjData->strSnsMasterObj[PSL_SNS_CH_RRS].fObjY = LpstrPSLObjCoor->strCarMasterObj[enuPSLSnsCh][PSL_OBJ_FIRST].fObjY;
            LpPSLOBjData->strSnsRightObj[PSL_SNS_CH_RRS].fObjX = LpstrPSLObjCoor->strCarRightObj[enuPSLSnsCh][PSL_OBJ_FIRST].fObjX;
            LpPSLOBjData->strSnsRightObj[PSL_SNS_CH_RRS].fObjY = LpstrPSLObjCoor->strCarRightObj[enuPSLSnsCh][PSL_OBJ_FIRST].fObjY;
            LpPSLOBjData->u8SnsActiveFlag[PSL_SNS_CH_RRS] = 1;
            LpPSLOBjData->u16MasterDis[PSL_SNS_CH_RRS] = LpstrPSLObjCoor->u16MasterDis[enuPSLSnsCh][PSL_OBJ_FIRST];
            LpPSLOBjData->u16MasterHeight[PSL_SNS_CH_RRS] = LpstrPSLObjCoor->u16MasterHeight[enuPSLSnsCh][PSL_OBJ_FIRST];
            LpPSLOBjData->enuMeasMode[PSL_SNS_CH_RRS] = (PSLSnsMeasTypeType)enuMeasType;
        }
    }
#endif
}

/******************************************************************************
 * 函数名称: PSLUpdateRawCoor
 * 
 * 功能描述: PSL更新原始回波数据
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-10-30 16:15    V0.1       22866        初次发布
 ******************************************************************************/


void PSLUpdateRawCoor(void)
{
    PDCSnsGroupType LenuSnsGroup;
    PDCSnsChannelType LenuSnsCh;
 
    for(LenuSnsGroup = PDC_SNS_GROUP_FRONT;LenuSnsGroup<PDC_SNS_GROUP_NUM;LenuSnsGroup++)
    {
        for(LenuSnsCh = PDC_SNS_CH_FLS_RLS;LenuSnsCh<PDC_SNS_CH_NUM;LenuSnsCh++)
        {
            if(LenuSnsCh ==PDC_SNS_CH_FLS_RLS ||  LenuSnsCh ==PDC_SNS_CH_FRS_RRS)
            {
                PSLSnsSigGroupDataCacheType *LpPslSnsSigGroupDataCache = NULL;
                LpPslSnsSigGroupDataCache = &GstrPSLSnsSigGroupDataCache[LenuSnsGroup][LenuSnsCh];
                if(LpPslSnsSigGroupDataCache->u8SigGroupUpdateFlag)
                {
                    PSLClearSnsSigGroupDataUpdateFlag(LenuSnsGroup,LenuSnsCh);

                    UpdatePSLObjOdoCoor(LenuSnsGroup,LenuSnsCh,LpPslSnsSigGroupDataCache->SysDataBuf[LpPslSnsSigGroupDataCache->enuCurIndex].eMeasType,\
                      &LpPslSnsSigGroupDataCache->MasterBuf[LpPslSnsSigGroupDataCache->enuCurIndex],&LpPslSnsSigGroupDataCache->LeftBuf[LpPslSnsSigGroupDataCache->enuCurIndex],\
                        &LpPslSnsSigGroupDataCache->RightBuf[LpPslSnsSigGroupDataCache->enuCurIndex]);

                }
            }
        }
    }    
}

