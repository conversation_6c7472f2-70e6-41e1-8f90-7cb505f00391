/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * SDW_type: 
 * Created on: 2020-12-30 15:25
 * Original designer: <PERSON><PERSON><PERSON>
 ******************************************************************************/

#ifndef SDW_type_H
#define SDW_type_H

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "types.h"
#include "SnsRawData_Int.h"

#define VS_PYTHON_SWITCH        STD_ON     /*OFF ：使用vs 工程 */
#define SDW_LOAD_TIME_SWITCH    STD_OFF     /*负载测试开关打印*/

    
#define SIDE_SNS_MAX_ECHO_CNT 3u
#define SIDE_SNS_BUFFER_CNT   5u


#if VS_PYTHON_SWITCH == STD_OFF
#define INVALID_DIS_VAL       (uint16)65535
#define SDW_SIDE_INVALID_DISTANCE_TO_CAN     (uint16)250
#define SDW_SIDE_NO_OBJ_DISTANCE_TO_CAN      (uint16)251
#define SDW_SIDE_NO_DISPLAY_DISTANCE_TO_CAN  (uint16)255
#endif

/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************************
* 设计描述 : 左右障碍物位置枚举定义 
* 设计索引 : 
*******************************************************************************/
typedef enum
{
    LEFT_SIDE_OBJ = 0u,
    RIGHT_SIDE_OBJ,
    SIDE_OBJ_NUM
}SDW_tenuSIDE_INDEX;


/*  需要计算虚拟障碍物的个数，P81单边需要记录3个*/
typedef enum
{
    SDW_AREA_1 = 0u,
    SDW_AREA_2,
    SDW_AREA_3,
    SDW_AREA_4,
    SDW_AREA_5,
    SDW_AREA_6,
    SDW_AREA_7,
    SDW_AREA_8,
    SIDE_AREA_NUM
}SDW_tenuSIDE_AREA;


/******************************************************************************
* 设计描述 : SDW接收判断轮速脉冲方向枚举定义 
* 设计索引 : 
*******************************************************************************/
typedef enum
{
    SDW_WHEEL_DIR_STOP = 0u,
    SDW_WHEEL_DIR_FORWARD,
    SDW_WHEEL_DIR_BACKWARD,
    SIDE_WHEEL_DIR_NUM
}SDW_tenuWheelDir;





/******************************************************************************
* 设计描述 : 探头位置定义
* 设计索引 : 
*******************************************************************************/
typedef enum
{
    SDW_FLS_CHANNEL = 0u,
    SDW_FRS_CHANNEL,
    SDW_RLS_CHANNEL,
    SDW_RRS_CHANNEL,
    SDW_SNS_CHANNEL_NUM
}SDW_tenuSnsChannel;



/* 自前而后，从1到5*/
typedef enum
{
    SDW_CLOSE = 0u,
    SDW_OPEN,
    SDW_WORK_STS_NUM
}SDW_tenuWorkStatus;



/******************************************************************************
* 设计描述 : SDW 后轮CAN信息，接收从CAN接口读取的轮速脉冲等信息 
* 设计索引 : 
*******************************************************************************/
#if VS_PYTHON_SWITCH
typedef struct
{
    /* 暂时计算，后续删除*/
    SDW_tenuWheelDir enuRLWheelDir;
    SDW_tenuWheelDir enuRRWheelDir;
}SDW_WheelType;
#else
typedef struct
{
    /* 暂时计算，后续删除*/
    uint8 enuRLWheelDir;
    uint8 enuRRWheelDir;
}SDW_WheelType;

#endif


/******************************************************************************
* 设计描述 : 车身坐标计算，主要通过轮速脉冲、方向盘转角获取车身姿态坐标 
* 设计索引 : 
*******************************************************************************/
typedef struct
{
    float fFrontMoveDis;      /* 整车向前移动的距离 */
    float fRearMoveDis;       /* 整车往后移动的距离 */
    

    float fPosX;             /* X轴的坐标 */
    float fPosY;             /* Y轴的坐标 */
    float fPosAngle;         /* 车身斜度*/
    
    float fsinAngle;         /* 车身斜度的sin值*/
    float fcosAngle;         /* 车身斜度的cos值*/
    
    uint8 u8FrontMoveDis20cmFlag;      /* 车子向前移动20cm标志位  */
    uint8 u8RearMoveDis20cmFlag;       /* 整车往后移动20cm标志位  */
    SDW_tenuWheelDir enuCarMovingStatus;    /* 车子移动的状态*/
    uint16 u16CarContinueStopCnt;             /* 车辆持续停止的计数 */
        
}SDW_CarCoorType;


typedef struct
{
    float fX;
    float fY;
    float fAngle;
}SDWCurCarPointDataType;



/******************************************************************************
* 设计描述 : 侧边探头安装位置坐标结构体定义
* 设计索引 : 
*******************************************************************************/
typedef struct
{
    float fPosX;                /* 探头位置的X轴的坐标*/
    float fPosY;                /* 探头位置的Y轴的坐标*/
}SDW_SnsCoorType;


/******************************************************************************
* 设计描述 : 侧边探头安装位置坐标备份结构体定义
* 设计索引 : 
*******************************************************************************/
typedef struct
{
    float fPosX;                /* 探头位置的X轴的坐标*/
    float fPosY;                /* 探头位置的Y轴的坐标*/
    float fMovingDis;           /* 探头移动的距离 */
    uint8 u8Move10cmFlg;        /* 移动10cm标志*/
}SDW_SnsCoorBackupType;



/******************************************************************************
* 设计描述 : 障碍物坐标结构体定义
* 设计索引 : 
*******************************************************************************/
typedef struct
{
    float fPosX;                /* 障碍物的X轴的坐标*/
    float fPosY;                /* 障碍物的Y轴的坐标*/
    float fFrontSnsCosAngle;
    float fRearSnsCosAngle;
    float fObjInCraX;           /* 障碍物在车辆坐标系的X轴的坐标*/
    float fObjInCraY;           /* 障碍物在车辆坐标系的Y轴的坐标*/
    uint16 u16LateralDis;
    uint16 u16LongitudiDis;
    uint8 u8ValidFlag;          /* 障碍物坐标的有效性*/
}SDW_ObjCoorType;


/*  第一层，获取原始的SDW数据*/
typedef struct
{
    /* 经过感度表筛选后的主发、侦听距离和回波宽度 */
    uint16 u16MasterDis[SDW_SNS_CHANNEL_NUM][4];
    uint16 u16MasterHeight[SDW_SNS_CHANNEL_NUM][4];

    uint16 u16ListenDis[SDW_SNS_CHANNEL_NUM][4];
    uint16 u16ListenHeight[SDW_SNS_CHANNEL_NUM][4];


    /* 有效回波的个数、近距离障碍物标志、时间戳 */
    uint8 u8MasterObjCnt[SDW_SNS_CHANNEL_NUM];
    uint8 u8ListenObjCnt[SDW_SNS_CHANNEL_NUM];

}

SDWOriginalDataType;


/******************************************************************************
* 设计描述 : SDW提取侧边探头原始数据
* 设计索引 : 
*******************************************************************************/
typedef struct
{
    PDCSnsMeasTypeType  eMeasType[SDW_SNS_CHANNEL_NUM];    /* 添加探头的工作模式 */
    uint16 u16MasterDis[SDW_SNS_CHANNEL_NUM];
    uint16 u16MasterHeight[SDW_SNS_CHANNEL_NUM];
    uint16 u16ListenDis[SDW_SNS_CHANNEL_NUM];
    uint16 u16ListenHeight[SDW_SNS_CHANNEL_NUM];
    uint8  u8ObjCnt[SDW_SNS_CHANNEL_NUM]; 
    uint8  u8ListenObjCnt[SDW_SNS_CHANNEL_NUM]; 
    uint16 u16Dis[SDW_SNS_CHANNEL_NUM][SIDE_SNS_MAX_ECHO_CNT];
    uint16 u16Height[SDW_SNS_CHANNEL_NUM][SIDE_SNS_MAX_ECHO_CNT];
    uint8 u8ValidCnt[SDW_SNS_CHANNEL_NUM];    
}SDW_SnsOriginalDataType;

typedef struct
{
    
    uint16 u16RealTimeDis[SDW_SNS_CHANNEL_NUM];
    uint16 u16SnsDis[SDW_SNS_CHANNEL_NUM][SIDE_SNS_BUFFER_CNT];
    uint16 u16SnsHeight[SDW_SNS_CHANNEL_NUM][SIDE_SNS_BUFFER_CNT];
    uint8 u8ValidCnt[SDW_SNS_CHANNEL_NUM][SIDE_SNS_BUFFER_CNT];
    float fSnsObjX[SDW_SNS_CHANNEL_NUM][SIDE_SNS_BUFFER_CNT];
    float fSnsObjY[SDW_SNS_CHANNEL_NUM][SIDE_SNS_BUFFER_CNT];
    float fSnsCosAngle[SDW_SNS_CHANNEL_NUM][SIDE_SNS_BUFFER_CNT];
    float fSnsSinAngle[SDW_SNS_CHANNEL_NUM][SIDE_SNS_BUFFER_CNT];
}SDW_SnsBufferDataType;

/******************************************************************************
* 设计描述 : 有效障碍物坐标结构体定义
* 设计索引 : 
*******************************************************************************/
typedef struct
{
    float fPosX[SDW_SNS_CHANNEL_NUM];                /* 障碍物的X轴的坐标*/
    float fPosY[SDW_SNS_CHANNEL_NUM];                /* 障碍物的Y轴的坐标*/
    float fCosAngle[SDW_SNS_CHANNEL_NUM];
    float fSinAngle[SDW_SNS_CHANNEL_NUM];

    uint16 u16Dis[SDW_SNS_CHANNEL_NUM];              /* 障碍物的距离*/
    uint16 u16MinDisBackup[SDW_SNS_CHANNEL_NUM];     /* 障碍物的距离*/
    uint8 u8ValidFlag[SDW_SNS_CHANNEL_NUM];          /* 障碍物坐标的有效性*/
}SDW_ValidObjCoorType;

//sdw 车速 状态
typedef enum
{
    CAR_SPEED_NONE=-1,
    CAR_SPEED_LESS_1KM=0,
    CAR_SPEED_1KM_3KM=1,
    CAR_SPEED_3KM_5KM=2,
    CAR_SPEED_5KM_7KM=3,
    CAR_SPEED_7KM_9KM=4,
    CAR_SPEED_GREATER_9KM=5,
    CAR_SPEED_STATE_NUM
}SDW_Speed_StsVal;


/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/



#endif /* end of SDW_type_H */

