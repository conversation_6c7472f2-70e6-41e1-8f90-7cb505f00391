/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * PSL_Calibration.h: 
 * Created on: 2023-2-27 19:30
 * Original designer: 22866
 ******************************************************************************/


#ifndef PSL_Calibration_H
#define PSL_Calibration_H



/* ============================================================================================== */
/* Header files																				      */
/* ============================================================================================== */
//#include "Std_Types.h"
#include "Types.h"




/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/





/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************************
* 设计描述 : PSL APP Parameter
* 设计索引 : 
*******************************************************************************/

typedef struct
{
    uint8 u8OutSlotMaxNum;
    uint8 u8SideSlotMaxNum;
    uint16 u16SlotWidthMin_H;
    uint16 u16SlotWidthMax_H;
    uint16 u16SlotWidthMin_V;
    uint16 u16SlotWidthMax_V;
    uint16 u16SlotDepthMin_H;
    uint16 u16DetObjDisMin;
    uint16 u16DetObjDisMax;
    uint16 u16RomoveSlotMoveDis_H;
    uint16 u16RomoveSlotMoveDis_V;
    uint8 u8CarSlopeoffset;

}PSL_APP_PARA_TYPE;





/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
extern void PSL_APPPara_Init(void);


/******************************PSL标定参数_存储在Ram中--*********************************************/

extern PSL_APP_PARA_TYPE GstrPSL_APPPara_Ram;

#endif
