/*
 * DSI3_SPI.c
 *
 *  Created on: 2021年3月23日
 *      Author: 6000021992
 */
#include "Queue_DSI_SPI_Data.h"
#include "AK2_MCU_Drv.h"

#define DSI_SPI_RX_Que_TimeOut 10

typedef struct
{
    DSI_SPI_RX_Data_str QueueDataBuff[DSISPI_RXBuf_NUM];
    QueueDataStatus_en QueDataStatus[DSISPI_RXBuf_NUM];
    uint16 QueueTimeOutCnt[DSISPI_RXBuf_NUM];
    uint16 wQueSize;
    uint16 WriteIndexCnt;
    uint16 ReadIndexCnt;
}DSI_SPI_RX_Que_Str;

#pragma location = "R_DMA_DATA"
DSI_SPI_RX_Que_Str DSI_SPI_RX_Que;

/******************************************************************************/
/**
 * @brief            初始化DSI SPI接收队列
 * @param[in]         无
 * @param[Out]        无
 *
 * @return           无
 */
/*****************************************************************************/
void InitDSI_SPI_RX_Que()
{
    memset(&DSI_SPI_RX_Que,0,sizeof(DSI_SPI_RX_Que_Str));
    DSI_SPI_RX_Que.wQueSize = DSISPI_RXBuf_NUM;
}

/******************************************************************************/
/**
 * @brief            设置DSI SPI 接收数据Ready
 * @param[in]         DSI_SPI_RX_QueNum   BufID
 * @param[in]         
 *
 * @return           0xFF : 无效DSI_SPI_RX_QueNum
 *                   else   OK
 */
/*****************************************************************************/
uint8 SetDSI_SPI_RX_Data_Ready(uint16 DSI_SPI_RX_QueNum)
{
    QueueDataStatus_en *pQueDataStatus;
    DSI_SPI_RX_Data_str *pQueueDataBuff;
	
    pQueDataStatus = &DSI_SPI_RX_Que.QueDataStatus[0];
	pQueueDataBuff = &DSI_SPI_RX_Que.QueueDataBuff[0];

    if((DSI_SPI_RX_QueNum < DSISPI_RXBuf_NUM) && (pQueDataStatus[DSI_SPI_RX_QueNum] == QueueData_WAIT))
    {
        pQueDataStatus[DSI_SPI_RX_QueNum] = QueueData_READY;
		pQueueDataBuff[DSI_SPI_RX_QueNum].RecTime = AK2_GetSys1msTickFunc();
		return 0;
    }
    else
    {
        return 0xFF;
    }
}

/******************************************************************************/
/**
 * @brief            获取空闲的DSI SPI 接收数据地址，并将Buf状态置为Wait
 * @param[in]         BufLen
 * @param[in/out]     pOutBufNum   输出bufNum
 *
 * @return           NULL : 无空闲或数据长度超过范围
 *                   else   数据地址
 */
/*****************************************************************************/
DSI_SPI_RX_Data_str * GetDSI_SPI_RX_IdleBufAddr(uint16 BufLen,uint8 *pOutBufNum)
{
    QueueDataStatus_en *pQueDataStatus;
    DSI_SPI_RX_Data_str *pRet = NULL;
    uint16 *pTimeCnt;
    uint16 LcwQueSize = 0;
    uint8 FlgCnt = 0xFF;
    DSI_SPI_RX_Que_Str *pQueue;
    if((BufLen > DSI_SPI_TX_RX_BufLen) || (pOutBufNum == NULL))
    {
        return NULL;
    }
    pQueue = &DSI_SPI_RX_Que;
    
    pQueDataStatus = pQueue->QueDataStatus;
    LcwQueSize = pQueue->wQueSize;
    pTimeCnt = pQueue->QueueTimeOutCnt;
    
    /** @brief 添加原子锁 */
    if(pQueue->WriteIndexCnt >= LcwQueSize)
    {
        pQueue->WriteIndexCnt = 0;
    }

    if(pQueDataStatus[pQueue->WriteIndexCnt] == QueueData_IDLE)
    {
        pQueDataStatus[pQueue->WriteIndexCnt] = QueueData_WAIT;
        FlgCnt = pQueue->WriteIndexCnt;
        pTimeCnt[FlgCnt] = 0;
        if(pQueue->WriteIndexCnt < LcwQueSize)
        {
            pQueue->WriteIndexCnt ++;
        }
        else
        {
            pQueue->WriteIndexCnt = 0;
        }
        pRet = &pQueue->QueueDataBuff[FlgCnt];
    }
    else
    {

    }
    
    *pOutBufNum = FlgCnt;

    return pRet;
}

/******************************************************************************/
/**
 * @brief            获取DSI SPI 接收数据
 * @param[in/out]     pOutData   数据保存地址
 *
 * @return           0xFFFF : 无有效数据
 *                   else   OK，数据BufNumID
 */
/*****************************************************************************/
uint16 GetDSI_SPI_RxReadyData(DSI_SPI_RX_Data_str *pOutData)
{
    DSI_SPI_RX_Data_str *pQueueDataBuff;
    QueueDataStatus_en *pQueDataStatus;
    uint16 *pTimeCnt;
    uint16 LcwQueSize = 0;
    uint16 FlgCnt = 0xFFFF;
    DSI_SPI_RX_Que_Str *pQueue;

    if(pOutData == NULL)
    {
        return 0xFFFF;
    }
    pQueue = &DSI_SPI_RX_Que;
    pQueDataStatus = pQueue->QueDataStatus;
    pQueueDataBuff = pQueue->QueueDataBuff;
    LcwQueSize = pQueue->wQueSize;
    pTimeCnt = pQueue->QueueTimeOutCnt;

    if(pQueue->ReadIndexCnt >= LcwQueSize)
    {
        pQueue->ReadIndexCnt = 0;
    }
    
    if(pQueDataStatus[pQueue->ReadIndexCnt] == QueueData_READY)
    {
        FlgCnt = pQueue->ReadIndexCnt;
        pQueue->ReadIndexCnt ++;
    }

    if(FlgCnt != 0xFFFF)
    {
        memcpy(pOutData,&pQueueDataBuff[FlgCnt],sizeof(DSI_SPI_RX_Data_str));

        pQueDataStatus[FlgCnt] = QueueData_IDLE;
        pTimeCnt[FlgCnt] = 0;

        return FlgCnt;
    }
    else
    {
        return 0xFFFF;
    }
}

uint16 GetDSI_SPI_RxReadyCnt()
{
    uint16 LwCnt = 0;
    uint16 ReadyCnt = 0;
    QueueDataStatus_en *pQueDataStatus;
    
    pQueDataStatus = DSI_SPI_RX_Que.QueDataStatus;

    for(LwCnt = 0;LwCnt < DSISPI_RXBuf_NUM;LwCnt ++)
    {
        if(pQueDataStatus[LwCnt] == QueueData_READY)
        {
            ReadyCnt ++;
        }
    }

    return ReadyCnt;
}


uint8 DSI_SPI_RXQueue1msHook(DSI_SPI_RX_Que_Str *pQueue)
{
    uint16 LwCnt = 0;
    QueueDataStatus_en *pQueDataStatus;
    uint16 *pTimeCnt;
    uint16 LcwQueSize = 0;
    if(pQueue == NULL)
    {
        return 0xFF;
    }

    pQueDataStatus = pQueue->QueDataStatus;
    LcwQueSize = pQueue->wQueSize;
    pTimeCnt = pQueue->QueueTimeOutCnt;

    for(LwCnt = 0;LwCnt < LcwQueSize;LwCnt ++)
    {
        if((pQueDataStatus[LwCnt] == QueueData_READY )||(pQueDataStatus[LwCnt] == QueueData_WAIT) )
        {
            if(pTimeCnt[LwCnt] < DSI_SPI_RX_Que_TimeOut)
            {
                pTimeCnt[LwCnt]++;
            }
            else
            {
                /*超时处理*/
            }
        }
    }
    return 0;
}

