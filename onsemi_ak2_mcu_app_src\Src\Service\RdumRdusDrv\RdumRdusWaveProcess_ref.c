/******************************************************************************
 * @Copyright
 *****************************************************************************/

/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "types.h"
#include "BaseDrv.h"
#include "SpiCmd.h"
#include "RdumRdusBrc.h"
#include "RdumRdusCrm.h"
#include "RdumRdusWaveProcess.h"
#include "debug.h"

/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/
#define WAVE_PROCESS_STEP_COUNT      27      /* 发波流程总步骤数 */
#define WAVE_PROCESS_BRC_COUNT       13      /* 最大获取回波包络的次数 */
#define WAVE_PROCESS_SLOT_COUNT      2       /* 每次获取的回波槽数量 */

/******************************************************************************
 * @Type Definitions
 *****************************************************************************/
/* 发波流程步骤枚举 */
typedef enum
{
    WAVE_STEP_INIT = 0, /* 初始化 */
    WAVE_STEP_CONFIG_MODE, /* 配置发波模式 */
    WAVE_STEP_CONFIG_PARAM, /* 配置发波参数 */
    WAVE_STEP_READ_STATUS, /* 读取状态 */
    WAVE_STEP_GET_PARAM, /* 获取发波参数 */
    WAVE_STEP_CONFIG_PARAM1, /* 配置发波参数 */
    WAVE_STEP_CONFIG_PARAM2, /* 配置发波参数 */
    WAVE_STEP_GET_BRC, /* 获取回波包络 */

    WAVE_STEP_COMPLETE = 0xFF, /* 完成 */
} WaveProcessStep_t;

/* 发波流程状态枚举 */
typedef enum
{
    WAVE_PROCESS_IDLE = 0,             /* 空闲 */
    WAVE_PROCESS_BUSY,                 /* 忙 */
    WAVE_PROCESS_COMPLETE,             /* 完成 */
    WAVE_PROCESS_ERROR,                /* 错误 */
} WaveProcessStatus_t;

/* 发波流程控制结构体 */
typedef struct
{
    WaveProcessStep_t CurrentStep;     /* 当前步骤 */
    WaveProcessStatus_t Status;        /* 状态 */
    uint8 RetryCount;                  /* 重试次数 */
    uint32 StartTime;                  /* 开始时间 */
    uint32 StepStartTime;              /* 步骤开始时间 */
    BrcResult_t BrcResults[WAVE_PROCESS_BRC_COUNT]; /* 回波包络结果 */
    uint16 DecompressedEnvelopes[MAX_SLOT_COUNT][WAVE_PROCESS_BRC_COUNT * ENVELOPE_GROUPS][ENVELOPE_POINTS_PER_GROUP]; /* 存储所有解压缩后的包络点位 */
    uint8 BrcIndex;                    /* 当前回波包络索引 */
    uint8 BrcCount;                    /* 总共需要接收的回波包络数量 */
    uint8 RegValues[16];               /* 寄存器值缓存 */
} WaveProcessControl_t;

/******************************************************************************
 * @Global Variables
 *****************************************************************************/
static WaveProcessControl_t WaveProcessCtrl;

/******************************************************************************
 * @Function Declaration
 *****************************************************************************/
static SpiTransReturn_t WaveProcess_ExecuteStep(WaveProcessStep_t Step);
static void WaveProcess_NextStep(void);

/******************************************************************************
 * @Function Implementation
 *****************************************************************************/

/******************************************************************************
 * @brief      初始化发波流程
 * <AUTHOR>
 * @date       2025-04-04 10:00:00
 * @note       初始化发波流程控制结构体
 *****************************************************************************/
void RdumRdusWaveProcess_Init(void)
{
    /* 初始化发波流程控制结构体 */
    memset(&WaveProcessCtrl, 0, sizeof(WaveProcessControl_t));
    WaveProcessCtrl.CurrentStep = WAVE_STEP_INIT;
    WaveProcessCtrl.Status = WAVE_PROCESS_IDLE;
}

/******************************************************************************
 * @brief      启动发波流程
 * @return     启动结果
 * <AUTHOR>
 * @date       2025-04-04 10:05:00
 * @note       启动发波流程
 *****************************************************************************/
uint8 RdumRdusWaveProcess_Start(void)
{
    /* 检查当前状态 */
    if (WaveProcessCtrl.Status != WAVE_PROCESS_IDLE)
    {
        return 0; /* 当前不是空闲状态，无法启动 */
    }

    /* 初始化控制结构体 */
    memset(&WaveProcessCtrl, 0, sizeof(WaveProcessControl_t));
    WaveProcessCtrl.CurrentStep = WAVE_STEP_CONFIG_REG_1;
    WaveProcessCtrl.Status = WAVE_PROCESS_BUSY;
    WaveProcessCtrl.StartTime = BaseDrv_GetSys1MsTick();
    WaveProcessCtrl.StepStartTime = WaveProcessCtrl.StartTime;

    /* 执行第一步 */
    WaveProcess_ExecuteStep(WaveProcessCtrl.CurrentStep);

    return 1; /* 启动成功 */
}

/******************************************************************************
 * @brief      命令响应数据处理回调函数
 * @param[in]  SpiComData 通信相关数据
 * <AUTHOR>
 * @date       2025-05-20
 * @note       处理读命令响应数据
 *****************************************************************************/
static SpiCallbackReturn_t ReadCommandCallback(SpiCom_t SpiComData)
{
    SpiReadResp_t ReadResp;
    SpiDataBuffer_t *DataBuffer = SpiComData.DataBuffer;
    uint8 *RxData = DataBuffer->RxBuff;
    uint8 DataLens = DataBuffer->DataLens;
    SpiProtocol_t *SendCmdProtocol = (SpiProtocol_t *)DataBuffer->TxBuff;
    uint8 Addr = SendCmdProtocol->ReadAndWrite_t.Addr;
    uint8 CalcCrc;

    if (DataLens < SPI_CMD_LENS)
    {
        PRINTF_RDUM("SpiCom_DataAnalysis: Invalid data length %d\r\n", DataLens);
        return SPI_CALLBACK_CONTINUE;
    }

    /* 计算CRC */
    CalcCrc = BaseDrv_Crc8Calculate(CrcData, SPI_CMD_CRC_LENS, CRC8_C2_INIT_SPI_AND_CRC_SD);

    if (CalcCrc != RxData[SPI_CMD_CRC_LENS])
    {
        PRINTF_RDUM("SpiCom_DataAnalysis: ReadCommandCallback CRC error, CalcCrc=%02X, Crc=%02X\r\n", CalcCrc, Crc);
        return SPI_CALLBACK_CONTINUE;
    }

    /* 复制原始响应数据 */
    memcpy(ReadResp->RespData, RxData, SPI_CMD_LENS);

    // if (ReadResp.Bit.CRM_RCVD)
    // {
    //     PRINTF_RDUM("SpiCom_DataAnalysis: ReadCommandCallback CRM_RCVD\r\n");
    //     return SPI_CALLBACK_CONTINUE;
    // }

    // if (ReadResp.Bit.TIMER_INT)
    // {
    //     PRINTF_RDUM("SpiCom_DataAnalysis: ReadCommandCallback TIMER_INT\r\n");
    //     return SPI_CALLBACK_CONTINUE;
    // }

    // if (ReadResp.Bit.SPIERR)
    // {
    //     PRINTF_RDUM("SpiCom_DataAnalysis: ReadCommandCallback SPIERR\r\n");
    //     return SPI_CALLBACK_CONTINUE;
    // }

    if (ReadResp.Bit.HW_ERR)
    {
        PRINTF_RDUM("SpiCom_DataAnalysis: ReadCommandCallback HW_ERR\r\n");
        return SPI_CALLBACK_CONTINUE;
    }

    /*根据地址识别返回的数据RxData定义*/
    switch (Addr)
    {
    case SPI_CMD_ADDR_STATUS:
        if (!ReadResp.Bit.Data.Addr0x12.Bits.BUS_AVAIL)
        {
                PRINTF_RDUM("SpiCom_DataAnalysis: ReadCommandCallback BUS_AVAIL\r\n");
                return SPI_CALLBACK_CONTINUE;
        }
        break;

    case default:
        break;
    }


    return SPI_CALLBACK_OK;
}

/******************************************************************************
 * @brief      非读命令响应数据处理回调函数
 * @param[in]  SpiComData
 * <AUTHOR> @date       2025-05-20
 * @note       处理非读命令响应数据
 *****************************************************************************/
static SpiCallbackReturn_t NonReadCommandCallback(SpiCom_t SpiComData)
{
    SpiNonReadResp_t NonReadResp;
    SpiDataBuffer_t *DataBuffer = SpiComData.DataBuffer;
    uint8 *RxData = DataBuffer->RxBuff;
    uint8 DataLens = DataBuffer->DataLens;
    uint8 CalcCrc;

    if (DataLens < SPI_CMD_LENS)
    {
        PRINTF_RDUM("SpiCom_DataAnalysis: Invalid data length %d\r\n", DataLens);
        return SPI_CALLBACK_CONTINUE;
    }

    /* 计算CRC */
    CalcCrc = BaseDrv_Crc8Calculate(RxData, SPI_CMD_CRC_LENS, CRC8_C2_INIT_SPI_AND_CRC_SD);

    if (CalcCrc != RxData[SPI_CMD_CRC_LENS])
    {
        PRINTF_RDUM("SpiCom_DataAnalysis: NonReadCommandCallback CRC error, CalcCrc=%02X, Crc=%02X\r\n", CalcCrc, Crc);
        return SPI_CALLBACK_CONTINUE;
    }

    /* 复制原始响应数据 */
    memcpy(NonReadResp->RespData, RxData, SPI_CMD_LENS);

    // if (NonReadResp.Bit.SPIERR)
    // {
    //     PRINTF_RDUM("SpiCom_DataAnalysis: NonReadCommandCallback SPIERR\r\n");
    //     return SPI_CALLBACK_CONTINUE;
    // }

    if (NonReadResp.Bit.HW_ERR)
    {
        PRINTF_RDUM("SpiCom_DataAnalysis: NonReadCommandCallback HW_ERR\r\n");
        return SPI_CALLBACK_CONTINUE;
    }

    return SPI_CALLBACK_OK;
}

/******************************************************************************
 * @brief      处理发波流程
 * <AUTHOR>
 * @date       2025-04-04 10:10:00
 * @note       周期性调用此函数处理发波流程
 *****************************************************************************/
void RdumRdusWaveProcess_Process(void)
{
    RdumRdusCrm_MeasureFrame_t Frame;
    SpiPayloadData_t Payload;
    uint8 i, DelayMs;

    /* 检查当前状态 */
    if (WaveProcessCtrl.Status != WAVE_PROCESS_BUSY)
    {
        return; /* 当前不是忙状态，无需处理 */
    }

    WaveProcess_ExecuteStep(WaveProcessCtrl.CurrentStep);

    switch (WaveProcessCtrl.CurrentStep)
    {
    case WAVE_STEP_CONFIG_MODE:
        Frame.Bit.Sid = Dsi3Id;
        Frame.Bit.Mode = Mode & 0x07;
        // 0为主发，1为左侦听，2为右侦听，3为不动作(最多只支持同时8个通道主发和侦听数据)
        Frame.Bit.SID1_BEH_CH_H = 0x01;
        Frame.Bit.SID1_BEH_CH_L = 0x03;
        Frame.Bit.SID2_BEH_CH_H = 0x02;
        Frame.Bit.SID2_BEH_CH_L = 0x01;
        Frame.Bit.SID3_BEH_CH_H = 0x00;
        Frame.Bit.SID3_BEH_CH_L = 0x02;
        Frame.Bit.SID4_BEH_CH_H = 0x03;
        Frame.Bit.SID4_BEH_CH_L = 0x00;
        Frame.Bit.SID5_BEH_CH_H = 0x01;
        Frame.Bit.SID5_BEH_CH_L = 0x03;
        Frame.Bit.SID6_BEH_CH_H = 0x02;
        Frame.Bit.SID6_BEH_CH_L = 0x03;
        if (RdumRdusCrm_StartMeasurement(DSI3_CHANNEL_A, &Frame))
        {
            WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
            break;
        }
        else
        {
            WaveProcessCtrl.CurrentStep = WAVE_STEP_CONFIG_PARAM;
        }
        break;

    case WAVE_STEP_CONFIG_PARAM:
        /* 配置寄存器9: WRITE_BYADDR 02 09 00 00 00 00 6F */
        memset(&Payload, 0, sizeof(SpiPayloadData_t));
        Payload.Addr0x09.Bits.N_FRAMES_MSB = 0x00;
        if (SpiCmd_WriteByAddress(DSI3_CHANNEL_A, 0x09, (uint8 *)Payload.Bytes))
        {
            WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
            break;
        }

        /* 配置寄存器2: WRITE_BYADDR 02 02 37 F1 00 1A C7 */
        memset(&Payload, 0, sizeof(SpiPayloadData_t));
        Payload.Addr0x02.Bits.BURST_ENA = 0x01;
        Payload.Addr0x02.Bits.BUS_DRV_ENA = 0x01;
        Payload.Addr0x02.Bits.BRC_INT_MASK = 0x7F;
        Payload.Addr0x02.Bits.PDCM_NUM_SLOTS = 0x01;
        Payload.Addr0x02.Bits.N_FRAMES = 0x0A;
        if (SpiCmd_WriteByAddress(DSI3_CHANNEL_A, SPI_CMD_ADDR_R2, (uint8 *)Payload.Bytes))
        {
            WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
            break;
        }
        else
        {
            WaveProcessCtrl.CurrentStep = WAVE_STEP_READ_STATUS;
        }
        break;

    case WAVE_STEP_READ_STATUS:
        /* 读取状态寄存器: READ_BYADDR 03 12 00 00 00 00 30 */
        if (SpiCmd_ReadByAddress(DSI3_CHANNEL_A, SPI_CMD_ADDR_STATUS))
        {
            WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
            break;
        }

        /* 获取状态寄存器值: NO_OPERATION 01 12 00 00 00 00 98 */
        if (SpiCmd_NoOperation(DSI3_CHANNEL_A, SPI_CMD_ADDR_STATUS, WaveProcessCtrl.RegValues))
        {
            WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
            break;
        }
        else
        {
            WaveProcessCtrl.CurrentStep = WAVE_STEP_GET_PARAM;
        }
        break;
    case WAVE_STEP_GET_PARAM:
        /* 读取寄存器09: READ_BYADDR 03 09 00 00 00 00 CA */
        if (SpiCmd_ReadByAddress(DSI3_CHANNEL_A, SPI_CMD_ADDR_AOD))
        {
            WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
            break;
        }

        /* 获取寄存器09值: NO_OPERATION 01 09 00 00 00 00 60 */
        if (SpiCmd_NoOperation(DSI3_CHANNEL_A, SPI_CMD_ADDR_AOD, WaveProcessCtrl.RegValues))
        {
            WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
            break;
        }

        /* 读取寄存器02: READ_BYADDR 03 02 00 00 00 00 E9 */
        if (SpiCmd_ReadByAddress(DSI3_CHANNEL_A, SPI_CMD_ADDR_R2))
        {
            WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
            break;
        }

        /* 获取寄存器02值: NO_OPERATION 01 02 00 00 00 00 60 */
        if (SpiCmd_NoOperation(DSI3_CHANNEL_A, SPI_CMD_ADDR_R2, WaveProcessCtrl.RegValues))
        {
            WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
            break;
        }
        else
        {
            WaveProcessCtrl.CurrentStep = WAVE_STEP_CONFIG_PARAM1;
        }
        break;

    case WAVE_STEP_CONFIG_PARAM1:
        /* 配置寄存器9: WRITE_BYADDR 02 09 00 00 00 00 6F */
        memset(&Payload, 0, sizeof(SpiPayloadData_t));
        Payload.Addr0x09.Bits.N_FRAMES_MSB = 0x00;
        if (SpiCmd_WriteByAddress(DSI3_CHANNEL_A, SPI_CMD_ADDR_AOD, (uint8 *)Payload.Bytes))
        {
            WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
            break;
        }

        /* 配置寄存器02: WRITE_BYADDR 02 02 B7 F1 00 1A 06 */
        memset(&Payload, 0, sizeof(SpiPayloadData_t));
        Payload.Addr0x02.Bits.TX_START = 0x01;
        Payload.Addr0x02.Bits.BURST_ENA = 0x01;
        Payload.Addr0x02.Bits.BUS_DRV_ENA = 0x01;
        Payload.Addr0x02.Bits.BRC_INT_MASK = 0x7F;
        Payload.Addr0x02.Bits.PDCM_NUM_SLOTS = 0x01;
        Payload.Addr0x02.Bits.N_FRAMES = 0x0A;
        if (SpiCmd_WriteByAddress(DSI3_CHANNEL_A, SPI_CMD_ADDR_R2, (uint8 *)Payload.Bytes))
        {
            WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
            break;
        }
        else
        {
            WaveProcessCtrl.CurrentStep = WAVE_STEP_CONFIG_PARAM2;
        }
        break;

    case WAVE_STEP_CONFIG_PARAM2:
        /* 配置寄存器9: WRITE_BYADDR 02 09 00 00 00 00 6F */
        memset(&Payload, 0, sizeof(SpiPayloadData_t));
        Payload.Addr0x09.Bits.N_FRAMES_MSB = 0x00;
        if (SpiCmd_WriteByAddress(DSI3_CHANNEL_A, SPI_CMD_ADDR_AOD, (uint8 *)Payload.Bytes))
        {
            WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
            break;
        }

        /* 配置寄存器02: WRITE_BYADDR 02 02 D7 F1 13 9C 2D */
        memset(&Payload, 0, sizeof(SpiPayloadData_t));
        Payload.Addr0x02.Bits.TX_START = 0x01;
        Payload.Addr0x02.Bits.BRC_ENA = 0x01;
        Payload.Addr0x02.Bits.BUS_DRV_ENA = 0x01;
        Payload.Addr0x02.Bits.BRC_INT_MASK = 0x7F;
        Payload.Addr0x02.Bits.PDCM_NUM_SLOTS = 0x01; // 单次GET_ALL_BRC需要接收几个slot插槽，PDCM_NUM_SLOTS + 1  表示实际接收的slot插槽数量
        Payload.Addr0x02.Bits.TIMER = 39;            // 当和TX_START=1和BRC_ENA=1一起时用于设置GET_ALL_BRC间隔读取的值，= 39/10 = 3.9ms
        Payload.Addr0x02.Bits.N_FRAMES = 0x0C;       // 根据发波的周期确定需要收几次slot包才可以获取所有的回波包络
        WaveProcessCtrl.BrcCount = Payload.Addr0x02.Bits.N_FRAMES;
        if (SpiCmd_WriteByAddress(DSI3_CHANNEL_A, SPI_CMD_ADDR_R2, (uint8 *)Payload.Bytes))
        {
            WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
            break;
        }
        else
        {
            WaveProcessCtrl.CurrentStep = WAVE_STEP_GET_BRC;
        }
        break;

    case WAVE_STEP_GET_BRC:
        /* 获取回波包络: GET_ALL_BRC */
        for (i = 0; i < Payload.Addr0x02.Bits.N_FRAMES; i++)
        {
            if (i == 0)
            {
                /* 根据发波模式不同，第一次延时时间不一样，为了通用适配通用使用6ms */
                DelayMs = 6;
            }
            else
            {
                DelayMs = Payload.Addr0x02.Bits.TIMER / 10 + 1;
            }
            if (SpiCmd_GetAllBrc(DSI3_CHANNEL_A, Payload.Addr0x02.Bits.PDCM_NUM_SLOTS + 1, DelayMs, NULL))
            {
                WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
                break;
            }
        }

        WaveProcessCtrl.CurrentStep = WAVE_STEP_COMPLETE;
        WaveProcessCtrl.Status = WAVE_PROCESS_COMPLETE;
        break;

    case default:
        WaveProcessCtrl.CurrentStep = WAVE_STEP_ERROR;
        WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
        break;
    }
}

/******************************************************************************
 * @brief      获取发波流程状态
 * @return     发波流程状态
 * <AUTHOR>
 * @date       2025-04-04 10:15:00
 * @note       获取当前发波流程状态
 *****************************************************************************/
WaveProcessStatus_t RdumRdusWaveProcess_GetStatus(void)
{
    return WaveProcessCtrl.Status;
}

/******************************************************************************
 * @brief      获取回波包络结果
 * @param[out] Results 回波包络结果数组
 * @return     回波包络结果数量
 * <AUTHOR>
 * @date       2025-04-04 10:20:00
 * @note       获取发波流程中采集的回波包络结果
 *****************************************************************************/
uint8 RdumRdusWaveProcess_GetBrcResults(BrcResult_t *Results)
{
    uint8 i;

    /* 检查参数 */
    if (Results == NULL)
    {
        return 0;
    }

    /* 检查当前状态 */
    if (WaveProcessCtrl.Status != WAVE_PROCESS_COMPLETE)
    {
        return 0; /* 当前不是完成状态，无法获取结果 */
    }

    /* 复制结果 */
    for (i = 0; i < WAVE_PROCESS_BRC_COUNT; i++)
    {
        memcpy(&Results[i], &WaveProcessCtrl.BrcResults[i], sizeof(BrcResult_t));
    }

    return WAVE_PROCESS_BRC_COUNT;
}

/******************************************************************************
 * @brief      SPI传输完成回调函数
 * @param[in]  TxSeqId 传输序号
 * @param[in]  Status 传输状态
 * @param[in]  UserData 用户数据
 * @return     回调函数返回值
 * <AUTHOR>
 * @date       2025-04-04 10:25:00
 * @note       处理SPI传输完成事件
 *****************************************************************************/
static SpiCallbackReturn_t WaveProcess_TransferCallback(SpiTxSeqId_t TxSeqId, SpiTxStatus_t Status, void* UserData)
{
    /* 检查传输状态 */
    if (Status == SPI_TX_READY)
    {
        /* 传输成功，进入下一步 */
        WaveProcess_NextStep();
        return SPI_CALLBACK_OK;
    }
    else if (Status == SPI_TX_ERROR || Status == SPI_TX_TIMEOUT)
    {
        /* 传输失败，重试或报错 */
        if (WaveProcessCtrl.RetryCount < 3)
        {
            /* 重试当前步骤 */
            WaveProcessCtrl.RetryCount++;
            WaveProcessCtrl.StepStartTime = BaseDrv_GetSys1MsTick();
            WaveProcess_ExecuteStep(WaveProcessCtrl.CurrentStep);
            return SPI_CALLBACK_CONTINUE;
        }
        else
        {
            /* 超过最大重试次数，报错 */
            WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
            return SPI_CALLBACK_ABORT;
        }
    }

    return SPI_CALLBACK_OK;
}

/******************************************************************************
 * @brief      执行发波流程步骤
 * @param[in]  Step 步骤
 * @return     执行结果
 * <AUTHOR>
 * @date       2025-04-04 10:30:00
 * @note       执行指定的发波流程步骤
 *****************************************************************************/
static SpiTransReturn_t WaveProcess_ExecuteStep(WaveProcessStep_t Step)
{
    SpiTransReturn_t Result = SPI_TRANS_FAIL;
    SpiProtocol_t SpiTxData;
    SpiTxSeqId_t TxSeqIndex;
    uint8 TxData[7];
    uint8 SlotIndex;

    /* 更新步骤开始时间 */
    WaveProcessCtrl.StepStartTime = BaseDrv_GetSys1MsTick();

    /* 根据步骤执行相应操作 */
    switch (Step)
    {
        case WAVE_STEP_CONFIG_MODE:
            Frame.Bit.Sid = Dsi3Id;
            Frame.Bit.Mode = Mode & 0x07;
            // 0为主发，1为左侦听，2为右侦听，3为不动作(最多只支持同时8个通道主发和侦听数据)
            Frame.Bit.SID1_BEH_CH_H = 0x01;
            Frame.Bit.SID1_BEH_CH_L = 0x03;
            Frame.Bit.SID2_BEH_CH_H = 0x02;
            Frame.Bit.SID2_BEH_CH_L = 0x01;
            Frame.Bit.SID3_BEH_CH_H = 0x00;
            Frame.Bit.SID3_BEH_CH_L = 0x02;
            Frame.Bit.SID4_BEH_CH_H = 0x03;
            Frame.Bit.SID4_BEH_CH_L = 0x00;
            Frame.Bit.SID5_BEH_CH_H = 0x01;
            Frame.Bit.SID5_BEH_CH_L = 0x03;
            Frame.Bit.SID6_BEH_CH_H = 0x02;
            Frame.Bit.SID6_BEH_CH_L = 0x03;
            if (RdumRdusCrm_StartMeasurement(DSI3_CHANNEL_A, &Frame))
            {
                return SPI_TRANS_FAIL;
            }
            break;

        case WAVE_STEP_CONFIG_PARAM:
            /* 配置寄存器9: WRITE_BYADDR 02 09 00 00 00 00 6F */
            memset(&Payload, 0, sizeof(SpiPayloadData_t));
            Payload.Addr0x09.Bits.N_FRAMES_MSB = 0x00;
            SpiCmd_WriteByAddress(DSI3_CHANNEL_A, 0x09, (uint8 *)Payload.Bytes);
            break;

        case WAVE_STEP_READ_STATUS:
            /* 读取状态寄存器: READ_BYADDR 03 12 00 00 00 00 30 */
            SpiCmd_ReadByAddress(DSI3_CHANNEL_A, SPI_CMD_ADDR_STATUS);
            /* 获取状态寄存器值: NO_OPERATION 01 12 00 00 00 00 98 */
            SpiCmd_NoOperation(DSI3_CHANNEL_A, SPI_CMD_ADDR_STATUS, WaveProcessCtrl.RegValues);
            break;

        case WAVE_STEP_GET_PARAM:
            /* 读取寄存器09: READ_BYADDR 03 09 00 00 00 00 CA */
            SpiCmd_ReadByAddress(DSI3_CHANNEL_A, SPI_CMD_ADDR_AOD);
            /* 获取寄存器09值: NO_OPERATION 01 09 00 00 00 00 60 */
            SpiCmd_NoOperation(DSI3_CHANNEL_A, SPI_CMD_ADDR_AOD, WaveProcessCtrl.RegValues);
            /* 读取寄存器02: READ_BYADDR 03 02 00 00 00 00 E9 */
            SpiCmd_ReadByAddress(DSI3_CHANNEL_A, SPI_CMD_ADDR_R2);
            /* 获取寄存器02值: NO_OPERATION 01 02 00 00 00 00 60 */
            SpiCmd_NoOperation(DSI3_CHANNEL_A, SPI_CMD_ADDR_R2, WaveProcessCtrl.RegValues);
            break;

        case WAVE_STEP_CONFIG_PARAM1:

            break;

        case WAVE_STEP_CONFIG_PARAM2:

            break;

        case WAVE_STEP_GET_BRC:

            break;

        default:

            break;
    }

    return Result;
}

/******************************************************************************
 * @brief      进入下一步
 * <AUTHOR>
 * @date       2025-04-04 10:35:00
 * @note       完成当前步骤，进入下一步
 *****************************************************************************/
static void WaveProcess_NextStep(void)
{
    /* 重置重试计数 */
    WaveProcessCtrl.RetryCount = 0;

    /* 根据当前步骤确定下一步 */
    switch (WaveProcessCtrl.CurrentStep)
    {
        case WAVE_STEP_CONFIG_REG_1:
            WaveProcessCtrl.CurrentStep = WAVE_STEP_CONFIG_REG_2;
            break;

        case WAVE_STEP_CONFIG_REG_2:
            WaveProcessCtrl.CurrentStep = WAVE_STEP_CONFIG_REG_3;
            break;

        case WAVE_STEP_CONFIG_REG_3:
            WaveProcessCtrl.CurrentStep = WAVE_STEP_CONFIG_REG_4;
            break;

        case WAVE_STEP_CONFIG_REG_4:
            WaveProcessCtrl.CurrentStep = WAVE_STEP_CONFIG_REG_5;
            break;

        case WAVE_STEP_CONFIG_REG_5:
            WaveProcessCtrl.CurrentStep = WAVE_STEP_READ_STATUS_REG;
            break;

        case WAVE_STEP_READ_STATUS_REG:
            WaveProcessCtrl.CurrentStep = WAVE_STEP_GET_STATUS_REG;
            break;

        case WAVE_STEP_GET_STATUS_REG:
            WaveProcessCtrl.CurrentStep = WAVE_STEP_READ_REG_09;
            break;

        case WAVE_STEP_READ_REG_09:
            WaveProcessCtrl.CurrentStep = WAVE_STEP_GET_REG_09;
            break;

        case WAVE_STEP_GET_REG_09:
            WaveProcessCtrl.CurrentStep = WAVE_STEP_READ_REG_02;
            break;

        case WAVE_STEP_READ_REG_02:
            WaveProcessCtrl.CurrentStep = WAVE_STEP_GET_REG_02;
            break;

        case WAVE_STEP_GET_REG_02:
            WaveProcessCtrl.CurrentStep = WAVE_STEP_CONFIG_REG_09_1;
            break;

        case WAVE_STEP_CONFIG_REG_09_1:
            WaveProcessCtrl.CurrentStep = WAVE_STEP_CONFIG_REG_02_1;
            break;

        case WAVE_STEP_CONFIG_REG_02_1:
            WaveProcessCtrl.CurrentStep = WAVE_STEP_CONFIG_REG_09_2;
            break;

        case WAVE_STEP_CONFIG_REG_09_2:
            WaveProcessCtrl.CurrentStep = WAVE_STEP_CONFIG_REG_02_2;
            break;

        case WAVE_STEP_CONFIG_REG_02_2:
            /* 开始获取回波包络 */
            WaveProcessCtrl.CurrentStep = WAVE_STEP_GET_BRC_START;
            break;

        default:
            /* 处理获取回波包络步骤 */
            if (WaveProcessCtrl.CurrentStep >= WAVE_STEP_GET_BRC_START &&
                WaveProcessCtrl.CurrentStep < WAVE_STEP_GET_BRC_START + WAVE_PROCESS_BRC_COUNT - 1)
            {
                /* 进入下一个回波包络获取步骤 */
                WaveProcessCtrl.CurrentStep++;
            }
            else if (WaveProcessCtrl.CurrentStep == WAVE_STEP_GET_BRC_START + WAVE_PROCESS_BRC_COUNT - 1)
            {
                /* 所有步骤完成 */
                WaveProcessCtrl.CurrentStep = WAVE_STEP_COMPLETE;
                WaveProcessCtrl.Status = WAVE_PROCESS_COMPLETE;
            }
            break;
    }

    /* 如果不是完成状态，执行下一步 */
    if (WaveProcessCtrl.CurrentStep != WAVE_STEP_COMPLETE)
    {
        WaveProcess_ExecuteStep(WaveProcessCtrl.CurrentStep);
    }
}