"""
测试地址定义模块
"""

import json
from command_parser.address_definitions import get_register

def test_register_parse_data():
    """测试寄存器解析数据"""
    print("\n=== 测试寄存器解析数据 ===")
    
    # 获取寄存器17（REG17）
    reg17 = get_register(17)
    
    # 测试用例1：全0数据
    data1 = 0x00000000
    
    # 解析数据
    result1 = reg17.parse_data(data1)
    
    print(f"寄存器: {reg17.name} (地址: 0x{reg17.address:02X})")
    print(f"数据: 0x{data1:08X}")
    print("解析结果:")
    print(json.dumps(result1, indent=2, ensure_ascii=False))
    
    # 测试用例2：非全0数据
    data2 = 0x12345678
    
    # 解析数据
    result2 = reg17.parse_data(data2)
    
    print(f"\n数据: 0x{data2:08X}")
    print("解析结果:")
    print(json.dumps(result2, indent=2, ensure_ascii=False))
    
    # 获取寄存器2（REG2）
    reg2 = get_register(2)
    
    # 测试用例3：非全0数据
    data3 = 0x12345678
    
    # 解析数据
    result3 = reg2.parse_data(data3)
    
    print(f"\n寄存器: {reg2.name} (地址: 0x{reg2.address:02X})")
    print(f"数据: 0x{data3:08X}")
    print("解析结果:")
    print(json.dumps(result3, indent=2, ensure_ascii=False))

def test_register_with_descriptions():
    """测试带有描述的寄存器"""
    print("\n=== 测试带有描述的寄存器 ===")
    
    # 创建一个带有描述的寄存器
    from command_parser.address_definitions import Register, BitField
    
    reg_test = Register(0xFF, "TEST_REG", "测试寄存器")
    reg_test.add_bit_field("TEST_FIELD1", 31, 24, "测试字段1，高8位")
    reg_test.add_bit_field("TEST_FIELD2", 23, 16, "测试字段2，中8位")
    reg_test.add_bit_field("TEST_FIELD3", 15, 8, "测试字段3，低8位")
    reg_test.add_bit_field("TEST_BIT", 0, 0, "测试位，最低位")
    
    # 测试数据
    data = 0x12345678
    
    # 解析数据
    result = reg_test.parse_data(data)
    
    print(f"寄存器: {reg_test.name} (地址: 0x{reg_test.address:02X})")
    print(f"数据: 0x{data:08X}")
    print("解析结果:")
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    # 打印位域信息
    print("\n位域信息:")
    for field_name, field_info in result["_bit_fields_info"].items():
        print(f"{field_name}: {field_info['description']} (位 {field_info['msb']}-{field_info['lsb']})")
    
    # 打印字段值和描述
    print("\n字段值和描述:")
    for field_name, field_value in result.items():
        if field_name.startswith("_"):
            continue
        if isinstance(field_value, dict):
            print(f"{field_name}: 值={field_value['value']} ({field_value['hex']}), 描述={field_value['description']}")

if __name__ == "__main__":
    test_register_parse_data()
    test_register_with_descriptions()
