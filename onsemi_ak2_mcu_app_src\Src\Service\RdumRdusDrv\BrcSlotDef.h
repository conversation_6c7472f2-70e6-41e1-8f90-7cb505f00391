/******************************************************************************
 * @file      BrcSlotDef.h
 * @brief     回波slot数据定义
 * <AUTHOR>
 * @date      2025-05-16
 * @note
 *****************************************************************************/

#ifndef __BRC_SLOT_DEF_H__
#define __BRC_SLOT_DEF_H__
/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "types.h"



/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/
#define MAX_SLOT_COUNT 8                                                 /* 单次SPI命令最多可获取的slot数量（通信速率限制） */
#define SLOT_DATA_SIZE 20                                                /* 完整slot包大小(含2字节slot duration(PDCM传输时隙的持续时间记录)) */
#define SLOT_PACKAGE_SIZE 18                                             /* slot包数据大小(含1字节KAC+SS和1字节CRC8) */
#define ENVELOPE_DATA_SIZE 16                                            /* 包络数据大小(byte) */
#define ENVELOPE_GROUPS 4                                                /* 一个slot的包络组数 */
#define ENVELOPE_POINTS_PER_GROUP 9                                      /* 每组包络点数 */
#define TOTAL_ENVELOPE_POINTS (ENVELOPE_GROUPS*ENVELOPE_POINTS_PER_GROUP)/* 一个slot解压缩后包络点数 */
#define POINT_INTERVAL_US 102.4                                          /* 点位间隔时间（微秒） */

/******************************************************************************
 * @Type Definitions
 *****************************************************************************/
/* slot状态 */
typedef enum
{
    SLOT_STATUS_OK = 0,             /* 状态正常 */
    SLOT_STATUS_ERROR,              /* 状态错误 */
    SLOT_STATUS_TIMEOUT,            /* 状态超时 */
    SLOT_STATUS_INVALID             /* 状态无效 */
} SlotStatus_t;

/* slot数据 */
typedef struct
{
    uint8 Kac : 4;                         /* Keep-Alive Counter */
    uint8 Status : 4;                      /* 状态 */
    uint8 SlotData[ENVELOPE_DATA_SIZE];    /* 包数据 */
    uint8 SlotCrc;                         /* slot CRC */
    uint8 SlotDuration[2];                 /* slot持续时间 */
} SlotData_t;

/* 包络点位 */
typedef struct
{
    uint8 Scale: 5;                 /* 比例 */
    uint8 Points[ENVELOPE_POINTS_PER_GROUP]; /* 点位 */
} EnvelopeGroup_t;

/* 解压缩后的包络点位 */
typedef struct
{
    uint16 Points[TOTAL_ENVELOPE_POINTS]; /* 解压缩后的点位 */
} DecompressedEnvelope_t;

/* BRC命令结果 */
typedef struct
{
    uint8 SlotCount;                     /* slot数量 */
    SlotData_t SlotData[MAX_SLOT_COUNT]; /* slot数据 */
    uint8 SlotSDCrc;                     /* 包的CRC8校验值 */
    SlotStatus_t Status;                 /* 状态 */
} BrcResult_t;

/******************************************************************************
 * @Const Declaration
 *****************************************************************************/
/* 异或表 */
extern const uint8 XorMasks[MAX_SLOT_COUNT][SLOT_PACKAGE_SIZE];

/* 分频器查找表 */
extern const uint16 DividerLut[4];



/******************************************************************************
 * @Variable Declaration
 *****************************************************************************/



/******************************************************************************
 * @Function Declaration
 *****************************************************************************/
/* 解码slot数据 */
uint8 BrcSlotDef_DecodeSlot(uint8 SlotIndex, uint8 *SlotData, uint8 *DecodedData);

/* 解压缩包络点位 */
uint8 BrcSlotDef_DecompressEnvelope(uint8 *EnvelopeData, DecompressedEnvelope_t *DecompressedEnvelope);

/* 验证slot CRC */
uint8 BrcSlotDef_VerifySlotCrc(uint8 *SlotData, uint8 SlotIndex);

/* 解压缩单组包络点位 */
void BrcSlotDef_DecompressSingleEnvelope(uint32 GroupData, EnvelopeGroup_t *Group);

#endif /* __BRC_SLOT_DEF_H__ */
