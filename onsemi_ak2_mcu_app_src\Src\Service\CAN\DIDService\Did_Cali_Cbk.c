#include "Did_Cali_Cbk.h"
#include "ODO_CalibPara_Types.h"


#if (CALIBRATION_EN == STD_ON)

uint8_t Did_ReadData(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    const DataIdentifier_Cail* psDid = DID_CFG_HANDLE;
    uint8_t Nrc = 0;

    if (psDid->pfReadData != NULL)
    {
        Nrc = psDid->pfReadData(u16Handle, u16Did, u16Size, pu8Dst);
    }
    return (Nrc);
}

uint8_t Did_WriteData(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    const DataIdentifier_Cail* psDid = DID_CFG_HANDLE;
    uint8_t Nrc = 0;

    if (psDid->pfWriteData != NULL)
    {
        Nrc = psDid->pfWriteData(u16Handle, u16Did, u16Size, pu8Src);
    }
    return (Nrc);
}




uint8_t Did_ReadData_FDC0(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read Sns Gain*/


    return (Nrc);
}

uint8_t Did_WriteData_FDC0(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;

    /*write Sns Gain*/


    return (Nrc);
}

uint8_t Did_ReadData_FD01(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FLS_FRS_StandardThreshold*/


    return (Nrc);
}

uint8_t Did_ReadData_FD02(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FLS_FRS_ChirpThreshold*/


    return (Nrc);
}

uint8_t Did_ReadData_FD03(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FL_FR_StandardThreshold*/


    return (Nrc);
}

uint8_t Did_ReadData_FD04(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FL_FR__ChirpThreshold*/


    return (Nrc);
}

uint8_t Did_ReadData_FD05(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FML_FMR_StandardThreshold*/


    return (Nrc);
}

uint8_t Did_ReadData_FD06(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FML_FMR_ChirpThreshold*/


    return (Nrc);
}

uint8_t Did_ReadData_FD07(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read RLS_RRS_StandardThreshold*/


    return (Nrc);
}

uint8_t Did_ReadData_FD08(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read RLS_RRS_ChirpThreshold*/


    return (Nrc);
}

uint8_t Did_ReadData_FD09(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read RL_RR_StandardThreshold*/


    return (Nrc);
}

uint8_t Did_ReadData_FD0A(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read RL_RR_ChirpThreshold*/


    return (Nrc);
}

uint8_t Did_WriteData_FD0A(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;

    /*write RL_RR_ChirpThreshold*/


    return (Nrc);
}

uint8_t Did_ReadData_FD0B(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read RML_RMR_StandardThreshold*/


    return (Nrc);
}

uint8_t Did_ReadData_FD0C(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read RML_RMR_ChirpThreshold*/


    return (Nrc);
}

uint8_t Did_ReadData_FD0D(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read ObjJudgeStandardThreshold*/


    return (Nrc);
}

uint8_t Did_ReadData_FD0E(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read ObjJudgeChirpThreshold*/


    return (Nrc);
}

uint8_t Did_WriteData_FD0E(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;

    /*write ObjJudgeChirpThreshold*/


    return (Nrc);
}

uint8_t Did_ReadData_FD00(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;
	APA_Odom_CarParameterType *APA_Odom_CarParameter;
	APA_Odom_CarParameter = &GstrAPA_OdomCarPara_Ram;
    /*read FD00*/
	pu8Dst[0] = ((int32_t)(APA_Odom_CarParameter->fOneWheelPulseDisAtRRForward * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 24;
	pu8Dst[1] = ((int32_t)(APA_Odom_CarParameter->fOneWheelPulseDisAtRRForward * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 16;
	pu8Dst[2] = ((int32_t)(APA_Odom_CarParameter->fOneWheelPulseDisAtRRForward * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 8;
	pu8Dst[3] = ((int32_t)(APA_Odom_CarParameter->fOneWheelPulseDisAtRRForward * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) & 0XFF;

	pu8Dst[4] = ((int32_t)(APA_Odom_CarParameter->fOneWheelPulseDisAtRLForward * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 24;
	pu8Dst[5] = ((int32_t)(APA_Odom_CarParameter->fOneWheelPulseDisAtRLForward * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 16;
	pu8Dst[6] = ((int32_t)(APA_Odom_CarParameter->fOneWheelPulseDisAtRLForward * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 8;
	pu8Dst[7] = ((int32_t)(APA_Odom_CarParameter->fOneWheelPulseDisAtRLForward * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) & 0XFF;

	pu8Dst[8] = ((int32_t)(APA_Odom_CarParameter->fOneWheelPulseDisAtRRBackward * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 24;
	pu8Dst[9] = ((int32_t)(APA_Odom_CarParameter->fOneWheelPulseDisAtRRBackward * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 16;
	pu8Dst[10] = ((int32_t)(APA_Odom_CarParameter->fOneWheelPulseDisAtRRBackward * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 8;
	pu8Dst[11] = ((int32_t)(APA_Odom_CarParameter->fOneWheelPulseDisAtRRBackward * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) & 0XFF;
	pu8Dst[12] = ((int32_t)(APA_Odom_CarParameter->fOneWheelPulseDisAtRLBackward * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 24;
	pu8Dst[13] = ((int32_t)(APA_Odom_CarParameter->fOneWheelPulseDisAtRLBackward * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 16;
	pu8Dst[14] = ((int32_t)(APA_Odom_CarParameter->fOneWheelPulseDisAtRLBackward * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 8;
	pu8Dst[15] = ((int32_t)(APA_Odom_CarParameter->fOneWheelPulseDisAtRLBackward * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) & 0XFF;
	pu8Dst[16] = ((int32_t)(APA_Odom_CarParameter->fFront_left_R * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 24;
	pu8Dst[17] = ((int32_t)(APA_Odom_CarParameter->fFront_left_R * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 16;
	pu8Dst[18] = ((int32_t)(APA_Odom_CarParameter->fFront_left_R * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 8;
	pu8Dst[19] = ((int32_t)(APA_Odom_CarParameter->fFront_left_R * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) & 0XFF;
	pu8Dst[20] = ((int32_t)(APA_Odom_CarParameter->fFront_right_R * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 24;
	pu8Dst[21] = ((int32_t)(APA_Odom_CarParameter->fFront_right_R * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 16;
	pu8Dst[22] = ((int32_t)(APA_Odom_CarParameter->fFront_right_R * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 8;
	pu8Dst[23] = ((int32_t)(APA_Odom_CarParameter->fFront_right_R * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) & 0XFF;
	pu8Dst[24] = ((int32_t)(APA_Odom_CarParameter->fRear_left_R * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 24;
	pu8Dst[25] = ((int32_t)(APA_Odom_CarParameter->fRear_left_R * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 16;
	pu8Dst[26] = ((int32_t)(APA_Odom_CarParameter->fRear_left_R * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 8;
	pu8Dst[27] = ((int32_t)(APA_Odom_CarParameter->fRear_left_R * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) & 0XFF;
	pu8Dst[28] = ((int32_t)(APA_Odom_CarParameter->fRear_right_R * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 24;
	pu8Dst[29] = ((int32_t)(APA_Odom_CarParameter->fRear_right_R * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 16;
	pu8Dst[30] = ((int32_t)(APA_Odom_CarParameter->fRear_right_R * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 8;
	pu8Dst[31] = ((int32_t)(APA_Odom_CarParameter->fRear_right_R * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) & 0XFF;
	pu8Dst[32] = ((int32_t)(APA_Odom_CarParameter->fYawVar * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 24;
	pu8Dst[33] = ((int32_t)(APA_Odom_CarParameter->fYawVar * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 16;
	pu8Dst[34] = ((int32_t)(APA_Odom_CarParameter->fYawVar * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 8;
	pu8Dst[35] = ((int32_t)(APA_Odom_CarParameter->fYawVar * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) & 0XFF;
	pu8Dst[36] = ((int32_t)(APA_Odom_CarParameter->fYawRateVar * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 24;
	pu8Dst[37] = ((int32_t)(APA_Odom_CarParameter->fYawRateVar * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 16;
	pu8Dst[38] = ((int32_t)(APA_Odom_CarParameter->fYawRateVar * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 8;
	pu8Dst[39] = ((int32_t)(APA_Odom_CarParameter->fYawRateVar * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) & 0XFF;
	pu8Dst[40] = ((int32_t)(APA_Odom_CarParameter->fYaw_YawrateCov * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 24;
	pu8Dst[41] = ((int32_t)(APA_Odom_CarParameter->fYaw_YawrateCov * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 16;
	pu8Dst[42] = ((int32_t)(APA_Odom_CarParameter->fYaw_YawrateCov * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) >> 8;
	pu8Dst[43] = ((int32_t)(APA_Odom_CarParameter->fYaw_YawrateCov * ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM)) & 0XFF;
    return (Nrc);
}
uint8_t Did_WriteData_FD00(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
	APA_Odom_CarParameterType *APA_Odom_CarParameter;
	APA_Odom_CarParameter = &GstrAPA_OdomCarPara_Ram;
 	APA_Odom_CarParameter->fOneWheelPulseDisAtRRForward  = ((float)(((int32_t)pu8Src[0]   << 24)  | ((int32_t)pu8Src[1]   << 16) | ((int32_t)(pu8Src[2])  << 8 ) | (int32_t)(pu8Src[3])))  / ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM;
 	APA_Odom_CarParameter->fOneWheelPulseDisAtRLForward  = ((float)(((int32_t)pu8Src[4]   << 24)  | ((int32_t)pu8Src[5]   << 16) | ((int32_t)(pu8Src[6])  << 8 ) | (int32_t)(pu8Src[7])))  / ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM;
	APA_Odom_CarParameter->fOneWheelPulseDisAtRRBackward = ((float)(((int32_t)pu8Src[8]   << 24)  | ((int32_t)pu8Src[9]   << 16) | ((int32_t)(pu8Src[10]) << 8 ) | (int32_t)(pu8Src[11]))) / ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM;
	APA_Odom_CarParameter->fOneWheelPulseDisAtRLBackward = ((float)(((int32_t)pu8Src[12]   << 24) | ((int32_t)pu8Src[13]  << 16) | ((int32_t)(pu8Src[14]) << 8 ) | (int32_t)(pu8Src[15]))) / ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM;
	APA_Odom_CarParameter->fFront_left_R                 = ((float)(((int32_t)pu8Src[16]   << 24) | ((int32_t)pu8Src[17]  << 16) | ((int32_t)(pu8Src[18]) << 8 ) | (int32_t)(pu8Src[19]))) / ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM;
	APA_Odom_CarParameter->fFront_right_R                = ((float)(((int32_t)pu8Src[20]   << 24) | ((int32_t)pu8Src[21]  << 16) | ((int32_t)(pu8Src[22]) << 8 ) | (int32_t)(pu8Src[23]))) / ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM;
	APA_Odom_CarParameter->fRear_left_R                  = ((float)(((int32_t)pu8Src[24]   << 24) | ((int32_t)pu8Src[25]  << 16) | ((int32_t)(pu8Src[26]) << 8 ) | (int32_t)(pu8Src[27]))) / ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM;
	APA_Odom_CarParameter->fRear_right_R                 = ((float)(((int32_t)pu8Src[28]   << 24) | ((int32_t)pu8Src[29]  << 16) | ((int32_t)(pu8Src[30]) << 8 ) | (int32_t)(pu8Src[31]))) / ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM;
	APA_Odom_CarParameter->fYawVar                       = ((float)(((int32_t)pu8Src[32]   << 24) | ((int32_t)pu8Src[33]  << 16) | ((int32_t)(pu8Src[34]) << 8 ) | (int32_t)(pu8Src[35]))) / ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM;
	APA_Odom_CarParameter->fYawRateVar                   = ((float)(((int32_t)pu8Src[36]   << 24) | ((int32_t)pu8Src[37]  << 16) | ((int32_t)(pu8Src[38]) << 8 ) | (int32_t)(pu8Src[39]))) / ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM;
	APA_Odom_CarParameter->fYaw_YawrateCov               = ((float)(((int32_t)pu8Src[40]   << 24) | ((int32_t)pu8Src[41]  << 16) | ((int32_t)(pu8Src[42]) << 8 ) | (int32_t)(pu8Src[43]))) / ODO_DATA_TRANS_TIMES_FROM_FLASH_TO_RAM;
	return (Nrc);
}
uint8_t Did_ReadData_FD20(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FD20*/


    return (Nrc);
}

uint8_t Did_ReadData_FD21(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FD21*/


    return (Nrc);
}

uint8_t Did_WriteData_FD21(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;

    /*write FD21*/


    return (Nrc);
}

uint8_t Did_ReadData_FD22(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FD22*/


    return (Nrc);
}

uint8_t Did_WriteData_FD22(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;

    /*write FD22*/


    return (Nrc);
}

uint8_t Did_ReadData_FD23(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FD23*/


    return (Nrc);
}

uint8_t Did_WriteData_FD23(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;

    /*write FD23*/


    return (Nrc);
}

uint8_t Did_ReadData_FD24(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FD24*/


    return (Nrc);
}

uint8_t Did_WriteData_FD24(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;

    /*write FD24*/


    return (Nrc);
}

uint8_t Did_ReadData_FD25(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FD25*/


    return (Nrc);
}

uint8_t Did_WriteData_FD25(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;

    /*write FD25*/


    return (Nrc);
}

uint8_t Did_ReadData_FD26(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FD26*/


    return (Nrc);
}

uint8_t Did_WriteData_FD26(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;

    /*write FD26*/


    return (Nrc);
}

uint8_t Did_ReadData_FD27(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FD27*/


    return (Nrc);
}

uint8_t Did_WriteData_FD27(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;

    /*write FD27*/


    return (Nrc);
}

uint8_t Did_ReadData_FD28(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FD28*/


    return (Nrc);
}

uint8_t Did_WriteData_FD28(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;

    /*write FD28*/


    return (Nrc);
}

uint8_t Did_ReadData_FD29(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FD29*/


    return (Nrc);
}

uint8_t Did_WriteData_FD29(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;

    /*write FD29*/


    return (Nrc);
}

uint8_t Did_ReadData_FD2A(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FD2A*/


    return (Nrc);
}

uint8_t Did_WriteData_FD2A(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;

    /*write FD2A*/


    return (Nrc);
}

uint8_t Did_ReadData_FD2B(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FD2B*/


    return (Nrc);
}

uint8_t Did_WriteData_FD2B(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;

    /*write FD2B*/


    return (Nrc);
}

uint8_t Did_ReadData_FD2C(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FD2C*/


    return (Nrc);
}

uint8_t Did_WriteData_FD2C(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;

    /*write FD2C*/


    return (Nrc);
}

uint8_t Did_ReadData_FD2D(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FD2D*/


    return (Nrc);
}

uint8_t Did_WriteData_FD2D(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;

    /*write FD2D*/


    return (Nrc);
}

uint8_t Did_ReadData_FD2E(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FD2E*/


    return (Nrc);
}

uint8_t Did_WriteData_FD2E(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;

    /*write FD2E*/


    return (Nrc);
}

uint8_t Did_ReadData_FD2F(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FD2F*/


    return (Nrc);
}

uint8_t Did_WriteData_FD2F(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;

    /*write FD2F*/


    return (Nrc);
}

uint8_t Did_ReadData_FD40(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    (void)u16Did;   (void)u16Handle;(void)u16Size;

    /*read FD40*/
    pu8Dst[0] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE0_1].fAreaStart_X + 10000)*10) >> 24);
    pu8Dst[1] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE0_1].fAreaStart_X + 10000)*10) >> 16);
    pu8Dst[2] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE0_1].fAreaStart_X + 10000)*10) >> 8);
    pu8Dst[3] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE0_1].fAreaStart_X + 10000)*10);
    pu8Dst[4] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE0_1].fAreaStart_Y + 10000)*10) >> 24);
    pu8Dst[5] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE0_1].fAreaStart_Y + 10000)*10) >> 16);
    pu8Dst[6] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE0_1].fAreaStart_Y + 10000)*10) >> 8);
    pu8Dst[7] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE0_1].fAreaStart_Y + 10000)*10);
    pu8Dst[8] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE0_1].fAreaEnd_X + 10000)*10) >> 24);
    pu8Dst[9] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE0_1].fAreaEnd_X + 10000)*10) >> 16);
    pu8Dst[10] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE0_1].fAreaEnd_X + 10000)*10) >> 8);
    pu8Dst[11] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE0_1].fAreaEnd_X + 10000)*10);
    pu8Dst[12] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE0_1].fAreaEnd_Y + 10000)*10) >> 24);
    pu8Dst[13] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE0_1].fAreaEnd_Y + 10000)*10) >> 16);
    pu8Dst[14] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE0_1].fAreaEnd_Y + 10000)*10) >> 8);
    pu8Dst[15] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE0_1].fAreaEnd_Y + 10000)*10);
    pu8Dst[16] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE0_1].fSnsToCarAngle + 1.571)*1000) >> 24);
    pu8Dst[17] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE0_1].fSnsToCarAngle + 1.571)*1000) >> 16);
    pu8Dst[18] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE0_1].fSnsToCarAngle + 1.571)*1000) >> 8);
    pu8Dst[19] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE0_1].fSnsToCarAngle + 1.571)*1000);

    pu8Dst[20] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE1_2].fAreaStart_X + 10000)*10) >> 24);
    pu8Dst[21] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE1_2].fAreaStart_X + 10000)*10) >> 16);
    pu8Dst[22] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE1_2].fAreaStart_X + 10000)*10) >> 8);
    pu8Dst[23] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE1_2].fAreaStart_X + 10000)*10);
    pu8Dst[24] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE1_2].fAreaStart_Y + 10000)*10) >> 24);
    pu8Dst[25] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE1_2].fAreaStart_Y + 10000)*10) >> 16);
    pu8Dst[26] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE1_2].fAreaStart_Y + 10000)*10) >> 8);
    pu8Dst[27] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE1_2].fAreaStart_Y + 10000)*10);
    pu8Dst[28] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE1_2].fAreaEnd_X + 10000)*10) >> 24);
    pu8Dst[29] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE1_2].fAreaEnd_X + 10000)*10) >> 16);
    pu8Dst[30] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE1_2].fAreaEnd_X + 10000)*10) >> 8);
    pu8Dst[31] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE1_2].fAreaEnd_X + 10000)*10);
    pu8Dst[32] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE1_2].fAreaEnd_Y + 10000)*10) >> 24);
    pu8Dst[33] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE1_2].fAreaEnd_Y + 10000)*10) >> 16);
    pu8Dst[34] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE1_2].fAreaEnd_Y + 10000)*10) >> 8);
    pu8Dst[35] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE1_2].fAreaEnd_Y + 10000)*10);
    pu8Dst[36] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE1_2].fSnsToCarAngle + 1.571)*1000) >> 24);
    pu8Dst[37] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE1_2].fSnsToCarAngle + 1.571)*1000) >> 16);
    pu8Dst[38] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE1_2].fSnsToCarAngle + 1.571)*1000) >> 8);
    pu8Dst[39] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE1_2].fSnsToCarAngle + 1.571)*1000);

    return 0;
}

uint8_t Did_WriteData_FD40(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    (void)u16Did;

    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE0_1].fAreaStart_X   = ((float)(((uint32_t)pu8Src[0] <<24)|((uint32_t)pu8Src[1] <<16)|((uint32_t)pu8Src[2] <<8)|((uint32_t)pu8Src[3] ))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE0_1].fAreaStart_Y   = ((float)(((uint32_t)pu8Src[4] <<24)|((uint32_t)pu8Src[5] <<16)|((uint32_t)pu8Src[6] <<8)|((uint32_t)pu8Src[7] ))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE0_1].fAreaEnd_X     = ((float)(((uint32_t)pu8Src[8] <<24)|((uint32_t)pu8Src[9] <<16)|((uint32_t)pu8Src[10]<<8)|((uint32_t)pu8Src[11]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE0_1].fAreaEnd_Y     = ((float)(((uint32_t)pu8Src[12]<<24)|((uint32_t)pu8Src[13]<<16)|((uint32_t)pu8Src[14]<<8)|((uint32_t)pu8Src[15]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE0_1].fSnsToCarAngle = ((float)(((uint32_t)pu8Src[16]<<24)|((uint32_t)pu8Src[17]<<16)|((uint32_t)pu8Src[18]<<8)|((uint32_t)pu8Src[19]))/1000 - 1.571);

    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE1_2].fAreaStart_X   = ((float)(((uint32_t)pu8Src[20]<<24)|((uint32_t)pu8Src[21]<<16)|((uint32_t)pu8Src[22]<<8)|((uint32_t)pu8Src[23]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE1_2].fAreaStart_Y   = ((float)(((uint32_t)pu8Src[24]<<24)|((uint32_t)pu8Src[25]<<16)|((uint32_t)pu8Src[26]<<8)|((uint32_t)pu8Src[27]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE1_2].fAreaEnd_X     = ((float)(((uint32_t)pu8Src[28]<<24)|((uint32_t)pu8Src[29]<<16)|((uint32_t)pu8Src[30]<<8)|((uint32_t)pu8Src[31]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE1_2].fAreaEnd_Y     = ((float)(((uint32_t)pu8Src[32]<<24)|((uint32_t)pu8Src[33]<<16)|((uint32_t)pu8Src[34]<<8)|((uint32_t)pu8Src[35]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE1_2].fSnsToCarAngle = ((float)(((uint32_t)pu8Src[36]<<24)|((uint32_t)pu8Src[37]<<16)|((uint32_t)pu8Src[38]<<8)|((uint32_t)pu8Src[39]))/1000 - 1.571);


    /*write FD40 to Codeflash*/
    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return 0;
}

uint8_t Did_ReadData_FD41(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    (void)u16Did;   (void)u16Handle;(void)u16Size;
    uint8_t Nrc = 0;

    /*read FD41*/
    pu8Dst[0] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE2_3].fAreaStart_X + 10000)*10) >> 24);
    pu8Dst[1] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE2_3].fAreaStart_X + 10000)*10) >> 16);
    pu8Dst[2] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE2_3].fAreaStart_X + 10000)*10) >> 8);
    pu8Dst[3] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE2_3].fAreaStart_X + 10000)*10);
    pu8Dst[4] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE2_3].fAreaStart_Y + 10000)*10) >> 24);
    pu8Dst[5] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE2_3].fAreaStart_Y + 10000)*10) >> 16);
    pu8Dst[6] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE2_3].fAreaStart_Y + 10000)*10) >> 8);
    pu8Dst[7] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE2_3].fAreaStart_Y + 10000)*10);
    pu8Dst[8] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE2_3].fAreaEnd_X + 10000)*10) >> 24);
    pu8Dst[9] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE2_3].fAreaEnd_X + 10000)*10) >> 16);
    pu8Dst[10] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE2_3].fAreaEnd_X + 10000)*10) >> 8);
    pu8Dst[11] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE2_3].fAreaEnd_X + 10000)*10);
    pu8Dst[12] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE2_3].fAreaEnd_Y + 10000)*10) >> 24);
    pu8Dst[13] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE2_3].fAreaEnd_Y + 10000)*10) >> 16);
    pu8Dst[14] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE2_3].fAreaEnd_Y + 10000)*10) >> 8);
    pu8Dst[15] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE2_3].fAreaEnd_Y + 10000)*10);
    pu8Dst[16] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE2_3].fSnsToCarAngle + 1.571)*1000) >> 24);
    pu8Dst[17] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE2_3].fSnsToCarAngle + 1.571)*1000) >> 16);
    pu8Dst[18] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE2_3].fSnsToCarAngle + 1.571)*1000) >> 8);
    pu8Dst[19] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE2_3].fSnsToCarAngle + 1.571)*1000);

    pu8Dst[20] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE3_4].fAreaStart_X + 10000)*10) >> 24);
    pu8Dst[21] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE3_4].fAreaStart_X + 10000)*10) >> 16);
    pu8Dst[22] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE3_4].fAreaStart_X + 10000)*10) >> 8);
    pu8Dst[23] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE3_4].fAreaStart_X + 10000)*10);
    pu8Dst[24] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE3_4].fAreaStart_Y + 10000)*10) >> 24);
    pu8Dst[25] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE3_4].fAreaStart_Y + 10000)*10) >> 16);
    pu8Dst[26] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE3_4].fAreaStart_Y + 10000)*10) >> 8);
    pu8Dst[27] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE3_4].fAreaStart_Y + 10000)*10);
    pu8Dst[28] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE3_4].fAreaEnd_X + 10000)*10) >> 24);
    pu8Dst[29] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE3_4].fAreaEnd_X + 10000)*10) >> 16);
    pu8Dst[30] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE3_4].fAreaEnd_X + 10000)*10) >> 8);
    pu8Dst[31] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE3_4].fAreaEnd_X + 10000)*10);
    pu8Dst[32] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE3_4].fAreaEnd_Y + 10000)*10) >> 24);
    pu8Dst[33] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE3_4].fAreaEnd_Y + 10000)*10) >> 16);
    pu8Dst[34] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE3_4].fAreaEnd_Y + 10000)*10) >> 8);
    pu8Dst[35] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE3_4].fAreaEnd_Y + 10000)*10);
    pu8Dst[36] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE3_4].fSnsToCarAngle + 1.571)*1000) >> 24);
    pu8Dst[37] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE3_4].fSnsToCarAngle + 1.571)*1000) >> 16);
    pu8Dst[38] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE3_4].fSnsToCarAngle + 1.571)*1000) >> 8);
    pu8Dst[39] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE3_4].fSnsToCarAngle + 1.571)*1000);


    return (Nrc);
}

uint8_t Did_WriteData_FD41(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    (void)u16Did;

    /*write FD41*/
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE2_3].fAreaStart_X   = ((float)(((uint32_t)pu8Src[0] <<24)|((uint32_t)pu8Src[1] <<16)|((uint32_t)pu8Src[2] <<8)|((uint32_t)pu8Src[3] ))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE2_3].fAreaStart_Y   = ((float)(((uint32_t)pu8Src[4] <<24)|((uint32_t)pu8Src[5] <<16)|((uint32_t)pu8Src[6] <<8)|((uint32_t)pu8Src[7] ))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE2_3].fAreaEnd_X     = ((float)(((uint32_t)pu8Src[8] <<24)|((uint32_t)pu8Src[9] <<16)|((uint32_t)pu8Src[10]<<8)|((uint32_t)pu8Src[11]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE2_3].fAreaEnd_Y     = ((float)(((uint32_t)pu8Src[12]<<24)|((uint32_t)pu8Src[13]<<16)|((uint32_t)pu8Src[14]<<8)|((uint32_t)pu8Src[15]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE2_3].fSnsToCarAngle = ((float)(((uint32_t)pu8Src[16]<<24)|((uint32_t)pu8Src[17]<<16)|((uint32_t)pu8Src[18]<<8)|((uint32_t)pu8Src[19]))/1000 - 1.571);

    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE3_4].fAreaStart_X   = ((float)(((uint32_t)pu8Src[20]<<24)|((uint32_t)pu8Src[21]<<16)|((uint32_t)pu8Src[22]<<8)|((uint32_t)pu8Src[23]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE3_4].fAreaStart_Y   = ((float)(((uint32_t)pu8Src[24]<<24)|((uint32_t)pu8Src[25]<<16)|((uint32_t)pu8Src[26]<<8)|((uint32_t)pu8Src[27]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE3_4].fAreaEnd_X     = ((float)(((uint32_t)pu8Src[28]<<24)|((uint32_t)pu8Src[29]<<16)|((uint32_t)pu8Src[30]<<8)|((uint32_t)pu8Src[31]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE3_4].fAreaEnd_Y     = ((float)(((uint32_t)pu8Src[32]<<24)|((uint32_t)pu8Src[33]<<16)|((uint32_t)pu8Src[34]<<8)|((uint32_t)pu8Src[35]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE3_4].fSnsToCarAngle = ((float)(((uint32_t)pu8Src[36]<<24)|((uint32_t)pu8Src[37]<<16)|((uint32_t)pu8Src[38]<<8)|((uint32_t)pu8Src[39]))/1000 - 1.571);

    /*write FD41 to Codeflash*/
    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return (Nrc);
}

uint8_t Did_ReadData_FD42(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    (void)u16Did;   (void)u16Handle;(void)u16Size;
    uint8_t Nrc = 0;

    /*read FD42*/
    pu8Dst[0] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE4_5].fAreaStart_X + 10000)*10) >> 24);
    pu8Dst[1] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE4_5].fAreaStart_X + 10000)*10) >> 16);
    pu8Dst[2] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE4_5].fAreaStart_X + 10000)*10) >> 8);
    pu8Dst[3] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE4_5].fAreaStart_X + 10000)*10);
    pu8Dst[4] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE4_5].fAreaStart_Y + 10000)*10) >> 24);
    pu8Dst[5] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE4_5].fAreaStart_Y + 10000)*10) >> 16);
    pu8Dst[6] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE4_5].fAreaStart_Y + 10000)*10) >> 8);
    pu8Dst[7] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE4_5].fAreaStart_Y + 10000)*10);
    pu8Dst[8] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE4_5].fAreaEnd_X + 10000)*10) >> 24);
    pu8Dst[9] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE4_5].fAreaEnd_X + 10000)*10) >> 16);
    pu8Dst[10] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE4_5].fAreaEnd_X + 10000)*10) >> 8);
    pu8Dst[11] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE4_5].fAreaEnd_X + 10000)*10);
    pu8Dst[12] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE4_5].fAreaEnd_Y + 10000)*10) >> 24);
    pu8Dst[13] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE4_5].fAreaEnd_Y + 10000)*10) >> 16);
    pu8Dst[14] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE4_5].fAreaEnd_Y + 10000)*10) >> 8);
    pu8Dst[15] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE4_5].fAreaEnd_Y + 10000)*10);
    pu8Dst[16] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE4_5].fSnsToCarAngle + 1.571)*1000) >> 24);
    pu8Dst[17] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE4_5].fSnsToCarAngle + 1.571)*1000) >> 16);
    pu8Dst[18] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE4_5].fSnsToCarAngle + 1.571)*1000) >> 8);
    pu8Dst[19] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE4_5].fSnsToCarAngle + 1.571)*1000);

    pu8Dst[20] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE5_6].fAreaStart_X + 10000)*10) >> 24);
    pu8Dst[21] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE5_6].fAreaStart_X + 10000)*10) >> 16);
    pu8Dst[22] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE5_6].fAreaStart_X + 10000)*10) >> 8);
    pu8Dst[23] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE5_6].fAreaStart_X + 10000)*10);
    pu8Dst[24] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE5_6].fAreaStart_Y + 10000)*10) >> 24);
    pu8Dst[25] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE5_6].fAreaStart_Y + 10000)*10) >> 16);
    pu8Dst[26] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE5_6].fAreaStart_Y + 10000)*10) >> 8);
    pu8Dst[27] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE5_6].fAreaStart_Y + 10000)*10);
    pu8Dst[28] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE5_6].fAreaEnd_X + 10000)*10) >> 24);
    pu8Dst[29] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE5_6].fAreaEnd_X + 10000)*10) >> 16);
    pu8Dst[30] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE5_6].fAreaEnd_X + 10000)*10) >> 8);
    pu8Dst[31] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE5_6].fAreaEnd_X + 10000)*10);
    pu8Dst[32] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE5_6].fAreaEnd_Y + 10000)*10) >> 24);
    pu8Dst[33] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE5_6].fAreaEnd_Y + 10000)*10) >> 16);
    pu8Dst[34] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE5_6].fAreaEnd_Y + 10000)*10) >> 8);
    pu8Dst[35] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE5_6].fAreaEnd_Y + 10000)*10);
    pu8Dst[36] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE5_6].fSnsToCarAngle + 1.571)*1000) >> 24);
    pu8Dst[37] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE5_6].fSnsToCarAngle + 1.571)*1000) >> 16);
    pu8Dst[38] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE5_6].fSnsToCarAngle + 1.571)*1000) >> 8);
    pu8Dst[39] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE5_6].fSnsToCarAngle + 1.571)*1000);


    return (Nrc);
}

uint8_t Did_WriteData_FD42(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    (void)u16Did;

    /*write FD42*/
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE4_5].fAreaStart_X   = ((float)(((uint32_t)pu8Src[0] <<24)|((uint32_t)pu8Src[1] <<16)|((uint32_t)pu8Src[2] <<8)|((uint32_t)pu8Src[3] ))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE4_5].fAreaStart_Y   = ((float)(((uint32_t)pu8Src[4] <<24)|((uint32_t)pu8Src[5] <<16)|((uint32_t)pu8Src[6] <<8)|((uint32_t)pu8Src[7] ))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE4_5].fAreaEnd_X     = ((float)(((uint32_t)pu8Src[8] <<24)|((uint32_t)pu8Src[9] <<16)|((uint32_t)pu8Src[10]<<8)|((uint32_t)pu8Src[11]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE4_5].fAreaEnd_Y     = ((float)(((uint32_t)pu8Src[12]<<24)|((uint32_t)pu8Src[13]<<16)|((uint32_t)pu8Src[14]<<8)|((uint32_t)pu8Src[15]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE4_5].fSnsToCarAngle = ((float)(((uint32_t)pu8Src[16]<<24)|((uint32_t)pu8Src[17]<<16)|((uint32_t)pu8Src[18]<<8)|((uint32_t)pu8Src[19]))/1000 - 1.571);

    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE5_6].fAreaStart_X   = ((float)(((uint32_t)pu8Src[20]<<24)|((uint32_t)pu8Src[21]<<16)|((uint32_t)pu8Src[22]<<8)|((uint32_t)pu8Src[23]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE5_6].fAreaStart_Y   = ((float)(((uint32_t)pu8Src[24]<<24)|((uint32_t)pu8Src[25]<<16)|((uint32_t)pu8Src[26]<<8)|((uint32_t)pu8Src[27]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE5_6].fAreaEnd_X     = ((float)(((uint32_t)pu8Src[28]<<24)|((uint32_t)pu8Src[29]<<16)|((uint32_t)pu8Src[30]<<8)|((uint32_t)pu8Src[31]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE5_6].fAreaEnd_Y     = ((float)(((uint32_t)pu8Src[32]<<24)|((uint32_t)pu8Src[33]<<16)|((uint32_t)pu8Src[34]<<8)|((uint32_t)pu8Src[35]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE5_6].fSnsToCarAngle = ((float)(((uint32_t)pu8Src[36]<<24)|((uint32_t)pu8Src[37]<<16)|((uint32_t)pu8Src[38]<<8)|((uint32_t)pu8Src[39]))/1000 - 1.571);

    /*write FD42 to Codeflash*/
    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);

    return (Nrc);
}

uint8_t Did_ReadData_FD43(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    (void)u16Did;   (void)u16Handle;(void)u16Size;
    uint8_t Nrc = 0;

    /*read FD43*/
    pu8Dst[0] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE6_7].fAreaStart_X + 10000)*10) >> 24);
    pu8Dst[1] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE6_7].fAreaStart_X + 10000)*10) >> 16);
    pu8Dst[2] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE6_7].fAreaStart_X + 10000)*10) >> 8);
    pu8Dst[3] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE6_7].fAreaStart_X + 10000)*10);
    pu8Dst[4] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE6_7].fAreaStart_Y + 10000)*10) >> 24);
    pu8Dst[5] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE6_7].fAreaStart_Y + 10000)*10) >> 16);
    pu8Dst[6] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE6_7].fAreaStart_Y + 10000)*10) >> 8);
    pu8Dst[7] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE6_7].fAreaStart_Y + 10000)*10);
    pu8Dst[8] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE6_7].fAreaEnd_X + 10000)*10) >> 24);
    pu8Dst[9] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE6_7].fAreaEnd_X + 10000)*10) >> 16);
    pu8Dst[10] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE6_7].fAreaEnd_X + 10000)*10) >> 8);
    pu8Dst[11] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE6_7].fAreaEnd_X + 10000)*10);
    pu8Dst[12] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE6_7].fAreaEnd_Y + 10000)*10) >> 24);
    pu8Dst[13] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE6_7].fAreaEnd_Y + 10000)*10) >> 16);
    pu8Dst[14] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE6_7].fAreaEnd_Y + 10000)*10) >> 8);
    pu8Dst[15] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE6_7].fAreaEnd_Y + 10000)*10);
    pu8Dst[16] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE6_7].fSnsToCarAngle + 1.571)*1000) >> 24);
    pu8Dst[17] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE6_7].fSnsToCarAngle + 1.571)*1000) >> 16);
    pu8Dst[18] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE6_7].fSnsToCarAngle + 1.571)*1000) >> 8);
    pu8Dst[19] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE6_7].fSnsToCarAngle + 1.571)*1000);

    pu8Dst[20] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE7_8].fAreaStart_X + 10000)*10) >> 24);
    pu8Dst[21] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE7_8].fAreaStart_X + 10000)*10) >> 16);
    pu8Dst[22] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE7_8].fAreaStart_X + 10000)*10) >> 8);
    pu8Dst[23] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE7_8].fAreaStart_X + 10000)*10);
    pu8Dst[24] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE7_8].fAreaStart_Y + 10000)*10) >> 24);
    pu8Dst[25] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE7_8].fAreaStart_Y + 10000)*10) >> 16);
    pu8Dst[26] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE7_8].fAreaStart_Y + 10000)*10) >> 8);
    pu8Dst[27] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE7_8].fAreaStart_Y + 10000)*10);
    pu8Dst[28] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE7_8].fAreaEnd_X + 10000)*10) >> 24);
    pu8Dst[29] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE7_8].fAreaEnd_X + 10000)*10) >> 16);
    pu8Dst[30] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE7_8].fAreaEnd_X + 10000)*10) >> 8);
    pu8Dst[31] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE7_8].fAreaEnd_X + 10000)*10);
    pu8Dst[32] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE7_8].fAreaEnd_Y + 10000)*10) >> 24);
    pu8Dst[33] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE7_8].fAreaEnd_Y + 10000)*10) >> 16);
    pu8Dst[34] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE7_8].fAreaEnd_Y + 10000)*10) >> 8);
    pu8Dst[35] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE7_8].fAreaEnd_Y + 10000)*10);
    pu8Dst[36] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE7_8].fSnsToCarAngle + 1.571)*1000) >> 24);
    pu8Dst[37] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE7_8].fSnsToCarAngle + 1.571)*1000) >> 16);
    pu8Dst[38] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE7_8].fSnsToCarAngle + 1.571)*1000) >> 8);
    pu8Dst[39] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE7_8].fSnsToCarAngle + 1.571)*1000);


    return (Nrc);
}

uint8_t Did_WriteData_FD43(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    (void)u16Did;

    /*write FD43*/
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE6_7].fAreaStart_X   = ((float)(((uint32_t)pu8Src[0] <<24)|((uint32_t)pu8Src[1] <<16)|((uint32_t)pu8Src[2] <<8)|((uint32_t)pu8Src[3] ))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE6_7].fAreaStart_Y   = ((float)(((uint32_t)pu8Src[4] <<24)|((uint32_t)pu8Src[5] <<16)|((uint32_t)pu8Src[6] <<8)|((uint32_t)pu8Src[7] ))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE6_7].fAreaEnd_X     = ((float)(((uint32_t)pu8Src[8] <<24)|((uint32_t)pu8Src[9] <<16)|((uint32_t)pu8Src[10]<<8)|((uint32_t)pu8Src[11]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE6_7].fAreaEnd_Y     = ((float)(((uint32_t)pu8Src[12]<<24)|((uint32_t)pu8Src[13]<<16)|((uint32_t)pu8Src[14]<<8)|((uint32_t)pu8Src[15]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE6_7].fSnsToCarAngle = ((float)(((uint32_t)pu8Src[16]<<24)|((uint32_t)pu8Src[17]<<16)|((uint32_t)pu8Src[18]<<8)|((uint32_t)pu8Src[19]))/1000 - 1.571);

    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE7_8].fAreaStart_X   = ((float)(((uint32_t)pu8Src[20]<<24)|((uint32_t)pu8Src[21]<<16)|((uint32_t)pu8Src[22]<<8)|((uint32_t)pu8Src[23]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE7_8].fAreaStart_Y   = ((float)(((uint32_t)pu8Src[24]<<24)|((uint32_t)pu8Src[25]<<16)|((uint32_t)pu8Src[26]<<8)|((uint32_t)pu8Src[27]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE7_8].fAreaEnd_X     = ((float)(((uint32_t)pu8Src[28]<<24)|((uint32_t)pu8Src[29]<<16)|((uint32_t)pu8Src[30]<<8)|((uint32_t)pu8Src[31]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE7_8].fAreaEnd_Y     = ((float)(((uint32_t)pu8Src[32]<<24)|((uint32_t)pu8Src[33]<<16)|((uint32_t)pu8Src[34]<<8)|((uint32_t)pu8Src[35]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_FRONT][MAP_LINE7_8].fSnsToCarAngle = ((float)(((uint32_t)pu8Src[36]<<24)|((uint32_t)pu8Src[37]<<16)|((uint32_t)pu8Src[38]<<8)|((uint32_t)pu8Src[39]))/1000 - 1.571);

    /*write FD43 to Codeflash*/
    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);

    return (Nrc);
}

uint8_t Did_ReadData_FD44(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    (void)u16Did;   (void)u16Handle;(void)u16Size;
    uint8_t Nrc = 0;

    /*read FD44*/
    pu8Dst[0] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE0_1].fAreaStart_X + 10000)*10) >> 24);
    pu8Dst[1] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE0_1].fAreaStart_X + 10000)*10) >> 16);
    pu8Dst[2] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE0_1].fAreaStart_X + 10000)*10) >> 8);
    pu8Dst[3] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE0_1].fAreaStart_X + 10000)*10);
    pu8Dst[4] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE0_1].fAreaStart_Y + 10000)*10) >> 24);
    pu8Dst[5] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE0_1].fAreaStart_Y + 10000)*10) >> 16);
    pu8Dst[6] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE0_1].fAreaStart_Y + 10000)*10) >> 8);
    pu8Dst[7] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE0_1].fAreaStart_Y + 10000)*10);
    pu8Dst[8] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE0_1].fAreaEnd_X + 10000)*10) >> 24);
    pu8Dst[9] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE0_1].fAreaEnd_X + 10000)*10) >> 16);
    pu8Dst[10] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE0_1].fAreaEnd_X + 10000)*10) >> 8);
    pu8Dst[11] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE0_1].fAreaEnd_X + 10000)*10);
    pu8Dst[12] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE0_1].fAreaEnd_Y + 10000)*10) >> 24);
    pu8Dst[13] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE0_1].fAreaEnd_Y + 10000)*10) >> 16);
    pu8Dst[14] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE0_1].fAreaEnd_Y + 10000)*10) >> 8);
    pu8Dst[15] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE0_1].fAreaEnd_Y + 10000)*10);
    pu8Dst[16] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE0_1].fSnsToCarAngle + 1.571)*1000) >> 24);
    pu8Dst[17] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE0_1].fSnsToCarAngle + 1.571)*1000) >> 16);
    pu8Dst[18] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE0_1].fSnsToCarAngle + 1.571)*1000) >> 8);
    pu8Dst[19] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE0_1].fSnsToCarAngle + 1.571)*1000);

    pu8Dst[20] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE1_2].fAreaStart_X + 10000)*10) >> 24);
    pu8Dst[21] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE1_2].fAreaStart_X + 10000)*10) >> 16);
    pu8Dst[22] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE1_2].fAreaStart_X + 10000)*10) >> 8);
    pu8Dst[23] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE1_2].fAreaStart_X + 10000)*10);
    pu8Dst[24] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE1_2].fAreaStart_Y + 10000)*10) >> 24);
    pu8Dst[25] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE1_2].fAreaStart_Y + 10000)*10) >> 16);
    pu8Dst[26] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE1_2].fAreaStart_Y + 10000)*10) >> 8);
    pu8Dst[27] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE1_2].fAreaStart_Y + 10000)*10);
    pu8Dst[28] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE1_2].fAreaEnd_X + 10000)*10) >> 24);
    pu8Dst[29] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE1_2].fAreaEnd_X + 10000)*10) >> 16);
    pu8Dst[30] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE1_2].fAreaEnd_X + 10000)*10) >> 8);
    pu8Dst[31] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE1_2].fAreaEnd_X + 10000)*10);
    pu8Dst[32] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE1_2].fAreaEnd_Y + 10000)*10) >> 24);
    pu8Dst[33] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE1_2].fAreaEnd_Y + 10000)*10) >> 16);
    pu8Dst[34] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE1_2].fAreaEnd_Y + 10000)*10) >> 8);
    pu8Dst[35] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE1_2].fAreaEnd_Y + 10000)*10);
    pu8Dst[36] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE1_2].fSnsToCarAngle + 1.571)*1000) >> 24);
    pu8Dst[37] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE1_2].fSnsToCarAngle + 1.571)*1000) >> 16);
    pu8Dst[38] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE1_2].fSnsToCarAngle + 1.571)*1000) >> 8);
    pu8Dst[39] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE1_2].fSnsToCarAngle + 1.571)*1000);


    return (Nrc);
}

uint8_t Did_WriteData_FD44(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    (void)u16Did;

    /*write FD44*/
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE0_1].fAreaStart_X   = ((float)(((uint32_t)pu8Src[0] <<24)|((uint32_t)pu8Src[1] <<16)|((uint32_t)pu8Src[2] <<8)|((uint32_t)pu8Src[3] ))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE0_1].fAreaStart_Y   = ((float)(((uint32_t)pu8Src[4] <<24)|((uint32_t)pu8Src[5] <<16)|((uint32_t)pu8Src[6] <<8)|((uint32_t)pu8Src[7] ))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE0_1].fAreaEnd_X     = ((float)(((uint32_t)pu8Src[8] <<24)|((uint32_t)pu8Src[9] <<16)|((uint32_t)pu8Src[10]<<8)|((uint32_t)pu8Src[11]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE0_1].fAreaEnd_Y     = ((float)(((uint32_t)pu8Src[12]<<24)|((uint32_t)pu8Src[13]<<16)|((uint32_t)pu8Src[14]<<8)|((uint32_t)pu8Src[15]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE0_1].fSnsToCarAngle = ((float)(((uint32_t)pu8Src[16]<<24)|((uint32_t)pu8Src[17]<<16)|((uint32_t)pu8Src[18]<<8)|((uint32_t)pu8Src[19]))/1000 - 1.571);

    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE1_2].fAreaStart_X   = ((float)(((uint32_t)pu8Src[20]<<24)|((uint32_t)pu8Src[21]<<16)|((uint32_t)pu8Src[22]<<8)|((uint32_t)pu8Src[23]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE1_2].fAreaStart_Y   = ((float)(((uint32_t)pu8Src[24]<<24)|((uint32_t)pu8Src[25]<<16)|((uint32_t)pu8Src[26]<<8)|((uint32_t)pu8Src[27]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE1_2].fAreaEnd_X     = ((float)(((uint32_t)pu8Src[28]<<24)|((uint32_t)pu8Src[29]<<16)|((uint32_t)pu8Src[30]<<8)|((uint32_t)pu8Src[31]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE1_2].fAreaEnd_Y     = ((float)(((uint32_t)pu8Src[32]<<24)|((uint32_t)pu8Src[33]<<16)|((uint32_t)pu8Src[34]<<8)|((uint32_t)pu8Src[35]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE1_2].fSnsToCarAngle = ((float)(((uint32_t)pu8Src[36]<<24)|((uint32_t)pu8Src[37]<<16)|((uint32_t)pu8Src[38]<<8)|((uint32_t)pu8Src[39]))/1000 - 1.571);

    /*write FD44 to Codeflash*/
    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);

    return (Nrc);
}

uint8_t Did_ReadData_FD45(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    (void)u16Did;   (void)u16Handle;(void)u16Size;
    uint8_t Nrc = 0;

    /*read FD45*/
    pu8Dst[0] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE2_3].fAreaStart_X + 10000)*10) >> 24);
    pu8Dst[1] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE2_3].fAreaStart_X + 10000)*10) >> 16);
    pu8Dst[2] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE2_3].fAreaStart_X + 10000)*10) >> 8);
    pu8Dst[3] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE2_3].fAreaStart_X + 10000)*10);
    pu8Dst[4] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE2_3].fAreaStart_Y + 10000)*10) >> 24);
    pu8Dst[5] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE2_3].fAreaStart_Y + 10000)*10) >> 16);
    pu8Dst[6] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE2_3].fAreaStart_Y + 10000)*10) >> 8);
    pu8Dst[7] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE2_3].fAreaStart_Y + 10000)*10);
    pu8Dst[8] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE2_3].fAreaEnd_X + 10000)*10) >> 24);
    pu8Dst[9] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE2_3].fAreaEnd_X + 10000)*10) >> 16);
    pu8Dst[10] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE2_3].fAreaEnd_X + 10000)*10) >> 8);
    pu8Dst[11] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE2_3].fAreaEnd_X + 10000)*10);
    pu8Dst[12] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE2_3].fAreaEnd_Y + 10000)*10) >> 24);
    pu8Dst[13] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE2_3].fAreaEnd_Y + 10000)*10) >> 16);
    pu8Dst[14] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE2_3].fAreaEnd_Y + 10000)*10) >> 8);
    pu8Dst[15] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE2_3].fAreaEnd_Y + 10000)*10);
    pu8Dst[16] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE2_3].fSnsToCarAngle + 1.571)*1000) >> 24);
    pu8Dst[17] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE2_3].fSnsToCarAngle + 1.571)*1000) >> 16);
    pu8Dst[18] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE2_3].fSnsToCarAngle + 1.571)*1000) >> 8);
    pu8Dst[19] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE2_3].fSnsToCarAngle + 1.571)*1000);

    pu8Dst[20] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE3_4].fAreaStart_X + 10000)*10) >> 24);
    pu8Dst[21] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE3_4].fAreaStart_X + 10000)*10) >> 16);
    pu8Dst[22] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE3_4].fAreaStart_X + 10000)*10) >> 8);
    pu8Dst[23] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE3_4].fAreaStart_X + 10000)*10);
    pu8Dst[24] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE3_4].fAreaStart_Y + 10000)*10) >> 24);
    pu8Dst[25] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE3_4].fAreaStart_Y + 10000)*10) >> 16);
    pu8Dst[26] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE3_4].fAreaStart_Y + 10000)*10) >> 8);
    pu8Dst[27] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE3_4].fAreaStart_Y + 10000)*10);
    pu8Dst[28] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE3_4].fAreaEnd_X + 10000)*10) >> 24);
    pu8Dst[29] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE3_4].fAreaEnd_X + 10000)*10) >> 16);
    pu8Dst[30] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE3_4].fAreaEnd_X + 10000)*10) >> 8);
    pu8Dst[31] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE3_4].fAreaEnd_X + 10000)*10);
    pu8Dst[32] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE3_4].fAreaEnd_Y + 10000)*10) >> 24);
    pu8Dst[33] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE3_4].fAreaEnd_Y + 10000)*10) >> 16);
    pu8Dst[34] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE3_4].fAreaEnd_Y + 10000)*10) >> 8);
    pu8Dst[35] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE3_4].fAreaEnd_Y + 10000)*10);
    pu8Dst[36] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE3_4].fSnsToCarAngle + 1.571)*1000) >> 24);
    pu8Dst[37] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE3_4].fSnsToCarAngle + 1.571)*1000) >> 16);
    pu8Dst[38] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE3_4].fSnsToCarAngle + 1.571)*1000) >> 8);
    pu8Dst[39] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE3_4].fSnsToCarAngle + 1.571)*1000);

    return (Nrc);
}

uint8_t Did_WriteData_FD45(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    (void)u16Did;
    
    /*write FD45*/
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE2_3].fAreaStart_X   = ((float)(((uint32_t)pu8Src[0] <<24)|((uint32_t)pu8Src[1] <<16)|((uint32_t)pu8Src[2] <<8)|((uint32_t)pu8Src[3] ))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE2_3].fAreaStart_Y   = ((float)(((uint32_t)pu8Src[4] <<24)|((uint32_t)pu8Src[5] <<16)|((uint32_t)pu8Src[6] <<8)|((uint32_t)pu8Src[7] ))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE2_3].fAreaEnd_X     = ((float)(((uint32_t)pu8Src[8] <<24)|((uint32_t)pu8Src[9] <<16)|((uint32_t)pu8Src[10]<<8)|((uint32_t)pu8Src[11]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE2_3].fAreaEnd_Y     = ((float)(((uint32_t)pu8Src[12]<<24)|((uint32_t)pu8Src[13]<<16)|((uint32_t)pu8Src[14]<<8)|((uint32_t)pu8Src[15]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE2_3].fSnsToCarAngle = ((float)(((uint32_t)pu8Src[16]<<24)|((uint32_t)pu8Src[17]<<16)|((uint32_t)pu8Src[18]<<8)|((uint32_t)pu8Src[19]))/1000 - 1.571);

    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE3_4].fAreaStart_X   = ((float)(((uint32_t)pu8Src[20]<<24)|((uint32_t)pu8Src[21]<<16)|((uint32_t)pu8Src[22]<<8)|((uint32_t)pu8Src[23]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE3_4].fAreaStart_Y   = ((float)(((uint32_t)pu8Src[24]<<24)|((uint32_t)pu8Src[25]<<16)|((uint32_t)pu8Src[26]<<8)|((uint32_t)pu8Src[27]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE3_4].fAreaEnd_X     = ((float)(((uint32_t)pu8Src[28]<<24)|((uint32_t)pu8Src[29]<<16)|((uint32_t)pu8Src[30]<<8)|((uint32_t)pu8Src[31]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE3_4].fAreaEnd_Y     = ((float)(((uint32_t)pu8Src[32]<<24)|((uint32_t)pu8Src[33]<<16)|((uint32_t)pu8Src[34]<<8)|((uint32_t)pu8Src[35]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE3_4].fSnsToCarAngle = ((float)(((uint32_t)pu8Src[36]<<24)|((uint32_t)pu8Src[37]<<16)|((uint32_t)pu8Src[38]<<8)|((uint32_t)pu8Src[39]))/1000 - 1.571);

    /*write FD45 to Codeflash*/

    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return (Nrc);
}

uint8_t Did_ReadData_FD46(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    (void)u16Did;   (void)u16Handle;(void)u16Size;
    uint8_t Nrc = 0;

    /*read FD46*/
    pu8Dst[0] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE4_5].fAreaStart_X + 10000)*10) >> 24);
    pu8Dst[1] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE4_5].fAreaStart_X + 10000)*10) >> 16);
    pu8Dst[2] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE4_5].fAreaStart_X + 10000)*10) >> 8);
    pu8Dst[3] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE4_5].fAreaStart_X + 10000)*10);
    pu8Dst[4] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE4_5].fAreaStart_Y + 10000)*10) >> 24);
    pu8Dst[5] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE4_5].fAreaStart_Y + 10000)*10) >> 16);
    pu8Dst[6] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE4_5].fAreaStart_Y + 10000)*10) >> 8);
    pu8Dst[7] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE4_5].fAreaStart_Y + 10000)*10);
    pu8Dst[8] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE4_5].fAreaEnd_X + 10000)*10) >> 24);
    pu8Dst[9] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE4_5].fAreaEnd_X + 10000)*10) >> 16);
    pu8Dst[10] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE4_5].fAreaEnd_X + 10000)*10) >> 8);
    pu8Dst[11] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE4_5].fAreaEnd_X + 10000)*10);
    pu8Dst[12] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE4_5].fAreaEnd_Y + 10000)*10) >> 24);
    pu8Dst[13] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE4_5].fAreaEnd_Y + 10000)*10) >> 16);
    pu8Dst[14] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE4_5].fAreaEnd_Y + 10000)*10) >> 8);
    pu8Dst[15] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE4_5].fAreaEnd_Y + 10000)*10);
    pu8Dst[16] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE4_5].fSnsToCarAngle + 1.571)*1000) >> 24);
    pu8Dst[17] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE4_5].fSnsToCarAngle + 1.571)*1000) >> 16);
    pu8Dst[18] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE4_5].fSnsToCarAngle + 1.571)*1000) >> 8);
    pu8Dst[19] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE4_5].fSnsToCarAngle + 1.571)*1000);

    pu8Dst[20] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE5_6].fAreaStart_X + 10000)*10) >> 24);
    pu8Dst[21] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE5_6].fAreaStart_X + 10000)*10) >> 16);
    pu8Dst[22] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE5_6].fAreaStart_X + 10000)*10) >> 8);
    pu8Dst[23] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE5_6].fAreaStart_X + 10000)*10);
    pu8Dst[24] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE5_6].fAreaStart_Y + 10000)*10) >> 24);
    pu8Dst[25] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE5_6].fAreaStart_Y + 10000)*10) >> 16);
    pu8Dst[26] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE5_6].fAreaStart_Y + 10000)*10) >> 8);
    pu8Dst[27] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE5_6].fAreaStart_Y + 10000)*10);
    pu8Dst[28] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE5_6].fAreaEnd_X + 10000)*10) >> 24);
    pu8Dst[29] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE5_6].fAreaEnd_X + 10000)*10) >> 16);
    pu8Dst[30] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE5_6].fAreaEnd_X + 10000)*10) >> 8);
    pu8Dst[31] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE5_6].fAreaEnd_X + 10000)*10);
    pu8Dst[32] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE5_6].fAreaEnd_Y + 10000)*10) >> 24);
    pu8Dst[33] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE5_6].fAreaEnd_Y + 10000)*10) >> 16);
    pu8Dst[34] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE5_6].fAreaEnd_Y + 10000)*10) >> 8);
    pu8Dst[35] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE5_6].fAreaEnd_Y + 10000)*10);
    pu8Dst[36] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE5_6].fSnsToCarAngle + 1.571)*1000) >> 24);
    pu8Dst[37] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE5_6].fSnsToCarAngle + 1.571)*1000) >> 16);
    pu8Dst[38] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE5_6].fSnsToCarAngle + 1.571)*1000) >> 8);
    pu8Dst[39] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE5_6].fSnsToCarAngle + 1.571)*1000);


    return (Nrc);
}

uint8_t Did_WriteData_FD46(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    (void)u16Did;

    /*write FD46*/
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE4_5].fAreaStart_X   = ((float)(((uint32_t)pu8Src[0] <<24)|((uint32_t)pu8Src[1] <<16)|((uint32_t)pu8Src[2] <<8)|((uint32_t)pu8Src[3] ))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE4_5].fAreaStart_Y   = ((float)(((uint32_t)pu8Src[4] <<24)|((uint32_t)pu8Src[5] <<16)|((uint32_t)pu8Src[6] <<8)|((uint32_t)pu8Src[7] ))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE4_5].fAreaEnd_X     = ((float)(((uint32_t)pu8Src[8] <<24)|((uint32_t)pu8Src[9] <<16)|((uint32_t)pu8Src[10]<<8)|((uint32_t)pu8Src[11]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE4_5].fAreaEnd_Y     = ((float)(((uint32_t)pu8Src[12]<<24)|((uint32_t)pu8Src[13]<<16)|((uint32_t)pu8Src[14]<<8)|((uint32_t)pu8Src[15]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE4_5].fSnsToCarAngle = ((float)(((uint32_t)pu8Src[16]<<24)|((uint32_t)pu8Src[17]<<16)|((uint32_t)pu8Src[18]<<8)|((uint32_t)pu8Src[19]))/1000 - 1.571);

    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE5_6].fAreaStart_X   = ((float)(((uint32_t)pu8Src[20]<<24)|((uint32_t)pu8Src[21]<<16)|((uint32_t)pu8Src[22]<<8)|((uint32_t)pu8Src[23]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE5_6].fAreaStart_Y   = ((float)(((uint32_t)pu8Src[24]<<24)|((uint32_t)pu8Src[25]<<16)|((uint32_t)pu8Src[26]<<8)|((uint32_t)pu8Src[27]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE5_6].fAreaEnd_X     = ((float)(((uint32_t)pu8Src[28]<<24)|((uint32_t)pu8Src[29]<<16)|((uint32_t)pu8Src[30]<<8)|((uint32_t)pu8Src[31]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE5_6].fAreaEnd_Y     = ((float)(((uint32_t)pu8Src[32]<<24)|((uint32_t)pu8Src[33]<<16)|((uint32_t)pu8Src[34]<<8)|((uint32_t)pu8Src[35]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE5_6].fSnsToCarAngle = ((float)(((uint32_t)pu8Src[36]<<24)|((uint32_t)pu8Src[37]<<16)|((uint32_t)pu8Src[38]<<8)|((uint32_t)pu8Src[39]))/1000 - 1.571);

    /*write FD46 to Codeflash*/

    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return (Nrc);
}

uint8_t Did_ReadData_FD47(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    (void)u16Did;   (void)u16Handle;(void)u16Size;
    uint8_t Nrc = 0;

    /*read FD47*/
    pu8Dst[0] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE6_7].fAreaStart_X + 10000)*10) >> 24);
    pu8Dst[1] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE6_7].fAreaStart_X + 10000)*10) >> 16);
    pu8Dst[2] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE6_7].fAreaStart_X + 10000)*10) >> 8);
    pu8Dst[3] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE6_7].fAreaStart_X + 10000)*10);
    pu8Dst[4] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE6_7].fAreaStart_Y + 10000)*10) >> 24);
    pu8Dst[5] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE6_7].fAreaStart_Y + 10000)*10) >> 16);
    pu8Dst[6] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE6_7].fAreaStart_Y + 10000)*10) >> 8);
    pu8Dst[7] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE6_7].fAreaStart_Y + 10000)*10);
    pu8Dst[8] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE6_7].fAreaEnd_X + 10000)*10) >> 24);
    pu8Dst[9] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE6_7].fAreaEnd_X + 10000)*10) >> 16);
    pu8Dst[10] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE6_7].fAreaEnd_X + 10000)*10) >> 8);
    pu8Dst[11] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE6_7].fAreaEnd_X + 10000)*10);
    pu8Dst[12] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE6_7].fAreaEnd_Y + 10000)*10) >> 24);
    pu8Dst[13] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE6_7].fAreaEnd_Y + 10000)*10) >> 16);
    pu8Dst[14] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE6_7].fAreaEnd_Y + 10000)*10) >> 8);
    pu8Dst[15] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE6_7].fAreaEnd_Y + 10000)*10);
    pu8Dst[16] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE6_7].fSnsToCarAngle + 1.571)*1000) >> 24);
    pu8Dst[17] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE6_7].fSnsToCarAngle + 1.571)*1000) >> 16);
    pu8Dst[18] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE6_7].fSnsToCarAngle + 1.571)*1000) >> 8);
    pu8Dst[19] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE6_7].fSnsToCarAngle + 1.571)*1000);

    pu8Dst[20] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE7_8].fAreaStart_X + 10000)*10) >> 24);
    pu8Dst[21] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE7_8].fAreaStart_X + 10000)*10) >> 16);
    pu8Dst[22] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE7_8].fAreaStart_X + 10000)*10) >> 8);
    pu8Dst[23] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE7_8].fAreaStart_X + 10000)*10);
    pu8Dst[24] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE7_8].fAreaStart_Y + 10000)*10) >> 24);
    pu8Dst[25] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE7_8].fAreaStart_Y + 10000)*10) >> 16);
    pu8Dst[26] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE7_8].fAreaStart_Y + 10000)*10) >> 8);
    pu8Dst[27] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE7_8].fAreaStart_Y + 10000)*10);
    pu8Dst[28] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE7_8].fAreaEnd_X + 10000)*10) >> 24);
    pu8Dst[29] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE7_8].fAreaEnd_X + 10000)*10) >> 16);
    pu8Dst[30] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE7_8].fAreaEnd_X + 10000)*10) >> 8);
    pu8Dst[31] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE7_8].fAreaEnd_X + 10000)*10);
    pu8Dst[32] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE7_8].fAreaEnd_Y + 10000)*10) >> 24);
    pu8Dst[33] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE7_8].fAreaEnd_Y + 10000)*10) >> 16);
    pu8Dst[34] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE7_8].fAreaEnd_Y + 10000)*10) >> 8);
    pu8Dst[35] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE7_8].fAreaEnd_Y + 10000)*10);
    pu8Dst[36] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE7_8].fSnsToCarAngle + 1.571)*1000) >> 24);
    pu8Dst[37] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE7_8].fSnsToCarAngle + 1.571)*1000) >> 16);
    pu8Dst[38] = (uint8_t)((uint32_t)((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE7_8].fSnsToCarAngle + 1.571)*1000) >> 8);
    pu8Dst[39] = (uint8_t) ((GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE7_8].fSnsToCarAngle + 1.571)*1000);


    return (Nrc);
}

uint8_t Did_WriteData_FD47(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    (void)u16Did;

    /*write FD47*/
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE6_7].fAreaStart_X   = ((float)(((uint32_t)pu8Src[0] <<24)|((uint32_t)pu8Src[1] <<16)|((uint32_t)pu8Src[2] <<8)|((uint32_t)pu8Src[3] ))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE6_7].fAreaStart_Y   = ((float)(((uint32_t)pu8Src[4] <<24)|((uint32_t)pu8Src[5] <<16)|((uint32_t)pu8Src[6] <<8)|((uint32_t)pu8Src[7] ))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE6_7].fAreaEnd_X     = ((float)(((uint32_t)pu8Src[8] <<24)|((uint32_t)pu8Src[9] <<16)|((uint32_t)pu8Src[10]<<8)|((uint32_t)pu8Src[11]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE6_7].fAreaEnd_Y     = ((float)(((uint32_t)pu8Src[12]<<24)|((uint32_t)pu8Src[13]<<16)|((uint32_t)pu8Src[14]<<8)|((uint32_t)pu8Src[15]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE6_7].fSnsToCarAngle = ((float)(((uint32_t)pu8Src[16]<<24)|((uint32_t)pu8Src[17]<<16)|((uint32_t)pu8Src[18]<<8)|((uint32_t)pu8Src[19]))/1000 - 1.571);

    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE7_8].fAreaStart_X   = ((float)(((uint32_t)pu8Src[20]<<24)|((uint32_t)pu8Src[21]<<16)|((uint32_t)pu8Src[22]<<8)|((uint32_t)pu8Src[23]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE7_8].fAreaStart_Y   = ((float)(((uint32_t)pu8Src[24]<<24)|((uint32_t)pu8Src[25]<<16)|((uint32_t)pu8Src[26]<<8)|((uint32_t)pu8Src[27]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE7_8].fAreaEnd_X     = ((float)(((uint32_t)pu8Src[28]<<24)|((uint32_t)pu8Src[29]<<16)|((uint32_t)pu8Src[30]<<8)|((uint32_t)pu8Src[31]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE7_8].fAreaEnd_Y     = ((float)(((uint32_t)pu8Src[32]<<24)|((uint32_t)pu8Src[33]<<16)|((uint32_t)pu8Src[34]<<8)|((uint32_t)pu8Src[35]))/10 - 10000);
    GstrMapAreaRam[SNS_INSTALL_GROUP_REAR][MAP_LINE7_8].fSnsToCarAngle = ((float)(((uint32_t)pu8Src[36]<<24)|((uint32_t)pu8Src[37]<<16)|((uint32_t)pu8Src[38]<<8)|((uint32_t)pu8Src[39]))/1000 - 1.571);

    /*write FD47 to Codeflash*/

    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return (Nrc);
}

uint8_t Did_ReadData_FD48(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FD48*/
    pu8Dst[0] = (uint8_t)((uint32_t)((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_FRONT][0] + 2000)*10) >> 24);
    pu8Dst[1] = (uint8_t)((uint32_t)((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_FRONT][0] + 2000)*10) >> 16);
    pu8Dst[2] = (uint8_t)((uint32_t)((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_FRONT][0] + 2000)*10) >> 8);
    pu8Dst[3] = (uint8_t) ((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_FRONT][0] + 2000)*10);
    pu8Dst[4] = (uint8_t)((uint32_t)((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_FRONT][1] + 2000)*10) >> 24);
    pu8Dst[5] = (uint8_t)((uint32_t)((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_FRONT][1] + 2000)*10) >> 16);
    pu8Dst[6] = (uint8_t)((uint32_t)((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_FRONT][1] + 2000)*10) >> 8);
    pu8Dst[7] = (uint8_t) ((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_FRONT][1] + 2000)*10);
    pu8Dst[8] = (uint8_t)((uint32_t)((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_FRONT][2] + 2000)*10) >> 24);
    pu8Dst[9] = (uint8_t)((uint32_t)((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_FRONT][2] + 2000)*10) >> 16);
    pu8Dst[10] = (uint8_t)((uint32_t)((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_FRONT][2] + 2000)*10) >> 8);
    pu8Dst[11] = (uint8_t) ((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_FRONT][2] + 2000)*10);
    pu8Dst[12] = (uint8_t)((uint32_t)((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_FRONT][3] + 2000)*10) >> 24);
    pu8Dst[13] = (uint8_t)((uint32_t)((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_FRONT][3] + 2000)*10) >> 16);
    pu8Dst[14] = (uint8_t)((uint32_t)((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_FRONT][3] + 2000)*10) >> 8);
    pu8Dst[15] = (uint8_t) ((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_FRONT][3] + 2000)*10);


    return (Nrc);
}

uint8_t Did_WriteData_FD48(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;

    /*write FD48*/
    GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_FRONT][0]   = ((float)(((uint32_t)pu8Src[0] <<24)|((uint32_t)pu8Src[1] <<16)|((uint32_t)pu8Src[2] <<8)|((uint32_t)pu8Src[3] ))/10 - 2000);
    GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_FRONT][1]   = ((float)(((uint32_t)pu8Src[4] <<24)|((uint32_t)pu8Src[5] <<16)|((uint32_t)pu8Src[6] <<8)|((uint32_t)pu8Src[7] ))/10 - 2000);
    GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_FRONT][2]   = ((float)(((uint32_t)pu8Src[8] <<24)|((uint32_t)pu8Src[9] <<16)|((uint32_t)pu8Src[10]<<8)|((uint32_t)pu8Src[11]))/10 - 2000);
    GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_FRONT][3]   = ((float)(((uint32_t)pu8Src[12]<<24)|((uint32_t)pu8Src[13]<<16)|((uint32_t)pu8Src[14]<<8)|((uint32_t)pu8Src[15]))/10 - 2000);
    
    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);

    return (Nrc);
}

uint8_t Did_ReadData_FD49(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FD49*/
    pu8Dst[0] = (uint8_t)((uint32_t)((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_REAR][0] + 2000)*10) >> 24);
    pu8Dst[1] = (uint8_t)((uint32_t)((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_REAR][0] + 2000)*10) >> 16);
    pu8Dst[2] = (uint8_t)((uint32_t)((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_REAR][0] + 2000)*10) >> 8);
    pu8Dst[3] = (uint8_t) ((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_REAR][0] + 2000)*10);
    pu8Dst[4] = (uint8_t)((uint32_t)((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_REAR][1] + 2000)*10) >> 24);
    pu8Dst[5] = (uint8_t)((uint32_t)((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_REAR][1] + 2000)*10) >> 16);
    pu8Dst[6] = (uint8_t)((uint32_t)((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_REAR][1] + 2000)*10) >> 8);
    pu8Dst[7] = (uint8_t) ((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_REAR][1] + 2000)*10);
    pu8Dst[8] = (uint8_t)((uint32_t)((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_REAR][2] + 2000)*10) >> 24);
    pu8Dst[9] = (uint8_t)((uint32_t)((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_REAR][2] + 2000)*10) >> 16);
    pu8Dst[10] = (uint8_t)((uint32_t)((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_REAR][2] + 2000)*10) >> 8);
    pu8Dst[11] = (uint8_t) ((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_REAR][2] + 2000)*10);
    pu8Dst[12] = (uint8_t)((uint32_t)((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_REAR][3] + 2000)*10) >> 24);
    pu8Dst[13] = (uint8_t)((uint32_t)((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_REAR][3] + 2000)*10) >> 16);
    pu8Dst[14] = (uint8_t)((uint32_t)((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_REAR][3] + 2000)*10) >> 8);
    pu8Dst[15] = (uint8_t) ((GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_REAR][3] + 2000)*10);


    return (Nrc);
}

uint8_t Did_WriteData_FD49(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;

    /*write FD49*/
    GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_REAR][0]   = ((float)(((uint32_t)pu8Src[0] <<24)|((uint32_t)pu8Src[1] <<16)|((uint32_t)pu8Src[2] <<8)|((uint32_t)pu8Src[3] ))/10 - 2000);
    GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_REAR][1]   = ((float)(((uint32_t)pu8Src[4] <<24)|((uint32_t)pu8Src[5] <<16)|((uint32_t)pu8Src[6] <<8)|((uint32_t)pu8Src[7] ))/10 - 2000);
    GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_REAR][2]   = ((float)(((uint32_t)pu8Src[8] <<24)|((uint32_t)pu8Src[9] <<16)|((uint32_t)pu8Src[10]<<8)|((uint32_t)pu8Src[11]))/10 - 2000);
    GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_REAR][3]   = ((float)(((uint32_t)pu8Src[12]<<24)|((uint32_t)pu8Src[13]<<16)|((uint32_t)pu8Src[14]<<8)|((uint32_t)pu8Src[15]))/10 - 2000);

    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return (Nrc);
}

uint8_t Did_ReadData_FD50(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*read FD50*/
   for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
   {
      pu8Dst[2*i] = (uint8_t)(Gu16FLS_FRS_MasStdThresTableForMapInRAM[i]>>8);
      pu8Dst[2*i+1] = (uint8_t)(Gu16FLS_FRS_MasStdThresTableForMapInRAM[i]);
   }

    return (Nrc);
}

uint8_t Did_WriteData_FD50(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*write FD50*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
        Gu16FLS_FRS_MasStdThresTableForMapInRAM[i] = (((uint16_t)pu8Src[2*i]<<8)|((uint16_t)pu8Src[2*i+1]));
    }

    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return (Nrc);
}

uint8_t Did_ReadData_FD51(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*read FD51*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
       pu8Dst[2*i] = (uint8_t)(Gu16FLS_FRS_MasChirpThresTableForMapInRAM[i]>>8);
       pu8Dst[2*i+1] = (uint8_t)(Gu16FLS_FRS_MasChirpThresTableForMapInRAM[i]);
    }


    return (Nrc);
}

uint8_t Did_WriteData_FD51(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*write FD51*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
        Gu16FLS_FRS_MasChirpThresTableForMapInRAM[i] = (((uint16_t)pu8Src[2*i]<<8)|((uint16_t)pu8Src[2*i+1]));
    }

    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return (Nrc);
}

uint8_t Did_ReadData_FD52(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*read FD52*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
       pu8Dst[2*i] = (uint8_t)(Gu16FL_FR_MasStdThresTableForMapInRAM[i]>>8);
       pu8Dst[2*i+1] = (uint8_t)(Gu16FL_FR_MasStdThresTableForMapInRAM[i]);
    }


    return (Nrc);
}

uint8_t Did_WriteData_FD52(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*write FD52*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
        Gu16FL_FR_MasStdThresTableForMapInRAM[i] = (((uint16_t)pu8Src[2*i]<<8)|((uint16_t)pu8Src[2*i+1]));
    }

    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return (Nrc);
}

uint8_t Did_ReadData_FD53(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*read FD53*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
       pu8Dst[2*i] = (uint8_t)(Gu16FL_FR_MasChirpThresTableForMapInRAM[i]>>8);
       pu8Dst[2*i+1] = (uint8_t)(Gu16FL_FR_MasChirpThresTableForMapInRAM[i]);
    }


    return (Nrc);
}

uint8_t Did_WriteData_FD53(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*write FD53*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
        Gu16FL_FR_MasChirpThresTableForMapInRAM[i] = (((uint16_t)pu8Src[2*i]<<8)|((uint16_t)pu8Src[2*i+1]));
    }

    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return (Nrc);
}

uint8_t Did_ReadData_FD54(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*read FD54*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
       pu8Dst[2*i] = (uint8_t)(Gu16FML_FMR_MasStdThresTableForMapInRAM[i]>>8);
       pu8Dst[2*i+1] = (uint8_t)(Gu16FML_FMR_MasStdThresTableForMapInRAM[i]);
    }


    return (Nrc);
}

uint8_t Did_WriteData_FD54(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*write FD54*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
        Gu16FML_FMR_MasStdThresTableForMapInRAM[i] = (((uint16_t)pu8Src[2*i]<<8)|((uint16_t)pu8Src[2*i+1]));
    }

    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return (Nrc);
}

uint8_t Did_ReadData_FD55(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*read FD55*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
       pu8Dst[2*i] = (uint8_t)(Gu16FML_FMR_MasChirpThresTableForMapInRAM[i]>>8);
       pu8Dst[2*i+1] = (uint8_t)(Gu16FML_FMR_MasChirpThresTableForMapInRAM[i]);
    }

    return (Nrc);
}

uint8_t Did_WriteData_FD55(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*write FD55*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
        Gu16FML_FMR_MasChirpThresTableForMapInRAM[i] = (((uint16_t)pu8Src[2*i]<<8)|((uint16_t)pu8Src[2*i+1]));
    }

    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return (Nrc);
}

uint8_t Did_ReadData_FD56(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*read FD56*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
       pu8Dst[2*i] = (uint8_t)(Gu16RLS_RRS_MasStdThresTableForMapInRAM[i]>>8);
       pu8Dst[2*i+1] = (uint8_t)(Gu16RLS_RRS_MasStdThresTableForMapInRAM[i]);
    }


    return (Nrc);
}

uint8_t Did_WriteData_FD56(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*write FD56*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
        Gu16RLS_RRS_MasStdThresTableForMapInRAM[i] = (((uint16_t)pu8Src[2*i]<<8)|((uint16_t)pu8Src[2*i+1]));
    }

    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return (Nrc);
}

uint8_t Did_ReadData_FD57(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*read FD57*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
       pu8Dst[2*i] = (uint8_t)(Gu16RLS_RRS_MasChirpThresTableForMapInRAM[i]>>8);
       pu8Dst[2*i+1] = (uint8_t)(Gu16RLS_RRS_MasChirpThresTableForMapInRAM[i]);
    }


    return (Nrc);
}

uint8_t Did_WriteData_FD57(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*write FD57*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
        Gu16RLS_RRS_MasChirpThresTableForMapInRAM[i] = (((uint16_t)pu8Src[2*i]<<8)|((uint16_t)pu8Src[2*i+1]));
    }

    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return (Nrc);
}

uint8_t Did_ReadData_FD58(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*read FD58*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
       pu8Dst[2*i] = (uint8_t)(Gu16RL_RR_MasStdThresTableForMapInRAM[i]>>8);
       pu8Dst[2*i+1] = (uint8_t)(Gu16RL_RR_MasStdThresTableForMapInRAM[i]);
    }


    return (Nrc);
}

uint8_t Did_WriteData_FD58(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*write FD58*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
        Gu16RL_RR_MasStdThresTableForMapInRAM[i] = (((uint16_t)pu8Src[2*i]<<8)|((uint16_t)pu8Src[2*i+1]));
    }

    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return (Nrc);
}

uint8_t Did_ReadData_FD59(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*read FD59*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
       pu8Dst[2*i] = (uint8_t)(Gu16RL_RR_MasChirpThresTableForMapInRAM[i]>>8);
       pu8Dst[2*i+1] = (uint8_t)(Gu16RL_RR_MasChirpThresTableForMapInRAM[i]);
    }


    return (Nrc);
}

uint8_t Did_WriteData_FD59(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*write FD59*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
        Gu16RL_RR_MasChirpThresTableForMapInRAM[i] = (((uint16_t)pu8Src[2*i]<<8)|((uint16_t)pu8Src[2*i+1]));
    }

    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return (Nrc);
}

uint8_t Did_ReadData_FD5A(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*read FD5A*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
       pu8Dst[2*i] = (uint8_t)(Gu16RML_RMR_MasStdThresTableForMapInRAM[i]>>8);
       pu8Dst[2*i+1] = (uint8_t)(Gu16RML_RMR_MasStdThresTableForMapInRAM[i]);
    }


    return (Nrc);
}

uint8_t Did_WriteData_FD5A(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*write FD5A*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
        Gu16RML_RMR_MasStdThresTableForMapInRAM[i] = (((uint16_t)pu8Src[2*i]<<8)|((uint16_t)pu8Src[2*i+1]));
    }

    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return (Nrc);
}

uint8_t Did_ReadData_FD5B(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*read FD5B*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
       pu8Dst[2*i] = (uint8_t)(Gu16RML_RMR_MasChirpThresTableForMapInRAM[i]>>8);
       pu8Dst[2*i+1] = (uint8_t)(Gu16RML_RMR_MasChirpThresTableForMapInRAM[i]);
    }


    return (Nrc);
}

uint8_t Did_WriteData_FD5B(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*write FD5B*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
        Gu16RML_RMR_MasChirpThresTableForMapInRAM[i] = (((uint16_t)pu8Src[2*i]<<8)|((uint16_t)pu8Src[2*i+1]));
    }

    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return (Nrc);
}

uint8_t Did_ReadData_FD5C(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*read FD5C*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
       pu8Dst[2*i] = (uint8_t)(Gu16FLS_FRS_LisStdThresTableForMapInRAM[i]>>8);
       pu8Dst[2*i+1] = (uint8_t)(Gu16FLS_FRS_LisStdThresTableForMapInRAM[i]);
    }


    return (Nrc);
}

uint8_t Did_WriteData_FD5C(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*write FD5C*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
        Gu16FLS_FRS_LisStdThresTableForMapInRAM[i] = (((uint16_t)pu8Src[2*i]<<8)|((uint16_t)pu8Src[2*i+1]));
    }

    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return (Nrc);
}

uint8_t Did_ReadData_FD5D(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*read FD5D*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
       pu8Dst[2*i] = (uint8_t)(Gu16FLS_FRS_LisChirpThresTableForMapInRAM[i]>>8);
       pu8Dst[2*i+1] = (uint8_t)(Gu16FLS_FRS_LisChirpThresTableForMapInRAM[i]);
    }


    return (Nrc);
}

uint8_t Did_WriteData_FD5D(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*write FD5D*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
        Gu16FLS_FRS_LisChirpThresTableForMapInRAM[i] = (((uint16_t)pu8Src[2*i]<<8)|((uint16_t)pu8Src[2*i+1]));
    }

    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return (Nrc);
}

uint8_t Did_ReadData_FD5E(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*read FD5E*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
       pu8Dst[2*i] = (uint8_t)(Gu16FL_FR_LisStdThresTableForMapInRAM[i]>>8);
       pu8Dst[2*i+1] = (uint8_t)(Gu16FL_FR_LisStdThresTableForMapInRAM[i]);
    }


    return (Nrc);
}

uint8_t Did_WriteData_FD5E(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*write FD5E*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
        Gu16FL_FR_LisStdThresTableForMapInRAM[i] = (((uint16_t)pu8Src[2*i]<<8)|((uint16_t)pu8Src[2*i+1]));
    }

    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return (Nrc);
}

uint8_t Did_ReadData_FD5F(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*read FD5F*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
       pu8Dst[2*i] = (uint8_t)(Gu16FL_FR_LisChirpThresTableForMapInRAM[i]>>8);
       pu8Dst[2*i+1] = (uint8_t)(Gu16FL_FR_LisChirpThresTableForMapInRAM[i]);
    }


    return (Nrc);
}

uint8_t Did_WriteData_FD5F(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*write FD5F*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
        Gu16FL_FR_LisChirpThresTableForMapInRAM[i] = (((uint16_t)pu8Src[2*i]<<8)|((uint16_t)pu8Src[2*i+1]));
    }

    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return (Nrc);
}

uint8_t Did_ReadData_FD60(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*read FD60*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
       pu8Dst[2*i] = (uint8_t)(Gu16FML_FMR_LisStdThresTableForMapInRAM[i]>>8);
       pu8Dst[2*i+1] = (uint8_t)(Gu16FML_FMR_LisStdThresTableForMapInRAM[i]);
    }


    return (Nrc);
}

uint8_t Did_WriteData_FD60(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*write FD60*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
        Gu16FML_FMR_LisStdThresTableForMapInRAM[i] = (((uint16_t)pu8Src[2*i]<<8)|((uint16_t)pu8Src[2*i+1]));
    }

    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return (Nrc);
}

uint8_t Did_ReadData_FD61(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*read FD61*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
       pu8Dst[2*i] = (uint8_t)(Gu16FML_FMR_LisChirpThresTableForMapInRAM[i]>>8);
       pu8Dst[2*i+1] = (uint8_t)(Gu16FML_FMR_LisChirpThresTableForMapInRAM[i]);
    }


    return (Nrc);
}

uint8_t Did_WriteData_FD61(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*write FD61*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
        Gu16FML_FMR_LisChirpThresTableForMapInRAM[i] = (((uint16_t)pu8Src[2*i]<<8)|((uint16_t)pu8Src[2*i+1]));
    }

    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return (Nrc);
}

uint8_t Did_ReadData_FD62(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*read FD62*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
       pu8Dst[2*i] = (uint8_t)(Gu16RLS_RRS_LisStdThresTableForMapInRAM[i]>>8);
       pu8Dst[2*i+1] = (uint8_t)(Gu16RLS_RRS_LisStdThresTableForMapInRAM[i]);
    }


    return (Nrc);
}

uint8_t Did_WriteData_FD62(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*write FD62*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
        Gu16RLS_RRS_LisStdThresTableForMapInRAM[i] = (((uint16_t)pu8Src[2*i]<<8)|((uint16_t)pu8Src[2*i+1]));
    }

    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return (Nrc);
}

uint8_t Did_ReadData_FD63(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*read FD63*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
       pu8Dst[2*i] = (uint8_t)(Gu16RLS_RRS_LisChirpThresTableForMapInRAM[i]>>8);
       pu8Dst[2*i+1] = (uint8_t)(Gu16RLS_RRS_LisChirpThresTableForMapInRAM[i]);
    }


    return (Nrc);
}

uint8_t Did_WriteData_FD63(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*write FD63*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
        Gu16RLS_RRS_LisChirpThresTableForMapInRAM[i] = (((uint16_t)pu8Src[2*i]<<8)|((uint16_t)pu8Src[2*i+1]));
    }

    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return (Nrc);
}

uint8_t Did_ReadData_FD64(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*read FD64*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
       pu8Dst[2*i] = (uint8_t)(Gu16RL_RR_LisStdThresTableForMapInRAM[i]>>8);
       pu8Dst[2*i+1] = (uint8_t)(Gu16RL_RR_LisStdThresTableForMapInRAM[i]);
    }


    return (Nrc);
}

uint8_t Did_WriteData_FD64(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*write FD64*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
        Gu16RL_RR_LisStdThresTableForMapInRAM[i] = (((uint16_t)pu8Src[2*i]<<8)|((uint16_t)pu8Src[2*i+1]));
    }

    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return (Nrc);
}

uint8_t Did_ReadData_FD65(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*read FD65*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
       pu8Dst[2*i] = (uint8_t)(Gu16RL_RR_LisChirpThresTableForMapInRAM[i]>>8);
       pu8Dst[2*i+1] = (uint8_t)(Gu16RL_RR_LisChirpThresTableForMapInRAM[i]);
    }


    return (Nrc);
}

uint8_t Did_WriteData_FD65(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*write FD65*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
        Gu16RL_RR_LisChirpThresTableForMapInRAM[i] = (((uint16_t)pu8Src[2*i]<<8)|((uint16_t)pu8Src[2*i+1]));
    }

    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return (Nrc);
}

uint8_t Did_ReadData_FD66(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*read FD66*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
       pu8Dst[2*i] = (uint8_t)(Gu16RML_RMR_LisStdThresTableForMapInRAM[i]>>8);
       pu8Dst[2*i+1] = (uint8_t)(Gu16RML_RMR_LisStdThresTableForMapInRAM[i]);
    }


    return (Nrc);
}

uint8_t Did_WriteData_FD66(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*write FD66*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
        Gu16RML_RMR_LisStdThresTableForMapInRAM[i] = (((uint16_t)pu8Src[2*i]<<8)|((uint16_t)pu8Src[2*i+1]));
    }

    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return (Nrc);
}

uint8_t Did_ReadData_FD67(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*read FD67*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
       pu8Dst[2*i] = (uint8_t)(Gu16RML_RMR_LisChirpThresTableForMapInRAM[i]>>8);
       pu8Dst[2*i+1] = (uint8_t)(Gu16RML_RMR_LisChirpThresTableForMapInRAM[i]);
    }


    return (Nrc);
}

uint8_t Did_WriteData_FD67(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;
    uint8_t i = 0;

    /*write FD67*/
    for(i = 0;i < SNS_MAP_DIS_TOTAL_NUM;i++)
    {
        Gu16RML_RMR_LisChirpThresTableForMapInRAM[i] = (((uint16_t)pu8Src[2*i]<<8)|((uint16_t)pu8Src[2*i+1]));
    }

    //WriteToCodeFlash(CalibrationDIDDefine[u16Handle].GdwADDR, u16Size, &pu8Src[0]);
    return (Nrc);
}

uint8_t Did_ReadData_FD70(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FD70*/


    return (Nrc);
}

uint8_t Did_WriteData_FD70(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;

    /*write FD70*/


    return (Nrc);
}

uint8_t Did_ReadData_FDA0(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FDA0*/


    return (Nrc);
}

uint8_t Did_WriteData_FDA0(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;

    /*write FDA0*/


    return (Nrc);
}

uint8_t Did_ReadData_FDA1(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FDA1*/


    return (Nrc);
}

uint8_t Did_WriteData_FDA1(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;

    /*write FDA1*/


    return (Nrc);
}

uint8_t Did_ReadData_FDA2(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FDA2*/


    return (Nrc);
}

uint8_t Did_WriteData_FDA2(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;

    /*write FDA2*/


    return (Nrc);
}

uint8_t Did_ReadData_FDA3(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FDA3*/


    return (Nrc);
}

uint8_t Did_WriteData_FDA3(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;

    /*write FDA3*/


    return (Nrc);
}

uint8_t Did_ReadData_FDA4(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst)
{
    uint8_t Nrc = 0;

    /*read FDA4*/


    return (Nrc);
}

uint8_t Did_WriteData_FDA4(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src)
{
    uint8_t Nrc = 0;

    /*write FDA4*/


    return (Nrc);
}



#endif // (CALIBRATION_EN == STD_ON)




