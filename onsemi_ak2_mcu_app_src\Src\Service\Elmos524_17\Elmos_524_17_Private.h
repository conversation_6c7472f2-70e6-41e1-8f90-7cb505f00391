/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * Elmos_524_17_Private.h:
 * Created on: 2019-11-01 15:04
 * Original designer: Lvqv Lin
 ******************************************************************************/
#ifndef __ELMOS_PRIVATED_H__
#define __ELMOS_PRIVATED_H__

/* Includes ------------------------------------------------------------------*/
#include "Elmos_524_17SnsMeasCfg.h"

/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/





/******************************************************************************/
/* \Description :                                                       */
/******************************************************************************/


/******************************************************************************/
/* \Description :                                                    */
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
extern MeasChlECHOData_Str MeasECHOMsg[DSIMasterNum][DSIChlNum];

/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/

extern void Clear_ECHOMsgMEAS(DSIMasterID_en LeDSIMasterID,DSIChlID_en LeDSIChlID);
extern void Clear_SnsSelfTestStatusMsg(SnsID_en SnsID);
extern void Clear_SnsPDCMMsg_Status(SnsID_en SnsID);
extern Sns_PDCM_MEASMsg_Str * GetMeasMsgBufAddr(DSIMasterID_en LeDSIMasterID,DSIChlID_en LeDSIChlID,uint8 LeSlotID);
extern Sns_PDCMMsg_Status_Str * GetPDCMMsg_StatusAddr(DSIMasterID_en LeDSIMasterID,DSIChlID_en LeDSIChlID,uint8 LeSlotID);
extern SnsSelfTestStatusMsg_str * GetSnsSelfTestStatusAddr(DSIMasterID_en LeDSIMasterID,DSIChlID_en LeDSIChlID,uint8 LeSlotID);
extern uint8 SetECHOMsgMEAS_Mode(DSIMasterID_en LeDSIMasterID,DSIChlID_en LeDSIChlID,MeasType_en MeasType,SnsMeasProfile_en MeasProfile,CMDSeq_en MeasSeq);
extern uint8 SetSns_ECHOMsgSnsMEAS_TYPE(DSIMasterID_en LeDSIMasterID,DSIChlID_en LeDSIChlID,uint8 LeSlotID,SNS_MEAS_TYPE_en SNS_MEAS_TYPE);
extern uint8 DetPDCMResFrameMSg(DSIMasterID_en DSIMasterID,DSIChlID_en DSIChlID,PDCM_DSIChlRES_str *pPDCMResData);
extern void SetTransMeasCmdTime(DSIMasterID_en LeDSIMasterID,DSIChlID_en LeDSIChlID,uint32 TransTime);
extern void SetMeasECHOMsgUpdateTimeTime(DSIMasterID_en LeDSIMasterID,DSIChlID_en LeDSIChlID);
extern void SetDataRecEndTime(DSIMasterID_en LeDSIMasterID,DSIChlID_en LeDSIChlID,uint32 TransTime);
extern void SetADASYNTime(DSIMasterID_en LeDSIMasterID,DSIChlID_en LeDSIChlID,uint32 SYNTime);

#endif /* __SVC_INTERRUPT_INT_H__ */
/*****************************************************END Sns_int.h*********************************************************/

