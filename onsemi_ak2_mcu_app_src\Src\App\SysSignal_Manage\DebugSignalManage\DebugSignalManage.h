/******************************************************************************
* MODIFICATION LOG :                                                           
*******************************************************************************
*                                                                              
* !Designed by     : 
* !Coded by        :   
*                                                                              
* 
*******************************************************************************/
#ifndef __DEBUGSIGNALMANAGE_H__
#define __DEBUGSIGNALMANAGE_H__
/* Includes ------------------------------------------------------------------*/
#include "DebugSignal_CommonTypes.h"
#include "CAN_COM.h"
#if(DEBUG_FRAME_TX_EN==STD_ON)
/**********************************************************************************************************************************/
#define GetTxFLS_ImpData_6EE_ecu0_0()                       (Sns_ImpData_6EE_ecu0_0_pool.bits.FLS_ImpData_1|(Sns_ImpData_6EE_ecu0_0_pool.bits.FLS_ImpData_2<<8))
#define SetTxFLS_ImpData_6EE_ecu0_0(v)                      Sns_ImpData_6EE_ecu0_0_pool.bits.FLS_ImpData_1=(v);Sns_ImpData_6EE_ecu0_0_pool.bits.FLS_ImpData_2=((v)>>8);
#define GetTxFL_ImpData_6EE_ecu0_0()                        (Sns_ImpData_6EE_ecu0_0_pool.bits.FL_ImpData_1|(Sns_ImpData_6EE_ecu0_0_pool.bits.FL_ImpData_2<<8))
#define SetTxFL_ImpData_6EE_ecu0_0(v)                       Sns_ImpData_6EE_ecu0_0_pool.bits.FL_ImpData_1=(v);Sns_ImpData_6EE_ecu0_0_pool.bits.FL_ImpData_2=((v)>>8);
#define GetTxFML_ImpData_6EE_ecu0_0()                       (Sns_ImpData_6EE_ecu0_0_pool.bits.FML_ImpData_1|(Sns_ImpData_6EE_ecu0_0_pool.bits.FML_ImpData_2<<8))
#define SetTxFML_ImpData_6EE_ecu0_0(v)                      Sns_ImpData_6EE_ecu0_0_pool.bits.FML_ImpData_1=(v);Sns_ImpData_6EE_ecu0_0_pool.bits.FML_ImpData_2=((v)>>8);
#define GetTxFMR_ImpData_6EE_ecu0_0()                       (Sns_ImpData_6EE_ecu0_0_pool.bits.FMR_ImpData_1|(Sns_ImpData_6EE_ecu0_0_pool.bits.FMR_ImpData_2<<8))
#define SetTxFMR_ImpData_6EE_ecu0_0(v)                      Sns_ImpData_6EE_ecu0_0_pool.bits.FMR_ImpData_1=(v);Sns_ImpData_6EE_ecu0_0_pool.bits.FMR_ImpData_2=((v)>>8);
#define GetTxFR_ImpData_6EE_ecu0_0()                        (Sns_ImpData_6EE_ecu0_0_pool.bits.FR_ImpData_1|(Sns_ImpData_6EE_ecu0_0_pool.bits.FR_ImpData_2<<8))
#define SetTxFR_ImpData_6EE_ecu0_0(v)                       Sns_ImpData_6EE_ecu0_0_pool.bits.FR_ImpData_1=(v);Sns_ImpData_6EE_ecu0_0_pool.bits.FR_ImpData_2=((v)>>8);
#define GetTxFRS_ImpData_6EE_ecu0_0()                       (Sns_ImpData_6EE_ecu0_0_pool.bits.FRS_ImpData_1|(Sns_ImpData_6EE_ecu0_0_pool.bits.FRS_ImpData_2<<8))
#define SetTxFRS_ImpData_6EE_ecu0_0(v)                      Sns_ImpData_6EE_ecu0_0_pool.bits.FRS_ImpData_1=(v);Sns_ImpData_6EE_ecu0_0_pool.bits.FRS_ImpData_2=((v)>>8);
#define GetTxRRS_ImpData_6EE_ecu0_0()                       (Sns_ImpData_6EE_ecu0_0_pool.bits.RRS_ImpData_1|(Sns_ImpData_6EE_ecu0_0_pool.bits.RRS_ImpData_2<<8))
#define SetTxRRS_ImpData_6EE_ecu0_0(v)                      Sns_ImpData_6EE_ecu0_0_pool.bits.RRS_ImpData_1=(v);Sns_ImpData_6EE_ecu0_0_pool.bits.RRS_ImpData_2=((v)>>8);
#define GetTxRR_ImpData_6EE_ecu0_0()                        (Sns_ImpData_6EE_ecu0_0_pool.bits.RR_ImpData_1|(Sns_ImpData_6EE_ecu0_0_pool.bits.RR_ImpData_2<<8))
#define SetTxRR_ImpData_6EE_ecu0_0(v)                       Sns_ImpData_6EE_ecu0_0_pool.bits.RR_ImpData_1=(v);Sns_ImpData_6EE_ecu0_0_pool.bits.RR_ImpData_2=((v)>>8);
#define GetTxRMR_ImpData_6EE_ecu0_0()                       (Sns_ImpData_6EE_ecu0_0_pool.bits.RMR_ImpData_1|(Sns_ImpData_6EE_ecu0_0_pool.bits.RMR_ImpData_2<<8))
#define SetTxRMR_ImpData_6EE_ecu0_0(v)                      Sns_ImpData_6EE_ecu0_0_pool.bits.RMR_ImpData_1=(v);Sns_ImpData_6EE_ecu0_0_pool.bits.RMR_ImpData_2=((v)>>8);
#define GetTxRML_ImpData_6EE_ecu0_0()                       (Sns_ImpData_6EE_ecu0_0_pool.bits.RML_ImpData_1|(Sns_ImpData_6EE_ecu0_0_pool.bits.RML_ImpData_2<<8))
#define SetTxRML_ImpData_6EE_ecu0_0(v)                      Sns_ImpData_6EE_ecu0_0_pool.bits.RML_ImpData_1=(v);Sns_ImpData_6EE_ecu0_0_pool.bits.RML_ImpData_2=((v)>>8);
#define GetTxRL_ImpData_6EE_ecu0_0()                        (Sns_ImpData_6EE_ecu0_0_pool.bits.RL_ImpData_1|(Sns_ImpData_6EE_ecu0_0_pool.bits.RL_ImpData_2<<8))
#define SetTxRL_ImpData_6EE_ecu0_0(v)                       Sns_ImpData_6EE_ecu0_0_pool.bits.RL_ImpData_1=(v);Sns_ImpData_6EE_ecu0_0_pool.bits.RL_ImpData_2=((v)>>8);
#define GetTxRLS_ImpData_6EE_ecu0_0()                       (Sns_ImpData_6EE_ecu0_0_pool.bits.RLS_ImpData_1|(Sns_ImpData_6EE_ecu0_0_pool.bits.RLS_ImpData_2<<8))
#define SetTxRLS_ImpData_6EE_ecu0_0(v)                      Sns_ImpData_6EE_ecu0_0_pool.bits.RLS_ImpData_1=(v);Sns_ImpData_6EE_ecu0_0_pool.bits.RLS_ImpData_2=((v)>>8);
#define GetTxAK2Upper_SnsId_6EF_ecu0_0()                    (AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_SnsId)
#define SetTxAK2Upper_SnsId_6EF_ecu0_0(v)                   AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_SnsId= (v)
#define GetTxAK2Upper_LabData0_6EF_ecu0_0()                 ((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData0_1|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData0_2<<8)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData0_3<<16)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData0_4<<24)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData0_5<<32)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData0_6<<40)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData0_7<<48)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData0_8<<56))
#define SetTxAK2Upper_LabData0_6EF_ecu0_0(v)                AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData0_1=(v);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData0_2=((v)>>8);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData0_3=((v)>>16);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData0_4=((v)>>24);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData0_5=((v)>>32);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData0_6=((v)>>40);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData0_7=((v)>>48);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData0_8=((v)>>56);
#define GetTxAK2Upper_LabData1_6EF_ecu0_0()                 ((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData1_1|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData1_2<<8)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData1_3<<16)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData1_4<<24)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData1_5<<32)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData1_6<<40)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData1_7<<48)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData1_8<<56))
#define SetTxAK2Upper_LabData1_6EF_ecu0_0(v)                AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData1_1=(v);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData1_2=((v)>>8);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData1_3=((v)>>16);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData1_4=((v)>>24);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData1_5=((v)>>32);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData1_6=((v)>>40);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData1_7=((v)>>48);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData1_8=((v)>>56);
#define GetTxAK2Upper_LabData2_6EF_ecu0_0()                 ((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData2_1|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData2_2<<8)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData2_3<<16)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData2_4<<24)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData2_5<<32)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData2_6<<40)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData2_7<<48)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData2_8<<56))
#define SetTxAK2Upper_LabData2_6EF_ecu0_0(v)                AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData2_1=(v);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData2_2=((v)>>8);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData2_3=((v)>>16);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData2_4=((v)>>24);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData2_5=((v)>>32);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData2_6=((v)>>40);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData2_7=((v)>>48);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData2_8=((v)>>56);
#define GetTxAK2Upper_LabData3_6EF_ecu0_0()                 ((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData3_1|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData3_2<<8)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData3_3<<16)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData3_4<<24)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData3_5<<32)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData3_6<<40)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData3_7<<48)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData3_8<<56))
#define SetTxAK2Upper_LabData3_6EF_ecu0_0(v)                AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData3_1=(v);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData3_2=((v)>>8);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData3_3=((v)>>16);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData3_4=((v)>>24);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData3_5=((v)>>32);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData3_6=((v)>>40);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData3_7=((v)>>48);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData3_8=((v)>>56);
#define GetTxAK2Upper_LabData4_6EF_ecu0_0()                 ((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData4_1|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData4_2<<8)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData4_3<<16)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData4_4<<24)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData4_5<<32)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData4_6<<40)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData4_7<<48)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData4_8<<56))
#define SetTxAK2Upper_LabData4_6EF_ecu0_0(v)                AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData4_1=(v);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData4_2=((v)>>8);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData4_3=((v)>>16);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData4_4=((v)>>24);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData4_5=((v)>>32);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData4_6=((v)>>40);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData4_7=((v)>>48);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData4_8=((v)>>56);
#define GetTxAK2Upper_LabData5_6EF_ecu0_0()                 ((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData5_1|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData5_2<<8)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData5_3<<16)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData5_4<<24)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData5_5<<32)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData5_6<<40)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData5_7<<48)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData5_8<<56))
#define SetTxAK2Upper_LabData5_6EF_ecu0_0(v)                AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData5_1=(v);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData5_2=((v)>>8);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData5_3=((v)>>16);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData5_4=((v)>>24);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData5_5=((v)>>32);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData5_6=((v)>>40);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData5_7=((v)>>48);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData5_8=((v)>>56);
#define GetTxAK2Upper_LabData6_6EF_ecu0_0()                 ((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData6_1|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData6_2<<8)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData6_3<<16)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData6_4<<24)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData6_5<<32)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData6_6<<40)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData6_7<<48)|((uint64)AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData6_8<<56))
#define SetTxAK2Upper_LabData6_6EF_ecu0_0(v)                AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData6_1=(v);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData6_2=((v)>>8);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData6_3=((v)>>16);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData6_4=((v)>>24);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData6_5=((v)>>32);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData6_6=((v)>>40);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData6_7=((v)>>48);AK2Upper_LabData_6EF_ecu0_0_pool.bits.AK2Upper_LabData6_8=((v)>>56);
#define GetTxAK2Upper_Command_6F1_ecu0_0()                  (AK2Upper_Mcu2Pc_6F1_ecu0_0_pool.bits.AK2Upper_Command)
#define SetTxAK2Upper_Command_6F1_ecu0_0(v)                 AK2Upper_Mcu2Pc_6F1_ecu0_0_pool.bits.AK2Upper_Command= (v)
#define GetTxAK2Upper_03_ACK_6F1_ecu0_0()                   (AK2Upper_Mcu2Pc_6F1_ecu0_0_pool.bits.AK2Upper_03_ACK)
#define SetTxAK2Upper_03_ACK_6F1_ecu0_0(v)                  AK2Upper_Mcu2Pc_6F1_ecu0_0_pool.bits.AK2Upper_03_ACK= (v)
#define GetTxAK2Upper_02_Ack_6F1_ecu0_0()                   (AK2Upper_Mcu2Pc_6F1_ecu0_0_pool.bits.AK2Upper_02_Ack)
#define SetTxAK2Upper_02_Ack_6F1_ecu0_0(v)                  AK2Upper_Mcu2Pc_6F1_ecu0_0_pool.bits.AK2Upper_02_Ack= (v)
#define GetTxAK2Upper_Version_6F1_ecu0_0()                  (AK2Upper_Mcu2Pc_6F1_ecu0_0_pool.bits.AK2Upper_Version)
#define SetTxAK2Upper_Version_6F1_ecu0_0(v)                 AK2Upper_Mcu2Pc_6F1_ecu0_0_pool.bits.AK2Upper_Version= (v)
#define GetTxAK2Upper_DM_6F1_ecu0_0()                       (AK2Upper_Mcu2Pc_6F1_ecu0_0_pool.bits.AK2Upper_DM_1|(AK2Upper_Mcu2Pc_6F1_ecu0_0_pool.bits.AK2Upper_DM_2<<8))
#define SetTxAK2Upper_DM_6F1_ecu0_0(v)                      AK2Upper_Mcu2Pc_6F1_ecu0_0_pool.bits.AK2Upper_DM_1=(v);AK2Upper_Mcu2Pc_6F1_ecu0_0_pool.bits.AK2Upper_DM_2=((v)>>8);
#define GetTxFLS_Distance_6F2_ecu0_0()                      (Radar_EchoData_6F2_ecu0_0_pool.bits.FLS_Distance_1|(Radar_EchoData_6F2_ecu0_0_pool.bits.FLS_Distance_2<<8))
#define SetTxFLS_Distance_6F2_ecu0_0(v)                     Radar_EchoData_6F2_ecu0_0_pool.bits.FLS_Distance_1=(v);Radar_EchoData_6F2_ecu0_0_pool.bits.FLS_Distance_2=((v)>>8);
#define GetTxFL_Distance_6F2_ecu0_0()                       (Radar_EchoData_6F2_ecu0_0_pool.bits.FL_Distance_1|(Radar_EchoData_6F2_ecu0_0_pool.bits.FL_Distance_2<<8))
#define SetTxFL_Distance_6F2_ecu0_0(v)                      Radar_EchoData_6F2_ecu0_0_pool.bits.FL_Distance_1=(v);Radar_EchoData_6F2_ecu0_0_pool.bits.FL_Distance_2=((v)>>8);
#define GetTxFML_Distance_6F2_ecu0_0()                      (Radar_EchoData_6F2_ecu0_0_pool.bits.FML_Distance_1|(Radar_EchoData_6F2_ecu0_0_pool.bits.FML_Distance_2<<8))
#define SetTxFML_Distance_6F2_ecu0_0(v)                     Radar_EchoData_6F2_ecu0_0_pool.bits.FML_Distance_1=(v);Radar_EchoData_6F2_ecu0_0_pool.bits.FML_Distance_2=((v)>>8);
#define GetTxFMR_Distance_6F2_ecu0_0()                      (Radar_EchoData_6F2_ecu0_0_pool.bits.FMR_Distance_1|(Radar_EchoData_6F2_ecu0_0_pool.bits.FMR_Distance_2<<8))
#define SetTxFMR_Distance_6F2_ecu0_0(v)                     Radar_EchoData_6F2_ecu0_0_pool.bits.FMR_Distance_1=(v);Radar_EchoData_6F2_ecu0_0_pool.bits.FMR_Distance_2=((v)>>8);
#define GetTxFR_Distance_6F2_ecu0_0()                       (Radar_EchoData_6F2_ecu0_0_pool.bits.FR_Distance_1|(Radar_EchoData_6F2_ecu0_0_pool.bits.FR_Distance_2<<8))
#define SetTxFR_Distance_6F2_ecu0_0(v)                      Radar_EchoData_6F2_ecu0_0_pool.bits.FR_Distance_1=(v);Radar_EchoData_6F2_ecu0_0_pool.bits.FR_Distance_2=((v)>>8);
#define GetTxFRS_Distance_6F2_ecu0_0()                      (Radar_EchoData_6F2_ecu0_0_pool.bits.FRS_Distance_1|(Radar_EchoData_6F2_ecu0_0_pool.bits.FRS_Distance_2<<8))
#define SetTxFRS_Distance_6F2_ecu0_0(v)                     Radar_EchoData_6F2_ecu0_0_pool.bits.FRS_Distance_1=(v);Radar_EchoData_6F2_ecu0_0_pool.bits.FRS_Distance_2=((v)>>8);
#define GetTxRRS_Distance_6F2_ecu0_0()                      (Radar_EchoData_6F2_ecu0_0_pool.bits.RRS_Distance_1|(Radar_EchoData_6F2_ecu0_0_pool.bits.RRS_Distance_2<<8))
#define SetTxRRS_Distance_6F2_ecu0_0(v)                     Radar_EchoData_6F2_ecu0_0_pool.bits.RRS_Distance_1=(v);Radar_EchoData_6F2_ecu0_0_pool.bits.RRS_Distance_2=((v)>>8);
#define GetTxRR_Distance_6F2_ecu0_0()                       (Radar_EchoData_6F2_ecu0_0_pool.bits.RR_Distance_1|(Radar_EchoData_6F2_ecu0_0_pool.bits.RR_Distance_2<<8))
#define SetTxRR_Distance_6F2_ecu0_0(v)                      Radar_EchoData_6F2_ecu0_0_pool.bits.RR_Distance_1=(v);Radar_EchoData_6F2_ecu0_0_pool.bits.RR_Distance_2=((v)>>8);
#define GetTxRMR_Distance_6F2_ecu0_0()                      (Radar_EchoData_6F2_ecu0_0_pool.bits.RMR_Distance_1|(Radar_EchoData_6F2_ecu0_0_pool.bits.RMR_Distance_2<<8))
#define SetTxRMR_Distance_6F2_ecu0_0(v)                     Radar_EchoData_6F2_ecu0_0_pool.bits.RMR_Distance_1=(v);Radar_EchoData_6F2_ecu0_0_pool.bits.RMR_Distance_2=((v)>>8);
#define GetTxRML_Distance_6F2_ecu0_0()                      (Radar_EchoData_6F2_ecu0_0_pool.bits.RML_Distance_1|(Radar_EchoData_6F2_ecu0_0_pool.bits.RML_Distance_2<<8))
#define SetTxRML_Distance_6F2_ecu0_0(v)                     Radar_EchoData_6F2_ecu0_0_pool.bits.RML_Distance_1=(v);Radar_EchoData_6F2_ecu0_0_pool.bits.RML_Distance_2=((v)>>8);
#define GetTxRL_Distance_6F2_ecu0_0()                       (Radar_EchoData_6F2_ecu0_0_pool.bits.RL_Distance_1|(Radar_EchoData_6F2_ecu0_0_pool.bits.RL_Distance_2<<8))
#define SetTxRL_Distance_6F2_ecu0_0(v)                      Radar_EchoData_6F2_ecu0_0_pool.bits.RL_Distance_1=(v);Radar_EchoData_6F2_ecu0_0_pool.bits.RL_Distance_2=((v)>>8);
#define GetTxRLS_Distance_6F2_ecu0_0()                      (Radar_EchoData_6F2_ecu0_0_pool.bits.RLS_Distance_1|(Radar_EchoData_6F2_ecu0_0_pool.bits.RLS_Distance_2<<8))
#define SetTxRLS_Distance_6F2_ecu0_0(v)                     Radar_EchoData_6F2_ecu0_0_pool.bits.RLS_Distance_1=(v);Radar_EchoData_6F2_ecu0_0_pool.bits.RLS_Distance_2=((v)>>8);
#define GetTxFMR_HeightType_6F2_ecu0_0()                    (Radar_EchoData_6F2_ecu0_0_pool.bits.FMR_HeightType)
#define SetTxFMR_HeightType_6F2_ecu0_0(v)                   Radar_EchoData_6F2_ecu0_0_pool.bits.FMR_HeightType= (v)
#define GetTxFML_HeightType_6F2_ecu0_0()                    (Radar_EchoData_6F2_ecu0_0_pool.bits.FML_HeightType)
#define SetTxFML_HeightType_6F2_ecu0_0(v)                   Radar_EchoData_6F2_ecu0_0_pool.bits.FML_HeightType= (v)
#define GetTxFL_HeightType_6F2_ecu0_0()                     (Radar_EchoData_6F2_ecu0_0_pool.bits.FL_HeightType)
#define SetTxFL_HeightType_6F2_ecu0_0(v)                    Radar_EchoData_6F2_ecu0_0_pool.bits.FL_HeightType= (v)
#define GetTxFLS_HeightType_6F2_ecu0_0()                    (Radar_EchoData_6F2_ecu0_0_pool.bits.FLS_HeightType)
#define SetTxFLS_HeightType_6F2_ecu0_0(v)                   Radar_EchoData_6F2_ecu0_0_pool.bits.FLS_HeightType= (v)
#define GetTxRR_HeightType_6F2_ecu0_0()                     (Radar_EchoData_6F2_ecu0_0_pool.bits.RR_HeightType)
#define SetTxRR_HeightType_6F2_ecu0_0(v)                    Radar_EchoData_6F2_ecu0_0_pool.bits.RR_HeightType= (v)
#define GetTxRRS_HeightType_6F2_ecu0_0()                    (Radar_EchoData_6F2_ecu0_0_pool.bits.RRS_HeightType)
#define SetTxRRS_HeightType_6F2_ecu0_0(v)                   Radar_EchoData_6F2_ecu0_0_pool.bits.RRS_HeightType= (v)
#define GetTxFRS_HeightType_6F2_ecu0_0()                    (Radar_EchoData_6F2_ecu0_0_pool.bits.FRS_HeightType)
#define SetTxFRS_HeightType_6F2_ecu0_0(v)                   Radar_EchoData_6F2_ecu0_0_pool.bits.FRS_HeightType= (v)
#define GetTxFR_HeightType_6F2_ecu0_0()                     (Radar_EchoData_6F2_ecu0_0_pool.bits.FR_HeightType)
#define SetTxFR_HeightType_6F2_ecu0_0(v)                    Radar_EchoData_6F2_ecu0_0_pool.bits.FR_HeightType= (v)
#define GetTxRLS_HeightType_6F2_ecu0_0()                    (Radar_EchoData_6F2_ecu0_0_pool.bits.RLS_HeightType)
#define SetTxRLS_HeightType_6F2_ecu0_0(v)                   Radar_EchoData_6F2_ecu0_0_pool.bits.RLS_HeightType= (v)
#define GetTxRL_HeightType_6F2_ecu0_0()                     (Radar_EchoData_6F2_ecu0_0_pool.bits.RL_HeightType)
#define SetTxRL_HeightType_6F2_ecu0_0(v)                    Radar_EchoData_6F2_ecu0_0_pool.bits.RL_HeightType= (v)
#define GetTxRML_HeightType_6F2_ecu0_0()                    (Radar_EchoData_6F2_ecu0_0_pool.bits.RML_HeightType)
#define SetTxRML_HeightType_6F2_ecu0_0(v)                   Radar_EchoData_6F2_ecu0_0_pool.bits.RML_HeightType= (v)
#define GetTxRMR_HeightType_6F2_ecu0_0()                    (Radar_EchoData_6F2_ecu0_0_pool.bits.RMR_HeightType)
#define SetTxRMR_HeightType_6F2_ecu0_0(v)                   Radar_EchoData_6F2_ecu0_0_pool.bits.RMR_HeightType= (v)
#define GetTxCar_Odo_X_6F3_ecu0_0()                         (VehicelInf_6F3_ecu0_0_pool.bits.Car_Odo_X_1|(VehicelInf_6F3_ecu0_0_pool.bits.Car_Odo_X_2<<8)|(VehicelInf_6F3_ecu0_0_pool.bits.Car_Odo_X_3<<16)|(VehicelInf_6F3_ecu0_0_pool.bits.Car_Odo_X_4<<24))
#define SetTxCar_Odo_X_6F3_ecu0_0(v)                        VehicelInf_6F3_ecu0_0_pool.bits.Car_Odo_X_1=(v);VehicelInf_6F3_ecu0_0_pool.bits.Car_Odo_X_2=((v)>>8);VehicelInf_6F3_ecu0_0_pool.bits.Car_Odo_X_3=((v)>>16);VehicelInf_6F3_ecu0_0_pool.bits.Car_Odo_X_4=((v)>>24);
#define GetTxCar_Odo_Y_6F3_ecu0_0()                         (VehicelInf_6F3_ecu0_0_pool.bits.Car_Odo_Y_1|(VehicelInf_6F3_ecu0_0_pool.bits.Car_Odo_Y_2<<8)|(VehicelInf_6F3_ecu0_0_pool.bits.Car_Odo_Y_3<<16)|(VehicelInf_6F3_ecu0_0_pool.bits.Car_Odo_Y_4<<24))
#define SetTxCar_Odo_Y_6F3_ecu0_0(v)                        VehicelInf_6F3_ecu0_0_pool.bits.Car_Odo_Y_1=(v);VehicelInf_6F3_ecu0_0_pool.bits.Car_Odo_Y_2=((v)>>8);VehicelInf_6F3_ecu0_0_pool.bits.Car_Odo_Y_3=((v)>>16);VehicelInf_6F3_ecu0_0_pool.bits.Car_Odo_Y_4=((v)>>24);
#define GetTxCar_Odo_YawAngle_6F3_ecu0_0()                  (VehicelInf_6F3_ecu0_0_pool.bits.Car_Odo_YawAngle_1|(VehicelInf_6F3_ecu0_0_pool.bits.Car_Odo_YawAngle_2<<8))
#define SetTxCar_Odo_YawAngle_6F3_ecu0_0(v)                 VehicelInf_6F3_ecu0_0_pool.bits.Car_Odo_YawAngle_1=(v);VehicelInf_6F3_ecu0_0_pool.bits.Car_Odo_YawAngle_2=((v)>>8);
#define GetTxVehicle_Type_6F3_ecu0_0()                      (VehicelInf_6F3_ecu0_0_pool.bits.Vehicle_Type)
#define SetTxVehicle_Type_6F3_ecu0_0(v)                     VehicelInf_6F3_ecu0_0_pool.bits.Vehicle_Type= (v)
#define GetTxVehicle_Cfg_6F3_ecu0_0()                       (VehicelInf_6F3_ecu0_0_pool.bits.Vehicle_Cfg)
#define SetTxVehicle_Cfg_6F3_ecu0_0(v)                      VehicelInf_6F3_ecu0_0_pool.bits.Vehicle_Cfg= (v)
#define GetTxVehicle_Platform_6F3_ecu0_0()                  (VehicelInf_6F3_ecu0_0_pool.bits.Vehicle_Platform)
#define SetTxVehicle_Platform_6F3_ecu0_0(v)                 VehicelInf_6F3_ecu0_0_pool.bits.Vehicle_Platform= (v)
#define GetTxCar_Dirve_Dis_6F3_ecu0_0()                     (VehicelInf_6F3_ecu0_0_pool.bits.Car_Dirve_Dis_1|(VehicelInf_6F3_ecu0_0_pool.bits.Car_Dirve_Dis_2<<8)|(VehicelInf_6F3_ecu0_0_pool.bits.Car_Dirve_Dis_3<<16)|(VehicelInf_6F3_ecu0_0_pool.bits.Car_Dirve_Dis_4<<24))
#define SetTxCar_Dirve_Dis_6F3_ecu0_0(v)                    VehicelInf_6F3_ecu0_0_pool.bits.Car_Dirve_Dis_1=(v);VehicelInf_6F3_ecu0_0_pool.bits.Car_Dirve_Dis_2=((v)>>8);VehicelInf_6F3_ecu0_0_pool.bits.Car_Dirve_Dis_3=((v)>>16);VehicelInf_6F3_ecu0_0_pool.bits.Car_Dirve_Dis_4=((v)>>24);
#define GetTxUpdateFlg_6F4_ecu0_0()                         (FLS_EchoData_6F4_ecu0_0_pool.bits.UpdateFlg)
#define SetTxUpdateFlg_6F4_ecu0_0(v)                        FLS_EchoData_6F4_ecu0_0_pool.bits.UpdateFlg= (v)
#define GetTxSnsWorkSts_6F4_ecu0_0()                        (FLS_EchoData_6F4_ecu0_0_pool.bits.SnsWorkSts)
#define SetTxSnsWorkSts_6F4_ecu0_0(v)                       FLS_EchoData_6F4_ecu0_0_pool.bits.SnsWorkSts= (v)
#define GetTxMaster_Meas_Type_6F4_ecu0_0()                  (FLS_EchoData_6F4_ecu0_0_pool.bits.Master_Meas_Type)
#define SetTxMaster_Meas_Type_6F4_ecu0_0(v)                 FLS_EchoData_6F4_ecu0_0_pool.bits.Master_Meas_Type= (v)
#define GetTxEepromErrFlg_6F4_ecu0_0()                      (FLS_EchoData_6F4_ecu0_0_pool.bits.EepromErrFlg)
#define SetTxEepromErrFlg_6F4_ecu0_0(v)                     FLS_EchoData_6F4_ecu0_0_pool.bits.EepromErrFlg= (v)
#define GetTxMaster_Rtm_6F4_ecu0_0()                        (FLS_EchoData_6F4_ecu0_0_pool.bits.Master_Rtm_1|(FLS_EchoData_6F4_ecu0_0_pool.bits.Master_Rtm_2<<8))
#define SetTxMaster_Rtm_6F4_ecu0_0(v)                       FLS_EchoData_6F4_ecu0_0_pool.bits.Master_Rtm_1=(v);FLS_EchoData_6F4_ecu0_0_pool.bits.Master_Rtm_2=((v)>>8);
#define GetTxMaster_Distance_0_6F4_ecu0_0()                 (FLS_EchoData_6F4_ecu0_0_pool.bits.Master_Distance_0_1|(FLS_EchoData_6F4_ecu0_0_pool.bits.Master_Distance_0_2<<8))
#define SetTxMaster_Distance_0_6F4_ecu0_0(v)                FLS_EchoData_6F4_ecu0_0_pool.bits.Master_Distance_0_1=(v);FLS_EchoData_6F4_ecu0_0_pool.bits.Master_Distance_0_2=((v)>>8);
#define GetTxMaster_Distance_1_6F4_ecu0_0()                 (FLS_EchoData_6F4_ecu0_0_pool.bits.Master_Distance_1_1|(FLS_EchoData_6F4_ecu0_0_pool.bits.Master_Distance_1_2<<8))
#define SetTxMaster_Distance_1_6F4_ecu0_0(v)                FLS_EchoData_6F4_ecu0_0_pool.bits.Master_Distance_1_1=(v);FLS_EchoData_6F4_ecu0_0_pool.bits.Master_Distance_1_2=((v)>>8);
#define GetTxMaster_Distance_2_6F4_ecu0_0()                 (FLS_EchoData_6F4_ecu0_0_pool.bits.Master_Distance_2_1|(FLS_EchoData_6F4_ecu0_0_pool.bits.Master_Distance_2_2<<8))
#define SetTxMaster_Distance_2_6F4_ecu0_0(v)                FLS_EchoData_6F4_ecu0_0_pool.bits.Master_Distance_2_1=(v);FLS_EchoData_6F4_ecu0_0_pool.bits.Master_Distance_2_2=((v)>>8);
#define GetTxMaster_Distance_3_6F4_ecu0_0()                 (FLS_EchoData_6F4_ecu0_0_pool.bits.Master_Distance_3_1|(FLS_EchoData_6F4_ecu0_0_pool.bits.Master_Distance_3_2<<8))
#define SetTxMaster_Distance_3_6F4_ecu0_0(v)                FLS_EchoData_6F4_ecu0_0_pool.bits.Master_Distance_3_1=(v);FLS_EchoData_6F4_ecu0_0_pool.bits.Master_Distance_3_2=((v)>>8);
#define GetTxMaster_EchoHigh_0_6F4_ecu0_0()                 (FLS_EchoData_6F4_ecu0_0_pool.bits.Master_EchoHigh_0_1|(FLS_EchoData_6F4_ecu0_0_pool.bits.Master_EchoHigh_0_2<<8))
#define SetTxMaster_EchoHigh_0_6F4_ecu0_0(v)                FLS_EchoData_6F4_ecu0_0_pool.bits.Master_EchoHigh_0_1=(v);FLS_EchoData_6F4_ecu0_0_pool.bits.Master_EchoHigh_0_2=((v)>>8);
#define GetTxMaster_EchoHigh_1_6F4_ecu0_0()                 (FLS_EchoData_6F4_ecu0_0_pool.bits.Master_EchoHigh_1_1|(FLS_EchoData_6F4_ecu0_0_pool.bits.Master_EchoHigh_1_2<<8))
#define SetTxMaster_EchoHigh_1_6F4_ecu0_0(v)                FLS_EchoData_6F4_ecu0_0_pool.bits.Master_EchoHigh_1_1=(v);FLS_EchoData_6F4_ecu0_0_pool.bits.Master_EchoHigh_1_2=((v)>>8);
#define GetTxMaster_EchoHigh_2_6F4_ecu0_0()                 (FLS_EchoData_6F4_ecu0_0_pool.bits.Master_EchoHigh_2_1|(FLS_EchoData_6F4_ecu0_0_pool.bits.Master_EchoHigh_2_2<<8))
#define SetTxMaster_EchoHigh_2_6F4_ecu0_0(v)                FLS_EchoData_6F4_ecu0_0_pool.bits.Master_EchoHigh_2_1=(v);FLS_EchoData_6F4_ecu0_0_pool.bits.Master_EchoHigh_2_2=((v)>>8);
#define GetTxMaster_EchoHigh_3_6F4_ecu0_0()                 (FLS_EchoData_6F4_ecu0_0_pool.bits.Master_EchoHigh_3_1|(FLS_EchoData_6F4_ecu0_0_pool.bits.Master_EchoHigh_3_2<<8))
#define SetTxMaster_EchoHigh_3_6F4_ecu0_0(v)                FLS_EchoData_6F4_ecu0_0_pool.bits.Master_EchoHigh_3_1=(v);FLS_EchoData_6F4_ecu0_0_pool.bits.Master_EchoHigh_3_2=((v)>>8);
#define GetTxLeftListen_Distance_0_6F4_ecu0_0()             (FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_Distance_0_1|(FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_Distance_0_2<<8))
#define SetTxLeftListen_Distance_0_6F4_ecu0_0(v)            FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_Distance_0_1=(v);FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_Distance_0_2=((v)>>8);
#define GetTxLeftListen_Distance_1_6F4_ecu0_0()             (FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_Distance_1_1|(FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_Distance_1_2<<8))
#define SetTxLeftListen_Distance_1_6F4_ecu0_0(v)            FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_Distance_1_1=(v);FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_Distance_1_2=((v)>>8);
#define GetTxLeftListen_Distance_2_6F4_ecu0_0()             (FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_Distance_2_1|(FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_Distance_2_2<<8))
#define SetTxLeftListen_Distance_2_6F4_ecu0_0(v)            FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_Distance_2_1=(v);FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_Distance_2_2=((v)>>8);
#define GetTxLeftListen_Distance_3_6F4_ecu0_0()             (FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_Distance_3_1|(FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_Distance_3_2<<8))
#define SetTxLeftListen_Distance_3_6F4_ecu0_0(v)            FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_Distance_3_1=(v);FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_Distance_3_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_0_6F4_ecu0_0()             (FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_EchoHigh_0_1|(FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_EchoHigh_0_2<<8))
#define SetTxLeftListen_EchoHigh_0_6F4_ecu0_0(v)            FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_EchoHigh_0_1=(v);FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_EchoHigh_0_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_1_6F4_ecu0_0()             (FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_EchoHigh_1_1|(FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_EchoHigh_1_2<<8))
#define SetTxLeftListen_EchoHigh_1_6F4_ecu0_0(v)            FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_EchoHigh_1_1=(v);FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_EchoHigh_1_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_2_6F4_ecu0_0()             (FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_EchoHigh_2_1|(FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_EchoHigh_2_2<<8))
#define SetTxLeftListen_EchoHigh_2_6F4_ecu0_0(v)            FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_EchoHigh_2_1=(v);FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_EchoHigh_2_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_3_6F4_ecu0_0()             (FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_EchoHigh_3_1|(FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_EchoHigh_3_2<<8))
#define SetTxLeftListen_EchoHigh_3_6F4_ecu0_0(v)            FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_EchoHigh_3_1=(v);FLS_EchoData_6F4_ecu0_0_pool.bits.LeftListen_EchoHigh_3_2=((v)>>8);
#define GetTxRightListen_Distance_0_6F4_ecu0_0()            (FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_Distance_0_1|(FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_Distance_0_2<<8))
#define SetTxRightListen_Distance_0_6F4_ecu0_0(v)           FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_Distance_0_1=(v);FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_Distance_0_2=((v)>>8);
#define GetTxRightListen_Distance_1_6F4_ecu0_0()            (FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_Distance_1_1|(FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_Distance_1_2<<8))
#define SetTxRightListen_Distance_1_6F4_ecu0_0(v)           FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_Distance_1_1=(v);FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_Distance_1_2=((v)>>8);
#define GetTxRightListen_Distance_2_6F4_ecu0_0()            (FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_Distance_2_1|(FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_Distance_2_2<<8))
#define SetTxRightListen_Distance_2_6F4_ecu0_0(v)           FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_Distance_2_1=(v);FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_Distance_2_2=((v)>>8);
#define GetTxRightListen_Distance_3_6F4_ecu0_0()            (FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_Distance_3_1|(FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_Distance_3_2<<8))
#define SetTxRightListen_Distance_3_6F4_ecu0_0(v)           FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_Distance_3_1=(v);FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_Distance_3_2=((v)>>8);
#define GetTxRightListen_EchoHigh_0_6F4_ecu0_0()            (FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_EchoHigh_0_1|(FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_EchoHigh_0_2<<8))
#define SetTxRightListen_EchoHigh_0_6F4_ecu0_0(v)           FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_EchoHigh_0_1=(v);FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_EchoHigh_0_2=((v)>>8);
#define GetTxRightListen_EchoHigh_1_6F4_ecu0_0()            (FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_EchoHigh_1_1|(FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_EchoHigh_1_2<<8))
#define SetTxRightListen_EchoHigh_1_6F4_ecu0_0(v)           FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_EchoHigh_1_1=(v);FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_EchoHigh_1_2=((v)>>8);
#define GetTxRightListen_EchoHigh_2_6F4_ecu0_0()            (FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_EchoHigh_2_1|(FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_EchoHigh_2_2<<8))
#define SetTxRightListen_EchoHigh_2_6F4_ecu0_0(v)           FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_EchoHigh_2_1=(v);FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_EchoHigh_2_2=((v)>>8);
#define GetTxRightListen_EchoHigh_3_6F4_ecu0_0()            (FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_EchoHigh_3_1|(FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_EchoHigh_3_2<<8))
#define SetTxRightListen_EchoHigh_3_6F4_ecu0_0(v)           FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_EchoHigh_3_1=(v);FLS_EchoData_6F4_ecu0_0_pool.bits.RightListen_EchoHigh_3_2=((v)>>8);
#define GetTxMasterEchoCnt_6F4_ecu0_0()                     (FLS_EchoData_6F4_ecu0_0_pool.bits.MasterEchoCnt)
#define SetTxMasterEchoCnt_6F4_ecu0_0(v)                    FLS_EchoData_6F4_ecu0_0_pool.bits.MasterEchoCnt= (v)
#define GetTxLeftEchoCnt_6F4_ecu0_0()                       (FLS_EchoData_6F4_ecu0_0_pool.bits.LeftEchoCnt)
#define SetTxLeftEchoCnt_6F4_ecu0_0(v)                      FLS_EchoData_6F4_ecu0_0_pool.bits.LeftEchoCnt= (v)
#define GetTxRightEchoCnt_6F4_ecu0_0()                      (FLS_EchoData_6F4_ecu0_0_pool.bits.RightEchoCnt)
#define SetTxRightEchoCnt_6F4_ecu0_0(v)                     FLS_EchoData_6F4_ecu0_0_pool.bits.RightEchoCnt= (v)
#define GetTxNFD_Flag_6F4_ecu0_0()                          (FLS_EchoData_6F4_ecu0_0_pool.bits.NFD_Flag)
#define SetTxNFD_Flag_6F4_ecu0_0(v)                         FLS_EchoData_6F4_ecu0_0_pool.bits.NFD_Flag= (v)
#define GetTxNFD_Dis_6F4_ecu0_0()                           (FLS_EchoData_6F4_ecu0_0_pool.bits.NFD_Dis_1|(FLS_EchoData_6F4_ecu0_0_pool.bits.NFD_Dis_2<<8))
#define SetTxNFD_Dis_6F4_ecu0_0(v)                          FLS_EchoData_6F4_ecu0_0_pool.bits.NFD_Dis_1=(v);FLS_EchoData_6F4_ecu0_0_pool.bits.NFD_Dis_2=((v)>>8);
#define GetTxRing_Fre_6F4_ecu0_0()                          (FLS_EchoData_6F4_ecu0_0_pool.bits.Ring_Fre_1|(FLS_EchoData_6F4_ecu0_0_pool.bits.Ring_Fre_2<<8))
#define SetTxRing_Fre_6F4_ecu0_0(v)                         FLS_EchoData_6F4_ecu0_0_pool.bits.Ring_Fre_1=(v);FLS_EchoData_6F4_ecu0_0_pool.bits.Ring_Fre_2=((v)>>8);
#define GetTxNoiseCnt_6F4_ecu0_0()                          (FLS_EchoData_6F4_ecu0_0_pool.bits.NoiseCnt)
#define SetTxNoiseCnt_6F4_ecu0_0(v)                         FLS_EchoData_6F4_ecu0_0_pool.bits.NoiseCnt= (v)
#define GetTxNoiseSum_6F4_ecu0_0()                          (FLS_EchoData_6F4_ecu0_0_pool.bits.NoiseSum_1|(FLS_EchoData_6F4_ecu0_0_pool.bits.NoiseSum_2<<4))
#define SetTxNoiseSum_6F4_ecu0_0(v)                         FLS_EchoData_6F4_ecu0_0_pool.bits.NoiseSum_1=(v);FLS_EchoData_6F4_ecu0_0_pool.bits.NoiseSum_2=((v)>>4);
#define GetTxUpdateFlg_6F5_ecu0_0()                         (FL_EchoData_6F5_ecu0_0_pool.bits.UpdateFlg)
#define SetTxUpdateFlg_6F5_ecu0_0(v)                        FL_EchoData_6F5_ecu0_0_pool.bits.UpdateFlg= (v)
#define GetTxSnsWorkSts_6F5_ecu0_0()                        (FL_EchoData_6F5_ecu0_0_pool.bits.SnsWorkSts)
#define SetTxSnsWorkSts_6F5_ecu0_0(v)                       FL_EchoData_6F5_ecu0_0_pool.bits.SnsWorkSts= (v)
#define GetTxMaster_Meas_Type_6F5_ecu0_0()                  (FL_EchoData_6F5_ecu0_0_pool.bits.Master_Meas_Type)
#define SetTxMaster_Meas_Type_6F5_ecu0_0(v)                 FL_EchoData_6F5_ecu0_0_pool.bits.Master_Meas_Type= (v)
#define GetTxEepromErrFlg_6F5_ecu0_0()                      (FL_EchoData_6F5_ecu0_0_pool.bits.EepromErrFlg)
#define SetTxEepromErrFlg_6F5_ecu0_0(v)                     FL_EchoData_6F5_ecu0_0_pool.bits.EepromErrFlg= (v)
#define GetTxMaster_Rtm_6F5_ecu0_0()                        (FL_EchoData_6F5_ecu0_0_pool.bits.Master_Rtm_1|(FL_EchoData_6F5_ecu0_0_pool.bits.Master_Rtm_2<<8))
#define SetTxMaster_Rtm_6F5_ecu0_0(v)                       FL_EchoData_6F5_ecu0_0_pool.bits.Master_Rtm_1=(v);FL_EchoData_6F5_ecu0_0_pool.bits.Master_Rtm_2=((v)>>8);
#define GetTxMaster_Distance_0_6F5_ecu0_0()                 (FL_EchoData_6F5_ecu0_0_pool.bits.Master_Distance_0_1|(FL_EchoData_6F5_ecu0_0_pool.bits.Master_Distance_0_2<<8))
#define SetTxMaster_Distance_0_6F5_ecu0_0(v)                FL_EchoData_6F5_ecu0_0_pool.bits.Master_Distance_0_1=(v);FL_EchoData_6F5_ecu0_0_pool.bits.Master_Distance_0_2=((v)>>8);
#define GetTxMaster_Distance_1_6F5_ecu0_0()                 (FL_EchoData_6F5_ecu0_0_pool.bits.Master_Distance_1_1|(FL_EchoData_6F5_ecu0_0_pool.bits.Master_Distance_1_2<<8))
#define SetTxMaster_Distance_1_6F5_ecu0_0(v)                FL_EchoData_6F5_ecu0_0_pool.bits.Master_Distance_1_1=(v);FL_EchoData_6F5_ecu0_0_pool.bits.Master_Distance_1_2=((v)>>8);
#define GetTxMaster_Distance_2_6F5_ecu0_0()                 (FL_EchoData_6F5_ecu0_0_pool.bits.Master_Distance_2_1|(FL_EchoData_6F5_ecu0_0_pool.bits.Master_Distance_2_2<<8))
#define SetTxMaster_Distance_2_6F5_ecu0_0(v)                FL_EchoData_6F5_ecu0_0_pool.bits.Master_Distance_2_1=(v);FL_EchoData_6F5_ecu0_0_pool.bits.Master_Distance_2_2=((v)>>8);
#define GetTxMaster_Distance_3_6F5_ecu0_0()                 (FL_EchoData_6F5_ecu0_0_pool.bits.Master_Distance_3_1|(FL_EchoData_6F5_ecu0_0_pool.bits.Master_Distance_3_2<<8))
#define SetTxMaster_Distance_3_6F5_ecu0_0(v)                FL_EchoData_6F5_ecu0_0_pool.bits.Master_Distance_3_1=(v);FL_EchoData_6F5_ecu0_0_pool.bits.Master_Distance_3_2=((v)>>8);
#define GetTxMaster_EchoHigh_0_6F5_ecu0_0()                 (FL_EchoData_6F5_ecu0_0_pool.bits.Master_EchoHigh_0_1|(FL_EchoData_6F5_ecu0_0_pool.bits.Master_EchoHigh_0_2<<8))
#define SetTxMaster_EchoHigh_0_6F5_ecu0_0(v)                FL_EchoData_6F5_ecu0_0_pool.bits.Master_EchoHigh_0_1=(v);FL_EchoData_6F5_ecu0_0_pool.bits.Master_EchoHigh_0_2=((v)>>8);
#define GetTxMaster_EchoHigh_1_6F5_ecu0_0()                 (FL_EchoData_6F5_ecu0_0_pool.bits.Master_EchoHigh_1_1|(FL_EchoData_6F5_ecu0_0_pool.bits.Master_EchoHigh_1_2<<8))
#define SetTxMaster_EchoHigh_1_6F5_ecu0_0(v)                FL_EchoData_6F5_ecu0_0_pool.bits.Master_EchoHigh_1_1=(v);FL_EchoData_6F5_ecu0_0_pool.bits.Master_EchoHigh_1_2=((v)>>8);
#define GetTxMaster_EchoHigh_2_6F5_ecu0_0()                 (FL_EchoData_6F5_ecu0_0_pool.bits.Master_EchoHigh_2_1|(FL_EchoData_6F5_ecu0_0_pool.bits.Master_EchoHigh_2_2<<8))
#define SetTxMaster_EchoHigh_2_6F5_ecu0_0(v)                FL_EchoData_6F5_ecu0_0_pool.bits.Master_EchoHigh_2_1=(v);FL_EchoData_6F5_ecu0_0_pool.bits.Master_EchoHigh_2_2=((v)>>8);
#define GetTxMaster_EchoHigh_3_6F5_ecu0_0()                 (FL_EchoData_6F5_ecu0_0_pool.bits.Master_EchoHigh_3_1|(FL_EchoData_6F5_ecu0_0_pool.bits.Master_EchoHigh_3_2<<8))
#define SetTxMaster_EchoHigh_3_6F5_ecu0_0(v)                FL_EchoData_6F5_ecu0_0_pool.bits.Master_EchoHigh_3_1=(v);FL_EchoData_6F5_ecu0_0_pool.bits.Master_EchoHigh_3_2=((v)>>8);
#define GetTxLeftListen_Distance_0_6F5_ecu0_0()             (FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_Distance_0_1|(FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_Distance_0_2<<8))
#define SetTxLeftListen_Distance_0_6F5_ecu0_0(v)            FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_Distance_0_1=(v);FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_Distance_0_2=((v)>>8);
#define GetTxLeftListen_Distance_1_6F5_ecu0_0()             (FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_Distance_1_1|(FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_Distance_1_2<<8))
#define SetTxLeftListen_Distance_1_6F5_ecu0_0(v)            FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_Distance_1_1=(v);FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_Distance_1_2=((v)>>8);
#define GetTxLeftListen_Distance_2_6F5_ecu0_0()             (FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_Distance_2_1|(FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_Distance_2_2<<8))
#define SetTxLeftListen_Distance_2_6F5_ecu0_0(v)            FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_Distance_2_1=(v);FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_Distance_2_2=((v)>>8);
#define GetTxLeftListen_Distance_3_6F5_ecu0_0()             (FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_Distance_3_1|(FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_Distance_3_2<<8))
#define SetTxLeftListen_Distance_3_6F5_ecu0_0(v)            FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_Distance_3_1=(v);FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_Distance_3_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_0_6F5_ecu0_0()             (FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_EchoHigh_0_1|(FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_EchoHigh_0_2<<8))
#define SetTxLeftListen_EchoHigh_0_6F5_ecu0_0(v)            FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_EchoHigh_0_1=(v);FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_EchoHigh_0_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_1_6F5_ecu0_0()             (FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_EchoHigh_1_1|(FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_EchoHigh_1_2<<8))
#define SetTxLeftListen_EchoHigh_1_6F5_ecu0_0(v)            FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_EchoHigh_1_1=(v);FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_EchoHigh_1_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_2_6F5_ecu0_0()             (FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_EchoHigh_2_1|(FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_EchoHigh_2_2<<8))
#define SetTxLeftListen_EchoHigh_2_6F5_ecu0_0(v)            FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_EchoHigh_2_1=(v);FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_EchoHigh_2_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_3_6F5_ecu0_0()             (FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_EchoHigh_3_1|(FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_EchoHigh_3_2<<8))
#define SetTxLeftListen_EchoHigh_3_6F5_ecu0_0(v)            FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_EchoHigh_3_1=(v);FL_EchoData_6F5_ecu0_0_pool.bits.LeftListen_EchoHigh_3_2=((v)>>8);
#define GetTxRightListen_Distance_0_6F5_ecu0_0()            (FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_Distance_0_1|(FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_Distance_0_2<<8))
#define SetTxRightListen_Distance_0_6F5_ecu0_0(v)           FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_Distance_0_1=(v);FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_Distance_0_2=((v)>>8);
#define GetTxRightListen_Distance_1_6F5_ecu0_0()            (FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_Distance_1_1|(FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_Distance_1_2<<8))
#define SetTxRightListen_Distance_1_6F5_ecu0_0(v)           FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_Distance_1_1=(v);FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_Distance_1_2=((v)>>8);
#define GetTxRightListen_Distance_2_6F5_ecu0_0()            (FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_Distance_2_1|(FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_Distance_2_2<<8))
#define SetTxRightListen_Distance_2_6F5_ecu0_0(v)           FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_Distance_2_1=(v);FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_Distance_2_2=((v)>>8);
#define GetTxRightListen_Distance_3_6F5_ecu0_0()            (FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_Distance_3_1|(FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_Distance_3_2<<8))
#define SetTxRightListen_Distance_3_6F5_ecu0_0(v)           FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_Distance_3_1=(v);FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_Distance_3_2=((v)>>8);
#define GetTxRightListen_EchoHigh_0_6F5_ecu0_0()            (FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_EchoHigh_0_1|(FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_EchoHigh_0_2<<8))
#define SetTxRightListen_EchoHigh_0_6F5_ecu0_0(v)           FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_EchoHigh_0_1=(v);FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_EchoHigh_0_2=((v)>>8);
#define GetTxRightListen_EchoHigh_1_6F5_ecu0_0()            (FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_EchoHigh_1_1|(FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_EchoHigh_1_2<<8))
#define SetTxRightListen_EchoHigh_1_6F5_ecu0_0(v)           FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_EchoHigh_1_1=(v);FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_EchoHigh_1_2=((v)>>8);
#define GetTxRightListen_EchoHigh_2_6F5_ecu0_0()            (FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_EchoHigh_2_1|(FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_EchoHigh_2_2<<8))
#define SetTxRightListen_EchoHigh_2_6F5_ecu0_0(v)           FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_EchoHigh_2_1=(v);FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_EchoHigh_2_2=((v)>>8);
#define GetTxRightListen_EchoHigh_3_6F5_ecu0_0()            (FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_EchoHigh_3_1|(FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_EchoHigh_3_2<<8))
#define SetTxRightListen_EchoHigh_3_6F5_ecu0_0(v)           FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_EchoHigh_3_1=(v);FL_EchoData_6F5_ecu0_0_pool.bits.RightListen_EchoHigh_3_2=((v)>>8);
#define GetTxMasterEchoCnt_6F5_ecu0_0()                     (FL_EchoData_6F5_ecu0_0_pool.bits.MasterEchoCnt)
#define SetTxMasterEchoCnt_6F5_ecu0_0(v)                    FL_EchoData_6F5_ecu0_0_pool.bits.MasterEchoCnt= (v)
#define GetTxLeftEchoCnt_6F5_ecu0_0()                       (FL_EchoData_6F5_ecu0_0_pool.bits.LeftEchoCnt)
#define SetTxLeftEchoCnt_6F5_ecu0_0(v)                      FL_EchoData_6F5_ecu0_0_pool.bits.LeftEchoCnt= (v)
#define GetTxRightEchoCnt_6F5_ecu0_0()                      (FL_EchoData_6F5_ecu0_0_pool.bits.RightEchoCnt)
#define SetTxRightEchoCnt_6F5_ecu0_0(v)                     FL_EchoData_6F5_ecu0_0_pool.bits.RightEchoCnt= (v)
#define GetTxNFD_Flag_6F5_ecu0_0()                          (FL_EchoData_6F5_ecu0_0_pool.bits.NFD_Flag)
#define SetTxNFD_Flag_6F5_ecu0_0(v)                         FL_EchoData_6F5_ecu0_0_pool.bits.NFD_Flag= (v)
#define GetTxNFD_Dis_6F5_ecu0_0()                           (FL_EchoData_6F5_ecu0_0_pool.bits.NFD_Dis_1|(FL_EchoData_6F5_ecu0_0_pool.bits.NFD_Dis_2<<8))
#define SetTxNFD_Dis_6F5_ecu0_0(v)                          FL_EchoData_6F5_ecu0_0_pool.bits.NFD_Dis_1=(v);FL_EchoData_6F5_ecu0_0_pool.bits.NFD_Dis_2=((v)>>8);
#define GetTxRing_Fre_6F5_ecu0_0()                          (FL_EchoData_6F5_ecu0_0_pool.bits.Ring_Fre_1|(FL_EchoData_6F5_ecu0_0_pool.bits.Ring_Fre_2<<8))
#define SetTxRing_Fre_6F5_ecu0_0(v)                         FL_EchoData_6F5_ecu0_0_pool.bits.Ring_Fre_1=(v);FL_EchoData_6F5_ecu0_0_pool.bits.Ring_Fre_2=((v)>>8);
#define GetTxNoiseCnt_6F5_ecu0_0()                          (FL_EchoData_6F5_ecu0_0_pool.bits.NoiseCnt)
#define SetTxNoiseCnt_6F5_ecu0_0(v)                         FL_EchoData_6F5_ecu0_0_pool.bits.NoiseCnt= (v)
#define GetTxNoiseSum_6F5_ecu0_0()                          (FL_EchoData_6F5_ecu0_0_pool.bits.NoiseSum_1|(FL_EchoData_6F5_ecu0_0_pool.bits.NoiseSum_2<<4))
#define SetTxNoiseSum_6F5_ecu0_0(v)                         FL_EchoData_6F5_ecu0_0_pool.bits.NoiseSum_1=(v);FL_EchoData_6F5_ecu0_0_pool.bits.NoiseSum_2=((v)>>4);
#define GetTxUpdateFlg_6F6_ecu0_0()                         (FML_EchoData_6F6_ecu0_0_pool.bits.UpdateFlg)
#define SetTxUpdateFlg_6F6_ecu0_0(v)                        FML_EchoData_6F6_ecu0_0_pool.bits.UpdateFlg= (v)
#define GetTxSnsWorkSts_6F6_ecu0_0()                        (FML_EchoData_6F6_ecu0_0_pool.bits.SnsWorkSts)
#define SetTxSnsWorkSts_6F6_ecu0_0(v)                       FML_EchoData_6F6_ecu0_0_pool.bits.SnsWorkSts= (v)
#define GetTxMaster_Meas_Type_6F6_ecu0_0()                  (FML_EchoData_6F6_ecu0_0_pool.bits.Master_Meas_Type)
#define SetTxMaster_Meas_Type_6F6_ecu0_0(v)                 FML_EchoData_6F6_ecu0_0_pool.bits.Master_Meas_Type= (v)
#define GetTxEepromErrFlg_6F6_ecu0_0()                      (FML_EchoData_6F6_ecu0_0_pool.bits.EepromErrFlg)
#define SetTxEepromErrFlg_6F6_ecu0_0(v)                     FML_EchoData_6F6_ecu0_0_pool.bits.EepromErrFlg= (v)
#define GetTxMaster_Rtm_6F6_ecu0_0()                        (FML_EchoData_6F6_ecu0_0_pool.bits.Master_Rtm_1|(FML_EchoData_6F6_ecu0_0_pool.bits.Master_Rtm_2<<8))
#define SetTxMaster_Rtm_6F6_ecu0_0(v)                       FML_EchoData_6F6_ecu0_0_pool.bits.Master_Rtm_1=(v);FML_EchoData_6F6_ecu0_0_pool.bits.Master_Rtm_2=((v)>>8);
#define GetTxMaster_Distance_0_6F6_ecu0_0()                 (FML_EchoData_6F6_ecu0_0_pool.bits.Master_Distance_0_1|(FML_EchoData_6F6_ecu0_0_pool.bits.Master_Distance_0_2<<8))
#define SetTxMaster_Distance_0_6F6_ecu0_0(v)                FML_EchoData_6F6_ecu0_0_pool.bits.Master_Distance_0_1=(v);FML_EchoData_6F6_ecu0_0_pool.bits.Master_Distance_0_2=((v)>>8);
#define GetTxMaster_Distance_1_6F6_ecu0_0()                 (FML_EchoData_6F6_ecu0_0_pool.bits.Master_Distance_1_1|(FML_EchoData_6F6_ecu0_0_pool.bits.Master_Distance_1_2<<8))
#define SetTxMaster_Distance_1_6F6_ecu0_0(v)                FML_EchoData_6F6_ecu0_0_pool.bits.Master_Distance_1_1=(v);FML_EchoData_6F6_ecu0_0_pool.bits.Master_Distance_1_2=((v)>>8);
#define GetTxMaster_Distance_2_6F6_ecu0_0()                 (FML_EchoData_6F6_ecu0_0_pool.bits.Master_Distance_2_1|(FML_EchoData_6F6_ecu0_0_pool.bits.Master_Distance_2_2<<8))
#define SetTxMaster_Distance_2_6F6_ecu0_0(v)                FML_EchoData_6F6_ecu0_0_pool.bits.Master_Distance_2_1=(v);FML_EchoData_6F6_ecu0_0_pool.bits.Master_Distance_2_2=((v)>>8);
#define GetTxMaster_Distance_3_6F6_ecu0_0()                 (FML_EchoData_6F6_ecu0_0_pool.bits.Master_Distance_3_1|(FML_EchoData_6F6_ecu0_0_pool.bits.Master_Distance_3_2<<8))
#define SetTxMaster_Distance_3_6F6_ecu0_0(v)                FML_EchoData_6F6_ecu0_0_pool.bits.Master_Distance_3_1=(v);FML_EchoData_6F6_ecu0_0_pool.bits.Master_Distance_3_2=((v)>>8);
#define GetTxMaster_EchoHigh_0_6F6_ecu0_0()                 (FML_EchoData_6F6_ecu0_0_pool.bits.Master_EchoHigh_0_1|(FML_EchoData_6F6_ecu0_0_pool.bits.Master_EchoHigh_0_2<<8))
#define SetTxMaster_EchoHigh_0_6F6_ecu0_0(v)                FML_EchoData_6F6_ecu0_0_pool.bits.Master_EchoHigh_0_1=(v);FML_EchoData_6F6_ecu0_0_pool.bits.Master_EchoHigh_0_2=((v)>>8);
#define GetTxMaster_EchoHigh_1_6F6_ecu0_0()                 (FML_EchoData_6F6_ecu0_0_pool.bits.Master_EchoHigh_1_1|(FML_EchoData_6F6_ecu0_0_pool.bits.Master_EchoHigh_1_2<<8))
#define SetTxMaster_EchoHigh_1_6F6_ecu0_0(v)                FML_EchoData_6F6_ecu0_0_pool.bits.Master_EchoHigh_1_1=(v);FML_EchoData_6F6_ecu0_0_pool.bits.Master_EchoHigh_1_2=((v)>>8);
#define GetTxMaster_EchoHigh_2_6F6_ecu0_0()                 (FML_EchoData_6F6_ecu0_0_pool.bits.Master_EchoHigh_2_1|(FML_EchoData_6F6_ecu0_0_pool.bits.Master_EchoHigh_2_2<<8))
#define SetTxMaster_EchoHigh_2_6F6_ecu0_0(v)                FML_EchoData_6F6_ecu0_0_pool.bits.Master_EchoHigh_2_1=(v);FML_EchoData_6F6_ecu0_0_pool.bits.Master_EchoHigh_2_2=((v)>>8);
#define GetTxMaster_EchoHigh_3_6F6_ecu0_0()                 (FML_EchoData_6F6_ecu0_0_pool.bits.Master_EchoHigh_3_1|(FML_EchoData_6F6_ecu0_0_pool.bits.Master_EchoHigh_3_2<<8))
#define SetTxMaster_EchoHigh_3_6F6_ecu0_0(v)                FML_EchoData_6F6_ecu0_0_pool.bits.Master_EchoHigh_3_1=(v);FML_EchoData_6F6_ecu0_0_pool.bits.Master_EchoHigh_3_2=((v)>>8);
#define GetTxLeftListen_Distance_0_6F6_ecu0_0()             (FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_Distance_0_1|(FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_Distance_0_2<<8))
#define SetTxLeftListen_Distance_0_6F6_ecu0_0(v)            FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_Distance_0_1=(v);FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_Distance_0_2=((v)>>8);
#define GetTxLeftListen_Distance_1_6F6_ecu0_0()             (FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_Distance_1_1|(FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_Distance_1_2<<8))
#define SetTxLeftListen_Distance_1_6F6_ecu0_0(v)            FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_Distance_1_1=(v);FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_Distance_1_2=((v)>>8);
#define GetTxLeftListen_Distance_2_6F6_ecu0_0()             (FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_Distance_2_1|(FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_Distance_2_2<<8))
#define SetTxLeftListen_Distance_2_6F6_ecu0_0(v)            FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_Distance_2_1=(v);FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_Distance_2_2=((v)>>8);
#define GetTxLeftListen_Distance_3_6F6_ecu0_0()             (FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_Distance_3_1|(FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_Distance_3_2<<8))
#define SetTxLeftListen_Distance_3_6F6_ecu0_0(v)            FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_Distance_3_1=(v);FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_Distance_3_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_0_6F6_ecu0_0()             (FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_EchoHigh_0_1|(FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_EchoHigh_0_2<<8))
#define SetTxLeftListen_EchoHigh_0_6F6_ecu0_0(v)            FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_EchoHigh_0_1=(v);FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_EchoHigh_0_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_1_6F6_ecu0_0()             (FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_EchoHigh_1_1|(FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_EchoHigh_1_2<<8))
#define SetTxLeftListen_EchoHigh_1_6F6_ecu0_0(v)            FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_EchoHigh_1_1=(v);FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_EchoHigh_1_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_2_6F6_ecu0_0()             (FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_EchoHigh_2_1|(FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_EchoHigh_2_2<<8))
#define SetTxLeftListen_EchoHigh_2_6F6_ecu0_0(v)            FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_EchoHigh_2_1=(v);FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_EchoHigh_2_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_3_6F6_ecu0_0()             (FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_EchoHigh_3_1|(FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_EchoHigh_3_2<<8))
#define SetTxLeftListen_EchoHigh_3_6F6_ecu0_0(v)            FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_EchoHigh_3_1=(v);FML_EchoData_6F6_ecu0_0_pool.bits.LeftListen_EchoHigh_3_2=((v)>>8);
#define GetTxRightListen_Distance_0_6F6_ecu0_0()            (FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_Distance_0_1|(FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_Distance_0_2<<8))
#define SetTxRightListen_Distance_0_6F6_ecu0_0(v)           FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_Distance_0_1=(v);FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_Distance_0_2=((v)>>8);
#define GetTxRightListen_Distance_1_6F6_ecu0_0()            (FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_Distance_1_1|(FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_Distance_1_2<<8))
#define SetTxRightListen_Distance_1_6F6_ecu0_0(v)           FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_Distance_1_1=(v);FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_Distance_1_2=((v)>>8);
#define GetTxRightListen_Distance_2_6F6_ecu0_0()            (FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_Distance_2_1|(FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_Distance_2_2<<8))
#define SetTxRightListen_Distance_2_6F6_ecu0_0(v)           FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_Distance_2_1=(v);FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_Distance_2_2=((v)>>8);
#define GetTxRightListen_Distance_3_6F6_ecu0_0()            (FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_Distance_3_1|(FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_Distance_3_2<<8))
#define SetTxRightListen_Distance_3_6F6_ecu0_0(v)           FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_Distance_3_1=(v);FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_Distance_3_2=((v)>>8);
#define GetTxRightListen_EchoHigh_0_6F6_ecu0_0()            (FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_EchoHigh_0_1|(FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_EchoHigh_0_2<<8))
#define SetTxRightListen_EchoHigh_0_6F6_ecu0_0(v)           FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_EchoHigh_0_1=(v);FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_EchoHigh_0_2=((v)>>8);
#define GetTxRightListen_EchoHigh_1_6F6_ecu0_0()            (FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_EchoHigh_1_1|(FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_EchoHigh_1_2<<8))
#define SetTxRightListen_EchoHigh_1_6F6_ecu0_0(v)           FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_EchoHigh_1_1=(v);FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_EchoHigh_1_2=((v)>>8);
#define GetTxRightListen_EchoHigh_2_6F6_ecu0_0()            (FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_EchoHigh_2_1|(FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_EchoHigh_2_2<<8))
#define SetTxRightListen_EchoHigh_2_6F6_ecu0_0(v)           FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_EchoHigh_2_1=(v);FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_EchoHigh_2_2=((v)>>8);
#define GetTxRightListen_EchoHigh_3_6F6_ecu0_0()            (FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_EchoHigh_3_1|(FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_EchoHigh_3_2<<8))
#define SetTxRightListen_EchoHigh_3_6F6_ecu0_0(v)           FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_EchoHigh_3_1=(v);FML_EchoData_6F6_ecu0_0_pool.bits.RightListen_EchoHigh_3_2=((v)>>8);
#define GetTxMasterEchoCnt_6F6_ecu0_0()                     (FML_EchoData_6F6_ecu0_0_pool.bits.MasterEchoCnt)
#define SetTxMasterEchoCnt_6F6_ecu0_0(v)                    FML_EchoData_6F6_ecu0_0_pool.bits.MasterEchoCnt= (v)
#define GetTxLeftEchoCnt_6F6_ecu0_0()                       (FML_EchoData_6F6_ecu0_0_pool.bits.LeftEchoCnt)
#define SetTxLeftEchoCnt_6F6_ecu0_0(v)                      FML_EchoData_6F6_ecu0_0_pool.bits.LeftEchoCnt= (v)
#define GetTxRightEchoCnt_6F6_ecu0_0()                      (FML_EchoData_6F6_ecu0_0_pool.bits.RightEchoCnt)
#define SetTxRightEchoCnt_6F6_ecu0_0(v)                     FML_EchoData_6F6_ecu0_0_pool.bits.RightEchoCnt= (v)
#define GetTxNFD_Flag_6F6_ecu0_0()                          (FML_EchoData_6F6_ecu0_0_pool.bits.NFD_Flag)
#define SetTxNFD_Flag_6F6_ecu0_0(v)                         FML_EchoData_6F6_ecu0_0_pool.bits.NFD_Flag= (v)
#define GetTxNFD_Dis_6F6_ecu0_0()                           (FML_EchoData_6F6_ecu0_0_pool.bits.NFD_Dis_1|(FML_EchoData_6F6_ecu0_0_pool.bits.NFD_Dis_2<<8))
#define SetTxNFD_Dis_6F6_ecu0_0(v)                          FML_EchoData_6F6_ecu0_0_pool.bits.NFD_Dis_1=(v);FML_EchoData_6F6_ecu0_0_pool.bits.NFD_Dis_2=((v)>>8);
#define GetTxRing_Fre_6F6_ecu0_0()                          (FML_EchoData_6F6_ecu0_0_pool.bits.Ring_Fre_1|(FML_EchoData_6F6_ecu0_0_pool.bits.Ring_Fre_2<<8))
#define SetTxRing_Fre_6F6_ecu0_0(v)                         FML_EchoData_6F6_ecu0_0_pool.bits.Ring_Fre_1=(v);FML_EchoData_6F6_ecu0_0_pool.bits.Ring_Fre_2=((v)>>8);
#define GetTxNoiseCnt_6F6_ecu0_0()                          (FML_EchoData_6F6_ecu0_0_pool.bits.NoiseCnt)
#define SetTxNoiseCnt_6F6_ecu0_0(v)                         FML_EchoData_6F6_ecu0_0_pool.bits.NoiseCnt= (v)
#define GetTxNoiseSum_6F6_ecu0_0()                          (FML_EchoData_6F6_ecu0_0_pool.bits.NoiseSum_1|(FML_EchoData_6F6_ecu0_0_pool.bits.NoiseSum_2<<4))
#define SetTxNoiseSum_6F6_ecu0_0(v)                         FML_EchoData_6F6_ecu0_0_pool.bits.NoiseSum_1=(v);FML_EchoData_6F6_ecu0_0_pool.bits.NoiseSum_2=((v)>>4);
#define GetTxUpdateFlg_6F7_ecu0_0()                         (FMR_EchoData_6F7_ecu0_0_pool.bits.UpdateFlg)
#define SetTxUpdateFlg_6F7_ecu0_0(v)                        FMR_EchoData_6F7_ecu0_0_pool.bits.UpdateFlg= (v)
#define GetTxSnsWorkSts_6F7_ecu0_0()                        (FMR_EchoData_6F7_ecu0_0_pool.bits.SnsWorkSts)
#define SetTxSnsWorkSts_6F7_ecu0_0(v)                       FMR_EchoData_6F7_ecu0_0_pool.bits.SnsWorkSts= (v)
#define GetTxMaster_Meas_Type_6F7_ecu0_0()                  (FMR_EchoData_6F7_ecu0_0_pool.bits.Master_Meas_Type)
#define SetTxMaster_Meas_Type_6F7_ecu0_0(v)                 FMR_EchoData_6F7_ecu0_0_pool.bits.Master_Meas_Type= (v)
#define GetTxEepromErrFlg_6F7_ecu0_0()                      (FMR_EchoData_6F7_ecu0_0_pool.bits.EepromErrFlg)
#define SetTxEepromErrFlg_6F7_ecu0_0(v)                     FMR_EchoData_6F7_ecu0_0_pool.bits.EepromErrFlg= (v)
#define GetTxMaster_Rtm_6F7_ecu0_0()                        (FMR_EchoData_6F7_ecu0_0_pool.bits.Master_Rtm_1|(FMR_EchoData_6F7_ecu0_0_pool.bits.Master_Rtm_2<<8))
#define SetTxMaster_Rtm_6F7_ecu0_0(v)                       FMR_EchoData_6F7_ecu0_0_pool.bits.Master_Rtm_1=(v);FMR_EchoData_6F7_ecu0_0_pool.bits.Master_Rtm_2=((v)>>8);
#define GetTxMaster_Distance_0_6F7_ecu0_0()                 (FMR_EchoData_6F7_ecu0_0_pool.bits.Master_Distance_0_1|(FMR_EchoData_6F7_ecu0_0_pool.bits.Master_Distance_0_2<<8))
#define SetTxMaster_Distance_0_6F7_ecu0_0(v)                FMR_EchoData_6F7_ecu0_0_pool.bits.Master_Distance_0_1=(v);FMR_EchoData_6F7_ecu0_0_pool.bits.Master_Distance_0_2=((v)>>8);
#define GetTxMaster_Distance_1_6F7_ecu0_0()                 (FMR_EchoData_6F7_ecu0_0_pool.bits.Master_Distance_1_1|(FMR_EchoData_6F7_ecu0_0_pool.bits.Master_Distance_1_2<<8))
#define SetTxMaster_Distance_1_6F7_ecu0_0(v)                FMR_EchoData_6F7_ecu0_0_pool.bits.Master_Distance_1_1=(v);FMR_EchoData_6F7_ecu0_0_pool.bits.Master_Distance_1_2=((v)>>8);
#define GetTxMaster_Distance_2_6F7_ecu0_0()                 (FMR_EchoData_6F7_ecu0_0_pool.bits.Master_Distance_2_1|(FMR_EchoData_6F7_ecu0_0_pool.bits.Master_Distance_2_2<<8))
#define SetTxMaster_Distance_2_6F7_ecu0_0(v)                FMR_EchoData_6F7_ecu0_0_pool.bits.Master_Distance_2_1=(v);FMR_EchoData_6F7_ecu0_0_pool.bits.Master_Distance_2_2=((v)>>8);
#define GetTxMaster_Distance_3_6F7_ecu0_0()                 (FMR_EchoData_6F7_ecu0_0_pool.bits.Master_Distance_3_1|(FMR_EchoData_6F7_ecu0_0_pool.bits.Master_Distance_3_2<<8))
#define SetTxMaster_Distance_3_6F7_ecu0_0(v)                FMR_EchoData_6F7_ecu0_0_pool.bits.Master_Distance_3_1=(v);FMR_EchoData_6F7_ecu0_0_pool.bits.Master_Distance_3_2=((v)>>8);
#define GetTxMaster_EchoHigh_0_6F7_ecu0_0()                 (FMR_EchoData_6F7_ecu0_0_pool.bits.Master_EchoHigh_0_1|(FMR_EchoData_6F7_ecu0_0_pool.bits.Master_EchoHigh_0_2<<8))
#define SetTxMaster_EchoHigh_0_6F7_ecu0_0(v)                FMR_EchoData_6F7_ecu0_0_pool.bits.Master_EchoHigh_0_1=(v);FMR_EchoData_6F7_ecu0_0_pool.bits.Master_EchoHigh_0_2=((v)>>8);
#define GetTxMaster_EchoHigh_1_6F7_ecu0_0()                 (FMR_EchoData_6F7_ecu0_0_pool.bits.Master_EchoHigh_1_1|(FMR_EchoData_6F7_ecu0_0_pool.bits.Master_EchoHigh_1_2<<8))
#define SetTxMaster_EchoHigh_1_6F7_ecu0_0(v)                FMR_EchoData_6F7_ecu0_0_pool.bits.Master_EchoHigh_1_1=(v);FMR_EchoData_6F7_ecu0_0_pool.bits.Master_EchoHigh_1_2=((v)>>8);
#define GetTxMaster_EchoHigh_2_6F7_ecu0_0()                 (FMR_EchoData_6F7_ecu0_0_pool.bits.Master_EchoHigh_2_1|(FMR_EchoData_6F7_ecu0_0_pool.bits.Master_EchoHigh_2_2<<8))
#define SetTxMaster_EchoHigh_2_6F7_ecu0_0(v)                FMR_EchoData_6F7_ecu0_0_pool.bits.Master_EchoHigh_2_1=(v);FMR_EchoData_6F7_ecu0_0_pool.bits.Master_EchoHigh_2_2=((v)>>8);
#define GetTxMaster_EchoHigh_3_6F7_ecu0_0()                 (FMR_EchoData_6F7_ecu0_0_pool.bits.Master_EchoHigh_3_1|(FMR_EchoData_6F7_ecu0_0_pool.bits.Master_EchoHigh_3_2<<8))
#define SetTxMaster_EchoHigh_3_6F7_ecu0_0(v)                FMR_EchoData_6F7_ecu0_0_pool.bits.Master_EchoHigh_3_1=(v);FMR_EchoData_6F7_ecu0_0_pool.bits.Master_EchoHigh_3_2=((v)>>8);
#define GetTxLeftListen_Distance_0_6F7_ecu0_0()             (FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_Distance_0_1|(FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_Distance_0_2<<8))
#define SetTxLeftListen_Distance_0_6F7_ecu0_0(v)            FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_Distance_0_1=(v);FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_Distance_0_2=((v)>>8);
#define GetTxLeftListen_Distance_1_6F7_ecu0_0()             (FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_Distance_1_1|(FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_Distance_1_2<<8))
#define SetTxLeftListen_Distance_1_6F7_ecu0_0(v)            FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_Distance_1_1=(v);FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_Distance_1_2=((v)>>8);
#define GetTxLeftListen_Distance_2_6F7_ecu0_0()             (FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_Distance_2_1|(FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_Distance_2_2<<8))
#define SetTxLeftListen_Distance_2_6F7_ecu0_0(v)            FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_Distance_2_1=(v);FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_Distance_2_2=((v)>>8);
#define GetTxLeftListen_Distance_3_6F7_ecu0_0()             (FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_Distance_3_1|(FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_Distance_3_2<<8))
#define SetTxLeftListen_Distance_3_6F7_ecu0_0(v)            FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_Distance_3_1=(v);FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_Distance_3_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_0_6F7_ecu0_0()             (FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_EchoHigh_0_1|(FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_EchoHigh_0_2<<8))
#define SetTxLeftListen_EchoHigh_0_6F7_ecu0_0(v)            FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_EchoHigh_0_1=(v);FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_EchoHigh_0_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_1_6F7_ecu0_0()             (FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_EchoHigh_1_1|(FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_EchoHigh_1_2<<8))
#define SetTxLeftListen_EchoHigh_1_6F7_ecu0_0(v)            FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_EchoHigh_1_1=(v);FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_EchoHigh_1_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_2_6F7_ecu0_0()             (FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_EchoHigh_2_1|(FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_EchoHigh_2_2<<8))
#define SetTxLeftListen_EchoHigh_2_6F7_ecu0_0(v)            FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_EchoHigh_2_1=(v);FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_EchoHigh_2_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_3_6F7_ecu0_0()             (FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_EchoHigh_3_1|(FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_EchoHigh_3_2<<8))
#define SetTxLeftListen_EchoHigh_3_6F7_ecu0_0(v)            FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_EchoHigh_3_1=(v);FMR_EchoData_6F7_ecu0_0_pool.bits.LeftListen_EchoHigh_3_2=((v)>>8);
#define GetTxRightListen_Distance_0_6F7_ecu0_0()            (FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_Distance_0_1|(FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_Distance_0_2<<8))
#define SetTxRightListen_Distance_0_6F7_ecu0_0(v)           FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_Distance_0_1=(v);FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_Distance_0_2=((v)>>8);
#define GetTxRightListen_Distance_1_6F7_ecu0_0()            (FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_Distance_1_1|(FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_Distance_1_2<<8))
#define SetTxRightListen_Distance_1_6F7_ecu0_0(v)           FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_Distance_1_1=(v);FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_Distance_1_2=((v)>>8);
#define GetTxRightListen_Distance_2_6F7_ecu0_0()            (FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_Distance_2_1|(FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_Distance_2_2<<8))
#define SetTxRightListen_Distance_2_6F7_ecu0_0(v)           FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_Distance_2_1=(v);FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_Distance_2_2=((v)>>8);
#define GetTxRightListen_Distance_3_6F7_ecu0_0()            (FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_Distance_3_1|(FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_Distance_3_2<<8))
#define SetTxRightListen_Distance_3_6F7_ecu0_0(v)           FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_Distance_3_1=(v);FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_Distance_3_2=((v)>>8);
#define GetTxRightListen_EchoHigh_0_6F7_ecu0_0()            (FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_EchoHigh_0_1|(FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_EchoHigh_0_2<<8))
#define SetTxRightListen_EchoHigh_0_6F7_ecu0_0(v)           FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_EchoHigh_0_1=(v);FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_EchoHigh_0_2=((v)>>8);
#define GetTxRightListen_EchoHigh_1_6F7_ecu0_0()            (FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_EchoHigh_1_1|(FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_EchoHigh_1_2<<8))
#define SetTxRightListen_EchoHigh_1_6F7_ecu0_0(v)           FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_EchoHigh_1_1=(v);FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_EchoHigh_1_2=((v)>>8);
#define GetTxRightListen_EchoHigh_2_6F7_ecu0_0()            (FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_EchoHigh_2_1|(FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_EchoHigh_2_2<<8))
#define SetTxRightListen_EchoHigh_2_6F7_ecu0_0(v)           FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_EchoHigh_2_1=(v);FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_EchoHigh_2_2=((v)>>8);
#define GetTxRightListen_EchoHigh_3_6F7_ecu0_0()            (FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_EchoHigh_3_1|(FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_EchoHigh_3_2<<8))
#define SetTxRightListen_EchoHigh_3_6F7_ecu0_0(v)           FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_EchoHigh_3_1=(v);FMR_EchoData_6F7_ecu0_0_pool.bits.RightListen_EchoHigh_3_2=((v)>>8);
#define GetTxMasterEchoCnt_6F7_ecu0_0()                     (FMR_EchoData_6F7_ecu0_0_pool.bits.MasterEchoCnt)
#define SetTxMasterEchoCnt_6F7_ecu0_0(v)                    FMR_EchoData_6F7_ecu0_0_pool.bits.MasterEchoCnt= (v)
#define GetTxLeftEchoCnt_6F7_ecu0_0()                       (FMR_EchoData_6F7_ecu0_0_pool.bits.LeftEchoCnt)
#define SetTxLeftEchoCnt_6F7_ecu0_0(v)                      FMR_EchoData_6F7_ecu0_0_pool.bits.LeftEchoCnt= (v)
#define GetTxRightEchoCnt_6F7_ecu0_0()                      (FMR_EchoData_6F7_ecu0_0_pool.bits.RightEchoCnt)
#define SetTxRightEchoCnt_6F7_ecu0_0(v)                     FMR_EchoData_6F7_ecu0_0_pool.bits.RightEchoCnt= (v)
#define GetTxNFD_Flag_6F7_ecu0_0()                          (FMR_EchoData_6F7_ecu0_0_pool.bits.NFD_Flag)
#define SetTxNFD_Flag_6F7_ecu0_0(v)                         FMR_EchoData_6F7_ecu0_0_pool.bits.NFD_Flag= (v)
#define GetTxNFD_Dis_6F7_ecu0_0()                           (FMR_EchoData_6F7_ecu0_0_pool.bits.NFD_Dis_1|(FMR_EchoData_6F7_ecu0_0_pool.bits.NFD_Dis_2<<8))
#define SetTxNFD_Dis_6F7_ecu0_0(v)                          FMR_EchoData_6F7_ecu0_0_pool.bits.NFD_Dis_1=(v);FMR_EchoData_6F7_ecu0_0_pool.bits.NFD_Dis_2=((v)>>8);
#define GetTxRing_Fre_6F7_ecu0_0()                          (FMR_EchoData_6F7_ecu0_0_pool.bits.Ring_Fre_1|(FMR_EchoData_6F7_ecu0_0_pool.bits.Ring_Fre_2<<8))
#define SetTxRing_Fre_6F7_ecu0_0(v)                         FMR_EchoData_6F7_ecu0_0_pool.bits.Ring_Fre_1=(v);FMR_EchoData_6F7_ecu0_0_pool.bits.Ring_Fre_2=((v)>>8);
#define GetTxNoiseCnt_6F7_ecu0_0()                          (FMR_EchoData_6F7_ecu0_0_pool.bits.NoiseCnt)
#define SetTxNoiseCnt_6F7_ecu0_0(v)                         FMR_EchoData_6F7_ecu0_0_pool.bits.NoiseCnt= (v)
#define GetTxNoiseSum_6F7_ecu0_0()                          (FMR_EchoData_6F7_ecu0_0_pool.bits.NoiseSum_1|(FMR_EchoData_6F7_ecu0_0_pool.bits.NoiseSum_2<<4))
#define SetTxNoiseSum_6F7_ecu0_0(v)                         FMR_EchoData_6F7_ecu0_0_pool.bits.NoiseSum_1=(v);FMR_EchoData_6F7_ecu0_0_pool.bits.NoiseSum_2=((v)>>4);
#define GetTxUpdateFlg_6F8_ecu0_0()                         (FR_EchoData_6F8_ecu0_0_pool.bits.UpdateFlg)
#define SetTxUpdateFlg_6F8_ecu0_0(v)                        FR_EchoData_6F8_ecu0_0_pool.bits.UpdateFlg= (v)
#define GetTxSnsWorkSts_6F8_ecu0_0()                        (FR_EchoData_6F8_ecu0_0_pool.bits.SnsWorkSts)
#define SetTxSnsWorkSts_6F8_ecu0_0(v)                       FR_EchoData_6F8_ecu0_0_pool.bits.SnsWorkSts= (v)
#define GetTxMaster_Meas_Type_6F8_ecu0_0()                  (FR_EchoData_6F8_ecu0_0_pool.bits.Master_Meas_Type)
#define SetTxMaster_Meas_Type_6F8_ecu0_0(v)                 FR_EchoData_6F8_ecu0_0_pool.bits.Master_Meas_Type= (v)
#define GetTxEepromErrFlg_6F8_ecu0_0()                      (FR_EchoData_6F8_ecu0_0_pool.bits.EepromErrFlg)
#define SetTxEepromErrFlg_6F8_ecu0_0(v)                     FR_EchoData_6F8_ecu0_0_pool.bits.EepromErrFlg= (v)
#define GetTxMaster_Rtm_6F8_ecu0_0()                        (FR_EchoData_6F8_ecu0_0_pool.bits.Master_Rtm_1|(FR_EchoData_6F8_ecu0_0_pool.bits.Master_Rtm_2<<8))
#define SetTxMaster_Rtm_6F8_ecu0_0(v)                       FR_EchoData_6F8_ecu0_0_pool.bits.Master_Rtm_1=(v);FR_EchoData_6F8_ecu0_0_pool.bits.Master_Rtm_2=((v)>>8);
#define GetTxMaster_Distance_0_6F8_ecu0_0()                 (FR_EchoData_6F8_ecu0_0_pool.bits.Master_Distance_0_1|(FR_EchoData_6F8_ecu0_0_pool.bits.Master_Distance_0_2<<8))
#define SetTxMaster_Distance_0_6F8_ecu0_0(v)                FR_EchoData_6F8_ecu0_0_pool.bits.Master_Distance_0_1=(v);FR_EchoData_6F8_ecu0_0_pool.bits.Master_Distance_0_2=((v)>>8);
#define GetTxMaster_Distance_1_6F8_ecu0_0()                 (FR_EchoData_6F8_ecu0_0_pool.bits.Master_Distance_1_1|(FR_EchoData_6F8_ecu0_0_pool.bits.Master_Distance_1_2<<8))
#define SetTxMaster_Distance_1_6F8_ecu0_0(v)                FR_EchoData_6F8_ecu0_0_pool.bits.Master_Distance_1_1=(v);FR_EchoData_6F8_ecu0_0_pool.bits.Master_Distance_1_2=((v)>>8);
#define GetTxMaster_Distance_2_6F8_ecu0_0()                 (FR_EchoData_6F8_ecu0_0_pool.bits.Master_Distance_2_1|(FR_EchoData_6F8_ecu0_0_pool.bits.Master_Distance_2_2<<8))
#define SetTxMaster_Distance_2_6F8_ecu0_0(v)                FR_EchoData_6F8_ecu0_0_pool.bits.Master_Distance_2_1=(v);FR_EchoData_6F8_ecu0_0_pool.bits.Master_Distance_2_2=((v)>>8);
#define GetTxMaster_Distance_3_6F8_ecu0_0()                 (FR_EchoData_6F8_ecu0_0_pool.bits.Master_Distance_3_1|(FR_EchoData_6F8_ecu0_0_pool.bits.Master_Distance_3_2<<8))
#define SetTxMaster_Distance_3_6F8_ecu0_0(v)                FR_EchoData_6F8_ecu0_0_pool.bits.Master_Distance_3_1=(v);FR_EchoData_6F8_ecu0_0_pool.bits.Master_Distance_3_2=((v)>>8);
#define GetTxMaster_EchoHigh_0_6F8_ecu0_0()                 (FR_EchoData_6F8_ecu0_0_pool.bits.Master_EchoHigh_0_1|(FR_EchoData_6F8_ecu0_0_pool.bits.Master_EchoHigh_0_2<<8))
#define SetTxMaster_EchoHigh_0_6F8_ecu0_0(v)                FR_EchoData_6F8_ecu0_0_pool.bits.Master_EchoHigh_0_1=(v);FR_EchoData_6F8_ecu0_0_pool.bits.Master_EchoHigh_0_2=((v)>>8);
#define GetTxMaster_EchoHigh_1_6F8_ecu0_0()                 (FR_EchoData_6F8_ecu0_0_pool.bits.Master_EchoHigh_1_1|(FR_EchoData_6F8_ecu0_0_pool.bits.Master_EchoHigh_1_2<<8))
#define SetTxMaster_EchoHigh_1_6F8_ecu0_0(v)                FR_EchoData_6F8_ecu0_0_pool.bits.Master_EchoHigh_1_1=(v);FR_EchoData_6F8_ecu0_0_pool.bits.Master_EchoHigh_1_2=((v)>>8);
#define GetTxMaster_EchoHigh_2_6F8_ecu0_0()                 (FR_EchoData_6F8_ecu0_0_pool.bits.Master_EchoHigh_2_1|(FR_EchoData_6F8_ecu0_0_pool.bits.Master_EchoHigh_2_2<<8))
#define SetTxMaster_EchoHigh_2_6F8_ecu0_0(v)                FR_EchoData_6F8_ecu0_0_pool.bits.Master_EchoHigh_2_1=(v);FR_EchoData_6F8_ecu0_0_pool.bits.Master_EchoHigh_2_2=((v)>>8);
#define GetTxMaster_EchoHigh_3_6F8_ecu0_0()                 (FR_EchoData_6F8_ecu0_0_pool.bits.Master_EchoHigh_3_1|(FR_EchoData_6F8_ecu0_0_pool.bits.Master_EchoHigh_3_2<<8))
#define SetTxMaster_EchoHigh_3_6F8_ecu0_0(v)                FR_EchoData_6F8_ecu0_0_pool.bits.Master_EchoHigh_3_1=(v);FR_EchoData_6F8_ecu0_0_pool.bits.Master_EchoHigh_3_2=((v)>>8);
#define GetTxLeftListen_Distance_0_6F8_ecu0_0()             (FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_Distance_0_1|(FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_Distance_0_2<<8))
#define SetTxLeftListen_Distance_0_6F8_ecu0_0(v)            FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_Distance_0_1=(v);FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_Distance_0_2=((v)>>8);
#define GetTxLeftListen_Distance_1_6F8_ecu0_0()             (FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_Distance_1_1|(FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_Distance_1_2<<8))
#define SetTxLeftListen_Distance_1_6F8_ecu0_0(v)            FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_Distance_1_1=(v);FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_Distance_1_2=((v)>>8);
#define GetTxLeftListen_Distance_2_6F8_ecu0_0()             (FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_Distance_2_1|(FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_Distance_2_2<<8))
#define SetTxLeftListen_Distance_2_6F8_ecu0_0(v)            FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_Distance_2_1=(v);FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_Distance_2_2=((v)>>8);
#define GetTxLeftListen_Distance_3_6F8_ecu0_0()             (FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_Distance_3_1|(FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_Distance_3_2<<8))
#define SetTxLeftListen_Distance_3_6F8_ecu0_0(v)            FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_Distance_3_1=(v);FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_Distance_3_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_0_6F8_ecu0_0()             (FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_EchoHigh_0_1|(FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_EchoHigh_0_2<<8))
#define SetTxLeftListen_EchoHigh_0_6F8_ecu0_0(v)            FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_EchoHigh_0_1=(v);FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_EchoHigh_0_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_1_6F8_ecu0_0()             (FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_EchoHigh_1_1|(FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_EchoHigh_1_2<<8))
#define SetTxLeftListen_EchoHigh_1_6F8_ecu0_0(v)            FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_EchoHigh_1_1=(v);FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_EchoHigh_1_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_2_6F8_ecu0_0()             (FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_EchoHigh_2_1|(FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_EchoHigh_2_2<<8))
#define SetTxLeftListen_EchoHigh_2_6F8_ecu0_0(v)            FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_EchoHigh_2_1=(v);FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_EchoHigh_2_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_3_6F8_ecu0_0()             (FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_EchoHigh_3_1|(FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_EchoHigh_3_2<<8))
#define SetTxLeftListen_EchoHigh_3_6F8_ecu0_0(v)            FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_EchoHigh_3_1=(v);FR_EchoData_6F8_ecu0_0_pool.bits.LeftListen_EchoHigh_3_2=((v)>>8);
#define GetTxRightListen_Distance_0_6F8_ecu0_0()            (FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_Distance_0_1|(FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_Distance_0_2<<8))
#define SetTxRightListen_Distance_0_6F8_ecu0_0(v)           FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_Distance_0_1=(v);FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_Distance_0_2=((v)>>8);
#define GetTxRightListen_Distance_1_6F8_ecu0_0()            (FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_Distance_1_1|(FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_Distance_1_2<<8))
#define SetTxRightListen_Distance_1_6F8_ecu0_0(v)           FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_Distance_1_1=(v);FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_Distance_1_2=((v)>>8);
#define GetTxRightListen_Distance_2_6F8_ecu0_0()            (FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_Distance_2_1|(FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_Distance_2_2<<8))
#define SetTxRightListen_Distance_2_6F8_ecu0_0(v)           FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_Distance_2_1=(v);FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_Distance_2_2=((v)>>8);
#define GetTxRightListen_Distance_3_6F8_ecu0_0()            (FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_Distance_3_1|(FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_Distance_3_2<<8))
#define SetTxRightListen_Distance_3_6F8_ecu0_0(v)           FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_Distance_3_1=(v);FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_Distance_3_2=((v)>>8);
#define GetTxRightListen_EchoHigh_0_6F8_ecu0_0()            (FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_EchoHigh_0_1|(FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_EchoHigh_0_2<<8))
#define SetTxRightListen_EchoHigh_0_6F8_ecu0_0(v)           FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_EchoHigh_0_1=(v);FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_EchoHigh_0_2=((v)>>8);
#define GetTxRightListen_EchoHigh_1_6F8_ecu0_0()            (FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_EchoHigh_1_1|(FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_EchoHigh_1_2<<8))
#define SetTxRightListen_EchoHigh_1_6F8_ecu0_0(v)           FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_EchoHigh_1_1=(v);FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_EchoHigh_1_2=((v)>>8);
#define GetTxRightListen_EchoHigh_2_6F8_ecu0_0()            (FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_EchoHigh_2_1|(FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_EchoHigh_2_2<<8))
#define SetTxRightListen_EchoHigh_2_6F8_ecu0_0(v)           FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_EchoHigh_2_1=(v);FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_EchoHigh_2_2=((v)>>8);
#define GetTxRightListen_EchoHigh_3_6F8_ecu0_0()            (FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_EchoHigh_3_1|(FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_EchoHigh_3_2<<8))
#define SetTxRightListen_EchoHigh_3_6F8_ecu0_0(v)           FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_EchoHigh_3_1=(v);FR_EchoData_6F8_ecu0_0_pool.bits.RightListen_EchoHigh_3_2=((v)>>8);
#define GetTxMasterEchoCnt_6F8_ecu0_0()                     (FR_EchoData_6F8_ecu0_0_pool.bits.MasterEchoCnt)
#define SetTxMasterEchoCnt_6F8_ecu0_0(v)                    FR_EchoData_6F8_ecu0_0_pool.bits.MasterEchoCnt= (v)
#define GetTxLeftEchoCnt_6F8_ecu0_0()                       (FR_EchoData_6F8_ecu0_0_pool.bits.LeftEchoCnt)
#define SetTxLeftEchoCnt_6F8_ecu0_0(v)                      FR_EchoData_6F8_ecu0_0_pool.bits.LeftEchoCnt= (v)
#define GetTxRightEchoCnt_6F8_ecu0_0()                      (FR_EchoData_6F8_ecu0_0_pool.bits.RightEchoCnt)
#define SetTxRightEchoCnt_6F8_ecu0_0(v)                     FR_EchoData_6F8_ecu0_0_pool.bits.RightEchoCnt= (v)
#define GetTxNFD_Flag_6F8_ecu0_0()                          (FR_EchoData_6F8_ecu0_0_pool.bits.NFD_Flag)
#define SetTxNFD_Flag_6F8_ecu0_0(v)                         FR_EchoData_6F8_ecu0_0_pool.bits.NFD_Flag= (v)
#define GetTxNFD_Dis_6F8_ecu0_0()                           (FR_EchoData_6F8_ecu0_0_pool.bits.NFD_Dis_1|(FR_EchoData_6F8_ecu0_0_pool.bits.NFD_Dis_2<<8))
#define SetTxNFD_Dis_6F8_ecu0_0(v)                          FR_EchoData_6F8_ecu0_0_pool.bits.NFD_Dis_1=(v);FR_EchoData_6F8_ecu0_0_pool.bits.NFD_Dis_2=((v)>>8);
#define GetTxRing_Fre_6F8_ecu0_0()                          (FR_EchoData_6F8_ecu0_0_pool.bits.Ring_Fre_1|(FR_EchoData_6F8_ecu0_0_pool.bits.Ring_Fre_2<<8))
#define SetTxRing_Fre_6F8_ecu0_0(v)                         FR_EchoData_6F8_ecu0_0_pool.bits.Ring_Fre_1=(v);FR_EchoData_6F8_ecu0_0_pool.bits.Ring_Fre_2=((v)>>8);
#define GetTxNoiseCnt_6F8_ecu0_0()                          (FR_EchoData_6F8_ecu0_0_pool.bits.NoiseCnt)
#define SetTxNoiseCnt_6F8_ecu0_0(v)                         FR_EchoData_6F8_ecu0_0_pool.bits.NoiseCnt= (v)
#define GetTxNoiseSum_6F8_ecu0_0()                          (FR_EchoData_6F8_ecu0_0_pool.bits.NoiseSum_1|(FR_EchoData_6F8_ecu0_0_pool.bits.NoiseSum_2<<4))
#define SetTxNoiseSum_6F8_ecu0_0(v)                         FR_EchoData_6F8_ecu0_0_pool.bits.NoiseSum_1=(v);FR_EchoData_6F8_ecu0_0_pool.bits.NoiseSum_2=((v)>>4);
#define GetTxUpdateFlg_6F9_ecu0_0()                         (FRS_EchoData_6F9_ecu0_0_pool.bits.UpdateFlg)
#define SetTxUpdateFlg_6F9_ecu0_0(v)                        FRS_EchoData_6F9_ecu0_0_pool.bits.UpdateFlg= (v)
#define GetTxSnsWorkSts_6F9_ecu0_0()                        (FRS_EchoData_6F9_ecu0_0_pool.bits.SnsWorkSts)
#define SetTxSnsWorkSts_6F9_ecu0_0(v)                       FRS_EchoData_6F9_ecu0_0_pool.bits.SnsWorkSts= (v)
#define GetTxMaster_Meas_Type_6F9_ecu0_0()                  (FRS_EchoData_6F9_ecu0_0_pool.bits.Master_Meas_Type)
#define SetTxMaster_Meas_Type_6F9_ecu0_0(v)                 FRS_EchoData_6F9_ecu0_0_pool.bits.Master_Meas_Type= (v)
#define GetTxEepromErrFlg_6F9_ecu0_0()                      (FRS_EchoData_6F9_ecu0_0_pool.bits.EepromErrFlg)
#define SetTxEepromErrFlg_6F9_ecu0_0(v)                     FRS_EchoData_6F9_ecu0_0_pool.bits.EepromErrFlg= (v)
#define GetTxMaster_Rtm_6F9_ecu0_0()                        (FRS_EchoData_6F9_ecu0_0_pool.bits.Master_Rtm_1|(FRS_EchoData_6F9_ecu0_0_pool.bits.Master_Rtm_2<<8))
#define SetTxMaster_Rtm_6F9_ecu0_0(v)                       FRS_EchoData_6F9_ecu0_0_pool.bits.Master_Rtm_1=(v);FRS_EchoData_6F9_ecu0_0_pool.bits.Master_Rtm_2=((v)>>8);
#define GetTxMaster_Distance_0_6F9_ecu0_0()                 (FRS_EchoData_6F9_ecu0_0_pool.bits.Master_Distance_0_1|(FRS_EchoData_6F9_ecu0_0_pool.bits.Master_Distance_0_2<<8))
#define SetTxMaster_Distance_0_6F9_ecu0_0(v)                FRS_EchoData_6F9_ecu0_0_pool.bits.Master_Distance_0_1=(v);FRS_EchoData_6F9_ecu0_0_pool.bits.Master_Distance_0_2=((v)>>8);
#define GetTxMaster_Distance_1_6F9_ecu0_0()                 (FRS_EchoData_6F9_ecu0_0_pool.bits.Master_Distance_1_1|(FRS_EchoData_6F9_ecu0_0_pool.bits.Master_Distance_1_2<<8))
#define SetTxMaster_Distance_1_6F9_ecu0_0(v)                FRS_EchoData_6F9_ecu0_0_pool.bits.Master_Distance_1_1=(v);FRS_EchoData_6F9_ecu0_0_pool.bits.Master_Distance_1_2=((v)>>8);
#define GetTxMaster_Distance_2_6F9_ecu0_0()                 (FRS_EchoData_6F9_ecu0_0_pool.bits.Master_Distance_2_1|(FRS_EchoData_6F9_ecu0_0_pool.bits.Master_Distance_2_2<<8))
#define SetTxMaster_Distance_2_6F9_ecu0_0(v)                FRS_EchoData_6F9_ecu0_0_pool.bits.Master_Distance_2_1=(v);FRS_EchoData_6F9_ecu0_0_pool.bits.Master_Distance_2_2=((v)>>8);
#define GetTxMaster_Distance_3_6F9_ecu0_0()                 (FRS_EchoData_6F9_ecu0_0_pool.bits.Master_Distance_3_1|(FRS_EchoData_6F9_ecu0_0_pool.bits.Master_Distance_3_2<<8))
#define SetTxMaster_Distance_3_6F9_ecu0_0(v)                FRS_EchoData_6F9_ecu0_0_pool.bits.Master_Distance_3_1=(v);FRS_EchoData_6F9_ecu0_0_pool.bits.Master_Distance_3_2=((v)>>8);
#define GetTxMaster_EchoHigh_0_6F9_ecu0_0()                 (FRS_EchoData_6F9_ecu0_0_pool.bits.Master_EchoHigh_0_1|(FRS_EchoData_6F9_ecu0_0_pool.bits.Master_EchoHigh_0_2<<8))
#define SetTxMaster_EchoHigh_0_6F9_ecu0_0(v)                FRS_EchoData_6F9_ecu0_0_pool.bits.Master_EchoHigh_0_1=(v);FRS_EchoData_6F9_ecu0_0_pool.bits.Master_EchoHigh_0_2=((v)>>8);
#define GetTxMaster_EchoHigh_1_6F9_ecu0_0()                 (FRS_EchoData_6F9_ecu0_0_pool.bits.Master_EchoHigh_1_1|(FRS_EchoData_6F9_ecu0_0_pool.bits.Master_EchoHigh_1_2<<8))
#define SetTxMaster_EchoHigh_1_6F9_ecu0_0(v)                FRS_EchoData_6F9_ecu0_0_pool.bits.Master_EchoHigh_1_1=(v);FRS_EchoData_6F9_ecu0_0_pool.bits.Master_EchoHigh_1_2=((v)>>8);
#define GetTxMaster_EchoHigh_2_6F9_ecu0_0()                 (FRS_EchoData_6F9_ecu0_0_pool.bits.Master_EchoHigh_2_1|(FRS_EchoData_6F9_ecu0_0_pool.bits.Master_EchoHigh_2_2<<8))
#define SetTxMaster_EchoHigh_2_6F9_ecu0_0(v)                FRS_EchoData_6F9_ecu0_0_pool.bits.Master_EchoHigh_2_1=(v);FRS_EchoData_6F9_ecu0_0_pool.bits.Master_EchoHigh_2_2=((v)>>8);
#define GetTxMaster_EchoHigh_3_6F9_ecu0_0()                 (FRS_EchoData_6F9_ecu0_0_pool.bits.Master_EchoHigh_3_1|(FRS_EchoData_6F9_ecu0_0_pool.bits.Master_EchoHigh_3_2<<8))
#define SetTxMaster_EchoHigh_3_6F9_ecu0_0(v)                FRS_EchoData_6F9_ecu0_0_pool.bits.Master_EchoHigh_3_1=(v);FRS_EchoData_6F9_ecu0_0_pool.bits.Master_EchoHigh_3_2=((v)>>8);
#define GetTxLeftListen_Distance_0_6F9_ecu0_0()             (FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_Distance_0_1|(FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_Distance_0_2<<8))
#define SetTxLeftListen_Distance_0_6F9_ecu0_0(v)            FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_Distance_0_1=(v);FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_Distance_0_2=((v)>>8);
#define GetTxLeftListen_Distance_1_6F9_ecu0_0()             (FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_Distance_1_1|(FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_Distance_1_2<<8))
#define SetTxLeftListen_Distance_1_6F9_ecu0_0(v)            FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_Distance_1_1=(v);FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_Distance_1_2=((v)>>8);
#define GetTxLeftListen_Distance_2_6F9_ecu0_0()             (FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_Distance_2_1|(FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_Distance_2_2<<8))
#define SetTxLeftListen_Distance_2_6F9_ecu0_0(v)            FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_Distance_2_1=(v);FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_Distance_2_2=((v)>>8);
#define GetTxLeftListen_Distance_3_6F9_ecu0_0()             (FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_Distance_3_1|(FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_Distance_3_2<<8))
#define SetTxLeftListen_Distance_3_6F9_ecu0_0(v)            FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_Distance_3_1=(v);FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_Distance_3_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_0_6F9_ecu0_0()             (FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_EchoHigh_0_1|(FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_EchoHigh_0_2<<8))
#define SetTxLeftListen_EchoHigh_0_6F9_ecu0_0(v)            FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_EchoHigh_0_1=(v);FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_EchoHigh_0_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_1_6F9_ecu0_0()             (FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_EchoHigh_1_1|(FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_EchoHigh_1_2<<8))
#define SetTxLeftListen_EchoHigh_1_6F9_ecu0_0(v)            FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_EchoHigh_1_1=(v);FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_EchoHigh_1_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_2_6F9_ecu0_0()             (FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_EchoHigh_2_1|(FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_EchoHigh_2_2<<8))
#define SetTxLeftListen_EchoHigh_2_6F9_ecu0_0(v)            FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_EchoHigh_2_1=(v);FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_EchoHigh_2_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_3_6F9_ecu0_0()             (FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_EchoHigh_3_1|(FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_EchoHigh_3_2<<8))
#define SetTxLeftListen_EchoHigh_3_6F9_ecu0_0(v)            FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_EchoHigh_3_1=(v);FRS_EchoData_6F9_ecu0_0_pool.bits.LeftListen_EchoHigh_3_2=((v)>>8);
#define GetTxRightListen_Distance_0_6F9_ecu0_0()            (FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_Distance_0_1|(FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_Distance_0_2<<8))
#define SetTxRightListen_Distance_0_6F9_ecu0_0(v)           FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_Distance_0_1=(v);FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_Distance_0_2=((v)>>8);
#define GetTxRightListen_Distance_1_6F9_ecu0_0()            (FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_Distance_1_1|(FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_Distance_1_2<<8))
#define SetTxRightListen_Distance_1_6F9_ecu0_0(v)           FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_Distance_1_1=(v);FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_Distance_1_2=((v)>>8);
#define GetTxRightListen_Distance_2_6F9_ecu0_0()            (FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_Distance_2_1|(FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_Distance_2_2<<8))
#define SetTxRightListen_Distance_2_6F9_ecu0_0(v)           FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_Distance_2_1=(v);FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_Distance_2_2=((v)>>8);
#define GetTxRightListen_Distance_3_6F9_ecu0_0()            (FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_Distance_3_1|(FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_Distance_3_2<<8))
#define SetTxRightListen_Distance_3_6F9_ecu0_0(v)           FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_Distance_3_1=(v);FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_Distance_3_2=((v)>>8);
#define GetTxRightListen_EchoHigh_0_6F9_ecu0_0()            (FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_EchoHigh_0_1|(FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_EchoHigh_0_2<<8))
#define SetTxRightListen_EchoHigh_0_6F9_ecu0_0(v)           FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_EchoHigh_0_1=(v);FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_EchoHigh_0_2=((v)>>8);
#define GetTxRightListen_EchoHigh_1_6F9_ecu0_0()            (FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_EchoHigh_1_1|(FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_EchoHigh_1_2<<8))
#define SetTxRightListen_EchoHigh_1_6F9_ecu0_0(v)           FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_EchoHigh_1_1=(v);FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_EchoHigh_1_2=((v)>>8);
#define GetTxRightListen_EchoHigh_2_6F9_ecu0_0()            (FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_EchoHigh_2_1|(FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_EchoHigh_2_2<<8))
#define SetTxRightListen_EchoHigh_2_6F9_ecu0_0(v)           FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_EchoHigh_2_1=(v);FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_EchoHigh_2_2=((v)>>8);
#define GetTxRightListen_EchoHigh_3_6F9_ecu0_0()            (FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_EchoHigh_3_1|(FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_EchoHigh_3_2<<8))
#define SetTxRightListen_EchoHigh_3_6F9_ecu0_0(v)           FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_EchoHigh_3_1=(v);FRS_EchoData_6F9_ecu0_0_pool.bits.RightListen_EchoHigh_3_2=((v)>>8);
#define GetTxMasterEchoCnt_6F9_ecu0_0()                     (FRS_EchoData_6F9_ecu0_0_pool.bits.MasterEchoCnt)
#define SetTxMasterEchoCnt_6F9_ecu0_0(v)                    FRS_EchoData_6F9_ecu0_0_pool.bits.MasterEchoCnt= (v)
#define GetTxLeftEchoCnt_6F9_ecu0_0()                       (FRS_EchoData_6F9_ecu0_0_pool.bits.LeftEchoCnt)
#define SetTxLeftEchoCnt_6F9_ecu0_0(v)                      FRS_EchoData_6F9_ecu0_0_pool.bits.LeftEchoCnt= (v)
#define GetTxRightEchoCnt_6F9_ecu0_0()                      (FRS_EchoData_6F9_ecu0_0_pool.bits.RightEchoCnt)
#define SetTxRightEchoCnt_6F9_ecu0_0(v)                     FRS_EchoData_6F9_ecu0_0_pool.bits.RightEchoCnt= (v)
#define GetTxNFD_Flag_6F9_ecu0_0()                          (FRS_EchoData_6F9_ecu0_0_pool.bits.NFD_Flag)
#define SetTxNFD_Flag_6F9_ecu0_0(v)                         FRS_EchoData_6F9_ecu0_0_pool.bits.NFD_Flag= (v)
#define GetTxNFD_Dis_6F9_ecu0_0()                           (FRS_EchoData_6F9_ecu0_0_pool.bits.NFD_Dis_1|(FRS_EchoData_6F9_ecu0_0_pool.bits.NFD_Dis_2<<8))
#define SetTxNFD_Dis_6F9_ecu0_0(v)                          FRS_EchoData_6F9_ecu0_0_pool.bits.NFD_Dis_1=(v);FRS_EchoData_6F9_ecu0_0_pool.bits.NFD_Dis_2=((v)>>8);
#define GetTxRing_Fre_6F9_ecu0_0()                          (FRS_EchoData_6F9_ecu0_0_pool.bits.Ring_Fre_1|(FRS_EchoData_6F9_ecu0_0_pool.bits.Ring_Fre_2<<8))
#define SetTxRing_Fre_6F9_ecu0_0(v)                         FRS_EchoData_6F9_ecu0_0_pool.bits.Ring_Fre_1=(v);FRS_EchoData_6F9_ecu0_0_pool.bits.Ring_Fre_2=((v)>>8);
#define GetTxNoiseCnt_6F9_ecu0_0()                          (FRS_EchoData_6F9_ecu0_0_pool.bits.NoiseCnt)
#define SetTxNoiseCnt_6F9_ecu0_0(v)                         FRS_EchoData_6F9_ecu0_0_pool.bits.NoiseCnt= (v)
#define GetTxNoiseSum_6F9_ecu0_0()                          (FRS_EchoData_6F9_ecu0_0_pool.bits.NoiseSum_1|(FRS_EchoData_6F9_ecu0_0_pool.bits.NoiseSum_2<<4))
#define SetTxNoiseSum_6F9_ecu0_0(v)                         FRS_EchoData_6F9_ecu0_0_pool.bits.NoiseSum_1=(v);FRS_EchoData_6F9_ecu0_0_pool.bits.NoiseSum_2=((v)>>4);
#define GetTxUpdateFlg_6FA_ecu0_0()                         (RRS_EchoData_6FA_ecu0_0_pool.bits.UpdateFlg)
#define SetTxUpdateFlg_6FA_ecu0_0(v)                        RRS_EchoData_6FA_ecu0_0_pool.bits.UpdateFlg= (v)
#define GetTxSnsWorkSts_6FA_ecu0_0()                        (RRS_EchoData_6FA_ecu0_0_pool.bits.SnsWorkSts)
#define SetTxSnsWorkSts_6FA_ecu0_0(v)                       RRS_EchoData_6FA_ecu0_0_pool.bits.SnsWorkSts= (v)
#define GetTxMaster_Meas_Type_6FA_ecu0_0()                  (RRS_EchoData_6FA_ecu0_0_pool.bits.Master_Meas_Type)
#define SetTxMaster_Meas_Type_6FA_ecu0_0(v)                 RRS_EchoData_6FA_ecu0_0_pool.bits.Master_Meas_Type= (v)
#define GetTxEepromErrFlg_6FA_ecu0_0()                      (RRS_EchoData_6FA_ecu0_0_pool.bits.EepromErrFlg)
#define SetTxEepromErrFlg_6FA_ecu0_0(v)                     RRS_EchoData_6FA_ecu0_0_pool.bits.EepromErrFlg= (v)
#define GetTxMaster_Rtm_6FA_ecu0_0()                        (RRS_EchoData_6FA_ecu0_0_pool.bits.Master_Rtm_1|(RRS_EchoData_6FA_ecu0_0_pool.bits.Master_Rtm_2<<8))
#define SetTxMaster_Rtm_6FA_ecu0_0(v)                       RRS_EchoData_6FA_ecu0_0_pool.bits.Master_Rtm_1=(v);RRS_EchoData_6FA_ecu0_0_pool.bits.Master_Rtm_2=((v)>>8);
#define GetTxMaster_Distance_0_6FA_ecu0_0()                 (RRS_EchoData_6FA_ecu0_0_pool.bits.Master_Distance_0_1|(RRS_EchoData_6FA_ecu0_0_pool.bits.Master_Distance_0_2<<8))
#define SetTxMaster_Distance_0_6FA_ecu0_0(v)                RRS_EchoData_6FA_ecu0_0_pool.bits.Master_Distance_0_1=(v);RRS_EchoData_6FA_ecu0_0_pool.bits.Master_Distance_0_2=((v)>>8);
#define GetTxMaster_Distance_1_6FA_ecu0_0()                 (RRS_EchoData_6FA_ecu0_0_pool.bits.Master_Distance_1_1|(RRS_EchoData_6FA_ecu0_0_pool.bits.Master_Distance_1_2<<8))
#define SetTxMaster_Distance_1_6FA_ecu0_0(v)                RRS_EchoData_6FA_ecu0_0_pool.bits.Master_Distance_1_1=(v);RRS_EchoData_6FA_ecu0_0_pool.bits.Master_Distance_1_2=((v)>>8);
#define GetTxMaster_Distance_2_6FA_ecu0_0()                 (RRS_EchoData_6FA_ecu0_0_pool.bits.Master_Distance_2_1|(RRS_EchoData_6FA_ecu0_0_pool.bits.Master_Distance_2_2<<8))
#define SetTxMaster_Distance_2_6FA_ecu0_0(v)                RRS_EchoData_6FA_ecu0_0_pool.bits.Master_Distance_2_1=(v);RRS_EchoData_6FA_ecu0_0_pool.bits.Master_Distance_2_2=((v)>>8);
#define GetTxMaster_Distance_3_6FA_ecu0_0()                 (RRS_EchoData_6FA_ecu0_0_pool.bits.Master_Distance_3_1|(RRS_EchoData_6FA_ecu0_0_pool.bits.Master_Distance_3_2<<8))
#define SetTxMaster_Distance_3_6FA_ecu0_0(v)                RRS_EchoData_6FA_ecu0_0_pool.bits.Master_Distance_3_1=(v);RRS_EchoData_6FA_ecu0_0_pool.bits.Master_Distance_3_2=((v)>>8);
#define GetTxMaster_EchoHigh_0_6FA_ecu0_0()                 (RRS_EchoData_6FA_ecu0_0_pool.bits.Master_EchoHigh_0_1|(RRS_EchoData_6FA_ecu0_0_pool.bits.Master_EchoHigh_0_2<<8))
#define SetTxMaster_EchoHigh_0_6FA_ecu0_0(v)                RRS_EchoData_6FA_ecu0_0_pool.bits.Master_EchoHigh_0_1=(v);RRS_EchoData_6FA_ecu0_0_pool.bits.Master_EchoHigh_0_2=((v)>>8);
#define GetTxMaster_EchoHigh_1_6FA_ecu0_0()                 (RRS_EchoData_6FA_ecu0_0_pool.bits.Master_EchoHigh_1_1|(RRS_EchoData_6FA_ecu0_0_pool.bits.Master_EchoHigh_1_2<<8))
#define SetTxMaster_EchoHigh_1_6FA_ecu0_0(v)                RRS_EchoData_6FA_ecu0_0_pool.bits.Master_EchoHigh_1_1=(v);RRS_EchoData_6FA_ecu0_0_pool.bits.Master_EchoHigh_1_2=((v)>>8);
#define GetTxMaster_EchoHigh_2_6FA_ecu0_0()                 (RRS_EchoData_6FA_ecu0_0_pool.bits.Master_EchoHigh_2_1|(RRS_EchoData_6FA_ecu0_0_pool.bits.Master_EchoHigh_2_2<<8))
#define SetTxMaster_EchoHigh_2_6FA_ecu0_0(v)                RRS_EchoData_6FA_ecu0_0_pool.bits.Master_EchoHigh_2_1=(v);RRS_EchoData_6FA_ecu0_0_pool.bits.Master_EchoHigh_2_2=((v)>>8);
#define GetTxMaster_EchoHigh_3_6FA_ecu0_0()                 (RRS_EchoData_6FA_ecu0_0_pool.bits.Master_EchoHigh_3_1|(RRS_EchoData_6FA_ecu0_0_pool.bits.Master_EchoHigh_3_2<<8))
#define SetTxMaster_EchoHigh_3_6FA_ecu0_0(v)                RRS_EchoData_6FA_ecu0_0_pool.bits.Master_EchoHigh_3_1=(v);RRS_EchoData_6FA_ecu0_0_pool.bits.Master_EchoHigh_3_2=((v)>>8);
#define GetTxLeftListen_Distance_0_6FA_ecu0_0()             (RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_Distance_0_1|(RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_Distance_0_2<<8))
#define SetTxLeftListen_Distance_0_6FA_ecu0_0(v)            RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_Distance_0_1=(v);RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_Distance_0_2=((v)>>8);
#define GetTxLeftListen_Distance_1_6FA_ecu0_0()             (RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_Distance_1_1|(RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_Distance_1_2<<8))
#define SetTxLeftListen_Distance_1_6FA_ecu0_0(v)            RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_Distance_1_1=(v);RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_Distance_1_2=((v)>>8);
#define GetTxLeftListen_Distance_2_6FA_ecu0_0()             (RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_Distance_2_1|(RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_Distance_2_2<<8))
#define SetTxLeftListen_Distance_2_6FA_ecu0_0(v)            RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_Distance_2_1=(v);RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_Distance_2_2=((v)>>8);
#define GetTxLeftListen_Distance_3_6FA_ecu0_0()             (RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_Distance_3_1|(RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_Distance_3_2<<8))
#define SetTxLeftListen_Distance_3_6FA_ecu0_0(v)            RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_Distance_3_1=(v);RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_Distance_3_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_0_6FA_ecu0_0()             (RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_EchoHigh_0_1|(RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_EchoHigh_0_2<<8))
#define SetTxLeftListen_EchoHigh_0_6FA_ecu0_0(v)            RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_EchoHigh_0_1=(v);RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_EchoHigh_0_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_1_6FA_ecu0_0()             (RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_EchoHigh_1_1|(RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_EchoHigh_1_2<<8))
#define SetTxLeftListen_EchoHigh_1_6FA_ecu0_0(v)            RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_EchoHigh_1_1=(v);RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_EchoHigh_1_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_2_6FA_ecu0_0()             (RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_EchoHigh_2_1|(RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_EchoHigh_2_2<<8))
#define SetTxLeftListen_EchoHigh_2_6FA_ecu0_0(v)            RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_EchoHigh_2_1=(v);RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_EchoHigh_2_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_3_6FA_ecu0_0()             (RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_EchoHigh_3_1|(RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_EchoHigh_3_2<<8))
#define SetTxLeftListen_EchoHigh_3_6FA_ecu0_0(v)            RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_EchoHigh_3_1=(v);RRS_EchoData_6FA_ecu0_0_pool.bits.LeftListen_EchoHigh_3_2=((v)>>8);
#define GetTxRightListen_Distance_0_6FA_ecu0_0()            (RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_Distance_0_1|(RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_Distance_0_2<<8))
#define SetTxRightListen_Distance_0_6FA_ecu0_0(v)           RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_Distance_0_1=(v);RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_Distance_0_2=((v)>>8);
#define GetTxRightListen_Distance_1_6FA_ecu0_0()            (RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_Distance_1_1|(RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_Distance_1_2<<8))
#define SetTxRightListen_Distance_1_6FA_ecu0_0(v)           RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_Distance_1_1=(v);RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_Distance_1_2=((v)>>8);
#define GetTxRightListen_Distance_2_6FA_ecu0_0()            (RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_Distance_2_1|(RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_Distance_2_2<<8))
#define SetTxRightListen_Distance_2_6FA_ecu0_0(v)           RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_Distance_2_1=(v);RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_Distance_2_2=((v)>>8);
#define GetTxRightListen_Distance_3_6FA_ecu0_0()            (RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_Distance_3_1|(RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_Distance_3_2<<8))
#define SetTxRightListen_Distance_3_6FA_ecu0_0(v)           RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_Distance_3_1=(v);RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_Distance_3_2=((v)>>8);
#define GetTxRightListen_EchoHigh_0_6FA_ecu0_0()            (RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_EchoHigh_0_1|(RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_EchoHigh_0_2<<8))
#define SetTxRightListen_EchoHigh_0_6FA_ecu0_0(v)           RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_EchoHigh_0_1=(v);RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_EchoHigh_0_2=((v)>>8);
#define GetTxRightListen_EchoHigh_1_6FA_ecu0_0()            (RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_EchoHigh_1_1|(RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_EchoHigh_1_2<<8))
#define SetTxRightListen_EchoHigh_1_6FA_ecu0_0(v)           RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_EchoHigh_1_1=(v);RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_EchoHigh_1_2=((v)>>8);
#define GetTxRightListen_EchoHigh_2_6FA_ecu0_0()            (RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_EchoHigh_2_1|(RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_EchoHigh_2_2<<8))
#define SetTxRightListen_EchoHigh_2_6FA_ecu0_0(v)           RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_EchoHigh_2_1=(v);RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_EchoHigh_2_2=((v)>>8);
#define GetTxRightListen_EchoHigh_3_6FA_ecu0_0()            (RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_EchoHigh_3_1|(RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_EchoHigh_3_2<<8))
#define SetTxRightListen_EchoHigh_3_6FA_ecu0_0(v)           RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_EchoHigh_3_1=(v);RRS_EchoData_6FA_ecu0_0_pool.bits.RightListen_EchoHigh_3_2=((v)>>8);
#define GetTxMasterEchoCnt_6FA_ecu0_0()                     (RRS_EchoData_6FA_ecu0_0_pool.bits.MasterEchoCnt)
#define SetTxMasterEchoCnt_6FA_ecu0_0(v)                    RRS_EchoData_6FA_ecu0_0_pool.bits.MasterEchoCnt= (v)
#define GetTxLeftEchoCnt_6FA_ecu0_0()                       (RRS_EchoData_6FA_ecu0_0_pool.bits.LeftEchoCnt)
#define SetTxLeftEchoCnt_6FA_ecu0_0(v)                      RRS_EchoData_6FA_ecu0_0_pool.bits.LeftEchoCnt= (v)
#define GetTxRightEchoCnt_6FA_ecu0_0()                      (RRS_EchoData_6FA_ecu0_0_pool.bits.RightEchoCnt)
#define SetTxRightEchoCnt_6FA_ecu0_0(v)                     RRS_EchoData_6FA_ecu0_0_pool.bits.RightEchoCnt= (v)
#define GetTxNFD_Flag_6FA_ecu0_0()                          (RRS_EchoData_6FA_ecu0_0_pool.bits.NFD_Flag)
#define SetTxNFD_Flag_6FA_ecu0_0(v)                         RRS_EchoData_6FA_ecu0_0_pool.bits.NFD_Flag= (v)
#define GetTxNFD_Dis_6FA_ecu0_0()                           (RRS_EchoData_6FA_ecu0_0_pool.bits.NFD_Dis_1|(RRS_EchoData_6FA_ecu0_0_pool.bits.NFD_Dis_2<<8))
#define SetTxNFD_Dis_6FA_ecu0_0(v)                          RRS_EchoData_6FA_ecu0_0_pool.bits.NFD_Dis_1=(v);RRS_EchoData_6FA_ecu0_0_pool.bits.NFD_Dis_2=((v)>>8);
#define GetTxRing_Fre_6FA_ecu0_0()                          (RRS_EchoData_6FA_ecu0_0_pool.bits.Ring_Fre_1|(RRS_EchoData_6FA_ecu0_0_pool.bits.Ring_Fre_2<<8))
#define SetTxRing_Fre_6FA_ecu0_0(v)                         RRS_EchoData_6FA_ecu0_0_pool.bits.Ring_Fre_1=(v);RRS_EchoData_6FA_ecu0_0_pool.bits.Ring_Fre_2=((v)>>8);
#define GetTxNoiseCnt_6FA_ecu0_0()                          (RRS_EchoData_6FA_ecu0_0_pool.bits.NoiseCnt)
#define SetTxNoiseCnt_6FA_ecu0_0(v)                         RRS_EchoData_6FA_ecu0_0_pool.bits.NoiseCnt= (v)
#define GetTxNoiseSum_6FA_ecu0_0()                          (RRS_EchoData_6FA_ecu0_0_pool.bits.NoiseSum_1|(RRS_EchoData_6FA_ecu0_0_pool.bits.NoiseSum_2<<4))
#define SetTxNoiseSum_6FA_ecu0_0(v)                         RRS_EchoData_6FA_ecu0_0_pool.bits.NoiseSum_1=(v);RRS_EchoData_6FA_ecu0_0_pool.bits.NoiseSum_2=((v)>>4);
#define GetTxUpdateFlg_6FB_ecu0_0()                         (RR_EchoData_6FB_ecu0_0_pool.bits.UpdateFlg)
#define SetTxUpdateFlg_6FB_ecu0_0(v)                        RR_EchoData_6FB_ecu0_0_pool.bits.UpdateFlg= (v)
#define GetTxSnsWorkSts_6FB_ecu0_0()                        (RR_EchoData_6FB_ecu0_0_pool.bits.SnsWorkSts)
#define SetTxSnsWorkSts_6FB_ecu0_0(v)                       RR_EchoData_6FB_ecu0_0_pool.bits.SnsWorkSts= (v)
#define GetTxMaster_Meas_Type_6FB_ecu0_0()                  (RR_EchoData_6FB_ecu0_0_pool.bits.Master_Meas_Type)
#define SetTxMaster_Meas_Type_6FB_ecu0_0(v)                 RR_EchoData_6FB_ecu0_0_pool.bits.Master_Meas_Type= (v)
#define GetTxEepromErrFlg_6FB_ecu0_0()                      (RR_EchoData_6FB_ecu0_0_pool.bits.EepromErrFlg)
#define SetTxEepromErrFlg_6FB_ecu0_0(v)                     RR_EchoData_6FB_ecu0_0_pool.bits.EepromErrFlg= (v)
#define GetTxMaster_Rtm_6FB_ecu0_0()                        (RR_EchoData_6FB_ecu0_0_pool.bits.Master_Rtm_1|(RR_EchoData_6FB_ecu0_0_pool.bits.Master_Rtm_2<<8))
#define SetTxMaster_Rtm_6FB_ecu0_0(v)                       RR_EchoData_6FB_ecu0_0_pool.bits.Master_Rtm_1=(v);RR_EchoData_6FB_ecu0_0_pool.bits.Master_Rtm_2=((v)>>8);
#define GetTxMaster_Distance_0_6FB_ecu0_0()                 (RR_EchoData_6FB_ecu0_0_pool.bits.Master_Distance_0_1|(RR_EchoData_6FB_ecu0_0_pool.bits.Master_Distance_0_2<<8))
#define SetTxMaster_Distance_0_6FB_ecu0_0(v)                RR_EchoData_6FB_ecu0_0_pool.bits.Master_Distance_0_1=(v);RR_EchoData_6FB_ecu0_0_pool.bits.Master_Distance_0_2=((v)>>8);
#define GetTxMaster_Distance_1_6FB_ecu0_0()                 (RR_EchoData_6FB_ecu0_0_pool.bits.Master_Distance_1_1|(RR_EchoData_6FB_ecu0_0_pool.bits.Master_Distance_1_2<<8))
#define SetTxMaster_Distance_1_6FB_ecu0_0(v)                RR_EchoData_6FB_ecu0_0_pool.bits.Master_Distance_1_1=(v);RR_EchoData_6FB_ecu0_0_pool.bits.Master_Distance_1_2=((v)>>8);
#define GetTxMaster_Distance_2_6FB_ecu0_0()                 (RR_EchoData_6FB_ecu0_0_pool.bits.Master_Distance_2_1|(RR_EchoData_6FB_ecu0_0_pool.bits.Master_Distance_2_2<<8))
#define SetTxMaster_Distance_2_6FB_ecu0_0(v)                RR_EchoData_6FB_ecu0_0_pool.bits.Master_Distance_2_1=(v);RR_EchoData_6FB_ecu0_0_pool.bits.Master_Distance_2_2=((v)>>8);
#define GetTxMaster_Distance_3_6FB_ecu0_0()                 (RR_EchoData_6FB_ecu0_0_pool.bits.Master_Distance_3_1|(RR_EchoData_6FB_ecu0_0_pool.bits.Master_Distance_3_2<<8))
#define SetTxMaster_Distance_3_6FB_ecu0_0(v)                RR_EchoData_6FB_ecu0_0_pool.bits.Master_Distance_3_1=(v);RR_EchoData_6FB_ecu0_0_pool.bits.Master_Distance_3_2=((v)>>8);
#define GetTxMaster_EchoHigh_0_6FB_ecu0_0()                 (RR_EchoData_6FB_ecu0_0_pool.bits.Master_EchoHigh_0_1|(RR_EchoData_6FB_ecu0_0_pool.bits.Master_EchoHigh_0_2<<8))
#define SetTxMaster_EchoHigh_0_6FB_ecu0_0(v)                RR_EchoData_6FB_ecu0_0_pool.bits.Master_EchoHigh_0_1=(v);RR_EchoData_6FB_ecu0_0_pool.bits.Master_EchoHigh_0_2=((v)>>8);
#define GetTxMaster_EchoHigh_1_6FB_ecu0_0()                 (RR_EchoData_6FB_ecu0_0_pool.bits.Master_EchoHigh_1_1|(RR_EchoData_6FB_ecu0_0_pool.bits.Master_EchoHigh_1_2<<8))
#define SetTxMaster_EchoHigh_1_6FB_ecu0_0(v)                RR_EchoData_6FB_ecu0_0_pool.bits.Master_EchoHigh_1_1=(v);RR_EchoData_6FB_ecu0_0_pool.bits.Master_EchoHigh_1_2=((v)>>8);
#define GetTxMaster_EchoHigh_2_6FB_ecu0_0()                 (RR_EchoData_6FB_ecu0_0_pool.bits.Master_EchoHigh_2_1|(RR_EchoData_6FB_ecu0_0_pool.bits.Master_EchoHigh_2_2<<8))
#define SetTxMaster_EchoHigh_2_6FB_ecu0_0(v)                RR_EchoData_6FB_ecu0_0_pool.bits.Master_EchoHigh_2_1=(v);RR_EchoData_6FB_ecu0_0_pool.bits.Master_EchoHigh_2_2=((v)>>8);
#define GetTxMaster_EchoHigh_3_6FB_ecu0_0()                 (RR_EchoData_6FB_ecu0_0_pool.bits.Master_EchoHigh_3_1|(RR_EchoData_6FB_ecu0_0_pool.bits.Master_EchoHigh_3_2<<8))
#define SetTxMaster_EchoHigh_3_6FB_ecu0_0(v)                RR_EchoData_6FB_ecu0_0_pool.bits.Master_EchoHigh_3_1=(v);RR_EchoData_6FB_ecu0_0_pool.bits.Master_EchoHigh_3_2=((v)>>8);
#define GetTxLeftListen_Distance_0_6FB_ecu0_0()             (RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_Distance_0_1|(RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_Distance_0_2<<8))
#define SetTxLeftListen_Distance_0_6FB_ecu0_0(v)            RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_Distance_0_1=(v);RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_Distance_0_2=((v)>>8);
#define GetTxLeftListen_Distance_1_6FB_ecu0_0()             (RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_Distance_1_1|(RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_Distance_1_2<<8))
#define SetTxLeftListen_Distance_1_6FB_ecu0_0(v)            RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_Distance_1_1=(v);RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_Distance_1_2=((v)>>8);
#define GetTxLeftListen_Distance_2_6FB_ecu0_0()             (RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_Distance_2_1|(RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_Distance_2_2<<8))
#define SetTxLeftListen_Distance_2_6FB_ecu0_0(v)            RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_Distance_2_1=(v);RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_Distance_2_2=((v)>>8);
#define GetTxLeftListen_Distance_3_6FB_ecu0_0()             (RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_Distance_3_1|(RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_Distance_3_2<<8))
#define SetTxLeftListen_Distance_3_6FB_ecu0_0(v)            RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_Distance_3_1=(v);RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_Distance_3_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_0_6FB_ecu0_0()             (RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_EchoHigh_0_1|(RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_EchoHigh_0_2<<8))
#define SetTxLeftListen_EchoHigh_0_6FB_ecu0_0(v)            RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_EchoHigh_0_1=(v);RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_EchoHigh_0_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_1_6FB_ecu0_0()             (RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_EchoHigh_1_1|(RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_EchoHigh_1_2<<8))
#define SetTxLeftListen_EchoHigh_1_6FB_ecu0_0(v)            RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_EchoHigh_1_1=(v);RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_EchoHigh_1_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_2_6FB_ecu0_0()             (RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_EchoHigh_2_1|(RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_EchoHigh_2_2<<8))
#define SetTxLeftListen_EchoHigh_2_6FB_ecu0_0(v)            RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_EchoHigh_2_1=(v);RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_EchoHigh_2_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_3_6FB_ecu0_0()             (RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_EchoHigh_3_1|(RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_EchoHigh_3_2<<8))
#define SetTxLeftListen_EchoHigh_3_6FB_ecu0_0(v)            RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_EchoHigh_3_1=(v);RR_EchoData_6FB_ecu0_0_pool.bits.LeftListen_EchoHigh_3_2=((v)>>8);
#define GetTxRightListen_Distance_0_6FB_ecu0_0()            (RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_Distance_0_1|(RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_Distance_0_2<<8))
#define SetTxRightListen_Distance_0_6FB_ecu0_0(v)           RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_Distance_0_1=(v);RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_Distance_0_2=((v)>>8);
#define GetTxRightListen_Distance_1_6FB_ecu0_0()            (RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_Distance_1_1|(RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_Distance_1_2<<8))
#define SetTxRightListen_Distance_1_6FB_ecu0_0(v)           RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_Distance_1_1=(v);RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_Distance_1_2=((v)>>8);
#define GetTxRightListen_Distance_2_6FB_ecu0_0()            (RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_Distance_2_1|(RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_Distance_2_2<<8))
#define SetTxRightListen_Distance_2_6FB_ecu0_0(v)           RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_Distance_2_1=(v);RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_Distance_2_2=((v)>>8);
#define GetTxRightListen_Distance_3_6FB_ecu0_0()            (RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_Distance_3_1|(RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_Distance_3_2<<8))
#define SetTxRightListen_Distance_3_6FB_ecu0_0(v)           RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_Distance_3_1=(v);RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_Distance_3_2=((v)>>8);
#define GetTxRightListen_EchoHigh_0_6FB_ecu0_0()            (RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_EchoHigh_0_1|(RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_EchoHigh_0_2<<8))
#define SetTxRightListen_EchoHigh_0_6FB_ecu0_0(v)           RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_EchoHigh_0_1=(v);RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_EchoHigh_0_2=((v)>>8);
#define GetTxRightListen_EchoHigh_1_6FB_ecu0_0()            (RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_EchoHigh_1_1|(RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_EchoHigh_1_2<<8))
#define SetTxRightListen_EchoHigh_1_6FB_ecu0_0(v)           RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_EchoHigh_1_1=(v);RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_EchoHigh_1_2=((v)>>8);
#define GetTxRightListen_EchoHigh_2_6FB_ecu0_0()            (RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_EchoHigh_2_1|(RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_EchoHigh_2_2<<8))
#define SetTxRightListen_EchoHigh_2_6FB_ecu0_0(v)           RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_EchoHigh_2_1=(v);RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_EchoHigh_2_2=((v)>>8);
#define GetTxRightListen_EchoHigh_3_6FB_ecu0_0()            (RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_EchoHigh_3_1|(RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_EchoHigh_3_2<<8))
#define SetTxRightListen_EchoHigh_3_6FB_ecu0_0(v)           RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_EchoHigh_3_1=(v);RR_EchoData_6FB_ecu0_0_pool.bits.RightListen_EchoHigh_3_2=((v)>>8);
#define GetTxMasterEchoCnt_6FB_ecu0_0()                     (RR_EchoData_6FB_ecu0_0_pool.bits.MasterEchoCnt)
#define SetTxMasterEchoCnt_6FB_ecu0_0(v)                    RR_EchoData_6FB_ecu0_0_pool.bits.MasterEchoCnt= (v)
#define GetTxLeftEchoCnt_6FB_ecu0_0()                       (RR_EchoData_6FB_ecu0_0_pool.bits.LeftEchoCnt)
#define SetTxLeftEchoCnt_6FB_ecu0_0(v)                      RR_EchoData_6FB_ecu0_0_pool.bits.LeftEchoCnt= (v)
#define GetTxRightEchoCnt_6FB_ecu0_0()                      (RR_EchoData_6FB_ecu0_0_pool.bits.RightEchoCnt)
#define SetTxRightEchoCnt_6FB_ecu0_0(v)                     RR_EchoData_6FB_ecu0_0_pool.bits.RightEchoCnt= (v)
#define GetTxNFD_Flag_6FB_ecu0_0()                          (RR_EchoData_6FB_ecu0_0_pool.bits.NFD_Flag)
#define SetTxNFD_Flag_6FB_ecu0_0(v)                         RR_EchoData_6FB_ecu0_0_pool.bits.NFD_Flag= (v)
#define GetTxNFD_Dis_6FB_ecu0_0()                           (RR_EchoData_6FB_ecu0_0_pool.bits.NFD_Dis_1|(RR_EchoData_6FB_ecu0_0_pool.bits.NFD_Dis_2<<8))
#define SetTxNFD_Dis_6FB_ecu0_0(v)                          RR_EchoData_6FB_ecu0_0_pool.bits.NFD_Dis_1=(v);RR_EchoData_6FB_ecu0_0_pool.bits.NFD_Dis_2=((v)>>8);
#define GetTxRing_Fre_6FB_ecu0_0()                          (RR_EchoData_6FB_ecu0_0_pool.bits.Ring_Fre_1|(RR_EchoData_6FB_ecu0_0_pool.bits.Ring_Fre_2<<8))
#define SetTxRing_Fre_6FB_ecu0_0(v)                         RR_EchoData_6FB_ecu0_0_pool.bits.Ring_Fre_1=(v);RR_EchoData_6FB_ecu0_0_pool.bits.Ring_Fre_2=((v)>>8);
#define GetTxNoiseCnt_6FB_ecu0_0()                          (RR_EchoData_6FB_ecu0_0_pool.bits.NoiseCnt)
#define SetTxNoiseCnt_6FB_ecu0_0(v)                         RR_EchoData_6FB_ecu0_0_pool.bits.NoiseCnt= (v)
#define GetTxNoiseSum_6FB_ecu0_0()                          (RR_EchoData_6FB_ecu0_0_pool.bits.NoiseSum_1|(RR_EchoData_6FB_ecu0_0_pool.bits.NoiseSum_2<<4))
#define SetTxNoiseSum_6FB_ecu0_0(v)                         RR_EchoData_6FB_ecu0_0_pool.bits.NoiseSum_1=(v);RR_EchoData_6FB_ecu0_0_pool.bits.NoiseSum_2=((v)>>4);
#define GetTxUpdateFlg_6FC_ecu0_0()                         (RMR_EchoData_6FC_ecu0_0_pool.bits.UpdateFlg)
#define SetTxUpdateFlg_6FC_ecu0_0(v)                        RMR_EchoData_6FC_ecu0_0_pool.bits.UpdateFlg= (v)
#define GetTxSnsWorkSts_6FC_ecu0_0()                        (RMR_EchoData_6FC_ecu0_0_pool.bits.SnsWorkSts)
#define SetTxSnsWorkSts_6FC_ecu0_0(v)                       RMR_EchoData_6FC_ecu0_0_pool.bits.SnsWorkSts= (v)
#define GetTxMaster_Meas_Type_6FC_ecu0_0()                  (RMR_EchoData_6FC_ecu0_0_pool.bits.Master_Meas_Type)
#define SetTxMaster_Meas_Type_6FC_ecu0_0(v)                 RMR_EchoData_6FC_ecu0_0_pool.bits.Master_Meas_Type= (v)
#define GetTxEepromErrFlg_6FC_ecu0_0()                      (RMR_EchoData_6FC_ecu0_0_pool.bits.EepromErrFlg)
#define SetTxEepromErrFlg_6FC_ecu0_0(v)                     RMR_EchoData_6FC_ecu0_0_pool.bits.EepromErrFlg= (v)
#define GetTxMaster_Rtm_6FC_ecu0_0()                        (RMR_EchoData_6FC_ecu0_0_pool.bits.Master_Rtm_1|(RMR_EchoData_6FC_ecu0_0_pool.bits.Master_Rtm_2<<8))
#define SetTxMaster_Rtm_6FC_ecu0_0(v)                       RMR_EchoData_6FC_ecu0_0_pool.bits.Master_Rtm_1=(v);RMR_EchoData_6FC_ecu0_0_pool.bits.Master_Rtm_2=((v)>>8);
#define GetTxMaster_Distance_0_6FC_ecu0_0()                 (RMR_EchoData_6FC_ecu0_0_pool.bits.Master_Distance_0_1|(RMR_EchoData_6FC_ecu0_0_pool.bits.Master_Distance_0_2<<8))
#define SetTxMaster_Distance_0_6FC_ecu0_0(v)                RMR_EchoData_6FC_ecu0_0_pool.bits.Master_Distance_0_1=(v);RMR_EchoData_6FC_ecu0_0_pool.bits.Master_Distance_0_2=((v)>>8);
#define GetTxMaster_Distance_1_6FC_ecu0_0()                 (RMR_EchoData_6FC_ecu0_0_pool.bits.Master_Distance_1_1|(RMR_EchoData_6FC_ecu0_0_pool.bits.Master_Distance_1_2<<8))
#define SetTxMaster_Distance_1_6FC_ecu0_0(v)                RMR_EchoData_6FC_ecu0_0_pool.bits.Master_Distance_1_1=(v);RMR_EchoData_6FC_ecu0_0_pool.bits.Master_Distance_1_2=((v)>>8);
#define GetTxMaster_Distance_2_6FC_ecu0_0()                 (RMR_EchoData_6FC_ecu0_0_pool.bits.Master_Distance_2_1|(RMR_EchoData_6FC_ecu0_0_pool.bits.Master_Distance_2_2<<8))
#define SetTxMaster_Distance_2_6FC_ecu0_0(v)                RMR_EchoData_6FC_ecu0_0_pool.bits.Master_Distance_2_1=(v);RMR_EchoData_6FC_ecu0_0_pool.bits.Master_Distance_2_2=((v)>>8);
#define GetTxMaster_Distance_3_6FC_ecu0_0()                 (RMR_EchoData_6FC_ecu0_0_pool.bits.Master_Distance_3_1|(RMR_EchoData_6FC_ecu0_0_pool.bits.Master_Distance_3_2<<8))
#define SetTxMaster_Distance_3_6FC_ecu0_0(v)                RMR_EchoData_6FC_ecu0_0_pool.bits.Master_Distance_3_1=(v);RMR_EchoData_6FC_ecu0_0_pool.bits.Master_Distance_3_2=((v)>>8);
#define GetTxMaster_EchoHigh_0_6FC_ecu0_0()                 (RMR_EchoData_6FC_ecu0_0_pool.bits.Master_EchoHigh_0_1|(RMR_EchoData_6FC_ecu0_0_pool.bits.Master_EchoHigh_0_2<<8))
#define SetTxMaster_EchoHigh_0_6FC_ecu0_0(v)                RMR_EchoData_6FC_ecu0_0_pool.bits.Master_EchoHigh_0_1=(v);RMR_EchoData_6FC_ecu0_0_pool.bits.Master_EchoHigh_0_2=((v)>>8);
#define GetTxMaster_EchoHigh_1_6FC_ecu0_0()                 (RMR_EchoData_6FC_ecu0_0_pool.bits.Master_EchoHigh_1_1|(RMR_EchoData_6FC_ecu0_0_pool.bits.Master_EchoHigh_1_2<<8))
#define SetTxMaster_EchoHigh_1_6FC_ecu0_0(v)                RMR_EchoData_6FC_ecu0_0_pool.bits.Master_EchoHigh_1_1=(v);RMR_EchoData_6FC_ecu0_0_pool.bits.Master_EchoHigh_1_2=((v)>>8);
#define GetTxMaster_EchoHigh_2_6FC_ecu0_0()                 (RMR_EchoData_6FC_ecu0_0_pool.bits.Master_EchoHigh_2_1|(RMR_EchoData_6FC_ecu0_0_pool.bits.Master_EchoHigh_2_2<<8))
#define SetTxMaster_EchoHigh_2_6FC_ecu0_0(v)                RMR_EchoData_6FC_ecu0_0_pool.bits.Master_EchoHigh_2_1=(v);RMR_EchoData_6FC_ecu0_0_pool.bits.Master_EchoHigh_2_2=((v)>>8);
#define GetTxMaster_EchoHigh_3_6FC_ecu0_0()                 (RMR_EchoData_6FC_ecu0_0_pool.bits.Master_EchoHigh_3_1|(RMR_EchoData_6FC_ecu0_0_pool.bits.Master_EchoHigh_3_2<<8))
#define SetTxMaster_EchoHigh_3_6FC_ecu0_0(v)                RMR_EchoData_6FC_ecu0_0_pool.bits.Master_EchoHigh_3_1=(v);RMR_EchoData_6FC_ecu0_0_pool.bits.Master_EchoHigh_3_2=((v)>>8);
#define GetTxLeftListen_Distance_0_6FC_ecu0_0()             (RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_Distance_0_1|(RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_Distance_0_2<<8))
#define SetTxLeftListen_Distance_0_6FC_ecu0_0(v)            RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_Distance_0_1=(v);RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_Distance_0_2=((v)>>8);
#define GetTxLeftListen_Distance_1_6FC_ecu0_0()             (RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_Distance_1_1|(RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_Distance_1_2<<8))
#define SetTxLeftListen_Distance_1_6FC_ecu0_0(v)            RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_Distance_1_1=(v);RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_Distance_1_2=((v)>>8);
#define GetTxLeftListen_Distance_2_6FC_ecu0_0()             (RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_Distance_2_1|(RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_Distance_2_2<<8))
#define SetTxLeftListen_Distance_2_6FC_ecu0_0(v)            RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_Distance_2_1=(v);RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_Distance_2_2=((v)>>8);
#define GetTxLeftListen_Distance_3_6FC_ecu0_0()             (RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_Distance_3_1|(RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_Distance_3_2<<8))
#define SetTxLeftListen_Distance_3_6FC_ecu0_0(v)            RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_Distance_3_1=(v);RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_Distance_3_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_0_6FC_ecu0_0()             (RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_EchoHigh_0_1|(RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_EchoHigh_0_2<<8))
#define SetTxLeftListen_EchoHigh_0_6FC_ecu0_0(v)            RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_EchoHigh_0_1=(v);RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_EchoHigh_0_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_1_6FC_ecu0_0()             (RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_EchoHigh_1_1|(RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_EchoHigh_1_2<<8))
#define SetTxLeftListen_EchoHigh_1_6FC_ecu0_0(v)            RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_EchoHigh_1_1=(v);RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_EchoHigh_1_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_2_6FC_ecu0_0()             (RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_EchoHigh_2_1|(RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_EchoHigh_2_2<<8))
#define SetTxLeftListen_EchoHigh_2_6FC_ecu0_0(v)            RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_EchoHigh_2_1=(v);RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_EchoHigh_2_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_3_6FC_ecu0_0()             (RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_EchoHigh_3_1|(RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_EchoHigh_3_2<<8))
#define SetTxLeftListen_EchoHigh_3_6FC_ecu0_0(v)            RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_EchoHigh_3_1=(v);RMR_EchoData_6FC_ecu0_0_pool.bits.LeftListen_EchoHigh_3_2=((v)>>8);
#define GetTxRightListen_Distance_0_6FC_ecu0_0()            (RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_Distance_0_1|(RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_Distance_0_2<<8))
#define SetTxRightListen_Distance_0_6FC_ecu0_0(v)           RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_Distance_0_1=(v);RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_Distance_0_2=((v)>>8);
#define GetTxRightListen_Distance_1_6FC_ecu0_0()            (RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_Distance_1_1|(RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_Distance_1_2<<8))
#define SetTxRightListen_Distance_1_6FC_ecu0_0(v)           RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_Distance_1_1=(v);RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_Distance_1_2=((v)>>8);
#define GetTxRightListen_Distance_2_6FC_ecu0_0()            (RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_Distance_2_1|(RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_Distance_2_2<<8))
#define SetTxRightListen_Distance_2_6FC_ecu0_0(v)           RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_Distance_2_1=(v);RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_Distance_2_2=((v)>>8);
#define GetTxRightListen_Distance_3_6FC_ecu0_0()            (RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_Distance_3_1|(RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_Distance_3_2<<8))
#define SetTxRightListen_Distance_3_6FC_ecu0_0(v)           RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_Distance_3_1=(v);RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_Distance_3_2=((v)>>8);
#define GetTxRightListen_EchoHigh_0_6FC_ecu0_0()            (RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_EchoHigh_0_1|(RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_EchoHigh_0_2<<8))
#define SetTxRightListen_EchoHigh_0_6FC_ecu0_0(v)           RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_EchoHigh_0_1=(v);RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_EchoHigh_0_2=((v)>>8);
#define GetTxRightListen_EchoHigh_1_6FC_ecu0_0()            (RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_EchoHigh_1_1|(RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_EchoHigh_1_2<<8))
#define SetTxRightListen_EchoHigh_1_6FC_ecu0_0(v)           RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_EchoHigh_1_1=(v);RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_EchoHigh_1_2=((v)>>8);
#define GetTxRightListen_EchoHigh_2_6FC_ecu0_0()            (RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_EchoHigh_2_1|(RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_EchoHigh_2_2<<8))
#define SetTxRightListen_EchoHigh_2_6FC_ecu0_0(v)           RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_EchoHigh_2_1=(v);RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_EchoHigh_2_2=((v)>>8);
#define GetTxRightListen_EchoHigh_3_6FC_ecu0_0()            (RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_EchoHigh_3_1|(RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_EchoHigh_3_2<<8))
#define SetTxRightListen_EchoHigh_3_6FC_ecu0_0(v)           RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_EchoHigh_3_1=(v);RMR_EchoData_6FC_ecu0_0_pool.bits.RightListen_EchoHigh_3_2=((v)>>8);
#define GetTxMasterEchoCnt_6FC_ecu0_0()                     (RMR_EchoData_6FC_ecu0_0_pool.bits.MasterEchoCnt)
#define SetTxMasterEchoCnt_6FC_ecu0_0(v)                    RMR_EchoData_6FC_ecu0_0_pool.bits.MasterEchoCnt= (v)
#define GetTxLeftEchoCnt_6FC_ecu0_0()                       (RMR_EchoData_6FC_ecu0_0_pool.bits.LeftEchoCnt)
#define SetTxLeftEchoCnt_6FC_ecu0_0(v)                      RMR_EchoData_6FC_ecu0_0_pool.bits.LeftEchoCnt= (v)
#define GetTxRightEchoCnt_6FC_ecu0_0()                      (RMR_EchoData_6FC_ecu0_0_pool.bits.RightEchoCnt)
#define SetTxRightEchoCnt_6FC_ecu0_0(v)                     RMR_EchoData_6FC_ecu0_0_pool.bits.RightEchoCnt= (v)
#define GetTxNFD_Flag_6FC_ecu0_0()                          (RMR_EchoData_6FC_ecu0_0_pool.bits.NFD_Flag)
#define SetTxNFD_Flag_6FC_ecu0_0(v)                         RMR_EchoData_6FC_ecu0_0_pool.bits.NFD_Flag= (v)
#define GetTxNFD_Dis_6FC_ecu0_0()                           (RMR_EchoData_6FC_ecu0_0_pool.bits.NFD_Dis_1|(RMR_EchoData_6FC_ecu0_0_pool.bits.NFD_Dis_2<<8))
#define SetTxNFD_Dis_6FC_ecu0_0(v)                          RMR_EchoData_6FC_ecu0_0_pool.bits.NFD_Dis_1=(v);RMR_EchoData_6FC_ecu0_0_pool.bits.NFD_Dis_2=((v)>>8);
#define GetTxRing_Fre_6FC_ecu0_0()                          (RMR_EchoData_6FC_ecu0_0_pool.bits.Ring_Fre_1|(RMR_EchoData_6FC_ecu0_0_pool.bits.Ring_Fre_2<<8))
#define SetTxRing_Fre_6FC_ecu0_0(v)                         RMR_EchoData_6FC_ecu0_0_pool.bits.Ring_Fre_1=(v);RMR_EchoData_6FC_ecu0_0_pool.bits.Ring_Fre_2=((v)>>8);
#define GetTxNoiseCnt_6FC_ecu0_0()                          (RMR_EchoData_6FC_ecu0_0_pool.bits.NoiseCnt)
#define SetTxNoiseCnt_6FC_ecu0_0(v)                         RMR_EchoData_6FC_ecu0_0_pool.bits.NoiseCnt= (v)
#define GetTxNoiseSum_6FC_ecu0_0()                          (RMR_EchoData_6FC_ecu0_0_pool.bits.NoiseSum_1|(RMR_EchoData_6FC_ecu0_0_pool.bits.NoiseSum_2<<4))
#define SetTxNoiseSum_6FC_ecu0_0(v)                         RMR_EchoData_6FC_ecu0_0_pool.bits.NoiseSum_1=(v);RMR_EchoData_6FC_ecu0_0_pool.bits.NoiseSum_2=((v)>>4);
#define GetTxUpdateFlg_6FD_ecu0_0()                         (RML_EchoData_6FD_ecu0_0_pool.bits.UpdateFlg)
#define SetTxUpdateFlg_6FD_ecu0_0(v)                        RML_EchoData_6FD_ecu0_0_pool.bits.UpdateFlg= (v)
#define GetTxSnsWorkSts_6FD_ecu0_0()                        (RML_EchoData_6FD_ecu0_0_pool.bits.SnsWorkSts)
#define SetTxSnsWorkSts_6FD_ecu0_0(v)                       RML_EchoData_6FD_ecu0_0_pool.bits.SnsWorkSts= (v)
#define GetTxMaster_Meas_Type_6FD_ecu0_0()                  (RML_EchoData_6FD_ecu0_0_pool.bits.Master_Meas_Type)
#define SetTxMaster_Meas_Type_6FD_ecu0_0(v)                 RML_EchoData_6FD_ecu0_0_pool.bits.Master_Meas_Type= (v)
#define GetTxEepromErrFlg_6FD_ecu0_0()                      (RML_EchoData_6FD_ecu0_0_pool.bits.EepromErrFlg)
#define SetTxEepromErrFlg_6FD_ecu0_0(v)                     RML_EchoData_6FD_ecu0_0_pool.bits.EepromErrFlg= (v)
#define GetTxMaster_Rtm_6FD_ecu0_0()                        (RML_EchoData_6FD_ecu0_0_pool.bits.Master_Rtm_1|(RML_EchoData_6FD_ecu0_0_pool.bits.Master_Rtm_2<<8))
#define SetTxMaster_Rtm_6FD_ecu0_0(v)                       RML_EchoData_6FD_ecu0_0_pool.bits.Master_Rtm_1=(v);RML_EchoData_6FD_ecu0_0_pool.bits.Master_Rtm_2=((v)>>8);
#define GetTxMaster_Distance_0_6FD_ecu0_0()                 (RML_EchoData_6FD_ecu0_0_pool.bits.Master_Distance_0_1|(RML_EchoData_6FD_ecu0_0_pool.bits.Master_Distance_0_2<<8))
#define SetTxMaster_Distance_0_6FD_ecu0_0(v)                RML_EchoData_6FD_ecu0_0_pool.bits.Master_Distance_0_1=(v);RML_EchoData_6FD_ecu0_0_pool.bits.Master_Distance_0_2=((v)>>8);
#define GetTxMaster_Distance_1_6FD_ecu0_0()                 (RML_EchoData_6FD_ecu0_0_pool.bits.Master_Distance_1_1|(RML_EchoData_6FD_ecu0_0_pool.bits.Master_Distance_1_2<<8))
#define SetTxMaster_Distance_1_6FD_ecu0_0(v)                RML_EchoData_6FD_ecu0_0_pool.bits.Master_Distance_1_1=(v);RML_EchoData_6FD_ecu0_0_pool.bits.Master_Distance_1_2=((v)>>8);
#define GetTxMaster_Distance_2_6FD_ecu0_0()                 (RML_EchoData_6FD_ecu0_0_pool.bits.Master_Distance_2_1|(RML_EchoData_6FD_ecu0_0_pool.bits.Master_Distance_2_2<<8))
#define SetTxMaster_Distance_2_6FD_ecu0_0(v)                RML_EchoData_6FD_ecu0_0_pool.bits.Master_Distance_2_1=(v);RML_EchoData_6FD_ecu0_0_pool.bits.Master_Distance_2_2=((v)>>8);
#define GetTxMaster_Distance_3_6FD_ecu0_0()                 (RML_EchoData_6FD_ecu0_0_pool.bits.Master_Distance_3_1|(RML_EchoData_6FD_ecu0_0_pool.bits.Master_Distance_3_2<<8))
#define SetTxMaster_Distance_3_6FD_ecu0_0(v)                RML_EchoData_6FD_ecu0_0_pool.bits.Master_Distance_3_1=(v);RML_EchoData_6FD_ecu0_0_pool.bits.Master_Distance_3_2=((v)>>8);
#define GetTxMaster_EchoHigh_0_6FD_ecu0_0()                 (RML_EchoData_6FD_ecu0_0_pool.bits.Master_EchoHigh_0_1|(RML_EchoData_6FD_ecu0_0_pool.bits.Master_EchoHigh_0_2<<8))
#define SetTxMaster_EchoHigh_0_6FD_ecu0_0(v)                RML_EchoData_6FD_ecu0_0_pool.bits.Master_EchoHigh_0_1=(v);RML_EchoData_6FD_ecu0_0_pool.bits.Master_EchoHigh_0_2=((v)>>8);
#define GetTxMaster_EchoHigh_1_6FD_ecu0_0()                 (RML_EchoData_6FD_ecu0_0_pool.bits.Master_EchoHigh_1_1|(RML_EchoData_6FD_ecu0_0_pool.bits.Master_EchoHigh_1_2<<8))
#define SetTxMaster_EchoHigh_1_6FD_ecu0_0(v)                RML_EchoData_6FD_ecu0_0_pool.bits.Master_EchoHigh_1_1=(v);RML_EchoData_6FD_ecu0_0_pool.bits.Master_EchoHigh_1_2=((v)>>8);
#define GetTxMaster_EchoHigh_2_6FD_ecu0_0()                 (RML_EchoData_6FD_ecu0_0_pool.bits.Master_EchoHigh_2_1|(RML_EchoData_6FD_ecu0_0_pool.bits.Master_EchoHigh_2_2<<8))
#define SetTxMaster_EchoHigh_2_6FD_ecu0_0(v)                RML_EchoData_6FD_ecu0_0_pool.bits.Master_EchoHigh_2_1=(v);RML_EchoData_6FD_ecu0_0_pool.bits.Master_EchoHigh_2_2=((v)>>8);
#define GetTxMaster_EchoHigh_3_6FD_ecu0_0()                 (RML_EchoData_6FD_ecu0_0_pool.bits.Master_EchoHigh_3_1|(RML_EchoData_6FD_ecu0_0_pool.bits.Master_EchoHigh_3_2<<8))
#define SetTxMaster_EchoHigh_3_6FD_ecu0_0(v)                RML_EchoData_6FD_ecu0_0_pool.bits.Master_EchoHigh_3_1=(v);RML_EchoData_6FD_ecu0_0_pool.bits.Master_EchoHigh_3_2=((v)>>8);
#define GetTxLeftListen_Distance_0_6FD_ecu0_0()             (RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_Distance_0_1|(RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_Distance_0_2<<8))
#define SetTxLeftListen_Distance_0_6FD_ecu0_0(v)            RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_Distance_0_1=(v);RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_Distance_0_2=((v)>>8);
#define GetTxLeftListen_Distance_1_6FD_ecu0_0()             (RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_Distance_1_1|(RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_Distance_1_2<<8))
#define SetTxLeftListen_Distance_1_6FD_ecu0_0(v)            RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_Distance_1_1=(v);RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_Distance_1_2=((v)>>8);
#define GetTxLeftListen_Distance_2_6FD_ecu0_0()             (RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_Distance_2_1|(RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_Distance_2_2<<8))
#define SetTxLeftListen_Distance_2_6FD_ecu0_0(v)            RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_Distance_2_1=(v);RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_Distance_2_2=((v)>>8);
#define GetTxLeftListen_Distance_3_6FD_ecu0_0()             (RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_Distance_3_1|(RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_Distance_3_2<<8))
#define SetTxLeftListen_Distance_3_6FD_ecu0_0(v)            RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_Distance_3_1=(v);RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_Distance_3_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_0_6FD_ecu0_0()             (RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_EchoHigh_0_1|(RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_EchoHigh_0_2<<8))
#define SetTxLeftListen_EchoHigh_0_6FD_ecu0_0(v)            RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_EchoHigh_0_1=(v);RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_EchoHigh_0_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_1_6FD_ecu0_0()             (RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_EchoHigh_1_1|(RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_EchoHigh_1_2<<8))
#define SetTxLeftListen_EchoHigh_1_6FD_ecu0_0(v)            RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_EchoHigh_1_1=(v);RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_EchoHigh_1_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_2_6FD_ecu0_0()             (RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_EchoHigh_2_1|(RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_EchoHigh_2_2<<8))
#define SetTxLeftListen_EchoHigh_2_6FD_ecu0_0(v)            RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_EchoHigh_2_1=(v);RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_EchoHigh_2_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_3_6FD_ecu0_0()             (RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_EchoHigh_3_1|(RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_EchoHigh_3_2<<8))
#define SetTxLeftListen_EchoHigh_3_6FD_ecu0_0(v)            RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_EchoHigh_3_1=(v);RML_EchoData_6FD_ecu0_0_pool.bits.LeftListen_EchoHigh_3_2=((v)>>8);
#define GetTxRightListen_Distance_0_6FD_ecu0_0()            (RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_Distance_0_1|(RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_Distance_0_2<<8))
#define SetTxRightListen_Distance_0_6FD_ecu0_0(v)           RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_Distance_0_1=(v);RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_Distance_0_2=((v)>>8);
#define GetTxRightListen_Distance_1_6FD_ecu0_0()            (RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_Distance_1_1|(RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_Distance_1_2<<8))
#define SetTxRightListen_Distance_1_6FD_ecu0_0(v)           RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_Distance_1_1=(v);RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_Distance_1_2=((v)>>8);
#define GetTxRightListen_Distance_2_6FD_ecu0_0()            (RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_Distance_2_1|(RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_Distance_2_2<<8))
#define SetTxRightListen_Distance_2_6FD_ecu0_0(v)           RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_Distance_2_1=(v);RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_Distance_2_2=((v)>>8);
#define GetTxRightListen_Distance_3_6FD_ecu0_0()            (RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_Distance_3_1|(RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_Distance_3_2<<8))
#define SetTxRightListen_Distance_3_6FD_ecu0_0(v)           RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_Distance_3_1=(v);RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_Distance_3_2=((v)>>8);
#define GetTxRightListen_EchoHigh_0_6FD_ecu0_0()            (RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_EchoHigh_0_1|(RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_EchoHigh_0_2<<8))
#define SetTxRightListen_EchoHigh_0_6FD_ecu0_0(v)           RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_EchoHigh_0_1=(v);RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_EchoHigh_0_2=((v)>>8);
#define GetTxRightListen_EchoHigh_1_6FD_ecu0_0()            (RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_EchoHigh_1_1|(RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_EchoHigh_1_2<<8))
#define SetTxRightListen_EchoHigh_1_6FD_ecu0_0(v)           RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_EchoHigh_1_1=(v);RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_EchoHigh_1_2=((v)>>8);
#define GetTxRightListen_EchoHigh_2_6FD_ecu0_0()            (RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_EchoHigh_2_1|(RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_EchoHigh_2_2<<8))
#define SetTxRightListen_EchoHigh_2_6FD_ecu0_0(v)           RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_EchoHigh_2_1=(v);RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_EchoHigh_2_2=((v)>>8);
#define GetTxRightListen_EchoHigh_3_6FD_ecu0_0()            (RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_EchoHigh_3_1|(RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_EchoHigh_3_2<<8))
#define SetTxRightListen_EchoHigh_3_6FD_ecu0_0(v)           RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_EchoHigh_3_1=(v);RML_EchoData_6FD_ecu0_0_pool.bits.RightListen_EchoHigh_3_2=((v)>>8);
#define GetTxMasterEchoCnt_6FD_ecu0_0()                     (RML_EchoData_6FD_ecu0_0_pool.bits.MasterEchoCnt)
#define SetTxMasterEchoCnt_6FD_ecu0_0(v)                    RML_EchoData_6FD_ecu0_0_pool.bits.MasterEchoCnt= (v)
#define GetTxLeftEchoCnt_6FD_ecu0_0()                       (RML_EchoData_6FD_ecu0_0_pool.bits.LeftEchoCnt)
#define SetTxLeftEchoCnt_6FD_ecu0_0(v)                      RML_EchoData_6FD_ecu0_0_pool.bits.LeftEchoCnt= (v)
#define GetTxRightEchoCnt_6FD_ecu0_0()                      (RML_EchoData_6FD_ecu0_0_pool.bits.RightEchoCnt)
#define SetTxRightEchoCnt_6FD_ecu0_0(v)                     RML_EchoData_6FD_ecu0_0_pool.bits.RightEchoCnt= (v)
#define GetTxNFD_Flag_6FD_ecu0_0()                          (RML_EchoData_6FD_ecu0_0_pool.bits.NFD_Flag)
#define SetTxNFD_Flag_6FD_ecu0_0(v)                         RML_EchoData_6FD_ecu0_0_pool.bits.NFD_Flag= (v)
#define GetTxNFD_Dis_6FD_ecu0_0()                           (RML_EchoData_6FD_ecu0_0_pool.bits.NFD_Dis_1|(RML_EchoData_6FD_ecu0_0_pool.bits.NFD_Dis_2<<8))
#define SetTxNFD_Dis_6FD_ecu0_0(v)                          RML_EchoData_6FD_ecu0_0_pool.bits.NFD_Dis_1=(v);RML_EchoData_6FD_ecu0_0_pool.bits.NFD_Dis_2=((v)>>8);
#define GetTxRing_Fre_6FD_ecu0_0()                          (RML_EchoData_6FD_ecu0_0_pool.bits.Ring_Fre_1|(RML_EchoData_6FD_ecu0_0_pool.bits.Ring_Fre_2<<8))
#define SetTxRing_Fre_6FD_ecu0_0(v)                         RML_EchoData_6FD_ecu0_0_pool.bits.Ring_Fre_1=(v);RML_EchoData_6FD_ecu0_0_pool.bits.Ring_Fre_2=((v)>>8);
#define GetTxNoiseCnt_6FD_ecu0_0()                          (RML_EchoData_6FD_ecu0_0_pool.bits.NoiseCnt)
#define SetTxNoiseCnt_6FD_ecu0_0(v)                         RML_EchoData_6FD_ecu0_0_pool.bits.NoiseCnt= (v)
#define GetTxNoiseSum_6FD_ecu0_0()                          (RML_EchoData_6FD_ecu0_0_pool.bits.NoiseSum_1|(RML_EchoData_6FD_ecu0_0_pool.bits.NoiseSum_2<<4))
#define SetTxNoiseSum_6FD_ecu0_0(v)                         RML_EchoData_6FD_ecu0_0_pool.bits.NoiseSum_1=(v);RML_EchoData_6FD_ecu0_0_pool.bits.NoiseSum_2=((v)>>4);
#define GetTxUpdateFlg_6FE_ecu0_0()                         (RL_EchoData_6FE_ecu0_0_pool.bits.UpdateFlg)
#define SetTxUpdateFlg_6FE_ecu0_0(v)                        RL_EchoData_6FE_ecu0_0_pool.bits.UpdateFlg= (v)
#define GetTxSnsWorkSts_6FE_ecu0_0()                        (RL_EchoData_6FE_ecu0_0_pool.bits.SnsWorkSts)
#define SetTxSnsWorkSts_6FE_ecu0_0(v)                       RL_EchoData_6FE_ecu0_0_pool.bits.SnsWorkSts= (v)
#define GetTxMaster_Meas_Type_6FE_ecu0_0()                  (RL_EchoData_6FE_ecu0_0_pool.bits.Master_Meas_Type)
#define SetTxMaster_Meas_Type_6FE_ecu0_0(v)                 RL_EchoData_6FE_ecu0_0_pool.bits.Master_Meas_Type= (v)
#define GetTxEepromErrFlg_6FE_ecu0_0()                      (RL_EchoData_6FE_ecu0_0_pool.bits.EepromErrFlg)
#define SetTxEepromErrFlg_6FE_ecu0_0(v)                     RL_EchoData_6FE_ecu0_0_pool.bits.EepromErrFlg= (v)
#define GetTxMaster_Rtm_6FE_ecu0_0()                        (RL_EchoData_6FE_ecu0_0_pool.bits.Master_Rtm_1|(RL_EchoData_6FE_ecu0_0_pool.bits.Master_Rtm_2<<8))
#define SetTxMaster_Rtm_6FE_ecu0_0(v)                       RL_EchoData_6FE_ecu0_0_pool.bits.Master_Rtm_1=(v);RL_EchoData_6FE_ecu0_0_pool.bits.Master_Rtm_2=((v)>>8);
#define GetTxMaster_Distance_0_6FE_ecu0_0()                 (RL_EchoData_6FE_ecu0_0_pool.bits.Master_Distance_0_1|(RL_EchoData_6FE_ecu0_0_pool.bits.Master_Distance_0_2<<8))
#define SetTxMaster_Distance_0_6FE_ecu0_0(v)                RL_EchoData_6FE_ecu0_0_pool.bits.Master_Distance_0_1=(v);RL_EchoData_6FE_ecu0_0_pool.bits.Master_Distance_0_2=((v)>>8);
#define GetTxMaster_Distance_1_6FE_ecu0_0()                 (RL_EchoData_6FE_ecu0_0_pool.bits.Master_Distance_1_1|(RL_EchoData_6FE_ecu0_0_pool.bits.Master_Distance_1_2<<8))
#define SetTxMaster_Distance_1_6FE_ecu0_0(v)                RL_EchoData_6FE_ecu0_0_pool.bits.Master_Distance_1_1=(v);RL_EchoData_6FE_ecu0_0_pool.bits.Master_Distance_1_2=((v)>>8);
#define GetTxMaster_Distance_2_6FE_ecu0_0()                 (RL_EchoData_6FE_ecu0_0_pool.bits.Master_Distance_2_1|(RL_EchoData_6FE_ecu0_0_pool.bits.Master_Distance_2_2<<8))
#define SetTxMaster_Distance_2_6FE_ecu0_0(v)                RL_EchoData_6FE_ecu0_0_pool.bits.Master_Distance_2_1=(v);RL_EchoData_6FE_ecu0_0_pool.bits.Master_Distance_2_2=((v)>>8);
#define GetTxMaster_Distance_3_6FE_ecu0_0()                 (RL_EchoData_6FE_ecu0_0_pool.bits.Master_Distance_3_1|(RL_EchoData_6FE_ecu0_0_pool.bits.Master_Distance_3_2<<8))
#define SetTxMaster_Distance_3_6FE_ecu0_0(v)                RL_EchoData_6FE_ecu0_0_pool.bits.Master_Distance_3_1=(v);RL_EchoData_6FE_ecu0_0_pool.bits.Master_Distance_3_2=((v)>>8);
#define GetTxMaster_EchoHigh_0_6FE_ecu0_0()                 (RL_EchoData_6FE_ecu0_0_pool.bits.Master_EchoHigh_0_1|(RL_EchoData_6FE_ecu0_0_pool.bits.Master_EchoHigh_0_2<<8))
#define SetTxMaster_EchoHigh_0_6FE_ecu0_0(v)                RL_EchoData_6FE_ecu0_0_pool.bits.Master_EchoHigh_0_1=(v);RL_EchoData_6FE_ecu0_0_pool.bits.Master_EchoHigh_0_2=((v)>>8);
#define GetTxMaster_EchoHigh_1_6FE_ecu0_0()                 (RL_EchoData_6FE_ecu0_0_pool.bits.Master_EchoHigh_1_1|(RL_EchoData_6FE_ecu0_0_pool.bits.Master_EchoHigh_1_2<<8))
#define SetTxMaster_EchoHigh_1_6FE_ecu0_0(v)                RL_EchoData_6FE_ecu0_0_pool.bits.Master_EchoHigh_1_1=(v);RL_EchoData_6FE_ecu0_0_pool.bits.Master_EchoHigh_1_2=((v)>>8);
#define GetTxMaster_EchoHigh_2_6FE_ecu0_0()                 (RL_EchoData_6FE_ecu0_0_pool.bits.Master_EchoHigh_2_1|(RL_EchoData_6FE_ecu0_0_pool.bits.Master_EchoHigh_2_2<<8))
#define SetTxMaster_EchoHigh_2_6FE_ecu0_0(v)                RL_EchoData_6FE_ecu0_0_pool.bits.Master_EchoHigh_2_1=(v);RL_EchoData_6FE_ecu0_0_pool.bits.Master_EchoHigh_2_2=((v)>>8);
#define GetTxMaster_EchoHigh_3_6FE_ecu0_0()                 (RL_EchoData_6FE_ecu0_0_pool.bits.Master_EchoHigh_3_1|(RL_EchoData_6FE_ecu0_0_pool.bits.Master_EchoHigh_3_2<<8))
#define SetTxMaster_EchoHigh_3_6FE_ecu0_0(v)                RL_EchoData_6FE_ecu0_0_pool.bits.Master_EchoHigh_3_1=(v);RL_EchoData_6FE_ecu0_0_pool.bits.Master_EchoHigh_3_2=((v)>>8);
#define GetTxLeftListen_Distance_0_6FE_ecu0_0()             (RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_Distance_0_1|(RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_Distance_0_2<<8))
#define SetTxLeftListen_Distance_0_6FE_ecu0_0(v)            RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_Distance_0_1=(v);RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_Distance_0_2=((v)>>8);
#define GetTxLeftListen_Distance_1_6FE_ecu0_0()             (RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_Distance_1_1|(RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_Distance_1_2<<8))
#define SetTxLeftListen_Distance_1_6FE_ecu0_0(v)            RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_Distance_1_1=(v);RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_Distance_1_2=((v)>>8);
#define GetTxLeftListen_Distance_2_6FE_ecu0_0()             (RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_Distance_2_1|(RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_Distance_2_2<<8))
#define SetTxLeftListen_Distance_2_6FE_ecu0_0(v)            RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_Distance_2_1=(v);RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_Distance_2_2=((v)>>8);
#define GetTxLeftListen_Distance_3_6FE_ecu0_0()             (RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_Distance_3_1|(RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_Distance_3_2<<8))
#define SetTxLeftListen_Distance_3_6FE_ecu0_0(v)            RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_Distance_3_1=(v);RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_Distance_3_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_0_6FE_ecu0_0()             (RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_EchoHigh_0_1|(RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_EchoHigh_0_2<<8))
#define SetTxLeftListen_EchoHigh_0_6FE_ecu0_0(v)            RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_EchoHigh_0_1=(v);RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_EchoHigh_0_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_1_6FE_ecu0_0()             (RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_EchoHigh_1_1|(RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_EchoHigh_1_2<<8))
#define SetTxLeftListen_EchoHigh_1_6FE_ecu0_0(v)            RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_EchoHigh_1_1=(v);RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_EchoHigh_1_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_2_6FE_ecu0_0()             (RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_EchoHigh_2_1|(RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_EchoHigh_2_2<<8))
#define SetTxLeftListen_EchoHigh_2_6FE_ecu0_0(v)            RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_EchoHigh_2_1=(v);RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_EchoHigh_2_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_3_6FE_ecu0_0()             (RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_EchoHigh_3_1|(RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_EchoHigh_3_2<<8))
#define SetTxLeftListen_EchoHigh_3_6FE_ecu0_0(v)            RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_EchoHigh_3_1=(v);RL_EchoData_6FE_ecu0_0_pool.bits.LeftListen_EchoHigh_3_2=((v)>>8);
#define GetTxRightListen_Distance_0_6FE_ecu0_0()            (RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_Distance_0_1|(RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_Distance_0_2<<8))
#define SetTxRightListen_Distance_0_6FE_ecu0_0(v)           RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_Distance_0_1=(v);RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_Distance_0_2=((v)>>8);
#define GetTxRightListen_Distance_1_6FE_ecu0_0()            (RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_Distance_1_1|(RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_Distance_1_2<<8))
#define SetTxRightListen_Distance_1_6FE_ecu0_0(v)           RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_Distance_1_1=(v);RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_Distance_1_2=((v)>>8);
#define GetTxRightListen_Distance_2_6FE_ecu0_0()            (RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_Distance_2_1|(RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_Distance_2_2<<8))
#define SetTxRightListen_Distance_2_6FE_ecu0_0(v)           RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_Distance_2_1=(v);RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_Distance_2_2=((v)>>8);
#define GetTxRightListen_Distance_3_6FE_ecu0_0()            (RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_Distance_3_1|(RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_Distance_3_2<<8))
#define SetTxRightListen_Distance_3_6FE_ecu0_0(v)           RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_Distance_3_1=(v);RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_Distance_3_2=((v)>>8);
#define GetTxRightListen_EchoHigh_0_6FE_ecu0_0()            (RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_EchoHigh_0_1|(RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_EchoHigh_0_2<<8))
#define SetTxRightListen_EchoHigh_0_6FE_ecu0_0(v)           RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_EchoHigh_0_1=(v);RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_EchoHigh_0_2=((v)>>8);
#define GetTxRightListen_EchoHigh_1_6FE_ecu0_0()            (RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_EchoHigh_1_1|(RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_EchoHigh_1_2<<8))
#define SetTxRightListen_EchoHigh_1_6FE_ecu0_0(v)           RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_EchoHigh_1_1=(v);RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_EchoHigh_1_2=((v)>>8);
#define GetTxRightListen_EchoHigh_2_6FE_ecu0_0()            (RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_EchoHigh_2_1|(RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_EchoHigh_2_2<<8))
#define SetTxRightListen_EchoHigh_2_6FE_ecu0_0(v)           RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_EchoHigh_2_1=(v);RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_EchoHigh_2_2=((v)>>8);
#define GetTxRightListen_EchoHigh_3_6FE_ecu0_0()            (RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_EchoHigh_3_1|(RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_EchoHigh_3_2<<8))
#define SetTxRightListen_EchoHigh_3_6FE_ecu0_0(v)           RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_EchoHigh_3_1=(v);RL_EchoData_6FE_ecu0_0_pool.bits.RightListen_EchoHigh_3_2=((v)>>8);
#define GetTxMasterEchoCnt_6FE_ecu0_0()                     (RL_EchoData_6FE_ecu0_0_pool.bits.MasterEchoCnt)
#define SetTxMasterEchoCnt_6FE_ecu0_0(v)                    RL_EchoData_6FE_ecu0_0_pool.bits.MasterEchoCnt= (v)
#define GetTxLeftEchoCnt_6FE_ecu0_0()                       (RL_EchoData_6FE_ecu0_0_pool.bits.LeftEchoCnt)
#define SetTxLeftEchoCnt_6FE_ecu0_0(v)                      RL_EchoData_6FE_ecu0_0_pool.bits.LeftEchoCnt= (v)
#define GetTxRightEchoCnt_6FE_ecu0_0()                      (RL_EchoData_6FE_ecu0_0_pool.bits.RightEchoCnt)
#define SetTxRightEchoCnt_6FE_ecu0_0(v)                     RL_EchoData_6FE_ecu0_0_pool.bits.RightEchoCnt= (v)
#define GetTxNFD_Flag_6FE_ecu0_0()                          (RL_EchoData_6FE_ecu0_0_pool.bits.NFD_Flag)
#define SetTxNFD_Flag_6FE_ecu0_0(v)                         RL_EchoData_6FE_ecu0_0_pool.bits.NFD_Flag= (v)
#define GetTxNFD_Dis_6FE_ecu0_0()                           (RL_EchoData_6FE_ecu0_0_pool.bits.NFD_Dis_1|(RL_EchoData_6FE_ecu0_0_pool.bits.NFD_Dis_2<<8))
#define SetTxNFD_Dis_6FE_ecu0_0(v)                          RL_EchoData_6FE_ecu0_0_pool.bits.NFD_Dis_1=(v);RL_EchoData_6FE_ecu0_0_pool.bits.NFD_Dis_2=((v)>>8);
#define GetTxRing_Fre_6FE_ecu0_0()                          (RL_EchoData_6FE_ecu0_0_pool.bits.Ring_Fre_1|(RL_EchoData_6FE_ecu0_0_pool.bits.Ring_Fre_2<<8))
#define SetTxRing_Fre_6FE_ecu0_0(v)                         RL_EchoData_6FE_ecu0_0_pool.bits.Ring_Fre_1=(v);RL_EchoData_6FE_ecu0_0_pool.bits.Ring_Fre_2=((v)>>8);
#define GetTxNoiseCnt_6FE_ecu0_0()                          (RL_EchoData_6FE_ecu0_0_pool.bits.NoiseCnt)
#define SetTxNoiseCnt_6FE_ecu0_0(v)                         RL_EchoData_6FE_ecu0_0_pool.bits.NoiseCnt= (v)
#define GetTxNoiseSum_6FE_ecu0_0()                          (RL_EchoData_6FE_ecu0_0_pool.bits.NoiseSum_1|(RL_EchoData_6FE_ecu0_0_pool.bits.NoiseSum_2<<4))
#define SetTxNoiseSum_6FE_ecu0_0(v)                         RL_EchoData_6FE_ecu0_0_pool.bits.NoiseSum_1=(v);RL_EchoData_6FE_ecu0_0_pool.bits.NoiseSum_2=((v)>>4);
#define GetTxUpdateFlg_6FF_ecu0_0()                         (RLS_EchoData_6FF_ecu0_0_pool.bits.UpdateFlg)
#define SetTxUpdateFlg_6FF_ecu0_0(v)                        RLS_EchoData_6FF_ecu0_0_pool.bits.UpdateFlg= (v)
#define GetTxSnsWorkSts_6FF_ecu0_0()                        (RLS_EchoData_6FF_ecu0_0_pool.bits.SnsWorkSts)
#define SetTxSnsWorkSts_6FF_ecu0_0(v)                       RLS_EchoData_6FF_ecu0_0_pool.bits.SnsWorkSts= (v)
#define GetTxMaster_Meas_Type_6FF_ecu0_0()                  (RLS_EchoData_6FF_ecu0_0_pool.bits.Master_Meas_Type)
#define SetTxMaster_Meas_Type_6FF_ecu0_0(v)                 RLS_EchoData_6FF_ecu0_0_pool.bits.Master_Meas_Type= (v)
#define GetTxEepromErrFlg_6FF_ecu0_0()                      (RLS_EchoData_6FF_ecu0_0_pool.bits.EepromErrFlg)
#define SetTxEepromErrFlg_6FF_ecu0_0(v)                     RLS_EchoData_6FF_ecu0_0_pool.bits.EepromErrFlg= (v)
#define GetTxMaster_Rtm_6FF_ecu0_0()                        (RLS_EchoData_6FF_ecu0_0_pool.bits.Master_Rtm_1|(RLS_EchoData_6FF_ecu0_0_pool.bits.Master_Rtm_2<<8))
#define SetTxMaster_Rtm_6FF_ecu0_0(v)                       RLS_EchoData_6FF_ecu0_0_pool.bits.Master_Rtm_1=(v);RLS_EchoData_6FF_ecu0_0_pool.bits.Master_Rtm_2=((v)>>8);
#define GetTxMaster_Distance_0_6FF_ecu0_0()                 (RLS_EchoData_6FF_ecu0_0_pool.bits.Master_Distance_0_1|(RLS_EchoData_6FF_ecu0_0_pool.bits.Master_Distance_0_2<<8))
#define SetTxMaster_Distance_0_6FF_ecu0_0(v)                RLS_EchoData_6FF_ecu0_0_pool.bits.Master_Distance_0_1=(v);RLS_EchoData_6FF_ecu0_0_pool.bits.Master_Distance_0_2=((v)>>8);
#define GetTxMaster_Distance_1_6FF_ecu0_0()                 (RLS_EchoData_6FF_ecu0_0_pool.bits.Master_Distance_1_1|(RLS_EchoData_6FF_ecu0_0_pool.bits.Master_Distance_1_2<<8))
#define SetTxMaster_Distance_1_6FF_ecu0_0(v)                RLS_EchoData_6FF_ecu0_0_pool.bits.Master_Distance_1_1=(v);RLS_EchoData_6FF_ecu0_0_pool.bits.Master_Distance_1_2=((v)>>8);
#define GetTxMaster_Distance_2_6FF_ecu0_0()                 (RLS_EchoData_6FF_ecu0_0_pool.bits.Master_Distance_2_1|(RLS_EchoData_6FF_ecu0_0_pool.bits.Master_Distance_2_2<<8))
#define SetTxMaster_Distance_2_6FF_ecu0_0(v)                RLS_EchoData_6FF_ecu0_0_pool.bits.Master_Distance_2_1=(v);RLS_EchoData_6FF_ecu0_0_pool.bits.Master_Distance_2_2=((v)>>8);
#define GetTxMaster_Distance_3_6FF_ecu0_0()                 (RLS_EchoData_6FF_ecu0_0_pool.bits.Master_Distance_3_1|(RLS_EchoData_6FF_ecu0_0_pool.bits.Master_Distance_3_2<<8))
#define SetTxMaster_Distance_3_6FF_ecu0_0(v)                RLS_EchoData_6FF_ecu0_0_pool.bits.Master_Distance_3_1=(v);RLS_EchoData_6FF_ecu0_0_pool.bits.Master_Distance_3_2=((v)>>8);
#define GetTxMaster_EchoHigh_0_6FF_ecu0_0()                 (RLS_EchoData_6FF_ecu0_0_pool.bits.Master_EchoHigh_0_1|(RLS_EchoData_6FF_ecu0_0_pool.bits.Master_EchoHigh_0_2<<8))
#define SetTxMaster_EchoHigh_0_6FF_ecu0_0(v)                RLS_EchoData_6FF_ecu0_0_pool.bits.Master_EchoHigh_0_1=(v);RLS_EchoData_6FF_ecu0_0_pool.bits.Master_EchoHigh_0_2=((v)>>8);
#define GetTxMaster_EchoHigh_1_6FF_ecu0_0()                 (RLS_EchoData_6FF_ecu0_0_pool.bits.Master_EchoHigh_1_1|(RLS_EchoData_6FF_ecu0_0_pool.bits.Master_EchoHigh_1_2<<8))
#define SetTxMaster_EchoHigh_1_6FF_ecu0_0(v)                RLS_EchoData_6FF_ecu0_0_pool.bits.Master_EchoHigh_1_1=(v);RLS_EchoData_6FF_ecu0_0_pool.bits.Master_EchoHigh_1_2=((v)>>8);
#define GetTxMaster_EchoHigh_2_6FF_ecu0_0()                 (RLS_EchoData_6FF_ecu0_0_pool.bits.Master_EchoHigh_2_1|(RLS_EchoData_6FF_ecu0_0_pool.bits.Master_EchoHigh_2_2<<8))
#define SetTxMaster_EchoHigh_2_6FF_ecu0_0(v)                RLS_EchoData_6FF_ecu0_0_pool.bits.Master_EchoHigh_2_1=(v);RLS_EchoData_6FF_ecu0_0_pool.bits.Master_EchoHigh_2_2=((v)>>8);
#define GetTxMaster_EchoHigh_3_6FF_ecu0_0()                 (RLS_EchoData_6FF_ecu0_0_pool.bits.Master_EchoHigh_3_1|(RLS_EchoData_6FF_ecu0_0_pool.bits.Master_EchoHigh_3_2<<8))
#define SetTxMaster_EchoHigh_3_6FF_ecu0_0(v)                RLS_EchoData_6FF_ecu0_0_pool.bits.Master_EchoHigh_3_1=(v);RLS_EchoData_6FF_ecu0_0_pool.bits.Master_EchoHigh_3_2=((v)>>8);
#define GetTxLeftListen_Distance_0_6FF_ecu0_0()             (RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_Distance_0_1|(RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_Distance_0_2<<8))
#define SetTxLeftListen_Distance_0_6FF_ecu0_0(v)            RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_Distance_0_1=(v);RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_Distance_0_2=((v)>>8);
#define GetTxLeftListen_Distance_1_6FF_ecu0_0()             (RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_Distance_1_1|(RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_Distance_1_2<<8))
#define SetTxLeftListen_Distance_1_6FF_ecu0_0(v)            RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_Distance_1_1=(v);RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_Distance_1_2=((v)>>8);
#define GetTxLeftListen_Distance_2_6FF_ecu0_0()             (RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_Distance_2_1|(RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_Distance_2_2<<8))
#define SetTxLeftListen_Distance_2_6FF_ecu0_0(v)            RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_Distance_2_1=(v);RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_Distance_2_2=((v)>>8);
#define GetTxLeftListen_Distance_3_6FF_ecu0_0()             (RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_Distance_3_1|(RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_Distance_3_2<<8))
#define SetTxLeftListen_Distance_3_6FF_ecu0_0(v)            RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_Distance_3_1=(v);RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_Distance_3_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_0_6FF_ecu0_0()             (RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_EchoHigh_0_1|(RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_EchoHigh_0_2<<8))
#define SetTxLeftListen_EchoHigh_0_6FF_ecu0_0(v)            RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_EchoHigh_0_1=(v);RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_EchoHigh_0_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_1_6FF_ecu0_0()             (RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_EchoHigh_1_1|(RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_EchoHigh_1_2<<8))
#define SetTxLeftListen_EchoHigh_1_6FF_ecu0_0(v)            RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_EchoHigh_1_1=(v);RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_EchoHigh_1_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_2_6FF_ecu0_0()             (RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_EchoHigh_2_1|(RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_EchoHigh_2_2<<8))
#define SetTxLeftListen_EchoHigh_2_6FF_ecu0_0(v)            RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_EchoHigh_2_1=(v);RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_EchoHigh_2_2=((v)>>8);
#define GetTxLeftListen_EchoHigh_3_6FF_ecu0_0()             (RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_EchoHigh_3_1|(RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_EchoHigh_3_2<<8))
#define SetTxLeftListen_EchoHigh_3_6FF_ecu0_0(v)            RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_EchoHigh_3_1=(v);RLS_EchoData_6FF_ecu0_0_pool.bits.LeftListen_EchoHigh_3_2=((v)>>8);
#define GetTxRightListen_Distance_0_6FF_ecu0_0()            (RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_Distance_0_1|(RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_Distance_0_2<<8))
#define SetTxRightListen_Distance_0_6FF_ecu0_0(v)           RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_Distance_0_1=(v);RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_Distance_0_2=((v)>>8);
#define GetTxRightListen_Distance_1_6FF_ecu0_0()            (RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_Distance_1_1|(RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_Distance_1_2<<8))
#define SetTxRightListen_Distance_1_6FF_ecu0_0(v)           RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_Distance_1_1=(v);RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_Distance_1_2=((v)>>8);
#define GetTxRightListen_Distance_2_6FF_ecu0_0()            (RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_Distance_2_1|(RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_Distance_2_2<<8))
#define SetTxRightListen_Distance_2_6FF_ecu0_0(v)           RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_Distance_2_1=(v);RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_Distance_2_2=((v)>>8);
#define GetTxRightListen_Distance_3_6FF_ecu0_0()            (RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_Distance_3_1|(RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_Distance_3_2<<8))
#define SetTxRightListen_Distance_3_6FF_ecu0_0(v)           RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_Distance_3_1=(v);RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_Distance_3_2=((v)>>8);
#define GetTxRightListen_EchoHigh_0_6FF_ecu0_0()            (RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_EchoHigh_0_1|(RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_EchoHigh_0_2<<8))
#define SetTxRightListen_EchoHigh_0_6FF_ecu0_0(v)           RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_EchoHigh_0_1=(v);RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_EchoHigh_0_2=((v)>>8);
#define GetTxRightListen_EchoHigh_1_6FF_ecu0_0()            (RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_EchoHigh_1_1|(RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_EchoHigh_1_2<<8))
#define SetTxRightListen_EchoHigh_1_6FF_ecu0_0(v)           RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_EchoHigh_1_1=(v);RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_EchoHigh_1_2=((v)>>8);
#define GetTxRightListen_EchoHigh_2_6FF_ecu0_0()            (RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_EchoHigh_2_1|(RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_EchoHigh_2_2<<8))
#define SetTxRightListen_EchoHigh_2_6FF_ecu0_0(v)           RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_EchoHigh_2_1=(v);RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_EchoHigh_2_2=((v)>>8);
#define GetTxRightListen_EchoHigh_3_6FF_ecu0_0()            (RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_EchoHigh_3_1|(RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_EchoHigh_3_2<<8))
#define SetTxRightListen_EchoHigh_3_6FF_ecu0_0(v)           RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_EchoHigh_3_1=(v);RLS_EchoData_6FF_ecu0_0_pool.bits.RightListen_EchoHigh_3_2=((v)>>8);
#define GetTxMasterEchoCnt_6FF_ecu0_0()                     (RLS_EchoData_6FF_ecu0_0_pool.bits.MasterEchoCnt)
#define SetTxMasterEchoCnt_6FF_ecu0_0(v)                    RLS_EchoData_6FF_ecu0_0_pool.bits.MasterEchoCnt= (v)
#define GetTxLeftEchoCnt_6FF_ecu0_0()                       (RLS_EchoData_6FF_ecu0_0_pool.bits.LeftEchoCnt)
#define SetTxLeftEchoCnt_6FF_ecu0_0(v)                      RLS_EchoData_6FF_ecu0_0_pool.bits.LeftEchoCnt= (v)
#define GetTxRightEchoCnt_6FF_ecu0_0()                      (RLS_EchoData_6FF_ecu0_0_pool.bits.RightEchoCnt)
#define SetTxRightEchoCnt_6FF_ecu0_0(v)                     RLS_EchoData_6FF_ecu0_0_pool.bits.RightEchoCnt= (v)
#define GetTxNFD_Flag_6FF_ecu0_0()                          (RLS_EchoData_6FF_ecu0_0_pool.bits.NFD_Flag)
#define SetTxNFD_Flag_6FF_ecu0_0(v)                         RLS_EchoData_6FF_ecu0_0_pool.bits.NFD_Flag= (v)
#define GetTxNFD_Dis_6FF_ecu0_0()                           (RLS_EchoData_6FF_ecu0_0_pool.bits.NFD_Dis_1|(RLS_EchoData_6FF_ecu0_0_pool.bits.NFD_Dis_2<<8))
#define SetTxNFD_Dis_6FF_ecu0_0(v)                          RLS_EchoData_6FF_ecu0_0_pool.bits.NFD_Dis_1=(v);RLS_EchoData_6FF_ecu0_0_pool.bits.NFD_Dis_2=((v)>>8);
#define GetTxRing_Fre_6FF_ecu0_0()                          (RLS_EchoData_6FF_ecu0_0_pool.bits.Ring_Fre_1|(RLS_EchoData_6FF_ecu0_0_pool.bits.Ring_Fre_2<<8))
#define SetTxRing_Fre_6FF_ecu0_0(v)                         RLS_EchoData_6FF_ecu0_0_pool.bits.Ring_Fre_1=(v);RLS_EchoData_6FF_ecu0_0_pool.bits.Ring_Fre_2=((v)>>8);
#define GetTxNoiseCnt_6FF_ecu0_0()                          (RLS_EchoData_6FF_ecu0_0_pool.bits.NoiseCnt)
#define SetTxNoiseCnt_6FF_ecu0_0(v)                         RLS_EchoData_6FF_ecu0_0_pool.bits.NoiseCnt= (v)
#define GetTxNoiseSum_6FF_ecu0_0()                          (RLS_EchoData_6FF_ecu0_0_pool.bits.NoiseSum_1|(RLS_EchoData_6FF_ecu0_0_pool.bits.NoiseSum_2<<4))
#define SetTxNoiseSum_6FF_ecu0_0(v)                         RLS_EchoData_6FF_ecu0_0_pool.bits.NoiseSum_1=(v);RLS_EchoData_6FF_ecu0_0_pool.bits.NoiseSum_2=((v)>>4);

/***********************外部接口*********************************/
extern void DebugMsgTxTrigger_6F3(void);
extern void DebugMsgTxTrigger_6F4(void);
extern void DebugMsgTxTrigger_6F5(void);
extern void DebugMsgTxTrigger_6F6(void);
extern void DebugMsgTxTrigger_6F7(void);
extern void DebugMsgTxTrigger_6F8(void);
extern void DebugMsgTxTrigger_6F9(void);
extern void DebugMsgTxTrigger_6FA(void);
extern void DebugMsgTxTrigger_6FB(void);
extern void DebugMsgTxTrigger_6FC(void);
extern void DebugMsgTxTrigger_6FD(void);
extern void DebugMsgTxTrigger_6FE(void);
extern void DebugMsgTxTrigger_6FF(void);


extern void DebugMsgTxTrigger_6EE(void);
extern void DebugMsgTxTrigger_6EF(void);
extern void DebugMsgTxTrigger_6F1(void);

extern void Com_RxCvt_AK2Upper_Pc2Mcu_6F0_ecu0_0(void);
#endif
#endif
/*END __DEBUGSIGNALMANAGE_H__**************************************************/
