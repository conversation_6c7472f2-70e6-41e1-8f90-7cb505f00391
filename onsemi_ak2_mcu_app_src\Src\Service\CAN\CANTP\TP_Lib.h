
#ifndef __TP_LIB_H__
#define __TP_LIB_H__
 

/*******************************************************************************
* Includes
********************************************************************************/
#include "Types.h"
#include "TP_Config.h"

/******************************************************************************
* Constants and macros
*******************************************************************************/




/******************************************************************************
* Global objects 
******************************************************************************/
  




/****************************************************************************
* Global Functions
****************************************************************************/

uint8 TP_Init (TpCfgStr *LsTpMsgCfg,TPCOMStr *LsTPCOMParaCfg,TPUDSStr *LsTPUDSParaCfg);
uint8 UDS_TP_RespondManage(uint8 LcUdsStatus, uint16 LwLen, uint8 *pUdsData);
void TP_Manage(void);
#endif




