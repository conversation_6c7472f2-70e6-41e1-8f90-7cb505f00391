/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * SnsEchoFilterAndSigGroup_type: 
 * Created on: 2024-02-23 14:29
 * Original designer: 22866
 ******************************************************************************/
#ifndef PSL_EchoFilterAndSigGroup_type_H
#define PSL_EchoFilterAndSigGroup_type_H

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "Types.h"






/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/
#define PSL_MASTER_COMPEN_DIS                0u
#define PSL_LISTEN_COMPEN_DIS                0u

#define PSL_SNS_HIGH_CURB_USE_FIRST_DIS_ENABLE   0u
#define PSL_SNS_MASTER_COMPEN_DIS                0u
#define PSL_SNS_LISTEN_COMPEN_DIS                0u

#define PSL_SNS_NFD_ECHO_HEIGHT                  2000

#define PSL_TABLE_STEP           50   /* 单位mm，实际5cm */
#define PSL_HIGH_TABLE_STEP      100   /* 单位mm，实际10cm，障碍物高度分类按照10cm步进进行处理 */
#define PSL_TABLE_MAX_DIS        4000  /* 单位mm，实际300cm,感度表和阈值暂设置到4000以内，以外统一使用3000的值 */
#define PSL_MASTER_SECOND_ECHO_DIS  250  /* 第二回波主发距离差值 */
#define PSL_MASTER_SECOND_ECHO_DIS2 300  /* 第二回波主发距离差值-远距离 */

#define PSL_LISTEN_SECOND_ECHO_DIS  255  /* 第二回波侦听距离 */
#define PSL_LISTEN_SECOND_ECHO_DIS2 300  /* 第二回波侦听距离 */

#define PSL_SNS_STD_LISTEN_COMPEN_HEIGHT         8
#define PSL_SNS_STD_NEAR_LISTEN_COMPEN_HEIGHT    8
#define PSL_SNS_CHIRP_LISTEN_COMPEN_HEIGHT       25
#define PSL_SNS_STD_SECOND_THAN_FIRST_HEIGHT     850      /* 定频回波高度修改为14bit后,此处同样修改为350 */
#define PSL_SNS_CHIRP_SECOND_THAN_FIRST_HEIGHT   400      /* 扫频灵敏度提升后，该参数同样需要修改 */

#define PSL_SNS_STD_MASTER_CMP_THRES_1KM         1
#define PSL_SNS_STD_MASTER_CMP_THRES_3KM         2
#define PSL_SNS_STD_MASTER_CMP_THRES_5KM         4
#define PSL_SNS_STD_MASTER_CMP_THRES_7KM         6
#define PSL_SNS_STD_MASTER_CMP_THRES_MORE_7KM    8

#define PSL_SNS_CHIRP_MASTER_CMP_THRES_1KM       5
#define PSL_SNS_CHIRP_MASTER_CMP_THRES_3KM       8
#define PSL_SNS_CHIRP_MASTER_CMP_THRES_5KM       12
#define PSL_SNS_CHIRP_MASTER_CMP_THRES_7KM       16
#define PSL_SNS_CHIRP_MASTER_CMP_THRES_MORE_7KM  20

#define PSL_SNS_MAX_DE_CE_DIS                    (uint16)5110
#define PSL_SNS_MAX_DE_CE_DIS_TO_CAN             (uint16)511
#define PSL_SNS_INIT_DE_CE_DIS_TO_CAN            (uint16)0

#define PSL_CLOSE_RANGE_STD_DE_FILTER_START_DIS  500U     /*uint:mm*/
#define MAX_NUM_OF_PSL_SIG_GROUP     3u

/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/
typedef enum
{    
    PSL_CYCLE_0 = 0u,
        
    PSL_CYCLE_1,   
    
    PSL_CYCLE_2,      
    
    PSL_CYCLE_NUM,    
}PSL_CycleType; 

typedef struct
{
    uint32                 u32SysTime;
    float                  fCarMoveDisSub;
#if VS_SIMULATE_ENABLE
    uint8                 eCarDir;
    uint8                 eMeasType;
#else
    SnsCarMoveDirType      eCarDir;
    PDCSnsMeasTypeType     eMeasType;
#endif
}PSLOrigSystemDataType;

/******************************************************************************/
/**
 * @brief  信号组数据类型定义
 */
/******************************************************************************/
typedef struct
{
    uint8                   u8SigGroupCnt;
    uint8                   u8GroupEchoCnt[MAX_NUM_OF_PSL_SIG_GROUP];      /* 回波信号组中的回波个数,1~2个 */
    uint16                  u16FirstEchoDis[MAX_NUM_OF_PSL_SIG_GROUP];     /* 第一回波距离 */
	uint16                  u16FirstEchoHeight[MAX_NUM_OF_PSL_SIG_GROUP];  /* 第一回波高度 */
    uint16                  u16SecondEchoDis[MAX_NUM_OF_PSL_SIG_GROUP];    /* 第二回波距离 */
	uint16                  u16SecondEchoHeight[MAX_NUM_OF_PSL_SIG_GROUP]; /* 第二回波高度 */
    uint16                  u16ActualDis[MAX_NUM_OF_PSL_SIG_GROUP];        /* 该回波信号组的实际使用的距离 */
    uint16                  u16FirstDisBackup[MAX_NUM_OF_PSL_SIG_GROUP];   /* 第一回波距离的备份，用于过滤第一回波前的噪点组成的高路沿 */
	uint16                  u16MaxHeight[MAX_NUM_OF_PSL_SIG_GROUP];        /* 该回波信号组的最大回波高度 */
#if VS_SIMULATE_ENABLE
    uint8                  enuOriObjType[MAX_NUM_OF_PSL_SIG_GROUP];       /* 通过回波高度初步判定的障碍物类型，主要用于DE CE置信度初始值选取 */
#else
    OriginalObjType         enuOriObjType[MAX_NUM_OF_PSL_SIG_GROUP];       /* 通过回波高度初步判定的障碍物类型，主要用于DE CE置信度初始值选取 */
#endif
}PSLSigGroupDataUnitType;


/******************************************************************************/
/**
 * @brief  定义每一个通道的回波信号组数据缓存
 */
/******************************************************************************/
typedef struct
{
    uint8                   u8SigGroupUpdateFlag;                      /* 探头原始信号组数据更新标志 */
    uint8                   u8RecordCnt;                               /* 已经记录的缓存数据个数 */
#if VS_SIMULATE_ENABLE
    uint8                  enuCurIndex;                               /* 当前的索引 */
#else
    PSL_CycleType           enuCurIndex;                               /* 当前的索引 */
#endif
    PSLOrigSystemDataType   SysDataBuf[PSL_CYCLE_NUM];
    PSLSigGroupDataUnitType MasterBuf[PSL_CYCLE_NUM];
    PSLSigGroupDataUnitType LeftBuf[PSL_CYCLE_NUM];
    PSLSigGroupDataUnitType RightBuf[PSL_CYCLE_NUM];
}PSLSnsSigGroupDataCacheType;

extern PSLSnsSigGroupDataCacheType GstrPSLSnsSigGroupDataCache[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];
/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/








#endif /* end of SnsEchoFilterAndSigGroup_type_H */

