/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * PublicCalAlgorithm_Int: 
 * Created on: 2022-11-26 09:57
 * Original designer: AntonyFang
 ******************************************************************************/
#ifndef PublicCalAlgorithm_Int_H
#define PublicCalAlgorithm_Int_H

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "PublicCalAlgorithm_Type.h"



/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
float PubAI_CalTwoPointDis(float LfP1_X_Coor,float LfP1_Y_Coor,float LfP2_X_Coor,float LfP2_Y_Coor);
float PubAI_CalP1_P2_LineToX_AxleAngle(float LfP1_X_Coor,float LfP1_Y_Coor,float LfP2_X_Coor,float LfP2_Y_Coor);
void PubAI_UpdateCarOdoCoor(float LfCarRearAxle_X,float LfCarRearAxle_Y,float LfCarRearAxle_SinAngle,float LfCarRearAxle_CosAngle);
void PubAI_TransObjCarCoorToOdoCoor(float *LfObjCarCoorX,float *LfObjCarCoorY,float *LfObjOdoCoorX,float *LfObjOdoCoorY);
void PubAI_TransObjOdoCoorToCarCoor(float *LfObjOdoCoorX,float *LfObjOdoCoorY,float *LfObjCarCoorX,float *LfObjCarCoorY);
void PubAI_UpdateSnsInCarCoor(float LfSnsCoorX,float LfSnsCoorY,float LfSnsCoorSinAngle,float LfSnsCoorCosAngle);
void PubAI_TransObjSnsCoorToCarCoor(float *LfObjSnsCoorX,float *LfObjSnsCoorY,float *LfObjCarCoorX,float *LfObjCarCoorY);

float PubAI_CalObjCosAngle(uint16 Lu16OppositeSideDis,uint16 Lu16FirstNearSideDis,uint16 Lu16SecondNearSideDis);
void PubAI_BubbleSortFor_uint8_Type(uint16 Lu16DataLen,uint8 *Lpu8DataPtr,Pub_SortTypeType LenuPub_SortType);
void PubAI_BubbleSortFor_sint8_Type(uint16 Lu16DataLen,sint8 *Lps8DataPtr,Pub_SortTypeType LenuPub_SortType);
void PubAI_BubbleSortFor_uint16_Type(uint16 Lu16DataLen,uint16 *Lpu16DataPtr,Pub_SortTypeType LenuPub_SortType);
void PubAI_BubbleSortFor_sint16_Type(uint16 Lu16DataLen,sint16 *Lps16DataPtr,Pub_SortTypeType LenuPub_SortType);
void PubAI_BubbleSortFor_uint32_Type(uint16 Lu16DataLen,uint32 *Lpu32DataPtr,Pub_SortTypeType LenuPub_SortType);
void PubAI_BubbleSortFor_sint32_Type(uint16 Lu16DataLen,sint32 *Lps32DataPtr,Pub_SortTypeType LenuPub_SortType);
void PubAI_BubbleSortFor_float_Type(uint16 Lu16DataLen,float *LpfDataPtr,Pub_SortTypeType LenuPub_SortType);

void PubAI_ArrayDataHandle_uint8_Type(uint16 Lu16DataLen,uint8 *Lpu8DataPtr,Pub_CalAiArrayDataHandleType *LPstrArrayDataHandle);
void PubAI_ArrayDataHandle_sint8_Type(uint16 Lu16DataLen,sint8 *Lps8DataPtr,Pub_CalAiArrayDataHandleType *LPstrArrayDataHandle);
void PubAI_ArrayDataHandle_uint16_Type(uint16 Lu16DataLen,uint16 *Lpu16DataPtr,Pub_CalAiArrayDataHandleType *LPstrArrayDataHandle);
void PubAI_ArrayDataHandle_sint16_Type(uint16 Lu16DataLen,sint16 *Lps16DataPtr,Pub_CalAiArrayDataHandleType *LPstrArrayDataHandle);
void PubAI_ArrayDataHandle_uint32_Type(uint16 Lu16DataLen,uint32 *Lpu32DataPtr,Pub_CalAiArrayDataHandleType *LPstrArrayDataHandle);
void PubAI_ArrayDataHandle_sint32_Type(uint16 Lu16DataLen,sint32 *Lps32DataPtr,Pub_CalAiArrayDataHandleType *LPstrArrayDataHandle);
void PubAI_ArrayDataHandle_float_Type(uint16 Lu16DataLen,float *LpfDataPtr,Pub_CalAiArrayDataHandleType *LPstrArrayDataHandle);
Pub_Point2LineType PubAI_CalPoint2LineRelation(float P1_X,float P1_Y,float P2_X,float P2_Y,float P3_X,float P3_Y);
float PubAI_CalOnePointToOtherPointDis(float LfX0,float LfY0,float LfX1,float LfY1,float LfX2,float LfY2);
void PubAI_Filter(Pub_FilterCtrl_t *pCtrl);
float PubAI_CalOnePointToSegmentDis(float LfX0,float LfY0,float LfX1,float LfY1,float LfX2,float LfY2);
float PubAI_CalOnePointToSegmentDisAndRelation(float LfX0,float LfY0,float LfX1,float LfY1,float LfX2,float LfY2,uint8* Lpu8PointInMiddleFlag);



#endif /* end of PublicCalAlgorithm_Int_H */

