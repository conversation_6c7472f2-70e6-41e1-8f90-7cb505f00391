/**********************************************************************
*   版权所有    : 2017 深圳市豪恩汽车电子装备有限公司
*   项目名称    : TP_Lib
*   M C U       : RL78
*   编译环境    : IAR
*   文件名称    : CAN_TP.c
*   其它说明    : CAN传输层
*   当前版本    : V0.1
*   作    者  : 20460
*   完成日期    :   
*   内容摘要    :
*   修改记录1   :
*   修改日期    :
*   版 本 号 :
*   修 改 人 :
*   修改内容    :
*   修改记录2   :

***********************************************************************/
/***********************************************************************
* Define macro 
***********************************************************************/
#define TP_OBJ
#define TP_GFUNCTION
#define UDS_TP_OBJ

/***********************************************************************
* Includes 
***********************************************************************/
#include "TP_Manage.h"
#include "TP_Config.h"
#include "TP_COM_Interface.h"
#include "UDS_TP_Interface.h"
#include "CAN_COM.h"
//#include "PowerSingalManage.h"
#include "CANCfg.h"
//#include "TimerManage.h"


/* CAN FD extended data length DLC encoding */
#define   CANFD_DLC_VALUE_8_BYTES                    8U
#define   CANFD_DLC_VALUE_12_BYTES                   9U
#define   CANFD_DLC_VALUE_16_BYTES                   10U
#define   CANFD_DLC_VALUE_20_BYTES                   11U
#define   CANFD_DLC_VALUE_24_BYTES                   12U
#define   CANFD_DLC_VALUE_32_BYTES                   13U
#define   CANFD_DLC_VALUE_48_BYTES                   14U
#define   CANFD_DLC_VALUE_64_BYTES                   15U

uint8 FF_Can_Dlc = 0;

/*********************************************************************
* 函数名称: TP_ParaInit
*
* 功能描述: Tp层参数初始化
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明: 无                     
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
static uint8 TP_ParaInit(TpCfgStr *LsTpMsgCfg)
{
    uint8 LcInitState = INIT_OK;
    uint8 LcChannel;
    
    if(LsTpMsgCfg != NULL)
    {
        /************TP发送参数初始化*******************/
        GsTpTxMsg.GcTxStatus = TP_MSG_STATUS_NONE;
        GsTpTxMsg.GwTxLen = 0;          
        GsTpTxMsg.GwTxLenCnt = 0;  
        GsTpTxMsg.GwuNAs = LsTpMsgCfg->GwuNAs;
        GsTpTxMsg.GwTxuNAsCnt = 0u;
        GsTpTxMsg.GwuNBs = LsTpMsgCfg->GwuNBs;
        GsTpTxMsg.GwTxuNBsCnt = 0;             
        GsTpTxMsg.GcTxSnCnt = 0;
        GsTpTxMsg.GcTxBs = 0;
        GsTpTxMsg.GcTxBsCnt = 0;
        GsTpTxMsg.GcTxSTmin = 0;
        GsTpTxMsg.GcPaddingByte = LsTpMsgCfg->GcPaddingByte;
        GsTpTxMsg.GcTxFCWaitMax = LsTpMsgCfg->GcTxFCWaitMax;

        /************TP接收通道参数初始化***************/
        for (LcChannel = 0U; LcChannel < TP_CHANNEL_MAX; LcChannel ++)
        {
            TpRxMsg *const pTpRxMsg = &GsTpRxMsg[LcChannel];

            pTpRxMsg->GcRxStatus = TP_MSG_STATUS_NONE;  
            #if 0/*Dead code*/
            if(LsTpMsgCfg->pRxData == NULL)
            {
                LcInitState = TP_INIT_FAIL;
                break;
            }
            #endif
            pTpRxMsg->pRxData = LsTpMsgCfg->pRxData[LcChannel];
            pTpRxMsg->GwDataMax = LsTpMsgCfg->GwBuffMax[LcChannel];
            pTpRxMsg->GwRxLen = 0;        
            pTpRxMsg->GwRxLenCnt = 0; 
            pTpRxMsg->GwuNAr = LsTpMsgCfg->GwuNAr;
            pTpRxMsg->GwuNArCnt = 0u;
            pTpRxMsg->GwuNCr = LsTpMsgCfg->GwuNCr;
            pTpRxMsg->GwuNCrCnt = 0;       
            pTpRxMsg->GwRxBs = LsTpMsgCfg->GwRxBs;             
            pTpRxMsg->GwRxBsCnt = 0;
            pTpRxMsg->GcRxSnCnt = 0;
            pTpRxMsg->GcRxSTmin = LsTpMsgCfg->GcRxSTmin;
            pTpRxMsg->GcLockState = TP_CHANNEL_UNLOCK;
        }
    }
    else
    {
        LcInitState = TP_INIT_FAIL;
    }

    return LcInitState;
}
/*********************************************************************
* 函数名称: TP_COMParaInit
*
* 功能描述: Tp_COM层参数初始化
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明: 无                     
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
static uint8 TP_COMParaInit(TPCOMStr *LsTPCOMParaCfg)
{
    uint8 LcInitState = INIT_OK;
    
    if((LsTPCOMParaCfg->pFunctionDiagFrameState != NULL)&&(LsTPCOMParaCfg->pPhysicalDiagFrameState != NULL)\
    &&(LsTPCOMParaCfg->pRespondDiagFrameState != NULL)&&(LsTPCOMParaCfg->pFunctionDiagComBuf != NULL)\
    &&(LsTPCOMParaCfg->pPhysicalDiagComBuf != NULL)&&(LsTPCOMParaCfg->pRespondDiagComBuff != NULL))
    {
        GsTpComStr.pFunctionDiagFrameState = LsTPCOMParaCfg->pFunctionDiagFrameState;
        GsTpComStr.pPhysicalDiagFrameState = LsTPCOMParaCfg->pPhysicalDiagFrameState;
        GsTpComStr.pRespondDiagFrameState = LsTPCOMParaCfg->pRespondDiagFrameState;
        GsTpComStr.pFunctionDiagComBuf = LsTPCOMParaCfg->pFunctionDiagComBuf;
        GsTpComStr.pPhysicalDiagComBuf = LsTPCOMParaCfg->pPhysicalDiagComBuf;
        GsTpComStr.pRespondDiagComBuff = LsTPCOMParaCfg->pRespondDiagComBuff;
        GsTpComStr.pFunctionRxDLC = LsTPCOMParaCfg->pFunctionRxDLC;
        GsTpComStr.pPhysicalRxDLC = LsTPCOMParaCfg->pPhysicalRxDLC;
    }
    else
    {
        LcInitState = TP_COM_INIT_FAIL;
    }
    return LcInitState;
}

/*********************************************************************
* 函数名称: TP_Init
*
* 功能描述: Tp层变量初始化
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明: 无                     
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
uint8 TP_Init (TpCfgStr *LsTpMsgCfg,TPCOMStr *LsTPCOMParaCfg,TPUDSStr *LsTPUDSParaCfg)
{
    uint8 LcInitState = INIT_OK;
    
    LcInitState |= TP_ParaInit(LsTpMsgCfg);
    
    LcInitState |= TP_UDSParaInit(LsTPUDSParaCfg);

    LcInitState |= TP_COMParaInit(LsTPCOMParaCfg);
    
    return LcInitState;
}

/**********************************************************************************************
* 函数名称: TP_AbortMsg
*
* 功能描述: TP层诊断发送异常，终止发送响应数据
*
* 输入参数: uMsgIdx TP message index.
*
* 输出参数: None
*
* 返 回 值:  Result of treatment
*           \retval TP_ERR_OK no issue occurs
*           \retval TP_ERR_IDX incorrect input TP message index
*
* 其它说明: None                    
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************************************/
static uint8 TP_AbortMsg(uint8 LcChannel)
{
    uint8 LcStatus = TP_ERR_OK;

    if (LcChannel < TP_CHANNEL_MAX)        /* 形参检查 */
    {
        GsTpTxMsg.GcTxStatus = TP_MSG_STATUS_NONE;
        GsTpTxMsg.GwTxuNAsCnt = 0U;         /* 21/06/28增加As参数处理 */
        GsTpTxMsg.GwTxuNBsCnt = 0U;
        
        GsTpRxMsg[LcChannel].GcRxStatus = TP_MSG_STATUS_NONE;      //改动
        GsTpRxMsg[LcChannel].GwuNArCnt = 0U;                       /* 21/06/28增加Ar参数处理 */
        GsTpRxMsg[LcChannel].GwuNCrCnt = 0U;
        GsTpRxMsg[LcChannel].GwRxLenCnt = 0u;
    
    }
    else
    {
        LcStatus = TP_ERR_IDX;
    }

    return (LcStatus);
}

/****************************************************************************************
* 函数名称: TP_RxEnable
*
* 功能描述: ①主要功能是锁住或解锁诊断接收通道，为了阻止诊断信息的接收混乱交错。 
*           ②当需要锁住接收通道时，锁住时间为DiagRxEnableCnt = 1000ms，这个时间
*             在TP_Manage里递减，当递减至0时，通道使能接收。
*
* 输入参数:LcMsgIdx:诊断接收通道索引; LcRxState:设置通道状态(使能 / 禁止)
*
* 输出参数: DiagRxEnableCnt
*
* 返 回 值:  设置是否成功的状态
*
* 其它说明: 无                     
*
* 修改日期      版本号       修改人       修改内容

***************************************************************************************/
uint8 TP_RxEnable (uint8 LcChannel, uint8 LcRxState)
{
    uint8 LcStatus = TP_ERR_OK;

    if ((LcChannel < TP_CHANNEL_MAX) && (LcRxState < TP_RX_ENABLE_MAX))  /* 参数检查 */
    {
        if (LcRxState == TP_RX_ENABLE_ON )
        {
            GsTpRxMsg[LcChannel].GcLockState = TP_CHANNEL_UNLOCK;
        }
        else
        {
            GsTpRxMsg[LcChannel].GcLockState = TP_CHANNEL_LOCK;
            
            if (GsTpRxMsg[LcChannel].GcRxStatus != TP_MSG_STATUS_NONE)
            {
                (void)TP_AbortMsg (LcChannel);    /* Stop TP message reception */
            }
        }
    }
    else
    {
        LcStatus = TP_ERR_IDX;
    }

    return (LcStatus);
}

/**********************************************************************************************
* 函数名称: TP_TxConf
*
* 功能描述: 诊断响应发送状态，成功或失败
*
* 输入参数: uMsgIdx TP message index;  ebStatus TP message status (TP_MSG_STATUS_*)
*
* 输出参数: None
*
* 返 回 值:  None
*
* 其它说明: None                    
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************************************/
static void TP_TxConf (uint8 LcStatus)
{
	(void)LcStatus;
    if(GcUdsACKType != UDS_NRC_78)
    {
        (void)TP_RxEnable(PHYSICALCHANNEL, TP_RX_ENABLE_ON);
        (void)TP_RxEnable(FUNCTIONALCHANNEL, TP_RX_ENABLE_ON);
    }
    else
    {
        
    }
}
/**********************************************************************************************
* 函数名称: TP_ComTxConfirm
*
* 功能描述: TP frame transmission confirmation
*           This function shall be called when a segmented Tx frame is acknowledged.
*
* 输入参数: uMsgIdx TP message index.
*
* 输出参数: None
*
* 返 回 值:  None
*
* 其它说明: None                    
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************************************/
void TP_ComTxConfirm (void)
{
    uint8 LcChannelIdx;

    /**************************** 针对服务器发送报文处理 ***********************************/
    if (GsTpTxMsg.GcTxStatus == TP_MSG_STATUS_TX_SF)
    {
        GsTpTxMsg.GcTxStatus = TP_MSG_STATUS_NONE; 

        TP_TxConf (TP_MSG_STATUS_OK);                        /* 成功发送单帧 */
    }
    else if (GsTpTxMsg.GcTxStatus == TP_MSG_STATUS_TX_FF)
    {
        GsTpTxMsg.GcTxStatus = TP_MSG_STATUS_RX_FC;        /* 成功发送首帧，设置发送状态为等待接收流控制帧 */
        GsTpTxMsg.GwTxuNAsCnt = 0u;                 /* 21/06/28 发送完成首帧后清除As计数 */
        GsTpTxMsg.GwTxuNBsCnt = GsTpTxMsg.GwuNBs;
    }
    else if (GsTpTxMsg.GcTxStatus == TP_MSG_STATUS_TX_CF)  /* 成功发送后续帧 */
    {
        GsTpTxMsg.GwTxuNAsCnt = 0u;                 /* 21/06/28 发送完成连续帧后清除As计数 */
        if (GsTpTxMsg.GwTxLenCnt == GsTpTxMsg.GwTxLen)
        {
            GsTpTxMsg.GcTxStatus = TP_MSG_STATUS_NONE;     /* 成功发送多帧响应 */
   
            TP_TxConf (TP_MSG_STATUS_OK);
        }
        else
        {
            if(GsTpTxMsg.GcTxBsCnt > 1u)        /* 21/06/29 BS计数大于1则开始正常的连续帧发送 */
            {
                GsTpTxMsg.GcTxBsCnt--;
                GsTpTxMsg.GwTxuNBsCnt = (GsTpTxMsg.GcTxSTmin + 1);     /* 未完成响应，设置Stmin时间后发送下一后续帧 */
            }
            else if(GsTpTxMsg.GcTxBsCnt == 1u)  /* 21/06/29 BS计数等于1则开始等待下一帧流控帧的出现 */
            {
                GsTpTxMsg.GcTxStatus = TP_MSG_STATUS_RX_FC;            /* 21/06/29 BS计数为1后设置发送状态为等待接收流控制帧 */
                GsTpTxMsg.GwTxuNBsCnt = GsTpTxMsg.GwuNBs;
            }
            else                                /* 21/06/29 BS计数等于0则一直以Stmin的时间发送后续帧 */
            {
                GsTpTxMsg.GwTxuNBsCnt = (GsTpTxMsg.GcTxSTmin + 1);     /* 未完成响应，设置Stmin时间后发送下一后续帧 */
            }
        }
    }
    else{}

    /*********************************** 针对服务器接收报文处理 ***************************************/
    /*                                 例如完成发送流控制帧后处理                                     */
    /**************************************************************************************************/
    for(LcChannelIdx = (uint8)0;LcChannelIdx < (uint8)2;LcChannelIdx++)
    {
        TpRxMsg *const pTpRxMsg = &GsTpRxMsg[LcChannelIdx];            /* 指针指向TP层信息 */

        if (pTpRxMsg->GcRxStatus == TP_MSG_STATUS_TX_FC)                /* 成功发送流控制帧 */
        {
            pTpRxMsg->GcRxStatus = TP_MSG_STATUS_RX_CF;                 /* 设置接收状态为等待接收后续帧 */
            pTpRxMsg->GwuNCrCnt = pTpRxMsg->GwuNCr;                     /* 设置等待时间为uNCr */
        }
        else if (pTpRxMsg->GcRxStatus == TP_MSG_STATUS_TX_FC_OVERFLOW)  /* 成功发送过载流控制帧 */
        {
            pTpRxMsg->GcRxStatus = TP_MSG_STATUS_NONE;
        }
        else{}

        pTpRxMsg->GwuNArCnt = 0u;       /* 21/06/28 发送完成后Ar时间计数清零 */
    }
}

/**********************************************************************************************
* 函数名称: TP_RxInd
*
* 功能描述: Message complete reception
*           This callback is called when a message reception is completed, successfully or not.
*
* 输入参数: uMsgIdx TP message index;  ebStatus TP message status (TP_MSG_STATUS_*)
*
* 输出参数: None
*
* 返 回 值:  None
*
* 其它说明: None                    
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************************************/
static void TP_RxInd (uint8 LcDiagChannel, uint8 LcStatus)
{
    if (LcStatus == TP_MSG_STATUS_OK)
    {
        switch(LcDiagChannel)
        {
            case PHYSICALCHANNEL:  
            {
                *GsUDS_TP_Interface.GcPhysicalChannelRxCompleteFlag = TRUE;
                *GsUDS_TP_Interface.GwPhysicalChannelRxLen = GsTpRxMsg[PHYSICALCHANNEL].GwRxLen;
                
                GwWaitRespondTimer = GsUDS_TP_Interface.GcP2CAN_Server;
                
                (void)TP_RxEnable(PHYSICALCHANNEL, TP_RX_ENABLE_OFF);          /* 接收到一包诊断数据请求后，必须关闭物理通道和功能通道，目的是防止旧的诊断请求未处理，又有新的诊断请求，从而导致混乱 */
                (void)TP_RxEnable(FUNCTIONALCHANNEL, TP_RX_ENABLE_OFF);
                break;
            }
            case FUNCTIONALCHANNEL:  
            {
                *GsUDS_TP_Interface.GcFunctionChannelRxCompleteFlag = TRUE;
                *GsUDS_TP_Interface.GcFunctionChannelRxLen =  GsTpRxMsg[FUNCTIONALCHANNEL].GwRxLen;
                
                GwWaitRespondTimer = GsUDS_TP_Interface.GcP2CAN_Server;
                
                (void)TP_RxEnable(PHYSICALCHANNEL, TP_RX_ENABLE_OFF);
                (void)TP_RxEnable(FUNCTIONALCHANNEL, TP_RX_ENABLE_OFF);        /* 接收到一包诊断数据请求后，必须关闭物理通道和功能通道，目的是防止旧的诊断请求未处理，又有新的诊断请求，从而导致混乱 */
                break;
            }
            default:
            {
                break; 
            }
        }
    }
} 
/**************************************************************************************************
* 函数名称: CanFd_GetDLCValue
*
* 功能描述: 动态计算CANFD帧DLC
*
* 输入参数: uMsgIdx TP message index
*
* 输出参数: None
*
* 返 回 值:  return Result of treatment
*
* 其它说明: None                    
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************************************/

static uint8 CanFd_GetDLCValue(uint8 DataLength,uint8 *Datalen_hex)
{
    uint8 ret;

    if( DataLength <= 8u )
    {
        ret = 8;
        *Datalen_hex = 8;
    }
    else if( (DataLength > 8u) && (DataLength <= 12u) )
    {
        ret = CANFD_DLC_VALUE_12_BYTES;
        *Datalen_hex = 12;
    }
    else if( (DataLength > 12u) && (DataLength <= 16u) )
    {
        ret = CANFD_DLC_VALUE_16_BYTES;
        *Datalen_hex = 16;
    }
    else if( (DataLength > 16u) && (DataLength <= 20u) )
    {
        ret = CANFD_DLC_VALUE_20_BYTES;
        *Datalen_hex = 20;
    }
    else if( (DataLength > 20u) && (DataLength <= 24u) )
    {
        ret = CANFD_DLC_VALUE_24_BYTES;
        *Datalen_hex = 24;
    }
    else if( (DataLength > 24u) && (DataLength <= 32u) )
    {
        ret = CANFD_DLC_VALUE_32_BYTES;
        *Datalen_hex = 32;
    }
    else if( (DataLength > 32u) && (DataLength <= 48u) )
    {
        ret = CANFD_DLC_VALUE_48_BYTES;
        *Datalen_hex = 48;
    }
    else if( (DataLength > 48u) && (DataLength <= 64u) )
    {
        ret = CANFD_DLC_VALUE_64_BYTES;
        *Datalen_hex = 64;
    }
    else
    {
        /* The argument is not a valid payload size */
        ret = 0xFFu;
    }

    return ret;
}
/**************************************************************************************************
* 函数名称: CanFd_GetDLC_hex
*
* 功能描述: 动态计算CANFD帧DLC_16
*
* 输入参数: 
*
* 输出参数: None
*
* 返 回 值:  return Result of DLC_HEX
*
* 其它说明: None                    
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************************************/
static uint8 CanFd_GetDLC_hex(uint8 Datalen_hex)
{
    uint8 ret;

    if( Datalen_hex == 8u)
    {
        ret = 8;
    }
    else if( Datalen_hex == CANFD_DLC_VALUE_12_BYTES)
    {
        ret = 12;
    }
    else if(Datalen_hex == CANFD_DLC_VALUE_16_BYTES )
    {
        ret = 16;
    }
    else if(Datalen_hex == CANFD_DLC_VALUE_20_BYTES)
    {
        ret = 20;
    }
    else if(Datalen_hex == CANFD_DLC_VALUE_24_BYTES)
    {
        ret = 24;
    }
    else if(Datalen_hex == CANFD_DLC_VALUE_32_BYTES)
    {
        ret = 32;
    }
    else if(Datalen_hex == CANFD_DLC_VALUE_48_BYTES)
    {
        ret = 48;
    }
    else if(Datalen_hex == CANFD_DLC_VALUE_64_BYTES)
    {
        ret = 64;
    }
    else
    {
        /* The argument is not a valid payload size */
        ret = 0;
    }

    return ret;
}

/**************************************************************************************************
* 函数名称: TP_SendSingleFrame
*
* 功能描述: AVM发送单帧诊断报文
*
* 输入参数: uMsgIdx TP message index
*
* 输出参数: None
*
* 返 回 值:  return Result of treatment
*
* 其它说明: None                    
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************************************/
static uint8 TP_SendSingleFrame (void)
{
    uint8 LcComStatus;
    uint8 LcIdx;
    uint8 LcDataBuff[COM_DATA_MAX_DIAG];
    uint8 DataOffset, FrameLength;
    uint8 Datalen;
    GsTpTxMsg.GcTxStatus = TP_MSG_STATUS_TX_SF;      /* 设置诊断发送状态为单帧 */

    GsTpTxMsg.GwTxuNBsCnt = 0U;
    if( GsTpTxMsg.GwTxLen <= TP_DATA_MAX_SINGLE_FRAME )
    {
        GcComFrameDLC[ID7DAIdx] = 8u;
        FrameLength = 8u;
        DataOffset  = 1u;

        /* 对于CAN_DL <=8 而言，首个字节的高四位表示帧类型，低四位表示单帧的长度*/
        /* 设置单帧的数据长度及协议格式 */
        LcDataBuff[0] = (uint8)((GsTpTxMsg.GwTxLen & 0x0FU) | TP_NPCI_SINGLE_FRAME);
    }
    else
    {
        GcComFrameDLC[ID7DAIdx] = CanFd_GetDLCValue( (GsTpTxMsg.GwTxLen + 2),&Datalen);
        FrameLength = Datalen;
        DataOffset  = 2u;

        /* 对于新格式单帧而言，第一个字节为0，第二个字节表长度*/
        /* 设置单帧的数据长度及协议格式 */
        LcDataBuff[0] = (uint8)0x0;
        LcDataBuff[1] = (uint8)(GsTpTxMsg.GwTxLen);
    }

    for (LcIdx = 0U; LcIdx < GsTpTxMsg.GwTxLen; LcIdx ++)     /* 填充需要发送的诊断数据 */
    {
        LcDataBuff[LcIdx + DataOffset] = GsTpTxMsg.pTxData[LcIdx];
    }

    for (; LcIdx < (FrameLength - DataOffset); LcIdx++)            /* 填充无效字节为0x00 */
    {
        LcDataBuff[LcIdx + DataOffset] = GsTpTxMsg.GcPaddingByte;
    }   
    
    LcComStatus = TP_COM_SendFrameDataReq (LcDataBuff);      /* 把数据放置到COM缓冲区,并设置请求发送标志位 */
/*因目前采用发送轮询的方式查询是否发送成功，因此需要提前释放通道接收诊断请求报文*/
    GsTpTxMsg.GcTxStatus = TP_MSG_STATUS_NONE;
    TP_TxConf (TP_MSG_STATUS_OK);
    return (LcComStatus);

}

/**************************************************************************************************
* 函数名称: TP_SendFirstFrame
*
* 功能描述: 诊断仪发送首帧
*
* 输入参数: uMsgIdx TP message index
*
* 输出参数: None
*
* 返 回 值:  return Result of treatment
*           \retval COM_ERR_OK COM status Ok
*           \retval COM_ERR_TX_MSG_LOST COM rejects Tx
*
* 其它说明: None                    
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************************************/
static uint8 TP_SendFirstFrame (void)
{
    uint8 LcComStatus;
    uint8 LcIdx;
    uint8 LcDataBuff[COM_DATA_MAX_DIAG];
    uint8 DataOffset;

    GsTpTxMsg.GcTxStatus = TP_MSG_STATUS_TX_FF;     /* 设置诊断发送状态为首帧 */
    GcComFrameDLC[ID7DAIdx] = 15;

    LcDataBuff[0] = (uint8)((uint8)((uint16)(GsTpTxMsg.GwTxLen >> 8U) & 0x000FU) | TP_NPCI_FIRST_FRAME);
    LcDataBuff[1] = (uint8)(GsTpTxMsg.GwTxLen);     /* 设置诊断相应数据长度及首帧格式 */
    DataOffset = 2;


#if 0
    LcDataBuff[0] = (uint8)((uint8)((uint16)(GsTpTxMsg.GwTxLen >> 8U) & 0x000FU) | TP_NPCI_FIRST_FRAME);
    LcDataBuff[1] = (uint8)(GsTpTxMsg.GwTxLen);     /* 设置诊断相应数据长度及首帧格式 */
#endif

    for (LcIdx = 0U; LcIdx < (COM_DATA_MAX_DIAG - DataOffset); LcIdx ++) 
    {
        LcDataBuff[LcIdx + DataOffset] = GsTpTxMsg.pTxData[LcIdx];
    }

    GsTpTxMsg.GcTxSnCnt = 1U;                 /* 设置后续帧发送的序号Sn */

    GsTpTxMsg.GwTxLenCnt = LcIdx;             /* 设置目前已发送的诊断数据长度 */

    GsTpTxMsg.GwTxuNBsCnt = 0U;                   /* 直接发送，不需要等待 */

    GsTpTxMsg.GwTxuNAsCnt = GsTpTxMsg.GwuNAs; /* 21/06/28 增加As时间参数处理,发送首帧时开始As计数 */

    GsTpTxMsg.GcTxFCWaitCnt = GsTpTxMsg.GcTxFCWaitMax;      /* 21/06/29修改设置项 设置连续接收流控制帧的最大次数 */

    LcComStatus = TP_COM_SendFrameDataReq (LcDataBuff);
    
    return (LcComStatus);
}

/**********************************************************************************************
* 函数名称: TP_SendConsecutiveFrame
*
* 功能描述: 发送流控制帧
*
* 输入参数: uMsgIdx TP message index
*
* 输出参数: None
*
* 返 回 值:  请求发送的状态
*
* 其它说明: None                    
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************************************/
static uint8 TP_SendConsecutiveFrame (void)
{
    uint8 LcComStatus;
    uint8 LcIdx;
    uint8 LcDataBuff[COM_DATA_MAX_DIAG];
    uint8 Datalen;
    GsTpTxMsg.GcTxStatus = TP_MSG_STATUS_TX_CF;     /* 设置发送状态为发送后续帧 */

    GsTpTxMsg.GwTxuNBsCnt = 0U;
    
    if( (GsTpTxMsg.GwTxLen - GsTpTxMsg.GwTxLenCnt) >= 48 )
    {
        GcComFrameDLC[ID7DAIdx] = CANFD_DLC_VALUE_64_BYTES;
    }
    else
    {
        GcComFrameDLC[ID7DAIdx] = CanFd_GetDLCValue( (GsTpTxMsg.GwTxLen - GsTpTxMsg.GwTxLenCnt + 1),&Datalen );
    }

    LcDataBuff[0] = (uint8)(((GsTpTxMsg.GcTxSnCnt) & 0x0FU) | TP_NPCI_CONSECUTIVE_FRAME);  /* 后续帧的协议数据单元 */

    for (LcIdx = 1U; (GsTpTxMsg.GwTxLenCnt < GsTpTxMsg.GwTxLen) && (LcIdx < TP_LEN_MAX_CONSECUTIVE_FRAME); LcIdx ++)
    {
        LcDataBuff[LcIdx] = GsTpTxMsg.pTxData[GsTpTxMsg.GwTxLenCnt];

        GsTpTxMsg.GwTxLenCnt ++;
    }

    GsTpTxMsg.GcTxSnCnt ++;                   /* 每发送一帧后续帧，SN序加1 */

    GsTpTxMsg.GwTxuNAsCnt = GsTpTxMsg.GwuNAs; /* 21/06/28 增加As时间参数处理,发送连续帧时开始As计数 */

    for (; LcIdx < (uint8)COM_DATA_MAX_DIAG; LcIdx++)        /* 判断最后一包数据是否需要填充字节 */
    {
        LcDataBuff[LcIdx] = GsTpTxMsg.GcPaddingByte;
    }

    LcComStatus = TP_COM_SendFrameDataReq (LcDataBuff);
    if(GsTpTxMsg.GwTxLen == GsTpTxMsg.GwTxLenCnt)
    {
        /*因目前采用发送轮询的方式查询是否发送成功，因此需要提前释放通道接收诊断请求报文*/
        GsTpTxMsg.GcTxStatus = TP_MSG_STATUS_NONE;

        TP_TxConf (TP_MSG_STATUS_OK);
    }
    return (LcComStatus);
}

/**********************************************************************************************
* 函数名称: TP_SendFlowControl
*
* 功能描述:  发送流控制帧
*
* 输入参数: uMsgIdx TP message index
*
* 输出参数: None
*
* 返 回 值:  return Result of treatment
*
* 其它说明: None                    
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************************************/
static uint8 TP_SendFlowControl (uint8 LcChannel, uint8 LcFlowStatus)
{
    uint8 LcComStatus;
    uint8 LcIdx;
    uint8 LcDataBuff[COM_DATA_MAX_DIAG];    
    GcComFrameDLC[ID7DAIdx] = 8;

    if (LcFlowStatus == TP_FS_OVERFLOW)
    {
        GsTpRxMsg[LcChannel].GcRxStatus = TP_MSG_STATUS_TX_FC_OVERFLOW; /* 设置诊断接收状态为发送过载帧*/
    }
    else
    {
        GsTpRxMsg[LcChannel].GcRxStatus = TP_MSG_STATUS_TX_FC;          /* 设置诊断接收状态为发送流控制帧 */
    }

    LcDataBuff[0] = (uint8)(TP_NPCI_FLOW_CONTROL | LcFlowStatus); 
    LcDataBuff[1] = GsTpRxMsg[LcChannel].GwRxBs;                        /* 流控制帧参数 */
    LcDataBuff[2] = GsTpRxMsg[LcChannel].GcRxSTmin; 
    
    for (LcIdx = TP_LEN_FLOW_CONTROL; LcIdx < (uint8)COM_DATA_MAX_DIAG; LcIdx++)
    {
        LcDataBuff[LcIdx] = GsTpTxMsg.GcPaddingByte;                        /* 填充字节 */
    }
    
    GsTpRxMsg[LcChannel].GwRxBsCnt = GsTpRxMsg[LcChannel].GwRxBs;     /* 连续接收后续帧个数 */
    GsTpRxMsg[LcChannel].GwuNArCnt = GsTpRxMsg[LcChannel].GwuNAr;     /* 21/06/28 增加Ar时间参数处理,发送流控帧时开始Ar计数 */

    GsTpRxMsg[LcChannel].GwuNCrCnt = 0U;                                  /* 不需要等待接收后续帧，需要在发送流控制帧成功处设置 */

    LcComStatus = TP_COM_SendFrameDataReq (LcDataBuff);
    
    return (LcComStatus);
}

/**************************************************************************************************
* 函数名称: TP_SendMsg
*
* 功能描述: 发送诊断响应报文
*
* 输入参数: uMsgIdx TP message index
*
* 输出参数: None
*
* 返 回 值:  return Result of treatment
*           \retval TP_ERR_OK no issue occurs
*           \retval TP_ERR_IDX incorrect input TP message index
*           \retval TP_ERR_COM_FRAME_MODE COM frame is only periodic, segmentation is forbidden
*           \retval TP_ERR_COM_TX_MSG_LOST COM rejects transmission request (there is already a pending request)
*
* 其它说明: None                    
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************************************/
uint8 TP_SendMsg (void)
{
    uint8 LcStatus = TP_MSG_STATUS_OK;

    if (GsTpTxMsg.GwTxLen <= TP_DATA_MAX_SINGLE_FRAME_DL64)   /* 诊断响应数据小于8，发送单帧 */
    {
        LcStatus = TP_SendSingleFrame ();
    }
    else
    {
        LcStatus = TP_SendFirstFrame ();                 /* 发送多帧的首帧 */
    }
#if 0
    if (TP_MSG_STATUS_OK != LcStatus)/*这里不成立，是Dead code*/
    {
        (void)TP_AbortMsg(PHYSICALCHANNEL);
        
        (void)TP_AbortMsg(FUNCTIONALCHANNEL);
        
        TP_TxConf (TP_MSG_STATUS_COM_NOK);
    }
#endif
    return (LcStatus);
}


/**************************************************************************************************
* 函数名称: TP_ReceiveSingleFrame
*
* 功能描述: 接收到单帧
*
* 输入参数: uMsgIdx TP message index; aubData buffer data
*
* 输出参数: None
*
* 返 回 值:  return Result of treatment
*           \retval TP_ERR_OK no issue occurs
*           \retval TP_ERR_LEN incorrect length
*           \retval TP_ERR_COM_TX_MSG_LOST segmented Tx in progress
*
* 其它说明: None                    
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************************************/
static void TP_ReceiveSingleFrame (uint8 LcDiagChannel, const uint8 *pDataBuff,uint8 DLC)
{
    uint8 LcIdx;
    uint8 DLC_Hex;
    
    TpRxMsg * pTpRxMsg = &GsTpRxMsg[LcDiagChannel];

    DLC_Hex = CanFd_GetDLC_hex(DLC);
    

    if((LcDiagChannel == PHYSICALCHANNEL)||((LcDiagChannel == FUNCTIONALCHANNEL)&&(GsTpRxMsg[PHYSICALCHANNEL].GcRxStatus == TP_MSG_STATUS_NONE)))       /* 物理通道接收诊断报文时，不处理功能通道的数据 */
    {
        if (GsTpTxMsg.GcTxStatus == TP_MSG_STATUS_NONE)                         /* 诊断响应的状态一定要为None。 否则不接收诊断服务报文 */
        {
           if(pDataBuff[0] != 0) 
           {
                if (((pDataBuff[0] & 0x0FU) > 0U) && ((pDataBuff[0] & 0x0FU) < 8) && (DLC_Hex == 8))  /* 单帧的数据长度一定要为1~7个字节 */
                {
                    pTpRxMsg->GcRxStatus = TP_MSG_STATUS_RX_SF;   /* 设置当前接收状态为接收单帧 */
                    
                    pTpRxMsg->GwuNCrCnt = 0U;      /* 不需要等待接收任何诊断报文 */

                    pTpRxMsg->GwRxLen = (uint16)((uint16)pDataBuff[0] & 0x000FU);  /* 获取单帧报文的数据长度 */

                    for (LcIdx = 0U; LcIdx < (pTpRxMsg->GwRxLen); LcIdx ++)
                    {
                        pTpRxMsg->pRxData[LcIdx] = pDataBuff[LcIdx + 1U];   /* 获取单帧的数据 */
                    }
                    
                    pTpRxMsg->GcRxStatus = TP_MSG_STATUS_NONE;   /* 完成单帧报文的接收 */

                    TP_RxInd (LcDiagChannel, TP_MSG_STATUS_OK);
                }
           }
           else
           {   
               if ((pDataBuff[1] > 7U) && (pDataBuff[1] < 63U) && (pDataBuff[1] <= (DLC_Hex - 2)))  /* 单帧的数据长度一定要为8~62个字节 */
               {
                   pTpRxMsg->GcRxStatus = TP_MSG_STATUS_RX_SF;   /* 设置当前接收状态为接收单帧 */
               
                   pTpRxMsg->GwuNCrCnt = 0U;      /* 不需要等待接收任何诊断报文 */
               
                   pTpRxMsg->GwRxLen = (uint16)(pDataBuff[1]);  /* 获取单帧报文的数据长度 */
               
                   for (LcIdx = 0U; LcIdx < (pTpRxMsg->GwRxLen); LcIdx ++)
                   {
                       pTpRxMsg->pRxData[LcIdx] = pDataBuff[LcIdx + 2U];   /* 获取单帧的数据 */
                   }
               
                   pTpRxMsg->GcRxStatus = TP_MSG_STATUS_NONE;   /* 完成单帧报文的接收，将接收状态重新清零 */
               
                   /* 关闭接收通道，配置相应的诊断服务参数 */
                   TP_RxInd (LcDiagChannel, TP_MSG_STATUS_OK);
               }

           }
        }
    }
}
uint8 TP_JugeFirstFrame(uint8 RxDLC,uint16 RxLen,uint8 Offset)
{
    uint8 Ret;
    if(RxLen > (RxDLC-Offset))
    {
        Ret =(RxDLC-Offset);
    }
    else
    {
       Ret =0;
    }
    return Ret;
}
/**************************************************************************************************
* 函数名称: TP_ProcessFFDL_Less4095
*
* 功能描述: 接收多帧的首帧小于4095字节
*
* 输入参数: uMsgIdx TP message index; aubData buffer data
*
* 输出参数: None
*
* 返 回 值:  return Result of treatment
*           \retval TP_ERR_OK no issue occurs
*           \retval TP_ERR_LEN incorrect length
*           \retval TP_ERR_COM_TX_MSG_LOST segmented Tx in progress
*
* 其它说明: None                    
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************************************/
void  TP_ProcessFFDL_Less4095(uint8 LcDiagChannel,const uint8 *pDataBuff,uint8 DLC)
{
    TpRxMsg * pTpRxMsg = &GsTpRxMsg[LcDiagChannel];
    uint8 Lu8Cnt;
    uint8 DLC_Hex;
    uint8 LcIdx;
    DLC_Hex = CanFd_GetDLC_hex(DLC);
    /* 获取整包的数据长度 */
    pTpRxMsg->GwRxLenCnt = (uint16)((uint16)(((uint16)pDataBuff[0] & 0x000FU) << (uint8)8U) + (uint16)(pDataBuff[1]));
    Lu8Cnt = TP_JugeFirstFrame(DLC_Hex,pTpRxMsg->GwRxLenCnt,2);
    if(pTpRxMsg->GwRxLenCnt > 6)
    {
        /* 判断数据包的长度是否符合多帧的长度 */
        if (Lu8Cnt != 0)
        {
            /* 设置诊断诊断接收状态为接收首帧 */
            pTpRxMsg->GcRxStatus = TP_MSG_STATUS_RX_FF;

            /* 需要发送流控制帧，因此不需要等待接收后续帧 */
            pTpRxMsg->GwuNCrCnt = 0U;

            /* 如果接收的数据长度小于接收缓冲区的大小，则允许接收，否则发送过载流控制帧 */
            if (pTpRxMsg->GwRxLenCnt <= pTpRxMsg->GwDataMax)
            {
                /* Record the First frame length. */
                FF_Can_Dlc = DLC_Hex;
                
                /* 记住多帧请求的报文长度 */
                pTpRxMsg->GwRxLen = pTpRxMsg->GwRxLenCnt;   

                /* 设置接收后续帧序号为1 */
                pTpRxMsg->GcRxSnCnt = 1U;

                /* 接收诊断数据 */
                for (LcIdx = 0U; LcIdx < (Lu8Cnt); LcIdx ++)
                {
                    pTpRxMsg->pRxData[LcIdx] = pDataBuff[LcIdx + 2U];
                }
                
                /* 设置已经接收到的诊断字节个数为6 */
                pTpRxMsg->GwRxLenCnt = Lu8Cnt;//TP_DATA_MAX_FIRST_FRAME;

                /* 发送流控制帧 */
                (void)TP_SendFlowControl (LcDiagChannel, TP_FS_CLEAR_TO_SEND);
                #if 0/*Dead code*/
                if (TP_MSG_STATUS_OK != LcStatus)
                {
                    (void)TP_AbortMsg(LcDiagChannel);
                }
                #endif
            }
            else
            {
                (void)TP_SendFlowControl (LcDiagChannel, TP_FS_OVERFLOW);   /* 发送过载流控制帧 */
                #if 0/*Dead code*/
                if (TP_MSG_STATUS_OK != LcStatus)
                {
                    (void)TP_AbortMsg(LcDiagChannel);
                }
                #endif
            }
        }
    }
}
/**************************************************************************************************
* 函数名称: TP_ProcessFFDL_Over4095
*
* 功能描述: 接收多帧的首帧超过4095字节
*
* 输入参数: uMsgIdx TP message index; aubData buffer data
*
* 输出参数: None
*
* 返 回 值:  return Result of treatment
*           \retval TP_ERR_OK no issue occurs
*           \retval TP_ERR_LEN incorrect length
*           \retval TP_ERR_COM_TX_MSG_LOST segmented Tx in progress
*
* 其它说明: None                    
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************************************/

void  TP_ProcessFFDL_Over4095(uint8 LcDiagChannel,const uint8 *pDataBuff,uint8 DLC)
{
    TpRxMsg * pTpRxMsg = &GsTpRxMsg[LcDiagChannel];
    uint8 Lu8Cnt;
    uint8 DLC_Hex;
    uint8 LcIdx;
    DLC_Hex = CanFd_GetDLC_hex(DLC);

    /* 获取整包的数据长度 */
    pTpRxMsg->GwRxLenCnt = (uint16)(((uint16)(pDataBuff[2]<<24)&0xFF000000U)|((uint16)(pDataBuff[3]<<16)&0xFF0000U)\
                                        |((uint16)(pDataBuff[4]<<8)&0xFF00U)|((uint16)(pDataBuff[5])&0xFFU));
    Lu8Cnt = TP_JugeFirstFrame(DLC_Hex,pTpRxMsg->GwRxLenCnt,6);
    if(pTpRxMsg->GwRxLenCnt > 6)
    {
    /* 判断数据包的长度是否符合多帧的长度 */
        if (pTpRxMsg->GwRxLenCnt > 4095)
        {
            /* 设置诊断诊断接收状态为接收首帧 */
            pTpRxMsg->GcRxStatus = TP_MSG_STATUS_RX_FF;

            /* 需要发送流控制帧，因此不需要等待接收后续帧 */
            pTpRxMsg->GwuNCrCnt = 0U;

            /* 如果接收的数据长度小于接收缓冲区的大小，则允许接收，否则发送过载流控制帧 */
            if (pTpRxMsg->GwRxLenCnt <= pTpRxMsg->GwDataMax)
            {
                pTpRxMsg->GwRxLen = pTpRxMsg->GwRxLenCnt;   /* 记住多帧请求的报文长度 */

                /* 设置接收后续帧序号为1 */
                pTpRxMsg->GcRxSnCnt = 1U;

                /* 接收诊断数据 */
                for (LcIdx = 0U; LcIdx < Lu8Cnt; LcIdx ++)
                {
                    pTpRxMsg->pRxData[LcIdx] = pDataBuff[LcIdx + 6U];
                }

                /* 设置已经接收到的诊断字节个数为62 */
                //pTpRxMsg->GwRxLenCnt = TP_DATA_MAX_FIRST_FRAME_FFDL4096;
                pTpRxMsg->GwRxLenCnt = Lu8Cnt;

                /* 发送流控制帧 */
                (void)TP_SendFlowControl (LcDiagChannel, TP_FS_CLEAR_TO_SEND);
                /*Dead code*/
                #if 0
                if (TP_MSG_STATUS_OK != LcStatus)
                {
                    (void)TP_AbortMsg(LcDiagChannel);
                }
                #endif
            }
            else
            {
                (void)TP_SendFlowControl (LcDiagChannel, TP_FS_OVERFLOW);   /* 发送过载流控制帧 */
                /*Dead code*/
                #if 0
                if (TP_MSG_STATUS_OK != LcStatus)
                {
                    (void)TP_AbortMsg(LcDiagChannel);
                }
                #endif
            }
        }
    }
}

/**************************************************************************************************
* 函数名称: TP_ReceiveFirstFrame
*
* 功能描述: 接收多帧的首帧
*
* 输入参数: uMsgIdx TP message index; aubData buffer data
*
* 输出参数: None
*
* 返 回 值:  return Result of treatment
*           \retval TP_ERR_OK no issue occurs
*           \retval TP_ERR_LEN incorrect length
*           \retval TP_ERR_COM_TX_MSG_LOST segmented Tx in progress
*
* 其它说明: None                    
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************************************/

static void  TP_ReceiveFirstFrame(uint8 LcDiagChannel,const uint8 *pDataBuff,uint8 DLC)
{
    uint16 Lu16PCI;
    TpRxMsg * pTpRxMsg = &GsTpRxMsg[LcDiagChannel];
    Lu16PCI =  (uint16)(((uint16)(pDataBuff[0]<<8)&0xFF00U) | (pDataBuff[1]&0xFFU));
    /* 当前诊断响应状态为空闲，且接收到的首帧必须是物理寻址 */
    if ((GsTpTxMsg.GcTxStatus == TP_MSG_STATUS_NONE)&&(LcDiagChannel != FUNCTIONALCHANNEL))  
    {
        /* 如果当前诊断接收状态不为空闲，则放弃之前接收的报文 */
        if (pTpRxMsg->GcRxStatus != TP_MSG_STATUS_NONE)
        {
            pTpRxMsg->GcRxStatus = TP_MSG_STATUS_NONE;
            
        }
        /* FF_DL<=4095 */
        if(Lu16PCI != 0x1000)
        {
            TP_ProcessFFDL_Less4095(LcDiagChannel, pDataBuff,DLC);
        }
        else
        {
            TP_ProcessFFDL_Over4095(LcDiagChannel, pDataBuff,DLC);
        }

    }
}
/**************************************************************************************************
* 函数名称: TP_ReceiveConsecutiveFrame
*
* 功能描述: 接收后续帧
*
* 输入参数: uMsgIdx TP message index; aubData buffer data
*
* 输出参数: None
*
* 返 回 值:  return Result of treatment
*           \retval TP_ERR_OK no issue occurs
*           \retval TP_ERR_LEN incorrect length
*           \retval TP_ERR_COM_TX_MSG_LOST segmented Tx in progress
*
* 其它说明: None                    
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************************************/
static void  TP_ReceiveConsecutiveFrame(uint8 LcDiagChannel,const uint8 *pDataBuff,uint8 DLC)
{
    //uint8 LcStatus = TP_ERR_OK;
    uint8 LcIdx;

    TpRxMsg *pTpRxMsg  = &GsTpRxMsg[LcDiagChannel];
    uint8 DLC_Hex;
    DLC_Hex = CanFd_GetDLC_hex(DLC);
    /* 诊断接收状态不为空闲，且不为过载流控制帧 */
    if ((pTpRxMsg->GcRxStatus != TP_MSG_STATUS_NONE) &&(pTpRxMsg->GcRxStatus != TP_MSG_STATUS_TX_FC_OVERFLOW))
    {
        /* 后续帧Sn正确 */
        if ((pTpRxMsg->GcRxSnCnt & 0x0FU) == (pDataBuff[0] & 0x0FU))
        {
            /* 设置诊断接收状态为接收后续帧 */
            pTpRxMsg->GcRxStatus = TP_MSG_STATUS_RX_CF;

            pTpRxMsg->GwuNCrCnt = 0U;

            pTpRxMsg->GcRxSnCnt ++;     /* 每接收一帧后续帧SN加1 */
            
            for (LcIdx = 1U; (pTpRxMsg->GwRxLenCnt < pTpRxMsg->GwRxLen) && (LcIdx < (uint8)DLC_Hex); LcIdx ++)
            //for (LcIdx = 1U; (pTpRxMsg->GwRxLenCnt < pTpRxMsg->GwRxLen) && (LcIdx < (uint8)COM_DATA_MAX_DIAG); LcIdx ++)
            {
                pTpRxMsg->pRxData[pTpRxMsg->GwRxLenCnt] = pDataBuff[LcIdx];   /* 接收处理诊断数据 */

                pTpRxMsg->GwRxLenCnt ++;
            }
            
            if (pTpRxMsg->GwRxLenCnt == pTpRxMsg->GwRxLen)    /* 多帧诊断报文接收完成 */
            {
                pTpRxMsg->GcRxStatus = TP_MSG_STATUS_NONE;
                
                TP_RxInd (LcDiagChannel, TP_MSG_STATUS_OK);
            }
            else
            {
                if (pTpRxMsg->GwRxBs != 0U)                   /* 如果配置的连续接收后续帧次数不为无限次 */
                {
                    pTpRxMsg->GwRxBsCnt--;
                }

                if ((pTpRxMsg->GwRxBs == 0U)||((pTpRxMsg->GwRxBsCnt) != 0U))
                {
                    pTpRxMsg->GwuNCrCnt = pTpRxMsg->GwuNCr;    /* 设置等待接收下一后续帧的最大时间 */
                }
                else
                {
                    (void)TP_SendFlowControl (LcDiagChannel, TP_FS_CLEAR_TO_SEND);  /* 发送流控制帧 */
                    #if 0 /*Dead code*/
                    if (TP_MSG_STATUS_OK != LcStatus)
                    {
                        (void)TP_AbortMsg(LcDiagChannel);
                                              
                    }
                    #endif 
                }

                /* The payload data length CAN_DL of the received CAN frame has to match the RX_DL calue which 
                was determined in the reception process of the FirstFrame.Only the last CF in the multi-frame 
                transmission may contain less than RX_DL bytes.See also ISO15765-2 section 9.6.4. */
                if(FF_Can_Dlc != DLC_Hex)
                {
                    pTpRxMsg->GcRxStatus = TP_MSG_STATUS_NONE;   /* 接收后续帧序号错误，需要设置接收状态为空闲 */

                    pTpRxMsg->GwuNCrCnt = 0U;    
                }
            }
        }
        else
        {
            pTpRxMsg->GcRxStatus = TP_MSG_STATUS_NONE;   /* 接收后续帧序号错误，需要设置接收状态为空闲 */

            pTpRxMsg->GwuNCrCnt = 0U;            
        }
    }
    else
    {
        if (pTpRxMsg->GcRxStatus != TP_MSG_STATUS_NONE)
        {
            (void)TP_AbortMsg(LcDiagChannel);
                        
        }
    }
    
}

/**************************************************************************************************
* 函数名称: TP_ReceiveFlowControl
*
* 功能描述: 接收流控制帧
*
* 输入参数: uMsgIdx TP message index; pDataBuff buffer data
*
* 输出参数: None
*
* 返 回 值:  return Result of treatment
*           \retval TP_ERR_OK no issue occurs
*           \retval TP_ERR_LEN incorrect length
*           \retval TP_ERR_STMIN STMIN error
*           \retval TP_ERR_WFT_OVRN FC Wait allowed error
*           \retval TP_ERR_OVFLOW Overflow Rx
*           \retval TP_ERR_NPCI unexpected NPCI
*
* 其它说明: None                    
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************************************/
static void TP_ReceiveFlowControl (uint8 LcDiagChannel,const uint8 *pDataBuff)
{
    /* 三种情况下有可能需要接收流控制帧 */
    /* ①发送多帧的首帧后 */
    /* ②发送流控制帧后 */
    /* ③发送后续帧，并且发送的连续帧次数已经与接收方的Bs相等 */
    if ((LcDiagChannel != FUNCTIONALCHANNEL)&&(((GsTpTxMsg.GcTxStatus == TP_MSG_STATUS_TX_FF)||(GsTpTxMsg.GcTxStatus == TP_MSG_STATUS_RX_FC))\
        ||((GsTpTxMsg.GcTxStatus == TP_MSG_STATUS_TX_CF) && ((GsTpTxMsg.GcTxBs != 0U) && (GsTpTxMsg.GcTxBsCnt == 0U)))))
    {
    
            GsTpTxMsg.GwTxuNBsCnt = 0U;       /* 不需要等待发送 */

            if (GsTpTxMsg.GwTxLenCnt == TP_DATA_MAX_FIRST_FRAME)         /* 接收到的首帧后的第一帧流控制帧 */
            {
                GsTpTxMsg.GcTxBs = pDataBuff[1];                         /* 记住诊断仪的流控制帧参数 */
                GsTpTxMsg.GcTxSTmin = pDataBuff[2];
                {
                    if ((pDataBuff[0] & 0x0FU) == TP_FS_CLEAR_TO_SEND)     /*  接收到的流控制帧中，可持续发送 BS 次多帧报文 */
                    {
                        GsTpTxMsg.GcTxBsCnt = GsTpTxMsg.GcTxBs;          /* 赋值给BsCnt 计数 */

                        (void)TP_SendConsecutiveFrame ();             /* 发送后续帧 */
                        /*Dead code*/
                        #if 0
                        if (TP_MSG_STATUS_OK != LcStatus)
                        {
                            (void)TP_AbortMsg(PHYSICALCHANNEL);
                            (void)TP_AbortMsg(FUNCTIONALCHANNEL);
                            
                            TP_TxConf (TP_MSG_STATUS_COM_NOK);
                        }
                        #endif
                    }
                    else if ((pDataBuff[0] & 0x0FU) == TP_FS_WAIT)         /* 等待下一流控制帧 */
                    {
                        GsTpTxMsg.GwTxuNBsCnt = GsTpTxMsg.GwuNBs;       /* 等待时间为　pstTpMsgCfg->uNBs　 */

                        if ( GsTpTxMsg.GcTxFCWaitCnt == 255U )            /* 无限次等待流控制帧 */
                        {
                            
                        }
                        else if ( GsTpTxMsg.GcTxFCWaitCnt > 0U )
                        {
                            GsTpTxMsg.GcTxFCWaitCnt--;
                        }
                        else
                        {
                            GsTpTxMsg.GcTxStatus = TP_MSG_STATUS_NONE;    /* 等待的流控制帧次数溢出，不在发送诊断报文 */
                            
                            TP_TxConf (TP_MSG_STATUS_WFT_OVRN);
                        }
                    }
                    else if ((pDataBuff[0] & 0x0FU) == TP_FS_OVERFLOW)
                    {
                        GsTpTxMsg.GcTxStatus = TP_MSG_STATUS_NONE;        /* 发送的诊断报文数据溢出，不在接收发送 */
                        
                        TP_TxConf (TP_MSG_STATUS_OVFLOW);
                        
                    }
                    else       /* 无效的诊断报文格式 */
                    {
                        GsTpTxMsg.GcTxStatus = TP_MSG_STATUS_NONE;

                        TP_TxConf (TP_MSG_STATUS_FS);

                    }
                }
            }
            else
            {
                if ((pDataBuff[0] & 0x0FU) == TP_FS_CLEAR_TO_SEND)
                {
                    GsTpTxMsg.GcTxBsCnt = GsTpTxMsg.GcTxBs;

                   (void)TP_SendConsecutiveFrame ();
                    /*Dead code*/
                    #if 0
                    if (TP_MSG_STATUS_OK != LcStatus)
                    {
                        (void)TP_AbortMsg(PHYSICALCHANNEL);
                        (void)TP_AbortMsg(FUNCTIONALCHANNEL);
                        
                         TP_TxConf (TP_MSG_STATUS_COM_NOK);
                    }
                    #endif
                }
                else if ((pDataBuff[0] & 0x0FU) == TP_FS_WAIT)
                {
                    GsTpTxMsg.GwTxuNBsCnt = GsTpTxMsg.GwuNBs;

                    if ( GsTpTxMsg.GcTxFCWaitCnt > 0U )
                    {
                        GsTpTxMsg.GcTxFCWaitCnt--;
                    }
                    else
                    {
                        GsTpTxMsg.GcTxStatus = TP_MSG_STATUS_NONE;
                        
                        TP_TxConf (TP_MSG_STATUS_WFT_OVRN);
                    }
                }
                else
                {
                    GsTpTxMsg.GcTxStatus = TP_MSG_STATUS_NONE;
                    TP_TxConf (TP_MSG_STATUS_FS);
                }
            }
    }
}

/**************************************************************************************************
* 函数名称: TP_ReceiveFrame
*
* 功能描述: 处理从总线上接收到诊断报文
*
* 输入参数: uMsgIdx TP message index
*
* 输出参数: None
*
* 返 回 值:  return Result of treatment
*           \retval TP_ERR_OK no issue occurs
*           \retval TP_ERR_IDX incorrect input TP message index
*           \retval TP_ERR_OVFLOW length received is greater than storage capacity
*           \retval TP_ERR_LEN incorrect length
*           \retval TP_ERR_NO_RX_MSG consecutive frame received without segmentation is progress
*
* 其它说明: None                    
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************************************/
static void TP_ReceiveFrame (uint8 LcChannel)
{
    uint8 LcDataBuff[COM_DATA_MAX_DIAG];
    
    uint8 LcDLC = 0;
    TpRxMsg * pTpRxMsg  = &GsTpRxMsg[LcChannel];

    LcDLC =  COM_GetFrameData (LcChannel, LcDataBuff);

    if ((LcChannel == FUNCTIONALCHANNEL) && (LcDLC > 8))
    {
        return;
    }
    if ((pTpRxMsg->GcLockState == TP_CHANNEL_UNLOCK) && (LcDLC >= COM_DATA_MIN_DIAG))  /*  判断诊断Buffer是否使能，如果使能，把COM口诊断数据接收到诊断Buffer中 */
    {
        switch (LcDataBuff[0] & TP_NPCI)
        {
            case TP_NPCI_SINGLE_FRAME :
            {
                TP_ReceiveSingleFrame (LcChannel, LcDataBuff,LcDLC);
                break;
            }

            case TP_NPCI_FIRST_FRAME :
            {
                TP_ReceiveFirstFrame (LcChannel, LcDataBuff,LcDLC);
                break;
            }

            case TP_NPCI_CONSECUTIVE_FRAME :
            {
                TP_ReceiveConsecutiveFrame (LcChannel, LcDataBuff,LcDLC);
                break;
            }

            case TP_NPCI_FLOW_CONTROL :
            {
                TP_ReceiveFlowControl (LcChannel,LcDataBuff);
                break;
            }

            default :     /* 非法的数据 */
            {
                if (pTpRxMsg->GcRxStatus != TP_MSG_STATUS_NONE)
                {

                }
                break;
            }
        }
    } 
}

/*********************************************************************
 * 函数名称:  Tp_ReceiveManage
 *                                  
 * 功能描述:  TP层接收诊断报文管理  
 *                  
 * 入口参数:  无
 *
 * 输出参数:  无      
 *          
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 * 
 ********************************************************************/
static void Tp_ReceiveManage (void)
{
    /***************************** 物理寻址 *************************************************/
    if ((*GsTpComStr.pPhysicalDiagFrameState & COM_FRAME_STATUS_RX_FLAG) != 0U)
    {
        TP_ReceiveFrame (PHYSICALCHANNEL); 
    }
    
    /****************************** 功能寻址 ************************************************/
    if ((*GsTpComStr.pFunctionDiagFrameState & COM_FRAME_STATUS_RX_FLAG) != 0U)
    {
        TP_ReceiveFrame (FUNCTIONALCHANNEL);
    }                  
}
/*********************************************************************
 * 函数名称:  TP_SendTimeManage
 *                                  
 * 功能描述:  TP层发送诊断报文时间管理  
 *                  
 * 入口参数:  Lu8TimeOffset：上次运行与本次运行时间差
 *
 * 输出参数:  无      
 *          
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 * 
 ********************************************************************/
void TP_SendTimeManage(uint8 Lu8TimeOffset)
{
    if (GsTpTxMsg.GwTxuNBsCnt > Lu8TimeOffset)                 /* 连续帧时间间隔延时时间控制,或等待流控制帧时间 */
    {
        GsTpTxMsg.GwTxuNBsCnt -= Lu8TimeOffset;
    }
    else if(GsTpTxMsg.GwTxuNBsCnt != 0U)                     /* 需要发送下一连续帧 */
    {
        GsTpTxMsg.GwTxuNBsCnt = 0U;

        if (GsTpTxMsg.GcTxStatus == TP_MSG_STATUS_RX_FC)       /* 发送首帧后，等待流控制帧超时 */
        {
            GsTpTxMsg.GcTxStatus = TP_MSG_STATUS_NONE;

            TP_TxConf (TP_MSG_STATUS_NBS);

        }
        else if (GsTpTxMsg.GcTxStatus == TP_MSG_STATUS_TX_CF)   /*　等待STmin时间到，响应下一后续帧 */
        {

            if((GsTpComStr.pRespondDiagFrameState[0] & COM_FRAME_STATUS_TX_REQ) != COM_FRAME_STATUS_TX_REQ)    /* 上一帧响应报文已发送成功 */
            {
                (void)TP_SendConsecutiveFrame();
                #if 0/*Dead code*/
                if (TP_MSG_STATUS_OK != eStatus)
                {
                    (void)TP_AbortMsg(PHYSICALCHANNEL);
                    (void)TP_AbortMsg(FUNCTIONALCHANNEL);

                    TP_TxConf (TP_MSG_STATUS_COM_NOK);
                }
                else
                {
                    /* Nothing to do */
                }
                #endif
            }
            else
            {
                /* Nothing to do */
            }
        }
        else
        {
            /* Nothing to do */
        }
    }
    else
    {
        /* Nothing to do */
    }
    /* 21/06/28增加As时间监控逻辑 */
    if (GsTpTxMsg.GwTxuNAsCnt > Lu8TimeOffset)
    {
        GsTpTxMsg.GwTxuNAsCnt -= Lu8TimeOffset;
    }
    else if(GsTpTxMsg.GwTxuNAsCnt != 0u)/* 超时后直接取消发送 */
    {
        GsTpTxMsg.GwTxuNAsCnt = 0u;

        (void)TP_AbortMsg(PHYSICALCHANNEL);
        (void)TP_AbortMsg(FUNCTIONALCHANNEL);

        TP_TxConf (TP_MSG_STATUS_COM_NOK);
    }
    else
    {
        /* Nothing to do */
    }
}
/*********************************************************************
 * 函数名称:  TP_SendTimeManage
 *                                  
 * 功能描述:  TP层接收诊断报文时间管理  
 *                  
 * 入口参数:  Lu8TimeOffset：上次运行与本次运行时间差
 *
 * 输出参数:  无      
 *          
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 * 
 ********************************************************************/
void TP_ReciveTimeManage(uint8 Lu8TimeOffset)
{
    uint8 LcChannel;
    for (LcChannel = 0U; LcChannel < TP_CHANNEL_MAX; LcChannel ++)
    {
        TpRxMsg *const pTpRxMsg = &GsTpRxMsg[LcChannel];      /* 定义指针指向TP层参数 */

        if (pTpRxMsg->GwuNCrCnt > Lu8TimeOffset)
        {
            pTpRxMsg->GwuNCrCnt -= Lu8TimeOffset;
        }
        else if (pTpRxMsg->GwuNCrCnt != 0U)
        {
            pTpRxMsg->GwuNCrCnt = 0U;
            
            if (pTpRxMsg->GcRxStatus == TP_MSG_STATUS_RX_CF)   /* 等待N_Cr时间没有接收到后续帧，则接收超时 */
            {
                pTpRxMsg->GcRxStatus = TP_MSG_STATUS_NONE;
            } 
            else{}
        }
        else{}

        /* 21/06/28增加Ar时间监控逻辑 */
        if(pTpRxMsg->GwuNArCnt > Lu8TimeOffset)
        {
            pTpRxMsg->GwuNArCnt -= Lu8TimeOffset;
        }
        else if(pTpRxMsg->GwuNArCnt != 0u) /* 超时后直接取消发送 且只进来一次*/
        {
            pTpRxMsg->GwuNArCnt = 0u;

            (void)TP_AbortMsg(PHYSICALCHANNEL);
            (void)TP_AbortMsg(FUNCTIONALCHANNEL);

            TP_TxConf (TP_MSG_STATUS_COM_NOK);
        }
        else{}
    }
}
/**********************************************************************************************
* 函数名称: TP_TimeSequenceManage
*
* 功能描述: TP层时序管理
*           This functions is called to send FC
*
* 输入参数: 无
*
* 输出参数: None
*
* 返 回 值:  None
*
* 其它说明: 主要管理TP层事件参数，包括N_Bs，N_Cr
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************************************/
void TP_TimeSequenceManage (void)
{
    uint8 LcTPScanTimer;
    static uint8 LcSystemTimer = 0;
    
    /****************计算系统上一次运行TP库至这次运行的TP库的时间差***********************/
    if(*GsUDS_TP_Interface.GdSystemTimer >= LcSystemTimer)
    {
        LcTPScanTimer = (*GsUDS_TP_Interface.GdSystemTimer - LcSystemTimer);
    }
    else
    {
        LcTPScanTimer = (uint8)((uint8)0xFF - LcSystemTimer + (*GsUDS_TP_Interface.GdSystemTimer));
    }
    LcSystemTimer = *GsUDS_TP_Interface.GdSystemTimer;

    /******************发送时间参数监控****************************************************/
    TP_SendTimeManage(LcTPScanTimer);

    /******************接收时间参数监控****************************************************/
    TP_ReciveTimeManage(LcTPScanTimer);

    /******************等待UDS层响应的时间检测******************************************/
    if(GwWaitRespondTimer > LcTPScanTimer)
    {
        GwWaitRespondTimer -= LcTPScanTimer;
    }
    else if(GwWaitRespondTimer != 0u)/*只执行一次*/
    {
        GwWaitRespondTimer = 0u;
        (void)TP_RxEnable (PHYSICALCHANNEL, TP_RX_ENABLE_ON);        /* 等待UDS响应超时，使能物理通道和功能通道 */ 
        (void)TP_RxEnable (FUNCTIONALCHANNEL, TP_RX_ENABLE_ON);
    }
    else{}
}
/**********************************************************************************************
* 函数名称: TP_RespondStateManage
*
* 功能描述: TP响应报文发送状态管理
*
* 输入参数: 无
*
* 输出参数: None
*
* 返 回 值:  None
*
* 其它说明: 主要监控响应报文是否发送成功
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************************************/
static void TP_RespondStateManage(void)
{
    if((GsTpComStr.pRespondDiagFrameState[0] & COM_FRAME_STATUS_TX_CONF) != (uint8)0)  
    {
        GsTpComStr.pRespondDiagFrameState[0] &= (uint8)(~COM_FRAME_STATUS_TX_CONF);
        
        TP_ComTxConfirm();
    }
}

/**********************************************************************************************
* 函数名称: Tp_TransmitManage
*
* 功能描述: TP层发送管理
*
* 输入参数: 无
*
* 输出参数: None
*
* 返 回 值:  None
*
* 其它说明: Tp层管理
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************************************/
static void Tp_TransmitManage(void)
{
    TP_RespondStateManage();         /* 处理报文发送成功或失败 */
}

/**********************************************************************************************
* 函数名称: TP_Manage
*
* 功能描述: TP层管理
*
* 输入参数: 无
*
* 输出参数: None
*
* 返 回 值:  None
*
* 其它说明: Tp层管理
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************************************/
void TP_Manage(void)
{
 
    Tp_ReceiveManage();

    Tp_TransmitManage();

    TP_TimeSequenceManage();
}






