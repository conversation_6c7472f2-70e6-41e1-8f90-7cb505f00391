 #ifndef   __DID_H__
 #define   __DID_H__
  
/******************************************************************************* 
* Includes 
********************************************************************************/ 

#if (DID_ECU_CONFIG_BYTE_DEBUG_PRINT == (STD_ON))
#define DID_DEBUG_PRINTF                Lhprintf
#else
#define DID_DEBUG_PRINTF(...)
#endif

#include "Types.h"

#define DIDTYPE_X02                0
#define DIDTYPE_W02                1
#define DIDTYPE                    DIDTYPE_X02


#define DID_APP_NUM                     (uint8)(29u)
#define DID_CALI_NUM                    (uint8)(0u)
#define DID_TOTAL_NUM                   (uint8)(DID_APP_NUM + DID_CALI_NUM)


#define DID_NEED_UPDATA_NUM                   (uint8)(10)

typedef void (*tpfUpDataProcess)(void);


#define ECU_CONFIG_BYTE_FLAG     0xAACC  //0xBACD  //ABBA

#define FBL_UPDATE_FLAG                 0xAAAAAAAA
#define FBL_APP_VALID                   0x55555555
#define FBL_APP_INVALID                 0x00000000

#define SBL_VALID                       0x4C454541    /*LEEA*/
#define SBL_INVALID                     0x00000000

#define SBL_BAK_VALID                   0x5F42414B    /*_BAK*/
#define SBL_BAK_INVALID                 0x00000000

#define SBL_UPDATE_NEED                 0x4E454544    /*NEED*/
#define SBL_UPDATE_NOT_NEED             0x00000000

#define ACTIVE_NEW_SBL_VALID            0x66666666    /*ACTIVE_NEW_SBL*/
#define INACTIVE_NEW_SBL_VALID          0x99999999    /*ACTIVE_NEW_SBL*/
#define ACTIVE_NEW_SBL_INVALID          0x00000000

/****************************************************************************** 
* Constants and macros 
*******************************************************************************/

struct UDS_Updata_DID_Type
{
    uint16 Gu16DID;         /*读取前需要更新数据的DID*/   
    tpfUpDataProcess IpduFuc;  /*更新数据接口函数*/ 
};
struct DataIdentifier
{
    uint16 GwDID;               
    volatile uint8 *GcADDR;     
    uint16 GwDataLen;            
    uint8 GcDir;   
    volatile uint32 GdwADDR;    /*数据对应EEP地址*/
};
enum DataDirection
{
     OnlyRead = 0x01,
     ReadWrite= 0x02,
};
/****************************************************************************** 
* External objects 
********************************************************************************/ 
extern const struct DataIdentifier DIDDefinition[DID_TOTAL_NUM];
extern const struct UDS_Updata_DID_Type DIDDefinition_Needupdata[DID_NEED_UPDATA_NUM];
extern uint8 DID_F193_Buff[2];
extern uint8 DID_F18C_Buff[16];
extern uint8 DID_F14A_Buff[2];
extern uint8 DID_F14B_Buff[1];
extern uint8 DID_F1A1_Buff[17];
extern uint8 DID_F195_Buff[2];
extern uint8 DID_F187_Buff[15];
extern uint8 DID_F197_Buff[10];
extern uint8 DID_F026_Buff[2];
extern uint8 DID_F18B_Buff[4];
extern uint8 DID_F186_Buff[1];
extern uint8 GcEOLEndCfgFlg;

extern uint8 DID_FEFF_Buff[4];
extern uint8 DID_FEFE_Buff[128];
//extern uint8 DID_FEFD_Buff[1];
 /*******************************************************************************
 * Global Functions 
 ********************************************************************************/ 
extern void DIDInit(void);
extern void DID_F1A1_ECUConfigByteFlag_Init(void);
extern void DID_F1A1_ECUConfigByteFlag_Save(void);
extern void DID_init_F1B1(void);
extern void DID_F1B1_Save(void);
extern uint8 EOLEndECUConfigByteStatus(void);
extern void Updata_F15F(void);

#endif
