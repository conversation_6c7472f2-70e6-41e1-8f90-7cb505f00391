/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: Interrupt.c
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/
#include "types.h"
#include "intVecNumF1K.h"
#include "Interrupt.h"

/******************************************************************************/
/**<pre>
 *函数名称: INTRCANGRECC0
 *功能描述: CAN Receive FIFO interrupt
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
#pragma vector = NUMINTRCANGRECC0
__interrupt void INTRCANGRECC0(void)
{
    INTRCANGRECC0_CALLBACK();    /**<pre> 调用函数INTRCANGRECC0_CALLBACK()进行CAN接收中断处理 </pre>*/
    //INTC1.ICRCANGRECC0.BIT.RFRCANGRECC0=0;
}

///******************************************************************************/
///**<pre>
// *函数名称: INTRCAN0ERR
// *功能描述: CAN错误中断
// *输入参数: 无
// *输出参数: 无
// *返回数据: 无
// *修改记录: 无
//*********************************************************************** </pre>*/
//#pragma vector = NUMINTRCAN0ERR
//__interrupt void INTRCAN0ERR(void)
//{
//    INTRCAN0ERR_CALLBACK();    /**<pre> 调用函数INTRCAN0ERR_CALLBACK()进行CAN错误中断处理 </pre>*/
//    RSCFD0CFDC0ERFL = 0u;    /**<pre> 清除故障中断标志 </pre>*/
//}
//
///******************************************************************************/
///**<pre>
// *函数名称: INTRCAN0TRX
// *功能描述: CAN发送中断
// *输入参数: 无
// *输出参数: 无
// *返回数据: 无
// *修改记录: 无
//*********************************************************************** </pre>*/
//#pragma vector = NUMINTRCAN0TRX
//__interrupt void INTRCAN0TRX(void)
//{
//    INTRCAN0TRX_CALLBACK();
//}

 /******************************************************************************/
 /**<pre>
  *函数名称: NUMINTRCAN3REC
  *功能描述: CAN Receive FIFO interrupt
  *输入参数: 无
  *输出参数: 无
  *返回数据: 无
  *修改记录: 无
 *********************************************************************** </pre>*/
// #pragma vector = NUMINTRCAN3REC
// __interrupt void INTRCAN3REC(void)
// {
//     INTRCAN3REC_CALLBACK();    /**<pre> 调用函数INTRCANGRECC0_CALLBACK()进行CAN接收中断处理 </pre>*/
// }

 /******************************************************************************/
 /**<pre>
  *函数名称: INTRCAN3ERR
  *功能描述: CAN错误中断
  *输入参数: 无
  *输出参数: 无
  *返回数据: 无
  *修改记录: 无
 *********************************************************************** </pre>*/
 #pragma vector = NUMINTRCAN4ERR
 __interrupt void INTRCAN4ERR(void)
 {
     INTRCAN4ERR_CALLBACK();    /**<pre> 调用函数INTRCAN0ERR_CALLBACK()进行CAN错误中断处理 </pre>*/
     RSCFD0CFDC4ERFL = 0u;    /**<pre> 清除故障中断标志 </pre>*/
 }

 /******************************************************************************/
 /**<pre>
  *函数名称: INTRCAN0TRX
  *功能描述: CAN发送中断
  *输入参数: 无
  *输出参数: 无
  *返回数据: 无
  *修改记录: 无
 *********************************************************************** </pre>*/
 #pragma vector = NUMINTRCAN4TRX
 __interrupt void INTRCAN4TRX(void)
 {
     INTRCAN4TRX_CALLBACK();
 }

/******************************************************************************/
/**<pre>
 *函数名称: INTRLIN32UR0
 *功能描述: UART发送中断
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
#pragma vector = NUMINTRLIN32UR0
__interrupt void INTRLIN32UR0(void)
{
    INTRLIN32UR0_CALLBACK();    /**<pre> 调用函数INTRLIN32UR0_CALLBACK()进行UART发送中断处理 </pre>*/
}

/******************************************************************************/
/**<pre>
 *函数名称: INTRLIN32UR0
 *功能描述: UART接收中断
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
#pragma vector = NUMINTRLIN32UR1
__interrupt void INTRLIN32UR1(void)
{
    INTRLIN32UR1_CALLBACK();    /**<pre> 调用函数INTRLIN32UR1_CALLBACK()进行UART接收中断处理 </pre>*/
}

/******************************************************************************/
/**<pre>
 *函数名称: INTP0
 *功能描述: CAN唤醒中断
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
#pragma vector = NUMINTP0
__interrupt void INTP0(void)
{
    //INTP0_CALLBACK();    /**<pre> 调用函数INTP0_CALLBACK()进行CAN唤醒中断处理 </pre>*/
    /**<pre> 关闭INTP0中断并清除中断标志 </pre>*/
    INTC2.ICP0.BIT.MKP0 = _INT_PROCESSING_DISABLED;
    INTC2.ICP0.BIT.RFP0 = _INT_REQUEST_NOT_OCCUR;
}

/******************************************************************************/
/**<pre>
 *函数名称: INTP3
 *功能描述: BAT电源检测中断
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
#pragma vector = NUMINTP3
__interrupt void INTP3(void)
{
    //INTP3_CALLBACK();    /**<pre> 调用函数INTP3_CALLBACK()进行BAT电源掉电中断处理 </pre>*/
    /**<pre> 清除中断标志并使能中断 </pre>*/
    INTC2.ICP3.BIT.RFP3 = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICP3.BIT.MKP3 = _INT_PROCESSING_ENABLED;
}


/******************************************************************************/
/**<pre>
 *函数名称: INTOSTM0
 *功能描述: 定时器中断函数
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
#pragma vector = NUMINTOSTM0
__interrupt void INTOSTM0(void)
{
    INTOSTM0_CALLBACK();    /**<pre> 调用函数INTOSTM0_CALLBACK()进行定时器中断处理 </pre>*/
}

/******************************************************************************/
/**<pre>
 *函数名称: INTADC0SG1
 *功能描述: ADC0 ScanGroup1结束中断函数
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
#pragma vector = NUMINTADC0SG1
__interrupt void INTADC0SG1(void)
{
    INTADC0SG1_CALLBACK();
}

// #pragma vector = NUMINTCSIH0IC
// __interrupt void INTCSIH0IC(void)
// {
//     INTCSIH0IC_CALLBACK();    /**<pre> 调用函数INTCSIH0IC_CALLBACK()进行CSIH中断处理(不开启) </pre>*/
// }

// #pragma vector = NUMINTCSIH0IR
// __interrupt void INTCSIH0IR(void)
// {
//     INTCSIH0IR_CALLBACK();    /**<pre> 调用函数INTCSIH0IR_CALLBACK()进行CSIH中断处理(不开启) </pre>*/
// }

#pragma vector = NUMINTCSIH0RE
__interrupt void INTCSIH0IRE(void)
{
    INTCSIH0IRE_CALLBACK();    /**<pre> 调用函数INTCSIH0IRE_CALLBACK()进行CSIH中断处理(不开启) </pre>*/
}

#pragma vector = NUMINTCSIH2RE
__interrupt void INTCSIH2IRE(void)
{
    INTCSIH2IRE_CALLBACK();    /**<pre> 调用函数INTCSIH2IRE_CALLBACK()进行CSIH中断处理 </pre>*/
}
#pragma vector = NUMINTCSIH3RE
__interrupt void INTCSIH3IRE(void)
{
    INTCSIH3IRE_CALLBACK();    /**<pre> 调用函数INTCSIH3IRE_CALLBACK()进行CSIH中断处理 </pre>*/
}

// #pragma vector = NUMINTTAUD0I15
// __interrupt void INTTAUD0I15(void)
// {
//     INTTAUD0I15_CALLBACK();    /**<pre> 调用函数INTTAUD0I15_CALLBACK()进定时断处理(不开启) </pre>*/
// }

#pragma vector = NUMINTDMA1
__interrupt void INTDMA1(void)
{
    INTDMA1_CALLBACK();    /**<pre> 调用函数INTDMA1_CALLBACK()进行DMA1中断处理 </pre>*/
}
#pragma vector = NUMINTDMA3
__interrupt void INTDMA3(void)
{
    INTDMA3_CALLBACK();    /**<pre> 调用函数INTDMA3_CALLBACK()进行DMA3中断处理 </pre>*/
}
#pragma vector = NUMINTDMA5
__interrupt void INTDMA5(void)
{
    INTDMA5_CALLBACK();    /**<pre> 调用函数INTDMA3_CALLBACK()进行DMA3中断处理 </pre>*/
}
/******************************************************************************/
/**<pre>
 *函数名称: INTINTP2
 *功能描述: DSI MASTER0 DCR1B中断函数
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
#pragma vector = NUMINTINTP2
__interrupt void INTINTP2(void)
{
    INTINTP2_CALLBACK();
	//INTC2.ICP2.BIT.MKP2 = _INT_PROCESSING_DISABLED;
    INTC2.ICP2.BIT.RFP2 = _INT_REQUEST_NOT_OCCUR;
}

/******************************************************************************/
/**<pre>
 *函数名称: INTINTP7
 *功能描述: DSI MASTER1 DCR1B中断函数
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
#pragma vector = NUMINTINTP7
__interrupt void INTINTP7(void)
{
    INTINTP7_CALLBACK();
	//INTC2.ICP7.BIT.MKP7 = _INT_PROCESSING_DISABLED;
    INTC2.ICP7.BIT.RFP7 = _INT_REQUEST_NOT_OCCUR;
}

/******************************************************************************/
/**<pre>
 *函数名称: INTINTP8
 *功能描述: DSI MASTER0 DCR2B中断函数
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
#pragma vector = NUMINTINTP8
__interrupt void INTINTP8(void)
{
    INTINTP8_CALLBACK();
    //INTC2.ICP8.BIT.MKP8 = _INT_PROCESSING_DISABLED;
    INTC2.ICP8.BIT.RFP8 = _INT_REQUEST_NOT_OCCUR;
}

/******************************************************************************/
/**<pre>
 *函数名称: INTINTP11
 *功能描述: DSI MASTER1 DCR2B中断函数
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
#pragma vector = NUMINTINTP11
__interrupt void INTINTP11(void)
{
    INTINTP11_CALLBACK();
	//INTC2.ICP11.BIT.MKP11 = _INT_PROCESSING_DISABLED;
    INTC2.ICP11.BIT.RFP11 = _INT_REQUEST_NOT_OCCUR;
}