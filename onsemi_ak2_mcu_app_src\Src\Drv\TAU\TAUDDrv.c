/***********************************************************************************************************************
* DISCLAIMER
* This software is supplied by Renesas Electronics Corporation and is only 
* intended for use with Renesas products. No other uses are authorized. This 
* software is owned by Renesas Electronics Corporation and is protected under 
* all applicable laws, including copyright laws.
* THIS SOFTWARE IS PROVIDED "AS IS" AND R<PERSON><PERSON>AS MAKES NO WARRANTIES REGARDING 
* THIS SOFTWARE, WHETHER EXPRESS, IMPLIED OR STATUTORY, INCLUDING BUT NOT 
* LIMITED TO WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE 
* AND NON-INFRINGEMENT.  ALL SUCH WARRANTIES ARE EXPRESSLY DISCLAIMED.
* TO THE MAXIMUM EXTENT PERMITTED NOT PROHIBITED BY LAW, NEITHER RENESAS 
* ELECTRONICS CORPORATION NOR ANY OF ITS AFFILIATED COMPANIES SHALL BE LIABLE 
* FOR ANY DIRECT, INDIRECT, SPECIAL, INCIDENTAL OR CONSEQUENTIAL DAMAGES FOR 
* ANY REASON RELATED TO THIS SOFTWARE, EVEN IF RENESAS OR ITS AFFILIATES HAVE 
* BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.
* Renesas reserves the right, without notice, to make changes to this software 
* and to discontinue the availability of this software.  By using this software, 
* you agree to the additional terms and conditions found by accessing the 
* following link:
* http://www.renesas.com/disclaimer
*
* Copyright (C) 2015, 2018 Renesas Electronics Corporation. All rights reserved.
***********************************************************************************************************************/

/***********************************************************************************************************************
* File Name    : r_cg_taud.c
* Version      : Applilet4 for RH850/F1K V1.01.02.02 [08 May 2018]
* Device(s)    : R7F701581(LQFP100pin)
* Tool-Chain   : CCRH
* Description  : This file implements device driver for TAUD module.
* Creation Date: 2023/10/20
***********************************************************************************************************************/

/***********************************************************************************************************************
Pragma directive
***********************************************************************************************************************/
/* Start user code for pragma. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */

/***********************************************************************************************************************
Includes
***********************************************************************************************************************/
#include "TAUDDrv.h"
/* Start user code for include. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */

/***********************************************************************************************************************
Global variables and functions
***********************************************************************************************************************/
extern volatile uint32_t  g_cg_sync_read;  
/* Start user code for global. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */

void TAUDInit(void)
{
    R_TAUD0_Create();
    R_TAUD0_Channel15_Start();
}

/***********************************************************************************************************************
* Function Name: R_TAUD0_Create
* Description  : This function initializes the TAUD0 Bus Interface.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_TAUD0_Create(void)
{
    /* Disable channel0 counter operation */
    TAUD0.TT = _TAUD_CHANNEL15_COUNTER_STOP | _TAUD_CHANNEL14_COUNTER_STOP | _TAUD_CHANNEL13_COUNTER_STOP | 
               _TAUD_CHANNEL12_COUNTER_STOP | _TAUD_CHANNEL11_COUNTER_STOP | _TAUD_CHANNEL10_COUNTER_STOP | 
               _TAUD_CHANNEL9_COUNTER_STOP | _TAUD_CHANNEL8_COUNTER_STOP | _TAUD_CHANNEL7_COUNTER_STOP | 
               _TAUD_CHANNEL6_COUNTER_STOP | _TAUD_CHANNEL5_COUNTER_STOP | _TAUD_CHANNEL4_COUNTER_STOP | 
               _TAUD_CHANNEL3_COUNTER_STOP | _TAUD_CHANNEL2_COUNTER_STOP | _TAUD_CHANNEL1_COUNTER_STOP | 
               _TAUD_CHANNEL0_COUNTER_STOP;
    /* Disable INTTAUD0I15 operation and clear request */
    INTC2.ICTAUD0I15.BIT.MKTAUD0I15 = _INT_PROCESSING_DISABLED;
    INTC2.ICTAUD0I15.BIT.RFTAUD0I15 = _INT_REQUEST_NOT_OCCUR;
    /* Set INTTAUD0I15 setting */
    INTC2.ICTAUD0I15.BIT.TBTAUD0I15 = _INT_TABLE_VECTOR;
    INTC2.ICTAUD0I15.UINT16 &= _INT_PRIORITY_LOWEST;
    TAUD0.TPS = _TAUD_CK3_PRE_PCLK_0 | _TAUD_CK2_PRE_PCLK_0 | _TAUD_CK1_PRE_PCLK_0 | _TAUD_CK0_PRE_PCLK_7;
    TAUD0.BRS = _TAUD0_CK3_PRE_CLOCK_DIVISION;
    /* Set channel 15 setting */
    TAUD0.CMOR15 = _TAUD_SELECTION_CK0 | _TAUD_COUNT_CLOCK_PCLK | _TAUD_INDEPENDENT_CHANNEL | 
                   _TAUD_START_TRIGGER_VALID_EDGE | _TAUD_OVERFLOW_AUTO_CLEAR | _TAUD_COUNT_UP_DOWN_MODE | 
                   _TAUD_START_TRIGGER_DISABLE;          
    TAUD0.CMUR15 = _TAUD_INPUT_EDGE_FALLING;
    TAUD0.CDR15 = _TAUD0_CHANNEL15_COMPARE_VALUE;
    /* Set output mode setting */
    TAUD0.TOE = _TAUD_CHANNEL15_DISABLES_OUTPUT_FUNCTION;
    /* Synchronization processing */
    g_cg_sync_read = TAUD0.TPS;

    SL_TAUD0.SELB_TAUD0I = _TAUD_NO_CHANGE_INPUT_SIGNAL;
    /* Set TAUD0I15 pin */
    PORT.PIBC10 &= _PORT_CLEAR_BIT7;
    PORT.PBDC10 &= _PORT_CLEAR_BIT7;
    PORT.PM10 |= _PORT_SET_BIT7;  
    PORT.PMC10 &= _PORT_CLEAR_BIT7;
    PORT.PIPC10 &= _PORT_CLEAR_BIT7;
    PORT.PFC10 &= _PORT_CLEAR_BIT7;
    PORT.PFCE10 &= _PORT_CLEAR_BIT7;
    PORT.PMC10 |= _PORT_SET_BIT7;  
    DNF.ATAUD0IENH.BIT.ATAUD0IENH7 = _TAUD_FILTER_ENABLED;
}
/***********************************************************************************************************************
* Function Name: R_TAUD0_Channel15_Start
* Description  : This function clears TAUD015 interrupt flag and enables interrupt.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_TAUD0_Channel15_Start(void)
{
    /* Clear INTTAUD0I15 request and enable operation */
    INTC2.ICTAUD0I15.BIT.RFTAUD0I15 = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICTAUD0I15.BIT.MKTAUD0I15 = _INT_PROCESSING_DISABLED;    
    /* Enable channel15 counter operation */
    TAUD0.TS |= _TAUD_CHANNEL15_COUNTER_START;
}
/***********************************************************************************************************************
* Function Name: R_TAUD0_Channel15_Stop
* Description  : This function disables TAUD015 interrupt and clears interrupt flag.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_TAUD0_Channel15_Stop(void)
{
    /* Disable channel15 counter operation */
    TAUD0.TT |= _TAUD_CHANNEL15_COUNTER_STOP;
    /* Synchronization processing */
    g_cg_sync_read = TAUD0.TT;
}

/* Start user code for adding. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */
