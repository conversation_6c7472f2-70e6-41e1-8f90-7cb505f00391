/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: IOHal.h
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/

#ifndef __IOHAL_H
#define __IOHAL_H

#include "types.h"
#include "IODrv.h"

/*************************************宏定义***********************************/
#define READ_CHIP_SUPPLY_POWER_EN_PIN	R_PORT_GetLevel(Port8, 0);   
#define READ_SNS_GROUP1_POWER_EN_PIN	R_PORT_GetLevel(Port8, 1); 
#define READ_SNS_GROUP2_POWER_EN_PIN    R_PORT_GetLevel(Port8, 12);  
#define READ_SNS_GROUP3_POWER_EN_PIN    R_PORT_GetLevel(Port8, 11);

/*********************************数据类型定义*********************************/

/*******************************全局函数声明***********************************/
extern void IOHalInit(void);
extern void IOHalClose(void);

#endif
