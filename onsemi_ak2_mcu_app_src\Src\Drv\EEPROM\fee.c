#include "fee.h"
#include "R_FDL.h"
#include "FDL_Descriptor.h"
#include "R_EEL.h"
#include "EEL_Descriptor.h"

static inline void Fee_EEL_ErrorHdr(r_eel_status_t stat);
static inline void Fee_FDL_ErrorHdr(r_fdl_status_t stat);
static inline void Fee_EEL_HdrLoop( r_eel_request_t *req );
static inline r_eel_status_t Fee_EEL_Init(void);
static inline r_eel_status_t Fee_EEL_ReinitCheck(r_eel_status_t eel_status);

static boolean GbEELInitFlag = FALSE;

void DL_EELInit(void)
{
    r_eel_status_t                eelRet = R_EEL_OK;
    eelRet = Fee_EEL_Init();
    eelRet = Fee_EEL_ReinitCheck(eelRet);
    if(eelRet == R_EEL_OK)
    {
        GbEELInitFlag = TRUE;
    }
    else
    {
        GbEELInitFlag = FALSE;
    }
}

r_eel_status_t DL_EELShutDown(void)
{
    r_eel_status_t                eelRet = R_EEL_OK;
    r_eel_driver_status_t         driverStatus;
    /* Shut down processing */
    eelRet = R_EEL_ShutDown();
    Fee_EEL_ErrorHdr(eelRet); 
    do
    {
        R_EEL_Handler();
        R_EEL_GetDriverStatus(&driverStatus);
    }
    while((R_EEL_OPERATION_PASSIVE != driverStatus.operationStatus_enu));
    eelRet = driverStatus.backgroundStatus_enu;
    GbEELInitFlag = FALSE;
    return eelRet;
}

r_eel_status_t DL_EELWrite(uint8_t* pData,uint16_t blockId)
{
    r_eel_request_t           Req;
    r_eel_status_t            reqStat = R_EEL_ERR_NO_INIT;

    if(TRUE == GbEELInitFlag)
    {
        /* write access */
        Req.address_pu08 = pData;
        Req.identifier_u16 = blockId;
        Req.command_enu = R_EEL_CMD_WRITE;
        R_EEL_Execute(&Req);
        Fee_EEL_HdrLoop(&Req);
        if (Req.status_enu == R_EEL_ERR_POOL_FULL)
        {
            /* rewrite access */
            Req.address_pu08 = pData;
            Req.identifier_u16 = blockId;
            Req.command_enu = R_EEL_CMD_WRITE;
            R_EEL_Execute(&Req);
            Fee_EEL_HdrLoop(&Req);
        }
        reqStat = Req.status_enu;
    }
    return reqStat;
}

r_eel_status_t DL_EELRead(uint8_t* pData, uint16_t blockId, uint16_t len)
{
    r_eel_request_t           Req;
    r_eel_status_t            reqStat = R_EEL_ERR_NO_INIT;
    
    if(TRUE == GbEELInitFlag)
    {
        /* read access */
        Req.address_pu08 = pData;
        Req.identifier_u16 = blockId;
        Req.length_u16 = len;
        Req.offset_u16 = 0x0u;
        Req.command_enu = R_EEL_CMD_READ;
        R_EEL_Execute(&Req);
        Fee_EEL_HdrLoop(&Req);
        reqStat = Req.status_enu;
    }
    return reqStat;
}

static inline void Fee_EEL_ErrorHdr(r_eel_status_t stat)
{
    if(R_EEL_OK != stat)
    {
        if(R_EEL_ERR > stat)
        {
            /* If the status is a warning only, we might ignore it */
        }
        else
        {
            /* Error handler */
            FEE_EEL_ERR_HOOK(stat);
        }
    }
}

static inline void Fee_FDL_ErrorHdr(r_fdl_status_t stat)
{
    /* Error handler */
    FEE_FDL_ERR_HOOK(stat);
}

static inline void Fee_EEL_HdrLoop( r_eel_request_t *req )
{
    r_eel_driver_status_t Sts;

    /* Wait until operation end */
    while(R_EEL_BUSY == req->status_enu)
    {
        R_EEL_Handler();
    }

    if (R_EEL_ERR_POOL_FULL == req->status_enu)
    {
        R_EEL_GetDriverStatus(&Sts);
        while (R_EEL_OPERATION_IDLE != Sts.operationStatus_enu)
        {
            R_EEL_Handler();
            R_EEL_GetDriverStatus(&Sts);
        }
    }
    Fee_EEL_ErrorHdr(req->status_enu);         /* Return value check */
}

static inline r_eel_status_t Fee_EEL_Init(void)
{
    r_fdl_status_t                fdlRet;
    r_eel_status_t                eelRet = R_EEL_OK;
    r_eel_driver_status_t         driverStatus;
    r_fdl_request_t               fdlReq;

    /* 1st initialize the FDL */
    fdlRet = R_FDL_Init(&fdlConfig_enu);
    if( R_FDL_OK != fdlRet )
    {   
        /* Error handler */
        Fee_FDL_ErrorHdr(fdlRet);
    }
    
    /* Prepare the environment */
    fdlReq.command_enu     = R_FDL_CMD_PREPARE_ENV;
    fdlReq.idx_u32         = 0;
    fdlReq.cnt_u16         = 0;
    fdlReq.accessType_enu  = R_FDL_ACCESS_NONE;
    R_FDL_Execute(&fdlReq);
    
    while(R_FDL_BUSY == fdlReq.status_enu)
    {
        R_FDL_Handler();
    }

    if(R_FDL_OK != fdlReq.status_enu)
    {   
        /* Error handler */
        Fee_FDL_ErrorHdr(fdlReq.status_enu);
    }

    /* Next initialize the EEL */
    eelRet = R_EEL_Init(&eelConfig_enu, R_EEL_OPERATION_MODE_NORMAL);
    Fee_EEL_ErrorHdr(eelRet);         /* Return value check */
    
    eelRet = R_EEL_Startup();
    Fee_EEL_ErrorHdr( eelRet );         /* Return value check */
    
    /* Call the R_EEL_Handler for startup processing until limited access is possible or 
       error occurred. */
    do
    {
        R_EEL_Handler();
        R_EEL_GetDriverStatus( &driverStatus );
    }
    /* Wait until early read/write is possible (or error) */
    while((( R_EEL_OPERATION_STARTUP == driverStatus.operationStatus_enu )
           &&( R_EEL_ACCESS_LOCKED == driverStatus.accessStatus_enu)));

    return driverStatus.backgroundStatus_enu;
}

static inline r_eel_status_t Fee_EEL_ReinitCheck(r_eel_status_t eel_status)
{
    r_eel_request_t               eelReq;
    r_eel_status_t                eelRet = R_EEL_OK;
    /* Format eel if pool is inconsistent */
    if(R_EEL_ERR_POOL_INCONSISTENT == eel_status)
    {    
        eelReq.command_enu = R_EEL_CMD_FORMAT;
        R_EEL_Execute(&eelReq);
        Fee_EEL_HdrLoop(&eelReq);
        /* Format procedure ends with EEL re-init */
        eelRet = Fee_EEL_Init();
    }
    return eelRet;
}
