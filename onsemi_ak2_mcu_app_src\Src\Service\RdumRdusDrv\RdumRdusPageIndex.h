/******************************************************************************
 * @file      RdumRdusPageIndex.h
 * @brief     安森美超声波探头页面和索引地址映射定义
 * <AUTHOR>
 * @date      2025-05-15
 * @note
 *****************************************************************************/

#ifndef __RDUM_RDUS_PAGE_INDEX_H__
#define __RDUM_RDUS_PAGE_INDEX_H__
/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "types.h"

/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/
/* 页面定义 */
#define PAGE_0 0x00 /* 页面0 - 基本配置和状态 */
#define PAGE_1 0x01 /* 页面1 - 非易失性存储器 */
#define PAGE_2 0x02 /* 页面2 - 通道H循环数据缓冲区 */
#define PAGE_3 0x03 /* 页面3 - 通道L循环数据缓冲区 */

/* 页面0索引定义 */
#define PAGE0_IDX_ICMFGID 0x00          /* IC制造商ID */
#define PAGE0_IDX_SENSOR_ID 0x08        /* 传感器ID */
#define PAGE0_IDX_UNLOCK_ID 0x0A        /* 解锁ID */
#define PAGE0_IDX_GP_STATUS 0x0C        /* 通用状态位 */
#define PAGE0_IDX_GP_CONTROL 0x0E       /* 通用控制位 */
#define PAGE0_IDX_DIR_CFAR_SNR 0x10     /* 直接CFAR SNR比例 */
#define PAGE0_IDX_ICREV 0x20            /* ASIC修订标识符 */
#define PAGE0_IDX_WAFER_NR 0x22         /* 晶圆编号 */
#define PAGE0_IDX_LOT_NUMBER_LSB 0x24   /* 批次号LSB */
#define PAGE0_IDX_DLT 0x26              /* DLT坐标 */
#define PAGE0_IDX_CRC_NVM_ONSEMI 0x30   /* NVM onsemi区域CRC */
#define PAGE0_IDX_TX_PER_L 0x40         /* 通道L中心啁啾周期或AM周期 */
#define PAGE0_IDX_TX_PER_H 0x42         /* 通道H中心啁啾周期或AM周期 */
#define PAGE0_IDX_CORR_L_SCALE 0x44     /* 通道L相关器比例因子 */
#define PAGE0_IDX_CORR_H_SCALE 0x46     /* 通道H相关器比例因子 */
#define PAGE0_IDX_TX_CURRENT 0x48       /* 发送电流索引 */
#define PAGE0_IDX_MEAS_DURATION 0x4A    /* 测量持续时间 */
#define PAGE0_IDX_GAIN_LNA 0x4C         /* LNA增益控制 */
#define PAGE0_IDX_DELTA_GAIN_0 0x4E     /* 增益差值0 */
#define PAGE0_IDX_DELTA_GAIN_1 0x50     /* 增益差值1 */
#define PAGE0_IDX_DELTA_GAIN_2 0x52     /* 增益差值2 */
#define PAGE0_IDX_DELTA_GAIN_3 0x54     /* 增益差值3 */
#define PAGE0_IDX_DELTA_GAIN_4 0x56     /* 增益差值4 */
#define PAGE0_IDX_DBG_OUTPUT_SEL 0x58   /* 调试输出选择 */
#define PAGE0_IDX_DIR_CFAR_THR 0x5A     /* 直接CFAR阈值 */
#define PAGE0_IDX_INDIR_CFAR_THR 0x5C   /* 间接CFAR阈值 */
#define PAGE0_IDX_AUTOCORREL_SUPPR 0x5E /* 自相关抑制因子 */
#define PAGE0_IDX_CRC_MODE_CONFIG 0x60  /* DSP参数CRC16 */
#define PAGE0_IDX_COM_SLOT 0x62         /* 通信时隙 */
#define PAGE0_IDX_PDCM_R_IDX 0x64       /* PDCM读指针索引 */
#define PAGE0_IDX_MEAS_IDX 0x68         /* 测量数据指针 */
#define PAGE0_IDX_CRM_W_IDX 0x6A        /* CRM写指针索引 */
#define PAGE0_IDX_KAC 0x70              /* 保活计数器 */
#define PAGE0_IDX_BLOCK_DATA_0 0x74     /* WRITE_BLK_P帧数据缓冲区0 */
#define PAGE0_IDX_BLOCK_DATA_1 0x76     /* WRITE_BLK_P帧数据缓冲区1 */
#define PAGE0_IDX_BLOCK_DATA_2 0x78     /* WRITE_BLK_P帧数据缓冲区2 */
#define PAGE0_IDX_BLOCK_DATA_3 0x7A     /* WRITE_BLK_P帧数据缓冲区3 */
#define PAGE0_IDX_BLOCK_DATA_4 0x7C     /* WRITE_BLK_P帧数据缓冲区4 */
#define PAGE0_IDX_BLOCK_DATA_5 0x7E     /* WRITE_BLK_P帧数据缓冲区5 */

/* 页面1索引定义 */
#define PAGE1_IDX_NVM_SECTOR_0 0x00      /* 非易失性存储器 - 客户区域0 */
#define PAGE1_IDX_NVM_SECTOR_1 0x02      /* 非易失性存储器 - 客户区域1 */
#define PAGE1_IDX_NVM_SECTOR_2 0x04      /* 非易失性存储器 - 客户区域2 */
#define PAGE1_IDX_NVM_SECTOR_3 0x06      /* 非易失性存储器 - 客户区域3 */
#define PAGE1_IDX_NVM_SECTOR_4 0x08      /* 非易失性存储器 - 客户区域4 */
#define PAGE1_IDX_NVM_SECTOR_5 0x0A      /* 非易失性存储器 - 客户区域5 */
#define PAGE1_IDX_NVM_SECTOR_6 0x0C      /* 非易失性存储器 - 客户区域6 */
#define PAGE1_IDX_NVM_SECTOR_7 0x0E      /* 非易失性存储器 - 客户区域7 */
#define PAGE1_IDX_NVM_SECTOR_8 0x10      /* 非易失性存储器 - 客户区域8 */
#define PAGE1_IDX_NVM_SECTOR_9 0x12      /* 非易失性存储器 - 客户区域9 */
#define PAGE1_IDX_NVM_SECTOR_10 0x14     /* 非易失性存储器 - 客户区域10 */
#define PAGE1_IDX_NVM_SECTOR_11 0x16     /* 非易失性存储器 - 客户区域11 */
#define PAGE1_IDX_NVM_SECTOR_12 0x18     /* 非易失性存储器 - 客户区域12 */
#define PAGE1_IDX_CRC_NVM 0x1A           /* 保护NVM客户区域的16位CRC */
#define PAGE1_IDX_SAFETY_STATUS_0 0x1C   /* 安全标志0 */
#define PAGE1_IDX_SAFETY_STATUS_1 0x1E   /* 安全标志1 */
#define PAGE1_IDX_PDCM_SIZE 0x20         /* PDCM大小 */
#define PAGE1_IDX_CRC_DSI_CONFIG 0x22    /* DSI3ON主设置CRC-8 */
#define PAGE1_IDX_BEH_CH 0x24            /* 通道行为 */
#define PAGE1_IDX_OVT_THRESHOLD 0x26     /* 过温阈值 */
#define PAGE1_IDX_DOPP_CORR 0x2A         /* 多普勒校正因子 */
#define PAGE1_IDX_FREQ_TRIM 0x2E         /* 频率调整因子 */
#define PAGE1_IDX_MODE_CONFIG_0 0x30     /* 模式0参数 */
#define PAGE1_IDX_CRC_MODE_CONFIG_0 0x50 /* 模式0参数CRC */
#define PAGE1_IDX_MODE_CONFIG_1 0x52     /* 模式1参数 */
#define PAGE1_IDX_CRC_MODE_CONFIG_1 0x72 /* 模式1参数CRC */
#define PAGE1_IDX_MODE_CONFIG_2 0x74     /* 模式2参数 */
#define PAGE1_IDX_CRC_MODE_CONFIG_2 0x94 /* 模式2参数CRC */
#define PAGE1_IDX_MODE_CONFIG_3 0x96     /* 模式3参数 */
#define PAGE1_IDX_CRC_MODE_CONFIG_3 0xB6 /* 模式3参数CRC */
#define PAGE1_IDX_MODE_CONFIG_4 0xB8     /* 模式4参数 */
#define PAGE1_IDX_CRC_MODE_CONFIG_4 0xD8 /* 模式4参数CRC */
#define PAGE1_IDX_MODE_CONFIG_5 0xDA     /* 模式5参数 */
#define PAGE1_IDX_CRC_MODE_CONFIG_5 0xFA /* 模式5参数CRC */

/* 页面2索引定义 - 通道H循环数据缓冲区 */
/* 页面3索引定义 - 通道L循环数据缓冲区 */
/* 这些索引从0x00到0x3E，每2个字节一个条目 */

/******************************************************************************
 * @Type Definitions
 *****************************************************************************/
/* 页面索引结构 */
typedef struct
{
    uint8 Page;  /* 页面 */
    uint8 Index; /* 索引 */
} RdumRdusPageIndex_t;

/******************************************************************************
 * @Page Index
 *****************************************************************************/
typedef union
{
    uint16 PageIndexData;
    uint8 Bytes[2];

    /* PAGE0_IDX_ICMFGID (0x00) 位定义 */
    struct
    {
        uint8 ictypeid : 8; /* [15:8] */
        uint8 icmfgid : 8;  /* [7:0] */
    } page0_idx_icmfgid_t;

    /* PAGE0_IDX_DIR_CFAR_SNR (0x10) 位定义 */
    struct
    {
        uint8 reserved_H : 8;            /* [15:8] */

        uint8 dir_cfar_snr_scale : 3;    /* [2:0] */
        uint8 indir_cfar_snr_scale : 3;  /* [5:3] */
        uint8 reserved_L : 2;            /* [7:6] */
    } page0_idx_dir_cfar_snr_t;

    /* PAGE0_IDX_TX_PER_L (0x40) 位定义 */
    struct
    {
        uint8 tx_per_l_H : 2;    /* [9:8] */
        uint8 reserved : 5;      /* [14:10] */
        uint8 tx_per_h_msb : 1;  /* [15] - tx_per_h[9] */


        uint8 tx_per_l_L : 8;    /* [7:0] */
    } page0_idx_tx_per_l_t;

    /* PAGE0_IDX_TX_PER_H (0x42) 位定义 */
    struct
    {
        uint8 tx_per_h_H : 1;      /* [8] - tx_per_h[8] */
        uint8 dyn_gain_offset : 7; /* [15:9] */

        uint8 tx_per_h_L : 8;      /* [7:0] - tx_per_h[7:0] */
    } page0_idx_tx_per_h_t;

    /* PAGE0_IDX_CORR_L_SCALE (0x44) 位定义 */
    struct
    {
        uint8 corr_l_scale_H : 2; /* [9:8] corr_l_scale的高位 */
        uint8 corr_l_scale_l : 5; /* [14:10] */
        uint8 reserved : 1;       /* [15] */


        uint8 corr_l_scale_r : 5; /* [4:0] */
        uint8 corr_l_scale_L : 3; /* [7:5] corr_l_scale的低位*/
    } page0_idx_corr_l_scale_t;

    /* PAGE0_IDX_CORR_H_SCALE (0x46) 位定义 */
    struct
    {
        uint8 corr_h_scale_H : 2; /* [9:8] corr_h_scale的高位*/
        uint8 corr_h_scale_l : 5; /* [14:10] */
        uint8 reserved : 1;       /* [15] */


        uint8 corr_h_scale_r : 5; /* [4:0] */
        uint8 corr_h_scale_L : 3; /* [7:5] corr_h_scale的低位*/
    } page0_idx_corr_h_scale_t;

    /* PAGE0_IDX_TX_CURRENT (0x48) 位定义 */
    struct
    {
        uint8 tx_current_h_H : 6; /* [13:8] tx_current_h的高位 */
        uint8 dbg_rx_ena : 1;   /* [14] */
        uint8 dbg_ena : 1;      /* [15] */

        uint8 tx_current_l : 7; /* [6:0] */
        uint8 tx_current_h_L : 1; /* [7] tx_current_h的低位 */
    } page0_idx_tx_current_t;

    /* PAGE0_IDX_MEAS_DURATION (0x4A) 位定义 */
    struct
    {
        uint8 meas_type_H : 4;   /* [11:8] meas_type的高位*/
        uint8 wb_noise_th : 3;   /* [14:12] */
        uint8 rev_sh_ena : 1;    /* [15] */

        uint8 meas_duration : 7; /* [6:0] */
        uint8 meas_type_L : 1;   /* [7] meas_type的低位*/
    } page0_idx_meas_duration_t;

    /* PAGE0_IDX_GAIN_LNA (0x4C) 位定义 */
    struct
    {
        uint8 rev_end_timeout : 6; /* [13:8] */
        uint8 reserved : 2;        /* [15:14] */

        uint8 gain_lna : 2;        /* [1:0] */
        uint8 tx_damp : 6;         /* [7:2] */
    } page0_idx_gain_lna_t;

    /* PAGE0_IDX_DELTA_GAIN_0 (0x4E) 位定义 */
    struct
    {
        uint8 delta_gain_tim0_H : 3; /* [10:8] delta_gain_tim0的高位 */
        uint8 reserved : 5;          /* [15:11] */

        uint8 delta_gain_val0 : 7; /* [6:0] */
        uint8 delta_gain_tim0_L : 1; /* [7] delta_gain_tim0的低位*/
    } page0_idx_delta_gain_0_t;

    /* PAGE0_IDX_DELTA_GAIN_1 (0x50) 位定义 */
    struct
    {
        uint8 delta_gain_tim1_H : 3;               /* [10:8] delta_gain_tim1的高位 */
        uint8 reserved1 : 1;                        /* [11] */
        uint8 rx_10kohm_ena_lna_discn_vga_ena : 2; /* [13:12] - [rx_10kohm_ena, lna_discn_vga_ena] */
        uint8 rev_sh : 2;                          /* [15:14] */

        uint8 delta_gain_val1 : 7;                 /* [6:0] */
        uint8 delta_gain_tim1_L : 1;               /* [7] delta_gain_tim1的低位 */
    } page0_idx_delta_gain_1_t;

    /* PAGE0_IDX_DELTA_GAIN_2 (0x52) 位定义 */
    struct
    {
        uint8 delta_gain_tim2_H : 3; /* [10:8]  delta_gain_tim2的高位 */
        uint8 comp_method : 3;       /* [13:11] */
        uint8 vsup_tx_th : 2;      /* [15:14] */

        uint8 delta_gain_val2 : 7; /* [6:0] */
        uint8 delta_gain_tim2_L : 1; /* [7]  delta_gain_tim2的低位 */
    } page0_idx_delta_gain_2_t;

    /* PAGE0_IDX_DELTA_GAIN_3 (0x54) 位定义 */
    struct
    {
        uint8 delta_gain_tim3_H : 3; /* [10:8] delta_gain_tim3的高位 */
        uint8 var_gain_enable : 1;   /* [11] */
        uint8 var_gain_start : 4;  /* [15:12] */

        uint8 delta_gain_val3 : 7; /* [6:0] */
        uint8 delta_gain_tim3_L : 1; /* [7] delta_gain_tim3的低位 */
    } page0_idx_delta_gain_3_t;

    /* PAGE0_IDX_DELTA_GAIN_4 (0x56) 位定义 */
    struct
    {
        uint8 delta_gain_tim4_H : 3; /* [10:8] delta_gain_tim4的高位 */
        uint8 indir_cfar_scale : 3;  /* [13:11] */
        uint8 reserved : 2;         /* [15:14] */

        uint8 delta_gain_val4 : 7;  /* [6:0] */
        uint8 delta_gain_tim4_L : 1; /* [7]  delta_gain_tim4的低位 */
    } page0_idx_delta_gain_4_t;

    /* PAGE0_IDX_DBG_OUTPUT_SEL (0x58) 位定义 */
    struct
    {
        uint8 zif_scale_H : 3;         /* [10:8] zif_scale的高位 */
        uint8 dir_cfar_scale : 3;      /* [13:11] */
        uint8 comp_method_b3 : 1;      /* [14] */
        uint8 reserved2 : 1;           /* [15] */

        uint8 dbg_output_sel_low : 2;  /* [1:0] - dbg_output_sel[1:0] */
        uint8 dbg_output_sel_high : 4; /* [5:2] - dbg_output_sel[5:2] */
        uint8 reserved1 : 1;           /* [6] */
        uint8 zif_scale_L : 1;         /* [7] - zif_scale的低位 */
    } page0_idx_dbg_output_sel_t;

    /* PAGE0_IDX_DIR_CFAR_THR (0x5A) 位定义 */
    struct
    {
        uint8 dir_cfar_der_thr : 6;  /* [13:8] */
        uint8 dir_cfar_der_size : 2; /* [15:14] */

        uint8 dir_cfar_thr_off : 8;  /* [7:0] */
    } page0_idx_dir_cfar_thr_t;

    /* PAGE0_IDX_INDIR_CFAR_THR (0x5C) 位定义 */
    struct
    {
        uint8 indir_cfar_der_thr : 6;  /* [13:8] */
        uint8 indir_cfar_der_size : 2; /* [15:14] */

        uint8 indir_cfar_thr_off : 8;  /* [7:0] */
    } page0_idx_indir_cfar_thr_t;

    /* PAGE0_IDX_AUTOCORREL_SUPPR (0x5E) 位定义 */
    struct
    {
        uint8 reserved_H : 8;          /* [15:8] */

        uint8 autocorrel_suppr_h : 3; /* [2:0] */
        uint8 autocorrel_suppr_l : 3; /* [5:3] */
        uint8 reserved_L : 2;         /* [7:6] */
    } page0_idx_autocorrel_suppr_t;

    /* PAGE0_IDX_COM_SLOT (0x62) 位定义 */
    struct
    {
        uint8 com_slot_assignment : 8; /* [15:8] */

        uint8 com_slot_h : 4;          /* [3:0] */
        uint8 com_slot_l : 4;          /* [7:4] */
    } page0_idx_com_slot_t;

    /* PAGE0_IDX_PDCM_R_IDX (0x64) 位定义 */
    struct
    {
        uint8 pdcm_r_idx_h : 8; /* [15:8] */
        uint8 pdcm_r_idx_l : 8; /* [7:0] */
    } page0_idx_pdcm_r_idx_t;

    /* PAGE0_IDX_MEAS_IDX (0x68) 位定义 */
    struct
    {
        uint8 meas_idx_l : 8; /* [15:8] */
        uint8 meas_idx_h : 8; /* [7:0] */
    } page0_idx_meas_idx_t;

    /* PAGE0_IDX_CRM_W_IDX (0x6A) 位定义 */
    struct
    {
        uint8 crm_w_pg : 2;  /* [9:8] */
        uint8 reserved : 2;  /* [11:10] */
        uint8 pdcm_r_pg : 2; /* [13:12] */
        uint8 reserved2 : 2; /* [15:14] */

        uint8 crm_w_idx : 8; /* [7:0] */
    } page0_idx_crm_w_idx_t;

    /* PAGE1_IDX_PDCM_SIZE (0x20) 位定义 */
    struct
    {
        uint8 t_chip : 1;        /* [8] */
        uint8 crc_mode : 1;      /* [9] */
        uint8 scrambler_ena : 1; /* [10] */
        uint8 dsi3on_vpp : 1;    /* [11] */
        uint8 mount_id : 3;      /* [14:12] */
        uint8 vsds : 1;          /* [15] */

        uint8 pdcm_size : 2;     /* [1:0] */
        uint8 tx_filter_ena : 1; /* [2] */
        uint8 dsi3on_ref : 1;    /* [3] */
        uint8 pdcm_start : 2;    /* [5:4] */
        uint8 crm_start : 2;     /* [7:6] */
    } page1_idx_pdcm_size_t;

    /* PAGE1_IDX_CRC_DSI_CONFIG (0x22) 位定义 */
    struct
    {
        uint8 gain_clamp : 8;     /* [15:8] */
        uint8 crc_dsi_config : 8; /* [7:0] */
    } page1_idx_crc_dsi_config_t;

    /* PAGE1_IDX_BEH_CH (0x24) 位定义 */
    struct
    {
        uint8 beh_ch_h : 2;  /* [9:8] */
        uint8 beh_ch_l : 2;  /* [11:10] */
        uint8 act_mode : 3;  /* [14:12] */
        uint8 diag_mode : 1; /* [15] */

        uint8 reserved1 : 8; /* [7:0] */
    } page1_idx_beh_ch_t;

    /* PAGE1_IDX_OVT_THRESHOLD (0x26) 位定义 */
    struct
    {
        uint8 die_temp : 8;      /* [15:8] */
        uint8 ovt_threshold : 8; /* [7:0] */
    } page1_idx_ovt_threshold_t;

    /* PAGE1_IDX_DOPP_CORR (0x2A) 位定义 */
    struct
    {
        uint8 dopp_rx_outer : 2; /* [9:8] */
        uint8 dopp_tx_inner : 4; /* [13:10] */
        uint8 dopp_rx_inner : 2; /* [15:14] */

        uint8 dopp_tx_side : 2;  /* [1:0] */
        uint8 dopp_rx_side : 2;  /* [3:2] */
        uint8 dopp_tx_outer : 4; /* [7:4] */
    } page1_idx_dopp_corr_t;

    /* PAGE1_IDX_FREQ_TRIM (0x2E) 位定义 */
    struct
    {
        uint8 freq_trim_actual : 8; /* [15:8] */
        uint8 freq_trim_factor : 8; /* [7:0] */
    } page1_idx_freq_trim_t;
} RdumRdusPageIndex_u;

/******************************************************************************
 * @Const Declaration
 *****************************************************************************/

/******************************************************************************
 * @Variable Declaration
 *****************************************************************************/

/******************************************************************************
 * @Function Declaration
 *****************************************************************************/
/* 获取页面索引结构 */
RdumRdusPageIndex_t RdumRdusPageIndex_Get(uint8 Page, uint8 Index);

/* 检查页面索引是否有效 */
uint8 RdumRdusPageIndex_IsValid(RdumRdusPageIndex_t PageIndex);

#endif /* __RDUM_RDUS_PAGE_INDEX_H__ */
