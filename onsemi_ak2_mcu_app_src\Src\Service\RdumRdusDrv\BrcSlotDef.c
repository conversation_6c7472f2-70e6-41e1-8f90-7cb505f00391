/******************************************************************************
 * @file      BrcSlotDef.c
 * @brief     回波slot数据定义实现
 * <AUTHOR>
 * @date      2025-05-16
 * @note      
 *****************************************************************************/

/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include <string.h>
#include "BrcSlotDef.h"
#include "BaseDrv.h"
#include "SpiCmd.h"

/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/



/******************************************************************************
 * @Const Declaration
 *****************************************************************************/
/* 异或表 */
const uint8 XorMasks[MAX_SLOT_COUNT][SLOT_PACKAGE_SIZE] = {
    /* Slot 1 */
    {0x67, 0x3D, 0x66, 0xDD, 0xAE, 0x9D, 0xF8, 0xA1, 0x4E, 0xB0, 0xB0, 0x74, 0x74, 0x27, 0x2B, 0x5A, 0x5B, 0xBB},
    /* Slot 2 */
    {0x86, 0xB2, 0xE6, 0xF9, 0xC6, 0x86, 0xD2, 0xEE, 0xD9, 0xC8, 0x96, 0x5A, 0xEE, 0xBF, 0xC0, 0x34, 0x54, 0x1F},
    /* Slot 3 */
    {0x40, 0x41, 0x30, 0xF4, 0x14, 0x4F, 0x03, 0x74, 0x45, 0x23, 0xFB, 0x55, 0x0B, 0xF3, 0x43, 0x01, 0x71, 0xC4},
    /* Slot 4 */
    {0x93, 0x6C, 0x29, 0x2D, 0xDE, 0xD9, 0xD8, 0x9A, 0x5A, 0xEB, 0xBF, 0x03, 0x34, 0x45, 0x13, 0xFF, 0x45, 0x04},
    /* Slot 5 */
    {0x72, 0x43, 0xA1, 0x79, 0xB4, 0xE2, 0x7B, 0xC5, 0x67, 0x9B, 0x6E, 0x2F, 0xAC, 0x5C, 0x79, 0x31, 0xE2, 0xD8},
    /* Slot 6 */
    {0xD3, 0xCB, 0x11, 0x1B, 0xCC, 0x47, 0x55, 0x7E, 0xF7, 0xA0, 0x0A, 0x70, 0x83, 0x20, 0x29, 0x18, 0xDA, 0x0A},
    /* Slot 7 */
    {0x22, 0xC9, 0x91, 0x9A, 0xAC, 0xEF, 0x7D, 0x00, 0xE5, 0x08, 0x83, 0x06, 0x21, 0x8A, 0xD0, 0xEB, 0x58, 0x0B},
    /* Slot 8 */
    {0xB1, 0x95, 0xBC, 0xA7, 0x7D, 0x36, 0xE1, 0x9A, 0x8C, 0xE7, 0x6D, 0x0A, 0xE5, 0x8F, 0x83, 0x24, 0x29, 0x1B}
};

/* 分频器查找表 */
const uint16 DividerLut[4] = {128, 108, 91, 76};



/******************************************************************************
 * @Variable Declaration
 *****************************************************************************/



/******************************************************************************
 * @Function Definitions
 *****************************************************************************/

/******************************************************************************
 * @brief      解码slot数据
 * @param[in]  SlotIndex slot索引
 * @param[in]  SlotData slot数据
 * @param[out] DecodedData 解码后的数据
 * @return     0: 成功, 非0: 失败
 * <AUTHOR>
 * @date       2025-05-16
 * @note       
 *****************************************************************************/
uint8 BrcSlotDef_DecodeSlot(uint8 SlotIndex, uint8 *SlotData, uint8 *DecodedData)
{
    uint8 i;
    
    /* 参数检查 */
    if (SlotData == NULL || DecodedData == NULL)
    {
        return 1;
    }
    
    /* 检查slot索引 */
    if (SlotIndex >= MAX_SLOT_COUNT)
    {
        return 2;
    }
    
    /* 解码slot数据 */
    for (i = 0; i < SLOT_PACKAGE_SIZE; i++)
    {
        DecodedData[i] = SlotData[i] ^ XorMasks[SlotIndex][i];
    }
    
    /* 验证包CRC */
    if (!BrcSlotDef_VerifySlotCrc(DecodedData, SlotIndex))
    {
        return 3;
    }

    return 0;
}

/******************************************************************************
 * @brief      解压缩单个压缩包
 * @param[in]  GroupData 压缩包数据
 * @param[out] Group 解压缩后的包络点位
 * @return     无
 * <AUTHOR>
 * @date       2025-05-16
 * @note       
 ******************************************************************************/
void BrcSlotDef_DecompressSingleEnvelope(uint32 GroupData, EnvelopeGroup_t *Group)
{
    uint8 i, j, k;
    uint8 Scale;
    uint8 Point;
    uint16 D;
    uint16 S;

    /* 取前5bit获取比例 */
    Scale = (GroupData >> 27) & 0x1F;
    Group->Scale = Scale;

    /* 解压缩9个点位 */
    for (j = 0; j < ENVELOPE_POINTS_PER_GROUP; j++)
    {
        /* 获取3bit点位 */
        Point = (GroupData >> (24 - j * 3)) & 0x07;

        /* 计算点位索引 */
        k = i * ENVELOPE_POINTS_PER_GROUP + j;

        /* 解压缩点位 */
        if (Point != 0)
        {
            /* 获取分频器值 */
            D = DividerLut[Scale & 0x03];

            /* 计算解压缩值 */
            S = Point * D + (D / 2);
            S = S >> (Scale >> 2);

            /* 存储解压缩后的点位 */
            Group->Points[k] = S;
        }
        else
        {
            /* 点位为0 */
            Group->Points[k] = 0;
        }
    }
}

/******************************************************************************
 * @brief      解压缩一个slot的包络点位
 * @param[in]  EnvelopeData 包络数据
 * @param[out] DecompressedEnvelope 解压缩后的包络
 * @return     0: 成功, 非0: 失败
 * <AUTHOR>
 * @date       2025-05-16
 * @note       
 *****************************************************************************/
uint8 BrcSlotDef_DecompressEnvelope(uint8 *EnvelopeData, DecompressedEnvelope_t *DecompressedEnvelope)
{
    uint8 i, j, k;
    uint8 Scale;
    uint8 Point;
    uint16 D;
    uint16 S;
    uint32 GroupData;
    
    /* 参数检查 */
    if (EnvelopeData == NULL || DecompressedEnvelope == NULL)
    {
        return 1;
    }
    
    /* 初始化解压缩后的包络 */
    memset(DecompressedEnvelope, 0, sizeof(DecompressedEnvelope_t));
    
    /* 解压缩包络点位 */
    for (i = 0; i < ENVELOPE_GROUPS; i++)
    {
        /* 获取4字节数据 */
        GroupData = (EnvelopeData[i * 4] << 24) |
                    (EnvelopeData[i * 4 + 1] << 16) |
                    (EnvelopeData[i * 4 + 2] << 8) |
                    (EnvelopeData[i * 4 + 3]);
        
        /* 取前5bit获取比例 */
        Scale = (GroupData >> 27) & 0x1F;
        
        /* 解压缩9个点位 */
        for (j = 0; j < ENVELOPE_POINTS_PER_GROUP; j++)
        {
            /* 获取3bit点位 */
            Point = (GroupData >> (24 - j * 3)) & 0x07;
            
            /* 计算点位索引 */
            k = i * ENVELOPE_POINTS_PER_GROUP + j;
            
            /* 解压缩点位 */
            if (Point != 0)
            {
                /* 获取分频器值 */
                D = DividerLut[Scale & 0x03];
                
                /* 计算解压缩值 */
                S = Point * D + (D / 2);
                S = S >> (Scale >> 2);
                
                /* 存储解压缩后的点位 */
                DecompressedEnvelope->Points[k] = S;
            }
            else
            {
                /* 点位为0 */
                DecompressedEnvelope->Points[k] = 0;
            }
        }
    }
    
    return 0;
}

/******************************************************************************
 * @brief      验证slot CRC
 * @param[in]  SlotData slot数据
 * @param[in]  SlotIndex slot索引
 * @return     0: 失败, 1: 成功
 * <AUTHOR>
 * @date       2025-05-16
 * @note       
 *****************************************************************************/
uint8 BrcSlotDef_VerifySlotCrc(uint8 *SlotData, uint8 SlotIndex)
{
    uint8 CalculatedCrc;
    
    /* 参数检查 */
    if (SlotData == NULL)
    {
        return 0;
    }
    
    /* 计算CRC */
    CalculatedCrc = BaseDrv_Crc8Calculate(SlotData, SLOT_PACKAGE_SIZE - 1, CRC8_C2_INIT_TABLE[SlotIndex]);

    /* 验证CRC */
    return (CalculatedCrc == SlotData[SLOT_PACKAGE_SIZE - 1]);
}
