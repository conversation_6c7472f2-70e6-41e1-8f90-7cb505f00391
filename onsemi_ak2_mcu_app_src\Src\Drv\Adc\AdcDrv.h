/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: AdcDrv.h
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/
#ifndef __ADCDRV_H
#define __ADCDRV_H

/********************************数据类型定义**********************************/


/*******************************全局函数声明***********************************/
extern void ADCDrvInit(void);
extern void ADCDrvClose(void);
extern void ADC0_ScanGroup1_Start(void);
extern void ADC0_ScanGroup1_GetResult(uint16 * const Lpu16Buffer);

#endif
