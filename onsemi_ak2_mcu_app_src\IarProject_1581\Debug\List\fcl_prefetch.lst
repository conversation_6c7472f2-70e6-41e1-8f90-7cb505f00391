###############################################################################
#
# IAR Assembler V2.10.1.1473 for RH850                    30/May/2025  14:07:26
# Copyright 2012-2018 IAR Systems AB.
#
#    Core         =  g3kh
#    Source file  =  
#        D:\zhang9591_project\AK2\tools\onsemi\AnalysisTool\onsemi_ak2_mcu_app_src\Src\Drv\FlashDrv\FCL\lib\fcl_prefetch.s
#    Command line =  
#        -f C:\Users\<USER>\AppData\Local\Temp\EW58F6.tmp
#        (D:\zhang9591_project\AK2\tools\onsemi\AnalysisTool\onsemi_ak2_mcu_app_src\Src\Drv\FlashDrv\FCL\lib\fcl_prefetch.s
#        --core g3kh --code_model normal --data_model medium --fpu single
#        --double=64 -o
#        D:\zhang9591_project\AK2\tools\onsemi\AnalysisTool\onsemi_ak2_mcu_app_src\IarProject_1581\Debug\Obj
#        -M<> -r -ld
#        D:\zhang9591_project\AK2\tools\onsemi\AnalysisTool\onsemi_ak2_mcu_app_src\IarProject_1581\Debug\List\)
#    List file    =  
#        D:\zhang9591_project\AK2\tools\onsemi\AnalysisTool\onsemi_ak2_mcu_app_src\IarProject_1581\Debug\List\fcl_prefetch.lst
#    Object file  =  
#        D:\zhang9591_project\AK2\tools\onsemi\AnalysisTool\onsemi_ak2_mcu_app_src\IarProject_1581\Debug\Obj\fcl_prefetch.o
#
###############################################################################

##############################################################################
# Module fcl_prefetch                                                        #
##############################################################################

     76                        ;
 
 80 bytes in section R_FCL_CODE_RAM_EX_PROT   , module fcl_prefetch
 
 80 bytes of CODE memory  in module fcl_prefetch

Errors: none
Warnings: none
