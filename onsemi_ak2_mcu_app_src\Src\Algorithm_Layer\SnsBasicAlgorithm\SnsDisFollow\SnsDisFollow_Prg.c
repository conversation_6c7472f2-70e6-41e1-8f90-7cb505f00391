/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * SnsDisFollow_Prg: 
 * Created on: 2023-07-01 16:35
 * Original designer: AntonyFang
 ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "SnsDisFollow_Int.h"
#include "SnsRawData_Type.h"
#include "SnsRawData_Int.h"
#include "SnsRawDataCalib.h"
#include "CAN_AppSignalManage.h"
#include "Sns_install_Coordinate.h"
#include "SnsPPCalculate_Int.h"

/******************************************************************************/
/******************************************************************************/
/****************************** Public Definition *****************************/
/******************************************************************************/
/******************************************************************************/
static PDCSnsGroupType GenuSnsGroup;
static PDCSnsChannelType GenuSnsCh;
static SnsFollowWorkType GenuSnsWorkMode;


/******************************************************************************/
/******************************************************************************/
/****************************** Private Function ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/******************************* Public Function ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************
 * 函数名称: SnsSigGroupFollowGetLastIndex
 * 
 * 功能描述: 获取本周期的上一个周期的索引值
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-07-12 08:32   V0.1      AntonyFang   初次发布
 ******************************************************************************/
Sns_CycleType SnsSigGroupFollowGetLastIndex(Sns_CycleType LenuCurIndex)
{
    if(LenuCurIndex)
    {
        return (LenuCurIndex - 1u);
    }
    return SNS_CYCLE_NUM-1;
}



/******************************************************************************
 * 函数名称: SnsSigGroupFollowDataClear
 * 
 * 功能描述: 清零跟踪的数据
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-07-12 08:32   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void SnsSigGroupFollowDataClear(SnsSigGroupDisFollowPointType* LptrFollowPoint)
{
    uint8 i;
    LptrFollowPoint->u8DisFollowFlag = INVALID;
    LptrFollowPoint->u8DisFollowInvalidCnt = 0;
    LptrFollowPoint->enuFollowDisPro = SNS_PROB_NONE;
    LptrFollowPoint->u8FollowUpdateFlag = 0;
    LptrFollowPoint->u8ParallelWallCnt = 0;
    LptrFollowPoint->u8ParallelWallFlag = 0;

    LptrFollowPoint->u8BlindFollowFlag = 0;
    LptrFollowPoint->u8BlindFollowCardir = SNS_CAR_INVALID;
    LptrFollowPoint->u8KeepBeepFlag = 0;

    LptrFollowPoint->u8FollowNoiseCnt = 0;
    LptrFollowPoint->u8FollowNoiseFlag = 0;
    LptrFollowPoint->u8FollowCnt = 0;

    LptrFollowPoint->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
    LptrFollowPoint->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;    

    LptrFollowPoint->u16DisDetect_New = PDC_OBJ_FOLLOW_INVALID_DIS;
    LptrFollowPoint->u16EchoHeightDetect_New = SNS_INVALID_HEIGHT;
    LptrFollowPoint->u16FirstDis_New = PDC_OBJ_FOLLOW_INVALID_DIS;
    LptrFollowPoint->u16FirstHeight_New = SNS_INVALID_HEIGHT;    
    LptrFollowPoint->u16SecondDis_New = PDC_OBJ_FOLLOW_INVALID_DIS;    
    LptrFollowPoint->u16SecondHeight_New = SNS_INVALID_HEIGHT;
    
    LptrFollowPoint->u16StableOutputDis = PDC_OBJ_FOLLOW_INVALID_DIS;
    LptrFollowPoint->fCarMoveLastDetectToNow = 0;
    

    for(i = 0; i < PDC_FOLLOW_ECHO_DIS_BACKUP_NUM; i++)
    {
        LptrFollowPoint->u16StableOutputDisBackup[i] = PDC_OBJ_FOLLOW_INVALID_DIS;
    }
}


/******************************************************************************
 * 函数名称: SnsFollowDisProAdd
 * 
 * 功能描述: 跟踪距离DE CE 置信度累加
 * 
 * 输入参数:*LptrSnsDisProb--置信度变量对应的指针 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-07-15 09:15   V0.1      AntonyFang   初次发布
 ******************************************************************************/
#if VS_SIMULATE_ENABLE
static void SnsFollowDisProAdd(uint8 *LptrSnsDisProb)
{
    uint8 LenuSnsProb;

    LenuSnsProb = *LptrSnsDisProb;
    if(LenuSnsProb < SNS_PROB_VERY_HIGH)
    {
        LenuSnsProb++;
    }
    *LptrSnsDisProb = LenuSnsProb;        
}
#else
static void SnsFollowDisProAdd(SnsProbType *LptrSnsDisProb)
{
    SnsProbType LenuSnsProb;

    LenuSnsProb = *LptrSnsDisProb;
    if(LenuSnsProb < SNS_PROB_VERY_HIGH)
    {
        LenuSnsProb++;
    }
    *LptrSnsDisProb = LenuSnsProb;        
}
#endif




/******************************************************************************
 * 函数名称: SnsFollowDisProSub
 * 
 * 功能描述: 跟踪距离DE CE 置信度递减
 * 
 * 输入参数:*LptrSnsDisProb--置信度变量对应的指针 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-07-15 11:02   V0.1      AntonyFang   初次发布
 ******************************************************************************/
#if VS_SIMULATE_ENABLE
static void SnsFollowDisProSub(uint8 *LptrSnsDisProb,uint16 Lu16Dis,PDCSnsMeasTypeType LenuSnsMeasType)
{
    uint8 LenuSnsProb;

    if(LenuSnsMeasType == PDC_SNS_MEAS_STD)
    {
        if(Lu16Dis < STANDARD_PROB_SUB_DIS)
        {
            LenuSnsProb = *LptrSnsDisProb;
            if(LenuSnsProb > SNS_PROB_NONE)
            {
                LenuSnsProb--;
            }
            *LptrSnsDisProb = LenuSnsProb; 
        }
    }
    else
    {
        if(Lu16Dis > CHIRP_PROB_SUB_DIS)
        {
            LenuSnsProb = *LptrSnsDisProb;
            if(LenuSnsProb > SNS_PROB_NONE)
            {
                LenuSnsProb--;
            }
            *LptrSnsDisProb = LenuSnsProb; 
        }
    } 
}
#else
static void SnsFollowDisProSub(SnsProbType *LptrSnsDisProb,uint16 Lu16Dis,PDCSnsMeasTypeType LenuSnsMeasType)
{
    SnsProbType LenuSnsProb;

    if(LenuSnsMeasType == PDC_SNS_MEAS_STD)
    {
        if(Lu16Dis < STANDARD_PROB_SUB_DIS)
        {
            LenuSnsProb = *LptrSnsDisProb;
            if(LenuSnsProb > SNS_PROB_NONE)
            {
                LenuSnsProb--;
            }
            *LptrSnsDisProb = LenuSnsProb; 
        }
    }
    else
    {
        if(Lu16Dis > CHIRP_PROB_SUB_DIS)
        {
            LenuSnsProb = *LptrSnsDisProb;
            if(LenuSnsProb > SNS_PROB_NONE)
            {
                LenuSnsProb--;
            }
            *LptrSnsDisProb = LenuSnsProb; 
        }
    } 
}

#endif


/******************************************************************************
 * 函数名称: SnsSigGroupChannelDataClear
 * 
 * 功能描述: 某一个通道跟踪距离清零
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-07-12 08:33   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupChannelDataClear(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh)
{
    SnsSigGroupDisFollowType* LptrPSnsSigGroupDisFollow;
    LptrPSnsSigGroupDisFollow = &GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh];
    
    SnsSigGroupFollowDataClear(&LptrPSnsSigGroupDisFollow->MasDisData);
    SnsSigGroupFollowDataClear(&LptrPSnsSigGroupDisFollow->LeftLisDisData);
    SnsSigGroupFollowDataClear(&LptrPSnsSigGroupDisFollow->RightLisDisData);
    LptrPSnsSigGroupDisFollow->eMeasType = PDC_SNS_MEAS_IDLE;
    LptrPSnsSigGroupDisFollow->eFolllowObjType = FOLLOW_OBJ_NONE_TYPE;
    LptrPSnsSigGroupDisFollow->u8ObjParallelWallFlag = 0;
    LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbFlag = 0;
    LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbValidCnt = 0;
    LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbInvalidCnt = 0;
    LptrPSnsSigGroupDisFollow->LeftStandCurb.u16StandCurbMasterDis = PDC_OBJ_FOLLOW_INVALID_DIS;
    
    LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbFlag = 0;
    LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbValidCnt = 0;
    LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbInvalidCnt = 0;
    LptrPSnsSigGroupDisFollow->RightStandCurb.u16StandCurbMasterDis = PDC_OBJ_FOLLOW_INVALID_DIS;
}


/******************************************************************************
 * 函数名称: PDCFollowEchoDataInit
 * 
 * 功能描述: 信号组距离跟踪数据初始化
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-07-01 16:54   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupFollowDataInit(PDCSnsGroupType LeGroup)
{
    PDCSnsChannelType LenuSnsCh;
    for(LenuSnsCh=PDC_SNS_CH_FLS_RLS; LenuSnsCh<PDC_SNS_CH_NUM;LenuSnsCh++)
    {
        SnsSigGroupChannelDataClear(LeGroup,LenuSnsCh);
    }
}


/******************************************************************************
 * 函数名称: SnsSigGroupFollowDataPDC_Sns_Init
 * 
 * 功能描述: PDC探头跟踪数据初始化
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-07 10:50   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupFollowDataPDC_Sns_Init(PDCSnsGroupType LeGroup)
{
    PDCSnsChannelType LenuSnsCh;
    for(LenuSnsCh=PDC_SNS_CH_FL_RL; LenuSnsCh<PDC_SNS_CH_FRS_RRS;LenuSnsCh++)
    {
        SnsSigGroupChannelDataClear(LeGroup,LenuSnsCh);
    }
}


/******************************************************************************
 * 函数名称: SnsSigGroupFollowDataPowerOnInit
 * 
 * 功能描述: 信号组距离跟踪数据上电初始化
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-07-12 08:38   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupFollowDataPowerOnInit(void)
{
#if 1
    SnsSigGroupFollowDataInit(PDC_SNS_GROUP_FRONT);
    SnsSigGroupFollowDataInit(PDC_SNS_GROUP_REAR);
#endif
}

/******************************************************************************
 * 函数名称: SnsSigGroupSideEchoDisLimit
 * 
 * 功能描述: 限制侧边探头距离变化
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-07-13 21:11   V0.1      AntonyFang   初次发布
 ******************************************************************************/
uint16 SnsSigGroupSideEchoDisLimit(sint16 Ls16CarMoveDis)
{
    /** @brief: 以侧雷达扫描周期为80ms计算,1km/h时速下每个周期车辆运动22mm */
    uint16 Lu16CarMoveDis = (uint16)ABS_VALUE(Ls16CarMoveDis);

    if(Lu16CarMoveDis < 2*PDC_SIDE_SNS_MOVEDIS_IN_1KM_H_SPEED)
    {
        /** @brief: 三公里时速内，障碍物移动距离 */
        return (uint16)70;
    }
    else if(Lu16CarMoveDis < 4*PDC_SIDE_SNS_MOVEDIS_IN_1KM_H_SPEED)
    {
        return (uint16)100;
    }
    else if(Lu16CarMoveDis < 6*PDC_SIDE_SNS_MOVEDIS_IN_1KM_H_SPEED)
    {
        return (uint16)150;
    }
    else if(Lu16CarMoveDis < 8*PDC_SIDE_SNS_MOVEDIS_IN_1KM_H_SPEED)
    {
        return (uint16)200;
    }
    else if(Lu16CarMoveDis < 10*PDC_SIDE_SNS_MOVEDIS_IN_1KM_H_SPEED)
    {
        return (uint16)250;
    }
    else
    {
        return (uint16)300;
    }
}

/************************************重构障碍物跟踪的逻辑代码--按照中间、边角、侧边分开处理 2023-09-12 ***************************************************/

/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForMiddleMasterStopSearch
 * 
 * 功能描述: 中间探头车辆停止时主发距离的匹配查询处理
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应主发探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-13 08:37   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForMiddleCornerMasterStopSearch(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SysDisInfoType *LpstrSysDisInfo;
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    Sns_CycleType LenuCurIndex,LenuLastIndex,LenuLastBefIndex;
    sint16 Ls16ObjDisSub1_2,Ls16ObjDisSub1_3;
    uint8 Lu8SigGroupCnt1,Lu8SigGroupCnt2,Lu8SigGroupCnt3;
    uint8 Lu8ObjValidFlag = 0;
    uint8 Lu8ObjValidCheckFlag = 0;
    LpstrSysDisInfo = &GstrSysDisInfo[LeGroup][LenuSnsCh];
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    LenuLastIndex = SnsSigGroupFollowGetLastIndex(LenuCurIndex);
    LenuLastBefIndex = SnsSigGroupFollowGetLastIndex(LenuLastIndex);

    
    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;

    for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
    {
        Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
        /* 先用本轮和上一轮进行差值比较 */
        for(Lu8SigGroupCnt2 = 0; Lu8SigGroupCnt2 < LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u8SigGroupCnt; Lu8SigGroupCnt2++)
        {
            /** 比较本轮和上一轮的差值 */
            if((LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]!=SNS_INVALID_DIS))
            {
                Ls16ObjDisSub1_2 = ABS(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]);
                if(Ls16ObjDisSub1_2 < FOLLOW_STOP_VALID_DIS_SUB)
                {
                    break;
                }
                else
                {
                    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
                }
            }
        }
    
        /* 本轮数据和上上一轮数据做比较 */
        for(Lu8SigGroupCnt3 = 0; Lu8SigGroupCnt3 < LpstrSnsSigGroupDataCache->MasterBuf[LenuLastBefIndex].u8SigGroupCnt; Lu8SigGroupCnt3++)
        {
            if((LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->MasterBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]!=SNS_INVALID_DIS))
            {
                Ls16ObjDisSub1_3 = ABS(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->MasterBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]);
                if(Ls16ObjDisSub1_3 < FOLLOW_STOP_VALID_DIS_SUB)
                {
                    break;
                }
                else
                {
                    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
                }
            }
        }
        
        if((Ls16ObjDisSub1_2 != FOLLOW_DIS_SUB_INVALID)&&(Ls16ObjDisSub1_3 != FOLLOW_DIS_SUB_INVALID))
        {
            if((Ls16ObjDisSub1_2 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)&&(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB))
            {
                Lu8ObjValidFlag = VALID;
                if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                }
                else
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                }
                break;
            }
            else
            {
                Lu8ObjValidFlag = VALID;
                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                break;
            }
        }
        else if(Ls16ObjDisSub1_3 != FOLLOW_DIS_SUB_INVALID)
        {
            /* 只有本轮和上上轮匹配时，需要分探头探测距离和定扫频进行区分 */
            if(LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType > PDC_SNS_MEAS_STD)
            {
                if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] > FOLLOW_CHIRP_FAST_CONFIRM_DIS)
                {
                    if(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                    {
                        if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                    }
                    Lu8ObjValidFlag = VALID;
                    break;
                }
            }
            else
            {
                /* 做定扫确认过滤噪点 */
                if ((LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] > FOLLOW_STOP_STAND_CHIP_COMFIRM_STAR_DISTANCE) && 
                    (LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_STOP_STAND_CHIP_COMFIRM_END_DISTANCE))
                {
                    if(Ls16ObjDisSub1_2 > FOLLOW_STOP_VALID_DIS_SUB)
                    {
                        break;
                    }
                }
                
                if (LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] <= FOLLOW_STANDARD_FAST_CONFIRM_DIS)
                {
                    if(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                    {
                        if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                    }
                    if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_HIGH_CONFIRDENCE_DIS)
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_VERY_HIGH;
                    }

                    /* 50cm内采用定扫结合 */
                    if (LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] <= FOLLOW_STANDARD_SLOW_COMFIRM_DIS)
                    {
                        if(Ls16ObjDisSub1_2 > FOLLOW_STOP_STAND_CHIP_SUB)
                        {
                            break;
                        }
                    }
                    
                    Lu8ObjValidFlag = VALID;
                    break;
                }
            }
        }
        else if(Ls16ObjDisSub1_2 != FOLLOW_DIS_SUB_INVALID)
        {
            /* 对于中间雷达，车辆静止时，首次上电，快速确认障碍物，才采用此策略 */
            if(LpstrSnsSigGroupDataCache->u8RecordCnt == 2)
            {
                /* 只有本轮和上上轮匹配时，需要分探头探测距离和定扫频进行区分 */
                if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_STOP_POWER_ON_FAST_CONFIRM_DIS)
                {
                    if(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                    {
                        if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                    }
                    Lu8ObjValidFlag = VALID;
                    break;
                }
            }
            else
            {
                if (LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < SEARCH_FAST_COMFIRM_DIS)
                {
                    Lu8ObjValidFlag = VALID;
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                    break;
                }
                
            }
        }
    }
    if(Lu8ObjValidFlag)
    {
        if(!LpstrSnsSigGroupDisFollow->u8DisFollowFlag)
        {
            Lu8ObjValidCheckFlag = 1;
        }
        else
        {
            /* 已经跟踪上的，再后续的比较中，只考虑近距离快速进入的，远离的等已经跟踪上的障碍物消失后再重新匹配 */
            if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < LpstrSnsSigGroupDisFollow->u16FollowDis)
            {
                Lu8ObjValidCheckFlag = 1;
            }
        }
        if (LpstrSysDisInfo->u8BlindKeepFlag)
        {
            Lu8ObjValidCheckFlag = 0;
        }

        if(Lu8ObjValidCheckFlag)
        {
            LpstrSnsSigGroupDisFollow->u8DisFollowFlag = VALID;
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
            LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
            
            LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
            LpstrSnsSigGroupDisFollow->u8KeepBeepFlag = 0;
            LpstrSnsSigGroupDisFollow->u8BlindFollowCardir = SNS_CAR_INVALID;
        }
    }
}



/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForMiddleMasterStopFollow
 * 
 * 功能描述: 中间探头的主发距离跟踪
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应主发探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-13 08:40   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForMiddleCornerMasterStopFollow(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    Sns_CycleType LenuCurIndex,LenuLastIndex,LenuLastBefIndex;
    sint16 Ls16ObjDisSub1_2,Ls16ObjDisSub1_3;
    sint16 Ls16ObjDisSub1_Follow,Ls16ObjDisSub2_Follow,Ls16ObjDisSub3_Follow;
    sint16 Ls16ObjLastNewSub;
    uint8 Lu8SigGroupCnt1,Lu8SigGroupCnt2,Lu8SigGroupCnt3;
    uint8 Lu8ObjValidFlag = 0;
    uint8 Lu8ObjValidCheckFlag = 0;
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    LenuLastIndex = SnsSigGroupFollowGetLastIndex(LenuCurIndex);
    LenuLastBefIndex = SnsSigGroupFollowGetLastIndex(LenuLastIndex);

    
    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_Follow = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub2_Follow = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub3_Follow = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjLastNewSub = FOLLOW_DIS_SUB_INVALID;

    if(LpstrSnsSigGroupDisFollow->u16FollowDis < CHIRP_BLIND_DIS)
    {
        if(LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType > PDC_SNS_MEAS_STD)
        {
            return;/* 扫频近距离数据不用，只使用定频进行更新 */
        }
    }
    else if(LpstrSnsSigGroupDisFollow->u16FollowDis > STANDARD_BLIND_DIS)
    {
        if((LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType == PDC_SNS_MEAS_STD)&&(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[0]==SNS_INVALID_DIS))
        {
            return;/* 扫频跟踪远距离数据不用，只使用扫频进行更新 */
        }
    }

    if(LpstrSnsSigGroupDisFollow->u16FollowDis == PDC_OBJ_FOLLOW_INVALID_DIS)
    {
        LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
        LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
        LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
        LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
        LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
        LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
        LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 0;
    }
    else
    {
        /* 10轮为一个周期 */
        LpstrSnsSigGroupDisFollow->u8FollowCnt++;
        if (LpstrSnsSigGroupDisFollow->u8FollowCnt == FOLLOW_PERIOD)
        {
            LpstrSnsSigGroupDisFollow->u8FollowCnt = 0;
            LpstrSnsSigGroupDisFollow->u8FollowNoiseCnt = 0;
            LpstrSnsSigGroupDisFollow->u8FollowNoiseFlag = 0;
        }

        for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
        {
            Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub1_Follow = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub2_Follow = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub3_Follow = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjLastNewSub = FOLLOW_DIS_SUB_INVALID;
            /* 先用本轮和已经跟踪上的距离进行差值比较 */
            if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)
            {
                Ls16ObjDisSub1_Follow = ABS(LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]);
            }
            else
            {
                Ls16ObjDisSub1_Follow = FOLLOW_DIS_SUB_INVALID;
            }
            if(Ls16ObjDisSub1_Follow < FOLLOW_STOP_VALID_DIS_SUB)
            {
                LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                if(Ls16ObjDisSub1_Follow < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                {
                    /** @brief: 更新实时障碍物数据 */
                    LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                    
                    LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                    SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                    break;
                }
                else
                {
                    for(Lu8SigGroupCnt2 = 0; Lu8SigGroupCnt2 < LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u8SigGroupCnt; Lu8SigGroupCnt2++)
                    {
                        if(LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]!=SNS_INVALID_DIS)
                        {
                            Ls16ObjDisSub2_Follow = ABS(LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]);
                        }
                        else
                        {
                            Ls16ObjDisSub2_Follow = FOLLOW_DIS_SUB_INVALID;
                        }
                        if(Ls16ObjDisSub2_Follow < FOLLOW_STOP_VALID_DIS_SUB)
                        {
                            Ls16ObjDisSub1_2 = ABS(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]);
                            break;
                        }
                    }
                    if(Ls16ObjDisSub1_2 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                    {
                        /** @brief: 更新实时障碍物数据 */
                        LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                        
                        LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                        SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                        break;
                    }
                    else
                    {
                        for(Lu8SigGroupCnt3 = 0; Lu8SigGroupCnt3 < LpstrSnsSigGroupDataCache->MasterBuf[LenuLastBefIndex].u8SigGroupCnt; Lu8SigGroupCnt3++)
                        {
                            if(LpstrSnsSigGroupDataCache->MasterBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]!=SNS_INVALID_DIS)
                            {
                                Ls16ObjDisSub3_Follow = ABS(LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->MasterBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]);
                            }
                            else
                            {
                                Ls16ObjDisSub3_Follow = FOLLOW_DIS_SUB_INVALID;
                            }
                            if(Ls16ObjDisSub3_Follow < FOLLOW_STOP_VALID_DIS_SUB)
                            {
                                Ls16ObjDisSub1_3 = ABS(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->MasterBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]);
                                break;
                            }
                        }
                        if((Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)&&(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_STOP_1_3_CONFIRM_DIS))
                        {
                            /** @brief: 更新实时障碍物数据 */
                            LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                            
                            LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                            SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                            break;
                        }
                    }
                    break;/* 距离突变5cm的，在过去周期中匹配不到的不更新，同时置信度维持不变 */
                }
            }
            else 
            {     
                if((LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDisFollow->u16DisDetect_Last!=SNS_INVALID_DIS))
                {
                    Ls16ObjLastNewSub = ABS(LpstrSnsSigGroupDisFollow->u16DisDetect_Last,LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]);
                }
                else
                {
                    Ls16ObjLastNewSub = FOLLOW_DIS_SUB_INVALID;
                }
                if(Ls16ObjLastNewSub < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                {
                    /** @brief: 障碍物未移动 */
                    LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                    LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                    
                    LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                    SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);

                    break;
                }
#if 0
                /* 遍历完成 信号组中没有与跟踪距离相匹配的距离认为是噪声 */
                if (Lu8SigGroupCnt1 == LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8SigGroupCnt)
                {
                    LpstrSnsSigGroupDisFollow->u8FollowNoiseCnt++;
                    if (LpstrSnsSigGroupDisFollow->u8FollowNoiseCnt > FOLLOW_STOP_STAND_CHIP_NOISE_CNT)
                    {
                        LpstrSnsSigGroupDisFollow->u8FollowNoiseFlag = 1;
                    }

                    if (LpstrSnsSigGroupDisFollow->u8FollowNoiseFlag)
                    {
                        LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
                        LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                        LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
                        LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
                        LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                        LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
                        LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 0;
                        LpstrSnsSigGroupDisFollow->u8FollowNoiseCnt = 0;
                        LpstrSnsSigGroupDisFollow->u8FollowNoiseFlag = 0;
                    }
                }
#endif
            }
        }
        /* 遍历完成，也没有查到有效数据，需要维持跟踪距离，同时置信度递减，跟踪失效技术累加 */
        if(Lu8SigGroupCnt1 == LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8SigGroupCnt)
        {
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt++;
            if(LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt > FOLLOW_STOP_EXIT_CNT)
            {
                /* 强制跟踪 */
                if (LpstrSnsSigGroupDisFollow->u8BlindFollowFlag)
                {
                    /* code */
                }
                else
                {
                    /** @brief: 退出跟踪 */
                    LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
                    LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                    LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
                    LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
                    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                    LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
                    LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 0;
                    LpstrSnsSigGroupDisFollow->u8FollowNoiseCnt = 0;
                    LpstrSnsSigGroupDisFollow->u8FollowNoiseFlag = 0;
                }
            }
            SnsFollowDisProSub(&LpstrSnsSigGroupDisFollow->enuFollowDisPro,LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType);
            /* 查询是否有近距离障碍物出现 */
            if(Lu8SigGroupCnt1 != 0)
            {
                SnsSigGroupDisFollowForMiddleCornerMasterStopSearch(LeGroup,LenuSnsCh,LpstrSnsSigGroupDisFollow);
            }
        }
        else if(Lu8SigGroupCnt1 > 0)
        {
            //SnsSigGroupCarStopObjSearch(LpstrSysData,LpstrSigGroupData,LpstrSnsSigGroupDisFollow,LenuCurIndex);
        }
    }
}


/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForMiddleLeftStopSearch
 * 
 * 功能描述: 中间探头车辆停止时左侦听距离的匹配查询处理
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应主发探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-13 10:07   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForMiddleCornerLeftStopSearch(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SysDisInfoType *LpstrSysDisInfo;
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;

    Sns_CycleType LenuCurIndex,LenuLastIndex,LenuLastBefIndex;
    sint16 Ls16ObjDisSub1_2,Ls16ObjDisSub1_3;
    uint8 Lu8SigGroupCnt1,Lu8SigGroupCnt2,Lu8SigGroupCnt3;
    uint8 Lu8ObjValidFlag = 0;
    uint8 Lu8ObjValidCheckFlag = 0;
    LpstrSysDisInfo = &GstrSysDisInfo[LeGroup][LenuSnsCh];
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    LenuLastIndex = SnsSigGroupFollowGetLastIndex(LenuCurIndex);
    LenuLastBefIndex = SnsSigGroupFollowGetLastIndex(LenuLastIndex);

    
    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;

    for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
    {
        Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
        /* 先用本轮和上一轮进行差值比较 */
        for(Lu8SigGroupCnt2 = 0; Lu8SigGroupCnt2 < LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u8SigGroupCnt; Lu8SigGroupCnt2++)
        {
            /** 比较本轮和上一轮的差值 */
            if((LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]!=SNS_INVALID_DIS))
            {
                Ls16ObjDisSub1_2 = ABS(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]);
                if(Ls16ObjDisSub1_2 < FOLLOW_STOP_VALID_DIS_SUB)
                {
                    break;
                }
                else
                {
                    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
                }
            }
        }
    
        /* 本轮数据和上上一轮数据做比较 */
        for(Lu8SigGroupCnt3 = 0; Lu8SigGroupCnt3 < LpstrSnsSigGroupDataCache->LeftBuf[LenuLastBefIndex].u8SigGroupCnt; Lu8SigGroupCnt3++)
        {
            if((LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->LeftBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]!=SNS_INVALID_DIS))
            {
                Ls16ObjDisSub1_3 = ABS(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->LeftBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]);
                if(Ls16ObjDisSub1_3 < FOLLOW_STOP_VALID_DIS_SUB)
                {
                    break;
                }
                else
                {
                    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
                }
            }
        }
        
        if((Ls16ObjDisSub1_2 != FOLLOW_DIS_SUB_INVALID)&&(Ls16ObjDisSub1_3 != FOLLOW_DIS_SUB_INVALID))
        {
            if((Ls16ObjDisSub1_2 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)&&(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB))
            {
                Lu8ObjValidFlag = VALID;
                if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                }
                else
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                }
                break;
            }
            else
            {
                Lu8ObjValidFlag = VALID;
                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                break;
            }
        }
        else if(Ls16ObjDisSub1_3 != FOLLOW_DIS_SUB_INVALID)
        {
            /* 只有本轮和上上轮匹配时，需要分探头探测距离和定扫频进行区分 */
            if(LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType > PDC_SNS_MEAS_STD)
            {
                if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] > FOLLOW_CHIRP_FAST_CONFIRM_DIS)
                {
                    if(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                    {
                        if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                    }
                    Lu8ObjValidFlag = VALID;
                    break;
                }
            }
            else
            {
                /* 做定扫确认过滤噪点 */
                if ((LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] > FOLLOW_STOP_STAND_CHIP_COMFIRM_STAR_DISTANCE) && 
                    (LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_STOP_STAND_CHIP_COMFIRM_END_DISTANCE))
                {
                    if(Ls16ObjDisSub1_2 > FOLLOW_STOP_STAND_CHIP_SUB)
                    {
                        break;
                    }
                }

                if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_STANDARD_FAST_CONFIRM_DIS)
                {
                    if(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                    {
                        if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                    }
                    if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_HIGH_CONFIRDENCE_DIS)
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_VERY_HIGH;
                    }

                    /* 50cm内采用定扫结合 */
                    if (LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] <= FOLLOW_STANDARD_SLOW_COMFIRM_DIS)
                    {
                        if(Ls16ObjDisSub1_2 > FOLLOW_STOP_STAND_CHIP_SUB)
                        {
                            break;
                        }
                    }
                    
                    Lu8ObjValidFlag = VALID;
                    break;
                }
            }
        }
        else if(Ls16ObjDisSub1_2 != FOLLOW_DIS_SUB_INVALID)
        {
            /* 对于中间雷达，车辆静止时，首次上电，快速确认障碍物，才采用此策略 */
            if(LpstrSnsSigGroupDataCache->u8RecordCnt == 2)
            {
                /* 只有本轮和上上轮匹配时，需要分探头探测距离和定扫频进行区分 */
                if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_STOP_POWER_ON_FAST_CONFIRM_DIS)
                {
                    if(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                    {
                        if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                    }
                    Lu8ObjValidFlag = VALID;
                    break;
                }
            }
        }
    }
    if(Lu8ObjValidFlag)
    {
        if(!LpstrSnsSigGroupDisFollow->u8DisFollowFlag)
        {
            Lu8ObjValidCheckFlag = 1;
        }
        else
        {
            /* 已经跟踪上的，再后续的比较中，只考虑近距离快速进入的，远离的等已经跟踪上的障碍物消失后再重新匹配 */
            if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < LpstrSnsSigGroupDisFollow->u16FollowDis)
            {
                Lu8ObjValidCheckFlag = 1;
            }
        }
        if (LpstrSysDisInfo->u8BlindKeepFlag)
        {
            Lu8ObjValidCheckFlag = 0;
        }

        if(Lu8ObjValidCheckFlag)
        {
            LpstrSnsSigGroupDisFollow->u8DisFollowFlag = VALID;
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
            LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
            
            LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
            LpstrSnsSigGroupDisFollow->u8KeepBeepFlag = 0;
            LpstrSnsSigGroupDisFollow->u8BlindFollowCardir = SNS_CAR_INVALID;
        }
    }
#if 0
    if((GenuSnsGroup == 0x00)&&(GenuSnsCh == 0x02))
    {
        printf("FML Left Search,Time:%.3f,FollowDis:%d,Flag:%d,NewDis:%d,LastDis:%d,LastBefDis:%d,Sub1_2:%d,Sub1_3:%d\r\n",GfMessageTime,\
            LpstrSnsSigGroupDisFollow->u16FollowDis,Lu8ObjValidCheckFlag,\
            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[0],LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u16ActualDis[0],\
            LpstrSnsSigGroupDataCache->LeftBuf[LenuLastBefIndex].u16ActualDis[0],\
            Ls16ObjDisSub1_2,Ls16ObjDisSub1_3);
    }
#endif
}


/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForMiddleLeftStopFollow
 * 
 * 功能描述: 中间探头的左侦听距离跟踪
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应主发探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-13 10:10   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForMiddleCornerLeftStopFollow(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    Sns_CycleType LenuCurIndex,LenuLastIndex,LenuLastBefIndex;
    sint16 Ls16ObjDisSub1_2,Ls16ObjDisSub1_3;
    sint16 Ls16ObjDisSub1_Follow,Ls16ObjDisSub2_Follow,Ls16ObjDisSub3_Follow;
    sint16 Ls16ObjLastNewSub;
    uint8 Lu8SigGroupCnt1,Lu8SigGroupCnt2,Lu8SigGroupCnt3;
    uint8 Lu8ObjValidFlag = 0;
    uint8 Lu8ObjValidCheckFlag = 0;
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    LenuLastIndex = SnsSigGroupFollowGetLastIndex(LenuCurIndex);
    LenuLastBefIndex = SnsSigGroupFollowGetLastIndex(LenuLastIndex);

    
    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_Follow = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub2_Follow = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub3_Follow = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjLastNewSub = FOLLOW_DIS_SUB_INVALID;

    if(LpstrSnsSigGroupDisFollow->u16FollowDis < CHIRP_BLIND_DIS)
    {
        if(LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType > PDC_SNS_MEAS_STD)
        {
            return;/* 扫频近距离数据不用，只使用定频进行更新 */
        }
    }
    else if(LpstrSnsSigGroupDisFollow->u16FollowDis > STANDARD_BLIND_DIS)
    {
        if((LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType == PDC_SNS_MEAS_STD)&&(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[0]==SNS_INVALID_DIS))
        {
            return;/* 扫频跟踪远距离数据不用，只使用扫频进行更新 */
        }
    }
    
    if(LpstrSnsSigGroupDisFollow->u16FollowDis == PDC_OBJ_FOLLOW_INVALID_DIS)
    {
        LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
        LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
        LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
        LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
        LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
        LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
        LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 0;
    }
    else
    {
        /* 10轮为一个周期 */
        LpstrSnsSigGroupDisFollow->u8FollowCnt++;
        if (LpstrSnsSigGroupDisFollow->u8FollowCnt == FOLLOW_PERIOD)
        {
            LpstrSnsSigGroupDisFollow->u8FollowCnt = 0;
            LpstrSnsSigGroupDisFollow->u8FollowNoiseCnt = 0;
            LpstrSnsSigGroupDisFollow->u8FollowNoiseFlag = 0;
        }

        for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
        {
            Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub1_Follow = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub2_Follow = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub3_Follow = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjLastNewSub = FOLLOW_DIS_SUB_INVALID;
            /* 先用本轮和已经跟踪上的距离进行差值比较 */
            if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)
            {
                Ls16ObjDisSub1_Follow = ABS(LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]);
            }
            else
            {
                Ls16ObjDisSub1_Follow = FOLLOW_DIS_SUB_INVALID;
            }
            if(Ls16ObjDisSub1_Follow < FOLLOW_STOP_VALID_DIS_SUB)
            {
                LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                if(Ls16ObjDisSub1_Follow < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                {
                    /** @brief: 更新实时障碍物数据 */
                    LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                    
                    LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                    SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                    break;
                }
                else
                {
                    for(Lu8SigGroupCnt2 = 0; Lu8SigGroupCnt2 < LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u8SigGroupCnt; Lu8SigGroupCnt2++)
                    {
                        if(LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]!=SNS_INVALID_DIS)
                        {
                            Ls16ObjDisSub2_Follow = ABS(LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]);
                        }
                        else
                        {
                            Ls16ObjDisSub2_Follow = FOLLOW_DIS_SUB_INVALID;
                        }
                        if(Ls16ObjDisSub2_Follow < FOLLOW_STOP_VALID_DIS_SUB)
                        {
                            Ls16ObjDisSub1_2 = ABS(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]);
                            break;
                        }
                    }
                    if(Ls16ObjDisSub1_2 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                    {
                        /** @brief: 更新实时障碍物数据 */
                        LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                        
                        LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                        SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                        break;
                    }
                    else
                    {
                        for(Lu8SigGroupCnt3 = 0; Lu8SigGroupCnt3 < LpstrSnsSigGroupDataCache->LeftBuf[LenuLastBefIndex].u8SigGroupCnt; Lu8SigGroupCnt3++)
                        {
                            if(LpstrSnsSigGroupDataCache->LeftBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]!=SNS_INVALID_DIS)
                            {
                                Ls16ObjDisSub3_Follow = ABS(LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->LeftBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]);
                            }
                            else
                            {
                                Ls16ObjDisSub3_Follow = FOLLOW_DIS_SUB_INVALID;
                            }
                            if(Ls16ObjDisSub3_Follow < FOLLOW_STOP_VALID_DIS_SUB)
                            {
                                Ls16ObjDisSub1_3 = ABS(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->LeftBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]);
                                break;
                            }
                        }
                        if((Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)&&(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_STOP_1_3_CONFIRM_DIS))
                        {
                            /** @brief: 更新实时障碍物数据 */
                            LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                            
                            LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                            SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                            break;
                        }
                    }
                    break;/* 距离突变5cm的，在过去周期中匹配不到的不更新，同时置信度维持不变 */
                }
            }
            else 
            {     
                if((LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDisFollow->u16DisDetect_Last!=SNS_INVALID_DIS))
                {
                    Ls16ObjLastNewSub = ABS(LpstrSnsSigGroupDisFollow->u16DisDetect_Last,LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]);
                }
                else
                {
                    Ls16ObjLastNewSub = FOLLOW_DIS_SUB_INVALID;
                }
                if(Ls16ObjLastNewSub < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                {
                    /** @brief: 障碍物未移动 */
                    LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                    LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                    
                    LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                    SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);

                    break;
                }
#if 0
                /* 遍历完成 信号组中没有与跟踪距离相匹配的距离认为是噪声 */
                if (Lu8SigGroupCnt1 == LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8SigGroupCnt)
                {
                    LpstrSnsSigGroupDisFollow->u8FollowNoiseCnt++;
                    if (LpstrSnsSigGroupDisFollow->u8FollowNoiseCnt > FOLLOW_STOP_STAND_CHIP_NOISE_CNT)
                    {
                        LpstrSnsSigGroupDisFollow->u8FollowNoiseFlag = 1;
                    }

                    if (LpstrSnsSigGroupDisFollow->u8FollowNoiseFlag)
                    {
                        LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
                        LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                        LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
                        LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
                        LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                        LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
                        LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 0;
                        LpstrSnsSigGroupDisFollow->u8FollowNoiseCnt = 0;
                        LpstrSnsSigGroupDisFollow->u8FollowNoiseFlag = 0;
                    }
                }
#endif
            }
        }
        /* 遍历完成，也没有查到有效数据，需要维持跟踪距离，同时置信度递减，跟踪失效技术累加 */
        if(Lu8SigGroupCnt1 == LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8SigGroupCnt)
        {
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt++;
            if(LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt > FOLLOW_STOP_EXIT_CNT)
            {
                /* 强制跟踪 */
                if (LpstrSnsSigGroupDisFollow->u8BlindFollowFlag)
                {
                    /* code */
                }
                else
                {
                    /** @brief: 退出跟踪 */
                    LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
                    LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                    LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
                    LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
                    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                    LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
                    LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 0;
                    LpstrSnsSigGroupDisFollow->u8FollowNoiseCnt = 0;
                    LpstrSnsSigGroupDisFollow->u8FollowNoiseFlag = 0;
                }
            }
            SnsFollowDisProSub(&LpstrSnsSigGroupDisFollow->enuFollowDisPro,LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType);
            /* 查询是否有近距离障碍物出现 */
            if(Lu8SigGroupCnt1 != 0)
            {
                SnsSigGroupDisFollowForMiddleCornerLeftStopSearch(LeGroup,LenuSnsCh,LpstrSnsSigGroupDisFollow);
            }
        }
        else if(Lu8SigGroupCnt1 > 0)
        {
            //SnsSigGroupCarStopObjSearch(LpstrSysData,LpstrSigGroupData,LpstrSnsSigGroupDisFollow,LenuCurIndex);
        }
    }
}


/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForMiddleRightStopSearch
 * 
 * 功能描述: 中间探头车辆停止时右侦听距离的匹配查询处理
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应右侦听探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-13 10:07   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForMiddleCornerRightStopSearch(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SysDisInfoType *LpstrSysDisInfo;
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    Sns_CycleType LenuCurIndex,LenuLastIndex,LenuLastBefIndex;
    sint16 Ls16ObjDisSub1_2,Ls16ObjDisSub1_3;
    uint8 Lu8SigGroupCnt1,Lu8SigGroupCnt2,Lu8SigGroupCnt3;
    uint8 Lu8ObjValidFlag = 0;
    uint8 Lu8ObjValidCheckFlag = 0;
    LpstrSysDisInfo = &GstrSysDisInfo[LeGroup][LenuSnsCh];
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    LenuLastIndex = SnsSigGroupFollowGetLastIndex(LenuCurIndex);
    LenuLastBefIndex = SnsSigGroupFollowGetLastIndex(LenuLastIndex);

    
    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;

    for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
    {
        Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
        /* 先用本轮和上一轮进行差值比较 */
        for(Lu8SigGroupCnt2 = 0; Lu8SigGroupCnt2 < LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u8SigGroupCnt; Lu8SigGroupCnt2++)
        {
            /** 比较本轮和上一轮的差值 */
            if((LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]!=SNS_INVALID_DIS))
            {
                Ls16ObjDisSub1_2 = ABS(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]);
                if(Ls16ObjDisSub1_2 < FOLLOW_STOP_VALID_DIS_SUB)
                {
                    break;
                }
                else
                {
                    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
                }
            }
        }
    
        /* 本轮数据和上上一轮数据做比较 */
        for(Lu8SigGroupCnt3 = 0; Lu8SigGroupCnt3 < LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u8SigGroupCnt; Lu8SigGroupCnt3++)
        {
            if((LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]!=SNS_INVALID_DIS))
            {
                Ls16ObjDisSub1_3 = ABS(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]);
                if(Ls16ObjDisSub1_3 < FOLLOW_STOP_VALID_DIS_SUB)
                {
                    break;
                }
                else
                {
                    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
                }
            }
        }
        
        if((Ls16ObjDisSub1_2 != FOLLOW_DIS_SUB_INVALID)&&(Ls16ObjDisSub1_3 != FOLLOW_DIS_SUB_INVALID))
        {
            if((Ls16ObjDisSub1_2 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)&&(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB))
            {
                Lu8ObjValidFlag = VALID;
                if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                }
                else
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                }
                break;
            }
            else
            {
                Lu8ObjValidFlag = VALID;
                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                break;
            }
        }
        else if(Ls16ObjDisSub1_3 != FOLLOW_DIS_SUB_INVALID)
        {
            /* 只有本轮和上上轮匹配时，需要分探头探测距离和定扫频进行区分 */
            if(LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType > PDC_SNS_MEAS_STD)
            {
                if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] > FOLLOW_CHIRP_FAST_CONFIRM_DIS)
                {
                    if(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                    {
                        if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                    }
                    Lu8ObjValidFlag = VALID;
                    break;
                }
            }
            else
            {
                /* 做定扫确认过滤噪点 */
                if ((LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] > FOLLOW_STOP_STAND_CHIP_COMFIRM_STAR_DISTANCE) && 
                    (LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_STOP_STAND_CHIP_COMFIRM_END_DISTANCE))
                {
                    if(Ls16ObjDisSub1_2 > FOLLOW_STOP_STAND_CHIP_SUB)
                    {
                        break;
                    }
                }

                if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_STANDARD_FAST_CONFIRM_DIS)
                {
                    if(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                    {
                        if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                    }
                    if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_HIGH_CONFIRDENCE_DIS)
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_VERY_HIGH;
                    }
                    
                    /* 50cm内采用定扫结合 */
                    if (LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] <= FOLLOW_STANDARD_SLOW_COMFIRM_DIS)
                    {
                        if(Ls16ObjDisSub1_2 > FOLLOW_STOP_STAND_CHIP_SUB)
                        {
                            break;
                        }
                    }

                    Lu8ObjValidFlag = VALID;
                    break;
                }
            }
        }
        else if(Ls16ObjDisSub1_2 != FOLLOW_DIS_SUB_INVALID)
        {
            /* 对于中间雷达，车辆静止时，首次上电，快速确认障碍物，才采用此策略 */
            if(LpstrSnsSigGroupDataCache->u8RecordCnt == 2)
            {
                /* 只有本轮和上上轮匹配时，需要分探头探测距离和定扫频进行区分 */
                if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_STOP_POWER_ON_FAST_CONFIRM_DIS)
                {
                    if(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                    {
                        if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                    }
                    Lu8ObjValidFlag = VALID;
                    break;
                }
            }
        }
    }
    if(Lu8ObjValidFlag)
    {
        if(!LpstrSnsSigGroupDisFollow->u8DisFollowFlag)
        {
            Lu8ObjValidCheckFlag = 1;
        }
        else
        {
            /* 已经跟踪上的，再后续的比较中，只考虑近距离快速进入的，远离的等已经跟踪上的障碍物消失后再重新匹配 */
            if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < LpstrSnsSigGroupDisFollow->u16FollowDis)
            {
                Lu8ObjValidCheckFlag = 1;
            }
        }
        if (LpstrSysDisInfo->u8BlindKeepFlag)
        {
            Lu8ObjValidCheckFlag = 0;
        }

        if(Lu8ObjValidCheckFlag)
        {
            LpstrSnsSigGroupDisFollow->u8DisFollowFlag = VALID;
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
            LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
            
            LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
            LpstrSnsSigGroupDisFollow->u8KeepBeepFlag = 0;
            LpstrSnsSigGroupDisFollow->u8BlindFollowCardir = SNS_CAR_INVALID;
        }
    }
#if 0
    if((GenuSnsGroup == 0x00)&&(GenuSnsCh == 0x02))
    {
        printf("FML Right Search,Time:%.3f,FollowDis:%d,Flag:%d,NewDis:%d,LastDis:%d,LastBefDis:%d,Sub1_2:%d,Sub1_3:%d\r\n",GfMessageTime,\
            LpstrSnsSigGroupDisFollow->u16FollowDis,Lu8ObjValidCheckFlag,\
            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[0],LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u16ActualDis[0],\
            LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u16ActualDis[0],\
            Ls16ObjDisSub1_2,Ls16ObjDisSub1_3);
    }
#endif
}
/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForMiddleRightStopFollow
 * 
 * 功能描述: 中间探头的右侦听距离跟踪
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应右侦听探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-13 10:10   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForMiddleCornerRightStopFollow(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    Sns_CycleType LenuCurIndex,LenuLastIndex,LenuLastBefIndex;
    sint16 Ls16ObjDisSub1_2,Ls16ObjDisSub1_3;
    sint16 Ls16ObjDisSub1_Follow,Ls16ObjDisSub2_Follow,Ls16ObjDisSub3_Follow;
    sint16 Ls16ObjLastNewSub;
    uint8 Lu8SigGroupCnt1,Lu8SigGroupCnt2,Lu8SigGroupCnt3;
    uint8 Lu8ObjValidFlag = 0;
    uint8 Lu8ObjValidCheckFlag = 0;
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    LenuLastIndex = SnsSigGroupFollowGetLastIndex(LenuCurIndex);
    LenuLastBefIndex = SnsSigGroupFollowGetLastIndex(LenuLastIndex);

    
    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_Follow = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub2_Follow = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub3_Follow = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjLastNewSub = FOLLOW_DIS_SUB_INVALID;

    if(LpstrSnsSigGroupDisFollow->u16FollowDis < CHIRP_BLIND_DIS)
    {
        if(LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType > PDC_SNS_MEAS_STD)
        {
            return;/* 扫频近距离数据不用，只使用定频进行更新 */
        }
    }
    else if(LpstrSnsSigGroupDisFollow->u16FollowDis > STANDARD_BLIND_DIS)
    {
        if((LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType == PDC_SNS_MEAS_STD)&&(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[0]==SNS_INVALID_DIS))
        {
            return;/* 扫频跟踪远距离数据不用，只使用扫频进行更新 */
        }
    }
    
    if(LpstrSnsSigGroupDisFollow->u16FollowDis == PDC_OBJ_FOLLOW_INVALID_DIS)
    {
        LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
        LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
        LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
        LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
        LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
        LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
        LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 0;
    }
    else
    {
        /* 10轮为一个周期 */
        LpstrSnsSigGroupDisFollow->u8FollowCnt++;
        if (LpstrSnsSigGroupDisFollow->u8FollowCnt == FOLLOW_PERIOD)
        {
            LpstrSnsSigGroupDisFollow->u8FollowCnt = 0;
            LpstrSnsSigGroupDisFollow->u8FollowNoiseCnt = 0;
            LpstrSnsSigGroupDisFollow->u8FollowNoiseFlag = 0;
        }
        for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
        {
            Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub1_Follow = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub2_Follow = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub3_Follow = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjLastNewSub = FOLLOW_DIS_SUB_INVALID;
            /* 先用本轮和已经跟踪上的距离进行差值比较 */
            if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)
            {
                Ls16ObjDisSub1_Follow = ABS(LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]);
            }
            else
            {
                Ls16ObjDisSub1_Follow = FOLLOW_DIS_SUB_INVALID;
            }
            if(Ls16ObjDisSub1_Follow < FOLLOW_STOP_VALID_DIS_SUB)
            {
                LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                if(Ls16ObjDisSub1_Follow < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                {
                    /** @brief: 更新实时障碍物数据 */
                    LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                    
                    LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                    SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                    break;
                }
                else
                {
                    for(Lu8SigGroupCnt2 = 0; Lu8SigGroupCnt2 < LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u8SigGroupCnt; Lu8SigGroupCnt2++)
                    {
                        if(LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]!=SNS_INVALID_DIS)
                        {
                            Ls16ObjDisSub2_Follow = ABS(LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]);
                        }
                        else
                        {
                            Ls16ObjDisSub2_Follow = FOLLOW_DIS_SUB_INVALID;
                        }
                        if(Ls16ObjDisSub2_Follow < FOLLOW_STOP_VALID_DIS_SUB)
                        {
                            Ls16ObjDisSub1_2 = ABS(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]);
                            break;
                        }
                    }
                    if(Ls16ObjDisSub1_2 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                    {
                        /** @brief: 更新实时障碍物数据 */
                        LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                        
                        LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                        SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                        break;
                    }
                    else
                    {
                        for(Lu8SigGroupCnt3 = 0; Lu8SigGroupCnt3 < LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u8SigGroupCnt; Lu8SigGroupCnt3++)
                        {
                            if(LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]!=SNS_INVALID_DIS)
                            {
                                Ls16ObjDisSub3_Follow = ABS(LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]);
                            }
                            else
                            {
                                Ls16ObjDisSub3_Follow = FOLLOW_DIS_SUB_INVALID;
                            }
                            if(Ls16ObjDisSub3_Follow < FOLLOW_STOP_VALID_DIS_SUB)
                            {
                                Ls16ObjDisSub1_3 = ABS(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]);
                                break;
                            }
                        }
                        //if(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                        if((Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)&&(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_STOP_1_3_CONFIRM_DIS))
                        {
                            /** @brief: 更新实时障碍物数据 */
                            LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                            
                            LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                            SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                            break;
                        }
                    }
                    break;/* 距离突变5cm的，在过去周期中匹配不到的不更新，同时置信度维持不变 */
                }
            }
            else 
            {     
                if((LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDisFollow->u16DisDetect_Last!=SNS_INVALID_DIS))
                {
                    Ls16ObjLastNewSub = ABS(LpstrSnsSigGroupDisFollow->u16DisDetect_Last,LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]);
                }
                else
                {
                    Ls16ObjLastNewSub = FOLLOW_DIS_SUB_INVALID;
                }
                if(Ls16ObjLastNewSub < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                {
                    /** @brief: 障碍物未移动 */
                    LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                    LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                    
                    LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                    SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);

                    break;
                }
#if 0
                /* 遍历完成 信号组中没有与跟踪距离相匹配的距离认为是噪声 */
                if (Lu8SigGroupCnt1 == LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8SigGroupCnt)
                {
                    LpstrSnsSigGroupDisFollow->u8FollowNoiseCnt++;
                    if (LpstrSnsSigGroupDisFollow->u8FollowNoiseCnt > FOLLOW_STOP_STAND_CHIP_NOISE_CNT)
                    {
                        LpstrSnsSigGroupDisFollow->u8FollowNoiseFlag = 1;
                    }

                    if (LpstrSnsSigGroupDisFollow->u8FollowNoiseFlag)
                    {
                        LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
                        LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                        LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
                        LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
                        LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                        LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
                        LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 0;
                        LpstrSnsSigGroupDisFollow->u8FollowNoiseCnt = 0;
                        LpstrSnsSigGroupDisFollow->u8FollowNoiseFlag = 0;
                    }
                }
#endif
            }
        }
        /* 遍历完成，也没有查到有效数据，需要维持跟踪距离，同时置信度递减，跟踪失效技术累加 */
        if(Lu8SigGroupCnt1 == LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8SigGroupCnt)
        {
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt++;
            if(LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt > FOLLOW_STOP_EXIT_CNT)
            {
                /* 强制跟踪 */
                if (LpstrSnsSigGroupDisFollow->u8BlindFollowFlag)
                {
                    /* code */
                }
                else
                {
                    /** @brief: 退出跟踪 */
                    LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
                    LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                    LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
                    LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
                    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                    LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
                    LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 0;
                    LpstrSnsSigGroupDisFollow->u8FollowNoiseCnt = 0;
                    LpstrSnsSigGroupDisFollow->u8FollowNoiseFlag = 0;
                }
            }
            SnsFollowDisProSub(&LpstrSnsSigGroupDisFollow->enuFollowDisPro,LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType);
            /* 查询是否有近距离障碍物出现 */
            if(Lu8SigGroupCnt1 != 0)
            {
                SnsSigGroupDisFollowForMiddleCornerRightStopSearch(LeGroup,LenuSnsCh,LpstrSnsSigGroupDisFollow);
            }
        }
        else if(Lu8SigGroupCnt1 > 0)
        {
            //SnsSigGroupCarStopObjSearch(LpstrSysData,LpstrSigGroupData,LpstrSnsSigGroupDisFollow,LenuCurIndex);
        }
    }
#if 0
    if((GenuSnsGroup == 0x00)&&(GenuSnsCh == 0x02))
    {
        printf("FML Right Follow,Time:%.3f,FollowDis:%d,Flag:%d,NewDis:%d,LastDis:%d,LastBefDis:%d,Sub1_2:%d,Sub1_3:%d\r\n",GfMessageTime,\
            LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDisFollow->u8DisFollowFlag,\
            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[0],LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u16ActualDis[0],\
            LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u16ActualDis[0],\
            Ls16ObjDisSub1_2,Ls16ObjDisSub1_3);
    }
#endif
}


/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForSideMasterStopSearch
 * 
 * 功能描述: 侧边探头车辆停止时主发距离的匹配查询处理
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应主发探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-13 11:34   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForSideMasterStopSearch(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    Sns_CycleType LenuCurIndex,LenuLastIndex,LenuLastBefIndex;
    sint16 Ls16ObjDisSub1_2,Ls16ObjDisSub1_3;
    uint8 Lu8SigGroupCnt1,Lu8SigGroupCnt2,Lu8SigGroupCnt3;
    uint8 Lu8ObjValidFlag = 0;
    uint8 Lu8ObjValidCheckFlag = 0;
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    LenuLastIndex = SnsSigGroupFollowGetLastIndex(LenuCurIndex);
    LenuLastBefIndex = SnsSigGroupFollowGetLastIndex(LenuLastIndex);

    
    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;

    for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
    {
        Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
        /* 先用本轮和上一轮进行差值比较 */
        for(Lu8SigGroupCnt2 = 0; Lu8SigGroupCnt2 < LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u8SigGroupCnt; Lu8SigGroupCnt2++)
        {
            /** 比较本轮和上一轮的差值 */
            if((LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]!=SNS_INVALID_DIS))
            {
                Ls16ObjDisSub1_2 = ABS(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]);
                if(Ls16ObjDisSub1_2 < FOLLOW_STOP_VALID_DIS_SUB)
                {
                    break;
                }
                else
                {
                    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
                }
            }
        }
    
        /* 本轮数据和上上一轮数据做比较 */
        for(Lu8SigGroupCnt3 = 0; Lu8SigGroupCnt3 < LpstrSnsSigGroupDataCache->MasterBuf[LenuLastBefIndex].u8SigGroupCnt; Lu8SigGroupCnt3++)
        {
            if((LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->MasterBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]!=SNS_INVALID_DIS))
            {
                Ls16ObjDisSub1_3 = ABS(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->MasterBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]);
                if(Ls16ObjDisSub1_3 < FOLLOW_STOP_VALID_DIS_SUB)
                {
                    break;
                }
                else
                {
                    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
                }
            }
        }
        
        if((Ls16ObjDisSub1_2 != FOLLOW_DIS_SUB_INVALID)&&(Ls16ObjDisSub1_3 != FOLLOW_DIS_SUB_INVALID))
        {
            if((Ls16ObjDisSub1_2 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)&&(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB))
            {
                Lu8ObjValidFlag = VALID;
                if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                }
                else
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                }
                break;
            }
            else
            {
                Lu8ObjValidFlag = VALID;
                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                break;
            }
        }
        else if(Ls16ObjDisSub1_3 != FOLLOW_DIS_SUB_INVALID)
        {
            /* 只有本轮和上上轮匹配时，对于侧雷达而言是定扫匹配，不需要判断当前的探测类型 */
            if(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
            {
                if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                }
                else
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                }
            }
            else
            {
                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
            }
            Lu8ObjValidFlag = VALID;
            break;
            
        }
        else if(Ls16ObjDisSub1_2 != FOLLOW_DIS_SUB_INVALID)
        {
            /* 对于侧边雷达，定定、扫扫状态，连续2帧可以直接输出 */
            if(LpstrSnsSigGroupDataCache->u8RecordCnt == 2)
            {
                /* 只有本轮和上上轮匹配时，需要分探头探测距离和定扫频进行区分 */
                if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_STOP_POWER_ON_FAST_CONFIRM_DIS)
                {
                    if(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                    {
                        if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                    }
                    Lu8ObjValidFlag = VALID;
                    break;
                }
            }
            else
            {
                /* 非首次上电，必须根据探测类型进行更新 */
                if(LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType > PDC_SNS_MEAS_STD)
                {
                    if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] > FOLLOW_CHIRP_FAST_CONFIRM_DIS)
                    {
                        if(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                        {
                            if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                            }
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                        Lu8ObjValidFlag = VALID;
                        break;
                    }
                }
                else
                {
                    if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_STANDARD_FAST_CONFIRM_DIS)
                    {
                        if(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                        {
                            if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                            }
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                        if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_HIGH_CONFIRDENCE_DIS)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_VERY_HIGH;
                        }

                        if (LpstrSnsSigGroupDataCache->SysDataBuf[LenuLastIndex].eMeasType == PDC_SNS_MEAS_STD)
                        {
                            if(Ls16ObjDisSub1_3 > FOLLOW_STOP_STAND_CHIP_SUB)
                            {
                                break;
                            }
                        }

                        Lu8ObjValidFlag = VALID;
                        break;
                    }
                }
            }
        }
    }
    if(Lu8ObjValidFlag)
    {
        if(!LpstrSnsSigGroupDisFollow->u8DisFollowFlag)
        {
            Lu8ObjValidCheckFlag = 1;
        }
        else
        {
            /* 已经跟踪上的，再后续的比较中，只考虑近距离快速进入的，远离的等已经跟踪上的障碍物消失后再重新匹配 */
            if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < LpstrSnsSigGroupDisFollow->u16FollowDis)
            {
                Lu8ObjValidCheckFlag = 1;
            }
        }
        if(Lu8ObjValidCheckFlag)
        {
            LpstrSnsSigGroupDisFollow->u8DisFollowFlag = VALID;
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
            LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
            
            LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
        }
    }
}



/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForSideMasterStopFollow
 * 
 * 功能描述: 侧边探头的跟踪策略
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应主发探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-13 17:26   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForSideMasterStopFollow(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    Sns_CycleType LenuCurIndex,LenuLastIndex,LenuLastBefIndex;
    sint16 Ls16ObjDisSub1_2,Ls16ObjDisSub1_3;
    sint16 Ls16ObjDisSub1_Follow,Ls16ObjDisSub2_Follow,Ls16ObjDisSub3_Follow;
    sint16 Ls16ObjLastNewSub;
    uint8 Lu8SigGroupCnt1,Lu8SigGroupCnt2,Lu8SigGroupCnt3;
    uint8 Lu8ObjValidFlag = 0;
    uint8 Lu8ObjValidCheckFlag = 0;
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    LenuLastIndex = SnsSigGroupFollowGetLastIndex(LenuCurIndex);
    LenuLastBefIndex = SnsSigGroupFollowGetLastIndex(LenuLastIndex);

    
    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_Follow = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub2_Follow = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub3_Follow = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjLastNewSub = FOLLOW_DIS_SUB_INVALID;

    if(LpstrSnsSigGroupDisFollow->u16FollowDis < CHIRP_BLIND_DIS)
    {
        if(LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType > PDC_SNS_MEAS_STD)
        {
            return;/* 扫频近距离数据不用，只使用定频进行更新 */
        }
    }
    else if(LpstrSnsSigGroupDisFollow->u16FollowDis > STANDARD_BLIND_DIS)
    {
        if((LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType == PDC_SNS_MEAS_STD)&&(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[0]==SNS_INVALID_DIS))
        {
            return;/* 扫频跟踪远距离数据不用，只使用扫频进行更新 */
        }
    }
    
    if(LpstrSnsSigGroupDisFollow->u16FollowDis == PDC_OBJ_FOLLOW_INVALID_DIS)
    {
        LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
        LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
        LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
        LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
        LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
        LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
        LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 0;
    }
    else
    {
        for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
        {
            Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub1_Follow = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub2_Follow = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub3_Follow = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjLastNewSub = FOLLOW_DIS_SUB_INVALID;
            /* 先用本轮和已经跟踪上的距离进行差值比较 */
            if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)
            {
                Ls16ObjDisSub1_Follow = ABS(LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]);
            }
            else
            {
                Ls16ObjDisSub1_Follow = FOLLOW_DIS_SUB_INVALID;
            }
            if(Ls16ObjDisSub1_Follow < FOLLOW_STOP_VALID_DIS_SUB)
            {
                LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                if(Ls16ObjDisSub1_Follow < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                {
                    /** @brief: 更新实时障碍物数据 */
                    LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                    
                    LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                    SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                    break;
                }
                else
                {
                    for(Lu8SigGroupCnt2 = 0; Lu8SigGroupCnt2 < LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u8SigGroupCnt; Lu8SigGroupCnt2++)
                    {
                        if(LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]!=SNS_INVALID_DIS)
                        {
                            Ls16ObjDisSub2_Follow = ABS(LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]);
                        }
                        else
                        {
                            Ls16ObjDisSub2_Follow = FOLLOW_DIS_SUB_INVALID;
                        }
                        if(Ls16ObjDisSub2_Follow < FOLLOW_STOP_VALID_DIS_SUB)
                        {
                            Ls16ObjDisSub1_2 = ABS(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]);
                            break;
                        }
                    }
                    if(Ls16ObjDisSub1_2 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                    {
                        /** @brief: 更新实时障碍物数据 */
                        LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                        
                        LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                        SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                        break;
                    }
                    else
                    {
                        for(Lu8SigGroupCnt3 = 0; Lu8SigGroupCnt3 < LpstrSnsSigGroupDataCache->MasterBuf[LenuLastBefIndex].u8SigGroupCnt; Lu8SigGroupCnt3++)
                        {
                            if(LpstrSnsSigGroupDataCache->MasterBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]!=SNS_INVALID_DIS)
                            {
                                Ls16ObjDisSub3_Follow = ABS(LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->MasterBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]);
                            }
                            else
                            {
                                Ls16ObjDisSub3_Follow = FOLLOW_DIS_SUB_INVALID;
                            }
                            if(Ls16ObjDisSub3_Follow < FOLLOW_STOP_VALID_DIS_SUB)
                            {
                                Ls16ObjDisSub1_3 = ABS(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->MasterBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]);
                                break;
                            }
                        }
                        if(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                        {
                            /** @brief: 更新实时障碍物数据 */
                            LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                            
                            LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                            SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                            break;
                        }
                    }
                    break;/* 距离突变5cm的，在过去周期中匹配不到的不更新，同时置信度维持不变 */
                }
            }
            else 
            {     
                if((LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDisFollow->u16DisDetect_Last!=SNS_INVALID_DIS))
                {
                    Ls16ObjLastNewSub = ABS(LpstrSnsSigGroupDisFollow->u16DisDetect_Last,LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]);
                }
                else
                {
                    Ls16ObjLastNewSub = FOLLOW_DIS_SUB_INVALID;
                }
                if(Ls16ObjLastNewSub < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                {
                    /** @brief: 障碍物未移动 */
                    LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                    LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                    
                    LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                    SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);

                    break;
                }
            }
        }
        /* 遍历完成，也没有查到有效数据，需要维持跟踪距离，同时置信度递减，跟踪失效技术累加 */
        if(Lu8SigGroupCnt1 == LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8SigGroupCnt)
        {
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt++;
            if(LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt > FOLLOW_STOP_EXIT_CNT)
            {
                /** @brief: 退出跟踪 */
                LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
                LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
                LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
                LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
                LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 0;
            }
            SnsFollowDisProSub(&LpstrSnsSigGroupDisFollow->enuFollowDisPro,LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType);
            /* 查询是否有近距离障碍物出现 */
            if(Lu8SigGroupCnt1 != 0)
            {
                SnsSigGroupDisFollowForSideMasterStopSearch(LeGroup,LenuSnsCh,LpstrSnsSigGroupDisFollow);
            }
        }
        else if(Lu8SigGroupCnt1 > 0)
        {
            //SnsSigGroupCarStopObjSearch(LpstrSysData,LpstrSigGroupData,LpstrSnsSigGroupDisFollow,LenuCurIndex);
        }
    }
}

/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForSideLefttopSearch
 * 
 * 功能描述: 侧边左侦听探头车辆停止时主发距离的匹配查询处理
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应主发探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-13 11:34   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForSideLeftStopSearch(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    Sns_CycleType LenuCurIndex,LenuLastIndex,LenuLastBefIndex;
    sint16 Ls16ObjDisSub1_2,Ls16ObjDisSub1_3;
    uint8 Lu8SigGroupCnt1,Lu8SigGroupCnt2,Lu8SigGroupCnt3;
    uint8 Lu8ObjValidFlag = 0;
    uint8 Lu8ObjValidCheckFlag = 0;
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    LenuLastIndex = SnsSigGroupFollowGetLastIndex(LenuCurIndex);
    LenuLastBefIndex = SnsSigGroupFollowGetLastIndex(LenuLastIndex);

    
    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;

    for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
    {
        Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
        /* 先用本轮和上一轮进行差值比较 */
        for(Lu8SigGroupCnt2 = 0; Lu8SigGroupCnt2 < LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u8SigGroupCnt; Lu8SigGroupCnt2++)
        {
            /** 比较本轮和上一轮的差值 */
            if((LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]!=SNS_INVALID_DIS))
            {
                Ls16ObjDisSub1_2 = ABS(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]);
                if(Ls16ObjDisSub1_2 < FOLLOW_STOP_VALID_DIS_SUB)
                {
                    break;
                }
                else
                {
                    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
                }
            }
        }
    
        /* 本轮数据和上上一轮数据做比较 */
        for(Lu8SigGroupCnt3 = 0; Lu8SigGroupCnt3 < LpstrSnsSigGroupDataCache->LeftBuf[LenuLastBefIndex].u8SigGroupCnt; Lu8SigGroupCnt3++)
        {
            if((LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->LeftBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]!=SNS_INVALID_DIS))
            {
                Ls16ObjDisSub1_3 = ABS(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->LeftBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]);
                if(Ls16ObjDisSub1_3 < FOLLOW_STOP_VALID_DIS_SUB)
                {
                    break;
                }
                else
                {
                    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
                }
            }
        }
        
        if((Ls16ObjDisSub1_2 != FOLLOW_DIS_SUB_INVALID)&&(Ls16ObjDisSub1_3 != FOLLOW_DIS_SUB_INVALID))
        {
            if((Ls16ObjDisSub1_2 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)&&(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB))
            {
                Lu8ObjValidFlag = VALID;
                if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                }
                else
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                }
                break;
            }
            else
            {
                Lu8ObjValidFlag = VALID;
                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                break;
            }
        }
        else if(Ls16ObjDisSub1_3 != FOLLOW_DIS_SUB_INVALID)
        {
            /* 只有本轮和上上轮匹配时，对于侧雷达而言是定扫匹配，不需要判断当前的探测类型 */
            if(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
            {
                if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                }
                else
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                }
            }
            else
            {
                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
            }
            Lu8ObjValidFlag = VALID;
            break;
            
        }
        else if(Ls16ObjDisSub1_2 != FOLLOW_DIS_SUB_INVALID)
        {
            /* 对于侧边雷达，定定、扫扫状态，连续2帧可以直接输出 */
            if(LpstrSnsSigGroupDataCache->u8RecordCnt == 2)
            {
                /* 只有本轮和上上轮匹配时，需要分探头探测距离和定扫频进行区分 */
                if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_STOP_POWER_ON_FAST_CONFIRM_DIS)
                {
                    if(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                    {
                        if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                    }
                    Lu8ObjValidFlag = VALID;
                    break;
                }
            }
            else
            {
                /* 非首次上电，必须根据探测类型进行更新 */
                if(LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType > PDC_SNS_MEAS_STD)
                {
                    if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] > FOLLOW_CHIRP_FAST_CONFIRM_DIS)
                    {
                        if(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                        {
                            if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                            }
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                        Lu8ObjValidFlag = VALID;
                        break;
                    }
                }
                else
                {
                    if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_STANDARD_FAST_CONFIRM_DIS)
                    {
                        if(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                        {
                            if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                            }
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                        if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_HIGH_CONFIRDENCE_DIS)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_VERY_HIGH;
                        }

                        if (LpstrSnsSigGroupDataCache->SysDataBuf[LenuLastIndex].eMeasType == PDC_SNS_MEAS_STD)
                        {
                            if(Ls16ObjDisSub1_3 > FOLLOW_STOP_STAND_CHIP_SUB)
                            {
                                break;
                            }
                        }

                        Lu8ObjValidFlag = VALID;
                        break;
                    }
                }
            }
        }
    }
    if(Lu8ObjValidFlag)
    {
        if(!LpstrSnsSigGroupDisFollow->u8DisFollowFlag)
        {
            Lu8ObjValidCheckFlag = 1;
        }
        else
        {
            /* 已经跟踪上的，再后续的比较中，只考虑近距离快速进入的，远离的等已经跟踪上的障碍物消失后再重新匹配 */
            if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < LpstrSnsSigGroupDisFollow->u16FollowDis)
            {
                Lu8ObjValidCheckFlag = 1;
            }
        }
        if(Lu8ObjValidCheckFlag)
        {
            LpstrSnsSigGroupDisFollow->u8DisFollowFlag = VALID;
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
            LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
            
            LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
        }
    }
}

/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForSideMasterStopFollow
 * 
 * 功能描述: 侧边左侦听探头的跟踪策略
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应主发探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-13 17:26   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForSideLeftStopFollow(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    Sns_CycleType LenuCurIndex,LenuLastIndex,LenuLastBefIndex;
    sint16 Ls16ObjDisSub1_2,Ls16ObjDisSub1_3;
    sint16 Ls16ObjDisSub1_Follow,Ls16ObjDisSub2_Follow,Ls16ObjDisSub3_Follow;
    sint16 Ls16ObjLastNewSub;
    uint8 Lu8SigGroupCnt1,Lu8SigGroupCnt2,Lu8SigGroupCnt3;
    uint8 Lu8ObjValidFlag = 0;
    uint8 Lu8ObjValidCheckFlag = 0;
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    LenuLastIndex = SnsSigGroupFollowGetLastIndex(LenuCurIndex);
    LenuLastBefIndex = SnsSigGroupFollowGetLastIndex(LenuLastIndex);

    
    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_Follow = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub2_Follow = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub3_Follow = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjLastNewSub = FOLLOW_DIS_SUB_INVALID;

    if(LpstrSnsSigGroupDisFollow->u16FollowDis < CHIRP_BLIND_DIS)
    {
        if(LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType > PDC_SNS_MEAS_STD)
        {
            return;/* 扫频近距离数据不用，只使用定频进行更新 */
        }
    }
    else if(LpstrSnsSigGroupDisFollow->u16FollowDis > STANDARD_BLIND_DIS)
    {
        if((LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType == PDC_SNS_MEAS_STD)&&(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[0]==SNS_INVALID_DIS))
        {
            return;/* 扫频跟踪远距离数据不用，只使用扫频进行更新 */
        }
    }
    
    if(LpstrSnsSigGroupDisFollow->u16FollowDis == PDC_OBJ_FOLLOW_INVALID_DIS)
    {
        LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
        LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
        LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
        LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
        LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
        LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
        LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 0;
    }
    else
    {
        for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
        {
            Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub1_Follow = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub2_Follow = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub3_Follow = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjLastNewSub = FOLLOW_DIS_SUB_INVALID;
            /* 先用本轮和已经跟踪上的距离进行差值比较 */
            if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)
            {
                Ls16ObjDisSub1_Follow = ABS(LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]);
            }
            else
            {
                Ls16ObjDisSub1_Follow = FOLLOW_DIS_SUB_INVALID;
            }
            if(Ls16ObjDisSub1_Follow < FOLLOW_STOP_VALID_DIS_SUB)
            {
                LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                if(Ls16ObjDisSub1_Follow < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                {
                    /** @brief: 更新实时障碍物数据 */
                    LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                    
                    LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                    SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                    break;
                }
                else
                {
                    for(Lu8SigGroupCnt2 = 0; Lu8SigGroupCnt2 < LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u8SigGroupCnt; Lu8SigGroupCnt2++)
                    {
                        if(LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]!=SNS_INVALID_DIS)
                        {
                            Ls16ObjDisSub2_Follow = ABS(LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]);
                        }
                        else
                        {
                            Ls16ObjDisSub2_Follow = FOLLOW_DIS_SUB_INVALID;
                        }
                        if(Ls16ObjDisSub2_Follow < FOLLOW_STOP_VALID_DIS_SUB)
                        {
                            Ls16ObjDisSub1_2 = ABS(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]);
                            break;
                        }
                    }
                    if(Ls16ObjDisSub1_2 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                    {
                        /** @brief: 更新实时障碍物数据 */
                        LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                        
                        LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                        SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                        break;
                    }
                    else
                    {
                        for(Lu8SigGroupCnt3 = 0; Lu8SigGroupCnt3 < LpstrSnsSigGroupDataCache->LeftBuf[LenuLastBefIndex].u8SigGroupCnt; Lu8SigGroupCnt3++)
                        {
                            if(LpstrSnsSigGroupDataCache->LeftBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]!=SNS_INVALID_DIS)
                            {
                                Ls16ObjDisSub3_Follow = ABS(LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->LeftBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]);
                            }
                            else
                            {
                                Ls16ObjDisSub3_Follow = FOLLOW_DIS_SUB_INVALID;
                            }
                            if(Ls16ObjDisSub3_Follow < FOLLOW_STOP_VALID_DIS_SUB)
                            {
                                Ls16ObjDisSub1_3 = ABS(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->LeftBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]);
                                break;
                            }
                        }
                        if(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                        {
                            /** @brief: 更新实时障碍物数据 */
                            LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                            
                            LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                            SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                            break;
                        }
                    }
                    break;/* 距离突变5cm的，在过去周期中匹配不到的不更新，同时置信度维持不变 */
                }
            }
            else 
            {     
                if((LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDisFollow->u16DisDetect_Last!=SNS_INVALID_DIS))
                {
                    Ls16ObjLastNewSub = ABS(LpstrSnsSigGroupDisFollow->u16DisDetect_Last,LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]);
                }
                else
                {
                    Ls16ObjLastNewSub = FOLLOW_DIS_SUB_INVALID;
                }
                if(Ls16ObjLastNewSub < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                {
                    /** @brief: 障碍物未移动 */
                    LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                    LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                    
                    LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                    SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);

                    break;
                }
            }
        }
        /* 遍历完成，也没有查到有效数据，需要维持跟踪距离，同时置信度递减，跟踪失效技术累加 */
        if(Lu8SigGroupCnt1 == LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8SigGroupCnt)
        {
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt++;
            if(LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt > FOLLOW_STOP_EXIT_CNT)
            {
                /** @brief: 退出跟踪 */
                LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
                LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
                LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
                LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
                LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 0;
            }
            SnsFollowDisProSub(&LpstrSnsSigGroupDisFollow->enuFollowDisPro,LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType);
            /* 查询是否有近距离障碍物出现 */
            if(Lu8SigGroupCnt1 != 0)
            {
                SnsSigGroupDisFollowForSideLeftStopSearch(LeGroup,LenuSnsCh,LpstrSnsSigGroupDisFollow);
            }
        }
        else if(Lu8SigGroupCnt1 > 0)
        {
            //SnsSigGroupCarStopObjSearch(LpstrSysData,LpstrSigGroupData,LpstrSnsSigGroupDisFollow,LenuCurIndex);
        }
    }
}

/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForSideRightStopSearch
 * 
 * 功能描述: 侧边右侦听探头车辆停止时主发距离的匹配查询处理
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应主发探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-13 11:34   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForSideRightStopSearch(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    Sns_CycleType LenuCurIndex,LenuLastIndex,LenuLastBefIndex;
    sint16 Ls16ObjDisSub1_2,Ls16ObjDisSub1_3;
    uint8 Lu8SigGroupCnt1,Lu8SigGroupCnt2,Lu8SigGroupCnt3;
    uint8 Lu8ObjValidFlag = 0;
    uint8 Lu8ObjValidCheckFlag = 0;
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    LenuLastIndex = SnsSigGroupFollowGetLastIndex(LenuCurIndex);
    LenuLastBefIndex = SnsSigGroupFollowGetLastIndex(LenuLastIndex);

    
    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;

    for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
    {
        Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
        /* 先用本轮和上一轮进行差值比较 */
        for(Lu8SigGroupCnt2 = 0; Lu8SigGroupCnt2 < LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u8SigGroupCnt; Lu8SigGroupCnt2++)
        {
            /** 比较本轮和上一轮的差值 */
            if((LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]!=SNS_INVALID_DIS))
            {
                Ls16ObjDisSub1_2 = ABS(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]);
                if(Ls16ObjDisSub1_2 < FOLLOW_STOP_VALID_DIS_SUB)
                {
                    break;
                }
                else
                {
                    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
                }
            }
        }
    
        /* 本轮数据和上上一轮数据做比较 */
        for(Lu8SigGroupCnt3 = 0; Lu8SigGroupCnt3 < LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u8SigGroupCnt; Lu8SigGroupCnt3++)
        {
            if((LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]!=SNS_INVALID_DIS))
            {
                Ls16ObjDisSub1_3 = ABS(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]);
                if(Ls16ObjDisSub1_3 < FOLLOW_STOP_VALID_DIS_SUB)
                {
                    break;
                }
                else
                {
                    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
                }
            }
        }
        
        if((Ls16ObjDisSub1_2 != FOLLOW_DIS_SUB_INVALID)&&(Ls16ObjDisSub1_3 != FOLLOW_DIS_SUB_INVALID))
        {
            if((Ls16ObjDisSub1_2 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)&&(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB))
            {
                Lu8ObjValidFlag = VALID;
                if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                }
                else
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                }
                break;
            }
            else
            {
                Lu8ObjValidFlag = VALID;
                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                break;
            }
        }
        else if(Ls16ObjDisSub1_3 != FOLLOW_DIS_SUB_INVALID)
        {
            /* 只有本轮和上上轮匹配时，对于侧雷达而言是定扫匹配，不需要判断当前的探测类型 */
            if(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
            {
                if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                }
                else
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                }
            }
            else
            {
                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
            }
            Lu8ObjValidFlag = VALID;
            break;
            
        }
        else if(Ls16ObjDisSub1_2 != FOLLOW_DIS_SUB_INVALID)
        {
            /* 对于侧边雷达，定定、扫扫状态，连续2帧可以直接输出 */
            if(LpstrSnsSigGroupDataCache->u8RecordCnt == 2)
            {
                /* 只有本轮和上上轮匹配时，需要分探头探测距离和定扫频进行区分 */
                if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_STOP_POWER_ON_FAST_CONFIRM_DIS)
                {
                    if(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                    {
                        if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                    }
                    Lu8ObjValidFlag = VALID;
                    break;
                }
            }
            else
            {
                /* 非首次上电，必须根据探测类型进行更新 */
                if(LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType > PDC_SNS_MEAS_STD)
                {
                    if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] > FOLLOW_CHIRP_FAST_CONFIRM_DIS)
                    {
                        if(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                        {
                            if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                            }
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                        Lu8ObjValidFlag = VALID;
                        break;
                    }
                }
                else
                {
                    if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_STANDARD_FAST_CONFIRM_DIS)
                    {
                        if(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                        {
                            if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                            }
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                        if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_HIGH_CONFIRDENCE_DIS)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_VERY_HIGH;
                        }

                        if (LpstrSnsSigGroupDataCache->SysDataBuf[LenuLastIndex].eMeasType == PDC_SNS_MEAS_STD)
                        {
                            if(Ls16ObjDisSub1_3 > FOLLOW_STOP_STAND_CHIP_SUB)
                            {
                                break;
                            }
                        }

                        Lu8ObjValidFlag = VALID;
                        break;
                    }
                }
            }
        }
    }
    if(Lu8ObjValidFlag)
    {
        if(!LpstrSnsSigGroupDisFollow->u8DisFollowFlag)
        {
            Lu8ObjValidCheckFlag = 1;
        }
        else
        {
            /* 已经跟踪上的，再后续的比较中，只考虑近距离快速进入的，远离的等已经跟踪上的障碍物消失后再重新匹配 */
            if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < LpstrSnsSigGroupDisFollow->u16FollowDis)
            {
                Lu8ObjValidCheckFlag = 1;
            }
        }
        if(Lu8ObjValidCheckFlag)
        {
            LpstrSnsSigGroupDisFollow->u8DisFollowFlag = VALID;
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
            LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
            
            LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
        }
    }
#if 0
    if((GenuSnsGroup == 0x00)&&(GenuSnsCh == 0x00))
    {
        printf("FLS Right Search,Time:%.3f,FollowDis:%d,Flag:%d,NewDis:%d,LastDis:%d,LastBefDis:%d,Sub1_2:%d,Sub1_3:%d\r\n",GfMessageTime,\
            LpstrSnsSigGroupDisFollow->u16FollowDis,Lu8ObjValidCheckFlag,\
            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[0],LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u16ActualDis[0],\
            LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u16ActualDis[0],\
            Ls16ObjDisSub1_2,Ls16ObjDisSub1_3);
    }
#endif
}

/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForSideRightStopFollow
 * 
 * 功能描述: 侧边左侦听探头的跟踪策略
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应主发探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-13 17:26   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForSideRightStopFollow(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    Sns_CycleType LenuCurIndex,LenuLastIndex,LenuLastBefIndex;
    sint16 Ls16ObjDisSub1_2,Ls16ObjDisSub1_3;
    sint16 Ls16ObjDisSub1_Follow,Ls16ObjDisSub2_Follow,Ls16ObjDisSub3_Follow;
    sint16 Ls16ObjLastNewSub;
    uint8 Lu8SigGroupCnt1,Lu8SigGroupCnt2,Lu8SigGroupCnt3;
    uint8 Lu8ObjValidFlag = 0;
    uint8 Lu8ObjValidCheckFlag = 0;
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    LenuLastIndex = SnsSigGroupFollowGetLastIndex(LenuCurIndex);
    LenuLastBefIndex = SnsSigGroupFollowGetLastIndex(LenuLastIndex);

    
    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_Follow = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub2_Follow = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub3_Follow = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjLastNewSub = FOLLOW_DIS_SUB_INVALID;

    if(LpstrSnsSigGroupDisFollow->u16FollowDis < CHIRP_BLIND_DIS)
    {
        if(LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType > PDC_SNS_MEAS_STD)
        {
            return;/* 扫频近距离数据不用，只使用定频进行更新 */
        }
    }
    else if(LpstrSnsSigGroupDisFollow->u16FollowDis > STANDARD_BLIND_DIS)
    {
        if((LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType == PDC_SNS_MEAS_STD)&&(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[0]==SNS_INVALID_DIS))
        {
            return;/* 扫频跟踪远距离数据不用，只使用扫频进行更新 */
        }
    }
    
    if(LpstrSnsSigGroupDisFollow->u16FollowDis == PDC_OBJ_FOLLOW_INVALID_DIS)
    {
        LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
        LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
        LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
        LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
        LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
        LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
        LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 0;
    }
    else
    {
        for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
        {
            Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub1_Follow = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub2_Follow = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub3_Follow = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjLastNewSub = FOLLOW_DIS_SUB_INVALID;
            /* 先用本轮和已经跟踪上的距离进行差值比较 */
            if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)
            {
                Ls16ObjDisSub1_Follow = ABS(LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]);
            }
            else
            {
                Ls16ObjDisSub1_Follow = FOLLOW_DIS_SUB_INVALID;
            }
            if(Ls16ObjDisSub1_Follow < FOLLOW_STOP_VALID_DIS_SUB)
            {
                LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                if(Ls16ObjDisSub1_Follow < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                {
                    /** @brief: 更新实时障碍物数据 */
                    LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                    
                    LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                    SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                    break;
                }
                else
                {
                    for(Lu8SigGroupCnt2 = 0; Lu8SigGroupCnt2 < LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u8SigGroupCnt; Lu8SigGroupCnt2++)
                    {
                        if(LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]!=SNS_INVALID_DIS)
                        {
                            Ls16ObjDisSub2_Follow = ABS(LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]);
                        }
                        else
                        {
                            Ls16ObjDisSub2_Follow = FOLLOW_DIS_SUB_INVALID;
                        }
                        if(Ls16ObjDisSub2_Follow < FOLLOW_STOP_VALID_DIS_SUB)
                        {
                            Ls16ObjDisSub1_2 = ABS(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]);
                            break;
                        }
                    }
                    if(Ls16ObjDisSub1_2 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                    {
                        /** @brief: 更新实时障碍物数据 */
                        LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                        
                        LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                        LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                        SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                        break;
                    }
                    else
                    {
                        for(Lu8SigGroupCnt3 = 0; Lu8SigGroupCnt3 < LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u8SigGroupCnt; Lu8SigGroupCnt3++)
                        {
                            if(LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]!=SNS_INVALID_DIS)
                            {
                                Ls16ObjDisSub3_Follow = ABS(LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]);
                            }
                            else
                            {
                                Ls16ObjDisSub3_Follow = FOLLOW_DIS_SUB_INVALID;
                            }
                            if(Ls16ObjDisSub3_Follow < FOLLOW_STOP_VALID_DIS_SUB)
                            {
                                Ls16ObjDisSub1_3 = ABS(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]);
                                break;
                            }
                        }
                        if(Ls16ObjDisSub1_3 < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                        {
                            /** @brief: 更新实时障碍物数据 */
                            LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                            
                            LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                            LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                            SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                            break;
                        }
                    }
                    break;/* 距离突变5cm的，在过去周期中匹配不到的不更新，同时置信度维持不变 */
                }
            }
            else 
            {     
                if((LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDisFollow->u16DisDetect_Last!=SNS_INVALID_DIS))
                {
                    Ls16ObjLastNewSub = ABS(LpstrSnsSigGroupDisFollow->u16DisDetect_Last,LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]);
                }
                else
                {
                    Ls16ObjLastNewSub = FOLLOW_DIS_SUB_INVALID;
                }
                if(Ls16ObjLastNewSub < FOLLOW_STOP_FAST_CONFIRM_DIS_SUB)
                {
                    /** @brief: 障碍物未移动 */
                    LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                    LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                    
                    LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                    SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);

                    break;
                }
            }
        }
        /* 遍历完成，也没有查到有效数据，需要维持跟踪距离，同时置信度递减，跟踪失效技术累加 */
        if(Lu8SigGroupCnt1 == LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8SigGroupCnt)
        {
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt++;
            if(LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt > FOLLOW_STOP_EXIT_CNT)
            {
                /** @brief: 退出跟踪 */
                LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
                LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
                LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
                LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
                LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 0;
            }
            SnsFollowDisProSub(&LpstrSnsSigGroupDisFollow->enuFollowDisPro,LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType);
            /* 查询是否有近距离障碍物出现 */
            if(Lu8SigGroupCnt1 != 0)
            {
                SnsSigGroupDisFollowForSideRightStopSearch(LeGroup,LenuSnsCh,LpstrSnsSigGroupDisFollow);
            }
        }
        else if(Lu8SigGroupCnt1 > 0)
        {
            //SnsSigGroupCarStopObjSearch(LpstrSysData,LpstrSigGroupData,LpstrSnsSigGroupDisFollow,LenuCurIndex);
        }
    }
#if 0
    if((GenuSnsGroup == 0x00)&&(GenuSnsCh == 0x00))
    {
        printf("FLS Right Follow,Time:%.3f,FollowDis:%d,Flag:%d,NewDis:%d,LastDis:%d,LastBefDis:%d,Sub1_2:%d,Sub1_3:%d\r\n",GfMessageTime,\
            LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDisFollow->u8DisFollowFlag,\
            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[0],LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u16ActualDis[0],\
            LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u16ActualDis[0],\
            Ls16ObjDisSub1_2,Ls16ObjDisSub1_3);
    }
#endif
}

/********************************************************以下是动态场景的处理逻辑 2023-09-13********************************/


/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForMiddleMasterMoveSearch
 * 
 * 功能描述: 中间探头主发距离在车辆运动状态下的匹配查询策略
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应主发探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-13 19:40   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForMiddleMasterMoveSearch(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SysDisInfoType *LpstrSysDisInfo;
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    Sns_CycleType LenuCurIndex,LenuLastIndex,LenuLastBefIndex;
    uint8 Lu8SigGroupCnt1,Lu8SigGroupCnt2,Lu8SigGroupCnt3;
    sint16 Ls16ObjDisSub1_2,Ls16ObjDisSub1_3;
    sint16 Ls16ObjDisSub1_2_Abs,Ls16ObjDisSub1_3_Abs;
    sint16 Ls16CarMoveDis1_2,Ls16CarMoveDis1_3;
    sint16 Ls16ObjMoveCarMoveSub1_2,Ls16ObjMoveCarMoveSub1_3;
    sint16 Ls16ObjMoveDisSubLimit,Ls16ObjMoveCarMoveDisSubLimit;
    uint8 Lu8ObjValidFlag = 0;
    uint8 Lu8ObjValidCheckFlag = 0;
    LpstrSysDisInfo = &GstrSysDisInfo[LeGroup][LenuSnsCh];
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    LenuLastIndex = SnsSigGroupFollowGetLastIndex(LenuCurIndex);
    LenuLastBefIndex = SnsSigGroupFollowGetLastIndex(LenuLastIndex);

    if(Gu16CarSpdForSnsUse < 500)
    {
        Ls16ObjMoveDisSubLimit = MIDDLE_CORNER_MOVE_DIS_FOR_5KM;
        Ls16ObjMoveCarMoveDisSubLimit = MIDDLE_OBJ_CAR_MOVE_DIS_FOR_5KM;
    }
    else if(Gu16CarSpdForSnsUse < 1000)
    {
        Ls16ObjMoveDisSubLimit = MIDDLE_CORNER_MOVE_DIS_FOR_10KM;
        Ls16ObjMoveCarMoveDisSubLimit = MIDDLE_OBJ_CAR_MOVE_DIS_FOR_10KM;
    }
    else
    {
        Ls16ObjMoveDisSubLimit = MIDDLE_CORNER_MOVE_DIS_FOR_15KM;
        Ls16ObjMoveCarMoveDisSubLimit = MIDDLE_OBJ_CAR_MOVE_DIS_FOR_15KM;
    }
    
    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3_Abs = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjMoveCarMoveSub1_3 = FOLLOW_DIS_SUB_INVALID;

    /* Step1：计算车辆移动的距离 */
    /* 上个周期探测到本周期探测车辆移动的距离 */
    Ls16CarMoveDis1_2 = (sint16)LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub;
    /* 上上个周期探测到本周期探测车辆移动的距离 */
    Ls16CarMoveDis1_3 = (sint16)(LpstrSnsSigGroupDataCache->SysDataBuf[LenuLastIndex].fCarMoveDisSub + LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub);

    for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
    {
        Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjDisSub1_3_Abs = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjMoveCarMoveSub1_3 = FOLLOW_DIS_SUB_INVALID;
        
        /* 先用本轮和上一轮进行距离差值比较 */
        for(Lu8SigGroupCnt2 = 0; Lu8SigGroupCnt2 < LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u8SigGroupCnt; Lu8SigGroupCnt2++)
        {
            /** 比较本轮和上一轮的差值 */
            if((LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]!=SNS_INVALID_DIS))
            {
                Ls16ObjDisSub1_2 = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]-LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2];
                Ls16ObjDisSub1_2_Abs = ABS_VALUE(Ls16ObjDisSub1_2);

                if(Ls16ObjDisSub1_2_Abs < Ls16ObjMoveDisSubLimit)
                {
                    /* 计算障碍物移动的距离和车辆移动距离的差值 */
                    Ls16ObjMoveCarMoveSub1_2 = ABS(Ls16CarMoveDis1_2,Ls16ObjDisSub1_2);
                    if(Ls16ObjMoveCarMoveSub1_2 < Ls16ObjMoveCarMoveDisSubLimit)
                    {
                        break;
                    }
                    else
                    {
                        Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
                    }
                }
                else
                {
                    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
                    Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
                }
            }
        }
    
        /* 本轮数据和上上一轮数据做比较 */
        for(Lu8SigGroupCnt3 = 0; Lu8SigGroupCnt3 < LpstrSnsSigGroupDataCache->MasterBuf[LenuLastBefIndex].u8SigGroupCnt; Lu8SigGroupCnt3++)
        {
            if((LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->MasterBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]!=SNS_INVALID_DIS))
            {
                Ls16ObjDisSub1_3 = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]-LpstrSnsSigGroupDataCache->MasterBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3];
                Ls16ObjDisSub1_3_Abs = ABS_VALUE(Ls16ObjDisSub1_3);
                if(Ls16ObjDisSub1_3_Abs < (Ls16ObjMoveDisSubLimit*2))
                {
                    /* 计算障碍物移动的距离和车辆移动距离的差值 */
                    Ls16ObjMoveCarMoveSub1_3 = ABS(Ls16CarMoveDis1_3,Ls16ObjDisSub1_3);
                    if(Ls16ObjMoveCarMoveSub1_3 < Ls16ObjMoveCarMoveDisSubLimit)
                    {
                        break;
                    }
                    else
                    {
                        Ls16ObjMoveCarMoveSub1_3 = FOLLOW_DIS_SUB_INVALID;
                    }
                }
                else
                {
                    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
                    Ls16ObjDisSub1_3_Abs = FOLLOW_DIS_SUB_INVALID;
                }
            }
        }

        if((Ls16ObjMoveCarMoveSub1_2 != FOLLOW_DIS_SUB_INVALID)&&(Ls16ObjMoveCarMoveSub1_3 != FOLLOW_DIS_SUB_INVALID))
        {
            Lu8ObjValidFlag = VALID;
            if(Ls16ObjMoveCarMoveSub1_2 < MIDDLE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
            {
                if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                }
                else
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                }
            }
            else
            {
                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
            }
            break;
        }
        else if(Ls16ObjMoveCarMoveSub1_3 != FOLLOW_DIS_SUB_INVALID)
        {
            /* 只有本轮和上上轮匹配时，高速时，可以快速生成；低速时需要分探头探测距离和定扫频进行区分 */
            if(Gu16CarSpdForSnsUse > 500)
            {
#if 0
                if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < MIDDLE_HIGH_SPD_FAST_CONFIRM_DIS)
                {
                    if(Ls16ObjMoveCarMoveSub1_3 < MIDDLE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                    {
                        if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                        }
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                    }

                    Lu8ObjValidFlag = VALID;
                    break;
                }
#endif
            }
            else
            {
                if(LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType > PDC_SNS_MEAS_STD)
                {
                    if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] > MIDDLE_CHIRP_FAST_CONFIRM_DIS)
                    {
                        if(Ls16ObjMoveCarMoveSub1_3 < MIDDLE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                        {
                            if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                            }
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                        Lu8ObjValidFlag = VALID;
                        break;
                    }
                }
                else
                {
                    if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < MIDDLE_STANDARD_FAST_CONFIRM_DIS)
                    {
                        /* 做定扫确认 */
                        if((Ls16ObjDisSub1_2_Abs > Ls16ObjMoveDisSubLimit) && (Gu16CarSpdForSnsUse < 300))
                        {
                            break;
                        }

                        if(Ls16ObjMoveCarMoveSub1_3 < MIDDLE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                        {
                            if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                            }
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                
                        if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_HIGH_CONFIRDENCE_DIS)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_VERY_HIGH;
                        }
                        Lu8ObjValidFlag = VALID;
                        break;
                    }
                }
            }

        }
        else if(Ls16ObjMoveCarMoveSub1_2 != FOLLOW_DIS_SUB_INVALID)
        {
#if 0
            /* 对于中间雷达，车辆运动时，在高速时，需要快速生成Map DE ，快速确认障碍物，才采用此策略 */
            if(Gu16CarSpdForSnsUse > 500)
            {
                if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < MIDDLE_HIGH_SPD_FAST_CONFIRM_DIS)
                {
                    if(Ls16ObjMoveCarMoveSub1_2 < MIDDLE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                    {
                        if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                        }
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                    }

                    Lu8ObjValidFlag = VALID;
                    break;
                }
            }
#endif
        }
    }
    if(Lu8ObjValidFlag)
    {
        if(!LpstrSnsSigGroupDisFollow->u8DisFollowFlag)
        {
            Lu8ObjValidCheckFlag = 1;
        }
        else
        {
            /* 已经跟踪上的，再后续的比较中，只考虑近距离快速进入的，远离的等已经跟踪上的障碍物消失后再重新匹配 */
            if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < LpstrSnsSigGroupDisFollow->u16FollowDis)
            {
                Lu8ObjValidCheckFlag = 1;
            }
#if 0
            else
            {
                if(Ls16ObjDisSub1_2 !=FOLLOW_DIS_SUB_INVALID)
                {
                    if((Lu8SigGroupCnt1 == 0)&&(Lu8SigGroupCnt2 == 0))
                    {
                        Lu8ObjValidCheckFlag = 1;
                    }
                }
                else if(Ls16ObjDisSub1_3 !=FOLLOW_DIS_SUB_INVALID)
                {
                    if((Lu8SigGroupCnt1 == 0)&&(Lu8SigGroupCnt3 == 0))
                    {
                        Lu8ObjValidCheckFlag = 1;
                    }
                }
            }
#endif
        }
        if (LpstrSysDisInfo->u8BlindKeepFlag)
        {
            Lu8ObjValidCheckFlag = 0;
        }
        if(Lu8ObjValidCheckFlag)
        {
            LpstrSnsSigGroupDisFollow->u8DisFollowFlag = VALID;
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
            LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
            
            LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
            LpstrSnsSigGroupDisFollow->u8BlindFollowCardir = SNS_CAR_INVALID;
            LpstrSnsSigGroupDisFollow->u8KeepBeepFlag = 0;
        }
    }
}

/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForMiddleLeftMoveSearch
 * 
 * 功能描述: 中间探头左侦听距离在车辆运动状态下的匹配查询策略
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应左侦听探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-13 19:40   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForMiddleLeftMoveSearch(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SysDisInfoType *LpstrSysDisInfo;
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    Sns_CycleType LenuCurIndex,LenuLastIndex,LenuLastBefIndex;
    uint8 Lu8SigGroupCnt1,Lu8SigGroupCnt2,Lu8SigGroupCnt3;
    sint16 Ls16ObjDisSub1_2,Ls16ObjDisSub1_3;
    sint16 Ls16ObjDisSub1_2_Abs,Ls16ObjDisSub1_3_Abs;
    sint16 Ls16CarMoveDis1_2,Ls16CarMoveDis1_3;
    sint16 Ls16ObjMoveCarMoveSub1_2,Ls16ObjMoveCarMoveSub1_3;
    sint16 Ls16ObjMoveDisSubLimit,Ls16ObjMoveCarMoveDisSubLimit;
    uint8 Lu8ObjValidFlag = 0;
    uint8 Lu8ObjValidCheckFlag = 0;
    LpstrSysDisInfo = &GstrSysDisInfo[LeGroup][LenuSnsCh];
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    LenuLastIndex = SnsSigGroupFollowGetLastIndex(LenuCurIndex);
    LenuLastBefIndex = SnsSigGroupFollowGetLastIndex(LenuLastIndex);

    if(Gu16CarSpdForSnsUse < 500)
    {
        Ls16ObjMoveDisSubLimit = MIDDLE_CORNER_MOVE_DIS_FOR_5KM;
        Ls16ObjMoveCarMoveDisSubLimit = MIDDLE_OBJ_CAR_MOVE_DIS_FOR_5KM;
    }
    else if(Gu16CarSpdForSnsUse < 1000)
    {
        Ls16ObjMoveDisSubLimit = MIDDLE_CORNER_MOVE_DIS_FOR_10KM;
        Ls16ObjMoveCarMoveDisSubLimit = MIDDLE_OBJ_CAR_MOVE_DIS_FOR_10KM;
    }
    else
    {
        Ls16ObjMoveDisSubLimit = MIDDLE_CORNER_MOVE_DIS_FOR_15KM;
        Ls16ObjMoveCarMoveDisSubLimit = MIDDLE_OBJ_CAR_MOVE_DIS_FOR_15KM;
    }
    
    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3_Abs = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjMoveCarMoveSub1_3 = FOLLOW_DIS_SUB_INVALID;

    /* Step1：计算车辆移动的距离 */
    /* 上个周期探测到本周期探测车辆移动的距离 */
    Ls16CarMoveDis1_2 = (sint16)LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub;
    /* 上上个周期探测到本周期探测车辆移动的距离 */
    Ls16CarMoveDis1_3 = (sint16)(LpstrSnsSigGroupDataCache->SysDataBuf[LenuLastIndex].fCarMoveDisSub + LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub);

    for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
    {
        Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjDisSub1_3_Abs = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjMoveCarMoveSub1_3 = FOLLOW_DIS_SUB_INVALID;
        
        /* 先用本轮和上一轮进行距离差值比较 */
        for(Lu8SigGroupCnt2 = 0; Lu8SigGroupCnt2 < LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u8SigGroupCnt; Lu8SigGroupCnt2++)
        {
            /** 比较本轮和上一轮的差值 */
            if((LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]!=SNS_INVALID_DIS))
            {
                Ls16ObjDisSub1_2 = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]-LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2];
                Ls16ObjDisSub1_2_Abs = ABS_VALUE(Ls16ObjDisSub1_2);

                if(Ls16ObjDisSub1_2_Abs < Ls16ObjMoveDisSubLimit)
                {
                    /* 计算障碍物移动的距离和车辆移动距离的差值 */
                    Ls16ObjMoveCarMoveSub1_2 = ABS(Ls16CarMoveDis1_2,Ls16ObjDisSub1_2);
                    if(Ls16ObjMoveCarMoveSub1_2 < Ls16ObjMoveCarMoveDisSubLimit)
                    {
                        break;
                    }
                    else
                    {
                        Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
                    }
                }
                else
                {
                    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
                    Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
                }
            }
        }
    
        /* 本轮数据和上上一轮数据做比较 */
        for(Lu8SigGroupCnt3 = 0; Lu8SigGroupCnt3 < LpstrSnsSigGroupDataCache->LeftBuf[LenuLastBefIndex].u8SigGroupCnt; Lu8SigGroupCnt3++)
        {
            if((LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->LeftBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]!=SNS_INVALID_DIS))
            {
                Ls16ObjDisSub1_3 = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]-LpstrSnsSigGroupDataCache->LeftBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3];
                Ls16ObjDisSub1_3_Abs = ABS_VALUE(Ls16ObjDisSub1_3);
                if(Ls16ObjDisSub1_3_Abs < (Ls16ObjMoveDisSubLimit*2))
                {
                    /* 计算障碍物移动的距离和车辆移动距离的差值 */
                    Ls16ObjMoveCarMoveSub1_3 = ABS(Ls16CarMoveDis1_3,Ls16ObjDisSub1_3);
                    if(Ls16ObjMoveCarMoveSub1_3 < Ls16ObjMoveCarMoveDisSubLimit)
                    {
                        break;
                    }
                    else
                    {
                        Ls16ObjMoveCarMoveSub1_3 = FOLLOW_DIS_SUB_INVALID;
                    }
                }
                else
                {
                    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
                    Ls16ObjDisSub1_3_Abs = FOLLOW_DIS_SUB_INVALID;
                }
            }
        }

        if((Ls16ObjMoveCarMoveSub1_2 != FOLLOW_DIS_SUB_INVALID)&&(Ls16ObjMoveCarMoveSub1_3 != FOLLOW_DIS_SUB_INVALID))
        {
            Lu8ObjValidFlag = VALID;
            if(Ls16ObjMoveCarMoveSub1_2 < MIDDLE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
            {
                if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                }
                else
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                }
            }
            else
            {
                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
            }
            break;
        }
        else if(Ls16ObjMoveCarMoveSub1_3 != FOLLOW_DIS_SUB_INVALID)
        {
            /* 只有本轮和上上轮匹配时，高速时，可以快速生成；低速时需要分探头探测距离和定扫频进行区分 */
            if(Gu16CarSpdForSnsUse > 500)
            {
#if 0
                if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < MIDDLE_HIGH_SPD_FAST_CONFIRM_DIS)
                {
                    if(Ls16ObjMoveCarMoveSub1_3 < MIDDLE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                    {
                        if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                        }
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                    }

                    Lu8ObjValidFlag = VALID;
                    break;
                }
#endif
            }
            else
            {
                if(LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType > PDC_SNS_MEAS_STD)
                {
                    if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] > MIDDLE_CHIRP_FAST_CONFIRM_DIS)
                    {
                        if(Ls16ObjMoveCarMoveSub1_3 < MIDDLE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                        {
                            if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                            }
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                        Lu8ObjValidFlag = VALID;
                        break;
                    }
                }
                else
                {
                    if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < MIDDLE_STANDARD_FAST_CONFIRM_DIS)
                    {
                        /* 做定扫确认 */
                        if((Ls16ObjDisSub1_2_Abs > Ls16ObjMoveDisSubLimit) && (Gu16CarSpdForSnsUse < 300))
                        {
                            break;
                        }

                        if(Ls16ObjMoveCarMoveSub1_3 < MIDDLE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                        {
                            if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                            }
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                
                        if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_HIGH_CONFIRDENCE_DIS)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_VERY_HIGH;
                        }
                        Lu8ObjValidFlag = VALID;
                        break;
                    }
                }
            }

        }
        else if(Ls16ObjMoveCarMoveSub1_2 != FOLLOW_DIS_SUB_INVALID)
        {
#if 0
            /* 对于中间雷达，车辆运动时，在高速时，需要快速生成Map DE ，快速确认障碍物，才采用此策略 */
            if(Gu16CarSpdForSnsUse > 500)
            {
                if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < MIDDLE_HIGH_SPD_FAST_CONFIRM_DIS)
                {
                    if(Ls16ObjMoveCarMoveSub1_2 < MIDDLE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                    {
                        if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                        }
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                    }

                    Lu8ObjValidFlag = VALID;
                    break;
                }
            }
#endif
        }
    }
    if(Lu8ObjValidFlag)
    {
        if(!LpstrSnsSigGroupDisFollow->u8DisFollowFlag)
        {
            Lu8ObjValidCheckFlag = 1;
        }
        else
        {
            /* 已经跟踪上的，再后续的比较中，只考虑近距离快速进入的，远离的等已经跟踪上的障碍物消失后再重新匹配 */
            if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < LpstrSnsSigGroupDisFollow->u16FollowDis)
            {
                Lu8ObjValidCheckFlag = 1;
            }
#if 0
            else
            {
                if(Ls16ObjDisSub1_2 !=FOLLOW_DIS_SUB_INVALID)
                {
                    if((Lu8SigGroupCnt1 == 0)&&(Lu8SigGroupCnt2 == 0))
                    {
                        Lu8ObjValidCheckFlag = 1;
                    }
                }
                else if(Ls16ObjDisSub1_3 !=FOLLOW_DIS_SUB_INVALID)
                {
                    if((Lu8SigGroupCnt1 == 0)&&(Lu8SigGroupCnt3 == 0))
                    {
                        Lu8ObjValidCheckFlag = 1;
                    }
                }
            }
#endif
        }
        if (LpstrSysDisInfo->u8BlindKeepFlag)
        {
            Lu8ObjValidCheckFlag = 0;
        }
        if(Lu8ObjValidCheckFlag)
        {
            LpstrSnsSigGroupDisFollow->u8DisFollowFlag = VALID;
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
            LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
            
            LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
            LpstrSnsSigGroupDisFollow->u8BlindFollowCardir = SNS_CAR_INVALID;
            LpstrSnsSigGroupDisFollow->u8KeepBeepFlag = 0;
        }
    }
}


/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForMiddleRightMoveSearch
 * 
 * 功能描述: 中间探头右侦听距离在车辆运动状态下的匹配查询策略
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应右侦听探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-13 19:40   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForMiddleRightMoveSearch(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SysDisInfoType *LpstrSysDisInfo;
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    Sns_CycleType LenuCurIndex,LenuLastIndex,LenuLastBefIndex;
    uint8 Lu8SigGroupCnt1,Lu8SigGroupCnt2,Lu8SigGroupCnt3;
    sint16 Ls16ObjDisSub1_2,Ls16ObjDisSub1_3;
    sint16 Ls16ObjDisSub1_2_Abs,Ls16ObjDisSub1_3_Abs;
    sint16 Ls16CarMoveDis1_2,Ls16CarMoveDis1_3;
    sint16 Ls16ObjMoveCarMoveSub1_2,Ls16ObjMoveCarMoveSub1_3;
    sint16 Ls16ObjMoveDisSubLimit,Ls16ObjMoveCarMoveDisSubLimit;
    uint8 Lu8ObjValidFlag = 0;
    uint8 Lu8ObjValidCheckFlag = 0;
    LpstrSysDisInfo = &GstrSysDisInfo[LeGroup][LenuSnsCh];
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    LenuLastIndex = SnsSigGroupFollowGetLastIndex(LenuCurIndex);
    LenuLastBefIndex = SnsSigGroupFollowGetLastIndex(LenuLastIndex);

    if(Gu16CarSpdForSnsUse < 500)
    {
        Ls16ObjMoveDisSubLimit = MIDDLE_CORNER_MOVE_DIS_FOR_5KM;
        Ls16ObjMoveCarMoveDisSubLimit = MIDDLE_OBJ_CAR_MOVE_DIS_FOR_5KM;
    }
    else if(Gu16CarSpdForSnsUse < 1000)
    {
        Ls16ObjMoveDisSubLimit = MIDDLE_CORNER_MOVE_DIS_FOR_10KM;
        Ls16ObjMoveCarMoveDisSubLimit = MIDDLE_OBJ_CAR_MOVE_DIS_FOR_10KM;
    }
    else
    {
        Ls16ObjMoveDisSubLimit = MIDDLE_CORNER_MOVE_DIS_FOR_15KM;
        Ls16ObjMoveCarMoveDisSubLimit = MIDDLE_OBJ_CAR_MOVE_DIS_FOR_15KM;
    }
    
    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3_Abs = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjMoveCarMoveSub1_3 = FOLLOW_DIS_SUB_INVALID;

    /* Step1：计算车辆移动的距离 */
    /* 上个周期探测到本周期探测车辆移动的距离 */
    Ls16CarMoveDis1_2 = (sint16)LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub;
    /* 上上个周期探测到本周期探测车辆移动的距离 */
    Ls16CarMoveDis1_3 = (sint16)(LpstrSnsSigGroupDataCache->SysDataBuf[LenuLastIndex].fCarMoveDisSub + LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub);

    for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
    {
        Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjDisSub1_3_Abs = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjMoveCarMoveSub1_3 = FOLLOW_DIS_SUB_INVALID;
        
        /* 先用本轮和上一轮进行距离差值比较 */
        for(Lu8SigGroupCnt2 = 0; Lu8SigGroupCnt2 < LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u8SigGroupCnt; Lu8SigGroupCnt2++)
        {
            /** 比较本轮和上一轮的差值 */
            if((LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]!=SNS_INVALID_DIS))
            {
                Ls16ObjDisSub1_2 = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]-LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2];
                Ls16ObjDisSub1_2_Abs = ABS_VALUE(Ls16ObjDisSub1_2);

                if(Ls16ObjDisSub1_2_Abs < Ls16ObjMoveDisSubLimit)
                {
                    /* 计算障碍物移动的距离和车辆移动距离的差值 */
                    Ls16ObjMoveCarMoveSub1_2 = ABS(Ls16CarMoveDis1_2,Ls16ObjDisSub1_2);
                    if(Ls16ObjMoveCarMoveSub1_2 < Ls16ObjMoveCarMoveDisSubLimit)
                    {
                        break;
                    }
                    else
                    {
                        Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
                    }
                }
                else
                {
                    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
                    Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
                }
            }
        }
    
        /* 本轮数据和上上一轮数据做比较 */
        for(Lu8SigGroupCnt3 = 0; Lu8SigGroupCnt3 < LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u8SigGroupCnt; Lu8SigGroupCnt3++)
        {
            if((LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]!=SNS_INVALID_DIS))
            {
                Ls16ObjDisSub1_3 = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]-LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3];
                Ls16ObjDisSub1_3_Abs = ABS_VALUE(Ls16ObjDisSub1_3);
                if(Ls16ObjDisSub1_3_Abs < (Ls16ObjMoveDisSubLimit*2))
                {
                    /* 计算障碍物移动的距离和车辆移动距离的差值 */
                    Ls16ObjMoveCarMoveSub1_3 = ABS(Ls16CarMoveDis1_3,Ls16ObjDisSub1_3);
                    if(Ls16ObjMoveCarMoveSub1_3 < Ls16ObjMoveCarMoveDisSubLimit)
                    {
                        break;
                    }
                    else
                    {
                        Ls16ObjMoveCarMoveSub1_3 = FOLLOW_DIS_SUB_INVALID;
                    }
                }
                else
                {
                    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
                    Ls16ObjDisSub1_3_Abs = FOLLOW_DIS_SUB_INVALID;
                }
            }
        }

        if((Ls16ObjMoveCarMoveSub1_2 != FOLLOW_DIS_SUB_INVALID)&&(Ls16ObjMoveCarMoveSub1_3 != FOLLOW_DIS_SUB_INVALID))
        {
            Lu8ObjValidFlag = VALID;
            if(Ls16ObjMoveCarMoveSub1_2 < MIDDLE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
            {
                if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                }
                else
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                }
            }
            else
            {
                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
            }
            break;
        }
        else if(Ls16ObjMoveCarMoveSub1_3 != FOLLOW_DIS_SUB_INVALID)
        {
            /* 只有本轮和上上轮匹配时，高速时，可以快速生成；低速时需要分探头探测距离和定扫频进行区分 */
            if(Gu16CarSpdForSnsUse > 500)
            {
#if 0
                if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < MIDDLE_HIGH_SPD_FAST_CONFIRM_DIS)
                {
                    if(Ls16ObjMoveCarMoveSub1_3 < MIDDLE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                    {
                        if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                        }
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                    }

                    Lu8ObjValidFlag = VALID;
                    break;
                }
#endif
            }
            else
            {
                if(LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType > PDC_SNS_MEAS_STD)
                {
                    if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] > MIDDLE_CHIRP_FAST_CONFIRM_DIS)
                    {
                        if(Ls16ObjMoveCarMoveSub1_3 < MIDDLE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                        {
                            if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                            }
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                        Lu8ObjValidFlag = VALID;
                        break;
                    }
                }
                else
                {
                    if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < MIDDLE_STANDARD_FAST_CONFIRM_DIS)
                    {
                        /* 做定扫确认 */
                        if((Ls16ObjDisSub1_2_Abs > Ls16ObjMoveDisSubLimit) && (Gu16CarSpdForSnsUse < 300))
                        {
                            break;
                        }

                        if(Ls16ObjMoveCarMoveSub1_3 < MIDDLE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                        {
                            if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                            }
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                
                        if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_HIGH_CONFIRDENCE_DIS)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_VERY_HIGH;
                        }
                        Lu8ObjValidFlag = VALID;
                        break;
                    }
                }
            }

        }
        else if(Ls16ObjMoveCarMoveSub1_2 != FOLLOW_DIS_SUB_INVALID)
        {
#if 0
            /* 对于中间雷达，车辆运动时，在高速时，需要快速生成Map DE ，快速确认障碍物，才采用此策略 */
            if(Gu16CarSpdForSnsUse > 500)
            {
                if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < MIDDLE_HIGH_SPD_FAST_CONFIRM_DIS)
                {
                    if(Ls16ObjMoveCarMoveSub1_2 < MIDDLE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                    {
                        if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                        }
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                    }

                    Lu8ObjValidFlag = VALID;
                    break;
                }
            }
#endif
        }
    }
    if(Lu8ObjValidFlag)
    {
        if(!LpstrSnsSigGroupDisFollow->u8DisFollowFlag)
        {
            Lu8ObjValidCheckFlag = 1;
        }
        else
        {
            /* 已经跟踪上的，再后续的比较中，只考虑近距离快速进入的，远离的等已经跟踪上的障碍物消失后再重新匹配 */
            if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < LpstrSnsSigGroupDisFollow->u16FollowDis)
            {
                Lu8ObjValidCheckFlag = 1;
            }
#if 0
            else
            {
                if(Ls16ObjDisSub1_2 !=FOLLOW_DIS_SUB_INVALID)
                {
                    if((Lu8SigGroupCnt1 == 0)&&(Lu8SigGroupCnt2 == 0))
                    {
                        Lu8ObjValidCheckFlag = 1;
                    }
                }
                else if(Ls16ObjDisSub1_3 !=FOLLOW_DIS_SUB_INVALID)
                {
                    if((Lu8SigGroupCnt1 == 0)&&(Lu8SigGroupCnt3 == 0))
                    {
                        Lu8ObjValidCheckFlag = 1;
                    }
                }
            }
#endif
        }
        if (LpstrSysDisInfo->u8BlindKeepFlag)
        {
            Lu8ObjValidCheckFlag = 0;
        }
        if(Lu8ObjValidCheckFlag)
        {
            LpstrSnsSigGroupDisFollow->u8DisFollowFlag = VALID;
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
            LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
            
            LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
            LpstrSnsSigGroupDisFollow->u8BlindFollowCardir = SNS_CAR_INVALID;
            LpstrSnsSigGroupDisFollow->u8KeepBeepFlag = 0;
        }
    }
#if 0
    if((GenuSnsGroup == 0x00)&&(GenuSnsCh == 0x03))
    {
        printf("FMR Right Search,Time:%.3f,FollowDis:%d,Flag:%d,NewDis:%d,LastDis:%d,LastBefDis:%d,Prob:%d\r\n",GfMessageTime,\
            LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDisFollow->u8DisFollowFlag,\
            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[0],LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u16ActualDis[0],\
            LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u16ActualDis[0],\
            LpstrSnsSigGroupDisFollow->enuFollowDisPro);
    }
#endif
}


/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForMiddleMasterMoveFollow
 * 
 * 功能描述: 中间探头主发距离在车辆运动状态下的跟踪策略
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应主发探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-13 20:44   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForMiddleMasterMoveFollow(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SysDisInfoType *LpstrSysDisInfo;
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    Sns_CycleType LenuCurIndex,LenuLastIndex,LenuLastBefIndex;
    uint8 Lu8SigGroupCnt1,Lu8SigGroupCnt2,Lu8SigGroupCnt3;
    sint16 Ls16ObjDisSub1_2,Ls16ObjDisSub1_3;
    sint16 Ls16CarMoveDis1_2,Ls16CarMoveDis1_3;
    sint16 Ls16ObjMoveCarMoveSub1_2,Ls16ObjMoveCarMoveSub1_3;
    sint16 Ls16ObjMoveDisSubLimit,Ls16ObjMoveCarMoveDisSubLimit;
    sint16 Ls16CarMoveLastDetectToNow,Ls16ObjDisByFollow;
    sint16 Ls16Follow_DetectDisSub;
    uint8 Lu8CornerObjParallelFlag = 0;
    LpstrSysDisInfo = &GstrSysDisInfo[LeGroup][LenuSnsCh];
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    LenuLastIndex = SnsSigGroupFollowGetLastIndex(LenuCurIndex);
    LenuLastBefIndex = SnsSigGroupFollowGetLastIndex(LenuLastIndex);

    if(Gu16CarSpdForSnsUse < 500)
    {
        Ls16ObjMoveDisSubLimit = MIDDLE_CORNER_MOVE_DIS_FOR_5KM;
        Ls16ObjMoveCarMoveDisSubLimit = MIDDLE_OBJ_CAR_MOVE_DIS_FOR_5KM;
    }
    else if(Gu16CarSpdForSnsUse < 1000)
    {
        Ls16ObjMoveDisSubLimit = MIDDLE_CORNER_MOVE_DIS_FOR_10KM;
        Ls16ObjMoveCarMoveDisSubLimit = MIDDLE_OBJ_CAR_MOVE_DIS_FOR_10KM;
    }
    else
    {
        Ls16ObjMoveDisSubLimit = MIDDLE_CORNER_MOVE_DIS_FOR_15KM;
        Ls16ObjMoveCarMoveDisSubLimit = MIDDLE_OBJ_CAR_MOVE_DIS_FOR_15KM;
    }
    
    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjMoveCarMoveSub1_3 = FOLLOW_DIS_SUB_INVALID;

    /* Step1：计算上一次探测更新到本周期车辆移动的距离；根据车辆移动的距离推算出本周期的预估距离 */
    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow += LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub;
    Ls16CarMoveLastDetectToNow = (sint16)LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow;

    Ls16ObjDisByFollow = FOLLOW_DIS_SUB_INVALID;

    if(Ls16CarMoveLastDetectToNow < 0)
    {
        if(LpstrSnsSigGroupDisFollow->u16FollowDis < ABS_VALUE(Ls16CarMoveLastDetectToNow))
        {
            /* 防止跟踪错误，跟踪距离比待减去的距离还小，直接退出跟踪 */
            LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
            LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
            LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
            LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
            LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
            LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 0;
            LpstrSnsSigGroupDisFollow->u8BlindFollowFlag = 0;
        }
        else
        {
            Ls16ObjDisByFollow = (sint16)LpstrSnsSigGroupDisFollow->u16DisDetect_Last + Ls16CarMoveLastDetectToNow;
        }
    }
    else
    {
        Ls16ObjDisByFollow = (sint16)LpstrSnsSigGroupDisFollow->u16DisDetect_Last + Ls16CarMoveLastDetectToNow;
    }
    if(LpstrSnsSigGroupDisFollow->u16FollowDis != PDC_OBJ_FOLLOW_INVALID_DIS)
    {
        for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
        {
            Ls16Follow_DetectDisSub = FOLLOW_DIS_SUB_INVALID;
            if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)
            {
                Ls16Follow_DetectDisSub = ABS(Ls16ObjDisByFollow,LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]);
            }
            if(Ls16Follow_DetectDisSub < Ls16ObjMoveCarMoveDisSubLimit)
            {
                /* 预估距离和探测距离差值满足跟踪条件，直接更新最新的距离 */
                LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                
                LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                
                LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];;
                LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                break;
            }
        }
        if(Lu8SigGroupCnt1 == LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8SigGroupCnt)
        {
            uint8 Lu8ExitCntLimit;
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt++;
            
            /* 障碍物靠经情况下且在50cm内无才无回波进行强跟踪 */
            if (LpstrSnsSigGroupDisFollow->u16FollowDis < MIDDLE_MOVE_BLIND_FOLLOW_DIS && LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt == 1 &&
                LpstrSysDisInfo->u8MoveSta && LpstrSysDisInfo->u16MoveStaCnt > 2)
            {
                LpstrSnsSigGroupDisFollow->u8BlindFollowFlag = 1;
                LpstrSnsSigGroupDisFollow->u8BlindFollowCardir = LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eCarDir;
            }

            SnsFollowDisProSub(&LpstrSnsSigGroupDisFollow->enuFollowDisPro,LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType);
            if(LenuSnsCh == PDC_SNS_CH_FML_RML)
            {
                Lu8CornerObjParallelFlag = GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh-1].u8ObjParallelWallFlag;
            }
            else
            {
                Lu8CornerObjParallelFlag = GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh+1].u8ObjParallelWallFlag;
            }
            if((Lu8CornerObjParallelFlag)&&(LpstrSnsSigGroupDisFollow->u16FollowDis > MIDDLE_OBJ_USE_PARALLEL_DIS))
            {
                Lu8ExitCntLimit = MIDDLE_WALL_FAST_EXIT_CNT;

            }
            else
            {
                Lu8ExitCntLimit = MIDDLE_FOLLOW_EXIT_CNT;
                LpstrSnsSigGroupDisFollow->u16FollowDis = Ls16ObjDisByFollow;   /* 非平行墙场景才使用预估距离进行更新 */
            }

            if(LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt > Lu8ExitCntLimit)
            {
                /* 盲区强制跟踪 */
                if ((LpstrSnsSigGroupDisFollow->u8BlindFollowFlag == 1) && (LpstrSnsSigGroupDisFollow->u16FollowDis > 250) &&
                    (LpstrSnsSigGroupDisFollow->u8BlindFollowCardir == LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eCarDir))
                {

                }
                else
                {
                    /* 特殊处理争对50cm内无回波的障碍物在后续处理中能保持长鸣 */
                    if (LpstrSnsSigGroupDisFollow->u8BlindFollowFlag && LpstrSnsSigGroupDisFollow->u8BlindFollowCardir == LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eCarDir)
                    {
                        LpstrSnsSigGroupDisFollow->u8KeepBeepFlag = 1;
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->u8KeepBeepFlag = 0;
                    }
                    /** @brief: 退出跟踪 */
                    LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
                    LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                    LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
                    LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
                    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                    LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
                    LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 0;
                    LpstrSnsSigGroupDisFollow->u8BlindFollowFlag = 0;
                    LpstrSnsSigGroupDisFollow->u8BlindFollowCardir = SNS_CAR_INVALID;
                }
            }

            if(Lu8SigGroupCnt1 != 0)
            {
                SnsSigGroupDisFollowForMiddleMasterMoveSearch(LeGroup,LenuSnsCh,LpstrSnsSigGroupDisFollow);
            }
        }
        else if(Lu8SigGroupCnt1 > 0)
        {
            SnsSigGroupDisFollowForMiddleMasterMoveSearch(LeGroup,LenuSnsCh,LpstrSnsSigGroupDisFollow);
        }
    }
}

/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForMiddleLeftMoveFollow
 * 
 * 功能描述: 中间探头左侦听距离在车辆运动状态下的跟踪策略
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应主发探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-13 20:44   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForMiddleLeftMoveFollow(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SysDisInfoType *LpstrSysDisInfo;
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    Sns_CycleType LenuCurIndex,LenuLastIndex,LenuLastBefIndex;
    uint8 Lu8SigGroupCnt1,Lu8SigGroupCnt2,Lu8SigGroupCnt3;
    sint16 Ls16ObjDisSub1_2,Ls16ObjDisSub1_3;
    sint16 Ls16CarMoveDis1_2,Ls16CarMoveDis1_3;
    sint16 Ls16ObjMoveCarMoveSub1_2,Ls16ObjMoveCarMoveSub1_3;
    sint16 Ls16ObjMoveDisSubLimit,Ls16ObjMoveCarMoveDisSubLimit;
    sint16 Ls16CarMoveLastDetectToNow,Ls16ObjDisByFollow;
    sint16 Ls16Follow_DetectDisSub;
    uint8 Lu8CornerObjParallelFlag = 0;
    LpstrSysDisInfo = &GstrSysDisInfo[LeGroup][LenuSnsCh];
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    LenuLastIndex = SnsSigGroupFollowGetLastIndex(LenuCurIndex);
    LenuLastBefIndex = SnsSigGroupFollowGetLastIndex(LenuLastIndex);

    if(Gu16CarSpdForSnsUse < 500)
    {
        Ls16ObjMoveDisSubLimit = MIDDLE_CORNER_MOVE_DIS_FOR_5KM;
        Ls16ObjMoveCarMoveDisSubLimit = MIDDLE_OBJ_CAR_MOVE_DIS_FOR_5KM;
    }
    else if(Gu16CarSpdForSnsUse < 1000)
    {
        Ls16ObjMoveDisSubLimit = MIDDLE_CORNER_MOVE_DIS_FOR_10KM;
        Ls16ObjMoveCarMoveDisSubLimit = MIDDLE_OBJ_CAR_MOVE_DIS_FOR_10KM;
    }
    else
    {
        Ls16ObjMoveDisSubLimit = MIDDLE_CORNER_MOVE_DIS_FOR_15KM;
        Ls16ObjMoveCarMoveDisSubLimit = MIDDLE_OBJ_CAR_MOVE_DIS_FOR_15KM;
    }
    
    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjMoveCarMoveSub1_3 = FOLLOW_DIS_SUB_INVALID;

    /* Step1：计算上一次探测更新到本周期车辆移动的距离；根据车辆移动的距离推算出本周期的预估距离 */
    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow += LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub;
    Ls16CarMoveLastDetectToNow = (sint16)LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow;

    Ls16ObjDisByFollow = FOLLOW_DIS_SUB_INVALID;

    if(Ls16CarMoveLastDetectToNow < 0)
    {
        if(LpstrSnsSigGroupDisFollow->u16FollowDis < ABS_VALUE(Ls16CarMoveLastDetectToNow))
        {
            /* 防止跟踪错误，跟踪距离比待减去的距离还小，直接退出跟踪 */
            LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
            LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
            LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
            LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
            LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
            LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 0;
            LpstrSnsSigGroupDisFollow->u8BlindFollowFlag = 0;
        }
        else
        {
            Ls16ObjDisByFollow = (sint16)LpstrSnsSigGroupDisFollow->u16DisDetect_Last + Ls16CarMoveLastDetectToNow;
        }
    }
    else
    {
        Ls16ObjDisByFollow = (sint16)LpstrSnsSigGroupDisFollow->u16DisDetect_Last + Ls16CarMoveLastDetectToNow;
    }
    if(LpstrSnsSigGroupDisFollow->u16FollowDis != PDC_OBJ_FOLLOW_INVALID_DIS)
    {
        for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
        {
            Ls16Follow_DetectDisSub = FOLLOW_DIS_SUB_INVALID;
            if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)
            {
                Ls16Follow_DetectDisSub = ABS(Ls16ObjDisByFollow,LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]);
            }
            if(Ls16Follow_DetectDisSub < Ls16ObjMoveCarMoveDisSubLimit)
            {
                /* 预估距离和探测距离差值满足跟踪条件，直接更新最新的距离 */
                LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                
                LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                
                LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];;
                LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                break;
            }
        }
        if(Lu8SigGroupCnt1 == LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8SigGroupCnt)
        {
            uint8 Lu8ExitCntLimit;
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt++;

            /* 障碍物靠经情况下且在50cm内无才无回波进行强跟踪 */
            if (LpstrSnsSigGroupDisFollow->u16FollowDis < MIDDLE_MOVE_BLIND_FOLLOW_DIS && LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt == 1 &&
                LpstrSysDisInfo->u8MoveSta && LpstrSysDisInfo->u16MoveStaCnt > 2)
            {
                LpstrSnsSigGroupDisFollow->u8BlindFollowFlag = 1;
                LpstrSnsSigGroupDisFollow->u8BlindFollowCardir = LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eCarDir;
            }
            
            SnsFollowDisProSub(&LpstrSnsSigGroupDisFollow->enuFollowDisPro,LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType);
            if(LenuSnsCh == PDC_SNS_CH_FML_RML)
            {
                Lu8CornerObjParallelFlag = GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh-1].u8ObjParallelWallFlag;
            }
            else
            {
                Lu8CornerObjParallelFlag = GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh+1].u8ObjParallelWallFlag;
            }
            if((Lu8CornerObjParallelFlag)&&(LpstrSnsSigGroupDisFollow->u16FollowDis > MIDDLE_OBJ_USE_PARALLEL_DIS))
            {
                Lu8ExitCntLimit = MIDDLE_WALL_FAST_EXIT_CNT;

            }
            else
            {
                Lu8ExitCntLimit = MIDDLE_FOLLOW_EXIT_CNT;
                LpstrSnsSigGroupDisFollow->u16FollowDis = Ls16ObjDisByFollow;   /* 非平行墙场景才使用预估距离进行更新 */
            }

            if(LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt > Lu8ExitCntLimit)
            {
                /* 盲区强制跟踪 */
                if ((LpstrSnsSigGroupDisFollow->u8BlindFollowFlag == 1) && (LpstrSnsSigGroupDisFollow->u16FollowDis > 250) &&
                    (LpstrSnsSigGroupDisFollow->u8BlindFollowCardir == LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eCarDir))
                {

                }
                else
                {
                    /** @brief: 退出跟踪 */
                    LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
                    LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                    LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
                    LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
                    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                    LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
                    LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 0;
                    LpstrSnsSigGroupDisFollow->u8BlindFollowFlag = 0;
                    LpstrSnsSigGroupDisFollow->u8BlindFollowCardir = SNS_CAR_INVALID;
                }
            }

            if(Lu8SigGroupCnt1 != 0)
            {
                SnsSigGroupDisFollowForMiddleLeftMoveSearch(LeGroup,LenuSnsCh,LpstrSnsSigGroupDisFollow);
            }
        }
        else if(Lu8SigGroupCnt1 > 0)
        {
            SnsSigGroupDisFollowForMiddleLeftMoveSearch(LeGroup,LenuSnsCh,LpstrSnsSigGroupDisFollow);
        }
    }
}


/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForMiddleRightMoveFollow
 * 
 * 功能描述: 中间探头右侦听距离在车辆运动状态下的跟踪策略
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应主发探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-13 20:44   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForMiddleRightMoveFollow(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SysDisInfoType *LpstrSysDisInfo;
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    Sns_CycleType LenuCurIndex,LenuLastIndex,LenuLastBefIndex;
    uint8 Lu8SigGroupCnt1,Lu8SigGroupCnt2,Lu8SigGroupCnt3;
    sint16 Ls16ObjDisSub1_2,Ls16ObjDisSub1_3;
    sint16 Ls16CarMoveDis1_2,Ls16CarMoveDis1_3;
    sint16 Ls16ObjMoveCarMoveSub1_2,Ls16ObjMoveCarMoveSub1_3;
    sint16 Ls16ObjMoveDisSubLimit,Ls16ObjMoveCarMoveDisSubLimit;
    sint16 Ls16CarMoveLastDetectToNow,Ls16ObjDisByFollow;
    sint16 Ls16Follow_DetectDisSub;
    uint8 Lu8CornerObjParallelFlag = 0;
    LpstrSysDisInfo = &GstrSysDisInfo[LeGroup][LenuSnsCh];
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    LenuLastIndex = SnsSigGroupFollowGetLastIndex(LenuCurIndex);
    LenuLastBefIndex = SnsSigGroupFollowGetLastIndex(LenuLastIndex);
    
    if(Gu16CarSpdForSnsUse < 500)
    {
        Ls16ObjMoveDisSubLimit = MIDDLE_CORNER_MOVE_DIS_FOR_5KM;
        Ls16ObjMoveCarMoveDisSubLimit = MIDDLE_OBJ_CAR_MOVE_DIS_FOR_5KM;
    }
    else if(Gu16CarSpdForSnsUse < 1000)
    {
        Ls16ObjMoveDisSubLimit = MIDDLE_CORNER_MOVE_DIS_FOR_10KM;
        Ls16ObjMoveCarMoveDisSubLimit = MIDDLE_OBJ_CAR_MOVE_DIS_FOR_10KM;
    }
    else
    {
        Ls16ObjMoveDisSubLimit = MIDDLE_CORNER_MOVE_DIS_FOR_15KM;
        Ls16ObjMoveCarMoveDisSubLimit = MIDDLE_OBJ_CAR_MOVE_DIS_FOR_15KM;
    }
    
    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjMoveCarMoveSub1_3 = FOLLOW_DIS_SUB_INVALID;

    /* Step1：计算上一次探测更新到本周期车辆移动的距离；根据车辆移动的距离推算出本周期的预估距离 */
    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow += LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub;
    Ls16CarMoveLastDetectToNow = (sint16)LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow;

    Ls16ObjDisByFollow = FOLLOW_DIS_SUB_INVALID;

    if(Ls16CarMoveLastDetectToNow < 0)
    {
        if(LpstrSnsSigGroupDisFollow->u16FollowDis < ABS_VALUE(Ls16CarMoveLastDetectToNow))
        {
            /* 防止跟踪错误，跟踪距离比待减去的距离还小，直接退出跟踪 */
            LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
            LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
            LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
            LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
            LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
            LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 0;
            LpstrSnsSigGroupDisFollow->u8BlindFollowFlag = 0;
        }
        else
        {
            Ls16ObjDisByFollow = (sint16)LpstrSnsSigGroupDisFollow->u16DisDetect_Last + Ls16CarMoveLastDetectToNow;
        }
    }
    else
    {
        Ls16ObjDisByFollow = (sint16)LpstrSnsSigGroupDisFollow->u16DisDetect_Last + Ls16CarMoveLastDetectToNow;
    }
    if(LpstrSnsSigGroupDisFollow->u16FollowDis != PDC_OBJ_FOLLOW_INVALID_DIS)
    {
        for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
        {
            Ls16Follow_DetectDisSub = FOLLOW_DIS_SUB_INVALID;
            if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)
            {
                Ls16Follow_DetectDisSub = ABS(Ls16ObjDisByFollow,LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]);
            }
            if(Ls16Follow_DetectDisSub < Ls16ObjMoveCarMoveDisSubLimit)
            {
                /* 预估距离和探测距离差值满足跟踪条件，直接更新最新的距离 */
                LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                
                LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                
                LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];;
                LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                break;
            }
        }
        if(Lu8SigGroupCnt1 == LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8SigGroupCnt)
        {
            uint8 Lu8ExitCntLimit;
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt++;

            /* 障碍物靠经情况下且在50cm内无才无回波进行强跟踪 */
            if (LpstrSnsSigGroupDisFollow->u16FollowDis < MIDDLE_MOVE_BLIND_FOLLOW_DIS && LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt == 1 &&
                LpstrSysDisInfo->u8MoveSta && LpstrSysDisInfo->u16MoveStaCnt > 2)
            {
                LpstrSnsSigGroupDisFollow->u8BlindFollowFlag = 1;
                LpstrSnsSigGroupDisFollow->u8BlindFollowCardir = LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eCarDir;
            }
            
            SnsFollowDisProSub(&LpstrSnsSigGroupDisFollow->enuFollowDisPro,LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType);
            if(LenuSnsCh == PDC_SNS_CH_FML_RML)
            {
                Lu8CornerObjParallelFlag = GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh-1].u8ObjParallelWallFlag;
            }
            else
            {
                Lu8CornerObjParallelFlag = GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh+1].u8ObjParallelWallFlag;
            }
            if((Lu8CornerObjParallelFlag)&&(LpstrSnsSigGroupDisFollow->u16FollowDis > MIDDLE_OBJ_USE_PARALLEL_DIS))
            {
                Lu8ExitCntLimit = MIDDLE_WALL_FAST_EXIT_CNT;

            }
            else
            {
                Lu8ExitCntLimit = MIDDLE_FOLLOW_EXIT_CNT;
                LpstrSnsSigGroupDisFollow->u16FollowDis = Ls16ObjDisByFollow;   /* 非平行墙场景才使用预估距离进行更新 */
            }

            if(LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt > Lu8ExitCntLimit)
            {
                /* 盲区强制跟踪 */
                if ((LpstrSnsSigGroupDisFollow->u8BlindFollowFlag == 1) && (LpstrSnsSigGroupDisFollow->u16FollowDis > 250) &&
                    (LpstrSnsSigGroupDisFollow->u8BlindFollowCardir == LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eCarDir))
                {

                }
                else
                {
                    /** @brief: 退出跟踪 */
                    LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
                    LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                    LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
                    LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
                    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                    LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
                    LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 0;
                    LpstrSnsSigGroupDisFollow->u8BlindFollowFlag = 0;
                    LpstrSnsSigGroupDisFollow->u8BlindFollowCardir = SNS_CAR_INVALID;
                }
            }

            if(Lu8SigGroupCnt1 != 0)
            {
                SnsSigGroupDisFollowForMiddleRightMoveSearch(LeGroup,LenuSnsCh,LpstrSnsSigGroupDisFollow);
            }
        }
        else if(Lu8SigGroupCnt1 > 0)
        {
            SnsSigGroupDisFollowForMiddleRightMoveSearch(LeGroup,LenuSnsCh,LpstrSnsSigGroupDisFollow);
        }
    }
#if 0
        if((GenuSnsGroup == 0x00)&&(GenuSnsCh == 0x03))
        {
            printf("FMR Right Follow,Time:%.3f,FollowDis:%d,Flag:%d,NewDis:%d,LastDis:%d,LastBefDis:%d,Prob:%d\r\n",GfMessageTime,\
                LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDisFollow->u8DisFollowFlag,\
                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[0],LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u16ActualDis[0],\
                LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u16ActualDis[0],\
                LpstrSnsSigGroupDisFollow->enuFollowDisPro);
        }
#endif
}



/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForCornerMasterMoveSearch
 * 
 * 功能描述: 边角探头主发距离在车辆运动状态下的匹配查询策略；主要修改平行墙场景的跟踪
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应主发探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-13 19:40   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForCornerMasterMoveSearch(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    Sns_CycleType LenuCurIndex,LenuLastIndex,LenuLastBefIndex;
    uint8 Lu8SigGroupCnt1,Lu8SigGroupCnt2,Lu8SigGroupCnt3;
    sint16 Ls16ObjDisSub1_2,Ls16ObjDisSub1_3;
    sint16 Ls16ObjDisSub1_2_Abs,Ls16ObjDisSub1_3_Abs;
    sint16 Ls16CarMoveDis1_2,Ls16CarMoveDis1_3;
    sint16 Ls16CarMoveDis1_2_Abs,Ls16CarMoveDis1_3_Abs;
    sint16 Ls16ObjMoveCarMoveSub1_2,Ls16ObjMoveCarMoveSub1_3;
    sint16 Ls16ObjMoveDisSubLimit,Ls16ObjMoveCarMoveDisSubLimit;
    uint8 Lu8ObjValidFlag = 0;
    uint8 Lu8ObjValidCheckFlag = 0;
    uint8 Lu8ParallelWallFlag1,Lu8ParallelWallFlag2;
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    LenuLastIndex = SnsSigGroupFollowGetLastIndex(LenuCurIndex);
    LenuLastBefIndex = SnsSigGroupFollowGetLastIndex(LenuLastIndex);

    if(Gu16CarSpdForSnsUse < 500)
    {
        Ls16ObjMoveDisSubLimit = CORNER_CORNER_MOVE_DIS_FOR_5KM;
        Ls16ObjMoveCarMoveDisSubLimit = CORNER_OBJ_CAR_MOVE_DIS_FOR_5KM;
    }
    else if(Gu16CarSpdForSnsUse < 1000)
    {
        Ls16ObjMoveDisSubLimit = CORNER_CORNER_MOVE_DIS_FOR_10KM;
        Ls16ObjMoveCarMoveDisSubLimit = CORNER_OBJ_CAR_MOVE_DIS_FOR_10KM;
    }
    else
    {
        Ls16ObjMoveDisSubLimit = CORNER_CORNER_MOVE_DIS_FOR_15KM;
        Ls16ObjMoveCarMoveDisSubLimit = CORNER_OBJ_CAR_MOVE_DIS_FOR_15KM;
    }
    
    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3_Abs = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjMoveCarMoveSub1_3 = FOLLOW_DIS_SUB_INVALID;
    Lu8ParallelWallFlag1 = 0;
    Lu8ParallelWallFlag2 = 0;

    /* Step1：计算车辆移动的距离 */
    /* 上个周期探测到本周期探测车辆移动的距离 */
    Ls16CarMoveDis1_2 = (sint16)LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub;
    Ls16CarMoveDis1_2_Abs = ABS_VALUE(Ls16CarMoveDis1_2);

    /* 上上个周期探测到本周期探测车辆移动的距离 */
    Ls16CarMoveDis1_3 = (sint16)(LpstrSnsSigGroupDataCache->SysDataBuf[LenuLastIndex].fCarMoveDisSub + LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub);
    Ls16CarMoveDis1_3_Abs = ABS_VALUE(Ls16CarMoveDis1_3);
    
    for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
    {
        Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjDisSub1_3_Abs = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjMoveCarMoveSub1_3 = FOLLOW_DIS_SUB_INVALID;
        Lu8ParallelWallFlag1 = 0;
        Lu8ParallelWallFlag2 = 0;
        
        /* 先用本轮和上一轮进行距离差值比较 */
        for(Lu8SigGroupCnt2 = 0; Lu8SigGroupCnt2 < LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u8SigGroupCnt; Lu8SigGroupCnt2++)
        {
            /** 比较本轮和上一轮的差值 */
            if((LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]!=SNS_INVALID_DIS))
            {
                Ls16ObjDisSub1_2 = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]-LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2];
                Ls16ObjDisSub1_2_Abs = ABS_VALUE(Ls16ObjDisSub1_2);
                if(Ls16ObjDisSub1_2_Abs < Ls16ObjMoveDisSubLimit)
                {
                    /* 计算障碍物移动的距离和车辆移动距离的差值 */
                    Ls16ObjMoveCarMoveSub1_2 = ABS(Ls16CarMoveDis1_2,Ls16ObjDisSub1_2);
                    
                    if((Ls16ObjMoveCarMoveSub1_2 < Ls16ObjMoveCarMoveDisSubLimit)||(Ls16ObjDisSub1_2_Abs < CORNER_OBJ_PARALLEL_WALL_DIS_SUB))
                    {
                        if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < CORNER_DIRECT_WALL_DETECT_DIS_MAX)
                        {
                            if((Ls16ObjDisSub1_2_Abs + (Ls16CarMoveDis1_2_Abs/2))< Ls16ObjMoveCarMoveSub1_2)
                            {
                                Lu8ParallelWallFlag1 = 1;
                            }
                            else if((Ls16ObjDisSub1_2_Abs < CORNER_OBJ_DIRECT_WALL_DIS_SUB)&&(Ls16CarMoveDis1_2_Abs > CORNER_DIRECT_WALL_CAR_DIS_LIMIT))
                            {
                                Lu8ParallelWallFlag1 = 1;
                            }
                        }
                        break;
                    }
                    else
                    {
                        Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
                        Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
                        Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
                    }
                }
                else
                {
                    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
                    Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
                    Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
                }
            }
        }
    
        /* 本轮数据和上上一轮数据做比较 */
        for(Lu8SigGroupCnt3 = 0; Lu8SigGroupCnt3 < LpstrSnsSigGroupDataCache->MasterBuf[LenuLastBefIndex].u8SigGroupCnt; Lu8SigGroupCnt3++)
        {
            if((LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->MasterBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]!=SNS_INVALID_DIS))
            {
                Ls16ObjDisSub1_3 = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]-LpstrSnsSigGroupDataCache->MasterBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3];
                Ls16ObjDisSub1_3_Abs = ABS_VALUE(Ls16ObjDisSub1_3);
                if(Ls16ObjDisSub1_3_Abs < (Ls16ObjMoveDisSubLimit*2))
                {
                    /* 计算障碍物移动的距离和车辆移动距离的差值 */
                    Ls16ObjMoveCarMoveSub1_3 = ABS(Ls16CarMoveDis1_3,Ls16ObjDisSub1_3);
                    if((Ls16ObjMoveCarMoveSub1_3 < Ls16ObjMoveCarMoveDisSubLimit)||(Ls16ObjDisSub1_3_Abs < CORNER_OBJ_PARALLEL_WALL_DIS_SUB))
                    {
                        if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < CORNER_DIRECT_WALL_DETECT_DIS_MAX)
                        {
                            if((Ls16ObjDisSub1_3_Abs + (Ls16CarMoveDis1_3_Abs/2))< Ls16ObjMoveCarMoveSub1_3)
                            {
                                Lu8ParallelWallFlag2 = 1;
                            }
                            else if((Ls16ObjDisSub1_3_Abs < CORNER_OBJ_DIRECT_WALL_DIS_SUB)&&(Ls16CarMoveDis1_3_Abs > CORNER_DIRECT_WALL_CAR_DIS_LIMIT))
                            {
                                Lu8ParallelWallFlag2 = 1;
                            }
                        }
                        break;
                    }
                    else
                    {
                        Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
                        Ls16ObjDisSub1_3_Abs = FOLLOW_DIS_SUB_INVALID;
                        Ls16ObjMoveCarMoveSub1_3 = FOLLOW_DIS_SUB_INVALID;
                    }
                }
                else
                {
                    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
                    Ls16ObjDisSub1_3_Abs = FOLLOW_DIS_SUB_INVALID;
                    Ls16ObjMoveCarMoveSub1_3 = FOLLOW_DIS_SUB_INVALID;
                }
            }
        }

#if 0
        if((GenuSnsGroup == 0x00)&&(GenuSnsCh == 0x04))
        {
            printf("FR Master Search,GroupCnt1:%d,Time:%.3f,NewDis:%d,LastDis:%d,LastBefDis:%d,Sub1_2:%d,Sub1_3:%d,Obj_CarSub1_2:%d,Obj_CarSub1_3:%d,CarDis1_2:%d,CarDis1_3:%d,WallFlag1:%d,WallFlag2:%d\r\n",Lu8SigGroupCnt1,GfMessageTime,\
                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[0],LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u16ActualDis[0],\
                LpstrSnsSigGroupDataCache->MasterBuf[LenuLastBefIndex].u16ActualDis[0],\
                Ls16ObjDisSub1_2_Abs,Ls16ObjDisSub1_3_Abs,Ls16ObjMoveCarMoveSub1_2,Ls16ObjMoveCarMoveSub1_3,\
                Ls16CarMoveDis1_2_Abs,Ls16CarMoveDis1_3_Abs,Lu8ParallelWallFlag1,Lu8ParallelWallFlag2);
        }
#endif

        if(Lu8ParallelWallFlag1)
        {
            Lu8ObjValidFlag = VALID;
            if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
            {
                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
            }
            else
            {
                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
            }
            break;
        }
#if 0
        else if(Lu8ParallelWallFlag2)
        {
            Lu8ObjValidFlag = VALID;
            if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
            {
                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
            }
            else
            {
                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
            }
            break;
        }
#endif
        if((Ls16ObjMoveCarMoveSub1_2 != FOLLOW_DIS_SUB_INVALID)&&(Ls16ObjMoveCarMoveSub1_3 != FOLLOW_DIS_SUB_INVALID))
        {
            Lu8ObjValidFlag = VALID;
            if(Ls16ObjMoveCarMoveSub1_2 < CORNER_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
            {
                if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                }
                else
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                }
            }
            else
            {
                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
            }
            break;
        }
        else if(Ls16ObjMoveCarMoveSub1_3 != FOLLOW_DIS_SUB_INVALID)
        {
            /* 只有本轮和上上轮匹配时，高速时，可以快速生成；低速时需要分探头探测距离和定扫频进行区分 */
            if(Gu16CarSpdForSnsUse > 500)
            {
#if 0
                if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < CORNER_HIGH_SPD_FAST_CONFIRM_DIS)
                {
                    if(Ls16ObjMoveCarMoveSub1_3 < CORNER_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                    {
                        if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                        }
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                    }

                    Lu8ObjValidFlag = VALID;
                    break;
                }
#endif
            }
            else
            {
                if(LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType > PDC_SNS_MEAS_STD)
                {
                    if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] > CORNER_CHIRP_FAST_CONFIRM_DIS)
                    {
                        if(Ls16ObjMoveCarMoveSub1_3 < CORNER_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                        {
                            if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                            }
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                        Lu8ObjValidFlag = VALID;
                        break;
                    }
                }
                else
                {
                    if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < CORNER_STANDARD_FAST_CONFIRM_DIS)
                    {
                        if(Ls16ObjMoveCarMoveSub1_3 < CORNER_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                        {
                            if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                            }
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                
                        if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_HIGH_CONFIRDENCE_DIS)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_VERY_HIGH;
                        }

                        /* 做定扫结合 */
                        if ((Ls16ObjMoveCarMoveSub1_2 > Ls16ObjMoveCarMoveDisSubLimit) && (Gu16CarSpdForSnsUse < 300))
                        {
                            break;
                        }
                        
                        Lu8ObjValidFlag = VALID;
                        break;
                    }
                }
            }
        }
        else if(Ls16ObjMoveCarMoveSub1_2 != FOLLOW_DIS_SUB_INVALID)
        {
#if 0
            /* 对于中间雷达，车辆运动时，在高速时，需要快速生成Map DE ，快速确认障碍物，才采用此策略 */
            if(Gu16CarSpdForSnsUse > 500)
            {
                if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < CORNER_HIGH_SPD_FAST_CONFIRM_DIS)
                {
                    if(Ls16ObjMoveCarMoveSub1_2 < CORNER_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                    {
                        if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                        }
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                    }

                    Lu8ObjValidFlag = VALID;
                    break;
                }
            }
#endif
        }
    }
    
    if(Lu8ObjValidFlag)
    {
        if(!LpstrSnsSigGroupDisFollow->u8DisFollowFlag)
        {
            Lu8ObjValidCheckFlag = 1;
        }
        else
        {
            /* 已经跟踪上的，再后续的比较中，只考虑近距离快速进入的，远离的等已经跟踪上的障碍物消失后再重新匹配 */
            if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < LpstrSnsSigGroupDisFollow->u16FollowDis)
            {
                Lu8ObjValidCheckFlag = 1;
            }
        }
#if 1
        if(Lu8ObjValidCheckFlag)
        {
            if(Lu8ParallelWallFlag1||Lu8ParallelWallFlag2)
            {
                LpstrSnsSigGroupDisFollow->u8ParallelWallCnt++;
            }
            LpstrSnsSigGroupDisFollow->u8DisFollowFlag = VALID;
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
            LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
            
            LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
        }
#endif
    }

#if 0
        if((GenuSnsGroup == 0x00)&&(GenuSnsCh == 0x04))
        {
            printf("FR Master Search,Time:%.3f,FollowDis:%d,Flag:%d,NewDis:%d,LastDis:%d,LastBefDis:%d,Sub1_2:%d,Sub1_3:%d,Obj_CarSub1_2:%d,Obj_CarSub1_3:%d,WallFlag1:%d,WallFlag2:%d\r\n",GfMessageTime,\
                LpstrSnsSigGroupDisFollow->u16FollowDis,Lu8ObjValidCheckFlag,\
                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[0],LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u16ActualDis[0],\
                LpstrSnsSigGroupDataCache->MasterBuf[LenuLastBefIndex].u16ActualDis[0],\
                Ls16ObjDisSub1_2_Abs,Ls16ObjDisSub1_3_Abs,\
                Ls16ObjMoveCarMoveSub1_2,Ls16ObjMoveCarMoveSub1_3,Lu8ParallelWallFlag1,Lu8ParallelWallFlag2);
        }
#endif

}

/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForCornerLeftMoveSearch
 * 
 * 功能描述: 边角探头左侦听距离在车辆运动状态下的匹配查询策略；对于侧雷达的侦听才考虑平行墙逻辑
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应主发探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-14 20:47   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForCornerLeftMoveSearch(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    Sns_CycleType LenuCurIndex,LenuLastIndex,LenuLastBefIndex;
    uint8 Lu8SigGroupCnt1,Lu8SigGroupCnt2,Lu8SigGroupCnt3;
    sint16 Ls16ObjDisSub1_2,Ls16ObjDisSub1_3;
    sint16 Ls16ObjDisSub1_2_Abs,Ls16ObjDisSub1_3_Abs;
    sint16 Ls16CarMoveDis1_2,Ls16CarMoveDis1_3;
    sint16 Ls16CarMoveDis1_2_Abs,Ls16CarMoveDis1_3_Abs;
    sint16 Ls16ObjMoveCarMoveSub1_2,Ls16ObjMoveCarMoveSub1_3;
    sint16 Ls16ObjMoveDisSubLimit,Ls16ObjMoveCarMoveDisSubLimit;
    uint8 Lu8ObjValidFlag = 0;
    uint8 Lu8ObjValidCheckFlag = 0;
    uint8 Lu8ParallelWallFlag1,Lu8ParallelWallFlag2;
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    LenuLastIndex = SnsSigGroupFollowGetLastIndex(LenuCurIndex);
    LenuLastBefIndex = SnsSigGroupFollowGetLastIndex(LenuLastIndex);

    if(Gu16CarSpdForSnsUse < 500)
    {
        Ls16ObjMoveDisSubLimit = CORNER_CORNER_MOVE_DIS_FOR_5KM;
        Ls16ObjMoveCarMoveDisSubLimit = CORNER_OBJ_CAR_MOVE_DIS_FOR_5KM;
    }
    else if(Gu16CarSpdForSnsUse < 1000)
    {
        Ls16ObjMoveDisSubLimit = CORNER_CORNER_MOVE_DIS_FOR_10KM;
        Ls16ObjMoveCarMoveDisSubLimit = CORNER_OBJ_CAR_MOVE_DIS_FOR_10KM;
    }
    else
    {
        Ls16ObjMoveDisSubLimit = CORNER_CORNER_MOVE_DIS_FOR_15KM;
        Ls16ObjMoveCarMoveDisSubLimit = CORNER_OBJ_CAR_MOVE_DIS_FOR_15KM;
    }
    
    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3_Abs = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjMoveCarMoveSub1_3 = FOLLOW_DIS_SUB_INVALID;
    Lu8ParallelWallFlag1 = 0;
    Lu8ParallelWallFlag2 = 0;

    /* Step1：计算车辆移动的距离 */
    /* 上个周期探测到本周期探测车辆移动的距离 */
    Ls16CarMoveDis1_2 = (sint16)LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub;
    Ls16CarMoveDis1_2_Abs = ABS_VALUE(Ls16CarMoveDis1_2);

    /* 上上个周期探测到本周期探测车辆移动的距离 */
    Ls16CarMoveDis1_3 = (sint16)(LpstrSnsSigGroupDataCache->SysDataBuf[LenuLastIndex].fCarMoveDisSub + LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub);
    Ls16CarMoveDis1_3_Abs = ABS_VALUE(Ls16CarMoveDis1_3);
    
    for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
    {
        Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjDisSub1_3_Abs = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjMoveCarMoveSub1_3 = FOLLOW_DIS_SUB_INVALID;
        Lu8ParallelWallFlag1 = 0;
        Lu8ParallelWallFlag2 = 0;
        
        /* 先用本轮和上一轮进行距离差值比较 */
        for(Lu8SigGroupCnt2 = 0; Lu8SigGroupCnt2 < LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u8SigGroupCnt; Lu8SigGroupCnt2++)
        {
            /** 比较本轮和上一轮的差值 */
            if((LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]!=SNS_INVALID_DIS))
            {
                Ls16ObjDisSub1_2 = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]-LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2];
                Ls16ObjDisSub1_2_Abs = ABS_VALUE(Ls16ObjDisSub1_2);
                if(Ls16ObjDisSub1_2_Abs < Ls16ObjMoveDisSubLimit)
                {
                    /* 计算障碍物移动的距离和车辆移动距离的差值 */
                    Ls16ObjMoveCarMoveSub1_2 = ABS(Ls16CarMoveDis1_2,Ls16ObjDisSub1_2);

                    if((Ls16ObjMoveCarMoveSub1_2 < Ls16ObjMoveCarMoveDisSubLimit)||(Ls16ObjDisSub1_2_Abs < CORNER_OBJ_PARALLEL_WALL_DIS_SUB))
                    {
                        if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < CORNER_DIRECT_WALL_DETECT_DIS_MAX)
                        {
                            if((Ls16ObjDisSub1_2_Abs + (Ls16CarMoveDis1_2_Abs/2))< Ls16ObjMoveCarMoveSub1_2)
                            {
                                Lu8ParallelWallFlag1 = 1;
                            }
                            else if((Ls16ObjDisSub1_2_Abs < CORNER_OBJ_DIRECT_WALL_DIS_SUB)&&(Ls16CarMoveDis1_2_Abs > CORNER_DIRECT_WALL_CAR_DIS_LIMIT))
                            {
                                Lu8ParallelWallFlag1 = 1;
                            }
                        }
                        break;
                    }
                    else
                    {
                        Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
                        Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
                        Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
                    }
                }
                else
                {
                    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
                    Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
                    Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
                }
            }
        }
    
        /* 本轮数据和上上一轮数据做比较 */
        for(Lu8SigGroupCnt3 = 0; Lu8SigGroupCnt3 < LpstrSnsSigGroupDataCache->LeftBuf[LenuLastBefIndex].u8SigGroupCnt; Lu8SigGroupCnt3++)
        {
            if((LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->LeftBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]!=SNS_INVALID_DIS))
            {
                Ls16ObjDisSub1_3 = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]-LpstrSnsSigGroupDataCache->LeftBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3];
                Ls16ObjDisSub1_3_Abs = ABS_VALUE(Ls16ObjDisSub1_3);
                if(Ls16ObjDisSub1_3_Abs < (Ls16ObjMoveDisSubLimit*2))
                {
                    /* 计算障碍物移动的距离和车辆移动距离的差值 */
                    Ls16ObjMoveCarMoveSub1_3 = ABS(Ls16CarMoveDis1_3,Ls16ObjDisSub1_3);
                    if((Ls16ObjMoveCarMoveSub1_3 < Ls16ObjMoveCarMoveDisSubLimit)||(Ls16ObjDisSub1_3_Abs < CORNER_OBJ_PARALLEL_WALL_DIS_SUB))
                    {
                        if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < CORNER_DIRECT_WALL_DETECT_DIS_MAX)
                        {
                            if((Ls16ObjDisSub1_3_Abs + (Ls16CarMoveDis1_3_Abs/2))< Ls16ObjMoveCarMoveSub1_3)
                            {
                                Lu8ParallelWallFlag2 = 1;
                            }
                            else if((Ls16ObjDisSub1_3_Abs < CORNER_OBJ_DIRECT_WALL_DIS_SUB)&&(Ls16CarMoveDis1_3_Abs > CORNER_DIRECT_WALL_CAR_DIS_LIMIT))
                            {
                                Lu8ParallelWallFlag2 = 1;
                            }
                        }
                        break;
                    }
                    else
                    {
                        Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
                        Ls16ObjDisSub1_3_Abs = FOLLOW_DIS_SUB_INVALID;
                        Ls16ObjMoveCarMoveSub1_3 = FOLLOW_DIS_SUB_INVALID;
                    }
                }
                else
                {
                    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
                    Ls16ObjDisSub1_3_Abs = FOLLOW_DIS_SUB_INVALID;
                }
            }
        }

#if 0
        if((GenuSnsGroup == 0x00)&&(GenuSnsCh == 0x04))
        {
            printf("FR Master Search,GroupCnt1:%d,Time:%.3f,NewDis:%d,LastDis:%d,LastBefDis:%d,Sub1_2:%d,Sub1_3:%d,Obj_CarSub1_2:%d,Obj_CarSub1_3:%d,CarDis1_2:%d,CarDis1_3:%d,WallFlag1:%d,WallFlag2:%d\r\n",Lu8SigGroupCnt1,GfMessageTime,\
                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[0],LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u16ActualDis[0],\
                LpstrSnsSigGroupDataCache->LeftBuf[LenuLastBefIndex].u16ActualDis[0],\
                Ls16ObjDisSub1_2_Abs,Ls16ObjDisSub1_3_Abs,Ls16ObjMoveCarMoveSub1_2,Ls16ObjMoveCarMoveSub1_3,\
                Ls16CarMoveDis1_2_Abs,Ls16CarMoveDis1_3_Abs,Lu8ParallelWallFlag1,Lu8ParallelWallFlag2);
        }
#endif

        if(Lu8ParallelWallFlag1)
        {
            Lu8ObjValidFlag = VALID;
            if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
            {
                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
            }
            else
            {
                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
            }
            break;
        }
#if 0
        else if(Lu8ParallelWallFlag2)
        {
            Lu8ObjValidFlag = VALID;
            if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
            {
                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
            }
            else
            {
                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
            }
            break;
        }
#endif
        if((Ls16ObjMoveCarMoveSub1_2 != FOLLOW_DIS_SUB_INVALID)&&(Ls16ObjMoveCarMoveSub1_3 != FOLLOW_DIS_SUB_INVALID))
        {
            Lu8ObjValidFlag = VALID;
            if(Ls16ObjMoveCarMoveSub1_2 < CORNER_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
            {
                if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                }
                else
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                }
            }
            else
            {
                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
            }
            break;
        }
        else if(Ls16ObjMoveCarMoveSub1_3 != FOLLOW_DIS_SUB_INVALID)
        {
            /* 只有本轮和上上轮匹配时，高速时，可以快速生成；低速时需要分探头探测距离和定扫频进行区分 */
            if(Gu16CarSpdForSnsUse > 500)
            {
#if 0
                if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < CORNER_HIGH_SPD_FAST_CONFIRM_DIS)
                {
                    if(Ls16ObjMoveCarMoveSub1_3 < CORNER_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                    {
                        if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                        }
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                    }

                    Lu8ObjValidFlag = VALID;
                    break;
                }
#endif
            }
            else
            {
                if(LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType > PDC_SNS_MEAS_STD)
                {
                    if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] > CORNER_CHIRP_FAST_CONFIRM_DIS)
                    {
                        if(Ls16ObjMoveCarMoveSub1_3 < CORNER_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                        {
                            if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                            }
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                        Lu8ObjValidFlag = VALID;
                        break;
                    }
                }
                else
                {
                    if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < CORNER_STANDARD_FAST_CONFIRM_DIS)
                    {
                        if(Ls16ObjMoveCarMoveSub1_3 < CORNER_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                        {
                            if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                            }
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                
                        if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_HIGH_CONFIRDENCE_DIS)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_VERY_HIGH;
                        }

                        /* 做定扫结合 */
                        if ((Ls16ObjMoveCarMoveSub1_2 > Ls16ObjMoveCarMoveDisSubLimit) && (Gu16CarSpdForSnsUse < 300))
                        {
                            break;
                        }

                        Lu8ObjValidFlag = VALID;
                        break;
                    }
                }
            }
        }
        else if(Ls16ObjMoveCarMoveSub1_2 != FOLLOW_DIS_SUB_INVALID)
        {
#if 0
            /* 对于中间雷达，车辆运动时，在高速时，需要快速生成Map DE ，快速确认障碍物，才采用此策略 */
            if(Gu16CarSpdForSnsUse > 500)
            {
                if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < CORNER_HIGH_SPD_FAST_CONFIRM_DIS)
                {
                    if(Ls16ObjMoveCarMoveSub1_2 < CORNER_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                    {
                        if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                        }
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                    }

                    Lu8ObjValidFlag = VALID;
                    break;
                }
            }
#endif
        }
    }
    
    if(Lu8ObjValidFlag)
    {
        if(!LpstrSnsSigGroupDisFollow->u8DisFollowFlag)
        {
            Lu8ObjValidCheckFlag = 1;
        }
        else
        {
            /* 已经跟踪上的，再后续的比较中，只考虑近距离快速进入的，远离的等已经跟踪上的障碍物消失后再重新匹配 */
            if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < LpstrSnsSigGroupDisFollow->u16FollowDis)
            {
                Lu8ObjValidCheckFlag = 1;
            }
        }
#if 1
        if(Lu8ObjValidCheckFlag)
        {
            if(Lu8ParallelWallFlag1||Lu8ParallelWallFlag2)
            {
                LpstrSnsSigGroupDisFollow->u8ParallelWallCnt++;
            }
            LpstrSnsSigGroupDisFollow->u8DisFollowFlag = VALID;
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
            LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
            
            LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
        }
#endif
    }

#if 0
        if((GenuSnsGroup == 0x00)&&(GenuSnsCh == 0x04))
        {
            printf("FR Master Search,Time:%.3f,FollowDis:%d,Flag:%d,NewDis:%d,LastDis:%d,LastBefDis:%d,Sub1_2:%d,Sub1_3:%d,Obj_CarSub1_2:%d,Obj_CarSub1_3:%d,WallFlag1:%d,WallFlag2:%d\r\n",GfMessageTime,\
                LpstrSnsSigGroupDisFollow->u16FollowDis,Lu8ObjValidCheckFlag,\
                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[0],LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u16ActualDis[0],\
                LpstrSnsSigGroupDataCache->LeftBuf[LenuLastBefIndex].u16ActualDis[0],\
                Ls16ObjDisSub1_2_Abs,Ls16ObjDisSub1_3_Abs,\
                Ls16ObjMoveCarMoveSub1_2,Ls16ObjMoveCarMoveSub1_3,Lu8ParallelWallFlag1,Lu8ParallelWallFlag2);
        }
#endif

}


/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForCornerRightMoveSearch
 * 
 * 功能描述: 边角探头右侦听距离在车辆运动状态下的匹配查询策略；对于侧雷达的侦听才考虑平行墙逻辑
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应主发探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-14 20:54   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForCornerRightMoveSearch(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    Sns_CycleType LenuCurIndex,LenuLastIndex,LenuLastBefIndex;
    uint8 Lu8SigGroupCnt1,Lu8SigGroupCnt2,Lu8SigGroupCnt3;
    sint16 Ls16ObjDisSub1_2,Ls16ObjDisSub1_3;
    sint16 Ls16ObjDisSub1_2_Abs,Ls16ObjDisSub1_3_Abs;
    sint16 Ls16CarMoveDis1_2,Ls16CarMoveDis1_3;
    sint16 Ls16CarMoveDis1_2_Abs,Ls16CarMoveDis1_3_Abs;
    sint16 Ls16ObjMoveCarMoveSub1_2,Ls16ObjMoveCarMoveSub1_3;
    sint16 Ls16ObjMoveDisSubLimit,Ls16ObjMoveCarMoveDisSubLimit;
    uint8 Lu8ObjValidFlag = 0;
    uint8 Lu8ObjValidCheckFlag = 0;
    uint8 Lu8ParallelWallFlag1,Lu8ParallelWallFlag2;
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    LenuLastIndex = SnsSigGroupFollowGetLastIndex(LenuCurIndex);
    LenuLastBefIndex = SnsSigGroupFollowGetLastIndex(LenuLastIndex);

    if(Gu16CarSpdForSnsUse < 500)
    {
        Ls16ObjMoveDisSubLimit = CORNER_CORNER_MOVE_DIS_FOR_5KM;
        Ls16ObjMoveCarMoveDisSubLimit = CORNER_OBJ_CAR_MOVE_DIS_FOR_5KM;
    }
    else if(Gu16CarSpdForSnsUse < 1000)
    {
        Ls16ObjMoveDisSubLimit = CORNER_CORNER_MOVE_DIS_FOR_10KM;
        Ls16ObjMoveCarMoveDisSubLimit = CORNER_OBJ_CAR_MOVE_DIS_FOR_10KM;
    }
    else
    {
        Ls16ObjMoveDisSubLimit = CORNER_CORNER_MOVE_DIS_FOR_15KM;
        Ls16ObjMoveCarMoveDisSubLimit = CORNER_OBJ_CAR_MOVE_DIS_FOR_15KM;
    }
    
    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3_Abs = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjMoveCarMoveSub1_3 = FOLLOW_DIS_SUB_INVALID;
    Lu8ParallelWallFlag1 = 0;
    Lu8ParallelWallFlag2 = 0;

    /* Step1：计算车辆移动的距离 */
    /* 上个周期探测到本周期探测车辆移动的距离 */
    Ls16CarMoveDis1_2 = (sint16)LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub;
    Ls16CarMoveDis1_2_Abs = ABS_VALUE(Ls16CarMoveDis1_2);

    /* 上上个周期探测到本周期探测车辆移动的距离 */
    Ls16CarMoveDis1_3 = (sint16)(LpstrSnsSigGroupDataCache->SysDataBuf[LenuLastIndex].fCarMoveDisSub + LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub);
    Ls16CarMoveDis1_3_Abs = ABS_VALUE(Ls16CarMoveDis1_3);
    
    for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
    {
        Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjDisSub1_3_Abs = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
        Ls16ObjMoveCarMoveSub1_3 = FOLLOW_DIS_SUB_INVALID;
        Lu8ParallelWallFlag1 = 0;
        Lu8ParallelWallFlag2 = 0;
        
        /* 先用本轮和上一轮进行距离差值比较 */
        for(Lu8SigGroupCnt2 = 0; Lu8SigGroupCnt2 < LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u8SigGroupCnt; Lu8SigGroupCnt2++)
        {
            /** 比较本轮和上一轮的差值 */
            if((LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]!=SNS_INVALID_DIS))
            {
                Ls16ObjDisSub1_2 = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]-LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2];
                Ls16ObjDisSub1_2_Abs = ABS_VALUE(Ls16ObjDisSub1_2);
                if(Ls16ObjDisSub1_2_Abs < Ls16ObjMoveDisSubLimit)
                {
                    /* 计算障碍物移动的距离和车辆移动距离的差值 */
                    Ls16ObjMoveCarMoveSub1_2 = ABS(Ls16CarMoveDis1_2,Ls16ObjDisSub1_2);
                    if((Ls16ObjMoveCarMoveSub1_2 < Ls16ObjMoveCarMoveDisSubLimit)||(Ls16ObjDisSub1_2_Abs < CORNER_OBJ_PARALLEL_WALL_DIS_SUB))
                    {
                        if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < CORNER_DIRECT_WALL_DETECT_DIS_MAX)
                        {
                            if((Ls16ObjDisSub1_2_Abs + (Ls16CarMoveDis1_2_Abs/2))< Ls16ObjMoveCarMoveSub1_2)
                            {
                                Lu8ParallelWallFlag1 = 1;
                            }
                            else if((Ls16ObjDisSub1_2_Abs < CORNER_OBJ_DIRECT_WALL_DIS_SUB)&&(Ls16CarMoveDis1_2_Abs > CORNER_DIRECT_WALL_CAR_DIS_LIMIT))
                            {
                                Lu8ParallelWallFlag1 = 1;
                            }
                        }
                        break;
                    }
                    else
                    {
                        Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
                        Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
                        Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
                    }
                }
                else
                {
                    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
                    Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
                    Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
                }
            }
        }
    
        /* 本轮数据和上上一轮数据做比较 */
        for(Lu8SigGroupCnt3 = 0; Lu8SigGroupCnt3 < LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u8SigGroupCnt; Lu8SigGroupCnt3++)
        {
            if((LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]!=SNS_INVALID_DIS))
            {
                Ls16ObjDisSub1_3 = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]-LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3];
                Ls16ObjDisSub1_3_Abs = ABS_VALUE(Ls16ObjDisSub1_3);
                if(Ls16ObjDisSub1_3_Abs < (Ls16ObjMoveDisSubLimit*2))
                {
                    /* 计算障碍物移动的距离和车辆移动距离的差值 */
                    Ls16ObjMoveCarMoveSub1_3 = ABS(Ls16CarMoveDis1_3,Ls16ObjDisSub1_3);
                    if((Ls16ObjMoveCarMoveSub1_3 < Ls16ObjMoveCarMoveDisSubLimit)||(Ls16ObjDisSub1_3_Abs < CORNER_OBJ_PARALLEL_WALL_DIS_SUB))
                    {
                        if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < CORNER_DIRECT_WALL_DETECT_DIS_MAX)
                        {
                            if((Ls16ObjDisSub1_3_Abs + (Ls16CarMoveDis1_3_Abs/2))< Ls16ObjMoveCarMoveSub1_3)
                            {
                                Lu8ParallelWallFlag2 = 1;
                            }
                            else if((Ls16ObjDisSub1_3_Abs < CORNER_OBJ_DIRECT_WALL_DIS_SUB)&&(Ls16CarMoveDis1_3_Abs > CORNER_DIRECT_WALL_CAR_DIS_LIMIT))
                            {
                                Lu8ParallelWallFlag2 = 1;
                            }
                        }
                        break;
                    }
                    else
                    {
                        Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
                        Ls16ObjDisSub1_3_Abs = FOLLOW_DIS_SUB_INVALID;
                        Ls16ObjMoveCarMoveSub1_3 = FOLLOW_DIS_SUB_INVALID;
                    }
                }
                else
                {
                    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
                    Ls16ObjDisSub1_3_Abs = FOLLOW_DIS_SUB_INVALID;
                    Ls16ObjMoveCarMoveSub1_3 = FOLLOW_DIS_SUB_INVALID;
                }
            }
        }

#if 0
        if((GenuSnsGroup == 0x00)&&(GenuSnsCh == 0x04))
        {
            printf("FR Master Search,GroupCnt1:%d,Time:%.3f,NewDis:%d,LastDis:%d,LastBefDis:%d,Sub1_2:%d,Sub1_3:%d,Obj_CarSub1_2:%d,Obj_CarSub1_3:%d,CarDis1_2:%d,CarDis1_3:%d,WallFlag1:%d,WallFlag2:%d\r\n",Lu8SigGroupCnt1,GfMessageTime,\
                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[0],LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u16ActualDis[0],\
                LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u16ActualDis[0],\
                Ls16ObjDisSub1_2_Abs,Ls16ObjDisSub1_3_Abs,Ls16ObjMoveCarMoveSub1_2,Ls16ObjMoveCarMoveSub1_3,\
                Ls16CarMoveDis1_2_Abs,Ls16CarMoveDis1_3_Abs,Lu8ParallelWallFlag1,Lu8ParallelWallFlag2);
        }
#endif

        if(Lu8ParallelWallFlag1)
        {
            Lu8ObjValidFlag = VALID;
            if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
            {
                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
            }
            else
            {
                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
            }
            break;
        }
#if 0
        else if(Lu8ParallelWallFlag2)
        {
            Lu8ObjValidFlag = VALID;
            if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
            {
                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
            }
            else
            {
                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
            }
            break;
        }
#endif
        if((Ls16ObjMoveCarMoveSub1_2 != FOLLOW_DIS_SUB_INVALID)&&(Ls16ObjMoveCarMoveSub1_3 != FOLLOW_DIS_SUB_INVALID))
        {
            Lu8ObjValidFlag = VALID;
            if(Ls16ObjMoveCarMoveSub1_2 < CORNER_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
            {
                if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                }
                else
                {
                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                }
            }
            else
            {
                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
            }
            break;
        }
        else if(Ls16ObjMoveCarMoveSub1_3 != FOLLOW_DIS_SUB_INVALID)
        {
            /* 只有本轮和上上轮匹配时，高速时，可以快速生成；低速时需要分探头探测距离和定扫频进行区分 */
            if(Gu16CarSpdForSnsUse > 500)
            {
#if 0
                if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < CORNER_HIGH_SPD_FAST_CONFIRM_DIS)
                {
                    if(Ls16ObjMoveCarMoveSub1_3 < CORNER_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                    {
                        if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                        }
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                    }

                    Lu8ObjValidFlag = VALID;
                    break;
                }
#endif
            }
            else
            {
                if(LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType > PDC_SNS_MEAS_STD)
                {
                    if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] > CORNER_CHIRP_FAST_CONFIRM_DIS)
                    {
                        if(Ls16ObjMoveCarMoveSub1_3 < CORNER_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                        {
                            if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                            }
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                        Lu8ObjValidFlag = VALID;
                        break;
                    }
                }
                else
                {
                    if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < CORNER_STANDARD_FAST_CONFIRM_DIS)
                    {
                        if(Ls16ObjMoveCarMoveSub1_3 < CORNER_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                        {
                            if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                            }
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                        }
                
                        if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_HIGH_CONFIRDENCE_DIS)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_VERY_HIGH;
                        }

                        /* 做定扫结合 */
                        if ((Ls16ObjMoveCarMoveSub1_2 > Ls16ObjMoveCarMoveDisSubLimit) && (Gu16CarSpdForSnsUse < 300))
                        {
                            break;
                        }

                        Lu8ObjValidFlag = VALID;
                        break;
                    }
                }
            }
        }
        else if(Ls16ObjMoveCarMoveSub1_2 != FOLLOW_DIS_SUB_INVALID)
        {
#if 0
            /* 对于中间雷达，车辆运动时，在高速时，需要快速生成Map DE ，快速确认障碍物，才采用此策略 */
            if(Gu16CarSpdForSnsUse > 500)
            {
                if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < CORNER_HIGH_SPD_FAST_CONFIRM_DIS)
                {
                    if(Ls16ObjMoveCarMoveSub1_2 < CORNER_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                    {
                        if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                        }
                        else
                        {
                            LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                        }
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                    }

                    Lu8ObjValidFlag = VALID;
                    break;
                }
            }
#endif
        }
    }
    
    if(Lu8ObjValidFlag)
    {
        if(!LpstrSnsSigGroupDisFollow->u8DisFollowFlag)
        {
            Lu8ObjValidCheckFlag = 1;
        }
        else
        {
            /* 已经跟踪上的，再后续的比较中，只考虑近距离快速进入的，远离的等已经跟踪上的障碍物消失后再重新匹配 */
            if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < LpstrSnsSigGroupDisFollow->u16FollowDis)
            {
                Lu8ObjValidCheckFlag = 1;
            }
        }
#if 1
        if(Lu8ObjValidCheckFlag)
        {
            if(Lu8ParallelWallFlag1||Lu8ParallelWallFlag2)
            {
                LpstrSnsSigGroupDisFollow->u8ParallelWallCnt++;
            }
            LpstrSnsSigGroupDisFollow->u8DisFollowFlag = VALID;
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
            LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
            
            LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
        }
#endif
    }

#if 0
        if((GenuSnsGroup == 0x00)&&(GenuSnsCh == 0x04))
        {
            printf("FR Master Search,Time:%.3f,FollowDis:%d,Flag:%d,NewDis:%d,LastDis:%d,LastBefDis:%d,Sub1_2:%d,Sub1_3:%d,Obj_CarSub1_2:%d,Obj_CarSub1_3:%d,WallFlag1:%d,WallFlag2:%d\r\n",GfMessageTime,\
                LpstrSnsSigGroupDisFollow->u16FollowDis,Lu8ObjValidCheckFlag,\
                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[0],LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u16ActualDis[0],\
                LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u16ActualDis[0],\
                Ls16ObjDisSub1_2_Abs,Ls16ObjDisSub1_3_Abs,\
                Ls16ObjMoveCarMoveSub1_2,Ls16ObjMoveCarMoveSub1_3,Lu8ParallelWallFlag1,Lu8ParallelWallFlag2);
        }
#endif

}



/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForCornerMasterMoveFollow
 * 
 * 功能描述: 边角探头主发距离在车辆运动状态下的跟踪策略--主要优化平行墙场景
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应主发探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-14 19:39   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForCornerMasterMoveFollow(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    Sns_CycleType LenuCurIndex,LenuLastIndex,LenuLastBefIndex;
    uint8 Lu8ExitFollowCnt = 0;
    uint8 Lu8ParallelExitFlag = 0;
    uint8 Lu8SigGroupCnt1,Lu8SigGroupCnt2,Lu8SigGroupCnt3;
    sint16 Ls16ObjDisSub1_2;
    sint16 Ls16ObjDisSub1_2_Abs;
    sint16 Ls16CarMoveDis1_2;
    sint16 Ls16CarMoveDis1_2_Abs;
    sint16 Ls16ObjMoveCarMoveSub1_2;
    uint8 Lu8ParallelWallFlag1_2;
    
    sint16 Ls16ObjMoveDisSubLimit,Ls16ObjMoveCarMoveDisSubLimit;
    sint16 Ls16CarMoveLastDetectToNow,Ls16ObjDisByFollow;
    sint16 Ls16Follow_DetectDisSub;
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    LenuLastIndex = SnsSigGroupFollowGetLastIndex(LenuCurIndex);
    LenuLastBefIndex = SnsSigGroupFollowGetLastIndex(LenuLastIndex);

    if(Gu16CarSpdForSnsUse < 500)
    {
        Ls16ObjMoveDisSubLimit = CORNER_CORNER_MOVE_DIS_FOR_5KM;
        Ls16ObjMoveCarMoveDisSubLimit = CORNER_OBJ_CAR_MOVE_DIS_FOR_5KM;
    }
    else if(Gu16CarSpdForSnsUse < 1000)
    {
        Ls16ObjMoveDisSubLimit = CORNER_CORNER_MOVE_DIS_FOR_10KM;
        Ls16ObjMoveCarMoveDisSubLimit = CORNER_OBJ_CAR_MOVE_DIS_FOR_10KM;
    }
    else
    {
        Ls16ObjMoveDisSubLimit = CORNER_CORNER_MOVE_DIS_FOR_15KM;
        Ls16ObjMoveCarMoveDisSubLimit = CORNER_OBJ_CAR_MOVE_DIS_FOR_15KM;
    }
    
    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Lu8ParallelWallFlag1_2 = 0;

    /* 上个周期探测到本周期探测车辆移动的距离 */
    Ls16CarMoveDis1_2 = (sint16)LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub;
    Ls16CarMoveDis1_2_Abs = ABS_VALUE(Ls16CarMoveDis1_2);

    /* Step1：计算上一次探测更新到本周期车辆移动的距离；根据车辆移动的距离推算出本周期的预估距离 */
    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow += LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub;
    Ls16CarMoveLastDetectToNow = (sint16)LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow;

    Ls16ObjDisByFollow = FOLLOW_DIS_SUB_INVALID;

    if(Ls16CarMoveLastDetectToNow < 0)
    {
        if(LpstrSnsSigGroupDisFollow->u16FollowDis < ABS_VALUE(Ls16CarMoveLastDetectToNow))
        {
            /* 防止跟踪错误，跟踪距离比待减去的距离还小，直接退出跟踪 */
            LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
            LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
            LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
            LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
            LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
            LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 0;
        }
        else
        {
            Ls16ObjDisByFollow = (sint16)LpstrSnsSigGroupDisFollow->u16DisDetect_Last + Ls16CarMoveLastDetectToNow;
        }
    }
    else
    {
        Ls16ObjDisByFollow = (sint16)LpstrSnsSigGroupDisFollow->u16DisDetect_Last + Ls16CarMoveLastDetectToNow;
    }
    if(LpstrSnsSigGroupDisFollow->u16FollowDis != PDC_OBJ_FOLLOW_INVALID_DIS)
    {
        for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
        {
            Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
            Lu8ParallelWallFlag1_2 = 0;

            /* 先用本轮和上一轮进行距离差值比较 */
            for(Lu8SigGroupCnt2 = 0; Lu8SigGroupCnt2 < LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u8SigGroupCnt; Lu8SigGroupCnt2++)
            {
                /** 比较本轮和上一轮的差值 */
                if((LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]!=SNS_INVALID_DIS))
                {
                    Ls16ObjDisSub1_2 = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]-LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2];
                    Ls16ObjDisSub1_2_Abs = ABS_VALUE(Ls16ObjDisSub1_2);
                    if(Ls16ObjDisSub1_2_Abs < Ls16ObjMoveDisSubLimit)
                    {
                        /* 计算障碍物移动的距离和车辆移动距离的差值 */
                        Ls16ObjMoveCarMoveSub1_2 = ABS(Ls16CarMoveDis1_2,Ls16ObjDisSub1_2);
                        if((Ls16ObjMoveCarMoveSub1_2 < Ls16ObjMoveCarMoveDisSubLimit)||(Ls16ObjDisSub1_2_Abs < CORNER_OBJ_PARALLEL_WALL_DIS_SUB))
                        {
                            if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < CORNER_DIRECT_WALL_DETECT_DIS_MAX)
                            {
                                if((Ls16ObjDisSub1_2_Abs + (Ls16CarMoveDis1_2_Abs/2))< Ls16ObjMoveCarMoveSub1_2)
                                {
                                    Lu8ParallelWallFlag1_2 = 1;
                                }
                                else if((Ls16ObjDisSub1_2_Abs < CORNER_OBJ_DIRECT_WALL_DIS_SUB)&&(Ls16CarMoveDis1_2_Abs > CORNER_DIRECT_WALL_CAR_DIS_LIMIT))
                                {
                                    Lu8ParallelWallFlag1_2 = 1;
                                }
                            }
                            break;
                        }
                        else
                        {
                            Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
                            Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
                            Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
                        }
                    }
                    else
                    {
                        Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
                        Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
                        Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
                    }
                }
            }
            if(Lu8ParallelWallFlag1_2)
            {
                /* 平行墙场景，直接更新最新的距离 */
                LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                
                LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                
                LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];;
                LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                LpstrSnsSigGroupDisFollow->u8ParallelWallCnt++;
                break;
            }
            else
            {
                Ls16Follow_DetectDisSub = FOLLOW_DIS_SUB_INVALID;
                if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)
                {
                    Ls16Follow_DetectDisSub = ABS(Ls16ObjDisByFollow,LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]);
                }
                if(Ls16Follow_DetectDisSub < Ls16ObjMoveCarMoveDisSubLimit)
                {
                    /* 预估距离和探测距离差值满足跟踪条件，直接更新最新的距离 */
                    LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                    
                    LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                    
                    LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];;
                    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                    LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                    SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                    if(LpstrSnsSigGroupDisFollow->u8ParallelWallCnt > 0)
                    {
                        LpstrSnsSigGroupDisFollow->u8ParallelWallCnt--;
                    }
                    break;
                }
            }
        }
        
        if(LpstrSnsSigGroupDisFollow->u8ParallelWallCnt > CORNER_PARALLEL_WALL_CNT)
        {
            LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 1;
            LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
        }
        
        if(Lu8SigGroupCnt1 == LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8SigGroupCnt)
        {
            if (LpstrSnsSigGroupDisFollow->u8ParallelWallCnt != 0)
            {
                Lu8ParallelExitFlag = 1;
            }

            if (Gu16CarSpdForSnsUse > 300)
            {
                Lu8ExitFollowCnt = CORNER_FOLLOW_FAST_EXIT_CNT;
            }
            else
            {
                Lu8ExitFollowCnt = CORNER_FOLLOW_EXIT_CNT;
            }
            
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt++;
            if(!LpstrSnsSigGroupDisFollow->u8ParallelWallFlag)
            {
                LpstrSnsSigGroupDisFollow->u16FollowDis = Ls16ObjDisByFollow;   /* 非平行墙场景，使用预估距离进行更新 */
            }
            SnsFollowDisProSub(&LpstrSnsSigGroupDisFollow->enuFollowDisPro,LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType);
            if(LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt > Lu8ExitFollowCnt ||  Lu8ParallelExitFlag == 1)
            {
                /** @brief: 退出跟踪 */
                LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
                LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
                LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
                LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
                LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 0;
            }

            if(Lu8SigGroupCnt1 != 0)
            {
                SnsSigGroupDisFollowForCornerMasterMoveSearch(LeGroup,LenuSnsCh,LpstrSnsSigGroupDisFollow);
            }
        }
        else if(Lu8SigGroupCnt1 > 0)
        {
            SnsSigGroupDisFollowForCornerMasterMoveSearch(LeGroup,LenuSnsCh,LpstrSnsSigGroupDisFollow);
        }
    }
#if 0
    if((GenuSnsGroup == 0x00)&&(GenuSnsCh == 0x04))
    {
        printf("FR Master Follow,Time:%.3f,FollowDis:%d,Flag:%d,NewDis:%d,LastDis:%d,LastBefDis:%d,Sub1_2:%d,Obj_CarSub1_2:%d,WallFlag1_2:%d,Cnt:%d,WallReal:%d\r\n",GfMessageTime,\
            LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDisFollow->u8DisFollowFlag,\
            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[0],LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u16ActualDis[0],\
            LpstrSnsSigGroupDataCache->MasterBuf[LenuLastBefIndex].u16ActualDis[0],\
            Ls16ObjDisSub1_2_Abs,Ls16ObjMoveCarMoveSub1_2,Lu8ParallelWallFlag1_2,\
            LpstrSnsSigGroupDisFollow->u8ParallelWallCnt,LpstrSnsSigGroupDisFollow->u8ParallelWallFlag);
    }
#endif
}

/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForCornerLeftMoveFollow
 * 
 * 功能描述: 边角探头左侦听距离在车辆运动状态下的跟踪策略--主要优化平行墙场景；侦听探头为侧雷达才使用该策略
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应主发探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-14 20:55   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForCornerLeftMoveFollow(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    Sns_CycleType LenuCurIndex,LenuLastIndex,LenuLastBefIndex;
    uint8 Lu8ExitFollowCnt = 0;
    uint8 Lu8ParallelExitFlag = 0;
    uint8 Lu8SigGroupCnt1,Lu8SigGroupCnt2,Lu8SigGroupCnt3;
    sint16 Ls16ObjDisSub1_2;
    sint16 Ls16ObjDisSub1_2_Abs;
    sint16 Ls16CarMoveDis1_2;
    sint16 Ls16CarMoveDis1_2_Abs;
    sint16 Ls16ObjMoveCarMoveSub1_2;
    uint8 Lu8ParallelWallFlag1_2;
    
    sint16 Ls16ObjMoveDisSubLimit,Ls16ObjMoveCarMoveDisSubLimit;
    sint16 Ls16CarMoveLastDetectToNow,Ls16ObjDisByFollow;
    sint16 Ls16Follow_DetectDisSub;
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    LenuLastIndex = SnsSigGroupFollowGetLastIndex(LenuCurIndex);
    LenuLastBefIndex = SnsSigGroupFollowGetLastIndex(LenuLastIndex);

    if(Gu16CarSpdForSnsUse < 500)
    {
        Ls16ObjMoveDisSubLimit = CORNER_CORNER_MOVE_DIS_FOR_5KM;
        Ls16ObjMoveCarMoveDisSubLimit = CORNER_OBJ_CAR_MOVE_DIS_FOR_5KM;
    }
    else if(Gu16CarSpdForSnsUse < 1000)
    {
        Ls16ObjMoveDisSubLimit = CORNER_CORNER_MOVE_DIS_FOR_10KM;
        Ls16ObjMoveCarMoveDisSubLimit = CORNER_OBJ_CAR_MOVE_DIS_FOR_10KM;
    }
    else
    {
        Ls16ObjMoveDisSubLimit = CORNER_CORNER_MOVE_DIS_FOR_15KM;
        Ls16ObjMoveCarMoveDisSubLimit = CORNER_OBJ_CAR_MOVE_DIS_FOR_15KM;
    }
    
    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Lu8ParallelWallFlag1_2 = 0;

    /* 上个周期探测到本周期探测车辆移动的距离 */
    Ls16CarMoveDis1_2 = (sint16)LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub;
    Ls16CarMoveDis1_2_Abs = ABS_VALUE(Ls16CarMoveDis1_2);

    /* Step1：计算上一次探测更新到本周期车辆移动的距离；根据车辆移动的距离推算出本周期的预估距离 */
    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow += LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub;
    Ls16CarMoveLastDetectToNow = (sint16)LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow;

    Ls16ObjDisByFollow = FOLLOW_DIS_SUB_INVALID;

    if(Ls16CarMoveLastDetectToNow < 0)
    {
        if(LpstrSnsSigGroupDisFollow->u16FollowDis < ABS_VALUE(Ls16CarMoveLastDetectToNow))
        {
            /* 防止跟踪错误，跟踪距离比待减去的距离还小，直接退出跟踪 */
            LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
            LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
            LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
            LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
            LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
            LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 0;
        }
        else
        {
            Ls16ObjDisByFollow = (sint16)LpstrSnsSigGroupDisFollow->u16DisDetect_Last + Ls16CarMoveLastDetectToNow;
        }
    }
    else
    {
        Ls16ObjDisByFollow = (sint16)LpstrSnsSigGroupDisFollow->u16DisDetect_Last + Ls16CarMoveLastDetectToNow;
    }
    if(LpstrSnsSigGroupDisFollow->u16FollowDis != PDC_OBJ_FOLLOW_INVALID_DIS)
    {
        for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
        {
            Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
            Lu8ParallelWallFlag1_2 = 0;

            /* 先用本轮和上一轮进行距离差值比较 */
            for(Lu8SigGroupCnt2 = 0; Lu8SigGroupCnt2 < LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u8SigGroupCnt; Lu8SigGroupCnt2++)
            {
                /** 比较本轮和上一轮的差值 */
                if((LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]!=SNS_INVALID_DIS))
                {
                    Ls16ObjDisSub1_2 = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]-LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2];
                    Ls16ObjDisSub1_2_Abs = ABS_VALUE(Ls16ObjDisSub1_2);
                    if(Ls16ObjDisSub1_2_Abs < Ls16ObjMoveDisSubLimit)
                    {
                        /* 计算障碍物移动的距离和车辆移动距离的差值 */
                        Ls16ObjMoveCarMoveSub1_2 = ABS(Ls16CarMoveDis1_2,Ls16ObjDisSub1_2);
                        if((Ls16ObjMoveCarMoveSub1_2 < Ls16ObjMoveCarMoveDisSubLimit)||(Ls16ObjDisSub1_2_Abs < CORNER_OBJ_PARALLEL_WALL_DIS_SUB))
                        {
                            if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < CORNER_DIRECT_WALL_DETECT_DIS_MAX)
                            {
                                if((Ls16ObjDisSub1_2_Abs + (Ls16CarMoveDis1_2_Abs/2))< Ls16ObjMoveCarMoveSub1_2)
                                {
                                    Lu8ParallelWallFlag1_2 = 1;
                                }
                                else if((Ls16ObjDisSub1_2_Abs < CORNER_OBJ_DIRECT_WALL_DIS_SUB)&&(Ls16CarMoveDis1_2_Abs > CORNER_DIRECT_WALL_CAR_DIS_LIMIT))
                                {
                                    Lu8ParallelWallFlag1_2 = 1;
                                }
                            }
                            break;
                        }
                        else
                        {
                            Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
                            Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
                            Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
                        }
                    }
                    else
                    {
                        Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
                        Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
                        Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
                    }
                }
            }

            if(Lu8ParallelWallFlag1_2)
            {
                /* 平行墙场景，直接更新最新的距离 */
                LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                
                LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                
                LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];;
                LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                LpstrSnsSigGroupDisFollow->u8ParallelWallCnt++;
                break;
            }
            else
            {
                Ls16Follow_DetectDisSub = FOLLOW_DIS_SUB_INVALID;
                if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)
                {
                    Ls16Follow_DetectDisSub = ABS(Ls16ObjDisByFollow,LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]);
                }
                if(Ls16Follow_DetectDisSub < Ls16ObjMoveCarMoveDisSubLimit)
                {
                    /* 预估距离和探测距离差值满足跟踪条件，直接更新最新的距离 */
                    LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                    
                    LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                    
                    LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];;
                    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                    LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                    SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                    if(LpstrSnsSigGroupDisFollow->u8ParallelWallCnt > 0)
                    {
                        LpstrSnsSigGroupDisFollow->u8ParallelWallCnt--;
                    }
                    break;
                }
            }
        }
        
        if(LpstrSnsSigGroupDisFollow->u8ParallelWallCnt > CORNER_PARALLEL_WALL_CNT)
        {
            LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 1;
            LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
        }
        
        if(Lu8SigGroupCnt1 == LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8SigGroupCnt)
        {
            if (LpstrSnsSigGroupDisFollow->u8ParallelWallCnt != 0)
            {
                Lu8ParallelExitFlag = 1;
            }

            if (Gu16CarSpdForSnsUse > 300)
            {
                Lu8ExitFollowCnt = CORNER_FOLLOW_FAST_EXIT_CNT;
            }
            else
            {
                Lu8ExitFollowCnt = CORNER_FOLLOW_EXIT_CNT;
            }

            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt++;
            if(!LpstrSnsSigGroupDisFollow->u8ParallelWallFlag)
            {
                LpstrSnsSigGroupDisFollow->u16FollowDis = Ls16ObjDisByFollow;   /* 非平行墙场景，使用预估距离进行更新 */
            }
            SnsFollowDisProSub(&LpstrSnsSigGroupDisFollow->enuFollowDisPro,LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType);
            if(LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt > Lu8ExitFollowCnt ||  Lu8ParallelExitFlag == 1)
            {
                /** @brief: 退出跟踪 */
                LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
                LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
                LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
                LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
                LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 0;
            }

            if(Lu8SigGroupCnt1 != 0)
            {
                SnsSigGroupDisFollowForCornerLeftMoveSearch(LeGroup,LenuSnsCh,LpstrSnsSigGroupDisFollow);
            }
        }
        else if(Lu8SigGroupCnt1 > 0)
        {
            SnsSigGroupDisFollowForCornerLeftMoveSearch(LeGroup,LenuSnsCh,LpstrSnsSigGroupDisFollow);
        }
    }
#if 0
    if((GenuSnsGroup == 0x00)&&(GenuSnsCh == 0x04))
    {
        printf("FR Master Follow,Time:%.3f,FollowDis:%d,Flag:%d,NewDis:%d,LastDis:%d,LastBefDis:%d,Sub1_2:%d,Obj_CarSub1_2:%d,WallFlag1_2:%d,Cnt:%d,WallReal:%d\r\n",GfMessageTime,\
            LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDisFollow->u8DisFollowFlag,\
            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[0],LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u16ActualDis[0],\
            LpstrSnsSigGroupDataCache->LeftBuf[LenuLastBefIndex].u16ActualDis[0],\
            Ls16ObjDisSub1_2_Abs,Ls16ObjMoveCarMoveSub1_2,Lu8ParallelWallFlag1_2,\
            LpstrSnsSigGroupDisFollow->u8ParallelWallCnt,LpstrSnsSigGroupDisFollow->u8ParallelWallFlag);
    }
#endif
}

/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForCornerRightMoveFollow
 * 
 * 功能描述: 边角探头右侦听距离在车辆运动状态下的跟踪策略--主要优化平行墙场景；侦听探头为侧雷达才使用该策略
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应主发探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-14 20:59   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForCornerRightMoveFollow(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    Sns_CycleType LenuCurIndex,LenuLastIndex,LenuLastBefIndex;
    uint8 Lu8ExitFollowCnt = 0;
    uint8 Lu8ParallelExitFlag = 0;
    uint8 Lu8SigGroupCnt1,Lu8SigGroupCnt2,Lu8SigGroupCnt3;
    sint16 Ls16ObjDisSub1_2;
    sint16 Ls16ObjDisSub1_2_Abs;
    sint16 Ls16CarMoveDis1_2;
    sint16 Ls16CarMoveDis1_2_Abs;
    sint16 Ls16ObjMoveCarMoveSub1_2;
    uint8 Lu8ParallelWallFlag1_2;
    
    sint16 Ls16ObjMoveDisSubLimit,Ls16ObjMoveCarMoveDisSubLimit;
    sint16 Ls16CarMoveLastDetectToNow,Ls16ObjDisByFollow;
    sint16 Ls16Follow_DetectDisSub;
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    LenuLastIndex = SnsSigGroupFollowGetLastIndex(LenuCurIndex);
    LenuLastBefIndex = SnsSigGroupFollowGetLastIndex(LenuLastIndex);

    if(Gu16CarSpdForSnsUse < 500)
    {
        Ls16ObjMoveDisSubLimit = CORNER_CORNER_MOVE_DIS_FOR_5KM;
        Ls16ObjMoveCarMoveDisSubLimit = CORNER_OBJ_CAR_MOVE_DIS_FOR_5KM;
    }
    else if(Gu16CarSpdForSnsUse < 1000)
    {
        Ls16ObjMoveDisSubLimit = CORNER_CORNER_MOVE_DIS_FOR_10KM;
        Ls16ObjMoveCarMoveDisSubLimit = CORNER_OBJ_CAR_MOVE_DIS_FOR_10KM;
    }
    else
    {
        Ls16ObjMoveDisSubLimit = CORNER_CORNER_MOVE_DIS_FOR_15KM;
        Ls16ObjMoveCarMoveDisSubLimit = CORNER_OBJ_CAR_MOVE_DIS_FOR_15KM;
    }
    
    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Lu8ParallelWallFlag1_2 = 0;

    /* 上个周期探测到本周期探测车辆移动的距离 */
    Ls16CarMoveDis1_2 = (sint16)LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub;
    Ls16CarMoveDis1_2_Abs = ABS_VALUE(Ls16CarMoveDis1_2);

    /* Step1：计算上一次探测更新到本周期车辆移动的距离；根据车辆移动的距离推算出本周期的预估距离 */
    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow += LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub;
    Ls16CarMoveLastDetectToNow = (sint16)LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow;

    Ls16ObjDisByFollow = FOLLOW_DIS_SUB_INVALID;

    if(Ls16CarMoveLastDetectToNow < 0)
    {
        if(LpstrSnsSigGroupDisFollow->u16FollowDis < ABS_VALUE(Ls16CarMoveLastDetectToNow))
        {
            /* 防止跟踪错误，跟踪距离比待减去的距离还小，直接退出跟踪 */
            LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
            LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
            LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
            LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
            LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
            LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 0;
        }
        else
        {
            Ls16ObjDisByFollow = (sint16)LpstrSnsSigGroupDisFollow->u16DisDetect_Last + Ls16CarMoveLastDetectToNow;
        }
    }
    else
    {
        Ls16ObjDisByFollow = (sint16)LpstrSnsSigGroupDisFollow->u16DisDetect_Last + Ls16CarMoveLastDetectToNow;
    }
    if(LpstrSnsSigGroupDisFollow->u16FollowDis != PDC_OBJ_FOLLOW_INVALID_DIS)
    {
        for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
        {
            Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
            Lu8ParallelWallFlag1_2 = 0;

            /* 先用本轮和上一轮进行距离差值比较 */
            for(Lu8SigGroupCnt2 = 0; Lu8SigGroupCnt2 < LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u8SigGroupCnt; Lu8SigGroupCnt2++)
            {
                /** 比较本轮和上一轮的差值 */
                if((LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]!=SNS_INVALID_DIS))
                {
                    Ls16ObjDisSub1_2 = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]-LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2];
                    Ls16ObjDisSub1_2_Abs = ABS_VALUE(Ls16ObjDisSub1_2);
                    if(Ls16ObjDisSub1_2_Abs < Ls16ObjMoveDisSubLimit)
                    {
                        /* 计算障碍物移动的距离和车辆移动距离的差值 */
                        Ls16ObjMoveCarMoveSub1_2 = ABS(Ls16CarMoveDis1_2,Ls16ObjDisSub1_2);
                        if((Ls16ObjMoveCarMoveSub1_2 < Ls16ObjMoveCarMoveDisSubLimit)||(Ls16ObjDisSub1_2_Abs < CORNER_OBJ_PARALLEL_WALL_DIS_SUB))
                        {
                            if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < CORNER_DIRECT_WALL_DETECT_DIS_MAX)
                            {
                                if((Ls16ObjDisSub1_2_Abs + (Ls16CarMoveDis1_2_Abs/2))< Ls16ObjMoveCarMoveSub1_2)
                                {
                                    Lu8ParallelWallFlag1_2 = 1;
                                }
                                else if((Ls16ObjDisSub1_2_Abs < CORNER_OBJ_DIRECT_WALL_DIS_SUB)&&(Ls16CarMoveDis1_2_Abs > CORNER_DIRECT_WALL_CAR_DIS_LIMIT))
                                {
                                    Lu8ParallelWallFlag1_2 = 1;
                                }
                            }
                            break;
                        }
                        else
                        {
                            Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
                            Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
                            Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
                        }
                    }
                    else
                    {
                        Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
                        Ls16ObjDisSub1_2_Abs = FOLLOW_DIS_SUB_INVALID;
                        Ls16ObjMoveCarMoveSub1_2 = FOLLOW_DIS_SUB_INVALID;
                    }
                }
            }

            if(Lu8ParallelWallFlag1_2)
            {
                /* 平行墙场景，直接更新最新的距离 */
                LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                
                LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                
                LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];;
                LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                LpstrSnsSigGroupDisFollow->u8ParallelWallCnt++;
                break;
            }
            else
            {
                Ls16Follow_DetectDisSub = FOLLOW_DIS_SUB_INVALID;
                if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)
                {
                    Ls16Follow_DetectDisSub = ABS(Ls16ObjDisByFollow,LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]);
                }
                if(Ls16Follow_DetectDisSub < Ls16ObjMoveCarMoveDisSubLimit)
                {
                    /* 预估距离和探测距离差值满足跟踪条件，直接更新最新的距离 */
                    LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                    
                    LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                    
                    LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];;
                    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                    LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
                    SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                    if(LpstrSnsSigGroupDisFollow->u8ParallelWallCnt > 0)
                    {
                        LpstrSnsSigGroupDisFollow->u8ParallelWallCnt--;
                    }
                    break;
                }
            }
        }
        
        if(LpstrSnsSigGroupDisFollow->u8ParallelWallCnt > CORNER_PARALLEL_WALL_CNT)
        {
            LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 1;
            LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
        }
        
        if(Lu8SigGroupCnt1 == LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8SigGroupCnt)
        {
            if (LpstrSnsSigGroupDisFollow->u8ParallelWallCnt != 0)
            {
                Lu8ParallelExitFlag = 1;
            }

            if (Gu16CarSpdForSnsUse > 300)
            {
                Lu8ExitFollowCnt = CORNER_FOLLOW_FAST_EXIT_CNT;
            }
            else
            {
                Lu8ExitFollowCnt = CORNER_FOLLOW_EXIT_CNT;
            }

            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt++;
            if(!LpstrSnsSigGroupDisFollow->u8ParallelWallFlag)
            {
                LpstrSnsSigGroupDisFollow->u16FollowDis = Ls16ObjDisByFollow;   /* 非平行墙场景，使用预估距离进行更新 */
            }
            SnsFollowDisProSub(&LpstrSnsSigGroupDisFollow->enuFollowDisPro,LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType);
            if(LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt > Lu8ExitFollowCnt ||  Lu8ParallelExitFlag == 1)
            {
                /** @brief: 退出跟踪 */
                LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
                LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
                LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
                LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                LpstrSnsSigGroupDisFollow->u8ParallelWallCnt = 0;
                LpstrSnsSigGroupDisFollow->u8ParallelWallFlag = 0;
            }

            if(Lu8SigGroupCnt1 != 0)
            {
                SnsSigGroupDisFollowForCornerRightMoveSearch(LeGroup,LenuSnsCh,LpstrSnsSigGroupDisFollow);
            }
        }
        else if(Lu8SigGroupCnt1 > 0)
        {
            SnsSigGroupDisFollowForCornerRightMoveSearch(LeGroup,LenuSnsCh,LpstrSnsSigGroupDisFollow);
        }
    }
#if 0
    if((GenuSnsGroup == 0x00)&&(GenuSnsCh == 0x04))
    {
        printf("FR Master Follow,Time:%.3f,FollowDis:%d,Flag:%d,NewDis:%d,LastDis:%d,LastBefDis:%d,Sub1_2:%d,Obj_CarSub1_2:%d,WallFlag1_2:%d,Cnt:%d,WallReal:%d\r\n",GfMessageTime,\
            LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDisFollow->u8DisFollowFlag,\
            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[0],LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u16ActualDis[0],\
            LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u16ActualDis[0],\
            Ls16ObjDisSub1_2_Abs,Ls16ObjMoveCarMoveSub1_2,Lu8ParallelWallFlag1_2,\
            LpstrSnsSigGroupDisFollow->u8ParallelWallCnt,LpstrSnsSigGroupDisFollow->u8ParallelWallFlag);
    }
#endif
}

/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForSideMasterMoveSearch
 * 
 * 功能描述: 侧边探头主发距离在车辆运动状态下的匹配查询策略；主要考虑定定、扫扫的发波序列
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应主发探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-15 14:32   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForSideMasterMoveSearch(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    Sns_CycleType LenuCurIndex,LenuLastIndex,LenuLastBefIndex;
    uint8 Lu8SigGroupCnt1,Lu8SigGroupCnt2,Lu8SigGroupCnt3;
    sint16 Ls16ObjDisSub1_2,Ls16ObjDisSub1_3;
    sint16 Ls16CarMoveDis1_2,Ls16CarMoveDis1_3;
    uint16 Lu16EchoDisSubLimit1_2,Lu16EchoDisSubLimit1_3;
    uint8 Lu8ObjValidFlag = 0;
    uint8 Lu8ObjValidCheckFlag = 0;
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    LenuLastIndex = SnsSigGroupFollowGetLastIndex(LenuCurIndex);
    LenuLastBefIndex = SnsSigGroupFollowGetLastIndex(LenuLastIndex);
    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;

    if(Gu16CarSpdForSnsUse < SIDE_OBJ_FOLLOW_CAR_SPEED_LIMIT)
    {
        /* Step1：计算车辆移动的距离 */
        /* 上个周期探测到本周期探测车辆移动的距离 */
        Ls16CarMoveDis1_2 = (sint16)LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub;
        /* 上上个周期探测到本周期探测车辆移动的距离 */
        Ls16CarMoveDis1_3 = (sint16)(LpstrSnsSigGroupDataCache->SysDataBuf[LenuLastIndex].fCarMoveDisSub + LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub);
        Lu16EchoDisSubLimit1_2 = SnsSigGroupSideEchoDisLimit(Ls16CarMoveDis1_2);
        Lu16EchoDisSubLimit1_3 = SnsSigGroupSideEchoDisLimit(Ls16CarMoveDis1_3);
        for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
        {
            Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
            
            /* 先用本轮和上一轮进行距离差值比较 */
            for(Lu8SigGroupCnt2 = 0; Lu8SigGroupCnt2 < LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u8SigGroupCnt; Lu8SigGroupCnt2++)
            {
                /** 比较本轮和上一轮的差值 */
                if((LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]!=SNS_INVALID_DIS))
                {
                    Ls16ObjDisSub1_2 = ABS(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]);
                    if(Ls16ObjDisSub1_2 < Lu16EchoDisSubLimit1_2)
                    {
                        break;
                    }
                    else
                    {
                        Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
                    }
                }
            }
        
            /* 本轮数据和上上一轮数据做比较 */
            for(Lu8SigGroupCnt3 = 0; Lu8SigGroupCnt3 < LpstrSnsSigGroupDataCache->MasterBuf[LenuLastBefIndex].u8SigGroupCnt; Lu8SigGroupCnt3++)
            {
                if((LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->MasterBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]!=SNS_INVALID_DIS))
                {
                    Ls16ObjDisSub1_3 = ABS(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->MasterBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]);
                    if(Ls16ObjDisSub1_3 < Lu16EchoDisSubLimit1_3)
                    {
                        break;
                    }
                    else
                    {
                        Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
                    }
                }
            }
    
            if((Ls16ObjDisSub1_2 != FOLLOW_DIS_SUB_INVALID)&&(Ls16ObjDisSub1_3 != FOLLOW_DIS_SUB_INVALID))
            {
                Lu8ObjValidFlag = VALID;
                if(Ls16ObjDisSub1_2 < SIDE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                {
                    if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                    }
                }
                else
                {
                    if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                    }
                }
                break;
            }
            else if(Ls16ObjDisSub1_2 != FOLLOW_DIS_SUB_INVALID)
            {
                /* 由于侧边雷达是2帧定频、2帧扫频，因此本次和上次对比OK就需要输出,低速时需要分探头探测距离和定扫频进行区分 */
                if(Gu16CarSpdForSnsUse > SIDE_HIGH_SPD_FAST_CONFIRM_SPD)
                {
                    if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < SIDE_HIGH_SPD_FAST_CONFIRM_DIS)
                    {
                        Lu8ObjValidFlag = VALID;
                        if(Ls16ObjDisSub1_2 < SIDE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                        {
                            if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                            }
                        }
                        else
                        {
                            if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                            }
                        }
                        break;
                    }
                }
                else
                {
                    if(LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType > PDC_SNS_MEAS_STD)
                    {
                        if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] > SIDE_CHIRP_FAST_CONFIRM_DIS)
                        {
                            Lu8ObjValidFlag = VALID;
                            if(Ls16ObjDisSub1_2 < SIDE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                            {
                                if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                                {
                                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                                }
                                else
                                {
                                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                                }
                            }
                            else
                            {
                                if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                                {
                                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                                }
                                else
                                {
                                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                                }
                            }
                            break;
                        }
                    }
                    else
                    {
                        if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < SIDE_STANDARD_FAST_CONFIRM_DIS)
                        {
                            if(Ls16ObjDisSub1_2 < SIDE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                            {
                                if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                                {
                                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                                }
                                else
                                {
                                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                                }
                            }
                            else
                            {
                                if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                                {
                                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                                }
                                else
                                {
                                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                                }
                            }
                            if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_HIGH_CONFIRDENCE_DIS)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_VERY_HIGH;
                            }

                            if (Gu16CarSpdForSnsUse < 300)
                            {
                                if(Ls16ObjDisSub1_3 > Lu16EchoDisSubLimit1_3)
                                {
                                    break;
                                }
                            }

                            Lu8ObjValidFlag = VALID;
                            break;
                        }
                    }
                }
            }
            else if(Ls16ObjDisSub1_3 != FOLLOW_DIS_SUB_INVALID)
            {
                /* 对于中间雷达，车辆运动时，在高速时，需要快速生成Map DE ，快速确认障碍物，才采用此策略 */
                if(Gu16CarSpdForSnsUse > SIDE_HIGH_SPD_FAST_CONFIRM_SPD)
                {
                    if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < SIDE_HIGH_SPD_FAST_CONFIRM_DIS)
                    {
                        Lu8ObjValidFlag = VALID;
                        if(Ls16ObjDisSub1_2 < SIDE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                        {
                            if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                            }
                        }
                        else
                        {
                            if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                            }
                        }
                        break;
                    }
                }
            }
        }
    }
    else
    {
        /* 超过10km/h，只使用最近的距离 */
        LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
        LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[0];
        LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[0];
        LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
        
        LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[0];
        LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[0];
        LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[0];
        LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[0];
        LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[0];
        LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[0];
    }

    if(Lu8ObjValidFlag)
    {
        if(!LpstrSnsSigGroupDisFollow->u8DisFollowFlag)
        {
            Lu8ObjValidCheckFlag = 1;
        }
        else
        {
            /* 已经跟踪上的，再后续的比较中，只考虑近距离快速进入的，远离的等已经跟踪上的障碍物消失后再重新匹配 */
            if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < LpstrSnsSigGroupDisFollow->u16FollowDis)
            {
                Lu8ObjValidCheckFlag = 1;
            }
        }
        
        if(Lu8ObjValidCheckFlag)
        {
            LpstrSnsSigGroupDisFollow->u8DisFollowFlag = VALID;
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
            LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
            
            LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
        }
    }

#if 0
        if((GenuSnsGroup == 0x00)&&(GenuSnsCh == 0x04))
        {
            printf("FR Master Search,Time:%.3f,FollowDis:%d,Flag:%d,NewDis:%d,LastDis:%d,LastBefDis:%d,Sub1_2:%d,Sub1_3:%d,Obj_CarSub1_2:%d,Obj_CarSub1_3:%d,WallFlag1:%d,WallFlag2:%d\r\n",GfMessageTime,\
                LpstrSnsSigGroupDisFollow->u16FollowDis,Lu8ObjValidCheckFlag,\
                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[0],LpstrSnsSigGroupDataCache->MasterBuf[LenuLastIndex].u16ActualDis[0],\
                LpstrSnsSigGroupDataCache->MasterBuf[LenuLastBefIndex].u16ActualDis[0],\
                Ls16ObjDisSub1_2_Abs,Ls16ObjDisSub1_3_Abs,\
                Ls16ObjMoveCarMoveSub1_2,Ls16ObjMoveCarMoveSub1_3,Lu8ParallelWallFlag1,Lu8ParallelWallFlag2);
        }
#endif

}
/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForSideLeftMoveSearch
 * 
 * 功能描述: 侧边探头左侦听距离在车辆运动状态下的匹配查询策略；主要考虑定定、扫扫的发波序列
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应主发探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-15 14:32   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForSideLeftMoveSearch(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    Sns_CycleType LenuCurIndex,LenuLastIndex,LenuLastBefIndex;
    uint8 Lu8SigGroupCnt1,Lu8SigGroupCnt2,Lu8SigGroupCnt3;
    sint16 Ls16ObjDisSub1_2,Ls16ObjDisSub1_3;
    sint16 Ls16CarMoveDis1_2,Ls16CarMoveDis1_3;
    uint16 Lu16EchoDisSubLimit1_2,Lu16EchoDisSubLimit1_3;
    uint8 Lu8ObjValidFlag = 0;
    uint8 Lu8ObjValidCheckFlag = 0;
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    LenuLastIndex = SnsSigGroupFollowGetLastIndex(LenuCurIndex);
    LenuLastBefIndex = SnsSigGroupFollowGetLastIndex(LenuLastIndex);
    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;

    if(Gu16CarSpdForSnsUse < SIDE_OBJ_FOLLOW_CAR_SPEED_LIMIT)
    {
        /* Step1：计算车辆移动的距离 */
        /* 上个周期探测到本周期探测车辆移动的距离 */
        Ls16CarMoveDis1_2 = (sint16)LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub;
        /* 上上个周期探测到本周期探测车辆移动的距离 */
        Ls16CarMoveDis1_3 = (sint16)(LpstrSnsSigGroupDataCache->SysDataBuf[LenuLastIndex].fCarMoveDisSub + LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub);
        Lu16EchoDisSubLimit1_2 = SnsSigGroupSideEchoDisLimit(Ls16CarMoveDis1_2);
        Lu16EchoDisSubLimit1_3 = SnsSigGroupSideEchoDisLimit(Ls16CarMoveDis1_3);
        for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
        {
            Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
            
            /* 先用本轮和上一轮进行距离差值比较 */
            for(Lu8SigGroupCnt2 = 0; Lu8SigGroupCnt2 < LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u8SigGroupCnt; Lu8SigGroupCnt2++)
            {
                /** 比较本轮和上一轮的差值 */
                if((LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]!=SNS_INVALID_DIS))
                {
                    Ls16ObjDisSub1_2 = ABS(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]);
                    if(Ls16ObjDisSub1_2 < Lu16EchoDisSubLimit1_2)
                    {
                        break;
                    }
                    else
                    {
                        Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
                    }
                }
            }
        
            /* 本轮数据和上上一轮数据做比较 */
            for(Lu8SigGroupCnt3 = 0; Lu8SigGroupCnt3 < LpstrSnsSigGroupDataCache->LeftBuf[LenuLastBefIndex].u8SigGroupCnt; Lu8SigGroupCnt3++)
            {
                if((LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->LeftBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]!=SNS_INVALID_DIS))
                {
                    Ls16ObjDisSub1_3 = ABS(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->LeftBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]);
                    if(Ls16ObjDisSub1_3 < Lu16EchoDisSubLimit1_3)
                    {
                        break;
                    }
                    else
                    {
                        Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
                    }
                }
            }
    
            if((Ls16ObjDisSub1_2 != FOLLOW_DIS_SUB_INVALID)&&(Ls16ObjDisSub1_3 != FOLLOW_DIS_SUB_INVALID))
            {
                Lu8ObjValidFlag = VALID;
                if(Ls16ObjDisSub1_2 < SIDE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                {
                    if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                    }
                }
                else
                {
                    if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                    }
                }
                break;
            }
            else if(Ls16ObjDisSub1_2 != FOLLOW_DIS_SUB_INVALID)
            {
                /* 由于侧边雷达是2帧定频、2帧扫频，因此本次和上次对比OK就需要输出,低速时需要分探头探测距离和定扫频进行区分 */
                if(Gu16CarSpdForSnsUse > SIDE_HIGH_SPD_FAST_CONFIRM_SPD)
                {
                    if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < SIDE_HIGH_SPD_FAST_CONFIRM_DIS)
                    {
                        Lu8ObjValidFlag = VALID;
                        if(Ls16ObjDisSub1_2 < SIDE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                        {
                            if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                            }
                        }
                        else
                        {
                            if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                            }
                        }
                        break;
                    }
                }
                else
                {
                    if(LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType > PDC_SNS_MEAS_STD)
                    {
                        if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] > SIDE_CHIRP_FAST_CONFIRM_DIS)
                        {
                            Lu8ObjValidFlag = VALID;
                            if(Ls16ObjDisSub1_2 < SIDE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                            {
                                if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                                {
                                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                                }
                                else
                                {
                                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                                }
                            }
                            else
                            {
                                if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                                {
                                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                                }
                                else
                                {
                                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                                }
                            }
                            break;
                        }
                    }
                    else
                    {
                        if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < SIDE_STANDARD_FAST_CONFIRM_DIS)
                        {

                            if(Ls16ObjDisSub1_2 < SIDE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                            {
                                if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                                {
                                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                                }
                                else
                                {
                                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                                }
                            }
                            else
                            {
                                if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                                {
                                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                                }
                                else
                                {
                                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                                }
                            }
                            if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_HIGH_CONFIRDENCE_DIS)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_VERY_HIGH;
                            }

                            if (Gu16CarSpdForSnsUse < 300)
                            {
                                if(Ls16ObjDisSub1_3 > Lu16EchoDisSubLimit1_3)
                                {
                                    break;
                                }
                            }

                            Lu8ObjValidFlag = VALID;
                            break;
                        }
                    }
                }
            }
            else if(Ls16ObjDisSub1_3 != FOLLOW_DIS_SUB_INVALID)
            {
                /* 对于中间雷达，车辆运动时，在高速时，需要快速生成Map DE ，快速确认障碍物，才采用此策略 */
                if(Gu16CarSpdForSnsUse > SIDE_HIGH_SPD_FAST_CONFIRM_SPD)
                {
                    if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < SIDE_HIGH_SPD_FAST_CONFIRM_DIS)
                    {
                        Lu8ObjValidFlag = VALID;
                        if(Ls16ObjDisSub1_2 < SIDE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                        {
                            if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                            }
                        }
                        else
                        {
                            if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                            }
                        }
                        break;
                    }
                }
            }
        }
    }
    else
    {
        /* 超过10km/h，只使用最近的距离 */
        LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
        LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[0];
        LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[0];
        LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
        
        LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[0];
        LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[0];
        LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[0];
        LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[0];
        LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[0];
        LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[0];
    }

    if(Lu8ObjValidFlag)
    {
        if(!LpstrSnsSigGroupDisFollow->u8DisFollowFlag)
        {
            Lu8ObjValidCheckFlag = 1;
        }
        else
        {
            /* 已经跟踪上的，再后续的比较中，只考虑近距离快速进入的，远离的等已经跟踪上的障碍物消失后再重新匹配 */
            if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < LpstrSnsSigGroupDisFollow->u16FollowDis)
            {
                Lu8ObjValidCheckFlag = 1;
            }
        }
        
        if(Lu8ObjValidCheckFlag)
        {
            LpstrSnsSigGroupDisFollow->u8DisFollowFlag = VALID;
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
            LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
            
            LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
        }
    }

#if 0
        if((GenuSnsGroup == 0x00)&&(GenuSnsCh == 0x04))
        {
            printf("FRS Left Search,Time:%.3f,FollowDis:%d,Flag:%d,NewDis:%d,LastDis:%d,LastBefDis:%d,Sub1_2:%d,Sub1_3:%d,Spd:%d\r\n",GfMessageTime,\
                LpstrSnsSigGroupDisFollow->u16FollowDis,Lu8ObjValidCheckFlag,\
                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[0],LpstrSnsSigGroupDataCache->LeftBuf[LenuLastIndex].u16ActualDis[0],\
                LpstrSnsSigGroupDataCache->LeftBuf[LenuLastBefIndex].u16ActualDis[0],\
                Ls16ObjDisSub1_2,Ls16ObjDisSub1_3,Gu16CarSpdForSnsUse);
        }
#endif

}
/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForSideRightMoveSearch
 * 
 * 功能描述: 侧边探头右侦听距离在车辆运动状态下的匹配查询策略；主要考虑定定、扫扫的发波序列
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应主发探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-15 14:32   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForSideRightMoveSearch(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    Sns_CycleType LenuCurIndex,LenuLastIndex,LenuLastBefIndex;
    uint8 Lu8SigGroupCnt1,Lu8SigGroupCnt2,Lu8SigGroupCnt3;
    sint16 Ls16ObjDisSub1_2,Ls16ObjDisSub1_3;
    sint16 Ls16CarMoveDis1_2,Ls16CarMoveDis1_3;
    uint16 Lu16EchoDisSubLimit1_2,Lu16EchoDisSubLimit1_3;
    uint8 Lu8ObjValidFlag = 0;
    uint8 Lu8ObjValidCheckFlag = 0;
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    LenuLastIndex = SnsSigGroupFollowGetLastIndex(LenuCurIndex);
    LenuLastBefIndex = SnsSigGroupFollowGetLastIndex(LenuLastIndex);
    Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
    Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;

    if(Gu16CarSpdForSnsUse < SIDE_OBJ_FOLLOW_CAR_SPEED_LIMIT)
    {
        /* Step1：计算车辆移动的距离 */
        /* 上个周期探测到本周期探测车辆移动的距离 */
        Ls16CarMoveDis1_2 = (sint16)LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub;
        /* 上上个周期探测到本周期探测车辆移动的距离 */
        Ls16CarMoveDis1_3 = (sint16)(LpstrSnsSigGroupDataCache->SysDataBuf[LenuLastIndex].fCarMoveDisSub + LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub);
        Lu16EchoDisSubLimit1_2 = SnsSigGroupSideEchoDisLimit(Ls16CarMoveDis1_2);
        Lu16EchoDisSubLimit1_3 = SnsSigGroupSideEchoDisLimit(Ls16CarMoveDis1_3);
        for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
        {
            Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
            Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
            
            /* 先用本轮和上一轮进行距离差值比较 */
            for(Lu8SigGroupCnt2 = 0; Lu8SigGroupCnt2 < LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u8SigGroupCnt; Lu8SigGroupCnt2++)
            {
                /** 比较本轮和上一轮的差值 */
                if((LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]!=SNS_INVALID_DIS))
                {
                    Ls16ObjDisSub1_2 = ABS(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u16ActualDis[Lu8SigGroupCnt2]);
                    if(Ls16ObjDisSub1_2 < Lu16EchoDisSubLimit1_2)
                    {
                        break;
                    }
                    else
                    {
                        Ls16ObjDisSub1_2 = FOLLOW_DIS_SUB_INVALID;
                    }
                }
            }
        
            /* 本轮数据和上上一轮数据做比较 */
            for(Lu8SigGroupCnt3 = 0; Lu8SigGroupCnt3 < LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u8SigGroupCnt; Lu8SigGroupCnt3++)
            {
                if((LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)&&(LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]!=SNS_INVALID_DIS))
                {
                    Ls16ObjDisSub1_3 = ABS(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u16ActualDis[Lu8SigGroupCnt3]);
                    if(Ls16ObjDisSub1_3 < Lu16EchoDisSubLimit1_3)
                    {
                        break;
                    }
                    else
                    {
                        Ls16ObjDisSub1_3 = FOLLOW_DIS_SUB_INVALID;
                    }
                }
            }
    
            if((Ls16ObjDisSub1_2 != FOLLOW_DIS_SUB_INVALID)&&(Ls16ObjDisSub1_3 != FOLLOW_DIS_SUB_INVALID))
            {
                Lu8ObjValidFlag = VALID;
                if(Ls16ObjDisSub1_2 < SIDE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                {
                    if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                    }
                }
                else
                {
                    if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                    }
                    else
                    {
                        LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                    }
                }
                break;
            }
            else if(Ls16ObjDisSub1_2 != FOLLOW_DIS_SUB_INVALID)
            {
                /* 由于侧边雷达是2帧定频、2帧扫频，因此本次和上次对比OK就需要输出,低速时需要分探头探测距离和定扫频进行区分 */
                if(Gu16CarSpdForSnsUse > SIDE_HIGH_SPD_FAST_CONFIRM_SPD)
                {
                    if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < SIDE_HIGH_SPD_FAST_CONFIRM_DIS)
                    {
                        Lu8ObjValidFlag = VALID;
                        if(Ls16ObjDisSub1_2 < SIDE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                        {
                            if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                            }
                        }
                        else
                        {
                            if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                            }
                        }
                        break;
                    }
                }
                else
                {
                    if(LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType > PDC_SNS_MEAS_STD)
                    {
                        if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] > SIDE_CHIRP_FAST_CONFIRM_DIS)
                        {
                            Lu8ObjValidFlag = VALID;
                            if(Ls16ObjDisSub1_2 < SIDE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                            {
                                if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                                {
                                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                                }
                                else
                                {
                                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                                }
                            }
                            else
                            {
                                if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                                {
                                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                                }
                                else
                                {
                                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                                }
                            }
                            break;
                        }
                    }
                    else
                    {
                        if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < SIDE_STANDARD_FAST_CONFIRM_DIS)
                        {
                            Lu8ObjValidFlag = VALID;
                            if(Ls16ObjDisSub1_2 < SIDE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                            {
                                if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                                {
                                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                                }
                                else
                                {
                                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                                }
                            }
                            else
                            {
                                if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                                {
                                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                                }
                                else
                                {
                                    LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                                }
                            }
                            if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < FOLLOW_HIGH_CONFIRDENCE_DIS)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_VERY_HIGH;
                            }

                            if (Gu16CarSpdForSnsUse < 300)
                            {
                                if(Ls16ObjDisSub1_3 > Lu16EchoDisSubLimit1_3)
                                {
                                    break;
                                }
                            }
                            
                            Lu8ObjValidFlag = VALID;
                            break;
                        }
                    }
                }
            }
            else if(Ls16ObjDisSub1_3 != FOLLOW_DIS_SUB_INVALID)
            {
                /* 对于中间雷达，车辆运动时，在高速时，需要快速生成Map DE ，快速确认障碍物，才采用此策略 */
                if(Gu16CarSpdForSnsUse > SIDE_HIGH_SPD_FAST_CONFIRM_SPD)
                {
                    if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < SIDE_HIGH_SPD_FAST_CONFIRM_DIS)
                    {
                        Lu8ObjValidFlag = VALID;
                        if(Ls16ObjDisSub1_2 < SIDE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO)
                        {
                            if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_MEDIUM;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                            }
                        }
                        else
                        {
                            if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt1] == OBJ_BIGWALL_TYPE)
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_UNKNOWN;
                            }
                            else
                            {
                                LpstrSnsSigGroupDisFollow->enuFollowDisPro = SNS_PROB_LOW;
                            }
                        }
                        break;
                    }
                }
            }
        }
    }
    else
    {
        /* 超过10km/h，只使用最近的距离 */
        LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
        LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[0];
        LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[0];
        LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
        
        LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[0];
        LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[0];
        LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[0];
        LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[0];
        LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[0];
        LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[0];
    }

    if(Lu8ObjValidFlag)
    {
        if(!LpstrSnsSigGroupDisFollow->u8DisFollowFlag)
        {
            Lu8ObjValidCheckFlag = 1;
        }
        else
        {
            /* 已经跟踪上的，再后续的比较中，只考虑近距离快速进入的，远离的等已经跟踪上的障碍物消失后再重新匹配 */
            if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1] < LpstrSnsSigGroupDisFollow->u16FollowDis)
            {
                Lu8ObjValidCheckFlag = 1;
            }
        }
        
        if(Lu8ObjValidCheckFlag)
        {
            LpstrSnsSigGroupDisFollow->u8DisFollowFlag = VALID;
            LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
            LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
            
            LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
            LpstrSnsSigGroupDisFollow->u8FollowUpdateFlag = 1;
        }
    }

#if 0
        if((GenuSnsGroup == 0x00)&&(GenuSnsCh == 0x04))
        {
            printf("FR Master Search,Time:%.3f,FollowDis:%d,Flag:%d,NewDis:%d,LastDis:%d,LastBefDis:%d,Sub1_2:%d,Sub1_3:%d,Obj_CarSub1_2:%d,Obj_CarSub1_3:%d,WallFlag1:%d,WallFlag2:%d\r\n",GfMessageTime,\
                LpstrSnsSigGroupDisFollow->u16FollowDis,Lu8ObjValidCheckFlag,\
                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[0],LpstrSnsSigGroupDataCache->RightBuf[LenuLastIndex].u16ActualDis[0],\
                LpstrSnsSigGroupDataCache->RightBuf[LenuLastBefIndex].u16ActualDis[0],\
                Ls16ObjDisSub1_2_Abs,Ls16ObjDisSub1_3_Abs,\
                Ls16ObjMoveCarMoveSub1_2,Ls16ObjMoveCarMoveSub1_3,Lu8ParallelWallFlag1,Lu8ParallelWallFlag2);
        }
#endif

}

/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForSideMasterMoveFollow
 * 
 * 功能描述: 侧边探头主发距离在车辆运动状态下的距离跟踪策略；主要考虑定定、扫扫的发波序列
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应主发探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-15 15:19   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForSideMasterMoveFollow(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    Sns_CycleType LenuCurIndex;
    uint8 Lu8SigGroupCnt1;
    sint16 Ls16Follow_DetectDisSub;
    sint16 Ls16CarMoveLastDetectToNow;
    uint16 Lu16EchoDisSubLimit;

    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    Ls16Follow_DetectDisSub = FOLLOW_DIS_SUB_INVALID;


    if(Gu16CarSpdForSnsUse > (SIDE_OBJ_FOLLOW_CAR_SPEED_LIMIT+100))
    {
        LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
        SnsSigGroupDisFollowForSideMasterMoveSearch(LeGroup,LenuSnsCh,LpstrSnsSigGroupDisFollow);
    }
    else
    {
        /* Step1：计算车辆移动的距离 */
        LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow += LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub;
        Ls16CarMoveLastDetectToNow = (sint16)LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow;
        Lu16EchoDisSubLimit = SnsSigGroupSideEchoDisLimit(Ls16CarMoveLastDetectToNow);
        if(LpstrSnsSigGroupDisFollow->u16FollowDis != PDC_OBJ_FOLLOW_INVALID_DIS)
        {
            for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
            {
                if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)
                {
                    Ls16Follow_DetectDisSub = ABS(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDisFollow->u16FollowDis);
                }
                else
                {
                    Ls16Follow_DetectDisSub = FOLLOW_DIS_SUB_INVALID;
                }
                if(Ls16Follow_DetectDisSub < Lu16EchoDisSubLimit)
                {
                    /** @brief: 预估距离与探测距离在误差范围内 */ 
                    LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                    
                    LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                    
                    LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];;
                    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                    SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                    break;
                }
            }
            if(Lu8SigGroupCnt1 == LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8SigGroupCnt)
            {
                LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt++;
                SnsFollowDisProSub(&LpstrSnsSigGroupDisFollow->enuFollowDisPro,LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType);
                if(LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt > SIDE_FOLLOW_EXIT_CNT)
                {
                    /** @brief: 退出跟踪 */
                    LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
                    LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                    LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
                    LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
                    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                }
            
                if(Lu8SigGroupCnt1 != 0)
                {
                    SnsSigGroupDisFollowForSideMasterMoveSearch(LeGroup,LenuSnsCh,LpstrSnsSigGroupDisFollow);
                }
            }
            else if(Lu8SigGroupCnt1 > 0)
            {
                SnsSigGroupDisFollowForSideMasterMoveSearch(LeGroup,LenuSnsCh,LpstrSnsSigGroupDisFollow);
            }
        }
        else
        {
            LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
        }
    }
}
/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForSideLeftMoveFollow
 * 
 * 功能描述: 侧边探头左侦听距离在车辆运动状态下的距离跟踪策略；主要考虑定定、扫扫的发波序列
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应主发探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-15 15:19   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForSideLeftMoveFollow(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    Sns_CycleType LenuCurIndex;
    uint8 Lu8SigGroupCnt1;
    sint16 Ls16Follow_DetectDisSub;
    sint16 Ls16CarMoveLastDetectToNow;
    uint16 Lu16EchoDisSubLimit;

    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    Ls16Follow_DetectDisSub = FOLLOW_DIS_SUB_INVALID;


    if(Gu16CarSpdForSnsUse > (SIDE_OBJ_FOLLOW_CAR_SPEED_LIMIT+100))
    {
        LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
        SnsSigGroupDisFollowForSideLeftMoveSearch(LeGroup,LenuSnsCh,LpstrSnsSigGroupDisFollow);
    }
    else
    {
        /* Step1：计算车辆移动的距离 */
        LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow += LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub;
        Ls16CarMoveLastDetectToNow = (sint16)LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow;
        Lu16EchoDisSubLimit = SnsSigGroupSideEchoDisLimit(Ls16CarMoveLastDetectToNow);
        if(LpstrSnsSigGroupDisFollow->u16FollowDis != PDC_OBJ_FOLLOW_INVALID_DIS)
        {
            for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
            {
                if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)
                {
                    Ls16Follow_DetectDisSub = ABS(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDisFollow->u16FollowDis);
                }
                else
                {
                    Ls16Follow_DetectDisSub = FOLLOW_DIS_SUB_INVALID;
                }
                if(Ls16Follow_DetectDisSub < Lu16EchoDisSubLimit)
                {
                    /** @brief: 预估距离与探测距离在误差范围内 */ 
                    LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                    
                    LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                    
                    LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];;
                    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                    SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                    break;
                }
            }
            if(Lu8SigGroupCnt1 == LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8SigGroupCnt)
            {
                LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt++;
                SnsFollowDisProSub(&LpstrSnsSigGroupDisFollow->enuFollowDisPro,LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType);
                if(LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt > SIDE_FOLLOW_EXIT_CNT)
                {
                    /** @brief: 退出跟踪 */
                    LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
                    LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                    LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
                    LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
                    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                }
            
                if(Lu8SigGroupCnt1 != 0)
                {
                    SnsSigGroupDisFollowForSideLeftMoveSearch(LeGroup,LenuSnsCh,LpstrSnsSigGroupDisFollow);
                }
            }
            else if(Lu8SigGroupCnt1 > 0)
            {
                SnsSigGroupDisFollowForSideLeftMoveSearch(LeGroup,LenuSnsCh,LpstrSnsSigGroupDisFollow);
            }
        }
        else
        {
            LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
        }
    }
#if 0
    if((GenuSnsGroup == 0x00)&&(GenuSnsCh == 0x05))
    {
        printf("FRS Left Follow,Time:%.3f,FollowDis:%d,Flag:%d,NewDis:%d,Follow_DetectDisSub:%d\r\n",GfMessageTime,\
            LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDisFollow->u8DisFollowFlag,\
            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[0],\
            Ls16Follow_DetectDisSub);
    }
#endif
}

/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowForSideRightMoveFollow
 * 
 * 功能描述: 侧边探头右侦听距离在车辆运动状态下的距离跟踪策略；主要考虑定定、扫扫的发波序列
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号;LpstrSnsSigGroupDisFollow--对应主发探头的跟踪数据指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-15 15:19   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowForSideRightMoveFollow(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh,SnsSigGroupDisFollowPointType *LpstrSnsSigGroupDisFollow)
{
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    Sns_CycleType LenuCurIndex;
    uint8 Lu8SigGroupCnt1;
    sint16 Ls16Follow_DetectDisSub;
    sint16 Ls16CarMoveLastDetectToNow;
    uint16 Lu16EchoDisSubLimit;

    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    Ls16Follow_DetectDisSub = FOLLOW_DIS_SUB_INVALID;


    if(Gu16CarSpdForSnsUse > (SIDE_OBJ_FOLLOW_CAR_SPEED_LIMIT+100))
    {
        LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
        SnsSigGroupDisFollowForSideLeftMoveSearch(LeGroup,LenuSnsCh,LpstrSnsSigGroupDisFollow);
    }
    else
    {
        /* Step1：计算车辆移动的距离 */
        LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow += LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub;
        Ls16CarMoveLastDetectToNow = (sint16)LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow;
        Lu16EchoDisSubLimit = SnsSigGroupSideEchoDisLimit(Ls16CarMoveLastDetectToNow);
        if(LpstrSnsSigGroupDisFollow->u16FollowDis != PDC_OBJ_FOLLOW_INVALID_DIS)
        {
            for(Lu8SigGroupCnt1 = 0; Lu8SigGroupCnt1 < LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8SigGroupCnt; Lu8SigGroupCnt1++)
            {
                if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1]!=SNS_INVALID_DIS)
                {
                    Ls16Follow_DetectDisSub = ABS(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1],LpstrSnsSigGroupDisFollow->u16FollowDis);
                }
                else
                {
                    Ls16Follow_DetectDisSub = FOLLOW_DIS_SUB_INVALID;
                }
                if(Ls16Follow_DetectDisSub < Lu16EchoDisSubLimit)
                {
                    /** @brief: 预估距离与探测距离在误差范围内 */ 
                    LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                    
                    LpstrSnsSigGroupDisFollow->u16DisDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16EchoHeightDetect_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16FirstHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondDis_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16SecondHeight_New = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt1];
                    
                    LpstrSnsSigGroupDisFollow->u16FollowDis = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];
                    LpstrSnsSigGroupDisFollow->u16DisDetect_Last = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt1];;
                    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                    SnsFollowDisProAdd(&LpstrSnsSigGroupDisFollow->enuFollowDisPro);
                    break;
                }
            }
            if(Lu8SigGroupCnt1 == LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8SigGroupCnt)
            {
                LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt++;
                SnsFollowDisProSub(&LpstrSnsSigGroupDisFollow->enuFollowDisPro,LpstrSnsSigGroupDisFollow->u16FollowDis,LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType);
                if(LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt > SIDE_FOLLOW_EXIT_CNT)
                {
                    /** @brief: 退出跟踪 */
                    LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
                    LpstrSnsSigGroupDisFollow->u8DisFollowInvalidCnt = 0;
                    LpstrSnsSigGroupDisFollow->u16FollowDis = PDC_OBJ_FOLLOW_INVALID_DIS;
                    LpstrSnsSigGroupDisFollow->u16DisDetect_Last = PDC_OBJ_FOLLOW_INVALID_DIS;
                    LpstrSnsSigGroupDisFollow->fCarMoveLastDetectToNow = 0;
                }
            
                if(Lu8SigGroupCnt1 != 0)
                {
                    SnsSigGroupDisFollowForSideRightMoveSearch(LeGroup,LenuSnsCh,LpstrSnsSigGroupDisFollow);
                }
            }
            else if(Lu8SigGroupCnt1 > 0)
            {
                SnsSigGroupDisFollowForSideRightMoveSearch(LeGroup,LenuSnsCh,LpstrSnsSigGroupDisFollow);
            }
        }
        else
        {
            LpstrSnsSigGroupDisFollow->u8DisFollowFlag = INVALID;
        }
    }
}

/******************************************************************************
 * 函数名称: SnsSigGroupMasterDisFollow
 * 
 * 功能描述: 主发回波信号组距离跟踪
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-07-12 08:44   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void SnsSigGroupMasterDisFollow(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh)
{
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    SnsSigGroupDisFollowType *LpstrSnsSigGroupDisFollow;
    Sns_CycleType LenuCurIndex;
    
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    LpstrSnsSigGroupDisFollow = &GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh];
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;

    LpstrSnsSigGroupDisFollow->MasDisData.u16DisDetect_New = PDC_OBJ_FOLLOW_INVALID_DIS;
    LpstrSnsSigGroupDisFollow->MasDisData.u16EchoHeightDetect_New = SNS_INVALID_HEIGHT;

    LpstrSnsSigGroupDisFollow->MasDisData.u16FirstDis_New = PDC_OBJ_FOLLOW_INVALID_DIS;
    LpstrSnsSigGroupDisFollow->MasDisData.u16FirstHeight_New = SNS_INVALID_HEIGHT;
    LpstrSnsSigGroupDisFollow->MasDisData.u16SecondDis_New = PDC_OBJ_FOLLOW_INVALID_DIS;
    LpstrSnsSigGroupDisFollow->MasDisData.u16SecondHeight_New = SNS_INVALID_HEIGHT;
    
    /* 根据车辆静止及运动做不同的处理 */
    if(LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eCarDir == SNS_CAR_STOP)
    {
        if(!LpstrSnsSigGroupDisFollow->MasDisData.u8DisFollowFlag)
        {
            /* 车辆静止时，前后雷达走一个策略；侧边雷达单独一个策略 */
            if((LenuSnsCh > PDC_SNS_CH_FLS_RLS)&&(LenuSnsCh < PDC_SNS_CH_FRS_RRS))
            {
                SnsSigGroupDisFollowForMiddleCornerMasterStopSearch(LeGroup,LenuSnsCh,&LpstrSnsSigGroupDisFollow->MasDisData);
            }
            else
            {
                SnsSigGroupDisFollowForSideMasterStopSearch(LeGroup,LenuSnsCh,&LpstrSnsSigGroupDisFollow->MasDisData);
            }
        }
        else
        {
            if((LenuSnsCh > PDC_SNS_CH_FLS_RLS)&&(LenuSnsCh < PDC_SNS_CH_FRS_RRS))
            {
                SnsSigGroupDisFollowForMiddleCornerMasterStopFollow(LeGroup,LenuSnsCh,&LpstrSnsSigGroupDisFollow->MasDisData);
            }
            else
            {
                SnsSigGroupDisFollowForSideMasterStopFollow(LeGroup,LenuSnsCh,&LpstrSnsSigGroupDisFollow->MasDisData);
            }
        }
    }
    else
    {
        if(!LpstrSnsSigGroupDisFollow->MasDisData.u8DisFollowFlag)
        {
            /* 没有跟踪上目标，就在历史缓存中检索存在的有效回波 */
            if((LenuSnsCh == PDC_SNS_CH_FML_RML)||(LenuSnsCh == PDC_SNS_CH_FMR_RMR))
            {
                SnsSigGroupDisFollowForMiddleMasterMoveSearch(LeGroup,LenuSnsCh,&LpstrSnsSigGroupDisFollow->MasDisData);
            }
            else if((LenuSnsCh == PDC_SNS_CH_FL_RL)||(LenuSnsCh == PDC_SNS_CH_FR_RR))
            {
                SnsSigGroupDisFollowForCornerMasterMoveSearch(LeGroup,LenuSnsCh,&LpstrSnsSigGroupDisFollow->MasDisData);
            }
            else
            {
                SnsSigGroupDisFollowForSideMasterMoveSearch(LeGroup,LenuSnsCh,&LpstrSnsSigGroupDisFollow->MasDisData);
            }
        }
        else
        {
            /* 已经跟踪上目标，就持续追踪，直至退出 */
            if((LenuSnsCh == PDC_SNS_CH_FML_RML)||(LenuSnsCh == PDC_SNS_CH_FMR_RMR))
            {
                SnsSigGroupDisFollowForMiddleMasterMoveFollow(LeGroup,LenuSnsCh,&LpstrSnsSigGroupDisFollow->MasDisData);
            }
            else if((LenuSnsCh == PDC_SNS_CH_FL_RL)||(LenuSnsCh == PDC_SNS_CH_FR_RR))
            {
                SnsSigGroupDisFollowForCornerMasterMoveFollow(LeGroup,LenuSnsCh,&LpstrSnsSigGroupDisFollow->MasDisData);
            }
            else
            {
                SnsSigGroupDisFollowForSideMasterMoveFollow(LeGroup,LenuSnsCh,&LpstrSnsSigGroupDisFollow->MasDisData);
            }
        }
    }
}


/******************************************************************************
 * 函数名称: SnsSigGroupLeftListenDisFollow
 * 
 * 功能描述: 左侦听回波信号组距离跟踪
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-07-12 08:50   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void SnsSigGroupLeftListenDisFollow(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh)
{
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    SnsSigGroupDisFollowType *LpstrSnsSigGroupDisFollow;
    Sns_CycleType LenuCurIndex;
    
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    LpstrSnsSigGroupDisFollow = &GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh];
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;

    LpstrSnsSigGroupDisFollow->LeftLisDisData.u16DisDetect_New = PDC_OBJ_FOLLOW_INVALID_DIS;
    LpstrSnsSigGroupDisFollow->LeftLisDisData.u16EchoHeightDetect_New = SNS_INVALID_HEIGHT;
    LpstrSnsSigGroupDisFollow->LeftLisDisData.u16FirstDis_New = PDC_OBJ_FOLLOW_INVALID_DIS;
    LpstrSnsSigGroupDisFollow->LeftLisDisData.u16FirstHeight_New = SNS_INVALID_HEIGHT;
    LpstrSnsSigGroupDisFollow->LeftLisDisData.u16SecondDis_New = PDC_OBJ_FOLLOW_INVALID_DIS;
    LpstrSnsSigGroupDisFollow->LeftLisDisData.u16SecondHeight_New = SNS_INVALID_HEIGHT;

    /* 根据车辆静止及运动做不同的处理 */
    if(LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eCarDir == SNS_CAR_STOP)
    {
        if(!LpstrSnsSigGroupDisFollow->LeftLisDisData.u8DisFollowFlag)
        {
            if((LenuSnsCh > PDC_SNS_CH_FLS_RLS)&&(LenuSnsCh < PDC_SNS_CH_FRS_RRS))
            {
                SnsSigGroupDisFollowForMiddleCornerLeftStopSearch(LeGroup,LenuSnsCh,&LpstrSnsSigGroupDisFollow->LeftLisDisData);
            }
            else
            {
                SnsSigGroupDisFollowForSideLeftStopSearch(LeGroup,LenuSnsCh,&LpstrSnsSigGroupDisFollow->LeftLisDisData);
            }
        }
        else
        {
            if((LenuSnsCh > PDC_SNS_CH_FLS_RLS)&&(LenuSnsCh < PDC_SNS_CH_FRS_RRS))
            {
                SnsSigGroupDisFollowForMiddleCornerLeftStopFollow(LeGroup,LenuSnsCh,&LpstrSnsSigGroupDisFollow->LeftLisDisData);
            }
            else
            {
                SnsSigGroupDisFollowForSideLeftStopFollow(LeGroup,LenuSnsCh,&LpstrSnsSigGroupDisFollow->LeftLisDisData);
            }
        }
    }
    else
    {
        if(!LpstrSnsSigGroupDisFollow->LeftLisDisData.u8DisFollowFlag)
        {
            /* 没有跟踪上目标，就在历史缓存中检索存在的有效回波 */
            if((LenuSnsCh == PDC_SNS_CH_FML_RML)||(LenuSnsCh == PDC_SNS_CH_FMR_RMR))
            {
                SnsSigGroupDisFollowForMiddleLeftMoveSearch(LeGroup,LenuSnsCh,&LpstrSnsSigGroupDisFollow->LeftLisDisData);
            }
            else if((LenuSnsCh == PDC_SNS_CH_FL_RL)||(LenuSnsCh == PDC_SNS_CH_FR_RR))
            {
                SnsSigGroupDisFollowForCornerLeftMoveSearch(LeGroup,LenuSnsCh,&LpstrSnsSigGroupDisFollow->LeftLisDisData);
            }
            else
            {
                SnsSigGroupDisFollowForSideLeftMoveSearch(LeGroup,LenuSnsCh,&LpstrSnsSigGroupDisFollow->LeftLisDisData);
            }
        }
        else
        {
            /* 已经跟踪上目标，就持续追踪，直至退出 */
            if((LenuSnsCh == PDC_SNS_CH_FML_RML)||(LenuSnsCh == PDC_SNS_CH_FMR_RMR))
            {
				SnsSigGroupDisFollowForMiddleLeftMoveFollow(LeGroup, LenuSnsCh, &LpstrSnsSigGroupDisFollow->LeftLisDisData);
            }
            else if((LenuSnsCh == PDC_SNS_CH_FL_RL)||(LenuSnsCh == PDC_SNS_CH_FR_RR))
            {
                SnsSigGroupDisFollowForCornerLeftMoveFollow(LeGroup, LenuSnsCh, &LpstrSnsSigGroupDisFollow->LeftLisDisData);
            }
            else
            {
                SnsSigGroupDisFollowForSideLeftMoveFollow(LeGroup, LenuSnsCh, &LpstrSnsSigGroupDisFollow->LeftLisDisData);
            }
        }
    }
}




/******************************************************************************
 * 函数名称: SnsSigGroupRightListenDisFollow
 * 
 * 功能描述: 右侦听回波信号组距离跟踪
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-07-12 08:50   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void SnsSigGroupRightListenDisFollow(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh)
{
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    SnsSigGroupDisFollowType *LpstrSnsSigGroupDisFollow;
    Sns_CycleType LenuCurIndex;
    
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];
    LpstrSnsSigGroupDisFollow = &GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh];
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;

    LpstrSnsSigGroupDisFollow->RightLisDisData.u16DisDetect_New = PDC_OBJ_FOLLOW_INVALID_DIS;
    LpstrSnsSigGroupDisFollow->RightLisDisData.u16EchoHeightDetect_New = SNS_INVALID_HEIGHT;
    LpstrSnsSigGroupDisFollow->RightLisDisData.u16FirstDis_New = PDC_OBJ_FOLLOW_INVALID_DIS;
    LpstrSnsSigGroupDisFollow->RightLisDisData.u16FirstHeight_New = SNS_INVALID_HEIGHT;
    LpstrSnsSigGroupDisFollow->RightLisDisData.u16SecondDis_New = PDC_OBJ_FOLLOW_INVALID_DIS;
    LpstrSnsSigGroupDisFollow->RightLisDisData.u16SecondHeight_New = SNS_INVALID_HEIGHT;

    /* 根据车辆静止及运动做不同的处理 */
    if(LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eCarDir == SNS_CAR_STOP)
    {
        if(!LpstrSnsSigGroupDisFollow->RightLisDisData.u8DisFollowFlag)
        {
            if((LenuSnsCh > PDC_SNS_CH_FLS_RLS)&&(LenuSnsCh < PDC_SNS_CH_FRS_RRS))
            {
                SnsSigGroupDisFollowForMiddleCornerRightStopSearch(LeGroup,LenuSnsCh,&LpstrSnsSigGroupDisFollow->RightLisDisData);
            }
            else
            {
                SnsSigGroupDisFollowForSideRightStopSearch(LeGroup,LenuSnsCh,&LpstrSnsSigGroupDisFollow->RightLisDisData);
            }
        }
        else
        {
            if((LenuSnsCh > PDC_SNS_CH_FLS_RLS)&&(LenuSnsCh < PDC_SNS_CH_FRS_RRS))
            {
                SnsSigGroupDisFollowForMiddleCornerRightStopFollow(LeGroup,LenuSnsCh,&LpstrSnsSigGroupDisFollow->RightLisDisData);
            }
            else
            {
                SnsSigGroupDisFollowForSideRightStopFollow(LeGroup,LenuSnsCh,&LpstrSnsSigGroupDisFollow->RightLisDisData);
            }
        }
    }
    else
    {
        if(!LpstrSnsSigGroupDisFollow->RightLisDisData.u8DisFollowFlag)
        {
            /* 没有跟踪上目标，就在历史缓存中检索存在的有效回波 */
            if((LenuSnsCh == PDC_SNS_CH_FML_RML)||(LenuSnsCh == PDC_SNS_CH_FMR_RMR))
            {
                SnsSigGroupDisFollowForMiddleRightMoveSearch(LeGroup,LenuSnsCh,&LpstrSnsSigGroupDisFollow->RightLisDisData);
            }
            else if((LenuSnsCh == PDC_SNS_CH_FL_RL)||(LenuSnsCh == PDC_SNS_CH_FR_RR))
            {
                SnsSigGroupDisFollowForCornerRightMoveSearch(LeGroup,LenuSnsCh,&LpstrSnsSigGroupDisFollow->RightLisDisData);
            }
            else
            {
                SnsSigGroupDisFollowForSideRightMoveSearch(LeGroup,LenuSnsCh,&LpstrSnsSigGroupDisFollow->RightLisDisData);
            }
        }
        else
        {
            /* 已经跟踪上目标，就持续追踪，直至退出 */
            if((LenuSnsCh == PDC_SNS_CH_FML_RML)||(LenuSnsCh == PDC_SNS_CH_FMR_RMR))
            {
				SnsSigGroupDisFollowForMiddleRightMoveFollow(LeGroup, LenuSnsCh, &LpstrSnsSigGroupDisFollow->RightLisDisData);
            }
            else if((LenuSnsCh == PDC_SNS_CH_FL_RL)||(LenuSnsCh == PDC_SNS_CH_FR_RR))
            {
                SnsSigGroupDisFollowForCornerRightMoveFollow(LeGroup, LenuSnsCh, &LpstrSnsSigGroupDisFollow->RightLisDisData);
            }
            else
            {
                SnsSigGroupDisFollowForSideRightMoveFollow(LeGroup, LenuSnsCh, &LpstrSnsSigGroupDisFollow->RightLisDisData);
            }
        }
    }
}


/******************************************************************************
 * 函数名称: SnsSigGroupObjParallelJudge
 * 
 * 功能描述: 障碍物平行墙类型判断
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-15 20:25   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupObjParallelJudge(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh)
{
    SnsSigGroupDisFollowType* LptrPSnsSigGroupDisFollow;
    LptrPSnsSigGroupDisFollow = &GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh];
    if((LptrPSnsSigGroupDisFollow->MasDisData.u8ParallelWallFlag)||(LptrPSnsSigGroupDisFollow->LeftLisDisData.u8ParallelWallFlag)\
        ||(LptrPSnsSigGroupDisFollow->RightLisDisData.u8ParallelWallFlag))
    {
        LptrPSnsSigGroupDisFollow->u8ObjParallelWallFlag = 1;
    }
    else
    {
        LptrPSnsSigGroupDisFollow->u8ObjParallelWallFlag = 0;
    }
}



/******************************************************************************
 * 函数名称: SnsSigGroupObjStandLeftCurbJudge
 * 
 * 功能描述: 原始数据中，对于直立方管的类型判断,左侦听方管判断
 * 
 * 输入参数:无 
 * 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-18 14:55   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupObjStandLeftCurbJudge(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh)
{
    SnsSigGroupDisFollowType* LptrPSnsSigGroupDisFollow;
    LptrPSnsSigGroupDisFollow = &GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh];
    uint16 Lu16MasterListenHeightSub = 0;
    uint16 Lu16TwoListenHeightSub = 0;
    uint8 Lu8TableHighIndex;
    uint16 Lu16BigWallHeight;
    uint16 Lu16MasterListenHeightSubLimit;
    uint16 Lu16MasterListenHeightNearSUB;
    const SnsCalibHeightType *LptrSnsJudgeObjTypeThresholdTable;
    uint8 Lu8ValidConfirmCnt;
    uint16 Lu16LeftRightDisSub = 65535;
    uint16 Lu16LeftRightDisSubLimit;


    if(Gu16CarSpdForSnsUse < 100)
    {
        Lu16LeftRightDisSubLimit = STAND_CURB_LEFT_RIGHT_DIS_SUB_1_KM;
    }
    else if(Gu16CarSpdForSnsUse < 300)
    {
        Lu16LeftRightDisSubLimit = STAND_CURB_LEFT_RIGHT_DIS_SUB_3_KM;
    }
    else if(Gu16CarSpdForSnsUse < 500)
    {
        Lu16LeftRightDisSubLimit = STAND_CURB_LEFT_RIGHT_DIS_SUB_5_KM;
    }
    else
    {
        Lu16LeftRightDisSubLimit = STAND_CURB_LEFT_RIGHT_DIS_SUB_7_KM;
    }


    if(LptrPSnsSigGroupDisFollow->eMeasType == PDC_SNS_MEAS_STD)
    {
        LptrSnsJudgeObjTypeThresholdTable = &GStrObjJudgeStandardThresholdTableInRAM[0];
        Lu16MasterListenHeightSubLimit = STAND_CURB_STD_HEIGHT_SUB;
        Lu16MasterListenHeightNearSUB = STAND_CURB_STD_NEAR_HEIGHT_SUB;
    }
    else
    {
        LptrSnsJudgeObjTypeThresholdTable = &GStrObjJudgeChirpThresholdTableInRAM[0];
        Lu16MasterListenHeightSubLimit = STAND_CURB_CHIRP_HEIGHT_SUB;
        Lu16MasterListenHeightNearSUB = STAND_CURB_CHIRP__NEAR_HEIGHT_SUB;
    }

    /* 暂时先对中间两颗探头做处理 */
    if((LenuSnsCh == PDC_SNS_CH_FML_RML)||(LenuSnsCh == PDC_SNS_CH_FMR_RMR)||(LenuSnsCh == PDC_SNS_CH_FR_RR))
    {
        /* 判断右侦听直立方柱的处理 */
        if(LptrPSnsSigGroupDisFollow->LeftLisDisData.u16FollowDis < SNS_TABLE_MAX_DIS)
        {
            Lu8TableHighIndex = LptrPSnsSigGroupDisFollow->LeftLisDisData.u16FollowDis/SNS_HIGH_TABLE_STEP;
        }
        else
        {
            Lu8TableHighIndex = SNS_DIS_HIGH_400cm;
        }
        if(LptrPSnsSigGroupDisFollow->eMeasType == PDC_SNS_MEAS_STD)
        {
            Lu16BigWallHeight = LptrSnsJudgeObjTypeThresholdTable[Lu8TableHighIndex].u16BigWallHeight;
            Lu16BigWallHeight = Lu16BigWallHeight - 20;
        }
        else
        {
            Lu16BigWallHeight = LptrSnsJudgeObjTypeThresholdTable[Lu8TableHighIndex].u16BigWallHeight;
            Lu16BigWallHeight = Lu16BigWallHeight - 50;
        }
        
        if(!LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbFlag)
        {
            LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbInvalidCnt = 0;
            if(LptrPSnsSigGroupDisFollow->LeftLisDisData.u16FollowDis < MIDDLE_STAND_CURB_START_DIS)
            {                
                Lu16LeftRightDisSub = ABS(LptrPSnsSigGroupDisFollow->LeftLisDisData.u16FollowDis,GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh-1].RightLisDisData.u16FollowDis);
                if((Lu16LeftRightDisSub < Lu16LeftRightDisSubLimit)&&(LptrPSnsSigGroupDisFollow->LeftLisDisData.u16EchoHeightDetect_New != 0))
                {
                    //if(LptrPSnsSigGroupDisFollow->LeftLisDisData.u16EchoHeightDetect_New > Lu16BigWallHeight)
                    if((LptrPSnsSigGroupDisFollow->LeftLisDisData.u16EchoHeightDetect_New > Lu16BigWallHeight)||\
                        (LptrPSnsSigGroupDisFollow->MasDisData.u16FollowDis==PDC_OBJ_FOLLOW_INVALID_DIS))
                    {
                        if(LptrPSnsSigGroupDisFollow->MasDisData.u16FollowDis==PDC_OBJ_FOLLOW_INVALID_DIS)
                        {
                            LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbValidCnt++;
                        }
                        else
                        {
                            Lu16MasterListenHeightSub = ABS(LptrPSnsSigGroupDisFollow->LeftLisDisData.u16EchoHeightDetect_New,LptrPSnsSigGroupDisFollow->MasDisData.u16EchoHeightDetect_New);
                            Lu16TwoListenHeightSub = ABS(LptrPSnsSigGroupDisFollow->LeftLisDisData.u16EchoHeightDetect_New,LptrPSnsSigGroupDisFollow->RightLisDisData.u16EchoHeightDetect_New);
                            if(LptrPSnsSigGroupDisFollow->MasDisData.u16FollowDis > STAND_CURB_USE_HEIGHT_JUDGE_DIS)
                            {
                                if((Lu16MasterListenHeightSub > Lu16MasterListenHeightSubLimit)&&(Lu16TwoListenHeightSub > Lu16MasterListenHeightSubLimit))
                                {
                                    if(LptrPSnsSigGroupDisFollow->LeftLisDisData.u16EchoHeightDetect_New > LptrPSnsSigGroupDisFollow->MasDisData.u16EchoHeightDetect_New)
                                    {
                                        LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbValidCnt++;
                                    }
                                }
                                else if(Lu16MasterListenHeightSub < Lu16MasterListenHeightNearSUB)
                                {
                                    LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbValidCnt = 0; 
                                }
                            }
                            else
                            {
                                if((Lu16MasterListenHeightSub < Lu16MasterListenHeightNearSUB)||(Lu16TwoListenHeightSub < Lu16MasterListenHeightNearSUB))
                                {
                                    LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbValidCnt = 0; 
                                }
                            }
                        }
                    }
                    else
                    {
                        if(LptrPSnsSigGroupDisFollow->LeftLisDisData.u16EchoHeightDetect_New < (Lu16BigWallHeight/2))
                        {
                            if((LptrPSnsSigGroupDisFollow->LeftLisDisData.u16FollowDis > MIDDLE_STAND_CURB_HEIGHT_MIN_DIS)&&\
                                (LptrPSnsSigGroupDisFollow->LeftLisDisData.u16FollowDis < MIDDLE_STAND_CURB_HEIGHT_MAX_DIS))
                            {
                                LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbValidCnt = 0;
                            }
                        }
                    }
                }
                else
                {
                    /* 无数据更新维持 */
                    if((LptrPSnsSigGroupDisFollow->LeftLisDisData.u16FollowDis > MIDDLE_STAND_CURB_HEIGHT_MIN_DIS)&&\
                        (LptrPSnsSigGroupDisFollow->LeftLisDisData.u16FollowDis < MIDDLE_STAND_CURB_HEIGHT_MAX_DIS))
                    {
                        LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbValidCnt = 0;
                    }
                }
            }
            else
            {
                LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbValidCnt = 0;
            }

            if(LptrPSnsSigGroupDisFollow->LeftLisDisData.u16FollowDis < STAND_CURB_VALID_FAST_DIS)
            {
                if(Lu16LeftRightDisSub < 200)
                {
                    Lu8ValidConfirmCnt = STAND_CURB_VALID_FAST_CNT;
                }
                else
                {
                    Lu8ValidConfirmCnt = STAND_CURB_VALID_CNT;
                }
            }
            else
            {
                Lu8ValidConfirmCnt = STAND_CURB_VALID_CNT;
            }
            
#if 0
            if((LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbValidCnt > Lu8ValidConfirmCnt))
#else
            if((LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbValidCnt > Lu8ValidConfirmCnt)&&\
                (GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh-1].RightStandCurb.u8StandCurbValidCnt > Lu8ValidConfirmCnt))
#endif
            {
                LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbFlag = 1;
#if 0
                printf("Left Stand Curb Valid Flag Set,Group:%d,Ch:%d,Time:%.3f,MasterDis:%d,MasterHeight:%d,LeftDis:%d,LeftHeight:%d,Flag:%d,ValidCnt:%d,InvalidCnt:%d\r\n",GenuSnsGroup,GenuSnsCh,GfMessageTime,\
                    LptrPSnsSigGroupDisFollow->MasDisData.u16FollowDis,LptrPSnsSigGroupDisFollow->MasDisData.u16EchoHeightDetect_New,\
                    LptrPSnsSigGroupDisFollow->LeftLisDisData.u16FollowDis,LptrPSnsSigGroupDisFollow->LeftLisDisData.u16EchoHeightDetect_New,\
                    LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbFlag,LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbValidCnt,\
                    LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbInvalidCnt);
#endif
            }
        }
        else
        {
            LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbValidCnt = 0;
            if((LptrPSnsSigGroupDisFollow->LeftLisDisData.u16FollowDis < MIDDLE_STAND_CURB_START_DIS)&&\
                (LptrPSnsSigGroupDisFollow->LeftLisDisData.u16FollowDis > MIDDLE_STAND_CURB_END_DIS))
            {
                if(LptrPSnsSigGroupDisFollow->LeftLisDisData.u16EchoHeightDetect_New != 0)
                {
                    if(LptrPSnsSigGroupDisFollow->LeftLisDisData.u16EchoHeightDetect_New < (Lu16BigWallHeight/2))
                    {
                        LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbInvalidCnt++;
                    }
                    else
                    {
                        if(LptrPSnsSigGroupDisFollow->MasDisData.u16FollowDis!=PDC_OBJ_FOLLOW_INVALID_DIS)
                        {
                            Lu16MasterListenHeightSub = ABS(LptrPSnsSigGroupDisFollow->LeftLisDisData.u16EchoHeightDetect_New,LptrPSnsSigGroupDisFollow->MasDisData.u16EchoHeightDetect_New);
                            if(Lu16MasterListenHeightSub < Lu16MasterListenHeightNearSUB)
                            {
                                LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbInvalidCnt++;
                            }
                            else if(LptrPSnsSigGroupDisFollow->LeftLisDisData.u16EchoHeightDetect_New < LptrPSnsSigGroupDisFollow->MasDisData.u16EchoHeightDetect_New)
                            {
                                LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbInvalidCnt++;
                            }
                        }
                    }
                }
                else
                {
                    /* 无数据更新维持 */
                    if((LptrPSnsSigGroupDisFollow->LeftLisDisData.u16FollowDis > MIDDLE_STAND_CURB_HEIGHT_MIN_DIS)&&\
                        (LptrPSnsSigGroupDisFollow->LeftLisDisData.u16FollowDis < MIDDLE_STAND_CURB_HEIGHT_MAX_DIS))
                    {
                        LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbInvalidCnt++;
                    }
                }
            }
            if(LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbInvalidCnt > STAND_CURB_INVALID_CNT)
            {
                LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbFlag = 0;
#if 0
                printf("Left Stand Curb Valid Flag Clear,Froup:%d,Ch:%d,Time:%.3f,MasterDis:%d,MasterHeight:%d,LeftDis:%d,LeftHeight:%d,Flag:%d,ValidCnt:%d,InvalidCnt:%d\r\n",GenuSnsGroup,GenuSnsCh,GfMessageTime,\
                    LptrPSnsSigGroupDisFollow->MasDisData.u16FollowDis,LptrPSnsSigGroupDisFollow->MasDisData.u16EchoHeightDetect_New,\
                    LptrPSnsSigGroupDisFollow->LeftLisDisData.u16FollowDis,LptrPSnsSigGroupDisFollow->LeftLisDisData.u16EchoHeightDetect_New,\
                    LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbFlag,LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbValidCnt,\
                    LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbInvalidCnt);
#endif
            }
        }
#if 0
        if((GenuSnsGroup == 0x00)&&(GenuSnsCh == 0x02))
        {
            printf("FML,Time:%.3f,MasterDis:%d,MasterHeight:%d,RightDis:%d,RightHeight:%d,Flag:%d,ValidCnt:%d,InvalidCnt:%d\r\n",GfMessageTime,\
                LptrPSnsSigGroupDisFollow->MasDisData.u16FollowDis,LptrPSnsSigGroupDisFollow->MasDisData.u16EchoHeightDetect_New,\
                LptrPSnsSigGroupDisFollow->LeftLisDisData.u16FollowDis,LptrPSnsSigGroupDisFollow->LeftLisDisData.u16EchoHeightDetect_New,\
                LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbFlag,LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbValidCnt,\
                LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbInvalidCnt);
        }
#endif
        if(LptrPSnsSigGroupDisFollow->LeftStandCurb.u8StandCurbFlag)
        {
            if(LenuSnsCh == PDC_SNS_CH_FMR_RMR)
            {
                LptrPSnsSigGroupDisFollow->LeftStandCurb.u16StandCurbMasterDis = LptrPSnsSigGroupDisFollow->LeftLisDisData.u16FollowDis;
            }
            else
            {
                uint16 Lu16CornerInnerDis;
                uint16 Lu16ReplaceMasterDis;
                if(GenuSnsGroup == 0x00)
                {
                    Lu16CornerInnerDis = FRONT_CORNER_INNER_DIS;
                }
                else
                {
                    Lu16CornerInnerDis = REAR_CORNER_INNER_DIS;
                }
                Lu16ReplaceMasterDis = LptrPSnsSigGroupDisFollow->LeftLisDisData.u16FollowDis*2;
                Lu16ReplaceMasterDis = Lu16ReplaceMasterDis - Lu16CornerInnerDis;
                Lu16ReplaceMasterDis = Lu16ReplaceMasterDis/2;
                LptrPSnsSigGroupDisFollow->LeftStandCurb.u16StandCurbMasterDis = Lu16ReplaceMasterDis;
            }
        }
        else
        {
            LptrPSnsSigGroupDisFollow->LeftStandCurb.u16StandCurbMasterDis = PDC_OBJ_FOLLOW_INVALID_DIS;
        }
    }
}


/******************************************************************************
 * 函数名称: SnsSigGroupObjStandRightCurbJudge
 * 
 * 功能描述: 原始数据中，对于直立方管的类型判断,右侦听方管判断
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-18 14:53   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupObjStandRightCurbJudge(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh)
{
    SnsSigGroupDisFollowType* LptrPSnsSigGroupDisFollow;
    LptrPSnsSigGroupDisFollow = &GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh];
    uint16 Lu16MasterListenHeightSub = 0;
    uint16 Lu16TwoListenHeightSub = 0;
    uint8 Lu8TableHighIndex;
    uint16 Lu16BigWallHeight;
    uint16 Lu16MasterListenHeightSubLimit;
    uint16 Lu16MasterListenHeightNearSUB;
    const SnsCalibHeightType *LptrSnsJudgeObjTypeThresholdTable;
    uint8 Lu8ValidConfirmCnt;
    uint16 Lu16LeftRightDisSub = 65535;
    uint16 Lu16LeftRightDisSubLimit;

    if(Gu16CarSpdForSnsUse < 100)
    {
        Lu16LeftRightDisSubLimit = STAND_CURB_LEFT_RIGHT_DIS_SUB_1_KM;
    }
    else if(Gu16CarSpdForSnsUse < 300)
    {
        Lu16LeftRightDisSubLimit = STAND_CURB_LEFT_RIGHT_DIS_SUB_3_KM;
    }
    else if(Gu16CarSpdForSnsUse < 500)
    {
        Lu16LeftRightDisSubLimit = STAND_CURB_LEFT_RIGHT_DIS_SUB_5_KM;
    }
    else
    {
        Lu16LeftRightDisSubLimit = STAND_CURB_LEFT_RIGHT_DIS_SUB_7_KM;
    }


    if(LptrPSnsSigGroupDisFollow->eMeasType == PDC_SNS_MEAS_STD)
    {
        LptrSnsJudgeObjTypeThresholdTable = &GStrObjJudgeStandardThresholdTableInRAM[0];
        Lu16MasterListenHeightSubLimit = STAND_CURB_STD_HEIGHT_SUB;
        Lu16MasterListenHeightNearSUB = STAND_CURB_STD_NEAR_HEIGHT_SUB;
    }
    else
    {
        LptrSnsJudgeObjTypeThresholdTable = &GStrObjJudgeChirpThresholdTableInRAM[0];
        Lu16MasterListenHeightSubLimit = STAND_CURB_CHIRP_HEIGHT_SUB;
        Lu16MasterListenHeightNearSUB = STAND_CURB_CHIRP__NEAR_HEIGHT_SUB;
    }

    /* 暂时先对中间两颗探头做处理 */
    if((LenuSnsCh == PDC_SNS_CH_FML_RML)||(LenuSnsCh == PDC_SNS_CH_FMR_RMR)||(LenuSnsCh == PDC_SNS_CH_FL_RL))
    {
        /* 判断右侦听直立方柱的处理 */
        if(LptrPSnsSigGroupDisFollow->RightLisDisData.u16FollowDis < SNS_TABLE_MAX_DIS)
        {
            Lu8TableHighIndex = LptrPSnsSigGroupDisFollow->RightLisDisData.u16FollowDis/SNS_HIGH_TABLE_STEP;
        }
        else
        {
            Lu8TableHighIndex = SNS_DIS_HIGH_400cm;
        }
        if(LptrPSnsSigGroupDisFollow->eMeasType == PDC_SNS_MEAS_STD)
        {
            Lu16BigWallHeight = LptrSnsJudgeObjTypeThresholdTable[Lu8TableHighIndex].u16BigWallHeight;
            Lu16BigWallHeight = Lu16BigWallHeight - 20;
        }
        else
        {
            Lu16BigWallHeight = LptrSnsJudgeObjTypeThresholdTable[Lu8TableHighIndex].u16BigWallHeight;
            Lu16BigWallHeight = Lu16BigWallHeight - 50;
        }
        
        if(!LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbFlag)
        {
            LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbInvalidCnt = 0;
            if(LptrPSnsSigGroupDisFollow->RightLisDisData.u16FollowDis < MIDDLE_STAND_CURB_START_DIS)
            {      
                Lu16LeftRightDisSub = ABS(LptrPSnsSigGroupDisFollow->RightLisDisData.u16FollowDis,GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh+1].LeftLisDisData.u16FollowDis);
                if((Lu16LeftRightDisSub < Lu16LeftRightDisSubLimit)&&(LptrPSnsSigGroupDisFollow->RightLisDisData.u16EchoHeightDetect_New != 0))
                {
                    //if(LptrPSnsSigGroupDisFollow->RightLisDisData.u16EchoHeightDetect_New > Lu16BigWallHeight)
                    if((LptrPSnsSigGroupDisFollow->RightLisDisData.u16EchoHeightDetect_New > Lu16BigWallHeight)||\
                        (LptrPSnsSigGroupDisFollow->MasDisData.u16FollowDis==PDC_OBJ_FOLLOW_INVALID_DIS))
                    {
                        if(LptrPSnsSigGroupDisFollow->MasDisData.u16FollowDis==PDC_OBJ_FOLLOW_INVALID_DIS)
                        {
                            LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbValidCnt++;
#if 0
                            printf("Right Stand Curb Cnt Add 01,Froup:%d,Ch:%d,Time:%.3f,MasterDis:%d,MasterHeight:%d,RightDis:%d,RightHeight:%d,Flag:%d,ValidCnt:%d,InvalidCnt:%d\r\n",GenuSnsGroup,GenuSnsCh,GfMessageTime,\
                                LptrPSnsSigGroupDisFollow->MasDisData.u16FollowDis,LptrPSnsSigGroupDisFollow->MasDisData.u16EchoHeightDetect_New,\
                                LptrPSnsSigGroupDisFollow->RightLisDisData.u16FollowDis,LptrPSnsSigGroupDisFollow->RightLisDisData.u16EchoHeightDetect_New,\
                                LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbFlag,LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbValidCnt,\
                                LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbInvalidCnt);
#endif
                        }
                        else
                        {
                            Lu16TwoListenHeightSub = ABS(LptrPSnsSigGroupDisFollow->RightLisDisData.u16EchoHeightDetect_New,LptrPSnsSigGroupDisFollow->LeftLisDisData.u16EchoHeightDetect_New);
                            Lu16MasterListenHeightSub = ABS(LptrPSnsSigGroupDisFollow->RightLisDisData.u16EchoHeightDetect_New,LptrPSnsSigGroupDisFollow->MasDisData.u16EchoHeightDetect_New);
                            if(LptrPSnsSigGroupDisFollow->MasDisData.u16FollowDis > STAND_CURB_USE_HEIGHT_JUDGE_DIS)
                            {
                                if((Lu16MasterListenHeightSub > Lu16MasterListenHeightSubLimit)&&(Lu16TwoListenHeightSub > Lu16MasterListenHeightSubLimit))
                                {
                                    if(LptrPSnsSigGroupDisFollow->RightLisDisData.u16EchoHeightDetect_New > LptrPSnsSigGroupDisFollow->MasDisData.u16EchoHeightDetect_New)
                                    {
                                        LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbValidCnt++;
#if 0
                                        printf("Right Stand Curb Cnt Add 02,Froup:%d,Ch:%d,Time:%.3f,MasterDis:%d,MasterHeight:%d,RightDis:%d,RightHeight:%d,Flag:%d,ValidCnt:%d,InvalidCnt:%d\r\n",GenuSnsGroup,GenuSnsCh,GfMessageTime,\
                                            LptrPSnsSigGroupDisFollow->MasDisData.u16FollowDis,LptrPSnsSigGroupDisFollow->MasDisData.u16EchoHeightDetect_New,\
                                            LptrPSnsSigGroupDisFollow->RightLisDisData.u16FollowDis,LptrPSnsSigGroupDisFollow->RightLisDisData.u16EchoHeightDetect_New,\
                                            LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbFlag,LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbValidCnt,\
                                            LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbInvalidCnt);
#endif
                                    }
                                }
                                else if(Lu16MasterListenHeightSub < Lu16MasterListenHeightNearSUB)
                                {
                                    LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbValidCnt = 0; 
                                }
                            }
                            else
                            {
                                if((Lu16MasterListenHeightSub < Lu16MasterListenHeightNearSUB)||(Lu16TwoListenHeightSub < Lu16MasterListenHeightNearSUB))
                                {
                                    LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbValidCnt = 0; 
                                }
                            }
                        }
                    }
                    else
                    {
                        if(LptrPSnsSigGroupDisFollow->RightLisDisData.u16EchoHeightDetect_New < (Lu16BigWallHeight/2))
                        {
                            if((LptrPSnsSigGroupDisFollow->RightLisDisData.u16FollowDis > MIDDLE_STAND_CURB_HEIGHT_MIN_DIS)&&\
                                (LptrPSnsSigGroupDisFollow->RightLisDisData.u16FollowDis < MIDDLE_STAND_CURB_HEIGHT_MAX_DIS))
                            {
                                LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbValidCnt = 0;
                            }
                        }
                    }
                }
                else
                {
                    /* 无数据更新维持 */
                    if((LptrPSnsSigGroupDisFollow->RightLisDisData.u16FollowDis > MIDDLE_STAND_CURB_HEIGHT_MIN_DIS)&&\
                        (LptrPSnsSigGroupDisFollow->RightLisDisData.u16FollowDis < MIDDLE_STAND_CURB_HEIGHT_MAX_DIS))
                    {
                        LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbValidCnt = 0;
                    }
                }
#if 0
                if((LeGroup == 0x00)&&(LenuSnsCh == 0x01))
                {
                    printf("FMR Cnt,Time:%.3f,MasterDis:%d,MasterHeight:%d,RightDis:%d,RightHeight:%d,Flag:%d,ValidCnt:%d,LeftRightDisSub:%d,BigWallHeight:%d\r\n",GfMessageTime,\
                        LptrPSnsSigGroupDisFollow->MasDisData.u16FollowDis,LptrPSnsSigGroupDisFollow->MasDisData.u16EchoHeightDetect_New,\
                        LptrPSnsSigGroupDisFollow->RightLisDisData.u16FollowDis,LptrPSnsSigGroupDisFollow->RightLisDisData.u16EchoHeightDetect_New,\
                        LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbFlag,LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbValidCnt,\
                        Lu16LeftRightDisSub,Lu16BigWallHeight);
                }
#endif
            }
            else
            {
                LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbValidCnt = 0;
            }

            if(LptrPSnsSigGroupDisFollow->RightLisDisData.u16FollowDis < STAND_CURB_VALID_FAST_DIS)
            {
                if(Lu16LeftRightDisSub < 200)
                {
                    Lu8ValidConfirmCnt = STAND_CURB_VALID_FAST_CNT;
                }
                else
                {
                    Lu8ValidConfirmCnt = STAND_CURB_VALID_CNT;
                }
            }
            else
            {
                Lu8ValidConfirmCnt = STAND_CURB_VALID_CNT;
            }
            
#if 0
            if(LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbValidCnt > Lu8ValidConfirmCnt)
#else
            if((LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbValidCnt > Lu8ValidConfirmCnt)&&\
                (GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh+1].LeftStandCurb.u8StandCurbValidCnt > Lu8ValidConfirmCnt))
#endif
            {
                LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbFlag = 1;
#if 0
                printf("Right Stand Curb Valid Flag Set,Froup:%d,Ch:%d,Time:%.3f,MasterDis:%d,MasterHeight:%d,RightDis:%d,RightHeight:%d,Flag:%d,ValidCnt:%d,InvalidCnt:%d\r\n",GenuSnsGroup,GenuSnsCh,GfMessageTime,\
                    LptrPSnsSigGroupDisFollow->MasDisData.u16FollowDis,LptrPSnsSigGroupDisFollow->MasDisData.u16EchoHeightDetect_New,\
                    LptrPSnsSigGroupDisFollow->RightLisDisData.u16FollowDis,LptrPSnsSigGroupDisFollow->RightLisDisData.u16EchoHeightDetect_New,\
                    LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbFlag,LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbValidCnt,\
                    LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbInvalidCnt);
#endif
            }
        }
        else
        {
            LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbValidCnt = 0;
            if((LptrPSnsSigGroupDisFollow->RightLisDisData.u16FollowDis < MIDDLE_STAND_CURB_START_DIS)&&\
                (LptrPSnsSigGroupDisFollow->RightLisDisData.u16FollowDis > MIDDLE_STAND_CURB_END_DIS))
            {
                if(LptrPSnsSigGroupDisFollow->RightLisDisData.u16EchoHeightDetect_New != 0)
                {
                    if(LptrPSnsSigGroupDisFollow->RightLisDisData.u16EchoHeightDetect_New < (Lu16BigWallHeight/2))
                    {
                        LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbInvalidCnt++;
                    }
                    else
                    {
                        if(LptrPSnsSigGroupDisFollow->MasDisData.u16FollowDis!=PDC_OBJ_FOLLOW_INVALID_DIS)
                        {
                            Lu16MasterListenHeightSub = ABS(LptrPSnsSigGroupDisFollow->RightLisDisData.u16EchoHeightDetect_New,LptrPSnsSigGroupDisFollow->MasDisData.u16EchoHeightDetect_New);
                            if(Lu16MasterListenHeightSub < Lu16MasterListenHeightNearSUB)
                            {
                                LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbInvalidCnt++;
                            }
                            else if(LptrPSnsSigGroupDisFollow->RightLisDisData.u16EchoHeightDetect_New < LptrPSnsSigGroupDisFollow->MasDisData.u16EchoHeightDetect_New)
                            {
                                LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbInvalidCnt++;
                            }
                        }
                    }
                }
                else
                {
                    /* 无数据更新维持 */
                    if((LptrPSnsSigGroupDisFollow->RightLisDisData.u16FollowDis > MIDDLE_STAND_CURB_HEIGHT_MIN_DIS)&&\
                        (LptrPSnsSigGroupDisFollow->RightLisDisData.u16FollowDis < MIDDLE_STAND_CURB_HEIGHT_MAX_DIS))
                    {
                        LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbInvalidCnt++;
                    }
                }
            }
            if(LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbInvalidCnt > STAND_CURB_INVALID_CNT)
            {
                LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbFlag = 0;
#if 0
                printf("Right Stand Curb Valid Flag Clear,Froup:%d,Ch:%d,Time:%.3f,MasterDis:%d,MasterHeight:%d,RightDis:%d,RightHeight:%d,Flag:%d,ValidCnt:%d,InvalidCnt:%d\r\n",GenuSnsGroup,GenuSnsCh,GfMessageTime,\
                    LptrPSnsSigGroupDisFollow->MasDisData.u16FollowDis,LptrPSnsSigGroupDisFollow->MasDisData.u16EchoHeightDetect_New,\
                    LptrPSnsSigGroupDisFollow->RightLisDisData.u16FollowDis,LptrPSnsSigGroupDisFollow->RightLisDisData.u16EchoHeightDetect_New,\
                    LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbFlag,LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbValidCnt,\
                    LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbInvalidCnt);
#endif
            }
        }
#if 0
        if((GenuSnsGroup == 0x00)&&(GenuSnsCh == 0x02))
        {
            printf("FML,Time:%.3f,MasterDis:%d,MasterHeight:%d,RightDis:%d,RightHeight:%d,Flag:%d,ValidCnt:%d,InvalidCnt:%d\r\n",GfMessageTime,\
                LptrPSnsSigGroupDisFollow->MasDisData.u16FollowDis,LptrPSnsSigGroupDisFollow->MasDisData.u16EchoHeightDetect_New,\
                LptrPSnsSigGroupDisFollow->RightLisDisData.u16FollowDis,LptrPSnsSigGroupDisFollow->RightLisDisData.u16EchoHeightDetect_New,\
                LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbFlag,LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbValidCnt,\
                LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbInvalidCnt);
        }
#endif
        if(LptrPSnsSigGroupDisFollow->RightStandCurb.u8StandCurbFlag)
        {
            if(LenuSnsCh == PDC_SNS_CH_FML_RML)
            {
                LptrPSnsSigGroupDisFollow->RightStandCurb.u16StandCurbMasterDis = LptrPSnsSigGroupDisFollow->RightLisDisData.u16FollowDis;
            }
            else
            {
                uint16 Lu16CornerInnerDis;
                uint16 Lu16ReplaceMasterDis;
                if(GenuSnsGroup == 0x00)
                {
                    Lu16CornerInnerDis = FRONT_CORNER_INNER_DIS;
                }
                else
                {
                    Lu16CornerInnerDis = REAR_CORNER_INNER_DIS;
                }
                Lu16ReplaceMasterDis = LptrPSnsSigGroupDisFollow->RightLisDisData.u16FollowDis*2;
                Lu16ReplaceMasterDis = Lu16ReplaceMasterDis - Lu16CornerInnerDis;
                Lu16ReplaceMasterDis = Lu16ReplaceMasterDis/2;
                LptrPSnsSigGroupDisFollow->RightStandCurb.u16StandCurbMasterDis = Lu16ReplaceMasterDis;
            }
        }
        else
        {
            LptrPSnsSigGroupDisFollow->RightStandCurb.u16StandCurbMasterDis = PDC_OBJ_FOLLOW_INVALID_DIS;
        }
    }
}


/******************************************************************************
 * 函数名称: SnsSigGroupDisFollow
 * 
 * 功能描述: 回波信号组距离跟踪
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-07-12 08:29   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollow(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh)
{
    SnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    Sns_CycleType LenuCurIndex;
    LpstrSnsSigGroupDataCache = &GstrSnsSigGroupDataCache[LeGroup][LenuSnsCh];

#if 0
    if((LeGroup == 0x00)&&(LenuSnsCh == PDC_SNS_CH_FML))
    {
        printf("Time:%.3f,FML SysTime:%ld,%ld,%ld\r\n",GfMessageTime,LpstrSnsSigGroupDataCache->SysDataBuf[0].u32SysTime,\
            LpstrSnsSigGroupDataCache->SysDataBuf[1].u32SysTime,\
            LpstrSnsSigGroupDataCache->SysDataBuf[2].u32SysTime);
    }
#endif
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh].eMeasType = LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType;


    if(LpstrSnsSigGroupDataCache->u8RecordCnt >= 2)
    {
        GenuSnsWorkMode = SNS_FOLLOW_MASTER;
        SnsSigGroupMasterDisFollow(LeGroup,LenuSnsCh);
        
        if(LenuSnsCh != PDC_SNS_CH_FLS_RLS)
        {
            GenuSnsWorkMode = SNS_FOLLOW_LEFT_LISTEN;
            SnsSigGroupLeftListenDisFollow(LeGroup,LenuSnsCh);
        }
        if(LenuSnsCh != PDC_SNS_CH_FRS_RRS)
        {
            GenuSnsWorkMode = SNS_FOLLOW_RIGHT_LISTEN;
            SnsSigGroupRightListenDisFollow(LeGroup,LenuSnsCh);
        }
        SnsSigGroupObjParallelJudge(LeGroup,LenuSnsCh);
#if 1
        SnsSigGroupObjStandLeftCurbJudge(LeGroup,LenuSnsCh);
        SnsSigGroupObjStandRightCurbJudge(LeGroup,LenuSnsCh);
#endif
    }
    else
    {
        SnsSigGroupChannelDataClear(LeGroup,LenuSnsCh);
    }
}


/******************************************************************************
 * 函数名称: CalValidCE_DisForMiddleSns
 * 
 * 功能描述: 中间探头，通过三角匹配，判断DE CE的有效性
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-15 10:43   V0.1      AntonyFang   初次发布
 ******************************************************************************/
DE_CE_ValidListenDisType CalValidCE_DisForMiddleSns(uint16 Lu16MasterDis, uint16 Lu16SnsSpace)
{
    DE_CE_ValidListenDisType LstrObjValidListenDis;
    float LfCosAngValue;
    float LfMasterSquare;
    float LfSpaceSquare;
    float LfMasterMultSpace;
    float LfTemp;

    if(Lu16MasterDis < 700)
    {
        LfCosAngValue = 0.9;/* 0.707 */
    }
    else if(Lu16MasterDis < 1000)
    {
        LfCosAngValue = 760.0/Lu16MasterDis;
    }
    else if(Lu16MasterDis < 1500)
    {
        LfCosAngValue = 770.0/Lu16MasterDis;
    }
    else if(Lu16MasterDis < 2000)
    {
        LfCosAngValue = 780.0/Lu16MasterDis;
    }
    else if(Lu16MasterDis < 2500)
    {
        LfCosAngValue = 900.0/Lu16MasterDis;//800
    }
    else if(Lu16MasterDis < 3000)
    {
        LfCosAngValue = 900.0/Lu16MasterDis;//800
    }
    else if(Lu16MasterDis < 3500)
    {
        LfCosAngValue = 750.0/Lu16MasterDis;
    }
    else if(Lu16MasterDis < 4000)
    {
        LfCosAngValue = 780.0/Lu16MasterDis;
    }
    else 
    {
        LfCosAngValue = 800.0/Lu16MasterDis;
    }    

    LfMasterSquare = (float)(Lu16MasterDis*Lu16MasterDis);
    LfSpaceSquare = (float)(Lu16SnsSpace*Lu16SnsSpace);
    LfMasterMultSpace = (float)(Lu16MasterDis*Lu16SnsSpace*2);
    LfMasterMultSpace = LfMasterMultSpace*LfCosAngValue;

    LfTemp = LfMasterSquare+LfSpaceSquare+LfMasterMultSpace;
    LfTemp = powf(LfTemp,0.5);
    LstrObjValidListenDis.u16MaxDis = (uint16)LfTemp;
    LstrObjValidListenDis.u16MaxDis += Lu16MasterDis;

    
    LfTemp = LfMasterSquare+LfSpaceSquare-LfMasterMultSpace;
    LfTemp = powf(LfTemp,0.5);
    LstrObjValidListenDis.u16MinDis = (uint16)LfTemp;
    LstrObjValidListenDis.u16MinDis += Lu16MasterDis;
    return LstrObjValidListenDis;
}


/******************************************************************************
 * 函数名称: CalValidCE_DisForCornerSns
 * 
 * 功能描述: 边角探头，通过三角匹配，判断DE CE的有效性
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-15 10:44   V0.1      AntonyFang   初次发布
 ******************************************************************************/
DE_CE_ValidListenDisType CalValidCE_DisForCornerSns(uint16 Lu16MasterDis, uint16 Lu16SnsSpace)
{
    DE_CE_ValidListenDisType LstrObjValidListenDis;
    float LfCosAngValue;
    float LfMasterSquare;
    float LfSpaceSquare;
    float LfMasterMultSpace;
    float LfTemp;

    if(Lu16MasterDis < 700)
    {
        LfCosAngValue = 0.9;/* 0.707 */
    }
    else if(Lu16MasterDis < 1000)
    {
        LfCosAngValue = 850.0/Lu16MasterDis;
    }
    else if(Lu16MasterDis < 1500)
    {
        LfCosAngValue = 1000.0/Lu16MasterDis;
    }
    else if(Lu16MasterDis < 2000)
    {
        LfCosAngValue = 900.0/Lu16MasterDis;
    }
    else if(Lu16MasterDis < 2500)
    {
        LfCosAngValue = 900.0/Lu16MasterDis;//800
    }
    else if(Lu16MasterDis < 3000)
    {
        LfCosAngValue = 900.0/Lu16MasterDis;//800
    }
    else if(Lu16MasterDis < 3500)
    {
        LfCosAngValue = 750.0/Lu16MasterDis;
    }
    else if(Lu16MasterDis < 4000)
    {
        LfCosAngValue = 780.0/Lu16MasterDis;
    }
    else 
    {
        LfCosAngValue = 800.0/Lu16MasterDis;
    }    

    LfMasterSquare = (float)(Lu16MasterDis*Lu16MasterDis);
    LfSpaceSquare = (float)(Lu16SnsSpace*Lu16SnsSpace);
    LfMasterMultSpace = (float)(Lu16MasterDis*Lu16SnsSpace*2);
    LfMasterMultSpace = LfMasterMultSpace*LfCosAngValue;

    LfTemp = LfMasterSquare+LfSpaceSquare+LfMasterMultSpace;
    LfTemp = powf(LfTemp,0.5);
    LstrObjValidListenDis.u16MaxDis = (uint16)LfTemp;
    LstrObjValidListenDis.u16MaxDis += Lu16MasterDis;

    
    LfTemp = LfMasterSquare+LfSpaceSquare-LfMasterMultSpace;
    LfTemp = powf(LfTemp,0.5);
    LstrObjValidListenDis.u16MinDis = (uint16)LfTemp;
    LstrObjValidListenDis.u16MinDis += Lu16MasterDis;
    return LstrObjValidListenDis;
}


/******************************************************************************
 * 函数名称: CalValidCE_DisForSideSns
 * 
 * 功能描述: 侧边探头，通过三角匹配，判断DE CE的有效性
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-09-15 10:45   V0.1      AntonyFang   初次发布
 ******************************************************************************/
DE_CE_ValidListenDisType CalValidCE_DisForSideSns(uint16 Lu16MasterDis, uint16 Lu16SnsSpace)
{
    DE_CE_ValidListenDisType LstrObjValidListenDis;
    float LfCosAngValue;
    float LfMasterSquare;
    float LfSpaceSquare;
    float LfMasterMultSpace;
    float LfTemp;

    if(Lu16MasterDis < 700)
    {
        LfCosAngValue = 0.9;/* 0.707 */
    }
    else if(Lu16MasterDis < 1000)
    {
        LfCosAngValue = 850.0/Lu16MasterDis;
    }
    else if(Lu16MasterDis < 1500)
    {
        LfCosAngValue = 900.0/Lu16MasterDis;
    }
    else if(Lu16MasterDis < 2000)
    {
        LfCosAngValue = 800.0/Lu16MasterDis;
    }
    else if(Lu16MasterDis < 2500)
    {
        LfCosAngValue = 900.0/Lu16MasterDis;//800
    }
    else if(Lu16MasterDis < 3000)
    {
        LfCosAngValue = 900.0/Lu16MasterDis;//800
    }
    else if(Lu16MasterDis < 3500)
    {
        LfCosAngValue = 750.0/Lu16MasterDis;
    }
    else if(Lu16MasterDis < 4000)
    {
        LfCosAngValue = 780.0/Lu16MasterDis;
    }
    else 
    {
        LfCosAngValue = 800.0/Lu16MasterDis;
    }    

    LfMasterSquare = (float)(Lu16MasterDis*Lu16MasterDis);
    LfSpaceSquare = (float)(Lu16SnsSpace*Lu16SnsSpace);
    LfMasterMultSpace = (float)(Lu16MasterDis*Lu16SnsSpace*2);
    LfMasterMultSpace = LfMasterMultSpace*LfCosAngValue;

    LfTemp = LfMasterSquare+LfSpaceSquare+LfMasterMultSpace;
    LfTemp = powf(LfTemp,0.5);
    LstrObjValidListenDis.u16MaxDis = (uint16)LfTemp;
    LstrObjValidListenDis.u16MaxDis += Lu16MasterDis;

    
    LfTemp = LfMasterSquare+LfSpaceSquare-LfMasterMultSpace;
    LfTemp = powf(LfTemp,0.5);
    LstrObjValidListenDis.u16MinDis = (uint16)LfTemp;
    LstrObjValidListenDis.u16MinDis += Lu16MasterDis;
    return LstrObjValidListenDis;
}

/******************************************************************************
 * 函数名称: SnsSigGroupDisStableHandle
 * 
 * 功能描述: 在过去5轮缓存中，查询输出稳定的距离
 * 
 * 输入参数:Lpu16DisBuff--过去5个周期的历史距离缓存；Lu16NewDis--最新探测的距离；Lu16LastDis--上一轮输出的距离
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-07-12 11:11   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static uint16 SnsSigGroupDisStableHandle(uint16 *Lpu16DisBuff,uint16 Lu16NewDis,uint16 Lu16LastDis)
{
    uint16 Lu16DsTemp;
    uint8 i;
    uint16 Lu16Diff;
    if(Lu16NewDis == PDC_OBJ_FOLLOW_INVALID_DIS)
    {
        Lu16DsTemp = PDC_OBJ_FOLLOW_INVALID_DIS;
    }
    else
    {
        Lu16DsTemp = Lu16NewDis;
    }
    
    if(ABS(Lu16DsTemp,Lu16LastDis) > PDC_ECHO_DIS_DIFF_ALLOW)
    {
        if(Lu16LastDis == PDC_OBJ_FOLLOW_INVALID_DIS)
        {
            Lu16Diff = 30;
        }
        else
        {
            Lu16Diff = PDC_ECHO_DIS_DIFF_ALLOW;
        }
        /** @brief: 避免距离跳动(如果本次距离波动较大，则遍历历史距离，在历史距离中找到与本次距离偏差较小的数据作为输出) */
        for(i=0;i<PDC_FOLLOW_ECHO_DIS_BACKUP_NUM;i++)
        {
            if(ABS(Lu16DsTemp,Lpu16DisBuff[i]) <= Lu16Diff)
            {
                Lu16DsTemp = Lpu16DisBuff[i];
                break;
            }
        }
    }
    else
    {
       Lu16DsTemp = Lu16LastDis;
    }

    /** @brief: 更新备份数据 */
    for(i=PDC_FOLLOW_ECHO_DIS_BACKUP_NUM-1; i>0; i--)
    {
        Lpu16DisBuff[i] = Lpu16DisBuff[i-1];
    }
    Lpu16DisBuff[0] = Lu16NewDis;
    
    return Lu16DsTemp;
}

/******************************************************************************
 * 函数名称: SnsSigGroupStableDisOutPut
 * 
 * 功能描述: 稳定距离
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-07-12 11:06   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupStableDisOutPut(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh)
{
    SnsSigGroupDisFollowType *LpstrSnsSigGroupDisFollow;
    
    LpstrSnsSigGroupDisFollow = &GstrSnsSigGroupDisFollow[LeGroup][LenuSnsCh];

    LpstrSnsSigGroupDisFollow->MasDisData.u16StableOutputDis = SnsSigGroupDisStableHandle(&LpstrSnsSigGroupDisFollow->MasDisData.u16StableOutputDisBackup[0],LpstrSnsSigGroupDisFollow->MasDisData.u16FollowDis,LpstrSnsSigGroupDisFollow->MasDisData.u16StableOutputDis);

    LpstrSnsSigGroupDisFollow->LeftLisDisData.u16StableOutputDis = SnsSigGroupDisStableHandle(&LpstrSnsSigGroupDisFollow->LeftLisDisData.u16StableOutputDisBackup[0],LpstrSnsSigGroupDisFollow->LeftLisDisData.u16FollowDis,LpstrSnsSigGroupDisFollow->LeftLisDisData.u16StableOutputDis);
    LpstrSnsSigGroupDisFollow->RightLisDisData.u16StableOutputDis = SnsSigGroupDisStableHandle(&LpstrSnsSigGroupDisFollow->RightLisDisData.u16StableOutputDisBackup[0],LpstrSnsSigGroupDisFollow->RightLisDisData.u16FollowDis,LpstrSnsSigGroupDisFollow->RightLisDisData.u16StableOutputDis);
}



/******************************************************************************
 * 函数名称: SnsSigGroupDisFollowAndOutputHandle
 * 
 * 功能描述: 对信号组的数据进行跟踪，并输出跟踪距离和稳定距离，供后续的坐标生成使用
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-07-12 08:24   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsSigGroupDisFollowAndOutputHandle(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh)
{
#if 1
    GenuSnsGroup = LeGroup;
    GenuSnsCh = LenuSnsCh;
    SnsSigGroupDisFollow(LeGroup,LenuSnsCh);
    SnsSigGroupStableDisOutPut(LeGroup,LenuSnsCh);    
#endif
}


