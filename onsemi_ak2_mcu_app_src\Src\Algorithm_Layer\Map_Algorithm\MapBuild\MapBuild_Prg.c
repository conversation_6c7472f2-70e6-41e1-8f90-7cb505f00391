/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * F_R_ObjMapCal_Prg: 
 * Created on: 2023-02-16 15:45
 * Original designer: AntonyFang
 ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "MapBuild_Int.h"
#include "SnsRawData_Int.h"
#include "Sns_install_Coordinate.h"
#include "PublicCalAlgorithm_Int.h"

#if !VS_SIMULATE_ENABLE
#include "TimerManage.h"
#endif

#include "PAS_MAP_SignalManage.h"
#include "MapCoorCalculate_Int.h"
#include "MapEchoFilterAndSigGroup_int.h"
#include "CAN_AppSignalManage.h"
#include "MapRawDataCalib.h"
//#include "debug.h"


/******************************************************************************/
/******************************************************************************/
/****************************** Public Definition *****************************/
/******************************************************************************/
/******************************************************************************/
PDC_AreaObjType  GstrPDC_AreaObj[PDC_SNS_GROUP_NUM];

/* 构建前后雷达的Map */
ObjMapInfoType   GstrObjMap;

/* 定义Map To CAN 的接口 */
ObjMapToCANType  GstrObjMapToCAN[MAP_OBJ_TO_CAN_MAX];

MapBigWallBufType  GstrMapBigWallBuf[PDC_SNS_GROUP_NUM];
PDCSnsGroupType GenuGroupForPrintf;
/* 定义前保中间区域4中Map到保杠的距离，用于PP稳定性报警处理 */
uint16 Gu16FrontArea4MapToBumperDis;

MapLineBufType     GstrMapLineBuf[PDC_SNS_GROUP_NUM];

extern uint32 G_TimeStampPoint;

#if MAP_PRECISION_PRINT_SWITCH
MapPrecisionPrintType GstrMapPrecisionPrint;
#endif


/******************************************************************************/
/******************************************************************************/
/****************************** Private Function ******************************/
/******************************************************************************/
/******************************************************************************/
const ObjMapCoorType GstrObjMapCoorInit =
{
    .P1_OdoCoorBuf[0] = {0.0,0.0},
    .P1_OdoCoorBuf[1] = {0.0,0.0},
    .P1_OdoCoorBuf[2] = {0.0,0.0},

    .P2_OdoCoorBuf[0] = {0.0,0.0},
    .P2_OdoCoorBuf[1] = {0.0,0.0},
    .P2_OdoCoorBuf[2] = {0.0,0.0},

    .P1_OdoCoor = {0.0,0.0},
    .P2_OdoCoor = {0.0,0.0},
    .P1_CarCoor = {0.0,0.0},
    .P2_CarCoor = {0.0,0.0},
    .eMapObjId = MAP_NONE,
    .eMapObjType = OBJ_TYPE_NONE,
    .eObjExistProb = OBJ_PROB_NONE,
    .eMapObjHeight = OBJ_UNKNOW,
    .eObjHeightProb = OBJ_PROB_NONE,
    .eMapSigGroupHeight = OBJ_LOW,
    .eMapHeightTrendHeight = OBJ_LOW,
    .eMapDE_NoMatchHeight = OBJ_HIGH,
    .fP1_P2_Dis = 0.0,
    .fP1_P2ToCarX_Angle = 0.0,
    .fMapToBumper_Dis = MAP_TO_BUMPER_INVALID_DIS,
    .fP1_ToBumper_Dis = MAP_TO_BUMPER_INVALID_DIS,
    .fP2_ToBumper_Dis = MAP_TO_BUMPER_INVALID_DIS,
    .fSnsFLS_DeNoMatchMapMovDis = 0,
    .fSnsFRS_DeNoMatchMapMovDis = 0,
    .fSnsRLS_DeNoMatchMapMovDis = 0,
    .fSnsRRS_DeNoMatchMapMovDis = 0,
    .fSideMapToSelfSnsDis = 0,
    .fNearestDE = 5000,
    .eObjBumperArea = OBJ_NONE_BUMP,
    .eP1_DetailArea = MAP_AREA_NONE,
    .eP2_DetailArea = MAP_AREA_NONE,
    .eObjMoveSts = OBJ_TO_CAR_STOP_STOP,
    .eObjFollowSts = OBJ_NONE_FOLLOW,
    .eMapObjDetailType = OBJ_NONE_TYPE,
    .enuSideMapSns = SIDE_SNS_NONE,
    .enuSideMapUpdateSts = SIDE_MAP_UPDATE_NONE,
    .enuMapPointInSideSnsFov = SIDE_SNS_NONE,
    .enuShortMapInSideSnsFov = SIDE_SNS_NONE,
    .eMapCreatAdasSts = APA_NONE,
    .ObjTypeTotalCnt = {0,0,0,0,0, 0,0,0,0,0},
    .ObjTypeCntBuf =
    {
        [0] = {0,0,0,0,0, 0,0,0,0,0},
        [1] = {0,0,0,0,0, 0,0,0,0,0},
        [2] = {0,0,0,0,0, 0,0,0,0,0},
        [3] = {0,0,0,0,0, 0,0,0,0,0},
        [4] = {0,0,0,0,0, 0,0,0,0,0},
        [5] = {0,0,0,0,0, 0,0,0,0,0},
        [6] = {0,0,0,0,0, 0,0,0,0,0},
        [7] = {0,0,0,0,0, 0,0,0,0,0},
        [8] = {0,0,0,0,0, 0,0,0,0,0},
        [9] = {0,0,0,0,0, 0,0,0,0,0},
#if 0
        [10] = {0,0,0,0,0, 0,0,0,0,0},
        [11] = {0,0,0,0,0, 0,0,0,0,0},
        [12] = {0,0,0,0,0, 0,0,0,0,0},
        [13] = {0,0,0,0,0, 0,0,0,0,0},
        [14] = {0,0,0,0,0, 0,0,0,0,0},
        [15] = {0,0,0,0,0, 0,0,0,0,0},
        [16] = {0,0,0,0,0, 0,0,0,0,0},
        [17] = {0,0,0,0,0, 0,0,0,0,0},
        [18] = {0,0,0,0,0, 0,0,0,0,0},
        [19] = {0,0,0,0,0, 0,0,0,0,0},
#endif
    },
    
    .SideMapCarMovDir = SNS_CAR_STOP,
    .u32CreatMapTime = 0,
    .u32UpdateSysTime = 0,
    .u32BlindNoObjTime = 0,
    .u32BlindWaitDisappearTime = 0,
    .u32MapToCarStopTime = 0,
    .u32FirstNearDeMatchedTime = 0,
    .u16SideMapUpdateMasterDis = 65535,
    .u16NoUpdateHoldTime = 0,
    .MapHeightBuf = {{0,0},{0,0},{0,0},{0,0},{0,0},{0,0},{0,0},{0, 0},{0,0},{0,0}},
    .u8MapExistFlag = 0,
    .u8MapDisplayFlag = 0,
    .u8HighCurbCnt = 0,
    .u8LowCurbCnt = 0,
    .u8PVC_Cnt = 0,
    .u8BigWallCnt = 0,
    .u8SideMapBigWallCnt = 0,
    .u8TotalTypeCnt = 0,
    .u8BlindNoObjFlag = 0,
    .u8CarCloseToMapBlindFlag = 0,
    .u8MapContinueMatchCnt = 0,
    .u8CarMoveCloseMapFlag = 0,
    .u8CarMoveCloseMapCnt = 0,
    .u8P1OdoCoorBufCnt = 0,
    .u8P2OdoCoorBufCnt = 0,
    .u8LineOdoCoorBufCnt = 0,
    .u8LineMatchLineCnt = 0,
    .u8PointMatchPointCnt = 0,
    .u8P_P_MatchContiCnt = 0,
    .u8MapGrenerateType = 0,
    .u8MapCreateCarSts = 0,
    .u8HeightLockFlag = 0,
    .u8ObjTypeUpdateFlag = 0,
    .u8Map_DE_NoMatchCnt = 0,
    .u8MiddleMapToSideFlag = 0,
    .u8SideMapAndNewPointNomatchCnt = 0,
    .u8MemoryMapDeNoMatchCnt = 0,
    .u8SideShortMapFovNoMatchCnt = 0,
    .u8CarStopFLS_MapDeNoMatchCnt = 0,
    .u8CarStopFRS_MapDeNoMatchCnt = 0,
    .u8CarStopRLS_MapDeNoMatchCnt = 0,
    .u8CarStopRRS_MapDeNoMatchCnt = 0,
    .u8CreatMapInRearBumperInnerFlag = 0,
    .u8SideMapToF_R_MapFlag = 0,
    .u8MapToLowLockFlag = 0,
    .u8MapBeCutFlag = 0,
    .u8MapBeCutCnt = 0,
    .u8Map_DE_MatchNearHighFlag = 0,
    .u8StdHeightStartInx = 0,
    .u8ChirpHeightStartInx = 0,
    .u8Map_DE_MatchNearHighFlagClearCnt = 0,
    .u8SideMapHeightLockFlag = 0,
    .u8Map_SFR_AreaFlag = 0,
    .u8MapTooLongNeedEndFlag = 0,
    .u8SideMapToCarInnerDeNoMatchCnt = 0,
    .u8SideMapCoverCarDeNoMatchCnt = 0,
    .u8SideMapToCarEdgeDeNoMatchCnt = 0,
    .u8SideMapLowTagFlag = 0,
    .u8SideLowMapDeNoMatchCnt = 0,
    .u8SideLowMapDeMatchOKCnt = 0,
    .u8DEMapStdCnt = 0,
    .u8DEMapChripCnt = 0,
    .u8MapInSideAreaDisplayFlag = 0,
	.u8TwoEchoDeMatchedCnt = 0,
};

const ObjMapToCANType GstrObjMapToCANInit = 
{
    .eMapObjId = MAP_NONE,
    .eMapObjType = OBJ_TYPE_NONE,
    .eObjExistProb = OBJ_PROB_NONE,
    .eMapObjHeight = OBJ_LOW,
    .eObjHeightProb = OBJ_PROB_NONE,
    .s16ObjMapP1_X = 0,
    .s16ObjMapP1_Y = 0,
    .s16ObjMapP2_X = 0,
    .s16ObjMapP2_Y = 0,
    .u32MapObjTimestamp = 0
};

const MapBigWallUnitType GstrMapBigWallUnitInit = 
{
    .eMapObjId = MAP_NONE,
    .u8BeFusionLineFlag = 0
};


const MapLineFusionUnitType GstrMapLineFusionUnitInit = 
{
    .fLineMapX_Coor = 0,
    .fLineLeftY_Coor = 0,
    .fLineRightY_Coor = 0,
    .eLineMapId = MAP_NONE,
    .eBeFusionMapId[0] = MAP_NONE,
    .eBeFusionMapId[1] = MAP_NONE,
    .eBeFusionMapId[2] = MAP_NONE,
    .u8BeFusionMapCnt = 0,
    .u8LineMapWaitFusionFlag = 0
};

/******************************************************************************/
/******************************************************************************/
/******************************* Public Function ******************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************************
 * 函数名称: SnsCalDetAreaCoor
 *
 * 功能描述: 计算障碍物的区域坐标，以主发探头探测5m作为边界进行分区
 *
 * 输入参数:无
 *
 * 输出参数:无
 *
 * 返回值:无
 *
 * 其它说明:无
 *
 * 修改日期              版本号        修改人          修改内容
 * 2023-02-20 09:34   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SnsCalDetAreaCoor(void)
{
#if 0
    uint8 Lu8Group;
    uint8 Lu8Channel;
    float LfSnsObjX = 0.0;
    float LfSnsObjY = 0.0;

    float LfCarObjX = 0.0;
    float LfCarObjY = 0.0;
    float LfCornerSnsX_Coor,LfCornerSnsY_Coor,LfCornerSnsAngle;

    Lu8Group = 0;
    LfSnsObjX = 5000;
    LfSnsObjY = 0;
    for (Lu8Channel = 0; Lu8Channel < 6; Lu8Channel++)
    {
        PubAI_UpdateSnsInCarCoor(GstrRamSnsCoor[Lu8Group][Lu8Channel].cRadarX, GstrRamSnsCoor[Lu8Group][Lu8Channel].cRadarY, \
            GstrSnsOutAngle[Lu8Group].fSinA[Lu8Channel], GstrSnsOutAngle[Lu8Group].fCosA[Lu8Channel]);
        PubAI_TransObjSnsCoorToCarCoor(&LfSnsObjX, &LfSnsObjY, &LfCarObjX, &LfCarObjY);
        VS_PRINT("Front SnsCoor,Ch:%d,X:%.3f,Y:%.3f,Angle:%.3f\r\n", Lu8Channel, GstrRamSnsCoor[Lu8Group][Lu8Channel].cRadarX,\
            GstrRamSnsCoor[Lu8Group][Lu8Channel].cRadarY,GstrRamSnsAngle_Ram[Lu8Group][Lu8Channel].cOuterAngle);
        VS_PRINT("Front AreaCoor,Ch:%d,X:%.3f,Y:%.3f\r\n\r\n", Lu8Channel, LfCarObjX, LfCarObjY);
    }
    /* 前左角 */
    
#if 0
    /* X02 */
    LfCornerSnsX_Coor = 3677;
    LfCornerSnsY_Coor = 900;
    LfCornerSnsAngle = 1.0472;
#endif

#if 1
    /* X03 */
    LfCornerSnsX_Coor = 3686;
    LfCornerSnsY_Coor = 889.4;
    LfCornerSnsAngle = 1.0472;
#endif

#if 0
    /* X04 */
    LfCornerSnsX_Coor = 3526;
    LfCornerSnsY_Coor = 892;
    LfCornerSnsAngle = 1.0472;
#endif

    PubAI_UpdateSnsInCarCoor(LfCornerSnsX_Coor, LfCornerSnsY_Coor,sinf(LfCornerSnsAngle), cosf(LfCornerSnsAngle));
    PubAI_TransObjSnsCoorToCarCoor(&LfSnsObjX, &LfSnsObjY, &LfCarObjX, &LfCarObjY);
    VS_PRINT("Front Corner Sns,Ch:%d,X:%.3f,Y:%.3f,Angle:%.3f\r\n", Lu8Channel, LfCornerSnsX_Coor,LfCornerSnsY_Coor,LfCornerSnsAngle);
    VS_PRINT("Front Corner,Ch:%d,X:%.3f,Y:%.3f\r\n", Lu8Channel, LfCarObjX, LfCarObjY);
    
    VS_PRINT("End\r\n\r\n");

    Lu8Group = 1;
    LfSnsObjX = -5000;
    LfSnsObjY = 0;
    for (Lu8Channel = 0; Lu8Channel < 6; Lu8Channel++)
    {
        PubAI_UpdateSnsInCarCoor(GstrRamSnsCoor[Lu8Group][Lu8Channel].cRadarX, GstrRamSnsCoor[Lu8Group][Lu8Channel].cRadarY, \
            GstrSnsOutAngle[Lu8Group].fSinA[Lu8Channel], GstrSnsOutAngle[Lu8Group].fCosA[Lu8Channel]);
        PubAI_TransObjSnsCoorToCarCoor(&LfSnsObjX, &LfSnsObjY, &LfCarObjX, &LfCarObjY);
        VS_PRINT("Rear SnsCoor,Ch:%d,X:%.3f,Y:%.3f,Angle:%.3f\r\n", Lu8Channel, GstrRamSnsCoor[Lu8Group][Lu8Channel].cRadarX,\
            GstrRamSnsCoor[Lu8Group][Lu8Channel].cRadarY,GstrRamSnsAngle_Ram[Lu8Group][Lu8Channel].cOuterAngle);
        VS_PRINT("Rear AreaCoor,Ch:%d,X:%.3f,Y:%.3f\r\n\r\n", Lu8Channel, LfCarObjX, LfCarObjY);
    }
    /* 后左角 */
#if 0
    /* X02 */
    LfCornerSnsX_Coor = -890.4;
    LfCornerSnsY_Coor = 873.4;
    LfCornerSnsAngle = -1.0472;
#endif

#if 1
    /* X03 */
    LfCornerSnsX_Coor = -815;
    LfCornerSnsY_Coor = 862.7;
    LfCornerSnsAngle = -1.0472;
#endif

#if 0
    /* X04 */
    LfCornerSnsX_Coor = -827.5;
    LfCornerSnsY_Coor = 859;
    LfCornerSnsAngle = -1.0472;
#endif
    PubAI_UpdateSnsInCarCoor(LfCornerSnsX_Coor, LfCornerSnsY_Coor,sinf(LfCornerSnsAngle), cosf(LfCornerSnsAngle));
    PubAI_TransObjSnsCoorToCarCoor(&LfSnsObjX, &LfSnsObjY, &LfCarObjX, &LfCarObjY);
    VS_PRINT("Rear Corner,Ch:%d,X:%.3f,Y:%.3f,Angle:%.3f\r\n", Lu8Channel, LfCornerSnsX_Coor, LfCornerSnsY_Coor,LfCornerSnsAngle);
    VS_PRINT("Rear Corner,Ch:%d,X:%.3f,Y:%.3f\r\n", Lu8Channel, LfCarObjX, LfCarObjY);
    VS_PRINT("End\r\n\r\n");
#endif
}


/******************************************************************************
 * 函数名称: ParkingGuidenceDataInit
 * 
 * 功能描述: 泊车工作状态数据初始化
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-08 11:11   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void ParkingGuidenceDataInit(void)
{
    GstrParkingGuidenceData.eCurrentAdasSts = APA_NONE;
    GstrParkingGuidenceData.eLastAdasSts = APA_NONE;
    GstrParkingGuidenceData.eLowVolPwrMdFlag2 = LOWPOWER_MODFLAG2_LOCAL;
    GstrParkingGuidenceData.fSummonSideDis = 0;
    GstrParkingGuidenceData.fSearchToGuidenceCarAngle = 0;
    GstrParkingGuidenceData.fCarAngleSub = 0;
    GstrParkingGuidenceData.u8ParkingFinishFlag = 0;
    GstrParkingGuidenceData.u8DeleAllMapFlag = 0;
    GstrParkingGuidenceData.fParkingFinishDrvDis = 0;
    GstrParkingGuidenceData.eParking_Sts = PARKING_STANDBY;
    
    GstrParkingGuidenceData.u32SameFreqNoiseUpdateTime[0] = 0;
    GstrParkingGuidenceData.u32SameFreqNoiseUpdateTime[1] = 0;
    GstrParkingGuidenceData.u32LeftSideNoiseUpdateTime[0] = 0;
    GstrParkingGuidenceData.u32LeftSideNoiseUpdateTime[1] = 0;
    GstrParkingGuidenceData.u32RightSideNoiseUpdateTime[0] = 0;
    GstrParkingGuidenceData.u32RightSideNoiseUpdateTime[1] = 0;

    GstrParkingGuidenceData.u8SameFreqNoiseFlag[0] = 0;
    GstrParkingGuidenceData.u8SameFreqNoiseFlag[1] = 0;
    GstrParkingGuidenceData.u8LeftSideFreqNoiseFlag[0] = 0;
    GstrParkingGuidenceData.u8LeftSideFreqNoiseFlag[1] = 0;
    GstrParkingGuidenceData.u8RightSideFreqNoiseFlag[0] = 0;
    GstrParkingGuidenceData.u8RightSideFreqNoiseFlag[1] = 0;  
    GstrParkingGuidenceData.u8ParkingStsLockFlag = 0;


    GstrParkingGuidenceData.eSlotType = SLOT_TYPE_NONE;
    GstrParkingGuidenceData.eParkDir = PARK_DIR_NONE;
    GstrParkingGuidenceData.fCarAngleSubBackup = 0;
    GstrParkingGuidenceData.u8SlotTypeLockFlag = 0;
    GstrParkingGuidenceData.u8CarForwardVerticalSlotLockFlag = 0;

    GstrParkingGuidenceData.fFrontSideStartMapDis = 0;
    GstrParkingGuidenceData.fRearSideStartMapDis = 0;
    
}



/******************************************************************************
 * 函数名称: MapLineBufInit
 * 
 * 功能描述: 前后保线状Map融合缓存
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-01-16 17:02   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void MapLineBufInit(PDCSnsGroupType LenuSnsGroup)
{
    uint8 i;
    MapLineBufType *LstrMapLineBuf;

    LstrMapLineBuf = &GstrMapLineBuf[LenuSnsGroup];

    for(i = 0; i < MAP_FUSION_LINE_CNT; i++)
    {
        LstrMapLineBuf->LineMapUnit[i] = GstrMapLineFusionUnitInit;
    }
    LstrMapLineBuf->u8WaitFusionLineCnt = 0;
}

/******************************************************************************
 * 函数名称: MapBigWallBufInit
 * 
 * 功能描述: 初始化大墙Map缓存
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-23 16:23   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void MapBigWallBufInit(PDCSnsGroupType LenuSnsGroup)
{
    uint8 i;
    MapBigWallBufType *LstrMapBigWallBuf;
    
    LstrMapBigWallBuf = &GstrMapBigWallBuf[LenuSnsGroup];
    for(i = 0; i < MAP_BIG_WALL_BUF_CNT; i++)
    {
        LstrMapBigWallBuf->BigWallUnit[i] = GstrMapBigWallUnitInit;
    }
    LstrMapBigWallBuf->u8BigWallPointCnt = 0;
}


/******************************************************************************
 * 函数名称: MapClearBuffer
 * 
 * 功能描述: 清除一个Map的数据结构
 * 
 * 输入参数:LptrF_R_Map--待清除数据结构的指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-14 14:59   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void MapClearBuffer(ObjMapCoorType* LptrF_R_Map)
{
    *LptrF_R_Map = GstrObjMapCoorInit;
}

/******************************************************************************
 * 函数名称: ObjMapToCANInit
 * 
 * 功能描述: Map数据To CAN 初始化
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-14 15:01   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void ObjMapToCANInit(void)
{
    eMapObjIdType LenuBufId;

    for(LenuBufId=MAP_OBJ_0;LenuBufId<MAP_OBJ_TO_CAN_MAX;LenuBufId++)
    {
        GstrObjMapToCAN[LenuBufId] = GstrObjMapToCANInit;
    }
}

/******************************************************************************
 * 函数名称: ObjMapInit
 * 
 * 功能描述: Map初始化
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-14 15:02   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void ObjMapInit(void)
{
    eMapObjIdType LenuBufId;
    GstrObjMap.u8MapNum = 0;
    GstrObjMap.eSideMapFusionId = MAP_NONE;
    for(LenuBufId=MAP_OBJ_0; LenuBufId<MAP_OBJ_NUM;LenuBufId++)
    {
        MapClearBuffer(&GstrObjMap.ObjMapCoor[LenuBufId]);
    }
}

/******************************************************************************
 * 函数名称: PDC_AreaObjInit
 * 
 * 功能描述: 前后PDC区域数据初始化
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-14 15:07   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void PDC_AreaObjInit(PDCSnsGroupType LenuSnsGroup)
{
    uint8 i,j;

    for(i = 0; i < OBJ_PDC_AREA_NUM; i++)
    {
        GstrPDC_AreaObj[LenuSnsGroup].u16MinDis[i] = MAP_INVALID_DIS;
    }
}


/******************************************************************************
 * 函数名称: F_R_ObjMapPowerOnInit
 * 
 * 功能描述: 前后雷达Map上电初始化
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-08-08 20:04   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void ObjMapPowerOnInit(void)
{
    PDC_AreaObjInit(PDC_SNS_GROUP_FRONT);
    PDC_AreaObjInit(PDC_SNS_GROUP_REAR);
    MapBigWallBufInit(PDC_SNS_GROUP_FRONT);
    MapBigWallBufInit(PDC_SNS_GROUP_REAR);
    MapLineBufInit(PDC_SNS_GROUP_FRONT);
    MapLineBufInit(PDC_SNS_GROUP_REAR);
    ObjMapInit();
    ObjMapToCANInit();
    SnsCalDetAreaCoor();
    ParkingGuidenceDataInit(); 
    G_TimeStampPoint = 0;
}

/******************************************************************************
 * 函数名称: JudgePointToMapPointNear
 * 
 * 功能描述: 计算点和Map是否匹配
 * 
 * 输入参数:LpstrObjPoint--障碍物坐标指针； LpstrMapPoint--点状Map坐标指针 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-04-18 21:18   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static uint8 JudgePointToMapPointNear(ObjCoorType *LpstrObjPoint, ObjCoorType *LpstrMapPoint,ObjMAPAreaType LenuObjMAPArea)
{
    uint8 Lu8TwoPointNearFlag = 0;
    float LfXSub,LfYSub;

    LfXSub = ABS(LpstrObjPoint->fObjX,LpstrMapPoint->fObjX);
    LfYSub = ABS(LpstrObjPoint->fObjY,LpstrMapPoint->fObjY);
    if((LenuObjMAPArea > MAP_AREA2)&&(LenuObjMAPArea < MAP_AREA6))
    {
        if(LfXSub < F_R_OBJ_MAP_X_SUB)
        {
            if(LfYSub < F_R_OBJ_MAP_Y_SUB)
            {
                Lu8TwoPointNearFlag = 1;
            }
            else
            {
                Lu8TwoPointNearFlag = 0;
            }
        }
        else
        {
            Lu8TwoPointNearFlag = 0;
        }
    }
    else
    {
        if(LfXSub < F_R_SIDE_OBJ_MAP_X_SUB)
        {
            if(LfYSub < F_R_SIDE_OBJ_MAP_Y_SUB)
            {
                Lu8TwoPointNearFlag = 1;
            }
            else
            {
                Lu8TwoPointNearFlag = 0;
            }
        }
        else
        {
            Lu8TwoPointNearFlag = 0;
        }
    }

    return Lu8TwoPointNearFlag;
}


/******************************************************************************
 * 函数名称: JudgePointToMapLineOnePointNear
 * 
 * 功能描述: 判断最新点和已经存在的线状Map最新点是否匹配
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-23 21:07   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static uint8 JudgePointToMapLineOnePointNear(ObjCoorType *LpstrObjPoint, ObjCoorType *LpstrMapPoint)
{
    uint8 Lu8TwoPointNearFlag = 0;
    float LfXSub,LfYSub;

    LfXSub = ABS(LpstrObjPoint->fObjX,LpstrMapPoint->fObjX);
    LfYSub = ABS(LpstrObjPoint->fObjY,LpstrMapPoint->fObjY);

    if(LfXSub < MAP_LINE_ONE_POINT_X_LIMIT)
    {
        if(LfYSub < MAP_LINE_ONE_POINT_Y_LIMIT)
        {
            Lu8TwoPointNearFlag = 1;
        }
        else
        {
            Lu8TwoPointNearFlag = 0;
        }
    }
    else
    {
        Lu8TwoPointNearFlag = 0;
    }
    return Lu8TwoPointNearFlag;
}



/******************************************************************************
 * 函数名称: JudgePointToMapLineNear
 * 
 * 功能描述: 计算点和线装Map进行匹配
 * 
 * 输入参数:LpstrObjPoint--障碍物坐标指针； LpstrMap_P1_Point--线状Map坐标1指针；  LpstrMap_P2_Point--线状Map坐标2指针;LfLineMapDis--线装Map的距离
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-04-18 21:19   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static uint8 JudgePointToMapLineNear(ObjCoorType *LpstrObjPoint, ObjCoorType *LpstrMap_P1_Point,ObjCoorType *LpstrMap_P2_Point)
#if 0
{
    uint8 Lu8TwoPointNearFlag = 0;
    /* 判断点和线状Map的匹配，首先判断该点和线的关系 */
    Pub_CalAiPointType LstrVectorP1_P0;
    Pub_CalAiPointType LstrVectorP1_P2;
    float LfVectorP1_P0_DotProduct_P1_P2;
    float LfVectorP1_P2_Square;
    float LfRadius;
    float LfPointToSegmentDis;
    float LfX0,LfY0,LfX1,LfY1,LfX2,LfY2;
    float LfXSub,LfYSub;

    LfX0 = LpstrObjPoint->fObjX;
    LfY0 = LpstrObjPoint->fObjY;
    LfX1 = LpstrMap_P1_Point->fObjX;
    LfY1 = LpstrMap_P1_Point->fObjX;
    LfX2 = LpstrMap_P1_Point->fObjX;
    LfY2 = LpstrMap_P1_Point->fObjX;
    

    LstrVectorP1_P0.fPoint_X = LfX0 - LfX1;
    LstrVectorP1_P0.fPoint_Y = LfY0 - LfY1;

    LstrVectorP1_P2.fPoint_X = LfX2 - LfX1;
    LstrVectorP1_P2.fPoint_Y = LfY2 - LfY1;

    LfVectorP1_P0_DotProduct_P1_P2 = (LstrVectorP1_P0.fPoint_X*LstrVectorP1_P2.fPoint_X)+(LstrVectorP1_P0.fPoint_Y*LstrVectorP1_P2.fPoint_Y);
    LfVectorP1_P2_Square = (LstrVectorP1_P2.fPoint_X*LstrVectorP1_P2.fPoint_X)+(LstrVectorP1_P2.fPoint_Y*LstrVectorP1_P2.fPoint_Y);
    LfRadius = LfVectorP1_P0_DotProduct_P1_P2/LfVectorP1_P2_Square;

    if(LfRadius < 0.0)
    {
        /* 障碍物点落在线段的左侧(P1点左边这一侧) */
        LfXSub = ABS(LpstrObjPoint->fObjX,LpstrMap_P1_Point->fObjX);
        LfYSub = ABS(LpstrObjPoint->fObjY,LpstrMap_P1_Point->fObjY);
        if(LfXSub < MAP_LINE_ONE_POINT_X_LIMIT)
        {
            if(LfYSub < MAP_LINE_ONE_POINT_Y_LIMIT)
            {
                Lu8TwoPointNearFlag = 1;
            }
            else
            {
                Lu8TwoPointNearFlag = 0;
            }
        }
        else
        {
            Lu8TwoPointNearFlag = 0;
        } 
    }
    else if(LfRadius > 1.0)
    {
        /* 障碍物点落在线段的右侧(P2点右边这一侧) */
        LfXSub = ABS(LpstrObjPoint->fObjX,LpstrMap_P2_Point->fObjX);
        LfYSub = ABS(LpstrObjPoint->fObjY,LpstrMap_P2_Point->fObjY);
        if(LfXSub < MAP_LINE_ONE_POINT_X_LIMIT)
        {
            if(LfYSub < MAP_LINE_ONE_POINT_Y_LIMIT)
            {
                Lu8TwoPointNearFlag = 1;
            }
            else
            {
                Lu8TwoPointNearFlag = 0;
            }
        }
        else
        {
            Lu8TwoPointNearFlag = 0;
        }
    }
    else
    {
        /* 障碍物落在线段上 */
        LfPointToSegmentDis = PubAI_CalOnePointToOtherPointDis(LfX0,LfY0,LfX1,LfY1,LfX2,LfY2);
        if(LfPointToSegmentDis < MAP_LINE_ON_LINE_DIS_LIMIT)
        {
            Lu8TwoPointNearFlag = 1;
        }
        else
        {
            Lu8TwoPointNearFlag = 0;   /* 障碍物匹配不上 */
        }
    }
    return Lu8TwoPointNearFlag;
}

#else
{
    uint8 Lu8TwoPointNearFlag = 0;
    float LfObjToP1_P2_Dis;

    LfObjToP1_P2_Dis = PubAI_CalOnePointToSegmentDis(LpstrObjPoint->fObjX,LpstrObjPoint->fObjY,\
        LpstrMap_P1_Point->fObjX,LpstrMap_P1_Point->fObjY,LpstrMap_P2_Point->fObjX,LpstrMap_P2_Point->fObjY);
    if(LfObjToP1_P2_Dis < F_R_OBJ_MAP_NO_MOVE)
    {
        Lu8TwoPointNearFlag = 2;    /* 障碍物不移动 */
    }
    else if(LfObjToP1_P2_Dis < F_R_OBJ_MAP_MATCH)
    {
        Lu8TwoPointNearFlag = 1;   /* 障碍物匹配上 */
    }
    else
    {
        Lu8TwoPointNearFlag = 0;   /* 障碍物匹配不上 */
    }
    
    return Lu8TwoPointNearFlag;
}
#endif
/******************************************************************************
 * 函数名称: MapExistProbAdd
 * 
 * 功能描述: Map置信度累加
 * 
 * 输入参数:LeMapIndex--Map 的Index
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-04-21 10:41   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void MapExistProbAdd(eMapObjIdType LeMapId)
{
    ObjMapInfoType *LstrObjMap;
    LstrObjMap = &GstrObjMap;
    if(LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb < OBJ_PROB_LV6)
    {
        LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb++;
#if 0
        if((LeMapId == 0x00)&&(GfMessageTime > 10)&&(GfMessageTime < 15))
        {
            VS_PRINT("Map Prob Add,Map_Id:%d,Time:%.3f,P1_X:%.3f,P1_Y:%.3f,BumperDis:%.3f,DetailArea:%d,Prob:%d,HoldTime:%d\r\n",LeMapId,GfMessageTime,\
                LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjX,LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY,\
                LstrObjMap->ObjMapCoor[LeMapId].fMapToBumper_Dis,LstrObjMap->ObjMapCoor[LeMapId].eP1_DetailArea,\
                LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb,LstrObjMap->ObjMapCoor[LeMapId].u16NoUpdateHoldTime);
        }

#endif
    }
}

/******************************************************************************
 * 函数名称: DeleteMap
 * 
 * 功能描述: 删除一个MAP
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-04-28 12:00   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void DeleteMap(eMapObjIdType  LeMapIndex)
{
    ObjMapInfoType *LstrObjMap;
    LstrObjMap = &GstrObjMap;

    MapClearBuffer(&LstrObjMap->ObjMapCoor[LeMapIndex]);
#if 0
    if((GfMessageTime > 40)&&(GfMessageTime < 44))
    {
        VS_PRINT("Delete Map,Time:%.3f,Map_Num:%d,Map Id:%d,BumperDis:%.3f\r\n",GfMessageTime,LstrObjMap->u8MapNum,LeMapIndex,\
        LstrObjMap->ObjMapCoor[LeMapIndex].fMapToBumper_Dis);
    }
#endif
}

/******************************************************************************
 * 函数名称: JudgeObjInArea
 * 
 * 功能描述: 判断障碍物在PDC分区中的位置
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-03-23 20:25   V0.1      AntonyFang   初次发布
 ******************************************************************************/
ObjMAPAreaType JudgeObjInArea(PDCSnsGroupType LenuSnsGroup,float LfObjX,float LfObjY)
{
    ObjMAPAreaType LenuObjArea = MAP_AREA_NONE;
    Pub_Point2LineType LenuPoint2LineRelation;

    ObjMapAreaLineType LenuStartLine;
    ObjMapAreaLineType LenuEndLine;
    ObjMapAreaLineType LenuLineInx;
    float P1_X;
    float P1_Y;
    float P2_X;
    float P2_Y;
    
    if(LfObjY > 0)
    {   
        /* 障碍物在保杠的左侧，从中间往左边找 */
        LenuStartLine = MAP_LINE3_4;
        LenuEndLine = MAP_LINE0_1;
        for(LenuLineInx = MAP_LINE3_4; LenuLineInx >= MAP_LINE0_1; LenuLineInx--)
        {
            if(LenuSnsGroup == PDC_SNS_GROUP_FRONT)
            {
                /* 此处左右的相对视角是以P1点指向P2点来看的 */
                P2_X = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaEnd_X;
                P2_Y = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaEnd_Y;
                P1_X = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaStart_X;
                P1_Y = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaStart_Y;
            }
            else
            {
                /* 此处左右的相对视角是以P1点指向P2点来看的 */
                P1_X = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaEnd_X;
                P1_Y = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaEnd_Y;
                P2_X = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaStart_X;
                P2_Y = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaStart_Y;
            }
            
            LenuPoint2LineRelation = PubAI_CalPoint2LineRelation(P1_X,P1_Y,P2_X,P2_Y,LfObjX,LfObjY);

            if(LenuLineInx == LenuEndLine)
            {
                if((LenuPoint2LineRelation == PUB_POINT_ON_LINE_RIGHT)||(LenuPoint2LineRelation == PUB_POINT_ON_LINE))
                {
                    return MAP_AREA1;
                }
                else
                {
                    return MAP_AREA0;
                }
            }
            else
            {
                if((LenuPoint2LineRelation == PUB_POINT_ON_LINE_RIGHT)||(LenuPoint2LineRelation == PUB_POINT_ON_LINE))
                {
                    LenuObjArea = (ObjMAPAreaType)(LenuLineInx + 1);
                    return LenuObjArea;
                }
            }
        }
    }
    else
    {
        /* 障碍物在保杠的右侧，从中间往右边找 */
        LenuStartLine = MAP_LINE4_5;
        LenuEndLine = MAP_LINE7_8;
        
        for(LenuLineInx = MAP_LINE4_5; LenuLineInx <= MAP_LINE7_8; LenuLineInx++)
        {
            if(LenuSnsGroup == PDC_SNS_GROUP_FRONT)
            {
                /* 此处左右的相对视角是以P1点指向P2点来看的 */
                P2_X = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaEnd_X;
                P2_Y = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaEnd_Y;
                P1_X = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaStart_X;
                P1_Y = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaStart_Y;
            }
            else
            {
                /* 此处左右的相对视角是以P1点指向P2点来看的 */
                P1_X = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaEnd_X;
                P1_Y = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaEnd_Y;
                P2_X = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaStart_X;
                P2_Y = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaStart_Y;
            }

            LenuPoint2LineRelation = PubAI_CalPoint2LineRelation(P1_X,P1_Y,P2_X,P2_Y,LfObjX,LfObjY);

            if(LenuLineInx == LenuEndLine)
            {
                if(LenuPoint2LineRelation < PUB_POINT_ON_LINE_RIGHT)
                {
                    return MAP_AREA7;
                }
                else
                {
                    return MAP_AREA8;
                }
            }
            else
            {
                if(LenuPoint2LineRelation < PUB_POINT_ON_LINE_RIGHT)
                {
                    LenuObjArea = (ObjMAPAreaType)(LenuLineInx);
                    return LenuObjArea;
                }
            }
        }
    }

    return LenuObjArea;
}


/******************************************************************************
 * 函数名称: CalPointToBumperRelationAndDis
 * 
 * 功能描述: 计算一个点到保杠的关系以及距离
 * 
 * 输入参数:LpstrPointCarCoor--待计算点的坐标指针；LpstrPointToBumperDis--待返回Bumper距离的指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-04-23 11:33   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static ObjMAPAreaType CalPointToBumperRelationAndDis(ObjCoorType *LpstrPointCarCoor,float *LpstrPointToBumperDis)
{
    ObjMAPAreaType LenuMapDetailArea = MAP_AREA_NONE;
    ObjCoorType LstrBumperStartPoint;
    ObjCoorType LstrBumperEndPoint;
    float LfMinAreaX;

    if(LpstrPointCarCoor->fObjX > (GstrMapAreaRam[PDC_SNS_GROUP_FRONT][MAP_LINE0_1].fAreaStart_X))
    {
        LenuMapDetailArea = JudgeObjInArea(PDC_SNS_GROUP_FRONT,LpstrPointCarCoor->fObjX,LpstrPointCarCoor->fObjY);
        if((LenuMapDetailArea < MAP_AREA1))
        {
            *LpstrPointToBumperDis = LpstrPointCarCoor->fObjY - GstrMapAreaRam[PDC_SNS_GROUP_FRONT][MAP_LINE0_1].fAreaStart_Y;
        }
        else if(LenuMapDetailArea > MAP_AREA7)
        {
            *LpstrPointToBumperDis = -(LpstrPointCarCoor->fObjY - GstrMapAreaRam[PDC_SNS_GROUP_FRONT][MAP_LINE7_8].fAreaStart_Y);
        }
        else
        {
            LstrBumperStartPoint.fObjX = GstrMapAreaRam[PDC_SNS_GROUP_FRONT][LenuMapDetailArea-1].fAreaStart_X;
            LstrBumperStartPoint.fObjY = GstrMapAreaRam[PDC_SNS_GROUP_FRONT][LenuMapDetailArea-1].fAreaStart_Y;
            LstrBumperEndPoint.fObjX = GstrMapAreaRam[PDC_SNS_GROUP_FRONT][LenuMapDetailArea].fAreaStart_X;
            LstrBumperEndPoint.fObjY = GstrMapAreaRam[PDC_SNS_GROUP_FRONT][LenuMapDetailArea].fAreaStart_Y;
            *LpstrPointToBumperDis = PubAI_CalOnePointToSegmentDis(LpstrPointCarCoor->fObjX,LpstrPointCarCoor->fObjY,\
                LstrBumperStartPoint.fObjX,LstrBumperStartPoint.fObjY,LstrBumperEndPoint.fObjX,LstrBumperEndPoint.fObjY);
            if((LenuMapDetailArea > MAP_AREA2)&&(LenuMapDetailArea < MAP_AREA6))
            {
                LfMinAreaX = GstrMapAreaRam[PDC_SNS_GROUP_FRONT][LenuMapDetailArea-1].fAreaStart_X;
                if(LfMinAreaX > GstrMapAreaRam[PDC_SNS_GROUP_FRONT][LenuMapDetailArea].fAreaStart_X)
                {
                    LfMinAreaX = GstrMapAreaRam[PDC_SNS_GROUP_FRONT][LenuMapDetailArea].fAreaStart_X;
                }
                if(LpstrPointCarCoor->fObjX < (LfMinAreaX-40))
                {
                    *LpstrPointToBumperDis = 0;
                }
            }
        }
    }
    else if(LpstrPointCarCoor->fObjX < (GstrMapAreaRam[PDC_SNS_GROUP_REAR][MAP_LINE0_1].fAreaStart_X))
    {
        LenuMapDetailArea = JudgeObjInArea(PDC_SNS_GROUP_REAR,LpstrPointCarCoor->fObjX,LpstrPointCarCoor->fObjY);
        if((LenuMapDetailArea < MAP_AREA1))
        {
            *LpstrPointToBumperDis = LpstrPointCarCoor->fObjY - GstrMapAreaRam[PDC_SNS_GROUP_REAR][MAP_LINE0_1].fAreaStart_Y;
        }
        else if(LenuMapDetailArea > MAP_AREA7)
        {
            *LpstrPointToBumperDis = -(LpstrPointCarCoor->fObjY - GstrMapAreaRam[PDC_SNS_GROUP_REAR][MAP_LINE7_8].fAreaStart_Y);
        }
        else
        {
            LstrBumperStartPoint.fObjX = GstrMapAreaRam[PDC_SNS_GROUP_REAR][LenuMapDetailArea-1].fAreaStart_X;
            LstrBumperStartPoint.fObjY = GstrMapAreaRam[PDC_SNS_GROUP_REAR][LenuMapDetailArea-1].fAreaStart_Y;
            LstrBumperEndPoint.fObjX = GstrMapAreaRam[PDC_SNS_GROUP_REAR][LenuMapDetailArea].fAreaStart_X;
            LstrBumperEndPoint.fObjY = GstrMapAreaRam[PDC_SNS_GROUP_REAR][LenuMapDetailArea].fAreaStart_Y;
            *LpstrPointToBumperDis = PubAI_CalOnePointToSegmentDis(LpstrPointCarCoor->fObjX,LpstrPointCarCoor->fObjY,\
                LstrBumperStartPoint.fObjX,LstrBumperStartPoint.fObjY,LstrBumperEndPoint.fObjX,LstrBumperEndPoint.fObjY);
            if((LenuMapDetailArea > MAP_AREA2)&&(LenuMapDetailArea < MAP_AREA6))
            {
                LfMinAreaX = GstrMapAreaRam[PDC_SNS_GROUP_REAR][LenuMapDetailArea-1].fAreaStart_X;
                if(LfMinAreaX < GstrMapAreaRam[PDC_SNS_GROUP_REAR][LenuMapDetailArea].fAreaStart_X)
                {
                    LfMinAreaX = GstrMapAreaRam[PDC_SNS_GROUP_REAR][LenuMapDetailArea].fAreaStart_X;
                }
                if(LpstrPointCarCoor->fObjX > (LfMinAreaX+40))
                {
                    *LpstrPointToBumperDis = 0;
                }
            }
            if((LenuMapDetailArea == MAP_AREA3)||(LenuMapDetailArea == MAP_AREA5))
            {
                if(*LpstrPointToBumperDis > REAR_MAP_TO_BUMPER_DIS_CMP_CONNER)
                {
                    *LpstrPointToBumperDis = *LpstrPointToBumperDis - REAR_MAP_TO_BUMPER_DIS_CMP_CONNER;
                }
                else
                {
                    *LpstrPointToBumperDis = 0;
                }
            }
        }
    }
    else
    {
        if(LpstrPointCarCoor->fObjY > 900)
        {
            LenuMapDetailArea = MAP_AREA0;
            *LpstrPointToBumperDis = LpstrPointCarCoor->fObjY - GstrMapAreaRam[PDC_SNS_GROUP_FRONT][MAP_LINE0_1].fAreaStart_Y;
        }
        else if(LpstrPointCarCoor->fObjY < -900)
        {
            LenuMapDetailArea = MAP_AREA8;
            *LpstrPointToBumperDis = -(LpstrPointCarCoor->fObjY - GstrMapAreaRam[PDC_SNS_GROUP_FRONT][MAP_LINE7_8].fAreaStart_Y);
        }
        else
        {
            LenuMapDetailArea = MAP_AREA_NONE;
            *LpstrPointToBumperDis = 0;
        }
    }

    return LenuMapDetailArea;
}


/******************************************************************************
 * 函数名称: FindEmptyMapId
 * 
 * 功能描述: 在Map缓存中查找空ID，用于新建Map
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-14 15:26   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static eMapObjIdType FindEmptyMapId(void)
{
    ObjMapInfoType *LstrObjMap;
    eMapObjIdType LeMapId = MAP_NONE;
    eMapObjIdType LeMaxCreatTime_MapId;
    uint8 i;
    uint8 j;
    uint32 Lu32MinCreatTime = 0;
    
    LstrObjMap = &GstrObjMap;
    if(LstrObjMap->u8MapNum != MAP_OBJ_NUM)
    {
        /* 未满，遍历查询已经存在的缓存区域，找到空位置进行建立Map */
        for(i = MAP_OBJ_0; i < MAP_OBJ_NUM; i++)
        {
            if(!LstrObjMap->ObjMapCoor[i].u8MapExistFlag)
            {
                LeMapId = (eMapObjIdType)i;
                LstrObjMap->u8MapNum++;
                break;
            }
        }
    }
    else
    {
        LeMaxCreatTime_MapId = MAP_OBJ_0;
        /* 当前Map缓存已满，需要把距离保杠最远的点删除，强制替换即可 */
        Lu32MinCreatTime = LstrObjMap->ObjMapCoor[0].u32CreatMapTime;
        for(j = MAP_OBJ_1; j < MAP_OBJ_NUM; j++)
        {
            if(Lu32MinCreatTime > LstrObjMap->ObjMapCoor[j].u32CreatMapTime)
            {
                Lu32MinCreatTime = LstrObjMap->ObjMapCoor[j].u32CreatMapTime;
                LeMaxCreatTime_MapId = (eMapObjIdType)j;
            }
        }
        LeMapId = LeMaxCreatTime_MapId;
        MapClearBuffer(&LstrObjMap->ObjMapCoor[LeMapId]);
    }
    return LeMapId;
}

/******************************************************************************
 * 函数名称: NewBuildMapPoint
 * 
 * 功能描述: 新建一个Map点
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-04-23 09:22   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static eMapObjIdType NewBuildMapPoint(SnsAreaMapType *LpstrSnsAreaMap)
{      
    ObjMapInfoType *LstrObjMap;
    eMapObjIdType LeMapId;
    uint8 i;
    float LstrPointToBumperDis;
    ObjMAPAreaType LenuMapDetailArea = MAP_AREA_NONE;
    uint8 Lu8MasterDisInx;
    uint8 Lu8Group = 0;
    float LfP1_Coor_Y_Abs;

    LstrObjMap = &GstrObjMap;
    

    /* 创建Map前先计算Map到保杠的距离，小于设定距离才开始新建Map */
    LenuMapDetailArea = CalPointToBumperRelationAndDis(&LpstrSnsAreaMap->P1_CarCoor,&LstrPointToBumperDis);
#if 0

    if(LstrPointToBumperDis > MAP_DETECT_ENTER_DIS)
    {
        return MAP_NONE;
    }
#endif

    LeMapId = FindEmptyMapId();

    LstrObjMap->ObjMapCoor[LeMapId].eMapObjId = LeMapId;
    LstrObjMap->ObjMapCoor[LeMapId].eMapObjType = OBJ_TYPE_POINT;
    LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb = OBJ_PROB_LV1;    /* 首次生成Map，置信度最低 */

    LstrObjMap->ObjMapCoor[LeMapId].eObjHeightProb = OBJ_PROB_LV1;

    LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor = LpstrSnsAreaMap->P1_CarCoor;
    LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor = LpstrSnsAreaMap->P1_CarCoor;
    LstrObjMap->ObjMapCoor[LeMapId].P1_OdoCoor = LpstrSnsAreaMap->P1_OdoCoor;
    LstrObjMap->ObjMapCoor[LeMapId].P2_OdoCoor = LpstrSnsAreaMap->P1_OdoCoor;
    
    LstrObjMap->ObjMapCoor[LeMapId].fMapToBumper_Dis = LstrPointToBumperDis;
    LstrObjMap->ObjMapCoor[LeMapId].fP1_ToBumper_Dis = LstrPointToBumperDis;
    LstrObjMap->ObjMapCoor[LeMapId].fP2_ToBumper_Dis = LstrPointToBumperDis;
    LstrObjMap->ObjMapCoor[LeMapId].eP1_DetailArea = LenuMapDetailArea;
    LstrObjMap->ObjMapCoor[LeMapId].eP2_DetailArea = LenuMapDetailArea;

    LfP1_Coor_Y_Abs = ABS_VALUE(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY);
    if(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjX < GstrRamSnsCoor[1][0].cRadarX)
    {
        if(LfP1_Coor_Y_Abs < (GstrRamSnsCoor[1][0].cRadarY-50))
        {
            LstrObjMap->ObjMapCoor[LeMapId].u8CreatMapInRearBumperInnerFlag = 1;
        }
    }

    
    if(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjX > 0)
    {
        LstrObjMap->ObjMapCoor[LeMapId].eObjBumperArea = OBJ_FRONT_BUMP;
        Lu8Group = 0;
    }
    else
    {
        LstrObjMap->ObjMapCoor[LeMapId].eObjBumperArea = OBJ_REAR_BUMP;
        Lu8Group = 1;
    }

    if(GstrSnsCarMovSts[0][0].eCarDir == SNS_CAR_STOP)
    {
        LstrObjMap->ObjMapCoor[LeMapId].eObjMoveSts = OBJ_TO_CAR_STOP_STOP;
    }
    else if(GstrSnsCarMovSts[0][0].eCarDir == SNS_CAR_FORWARD)
    {
        if(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjX > 0)
        {
            LstrObjMap->ObjMapCoor[LeMapId].eObjMoveSts = OBJ_TO_CAR_MOVE_CLOSE;
        }
        else
        {
            LstrObjMap->ObjMapCoor[LeMapId].eObjMoveSts = OBJ_TO_CAR_MOVE_FARAWAY;
        }
    }
    else
    {
        if(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjX > 0)
        {
            LstrObjMap->ObjMapCoor[LeMapId].eObjMoveSts = OBJ_TO_CAR_MOVE_FARAWAY;
        }
        else
        {
            LstrObjMap->ObjMapCoor[LeMapId].eObjMoveSts = OBJ_TO_CAR_MOVE_CLOSE;
        }
    }
    LstrObjMap->ObjMapCoor[LeMapId].eObjFollowSts = OBJ_DET_UPDATE;
    LstrObjMap->ObjMapCoor[LeMapId].u8MapExistFlag = 1;
    LstrObjMap->ObjMapCoor[LeMapId].u32UpdateSysTime = GdSystemMsTimer;
    LstrObjMap->ObjMapCoor[LeMapId].u32CreatMapTime = GdSystemMsTimer;

    LstrObjMap->ObjMapCoor[LeMapId].enuSideMapSns = SIDE_SNS_NONE;
    LstrObjMap->ObjMapCoor[LeMapId].eMapCreatAdasSts = GstrParkingGuidenceData.eCurrentAdasSts;

    /* 对于近距离产生的Map快速置高的处理 */
    if((LenuMapDetailArea > MAP_AREA2)&&(LenuMapDetailArea < MAP_AREA6))
    {
        if(LstrObjMap->ObjMapCoor[LeMapId].eObjBumperArea == OBJ_FRONT_BUMP)
        {
            if(LenuMapDetailArea == MAP_AREA4)
            {
                if(LstrPointToBumperDis < FRONR_AREA4_FAST_HIGH_DIS)
                {
                    LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb = OBJ_PROB_LV5;    /* 近距离产生的Map，存在置信度快速置高 */
                    LstrObjMap->ObjMapCoor[LeMapId].u8HeightLockFlag = 1;
                }
            }
            else
            {
                if(LstrPointToBumperDis < FRONR_MAP_FAST_HIGH_DIS)
                {
                    LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb = OBJ_PROB_LV4;    /* 近距离产生的Map，存在置信度快速置高 */
                    LstrObjMap->ObjMapCoor[LeMapId].u8HeightLockFlag = 1;
                }
            }
        }
        else
        {
            if(LstrPointToBumperDis < REAR_MAP_FAST_HIGH_DIS)
            {
                LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb = OBJ_PROB_LV4;    /* 近距离产生的Map，存在置信度快速置高 */
                LstrObjMap->ObjMapCoor[LeMapId].u8HeightLockFlag = 1;
            }
        }
    }
    else if(LenuMapDetailArea == MAP_AREA6)
    {
        if((LstrObjMap->ObjMapCoor[LeMapId].eObjBumperArea == OBJ_FRONT_BUMP)&&(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY > GstrRamSnsCoor[0][5].cRadarY))
        {
            if(LstrPointToBumperDis < FRONR_MAP_FAST_HIGH_DIS)
            {
                LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb = OBJ_PROB_LV4;    /* 近距离产生的Map，存在置信度快速置高 */
                LstrObjMap->ObjMapCoor[LeMapId].u8HeightLockFlag = 1;
            }
        }
        else
        {
            if(LstrPointToBumperDis < REAR_MAP_FAST_HIGH_DIS)
            {
                LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb = OBJ_PROB_LV4;    /* 近距离产生的Map，存在置信度快速置高 */
                LstrObjMap->ObjMapCoor[LeMapId].u8HeightLockFlag = 1;
            }
        }
    }
    else if(LenuMapDetailArea == MAP_AREA2)
    {
        if((LstrObjMap->ObjMapCoor[LeMapId].eObjBumperArea == OBJ_FRONT_BUMP)&&(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY < GstrRamSnsCoor[0][0].cRadarY))
        {
            if(LstrPointToBumperDis < FRONR_MAP_FAST_HIGH_DIS)
            {
                LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb = OBJ_PROB_LV4;    /* 近距离产生的Map，存在置信度快速置高 */
                LstrObjMap->ObjMapCoor[LeMapId].u8HeightLockFlag = 1;
            }
        }
        else
        {
            if(LstrPointToBumperDis < REAR_MAP_FAST_HIGH_DIS)
            {
                LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb = OBJ_PROB_LV4;    /* 近距离产生的Map，存在置信度快速置高 */
                LstrObjMap->ObjMapCoor[LeMapId].u8HeightLockFlag = 1;
            }
        }
    }

    /* 同频环境下，矫正初次存在置信度直接置高 */
    if(LfP1_Coor_Y_Abs < (GstrRamSnsCoor[Lu8Group][0].cRadarY+100))
    {
        if(GstrParkingGuidenceData.u8SameFreqNoiseFlag[Lu8Group])
        {
            LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb = OBJ_PROB_LV1;    /* 近距离产生的Map，存在置信度快速置高 */
        }
    }
    else
    {
        if(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY > 0)
        {
            if(GstrParkingGuidenceData.u8LeftSideFreqNoiseFlag[Lu8Group])
            {
                LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb = OBJ_PROB_LV1;    /* 近距离产生的Map，存在置信度快速置高 */
            }
        }
        else
        {
            if(GstrParkingGuidenceData.u8RightSideFreqNoiseFlag[Lu8Group])
            {
                LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb = OBJ_PROB_LV1;    /* 近距离产生的Map，存在置信度快速置高 */
            }
        }
    }


    /* 添加对于障碍物类型的计数映射 */
    LstrObjMap->ObjMapCoor[LeMapId].ObjTypeCntBuf[0] = LpstrSnsAreaMap->ObjTypeCnt;
    LstrObjMap->ObjMapCoor[LeMapId].ObjTypeTotalCnt = LpstrSnsAreaMap->ObjTypeCnt;
    /* 创建Map时即根据原始点云的各个类型计数，直接映射到Map端，并初步判断Map的信号组高低属性 */
    /* 根据MAX 和非MAX车型分开处理 */
    if((GenuMapVheiCfgLevel == MAP_VEH_MAX)&&(GstrParkingGuidenceData.eParking_Sts != PARKING_SUMMON))
    {
        if(LstrObjMap->ObjMapCoor[LeMapId].u8HeightLockFlag)
        {
            LstrObjMap->ObjMapCoor[LeMapId].eMapSigGroupHeight = OBJ_HIGH;
            LstrObjMap->ObjMapCoor[LeMapId].eMapObjHeight = OBJ_HIGH;
            LstrObjMap->ObjMapCoor[LeMapId].eMapHeightTrendHeight = OBJ_HIGH;
            LstrObjMap->ObjMapCoor[LeMapId].eMapDE_NoMatchHeight = OBJ_HIGH;
        }
        else
        {
            /* MAX 车型首次创建Map时设置为低，根据后续信号组高低以及回波变化趋势设置不同的高低值 */
            LstrObjMap->ObjMapCoor[LeMapId].eMapObjHeight = OBJ_LOW;
            LstrObjMap->ObjMapCoor[LeMapId].eMapSigGroupHeight = OBJ_LOW;  
            LstrObjMap->ObjMapCoor[LeMapId].eMapHeightTrendHeight = OBJ_LOW;
            LstrObjMap->ObjMapCoor[LeMapId].eMapDE_NoMatchHeight = OBJ_HIGH;
        }
    }
    else
    {
        /* 非MAX车型不确定的障碍物优先设置为高，创建Map时即设置为高 */
        LstrObjMap->ObjMapCoor[LeMapId].eMapObjHeight = OBJ_HIGH;
        LstrObjMap->ObjMapCoor[LeMapId].eMapDE_NoMatchHeight = OBJ_HIGH;
    }


#if 0
    VS_PRINT("Creat New Map Point,Map_Num:%d,Map_Index:%d,Time:%.3f,P1_X:%.3f,P1_Y:%.3f,BumperDis:%.3f,BumperArea:%d,DetailArea:%d,Height:%d\r\n",LstrObjMap->u8MapNum,LeMapId,GfMessageTime,\
        LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjX,LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY,\
        LstrPointToBumperDis,LstrObjMap->ObjMapCoor[LeMapId].eObjBumperArea,LenuMapDetailArea,LstrObjMap->ObjMapCoor[LeMapId].eMapObjHeight);
#endif
    return LeMapId;
}


/******************************************************************************
 * 函数名称: NewBuildSideMapPoint
 * 
 * 功能描述: 新建侧边的Map点
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-01-24 16:17   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static eMapObjIdType NewBuildSideMapPoint(SideAreaMapType *LpstrSideAreaMap,SideSnsIndexType LeSideSnsInx)
{      
    ObjMapInfoType *LstrObjMap;
    eMapObjIdType LeMapId;
    uint8 i;
    float LstrPointToBumperDis;
    ObjMAPAreaType LenuMapDetailArea = MAP_AREA_NONE;
    float LfP1_Coor_Y_Abs;
    uint8 Lu8Group = 0;
    ObjCoorType LStrNearMapMax_X_Coor;
    ObjCoorType LStrNearMapMin_X_Coor;
    float LfAreaPointY_CoorAbs;
    float LfFrontSideAreaPointToSnsDisSub;

    LstrObjMap = &GstrObjMap;

    if(LeSideSnsInx < SIDE_SNS_RLS)
    {
        if(LpstrSideAreaMap->P2_CarCoor.fObjX < GstrRamSnsCoor[0][0].cRadarX)
        {
            LfFrontSideAreaPointToSnsDisSub = GstrRamSnsCoor[0][0].cRadarX - LpstrSideAreaMap->P2_CarCoor.fObjX;
        }
        else
        {
            LfFrontSideAreaPointToSnsDisSub = 0;
        }
    }
    else
    {
        LfFrontSideAreaPointToSnsDisSub = 0;
    }

#if 0
    if((LeSideSnsInx == SIDE_SNS_RRS)&&(GfMessageTime > 42)&&(GfMessageTime < 47))
    {
        VS_PRINT("RRS New Map,Time:%.3f,NearstMapId:%d,NearstMapY_Coor:%.2f,LastMap_P1_X:%.2f,LastMap_P2_X:%.2f,AreaMap_P1_X:%.2f,%.2f,AreaMap_P2_X:%.2f,%.2f,SnsDisSub:%.2f\r\n",GfMessageTime,LpstrSideAreaMap->u8NearstMapId,\
            LpstrSideAreaMap->fNearestMapToSnsDis,LstrObjMap->ObjMapCoor[LpstrSideAreaMap->u8NearstMapId].P1_CarCoor.fObjX,\
            LstrObjMap->ObjMapCoor[LpstrSideAreaMap->u8NearstMapId].P2_CarCoor.fObjX,\
            LpstrSideAreaMap->P1_CarCoor.fObjX,LpstrSideAreaMap->P1_CarCoor.fObjY,\
            LpstrSideAreaMap->P2_CarCoor.fObjX,LpstrSideAreaMap->P2_CarCoor.fObjY,\
            LfFrontSideAreaPointToSnsDisSub);
    }
#endif

    
#if 1
    uint8 Lu8AreaMap_P1_InExistMapFlag = 0;
    uint8 Lu8AreaMap_P2_InExistMapFlag = 0;
    float LfAreaToExistP1_X_Sub,LfAreaToExistP2_X_Sub;
    float LfNearMapY_CoorAbs;
    if(LpstrSideAreaMap->u8NearstMapId != MAP_NONE)
    {
        if((LstrObjMap->ObjMapCoor[LpstrSideAreaMap->u8NearstMapId].u8MapExistFlag)&&\
            (LstrObjMap->ObjMapCoor[LpstrSideAreaMap->u8NearstMapId].fP1_P2_Dis > 30))
        {
            if(LstrObjMap->ObjMapCoor[LpstrSideAreaMap->u8NearstMapId].P1_CarCoor.fObjX > LstrObjMap->ObjMapCoor[LpstrSideAreaMap->u8NearstMapId].P2_CarCoor.fObjX)
            {
                LStrNearMapMax_X_Coor = LstrObjMap->ObjMapCoor[LpstrSideAreaMap->u8NearstMapId].P1_CarCoor;
                LStrNearMapMin_X_Coor = LstrObjMap->ObjMapCoor[LpstrSideAreaMap->u8NearstMapId].P2_CarCoor;
            }
            else
            {
                LStrNearMapMax_X_Coor = LstrObjMap->ObjMapCoor[LpstrSideAreaMap->u8NearstMapId].P2_CarCoor;
                LStrNearMapMin_X_Coor = LstrObjMap->ObjMapCoor[LpstrSideAreaMap->u8NearstMapId].P1_CarCoor;
            }
            if(LeSideSnsInx < SIDE_SNS_RLS)
            {
                if((LpstrSideAreaMap->P2_CarCoor.fObjX < (LStrNearMapMax_X_Coor.fObjX+30))&&\
                    (LpstrSideAreaMap->P2_CarCoor.fObjX > (LStrNearMapMin_X_Coor.fObjX-30)))
                {
                    LfAreaPointY_CoorAbs = ABS_VALUE(LpstrSideAreaMap->P2_CarCoor.fObjY);
                    if(LfAreaPointY_CoorAbs > (LpstrSideAreaMap->fNearestMapToSnsDis-200))
                    {
                        return MAP_NONE;
                    }
                }
            }
            else
            {
                /* 后侧雷达画Map，判断是否重复，看区域Map中，两个点，有任意一个在已知Map的膨胀区域中，就不再画Map */
                if((LpstrSideAreaMap->P2_CarCoor.fObjX > (LStrNearMapMin_X_Coor.fObjX-SIDE_MAP_REAR_SNS_EXTEND_DIS))&&\
                    (LpstrSideAreaMap->P2_CarCoor.fObjX < (LStrNearMapMax_X_Coor.fObjX+SIDE_MAP_REAR_SNS_EXTEND_DIS)))
                {
                    LfAreaToExistP1_X_Sub = ABS(LStrNearMapMin_X_Coor.fObjX,LpstrSideAreaMap->P2_CarCoor.fObjX);
                    LfAreaToExistP2_X_Sub = ABS(LStrNearMapMax_X_Coor.fObjX,LpstrSideAreaMap->P2_CarCoor.fObjX);
                    if(LfAreaToExistP1_X_Sub < LfAreaToExistP2_X_Sub)
                    {
                        LfAreaPointY_CoorAbs = ABS_VALUE(LpstrSideAreaMap->P2_CarCoor.fObjY);
                        LfNearMapY_CoorAbs = ABS_VALUE(LStrNearMapMin_X_Coor.fObjY);
                    }
                    else
                    {
                        LfAreaPointY_CoorAbs = ABS_VALUE(LpstrSideAreaMap->P2_CarCoor.fObjY);
                        LfNearMapY_CoorAbs = ABS_VALUE(LStrNearMapMax_X_Coor.fObjY);
                    }
                    
                    if(LfAreaPointY_CoorAbs > (LfNearMapY_CoorAbs-200))
                    {
                        return MAP_NONE;
                    }
                }
                if((LpstrSideAreaMap->P1_CarCoor.fObjX > (LStrNearMapMin_X_Coor.fObjX-SIDE_MAP_REAR_SNS_EXTEND_DIS))&&\
                    (LpstrSideAreaMap->P1_CarCoor.fObjX < (LStrNearMapMax_X_Coor.fObjX+SIDE_MAP_REAR_SNS_EXTEND_DIS)))
                {
                    LfAreaToExistP1_X_Sub = ABS(LStrNearMapMin_X_Coor.fObjX,LpstrSideAreaMap->P1_CarCoor.fObjX);
                    LfAreaToExistP2_X_Sub = ABS(LStrNearMapMax_X_Coor.fObjX,LpstrSideAreaMap->P1_CarCoor.fObjX);
                    if(LfAreaToExistP1_X_Sub < LfAreaToExistP2_X_Sub)
                    {
                        LfAreaPointY_CoorAbs = ABS_VALUE(LpstrSideAreaMap->P1_CarCoor.fObjY);
                        LfNearMapY_CoorAbs = ABS_VALUE(LStrNearMapMin_X_Coor.fObjY);
                    }
                    else
                    {
                        LfAreaPointY_CoorAbs = ABS_VALUE(LpstrSideAreaMap->P1_CarCoor.fObjY);
                        LfNearMapY_CoorAbs = ABS_VALUE(LStrNearMapMax_X_Coor.fObjY);
                    }
                    
                    if(LfAreaPointY_CoorAbs > (LfNearMapY_CoorAbs-200))
                    {
                        return MAP_NONE;
                    }

                }
            }
        }
    }
#endif
    
    if(LeSideSnsInx < SIDE_SNS_RLS)
    {
        LenuMapDetailArea = CalPointToBumperRelationAndDis(&LpstrSideAreaMap->P2_CarCoor,&LstrPointToBumperDis);
    }
    else
    {
        LenuMapDetailArea = CalPointToBumperRelationAndDis(&LpstrSideAreaMap->P1_CarCoor,&LstrPointToBumperDis);
    }

#if 0
    if((LeSideSnsInx == SIDE_SNS_RRS)&&(GfMessageTime > 42)&&(GfMessageTime < 47))
    {
        VS_PRINT("RRS New Map,Time:%.3f,BumperDis:%.2f,RearSideStartMapDis:%.2f\r\n",GfMessageTime,LstrPointToBumperDis,\
            GstrParkingGuidenceData.fRearSideStartMapDis);
    }
#endif


    
    /* 创建Map前先计算Map到保杠的距离，小于设定距离才开始新建Map */
    if(LeSideSnsInx < SIDE_SNS_RLS)
    {
        if(LstrPointToBumperDis > GstrParkingGuidenceData.fFrontSideStartMapDis)
        {
            return MAP_NONE;
        }
    }
    else
    {
        if(LstrPointToBumperDis > GstrParkingGuidenceData.fRearSideStartMapDis)
        {
            return MAP_NONE;
        }
    }

#if 0
    if(LstrPointToBumperDis > MAP_SIDE_NEW_MAP_DIS)
    {
#if 0
        VS_PRINT("Creat Side New Dis Far,Time:%.3f,BumperDis:%.3f,DetailArea:%d\r\n",GfMessageTime,\
            LstrPointToBumperDis,LenuMapDetailArea);
#endif
        return MAP_NONE;
    }
#endif

    LeMapId = FindEmptyMapId();

    LstrObjMap->ObjMapCoor[LeMapId].eMapObjId = LeMapId;
    LstrObjMap->ObjMapCoor[LeMapId].eMapObjType = OBJ_TYPE_POINT;
    if(GstrParkingGuidenceData.eCurrentAdasSts != APA_GUIDANCE)
    {
        LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb = OBJ_PROB_LV3;    /* 找车位阶段，3个点才开始聚类，首次设置置信度为等级3 */
    }
    else
    {
        LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb = OBJ_PROB_LV1;    /* 首次生成Map，置信度最低 */
    }
    LstrObjMap->ObjMapCoor[LeMapId].eObjHeightProb = OBJ_PROB_LV1;

    if(LeSideSnsInx < SIDE_SNS_RLS)
    {
        LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor = LpstrSideAreaMap->P2_CarCoor;
        LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor = LpstrSideAreaMap->P2_CarCoor;
        LstrObjMap->ObjMapCoor[LeMapId].P1_OdoCoor = LpstrSideAreaMap->P2_OdoCoor;
        LstrObjMap->ObjMapCoor[LeMapId].P2_OdoCoor = LpstrSideAreaMap->P2_OdoCoor;
    }
    else
    {
        LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor = LpstrSideAreaMap->P1_CarCoor;
        LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor = LpstrSideAreaMap->P1_CarCoor;
        LstrObjMap->ObjMapCoor[LeMapId].P1_OdoCoor = LpstrSideAreaMap->P1_OdoCoor;
        LstrObjMap->ObjMapCoor[LeMapId].P2_OdoCoor = LpstrSideAreaMap->P1_OdoCoor;
    }
    
    LstrObjMap->ObjMapCoor[LeMapId].fMapToBumper_Dis = LstrPointToBumperDis;
    LstrObjMap->ObjMapCoor[LeMapId].fP1_ToBumper_Dis = LstrPointToBumperDis;
    LstrObjMap->ObjMapCoor[LeMapId].fP2_ToBumper_Dis = LstrPointToBumperDis;
    LstrObjMap->ObjMapCoor[LeMapId].eP1_DetailArea = LenuMapDetailArea;
    LstrObjMap->ObjMapCoor[LeMapId].eP2_DetailArea = LenuMapDetailArea;
    
    LfP1_Coor_Y_Abs = ABS_VALUE(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY);
    if(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjX < GstrRamSnsCoor[1][0].cRadarX)
    {
        if(LfP1_Coor_Y_Abs < (GstrRamSnsCoor[1][0].cRadarY-50))
        {
            LstrObjMap->ObjMapCoor[LeMapId].u8CreatMapInRearBumperInnerFlag = 1;
        }
    }

    
    if(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjX > 0)
    {
        LstrObjMap->ObjMapCoor[LeMapId].eObjBumperArea = OBJ_FRONT_BUMP;
        Lu8Group = 0;
    }
    else
    {
        LstrObjMap->ObjMapCoor[LeMapId].eObjBumperArea = OBJ_REAR_BUMP;
        Lu8Group = 1;
    }

    if(GstrSnsCarMovSts[0][0].eCarDir == SNS_CAR_STOP)
    {
        LstrObjMap->ObjMapCoor[LeMapId].eObjMoveSts = OBJ_TO_CAR_STOP_STOP;
    }
    else if(GstrSnsCarMovSts[0][0].eCarDir == SNS_CAR_FORWARD)
    {
        if(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjX > 0)
        {
            LstrObjMap->ObjMapCoor[LeMapId].eObjMoveSts = OBJ_TO_CAR_MOVE_CLOSE;
        }
        else
        {
            LstrObjMap->ObjMapCoor[LeMapId].eObjMoveSts = OBJ_TO_CAR_MOVE_FARAWAY;
        }
    }
    else
    {
        if(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjX > 0)
        {
            LstrObjMap->ObjMapCoor[LeMapId].eObjMoveSts = OBJ_TO_CAR_MOVE_FARAWAY;
        }
        else
        {
            LstrObjMap->ObjMapCoor[LeMapId].eObjMoveSts = OBJ_TO_CAR_MOVE_CLOSE;
        }
    }
    LstrObjMap->ObjMapCoor[LeMapId].eObjFollowSts = OBJ_DET_UPDATE;
    LstrObjMap->ObjMapCoor[LeMapId].u8MapExistFlag = 1;
    LstrObjMap->ObjMapCoor[LeMapId].u32UpdateSysTime = LpstrSideAreaMap->u32SideUpdateTime;
    LstrObjMap->ObjMapCoor[LeMapId].u32CreatMapTime = LpstrSideAreaMap->u32SideUpdateTime;

    LstrObjMap->ObjMapCoor[LeMapId].enuSideMapSns = SIDE_SNS_NONE;
    LstrObjMap->ObjMapCoor[LeMapId].eMapCreatAdasSts = GstrParkingGuidenceData.eCurrentAdasSts;

    /* 对于近距离产生的Map快速置高的处理 */
    if((LenuMapDetailArea > MAP_AREA2)&&(LenuMapDetailArea < MAP_AREA6))
    {
        if(LstrObjMap->ObjMapCoor[LeMapId].eObjBumperArea == OBJ_FRONT_BUMP)
        {
            if(LenuMapDetailArea == MAP_AREA4)
            {
                if(LstrPointToBumperDis < FRONR_AREA4_FAST_HIGH_DIS)
                {
                    LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb = OBJ_PROB_LV5;    /* 近距离产生的Map，存在置信度快速置高 */
                    LstrObjMap->ObjMapCoor[LeMapId].u8HeightLockFlag = 1;
                }
            }
            else
            {
                if(LstrPointToBumperDis < FRONR_MAP_FAST_HIGH_DIS)
                {
                    LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb = OBJ_PROB_LV4;    /* 近距离产生的Map，存在置信度快速置高 */
                    LstrObjMap->ObjMapCoor[LeMapId].u8HeightLockFlag = 1;
                }
            }
        }
        else
        {
            if(LstrPointToBumperDis < REAR_MAP_FAST_HIGH_DIS)
            {
                LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb = OBJ_PROB_LV4;    /* 近距离产生的Map，存在置信度快速置高 */
                LstrObjMap->ObjMapCoor[LeMapId].u8HeightLockFlag = 1;
            }
        }
    }
    else if(LenuMapDetailArea == MAP_AREA6)
    {
        if((LstrObjMap->ObjMapCoor[LeMapId].eObjBumperArea == OBJ_FRONT_BUMP)&&(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY > GstrRamSnsCoor[0][5].cRadarY))
        {
            if(LstrPointToBumperDis < FRONR_MAP_FAST_HIGH_DIS)
            {
                LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb = OBJ_PROB_LV4;    /* 近距离产生的Map，存在置信度快速置高 */
                LstrObjMap->ObjMapCoor[LeMapId].u8HeightLockFlag = 1;
            }
        }
        else
        {
            if(LstrPointToBumperDis < REAR_MAP_FAST_HIGH_DIS)
            {
                LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb = OBJ_PROB_LV4;    /* 近距离产生的Map，存在置信度快速置高 */
                LstrObjMap->ObjMapCoor[LeMapId].u8HeightLockFlag = 1;
            }
        }
    }
    else if(LenuMapDetailArea == MAP_AREA2)
    {
        if((LstrObjMap->ObjMapCoor[LeMapId].eObjBumperArea == OBJ_FRONT_BUMP)&&(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY < GstrRamSnsCoor[0][0].cRadarY))
        {
            if(LstrPointToBumperDis < FRONR_MAP_FAST_HIGH_DIS)
            {
                LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb = OBJ_PROB_LV4;    /* 近距离产生的Map，存在置信度快速置高 */
                LstrObjMap->ObjMapCoor[LeMapId].u8HeightLockFlag = 1;
            }
        }
        else
        {
            if(LstrPointToBumperDis < REAR_MAP_FAST_HIGH_DIS)
            {
                LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb = OBJ_PROB_LV4;    /* 近距离产生的Map，存在置信度快速置高 */
                LstrObjMap->ObjMapCoor[LeMapId].u8HeightLockFlag = 1;
            }
        }
    }

    
    /* 同频环境下，矫正初次存在置信度直接置高 */
    /* 同频环境下，矫正初次存在置信度直接置高 */
    if(LfP1_Coor_Y_Abs < (GstrRamSnsCoor[Lu8Group][0].cRadarY+100))
    {
        if(GstrParkingGuidenceData.u8SameFreqNoiseFlag[Lu8Group])
        {
            LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb = OBJ_PROB_LV1;    /* 近距离产生的Map，存在置信度快速置高 */
        }
    }
    else
    {
        if(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY > 0)
        {
            if(GstrParkingGuidenceData.u8LeftSideFreqNoiseFlag[Lu8Group])
            {
                LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb = OBJ_PROB_LV1;    /* 近距离产生的Map，存在置信度快速置高 */
            }
        }
        else
        {
            if(GstrParkingGuidenceData.u8RightSideFreqNoiseFlag[Lu8Group])
            {
                LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb = OBJ_PROB_LV1;    /* 近距离产生的Map，存在置信度快速置高 */
            }
        }
    }


    /* 侧边近距离产生的Map，默认为高 */
    if(LfP1_Coor_Y_Abs < (GstrRamSnsCoor[1][0].cRadarY+600))
    {
        LstrObjMap->ObjMapCoor[LeMapId].u8HeightLockFlag = 1;
    }

    
    /* 添加对于障碍物类型的计数映射 */
    LstrObjMap->ObjMapCoor[LeMapId].ObjTypeCntBuf[0] = LpstrSideAreaMap->ObjTypeCnt;
    LstrObjMap->ObjMapCoor[LeMapId].ObjTypeTotalCnt = LpstrSideAreaMap->ObjTypeCnt;
    /* 创建Map时即根据原始点云的各个类型计数，直接映射到Map端，并初步判断Map的信号组高低属性 */
    /* 根据MAX 和非MAX车型分开处理 */
    if((GenuMapVheiCfgLevel == MAP_VEH_MAX)&&(GstrParkingGuidenceData.eParking_Sts != PARKING_SUMMON))
    {
        if(LstrObjMap->ObjMapCoor[LeMapId].u8HeightLockFlag)
        {
            LstrObjMap->ObjMapCoor[LeMapId].eMapSigGroupHeight = OBJ_HIGH;
            LstrObjMap->ObjMapCoor[LeMapId].eMapObjHeight = OBJ_HIGH;
            LstrObjMap->ObjMapCoor[LeMapId].eMapHeightTrendHeight = OBJ_HIGH;
        }
        else
        {
            /* MAX 车型首次创建Map时设置为低，根据后续信号组高低以及回波变化趋势设置不同的高低值 */
            LstrObjMap->ObjMapCoor[LeMapId].eMapObjHeight = OBJ_LOW;
            LstrObjMap->ObjMapCoor[LeMapId].eMapSigGroupHeight = OBJ_LOW;
            LstrObjMap->ObjMapCoor[LeMapId].eMapHeightTrendHeight = OBJ_LOW;
            LstrObjMap->ObjMapCoor[LeMapId].eMapDE_NoMatchHeight = OBJ_HIGH;
        }
    }
    else
    {
        /* 非MAX车型不确定的障碍物优先设置为高，创建Map时即设置为高 */
        LstrObjMap->ObjMapCoor[LeMapId].eMapObjHeight = OBJ_HIGH;
        LstrObjMap->ObjMapCoor[LeMapId].eMapDE_NoMatchHeight = OBJ_HIGH;
    }


    /* Guidance阶段，近距离产生的Map，直接置高，并且立即提升置信度 */
    if(GstrParkingGuidenceData.eCurrentAdasSts == APA_GUIDANCE)
    {
        if(LstrPointToBumperDis < 600)
        {
            LstrObjMap->ObjMapCoor[LeMapId].u8SideMapHeightLockFlag = 1;
            LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb = OBJ_PROB_LV5;
            LstrObjMap->ObjMapCoor[LeMapId].eMapObjHeight = OBJ_HIGH;
        }
    }


    
    LstrObjMap->ObjMapCoor[LeMapId].u16SideMapUpdateMasterDis = LpstrSideAreaMap->u16ValidMasterDis;
    LpstrSideAreaMap->u8MasterDisNoMatchCnt = 0;
    
    uint8 Lu8CreatMapLockHighFlag;
    AnalysisSideAreaBufDataAndPrintf(&GstrSideAreaPointBuf[LeSideSnsInx],LeMapId);
    CreatNewMapSideAreaPointBufInit(&GstrSideAreaPointBuf[LeSideSnsInx]);
    Lu8CreatMapLockHighFlag = AnalysisSideAreaBufDataWhenMapUpdate(&GstrSideAreaPointBuf[LeSideSnsInx],LeMapId,LpstrSideAreaMap->u16ValidMasterDis);

    LstrObjMap->ObjMapCoor[LeMapId].SideMapCarMovDir = LpstrSideAreaMap->eSnsMovDir;

    if(Lu8CreatMapLockHighFlag)
    {
        LstrObjMap->ObjMapCoor[LeMapId].u8SideMapHeightLockFlag = 1;
        LstrObjMap->ObjMapCoor[LeMapId].eMapObjHeight = OBJ_HIGH;
    }
#if 0
    if((GfMessageTime > 40)&&(GfMessageTime < 44))//&&(LeSideSnsInx == SIDE_SNS_RRS)
    //if(1)
    {
        VS_PRINT("Creat Side New Map Point,Map_Num:%d,Map_Index:%d,Time:%.3f,P1_X:%.3f,P1_Y:%.3f,BumperDis:%.3f,BumperArea:%d,DetailArea:%d,Height:%d,CreatMapTime:%ld,SideMapHeightLockFlag:%d,UpdateMasterDis:%d,SideSnsInx:%d\r\n\r\n",LstrObjMap->u8MapNum,LeMapId,GfMessageTime,\
            LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjX,LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY,\
            LstrPointToBumperDis,LstrObjMap->ObjMapCoor[LeMapId].eObjBumperArea,LenuMapDetailArea,LstrObjMap->ObjMapCoor[LeMapId].eMapObjHeight,\
            LstrObjMap->ObjMapCoor[LeMapId].u32CreatMapTime,LstrObjMap->ObjMapCoor[LeMapId].u8SideMapHeightLockFlag,\
            LstrObjMap->ObjMapCoor[LeMapId].u16SideMapUpdateMasterDis,LeSideSnsInx);
    }

#endif
    LstrObjMap->ObjMapCoor[LeMapId].u8Map_SFR_AreaFlag = 1;/* 侧边Map永久标志 */
    return LeMapId;
}


/******************************************************************************
 * 函数名称: UpdateMapTypeBuf
 * 
 * 功能描述: 更新Map缓存的障碍物类型缓存
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-23 10:18   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void UpdateMapTypeToBuf(OriginalObjType *LpObjTypeBuf,OriginalObjType LenuObjType)
{
    uint8 i;

    for(i= (MAP_TYPE_BUF_CNT -1); i > 0; i--)
    {
        LpObjTypeBuf[i] = LpObjTypeBuf[i-1];
    }
    LpObjTypeBuf[0] = LenuObjType;
}


/******************************************************************************
 * 函数名称: UpdateMapTypeCntToBuf
 * 
 * 功能描述: 更新障碍物类型计数至缓存
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-01-26 09:48   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void UpdateMapTypeCntToBuf(ObjTypeCntType *LpObjTypeCntBuf,ObjTypeCntType LstrObjTypeCnt)
{
#if 0
    uint8 i;

    for(i= (MAP_TYPE_CNT_BUF_CNT -1); i > 0; i--)
    {
        LpObjTypeCntBuf[i] = LpObjTypeCntBuf[i-1];
    }
#endif
    LpObjTypeCntBuf[0] = LstrObjTypeCnt;
}


/******************************************************************************
 * 函数名称: JudgeMapTypeByTypeCnt
 * 
 * 功能描述: 通过对最新10个周期的Map点的回波类型计数进行统计，梳理出该Map的类型
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-01-26 10:06   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void JudgeMapTypeByTypeCnt(ObjMapCoorType *LpstrMapCoor)
{
#if 1
    uint16 Lu16TotalCnt;

    Lu16TotalCnt = LpstrMapCoor->ObjTypeTotalCnt.u8StdTotalPointCnt + LpstrMapCoor->ObjTypeTotalCnt.u8ChirpTotalPointCnt;

    if(Lu16TotalCnt < 200)
    {
        LpstrMapCoor->ObjTypeTotalCnt.u8StdPVC_PointCnt += LpstrMapCoor->ObjTypeCntBuf[0].u8StdPVC_PointCnt;
        LpstrMapCoor->ObjTypeTotalCnt.u8ChirpPVC_PointCnt += LpstrMapCoor->ObjTypeCntBuf[0].u8ChirpPVC_PointCnt;
        LpstrMapCoor->ObjTypeTotalCnt.u8StdLowCurb_PointCnt += LpstrMapCoor->ObjTypeCntBuf[0].u8StdLowCurb_PointCnt;
        LpstrMapCoor->ObjTypeTotalCnt.u8ChirpLowCurb_PointCnt += LpstrMapCoor->ObjTypeCntBuf[0].u8ChirpLowCurb_PointCnt;
        LpstrMapCoor->ObjTypeTotalCnt.u8StdBigWall_PointCnt += LpstrMapCoor->ObjTypeCntBuf[0].u8StdBigWall_PointCnt;
        LpstrMapCoor->ObjTypeTotalCnt.u8ChirpBigWall_PointCnt += LpstrMapCoor->ObjTypeCntBuf[0].u8ChirpBigWall_PointCnt;
        LpstrMapCoor->ObjTypeTotalCnt.u8StdHigh_HighCurb_PointCnt += LpstrMapCoor->ObjTypeCntBuf[0].u8StdHigh_HighCurb_PointCnt;
        LpstrMapCoor->ObjTypeTotalCnt.u8ChirpHigh_HighCurb_PointCnt += LpstrMapCoor->ObjTypeCntBuf[0].u8ChirpHigh_HighCurb_PointCnt;
        LpstrMapCoor->ObjTypeTotalCnt.u8StdTotalPointCnt += LpstrMapCoor->ObjTypeCntBuf[0].u8StdTotalPointCnt;
        LpstrMapCoor->ObjTypeTotalCnt.u8ChirpTotalPointCnt += LpstrMapCoor->ObjTypeCntBuf[0].u8ChirpTotalPointCnt;
    }

#else
    uint8 i;
    ObjTypeCntType LstrTotalObjTypeCnt;
    
    LstrTotalObjTypeCnt = GstrObjTypeCntInit;
    
    for(i= 0; i < MAP_TYPE_CNT_BUF_CNT; i++)
    {
        LstrTotalObjTypeCnt.u8StdPVC_PointCnt += LpstrMapCoor->ObjTypeCntBuf[i].u8StdPVC_PointCnt;
        LstrTotalObjTypeCnt.u8ChirpPVC_PointCnt += LpstrMapCoor->ObjTypeCntBuf[i].u8ChirpPVC_PointCnt;
        LstrTotalObjTypeCnt.u8StdLowCurb_PointCnt += LpstrMapCoor->ObjTypeCntBuf[i].u8StdLowCurb_PointCnt;
        LstrTotalObjTypeCnt.u8ChirpLowCurb_PointCnt += LpstrMapCoor->ObjTypeCntBuf[i].u8ChirpLowCurb_PointCnt;
        LstrTotalObjTypeCnt.u8StdBigWall_PointCnt += LpstrMapCoor->ObjTypeCntBuf[i].u8StdBigWall_PointCnt;
        LstrTotalObjTypeCnt.u8ChirpBigWall_PointCnt += LpstrMapCoor->ObjTypeCntBuf[i].u8ChirpBigWall_PointCnt;
        LstrTotalObjTypeCnt.u8StdHigh_HighCurb_PointCnt += LpstrMapCoor->ObjTypeCntBuf[i].u8StdHigh_HighCurb_PointCnt;
        LstrTotalObjTypeCnt.u8ChirpHigh_HighCurb_PointCnt += LpstrMapCoor->ObjTypeCntBuf[i].u8ChirpHigh_HighCurb_PointCnt;
        LstrTotalObjTypeCnt.u8StdTotalPointCnt += LpstrMapCoor->ObjTypeCntBuf[i].u8StdTotalPointCnt;
        LstrTotalObjTypeCnt.u8ChirpTotalPointCnt += LpstrMapCoor->ObjTypeCntBuf[i].u8ChirpTotalPointCnt;
    }
    LpstrMapCoor->ObjTypeTotalCnt = LstrTotalObjTypeCnt;    
#endif
}

/******************************************************************************
 * 函数名称: UpdateMapPointCoor
 * 
 * 功能描述: 更新Map点的坐标及相关信息
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-23 10:14   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void UpdateMapPointCoor(ObjMapCoorType *LpstrMapCoor,ObjMAPAreaType LenuObjMAPArea,SnsAreaMapType *LpstrSnsAreaMap)
{   
    uint8 i;
    float LfX_Sub;
    float LfY_Sub;
    ObjCoorType LstrNewOdoCoor;
    ObjCoorType LstrNewCarCoor;
    uint8 Lu8MasterDisInx;

    LpstrMapCoor->P1_OdoCoorBuf[2] =  LpstrMapCoor->P1_OdoCoorBuf[1];
    LpstrMapCoor->P1_OdoCoorBuf[1] =  LpstrMapCoor->P1_OdoCoorBuf[0];
    LpstrMapCoor->P1_OdoCoorBuf[0] = LpstrSnsAreaMap->P1_OdoCoor;
    
    if(LpstrMapCoor->u8P1OdoCoorBufCnt < MAP_COOR_BUF_CNT)
    {
        LpstrMapCoor->u8P1OdoCoorBufCnt++;
    }
    else
    {
        /* 超过3次才更新Map点坐标，通过Odo缓存，转车辆坐标对比后进行更新 */
        if(LpstrMapCoor->eObjFollowSts == OBJ_DET_UPDATE)
        {
            LstrNewOdoCoor.fObjX = 0;
            LstrNewOdoCoor.fObjY = 0;
            for(i = 0; i < MAP_COOR_BUF_CNT; i++)
            {
                LstrNewOdoCoor.fObjX += LpstrMapCoor->P1_OdoCoorBuf[i].fObjX;
                LstrNewOdoCoor.fObjY += LpstrMapCoor->P1_OdoCoorBuf[i].fObjY;
            }
            LstrNewOdoCoor.fObjX = LstrNewOdoCoor.fObjX/3;
            LstrNewOdoCoor.fObjY = LstrNewOdoCoor.fObjY/3;
            
            PubAI_TransObjOdoCoorToCarCoor(&LstrNewOdoCoor.fObjX,&LstrNewOdoCoor.fObjY,&LstrNewCarCoor.fObjX,&LstrNewCarCoor.fObjY);
            LfX_Sub = ABS(LpstrMapCoor->P1_CarCoor.fObjX,LstrNewCarCoor.fObjX);
            LfY_Sub = ABS(LpstrMapCoor->P1_CarCoor.fObjY,LstrNewCarCoor.fObjY);

            /* 中间区域和侧边Map的更新坐标比较需要分开处理，侧边X抖动较大 */
            if((LenuObjMAPArea > MAP_AREA2)&&(LenuObjMAPArea < MAP_AREA6))
            {
                if((LfX_Sub > MAP_POINT_UPDATE_X_LIMIT)||(LfY_Sub > MAP_POINT_UPDATE_Y_LIMIT))
                {
                    LpstrMapCoor->P1_CarCoor = LstrNewCarCoor;
                    LpstrMapCoor->P2_CarCoor = LstrNewCarCoor;
                    LpstrMapCoor->P1_OdoCoor = LstrNewOdoCoor;
                    LpstrMapCoor->P2_OdoCoor = LstrNewOdoCoor;
#if 0
                    if(LpstrMapCoor->eMapObjId == 0x02)
                    {
                        VS_PRINT("Time:%.3f,Map Point Update,Map_X:%.3f,Map_Y:%.3f,Obj_X:%.3f,Obj_Y:%.3f,X_Sub:%.3f,Y_Sub:%.3f\r\n",GfMessageTime,\
                            LpstrMapCoor->P1_CarCoor.fObjX,LpstrMapCoor->P1_CarCoor.fObjY,\
                            LstrNewCarCoor.fObjX,LstrNewCarCoor.fObjY,LfX_Sub,LfY_Sub);
                    }
#endif
                }
            }
            else
            {
                if((LfX_Sub > MAP_SIDE_POINT_UPDATE_X_LIMIT)||(LfY_Sub > MAP_SIDE_POINT_UPDATE_Y_LIMIT))
                {
                    LpstrMapCoor->P1_CarCoor = LstrNewCarCoor;
                    LpstrMapCoor->P2_CarCoor = LstrNewCarCoor;
                    LpstrMapCoor->P1_OdoCoor = LstrNewOdoCoor;
                    LpstrMapCoor->P2_OdoCoor = LstrNewOdoCoor;
                }
            }
        }
    }
    
    if((LpstrMapCoor->eObjFollowSts == OBJ_DET_UPDATE)||(LpstrMapCoor->eObjFollowSts == OBJ_BLIND_FOLLOW))
    {        
        LpstrMapCoor->u32UpdateSysTime = GdSystemMsTimer;
        LpstrMapCoor->eMapCreatAdasSts = GstrParkingGuidenceData.eCurrentAdasSts;
        
        MapExistProbAdd(LpstrMapCoor->eMapObjId);
        UpdateMapTypeCntToBuf(&LpstrMapCoor->ObjTypeCntBuf[0],LpstrSnsAreaMap->ObjTypeCnt);
        LpstrMapCoor->u8ObjTypeUpdateFlag = 1;
    }
}

/******************************************************************************
 * 函数名称: JudgePointToPointCarCoorNear
 * 
 * 功能描述: 判断两个点的车辆坐标临近
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-08-14 19:26   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static uint8 JudgePointToPointCarCoorNear(ObjCoorType *LpstrObjCoor1, ObjCoorType *LpstrObjCoor2)
{
    uint8 Lu8TwoPointNearFlag = 0;
    float LfXSub,LfYSub;

    LfXSub = ABS(LpstrObjCoor1->fObjX,LpstrObjCoor2->fObjX);
    LfYSub = ABS(LpstrObjCoor1->fObjY,LpstrObjCoor2->fObjY);

    if((LfXSub < F_R_NEAR_X_SUB)&&(LfYSub < F_R_NEAR_Y_SUB))
    {
        Lu8TwoPointNearFlag = 1;
    }

    return Lu8TwoPointNearFlag;
}


/******************************************************************************
 * 函数名称: FloatValueDivid10ToSintValue
 * 
 * 功能描述: 转换一个浮点数到有符号整数，并且在除10前进行四舍五入取舍
 * 
 * 输入参数:LfFloatVar--原始浮点数类型
 * 
 * 输出参数:Ls16VarInteger--经过除10后，并四舍五入小数部分后的输出
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-14 16:09   V0.1      AntonyFang   初次发布
 ******************************************************************************/
sint16 FloatValueDivid10ToSintValue(float LfFloatVar)
{
    sint16 Ls16VarInteger;
    sint16 LsVarPoint;
    
    LsVarPoint = (sint16)LfFloatVar;
    Ls16VarInteger = LsVarPoint/10;
    LsVarPoint = LsVarPoint%10;

    if(LsVarPoint >= 5)
    {
        Ls16VarInteger++;
    }

    return Ls16VarInteger; 
}

/******************************************************************************
 * 函数名称: F_R_ObjMapToCAN
 * 
 * 功能描述: 前后雷达Map发送至CAN
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-03-08 10:16   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void ObjMapToCAN(void)
{
    ObjMapInfoType* LstrObjMap;
    eMapObjIdType LeMapId;
    eMapObjIdType LeCAN_MapIndex;
    eMapObjIdType LeOriginal_MapIndex;
    eMapObjIdType LeCAN_MapIndex2;
    uint8 Lu8ValidMapNum;
    uint8 i;
    
    LstrObjMap = &GstrObjMap;
    Lu8ValidMapNum = 0;
    float LfAngleAbs;
    float LfP1_P2_Line_K,LfNewPointY_Coor;
    uint32 Lu32ADAS_SYNC_Tim;
    ReadCAN_AppSignal_ADAS_CAN_SYNC_Time(&Lu32ADAS_SYNC_Tim);

    LeCAN_MapIndex = 0;
    Gu16FrontArea4MapToBumperDis = 0xFFFF;
    for(LeOriginal_MapIndex = MAP_OBJ_0; LeOriginal_MapIndex < MAP_OBJ_NUM; LeOriginal_MapIndex++)
    {
        /* 查找最近Map所对应的Index */            
        LeMapId = LeOriginal_MapIndex;

        if(LstrObjMap->ObjMapCoor[LeMapId].u8MapDisplayFlag)
        {
            GstrObjMapToCAN[LeCAN_MapIndex].eMapObjId = LstrObjMap->ObjMapCoor[LeMapId].eMapObjId;
#if 1
            /** @brief: 根据客户新增需求进行修改 */

            if(LstrObjMap->ObjMapCoor[LeMapId].u8MapInSideAreaDisplayFlag == 1)
            {
                GstrObjMapToCAN[LeCAN_MapIndex].eMapObjType = OBJ_TYPE_LEFT_SIDE;
            }
            else if(LstrObjMap->ObjMapCoor[LeMapId].u8MapInSideAreaDisplayFlag == 2)
            {
                GstrObjMapToCAN[LeCAN_MapIndex].eMapObjType = OBJ_TYPE_RIGHT_SIDE;
            }
            else
            {
                GstrObjMapToCAN[LeCAN_MapIndex].eMapObjType = LstrObjMap->ObjMapCoor[LeMapId].eMapObjType;
            }
#else
            GstrObjMapToCAN[LeCAN_MapIndex].eMapObjType = LstrObjMap->ObjMapCoor[LeMapId].eMapObjType;
#endif

            GstrObjMapToCAN[LeCAN_MapIndex].eObjExistProb = LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb;
            GstrObjMapToCAN[LeCAN_MapIndex].eMapObjHeight = LstrObjMap->ObjMapCoor[LeMapId].eMapObjHeight;
#if 1
            /* 高度置信度用于区分前后Map，本身无实质作用，前后Map 99.9，侧边Map 33.3 */
            if(LstrObjMap->ObjMapCoor[LeMapId].u8Map_SFR_AreaFlag == 1)
            {
                GstrObjMapToCAN[LeCAN_MapIndex].eObjHeightProb = OBJ_PROB_LV2;
#if 0
                if(LstrObjMap->ObjMapCoor[LeMapId].u8SideMapLowTagFlag)
                {
                    GstrObjMapToCAN[LeCAN_MapIndex].eObjHeightProb = OBJ_PROB_LV3;
                }
#endif
            } 
            else if(LstrObjMap->ObjMapCoor[LeMapId].u8Map_SFR_AreaFlag == 2)
            {
                GstrObjMapToCAN[LeCAN_MapIndex].eObjHeightProb = OBJ_PROB_LV6;
            } 
            else
            {
                /* 异常Map */
                GstrObjMapToCAN[LeCAN_MapIndex].eObjHeightProb = OBJ_PROB_LV1;
            }
#else
            GstrObjMapToCAN[LeCAN_MapIndex].eObjHeightProb = LstrObjMap->ObjMapCoor[LeMapId].eObjHeightProb;
#endif
            GstrObjMapToCAN[LeCAN_MapIndex].u32MapObjTimestamp = Lu32ADAS_SYNC_Tim;
            /* 添加对于超过CAN总线最大值的处理 */
            if(LstrObjMap->ObjMapCoor[LeMapId].eMapObjType == OBJ_TYPE_POINT)
            {
                GstrObjMapToCAN[LeCAN_MapIndex].s16ObjMapP1_X = FloatValueDivid10ToSintValue(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjX);
                GstrObjMapToCAN[LeCAN_MapIndex].s16ObjMapP1_Y = FloatValueDivid10ToSintValue(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY);
                GstrObjMapToCAN[LeCAN_MapIndex].s16ObjMapP2_X = GstrObjMapToCAN[LeCAN_MapIndex].s16ObjMapP1_X;
                GstrObjMapToCAN[LeCAN_MapIndex].s16ObjMapP2_Y = GstrObjMapToCAN[LeCAN_MapIndex].s16ObjMapP1_Y;

                /* 提供前保中间区域最小距离给PP使用 */
                if((LstrObjMap->ObjMapCoor[LeMapId].eObjBumperArea == OBJ_FRONT_BUMP))
                {
                    if((LstrObjMap->ObjMapCoor[LeMapId].eP1_DetailArea == MAP_AREA4)&&(LstrObjMap->ObjMapCoor[LeMapId].fMapToBumper_Dis < 2500))
                    {
                        if(LstrObjMap->ObjMapCoor[LeMapId].fMapToBumper_Dis < Gu16FrontArea4MapToBumperDis)
                        {
                            Gu16FrontArea4MapToBumperDis = (uint16)LstrObjMap->ObjMapCoor[LeMapId].fMapToBumper_Dis;
                        }
                    }
                }
            }
            else
            {
                /* 提供前保中间区域最小距离给PP使用 */
                if((LstrObjMap->ObjMapCoor[LeMapId].eObjBumperArea == OBJ_FRONT_BUMP))
                {
                    if((LstrObjMap->ObjMapCoor[LeMapId].eP1_DetailArea == MAP_AREA4)&&(LstrObjMap->ObjMapCoor[LeMapId].eP2_DetailArea == MAP_AREA4)&&\
                        (LstrObjMap->ObjMapCoor[LeMapId].fMapToBumper_Dis < 2500))
                    {
                        if(LstrObjMap->ObjMapCoor[LeMapId].fMapToBumper_Dis < Gu16FrontArea4MapToBumperDis)
                        {
                            Gu16FrontArea4MapToBumperDis = (uint16)LstrObjMap->ObjMapCoor[LeMapId].fMapToBumper_Dis;
                        }
                    }
                }
                if(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjX > MAP_TO_CAN_X_LIMIT_MAX)
                {
                    GstrObjMapToCAN[LeCAN_MapIndex].s16ObjMapP1_X = MAP_TO_CAN_MAX_VALUE;
                    LfAngleAbs = ABS_VALUE(LstrObjMap->ObjMapCoor[LeMapId].fP1_P2ToCarX_Angle);
                    if(LfAngleAbs < F_R_MAP_89_8_ANGLE)
                    {
                        LfP1_P2_Line_K = (LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY - LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjY)\
                            /(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjX - LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjX);
                        LfP1_P2_Line_K = (MAP_TO_CAN_X_LIMIT_MAX - LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjX)*LfP1_P2_Line_K;
                        LfNewPointY_Coor = LfP1_P2_Line_K + LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjY;
                        GstrObjMapToCAN[LeCAN_MapIndex].s16ObjMapP1_Y = FloatValueDivid10ToSintValue(LfNewPointY_Coor);
                    }
                    else
                    {
                        GstrObjMapToCAN[LeCAN_MapIndex].s16ObjMapP1_Y = FloatValueDivid10ToSintValue(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY);
                    } 
                }
                else if(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjX < (-MAP_TO_CAN_X_LIMIT_MAX))
                {
                    GstrObjMapToCAN[LeCAN_MapIndex].s16ObjMapP1_X = -MAP_TO_CAN_MAX_VALUE;
                    LfAngleAbs = ABS_VALUE(LstrObjMap->ObjMapCoor[LeMapId].fP1_P2ToCarX_Angle);
                    if(LfAngleAbs < F_R_MAP_89_8_ANGLE)
                    {
                        LfP1_P2_Line_K = (LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY - LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjY)\
                            /(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjX - LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjX);
                        LfP1_P2_Line_K = ((-MAP_TO_CAN_X_LIMIT_MAX) - LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjX)*LfP1_P2_Line_K;
                        LfNewPointY_Coor = LfP1_P2_Line_K + LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjY;
                        GstrObjMapToCAN[LeCAN_MapIndex].s16ObjMapP1_Y = FloatValueDivid10ToSintValue(LfNewPointY_Coor);
                    }
                    else
                    {
                        GstrObjMapToCAN[LeCAN_MapIndex].s16ObjMapP1_Y = FloatValueDivid10ToSintValue(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY);
                    }  
                }
                else
                {
                    GstrObjMapToCAN[LeCAN_MapIndex].s16ObjMapP1_X = FloatValueDivid10ToSintValue(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjX);
                    GstrObjMapToCAN[LeCAN_MapIndex].s16ObjMapP1_Y = FloatValueDivid10ToSintValue(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY);
                }

                if(LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjX > MAP_TO_CAN_X_LIMIT_MAX)
                {
                    GstrObjMapToCAN[LeCAN_MapIndex].s16ObjMapP2_X = MAP_TO_CAN_MAX_VALUE;
                    LfAngleAbs = ABS_VALUE(LstrObjMap->ObjMapCoor[LeMapId].fP1_P2ToCarX_Angle);
                    if(LfAngleAbs < F_R_MAP_89_8_ANGLE)
                    {
                         LfP1_P2_Line_K = (LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjY - LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY)\
                            /(LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjX - LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjX);
                        LfP1_P2_Line_K = (MAP_TO_CAN_X_LIMIT_MAX - LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjX)*LfP1_P2_Line_K;
                        LfNewPointY_Coor = LfP1_P2_Line_K + LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY;
                        GstrObjMapToCAN[LeCAN_MapIndex].s16ObjMapP2_Y = FloatValueDivid10ToSintValue(LfNewPointY_Coor);
                    }
                    else
                    {
                        GstrObjMapToCAN[LeCAN_MapIndex].s16ObjMapP2_Y = FloatValueDivid10ToSintValue(LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjY);
                    } 
                }
                else if(LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjX < (-MAP_TO_CAN_X_LIMIT_MAX))
                {
                    GstrObjMapToCAN[LeCAN_MapIndex].s16ObjMapP2_X = -MAP_TO_CAN_MAX_VALUE;
                    LfAngleAbs = ABS_VALUE(LstrObjMap->ObjMapCoor[LeMapId].fP1_P2ToCarX_Angle);
                    if(LfAngleAbs < F_R_MAP_89_8_ANGLE)
                    {
                         LfP1_P2_Line_K = (LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjY - LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY)\
                            /(LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjX - LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjX);
                        LfP1_P2_Line_K = ((-MAP_TO_CAN_X_LIMIT_MAX) - LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjX)*LfP1_P2_Line_K;
                        LfNewPointY_Coor = LfP1_P2_Line_K + LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY;
                        GstrObjMapToCAN[LeCAN_MapIndex].s16ObjMapP2_Y = FloatValueDivid10ToSintValue(LfNewPointY_Coor);
                    }
                    else
                    {
                        GstrObjMapToCAN[LeCAN_MapIndex].s16ObjMapP2_Y = FloatValueDivid10ToSintValue(LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjY);
                    }

                }
                else
                {
                    GstrObjMapToCAN[LeCAN_MapIndex].s16ObjMapP2_X = FloatValueDivid10ToSintValue(LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjX);
                    GstrObjMapToCAN[LeCAN_MapIndex].s16ObjMapP2_Y = FloatValueDivid10ToSintValue(LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjY);
                }
            }
            LeCAN_MapIndex++;

#if 0
            VS_PRINT("Map To CAN,Id:%d,P1_X:%.3f,P1_Y:%.3f,P2_X:%.3f,P2_Y:%.3f\r\n",LstrObjMap->ObjMapCoor[LeMapId].eMapObjId,\
                LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjX,LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY,\
                LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjX,LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjY);
#endif
        }
    }
    
    if(LeCAN_MapIndex < MAP_OBJ_TO_CAN_MAX)
    {
        /* 剩下的点需要清零 */
        for(LeCAN_MapIndex2 = LeCAN_MapIndex; LeCAN_MapIndex2 < MAP_OBJ_TO_CAN_MAX; LeCAN_MapIndex2++)
        {
            if(GstrObjMapToCAN[LeCAN_MapIndex2].eMapObjType != OBJ_TYPE_NONE)
            {
                GstrObjMapToCAN[LeCAN_MapIndex2] = GstrObjMapToCANInit;
            }
        }
    }
#if 0
    VS_PRINT("Time:%.3f,FrontArea4MapToBumperDis:%d\r\n",GfMessageTime,Gu16FrontArea4MapToBumperDis);
#endif
}


/******************************************************************************
 * 函数名称: TransfMapOdoCoorToCarCoor
 * 
 * 功能描述: 更新Map的Odo坐标，以保证车辆坐标都是最新状态
 * 
 * 输入参数:LpstrMapCoor--Map结构体指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-22 18:54   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void TransfMapOdoCoorToCarCoor(ObjMapCoorType *LpstrMapCoor)
{
    if(GstrSnsCarMovSts[0][0].eCarDir != SNS_CAR_STOP)
    {
        if(LpstrMapCoor->eMapObjType == OBJ_TYPE_POINT)
        {
            PubAI_TransObjOdoCoorToCarCoor(&LpstrMapCoor->P1_OdoCoor.fObjX,&LpstrMapCoor->P1_OdoCoor.fObjY,\
                &LpstrMapCoor->P1_CarCoor.fObjX,&LpstrMapCoor->P1_CarCoor.fObjY);
            LpstrMapCoor->P2_CarCoor = LpstrMapCoor->P1_CarCoor;
        }
        else
        {
            PubAI_TransObjOdoCoorToCarCoor(&LpstrMapCoor->P1_OdoCoor.fObjX,&LpstrMapCoor->P1_OdoCoor.fObjY,\
                &LpstrMapCoor->P1_CarCoor.fObjX,&LpstrMapCoor->P1_CarCoor.fObjY);
            
            PubAI_TransObjOdoCoorToCarCoor(&LpstrMapCoor->P2_OdoCoor.fObjX,&LpstrMapCoor->P2_OdoCoor.fObjY,\
                &LpstrMapCoor->P2_CarCoor.fObjX,&LpstrMapCoor->P2_CarCoor.fObjY);
        }
    }
}


/******************************************************************************
 * 函数名称: NearMapFastHighHandle
 * 
 * 功能描述: 针对前雷达墙角，Map快速置高的处理
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-18 14:53   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void NearMapFastHighHandle(ObjMapCoorType *LpstrMapCoor)
{
    ObjMAPAreaType LenuNearMAPArea;
    float LfPointToBumperDis;

    if(LpstrMapCoor->fP1_ToBumper_Dis > (LpstrMapCoor->fP2_ToBumper_Dis+5))
    {
        LenuNearMAPArea = LpstrMapCoor->eP2_DetailArea;
        LfPointToBumperDis = LpstrMapCoor->fP2_ToBumper_Dis;
    }
    else
    {
        LenuNearMAPArea = LpstrMapCoor->eP1_DetailArea;
        LfPointToBumperDis = LpstrMapCoor->fP1_ToBumper_Dis;
    }

    if((LenuNearMAPArea > MAP_AREA2)&&(LenuNearMAPArea < MAP_AREA6))
    {
        if(LpstrMapCoor->eObjBumperArea == OBJ_FRONT_BUMP)
        {
            if(LenuNearMAPArea == MAP_AREA4)
            {
                if(LfPointToBumperDis < FRONR_AREA4_FAST_HIGH_DIS)
                {
                    LpstrMapCoor->eObjExistProb = OBJ_PROB_LV5;    /* 近距离产生的Map，存在置信度快速置高 */
                    LpstrMapCoor->u8HeightLockFlag = 1;
                }
            }
            else
            {
                if(LfPointToBumperDis < FRONR_MAP_FAST_HIGH_DIS)
                {
                    LpstrMapCoor->eObjExistProb = OBJ_PROB_LV4;    /* 近距离产生的Map，存在置信度快速置高 */
                    LpstrMapCoor->u8HeightLockFlag = 1;
                }
            }
        }
        else
        {
            if(LfPointToBumperDis < REAR_MAP_FAST_HIGH_DIS)
            {
                LpstrMapCoor->eObjExistProb = OBJ_PROB_LV4;    /* 近距离产生的Map，存在置信度快速置高 */
                LpstrMapCoor->u8HeightLockFlag = 1;
            }
        }
    }
    else if(LenuNearMAPArea == MAP_AREA6)
    {
        if((LpstrMapCoor->eObjBumperArea == OBJ_FRONT_BUMP)&&(LpstrMapCoor->P1_CarCoor.fObjY > GstrRamSnsCoor[0][5].cRadarY))
        {
            if(LfPointToBumperDis < FRONR_MAP_FAST_HIGH_DIS)
            {
                LpstrMapCoor->eObjExistProb = OBJ_PROB_LV4;    /* 近距离产生的Map，存在置信度快速置高 */
                LpstrMapCoor->u8HeightLockFlag = 1;
            }
        }
        else
        {
            if(LfPointToBumperDis < REAR_MAP_FAST_HIGH_DIS)
            {
                LpstrMapCoor->eObjExistProb = OBJ_PROB_LV4;    /* 近距离产生的Map，存在置信度快速置高 */
                LpstrMapCoor->u8HeightLockFlag = 1;
            }
        }
    }
    else if(LenuNearMAPArea == MAP_AREA2)
    {
        if((LpstrMapCoor->eObjBumperArea == OBJ_FRONT_BUMP)&&(LpstrMapCoor->P1_CarCoor.fObjY < GstrRamSnsCoor[0][0].cRadarY))
        {
            if(LfPointToBumperDis < FRONR_MAP_FAST_HIGH_DIS)
            {
                LpstrMapCoor->eObjExistProb = OBJ_PROB_LV4;    /* 近距离产生的Map，存在置信度快速置高 */
                LpstrMapCoor->u8HeightLockFlag = 1;
            }
        }
        else
        {
            if(LfPointToBumperDis < REAR_MAP_FAST_HIGH_DIS)
            {
                LpstrMapCoor->eObjExistProb = OBJ_PROB_LV4;    /* 近距离产生的Map，存在置信度快速置高 */
                LpstrMapCoor->u8HeightLockFlag = 1;
            }
        }
    }

    if(LpstrMapCoor->u8HeightLockFlag)
    {
        LpstrMapCoor->eMapSigGroupHeight = OBJ_HIGH;
        LpstrMapCoor->eMapObjHeight = OBJ_HIGH;
        LpstrMapCoor->eMapHeightTrendHeight = OBJ_HIGH;
        LpstrMapCoor->eMapDE_NoMatchHeight = OBJ_HIGH;
    }
}



/******************************************************************************
 * 函数名称: JudgeSigGroupHeight
 * 
 * 功能描述: 根据Map对应的信号组关系，判断信号组的高度
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-01-26 14:49   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void JudgeSigGroupHeight(ObjMapCoorType *LpstrMapCoor)
{
    float LfTotalCnt;
    uint8 Lu8PvcCnt;
    uint8 Lu8LowCurbCnt;
    uint8 Lu8BigWallCnt;
    uint8 Lu8HighHighCurbCnt;
    float LfLowCurbPercent;
    float LfBigWallPercent;
    float LfHighHighCurbPercent;
    
    if(LpstrMapCoor->eObjFollowSts == OBJ_DET_UPDATE)
    {
#if 0
        if((LpstrMapCoor->eMapObjId == 0x02)&&(GfMessageTime > 9.5)&&(GfMessageTime < 11.5))
        {
            VS_PRINT("Time:%.3f,Chirp:%d,Pvc:%d,LowCurb:%d,BigWall:%d,HighHighCurb:%d,  Std:%d,Pvc:%d,LowCurb:%d,BigWall:%d,HighHighCurb:%d\r\n",GfMessageTime,\
                LpstrMapCoor->ObjTypeTotalCnt.u8ChirpTotalPointCnt,LpstrMapCoor->ObjTypeTotalCnt.u8ChirpPVC_PointCnt,\
                LpstrMapCoor->ObjTypeTotalCnt.u8ChirpLowCurb_PointCnt,LpstrMapCoor->ObjTypeTotalCnt.u8ChirpBigWall_PointCnt,\
                LpstrMapCoor->ObjTypeTotalCnt.u8ChirpHigh_HighCurb_PointCnt,LpstrMapCoor->ObjTypeTotalCnt.u8StdTotalPointCnt,\
                LpstrMapCoor->ObjTypeTotalCnt.u8StdPVC_PointCnt,LpstrMapCoor->ObjTypeTotalCnt.u8StdLowCurb_PointCnt,\
                LpstrMapCoor->ObjTypeTotalCnt.u8StdBigWall_PointCnt,LpstrMapCoor->ObjTypeTotalCnt.u8StdHigh_HighCurb_PointCnt);        
        }
#endif
        LfTotalCnt = LpstrMapCoor->ObjTypeTotalCnt.u8ChirpTotalPointCnt+LpstrMapCoor->ObjTypeTotalCnt.u8StdTotalPointCnt;
        Lu8LowCurbCnt = LpstrMapCoor->ObjTypeTotalCnt.u8ChirpLowCurb_PointCnt+LpstrMapCoor->ObjTypeTotalCnt.u8StdLowCurb_PointCnt;
        Lu8BigWallCnt = LpstrMapCoor->ObjTypeTotalCnt.u8ChirpBigWall_PointCnt+LpstrMapCoor->ObjTypeTotalCnt.u8StdBigWall_PointCnt;
        Lu8HighHighCurbCnt = LpstrMapCoor->ObjTypeTotalCnt.u8ChirpHigh_HighCurb_PointCnt+LpstrMapCoor->ObjTypeTotalCnt.u8StdHigh_HighCurb_PointCnt;
        if(LfTotalCnt > 4)
        {
            LfLowCurbPercent = Lu8LowCurbCnt/LfTotalCnt;
            LfBigWallPercent = Lu8BigWallCnt/LfTotalCnt;
            LfHighHighCurbPercent = Lu8HighHighCurbCnt/LfTotalCnt;
            if((LfBigWallPercent > 0.2)||(LfHighHighCurbPercent > 0.2))
            {
                LpstrMapCoor->eMapSigGroupHeight = OBJ_HIGH;
            }
            else if(LfLowCurbPercent > 0.45)
            {
                LpstrMapCoor->eMapSigGroupHeight = OBJ_LOW;
            }
        }
        else
        {
            Lu8BigWallCnt = Lu8BigWallCnt + Lu8HighHighCurbCnt;
            if(Lu8BigWallCnt >= 2)
            {
                LpstrMapCoor->eMapSigGroupHeight = OBJ_HIGH;
            }
            else
            {
                LpstrMapCoor->eMapSigGroupHeight = OBJ_LOW;
            }
        }
    }
    if(LpstrMapCoor->eMapSigGroupHeight == OBJ_LOW)
    {
        if(LpstrMapCoor->fMapToBumper_Dis < 400)
        {
            LpstrMapCoor->eMapSigGroupHeight = OBJ_HIGH;
            LpstrMapCoor->u8HeightLockFlag = 1;
        }
    }
    NearMapFastHighHandle(LpstrMapCoor);

    /* 侧边Map直接置高 */
    if(LpstrMapCoor->enuSideMapSns != SIDE_SNS_NONE)
    {
        LpstrMapCoor->eMapSigGroupHeight = OBJ_HIGH;
    }
#if 0
    if((LpstrMapCoor->eMapObjId == 0x00)&&(GfMessageTime > 9)&&(GfMessageTime < 12))
    {
        VS_PRINT("Time:%.3f,ObjHeight:%d,TrendHeight:%d,DE_NoMatchHeight:%d,SigGroupHeight:%d,Bumper_Dis:%.3f\r\n",GfMessageTime,\
            LpstrMapCoor->eMapObjHeight,LpstrMapCoor->eMapHeightTrendHeight,\
            LpstrMapCoor->eMapDE_NoMatchHeight,LpstrMapCoor->eMapSigGroupHeight,LpstrMapCoor->fMapToBumper_Dis);        
    }
#endif

}


/******************************************************************************
 * 函数名称: JudgeSideMapHeightInProcess
 * 
 * 功能描述: 运动过程中判断Map的高低，侧边Map高低的判断;原始点云过来的数据中，包含LowCurb--代表一个回波且回波高度低；
 *           BigWall--一个回波，但回波高度高；
 *           HighHighCurb--有二次回波且回波高度很大，后续会拆分成PVC和大墙的组合
 *           Pvc--单纯的有两个匹配的回波
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-04-08 17:03   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void JudgeSideMapHeightInProcess(ObjMapCoorType *LpstrMapCoor)
{
    uint8 Lu8TotalCnt;
    uint8 Lu8PvcCnt;
    uint8 Lu8LowCurbCnt;
    uint8 Lu8BigWallCnt;
    uint8 Lu8HighHighCurbCnt;
    float LfLowCurbPercent;

    Lu8TotalCnt = LpstrMapCoor->ObjTypeTotalCnt.u8ChirpTotalPointCnt+LpstrMapCoor->ObjTypeTotalCnt.u8StdTotalPointCnt;
    Lu8LowCurbCnt = LpstrMapCoor->ObjTypeTotalCnt.u8ChirpLowCurb_PointCnt+LpstrMapCoor->ObjTypeTotalCnt.u8StdLowCurb_PointCnt;
    Lu8BigWallCnt = LpstrMapCoor->ObjTypeTotalCnt.u8ChirpBigWall_PointCnt+LpstrMapCoor->ObjTypeTotalCnt.u8StdBigWall_PointCnt;
    Lu8HighHighCurbCnt = LpstrMapCoor->ObjTypeTotalCnt.u8ChirpHigh_HighCurb_PointCnt+LpstrMapCoor->ObjTypeTotalCnt.u8StdHigh_HighCurb_PointCnt;
    Lu8PvcCnt = LpstrMapCoor->ObjTypeTotalCnt.u8StdPVC_PointCnt+LpstrMapCoor->ObjTypeTotalCnt.u8ChirpPVC_PointCnt;

    /* 将Lu8HighHighCurbCnt拆分给大墙和PVC */
    Lu8BigWallCnt = Lu8BigWallCnt + Lu8HighHighCurbCnt;
    Lu8PvcCnt = Lu8PvcCnt + Lu8HighHighCurbCnt;

#if 0
    if((LpstrMapCoor->eMapObjId == 0x02))//&&(GfMessageTime > 3)&&(GfMessageTime < 14)
    {
        VS_PRINT("Side Map Process,MapId:%d,Time:%.3f,TotalCnt:%d,LowCurbCnt:%d,BigWall_Cnt:%d,PVC_Cnt:%d,P1_P2_Dis:%.3f,ExistProb:%d,Height:%d,LockFlag:%d\r\n",LpstrMapCoor->eMapObjId,GfMessageTime,\
            Lu8TotalCnt,Lu8LowCurbCnt,Lu8BigWallCnt,Lu8PvcCnt,LpstrMapCoor->fP1_P2_Dis,LpstrMapCoor->eObjExistProb,\
            LpstrMapCoor->eMapObjHeight,LpstrMapCoor->u8SideMapHeightLockFlag);        
    }
#endif



    if(!LpstrMapCoor->u8SideMapHeightLockFlag)
    {
        if(Lu8TotalCnt > 3)
        {
            if(Lu8PvcCnt > 3)
            {
                LpstrMapCoor->eMapObjHeight = OBJ_HIGH;
                LpstrMapCoor->u8SideMapHeightLockFlag = 1;
            }
            else
            {
                if(Lu8BigWallCnt >= 8)
                {
                    LpstrMapCoor->eMapObjHeight = OBJ_HIGH;
                    LpstrMapCoor->u8SideMapHeightLockFlag = 1;
                }
                else
                {
                    LfLowCurbPercent = Lu8TotalCnt;
                    LfLowCurbPercent = Lu8LowCurbCnt/LfLowCurbPercent;
                    if(LfLowCurbPercent > 0.6)
                    {
                        LpstrMapCoor->eMapObjHeight = OBJ_LOW;
                    }
                    else
                    {
                        LpstrMapCoor->eMapObjHeight = OBJ_HIGH;
                    }
                }
            }
        }
        else
        {
            LpstrMapCoor->eMapObjHeight = OBJ_LOW;
        }
        if(Lu8TotalCnt > 150)
        {
            LpstrMapCoor->u8SideMapHeightLockFlag = 1;
        }
    }
}



/******************************************************************************
 * 函数名称: JudgeSideMapHeightInEnd
 * 
 * 功能描述: Map结束时判断Map的高度
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-04-08 17:09   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void JudgeSideMapHeightInEnd(ObjMapCoorType *LpstrMapCoor)
{
    uint8 Lu8TotalCnt;
    uint8 Lu8PvcCnt;
    uint8 Lu8LowCurbCnt;
    uint8 Lu8BigWallCnt;
    uint8 Lu8HighHighCurbCnt;
    float LfLowCurbPercent;
    float LfBigWallPercent;
    float LfHighHighCurbPercent;

    Lu8TotalCnt = LpstrMapCoor->ObjTypeTotalCnt.u8ChirpTotalPointCnt+LpstrMapCoor->ObjTypeTotalCnt.u8StdTotalPointCnt;
    Lu8LowCurbCnt = LpstrMapCoor->ObjTypeTotalCnt.u8ChirpLowCurb_PointCnt+LpstrMapCoor->ObjTypeTotalCnt.u8StdLowCurb_PointCnt;
    Lu8BigWallCnt = LpstrMapCoor->ObjTypeTotalCnt.u8ChirpBigWall_PointCnt+LpstrMapCoor->ObjTypeTotalCnt.u8StdBigWall_PointCnt;
    Lu8HighHighCurbCnt = LpstrMapCoor->ObjTypeTotalCnt.u8ChirpHigh_HighCurb_PointCnt+LpstrMapCoor->ObjTypeTotalCnt.u8StdHigh_HighCurb_PointCnt;
    Lu8PvcCnt = LpstrMapCoor->ObjTypeTotalCnt.u8StdPVC_PointCnt+LpstrMapCoor->ObjTypeTotalCnt.u8ChirpPVC_PointCnt;

    /* 将Lu8HighHighCurbCnt拆分给大墙和PVC */
    Lu8BigWallCnt = Lu8BigWallCnt + Lu8HighHighCurbCnt;
    Lu8PvcCnt = Lu8PvcCnt + Lu8HighHighCurbCnt;

#if 0
    if((LpstrMapCoor->eMapObjId == 0x00))//&&(GfMessageTime > 3)&&(GfMessageTime < 14)
    {
        VS_PRINT("Side Map End,MapId:%d,Time:%.3f,TotalCnt:%d,LowCurbCnt:%d,BigWall_Cnt:%d,PVC_Cnt:%d,P1_P2_Dis:%.3f,ExistProb:%d\r\n",LpstrMapCoor->eMapObjId,GfMessageTime,\
            Lu8TotalCnt,Lu8LowCurbCnt,Lu8BigWallCnt,Lu8PvcCnt,LpstrMapCoor->fP1_P2_Dis,LpstrMapCoor->eObjExistProb);        
    }
#endif

    if(!LpstrMapCoor->u8SideMapHeightLockFlag)
    {
        if(LpstrMapCoor->fP1_P2_Dis < 500)
        {
            if((Lu8PvcCnt >= 2)||(Lu8BigWallCnt >= 2))
            {
                LpstrMapCoor->eMapObjHeight = OBJ_HIGH;
                LpstrMapCoor->u8SideMapHeightLockFlag = 1;
            }
        }
        else
        {
            if(Lu8TotalCnt > 3)
            {
                if(Lu8PvcCnt > 3)
                {
                    LpstrMapCoor->eMapObjHeight = OBJ_HIGH;
                    LpstrMapCoor->u8SideMapHeightLockFlag = 1;
                }
                else
                {
                    if(Lu8BigWallCnt >= 8)
                    {
                        LpstrMapCoor->eMapObjHeight = OBJ_HIGH;
                        LpstrMapCoor->u8SideMapHeightLockFlag = 1;
                    }
                    else
                    {
                        LfLowCurbPercent = Lu8TotalCnt;
                        LfLowCurbPercent = Lu8LowCurbCnt/LfLowCurbPercent;
                        if(LfLowCurbPercent > 0.6)
                        {
                            LpstrMapCoor->eMapObjHeight = OBJ_LOW;
                        }
                        else
                        {
                            LpstrMapCoor->eMapObjHeight = OBJ_HIGH;
                        }
                    }
                }
            }
            else
            {
                LpstrMapCoor->eMapObjHeight = OBJ_LOW;
            }
        }
    }
}


/******************************************************************************
 * 函数名称: FindMapPointNearSns
 * 
 * 功能描述: 查找距离点状Map最近的探头，以便于后续的回波高度匹配使用
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-01-28 11:32   V0.1      AntonyFang   初次发布
 ******************************************************************************/
PDCSnsChannelType FindMapPointNearSns(float LfMapPoint_Y,uint8 Lu8SnsGroup)
{
    PDCSnsChannelType LePDCSnsCh;
    float LfNearSns_Y_ToMap;
    float LfSns_Y_ToMap;
    PDCSnsChannelType LeNearSnsCh = PDC_SNS_CH_NONE;
    
    
    LfNearSns_Y_ToMap = 5000;        
    for(LePDCSnsCh = PDC_SNS_CH_FL_RL; LePDCSnsCh < PDC_SNS_CH_FRS_RRS; LePDCSnsCh++)
    {
        LfSns_Y_ToMap = ABS(LfMapPoint_Y,GstrRamSnsCoor[Lu8SnsGroup][LePDCSnsCh].cRadarY);
        if(LfSns_Y_ToMap < LfNearSns_Y_ToMap)
        {
            LfNearSns_Y_ToMap = LfSns_Y_ToMap;
            LeNearSnsCh = LePDCSnsCh;
        }
    }
    
    return LeNearSnsCh;
}


/******************************************************************************
 * 函数名称: JudgeMapHeight
 * 
 * 功能描述: 判断障碍物高度处理函数
 * 
 * 输入参数:LpstrMapCoor--Map结构体指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-23 11:51   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void JudgeMapHeight(ObjMapCoorType *LpstrMapCoor)
{
    /* MAX 车型，非召唤模式下 */
    if((GenuMapVheiCfgLevel == MAP_VEH_MAX)&&(GstrParkingGuidenceData.eParking_Sts != PARKING_SUMMON))
    {
        /* MAX 车型，通过3个维度的高度，联合判断Map的高低 */
        if(LpstrMapCoor->eMapDE_NoMatchHeight == OBJ_LOW)
        {
#if 1
            LpstrMapCoor->eMapObjHeight = OBJ_LOW;
#else
            if(LpstrMapCoor->u8Map_DE_MatchNearHighFlag)
            {
                LpstrMapCoor->eMapObjHeight = OBJ_HIGH;
            }
            else
            {
                LpstrMapCoor->eMapObjHeight = OBJ_LOW;
            }
#endif
        }
        else
        {
            if(LpstrMapCoor->u8Map_DE_MatchNearHighFlag)
            {
                LpstrMapCoor->eMapObjHeight = OBJ_HIGH;
            }
            else
            {
                if(LpstrMapCoor->eMapHeightTrendHeight == OBJ_HIGH)
                {
                    LpstrMapCoor->eMapObjHeight = OBJ_HIGH;
                }
                else
                {
                    LpstrMapCoor->eMapObjHeight = LpstrMapCoor->eMapSigGroupHeight;
                }
            }
        }
    }
    else
    {
        /* 非MAX 车型或MAX 车型在召唤模式下，只通过DE匹配性判断高度 */
        LpstrMapCoor->eMapObjHeight = LpstrMapCoor->eMapDE_NoMatchHeight;
    }
#if 0
    if((LpstrMapCoor->eMapObjId == 0x00)&&(GfMessageTime > 54.5)&&(GfMessageTime < 70))
    {
        VS_PRINT("Time:%.3f,Bumper_Dis:%.3f,ObjHeight:%d,TrendHeight:%d,DE_NoMatchHeight:%d,SigGroupHeight:%d,DE_MatchNearHighFlag:%d\r\n",GfMessageTime,LpstrMapCoor->fMapToBumper_Dis,\
            LpstrMapCoor->eMapObjHeight,LpstrMapCoor->eMapHeightTrendHeight,\
            LpstrMapCoor->eMapDE_NoMatchHeight,LpstrMapCoor->eMapSigGroupHeight,LpstrMapCoor->u8Map_DE_MatchNearHighFlag);        
    }
#endif
}

/******************************************************************************
 * 函数名称: JudgeMapBumperArea
 * 
 * 功能描述: 判断Map模块在保杠中的大区域位置，分为前保、后保、左侧保、右侧保
 * 
 * 输入参数:LpstrMapCoor--Map结构体指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-29 09:39   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void JudgeMapBumperArea(ObjMapCoorType *LpstrMapCoor)
{
    /* 更新Map在前后侧保杠的位置区域 */
    float LfFrontBumperX_Line,LfRearBumperX_Line;
    
    LfFrontBumperX_Line = GstrRamSnsCoor[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FLS].cRadarX - MAP_JUDGE_F_BUMPER_AREA_X_LINE_LIMIT;
    LfRearBumperX_Line = GstrRamSnsCoor[PDC_SNS_GROUP_REAR][PDC_SNS_CH_RLS].cRadarX + MAP_JUDGE_R_BUMPER_AREA_X_LINE_LIMIT;
    
    if((LpstrMapCoor->P1_CarCoor.fObjX > LfFrontBumperX_Line)&&(LpstrMapCoor->P2_CarCoor.fObjX > LfFrontBumperX_Line))
    {
        LpstrMapCoor->eObjBumperArea = OBJ_FRONT_BUMP;
    }
    else if((LpstrMapCoor->P1_CarCoor.fObjX < LfRearBumperX_Line)&&(LpstrMapCoor->P2_CarCoor.fObjX < LfRearBumperX_Line))
    {
        LpstrMapCoor->eObjBumperArea = OBJ_REAR_BUMP;
    }
    else
    {
        if(LpstrMapCoor->P1_CarCoor.fObjY > 0)
        {
            LpstrMapCoor->eObjBumperArea = OBJ_LEFT_SIDE_BUMP;
        }
        else
        {
            LpstrMapCoor->eObjBumperArea = OBJ_RIGHT_SIDE_BUMP;
        }
    }
}


/******************************************************************************
 * 函数名称: JudgeMapMoveSts
 * 
 * 功能描述: 判断Map和车辆的运动关系
 * 
 * 输入参数:LpstrMapCoor--Map结构体指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-29 10:36   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void JudgeMapMoveSts(ObjMapCoorType *LpstrMapCoor)
{
    /* 更新障碍物和车辆的相对运动状态 */
    if(LpstrMapCoor->eObjBumperArea == OBJ_FRONT_BUMP)
    {
        if(GstrSnsCarMovSts[0][0].eCarDir == SNS_CAR_FORWARD)
        {
            LpstrMapCoor->eObjMoveSts = OBJ_TO_CAR_MOVE_CLOSE;
            if(!LpstrMapCoor->u8CarMoveCloseMapFlag)
            {
                LpstrMapCoor->u8CarMoveCloseMapCnt++;
            }
            LpstrMapCoor->u32MapToCarStopTime = GdSystemMsTimer;
        }
        else if(GstrSnsCarMovSts[0][0].eCarDir == SNS_CAR_BACKWARD)
        {
            LpstrMapCoor->eObjMoveSts = OBJ_TO_CAR_MOVE_FARAWAY;
            LpstrMapCoor->u8CarMoveCloseMapFlag = 0;
            LpstrMapCoor->u8CarMoveCloseMapCnt = 0;
            LpstrMapCoor->u32MapToCarStopTime = GdSystemMsTimer;
        }
        else
        {
            if(GdSystemMsTimer > (LpstrMapCoor->u32MapToCarStopTime+MAP_MOVE_TO_STOP_HOLD_TIME))
            {
                LpstrMapCoor->eObjMoveSts = OBJ_TO_CAR_STOP_STOP;
            }
        }  
    }
    else if(LpstrMapCoor->eObjBumperArea == OBJ_REAR_BUMP)
    {
        if(GstrSnsCarMovSts[1][0].eCarDir == SNS_CAR_FORWARD)
        {
            LpstrMapCoor->eObjMoveSts = OBJ_TO_CAR_MOVE_FARAWAY;
            LpstrMapCoor->u8CarMoveCloseMapFlag = 0;
            LpstrMapCoor->u8CarMoveCloseMapCnt = 0;
            LpstrMapCoor->u32MapToCarStopTime = GdSystemMsTimer;
        }
        else if(GstrSnsCarMovSts[1][0].eCarDir == SNS_CAR_BACKWARD)
        {
            LpstrMapCoor->eObjMoveSts = OBJ_TO_CAR_MOVE_CLOSE;
            if(!LpstrMapCoor->u8CarMoveCloseMapFlag)
            {
                LpstrMapCoor->u8CarMoveCloseMapCnt++;
            }
            LpstrMapCoor->u32MapToCarStopTime = GdSystemMsTimer;
        }
        else
        {
            if(GdSystemMsTimer > (LpstrMapCoor->u32MapToCarStopTime+MAP_MOVE_TO_STOP_HOLD_TIME))
            {
                LpstrMapCoor->eObjMoveSts = OBJ_TO_CAR_STOP_STOP;
            }
        }
    }
    else
    {
        LpstrMapCoor->eObjMoveSts = OBJ_IN_SIDE_MOVE;
        LpstrMapCoor->u8CarMoveCloseMapFlag = 0;
        LpstrMapCoor->u8CarMoveCloseMapCnt = 0;
        LpstrMapCoor->u32MapToCarStopTime = GdSystemMsTimer;
    }
    /* 判断Map靠近跟踪的标志 */
    if(LpstrMapCoor->u8CarMoveCloseMapCnt > F_R_MAP_CAR_MOVE_CLOSE_CNT)
    {
        LpstrMapCoor->u8CarMoveCloseMapCnt = 0;
        LpstrMapCoor->u8CarMoveCloseMapFlag = 1;
    }    
}



/******************************************************************************
 * 函数名称: MapInBlindAreaExistObjJudge
 * 
 * 功能描述: 判断盲区内是否存在障碍物
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-16 15:06   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void MapInBlindAreaExistObjJudge(ObjMapCoorType *LpstrMapCoor)
{
    PDCSnsChannelType LenuLeftCh,LenuRightCh;
    PDCSnsGroupType LenuSnsGroup;
    uint8 Lu8MasterHaveObjFlag = 0;
    uint8 Lu8ListenHaveObjFlag = 0;
    ObjMAPAreaType LenuNearPointDetailArea;

    if(LpstrMapCoor->eMapObjType == OBJ_TYPE_POINT)
    {
        LenuNearPointDetailArea = LpstrMapCoor->eP1_DetailArea;
    }
    else
    {
        if(LpstrMapCoor->fP1_ToBumper_Dis < LpstrMapCoor->fP2_ToBumper_Dis)
        {
            LenuNearPointDetailArea = LpstrMapCoor->eP1_DetailArea;
        }
        else
        {
            LenuNearPointDetailArea = LpstrMapCoor->eP2_DetailArea;
        }
    }

    if((LenuNearPointDetailArea == MAP_AREA1)||(LenuNearPointDetailArea == MAP_AREA2))
    {
        LenuLeftCh = PDC_SNS_CH_FLS_RLS;
        LenuRightCh = LenuLeftCh+1;
    }
    else if(LenuNearPointDetailArea == MAP_AREA3)
    {
        LenuLeftCh = PDC_SNS_CH_FL_RL;
        LenuRightCh = LenuLeftCh+1;
    }
    else if(LenuNearPointDetailArea == MAP_AREA4)
    {
        LenuLeftCh = PDC_SNS_CH_FML_RML;
        LenuRightCh = LenuLeftCh+1;
    }
    else if(LenuNearPointDetailArea == MAP_AREA5)
    {
        LenuLeftCh = PDC_SNS_CH_FMR_RMR;
        LenuRightCh = LenuLeftCh+1;
    }
    else if((LenuNearPointDetailArea == MAP_AREA6)||(LenuNearPointDetailArea == MAP_AREA7))
    {
        LenuLeftCh = PDC_SNS_CH_FR_RR;
        LenuRightCh = LenuLeftCh+1;
    }
    else
    {
        LpstrMapCoor->u8BlindNoObjFlag = 0;
        return ;
    }

    if(LpstrMapCoor->eObjBumperArea == OBJ_FRONT_BUMP)
    {
        LenuSnsGroup = PDC_SNS_GROUP_FRONT;
    }
    else if(LpstrMapCoor->eObjBumperArea == OBJ_REAR_BUMP)
    {
        LenuSnsGroup = PDC_SNS_GROUP_REAR;
    }
    else
    {
        LpstrMapCoor->u8BlindNoObjFlag = 0;
        return ;
    }
    

    if((GstrMapSnsRealTimeDis[LenuSnsGroup][LenuLeftCh].u16MasDis < F_R_MAP_JUDGE_EXIST_OBJ_DIS)||\
        (GstrMapSnsRealTimeDis[LenuSnsGroup][LenuRightCh].u16MasDis < F_R_MAP_JUDGE_EXIST_OBJ_DIS))
    {
        Lu8MasterHaveObjFlag = 1;
    }
    if((GstrMapSnsRealTimeDis[LenuSnsGroup][LenuLeftCh].u16RightDis < F_R_MAP_JUDGE_EXIST_OBJ_LISTEN_DIS)||\
        (GstrMapSnsRealTimeDis[LenuSnsGroup][LenuRightCh].u16LeftDis < F_R_MAP_JUDGE_EXIST_OBJ_LISTEN_DIS))
    {
        Lu8ListenHaveObjFlag = 1;
    }
#if 0
    VS_PRINT("Time:%.3f,Near Obj,Id:%d,LeftMaster:%d,RightMaster:%d,Left:%d,Right:%d,LeftCh:%d,RightCh:%d\r\n",GfMessageTime,LpstrMapCoor->eMapObjId,\
        GstrMapSnsRealTimeDis[LenuSnsGroup][LenuLeftCh].u16MasDis,GstrMapSnsRealTimeDis[LenuSnsGroup][LenuRightCh].u16MasDis,\
        GstrMapSnsRealTimeDis[LenuSnsGroup][LenuLeftCh].u16RightDis,GstrMapSnsRealTimeDis[LenuSnsGroup][LenuRightCh].u16LeftDis,\
        LenuLeftCh,LenuRightCh);
#endif
    if(!LpstrMapCoor->u8BlindNoObjFlag)
    {
        if((Lu8MasterHaveObjFlag == 0)&&(Lu8ListenHaveObjFlag == 0))
        {
            if((GdSystemMsTimer - LpstrMapCoor->u32BlindNoObjTime) > MAP_BLIND_NO_OBJ_TIME)
            {
                LpstrMapCoor->u32BlindNoObjTime = GdSystemMsTimer;
                LpstrMapCoor->u8BlindNoObjFlag = 1;
            }
        }
        else
        {
            LpstrMapCoor->u32BlindNoObjTime = GdSystemMsTimer;
        }
    }
}




/******************************************************************************
 * 函数名称: JudgeMapUpdateSts
 * 
 * 功能描述: 判断Map的更新状态
 * 
 * 输入参数:LpstrMapCoor--Map结构体指针
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-29 10:42   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void JudgeMapUpdateSts(ObjMapCoorType *LpstrMapCoor)
{
    float LfLeftSideBumper_Y_Line,LfRightSideBumper_Y_Line;
    uint16 Lu16BlindEnterDis,Lu16BlindExitDis;
    ObjMAPAreaType LenuLineNearPointArea;
    
    LfLeftSideBumper_Y_Line = GstrRamSnsCoor[0][0].cRadarY +MAP_JUDGE_BUMPER_AREA_Y_LINE_LIMIT;
    LfRightSideBumper_Y_Line = GstrRamSnsCoor[0][5].cRadarY -MAP_JUDGE_BUMPER_AREA_Y_LINE_LIMIT;

    if(LpstrMapCoor->eMapObjType == OBJ_TYPE_POINT)
    {
        LenuLineNearPointArea = LpstrMapCoor->eP1_DetailArea;
    }
    else
    {
        if(LpstrMapCoor->fP1_ToBumper_Dis < LpstrMapCoor->fP2_ToBumper_Dis)
        {
            LenuLineNearPointArea = LpstrMapCoor->eP1_DetailArea;
        }
        else
        {
            LenuLineNearPointArea = LpstrMapCoor->eP2_DetailArea;
        }
    }

    /* 区分召唤模式和非召唤模式 */
    if(GstrParkingGuidenceData.eParking_Sts == PARKING_SUMMON)
    {
        if((LenuLineNearPointArea > MAP_AREA2)&&(LenuLineNearPointArea < MAP_AREA6))
        {
            if(LenuLineNearPointArea == MAP_AREA4)
            {
                Lu16BlindEnterDis = MAP_BLIND_SUMMON_AREA4_ENTER_DIS;
                Lu16BlindExitDis = MAP_BLIND_SUMMON_AREA4_EXIT_DIS;
            }
            else
            {
                Lu16BlindEnterDis = MAP_BLIND_SUMMON_MID_ENTER_DIS;
                Lu16BlindExitDis = MAP_BLIND_SUMMON_MID_EXIT_DIS;
            }
        }
        else
        {
            Lu16BlindEnterDis = MAP_BLIND_SUMMON_CORNER_ENTER_DIS;
            Lu16BlindExitDis = MAP_BLIND_SUMMON_CORNER_EXIT_DIS;
        }
    }
    else
    {
        if((LenuLineNearPointArea > MAP_AREA2)&&(LenuLineNearPointArea < MAP_AREA6))
        {
            if(LenuLineNearPointArea == MAP_AREA4)
            {
                Lu16BlindEnterDis = MAP_BLIND_AREA4_ENTER_DIS;
                Lu16BlindExitDis = MAP_BLIND_AREA4_EXIT_DIS;
            }
            else
            {
                Lu16BlindEnterDis = MAP_BLIND_MIDDLE_ENTER_DIS;
                Lu16BlindExitDis = MAP_BLIND_MIDDLE_EXIT_DIS;
            }
        }
        else
        {
            Lu16BlindEnterDis = MAP_BLIND_CORNER_ENTER_DIS;
            Lu16BlindExitDis = MAP_BLIND_CORNER_EXIT_DIS;
        }
    }

    
    /* 更新Map的更新状态，分为更新区、远离记忆区、近距离盲区跟踪 */
    if((LpstrMapCoor->eObjBumperArea == OBJ_FRONT_BUMP)||(LpstrMapCoor->eObjBumperArea == OBJ_REAR_BUMP))
    {
        if(LpstrMapCoor->eObjFollowSts == OBJ_DET_UPDATE)
        {
            if(LpstrMapCoor->eObjMoveSts == OBJ_TO_CAR_MOVE_CLOSE)
            {
                if(LpstrMapCoor->fMapToBumper_Dis < Lu16BlindEnterDis)
                {
                    LpstrMapCoor->eObjFollowSts = OBJ_BLIND_FOLLOW;
                    LpstrMapCoor->u8CarCloseToMapBlindFlag = 1;
                    LpstrMapCoor->u32BlindNoObjTime = GdSystemMsTimer;
                }
                else
                {
                    LpstrMapCoor->eObjFollowSts = OBJ_DET_UPDATE;
                }
            }
            else if(LpstrMapCoor->eObjMoveSts == OBJ_TO_CAR_MOVE_FARAWAY)
            {
                if(LpstrMapCoor->fMapToBumper_Dis < Lu16BlindEnterDis)
                {
                    LpstrMapCoor->eObjFollowSts = OBJ_BLIND_FOLLOW;
                }
                else if(LpstrMapCoor->fMapToBumper_Dis > MAP_FAR_AWAY_NO_UPDATE_DIS)
                {
                    LpstrMapCoor->eObjFollowSts = OBJ_OUT_DET_FOLLOW;
                }
                else
                {
                    LpstrMapCoor->eObjFollowSts = OBJ_DET_UPDATE;
                }
            }
            else
            {
                if(LpstrMapCoor->fMapToBumper_Dis < MAP_DETECT_ENTER_DIS)
                {
                    LpstrMapCoor->eObjFollowSts = OBJ_DET_UPDATE;
                }
            }
        }
        else if(LpstrMapCoor->eObjFollowSts == OBJ_OUT_DET_FOLLOW)
        {
            if(LpstrMapCoor->eObjMoveSts == OBJ_TO_CAR_MOVE_CLOSE)
            {
                if(LpstrMapCoor->fMapToBumper_Dis < MAP_CLOSE_DETECT_ENTER_DIS)
                {
                    LpstrMapCoor->eObjFollowSts = OBJ_DET_UPDATE;
                }
                else
                {
                    LpstrMapCoor->eObjFollowSts = OBJ_OUT_DET_FOLLOW;
                }
            }
            else if(LpstrMapCoor->eObjMoveSts == OBJ_TO_CAR_MOVE_FARAWAY)
            {
                LpstrMapCoor->eObjFollowSts = OBJ_OUT_DET_FOLLOW;
            }
            else
            {
                if(LpstrMapCoor->fMapToBumper_Dis < MAP_DETECT_ENTER_DIS)
                {
                    LpstrMapCoor->eObjFollowSts = OBJ_DET_UPDATE;
                }
            }
        }
        else if(LpstrMapCoor->eObjFollowSts == OBJ_BLIND_FOLLOW)
        {
            if(LpstrMapCoor->eObjMoveSts == OBJ_TO_CAR_MOVE_CLOSE)
            {
                LpstrMapCoor->eObjFollowSts = OBJ_BLIND_FOLLOW;
                LpstrMapCoor->u8CarCloseToMapBlindFlag = 1;
                LpstrMapCoor->u32BlindNoObjTime = GdSystemMsTimer;
            }
            else if(LpstrMapCoor->eObjMoveSts == OBJ_TO_CAR_MOVE_FARAWAY)
            {
                LpstrMapCoor->u8CarCloseToMapBlindFlag = 0;
                if(LpstrMapCoor->fMapToBumper_Dis > Lu16BlindExitDis)
                {
                    if(LpstrMapCoor->fMapToBumper_Dis < MAP_BLIND_FARAWAY_DETECT_DIS)
                    {
                        LpstrMapCoor->eObjFollowSts = OBJ_DET_UPDATE;
                    }
                    else 
                    {
                        LpstrMapCoor->eObjFollowSts = OBJ_OUT_DET_FOLLOW;
                    }
                }
                else
                {
                    LpstrMapCoor->eObjFollowSts = OBJ_BLIND_FOLLOW;
                }
                LpstrMapCoor->u32BlindNoObjTime = GdSystemMsTimer;
            }
            else
            {
                /* 需添加判断近距离有无障碍物的函数 */
                MapInBlindAreaExistObjJudge(LpstrMapCoor);
                if(!LpstrMapCoor->u8BlindNoObjFlag)
                {
                    LpstrMapCoor->eObjFollowSts = OBJ_BLIND_FOLLOW;
                }
                else
                {
                    if(LpstrMapCoor->u8CarCloseToMapBlindFlag)
                    {
                        LpstrMapCoor->u8CarCloseToMapBlindFlag = 0;
                        LpstrMapCoor->u32BlindWaitDisappearTime = GdSystemMsTimer;
                    }
                    if((GdSystemMsTimer - LpstrMapCoor->u32BlindWaitDisappearTime) < MAP_BLIND_DISAPPERA_WAIT_TIME)
                    {
                        LpstrMapCoor->eObjFollowSts = OBJ_BLIND_FOLLOW;
                    }
                    else
                    {
                        LpstrMapCoor->eObjFollowSts = OBJ_DET_UPDATE;
                        LpstrMapCoor->u8BlindNoObjFlag = 0;
                    }
                }
            }
        }
        else if(LpstrMapCoor->eObjFollowSts == OBJ_SIDE_FOLLOW)
        {
            if(((LpstrMapCoor->P1_CarCoor.fObjY > LfRightSideBumper_Y_Line)&&(LpstrMapCoor->P1_CarCoor.fObjY < LfLeftSideBumper_Y_Line))||\
                ((LpstrMapCoor->P2_CarCoor.fObjY > LfRightSideBumper_Y_Line)&&(LpstrMapCoor->P2_CarCoor.fObjY < LfLeftSideBumper_Y_Line)))
            {
                if(LpstrMapCoor->fMapToBumper_Dis < MAP_SIDE_DETECT_ENTER_DIS)
                {
                    LpstrMapCoor->eObjFollowSts = OBJ_DET_UPDATE;
                }
                else
                {
                    LpstrMapCoor->eObjFollowSts = OBJ_OUT_DET_FOLLOW;
                }
            }
        }
    }
    else
    {
        LpstrMapCoor->eObjFollowSts = OBJ_SIDE_FOLLOW;
    }
}

static void MapExistProbSub(ObjMapCoorType *LpstrMapCoor)
{
    if(LpstrMapCoor->eObjExistProb > OBJ_PROB_NONE)
    {
        LpstrMapCoor->eObjExistProb--;
#if 0
        VS_PRINT("Map Prob Sub,Map_Id:%d,Time:%.3f,P1_X:%.3f,P1_Y:%.3f,BumperDis:%.3f,DetailArea:%d,Prob:%d,HoldTime:%d\r\n",LpstrMapCoor->eMapObjId,GfMessageTime,\
            LpstrMapCoor->P1_CarCoor.fObjX,LpstrMapCoor->P1_CarCoor.fObjY,\
            LpstrMapCoor->fMapToBumper_Dis,LpstrMapCoor->eP1_DetailArea,\
            LpstrMapCoor->eObjExistProb,LpstrMapCoor->u16NoUpdateHoldTime);
#endif
    }
}

/******************************************************************************
 * 函数名称: MapExistProbSubHandle
 * 
 * 功能描述: Map存在置信度递减处理函数，根据Map在不同位置，采用不同的更新策略
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-22 14:21   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void MapExistProbSubHandle(ObjMapCoorType *LpstrMapCoor)
{
    if(LpstrMapCoor->eObjFollowSts == OBJ_DET_UPDATE)
    {
        if(LpstrMapCoor->fMapToBumper_Dis < MAP_EXIST_PROB_SUB_NEAR_DIS)
        {
            /* 近距离不处理 */
        }
        else if(LpstrMapCoor->fMapToBumper_Dis < MAP_EXIST_PROB_SUB_MIDDLE_DIS)
        {
            if(LpstrMapCoor->u16NoUpdateHoldTime > 160)
            {
                MapExistProbSub(LpstrMapCoor);
                LpstrMapCoor->u16NoUpdateHoldTime = 0;
                LpstrMapCoor->u32UpdateSysTime = GdSystemMsTimer;
            }
        }
        else
        {
            if(Gu16CarSpdForSnsUse < MAP_EXIST_PROB_SUB_HIGH_SPD)
            {
                if(LpstrMapCoor->u16NoUpdateHoldTime > 320)
                {
                    MapExistProbSub(LpstrMapCoor);
                    LpstrMapCoor->u16NoUpdateHoldTime = 0;
                    LpstrMapCoor->u32UpdateSysTime = GdSystemMsTimer;
                }
            }
            else
            {
                if(LpstrMapCoor->u16NoUpdateHoldTime > 160)
                {
                    MapExistProbSub(LpstrMapCoor);
                    LpstrMapCoor->u16NoUpdateHoldTime = 0;
                    LpstrMapCoor->u32UpdateSysTime = GdSystemMsTimer;
                }
            }
        }
    }
}


/******************************************************************************
 * 函数名称: UpdateMapDetTimeAndJudgeDelete
 * 
 * 功能描述: 更新Map的探测时间，并判断Map是否满足删除条件
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-29 10:47   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static uint8 UpdateMapDetTimeAndJudgeDelete(ObjMapCoorType *LpstrMapCoor)
{
    uint8 Lu8MapNeedDeleteFlag = 0;
    uint16 Lu16MapNoUpdateNeedDeleTimeSet;
    float LfMapP1_X_ABS_Value;
    float LfMapP2_X_ABS_Value;
    float LfMapP1_Y_ABS_Value;
    float LfMapP2_Y_ABS_Value;
    uint8 Lu8SnsCh1,Lu8SnsCh2;
    uint16 Lu16ToBumperDisAndDE_Sub;
    PdcSignal_PDCWorkStatusType LenuPDCWorkStatus;

    float LfNearCarPointX_Coor;
    float LfNearCarPointY_Coor;
    float LfNearCarPointY_CoorAbs;
    float LfP1_P2ToCarX_AngleAbs;
    
    LfP1_P2ToCarX_AngleAbs = ABS_VALUE(LpstrMapCoor->fP1_P2ToCarX_Angle);
    LfMapP1_X_ABS_Value = ABS_VALUE(LpstrMapCoor->P1_CarCoor.fObjX);
    LfMapP1_Y_ABS_Value = ABS_VALUE(LpstrMapCoor->P1_CarCoor.fObjY);
    LfMapP2_X_ABS_Value = ABS_VALUE(LpstrMapCoor->P2_CarCoor.fObjX);
    LfMapP2_Y_ABS_Value = ABS_VALUE(LpstrMapCoor->P2_CarCoor.fObjY);

#if 0
    PdcSignal_ReadPDCWorkStatus(&LenuPDCWorkStatus);
    /* standby模式下前后雷达Map直接删除 */
    if(LenuPDCWorkStatus == PDC_Work_Standby)
    {
        if(LpstrMapCoor->enuSideMapSns == SIDE_SNS_NONE)
        {
            return 1;
        }
    }
#endif
#if 0
    if((GstrParkingGuidenceData.fCarAngleSub > F_R_MAP_30_ANGLE)&&(LpstrMapCoor->u8CreatMapInRearBumperInnerFlag))
    {
        if(!LpstrMapCoor->u8MiddleMapToSideFlag)
        {
            if(LpstrMapCoor->enuSideMapSns == SIDE_SNS_NONE)
            {
                if(LpstrMapCoor->eMapObjType == OBJ_TYPE_POINT)
                {
                    LfNearCarPointX_Coor = LpstrMapCoor->P1_CarCoor.fObjX;
                    LfNearCarPointY_Coor = LpstrMapCoor->P1_CarCoor.fObjY;
                    LfNearCarPointY_CoorAbs = LfMapP1_Y_ABS_Value;
                }
                else
                {
                    if(LfMapP1_Y_ABS_Value < LfMapP2_Y_ABS_Value)
                    {
                        LfNearCarPointX_Coor = LpstrMapCoor->P1_CarCoor.fObjX;
                        LfNearCarPointY_Coor = LpstrMapCoor->P1_CarCoor.fObjY;
                        LfNearCarPointY_CoorAbs = LfMapP1_Y_ABS_Value;
                    }
                    else
                    {
                        LfNearCarPointX_Coor = LpstrMapCoor->P2_CarCoor.fObjX;
                        LfNearCarPointY_Coor = LpstrMapCoor->P2_CarCoor.fObjY;
                        LfNearCarPointY_CoorAbs = LfMapP2_Y_ABS_Value;
                    }
                }
                if(LfNearCarPointX_Coor < GstrRamSnsCoor[1][0].cRadarX)
                {
                    if(LfNearCarPointY_CoorAbs > (GstrRamSnsCoor[1][0].cRadarY+50))
                    {
                        LpstrMapCoor->eObjHeightProb = OBJ_PROB_LV4;
                        LpstrMapCoor->u8MiddleMapToSideFlag = 1;
                        LpstrMapCoor->u32UpdateSysTime = GdSystemMsTimer;
                    }
                }
            }
        }
        else
        {
            if(LpstrMapCoor->eMapObjType == OBJ_TYPE_POINT)
            {
                LfNearCarPointX_Coor = LpstrMapCoor->P1_CarCoor.fObjX;
                LfNearCarPointY_Coor = LpstrMapCoor->P1_CarCoor.fObjY;
                LfNearCarPointY_CoorAbs = LfMapP1_Y_ABS_Value;
            }
            else
            {
                if(LfMapP1_Y_ABS_Value < LfMapP2_Y_ABS_Value)
                {
                    LfNearCarPointX_Coor = LpstrMapCoor->P1_CarCoor.fObjX;
                    LfNearCarPointY_Coor = LpstrMapCoor->P1_CarCoor.fObjY;
                    LfNearCarPointY_CoorAbs = LfMapP1_Y_ABS_Value;
                }
                else
                {
                    LfNearCarPointX_Coor = LpstrMapCoor->P2_CarCoor.fObjX;
                    LfNearCarPointY_Coor = LpstrMapCoor->P2_CarCoor.fObjY;
                    LfNearCarPointY_CoorAbs = LfMapP2_Y_ABS_Value;
                }
            }
            LpstrMapCoor->u32UpdateSysTime = GdSystemMsTimer;
            if(LfNearCarPointY_CoorAbs < GstrRamSnsCoor[0][0].cRadarY)
            {
                LpstrMapCoor->u8MiddleMapToSideFlag = 0;
                LpstrMapCoor->eObjHeightProb = OBJ_PROB_LV1;
            }
            /* 待添加防止卡死策略，设置车身边沿20cm以内，使用角雷达和侧雷达的DE进行匹配判断，超过1s未匹配的，即进入删除策略 */
            if((LpstrMapCoor->fMapToBumper_Dis < 600)&&(GstrSnsCarMovSts[0][2].eCarDir != SNS_CAR_FORWARD))
            {
                if(LfNearCarPointY_Coor > 0)
                {
                    Lu8SnsCh1 = 0;
                    Lu8SnsCh2 = 1;
                }
                else
                {
                    Lu8SnsCh1 = 5;
                    Lu8SnsCh2 = 4;
                }
                if(GstrMapSnsRealTimeDis[1][Lu8SnsCh1].u8RealDisUpdateFlag)
                {
                    Lu16ToBumperDisAndDE_Sub = ABS(LpstrMapCoor->fMapToBumper_Dis,GstrMapSnsRealTimeDis[1][Lu8SnsCh1].u16MasDis);
                    if(Lu16ToBumperDisAndDE_Sub < 200)
                    {
                        LpstrMapCoor->u8MemoryMapDeNoMatchCnt = 0;
                    }
                    else
                    {
                        LpstrMapCoor->u8MemoryMapDeNoMatchCnt++;
                    } 
                }
                if(GstrMapSnsRealTimeDis[1][Lu8SnsCh2].u8RealDisUpdateFlag)
                {
                    Lu16ToBumperDisAndDE_Sub = ABS(LpstrMapCoor->fMapToBumper_Dis,GstrMapSnsRealTimeDis[1][Lu8SnsCh2].u16MasDis);
                    if(Lu16ToBumperDisAndDE_Sub < 200)
                    {
                        LpstrMapCoor->u8MemoryMapDeNoMatchCnt = 0;
                    }
                    else
                    {
                        LpstrMapCoor->u8MemoryMapDeNoMatchCnt++;
                    } 
                }
                if(LpstrMapCoor->u8MemoryMapDeNoMatchCnt > 10)
                {
                    Lu8MapNeedDeleteFlag = 1;
                }
            }
            else
            {
                LpstrMapCoor->u8MemoryMapDeNoMatchCnt = 0;
            }
            if(LfNearCarPointX_Coor > (GstrRamSnsCoor[1][0].cRadarX+50))
            {
                Lu8MapNeedDeleteFlag = 1;
            }
        }
    }
#endif

    

    /* 对于已经从侧边进入到前后区域的恢复处理，避免误删除 */
    if(LpstrMapCoor->u8SideMapToF_R_MapFlag)
    {
        if(((LpstrMapCoor->eP1_DetailArea < MAP_AREA3)||(LpstrMapCoor->eP1_DetailArea > MAP_AREA5))&&\
            ((LpstrMapCoor->eP2_DetailArea < MAP_AREA3)||(LpstrMapCoor->eP2_DetailArea > MAP_AREA5)))
        {
            LpstrMapCoor->u32UpdateSysTime = GdSystemMsTimer;
            LpstrMapCoor->enuSideMapSns = SIDE_SNS_FLS;
            LpstrMapCoor->eObjHeightProb = OBJ_PROB_LV2;
            LpstrMapCoor->u8SideMapToF_R_MapFlag = 0;
        }
    }

    /* 对于低矮障碍物的删除处理 */
    if((LpstrMapCoor->eMapObjHeight == OBJ_LOW)&&(LpstrMapCoor->eObjExistProb > OBJ_PROB_LV1))
    {
        if((LfMapP1_Y_ABS_Value < (GstrRamSnsCoor[0][0].cRadarY+200))||(LfMapP2_Y_ABS_Value < (GstrRamSnsCoor[0][0].cRadarY+200)))
        {
            LpstrMapCoor->u32UpdateSysTime = GdSystemMsTimer;
        }
    }

    LpstrMapCoor->u16NoUpdateHoldTime = GdSystemMsTimer - LpstrMapCoor->u32UpdateSysTime;

    if((LpstrMapCoor->fMapToBumper_Dis > SIDE_MAP_FAR_AWAY_DELETE_DIS))
    {
        Lu8MapNeedDeleteFlag = 1;
        //VS_PRINT("Time:%.3f,Dele 001,Id:%d\r\n",GfMessageTime,LpstrMapCoor->eMapObjId);
    }
    else
    {
        if((LfMapP1_X_ABS_Value > MAP_TO_CAN_X_LIMIT_MAX)&&(LfMapP2_X_ABS_Value > MAP_TO_CAN_X_LIMIT_MAX))
        {
            Lu8MapNeedDeleteFlag = 1;
            //VS_PRINT("Time:%.3f,Dele 007,Id:%d\r\n",GfMessageTime,LpstrMapCoor->eMapObjId);
        }
    }

    if((LpstrMapCoor->eObjFollowSts != OBJ_DET_UPDATE))
    {
        /* 非更新区域内，需要实时更新其刷新时间，以保证再次进入到更新区时，不会立马被删除 */
        if(LpstrMapCoor->enuSideMapSns == SIDE_SNS_NONE)
        {
            LpstrMapCoor->u32UpdateSysTime = GdSystemMsTimer;
        }
    }
    
    if(GstrSnsCarMovSts[0][0].eCarDir == SNS_CAR_STOP)
    {
        if(LpstrMapCoor->fMapToBumper_Dis < 750)
        {
            Lu16MapNoUpdateNeedDeleTimeSet = MAP_NO_UPDATE_DELETE_NEAR_STOP_TIME;
        }
        else if(LpstrMapCoor->fMapToBumper_Dis < 2000)
        {
            Lu16MapNoUpdateNeedDeleTimeSet = MAP_NO_UPDATE_DELETE_MIDDLE_STOP_TIME;
        }
        else
        {
            Lu16MapNoUpdateNeedDeleTimeSet = MAP_NO_UPDATE_DELETE_FAR_STOP_TIME;
        }
    }
    else
    {
        if(LpstrMapCoor->fMapToBumper_Dis < 750)
        {
            Lu16MapNoUpdateNeedDeleTimeSet = MAP_NO_UPDATE_DELETE_NEAR_MOVE_TIME;
        }
        else if(LpstrMapCoor->fMapToBumper_Dis < 2000)
        {
            Lu16MapNoUpdateNeedDeleTimeSet = MAP_NO_UPDATE_DELETE_MIDDLE_MOVE_TIME;
        }
        else
        {
            Lu16MapNoUpdateNeedDeleTimeSet = MAP_NO_UPDATE_DELETE_FAR_MOVE_TIME;
        }
    }

    

    if(LpstrMapCoor->enuSideMapSns == SIDE_SNS_NONE)
    {
        if((LpstrMapCoor->u16NoUpdateHoldTime > Lu16MapNoUpdateNeedDeleTimeSet))
        {
            Lu8MapNeedDeleteFlag = 1;
            //VS_PRINT("Time:%.3f,Dele 003,Id:%d\r\n",GfMessageTime,LpstrMapCoor->eMapObjId);
        }
    }

    /* 超过保杠区域的删除策略--主要针对前后Map */
    float LfMapOutRangeDeleDisCompent;

    if(LpstrMapCoor->enuSideMapSns == SIDE_SNS_NONE)
    {
        if(LpstrMapCoor->eMapObjHeight == OBJ_LOW)
        {
            LfMapOutRangeDeleDisCompent = MAP_OUT_RANGE_DELE_DIS_COMPENSET_LOW;
        }
        else
        {
            LfMapOutRangeDeleDisCompent = MAP_OUT_RANGE_DELE_DIS_COMPENSET;
        }
        if(LpstrMapCoor->eObjBumperArea == OBJ_FRONT_BUMP)
        {
            if(LpstrMapCoor->P1_CarCoor.fObjX < LpstrMapCoor->P2_CarCoor.fObjX)
            {
                if(LfMapP1_Y_ABS_Value < (GstrRamSnsCoor[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FLS].cRadarY +100))
                {
                    if(LpstrMapCoor->eP1_DetailArea == MAP_AREA4)
                    {
                        if(LpstrMapCoor->P1_CarCoor.fObjX < (GstrRamSnsCoor[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FML].cRadarX-LfMapOutRangeDeleDisCompent))
                        {
                            Lu8MapNeedDeleteFlag = 1;
                        }
                    }
                    else if((LpstrMapCoor->eP1_DetailArea == MAP_AREA3)||(LpstrMapCoor->eP1_DetailArea == MAP_AREA5))
                    {
                        if(LpstrMapCoor->P1_CarCoor.fObjX < (GstrRamSnsCoor[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FL].cRadarX-LfMapOutRangeDeleDisCompent))
                        {
                            Lu8MapNeedDeleteFlag = 1;
                        }
                    }
                    else if(LfMapP1_Y_ABS_Value < GstrRamSnsCoor[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FLS].cRadarY)
                    {
                        if(LpstrMapCoor->P1_CarCoor.fObjX < (GstrRamSnsCoor[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FLS].cRadarX+150))
                        {
                            Lu8MapNeedDeleteFlag = 1;
                        }
                    }
                }
                if(LpstrMapCoor->eMapObjHeight != OBJ_LOW)
                {
                    if(LpstrMapCoor->P1_CarCoor.fObjX < (GstrRamSnsCoor[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FLS].cRadarX))
                    {
                        Lu8MapNeedDeleteFlag = 1;
                    }
                }
            }
            else
            {
                if(LfMapP2_Y_ABS_Value < (GstrRamSnsCoor[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FLS].cRadarY +100))
                {
                    if(LpstrMapCoor->eP2_DetailArea == MAP_AREA4)
                    {
                        if(LpstrMapCoor->P2_CarCoor.fObjX < (GstrRamSnsCoor[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FML].cRadarX-LfMapOutRangeDeleDisCompent))
                        {
                            Lu8MapNeedDeleteFlag = 1;
                        }
                    }
                    else if((LpstrMapCoor->eP2_DetailArea == MAP_AREA3)||(LpstrMapCoor->eP1_DetailArea == MAP_AREA5))
                    {
                        if(LpstrMapCoor->P2_CarCoor.fObjX < (GstrRamSnsCoor[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FL].cRadarX-LfMapOutRangeDeleDisCompent))
                        {
                            Lu8MapNeedDeleteFlag = 1;
                        }
                    }
                    else if(LfMapP2_Y_ABS_Value < GstrRamSnsCoor[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FLS].cRadarY)
                    {
                        if(LpstrMapCoor->P2_CarCoor.fObjX < (GstrRamSnsCoor[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FLS].cRadarX+150))
                        {
                            Lu8MapNeedDeleteFlag = 1;
                        }
                    }
                }
                if(LpstrMapCoor->eMapObjHeight != OBJ_LOW)
                {
                    if(LpstrMapCoor->P2_CarCoor.fObjX < (GstrRamSnsCoor[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FLS].cRadarX))
                    {
                        Lu8MapNeedDeleteFlag = 1;
                    }
                }
            }
        }
        else if(LpstrMapCoor->eObjBumperArea == OBJ_REAR_BUMP)
        {
            if(LpstrMapCoor->P1_CarCoor.fObjX > LpstrMapCoor->P2_CarCoor.fObjX)
            {
                if(LfMapP1_Y_ABS_Value < (GstrRamSnsCoor[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FLS].cRadarY +200))
                {
                    if(LpstrMapCoor->eP1_DetailArea == MAP_AREA4)
                    {
                        if(LpstrMapCoor->P1_CarCoor.fObjX > (GstrRamSnsCoor[PDC_SNS_GROUP_REAR][PDC_SNS_CH_RML].cRadarX+LfMapOutRangeDeleDisCompent))
                        {
                            Lu8MapNeedDeleteFlag = 1;
                        }
                    }
                    else if((LpstrMapCoor->eP1_DetailArea == MAP_AREA3)||(LpstrMapCoor->eP1_DetailArea == MAP_AREA5))
                    {
                        if(LpstrMapCoor->P1_CarCoor.fObjX > (GstrRamSnsCoor[PDC_SNS_GROUP_REAR][PDC_SNS_CH_RL].cRadarX+LfMapOutRangeDeleDisCompent))
                        {
                            Lu8MapNeedDeleteFlag = 1;
                        }
                    }
                    else if(LfMapP1_Y_ABS_Value < (GstrRamSnsCoor[PDC_SNS_GROUP_REAR][PDC_SNS_CH_RLS].cRadarY+200))
                    {
                        if(LpstrMapCoor->P1_CarCoor.fObjX > (GstrRamSnsCoor[PDC_SNS_GROUP_REAR][PDC_SNS_CH_RLS].cRadarX-200))
                        {
                            Lu8MapNeedDeleteFlag = 1;
                        }
                    }
                }
                if(LpstrMapCoor->eMapObjHeight != OBJ_LOW)
                {
                    if(LpstrMapCoor->P1_CarCoor.fObjX > (GstrRamSnsCoor[PDC_SNS_GROUP_REAR][PDC_SNS_CH_RLS].cRadarX-100))
                    {
                        Lu8MapNeedDeleteFlag = 1;
                    }
                }
            }
            else
            {
                if(LfMapP2_Y_ABS_Value < (GstrRamSnsCoor[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FLS].cRadarY +200))
                {
                    if(LpstrMapCoor->eP2_DetailArea == MAP_AREA4)
                    {
                        if(LpstrMapCoor->P2_CarCoor.fObjX > (GstrRamSnsCoor[PDC_SNS_GROUP_REAR][PDC_SNS_CH_RML].cRadarX+LfMapOutRangeDeleDisCompent))
                        {
                            Lu8MapNeedDeleteFlag = 1;
                        }
                    }
                    else if((LpstrMapCoor->eP2_DetailArea == MAP_AREA3)||(LpstrMapCoor->eP1_DetailArea == MAP_AREA5))
                    {
                        if(LpstrMapCoor->P2_CarCoor.fObjX > (GstrRamSnsCoor[PDC_SNS_GROUP_REAR][PDC_SNS_CH_RL].cRadarX+LfMapOutRangeDeleDisCompent))
                        {
                            Lu8MapNeedDeleteFlag = 1;
                        }
                    }
                    else if(LfMapP2_Y_ABS_Value < (GstrRamSnsCoor[PDC_SNS_GROUP_REAR][PDC_SNS_CH_RLS].cRadarY+200))
                    {
                        if(LpstrMapCoor->P2_CarCoor.fObjX > (GstrRamSnsCoor[PDC_SNS_GROUP_REAR][PDC_SNS_CH_RLS].cRadarX-200))
                        {
                            Lu8MapNeedDeleteFlag = 1;
                        }
                    }
                }
                if(LpstrMapCoor->eMapObjHeight != OBJ_LOW)
                {
                    if(LpstrMapCoor->P2_CarCoor.fObjX > (GstrRamSnsCoor[PDC_SNS_GROUP_REAR][PDC_SNS_CH_RLS].cRadarX-100))
                    {
                        Lu8MapNeedDeleteFlag = 1;
                    }
                }
            }
        } 
    }

    /* 针对侧边短Map的删除策略 */
    if((LpstrMapCoor->enuSideMapSns != SIDE_SNS_NONE)&&(LpstrMapCoor->enuSideMapUpdateSts == SIDE_MAP_MEMORY_P1_MEMORY_P2))
    {
        /* 仅针对Search阶段的短Map和置信度低的Map进行删除 */
        if(LpstrMapCoor->eMapCreatAdasSts != APA_GUIDANCE)
        {
            /* 需要维持点状Map High的存在 (LpstrMapCoor->fP1_P2_Dis < 80)*/
            if((LpstrMapCoor->eObjExistProb < OBJ_PROB_LV3))
            {
                Lu8MapNeedDeleteFlag = 1;
                //VS_PRINT("Time:%.3f,Dele 004,Id:%d,P1_P2_Dis:%.3f,ExistProb:%d\r\n",GfMessageTime,LpstrMapCoor->eMapObjId,LpstrMapCoor->fP1_P2_Dis,LpstrMapCoor->eObjExistProb);
            }
        }

#if 0
        if(LpstrMapCoor->eObjExistProb < OBJ_PROB_LV6)
        {
            if(LpstrMapCoor->fP1_P2_Dis > 100)
            {
                LpstrMapCoor->eObjExistProb = OBJ_PROB_LV6;
            }
        }
#endif

        /* 针对侧边Map重新进入到前后保区域的更新--判断进入前后保之前，还需要判断该线Map是否与车身平齐，平齐则无需切换--2024-04-16 */
        uint8 Lu8NoNeedSwitchTo_F_R_Flag = 0;

        if((LpstrMapCoor->fP1_P2_Dis > 150)&&(LfP1_P2ToCarX_AngleAbs < F_R_MAP_10_ANGLE))
        {
            if(LfMapP1_Y_ABS_Value > (GstrRamSnsCoor[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FLS_RLS].cRadarY+50))
            {
                Lu8NoNeedSwitchTo_F_R_Flag = 1;
            }
        }

        if(!Lu8NoNeedSwitchTo_F_R_Flag)
        {
            if((LpstrMapCoor->eP1_DetailArea > MAP_AREA2)&&(LpstrMapCoor->eP1_DetailArea < MAP_AREA6))
            {
                if(LpstrMapCoor->fP1_ToBumper_Dis < 1400)
                {
                    LpstrMapCoor->u32UpdateSysTime = GdSystemMsTimer;
                    LpstrMapCoor->enuSideMapSns = SIDE_SNS_NONE;
                    LpstrMapCoor->eObjHeightProb = OBJ_PROB_LV5;
                    LpstrMapCoor->u8SideMapToF_R_MapFlag = 1;
                }
            }
            if((LpstrMapCoor->eP2_DetailArea > MAP_AREA2)&&(LpstrMapCoor->eP2_DetailArea < MAP_AREA6))
            {
                if(LpstrMapCoor->fP2_ToBumper_Dis < 1400)
                {
                    LpstrMapCoor->u32UpdateSysTime = GdSystemMsTimer;
                    LpstrMapCoor->enuSideMapSns = SIDE_SNS_NONE;
                    LpstrMapCoor->eObjHeightProb = OBJ_PROB_LV5;
                    LpstrMapCoor->u8SideMapToF_R_MapFlag = 1;
                }
            }
        }


        if(GstrParkingGuidenceData.eParking_Sts == PARKING_SUMMON)
        {
            if((LpstrMapCoor->eP1_DetailArea > MAP_AREA1)&&(LpstrMapCoor->eP1_DetailArea < MAP_AREA7))
            {
                if((LpstrMapCoor->eP2_DetailArea > MAP_AREA1)&&(LpstrMapCoor->eP2_DetailArea < MAP_AREA7))
                {
                    if(LpstrMapCoor->fP2_ToBumper_Dis < 1000)
                    {
                        LpstrMapCoor->u32UpdateSysTime = GdSystemMsTimer;
                        LpstrMapCoor->enuSideMapSns = SIDE_SNS_NONE;
                    }
                }
            }
        }

        /* 垂直与车身，Y偏离较远的删除 */
        if(LfP1_P2ToCarX_AngleAbs > F_R_MAP_45_ANGLE)
        {
            if((LfMapP1_Y_ABS_Value > SIDE_MAP_OUT_ANGLE_DELE_Y)&&(LfMapP2_Y_ABS_Value > SIDE_MAP_OUT_ANGLE_DELE_Y))
            {
                Lu8MapNeedDeleteFlag = 1;
                //VS_PRINT("Time:%.3f,Dele 005,Id:%d\r\n",GfMessageTime,LpstrMapCoor->eMapObjId);
            }
        }
        /* 侧边点状Map的删除策略 */
        if(LpstrMapCoor->fP1_P2_Dis < 50)
        {
            if(LfMapP1_Y_ABS_Value > SIDE_MAP_POINT_OUT_ANGLE_DELE_Y)
            {
                Lu8MapNeedDeleteFlag = 1;
            }
        }
    }

    /* 针对前后保点状Map,在非更新区后，Y值偏离太大的删除策略 */
    if((LpstrMapCoor->enuSideMapSns == SIDE_SNS_NONE)&&(LpstrMapCoor->eMapObjType == OBJ_TYPE_POINT))
    {
        if(LpstrMapCoor->eObjFollowSts != OBJ_DET_UPDATE)
        {
            if(LfMapP1_Y_ABS_Value > F_R_MAP_OUT_Y_DELE_DIS)
            {
                Lu8MapNeedDeleteFlag = 1;
            }
        }
    }

    return Lu8MapNeedDeleteFlag;
}


/******************************************************************************
 * 函数名称: JudgeSideSnsExistMapAndRelation
 * 
 * 功能描述: 判断侧边探头是否已经有存在的Map，存在则使用DE判断与Map的关系，防止重复画Map以及画出的Map不能够删除
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-06 19:08   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static uint8 JudgeSideSnsExistMapAndRelation(ObjMapCoorType *LpstrMapCoor)
{
    float LfFirstX = 0.0;
    float LfSecondX = 0.0;
    float LfP1_X_P2_X_Sub =0;
    float LfAngleAbs = 0.0;
    float LfSnsToMapLineY_Coor;    /* 探头正对应于线的Y坐标 */
    float LfP1_P2_Line_K;
    float LfSns_X_Coor,LfSns_Y_Coor;
    float LfSns_X_P1_X_Sub,LfSns_X_P2_X_Sub;
    uint8 Lu8AngleOKFlag = 0;
    uint16 Lu16SnsMasterDis;
    sint16 Ls16SnsLineY_MasterDisSub = 0;

    float LfSnsPosToObjY_Dis;
    float LfDeleteAngle;
    float LfCarMovDisToDelete;
    uint16 Lu16ObjY_DisAndMasterDisSub = 65000;
    uint16 Lu16CmpareThresholdSet;

    sint16 Ls16P1_Y_AbsValue = 0;
    sint16 Ls16P2_Y_AbsValue = 0;

    sint16 Ls16P1_X_AbsValue = 0;
    sint16 Ls16P2_X_AbsValue = 0;
    uint8 Lu8MapNeedDeleteFlag = 0; 
    uint16 Lu16SnsDeNoMatchMap_Y_DeleDis = 0;
    uint16 Lu16SnsDeNoMatchMapTime;
    uint16 Lu16SnsDeNoMatchMapDis = 0;
    uint8 Lu8NearObjFlag = 0;
    float LfSideMapDisNoMatchDivid = 2;

    if(GstrParkingGuidenceData.eSlotType == SLOT_PARALLEL)
    {
        if(LpstrMapCoor->eMapObjHeight == OBJ_LOW)
        {
            return 0;
        }
    }

    Ls16P1_X_AbsValue = ABS_VALUE(LpstrMapCoor->P1_CarCoor.fObjX);
    Ls16P2_X_AbsValue = ABS_VALUE(LpstrMapCoor->P2_CarCoor.fObjX);

    Ls16P1_Y_AbsValue = ABS_VALUE(LpstrMapCoor->P1_CarCoor.fObjY);
    Ls16P2_Y_AbsValue = ABS_VALUE(LpstrMapCoor->P2_CarCoor.fObjY);

    LfAngleAbs = ABS_VALUE(LpstrMapCoor->fP1_P2ToCarX_Angle);
    
    if(LpstrMapCoor->P1_CarCoor.fObjX < LpstrMapCoor->P2_CarCoor.fObjX)
    {
        LfFirstX = LpstrMapCoor->P1_CarCoor.fObjX;
        LfSecondX = LpstrMapCoor->P2_CarCoor.fObjX;
    }
    else
    {
        LfFirstX = LpstrMapCoor->P2_CarCoor.fObjX;
        LfSecondX = LpstrMapCoor->P1_CarCoor.fObjX;
    }
    LfP1_X_P2_X_Sub = LfSecondX - LfFirstX;

    LfFirstX = LfFirstX+SDW_JUDGE_X_OFFSET;
    LfSecondX = LfSecondX-SDW_JUDGE_X_OFFSET;
    if(LfAngleAbs < F_R_MAP_65_ANGLE)
    {
        if(LpstrMapCoor->fP1_P2_Dis > 2000)
        {
            LfSideMapDisNoMatchDivid = SIDE_MAP_DIS_NO_MATCH_DELE_DIVID_4;
        }
    
        /* 短线处理 */
        if(LpstrMapCoor->eMapObjType == OBJ_TYPE_STRAIGHT2_CORNER)
        {
            if(LfP1_X_P2_X_Sub < SDW_SAMPLE_POINT_DIS)
            {
                /* 单点的删除策略 */
                LfFirstX = LfFirstX-SDW_JUDGE_X_OFFSET;
                LfFirstX = LfFirstX-SDW_JUDGE_X_OFFSET;
                LfSecondX = LfSecondX+SDW_JUDGE_X_OFFSET;
            }
            else if(LfP1_X_P2_X_Sub < 180)
            {
                /* 短线需要采用单独的策略 */
                LfFirstX = LfFirstX-SDW_JUDGE_X_OFFSET;
                LfSecondX = LfSecondX+SDW_JUDGE_X_OFFSET;
            }
        }
        
        /* 此分支是查询FRS RRS探头，Y坐标为负 */
        if((LpstrMapCoor->P1_CarCoor.fObjY < -SDW_SNS_Y_LIMIT_DIS)&&(LpstrMapCoor->P2_CarCoor.fObjY < -SDW_SNS_Y_LIMIT_DIS))
        {
            /* 障碍物在车身的右边，因此只跟FRS和RRS有关系 */
            LfSns_X_Coor = GstrRamSnsCoor[0][5].cRadarX;
            LfSns_Y_Coor = GstrRamSnsCoor[0][5].cRadarY;
            Lu8NearObjFlag = 0;
            if((LfFirstX < LfSns_X_Coor)&&(LfSns_X_Coor < LfSecondX))
            {
                if(LpstrMapCoor->eMapObjType == OBJ_TYPE_STRAIGHT2_CORNER)
                {
                    if(LpstrMapCoor->P1_CarCoor.fObjX > LpstrMapCoor->P2_CarCoor.fObjX)
                    {
                        if((LfSns_X_Coor > LpstrMapCoor->P2_CarCoor.fObjX)&&(LfSns_X_Coor < LpstrMapCoor->P1_CarCoor.fObjX))
                        {
                            LfP1_P2_Line_K = (LpstrMapCoor->P1_CarCoor.fObjY - LpstrMapCoor->P2_CarCoor.fObjY)/(LpstrMapCoor->P1_CarCoor.fObjX - LpstrMapCoor->P2_CarCoor.fObjX);
                            LfP1_P2_Line_K = LfP1_P2_Line_K*(LpstrMapCoor->P1_CarCoor.fObjX - LfSns_X_Coor);
                            LfSnsToMapLineY_Coor = LpstrMapCoor->P1_CarCoor.fObjY - LfP1_P2_Line_K;
                        }
                        else
                        {
                            LfSns_X_P1_X_Sub = ABS(LfSns_X_Coor,LpstrMapCoor->P1_CarCoor.fObjX);
                            LfSns_X_P2_X_Sub = ABS(LfSns_X_Coor,LpstrMapCoor->P2_CarCoor.fObjX);
                            if(LfSns_X_P1_X_Sub < LfSns_X_P2_X_Sub)
                            {
                                LfSnsToMapLineY_Coor = LpstrMapCoor->P1_CarCoor.fObjY;
                            }
                            else
                            {
                                LfSnsToMapLineY_Coor = LpstrMapCoor->P2_CarCoor.fObjY;
                            }
                        }
                    }
                    else
                    {
                        if((LfSns_X_Coor > LpstrMapCoor->P1_CarCoor.fObjX)&&(LfSns_X_Coor < LpstrMapCoor->P2_CarCoor.fObjX))
                        {
                            LfP1_P2_Line_K = (LpstrMapCoor->P1_CarCoor.fObjY - LpstrMapCoor->P2_CarCoor.fObjY)/(LpstrMapCoor->P1_CarCoor.fObjX - LpstrMapCoor->P2_CarCoor.fObjX);
                            LfP1_P2_Line_K = LfP1_P2_Line_K*(LpstrMapCoor->P1_CarCoor.fObjX - LfSns_X_Coor);
                            LfSnsToMapLineY_Coor = LpstrMapCoor->P1_CarCoor.fObjY - LfP1_P2_Line_K;
                        }
                        else
                        {
                            LfSns_X_P1_X_Sub = ABS(LfSns_X_Coor,LpstrMapCoor->P1_CarCoor.fObjX);
                            LfSns_X_P2_X_Sub = ABS(LfSns_X_Coor,LpstrMapCoor->P2_CarCoor.fObjX);
                            if(LfSns_X_P1_X_Sub < LfSns_X_P2_X_Sub)
                            {
                                LfSnsToMapLineY_Coor = LpstrMapCoor->P1_CarCoor.fObjY;
                            }
                            else
                            {
                                LfSnsToMapLineY_Coor = LpstrMapCoor->P2_CarCoor.fObjY;
                            }
                        }
                    }
                    LfSnsToMapLineY_Coor = ABS_VALUE(LfSnsToMapLineY_Coor);

                    LfSnsToMapLineY_Coor = LfSnsToMapLineY_Coor - GstrRamSnsCoor[0][0].cRadarY;
                    
                    Lu16SnsMasterDis = GstrMapSnsRealTimeDis[0][5].u16MasDis;
                    if(Lu16SnsMasterDis < 5000)
                    {
                        Ls16SnsLineY_MasterDisSub = Lu16SnsMasterDis - LfSnsToMapLineY_Coor;
                    }
                    else
                    {
                        Ls16SnsLineY_MasterDisSub = 32767;
                    }
                    if(Ls16SnsLineY_MasterDisSub > SIDE_MAP_JUDGE_SNS_DIS_ON_LINE)
                    {
                        GstrSideAreaMap[SIDE_SNS_FRS].u8NearExistMapFlag = 1;
                    }
                    else
                    {
                        Lu8NearObjFlag = 1;
                    }
                    if(Ls16SnsLineY_MasterDisSub > 0)
                    {
                        if((LfAngleAbs < F_R_MAP_40_ANGLE)&&(LpstrMapCoor->fMapToBumper_Dis < 2500))
                        {
                            if(LfAngleAbs < F_R_MAP_10_ANGLE)
                            {
                                Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_50_CM;
                                if(LpstrMapCoor->fP1_P2_Dis < 1500)
                                {
                                    LfSideMapDisNoMatchDivid = SIDE_MAP_DIS_NO_MATCH_DELE_DIVID_3;
                                }
                                else
                                {
                                    LfSideMapDisNoMatchDivid = SIDE_MAP_DIS_NO_MATCH_DELE_DIVID_4;
                                }
                            }
                            else if(LfAngleAbs < F_R_MAP_20_ANGLE)
                            {
                                if(LfSnsToMapLineY_Coor < 500)
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_50_CM;
                                    LfSideMapDisNoMatchDivid = 4;
                                }
                                else if(LfSnsToMapLineY_Coor < 1000)
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_100_CM;
                                    LfSideMapDisNoMatchDivid = 3;
                                }
                                else
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_150_CM;
                                }
                            }
                            else
                            {
                                if(LfSnsToMapLineY_Coor < 500)
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_50_CM;
                                    LfSideMapDisNoMatchDivid = 3;
                                }
                                else if(LfSnsToMapLineY_Coor < 1000)
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_100_CM;
                                    LfSideMapDisNoMatchDivid = 2;
                                }
                                else if(LfSnsToMapLineY_Coor < 1500)
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_150_CM;
                                }
                                else if(LfSnsToMapLineY_Coor < 2000)
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_200_CM;
                                }
                                else
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_250_CM;
                                }
                            }
                            /* FRS 只在前进的时候画Map，因此倒退状态距离偏小的不更新 */
                            if((GstrSnsCarMovSts[0][5].eCarDir == SNS_CAR_BACKWARD)&&(Ls16SnsLineY_MasterDisSub < 0))
                            {
                                Ls16SnsLineY_MasterDisSub = 0;
                            }
                            else
                            {
                                Ls16SnsLineY_MasterDisSub = ABS_VALUE(Ls16SnsLineY_MasterDisSub);
                            }
                            
                            if(Ls16SnsLineY_MasterDisSub > Lu16SnsDeNoMatchMap_Y_DeleDis)
                            {
                                if(LpstrMapCoor->fSnsFRS_DeNoMatchMapMovDis < 5)
                                {
                                    LpstrMapCoor->fSnsFRS_DeNoMatchMapMovDis = GstrPDCSnsUseOdo.fDrvDis;
                                    Lu16SnsDeNoMatchMapDis = 0;
                                }
                                else
                                {
                                    Lu16SnsDeNoMatchMapDis = GstrPDCSnsUseOdo.fDrvDis - LpstrMapCoor->fSnsFRS_DeNoMatchMapMovDis;
                                }
                                if(GstrSnsCarMovSts[0][5].eCarDir == SNS_CAR_STOP)
                                {
                                    if(GstrMapSnsRealTimeDis[0][5].u8RealDisUpdateFlag)
                                    {
                                        LpstrMapCoor->u8CarStopFRS_MapDeNoMatchCnt++;
                                    }
                                }
                                else
                                {
                                    LpstrMapCoor->u8CarStopFRS_MapDeNoMatchCnt = 0;
                                }
                                if(LpstrMapCoor->u8CarStopFRS_MapDeNoMatchCnt > SIDE_MAP_STOP_DE_NO_MATCH_CNT)
                                {
                                    Lu8MapNeedDeleteFlag = 1; 
                                }
                                if(Lu16SnsDeNoMatchMapDis > (LpstrMapCoor->fP1_P2_Dis/LfSideMapDisNoMatchDivid))
                                {
                                    Lu8MapNeedDeleteFlag = 1; 
                                }
                            }
                            else
                            {
                                LpstrMapCoor->fSnsFRS_DeNoMatchMapMovDis = 0;
                                LpstrMapCoor->u8CarStopFRS_MapDeNoMatchCnt = 0;
                            }
                        }
                        else
                        {
                            LpstrMapCoor->fSnsFRS_DeNoMatchMapMovDis = 0;
                            LpstrMapCoor->u8CarStopFRS_MapDeNoMatchCnt = 0;
                        }
                    }
                    else
                    {
                        LpstrMapCoor->fSnsFRS_DeNoMatchMapMovDis = 0;
                        LpstrMapCoor->u8CarStopFRS_MapDeNoMatchCnt = 0;
                    }
                }
                /* 匹配删除策略待更新 */
            }
            if((!GstrSideAreaMap[SIDE_SNS_FRS].u8NearExistMapFlag)&&(Lu8NearObjFlag == 0))
            {
                LpstrMapCoor->fSnsFRS_DeNoMatchMapMovDis = 0;
                LpstrMapCoor->u8CarStopFRS_MapDeNoMatchCnt = 0;
            }
            
            
            LfSns_X_Coor = GstrRamSnsCoor[1][5].cRadarX;
            LfSns_Y_Coor = GstrRamSnsCoor[1][5].cRadarY;
            Lu8NearObjFlag = 0;
            if((LfFirstX < LfSns_X_Coor)&&(LfSns_X_Coor < LfSecondX))
            {
                if(LpstrMapCoor->eMapObjType == OBJ_TYPE_STRAIGHT2_CORNER)
                {
                    if(LpstrMapCoor->P1_CarCoor.fObjX > LpstrMapCoor->P2_CarCoor.fObjX)
                    {
                        if((LfSns_X_Coor > LpstrMapCoor->P2_CarCoor.fObjX)&&(LfSns_X_Coor < LpstrMapCoor->P1_CarCoor.fObjX))
                        {
                            LfP1_P2_Line_K = (LpstrMapCoor->P1_CarCoor.fObjY - LpstrMapCoor->P2_CarCoor.fObjY)/(LpstrMapCoor->P1_CarCoor.fObjX - LpstrMapCoor->P2_CarCoor.fObjX);
                            LfP1_P2_Line_K = LfP1_P2_Line_K*(LpstrMapCoor->P1_CarCoor.fObjX - LfSns_X_Coor);
                            LfSnsToMapLineY_Coor = LpstrMapCoor->P1_CarCoor.fObjY - LfP1_P2_Line_K;
                        }
                        else
                        {
                            LfSns_X_P1_X_Sub = ABS(LfSns_X_Coor,LpstrMapCoor->P1_CarCoor.fObjX);
                            LfSns_X_P2_X_Sub = ABS(LfSns_X_Coor,LpstrMapCoor->P2_CarCoor.fObjX);
                            if(LfSns_X_P1_X_Sub < LfSns_X_P2_X_Sub)
                            {
                                LfSnsToMapLineY_Coor = LpstrMapCoor->P1_CarCoor.fObjY;
                            }
                            else
                            {
                                LfSnsToMapLineY_Coor = LpstrMapCoor->P2_CarCoor.fObjY;
                            }
                        }
                    }
                    else
                    {
                        if((LfSns_X_Coor > LpstrMapCoor->P1_CarCoor.fObjX)&&(LfSns_X_Coor < LpstrMapCoor->P2_CarCoor.fObjX))
                        {
                            LfP1_P2_Line_K = (LpstrMapCoor->P1_CarCoor.fObjY - LpstrMapCoor->P2_CarCoor.fObjY)/(LpstrMapCoor->P1_CarCoor.fObjX - LpstrMapCoor->P2_CarCoor.fObjX);
                            LfP1_P2_Line_K = LfP1_P2_Line_K*(LpstrMapCoor->P1_CarCoor.fObjX - LfSns_X_Coor);
                            LfSnsToMapLineY_Coor = LpstrMapCoor->P1_CarCoor.fObjY - LfP1_P2_Line_K;
                        }
                        else
                        {
                            LfSns_X_P1_X_Sub = ABS(LfSns_X_Coor,LpstrMapCoor->P1_CarCoor.fObjX);
                            LfSns_X_P2_X_Sub = ABS(LfSns_X_Coor,LpstrMapCoor->P2_CarCoor.fObjX);
                            if(LfSns_X_P1_X_Sub < LfSns_X_P2_X_Sub)
                            {
                                LfSnsToMapLineY_Coor = LpstrMapCoor->P1_CarCoor.fObjY;
                            }
                            else
                            {
                                LfSnsToMapLineY_Coor = LpstrMapCoor->P2_CarCoor.fObjY;
                            }
                        }
                    }
                    LfSnsToMapLineY_Coor = ABS_VALUE(LfSnsToMapLineY_Coor);
                    LfSnsToMapLineY_Coor = LfSnsToMapLineY_Coor - GstrRamSnsCoor[1][0].cRadarY;
                    Lu16SnsMasterDis = GstrMapSnsRealTimeDis[1][5].u16MasDis;
                    if(Lu16SnsMasterDis < 5000)
                    {
                        Ls16SnsLineY_MasterDisSub = Lu16SnsMasterDis - LfSnsToMapLineY_Coor;
                    }
                    else
                    {
                        Ls16SnsLineY_MasterDisSub = 32767;
                    }
                    if(Ls16SnsLineY_MasterDisSub > SIDE_MAP_JUDGE_SNS_DIS_ON_LINE)
                    {
                        GstrSideAreaMap[SIDE_SNS_RRS].u8NearExistMapFlag = 1;
                    }
                    else
                    {
                        Lu8NearObjFlag = 1;
                    }
                    if(Ls16SnsLineY_MasterDisSub > 0)
                    {
                        if((LfAngleAbs < F_R_MAP_40_ANGLE)&&(LpstrMapCoor->fMapToBumper_Dis < 2500))
                        {
                            if(LfAngleAbs < F_R_MAP_10_ANGLE)
                            {
                                Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_50_CM;
                                if(LpstrMapCoor->fP1_P2_Dis < 1500)
                                {
                                    LfSideMapDisNoMatchDivid = SIDE_MAP_DIS_NO_MATCH_DELE_DIVID_3;
                                }
                                else
                                {
                                    LfSideMapDisNoMatchDivid = SIDE_MAP_DIS_NO_MATCH_DELE_DIVID_4;
                                }
                            }
                            else if(LfAngleAbs < F_R_MAP_20_ANGLE)
                            {
                                if(LfSnsToMapLineY_Coor < 500)
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_50_CM;
                                    LfSideMapDisNoMatchDivid = 4;
                                }
                                else if(LfSnsToMapLineY_Coor < 1000)
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_100_CM;
                                    LfSideMapDisNoMatchDivid = 3;
                                }
                                else
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_150_CM;
                                }
                            }
                            else
                            {
                                if(LfSnsToMapLineY_Coor < 500)
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_50_CM;
                                    LfSideMapDisNoMatchDivid = 3;
                                }
                                else if(LfSnsToMapLineY_Coor < 1000)
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_100_CM;
                                    LfSideMapDisNoMatchDivid = 2;
                                }
                                else if(LfSnsToMapLineY_Coor < 1500)
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_150_CM;
                                }
                                else if(LfSnsToMapLineY_Coor < 2000)
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_200_CM;
                                }
                                else
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_250_CM;
                                }
                            }
    
                            /* RRS 只在后退的时候画Map，因此前进状态距离偏小的不更新 */
                            if((GstrSnsCarMovSts[1][5].eCarDir == SNS_CAR_FORWARD)&&(Ls16SnsLineY_MasterDisSub < 0))
                            {
                                Ls16SnsLineY_MasterDisSub = 0;
                            }
                            else
                            {
                                Ls16SnsLineY_MasterDisSub = ABS_VALUE(Ls16SnsLineY_MasterDisSub);
                            }
                            if(Ls16SnsLineY_MasterDisSub > Lu16SnsDeNoMatchMap_Y_DeleDis)
                            {
                                if(LpstrMapCoor->fSnsRRS_DeNoMatchMapMovDis < 5)
                                {
                                    LpstrMapCoor->fSnsRRS_DeNoMatchMapMovDis = GstrPDCSnsUseOdo.fDrvDis;
                                    Lu16SnsDeNoMatchMapDis = 0;
                                }
                                else
                                {
                                    Lu16SnsDeNoMatchMapDis = GstrPDCSnsUseOdo.fDrvDis - LpstrMapCoor->fSnsRRS_DeNoMatchMapMovDis;
                                }
                                if(GstrSnsCarMovSts[1][5].eCarDir == SNS_CAR_STOP)
                                {
                                    if(GstrMapSnsRealTimeDis[1][5].u8RealDisUpdateFlag)
                                    {
                                        LpstrMapCoor->u8CarStopRRS_MapDeNoMatchCnt++;
                                    }
                                }
                                else
                                {
                                    LpstrMapCoor->u8CarStopRRS_MapDeNoMatchCnt = 0;
                                }
#if 0
                                if(LpstrMapCoor->eMapObjId == 0x02)
                                {
                                    VS_PRINT("Time:%.3f,Line Map,Id:%d,RRS_MapDeNoMatchCnt:%d,DisSub:%d,Y_DeleDis:%d,MasDis:%d,LineY_Coor:%.3f\r\n",GfMessageTime,\
                                                LpstrMapCoor->eMapObjId,LpstrMapCoor->u8CarStopRRS_MapDeNoMatchCnt,\
                                                Ls16SnsLineY_MasterDisSub,Lu16SnsDeNoMatchMap_Y_DeleDis,\
                                                GstrMapSnsRealTimeDis[1][5].u16MasDis,LfSnsToMapLineY_Coor);
                                }
#endif
                                if(LpstrMapCoor->u8CarStopRRS_MapDeNoMatchCnt > SIDE_MAP_STOP_DE_NO_MATCH_CNT)
                                {
                                    Lu8MapNeedDeleteFlag = 1; 
                                }
                                if(Lu16SnsDeNoMatchMapDis > (LpstrMapCoor->fP1_P2_Dis/LfSideMapDisNoMatchDivid))
                                {
                                    Lu8MapNeedDeleteFlag = 1; 
                                }
                            }
                            else
                            {
                                LpstrMapCoor->fSnsRRS_DeNoMatchMapMovDis = 0;
                                LpstrMapCoor->u8CarStopRRS_MapDeNoMatchCnt = 0;
                            }
                        }
                        else
                        {
                            LpstrMapCoor->fSnsRRS_DeNoMatchMapMovDis = 0;
                            LpstrMapCoor->u8CarStopRRS_MapDeNoMatchCnt = 0;
                        }
                    }
                    else
                    {
                        LpstrMapCoor->fSnsRRS_DeNoMatchMapMovDis = 0;
                        LpstrMapCoor->u8CarStopRRS_MapDeNoMatchCnt = 0;
                    }
#if 0
                    if((LpstrMapCoor->eMapObjId == 0x01))
                    {
                        VS_PRINT("Time:%.3f,RRS Sns De Match,Id:%d,P1_P2_Dis:%.3f,AngleAbs:%.3f,MasterDis:%d,Y_Coor:%.3f,Sub:%d,SnsDeNoMatchMapDis:%d,Y_DeleDis:%d,Flag:%d,NearObjFlag:%d\r\n",GfMessageTime,\
                            LpstrMapCoor->eMapObjId,LpstrMapCoor->fP1_P2_Dis,\
                            LfAngleAbs*57.3,Lu16SnsMasterDis,LfSnsToMapLineY_Coor,Ls16SnsLineY_MasterDisSub,\
                            Lu16SnsDeNoMatchMapDis,Lu16SnsDeNoMatchMap_Y_DeleDis,Lu8MapNeedDeleteFlag,Lu8NearObjFlag);
                    }
#endif
                }
                /* 匹配删除策略待更新 */
            }
            if((!GstrSideAreaMap[SIDE_SNS_RRS].u8NearExistMapFlag)&&(Lu8NearObjFlag == 0))
            {
                LpstrMapCoor->fSnsRRS_DeNoMatchMapMovDis = 0;
                LpstrMapCoor->u8CarStopRRS_MapDeNoMatchCnt = 0;
            }
            
        }
        else if((LpstrMapCoor->P1_CarCoor.fObjY > SDW_SNS_Y_LIMIT_DIS)&&(LpstrMapCoor->P2_CarCoor.fObjY > SDW_SNS_Y_LIMIT_DIS))
        {
            /* 障碍物在车身的左边，因此只跟FLLS和RLS有关系 */
            LfSns_X_Coor = GstrRamSnsCoor[0][0].cRadarX;
            LfSns_Y_Coor = GstrRamSnsCoor[0][0].cRadarY;
            Lu8NearObjFlag = 0;
            if((LfFirstX < LfSns_X_Coor)&&(LfSns_X_Coor < LfSecondX))
            {
                if(LpstrMapCoor->eMapObjType == OBJ_TYPE_STRAIGHT2_CORNER)
                {
                    if(LpstrMapCoor->P1_CarCoor.fObjX > LpstrMapCoor->P2_CarCoor.fObjX)
                    {
                        if((LfSns_X_Coor > LpstrMapCoor->P2_CarCoor.fObjX)&&(LfSns_X_Coor < LpstrMapCoor->P1_CarCoor.fObjX))
                        {
                            LfP1_P2_Line_K = (LpstrMapCoor->P1_CarCoor.fObjY - LpstrMapCoor->P2_CarCoor.fObjY)/(LpstrMapCoor->P1_CarCoor.fObjX - LpstrMapCoor->P2_CarCoor.fObjX);
                            LfP1_P2_Line_K = LfP1_P2_Line_K*(LpstrMapCoor->P1_CarCoor.fObjX - LfSns_X_Coor);
                            LfSnsToMapLineY_Coor = LpstrMapCoor->P1_CarCoor.fObjY - LfP1_P2_Line_K;
                        }
                        else
                        {
                            LfSns_X_P1_X_Sub = ABS(LfSns_X_Coor,LpstrMapCoor->P1_CarCoor.fObjX);
                            LfSns_X_P2_X_Sub = ABS(LfSns_X_Coor,LpstrMapCoor->P2_CarCoor.fObjX);
                            if(LfSns_X_P1_X_Sub < LfSns_X_P2_X_Sub)
                            {
                                LfSnsToMapLineY_Coor = LpstrMapCoor->P1_CarCoor.fObjY;
                            }
                            else
                            {
                                LfSnsToMapLineY_Coor = LpstrMapCoor->P2_CarCoor.fObjY;
                            }
                        }
                    }
                    else
                    {
                        if((LfSns_X_Coor > LpstrMapCoor->P1_CarCoor.fObjX)&&(LfSns_X_Coor < LpstrMapCoor->P2_CarCoor.fObjX))
                        {
                            LfP1_P2_Line_K = (LpstrMapCoor->P1_CarCoor.fObjY - LpstrMapCoor->P2_CarCoor.fObjY)/(LpstrMapCoor->P1_CarCoor.fObjX - LpstrMapCoor->P2_CarCoor.fObjX);
                            LfP1_P2_Line_K = LfP1_P2_Line_K*(LpstrMapCoor->P1_CarCoor.fObjX - LfSns_X_Coor);
                            LfSnsToMapLineY_Coor = LpstrMapCoor->P1_CarCoor.fObjY - LfP1_P2_Line_K;
                        }
                        else
                        {
                            LfSns_X_P1_X_Sub = ABS(LfSns_X_Coor,LpstrMapCoor->P1_CarCoor.fObjX);
                            LfSns_X_P2_X_Sub = ABS(LfSns_X_Coor,LpstrMapCoor->P2_CarCoor.fObjX);
                            if(LfSns_X_P1_X_Sub < LfSns_X_P2_X_Sub)
                            {
                                LfSnsToMapLineY_Coor = LpstrMapCoor->P1_CarCoor.fObjY;
                            }
                            else
                            {
                                LfSnsToMapLineY_Coor = LpstrMapCoor->P2_CarCoor.fObjY;
                            }
                        }
                    }
                    LfSnsToMapLineY_Coor = ABS_VALUE(LfSnsToMapLineY_Coor);
                    LfSnsToMapLineY_Coor = LfSnsToMapLineY_Coor - GstrRamSnsCoor[0][0].cRadarY;
                    
                    Lu16SnsMasterDis = GstrMapSnsRealTimeDis[0][0].u16MasDis;
                    if(Lu16SnsMasterDis < 5000)
                    {
                        Ls16SnsLineY_MasterDisSub = Lu16SnsMasterDis - LfSnsToMapLineY_Coor;
                    }
                    else
                    {
                        Ls16SnsLineY_MasterDisSub = 32767;
                    }
                    if(Ls16SnsLineY_MasterDisSub > SIDE_MAP_JUDGE_SNS_DIS_ON_LINE)
                    {
                        GstrSideAreaMap[SIDE_SNS_FLS].u8NearExistMapFlag = 1;
                    }
                    else
                    {
                        Lu8NearObjFlag = 1;
                    }
                    if(Ls16SnsLineY_MasterDisSub > 0)
                    {
                        if((LfAngleAbs < F_R_MAP_40_ANGLE)&&(LpstrMapCoor->fMapToBumper_Dis < 2500))
                        {
                            if(LfAngleAbs < F_R_MAP_10_ANGLE)
                            {
                                Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_50_CM;
                                if(LpstrMapCoor->fP1_P2_Dis < 1500)
                                {
                                    LfSideMapDisNoMatchDivid = SIDE_MAP_DIS_NO_MATCH_DELE_DIVID_3;
                                }
                                else
                                {
                                    LfSideMapDisNoMatchDivid = SIDE_MAP_DIS_NO_MATCH_DELE_DIVID_4;
                                }
                            }
                            else if(LfAngleAbs < F_R_MAP_20_ANGLE)
                            {
                                if(LfSnsToMapLineY_Coor < 500)
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_50_CM;
                                    LfSideMapDisNoMatchDivid = 4;
                                }
                                else if(LfSnsToMapLineY_Coor < 1000)
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_100_CM;
                                    LfSideMapDisNoMatchDivid = 3;
                                }
                                else
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_150_CM;
                                }
                            }
                            else
                            {
                                if(LfSnsToMapLineY_Coor < 500)
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_50_CM;
                                    LfSideMapDisNoMatchDivid = 3;
                                }
                                else if(LfSnsToMapLineY_Coor < 1000)
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_100_CM;
                                    LfSideMapDisNoMatchDivid = 2;
                                }
                                else if(LfSnsToMapLineY_Coor < 1500)
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_150_CM;
                                }
                                else if(LfSnsToMapLineY_Coor < 2000)
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_200_CM;
                                }
                                else
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_250_CM;
                                }
                            }
                        
                            /* FLS 只在前进的时候画Map，因此后退状态距离偏小的不更新 */
                            if((GstrSnsCarMovSts[0][0].eCarDir == SNS_CAR_BACKWARD)&&(Ls16SnsLineY_MasterDisSub < 0))
                            {
                                Ls16SnsLineY_MasterDisSub = 0;
                            }
                            else
                            {
                                Ls16SnsLineY_MasterDisSub = ABS_VALUE(Ls16SnsLineY_MasterDisSub);
                            }
                            if(Ls16SnsLineY_MasterDisSub > Lu16SnsDeNoMatchMap_Y_DeleDis)
                            {
                                if(LpstrMapCoor->fSnsFLS_DeNoMatchMapMovDis < 5)
                                {
                                    LpstrMapCoor->fSnsFLS_DeNoMatchMapMovDis = GstrPDCSnsUseOdo.fDrvDis;
                                    Lu16SnsDeNoMatchMapDis = 0;
                                }
                                else
                                {
                                    Lu16SnsDeNoMatchMapDis = GstrPDCSnsUseOdo.fDrvDis - LpstrMapCoor->fSnsFLS_DeNoMatchMapMovDis;
                                }
                        
                                if(GstrSnsCarMovSts[0][0].eCarDir == SNS_CAR_STOP)
                                {
                                    if(GstrMapSnsRealTimeDis[0][0].u8RealDisUpdateFlag)
                                    {
                                        LpstrMapCoor->u8CarStopFLS_MapDeNoMatchCnt++;
                                    }
                                }
                                else
                                {
                                    LpstrMapCoor->u8CarStopFLS_MapDeNoMatchCnt = 0;
                                }
                                if(LpstrMapCoor->u8CarStopFLS_MapDeNoMatchCnt > SIDE_MAP_STOP_DE_NO_MATCH_CNT)
                                {
                                    Lu8MapNeedDeleteFlag = 1; 
                                }
                                if(Lu16SnsDeNoMatchMapDis > (LpstrMapCoor->fP1_P2_Dis/LfSideMapDisNoMatchDivid))
                                {
                                    Lu8MapNeedDeleteFlag = 1; 
                                }
                            }
                            else
                            {
                                LpstrMapCoor->fSnsFLS_DeNoMatchMapMovDis = 0;
                                LpstrMapCoor->u8CarStopFLS_MapDeNoMatchCnt = 0;
                            }
                        }
                        else
                        {
                            LpstrMapCoor->fSnsFLS_DeNoMatchMapMovDis = 0;
                            LpstrMapCoor->u8CarStopFLS_MapDeNoMatchCnt = 0;
                        }
                    }
                    else
                    {
                        LpstrMapCoor->fSnsFLS_DeNoMatchMapMovDis = 0;
                        LpstrMapCoor->u8CarStopFLS_MapDeNoMatchCnt = 0;
                    }
                }
                /* 匹配删除策略待更新 */
            }
            if((!GstrSideAreaMap[SIDE_SNS_FLS].u8NearExistMapFlag)&&(Lu8NearObjFlag == 0))
            {
                LpstrMapCoor->fSnsFLS_DeNoMatchMapMovDis = 0;
                LpstrMapCoor->u8CarStopFLS_MapDeNoMatchCnt = 0;
            }

            
            LfSns_X_Coor = GstrRamSnsCoor[1][0].cRadarX;
            LfSns_Y_Coor = GstrRamSnsCoor[1][0].cRadarY;
            Lu8NearObjFlag = 0;
            if((LfFirstX < LfSns_X_Coor)&&(LfSns_X_Coor < LfSecondX))
            {
                if(LpstrMapCoor->eMapObjType == OBJ_TYPE_STRAIGHT2_CORNER)
                {
                    if(LpstrMapCoor->P1_CarCoor.fObjX > LpstrMapCoor->P2_CarCoor.fObjX)
                    {
                        if((LfSns_X_Coor > LpstrMapCoor->P2_CarCoor.fObjX)&&(LfSns_X_Coor < LpstrMapCoor->P1_CarCoor.fObjX))
                        {
                            LfP1_P2_Line_K = (LpstrMapCoor->P1_CarCoor.fObjY - LpstrMapCoor->P2_CarCoor.fObjY)/(LpstrMapCoor->P1_CarCoor.fObjX - LpstrMapCoor->P2_CarCoor.fObjX);
                            LfP1_P2_Line_K = LfP1_P2_Line_K*(LpstrMapCoor->P1_CarCoor.fObjX - LfSns_X_Coor);
                            LfSnsToMapLineY_Coor = LpstrMapCoor->P1_CarCoor.fObjY - LfP1_P2_Line_K;
                        }
                        else
                        {
                            LfSns_X_P1_X_Sub = ABS(LfSns_X_Coor,LpstrMapCoor->P1_CarCoor.fObjX);
                            LfSns_X_P2_X_Sub = ABS(LfSns_X_Coor,LpstrMapCoor->P2_CarCoor.fObjX);
                            if(LfSns_X_P1_X_Sub < LfSns_X_P2_X_Sub)
                            {
                                LfSnsToMapLineY_Coor = LpstrMapCoor->P1_CarCoor.fObjY;
                            }
                            else
                            {
                                LfSnsToMapLineY_Coor = LpstrMapCoor->P2_CarCoor.fObjY;
                            }
                        }
                    }
                    else
                    {
                        if((LfSns_X_Coor > LpstrMapCoor->P1_CarCoor.fObjX)&&(LfSns_X_Coor < LpstrMapCoor->P2_CarCoor.fObjX))
                        {
                            LfP1_P2_Line_K = (LpstrMapCoor->P1_CarCoor.fObjY - LpstrMapCoor->P2_CarCoor.fObjY)/(LpstrMapCoor->P1_CarCoor.fObjX - LpstrMapCoor->P2_CarCoor.fObjX);
                            LfP1_P2_Line_K = LfP1_P2_Line_K*(LpstrMapCoor->P1_CarCoor.fObjX - LfSns_X_Coor);
                            LfSnsToMapLineY_Coor = LpstrMapCoor->P1_CarCoor.fObjY - LfP1_P2_Line_K;
                        }
                        else
                        {
                            LfSns_X_P1_X_Sub = ABS(LfSns_X_Coor,LpstrMapCoor->P1_CarCoor.fObjX);
                            LfSns_X_P2_X_Sub = ABS(LfSns_X_Coor,LpstrMapCoor->P2_CarCoor.fObjX);
                            if(LfSns_X_P1_X_Sub < LfSns_X_P2_X_Sub)
                            {
                                LfSnsToMapLineY_Coor = LpstrMapCoor->P1_CarCoor.fObjY;
                            }
                            else
                            {
                                LfSnsToMapLineY_Coor = LpstrMapCoor->P2_CarCoor.fObjY;
                            }
                        }
                    }
                    LfSnsToMapLineY_Coor = ABS_VALUE(LfSnsToMapLineY_Coor);
                    LfSnsToMapLineY_Coor = LfSnsToMapLineY_Coor - GstrRamSnsCoor[1][0].cRadarY;
                    
                    Lu16SnsMasterDis = GstrMapSnsRealTimeDis[1][0].u16MasDis;
                    if(Lu16SnsMasterDis < 5000)
                    {
                        Ls16SnsLineY_MasterDisSub = Lu16SnsMasterDis - LfSnsToMapLineY_Coor;
                    }
                    else
                    {
                        Ls16SnsLineY_MasterDisSub = 32767;
                    }
                    if(Ls16SnsLineY_MasterDisSub > SIDE_MAP_JUDGE_SNS_DIS_ON_LINE)
                    {
                        GstrSideAreaMap[SIDE_SNS_RLS].u8NearExistMapFlag = 1;
                    }
                    else
                    {
                        Lu8NearObjFlag = 1;
                    }
                    if(Ls16SnsLineY_MasterDisSub > 0)
                    {
                        if((LfAngleAbs < F_R_MAP_40_ANGLE)&&(LpstrMapCoor->fMapToBumper_Dis < 2500))
                        {
                            if(LfAngleAbs < F_R_MAP_10_ANGLE)
                            {
                                Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_50_CM;
                                if(LpstrMapCoor->fP1_P2_Dis < 1500)
                                {
                                    LfSideMapDisNoMatchDivid = SIDE_MAP_DIS_NO_MATCH_DELE_DIVID_3;
                                }
                                else
                                {
                                    LfSideMapDisNoMatchDivid = SIDE_MAP_DIS_NO_MATCH_DELE_DIVID_4;
                                }
                            }
                            else if(LfAngleAbs < F_R_MAP_20_ANGLE)
                            {
                                if(LfSnsToMapLineY_Coor < 500)
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_50_CM;
                                    LfSideMapDisNoMatchDivid = 4;
                                }
                                else if(LfSnsToMapLineY_Coor < 1000)
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_100_CM;
                                    LfSideMapDisNoMatchDivid = 3;
                                }
                                else
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_150_CM;
                                }
                            }
                            else
                            {
                                if(LfSnsToMapLineY_Coor < 500)
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_50_CM;
                                    LfSideMapDisNoMatchDivid = 3;
                                }
                                else if(LfSnsToMapLineY_Coor < 1000)
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_100_CM;
                                    LfSideMapDisNoMatchDivid = 2;
                                }
                                else if(LfSnsToMapLineY_Coor < 1500)
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_150_CM;
                                }
                                else if(LfSnsToMapLineY_Coor < 2000)
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_200_CM;
                                }
                                else
                                {
                                    Lu16SnsDeNoMatchMap_Y_DeleDis = SIDE_MAP_DIS_NO_MATCH_DELE_Y_250_CM;
                                }
                            }
                        
                            /* RLS 只在后退的时候画Map，因此前进状态距离偏小的不更新 */
                            if((GstrSnsCarMovSts[1][0].eCarDir == SNS_CAR_FORWARD)&&(Ls16SnsLineY_MasterDisSub < 0))
                            {
                                Ls16SnsLineY_MasterDisSub = 0;
                            }
                            else
                            {
                                Ls16SnsLineY_MasterDisSub = ABS_VALUE(Ls16SnsLineY_MasterDisSub);
                            }
                            if(Ls16SnsLineY_MasterDisSub > Lu16SnsDeNoMatchMap_Y_DeleDis)
                            {
                                if(LpstrMapCoor->fSnsRLS_DeNoMatchMapMovDis < 5)
                                {
                                    LpstrMapCoor->fSnsRLS_DeNoMatchMapMovDis = GstrPDCSnsUseOdo.fDrvDis;
                                    Lu16SnsDeNoMatchMapDis = 0;
                                }
                                else
                                {
                                    Lu16SnsDeNoMatchMapDis = GstrPDCSnsUseOdo.fDrvDis - LpstrMapCoor->fSnsRLS_DeNoMatchMapMovDis;
                                }
                        
                                if(GstrSnsCarMovSts[1][0].eCarDir == SNS_CAR_STOP)
                                {
                                    if(GstrMapSnsRealTimeDis[1][0].u8RealDisUpdateFlag)
                                    {
                                        LpstrMapCoor->u8CarStopRLS_MapDeNoMatchCnt++;
                                    }
                                }
                                else
                                {
                                    LpstrMapCoor->u8CarStopRLS_MapDeNoMatchCnt = 0;
                                }
                                if(LpstrMapCoor->u8CarStopRLS_MapDeNoMatchCnt > SIDE_MAP_STOP_DE_NO_MATCH_CNT)
                                {
                                    Lu8MapNeedDeleteFlag = 1; 
                                }
                                if(Lu16SnsDeNoMatchMapDis > (LpstrMapCoor->fP1_P2_Dis/LfSideMapDisNoMatchDivid))
                                {
                                    Lu8MapNeedDeleteFlag = 1; 
                                }
                            }
                            else
                            {
                                LpstrMapCoor->fSnsRLS_DeNoMatchMapMovDis = 0;
                                LpstrMapCoor->u8CarStopRLS_MapDeNoMatchCnt = 0;
                            }
                        }
                        else
                        {
                            LpstrMapCoor->fSnsRLS_DeNoMatchMapMovDis = 0;
                            LpstrMapCoor->u8CarStopRLS_MapDeNoMatchCnt = 0;
                        }
                    }
                    else
                    {
                        LpstrMapCoor->fSnsRLS_DeNoMatchMapMovDis = 0;
                        LpstrMapCoor->u8CarStopRLS_MapDeNoMatchCnt = 0;
                    }
#if 0
                    if((LpstrMapCoor->eMapObjId == 0x00)&&(GfMessageTime > 8)&&(GfMessageTime < 11))
                    {
                        VS_PRINT("Time:%.3f,RLS Sns De Match,Id:%d,P1_P2_Dis:%.3f,AngleAbs:%.3f,MasterDis:%d,Y_Coor:%.3f,Sub:%d,SnsDeNoMatchMapDis:%d,Y_DeleDis:%d,NoMatchDivid:%.2f\r\n",GfMessageTime,\
                            LpstrMapCoor->eMapObjId,LpstrMapCoor->fP1_P2_Dis,LfAngleAbs*57.3,Lu16SnsMasterDis,LfSnsToMapLineY_Coor,\
                            Ls16SnsLineY_MasterDisSub,Lu16SnsDeNoMatchMapDis,Lu16SnsDeNoMatchMap_Y_DeleDis,LfSideMapDisNoMatchDivid);
                    }
#endif
                    
                }
                /* 匹配删除策略待更新 */
            }
            if((!GstrSideAreaMap[SIDE_SNS_RLS].u8NearExistMapFlag)&&(Lu8NearObjFlag == 0))
            {
                LpstrMapCoor->fSnsRLS_DeNoMatchMapMovDis = 0;
                LpstrMapCoor->u8CarStopRLS_MapDeNoMatchCnt = 0;
            }
        }
#if 0
        if(Lu8MapNeedDeleteFlag)
        {
            VS_PRINT("Point Dele 001,Time:%.3f,Id:%d,P1_X:%.3f,P1_Y:%.3f\r\n",GfMessageTime,LpstrMapCoor->eMapObjId,\
                LpstrMapCoor->P1_CarCoor.fObjX,LpstrMapCoor->P1_CarCoor.fObjY);
        }
#endif
    }


    /* 判断Map点是否在侧边探头的FOV 内,对侧边入侵Map进行裁剪处理 */
    LpstrMapCoor->enuMapPointInSideSnsFov = SIDE_SNS_NONE;
    uint8 Lu8CutCondituonFlag = 0;
    /* 防止点状Map不更新 */
    if(LpstrMapCoor->fP1_P2_Dis < 50)
    {
        LfAngleAbs = F_R_MAP_40_ANGLE;
    }
    if((LfAngleAbs > F_R_MAP_35_ANGLE)&&(LpstrMapCoor->enuSideMapUpdateSts == SIDE_MAP_MEMORY_P1_MEMORY_P2))
    {
        if(LpstrMapCoor->fMapToBumper_Dis < 1500)
        {
            Lu8CutCondituonFlag = 1;
        }
    }
    if(Lu8CutCondituonFlag)
    {
        uint8 Lu8MapNeedCutFlag = 0;
        uint8 Lu8SnsGroup,Lu8SnsCh;
        uint16 Lu16SideSnsDeDis;
        float LfNewPointX_Coor,LfNewPointY_Coor;
        float LfX_CoorSub,LfY_CoorSub;
        float LfLineDividValue;
        float LfCutDisSet = 400;
        uint8 Lu8SnsMovDir;
        uint16 Lu16CutMinY_Abs;

        if(LpstrMapCoor->u8MapBeCutCnt == 0)
        {
            Lu16CutMinY_Abs = F_R_MAP_CUT_MIN_Y_ABS_DIS0;
        }
        else if(LpstrMapCoor->u8MapBeCutCnt == 1)
        {
            Lu16CutMinY_Abs = F_R_MAP_CUT_MIN_Y_ABS_DIS1;
        }
        else if(LpstrMapCoor->u8MapBeCutCnt == 2)
        {
            Lu16CutMinY_Abs = F_R_MAP_CUT_MIN_Y_ABS_DIS2;
        }
        else
        {
            Lu16CutMinY_Abs = F_R_MAP_CUT_MIN_Y_ABS_DIS3;
        }
        
        if(Ls16P1_Y_AbsValue < Ls16P2_Y_AbsValue)
        {
            if(Ls16P1_Y_AbsValue < (GstrRamSnsCoor[0][0].cRadarY+Lu16CutMinY_Abs))
            {
                if((LpstrMapCoor->P1_CarCoor.fObjX > (GstrRamSnsCoor[0][0].cRadarX - SIDE_MAP_POINT_IN_FOV_DIS))&&\
                    (LpstrMapCoor->P1_CarCoor.fObjX < (GstrRamSnsCoor[0][0].cRadarX + SIDE_MAP_POINT_IN_FOV_DIS)))
                {
                    if(LfAngleAbs > F_R_MAP_60_ANGLE)
                    {
                        if(LpstrMapCoor->P1_CarCoor.fObjY > 0)
                        {
                            LpstrMapCoor->enuMapPointInSideSnsFov = SIDE_SNS_FLS;
                            Lu8SnsGroup = 0;
                            Lu8SnsCh = 0;
                        }
                        else
                        {
                            LpstrMapCoor->enuMapPointInSideSnsFov = SIDE_SNS_FRS;
                            Lu8SnsGroup = 0;
                            Lu8SnsCh = 5;
                        }
                    }
                }
                if((LpstrMapCoor->P1_CarCoor.fObjX > (GstrRamSnsCoor[1][0].cRadarX - SIDE_MAP_POINT_IN_FOV_DIS))&&\
                    (LpstrMapCoor->P1_CarCoor.fObjX < (GstrRamSnsCoor[1][0].cRadarX + SIDE_MAP_POINT_IN_FOV_DIS)))
                {
                    if(LpstrMapCoor->P1_CarCoor.fObjY > 0)
                    {
                        LpstrMapCoor->enuMapPointInSideSnsFov = SIDE_SNS_RLS;
                        Lu8SnsGroup = 1;
                        Lu8SnsCh = 0;
                    }
                    else
                    {
                        LpstrMapCoor->enuMapPointInSideSnsFov = SIDE_SNS_RRS;
                        Lu8SnsGroup = 1;
                        Lu8SnsCh = 5;
                    }
                }
            }

            if(LpstrMapCoor->enuMapPointInSideSnsFov != SIDE_SNS_NONE)
            {
                Lu16SideSnsDeDis = GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh].u16MasDis;
                if(GstrSnsCarMovSts[Lu8SnsGroup][Lu8SnsCh].eCarDir != SNS_CAR_STOP)
                {
                    if(Lu16SideSnsDeDis > 2000)
                    {
                        Lu8MapNeedCutFlag = 2;
                        LfCutDisSet = 450;
                    }
                    else
                    {
                        Lu16SideSnsDeDis = Lu16SideSnsDeDis + GstrRamSnsCoor[Lu8SnsGroup][0].cRadarY;
                        if(LpstrMapCoor->fP1_P2_Dis > F_R_MAP_CUT_MIN_P1_P2_DIS)
                        {
                            Lu16SideSnsDeDis += 100;
                            if(Lu16SideSnsDeDis > Ls16P1_Y_AbsValue)
                            {
                                Lu8MapNeedCutFlag = 1;
                            }
                        }
                        else
                        {
                            if(Lu16SideSnsDeDis > (Ls16P1_Y_AbsValue+100))
                            {
                                Lu8MapNeedCutFlag = 1;
                            }
                        }

                        if(LpstrMapCoor->fP1_P2_Dis > F_R_MAP_CUT_LONG_P1_P2_DIS)
                        {
                            LfCutDisSet = 450;
                        }
                        else
                        {
                            LfCutDisSet = 400;
                        }
                    }
                }

                if((Lu8MapNeedCutFlag)&&(!LpstrMapCoor->u8MapBeCutFlag))
                {
                    if(LpstrMapCoor->fP1_P2_Dis > F_R_MAP_CUT_MIN_P1_P2_DIS)
                    {
                        if(GstrSnsCarMovSts[Lu8SnsGroup][Lu8SnsCh].eCarDir == SNS_CAR_FORWARD)
                        {
                            LpstrMapCoor->u8MapBeCutFlag = 1;
                        }
                        else
                        {
                            LpstrMapCoor->u8MapBeCutFlag = 2;
                        }
                        LpstrMapCoor->u8MapBeCutCnt++;
                        
                        LpstrMapCoor->eObjHeightProb = OBJ_PROB_LV6;
                        LfLineDividValue = LfCutDisSet/LpstrMapCoor->fP1_P2_Dis;
                        LfX_CoorSub = LpstrMapCoor->P1_CarCoor.fObjX - LpstrMapCoor->P2_CarCoor.fObjX;
                        LfY_CoorSub = LpstrMapCoor->P1_CarCoor.fObjY - LpstrMapCoor->P2_CarCoor.fObjY;
                        LfNewPointX_Coor = LfLineDividValue*LfX_CoorSub;
                        LfNewPointX_Coor = LpstrMapCoor->P1_CarCoor.fObjX - LfNewPointX_Coor;
                        LfNewPointY_Coor = LfLineDividValue*LfY_CoorSub;
                        LfNewPointY_Coor = LpstrMapCoor->P1_CarCoor.fObjY - LfNewPointY_Coor;
                        LpstrMapCoor->P1_CarCoor.fObjX = LfNewPointX_Coor;
                        LpstrMapCoor->P1_CarCoor.fObjY = LfNewPointY_Coor;
                        PubAI_TransObjCarCoorToOdoCoor(&LpstrMapCoor->P1_CarCoor.fObjX,&LpstrMapCoor->P1_CarCoor.fObjY,\
                            &LpstrMapCoor->P1_OdoCoor.fObjX,&LpstrMapCoor->P1_OdoCoor.fObjY);
                    }
                    else
                    {
                        if(LpstrMapCoor->fMapToBumper_Dis < SIDE_MAP_POINT_DE_NO_MATCH_DELE_DIS)
                        {
                            Lu8MapNeedDeleteFlag = 1;
#if 0
                            VS_PRINT("Point Dele 002,Time:%.3f,Id:%d,P1_X:%.3f,P1_Y:%.3f\r\n",GfMessageTime,LpstrMapCoor->eMapObjId,\
                                LpstrMapCoor->P1_CarCoor.fObjX,LpstrMapCoor->P1_CarCoor.fObjY);
#endif
                        }
                    }
                }
                else
                {
                    if(Lu8MapNeedCutFlag)
                    {
                        Lu8SnsMovDir = (uint8)GstrSnsCarMovSts[Lu8SnsGroup][Lu8SnsCh].eCarDir;
                        if(LpstrMapCoor->u8MapBeCutFlag != Lu8SnsMovDir)
                        {
                            LpstrMapCoor->u8MapBeCutFlag = 0;
                        }
                    }
                }
            }
        }
        else
        {
            if(Ls16P2_Y_AbsValue < (GstrRamSnsCoor[0][0].cRadarY+Lu16CutMinY_Abs))
            {
                if((LpstrMapCoor->P2_CarCoor.fObjX > (GstrRamSnsCoor[0][0].cRadarX - SIDE_MAP_POINT_IN_FOV_DIS))&&\
                    (LpstrMapCoor->P2_CarCoor.fObjX < (GstrRamSnsCoor[0][0].cRadarX + SIDE_MAP_POINT_IN_FOV_DIS)))
                {
                    if(LfAngleAbs > F_R_MAP_60_ANGLE)
                    {
                        if(LpstrMapCoor->P2_CarCoor.fObjY > 0)
                        {
                            LpstrMapCoor->enuMapPointInSideSnsFov = SIDE_SNS_FLS;
                            Lu8SnsGroup = 0;
                            Lu8SnsCh = 0;
                        }
                        else
                        {
                            LpstrMapCoor->enuMapPointInSideSnsFov = SIDE_SNS_FRS;
                            Lu8SnsGroup = 0;
                            Lu8SnsCh = 5;
                        }
                    }
                }
                if((LpstrMapCoor->P2_CarCoor.fObjX > (GstrRamSnsCoor[1][0].cRadarX - SIDE_MAP_POINT_IN_FOV_DIS))&&\
                    (LpstrMapCoor->P2_CarCoor.fObjX < (GstrRamSnsCoor[1][0].cRadarX + SIDE_MAP_POINT_IN_FOV_DIS)))
                {
                    if(LpstrMapCoor->P2_CarCoor.fObjY > 0)
                    {
                        LpstrMapCoor->enuMapPointInSideSnsFov = SIDE_SNS_RLS;
                        Lu8SnsGroup = 1;
                        Lu8SnsCh = 0;
                    }
                    else
                    {
                        LpstrMapCoor->enuMapPointInSideSnsFov = SIDE_SNS_RRS;
                        Lu8SnsGroup = 1;
                        Lu8SnsCh = 5;
                    }
                }
            }
#if 0
            if((LpstrMapCoor->eMapObjId == 0x00)&&(GfMessageTime > 37)&&(GfMessageTime < 43))
            {
                if(GstrMapSnsRealTimeDis[1][5].u8RealDisUpdateFlag)
                {
                    VS_PRINT("Side Dele,Time:%.2f,Id:%d,Angle:%.2f,BumperDis:%.2f,P1_X:%.2f,P1_Y:%.2f,P2_X:%.2f,P2_Y:%.2f,MasDis:%d,Fov:%d\r\n",GfMessageTime,LpstrMapCoor->eMapObjId,\
                        LpstrMapCoor->fP1_P2ToCarX_Angle*57.3,LpstrMapCoor->fMapToBumper_Dis,\
                        LpstrMapCoor->P1_CarCoor.fObjX,LpstrMapCoor->P1_CarCoor.fObjY,\
                        LpstrMapCoor->P2_CarCoor.fObjX,LpstrMapCoor->P2_CarCoor.fObjY,\
                        GstrMapSnsRealTimeDis[1][5].u16MasDis,LpstrMapCoor->enuMapPointInSideSnsFov);
                }
            }
#endif
            if(LpstrMapCoor->enuMapPointInSideSnsFov != SIDE_SNS_NONE)
            {
                Lu16SideSnsDeDis = GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh].u16MasDis;
                if(GstrSnsCarMovSts[Lu8SnsGroup][Lu8SnsCh].eCarDir != SNS_CAR_STOP)
                {
                    if(Lu16SideSnsDeDis > 2000)
                    {
                        Lu8MapNeedCutFlag = 2;
                        LfCutDisSet = 450;
                    }
                    else
                    {
                        Lu16SideSnsDeDis = Lu16SideSnsDeDis + GstrRamSnsCoor[Lu8SnsGroup][0].cRadarY;

                        if(LpstrMapCoor->fP1_P2_Dis > F_R_MAP_CUT_MIN_P1_P2_DIS)
                        {
                            Lu16SideSnsDeDis += 100;
                            if(Lu16SideSnsDeDis > Ls16P2_Y_AbsValue)
                            {
                                Lu8MapNeedCutFlag = 1;
                            }
                        }
                        else
                        {
                            if(Lu16SideSnsDeDis > (Ls16P2_Y_AbsValue+100))
                            {
                                Lu8MapNeedCutFlag = 1;
                            }
                        }

                        if(LpstrMapCoor->fP1_P2_Dis > F_R_MAP_CUT_LONG_P1_P2_DIS)
                        {
                            LfCutDisSet = 450;
                        }
                        else
                        {
                            LfCutDisSet = 400;
                        }
                    }
                }
                
                if((Lu8MapNeedCutFlag)&&(!LpstrMapCoor->u8MapBeCutFlag))
                {
                    if(LpstrMapCoor->fP1_P2_Dis > F_R_MAP_CUT_MIN_P1_P2_DIS)
                    {
                        if(GstrSnsCarMovSts[Lu8SnsGroup][Lu8SnsCh].eCarDir == SNS_CAR_FORWARD)
                        {
                            LpstrMapCoor->u8MapBeCutFlag = 1;
                        }
                        else
                        {
                            LpstrMapCoor->u8MapBeCutFlag = 2;
                        }
                        LpstrMapCoor->u8MapBeCutCnt++;
                        LpstrMapCoor->eObjHeightProb = OBJ_PROB_LV6;
                        LfLineDividValue = LfCutDisSet/LpstrMapCoor->fP1_P2_Dis;
                        LfX_CoorSub = LpstrMapCoor->P2_CarCoor.fObjX - LpstrMapCoor->P1_CarCoor.fObjX;
                        LfY_CoorSub = LpstrMapCoor->P2_CarCoor.fObjY - LpstrMapCoor->P1_CarCoor.fObjY;
                        LfNewPointX_Coor = LfLineDividValue*LfX_CoorSub;
                        LfNewPointX_Coor = LpstrMapCoor->P2_CarCoor.fObjX - LfNewPointX_Coor;
                        LfNewPointY_Coor = LfLineDividValue*LfY_CoorSub;
                        LfNewPointY_Coor = LpstrMapCoor->P2_CarCoor.fObjY - LfNewPointY_Coor;
                        LpstrMapCoor->P2_CarCoor.fObjX = LfNewPointX_Coor;
                        LpstrMapCoor->P2_CarCoor.fObjY = LfNewPointY_Coor;
                        PubAI_TransObjCarCoorToOdoCoor(&LpstrMapCoor->P2_CarCoor.fObjX,&LpstrMapCoor->P2_CarCoor.fObjY,\
                            &LpstrMapCoor->P2_OdoCoor.fObjX,&LpstrMapCoor->P2_OdoCoor.fObjY);
                    }
                    else
                    {
                        if(LpstrMapCoor->fMapToBumper_Dis < SIDE_MAP_POINT_DE_NO_MATCH_DELE_DIS)//400
                        {
                            Lu8MapNeedDeleteFlag = 1;
#if 0
                            VS_PRINT("Point Dele 003,Time:%.3f,Id:%d,P1_X:%.3f,P1_Y:%.3f,RealTimeDis:%d\r\n",GfMessageTime,LpstrMapCoor->eMapObjId,\
                                LpstrMapCoor->P1_CarCoor.fObjX,LpstrMapCoor->P1_CarCoor.fObjY,\
                                GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh].u16MasDis);
#endif
                        }
                    }
                }
                else
                {
                    if(Lu8MapNeedCutFlag)
                    {
                        Lu8SnsMovDir = (uint8)GstrSnsCarMovSts[Lu8SnsGroup][Lu8SnsCh].eCarDir;
                        if(LpstrMapCoor->u8MapBeCutFlag != Lu8SnsMovDir)
                        {
                            LpstrMapCoor->u8MapBeCutFlag = 0;
                        }
                    }
                }
            }
        }
    }

    /* 针对泊车基本入库后的轻微入侵Map进行修剪策略 */
    if((GstrParkingGuidenceData.fCarAngleSub > F_R_MAP_65_ANGLE)&&(LpstrMapCoor->enuSideMapUpdateSts == SIDE_MAP_MEMORY_P1_MEMORY_P2))
    {
        float LfMin_Y_Point_X_Coor,LfMin_Y_Point_Y_Coor;
        uint8 Lu8NeedCutPointFlag = 0;
        float Lf16RealMasterDis_Y_Sub = 0;
        float LfMin_Y_Point_Y_CoorTemp;
        if(GstrMapSnsRealTimeDis[1][0].u16MasDis < SIDE_MAP_ONE_POINT_CUT_DIS)
        {
            if(Ls16P1_Y_AbsValue < Ls16P2_Y_AbsValue)
            {
                LfMin_Y_Point_X_Coor = LpstrMapCoor->P1_CarCoor.fObjX;
                LfMin_Y_Point_Y_Coor = LpstrMapCoor->P1_CarCoor.fObjY;
                if(Ls16P1_Y_AbsValue < (SIDE_MAP_ONE_POINT_CUT_DIS+GstrRamSnsCoor[1][0].cRadarY))
                {
                    Lu8NeedCutPointFlag = 1;
                }
            }
            else
            {
                LfMin_Y_Point_X_Coor = LpstrMapCoor->P2_CarCoor.fObjX;
                LfMin_Y_Point_Y_Coor = LpstrMapCoor->P2_CarCoor.fObjY;
                if(Ls16P2_Y_AbsValue < (SIDE_MAP_ONE_POINT_CUT_DIS+GstrRamSnsCoor[1][0].cRadarY))
                {
                    Lu8NeedCutPointFlag = 2;
                }
            }
        }

        if(Lu8NeedCutPointFlag)
        {
            if((LfMin_Y_Point_X_Coor > (GstrRamSnsCoor[1][0].cRadarX - SIDE_MAP_LINE_ONE_POINT_IN_FOV_DIS))&&\
                (LfMin_Y_Point_X_Coor < (GstrRamSnsCoor[1][0].cRadarX + SIDE_MAP_LINE_ONE_POINT_IN_FOV_DIS)))
            {
                if(LfMin_Y_Point_Y_Coor > 0)
                {
                    if(LfMin_Y_Point_Y_Coor < (GstrMapSnsRealTimeDis[1][0].u16MasDis+GstrRamSnsCoor[1][0].cRadarY))
                    {
                        Lf16RealMasterDis_Y_Sub = GstrMapSnsRealTimeDis[1][0].u16MasDis+GstrRamSnsCoor[1][0].cRadarY;
                        Lf16RealMasterDis_Y_Sub = Lf16RealMasterDis_Y_Sub - LfMin_Y_Point_Y_Coor;
                    }
#if 0
                    if((LpstrMapCoor->eMapObjId == 0x02)&&(GfMessageTime > 35)&&(GfMessageTime < 45.5))
                    {
                        VS_PRINT("Time:%.3f,RLS Sns De Match Line Point,Id:%d,CarAngleSub:%.3f,MasterDis:%d,Y_Coor:%.3f,Sub:%.2f,Map_X_Coor:%.2f\r\n",GfMessageTime,\
                            LpstrMapCoor->eMapObjId,GstrParkingGuidenceData.fCarAngleSub*57.3,\
                            GstrMapSnsRealTimeDis[1][0].u16MasDis,LfMin_Y_Point_Y_Coor,Lf16RealMasterDis_Y_Sub,LfMin_Y_Point_X_Coor);
                    }
#endif
                }
                else
                {
                    LfMin_Y_Point_Y_CoorTemp = -LfMin_Y_Point_Y_Coor;
                    if(LfMin_Y_Point_Y_CoorTemp < (GstrMapSnsRealTimeDis[1][5].u16MasDis+GstrRamSnsCoor[1][0].cRadarY))
                    {
                        Lf16RealMasterDis_Y_Sub = GstrMapSnsRealTimeDis[1][5].u16MasDis+GstrRamSnsCoor[1][0].cRadarY;
                        Lf16RealMasterDis_Y_Sub = Lf16RealMasterDis_Y_Sub - LfMin_Y_Point_Y_CoorTemp;
                    }
                }
                if((Lf16RealMasterDis_Y_Sub > SIDE_MAP_ONE_POINT_CUT_Y_SUB)&&(Lf16RealMasterDis_Y_Sub < 200))
                {
                    if(LfMin_Y_Point_Y_Coor > 0)
                    {
                        LfMin_Y_Point_Y_Coor = LfMin_Y_Point_Y_Coor + Lf16RealMasterDis_Y_Sub;
                    }
                    else
                    {
                        LfMin_Y_Point_Y_Coor = LfMin_Y_Point_Y_Coor - Lf16RealMasterDis_Y_Sub;
                    }
                    if(Lu8NeedCutPointFlag == 1)
                    {
                        /* 矫正P1 点 */
                        LpstrMapCoor->P1_CarCoor.fObjX = LfMin_Y_Point_X_Coor;
                        LpstrMapCoor->P1_CarCoor.fObjY = LfMin_Y_Point_Y_Coor;
                        PubAI_TransObjCarCoorToOdoCoor(&LpstrMapCoor->P1_CarCoor.fObjX,&LpstrMapCoor->P1_CarCoor.fObjY,\
                            &LpstrMapCoor->P1_OdoCoor.fObjX,&LpstrMapCoor->P1_OdoCoor.fObjY);
                    }
                    else
                    {
                        /* 矫正P2 点 */
#if 0
                        VS_PRINT("Line Map Near Cut Point,Time:%.3f,Before:%.2f,%.2f,After:%.2f,%.2f\r\n",GfMessageTime,\
                            LpstrMapCoor->P2_CarCoor.fObjX,LpstrMapCoor->P2_CarCoor.fObjY,\
                            LfMin_Y_Point_X_Coor,LfMin_Y_Point_Y_Coor);
#endif
                        LpstrMapCoor->P2_CarCoor.fObjX = LfMin_Y_Point_X_Coor;
                        LpstrMapCoor->P2_CarCoor.fObjY = LfMin_Y_Point_Y_Coor;
                        PubAI_TransObjCarCoorToOdoCoor(&LpstrMapCoor->P2_CarCoor.fObjX,&LpstrMapCoor->P2_CarCoor.fObjY,\
                            &LpstrMapCoor->P2_OdoCoor.fObjX,&LpstrMapCoor->P2_OdoCoor.fObjY);
                    }
                }
            }
        }
    }
    
#if 0
    if((LpstrMapCoor->eMapObjId == 0x01)&&(GstrMapSnsRealTimeDis[0][5].u8RealDisUpdateFlag))
    {
#if 0
        if((GfMessageTime > 45))
        {
            VS_PRINT("Map To X Angle,Time:%.3f,Id:%d,X_Angle:%.3f,Car_StrAngle:%d\r\n",GfMessageTime,\
                LpstrMapCoor->eMapObjId,LpstrMapCoor->fP1_P2ToCarX_Angle*57.3,\
                Gs16CarStrAngleForSnsUse);
        }
#endif
#if 1
        if((GfMessageTime > 45))
        {
            VS_PRINT("Park Sts Update,Time:%.3f,Id:%d,CarAngleSub:%.3f,CarAngleSubBack:%.3f,Car_StrAngle:%d,SlotType:%d,ParkDir:%d\r\n",GfMessageTime,\
                LpstrMapCoor->eMapObjId,GstrParkingGuidenceData.fCarAngleSub*57.3,GstrParkingGuidenceData.fCarAngleSubBackup*57.3,\
                Gs16CarStrAngleForSnsUse,GstrParkingGuidenceData.eSlotType,GstrParkingGuidenceData.eParkDir);
        }
#endif
    }
#endif
    if(GstrParkingGuidenceData.eSlotType == SLOT_PARALLEL)
    {
        uint16 Lu16SideDE_AndToMapDisSub = 65535;
        uint16 Lu16Sns_ToMapDis = 65535;
        uint8 Lu8HighIndex = 0;
        const SnsMapCalibHeightType *LptrThreTable;
        if(LpstrMapCoor->eMapObjHeight == OBJ_LOW)
        {
            if(GstrParkingGuidenceData.eParkDir == PARK_DIR_LEFT)
            {
                if(Lu8MapNeedDeleteFlag)
                {
                    if((LpstrMapCoor->P1_CarCoor.fObjY > GstrRamSnsCoor[1][0].cRadarY)&&\
                        (LpstrMapCoor->P2_CarCoor.fObjY > GstrRamSnsCoor[1][0].cRadarY))
                    {
                        Lu8MapNeedDeleteFlag = 0;
                    }
                }
                if(GstrMapSnsRealTimeDis[1][0].u8RealDisUpdateFlag)
                {
                    if((LpstrMapCoor->P1_CarCoor.fObjY > GstrRamSnsCoor[1][0].cRadarY)||\
                        (LpstrMapCoor->P2_CarCoor.fObjY > GstrRamSnsCoor[1][0].cRadarY))
                    {
                        if(LpstrMapCoor->P1_CarCoor.fObjX < LpstrMapCoor->P2_CarCoor.fObjX)
                        {
                            LfFirstX = LpstrMapCoor->P1_CarCoor.fObjX;
                            LfSecondX = LpstrMapCoor->P2_CarCoor.fObjX;
                        }
                        else
                        {
                            LfFirstX = LpstrMapCoor->P2_CarCoor.fObjX;
                            LfSecondX = LpstrMapCoor->P1_CarCoor.fObjX;
                        }
                        LfSns_X_Coor = GstrRamSnsCoor[1][0].cRadarX;
                        if((LfFirstX < LfSns_X_Coor)&&(LfSns_X_Coor < LfSecondX))
                        {
                            LfP1_P2_Line_K = (LpstrMapCoor->P1_CarCoor.fObjY - LpstrMapCoor->P2_CarCoor.fObjY)/(LpstrMapCoor->P1_CarCoor.fObjX - LpstrMapCoor->P2_CarCoor.fObjX);
                            LfP1_P2_Line_K = LfP1_P2_Line_K*(LpstrMapCoor->P1_CarCoor.fObjX - LfSns_X_Coor);
                            LfSnsToMapLineY_Coor = LpstrMapCoor->P1_CarCoor.fObjY - LfP1_P2_Line_K;
                            LfSnsToMapLineY_Coor = ABS_VALUE(LfSnsToMapLineY_Coor);
                            Lu16Sns_ToMapDis = LfSnsToMapLineY_Coor - GstrRamSnsCoor[1][0].cRadarY;
                            Lu16SideDE_AndToMapDisSub = ABS(Lu16Sns_ToMapDis,GstrMapSnsRealTimeDis[1][0].u16MasDis);
                        }
                    }
                    if(Lu16Sns_ToMapDis < 1300)
                    {
                        if(Lu16SideDE_AndToMapDisSub < 250)
                        {
                            if(GstrMapSnsRealTimeDis[1][0].u16MasDis < MAP_TABLE_MAX_DIS)
                            {
                                Lu8HighIndex = GstrMapSnsRealTimeDis[1][0].u16MasDis/MAP_HIGH_TABLE_STEP;
                            }
                            else
                            {
                                Lu8HighIndex = SNS_MAP_DIS_HIGH_400cm;
                            }
                        
                            if(GstrMapSnsRealTimeDis[1][0].eMeasType == PDC_SNS_MEAS_STD)
                            {
                                LptrThreTable = &GStrMapObjJudgeStdThresTableForSideMapInRAM[0];
                            }
                            else
                            {
                                LptrThreTable = &GStrMapObjJudgeChirpThresTableForSideMapInRAM[0];
                            }
                            if(GstrMapSnsRealTimeDis[1][0].u16MasterHeight > LptrThreTable[Lu8HighIndex].u16BigWallHeight)
                            {
                                Lu8HighIndex = GstrMapSnsRealTimeDis[1][0].u16MasterHeight/LptrThreTable[Lu8HighIndex].u16BigWallHeight;
                                LpstrMapCoor->u8SideMapBigWallCnt += Lu8HighIndex;
                            }
                        }
                        else
                        {
                            LpstrMapCoor->u8SideMapBigWallCnt = 0;
                        }
                    }
                    else
                    {
                        LpstrMapCoor->u8SideMapBigWallCnt = 0;
                    }
                    if(LpstrMapCoor->u8SideMapBigWallCnt > 6)
                    {
                        LpstrMapCoor->eMapObjHeight = OBJ_HIGH;
                    }
                }
            }
            else if(GstrParkingGuidenceData.eParkDir == PARK_DIR_RIGHT)
            {
                if(Lu8MapNeedDeleteFlag)
                {
                    if((LpstrMapCoor->P1_CarCoor.fObjY < GstrRamSnsCoor[1][5].cRadarY)&&\
                        (LpstrMapCoor->P2_CarCoor.fObjY < GstrRamSnsCoor[1][5].cRadarY))
                    {
                        Lu8MapNeedDeleteFlag = 0;
                    }
                }
                if(GstrMapSnsRealTimeDis[1][5].u8RealDisUpdateFlag)
                {
                    if((LpstrMapCoor->P1_CarCoor.fObjY < GstrRamSnsCoor[1][5].cRadarY)||\
                        (LpstrMapCoor->P2_CarCoor.fObjY < GstrRamSnsCoor[1][5].cRadarY))
                    {
                        if(LpstrMapCoor->P1_CarCoor.fObjX < LpstrMapCoor->P2_CarCoor.fObjX)
                        {
                            LfFirstX = LpstrMapCoor->P1_CarCoor.fObjX;
                            LfSecondX = LpstrMapCoor->P2_CarCoor.fObjX;
                        }
                        else
                        {
                            LfFirstX = LpstrMapCoor->P2_CarCoor.fObjX;
                            LfSecondX = LpstrMapCoor->P1_CarCoor.fObjX;
                        }
                        LfSns_X_Coor = GstrRamSnsCoor[1][0].cRadarX;
                        if((LfFirstX < LfSns_X_Coor)&&(LfSns_X_Coor < LfSecondX))
                        {
                            LfP1_P2_Line_K = (LpstrMapCoor->P1_CarCoor.fObjY - LpstrMapCoor->P2_CarCoor.fObjY)/(LpstrMapCoor->P1_CarCoor.fObjX - LpstrMapCoor->P2_CarCoor.fObjX);
                            LfP1_P2_Line_K = LfP1_P2_Line_K*(LpstrMapCoor->P1_CarCoor.fObjX - LfSns_X_Coor);
                            LfSnsToMapLineY_Coor = LpstrMapCoor->P1_CarCoor.fObjY - LfP1_P2_Line_K;
                            LfSnsToMapLineY_Coor = ABS_VALUE(LfSnsToMapLineY_Coor);
                            Lu16Sns_ToMapDis = LfSnsToMapLineY_Coor - GstrRamSnsCoor[1][0].cRadarY;
                            Lu16SideDE_AndToMapDisSub = ABS(Lu16Sns_ToMapDis,GstrMapSnsRealTimeDis[1][5].u16MasDis);
                        }
                    }
                    if(Lu16Sns_ToMapDis < 1300)
                    {
                        if(Lu16SideDE_AndToMapDisSub < 250)
                        {
                            if(GstrMapSnsRealTimeDis[1][5].u16MasDis < MAP_TABLE_MAX_DIS)
                            {
                                Lu8HighIndex = GstrMapSnsRealTimeDis[1][5].u16MasDis/MAP_HIGH_TABLE_STEP;
                            }
                            else
                            {
                                Lu8HighIndex = SNS_MAP_DIS_HIGH_400cm;
                            }
                        
                            if(GstrMapSnsRealTimeDis[1][5].eMeasType == PDC_SNS_MEAS_STD)
                            {
                                LptrThreTable = &GStrMapObjJudgeStdThresTableForSideMapInRAM[0];
                            }
                            else
                            {
                                LptrThreTable = &GStrMapObjJudgeChirpThresTableForSideMapInRAM[0];
                            }
                            if(GstrMapSnsRealTimeDis[1][5].u16MasterHeight > LptrThreTable[Lu8HighIndex].u16BigWallHeight)
                            {
                                Lu8HighIndex = GstrMapSnsRealTimeDis[1][5].u16MasterHeight/LptrThreTable[Lu8HighIndex].u16BigWallHeight;
                                LpstrMapCoor->u8SideMapBigWallCnt += Lu8HighIndex;
                            }
                        }
                        else
                        {
                            LpstrMapCoor->u8SideMapBigWallCnt = 0;
                        }
#if 0
                        if((LpstrMapCoor->eMapObjId == 0x03)||(LpstrMapCoor->eMapObjId == 0x01))
                        {
                            VS_PRINT("Time:%.3f,Id:%d,RRS Sns De,MasterDis:%d,MasterHeight:%d,RRS_ToMapDis:%d,DisSub:%d,SideMapBigWallCnt:%d,HighIndex:%d\r\n",GfMessageTime,LpstrMapCoor->eMapObjId,\
                                GstrMapSnsRealTimeDis[1][5].u16MasDis,GstrMapSnsRealTimeDis[1][5].u16MasterHeight,\
                                Lu16Sns_ToMapDis,Lu16SideDE_AndToMapDisSub,LpstrMapCoor->u8SideMapBigWallCnt,Lu8HighIndex);
                        }
#endif
                        if(LpstrMapCoor->u8SideMapBigWallCnt > 6)
                        {
                            LpstrMapCoor->eMapObjHeight = OBJ_HIGH;
                        }
                    }
                    else
                    {
                        LpstrMapCoor->u8SideMapBigWallCnt = 0;
                    }
                }
            }
        }
    }

    /* 针对侧边Map进入到重复区域的处理策略 */
    if((LpstrMapCoor->enuSideMapUpdateSts == SIDE_MAP_MEMORY_P1_UPDATE_P2)&&(LpstrMapCoor->enuSideMapSns > SIDE_SNS_FRS))
    {
        uint8 Lu8NearId;
        ObjCoorType LStrAreaMapMax_X_Coor;
        ObjCoorType LStrAreaMapMin_X_Coor;
        float LfMapP2_Y_CoorAbs;
        
        Lu8NearId = GstrSideAreaMap[LpstrMapCoor->enuSideMapSns].u8NearstMapId;
        if(Lu8NearId != MAP_NONE)
        {
            if(GstrObjMap.ObjMapCoor[Lu8NearId].P1_CarCoor.fObjX > GstrObjMap.ObjMapCoor[Lu8NearId].P2_CarCoor.fObjX)
            {
                LStrAreaMapMax_X_Coor = GstrObjMap.ObjMapCoor[Lu8NearId].P1_CarCoor;
                LStrAreaMapMin_X_Coor = GstrObjMap.ObjMapCoor[Lu8NearId].P2_CarCoor;
            }
            else
            {
                LStrAreaMapMax_X_Coor = GstrObjMap.ObjMapCoor[Lu8NearId].P2_CarCoor;
                LStrAreaMapMin_X_Coor = GstrObjMap.ObjMapCoor[Lu8NearId].P1_CarCoor;
            }

            if((LpstrMapCoor->P2_CarCoor.fObjX < (LStrAreaMapMax_X_Coor.fObjX+30))&&\
                (LpstrMapCoor->P2_CarCoor.fObjX > (LStrAreaMapMin_X_Coor.fObjX-30)))
            {
                LfMapP2_Y_CoorAbs = ABS_VALUE(LpstrMapCoor->P2_CarCoor.fObjY);
                if(LfMapP2_Y_CoorAbs > (GstrSideAreaMap[LpstrMapCoor->enuSideMapSns].fNearestMapToSnsDis-200))
                {
                    LpstrMapCoor->u8MapTooLongNeedEndFlag = 1;
                }
            }
        }
    }

#if 0
    if((LpstrMapCoor->eMapObjId == 0x02)&&(GstrMapSnsRealTimeDis[0][0].u8RealDisUpdateFlag)&&(GfMessageTime < 5))
    {
        VS_PRINT("FLS Master Dis,Time:%.3f,Id:%d,ValidMasterDis:%d,RealMasterDis:%d\r\n",GfMessageTime,LpstrMapCoor->eMapObjId,\
            GstrSideAreaMap[SIDE_SNS_FLS].u16ValidMasterDis,GstrMapSnsRealTimeDis[0][0].u16MasDis);
    }
#endif

    
    return Lu8MapNeedDeleteFlag;
}


/******************************************************************************
 * 函数名称: Map_DE_MatchJudge
 * 
 * 功能描述: 判断Map和对应的DE 是否匹配
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-01-08 10:54   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void Map_DE_MatchJudge(ObjMapCoorType *LpstrMapCoor)
#if 1
{
    uint16 Lu16ToBumperDisAndDE_Sub;
    PDCSnsChannelType LeMatchSnsCh[4];
    PDCSnsChannelType LeP1NearSnsCh,LeP2NearSnsCh;
    uint8 Lu8MatchSnsCnt = 0;
    uint8 Lu8SnsGroup = 2;
    uint8 i;
    float LfLeftPointToBumperDis,LfRightPointToBumperDis;
    float LfUsePointToBumperDis;

    
    uint8 Lu8MapDE_NoMatchFlag = 0;
    uint8 Lu8MapUpdateTimeByDeMatchFlag = 0;
    uint8 Lu8MapDeMatchOkFlag = 0;
    ObjMAPAreaType LenuLeftArea;
    ObjMAPAreaType LenuRightArea;
    float LfLeftPointY_Coor,LfRightPointY_Coor;
    uint16 Lu16PointToMiddleSnsY_Dis,Lu16PointToCornerSnsY_Dis;
    static uint8 Lu8GuidanceDeleCntSet = 2;

    if(GstrParkingGuidenceData.eParking_Sts == PARKING_SUMMON)
    {
        if((LpstrMapCoor->enuSideMapSns == SIDE_SNS_NONE)&&(LpstrMapCoor->eObjMoveSts == OBJ_TO_CAR_MOVE_CLOSE))
        {
            Lu8MapDE_NoMatchFlag = 1;
        }
        if(LpstrMapCoor->enuSideMapSns == SIDE_SNS_NONE)
        {
            Lu8MapUpdateTimeByDeMatchFlag = 1;
        }
    }
    if(GstrParkingGuidenceData.eLastAdasSts == APA_GUIDANCE)
    {
        if((LpstrMapCoor->enuSideMapSns == SIDE_SNS_NONE)&&(LpstrMapCoor->eObjMoveSts == OBJ_TO_CAR_MOVE_CLOSE))
        {
            Lu8MapDE_NoMatchFlag = 1;
        }
    }
    else
    {
        if((LpstrMapCoor->enuSideMapSns == SIDE_SNS_NONE)&&(LpstrMapCoor->eObjMoveSts == OBJ_TO_CAR_MOVE_CLOSE))
        {
            /* 非泊车状态下，前后保的处理逻辑一样 */
            if(1)
            //if(LpstrMapCoor->eObjBumperArea == OBJ_FRONT_BUMP)
            {
                Lu8MapDE_NoMatchFlag = 1;
            }
        }
    }
    if(LpstrMapCoor->eObjBumperArea == OBJ_FRONT_BUMP)
    {
        Lu8SnsGroup = 0;
    }
    else if(LpstrMapCoor->eObjBumperArea == OBJ_REAR_BUMP)
    {
        Lu8SnsGroup = 1;
    }
    if(Lu8SnsGroup < 2)
    {
        if(Lu8MapDE_NoMatchFlag)
        {
            if(LpstrMapCoor->fP1_P2_Dis < 50)
            {
                LeP1NearSnsCh = FindMapPointNearSns(LpstrMapCoor->P1_CarCoor.fObjY,Lu8SnsGroup);
                LfUsePointToBumperDis = LpstrMapCoor->fMapToBumper_Dis;
                if(GstrMapSnsRealTimeDis[Lu8SnsGroup][LeP1NearSnsCh].u8RealDisUpdateFlag)
                {
                    if(!LpstrMapCoor->u8MapToLowLockFlag)
                    {
                        if((LfUsePointToBumperDis > MAP_DE_MATCH_START_DIS)&&(LfUsePointToBumperDis < MAP_DE_MATCH_END_DIS))
                        {
                            Lu16ToBumperDisAndDE_Sub = ABS(LfUsePointToBumperDis,GstrMapSnsRealTimeDis[Lu8SnsGroup][LeP1NearSnsCh].u16MasDis);
                            if(Lu16ToBumperDisAndDE_Sub < 250)
                            {
                                LpstrMapCoor->u8Map_DE_NoMatchCnt = 0;
                            }
                            else
                            {
                                LpstrMapCoor->u8Map_DE_NoMatchCnt++;
                            }
                        }
                        else if(LfUsePointToBumperDis < MAP_DE_MATCH_START_DIS)
                        {
                            Lu16ToBumperDisAndDE_Sub = ABS(LfUsePointToBumperDis,GstrMapSnsRealTimeDis[Lu8SnsGroup][LeP1NearSnsCh].u16MasDis);
                            if(Lu16ToBumperDisAndDE_Sub < 250)
                            {
                                LpstrMapCoor->u8Map_DE_NoMatchCnt = 0;
                            }
                        }
                    }
                    else
                    {
                        LpstrMapCoor->eMapDE_NoMatchHeight = OBJ_LOW;
                        LpstrMapCoor->u8Map_DE_NoMatchCnt = 0;
                        if(GstrMapSnsRealTimeDis[Lu8SnsGroup][LeP1NearSnsCh].u16MasDis < MAP_DE_MATCH_START_DIS)
                        {
                            Lu16ToBumperDisAndDE_Sub = ABS(LfUsePointToBumperDis,GstrMapSnsRealTimeDis[Lu8SnsGroup][LeP1NearSnsCh].u16MasDis);
                            if(Lu16ToBumperDisAndDE_Sub < 250)
                            {
                                LpstrMapCoor->eMapDE_NoMatchHeight = OBJ_HIGH;
                                LpstrMapCoor->u8MapToLowLockFlag = 0;
                            }
                        }
                    }
                    Lu8GuidanceDeleCntSet = 2;                    
                }  

                /* 以下是DE 稳定性处理Map的过程 */
                if(Lu8MapUpdateTimeByDeMatchFlag)
                {
                    if(GstrMapSnsRealTimeDis[Lu8SnsGroup][LeP1NearSnsCh].u8RealDisUpdateFlag)
                    {
                        if(LfUsePointToBumperDis < MAP_DE_HOLD_MAP_DIS)
                        {
                            Lu16ToBumperDisAndDE_Sub = ABS(LfUsePointToBumperDis,GstrMapSnsRealTimeDis[Lu8SnsGroup][LeP1NearSnsCh].u16MasDis);
                            if(Lu16ToBumperDisAndDE_Sub < 200)
                            {
                                Lu8MapDeMatchOkFlag = 1;
                            }
                        }                    
                    }
                    
                    /* 只要DE 匹配上，强制保持探测更新时间 */
                    if(Lu8MapDeMatchOkFlag)
                    {
                        LpstrMapCoor->u32UpdateSysTime = GdSystemMsTimer;
                    }
                } 
            }
            else
            {
                LeP1NearSnsCh = FindMapPointNearSns(LpstrMapCoor->P1_CarCoor.fObjY,Lu8SnsGroup);
                LeP2NearSnsCh = FindMapPointNearSns(LpstrMapCoor->P2_CarCoor.fObjY,Lu8SnsGroup);
                if(LeP1NearSnsCh == LeP2NearSnsCh)
                {
                    LeMatchSnsCh[Lu8MatchSnsCnt] = LeP1NearSnsCh;
                    Lu8MatchSnsCnt++;
                }
                else if(LeP1NearSnsCh < LeP2NearSnsCh)
                {
                    LeMatchSnsCh[Lu8MatchSnsCnt] = LeP1NearSnsCh;
                    Lu8MatchSnsCnt++;
                    for(i = 1; i < 4; i++)
                    {
                        if((LeP1NearSnsCh+i) == LeP2NearSnsCh)
                        {
                            LeMatchSnsCh[Lu8MatchSnsCnt] = LeP1NearSnsCh+i;
                            Lu8MatchSnsCnt++;
                            break;
                        }
                        else
                        {
                            LeMatchSnsCh[Lu8MatchSnsCnt] = LeP1NearSnsCh+i;
                            Lu8MatchSnsCnt++;
                        }
                    }
                }
                else
                {
                    LeMatchSnsCh[Lu8MatchSnsCnt] = LeP2NearSnsCh;
                    Lu8MatchSnsCnt++;
                    for(i = 1; i < 4; i++)
                    {
                        if((LeP2NearSnsCh+i) == LeP1NearSnsCh)
                        {
                            LeMatchSnsCh[Lu8MatchSnsCnt] = LeP2NearSnsCh+i;
                            Lu8MatchSnsCnt++;
                            break;
                        }
                        else
                        {
                            LeMatchSnsCh[Lu8MatchSnsCnt] = LeP2NearSnsCh+i;
                            Lu8MatchSnsCnt++;
                        }
                    }
                }
                Lu8GuidanceDeleCntSet = Lu8MatchSnsCnt+2;
                if(LpstrMapCoor->P1_CarCoor.fObjY < LpstrMapCoor->P2_CarCoor.fObjY)
                {
                    LfLeftPointToBumperDis = LpstrMapCoor->fP2_ToBumper_Dis;
                    LfRightPointToBumperDis = LpstrMapCoor->fP1_ToBumper_Dis;
                }
                else
                {
                    LfLeftPointToBumperDis = LpstrMapCoor->fP1_ToBumper_Dis;
                    LfRightPointToBumperDis = LpstrMapCoor->fP2_ToBumper_Dis;
                }
                
                for(i = 0; i < Lu8MatchSnsCnt; i++)
                {
                    if(i == 0)
                    {
                        LfUsePointToBumperDis = LfLeftPointToBumperDis;
                    }
                    else if(i == (Lu8MatchSnsCnt-1))
                    {
                        LfUsePointToBumperDis = LfRightPointToBumperDis;
                    }
                    else
                    {
                        LfUsePointToBumperDis = LfLeftPointToBumperDis + LfRightPointToBumperDis;
                        LfUsePointToBumperDis = LfUsePointToBumperDis/2;
                    }
            
                    if(GstrMapSnsRealTimeDis[Lu8SnsGroup][LeMatchSnsCh[i]].u8RealDisUpdateFlag)
                    {
                        if(!LpstrMapCoor->u8MapToLowLockFlag)
                        {
                            if((LfUsePointToBumperDis > MAP_DE_MATCH_START_DIS)&&(LfUsePointToBumperDis < MAP_DE_MATCH_END_DIS))
                            {
                                Lu16ToBumperDisAndDE_Sub = ABS(LfUsePointToBumperDis,GstrMapSnsRealTimeDis[Lu8SnsGroup][LeMatchSnsCh[i]].u16MasDis);
                                if(Lu16ToBumperDisAndDE_Sub < 250)
                                {
                                    LpstrMapCoor->u8Map_DE_NoMatchCnt = 0;
                                }
                                else
                                {
                                    LpstrMapCoor->u8Map_DE_NoMatchCnt++;
                                }
                            }
                            else if(LfUsePointToBumperDis < MAP_DE_MATCH_START_DIS)
                            {
                                Lu16ToBumperDisAndDE_Sub = ABS(LfUsePointToBumperDis,GstrMapSnsRealTimeDis[Lu8SnsGroup][LeMatchSnsCh[i]].u16MasDis);
                                if(Lu16ToBumperDisAndDE_Sub < 250)
                                {
                                    LpstrMapCoor->u8Map_DE_NoMatchCnt = 0;
                                }
                            }
                        }
                        else
                        {
                            LpstrMapCoor->eMapDE_NoMatchHeight = OBJ_LOW;
                            LpstrMapCoor->u8Map_DE_NoMatchCnt = 0;
                            if(GstrMapSnsRealTimeDis[Lu8SnsGroup][LeMatchSnsCh[i]].u16MasDis < MAP_DE_MATCH_START_DIS)
                            {
                                Lu16ToBumperDisAndDE_Sub = ABS(LfUsePointToBumperDis,GstrMapSnsRealTimeDis[Lu8SnsGroup][LeMatchSnsCh[i]].u16MasDis);
                                if(Lu16ToBumperDisAndDE_Sub < 250)
                                {
                                    LpstrMapCoor->eMapDE_NoMatchHeight = OBJ_HIGH;
                                    LpstrMapCoor->u8MapToLowLockFlag = 0;
                                }
                            }
                        } 
                    } 
                    /* 以下是DE 稳定性处理Map的过程 */
                    if(Lu8MapUpdateTimeByDeMatchFlag)
                    {
                        if(GstrMapSnsRealTimeDis[Lu8SnsGroup][LeMatchSnsCh[i]].u8RealDisUpdateFlag)
                        {
                            if(LfUsePointToBumperDis < MAP_DE_HOLD_MAP_DIS)
                            {
                                Lu16ToBumperDisAndDE_Sub = ABS(LfUsePointToBumperDis,GstrMapSnsRealTimeDis[Lu8SnsGroup][LeMatchSnsCh[i]].u16MasDis);
                                if(Lu16ToBumperDisAndDE_Sub < 200)
                                {
                                    Lu8MapDeMatchOkFlag = 1;
                                }
                            }                    
                        }
                        
                        /* 只要DE 匹配上，强制保持探测更新时间 */
                        if(Lu8MapDeMatchOkFlag)
                        {
                            LpstrMapCoor->u32UpdateSysTime = GdSystemMsTimer;
                        }
                    }
                }
            } 
        }
        if(!LpstrMapCoor->u8MapToLowLockFlag)
        {
            if(GstrParkingGuidenceData.eParking_Sts == PARKING_SUMMON)
            {
                if(LpstrMapCoor->u8Map_DE_NoMatchCnt > Lu8GuidanceDeleCntSet)
                {
                    LpstrMapCoor->eMapDE_NoMatchHeight = OBJ_LOW;
                }
                else
                {
                    LpstrMapCoor->eMapDE_NoMatchHeight = OBJ_HIGH;
                }
            }
            else
            {
                if(LpstrMapCoor->u8Map_DE_NoMatchCnt > Lu8GuidanceDeleCntSet)
                {
                    LpstrMapCoor->eMapDE_NoMatchHeight = OBJ_LOW;
                }
                else
                {
                    LpstrMapCoor->eMapDE_NoMatchHeight = OBJ_HIGH;
                }
            }
            if((LpstrMapCoor->eMapDE_NoMatchHeight == OBJ_LOW)&&(LpstrMapCoor->eObjMoveSts == OBJ_TO_CAR_MOVE_FARAWAY))
            {
                LpstrMapCoor->u8MapToLowLockFlag = 1;
            }
        }
        else
        {
            LpstrMapCoor->eMapDE_NoMatchHeight = OBJ_LOW;
        }
    }
    else
    {
        LpstrMapCoor->u8Map_DE_NoMatchCnt = 0;
    }
}

#else
{
    uint16 Lu16ToBumperDisAndDE_Sub;
    uint8 Lu8SnsGroup = 0;
    uint8 Lu8SnsCh1 = 2;
    uint8 Lu8SnsCh2 = 3;
    uint8 Lu8MapInAreaFlag = 0;
    uint8 Lu8MapDE_NoMatchFlag = 0;
    uint8 Lu8MapUpdateTimeByDeMatchFlag = 0;
    uint8 Lu8MapDeMatchOkFlag = 0;
    ObjMAPAreaType LenuLeftArea;
    ObjMAPAreaType LenuRightArea;
    float LfLeftPointY_Coor,LfRightPointY_Coor;
    uint16 Lu16PointToMiddleSnsY_Dis,Lu16PointToCornerSnsY_Dis;
    static uint8 Lu8GuidanceDeleCntSet = 2;

    if(GstrParkingGuidenceData.eLowVolPwrMdFlag2 == LOWPOWER_MODFLAG2_ADAS)
    {
        if((LpstrMapCoor->enuSideMapSns == SIDE_SNS_NONE)&&(LpstrMapCoor->eObjMoveSts == OBJ_TO_CAR_MOVE_CLOSE))
        {
            Lu8MapDE_NoMatchFlag = 1;
        }
        if(LpstrMapCoor->enuSideMapSns == SIDE_SNS_NONE)
        {
            Lu8MapUpdateTimeByDeMatchFlag = 1;
        }
    }
    if(GstrParkingGuidenceData.eLastAdasSts == APA_GUIDANCE)
    {
        if((LpstrMapCoor->enuSideMapSns == SIDE_SNS_NONE)&&(LpstrMapCoor->eObjMoveSts == OBJ_TO_CAR_MOVE_CLOSE))
        {
            Lu8MapDE_NoMatchFlag = 1;
        }
    }
    else
    {
        if((LpstrMapCoor->enuSideMapSns == SIDE_SNS_NONE)&&(LpstrMapCoor->eObjMoveSts == OBJ_TO_CAR_MOVE_CLOSE))
        {
            if(LpstrMapCoor->eObjBumperArea == OBJ_FRONT_BUMP)
            {
                Lu8MapDE_NoMatchFlag = 1;
            }
        }
    }

    /* 根据分区，配属不同的探头 DE，以下需要根据点状障碍物和线状障碍物进行区分处理 */
    if(LpstrMapCoor->eMapObjType == OBJ_TYPE_POINT)
    {
        /* 点状障碍物根据不同的分区设置不同的匹配探头 */
        if(LpstrMapCoor->eP1_DetailArea == MAP_AREA3)
        {
            Lu8MapInAreaFlag = 1;
            Lu8SnsCh1 = 1;
            Lu8SnsCh2 = 2;
            Lu8GuidanceDeleCntSet = 2;
        }
        else if(LpstrMapCoor->eP1_DetailArea == MAP_AREA4)
        {
            Lu8MapInAreaFlag = 1;
            Lu8SnsCh1 = 2;
            Lu8SnsCh2 = 3;
            Lu8GuidanceDeleCntSet = 2;
        }
        else if(LpstrMapCoor->eP1_DetailArea == MAP_AREA5)
        {
            Lu8MapInAreaFlag = 1;
            Lu8SnsCh1 = 3;
            Lu8SnsCh2 = 4;
            Lu8GuidanceDeleCntSet = 2;
        }
        if(LpstrMapCoor->eObjBumperArea == OBJ_FRONT_BUMP)
        {
            Lu8SnsGroup = 0;
        }
        else if(LpstrMapCoor->eObjBumperArea == OBJ_REAR_BUMP)
        {
            Lu8SnsGroup = 1;
        }
        else
        {
            Lu8MapInAreaFlag = 0;
        }
    }
    else
    {
        if(LpstrMapCoor->eObjBumperArea == OBJ_FRONT_BUMP)
        {
            Lu8SnsGroup = 0;
            Lu8MapInAreaFlag = 1;
        }
        else if(LpstrMapCoor->eObjBumperArea == OBJ_REAR_BUMP)
        {
            Lu8SnsGroup = 1;
            Lu8MapInAreaFlag = 1;
        }
        else
        {
            Lu8MapInAreaFlag = 0;
        }
        if(Lu8MapInAreaFlag)
        {
            Lu8MapInAreaFlag = 0;
            if(LpstrMapCoor->P1_CarCoor.fObjY > LpstrMapCoor->P2_CarCoor.fObjY)
            {
                LenuLeftArea = LpstrMapCoor->eP1_DetailArea;
                LenuRightArea = LpstrMapCoor->eP2_DetailArea;
                LfLeftPointY_Coor = LpstrMapCoor->P1_CarCoor.fObjY;
                LfRightPointY_Coor = LpstrMapCoor->P2_CarCoor.fObjY;
            }
            else
            {
                LenuLeftArea = LpstrMapCoor->eP2_DetailArea;
                LenuRightArea = LpstrMapCoor->eP1_DetailArea;
                LfLeftPointY_Coor = LpstrMapCoor->P2_CarCoor.fObjY;
                LfRightPointY_Coor = LpstrMapCoor->P1_CarCoor.fObjY;
            }
            
            if((LenuLeftArea == MAP_AREA3)&&(LenuRightArea == MAP_AREA3))
            {
                Lu8MapInAreaFlag = 1;
                Lu8SnsCh1 = 1;
                Lu8SnsCh2 = 2;
                Lu8GuidanceDeleCntSet = 3;
            }
            else if((LenuLeftArea == MAP_AREA3)&&(LenuRightArea == MAP_AREA4))
            {
                Lu8MapInAreaFlag = 1;
                Lu16PointToCornerSnsY_Dis = ABS(LfLeftPointY_Coor,GstrRamSnsCoor[Lu8SnsGroup][1].cRadarY);
                Lu16PointToMiddleSnsY_Dis = ABS(LfLeftPointY_Coor,GstrRamSnsCoor[Lu8SnsGroup][2].cRadarY);
                if(Lu16PointToCornerSnsY_Dis < Lu16PointToMiddleSnsY_Dis)
                {
                    Lu8SnsCh1 = 1;
                    Lu8SnsCh2 = 2;
                    Lu8GuidanceDeleCntSet = 3;
                }
                else
                {
                    Lu8SnsCh1 = 2;
                    Lu8SnsCh2 = 3;
                    Lu8GuidanceDeleCntSet = 2;
                }
            }
            else if((LenuLeftArea == MAP_AREA3)&&(LenuRightArea == MAP_AREA5))
            {
                Lu8MapInAreaFlag = 1;
                Lu8SnsCh1 = 2;
                Lu8SnsCh2 = 3;
                Lu8GuidanceDeleCntSet = 2;
            }
            else if((LenuLeftArea == MAP_AREA4)&&(LenuRightArea == MAP_AREA4))
            {
                Lu8MapInAreaFlag = 1;
                Lu8SnsCh1 = 2;
                Lu8SnsCh2 = 3;
                Lu8GuidanceDeleCntSet = 2;
            }
            else if((LenuLeftArea == MAP_AREA4)&&(LenuRightArea == MAP_AREA5))
            {
                Lu8MapInAreaFlag = 1;
                Lu16PointToCornerSnsY_Dis = ABS(LfRightPointY_Coor,GstrRamSnsCoor[Lu8SnsGroup][4].cRadarY);
                Lu16PointToMiddleSnsY_Dis = ABS(LfRightPointY_Coor,GstrRamSnsCoor[Lu8SnsGroup][3].cRadarY);
                if(Lu16PointToCornerSnsY_Dis < Lu16PointToMiddleSnsY_Dis)
                {
                    Lu8SnsCh1 = 3;
                    Lu8SnsCh2 = 4;
                    Lu8GuidanceDeleCntSet = 3;
                }
                else
                {
                    Lu8SnsCh1 = 2;
                    Lu8SnsCh2 = 3;
                    Lu8GuidanceDeleCntSet = 2;
                }
            }
            else if((LenuLeftArea == MAP_AREA5)&&(LenuRightArea == MAP_AREA5))
            {
                Lu8MapInAreaFlag = 1;
                Lu8SnsCh1 = 3;
                Lu8SnsCh2 = 4;
                Lu8GuidanceDeleCntSet = 3;
            }
        }
    }
    if(Lu8MapInAreaFlag)
    {
        if(Lu8MapDE_NoMatchFlag)
        {
            if(GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh1].u8RealDisUpdateFlag)
            {
                if(!LpstrMapCoor->u8MapToLowLockFlag)
                {
                    if((LpstrMapCoor->fMapToBumper_Dis > MAP_DE_MATCH_START_DIS)&&(LpstrMapCoor->fMapToBumper_Dis < MAP_DE_MATCH_END_DIS))
                    {
                        Lu16ToBumperDisAndDE_Sub = ABS(LpstrMapCoor->fMapToBumper_Dis,GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh1].u16MasDis);
                        if(Lu16ToBumperDisAndDE_Sub < 250)
                        {
                            LpstrMapCoor->u8Map_DE_NoMatchCnt = 0;
                        }
                        else
                        {
                            LpstrMapCoor->u8Map_DE_NoMatchCnt++;
                        }
                    }
                    else if(LpstrMapCoor->fMapToBumper_Dis < MAP_DE_MATCH_START_DIS)
                    {
                        Lu16ToBumperDisAndDE_Sub = ABS(LpstrMapCoor->fMapToBumper_Dis,GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh1].u16MasDis);
                        if(Lu16ToBumperDisAndDE_Sub < 250)
                        {
                            LpstrMapCoor->u8Map_DE_NoMatchCnt = 0;
                        }
                    }
                }
                else
                {
                    LpstrMapCoor->eMapDE_NoMatchHeight = OBJ_LOW;
                    LpstrMapCoor->u8Map_DE_NoMatchCnt = 0;
                    if(GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh1].u16MasDis < MAP_DE_MATCH_START_DIS)
                    {
                        Lu16ToBumperDisAndDE_Sub = ABS(LpstrMapCoor->fMapToBumper_Dis,GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh1].u16MasDis);
                        if(Lu16ToBumperDisAndDE_Sub < 250)
                        {
                            LpstrMapCoor->eMapDE_NoMatchHeight = OBJ_HIGH;
                            LpstrMapCoor->u8MapToLowLockFlag = 0;
                        }
                    }
                }           
            }
            
            if(GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh2].u8RealDisUpdateFlag)
            {
                if(!LpstrMapCoor->u8MapToLowLockFlag)
                {
                    if((LpstrMapCoor->fMapToBumper_Dis > MAP_DE_MATCH_START_DIS)&&(LpstrMapCoor->fMapToBumper_Dis < MAP_DE_MATCH_END_DIS))
                    {
                        Lu16ToBumperDisAndDE_Sub = ABS(LpstrMapCoor->fMapToBumper_Dis,GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh2].u16MasDis);
                        if(Lu16ToBumperDisAndDE_Sub < 250)
                        {
                            LpstrMapCoor->u8Map_DE_NoMatchCnt = 0;
                        }
                        else
                        {
                            LpstrMapCoor->u8Map_DE_NoMatchCnt++;
                        }
                    }
                    else if(LpstrMapCoor->fMapToBumper_Dis < MAP_DE_MATCH_START_DIS)
                    {
                        Lu16ToBumperDisAndDE_Sub = ABS(LpstrMapCoor->fMapToBumper_Dis,GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh2].u16MasDis);
                        if(Lu16ToBumperDisAndDE_Sub < 250)
                        {
                            LpstrMapCoor->u8Map_DE_NoMatchCnt = 0;
                        }
                    }
                }
                else
                {
                    LpstrMapCoor->eMapDE_NoMatchHeight = OBJ_LOW;
                    LpstrMapCoor->u8Map_DE_NoMatchCnt = 0;
                    if(GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh2].u16MasDis < MAP_DE_MATCH_START_DIS)
                    {
                        Lu16ToBumperDisAndDE_Sub = ABS(LpstrMapCoor->fMapToBumper_Dis,GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh2].u16MasDis);
                        if(Lu16ToBumperDisAndDE_Sub < 250)
                        {
                            LpstrMapCoor->eMapDE_NoMatchHeight = OBJ_HIGH;
                            LpstrMapCoor->u8MapToLowLockFlag = 0;
                        }
                    }
                }
            }        
        }
    }
    else 
    {
        LpstrMapCoor->u8Map_DE_NoMatchCnt = 0;
    }

#if 1
    if(Lu8MapInAreaFlag)
    {
        if(!LpstrMapCoor->u8MapToLowLockFlag)
        {
            if(GstrParkingGuidenceData.eLowVolPwrMdFlag2 == LOWPOWER_MODFLAG2_ADAS)
            {
                if(LpstrMapCoor->u8Map_DE_NoMatchCnt > 3)
                {
                    LpstrMapCoor->eMapDE_NoMatchHeight = OBJ_LOW;
                }
                else
                {
                    LpstrMapCoor->eMapDE_NoMatchHeight = OBJ_HIGH;
                }
            }
            else
            {
                if(LpstrMapCoor->u8Map_DE_NoMatchCnt > Lu8GuidanceDeleCntSet)
                {
                    LpstrMapCoor->eMapDE_NoMatchHeight = OBJ_LOW;
                }
                else
                {
                    LpstrMapCoor->eMapDE_NoMatchHeight = OBJ_HIGH;
                }
            }
            if((LpstrMapCoor->eMapDE_NoMatchHeight == OBJ_LOW)&&(LpstrMapCoor->eObjMoveSts == OBJ_TO_CAR_MOVE_FARAWAY))
            {
                LpstrMapCoor->u8MapToLowLockFlag = 1;
            }
        }
        else
        {
            LpstrMapCoor->eMapDE_NoMatchHeight = OBJ_LOW;
        }
    }
#endif



    /* 以下是DE 稳定性处理Map的过程 */
    if((Lu8MapUpdateTimeByDeMatchFlag)&&(Lu8MapInAreaFlag))
    {
        if(GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh1].u8RealDisUpdateFlag)
        {
            if(LpstrMapCoor->fMapToBumper_Dis < MAP_DE_HOLD_MAP_DIS)
            {
                Lu16ToBumperDisAndDE_Sub = ABS(LpstrMapCoor->fMapToBumper_Dis,GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh1].u16MasDis);
                if(Lu16ToBumperDisAndDE_Sub < 200)
                {
                    Lu8MapDeMatchOkFlag = 1;
                }
            }                    
        }
        
        if(GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh2].u8RealDisUpdateFlag)
        {
            if(LpstrMapCoor->fMapToBumper_Dis < MAP_DE_HOLD_MAP_DIS)
            {
                Lu16ToBumperDisAndDE_Sub = ABS(LpstrMapCoor->fMapToBumper_Dis,GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh2].u16MasDis);
                if(Lu16ToBumperDisAndDE_Sub < 200)
                {
                    Lu8MapDeMatchOkFlag = 1;
                }
            }
        }
        /* 只要DE 匹配上，强制保持探测更新时间 */
        if(Lu8MapDeMatchOkFlag)
        {
            LpstrMapCoor->u32UpdateSysTime = GdSystemMsTimer;
        }
    }
}
#endif

static uint8 FindMapPointNearSnsCh(uint8 Lu8SnsGroup, float LfMapPointY_Coor)
{
    uint8 Lu8SnsCh;
    uint16 Lu16MasterDisSub;
    uint16 Lu16PointToSns01_Dis,Lu16PointToSns02_Dis;
    

    if(LfMapPointY_Coor > GstrRamSnsCoor[Lu8SnsGroup][1].cRadarY)
    {
        Lu8SnsCh = 1;
        return Lu8SnsCh;
    }
    else if(LfMapPointY_Coor > GstrRamSnsCoor[Lu8SnsGroup][2].cRadarY)
    {
        Lu8SnsCh = 1;
        Lu16MasterDisSub = ABS(GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh].u16MasDis,GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh+1].u16MasDis);
        if(Lu16MasterDisSub < 100)
        {
            /* 离那个探头近，选择哪一个探头 */
            Lu16PointToSns01_Dis = ABS(LfMapPointY_Coor,GstrRamSnsCoor[Lu8SnsGroup][Lu8SnsCh].cRadarY);
            Lu16PointToSns02_Dis = ABS(LfMapPointY_Coor,GstrRamSnsCoor[Lu8SnsGroup][Lu8SnsCh+1].cRadarY);
            if(Lu16PointToSns01_Dis < Lu16PointToSns02_Dis)
            {
                return Lu8SnsCh;
            }
            else
            {
                return (Lu8SnsCh+1);
            }
        }
        else
        {
            if(GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh].u16MasDis < GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh+1].u16MasDis)
            {
                return Lu8SnsCh;
            }
            else
            {
                return (Lu8SnsCh+1);
            }
        }
    }
    else if(LfMapPointY_Coor > GstrRamSnsCoor[Lu8SnsGroup][3].cRadarY)
    {
        Lu8SnsCh = 2;
        Lu16MasterDisSub = ABS(GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh].u16MasDis,GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh+1].u16MasDis);
        if(Lu16MasterDisSub < 100)
        {
            /* 离那个探头近，选择哪一个探头 */
            Lu16PointToSns01_Dis = ABS(LfMapPointY_Coor,GstrRamSnsCoor[Lu8SnsGroup][Lu8SnsCh].cRadarY);
            Lu16PointToSns02_Dis = ABS(LfMapPointY_Coor,GstrRamSnsCoor[Lu8SnsGroup][Lu8SnsCh+1].cRadarY);
            if(Lu16PointToSns01_Dis < Lu16PointToSns02_Dis)
            {
                return Lu8SnsCh;
            }
            else
            {
                return (Lu8SnsCh+1);
            }
        }
        else
        {
            if(GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh].u16MasDis < GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh+1].u16MasDis)
            {
                return Lu8SnsCh;
            }
            else
            {
                return (Lu8SnsCh+1);
            }
        }
    }
    else if(LfMapPointY_Coor > GstrRamSnsCoor[Lu8SnsGroup][4].cRadarY)
    {
        Lu8SnsCh = 3;
        Lu16MasterDisSub = ABS(GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh].u16MasDis,GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh+1].u16MasDis);
        if(Lu16MasterDisSub < 100)
        {
            /* 离那个探头近，选择哪一个探头 */
            Lu16PointToSns01_Dis = ABS(LfMapPointY_Coor,GstrRamSnsCoor[Lu8SnsGroup][Lu8SnsCh].cRadarY);
            Lu16PointToSns02_Dis = ABS(LfMapPointY_Coor,GstrRamSnsCoor[Lu8SnsGroup][Lu8SnsCh+1].cRadarY);
            if(Lu16PointToSns01_Dis < Lu16PointToSns02_Dis)
            {
                return Lu8SnsCh;
            }
            else
            {
                return (Lu8SnsCh+1);
            }
        }
        else
        {
            if(GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh].u16MasDis < GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh+1].u16MasDis)
            {
                return Lu8SnsCh;
            }
            else
            {
                return (Lu8SnsCh+1);
            }
        }
    }
    else
    {
        Lu8SnsCh = 4;
        return Lu8SnsCh;
    }
}



/******************************************************************************
 * 函数名称: SideMap_DeleHandle
 * 
 * 功能描述: 侧边Map入侵的特殊处理，主要用于侧雷达Map记忆后，尚未进入到后保更新区的一些更新策略
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-01-15 14:42   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static uint8 SideMap_DeleHandle(ObjMapCoorType *LpstrMapCoor)
{
    uint8 Lu8MapNeedDeleteFlag = 0;
    float LfMapP1_X_ABS_Value;
    float LfMapP2_X_ABS_Value;
    float LfMapP1_Y_ABS_Value;
    float LfMapP2_Y_ABS_Value;
    float LfP1_P2ToCarX_AngleAbs;
    float LfNearPointX_Coor,LfNearPointY_Coor,LfNearPointY_CoorAbs;
    float LfNearPointToBumperDis = 0;
    uint8 Lu8SnsGroup;
    uint8 Lu8SnsCh;
    uint16 Lu16NearPointToMiddleSnsY_Dis,Lu16NearPointToCornerSnsY_Dis;
    uint16 Lu16ToBumperDisAndDE_Sub;
    float LfSideCarInnerAreaY_Limit;
    float LfFrontBumperX_Limit;
    float LfRearBumperX_Limit;

    LfNearPointX_Coor = 0;
    LfNearPointY_Coor = 0;
    LfNearPointY_CoorAbs = 0;

    /* 针对平行泊车的低矮障碍物，不在走删除处理函数 */
    if(GstrParkingGuidenceData.eSlotType == SLOT_PARALLEL)
    {
        if(LpstrMapCoor->eMapObjHeight == OBJ_LOW)
        {
            return 0;
        }
    }
    /* 侧边Map删除都是针对已经确定的记忆障碍物 */
    if((LpstrMapCoor->enuSideMapUpdateSts == SIDE_MAP_MEMORY_P1_MEMORY_P2))
    {
        LfMapP1_X_ABS_Value = ABS_VALUE(LpstrMapCoor->P1_CarCoor.fObjX);
        LfMapP1_Y_ABS_Value = ABS_VALUE(LpstrMapCoor->P1_CarCoor.fObjY);
        LfMapP2_X_ABS_Value = ABS_VALUE(LpstrMapCoor->P2_CarCoor.fObjX);
        LfMapP2_Y_ABS_Value = ABS_VALUE(LpstrMapCoor->P2_CarCoor.fObjY);
        LfP1_P2ToCarX_AngleAbs = ABS_VALUE(LpstrMapCoor->fP1_P2ToCarX_Angle);
        LfSideCarInnerAreaY_Limit = GstrRamSnsCoor[1][0].cRadarY+350;
        LfFrontBumperX_Limit= GstrRamSnsCoor[0][0].cRadarX+150;
        LfRearBumperX_Limit = GstrRamSnsCoor[1][0].cRadarX-250;

        /* 删除条件1：侧边Map距离保杠太远或P1 P2的X坐标均超出范围 */
        if((LpstrMapCoor->fMapToBumper_Dis > SIDE_MAP_FAR_AWAY_DELETE_DIS))
        {
            Lu8MapNeedDeleteFlag = 1;
            //VS_PRINT("Time:%.3f,Side Dele 001,Id:%d\r\n",GfMessageTime,LpstrMapCoor->eMapObjId);
            return Lu8MapNeedDeleteFlag;
        }
        else
        {
            if((LfMapP1_X_ABS_Value > MAP_TO_CAN_X_LIMIT_MAX)&&(LfMapP2_X_ABS_Value > MAP_TO_CAN_X_LIMIT_MAX))
            {
                Lu8MapNeedDeleteFlag = 1;
                //VS_PRINT("Time:%.3f,Side Dele 002,Id:%d\r\n",GfMessageTime,LpstrMapCoor->eMapObjId);
                return Lu8MapNeedDeleteFlag;
            }
        }

        /* 删除条件2：侧边Map距离保杠太远或P1 P2的X坐标均超出范围 */
        /* 仅针对Search阶段的短Map和置信度低的Map进行删除 */
        if(LpstrMapCoor->eMapCreatAdasSts != APA_GUIDANCE)
        {
            /* 需要维持点状Map High的存在 (LpstrMapCoor->fP1_P2_Dis < 80)*/
            if((LpstrMapCoor->eObjExistProb < OBJ_PROB_LV2))
            {
                Lu8MapNeedDeleteFlag = 1;
                //VS_PRINT("Time:%.3f,Side Dele 003,Id:%d,P1_P2_Dis:%.3f,ExistProb:%d\r\n",GfMessageTime,LpstrMapCoor->eMapObjId,LpstrMapCoor->fP1_P2_Dis,LpstrMapCoor->eObjExistProb);
                return Lu8MapNeedDeleteFlag;
            }
        }

        /* 删除条件3：垂直与车身，Y偏离较远的删除 */
        /* 垂直与车身，Y偏离较远的删除 */
        if(LfP1_P2ToCarX_AngleAbs > F_R_MAP_45_ANGLE)
        {
            if((LfMapP1_Y_ABS_Value > SIDE_MAP_OUT_ANGLE_DELE_Y)&&(LfMapP2_Y_ABS_Value > SIDE_MAP_OUT_ANGLE_DELE_Y))
            {
                Lu8MapNeedDeleteFlag = 1;
                //VS_PRINT("Time:%.3f,Side Dele 004-Line,Id:%d\r\n",GfMessageTime,LpstrMapCoor->eMapObjId);
                return Lu8MapNeedDeleteFlag;
            }
        }
        /* 侧边点状Map的删除策略 */
        if(LpstrMapCoor->fP1_P2_Dis < 50)
        {
            if(LfMapP1_Y_ABS_Value > SIDE_MAP_POINT_OUT_ANGLE_DELE_Y)
            {
                Lu8MapNeedDeleteFlag = 1;
                //VS_PRINT("Time:%.3f,Side Dele 004-Point,Id:%d\r\n",GfMessageTime,LpstrMapCoor->eMapObjId);
                return Lu8MapNeedDeleteFlag;
            }
        }

        /* 针对侧边Map进入到车内的删除逻辑，即P1 P1点有一个入侵到车位内，就直接删除 */
        if((LfMapP1_Y_ABS_Value < GstrRamSnsCoor[1][0].cRadarY)||(LfMapP2_Y_ABS_Value < GstrRamSnsCoor[1][0].cRadarY))
        {
            if((LpstrMapCoor->P1_CarCoor.fObjX > GstrRamSnsCoor[1][1].cRadarX)&&(LpstrMapCoor->P1_CarCoor.fObjX < GstrRamSnsCoor[0][1].cRadarX))
            {
                Lu8MapNeedDeleteFlag = 1;
                return Lu8MapNeedDeleteFlag;
            }
            if((LpstrMapCoor->P2_CarCoor.fObjX > GstrRamSnsCoor[1][1].cRadarX)&&(LpstrMapCoor->P2_CarCoor.fObjX < GstrRamSnsCoor[0][1].cRadarX))
            {
                Lu8MapNeedDeleteFlag = 1;
                return Lu8MapNeedDeleteFlag;
            }
        }
        

        /* 以下根据Map在保杠区域的不同位置进行不同的处理 */
        if((LfMapP1_Y_ABS_Value < LfSideCarInnerAreaY_Limit)&&(LfMapP2_Y_ABS_Value < LfSideCarInnerAreaY_Limit))
        {
            LpstrMapCoor->u8SideMapCoverCarDeNoMatchCnt = 0;
            LpstrMapCoor->u8SideMapToCarEdgeDeNoMatchCnt = 0;
            
            /* 区分前保还是后保 */
            if((LpstrMapCoor->P1_CarCoor.fObjX > LfFrontBumperX_Limit)&&(LpstrMapCoor->P2_CarCoor.fObjX > LfFrontBumperX_Limit))
            {
                Lu8SnsGroup = 0x00;
            }
            else if((LpstrMapCoor->P1_CarCoor.fObjX < LfRearBumperX_Limit)&&(LpstrMapCoor->P2_CarCoor.fObjX < LfRearBumperX_Limit))
            {
                Lu8SnsGroup = 0x01;
            }
            else 
            {
                Lu8SnsGroup = 0x02;
            }

            /* 选择离保杠最近的点 */
            if(LpstrMapCoor->fP2_ToBumper_Dis < LpstrMapCoor->fP1_ToBumper_Dis)
            {
                LfNearPointX_Coor = LpstrMapCoor->P2_CarCoor.fObjX;
                LfNearPointY_Coor = LpstrMapCoor->P2_CarCoor.fObjY;
                LfNearPointY_CoorAbs = LfMapP2_Y_ABS_Value;
                LfNearPointToBumperDis = LpstrMapCoor->fP2_ToBumper_Dis;
            }
            else
            {
                LfNearPointX_Coor = LpstrMapCoor->P1_CarCoor.fObjX;
                LfNearPointY_Coor = LpstrMapCoor->P1_CarCoor.fObjY;
                LfNearPointY_CoorAbs = LfMapP1_Y_ABS_Value;
                LfNearPointToBumperDis = LpstrMapCoor->fP1_ToBumper_Dis;
            }

            
            /* 判断和车身的角度，根据角度决定选择一个点还是2个点的坐标进行匹配 */
            if(Lu8SnsGroup == 0x00)
            {
                if((LfNearPointToBumperDis < SIDE_MAP_INNER_DELE_BUMPER_DIS)&&(GstrSnsCarMovSts[Lu8SnsGroup][0].eCarDir != SNS_CAR_BACKWARD))
                {
                    /* 查询离该点最近的探头，用探头的DE来进行约束删除 */
                    Lu8SnsCh = FindMapPointNearSnsCh(Lu8SnsGroup, LfNearPointY_Coor);
                    if(GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh].u8RealDisUpdateFlag)
                    {
                        /* 入侵才删除，远距离不删除 */
                        if(LfNearPointToBumperDis < GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh].u16MasDis)
                        {
                            Lu16ToBumperDisAndDE_Sub = ABS(LfNearPointToBumperDis,GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh].u16MasDis);
                        }
                        else
                        {
                            Lu16ToBumperDisAndDE_Sub = 0;
                        }
#if 0
                        if((LpstrMapCoor->eMapObjId == 0x00)&&(GfMessageTime > 49)&&(GfMessageTime < 54.5))
                        {
                            VS_PRINT("Time:%.3f,Id:%d,SnsCh:%d,MasterDis:%d,NearPointToBumperDis:%.2f,Sub:%d,DeNoMatchCnt:%d,Near_Y:%.2f\r\n",GfMessageTime,LpstrMapCoor->eMapObjId,Lu8SnsCh,\
                                GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh].u16MasDis,LfNearPointToBumperDis,Lu16ToBumperDisAndDE_Sub,\
                                LpstrMapCoor->u8SideMapToCarInnerDeNoMatchCnt,LfNearPointY_Coor);
                        }
#endif
                        
                        if(Lu16ToBumperDisAndDE_Sub > SIDE_MAP_INNER_DELE_DIS_SUB)
                        {
                            LpstrMapCoor->u8SideMapToCarInnerDeNoMatchCnt++;
                        }
                        else
                        {
                            LpstrMapCoor->u8SideMapToCarInnerDeNoMatchCnt = 0;
                        }
                    }
                }
                else
                {
                    LpstrMapCoor->u8SideMapToCarInnerDeNoMatchCnt = 0;
                }
            }
            else if(Lu8SnsGroup == 0x01)
            {
                if((LfNearPointToBumperDis < SIDE_MAP_INNER_DELE_BUMPER_DIS)&&(GstrSnsCarMovSts[Lu8SnsGroup][0].eCarDir != SNS_CAR_FORWARD))
                {
                    /* 查询离该点最近的探头，用探头的DE来进行约束删除 */
                    Lu8SnsCh = FindMapPointNearSnsCh(Lu8SnsGroup, LfNearPointY_Coor);
                    if(GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh].u8RealDisUpdateFlag)
                    {
                        if(LfNearPointToBumperDis < GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh].u16MasDis)
                        {
                            Lu16ToBumperDisAndDE_Sub = ABS(LfNearPointToBumperDis,GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh].u16MasDis);
                        }
                        else
                        {
                            Lu16ToBumperDisAndDE_Sub = 0;
                        }
                        if(Lu16ToBumperDisAndDE_Sub > SIDE_MAP_INNER_DELE_DIS_SUB)
                        {
                            LpstrMapCoor->u8SideMapToCarInnerDeNoMatchCnt++;
                        }
                        else
                        {
                            LpstrMapCoor->u8SideMapToCarInnerDeNoMatchCnt = 0;
                        }
#if 0
                        if((LpstrMapCoor->eMapObjId == 0x00)&&(GfMessageTime > 32)&&(GfMessageTime < 36))
                        {
                            VS_PRINT("Middle Dele,Time:%.2f,Id:%d,BumperDis:%.2f,MasDis:%d,DE_Sub:%d,NoMatchCnt:%d,Ch:%d\r\n",GfMessageTime,LpstrMapCoor->eMapObjId,\
                                LfNearPointToBumperDis,GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh].u16MasDis,\
                                Lu16ToBumperDisAndDE_Sub,LpstrMapCoor->u8SideMapToCarInnerDeNoMatchCnt,Lu8SnsCh);
                        }
#endif
                    }
                }
                else
                {
                    LpstrMapCoor->u8SideMapToCarInnerDeNoMatchCnt = 0;
                }
            }
            else
            {
                LpstrMapCoor->u8SideMapToCarInnerDeNoMatchCnt = 0;
            }
            if(LpstrMapCoor->u8SideMapToCarInnerDeNoMatchCnt > SIDE_MAP_INNER_DE_NO_MATCH_DELE_CNT)
            {
                Lu8MapNeedDeleteFlag = 1;
                return Lu8MapNeedDeleteFlag;
            }
        }
        else if((LfMapP1_Y_ABS_Value > LfSideCarInnerAreaY_Limit)&&(LfMapP2_Y_ABS_Value > LfSideCarInnerAreaY_Limit))
        {
#if 0
            if((LpstrMapCoor->eMapObjId == 0x01)&&(GfMessageTime > 45)&&(GfMessageTime < 52))
            {
                if(GstrMapSnsRealTimeDis[1][4].u8RealDisUpdateFlag)
                {
                    VS_PRINT("Ourter Dele,Time:%.2f,Id:%d,Angle:%.2f,BumperDis:%.2f,P1_X:%.2f,P1_Y:%.2f,P2_X:%.2f,P2_Y:%.2f,MasDis:%d\r\n",GfMessageTime,LpstrMapCoor->eMapObjId,\
                        LpstrMapCoor->fP1_P2ToCarX_Angle*57.3,LpstrMapCoor->fMapToBumper_Dis,\
                        LpstrMapCoor->P1_CarCoor.fObjX,LpstrMapCoor->P1_CarCoor.fObjY,\
                        LpstrMapCoor->P2_CarCoor.fObjX,LpstrMapCoor->P2_CarCoor.fObjY,\
                        GstrMapSnsRealTimeDis[1][4].u16MasDis);
                }
            }
#endif
            LpstrMapCoor->u8SideMapToCarInnerDeNoMatchCnt = 0;
            LpstrMapCoor->u8SideMapToCarEdgeDeNoMatchCnt = 0;
            /* 以下再分三种情况 */
            if((LpstrMapCoor->P1_CarCoor.fObjY > 0)&&(LpstrMapCoor->P2_CarCoor.fObjY > 0))
            {
                /* P1 P2点都在车身左侧 */
                LpstrMapCoor->u8SideMapCoverCarDeNoMatchCnt = 0;
            }
            else if((LpstrMapCoor->P1_CarCoor.fObjY < 0)&&(LpstrMapCoor->P2_CarCoor.fObjY < 0))
            {
                /* P1 P2点都在车身右侧 */
                LpstrMapCoor->u8SideMapCoverCarDeNoMatchCnt = 0;
            }
            else
            {
                /* P1 P2点横跨车身，一个在左，一个在右 */
                /* 区分前保还是后保 */
                if(LfP1_P2ToCarX_AngleAbs > F_R_MAP_20_ANGLE)
                {
                    if(LpstrMapCoor->fP2_ToBumper_Dis < LpstrMapCoor->fP1_ToBumper_Dis)
                    {
                        if(LpstrMapCoor->P2_CarCoor.fObjX > LfFrontBumperX_Limit)
                        {
                            Lu8SnsGroup = 0x00;
                        }
                        else if(LpstrMapCoor->P2_CarCoor.fObjX < LfRearBumperX_Limit)
                        {
                            Lu8SnsGroup = 0x01;
                        }
                        else
                        {
                            Lu8SnsGroup = 0x02;
                        }
                    }
                    else
                    {
                        if(LpstrMapCoor->P1_CarCoor.fObjX > LfFrontBumperX_Limit)
                        {
                            Lu8SnsGroup = 0x00;
                        }
                        else if(LpstrMapCoor->P1_CarCoor.fObjX < LfRearBumperX_Limit)
                        {
                            Lu8SnsGroup = 0x01;
                        }
                        else
                        {
                            Lu8SnsGroup = 0x02;
                        }
                    }
                }
                else
                {
                    Lu8SnsGroup = 0x02;
                }

                float LfLineSegmentK;
                float LfSnsAtLineX_Coor[4];/* 后保4颗雷达到线段的映射，在线段上对应的X坐标 */
                float LfSnsToLineDis[4];
                uint8 i;

                if(Lu8SnsGroup == 0x00)
                {
                    /* Map横跨前保区域 */
                    LfLineSegmentK = (LpstrMapCoor->P1_CarCoor.fObjX-LpstrMapCoor->P2_CarCoor.fObjX)/(LpstrMapCoor->P1_CarCoor.fObjY-LpstrMapCoor->P2_CarCoor.fObjY);
                    for(i = 0; i < 4; i++)
                    {
                        LfSnsAtLineX_Coor[i] = LpstrMapCoor->P2_CarCoor.fObjX+((GstrRamSnsCoor[Lu8SnsGroup][i+1].cRadarY-LpstrMapCoor->P2_CarCoor.fObjY)*LfLineSegmentK);
                        LfSnsToLineDis[i] = LfSnsAtLineX_Coor[i] - GstrRamSnsCoor[Lu8SnsGroup][i+1].cRadarX;
                        if((LfSnsToLineDis[i] < SIDE_MAP_COVER_CAR_DELE_BUMPER_DIS)&&(GstrSnsCarMovSts[Lu8SnsGroup][0].eCarDir != SNS_CAR_BACKWARD))
                        {
                            if(GstrMapSnsRealTimeDis[Lu8SnsGroup][i+1].u8RealDisUpdateFlag)
                            {
                                if(LfSnsToLineDis[i] < GstrMapSnsRealTimeDis[Lu8SnsGroup][i+1].u16MasDis)
                                {
                                    Lu16ToBumperDisAndDE_Sub = ABS(LfSnsToLineDis[i],GstrMapSnsRealTimeDis[Lu8SnsGroup][i+1].u16MasDis);
                                }
                                else
                                {
                                    Lu16ToBumperDisAndDE_Sub = 0;
                                }
                                if(Lu16ToBumperDisAndDE_Sub > SIDE_MAP_COVER_CAR_DELE_DIS_SUB)
                                {
                                    LpstrMapCoor->u8SideMapCoverCarDeNoMatchCnt++;
                                }
                                else
                                {
                                    LpstrMapCoor->u8SideMapCoverCarDeNoMatchCnt = 0;
                                }
                            }
                        }
                        else
                        {
                            LpstrMapCoor->u8SideMapCoverCarDeNoMatchCnt = 0;
                        }
                    }
                }
                else if(Lu8SnsGroup == 0x01)
                {
                    LfLineSegmentK = (LpstrMapCoor->P1_CarCoor.fObjX-LpstrMapCoor->P2_CarCoor.fObjX)/(LpstrMapCoor->P1_CarCoor.fObjY-LpstrMapCoor->P2_CarCoor.fObjY);
                    for(i = 0; i < 4; i++)
                    {
                        LfSnsAtLineX_Coor[i] = LpstrMapCoor->P2_CarCoor.fObjX+((GstrRamSnsCoor[Lu8SnsGroup][i+1].cRadarY-LpstrMapCoor->P2_CarCoor.fObjY)*LfLineSegmentK);
                        LfSnsToLineDis[i] = LfSnsAtLineX_Coor[i] - GstrRamSnsCoor[Lu8SnsGroup][i+1].cRadarX;
                        if((LfSnsToLineDis[i] < SIDE_MAP_COVER_CAR_DELE_BUMPER_DIS)&&(GstrSnsCarMovSts[Lu8SnsGroup][0].eCarDir != SNS_CAR_FORWARD))
                        {
                            if(GstrMapSnsRealTimeDis[Lu8SnsGroup][i+1].u8RealDisUpdateFlag)
                            {
                                if(LfSnsToLineDis[i] < GstrMapSnsRealTimeDis[Lu8SnsGroup][i+1].u16MasDis)
                                {
                                    Lu16ToBumperDisAndDE_Sub = ABS(LfSnsToLineDis[i],GstrMapSnsRealTimeDis[Lu8SnsGroup][i+1].u16MasDis);
                                }
                                else
                                {
                                    Lu16ToBumperDisAndDE_Sub = 0;
                                }
                                if(Lu16ToBumperDisAndDE_Sub > SIDE_MAP_COVER_CAR_DELE_DIS_SUB)
                                {
                                    LpstrMapCoor->u8SideMapCoverCarDeNoMatchCnt++;
                                }
                                else
                                {
                                    LpstrMapCoor->u8SideMapCoverCarDeNoMatchCnt = 0;
                                }
                            }
                        }
                        else
                        {
                            LpstrMapCoor->u8SideMapCoverCarDeNoMatchCnt = 0;
                        }
                    }
                }
                else
                {
                    LpstrMapCoor->u8SideMapCoverCarDeNoMatchCnt = 0;
                }

                if(LpstrMapCoor->u8SideMapCoverCarDeNoMatchCnt > SIDE_MAP_COVER_CAR_DE_NO_MATCH_DELE_CNT)
                {
                    Lu8MapNeedDeleteFlag = 1;
                    return Lu8MapNeedDeleteFlag;
                }
            }
        }
        else
        {
            LpstrMapCoor->u8SideMapToCarInnerDeNoMatchCnt = 0;
            LpstrMapCoor->u8SideMapCoverCarDeNoMatchCnt = 0;
            uint8 Lu8InnerPoint = 0;
            float LfP1_P2_X_Sub,LfP1_P2_Y_Sub;
            float LfNewPointX,LfNewPointY;
            /* 一个在车内，一个在车外 */
            if(LfMapP1_Y_ABS_Value > LfMapP2_Y_ABS_Value)
            {
                LfNearPointX_Coor = LpstrMapCoor->P2_CarCoor.fObjX;
                LfNearPointY_Coor = LpstrMapCoor->P2_CarCoor.fObjY;
                LfNearPointY_CoorAbs = LfMapP2_Y_ABS_Value;
                LfNearPointToBumperDis = LpstrMapCoor->fP2_ToBumper_Dis;
                Lu8InnerPoint = 2;
            }
            else
            {
                LfNearPointX_Coor = LpstrMapCoor->P1_CarCoor.fObjX;
                LfNearPointY_Coor = LpstrMapCoor->P1_CarCoor.fObjY;
                LfNearPointY_CoorAbs = LfMapP1_Y_ABS_Value;
                LfNearPointToBumperDis = LpstrMapCoor->fP1_ToBumper_Dis;
                Lu8InnerPoint = 1;
            }

            if(LfNearPointX_Coor > LfFrontBumperX_Limit)
            {
                Lu8SnsGroup = 0x00;
            }
            else if(LfNearPointX_Coor < LfRearBumperX_Limit)
            {
                Lu8SnsGroup = 0x01;
            }
            else 
            {
                Lu8SnsGroup = 0x02;
            }

            if(Lu8SnsGroup == 0x00)
            {
                if((LfNearPointToBumperDis < SIDE_MAP_DEGE_DELE_BUMPER_DIS)&&(GstrSnsCarMovSts[Lu8SnsGroup][0].eCarDir != SNS_CAR_BACKWARD))
                {
                    /* 查询离该点最近的探头，用探头的DE来进行约束删除 */
                    if(LfNearPointY_Coor > 0)
                    {
                        Lu16NearPointToMiddleSnsY_Dis = ABS(LfNearPointY_Coor, GstrRamSnsCoor[Lu8SnsGroup][2].cRadarY);
                        Lu16NearPointToCornerSnsY_Dis = ABS(LfNearPointY_Coor, GstrRamSnsCoor[Lu8SnsGroup][1].cRadarY);
                        if(Lu16NearPointToMiddleSnsY_Dis < Lu16NearPointToCornerSnsY_Dis)
                        {
                            Lu8SnsCh = 0x02;
                        }
                        else
                        {
                            Lu8SnsCh = 0x01;
                        }
                    }
                    else
                    {
                        Lu16NearPointToMiddleSnsY_Dis = ABS(LfNearPointY_Coor, GstrRamSnsCoor[Lu8SnsGroup][3].cRadarY);
                        Lu16NearPointToCornerSnsY_Dis = ABS(LfNearPointY_Coor, GstrRamSnsCoor[Lu8SnsGroup][4].cRadarY);
                        if(Lu16NearPointToMiddleSnsY_Dis < Lu16NearPointToCornerSnsY_Dis)
                        {
                            Lu8SnsCh = 0x03;
                        }
                        else
                        {
                            Lu8SnsCh = 0x04;
                        }
                    }
                    if(GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh].u8RealDisUpdateFlag)
                    {
                        if(LfNearPointToBumperDis < GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh].u16MasDis)
                        {
                            Lu16ToBumperDisAndDE_Sub = ABS(LfNearPointToBumperDis,GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh].u16MasDis);
                        }
                        else
                        {
                            Lu16ToBumperDisAndDE_Sub = 0;
                        }
                        if(Lu16ToBumperDisAndDE_Sub > SIDE_MAP_EDGE_DELE_DIS_SUB)
                        {
                            LpstrMapCoor->u8SideMapToCarEdgeDeNoMatchCnt++;
                        }
                        else
                        {
                            LpstrMapCoor->u8SideMapToCarEdgeDeNoMatchCnt = 0;
                        }
                    }
                }
                else
                {
                    LpstrMapCoor->u8SideMapToCarEdgeDeNoMatchCnt = 0;
                }
            }
            else if(Lu8SnsGroup == 0x01)
            {
                if((LfNearPointToBumperDis < SIDE_MAP_DEGE_DELE_BUMPER_DIS)&&(GstrSnsCarMovSts[Lu8SnsGroup][0].eCarDir != SNS_CAR_FORWARD))
                {
                    /* 查询离该点最近的探头，用探头的DE来进行约束删除 */
                    if(LfNearPointY_Coor > 0)
                    {
                        Lu16NearPointToMiddleSnsY_Dis = ABS(LfNearPointY_Coor, GstrRamSnsCoor[Lu8SnsGroup][2].cRadarY);
                        Lu16NearPointToCornerSnsY_Dis = ABS(LfNearPointY_Coor, GstrRamSnsCoor[Lu8SnsGroup][1].cRadarY);
                        if(Lu16NearPointToMiddleSnsY_Dis < Lu16NearPointToCornerSnsY_Dis)
                        {
                            Lu8SnsCh = 0x02;
                        }
                        else
                        {
                            Lu8SnsCh = 0x01;
                        }
                    }
                    else
                    {
                        Lu16NearPointToMiddleSnsY_Dis = ABS(LfNearPointY_Coor, GstrRamSnsCoor[Lu8SnsGroup][3].cRadarY);
                        Lu16NearPointToCornerSnsY_Dis = ABS(LfNearPointY_Coor, GstrRamSnsCoor[Lu8SnsGroup][4].cRadarY);
                        if(Lu16NearPointToMiddleSnsY_Dis < Lu16NearPointToCornerSnsY_Dis)
                        {
                            Lu8SnsCh = 0x03;
                        }
                        else
                        {
                            Lu8SnsCh = 0x04;
                        }
                    }
                    if(GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh].u8RealDisUpdateFlag)
                    {
                        if(LfNearPointToBumperDis < GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh].u16MasDis)
                        {
                            Lu16ToBumperDisAndDE_Sub = ABS(LfNearPointToBumperDis,GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh].u16MasDis);
                        }
                        else
                        {
                            Lu16ToBumperDisAndDE_Sub = 0;
                        }
                        if(Lu16ToBumperDisAndDE_Sub > SIDE_MAP_EDGE_DELE_DIS_SUB)
                        {
                            LpstrMapCoor->u8SideMapToCarEdgeDeNoMatchCnt++;
                        }
                        else
                        {
                            LpstrMapCoor->u8SideMapToCarEdgeDeNoMatchCnt = 0;
                        }

#if 0
                        if((LpstrMapCoor->eMapObjId == 0x00)&&(GfMessageTime > 30)&&(GfMessageTime < 33))
                        {
                            if(GstrMapSnsRealTimeDis[1][3].u8RealDisUpdateFlag)
                            {
                                VS_PRINT("Ourter Dele,Time:%.2f,Id:%d,Angle:%.2f,BumperDis:%.2f,P1_X:%.2f,P1_Y:%.2f,P2_X:%.2f,P2_Y:%.2f,MasDis:%d,Dir:%d,Cnt:%d\r\n",GfMessageTime,LpstrMapCoor->eMapObjId,\
                                    LpstrMapCoor->fP1_P2ToCarX_Angle*57.3,LpstrMapCoor->fMapToBumper_Dis,\
                                    LpstrMapCoor->P1_CarCoor.fObjX,LpstrMapCoor->P1_CarCoor.fObjY,\
                                    LpstrMapCoor->P2_CarCoor.fObjX,LpstrMapCoor->P2_CarCoor.fObjY,\
                                    GstrMapSnsRealTimeDis[1][3].u16MasDis,GstrSnsCarMovSts[Lu8SnsGroup][2].eCarDir,\
                                    LpstrMapCoor->u8SideMapToCarEdgeDeNoMatchCnt);
                            }
                        }
#endif
                    }
                }
                else
                {
                    LpstrMapCoor->u8SideMapToCarEdgeDeNoMatchCnt = 0;
                }
            }
            else
            {
                LpstrMapCoor->u8SideMapToCarEdgeDeNoMatchCnt = 0;
            }
            
            if(LpstrMapCoor->u8SideMapToCarEdgeDeNoMatchCnt >= SIDE_MAP_EDGE_DE_NO_MATCH_DELE_CNT)
            {
                /** @brief: 恢复为直接删除策略--2024-11-29 */
#if 0
                /* 修改为裁剪入侵的部分，而不是直接删除，防止柱子直接被删除 */
                LpstrMapCoor->u8SideMapToCarEdgeDeNoMatchCnt = 0;
                LfSideCarInnerAreaY_Limit = GstrRamSnsCoor[1][0].cRadarY+100;
                uint8 Lu8ModifyFlag = 0;
                
                if(LfMapP1_Y_ABS_Value < LfSideCarInnerAreaY_Limit)
                {
                    /* P1 入侵，矫正P1点 */
                    LfP1_P2_X_Sub = LpstrMapCoor->P1_CarCoor.fObjX - LpstrMapCoor->P2_CarCoor.fObjX;
                    LfP1_P2_Y_Sub = LpstrMapCoor->P1_CarCoor.fObjY - LpstrMapCoor->P2_CarCoor.fObjY;
                    if(LpstrMapCoor->P2_CarCoor.fObjY > 0)
                    {
                        LfNewPointY = GstrRamSnsCoor[1][0].cRadarY+50;
                    }
                    else
                    {
                        LfNewPointY = -(GstrRamSnsCoor[1][0].cRadarY+50);
                    }
                    LpstrMapCoor->P1_CarCoor.fObjY = LfNewPointY;
                    
                    LfNewPointY = LfNewPointY - LpstrMapCoor->P2_CarCoor.fObjY;
                    LfNewPointX = (LfNewPointY*LfP1_P2_X_Sub)/LfP1_P2_Y_Sub;
                    LfNewPointX = LfNewPointX + LpstrMapCoor->P2_CarCoor.fObjX;
                    LpstrMapCoor->P1_CarCoor.fObjX = LfNewPointX;
                    PubAI_TransObjCarCoorToOdoCoor(&LpstrMapCoor->P1_CarCoor.fObjX,&LpstrMapCoor->P1_CarCoor.fObjY,\
                        &LpstrMapCoor->P1_OdoCoor.fObjX,&LpstrMapCoor->P1_OdoCoor.fObjY);
                    Lu8ModifyFlag = 1;
                }
                else if(LfMapP2_Y_ABS_Value < LfSideCarInnerAreaY_Limit)
                {
                    /* P2入侵，矫正P2点 */
                    LfP1_P2_X_Sub = LpstrMapCoor->P2_CarCoor.fObjX - LpstrMapCoor->P1_CarCoor.fObjX;
                    LfP1_P2_Y_Sub = LpstrMapCoor->P2_CarCoor.fObjY - LpstrMapCoor->P1_CarCoor.fObjY;
                    if(LpstrMapCoor->P1_CarCoor.fObjY > 0)
                    {
                        LfNewPointY = GstrRamSnsCoor[1][0].cRadarY+50;
                    }
                    else
                    {
                        LfNewPointY = -(GstrRamSnsCoor[1][0].cRadarY+50);
                    }
                    LpstrMapCoor->P2_CarCoor.fObjY = LfNewPointY;
                    
                    LfNewPointY = LfNewPointY - LpstrMapCoor->P1_CarCoor.fObjY;
                    LfNewPointX = (LfNewPointY*LfP1_P2_X_Sub)/LfP1_P2_Y_Sub;
                    LfNewPointX = LfNewPointX + LpstrMapCoor->P1_CarCoor.fObjX;
                    LpstrMapCoor->P2_CarCoor.fObjX = LfNewPointX;
                    PubAI_TransObjCarCoorToOdoCoor(&LpstrMapCoor->P2_CarCoor.fObjX,&LpstrMapCoor->P2_CarCoor.fObjY,\
                        &LpstrMapCoor->P2_OdoCoor.fObjX,&LpstrMapCoor->P2_OdoCoor.fObjY);
                    Lu8ModifyFlag = 1;
                }
                if(GenuMapVheiCfgLevel == MAP_VEH_MAX)
                //if(1)
                {
                    if(!Lu8ModifyFlag)
                    {
                        if(LfNearPointToBumperDis < SIDE_MAP_DEGE_DELE_BUMPER_DIS02)
                        {
                            LfSideCarInnerAreaY_Limit = GstrRamSnsCoor[1][0].cRadarY+300;
                            if(LfMapP1_Y_ABS_Value < LfSideCarInnerAreaY_Limit)
                            {
                                /* P1 入侵，矫正P1点 */
                                LfP1_P2_X_Sub = LpstrMapCoor->P1_CarCoor.fObjX - LpstrMapCoor->P2_CarCoor.fObjX;
                                LfP1_P2_Y_Sub = LpstrMapCoor->P1_CarCoor.fObjY - LpstrMapCoor->P2_CarCoor.fObjY;
                                if(LpstrMapCoor->P2_CarCoor.fObjY > 0)
                                {
                                    LfNewPointY = GstrRamSnsCoor[1][0].cRadarY+300;
                                }
                                else
                                {
                                    LfNewPointY = -(GstrRamSnsCoor[1][0].cRadarY+300);
                                }
                                LpstrMapCoor->P1_CarCoor.fObjY = LfNewPointY;
                                
                                LfNewPointY = LfNewPointY - LpstrMapCoor->P2_CarCoor.fObjY;
                                LfNewPointX = (LfNewPointY*LfP1_P2_X_Sub)/LfP1_P2_Y_Sub;
                                LfNewPointX = LfNewPointX + LpstrMapCoor->P2_CarCoor.fObjX;
                                LpstrMapCoor->P1_CarCoor.fObjX = LfNewPointX;
                                PubAI_TransObjCarCoorToOdoCoor(&LpstrMapCoor->P1_CarCoor.fObjX,&LpstrMapCoor->P1_CarCoor.fObjY,\
                                    &LpstrMapCoor->P1_OdoCoor.fObjX,&LpstrMapCoor->P1_OdoCoor.fObjY);
                                Lu8ModifyFlag = 1;
                            }
                            else if(LfMapP2_Y_ABS_Value < LfSideCarInnerAreaY_Limit)
                            {
                                /* P2入侵，矫正P2点 */
                                LfP1_P2_X_Sub = LpstrMapCoor->P2_CarCoor.fObjX - LpstrMapCoor->P1_CarCoor.fObjX;
                                LfP1_P2_Y_Sub = LpstrMapCoor->P2_CarCoor.fObjY - LpstrMapCoor->P1_CarCoor.fObjY;
                                if(LpstrMapCoor->P1_CarCoor.fObjY > 0)
                                {
                                    LfNewPointY = GstrRamSnsCoor[1][0].cRadarY+300;
                                }
                                else
                                {
                                    LfNewPointY = -(GstrRamSnsCoor[1][0].cRadarY+300);
                                }
                                LpstrMapCoor->P2_CarCoor.fObjY = LfNewPointY;
                                
                                LfNewPointY = LfNewPointY - LpstrMapCoor->P1_CarCoor.fObjY;
                                LfNewPointX = (LfNewPointY*LfP1_P2_X_Sub)/LfP1_P2_Y_Sub;
                                LfNewPointX = LfNewPointX + LpstrMapCoor->P1_CarCoor.fObjX;
                                LpstrMapCoor->P2_CarCoor.fObjX = LfNewPointX;
                                PubAI_TransObjCarCoorToOdoCoor(&LpstrMapCoor->P2_CarCoor.fObjX,&LpstrMapCoor->P2_CarCoor.fObjY,\
                                    &LpstrMapCoor->P2_OdoCoor.fObjX,&LpstrMapCoor->P2_OdoCoor.fObjY);
                                Lu8ModifyFlag = 1;
                            }
                        }
                    }
                }

#else
                Lu8MapNeedDeleteFlag = 1;
                return Lu8MapNeedDeleteFlag;
#endif
            }


#if 0
            if((LpstrMapCoor->eMapObjId == 0x01))
            {
                if(GstrMapSnsRealTimeDis[1][5].u8RealDisUpdateFlag)
                {
                    VS_PRINT("Inner Dele,Time:%.2f,Id:%d,Angle:%.2f,BumperDis:%.2f,NearPointY_CoorAbs:%.3f,NearPointX_Coor:%.3f,NearPointY_Coor:%.3f,ParkDir:%d,NewFlag:%d\r\n",GfMessageTime,LpstrMapCoor->eMapObjId,\
                        LfP1_P2ToCarX_AngleAbs*57.3,LpstrMapCoor->fMapToBumper_Dis,\
                        LfNearPointY_CoorAbs,LfNearPointX_Coor,LfNearPointY_Coor,GstrParkingGuidenceData.eParkDir,GstrSideAreaMap[SIDE_SNS_RRS].u8NewObjCoorFlag);
                }
            }
#endif


            /* 针对柱子入侵，提前使用侧边稳定坐标去进行矫正--暂时屏蔽--2024-07-01 */
            float LfPointToLineDis;
            uint8 Lu8PointInLineMiddleFlag = 0;
            if(LfP1_P2ToCarX_AngleAbs > F_R_MAP_40_ANGLE)
            {
                if((LfNearPointY_CoorAbs < 1500)&&(LfNearPointX_Coor < (GstrRamSnsCoor[1][0].cRadarX+100)))
                {
                    if(LpstrMapCoor->fMapToBumper_Dis < 1000)
                    {
                        if(LfNearPointY_Coor > 0)
                        {
                            Lu8SnsCh = SIDE_SNS_RLS;/* RLS 探头 */
                        }
                        else
                        {
                            Lu8SnsCh = SIDE_SNS_RRS;/* RRS 探头 */
                        }
                        
                        if(GstrSideAreaMap[Lu8SnsCh].u8NewObjCoorFlag)
                        {
                            LfPointToLineDis = PubAI_CalOnePointToSegmentDisAndRelation(GstrSideAreaMap[Lu8SnsCh].P1_CarCoor.fObjX,GstrSideAreaMap[Lu8SnsCh].P1_CarCoor.fObjY,\
                                LpstrMapCoor->P1_CarCoor.fObjX,LpstrMapCoor->P1_CarCoor.fObjY,LpstrMapCoor->P2_CarCoor.fObjX,LpstrMapCoor->P2_CarCoor.fObjY,&Lu8PointInLineMiddleFlag);
#if 0
                            if((LpstrMapCoor->eMapObjId == 0x01))
                            {
                                VS_PRINT("New Point,Time:%.2f,Id:%d,Angle:%.2f,BumperDis:%.2f,P1_X:%.2f,P1_Y:%.2f,P2_X:%.2f,P2_Y:%.2f,New_Y:%.3f,PointToLineDis:%.3f,Flag:%d,NewCoor:%.3f,%.3f\r\n",GfMessageTime,LpstrMapCoor->eMapObjId,\
                                    LpstrMapCoor->fP1_P2ToCarX_Angle*57.3,LpstrMapCoor->fMapToBumper_Dis,\
                                    LpstrMapCoor->P1_CarCoor.fObjX,LpstrMapCoor->P1_CarCoor.fObjY,\
                                    LpstrMapCoor->P2_CarCoor.fObjX,LpstrMapCoor->P2_CarCoor.fObjY,\
                                    GstrSideAreaMap[SIDE_SNS_RRS].P1_CarCoor.fObjY,LfPointToLineDis,Lu8PointInLineMiddleFlag,\
                                    GstrSideAreaMap[Lu8SnsCh].P1_CarCoor.fObjX,GstrSideAreaMap[Lu8SnsCh].P1_CarCoor.fObjY);
                            }
#endif
                            if(Lu8PointInLineMiddleFlag)
                            {
                                /* 新构成的点在线上，则可以采用新点裁剪策略，具体分2种情况:点离线比较近(接近在线上，则用新点直接更新)
                                   若是差值超过10cm，则按照点到线的垂直距离进行裁剪*/
                                if(LfPointToLineDis < 100)
                                {
                                    if(Lu8InnerPoint == 1)
                                    {
                                        /* 矫正P1 点 */
                                        LpstrMapCoor->P1_CarCoor.fObjX = GstrSideAreaMap[Lu8SnsCh].P1_CarCoor.fObjX;
                                        LpstrMapCoor->P1_CarCoor.fObjY = GstrSideAreaMap[Lu8SnsCh].P1_CarCoor.fObjY;
                                        PubAI_TransObjCarCoorToOdoCoor(&LpstrMapCoor->P1_CarCoor.fObjX,&LpstrMapCoor->P1_CarCoor.fObjY,\
                                            &LpstrMapCoor->P1_OdoCoor.fObjX,&LpstrMapCoor->P1_OdoCoor.fObjY);
                                    }
                                    else if(Lu8InnerPoint == 2)
                                    {
                                        /* 矫正P2 点 */
                                        LpstrMapCoor->P2_CarCoor.fObjX = GstrSideAreaMap[Lu8SnsCh].P1_CarCoor.fObjX;
                                        LpstrMapCoor->P2_CarCoor.fObjY = GstrSideAreaMap[Lu8SnsCh].P1_CarCoor.fObjY;
                                        PubAI_TransObjCarCoorToOdoCoor(&LpstrMapCoor->P2_CarCoor.fObjX,&LpstrMapCoor->P2_CarCoor.fObjY,\
                                            &LpstrMapCoor->P2_OdoCoor.fObjX,&LpstrMapCoor->P2_OdoCoor.fObjY);
                                    }
                                }
                                else if(LfPointToLineDis < 600)
                                {
                                    LfNewPointX,LfNewPointY;
                                    float LfNewPointToP1_Dis;
                                    float LfNewOccuredPointToP1_Dis;
                                    
                                    LfNewPointToP1_Dis = PubAI_CalTwoPointDis(LpstrMapCoor->P1_CarCoor.fObjX,LpstrMapCoor->P1_CarCoor.fObjY,\
                                        GstrSideAreaMap[Lu8SnsCh].P1_CarCoor.fObjX,GstrSideAreaMap[Lu8SnsCh].P1_CarCoor.fObjY);
                                    LfNewOccuredPointToP1_Dis = LfNewPointToP1_Dis*LfNewPointToP1_Dis - LfPointToLineDis*LfPointToLineDis;
                                    LfNewOccuredPointToP1_Dis = powf(LfNewOccuredPointToP1_Dis,0.5);
                                    LfNewPointX = ((LpstrMapCoor->P1_CarCoor.fObjX - LpstrMapCoor->P2_CarCoor.fObjX)*LfNewOccuredPointToP1_Dis)/LpstrMapCoor->fP1_P2_Dis;
                                    LfNewPointX = LpstrMapCoor->P1_CarCoor.fObjX - LfNewPointX;

                                    LfNewPointY = ((LpstrMapCoor->P1_CarCoor.fObjY - LpstrMapCoor->P2_CarCoor.fObjY)*LfNewOccuredPointToP1_Dis)/LpstrMapCoor->fP1_P2_Dis;
                                    LfNewPointY = LpstrMapCoor->P1_CarCoor.fObjY - LfNewPointY;
                                
                                    if(Lu8InnerPoint == 1)
                                    {
                                        /* 矫正P1 点 */
                                        LpstrMapCoor->P1_CarCoor.fObjX = LfNewPointX;
                                        LpstrMapCoor->P1_CarCoor.fObjY = LfNewPointY;
                                        PubAI_TransObjCarCoorToOdoCoor(&LpstrMapCoor->P1_CarCoor.fObjX,&LpstrMapCoor->P1_CarCoor.fObjY,\
                                            &LpstrMapCoor->P1_OdoCoor.fObjX,&LpstrMapCoor->P1_OdoCoor.fObjY);
                                    }
                                    else if(Lu8InnerPoint == 2)
                                    {
                                        /* 矫正P2 点 */
                                        LpstrMapCoor->P2_CarCoor.fObjX = LfNewPointX;
                                        LpstrMapCoor->P2_CarCoor.fObjY = LfNewPointY;
                                        PubAI_TransObjCarCoorToOdoCoor(&LpstrMapCoor->P2_CarCoor.fObjX,&LpstrMapCoor->P2_CarCoor.fObjY,\
                                            &LpstrMapCoor->P2_OdoCoor.fObjX,&LpstrMapCoor->P2_OdoCoor.fObjY);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        /* 针对Search阶段识别的低矮，在入库阶段重新置低的处理 */
#if 1
        if(LpstrMapCoor->u8SideMapLowTagFlag)
        {
            LfSideCarInnerAreaY_Limit = GstrRamSnsCoor[1][0].cRadarY+100;
            LfRearBumperX_Limit = GstrRamSnsCoor[1][0].cRadarX-250;
            if((LfMapP1_Y_ABS_Value < LfSideCarInnerAreaY_Limit)&&(LfMapP2_Y_ABS_Value < LfSideCarInnerAreaY_Limit))
            {
                /* 区分前保还是后保 */
                if((LpstrMapCoor->P1_CarCoor.fObjX < LfRearBumperX_Limit)&&(LpstrMapCoor->P2_CarCoor.fObjX < LfRearBumperX_Limit))
                {
                    Lu8SnsGroup = 0x01;
                }
                else 
                {
                    Lu8SnsGroup = 0x02;
                }
        
                /* 选择离保杠最近的点 */
                if(LpstrMapCoor->fP2_ToBumper_Dis < LpstrMapCoor->fP1_ToBumper_Dis)
                {
                    LfNearPointX_Coor = LpstrMapCoor->P2_CarCoor.fObjX;
                    LfNearPointY_Coor = LpstrMapCoor->P2_CarCoor.fObjY;
                    LfNearPointY_CoorAbs = LfMapP2_Y_ABS_Value;
                    LfNearPointToBumperDis = LpstrMapCoor->fP2_ToBumper_Dis;
                }
                else
                {
                    LfNearPointX_Coor = LpstrMapCoor->P1_CarCoor.fObjX;
                    LfNearPointY_Coor = LpstrMapCoor->P1_CarCoor.fObjY;
                    LfNearPointY_CoorAbs = LfMapP1_Y_ABS_Value;
                    LfNearPointToBumperDis = LpstrMapCoor->fP1_ToBumper_Dis;
                }
                
                /* 判断和车身的角度，根据角度决定选择一个点还是2个点的坐标进行匹配 */
                if(Lu8SnsGroup == 0x01)
                {
                    Lu8SnsCh = FindMapPointNearSnsCh(Lu8SnsGroup, LfNearPointY_Coor);
                    if((LfNearPointToBumperDis < SIDE_MAP_LOW_MATCH_BUMPER_MAX_DIS)&&(LfNearPointToBumperDis > SIDE_MAP_LOW_MATCH_BUMPER_MIN_DIS)&&\
                        (GstrSnsCarMovSts[Lu8SnsGroup][0].eCarDir != SNS_CAR_FORWARD))
                    {
                        /* 查询离该点最近的探头，用探头的DE来进行约束删除 */
                        if(GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh].u8RealDisUpdateFlag)
                        {
                            Lu16ToBumperDisAndDE_Sub = ABS(LfNearPointToBumperDis,GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh].u16MasDis);
                            if(Lu16ToBumperDisAndDE_Sub > SIDE_LOW_MAP_JUDGE_DIS_SUB)
                            {
                                if(LpstrMapCoor->eMapObjHeight != OBJ_LOW)
                                {
                                    LpstrMapCoor->u8SideLowMapDeNoMatchCnt++;
                                }
                                LpstrMapCoor->u8SideLowMapDeMatchOKCnt = 0;
                            }
                            else
                            {
                                LpstrMapCoor->u8SideLowMapDeNoMatchCnt = 0;
                                if(GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh].u16MasDis < SIDE_MAP_LOW_MATCH_BUMPER_OK_DIS)
                                {
                                    LpstrMapCoor->u8SideLowMapDeMatchOKCnt++;
                                }
                            }
                        }
                        if(GstrParkingGuidenceData.fCarAngleSub < F_R_MAP_70_ANGLE)
                        {
                            /* 根据泊车方向，进行不同的区域限制 */
                            if(GstrParkingGuidenceData.eParkDir == PARK_DIR_RIGHT)
                            {
                                /* 右侧泊车，绕地锁一侧，地锁位置偏右，至少有一个点的Y坐标为负 */
                                if((LpstrMapCoor->P1_CarCoor.fObjY < 0)||(LpstrMapCoor->P2_CarCoor.fObjY < 0))
                                {
                                    LpstrMapCoor->eMapObjHeight = OBJ_LOW;
                                }
                            }
                            else if(GstrParkingGuidenceData.eParkDir == PARK_DIR_LEFT)
                            {
                                /* 左侧泊车，绕地锁一侧，地锁位置偏左，至少有一个点的Y坐标为负 */
                                if((LpstrMapCoor->P1_CarCoor.fObjY > 0)||(LpstrMapCoor->P2_CarCoor.fObjY > 0))
                                {
                                    LpstrMapCoor->eMapObjHeight = OBJ_LOW;
                                }
                            }
                            else
                            {
                                LpstrMapCoor->eMapObjHeight = OBJ_LOW;
                            }
                        }
                        else
                        {
                            LpstrMapCoor->eMapObjHeight = OBJ_LOW;
                        }
                    }
                    else
                    {
                        LpstrMapCoor->u8SideMapToCarInnerDeNoMatchCnt = 0;
                        LpstrMapCoor->u8SideLowMapDeMatchOKCnt = 0;
                    }
#if 0
                    //if((LpstrMapCoor->eMapObjId == 0x02)&&(GfMessageTime > 30)&&(GfMessageTime < 35))
                    if((LpstrMapCoor->eMapObjId == 0x02))
                    {
                        if((GstrMapSnsRealTimeDis[1][3].u8RealDisUpdateFlag)||(GstrMapSnsRealTimeDis[1][4].u8RealDisUpdateFlag))
                        {
                            VS_PRINT("Low Map,Time:%.2f,Id:%d,BumperDis:%.2f,Angle:%.3f,MasDis_RL:%d,MasDis_RML:%d,Ch:%d,LowMapDeNoMatchCnt:%d\r\n",GfMessageTime,LpstrMapCoor->eMapObjId,\
                                LfNearPointToBumperDis,LfP1_P2ToCarX_AngleAbs*57.3,GstrMapSnsRealTimeDis[Lu8SnsGroup][1].u16MasDis,\
                                GstrMapSnsRealTimeDis[Lu8SnsGroup][2].u16MasDis,Lu8SnsCh,LpstrMapCoor->u8SideLowMapDeNoMatchCnt);
                        }
                    }
#endif
                }
                else
                {
                    LpstrMapCoor->u8SideLowMapDeNoMatchCnt = 0;
                }
                
                if(LpstrMapCoor->u8SideLowMapDeMatchOKCnt >= 2)
                {
                    LpstrMapCoor->u8SideLowMapDeMatchOKCnt = 0;
                    LpstrMapCoor->eMapObjHeight = OBJ_HIGH;
                    LpstrMapCoor->u8SideMapLowTagFlag = 0;
                }
            }
            else
            {
                LpstrMapCoor->u8SideLowMapDeNoMatchCnt = 0;
                LpstrMapCoor->u8SideLowMapDeMatchOKCnt = 0;
            }
        }
#endif
    }

    /* 判断短Map点是否在侧边探头的FOV 内 */
    LpstrMapCoor->enuShortMapInSideSnsFov = SIDE_SNS_NONE;
    if((LpstrMapCoor->fP1_P2_Dis < 50)&&(LpstrMapCoor->enuSideMapUpdateSts != SIDE_MAP_UPDATE_NONE)\
        &&(LpstrMapCoor->fMapToBumper_Dis < 1500))
    {
        if((LpstrMapCoor->P1_CarCoor.fObjX > (GstrRamSnsCoor[0][0].cRadarX - SIDE_SHORT_MAP_IN_FOV_DIS))&&\
            (LpstrMapCoor->P1_CarCoor.fObjX < (GstrRamSnsCoor[0][0].cRadarX + SIDE_SHORT_MAP_IN_FOV_DIS)))
        {
            if(LpstrMapCoor->P1_CarCoor.fObjY > 0)
            {
                LpstrMapCoor->enuShortMapInSideSnsFov = SIDE_SNS_FLS;
                Lu8SnsGroup = 0;
                Lu8SnsCh = 0;
            }
            else
            {
                LpstrMapCoor->enuShortMapInSideSnsFov = SIDE_SNS_FRS;
                Lu8SnsGroup = 0;
                Lu8SnsCh = 5;
            }
        }
        else if((LpstrMapCoor->P1_CarCoor.fObjX > (GstrRamSnsCoor[1][0].cRadarX - SIDE_SHORT_MAP_IN_FOV_DIS))&&\
            (LpstrMapCoor->P1_CarCoor.fObjX < (GstrRamSnsCoor[1][0].cRadarX + SIDE_SHORT_MAP_IN_FOV_DIS)))
        {
            if(LpstrMapCoor->P1_CarCoor.fObjY > 0)
            {
                LpstrMapCoor->enuShortMapInSideSnsFov = SIDE_SNS_RLS;
                Lu8SnsGroup = 1;
                Lu8SnsCh = 0;
            }
            else
            {
                LpstrMapCoor->enuShortMapInSideSnsFov = SIDE_SNS_RRS;
                Lu8SnsGroup = 1;
                Lu8SnsCh = 5;
            }
        }
        if(LpstrMapCoor->enuShortMapInSideSnsFov != SIDE_SNS_NONE)
        {            
            if(GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh].u8RealDisUpdateFlag)
            {
                Lu16ToBumperDisAndDE_Sub = ABS(LpstrMapCoor->fMapToBumper_Dis,GstrMapSnsRealTimeDis[Lu8SnsGroup][Lu8SnsCh].u16MasDis);
                if(Lu16ToBumperDisAndDE_Sub > SIDE_SHORT_MAP_DIS_SUB)
                {
                    LpstrMapCoor->u8SideShortMapFovNoMatchCnt++;
                }
                else
                {
                    LpstrMapCoor->u8SideShortMapFovNoMatchCnt = 0;
                }
            }
        }
        else
        {
            LpstrMapCoor->u8SideShortMapFovNoMatchCnt = 0;
        }
    }
    else
    {
        LpstrMapCoor->u8SideShortMapFovNoMatchCnt = 0;
    }
    if(LpstrMapCoor->u8SideShortMapFovNoMatchCnt > SIDE_SHORT_MAP_DE_NO_MATCH_CNT)
    {
        Lu8MapNeedDeleteFlag = 1;
    }

    return Lu8MapNeedDeleteFlag;
}



/******************************************************************************
 * 函数名称: FindLineMapFusionPoint
 * 
 * 功能描述: 查询融合Line符合的点
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-01-16 18:28   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void FindLineMapFusionPoint(ObjMapCoorType *LpstrMapCoor)
{
    uint8 Lu8SnsGroup = 2;
    MapLineBufType *LpsrMapLineBuf;
    uint8 i;
    float LfP1_Coor_Y_Abs;
    float LfFusionLineY_Limit;
    float LfLine_X_CoorSub;
    float LfLine_Y_CoorSub;
    uint8 Lu8PointCanFusionFlag = 0;

    if(GstrSnsCarMovSts[0][0].eCarDir == SNS_CAR_FORWARD)
    {
        Lu8SnsGroup = 0;
    }
    else if(GstrSnsCarMovSts[0][0].eCarDir == SNS_CAR_BACKWARD)
    {
        Lu8SnsGroup = 1;
    }


    LfFusionLineY_Limit = GstrRamSnsCoor[0][0].cRadarY + 100;

    if(Lu8SnsGroup < 2)
    {
        if((LpstrMapCoor->eMapObjType ==  OBJ_TYPE_POINT)&&(LpstrMapCoor->eObjExistProb > OBJ_PROB_LV3))
        {
            if((LpstrMapCoor->fMapToBumper_Dis < 2000)&&(LpstrMapCoor->eObjBumperArea == Lu8SnsGroup))
            {
                LfP1_Coor_Y_Abs = ABS_VALUE(LpstrMapCoor->P1_CarCoor.fObjY);
                if(LfP1_Coor_Y_Abs < LfFusionLineY_Limit)
                {
                    Lu8PointCanFusionFlag = 1;
                }
            }
        }
    }

    if((Lu8SnsGroup < 2)&&(Lu8PointCanFusionFlag))
    {
        LpsrMapLineBuf = &GstrMapLineBuf[Lu8SnsGroup];
        if(LpsrMapLineBuf->u8WaitFusionLineCnt > 0)
        {
            for(i = 0; i < LpsrMapLineBuf->u8WaitFusionLineCnt; i++)
            {
                if(LpstrMapCoor->eMapObjId != LpsrMapLineBuf->LineMapUnit[i].eLineMapId)
                {
                    LfLine_X_CoorSub = ABS(LpstrMapCoor->P1_CarCoor.fObjX,LpsrMapLineBuf->LineMapUnit[i].fLineMapX_Coor);
                    if(LfLine_X_CoorSub < LINE_MAP_FUSION_X_COOR_SUB)
                    {
                        if(LpstrMapCoor->P1_CarCoor.fObjY > LpsrMapLineBuf->LineMapUnit[i].fLineLeftY_Coor)
                        {
                            LfLine_Y_CoorSub = ABS(LpstrMapCoor->P1_CarCoor.fObjY,LpsrMapLineBuf->LineMapUnit[i].fLineLeftY_Coor);
                        }
                        else if(LpstrMapCoor->P1_CarCoor.fObjY < LpsrMapLineBuf->LineMapUnit[i].fLineRightY_Coor)
                        {
                            LfLine_Y_CoorSub = ABS(LpstrMapCoor->P1_CarCoor.fObjY,LpsrMapLineBuf->LineMapUnit[i].fLineRightY_Coor);
                        }
                        else
                        {
                            LfLine_Y_CoorSub = 0;
                        }
                        if(LfLine_Y_CoorSub < LINE_MAP_FUSION_Y_COOR_SUB)
                        {
                            if(LpsrMapLineBuf->LineMapUnit[i].u8BeFusionMapCnt < MAP_LINE_FUSION_POINT_CNT)
                            {
                                LpsrMapLineBuf->LineMapUnit[i].eBeFusionMapId[LpsrMapLineBuf->LineMapUnit[i].u8BeFusionMapCnt] = LpstrMapCoor->eMapObjId;
                                LpsrMapLineBuf->LineMapUnit[i].u8BeFusionMapCnt++;
                                LpsrMapLineBuf->LineMapUnit[i].u8LineMapWaitFusionFlag = 1;
                            }
                            break;
                        }
                    }
                }
            }
        }
    }
}
static uint8 JudgeMapDispalySideArea(float LfObj_X,float LfObj_Y)
{
    uint8 Lu8MapInSideAreaFlag = 0;

    if(LfObj_X < GstrRamSnsCoor[0][2].cRadarX)
    {
        if(LfObj_Y > 700)
        {
            Lu8MapInSideAreaFlag = 1;
        }
        else if(LfObj_Y < -700)
        {
            Lu8MapInSideAreaFlag = 2;
        }
        else
        {
            if(LfObj_X > GstrRamSnsCoor[1][2].cRadarX)
            {
                if(LfObj_Y > 0)
                {
                    Lu8MapInSideAreaFlag = 1;
                }
                else
                {
                    Lu8MapInSideAreaFlag = 2;
                }
            }
            else if(LfObj_X > (GstrRamSnsCoor[1][2].cRadarX-1400))
            {
                Lu8MapInSideAreaFlag = 0;
            }
            else 
            {
                if(LfObj_Y > (GstrRamSnsCoor[1][0].cRadarY-200))
                {
                    Lu8MapInSideAreaFlag = 1;
                }
                else if(LfObj_Y < (GstrRamSnsCoor[1][5].cRadarY+200))
                {
                    Lu8MapInSideAreaFlag = 2;
                }
                else
                {
                    Lu8MapInSideAreaFlag = 0;
                }
            }
        }
    }
    else
    {
        Lu8MapInSideAreaFlag = 0;
    }

    return Lu8MapInSideAreaFlag;
}



/******************************************************************************
 * 函数名称: UpdateMapInf
 * 
 * 功能描述: 更新Map的细节信息
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-22 19:15   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static uint8 UpdateMapInf(ObjMapCoorType *LpstrMapCoor)
{
    uint8 Lu8MapNeedDeleteFlag1 = 0;
    uint8 Lu8MapNeedDeleteFlag2 = 0;
    uint8 Lu8MapNeedDeleteFlag3 = 0;
    float LstrP1_ToBumperDis = 65535;
    float LstrP2_ToBumperDis = 65535;
    ObjMAPAreaType LenuMapP1_DetailArea = MAP_AREA_NONE;
    ObjMAPAreaType LenuMapP2_DetailArea = MAP_AREA_NONE;
    float LfMapP1_X_ABS_Value,LfMapP1_Y_ABS_Value;
    float LfMapP2_X_ABS_Value,LfMapP2_Y_ABS_Value;
    uint8 Lu8P1_InSideAreaFlag = 0;
    uint8 Lu8P2_InSideAreaFlag = 0;

    LfMapP1_X_ABS_Value = ABS_VALUE(LpstrMapCoor->P1_CarCoor.fObjX);
    LfMapP1_Y_ABS_Value = ABS_VALUE(LpstrMapCoor->P1_CarCoor.fObjY);
    LfMapP2_X_ABS_Value = ABS_VALUE(LpstrMapCoor->P2_CarCoor.fObjX);
    LfMapP2_Y_ABS_Value = ABS_VALUE(LpstrMapCoor->P2_CarCoor.fObjY);
    
    if(LpstrMapCoor->eMapObjType == OBJ_TYPE_POINT)
    {
        LpstrMapCoor->fP1_P2_Dis = 0;
        LpstrMapCoor->fP1_P2ToCarX_Angle = 0;
        LenuMapP1_DetailArea = CalPointToBumperRelationAndDis(&LpstrMapCoor->P1_CarCoor,&LstrP1_ToBumperDis);
        LpstrMapCoor->fP1_ToBumper_Dis = LstrP1_ToBumperDis;
        LpstrMapCoor->eP1_DetailArea = LenuMapP1_DetailArea;
        LpstrMapCoor->fP2_ToBumper_Dis = LstrP1_ToBumperDis;
        LpstrMapCoor->eP2_DetailArea = LenuMapP1_DetailArea;
        if((LfMapP1_X_ABS_Value > MAP_TO_CAN_X_LIMIT_MAX)||(LfMapP1_Y_ABS_Value > MAP_TO_CAN_Y_LIMIT_MAX))
        {
            LpstrMapCoor->u8MapDisplayFlag = 0;
        }
        else
        {
            LpstrMapCoor->u8MapDisplayFlag = 1;
        }
        Lu8P1_InSideAreaFlag = JudgeMapDispalySideArea(LpstrMapCoor->P1_CarCoor.fObjX,LpstrMapCoor->P1_CarCoor.fObjY);
        LpstrMapCoor->u8MapInSideAreaDisplayFlag = Lu8P1_InSideAreaFlag;
    }
    else
    {
        LpstrMapCoor->fP1_P2_Dis = PubAI_CalTwoPointDis(LpstrMapCoor->P1_CarCoor.fObjX,LpstrMapCoor->P1_CarCoor.fObjY,\
            LpstrMapCoor->P2_CarCoor.fObjX,LpstrMapCoor->P2_CarCoor.fObjY);
        
        LpstrMapCoor->fP1_P2ToCarX_Angle = PubAI_CalP1_P2_LineToX_AxleAngle(LpstrMapCoor->P1_CarCoor.fObjX,LpstrMapCoor->P1_CarCoor.fObjY,\
            LpstrMapCoor->P2_CarCoor.fObjX,LpstrMapCoor->P2_CarCoor.fObjY);
        LenuMapP1_DetailArea = CalPointToBumperRelationAndDis(&LpstrMapCoor->P1_CarCoor,&LstrP1_ToBumperDis);
        LenuMapP2_DetailArea = CalPointToBumperRelationAndDis(&LpstrMapCoor->P2_CarCoor,&LstrP2_ToBumperDis);
        LpstrMapCoor->fP1_ToBumper_Dis = LstrP1_ToBumperDis;
        LpstrMapCoor->eP1_DetailArea = LenuMapP1_DetailArea;
        LpstrMapCoor->fP2_ToBumper_Dis = LstrP2_ToBumperDis;
        LpstrMapCoor->eP2_DetailArea = LenuMapP2_DetailArea;

        Lu8P1_InSideAreaFlag = JudgeMapDispalySideArea(LpstrMapCoor->P1_CarCoor.fObjX,LpstrMapCoor->P1_CarCoor.fObjY);
        Lu8P2_InSideAreaFlag = JudgeMapDispalySideArea(LpstrMapCoor->P2_CarCoor.fObjX,LpstrMapCoor->P2_CarCoor.fObjY);
        if((Lu8P1_InSideAreaFlag == 1)&&(Lu8P2_InSideAreaFlag == 1))
        {
            LpstrMapCoor->u8MapInSideAreaDisplayFlag = 1;
        }
        else if((Lu8P1_InSideAreaFlag == 2)&&(Lu8P2_InSideAreaFlag == 2))
        {
            LpstrMapCoor->u8MapInSideAreaDisplayFlag = 2;
        }
        else
        {
            LpstrMapCoor->u8MapInSideAreaDisplayFlag = 0;
        }

#if 1
    if((LfMapP1_Y_ABS_Value > MAP_TO_CAN_Y_LIMIT_MAX)||(LfMapP2_Y_ABS_Value > MAP_TO_CAN_Y_LIMIT_MAX)||\
        ((LfMapP1_X_ABS_Value > MAP_TO_CAN_X_LIMIT_MAX)&&(LfMapP2_X_ABS_Value > MAP_TO_CAN_X_LIMIT_MAX)))
#else
        if((LfMapP1_X_ABS_Value > MAP_TO_CAN_X_LIMIT_MAX)||(LfMapP1_Y_ABS_Value > MAP_TO_CAN_Y_LIMIT_MAX)\
            ||(LfMapP2_X_ABS_Value > MAP_TO_CAN_X_LIMIT_MAX)||(LfMapP2_Y_ABS_Value > MAP_TO_CAN_Y_LIMIT_MAX))
#endif
        {
            LpstrMapCoor->u8MapDisplayFlag = 0;
        }
        else
        {
            LpstrMapCoor->u8MapDisplayFlag = 1;
        }
        if((LpstrMapCoor->enuSideMapUpdateSts == SIDE_MAP_MEMORY_P1_UPDATE_P2)&&(LpstrMapCoor->enuSideMapSns != SIDE_SNS_NONE))
        {
            if(LpstrMapCoor->fP1_P2_Dis > 10000)
            {
                LpstrMapCoor->u8MapTooLongNeedEndFlag = 1;
            }
        }
    }
    /* 对于置信度小于33.3的先不显示 */
#if 1
    if(LpstrMapCoor->eObjExistProb < OBJ_PROB_LV2)
    {
        LpstrMapCoor->u8MapDisplayFlag = 0;
    }
#endif

    if(LstrP1_ToBumperDis < LstrP2_ToBumperDis)
    {
        LpstrMapCoor->fMapToBumper_Dis = LstrP1_ToBumperDis;
    }
    else
    {
        LpstrMapCoor->fMapToBumper_Dis = LstrP2_ToBumperDis;
    }
    /* 判断Map在保杠中的大致分区 */
    JudgeMapBumperArea(LpstrMapCoor);

    /*  判断Map和车辆的运动关系 */
    JudgeMapMoveSts(LpstrMapCoor);

    /* 判断Map的更新状态 */
    JudgeMapUpdateSts(LpstrMapCoor);

    /* Map高低区分及类型区分待添加 */
    if(LpstrMapCoor->enuSideMapSns != SIDE_SNS_NONE)
    {
        /* 侧边Map高低的处理 */
        if(LpstrMapCoor->u8ObjTypeUpdateFlag)
        {
            JudgeMapTypeByTypeCnt(LpstrMapCoor);
            LpstrMapCoor->u8ObjTypeUpdateFlag = 0;
            JudgeSideMapHeightInProcess(LpstrMapCoor);
        }
    }

#if 1
    if(LpstrMapCoor->eMapObjHeight == OBJ_LOW)
    {
        if(GstrParkingGuidenceData.fCarAngleSub > F_R_MAP_40_ANGLE)
        {
            if((LfMapP1_Y_ABS_Value > GstrRamSnsCoor[0][0].cRadarY)&&(LfMapP2_Y_ABS_Value > GstrRamSnsCoor[0][0].cRadarY))
            {
                LpstrMapCoor->eMapObjHeight = OBJ_HIGH;
            }
            else if((LfMapP1_Y_ABS_Value > (GstrRamSnsCoor[0][0].cRadarY+200))||(LfMapP2_Y_ABS_Value > (GstrRamSnsCoor[0][0].cRadarY+200)))
            {
                LpstrMapCoor->eMapObjHeight = OBJ_HIGH;
            }
            else
            {
                if(((LfMapP1_Y_ABS_Value > GstrRamSnsCoor[0][0].cRadarY)&&(LpstrMapCoor->P1_CarCoor.fObjX > 0)&&(LpstrMapCoor->P1_CarCoor.fObjX < GstrRamSnsCoor[0][0].cRadarX))\
                    ||((LfMapP2_Y_ABS_Value > GstrRamSnsCoor[0][0].cRadarY)&&(LpstrMapCoor->P2_CarCoor.fObjX > 0)&&(LpstrMapCoor->P2_CarCoor.fObjX < GstrRamSnsCoor[0][0].cRadarX)))
                {
                    LpstrMapCoor->eMapObjHeight = OBJ_HIGH;
                }
            }
        }
#if 0
        if(LpstrMapCoor->eMapObjId == 0x01)
        {
            VS_PRINT("Low Map,Time:%.3f,Id:%d,P1_Y_ABS:%.3f,P2_Y_ABS:%.3f,AngleSub:%.3f\r\n",GfMessageTime,LpstrMapCoor->eMapObjId,\
                LfMapP1_Y_ABS_Value,LfMapP2_Y_ABS_Value,GstrParkingGuidenceData.fCarAngleSub*57.3);
        }
#endif
    }

#endif


    
    LpstrMapCoor->u16NoUpdateHoldTime = GdSystemMsTimer - LpstrMapCoor->u32UpdateSysTime;
    
    Lu8MapNeedDeleteFlag1 = JudgeSideSnsExistMapAndRelation(LpstrMapCoor);
    Lu8MapNeedDeleteFlag3 = SideMap_DeleHandle(LpstrMapCoor);

#if 0
    
    if((LpstrMapCoor->eMapObjId == 0x01)&&(GfMessageTime > 48))
    {
        VS_PRINT("Map Inf Update,Time:%.3f,Id:%d,P1_P2_Dis:%.3f,ExistProb:%d,Flag1:%d,Flag2:%d,Flag3:%d,DispalyFlag:%d,MapNum:%d\r\n",GfMessageTime,\
            LpstrMapCoor->eMapObjId,LpstrMapCoor->fP1_P2_Dis,LpstrMapCoor->eObjExistProb,\
            Lu8MapNeedDeleteFlag1,Lu8MapNeedDeleteFlag2,Lu8MapNeedDeleteFlag3,\
            LpstrMapCoor->u8MapDisplayFlag,GstrObjMap.u8MapNum);
    }
#endif
    if((Lu8MapNeedDeleteFlag1)||(Lu8MapNeedDeleteFlag2)||(Lu8MapNeedDeleteFlag3))
    {
#if 0
        if((GfMessageTime > 40)&&(GfMessageTime < 45))
        {
            VS_PRINT("Dele Map,Time:%.3f,Id:%d,P1_P2_Dis:%.3f,ExistProb:%d,Flag1:%d,Flag2:%d,Flag3:%d\r\n",GfMessageTime,\
                LpstrMapCoor->eMapObjId,LpstrMapCoor->fP1_P2_Dis,LpstrMapCoor->eObjExistProb,\
                Lu8MapNeedDeleteFlag1,Lu8MapNeedDeleteFlag2,Lu8MapNeedDeleteFlag3);
        }
#endif
        return 1;
    }
    else
    {
        return 0;
    }
}

/******************************************************************************
 * 函数名称: JudgeSideMapToPointNear
 * 
 * 功能描述: 判断侧边Map与区域Map点的临近状态
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-29 16:56   V0.1      AntonyFang   初次发布
 ******************************************************************************/
uint8 JudgeSideMapToPointNear(ObjCoorType *LpstrObjPoint, ObjCoorType *LpstrMapPoint,eSideMapUpdateStsType LeSideMapUpdateSts,uint16 Lu16SideMasterDis)
{
    uint8 Lu8TwoPointNearFlag = 0;
    float LfXSub,LfYSub;
    float LfX_SubLimit;

    if(LeSideMapUpdateSts == SIDE_MAP_UPDATE_P1_P2)
    {
        if(Gu16CarSpdForSnsUse < 100)
        {
            LfX_SubLimit = 200;
        }
        else if(Gu16CarSpdForSnsUse < 200)
        {
            LfX_SubLimit = 300;
        }
        else if(Gu16CarSpdForSnsUse < 300)
        {
            LfX_SubLimit = 400;
        }
        else if(Gu16CarSpdForSnsUse < 500)
        {
            LfX_SubLimit = 500;
        }
        else if(Gu16CarSpdForSnsUse < 700)
        {
            LfX_SubLimit = 650;
        }
        else if(Gu16CarSpdForSnsUse < 900)
        {
            LfX_SubLimit = 800;
        }
        else
        {
            LfX_SubLimit = 1000;
        }
    }
    else
    {
        /* 需要根据车速配置不同的值 */
        if(Gu16CarSpdForSnsUse < 100)
        {
            LfX_SubLimit = 250;
        }
        else if(Gu16CarSpdForSnsUse < 200)
        {
            LfX_SubLimit = 350;
        }
        else if(Gu16CarSpdForSnsUse < 300)
        {
            LfX_SubLimit = 450;
        }
        else if(Gu16CarSpdForSnsUse < 500)
        {
            LfX_SubLimit = 550;
        }
        else if(Gu16CarSpdForSnsUse < 700)
        {
            LfX_SubLimit = 700;
        }
        else if(Gu16CarSpdForSnsUse < 900)
        {
            LfX_SubLimit = 900;
        }
        else
        {
            LfX_SubLimit = 1200;
        }
    }
    /* 低速时，定定扫扫策略下，200cm外无定频数据，需要适当延长匹配的距离 */
    if((Lu16SideMasterDis > 2000)&&(Gu16CarSpdForSnsUse < 350))
    {
        LfX_SubLimit = LfX_SubLimit*2;
    }

    LfXSub = ABS(LpstrObjPoint->fObjX,LpstrMapPoint->fObjX);
    LfYSub = ABS(LpstrObjPoint->fObjY,LpstrMapPoint->fObjY);


    if(LfYSub < SIDE_MAP_OBJ_Y_SUB)
    {
        if(LfXSub < LfX_SubLimit)
        {
            Lu8TwoPointNearFlag = 1;
        }
        else
        {
            Lu8TwoPointNearFlag = 0;
        }
    }
    else
    {
        Lu8TwoPointNearFlag = 0;
    }

    return Lu8TwoPointNearFlag;
}



/******************************************************************************
 * 函数名称: SideMapModifyStartPointHandle
 * 
 * 功能描述: 矫正起始点的坐标
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-04-24 15:01   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SideMapModifyStartPointHandle(SideAreaPointBufType  *LpstrSideAreaPointBuf,ObjMapCoorType *LpstrMapCoor)
#if 1
{
    uint8 i;
    uint8 Lu8Id;
    uint8 Lu8HighIndex;
    const SnsMapCalibHeightType *LptrThreTable;
    uint8 Lu8IdBuf[6];
    uint8 Lu8IdBufCnt = 0;
    uint8 Lu8AfterStartInxCnt = 0;
    uint16 Lu16MasterDis,Lu16MasterHeight;
    uint8 Lu8NewPointHeightThanFrontCnt = 0;
    uint8 Lu8FirstThanCurbInx = 255;
    float LfMap_P1_Y_MasterDisSub = 65535;
    ObjCoorType LstrModifyObjCarCoor;

    GetSideAreaBufId(LpstrSideAreaPointBuf);

    if((LpstrSideAreaPointBuf->u8ValidBufCnt <= 7)&&(LpstrMapCoor->u16SideMapUpdateMasterDis < 400))
    {
        return;
    }

#if 0
    if((LpstrMapCoor->eMapObjId == 0x04)&&(GfMessageTime > 18)&&(GfMessageTime < 19))
    {
        VS_PRINT("Time:%.3f,MapId:%d,ValidBufCnt:%d,SideMapUpdateMasterDis:%d\r\n",GfMessageTime,LpstrMapCoor->eMapObjId,\
            LpstrSideAreaPointBuf->u8ValidBufCnt,LpstrMapCoor->u16SideMapUpdateMasterDis);
        for(i = 0; i < LpstrSideAreaPointBuf->u8ValidBufCnt; i++)
        {
            Lu8Id = LpstrSideAreaPointBuf->u8IdBuf[i];
            Lu16MasterDis = LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterDis;
            Lu16MasterHeight = LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterHeight;


            LptrThreTable = &GStrMapObjJudgeChirpThresTableForSideMapInRAM[0];

            if(Lu16MasterDis < MAP_TABLE_MAX_DIS)
            {
                Lu8HighIndex = Lu16MasterDis/MAP_HIGH_TABLE_STEP;
            }
            else
            {
                Lu8HighIndex = SNS_MAP_DIS_HIGH_400cm;
            }

            
            VS_PRINT("i:%d,MasterDis:%d,MasterHeight:%d,CurbHeight:%d\r\n",i,Lu16MasterDis,Lu16MasterHeight,\
                LptrThreTable[Lu8HighIndex].u16CurbHeight);
        }
        VS_PRINT("Time:%.3f,MapId:%d,ValidBufCnt:%d,StartModifyFlag:%d\r\n\r\n",GfMessageTime,LpstrMapCoor->eMapObjId,\
            LpstrSideAreaPointBuf->u8ValidBufCnt,LpstrSideAreaPointBuf->u8StartModifyFlag);
    }
#endif

    if(!LpstrSideAreaPointBuf->u8StartModifyFlag)
    {
        for(i = 0; i < LpstrSideAreaPointBuf->u8ValidBufCnt; i++)
        {
            Lu8Id = LpstrSideAreaPointBuf->u8IdBuf[i];
            Lu16MasterDis = LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterDis;
            Lu16MasterHeight = LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterHeight;
            LptrThreTable = &GStrMapObjJudgeChirpThresTableForSideMapInRAM[0];

            if(Lu16MasterDis < MAP_TABLE_MAX_DIS)
            {
                Lu8HighIndex = Lu16MasterDis/MAP_HIGH_TABLE_STEP;
            }
            else
            {
                Lu8HighIndex = SNS_MAP_DIS_HIGH_400cm;
            }
            if(Lu16MasterDis > 400)
            {
                if(Lu16MasterHeight > LptrThreTable[Lu8HighIndex].u16CurbHeight)
                {
                    Lu8FirstThanCurbInx = Lu8Id;
                }
                else
                {
                    break;
                }
            }

            if(Lu8AfterStartInxCnt > 0)
            {
                Lu8AfterStartInxCnt++;
            }
            if(Lu8AfterStartInxCnt == 4)
            {
                break;
            }
            if(Lu8Id == LpstrSideAreaPointBuf->u8MapStartInx)
            {
                Lu8AfterStartInxCnt++;
            }
        }
        if(Lu8FirstThanCurbInx != 255)
        {
            PubAI_TransObjOdoCoorToCarCoor(&LpstrSideAreaPointBuf->Buf[Lu8FirstThanCurbInx].PointOdoCoor.fObjX,&LpstrSideAreaPointBuf->Buf[Lu8FirstThanCurbInx].PointOdoCoor.fObjY,\
                &LstrModifyObjCarCoor.fObjX,&LstrModifyObjCarCoor.fObjY);
            if(LpstrMapCoor->P1_CarCoor.fObjX < LstrModifyObjCarCoor.fObjX)
            {
                LfMap_P1_Y_MasterDisSub = ABS(LstrModifyObjCarCoor.fObjY,LpstrMapCoor->P1_CarCoor.fObjY);
                
                if(LfMap_P1_Y_MasterDisSub < 300)
                {
                    LpstrMapCoor->P1_CarCoor.fObjX = LstrModifyObjCarCoor.fObjX;
                    PubAI_TransObjCarCoorToOdoCoor(&LpstrMapCoor->P1_CarCoor.fObjX,&LpstrMapCoor->P1_CarCoor.fObjY,\
                        &LpstrMapCoor->P1_OdoCoor.fObjX,&LpstrMapCoor->P1_OdoCoor.fObjY);

                    /* 高低判断放到缓存模块中进行处理 */
                    GstrSideAreaMap[LpstrMapCoor->enuSideMapSns].u8FarDisNoNeedMasterCoorFlag = 1;
                }
            }
            else
            {
                /** @brief: 对于不满足主发矫正的条件的，直接进行矫正 */
#if 0
                if((LpstrMapCoor->eMapObjId == 0x04)&&(GfMessageTime > 18)&&(GfMessageTime < 19))
                {
                    VS_PRINT("Time:%.3f,MapId:%d,FirstInx:%d,P1_CarX:%.3f,P2_CarX:%.3f,Modify_X:%.3f\r\n",GfMessageTime,LpstrMapCoor->eMapObjId,\
                        Lu8FirstThanCurbInx,LpstrMapCoor->P1_CarCoor.fObjX,LpstrMapCoor->P2_CarCoor.fObjX,LstrModifyObjCarCoor.fObjX);
                }
#endif
                if(Gu16CarSpdForSnsUse < 450)
                {
                    /** @brief: 直接对X进行减半处理 */
                    LpstrMapCoor->P1_CarCoor.fObjX = (LpstrMapCoor->P1_CarCoor.fObjX+LpstrMapCoor->P2_CarCoor.fObjX)/2;
                    
                    PubAI_TransObjCarCoorToOdoCoor(&LpstrMapCoor->P1_CarCoor.fObjX,&LpstrMapCoor->P1_CarCoor.fObjY,\
                        &LpstrMapCoor->P1_OdoCoor.fObjX,&LpstrMapCoor->P1_OdoCoor.fObjY);
                }
            }
            LpstrSideAreaPointBuf->u8StartModifyFlag = 1;
        

        }
        
#if 0
        if((Lu16MasterDis > 1000)&&(Lu16MasterHeight > LptrThreTable[Lu8HighIndex].u16CurbHeight))
        {

            
            for(i = 0; i < Lu8IdBufCnt; i++)
            {
                if(Lu16MasterHeight > (LpstrSideAreaPointBuf->Buf[Lu8IdBuf[i]].u16MasterHeight*2))
                {
                    Lu8NewPointHeightThanFrontCnt++;
                }
            }

#if 1
            if(Lu8NewPointHeightThanFrontCnt > 3)
            {
                if(LpstrMapCoor->P1_CarCoor.fObjX < (GstrRamSnsCoor[0][0].cRadarX+100))
                {
                    if(LpstrMapCoor->P1_CarCoor.fObjY < 0)
                    {
                        LfMap_P1_Y_MasterDisSub = -LpstrMapCoor->P1_CarCoor.fObjY;
                        LfMap_P1_Y_MasterDisSub = LfMap_P1_Y_MasterDisSub - GstrRamSnsCoor[0][0].cRadarY;
                    }
                    else
                    {
                        LfMap_P1_Y_MasterDisSub = LpstrMapCoor->P1_CarCoor.fObjY - GstrRamSnsCoor[0][0].cRadarY;
                    }
                    LfMap_P1_Y_MasterDisSub = ABS(LfMap_P1_Y_MasterDisSub,Lu16MasterDis);
                    
                    if(LfMap_P1_Y_MasterDisSub < 300)
                    {
#if 1
                        LpstrMapCoor->P1_CarCoor.fObjX = GstrRamSnsCoor[0][0].cRadarX;
                        PubAI_TransObjCarCoorToOdoCoor(&LpstrMapCoor->P1_CarCoor.fObjX,&LpstrMapCoor->P1_CarCoor.fObjY,\
                            &LpstrMapCoor->P1_OdoCoor.fObjX,&LpstrMapCoor->P1_OdoCoor.fObjY);
#else
                        if(LpstrSideAreaPointBuf->Buf[Lu8Id02].enuDetType != POINT_CLOUD_DET_NONE)
                        {
                            LpstrMapCoor->P1_OdoCoor = LpstrSideAreaPointBuf->Buf[Lu8Id02].PointOdoCoor;
                            PubAI_TransObjOdoCoorToCarCoor(&LpstrMapCoor->P1_OdoCoor.fObjX,&LpstrMapCoor->P1_OdoCoor.fObjY,\
                                &LpstrMapCoor->P1_CarCoor.fObjX,&LpstrMapCoor->P1_CarCoor.fObjY);
                        }
                        else
                        {
                            LpstrMapCoor->P1_CarCoor.fObjX = GstrRamSnsCoor[0][0].cRadarX;
                            PubAI_TransObjCarCoorToOdoCoor(&LpstrMapCoor->P1_CarCoor.fObjX,&LpstrMapCoor->P1_CarCoor.fObjY,\
                                &LpstrMapCoor->P1_OdoCoor.fObjX,&LpstrMapCoor->P1_OdoCoor.fObjY);
                        }
#endif
                        /* 高低判断放到缓存模块中进行处理 */
                        GstrSideAreaMap[LpstrMapCoor->enuSideMapSns].u8FarDisNoNeedMasterCoorFlag = 1;
                    }
                }
                LpstrSideAreaPointBuf->u8StartModifyFlag = 1;
            }
#endif
        }
        else
        {
            return;
        }
#endif
        
        if(LpstrSideAreaPointBuf->u8AfterStartCnt > 8)
        {
            LpstrSideAreaPointBuf->u8StartModifyFlag = 1;
        }
    }    
}

#else
{
    uint8 i;
    uint8 Lu8Id,Lu8Id02;
    uint8 Lu8HighIndex;
    const SnsMapCalibHeightType *LptrThreTable;
    uint8 Lu8IdBuf[6];
    uint8 Lu8IdBufCnt = 0;
    uint8 Lu8AfterStartInxCnt = 0;
    uint16 Lu16MasterDis,Lu16MasterHeight;
    uint8 Lu8NewPointHeightThanFrontCnt = 0;
    float LfMap_P1_Y_MasterDisSub = 65535;

    GetSideAreaBufId(LpstrSideAreaPointBuf);

    if(LpstrSideAreaPointBuf->u8ValidBufCnt <= 7)
    {
        return;
    }

    if(!LpstrSideAreaPointBuf->u8StartModifyFlag)
    {
        Lu8Id02 = LpstrSideAreaPointBuf->u8IdBuf[0];
        Lu16MasterDis = LpstrSideAreaPointBuf->Buf[Lu8Id02].u16MasterDis;
        Lu16MasterHeight = LpstrSideAreaPointBuf->Buf[Lu8Id02].u16MasterHeight;
        LptrThreTable = &GStrMapObjJudgeChirpThresTableForSideMapInRAM[0];

        if(LpstrMapCoor->eMapObjId == 0x03)
        {
            VS_PRINT("Time:%.3f,MapId:%d,MasterDis:%d,MasterHeight:%d,AfterStartCnt:%d\r\n",GfMessageTime,LpstrMapCoor->eMapObjId,\
                Lu16MasterDis,Lu16MasterHeight,LpstrSideAreaPointBuf->u8AfterStartCnt);
        }


        
        if(Lu16MasterDis < MAP_TABLE_MAX_DIS)
        {
            Lu8HighIndex = Lu16MasterDis/MAP_HIGH_TABLE_STEP;
        }
        else
        {
            Lu8HighIndex = SNS_MAP_DIS_HIGH_400cm;
        }
        if((Lu16MasterDis > 1000)&&(Lu16MasterHeight > LptrThreTable[Lu8HighIndex].u16CurbHeight))
        {
            for(i = 1; i < LpstrSideAreaPointBuf->u8ValidBufCnt; i++)
            {
                Lu8Id = LpstrSideAreaPointBuf->u8IdBuf[i];
                Lu8IdBuf[Lu8IdBufCnt] = Lu8Id;
                Lu8IdBufCnt++;
                if(Lu8IdBufCnt == 6)
                {
                    break;
                }
                if(Lu8AfterStartInxCnt > 0)
                {
                    Lu8AfterStartInxCnt++;
                }
                if(Lu8AfterStartInxCnt == 3)
                {
                    break;
                }
                if(Lu8Id == LpstrSideAreaPointBuf->u8MapStartInx)
                {
                    Lu8AfterStartInxCnt++;
                }
            }
            
            for(i = 0; i < Lu8IdBufCnt; i++)
            {
                if(Lu16MasterHeight > (LpstrSideAreaPointBuf->Buf[Lu8IdBuf[i]].u16MasterHeight*2))
                {
                    Lu8NewPointHeightThanFrontCnt++;
                }
            }

#if 1
            if(Lu8NewPointHeightThanFrontCnt > 3)
            {
                if(LpstrMapCoor->P1_CarCoor.fObjX < (GstrRamSnsCoor[0][0].cRadarX+100))
                {
                    if(LpstrMapCoor->P1_CarCoor.fObjY < 0)
                    {
                        LfMap_P1_Y_MasterDisSub = -LpstrMapCoor->P1_CarCoor.fObjY;
                        LfMap_P1_Y_MasterDisSub = LfMap_P1_Y_MasterDisSub - GstrRamSnsCoor[0][0].cRadarY;
                    }
                    else
                    {
                        LfMap_P1_Y_MasterDisSub = LpstrMapCoor->P1_CarCoor.fObjY - GstrRamSnsCoor[0][0].cRadarY;
                    }
                    LfMap_P1_Y_MasterDisSub = ABS(LfMap_P1_Y_MasterDisSub,Lu16MasterDis);
                    
                    if(LfMap_P1_Y_MasterDisSub < 300)
                    {
#if 1
                        LpstrMapCoor->P1_CarCoor.fObjX = GstrRamSnsCoor[0][0].cRadarX;
                        PubAI_TransObjCarCoorToOdoCoor(&LpstrMapCoor->P1_CarCoor.fObjX,&LpstrMapCoor->P1_CarCoor.fObjY,\
                            &LpstrMapCoor->P1_OdoCoor.fObjX,&LpstrMapCoor->P1_OdoCoor.fObjY);
#else
                        if(LpstrSideAreaPointBuf->Buf[Lu8Id02].enuDetType != POINT_CLOUD_DET_NONE)
                        {
                            LpstrMapCoor->P1_OdoCoor = LpstrSideAreaPointBuf->Buf[Lu8Id02].PointOdoCoor;
                            PubAI_TransObjOdoCoorToCarCoor(&LpstrMapCoor->P1_OdoCoor.fObjX,&LpstrMapCoor->P1_OdoCoor.fObjY,\
                                &LpstrMapCoor->P1_CarCoor.fObjX,&LpstrMapCoor->P1_CarCoor.fObjY);
                        }
                        else
                        {
                            LpstrMapCoor->P1_CarCoor.fObjX = GstrRamSnsCoor[0][0].cRadarX;
                            PubAI_TransObjCarCoorToOdoCoor(&LpstrMapCoor->P1_CarCoor.fObjX,&LpstrMapCoor->P1_CarCoor.fObjY,\
                                &LpstrMapCoor->P1_OdoCoor.fObjX,&LpstrMapCoor->P1_OdoCoor.fObjY);
                        }
#endif
                        /* 高低判断放到缓存模块中进行处理 */
#if 0
                        LpstrMapCoor->u8SideMapHeightLockFlag = 1;
                        LpstrMapCoor->eMapObjHeight = OBJ_HIGH;
#endif
                        GstrSideAreaMap[LpstrMapCoor->enuSideMapSns].u8FarDisNoNeedMasterCoorFlag = 1;
                    }
                }
                LpstrSideAreaPointBuf->u8StartModifyFlag = 1;
            }
#if 0
            if(LpstrMapCoor->eMapObjId == 0x02)
            {
                VS_PRINT("Time:%.3f,MapId:%d,NewPointHeightThanFrontCnt:%d,IdBufCnt:%d,MasterDis:%d,MasterHeight:%d,P1_X:%.3f,P1_Y:%.3f,Sub:%.2f\r\n",GfMessageTime,LpstrMapCoor->eMapObjId,\
                    Lu8NewPointHeightThanFrontCnt,Lu8IdBufCnt,Lu16MasterDis,Lu16MasterHeight,\
                    LpstrMapCoor->P1_CarCoor.fObjX,LpstrMapCoor->P1_CarCoor.fObjY,LfMap_P1_Y_MasterDisSub);
            }
#endif
#endif
        }
        else
        {
            return;
        }
        
        if(LpstrSideAreaPointBuf->u8AfterStartCnt > 8)
        {
            LpstrSideAreaPointBuf->u8StartModifyFlag = 1;
        }
    }    
}
#endif



/******************************************************************************
 * 函数名称: SideMapModifyEndPointHandle
 * 
 * 功能描述: 找车位阶段对结束点进行裁剪的处理
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-05-27 20:03   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SideMapModifyEndPointHandle(SideAreaPointBufType  *LpstrSideAreaPointBuf,ObjMapCoorType *LpstrMapCoor)
{
    sint8 i;
    uint8 Lu8Id,Lu8Id02;
    uint8 Lu8HighIndex;
    const SnsMapCalibHeightType *LptrThreTable;

    uint8 Lu8HeightLessThanFirstCnt = 0;
    uint8 Lu8FirstBigHeightId;
    uint8 Lu8NeedModifyFlag = 0;
    uint16 Lu16MasterDis,Lu16MasterHeight;
    uint16 Lu16MasterDisSub;
    ObjCoorType LstrModifyObjCarCoor;
    float LfMap_P1_Y_MasterDisSub = 65535;

    GetSideAreaBufId(LpstrSideAreaPointBuf);

#if 0
    if(LpstrMapCoor->eMapObjId == 0x01)
    {
        VS_PRINT("Map Id:%d,MapEndInxNoUpdateCnt:%d\r\n",LpstrMapCoor->eMapObjId,\
            LpstrSideAreaPointBuf->u8MapEndInxNoUpdateCnt);
    }
    for(i = LpstrSideAreaPointBuf->u8MapEndInxNoUpdateCnt; i >= 0; i--)
    {
        Lu8Id02 = LpstrSideAreaPointBuf->u8IdBuf[i];
        Lu16MasterDis = LpstrSideAreaPointBuf->Buf[Lu8Id02].u16MasterDis;
        Lu16MasterHeight = LpstrSideAreaPointBuf->Buf[Lu8Id02].u16MasterHeight;
        
        VS_PRINT("Inx:%d,MasterDis:%d,MasterHeight:%d\r\n",i,\
            Lu16MasterDis,Lu16MasterHeight);
    }
#endif

    
    if((LpstrSideAreaPointBuf->u8MapEndInxNoUpdateCnt < 2)||(LpstrSideAreaPointBuf->u8MapEndInxNoUpdateCnt > 13))
    {
        return;
    }

    Lu8Id02 = LpstrSideAreaPointBuf->u8IdBuf[LpstrSideAreaPointBuf->u8MapEndInxNoUpdateCnt];
    Lu16MasterDis = LpstrSideAreaPointBuf->Buf[Lu8Id02].u16MasterDis;
    Lu16MasterHeight = LpstrSideAreaPointBuf->Buf[Lu8Id02].u16MasterHeight;
    LptrThreTable = &GStrMapObjJudgeChirpThresTableForSideMapInRAM[0];
    if(Lu16MasterDis < MAP_TABLE_MAX_DIS)
    {
        Lu8HighIndex = Lu16MasterDis/MAP_HIGH_TABLE_STEP;
    }
    else
    {
        Lu8HighIndex = SNS_MAP_DIS_HIGH_400cm;
    }
    if((Lu16MasterDis > 2000)&&(Lu16MasterHeight > LptrThreTable[Lu8HighIndex].u16CurbHeight))
    //if((Lu16MasterDis > 700)&&(Lu16MasterHeight > LptrThreTable[Lu8HighIndex].u16CurbHeight))
    {
        Lu8FirstBigHeightId = Lu8Id02;
        Lu8HeightLessThanFirstCnt = 0;
        for(i = LpstrSideAreaPointBuf->u8MapEndInxNoUpdateCnt - 1; i >= 0; i--)
        {
            Lu8Id = LpstrSideAreaPointBuf->u8IdBuf[i];
            Lu16MasterDisSub = ABS(Lu16MasterDis,LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterDis);
            if((Lu16MasterDisSub < 300)&&(LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterHeight > LptrThreTable[Lu8HighIndex].u16CurbHeight))
            {
                Lu16MasterDis = LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterDis;
                Lu16MasterHeight = LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterHeight;
                Lu8HeightLessThanFirstCnt = 0;
                Lu8FirstBigHeightId = Lu8Id;
            }
            else
            {
                if(LpstrSideAreaPointBuf->Buf[Lu8Id].u16MasterHeight*2 < Lu16MasterHeight)
                {
                    Lu8HeightLessThanFirstCnt++;
                }
            }
            if(Lu8HeightLessThanFirstCnt > 3)
            {
                Lu8NeedModifyFlag = 1;
                break;
            }
        }
        if(Lu8NeedModifyFlag)
        {
            PubAI_TransObjOdoCoorToCarCoor(&LpstrSideAreaPointBuf->Buf[Lu8FirstBigHeightId].PointOdoCoor.fObjX,&LpstrSideAreaPointBuf->Buf[Lu8FirstBigHeightId].PointOdoCoor.fObjY,\
                &LstrModifyObjCarCoor.fObjX,&LstrModifyObjCarCoor.fObjY);
             
            if(LpstrMapCoor->P2_CarCoor.fObjX > (LstrModifyObjCarCoor.fObjX+50))
            {
                LfMap_P1_Y_MasterDisSub = ABS(LpstrMapCoor->P2_CarCoor.fObjY,LstrModifyObjCarCoor.fObjY);
                
                if(LfMap_P1_Y_MasterDisSub < 300)
                {
                    LpstrMapCoor->P2_CarCoor = LstrModifyObjCarCoor;
                    LpstrMapCoor->P2_OdoCoor = LpstrSideAreaPointBuf->Buf[Lu8FirstBigHeightId].PointOdoCoor;
                }
            }
        }
    }  
}


/******************************************************************************
 * 函数名称: SideMapEndHnadle
 * 
 * 功能描述: 侧边Map结束处理
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-05-14 15:13   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SideMapEndHnadle(ObjMapCoorType *LpstrMapCoor)
{
    LpstrMapCoor->enuSideMapUpdateSts = SIDE_MAP_MEMORY_P1_MEMORY_P2;
    LpstrMapCoor->eMapObjType = OBJ_TYPE_STRAIGHT2_CORNER;
    LpstrMapCoor->eMapCreatAdasSts = GstrParkingGuidenceData.eCurrentAdasSts;

    if(GstrParkingGuidenceData.eCurrentAdasSts == APA_GUIDANCE)
    {
        if(LpstrMapCoor->fP1_P2_Dis > 300)
        {
            LpstrMapCoor->eObjExistProb = OBJ_PROB_LV5;
        }
        if((LpstrMapCoor->eObjExistProb > OBJ_PROB_LV1)&&(LpstrMapCoor->fMapToBumper_Dis < 1300))
        {
            LpstrMapCoor->eObjExistProb = OBJ_PROB_LV5;
        }
    }
    if(LpstrMapCoor->eMapObjHeight == OBJ_LOW)
    {
        if(LpstrMapCoor->fMapToBumper_Dis < 2600)
        {
            if(LpstrMapCoor->fMapToBumper_Dis < 1000)
            {
                LpstrMapCoor->eMapObjHeight = OBJ_HIGH;
            }
            
            if(GstrParkingGuidenceData.fCarAngleSub < F_R_MAP_20_ANGLE)
            {
                LpstrMapCoor->u8SideMapLowTagFlag = 1;
            }
        }
        else
        {
            if(LpstrMapCoor->fP1_P2_Dis < 500)
            {
                LpstrMapCoor->u8SideMapLowTagFlag = 1;
            }
        }
    }
    if((GstrObjMap.eSideMapFusionId == MAP_NONE)&&(LpstrMapCoor->eObjExistProb > OBJ_PROB_LV3))
    {
        GstrObjMap.eSideMapFusionId = LpstrMapCoor->eMapObjId;
    }
}

/**
 * @FunName   SideMapCutPillarHandle
 * <AUTHOR>
 * @brief     侧边Map为柱子时的裁剪处理
 * @param[in] 
 * @param[out] 
 * @param[in|out] 
 * @return 
 * @note: 
 */
void SideMapCutPillarHandle(ObjMapCoorType *LpstrMapCoor)
{
#if 0
    VS_PRINT("Map Cut,Map Id:%d,Time:%.3f\r\n",LpstrMapCoor->eMapObjId,GfMessageTime);
#endif
    float LfP1_P2ToCarX_AngleAbs;
    LfP1_P2ToCarX_AngleAbs = ABS_VALUE(LpstrMapCoor->fP1_P2ToCarX_Angle);
    
    if((GstrParkingGuidenceData.fCarAngleSub < F_R_MAP_25_ANGLE))
    {
        if(Gu16CarSpdForSnsUse < 300)
        {
#if 1
            if((LpstrMapCoor->fP1_P2_Dis > 500)&&(LfP1_P2ToCarX_AngleAbs < F_R_MAP_10_ANGLE))
            {
                if((LpstrMapCoor->fMapToBumper_Dis < 3500)&&(LpstrMapCoor->fMapToBumper_Dis > 500))
                {
                    LpstrMapCoor->P1_CarCoor.fObjX = LpstrMapCoor->P1_CarCoor.fObjX + 250;
                    PubAI_TransObjCarCoorToOdoCoor(&LpstrMapCoor->P1_CarCoor.fObjX,&LpstrMapCoor->P1_CarCoor.fObjY,\
                        &LpstrMapCoor->P1_OdoCoor.fObjX,&LpstrMapCoor->P1_OdoCoor.fObjY);
                    LpstrMapCoor->fP1_P2_Dis = LpstrMapCoor->fP1_P2_Dis - 250;
                }
            }
#endif

#if 1
            if((LpstrMapCoor->fP1_P2_Dis > 400)&&(LpstrMapCoor->eMapObjHeight == OBJ_HIGH)&&(LfP1_P2ToCarX_AngleAbs < F_R_MAP_10_ANGLE))
            {
                if((LpstrMapCoor->fMapToBumper_Dis < 2500)&&(LpstrMapCoor->fMapToBumper_Dis > 500))
                {
                    LpstrMapCoor->P2_CarCoor.fObjX = LpstrMapCoor->P2_CarCoor.fObjX - 150;
                    PubAI_TransObjCarCoorToOdoCoor(&LpstrMapCoor->P2_CarCoor.fObjX,&LpstrMapCoor->P2_CarCoor.fObjY,\
                        &LpstrMapCoor->P2_OdoCoor.fObjX,&LpstrMapCoor->P2_OdoCoor.fObjY);
                }
            }
#endif
        }
        else if(Gu16CarSpdForSnsUse < 800)
        {
            if((LpstrMapCoor->fP1_P2_Dis > 600)&&(LpstrMapCoor->eMapObjHeight == OBJ_HIGH)&&(LfP1_P2ToCarX_AngleAbs < F_R_MAP_10_ANGLE))
            {
                if((LpstrMapCoor->fMapToBumper_Dis < 2500)&&(LpstrMapCoor->fMapToBumper_Dis > 500))
                {
                    LpstrMapCoor->P2_CarCoor.fObjX = LpstrMapCoor->P2_CarCoor.fObjX - 220;
                    PubAI_TransObjCarCoorToOdoCoor(&LpstrMapCoor->P2_CarCoor.fObjX,&LpstrMapCoor->P2_CarCoor.fObjY,\
                        &LpstrMapCoor->P2_OdoCoor.fObjX,&LpstrMapCoor->P2_OdoCoor.fObjY);
                }
            }
        }
    }                
}



/******************************************************************************
 * 函数名称: ObjSideMapBuildAndUpdate
 * 
 * 功能描述: 侧边Map的构建与更新
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-11-29 14:00   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void ObjSideMapBuildAndUpdate(ObjMapCoorType *LpstrMapCoor,SideAreaMapType *LpstrSideAreaMap)
{
    uint8 j;
    uint8 Lu8TwoPointNearFlag = 0;
    uint16 Lu16NewPointAndOldMapDisSub = 0;
    uint16 Lu16NewPointAndOldMapDisSubLimit;
    float LfNewMapToCarAngle;
    float LfAngleSub = 0;
    ObjCoorType LstrNewMapP2_Coor;
    uint8 Lu8MapAngleNoMatchFlag = 0;
    uint8 Lu8CreatMapLockHighFlag = 0;
    uint8 Lu8EndMapPrintfId = 0x1;

    if((LpstrMapCoor->enuSideMapUpdateSts == SIDE_MAP_MEMORY_P1_UPDATE_P2)||(LpstrMapCoor->enuSideMapUpdateSts == SIDE_MAP_UPDATE_P1_P2))
    {
        if(LpstrMapCoor->u8MapTooLongNeedEndFlag)
        {
            LpstrMapCoor->eObjExistProb = OBJ_PROB_LV5;
            JudgeSideMapHeightInEnd(LpstrMapCoor);
            LpstrMapCoor->u8SideMapHeightLockFlag = 1;
            SideMapEndHnadle(LpstrMapCoor);
            LpstrSideAreaMap->u8CurbNeedMasterCoorFlag = 0;
            LpstrSideAreaMap->u8FarDisNoNeedMasterCoorFlag = 0;
            
            GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns].u8MapStartInx = 255;
            GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns].u8MapEndInx = 255;
#if 0
            if((LpstrMapCoor->eMapObjId == Lu8EndMapPrintfId))//&&(GfMessageTime > 4.5)&&(GfMessageTime < 6.5))
            {
                VS_PRINT("End Map 01,Map too long,Map Id:%d,Time:%.3f,SideMapToSelfSnsDis:%.3f,ExistProb:%d,Height:%d\r\n",LpstrMapCoor->eMapObjId,GfMessageTime,\
                    LpstrMapCoor->fSideMapToSelfSnsDis,LpstrMapCoor->eObjExistProb,LpstrMapCoor->eMapObjHeight);
            }
#endif
            return;
        }

        if(((LpstrMapCoor->SideMapCarMovDir == SNS_CAR_FORWARD)&&(LpstrSideAreaMap->eSnsMovDir == SNS_CAR_BACKWARD))||\
            ((LpstrMapCoor->SideMapCarMovDir == SNS_CAR_BACKWARD)&&(LpstrSideAreaMap->eSnsMovDir == SNS_CAR_FORWARD)))
        {
            //LpstrMapCoor->eObjExistProb = OBJ_PROB_LV5;
            JudgeSideMapHeightInEnd(LpstrMapCoor);
            LpstrMapCoor->u8SideMapHeightLockFlag = 1;
            SideMapEndHnadle(LpstrMapCoor);
            if(LpstrMapCoor->enuSideMapSns < SIDE_SNS_RLS)
            {
                SideMapCutPillarHandle(LpstrMapCoor);
            }
            LpstrSideAreaMap->u8CurbNeedMasterCoorFlag = 0;
            LpstrSideAreaMap->u8FarDisNoNeedMasterCoorFlag = 0;
            
            GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns].u8MapStartInx = 255;
            GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns].u8MapEndInx = 255;
#if 0
            if((LpstrMapCoor->eMapObjId == Lu8EndMapPrintfId))//&&(GfMessageTime > 4.5)&&(GfMessageTime < 6.5))
            {
                VS_PRINT("End Map 01,Map too long 02,Map Id:%d,Time:%.3f,SideMapToSelfSnsDis:%.3f,ExistProb:%d,Height:%d\r\n",LpstrMapCoor->eMapObjId,GfMessageTime,\
                    LpstrMapCoor->fSideMapToSelfSnsDis,LpstrMapCoor->eObjExistProb,LpstrMapCoor->eMapObjHeight);
            }
#endif
            return;
        }
        
        if(LpstrSideAreaMap->u8ValidObjCoorFlag)
        {
            /* 先判断点云Map是否和已经跟踪的Map匹配，匹配则更新，不匹配则计算持续时间，判断是否需要删除及进入侧保记忆 */
            if(LpstrMapCoor->enuSideMapUpdateSts == SIDE_MAP_UPDATE_P1_P2)
            {
                if(LpstrMapCoor->enuSideMapSns < SIDE_SNS_RLS)
                {
                    /* 前侧雷达 */
                    Lu8TwoPointNearFlag = JudgeSideMapToPointNear(&LpstrMapCoor->P2_CarCoor,&LpstrSideAreaMap->P2_CarCoor,LpstrMapCoor->enuSideMapUpdateSts,LpstrSideAreaMap->u16ValidMasterDis);
                }
                else
                {
                    /* 后侧雷达 */
                    if(LpstrMapCoor->SideMapCarMovDir == SNS_CAR_BACKWARD)
                    {
                        Lu8TwoPointNearFlag = JudgeSideMapToPointNear(&LpstrMapCoor->P1_CarCoor,&LpstrSideAreaMap->P1_CarCoor,LpstrMapCoor->enuSideMapUpdateSts,LpstrSideAreaMap->u16ValidMasterDis);
                    }
                    else
                    {
                        Lu8TwoPointNearFlag = JudgeSideMapToPointNear(&LpstrMapCoor->P2_CarCoor,&LpstrSideAreaMap->P2_CarCoor,LpstrMapCoor->enuSideMapUpdateSts,LpstrSideAreaMap->u16ValidMasterDis);
                    }
                }
                if(Lu8TwoPointNearFlag == 1)
                {            
                    if(LpstrMapCoor->enuSideMapSns < SIDE_SNS_RLS)
                    {
                        /* 前侧雷达MapP1 X更小;区域线状Map中，P1 X更大 */
                        LpstrMapCoor->P1_CarCoor = LpstrSideAreaMap->P2_CarCoor;
                        LpstrMapCoor->P2_CarCoor = LpstrSideAreaMap->P1_CarCoor;
                        LpstrMapCoor->P1_OdoCoor = LpstrSideAreaMap->P2_OdoCoor;
                        LpstrMapCoor->P2_OdoCoor = LpstrSideAreaMap->P1_OdoCoor;
                    }
                    else
                    {
                        /* 后侧雷达，车辆倒退，P1固定，P2移动；区域Map P1_X更大*/
                        if(LpstrMapCoor->SideMapCarMovDir == SNS_CAR_BACKWARD)
                        {
                            LpstrMapCoor->P1_CarCoor = LpstrSideAreaMap->P1_CarCoor;
                            LpstrMapCoor->P2_CarCoor = LpstrSideAreaMap->P2_CarCoor;
                            LpstrMapCoor->P1_OdoCoor = LpstrSideAreaMap->P1_OdoCoor;
                            LpstrMapCoor->P2_OdoCoor = LpstrSideAreaMap->P2_OdoCoor;
                        }
                        else 
                        {
                            LpstrMapCoor->P1_CarCoor = LpstrSideAreaMap->P2_CarCoor;
                            LpstrMapCoor->P2_CarCoor = LpstrSideAreaMap->P1_CarCoor;
                            LpstrMapCoor->P1_OdoCoor = LpstrSideAreaMap->P2_OdoCoor;
                            LpstrMapCoor->P2_OdoCoor = LpstrSideAreaMap->P1_OdoCoor;
                        }
                    }
                    if(LpstrSideAreaMap->MapSharp == AREA_MAP_SHAPE_POINT)
                    {
                        LpstrMapCoor->enuSideMapUpdateSts = SIDE_MAP_UPDATE_P1_P2;
                        LpstrMapCoor->eMapObjType = OBJ_TYPE_POINT;
                        /* 针对后侧雷达在Guidance下的连线策略 */
#if 0
                        if((GstrParkingGuidenceData.eCurrentAdasSts == APA_GUIDANCE)&&(LpstrSideAreaMap->eSnsMovDir == SNS_CAR_BACKWARD))
                        {
                            if((LpstrMapCoor->enuSideMapSns > SIDE_SNS_FRS)&&(LpstrMapCoor->fMapToBumper_Dis < 1000))
                            {
                                LpstrMapCoor->enuSideMapUpdateSts = SIDE_MAP_MEMORY_P1_UPDATE_P2;
                                LpstrMapCoor->eMapObjType = OBJ_TYPE_STRAIGHT1_CORNER;
                            }
                        }
#endif
                    }
                    else
                    {
                        LpstrMapCoor->enuSideMapUpdateSts = SIDE_MAP_MEMORY_P1_UPDATE_P2;
                        LpstrMapCoor->eMapObjType = OBJ_TYPE_STRAIGHT1_CORNER;
                    }
                    LpstrMapCoor->u32UpdateSysTime = GdSystemMsTimer;
                    UpdateMapTypeCntToBuf(&LpstrMapCoor->ObjTypeCntBuf[0],LpstrSideAreaMap->ObjTypeCnt);
                    MapExistProbAdd(LpstrMapCoor->eMapObjId);
                    LpstrMapCoor->u8ObjTypeUpdateFlag = 1;
                    LpstrSideAreaMap->u8ValidObjCoorFlag = 0;
                    /* 点状Map，连续3帧有效，立即置高--由于调整了初始的置信度为 OBJ_PROB_LV3，因此此处需要同步提高*/
#if 0
                    if(GstrParkingGuidenceData.eCurrentAdasSts != APA_GUIDANCE)
                    {
                        if(LpstrMapCoor->eObjExistProb >= OBJ_PROB_LV5)
                        {
                            LpstrMapCoor->eMapObjHeight = OBJ_HIGH;
                            LpstrMapCoor->u8SideMapHeightLockFlag = 1;
                        }
                    }
                    else
                    {
                        if(LpstrMapCoor->eObjExistProb >= OBJ_PROB_LV3)
                        {
                            LpstrMapCoor->eMapObjHeight = OBJ_HIGH;
                            LpstrMapCoor->u8SideMapHeightLockFlag = 1;
                        }
                    }
#endif
                    LpstrMapCoor->u16SideMapUpdateMasterDis = LpstrSideAreaMap->u16ValidMasterDis;
                    UpdateSideAreaPointMapEndInx(&GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns]);
                    //if((!LpstrMapCoor->u8SideMapHeightLockFlag)&&(GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns].u8AfterStartCnt < 3))
                    if(!LpstrMapCoor->u8SideMapHeightLockFlag)
                    {
                        //AnalysisSideAreaBufDataAndPrintf(&GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns],LpstrMapCoor->eMapObjId);
                        Lu8CreatMapLockHighFlag = AnalysisSideAreaBufDataWhenMapUpdate(&GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns],LpstrMapCoor->eMapObjId,LpstrSideAreaMap->u16ValidMasterDis);
                        if(Lu8CreatMapLockHighFlag)
                        {
                            LpstrMapCoor->u8SideMapHeightLockFlag = 1;
                            LpstrMapCoor->eMapObjHeight = OBJ_HIGH;
                        }
                    }
                    AnalysisSideAreaBufDataAndPrintf(&GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns],LpstrMapCoor->eMapObjId);                    
#if 0
                    if((LpstrMapCoor->eMapObjId == Lu8EndMapPrintfId))//&&(GfMessageTime > 4.5)&&(GfMessageTime < 6.5))
                    {
                        VS_PRINT("Update SideMapUpdateMasterDis 01,Map Id:%d,Time:%.3f,SideMapUpdateMasterDis:%d,ValidMasterDis:%d\r\n",LpstrMapCoor->eMapObjId,GfMessageTime,\
                            LpstrMapCoor->u16SideMapUpdateMasterDis,LpstrSideAreaMap->u16ValidMasterDis);
                    }
#endif
                }
            }
            else if(LpstrMapCoor->enuSideMapUpdateSts == SIDE_MAP_MEMORY_P1_UPDATE_P2)
            {
                if(LpstrMapCoor->enuSideMapSns < SIDE_SNS_RLS)
                {
                    /* 前侧雷达 */
                    Lu8TwoPointNearFlag = JudgeSideMapToPointNear(&LpstrMapCoor->P2_CarCoor,&LpstrSideAreaMap->P1_CarCoor,LpstrMapCoor->enuSideMapUpdateSts,LpstrSideAreaMap->u16ValidMasterDis);
                }
                else
                {
                    /* 后侧雷达 */
                    if(LpstrMapCoor->SideMapCarMovDir == SNS_CAR_BACKWARD)
                    {
                        Lu8TwoPointNearFlag = JudgeSideMapToPointNear(&LpstrMapCoor->P2_CarCoor,&LpstrSideAreaMap->P2_CarCoor,LpstrMapCoor->enuSideMapUpdateSts,LpstrSideAreaMap->u16ValidMasterDis);
                    }
                    else
                    {
                        Lu8TwoPointNearFlag = JudgeSideMapToPointNear(&LpstrMapCoor->P2_CarCoor,&LpstrSideAreaMap->P1_CarCoor,LpstrMapCoor->enuSideMapUpdateSts,LpstrSideAreaMap->u16ValidMasterDis);
                    }
#if 0
                    if((LpstrMapCoor->enuSideMapSns == SIDE_SNS_RRS)&&(GfMessageTime > 25.2)&&(GfMessageTime < 28))
                    {
                        VS_PRINT("Map Id:%d,Time:%.3f,X_Sub:%.3f,Y_Sub:%.3f\r\n",LpstrMapCoor->eMapObjId,GfMessageTime,\
                            ABS(LpstrMapCoor->P2_CarCoor.fObjX,LpstrSideAreaMap->P2_CarCoor.fObjX),\
                            ABS(LpstrMapCoor->P2_CarCoor.fObjY,LpstrSideAreaMap->P2_CarCoor.fObjY));
                    }
#endif
                }
                if(Lu8TwoPointNearFlag == 1)
                {
                    /* 计算即将生成Map的斜度 */
                    if(LpstrMapCoor->enuSideMapSns < SIDE_SNS_RLS)
                    {
                        /* 前侧雷达 */
                        if(LpstrSideAreaMap->P1_CarCoor.fObjX > LpstrMapCoor->P2_CarCoor.fObjX)
                        {
                            LstrNewMapP2_Coor = LpstrSideAreaMap->P1_CarCoor;
                            Lu8MapAngleNoMatchFlag = 2;
                        }
                    }
                    else
                    {
                        /* 后侧雷达 */
                        if(LpstrMapCoor->SideMapCarMovDir == SNS_CAR_BACKWARD)
                        {
                            if(LpstrSideAreaMap->P2_CarCoor.fObjX < LpstrMapCoor->P2_CarCoor.fObjX)
                            {
                                LstrNewMapP2_Coor = LpstrSideAreaMap->P2_CarCoor;
                                Lu8MapAngleNoMatchFlag = 2;
                            }
                        }
                        else
                        {
                            if(LpstrSideAreaMap->P1_CarCoor.fObjX > LpstrMapCoor->P2_CarCoor.fObjX)
                            {
                                LstrNewMapP2_Coor = LpstrSideAreaMap->P1_CarCoor;
                                Lu8MapAngleNoMatchFlag = 2;
                            }
                        }

                    }
                    if(Lu8MapAngleNoMatchFlag == 2)
                    {
                        LfNewMapToCarAngle = PubAI_CalP1_P2_LineToX_AxleAngle(LpstrMapCoor->P1_CarCoor.fObjX,LpstrMapCoor->P1_CarCoor.fObjY,\
                                    LstrNewMapP2_Coor.fObjX,LstrNewMapP2_Coor.fObjY);
                                    
                        LfAngleSub = ABS(LfNewMapToCarAngle,LpstrMapCoor->fP1_P2ToCarX_Angle);
#if 0
                        if((LpstrMapCoor->enuSideMapSns == SIDE_SNS_RRS)&&(LpstrMapCoor->eMapObjId == 0x02))
                        {
                            VS_PRINT("P1 Memory,Time:%.3f,MapId:%d,MapToCarAngle:%.2f,NewMapToCarAngle:%.2f,AngleSub:%.2f,P1_P2_Dis:%.3f,MapObjType:%d\r\n",GfMessageTime,LpstrMapCoor->eMapObjId,\
                                LpstrMapCoor->fP1_P2ToCarX_Angle*57.3,LfNewMapToCarAngle*57.3,LfAngleSub*57.3,\
                                LpstrMapCoor->fP1_P2_Dis,LpstrMapCoor->eMapObjType); 
                        }
#endif
                    }
                    else
                    {
                        LfAngleSub = 0;
                    }
                    Lu8MapAngleNoMatchFlag = 0;
                    if(LpstrMapCoor->fP1_P2_Dis > 4000)
                    {
                        if(LfAngleSub > F_R_MAP_0_25_ANGLE)
                        {
                            Lu8MapAngleNoMatchFlag = 1;
                        }
                    }
                    if(LpstrMapCoor->fP1_P2_Dis > 3000)
                    {
                        if(LfAngleSub > F_R_MAP_1_ANGLE)
                        {
                            Lu8MapAngleNoMatchFlag = 1;
                        }
                    }
                    else if(LpstrMapCoor->fP1_P2_Dis > 2000)
                    {
                        if(LfAngleSub > F_R_MAP_2_ANGLE)
                        {
                            Lu8MapAngleNoMatchFlag = 1;
                        }
                    }
                    else if(LpstrMapCoor->fP1_P2_Dis > 1000)
                    {
                        if(LfAngleSub > F_R_MAP_5_ANGLE)
                        {
                            Lu8MapAngleNoMatchFlag = 1;
                        }
                    }
                    else if(LpstrMapCoor->fP1_P2_Dis > 500)
                    {
                        if(LpstrMapCoor->enuSideMapSns < SIDE_SNS_RLS)
                        {
                            if(LfAngleSub > F_R_MAP_6_ANGLE)
                            {
                                Lu8MapAngleNoMatchFlag = 1;
                            }
                        }
                        else
                        {
                            if(LfAngleSub > F_R_MAP_4_ANGLE)
                            {
                                Lu8MapAngleNoMatchFlag = 1;
                            }
                        }
                    }
                    else if(LpstrMapCoor->fP1_P2_Dis > 300)
                    {
                        if(LpstrMapCoor->enuSideMapSns < SIDE_SNS_RLS)
                        {
                            if(LfAngleSub > F_R_MAP_7_ANGLE)
                            {
                                Lu8MapAngleNoMatchFlag = 1;
                            }
                        }
                        else
                        {
                            if(LfAngleSub > F_R_MAP_5_ANGLE)
                            {
                                Lu8MapAngleNoMatchFlag = 1;
                            }
                        }
                    }
                    else if(LpstrMapCoor->fP1_P2_Dis > 150)
                    {
                        if(LpstrMapCoor->enuSideMapSns < SIDE_SNS_RLS)
                        {
                            if(LfAngleSub > F_R_MAP_8_ANGLE)
                            {
                                Lu8MapAngleNoMatchFlag = 1;
                            }
                        }
                        else
                        {
                            if(LfAngleSub > F_R_MAP_7_ANGLE)
                            {
                                Lu8MapAngleNoMatchFlag = 1;
                            }
                        }
                    }
                    else if(LpstrMapCoor->fP1_P2_Dis > 100)
                    {
                        if(LfAngleSub > F_R_MAP_15_ANGLE)
                        {
                            Lu8MapAngleNoMatchFlag = 1;
                        }
                    }

                    /* 添加对于后侧雷达的Map角度限制 */
                    float LfP1_P2ToCarX_AngleAbs;
                    LfP1_P2ToCarX_AngleAbs = ABS_VALUE(LpstrMapCoor->fP1_P2ToCarX_Angle);
                    if(LpstrMapCoor->enuSideMapSns > SIDE_SNS_FRS)
                    {
                        if((LpstrMapCoor->fP1_P2_Dis > 400)&&(LfP1_P2ToCarX_AngleAbs > F_R_MAP_20_ANGLE))
                        {
                            Lu8MapAngleNoMatchFlag = 1;
                        }
                    }
                    
                    
                    /* 角度差值太大就不再更新后续的Map了 */
                    if(!Lu8MapAngleNoMatchFlag)
                    {
                        if(LpstrMapCoor->enuSideMapSns < SIDE_SNS_RLS)
                        {
                            /* 前侧雷达 */
                            if(LpstrSideAreaMap->P1_CarCoor.fObjX > LpstrMapCoor->P2_CarCoor.fObjX)
                            {
                                LpstrMapCoor->P2_CarCoor = LpstrSideAreaMap->P1_CarCoor;
                                LpstrMapCoor->P2_OdoCoor = LpstrSideAreaMap->P1_OdoCoor;
                            }
                        }
                        else
                        {
                            /* 后侧雷达 */
                            if(LpstrMapCoor->SideMapCarMovDir == SNS_CAR_BACKWARD)
                            {
                                if(LpstrSideAreaMap->P2_CarCoor.fObjX < LpstrMapCoor->P2_CarCoor.fObjX)
                                {
                                    LpstrMapCoor->P2_CarCoor = LpstrSideAreaMap->P2_CarCoor;
                                    LpstrMapCoor->P2_OdoCoor = LpstrSideAreaMap->P2_OdoCoor;
                                }
                            }
                            else
                            {
                                if(LpstrSideAreaMap->P1_CarCoor.fObjX > LpstrMapCoor->P2_CarCoor.fObjX)
                                {
                                    LpstrMapCoor->P2_CarCoor = LpstrSideAreaMap->P1_CarCoor;
                                    LpstrMapCoor->P2_OdoCoor = LpstrSideAreaMap->P1_OdoCoor;
                                }
                            }
                        }
                    }
    
                    LpstrMapCoor->u32UpdateSysTime = GdSystemMsTimer;
                    MapExistProbAdd(LpstrMapCoor->eMapObjId);
                    UpdateMapTypeCntToBuf(&LpstrMapCoor->ObjTypeCntBuf[0],LpstrSideAreaMap->ObjTypeCnt);
                    LpstrMapCoor->u8ObjTypeUpdateFlag = 1;
                    LpstrSideAreaMap->u8ValidObjCoorFlag = 0;
                    float LfP1_P2_X_Sub = 0;
                    LfP1_P2_X_Sub = ABS(LpstrMapCoor->P1_CarCoor.fObjX,LpstrMapCoor->P2_CarCoor.fObjX);
#if 0
                    if((LpstrMapCoor->eObjExistProb > OBJ_PROB_LV4)&&(LfP1_P2_X_Sub < 100))
                    {
                        LpstrMapCoor->eMapObjHeight = OBJ_HIGH;
                        LpstrMapCoor->u8SideMapHeightLockFlag = 1;
                    }
#endif
                    LpstrMapCoor->u16SideMapUpdateMasterDis = LpstrSideAreaMap->u16ValidMasterDis;
                    if(LpstrMapCoor->eMapObjHeight == OBJ_LOW)
                    {
                        if((LfP1_P2_X_Sub > 1200)&&(LpstrSideAreaMap->u16ValidMasterDis > 2200))
                        {
                            LpstrSideAreaMap->u8CurbNeedMasterCoorFlag = 1;
                        }
                    }

                    UpdateSideAreaPointMapEndInx(&GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns]);
                    //if((!LpstrMapCoor->u8SideMapHeightLockFlag)&&(GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns].u8AfterStartCnt < 3))
                    if(!LpstrMapCoor->u8SideMapHeightLockFlag)
                    {
                        //AnalysisSideAreaBufDataAndPrintf(&GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns],LpstrMapCoor->eMapObjId);
                        Lu8CreatMapLockHighFlag = AnalysisSideAreaBufDataWhenMapUpdate(&GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns],LpstrMapCoor->eMapObjId,LpstrSideAreaMap->u16ValidMasterDis);
                        if(Lu8CreatMapLockHighFlag)
                        {
                            LpstrMapCoor->u8SideMapHeightLockFlag = 1;
                            LpstrMapCoor->eMapObjHeight = OBJ_HIGH;
                        }
                    }
                    AnalysisSideAreaBufDataAndPrintf(&GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns],LpstrMapCoor->eMapObjId);
                    if(LpstrMapCoor->enuSideMapSns < SIDE_SNS_RLS)
                    {
                        SideMapModifyStartPointHandle(&GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns],LpstrMapCoor);
                    }
#if 0
                    if((LpstrMapCoor->eMapObjId == Lu8EndMapPrintfId))//&&(GfMessageTime > 4.5)&&(GfMessageTime < 6.5))
                    {
                        VS_PRINT("Update SideMapUpdateMasterDis 02,Map Id:%d,Time:%.3f,SideMapUpdateMasterDis:%d,ValidMasterDis:%d\r\n",LpstrMapCoor->eMapObjId,GfMessageTime,\
                            LpstrMapCoor->u16SideMapUpdateMasterDis,LpstrSideAreaMap->u16ValidMasterDis);
                    }
#endif
                }
            }
        }
        if(LpstrMapCoor->enuSideMapSns < SIDE_SNS_RLS)
        {
            if(GstrRamSnsCoor[0][0].cRadarX > LpstrMapCoor->P2_CarCoor.fObjX)
            {
                LpstrMapCoor->fSideMapToSelfSnsDis = ABS(LpstrMapCoor->P2_CarCoor.fObjX,GstrRamSnsCoor[0][0].cRadarX);
            }
            else
            {
                LpstrMapCoor->fSideMapToSelfSnsDis = 0;
            }
        }
        else
        {
            if(LpstrMapCoor->SideMapCarMovDir == SNS_CAR_BACKWARD)
            {
                if(GstrRamSnsCoor[1][0].cRadarX < LpstrMapCoor->P2_CarCoor.fObjX)
                {
                    LpstrMapCoor->fSideMapToSelfSnsDis = ABS(LpstrMapCoor->P2_CarCoor.fObjX,GstrRamSnsCoor[1][0].cRadarX);
                }
                else
                {
                    LpstrMapCoor->fSideMapToSelfSnsDis = 0;
                }
            }
            else
            {
                if(GstrRamSnsCoor[1][0].cRadarX > LpstrMapCoor->P2_CarCoor.fObjX)
                {
                    LpstrMapCoor->fSideMapToSelfSnsDis = ABS(LpstrMapCoor->P2_CarCoor.fObjX,GstrRamSnsCoor[1][0].cRadarX);
                }
                else
                {
                    LpstrMapCoor->fSideMapToSelfSnsDis = 0;
                }
            }

        }
#if 0
        if((LpstrMapCoor->eMapObjId == 0x03))//&&(GfMessageTime > 4.5)&&(GfMessageTime < 6.5))
        //if(LpstrMapCoor->enuSideMapSns == SIDE_SNS_FLS)
        {
            VS_PRINT("Map Id:%d,Time:%.3f,SideMapToSelfSnsDis:%.3f,SideMapUpdateSts:%d\r\n",LpstrMapCoor->eMapObjId,GfMessageTime,\
                LpstrMapCoor->fSideMapToSelfSnsDis,LpstrMapCoor->enuSideMapUpdateSts);
        }
#endif

        /* 待添加Map距离限制，并在融合时取消该帧的融合 */
        if(Lu8MapAngleNoMatchFlag)
        {
            //if(GstrParkingGuidenceData.eCurrentAdasSts == APA_GUIDANCE)
            if(1)
            {
                LpstrMapCoor->u8SideMapHeightLockFlag = 1;
                SideMapEndHnadle(LpstrMapCoor);
                LpstrSideAreaMap->u8CurbNeedMasterCoorFlag = 0;
                LpstrSideAreaMap->u8FarDisNoNeedMasterCoorFlag = 0;
                GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns].u8MapStartInx = 255;
                GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns].u8MapEndInx = 255;
#if 0
                if((LpstrMapCoor->eMapObjId == Lu8EndMapPrintfId))//&&(GfMessageTime > 4.5)&&(GfMessageTime < 6.5))
                {
                    VS_PRINT("End Map 02,Map Angle Change,Map Id:%d,Time:%.3f,SideMapToSelfSnsDis:%.3f,ExistProb:%d,Height:%d\r\n",LpstrMapCoor->eMapObjId,GfMessageTime,\
                        LpstrMapCoor->fSideMapToSelfSnsDis,LpstrMapCoor->eObjExistProb,LpstrMapCoor->eMapObjHeight);
                }
#endif
#if 0
                VS_PRINT("Map End By Angle Sub,Time:%.3f,MapId:%d,MapToCarAngle:%.2f,NewMapToCarAngle:%.2f,AngleSub:%.2f,P1_P2_Dis:%.3f,MapObjType:%d\r\n",GfMessageTime,LpstrMapCoor->eMapObjId,\
                    LpstrMapCoor->fP1_P2ToCarX_Angle*57.3,LfNewMapToCarAngle*57.3,LfAngleSub*57.3,\
                    LpstrMapCoor->fP1_P2_Dis,LpstrMapCoor->eMapObjType); 
#endif
            }
        }
        
        if(Lu8TwoPointNearFlag == 0)
        {
            /* 不匹配就更新匹配时间，超过阈值时间后，进入到侧边记忆更新状态 */
            if(LpstrMapCoor->enuSideMapUpdateSts == SIDE_MAP_MEMORY_P1_UPDATE_P2)
            {
                if((LpstrMapCoor->u16NoUpdateHoldTime > 1000)||(LpstrMapCoor->fSideMapToSelfSnsDis > 1000))
                {
                    if(LpstrMapCoor->enuSideMapSns < SIDE_SNS_RLS)
                    {
                        SideMapModifyEndPointHandle(&GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns],LpstrMapCoor);
                        SideMapCutPillarHandle(LpstrMapCoor);
                    }
                    JudgeSideMapHeightInEnd(LpstrMapCoor);
                    if(Gu16CarSpdForSnsUse > 400)
                    {
                        if((LpstrMapCoor->eObjExistProb > OBJ_PROB_LV1)&&(LpstrMapCoor->fP1_P2_Dis > 60))
                        {
                            LpstrMapCoor->eObjExistProb = OBJ_PROB_LV6;
                        }
                    }
                    LpstrMapCoor->u8SideMapHeightLockFlag = 1;
                    SideMapEndHnadle(LpstrMapCoor);
                    LpstrSideAreaMap->u8CurbNeedMasterCoorFlag = 0;
                    LpstrSideAreaMap->u8FarDisNoNeedMasterCoorFlag = 0;
                    GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns].u8MapStartInx = 255;
                    GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns].u8MapEndInx = 255;
#if 0
                    if((LpstrMapCoor->eMapObjId == Lu8EndMapPrintfId))//&&(GfMessageTime > 4.5)&&(GfMessageTime < 6.5))
                    {
                        VS_PRINT("End Map 03,Dis or Time out range,Map Id:%d,Time:%.3f,SideMapToSelfSnsDis:%.3f,ExistProb:%d,Height:%d\r\n",LpstrMapCoor->eMapObjId,GfMessageTime,\
                            LpstrMapCoor->fSideMapToSelfSnsDis,LpstrMapCoor->eObjExistProb,LpstrMapCoor->eMapObjHeight);
                    }
#endif
                }
            }
            else if(LpstrMapCoor->enuSideMapUpdateSts == SIDE_MAP_UPDATE_P1_P2)
            {
                if(LpstrMapCoor->u16NoUpdateHoldTime > 500)
                {
                    if((GstrParkingGuidenceData.eCurrentAdasSts == APA_GUIDANCE)&&(LpstrMapCoor->fMapToBumper_Dis < 800))
                    {
                        if(LpstrMapCoor->eObjExistProb > OBJ_PROB_LV1)
                        {
                            LpstrMapCoor->eObjExistProb = OBJ_PROB_LV5;
                            LpstrMapCoor->eMapObjHeight = OBJ_HIGH;
                        }
                    }
                    LpstrMapCoor->u8SideMapHeightLockFlag = 1;
                    SideMapEndHnadle(LpstrMapCoor);
                    LpstrSideAreaMap->u8CurbNeedMasterCoorFlag = 0;
                    LpstrSideAreaMap->u8FarDisNoNeedMasterCoorFlag = 0;
                    GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns].u8MapStartInx = 255;
                    GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns].u8MapEndInx = 255;
#if 0
                    if((LpstrMapCoor->eMapObjId == Lu8EndMapPrintfId))//&&(GfMessageTime > 4.5)&&(GfMessageTime < 6.5))
                    {
                        VS_PRINT("End Map 04,Point Map out range,Map Id:%d,Time:%.3f,SideMapToSelfSnsDis:%.3f,ExistProb:%d,Height:%d\r\n",LpstrMapCoor->eMapObjId,GfMessageTime,\
                            LpstrMapCoor->fSideMapToSelfSnsDis,LpstrMapCoor->eObjExistProb,LpstrMapCoor->eMapObjHeight);
                    }
#endif
                }
            }
        }

        if(LpstrSideAreaMap->u8ValidObjCoorFlag)
        {
            Lu16NewPointAndOldMapDisSub = ABS(LpstrMapCoor->u16SideMapUpdateMasterDis,LpstrSideAreaMap->u16ValidMasterDis);
            if(LpstrSideAreaMap->u16ValidMasterDis < 500)
            {
                Lu16NewPointAndOldMapDisSubLimit = 150;
            }
            else if(LpstrSideAreaMap->u16ValidMasterDis < 800)
            {
                Lu16NewPointAndOldMapDisSubLimit = 200;
            }
            else 
            {
                Lu16NewPointAndOldMapDisSubLimit = 300;
            }
            if(Lu16NewPointAndOldMapDisSub > Lu16NewPointAndOldMapDisSubLimit)
            {
                LpstrMapCoor->u8SideMapAndNewPointNomatchCnt++;
            }
#if 0
            if((LpstrMapCoor->eMapObjId == Lu8EndMapPrintfId))//&&(GfMessageTime > 4.5)&&(GfMessageTime < 6.5))
            {
                VS_PRINT("Near New point,Map Id:%d,Time:%.3f,SideMapUpdateMasterDis:%d,ValidMasterDis:%d\r\n",LpstrMapCoor->eMapObjId,GfMessageTime,\
                    LpstrMapCoor->u16SideMapUpdateMasterDis,LpstrSideAreaMap->u16ValidMasterDis);
            }
#endif
            if(LpstrMapCoor->u8SideMapAndNewPointNomatchCnt >= 2)// 2
            {
                LpstrSideAreaMap->u8ValidObjCoorFlag = 1;/* 保持为1，同时结束正在绘制的Map */
                LpstrSideAreaMap->u8CurbNeedMasterCoorFlag = 0;
                LpstrSideAreaMap->u8FarDisNoNeedMasterCoorFlag = 0;
                //JudgeSideMapHeightInEnd(LpstrMapCoor);
                LpstrMapCoor->u8SideMapHeightLockFlag = 1;
                SideMapEndHnadle(LpstrMapCoor);
                GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns].u8MapStartInx = 255;
                GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns].u8MapEndInx = 255;
#if 0
                if((LpstrMapCoor->eMapObjId == Lu8EndMapPrintfId))//&&(GfMessageTime > 4.5)&&(GfMessageTime < 6.5))
                {
                    VS_PRINT("End Map 05,Near New point,Map Id:%d,Time:%.3f,SideMapToSelfSnsDis:%.3f,ExistProb:%d,Height:%d\r\n",LpstrMapCoor->eMapObjId,GfMessageTime,\
                        LpstrMapCoor->fSideMapToSelfSnsDis,LpstrMapCoor->eObjExistProb,LpstrMapCoor->eMapObjHeight);
                }
#endif
            }
            else
            {
                LpstrSideAreaMap->u8ValidObjCoorFlag = 0;
            }
        }

#if 0
        if((LpstrMapCoor->enuSideMapSns == SIDE_SNS_FRS)&&(LpstrMapCoor->eMapObjId == 0x01))
        {
            VS_PRINT("Time:%.3f,MapEndInxNoUpdateCnt:%d\r\n",GfMessageTime,GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns].u8MapEndInxNoUpdateCnt); 
        }
#endif

        /* 更新MapEndInxNoUpdateCnt计数 */
        if(LpstrMapCoor->fP1_P2_Dis > 700)
        {
            if(GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns].u8ContinueUseMasterCnt > 3)
            {
                ObjCoorType LstrFirstMasterOdoCoor;
                ObjCoorType LstrFirstMasterCarCoor;
                float LfModify_Y_Sub;
                uint32 Lu32FirstMasterTime;
                uint8 Lu8FirstUseMasterInx;
                uint8 Lu8ModifyFlag = 0;
                Lu8FirstUseMasterInx = GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns].u8FirstUseMasterInx;
                LstrFirstMasterOdoCoor = GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns].Buf[Lu8FirstUseMasterInx].PointOdoCoor;
                Lu32FirstMasterTime = GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns].Buf[Lu8FirstUseMasterInx].u32DetSysTime;
                PubAI_TransObjOdoCoorToCarCoor(&LstrFirstMasterOdoCoor.fObjX,&LstrFirstMasterOdoCoor.fObjY,\
                    &LstrFirstMasterCarCoor.fObjX,&LstrFirstMasterCarCoor.fObjY);
#if 0
                VS_PRINT("Modify Map Coor,Time:%.3f,MapId:%d,ContinueUseMasterCnt:%d,FirstUseMasterInx:%d,First_X:%.3f,First_Y:%.3f,P2_X:%.3f,P2_Y:%.3f,FirstMasterTime:%ld\r\n",GfMessageTime,LpstrMapCoor->eMapObjId,\
                    GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns].u8ContinueUseMasterCnt,\
                    Lu8FirstUseMasterInx,LstrFirstMasterOdoCoor.fObjX,LstrFirstMasterOdoCoor.fObjY,\
                    LpstrMapCoor->P2_OdoCoor.fObjX,LpstrMapCoor->P2_OdoCoor.fObjY,Lu32FirstMasterTime); 
#endif

                /* 先矫正P2点的坐标--矫正有两个原则，1.只裁剪不拉长；2Y值偏差太大不裁剪 */
                if(LpstrMapCoor->enuSideMapSns < SIDE_SNS_RLS)
                {
                    if(LpstrMapCoor->P2_CarCoor.fObjX > LstrFirstMasterCarCoor.fObjX)
                    {
                        LfModify_Y_Sub = ABS(LpstrMapCoor->P2_CarCoor.fObjX,LstrFirstMasterCarCoor.fObjX);
                        if(LfModify_Y_Sub < 200)
                        {
                            Lu8ModifyFlag = 1;
                        }
                    }
                }
                else
                {
                    if(LpstrMapCoor->P2_CarCoor.fObjX < LstrFirstMasterCarCoor.fObjX)
                    {
                        LfModify_Y_Sub = ABS(LpstrMapCoor->P2_CarCoor.fObjX,LstrFirstMasterCarCoor.fObjX);
                        if(LfModify_Y_Sub < 200)
                        {
                            Lu8ModifyFlag = 1;
                        }
                    }
                }

                if(Lu8ModifyFlag)
                {
                    LpstrMapCoor->P2_OdoCoor = LstrFirstMasterOdoCoor;
                    LpstrMapCoor->P2_CarCoor = LstrFirstMasterCarCoor;
                    JudgeSideMapHeightInEnd(LpstrMapCoor);
                    if(LpstrMapCoor->eMapObjHeight == OBJ_HIGH)
                    {
                        LpstrMapCoor->eObjExistProb = OBJ_PROB_LV5;
                    }
                    LpstrMapCoor->u8SideMapHeightLockFlag = 1;
                    SideMapEndHnadle(LpstrMapCoor);
                    LpstrSideAreaMap->u8CurbNeedMasterCoorFlag = 0;
                    LpstrSideAreaMap->u8FarDisNoNeedMasterCoorFlag = 0;
                    
                    GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns].u8MapStartInx = 255;
                    GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns].u8MapEndInx = 255;
#if 0
                    if((LpstrMapCoor->eMapObjId == Lu8EndMapPrintfId))//&&(GfMessageTime > 4.5)&&(GfMessageTime < 6.5))
                    {
                        VS_PRINT("End Map 06,Continue Master No Match,Map Id:%d,Time:%.3f,SideMapToSelfSnsDis:%.3f,ExistProb:%d,Height:%d\r\n",LpstrMapCoor->eMapObjId,GfMessageTime,\
                            LpstrMapCoor->fSideMapToSelfSnsDis,LpstrMapCoor->eObjExistProb,LpstrMapCoor->eMapObjHeight);
                    }
#endif
                }
            }
        }

        /* 针对扫频主发连续无效，提前结束Map的策略 */
        uint8 Lu8Group,Lu8Ch;
        uint16 Lu16DisSub = 0;
        if(LpstrMapCoor->enuSideMapSns == SIDE_SNS_FLS)
        {
            Lu8Group = 0;
            Lu8Ch = 0;
        }
        else if(LpstrMapCoor->enuSideMapSns == SIDE_SNS_FRS)
        {
            Lu8Group = 0;
            Lu8Ch = 5;
        }
        else if(LpstrMapCoor->enuSideMapSns == SIDE_SNS_RLS)
        {
            Lu8Group = 1;
            Lu8Ch = 0;
        }
        else
        {
            Lu8Group = 1;
            Lu8Ch = 5;
        }
        if(GstrMapSnsRealTimeDis[Lu8Group][Lu8Ch].u8RealDisUpdateFlag)
        {
            if(GstrMapSnsRealTimeDis[Lu8Group][Lu8Ch].u16MasDis > (LpstrSideAreaMap->u16ValidMasterDis+200))
            {
                LpstrSideAreaMap->u8MasterDisNoMatchCnt++;
            }
            else
            {
                LpstrSideAreaMap->u8MasterDisNoMatchCnt = 0;
            }
        }

#if 0
        if((LpstrMapCoor->enuSideMapSns == SIDE_SNS_FRS)&&(LpstrMapCoor->eMapObjId == 0x0))
        {
            if(GstrMapSnsRealTimeDis[Lu8Group][Lu8Ch].u8RealDisUpdateFlag)
            {
                VS_PRINT("FRS Dis,Map Id:%d,Time:%.3f,SideMapToSelfSnsDis:%.3f,MasDis:%d,MasterHeight:%d,ValidMasterDis:%d,EndInxNoUpdateCnt:%d\r\n",LpstrMapCoor->eMapObjId,GfMessageTime,\
                    LpstrMapCoor->fSideMapToSelfSnsDis,GstrMapSnsRealTimeDis[Lu8Group][Lu8Ch].u16MasDis,\
                    GstrMapSnsRealTimeDis[Lu8Group][Lu8Ch].u16MasterHeight,LpstrSideAreaMap->u16ValidMasterDis,\
                    GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns].u8MapEndInxNoUpdateCnt);
            }
        }
#endif

        
        if(Gu16CarSpdForSnsUse > 300)
        {
            if(LpstrSideAreaMap->u8MasterDisNoMatchCnt > 2)
            {
                if(Gu16CarSpdForSnsUse > 500)
                {
                    if((LpstrMapCoor->eObjExistProb > OBJ_PROB_LV1)&&(LpstrMapCoor->fP1_P2_Dis > 60))
                    {
                        LpstrMapCoor->eObjExistProb = OBJ_PROB_LV6;
                    }
                }
                JudgeSideMapHeightInEnd(LpstrMapCoor);                
                LpstrMapCoor->u8SideMapHeightLockFlag = 1;
                SideMapEndHnadle(LpstrMapCoor);
                if(LpstrMapCoor->enuSideMapSns < SIDE_SNS_RLS)
                {
                    SideMapCutPillarHandle(LpstrMapCoor);
                }
                LpstrSideAreaMap->u8CurbNeedMasterCoorFlag = 0;
                LpstrSideAreaMap->u8FarDisNoNeedMasterCoorFlag = 0;
                GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns].u8MapStartInx = 255;
                GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns].u8MapEndInx = 255;
#if 0
                if((LpstrMapCoor->eMapObjId == Lu8EndMapPrintfId))//&&(GfMessageTime > 4.5)&&(GfMessageTime < 6.5))
                {
                    VS_PRINT("End Map 07,Continue Dis No Match,Map Id:%d,Time:%.3f,SideMapToSelfSnsDis:%.3f,ExistProb:%d,Height:%d\r\n",LpstrMapCoor->eMapObjId,GfMessageTime,\
                        LpstrMapCoor->fSideMapToSelfSnsDis,LpstrMapCoor->eObjExistProb,LpstrMapCoor->eMapObjHeight);
                }
#endif
            }
        }

        /* 不匹配时的高度进一步处理 */
        if(!LpstrMapCoor->u8SideMapHeightLockFlag)
        {
            Lu8CreatMapLockHighFlag = AnalysisSideAreaBufDataWhenMapNoUpdate(&GstrSideAreaPointBuf[LpstrMapCoor->enuSideMapSns],LpstrMapCoor->eMapObjId,LpstrMapCoor->enuSideMapSns,LpstrMapCoor->u16SideMapUpdateMasterDis);
            if(Lu8CreatMapLockHighFlag)
            {
                LpstrMapCoor->u8SideMapHeightLockFlag = 1;
                LpstrMapCoor->eMapObjHeight = OBJ_HIGH;
            }
        }

        /* 针对300cm外，采用单主发定位的短小Map，采用删除策略 */
#if 0
        if(LpstrMapCoor->enuSideMapUpdateSts == SIDE_MAP_MEMORY_P1_MEMORY_P2)
        {
            if(LpstrSideAreaMap->u16ValidMasterDis > 3000)
            {
                if((LpstrMapCoor->fP1_P2_Dis < 450)||(LpstrMapCoor->eObjExistProb < OBJ_PROB_LV4))
                {
                    DeleteMap(LpstrMapCoor->eMapObjId);
                    GstrObjMap.u8MapNum--;
                }
            }
        }
#endif
        if(LpstrMapCoor->enuSideMapUpdateSts == SIDE_MAP_MEMORY_P1_UPDATE_P2)
        {
            if(LpstrMapCoor->enuSideMapSns == SIDE_SNS_FLS)
            {
                if(LpstrMapCoor->fP1_P2ToCarX_Angle > F_R_MAP_5_ANGLE)
                {
                    GstrSideAreaMap[SIDE_SNS_FLS].u8SnsFarawayLineFlag = 1;
                }
            }
            else if(LpstrMapCoor->enuSideMapSns == SIDE_SNS_FRS)
            {
                if(LpstrMapCoor->fP1_P2ToCarX_Angle < (-F_R_MAP_5_ANGLE))
                {
                    GstrSideAreaMap[SIDE_SNS_FRS].u8SnsFarawayLineFlag = 1;
                }
            }
        }
        else if(LpstrMapCoor->enuSideMapUpdateSts == SIDE_MAP_MEMORY_P1_MEMORY_P2)
        {
            if(LpstrMapCoor->enuSideMapSns == SIDE_SNS_FLS)
            {
                GstrSideAreaMap[SIDE_SNS_FLS].u8SnsFarawayLineFlag = 0;
            }
            else if(LpstrMapCoor->enuSideMapSns == SIDE_SNS_FRS)
            {
                GstrSideAreaMap[SIDE_SNS_FRS].u8SnsFarawayLineFlag = 0;
            }
        }
#if 0
        if((LpstrMapCoor->enuSideMapUpdateSts == SIDE_MAP_MEMORY_P1_MEMORY_P2)&&(LpstrMapCoor->fP1_P2_Dis > 500))
        {
            VS_PRINT("Side Map End,Map Id:%d,Time:%.3f,P1_X:%.3f,P1_Y:%.3f,P2_X:%.3f,P2_Y:%.3f,P1_P2_Dis:%.2f,P1_P2ToCarX_Angle:%.3f\r\n",LpstrMapCoor->eMapObjId,GfMessageTime,\
                LpstrMapCoor->P1_OdoCoor.fObjX,LpstrMapCoor->P1_OdoCoor.fObjY,LpstrMapCoor->P2_OdoCoor.fObjX,LpstrMapCoor->P2_OdoCoor.fObjY,\
                LpstrMapCoor->fP1_P2_Dis,LpstrMapCoor->fP1_P2ToCarX_Angle*57.3);
        }
#endif
    }
}



/******************************************************************************
 * 函数名称: CalSideSnsNearestMapId
 * 
 * 功能描述: 轮询查询侧边四个探头附近最近的Map Id，用于侧雷达画Map,避开重叠使用
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-04-20 09:11   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void CalSideSnsNearestMapId(ObjMapCoorType *LpstrMapCoor)
{
    float LfP1_P2ToCarX_AngleAbs;
    float LfFirstX = 0.0;
    float LfSecondX = 0.0;
    float LfSns_X_Coor,LfSns_Y_Coor;
    float LfLineMapY_Coor;
    float LfHysteresisDis;
    /* 根据车速不同，设置不同的边界回执阈值 */
    if(Gu16CarSpdForSnsUse < 700)
    {
        LfHysteresisDis = 1000;
    }
    else if(Gu16CarSpdForSnsUse < 1000)
    {
        LfHysteresisDis = 1400;
    }
    else if(Gu16CarSpdForSnsUse < 1500)
    {
        LfHysteresisDis = 1800;
    }
    else if(Gu16CarSpdForSnsUse < 2000)
    {
        LfHysteresisDis = 2200;
    }
    else
    {
        LfHysteresisDis = 2500;
    }
    if((LpstrMapCoor->enuSideMapUpdateSts == SIDE_MAP_MEMORY_P1_MEMORY_P2)&&(LpstrMapCoor->fP1_P2_Dis > 50))
    {        
        LfP1_P2ToCarX_AngleAbs = ABS_VALUE(LpstrMapCoor->fP1_P2ToCarX_Angle);

        if(LfP1_P2ToCarX_AngleAbs < F_R_MAP_40_ANGLE)
        {
            if(LpstrMapCoor->P1_CarCoor.fObjX < LpstrMapCoor->P2_CarCoor.fObjX)
            {
                LfFirstX = LpstrMapCoor->P1_CarCoor.fObjX;
                LfSecondX = LpstrMapCoor->P2_CarCoor.fObjX;
            }
            else
            {
                LfFirstX = LpstrMapCoor->P2_CarCoor.fObjX;
                LfSecondX = LpstrMapCoor->P1_CarCoor.fObjX;
            }
            LfLineMapY_Coor = LpstrMapCoor->P1_CarCoor.fObjY+LpstrMapCoor->P2_CarCoor.fObjY;
            LfLineMapY_Coor = LfLineMapY_Coor/2;
            LfLineMapY_Coor = ABS_VALUE(LfLineMapY_Coor);
            
            LfFirstX = LfFirstX-LfHysteresisDis;
            LfSecondX = LfSecondX+LfHysteresisDis;

            if((LpstrMapCoor->P1_CarCoor.fObjY < -SDW_SNS_Y_LIMIT_DIS)&&(LpstrMapCoor->P2_CarCoor.fObjY < -SDW_SNS_Y_LIMIT_DIS))
            {
                /* 障碍物在车身的右边，因此只跟FRS和RRS有关系 */
                LfSns_X_Coor = GstrRamSnsCoor[0][5].cRadarX;
                LfSns_Y_Coor = GstrRamSnsCoor[0][5].cRadarY;
                if((LfFirstX < LfSns_X_Coor)&&(LfSns_X_Coor < LfSecondX))
                {
                    if(LfLineMapY_Coor < GstrSideAreaMap[SIDE_SNS_FRS].fNearestMapToSnsDis)
                    {
                        GstrSideAreaMap[SIDE_SNS_FRS].u8NearstMapId = LpstrMapCoor->eMapObjId;
                        GstrSideAreaMap[SIDE_SNS_FRS].fNearestMapToSnsDis = LfLineMapY_Coor;
                    }
                }

                LfSns_X_Coor = GstrRamSnsCoor[1][5].cRadarX;
                LfSns_Y_Coor = GstrRamSnsCoor[1][5].cRadarY;
                if((LfFirstX < LfSns_X_Coor)&&(LfSns_X_Coor < LfSecondX))
                {
                    if(LfLineMapY_Coor < GstrSideAreaMap[SIDE_SNS_RRS].fNearestMapToSnsDis)
                    {
                        GstrSideAreaMap[SIDE_SNS_RRS].u8NearstMapId = LpstrMapCoor->eMapObjId;
                        GstrSideAreaMap[SIDE_SNS_RRS].fNearestMapToSnsDis = LfLineMapY_Coor;
                    }
                }
            }
            else if((LpstrMapCoor->P1_CarCoor.fObjY > SDW_SNS_Y_LIMIT_DIS)&&(LpstrMapCoor->P2_CarCoor.fObjY > SDW_SNS_Y_LIMIT_DIS))
            {
                /* 障碍物在车身的左边，因此只跟FLS和RLS有关系 */
                LfSns_X_Coor = GstrRamSnsCoor[0][0].cRadarX;
                LfSns_Y_Coor = GstrRamSnsCoor[0][0].cRadarY;
                if((LfFirstX < LfSns_X_Coor)&&(LfSns_X_Coor < LfSecondX))
                {
                    if(LfLineMapY_Coor < GstrSideAreaMap[SIDE_SNS_FLS].fNearestMapToSnsDis)
                    {
                        GstrSideAreaMap[SIDE_SNS_FLS].u8NearstMapId = LpstrMapCoor->eMapObjId;
                        GstrSideAreaMap[SIDE_SNS_FLS].fNearestMapToSnsDis = LfLineMapY_Coor;
                    }
                }
                
                LfSns_X_Coor = GstrRamSnsCoor[1][0].cRadarX;
                LfSns_Y_Coor = GstrRamSnsCoor[1][0].cRadarY;
                if((LfFirstX < LfSns_X_Coor)&&(LfSns_X_Coor < LfSecondX))
                {
                    if(LfLineMapY_Coor < GstrSideAreaMap[SIDE_SNS_RLS].fNearestMapToSnsDis)
                    {
                        GstrSideAreaMap[SIDE_SNS_RLS].u8NearstMapId = LpstrMapCoor->eMapObjId;
                        GstrSideAreaMap[SIDE_SNS_RLS].fNearestMapToSnsDis = LfLineMapY_Coor;
                    }
                }
            }
        }
    }
}

/******************************************************************************
 * 函数名称: TransMapOdoCoorToCarCoor
 * 
 * 功能描述: 每次更新Map模块前，首先通过Odo坐标更新Map的车辆坐标
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-06 14:31   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void TransMapOdoCoorToCarCoor(void)
{
    uint8 i;
    ObjMapInfoType *LstrObjMap;
    LstrObjMap = &GstrObjMap;
    eMapObjIdType LeMapId;
    PDCSnsUseOdoType *LpStrPDCSnsUseOdo;
    
    LpStrPDCSnsUseOdo = &GstrPDCSnsUseOdo;

    for(i = 0; i < SIDE_SNS_NUM; i++)
    {
        GstrSideAreaMap[i].u8NearstMapId = 31;
        GstrSideAreaMap[i].fNearestMapToSnsDis = 65535;
    }


    /* 更新Odo坐标 */
    PubAI_UpdateCarOdoCoor(LpStrPDCSnsUseOdo->fCar_X_Coor,LpStrPDCSnsUseOdo->fCar_Y_Coor,LpStrPDCSnsUseOdo->fCar_SinYawAngle,LpStrPDCSnsUseOdo->fCar_CosYawAngle);

    for(LeMapId = MAP_OBJ_0; LeMapId < MAP_OBJ_NUM; LeMapId++)
    {
        if(LstrObjMap->ObjMapCoor[LeMapId].u8MapExistFlag)
        {
            /* 第一步；更新现存Map的车辆坐标 */
            TransfMapOdoCoorToCarCoor(&LstrObjMap->ObjMapCoor[LeMapId]);
            if(LstrObjMap->ObjMapCoor[LeMapId].u8Map_SFR_AreaFlag == 0x1)
            {
                CalSideSnsNearestMapId(&LstrObjMap->ObjMapCoor[LeMapId]);
            }
        }
    }
}


/******************************************************************************
 * 函数名称: LineMapFusionHandle
 * 
 * 功能描述: 线状Map融合处理
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-01-16 20:25   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void LineMapFusionHandle(void)
{
    uint8 Lu8SnsGroup = 2;
    MapLineBufType *LpsrMapLineBuf;
    uint8 i,j;
    float LfLine_X_Coor;
    float LfLine_Left_Y_Coor;
    float LfLine_Right_Y_Coor;
    eMapObjIdType LeMapId;
    ObjMapInfoType *LstrObjMap;
    LstrObjMap = &GstrObjMap;

    if(GstrSnsCarMovSts[0][0].eCarDir == SNS_CAR_FORWARD)
    {
        Lu8SnsGroup = 0;
    }
    else if(GstrSnsCarMovSts[0][0].eCarDir == SNS_CAR_BACKWARD)
    {
        Lu8SnsGroup = 1;
    }
    if(Lu8SnsGroup < 2)
    {
        LpsrMapLineBuf = &GstrMapLineBuf[Lu8SnsGroup];
        if(LpsrMapLineBuf->u8WaitFusionLineCnt > 0)
        {
            for(i = 0; i < LpsrMapLineBuf->u8WaitFusionLineCnt; i++)
            {
                if(LpsrMapLineBuf->LineMapUnit[i].u8LineMapWaitFusionFlag)
                {
#if 0
                    VS_PRINT("Line Fusion OK,Time:%.3f,WaitFusionLineCnt:%d,Line Id:%d,PointCnt:%d,Point Id:%d,%d,%d\r\n",GfMessageTime,LpsrMapLineBuf->u8WaitFusionLineCnt,\
                        LpsrMapLineBuf->LineMapUnit[i].eLineMapId,LpsrMapLineBuf->LineMapUnit[i].u8BeFusionMapCnt,\
                        LpsrMapLineBuf->LineMapUnit[i].eBeFusionMapId[0],LpsrMapLineBuf->LineMapUnit[i].eBeFusionMapId[1],\
                        LpsrMapLineBuf->LineMapUnit[i].eBeFusionMapId[2]);
#endif
                    
                    LfLine_Left_Y_Coor = LpsrMapLineBuf->LineMapUnit[i].fLineLeftY_Coor;
                    LfLine_Right_Y_Coor = LpsrMapLineBuf->LineMapUnit[i].fLineRightY_Coor;
                    LfLine_X_Coor = LpsrMapLineBuf->LineMapUnit[i].fLineMapX_Coor;
                    for(j = 0; j < LpsrMapLineBuf->LineMapUnit[i].u8BeFusionMapCnt; j++)
                    {
                        LeMapId = LpsrMapLineBuf->LineMapUnit[i].eBeFusionMapId[j];
                        if(LstrObjMap->ObjMapCoor[LeMapId].eMapObjType == OBJ_TYPE_POINT)
                        {
                            if(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY > LfLine_Left_Y_Coor)
                            {
                                LfLine_Left_Y_Coor = LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY;
                            }
                            if(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY < LfLine_Right_Y_Coor)
                            {
                                LfLine_Right_Y_Coor = LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY;
                            }
                            LfLine_X_Coor += LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjX;
                            LfLine_X_Coor = LfLine_X_Coor/2;
                        }
                        else
                        {
                            if(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY > LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjY)
                            {
                                if(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY > LfLine_Left_Y_Coor)
                                {
                                    LfLine_Left_Y_Coor = LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY;
                                }
                                if(LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjY < LfLine_Right_Y_Coor)
                                {
                                    LfLine_Right_Y_Coor = LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjY;
                                }
                            }
                            else
                            {
                                if(LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjY > LfLine_Left_Y_Coor)
                                {
                                    LfLine_Left_Y_Coor = LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjY;
                                }
                                if(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY < LfLine_Right_Y_Coor)
                                {
                                    LfLine_Right_Y_Coor = LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY;
                                }
                            }
                            LfLine_X_Coor += LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjX;
                            LfLine_X_Coor = LfLine_X_Coor/2;
                            LfLine_X_Coor += LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjX;
                            LfLine_X_Coor = LfLine_X_Coor/2;
                        }
                        DeleteMap(LeMapId);
#if 0
                        VS_PRINT("Line Fusion Delete Id,Time:%.3f,Map_Num:%d,Dele Map Id:%d,Line Map Id:%d\r\n\r\n",GfMessageTime,LstrObjMap->u8MapNum,\
                            LeMapId,LpsrMapLineBuf->LineMapUnit[i].eLineMapId);
#endif
                        LstrObjMap->u8MapNum--;
                    }
                    /* 更新最新的Map坐标 */
                    LeMapId = LpsrMapLineBuf->LineMapUnit[i].eLineMapId;
                    LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjX = LfLine_X_Coor;
                    LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY = LfLine_Left_Y_Coor;
                    LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjX = LfLine_X_Coor;
                    LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjY = LfLine_Right_Y_Coor;
                    LstrObjMap->ObjMapCoor[LeMapId].u32UpdateSysTime = GdSystemMsTimer;
                    LstrObjMap->ObjMapCoor[LeMapId].u32CreatMapTime = GdSystemMsTimer;
                    /* 需要同步更新Odo坐标 */
                    PubAI_TransObjCarCoorToOdoCoor(&LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjX,&LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY,\
                        &LstrObjMap->ObjMapCoor[LeMapId].P1_OdoCoor.fObjX,&LstrObjMap->ObjMapCoor[LeMapId].P1_OdoCoor.fObjY);
                    PubAI_TransObjCarCoorToOdoCoor(&LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjX,&LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjY,\
                        &LstrObjMap->ObjMapCoor[LeMapId].P2_OdoCoor.fObjX,&LstrObjMap->ObjMapCoor[LeMapId].P2_OdoCoor.fObjY);
                }
            }
        }
    }
}

/******************************************************************************
 * 函数名称: UpdateMapInforAndDele
 * 
 * 功能描述: 更新探头的信息，及删除Map
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-06 14:45   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void UpdateMapInforAndDele(void)
{
    ObjMapInfoType *LstrObjMap;
    LstrObjMap = &GstrObjMap;
    eMapObjIdType LeMapId;
    eMapObjIdType LeNeedDeleteMapId = MAP_NONE;
    uint8 Lu8MapNeedDeleteFlag;

    GstrSideAreaMap[SIDE_SNS_FLS].u8NearExistMapFlag = 0;
    GstrSideAreaMap[SIDE_SNS_FRS].u8NearExistMapFlag = 0;
    GstrSideAreaMap[SIDE_SNS_RLS].u8NearExistMapFlag = 0;
    GstrSideAreaMap[SIDE_SNS_RRS].u8NearExistMapFlag = 0;
    
    for(LeMapId = MAP_OBJ_0; LeMapId < MAP_OBJ_NUM; LeMapId++)
    {
        if((LstrObjMap->ObjMapCoor[LeMapId].u8MapExistFlag)&&(LstrObjMap->ObjMapCoor[LeMapId].u8Map_SFR_AreaFlag == 1))
        {
            Lu8MapNeedDeleteFlag = UpdateMapInf(&LstrObjMap->ObjMapCoor[LeMapId]);
            if(Lu8MapNeedDeleteFlag)
            {
                if(LeNeedDeleteMapId == MAP_NONE)
                {
                    LeNeedDeleteMapId = LeMapId;
                }
            }
        }
    }
    if(LeNeedDeleteMapId != MAP_NONE)
    {
        DeleteMap(LeNeedDeleteMapId);
        LstrObjMap->u8MapNum--;
    }
    uint8 Lu8SnsCh;

    for(Lu8SnsCh = 0; Lu8SnsCh < 6; Lu8SnsCh++)
    {
        GstrMapSnsRealTimeDis[0][Lu8SnsCh].u8RealDisUpdateFlag = 0;
        GstrMapSnsRealTimeDis[1][Lu8SnsCh].u8RealDisUpdateFlag = 0;
    }   
}



/******************************************************************************
 * 函数名称: ObjMapBuildAndUpdate
 * 
 * 功能描述: 前后雷达Map的更新及输出到CAN
 * 
 * 输入参数:LenuGroup--探头组别 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-08-26 15:58   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void ObjMapBuildAndUpdate(PDCSnsGroupType LenuGroup)
{
    ObjMapInfoType *LstrObjMap;
    SnsPointCloudBufType *LpstrPointCloudBuf;
    eMapObjIdType LeMapId;
    eMapObjIdType LeNeedDeleteMapId = MAP_NONE;
    uint8 Lu8MapNeedDeleteFlag;
    PointCloudAreaType LenuPointCloudArea;
    uint8 i,j;
    uint8 Lu8PointToMapNearFlag;
    float LstrPointToBumperDis;
    ObjMAPAreaType LenuMapDetailArea = MAP_AREA_NONE;
    uint8 Lu8CreatNewMapFlag = 0;
    MapBigWallBufType *LstrMapBigWallBuf;
    MapBigWallUnitType LstrMapBigWallUnit;
    SideAreaMapType *LpstrSideAreaMap;
    SideSnsIndexType LeSideSnsInx;
    MapLineBufType *LpsrMapLineBuf;
    
    LpsrMapLineBuf = &GstrMapLineBuf[LenuGroup];
    LstrMapBigWallBuf = &GstrMapBigWallBuf[LenuGroup];
    LstrObjMap = &GstrObjMap;
    GenuGroupForPrintf = LenuGroup;
    LstrMapBigWallBuf->u8BigWallPointCnt = 0;
    GstrMapLineBuf[0].u8WaitFusionLineCnt = 0;
    GstrMapLineBuf[1].u8WaitFusionLineCnt = 0;
    for(LeMapId = MAP_OBJ_0; LeMapId < MAP_OBJ_NUM; LeMapId++)
    {
        if(LstrObjMap->ObjMapCoor[LeMapId].u8MapExistFlag)
        {
            /* 以下更新分两种，一类是侧边运动状态下的记忆的Map更新，一类是前后Map实时更新的 */
            if(LstrObjMap->ObjMapCoor[LeMapId].u8Map_SFR_AreaFlag == 1)
            {
                if(LstrObjMap->ObjMapCoor[LeMapId].enuSideMapSns != SIDE_SNS_NONE)
                {
                    LeSideSnsInx = LstrObjMap->ObjMapCoor[LeMapId].enuSideMapSns;
                    LpstrSideAreaMap = &GstrSideAreaMap[LeSideSnsInx];
                    ObjSideMapBuildAndUpdate(&LstrObjMap->ObjMapCoor[LeMapId],LpstrSideAreaMap);
#if 0
                    if(GstrSnsCarMovSts[LenuGroup][0].eCarDir == SNS_CAR_STOP)
                    {
                        for(LenuPointCloudArea = POINT_CLOUD_AREA0; LenuPointCloudArea < POINT_CLOUD_AREA_NUM; LenuPointCloudArea++)
                        {
                            LpstrPointCloudBuf = &GstrPointCloudBuf[LenuGroup][LenuPointCloudArea];
                            for(j = 0 ; j < LpstrPointCloudBuf->u8AreaMapNum; j++)
                            {
                                if((!LpstrPointCloudBuf->AreaMap[j].u8BeFusionToRealMapFlag)&&(LpstrPointCloudBuf->AreaMap[j].enuSideMapSns == SIDE_SNS_NONE))
                                {
                                    if(LstrObjMap->ObjMapCoor[LeMapId].eMapObjType == OBJ_TYPE_POINT)
                                    {
                                        Lu8PointToMapNearFlag = JudgePointToMapPointNear(&LpstrPointCloudBuf->AreaMap[j].P1_CarCoor,&LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor,LstrObjMap->ObjMapCoor[LeMapId].eP1_DetailArea);
                                        if(Lu8PointToMapNearFlag)
                                        {
                                            LpstrPointCloudBuf->AreaMap[j].u8BeFusionToRealMapFlag = 1;
                                        }
                                    }
                                    else
                                    {
                                        Lu8PointToMapNearFlag = JudgePointToMapLineNear(&LpstrPointCloudBuf->AreaMap[j].P1_CarCoor,&LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor,&LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor);
                                        if(Lu8PointToMapNearFlag)
                                        {
                                            LpstrPointCloudBuf->AreaMap[j].u8BeFusionToRealMapFlag = 1;
                                        }
                                    }
                                }
                            }
                        }
                    }
#endif
                }
            }
        }
    }

    /* 侧边创建新的Map点 */
    for(LeSideSnsInx = SIDE_SNS_FLS; LeSideSnsInx < SIDE_SNS_NUM; LeSideSnsInx++)
    {
        LpstrSideAreaMap = &GstrSideAreaMap[LeSideSnsInx];
        if(LpstrSideAreaMap->u8ValidObjCoorFlag)
        {
            LpstrSideAreaMap->u8ValidObjCoorFlag = 0;
            LeMapId = NewBuildSideMapPoint(LpstrSideAreaMap,LeSideSnsInx);
            if(LeMapId != MAP_NONE)
            {
                LstrObjMap->ObjMapCoor[LeMapId].enuSideMapSns = LeSideSnsInx;
                LstrObjMap->ObjMapCoor[LeMapId].enuSideMapUpdateSts = SIDE_MAP_UPDATE_P1_P2;
                LstrObjMap->ObjMapCoor[LeMapId].eObjHeightProb = OBJ_PROB_LV2;
            }
            break;
        }
    }
}

/******************************************************************************
 * 函数名称: SideMapFusionHandle
 * 
 * 功能描述: 侧边Map的融合处理；仅把Map中包含与被包含的进行融合，不对临近融合(融合偏差更大)
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-25 20:26   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void SideMapFusionHandle(void)
{
    ObjMapInfoType *LstrObjMap;
    eMapObjIdType LeWaitFusioMapId;
    eMapObjIdType LeFindFusionMapId = MAP_NONE;
    eMapObjIdType LeMapInx;
    float LfWaitMapAngleAbs;
    float LfWaitMapMin_X,LfWaitMapMin_Y;
    float LfWaitMapMax_X,LfWaitMapMax_Y;
    float LfWaitMapP1_P2_Dis;
    float LfTwoLineAngleSub;
    float LfWaitMapP1_P2_Y;
    float LfFindMapP1_P2_Y;
    float LfFindMapMin_X,LfFindMapMin_Y;
    float LfFindMapMax_X,LfFindMapMax_Y;
    eSideMapLineRelationType LeSideMapLineRelation = WAIT_MAP_NEW_MAP_NONE;
    
    LstrObjMap = &GstrObjMap;
    if(LstrObjMap->eSideMapFusionId != MAP_NONE)
    {
        LeWaitFusioMapId = LstrObjMap->eSideMapFusionId;
        LstrObjMap->eSideMapFusionId = MAP_NONE;   /* 仅在结束时融合一次 */
        
        if(LstrObjMap->ObjMapCoor[LeWaitFusioMapId].u8MapExistFlag)
        {
            LfWaitMapAngleAbs = ABS_VALUE(LstrObjMap->ObjMapCoor[LeWaitFusioMapId].fP1_P2ToCarX_Angle);
            if(LstrObjMap->ObjMapCoor[LeWaitFusioMapId].P1_CarCoor.fObjX < LstrObjMap->ObjMapCoor[LeWaitFusioMapId].P2_CarCoor.fObjX)
            {
                LfWaitMapMin_X = LstrObjMap->ObjMapCoor[LeWaitFusioMapId].P1_CarCoor.fObjX;
                LfWaitMapMin_Y = LstrObjMap->ObjMapCoor[LeWaitFusioMapId].P1_CarCoor.fObjY;
                LfWaitMapMax_X = LstrObjMap->ObjMapCoor[LeWaitFusioMapId].P2_CarCoor.fObjX;
                LfWaitMapMax_Y = LstrObjMap->ObjMapCoor[LeWaitFusioMapId].P2_CarCoor.fObjY;
            }
            else
            {
                LfWaitMapMin_X = LstrObjMap->ObjMapCoor[LeWaitFusioMapId].P2_CarCoor.fObjX;
                LfWaitMapMin_Y = LstrObjMap->ObjMapCoor[LeWaitFusioMapId].P2_CarCoor.fObjY;
                LfWaitMapMax_X = LstrObjMap->ObjMapCoor[LeWaitFusioMapId].P1_CarCoor.fObjX;
                LfWaitMapMax_Y = LstrObjMap->ObjMapCoor[LeWaitFusioMapId].P1_CarCoor.fObjY;
            }
            LfWaitMapP1_P2_Y = (LfWaitMapMin_Y+LfWaitMapMax_Y)/2;
            
            LfWaitMapP1_P2_Dis = LstrObjMap->ObjMapCoor[LeWaitFusioMapId].fP1_P2_Dis;
            if(LfWaitMapAngleAbs < F_R_MAP_65_ANGLE)
            {
                for(LeMapInx = MAP_OBJ_0; LeMapInx < MAP_OBJ_NUM; LeMapInx++)
                {
                    /* Map高低类型一致才融合，否则不融合 */
                    if((LeMapInx != LeWaitFusioMapId)&&(LstrObjMap->ObjMapCoor[LeMapInx].u8Map_SFR_AreaFlag == 0x1)&&\
                        (LstrObjMap->ObjMapCoor[LeMapInx].enuSideMapUpdateSts == SIDE_MAP_MEMORY_P1_MEMORY_P2))
                    {
                        LfTwoLineAngleSub = ABS(LstrObjMap->ObjMapCoor[LeWaitFusioMapId].fP1_P2ToCarX_Angle,LstrObjMap->ObjMapCoor[LeMapInx].fP1_P2ToCarX_Angle);
                        LfFindMapP1_P2_Y = LstrObjMap->ObjMapCoor[LeMapInx].P1_CarCoor.fObjY+LstrObjMap->ObjMapCoor[LeMapInx].P2_CarCoor.fObjY;
                        LfFindMapP1_P2_Y = LfFindMapP1_P2_Y/2;
                        LfFindMapP1_P2_Y = ABS(LfWaitMapP1_P2_Y,LfFindMapP1_P2_Y);

                        if((LfTwoLineAngleSub < F_R_MAP_10_ANGLE)&&(LfFindMapP1_P2_Y < SIDE_MAP_FUSION_Y_SUB))
                        {
                            if(LstrObjMap->ObjMapCoor[LeMapInx].P1_CarCoor.fObjX < LstrObjMap->ObjMapCoor[LeMapInx].P2_CarCoor.fObjX)
                            {
                                LfFindMapMin_X = LstrObjMap->ObjMapCoor[LeMapInx].P1_CarCoor.fObjX;
                                LfFindMapMin_Y = LstrObjMap->ObjMapCoor[LeMapInx].P1_CarCoor.fObjY;
                                LfFindMapMax_X = LstrObjMap->ObjMapCoor[LeMapInx].P2_CarCoor.fObjX;
                                LfFindMapMax_Y = LstrObjMap->ObjMapCoor[LeMapInx].P2_CarCoor.fObjY;
                            }
                            else
                            {
                                LfFindMapMin_X = LstrObjMap->ObjMapCoor[LeMapInx].P2_CarCoor.fObjX;
                                LfFindMapMin_Y = LstrObjMap->ObjMapCoor[LeMapInx].P2_CarCoor.fObjY;
                                LfFindMapMax_X = LstrObjMap->ObjMapCoor[LeMapInx].P1_CarCoor.fObjX;
                                LfFindMapMax_Y = LstrObjMap->ObjMapCoor[LeMapInx].P1_CarCoor.fObjY;
                            }

                            /* 以下分待融合Map包含与被包含进行融合 */
                            if(((LfWaitMapMin_X-50) < LfFindMapMin_X)&&((LfWaitMapMax_X+50) > LfFindMapMax_X))
                            {
                                LeSideMapLineRelation = NEW_MAP_P1_P2_WITHIN_WAIT_MAP;
                                LeFindFusionMapId = LeMapInx;
                                break;
                            }
                            else if(((LfFindMapMin_X-50) < LfWaitMapMin_X)&&((LfFindMapMax_X+50) > LfWaitMapMax_X))
                            {
                                LeSideMapLineRelation = WAIT_MAP_P1_P2_WITHIN_NEW_MAP;
                                LeFindFusionMapId = LeMapInx;
                                break;
                            }
                        }
                    }
                }
            }
        }

        if(LeFindFusionMapId != MAP_NONE)
        {
            /* 以下根据不同的类型进行融合处理 */
            if(LeSideMapLineRelation == NEW_MAP_P1_P2_WITHIN_WAIT_MAP)
            {
                /* 原Wait Map P1 P2点均保持不变--把被FusionMap删除 */

            }
            else if(LeSideMapLineRelation == WAIT_MAP_P1_P2_WITHIN_NEW_MAP)
            {
                LeFindFusionMapId = LeWaitFusioMapId;
            }

            DeleteMap(LeFindFusionMapId);
            LstrObjMap->u8MapNum--;
        }
    }
}

/******************************************************************************
 * 函数名称: ParkingGuidenceDataUpdate
 * 
 * 功能描述: 更新车辆泊车状态
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-08 11:21   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void ParkingGuidenceDataUpdate(void)
{
    ADAS_APAStatusType LenuADAS_APAStatus;
    LowVolPwrMdFlag2_Type LenuLowVolPwrMdFlag2;
    float LfAngleSub;
    
    ReadCAN_AppSignal_ADAS_APAStatus(&LenuADAS_APAStatus);
    ReadComSignal_LowVolPwrMdFlag2(&LenuLowVolPwrMdFlag2);
    
    GstrParkingGuidenceData.eCurrentAdasSts = LenuADAS_APAStatus;
    GstrParkingGuidenceData.eLowVolPwrMdFlag2 = LenuLowVolPwrMdFlag2;

    if(GstrParkingGuidenceData.eParking_Sts == PARKING_SUMMON)
    {
        GstrParkingGuidenceData.fSummonSideDis = 300;
    }
    else
    {
        GstrParkingGuidenceData.fSummonSideDis = 0;
    }


    if(GstrParkingGuidenceData.eLastAdasSts == APA_GUIDANCE)
    {
        GstrParkingGuidenceData.fCarAngleSub = ABS(GstrPDCSnsUseOdo.fCar_YawAngle,GstrParkingGuidenceData.fSearchToGuidenceCarAngle);
        if(GstrParkingGuidenceData.fCarAngleSub >= F_R_MAP_180_ANGLE)
        {
            GstrParkingGuidenceData.fCarAngleSub = F_R_MAP_360_ANGLE - GstrParkingGuidenceData.fCarAngleSub;
        }
        if(GstrParkingGuidenceData.eLowVolPwrMdFlag2 == LOWPOWER_MODFLAG2_ADAS)
        {
            if(!GstrParkingGuidenceData.u8ParkingStsLockFlag)
            {
                if(GstrParkingGuidenceData.fCarAngleSub > F_R_MAP_5_ANGLE)
                {
                    GstrParkingGuidenceData.eParking_Sts = PARKING_RPA;
                    GstrParkingGuidenceData.u8ParkingStsLockFlag = 1;
                }
                else
                {
                    GstrParkingGuidenceData.eParking_Sts = PARKING_SUMMON;
                }
            }
        }
        else
        {
            GstrParkingGuidenceData.eParking_Sts = PARKING_APA;
        }
        if((GstrParkingGuidenceData.eParking_Sts == PARKING_APA)||(GstrParkingGuidenceData.eParking_Sts == PARKING_RPA))
        {
            if(!GstrParkingGuidenceData.u8SlotTypeLockFlag)
            {
                if(GstrParkingGuidenceData.fCarAngleSub > F_R_MAP_40_ANGLE)
                {
                    GstrParkingGuidenceData.eSlotType = SLOT_VERTICAL;
                    //GstrParkingGuidenceData.eParkDir = PARK_DIR_NONE;
                    GstrParkingGuidenceData.fCarAngleSubBackup = 0;
                    GstrParkingGuidenceData.u8SlotTypeLockFlag = 1;    
                }
                else
                {
                    if(GstrSnsCarMovSts[0][0].eCarDir == SNS_CAR_BACKWARD)
                    {
                        if(GstrParkingGuidenceData.eParkDir == PARK_DIR_NONE)
                        {
                            if(GstrParkingGuidenceData.fCarAngleSub > F_R_MAP_10_ANGLE)
                            {
                                if(Gs16CarStrAngleForSnsUse > 200)
                                {
                                    GstrParkingGuidenceData.eParkDir = PARK_DIR_LEFT;
                                }
                                else if(Gs16CarStrAngleForSnsUse < -200)
                                {
                                    GstrParkingGuidenceData.eParkDir = PARK_DIR_RIGHT;
                                }
                            }
                        }
                        if((GstrParkingGuidenceData.fCarAngleSub > F_R_MAP_15_ANGLE)&&(GstrParkingGuidenceData.fCarAngleSub < F_R_MAP_40_ANGLE))
                        {
                            if(GstrParkingGuidenceData.fCarAngleSubBackup > (GstrParkingGuidenceData.fCarAngleSub+F_R_MAP_0_25_ANGLE))
                            {
                                GstrParkingGuidenceData.eSlotType = SLOT_PARALLEL;
                                GstrParkingGuidenceData.u8SlotTypeLockFlag = 1;    
                            }
                        }
                    }
                    else if(GstrSnsCarMovSts[0][0].eCarDir == SNS_CAR_FORWARD)
                    {
                        if(!GstrParkingGuidenceData.u8CarForwardVerticalSlotLockFlag)
                        {
                            if(GstrParkingGuidenceData.fCarAngleSub > F_R_MAP_20_ANGLE)
                            {
                                GstrParkingGuidenceData.eSlotType = SLOT_VERTICAL;
                                GstrParkingGuidenceData.fCarAngleSubBackup = 0;
                                GstrParkingGuidenceData.u8SlotTypeLockFlag = 1; 
                            }
                            if(GstrParkingGuidenceData.fCarAngleSub > F_R_MAP_10_ANGLE)
                            {
                                if(Gs16CarStrAngleForSnsUse > 200)
                                {
                                    GstrParkingGuidenceData.eParkDir = PARK_DIR_RIGHT;
                                }
                                else if(Gs16CarStrAngleForSnsUse < -200)
                                {
                                    GstrParkingGuidenceData.eParkDir = PARK_DIR_LEFT;
                                }
                            }
                        }
                    }
                }
                LfAngleSub = ABS(GstrParkingGuidenceData.fCarAngleSubBackup,GstrParkingGuidenceData.fCarAngleSub);
                if(LfAngleSub > F_R_MAP_0_5_ANGLE)
                {
                    GstrParkingGuidenceData.fCarAngleSubBackup = GstrParkingGuidenceData.fCarAngleSub;
                }
                if((GstrParkingGuidenceData.fCarAngleSub > F_R_MAP_8_ANGLE)&&(!GstrParkingGuidenceData.u8CarForwardVerticalSlotLockFlag))
                {
                    if(GstrParkingGuidenceData.fCarAngleSubBackup > (GstrParkingGuidenceData.fCarAngleSub+F_R_MAP_0_25_ANGLE))
                    {
                        GstrParkingGuidenceData.u8CarForwardVerticalSlotLockFlag = 1;    
                    }
                }
            }
        }
    }
    else
    {
        GstrParkingGuidenceData.fCarAngleSub = 0.0;
        GstrParkingGuidenceData.fSearchToGuidenceCarAngle = 0.0;
        GstrParkingGuidenceData.u8ParkingFinishFlag = 0;
        GstrParkingGuidenceData.eParking_Sts = PARKING_SEARCH;
        GstrParkingGuidenceData.u8ParkingStsLockFlag = 0;

        GstrParkingGuidenceData.eSlotType = SLOT_TYPE_NONE;
        GstrParkingGuidenceData.eParkDir = PARK_DIR_NONE;
        GstrParkingGuidenceData.fCarAngleSubBackup = 0;
        GstrParkingGuidenceData.u8SlotTypeLockFlag = 0;
        GstrParkingGuidenceData.u8CarForwardVerticalSlotLockFlag = 0;
    }

    if((GstrParkingGuidenceData.eCurrentAdasSts != APA_GUIDANCE)&&(GstrParkingGuidenceData.eLastAdasSts == APA_GUIDANCE))
    {
        GstrParkingGuidenceData.u8ParkingFinishFlag = 1;
        GstrParkingGuidenceData.u8DeleAllMapFlag = 1;
        GstrParkingGuidenceData.fParkingFinishDrvDis = GstrPDCSnsUseOdo.fDrvDis;
    }
    
    if((GstrParkingGuidenceData.eCurrentAdasSts == APA_GUIDANCE)&&(GstrParkingGuidenceData.eLastAdasSts != APA_GUIDANCE))
    {
        GstrParkingGuidenceData.fSearchToGuidenceCarAngle = GstrPDCSnsUseOdo.fCar_YawAngle;
    }
    /* 备份ADAS 状态信号 */
    GstrParkingGuidenceData.eLastAdasSts = GstrParkingGuidenceData.eCurrentAdasSts;
    if(GstrParkingGuidenceData.u8DeleAllMapFlag)
    {
        if(GstrPDCSnsUseOdo.fDrvDis > (GstrParkingGuidenceData.fParkingFinishDrvDis+100))
        {
            GstrParkingGuidenceData.u8DeleAllMapFlag = 0;
            MapBigWallBufInit(PDC_SNS_GROUP_FRONT);
            MapBigWallBufInit(PDC_SNS_GROUP_REAR);
            MapLineBufInit(PDC_SNS_GROUP_FRONT);
            MapLineBufInit(PDC_SNS_GROUP_REAR);
            ObjMapInit();
            ObjMapToCANInit();
        }
    }

    if((GstrParkingGuidenceData.eParking_Sts == PARKING_APA)||(GstrParkingGuidenceData.eParking_Sts == PARKING_RPA))
    {
        if(GstrParkingGuidenceData.eSlotType == SLOT_TYPE_NONE)
        {
            /* 未确认泊车状态前 */
            if(GstrSnsCarMovSts[0][0].eCarDir == SNS_CAR_FORWARD)
            {
                GstrParkingGuidenceData.fFrontSideStartMapDis = FRONT_MAP_GUIDANCE_FORWARD_DIS;
                GstrParkingGuidenceData.fRearSideStartMapDis = REAR_MAP_GUIDANCE_FORWARD_DIS;
            }
            else
            {
                GstrParkingGuidenceData.fFrontSideStartMapDis = FRONT_MAP_GUIDANCE_BACKWARD_DIS;

                /* 后保根据角度不同再做进一步细化区分 */
                if(GstrParkingGuidenceData.fCarAngleSub < F_R_MAP_10_ANGLE)
                {
                    GstrParkingGuidenceData.fRearSideStartMapDis = REAR_MAP_GUIDANCE_BACKWARD_01_DIS+200;
                }
                else if(GstrParkingGuidenceData.fCarAngleSub < F_R_MAP_15_ANGLE)
                {
                    GstrParkingGuidenceData.fRearSideStartMapDis = REAR_MAP_GUIDANCE_BACKWARD_01_DIS;
                }
                else if(GstrParkingGuidenceData.fCarAngleSub < F_R_MAP_25_ANGLE)
                {
                    GstrParkingGuidenceData.fRearSideStartMapDis = REAR_MAP_GUIDANCE_BACKWARD_01_DIS-100;
                }
                else if(GstrParkingGuidenceData.fCarAngleSub < F_R_MAP_30_ANGLE)
                {
                    GstrParkingGuidenceData.fRearSideStartMapDis = REAR_MAP_GUIDANCE_BACKWARD_01_DIS-200;
                }
                else
                {
                    GstrParkingGuidenceData.fRearSideStartMapDis = REAR_MAP_GUIDANCE_BACKWARD_01_DIS-400;
                }
            }
        }
        else if(GstrParkingGuidenceData.eSlotType == SLOT_PARALLEL)
        {
            /* 确定平行泊车状态 */
            if(GstrSnsCarMovSts[0][0].eCarDir == SNS_CAR_FORWARD)
            {
                GstrParkingGuidenceData.fFrontSideStartMapDis = FRONT_MAP_GUIDANCE_FORWARD_PARA_DIS;
                GstrParkingGuidenceData.fRearSideStartMapDis = REAR_MAP_GUIDANCE_FORWARD_DIS;
            }
            else
            {
                GstrParkingGuidenceData.fFrontSideStartMapDis = FRONT_MAP_GUIDANCE_BACKWARD_DIS;
                GstrParkingGuidenceData.fRearSideStartMapDis = REAR_MAP_GUIDANCE_BACKWARD_02_DIS;
            }
        }
        else
        {
            /* 确定垂直泊车状态 */
            if(GstrSnsCarMovSts[0][0].eCarDir == SNS_CAR_FORWARD)
            {
                GstrParkingGuidenceData.fFrontSideStartMapDis = FRONT_MAP_GUIDANCE_FORWARD_DIS;
                GstrParkingGuidenceData.fRearSideStartMapDis = REAR_MAP_GUIDANCE_FORWARD_DIS;
            }
            else
            {
                GstrParkingGuidenceData.fFrontSideStartMapDis = FRONT_MAP_GUIDANCE_BACKWARD_DIS;
                GstrParkingGuidenceData.fRearSideStartMapDis = REAR_MAP_GUIDANCE_BACKWARD_03_DIS;
            }
        }
    }
    else if(GstrParkingGuidenceData.eParking_Sts == PARKING_SUMMON)
    {
        GstrParkingGuidenceData.fFrontSideStartMapDis = FRONT_MAP_SEARCH_START_DIS;
        GstrParkingGuidenceData.fRearSideStartMapDis = REAR_MAP_SEARCH_START_DIS;
    }
    else
    {
        /* 非泊车、非召唤模式下的起始画Map距离 */
        if(Gu16CarSpdForSnsUse < 250)
        {
            GstrParkingGuidenceData.fFrontSideStartMapDis = FRONT_MAP_SEARCH_LOW_START_DIS;
        }
        else
        {
            GstrParkingGuidenceData.fFrontSideStartMapDis = FRONT_MAP_SEARCH_START_DIS;
        }
        GstrParkingGuidenceData.fRearSideStartMapDis = REAR_MAP_SEARCH_START_DIS;
    }
}

/******************************************************************************
 * 函数名称: MapPrecisionPrintInit
 * 
 * 功能描述: Map精度打印输出初始化
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-04-28 15:08   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void MapPrecisionPrintInit(void)
{
    #if MAP_PRECISION_PRINT_SWITCH
    GstrMapPrecisionPrint.P1_Point_X_Real = 7600;
    GstrMapPrecisionPrint.P2_Point_X_Real = 9440;
    GstrMapPrecisionPrint.P3_Point_X_Real = 12440;
    GstrMapPrecisionPrint.P4_Point_X_Real = 13150;
    
    GstrMapPrecisionPrint.P1_Point_X_Measure = 0;
    GstrMapPrecisionPrint.P2_Point_X_Measure = 0;
    GstrMapPrecisionPrint.P3_Point_X_Measure = 0;
    GstrMapPrecisionPrint.P4_Point_X_Measure = 0;

    GstrMapPrecisionPrint.P1_Bias = 0;
    GstrMapPrecisionPrint.P2_Bias = 0;
    GstrMapPrecisionPrint.P3_Bias = 0;
    GstrMapPrecisionPrint.P4_Bias = 0;
    
    GstrMapPrecisionPrint.Obj1_Min_X = 65535;
    GstrMapPrecisionPrint.Obj1_Max_X = 0;
    GstrMapPrecisionPrint.Obj2_Min_X = 65535;
    GstrMapPrecisionPrint.Obj2_Max_X = 0;
    #endif
}


/******************************************************************************
 * 函数名称: MapPrecisionPrintHandle
 * 
 * 功能描述: Map精度打印输出处理
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-04-28 15:15   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void MapPrecisionPrintHandle(void)
{
    #if MAP_PRECISION_PRINT_SWITCH
    ObjMapInfoType* LstrObjMap;
    eMapObjIdType LeMapId;

    LstrObjMap = &GstrObjMap;

    if(GstrPDCSnsUseOdo.fCar_X_Coor < 15400)
    {
        GstrMapPrecisionPrint.u8PrintfFlag = 0;
    }
    else
    {
        if(GstrMapPrecisionPrint.u8PrintfFlag == 1)
        {
            GstrMapPrecisionPrint.u8PrintfFlag = 2;
        }
        if(GstrMapPrecisionPrint.u8PrintfFlag == 0)
        {
            GstrMapPrecisionPrint.u8PrintfFlag = 1;
            MapPrecisionPrintInit();
        }
    }

    if(GstrMapPrecisionPrint.u8PrintfFlag == 1)
    {
        for(LeMapId = MAP_OBJ_0; LeMapId < MAP_OBJ_NUM; LeMapId++)
        {
            if(LstrObjMap->ObjMapCoor[LeMapId].u8MapDisplayFlag)
            {
                if((LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY > 0)&&(LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY > 0))
                {
                    if((LstrObjMap->ObjMapCoor[LeMapId].P1_OdoCoor.fObjX > (GstrMapPrecisionPrint.P1_Point_X_Real-1000))&&\
                        (LstrObjMap->ObjMapCoor[LeMapId].P2_OdoCoor.fObjX < (GstrMapPrecisionPrint.P2_Point_X_Real+1000)))
                    {
#if 0
                        VS_PRINT("First Turn Point,Id:%d,P1_Odo_X:%.3f,P1_Odo_Y:%.3f,P2_Odo_X:%.3f,P2_Odo_Y:%.3f\r\n",LstrObjMap->ObjMapCoor[LeMapId].eMapObjId,\
                            LstrObjMap->ObjMapCoor[LeMapId].P1_OdoCoor.fObjX,LstrObjMap->ObjMapCoor[LeMapId].P1_OdoCoor.fObjY,\
                            LstrObjMap->ObjMapCoor[LeMapId].P2_OdoCoor.fObjX,LstrObjMap->ObjMapCoor[LeMapId].P2_OdoCoor.fObjY);
#endif
                        if(GstrMapPrecisionPrint.Obj1_Min_X > LstrObjMap->ObjMapCoor[LeMapId].P1_OdoCoor.fObjX)
                        {
                            GstrMapPrecisionPrint.Obj1_Min_X = LstrObjMap->ObjMapCoor[LeMapId].P1_OdoCoor.fObjX;
                        }
                        if(GstrMapPrecisionPrint.Obj1_Min_X > LstrObjMap->ObjMapCoor[LeMapId].P2_OdoCoor.fObjX)
                        {
                            GstrMapPrecisionPrint.Obj1_Min_X = LstrObjMap->ObjMapCoor[LeMapId].P2_OdoCoor.fObjX;
                        }

                        if(GstrMapPrecisionPrint.Obj1_Max_X < LstrObjMap->ObjMapCoor[LeMapId].P1_OdoCoor.fObjX)
                        {
                            GstrMapPrecisionPrint.Obj1_Max_X = LstrObjMap->ObjMapCoor[LeMapId].P1_OdoCoor.fObjX;
                        }
                        if(GstrMapPrecisionPrint.Obj1_Max_X < LstrObjMap->ObjMapCoor[LeMapId].P2_OdoCoor.fObjX)
                        {
                            GstrMapPrecisionPrint.Obj1_Max_X = LstrObjMap->ObjMapCoor[LeMapId].P2_OdoCoor.fObjX;
                        }   
                    }

                    if((LstrObjMap->ObjMapCoor[LeMapId].P1_OdoCoor.fObjX > (GstrMapPrecisionPrint.P3_Point_X_Real-1000))&&\
                        (LstrObjMap->ObjMapCoor[LeMapId].P2_OdoCoor.fObjX < (GstrMapPrecisionPrint.P4_Point_X_Real+1000)))
                    {
#if 0
                        VS_PRINT("Second Turn Point,Id:%d,P1_Odo_X:%.3f,P1_Odo_Y:%.3f,P2_Odo_X:%.3f,P2_Odo_Y:%.3f\r\n",LstrObjMap->ObjMapCoor[LeMapId].eMapObjId,\
                            LstrObjMap->ObjMapCoor[LeMapId].P1_OdoCoor.fObjX,LstrObjMap->ObjMapCoor[LeMapId].P1_OdoCoor.fObjY,\
                            LstrObjMap->ObjMapCoor[LeMapId].P2_OdoCoor.fObjX,LstrObjMap->ObjMapCoor[LeMapId].P2_OdoCoor.fObjY);
#endif

                        if(GstrMapPrecisionPrint.Obj2_Min_X > LstrObjMap->ObjMapCoor[LeMapId].P1_OdoCoor.fObjX)
                        {
                            GstrMapPrecisionPrint.Obj2_Min_X = LstrObjMap->ObjMapCoor[LeMapId].P1_OdoCoor.fObjX;
                        }
                        if(GstrMapPrecisionPrint.Obj2_Min_X > LstrObjMap->ObjMapCoor[LeMapId].P2_OdoCoor.fObjX)
                        {
                            GstrMapPrecisionPrint.Obj2_Min_X = LstrObjMap->ObjMapCoor[LeMapId].P2_OdoCoor.fObjX;
                        }
                        
                        if(GstrMapPrecisionPrint.Obj2_Max_X < LstrObjMap->ObjMapCoor[LeMapId].P1_OdoCoor.fObjX)
                        {
                            GstrMapPrecisionPrint.Obj2_Max_X = LstrObjMap->ObjMapCoor[LeMapId].P1_OdoCoor.fObjX;
                        }
                        if(GstrMapPrecisionPrint.Obj2_Max_X < LstrObjMap->ObjMapCoor[LeMapId].P2_OdoCoor.fObjX)
                        {
                            GstrMapPrecisionPrint.Obj2_Max_X = LstrObjMap->ObjMapCoor[LeMapId].P2_OdoCoor.fObjX;
                        }      
                    }
                }
            }
        }

        /* 统计结果 */
        if(GstrMapPrecisionPrint.Obj1_Min_X < 65535)
        {
            GstrMapPrecisionPrint.P1_Point_X_Measure = GstrMapPrecisionPrint.Obj1_Min_X;
        }
        else
        {
            GstrMapPrecisionPrint.P1_Point_X_Measure = 0;
        }
        
        if(GstrMapPrecisionPrint.Obj1_Max_X > 0)
        {
            GstrMapPrecisionPrint.P2_Point_X_Measure = GstrMapPrecisionPrint.Obj1_Max_X;
        }
        else
        {
            GstrMapPrecisionPrint.P2_Point_X_Measure = 0;
        }

        if(GstrMapPrecisionPrint.Obj2_Min_X < 65535)
        {
            GstrMapPrecisionPrint.P3_Point_X_Measure = GstrMapPrecisionPrint.Obj2_Min_X;
        }
        else
        {
            GstrMapPrecisionPrint.P3_Point_X_Measure = 0;
        }
        
        if(GstrMapPrecisionPrint.Obj2_Max_X > 0)
        {
            GstrMapPrecisionPrint.P4_Point_X_Measure = GstrMapPrecisionPrint.Obj2_Max_X;
        }
        else
        {
            GstrMapPrecisionPrint.P4_Point_X_Measure = 0;
        }
        GstrMapPrecisionPrint.P1_Bias = GstrMapPrecisionPrint.P1_Point_X_Measure - GstrMapPrecisionPrint.P1_Point_X_Real;
        GstrMapPrecisionPrint.P2_Bias = GstrMapPrecisionPrint.P2_Point_X_Measure - GstrMapPrecisionPrint.P2_Point_X_Real;
        GstrMapPrecisionPrint.P3_Bias = GstrMapPrecisionPrint.P3_Point_X_Measure - GstrMapPrecisionPrint.P3_Point_X_Real;
        GstrMapPrecisionPrint.P4_Bias = GstrMapPrecisionPrint.P4_Point_X_Measure - GstrMapPrecisionPrint.P4_Point_X_Real;

#if 0
        VS_PRINT("P1_Real:%.2f,P1_Measure:%.2f,P1_Bias:%.2f\r\n",GstrMapPrecisionPrint.P1_Point_X_Real,\
            GstrMapPrecisionPrint.P1_Point_X_Measure,GstrMapPrecisionPrint.P1_Bias);
        
        VS_PRINT("P2_Real:%.2f,P2_Measure:%.2f,P2_Bias:%.2f\r\n",GstrMapPrecisionPrint.P2_Point_X_Real,\
            GstrMapPrecisionPrint.P2_Point_X_Measure,GstrMapPrecisionPrint.P2_Bias);
        
        VS_PRINT("P3_Real:%.2f,P3_Measure:%.2f,P3_Bias:%.2f\r\n",GstrMapPrecisionPrint.P3_Point_X_Real,\
            GstrMapPrecisionPrint.P3_Point_X_Measure,GstrMapPrecisionPrint.P3_Bias);
        
        VS_PRINT("P4_Real:%.2f,P4_Measure:%.2f,P4_Bias:%.2f\r\n",GstrMapPrecisionPrint.P4_Point_X_Real,\
            GstrMapPrecisionPrint.P4_Point_X_Measure,GstrMapPrecisionPrint.P4_Bias);
#endif
    }
    #endif
}


/******************************************************************************
 * 函数名称: ObjMapBuild
 * 
 * 功能描述: Map构建调度，仅用于AK2仿真使用
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-08-28 20:10   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void ObjMapBuild(PDCSnsGroupType LenuGroup)
{
#if 1
    /* 第一步；更新现存Map的车辆坐标 */
    TransMapOdoCoorToCarCoor();

#if VHE_POINT_CLOUD_SIMULATE
    VheMapFusion();
#endif

    ObjMapBuildAndUpdate(LenuGroup);
    UpdateMapInforAndDele();
    /* 侧边融合暂时屏蔽 */
#if 1
    SideMapFusionHandle();
#endif
    ObjMapToCAN(); 
    ParkingGuidenceDataUpdate();
#endif

    MapPrecisionPrintHandle();
}




