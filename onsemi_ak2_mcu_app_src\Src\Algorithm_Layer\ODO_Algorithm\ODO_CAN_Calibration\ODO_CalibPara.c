
/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * PDC_MEB_CalibPara: 
 * Created on: 2021-08-16 19:24
 * Original designer: <PERSON><PERSON><PERSON>
 ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "ODO_CalibPara_Types.h"
#include "CAN_AppSignalManage.h"
//#include "Did_User.h"


/*************************************X02 20寸车型轮速脉冲************************************************/
#define ONE_WHEEL_PULSE_DISTANCE_AT_RL_FORWARD_X02_20INCH                      24.5368106//24.5992366
#define ONE_WHEEL_PULSE_DISTANCE_AT_RR_FORWARD_X02_20INCH                      24.526816//24.6160142
#define ONE_WHEEL_PULSE_DISTANCE_AT_RL_BACKWARD_X02_20INCH                      24.5368106
#define ONE_WHEEL_PULSE_DISTANCE_AT_RR_BACKWARD_X02_20INCH                      24.526816

/* 参数配置常量 */
APA_Odom_CarParameterType GstrAPA_OdomCarPara_X02_20INCH =
{
    .fOneWheelPulseDisAtRRForward = ONE_WHEEL_PULSE_DISTANCE_AT_RR_FORWARD_X02_20INCH,
    .fOneWheelPulseDisAtRLForward = ONE_WHEEL_PULSE_DISTANCE_AT_RL_FORWARD_X02_20INCH,
    .fOneWheelPulseDisAtRRBackward = ONE_WHEEL_PULSE_DISTANCE_AT_RR_BACKWARD_X02_20INCH,
    .fOneWheelPulseDisAtRLBackward = ONE_WHEEL_PULSE_DISTANCE_AT_RL_BACKWARD_X02_20INCH,
    .fFront_left_R = 1.3,
    .fFront_right_R = 0.01,
    .fRear_left_R = 1,
    .fRear_right_R = 1,
	.fYawVar = 1,
	.fYawRateVar = 1.1,
	.fYaw_YawrateCov = 0.01,
	.fRear_wheel_axle_length = 1.75,
};


/*************************************X02 21寸车型轮速脉冲************************************************/
#define ONE_WHEEL_PULSE_DISTANCE_AT_RL_FORWARD_X02_21INCH                      24.255171
#define ONE_WHEEL_PULSE_DISTANCE_AT_RR_FORWARD_X02_21INCH                      24.3471751
#define ONE_WHEEL_PULSE_DISTANCE_AT_RL_BACKWARD_X02_21INCH                      24.255171
#define ONE_WHEEL_PULSE_DISTANCE_AT_RR_BACKWARD_X02_21INCH                      24.3471751

/* 参数配置常量 */
APA_Odom_CarParameterType GstrAPA_OdomCarPara_X02_21INCH =
{
    .fOneWheelPulseDisAtRRForward = ONE_WHEEL_PULSE_DISTANCE_AT_RR_FORWARD_X02_21INCH,
    .fOneWheelPulseDisAtRLForward = ONE_WHEEL_PULSE_DISTANCE_AT_RL_FORWARD_X02_21INCH,
    .fOneWheelPulseDisAtRRBackward = ONE_WHEEL_PULSE_DISTANCE_AT_RR_BACKWARD_X02_21INCH,
    .fOneWheelPulseDisAtRLBackward = ONE_WHEEL_PULSE_DISTANCE_AT_RL_BACKWARD_X02_21INCH,
    .fFront_left_R = 0.5,
    .fFront_right_R = 0.1,
    .fRear_left_R = 1,
    .fRear_right_R = 1,
	.fYawVar = 1,
	.fYawRateVar = 1.1,
	.fYaw_YawrateCov = 0.01,
	.fRear_wheel_axle_length = 1.70,
};


/*************************************X03 20寸车型轮速脉冲************************************************/
#define ONE_WHEEL_PULSE_DISTANCE_AT_RL_FORWARD_X03_20INCH                      24.5985857
#define ONE_WHEEL_PULSE_DISTANCE_AT_RR_FORWARD_X03_20INCH                      24.5883576
#define ONE_WHEEL_PULSE_DISTANCE_AT_RL_BACKWARD_X03_20INCH                      24.5985857
#define ONE_WHEEL_PULSE_DISTANCE_AT_RR_BACKWARD_X03_20INCH                      24.5883576

/* 参数配置常量 */
APA_Odom_CarParameterType GstrAPA_OdomCarPara_X03_20INCH =
{
    .fOneWheelPulseDisAtRRForward = ONE_WHEEL_PULSE_DISTANCE_AT_RR_FORWARD_X03_20INCH,
    .fOneWheelPulseDisAtRLForward = ONE_WHEEL_PULSE_DISTANCE_AT_RL_FORWARD_X03_20INCH,
    .fOneWheelPulseDisAtRRBackward = ONE_WHEEL_PULSE_DISTANCE_AT_RR_BACKWARD_X03_20INCH,
    .fOneWheelPulseDisAtRLBackward = ONE_WHEEL_PULSE_DISTANCE_AT_RL_BACKWARD_X03_20INCH,    
    .fFront_left_R = 0.1,
    .fFront_right_R = 1.3,
    .fRear_left_R = 1,
    .fRear_right_R = 1,
	.fYawVar = 1,
	.fYawRateVar = 1.1,
	.fYaw_YawrateCov = 0.01,
	.fRear_wheel_axle_length = 1.75,
};


/*************************************X04 20寸车型轮速脉冲************************************************/
#define ONE_WHEEL_PULSE_DISTANCE_AT_RL_FORWARD_X04_20INCH                      24.5985857
#define ONE_WHEEL_PULSE_DISTANCE_AT_RR_FORWARD_X04_20INCH                      24.5883576
#define ONE_WHEEL_PULSE_DISTANCE_AT_RL_BACKWARD_X04_20INCH                      24.5985857
#define ONE_WHEEL_PULSE_DISTANCE_AT_RR_BACKWARD_X04_20INCH                      24.5883576

/* 参数配置常量 */
APA_Odom_CarParameterType GstrAPA_OdomCarPara_X04_20INCH =
{
    .fOneWheelPulseDisAtRRForward = ONE_WHEEL_PULSE_DISTANCE_AT_RR_FORWARD_X04_20INCH,
    .fOneWheelPulseDisAtRLForward = ONE_WHEEL_PULSE_DISTANCE_AT_RL_FORWARD_X04_20INCH,
    .fOneWheelPulseDisAtRRBackward = ONE_WHEEL_PULSE_DISTANCE_AT_RR_BACKWARD_X04_20INCH,
    .fOneWheelPulseDisAtRLBackward = ONE_WHEEL_PULSE_DISTANCE_AT_RL_BACKWARD_X04_20INCH,    
    .fFront_left_R = 0.5,
    .fFront_right_R = 0.1,
    .fRear_left_R = 1,
    .fRear_right_R = 1,
	.fYawVar = 1,
	.fYawRateVar = 1.1,
	.fYaw_YawrateCov = 0.01,
	.fRear_wheel_axle_length = 1.70,
};


/*************************************X04 21寸车型轮速脉冲************************************************/
#define ONE_WHEEL_PULSE_DISTANCE_AT_RL_FORWARD_X04_21INCH                      24.8747749
#define ONE_WHEEL_PULSE_DISTANCE_AT_RR_FORWARD_X04_21INCH                      24.8869964
#define ONE_WHEEL_PULSE_DISTANCE_AT_RL_BACKWARD_X04_21INCH                      24.8533508
#define ONE_WHEEL_PULSE_DISTANCE_AT_RR_BACKWARD_X04_21INCH                      24.8818898

/* 参数配置常量 */
APA_Odom_CarParameterType GstrAPA_OdomCarPara_X04_21INCH =
{
    .fOneWheelPulseDisAtRRForward = ONE_WHEEL_PULSE_DISTANCE_AT_RR_FORWARD_X04_21INCH,
    .fOneWheelPulseDisAtRLForward = ONE_WHEEL_PULSE_DISTANCE_AT_RL_FORWARD_X04_21INCH,
    .fOneWheelPulseDisAtRRBackward = ONE_WHEEL_PULSE_DISTANCE_AT_RR_BACKWARD_X04_21INCH,
    .fOneWheelPulseDisAtRLBackward = ONE_WHEEL_PULSE_DISTANCE_AT_RL_BACKWARD_X04_21INCH,
    .fFront_left_R = 0.5,
    .fFront_right_R = 0.1,
    .fRear_left_R = 1,
    .fRear_right_R = 1,
	.fYawVar = 1,
	.fYawRateVar = 1.1,
	.fYaw_YawrateCov = 0.01,
	.fRear_wheel_axle_length = 1.70,
};

/*************************************W02 20寸车型轮速脉冲************************************************/
#define ONE_WHEEL_PULSE_DISTANCE_AT_RL_FORWARD_W02_20INCH                      24.3897478
#define ONE_WHEEL_PULSE_DISTANCE_AT_RR_FORWARD_W02_20INCH                      24.3897478
#define ONE_WHEEL_PULSE_DISTANCE_AT_RL_BACKWARD_W02_20INCH                      24.3267239
#define ONE_WHEEL_PULSE_DISTANCE_AT_RR_BACKWARD_W02_20INCH                      24.3159296

/* 参数配置常量 */
APA_Odom_CarParameterType GstrAPA_OdomCarPara_W02_20INCH =
{
    .fOneWheelPulseDisAtRRForward = ONE_WHEEL_PULSE_DISTANCE_AT_RR_FORWARD_W02_20INCH,
    .fOneWheelPulseDisAtRLForward = ONE_WHEEL_PULSE_DISTANCE_AT_RL_FORWARD_W02_20INCH,
    .fOneWheelPulseDisAtRRBackward = ONE_WHEEL_PULSE_DISTANCE_AT_RR_BACKWARD_W02_20INCH,
    .fOneWheelPulseDisAtRLBackward = ONE_WHEEL_PULSE_DISTANCE_AT_RL_BACKWARD_W02_20INCH,  
    .fFront_left_R = 0.3,
    .fFront_right_R = 0.55,
    .fRear_left_R = 1,
    .fRear_right_R = 1,
	.fYawVar = 1,
	.fYawRateVar = 1.1,
	.fYaw_YawrateCov = 0.01,
	.fRear_wheel_axle_length = 1.75,
};

/*******************APA标定参数_存储在Ram中--*********************************/
APA_Odom_CarParameterType GstrAPA_OdomCarPara_Ram;

void ODO_Car_CalibPara_Config_Init(void)
{
    VehicleTypeType  LenuVEH_Type;
    VehicleWheelType  LenuVehicleWheel;

    ReadCAN_AppSignal_VEH_Type(&LenuVEH_Type);
    ReadCAN_AppSignal_VehicleWheel(&LenuVehicleWheel);

    if (LenuVEH_Type == VEHTYPE_X02 && (LenuVehicleWheel == VEH_WHEEL_20INCH))
    {
        GstrAPA_OdomCarPara_Ram = GstrAPA_OdomCarPara_X02_20INCH;
    }
    else if (LenuVEH_Type == VEHTYPE_X02 && (LenuVehicleWheel == VEH_WHEEL_21INCH))
    {
        GstrAPA_OdomCarPara_Ram = GstrAPA_OdomCarPara_X02_21INCH;
    }
    else if (LenuVEH_Type == VEHTYPE_X03 && (LenuVehicleWheel == VEH_WHEEL_20INCH))
    {
        GstrAPA_OdomCarPara_Ram = GstrAPA_OdomCarPara_X03_20INCH;
    }
    else if (LenuVEH_Type == VEHTYPE_X04 && (LenuVehicleWheel == VEH_WHEEL_20INCH))
    {
        GstrAPA_OdomCarPara_Ram = GstrAPA_OdomCarPara_X04_20INCH;
    }
    else if (LenuVEH_Type == VEHTYPE_X04 && (LenuVehicleWheel == VEH_WHEEL_21INCH))
    {
        GstrAPA_OdomCarPara_Ram = GstrAPA_OdomCarPara_X04_21INCH;
    }
    else if (LenuVEH_Type == VEHTYPE_W02 && (LenuVehicleWheel == VEH_WHEEL_20INCH))
    {
        GstrAPA_OdomCarPara_Ram = GstrAPA_OdomCarPara_W02_20INCH;
    }
    else
    {
        GstrAPA_OdomCarPara_Ram = GstrAPA_OdomCarPara_X03_20INCH;
    }
}
