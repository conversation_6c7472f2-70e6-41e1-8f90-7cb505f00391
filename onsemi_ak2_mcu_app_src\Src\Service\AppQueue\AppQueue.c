#include "AppQueue.h"


boolean AppQueue_Enqueue(QueueIdxType Idx, uint8* BufData, uint16 DataLen)
{
    boolean Rst = FALSE;
    uint16 LCnt, LTemp = 0u;
    QueueInfoType * LpQueueInfo;
    LpQueueInfo = (QueueInfoType *)GET_QUEUE_INFO_PTR(Idx);
    
    if(LpQueueInfo->HeadIdx < LpQueueInfo->TailIdx)
    {
        if((LpQueueInfo->HeadIdx + DataLen) < LpQueueInfo->TailIdx)
        {
            for(LCnt = 0u; LCnt < DataLen; LCnt++, LpQueueInfo->HeadIdx++)
            {
                LpQueueInfo->pDataBuf[LpQueueInfo->HeadIdx] = BufData[LCnt];
            }
            LpQueueInfo->QueueSize += DataLen;
            Rst = TRUE;
        }
    }
    else
    {
        if((LpQueueInfo->HeadIdx + DataLen) >= LpQueueInfo->BufSize)
        {
            if((LpQueueInfo->HeadIdx + DataLen) < (LpQueueInfo->TailIdx + LpQueueInfo->BufSize))
            {
                LTemp = LpQueueInfo->BufSize - LpQueueInfo->HeadIdx;
                for(LCnt = 0u; LCnt < LTemp; LCnt++)
                {
                    LpQueueInfo->pDataBuf[LpQueueInfo->HeadIdx + LCnt] = BufData[LCnt];
                }
                LpQueueInfo->HeadIdx = 0u;
                for(LCnt = LTemp; LCnt < DataLen; LCnt++, LpQueueInfo->HeadIdx++)
                {
                    LpQueueInfo->pDataBuf[LpQueueInfo->HeadIdx] = BufData[LCnt];
                }
                LpQueueInfo->QueueSize += DataLen;
                Rst = TRUE;
            }
        }
        else
        {
            for(LCnt = 0u; LCnt < DataLen; LCnt++, LpQueueInfo->HeadIdx++)
            {
                LpQueueInfo->pDataBuf[LpQueueInfo->HeadIdx] = BufData[LCnt];
            }
            LpQueueInfo->QueueSize += DataLen;
            Rst = TRUE;
        }
    }
    return Rst;
}

boolean AppQueue_Dequeue(QueueIdxType Idx, uint8* BufData, uint16 DataLen)
{
    boolean Rst = FALSE;
    uint16 LCnt, LTemp = 0u;
    QueueInfoType * LpQueueInfo;
    LpQueueInfo = (QueueInfoType *)GET_QUEUE_INFO_PTR(Idx);

    if(LpQueueInfo->TailIdx > LpQueueInfo->HeadIdx)
    {
        if((LpQueueInfo->TailIdx + DataLen) < LpQueueInfo->BufSize)
        {
            for(LCnt = 0u; LCnt < DataLen; LCnt++, LpQueueInfo->TailIdx++)
            {
                BufData[LCnt] = LpQueueInfo->pDataBuf[LpQueueInfo->TailIdx];
            }
            if(LpQueueInfo->QueueSize >= DataLen)
                LpQueueInfo->QueueSize -= DataLen;
            else
                LpQueueInfo->QueueSize = 0u;
        }
        else
        {
            LTemp = LpQueueInfo->BufSize - LpQueueInfo->TailIdx;
            for(LCnt = 0u; LCnt < LTemp; LCnt++)
            {
                BufData[LCnt] = LpQueueInfo->pDataBuf[LpQueueInfo->TailIdx + LCnt];
            }
            LpQueueInfo->TailIdx = 0u;
            for(LCnt = LTemp; LCnt < DataLen; LCnt++, LpQueueInfo->TailIdx++)
            {
                BufData[LCnt] = LpQueueInfo->pDataBuf[LpQueueInfo->TailIdx];
            }
            if(LpQueueInfo->QueueSize >= DataLen)
                LpQueueInfo->QueueSize -= DataLen;
            else
                LpQueueInfo->QueueSize = 0u;
        }
        Rst = TRUE;
    }
    else if(LpQueueInfo->TailIdx < LpQueueInfo->HeadIdx)
    {
        for(LCnt = 0u; LCnt < DataLen; LCnt++, LpQueueInfo->TailIdx++)
        {
            BufData[LCnt] = LpQueueInfo->pDataBuf[LpQueueInfo->TailIdx];
        }
        if(LpQueueInfo->QueueSize >= DataLen)
            LpQueueInfo->QueueSize -= DataLen;
        else
            LpQueueInfo->QueueSize = 0u;
        Rst = TRUE;
    }
    return Rst;
}

uint16 AppQueue_GetQueueSize(QueueIdxType Idx)
{
    QueueInfoType * LpQueueInfo;
    LpQueueInfo = (QueueInfoType *)GET_QUEUE_INFO_PTR(Idx);
    return LpQueueInfo->QueueSize;
}