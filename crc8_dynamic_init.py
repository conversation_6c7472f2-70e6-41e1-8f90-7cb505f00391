import sys

def generate_crc8_table(polynomial):
    """生成CRC8查找表"""
    table = []
    for i in range(256):
        crc = i
        for j in range(8):
            if crc & 0x80:
                crc = (crc << 1) ^ polynomial
            else:
                crc = crc << 1
            crc &= 0xFF
        table.append(crc)
    return table

def calculate_crc8(data, table, init_value):
    """使用查表法计算CRC8"""
    crc = init_value
    for byte in data:
        crc = table[crc ^ byte]
    return crc

# 已知多项式
polynomial = 0x2F  # x8+x5+x3+x2+x+1

# 生成CRC8表
crc8_table = generate_crc8_table(polynomial)

# 解析示例数据
def parse_hex_string(hex_string):
    # 移除所有空白字符
    hex_string = hex_string.strip()
    
    # 将字符串转换为字节列表
    return [int(hex_string[i:i+2], 16) for i in range(0, len(hex_string), 2)]

# 示例数据
examples = [
    'FF00EFFE',
    'FFC30D00',
    '02120000'
]

print('分析CRC8计算方法...')
print(f'多项式: 0x{polynomial:02X} (x8+x5+x3+x2+x+1)')
print()

# 分析每个示例
for example in examples:
    data_with_crc = parse_hex_string(example)
    data = data_with_crc[:-1]
    expected_crc = data_with_crc[-1]
    
    print(f'示例: {example}')
    print(f'数据: {[f"0x{b:02X}" for b in data]}')
    print(f'CRC8: 0x{expected_crc:02X}')
    
    # 尝试所有可能的初始值
    matching_init_values = []
    for init_value in range(256):
        calculated_crc = calculate_crc8(data, crc8_table, init_value)
        if calculated_crc == expected_crc:
            matching_init_values.append(init_value)
    
    if matching_init_values:
        print(f'找到 {len(matching_init_values)} 个匹配的初始值:')
        for init_value in matching_init_values[:5]:  # 只显示前5个
            print(f'0x{init_value:02X}')
        if len(matching_init_values) > 5:
            print(f'... 还有 {len(matching_init_values) - 5} 个')
    else:
        print('未找到匹配的初始值')
    print()

# 分析是否存在动态初始值模式
print('分析是否存在动态初始值模式...')

# 尝试使用前一个CRC作为下一个计算的初始值
print('尝试使用前一个CRC作为下一个计算的初始值:')
init_value = 0x00  # 假设第一个计算使用0x00作为初始值

for i, example in enumerate(examples):
    data_with_crc = parse_hex_string(example)
    data = data_with_crc[:-1]
    expected_crc = data_with_crc[-1]
    
    calculated_crc = calculate_crc8(data, crc8_table, init_value)
    print(f'示例 {i+1}: {example}')
    print(f'初始值: 0x{init_value:02X}')
    print(f'计算CRC8: 0x{calculated_crc:02X}')
    print(f'预期CRC8: 0x{expected_crc:02X}')
    print(f'验证结果: {"成功" if calculated_crc == expected_crc else "失败"}')
    print()
    
    # 使用当前CRC作为下一个计算的初始值
    init_value = expected_crc

# 尝试使用固定初始值0xFF
print('尝试使用固定初始值0xFF:')
init_value = 0xFF

for i, example in enumerate(examples):
    data_with_crc = parse_hex_string(example)
    data = data_with_crc[:-1]
    expected_crc = data_with_crc[-1]
    
    calculated_crc = calculate_crc8(data, crc8_table, init_value)
    print(f'示例 {i+1}: {example}')
    print(f'初始值: 0x{init_value:02X}')
    print(f'计算CRC8: 0x{calculated_crc:02X}')
    print(f'预期CRC8: 0x{expected_crc:02X}')
    print(f'验证结果: {"成功" if calculated_crc == expected_crc else "失败"}')
    print()

# 尝试使用数据的第一个字节作为初始值
print('尝试使用数据的第一个字节作为初始值:')

for i, example in enumerate(examples):
    data_with_crc = parse_hex_string(example)
    data = data_with_crc[:-1]
    expected_crc = data_with_crc[-1]
    
    init_value = data[0]
    calculated_crc = calculate_crc8(data[1:], crc8_table, init_value)
    print(f'示例 {i+1}: {example}')
    print(f'初始值 (第一个字节): 0x{init_value:02X}')
    print(f'计算CRC8: 0x{calculated_crc:02X}')
    print(f'预期CRC8: 0x{expected_crc:02X}')
    print(f'验证结果: {"成功" if calculated_crc == expected_crc else "失败"}')
    print()
