/**********************************************************************
*	版权所有	: 2023 深圳市豪恩汽车电子装备股份有限公司
*	项目名称	: 
*	M  C  U     : R7F701581
*	编译环境	: IAR 2.10
*	文件名称	: 
*	其它说明	: 
*	当前版本	: V0.1
*	作    者	: 22573
*	完成日期	: 	
*	内容摘要	: 
*	修改记录	:
*	修改日期	:
*	版 本 号	:
***********************************************************************/
#ifndef FCLDRIVE_H
#define FCLDRIVE_H
#include "types.h"


/* F1L WS2.0 needs Code Flash authentication prior to switch mode */
#define R_FCL_DEVICE_SPECIFIC_INIT                                  \
                *(volatile uint32 *)0xFFA08000 = 0xFFFFFFFF;          \
                *(volatile uint32 *)0xFFA08004 = 0xFFFFFFFF;          \
                *(volatile uint32 *)0xFFA08008 = 0xFFFFFFFF;          \
                *(volatile uint32 *)0xFFA0800C = 0xFFFFFFFF;

extern void Fls_Init(void);

extern ErrorType Fls_Erase(uint32 LcStartBlockNum,uint16 LcBlockNum);

extern ErrorType Fls_Write(uint32 LdwAddress,uint8 *pLcDataPointer,uint16 LcDataSize);


#endif





