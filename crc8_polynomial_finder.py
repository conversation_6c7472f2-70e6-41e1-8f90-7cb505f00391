import sys

def calculate_crc8(data, polynomial, init_value):
    crc = init_value
    
    # 初始化CRC表
    crc_table = []
    for i in range(256):
        crc_value = i
        for j in range(8):
            if crc_value & 0x80:
                crc_value = (crc_value << 1) ^ polynomial
            else:
                crc_value <<= 1
            crc_value &= 0xFF  # 确保结果是8位
        crc_table.append(crc_value)
    
    # 计算CRC
    for byte in data:
        crc = crc_table[crc ^ byte]
    
    return crc

def test_polynomial(data, expected_crc, init_value):
    # 尝试所有可能的多项式值
    for polynomial in range(1, 256):  # 从1开始，因为0不是有效的多项式
        crc = calculate_crc8(data, polynomial, init_value)
        if crc == expected_crc:
            return polynomial
    
    return None

# 已知数据、CRC8结果和初始值
data = [0xF8, 0x09, 0x30]
expected_crc = 0x1F
init_value = 0xFF

print('尝试推导CRC8多项式值...')
print(f'数据: {[f"0x{b:02X}" for b in data]}')
print(f'预期CRC8: 0x{expected_crc:02X}')
print(f'初始值: 0x{init_value:02X}')
print()

# 推导多项式值
polynomial = test_polynomial(data, expected_crc, init_value)

if polynomial is not None:
    print(f'找到匹配的多项式值: 0x{polynomial:02X}')
    
    # 将多项式转换为二进制表示
    binary = bin(polynomial)[2:].zfill(8)
    print(f'二进制表示: {binary}')
    
    # 构建多项式表达式
    terms = []
    for i, bit in enumerate(binary):
        if bit == '1':
            power = 7 - i
            if power == 0:
                terms.append('1')
            elif power == 1:
                terms.append('x')
            else:
                terms.append(f'x^{power}')
    
    polynomial_expr = ' + '.join(terms)
    print(f'多项式表达式: {polynomial_expr}')
    
    # 验证结果
    verification_crc = calculate_crc8(data, polynomial, init_value)
    print(f'验证CRC8: 0x{verification_crc:02X} ({"成功" if verification_crc == expected_crc else "失败"})')
else:
    print('未找到匹配的多项式值')

# 如果找到了多项式，测试另一个示例
if polynomial is not None:
    print('\n测试另一个示例:')
    test_data = [0xF8, 0x08, 0x0A]
    test_crc = calculate_crc8(test_data, polynomial, init_value)
    print(f'数据: {[f"0x{b:02X}" for b in test_data]}')
    print(f'计算CRC8: 0x{test_crc:02X}')
