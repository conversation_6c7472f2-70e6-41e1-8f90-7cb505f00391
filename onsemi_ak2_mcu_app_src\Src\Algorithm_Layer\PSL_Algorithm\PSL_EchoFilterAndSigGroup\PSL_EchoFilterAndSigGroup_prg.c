/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * SnsEchoFilterAndSigGroup_prg: 濮娉㈤艰婊ゅ淇″风澶?
 * Created on: 2024-02-23 14:29
 * Original designer: 22866
 ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "SnsRawData_cfg.h"
//#include "TimerManage.h"
#include "SnsRawData_Int.h"
#include "PSL_RawDataCalib.h"
#include "CAN_AppSignalManage.h"
#include "PSL_EchoFilterAndSigGroup_int.h"


/******************************************************************************/
/******************************************************************************/
/****************************** Private Definition ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Public Definition *****************************/
/******************************************************************************/
/******************************************************************************/
PSLSnsSigGroupDataCacheType GstrPSLSnsSigGroupDataCache[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];



/******************************************************************************/
/******************************************************************************/
/****************************** Private Function ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/******************************* Public Function ******************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************************
 * 芥板绉: PSLSnsSigGroupDataClear
 * 
 * 芥杩: 淇″风版娓?
 * 
 * 杈ュ:LeGroup--㈠ご缁凤LePDCSnsCh--㈠ご?
 * 
 * 杈哄:?
 * 
 * 杩??
 * 
 * 跺璇存:?
 * 
 * 淇规ユ              ?       淇逛?         淇瑰瀹  
 * 2023-07-11 13:49   V0.1      AntonyFang   娆″甯
 ******************************************************************************/
static void PSLSnsSigGroupDataClear(PDCSnsGroupType LeGroup,PDCSnsChannelType LePDCSnsCh,Sns_CycleType LenuCurIndex)
{
    uint8 i;
    PSLSnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    
    LpstrSnsSigGroupDataCache = &GstrPSLSnsSigGroupDataCache[LeGroup][LePDCSnsCh];

    LpstrSnsSigGroupDataCache->u8SigGroupUpdateFlag = 0;
    LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub = 0;
    LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].u32SysTime = 0;
    LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType = PDC_SNS_MEAS_IDLE;

    LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8SigGroupCnt = 0;
    LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8SigGroupCnt = 0;
    LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8SigGroupCnt = 0;

    for(i = 0; i < MAX_NUM_OF_PSL_SIG_GROUP; i++)
    {
        LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8GroupEchoCnt[i] = 0;
        LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[i] = SNS_INVALID_DIS;
        LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[i] = SNS_INVALID_HEIGHT;
        LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[i] = SNS_INVALID_DIS;
        LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[i] = SNS_INVALID_HEIGHT;
        LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[i] = SNS_INVALID_DIS;
        LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[i] = SNS_INVALID_HEIGHT;
        LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[i] = OBJ_NONE_TYPE;
        
        LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8GroupEchoCnt[i] = 0;
        LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[i] = SNS_INVALID_DIS;
        LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[i] = SNS_INVALID_HEIGHT;
        LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[i] = SNS_INVALID_DIS;
        LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[i] = SNS_INVALID_HEIGHT;
        LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[i] = SNS_INVALID_DIS;
        LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[i] = SNS_INVALID_HEIGHT;
        LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[i] = OBJ_NONE_TYPE;

        LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8GroupEchoCnt[i] = 0;
        LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[i] = SNS_INVALID_DIS;
        LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[i] = SNS_INVALID_HEIGHT;
        LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[i] = SNS_INVALID_DIS;
        LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[i] = SNS_INVALID_HEIGHT;
        LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[i] = SNS_INVALID_DIS;
        LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[i] = SNS_INVALID_HEIGHT;
        LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[i] = OBJ_NONE_TYPE;
    }
}


/******************************************************************************
 * 芥板绉: PSLSnsSigGroupDataCacheInit
 * 
 * 芥杩: ㈠ご淇″风瀛版濮?
 * 
 * 杈ュ:LeGroup--㈠ご缁
 * 
 * 杈哄:?
 * 
 * 杩??
 * 
 * 跺璇存:?
 * 
 * 淇规ユ              ?       淇逛?         淇瑰瀹  
 * 2023-07-11 11:28   V0.1      AntonyFang   娆″甯
 ******************************************************************************/
void PSLSnsSigGroupDataCacheInit(PDCSnsGroupType LeGroup)
{
    PDCSnsChannelType LePDCSnsCh;
    for(LePDCSnsCh = PDC_SNS_CH_FLS_RLS;LePDCSnsCh < PDC_SNS_CH_NUM;LePDCSnsCh++)
    {
        GstrPSLSnsSigGroupDataCache[LeGroup][LePDCSnsCh].enuCurIndex = SNS_CYCLE_2;      /* 濮Index璁剧疆澶у硷缁存版剁存ュSNS_CYCLE_0 */
        GstrPSLSnsSigGroupDataCache[LeGroup][LePDCSnsCh].u8RecordCnt = 0;
        GstrPSLSnsSigGroupDataCache[LeGroup][LePDCSnsCh].u8SigGroupUpdateFlag = INVALID;
        PSLSnsSigGroupDataClear(LeGroup,LePDCSnsCh,SNS_CYCLE_0);
        PSLSnsSigGroupDataClear(LeGroup,LePDCSnsCh,SNS_CYCLE_1);
        PSLSnsSigGroupDataClear(LeGroup,LePDCSnsCh,SNS_CYCLE_2);
    }
}






/******************************************************************************
 * 芥板绉: SnsSigGroupDataPowerOnInit
 * 
 * 芥杩: ㈠ご淇″风瀛版涓靛濮?
 * 
 * 杈ュ:?
 * 
 * 杈哄:?
 * 
 * 杩??
 * 
 * 跺璇存:?
 * 
 * 淇规ユ              ?       淇逛?         淇瑰瀹  
 * 2023-07-11 13:39   V0.1      AntonyFang   娆″甯
 ******************************************************************************/
void PSLSnsSigGroupDataPowerOnInit(void)
{
    PSLSnsSigGroupDataCacheInit(PDC_SNS_GROUP_FRONT);
    PSLSnsSigGroupDataCacheInit(PDC_SNS_GROUP_REAR);
}

/******************************************************************************
 * 芥板绉: SetSnsSigGroupDataUpdateFlag
 * 
 * 芥杩: 缃浣娉淇″风存版蹇锛ㄤ渚ч疯揪绛妯″璋搴浣跨
 * 
 * 杈ュ:LeGroup--㈠ご缁凤LePDCSnsCh--㈠ご?
 * 
 * 杈哄:?
 * 
 * 杩??
 * 
 * 跺璇存:?
 * 
 * 淇规ユ              ?       淇逛?         淇瑰瀹  
 * 2023-08-28 08:25   V0.1      AntonyFang   娆″甯
 ******************************************************************************/
void PSLSetSnsSigGroupDataUpdateFlag(PDCSnsGroupType LeGroup,PDCSnsChannelType LePDCSnsCh)
{
    PSLSnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;

    LpstrSnsSigGroupDataCache = &GstrPSLSnsSigGroupDataCache[LeGroup][LePDCSnsCh];
    LpstrSnsSigGroupDataCache->u8SigGroupUpdateFlag = 1;
}



/******************************************************************************
 * 芥板绉: ClearSnsSigGroupDataUpdateFlag
 * 
 * 芥杩: 缃浣娉淇″风存版蹇锛ㄤ渚ч疯揪绛妯″璋搴浣跨
 * 
 * 杈ュ:LeGroup--㈠ご缁凤LePDCSnsCh--㈠ご?
 * 
 * 杈哄:?
 * 
 * 杩??
 * 
 * 跺璇存:?
 * 
 * 淇规ユ              ?       淇逛?         淇瑰瀹  
 * 2023-08-28 08:26   V0.1      AntonyFang   娆″甯
 ******************************************************************************/
void PSLClearSnsSigGroupDataUpdateFlag(PDCSnsGroupType LeGroup,PDCSnsChannelType LePDCSnsCh)
{
    PSLSnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;

    LpstrSnsSigGroupDataCache = &GstrPSLSnsSigGroupDataCache[LeGroup][LePDCSnsCh];
    LpstrSnsSigGroupDataCache->u8SigGroupUpdateFlag = 0;
}



/******************************************************************************
 * 函数名称: PSLEchoFilterAndSigGroupDataGet
 * 
 * 功能描述: 对原始数据做初级滤波(过滤噪点)，并提取原始信号组(即对能够组成第一第二信号的信号组合并在一起)
 * 
 * 输入参数:LeGroup--探头分组号；LePDCSnsCh--探头通道号
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-12-22 21:03   V0.1      AntonyFang   初次发布
 ******************************************************************************/

void PSLSnsEchoFilterAndSigGroupDataGet(PDCSnsGroupType LeGroup,PDCSnsChannelType LePDCSnsCh)
#if 1
{
    uint8 i;
    PSLSnsSigGroupDataCacheType *LpstrSnsSigGroupDataCache;
    Sns_CycleType LenuCurIndex;
    float LfCarMoveSub = 0;
    PDCSnsRawDataType *LpStrPDCSnsRawData;
    uint8 Lu8MasterEchoCnt,Lu8ListenEchoCnt;
    uint16 Lu16MasterDis,Lu16MasterHeight,Lu16MasterDisSub;
    uint16 Lu16ListenDis,Lu16ListenSecondDis,Lu16ListenHeight,Lu16ListenDisSub;
    uint16 Lu16FirstSecondEchoSub;
    uint8 Lu8SigGroupCnt;
    uint8 Lu8TableIndex;
    uint8 Lu8TableHighIndex;
    const uint16 *Lpu16SnsEchoThresholdTable;      
    const PSLSnsCalibHeightType *LptrSnsJudgeObjTypeThresholdTable;
    uint8 Lu8FindValidObjFlag = 0;
    uint16 Lu16ThresCompenset = 0;
    uint16 Lu16MasterThresCompenset = 0;    /* 主发探头的阈值补偿，主要考虑车速较高时，适当降低阈值，以保证高速的探测稳定性 */
    uint16 Lu16SnsSecondThanFirstHeight;
    uint16 Lu16FirstDisToBackupSub,Lu16SecondDisToBackupSub;
    uint8 Lu8FirstDisIsNoise;
    uint16 Lu16NoiseHeight = 1000;
    uint8 Lu8ChirpModeFlag = 0;
    uint16 Lu16FirstSecondHeightSub = 0;

    LpStrPDCSnsRawData = &GstrPDCSnsRawData[LeGroup];
    LpstrSnsSigGroupDataCache = &GstrPSLSnsSigGroupDataCache[LeGroup][LePDCSnsCh];
    /* 主发阈值获取 */
    if((LePDCSnsCh == PDC_SNS_CH_FLS_RLS)||(LePDCSnsCh == PDC_SNS_CH_FRS_RRS))
    {
        if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
        {
            Lpu16SnsEchoThresholdTable = Gpu16PSLSnsThresholdTable[LeGroup][0];
        }
        else
        {
            Lpu16SnsEchoThresholdTable = Gpu16PSLSnsThresholdTable[LeGroup][1];
        }
    }
    else if((LePDCSnsCh == PDC_SNS_CH_FL_RL)||(LePDCSnsCh == PDC_SNS_CH_FR_RR))
    {
       return;
    }
    else
    {
      return;
    }
    //printf("MeasType:%d,Test Threshold20:%d\r\n",LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh],Lpu16SnsEchoThresholdTable[20]);
    if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
    {
        Lu8ChirpModeFlag = 0;
        LptrSnsJudgeObjTypeThresholdTable = &GStrPSLObjJudgeStandardThresholdTableInRAM[0];
        Lu16SnsSecondThanFirstHeight = PSL_SNS_STD_SECOND_THAN_FIRST_HEIGHT;
        if(Gu16CarSpdForSnsUse < 50)
        {
            Lu16MasterThresCompenset = 0;
        }
        else if(Gu16CarSpdForSnsUse < 100)
        {
            Lu16MasterThresCompenset = PSL_SNS_STD_MASTER_CMP_THRES_1KM;
        }
        else if(Gu16CarSpdForSnsUse < 300)
        {
            Lu16MasterThresCompenset = PSL_SNS_STD_MASTER_CMP_THRES_3KM;
        }
        else if(Gu16CarSpdForSnsUse < 500)
        {
            Lu16MasterThresCompenset = PSL_SNS_STD_MASTER_CMP_THRES_5KM;
        }
        else if(Gu16CarSpdForSnsUse < 700)
        {
            Lu16MasterThresCompenset = PSL_SNS_STD_MASTER_CMP_THRES_7KM;
        }
        else
        {
            Lu16MasterThresCompenset = PSL_SNS_STD_MASTER_CMP_THRES_MORE_7KM;
        }
    }
    else
    {
        Lu8ChirpModeFlag = 1;
        LptrSnsJudgeObjTypeThresholdTable = &GStrPSLObjJudgeChirpThresholdTableInRAM[0];
        Lu16SnsSecondThanFirstHeight = PSL_SNS_CHIRP_SECOND_THAN_FIRST_HEIGHT;
        if(Gu16CarSpdForSnsUse < 50)
        {
            Lu16MasterThresCompenset = 0;
        }
        else if(Gu16CarSpdForSnsUse < 100)
        {
            Lu16MasterThresCompenset = PSL_SNS_CHIRP_MASTER_CMP_THRES_1KM;
        }
        else if(Gu16CarSpdForSnsUse < 300)
        {
            Lu16MasterThresCompenset = PSL_SNS_CHIRP_MASTER_CMP_THRES_3KM;
        }
        else if(Gu16CarSpdForSnsUse < 500)
        {
            Lu16MasterThresCompenset = PSL_SNS_CHIRP_MASTER_CMP_THRES_5KM;
        }
        else if(Gu16CarSpdForSnsUse < 700)
        {
            Lu16MasterThresCompenset = PSL_SNS_CHIRP_MASTER_CMP_THRES_7KM;
        }
        else
        {
            Lu16MasterThresCompenset = PSL_SNS_CHIRP_MASTER_CMP_THRES_MORE_7KM;
        }
    }



    /* 更新当前索引值 */
    LpstrSnsSigGroupDataCache->enuCurIndex++;
    if(LpstrSnsSigGroupDataCache->enuCurIndex >= PSL_CYCLE_NUM)
    {
        LpstrSnsSigGroupDataCache->enuCurIndex = PSL_CYCLE_0;
    }
    LenuCurIndex = LpstrSnsSigGroupDataCache->enuCurIndex;
    /*开始索引技术，方便后续累计到3个周期后进行数据跟踪处理*/
    if(LpstrSnsSigGroupDataCache->u8RecordCnt < PSL_CYCLE_NUM)
    {
        LpstrSnsSigGroupDataCache->u8RecordCnt++;
    }

    PSLSnsSigGroupDataClear(LeGroup,LePDCSnsCh,LenuCurIndex);

    /* 获取上一轮到本轮探头探测车辆移动的距离及对应的系统时间 */
    if(LpstrSnsSigGroupDataCache->u8RecordCnt > 1)
    {
        LfCarMoveSub = GstrSnsCarMovSts[LeGroup][LePDCSnsCh].fSnsCarMovDisSub;
    }
    if(LeGroup == PDC_SNS_GROUP_FRONT)
    {
        /** @brief: 对于前探头，车子前进是靠近障碍物 */
        LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub = -LfCarMoveSub;
    }
    else
    {
        LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].fCarMoveDisSub = LfCarMoveSub;
    }
    //LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].u32SysTime = GetSystemTimeCnt_Ms();
    LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].u32SysTime = 0;
    LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eCarDir = GstrSnsCarMovSts[LeGroup][LePDCSnsCh].eCarDir;
    LpstrSnsSigGroupDataCache->SysDataBuf[LenuCurIndex].eMeasType = LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh];

    /* Step1:通过阈值过滤和提取主发回波信号组 */
    Lu8MasterEchoCnt = LpStrPDCSnsRawData->u8MasterEchoCnt[LePDCSnsCh];
    Lu8SigGroupCnt = 0;
    for(i = 0; i < Lu8MasterEchoCnt; i++)
    {
        /* 先判断是否有第二回波进行匹配，再进行查询滤波；避免把回波信号组的隐藏信息过滤掉 */
        if(i == 0)
        {
            if(LpStrPDCSnsRawData->u16NFD_Dis[LePDCSnsCh] < LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i])
            {
                Lu16MasterDis = LpStrPDCSnsRawData->u16NFD_Dis[LePDCSnsCh];
                Lu16MasterHeight = PSL_SNS_NFD_ECHO_HEIGHT;
            }
            else
            {
                Lu16MasterDis = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i];
                Lu16MasterHeight = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i];
            }
        }
        else
        {
            Lu16MasterDis = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i];
            Lu16MasterHeight = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i];
        }
        
        if(Lu16MasterDis < PSL_TABLE_MAX_DIS)
        {
            Lu8TableIndex = Lu16MasterDis/PSL_TABLE_STEP;
            Lu8TableHighIndex = Lu16MasterDis/PSL_HIGH_TABLE_STEP;
        }
        else
        {
            Lu8TableIndex = SNS_PSL_DIS_400cm;
            Lu8TableHighIndex = SNS_PSL_DIS_HIGH_400cm;
        }

        if(Lu8TableIndex < SNS_PSL_DIS_100cm)
        {
            Lu16NoiseHeight = 1500;
        }
        else if(Lu8TableIndex < SNS_PSL_DIS_150cm)
        {
            Lu16NoiseHeight = 1200;
        }
        else if(Lu8TableIndex < SNS_PSL_DIS_200cm)
        {
            Lu16NoiseHeight = 1100;
        }
        else 
        {
            Lu16NoiseHeight = 900;
        }

        /* 第一回波必须大于PVC的阈值，否则直接当做噪点过滤掉--2023-12-01 */
        if(Lu16MasterHeight < (Lpu16SnsEchoThresholdTable[Lu8TableIndex]-Lu16MasterThresCompenset))
        {
            continue;
        }

        if(Lu16MasterDis < 2000)
        {
            Lu16FirstSecondEchoSub = PSL_MASTER_SECOND_ECHO_DIS;
        }
        else
        {
            Lu16FirstSecondEchoSub = PSL_MASTER_SECOND_ECHO_DIS2;
        }
        //printf("Lu16MasterDis:%d\r\n",Lu16MasterDis);
        Lu8FindValidObjFlag = 0;
        if((i+1) < Lu8MasterEchoCnt)
        {
            Lu16MasterDisSub = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1] - Lu16MasterDis;
            if(Lu16MasterDisSub < Lu16FirstSecondEchoSub)
            {
                /* 此处存在风险，即一个大墙的20cm附近存在小的障碍物，可能存在距离不对问题,因此仅针对70cm以外的使用该策略 */
                if(((Lu16MasterHeight+Lu16SnsSecondThanFirstHeight) < LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1])&&\
                    (Lu16MasterDis > 700))
                {                   
                    if(LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1] < PSL_TABLE_MAX_DIS)
                    {
                        Lu8TableIndex = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1]/PSL_TABLE_STEP;
                        Lu8TableHighIndex = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1]/PSL_HIGH_TABLE_STEP;
                    }
                    else
                    {
                        Lu8TableIndex = SNS_PSL_DIS_400cm;
                        Lu8TableHighIndex = SNS_PSL_DIS_HIGH_400cm;
                    }
                    
                    if(LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1] > (Lpu16SnsEchoThresholdTable[Lu8TableIndex]-Lu16MasterThresCompenset))
                    {
                        /* 第一第二回波构成信号组，且第二回波大于第一回波的处理；
                           由于探头扫频模式下的灵敏度提升，导致较多噪点和正常回波进行误配属，需要在此处进行过滤处理*/
                        if(Lu8ChirpModeFlag)
                        {
                            Lu8FirstDisIsNoise = 0;
                            Lu16FirstSecondHeightSub = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1] - Lu16MasterHeight;
                            if(Lu16FirstSecondHeightSub > 500)
                            {
                                if(Lu16MasterHeight < Lu16NoiseHeight)
                                {
                                    Lu8FirstDisIsNoise = 1;
                                }
                            }
                            if(Lu8FirstDisIsNoise == 0)
                            {
                                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16MasterDis - PSL_MASTER_COMPEN_DIS;
                                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1] - PSL_MASTER_COMPEN_DIS;
                                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1];
                                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1] - PSL_MASTER_COMPEN_DIS;
                                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1];
                                Lu8FindValidObjFlag = 1;
                                i++;
                            }
                            else
                            {
                                /* 不做处理，继续下一个循环 */
                            }
                        }
                        else
                        {
                            /* 定频使用第一回距离作为实际使用的距离更接近真实效果 */
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16MasterDis - PSL_MASTER_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1] - PSL_MASTER_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1];
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16MasterDis - PSL_MASTER_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1];
                            Lu8FindValidObjFlag = 1;
                            i++;
                        }
                    }
                }
                else
                {
                    /* 第一回波高度大于第二回波高度，不进行后续的查询，直接配属到信号组中 */
                    if(Lu16MasterHeight > (Lpu16SnsEchoThresholdTable[Lu8TableIndex]-Lu16MasterThresCompenset))
                    {
                        /* 添加对于第二回波有效性的判断，第二回波高度同样需要通过阈值表过滤 */
                        //if(LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1] > (Lu16MasterHeight/3))
                        if(1)
                        {
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16MasterDis - PSL_MASTER_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterDis[LePDCSnsCh][i+1] - PSL_MASTER_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16MasterHeight[LePDCSnsCh][i+1];
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16MasterDis - PSL_MASTER_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                        }
                        else
                        {
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16MasterDis - PSL_MASTER_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16MasterDis - PSL_MASTER_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                        }
                        Lu8FindValidObjFlag = 1;
                        i++;
                    }
                }
            }
            else
            {
                /* 第一、第二回波构不成信号组，直接将第一回波单独构建一个信号组 */
                if(Lu16MasterHeight > (Lpu16SnsEchoThresholdTable[Lu8TableIndex]-Lu16MasterThresCompenset))
                {
                    LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                    LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16MasterDis - PSL_MASTER_COMPEN_DIS;
                    LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                    LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16MasterDis - PSL_MASTER_COMPEN_DIS;
                    LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                    Lu8FindValidObjFlag = 1;
                }
            }
        }
        else
        {
            /* 无后续回波，和设定阈值对比后直接配属到一个信号组中 */
            if(Lu16MasterHeight > (Lpu16SnsEchoThresholdTable[Lu8TableIndex]-Lu16MasterThresCompenset))
            {
                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16MasterDis - PSL_MASTER_COMPEN_DIS;
                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16MasterDis - PSL_MASTER_COMPEN_DIS;
                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16MasterHeight;
                Lu8FindValidObjFlag = 1;
            }
        }

        if(Lu8FindValidObjFlag)
        {
            if(LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] > LptrSnsJudgeObjTypeThresholdTable[Lu8TableHighIndex].u16BigWallHeight)
            {
                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt] = OBJ_BIGWALL_TYPE;
            }
            else
            {
                LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt] = OBJ_PVC_PIPE_TYPE;
            }

            /* 只保留小障碍物后的大障碍物逻辑处理--2023-11-13 */
            if(Lu8SigGroupCnt > 0)
            {
                if((LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt]-5) < LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt-1])
                {
                    break;
                }
            }
            Lu8SigGroupCnt++;
        }
        if(Lu8SigGroupCnt == MAX_NUM_OF_SIG_GROUP)
        {
            break;
        }
    }
    LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u8SigGroupCnt = Lu8SigGroupCnt;
    /* 保留备份值 */
    for(i = 0; i < MAX_NUM_OF_SIG_GROUP; i++)
    {
        LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstDisBackup[i] = LpstrSnsSigGroupDataCache->MasterBuf[LenuCurIndex].u16FirstEchoDis[i];
    }

    /* Step2:通过阈值过滤和提取左侦听回波信号组 */
    Lu16ThresCompenset = 0;
    Lu8ListenEchoCnt = LpStrPDCSnsRawData->u8LeftListenEchoCnt[LePDCSnsCh];
    Lu8SigGroupCnt = 0;
    for(i = 0; i < Lu8ListenEchoCnt; i++)
    {
        /* 先判断是否有第二回波进行匹配，再进行查询滤波；避免把回波信号组的隐藏信息过滤掉 */
        Lu16ListenDis = (LpStrPDCSnsRawData->u16LeftListenDis[LePDCSnsCh][i])>>1;
        Lu16ListenHeight = LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i];
        if((Lu16ListenDis > 1500)&&(Lu16ListenDis < 3600))
        {
            if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
            {
                Lu16ThresCompenset = PSL_SNS_STD_LISTEN_COMPEN_HEIGHT;
            }
            else
            {
                Lu16ThresCompenset = PSL_SNS_CHIRP_LISTEN_COMPEN_HEIGHT;
            }
        }
        else if(Lu16ListenDis < 900)
        {
            if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
            {
                Lu16ThresCompenset = PSL_SNS_STD_NEAR_LISTEN_COMPEN_HEIGHT;
            }
        }
        if(Lu16ListenDis < PSL_TABLE_MAX_DIS)
        {
            Lu8TableIndex = Lu16ListenDis/PSL_TABLE_STEP;
            Lu8TableHighIndex = Lu16ListenDis/PSL_HIGH_TABLE_STEP;
        }
        else
        {
            Lu8TableIndex = SNS_PSL_DIS_400cm;
            Lu8TableHighIndex = SNS_PSL_DIS_HIGH_400cm;
        }

        if(Lu16ListenDis < 2000)
        {
            Lu16FirstSecondEchoSub = PSL_LISTEN_SECOND_ECHO_DIS;
        }
        else
        {
            Lu16FirstSecondEchoSub = PSL_LISTEN_SECOND_ECHO_DIS2;
        }

        if(Lu8TableIndex < SNS_PSL_DIS_100cm)
        {
            Lu16NoiseHeight = 1500;
        }
        else if(Lu8TableIndex < SNS_PSL_DIS_150cm)
        {
            Lu16NoiseHeight = 1200;
        }
        else if(Lu8TableIndex < SNS_PSL_DIS_200cm)
        {
            Lu16NoiseHeight = 1100;
        }
        else 
        {
            Lu16NoiseHeight = 900;
        }

        /* 第一回波必须大于PVC的阈值，否则直接当做噪点过滤掉--2023-12-01 */
        if(Lu16ListenHeight < (Lpu16SnsEchoThresholdTable[Lu8TableIndex] - Lu16ThresCompenset))
        {
            continue;
        }
        
        Lu8FindValidObjFlag = 0;
        if((i+1) < Lu8ListenEchoCnt)
        {
            Lu16ListenSecondDis = (LpStrPDCSnsRawData->u16LeftListenDis[LePDCSnsCh][i+1])>>1;
            Lu16ListenDisSub = Lu16ListenSecondDis - Lu16ListenDis;
            if(Lu16ListenDisSub < Lu16FirstSecondEchoSub)
            {
                /* 此处存在风险，即一个大墙的20cm附近存在小的障碍物，可能存在距离不对问题,因此仅针对70cm以外的使用该策略 */
                if(((Lu16ListenHeight+Lu16SnsSecondThanFirstHeight) < LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1])&&\
                    (Lu16ListenDis > 700))
                {
                    if(Lu16ListenSecondDis < PSL_TABLE_MAX_DIS)
                    {
                        Lu8TableIndex = Lu16ListenSecondDis/PSL_TABLE_STEP;
                        Lu8TableHighIndex = Lu16ListenSecondDis/PSL_HIGH_TABLE_STEP;
                    }
                    else
                    {
                        Lu8TableIndex = SNS_PSL_DIS_400cm;
                        Lu8TableHighIndex = SNS_PSL_DIS_HIGH_400cm;
                    }
                    if(LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1] > (Lpu16SnsEchoThresholdTable[Lu8TableIndex] - Lu16ThresCompenset))
                    {   
                        /* 第一第二回波构成信号组，且第二回波大于第一回波的处理；
                           由于探头扫频模式下的灵敏度提升，导致较多噪点和正常回波进行误配属，需要在此处进行过滤处理*/
                        if(Lu8ChirpModeFlag)
                        {
                            Lu8FirstDisIsNoise = 0;
                            Lu16FirstSecondHeightSub = LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1] - Lu16ListenHeight;
                            if(Lu16FirstSecondHeightSub > 500)
                            {
                                if(Lu16ListenHeight < Lu16NoiseHeight)
                                {
                                    Lu8FirstDisIsNoise = 1;
                                }
                            }
                            
                            if(Lu8FirstDisIsNoise == 0)
                            {
                                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - PSL_LISTEN_COMPEN_DIS;
                                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - PSL_LISTEN_COMPEN_DIS;
                                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1];
                                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - PSL_LISTEN_COMPEN_DIS;
                                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1];
                                Lu8FindValidObjFlag = 1;
                                i++;
                            }
                            else
                            {
                                /* 继续后续的循环 */
                            }
                        }
                        else
                        {
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - PSL_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - PSL_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1];
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - PSL_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1];
                            Lu8FindValidObjFlag = 1;
                            i++;
                        }
                    }
                }
                else
                {
                    if(Lu16ListenHeight > (Lpu16SnsEchoThresholdTable[Lu8TableIndex] - Lu16ThresCompenset))
                    {
                        /* 添加对于第二回波有效性的判断，第二回波高度同样需要通过阈值表过滤 */
                        //if(LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1] > (Lu16ListenHeight/3))
                        if(1)
                        {
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - PSL_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - PSL_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16LeftListenHeight[LePDCSnsCh][i+1];
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - PSL_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                        }
                        else
                        {
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - PSL_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - PSL_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                        }
                        Lu8FindValidObjFlag = 1;
                        i++;
                    }
                }
            }
            else
            {
                if(Lu16ListenHeight > (Lpu16SnsEchoThresholdTable[Lu8TableIndex] - Lu16ThresCompenset))
                {
                    LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                    LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - PSL_LISTEN_COMPEN_DIS;
                    LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                    LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - PSL_LISTEN_COMPEN_DIS;
                    LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                    Lu8FindValidObjFlag = 1;
                }
            }
        }
        else
        {
            if(Lu16ListenHeight > (Lpu16SnsEchoThresholdTable[Lu8TableIndex] - Lu16ThresCompenset))
            {
                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - PSL_LISTEN_COMPEN_DIS;
                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - PSL_LISTEN_COMPEN_DIS;
                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                Lu8FindValidObjFlag = 1;
            }
        }
        if(Lu8FindValidObjFlag)
        {
            if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] > LptrSnsJudgeObjTypeThresholdTable[Lu8TableHighIndex].u16BigWallHeight)
            {
                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt] = OBJ_BIGWALL_TYPE;
            }
            else
            {
                LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt] = OBJ_PVC_PIPE_TYPE;
            }
#if 0
            /* 只保留小障碍物后的大障碍物逻辑处理--2023-11-13 */
            if(Lu8SigGroupCnt > 0)
            {
                if(LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] < LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt-1])
                {
                    break;
                }
            }
#endif
            Lu8SigGroupCnt++;
        }
        
        if(Lu8SigGroupCnt == MAX_NUM_OF_SIG_GROUP)
        {
            break;
        }
    }
    LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u8SigGroupCnt = Lu8SigGroupCnt;
    /* 保留备份值 */
    for(i = 0; i < MAX_NUM_OF_SIG_GROUP; i++)
    {
        LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstDisBackup[i] = LpstrSnsSigGroupDataCache->LeftBuf[LenuCurIndex].u16FirstEchoDis[i];
    }

    /* Step2:通过阈值过滤和提取左侦听回波信号组 */
    Lu8ListenEchoCnt = LpStrPDCSnsRawData->u8RightListenEchoCnt[LePDCSnsCh];
    Lu8SigGroupCnt = 0;
    Lu16ThresCompenset = 0;
    for(i = 0; i < Lu8ListenEchoCnt; i++)
    {
        /* 先判断是否有第二回波进行匹配，再进行查询滤波；避免把回波信号组的隐藏信息过滤掉 */
        Lu16ListenDis = (LpStrPDCSnsRawData->u16RightListenDis[LePDCSnsCh][i])>>1;
        Lu16ListenHeight = LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i];
        if((Lu16ListenDis > 1500)&&(Lu16ListenDis < 3600))
        {
            if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
            {
                Lu16ThresCompenset = PSL_SNS_STD_LISTEN_COMPEN_HEIGHT;
            }                           
            else
            {
                Lu16ThresCompenset = PSL_SNS_CHIRP_LISTEN_COMPEN_HEIGHT;
            }
        }
        else if(Lu16ListenDis < 900)
        {
            if(LpStrPDCSnsRawData->enuPDCSnsMeasType[LePDCSnsCh] == PDC_SNS_MEAS_STD)
            {
                Lu16ThresCompenset = PSL_SNS_STD_NEAR_LISTEN_COMPEN_HEIGHT;
            }
        }

        if(Lu16ListenDis < PSL_TABLE_MAX_DIS)
        {
            Lu8TableIndex = Lu16ListenDis/PSL_TABLE_STEP;
            Lu8TableHighIndex = Lu16ListenDis/PSL_HIGH_TABLE_STEP;
        }
        else
        {
            Lu8TableIndex = SNS_PSL_DIS_400cm;
            Lu8TableHighIndex = SNS_PSL_DIS_HIGH_400cm;
        }


        if(Lu16ListenDis < 2000)
        {
            Lu16FirstSecondEchoSub = PSL_LISTEN_SECOND_ECHO_DIS;
        }
        else
        {
            Lu16FirstSecondEchoSub = PSL_LISTEN_SECOND_ECHO_DIS2;
        }

        if(Lu8TableIndex < SNS_PSL_DIS_100cm)
        {
            Lu16NoiseHeight = 1500;
        }
        else if(Lu8TableIndex < SNS_PSL_DIS_150cm)
        {
            Lu16NoiseHeight = 1200;
        }
        else if(Lu8TableIndex < SNS_PSL_DIS_200cm)
        {
            Lu16NoiseHeight = 1100;
        }
        else 
        {
            Lu16NoiseHeight = 900;
        }

        /* 第一回波必须大于PVC的阈值，否则直接当做噪点过滤掉--2023-12-01 */
        if(Lu16ListenHeight < (Lpu16SnsEchoThresholdTable[Lu8TableIndex] - Lu16ThresCompenset))
        {
            continue;
        }
        Lu8FindValidObjFlag = 0;
        if((i+1) < Lu8ListenEchoCnt)
        {
            Lu16ListenSecondDis = (LpStrPDCSnsRawData->u16RightListenDis[LePDCSnsCh][i+1])>>1;
            Lu16ListenDisSub = Lu16ListenSecondDis - Lu16ListenDis;
            if(Lu16ListenDisSub < Lu16FirstSecondEchoSub)
            {
                /* 此处存在风险，即一个大墙的20cm附近存在小的障碍物，可能存在距离不对问题,因此仅针对70cm以外的使用该策略 */
                if(((Lu16ListenHeight+Lu16SnsSecondThanFirstHeight) < LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1])&&\
                    (Lu16ListenDis > 700))
                {
                    if(Lu16ListenSecondDis < PSL_TABLE_MAX_DIS)
                    {
                        Lu8TableIndex = Lu16ListenSecondDis/PSL_TABLE_STEP;
                        Lu8TableHighIndex = Lu16ListenSecondDis/PSL_HIGH_TABLE_STEP;
                    }
                    else
                    {
                        Lu8TableIndex = SNS_PSL_DIS_400cm;
                        Lu8TableHighIndex = SNS_PSL_DIS_HIGH_400cm;
                    }

                    if(LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1] > (Lpu16SnsEchoThresholdTable[Lu8TableIndex] - Lu16ThresCompenset))
                    {
                        /* 第一第二回波构成信号组，且第二回波大于第一回波的处理；
                           由于探头扫频模式下的灵敏度提升，导致较多噪点和正常回波进行误配属，需要在此处进行过滤处理*/
                        if(Lu8ChirpModeFlag)
                        {
                            Lu8FirstDisIsNoise = 0;
                            Lu16FirstSecondHeightSub = LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1] - Lu16ListenHeight;
                            if(Lu16FirstSecondHeightSub > 500)
                            {
                                if(Lu16ListenHeight < Lu16NoiseHeight)
                                {
                                    Lu8FirstDisIsNoise = 1;
                                }
                            }
                            if(Lu8FirstDisIsNoise == 0)
                            {
                                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - PSL_LISTEN_COMPEN_DIS;
                                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - PSL_LISTEN_COMPEN_DIS;
                                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1];
                                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - PSL_LISTEN_COMPEN_DIS;
                                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1];
                                Lu8FindValidObjFlag = 1;
                                i++;
                            }
                            else
                            {
                                /* 继续后续的查询 */
                            }
                        }
                        else
                        {
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - PSL_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - PSL_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1];
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - PSL_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1];
                            Lu8FindValidObjFlag = 1;
                            i++;
                        }
                    }
                }
                else
                {
                    if(Lu16ListenHeight > (Lpu16SnsEchoThresholdTable[Lu8TableIndex] - Lu16ThresCompenset))
                    {
                        /* 添加对于第二回波有效性的判断，第二回波高度同样需要通过阈值表过滤 */
                        //if(LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1] > (Lu16ListenHeight/3))
                        if(1)
                        {
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 2;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - PSL_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoDis[Lu8SigGroupCnt] = Lu16ListenSecondDis - PSL_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16SecondEchoHeight[Lu8SigGroupCnt] = LpStrPDCSnsRawData->u16RightListenHeight[LePDCSnsCh][i+1];
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - PSL_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                        }
                        else
                        {
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - PSL_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - PSL_LISTEN_COMPEN_DIS;
                            LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                        }
                        Lu8FindValidObjFlag = 1;
                        i++;
                    }
                }
            }
            else
            {
                if(Lu16ListenHeight > (Lpu16SnsEchoThresholdTable[Lu8TableIndex] - Lu16ThresCompenset))
                {
                    LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                    LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - PSL_LISTEN_COMPEN_DIS;
                    LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                    LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - PSL_LISTEN_COMPEN_DIS;
                    LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                    Lu8FindValidObjFlag = 1;
                }
            }
        }
        else
        {
            if(Lu16ListenHeight > (Lpu16SnsEchoThresholdTable[Lu8TableIndex] - Lu16ThresCompenset))
            {
                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8GroupEchoCnt[Lu8SigGroupCnt] = 1;
                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[Lu8SigGroupCnt] = Lu16ListenDis - PSL_LISTEN_COMPEN_DIS;
                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16ActualDis[Lu8SigGroupCnt] = Lu16ListenDis - PSL_LISTEN_COMPEN_DIS;
                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] = Lu16ListenHeight;
                Lu8FindValidObjFlag = 1;
            }
        }
        if(Lu8FindValidObjFlag)
        {
            if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] > LptrSnsJudgeObjTypeThresholdTable[Lu8TableHighIndex].u16BigWallHeight)
            {
                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt] = OBJ_BIGWALL_TYPE;
            }
            else
            {
                LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].enuOriObjType[Lu8SigGroupCnt] = OBJ_PVC_PIPE_TYPE;
            }
#if 0
            /* 只保留小障碍物后的大障碍物逻辑处理--2023-11-13 */
            if(Lu8SigGroupCnt > 0)
            {
                if(LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt] < LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16MaxHeight[Lu8SigGroupCnt-1])
                {
                    break;
                }
            }
#endif
            Lu8SigGroupCnt++;
        }

        
        if(Lu8SigGroupCnt == MAX_NUM_OF_SIG_GROUP)
        {
            break;
        }
    }
    LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u8SigGroupCnt = Lu8SigGroupCnt;
    /* 保留备份值 */
    for(i = 0; i < MAX_NUM_OF_SIG_GROUP; i++)
    {
        LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstDisBackup[i] = LpstrSnsSigGroupDataCache->RightBuf[LenuCurIndex].u16FirstEchoDis[i];
    }

    PSLSetSnsSigGroupDataUpdateFlag(LeGroup,LePDCSnsCh);
	
    LenuCurIndex = GstrPSLSnsSigGroupDataCache[LeGroup][LePDCSnsCh].enuCurIndex;
    
}

#endif



