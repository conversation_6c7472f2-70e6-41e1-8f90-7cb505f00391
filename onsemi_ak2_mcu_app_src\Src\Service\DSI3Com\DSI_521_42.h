/*
 * DSI_521_42.h
 *
 *  Created on: 2021年4月1日 
 *      Author: 6000021992
 */

#ifndef DSI_521_42
#define DSI_521_42

#define _LITTLE_ENDIAN_

#include "DSI3_SPI.h"

#define PRINTF_ELMOS42                      ELMOS42_DEBUG_PRINT

#define DSISlaveSnsNum 3



#define TDMA_PDCMSymbCnt (uint8)16

#define TDMA_ID (uint8)0x40
/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/
typedef enum
{
    eDSIReturnType_OK = 0,
    eDSIReturnType_N_OK,
    eDSIReturnType_Resp_Data_N_OK,
    eDSIReturnType_Resp_CRC_N_OK,
    eDSIReturnType_InData_ERR = 0xFF,
}DSIReturnType_en;

typedef enum
{
    DSIMaster0 = 0,
	DSIMaster1,
    DSIMasterNum,
}DSIMasterID_en;

typedef enum
{
    DSI_CHL_NONE = 0,
    DSIChlSEL1,
    DSIChlSEL2,
    DSIChlSEL1And2,
}DSIChlSel_en;

typedef struct
{
    uint8 cData[2];
}DSI3_WRRegCMD;

typedef struct
{
    uint8 Cmd[2];
    uint8 Data[2];
    uint8 SPI_CRC[2];
    uint8 SPI_Stuff[2];
}DSI3_WReg_str;

typedef union
{
    DSI3_WReg_str WRegCMD;
    uint16 wData[4];
    uint8 cData[8];
}DSI3_WReg_un;



typedef enum
{
    WR_RegNum_Start = 0,
    W1C_IRQ_STAT = WR_RegNum_Start,
    W1C_SPI_IRQ_STAT,
    W1C_DSI1_IRQ_STAT,
    W1C_DSI2_IRQ_STAT,

    W_VDSI_CTRL,
    W_IRQ_EN,
    W_SPI_IRQ_EN,

    W_DSI1_MODE,
    W_DSI1_CFG,
    W_DSI1_IRQ_EN,
    W_DSI1_TIMING_OFFSET = 10,
    W_DSI1_DM_START_TIME_BC,

    W_DSI2_MODE,
    W_DSI2_CFG,
    W_DSI2_IRQ_EN,
    W_DSI2_TIMING_OFFSET,
    W_DSI2_DM_START_TIME_BC,

    W_WAVE_FALL,
    W_WAVE_RISE,



    W_RegNum,
    
    R_RegNum_Start = W_RegNum,
    
    R_VDSI_CTRL = R_RegNum_Start,
    R_IRQ_EN = 20,
    R_SPI_IRQ_EN,

    R_DSI1_MODE,
    R_DSI1_CFG,
    R_DSI1_IRQ_EN,
    R_DSI1_TIMING_OFFSET,
    R_DSI1_DM_START_TIME_BC,

    R_DSI2_MODE,
    R_DSI2_CFG,
    R_DSI2_IRQ_EN,
    R_DSI2_TIMING_OFFSET = 30,
    R_DSI2_DM_START_TIME_BC,

    R_WAVE_FALL,
    R_WAVE_RISE,

    R_IRQ_STAT,
    R_SPI_IRQ_STAT,
    R_DSI1_IRQ_STAT,
    R_DSI2_IRQ_STAT,

    R_DSI1_STAT,
    R_DSI1_STAT2,
    
    R_DSI2_STAT = 40,
    R_DSI2_STAT2,
    
    R_STATUS_WORD,

    R_MAIN_STAT,
    R_VDSI_STAT,
    
    R_IC_REVISION,
    R_CHIP_ID_LOW,
    R_CHIP_ID_HIGH,

    R_DSI1_PDCM_PERIOD,

    R_DSI1_CMD_LOW,
    R_DSI1_CMD_HIGH = 50,

    R_DSI2_PDCM_PERIOD,

    R_DSI2_CMD_LOW,
    R_DSI2_CMD_HIGH,

    checkAdr1,
    checkAdr2,
    checkAdr3,
    checkAdr4,
    checkAdr5,
    checkAdr6,
    checkAdr7 = 60,
    checkAdr8,
    checkAdr9,
    checkAdr10,
    checkAdr11,
    checkAdr12,
    checkAdr13,
    checkAdr14,
    checkAdr15,
    checkAdr16,
    checkAdr17 = 70,
    checkAdr18,   
    checkAdr19,

    WR_RegNum
}WRReg_en;

typedef enum
{
    DSI1_2TDMAGroup = 0,

    TDMAGroupNum,
}TDMAGroup_en;


typedef struct
{
    uint16 EarliestStartTime;
    uint16 LatestStartTime;

    uint8 ID;/*bit 6-7 */

    uint8 SymbolCount;
}TDMA_PacketSCHEME_Str;

typedef struct
{
    uint16 PDCM_PERIOD;

    uint8 BP;/*bit6-7 BP, bit0-5 NOP */
    uint8 PacketCount; /*bit5-7 NOP,bit0-4 PacketCount*/

    TDMA_PacketSCHEME_Str PacketSCHEME[6];

}FRAME_TDMA_SCHEME_str;


typedef enum 
{
    e_VDSI_5_1V = 0,
    e_VDSI_5_9V ,
    e_VDSI_6_7V ,        
    e_VDSI_7_4V ,
    e_VDSI_8_2V ,        
    e_VDSI_9_0V ,        
    e_VDSI_9_8V ,
    e_VDSI_10_5V ,   
}DSI_VDSI_e;

typedef enum 
{
    e_FCCBitTime_8us = 0,
    e_FCCBitTime_16us ,
    e_FCCBitTime_32us ,        

}DSI_FCCBitTime_e;
    
typedef enum 
{
    e_RCCChipTime_3us = 0,
    e_RCCChipTime_4us ,
    e_RCCChipTime_5us ,        
    e_RCCChipTime_6us ,        
    e_RCCChipTime_7us ,        
    e_RCCChipTime_8us ,        
    e_RCCChipTime_9us ,        
    e_RCCChipTime_10us ,                  
}DSI_RCCChipTime_e;


typedef struct
{

    uint8 Addr_CMD;/*bit 4-7  Addr ,bit0-3 CMD*/
    uint8 Data[2];
    uint8 DSICRC;

}CRM_CMD_Str;


typedef struct
{
    CRM_CMD_Str CRMData[2];
}TRANS_CRM_DM_Str;




typedef struct
{

    uint8 Status;   /*bit7  EE, bit6 LE ,bit5 SC, bit4 CR,
                       bit3  SP, bit2 SE ,bit1 UV, bit0 CE*/
    uint8 SymbolCount;

}DSI_Status_str;

typedef struct
{

    uint8 Status; /*bit5  ND, bit4 OR ,bit3 DL, bit2 PC*/

    uint8 PacketCount;

}PDCM_FRAME_STATUS_Str;



typedef struct
{
    uint8 PhysAddr_Status; /*bit4-7  PhysAddr, bit3 ComErr ,bit4 HwErr, bit0-1 Status*/

    uint8 Data[2];

    uint8 DSICRC;

}SnsCRMRES_Data_str;

typedef struct
{
    uint8 PhysAddr_FrameCount; /*bit4-7  PhysAddr, bit0-3 FrameCount*/

    uint8 Status_Type; /*bit7 ComErr ,bit6 HwErr, bit4-5 Type,bit0-3 SubType*/

    uint8 Data1_6[5];

    uint8 DSICRC;

}SnsPDCMRES_Data_str;



/* SPI_IRQ_STAT寄存器 */
typedef struct
{
#ifdef Big_Endian
    uint16 Reserve     :10; /* 填充位                  */
    uint16 SPI_CRC_BIST:1;  /* SPI CRC模块自检错误        */
    uint16 CMD_OVR     :2;  /* 接收到命令但未执行命令          */
    uint16 UND_CMD     :1;  /* 接收到未定义的命令            */
    uint16 CRC_ERR     :1;  /* SPI_CRC错误            */
    uint16 CMD_INC     :1;  /* 前一个指令不完整             */
#else
    uint16 CMD_INC     :1;  /* 前一个指令不完整             */
    uint16 CRC_ERR     :1;  /* SPI_CRC错误            */
    uint16 UND_CMD     :1;  /* 接收到未定义的命令            */
    uint16 CMD_OVR     :2;  /* 接收到命令但未执行命令          */
    uint16 SPI_CRC_BIST:1;  /* SPI CRC模块自检错误        */
    uint16 Reserve     :10; /* 填充位                  */



#endif
}SPI_IRQ_STAT_str;

/* DSI_IRQ_STAT寄存器（DSI1/DSI2）*/
typedef struct
{
#ifdef Big_Endian
    uint16 Reserve1    :2;  /* 填充位                    */
    uint16 DSI_UV      :1;  /* DSI通道欠压标志              */
    uint16 DSI_CRC_BIST:1;  /* DSI_CRC模块自检错误标志 */
    uint16 DSI_OC      :1;  /* DSI通道过流标志              */
    uint16 Reserve2    :1;  /* 填充位                    */
    uint16 DMOV        :1;  /* 寻址从机数量超过最大值            */
    uint16 DMFIN       :1;  /* 寻址完成标志                 */

    uint16 Reserve3    :2;  /* 填充位              */
    uint16 PDCMCRC     :1;  /* PDCM数据包CRC错误     */
    uint16 PDCMREC     :1;  /*                  */
    uint16 CRMCRC      :1;  /* CRM响应CRC 错误      */
    uint16 Reserve4    :1;  /* 填充位              */
    uint16 CRMTIM      :1;  /* 响应超时             */
    uint16 CRMREC      :1;  /*                  */
#else
    uint16 CRMREC      :1; /*                  */
    uint16 CRMTIM      :1; /* 响应超时             */
    uint16 Reserve4    :1; /* 填充位              */
    uint16 CRMCRC      :1; /* CRM响应CRC 错误      */
    uint16 PDCMREC     :1; /*                  */
    uint16 PDCMCRC     :1; /* PDCM数据包CRC错误     */
    uint16 Reserve3    :2; /* 填充位              */

    uint16 DMFIN       :1; /* 寻址完成标志                 */
    uint16 DMOV        :1; /* 寻址从机数量超过最大值            */
    uint16 Reserve2    :1; /* 填充位                    */
    uint16 DSI_OC      :1; /* DSI通道过流标志              */
    uint16 DSI_CRC_BIST:1; /* DSI_CRC模块自检错误标志 */
    uint16 DSI_UV      :1; /* DSI通道欠压标志              */
    uint16 Reserve1    :2; /* 填充位                    */


#endif
    
}DSI_IRQ_STAT_str;

/* STATUS_WORD寄存器 */
typedef struct
{
#ifdef Big_Endian
    uint16 OT           :1;  /* 过温标志                    */
    uint16 DSI_1_CMD_OVR:1;  /* DSI1命令溢出                */
    uint16 DSI_1_UV     :1;  /* DSI1欠压                  */
    uint16 DSI_1_PDCMREC:1;  /*                         */
    uint16 DSI_1_CRMREC :1;  /*                         */
    uint16 DSI_1_TDMA   :1;  /* DSI1的TDMA上传完成标志         */
    uint16 VCCUV_RFCB   :1;  /* VCC欠压或IC启动未完成标志         */
    uint16 DSI_0_CMD_OVR:1;  /* DSI0命令溢出                */
    
    uint16 DSI_0_UV     :1;  /* DSI0欠压               */
    uint16 DSI_0_PDCMREC:1;  /*                      */
    uint16 DSI_0_CRMREC :1;  /*                      */
    uint16 DSI_0_TDMA   :1;  /* DSI0的TDMA上传完成标志      */
    uint16 CLK_ERR      :1;  /* IC参考时钟错误标志           */
    uint16 CMD_INC      :1;  /* 前一个SPI命令不完整          */
    uint16 CRC_ERROR    :1;  /* SPI传输CRC错误           */
    uint16 UND_CMD      :1;  /* SPI接收到未定义的命令         */
#else
    uint16 UND_CMD      :1; /* SPI接收到未定义的命令         */
    uint16 CRC_ERROR    :1; /* SPI传输CRC错误           */
    uint16 CMD_INC      :1; /* 前一个SPI命令不完整          */
    uint16 CLK_ERR      :1; /* IC参考时钟错误标志           */
    uint16 DSI_0_TDMA   :1; /* DSI0的TDMA上传完成标志      */
    uint16 DSI_0_CRMREC :1; /*                      */
    uint16 DSI_0_PDCMREC:1; /*                      */
    uint16 DSI_0_UV     :1; /* DSI0欠压               */

    uint16 DSI_0_CMD_OVR:1; /* DSI0命令溢出                */
    uint16 VCCUV_RFCB   :1; /* VCC欠压或IC启动未完成标志         */
    uint16 DSI_1_TDMA   :1; /* DSI1的TDMA上传完成标志         */
    uint16 DSI_1_CRMREC :1; /*                         */
    uint16 DSI_1_PDCMREC:1; /*                         */
    uint16 DSI_1_UV     :1; /* DSI1欠压                  */
    uint16 DSI_1_CMD_OVR:1; /* DSI1命令溢出                */
    uint16 OT           :1; /* 过温标志                    */
  
#endif

}IC_STATUS_Str;


typedef struct
{
#ifdef Big_Endian
    uint16 VCC_UV   :1;  /* VCC欠压标志    */
    uint16 OT       :1;  /* 过温标志       */
    uint16 Reserve1 :1;  /* 填充位        */
    uint16 SPI_ERR  :1;  /* SPI错误标志 */
    uint16 VDSI_UV  :1;  /* DSI欠压标志 */
    uint16 FSM_RESET:1;  /* 该位置1表示IC被复位 */

    uint16 Pin_RESET:1;  /* 复位引脚            */
    uint16 CLK_ERR  :1;  /* 参考时钟故障标志        */
    uint16 Reserve2 :5;  /* 填充位             */
    uint16 RAM_BIST :1;  /* RAM启动自检错误       */
    uint16 DSI_ERR  :2;  /* DSI错误标志         */
#else
    uint16 DSI_ERR  :2;  /* DSI错误标志         */
    uint16 RAM_BIST :1;  /* RAM启动自检错误       */
    uint16 Reserve2 :5;  /* 填充位             */
    uint16 CLK_ERR  :1;  /* 参考时钟故障标志        */
    uint16 Pin_RESET:1;  /* 复位引脚            */

    uint16 FSM_RESET:1;  /* 该位置1表示IC被复位 */
    uint16 VDSI_UV  :1;  /* DSI欠压标志 */
    uint16 SPI_ERR  :1;  /* SPI错误标志 */
    uint16 Reserve1 :1;  /* 填充位        */
    uint16 OT       :1;  /* 过温标志       */
    uint16 VCC_UV   :1;  /* VCC欠压标志    */

#endif

}IRQ_STAT_str;

typedef union
{
    uint16 u16Data;
    
    SPI_IRQ_STAT_str SPI_IRQ_STAT;      /* SPI_IRQ_STAT_ERR标志 */
    IRQ_STAT_str     IRQ_STAT;          /* IRQ_STAT_ERR标志       */
    DSI_IRQ_STAT_str DSI_IRQ_STAT;      /* DSI_STAT ERR标志       */
    IC_STATUS_Str    IC_STATUS;         /* IC状态                 */
    
}E521_42WRRegData_un;

typedef enum
{
	NoSPIDataTrans = 0,
	WaitSPIDataTransDone,
	SPIDataTransSuccess,
	SPIDataTransFail,
	SPIDataTransStsNum
}SPIDataTransSts_en;
/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/

#define READ_MasterReg_CMD_Code      (uint8)0x80
#define WRITE_MasterReg_CMD_Code     (uint8)0xC0
#define READ_ICStatus_CMD_Code       (uint8)0x00
#define CRM_CMD_Code         	     (uint8)0x10
#define ReadCRMRes_CMD_Code          (uint8)0x20
#define DM_CMD_Code                  (uint8)0x40
#define UpLoadTDMA_CMD_Code          (uint8)0x50
#define AUTO_BRC_CMD_Code            (uint8)0x60
#define ReadPDCMRes_CMD_Code         (uint8)0x70

#define ReadRegInvalidValue  (uint16)0x0 /* 修改默认值0xFFFF变更为0x0，防止SPI短路误报探头故障 */
/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/

extern DSIReturnType_en TransWMasterDSIReg(DSIMasterID_en DSIMasterID,WRReg_en WRegCmdID,uint16 WData);
extern DSIReturnType_en TransRMasterDSIReg(DSIMasterID_en DSIMasterID,WRReg_en RRegCmdID,uint16 *pOutRegData);
extern DSIReturnType_en TransRMasterICStatus(DSIMasterID_en DSIMasterID,uint16 *pOutICStatus);
extern DSIReturnType_en TransUpload_DSI_TDMA_SCHEME(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL,FRAME_TDMA_SCHEME_str *pFRAME_TDMA_SCHEME);


#endif /* DSI_521_42 */
