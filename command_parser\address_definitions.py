"""
地址定义模块，存储所有地址的定义
"""

class BitField:
    """表示寄存器中的一个位域"""

    def __init__(self, name, msb, lsb, description=""):
        """
        初始化位域

        Args:
            name: 位域名称
            msb: 最高有效位
            lsb: 最低有效位
            description: 位域描述
        """
        self.name = name
        self.msb = msb
        self.lsb = lsb
        self.bits = msb - lsb + 1
        self.description = description

    def extract_value(self, data):
        """
        从数据中提取位域的值

        Args:
            data: 32位整数数据

        Returns:
            位域的值
        """
        mask = ((1 << self.bits) - 1) << self.lsb
        return (data & mask) >> self.lsb

    def set_value(self, data, value):
        """
        设置数据中位域的值

        Args:
            data: 32位整数数据
            value: 要设置的值

        Returns:
            更新后的数据
        """
        mask = ((1 << self.bits) - 1) << self.lsb
        return (data & ~mask) | ((value << self.lsb) & mask)


class Register:
    """表示一个寄存器，包含多个位域"""

    def __init__(self, address, name, description="", bit_fields=None):
        """
        初始化寄存器

        Args:
            address: 寄存器地址
            name: 寄存器名称
            description: 寄存器描述
            bit_fields: 位域列表
        """
        self.address = address
        self.name = name
        self.description = description
        # 添加16进制表示的地址
        self.address_hex = f"0x{address:02X}"
        self.bit_fields = bit_fields or []
        self.bit_fields_dict = {field.name: field for field in self.bit_fields}

    def add_bit_field(self, name, msb, lsb, description=""):
        """
        添加位域

        Args:
            name: 位域名称
            msb: 最高有效位
            lsb: 最低有效位
            description: 位域描述
        """
        bit_field = BitField(name, msb, lsb, description)
        self.bit_fields.append(bit_field)
        self.bit_fields_dict[name] = bit_field

    def get_bit_field(self, name):
        """
        获取位域

        Args:
            name: 位域名称

        Returns:
            位域对象
        """
        return self.bit_fields_dict.get(name)

    def parse_data(self, data):
        """
        解析数据，提取所有位域的值

        Args:
            data: 32位整数数据

        Returns:
            包含所有位域值的字典
        """
        result = {}
        # 添加16进制表示的完整数据值
        # result["_data_hex"] = f"0x{data:08X}"

        # 添加寄存器描述
        result["_register_info"] = {
            "name": self.name,
            "address": f"0x{self.address:02X}",
            "description": self.description or f"寄存器 {self.name} (地址: 0x{self.address:02X})"
        }

        # 添加位域描述字典
        result["_bit_fields_info"] = {}

        for field in self.bit_fields:
            value = field.extract_value(data)

            # 添加位域描述
            result["_bit_fields_info"][field.name] = {
                "msb": field.msb,
                "lsb": field.lsb,
                "bits": field.bits,
                "description": field.description
            }

            # 根据位域宽度决定16进制表示的格式
            if field.bits > 8:
                result[field.name] = {
                    "value": value,
                    "hex": f"0x{value:X}",
                    "description": field.description
                }
            elif field.bits > 4:
                result[field.name] = {
                    "value": value,
                    "hex": f"0x{value:02X}",
                    "description": field.description
                }
            elif field.bits > 1:
                result[field.name] = {
                    "value": value,
                    "hex": f"0x{value:X}",
                    "description": field.description
                }
            else:
                # 对于单比特位域，创建包含描述的字典
                result[field.name] = {
                    "value": value,
                    "hex": f"0x{value:01X}",
                    "description": field.description
                }
        return result


# 定义所有寄存器
REGISTERS = {}

# 初始化寄存器定义
def init_registers():
    """初始化所有寄存器定义"""
    # 寄存器0 - 时序控制寄存器
    reg0 = Register(0, "REG0", "时序控制寄存器，包含各种时序参数 | Timing control register, contains various timing parameters")
    reg0.add_bit_field("TCHIP", 30, 30, "芯片温度标志 | Chip temperature flag")
    reg0.add_bit_field("PDCM_RSP_START_TIME", 29, 24, "PDCM响应开始时间 | PDCM response start time")
    reg0.add_bit_field("CLKIN_DET", 22, 19, "时钟输入检测 | Clock input detection")
    reg0.add_bit_field("PDCM_RSP_FRM_LEN", 16, 12, "PDCM响应帧长度 | PDCM response frame length")
    reg0.add_bit_field("BRC_DELAYED", 11, 8, "BRC延迟 | BRC delayed")
    reg0.add_bit_field("CRM_END_TIME", 7, 0, "CRM结束时间 | CRM end time")
    REGISTERS[0] = reg0

    # 寄存器1 - 配置寄存器
    reg1 = Register(1, "REG1", "配置寄存器，包含各种配置参数 | Configuration register, contains various configuration parameters")
    reg1.add_bit_field("REV_ID", 31, 24, "修订版本ID | Revision ID")
    reg1.add_bit_field("VBSTOV_MASK", 23, 23, "VBST过压掩码 | VBST overvoltage mask")
    reg1.add_bit_field("SLAVE_RX_FILTER_ENA", 22, 21, "从接收过滤器使能 | Slave RX filter enable")
    reg1.add_bit_field("DATA_REF", 20, 20, "数据参考 | Data reference")
    reg1.add_bit_field("DATA_AMP", 19, 19, "数据放大 | Data amplification")
    reg1.add_bit_field("HW_INT_MASK", 18, 18, "硬件中断掩码 | Hardware interrupt mask")
    reg1.add_bit_field("CLKIN_FREQ", 17, 12, "时钟输入频率 | Clock input frequency")
    reg1.add_bit_field("CRM_RSP_START_TIME", 8, 0, "CRM响应开始时间 | CRM response start time")
    REGISTERS[1] = reg1

    # 寄存器2 - 控制寄存器
    reg2 = Register(2, "REG2", "控制寄存器，包含各种控制参数 | Control register, contains various control parameters")
    reg2.add_bit_field("TX_START", 31, 31, "发送启动 | Transmission start")
    reg2.add_bit_field("BRC_ENA", 30, 30, "BRC使能 | BRC enable")
    reg2.add_bit_field("BURST_ENA", 29, 29, "突发使能 | Burst enable")
    reg2.add_bit_field("BUS_DRV_ENA", 28, 28, "总线驱动使能 | Bus driver enable")
    reg2.add_bit_field("BRC_INT_MASK", 27, 20, "BRC中断掩码 | BRC interrupt mask")
    reg2.add_bit_field("CRM_INT_MASK", 19, 19, "CRM中断掩码 | CRM interrupt mask")
    reg2.add_bit_field("PDCM_NUM_SLOTS", 18, 16, "PDCM槽数量 | PDCM number of slots")
    reg2.add_bit_field("TIMER", 15, 7, "定时器 | Timer")
    reg2.add_bit_field("N_FRAMES", 3, 0, "帧数量 | Number of frames")
    REGISTERS[2] = reg2

    # 寄存器3-7 (TX_DATA0-TX_DATA4)
    for i in range(3, 8):
        reg = Register(i, f"TX_DATA{i-3}", f"发送数据寄存器{i-3} | Transmission data register {i-3}")
        reg.add_bit_field(f"TX_DATA{i-3}", 31, 0, f"发送数据{i-3} | Transmission data {i-3}")
        REGISTERS[i] = reg

    # 寄存器8 - 电源控制寄存器
    reg8 = Register(8, "REG8", "电源控制寄存器 | Power control register")
    reg8.add_bit_field("PSW_SLP", 31, 30, "电源睡眠模式 | Power sleep mode")
    reg8.add_bit_field("PWRENA_CHA", 15, 15, "通道A电源使能 | Power enable for channel A")
    reg8.add_bit_field("PWRENA_CHB", 7, 7, "通道B电源使能 | Power enable for channel B")
    REGISTERS[8] = reg8

    # 寄存器9 - 帧控制扩展寄存器
    reg9 = Register(9, "REG9", "帧控制扩展寄存器 | Frame control extension register")
    reg9.add_bit_field("N_FRAMES_MSB", 1, 0, "帧数量最高有效位 | Most significant bits of number of frames")
    REGISTERS[9] = reg9

    # 寄存器11 - 升压转换器控制寄存器
    reg11 = Register(11, "REG11", "升压转换器控制寄存器 | Boost converter control register")
    reg11.add_bit_field("BST_ENA", 31, 31, "升压转换器使能 | Boost converter enable")
    reg11.add_bit_field("VBST_UV_THR", 27, 20, "VBST欠压阈值 | VBST undervoltage threshold")
    reg11.add_bit_field("BST_FREQ", 19, 15, "升压转换器频率 | Boost converter frequency")
    reg11.add_bit_field("BST_VSETPOINT", 14, 8, "升压转换器电压设定点 | Boost converter voltage setpoint")
    reg11.add_bit_field("BST_VLIM_THR", 7, 0, "升压转换器电压限制阈值 | Boost converter voltage limit threshold")
    REGISTERS[11] = reg11

    # 寄存器12 - 升压转换器配置寄存器
    reg12 = Register(12, "REG12", "升压转换器配置寄存器 | Boost converter configuration register")
    reg12.add_bit_field("BST_OV_REACT", 29, 28, "升压转换器过压反应 | Boost converter overvoltage reaction")
    reg12.add_bit_field("BST_OV_SD", 26, 24, "升压转换器过压关断 | Boost converter overvoltage shutdown")
    reg12.add_bit_field("BST_SLP_CTRL", 22, 20, "升压转换器睡眠控制 | Boost converter sleep control")
    reg12.add_bit_field("BST_DRV_SLP", 19, 18, "升压转换器驱动睡眠 | Boost converter driver sleep")
    reg12.add_bit_field("BST_SKCL_THR", 17, 16, "升压转换器短路阈值 | Boost converter short circuit threshold")
    reg12.add_bit_field("BST_OTA_GAIN", 13, 12, "升压转换器OTA增益 | Boost converter OTA gain")
    reg12.add_bit_field("BST_MIN_TON", 9, 8, "升压转换器最小开启时间 | Boost converter minimum on time")
    reg12.add_bit_field("BST_MIN_TOFF", 6, 4, "升压转换器最小关闭时间 | Boost converter minimum off time")
    reg12.add_bit_field("BST_COMP_DIV", 2, 0, "升压转换器比较器分频 | Boost converter comparator divider")
    REGISTERS[12] = reg12

    # 寄存器14 - 升压转换器行为寄存器
    reg14 = Register(14, "REG14", "升压转换器行为寄存器 | Boost converter behavior register")
    reg14.add_bit_field("BST_SKCL_BEH", 26, 24, "升压转换器短路行为 | Boost converter short circuit behavior")
    reg14.add_bit_field("BST_SOFTSTART", 18, 16, "升压转换器软启动 | Boost converter soft start")
    reg14.add_bit_field("BSTSS_SEL", 9, 8, "升压转换器软启动选择 | Boost converter soft start selection")
    reg14.add_bit_field("BSTSS_PERIOD", 4, 4, "升压转换器软启动周期 | Boost converter soft start period")
    reg14.add_bit_field("BSTSS_PROFILE", 1, 0, "升压转换器软启动配置文件 | Boost converter soft start profile")
    REGISTERS[14] = reg14

    # 寄存器16 - 升压转换器状态寄存器
    reg16 = Register(16, "REG16", "升压转换器状态寄存器 | Boost converter status register")
    reg16.add_bit_field("BST_RUNNING", 18, 18, "升压转换器运行中 | Boost converter running")
    reg16.add_bit_field("BST_REGSTATUS", 17, 16, "升压转换器调节状态 | Boost converter regulation status")
    reg16.add_bit_field("BST_VDRV_UV", 4, 4, "升压转换器驱动电压欠压 | Boost converter driver voltage undervoltage")
    reg16.add_bit_field("BST_FBFAIL", 2, 2, "升压转换器反馈失败 | Boost converter feedback failure")
    reg16.add_bit_field("BST_OSCFAIL", 1, 1, "升压转换器振荡器失败 | Boost converter oscillator failure")
    reg16.add_bit_field("BST_SKCL", 0, 0, "升压转换器短路 | Boost converter short circuit")
    REGISTERS[16] = reg16

    # 寄存器17 - 硬件错误状态寄存器
    reg17 = Register(17, "REG17", "硬件错误状态寄存器，包含各种硬件错误标志")
    reg17.add_bit_field("HW_ERR", 31, 31, "硬件错误标志，表示存在任何硬件错误")
    reg17.add_bit_field("OVCPW2", 30, 30, "通道2过流保护警告标志")
    reg17.add_bit_field("OVCPW1", 29, 29, "通道1过流保护警告标志")
    reg17.add_bit_field("OVCR2T", 28, 28, "通道2顶部过流保护触发标志")
    reg17.add_bit_field("OVCR2B", 27, 27, "通道2底部过流保护触发标志")
    reg17.add_bit_field("OVCR1T", 26, 26, "通道1顶部过流保护触发标志")
    reg17.add_bit_field("OVCR1B", 25, 25, "通道1底部过流保护触发标志")
    reg17.add_bit_field("VDDAUV", 24, 24, "VDDA欠压标志")
    reg17.add_bit_field("VDRVOV", 23, 23, "VDRV过压标志")
    reg17.add_bit_field("VDRVUV", 22, 22, "VDRV欠压标志")
    reg17.add_bit_field("VDDA2OV", 21, 21, "VDDA2过压标志")
    reg17.add_bit_field("VDDA2UV", 20, 20, "VDDA2欠压标志")
    reg17.add_bit_field("VDDA1OV", 19, 19, "VDDA1过压标志")
    reg17.add_bit_field("VDDA1UV", 18, 18, "VDDA1欠压标志")
    reg17.add_bit_field("VDDOV", 17, 17, "VDD过压标志")
    reg17.add_bit_field("VDDUV", 16, 16, "VDD欠压标志")
    reg17.add_bit_field("VIOOV", 15, 15, "VIO过压标志")
    reg17.add_bit_field("VIOUV", 14, 14, "VIO欠压标志")
    reg17.add_bit_field("VBSTOV", 13, 13, "VBST过压标志")
    reg17.add_bit_field("VBSTUV", 12, 12, "VBST欠压标志")
    reg17.add_bit_field("TSD2", 11, 11, "温度关断2标志")
    reg17.add_bit_field("TSD1", 10, 10, "温度关断1标志")
    reg17.add_bit_field("BSTFAIL", 6, 6, "升压转换器失败标志")
    reg17.add_bit_field("TW", 5, 5, "温度警告标志")
    reg17.add_bit_field("TSD", 4, 4, "温度关断标志")
    reg17.add_bit_field("OSCLCK_FAIL", 3, 3, "振荡器锁定失败标志")
    reg17.add_bit_field("OSCFAIL", 2, 2, "振荡器失败标志")
    reg17.add_bit_field("GNDL", 1, 1, "接地丢失标志")
    reg17.add_bit_field("HWR", 0, 0, "硬件复位标志")
    REGISTERS[17] = reg17

    # 寄存器18 - 通信状态寄存器
    reg18 = Register(18, "REG18", "通信状态寄存器 | Communication status register")
    reg18.add_bit_field("CLKIN_CNT", 31, 23, "时钟输入计数 | Clock input count")
    reg18.add_bit_field("SYMBOL_ERR", 22, 22, "符号错误 | Symbol error")
    reg18.add_bit_field("FRAME_TO", 21, 21, "帧超时 | Frame timeout")
    reg18.add_bit_field("RX_TO", 20, 20, "接收超时 | Receive timeout")
    reg18.add_bit_field("HW_ERR_2", 19, 19, "硬件错误2 | Hardware error 2")
    reg18.add_bit_field("BURST_DONE", 18, 18, "突发完成 | Burst done")
    reg18.add_bit_field("TIMER_INT", 17, 17, "定时器中断 | Timer interrupt")
    reg18.add_bit_field("CRM_RCVD", 16, 16, "CRM接收 | CRM received")
    reg18.add_bit_field("BRC_RCVD", 15, 8, "BRC接收 | BRC received")
    reg18.add_bit_field("BUS_AVAIL", 0, 0, "总线可用 | Bus available")
    REGISTERS[18] = reg18

    # 寄存器19 - 电压监控寄存器1
    reg19 = Register(19, "REG19", "电压监控寄存器1 | Voltage monitoring register 1")
    reg19.add_bit_field("VBST", 28, 20, "升压电压 | Boost voltage")
    reg19.add_bit_field("VDD", 18, 10, "VDD电压 | VDD voltage")
    reg19.add_bit_field("VDRV", 8, 0, "驱动电压 | Driver voltage")
    REGISTERS[19] = reg19

    # 寄存器20 - 电压监控寄存器2
    reg20 = Register(20, "REG20", "电压监控寄存器2 | Voltage monitoring register 2")
    reg20.add_bit_field("VDATA1", 28, 20, "数据电压1 | Data voltage 1")
    reg20.add_bit_field("VDATA2", 18, 10, "数据电压2 | Data voltage 2")
    reg20.add_bit_field("VIO", 8, 0, "IO电压 | IO voltage")
    REGISTERS[20] = reg20

    # 寄存器21 - 温度和模拟电压监控寄存器
    reg21 = Register(21, "REG21", "温度和模拟电压监控寄存器 | Temperature and analog voltage monitoring register")
    reg21.add_bit_field("TEMP", 28, 20, "温度 | Temperature")
    reg21.add_bit_field("VDDA_D1", 18, 10, "模拟电压D1 | Analog voltage D1")
    reg21.add_bit_field("VDDA_D2", 8, 0, "模拟电压D2 | Analog voltage D2")
    REGISTERS[21] = reg21

    # 寄存器22 - 电流监控寄存器
    reg22 = Register(22, "REG22", "电流监控寄存器 | Current monitoring register")
    reg22.add_bit_field("IDATA1", 15, 8, "数据电流1 | Data current 1")
    reg22.add_bit_field("IDATA2", 7, 0, "数据电流2 | Data current 2")
    REGISTERS[22] = reg22

    # 寄存器23 - 设备ID寄存器1
    reg23 = Register(23, "REG23", "设备ID寄存器1 | Device ID register 1")
    reg23.add_bit_field("DEVID_FAB", 29, 28, "设备ID工厂 | Device ID factory")
    reg23.add_bit_field("DEVID_LOT", 27, 16, "设备ID批次 | Device ID lot")
    reg23.add_bit_field("DEVID_Y", 15, 8, "设备ID Y坐标 | Device ID Y coordinate")
    reg23.add_bit_field("DEVID_X", 7, 0, "设备ID X坐标 | Device ID X coordinate")
    REGISTERS[23] = reg23

    # 寄存器24 - 设备ID寄存器2
    reg24 = Register(24, "REG24", "设备ID寄存器2 | Device ID register 2")
    reg24.add_bit_field("DEVID_WAF", 15, 11, "设备ID晶圆 | Device ID wafer")
    reg24.add_bit_field("DEVID_YEAR", 10, 6, "设备ID年份 | Device ID year")
    reg24.add_bit_field("DEVID_WEEK", 5, 0, "设备ID周数 | Device ID week")
    REGISTERS[24] = reg24

    # 寄存器79 - 升压转换器错误清除寄存器
    reg79 = Register(79, "REG79", "升压转换器错误清除寄存器 | Boost converter error clear register")
    reg79.add_bit_field("CLR_BST_VDRV_UV", 4, 4, "清除升压转换器驱动电压欠压 | Clear boost converter driver voltage undervoltage")
    reg79.add_bit_field("CLR_BST_FBFAIL", 2, 2, "清除升压转换器反馈失败 | Clear boost converter feedback failure")
    reg79.add_bit_field("CLR_BST_OSCFAIL", 1, 1, "清除升压转换器振荡器失败 | Clear boost converter oscillator failure")
    reg79.add_bit_field("CLR_BST_SKCL", 0, 0, "清除升压转换器短路 | Clear boost converter short circuit")
    REGISTERS[79] = reg79

    # 寄存器80 - 硬件错误清除寄存器
    reg80 = Register(80, "REG80", "硬件错误清除寄存器 | Hardware error clear register")
    reg80.add_bit_field("CLR_OVCPW2", 30, 30, "清除通道2过流保护警告 | Clear channel 2 overcurrent protection warning")
    reg80.add_bit_field("CLR_OVCPW1", 29, 29, "清除通道1过流保护警告 | Clear channel 1 overcurrent protection warning")
    reg80.add_bit_field("CLR_OVCR2T", 28, 28, "清除通道2顶部过流保护触发 | Clear channel 2 top overcurrent protection trigger")
    reg80.add_bit_field("CLR_OVCR2B", 27, 27, "清除通道2底部过流保护触发 | Clear channel 2 bottom overcurrent protection trigger")
    reg80.add_bit_field("CLR_OVCR1T", 26, 26, "清除通道1顶部过流保护触发 | Clear channel 1 top overcurrent protection trigger")
    reg80.add_bit_field("CLR_OVCR1B", 25, 25, "清除通道1底部过流保护触发 | Clear channel 1 bottom overcurrent protection trigger")
    reg80.add_bit_field("CLR_VDDAUV", 24, 24, "清除VDDA欠压 | Clear VDDA undervoltage")
    reg80.add_bit_field("CLR_VDRVOV", 23, 23, "清除VDRV过压 | Clear VDRV overvoltage")
    reg80.add_bit_field("CLR_VDRVUV", 22, 22, "清除VDRV欠压 | Clear VDRV undervoltage")
    reg80.add_bit_field("CLR_VDDA2OV", 21, 21, "清除VDDA2过压 | Clear VDDA2 overvoltage")
    reg80.add_bit_field("CLR_VDDA2UV", 20, 20, "清除VDDA2欠压 | Clear VDDA2 undervoltage")
    reg80.add_bit_field("CLR_VDDA1OV", 19, 19, "清除VDDA1过压 | Clear VDDA1 overvoltage")
    reg80.add_bit_field("CLR_VDDA1UV", 18, 18, "清除VDDA1欠压 | Clear VDDA1 undervoltage")
    reg80.add_bit_field("CLR_VDDOV", 17, 17, "清除VDD过压 | Clear VDD overvoltage")
    reg80.add_bit_field("CLR_VDDUV", 16, 16, "清除VDD欠压 | Clear VDD undervoltage")
    reg80.add_bit_field("CLR_VIOOV", 15, 15, "清除VIO过压 | Clear VIO overvoltage")
    reg80.add_bit_field("CLR_VIOUV", 14, 14, "清除VIO欠压 | Clear VIO undervoltage")
    reg80.add_bit_field("CLR_VBSTOV", 13, 13, "清除VBST过压 | Clear VBST overvoltage")
    reg80.add_bit_field("CLR_VBSTUV", 12, 12, "清除VBST欠压 | Clear VBST undervoltage")
    reg80.add_bit_field("CLR_TSD2", 11, 11, "清除温度关断2 | Clear temperature shutdown 2")
    reg80.add_bit_field("CLR_TSD1", 10, 10, "清除温度关断1 | Clear temperature shutdown 1")
    reg80.add_bit_field("CLR_TW", 5, 5, "清除温度警告 | Clear temperature warning")
    reg80.add_bit_field("CLR_TSD", 4, 4, "清除温度关断 | Clear temperature shutdown")
    reg80.add_bit_field("CLR_OSCLCK_FAIL", 3, 3, "清除振荡器锁定失败 | Clear oscillator lock failure")
    reg80.add_bit_field("CLR_OSCFAIL", 2, 2, "清除振荡器失败 | Clear oscillator failure")
    reg80.add_bit_field("CLR_GNDL", 1, 1, "清除接地丢失 | Clear ground loss")
    reg80.add_bit_field("CLR_HWR", 0, 0, "清除硬件复位 | Clear hardware reset")
    REGISTERS[80] = reg80

    # 寄存器81 - 通信错误清除寄存器
    reg81 = Register(81, "REG81", "通信错误清除寄存器 | Communication error clear register")
    reg81.add_bit_field("CLR_SYMBOL_ERR", 22, 22, "清除符号错误 | Clear symbol error")
    reg81.add_bit_field("CLR_FRAME_TO", 21, 21, "清除帧超时 | Clear frame timeout")
    reg81.add_bit_field("CLR_RX_TO", 20, 20, "清除接收超时 | Clear receive timeout")
    reg81.add_bit_field("CLR_BURST_DONE", 18, 18, "清除突发完成 | Clear burst done")
    reg81.add_bit_field("CLR_CRM_RCVD", 16, 16, "清除CRM接收 | Clear CRM received")
    REGISTERS[81] = reg81

# 初始化寄存器
init_registers()

def get_register(address):
    """
    获取指定地址的寄存器

    Args:
        address: 寄存器地址

    Returns:
        寄存器对象，如果不存在则返回None
    """
    return REGISTERS.get(address)
