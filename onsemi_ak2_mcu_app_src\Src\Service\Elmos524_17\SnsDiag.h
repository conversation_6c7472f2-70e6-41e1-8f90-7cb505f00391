/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * SnsDiag.h:
 * Created on: 2021-12-27 15:04
 * Original designer:
 ******************************************************************************/
#ifndef __SNSDIAG_H__
#define __SNSDIAG_H__

/* Includes ------------------------------------------------------------------*/
#include "Elmos_524_17.h"

#define PRINTF_SNSDIAG                          SNSDIAG_DEBUG_PRINT

/***************************************************************************** */
/***************************************************************************** */
/****************************** Type Definitions ***************************** */
/***************************************************************************** */
/***************************************************************************** */
/******************************************************************************/
/**
* @brief: 定义错误码类型 该故障码互斥 不可同时存在
**/
/******************************************************************************/
typedef enum
{
    SnsFaultNone = 0,
	PwrPinShortToGnd,
	PwrPinShortToPwr,	
	DSIShortToGnd,
	DSIShortToPwr,
	SnsSignalInvalid,
    SnsNoSignal,
    SnsBlindness,
    SnsInternalFailure,
	SnsFaultNum
}SnsFaultType_e; 

typedef struct
{
    bool    eErrFlag;
    SnsFaultType_e eErrType;
}SnsErrInfo_t;

typedef enum
{
	SnsReDM = 0,           /*需重新寻址*/
	SnsReadStatus,         /*获取探头故障状态*/
	SnsSelfTest,		   /*启动探头自检*/
	SnsReWrParam,		   /*启动探头自检*/
	SnsErrHandleNone
}SnsErrHandle_e;

typedef struct
{
	uint16 SnsErrMask;
	SnsErrHandle_e SnsErrHandle;
}SnsErrCtrl_t;

typedef enum
{
	TransducerErrNone = 0,
	TransducerPosAndNegShort,	    /*探芯正负极短路*/
	TransducerPosOpen,   			/*探芯正极开路*/
	TransducerNegOpen,         		/*探芯负极开路*/
	TransducerPosAndNegOpen		    /*探芯正负极开路*/
}TransducerErrType_e;

typedef struct
{
	SnsID_en eSnsIdx1;
	SnsID_en eSnsIdx2;	
	SnsID_en eSnsIdx3;
	uint16 u16RingTime1;
	uint16 u16RingTime2;
	uint16 u16RingTime3;
	uint16 u16RingFre1;
	uint16 u16RingFre2;
	uint16 u16RingFre3;
	uint8 u8ImpedanceIdx1;
	uint8 u8ImpedanceIdx2;
	uint8 u8ImpedanceIdx3;	
}SnsMonitorDataType_t;

/***************************************************************************** */
/***************************************************************************** */
/****************************** Macro Definitions **************************** */
/***************************************************************************** */
/***************************************************************************** */
#define SAMPLE_FREQUENCY          (48000) /*uint:Hz*/

/***************************************************************************** */
/***************************************************************************** */
/***************************** Symbol Definitions **************************** */
/***************************************************************************** */
/***************************************************************************** */

/***************************************************************************** */
/***************************************************************************** */
/*************************** Constants Declaration *************************** */
/***************************************************************************** */
/***************************************************************************** */


/***************************************************************************** */
/***************************************************************************** */
/*************************** Variables Declaration *************************** */
/***************************************************************************** */
/***************************************************************************** */
extern uint8 EepromErrFlg[SNSNum];


/***************************************************************************** */
/***************************************************************************** */
/*************************** Functions Declaration *************************** */
/***************************************************************************** */
/***************************************************************************** */
extern void SnsDiag_Init(void);
extern void SnsDiag_SnsSelfTestResultHandle(void);
extern uint8 SnsDiag_SnsDiagHandle(SnsID_en SnsIDIdx);
extern void SnsDiag_SnsIdleDiagHandle(void);
extern void SnsDiag_ReadSnsErrSignal(SnsErrInfo_t* LpSnsErr,SnsID_en LeSnsID);
extern bool SnsDiag_ReadFrontSnsGroupErrSts(void);
extern bool SnsDiag_ReadRearSnsGroupErrSts(void);
extern bool SnsDiag_ReadSysSnsGroupErrSts(void);
extern SnsErrCtrl_t* SnsDiag_GetSnsErrCtrl(void);
extern void SnsDiag_ClearSnsErrCtrl(void);
extern void SnsDiag_ClearEepromErrFlg(SnsID_en SnsIDIdx);
extern uint8 SnsDiag_InterFail_Event1_Num(void);
extern uint8 SnsDiag_InterFail_Event2_Num(void);
extern uint8 SnsDiag_InterFail_Event3_Num(void);
extern uint8 SnsDiag_InterFail_Event4_Num(void);
extern uint16 CalcRingFreq(uint16 SampleFreq,Sns_PDCM_RingTimeData *pRTData);
extern void SnsDiag_SnsMonitorData(SnsMonitorDataType_t *pstrOut);
extern uint8 SnsDiag_VolAbnormaFlg(void);

#endif /** @brief  __SNSDIAG_H__  */

