/******************************************************************************
 * @file       SnsPPCalculate_Int.h
 * @brief      
 * @date       2025-03-04 14:40:09
 * <AUTHOR>
 * @copyright  Longhorn Automotive Electronics Technology (Xiamen) Co., Ltd.
 *****************************************************************************/


#ifndef SnsPPCalculate_Int_H
#define SnsPPCalculate_Int_H
/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "SnsPPCalculate_Type.h"



/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/



/******************************************************************************
 * @Const Declaration
 *****************************************************************************/



/******************************************************************************
 * @Variable Declaration
 *****************************************************************************/
extern PPDis_ZONETye GstrPPZone[PDC_SNS_GROUP_NUM];
extern SysDisInfoType GstrSysDisInfo[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];



/******************************************************************************
 * @Function Declaration
 *****************************************************************************/
void PPDataPowerOnInit(void);
void SnsCalObjDistance(PDCSnsGroupType LenuSnsGroup, PDCSnsChannelType LenuSnsCh);



#endif
