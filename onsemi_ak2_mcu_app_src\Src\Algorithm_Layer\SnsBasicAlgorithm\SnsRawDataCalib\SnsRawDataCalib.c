/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * SnsRawDataCalib: 
 * Created on: 2023-02-27 14:28
 * Original designer: AntonyFang
 ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "SnsRawDataCalib.h"


/******************************************************************************/
/******************************************************************************/
/****************************** Public Definition *****************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************前雷达ECU端定频阈值筛选*****************************************************  */
/* 定频情况下，FLS和FRS阈值表，用于筛选有效障碍物回波，定频模式修改为14bit后，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16FLS_FRS_MasStandardThresholdTable[SNS_DIS_TOTAL_NUM] = 
{
#if DEMO_THRESHOLD
    5000u,    /* (0,10cm]  */
    5000u,    /* (10,20cm] */
    4000u,    /* (20,30cm] */
    3000u,    /* (30,40cm] */
    2500u,    /* (40,50cm] */
#else
    50000u,   /* (0,10cm]  */
    35000u,   /* (10,20cm] */
    10000u,   /* (20,30cm] */
    6000u,    /* (30,40cm] */
    2500u,    /* (40,50cm] */
#endif

    2000u,    /* (50,60cm] */
    1800u,    /* (60,70cm] */
    1600u,    /* (70,80cm] */
    1400u,    /* (80,90cm] */
    1200u,    /* (90,100cm]*/

    1100u,   /* (100,110cm] */
    1000u,   /* (110,120cm] */
    900u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    650u,    /* (170,180cm] */
    620u,    /* (180,190cm] */
    600u,    /* (190,200cm] */

    600u,    /* (200,210cm] */
    600u,    /* (210,220cm] */
    550u,    /* (220,230cm] */
    550u,    /* (230,240cm] */
    500u,    /* (240,250cm] */

    500u,    /* (250,260cm] */
    500u,    /* (260,270cm] */
    500u,    /* (270,280cm] */
    500u,    /* (280,290cm] */
    500u,    /* (290,300cm] */

    500u,    /* (300,310cm] */
    500u,    /* (310,320cm] */
    500u,    /* (320,330cm] */
    500u,    /* (330,340cm] */
    500u,    /* (340,350cm] */

    500u,    /* (350,360cm] */
    500u,    /* (360,370cm] */
    500u,    /* (370,380cm] */
    500u,    /* (380,390cm] */
    500u,    /* (390,400cm] */
};

/* 扫频情况下，FLS和FRS阈值表，用于筛选有效障碍物回波，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16FLS_FRS_MasChirpThresholdTable[SNS_DIS_TOTAL_NUM] = 
{
#if DEMO_THRESHOLD
    65535u,    /* (0,10cm]  */
    65535u,    /* (10,20cm] */
    20000u,    /* (20,30cm] */
    12000u,    /* (30,40cm] */
    2000u,     /* (40,50cm] */
#else
    65535u,    /* (0,10cm]  */
    65535u,    /* (10,20cm] */
    22000u,    /* (20,30cm] */
    13000u,    /* (30,40cm] */
    5000u,     /* (40,50cm] */
#endif

    2000u,     /* (50,60cm] */
    1500u,     /* (60,70cm] */
    1500u,     /* (70,80cm] */
    1300u,     /* (80,90cm] */
    1000u,     /* (90,100cm]*/

    900u,    /* (100,110cm] */
    900u,    /* (110,120cm] */
    850u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    600u,    /* (170,180cm] */
    550u,    /* (180,190cm] */
    500u,    /* (190,200cm] */

    450u,    /* (200,210cm] */
    400u,    /* (210,220cm] */
    350u,    /* (220,230cm] */
    330u,    /* (230,240cm] */
    300u,    /* (240,250cm] */

    250u,    /* (250,260cm] */
    220u,    /* (260,270cm] */
    200u,    /* (270,280cm] */
    200u,    /* (280,290cm] */
    200u,    /* (290,300cm] */

    180u,    /* (300,310cm] */
    170u,    /* (310,320cm] */
    160u,    /* (320,330cm] */
    150u,    /* (330,340cm] */
    150u,    /* (340,350cm] */

    150u,    /* (350,360cm] */
    140u,    /* (360,370cm] */
    140u,    /* (370,380cm] */
    140u,    /* (380,390cm] */
    140u,    /* (390,400cm] */
};

/* 定频情况下，FL和FR阈值表，用于筛选有效障碍物回波，定频模式修改为14bit后，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16FL_FR_MasStandardThresholdTable[SNS_DIS_TOTAL_NUM] = 
{
#if DEMO_THRESHOLD
    5000u,    /* (0,10cm]  */
    5000u,    /* (10,20cm] */
    4000u,    /* (20,30cm] */
    3000u,    /* (30,40cm] */
    2500u,    /* (40,50cm] */
#else
    50000u,   /* (0,10cm]  */
    35000u,   /* (10,20cm] */
    10000u,   /* (20,30cm] */
    6000u,    /* (30,40cm] */
    2500u,    /* (40,50cm] */
#endif

    2000u,     /* (50,60cm] */
    1800u,     /* (60,70cm] */
    1600u,     /* (70,80cm] */
    1400u,     /* (80,90cm] */
    1200u,     /* (90,100cm]*/

    1100u,   /* (100,110cm] */
    1000u,   /* (110,120cm] */
    900u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    650u,    /* (170,180cm] */
    620u,    /* (180,190cm] */
    600u,    /* (190,200cm] */

    600u,    /* (200,210cm] */
    600u,    /* (210,220cm] */
    550u,    /* (220,230cm] */
    550u,    /* (230,240cm] */
    500u,    /* (240,250cm] */

    500u,    /* (250,260cm] */
    500u,    /* (260,270cm] */
    500u,    /* (270,280cm] */
    500u,    /* (280,290cm] */
    500u,    /* (290,300cm] */

    500u,    /* (300,310cm] */
    500u,    /* (310,320cm] */
    500u,    /* (320,330cm] */
    500u,    /* (330,340cm] */
    500u,    /* (340,350cm] */

    500u,    /* (350,360cm] */
    500u,    /* (360,370cm] */
    500u,    /* (370,380cm] */
    500u,    /* (380,390cm] */
    500u,    /* (390,400cm] */
};

/* 扫频情况下，FL和FR阈值表，用于筛选有效障碍物回波，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16FL_FR_MasChirpThresholdTable[SNS_DIS_TOTAL_NUM] = 
{
#if DEMO_THRESHOLD
    65535u,    /* (0,10cm]  */
    65535u,    /* (10,20cm] */
    20000u,    /* (20,30cm] */
    12000u,    /* (30,40cm] */
    2000u,     /* (40,50cm] */
#else
    65535u,    /* (0,10cm]  */
    65535u,    /* (10,20cm] */
    22000u,    /* (20,30cm] */
    13000u,    /* (30,40cm] */
    5000u,     /* (40,50cm] */
#endif

    2000u,     /* (50,60cm] */
    1500u,     /* (60,70cm] */
    1500u,     /* (70,80cm] */
    1300u,     /* (80,90cm] */
    1000u,     /* (90,100cm]*/

    900u,    /* (100,110cm] */
    900u,    /* (110,120cm] */
    850u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    600u,    /* (170,180cm] */
    550u,    /* (180,190cm] */
    500u,    /* (190,200cm] */

    450u,    /* (200,210cm] */
    400u,    /* (210,220cm] */
    350u,    /* (220,230cm] */
    330u,    /* (230,240cm] */
    300u,    /* (240,250cm] */

    250u,    /* (250,260cm] */
    220u,    /* (260,270cm] */
    200u,    /* (270,280cm] */
    200u,    /* (280,290cm] */
    200u,    /* (290,300cm] */

    180u,    /* (300,310cm] */
    170u,    /* (310,320cm] */
    160u,    /* (320,330cm] */
    150u,    /* (330,340cm] */
    150u,    /* (340,350cm] */

    150u,    /* (350,360cm] */
    140u,    /* (360,370cm] */
    140u,    /* (370,380cm] */
    140u,    /* (380,390cm] */
    140u,    /* (390,400cm] */
};

/* 定频情况下，FML和FMR阈值表，用于筛选有效障碍物回波，定频模式修改为14bit后，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16FML_FMR_MasStandardThresholdTable[SNS_DIS_TOTAL_NUM] = 
{
#if DEMO_THRESHOLD
    5000u,    /* (0,10cm]  */
    5000u,    /* (10,20cm] */
    4000u,    /* (20,30cm] */
    3000u,    /* (30,40cm] */
    2500u,    /* (40,50cm] */
#else
    50000u,   /* (0,10cm]  */
    35000u,   /* (10,20cm] */
    10000u,   /* (20,30cm] */
    6000u,    /* (30,40cm] */
    2500u,    /* (40,50cm] */
#endif

    2000u,     /* (50,60cm] */
    1800u,     /* (60,70cm] */
    1600u,     /* (70,80cm] */
    1400u,     /* (80,90cm] */
    1200u,     /* (90,100cm]*/

    1100u,   /* (100,110cm] */
    1000u,   /* (110,120cm] */
    900u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    650u,    /* (170,180cm] */
    620u,    /* (180,190cm] */
    600u,    /* (190,200cm] */

    600u,    /* (200,210cm] */
    600u,    /* (210,220cm] */
    550u,    /* (220,230cm] */
    550u,    /* (230,240cm] */
    500u,    /* (240,250cm] */

    500u,    /* (250,260cm] */
    500u,    /* (260,270cm] */
    500u,    /* (270,280cm] */
    500u,    /* (280,290cm] */
    500u,    /* (290,300cm] */

    500u,    /* (300,310cm] */
    500u,    /* (310,320cm] */
    500u,    /* (320,330cm] */
    500u,    /* (330,340cm] */
    500u,    /* (340,350cm] */

    500u,    /* (350,360cm] */
    500u,    /* (360,370cm] */
    500u,    /* (370,380cm] */
    500u,    /* (380,390cm] */
    500u     /* (390,400cm] */
};

/* 扫频情况下，FML和FMR阈值表，用于筛选有效障碍物回波，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16FML_FMR_MasChirpThresholdTable[SNS_DIS_TOTAL_NUM] = 
{
#if DEMO_THRESHOLD
    65535u,    /* (0,10cm]  */
    65535u,    /* (10,20cm] */
    20000u,    /* (20,30cm] */
    12000u,    /* (30,40cm] */
    2000u,     /* (40,50cm] */
#else
    65535u,    /* (0,10cm]  */
    65535u,    /* (10,20cm] */
    22000u,    /* (20,30cm] */
    13000u,    /* (30,40cm] */
    5000u,     /* (40,50cm] */
#endif

    2000u,     /* (50,60cm] */
    1500u,     /* (60,70cm] */
    1500u,     /* (70,80cm] */
    1300u,     /* (80,90cm] */
    1000u,     /* (90,100cm]*/

    900u,    /* (100,110cm] */
    900u,    /* (110,120cm] */
    850u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    600u,    /* (170,180cm] */
    550u,    /* (180,190cm] */
    500u,    /* (190,200cm] */

    450u,    /* (200,210cm] */
    400u,    /* (210,220cm] */
    350u,    /* (220,230cm] */
    330u,    /* (230,240cm] */
    300u,    /* (240,250cm] */

    250u,    /* (250,260cm] */
    220u,    /* (260,270cm] */
    200u,    /* (270,280cm] */
    200u,    /* (280,290cm] */
    200u,    /* (290,300cm] */

    180u,    /* (300,310cm] */
    170u,    /* (310,320cm] */
    160u,    /* (320,330cm] */
    150u,    /* (330,340cm] */
    150u,    /* (340,350cm] */

    150u,    /* (350,360cm] */
    140u,    /* (360,370cm] */
    140u,    /* (370,380cm] */
    140u,    /* (380,390cm] */
    140u,    /* (390,400cm] */
};

/* 定频情况下，RLS和RRS阈值表，用于筛选有效障碍物回波，定频模式修改为14bit后，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16RLS_RRS_MasStandardThresholdTable[SNS_DIS_TOTAL_NUM] = 
{
#if DEMO_THRESHOLD
    5000u,    /* (0,10cm]  */
    5000u,    /* (10,20cm] */
    4000u,    /* (20,30cm] */
    3000u,    /* (30,40cm] */
    2500u,    /* (40,50cm] */
#else
    50000u,   /* (0,10cm]  */
    35000u,   /* (10,20cm] */
    10000u,   /* (20,30cm] */
    6000u,    /* (30,40cm] */
    2500u,    /* (40,50cm] */
#endif

    2000u,     /* (50,60cm] */
    1800u,     /* (60,70cm] */
    1600u,     /* (70,80cm] */
    1400u,     /* (80,90cm] */
    1200u,     /* (90,100cm]*/

    1100u,   /* (100,110cm] */
    1000u,   /* (110,120cm] */
    900u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    650u,    /* (170,180cm] */
    620u,    /* (180,190cm] */
    600u,    /* (190,200cm] */

    600u,    /* (200,210cm] */
    600u,    /* (210,220cm] */
    550u,    /* (220,230cm] */
    550u,    /* (230,240cm] */
    500u,    /* (240,250cm] */

    500u,    /* (250,260cm] */
    500u,    /* (260,270cm] */
    500u,    /* (270,280cm] */
    500u,    /* (280,290cm] */
    500u,    /* (290,300cm] */

    500u,    /* (300,310cm] */
    500u,    /* (310,320cm] */
    500u,    /* (320,330cm] */
    500u,    /* (330,340cm] */
    500u,    /* (340,350cm] */

    500u,    /* (350,360cm] */
    500u,    /* (360,370cm] */
    500u,    /* (370,380cm] */
    500u,    /* (380,390cm] */
    500u,    /* (390,400cm] */
};

/* 扫频情况下，RLS和RRS阈值表，用于筛选有效障碍物回波，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16RLS_RRS_MasChirpThresholdTable[SNS_DIS_TOTAL_NUM] = 
{
#if DEMO_THRESHOLD
    65535u,    /* (0,10cm]  */
    65535u,    /* (10,20cm] */
    20000u,    /* (20,30cm] */
    12000u,    /* (30,40cm] */
    2000u,     /* (40,50cm] */
#else
    65535u,    /* (0,10cm]  */
    65535u,    /* (10,20cm] */
    22000u,    /* (20,30cm] */
    13000u,    /* (30,40cm] */
    5000u,     /* (40,50cm] */
#endif

    2000u,     /* (50,60cm] */
    1500u,     /* (60,70cm] */
    1500u,     /* (70,80cm] */
    1300u,     /* (80,90cm] */
    1000u,     /* (90,100cm]*/

    900u,    /* (100,110cm] */
    900u,    /* (110,120cm] */
    850u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    600u,    /* (170,180cm] */
    550u,    /* (180,190cm] */
    500u,    /* (190,200cm] */

    450u,    /* (200,210cm] */
    400u,    /* (210,220cm] */
    350u,    /* (220,230cm] */
    330u,    /* (230,240cm] */
    300u,    /* (240,250cm] */

    250u,    /* (250,260cm] */
    220u,    /* (260,270cm] */
    200u,    /* (270,280cm] */
    200u,    /* (280,290cm] */
    200u,    /* (290,300cm] */

    180u,    /* (300,310cm] */
    170u,    /* (310,320cm] */
    160u,    /* (320,330cm] */
    150u,    /* (330,340cm] */
    150u,    /* (340,350cm] */

    150u,    /* (350,360cm] */
    140u,    /* (360,370cm] */
    140u,    /* (370,380cm] */
    140u,    /* (380,390cm] */
    140u,    /* (390,400cm] */
};

/* 定频情况下，RL和RR阈值表，用于筛选有效障碍物回波，定频模式修改为14bit后，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16RL_RR_MasStandardThresholdTable[SNS_DIS_TOTAL_NUM] = 
{
#if DEMO_THRESHOLD
    5000u,    /* (0,10cm]  */
    5000u,    /* (10,20cm] */
    4000u,    /* (20,30cm] */
    3000u,    /* (30,40cm] */
    2500u,    /* (40,50cm] */
#else
    50000u,   /* (0,10cm]  */
    35000u,   /* (10,20cm] */
    10000u,   /* (20,30cm] */
    6000u,    /* (30,40cm] */
    2500u,    /* (40,50cm] */
#endif

    2000u,     /* (50,60cm] */
    1800u,     /* (60,70cm] */
    1600u,     /* (70,80cm] */
    1400u,     /* (80,90cm] */
    1200u,     /* (90,100cm]*/

    1100u,   /* (100,110cm] */
    1000u,   /* (110,120cm] */
    900u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    650u,    /* (170,180cm] */
    620u,    /* (180,190cm] */
    600u,    /* (190,200cm] */

    600u,    /* (200,210cm] */
    600u,    /* (210,220cm] */
    550u,    /* (220,230cm] */
    550u,    /* (230,240cm] */
    500u,    /* (240,250cm] */

    500u,    /* (250,260cm] */
    500u,    /* (260,270cm] */
    500u,    /* (270,280cm] */
    500u,    /* (280,290cm] */
    500u,    /* (290,300cm] */

    500u,    /* (300,310cm] */
    500u,    /* (310,320cm] */
    500u,    /* (320,330cm] */
    500u,    /* (330,340cm] */
    500u,    /* (340,350cm] */

    500u,    /* (350,360cm] */
    500u,    /* (360,370cm] */
    500u,    /* (370,380cm] */
    500u,    /* (380,390cm] */
    500u,    /* (390,400cm] */
};

/* 扫频情况下，RL和RR阈值表，用于筛选有效障碍物回波，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16RL_RR_MasChirpThresholdTable[SNS_DIS_TOTAL_NUM] = 
{
#if DEMO_THRESHOLD
    65535u,    /* (0,10cm]  */
    65535u,    /* (10,20cm] */
    20000u,    /* (20,30cm] */
    12000u,    /* (30,40cm] */
    2000u,     /* (40,50cm] */
#else
    65535u,    /* (0,10cm]  */
    65535u,    /* (10,20cm] */
    22000u,    /* (20,30cm] */
    13000u,    /* (30,40cm] */
    5000u,     /* (40,50cm] */
#endif

    2000u,     /* (50,60cm] */
    1500u,     /* (60,70cm] */
    1500u,     /* (70,80cm] */
    1300u,     /* (80,90cm] */
    1000u,     /* (90,100cm]*/

    900u,    /* (100,110cm] */
    900u,    /* (110,120cm] */
    850u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    600u,    /* (170,180cm] */
    550u,    /* (180,190cm] */
    500u,    /* (190,200cm] */

    450u,    /* (200,210cm] */
    400u,    /* (210,220cm] */
    350u,    /* (220,230cm] */
    330u,    /* (230,240cm] */
    300u,    /* (240,250cm] */

    250u,    /* (250,260cm] */
    220u,    /* (260,270cm] */
    200u,    /* (270,280cm] */
    200u,    /* (280,290cm] */
    200u,    /* (290,300cm] */

    180u,    /* (300,310cm] */
    170u,    /* (310,320cm] */
    160u,    /* (320,330cm] */
    150u,    /* (330,340cm] */
    150u,    /* (340,350cm] */

    150u,    /* (350,360cm] */
    140u,    /* (360,370cm] */
    140u,    /* (370,380cm] */
    140u,    /* (380,390cm] */
    140u,    /* (390,400cm] */
};

/* 定频情况下，RML和RMR阈值表，用于筛选有效障碍物回波，定频模式修改为14bit后，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16RML_RMR_MasStandardThresholdTable[SNS_DIS_TOTAL_NUM] = 
{
#if DEMO_THRESHOLD
    5000u,    /* (0,10cm]  */
    5000u,    /* (10,20cm] */
    4000u,    /* (20,30cm] */
    3000u,    /* (30,40cm] */
    2500u,    /* (40,50cm] */
#else
    50000u,   /* (0,10cm]  */
    35000u,   /* (10,20cm] */
    10000u,   /* (20,30cm] */
    6000u,    /* (30,40cm] */
    2500u,    /* (40,50cm] */
#endif

    2000u,     /* (50,60cm] */
    1800u,     /* (60,70cm] */
    1600u,     /* (70,80cm] */
    1400u,     /* (80,90cm] */
    1200u,     /* (90,100cm]*/

    1100u,   /* (100,110cm] */
    1000u,   /* (110,120cm] */
    900u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    650u,    /* (170,180cm] */
    620u,    /* (180,190cm] */
    600u,    /* (190,200cm] */

    600u,    /* (200,210cm] */
    600u,    /* (210,220cm] */
    550u,    /* (220,230cm] */
    550u,    /* (230,240cm] */
    500u,    /* (240,250cm] */

    500u,    /* (250,260cm] */
    500u,    /* (260,270cm] */
    500u,    /* (270,280cm] */
    500u,    /* (280,290cm] */
    500u,    /* (290,300cm] */

    500u,    /* (300,310cm] */
    500u,    /* (310,320cm] */
    500u,    /* (320,330cm] */
    500u,    /* (330,340cm] */
    500u,    /* (340,350cm] */

    500u,    /* (350,360cm] */
    500u,    /* (360,370cm] */
    500u,    /* (370,380cm] */
    500u,    /* (380,390cm] */
    500u,    /* (390,400cm] */
};

/* 扫频情况下，RML和RMR阈值表，用于筛选有效障碍物回波，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16RML_RMR_MasChirpThresholdTable[SNS_DIS_TOTAL_NUM] = 
{
#if DEMO_THRESHOLD
    65535u,    /* (0,10cm]  */
    65535u,    /* (10,20cm] */
    20000u,    /* (20,30cm] */
    12000u,    /* (30,40cm] */
    2000u,     /* (40,50cm] */
#else
    65535u,    /* (0,10cm]  */
    65535u,    /* (10,20cm] */
    22000u,    /* (20,30cm] */
    13000u,    /* (30,40cm] */
    5000u,     /* (40,50cm] */
#endif

    2000u,     /* (50,60cm] */
    1500u,     /* (60,70cm] */
    1500u,     /* (70,80cm] */
    1300u,     /* (80,90cm] */
    1000u,     /* (90,100cm]*/

    900u,    /* (100,110cm] */
    900u,    /* (110,120cm] */
    850u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    600u,    /* (170,180cm] */
    550u,    /* (180,190cm] */
    500u,    /* (190,200cm] */

    450u,    /* (200,210cm] */
    400u,    /* (210,220cm] */
    350u,    /* (220,230cm] */
    330u,    /* (230,240cm] */
    300u,    /* (240,250cm] */

    250u,    /* (250,260cm] */
    220u,    /* (260,270cm] */
    200u,    /* (270,280cm] */
    200u,    /* (280,290cm] */
    200u,    /* (290,300cm] */

    180u,    /* (300,310cm] */
    170u,    /* (310,320cm] */
    160u,    /* (320,330cm] */
    150u,    /* (330,340cm] */
    150u,    /* (340,350cm] */

    150u,    /* (350,360cm] */
    140u,    /* (360,370cm] */
    140u,    /* (370,380cm] */
    140u,    /* (380,390cm] */
    140u,    /* (390,400cm] */
};

/************************************************ 侦听阈值表 ************************************************/
const uint16 Gu16FLS_FRS_LisStandardThresholdTable[SNS_DIS_TOTAL_NUM] = 
{
    50000u,   /* (0,10cm]  */
    35000u,   /* (10,20cm] */
    10000u,   /* (20,30cm] */
    6000u,    /* (30,40cm] */
    2500u,    /* (40,50cm] */

    2000u,     /* (50,60cm] */
    1800u,     /* (60,70cm] */
    1600u,     /* (70,80cm] */
    1400u,     /* (80,90cm] */
    1200u,     /* (90,100cm]*/

    1100u,   /* (100,110cm] */
    1000u,   /* (110,120cm] */
    900u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    650u,    /* (170,180cm] */
    620u,    /* (180,190cm] */
    600u,    /* (190,200cm] */

    600u,    /* (200,210cm] */
    600u,    /* (210,220cm] */
    550u,    /* (220,230cm] */
    550u,    /* (230,240cm] */
    500u,    /* (240,250cm] */

    500u,    /* (250,260cm] */
    500u,    /* (260,270cm] */
    500u,    /* (270,280cm] */
    500u,    /* (280,290cm] */
    500u,    /* (290,300cm] */

    500u,    /* (300,310cm] */
    500u,    /* (310,320cm] */
    500u,    /* (320,330cm] */
    500u,    /* (330,340cm] */
    500u,    /* (340,350cm] */

    500u,    /* (350,360cm] */
    500u,    /* (360,370cm] */
    500u,    /* (370,380cm] */
    500u,    /* (380,390cm] */
    500u,    /* (390,400cm] */
};

const uint16 Gu16FLS_FRS_LisChirpThresholdTable[SNS_DIS_TOTAL_NUM] = 
{
    65535u,    /* (0,10cm]  */
    65535u,    /* (10,20cm] */
    22000u,    /* (20,30cm] */
    13000u,    /* (30,40cm] */
    5000u,     /* (40,50cm] */

    850u,     /* (50,60cm] */
    850u,     /* (60,70cm] */
    850u,     /* (70,80cm] */
    850u,     /* (80,90cm] */
    850u,     /* (90,100cm]*/

    850u,    /* (100,110cm] */
    850u,    /* (110,120cm] */
    800u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    600u,    /* (170,180cm] */
    550u,    /* (180,190cm] */
    500u,    /* (190,200cm] */

    450u,    /* (200,210cm] */
    400u,    /* (210,220cm] */
    350u,    /* (220,230cm] */
    330u,    /* (230,240cm] */
    300u,    /* (240,250cm] */

    250u,    /* (250,260cm] */
    220u,    /* (260,270cm] */
    200u,    /* (270,280cm] */
    200u,    /* (280,290cm] */
    200u,    /* (290,300cm] */

    180u,    /* (300,310cm] */
    170u,    /* (310,320cm] */
    160u,    /* (320,330cm] */
    150u,    /* (330,340cm] */
    150u,    /* (340,350cm] */

    150u,    /* (350,360cm] */
    140u,    /* (360,370cm] */
    140u,    /* (370,380cm] */
    140u,    /* (380,390cm] */
    140u,    /* (390,400cm] */
};

const uint16 Gu16FL_FR_LisStandardThresholdTable[SNS_DIS_TOTAL_NUM] = 
{
    50000u,   /* (0,10cm]  */
    35000u,   /* (10,20cm] */
    10000u,   /* (20,30cm] */
    6000u,    /* (30,40cm] */
    2500u,    /* (40,50cm] */

    2000u,     /* (50,60cm] */
    1800u,     /* (60,70cm] */
    1600u,     /* (70,80cm] */
    1400u,     /* (80,90cm] */
    1200u,     /* (90,100cm]*/

    1100u,   /* (100,110cm] */
    1000u,   /* (110,120cm] */
    900u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    650u,    /* (170,180cm] */
    620u,    /* (180,190cm] */
    600u,    /* (190,200cm] */

    600u,    /* (200,210cm] */
    600u,    /* (210,220cm] */
    550u,    /* (220,230cm] */
    550u,    /* (230,240cm] */
    500u,    /* (240,250cm] */

    500u,    /* (250,260cm] */
    500u,    /* (260,270cm] */
    500u,    /* (270,280cm] */
    500u,    /* (280,290cm] */
    500u,    /* (290,300cm] */

    500u,    /* (300,310cm] */
    500u,    /* (310,320cm] */
    500u,    /* (320,330cm] */
    500u,    /* (330,340cm] */
    500u,    /* (340,350cm] */

    500u,    /* (350,360cm] */
    500u,    /* (360,370cm] */
    500u,    /* (370,380cm] */
    500u,    /* (380,390cm] */
    500u,    /* (390,400cm] */
};

const uint16 Gu16FL_FR_LisChirpThresholdTable[SNS_DIS_TOTAL_NUM] = 
{
    65535u,    /* (0,10cm]  */
    65535u,    /* (10,20cm] */
    22000u,    /* (20,30cm] */
    13000u,    /* (30,40cm] */
    5000u,     /* (40,50cm] */

    850u,     /* (50,60cm] */
    850u,     /* (60,70cm] */
    850u,     /* (70,80cm] */
    850u,     /* (80,90cm] */
    850u,     /* (90,100cm]*/

    850u,    /* (100,110cm] */
    850u,    /* (110,120cm] */
    800u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    600u,    /* (170,180cm] */
    550u,    /* (180,190cm] */
    500u,    /* (190,200cm] */

    450u,    /* (200,210cm] */
    400u,    /* (210,220cm] */
    350u,    /* (220,230cm] */
    330u,    /* (230,240cm] */
    300u,    /* (240,250cm] */

    250u,    /* (250,260cm] */
    220u,    /* (260,270cm] */
    200u,    /* (270,280cm] */
    200u,    /* (280,290cm] */
    200u,    /* (290,300cm] */

    180u,    /* (300,310cm] */
    170u,    /* (310,320cm] */
    160u,    /* (320,330cm] */
    150u,    /* (330,340cm] */
    150u,    /* (340,350cm] */

    150u,    /* (350,360cm] */
    140u,    /* (360,370cm] */
    140u,    /* (370,380cm] */
    140u,    /* (380,390cm] */
    140u,    /* (390,400cm] */
};

const uint16 Gu16FML_FMR_LisStandardThresholdTable[SNS_DIS_TOTAL_NUM] = 
{
    50000u,   /* (0,10cm]  */
    35000u,   /* (10,20cm] */
    10000u,   /* (20,30cm] */
    6000u,    /* (30,40cm] */
    2500u,    /* (40,50cm] */

    2000u,     /* (50,60cm] */
    1800u,     /* (60,70cm] */
    1600u,     /* (70,80cm] */
    1400u,     /* (80,90cm] */
    1200u,     /* (90,100cm]*/

    1100u,   /* (100,110cm] */
    1000u,   /* (110,120cm] */
    900u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    650u,    /* (170,180cm] */
    620u,    /* (180,190cm] */
    600u,    /* (190,200cm] */

    600u,    /* (200,210cm] */
    600u,    /* (210,220cm] */
    550u,    /* (220,230cm] */
    550u,    /* (230,240cm] */
    500u,    /* (240,250cm] */

    500u,    /* (250,260cm] */
    500u,    /* (260,270cm] */
    500u,    /* (270,280cm] */
    500u,    /* (280,290cm] */
    500u,    /* (290,300cm] */

    500u,    /* (300,310cm] */
    500u,    /* (310,320cm] */
    500u,    /* (320,330cm] */
    500u,    /* (330,340cm] */
    500u,    /* (340,350cm] */

    500u,    /* (350,360cm] */
    500u,    /* (360,370cm] */
    500u,    /* (370,380cm] */
    500u,    /* (380,390cm] */
    500u,    /* (390,400cm] */
};

const uint16 Gu16FML_FMR_LisChirpThresholdTable[SNS_DIS_TOTAL_NUM] = 
{
    65535u,    /* (0,10cm]  */
    65535u,    /* (10,20cm] */
    22000u,    /* (20,30cm] */
    13000u,    /* (30,40cm] */
    5000u,     /* (40,50cm] */

    850u,     /* (50,60cm] */
    850u,     /* (60,70cm] */
    850u,     /* (70,80cm] */
    850u,     /* (80,90cm] */
    850u,     /* (90,100cm]*/

    850u,    /* (100,110cm] */
    850u,    /* (110,120cm] */
    800u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    600u,    /* (170,180cm] */
    550u,    /* (180,190cm] */
    500u,    /* (190,200cm] */

    450u,    /* (200,210cm] */
    400u,    /* (210,220cm] */
    350u,    /* (220,230cm] */
    330u,    /* (230,240cm] */
    300u,    /* (240,250cm] */

    250u,    /* (250,260cm] */
    220u,    /* (260,270cm] */
    200u,    /* (270,280cm] */
    200u,    /* (280,290cm] */
    200u,    /* (290,300cm] */

    180u,    /* (300,310cm] */
    170u,    /* (310,320cm] */
    160u,    /* (320,330cm] */
    150u,    /* (330,340cm] */
    150u,    /* (340,350cm] */

    150u,    /* (350,360cm] */
    140u,    /* (360,370cm] */
    140u,    /* (370,380cm] */
    140u,    /* (380,390cm] */
    140u,    /* (390,400cm] */
};

const uint16 Gu16RLS_RRS_LisStandardThresholdTable[SNS_DIS_TOTAL_NUM] = 
{
    50000u,   /* (0,10cm]  */
    35000u,   /* (10,20cm] */
    10000u,   /* (20,30cm] */
    6000u,    /* (30,40cm] */
    2500u,    /* (40,50cm] */

    2000u,     /* (50,60cm] */
    1800u,     /* (60,70cm] */
    1600u,     /* (70,80cm] */
    1400u,     /* (80,90cm] */
    1200u,     /* (90,100cm]*/

    1100u,   /* (100,110cm] */
    1000u,   /* (110,120cm] */
    900u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    650u,    /* (170,180cm] */
    620u,    /* (180,190cm] */
    600u,    /* (190,200cm] */

    600u,    /* (200,210cm] */
    600u,    /* (210,220cm] */
    550u,    /* (220,230cm] */
    550u,    /* (230,240cm] */
    500u,    /* (240,250cm] */

    500u,    /* (250,260cm] */
    500u,    /* (260,270cm] */
    500u,    /* (270,280cm] */
    500u,    /* (280,290cm] */
    500u,    /* (290,300cm] */

    500u,    /* (300,310cm] */
    500u,    /* (310,320cm] */
    500u,    /* (320,330cm] */
    500u,    /* (330,340cm] */
    500u,    /* (340,350cm] */

    500u,    /* (350,360cm] */
    500u,    /* (360,370cm] */
    500u,    /* (370,380cm] */
    500u,    /* (380,390cm] */
    500u,    /* (390,400cm] */
};

const uint16 Gu16RLS_RRS_LisChirpThresholdTable[SNS_DIS_TOTAL_NUM] = 
{
    65535u,    /* (0,10cm]  */
    65535u,    /* (10,20cm] */
    22000u,    /* (20,30cm] */
    13000u,    /* (30,40cm] */
    5000u,     /* (40,50cm] */

    850u,     /* (50,60cm] */
    850u,     /* (60,70cm] */
    850u,     /* (70,80cm] */
    850u,     /* (80,90cm] */
    850u,     /* (90,100cm]*/

    850u,    /* (100,110cm] */
    850u,    /* (110,120cm] */
    800u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    600u,    /* (170,180cm] */
    550u,    /* (180,190cm] */
    500u,    /* (190,200cm] */

    450u,    /* (200,210cm] */
    400u,    /* (210,220cm] */
    350u,    /* (220,230cm] */
    330u,    /* (230,240cm] */
    300u,    /* (240,250cm] */

    250u,    /* (250,260cm] */
    220u,    /* (260,270cm] */
    200u,    /* (270,280cm] */
    200u,    /* (280,290cm] */
    200u,    /* (290,300cm] */

    180u,    /* (300,310cm] */
    170u,    /* (310,320cm] */
    160u,    /* (320,330cm] */
    150u,    /* (330,340cm] */
    150u,    /* (340,350cm] */

    150u,    /* (350,360cm] */
    140u,    /* (360,370cm] */
    140u,    /* (370,380cm] */
    140u,    /* (380,390cm] */
    140u,    /* (390,400cm] */
};

const uint16 Gu16RL_RR_LisStandardThresholdTable[SNS_DIS_TOTAL_NUM] = 
{
    50000u,   /* (0,10cm]  */
    35000u,   /* (10,20cm] */
    10000u,   /* (20,30cm] */
    6000u,    /* (30,40cm] */
    2500u,    /* (40,50cm] */

    2000u,     /* (50,60cm] */
    1800u,     /* (60,70cm] */
    1600u,     /* (70,80cm] */
    1400u,     /* (80,90cm] */
    1200u,     /* (90,100cm]*/

    1100u,   /* (100,110cm] */
    1000u,   /* (110,120cm] */
    900u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    650u,    /* (170,180cm] */
    620u,    /* (180,190cm] */
    600u,    /* (190,200cm] */

    600u,    /* (200,210cm] */
    600u,    /* (210,220cm] */
    550u,    /* (220,230cm] */
    550u,    /* (230,240cm] */
    500u,    /* (240,250cm] */

    500u,    /* (250,260cm] */
    500u,    /* (260,270cm] */
    500u,    /* (270,280cm] */
    500u,    /* (280,290cm] */
    500u,    /* (290,300cm] */

    500u,    /* (300,310cm] */
    500u,    /* (310,320cm] */
    500u,    /* (320,330cm] */
    500u,    /* (330,340cm] */
    500u,    /* (340,350cm] */

    500u,    /* (350,360cm] */
    500u,    /* (360,370cm] */
    500u,    /* (370,380cm] */
    500u,    /* (380,390cm] */
    500u,    /* (390,400cm] */
};

const uint16 Gu16RL_RR_LisChirpThresholdTable[SNS_DIS_TOTAL_NUM] = 
{
    65535u,    /* (0,10cm]  */
    65535u,    /* (10,20cm] */
    22000u,    /* (20,30cm] */
    13000u,    /* (30,40cm] */
    5000u,     /* (40,50cm] */

    850u,     /* (50,60cm] */
    850u,     /* (60,70cm] */
    850u,     /* (70,80cm] */
    850u,     /* (80,90cm] */
    850u,     /* (90,100cm]*/

    850u,    /* (100,110cm] */
    850u,    /* (110,120cm] */
    800u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    600u,    /* (170,180cm] */
    550u,    /* (180,190cm] */
    500u,    /* (190,200cm] */

    450u,    /* (200,210cm] */
    400u,    /* (210,220cm] */
    350u,    /* (220,230cm] */
    330u,    /* (230,240cm] */
    300u,    /* (240,250cm] */

    250u,    /* (250,260cm] */
    220u,    /* (260,270cm] */
    200u,    /* (270,280cm] */
    200u,    /* (280,290cm] */
    200u,    /* (290,300cm] */

    180u,    /* (300,310cm] */
    170u,    /* (310,320cm] */
    160u,    /* (320,330cm] */
    150u,    /* (330,340cm] */
    150u,    /* (340,350cm] */

    150u,    /* (350,360cm] */
    140u,    /* (360,370cm] */
    140u,    /* (370,380cm] */
    140u,    /* (380,390cm] */
    140u,    /* (390,400cm] */
};

const uint16 Gu16RML_RMR_LisStandardThresholdTable[SNS_DIS_TOTAL_NUM] = 
{
    50000u,   /* (0,10cm]  */
    35000u,   /* (10,20cm] */
    10000u,   /* (20,30cm] */
    6000u,    /* (30,40cm] */
    2500u,    /* (40,50cm] */

    2000u,     /* (50,60cm] */
    1800u,     /* (60,70cm] */
    1600u,     /* (70,80cm] */
    1400u,     /* (80,90cm] */
    1200u,     /* (90,100cm]*/

    1100u,   /* (100,110cm] */
    1000u,   /* (110,120cm] */
    900u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    650u,    /* (170,180cm] */
    620u,    /* (180,190cm] */
    600u,    /* (190,200cm] */

    600u,    /* (200,210cm] */
    600u,    /* (210,220cm] */
    550u,    /* (220,230cm] */
    550u,    /* (230,240cm] */
    500u,    /* (240,250cm] */

    500u,    /* (250,260cm] */
    500u,    /* (260,270cm] */
    500u,    /* (270,280cm] */
    500u,    /* (280,290cm] */
    500u,    /* (290,300cm] */

    500u,    /* (300,310cm] */
    500u,    /* (310,320cm] */
    500u,    /* (320,330cm] */
    500u,    /* (330,340cm] */
    500u,    /* (340,350cm] */

    500u,    /* (350,360cm] */
    500u,    /* (360,370cm] */
    500u,    /* (370,380cm] */
    500u,    /* (380,390cm] */
    500u,    /* (390,400cm] */
};

const uint16 Gu16RML_RMR_LisChirpThresholdTable[SNS_DIS_TOTAL_NUM] = 
{
    65535u,    /* (0,10cm]  */
    65535u,    /* (10,20cm] */
    22000u,    /* (20,30cm] */
    13000u,    /* (30,40cm] */
    5000u,     /* (40,50cm] */

    850u,     /* (50,60cm] */
    850u,     /* (60,70cm] */
    850u,     /* (70,80cm] */
    850u,     /* (80,90cm] */
    850u,     /* (90,100cm]*/

    850u,    /* (100,110cm] */
    850u,    /* (110,120cm] */
    800u,    /* (120,130cm] */
    800u,    /* (130,140cm] */
    750u,    /* (140,150cm] */

    700u,    /* (150,160cm] */
    650u,    /* (160,170cm] */
    600u,    /* (170,180cm] */
    550u,    /* (180,190cm] */
    500u,    /* (190,200cm] */

    450u,    /* (200,210cm] */
    400u,    /* (210,220cm] */
    350u,    /* (220,230cm] */
    330u,    /* (230,240cm] */
    300u,    /* (240,250cm] */

    250u,    /* (250,260cm] */
    220u,    /* (260,270cm] */
    200u,    /* (270,280cm] */
    200u,    /* (280,290cm] */
    200u,    /* (290,300cm] */

    180u,    /* (300,310cm] */
    170u,    /* (310,320cm] */
    160u,    /* (320,330cm] */
    150u,    /* (330,340cm] */
    150u,    /* (340,350cm] */

    150u,    /* (350,360cm] */
    140u,    /* (360,370cm] */
    140u,    /* (370,380cm] */
    140u,    /* (380,390cm] */
    140u,    /* (390,400cm] */
};

/* 定频模式下障碍物回波高度判断障碍物类型的阈值 */
const SnsCalibHeightType GStrObjJudgeStandardThresholdTable[SNS_DIS_HIGH_TOTAL_NUM] = 
{
    /* 需要根据路砍等低矮障碍物的高度过滤10~80cm的PVC阈值 */
    [SNS_DIS_HIGH_10cm] = 
    {
        .u16PVC_PileHeight = 2500,
        .u16BigWallHeight = 3500,
        .u16SecondWallHeight = 2000,
        .u16CurbHeight = 1400,
    },
    [SNS_DIS_HIGH_20cm] = 
    {
        .u16PVC_PileHeight = 2500,
        .u16BigWallHeight = 3500,
        .u16SecondWallHeight = 2000,
        .u16CurbHeight = 1400,
    },
    [SNS_DIS_HIGH_30cm] = 
    {
        .u16PVC_PileHeight = 2500,
        .u16BigWallHeight = 3500,
        .u16SecondWallHeight = 2000,
        .u16CurbHeight = 1400,
    },
    [SNS_DIS_HIGH_40cm] = 
    {
        .u16PVC_PileHeight = 2500,
        .u16BigWallHeight = 3500,
        .u16SecondWallHeight = 2000,
        .u16CurbHeight = 1400,
    },
    [SNS_DIS_HIGH_50cm] = 
    {
        .u16PVC_PileHeight = 3000,
        .u16BigWallHeight = 3000,
        .u16SecondWallHeight = 2000,
        .u16CurbHeight = 1400,
    },
    [SNS_DIS_HIGH_60cm] = 
    {
        .u16PVC_PileHeight = 2000,
        .u16BigWallHeight = 2800,
        .u16SecondWallHeight = 1800,
        .u16CurbHeight = 1400,
    },
    [SNS_DIS_HIGH_70cm] = 
    {
        .u16PVC_PileHeight = 1800,
        .u16BigWallHeight = 2500,
        .u16SecondWallHeight = 1600,
        .u16CurbHeight = 1400,
    },
    [SNS_DIS_HIGH_80cm] = 
    {
        .u16PVC_PileHeight = 1600,
        .u16BigWallHeight = 2000,
        .u16SecondWallHeight = 1500,
        .u16CurbHeight = 1400,
    },
    [SNS_DIS_HIGH_90cm] = 
    {
        .u16PVC_PileHeight = 1400,
        .u16BigWallHeight = 1900,
        .u16SecondWallHeight = 1500,
        .u16CurbHeight = 1400,
    },
    [SNS_DIS_HIGH_100cm] = 
    {
        .u16PVC_PileHeight = 1200,
        .u16BigWallHeight = 1850,
        .u16SecondWallHeight = 1450,
        .u16CurbHeight = 1400,
    },
    [SNS_DIS_HIGH_110cm] = 
    {
        .u16PVC_PileHeight = 1100,
        .u16BigWallHeight = 1800,
        .u16SecondWallHeight = 1400,
        .u16CurbHeight = 1400,
    },
    [SNS_DIS_HIGH_120cm] = 
    {
        .u16PVC_PileHeight = 1000,
        .u16BigWallHeight = 1800,
        .u16SecondWallHeight = 1300,
        .u16CurbHeight = 1400,
    },
    [SNS_DIS_HIGH_130cm] = 
    {
        .u16PVC_PileHeight = 900,
        .u16BigWallHeight = 1750,
        .u16SecondWallHeight = 1300,
        .u16CurbHeight = 1400,
    },
    [SNS_DIS_HIGH_140cm] = 
    {
        .u16PVC_PileHeight = 900,
        .u16BigWallHeight = 1700,
        .u16SecondWallHeight = 1300,
        .u16CurbHeight = 1400,
    },
    [SNS_DIS_HIGH_150cm] = 
    {
        .u16PVC_PileHeight = 800,
        .u16BigWallHeight = 1650,
        .u16SecondWallHeight = 1300,
        .u16CurbHeight = 1400,
    },
    [SNS_DIS_HIGH_160cm] = 
    {
        .u16PVC_PileHeight = 750,
        .u16BigWallHeight = 1600,
        .u16SecondWallHeight = 1300,
        .u16CurbHeight = 1400,
    },
    [SNS_DIS_HIGH_170cm] = 
    {
        .u16PVC_PileHeight = 700,
        .u16BigWallHeight = 1550,
        .u16SecondWallHeight = 1300,
        .u16CurbHeight = 1400,
    },
    [SNS_DIS_HIGH_180cm] = 
    {
        .u16PVC_PileHeight = 650,
        .u16BigWallHeight = 1500,
        .u16SecondWallHeight = 1300,
        .u16CurbHeight = 1400,
    },
    [SNS_DIS_HIGH_190cm] = 
    {
        .u16PVC_PileHeight = 620,
        .u16BigWallHeight = 1450,
        .u16SecondWallHeight = 1250,
        .u16CurbHeight = 1400,
    },
    [SNS_DIS_HIGH_200cm] = 
    {
        .u16PVC_PileHeight = 600,
        .u16BigWallHeight = 1400,
        .u16SecondWallHeight = 1200,
        .u16CurbHeight = 1400,
    },
    [SNS_DIS_HIGH_210cm] = 
    {
        .u16PVC_PileHeight = 600,
        .u16BigWallHeight = 1350,
        .u16SecondWallHeight = 1150,
        .u16CurbHeight = 1400,
    },
    [SNS_DIS_HIGH_220cm] = 
    {
        .u16PVC_PileHeight = 600,
        .u16BigWallHeight = 1300,
        .u16SecondWallHeight = 1100,
        .u16CurbHeight = 1400,
    },
    [SNS_DIS_HIGH_230cm] = 
    {
        .u16PVC_PileHeight = 550,
        .u16BigWallHeight = 1250,
        .u16SecondWallHeight = 1050,
        .u16CurbHeight = 1450,
    },
    [SNS_DIS_HIGH_240cm] = 
    {
        .u16PVC_PileHeight = 550,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_DIS_HIGH_250cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_DIS_HIGH_260cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_DIS_HIGH_270cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_DIS_HIGH_280cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_DIS_HIGH_290cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_DIS_HIGH_300cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },    
    [SNS_DIS_HIGH_310cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_DIS_HIGH_320cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_DIS_HIGH_330cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_DIS_HIGH_340cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_DIS_HIGH_350cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_DIS_HIGH_360cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_DIS_HIGH_370cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_DIS_HIGH_380cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_DIS_HIGH_390cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_DIS_HIGH_400cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    }, 

};


/* 扫频模式下障碍物回波高度判断障碍物类型的阈值 */
const SnsCalibHeightType GStrObjJudgeChirpThresholdTable[SNS_DIS_HIGH_TOTAL_NUM] = 
{
    [SNS_DIS_HIGH_10cm] = 
    {
        .u16PVC_PileHeight = 4500,
        .u16BigWallHeight = 5000,
        .u16SecondWallHeight = 1200,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_20cm] = 
    {
        .u16PVC_PileHeight = 4500,
        .u16BigWallHeight = 5000,
        .u16SecondWallHeight = 1200,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_30cm] = 
    {
        .u16PVC_PileHeight = 4500,
        .u16BigWallHeight = 5000,
        .u16SecondWallHeight = 1200,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_40cm] = 
    {
        .u16PVC_PileHeight = 4500,
        .u16BigWallHeight = 5000,
        .u16SecondWallHeight = 1200,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_50cm] = 
    {
        .u16PVC_PileHeight = 4500,
        .u16BigWallHeight = 5000,
        .u16SecondWallHeight = 1200,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_60cm] = 
    {
        .u16PVC_PileHeight = 3000,
        .u16BigWallHeight = 3200,
        .u16SecondWallHeight = 1200,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_70cm] = 
    {
        .u16PVC_PileHeight = 2500,
        .u16BigWallHeight = 3000,
        .u16SecondWallHeight = 1100,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_80cm] = 
    {
        .u16PVC_PileHeight = 1500,
        .u16BigWallHeight = 2800,
        .u16SecondWallHeight = 1100,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_90cm] = 
    {
        .u16PVC_PileHeight = 1000,
        .u16BigWallHeight = 2700,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_100cm] = 
    {
        .u16PVC_PileHeight = 900,
        .u16BigWallHeight = 2600,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_110cm] = 
    {
        .u16PVC_PileHeight = 900,
        .u16BigWallHeight = 2500,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_120cm] = 
    {
        .u16PVC_PileHeight = 850,
        .u16BigWallHeight = 2400,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_130cm] = 
    {
        .u16PVC_PileHeight = 800,
        .u16BigWallHeight = 2300,
        .u16SecondWallHeight = 1100,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_140cm] = 
    {
        .u16PVC_PileHeight = 800,
        .u16BigWallHeight = 2200,
        .u16SecondWallHeight = 1100,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_150cm] = 
    {
        .u16PVC_PileHeight = 750,
        .u16BigWallHeight = 2100,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_160cm] = 
    {
        .u16PVC_PileHeight = 700,
        .u16BigWallHeight = 2000,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_170cm] = 
    {
        .u16PVC_PileHeight = 650,
        .u16BigWallHeight = 1800,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_180cm] = 
    {
        .u16PVC_PileHeight = 600,
        .u16BigWallHeight = 2000,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_190cm] = 
    {
        .u16PVC_PileHeight = 550,
        .u16BigWallHeight = 2100,
        .u16SecondWallHeight = 900,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_200cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 2300,
        .u16SecondWallHeight = 900,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_210cm] = 
    {
        .u16PVC_PileHeight = 450,
        .u16BigWallHeight = 2300,
        .u16SecondWallHeight = 900,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_220cm] = 
    {
        .u16PVC_PileHeight = 400,
        .u16BigWallHeight = 2300,
        .u16SecondWallHeight = 900,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_230cm] = 
    {
        .u16PVC_PileHeight = 350,
        .u16BigWallHeight = 2300,
        .u16SecondWallHeight = 900,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_240cm] = 
    {
        .u16PVC_PileHeight = 330,
        .u16BigWallHeight = 2200,
        .u16SecondWallHeight = 900,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_250cm] = 
    {
        .u16PVC_PileHeight = 300,
        .u16BigWallHeight = 2100,
        .u16SecondWallHeight = 900,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_260cm] = 
    {
        .u16PVC_PileHeight = 250,
        .u16BigWallHeight = 2000,
        .u16SecondWallHeight = 800,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_270cm] = 
    {
        .u16PVC_PileHeight = 250,
        .u16BigWallHeight = 1800,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_280cm] = 
    {
        .u16PVC_PileHeight = 200,
        .u16BigWallHeight = 1700,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_290cm] = 
    {
        .u16PVC_PileHeight = 200,
        .u16BigWallHeight = 1550,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_300cm] = 
    {
        .u16PVC_PileHeight = 180,
        .u16BigWallHeight = 1500,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 900,
    },
    /* 待补充 */
    [SNS_DIS_HIGH_310cm] = 
    {
        .u16PVC_PileHeight = 170,
        .u16BigWallHeight = 1350,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_320cm] = 
    {
        .u16PVC_PileHeight = 160,
        .u16BigWallHeight = 1300,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_330cm] = 
    {
        .u16PVC_PileHeight = 150,
        .u16BigWallHeight = 1250,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_340cm] = 
    {
        .u16PVC_PileHeight = 150,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_350cm] = 
    {
        .u16PVC_PileHeight = 150,
        .u16BigWallHeight = 1150,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_360cm] = 
    {
        .u16PVC_PileHeight = 150,
        .u16BigWallHeight = 1100,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_370cm] = 
    {
        .u16PVC_PileHeight = 300,
        .u16BigWallHeight = 1050,
        .u16SecondWallHeight = 650,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_380cm] = 
    {
        .u16PVC_PileHeight = 300,
        .u16BigWallHeight = 1000,
        .u16SecondWallHeight = 600,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_390cm] = 
    {
        .u16PVC_PileHeight = 300,
        .u16BigWallHeight = 950,
        .u16SecondWallHeight = 550,
        .u16CurbHeight = 900,
    },
    [SNS_DIS_HIGH_400cm] = 
    {
        .u16PVC_PileHeight = 300,
        .u16BigWallHeight = 900,
        .u16SecondWallHeight = 500,
        .u16CurbHeight = 900,
    }, 
};


/* 在RAM中存储的数据，用于实际应用使用 */
uint16 Gu16FLS_FRS_MasStandardThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
uint16 Gu16FLS_FRS_MasChirpThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
uint16 Gu16FL_FR_MasStandardThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
uint16 Gu16FL_FR_MasChirpThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
uint16 Gu16FML_FMR_MasStandardThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
uint16 Gu16FML_FMR_MasChirpThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
uint16 Gu16FLS_FRS_LisStandardThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
uint16 Gu16FLS_FRS_LisChirpThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
uint16 Gu16FL_FR_LisStandardThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
uint16 Gu16FL_FR_LisChirpThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
uint16 Gu16FML_FMR_LisStandardThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
uint16 Gu16FML_FMR_LisChirpThresholdTableInRAM[SNS_DIS_TOTAL_NUM];

uint16 Gu16RLS_RRS_MasStandardThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
uint16 Gu16RLS_RRS_MasChirpThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
uint16 Gu16RL_RR_MasStandardThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
uint16 Gu16RL_RR_MasChirpThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
uint16 Gu16RML_RMR_MasStandardThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
uint16 Gu16RML_RMR_MasChirpThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
uint16 Gu16RLS_RRS_LisStandardThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
uint16 Gu16RLS_RRS_LisChirpThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
uint16 Gu16RL_RR_LisStandardThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
uint16 Gu16RL_RR_LisChirpThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
uint16 Gu16RML_RMR_LisStandardThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
uint16 Gu16RML_RMR_LisChirpThresholdTableInRAM[SNS_DIS_TOTAL_NUM];

/* 在RAM中定义用于障碍物类型区分的阈值数据 */
SnsCalibHeightType GStrObjJudgeStandardThresholdTableInRAM[SNS_DIS_HIGH_TOTAL_NUM];
SnsCalibHeightType GStrObjJudgeChirpThresholdTableInRAM[SNS_DIS_HIGH_TOTAL_NUM];

const uint16 *Gpu16SnsMasThresholdTable[PDC_SNS_GROUP_NUM][6] = 
{
    [PDC_SNS_GROUP_FRONT] = 
    {
        Gu16FLS_FRS_MasStandardThresholdTableInRAM,
        Gu16FLS_FRS_MasChirpThresholdTableInRAM,
        Gu16FL_FR_MasStandardThresholdTableInRAM,
        Gu16FL_FR_MasChirpThresholdTableInRAM,
        Gu16FML_FMR_MasStandardThresholdTableInRAM,
        Gu16FML_FMR_MasChirpThresholdTableInRAM,
    },
    
    [PDC_SNS_GROUP_REAR] = 
    {
        Gu16RLS_RRS_MasStandardThresholdTableInRAM,
        Gu16RLS_RRS_MasChirpThresholdTableInRAM,
        Gu16RL_RR_MasStandardThresholdTableInRAM,
        Gu16RL_RR_MasChirpThresholdTableInRAM,
        Gu16RML_RMR_MasStandardThresholdTableInRAM,
        Gu16RML_RMR_MasChirpThresholdTableInRAM,
    }
};

const uint16 *Gpu16SnsLisThresholdTable[PDC_SNS_GROUP_NUM][6] = 
{
    [PDC_SNS_GROUP_FRONT] = 
    {
        Gu16FLS_FRS_LisStandardThresholdTableInRAM,
        Gu16FLS_FRS_LisChirpThresholdTableInRAM,
        Gu16FL_FR_LisStandardThresholdTableInRAM,
        Gu16FL_FR_LisChirpThresholdTableInRAM,
        Gu16FML_FMR_LisStandardThresholdTableInRAM,
        Gu16FML_FMR_LisChirpThresholdTableInRAM,
    },
    
    [PDC_SNS_GROUP_REAR] = 
    {
        Gu16RLS_RRS_LisStandardThresholdTableInRAM,
        Gu16RLS_RRS_LisChirpThresholdTableInRAM,
        Gu16RL_RR_LisStandardThresholdTableInRAM,
        Gu16RL_RR_LisChirpThresholdTableInRAM,
        Gu16RML_RMR_LisStandardThresholdTableInRAM,
        Gu16RML_RMR_LisChirpThresholdTableInRAM,
    }
};
/******************************************************************************/
/******************************************************************************/
/****************************** Private Function ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/******************************* Public Function ******************************/
/******************************************************************************/
/******************************************************************************/
void CopySnsCalibWidthAndThresholdToRam(void)
{
    memcpy(&Gu16FLS_FRS_MasStandardThresholdTableInRAM[0],&Gu16FLS_FRS_MasStandardThresholdTable[0],SNS_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16FL_FR_MasStandardThresholdTableInRAM[0],&Gu16FL_FR_MasStandardThresholdTable[0],SNS_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16FML_FMR_MasStandardThresholdTableInRAM[0],&Gu16FML_FMR_MasStandardThresholdTable[0],SNS_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16RLS_RRS_MasStandardThresholdTableInRAM[0],&Gu16RLS_RRS_MasStandardThresholdTable[0],SNS_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16RL_RR_MasStandardThresholdTableInRAM[0],&Gu16RL_RR_MasStandardThresholdTable[0],SNS_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16RML_RMR_MasStandardThresholdTableInRAM[0],&Gu16RML_RMR_MasStandardThresholdTable[0],SNS_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16FLS_FRS_LisStandardThresholdTableInRAM[0],&Gu16FLS_FRS_LisStandardThresholdTable[0],SNS_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16FL_FR_LisStandardThresholdTableInRAM[0],&Gu16FL_FR_LisStandardThresholdTable[0],SNS_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16FML_FMR_LisStandardThresholdTableInRAM[0],&Gu16FML_FMR_LisStandardThresholdTable[0],SNS_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16RLS_RRS_LisStandardThresholdTableInRAM[0],&Gu16RLS_RRS_LisStandardThresholdTable[0],SNS_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16RL_RR_LisStandardThresholdTableInRAM[0],&Gu16RL_RR_LisStandardThresholdTable[0],SNS_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16RML_RMR_LisStandardThresholdTableInRAM[0],&Gu16RML_RMR_LisStandardThresholdTable[0],SNS_DIS_TOTAL_NUM*sizeof(uint16));
    
    memcpy(&Gu16FLS_FRS_MasChirpThresholdTableInRAM[0],&Gu16FLS_FRS_MasChirpThresholdTable[0],SNS_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16FL_FR_MasChirpThresholdTableInRAM[0],&Gu16FL_FR_MasChirpThresholdTable[0],SNS_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16FML_FMR_MasChirpThresholdTableInRAM[0],&Gu16FML_FMR_MasChirpThresholdTable[0],SNS_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16RLS_RRS_MasChirpThresholdTableInRAM[0],&Gu16RLS_RRS_MasChirpThresholdTable[0],SNS_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16RL_RR_MasChirpThresholdTableInRAM[0],&Gu16RL_RR_MasChirpThresholdTable[0],SNS_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16RML_RMR_MasChirpThresholdTableInRAM[0],&Gu16RML_RMR_MasChirpThresholdTable[0],SNS_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16FLS_FRS_LisChirpThresholdTableInRAM[0],&Gu16FLS_FRS_LisChirpThresholdTable[0],SNS_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16FL_FR_LisChirpThresholdTableInRAM[0],&Gu16FL_FR_LisChirpThresholdTable[0],SNS_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16FML_FMR_LisChirpThresholdTableInRAM[0],&Gu16FML_FMR_LisChirpThresholdTable[0],SNS_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16RLS_RRS_LisChirpThresholdTableInRAM[0],&Gu16RLS_RRS_LisChirpThresholdTable[0],SNS_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16RL_RR_LisChirpThresholdTableInRAM[0],&Gu16RL_RR_LisChirpThresholdTable[0],SNS_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16RML_RMR_LisChirpThresholdTableInRAM[0],&Gu16RML_RMR_LisChirpThresholdTable[0],SNS_DIS_TOTAL_NUM*sizeof(uint16));
    
    memcpy(&GStrObjJudgeStandardThresholdTableInRAM[0],&GStrObjJudgeStandardThresholdTable[0],SNS_DIS_HIGH_TOTAL_NUM*sizeof(SnsCalibHeightType));
    memcpy(&GStrObjJudgeChirpThresholdTableInRAM[0],&GStrObjJudgeChirpThresholdTable[0],SNS_DIS_HIGH_TOTAL_NUM*sizeof(SnsCalibHeightType));
}


void BigLittleEndianSwap(uint8_t *p, uint16_t size)
{
	uint16_t i;
	uint8_t tmp;
    for(i=0; i<size; i=i+2)
    {
	    tmp = p[i];
	    p[i] = p[i+1];
		p[i+1] = tmp;
	}
}


