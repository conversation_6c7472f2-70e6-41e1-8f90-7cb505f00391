/*==================================================================================================
*   Project              : RTD AUTOSAR 4.4
*   Platform             : CORTEXM
*   Peripheral           : Sbc_fs23
*   Dependencies         : 
*
*   Autosar Version      : 4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : 0.8.0
*   Build Version        : S32K3_RTD_0_8_0_D2303_ASR_REL_4_4_REV_0000_20230310
*
*   (c) Copyright 2020 - 2022 NXP Semiconductors
*   All Rights Reserved.
*
*   NXP Confidential. This software is owned or controlled by NXP and may only be
*   used strictly in accordance with the applicable license terms. By expressly
*   accepting such terms or by downloading, installing, activating and/or otherwise
*   using the software, you are agreeing that you have read, and that you agree to
*   comply with and are bound by, such license terms. If you do not agree to be
*   bound by the applicable license terms, then you may not retain, install,
*   activate or otherwise use the software.
==================================================================================================*/
/**
*   @file CDD_FS23_BOARD_InitPeripherals_PBcfg.c
*
*   @addtogroup CDD_FS23_PBCFG_DRIVER_CONFIGURATION SBC_FS23 Driver Configurations
*   @{
*/


#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                                        INCLUDE FILES
* 1) system and project includes
* 2) needed interfaces from external units
* 3) internal and external interfaces from this unit
==================================================================================================*/
#include "CDD_Sbc_fs23.h"
#include "types.h"
#include "DMACfg.h"

/*==================================================================================================
*                           LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
*                                          LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
*                                         LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                         LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                        GLOBAL CONSTANTS
==================================================================================================*/
#define SBC_FS23_START_SEC_CONFIG_DATA_UNSPECIFIED
//#include "Sbc_fs23_MemMap.h"
static const Sbc_fs23_InterfaceConfigType Sbc_fs23_InterfaceConfig_BOARD_INITPERIPHERALS =
{
#if (SBC_FS23_SPI_TRANSMIT == SBC_FS23_COMM_METHOD)
	CSIH0_MASTER,
    0U,
#else
    0U,
    32U,
#endif
    SBC_FS23_SYNC_TRANSMIT
};
static const Sbc_fs23_FailsafeConfigType Sbc_fs23_FailsafeConfig_BOARD_INITPERIPHERALS =
{
    /* InitOvUvCfg1Reg */
    ((uint16)0x0000U)
    | SBC_FS23_FS_V1MON_UV_RSTB_IMPACT_MASK
    | SBC_FS23_FS_V1MON_OV_RSTB_IMPACT_MASK
    ,
    /* InitOvUvCfg2Reg */
    ((uint16)0x0000U)
    ,
    /* InitFccuCfgReg */
    ((uint16)0x0000U) 
    ,
    /* InitFssmCfgReg */
    ((uint16)0x0000U)
    | SBC_FS23_FS_FLT_ERR_LIMIT_8
    | SBC_FS23_FS_RSTB_DUR_10MS
    | SBC_FS23_FS_RSTB8S_ENABLED
    | SBC_FS23_FS_EXT_RSTB_MON_ENABLED
    ,
    /* InitWdCfgReg */
    ((uint16)0x0000U)
    | SBC_FS23_FS_WD_ERR_LIMIT_6
    | SBC_FS23_FS_WD_RFR_LIMIT_2
    | SBC_FS23_FS_WD_RSTB_IMPACT_MASK
    ,
    /* InitCrcReg */
    ((uint16)0x0000U)
    ,
    /* WdWindowReg */
    ((uint16)0x0000U)
    | SBC_FS23_FS_WDW_PERIOD_1024MS
};
static const Sbc_fs23_MainConfigType Sbc_fs23_MainConfig_BOARD_INITPERIPHERALS =
{
    /* InitSysCfgReg */
    ((uint16)0x0000U)
    | SBC_FS23_M_MOD_EN_ENABLED
    | SBC_FS23_M_MOD_CONF_TRIANGULAR
    | SBC_FS23_M_INTB_DUR_100US
    | SBC_FS23_M_INT_TO_WUEN_DISABLED
    ,
    /* InitRegCtrlReg */
    ((uint16)0x0000U)
    | SBC_FS23_M_V2ON_LPON_MASK
    | SBC_FS23_M_V3ON_LPON_MASK
    ,
    /* InitRegMskReg */
    ((uint16)0x0000U)
    ,
    /* InitIoTimerMskReg */
    ((uint16)0x0000U)
    ,
    /* InitVsupComMskReg */
    ((uint16)0x0000U)
    ,
    /* InitIoWuEnReg */
    ((uint16)0x0000U)
    | SBC_FS23_M_WK1_WUEN_MASK
    ,
    /* InitWu1EnReg */
    ((uint16)0x0000U)
    | SBC_FS23_M_CAN_WUEN_MASK
    ,
    /* InitCsFlgMskReg */
    ((uint16)0x0000U)
    ,
    /* InitHsxMskReg */
    ((uint16)0x0000U)
    ,
    /* InitCanLinMskReg */
    ((uint16)0x0000U)
};
const Sbc_fs23_InitConfigType Sbc_fs23_InitConfig_BOARD_INITPERIPHERALS =
{
    &Sbc_fs23_MainConfig_BOARD_INITPERIPHERALS,
    &Sbc_fs23_FailsafeConfig_BOARD_INITPERIPHERALS
};
#if (STD_ON == SBC_FS23_LDT_API)
const Sbc_fs23_LdtConfigType Sbc_fs23_LdtConfig_BOARD_INITPERIPHERALS[1] =
{
    {
        /*LdtCfg1Reg*/
        ((uint16)0U)
        ,
        /*LdtCfg2Reg*/
        ((uint16)0U)
        ,
        /*LdtCfg3Reg*/
        ((uint16)0U)
        ,
        /*LdtCtrlReg*/
        ((uint16)0x0000U)
        | SBC_FS23_M_LDT_MODE_MASK
        | SBC_FS23_M_LDT_FNCT_1
        | SBC_FS23_M_LDT2LP_LPOFF
    }
};
#endif /*(STD_ON == SBC_FS23_LDT_API)*/
#if (STD_ON == SBC_FS23_WAKEUP_API)
const Sbc_fs23_WakeUpConfigType Sbc_fs23_WakeupConfig_BOARD_INITPERIPHERALS[1] =
{
    {
        /*IoWuCfgReg*/
        ((uint16)0x0000U)
        | SBC_FS23_M_WK1_WUCFG_HIGH
        ,
        /*IoWuEnReg*/
        ((uint16)0x0000U)
        | SBC_FS23_M_WK1_WUEN_WAKEUP
        ,
        /*Wu1EnReg*/
        ((uint16)0x0000U)
        | SBC_FS23_M_CAN_WUEN_WAKEUP
        ,
        /*CsCfgReg*/
        ((uint16)0x0000U)
    }
};
#endif /*(STD_ON == SBC_FS23_WAKEUP_API)*/
#if (STD_ON == SBC_FS23_HSX_API)
const Sbc_fs23_HSxConfigType Sbc_fs23_HSxConfig_BOARD_INITPERIPHERALS[1] =
{
    {
        /*Timer1Reg*/
        ((uint16)0x0000U)
        | SBC_FS23_M_TIMER1_PER_10MS
        | SBC_FS23_M_TIMER1_ON_0MS
        | SBC_FS23_M_TIMER1_DLY_0US
        ,
        /*Timer2Reg*/
        ((uint16)0x0000U)
        | SBC_FS23_M_TIMER2_PER_10MS
        | SBC_FS23_M_TIMER2_ON_0MS
        | SBC_FS23_M_TIMER2_DLY_0US
        ,
        /*Timer3Reg*/
        ((uint16)0x0000U)
        | SBC_FS23_M_TIMER3_PER_10MS
        | SBC_FS23_M_TIMER3_ON_0MS
        | SBC_FS23_M_TIMER3_DLY_0US
        ,
        /*Pwm1Reg*/
        ((uint16)0x0000U)
        | SBC_FS23_M_PWM1_F_200HZ
        | SBC_FS23_M_PWM1_DLY_0US
        | (uint16)500U
        ,
        /*Pwm2Reg*/
        ((uint16)0x0000U)
        | SBC_FS23_M_PWM2_F_200HZ
        | SBC_FS23_M_PWM2_DLY_0US
        | (uint16)500U
        ,
        /*Pwm3eg*/
        ((uint16)0x0000U)
        | SBC_FS23_M_PWM3_F_200HZ
        | SBC_FS23_M_PWM3_DLY_0US
        | (uint16)500U
        ,
        /*HsxSrcReg*/
        ((uint16)0x0000U)
        | SBC_FS23_M_HS1_SRC_SEL_EN_DIS
        | SBC_FS23_M_HS2_SRC_SEL_EN_DIS
        | SBC_FS23_M_HS3_SRC_SEL_EN_DIS
        | SBC_FS23_M_HS4_SRC_SEL_EN_DIS
        ,
        /*HsxCtrlReg*/
        ((uint16)0x0000U)
    }
};
#endif /*(STD_ON == SBC_FS23_HSX_API)*/
const Sbc_fs23_ConfigType Sbc_fs23_Config =
{
    &Sbc_fs23_InitConfig_BOARD_INITPERIPHERALS,
    &Sbc_fs23_InterfaceConfig_BOARD_INITPERIPHERALS,
#if (STD_ON == SBC_FS23_LDT_API)
    1U,
    &Sbc_fs23_LdtConfig_BOARD_INITPERIPHERALS,
#endif
#if (STD_ON == SBC_FS23_WAKEUP_API)
    1U,
    &Sbc_fs23_WakeupConfig_BOARD_INITPERIPHERALS,
#endif
#if (STD_ON == SBC_FS23_HSX_API)
    1U,
    &Sbc_fs23_HSxConfig_BOARD_INITPERIPHERALS,
#endif
#if (STD_OFF == SBC_FS23_DISABLE_DEM_REPORT_ERROR_STATUS)
    NULL_PTR,
#endif
#if (STD_OFF == SBC_FS23_EXTERNAL_WATCHDOG_API)
    NULL_PTR
#endif
};
#define SBC_FS23_STOP_SEC_CONFIG_DATA_UNSPECIFIED
//#include "Sbc_fs23_MemMap.h"
/*==================================================================================================
*                                        GLOBAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                    LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                         LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
*                                        GLOBAL FUNCTIONS
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */

