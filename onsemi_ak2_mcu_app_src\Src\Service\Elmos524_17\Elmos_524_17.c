/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * Elmos_524_17.c:
 * Created on: 2019-11-01 15:04
 * Original designer:
 ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "Elmos_524_17_Private.h"

/***************************************************************************** */
/***************************************************************************** */
/***************************** Symbol Definitions **************************** */
/***************************************************************************** */
/***************************************************************************** */

#define Sns524_17_STOPMEASCRMCMD      0x04
#define Sns524_17_AdjustCRMCMD        0x05
#define Sns524_17_IDAccessCRMCMD      0x07
#define Sns524_17_MeasCfgAccessCRMCMD 0x08
#define Sns524_17_MeasCfgWRITECRMCMD 0x09
#define Sns524_17_EEPROMAccessCRMCMD 0x0A
#define Sns524_17_ICRegAccessCRMCMD  0x0B
#define Sns524_17_ICRegWriteCRMCMD   0x0C
#define Sns524_17_ReadStatusCRMCMD   0x0D
#define Sns524_17_ReadStatusPDCMCMD  0x0E
#define Sns524_17_ICMODECRMCMD       0x0F

#define Sns524_17_CfgWriteAddrMax 197   /** @brief 测量参数写入地址最大值 */
#define Sns524_17_CfgReadAddrMax  203   /** @brief 测量参数读取地址最大值 */


/***************************************************************************** */
/***************************************************************************** */
/*************************** Constants Declaration *************************** */
/***************************************************************************** */
/***************************************************************************** */

const uint8 Elmos17EEPROMOpration[Elmos17EEPROMOperation_Num][2] =
{
        [Elmos17EEPROMToRAM]  = {0x01,0xAA},
        [Elmos17EnEEPProg]    = {0x02,0x66},
        [Elmos17RAMToEEPROM]  = {0x04,0x33},
        [Elmos17ProgSLAVE_ID] = {0x08,0x55},
};

/** @brief Sns探测类型对应命令位 */
const uint8 MeasTypeList[SNS_MEAS_TYPE_Num] =
{
        [MEAS_STD_EMIT] = 1,
        [MEAS_ADV_EMIT_ChirpDown] = 1,
        [MEAS_ADV_EMIT_ChirpUp] = 2,

        [MEAS_STD_LISTEN] = 3,

        [MEAS_ADV_LISTEN] = 3,
        [MEAS_IMPEDANCE] = 1,
        [MEAS_IDLE] = 0,

};



/***************************************************************************//**
 * @brief
 * 组包CRM命令
 *
 * @param       DSICfg        DSICHL 配置
 * @param       CRMCMD_Type   CRM命令类型
 * @param       InData[3]     3字节数据
 * @param       pMeasCRM_CMD  组包命令保持地址
 * @return      eRet_OK    OK else NG
 *
 *
 * ****************************************************************************/
 Elmos17RetType_en PackCRMCmd(DSIChlSel_en DSIChlSEL,uint8 InData[3],TRANS_CRM_DM_Str *pCRM_CMD)
{

    CRM_CMD_Str  Sns_CMD;


    if((InData == NULL) || (pCRM_CMD == NULL))
    {
        return eRet_InDat_ERR;
    }

    Sns_CMD.Addr_CMD = InData[0];
    Sns_CMD.Data[0]  = InData[1];
    Sns_CMD.Data[1]  = InData[2];

    if(DSIChlSEL == DSIChlSEL1)
    {
        memcpy(&pCRM_CMD->CRMData[0],&Sns_CMD,sizeof(CRM_CMD_Str));
    }
    else if(DSIChlSEL == DSIChlSEL2)
    {
        memcpy(&pCRM_CMD->CRMData[1],&Sns_CMD,sizeof(CRM_CMD_Str));

    }
    else if(DSIChlSEL == DSIChlSEL1And2)
    {
        memcpy(&pCRM_CMD->CRMData[0],&Sns_CMD,sizeof(CRM_CMD_Str));
        memcpy(&pCRM_CMD->CRMData[1],&Sns_CMD,sizeof(CRM_CMD_Str));
    }
    else
    {
        return eRet_InDat_ERR;
    }



    return eRet_OK;
}

 /***************************************************************************//**
  * @brief
  * 传输Elmos17寻址请求
  *
  * @param       DSICfg        DSICHL 配置
  * @return      eRet_OK    OK else NG
  *
  *
  * ****************************************************************************/

 Elmos17RetType_en TransElmos17DMReq(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL)
 {
    Elmos17RetType_en LcFlg = eRet_OK;
    DSIReturnType_en DSIReturnFlg = eDSIReturnType_OK;
    uint8 InData[3];

    TRANS_CRM_DM_Str OutCRM_CMD;

    InData[0] = Sns524_17_ICMODECRMCMD;
    InData[1] = 0x01;
    InData[2] = 0x55;


    LcFlg = PackCRMCmd(DSIChlSEL,InData,&OutCRM_CMD);
    if(LcFlg != eRet_OK)
    {
        return LcFlg;
    }
    
    DSIReturnFlg = TransCRMCmd(DSIMasterID,DSIChlSEL,DM,&OutCRM_CMD,NULL);

    if(DSIReturnFlg != eDSIReturnType_OK)
    {
        return eRet_ReqSPI_ERR;
    }
    
    return LcFlg;
 }


 /***************************************************************************//**
  * @brief
  * 传输Elmos17 ICmode 请求
  *
  * @param       SnsID        探头ID
  * @param       IcMode       ICMode
  * @return      eRet_OK    OK else NG
  *
  *
  * ****************************************************************************/

 Elmos17RetType_en TransElmos17ICModeReq(SnsID_en SnsID,Elmos17IcMode_en IcMode)
 {
    Elmos17RetType_en LcFlg = eRet_OK;
    DSIReturnType_en DSIReturnFlg = eDSIReturnType_OK;
    uint8 InData[3];

    TRANS_CRM_DM_Str OutCRM_CMD;
    CRMCMD_Type_en CRMCMD_Type = CRM2Response;

    DSIMasterID_en DSIMasterID;
    DSIChlSel_en DSIChlSEL;
    uint8 cSnsPhysAddr;

    if(SnsID >= SNSIDNum)
    {
        return eRet_InDat_ERR;
    }


    DSIMasterID = SnsAddCfg[SnsID].DSIMasterID;
    DSIChlSEL   = SnsAddCfg[SnsID].DSIChlSEL;
    cSnsPhysAddr= SnsAddCfg[SnsID].cSnsPhysAddr;

    if(cSnsPhysAddr == 0)
    {
        /*广播地址,无CRM应答*/
         CRMCMD_Type = CRM_NResponse;
    }
    else
    {

    }
    InData[0] = (cSnsPhysAddr << 4) | Sns524_17_ICMODECRMCMD;
    InData[1] = IcMode;  /*0x80 写数据请求*/
    InData[2] = 0x55;

    LcFlg = PackCRMCmd(DSIChlSEL,InData,&OutCRM_CMD);
    if(LcFlg != eRet_OK)
    {
        return LcFlg;
    }

    DSIReturnFlg = TransCRMCmd(DSIMasterID,DSIChlSEL,CRMCMD_Type,&OutCRM_CMD,NULL);

    if(DSIReturnFlg != eDSIReturnType_OK)
    {
        return eRet_ReqSPI_ERR;
    }
    
    return eRet_OK;
 }

/***************************************************************************//**
 * @brief
 * 传输Elmos17写Sns测量参数地址请求
 *
 * @param       SnsID        探头ID
 * @param       cDataAddr    数据地址  地址要为偶地址，且小于Sns524_17_CfgWriteAddrMax
 * @return      eRet_OK    OK else NG
 *
 *
 * ****************************************************************************/
Elmos17RetType_en TransElmos17WriteMeasCfgReq(SnsID_en SnsID,uint8 cDataAddr)
{
    Elmos17RetType_en LcFlg = eRet_OK;
    DSIReturnType_en DSIReturnFlg = eDSIReturnType_OK;
    uint8 InData[3];

    TRANS_CRM_DM_Str OutCRM_CMD;

    CRMCMD_Type_en CRMCMD_Type = CRM2Response;
    DSIMasterID_en DSIMasterID;
    DSIChlSel_en DSIChlSEL;
    uint8 cSnsPhysAddr;

    if(((cDataAddr & 0x01) != 0) || (cDataAddr > Sns524_17_CfgWriteAddrMax) || (SnsID >= SNSIDNum))
    {
        return eRet_InDat_ERR;

    }

    DSIMasterID = SnsAddCfg[SnsID].DSIMasterID;
    DSIChlSEL   = SnsAddCfg[SnsID].DSIChlSEL;
    cSnsPhysAddr= SnsAddCfg[SnsID].cSnsPhysAddr;

    if(cSnsPhysAddr == 0)
    {
        /*广播地址,无CRM应答*/
         CRMCMD_Type = CRM_NResponse;
    }
    else
    {

    }


    InData[0] = (cSnsPhysAddr << 4) | Sns524_17_MeasCfgAccessCRMCMD;
    InData[1] = 0x80;  /*0x80 写数据请求*/
    InData[2] = cDataAddr;

    LcFlg = PackCRMCmd(DSIChlSEL,InData,&OutCRM_CMD);
    if(LcFlg != eRet_OK)
    {
        return LcFlg;
    }

    DSIReturnFlg = TransCRMCmd(DSIMasterID,DSIChlSEL,CRMCMD_Type,&OutCRM_CMD,NULL);

    if(DSIReturnFlg != eDSIReturnType_OK)
    {
        return eRet_ReqSPI_ERR;
    }
    
    return eRet_OK;
}


/***************************************************************************//**
 * @brief
 * 传输Elmos17写Sns测量数据
 *
 * @param       SnsID        探头ID
 * @param       wData        数据
 * @return      eRet_OK    OK else NG
 *
 *
 * ****************************************************************************/
Elmos17RetType_en TransElmos17WriteMeasCfgData(SnsID_en SnsID,uint16 wData)
{
    Elmos17RetType_en LcFlg = eRet_OK;
    DSIReturnType_en DSIReturnFlg = eDSIReturnType_OK;
    uint8 InData[3];

    TRANS_CRM_DM_Str OutCRM_CMD;

    CRMCMD_Type_en CRMCMD_Type = CRM2Response;
    DSIMasterID_en DSIMasterID;
    DSIChlSel_en DSIChlSEL;
    uint8 cSnsPhysAddr;

    if(SnsID >= SNSIDNum)
    {
        return eRet_InDat_ERR;
    }

    
    DSIMasterID = SnsAddCfg[SnsID].DSIMasterID;
    DSIChlSEL   = SnsAddCfg[SnsID].DSIChlSEL;
    cSnsPhysAddr= SnsAddCfg[SnsID].cSnsPhysAddr;

    if(cSnsPhysAddr == 0)
    {
        /*广播地址,无CRM应答*/
         CRMCMD_Type = CRM_NResponse;
    }
    else
    {

    }


    InData[0] = (cSnsPhysAddr << 4) | Sns524_17_MeasCfgWRITECRMCMD;
    InData[1] = (uint8)(wData >> 8);
    InData[2] = (wData & 0xFF);

    LcFlg = PackCRMCmd(DSIChlSEL,InData,&OutCRM_CMD);
    if(LcFlg != eRet_OK)
    {
        return LcFlg;
    }

    DSIReturnFlg = TransCRMCmd(DSIMasterID,DSIChlSEL,CRMCMD_Type,&OutCRM_CMD,NULL);

    if(DSIReturnFlg != eDSIReturnType_OK)
    {
        return eRet_ReqSPI_ERR;
    }
    
    return eRet_OK;
}

/***************************************************************************//**
 * @brief
 * 传输Elmos17读Sns测量参数请求
 *
 * @param       SnsID        探头ID
 * @param       cDataAddr    读数据地址 地址要为偶地址，且小于Sns524_17_CfgReadAddrMax
 * @return      eRet_OK    OK else NG
 *
 *
 * ****************************************************************************/
Elmos17RetType_en TransElmos17ReadMeasCfgReq(SnsID_en SnsID,uint8 cDataAddr)
{
    Elmos17RetType_en LcFlg = eRet_OK;
    DSIReturnType_en DSIReturnFlg = eDSIReturnType_OK;
    uint8 InData[3];
    CRMCMD_Type_en CRMCMD_Type = CRM2Response;

    TRANS_CRM_DM_Str OutCRM_CMD;
    DSIMasterID_en DSIMasterID;
    DSIChlSel_en DSIChlSEL;
    uint8 cSnsPhysAddr;

    if(((cDataAddr & 0x01) != 0) || (cDataAddr > Sns524_17_CfgReadAddrMax) || (SnsID >= SNSIDNum))
    {
        return eRet_InDat_ERR;

    }
    
    DSIMasterID = SnsAddCfg[SnsID].DSIMasterID;
    DSIChlSEL   = SnsAddCfg[SnsID].DSIChlSEL;
    cSnsPhysAddr= SnsAddCfg[SnsID].cSnsPhysAddr;

    if(cSnsPhysAddr == 0)
    {
        /*广播地址,无CRM应答*/
         CRMCMD_Type = CRM_NResponse;
    }
    else
    {

    }


    InData[0] = (cSnsPhysAddr << 4) | Sns524_17_MeasCfgAccessCRMCMD;
    InData[1] = 0x00;   /*0x00读数据请求*/
    InData[2] = cDataAddr;

    LcFlg = PackCRMCmd(DSIChlSEL,InData,&OutCRM_CMD);
    if(LcFlg != eRet_OK)
    {
        return LcFlg;
    }

    DSIReturnFlg = TransCRMCmd(DSIMasterID,DSIChlSEL,CRMCMD_Type,&OutCRM_CMD,NULL);


    if(DSIReturnFlg != eDSIReturnType_OK)
    {
        return eRet_ReqSPI_ERR;
    }
    
    return eRet_OK;
}


/***************************************************************************//**
 * @brief
 * 传输Elmos17 EEPROM 操作请求
 *
 * @param       DSIMasterID   DSI master索引
 * @param		DSIChlSEL     通道选择
 * @param		SlaveID       探头物理地址
 * @param       Operation    EEPROM操作类型
 * @return      eRet_OK    OK else NG
 *
 *
 * ****************************************************************************/
Elmos17RetType_en TransElmos17EEPROMAccessReq(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL,uint8 SlaveID,Elmos17EEPROMOperation Operation)
{
    Elmos17RetType_en LcFlg = eRet_OK;
    DSIReturnType_en DSIReturnFlg = eDSIReturnType_OK;
    uint8 InData[3];

    TRANS_CRM_DM_Str OutCRM_CMD;

    CRMCMD_Type_en CRMCMD_Type = CRM2Response;

    uint8 cSnsPhysAddr;

    if((Operation >= Elmos17EEPROMOperation_Num) || (DSIMasterID >= DSIMasterNum) /*|| (SlaveID > DSISlaveSnsNum)*/)
    {
        return eRet_InDat_ERR;
    }
    
    cSnsPhysAddr= SlaveID;

    if(cSnsPhysAddr == 0)
    {
        /*广播地址,无CRM应答*/
         CRMCMD_Type = CRM_NResponse;
    }
    else
    {

    }



    InData[0] = (cSnsPhysAddr << 4) | Sns524_17_EEPROMAccessCRMCMD;
    InData[1] = Elmos17EEPROMOpration[Operation][0];
    InData[2] = Elmos17EEPROMOpration[Operation][1];

    LcFlg = PackCRMCmd(DSIChlSEL,InData,&OutCRM_CMD);
    if(LcFlg != eRet_OK)
    {
        return LcFlg;
    }

    DSIReturnFlg = TransCRMCmd(DSIMasterID,DSIChlSEL,CRMCMD_Type,&OutCRM_CMD,NULL);

    if(DSIReturnFlg != eDSIReturnType_OK)
    {
        return eRet_ReqSPI_ERR;
    }
    
    return eRet_OK;
}

/***************************************************************************//**
 * @brief
 * 传输Elmos17 读IC寄存器请求
 *
 * @param       DSIMasterID   DSI master索引
 * @param		DSIChlSEL     通道选择
 * @param		SlaveID       探头物理地址
 * @param       cModAddr      模块地址
 * @param       cDataAddr     数据地址
 * @return      eRet_OK    OK else NG
 *
 *
 * ****************************************************************************/
Elmos17RetType_en TransElmos17ReadICRegReq(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL,uint8 SlaveID,uint8 cModAddr, uint8 cDataAddr)
{

    Elmos17RetType_en LcFlg = eRet_OK;
    DSIReturnType_en DSIReturnFlg = eDSIReturnType_OK;
    uint8 InData[3];
    CRMCMD_Type_en CRMCMD_Type = CRM2Response;

    TRANS_CRM_DM_Str OutCRM_CMD;
    uint8 cSnsPhysAddr;

    if((DSIMasterID >= DSIMasterNum) /*|| (SlaveID > DSISlaveSnsNum)*/)
    {
        return eRet_InDat_ERR;
    }
  
    cSnsPhysAddr= SlaveID;

    if(cSnsPhysAddr == 0)
    {
        /*广播地址,无CRM应答*/
         CRMCMD_Type = CRM_NResponse;
    }
    else
    {

    }


    InData[0] = (cSnsPhysAddr << 4) | Sns524_17_ICRegAccessCRMCMD;
    InData[1] = 0x00 | cModAddr;  /*0x00读数据请求*/
    InData[2] = cDataAddr;

    LcFlg = PackCRMCmd(DSIChlSEL,InData,&OutCRM_CMD);
    if(LcFlg != eRet_OK)
    {
        return LcFlg;
    }

    DSIReturnFlg = TransCRMCmd(DSIMasterID,DSIChlSEL,CRMCMD_Type,&OutCRM_CMD,NULL);

    if(DSIReturnFlg != eDSIReturnType_OK)
    {
        return eRet_ReqSPI_ERR;
    }
    
    return eRet_OK;
}

/***************************************************************************//**
 * @brief
 * 传输Elmos17 写IC寄存器请求
 *
 * @param       SnsID         探头ID
 * @param       cModAddr      模块地址
 * @param       cDataAddr     数据地址
 * @return      eRet_OK    OK else NG
 *
 *
 * ****************************************************************************/
Elmos17RetType_en TransElmos17WriteICRegReq(SnsID_en SnsID,uint8 cModAddr, uint8 cDataAddr)
{
    Elmos17RetType_en LcFlg = eRet_OK;
    DSIReturnType_en DSIReturnFlg = eDSIReturnType_OK;
    uint8 InData[3];

    TRANS_CRM_DM_Str OutCRM_CMD;

    CRMCMD_Type_en CRMCMD_Type = CRM2Response;

    DSIMasterID_en DSIMasterID;
    DSIChlSel_en DSIChlSEL;
    uint8 cSnsPhysAddr;

    if(SnsID >= SNSIDNum)
    {
        return eRet_InDat_ERR;
    }
    
    DSIMasterID = SnsAddCfg[SnsID].DSIMasterID;
    DSIChlSEL   = SnsAddCfg[SnsID].DSIChlSEL;
    cSnsPhysAddr= SnsAddCfg[SnsID].cSnsPhysAddr;

    if(cSnsPhysAddr == 0)
    {
        /*广播地址,无CRM应答*/
         CRMCMD_Type = CRM_NResponse;
    }
    else
    {

    }




    InData[0] = (cSnsPhysAddr << 4) | Sns524_17_ICRegAccessCRMCMD;
    InData[1] = 0x80 | cModAddr;  /*0x80 写数据请求*/
    InData[2] = cDataAddr;

    LcFlg = PackCRMCmd(DSIChlSEL,InData,&OutCRM_CMD);
    if(LcFlg != eRet_OK)
    {
        return LcFlg;
    }

    DSIReturnFlg = TransCRMCmd(DSIMasterID,DSIChlSEL,CRMCMD_Type,&OutCRM_CMD,NULL);

    if(DSIReturnFlg != eDSIReturnType_OK)
    {
        return eRet_ReqSPI_ERR;
    }
    
    return eRet_OK;
}

/***************************************************************************//**
 * @brief
 * 传输Elmos17 写IC寄存器数据
 *
 * @param       SnsID         探头ID
 * @param       wData         数据
 * @return      eRet_OK    OK else NG
 *
 *
 * ****************************************************************************/
Elmos17RetType_en TransElmos17WriteICRegData(SnsID_en SnsID,uint16 wData)
{
    Elmos17RetType_en LcFlg = eRet_OK;
    DSIReturnType_en DSIReturnFlg = eDSIReturnType_OK;
    uint8 InData[3];

    TRANS_CRM_DM_Str OutCRM_CMD;
    CRMCMD_Type_en CRMCMD_Type = CRM2Response;
    DSIMasterID_en DSIMasterID;
    DSIChlSel_en DSIChlSEL;
    uint8 cSnsPhysAddr;

    if(SnsID >= SNSIDNum)
    {
        return eRet_InDat_ERR;
    }
    
    DSIMasterID = SnsAddCfg[SnsID].DSIMasterID;
    DSIChlSEL   = SnsAddCfg[SnsID].DSIChlSEL;
    cSnsPhysAddr= SnsAddCfg[SnsID].cSnsPhysAddr;

    if(cSnsPhysAddr == 0)
    {
        /*广播地址,无CRM应答*/
         CRMCMD_Type = CRM_NResponse;
    }
    else
    {

    }





    /*填充Slave地址及CMD*/
    InData[0] = (cSnsPhysAddr << 4) | Sns524_17_ICRegWriteCRMCMD;
    InData[1] = (uint8)(wData >> 8);
    InData[2] = (wData & 0xFF);

    LcFlg = PackCRMCmd(DSIChlSEL,InData,&OutCRM_CMD);
    if(LcFlg != eRet_OK)
    {
        return LcFlg;
    }

    DSIReturnFlg = TransCRMCmd(DSIMasterID,DSIChlSEL,CRMCMD_Type,&OutCRM_CMD,NULL);


    if(DSIReturnFlg != eDSIReturnType_OK)
    {
        return eRet_ReqSPI_ERR;
    }
    
    return eRet_OK;
}

/***************************************************************************//**
 * @brief
 * 传输Elmos17 增益/驱动电流 增减指令
 *
 * @param       DSIMasterID    DSI Master ID
 * @param       DSIChlSEL      DSI通道选择
 * @param       SnsSel         探头ID选择
 * @param       AdjustSel      调节数据选择
 * @param       DataSymbol     调节数据符号
 * @param       AdjustData     调节数据
 
 * @return      eRet_OK    OK else NG
 *
 *
 * ****************************************************************************/
Elmos17RetType_en TransElmos17AdjustSensitivity(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL,uint8 SnsSel,Elmos17_AdjSel_e AdjustSel,Elmos17_AdjSymbol_e DataSymbol,uint8 AdjustData)
{
    Elmos17RetType_en LcFlg = eRet_OK;
    DSIReturnType_en DSIReturnFlg = eDSIReturnType_OK;
    uint8 InData[3];
    TRANS_CRM_DM_Str OutCRM_CMD;
    CRMCMD_Type_en CRMCMD_Type = CRM_NResponse;


    /*填充广播地址及CMD*/
    InData[0] = Sns524_17_AdjustCRMCMD;
    InData[1] = (uint8)((AdjustSel & 0x03) << 6) | (SnsSel & 0x3F);
    InData[2] = (uint8)((DataSymbol & 0x01) << 7) | (AdjustData & 0x7F);

    LcFlg = PackCRMCmd(DSIChlSEL,InData,&OutCRM_CMD);
    if(LcFlg != eRet_OK)
    {
        return LcFlg;
    }

    DSIReturnFlg = TransCRMCmd(DSIMasterID,DSIChlSEL,CRMCMD_Type,&OutCRM_CMD,NULL);


    if(DSIReturnFlg != eDSIReturnType_OK)
    {
        return eRet_ReqSPI_ERR;
    }
    
    return eRet_OK;
}

/***************************************************************************//**
 * @brief
 * 传输Elmos17 修改当前探头ID
 *
 * @param       DSICfg         DSI3通道配置
 * @param       CurrentSlaveID 当前探头ID
 * @param       NewSlaveID     新ID
 * @return      eRet_OK    OK else NG
 *
 *
 * ****************************************************************************/

Elmos17RetType_en TransElmos17ChangeSlaveID(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL,uint8 CurrentSlaveID,uint8 NewSlaveID)
{
    Elmos17RetType_en LcFlg = eRet_OK;
    DSIReturnType_en DSIReturnFlg = eDSIReturnType_OK;
    uint8 InData[3];

    TRANS_CRM_DM_Str OutCRM_CMD;

    /*修改探头地址*/
    InData[0] = (CurrentSlaveID << 4) | Sns524_17_IDAccessCRMCMD;
    InData[1] = 0x07; /*修改Slave ID*/
    InData[2] = NewSlaveID;

    LcFlg = PackCRMCmd(DSIChlSEL,InData,&OutCRM_CMD);
    if(LcFlg != eRet_OK)
    {
        return LcFlg;
    }
    
    DSIReturnFlg = TransCRMCmd(DSIMasterID,DSIChlSEL,CRM2Response,&OutCRM_CMD,NULL);

    if(DSIReturnFlg != eDSIReturnType_OK)
    {
        return eRet_ReqSPI_ERR;
    }
    
    return eRet_OK;
}

/***************************************************************************//**
 * @brief
 * 传输Elmos17 CRM模式 读状态数据
 *
 * @param       SnsID         探头ID
 * @param       StatusAddr   状态地址
 * @return      eRet_OK    OK else NG
 *
 *
 * ****************************************************************************/
Elmos17RetType_en TransElmos17ReadStatusInCRMReq(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL,uint8 SlaveID,Elmos17_CRMReadStatusAddr_e StatusAddr)
{
    Elmos17RetType_en LcFlg = eRet_OK;
    DSIReturnType_en DSIReturnFlg = eDSIReturnType_OK;
    uint8 InData[3];
    CRMCMD_Type_en CRMCMD_Type = CRM2Response;

    TRANS_CRM_DM_Str OutCRM_CMD;

    InData[0] = (SlaveID << 4) | Sns524_17_ReadStatusCRMCMD;
    InData[1] = 0x00;
    InData[2] = StatusAddr;

    LcFlg = PackCRMCmd(DSIChlSEL,InData,&OutCRM_CMD);
    if(LcFlg != eRet_OK)
    {
        return LcFlg;
    }

    DSIReturnFlg = TransCRMCmd(DSIMasterID,DSIChlSEL,CRMCMD_Type,&OutCRM_CMD,NULL);


    if(DSIReturnFlg != eDSIReturnType_OK)
    {
        return eRet_ReqSPI_ERR;
    }
    
    return eRet_OK;
}

/***************************************************************************//**
 * @brief
 * 传输Elmos17 PDCM模式 读状态数据
 *
 * @param       SnsID         探头ID
 * @param       wStatusSel    状态选择
 * @return      eRet_OK    OK else NG
 *
 *
 * ****************************************************************************/
Elmos17RetType_en TransElmos17ReadStatusInPDCMReq(SnsID_en SnsID,uint16 wStatusSel)
{
    Elmos17RetType_en LcFlg = eRet_OK;
    DSIReturnType_en DSIReturnFlg = eDSIReturnType_OK;
    uint8 InData[3];
    CRMCMD_Type_en CRMCMD_Type = CRM2PDCM;

    TRANS_CRM_DM_Str OutCRM_CMD;
    uint16 BRCNum = 0;
    
    DSIMasterID_en DSIMasterID;
    DSIChlSel_en DSIChlSEL;
    uint8 cSnsPhysAddr;
	uint8 Lu8Idx = 0;

    if(SnsID >= SNSIDNum)
    {
        return eRet_InDat_ERR;
    }
    
    DSIMasterID = SnsAddCfg[SnsID].DSIMasterID;
    DSIChlSEL   = SnsAddCfg[SnsID].DSIChlSEL;
    cSnsPhysAddr= SnsAddCfg[SnsID].cSnsPhysAddr;

	for(Lu8Idx = 0; Lu8Idx < 16; Lu8Idx++)
	{
		if((wStatusSel >> Lu8Idx) & 0x0001)
		{
	    	BRCNum++;
		}
	}

    InData[0] = (cSnsPhysAddr << 4) | Sns524_17_ReadStatusPDCMCMD;
    InData[1] = (uint8)(wStatusSel >> 8);
    InData[2] = (wStatusSel & 0xFF);

    LcFlg = PackCRMCmd(DSIChlSEL,InData,&OutCRM_CMD);
    if(LcFlg != eRet_OK)
    {
        return LcFlg;
    }
    else
    {
        SetAutoBRCPart(DSIMasterID,DSIChlSEL,BRCNum);

    }

    DSIReturnFlg = TransCRMCmd(DSIMasterID,DSIChlSEL,CRMCMD_Type,&OutCRM_CMD,NULL);

    if(DSIReturnFlg != eDSIReturnType_OK)
    {
        return eRet_ReqSPI_ERR;
    }

   
    return eRet_OK;
}

/***************************************************************************//**
 * @brief
 * 传输Elmos17 DSI时钟同步空数据
 *
 * @param       无
 * @return      eRet_OK    OK else NG
 *
 *
 * ****************************************************************************/
Elmos17RetType_en TransElmos17DSIClockSyncData(DSIMasterID_en DSIMasterID)
{
    Elmos17RetType_en LcFlg = eRet_OK;
    DSIReturnType_en DSIReturnFlg = eDSIReturnType_OK;
    uint8 InData[3];

    TRANS_CRM_DM_Str OutCRM_CMD;

    InData[0] = 0;
    InData[1] = 0;
    InData[2] = 0;

    LcFlg = PackCRMCmd(DSIChlSEL1And2,InData,&OutCRM_CMD);
    if(LcFlg != eRet_OK)
    {
        return LcFlg;
    }
    DSIReturnFlg = TransCRMCmd(DSIMasterID,DSIChlSEL1And2,CRM_NResponse,&OutCRM_CMD,NULL);

    if(DSIReturnFlg != eDSIReturnType_OK)
    {
        return eRet_ReqSPI_ERR;
    }

    return eRet_OK;
}


/***************************************************************************//**
 * @brief
 * 传输Elmos17 停止测量命令
 *
 * @param       SnsID         探头ID
 * @return      eRet_OK    OK else NG
 *
 *
 * ****************************************************************************/
Elmos17RetType_en TransElmos17StopMeasCMD(SnsID_en SnsID)
{
    Elmos17RetType_en LcFlg = eRet_OK;
    DSIReturnType_en DSIReturnFlg = eDSIReturnType_OK;
    uint8 InData[3];
    uint16 wData;
    
    CRMCMD_Type_en CRMCMD_Type = CRM_NResponse;
    TRANS_CRM_DM_Str OutCRM_CMD;
    DSIMasterID_en DSIMasterID;
    DSIChlSel_en DSIChlSEL;
    uint8 cSnsPhysAddr;

    if(SnsID >= SNSIDNum)
    {
        return eRet_InDat_ERR;
    }
    
    DSIMasterID = SnsAddCfg[SnsID].DSIMasterID;
    DSIChlSEL   = SnsAddCfg[SnsID].DSIChlSEL;
    cSnsPhysAddr= SnsAddCfg[SnsID].cSnsPhysAddr;



    
    
    if(SnsID == SNS_M1_ALL)
    {
        wData = 0xFFF0;
    }
    else
    {
        if(SnsID <= SNS_GROUP_SIZE)
        {
            wData = 0x03 << SnsID ;
        }
        else
        {
            wData = 0x03 << (SnsID - SNS_GROUP_SIZE);
        }
    }
    
    InData[0] = (cSnsPhysAddr << 4) | Sns524_17_STOPMEASCRMCMD;
    InData[1] = wData >> 8;
    InData[2] = wData & 0xFF;

    LcFlg = PackCRMCmd(DSIChlSEL1And2,InData,&OutCRM_CMD);
    if(LcFlg != eRet_OK)
    {
        return LcFlg;
    }

    DSIReturnFlg = TransCRMCmd(DSIMasterID,DSIChlSEL,CRMCMD_Type,&OutCRM_CMD,NULL);


    if(DSIReturnFlg != eDSIReturnType_OK)
    {
        return eRet_ReqSPI_ERR;
    }
    
    return eRet_OK;
}




/***************************************************************************//**
 * @brief
 * 打包探测指令
 *
 * @param       pInElmos17MeasCMDMsg   测量命令信息
 * @param       pOutCMDBuff            打包后的CMD数据
 
 * @return      eRet_OK    OK else NG
 *
 *
 * ****************************************************************************/
Elmos17RetType_en PackMeasCMD(Elmos17MeasCMDMsg_st *pInElmos17MeasCMDMsg,CRM_CMD_Str *pOutCMDBuff)
{
    uint8 i;
    if((pInElmos17MeasCMDMsg == NULL) || (pOutCMDBuff == NULL))
    {
        return eRet_InDat_ERR;
    }
    pOutCMDBuff->Addr_CMD = 0x00 | pInElmos17MeasCMDMsg->MeasType;
    pOutCMDBuff->Data[0] = 0;
    pOutCMDBuff->Data[1] = 0;
    if(IMPEDANCE_MEAS == pInElmos17MeasCMDMsg->MeasType)
    {
        pOutCMDBuff->Data[1] = 5;
    }
    else
    {
        pOutCMDBuff->Data[1] = (pInElmos17MeasCMDMsg->MeasDataMode << 2) | pInElmos17MeasCMDMsg->profile;
    }
    for(i=0;i<2;i++)
    {
        pOutCMDBuff->Data[1] |= MeasTypeList[pInElmos17MeasCMDMsg->SNsMeasType[i]] << (i*2 + 4);
    }
    for(i=2;i<DSISlaveSnsNum;i++)
    {
        pOutCMDBuff->Data[0] |=  MeasTypeList[pInElmos17MeasCMDMsg->SNsMeasType[i]] << ((i-2)*2);
    }

    return eRet_OK;
}


/***************************************************************************//**
 * @brief
 * 打包探测指令
 *
 * @param       DSIMasterID   
 * @param       pChl1Elmos17MeasCMDMsg   DSI1测量命令信息
 * @param       pChl1Elmos17MeasCMDMsg   DSI2测量命令信息
 * @return      eRet_OK    OK else NG
 *
 *
 * ****************************************************************************/

Elmos17RetType_en TransElmos17MeasCmd(DSIMasterID_en DSIMasterID ,Elmos17MeasCMDMsg_st *pChl1Elmos17MeasCMDMsg,Elmos17MeasCMDMsg_st *pChl2Elmos17MeasCMDMsg)
{
    Elmos17RetType_en LcFlg = eRet_OK;
    DSIReturnType_en DSIReturnFlg = eDSIReturnType_OK;
    TRANS_CRM_DM_Str OutCRM_CMD;
    
    DSIChlSel_en DSIChlSEL = DSI_CHL_NONE;
    uint32 CRMTransCompleteTime = 0;
		
    if(pChl1Elmos17MeasCMDMsg != NULL)
    {
        DSIChlSEL |= 1;
        
        LcFlg = PackMeasCMD(pChl1Elmos17MeasCMDMsg,&OutCRM_CMD.CRMData[0]);
        if(LcFlg != eRet_OK)
        {
            return LcFlg;
        }
    }
    else
    {
    }
    
    if(pChl2Elmos17MeasCMDMsg != NULL)
    {
        DSIChlSEL |= 2;
        
        LcFlg = PackMeasCMD(pChl2Elmos17MeasCMDMsg,&OutCRM_CMD.CRMData[1]);
        
        if(LcFlg != eRet_OK)
        {
            return LcFlg;
        }
    }
    else
    {
    }
    
    if(DSIChlSEL == DSI_CHL_NONE)
    {
        return eRet_InDat_ERR;
    }

    DSIReturnFlg = TransCRMCmd(DSIMasterID,DSIChlSEL,CRM2PDCM,&OutCRM_CMD,&CRMTransCompleteTime);

    if(DSIReturnFlg != eDSIReturnType_OK)
    {
        return eRet_ReqSPI_ERR;
    }

	if(DSIChlSEL & 0x01)
	{
		/** @brief 设置命令传输时间 */
        SetTransMeasCmdTime(DSIMasterID,DSIChl1,CRMTransCompleteTime);
	}

	if(DSIChlSEL & 0x02)
	{
		/** @brief 设置命令传输时间 */
        SetTransMeasCmdTime(DSIMasterID,DSIChl2,CRMTransCompleteTime);
	}
	
    return eRet_OK;
}


/***************************************************************************//**
 * @brief
 * 打包探测指令SPI异步发送
 *
 * @param       DSIMasterID   
 * @param       pChl1Elmos17MeasCMDMsg   DSI1测量命令信息
 * @param       pChl1Elmos17MeasCMDMsg   DSI2测量命令信息
 * @return      eRet_OK    OK else NG
 *
 *
 * ****************************************************************************/
Elmos17RetType_en TransElmos17MeasCmdSPIAsyn(DSIMasterID_en DSIMasterID ,Elmos17MeasCMDMsg_st *pChl1Elmos17MeasCMDMsg,Elmos17MeasCMDMsg_st *pChl2Elmos17MeasCMDMsg)
{
    Elmos17RetType_en LcFlg = eRet_OK;
    DSIReturnType_en DSIReturnFlg = eDSIReturnType_OK;
    TRANS_CRM_DM_Str OutCRM_CMD;
    
    DSIChlSel_en DSIChlSEL = DSI_CHL_NONE;
    
    if(pChl1Elmos17MeasCMDMsg != NULL)
    {
        DSIChlSEL |= 1;
        
        LcFlg = PackMeasCMD(pChl1Elmos17MeasCMDMsg,&OutCRM_CMD.CRMData[0]);
        if(LcFlg != eRet_OK)
        {
            return LcFlg;
        }
    }
    else
    {
    }
    
    if(pChl2Elmos17MeasCMDMsg != NULL)
    {
        DSIChlSEL |= 2;
        
        LcFlg = PackMeasCMD(pChl2Elmos17MeasCMDMsg,&OutCRM_CMD.CRMData[1]);
        
        if(LcFlg != eRet_OK)
        {
            return LcFlg;
        }
    }
    else
    {
    }
    
    if(DSIChlSEL == DSI_CHL_NONE)
    {
        return eRet_InDat_ERR;
    }

    DSIReturnFlg = TransCRMCmdSPIAsyn(DSIMasterID,DSIChlSEL,CRM2PDCM,&OutCRM_CMD);

    if(DSIReturnFlg != eDSIReturnType_OK)
    {
        return eRet_ReqSPI_ERR;
    }

    return eRet_OK;
}

Elmos17RetType_en CheckCRMResStatus(CRM_RESPONSE_Data_str *pResOutData,uint8 SlaveID)
{
    /** @brief 检查应答数据是否有效 */
    if((pResOutData->ResData.DSIStatus.Status == 0) 
        && (pResOutData->ResData.DSIStatus.SymbolCount == 8))
    {
        if(((pResOutData->ResData.Data.PhysAddr_Status & 0x03) == 0x01) 
            && (((pResOutData->ResData.Data.PhysAddr_Status >> 4) & 0x0F) == SlaveID))
        {
            /** @brief 应答正确 */
            return eRet_OK;
        }
        else
        {
            /** @brief 应答错误 */
    
        }
    }
    else
    {
        /** @brief 应答错误 */
    
    }
    
    return eRet_N_OK;

}

/*  */

