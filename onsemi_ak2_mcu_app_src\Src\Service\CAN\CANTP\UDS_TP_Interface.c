/**********************************************************************
*   版权所有    : 2017 深圳市豪恩汽车电子装备有限公司
*   项目名称    : TP_LIB
*   M C U       : RL78
*   编译环境    : IAR
*   文件名称    : UDS_TP_Interface.c
*   其它说明    : 应用层与TP层接口
*   当前版本    : V0.1
*   作    者  : 20460
*   完成日期    :   
*   内容摘要    :
*   修改记录1   :
*   修改日期    :
*   版 本 号 :
*   修 改 人 :
*   修改内容    :
*   修改记录2   :

***********************************************************************/
/***********************************************************************
* Includes 
***********************************************************************/
#include "TP_Manage.h"
#include "UDS_TP_Interface.h"
#include "TP_Config.h"
/***********************************************************************
* Global objects 
************************************************************************/
volatile uint8 GcUdsACKType;

/*********************************************************************
* 函数名称: TP_UDSParaInit
*
* 功能描述: Tp_UDS层参数初始化
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明: 把用户定义的变量连接至TP库中                       
*
* 修改日期      版本号       修改人       修改内容

**********************************************************************/
uint8 TP_UDSParaInit(TPUDSStr *LsTPUDSParaCfg)
{
    uint8 LcInitState = INIT_OK;

    if((LsTPUDSParaCfg->GcFunctionChannelRxCompleteFlag != NULL)&&(LsTPUDSParaCfg->GcPhysicalChannelRxCompleteFlag != NULL)\
        &&(LsTPUDSParaCfg->GwPhysicalChannelRxLen != NULL)&&(LsTPUDSParaCfg->GcFunctionChannelRxLen != NULL)&&(LsTPUDSParaCfg->GdSystemTimer != NULL))
    {
        GsUDS_TP_Interface.GcFunctionChannelRxCompleteFlag = LsTPUDSParaCfg->GcFunctionChannelRxCompleteFlag;
        GsUDS_TP_Interface.GcPhysicalChannelRxCompleteFlag = LsTPUDSParaCfg->GcPhysicalChannelRxCompleteFlag;
        
        GsUDS_TP_Interface.GwPhysicalChannelRxLen =  LsTPUDSParaCfg->GwPhysicalChannelRxLen;
        GsUDS_TP_Interface.GcFunctionChannelRxLen = LsTPUDSParaCfg->GcFunctionChannelRxLen;
        
        GsUDS_TP_Interface.GcP2CAN_Server = LsTPUDSParaCfg->GcP2CAN_Server;
        GsUDS_TP_Interface.GwP2ExtendCAN_Server = LsTPUDSParaCfg->GwP2ExtendCAN_Server;
        GsUDS_TP_Interface.GdSystemTimer = LsTPUDSParaCfg->GdSystemTimer;

        GwWaitRespondTimer = 0;           /* 初始化不需要等待UDS响应的时间 */
    }
    else
    {
        LcInitState = TP_UDS_INIT_FAIL;
    }
    return LcInitState;
    
}


/*********************************************************************
 * 函数名称:  UDS_TP_RespondManage
 *
 * 功能描述:  UDS响应
 *                 
 * 输入参数:  请求报文地址和长度
 *
 * 输出参数:  无
 *
 * 其它说明:  
 * 
 * 修改日期     版本号       修改人       修改内容
 *
 ********************************************************************/
uint8 UDS_TP_RespondManage(uint8 LcUdsStatus, uint16 LwLen, uint8 *pUdsData)
{

    uint8 LcState = TRUE;
    
    GcUdsACKType = LcUdsStatus;                                   /* 用于记录UDS响应的状态 */
    
    if(LcUdsStatus == UDS_NRC_78)                                 /* 忙响应 */
    {
        (void)TP_RxEnable(PHYSICALCHANNEL, TP_RX_ENABLE_OFF);     /* 锁住TP层功能寻址通道 */
        (void)TP_RxEnable(FUNCTIONALCHANNEL, TP_RX_ENABLE_OFF);   /* 锁住当前诊断使用通道 */
        
        GwWaitRespondTimer = GsUDS_TP_Interface.GwP2ExtendCAN_Server;
    }
    else                                                          /* 否定响应或肯定 */
    {
         GwWaitRespondTimer = 0;
        (void)TP_RxEnable (PHYSICALCHANNEL, TP_RX_ENABLE_ON);     /* UDS数据已经处理完成 */
        (void)TP_RxEnable (FUNCTIONALCHANNEL, TP_RX_ENABLE_ON);
    }

    if(LwLen > (uint8)0)
    {
        GsTpTxMsg.pTxData = pUdsData;                            /* 复制诊断响应数据至TP层 */
        GsTpTxMsg.GwTxLen = LwLen;
        
        (void)TP_SendMsg ();                                      /* 发送诊断响应消息 */
    }
    
    return LcState;
}

