#ifndef   __DMA_CFG_H__
#define   __DMA_CFG_H__
 
/******************************************************************************* 
* Includes 
********************************************************************************/ 
#include "types.h"
/****************************************************************************** 
* Constants and macros 
*******************************************************************************/

/******************************************************************************
* 设计描述 : CSIH有效数据长度类型定义
* 设计索引 : 
*******************************************************************************/

typedef enum
{
    CSIH0_MASTER = 0x0u,
    CSIH1_MASTER,
	CSIH2_MASTER,
	CSIH3_MASTER
}CSIHMaterCfg;

typedef struct
{
    uint16 CSIHTxDataBuff[72];
    uint16 CSIHRxDataBuff[72];
    // uint16 CSIHDataLen;
	// CSIHMaterCfg CSIHMaster;
	uint16 CSIHFlg;
}CSIHTxRxTYPE;


/********RX Frame****************************************/


/********Tx Frame*****************************************/


/****************************************************************************** 
* External objects 
********************************************************************************/ 

/******************************************************************************* 
* Global Functions 
********************************************************************************/ 

#endif
