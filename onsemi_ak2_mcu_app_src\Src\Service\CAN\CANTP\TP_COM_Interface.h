#ifndef __TP_COM_INTERFACE_H__
#define __TP_COM_INTERFACE_H__
/*******************************************************************************
* Includes
********************************************************************************/


/******************************************************************************
* Constants and macros
*******************************************************************************/
#define COM_DATA_MAX_DIAG                         64U            /* 一帧报文的最大字节个数 */
#define COM_DATA_MIN_DIAG                         8U            /* 一帧报文的最大字节个数 */

/* COM functions status */
#define COM_ERR_BASE                        0x10U
#define COM_ERR_OK                          0x00U
#define COM_ERR_OFF                        ( COM_ERR_BASE + 0x01U )
#define COM_ERR_IDX                        ( COM_ERR_BASE + 0x03U )
#define COM_ERR_FRAME_MODE                 ( COM_ERR_BASE + 0x04U )
#define COM_ERR_TX_MSG_LOST                ( COM_ERR_BASE + 0x06U )
#define COM_ERR_NULL                       ( COM_ERR_BASE + 0x09U )


/******************************************************************************
* External objects
********************************************************************************/




/*******************************************************************************
* Global Functions
********************************************************************************/
uint8 TP_COM_SendFrameDataReq (const uint8 *pData);
uint8 COM_GetFrameData (uint8 LcChannel,uint8 *pData);

#endif
