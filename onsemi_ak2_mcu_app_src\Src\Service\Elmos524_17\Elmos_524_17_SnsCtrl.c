/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * Elmos_524_17.c:
 * Created on: 2019-11-01 15:04
 * Original designer:
 ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "DSI3_COM.h"
#include "Elmos_524_17_SnsCtrl.h"
#include "Elmos_524_17_Private.h"
#include "Elmos17SnsMeasParamType.h"
#include "Dsi_SPI_Callback.h"
#include "Queue_CRMResponse.h"
#include "SnsDiag.h"
#include "AK2_MCU_Drv.h"

/* Macro ----------------------------------------------------------------------*/
#define FIX_FREQUENCY_SEQ          0
#define UPADTE_SNS_PARAM           0
#define PWR_SW_DELAY_TIME  (80U)    /*uint: ms*/
#define WAIT_SNS_PWR_ON    (45U)    /*uint: ms*/
#define LOW_SPEED_SEQ_ON_SPEED     (350U)    /*uint: 0.01km/h*/
#define LOW_SPEED_SEQ_OFF_SPEED    (400U)    /*uint: 0.01km/h*/
#define MIDDLE_SPEED_SEQ_ON_SPEED  (1250U)    /*uint: 0.01km/h*/
#define MIDDLE_SPEED_SEQ_OFF_SPEED (1550U)    /*uint: 0.01km/h*/
#define HIGH_SPEED_SEQ_ON_SPEED	   (2550U)    /*uint: 0.01km/h*/
#define HIGH_SPEED_SEQ_OFF_SPEED   (3050U)    /*uint: 0.01km/h*/
#define SNS_TEMPCOMP_PERIOD       (10000U)	 /*uint: ms*/
#define SNS_REDMCHECK_PERIOD 	  (10000U)    /*uint: ms*/
#define ALL_SNS_SELECT     (0x3F)
#define WAIT_SNS_ID_TO_EEPROM            (10U)  /*uint: ms*/
#define WAIT_SNS_PARAM_RAM_TO_EEPROM     (750U)  /*uint: ms*/
#define WAIT_SNS_PARAM_EEPROM_TO_RAM     (10U)  /*uint: ms*/
#define WAIT_SNS_POWER_OFF               (80U)  /*uint: ms*/
#define Daisy_chainIdInversion 0
#define PARALLEL_DM_METHOD1		1
#define SNS_DEFAULT_ID     		(7U)
#define WRITE_SNS_EEPROM_RETRY_CNT     	 (3U)
#define DM_RETRY_CNT     	 			 (2U)
#define HIGH_SPEED_USE_FIX_FRE     1

static uint8 Elmos524_17_WtiteGain(void);

/* Typedef --------------------------------------------------------------------*/
typedef enum
{
	SnsParamCfgInit = 0,
#if 0
	ReadSnsParamAfterPwrOn,
	CheckSnsParamAfterPwrOn,
#endif
    ReadSnsParamID,   
	HandleReadSnsParamIDRes,
	ReadSnsGain,
	HandleReadSnsGainRes,
	WriteSnsParam,
	ReadSnsParam,
	CheckSnsParam,
	EnEEPROMPrg,
	HandleEnEEPROMPrgRes,
	WriteSnsParamToEEPROM,
	HandleWriteSnsParamToEEPROMRes,
	WaitParamSaveToEEPROM,
	CopyParamEEPROMToRam,
	HandleCopyParamEEPROMToRamRes,
	WaitParamEEPROMToRam,
	ReadSnsParamAfterWrite,
	CheckSnsParamAfterWrite,
	PrepareNextSnsParamCfg,
	SnsParamCfgEnd
}SnsParamCfgSeq_en;

typedef struct
{
	SnsParamCfgSeq_en SnsParamCfgSeq;
	SnsParamCfgSeq_en LastSeq;
	uint16 SnsParamNeedUpdateFlg;   /*force update param*/
	uint16 SnsParamCfgFlg;
	uint16 SnsParamID[SNSNum];
}SnsParamCfgCtrl;

typedef enum
{
    eElmos17DMSeq_Init = 0,
    eElmos17DMSeq_StartAutoDM,
    eElmos17DMSeq_TransChangeID,
    eElmos17DMSeq_CheckChangeIDRes,
    eElmos17DMSeq_TransReadEEPROMID,
    eElmos17DMSeq_CheckReadEEPROMIDRes,
    eElmos17DMSeq_EnEEPROMPrg,
    eElmos17DMSeq_CheckEnEEPROMPrgRes,
	eElmos17DMSeq_WriteIDToEEPROM,
    eElmos17DMSeq_CheckWriteIDToEEPROMRes,
    eElmos17DMSeq_PrepareNextDM,
    eElmos17DMSeq_Check_TransCRMReadStatus,
    eElmos17DMSeq_Check_ReadStatusRes,
    eElmos17DMSeq_End,
}Elmos17DMSeq_en;

typedef union
{
    uint16 WData[Elmos17MEAS_ALLParam_u16Len];
    uint8 cData[Elmos17MEAS_ALLParam_u16Len*2];
}Elmos17SnsMeasEEPROMSpace_u;

typedef enum
{
	GetNoSigSnsInfo = 0,
	GetSnsSoftwareVer,
	CheckGetSnsSoftwareVerRes,
	SnsReDMCheckSeqEnd,
	SnsReDMCheckSeqNum
}SnsReDMCheckSeq_en;

typedef struct
{
	SnsReDMCheckSeq_en SnsReDMCheckSeq;
	bool StartTime1msCnt;
	uint32 ReDMCheckTime1msCnt;
}SnsReDMCheckCtrl;

/* Variables ------------------------------------------------------------------*/
SnsCtrlGroup_Cfg SnsCtrlGroup;
SnsParamCfgCtrl GstrSnsParamCfgCtrl = 
{
	.SnsParamCfgSeq = SnsParamCfgInit,
	.LastSeq = SnsParamCfgInit,
	.SnsParamNeedUpdateFlg = 0,
    .SnsParamCfgFlg = 0,
	.SnsParamID[SNS_FL_S] = 0x0008,
	.SnsParamID[SNS_FL] = 0x0010,
	.SnsParamID[SNS_FML] = 0x0018,
	.SnsParamID[SNS_FMR] = 0x0018,
	.SnsParamID[SNS_FR] = 0x0010,
	.SnsParamID[SNS_FR_S] = 0x0008,
	.SnsParamID[SNS_RR_S] = 0x0020,
	.SnsParamID[SNS_RR] = 0x0028,
	.SnsParamID[SNS_RMR] = 0x0030,
	.SnsParamID[SNS_RML] = 0x0030,
	.SnsParamID[SNS_RL] = 0x0028,
	.SnsParamID[SNS_RL_S] = 0x0020,	
};
const int8 GTempGainCompTable[54]= {
	/*-40  -38  -36  -34  -32  -30  -28  -26  -24  -22*/
	    7,   6,   5,   4,   3,   1,   1,   0,  -1,  -2, \
	/*-20  -18  -16  -14  -12  -10   -8   -6   -4   -2 */
	   -2,  -3,  -4,  -5,  -5,  -6,  -6,  -6,  -6,  -6, \
	/*0      2    4    6    8   10   12   14   16   18 */
	   -7,  -7,  -6,  -6,  -5,  -6,  -5,  -4,  -4,  -2, \
	/*20    22   24   26   28   30   32   34   36   38 */
	   -1,  -1,   0,   0,   0,   0,   0,   0,   0,   0, \
	/*40    42   44   46   48   50   52   54   56   58 */
	    0,   0,   0,   2,   4,   5,   5,   5,   6,   6, \
	/*60    62   64   66                               */
	    6,   6,   7,   7,   \
};
	
SnsReDMCheckCtrl GstrSnsReDMCheckCtrl = 
{
	.SnsReDMCheckSeq = GetNoSigSnsInfo,
	.StartTime1msCnt = FALSE,
	.ReDMCheckTime1msCnt = 0,
};

Elmos17DMSeq_en Elmos17DMSeq = eElmos17DMSeq_Init;
uint8 TestErrCntSeq[25] = {0};
Elmos17SnsMeasEEPROMSpace_u WSnsEEPROMBuf;
Elmos17SnsMeasEEPROMSpace_u RSnsEEPROMBuf;
uint16 RWReturnFlg[DSIMasterNum] = {0,0};
WRSnsMeasParam_str WRSnsMeasParam;
uint8 DMRetryCnt = DM_RETRY_CNT;
uint8 StandbyFlg = 0;
uint16 TestDMFlg = 0;
SnsMeasDistData_Str SnsMeasDistData[SNSNum];
uint8 MeasSeqIndex = 0;
uint8 RWSNSEndFlg[DSIMasterNum] = {1,1};
uint32 lastTime = 0;
uint32 currentTime = 0;
uint8 Gu8DVWorkFlg = 0;/*0:正常工作模式 1:DV工作模式*/

uint16 SnsWriteStartFlg = 0;
uint8 GuGainDataBuf[24] ={0};
uint8 u8SnsGain[SNSNum] = {0};

/***************************************************************************//**
 * @brief
 * Init Sns CtrlGroup
 *
 * @param       
 * @return      0
 *
 * ****************************************************************************/
void InitSnsCtrlGroup(void)
{
	DSIMasterID_en DSIMasterIdx = DSIMaster0;
	DSIChlID_en DSIChlIdx = DSIChl1; 
	
	memset(&SnsCtrlGroup,0,sizeof(SnsCtrlGroup_Cfg));
	SnsCtrlGroup.GroupStatus = GroupStatus_SNS_DM;
    SnsCtrlGroup.Time1msCnt = 0; 
	SnsCtrlGroup.TempCompTime1msCnt = SNS_TEMPCOMP_PERIOD;
	SnsCtrlGroup.StartMeasFlg = TRUE;
	
	for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
	{
		for(DSIChlIdx = DSIChl1;DSIChlIdx < DSIChlNum; DSIChlIdx++)
		{
			SnsCtrlGroup.MeasTimeOut[DSIMasterIdx][DSIChlIdx] = 0;
			SnsCtrlGroup.SnsGroupMeasStatus[DSIMasterIdx][DSIChlIdx] = SnsGroupMeas_IDLE;
		}
		InitDSIWork(DSIMasterIdx,(DSICOM_SetParam_st*)&DSICOM_Param[DSIMasterIdx]);
	}

    InitDSI_SPI_RX_Que();
	SnsDiag_Init();
}

SnsGroupStatus_en SetNextSnsCtrlGroupStatus(SnsGroupStatus_en NextGroupStatus)
{
    
    SnsCtrlGroup.NextGroupStatus = NextGroupStatus;


    return NextGroupStatus;
}

SnsGroupStatus_en GetNextSnsCtrlGroupStatus()
{

    return SnsCtrlGroup.NextGroupStatus;
}


uint8 ResetSnsCtrlGroupStatus()
{
    SnsCtrlGroup.GroupStatus = GroupStatus_SNS_DM;
    SnsCtrlGroup.NextGroupStatus = GroupStatus_START_MEAS;

    return 0;
}

SnsGroupStatus_en GetSnsCtrlGroupStatus()
{

    return SnsCtrlGroup.GroupStatus;
}

uint8 SetSnsCtrlGroupStatus(SnsGroupStatus_en SnsGroupStatus)
{
    if(SnsGroupStatus > GroupStatus_START_MEAS)
    {
        return 0xFF;
    }
    if(SnsCtrlGroup.GroupStatus == GroupStatus_SNSStandby) 
    { 
        if(SnsGroupStatus != GroupStatus_SNSStandby)
        {
            SnsCtrlGroup.GroupStatus = GroupStatus_SNSWakeUp;
            if(SnsGroupStatus == GroupStatus_SNSWakeUp)
            {
                SetNextSnsCtrlGroupStatus(GroupStatus_START_MEAS);
            }
            else
            {
                SetNextSnsCtrlGroupStatus(SnsGroupStatus);
            }
        }
   
    }
    else
    {
        if(SnsCtrlGroup.GroupStatus != GroupStatus_START_MEAS)
        {
          return 0xFF;
        }
        else
        {
            SnsCtrlGroup.GroupStatus = SnsGroupStatus;
        }
   
    }

    
    return 0;
}

 uint32 CalcTimeInterval(uint32 startTime,uint32 endTime)
 {
	 uint32 timeInterval = 0U;
	 
	 if(endTime >= startTime)
	 {
		 timeInterval = endTime -startTime;
	 }
	 else
	 {
		 timeInterval = (UINT32_MAX - startTime) + endTime;
	 }
 
	 return timeInterval;
 }

 /******************************************************************************/
 /**
  * @brief	 计算两个521.42启动发波的时间差
  *          RH850 us计数器为uint16,且步进为1.6us
  */
 /*****************************************************************************/
 uint32 CalcMasterStartMeasTimeInterval(uint32 startTime,uint32 endTime)
 {
	 uint32 timeInterval = 0U;
	 
	 if(endTime >= startTime)
	 {
		 timeInterval = endTime -startTime;
	 }
	 else
	 {
		 timeInterval = (UINT16_MAX - (uint16)startTime) + (uint16)endTime;
	 }
 
	 return (uint32)((float)timeInterval * 1.6f);
 }

uint8 Elmos524_17_DMSeq(uint16 *pSnsDMFlg)
{
    DSISPI_SeqID SeqID;
    uint8 Lcnt = 0;
    //DSIMasterID_en DSIMasterID = DSIMaster0;	
    DSIMasterID_en DSIMasterIdx = DSIMaster0;
    DSIChlSel_en DSIChlSEL = DSIChlSEL1And2;
	static uint8 DSIMasterDone = 0;
    uint16 SnsENIOVlaue = 0;
    uint16 Flg = 0xFFFF;
	static bool DMSeqFlg = FALSE;
    CRM_RESPONSE_Data_str CRMRESOutData;
    
    static uint8 ScIOCnt = 0;
    SnsID_en SnsID;
	static uint32 PwrSwDelayStartTime = 0;
	static bool PwrSwFlg = FALSE;
	static uint32 PwrSwDelayTime = 0;	
    SnsCtrlGroup_Cfg *pSnsCtrlGroup = &SnsCtrlGroup;
	uint32 DelayTime = 0;
	static bool DMDone = FALSE;
 
    switch(Elmos17DMSeq)
    {
		
#if Daisy_chainIdInversion	

        case eElmos17DMSeq_Init:
        {
			SnsENIOVlaue = (uint16)(((uint16)1U << eSNS_GROUP1_POWER_EN_PIN) | ((uint16)1U << eSNS_GROUP2_POWER_EN_PIN) | ((uint16)1U << eSNS_GROUP3_POWER_EN_PIN));
            AK2_WriteEnSnsPin(SnsENIOVlaue);/** @brief  使能所有探头电源 */
 
            TransStopDSI(DSIMasterID,DSIChlSEL);
            InitCRMRESQueue(DSIMasterID);
            ScIOCnt = 0;
            Elmos17DMSeq = eElmos17DMSeq_StartAutoDM;
            *pSnsDMFlg = 0;
            break;
        }

        case eElmos17DMSeq_StartAutoDM:
        {
            if((GetDSICHLWorkStatus(DSIMasterID,DSIChl1) == DSICHL_IDLE)
                &&(GetDSICHLWorkStatus(DSIMasterID,DSIChl2) == DSICHL_IDLE))
            {
                SeqID = TransElmos17DMReq(DSIMasterID,DSIChlSEL);  /** @brief 启动自动寻址，先将所有探头地址设置1 */
                if(SeqID != 0xFF)
                {
                    Elmos17DMSeq = eElmos17DMSeq_Check_ReadStatusRes;
                }        
                else
                {
                    /** @brief 传输错误 */
                    
                    PRINTF_ELMOS17("DSIMaster[%d] Trans StartAutoDM Err \r\n",DSIMasterID);
                }
            }

            break;
        }
        
uint8 Daisy_chainIdInvStep = 0;
        
#if( DSISlaveSnsNum == 6)

#define Daisy_chainIdInvStepNum 9

uint8 IdInversion[9][2] = 
{
    {1,7},{6,1},{7,6},
    {2,7},{5,2},{7,5},
    {3,7},{4,3},{7,4},
};

#else
#define Daisy_chainIdInvStepNum 6

uint8 IdInversion[6][2] = 
{
    {1,7},{4,1},{7,4},
    {2,7},{3,2},{7,3},
};


#endif

        case eElmos17DMSeq_Check_ReadStatusRes:
        {
            DSIReturnType_en flg = 0;
            uint16 DSI_STAT[2] = {0,0};
            if((GetDSICHLWorkStatus(DSIMasterID,DSIChl1) == DSICHL_IDLE)
                &&(GetDSICHLWorkStatus(DSIMasterID,DSIChl2) == DSICHL_IDLE))
            {
                
                    flg = TransRMasterDSIReg(DSIMasterID,R_DSI1_STAT,&DSI_STAT[0]);
                    flg |= TransRMasterDSIReg(DSIMasterID,R_DSI2_STAT,&DSI_STAT[1]); 
                    if(flg != eDSIReturnType_OK)
                    {
                        
                    }
                    else
                    {
                        
                    }
                    *pSnsDMFlg = (DSI_STAT[0] & 0x0F) | ((DSI_STAT[1] & 0x0F) << 4);
                    
                    Elmos17DMSeq = eElmos17DMSeq_End;
            }

            break;
        }

        case eElmos17DMSeq_TransChangeID:
        {
            if((GetDSICHLWorkStatus(DSIMasterID,DSIChl1) == DSICHL_IDLE)
                &&(GetDSICHLWorkStatus(DSIMasterID,DSIChl2) == DSICHL_IDLE))
            {
                
                /** @brief 添加延时 */
                /** @brief 同时传输两个通道ID修改指令 */
                SeqID = TransElmos17ChangeSlaveID(DSIMasterID,DSIChlSEL,IdInversion[Daisy_chainIdInvStep][0],IdInversion[Daisy_chainIdInvStep][1]);
                if(SeqID != 0xFF)
                {
                    Elmos17DMSeq = eElmos17DMSeq_CheckChangeIDRes;
                }       
                else
                {
                    /** @brief 传输错误 */
                
                    PRINTF_ELMOS17("DSIMaster[%d] Trans ChangeID Err \r\n",DSIMasterID);
                }
            }

            break;
        }

        case eElmos17DMSeq_CheckChangeIDRes:
        {
            if((GetDSICHLWorkStatus(DSIMasterID,DSIChl1) == DSICHL_IDLE)
                &&(GetDSICHLWorkStatus(DSIMasterID,DSIChl2) == DSICHL_IDLE))
            {
                /** @brief 获取两个通道CRM 应答数据 */
                for(Lcnt=0;Lcnt < 2;Lcnt++)
                {
                    
                    Flg = GetCRMRESData(DSIMasterID,&CRMRESOutData);
                    if(Flg != 0xFFFF)
                    {
                        if((CRMRESOutData.ResData.DSIStatus.Status == 0) && (CRMRESOutData.ResData.DSIStatus.SymbolCount == 8))
                        {
                            /** @brief 数据接收正确 */
                            if(((CRMRESOutData.ResData.Data.PhysAddr_Status >> 4) & 0x0F) == (IdInversion[Daisy_chainIdInvStep][1]) && (CRMRESOutData.ResData.Data.Data[0] == 0x80))
                            {

                                SnsID = GetSnsID(DSIMasterID, CRMRESOutData.DSIChlID, IdInversion[Daisy_chainIdInvStep][1]);
                                if(SnsID < SNSNum)
                                {
                                    /** @brief 该探头地址应答正确 */
                                    *pSnsDMFlg |= 0x0001 << SnsID;
                                }
                                else
                                {
                                    /** @brief SnsID错误 */
                                
                                    PRINTF_ELMOS17("DSIMaster[%d] SnsID Err DSI%d IO%d \r\n",DSIMasterID,CRMRESOutData.DSIChlID,ScIOCnt);
                                }

                            }
                            else
                            {
                                /** @brief 数据错误 */
                                PRINTF_ELMOS17("DSIMaster[%d] SnsID Err DSI%d IO%d \r\n",DSIMasterID,CRMRESOutData.DSIChlID,ScIOCnt);
                                PRINTF_ELMOS17("DM Change ID Resp Data Err \r\n");
                                PrintfCRMRes(&CRMRESOutData);
                            }
                        }
                        else
                        {
                            /** @brief DSI 状态错??*/
                            PRINTF_ELMOS17("DSIMaster[%d] SnsID Err DSI%d IO%d \r\n",DSIMasterID,CRMRESOutData.DSIChlID,ScIOCnt);
                            PRINTF_ELMOS17("DM Change ID Resp Status Err \r\n");
                            PrintfCRMRes(&CRMRESOutData);

                        }
                    }
                    else
                    {
                        /** @brief 未接收到应答数据 */
                        
                    }
                }
                

               
                Daisy_chainIdInvStep ++;
                if(Daisy_chainIdInvStep >= Daisy_chainIdInvStepNum)
                {
                    Daisy_chainIdInvStep = 0;
                    Elmos17DMSeq = eElmos17DMSeq_End;
                }
                else
                {
                    Elmos17DMSeq = eElmos17DMSeq_TransChangeID;

                }
                
            }

            break;
        }

		case eElmos17DMSeq_End:
        {	
            Elmos17DMSeq = eElmos17DMSeq_Init;
            return 1;	
			break;
        }
#else
		case eElmos17DMSeq_Init:
		{
			//SnsENIOVlaue = (uint16)(((uint16)1U << eSNS_GROUP1_POWER_EN_PIN) | ((uint16)1U << eSNS_GROUP2_POWER_EN_PIN) | ((uint16)1U << eSNS_GROUP3_POWER_EN_PIN));
			//AK2_WriteEnSnsPin(SnsENIOVlaue);/** @brief	使能所有探头电源 */
 			
			for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
			{
				//TransStopDSI(DSIMasterIdx,DSIChlSEL);
				InitCRMRESQueue(DSIMasterIdx);
			}
			ScIOCnt = 0;
			Elmos17DMSeq = eElmos17DMSeq_StartAutoDM;
			break;
		}

		case eElmos17DMSeq_StartAutoDM:
		{
			if(FALSE == PwrSwFlg)
			{
				PwrSwFlg = TRUE;
				SnsENIOVlaue = ((uint16)1U << ScIOCnt);
            	AK2_WriteEnSnsPin(SnsENIOVlaue);
				PwrSwDelayStartTime = AK2_GetSys1msTickFunc();
			}
			else
			{
				PwrSwDelayTime = CalcTimeInterval(PwrSwDelayStartTime,AK2_GetSys1msTickFunc());
			}

			if((0 == ScIOCnt) && (FALSE == DMDone))
			{
				DelayTime = WAIT_SNS_PWR_ON;
			}
			else
			{
				DelayTime = PWR_SW_DELAY_TIME;
			}
			
			/** @brief 添加延时 */
			if(PwrSwDelayTime > DelayTime)
			{
				PwrSwFlg = FALSE;
				PwrSwDelayTime = 0;

				for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
				{
					if((GetDSICHLWorkStatus(DSIMasterIdx,DSIChl1) == DSICHL_IDLE)
						&&(GetDSICHLWorkStatus(DSIMasterIdx,DSIChl2) == DSICHL_IDLE)) 
					{
						DSIMasterDone |= 1 << DSIMasterIdx;
						SeqID = TransElmos17DMReq(DSIMasterIdx,DSIChlSEL);  /** @brief 启动自动寻址，先将探头地址设置1 */
						if(SeqID != 0xFF)
						{
							DMSeqFlg = TRUE;  
						}		 
						else
						{
							/** @brief 传输错误 */
							TestErrCntSeq[0]++;
							PRINTF_ELMOS17("DSIMaster[%d] Trans StartAutoDM Err \r\n",DSIMasterIdx);
						}
					}
				}

				if(((DSIMasterDone + 1) == (uint8)(1 << DSIMasterNum)) && (TRUE == DMSeqFlg))
				{
					DSIMasterDone = 0;
					DMSeqFlg = FALSE;
					Elmos17DMSeq = eElmos17DMSeq_TransChangeID; /** @brief 两个master都遍历过且有一个成功，就跳到下一个状态机 */
				}
			}

			break;
		}

        case eElmos17DMSeq_TransChangeID:
        {
			for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
			{
	            if((GetDSICHLWorkStatus(DSIMasterIdx,DSIChl1) == DSICHL_IDLE)
	                &&(GetDSICHLWorkStatus(DSIMasterIdx,DSIChl2) == DSICHL_IDLE)
	                &&(0 == (DSIMasterDone & (1 << DSIMasterIdx))))/** @brief 防止重复发送 */
	            {
					DSIMasterDone |= 1 << DSIMasterIdx;
	                /** @brief 同时传输两个通道ID修改指令 */
	                SeqID = TransElmos17ChangeSlaveID(DSIMasterIdx,DSIChlSEL,1,(ScIOCnt + 1));
	                if(SeqID != 0xFF)
	                {
	                    DMSeqFlg = TRUE; 
	                }       
	                else
	                {
	                    /** @brief 传输错误 */
						TestErrCntSeq[1]++;
	                    PRINTF_ELMOS17("DSIMaster[%d] Trans ChangeID Err \r\n",DSIMasterIdx);
	                }

	            }
			}
				
			if(((DSIMasterDone + 1) == (uint8)(1 << DSIMasterNum)) && (TRUE == DMSeqFlg))
			{
				DSIMasterDone = 0;
				DMSeqFlg = FALSE;
				Elmos17DMSeq = eElmos17DMSeq_CheckChangeIDRes;/** @brief 两个master都遍历过且有一个成功，就跳到下一个状态机 */ 
			}

            break;
        }

        case eElmos17DMSeq_CheckChangeIDRes:
        {
			for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
			{
	            if((GetDSICHLWorkStatus(DSIMasterIdx,DSIChl1) == DSICHL_IDLE)
	                &&(GetDSICHLWorkStatus(DSIMasterIdx,DSIChl2) == DSICHL_IDLE)
	                &&(0 == (DSIMasterDone & (1 << DSIMasterIdx))))/** @brief 防止重复获取 */
	            {
	            	DSIMasterDone |= 1 << DSIMasterIdx;
	                /** @brief 获取两个通道CRM 应答数据 */
	                for(Lcnt=0;Lcnt < 2;Lcnt++)
	                {
	                    
	                    Flg = GetCRMRESData(DSIMasterIdx,&CRMRESOutData);
	                    if(Flg != 0xFFFF)
	                    {
	                        if((CRMRESOutData.ResData.DSIStatus.Status == 0) && (CRMRESOutData.ResData.DSIStatus.SymbolCount == 8))
	                        {
	                            /** @brief 数据接收正确 */
	                            if((((CRMRESOutData.ResData.Data.PhysAddr_Status >> 4) & 0x0F) == (ScIOCnt + 1)) && (CRMRESOutData.ResData.Data.Data[0] == 0x80))
	                            {
									SnsID = GetSnsID(DSIMasterIdx, CRMRESOutData.DSIChlID, ScIOCnt + 1);
	                                if(SnsID < SNSNum)
	                                {
	                                    /** @brief 该探头地址应答正确 */
	                                }
	                                else
	                                {
	                                    /** @brief SnsID错误 */								
										TestErrCntSeq[2]++;
	                                    PRINTF_ELMOS17("DSIMaster%d channel%d slot%d SnsID Err \r\n",DSIMasterIdx,CRMRESOutData.DSIChlID + 1,ScIOCnt);
	                                }

	                            }
	                            else
	                            {
	                                /** @brief 数据错误 */							
									TestErrCntSeq[3]++;
	                                PRINTF_ELMOS17("DSIMaster%d channel%d slot%d DM Change ID Resp Data Err \r\n",DSIMasterIdx,CRMRESOutData.DSIChlID + 1,ScIOCnt);
	                            }
	                        }
	                        else
	                        {
	                            /** @brief DSI 状态  */						
								TestErrCntSeq[4]++;
								PRINTF_ELMOS17("DSIMaster%d channel%d slot%d DM Change ID Resp status Err \r\n",DSIMasterIdx,CRMRESOutData.DSIChlID + 1,ScIOCnt);
	                        }
	                    }
	                    else
	                    {
	                        /** @brief 未接收到应答数据 */
							TestErrCntSeq[5]++;                        
	                    }
	                }
	            }
			}
			
			if((DSIMasterDone + 1) == (uint8)(1 << DSIMasterNum))
			{
				DSIMasterDone = 0;
				Elmos17DMSeq = eElmos17DMSeq_TransReadEEPROMID;/** @brief 两个master都遍历过就跳到下一个状态机 */
			}

            break;
        }
	
		case eElmos17DMSeq_TransReadEEPROMID:
		{
			for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
			{
				if((GetDSICHLWorkStatus(DSIMasterIdx,DSIChl1) == DSICHL_IDLE)
	             &&(GetDSICHLWorkStatus(DSIMasterIdx,DSIChl2) == DSICHL_IDLE)
	             &&(0 == (DSIMasterDone & (1 << DSIMasterIdx))))/** @brief 防止重复发送 */
	            {
					DSIMasterDone |= 1 << DSIMasterIdx;
	                /** @brief 同时传输读取两个通道探头EEPROM中的ID指令 */
	                SeqID = TransElmos17ReadICRegReq(DSIMasterIdx,DSIChlSEL,(ScIOCnt + 1),EEPROM_MODE_ADDR,EEPROM_ID_ADDR);
	                if(SeqID != 0xFF)
	                {
	                    DMSeqFlg = TRUE;
	                }       
	                else
	                {
	                    /** @brief 传输错误 */
						TestErrCntSeq[6]++;
	                    PRINTF_ELMOS17("DSIMaster[%d] Trans ReadEEPROMID Err \r\n",DSIMasterIdx);
	                }
	            }
			}

			if(((DSIMasterDone + 1) == (uint8)(1 << DSIMasterNum)) && (TRUE == DMSeqFlg))
			{
				DSIMasterDone = 0;
				DMSeqFlg = FALSE;
				Elmos17DMSeq = eElmos17DMSeq_CheckReadEEPROMIDRes; /** @brief 两个master都遍历过且有一个成功，就跳到下一个状态机 */
			}
			
            break;
		}


		case eElmos17DMSeq_CheckReadEEPROMIDRes:
		{
			for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
			{
				if((GetDSICHLWorkStatus(DSIMasterIdx,DSIChl1) == DSICHL_IDLE)
	             &&(GetDSICHLWorkStatus(DSIMasterIdx,DSIChl2) == DSICHL_IDLE)
	             &&(0 == (DSIMasterDone & (1 << DSIMasterIdx))))/** @brief 防止重复获取 */
	            {
					DSIMasterDone |= 1 << DSIMasterIdx;
					/** @brief 获取两个通道CRM 应答数据 */
					for(Lcnt=0;Lcnt < 2;Lcnt++)
					{
						Flg = GetCRMRESData(DSIMasterIdx,&CRMRESOutData);
						if(Flg != 0xFFFF)
						{
							if((CRMRESOutData.ResData.DSIStatus.Status == 0) && (CRMRESOutData.ResData.DSIStatus.SymbolCount == 8))
							{
								/** @brief 数据接收正确 */
								if((((CRMRESOutData.ResData.Data.PhysAddr_Status >> 4) & 0x0F) == (ScIOCnt + 1)) 
									&& ((CRMRESOutData.ResData.Data.PhysAddr_Status & 0x03) == 0x01)
								 	&& ((CRMRESOutData.ResData.Data.Data[0] & 0x0F)== (ScIOCnt + 1)))
								{
									/** @brief RAM和EEPROM中的数据一致 */
	                                SnsID = GetSnsID(DSIMasterIdx, CRMRESOutData.DSIChlID, ScIOCnt + 1);
									*pSnsDMFlg |= 0x0001 << SnsID;
	 							}
	 							else
								{
									/** @brief RAM和EEPROM中的数据不一致 更新 EEPROM ID*/
									DMSeqFlg = TRUE;
								}	
							}
							else
							{
								/** @brief DSI 状态  */
								TestErrCntSeq[7]++;
								PRINTF_ELMOS17("DSIMaster%d channel%d slot%d Read EEPROMID Resp status Err \r\n",DSIMasterIdx,CRMRESOutData.DSIChlID + 1,ScIOCnt);
							}
						}
						else
						{
							/** @brief 未接收到应答数据 */					
							TestErrCntSeq[8]++;
						}
					}
	            }
			}

			if((DSIMasterDone + 1) == (uint8)(1 << DSIMasterNum))/** @brief 两个master都遍历过 */
			{
				DSIMasterDone = 0;
				if(TRUE == DMSeqFlg)
				{
					Elmos17DMSeq = eElmos17DMSeq_EnEEPROMPrg;
				}
				else
				{
					Elmos17DMSeq = eElmos17DMSeq_PrepareNextDM;
				}
				
				DMSeqFlg = FALSE;
			}
			
            break;
		}
		
		case eElmos17DMSeq_EnEEPROMPrg:
		{
			for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
			{
				if((GetDSICHLWorkStatus(DSIMasterIdx,DSIChl1) == DSICHL_IDLE)
	             &&(GetDSICHLWorkStatus(DSIMasterIdx,DSIChl2) == DSICHL_IDLE)
	             &&(0 == (DSIMasterDone & (1 << DSIMasterIdx))))/** @brief 防止重复发送 */
				{
					DSIMasterDone |= 1 << DSIMasterIdx;
					/** @brief 同时传输两个通道探头使能EEPROM写指令 */
	                SeqID = TransElmos17EEPROMAccessReq(DSIMasterIdx,DSIChlSEL,(ScIOCnt + 1),Elmos17EnEEPProg);
	                if(SeqID != 0xFF)
	                {
						DMSeqFlg = TRUE;
	                }       
	                else
	                {
	                    /** @brief 传输错误 */				
						TestErrCntSeq[9]++;
	                    PRINTF_ELMOS17("DSIMaster[%d] Trans EnEEPROMPrg Err \r\n",DSIMasterIdx);
	                }
				}
			}

			if(((DSIMasterDone + 1) == (uint8)(1 << DSIMasterNum)) && (TRUE == DMSeqFlg))
			{				
				DSIMasterDone = 0;
				DMSeqFlg = FALSE;
				Elmos17DMSeq = eElmos17DMSeq_CheckEnEEPROMPrgRes; /** @brief 两个master都遍历过且有一个成功，就跳到下一个状态机 */
			}
			
			break;
		}
		
    	case eElmos17DMSeq_CheckEnEEPROMPrgRes:
    	{
			for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
			{
				if((GetDSICHLWorkStatus(DSIMasterIdx,DSIChl1) == DSICHL_IDLE)
	             &&(GetDSICHLWorkStatus(DSIMasterIdx,DSIChl2) == DSICHL_IDLE)
	             &&(0 == (DSIMasterDone & (1 << DSIMasterIdx))))/** @brief 防止重复获取 */
	            {	
					DSIMasterDone |= 1 << DSIMasterIdx;
					/** @brief 获取两个通道CRM 应答数据 */
					for(Lcnt=0;Lcnt < 2;Lcnt++)
					{
						Flg = GetCRMRESData(DSIMasterIdx,&CRMRESOutData);
						if(Flg != 0xFFFF)
						{
							if((CRMRESOutData.ResData.DSIStatus.Status == 0) && (CRMRESOutData.ResData.DSIStatus.SymbolCount == 8))
							{
								/** @brief 数据接收正确 */
								if((((CRMRESOutData.ResData.Data.PhysAddr_Status >> 4) & 0x0F) == (ScIOCnt + 1)) && ((CRMRESOutData.ResData.Data.PhysAddr_Status & 0x03) == 0x01))
								{
									/** @brief 使能EEPROM写成功*/
									DMSeqFlg = TRUE;
	 							}
	 							else
								{
									/** @brief 使能EEPROM写不成功*/							
									TestErrCntSeq[10]++;
									PRINTF_ELMOS17("DSIMaster%d channel%d slave%d EnEEPROMPrg fail \r\n",DSIMasterIdx,CRMRESOutData.DSIChlID + 1,ScIOCnt);
								}	
							}
							else
							{
								/** @brief DSI 状态  */						
								TestErrCntSeq[11]++;
								PRINTF_ELMOS17("DSIMaster%d channel%d slave%d EnEEPROMPrgRes data status err \r\n",DSIMasterIdx,CRMRESOutData.DSIChlID + 1,ScIOCnt);
							}
						}
						else
						{
							/** @brief 未接收到应答数据 */					
							TestErrCntSeq[12]++;
						}
					}
	            }
			}

			if((DSIMasterDone + 1) == (uint8)(1 << DSIMasterNum)) /** @brief 两个master都遍历过 */
			{
				DSIMasterDone = 0;
				if(TRUE == DMSeqFlg)
				{
					Elmos17DMSeq = eElmos17DMSeq_WriteIDToEEPROM;
				}
				else
				{
					Elmos17DMSeq = eElmos17DMSeq_PrepareNextDM;
				}
				DMSeqFlg = FALSE;
			}
			break;
		}

		case eElmos17DMSeq_WriteIDToEEPROM:
		{
			for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
			{
				if((GetDSICHLWorkStatus(DSIMasterIdx,DSIChl1) == DSICHL_IDLE)
	             &&(GetDSICHLWorkStatus(DSIMasterIdx,DSIChl2) == DSICHL_IDLE)
	             &&(0 == (DSIMasterDone & (1 << DSIMasterIdx))))/** @brief 防止重复发送 */
				{
					DSIMasterDone |= 1 << DSIMasterIdx;
					/** @brief 同时传输两个通道探头写ID到EEPROM指令 */
		            SeqID = TransElmos17EEPROMAccessReq(DSIMasterIdx,DSIChlSEL,(ScIOCnt + 1),Elmos17ProgSLAVE_ID);
		            if(SeqID != 0xFF)
		            {
		                DMSeqFlg = TRUE;
		            }       
		            else
		            {
		                /** @brief 传输错误 */
						TestErrCntSeq[13]++;
		                PRINTF_ELMOS17("DSIMaster[%d] Trans Write ID To EEPROMID \r\n",DSIMasterIdx);
		            }
	            }
			}

			if(((DSIMasterDone + 1) == (uint8)(1 << DSIMasterNum)) && (TRUE == DMSeqFlg))
			{
				DSIMasterDone = 0;
				DMSeqFlg = FALSE;
				Elmos17DMSeq = eElmos17DMSeq_CheckWriteIDToEEPROMRes; /** @brief 两个master都遍历过且有一个成功，就跳到下一个状态机 */
			}
			
			break;
		}
		
   		case eElmos17DMSeq_CheckWriteIDToEEPROMRes:
   		{
			for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
			{
				if((GetDSICHLWorkStatus(DSIMasterIdx,DSIChl1) == DSICHL_IDLE)
	             &&(GetDSICHLWorkStatus(DSIMasterIdx,DSIChl2) == DSICHL_IDLE)
	             &&(0 == (DSIMasterDone & (1 << DSIMasterIdx))))/** @brief 防止重复获取 */
	            {
					DSIMasterDone |= 1 << DSIMasterIdx;
					/** @brief 获取两个通道CRM 应答数据 */
					for(Lcnt=0;Lcnt < 2;Lcnt++)
					{
						Flg = GetCRMRESData(DSIMasterIdx,&CRMRESOutData);
						if(Flg != 0xFFFF)
						{
							if((CRMRESOutData.ResData.DSIStatus.Status == 0) && (CRMRESOutData.ResData.DSIStatus.SymbolCount == 8))
							{
								/** @brief 数据接收正确 */
								if((((CRMRESOutData.ResData.Data.PhysAddr_Status >> 4) & 0x0F) == (ScIOCnt + 1)) && ((CRMRESOutData.ResData.Data.PhysAddr_Status & 0x03) == 0x01))
								{
									/** @brief ID保存至EEPROM成功 */
								    SnsID = GetSnsID(DSIMasterIdx, CRMRESOutData.DSIChlID, ScIOCnt + 1);								
									*pSnsDMFlg |= 0x0001 << SnsID;
	 							}
	 							else
								{
									/** @brief ID保存至EEPROM失败 */
									TestErrCntSeq[14]++;
								}	
							}
							else
							{
								/** @brief DSI 状态  */
								TestErrCntSeq[15]++;
								PRINTF_ELMOS17("DSIMaster %d channel %d slave%d WriteIDToEEPROMRes data status Err \r\n",DSIMasterIdx,CRMRESOutData.DSIChlID + 1,ScIOCnt);
							}
						}
						else
						{
							/** @brief 未接收到应答数据 */	
							TestErrCntSeq[16]++;
						}
					}
	            }
			}
			
			if((DSIMasterDone + 1) == (uint8)(1 << DSIMasterNum))/** @brief 两个master都遍历过就跳到下一个状态机 */
			{
				DSIMasterDone = 0;
				Elmos17DMSeq = eElmos17DMSeq_PrepareNextDM;
				pSnsCtrlGroup->Time1msCnt = 0; 
			}
			break;
		}

		case eElmos17DMSeq_PrepareNextDM:
		{
			if(pSnsCtrlGroup->Time1msCnt > WAIT_SNS_ID_TO_EEPROM)
			{
		        ScIOCnt ++;
	            if(ScIOCnt >= DSISlaveSnsNum)
	            {
	                ScIOCnt = 0;
					SnsENIOVlaue = (uint16)(((uint16)1U << eSNS_GROUP1_POWER_EN_PIN) | ((uint16)1U << eSNS_GROUP2_POWER_EN_PIN) | ((uint16)1U << eSNS_GROUP3_POWER_EN_PIN));
	                AK2_WriteEnSnsPin(SnsENIOVlaue);/** @brief  使能所有探头电源 */
	                Elmos17DMSeq = eElmos17DMSeq_End;
					pSnsCtrlGroup->Time1msCnt = 0; 
	            }
	            else
	            {
	                Elmos17DMSeq = eElmos17DMSeq_StartAutoDM;
	            }
			}
			break;
		}
		
        case eElmos17DMSeq_End:
        {
			/** @brief  延时30ms,等待探头上电后可以正常通讯 */
			if(pSnsCtrlGroup->Time1msCnt > 30)
			{
	            Elmos17DMSeq = eElmos17DMSeq_Init;
	            ScIOCnt = 0;
				DMDone = TRUE;
	            return 1;
			}
			break;
        }
#endif        
        default :
        {
            break;
        }
    }

    return 0;
}

uint8 Elmos524_17_UpdateIDToDefault(uint16 *pSnsDMFlg)
{
    DSISPI_SeqID SeqID;
    uint8 Lcnt = 0;
    //DSIMasterID_en DSIMasterID = DSIMaster0;	
    DSIMasterID_en DSIMasterIdx = DSIMaster0;
    DSIChlSel_en DSIChlSEL = DSIChlSEL1And2;
	static uint8 DSIMasterDone = 0;
    uint16 SnsENIOVlaue = 0;
    uint16 Flg = 0xFFFF;
	static bool DMSeqFlg = FALSE;
    CRM_RESPONSE_Data_str CRMRESOutData;
    
    static uint8 ScIOCnt = 0;
    SnsID_en SnsID;
	static uint32 PwrSwDelayStartTime = 0;
	static bool PwrSwFlg = FALSE;
	static uint32 PwrSwDelayTime = 0;	
    SnsCtrlGroup_Cfg *pSnsCtrlGroup = &SnsCtrlGroup;
	uint32 DelayTime = 0;
	static bool DMDone = FALSE;
 
    switch(Elmos17DMSeq)
    {
		case eElmos17DMSeq_Init:
		{
			//SnsENIOVlaue = (uint16)(((uint16)1U << eSNS_GROUP1_POWER_EN_PIN) | ((uint16)1U << eSNS_GROUP2_POWER_EN_PIN) | ((uint16)1U << eSNS_GROUP3_POWER_EN_PIN));
			//AK2_WriteEnSnsPin(SnsENIOVlaue);/** @brief	使能所有探头电源 */
 			
			for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
			{
				//TransStopDSI(DSIMasterIdx,DSIChlSEL);
				InitCRMRESQueue(DSIMasterIdx);
			}
			ScIOCnt = 0;
			Elmos17DMSeq = eElmos17DMSeq_StartAutoDM;
			break;
		}

		case eElmos17DMSeq_StartAutoDM:
		{
			if(FALSE == PwrSwFlg)
			{
				PwrSwFlg = TRUE;
				SnsENIOVlaue = ((uint16)1U << ScIOCnt);
            	AK2_WriteEnSnsPin(SnsENIOVlaue);
				PwrSwDelayStartTime = AK2_GetSys1msTickFunc();
			}
			else
			{
				PwrSwDelayTime = CalcTimeInterval(PwrSwDelayStartTime,AK2_GetSys1msTickFunc());
			}

			if((0 == ScIOCnt) && (FALSE == DMDone))
			{
				DelayTime = WAIT_SNS_PWR_ON;
			}
			else
			{
				DelayTime = PWR_SW_DELAY_TIME;
			}
			
			/** @brief 添加延时 */
			if(PwrSwDelayTime > DelayTime)
			{
				PwrSwFlg = FALSE;
				PwrSwDelayTime = 0;

				for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
				{
					if((GetDSICHLWorkStatus(DSIMasterIdx,DSIChl1) == DSICHL_IDLE)
						&&(GetDSICHLWorkStatus(DSIMasterIdx,DSIChl2) == DSICHL_IDLE)) 
					{
						DSIMasterDone |= 1 << DSIMasterIdx;
						SeqID = TransElmos17DMReq(DSIMasterIdx,DSIChlSEL);  /** @brief 启动自动寻址，先将探头地址设置1 */
						if(SeqID != 0xFF)
						{
							DMSeqFlg = TRUE;  
						}		 
						else
						{
							/** @brief 传输错误 */
							TestErrCntSeq[0]++;
							PRINTF_ELMOS17("DSIMaster[%d] Trans StartAutoDM Err \r\n",DSIMasterIdx);
						}
					}
				}

				if(((DSIMasterDone + 1) == (uint8)(1 << DSIMasterNum)) && (TRUE == DMSeqFlg))
				{
					DSIMasterDone = 0;
					DMSeqFlg = FALSE;
					Elmos17DMSeq = eElmos17DMSeq_TransChangeID; /** @brief 两个master都遍历过且有一个成功，就跳到下一个状态机 */
				}
			}

			break;
		}

        case eElmos17DMSeq_TransChangeID:
        {
			for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
			{
	            if((GetDSICHLWorkStatus(DSIMasterIdx,DSIChl1) == DSICHL_IDLE)
	                &&(GetDSICHLWorkStatus(DSIMasterIdx,DSIChl2) == DSICHL_IDLE)
	                &&(0 == (DSIMasterDone & (1 << DSIMasterIdx))))/** @brief 防止重复发送 */
	            {
					DSIMasterDone |= 1 << DSIMasterIdx;
	                /** @brief 同时传输两个通道ID修改指令 */
	                SeqID = TransElmos17ChangeSlaveID(DSIMasterIdx,DSIChlSEL,1,SNS_DEFAULT_ID);
	                if(SeqID != 0xFF)
	                {
	                    DMSeqFlg = TRUE; 
	                }       
	                else
	                {
	                    /** @brief 传输错误 */
						TestErrCntSeq[1]++;
	                    PRINTF_ELMOS17("DSIMaster[%d] Trans ChangeID Err \r\n",DSIMasterIdx);
	                }

	            }
			}
				
			if(((DSIMasterDone + 1) == (uint8)(1 << DSIMasterNum)) && (TRUE == DMSeqFlg))
			{
				DSIMasterDone = 0;
				DMSeqFlg = FALSE;
				Elmos17DMSeq = eElmos17DMSeq_CheckChangeIDRes;/** @brief 两个master都遍历过且有一个成功，就跳到下一个状态机 */ 
			}

            break;
        }

        case eElmos17DMSeq_CheckChangeIDRes:
        {
			for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
			{
	            if((GetDSICHLWorkStatus(DSIMasterIdx,DSIChl1) == DSICHL_IDLE)
	                &&(GetDSICHLWorkStatus(DSIMasterIdx,DSIChl2) == DSICHL_IDLE)
	                &&(0 == (DSIMasterDone & (1 << DSIMasterIdx))))/** @brief 防止重复获取 */
	            {
	            	DSIMasterDone |= 1 << DSIMasterIdx;
	                /** @brief 获取两个通道CRM 应答数据 */
	                for(Lcnt=0;Lcnt < 2;Lcnt++)
	                {
	                    
	                    Flg = GetCRMRESData(DSIMasterIdx,&CRMRESOutData);
	                    if(Flg != 0xFFFF)
	                    {
	                        if((CRMRESOutData.ResData.DSIStatus.Status == 0) && (CRMRESOutData.ResData.DSIStatus.SymbolCount == 8))
	                        {
	                            /** @brief 数据接收正确 */
	                            if((((CRMRESOutData.ResData.Data.PhysAddr_Status >> 4) & 0x0F) == (SNS_DEFAULT_ID)) && (CRMRESOutData.ResData.Data.Data[0] == 0x80))
	                            {
									SnsID = GetSnsID(DSIMasterIdx, CRMRESOutData.DSIChlID, ScIOCnt + 1);
	                                if(SnsID < SNSNum)
	                                {
	                                    /** @brief 该探头地址应答正确 */
	                                }
	                                else
	                                {
	                                    /** @brief SnsID错误 */								
										TestErrCntSeq[2]++;
	                                    PRINTF_ELMOS17("DSIMaster%d channel%d slot%d SnsID Err \r\n",DSIMasterIdx,CRMRESOutData.DSIChlID + 1,ScIOCnt);
	                                }

	                            }
	                            else
	                            {
	                                /** @brief 数据错误 */							
									TestErrCntSeq[3]++;
	                                PRINTF_ELMOS17("DSIMaster%d channel%d slot%d DM Change ID Resp Data Err \r\n",DSIMasterIdx,CRMRESOutData.DSIChlID + 1,ScIOCnt);
	                            }
	                        }
	                        else
	                        {
	                            /** @brief DSI 状态  */						
								TestErrCntSeq[4]++;
								PRINTF_ELMOS17("DSIMaster%d channel%d slot%d DM Change ID Resp status Err \r\n",DSIMasterIdx,CRMRESOutData.DSIChlID + 1,ScIOCnt);
	                        }
	                    }
	                    else
	                    {
	                        /** @brief 未接收到应答数据 */
							TestErrCntSeq[5]++;                        
	                    }
	                }
	            }
			}
			
			if((DSIMasterDone + 1) == (uint8)(1 << DSIMasterNum))
			{
				DSIMasterDone = 0;
				Elmos17DMSeq = eElmos17DMSeq_TransReadEEPROMID;/** @brief 两个master都遍历过就跳到下一个状态机 */
			}

            break;
        }
	
		case eElmos17DMSeq_TransReadEEPROMID:
		{
			for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
			{
				if((GetDSICHLWorkStatus(DSIMasterIdx,DSIChl1) == DSICHL_IDLE)
	             &&(GetDSICHLWorkStatus(DSIMasterIdx,DSIChl2) == DSICHL_IDLE)
	             &&(0 == (DSIMasterDone & (1 << DSIMasterIdx))))/** @brief 防止重复发送 */
	            {
					DSIMasterDone |= 1 << DSIMasterIdx;
	                /** @brief 同时传输读取两个通道探头EEPROM中的ID指令 */
	                SeqID = TransElmos17ReadICRegReq(DSIMasterIdx,DSIChlSEL,SNS_DEFAULT_ID,EEPROM_MODE_ADDR,EEPROM_ID_ADDR);
	                if(SeqID != 0xFF)
	                {
	                    DMSeqFlg = TRUE;
	                }       
	                else
	                {
	                    /** @brief 传输错误 */
						TestErrCntSeq[6]++;
	                    PRINTF_ELMOS17("DSIMaster[%d] Trans ReadEEPROMID Err \r\n",DSIMasterIdx);
	                }
	            }
			}

			if(((DSIMasterDone + 1) == (uint8)(1 << DSIMasterNum)) && (TRUE == DMSeqFlg))
			{
				DSIMasterDone = 0;
				DMSeqFlg = FALSE;
				Elmos17DMSeq = eElmos17DMSeq_CheckReadEEPROMIDRes; /** @brief 两个master都遍历过且有一个成功，就跳到下一个状态机 */
			}
			
            break;
		}


		case eElmos17DMSeq_CheckReadEEPROMIDRes:
		{
			for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
			{
				if((GetDSICHLWorkStatus(DSIMasterIdx,DSIChl1) == DSICHL_IDLE)
	             &&(GetDSICHLWorkStatus(DSIMasterIdx,DSIChl2) == DSICHL_IDLE)
	             &&(0 == (DSIMasterDone & (1 << DSIMasterIdx))))/** @brief 防止重复获取 */
	            {
					DSIMasterDone |= 1 << DSIMasterIdx;
					/** @brief 获取两个通道CRM 应答数据 */
					for(Lcnt=0;Lcnt < 2;Lcnt++)
					{
						Flg = GetCRMRESData(DSIMasterIdx,&CRMRESOutData);
						if(Flg != 0xFFFF)
						{
							if((CRMRESOutData.ResData.DSIStatus.Status == 0) && (CRMRESOutData.ResData.DSIStatus.SymbolCount == 8))
							{
								/** @brief 数据接收正确 */
								if((((CRMRESOutData.ResData.Data.PhysAddr_Status >> 4) & 0x0F) == (SNS_DEFAULT_ID)) 
									&& ((CRMRESOutData.ResData.Data.PhysAddr_Status & 0x03) == 0x01)
								 	&& ((CRMRESOutData.ResData.Data.Data[0] & 0x0F)== (SNS_DEFAULT_ID)))
								{
									/** @brief RAM和EEPROM中的数据一致 */
	                                SnsID = GetSnsID(DSIMasterIdx, CRMRESOutData.DSIChlID, ScIOCnt + 1);
									*pSnsDMFlg |= 0x0001 << SnsID;
	 							}
	 							else
								{
									/** @brief RAM和EEPROM中的数据不一致 更新 EEPROM ID*/
									DMSeqFlg = TRUE;
								}	
							}
							else
							{
								/** @brief DSI 状态  */
								TestErrCntSeq[7]++;
								PRINTF_ELMOS17("DSIMaster%d channel%d slot%d Read EEPROMID Resp status Err \r\n",DSIMasterIdx,CRMRESOutData.DSIChlID + 1,ScIOCnt);
							}
						}
						else
						{
							/** @brief 未接收到应答数据 */					
							TestErrCntSeq[8]++;
						}
					}
	            }
			}

			if((DSIMasterDone + 1) == (uint8)(1 << DSIMasterNum))/** @brief 两个master都遍历过 */
			{
				DSIMasterDone = 0;
				if(TRUE == DMSeqFlg)
				{
					Elmos17DMSeq = eElmos17DMSeq_EnEEPROMPrg;
				}
				else
				{
					Elmos17DMSeq = eElmos17DMSeq_PrepareNextDM;
				}
				
				DMSeqFlg = FALSE;
			}
			
            break;
		}
		
		case eElmos17DMSeq_EnEEPROMPrg:
		{
			for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
			{
				if((GetDSICHLWorkStatus(DSIMasterIdx,DSIChl1) == DSICHL_IDLE)
	             &&(GetDSICHLWorkStatus(DSIMasterIdx,DSIChl2) == DSICHL_IDLE)
	             &&(0 == (DSIMasterDone & (1 << DSIMasterIdx))))/** @brief 防止重复发送 */
				{
					DSIMasterDone |= 1 << DSIMasterIdx;
					/** @brief 同时传输两个通道探头使能EEPROM写指令 */
	                SeqID = TransElmos17EEPROMAccessReq(DSIMasterIdx,DSIChlSEL,SNS_DEFAULT_ID,Elmos17EnEEPProg);
	                if(SeqID != 0xFF)
	                {
						DMSeqFlg = TRUE;
	                }       
	                else
	                {
	                    /** @brief 传输错误 */				
						TestErrCntSeq[9]++;
	                    PRINTF_ELMOS17("DSIMaster[%d] Trans EnEEPROMPrg Err \r\n",DSIMasterIdx);
	                }
				}
			}

			if(((DSIMasterDone + 1) == (uint8)(1 << DSIMasterNum)) && (TRUE == DMSeqFlg))
			{				
				DSIMasterDone = 0;
				DMSeqFlg = FALSE;
				Elmos17DMSeq = eElmos17DMSeq_CheckEnEEPROMPrgRes; /** @brief 两个master都遍历过且有一个成功，就跳到下一个状态机 */
			}
			
			break;
		}
		
    	case eElmos17DMSeq_CheckEnEEPROMPrgRes:
    	{
			for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
			{
				if((GetDSICHLWorkStatus(DSIMasterIdx,DSIChl1) == DSICHL_IDLE)
	             &&(GetDSICHLWorkStatus(DSIMasterIdx,DSIChl2) == DSICHL_IDLE)
	             &&(0 == (DSIMasterDone & (1 << DSIMasterIdx))))/** @brief 防止重复获取 */
	            {	
					DSIMasterDone |= 1 << DSIMasterIdx;
					/** @brief 获取两个通道CRM 应答数据 */
					for(Lcnt=0;Lcnt < 2;Lcnt++)
					{
						Flg = GetCRMRESData(DSIMasterIdx,&CRMRESOutData);
						if(Flg != 0xFFFF)
						{
							if((CRMRESOutData.ResData.DSIStatus.Status == 0) && (CRMRESOutData.ResData.DSIStatus.SymbolCount == 8))
							{
								/** @brief 数据接收正确 */
								if((((CRMRESOutData.ResData.Data.PhysAddr_Status >> 4) & 0x0F) == (SNS_DEFAULT_ID)) && ((CRMRESOutData.ResData.Data.PhysAddr_Status & 0x03) == 0x01))
								{
									/** @brief 使能EEPROM写成功*/
									DMSeqFlg = TRUE;
	 							}
	 							else
								{
									/** @brief 使能EEPROM写不成功*/							
									TestErrCntSeq[10]++;
									PRINTF_ELMOS17("DSIMaster%d channel%d slave%d EnEEPROMPrg fail \r\n",DSIMasterIdx,CRMRESOutData.DSIChlID + 1,ScIOCnt);
								}	
							}
							else
							{
								/** @brief DSI 状态  */						
								TestErrCntSeq[11]++;
								PRINTF_ELMOS17("DSIMaster%d channel%d slave%d EnEEPROMPrgRes data status err \r\n",DSIMasterIdx,CRMRESOutData.DSIChlID + 1,ScIOCnt);
							}
						}
						else
						{
							/** @brief 未接收到应答数据 */					
							TestErrCntSeq[12]++;
						}
					}
	            }
			}

			if((DSIMasterDone + 1) == (uint8)(1 << DSIMasterNum)) /** @brief 两个master都遍历过 */
			{
				DSIMasterDone = 0;
				if(TRUE == DMSeqFlg)
				{
					Elmos17DMSeq = eElmos17DMSeq_WriteIDToEEPROM;
				}
				else
				{
					Elmos17DMSeq = eElmos17DMSeq_PrepareNextDM;
				}
				DMSeqFlg = FALSE;
			}
			break;
		}

		case eElmos17DMSeq_WriteIDToEEPROM:
		{
			for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
			{
				if((GetDSICHLWorkStatus(DSIMasterIdx,DSIChl1) == DSICHL_IDLE)
	             &&(GetDSICHLWorkStatus(DSIMasterIdx,DSIChl2) == DSICHL_IDLE)
	             &&(0 == (DSIMasterDone & (1 << DSIMasterIdx))))/** @brief 防止重复发送 */
				{
					DSIMasterDone |= 1 << DSIMasterIdx;
					/** @brief 同时传输两个通道探头写ID到EEPROM指令 */
		            SeqID = TransElmos17EEPROMAccessReq(DSIMasterIdx,DSIChlSEL,(SNS_DEFAULT_ID),Elmos17ProgSLAVE_ID);
		            if(SeqID != 0xFF)
		            {
		                DMSeqFlg = TRUE;
		            }       
		            else
		            {
		                /** @brief 传输错误 */
						TestErrCntSeq[13]++;
		                PRINTF_ELMOS17("DSIMaster[%d] Trans Write ID To EEPROMID \r\n",DSIMasterIdx);
		            }
	            }
			}

			if(((DSIMasterDone + 1) == (uint8)(1 << DSIMasterNum)) && (TRUE == DMSeqFlg))
			{
				DSIMasterDone = 0;
				DMSeqFlg = FALSE;
				Elmos17DMSeq = eElmos17DMSeq_CheckWriteIDToEEPROMRes; /** @brief 两个master都遍历过且有一个成功，就跳到下一个状态机 */
			}
			
			break;
		}
		
   		case eElmos17DMSeq_CheckWriteIDToEEPROMRes:
   		{
			for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
			{
				if((GetDSICHLWorkStatus(DSIMasterIdx,DSIChl1) == DSICHL_IDLE)
	             &&(GetDSICHLWorkStatus(DSIMasterIdx,DSIChl2) == DSICHL_IDLE)
	             &&(0 == (DSIMasterDone & (1 << DSIMasterIdx))))/** @brief 防止重复获取 */
	            {
					DSIMasterDone |= 1 << DSIMasterIdx;
					/** @brief 获取两个通道CRM 应答数据 */
					for(Lcnt=0;Lcnt < 2;Lcnt++)
					{
						Flg = GetCRMRESData(DSIMasterIdx,&CRMRESOutData);
						if(Flg != 0xFFFF)
						{
							if((CRMRESOutData.ResData.DSIStatus.Status == 0) && (CRMRESOutData.ResData.DSIStatus.SymbolCount == 8))
							{
								/** @brief 数据接收正确 */
								if((((CRMRESOutData.ResData.Data.PhysAddr_Status >> 4) & 0x0F) == (SNS_DEFAULT_ID)) && ((CRMRESOutData.ResData.Data.PhysAddr_Status & 0x03) == 0x01))
								{
									/** @brief ID保存至EEPROM成功 */
								    SnsID = GetSnsID(DSIMasterIdx, CRMRESOutData.DSIChlID, ScIOCnt + 1);								
									*pSnsDMFlg |= 0x0001 << SnsID;
	 							}
	 							else
								{
									/** @brief ID保存至EEPROM失败 */
									TestErrCntSeq[14]++;
								}	
							}
							else
							{
								/** @brief DSI 状态  */
								TestErrCntSeq[15]++;
								PRINTF_ELMOS17("DSIMaster %d channel %d slave%d WriteIDToEEPROMRes data status Err \r\n",DSIMasterIdx,CRMRESOutData.DSIChlID + 1,ScIOCnt);
							}
						}
						else
						{
							/** @brief 未接收到应答数据 */	
							TestErrCntSeq[16]++;
						}
					}
	            }
			}
			
			if((DSIMasterDone + 1) == (uint8)(1 << DSIMasterNum))/** @brief 两个master都遍历过就跳到下一个状态机 */
			{
				DSIMasterDone = 0;
				Elmos17DMSeq = eElmos17DMSeq_PrepareNextDM;
				pSnsCtrlGroup->Time1msCnt = 0; 
			}
			break;
		}

		case eElmos17DMSeq_PrepareNextDM:
		{
			if(pSnsCtrlGroup->Time1msCnt > WAIT_SNS_ID_TO_EEPROM)
			{
		        ScIOCnt ++;
	            if(ScIOCnt >= DSISlaveSnsNum)
	            {
	                ScIOCnt = 0;
					SnsENIOVlaue = 0;//(uint16)(((uint16)1U << eSNS_GROUP1_POWER_EN_PIN) | ((uint16)1U << eSNS_GROUP2_POWER_EN_PIN) | ((uint16)1U << eSNS_GROUP3_POWER_EN_PIN));
	                AK2_WriteEnSnsPin(SnsENIOVlaue);/** @brief  使能所有探头电源 */
	                Elmos17DMSeq = eElmos17DMSeq_End;
					pSnsCtrlGroup->Time1msCnt = 0; 
	            }
	            else
	            {
	                Elmos17DMSeq = eElmos17DMSeq_StartAutoDM;
	            }
			}
			break;
		}
		
        case eElmos17DMSeq_End:
        {
			/** @brief  延时80ms,等待探头上电后可以正常通讯 */
			if(pSnsCtrlGroup->Time1msCnt > 80)
			{
	            Elmos17DMSeq = eElmos17DMSeq_Init;
				memset(&TestErrCntSeq[0],0,sizeof(TestErrCntSeq));
	            ScIOCnt = 0;
				DMDone = TRUE;
	            return 1;
			}
			break;
        }
        
        default :
        {
            break;
        }
    }

    return 0;
}

uint8 Elmos524_17_ParallelDMMethod1(uint16 *pSnsDMFlg)
{
    DSISPI_SeqID SeqID;
    uint8 Lcnt = 0;
    DSIMasterID_en DSIMasterIdx = DSIMaster0;
    DSIChlSel_en DSIChlSEL = DSIChlSEL1And2;
	static uint8 DSIMasterDone = 0;
    uint16 SnsENIOVlaue = 0;
    uint16 Flg = 0xFFFF;
	static bool DMSeqFlg = FALSE;
    CRM_RESPONSE_Data_str CRMRESOutData;
    
    static uint8 ScIOCnt = 0;
    SnsID_en SnsID;
	static uint32 PwrSwDelayStartTime = 0;
	static bool PwrSwFlg = FALSE;
	static uint32 PwrSwDelayTime = 0;	
 
    switch(Elmos17DMSeq)
    {	
		case eElmos17DMSeq_Init:
		{	
			for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
			{
				InitCRMRESQueue(DSIMasterIdx);
			}
			ScIOCnt = 0;
			Elmos17DMSeq = eElmos17DMSeq_TransChangeID;
			break;
		}

		case eElmos17DMSeq_TransChangeID:
		{
			if(FALSE == PwrSwFlg)
			{
				PwrSwFlg = TRUE;
				SnsENIOVlaue = ((uint16)1U << (ScIOCnt + 1U)) - 1U;
            	AK2_WriteEnSnsPin(SnsENIOVlaue);
				PwrSwDelayStartTime = AK2_GetSys1msTickFunc();
			}
			else
			{
				PwrSwDelayTime = CalcTimeInterval(PwrSwDelayStartTime,AK2_GetSys1msTickFunc());
			}
			
			/** @brief 添加延时 */
			if(PwrSwDelayTime > WAIT_SNS_PWR_ON)
			{
				PwrSwFlg = FALSE;
				PwrSwDelayTime = 0;

				for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
				{
		            if((GetDSICHLWorkStatus(DSIMasterIdx,DSIChl1) == DSICHL_IDLE)
		                &&(GetDSICHLWorkStatus(DSIMasterIdx,DSIChl2) == DSICHL_IDLE)
		                &&(0 == (DSIMasterDone & (1 << DSIMasterIdx))))/** @brief 防止重复发送 */
		            {
						DSIMasterDone |= 1 << DSIMasterIdx;
		                /** @brief 同时传输两个通道ID修改指令 */
		                SeqID = TransElmos17ChangeSlaveID(DSIMasterIdx,DSIChlSEL,SNS_DEFAULT_ID,(ScIOCnt + 1));
		                if(SeqID != 0xFF)
		                {
		                    DMSeqFlg = TRUE; 
		                }       
		                else
		                {
		                    /** @brief 传输错误 */
							TestErrCntSeq[1]++;
		                    PRINTF_ELMOS17("DSIMaster[%d] Trans ChangeID Err \r\n",DSIMasterIdx);
		                }

		            }
				}
					
				if(((DSIMasterDone + 1) == (uint8)(1 << DSIMasterNum)) && (TRUE == DMSeqFlg))
				{
					DSIMasterDone = 0;
					DMSeqFlg = FALSE;
					Elmos17DMSeq = eElmos17DMSeq_CheckChangeIDRes;/** @brief 两个master都遍历过且有一个成功，就跳到下一个状态机 */ 
				}
			}

			break;
		}

        case eElmos17DMSeq_CheckChangeIDRes:
        {
			for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
			{
	            if((GetDSICHLWorkStatus(DSIMasterIdx,DSIChl1) == DSICHL_IDLE)
	                &&(GetDSICHLWorkStatus(DSIMasterIdx,DSIChl2) == DSICHL_IDLE)
	                &&(0 == (DSIMasterDone & (1 << DSIMasterIdx))))/** @brief 防止重复获取 */
	            {
	            	DSIMasterDone |= 1 << DSIMasterIdx;
	                /** @brief 获取两个通道CRM 应答数据 */
	                for(Lcnt=0;Lcnt < 2;Lcnt++)
	                {
	                    
	                    Flg = GetCRMRESData(DSIMasterIdx,&CRMRESOutData);
	                    if(Flg != 0xFFFF)
	                    {
	                        if((CRMRESOutData.ResData.DSIStatus.Status == 0) && (CRMRESOutData.ResData.DSIStatus.SymbolCount == 8))
	                        {
	                            /** @brief 数据接收正确 */
	                            if((((CRMRESOutData.ResData.Data.PhysAddr_Status >> 4) & 0x0F) == (ScIOCnt + 1)) && (CRMRESOutData.ResData.Data.Data[0] == 0x80))
	                            {
									SnsID = GetSnsID(DSIMasterIdx, CRMRESOutData.DSIChlID, ScIOCnt + 1);
	                                if(SnsID < SNSNum)
	                                {
	                                    /** @brief 该探头地址应答正确 */
										*pSnsDMFlg |= 0x0001 << SnsID;
	                                }
	                                else
	                                {
	                                    /** @brief SnsID错误 */								
										TestErrCntSeq[2]++;
	                                    PRINTF_ELMOS17("DSIMaster%d channel%d slot%d SnsID Err \r\n",DSIMasterIdx,CRMRESOutData.DSIChlID + 1,ScIOCnt);
	                                }

	                            }
	                            else
	                            {
	                                /** @brief 数据错误 */							
									TestErrCntSeq[3]++;
	                                PRINTF_ELMOS17("DSIMaster%d channel%d slot%d DM Change ID Resp Data Err \r\n",DSIMasterIdx,CRMRESOutData.DSIChlID + 1,ScIOCnt);
	                            }
	                        }
	                        else
	                        {
	                            /** @brief DSI 状态  */						
								TestErrCntSeq[4]++;
								PRINTF_ELMOS17("DSIMaster%d channel%d slot%d DM Change ID Resp status Err \r\n",DSIMasterIdx,CRMRESOutData.DSIChlID + 1,ScIOCnt);
	                        }
	                    }
	                    else
	                    {
	                        /** @brief 未接收到应答数据 */
							TestErrCntSeq[5]++;                        
	                    }
	                }
	            }
			}
			
			if((DSIMasterDone + 1) == (uint8)(1 << DSIMasterNum))
			{
				DSIMasterDone = 0;
				Elmos17DMSeq = eElmos17DMSeq_PrepareNextDM;/** @brief 两个master都遍历过就跳到下一个状态机 */
			}
            break;
        }

		case eElmos17DMSeq_PrepareNextDM:
		{
	        ScIOCnt ++;
            if(ScIOCnt >= DSISlaveSnsNum)
            {
                ScIOCnt = 0;
                Elmos17DMSeq = eElmos17DMSeq_End; 
            }
            else
            {
                Elmos17DMSeq = eElmos17DMSeq_TransChangeID;
            }
			break;
		}
		
        case eElmos17DMSeq_End:
        {
            Elmos17DMSeq = eElmos17DMSeq_Init;
            ScIOCnt = 0;
            return 1;
			
			break;
        }
       
        default :
        {
            break;
        }
    }

    return 0;
}

uint8 Elmos524_17_CfgInitSeq()
{    
    SnsCtrlGroup_Cfg *pSnsCtrlGroup = &SnsCtrlGroup;
   	DSIMasterID_en DSIMasterID = DSIMaster0;
    DSIChlID_en DSIChlID = DSIChl1;
    uint8 SlotID = 0;
    static SnsID_en SnsID = SNS_FL_S;
    uint16 Idx = 0;
    bool SuccessFlg = FALSE;
    uint16 BufIdx = 0xFFFF;
    CRM_RESPONSE_Data_str CRMRESOutData;
    WRSnsDataType_e CfgType = eWRSnsDataType_GeneralSet;
	WRSnsMeasParam_str WRSnsMeasParam;
	SnsParamCfgCtrl *pSnsParamCfgCtrl = &GstrSnsParamCfgCtrl;
	const Elmos17MeasParamCfg_str* pMeasParamCfg = GetMeasParamCfg();
	const uint8* pMeasParamDefaultVal = GetMeasParamDefaultVal();
	uint32 StartAddr = 0;
	static uint8 RetryCnt = WRITE_SNS_EEPROM_RETRY_CNT;
	static uint8 DataBuf[8] = {0};
	
    memset((void*)&CRMRESOutData,0,sizeof(CRM_RESPONSE_Data_str));
	memset((void*)&WRSnsMeasParam,0,sizeof(WRSnsMeasParam_str));

	DSIMasterID = SnsAddCfg[SnsID].DSIMasterID;
	DSIChlID = SnsAddCfg[SnsID].DSIChlSEL - 1U;
	SlotID = SnsAddCfg[SnsID].cSnsPhysAddr - 1U;

    switch(pSnsParamCfgCtrl->SnsParamCfgSeq)
    {
	   case SnsParamCfgInit:
	   {
			TransStopDSI(DSIMasterID,DSIChlSEL1And2);
			InitCRMRESQueue(DSIMasterID);
			memset((void *)&TestErrCntSeq[0],0,sizeof(TestErrCntSeq));
			pSnsParamCfgCtrl->SnsParamCfgSeq = ReadSnsParamID; //ReadSnsParamAfterPwrOn
			pSnsParamCfgCtrl->SnsParamCfgFlg = 0;
			TestErrCntSeq[0]++;
	   }
	   break;
#if 0
	   case ReadSnsParamAfterPwrOn:
	   {
			WRSnsMeasParam.SnsID = SnsID;
	   		WRSnsMeasParam.eWRFlg = eRSnsMeasParam;
	   		WRSnsMeasParam.ElmosMeasParam_StartAddr = 0;
			WRSnsMeasParam.u16Len = Elmos17MEAS_Param_Check_u16Len;
			memset((void*)&RSnsEEPROMBuf.WData[0],0,WRSnsMeasParam.u16Len*2);
			RWReturnFlg[DSIMasterID] = 0;
		    WRSnsMeasParam.pInOutDataBuf = &RSnsEEPROMBuf.WData[0];	
			WRSnsMeasParam.pResultflg = &RWReturnFlg[DSIMasterID];
			TriggerWRSnsMsg(DSIMasterID,pSnsCtrlGroup,&WRSnsMeasParam);
			SetNextSnsCtrlGroupStatus(GroupStatus_SNSINIT);
			
			pSnsParamCfgCtrl->SnsParamCfgSeq = CheckSnsParamAfterPwrOn;
			TestErrCntSeq[17]++;
	   }
	   break;
	   
	   case CheckSnsParamAfterPwrOn:
	   {
			if(WR_SNS_MEAS_PARAM_SUCCESS == RWReturnFlg[DSIMasterID])
			{
				/*拷贝默认参数到写buf*/
				memcpy((void*)&WSnsEEPROMBuf.WData[0],(const void*)pMeasParamDefaultVal,Elmos17MEAS_Param_Check_u16Len*2);
				
				if((((RSnsEEPROMBuf.WData[1] >> 9) & 0x001F) > 6) /*&& ((RSnsEEPROMBuf.WData[1] & 0x001F) > 6)*/)/*如果校准增益太小，说明数据被异常修改，使用默认值*/
				{
					WriteCalAndAnaGain(RSnsEEPROMBuf.WData[1],pMeasParamCfg[eWRSnsDataType_GeneralSet].pCfgStr[SnsID]);
				}

				for(CfgType = eWRSnsDataType_GeneralSet;CfgType < eWRSnsDataType_NULL;CfgType++)
				{
					if(NULL != pMeasParamCfg[CfgType].pCfgStr[SnsID])
					{
						StartAddr = pMeasParamCfg[CfgType].ParamAddr;
						pMeasParamCfg[CfgType].ConvStructToU16DataFunc(pMeasParamCfg[CfgType].pCfgStr[SnsID],(uint16*)&WSnsEEPROMBuf.cData[StartAddr]);
					}
				}

				for(Idx = 0;Idx < Elmos17MEAS_Param_Check_u16Len - 4;Idx++)
		        {
		            if(WSnsEEPROMBuf.WData[Idx] != RSnsEEPROMBuf.WData[Idx])
		            {
						pSnsParamCfgCtrl->SnsParamNeedUpdateFlg |= (uint16_t)1 << SnsID;
						break;
		            }
		        }
			}

			pSnsParamCfgCtrl->SnsParamCfgSeq = ReadSnsParamID;
			TestErrCntSeq[18]++;
	   }
	   break;
#endif	
	   case ReadSnsParamID:
	   {	
	   		if(0 != (TestDMFlg & (0x0001 << SnsID)))
	   		{
		   		WRSnsMeasParam.SnsID = SnsID;
		   		WRSnsMeasParam.eWRFlg = eRSnsMeasParam;
		   		WRSnsMeasParam.ElmosMeasParam_StartAddr = Elmos17MEAS_SnsParamID_StartAddr;
				WRSnsMeasParam.u16Len = 4;
				memset((void*)&RSnsEEPROMBuf.WData[0],0,WRSnsMeasParam.u16Len*2);
				RWReturnFlg[DSIMasterID] = 0;
			    WRSnsMeasParam.pInOutDataBuf = &RSnsEEPROMBuf.WData[0];	
				WRSnsMeasParam.pResultflg = &RWReturnFlg[DSIMasterID];
				TriggerWRSnsMsg(DSIMasterID,pSnsCtrlGroup,&WRSnsMeasParam);
				SetNextSnsCtrlGroupStatus(GroupStatus_SNSINIT);
				
				pSnsParamCfgCtrl->SnsParamCfgSeq = HandleReadSnsParamIDRes;
	   		}
			else
			{
				/*探头未被寻址，直接跳过*/
				pSnsParamCfgCtrl->SnsParamCfgSeq = PrepareNextSnsParamCfg;
			}
			
			TestErrCntSeq[1]++;
	   }
	   break;
	   
	   case HandleReadSnsParamIDRes:
	   {
			if(WR_SNS_MEAS_PARAM_SUCCESS == RWReturnFlg[DSIMasterID])
			{	
				if((pSnsParamCfgCtrl->SnsParamID[SnsID] == RSnsEEPROMBuf.WData[0])
					||(0x0001 == RSnsEEPROMBuf.WData[0]))
				{
					#if (UPADTE_SNS_PARAM == 0)
					if(0 == (pSnsParamCfgCtrl->SnsParamNeedUpdateFlg & ((uint16)1 << SnsID)))
					{
						pSnsParamCfgCtrl->SnsParamCfgFlg |= 0x0001 << SnsID;
					}
					#else
					SuccessFlg = TRUE;
					#endif
				}
				else
				{
					SuccessFlg = TRUE;
				}
			}

			if((TRUE == SuccessFlg) || (pSnsParamCfgCtrl->SnsParamNeedUpdateFlg & ((uint16)1 << SnsID)))
			{
				pSnsParamCfgCtrl->SnsParamNeedUpdateFlg &= (uint16)(~((uint16)1 << SnsID));
				pSnsParamCfgCtrl->SnsParamCfgSeq = ReadSnsGain;
				/*更新190~197数据*/
				memcpy((void*)&DataBuf[0],(const void*)&RSnsEEPROMBuf.cData[0],4*2);
			}
			else
			{
				pSnsParamCfgCtrl->LastSeq = HandleReadSnsParamIDRes;
				pSnsParamCfgCtrl->SnsParamCfgSeq = PrepareNextSnsParamCfg;
			}
			
			TestErrCntSeq[2]++;
	   }
	   break;

	   case ReadSnsGain:
	   {
	   	   WRSnsMeasParam.SnsID = SnsID;
		   WRSnsMeasParam.eWRFlg = eRSnsMeasParam;
		   WRSnsMeasParam.ElmosMeasParam_StartAddr = Elmos17MEAS_SnsGain_StartAddr;
		   WRSnsMeasParam.u16Len = 1;
		   memset((void*)&RSnsEEPROMBuf.WData[0],0,WRSnsMeasParam.u16Len*2);
		   RWReturnFlg[DSIMasterID] = 0;
		   WRSnsMeasParam.pInOutDataBuf = &RSnsEEPROMBuf.WData[0];   
		   WRSnsMeasParam.pResultflg = &RWReturnFlg[DSIMasterID];
		   TriggerWRSnsMsg(DSIMasterID,pSnsCtrlGroup,&WRSnsMeasParam);
		   SetNextSnsCtrlGroupStatus(GroupStatus_SNSINIT);
		   
		   pSnsParamCfgCtrl->SnsParamCfgSeq = HandleReadSnsGainRes;
		   TestErrCntSeq[3]++;
	   }
	   break;

	   case HandleReadSnsGainRes:
	   {
			if(WR_SNS_MEAS_PARAM_SUCCESS == RWReturnFlg[DSIMasterID])
			{
				/*拷贝默认参数到写buf*/
				memcpy((void*)&WSnsEEPROMBuf.WData[0],(const void*)pMeasParamDefaultVal,Elmos17MEAS_Param_Check_u16Len*2);
				
				/*校准增益*/
				if((((RSnsEEPROMBuf.WData[0] >> 9) & 0x001F) > 6) /*&& ((RSnsEEPROMBuf.WData[0] & 0x001F) > 6)*/)/*如果校准增益太小，说明数据被异常修改，使用默认值*/
				{
					WriteCalAndAnaGain(RSnsEEPROMBuf.WData[0],pMeasParamCfg[eWRSnsDataType_GeneralSet].pCfgStr[SnsID]);
				}

				/*EEPROM校准版本、下线测试得到的阻抗数据*/
				memcpy((void*)&WSnsEEPROMBuf.cData[Elmos17MEAS_CALVer_StartAddr],(const void*)&DataBuf[2],3*2);
				
				/*parameter ID*/
				WSnsEEPROMBuf.cData[Elmos17MEAS_SnsParamID_StartAddr] = (uint8)(pSnsParamCfgCtrl->SnsParamID[SnsID] & 0x00FF);
				WSnsEEPROMBuf.cData[Elmos17MEAS_SnsParamID_StartAddr + 1] = (uint8)((pSnsParamCfgCtrl->SnsParamID[SnsID] >> 8) & 0x00FF);
				
				/*更新所有配置参数*/
				for(CfgType = eWRSnsDataType_GeneralSet;CfgType < eWRSnsDataType_NULL;CfgType++)
				{
					if(NULL != pMeasParamCfg[CfgType].pCfgStr[SnsID])
					{
						StartAddr = pMeasParamCfg[CfgType].ParamAddr;
						pMeasParamCfg[CfgType].ConvStructToU16DataFunc(pMeasParamCfg[CfgType].pCfgStr[SnsID],(uint16*)&WSnsEEPROMBuf.cData[StartAddr]);
					}
				}
				SuccessFlg = TRUE;
			}

			if(TRUE == SuccessFlg)
			{
				pSnsParamCfgCtrl->SnsParamCfgSeq = WriteSnsParam;	
			}
			else
			{
				pSnsParamCfgCtrl->SnsParamCfgSeq = PrepareNextSnsParamCfg;
			}
			
			TestErrCntSeq[4]++;
	   }
	   break;
		   
	   case WriteSnsParam:
	   {
	   		/*DSISlaveID 0 ~ FrontSnsGroupPwrAdcIdx(1)
	   		  DSISlaveID 1 ~ RearSnsGroupPwrAdcIdx(2) 
			  DSISlaveID 2 ~ SideSnsGroupPwrAdcIdx(3)*/
	   	   if(ReadSnsPwrADVal(SlotID + 1) > AD_CALIBRATION_9_5V)
	   	   {
		   	   WRSnsMeasParam.SnsID = SnsID;
			   WRSnsMeasParam.eWRFlg = eWSnsMeasParam;
			   WRSnsMeasParam.ElmosMeasParam_StartAddr = 0;
			   WRSnsMeasParam.u16Len = Elmos17MEAS_Param_Check_u16Len;
			   RWReturnFlg[DSIMasterID] = 0;			   
			   WRSnsMeasParam.pInOutDataBuf = &WSnsEEPROMBuf.WData[0];   
			   WRSnsMeasParam.pResultflg = &RWReturnFlg[DSIMasterID];
			   TriggerWRSnsMsg(DSIMasterID,pSnsCtrlGroup,&WRSnsMeasParam);
			   
			   SetNextSnsCtrlGroupStatus(GroupStatus_SNSINIT);
			   
			   pSnsParamCfgCtrl->SnsParamCfgSeq = ReadSnsParam;
	   	   }
		   else
		   {
			   pSnsParamCfgCtrl->SnsParamCfgSeq = PrepareNextSnsParamCfg;
		   }

		   
		   TestErrCntSeq[5]++;
	   }
	   break;
	   
	   case ReadSnsParam:
	   {
	   	   if(WR_SNS_MEAS_PARAM_SUCCESS == RWReturnFlg[DSIMasterID])
	   	   {
		   	   WRSnsMeasParam.SnsID = SnsID;
			   WRSnsMeasParam.eWRFlg = eRSnsMeasParam;
			   WRSnsMeasParam.ElmosMeasParam_StartAddr = 0;
			   WRSnsMeasParam.u16Len = Elmos17MEAS_Param_Check_u16Len;
			   memset((void*)&RSnsEEPROMBuf.WData[0],0,WRSnsMeasParam.u16Len*2);			   
			   RWReturnFlg[DSIMasterID] = 0;
			   WRSnsMeasParam.pInOutDataBuf = &RSnsEEPROMBuf.WData[0];   
			   WRSnsMeasParam.pResultflg = &RWReturnFlg[DSIMasterID];
			   TriggerWRSnsMsg(DSIMasterID,pSnsCtrlGroup,&WRSnsMeasParam);
			   SetNextSnsCtrlGroupStatus(GroupStatus_SNSINIT);
			   SuccessFlg = TRUE;
	   	   }

	   	   if(TRUE == SuccessFlg)
		   {
			   pSnsParamCfgCtrl->SnsParamCfgSeq = CheckSnsParam;	
		   }
		   else
		   {
			   pSnsParamCfgCtrl->SnsParamCfgSeq = PrepareNextSnsParamCfg;
		   }
		   
		   TestErrCntSeq[6]++;
	   }
	   break;
	   
	   case CheckSnsParam:
	   {
	   		if(WR_SNS_MEAS_PARAM_SUCCESS == RWReturnFlg[DSIMasterID])
	   		{
				for(Idx = 0;Idx < Elmos17MEAS_Param_Check_u16Len;Idx++)
		        {
		            if(WSnsEEPROMBuf.WData[Idx] != RSnsEEPROMBuf.WData[Idx])
		            {
						SuccessFlg = FALSE;
						PRINTF_ELMOS17("sns%d write param fail \n\r",SnsID);
						break;
		            }
					else
					{
						SuccessFlg = TRUE;
					}
		        }
	   		}

			if(TRUE == SuccessFlg)
			{
				pSnsParamCfgCtrl->SnsParamCfgSeq = EnEEPROMPrg;
				pSnsParamCfgCtrl->LastSeq = CheckSnsParam;
			}
			else
			{
				pSnsParamCfgCtrl->SnsParamCfgSeq = PrepareNextSnsParamCfg;
			}
			
			TestErrCntSeq[7]++;
	   }
	   break;
	  
	   case EnEEPROMPrg:
	   {
	   		if(ReadSnsPwrADVal(SlotID + 1) > AD_CALIBRATION_9_5V)
	   		{
				if(GetDSICHLWorkStatus(DSIMasterID,DSIChlID) == DSICHL_IDLE)
	         	{
	                if(eRet_OK == TransElmos17EEPROMAccessReq(DSIMasterID,DSIChlID + 1,(SlotID + 1),Elmos17EnEEPProg))
	                {
						SuccessFlg = TRUE;
	                }       
	                else
	                {
	                    /** @brief 传输错误 */				
	                    PRINTF_ELMOS17("sns%d Trans EnEEPROMPrg Err \r\n",SnsID); 
	                }
					
					if(TRUE == SuccessFlg)
					{
						pSnsParamCfgCtrl->SnsParamCfgSeq = HandleEnEEPROMPrgRes;
					}
					else
					{
						pSnsParamCfgCtrl->SnsParamCfgSeq = PrepareNextSnsParamCfg;
					}

					TestErrCntSeq[8]++;
				}
			}
			else
	   		{
				pSnsParamCfgCtrl->SnsParamCfgSeq = PrepareNextSnsParamCfg; 
			}
	   }
	   break;
	   
	   case HandleEnEEPROMPrgRes:
	   {	
		   if(GetDSICHLWorkStatus(DSIMasterID,DSIChlID) == DSICHL_IDLE)
		   {   
		   	   BufIdx = GetCRMRESData(DSIMasterID,&CRMRESOutData);
			   if(BufIdx != 0xFFFF)
			   {
			   	   if(CheckCRMResStatus(&CRMRESOutData,(SlotID + 1)) == eRet_OK)
			   	   {
						SuccessFlg = TRUE;
				   }
			   }

			   if(TRUE == SuccessFlg)
			   {
			   	   if(CheckSnsParam == pSnsParamCfgCtrl->LastSeq)
			   	   {
				   		pSnsParamCfgCtrl->SnsParamCfgSeq = WriteSnsParamToEEPROM;
			   	   }
				   else
				   {
						pSnsParamCfgCtrl->SnsParamCfgSeq = CopyParamEEPROMToRam;
				   }
			   }
			   else
			   {
				   pSnsParamCfgCtrl->SnsParamCfgSeq = PrepareNextSnsParamCfg;
			   }

			   TestErrCntSeq[9]++;
		   }
	   }
	   break;

	   case WriteSnsParamToEEPROM:
	   {
	   		/*DSISlaveID 0 ~ FrontSnsGroupPwrAdcIdx(1)
	   		  DSISlaveID 1 ~ RearSnsGroupPwrAdcIdx(2) 
			  DSISlaveID 2 ~ SideSnsGroupPwrAdcIdx(3)*/
	   	   if(ReadSnsPwrADVal(SlotID + 1) > AD_CALIBRATION_9_5V)
	   	   {
				if(GetDSICHLWorkStatus(DSIMasterID,DSIChlID) == DSICHL_IDLE)
				{
		            if(eRet_OK == TransElmos17EEPROMAccessReq(DSIMasterID,DSIChlID + 1,(SlotID + 1),Elmos17RAMToEEPROM))
		            {
		                SuccessFlg = TRUE;
		            }       
		            else
		            {
		                /** @brief 传输错误 */
		                PRINTF_ELMOS17("sns%d Trans Write Sns Param To EEPROM Err \r\n",SnsID);
		            }

				    if(TRUE == SuccessFlg)
				    {
						pSnsParamCfgCtrl->SnsParamCfgSeq = HandleWriteSnsParamToEEPROMRes;
				    }
				    else
				    {
						pSnsParamCfgCtrl->SnsParamCfgSeq = PrepareNextSnsParamCfg;
				    }
				   
				   TestErrCntSeq[10]++;
	            }
	   	   }
		   else
		   {
		   		/*不执行剩余流程*/
		   		pSnsParamCfgCtrl->SnsParamCfgSeq = PrepareNextSnsParamCfg;
		   }
	   }
	   break;
	   
	   case HandleWriteSnsParamToEEPROMRes:
	   { 		   
		   if(GetDSICHLWorkStatus(DSIMasterID,DSIChlID) == DSICHL_IDLE)
		   {
		   	   BufIdx = GetCRMRESData(DSIMasterID,&CRMRESOutData);
			   if(BufIdx != 0xFFFF)
			   {
			   	   if(CheckCRMResStatus(&CRMRESOutData,(SlotID + 1)) == eRet_OK)
			   	   {
						SuccessFlg = TRUE;
				   }
			   }
			   else
			   {
				   /** @brief 未接收到应答数据 */  
			   }

			   if(TRUE == SuccessFlg)
			   {
				   pSnsCtrlGroup->Time1msCnt = 0;
			   	   pSnsParamCfgCtrl->SnsParamCfgSeq = WaitParamSaveToEEPROM;
			   }
			   else
			   {
				   pSnsParamCfgCtrl->SnsParamCfgSeq = PrepareNextSnsParamCfg;
			   }

			   TestErrCntSeq[11]++;
		   }
	   }
	   break;

	   case WaitParamSaveToEEPROM:
	   {
		   if(pSnsCtrlGroup->Time1msCnt > WAIT_SNS_PARAM_RAM_TO_EEPROM)
		   {
				pSnsParamCfgCtrl->SnsParamCfgSeq = EnEEPROMPrg;
				pSnsParamCfgCtrl->LastSeq = WaitParamSaveToEEPROM;
		   }
	   }
	   break;
	   
	   case CopyParamEEPROMToRam:
	   {
			if(GetDSICHLWorkStatus(DSIMasterID,DSIChlID) == DSICHL_IDLE)
         	{
                if(eRet_OK == TransElmos17EEPROMAccessReq(DSIMasterID,DSIChlID + 1,(SlotID + 1),Elmos17EEPROMToRAM))
                {
					pSnsParamCfgCtrl->SnsParamCfgSeq = HandleCopyParamEEPROMToRamRes;
					pSnsCtrlGroup->Time1msCnt = 0;
                }       
                else
                {
                    /** @brief 传输错误 */				
                    PRINTF_ELMOS17("sns%d Trans CopyRamToEEPROM Err \r\n",SnsID);
					pSnsParamCfgCtrl->SnsParamCfgSeq = PrepareNextSnsParamCfg; 
                }

				TestErrCntSeq[12]++;
			}
	   }
	   break;

	   case HandleCopyParamEEPROMToRamRes:
	   {
		   if(GetDSICHLWorkStatus(DSIMasterID,DSIChlID) == DSICHL_IDLE)
		   {
		   	   BufIdx = GetCRMRESData(DSIMasterID,&CRMRESOutData);
			   if(BufIdx != 0xFFFF)
			   {
			   	   if(CheckCRMResStatus(&CRMRESOutData,(SlotID + 1)) == eRet_OK)
			   	   {
						SuccessFlg = TRUE;
				   }
			   }
			   else
			   {
				   /** @brief 未接收到应答数据 */  
			   }

			   if(TRUE == SuccessFlg)
			   {
				   pSnsCtrlGroup->Time1msCnt = 0;
			   	   pSnsParamCfgCtrl->SnsParamCfgSeq = WaitParamEEPROMToRam;
			   }
			   else
			   {
				   pSnsParamCfgCtrl->SnsParamCfgSeq = PrepareNextSnsParamCfg;
			   }

			   TestErrCntSeq[13]++;
		   }
	   }
	   break;
	   
	   case WaitParamEEPROMToRam:
	   {
		   if(pSnsCtrlGroup->Time1msCnt > WAIT_SNS_PARAM_EEPROM_TO_RAM)
		   {
				pSnsParamCfgCtrl->SnsParamCfgSeq = ReadSnsParamAfterWrite;
				TestErrCntSeq[14]++;
		   }
	   }
	   break;
	   
	   case ReadSnsParamAfterWrite:
	   {
		   WRSnsMeasParam.SnsID = SnsID;
		   WRSnsMeasParam.eWRFlg = eRSnsMeasParam;
		   WRSnsMeasParam.ElmosMeasParam_StartAddr = 0;
		   WRSnsMeasParam.u16Len = Elmos17MEAS_Param_Check_u16Len;
		   memset((void*)&RSnsEEPROMBuf.WData[0],0,WRSnsMeasParam.u16Len*2);			   
		   RWReturnFlg[DSIMasterID] = 0;
		   WRSnsMeasParam.pInOutDataBuf = &RSnsEEPROMBuf.WData[0];	 
		   WRSnsMeasParam.pResultflg = &RWReturnFlg[DSIMasterID];
		   TriggerWRSnsMsg(DSIMasterID,pSnsCtrlGroup,&WRSnsMeasParam);
		   SetNextSnsCtrlGroupStatus(GroupStatus_SNSINIT);
		   
		   pSnsParamCfgCtrl->SnsParamCfgSeq = CheckSnsParamAfterWrite;
		   TestErrCntSeq[15]++;

	   }
	   break;
	   
	   case CheckSnsParamAfterWrite:
	   {
			if(WR_SNS_MEAS_PARAM_SUCCESS == RWReturnFlg[DSIMasterID])
	   		{
				for(Idx = 0;Idx < Elmos17MEAS_Param_Check_u16Len;Idx++)
		        {
		            if(WSnsEEPROMBuf.WData[Idx] != RSnsEEPROMBuf.WData[Idx])
		            {
						SuccessFlg = FALSE;
						PRINTF_ELMOS17("sns%d write param fail \n\r",SnsID);
						break;
		            }
					else
					{
						SuccessFlg = TRUE;
					}
		        }
	   		}

			if(TRUE == SuccessFlg)
			{
				 pSnsParamCfgCtrl->SnsParamCfgFlg |= 0x0001 << SnsID;
				 SnsDiag_ClearEepromErrFlg(SnsID);
			}
			
			pSnsParamCfgCtrl->SnsParamCfgSeq = PrepareNextSnsParamCfg;
			
			TestErrCntSeq[16]++;
	   }
	   break;
	   
	   case PrepareNextSnsParamCfg:
	   {
			if((0 != (TestDMFlg & (0x0001 << SnsID))) 
				&& (0 == (pSnsParamCfgCtrl->SnsParamCfgFlg & (0x0001 << SnsID))) 
				&& (RetryCnt > 0))
			{
				RetryCnt--;
				if(HandleReadSnsParamIDRes == pSnsParamCfgCtrl->LastSeq)
				{
					pSnsParamCfgCtrl->SnsParamCfgSeq = ReadSnsParamID; //ReadSnsParamAfterPwrOn
					pSnsParamCfgCtrl->LastSeq = SnsParamCfgInit;
				}
				else
				{
					pSnsParamCfgCtrl->SnsParamCfgSeq = ReadSnsGain;
				}
			}
			else
			{
				SnsID++;
				RetryCnt = WRITE_SNS_EEPROM_RETRY_CNT;
				if(SnsID < SNSNum)
				{
					pSnsParamCfgCtrl->SnsParamCfgSeq = ReadSnsParamID; //ReadSnsParamAfterPwrOn
				}
				else
				{
					pSnsParamCfgCtrl->SnsParamCfgSeq = SnsParamCfgEnd;
					SnsID = SNS_FL_S;
				}
			}
	   }
	   break;
		   
	   case SnsParamCfgEnd:
	   {
			pSnsParamCfgCtrl->SnsParamCfgSeq = SnsParamCfgInit;
			pSnsParamCfgCtrl->LastSeq = SnsParamCfgInit;
			pSnsCtrlGroup->GroupStatus = GroupStatus_READSNSGAIN;
	   }
	   break;

	   default:
	   break;

   }
   
   return 0;
}


uint8 Elmos524_17_SelfTestSeq()
{
    static uint8 SelfTestFlg = 0U;
	uint8 returnFlg = 0U;
    SnsCtrlGroup_Cfg *pSnsCtrlGroup;
    WRSnsMeasParam_str WRSnsMeasParam[DSIMasterNum];
    
    DSIMasterID_en DSIMasterIdx = DSIMaster0;
    pSnsCtrlGroup = &SnsCtrlGroup;
    
    
    if(SelfTestFlg == 0)
    {
		for(DSIMasterIdx = DSIMaster0;DSIMasterIdx < DSIMasterNum;DSIMasterIdx++)
		{
	        if((GetDSICHLWorkStatus(DSIMasterIdx,DSIChl1) == DSICHL_IDLE)
	            &&(GetDSICHLWorkStatus(DSIMasterIdx,DSIChl2) == DSICHL_IDLE))
	        {
	            /** @brief 启动探头自检?妫 */
	            returnFlg |= TransElmos17ICModeReq(MasterSnsSelect[DSIMasterIdx],eElmos17IcMode_SelfTest);
	        }
	        else
	        {
	        	returnFlg = 1U;
	        }
		}

		if(0U == returnFlg)
		{		
			SelfTestFlg = 1;
			pSnsCtrlGroup->Time1msCnt = 0;
		}
		
    }
    else
    {
        /*521.17 SelfTest*/
        if(pSnsCtrlGroup->Time1msCnt > 50)
        {
            SelfTestFlg = 0;
            pSnsCtrlGroup->GroupStatus = GroupStatus_START_MEAS;
			
            for(DSIMasterIdx = DSIMaster0;DSIMasterIdx < DSIMasterNum;DSIMasterIdx++)
            {
	            InitWRSnsMeasParam_str(&WRSnsMeasParam[DSIMasterIdx]);
	            WRSnsMeasParam[DSIMasterIdx].eWRFlg = eRALLSnsSelfTestResult;
	            WRSnsMeasParam[DSIMasterIdx].SnsID = MasterSnsSelect[DSIMasterIdx];				
				RWReturnFlg[DSIMasterIdx] = 0;
	            WRSnsMeasParam[DSIMasterIdx].pResultflg = &RWReturnFlg[DSIMasterIdx];
	            TriggerWRSnsMsg(DSIMasterIdx,pSnsCtrlGroup,&WRSnsMeasParam[DSIMasterIdx]);
            }
			
            SetNextSnsCtrlGroupStatus(GroupStatus_START_MEAS);
        }
        else
        {

        }
    }
    return 0;
}

uint8 Elmos524_17_StandbySeq()
{

    SnsCtrlGroup_Cfg *pSnsCtrlGroup;
    DSIMasterID_en DSIMasterID;
    
    pSnsCtrlGroup = &SnsCtrlGroup;

    DSIMasterID = DSIMaster0;
    if(StandbyFlg == 0)
    {
        if((GetDSICHLWorkStatus(DSIMaster0,DSIChl1) == DSICHL_IDLE)
            &&(GetDSICHLWorkStatus(DSIMaster0,DSIChl2) == DSICHL_IDLE)
            &&(GetDSICHLWorkStatus(DSIMaster1,DSIChl1) == DSICHL_IDLE)
            &&(GetDSICHLWorkStatus(DSIMaster1,DSIChl2) == DSICHL_IDLE))
        {
            StandbyFlg = 1;
            /** @brief Standby */
            TransElmos17ICModeReq(SNS_M0_ALL,eElmos17IcMode_Standby);
			TransElmos17ICModeReq(SNS_M1_ALL,eElmos17IcMode_Standby);
            pSnsCtrlGroup->Time1msCnt = 0;
        }
        else
        {
            
        }
    }    
    else
    {

    }
    return 0;
}

uint8 Elmos524_17_WakeUpSeq()
{
    SnsCtrlGroup_Cfg *pSnsCtrlGroup;
    DSIMasterID_en DSIMasterID;
    DSIMasterID = DSIMaster0;
    pSnsCtrlGroup = &SnsCtrlGroup;
    static uint8 WakeUpFlg = 0;
    
    if((GetDSICHLWorkStatus(DSIMaster0,DSIChl1) == DSICHL_IDLE)
        &&(GetDSICHLWorkStatus(DSIMaster0,DSIChl2) == DSICHL_IDLE)
        &&(GetDSICHLWorkStatus(DSIMaster1,DSIChl1) == DSICHL_IDLE)
        &&(GetDSICHLWorkStatus(DSIMaster1,DSIChl2) == DSICHL_IDLE))
    {
        StandbyFlg = 0;
        WakeUpFlg = 1;
        /** @brief 唤醒 */
        TransElmos17ICModeReq(SNS_M0_ALL,eElmos17IcMode_WakeUp);
		TransElmos17ICModeReq(SNS_M1_ALL,eElmos17IcMode_WakeUp);
        pSnsCtrlGroup->Time1msCnt = 0;
        
    }
    else
    {
        
    }
    
    if(WakeUpFlg == 1)
    {
        if(pSnsCtrlGroup->Time1msCnt > 1)
        {
            WakeUpFlg = 0;
            pSnsCtrlGroup->GroupStatus = GetNextSnsCtrlGroupStatus();
        }
    }  
    else
    {
    
    }
    return 0;
}

void Elmos524_17_SnsGainComp(void)
{
    DSIMasterID_en DSIMasterIdx = DSIMaster0;
    AK2_Car_AmbientTempType LenuCar_AmbientTemp = 25;
	uint8 Lu8TableIdx = 0xFF;
	int8 LGainComp = 0;
    static int8 Lu8OldGainComp = 0;
	Elmos17_AdjSymbol_e LenuDataSymbol = eElmos17_Adj_Positive;
	uint8 Lu8AdjustData = 0;
	
	LenuCar_AmbientTemp = AK2_ReadCAN_AppSignal_Car_AmbientTempType();
	if(LenuCar_AmbientTemp < -40)
	{
		LenuCar_AmbientTemp = -40;
	}

    Lu8TableIdx = (LenuCar_AmbientTemp + 40) / 2u; /*step is 2 */
	
    if(Lu8TableIdx > (sizeof(GTempGainCompTable) - 1))
    {
        Lu8TableIdx =  (sizeof(GTempGainCompTable) - 1);
    }
	
    LGainComp = GTempGainCompTable[Lu8TableIdx];
	
    if(Lu8OldGainComp != LGainComp)
    {
		Lu8OldGainComp = LGainComp;
		Lu8AdjustData = ABS_VALUE(LGainComp);
		
		if(LGainComp >= 0)
		{
			LenuDataSymbol = eElmos17_Adj_Positive;
		}
		else
		{
			LenuDataSymbol = eElmos17_Adj_Negative;
		}

		for(DSIMasterIdx = DSIMaster0;DSIMasterIdx < DSIMasterNum;DSIMasterIdx++)
		{
			TransElmos17AdjustSensitivity(DSIMasterIdx,DSIChlSEL1And2,ALL_SNS_SELECT,eElmos17_Adj_Gain,LenuDataSymbol,Lu8AdjustData);
		}
    }
}

void Elmos524_17_SnsReDMCheck(SnsReDMCheckCtrl* pSnsReDMCheckCtrl)
{
	SnsID_en SnsID = SNSID_NONE;	
	static DSIMasterID_en DSIMasterID = DSIMaster0;
	static DSIChlID_en DSIChlID = DSIChl1;
	static uint8 SlotID = 0;
	Elmos17RetType_en RetVal = eRet_N_OK;
	uint16 Flg = 0xFFFF;
    CRM_RESPONSE_Data_str CRMRESOutData = {0};
	bool ReDMFlg = FALSE;
    SnsCtrlGroup_Cfg *pSnsCtrlGroup = &SnsCtrlGroup;	
	SnsErrCtrl_t* pSnsErrCtrl = SnsDiag_GetSnsErrCtrl();
	
	switch(pSnsReDMCheckCtrl->SnsReDMCheckSeq)
	{		
		case GetNoSigSnsInfo:
		{
			for(SnsID = SNS_FL_S;SnsID < SNSNum;SnsID++)
			{
				if((pSnsErrCtrl->SnsErrMask) & ((uint16)1 << SnsID))
				{
					pSnsErrCtrl->SnsErrMask &= (~(uint16)((uint16)1 << SnsID));
					DSIMasterID = SnsAddCfg[SnsID].DSIMasterID;
					DSIChlID = SnsAddCfg[SnsID].DSIChlSEL - 1U;
					SlotID = SnsAddCfg[SnsID].cSnsPhysAddr - 1U;
					break;
				}
			}

			AK2_WriteEnSnsPin(0);/*重新寻址，需要给全部探头断电*/
			pSnsCtrlGroup->Time1msCnt = 0;
			pSnsReDMCheckCtrl->SnsReDMCheckSeq = SnsReDMCheckSeqEnd;//GetSnsSoftwareVer;
			break;
		}

		case GetSnsSoftwareVer:
		{
			if(GetDSICHLWorkStatus(DSIMasterID,DSIChlID) == DSICHL_IDLE)
            {
                /** @brief 读取探头软件版本 */
				RetVal = TransElmos17ReadStatusInCRMReq(DSIMasterID,DSIChlID+1,SNS_DEFAULT_ID,eElmos17_CRMReadStatusAddr_SoftwareVersion);
				
                if(eRet_OK != RetVal)
                {
                    /** @brief 传输错误 */
                    PRINTF_ELMOS17("DSIMaster[%d] Trans ReadStatusInCRM Err \r\n",DSIMasterID);
                }

				pSnsReDMCheckCtrl->SnsReDMCheckSeq = CheckGetSnsSoftwareVerRes;
            }
			break;
		}

		case CheckGetSnsSoftwareVerRes:
		{
			if(GetDSICHLWorkStatus(DSIMasterID,DSIChlID) == DSICHL_IDLE)
            {
				memset((void*)&CRMRESOutData,0,sizeof(CRM_RESPONSE_Data_str));

                Flg = GetCRMRESData(DSIMasterID,&CRMRESOutData);

                if(Flg != 0xFFFF)
                {
                    if((CRMRESOutData.ResData.DSIStatus.Status == 0) && (CRMRESOutData.ResData.DSIStatus.SymbolCount == 8))
                    {
                        /** @brief 数据接收正确 */
                        if(((CRMRESOutData.ResData.Data.PhysAddr_Status >> 4) & 0x0F) == SNS_DEFAULT_ID)
                        {
							if((CRMRESOutData.ResData.Data.Data[0] == 0) && (CRMRESOutData.ResData.Data.Data[1] == 0))
                            {
                                /** @brief 软件版本错误*/								
                                PRINTF_ELMOS17("DSIMaster%d channel%d slot%d REDM Software Ver Err \r\n",DSIMasterID,CRMRESOutData.DSIChlID,SlotID);
                            }
							else
							{
								ReDMFlg = TRUE;
							}
                        }
                        else
                        {
                            /** @brief 数据错误 */							
                            PRINTF_ELMOS17("DSIMaster%d channel%d slot%d REDM Resp Data Err \r\n",DSIMasterID,CRMRESOutData.DSIChlID,SlotID);
                        }
                    }
                    else
                    {
                        /** @brief DSI 状态  */						
						PRINTF_ELMOS17("DSIMaster%d channel%d slot%d REDM Resp status Err \r\n",DSIMasterID,CRMRESOutData.DSIChlID,SlotID);
                    }
                }
                else
                {
                    /** @brief 未接收到应答数据 */
			        PRINTF_ELMOS17("DSIMaster%d channel%d slot%d REDM No Resp Err \r\n",DSIMasterID,CRMRESOutData.DSIChlID,SlotID);              
                }

				if(TRUE == ReDMFlg)
				{
					pSnsReDMCheckCtrl->SnsReDMCheckSeq = SnsReDMCheckSeqEnd;
					AK2_WriteEnSnsPin(0);/*重新寻址，需要给全部探头断电*/
					pSnsCtrlGroup->Time1msCnt = 0;
				}
				else
				{
					if(pSnsErrCtrl->SnsErrMask != 0)
					{
						/*确认其他探头是否有插上*/
						pSnsReDMCheckCtrl->SnsReDMCheckSeq = GetNoSigSnsInfo;
					}
					else
					{
						/*没有探头被插上，复位SnsReDMCheckCtrl，重新延时后再次进入该函数*/
						memset((void*)pSnsReDMCheckCtrl,0,sizeof(SnsReDMCheckCtrl));
						SnsDiag_ClearSnsErrCtrl();
					    DSIMasterID = DSIMaster0;
						DSIChlID = DSIChl1;
						SlotID = 0;
						pSnsCtrlGroup->GroupStatus = GroupStatus_START_MEAS;
					}
				}
            }
			
			break;
		}

		case SnsReDMCheckSeqEnd:
		{
			if(pSnsCtrlGroup->Time1msCnt > WAIT_SNS_POWER_OFF)
			{
				/*复位寻址 写EEPROM参数 相关变量*/
				TestDMFlg = 0;
				GstrSnsParamCfgCtrl.SnsParamCfgFlg = 0;
				memset((void*)pSnsReDMCheckCtrl,0,sizeof(SnsReDMCheckCtrl));
				SnsDiag_ClearSnsErrCtrl();
			    DSIMasterID = DSIMaster0;
				DSIChlID = DSIChl1;
				SlotID = 0;
				pSnsCtrlGroup->Time1msCnt = 0;
				pSnsCtrlGroup->GroupStatus = GroupStatus_SNS_DM;
			}
			break;
		}
		
		default:
		break;
	}
}

void Elmos524_17_ReadSnsGain()
{
	SnsCtrlGroup_Cfg *pSnsCtrlGroup = &SnsCtrlGroup;
   	DSIMasterID_en DSIMasterID = DSIMaster0;
    DSIChlID_en DSIChlID = DSIChl1;
    uint8 SlotID = 0;
    static SnsID_en SnsID = SNS_FL_S;
	CRM_RESPONSE_Data_str CRMRESOutData;
	WRSnsMeasParam_str WRSnsMeasParam;
	static SnsParamCfgSeq_en ReadSnsGainSeq = ReadSnsGain;
	
    memset((void*)&CRMRESOutData,0,sizeof(CRM_RESPONSE_Data_str));
	memset((void*)&WRSnsMeasParam,0,sizeof(WRSnsMeasParam_str));

	DSIMasterID = SnsAddCfg[SnsID].DSIMasterID;
	DSIChlID = SnsAddCfg[SnsID].DSIChlSEL - 1U;
	SlotID = SnsAddCfg[SnsID].cSnsPhysAddr - 1U;

	switch(ReadSnsGainSeq)
	{
	    case ReadSnsGain:
	    {
	   	    WRSnsMeasParam.SnsID = SnsID;
		    WRSnsMeasParam.eWRFlg = eRSnsMeasParam;
		    WRSnsMeasParam.ElmosMeasParam_StartAddr = Elmos17MEAS_SnsGain_StartAddr;
		    WRSnsMeasParam.u16Len = 1;
		    memset((void*)&RSnsEEPROMBuf.WData[0],0,WRSnsMeasParam.u16Len*2);
		    RWReturnFlg[DSIMasterID] = 0;
		    WRSnsMeasParam.pInOutDataBuf = &RSnsEEPROMBuf.WData[0];   
		    WRSnsMeasParam.pResultflg = &RWReturnFlg[DSIMasterID];
		    TriggerWRSnsMsg(DSIMasterID,pSnsCtrlGroup,&WRSnsMeasParam);
		    SetNextSnsCtrlGroupStatus(GroupStatus_READSNSGAIN);
		   
		    ReadSnsGainSeq = HandleReadSnsGainRes;
	    }
	    break;

	   case HandleReadSnsGainRes:
	   {
			if(WR_SNS_MEAS_PARAM_SUCCESS == RWReturnFlg[DSIMasterID])
			{
				u8SnsGain[SnsID] = (uint8)((RSnsEEPROMBuf.WData[0] >> 9) & 0x001F); 
			}

			ReadSnsGainSeq = PrepareNextSnsParamCfg;
	   }
	   break;	

	   case PrepareNextSnsParamCfg:
	   {
	   		SnsID++;
			if(SnsID < SNSNum)
			{
				ReadSnsGainSeq = ReadSnsGain;
			}
			else
			{	
				ReadSnsGainSeq = SnsParamCfgEnd;
			}
	   }
	   break;
		
	   case SnsParamCfgEnd:
	   {
	   		ReadSnsGainSeq = ReadSnsGain;
			SnsID = SNS_FL_S;	
			pSnsCtrlGroup->GroupStatus = GroupStatus_SNSSelfTest;
	   }
	   break;
	   
	   default:
	   	break;
   
	}
}

/******************************************************************************
* 函数名称: Elmos524_17_GetRandom
* 设计作者: Fris
* 设计日期: 2022-05-18 09:50
* 功能描述: 利用系统时间生成随机数
* 参数描述: 无
* 返回类型: void
* 修改人员: 无
* 修改日期: 无
* 修改内容: 无
*******************************************************************************/
static uint8 Elmos524_17_GetRandom()
{
    static uint8 Lu8Index = 0;
    uint8 LvTemp = 0;

    Lu8Index++;
    switch(Lu8Index)
    {
        case 0:
            LvTemp = 2;
            break;
        case 1:
            LvTemp = 11;
            break;
        case 2:
            LvTemp = 6;
            break;
        case 3:
            LvTemp = 0;
            break;
        case 4:
            LvTemp = 3;
            break;
        case 5:
            LvTemp = 7;
            break;
        case 6:
            LvTemp = 9;
            break;
        case 7:
            LvTemp = 3;
            break;
        case 8:
            LvTemp = 0;
            break;
        case 9:
            LvTemp = 5;
            break;
        case 10:
            LvTemp = 1;
            break;
        default:
            Lu8Index = 0;
            LvTemp = 7;
            break;
    }
    
    return LvTemp;
}

void SnsWorkStsSwitch(MeasSeqListIdx_en *pMeasSeqSel,uint8 *pFxnSts,SnsMeasProfile_en *pSTDProfile,SnsMeasProfile_en *pADVProfile)
{
	static MeasSeqListIdx_en MeasSeqSelect = 0;
	static uint8 FunctionOn = TRUE;
	uint16 Lu16Car_Speed = 0;
	static SnsMeasProfile_en STDProfile = Profile_A;
	static SnsMeasProfile_en ADVProfile = Profile_A;
	ADAS_APAStatusType LenuAPASts = APA_NONE;
	
	ReadCAN_AppSignal_Car_Speed(&Lu16Car_Speed);
	ReadCAN_AppSignal_ADAS_APAStatus(&LenuAPASts);
	
	if(Lu16Car_Speed > HIGH_SPEED_SEQ_OFF_SPEED)
	{
		FunctionOn = FALSE;
	}						
	else if(Lu16Car_Speed < HIGH_SPEED_SEQ_ON_SPEED)
	{
		FunctionOn = TRUE;
	}
	else
	{
		/*FunctionOn keep last status*/
	}

	if(TRUE == FunctionOn)
	{
		if(Lu16Car_Speed > MIDDLE_SPEED_SEQ_OFF_SPEED)
		{
			/*high speed seq*/
			#if (FIX_FREQUENCY_SEQ == 1)
			MeasSeqSelect = MeasSeqList4;
			#else
				#if (HIGH_SPEED_USE_FIX_FRE == 0)
				MeasSeqSelect = MeasSeqList3;
				STDProfile = Profile_A;
				#else
				MeasSeqSelect = MeasSeqList4;
				STDProfile = Profile_B;
				#endif
			#endif
		}
		else if((Lu16Car_Speed < MIDDLE_SPEED_SEQ_ON_SPEED) 
			  && (Lu16Car_Speed > LOW_SPEED_SEQ_OFF_SPEED))
		{
			if(APA_GUIDANCE != LenuAPASts)
			{
				/*middle speed seq*/
				#if (FIX_FREQUENCY_SEQ == 1)
				MeasSeqSelect = MeasSeqList2;
				#else
				MeasSeqSelect = MeasSeqList1;
				STDProfile = Profile_A;
				#endif
			}
			else
			{
				MeasSeqSelect = MeasSeq_Guidance;
				STDProfile = Profile_A;
			}
		}
		else if(Lu16Car_Speed < LOW_SPEED_SEQ_ON_SPEED)
		{
			if(APA_GUIDANCE != LenuAPASts)
			{
				/*low speed seq*/
				#if (FIX_FREQUENCY_SEQ == 1)
				MeasSeqSelect = MeasSeqList2;
				#else
				MeasSeqSelect = MeasSeqList0;
				STDProfile = Profile_A;
				#endif
			}
			else
			{
				MeasSeqSelect = MeasSeq_Guidance;
				STDProfile = Profile_A;
			}
		}
		else
		{
			if(APA_GUIDANCE == LenuAPASts)
			{
				MeasSeqSelect = MeasSeq_Guidance;
				STDProfile = Profile_A;
			}
			/*MeasSeqSel keep last status*/
		}
	}
	
	*pMeasSeqSel = MeasSeqSelect; 
	*pFxnSts = FunctionOn;
	*pSTDProfile = STDProfile;
	*pADVProfile = ADVProfile;
}

void SnsEchoDataHandle(SnsMeasDistData_Str* pSnsMeasDistData,MeasChlECHOData_Str (*pMeasECHOMsg)[DSIChlNum])
{
	DSIMasterID_en DSIMasterIdx = DSIMaster0;
	DSIChlID_en DSIChlIdx = DSIChl1;	
    uint8 LcCnt=0;
	DSIMasterID_en FrontDSIMaster = DSIMaster0; /*前后是相对于SnsID_en的定义来说的*/
	DSIMasterID_en RearDSIMaster = DSIMaster0;
	DSIChlID_en	FrontDSIChl = DSIChl1;
	DSIChlID_en	RearDSIChl = DSIChl1;
	uint8	FrontIdx = 0;/*地址范围为1~DSISlaveSnsNum，数据索引范围0~DSISlaveSnsNum-1*/
	uint8	RearIdx = 0;
	SnsID_en SnsID = SNSID_NONE;
	Sns_PDCM_MEASMsg_Str *pSns_PDCM_MEASMsg;
	ThresholdSet_str * pThresholdSet = NULL;
	bool NeedTimeCompensationFlag = FALSE;
	int32 TimeCompensation = 0;
	Sns_PDCM_NFDData *pNFDData = NULL;
	SnsSelfTestStatusMsg_str *pSnsSelfTestStatusMsg = NULL;
	
	memset((void*)&pSnsMeasDistData[0],0,sizeof(SnsMeasDistData_Str) * SNSNum);	
		
	for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
	{
		for(DSIChlIdx = DSIChl1; DSIChlIdx < DSIChlNum; DSIChlIdx++)
		{
			for(LcCnt = 0;LcCnt < DSISlaveSnsNum;LcCnt ++)
			{
				SnsID = GetSnsID(DSIMasterIdx,DSIChlIdx,(LcCnt+1));
				NeedTimeCompensationFlag = FALSE;
				TimeCompensation = 0;
				pSns_PDCM_MEASMsg = &pMeasECHOMsg[DSIMasterIdx][DSIChlIdx].ECHOMsg[LcCnt];

				pSnsMeasDistData[SnsID].SnsDistMsg.SNS_MEAS_TYPE = pMeasECHOMsg[DSIMasterIdx][DSIChlIdx].ECHOMsg[LcCnt].SNS_MEAS_TYPE;
				
				/*如果探头为侦听且和主发不在同一个master,侦听需要补偿*/
				if((MEAS_STD_LISTEN == pMeasECHOMsg[DSIMasterIdx][DSIChlIdx].ECHOMsg[LcCnt].SNS_MEAS_TYPE) || (MEAS_ADV_LISTEN == pMeasECHOMsg[DSIMasterIdx][DSIChlIdx].ECHOMsg[LcCnt].SNS_MEAS_TYPE))
				{
					if((SNS_FL_S == SnsID) || (SNS_RR_S == SnsID))
					{
						/*+1:后一个探头为主发*/
						RearDSIMaster = GetSnsDSIMasterID(SnsID + 1U);
						RearDSIChl = GetSnsDSIChlSEL(SnsID + 1U) - 1U;
						RearIdx = GetSns_PhyAddr(SnsID + 1U) - 1U;

						if(pMeasECHOMsg[RearDSIMaster][RearDSIChl].ECHOMsg[RearIdx].SNS_MEAS_TYPE < MEAS_STD_LISTEN) 
						{
							if(DSIMasterIdx != RearDSIMaster)
							{
								NeedTimeCompensationFlag = TRUE;
							}
						}
						else
						{
							continue;
						}
					}
					else if((SNS_FR_S == SnsID) || (SNS_RL_S == SnsID))
					{
						/*-1:前一个探头主发*/
						FrontDSIMaster = GetSnsDSIMasterID(SnsID - 1U);
						FrontDSIChl = GetSnsDSIChlSEL(SnsID - 1U) - 1U;
						FrontIdx = GetSns_PhyAddr(SnsID - 1U) - 1U;

						if(pMeasECHOMsg[FrontDSIMaster][FrontDSIChl].ECHOMsg[FrontIdx].SNS_MEAS_TYPE < MEAS_STD_LISTEN) 
						{
							if(DSIMasterIdx != FrontDSIMaster)
							{
								NeedTimeCompensationFlag = TRUE;
							}
						}
						else
						{
							continue;
						}
					}
					else
					{	
						/*-1:前一个探头主发*/
						FrontDSIMaster = GetSnsDSIMasterID(SnsID - 1U);
						FrontDSIChl = GetSnsDSIChlSEL(SnsID - 1U) - 1U;
						FrontIdx = GetSns_PhyAddr(SnsID - 1U) - 1U;
						
						RearDSIMaster = GetSnsDSIMasterID(SnsID + 1U);
						RearDSIChl = GetSnsDSIChlSEL(SnsID + 1U) - 1U;
						RearIdx = GetSns_PhyAddr(SnsID + 1U) - 1U;
						
						if(pMeasECHOMsg[FrontDSIMaster][FrontDSIChl].ECHOMsg[FrontIdx].SNS_MEAS_TYPE < MEAS_STD_LISTEN)
						{
							if(DSIMasterIdx != FrontDSIMaster)
							{
								NeedTimeCompensationFlag = TRUE;
							}																			
						}
						else if(pMeasECHOMsg[RearDSIMaster][RearDSIChl].ECHOMsg[RearIdx].SNS_MEAS_TYPE  < MEAS_STD_LISTEN)
						{
							if(DSIMasterIdx != RearDSIMaster)
							{
								NeedTimeCompensationFlag = TRUE;
							}
						}
						else
						{
							continue;
						}
					}

					if(TRUE == NeedTimeCompensationFlag)
					{
						TimeCompensation = (int32)CalcMasterStartMeasTimeInterval(pMeasECHOMsg[DSIMaster0][DSIChlIdx].TransMeasCmdTime,pMeasECHOMsg[DSIMaster1][DSIChlIdx].TransMeasCmdTime);
						if(DSIMaster0 == DSIMasterIdx)
						{
							TimeCompensation = -1 * TimeCompensation;
						}
					}
				}
				else if((MEAS_IDLE == pMeasECHOMsg[DSIMasterIdx][DSIChlIdx].ECHOMsg[LcCnt].SNS_MEAS_TYPE) || (MEAS_ADV_LISTEN == pMeasECHOMsg[DSIMasterIdx][DSIChlIdx].ECHOMsg[LcCnt].SNS_MEAS_TYPE))
				{
					continue;
				}
				else
				{}

				pSnsSelfTestStatusMsg = GetSnsSelfTestStatusAddr(DSIMasterIdx,DSIChlIdx,LcCnt);

				pSnsMeasDistData[SnsID].MeasType = pMeasECHOMsg[DSIMasterIdx][DSIChlIdx].MeasType;
				pSnsMeasDistData[SnsID].MeasSeq = pMeasECHOMsg[DSIMasterIdx][DSIChlIdx].MeasSeq;
				pSnsMeasDistData[SnsID].MeasProfile = pMeasECHOMsg[DSIMasterIdx][DSIChlIdx].MeasProfile;
				pSnsMeasDistData[SnsID].TransMeasCmdTime = pMeasECHOMsg[DSIMasterIdx][DSIChlIdx].TransMeasCmdTime;
				pSnsMeasDistData[SnsID].UpdateTime = pMeasECHOMsg[DSIMasterIdx][DSIChlIdx].UpdateTime;
				pSnsMeasDistData[SnsID].ADASSYNTime = pMeasECHOMsg[DSIMasterIdx][DSIChlIdx].ADASSYNTime;
				pSnsMeasDistData[SnsID].SnsDiagData.RTM = pMeasECHOMsg[DSIMasterIdx][DSIChlIdx].ECHOMsg[LcCnt].RTData.RTM;
				pSnsMeasDistData[SnsID].SnsDiagData.RFreq = CalcRingFreq(SAMPLE_FREQUENCY,&pMeasECHOMsg[DSIMasterIdx][DSIChlIdx].ECHOMsg[LcCnt].RTData);
				pSnsMeasDistData[SnsID].SnsDiagData.ImpAndImpIdx[0] = pSnsSelfTestStatusMsg->Resverd2[0];
				pSnsMeasDistData[SnsID].SnsDiagData.ImpAndImpIdx[1] = pSnsSelfTestStatusMsg->Resverd2[1];
				pSnsMeasDistData[SnsID].SnsDiagData.NoiseSum = pMeasECHOMsg[DSIMasterIdx][DSIChlIdx].ECHOMsg[LcCnt].DiagData1.NoiseSum;
				pSnsMeasDistData[SnsID].SnsDiagData.NoiseAbnormalCnt = 0;//= pMeasECHOMsg[DSIMasterIdx][DSIChlIdx].ECHOMsg[LcCnt].DiagData1.NoiseCount;
				
				pNFDData = &pMeasECHOMsg[DSIMasterIdx][DSIChlIdx].ECHOMsg[LcCnt].NFDData;
					
				EchoDet_SnsEchoHandle(SnsID,pSns_PDCM_MEASMsg->SNS_MEAS_TYPE,pThresholdSet,&pSns_PDCM_MEASMsg->ECHOMsgData,&pSnsMeasDistData[SnsID].SnsDistMsg,TimeCompensation,pNFDData);
				
				if(MEAS_STD_EMIT == pSnsMeasDistData[SnsID].SnsDistMsg.SNS_MEAS_TYPE)
				{
					EchoDet_SnsSTDNFDHandle(SnsID,pNFDData,&pSnsMeasDistData[SnsID].SnsDistMsg);
				}

				if(pSnsMeasDistData[SnsID].SnsDistMsg.SNS_MEAS_TYPE < MEAS_STD_LISTEN)
				{
					pSnsMeasDistData[SnsID].SnsDiagData.NoiseAbnormalCnt = EchoDet_NoiseHandle(SnsID,&pSnsMeasDistData[SnsID].SnsDiagData);
				}
				
				SnsDiag_SnsDiagHandle(SnsID);
			}
		}
	}
}

void StopSnsGroupMeas(void)
{
	DSIMasterID_en DSIMasterIdx = DSIMaster0;
	DSIChlID_en DSIChlIdx = DSIChl1;
	SnsCtrlGroup_Cfg *pSnsCtrlGroup = &SnsCtrlGroup;
	
	for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
    {
		for(DSIChlIdx = DSIChl1; DSIChlIdx < DSIChlNum; DSIChlIdx++)
		{
			pSnsCtrlGroup->SnsGroupMeasStatus[DSIMasterIdx][DSIChlIdx] = SnsGroupMeas_IDLE;
			SetDSICHLWorkStatus(DSIMasterIdx,DSIChlIdx,DSICHL_IDLE);
		}
	}
}

// uint8 FlgTmp = 0;
void Elmos524_17_SnsCtrlMainFunc()
{
    uint8 Flg = 0;
    SnsGroupStatus_en GroupStatus;
    SnsCtrlGroup_Cfg *pSnsCtrlGroup;
    DSIMasterID_en DSIMasterID = DSIMaster0;
	DSIMasterID_en DSIMasterIdx = DSIMaster0;
	DSIChlID_en DSIChlIdx = DSIChl1;
    pSnsCtrlGroup = &SnsCtrlGroup;	
	MeasSeqListIdx_en MeasSeqSel = 0;  
	uint8 FxnOn = TRUE;
	static bool ReadSnsStatus = FALSE; 
	AK2_Signal_VoltageStatusType WorkVolSts = NORMAL_VOLTAGE;
	static uint8 IDUpdateDone = 0;
	SnsMeasProfile_en StandProfile = Profile_A;
	SnsMeasProfile_en AdvanceProfile = Profile_A;
	SnsMeasProfile_en MeasProfile = Profile_A;
	const SnsGroupMeasSeq_str *pRSnsGroupMeasSeq = NULL;
    const CMDSeq_en *pRSeqlist = NULL;
    const SnsGroupMeasSeq_str *pFSnsGroupMeasSeq = NULL;
    const CMDSeq_en *pFSeqlist = NULL;
	AK2_SnsPwrDiagStatus_e SnsPwrSts[SnsGroupPwrDiagNum] = {0};
	SnsErrCtrl_t* pSnsErrCtrl = SnsDiag_GetSnsErrCtrl();
	static uint8 SnsDataUpdate = FALSE;
	static uint32 MeasDelayStartTime = 0;
	static uint8 MeasDelayTime = 0;
	SnsParamCfgCtrl *pSnsParamCfgCtrl = &GstrSnsParamCfgCtrl;
		
	static AK2_Signal_VoltageStatusType SnsPwrStsLast = NORMAL_VOLTAGE;

    GroupStatus = pSnsCtrlGroup->GroupStatus;
	SnsWorkStsSwitch(&MeasSeqSel,&FxnOn,&StandProfile,&AdvanceProfile);
	WorkVolSts = AK2_ReadPwrManage_GroupStatus();

	AK2_ReadPwrManage_SnsPwrDiagStatus(FrontSnsGroupPwrDiagIdx,&SnsPwrSts[FrontSnsGroupPwrDiagIdx]);
	AK2_ReadPwrManage_SnsPwrDiagStatus(RearSnsGroupPwrDiagIdx,&SnsPwrSts[RearSnsGroupPwrDiagIdx]);
	AK2_ReadPwrManage_SnsPwrDiagStatus(SideSnsGroupPwrDiagIdx,&SnsPwrSts[SideSnsGroupPwrDiagIdx]);
	
	pRSnsGroupMeasSeq = &RearSnsGroupMeasSeqList[MeasSeqSel];
    pRSeqlist = pRSnsGroupMeasSeq->pSeqlist;
    pFSnsGroupMeasSeq = &FrontSnsGroupMeasSeqList[MeasSeqSel];
    pFSeqlist = pFSnsGroupMeasSeq->pSeqlist;

	if(MeasSeqIndex >= pRSnsGroupMeasSeq->SeqListNum)
    {
        MeasSeqIndex = 0;
    }
		
    switch(GroupStatus)
    {

        case GroupStatus_SNS_DM :
        {		
            if((GetDSIComWorkStatus(DSIMaster0) == DSI_MASTER_READY) && (GetDSIComWorkStatus(DSIMaster1) == DSI_MASTER_READY)
				&& (PWR_PIN_NORMAL == SnsPwrSts[FrontSnsGroupPwrDiagIdx]) 
				&& (PWR_PIN_NORMAL == SnsPwrSts[RearSnsGroupPwrDiagIdx]) 
				&& (PWR_PIN_NORMAL == SnsPwrSts[SideSnsGroupPwrDiagIdx]))
            {
                /** @brief DSI 寻址*/
				#if (1== PARALLEL_DM_METHOD1)
				// if(0 == IDUpdateDone)
				// {
				// 	if(1 == Elmos524_17_UpdateIDToDefault(&TestDMFlg))
				// 	{
				// 		TestDMFlg = 0;
				// 		IDUpdateDone = 1;
				// 	}
				// }
				// else
				// {
					Flg = Elmos524_17_ParallelDMMethod1(&TestDMFlg);
				// }
				#else
                Flg = Elmos524_17_DMSeq(&TestDMFlg);
				#endif
				
                if(Flg == 1)
                {
                    /** @brief 上电寻址失败后，重复寻址两次，运行过程探头丢失周期寻址*/
					if((0x0FFF != TestDMFlg) && (DMRetryCnt > 0))
					{
						DMRetryCnt--;
						pSnsCtrlGroup->GroupStatus = GroupStatus_SNS_REDMCHECK;
					}
					else
					{
						DMRetryCnt = 0;
						pSnsCtrlGroup->GroupStatus = GroupStatus_SNSINIT;
					}
                }
                else
                {

                }
            }
			else
			{
				/*上电时DSI对地或对电源短路，更新DSI故障状态*/
            	SnsDiag_SnsIdleDiagHandle();
			}
			
            break;
        }

        case GroupStatus_SNSINIT :
        {
            /** @brief  */
            Elmos524_17_CfgInitSeq();
            break;
        }

		case GroupStatus_READSNSGAIN:
		{
			Elmos524_17_ReadSnsGain();
			break;
		}
				
        case GroupStatus_SNSSelfTest :
        {
            Elmos524_17_SelfTestSeq();
			ReadSnsStatus = TRUE;
            break;
        }
        
        case GroupStatus_SNSStandby :
        {
            /*521.17 Standby*/
            Elmos524_17_StandbySeq();
            break;
        }
        case GroupStatus_SNSWakeUp :
        {
            /*521.17 Wakeup*/
            Elmos524_17_WakeUpSeq();
            break;
        }
        
        case GroupStatus_START_MEAS :
        {
            /** @brief 521.42为诊断模式时  */
            if((DSI_MASTER_DIAG == GetDSIComWorkStatus(DSIMaster0)) || (DSI_MASTER_DIAG == GetDSIComWorkStatus(DSIMaster1)))
            {
				pSnsCtrlGroup->Time1msCnt = 0;
                pSnsCtrlGroup->GroupStatus = GroupStatus_SNS_DIAG;
            }
			else if(SnsReWrParam == pSnsErrCtrl->SnsErrHandle) 
			{	
				pSnsParamCfgCtrl->SnsParamNeedUpdateFlg = pSnsErrCtrl->SnsErrMask;
				pSnsParamCfgCtrl->SnsParamCfgFlg = 0;
				SnsDiag_ClearSnsErrCtrl();
				pSnsCtrlGroup->GroupStatus = GroupStatus_SNSINIT;	
			}
            else
            {	
				if((TRUE == FxnOn) && (NORMAL_VOLTAGE == WorkVolSts) && (TRUE == pSnsCtrlGroup->StartMeasFlg))
				{
					Flg = 0;
					for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
					{
						//if(WaitSPIDataTransDone != G_DSI_MASTER[DSIMasterIdx].SPITransCtrl.SPIDataTransSts)
						{
							for(DSIChlIdx = DSIChl1; DSIChlIdx < DSIChlNum; DSIChlIdx++)
							{
				                G_DSI_MASTER[DSIMasterIdx].MasterCtrl.DSICHLCtrlStatus[DSIChlIdx].DCRBIntCnt = 0;
							}

							if(STANDARD_MEAS == GetSeqMeasType(pRSeqlist[MeasSeqIndex]))
							{
								MeasProfile = StandProfile;
							}
							else if(ADVANCED_MEAS == GetSeqMeasType(pRSeqlist[MeasSeqIndex]))
							{
								MeasProfile = AdvanceProfile;
							}
							
							if(0 == TriggerSnsGroupStartMeas(DSIMasterIdx,MeasProfile ,pRSeqlist[MeasSeqIndex],pFSeqlist[MeasSeqIndex]))
							{
								//G_DSI_MASTER[DSIMasterIdx].SPITransCtrl.SPIDataTransSts = WaitSPIDataTransDone;
								//G_DSI_MASTER[DSIMasterIdx].SPITransCtrl.u16TransTimeOutCnt = 0;
								Flg = 1;
							}
						}
					}
		
	                if(1 == Flg)/*有一个DSIMaster成功 跳转到下一个状态机*/
	                {
						pSnsCtrlGroup->GroupStatus = GroupStatus_BUSY_MEAS;
	                    MeasSeqIndex++;
						
						currentTime = AK2_GetSys1msTickFunc();
						#if 0
						DEBUG_PRINT("after temp compensation \n");
						DEBUG_PRINT(" %d start time %d \n",MeasSeqIndex,AK2_GetSys1msTickFunc());
						#endif
						lastTime = currentTime;
	                }
            	}
				else
				{
					pSnsCtrlGroup->GroupStatus = GroupStatus_IDLE;
					SnsPwrStsLast = WorkVolSts;
				}
            }
            
            break;
        }
		
		case GroupStatus_READY_MEAS:/*异步发送启动发波指令时需要跳转到此状态机*/
		{
			Flg = 0;
			for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
			{
				if(WaitSPIDataTransDone != G_DSI_MASTER[DSIMasterIdx].SPITransCtrl.SPIDataTransSts)
				{
					if(SPIDataTransSuccess == G_DSI_MASTER[DSIMasterIdx].SPITransCtrl.SPIDataTransSts)
					{
						for(DSIChlIdx = DSIChl1; DSIChlIdx < DSIChlNum; DSIChlIdx++)
						{
							pSnsCtrlGroup->SnsGroupMeasStatus[DSIMasterIdx][DSIChlIdx] = SnsGroupMeas_BUSY_MEAS;	
						}
					}					
					G_DSI_MASTER[DSIMasterIdx].SPITransCtrl.SPIDataTransSts = NoSPIDataTrans;
				}
				else
				{
					Flg = 1;
				}
			}

			if(0 == Flg)
			{
				pSnsCtrlGroup->GroupStatus = GroupStatus_BUSY_MEAS;
			}
			break;
		}

        case GroupStatus_BUSY_MEAS :
        {
			/** @brief 521.42为诊断模式时  */
			if((PWR_PIN_NORMAL != SnsPwrSts[FrontSnsGroupPwrDiagIdx]) 
				|| (PWR_PIN_NORMAL != SnsPwrSts[RearSnsGroupPwrDiagIdx]) 
				|| (PWR_PIN_NORMAL != SnsPwrSts[SideSnsGroupPwrDiagIdx]))
			{
				/*电源异常恢复后需要重新寻址*/
				StopSnsGroupMeas();
				pSnsCtrlGroup->GroupStatus = GroupStatus_SNS_WaitPwrRec;
			}
			else if((DSI_MASTER_DIAG == GetDSIComWorkStatus(DSIMaster0)) || (DSI_MASTER_DIAG == GetDSIComWorkStatus(DSIMaster1)))
			{
				StopSnsGroupMeas();
				pSnsCtrlGroup->Time1msCnt = 0;
				pSnsCtrlGroup->GroupStatus = GroupStatus_SNS_DIAG;
			}
			else if(0)//(SnsWriteStartFlg != 0)	/* 判断是否写入增益系数 */
			{
				pSnsCtrlGroup->GroupStatus = GroupStatus_WriteSnsGain;
				GstrSnsParamCfgCtrl.SnsParamCfgSeq = WriteSnsParam;
			}
			else
			{
				/** @brief 接收并解析回波 */
				Flg = 0;
	            for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
	            {
					for(DSIChlIdx = DSIChl1; DSIChlIdx < DSIChlNum; DSIChlIdx++)
					{
		                /** @brief 测量状态 */
		                if( pSnsCtrlGroup->SnsGroupMeasStatus[DSIMasterIdx][DSIChlIdx] == SnsGroupMeas_BUSY_MEAS)
		                {
		                    if((GetDSICHLWorkStatus(DSIMasterIdx,DSIChlIdx) == DSICHL_IDLE) 
		                        && (pSnsCtrlGroup->SnsGroupMeasTime[DSIMasterIdx][DSIChlIdx] >= pSnsCtrlGroup->MeasTimeOut[DSIMasterIdx][DSIChlIdx]))
		                    {
		                        /** @brief 测量完成 */
		                        pSnsCtrlGroup->SnsGroupMeasStatus[DSIMasterIdx][DSIChlIdx] = SnsGroupMeas_IDLE;
		                        SetMeasECHOMsgUpdateTimeTime(DSIMasterIdx,DSIChlIdx);
		                    }
							else
							{
								Flg = 1;
							}
		                }
					}
	            }

				if(0 == Flg)
				{
					SnsEchoDataHandle(&SnsMeasDistData[0],(MeasChlECHOData_Str (*)[DSIChlNum])&MeasECHOMsg[0][0]);

					if((0 == Gu8DVWorkFlg) 
						|| ((1 == Gu8DVWorkFlg) && (STANDARD_MEAS == MeasECHOMsg[0][0].MeasType)))
					{
						pSnsCtrlGroup->MeasSeqFinish = TRUE;
						
						#if 0
						DEBUG_PRINT(" %d end time %d \n",MeasSeqIndex,AK2_GetSys1msTickFunc());
						#endif
					}

					SnsDataUpdate = TRUE;
					if(MeasSeqList4 != MeasSeqSel)
					{
						MeasDelayTime = Elmos524_17_GetRandom();
					}
					else
					{
						MeasDelayTime = 0; /*高速时不延时*/
					}
					MeasDelayStartTime = GetSystemTimeCnt_Ms();
						
					pSnsCtrlGroup->GroupStatus = GroupStatus_END_MEAS;
				}
			}
            break;
        }

        case GroupStatus_END_MEAS :
        {			
			pSnsCtrlGroup->Time1msCnt = 0;

			if((TRUE == FxnOn) && (NORMAL_VOLTAGE == WorkVolSts) && (TRUE == pSnsCtrlGroup->StartMeasFlg))
			{
				if((SnsReDM == pSnsErrCtrl->SnsErrHandle) 
				&& (GstrSnsReDMCheckCtrl.ReDMCheckTime1msCnt > SNS_REDMCHECK_PERIOD)
				&& (TRUE == GstrSnsReDMCheckCtrl.StartTime1msCnt))
				{
					GstrSnsReDMCheckCtrl.ReDMCheckTime1msCnt = 0;
					GstrSnsReDMCheckCtrl.StartTime1msCnt = FALSE;
					pSnsCtrlGroup->GroupStatus = GroupStatus_SNS_REDMCHECK;
				}
				else if(SnsReadStatus == pSnsErrCtrl->SnsErrHandle) 
				{
					/*硬件故障或通讯异常读取故障状态*/
					for(DSIMasterIdx = DSIMaster0;DSIMasterIdx < DSIMasterNum;DSIMasterIdx++)
					{
						InitWRSnsMeasParam_str(&WRSnsMeasParam);
						WRSnsMeasParam.eWRFlg = eRALLSnsSelfTestResult;
						WRSnsMeasParam.SnsID = MasterSnsSelect[DSIMasterIdx];				
						RWReturnFlg[DSIMasterIdx] = 0;
						WRSnsMeasParam.pResultflg = &RWReturnFlg[DSIMasterIdx];
						TriggerWRSnsMsg(DSIMasterIdx,pSnsCtrlGroup,&WRSnsMeasParam);
					}
					SetNextSnsCtrlGroupStatus(GroupStatus_START_MEAS);
					SnsDiag_ClearSnsErrCtrl();
					ReadSnsStatus = TRUE;
				}
				else if((SnsSelfTest == pSnsErrCtrl->SnsErrHandle)
						&& (GstrSnsReDMCheckCtrl.ReDMCheckTime1msCnt > SNS_REDMCHECK_PERIOD)
						&& (TRUE == GstrSnsReDMCheckCtrl.StartTime1msCnt))
				{
					GstrSnsReDMCheckCtrl.ReDMCheckTime1msCnt = 0;
					GstrSnsReDMCheckCtrl.StartTime1msCnt = FALSE;
					pSnsCtrlGroup->GroupStatus = GroupStatus_SNSSelfTest;
					SnsDiag_ClearSnsErrCtrl();
				}
				else if(pSnsCtrlGroup->TempCompTime1msCnt > SNS_TEMPCOMP_PERIOD)
				{
					pSnsCtrlGroup->GroupStatus = GroupStatus_SNS_TEMPCOMP;
					pSnsCtrlGroup->TempCompTime1msCnt = 0;
				}
				else
				{	
					if((TRUE == SnsDataUpdate) && (CalcTimeInterval(MeasDelayStartTime,GetSystemTimeCnt_Ms()) >= MeasDelayTime))
					{
						#if 0
						DEBUG_PRINT(" delay time %d \n",MeasDelayTime);
						#endif
						SnsDataUpdate = FALSE;
						MeasDelayStartTime = 0;
						
						Flg = 0;
						for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
						{
							for(DSIChlIdx = DSIChl1; DSIChlIdx < DSIChlNum; DSIChlIdx++)
							{
				                G_DSI_MASTER[DSIMasterIdx].MasterCtrl.DSICHLCtrlStatus[DSIChlIdx].DCRBIntCnt = 0;
							}

							if(STANDARD_MEAS == GetSeqMeasType(pRSeqlist[MeasSeqIndex]))
							{
								MeasProfile = StandProfile;
							}
							else if(ADVANCED_MEAS == GetSeqMeasType(pRSeqlist[MeasSeqIndex]))
							{
								MeasProfile = AdvanceProfile;
							}

							
							if(0 == TriggerSnsGroupStartMeas(DSIMasterIdx,MeasProfile ,pRSeqlist[MeasSeqIndex],pFSeqlist[MeasSeqIndex]))
							{
								Flg = 1;
							}
					
						}
						
		                if(1 == Flg)/*有一个DSIMaster成功 跳转到下一个状态机*/
		                {
		                    MeasSeqIndex++;

							currentTime = AK2_GetSys1msTickFunc();
							#if 0
							DEBUG_PRINT(" %d start time %d \n",MeasSeqIndex,currentTime);
							#endif
							lastTime = currentTime;
		                }
						pSnsCtrlGroup->GroupStatus = GroupStatus_BUSY_MEAS;
					}
				}	
			}
			else
			{
				pSnsCtrlGroup->GroupStatus = GroupStatus_IDLE;
				SnsPwrStsLast = WorkVolSts;
			}

			if((SnsReDM == pSnsErrCtrl->SnsErrHandle) && (FALSE == GstrSnsReDMCheckCtrl.StartTime1msCnt))
			{
				GstrSnsReDMCheckCtrl.StartTime1msCnt = TRUE;
				GstrSnsReDMCheckCtrl.ReDMCheckTime1msCnt = 0;
			}
			else if((SnsSelfTest == pSnsErrCtrl->SnsErrHandle) && (FALSE == GstrSnsReDMCheckCtrl.StartTime1msCnt))
			{
				/*共用同一个结构体进行延时*/
				GstrSnsReDMCheckCtrl.StartTime1msCnt = TRUE;
				GstrSnsReDMCheckCtrl.ReDMCheckTime1msCnt = 0;
			}
			else
			{}
			

            break;
        }

		case GroupStatus_SNS_TEMPCOMP:
		{
			Elmos524_17_SnsGainComp();
			pSnsCtrlGroup->GroupStatus = GroupStatus_START_MEAS;
			break;
		}

		case GroupStatus_SNS_REDMCHECK:
		{
			Elmos524_17_SnsReDMCheck(&GstrSnsReDMCheckCtrl);
			break;
		}

		case GroupStatus_SNS_WaitPwrRec:
		{
			if((PWR_PIN_NORMAL == SnsPwrSts[FrontSnsGroupPwrDiagIdx]) 
				&& (PWR_PIN_NORMAL == SnsPwrSts[RearSnsGroupPwrDiagIdx]) 
				&& (PWR_PIN_NORMAL == SnsPwrSts[SideSnsGroupPwrDiagIdx]))
			{
				AK2_WriteEnSnsPin(0);
				if(pSnsCtrlGroup->Time1msCnt > WAIT_SNS_POWER_OFF)
				{
					TestDMFlg = 0;
					pSnsCtrlGroup->Time1msCnt = 0;
					pSnsCtrlGroup->GroupStatus = GroupStatus_SNS_DM;
				}
			}
			else
			{
				SnsDiag_SnsIdleDiagHandle();
				pSnsCtrlGroup->Time1msCnt = 0;
			}
			
			break;
		}
				
        case GroupStatus_BUSY_RWSNSStatus :
        {
			for(DSIMasterIdx = DSIMaster0;DSIMasterIdx < DSIMasterNum;DSIMasterIdx++)
			{
            	RWSNSEndFlg[DSIMasterIdx] = WRSnsMeasParamTask(DSIMasterIdx);
			}
			
            if((0 == RWSNSEndFlg[DSIMaster0]) && (0 == RWSNSEndFlg[DSIMaster1]))
            {
				if(TRUE == ReadSnsStatus)
				{	
					ReadSnsStatus = FALSE;
					SnsDiag_SnsSelfTestResultHandle();	
				}
							
				RWSNSEndFlg[DSIMaster0] = 1;
				RWSNSEndFlg[DSIMaster1] = 1;
                pSnsCtrlGroup->GroupStatus = GetNextSnsCtrlGroupStatus();
            }

            break;
        }
        

        case GroupStatus_SNS_DIAG :
        {            
            if((GetDSIComWorkStatus(DSIMaster0) == DSI_MASTER_READY)
				&&(GetDSIComWorkStatus(DSIMaster1) == DSI_MASTER_READY))
            {
				/*延时60ms再重新启动，解决探头还在探测过程中，又重新启动新一次发波*/
				if(pSnsCtrlGroup->Time1msCnt > 60)
				{
					TransStopDSI(DSIMaster0,DSIChlSEL1And2);
					TransStopDSI(DSIMaster1,DSIChlSEL1And2);
	            	pSnsCtrlGroup->GroupStatus = GroupStatus_START_MEAS;
				}
            }
            else
            {
				/*42异常时，检测是否是DSI对地或对电源短路，更新DSI故障状态*/
            	SnsDiag_SnsIdleDiagHandle();
            }
            break;
        }
		
		case GroupStatus_WriteSnsGain:
		{
			Elmos524_17_WtiteGain();
			break;
		}

		case GroupStatus_IDLE:
		{		
			if((TRUE == FxnOn) && (NORMAL_VOLTAGE == WorkVolSts) && (TRUE == pSnsCtrlGroup->StartMeasFlg))
			{
				if(SnsPwrStsLast != NORMAL_VOLTAGE)
				{
					pSnsCtrlGroup->GroupStatus = GroupStatus_SNS_DM;
					SnsPwrStsLast = NORMAL_VOLTAGE;
				}
				else
				{
					pSnsCtrlGroup->GroupStatus = GroupStatus_SNSSelfTest;
				}
			}
			break;
		}

        default :
        {

            break;
        }

    }
    
    
}

SnsGroupMeasStatus_en GetSnsGroupMeasStstus(DSIMasterID_en LeDSIMasterID,DSIChlID_en LeDSIChlID)
{
    SnsCtrlGroup_Cfg *pSnsCtrlGroup;
    pSnsCtrlGroup = &SnsCtrlGroup;
    if((pSnsCtrlGroup->GroupStatus < GroupStatus_START_MEAS) || (pSnsCtrlGroup->GroupStatus > GroupStatus_END_MEAS))
    {
        return SnsGroupMeas_NotReady;
    }
    if(pSnsCtrlGroup->SnsGroupMeasStatus[LeDSIMasterID][LeDSIChlID] == SnsGroupMeas_NULL)
    {
        pSnsCtrlGroup->SnsGroupMeasStatus[LeDSIMasterID][LeDSIChlID] = SnsGroupMeas_IDLE;
    }
    else
    {

    }
    return pSnsCtrlGroup->SnsGroupMeasStatus[LeDSIMasterID][LeDSIChlID];
}

/******************************************************************************
* 函数名称: TriggerSnsGroupStartMeas
* 
* 功能描述: 触发前后雷达启动发波
* 
* 输入参数: MEAS_Profile  探测profile
* 输入参数: R_SeqID       后雷达发波序列ID
* 输入参数: F_SeqID   前雷达发波序列ID
* 
* 输出参数: 无
* 
* 返 回 值: 0  OK  else 条件不满足或SPI 传输错误
* 
* 修改日期      版本号       修改人       修改内容
* 
*******************************************************************************/
uint8 TriggerSnsGroupStartMeas(DSIMasterID_en LeDSIMasterID,SnsMeasProfile_en MEAS_Profile ,CMDSeq_en R_SeqID,CMDSeq_en F_SeqID)
{
    SnsCtrlGroup_Cfg *pSnsCtrlGroup;
    DSIMasterID_en DSIMasterID = LeDSIMasterID;
    DSIChlSel_en DSIChlSEL;
    CMDSeq_en cMeasSeq[SnsMeasGroup_Num];
    SNS_MEAS_TYPE_en   SNS_MEAS_TYPE = SNS_MEAS_TYPE_NONE;
    SnsMeasDataMode_en DataMode;
    
    uint16 AUTOBRCNum = 0;
    uint16 MeasTime_us[DSIChlNum] = {0,0};
    Elmos17MeasCMDMsg_st Elmos17MeasCMDMsg[DSIChlNum];
    Elmos17MeasCMDMsg_st *pElmos17MeasCMDMsg[DSIChlNum];
    DSIChlID_en DSIChlIdx;
    uint8 LcSnsNumCnt;
    SnsID_en SnsID;

    Elmos17RetType_en RetType;
    cMeasSeq[RearSnsMeasGroup] = R_SeqID;
    cMeasSeq[FrontSnsMeasGroup] = F_SeqID;
    
    DataMode = APP_Mode;

    pSnsCtrlGroup = &SnsCtrlGroup;
	
    if((pSnsCtrlGroup->GroupStatus < GroupStatus_START_MEAS) || (pSnsCtrlGroup->GroupStatus > GroupStatus_END_MEAS)
		||(cMeasSeq[FrontSnsMeasGroup] >= CMDSeq_NONE) || (cMeasSeq[FrontSnsMeasGroup] >= CMDSeq_NONE))
    {
        return eRet_Other_ERR;
    }
		
	for(DSIChlIdx = DSIChl1; DSIChlIdx < DSIChlNum; DSIChlIdx++)
    {        
        if((GetDSICHLWorkStatus(DSIMasterID,DSIChlIdx) == DSICHL_IDLE)
            && (pSnsCtrlGroup->SnsGroupMeasStatus[DSIMasterID][DSIChlIdx] == SnsGroupMeas_IDLE))
        {     
			/** @brief 清回波缓存 */
    		Clear_ECHOMsgMEAS(DSIMasterID,DSIChlIdx);
				
            for(LcSnsNumCnt=0; LcSnsNumCnt < DSISlaveSnsNum; LcSnsNumCnt++)
            {
				SnsID = GetSnsID( DSIMasterID, DSIChlIdx,LcSnsNumCnt + 1);

				if(SNSID_NONE != SnsID)
				{
					if(SnsID < SNS_F_ID_OFFSET)/*前探头*/
					{
		                /** @brief 获取发波序列中对应探头工作模式 */
		                SNS_MEAS_TYPE = GetMeasSeqSNsMeasType(cMeasSeq[FrontSnsMeasGroup],SnsID);
					}
					else if(SnsID < SNSNum)/*后探头*/
					{
						/** @brief 获取发波序列中对应探头工作模式 */
		                SNS_MEAS_TYPE = GetMeasSeqSNsMeasType(cMeasSeq[RearSnsMeasGroup],SnsID - SNS_F_ID_OFFSET);
					}

					/** @brief 设置发波指令中对应探头工作模式 */
	                Elmos17MeasCMDMsg[DSIChlIdx].SNsMeasType[LcSnsNumCnt] = SNS_MEAS_TYPE;
	                
	                /** @brief 设置回波数据中的探头工作模式 */
	                SetSns_ECHOMsgSnsMEAS_TYPE(DSIMasterID,DSIChlIdx,LcSnsNumCnt,SNS_MEAS_TYPE);
                
                    Clear_SnsPDCMMsg_Status(SnsID);
                }
            }
            /** @brief 设置发波指令中探测类型 */
            Elmos17MeasCMDMsg[DSIChlIdx].MeasType = GetSeqMeasType(cMeasSeq[FrontSnsMeasGroup]);
            
            /** @brief 设置发波指令中回波数据模式 */
            Elmos17MeasCMDMsg[DSIChlIdx].MeasDataMode = DataMode;
            
            if((MEAS_Profile < Profile_A) || (MEAS_Profile > Profile_C) 
                || ((Elmos17MeasCMDMsg[DSIChlIdx].MeasType == ADVANCED_MEAS) && (MEAS_Profile == Profile_C)))
            {
                /** @brief MEAS_Profile范围错误,则默认Profile_A */
                Elmos17MeasCMDMsg[DSIChlIdx].profile = Profile_A;
            }
            else
            {
                Elmos17MeasCMDMsg[DSIChlIdx].profile = MEAS_Profile;
            }
            

            /** @brief 获取探测时间 */
            MeasTime_us[DSIChlIdx] = GetSeqMeasTime_us(Elmos17MeasCMDMsg[DSIChlIdx].MeasType, Elmos17MeasCMDMsg[DSIChlIdx].profile);

            /** @brief 设置探测退出时间 */
            pSnsCtrlGroup->MeasTimeOut[DSIMasterID][DSIChlIdx] = (MeasTime_us[DSIChlIdx] / 1000) + 3; /** @brief 测量完成时间 = 探测时间 + 3 ms(~探头测量初始化+诊断+发波同步时间+最后收波延时)*/

            /** @brief 测量时间清0 */
            pSnsCtrlGroup->SnsGroupMeasTime[DSIMasterID][DSIChlIdx] = 0u;

            /** @brief 计算探测时间内对应接收PDCM个数 */
            AUTOBRCNum = CalcSeqMeasPDCMNum(MeasTime_us[DSIChlIdx],TDMA_PDCM_PERIOD,Elmos17MeasCMDMsg[DSIChlIdx].MeasDataMode);

            AUTOBRCNum += 2;/** @brief 探测完成后继续收2个PDCM帧，用于接收探测结束时回波，可以通过判断BufFlg确认是否要继续收PDCM数据 */
            
            DSIChlSEL = (DSIChlSel_en)(DSIChlIdx + 1);
            
            /** @brief 设置AUTOBRC Num */
            SetAutoBRCPart(DSIMasterID,DSIChlSEL,AUTOBRCNum);

            /** @brief 设置测量信息 */
            SetECHOMsgMEAS_Mode(DSIMasterID,DSIChlIdx, Elmos17MeasCMDMsg[DSIChlIdx].MeasType, Elmos17MeasCMDMsg[DSIChlIdx].profile, cMeasSeq[DSIChlIdx]);

            pElmos17MeasCMDMsg[DSIChlIdx] = &Elmos17MeasCMDMsg[DSIChlIdx];

        }
        else
        {
            pElmos17MeasCMDMsg[DSIChlIdx] = NULL;
        }
    }

	ClearSPIRxCrcErrCnt(DSIMasterID);
	#if 0
    RetType = TransElmos17MeasCmdSPIAsyn(DSIMasterID,pElmos17MeasCMDMsg[DSIChl1],pElmos17MeasCMDMsg[DSIChl2]);
    
    pSnsCtrlGroup->Time1msCnt = 0;

    /** @brief */
    if(RetType != eRet_OK)
    {	
		return 1;
    }
	#else
	RetType = TransElmos17MeasCmd(DSIMasterID,pElmos17MeasCMDMsg[DSIChl1],pElmos17MeasCMDMsg[DSIChl2]);
    
    pSnsCtrlGroup->Time1msCnt = 0;

    if(RetType == eRet_OK)
    {
		for(DSIChlIdx = DSIChl1; DSIChlIdx < DSIChlNum; DSIChlIdx++)
	    {
	        if(pElmos17MeasCMDMsg[DSIChlIdx] != NULL)
	        {
	            pSnsCtrlGroup->SnsGroupMeasStatus[DSIMasterID][DSIChlIdx] = SnsGroupMeas_BUSY_MEAS;
	        }
	    }
    }
	else
	{
		return 1;  /** @brief ERROR */
	}
	#endif
	
    return 0;
}

void Elmos17SnsCtrl_UpdateTimeCnt()
{
	DSIMasterID_en DSIMasterIdx = DSIMaster0;
	DSIChlID_en DSIChlIdx = DSIChl1; 
	
	SnsCtrlGroup.Time1msCnt++;
	SnsCtrlGroup.TempCompTime1msCnt++;
	GstrSnsReDMCheckCtrl.ReDMCheckTime1msCnt++;

	for(DSIMasterIdx = DSIMaster0; DSIMasterIdx < DSIMasterNum; DSIMasterIdx++)
	{
		for(DSIChlIdx = DSIChl1; DSIChlIdx < DSIChlNum; DSIChlIdx++)
		{
		    if(SnsCtrlGroup.SnsGroupMeasStatus[DSIMasterIdx][DSIChlIdx] == SnsGroupMeas_BUSY_MEAS)
		    {
		        SnsCtrlGroup.SnsGroupMeasTime[DSIMasterIdx][DSIChlIdx] ++;
		    }
		}
	}
}

void Elmos17SnsCtrl_1msTASK()
{
    DSI_SPI_DataHandle();
    DSIComWorkMainFunc();
	UpdateTemperatureDistance();	
}

void Elmos17SnsCtrl_NoPeriod_TASK()
{   
    Elmos524_17_SnsCtrlMainFunc();
}

const SnsMeasDistData_Str* Elmos17SnsCtrl_GetSnsMeasDistData(void)
{
	return (const SnsMeasDistData_Str*)&SnsMeasDistData[0];
}

/******************************************************************************
 * 函数名称: Elmos17SnsCtrl_GetSeqFinishFlg
 * 
 * 功能描述: 获取发波序列完成标志位
 * 
 * 输入参数:无
 * 
 * 输出参数: TRUE:发波完成 FALSE:发波未完成
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 ******************************************************************************/
uint8 Elmos17SnsCtrl_GetSeqFinishFlg(void)
{
	return SnsCtrlGroup.MeasSeqFinish;
}

/******************************************************************************
 * 函数名称: Elmos17SnsCtrl_ClearSeqFinishFlg
 * 
 * 功能描述: 应用层获取完障碍物信息后，需清除发波序列完成标志位
 * 
 * 输入参数:无
 * 
 * 输出参数: 无
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 ******************************************************************************/
void Elmos17SnsCtrl_ClearSeqFinishFlg(void)
{
	SnsCtrlGroup.MeasSeqFinish = FALSE;
}

/******************************************************************************
 * 函数名称: Elmos17SnsCtrl_SetStartMeasFlg
 * 
 * 功能描述: 应用层根据车辆状态开启或关闭发波
 * 
 * 输入参数:Flag TRUE:开启发波 FALSE:关闭发波
 * 
 * 输出参数: 无
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 ******************************************************************************/
void Elmos17SnsCtrl_SetStartMeasFlg(uint8 Flag)
{
	SnsCtrlGroup.StartMeasFlg = Flag;
}

/******************************************************************************
 * 函数名称: Elmos17SnsCtrl_GetDMFlg()
 * 
 * 功能描述: 获取探头寻址标志
 * 
 * 输入参数: 无
 * 
 * 输出参数: 探头寻址标志
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 ******************************************************************************/
uint16 Elmos17SnsCtrl_GetDMFlg(void)
{
	return TestDMFlg;
}

/******************************************************************************
 * 函数名称: Elmos17SnsCtrl_GetSnsParamCfgFlg()
 * 
 * 功能描述: 获取探头参数配置标志
 * 
 * 输入参数: 无
 * 
 * 输出参数: 探头参数配置标志
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 ******************************************************************************/
uint16 Elmos17SnsCtrl_GetSnsParamCfgFlg(void)
{
	return GstrSnsParamCfgCtrl.SnsParamCfgFlg;
}

/******************************************************************************
 * 函数名称: Elmos17SnsCtrl_SetDVWorkFlg()
 * 
 * 功能描述: 设置雷达系统的工作模式
 * 
 * 输入参数: 0:正常工作模式 1:DV工作模式,定、扫都发，只输出定频距离
 * 
 * 输出参数: 无
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 ******************************************************************************/
void Elmos17SnsCtrl_SetDVWorkFlg(uint8 u8WorkMode)
{
	Gu8DVWorkFlg = u8WorkMode;
}

/******************************************************************************
 * 函数名称: Elmos17SnsCtrl_SetDVWorkFlg()
 * 
 * 功能描述: 获取雷达系统的工作模式
 * 
 * 输入参数: 无
 * 
 * 输出参数: 系统工作模式 0:正常工作模式 1:DV工作模式,定、扫都发，只输出定频距离
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 ******************************************************************************/
uint8 Elmos17SnsCtrl_GetDVWorkFlg(void)
{
	return Gu8DVWorkFlg;
}

/******************************************************************************
 * 函数名称: Elmos524_17_WtiteGain()
 * 
 * 功能描述: 修改雷达探头的增益系数
 * 
 * 输入参数: 无
 * 
 * 输出参数: 系统工作模式 0:未写入、写入失败或正在写入 1:写入成功
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 ******************************************************************************/
uint16 databuf = 0;
static uint8 Elmos524_17_WtiteGain(void)
{    
    SnsCtrlGroup_Cfg *pSnsCtrlGroup = &SnsCtrlGroup;
   	DSIMasterID_en DSIMasterID = DSIMaster0;
    DSIChlID_en DSIChlID = DSIChl1;
    uint8 SlotID = 0;
    static SnsID_en SnsID = SNS_FL_S;
    uint16 Idx = 0;
    bool SuccessFlg = FALSE;
    uint16 BufIdx = 0xFFFF;
    CRM_RESPONSE_Data_str CRMRESOutData;
    WRSnsDataType_e CfgType = eWRSnsDataType_GeneralSet;
	WRSnsMeasParam_str WRSnsMeasParam;
	SnsParamCfgCtrl *pSnsParamCfgCtrl = &GstrSnsParamCfgCtrl;
	static uint8 DataBuf[8] = {0};
	
    memset((void*)&CRMRESOutData,0,sizeof(CRM_RESPONSE_Data_str));
	memset((void*)&WRSnsMeasParam,0,sizeof(WRSnsMeasParam_str));

	DSIMasterID = SnsAddCfg[SnsID].DSIMasterID;
	DSIChlID = SnsAddCfg[SnsID].DSIChlSEL - 1U;
	SlotID = SnsAddCfg[SnsID].cSnsPhysAddr - 1U;

	static uint8 JudgeElmosShift = 0;

    switch(pSnsParamCfgCtrl->SnsParamCfgSeq)
    { 
	   case WriteSnsParam:
	   {
	   		/*DSISlaveID 0 ~ FrontSnsGroupPwrAdcIdx(1)
	   		  DSISlaveID 1 ~ RearSnsGroupPwrAdcIdx(2) 
			  DSISlaveID 2 ~ SideSnsGroupPwrAdcIdx(3)*/
	   	   if(ReadSnsPwrADVal(SlotID + 1) > AD_CALIBRATION_9_5V)
	   	   {
				/* 判断写入的探头 */
				if((SnsWriteStartFlg&(0x0001<<JudgeElmosShift)) == (0x0001<<JudgeElmosShift))
				{
					/* 写入当前探头 */
					SnsID = JudgeElmosShift;

					databuf = ((uint16)GuGainDataBuf[JudgeElmosShift*2+1]<<9) | ((uint16)Elmos17_LowPass_Normal<<8)
									| ((uint16)Elmos17_GANA_51_8dB<<5) | ((uint16)Elmos17_ITdr_310mA);

					DSIMasterID = SnsAddCfg[SnsID].DSIMasterID;
					DSIChlID = SnsAddCfg[SnsID].DSIChlSEL - 1U;
					SlotID = SnsAddCfg[SnsID].cSnsPhysAddr - 1U;

					JudgeElmosShift++;
					if(JudgeElmosShift > 12)
					{
						/* 清写增益标志位 */
						SnsWriteStartFlg = 0;
						JudgeElmosShift = 0;
						/* 退出写探头增益 */
						pSnsCtrlGroup->GroupStatus = GroupStatus_START_MEAS;
						pSnsCtrlGroup->NextGroupStatus = GroupStatus_START_MEAS;
						pSnsParamCfgCtrl->SnsParamCfgSeq = SnsParamCfgInit;
						pSnsParamCfgCtrl->LastSeq = SnsParamCfgInit;

						InitSnsCtrlGroup();
						return 0;
					}
				}
				else
				{
					JudgeElmosShift++;
					if(JudgeElmosShift > 12)
					{
						/* 清写增益标志位 */
						SnsWriteStartFlg = 0;
						JudgeElmosShift = 0;
						/* 退出写探头增益 */
						pSnsCtrlGroup->GroupStatus = GroupStatus_START_MEAS;
						pSnsCtrlGroup->NextGroupStatus = GroupStatus_START_MEAS;
						pSnsParamCfgCtrl->SnsParamCfgSeq = SnsParamCfgInit;
						pSnsParamCfgCtrl->LastSeq = SnsParamCfgInit;

						InitSnsCtrlGroup();
						return 0;
					}
				}

		   	   WRSnsMeasParam.SnsID = SnsID;
			   WRSnsMeasParam.eWRFlg = eWSnsMeasParam;
			   WRSnsMeasParam.ElmosMeasParam_StartAddr = 2;
			   WRSnsMeasParam.u16Len = 1;	
			   RWReturnFlg[DSIMasterID] = 0;	
			   WRSnsMeasParam.pInOutDataBuf = &databuf;  //&WSnsEEPROMBuf.WData[0]
			   WRSnsMeasParam.pResultflg = &RWReturnFlg[DSIMasterID];
			   TriggerWRSnsMsg(DSIMasterID,pSnsCtrlGroup,&WRSnsMeasParam);
			   
			   SetNextSnsCtrlGroupStatus(GroupStatus_WriteSnsGain);
			   
			   pSnsParamCfgCtrl->SnsParamCfgSeq = ReadSnsParam;
	   	   }
		   else
		   {
			   pSnsParamCfgCtrl->SnsParamCfgSeq = SnsParamCfgEnd; //PrepareNextSnsParamCfg
		   }

		   
		   TestErrCntSeq[5]++;
	   }
	   	break;

	   case ReadSnsParam:
	   {
	   	   if(WR_SNS_MEAS_PARAM_SUCCESS == RWReturnFlg[DSIMasterID])
	   	   {
		   	   WRSnsMeasParam.SnsID = SnsID;
			   WRSnsMeasParam.eWRFlg = eRSnsMeasParam;
			   WRSnsMeasParam.ElmosMeasParam_StartAddr = 0;
			   WRSnsMeasParam.u16Len = Elmos17MEAS_Param_Check_u16Len;
			   memset((void*)&RSnsEEPROMBuf.WData[0],0,WRSnsMeasParam.u16Len*2);			   
			   RWReturnFlg[DSIMasterID] = 0;
			   WRSnsMeasParam.pInOutDataBuf = &RSnsEEPROMBuf.WData[0];   
			   WRSnsMeasParam.pResultflg = &RWReturnFlg[DSIMasterID];
			   TriggerWRSnsMsg(DSIMasterID,pSnsCtrlGroup,&WRSnsMeasParam);
			   SetNextSnsCtrlGroupStatus(GroupStatus_WriteSnsGain);
			   SuccessFlg = TRUE;
	   	   }

	   	   if(TRUE == SuccessFlg)
		   {
			   pSnsParamCfgCtrl->SnsParamCfgSeq = EnEEPROMPrg;	
		   }
		   else
		   {
			   pSnsParamCfgCtrl->SnsParamCfgSeq = SnsParamCfgEnd;
		   }
		   
		   TestErrCntSeq[6]++;
	   }
	   break;

	   case EnEEPROMPrg:
	   {
	   		if(ReadSnsPwrADVal(SlotID + 1) > AD_CALIBRATION_9_5V)
	   		{
				if(GetDSICHLWorkStatus(DSIMasterID,DSIChlID) == DSICHL_IDLE)
	         	{
	                if(eRet_OK == TransElmos17EEPROMAccessReq(DSIMasterID,DSIChlID + 1,(SlotID + 1),Elmos17EnEEPProg))
	                {
						SuccessFlg = TRUE;
	                }       
	                else
	                {
	                    /** @brief 传输错误 */				
	                    PRINTF_ELMOS17("sns%d Trans EnEEPROMPrg Err \r\n",SnsID); 
	                }
					
					if(TRUE == SuccessFlg)
					{
						pSnsParamCfgCtrl->SnsParamCfgSeq = HandleEnEEPROMPrgRes;
					}
					else
					{
						pSnsParamCfgCtrl->SnsParamCfgSeq = SnsParamCfgEnd; //PrepareNextSnsParamCfg
					}

					TestErrCntSeq[8]++;
				}
			}
			else
	   		{
				pSnsParamCfgCtrl->SnsParamCfgSeq = SnsParamCfgEnd; //PrepareNextSnsParamCfg
			}
	   }
	   	break;
	   
	   case HandleEnEEPROMPrgRes:
	   {	
		   if(GetDSICHLWorkStatus(DSIMasterID,DSIChlID) == DSICHL_IDLE)
		   {   
		   	   BufIdx = GetCRMRESData(DSIMasterID,&CRMRESOutData);
			   if(BufIdx != 0xFFFF)
			   {
			   	   if(CheckCRMResStatus(&CRMRESOutData,(SlotID + 1)) == eRet_OK)
			   	   {
						SuccessFlg = TRUE;
				   }
			   }

			   if(TRUE == SuccessFlg)
			   {
			   	//    if(CheckSnsParam == pSnsParamCfgCtrl->LastSeq)
			   	//    {
				   		pSnsParamCfgCtrl->SnsParamCfgSeq = WriteSnsParamToEEPROM;
			   	//    }
				//    else
				//    {
				// 		pSnsParamCfgCtrl->SnsParamCfgSeq = SnsParamCfgEnd; //CopyParamEEPROMToRam
				//    }
			   }
			   else
			   {
				   pSnsParamCfgCtrl->SnsParamCfgSeq = SnsParamCfgEnd; //PrepareNextSnsParamCfg
			   }

			   TestErrCntSeq[9]++;
		   }
	   }
	   	break;

	   case WriteSnsParamToEEPROM:
	   {
	   		/*DSISlaveID 0 ~ FrontSnsGroupPwrAdcIdx(1)
	   		  DSISlaveID 1 ~ RearSnsGroupPwrAdcIdx(2) 
			  DSISlaveID 2 ~ SideSnsGroupPwrAdcIdx(3)*/
	   	   if(ReadSnsPwrADVal(SlotID + 1) > AD_CALIBRATION_9_5V)
	   	   {
				if(GetDSICHLWorkStatus(DSIMasterID,DSIChlID) == DSICHL_IDLE)
				{
		            if(eRet_OK == TransElmos17EEPROMAccessReq(DSIMasterID,DSIChlID + 1,(SlotID + 1),Elmos17RAMToEEPROM))
		            {
		                SuccessFlg = TRUE;
		            }       
		            else
		            {
		                /** @brief 传输错误 */
		                PRINTF_ELMOS17("sns%d Trans Write Sns Param To EEPROM Err \r\n",SnsID);
		            }

				    if(TRUE == SuccessFlg)
				    {
						pSnsParamCfgCtrl->SnsParamCfgSeq = HandleWriteSnsParamToEEPROMRes;
				    }
				    else
				    {
						pSnsParamCfgCtrl->SnsParamCfgSeq = SnsParamCfgEnd; //PrepareNextSnsParamCfg
				    }
				   
				   TestErrCntSeq[10]++;
	            }
	   	   }
		   else
		   {
		   		/*不执行剩余流程*/
		   		pSnsParamCfgCtrl->SnsParamCfgSeq = SnsParamCfgEnd; //PrepareNextSnsParamCfg
		   }
	   }
	   	break;
	   
	   case HandleWriteSnsParamToEEPROMRes:
	   { 		   
		   if(GetDSICHLWorkStatus(DSIMasterID,DSIChlID) == DSICHL_IDLE)
		   {
		   	   BufIdx = GetCRMRESData(DSIMasterID,&CRMRESOutData);
			   if(BufIdx != 0xFFFF)
			   {
			   	   if(CheckCRMResStatus(&CRMRESOutData,(SlotID + 1)) == eRet_OK)
			   	   {
						SuccessFlg = TRUE;
				   }
			   }
			   else
			   {
				   /** @brief 未接收到应答数据 */  
			   }

			   if(TRUE == SuccessFlg)
			   {
				   pSnsCtrlGroup->Time1msCnt = 0;
			   	   pSnsParamCfgCtrl->SnsParamCfgSeq = WriteSnsParam;
			   }
			   else
			   {
				   pSnsParamCfgCtrl->SnsParamCfgSeq = SnsParamCfgEnd; //PrepareNextSnsParamCfg
			   }

			   TestErrCntSeq[11]++;
		   }
	   }
	   	break;
		   
	   case SnsParamCfgEnd:
	   {
			pSnsParamCfgCtrl->SnsParamCfgSeq = SnsParamCfgInit;
			pSnsParamCfgCtrl->LastSeq = WriteSnsParam;
			pSnsCtrlGroup->GroupStatus = GroupStatus_START_MEAS;
			return 1;
	   }
	   	break;

	   default:
	   	break;
   }
   return 0;
}

/******************************************************************************
 * 函数名称: JudgeWriteGainFactor
 * 
 * 功能描述: 接收0xFDCO的DID数据
 * 
 * 输入参数: 无
 * 
 * 输出参数: 系统工作模式 0:未写入、写入失败或正在写入 1:写入成功
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 ******************************************************************************/
void GetWriteGainData(void)
{
	for(uint8 i=0; i<12; i++)
	{
		SnsWriteStartFlg |= (GuGainDataBuf[i*2]<<i);
	}
}

/******************************************************************************
 * 函数名称: Elmos17SnsCtrl_InterFail_Event5_Num(void)
 * 
 * 功能描述: 获取探头的校准增益，并通过CAN输出
 * 
 * 输入参数: 无
 * 
 * 输出参数: 探头索引或增益
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 ******************************************************************************/
 uint8 Elmos17SnsCtrl_InterFail_Event5_Num(void)
{
	uint8 returnVal = 0;
	static uint8 GainShowCnt = 0;
	uint8 Lu8IDIdx = 0;
	
	Lu8IDIdx = GainShowCnt / 3;/*每个探头索引和增益显示3个周期*/

	GainShowCnt++;
	if(GainShowCnt > 71)
	{
		GainShowCnt = 0;
	}

	if(0 == (Lu8IDIdx % 2))
	{
		returnVal = Lu8IDIdx / 2; /*SnsID*/
	}
	else
	{
		returnVal = u8SnsGain[Lu8IDIdx / 2];
	}

	return returnVal;
}

