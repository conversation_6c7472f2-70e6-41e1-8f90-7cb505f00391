"""
命令解析工具GUI界面
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import json
import re
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.backends.backend_tkagg import NavigationToolbar2Tk
import numpy as np
import matplotlib
# 设置matplotlib支持中文显示
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
matplotlib.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
from command_parser import (
    CommandParserFactory,
    ResponseParserFactory,
    history_manager,
    set_debug_level, DEBUG_LEVEL_INFO
)


# CRC8算法常量
# SPI CRC8_C2算法常量(x8+x5+x3+x2+x+1)
CRC8_C2_POLYNOMIAL = 0x2F
CRC8_C2_INIT = 0xFF

# DSI3 CRC8算法常量 (x8+x5+x3+x2+x+1)
CRC8_DSI3_POLYNOMIAL = 0x2F
CRC8_DSI3_INIT = 0x42

# Slot CRC8算法常量 (x8+x5+x3+x2+x+1)
CRC8_SLOT_POLYNOMIAL = 0x2F
# slot1---0x2F  slot2---0x5e
CRC8_SLOT_INIT = 0x5e
CRC8_SLOT1_INIT = 0x2F
CRC8_SLOT2_INIT = 0x5E
CRC8_SLOT3_INIT = 0x42
CRC8_SLOT4_INIT = 0x42
CRC8_SLOT5_INIT = 0x2F
CRC8_SLOT6_INIT = 0x5E
CRC8_SLOT7_INIT = 0x42
CRC8_SLOT8_INIT = 0x42

def crc8_c2_calculate(data):
    """
    计算CRC8_C2校验值 (SPI通信)

    参数:
        data: 字节列表或字节字符串

    返回:
        CRC8_C2校验值
    """
    crc = CRC8_C2_INIT

    # 初始化CRC表
    crc_table = []
    for i in range(256):
        crc_value = i
        for j in range(8):
            if crc_value & 0x80:
                crc_value = (crc_value << 1) ^ CRC8_C2_POLYNOMIAL
            else:
                crc_value <<= 1
            crc_value &= 0xFF  # 确保结果是8位
        crc_table.append(crc_value)

    # 计算CRC
    for byte in data:
        crc = crc_table[crc ^ byte]

    return crc

def crc8_dsi3_calculate(data):
    """
    计算CRC8 DSI3校验值 (DSI3通信)
    多项式: x8+x5+x3+x2+x+1

    参数:
        data: 字节列表或字节字符串

    返回:
        CRC8 DSI3校验值
    """
    crc = CRC8_DSI3_INIT
    for byte in data:
        crc ^= byte
        for _ in range(8):
            if crc & 0x80:
                crc = (crc << 1) ^ CRC8_DSI3_POLYNOMIAL
            else:
                crc <<= 1
        crc &= 0xFF  # 确保是8位值
    return crc

def crc8_slot_calculate(data, slot_num=1):
    """
    计算CRC8 Slot校验值 (用于slot校验)
    多项式: x8+x5+x3+x2+x+1

    参数:
        data: 字节列表或字节字符串
        slot_num: slot编号(1-8)

    返回:
        CRC8 Slot校验值
    """
    # 根据slot编号选择初始值
    if slot_num == 1:
        crc = CRC8_SLOT1_INIT
    elif slot_num == 2:
        crc = CRC8_SLOT2_INIT
    elif slot_num == 3:
        crc = CRC8_SLOT3_INIT
    elif slot_num == 4:
        crc = CRC8_SLOT4_INIT
    elif slot_num == 5:
        crc = CRC8_SLOT5_INIT
    elif slot_num == 6:
        crc = CRC8_SLOT6_INIT
    elif slot_num == 7:
        crc = CRC8_SLOT7_INIT
    else:  # slot_num == 8 或其他值
        crc = CRC8_SLOT8_INIT

    for byte in data:
        crc ^= byte
        for _ in range(8):
            if crc & 0x80:
                crc = (crc << 1) ^ CRC8_SLOT_POLYNOMIAL
            else:
                crc <<= 1
        crc &= 0xFF  # 确保是8位值
    return crc


# 设置调试级别
set_debug_level(DEBUG_LEVEL_INFO)

def get_slot_init_value(slot_num):
    """
    获取指定slot编号的初始值

    参数:
        slot_num: slot编号(1-8)

    返回:
        对应slot的初始值
    """
    if slot_num == 1:
        return CRC8_SLOT1_INIT
    elif slot_num == 2:
        return CRC8_SLOT2_INIT
    elif slot_num == 3:
        return CRC8_SLOT3_INIT
    elif slot_num == 4:
        return CRC8_SLOT4_INIT
    elif slot_num == 5:
        return CRC8_SLOT5_INIT
    elif slot_num == 6:
        return CRC8_SLOT6_INIT
    elif slot_num == 7:
        return CRC8_SLOT7_INIT
    else:  # slot_num == 8 或其他值
        return CRC8_SLOT8_INIT

class HexEntry(ttk.Entry):
    """十六进制输入框，只允许输入十六进制字符"""

    def __init__(self, master=None, **kwargs):
        """初始化十六进制输入框"""
        self.var = tk.StringVar()
        super().__init__(master, textvariable=self.var, **kwargs)
        self.var.trace_add("write", self.validate)

    def validate(self, *args):
        """验证输入是否为十六进制字符"""
        value = self.var.get()
        # 移除所有空白字符
        value = re.sub(r'\s+', '', value)
        # 检查是否包含非十六进制字符
        if value and not all(c in "0123456789abcdefABCDEF" for c in value):
            # 移除非十六进制字符
            value = ''.join(c for c in value if c in "0123456789abcdefABCDEF")
            self.var.set(value)

    def get(self):
        """获取输入值"""
        return self.var.get()

    def set(self, value):
        """设置输入值"""
        self.var.set(value)


class ByteFrame(ttk.Frame):
    """字节输入框，包含一个标签和一个输入框"""

    def __init__(self, master=None, label="", width=2, **kwargs):
        """初始化字节输入框"""
        super().__init__(master, **kwargs)
        self.label = ttk.Label(self, text=label)
        self.entry = HexEntry(self, width=width)
        self.label.pack(side=tk.TOP)
        self.entry.pack(side=tk.TOP)

    def get(self):
        """获取输入值"""
        value = self.entry.get()
        if not value:
            return 0
        return int(value, 16)

    def set(self, value):
        """设置输入值"""
        self.entry.set(f"{value:02X}")


class CommandFrame(ttk.LabelFrame):
    """命令输入框，使用单个文本框输入完整的十六进制字符串"""

    def __init__(self, master=None, title="命令", **kwargs):
        """初始化命令输入框"""
        super().__init__(master, text=title, **kwargs)

        # 创建输入框框架
        self.input_frame = ttk.Frame(self)

        # 创建标签
        self.label = ttk.Label(self.input_frame, text="请输入命令（十六进制格式，如：02 50 FF FF FF FF 7A）：")
        self.label.pack(side=tk.TOP, anchor=tk.W, padx=5, pady=5)

        # 创建输入框
        self.entry = ttk.Entry(self.input_frame, width=50)
        self.entry.pack(side=tk.TOP, fill=tk.X, padx=5, pady=5)

        # 示例标签
        self.example_label = ttk.Label(self.input_frame, text="示例：02 50 FF FF FF FF 7A（写操作）、03 11 00 00 00 00 E8（读操作）", foreground="gray")
        self.example_label.pack(side=tk.TOP, anchor=tk.W, padx=5, pady=2)

        self.input_frame.pack(side=tk.TOP, fill=tk.X, padx=5, pady=5)

        # 创建按钮
        self.buttons_frame = ttk.Frame(self)

        self.parse_button = ttk.Button(self.buttons_frame, text="解析命令", command=self.parse_command)
        self.parse_button.pack(side=tk.LEFT, padx=5, pady=5)

        self.crc8_button = ttk.Button(self.buttons_frame, text="计算CRC8", command=self.calculate_crc8)
        self.crc8_button.pack(side=tk.LEFT, padx=5, pady=5)

        # self.plot_button = ttk.Button(self.buttons_frame, text="生成曲线", command=self.plot_curve)
        # self.plot_button.pack(side=tk.LEFT, padx=5, pady=5)

        self.clear_button = ttk.Button(self.buttons_frame, text="清除", command=self.clear)
        self.clear_button.pack(side=tk.LEFT, padx=5, pady=5)

        self.buttons_frame.pack(side=tk.TOP, padx=5, pady=5)

        # 解析结果
        self.result = None

        # 回调函数
        self.on_parse = None

    def get_bytes(self):
        """获取输入的字节列表"""
        try:
            # 获取输入的十六进制字符串
            hex_string = self.entry.get().strip()

            # 解析十六进制字符串为字节列表
            return self.parse_hex_string(hex_string)
        except Exception as e:
            messagebox.showerror("错误", f"解析十六进制字符串失败：{str(e)}")
            return []

    def set_bytes(self, bytes_list):
        """设置输入的字节列表"""
        # 将字节列表转换为十六进制字符串
        hex_string = " ".join([f"{b:02X}" for b in bytes_list])

        # 设置输入框的值
        self.entry.delete(0, tk.END)
        self.entry.insert(0, hex_string)

    def parse_command(self):
        """解析命令"""
        try:
            # 获取输入的字节列表
            bytes_list = self.get_bytes()

            if not bytes_list:
                return

            # 创建命令解析器
            parser = CommandParserFactory.create_parser(bytes_list)

            # 解析命令
            self.result = parser.parse(bytes_list)

            # 调用回调函数
            if self.on_parse:
                self.on_parse(self.result)
        except Exception as e:
            messagebox.showerror("错误", str(e))

    def calculate_crc8(self):
        """计算CRC8校验值"""
        try:
            # 获取输入的字节列表
            bytes_list = self.get_bytes()

            if not bytes_list:
                messagebox.showwarning("警告", "请输入有效的十六进制数据")
                return

            # 计算CRC8
            crc = crc8_c2_calculate(bytes_list)

            # 显示结果
            messagebox.showinfo("CRC8计算结果", f"输入数据: {' '.join([f'{b:02X}' for b in bytes_list])}\nCRC8值: 0x{crc:02X}")

            # 询问是否将CRC8添加到输入框
            # if messagebox.askyesno("添加CRC8", "是否将CRC8值添加到输入框？"):
            #     # 获取当前输入
            #     current_input = self.entry.get().strip()
            #     # 添加CRC8
            #     self.entry.delete(0, tk.END)
            #     self.entry.insert(0, f"{current_input} {crc:02X}")
        except Exception as e:
            messagebox.showerror("错误", f"计算CRC8失败：{str(e)}")

    def plot_curve(self):
        """生成曲线"""
        try:
            # 获取输入的字节列表
            bytes_list = self.get_bytes()

            if not bytes_list:
                messagebox.showwarning("警告", "请输入有效的十六进制数据")
                return

            # 创建曲线图对话框
            self._create_curve_dialog(bytes_list, "命令数据曲线")
        except Exception as e:
            messagebox.showerror("错误", f"生成曲线失败：{str(e)}")

    def _create_curve_dialog(self, data_list, title):
        """创建曲线图对话框"""
        # 创建对话框
        dialog = tk.Toplevel()
        dialog.title(title)
        dialog.geometry("800x600")
        dialog.transient()
        dialog.grab_set()

        # 创建说明标签
        label = ttk.Label(dialog, text="每个十六进制数据点代表一个点位，时间间隔为102.4us")
        label.pack(padx=10, pady=5)

        # 创建输入框框架
        input_frame = ttk.Frame(dialog)
        input_frame.pack(fill=tk.X, padx=10, pady=5)

        # 创建输入标签
        input_label = ttk.Label(input_frame, text="添加新数据（十六进制格式）：")
        input_label.pack(side=tk.LEFT, padx=5)

        # 创建输入框
        input_entry = ttk.Entry(input_frame, width=50)
        input_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # 创建图形
        fig = plt.Figure(figsize=(8, 6), dpi=100)
        ax = fig.add_subplot(111)

        # 创建时间轴
        time_interval = 102.4  # us
        time_axis = np.arange(0, len(data_list) * time_interval, time_interval)

        # 绘制曲线
        line, = ax.plot(time_axis, data_list, 'b-', label='数据1')
        ax.set_xlabel('时间 (us)')
        ax.set_ylabel('数值')
        ax.set_title('数据曲线')
        ax.grid(True)
        ax.legend()

        # 创建画布
        canvas = FigureCanvasTkAgg(fig, master=dialog)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 存储所有曲线数据
        curves_data = [{'data': data_list, 'line': line, 'label': '数据1', 'color': 'b'}]

        # 添加新曲线函数
        def add_curve():
            try:
                # 获取输入的十六进制字符串
                hex_string = input_entry.get().strip()

                if not hex_string:
                    messagebox.showwarning("警告", "请输入有效的十六进制数据")
                    return

                # 解析十六进制字符串为字节列表
                new_data = []
                # 移除所有空白字符
                hex_string = re.sub(r'\s+', '', hex_string)

                # 检查字符串长度是否为偶数
                if len(hex_string) % 2 != 0:
                    raise ValueError("十六进制字符串长度必须为偶数")

                # 检查是否包含非十六进制字符
                if not all(c in "0123456789abcdefABCDEF" for c in hex_string):
                    raise ValueError("十六进制字符串只能包含0-9、a-f、A-F")

                # 将字符串转换为字节列表
                new_data = [int(hex_string[i:i+2], 16) for i in range(0, len(hex_string), 2)]

                if not new_data:
                    messagebox.showwarning("警告", "请输入有效的十六进制数据")
                    return

                # 创建新的时间轴
                new_time_axis = np.arange(0, len(new_data) * time_interval, time_interval)

                # 选择颜色
                colors = ['r', 'g', 'm', 'c', 'y', 'k']
                color_index = len(curves_data) % len(colors)
                color = colors[color_index]

                # 绘制新曲线
                new_line, = ax.plot(new_time_axis, new_data, color=color, label=f'数据{len(curves_data)+1}')

                # 添加到曲线数据
                curves_data.append({'data': new_data, 'line': new_line, 'label': f'数据{len(curves_data)+1}', 'color': color})

                # 更新图例
                ax.legend()

                # 重绘画布
                canvas.draw()

                # 清空输入框
                input_entry.delete(0, tk.END)
            except Exception as e:
                messagebox.showerror("错误", f"添加曲线失败：{str(e)}")

        # 创建按钮框架
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        # 创建添加按钮
        add_button = ttk.Button(button_frame, text="添加曲线", command=add_curve)
        add_button.pack(side=tk.LEFT, padx=5)

        # 创建关闭按钮
        close_button = ttk.Button(button_frame, text="关闭", command=dialog.destroy)
        close_button.pack(side=tk.RIGHT, padx=5)

        # 更新对话框
        dialog.update()

    def clear(self):
        """清除输入"""
        self.entry.delete(0, tk.END)

        # 清除解析结果
        self.result = None

        # 调用回调函数
        if self.on_parse:
            self.on_parse(None)

    def parse_hex_string(self, hex_string):
        """解析十六进制字符串为字节列表"""
        # 移除所有空白字符
        hex_string = re.sub(r'\s+', '', hex_string)

        # 检查字符串长度是否为偶数
        if len(hex_string) % 2 != 0:
            raise ValueError("十六进制字符串长度必须为偶数")

        # 检查是否包含非十六进制字符
        if not all(c in "0123456789abcdefABCDEF" for c in hex_string):
            raise ValueError("十六进制字符串只能包含0-9、a-f、A-F")

        # 将字符串转换为字节列表
        return [int(hex_string[i:i+2], 16) for i in range(0, len(hex_string), 2)]


class ResponseFrame(ttk.LabelFrame):
    """响应输入框，使用单个文本框输入完整的十六进制字符串"""

    def __init__(self, master=None, title="响应", **kwargs):
        """初始化响应输入框"""
        super().__init__(master, text=title, **kwargs)

        # 创建输入框框架
        self.input_frame = ttk.Frame(self)

        # 创建标签
        self.label = ttk.Label(self.input_frame, text="请输入响应（十六进制格式，如：00 00 06 45 A5 A1 A6）：")
        self.label.pack(side=tk.TOP, anchor=tk.W, padx=5, pady=5)

        # 创建输入框
        self.entry = ttk.Entry(self.input_frame, width=50)
        self.entry.pack(side=tk.TOP, fill=tk.X, padx=5, pady=5)

        # 示例标签
        self.example_label = ttk.Label(self.input_frame, text="示例：00 00 06 45 A5 A1 A6（写操作响应）、00 83 00 00 00 00 92（读操作响应）", foreground="gray")
        self.example_label.pack(side=tk.TOP, anchor=tk.W, padx=5, pady=2)

        self.input_frame.pack(side=tk.TOP, fill=tk.X, padx=5, pady=5)

        # 创建按钮
        self.buttons_frame = ttk.Frame(self)

        self.parse_button = ttk.Button(self.buttons_frame, text="解析响应", command=self.parse_response)
        self.parse_button.pack(side=tk.LEFT, padx=5, pady=5)

        self.crc8_button = ttk.Button(self.buttons_frame, text="计算CRC8", command=self.calculate_crc8)
        self.crc8_button.pack(side=tk.LEFT, padx=5, pady=5)

        # self.plot_button = ttk.Button(self.buttons_frame, text="生成曲线", command=self.plot_curve)
        # self.plot_button.pack(side=tk.LEFT, padx=5, pady=5)

        self.clear_button = ttk.Button(self.buttons_frame, text="清除", command=self.clear)
        self.clear_button.pack(side=tk.LEFT, padx=5, pady=5)

        self.buttons_frame.pack(side=tk.TOP, padx=5, pady=5)

        # 命令解析结果
        self.command_result = None

        # 解析结果
        self.result = None

        # 回调函数
        self.on_parse = None

    def get_bytes(self):
        """获取输入的字节列表"""
        try:
            # 获取输入的十六进制字符串
            hex_string = self.entry.get().strip()

            # 解析十六进制字符串为字节列表
            return self.parse_hex_string(hex_string)
        except Exception as e:
            messagebox.showerror("错误", f"解析十六进制字符串失败：{str(e)}")
            return []

    def set_bytes(self, bytes_list):
        """设置输入的字节列表"""
        # 将字节列表转换为十六进制字符串
        hex_string = " ".join([f"{b:02X}" for b in bytes_list])

        # 设置输入框的值
        self.entry.delete(0, tk.END)
        self.entry.insert(0, hex_string)

    def set_command_result(self, command_result):
        """设置命令解析结果"""
        self.command_result = command_result

    def parse_response(self):
        """解析响应"""
        try:
            # 检查是否有命令解析结果
            if not self.command_result:
                messagebox.showerror("错误", "请先解析命令")
                return

            # 获取输入的字节列表
            bytes_list = self.get_bytes()

            if not bytes_list:
                return

            # 创建响应解析器
            operation_name = self.command_result.get("operation_name")
            parser = ResponseParserFactory.create_parser(self.command_result["type"], operation_name)

            # 解析响应
            self.result = parser.parse(bytes_list)

            # 调用回调函数
            if self.on_parse:
                self.on_parse(self.result)
        except Exception as e:
            messagebox.showerror("错误", str(e))

    def calculate_crc8(self):
        """计算CRC8校验值"""
        try:
            # 获取输入的字节列表
            bytes_list = self.get_bytes()

            if not bytes_list:
                messagebox.showwarning("警告", "请输入有效的十六进制数据")
                return

            # 计算CRC8
            crc = crc8_c2_calculate(bytes_list)

            # 显示结果
            messagebox.showinfo("CRC8计算结果", f"输入数据: {' '.join([f'{b:02X}' for b in bytes_list])}\nCRC8值: 0x{crc:02X}")

            # 询问是否将CRC8添加到输入框
            # if messagebox.askyesno("添加CRC8", "是否将CRC8值添加到输入框？"):
            #     # 获取当前输入
            #     current_input = self.entry.get().strip()
            #     # 添加CRC8
            #     self.entry.delete(0, tk.END)
            #     self.entry.insert(0, f"{current_input} {crc:02X}")
        except Exception as e:
            messagebox.showerror("错误", f"计算CRC8失败：{str(e)}")

    def plot_curve(self):
        """生成曲线"""
        try:
            # 获取输入的字节列表
            bytes_list = self.get_bytes()

            if not bytes_list:
                messagebox.showwarning("警告", "请输入有效的十六进制数据")
                return

            # 创建曲线图对话框
            self._create_curve_dialog(bytes_list, "响应数据曲线")
        except Exception as e:
            messagebox.showerror("错误", f"生成曲线失败：{str(e)}")

    def _create_curve_dialog(self, data_list, title):
        """创建曲线图对话框"""
        # 创建对话框
        dialog = tk.Toplevel()
        dialog.title(title)
        dialog.geometry("800x600")
        dialog.transient()
        dialog.grab_set()

        # 创建说明标签
        label = ttk.Label(dialog, text="每个十六进制数据点代表一个点位，时间间隔为102.4us")
        label.pack(padx=10, pady=5)

        # 创建输入框框架
        input_frame = ttk.Frame(dialog)
        input_frame.pack(fill=tk.X, padx=10, pady=5)

        # 创建输入标签
        input_label = ttk.Label(input_frame, text="添加新数据（十六进制格式）：")
        input_label.pack(side=tk.LEFT, padx=5)

        # 创建输入框
        input_entry = ttk.Entry(input_frame, width=50)
        input_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # 创建图形
        fig = plt.Figure(figsize=(8, 6), dpi=100)
        ax = fig.add_subplot(111)

        # 创建时间轴
        time_interval = 102.4  # us
        time_axis = np.arange(0, len(data_list) * time_interval, time_interval)

        # 绘制曲线
        line, = ax.plot(time_axis, data_list, 'r-', label='数据1')
        ax.set_xlabel('时间 (us)')
        ax.set_ylabel('数值')
        ax.set_title('数据曲线')
        ax.grid(True)
        ax.legend()

        # 创建画布
        canvas = FigureCanvasTkAgg(fig, master=dialog)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 存储所有曲线数据
        curves_data = [{'data': data_list, 'line': line, 'label': '数据1', 'color': 'r'}]

        # 添加新曲线函数
        def add_curve():
            try:
                # 获取输入的十六进制字符串
                hex_string = input_entry.get().strip()

                if not hex_string:
                    messagebox.showwarning("警告", "请输入有效的十六进制数据")
                    return

                # 解析十六进制字符串为字节列表
                new_data = []
                # 移除所有空白字符
                hex_string = re.sub(r'\s+', '', hex_string)

                # 检查字符串长度是否为偶数
                if len(hex_string) % 2 != 0:
                    raise ValueError("十六进制字符串长度必须为偶数")

                # 检查是否包含非十六进制字符
                if not all(c in "0123456789abcdefABCDEF" for c in hex_string):
                    raise ValueError("十六进制字符串只能包含0-9、a-f、A-F")

                # 将字符串转换为字节列表
                new_data = [int(hex_string[i:i+2], 16) for i in range(0, len(hex_string), 2)]

                if not new_data:
                    messagebox.showwarning("警告", "请输入有效的十六进制数据")
                    return

                # 创建新的时间轴
                new_time_axis = np.arange(0, len(new_data) * time_interval, time_interval)

                # 选择颜色
                colors = ['b', 'g', 'm', 'c', 'y', 'k']
                color_index = len(curves_data) % len(colors)
                color = colors[color_index]

                # 绘制新曲线
                new_line, = ax.plot(new_time_axis, new_data, color=color, label=f'数据{len(curves_data)+1}')

                # 添加到曲线数据
                curves_data.append({'data': new_data, 'line': new_line, 'label': f'数据{len(curves_data)+1}', 'color': color})

                # 更新图例
                ax.legend()

                # 重绘画布
                canvas.draw()

                # 清空输入框
                input_entry.delete(0, tk.END)
            except Exception as e:
                messagebox.showerror("错误", f"添加曲线失败：{str(e)}")

        # 创建按钮框架
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        # 创建添加按钮
        add_button = ttk.Button(button_frame, text="添加曲线", command=add_curve)
        add_button.pack(side=tk.LEFT, padx=5)

        # 创建关闭按钮
        close_button = ttk.Button(button_frame, text="关闭", command=dialog.destroy)
        close_button.pack(side=tk.RIGHT, padx=5)

        # 更新对话框
        dialog.update()

    def clear(self):
        """清除输入"""
        self.entry.delete(0, tk.END)

        # 清除解析结果
        self.result = None

        # 调用回调函数
        if self.on_parse:
            self.on_parse(None)

    def parse_hex_string(self, hex_string):
        """解析十六进制字符串为字节列表"""
        # 移除所有空白字符
        hex_string = re.sub(r'\s+', '', hex_string)

        # 检查字符串长度是否为偶数
        if len(hex_string) % 2 != 0:
            raise ValueError("十六进制字符串长度必须为偶数")

        # 检查是否包含非十六进制字符
        if not all(c in "0123456789abcdefABCDEF" for c in hex_string):
            raise ValueError("十六进制字符串只能包含0-9、a-f、A-F")

        # 将字符串转换为字节列表
        return [int(hex_string[i:i+2], 16) for i in range(0, len(hex_string), 2)]


class ResultFrame(ttk.LabelFrame):
    """结果显示框"""

    def __init__(self, master=None, title="解析结果", **kwargs):
        """初始化结果显示框"""
        super().__init__(master, text=title, **kwargs)

        # 创建选项卡
        self.notebook = ttk.Notebook(self)

        # 创建命令解析结果选项卡
        self.command_frame = ttk.Frame(self.notebook)

        # 创建树形视图
        self.command_tree = ttk.Treeview(self.command_frame)
        self.command_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建滚动条
        self.command_scrollbar = ttk.Scrollbar(self.command_frame, orient="vertical", command=self.command_tree.yview)
        self.command_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.command_tree.configure(yscrollcommand=self.command_scrollbar.set)

        # 设置列
        self.command_tree["columns"] = ("value", "description")
        self.command_tree.column("#0", width=200, minwidth=200)
        self.command_tree.column("value", width=200, minwidth=150)
        self.command_tree.column("description", width=400, minwidth=200)

        # 设置列标题
        self.command_tree.heading("#0", text="字段")
        self.command_tree.heading("value", text="值")
        self.command_tree.heading("description", text="描述")

        self.notebook.add(self.command_frame, text="命令解析结果")

        # 创建响应解析结果选项卡
        self.response_frame = ttk.Frame(self.notebook)

        # 创建树形视图
        self.response_tree = ttk.Treeview(self.response_frame)
        self.response_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建滚动条
        self.response_scrollbar = ttk.Scrollbar(self.response_frame, orient="vertical", command=self.response_tree.yview)
        self.response_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.response_tree.configure(yscrollcommand=self.response_scrollbar.set)

        # 设置列
        self.response_tree["columns"] = ("value", "description")
        self.response_tree.column("#0", width=200, minwidth=200)
        self.response_tree.column("value", width=200, minwidth=150)
        self.response_tree.column("description", width=400, minwidth=200)

        # 设置列标题
        self.response_tree.heading("#0", text="字段")
        self.response_tree.heading("value", text="值")
        self.response_tree.heading("description", text="描述")

        self.notebook.add(self.response_frame, text="响应解析结果")

        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def set_command_result(self, result):
        """设置命令解析结果"""
        # 清空树形视图
        for item in self.command_tree.get_children():
            self.command_tree.delete(item)

        if result:
            # 添加结果到树形视图
            self._add_dict_to_tree(self.command_tree, "", result)
            # 切换到命令解析结果选项卡
            self.notebook.select(0)

    def set_response_result(self, result):
        """设置响应解析结果"""
        # 清空树形视图
        for item in self.response_tree.get_children():
            self.response_tree.delete(item)

        if result:
            # 添加结果到树形视图
            self._add_dict_to_tree(self.response_tree, "", result)
            # 切换到响应解析结果选项卡
            self.notebook.select(1)

    def _add_dict_to_tree(self, tree, parent, dictionary):
        """将字典添加到树形视图"""
        # 按照字段名称排序,更改为按照原来的排序，不更改
        # sorted_keys = sorted(dictionary.keys())
        sorted_keys = list(dictionary.keys())

        # 首先添加重要字段
        important_fields = ["type", "address", "address_hex", "operation", "operation_hex",
                           "register_name", "register_address_hex", "data_hex", "crc8_hex"]

        for field in important_fields:
            if field in dictionary:
                value = dictionary[field]
                description = ""

                # 获取描述
                if field == "register_name" and "_register_info" in dictionary and "description" in dictionary["_register_info"]:
                    description = dictionary["_register_info"]["description"]

                if isinstance(value, dict):
                    node = tree.insert(parent, "end", text=field, values=("", description))
                    self._add_dict_to_tree(tree, node, value)
                else:
                    tree.insert(parent, "end", text=field, values=(value, description))
                sorted_keys.remove(field)

        # 然后添加其他字段
        for key in sorted_keys:
            # 跳过内部字段
            if key.startswith("_"):
                continue

            value = dictionary[key]
            description = ""

            # 获取描述
            if "bit_fields_desc" in dictionary and key in dictionary["bit_fields_desc"]:
                description = dictionary["bit_fields_desc"][key]
            elif isinstance(value, dict) and "description" in value:
                description = value["description"]

            if key == "fields":
                # 特殊处理fields字段
                node = tree.insert(parent, "end", text=key, values=("", description))
                self._add_dict_to_tree(tree, node, value)
            elif key == "bit_fields":
                # 特殊处理bit_fields字段
                node = tree.insert(parent, "end", text=key, values=("", "位字段定义"))
                self._add_dict_to_tree(tree, node, value)
            elif key == "bit_fields_hex" or key == "bit_fields_desc" or key.endswith("_BITS_DESC") or key == "parsed_data_desc":
                # 跳过这些字段，它们的内容已经在对应的字段中显示
                continue
            elif key == "parsed_data_hex":
                # 特殊处理parsed_data_hex字段
                node = tree.insert(parent, "end", text=key, values=("", "解析后的数据"))
                # 获取对应的描述字典
                desc_dict = dictionary.get("parsed_data_desc", {})
                # 添加位字段
                for field_name, field_value in value.items():
                    field_desc = desc_dict.get(field_name, "")
                    tree.insert(node, "end", text=field_name, values=(field_value, field_desc))
            elif key == "CONTROL_BYTE_BITS" or key.endswith("_BITS"):
                # 特殊处理CONTROL_BYTE_BITS、SPI_RCVR_STATUS_CHA_BITS和SPI_RCVR_STATUS_CHB_BITS字段
                node = tree.insert(parent, "end", text=key, values=("", "位字段定义"))
                # 获取对应的描述字典
                desc_key = f"{key}_DESC"
                desc_dict = dictionary.get(desc_key, {})
                # 添加位字段
                for bit_name, bit_value in value.items():
                    bit_desc = desc_dict.get(bit_name, "")
                    tree.insert(node, "end", text=bit_name, values=(bit_value, bit_desc))
            elif isinstance(value, dict):
                # 检查是否是位字段值
                if "value" in value and "hex" in value and "description" in value:
                    # 显示位字段值、十六进制表示和描述
                    tree.insert(parent, "end", text=key, values=(f"{value['value']} ({value['hex']})", value["description"]))
                else:
                    node = tree.insert(parent, "end", text=key, values=("", description))
                    self._add_dict_to_tree(tree, node, value)
            elif isinstance(value, list):
                # 特殊处理列表
                if key == "data":
                    # 将字节列表转换为十六进制字符串
                    hex_str = " ".join([f"{b:02X}" for b in value])
                    tree.insert(parent, "end", text=key, values=(hex_str, description))
                else:
                    node = tree.insert(parent, "end", text=key, values=("", description))
                    for i, item in enumerate(value):
                        if isinstance(item, dict):
                            subnode = tree.insert(node, "end", text=f"[{i}]", values=("", ""))
                            self._add_dict_to_tree(tree, subnode, item)
                        else:
                            tree.insert(node, "end", text=f"[{i}]", values=(item, ""))
            else:
                tree.insert(parent, "end", text=key, values=(value, description))


class HistoryFrame(ttk.LabelFrame):
    """历史记录框"""

    def __init__(self, master=None, title="历史记录", **kwargs):
        """初始化历史记录框"""
        super().__init__(master, text=title, **kwargs)

        # 创建树形视图，启用多选
        self.tree = ttk.Treeview(self, selectmode="extended")
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建滚动条
        self.scrollbar = ttk.Scrollbar(self, orient="vertical", command=self.tree.yview)
        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.tree.configure(yscrollcommand=self.scrollbar.set)

        # 设置列
        self.tree["columns"] = ("command", "response", "datetime")
        self.tree.column("#0", width=50, minwidth=50)
        self.tree.column("command", width=250, minwidth=200)
        self.tree.column("response", width=250, minwidth=200)
        self.tree.column("datetime", width=150, minwidth=100)

        # 设置列标题
        self.tree.heading("#0", text="序号")
        self.tree.heading("command", text="命令")
        self.tree.heading("response", text="响应")
        self.tree.heading("datetime", text="时间")

        # 绑定双击事件
        self.tree.bind("<Double-1>", self.on_double_click)

        # 创建右键菜单
        self.context_menu = tk.Menu(self, tearoff=0)
        self.context_menu.add_command(label="复制命令", command=self.copy_command)
        self.context_menu.add_command(label="复制响应", command=self.copy_response)
        self.context_menu.add_command(label="复制命令和响应", command=self.copy_command_and_response)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="删除", command=self.delete_selected)
        self.context_menu.add_command(label="清除所有", command=self.clear)

        # 绑定右键菜单
        self.tree.bind("<Button-3>", self.show_context_menu)

        # 回调函数
        self.on_select = None

        # 加载历史记录
        self.load_history()

    def add_history(self, command_bytes, response_bytes=None, command_result=None, response_result=None):
        """添加历史记录"""
        # 使用历史记录管理器添加历史记录
        history_entry = history_manager.add_history(
            command_bytes=command_bytes,
            response_bytes=response_bytes,
            command_result=command_result,
            response_result=response_result
        )

        # 格式化命令和响应
        command_str = " ".join([f"{b:02X}" for b in command_bytes]) if command_bytes else ""
        response_str = " ".join([f"{b:02X}" for b in response_bytes]) if response_bytes else ""

        # 添加到树形视图
        self.tree.insert("", "end", text=str(len(history_manager.get_history())),
                        values=(command_str, response_str, history_entry["datetime"]))

    def on_double_click(self, event):
        """双击事件处理函数"""
        # 获取选中的项
        selection = self.tree.selection()
        if not selection:
            return

        item = selection[0]

        # 获取项的索引
        index = int(self.tree.item(item, "text")) - 1

        # 获取历史记录
        history = history_manager.get_history()[index]

        # 调用回调函数
        if self.on_select:
            self.on_select(history)

    def show_context_menu(self, event):
        """显示右键菜单"""
        # 选中点击的项
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)

    def copy_command(self):
        """复制命令"""
        selection = self.tree.selection()
        if not selection:
            return

        item = selection[0]
        values = self.tree.item(item, "values")
        command_str = values[0]

        # 复制到剪贴板
        self.clipboard_clear()
        self.clipboard_append(command_str)

    def copy_response(self):
        """复制响应"""
        selection = self.tree.selection()
        if not selection:
            return

        item = selection[0]
        values = self.tree.item(item, "values")
        response_str = values[1]

        # 复制到剪贴板
        self.clipboard_clear()
        self.clipboard_append(response_str)

    def copy_command_and_response(self):
        """复制命令和响应"""
        selection = self.tree.selection()
        if not selection:
            return

        item = selection[0]
        values = self.tree.item(item, "values")
        command_str = values[0]
        response_str = values[1]

        # 复制到剪贴板
        self.clipboard_clear()
        self.clipboard_append(f"{command_str} -> {response_str}")

    def delete_selected(self):
        """删除选中的历史记录"""
        selection = self.tree.selection()
        if not selection:
            return

        # 确认删除
        if not messagebox.askyesno("确认", f"确定要删除选中的 {len(selection)} 条历史记录吗？"):
            return

        # 获取选中项的索引
        indices = []
        for item in selection:
            # 获取项的索引
            index = int(self.tree.item(item, "text")) - 1
            indices.append(index)

        # 使用历史记录管理器删除选中的历史记录
        from command_parser import history_manager
        history_manager.delete_history(indices)

        # 重新加载历史记录
        self.load_history()

    def clear(self):
        """清除历史记录"""
        # 确认清除
        if not messagebox.askyesno("确认", "确定要清除所有历史记录吗？"):
            return

        # 清空树形视图
        for item in self.tree.get_children():
            self.tree.delete(item)

        # 清空历史记录
        history_manager.clear_history()

    def load_history(self):
        """加载历史记录"""
        # 清空树形视图
        for item in self.tree.get_children():
            self.tree.delete(item)

        # 获取历史记录
        history = history_manager.get_history()

        # 添加到树形视图
        for i, entry in enumerate(history):
            command_str = " ".join([f"{b:02X}" for b in entry["command_bytes"]]) if entry["command_bytes"] else ""
            response_str = " ".join([f"{b:02X}" for b in entry["response_bytes"]]) if entry["response_bytes"] else ""
            self.tree.insert("", "end", text=str(i + 1), values=(command_str, response_str, entry["datetime"]))





class CommandAnalyzerGUI(tk.Tk):
    """命令解析工具GUI界面"""

    def __init__(self):
        """初始化GUI界面"""
        super().__init__()

        # 设置窗口标题
        self.title("命令解析工具")

        # 设置窗口大小
        self.geometry("1200x900")

        # 创建菜单栏
        self.menu_bar = tk.Menu(self)

        # 创建文件菜单
        self.file_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.file_menu.add_command(label="批量解析", command=self.show_batch_dialog)
        self.file_menu.add_separator()
        self.file_menu.add_command(label="退出", command=self.quit)
        self.menu_bar.add_cascade(label="文件", menu=self.file_menu)

        # 创建历史记录菜单
        self.history_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.history_menu.add_command(label="保存历史记录", command=self.save_history)
        self.history_menu.add_command(label="加载历史记录", command=self.load_history)
        self.history_menu.add_separator()
        self.history_menu.add_command(label="清除历史记录", command=self.clear_history)
        self.menu_bar.add_cascade(label="历史记录", menu=self.history_menu)

        # 创建帮助菜单
        self.help_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.help_menu.add_command(label="关于", command=self.show_about)
        self.menu_bar.add_cascade(label="帮助", menu=self.help_menu)

        # 设置菜单栏
        self.config(menu=self.menu_bar)

        # 创建主框架
        self.main_frame = ttk.Frame(self)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建左侧框架
        self.left_frame = ttk.Frame(self.main_frame)
        self.left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建右侧框架
        self.right_frame = ttk.Frame(self.main_frame)
        self.right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=5, pady=5)

        # 创建命令和响应输入框
        self.input_frame = ttk.LabelFrame(self.left_frame, text="命令和响应输入")

        # 创建输入框框架
        self.input_area_frame = ttk.Frame(self.input_frame)

        # 创建标签
        self.input_label = ttk.Label(self.input_area_frame, text="请输入命令和响应（每行一条，格式：命令 -> 响应）：")
        self.input_label.pack(side=tk.TOP, anchor=tk.W, padx=5, pady=5)

        # 创建多行文本框
        self.input_text = scrolledtext.ScrolledText(self.input_area_frame, wrap=tk.WORD, width=80, height=10)
        self.input_text.pack(side=tk.TOP, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 示例标签
        self.example_label = ttk.Label(self.input_area_frame, text="示例：\n02 50 FF FF FF FF 7A -> 00 00 06 45 A5 A1 A6\n03 11 00 00 00 00 E8 -> 00 00 05 C0 81 A1 32\n02 03 FF 00 EF FE 0D -> 00 00 03 7D A5 A1 E6", foreground="gray")
        self.example_label.pack(side=tk.TOP, anchor=tk.W, padx=5, pady=2)

        self.input_area_frame.pack(side=tk.TOP, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建按钮
        self.buttons_frame = ttk.Frame(self.input_frame)
        self.buttons_frame.columnconfigure(0, weight=1)
        self.buttons_frame.columnconfigure(1, weight=1)
        self.buttons_frame.columnconfigure(2, weight=1)
        self.buttons_frame.columnconfigure(3, weight=1)
        self.buttons_frame.columnconfigure(4, weight=1)

        # 使用grid布局而不是pack，确保按钮均匀分布
        self.parse_button = ttk.Button(self.buttons_frame, text="解析命令和响应", command=self.parse_input, width=15)
        self.parse_button.grid(row=0, column=0, padx=5, pady=5, sticky="ew")

        self.crc8_button = ttk.Button(self.buttons_frame, text="计算CRC8", command=self.show_crc8_calculator, width=15)
        self.crc8_button.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

        # self.plot_button = ttk.Button(self.buttons_frame, text="生成曲线", command=self.show_curve_dialog, width=15)
        # self.plot_button.grid(row=0, column=2, padx=5, pady=5, sticky="ew")

        self.clear_button = ttk.Button(self.buttons_frame, text="清除", command=self.clear_input, width=15)
        self.clear_button.grid(row=0, column=3, padx=5, pady=5, sticky="ew")

        # 确保按钮框架足够宽以显示所有按钮
        self.buttons_frame.pack(side=tk.TOP, fill=tk.X, padx=5, pady=5)

        self.input_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建结果显示框
        self.result_frame = ResultFrame(self.left_frame)
        self.result_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建历史记录框
        self.history_frame = HistoryFrame(self.right_frame)
        self.history_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建状态栏
        self.status_bar = ttk.Label(self, text="就绪", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # 设置回调函数
        self.history_frame.on_select = self.on_history_select

    def on_history_select(self, history):
        """历史记录选择回调函数"""
        # 设置命令解析结果
        if history["command_result"]:
            self.result_frame.set_command_result(history["command_result"])

            # 更新状态栏
            command_type = history["command_result"].get("type", "")
            address_hex = history["command_result"].get("address_hex", "")
            register_name = history["command_result"].get("register_name", "")
            self.status_bar.config(text=f"历史记录：命令类型={command_type}, 地址={address_hex}, 寄存器={register_name}")

        # 设置响应解析结果
        if history["response_result"]:
            self.result_frame.set_response_result(history["response_result"])

        # 将历史记录添加到输入框
        if history["command_bytes"] and history["response_bytes"]:
            # 将字节列表转换为十六进制字符串
            command_str = " ".join([f"{b:02X}" for b in history["command_bytes"]])
            response_str = " ".join([f"{b:02X}" for b in history["response_bytes"]])

            # 组合命令和响应
            combined_str = f"{command_str} -> {response_str}"

            # 获取当前输入框内容
            current_text = self.input_text.get(1.0, tk.END).strip()

            # 如果输入框不为空，添加换行符
            if current_text:
                combined_str = f"{current_text}\n{combined_str}"

            # 设置输入框内容
            self.input_text.delete(1.0, tk.END)
            self.input_text.insert(tk.END, combined_str)

    def clear_history(self):
        """清除历史记录"""
        self.history_frame.clear()
        self.status_bar.config(text="历史记录已清除")

    def parse_input(self):
        """解析输入的命令和响应"""
        try:
            # 获取输入的文本
            input_text = self.input_text.get(1.0, tk.END).strip()

            # 按行分割文本
            lines = input_text.split("\n")

            # 解析成功计数
            success_count = 0

            # 最后一个成功解析的命令和响应
            last_command_result = None
            last_response_result = None

            # 更新状态栏
            self.status_bar.config(text=f"正在解析，共{len(lines)}行...")
            self.update()

            # 解析每一行
            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 检查是否包含 ->
                if "->" not in line:
                    messagebox.showwarning("警告", f"跳过格式错误的行：{line}\n应为：命令 -> 响应")
                    continue

                try:
                    # 分割命令和响应
                    command_str, response_str = line.split("->", 1)
                    command_str = command_str.strip()
                    response_str = response_str.strip()

                    # 解析命令
                    command_bytes = self.parse_hex_string(command_str)
                    command_parser = CommandParserFactory.create_parser(command_bytes)
                    command_result = command_parser.parse(command_bytes)

                    # 解析响应
                    response_bytes = self.parse_hex_string(response_str)
                    response_parser = ResponseParserFactory.create_parser(command_result["type"], command_result)
                    response_result = response_parser.parse(response_bytes, command_result)

                    # 添加到历史记录
                    self.history_frame.add_history(
                        command_bytes=command_bytes,
                        response_bytes=response_bytes,
                        command_result=command_result,
                        response_result=response_result
                    )

                    # 更新最后一个成功解析的命令和响应
                    last_command_result = command_result
                    last_response_result = response_result

                    success_count += 1
                except Exception as e:
                    messagebox.showwarning("警告", f"解析失败：{line}\n{str(e)}")

            # 如果有成功解析的命令和响应，显示最后一个
            if last_command_result and last_response_result:
                self.result_frame.set_command_result(last_command_result)
                self.result_frame.set_response_result(last_response_result)

            # 更新状态栏
            self.status_bar.config(text=f"解析完成，成功{success_count}条，共{len(lines)}行")
        except Exception as e:
            messagebox.showerror("错误", str(e))

    def clear_input(self):
        """清除输入"""
        self.input_text.delete(1.0, tk.END)

    def parse_hex_string(self, hex_string):
        """解析十六进制字符串为字节列表，支持0x前缀格式"""
        # 处理可能的0x前缀格式
        if "0x" in hex_string.lower():
            # 按空白分割
            parts = hex_string.split()
            bytes_list = []

            for part in parts:
                # 移除0x前缀并转换为整数
                if part.lower().startswith("0x"):
                    bytes_list.append(int(part, 16))
                else:
                    # 尝试直接解析为十六进制
                    try:
                        bytes_list.append(int(part, 16))
                    except ValueError:
                        raise ValueError(f"无效的十六进制值: {part}")

            return bytes_list
        else:
            # 原始处理方式（无0x前缀）
            # 移除所有空白字符
            hex_string = re.sub(r'\s+', '', hex_string)

            # 检查字符串长度是否为偶数
            if len(hex_string) % 2 != 0:
                raise ValueError("十六进制字符串长度必须为偶数")

            # 检查是否包含非十六进制字符
            if not all(c in "0123456789abcdefABCDEF" for c in hex_string):
                raise ValueError("十六进制字符串只能包含0-9、a-f、A-F")

            # 将字符串转换为字节列表
            return [int(hex_string[i:i+2], 16) for i in range(0, len(hex_string), 2)]

    def save_history(self):
        """保存历史记录"""
        try:
            # 保存历史记录
            history_manager.save_history()

            # 更新状态栏
            self.status_bar.config(text=f"历史记录已保存到文件：{history_manager.history_file}")
        except Exception as e:
            messagebox.showerror("错误", f"保存历史记录失败：{str(e)}")

    def load_history(self):
        """加载历史记录"""
        try:
            # 加载历史记录
            history_manager.load_history()

            # 更新历史记录列表
            self.history_frame.load_history()

            # 更新状态栏
            self.status_bar.config(text=f"历史记录已从文件加载：{history_manager.history_file}，共{len(history_manager.get_history())}条")
        except Exception as e:
            messagebox.showerror("错误", f"加载历史记录失败：{str(e)}")

    def show_debug_window(self):
        """显示调试窗口"""
        try:
            # 导入调试窗口
            from debug_window import DebugWindow

            # 创建调试窗口
            if not hasattr(self, "debug_window") or not self.debug_window.winfo_exists():
                self.debug_window = DebugWindow(self)

            # 显示调试窗口
            self.debug_window.deiconify()
            self.debug_window.lift()

            # 更新状态栏
            self.status_bar.config(text="调试窗口已显示")
        except Exception as e:
            messagebox.showerror("错误", f"显示调试窗口失败：{str(e)}")

    def show_crc8_calculator(self):
        """计算CRC8校验值"""
        try:
            # 获取输入的文本
            input_text = self.input_text.get(1.0, tk.END).strip()

            # 如果输入为空，显示计算器对话框(直接打开输入界面)
            self._show_crc8_calculator_dialog()
            return

            # 按行分割文本
            lines = input_text.split("\n")

            # 如果有多行，询问用户要计算哪一行
            if len(lines) > 1:
                # 创建对话框
                dialog = tk.Toplevel(self)
                dialog.title("选择要计算的行")
                dialog.geometry("600x400")
                dialog.transient(self)
                dialog.grab_set()

                # 创建说明标签
                label = ttk.Label(dialog, text="请选择要计算CRC8的行：")
                label.pack(padx=10, pady=10)

                # 创建列表框
                listbox = tk.Listbox(dialog, width=80, height=15)
                listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

                # 添加行到列表框
                for i, line in enumerate(lines):
                    listbox.insert(tk.END, f"{i+1}: {line}")

                # 创建按钮框架
                button_frame = ttk.Frame(dialog)
                button_frame.pack(fill=tk.X, padx=10, pady=10)

                # 计算选中行的CRC8
                def calculate_selected():
                    # 获取选中的索引
                    selection = listbox.curselection()
                    if not selection:
                        messagebox.showwarning("警告", "请选择一行")
                        return

                    # 获取选中的行
                    index = selection[0]
                    line = lines[index]

                    # 关闭对话框
                    dialog.destroy()

                    # 计算CRC8
                    self._calculate_crc8_for_line(line)

                # 创建计算按钮
                calculate_button = ttk.Button(button_frame, text="计算CRC8", command=calculate_selected)
                calculate_button.pack(side=tk.LEFT, padx=5)

                # 创建取消按钮
                cancel_button = ttk.Button(button_frame, text="取消", command=dialog.destroy)
                cancel_button.pack(side=tk.RIGHT, padx=5)

                return

            # 只有一行，直接计算
            self._calculate_crc8_for_line(lines[0])
        except Exception as e:
            messagebox.showerror("错误", f"计算CRC8失败：{str(e)}")

    def _calculate_crc8_for_line(self, line):
        """计算一行数据的CRC8校验值"""
        try:
            # 创建对话框选择算法
            algorithm_dialog = tk.Toplevel(self)
            algorithm_dialog.title("选择CRC8算法")
            algorithm_dialog.geometry("400x500")
            algorithm_dialog.transient(self)
            algorithm_dialog.grab_set()

            # 创建算法选择框架
            algorithm_frame = ttk.Frame(algorithm_dialog)
            algorithm_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 创建算法选择标签
            algorithm_label = ttk.Label(algorithm_frame, text="请选择要使用的CRC8算法：")
            algorithm_label.pack(side=tk.TOP, anchor=tk.W, padx=5, pady=5)

            # 创建算法选择变量
            algorithm_var = tk.StringVar()
            algorithm_var.set("SPI")  # 默认选择SPI算法

            # 创建算法选择单选按钮
            spi_radio = ttk.Radiobutton(algorithm_frame, text="SPI和CRC_SD", variable=algorithm_var, value="SPI")
            spi_radio.pack(side=tk.TOP, anchor=tk.W, padx=5, pady=5)

            dsi3_radio = ttk.Radiobutton(algorithm_frame, text="DSI3(CRM命令)", variable=algorithm_var, value="DSI3")
            dsi3_radio.pack(side=tk.TOP, anchor=tk.W, padx=5, pady=5)

            # 创建slot选择变量
            slot_var = tk.IntVar()
            slot_var.set(1)  # 默认选择slot1

            # 创建slot包校验单选按钮和slot选择下拉框的组合框架
            slot_combined_frame = ttk.Frame(algorithm_frame)
            slot_combined_frame.pack(side=tk.TOP, anchor=tk.W, padx=5, pady=5)

            # 创建Slot包校验单选按钮
            slot_radio = ttk.Radiobutton(slot_combined_frame, text="Slot包校验", variable=algorithm_var, value="SLOT")
            slot_radio.pack(side=tk.LEFT, padx=0, pady=0)

            # 创建slot选择下拉框
            slot_combobox = ttk.Combobox(slot_combined_frame, textvariable=slot_var, width=10)
            slot_combobox['values'] = (1, 2, 3, 4, 5, 6, 7, 8)  # 只提供1-8的选项
            slot_combobox.pack(side=tk.LEFT, padx=5, pady=0)

            # 设置下拉框初始状态
            slot_combobox.config(state="disabled")

            # 显示/隐藏slot选择下拉框
            def toggle_slot_combobox(*args):
                if algorithm_var.get() == "SLOT":
                    slot_combobox.config(state="readonly")
                else:
                    slot_combobox.config(state="disabled")

            # 绑定算法选择变化事件
            algorithm_var.trace_add("write", toggle_slot_combobox)

            # 初始调用一次，确保正确的初始状态
            toggle_slot_combobox()

            # 创建算法说明标签
            algorithm_info_label = ttk.Label(algorithm_frame,
                                           text="SPI: 用于SPI通信校验\nDSI3: 用于DSI3通信校验\nSlot校验: 用于slot校验，可选择不同slot",
                                           foreground="gray")
            algorithm_info_label.pack(side=tk.TOP, anchor=tk.W, padx=5, pady=5)

            # 创建按钮框架
            button_frame = ttk.Frame(algorithm_dialog)
            button_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)

            # 计算函数
            def calculate():
                # 获取选择的算法
                algorithm = algorithm_var.get()

                # 关闭对话框
                algorithm_dialog.destroy()

                # 如果包含 ->，只计算命令部分
                if "->" in line:
                    command_str = line.split("->")[0].strip()
                else:
                    command_str = line.strip()

                # 解析十六进制字符串为字节列表
                bytes_list = self.parse_hex_string(command_str)

                if not bytes_list:
                    messagebox.showwarning("警告", "请输入有效的十六进制数据")
                    return

                # 根据选择的算法计算CRC8
                if algorithm == "SPI":
                    crc = crc8_c2_calculate(bytes_list)
                    algorithm_name = "SPI"
                elif algorithm == "DSI3":
                    crc = crc8_dsi3_calculate(bytes_list)
                    algorithm_name = "DSI3"
                else:  # SLOT
                    # 获取选择的slot编号
                    slot_num = slot_var.get()
                    crc = crc8_slot_calculate(bytes_list, slot_num)
                    algorithm_name = f"Slot{slot_num}校验"

                # 显示结果
                result_message = f"算法: {algorithm_name}\n输入数据: {' '.join([f'{b:02X}' for b in bytes_list])}\nCRC8值: 0x{crc:02X}"
                messagebox.showinfo("CRC8计算结果", result_message)

                # 复制到剪贴板
                self.clipboard_clear()
                self.clipboard_append(f"{crc:02X}")

                # 询问是否将CRC8添加到输入框
                if messagebox.askyesno("添加CRC8", "是否将CRC8值添加到输入框？"):
                    # 获取当前输入
                    current_text = self.input_text.get(1.0, tk.END).strip()

                    # 替换当前行
                    if "->" in line:
                        command_str, response_str = line.split("->", 1)
                        new_line = f"{command_str.strip()} {crc:02X} -> {response_str.strip()}"
                    else:
                        new_line = f"{line.strip()} {crc:02X}"

                    # 替换文本
                    new_text = current_text.replace(line, new_line)

                    # 更新输入框
                    self.input_text.delete(1.0, tk.END)
                    self.input_text.insert(tk.END, new_text)

                # 更新状态栏
                self.status_bar.config(text=f"{algorithm_name} CRC8计算完成：0x{crc:02X}")

            # 创建计算按钮
            calculate_button = ttk.Button(button_frame, text="计算", command=calculate)
            calculate_button.pack(side=tk.LEFT, padx=5, pady=5)

            # 创建取消按钮
            cancel_button = ttk.Button(button_frame, text="取消", command=algorithm_dialog.destroy)
            cancel_button.pack(side=tk.RIGHT, padx=5, pady=5)

            # 等待对话框关闭
            self.wait_window(algorithm_dialog)
        except Exception as e:
            messagebox.showerror("错误", f"计算CRC8失败：{str(e)}")

    def _show_crc8_calculator_dialog(self):
        """显示CRC8计算器对话框"""
        try:
            # 创建对话框
            dialog = tk.Toplevel(self)
            dialog.title("CRC8计算器")
            dialog.geometry("600x350")
            dialog.transient(self)
            dialog.grab_set()

            # 创建输入框框架
            input_frame = ttk.Frame(dialog)
            input_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 创建标签
            label = ttk.Label(input_frame, text="请输入要计算CRC8的数据（十六进制格式）：")
            label.pack(side=tk.TOP, anchor=tk.W, padx=5, pady=5)

            # 创建输入框
            entry = ttk.Entry(input_frame, width=50)
            entry.pack(side=tk.TOP, fill=tk.X, padx=5, pady=5)

            # 示例标签
            example_label = ttk.Label(input_frame,
                                    text="支持格式：\n1. 无0x前缀：02 50 FF FF FF FF\n2. 带0x前缀：0x07 0x00 0x07 0x38 0xA5 0xA1",
                                    foreground="gray")
            example_label.pack(side=tk.TOP, anchor=tk.W, padx=5, pady=2)

            # 创建算法选择框架
            algorithm_frame = ttk.Frame(input_frame)
            algorithm_frame.pack(side=tk.TOP, fill=tk.X, padx=5, pady=5)

            # 创建算法选择标签
            algorithm_label = ttk.Label(algorithm_frame, text="选择CRC8算法：")
            algorithm_label.pack(side=tk.LEFT, padx=5, pady=5)

            # 创建算法选择变量
            algorithm_var = tk.StringVar()
            algorithm_var.set("SPI")  # 默认选择SPI算法

            # 创建算法选择单选按钮
            spi_radio = ttk.Radiobutton(algorithm_frame, text="SPI和CRC_SD", variable=algorithm_var, value="SPI")
            spi_radio.pack(side=tk.LEFT, padx=5, pady=5)

            dsi3_radio = ttk.Radiobutton(algorithm_frame, text="DSI3(CRM命令)", variable=algorithm_var, value="DSI3")
            dsi3_radio.pack(side=tk.LEFT, padx=5, pady=5)

            # 创建slot选择变量
            slot_var = tk.IntVar()
            slot_var.set(1)  # 默认选择slot1

            # 创建Slot包校验单选按钮和slot选择下拉框
            slot_radio = ttk.Radiobutton(algorithm_frame, text="Slot包校验", variable=algorithm_var, value="SLOT")
            slot_radio.pack(side=tk.LEFT, padx=5, pady=5)

            # 创建slot选择下拉框
            slot_combobox = ttk.Combobox(algorithm_frame, textvariable=slot_var, width=10)
            slot_combobox['values'] = (1, 2, 3, 4, 5, 6, 7, 8)  # 只提供1-8的选项
            slot_combobox.pack(side=tk.LEFT, padx=5, pady=5)

            # 设置下拉框初始状态
            slot_combobox.config(state="disabled")

            # 显示/隐藏slot选择下拉框
            def toggle_slot_combobox(*args):
                if algorithm_var.get() == "SLOT":
                    slot_combobox.config(state="readonly")
                else:
                    slot_combobox.config(state="disabled")

            # 绑定算法选择变化事件
            algorithm_var.trace_add("write", toggle_slot_combobox)

            # 初始调用一次，确保正确的初始状态
            toggle_slot_combobox()

            # 创建算法说明标签
            algorithm_info_label = ttk.Label(input_frame,
                                           text="SPI: 用于SPI通信校验\nDSI3: 用于DSI3通信校验\nSlot校验: 用于slot校验，可选择不同slot",
                                           foreground="gray")
            algorithm_info_label.pack(side=tk.TOP, anchor=tk.W, padx=5, pady=2)

            # 创建结果框架
            result_frame = ttk.Frame(input_frame)
            result_frame.pack(side=tk.TOP, fill=tk.X, padx=5, pady=5)

            # 创建结果标签
            result_label = ttk.Label(result_frame, text="CRC8结果：")
            result_label.pack(side=tk.LEFT, padx=5, pady=5)

            # 创建结果显示
            result_var = tk.StringVar()
            result_var.set("")
            result_display = ttk.Label(result_frame, textvariable=result_var, font=("Courier", 12, "bold"))
            result_display.pack(side=tk.LEFT, padx=5, pady=5)

            # 创建按钮框架
            buttons_frame = ttk.Frame(input_frame)
            buttons_frame.pack(side=tk.TOP, padx=5, pady=5)

            # 计算CRC8函数
            def calculate_crc8():
                try:
                    # 获取输入的十六进制字符串
                    hex_string = entry.get().strip()

                    # 解析十六进制字符串为字节列表
                    bytes_list = self.parse_hex_string(hex_string)

                    if not bytes_list:
                        messagebox.showwarning("警告", "请输入有效的十六进制数据")
                        return

                    # 获取选择的算法
                    algorithm = algorithm_var.get()

                    # 根据选择的算法计算CRC8
                    if algorithm == "SPI":
                        crc = crc8_c2_calculate(bytes_list)
                        algorithm_name = "SPI"
                    elif algorithm == "DSI3":
                        crc = crc8_dsi3_calculate(bytes_list)
                        algorithm_name = "DSI3"
                    else:  # SLOT
                        # 获取选择的slot编号
                        slot_num = slot_var.get()
                        crc = crc8_slot_calculate(bytes_list, slot_num)
                        algorithm_name = f"Slot{slot_num}校验"

                    # 显示结果
                    result_var.set(f"0x{crc:02X}")

                    # 复制到剪贴板
                    # self.clipboard_clear()
                    # self.clipboard_append(f"{crc:02X}")
                    # messagebox.showinfo("提示", f"{algorithm_name} CRC8值已复制到剪贴板")
                except Exception as e:
                    messagebox.showerror("错误", str(e))

            # 清除函数
            def clear():
                entry.delete(0, tk.END)
                result_var.set("")

            # 创建计算按钮
            calculate_button = ttk.Button(buttons_frame, text="计算CRC8", command=calculate_crc8)
            calculate_button.pack(side=tk.LEFT, padx=5, pady=5)

            # 创建清除按钮
            clear_button = ttk.Button(buttons_frame, text="清除", command=clear)
            clear_button.pack(side=tk.LEFT, padx=5, pady=5)

            # 创建关闭按钮
            # close_button = ttk.Button(dialog, text="关闭", command=dialog.destroy)
            # close_button.pack(side=tk.BOTTOM, padx=10, pady=10)

            # 更新状态栏
            self.status_bar.config(text="CRC8计算器已显示")
        except Exception as e:
            messagebox.showerror("错误", f"显示CRC8计算器失败：{str(e)}")

    def show_curve_dialog(self):
        """显示曲线图对话框"""
        try:
            # 获取输入的文本
            input_text = self.input_text.get(1.0, tk.END).strip()

            # 如果输入为空，显示空的曲线图对话框
            if not input_text:
                self._show_empty_curve_dialog()
                return

            # 按行分割文本
            lines = input_text.split("\n")

            # 如果有多行，询问用户要绘制哪些行
            if len(lines) > 1:
                # 创建对话框
                dialog = tk.Toplevel(self)
                dialog.title("选择要绘制的行")
                dialog.geometry("600x400")
                dialog.transient(self)
                dialog.grab_set()

                # 创建说明标签
                label = ttk.Label(dialog, text="请选择要绘制曲线的行（可多选）：")
                label.pack(padx=10, pady=10)

                # 创建列表框
                listbox = tk.Listbox(dialog, width=80, height=15, selectmode=tk.MULTIPLE)
                listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

                # 添加行到列表框
                for i, line in enumerate(lines):
                    listbox.insert(tk.END, f"{i+1}: {line}")

                # 创建按钮框架
                button_frame = ttk.Frame(dialog)
                button_frame.pack(fill=tk.X, padx=10, pady=10)

                # 绘制选中行的曲线
                def plot_selected():
                    # 获取选中的索引
                    selection = listbox.curselection()
                    if not selection:
                        messagebox.showwarning("警告", "请至少选择一行")
                        return

                    # 获取选中的行
                    selected_lines = [lines[i] for i in selection]

                    # 关闭对话框
                    dialog.destroy()

                    # 绘制曲线
                    self._show_curve_dialog_with_data(selected_lines)

                # 创建绘制按钮
                plot_button = ttk.Button(button_frame, text="绘制曲线", command=plot_selected)
                plot_button.pack(side=tk.LEFT, padx=5)

                # 创建取消按钮
                cancel_button = ttk.Button(button_frame, text="取消", command=dialog.destroy)
                cancel_button.pack(side=tk.RIGHT, padx=5)

                return

            # 只有一行，直接绘制
            self._show_curve_dialog_with_data(lines)
        except Exception as e:
            messagebox.showerror("错误", f"显示曲线图对话框失败：{str(e)}")

    def _show_curve_dialog_with_data(self, lines):
        """显示带有数据的曲线图对话框"""
        try:
            # 创建对话框
            dialog = tk.Toplevel(self)
            dialog.title("生成曲线")
            dialog.geometry("800x600")
            dialog.transient(self)
            dialog.grab_set()

            # 创建说明标签
            label = ttk.Label(dialog, text="每个十六进制数据点代表一个点位，时间间隔为102.4us")
            label.pack(padx=10, pady=5)

            # 创建输入框框架
            # input_frame = ttk.Frame(dialog)
            # input_frame.pack(fill=tk.X, padx=10, pady=5)

            # 创建输入框
            # entry = ttk.Entry(input_frame, width=50)
            # entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

            # 创建添加按钮
            # add_button = ttk.Button(input_frame, text="添加数据", command=lambda: add_data())
            # add_button.pack(side=tk.RIGHT, padx=5)

            # 创建图形框架
            plot_frame = ttk.Frame(dialog)
            plot_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 导入matplotlib
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
            import numpy as np

            # 创建图形
            fig = plt.Figure(figsize=(8, 6), dpi=100)
            ax = fig.add_subplot(111)
            ax.set_xlabel('时间 (us)')
            ax.set_ylabel('数值')
            ax.set_title('数据曲线')
            ax.grid(True)

            # 创建画布
            canvas = FigureCanvasTkAgg(fig, master=plot_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

            # 添加工具栏支持缩放
            toolbar_frame = ttk.Frame(plot_frame)
            toolbar_frame.pack(side=tk.BOTTOM, fill=tk.X)
            toolbar = NavigationToolbar2Tk(canvas, toolbar_frame)
            toolbar.update()

            # 存储所有曲线数据
            curves_data = []

            # 添加初始数据
            for i, line in enumerate(lines):
                try:
                    # 如果包含 ->，只使用命令部分
                    if "->" in line:
                        data_str = line.split("->")[0].strip()
                    else:
                        data_str = line.strip()

                    # 解析十六进制字符串为字节列表
                    data_list = self.parse_hex_string(data_str)

                    if not data_list:
                        continue

                    # 创建时间轴
                    time_interval = 102.4  # us
                    time_axis = np.arange(0, len(data_list) * time_interval, time_interval)

                    # 选择颜色
                    colors = ['b', 'r', 'g', 'm', 'c', 'y', 'k']
                    color_index = len(curves_data) % len(colors)
                    color = colors[color_index]

                    # 绘制曲线，添加圆点标记
                    line_label = f'数据{len(curves_data)+1}'
                    line, = ax.plot(time_axis, data_list, color=color, label=line_label, marker='o', markersize=4, linestyle='-')

                    # 添加到曲线数据
                    curves_data.append({'data': data_list, 'line': line, 'label': line_label, 'color': color})
                except Exception as e:
                    messagebox.showwarning("警告", f"解析行 {i+1} 失败：{str(e)}")

            # 更新图例
            if curves_data:
                ax.legend()
                canvas.draw()

            # 添加数据函数
            def add_data():
                try:
                    # 获取输入的十六进制字符串
                    hex_string = entry.get().strip()

                    if not hex_string:
                        messagebox.showwarning("警告", "请输入有效的十六进制数据")
                        return

                    # 解析十六进制字符串为字节列表
                    data_list = self.parse_hex_string(hex_string)

                    if not data_list:
                        messagebox.showwarning("警告", "请输入有效的十六进制数据")
                        return

                    # 创建时间轴
                    time_interval = 102.4  # us
                    time_axis = np.arange(0, len(data_list) * time_interval, time_interval)

                    # 选择颜色
                    colors = ['b', 'r', 'g', 'm', 'c', 'y', 'k']
                    color_index = len(curves_data) % len(colors)
                    color = colors[color_index]

                    # 绘制曲线，添加圆点标记
                    line, = ax.plot(time_axis, data_list, color=color, label=f'数据{len(curves_data)+1}', marker='o', markersize=4, linestyle='-')

                    # 添加到曲线数据
                    curves_data.append({'data': data_list, 'line': line, 'label': f'数据{len(curves_data)+1}', 'color': color})

                    # 更新图例
                    ax.legend()

                    # 重绘画布
                    canvas.draw()

                    # 清空输入框
                    entry.delete(0, tk.END)
                except Exception as e:
                    messagebox.showerror("错误", f"添加曲线失败：{str(e)}")

            # 创建按钮框架
            button_frame = ttk.Frame(dialog)
            button_frame.pack(fill=tk.X, padx=10, pady=10)

            # 创建关闭按钮
            close_button = ttk.Button(button_frame, text="关闭", command=dialog.destroy)
            close_button.pack(side=tk.RIGHT, padx=5)

            # 更新状态栏
            self.status_bar.config(text="曲线图对话框已显示")
        except Exception as e:
            messagebox.showerror("错误", f"显示曲线图对话框失败：{str(e)}")

    def _show_empty_curve_dialog(self):
        """显示空的曲线图对话框"""
        try:
            # 创建对话框
            dialog = tk.Toplevel(self)
            dialog.title("生成曲线")
            dialog.geometry("800x600")
            dialog.transient(self)
            dialog.grab_set()

            # 创建说明标签
            label = ttk.Label(dialog, text="请输入要绘制曲线的十六进制数据（每个字节代表一个点位，时间间隔为102.4us）：")
            label.pack(padx=10, pady=5)

            # 创建输入框框架
            # input_frame = ttk.Frame(dialog)
            # input_frame.pack(fill=tk.X, padx=10, pady=5)

            # 创建输入框
            # entry = ttk.Entry(input_frame, width=50)
            # entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

            # 创建添加按钮
            # add_button = ttk.Button(input_frame, text="添加数据", command=lambda: add_data())
            # add_button.pack(side=tk.RIGHT, padx=5)

            # 创建图形框架
            plot_frame = ttk.Frame(dialog)
            plot_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 导入matplotlib
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
            import numpy as np

            # 创建图形
            fig = plt.Figure(figsize=(8, 6), dpi=100)
            ax = fig.add_subplot(111)
            ax.set_xlabel('时间 (us)')
            ax.set_ylabel('数值')
            ax.set_title('数据曲线')
            ax.grid(True)

            # 创建画布
            canvas = FigureCanvasTkAgg(fig, master=plot_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

            # 添加工具栏支持缩放
            toolbar_frame = ttk.Frame(plot_frame)
            toolbar_frame.pack(side=tk.BOTTOM, fill=tk.X)
            toolbar = NavigationToolbar2Tk(canvas, toolbar_frame)
            toolbar.update()

            # 存储所有曲线数据
            curves_data = []

            # 添加数据函数
            # def add_data():
            #     try:
            #         # 获取输入的十六进制字符串
            #         hex_string = entry.get().strip()

            #         if not hex_string:
            #             messagebox.showwarning("警告", "请输入有效的十六进制数据")
            #             return

            #         # 解析十六进制字符串为字节列表
            #         data_list = self.parse_hex_string(hex_string)

            #         if not data_list:
            #             messagebox.showwarning("警告", "请输入有效的十六进制数据")
            #             return

            #         # 创建时间轴
            #         time_interval = 102.4  # us
            #         time_axis = np.arange(0, len(data_list) * time_interval, time_interval)

            #         # 选择颜色
            #         colors = ['b', 'r', 'g', 'm', 'c', 'y', 'k']
            #         color_index = len(curves_data) % len(colors)
            #         color = colors[color_index]

            #         # 绘制曲线
            #         line, = ax.plot(time_axis, data_list, color=color, label=f'数据{len(curves_data)+1}')

            #         # 添加到曲线数据
            #         curves_data.append({'data': data_list, 'line': line, 'label': f'数据{len(curves_data)+1}', 'color': color})

            #         # 更新图例
            #         ax.legend()

            #         # 重绘画布
            #         canvas.draw()

            #         # 清空输入框
            #         entry.delete(0, tk.END)
            #     except Exception as e:
            #         messagebox.showerror("错误", f"添加曲线失败：{str(e)}")

            # 创建按钮框架
            button_frame = ttk.Frame(dialog)
            button_frame.pack(fill=tk.X, padx=10, pady=10)

            # 创建关闭按钮
            close_button = ttk.Button(button_frame, text="关闭", command=dialog.destroy)
            close_button.pack(side=tk.RIGHT, padx=5)

            # 更新状态栏
            self.status_bar.config(text="曲线图对话框已显示")
        except Exception as e:
            messagebox.showerror("错误", f"显示曲线图对话框失败：{str(e)}")

    def show_about(self):
        """显示关于对话框"""
        messagebox.showinfo("关于", "命令解析工具\n\n版本：1.0\n\n作者：zhang9591")

    def show_batch_dialog(self):
        """显示批量解析对话框"""
        # 创建对话框
        dialog = tk.Toplevel(self)
        dialog.title("批量解析")
        dialog.geometry("800x600")
        dialog.transient(self)
        dialog.grab_set()

        # 创建说明标签
        label = ttk.Label(dialog, text="请输入要批量解析的命令和响应，每行一条，格式为：命令 -> 响应")
        label.pack(padx=10, pady=10)

        # 创建文本框
        text = scrolledtext.ScrolledText(dialog, wrap=tk.WORD, width=80, height=20)
        text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建示例标签
        example_label = ttk.Label(dialog, text="示例：\n1. 无0x前缀：\n02 50 FF FF FF FF 7A -> 00 00 06 45 A5 A1 A6\n03 11 00 00 00 00 E8 -> 00 00 05 C0 81 A1 32\n\n2. 带0x前缀：\n0x07 0x00 0x07 0x38 0xA5 0xA1 0x28 -> 0x0F 0x00 0x01 0xBB 0xA7 0xA1 0x3B", foreground="gray")
        example_label.pack(padx=10, pady=5)

        # 创建按钮框架
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        # 创建添加到输入框按钮
        add_button = ttk.Button(button_frame, text="添加到输入框", command=lambda: self.add_to_input(text.get(1.0, tk.END)))
        add_button.pack(side=tk.LEFT, padx=5)

        # 创建解析按钮
        parse_button = ttk.Button(button_frame, text="解析", command=lambda: self.parse_batch_dialog(text.get(1.0, tk.END), dialog))
        parse_button.pack(side=tk.RIGHT, padx=5)

        # 创建取消按钮
        cancel_button = ttk.Button(button_frame, text="取消", command=dialog.destroy)
        cancel_button.pack(side=tk.RIGHT, padx=5)

    def add_to_input(self, text):
        """将批量解析对话框中的文本添加到输入框"""
        # 获取当前输入框内容
        current_text = self.input_text.get(1.0, tk.END).strip()

        # 获取要添加的文本
        add_text = text.strip()

        # 如果当前输入框不为空且要添加的文本不为空，添加换行符
        if current_text and add_text:
            combined_text = f"{current_text}\n{add_text}"
        elif add_text:
            combined_text = add_text
        else:
            combined_text = current_text

        # 设置输入框内容
        self.input_text.delete(1.0, tk.END)
        self.input_text.insert(tk.END, combined_text)

        # 更新状态栏
        self.status_bar.config(text="文本已添加到输入框")

    def parse_batch_dialog(self, text, dialog):
        """解析批量对话框中的命令和响应，并关闭对话框"""
        # 解析文本
        self.parse_batch(text)

        # 关闭对话框
        dialog.destroy()

    def parse_batch(self, text):
        """批量解析命令和响应"""
        # 按行分割文本
        lines = text.strip().split("\n")

        # 更新状态栏
        self.status_bar.config(text=f"批量解析中，共{len(lines)}行...")
        self.update()

        # 成功计数
        success_count = 0

        # 最后一个成功解析的命令和响应
        last_command_result = None
        last_response_result = None

        # 解析每一行
        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 检查是否包含 ->
            if "->" in line:
                # 分割命令和响应
                command_str, response_str = line.split("->", 1)
                command_str = command_str.strip()
                response_str = response_str.strip()

                # 解析命令
                try:
                    command_bytes = self.parse_hex_string(command_str)
                    command_parser = CommandParserFactory.create_parser(command_bytes)
                    command_result = command_parser.parse(command_bytes)

                    # 解析响应
                    response_bytes = self.parse_hex_string(response_str)
                    response_parser = ResponseParserFactory.create_parser(command_result["type"], command_result)
                    response_result = response_parser.parse(response_bytes, command_result)

                    # 添加到历史记录
                    self.history_frame.add_history(
                        command_bytes=command_bytes,
                        response_bytes=response_bytes,
                        command_result=command_result,
                        response_result=response_result
                    )

                    # 更新最后一个成功解析的命令和响应
                    last_command_result = command_result
                    last_response_result = response_result

                    success_count += 1
                except Exception as e:
                    messagebox.showwarning("警告", f"解析失败：{line}\n{str(e)}")
            else:
                # 只有命令，跳过
                messagebox.showwarning("警告", f"跳过格式错误的行：{line}\n应为：命令 -> 响应")

        # 如果有成功解析的命令和响应，显示最后一个
        if last_command_result and last_response_result:
            self.result_frame.set_command_result(last_command_result)
            self.result_frame.set_response_result(last_response_result)

        # 更新状态栏
        self.status_bar.config(text=f"批量解析完成，成功{success_count}条，共{len(lines)}行")

    def parse_hex_string(self, hex_string):
        """解析十六进制字符串为字节列表，支持0x前缀格式"""
        # 处理可能的0x前缀格式
        if "0x" in hex_string.lower():
            # 按空白分割
            parts = hex_string.split()
            bytes_list = []

            for part in parts:
                # 移除0x前缀并转换为整数
                if part.lower().startswith("0x"):
                    bytes_list.append(int(part, 16))
                else:
                    # 尝试直接解析为十六进制
                    try:
                        bytes_list.append(int(part, 16))
                    except ValueError:
                        raise ValueError(f"无效的十六进制值: {part}")

            return bytes_list
        else:
            # 原始处理方式（无0x前缀）
            # 移除所有空白字符
            hex_string = re.sub(r'\s+', '', hex_string)

            # 检查字符串长度是否为偶数
            if len(hex_string) % 2 != 0:
                raise ValueError("十六进制字符串长度必须为偶数")

            # 检查是否包含非十六进制字符
            if not all(c in "0123456789abcdefABCDEF" for c in hex_string):
                raise ValueError("十六进制字符串只能包含0-9、a-f、A-F")

            # 将字符串转换为字节列表
            return [int(hex_string[i:i+2], 16) for i in range(0, len(hex_string), 2)]


def main():
    """主函数"""
    # 设置调试级别
    set_debug_level(DEBUG_LEVEL_INFO)

    # 添加一些示例历史记录
    try:
        # 解析示例命令和响应
        command_bytes = [0x02, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0x7A]
        response_bytes = [0x00, 0x00, 0x06, 0x45, 0xA5, 0xA1, 0xA6]

        # 创建命令解析器
        command_parser = CommandParserFactory.create_parser(command_bytes)

        # 解析命令
        command_result = command_parser.parse(command_bytes)

        # 创建响应解析器
        response_parser = ResponseParserFactory.create_parser(command_result["type"], command_result)

        # 解析响应
        response_result = response_parser.parse(response_bytes, command_result)

        # 添加到历史记录
        history_manager.add_history(
            command_bytes=command_bytes,
            response_bytes=response_bytes,
            command_result=command_result,
            response_result=response_result
        )

        # 保存历史记录
        history_manager.save_history()
    except Exception as e:
        print(f"添加示例历史记录失败：{str(e)}")

    # 创建GUI
    app = CommandAnalyzerGUI()
    app.mainloop()


if __name__ == "__main__":
    main()
