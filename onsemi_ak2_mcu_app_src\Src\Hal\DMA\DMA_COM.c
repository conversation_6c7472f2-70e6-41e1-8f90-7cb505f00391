/***********************************************************************************************************************
* DISCLAIMER
* This software is supplied by Renesas Electronics Corporation and is only 
* intended for use with Renesas products. No other uses are authorized. This 
* software is owned by Renesas Electronics Corporation and is protected under 
* all applicable laws, including copyright laws.
* THIS SOFTWARE IS PROVIDED "AS IS" AND R<PERSON><PERSON>AS MAKES NO WARRANTIES REGARDING 
* THIS SOFTWARE, WHETHER EXPRESS, IMPLIED OR STATUTORY, INCLUDING BUT NOT 
* LIMITED TO WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE 
* AND NON-INFRINGEMENT.  ALL SUCH WARRANTIES ARE EXPRESSLY DISCLAIMED.
* TO THE MAXIMUM EXTENT PERMITTED NOT PROHIBITED BY LAW, NEITHER RENESAS 
* ELECTRONICS CORPORATION NOR ANY OF ITS AFFILIATED COMPANIES SHALL BE LIABLE 
* FOR ANY DIRECT, INDIRECT, SPECIAL, INCIDENTAL OR CONSEQUENTIAL DAMAGES FOR 
* ANY REASON RELATED TO THIS SOFTWARE, EVEN IF RENESAS OR ITS AFFILIATES HAVE 
* BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.
* Renesas reserves the right, without notice, to make changes to this software 
* and to discontinue the availability of this software.  By using this software, 
* you agree to the additional terms and conditions found by accessing the 
* following link:
* http://www.renesas.com/disclaimer
*
* Copyright (C) 2015, 2018 Renesas Electronics Corporation. All rights reserved.
***********************************************************************************************************************/

/***********************************************************************************************************************
* File Name    : r_cg_dmac_user.c
* Version      : Code Generator for RH850/F1K V1.01.02.02 [08 May 2018]
* Device(s)    : R7F701587(LQFP176pin)
* Tool-Chain   : CCRH
* Description  : This file implements device driver for DMA module.
* Creation Date: 2022/4/2
***********************************************************************************************************************/

/***********************************************************************************************************************
Pragma directive
***********************************************************************************************************************/
/* Start user code for pragma. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */

/***********************************************************************************************************************
Includes
***********************************************************************************************************************/
#include "DMADrv.h"
#include "DMA_COM.h"
#include "CSIHDrv.h"
#include "DMACfg.h"
#include "IODrv.h"
#include "SpiCom_Prg.h"
/* Start user code for include. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */
/***********************************************************************************************************************
Global variables and functions
***********************************************************************************************************************/
/* Start user code for global. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */


// #pragma location = "R_DMA_DATA"
// CSIHTxRxTYPE GstrCSIH2TxRxFrame;

// #pragma location = "R_DMA_DATA"
// CSIHTxRxTYPE GstrCSIH3TxRxFrame;
extern CSIHTxRxTYPE GstrCSIH2TxRxFrame;
extern CSIHTxRxTYPE GstrCSIH3TxRxFrame;

extern void DSIMaster0_SPI_CompleteTransfer_cbk(void);
extern void DSIMaster1_SPI_CompleteTransfer_cbk(void);

/***********************************************************************************************************************
* Function Name: r_dmac02_interrupt
* Description  : None
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void DMA1IntCallBack(void)
{
    /* Start user code. Do not edit comment generated here */

	if(DMAC.DCST1&0x80)
	{
		/*Transfer error flag clear*/
		DMAC.DCSTC1 |= 0x80;
	}
	if(DMAC.DCST1&0x10)
	{
		/*Transfer completion flag clear*/
		DMAC.DCSTC1 |= 0x10;
		SpiCom_DSIMaster0_CompleteTransfer_Cbk();
	}
	
	if(DMAC.DCST1&0x01)
	{
		/*Transfer completion flag clear*/
		DMAC.DCSTC1 |= 0x01;
	}
	//stUartTrismit.uart0_tx_busy = 0;
    /* End user code. Do not edit comment generated here */
}

/***********************************************************************************************************************
* Function Name: r_dmac03_interrupt
* Description  : None
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void DMA3IntCallBack(void)
{
    /* Start user code. Do not edit comment generated here */

	if(DMAC.DCST3&0x80)
	{
		/*Transfer error flag clear*/
		DMAC.DCSTC3 |= 0x80;
	}
	if(DMAC.DCST3&0x10)
	{
		/*Transfer completion flag clear*/
		DMAC.DCSTC3 |= 0x10;
		SpiCom_DSIMaster1_CompleteTransfer_Cbk();
	}
	
	if(DMAC.DCST3&0x01)
	{
		/*Transfer completion flag clear*/
		DMAC.DCSTC3 |= 0x01;
	}
	//stUartTrismit.uart0_tx_busy = 0;
    /* End user code. Do not edit comment generated here */
}

/***********************************************************************************************************************
* Function Name: r_dmac05_interrupt
* Description  : None
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void DMA5IntCallBack(void)
{
    /* Start user code. Do not edit comment generated here */

	if(DMAC.DCST5&0x80)
	{
		/*Transfer error flag clear*/
		DMAC.DCSTC5 |= 0x80;
	}
	if(DMAC.DCST5&0x10)
	{
		/*Transfer completion flag clear*/
		DMAC.DCSTC5 |= 0x10;
		SBC_SetDmaTransmitFlg();
	}
	
	if(DMAC.DCST5&0x01)
	{
		/*Transfer completion flag clear*/
		DMAC.DCSTC5 |= 0x01;
	}
	//stUartTrismit.uart0_tx_busy = 0;
    /* End user code. Do not edit comment generated here */
}

/* Start user code for adding. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */

/* Start user code for adding. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */
#pragma location = "R_DMA_DATA"
uint8 buff2[4] = {0x11, 0x22,0x33, 0x44};

#pragma location = "R_DMA_DATA"
uint8 buff3[4] = {0};

void test_spi_communication(void)
{
	#if CSIH_DMA_START_ENABLE
	// R_PORT_ToggleGpioOutput(Port10, 2);
	//CSIH_DRV_MasterSendReceive(buff2, buff3, 4, CSIH0_MASTER);
    CSIH_DRV_MasterSendReceive(buff2, buff3, 4, CSIH2_MASTER);
    //CSIH_DRV_MasterSendReceive(buff3, GstrCSIH3TxRxFrame.CSIHRxDataBuff,72, CSIH3_MASTER);
	
	#else
    R_CSIH3_Master_Receive(GstrCSIH3TxRxFrame.CSIHRxDataBuff, 3, _CSIH_SELECT_CHIP_3);
    R_CSIH3_Master_Send(GstrCSIH3TxRxFrame.CSIHTxDataBuff, 3, _CSIH_SELECT_CHIP_3);

    R_CSIH2_Master_Receive(GstrCSIH2TxRxFrame.CSIHRxDataBuff, 2, _CSIH_SELECT_CHIP_0);
    R_CSIH2_Master_Send(GstrCSIH2TxRxFrame.CSIHTxDataBuff, 2, _CSIH_SELECT_CHIP_0);
	#endif
}
