/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * SnsDisFollow_Int: 
 * Created on: 2023-07-01 16:36
 * Original designer: AntonyFang
 ******************************************************************************/
#ifndef SnsDisFollow_Int_H
#define SnsDisFollow_Int_H

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "SnsRawData_Type.h"
#include "SnsDisFollow_Type.h"


/******************************************************************************
 * Compiler option used to distinguish inclusion done by the owner
 *****************************************************************************/


/******************************************************************************
 * Inclusion of private header files
 *****************************************************************************/



/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
void SnsSigGroupFollowDataPowerOnInit(void);
void SnsSigGroupDisFollowAndOutputHandle(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh);
void SnsSigGroupFollowDataInit(PDCSnsGroupType LeGroup);
void SnsSigGroupChannelDataClear(PDCSnsGroupType LeGroup, PDCSnsChannelType LenuSnsCh);
void SnsSigGroupFollowDataPDC_Sns_Init(PDCSnsGroupType LeGroup);




#endif /* end of SnsDisFollow_Int_H */

