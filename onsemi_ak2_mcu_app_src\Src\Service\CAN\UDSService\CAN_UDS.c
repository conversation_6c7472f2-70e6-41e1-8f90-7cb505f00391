/***************************************************************************
*   版权所有    : 2017 深圳市豪恩汽车电子装备有限公司
*   项目名称    : TATA Furuto AVM
*   M C U       : R5F10BBG
*   编译环境    : IAR
*   文件名称    : CAN_UDS.c
*   其它说明    : CAN诊断服务层
*   当前版本    : V0.1
*   作    者  : 20460
*   完成日期    :
*   内容摘要    :
*   修改记录1   :
*   修改日期    :
*   版 本 号 :
*   修 改 人 :
*   修改内容    :
*   修改记录2   :

*****************************************************************************/
/****************************************************************************
* Includes 
******************************************************************************/
#include <stdlib.h>
#include "CAN_UDS.h"
#include "TP_Config.h"
#include "TP_Lib.h"
#include "CAN_COM.h"
#include "DTCService.h"
#include "DTCRecordManage.h"
#include "DTC_Cfg.h"
#include "DID.h"
#include "EELHal.h"
#include "TimerManage.h"
#include "CAN_IL.h"
#include "PowerSingalManage.h"
#include "DID_Calibration.h"
#include "DTCMonitor.h"
#include "CAN_AppSignalManage.h"
#include "Elmos_524_17_SnsCtrl.h"
#include "Did_Cali_Cbk.h"
/***************************************************************************** 
* Global objects 
******************************************************************************/
UdsLongRequestStr GsLongRequest;    /* 78响应数据处理 */
UdsServiceStr GsUdsService;
uint8 GcUdsTxDataBuff[500];
uint8 GcReceive_Contect_PC_flg = 0;    /*接收到上位机通讯连接标志0：断开连接；1：连接*/
uint8 GcUdsData[500];               /* 保存写入的DID的数据 */
uint8 GcErrorKeyCnt = 0;
uint32 GdCalcKey;/*客户解锁秘钥*/
uint32 GdLH_SecurityCalcKey;/*豪恩内部解锁秘钥*/
uint16 Gu16RandSeedCnt = 0;
#define SECURITY_NEED               /*测试使用，注释掉后可以不用正确的密钥解锁。*/

#if (CALIBRATION_EN == STD_ON)
#define CRC8_POLYNOMIAL 0x07
uint8_t crc8_table[256];
#endif

static uint8 GcWaitKeyFlag = FALSE;
volatile uint8 CheckProgrammingPreConditionFlag = FALSE;

extern uint8 GuGainDataBuf[24];
/* Calculate random seed, four bytes, all 0x0 and  all 0xFF not allowed; */
uint32 GetRandSeed(void)
{
    uint32 LdSeed;
    uint8 LcSeedHigh24, LcSeedHigh16, LcSeedLow8, LcSeedLow0;
    /* recalculate if all zeros or all FFs occur */
    /*The following algorithm is traversed to make sure that there are no all-zero or all-FF outputs, so there is no need to add while judgement*/
    //do
    //{
        LcSeedLow0 = (uint8)((Gu16RandSeedCnt >> 8u) & 0x00ffu);
        LcSeedLow8 = (uint8)(Gu16RandSeedCnt & 0x00ffu);
        LcSeedHigh16 = (uint8)(((uint16)(~Gu16RandSeedCnt) >> 4u) & 0x00ffu);
        LcSeedHigh24 = (uint8)(((uint16)(~Gu16RandSeedCnt) >> 6u) & 0x00ffu);
    //} while (((LcSeedLow0 | LcSeedLow8 | LcSeedHigh16 | LcSeedHigh24) == 0u)
    //    || ((LcSeedLow0 & LcSeedLow8 & LcSeedHigh16 & LcSeedHigh24) == 0xFFu));
    /* Output four-byte seed */
    LdSeed = (uint32)((((uint32)LcSeedHigh24) << 24) | (((uint32)LcSeedHigh16) << 16)\
        | (((uint32)LcSeedLow8) << 8) | ((uint32)LcSeedLow0));

    return LdSeed;
}
/*********************************************************************
 * 函数名称:   generate_crc8_table
 *
 * 功能描述:  生成CRC8查表
 *
 * 输入参数:
 *
 * 输出参数:
 *
 * 其它说明:
 *
 * 修改日期     版本号       修改人       修改内容
 *
 ********************************************************************/
#if (CALIBRATION_EN == STD_ON)
void generate_crc8_table(void)
{
    for (uint16_t i = 0; i < 256; ++i)
    {
        uint8_t crc = i;
        for (int j = 0; j < 8; ++j)
        {
            if (crc & 1)
            {
                crc = (crc >> 1) ^ CRC8_POLYNOMIAL;
            }
            else
            {
                crc >>= 1;
            }
        }
        crc8_table[i] = crc;
    }
}
/*********************************************************************
 * 函数名称:   calculate_crc8
 *
 * 功能描述:  计算CRC8
 *
 * 输入参数:
 *
 * 输出参数:
 *
 * 其它说明:
 *
 * 修改日期     版本号       修改人       修改内容
 *
 ********************************************************************/
uint8_t calculate_crc8(const uint8_t* data, size_t len)
{
    uint8_t crc = 0;

    for (size_t i = 0; i < len; ++i)
    {
        crc = crc8_table[crc ^ data[i]];
    }

    return crc;
}
#endif
/*********************************************************************
 * 函数名称:   SeedToKey
 *
 * 功能描述:  种子生成安全密钥
 *
 * 输入参数:  seed,待发送种子
 *
 * 输出参数:  LcKey,生成的安全密钥
 *
 * 其它说明:  长城F7安全算法，掩码固定为 41 20 46 53(hex)
 *
 * 修改日期     版本号       修改人       修改内容
 *
 ********************************************************************/

/*Lever 1*/
uint32 SeedKey_Calc( uint32 Seed)
//定义计算 Key 的功能函数，包括变量 Seed 和 EncryptConstant 为四个字节
{
    uint32 wSubSeed; //定义变量 wSubSeed-四个字节
    uint32 wMiddle; //定义变量 wMiddle-四个字节
    uint32 wLastBit; //定义变量 wLastBit-四个字节
    uint32 wLeft31Bits; //定义变量 wLeft31Bits-四个字节
    uint32 Key; //定义变量 Key-四个字节
    uint32 counter; //定义变量 counter-四个字节
    uint8 DB1,DB2,DB3; //定义变量-都为一个字节
    uint32 EncryptConstant = 0x20001202;
    uint32 i;
    wSubSeed = Seed; //将种子的值赋值给变量 wSubSeed

	/*多项式不变的情况下middle恒为0，所以上面一段代码，优化为底下这行代码*/
	wMiddle = (uint8)(Seed & 0x000000ff); //种子与值进行位与运算
	
    DB1 = (uint8) ((EncryptConstant & 0x000007F8) >> 3); //掩码与值位与运算，保留低字节
    DB2 = (uint8) (((EncryptConstant & 0x7F800000) >> 23)^0xA5); //掩码与值进行位运算后右移 23位，再与 0xA5 进行异或运算
    DB3 = (uint8) (((EncryptConstant & 0x003FC000) >> 14)^0x5A); //掩码与值进行位运算后右移 14位，再与 0x5A 进行异或运算
    counter = (uint32)(((wMiddle ^ DB1) & DB2) + DB3 );
    // wMiddle 与 DB1 异或后再与 DB2 位运算，再与 DB3 相加后赋值给 counter
    for ( i = 0; i < counter; i++ ) //fou 语句循环
    {
        wMiddle = ((wSubSeed & 0x20000000)/0x20000000) ^ ((wSubSeed &
        0x01000000)/0x01000000) ^ ((wSubSeed & 0x2000)/0x2000) ^ ((wSubSeed &
        0x08)/0x08);
        wLastBit = (wMiddle & 0x00000001) ;
        wSubSeed = (uint32)(wSubSeed << 1);
        wLeft31Bits = (uint32)(wSubSeed & 0xFFFFFFFE) ;
        wSubSeed = (uint32)(wLeft31Bits | wLastBit);
    } //fou 循环中，按照循环语句算出 wSubSeed， 直到 i>= counter 停止

	wLeft31Bits=((wSubSeed & 0x00FF0000)>>16) | ((wSubSeed & 0xFF000000)>>16) | ((wSubSeed & 0x000000FF)<<24) | ((wSubSeed & 0x0000FF00)<<8);
    Key = wLeft31Bits ^ EncryptConstant; //最后计算出的 wLeft31Bits 与掩码位异或得到最终 Key
    return( Key ); //返回 Key 值
}
/*********************************************************************
 * 函数名称:  CAN_UDSInit
 *
 * 功能描述:  UDS初始化
 *
 * 入口参数:  无
 *
 * 输出参数:  无
 *
 * 其它说明:  无
 *
 * 修改日期  版本号修改人            修改内容
 *
 ********************************************************************/
void CAN_UDSInit(void)
{
    uint8 LcFrameIdx ;

    GsUdsService.UdsAddrMode = UDS_ADDR_PHYSICAL;/* Initial state is physically addressed */
    GsUdsService.DTCSettingType = DTC_SETTING_ON;/* Allow setting of DTC */
    GsUdsService.SessionMode =UDS_SESSION_DEFAULT;/* Initially in default session mode */
    GsUdsService.KeepSessionTimeCnt = 0;/* Maintain session mode timer is 0*/
    GsUdsService.SecurityLevel = SAFE_LEVEL_0;/*Security level set to L0 lockout status */

    GsUdsService.UDSTxLen = 0;/* Diagnostic response data length set to 0 */
    GsUdsService.pUDSTxBuff = GcUdsTxDataBuff;/*mapping send buffer*/

    GsLongRequest.m_eState = UDS_LONG_REQ_OFF;       /* Initialisation 78 data request */
    GsLongRequest.CycleTimer = 0U;
    GsLongRequest.GcNRC78Event = NRC78_NONE;
    GsLongRequest.GcSuppresPosRspMsgIndicationBit = UDS_FALSE;
    CheckProgrammingPreConditionFlag = FALSE;

    if(GcErrorKeyCnt == 3)/* Initialise security access error counters */
    {
        if(GsUdsService.AccessLockTime == 0u)/* Recount if just powered up */
        {
            GsUdsService.AccessLockTime = 10000;
        }
        else{}/* otherwise the count is not initialised */
    }
    else    /* If the error count is not equal to the limit, initialise the access delay to zero directly */
    {
        GsUdsService.AccessLockTime = 0u;
    }
    /* Enable sending and receiving of all messages */
    for(LcFrameIdx = 0;LcFrameIdx < RXANDTXFRAMEMAXNUM;LcFrameIdx++)
    {
        NCS_RESET_BIT(GcFrameStatus[LcFrameIdx],COM_FRAME_STATUS_RX_DISABLE,uint8);
        NCS_RESET_BIT(GcFrameStatus[LcFrameIdx],COM_FRAME_STATUS_TX_DISABLE,uint8);
    }
}
/*********************************************************************
 * 函数名称:  UDS_NegativeAnswer
 *
 * 功能描述:  UDS处理否定响应
 *
 * 入口参数:  否定响应码
 *
 * 输出参数:  否定响应的数据长度，以及数据
 *
 * 其它说明:  无
 *
 * 修改日期  版本号修改人            修改内容
 *
 ********************************************************************/
uint8 UDS_NegativeAnswer (uint16 *pLwLen,uint8 *pUdsData, uint8 NRCStatus)
{
    uint8 LcUdsBoolean = UDS_TRUE;

    if ((pLwLen == NULL) || (pUdsData == NULL))
    {
        LcUdsBoolean = UDS_FALSE;
    }
    else
    {
        if (GsUdsService.UdsAddrMode == UDS_ADDR_FUNCTIONAL) /* Under Functional Addressing */        
        {
            switch(NRCStatus)
            {
                case UDS_NRC_31:     /* Functional address 0x31, 0x11,0x12,0x7E, 0x7F  Negative response is no answer */
                case UDS_NRC_11:
                case UDS_NRC_12:
                case UDS_NRC_7E:
                case UDS_NRC_7F:

                {
                    GsUdsService.UDSTxLen = 0U;/*Negate the data length of the response*/
                    break;
                }
                default:/* Other responses granted */
                {
                    GsUdsService.pUDSTxBuff[0] = UDS_NRC;
                    GsUdsService.pUDSTxBuff[1] = pUdsData[0];   /* UDS service functions */
                    GsUdsService.pUDSTxBuff[2] = NRCStatus;/*Negate the data Status*/
                    GsUdsService.UDSTxLen = UDS_LEN_NRC;/*Negate the data length of the response*/
                    break;
                }
            }
        }
        else/* Physical addressing gives an answer */
        {
            GsUdsService.pUDSTxBuff[0] = UDS_NRC;
            GsUdsService.pUDSTxBuff[1] = pUdsData[0];           /* UDS service functions */
            GsUdsService.pUDSTxBuff[2] = NRCStatus;/*Negate the data Status*/
            GsUdsService.UDSTxLen = UDS_LEN_NRC;/*Negate the data length of the response*/
        }
    }
    return LcUdsBoolean;
}
/*********************************************************************
 * 函数名称:  UDS_LongRequestEnd
 *
 * 功能描述:  完成服务处理，并做出响应
 *
 * 入口参数:  否定响应码
 *
 * 输出参数:  否定响应的数据长度，以及数据
 *
 * 其它说明:  无
 *
 * 修改日期  版本号修改人            修改内容
 *
 ********************************************************************/
void UDS_LongRequestEnd (uint16 LwLen,uint8 *pUdsData, uint8 LcRespondStatus)
{
    if ((GsLongRequest.m_eState == UDS_LONG_REQ_ON) && (pUdsData != NULL))/*78响应状态为ON，且应答数据不为空*/
    {
        if(LcRespondStatus==UDS_ACK)/* Positive response */
        {
            if (GsLongRequest.GcSuppresPosRspMsgIndicationBit == UDS_TRUE)/* Affirmative response inhibit bit is true, affirmative response does not require an answer */
            {
                LwLen = 0U; /* Affirmative response inhibit bit is true, Positive response does not require an answer */
            }
            else
            {
                pUdsData[0] += UDS_ACK; /* Affirmative response inhibit bit is false, Positive response  */
            }
        }
        else
        {
            (void)UDS_NegativeAnswer (&LwLen, pUdsData, LcRespondStatus);/* Negative response */
        }

        GsLongRequest.m_eState = UDS_LONG_REQ_END;/* 78 response End*/
        GsLongRequest.GcNRC78Event = NRC78_NONE;/* 78 response event status set to none*/

        UDS_TP_RespondManage(LcRespondStatus,LwLen,pUdsData);  /* Call TP library function to send response */
    }
}
/*********************************************************************
 * 函数名称:  NRC78_RespondManage
 *
 * 功能描述:  NRC78响应管理
 *
 * 入口参数:  无
 *
 * 输出参数:  无
 *
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 *
 ********************************************************************/
void NRC78_RespondManage(void)
{
    switch (GsLongRequest.m_eState)
    {
        case UDS_LONG_REQ_END:                                /* NRC78 busy service processing completed */
        {
            GsLongRequest.m_eState = UDS_LONG_REQ_OFF;        /* Diagnostic services have been processed, stopping 78 busy response processing */

            GsUdsService.KeepSessionTimeCnt = (uint16)UDS_SESS_TIMEOUT;  /* Overloaded diagnostic session mode freshness time */

            break;
        }
        case  UDS_LONG_REQ_ON:
        {
            if(GsLongRequest.CycleTimer == 0U)                      /* Determine if a 78 busy response needs to be sent to ensure diagnostics are online */
            {
                GsUdsService.KeepSessionTimeCnt = (uint16)UDS_SESS_TIMEOUT;     /* Overloaded diagnostic session mode freshness time */

                GsLongRequest.CycleTimer = (UDS_P2STAR);             /* Overloaded NRC78 response time, period 3S */

                UDS_TP_RespondManage(UDS_NRC_78,3,GsLongRequest.m_aubDiagNack78Data); /* Call TP library function to send busy response */
            }
            else/*timer not reach*/
            {
                GsLongRequest.CycleTimer --;/*78 respond timer -- */
            }
            break;
        }
        default:                 
        {
            break;       /* There are currently no 78 requests */
        }
    }
}

/*********************************************************************
 * 函数名称:  UDS_Manage
 *
 * 功能描述:  ①处理诊断请求服务因长时间执行过程中响应NRC78
 *            ②处理诊断请求服务，执行完成之后的响应处理
 *            ③诊断在线的管理
 *
 * 入口参数:  无
 *
 * 输出参数:  无
 *
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 *
 ********************************************************************/
void UDS_Manage (void)
{
    uint16 LwBckAccessLockTime;
    static uint32 Ld_Last = 0;
    uint32 Ld_Prresent = GetSystemTimeCnt_Ms();/*get system timer*/
    uint16 Ld_TimeLen = 0;
    uint8 Lu8HisErrCnt[1]={0};

    if(Ld_Prresent >= Ld_Last)/* Calculate the difference between the last run time and the current run time (ms ）*/
    {
        Ld_TimeLen = (uint16)(Ld_Prresent- Ld_Last);/*offset timer*/
    }
    else
    {
        Ld_TimeLen = (uint16)(0xFFFFFFFF + Ld_Prresent- Ld_Last);/*offset timer*/
    }
    
    Ld_Last = Ld_Prresent;/*updata timer*/

    NRC78_RespondManage();/*78 respond process*/

    if(GsUdsService.SessionMode != UDS_SESSION_DEFAULT)       /* Maintain diagnostic session mode online management */
    {
        if(GsUdsService.KeepSessionTimeCnt == 0U)/*time out */
        {
            LwBckAccessLockTime =GsUdsService.AccessLockTime ; /* TP layer timeout, return to default mode should not zero out the timer for locking 27 services */

            CAN_UDSInit();/*initial CAN UDS*/

            GsUdsService.AccessLockTime = LwBckAccessLockTime;/*Keep he timer for locking 27 services*/
        }
        else
        {
            if(GsUdsService.KeepSessionTimeCnt > Ld_TimeLen)/*Maintain diagnostic session timer */
            {
                GsUdsService.KeepSessionTimeCnt -= Ld_TimeLen;/*updata Keep Session Timer*/
            }
            else
            {
                GsUdsService.KeepSessionTimeCnt = 0;/*time out set to 0*/
            }

        }
    }
    
    if(GsUdsService.AccessLockTime > Ld_TimeLen)/*the timer for locking 27 services not reach*/
    {
        GsUdsService.AccessLockTime -= Ld_TimeLen;/*updata this Timer*/
    }
    else
    {
        GsUdsService.AccessLockTime =0;/*time out set to 0*/
        if(GcErrorKeyCnt == 3)            /* Minus 1 if 10s has elapsed and the number of faults is within the limit */
        {
            GcErrorKeyCnt = 2;
            /*read error cnt*/
            EELReadData(&Lu8HisErrCnt[0], ERRORKEYCNT_ADDRESS, ERRORKEYCNT_EEPROM_LEN);
            if(Lu8HisErrCnt[0] != GcErrorKeyCnt)/*check this cnt */
            {
                Lu8HisErrCnt[0] = GcErrorKeyCnt;
                /* Storing security access error counters */
                (void)EELWriteDataImmediate(&Lu8HisErrCnt[0], ERRORKEYCNT_ADDRESS, ERRORKEYCNT_EEPROM_LEN);
            }
        }

    }
}
/*********************************************************************
 * 函数名称:  UDS_StartDiagnosticSession
 *
 * 功能描述:  开启诊断会话
 *
 * 输入参数:  请求报文的长度和首地址
 *
 * 输出参数:  无
 *
 * 其它说明:
 *
 * 修改日期     版本号       修改人       修改内容
 *
 ********************************************************************/
uint8 UDS_StartDiagnosticSession(uint16 *pLwLen, uint8 *pUdsData )
{
    uint8 LcUdsStatus;
    uint8 NRC22_Flag = 0;
    Car_SpeedType_Phy LfCar_Speed_phy;
    ReadSysSignal_Car_Speed_phy(&LfCar_Speed_phy);/*get speed signals  */
    Car_SpeedVildType LvCar_SpeedVild;
    ReadSysSignal_Car_SpeedVild(&LvCar_SpeedVild);/*get valid speed signals */
    /*If the vehicle speed message is prohibited from being received, it passes detection by default*/
    if (NCS_TST_BIT_SET(GcFrameStatus[ID096Idx], COM_FRAME_STATUS_RX_DISABLE, uint8))
    {
        /* Checksum pass*/
        NRC22_Flag = 0;
    }
    else/*If the speed message is allowed to be received, the specific signal needs to be detected*/
    {
        if (((pUdsData[1] & 0x7F) == 0x02) && ((LfCar_Speed_phy > 2.0) || (1 == LvCar_SpeedVild)))
        {
            NRC22_Flag = 1;
        }
        else
        {
            /* Checksum pass*/
            NRC22_Flag = 0;
        }
    }
    if(*pLwLen < 2)/*10  service  length less than 2,respond NRC13*/
    {
        LcUdsStatus = UDS_NRC_13;
    }
    else if(((pUdsData[1]&0x7F) != 0x01)&&((pUdsData[1]&0x7F) != 0x02)&&((pUdsData[1]&0x7F) != 0x03))/*10  service  SubID not equle 01,02,03,respond NRC12*/
    {
        LcUdsStatus = UDS_NRC_12;/*respond NRC12*/
    }
    else if(((pUdsData[1]&0x7F) == 0x02)&&(GsUdsService.SessionMode != UDS_SESSION_EXTENDED))/*10 02 not in extend session,respond NRC7E*/
    {
        LcUdsStatus = UDS_NRC_7E;/*respond NRC7E*/
    }
    else if(((pUdsData[1]&0x7F) == 0x02)&&(GsUdsService.UdsAddrMode != UDS_ADDR_PHYSICAL))/*10 02 not in Physical addressing,respond NRC7E*/
    {
        LcUdsStatus = UDS_NRC_7E;/*respond NRC7E*/
    }
    else if(*pLwLen != 2)/*10  service  length more than 2,respond NRC13*/
    {
        LcUdsStatus = UDS_NRC_13;/*respond NRC13*/
    }
    else if (((pUdsData[1] & 0x7F) == 0x02) &&((1==NRC22_Flag)||(CheckProgrammingPreConditionFlag == FALSE)))// ((LfCar_Speed_phy > 2.0 ) || (1 == LvCar_SpeedVild )|| (CheckProgrammingPreConditionFlag == FALSE)))
    {   
        LcUdsStatus = UDS_NRC_22;/*respond NRC22*/
    }
    else
    {
        if((pUdsData[1]&0x7F) == 0x01)/*10 01initial CAN UDS*/
        {
            CAN_UDSInit();
        }
        else if((pUdsData[1]&0x7F) == 0x02)/*10 02 Reinitialise time parameter */
        {
            GsUdsService.SessionMode = UDS_SESSION_PROGRAMMING;/*Switch to program session */
            GsUdsService.KeepSessionTimeCnt = (uint16)UDS_SESS_TIMEOUT;  /* Reinitialise S3service's time parameter */
            GsLongRequest.GcNRC78Event = NRC78_PROGRAMSEESION;/*Active 78 respond event*/
            GsUdsService.SecurityLevel = SAFE_LEVEL_0;  /* Re-lock 27 access */
            LcUdsStatus = UDS_NRC_78;/*Respond NRC78*/
            return LcUdsStatus;         
        }
        else if((pUdsData[1]&0x7F) == 0x03)
        {
            GsUdsService.SessionMode = UDS_SESSION_EXTENDED;/*Switch to extern session */
            GsUdsService.KeepSessionTimeCnt = (uint16) UDS_SESS_TIMEOUT; /* Reinitialise S3service's time parameter */
            GsUdsService.SecurityLevel = SAFE_LEVEL_0; /* Re-lock 27 access */
            GcWaitKeyFlag = FALSE;  /* Re-entry to extension services requires clearing of 27 01 seed request status */
            CheckProgrammingPreConditionFlag = FALSE;/* Reinitialise precondition status */
        }      
        if((pUdsData[1]&0x80) == 0x00)/* Uninhibited response*/
        {
            GsUdsService.UDSTxLen = 6;
            GsUdsService.pUDSTxBuff[0] = pUdsData[0] + UDS_ACK;
            GsUdsService.pUDSTxBuff[1] = pUdsData[1];
            GsUdsService.pUDSTxBuff[2] = (uint8) ((uint16)(UDS_P2CAN_Server) >> 8U);         /*P2CAN_Server_max (MSByte) */
            GsUdsService.pUDSTxBuff[3] = (uint8) (UDS_P2CAN_Server);                        /*P2CAN_Server_max (LSByte) */
            GsUdsService.pUDSTxBuff[4] = (uint8) ((uint16)((UDS_P2_78_CAN_Server/10)) >> 8U);     /*P2*CAN_Server_max (MSByte)*/
            GsUdsService.pUDSTxBuff[5] = (uint8) (UDS_P2_78_CAN_Server/10);                    /*P2*CAN_Server_max (LSByte)*/
        }
        else /*inhibited response*/
        {
            GsUdsService.UDSTxLen = 0;/*set len to 0*/
        }
        LcUdsStatus = UDS_ACK;

    }
    return LcUdsStatus;
}
/*********************************************************************
 * 函数名称:  UDS_ECUResetService
 *
 * 功能描述:  复位服务
 *
 * 输入参数:  请求报文的长度和首地址
 *
 * 输出参数:  无
 *
 * 其它说明:
 *
 * 修改日期     版本号       修改人       修改内容
 *
 ********************************************************************/
uint8 UDS_ECUResetService(uint16 *pLwLen, uint8 *pUdsData )
{
    uint8 LcUdsStatus;
    Car_SpeedType_Phy LfCar_Speed_phy;
    Car_SpeedVildType LvCar_SpeedVild;
    ReadSysSignal_Car_SpeedVild(&LvCar_SpeedVild);/*get valid speed signals */
    ReadSysSignal_Car_Speed_phy(&LfCar_Speed_phy);/*get speed signals  */
    if(*pLwLen < 2)/*11 service  length less than 2,respond NRC13*/
    {
        LcUdsStatus = UDS_NRC_13;
    }
    else if((pUdsData[1] & 0x7F) != 0x01)/*11  service  SubID not equle 01,respond NRC12*/
    {
        LcUdsStatus = UDS_NRC_12;
    }
    else if(*pLwLen != 2)/*11  service  length more than 2,respond NRC13*/
    {
        LcUdsStatus = UDS_NRC_13;
    }
    else if((LfCar_Speed_phy > 2.0)||(1 == LvCar_SpeedVild)) /*11 01 not in normal environment,respond NRC22*/
    {
        LcUdsStatus = UDS_NRC_22;
    }    
    else
    {
        if((pUdsData[1] & 0x80) == 0x00)/* Uninhibited response*/
        {
            GsUdsService.UDSTxLen = 2;
            GsUdsService.pUDSTxBuff[0] = pUdsData[0] + UDS_ACK;
            GsUdsService.pUDSTxBuff[1] = pUdsData[1];
        }
        else /*inhibited response*/
        {
            GsUdsService.UDSTxLen = 0;/*set len to 0*/
        }
        GsLongRequest.GcNRC78Event = NRC78_ECURESET;/*Active 78 respond event*/
        LcUdsStatus = UDS_ACK;  /*Respond passive respond*/
    }
    return LcUdsStatus;
}
/*********************************************************************
 * 函数名称:  UDS_ClearDiagnosticInformation
 *
 * 功能描述:  清除故障码服务
 *
 * 输入参数:  请求报文地址和长度
 *
 * 输出参数:  无
 *
 * 其它说明:
 *
 * 修改日期     版本号       修改人       修改内容
 *
 ********************************************************************/
uint8 UDS_ClearDiagnosticInformation(uint16 *pLwLen, uint8 *pUdsData)
{
    uint32 LdGroupOfDTC;
    uint8 LcUdsStatus;
    uint8 LcI;
    LdGroupOfDTC = (uint32)((((uint32)pUdsData[1])<<16)|(((uint32)pUdsData[2])<<8)|(uint32)pUdsData[3]);/*get DTC group */

    if(*pLwLen != 4)/*14 service  length not equle 4,respond NRC13*/
    {
        LcUdsStatus = UDS_NRC_13;/*respond NRC13*/
    }
    else if(LdGroupOfDTC != 0xFFFFFF)/* DTC group not equle 0xFFFFFF*/
    {
        LcUdsStatus = UDS_NRC_31;/*respond NRC31*/
    }        
    else
    {   
        for(LcI = 0;LcI < *pLwLen;LcI++)
        {
            GcUdsData[LcI] = pUdsData[LcI];
        }
        GsLongRequest.GcNRC78Event = NRC78_CLEARDTCINFO; /*Active 78 respond event*/
        LcUdsStatus = UDS_NRC_78;   /*Respond NRC78*/       
    }
    return LcUdsStatus;
}
/*********************************************************************
 * 函数名称:  UDS_ReadDiagnosticInformation
 *
 * 功能描述:  读取故障诊断信息
 *
 * 输入参数:  请求报文的长度和首地址
 *
 * 输出参数:  无
 *
 * 其它说明:
 *
 * 修改日期     版本号       修改人       修改内容
 *
 ********************************************************************/
uint8 UDS_ReadDiagnosticInformation(uint16 *pLwLen, uint8 *pUdsData )
{
    uint32 LdDTCCode;
    uint8 LcUdsStatus;
    uint8 LcDTCStatusMask;
    uint8 LcSnapshotNum;
    uint8 LcExtendrecordNum;    
    uint16 LwDTCCounter;

    if(*pLwLen == 6)/* The DTC code is calculated when the request length is right */
    {
        LdDTCCode = (uint32)(((uint32)pUdsData[2]<<16)|((uint32)pUdsData[3]<<8)|((uint32)pUdsData[4]));/*calculate DTC code*/
    }
    if((*pLwLen) < 2)/* When the length is 1 (01 19 04 FF FF FF FF FF 00), the sub-service ID can not be received, then it should be judged as NRC13 instead of NRC12*/
    {
        LcUdsStatus = UDS_NRC_13;/*respond NRC13 */
    }    
    else if((((pUdsData[1] ==0x01)||(pUdsData[1] == 0x81)||(pUdsData[1] == 0x02)||(pUdsData[1] == 0x82))&&((*pLwLen) < 3))\
            ||(((pUdsData[1] ==0x04)||(pUdsData[1] == 0x84)||(pUdsData[1] == 0x06)||(pUdsData[1] == 0x86))&&((*pLwLen) < 6))\
            ||((pUdsData[1] == 0x0A)&&((*pLwLen) < 2)))/*check subID's min length*/
    {
        LcUdsStatus = UDS_NRC_13;/*respond NRC13 */
    }    
    else if((pUdsData[1] != 0x01)&&(pUdsData[1] != 0x02)&&(pUdsData[1] != 0x04)\
        &&(pUdsData[1] != 0x06)&&(pUdsData[1] != 0x0A))/*check sunID*/
    {
        LcUdsStatus = UDS_NRC_12;/*respond NRC12 */
    }
    else if((((pUdsData[1] ==0x01)||(pUdsData[1] == 0x02))&&((*pLwLen) != 3))\
            ||(((pUdsData[1] ==0x04)||(pUdsData[1] == 0x06))&&((*pLwLen) != 6))\
            ||((pUdsData[1] == 0x0A)&&((*pLwLen) != 2))||((*pLwLen!=2)&&(*pLwLen!=3)&&(*pLwLen!=6)))/*check subID's max length*/
    {
        LcUdsStatus = UDS_NRC_13;/*respond NRC13*/
    }
    else if((((pUdsData[1]&0x7F) == 0x04)||((pUdsData[1]&0x7F) == 0x06))&&(DTCNumberJudge(LdDTCCode) == 0xFF))/*check mask as subID is 04or06*/
    {
        LcUdsStatus = UDS_NRC_31;/*respond NRC31*/
    }
    else
    {
        if(pUdsData[1] == 0x01)                 /*reportNumberOfDTCByStatusMask*/
        {
            LcDTCStatusMask = pUdsData[2];      /*GcDTCStatusMask―DTC状态掩码*/

            LwDTCCounter = ReportNumberOfDTCByStatusMask(LcDTCStatusMask);
            
            GsUdsService.pUDSTxBuff[0] = pUdsData[0] + UDS_ACK;
            GsUdsService.pUDSTxBuff[1] = 0x01;
            GsUdsService.pUDSTxBuff[2] = 0x09;                /*DTCStatusAvailabilityMask支持0x09*/
            GsUdsService.pUDSTxBuff[3] = 0x01;                /*DTCFormat = ISO_14229-1_DTCFormat*/
            GsUdsService.pUDSTxBuff[4] = (uint8)(LwDTCCounter>>8);
            GsUdsService.pUDSTxBuff[5] = (uint8)(LwDTCCounter);
            GsUdsService.UDSTxLen = 6;
            LcUdsStatus = UDS_ACK;
        } 
        else if(pUdsData[1] == 0x02)               /*reportDTCByStatusMask*/
        {
            LcDTCStatusMask = pUdsData[2];         /*GcDTCStatusMask―DTC状态掩码*/

            GsUdsService.pUDSTxBuff[0] = pUdsData[0] + UDS_ACK;
            GsUdsService.pUDSTxBuff[1] = 0x02;
            GsUdsService.pUDSTxBuff[2] = 0x09;    /*DTCStatusAvailabilityMask,支持0x09*/

            ReportDTCByStatusMask(LcDTCStatusMask,&LwDTCCounter,&GsUdsService.pUDSTxBuff[3]);

            GsUdsService.UDSTxLen = (LwDTCCounter+3);     
            LcUdsStatus = UDS_ACK;
        } 
        else if(pUdsData[1] == 0x04)               /*reportDTCSnapshotRecord-ByDTCNumber*/
        {
            if((pUdsData[5] != 0x01) && (pUdsData[5] != 0x02) && (pUdsData[5] != 0xFF))/*check support snapshot group*/
            {
                LcUdsStatus = UDS_NRC_31;
            }
            else/*check pass*/
            {
                LcSnapshotNum = pUdsData[5];
                GsUdsService.pUDSTxBuff[0] = pUdsData[0]+UDS_ACK;
                GsUdsService.pUDSTxBuff[1] = 0x04;   
                /*Report the corresponding snapshot information*/
                ReportDTCSnapshotRecordByDTCNumber(LcSnapshotNum,LdDTCCode,&LwDTCCounter,&GsUdsService.pUDSTxBuff[2]);

                GsUdsService.UDSTxLen = (LwDTCCounter + 2);/*Total length of reported data*/
                LcUdsStatus = UDS_ACK;
            }
        }
        else if(pUdsData[1] == 0x06)               /*reportDTCExtendedDataRecord-ByDTCNumber*/
        {
            if((pUdsData[5] != 0x01) && (pUdsData[5] != 0x02) && (pUdsData[5] != 0xFF))/*Check supported sanpshot groups*/
            {
                LcUdsStatus = UDS_NRC_31;
            }
            else/*check pass*/
            {
                LcExtendrecordNum = pUdsData[5];
                GsUdsService.pUDSTxBuff[0] = pUdsData[0]+UDS_ACK;
                GsUdsService.pUDSTxBuff[1] = 0x06;
                /*Report the corresponding extend information*/
                ReportDTCExtendedDataByDTCNumber(LcExtendrecordNum,LdDTCCode,&LwDTCCounter,&GsUdsService.pUDSTxBuff[2]);

                GsUdsService.UDSTxLen = (LwDTCCounter + 2);/*Total length of reported data*/
                LcUdsStatus = UDS_ACK;
            }      
        }
        else if(pUdsData[1] == 0x0A)                 /*reportSupportedDTC*/
        {
            LcDTCStatusMask = pUdsData[2];         /*GcDTCStatusMask―DTC状态掩码*/

            GsUdsService.pUDSTxBuff[0] = pUdsData[0] + UDS_ACK;
            GsUdsService.pUDSTxBuff[1] = 0x0A;
            GsUdsService.pUDSTxBuff[2] = 0x09;       /*DTCStatusAvailabilityMask*/
            /*Report the status of all supported DTCs*/
            ReportSupportedDTC(&LwDTCCounter,&GsUdsService.pUDSTxBuff[3]);

            GsUdsService.UDSTxLen = (LwDTCCounter+3);/*Total length of reported data*/

            LcUdsStatus = UDS_ACK;      
        }
    }
    return LcUdsStatus;
}
/*********************************************************************
 * 函数名称:  UDS_ReadDataByIdentifier
 *
 * 功能描述:  读取版本等信息
 *
 * 输入参数:  请求报文的长度和首地址
 *
 * 输出参数:  无
 *
 * 其它说明:
 *
 * 修改日期     版本号       修改人       修改内容
 *
 ********************************************************************/
uint8 UDS_ReadDataByIdentifier(uint16 *pLwLen, uint8 *pUdsData )
{
    uint16 LcI;
    uint8 LcCnt;
    uint16 LcJ;
    uint8 LcReadDIDNum;
    uint16 LcRespondCnt;
    uint16 LwDID[DID_TOTAL_NUM];
    const volatile uint8 *PFlashAdress = NULL;

    uint8 LcUdsStatus = UDS_ACK;

    if((*pLwLen != 0x03))//||(*pLwLen > (DID_TOTAL_NUM+1))||(*pLwLen%2 == 0))     /* ECU总共支持DID_TOTAL_NUM个DID，因此，请求长度不可能大于DID_TOTAL_NUM*2+1,且总长度不为偶数个字节 */
    {
        LcUdsStatus = UDS_NRC_13;
    }
    else
    {
        LcReadDIDNum = ((*pLwLen) - 1)/2;/*get DTD numbers*/

        for(LcI = 0;LcI < LcReadDIDNum;LcI++)/*get DID  which need  to read*/
        {
            LwDID[LcI] = ((uint16)pUdsData[1 + 2*LcI]<<8) | pUdsData[2 + 2*LcI];
        }

        GsUdsService.pUDSTxBuff[0] = (uint8) (pUdsData[0] + UDS_ACK);       /* positive respond ID */

        LcRespondCnt = 1;

        if(GsUdsService.SecurityLevel == LH_SAFE_LEVEL)
        {
#if (CALIBRATION_EN == STD_ON)
            /* 豪恩自定义标定模式 */
            for(LcI = 0;LcI < LcReadDIDNum;LcI++)
            {
                for(LcCnt = 0;LcCnt < DID_CALIB_NUM;LcCnt++)
                {
                    if(LwDID[LcI] == CalibrationDIDDefine[LcCnt].GwDID)
                    {
                        if ((LcCnt >= FLS_FRS_ST) && (LcCnt <= CAL_DATA_FD2F))
                        {//因为增加1字节crc8校验，数据长度要减1
                            PFlashAdress = CalibrationDIDDefine[LcCnt].GcADDR;
                            GsUdsService.pUDSTxBuff[LcRespondCnt++] = (uint8)(LwDID[LcI] >> 8);
                            GsUdsService.pUDSTxBuff[LcRespondCnt++] = (uint8)LwDID[LcI];
                            LcRespondCnt = LcRespondCnt + (CalibrationDIDDefine[LcCnt].GwDataLen - 1);
                            //for (LcJ = 0;LcJ < (CalibrationDIDDefine[LcCnt].GwDataLen - 1);LcJ++)
                            //{
                            //    GsUdsService.pUDSTxBuff[LcRespondCnt++] = (uint8) * (PFlashAdress + LcJ);
                            //}
                            (void)Did_ReadData((LcCnt), LwDID[LcI],(CalibrationDIDDefine[LcCnt].GwDataLen - 1), &GsUdsService.pUDSTxBuff[3]);
                        }                        
                        else if ((LcCnt >= CAL_DATA_FD40) && (LcCnt <= CAL_DATA_FD67))//新接口
                        {//因为增加1字节crc8校验，数据长度要减1
                            PFlashAdress = CalibrationDIDDefine[LcCnt].GcADDR;
                            GsUdsService.pUDSTxBuff[LcRespondCnt++] = (uint8)(LwDID[LcI] >> 8);
                            GsUdsService.pUDSTxBuff[LcRespondCnt++] = (uint8)LwDID[LcI];
                            LcRespondCnt = LcRespondCnt + (CalibrationDIDDefine[LcCnt].GwDataLen - 1);
                            //for (LcJ = 0;LcJ < (CalibrationDIDDefine[LcCnt].GwDataLen - 1);LcJ++)
                            //{
                            //    GsUdsService.pUDSTxBuff[LcRespondCnt++] = (uint8) * (PFlashAdress + LcJ);
                            //}
                            (void)Did_ReadData((LcCnt), LwDID[LcI],(CalibrationDIDDefine[LcCnt].GwDataLen - 1), &GsUdsService.pUDSTxBuff[3]);
                        }
                        else if ((LcCnt >= CAL_DATA_FD70) && (LcCnt <= CAL_DATA_FDA4))
                        {//因为增加1字节crc8校验，数据长度要减1
                            PFlashAdress = CalibrationDIDDefine[LcCnt].GcADDR;
                            GsUdsService.pUDSTxBuff[LcRespondCnt++] = (uint8)(LwDID[LcI] >> 8);
                            GsUdsService.pUDSTxBuff[LcRespondCnt++] = (uint8)LwDID[LcI];

                            for (LcJ = 0;LcJ < (CalibrationDIDDefine[LcCnt].GwDataLen - 1);LcJ++)
                            {
                                GsUdsService.pUDSTxBuff[LcRespondCnt++] = (uint8) * (PFlashAdress + LcJ);
                            }
                        }
                        else
                        {
                            PFlashAdress = CalibrationDIDDefine[LcCnt].GcADDR;
                            GsUdsService.pUDSTxBuff[LcRespondCnt++] = (uint8)(LwDID[LcI] >> 8);
                            GsUdsService.pUDSTxBuff[LcRespondCnt++] = (uint8)LwDID[LcI];

                            for (LcJ = 0;LcJ < CalibrationDIDDefine[LcCnt].GwDataLen;LcJ++)
                            {
                                GsUdsService.pUDSTxBuff[LcRespondCnt++] = (uint8) * (PFlashAdress + LcJ);
                            }
                        }
                        break;
                    }
                }
                if(LcCnt >= DID_CALIB_NUM)
                {
                    GsUdsService.pUDSTxBuff[0] = 0x22;
                    LcUdsStatus = UDS_NRC_31;
                    break;
                }
            }
#else
            GsUdsService.pUDSTxBuff[0] = 0x22;
            LcUdsStatus = UDS_NRC_31;
#endif
        }
        else
        {
            /* 满足客户的诊断DID*/
            for(LcI = 0;LcI < LcReadDIDNum;LcI++)/*Iterate over the DIDs to be read*/
            {
                for(LcCnt = 0;LcCnt < DID_TOTAL_NUM;LcCnt++)/*Iterate through all the DIDs that need to be supported*/
                {

                    if(LwDID[LcI] == DIDDefinition[LcCnt].GwDID)/*check that the DID to be read is supported*/
                    {

                        for (LcJ = 0;LcJ < DID_NEED_UPDATA_NUM;LcJ++)/*Whether the DID data needs to be updated before reading*/
                        {
                            if (LwDID[LcI] == DIDDefinition_Needupdata[LcJ].Gu16DID)/*If need to update the DID data, call the update function*/
                            {
                                if (DIDDefinition_Needupdata[LcJ].IpduFuc != NULL)
                                {
                                    DIDDefinition_Needupdata[LcJ].IpduFuc();
                                }
                            }
                        }
                        PFlashAdress = DIDDefinition[LcCnt].GcADDR;/*Get data access address*/
                        GsUdsService.pUDSTxBuff[LcRespondCnt++] = (uint8)(LwDID[LcI]>>8);
                        GsUdsService.pUDSTxBuff[LcRespondCnt++] = (uint8)LwDID[LcI];
            
                        for(LcJ = 0;LcJ < DIDDefinition[LcCnt].GwDataLen;LcJ++)/*Get data by address*/
                        {
                            GsUdsService.pUDSTxBuff[LcRespondCnt++] = (uint8)*(PFlashAdress + LcJ);
                        }
                        break;
                    }
                    else/*not support*/
                    {
                        ;
                    }
                }
                if(LcCnt >= DID_TOTAL_NUM)/*Access DID out of bounds*/
                {
                    GsUdsService.pUDSTxBuff[0] = 0x22;
                    LcUdsStatus = UDS_NRC_31;/*respond NRC31*/
                    break;
                }
            }
        }


        GsUdsService.UDSTxLen = LcRespondCnt;/*respond cnt*/
    }
    return LcUdsStatus;
}

/*********************************************************************
 * 函数名称:  SecurityAccessProcess_01
 *
 * 功能描述:  安全访问(请求种子)处理函数
 *
 * 输入参数:  请求报文的首地址
 *
 * 输出参数:  无
 *
 * 其它说明:
 *
 * 修改日期     版本号       修改人       修改内容
 *
 ********************************************************************/
uint8 SecurityAccessProcess_01(uint8 *pUdsData)
{
    uint8  LcUdsStatus = UDS_ACK;
    uint32 LdSeed =0;
    uint32 Lu32SystemTimeCnt = 0;
    /* Unlocked or seed has been requested, seed is 0x00 */
    if((GsUdsService.SecurityLevel == SAFE_LEVEL_1)||(GsUdsService.SecurityLevel == LH_SAFE_LEVEL))
    {
        GsUdsService.UDSTxLen = 6U;
        GsUdsService.pUDSTxBuff[0] = (uint8) (pUdsData[0] + UDS_ACK);
        GsUdsService.pUDSTxBuff[1] = pUdsData[1];
        GsUdsService.pUDSTxBuff[2] = 0x00;    /* Unlocked or seed has been requested, seed is 0x00 */
        GsUdsService.pUDSTxBuff[3] = 0x00;    /* Unlocked or seed has been requested, seed is 0x00 */
        GsUdsService.pUDSTxBuff[4] = 0x00;    /* Unlocked or seed has been requested, seed is 0x00 */
        GsUdsService.pUDSTxBuff[5] = 0x00;    /* Unlocked or seed has been requested, seed is 0x00 */
        LcUdsStatus = UDS_ACK;
        GcWaitKeyFlag = FALSE;
    }
    else    /* Locked, get new seed*/
    {
        Lu32SystemTimeCnt = GetRandSeed();/* get new seed*/
        GsUdsService.UDSTxLen = 6U;
        GsUdsService.pUDSTxBuff[0] = (uint8) (pUdsData[0] + UDS_ACK);
        GsUdsService.pUDSTxBuff[1] = pUdsData[1];
        GsUdsService.pUDSTxBuff[2] = (uint8)(Lu32SystemTimeCnt >> 24);/*12*/;
        GsUdsService.pUDSTxBuff[3] = (uint8)(Lu32SystemTimeCnt >> 16);/*34*/
        GsUdsService.pUDSTxBuff[4] = (uint8)(Lu32SystemTimeCnt >> 8);/*56*/
        GsUdsService.pUDSTxBuff[5] = (uint8)(Lu32SystemTimeCnt >> 0);/*78*/
        
        LcUdsStatus = UDS_ACK;
        /* Merged seeds */
        LdSeed = (uint32)(((uint32)GsUdsService.pUDSTxBuff[2])<<24)|(((uint32)GsUdsService.pUDSTxBuff[3])<<16)\
                        |(((uint32)GsUdsService.pUDSTxBuff[4])<<8)|((uint32)GsUdsService.pUDSTxBuff[5]);
        if(pUdsData[1] == 0x01)/*L1*/
        {
            /* Calculate the secret key */
            GdCalcKey = SeedKey_Calc(LdSeed);
        }
#if (CALIBRATION_EN == STD_ON)
        else if(pUdsData[1] == LH_SECURITY_ID_61)
        {
            GdLH_SecurityCalcKey = LonghornSecurityAlgorithm(LdSeed);
        }
#endif
        GcWaitKeyFlag = TRUE;                /* memory the request Sequence */
    }
   return LcUdsStatus; 
}
/*********************************************************************
 * 函数名称:  SecurityAccessProcess_02
 *
 * 功能描述:  安全访问(校验秘钥)处理函数
 *
 * 输入参数:  请求报文的首地址

 *
 * 输出参数:  无
 *
 * 其它说明:
 *
 * 修改日期     版本号       修改人       修改内容
 *
 ********************************************************************/
uint8 SecurityAccessProcess_02( uint8 *pUdsData)
{ 
    uint32 LdReceiveKey ;
    uint8  LcUdsStatus = UDS_ACK;
    if(GcWaitKeyFlag != TRUE)    /* Returns NRC24 if not waiting for the verify secret key step */
    {
        LcUdsStatus = UDS_NRC_24;/*Returns NRC24*/
    }
    else/*waiting for the verify secret key step */
    {
        GcWaitKeyFlag = 0;                                 /* Clear the request Sequence */
        LdReceiveKey = pUdsData[2];                        /*K4*/
        LdReceiveKey = (LdReceiveKey << 8) | pUdsData[3];  /*K3*/
        LdReceiveKey = (LdReceiveKey << 8) | pUdsData[4];  /*K2*/
        LdReceiveKey = (LdReceiveKey << 8) | pUdsData[5];  /*K1*/

        if(pUdsData[1] == 0x02)/*27 02*/
        {
#ifdef SECURITY_NEED                
            if(LdReceiveKey == GdCalcKey)/*check key*/
#else
            if(1)
#endif                  
            {
                GcErrorKeyCnt = 0;     /* Checksum successful, clears error counter to 0 */
                GsUdsService.SecurityLevel = SAFE_LEVEL_1;/*unlock*/
                /*Positive Response*/
                GsUdsService.UDSTxLen = 2U;
                GsUdsService.pUDSTxBuff[0] = (uint8) (pUdsData[0] + UDS_ACK);
                GsUdsService.pUDSTxBuff[1] = pUdsData[1];
                LcUdsStatus = UDS_ACK;/*Positive Response*/
            }
            else
            {
                if(GcErrorKeyCnt < 2)    /* Three times you receive an invalid key, you have to reply with a 36-negative code ,otherwise you have to answer NRC35*/
                {
                    LcUdsStatus = UDS_NRC_35;
                    GcErrorKeyCnt++;
                }
                else/* Three times you receive an invalid key, you have to reply with a 36-negative code */
                {
                    /* Set the error counter to 3; and lock for 10s */
                    GcErrorKeyCnt=3;
                    LcUdsStatus = UDS_NRC_36;
                    GsUdsService.AccessLockTime = 10000;     /* Locked for 10s per error */
                }
            }
        }
        else
        {
            if(LdReceiveKey == GdLH_SecurityCalcKey)/*27 62*/
            {
                GcErrorKeyCnt = 0;          /* Checksum successful, clears error counter to 0 */
                GsUdsService.SecurityLevel = LH_SAFE_LEVEL;
                GsUdsService.UDSTxLen = 2U;
                GsUdsService.pUDSTxBuff[0] = (uint8) (pUdsData[0] + UDS_ACK);
                GsUdsService.pUDSTxBuff[1] = pUdsData[1];
                LcUdsStatus = UDS_ACK;/*Positive Response*/
            }
            else
            {
                if(GcErrorKeyCnt < 2)  /* Three times you receive an invalid key, you have to reply with a 36-negative code ,otherwise you have to answer NRC35*/
                {
                    LcUdsStatus = UDS_NRC_35;
                    GcErrorKeyCnt++;
                }
                else
                {
                    /* Set the error counter to 3; and lock for 10s */
                    GcErrorKeyCnt=3;
                    LcUdsStatus = UDS_NRC_36;
                    GsUdsService.AccessLockTime = 10000;    /* Locked for 10s per error */
                }
            }
        }
    }
   return LcUdsStatus;
}
/*********************************************************************
 * 函数名称:  UDS_SecurityAccess
 *
 * 功能描述:  安全访问处理函数
 *
 * 输入参数:  请求报文的首地址
 *
 * 输出参数:  无
 *
 * 其它说明:
 *
 * 修改日期     版本号       修改人       修改内容
 *
 ********************************************************************/
uint8 SecurityAccessProcess(uint8 *pUdsData)
{
    uint8  LcUdsStatus = UDS_ACK;
    uint8 Lu8HisErrCnt[1] ={0};
    if((pUdsData[1] == 0x01)||(pUdsData[1] == LH_SECURITY_ID_61))/* Request for seeds */
    {
       LcUdsStatus = SecurityAccessProcess_01(pUdsData); /**/
    }
    else if((pUdsData[1] == 0x02)||(pUdsData[1] == LH_SECURITY_ID_62))/* Processing of secret keys */
    { 
        LcUdsStatus = SecurityAccessProcess_02(pUdsData);/**/
    }
    /* Storing security access error counters */
    EELReadData(&Lu8HisErrCnt[0], ERRORKEYCNT_ADDRESS, ERRORKEYCNT_EEPROM_LEN);
    if(Lu8HisErrCnt[0] != GcErrorKeyCnt)
    {
        /* Update security access error counters */
        Lu8HisErrCnt[0] = GcErrorKeyCnt;
        (void)EELWriteDataImmediate(&Lu8HisErrCnt[0], ERRORKEYCNT_ADDRESS, ERRORKEYCNT_EEPROM_LEN);
    }
    return LcUdsStatus;
}
/*********************************************************************
 * 函数名称:  UDS_SecurityAccess
 *
 * 功能描述:  安全访问请求
 *
 * 输入参数:  请求报文的长度和首地址
 *
 * 输出参数:  无
 *
 * 其它说明:
 *
 * 修改日期     版本号       修改人       修改内容
 *
 ********************************************************************/
uint8 UDS_SecurityAccess(uint16 *pLwLen, uint8 *pUdsData)
{
    uint8  LcUdsStatus;
    Car_SpeedType_Phy LfCar_Speed_phy;
    ReadSysSignal_Car_Speed_phy(&LfCar_Speed_phy);/* get speed signals;  */
    Car_SpeedVildType LvCar_SpeedVild;
    ReadSysSignal_Car_SpeedVild(&LvCar_SpeedVild);/* get speed validity signals */

    if((GsUdsService.SessionMode != UDS_SESSION_EXTENDED)||(GsUdsService.UdsAddrMode == UDS_ADDR_FUNCTIONAL))
    {
        LcUdsStatus = UDS_NRC_7F;/*27 Response NRC7F when service is not under extended session and is functionally addressed */
    }
    else if(*pLwLen < 2)/*27 Total length of service less than 2 in response to NRC13*/
    {
        LcUdsStatus = UDS_NRC_13;
    }
#if (CALIBRATION_EN == STD_ON)
    else if ((pUdsData[1] != 0x01) && (pUdsData[1] != 0x02)&&(pUdsData[1] != LH_SECURITY_ID_61) && (pUdsData[1] != LH_SECURITY_ID_62))/*SubID not support  response to NRC12*/
    {
        LcUdsStatus = UDS_NRC_12;/*27 service not support suppress bit*/
    }
#else
    else if ((pUdsData[1] != 0x01) && (pUdsData[1] != 0x02))/*SubID not support  response to NRC12*/
    {
        LcUdsStatus = UDS_NRC_12;/*27 service not support suppress bit*/
    }
#endif
    else if(((pUdsData[1] == 0x01)&&(*pLwLen != 2))\
        ||((pUdsData[1] == 0x02)&&(*pLwLen != 6)))  /* Byte length of request seed is 2, length of send key is 6 */
    {
        LcUdsStatus = UDS_NRC_13;/* response to NRC13*/
    }
#if (CALIBRATION_EN == STD_ON)
    else if (((pUdsData[1] == 0x01) && (*pLwLen != 2))\
            || ((pUdsData[1] == 0x02) && (*pLwLen != 6))\
            || ((pUdsData[1] == LH_SECURITY_ID_61) && (*pLwLen != 2))\
            || ((pUdsData[1] == LH_SECURITY_ID_62) && (*pLwLen != 6)))  /* Byte length of request seed is 2, length of send key is 6 */
    {
        LcUdsStatus = UDS_NRC_13;/* response to NRC13*/
    }
#else
    else if (((pUdsData[1] == 0x01) && (*pLwLen != 2))\
        || ((pUdsData[1] == 0x02) && (*pLwLen != 6)))  /* Byte length of request seed is 2, length of send key is 6 */
    {
        LcUdsStatus = UDS_NRC_13;/* response to NRC13*/
    }
#endif
    else if(GsUdsService.AccessLockTime != 0)
    {
        LcUdsStatus = UDS_NRC_37;/*Not reached unlock time, responding to NRC37*/
    }
    else
    {
        LcUdsStatus = SecurityAccessProcess(pUdsData);/* Processing 27 Service */
    }
    return LcUdsStatus;
}

/*********************************************************************
 * 函数名称:  UDS_RoutineControl
 *
 * 功能描述:  例程控制
 *
 * 输入参数:  请求报文地址和长度
 *
 * 输出参数:  无
 *
 * 其它说明:
 *
 * 修改日期     版本号       修改人       修改内容
 *
 ********************************************************************/
uint8 UDS_RoutineControl(uint16 *pLwLen, uint8 *pUdsData)
{
    uint8 LcUdsStatus;
    uint16 LwDID;
    Car_SpeedType_Phy LfCar_Speed_phy;/*  speed  signals */
    Car_SpeedVildType LvCar_SpeedVild;/* speed validity signals */

    LwDID = (uint16)((((uint16)pUdsData[2])<<8)|((uint16)pUdsData[3]));
    /*31 service not support functional address*/
    if((GsUdsService.SessionMode != UDS_SESSION_EXTENDED) ||((GsUdsService.UdsAddrMode == UDS_ADDR_FUNCTIONAL)))/*31 Response NRC7F when service is not under extended session  */
    {
        LcUdsStatus = UDS_NRC_7F;
    }
    else if(*pLwLen < 4)/*Total length of service less than 4 in response to NRC13*/
    {
        LcUdsStatus = UDS_NRC_13;
    } 
    else if (LwDID != 0xFF02)/* Only FF02DID access is allowed here */
    {
        LcUdsStatus = UDS_NRC_31;
    }
    else if (pUdsData[1] != 0x01)   /*LEEA_X01 only supports 01 sub-service*/
    {
        LcUdsStatus = UDS_NRC_12;
    }
    else if(*pLwLen != 4)/*Total length of service more than 4 in response to NRC13*/
    {
        LcUdsStatus = UDS_NRC_13;
    }
    else 
    {
        GsUdsService.UDSTxLen = 5;
        GsUdsService.pUDSTxBuff[0] = pUdsData[0] + UDS_ACK;
        GsUdsService.pUDSTxBuff[1] = pUdsData[1];
        GsUdsService.pUDSTxBuff[2] = (uint8)(LwDID>>8);/*DID high 8 bit/*/
        GsUdsService.pUDSTxBuff[3] = (uint8)LwDID;/* only take the low byte here, no worry about data overflow */

        ReadSysSignal_Car_SpeedVild(&LvCar_SpeedVild);/* get speed validity signals */
        ReadSysSignal_Car_Speed_phy(&LfCar_Speed_phy); /* get speed signals;  */
        /*If the vehicle speed message is prohibited from being received, it passes the pre-programmed detection by default*/
        if (NCS_TST_BIT_SET(GcFrameStatus[ID096Idx], COM_FRAME_STATUS_RX_DISABLE, uint8))
        {
            CheckProgrammingPreConditionFlag = TRUE;/* Checksum pass*/
            GsUdsService.pUDSTxBuff[4] = 0x00;
        }
        else
        {
            /* Invalid speed value or speed value exceeding 2km/h or speed message timeout, programming pre-conditions not fulfilled */
            if ((LfCar_Speed_phy > 2.0) || \
                (LvCar_SpeedVild == 1) || \
                (NCS_TST_BIT_SET(GcFrameStatus[ID096Idx], COM_FRAME_STATUS_RX_TIMEOUT, uint8)))
            {
                CheckProgrammingPreConditionFlag = FALSE;/* Checksum failed */
                GsUdsService.pUDSTxBuff[4] = 0x01;
            }
            else
            {
                CheckProgrammingPreConditionFlag = TRUE;/* Checksum pass*/
                GsUdsService.pUDSTxBuff[4] = 0x00;
            }
        }
       LcUdsStatus = UDS_ACK;
    } 
    return LcUdsStatus;

}
/*********************************************************************
 * 函数名称:  UDS_WriteDataByIdentifier_LH
 *
 * 功能描述:  处理豪恩内部写DID操作
 *
 * 输入参数:  请求报文地址和长度
 *
 * 输出参数:  无
 *
 * 其它说明:
 *
 * 修改日期     版本号       修改人       修改内容
 *
 ********************************************************************/
#if (CALIBRATION_EN == STD_ON)
uint8 UDS_WriteDataByIdentifier_LH(uint16 *pLwLen,uint8 *pUdsData)
{
    uint16 LcI;
    uint8 LcCnt;
    uint16 LcDID = 0;
    uint8 LcUdsStatus = UDS_NRC_78;
    uint8 LcDIDCheckErr = 0;
    uint8 LcDataLenCheckErr = 0;
    uint8 LcIndex = 0xFF;/*默认不支持的DID下标为0xFF;前提是目前DID数量未到255个*/
    if(*pLwLen > 3)/*计算当前接收到的DID*/
    {
        LcDID = (uint16)((((uint16)pUdsData[1])<<8)|((uint16)pUdsData[2]));
    }

    /*豪恩标定的写入DID*/
    for(LcCnt = 0;LcCnt < DID_CALIB_NUM;LcCnt++)/*接收到的DID在支持的DID表中的下标值*/
    {
        if((LcDID == CalibrationDIDDefine[LcCnt].GwDID)&&(ReadWrite == CalibrationDIDDefine[LcCnt].GcDir))
        {/*查询到支持的DID且支持写操作，退出for循环*/
            LcIndex = LcCnt;/*记录当前符合的DID下标*/
            break;
        }
    }

    if(0xFF == LcIndex)/*若下标为默认值0xFF，则说明未查询到支持的DID*/
    {
        LcDIDCheckErr = 0x1;/*置起DID检查异常标志*/
    }
    else if((LcIndex < DID_CALIB_NUM )&&((*pLwLen-3) !=CalibrationDIDDefine[LcIndex].GwDataLen))
    {/*查询到支持的DID，但是长度不符*/
        LcDataLenCheckErr = 0x1;/*置起数据长度检查异常标志*/
    }
    else/*DID与数据长度都支持，什么都不做*/
    {
        LcDataLenCheckErr = 0x0;
    }

    if(GsUdsService.SessionMode != UDS_SESSION_EXTENDED)
    {
        LcUdsStatus = UDS_NRC_7F;
    }
    else if(*pLwLen < 4)
    {
        LcUdsStatus = UDS_NRC_13;
    }
    else if(0x1 == LcDIDCheckErr)/*未查询到支持的DID*/
    {
        LcUdsStatus = UDS_NRC_31;
    }
    else if(0x1 == LcDataLenCheckErr)/*支持的DID长度不符*/
    {
        LcUdsStatus = UDS_NRC_13;
    }
    else if(GsUdsService.SecurityLevel != LH_SAFE_LEVEL)
    {
        LcUdsStatus = UDS_NRC_33;
    }
    else
    {
        for (LcCnt = 0;LcCnt < DID_CALIB_NUM;LcCnt++)
        {
            if (LcDID == CalibrationDIDDefine[LcCnt].GwDID)
            {
                if (LcDID == 0xFDC0)//探头参数不需要写入MCU EEP
                {
                    for (LcI = 0;LcI < *pLwLen;LcI++)
                    {
                        GcUdsData[LcI] = pUdsData[LcI];
                    }
                    GsLongRequest.GcNRC78Event = NRC78_WRITEDATABYDIDS;
                }
                else
                {
                    if(GcUdsData[3 + (CalibrationDIDDefine[LcCnt].GwDataLen - 1)] == calculate_crc8(&GcUdsData[3], (CalibrationDIDDefine[LcCnt].GwDataLen - 1)))
                    {
                        for (LcI = 0;LcI < *pLwLen;LcI++)
                        {
                            GcUdsData[LcI] = pUdsData[LcI];
                        }
                        GsLongRequest.GcNRC78Event = NRC78_WRITEDATABYDIDS;
                    }
                    else
                    {
                        LcUdsStatus = UDS_NRC_73;
                    }
                }
                break;
            }
            else
            {
                for (LcI = 0;LcI < *pLwLen;LcI++)
                {
                    GcUdsData[LcI] = pUdsData[LcI];
                }
                GsLongRequest.GcNRC78Event = NRC78_WRITEDATABYDIDS;
            }
        }

    }
    return LcUdsStatus;
}
#endif
/*********************************************************************
 * 函数名称:  UDS_CheckDIDBeforWriteDID
 *
 * 功能描述:  写服务
 *
 * 输入参数:  请求报文地址和长度
 *
 * 输出参数:  无
 *
 * 其它说明:
 *
 * 修改日期     版本号       修改人       修改内容
 *
 ********************************************************************/ 
uint8 UDS_CheckDIDBeforWriteDID(uint8 *pUdsData,uint16 LcDID)
{
    uint8 LcUdsStatus = UDS_NRC_78;
    if(0xF18B == LcDID)
    {  /* ECU manufacturing date can only be written once, after writing once the last byte is synchronously modified to '0x55', it is '0' before it is written */
       if(0x55 != DID_F18B_Buff[3])
       {
            if((pUdsData[4] < 1) || (pUdsData[4] > 12))
            {
                /*The Manufacturing month  out of range, set the ErrorCode*/
                LcUdsStatus = UDS_NRC_31;
            }
            else if((pUdsData[5] < 1) || (pUdsData[5] > 31))
            {
                /*The Manufacturing day  out of range, set the ErrorCode*/
                LcUdsStatus =  UDS_NRC_31;
            }
            else
            {
            
            }
       }
       else
       {
            LcUdsStatus =  UDS_NRC_31;
       }
    }
    else if(0xF18C == LcDID)
    {   /* ECU flow number can only be written once, after writing once the last byte is synchronously modified to '0x55', it is '0' before it is written */
        if(0x55 == DID_F18C_Buff[15])
        {
            LcUdsStatus =  UDS_NRC_31;
        }
        else
        {
        
        }
    }
    else if(0xF1A0 == LcDID)
    { 
        if((pUdsData[4] < 1) || (pUdsData[4] > 12))
        {
            /*The Manufacturing month  out of range, set the ErrorCode*/
            LcUdsStatus =  UDS_NRC_31;
        }
        else if((pUdsData[5] < 1) || (pUdsData[5] > 31))
        {
            /*The Manufacturing day  out of range, set the ErrorCode*/
            LcUdsStatus =  UDS_NRC_31;
        }
        else
        {
        
        }
    }
    else
    {
    
    }
    return LcUdsStatus;

}

/*********************************************************************
 * 函数名称:  UDS_WriteDataByIdentifier
 *
 * 功能描述:  写服务
 *
 * 输入参数:  请求报文地址和长度
 *
 * 输出参数:  无
 *
 * 其它说明:
 *
 * 修改日期     版本号       修改人       修改内容
 *
 ********************************************************************/
uint8 UDS_WriteDataLenCheck(uint16 u16Len, uint8 u8Index)
{
    if (0xFF == u8Index)/*若下标为默认值0xFF，则说明未查询到支持的DID*/
    {
        return 0x2;/*置起DID检查异常标志*/
    }
    else if ((u8Index < DID_TOTAL_NUM) && ((u16Len) != DIDDefinition[u8Index].GwDataLen))
    {/*查询到支持的DID，但是长度不符*/
        return  0x1;/*置起数据长度检查异常标志*/
    }
    else/*DID与数据长度都支持，什么都不做*/
    {
        return  0x0;
    }
}
uint8 UDS_WriteDataByIdentifier(uint16 *pLwLen, uint8 *pUdsData)
{
    uint16 LcI;
    uint8 LcCnt;
    uint16 LcDID = 0;
    uint8 LcUdsStatus = UDS_NRC_78;
    uint8 LcDataLenCheckErr = 0;
    uint8 LcIndex = 0xFF;/*默认不支持的DID下标为0xFF;前提是目前DID数量未到255个*/
    Signal_VoltageStatusType LenuVoltageStatus;
    ReadPwrManage_GroupStatus(PM_CAN_UDS,&LenuVoltageStatus); /*get voltage*/

    if(*pLwLen > 3)/*计算当前接收到的DID*/
    {
        LcDID = (uint16)((((uint16)pUdsData[1])<<8)|((uint16)pUdsData[2]));
    }
#if (CALIBRATION_EN == STD_ON)
    if(GsUdsService.SecurityLevel == LH_SAFE_LEVEL)
    {

        LcUdsStatus = UDS_WriteDataByIdentifier_LH(pLwLen,pUdsData);

    }
    else
#endif
    {
        /*满足客户需求的写入DID*/
        for(LcCnt = 0;LcCnt < DID_TOTAL_NUM;LcCnt++)/*接收到的DID在支持的DID表中的下标值*/
        {
            if((LcDID == DIDDefinition[LcCnt].GwDID)&&(ReadWrite == DIDDefinition[LcCnt].GcDir))
            {/*查询到支持的DID且支持写操作，退出for循环*/
                LcIndex = LcCnt;/*记录当前符合的DID下标*/
                break;
            }
        }
        /*check datalen*/
        LcDataLenCheckErr = UDS_WriteDataLenCheck((*pLwLen - 3), LcIndex);
        /*2E service not under physical addressing *//*2E service not under extend session */
        if ((GsUdsService.UdsAddrMode != UDS_ADDR_PHYSICAL)||(GsUdsService.SessionMode != UDS_SESSION_EXTENDED))
        {
            LcUdsStatus = UDS_NRC_7F;/*respond NRC7F*/
        }
        else if(*pLwLen < 4)/*2E service data len less than 4 */
        {
            LcUdsStatus = UDS_NRC_13;/*respond NRC13*/
        }
        else if(0x2 == LcDataLenCheckErr)/* Supported DID not queried */
        {
            LcUdsStatus = UDS_NRC_31;/*respond NRC31*/
        }
        else if(0x1 == LcDataLenCheckErr)/* Supported DID length does not match */
        {
            LcUdsStatus = UDS_NRC_13;/*respond NRC13*/
        }
        else if(GsUdsService.SecurityLevel != SAFE_LEVEL_1)/*2E service not under unlock  */
        {
            LcUdsStatus = UDS_NRC_33;/*respond NRC33*/
        }
        else if(NORMAL_VOLTAGE != LenuVoltageStatus )/*Voltage status discrepancy*/
        {
            LcUdsStatus = UDS_NRC_22;/*respond NRC22*/
        }
        else
        {
            LcUdsStatus = UDS_CheckDIDBeforWriteDID(pUdsData,LcDID);/*Calibration written to DID format*/
            if(LcUdsStatus == UDS_NRC_78)
            {
				for(LcI = 0;LcI < *pLwLen;LcI++)
				{
					GcUdsData[LcI] = pUdsData[LcI];
				}
				GsLongRequest.GcNRC78Event = NRC78_WRITEDATABYDIDS;/*active 78 event*/
            }
            else
            {
            	/*NRC31*/
            }
        }
    }

    return LcUdsStatus;
}
/*********************************************************************
 * 函数名称:  UDS_TesterPresent
 *
 * 功能描述:  诊断在线
 *
 * 输入参数:  请求报文地址和长度
 *
 * 输出参数:  无
 *
 * 其它说明:
 *
 * 修改日期     版本号       修改人       修改内容
 *
 ********************************************************************/
uint8 UDS_TesterPresent(uint16 *pLwLen, uint8 *pUdsData)
{
    uint8 LcUdsStatus;
    if(*pLwLen < 0x02)/*3E service data len less than 2 */
    {
        LcUdsStatus = UDS_NRC_13;
    }
    else if((pUdsData[1] & 0x7F)!= 0x00)/*3E SubID isn't 0 */
    {
        LcUdsStatus = UDS_NRC_12;
    }
    else if(*pLwLen != 0x02)/*3E service data len more than 2 */
    {
        LcUdsStatus = UDS_NRC_13;
    }    
    else 
    {
        if((pUdsData[1] & 0x80) == 0x00)/*unsuppressed response*/
        {
            GsUdsService.pUDSTxBuff[0] = pUdsData[0] + UDS_ACK;
            GsUdsService.pUDSTxBuff[1] = pUdsData[1];
            GsUdsService.UDSTxLen = 2;/*passive respond*/
        }
        else 
        {
            GsUdsService.UDSTxLen = 0;/*suppressed response*/
        }
        LcUdsStatus = UDS_ACK;
    }
    return LcUdsStatus;
}
/*********************************************************************
 * 函数名称:  UDS_ControlDTCSetting
 *
 * 功能描述:  DTC控制
 *
 * 输入参数:  请求报文地址和长度
 *
 * 输出参数:  无
 *
 * 其它说明:
 *
 * 修改日期     版本号       修改人       修改内容
 *
 ********************************************************************/
uint8 UDS_ControlDTCSetting(uint16 *pLwLen, uint8 *pUdsData)
{
    uint8 LcUdsStatus = UDS_ACK;
    Car_SpeedType_Phy LfCar_Speed_phy;/*speed signal */
    ReadSysSignal_Car_Speed_phy(&LfCar_Speed_phy);/* Get speed signal */
    Car_SpeedVildType LvCar_SpeedVild;/* speed valid signal */
    ReadSysSignal_Car_SpeedVild(&LvCar_SpeedVild);/* get speed valid signal */
    if(GsUdsService.SessionMode != UDS_SESSION_EXTENDED)/*In a non-extended session*/
    {
        if(GsUdsService.UdsAddrMode == UDS_ADDR_FUNCTIONAL)/*In a Function addressing */
        {
            GsUdsService.UDSTxLen = 0;/*negative response*/
        }   
        else if(GsUdsService.UdsAddrMode == UDS_ADDR_PHYSICAL)/*In a pyhsical addressing */
        {
            LcUdsStatus = UDS_NRC_7F;/*respond NRC7F*/
        }
    }
    else if(*pLwLen < 2)/* data len less than 2 */
    {
        LcUdsStatus = UDS_NRC_13;/*respond NRC13*/
    } 
    else if(((pUdsData[1]&0x7F) != 0x01)&&((pUdsData[1]&0x7F) != 0x02))/*Sub-ID verification 01 02*/
    {
        LcUdsStatus = UDS_NRC_12;/*respond NRC12*/
    }    
    else if(*pLwLen != 2)/* data len more than 2 */
    {
        LcUdsStatus = UDS_NRC_13;/*respond NRC13*/
    }
    else if(((pUdsData[1]&0x7F) == 0x02)&&((LfCar_Speed_phy > 2.0)||(1 == LvCar_SpeedVild)))/*Environmental checks, speed*/
    {
        LcUdsStatus = UDS_NRC_22;/*respond NRC22*/
    }      
    else 
    {
        if((pUdsData[1]&0x7F) == 0x01)
        {
            GsUdsService.DTCSettingType = DTC_SETTING_ON;/* Open DTC */
        }
        else
        {
            GsUdsService.DTCSettingType = DTC_SETTING_OFF;/* Close DTC */
        }

        if((pUdsData[1]&0x80) == 0x00)/*unsuppressed response*/
        {
            GsUdsService.pUDSTxBuff[0] = (uint8)(pUdsData[0] + UDS_ACK);
            GsUdsService.pUDSTxBuff[1] = pUdsData[1];
            GsUdsService.UDSTxLen = 2;/*passive respond*/
        }
        else /*suppressed response*/
        {
            GsUdsService.UDSTxLen = 0;
        }
    }
    return LcUdsStatus;
    
}
/*********************************************************************
 * 函数名称:  UDS_CommunicationControl
 *
 * 功能描述:  通信控制
 *
 * 输入参数:  请求报文地址和长度
 *
 * 输出参数:  无
 *
 * 其它说明:  子功能: 0x00:EnableRxAndTx;          0x01:EnableRxAndDisableTx;
 *                    0x02:DisableRxAndEnableTx    0x03:DisableRxAndTx
 *            通讯类型: 0x01:应用报文；0x02:网络管理报文；0x03:网络管理和应用报文
 *
 * 修改日期     版本号       修改人       修改内容
 *
 ********************************************************************/
uint8 UDS_CommunicationControl(uint16 *pLwLen, uint8 *pUdsData)
{
    uint8 LcUdsStatus;
    Car_SpeedType_Phy LfCar_Speed_phy;
    ReadSysSignal_Car_Speed_phy(&LfCar_Speed_phy);  /* Get speed signal */
    Car_SpeedVildType LvCar_SpeedVild;
    ReadSysSignal_Car_SpeedVild(&LvCar_SpeedVild);/* get speed valid signal */

    LcUdsStatus = UDS_ACK;

    if(GsUdsService.SessionMode != UDS_SESSION_EXTENDED)/*In a non-extended session*/
    {
        LcUdsStatus = UDS_NRC_7F;
    }
    else if(*pLwLen < 0x03)/* data len less than 3 */
    {
        LcUdsStatus = UDS_NRC_13;
    }
    else if(((pUdsData[1]&0x7F) != 00)&&((pUdsData[1]&0x7F) != 03))   /* Subfunction 0x00 0x03 for communication control */
    {
        LcUdsStatus = UDS_NRC_12;
    }
    else if(*pLwLen != 0x03)/* data len more than 3 */
    {
        LcUdsStatus = UDS_NRC_13;
    }
    else if((pUdsData[2] != 0x01)&&(pUdsData[2] != 0x03))    /* Type of communication supported */
    {
        LcUdsStatus = UDS_NRC_31;
    }
    else if(((LfCar_Speed_phy > 2.0)||(1 == LvCar_SpeedVild)))/*Environmental checks, speed */
    {
        LcUdsStatus = UDS_NRC_22;
    }       
    else 
    {
        /* Communications control - receive processing */
        COM_NMAndAPPMsgControl_RX((pUdsData[1]&0x7F),pUdsData[2]);
        /* Communications control -  transmit processing */
        COM_NMAndAPPMsgControl_TX((pUdsData[1]&0x7F),pUdsData[2]);

        if((pUdsData[1]&0x80) == 0x00)/* Positive response */
        {
            GsUdsService.pUDSTxBuff[0] = (uint8)(pUdsData[0] + UDS_ACK);
            GsUdsService.pUDSTxBuff[1] = pUdsData[1];
            GsUdsService.UDSTxLen = 2;
        }
        else
        {
            GsUdsService.UDSTxLen = 0;  /* Suppression of response */
        }
    }
    return LcUdsStatus;
}
/*********************************************************************
 * 函数名称:  UDS_Respond
 *
 * 功能描述:  UDS响应
 *
 * 输入参数:  请求报文地址和长度
 *
 * 输出参数:  无
 *
 * 其它说明:
 *
 * 修改日期     版本号       修改人       修改内容
 *
 ********************************************************************/
void UDS_Respond(uint8 LcUdsStatus, uint16 uwLen, uint8 *pUdsData)
{   
    if(LcUdsStatus == UDS_NRC_78)
    {
        GsLongRequest.m_aubDiagNack78Data[0] = UDS_NRC;
        GsLongRequest.m_aubDiagNack78Data[1] = pUdsData[0];
        GsLongRequest.m_aubDiagNack78Data[2] = LcUdsStatus;

        GsLongRequest.CycleTimer =  UDS_P2;         /* Set the first NRC78 response sent */
        GsLongRequest.m_eState = UDS_LONG_REQ_ON;   /* Enable NRC78 function */

        UDS_TP_RespondManage(LcUdsStatus,3,GsLongRequest.m_aubDiagNack78Data); /* Call TP library function to send response */
    }
    else
    {
        if(LcUdsStatus != UDS_ACK)
        {
            UDS_NegativeAnswer(&uwLen,pUdsData,LcUdsStatus);   /* Processing of diagnostic negative response data */
        }

        UDS_TP_RespondManage(LcUdsStatus, GsUdsService.UDSTxLen,GsUdsService.pUDSTxBuff);     /* Call TP library function to send response */
    }
}
/*********************************************************************
 * 函数名称:  DiagServiceManage
 *
 * 功能描述:  诊断服务处理
 *
 * 入口参数:  uint16 LwLen, uint8 *paubUdsData,uint8 LcMsgIdx
 *
 * 输出参数:  无
 *
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 *
 ********************************************************************/
void DiagServiceManage(uint16 LwLen, uint8 *pUdsData)
{
    uint8 LcUdsStatus;

   /* 只要接收到诊断?ㄎ木鸵?一直刷新这个时间，只有没有接收到诊断报文时才会对这个计数器进行减??  龚岩*/
    if(GsUdsService.SessionMode != UDS_SESSION_DEFAULT)
    {
        GsUdsService.KeepSessionTimeCnt = (uint16) UDS_SESS_TIMEOUT;   // 重新初始化 S3service 的时间
    }
    switch(pUdsData[0])
    {
        case 0x10:
        {
            LcUdsStatus = UDS_StartDiagnosticSession(&LwLen,pUdsData);/* 10 Service Handler Functions */
            break;
        }
        case 0x11:
        {
            LcUdsStatus = UDS_ECUResetService(&LwLen,pUdsData);/* 11 Service Handler Functions */
            break;
        }
        case 0x14:
        {
            LcUdsStatus = UDS_ClearDiagnosticInformation(&LwLen,pUdsData);/* 14 Service Handler Functions */
            break;
        }
        case 0x19:
        {
            LcUdsStatus = UDS_ReadDiagnosticInformation(&LwLen,pUdsData);/*19  Service Handler Functions */
            break;
        }
        case 0x22:
        {
            LcUdsStatus = UDS_ReadDataByIdentifier(&LwLen,pUdsData);/* 22 Service Handler Functions */
            break;
        }
        case 0x27:
        {
            LcUdsStatus = UDS_SecurityAccess(&LwLen,pUdsData);/* 27 Service Handler Functions */
            break;
        }
        case 0x28:
        {
            LcUdsStatus = UDS_CommunicationControl(&LwLen,pUdsData);/* 28 Service Handler Functions */
            break;
        }
        case 0x2E:
        {
            LcUdsStatus = UDS_WriteDataByIdentifier(&LwLen,pUdsData);/* 2E Service Handler Functions */
            break;
        }
        case 0x31:
        {
            LcUdsStatus = UDS_RoutineControl(&LwLen,pUdsData);/* 31 Service Handler Functions */
            break;
        }
        case 0x3E:
        {
            LcUdsStatus = UDS_TesterPresent(&LwLen,pUdsData);/*  3E Service Handler Functions */
            break;
        }
        case 0x85:
        {
            LcUdsStatus = UDS_ControlDTCSetting(&LwLen,pUdsData);/*85 Service Handler Functions */
            break;
        }
        default:
        {
            LcUdsStatus = UDS_NRC_11;/* Unsupported services */
            break;
        }
    }

    UDS_Respond(LcUdsStatus,LwLen,pUdsData);/*response processing*/
}

/*********************************************************************
 * 函数名称:  WriteDataByIdentifier
 *
 * 功能描述:  执行写DID服务
 *
 * 输入参数:
 *
 * 输出参数:  无
 *
 * 其它说明:
 *
 * 修改日期     版本号       修改人       修改内容
 *
 ********************************************************************/
void WriteDataByIdentifier(void)
{
    uint16 LcCnt;
    uint16 LwDID;
    uint8 LcUdsStatus = UDS_ACK;
    
    LwDID  = (uint16)((((uint16)GcUdsData[1])<<8)|((uint16)GcUdsData[2]));
    GsUdsService.pUDSTxBuff[0] = (uint8) (GcUdsData[0] + UDS_ACK);       /* positive respond ID */

    if(GsUdsService.SecurityLevel == LH_SAFE_LEVEL)
    {
#if (CALIBRATION_EN == STD_ON)
        for(LcCnt = 0;LcCnt < DID_CALIB_NUM;LcCnt++)
        {
            if(LwDID == CalibrationDIDDefine[LcCnt].GwDID)
            {
                if (LwDID == 0xFDC0)//探头参数不需要写入MCU EEP
                {
                    /*此处需要先拷贝数据到DID,然后写入EEPROM*/
                    memcpy((uint8*)CalibrationDIDDefine[LcCnt].GcADDR, &GcUdsData[3], CalibrationDIDDefine[LcCnt].GwDataLen);
                    //写入接口;
                    GetWriteGainData();
                }
                else 
                {
                    /*此处需要先拷贝数据到DID,然后写入EEPROM*/
                    memcpy((uint8*)CalibrationDIDDefine[LcCnt].GcADDR, &GcUdsData[3], CalibrationDIDDefine[LcCnt].GwDataLen-1);
                    (void)Did_WriteData(LcCnt, LwDID, CalibrationDIDDefine[LcCnt].GwDataLen - 1, &GcUdsData[3]);
                    WriteToCodeFlash(CalibrationDIDDefine[LcCnt].GdwADDR, CalibrationDIDDefine[LcCnt].GwDataLen - 1, &GcUdsData[3]);
                }
                break;
            }
            else
            {
                ;
            }
        }
#endif
    }
    else
    {
        for(LcCnt = 0;LcCnt < DID_TOTAL_NUM;LcCnt++)
        {
            if(LwDID == DIDDefinition[LcCnt].GwDID)
            {
                /*此处需要先拷贝数据到DID,然后写入EEPROM*/
                if(0xF1A1 == LwDID)
                {
                    memcpy((uint8*)DIDDefinition[LcCnt].GcADDR,&GcUdsData[3],DIDDefinition[LcCnt].GwDataLen);/*Updating cached data */
                    (void)EELWriteDataImmediate(&GcUdsData[3], DIDDefinition[LcCnt].GdwADDR, DIDDefinition[LcCnt].GwDataLen);/*updata to eel*/
                    
                    DID_F1A1_ECUConfigByteFlag_Save();  /** @brief: F1B1配置字写入标志 */
                    DID_F1A1_ECUConfigByteFlag_Init();  /** @brief: F1B1配置字配置标志初始化 */
                }
                else if(0xF18B == LwDID)
                {
                    GcUdsData[6] = 0x55;  /* @brief: F18B写入标志 */
                    memcpy((uint8*)DIDDefinition[LcCnt].GcADDR,&GcUdsData[3], DID_F18B_EEPROM_LEN);/*Updating cached data */
                    (void)EELWriteDataImmediate(&GcUdsData[3], DID_F18B_ADDRESS, DID_F18B_EEPROM_LEN);/*updata to eel*/
                }
                else if(0xF18C == LwDID)
                {
                    GcUdsData[17] = 0x55;  /* @brief: F18C写入标志 */
                    GcUdsData[18] = 0x55;  /* @brief: F18C写入标志 */
                    memcpy((uint8*)DIDDefinition[LcCnt].GcADDR,&GcUdsData[3],16);
                    (void)EELWriteDataImmediate(&GcUdsData[3], DID_F18C_ADDRESS, DID_F18C_EEPROM_LEN);/*updata to eel*/
                }
                else if (0xF1B1 == LwDID)
                {
                    memcpy((uint8*)DIDDefinition[LcCnt].GcADDR, &GcUdsData[3], DIDDefinition[LcCnt].GwDataLen);/*Updating cached data */
                    (void)EELWriteDataImmediate(&GcUdsData[3], DIDDefinition[LcCnt].GdwADDR, DIDDefinition[LcCnt].GwDataLen);/*updata to eel*/

                    DID_F1B1_Save();  /** @brief: F1B1配置字写入标志 */
                    DID_init_F1B1();  /** @brief: F1B1配置字配置标志初始化 */
                }
				//else if(0xFEFD == LwDID)
    //            {
				//	memcpy((uint8*)DIDDefinition[LcCnt].GcADDR,&GcUdsData[3],DIDDefinition[LcCnt].GwDataLen);/*Updating cached data */
    //                (void)EELWriteDataImmediate(&GcUdsData[3], DIDDefinition[LcCnt].GdwADDR, DIDDefinition[LcCnt].GwDataLen);/*updata to eel*/
				//	Elmos17SnsCtrl_SetDVWorkFlg(GcUdsData[3]);/*updata application data*/
				//}
                else
                {
                    memcpy((uint8*)DIDDefinition[LcCnt].GcADDR,&GcUdsData[3],DIDDefinition[LcCnt].GwDataLen);/*Updating cached data */
                    (void)EELWriteDataImmediate(&GcUdsData[3], DIDDefinition[LcCnt].GdwADDR, DIDDefinition[LcCnt].GwDataLen);/*updata to eel*/
                }
                break;
            }
            else
            {
                ;
            }
        }
    }

    UDS_LongRequestEnd(3,GcUdsData,LcUdsStatus);/*End Long Response Handler*/
}
/*********************************************************************
 * 函数名称:  CheckAPPValidHandle
 *
 * 功能描述:  校验升级前置条件
 *
 * 输入参数:
 *
 * 输出参数:  无
 *
 * 其它说明:
 *
 * 修改日期     版本号       修改人       修改内容
 *
 ********************************************************************/
void ClearDTCInformation(void)
{
    uint8 LcUdsState;
    LcUdsState = UDS_ACK;
    InitDTCStatus();                    /*清除DTC缓存状态*/
    SaveALLDTCImmediately();    /*立即存储DTC*/
    COMSigMgr_RxMonitorInit();/*清除节点相关DTC状态缓存*/
    COMSigMgr_InitSYNCMomiter();/*清除时间同步相关DTC状态缓存*/
    UDS_LongRequestEnd(1,GcUdsData,LcUdsState);/*End Long Response Handler*/
}
/*********************************************************************
 * 函数名称:  CheckProCondition
 *
 * 功能描述:  Programming preconditioning check
 *
 * 输入参数:
 *
 * 输出参数:  无
 *
 * 其它说明:
 *
 * 修改日期     版本号       修改人       修改内容
 *
 ********************************************************************/
void CheckProCondition(void)
{
    uint8 LcUdsState;
    LcUdsState = UDS_ACK;
    Car_SpeedType_Phy LfCar_Speed;
    /*Programming preconditioning check*/
    ReadSysSignal_Car_Speed_phy(&LfCar_Speed);
    Car_SpeedVildType LvCar_SpeedVild;
    ReadSysSignal_Car_SpeedVild(&LvCar_SpeedVild);
    if((LfCar_Speed > 2.0)||(LvCar_SpeedVild != 0))
    {
        GcUdsData[4] = 0x01;/*Speed conditions not met */
    }
   else
   {
        GcUdsData[4] = 0x00;/*Vehicle speed conditions  are met*/
   }
    UDS_LongRequestEnd(5,GcUdsData,LcUdsState);/*End Long Response Handler*/
}
/*********************************************************************
 * 函数名称:  HandleNRC78Event
 *
 * 功能描述:  处理需要响应NRC78的事件
 *
 * 入口参数:  无
 *
 * 输出参数:  无
 *
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 * 
 ********************************************************************/
void HandleNRC78Event(void)
{
    uint8 LcFlag[4] = {0xAA,0xAA,0xAA,0xAA};
    
    switch(GsLongRequest.GcNRC78Event)
    {
        case NRC78_ECURESET:
        {
            /* 延时20ms，等待诊断响应报文发送完成 */
            UserTimerDelayMs(10);/*delay 10ms*/         
            COM_NMAndAPPMsgControl_TX(DISABLE_RX_TX, APP_MSG);/*Disable sending messages*/
            UserTimerDelayMs(10);/*delay 10ms*/
            SystemSoftwareReset(); /* 复位系统 */
            break;
        }
        case NRC78_PROGRAMSEESION:
        {                     
            UserTimerDelayMs(10);/*delay 10ms*/
            COM_NMAndAPPMsgControl_TX(DISABLE_RX_TX, APP_MSG);/*Disable sending messages*/
            UserTimerDelayMs(10);/*delay 10ms*/
            (void)EELWriteDataImmediate(&LcFlag[0], APP_JUMPBOOT_ADDRESS, APP_JUMPBOOT_LEN);
            SystemJumpToSBL(); /* 复位系统 */
            break;
        }
        case NRC78_WRITEDATABYDIDS:
        {
            WriteDataByIdentifier();/*Data Write Processing*/
            break;
        }
        case NRC78_CLEARDTCINFO:
        {
            ClearDTCInformation();/*clear DTC Information*/
            break;
        }   
        case NRC78_CHECKPROCONDITION:
        {
            CheckProCondition();    /*Programming preconditioning check*/
            break;
        }   
        default:
        {
            break;
        }
    }
}
/*********************************************************************
 * 函数名称:  UDS_ServiceManage
 *
 * 功能描述:  接收到诊断服务请求，需要执行诊断服务
 *
 * 入口参数:  无
 *
 * 输出参数:  无
 *
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 * 
 ********************************************************************/
void UDS_ServiceManage(void)
{
    
    if(GcPhysicalChannelRxCompleteFlag == TRUE)
    {
        GcPhysicalChannelRxCompleteFlag = FALSE;/* Set physical addressing processing flag to False */

        GsUdsService.UdsAddrMode = UDS_ADDR_PHYSICAL; /* Set the current request as a physical addressing request */

        DiagServiceManage(GwPhysicalChannelRxLen, GcTpPhyChannelRxBuff);/* Processing of diagnostic requests */
    }

    if(GcFunctionChannelRxCompleteFlag == TRUE)
    {
        GcFunctionChannelRxCompleteFlag = FALSE;/* Set functinal addressing processing flag to False */

        GsUdsService.UdsAddrMode = UDS_ADDR_FUNCTIONAL;/* Set the current request as a functinal addressing request */

        DiagServiceManage(GcFunctionChannelRxLen,GcTpFuncChannelRxBuff);/* Processing of diagnostic requests */
    }
}



