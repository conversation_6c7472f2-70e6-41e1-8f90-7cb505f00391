/**********************************************************************
*   版权所有    : 2015 深圳市豪恩汽车电子装备有限公司
*   项目名称    : TATA Furuto AVM
*   M C U       : R5F10BBG
*   编译环境    : IAR
*   文件名称    : DTCService.c
*   其它说明    : 系统故障诊断服务
*   当前版本    : V0.1
*   作    者  : 20460
*   完成日期    :   
*   内容摘要    :
*   修改记录1   :
*   修改日期    :
*   版 本 号 :
*   修 改 人 :
*   修改内容    :
*   修改记录2   :

***********************************************************************/
/****************************************************************************
* Includes 
******************************************************************************/
#include "DTCService.h"
#include "DTC_Cfg.h"
#include "CAN_UDS.h"
#include "DID.h"
/***************************************************************************** 
* Global objects 
******************************************************************************/
/*********************************************************************
 * 函数名称:  DTCJudge
 *                                  
 * 功能描述:  根据DTC Number，判断系统是否支持该DTC Number，如果支持，
 *            则返回DTC Number的序列号
 *                  
 * 入口参数:  LcDTCNumber
 *
 * 输出参数:  无      
 *          
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 * 
 ********************************************************************/
uint8 DTCNumberJudge(uint32 LcDTCNumber)
{
    uint8 LcI;
    uint8 LcReturnValue = 0xFF;              /* 定义非法的返回值 */          

    for( LcI = 0; LcI < TOTAL_DTC_NUMBER; LcI++ )
    {
        if( LcDTCNumber == GwDTCNumberCfg[LcI] )
        {
            LcReturnValue = LcI;
            break;
        }
    }
    
    return LcReturnValue;
}
/*********************************************************************
 * 函数名称:  ReportNumberOfDTCByStatusMask
 *                                  
 * 功能描述:  根据状态掩码读取DTC个数     
 *                  
 * 入口参数:  StatusMask
 *
 * 输出参数:  无      
 *          
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 * 
 ********************************************************************/
uint16 ReportNumberOfDTCByStatusMask(uint8 StatusMask)
{
    uint8 LcI;
    uint16 LwDTCTotal = 0;

    for(LcI = 0;LcI < TOTAL_DTC_RECORD_NUMBER86;LcI++)
    {
        if((GsDTCInfomationStr[LcI].DTCSTATUS.GcDTCStatus & StatusMask) != 0)
        {
            LwDTCTotal++;
        }
        else
        {
            ;
        }
    }
    return LwDTCTotal;
}

/*********************************************************************
 * 函数名称:  ReportDTCByStatusMask
 *                                  
 * 功能描述:  根据状态掩码读取故障码信息    
 *                  
 * 入口参数:  StatusMask
 *
 * 输出参数:  无      
 *          
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 * 
 ********************************************************************/
void ReportDTCByStatusMask(uint8 StatusMask,uint16 *LwLen,uint8 *Buffer)
{
    uint8 LcI;
    uint16 LwCnt = 0;

    if(Buffer != NULL)
    {
        for(LcI=0;LcI<TOTAL_DTC_RECORD_NUMBER86;LcI++)
        {
            if(((GsDTCInfomationStr[LcI].DTCSTATUS.GcDTCStatus & StatusMask) != 0))
            {
                Buffer[LwCnt++] = (uint8)(GwDTCNumberCfg[LcI]>>16);
                Buffer[LwCnt++] = (uint8)(GwDTCNumberCfg[LcI]>>8);
                Buffer[LwCnt++] = (uint8)(GwDTCNumberCfg[LcI]);
                Buffer[LwCnt++] = GsDTCInfomationStr[LcI].DTCSTATUS.GcDTCStatus;
            }
        }
        *LwLen = LwCnt;
    }
}


/*********************************************************************
 * 函数名称:  ReportSupportedDTC
 *                                  
 * 功能描述:  根据状态掩码读取故障码信息    
 *                  
 * 入口参数:  
 *
 * 输出参数:  无      
 *          
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 * 
 ********************************************************************/
void ReportSupportedDTC(uint16 *LwLen,uint8 *Buffer)
{
    uint8 LcI;
    uint16 LwCnt = 0;

    if(Buffer != NULL)
    {
        for(LcI=0; LcI < TOTAL_DTC_NUMBER; LcI++)
        {
            Buffer[LwCnt++] = (uint8)(GwDTCNumberCfg[LcI]>>16);
            Buffer[LwCnt++] = (uint8)(GwDTCNumberCfg[LcI]>>8);
            Buffer[LwCnt++] = (uint8)(GwDTCNumberCfg[LcI]);
            if(LcI < TOTAL_DTC_RECORD_NUMBER86)
            {
                /*Only 79 DTCs will not be recorded in LEEA_X01 project*/
                Buffer[LwCnt++] = GsDTCInfomationStr[LcI].DTCSTATUS.GcDTCStatus;
            }
            else
            {
                /*Other DTC was not recorded, and it'status is 0*/
                Buffer[LwCnt++] = 0;
            }
        }
        *LwLen = LwCnt;
    }
}

/*********************************************************************
 * 函数名称:  ReportDTCExtendedDataByDTCNumber
 *                                  
 * 功能描述:  根据DTC码读取扩展数据    
 *                  
 * 入口参数:  DTCNumber
 *
 * 输出参数:  无      
 *          
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 * 
 ********************************************************************/
void ReportDTCExtendedDataByDTCNumber(uint8 LcExtrecordNum,uint32 LwDTCNumber,uint16 *LwLen,uint8 *LcData)
{
    uint8 LcDTCOrder;
    uint8 LcCnt = 0;

    LcDTCOrder = DTCNumberJudge(LwDTCNumber);

    if( LcDTCOrder < TOTAL_DTC_NUMBER )
    {
        if(LcDTCOrder < TOTAL_DTC_RECORD_NUMBER86)
        {
            /*Check if the DTC was record or not*/
            if((GsDTCInfomationStr[LcDTCOrder].DTCSTATUS.GcDTCStatus & 0x09) != 0)
            {
                /*The DTC was recorded, report extended data*/
                LcData[LcCnt++] = (uint8)(GwDTCNumberCfg[LcDTCOrder]>>16);
                LcData[LcCnt++] = (uint8)(GwDTCNumberCfg[LcDTCOrder]>>8);
                LcData[LcCnt++] = (uint8)(GwDTCNumberCfg[LcDTCOrder]);
                LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].DTCSTATUS.GcDTCStatus;
                
             //   LcData[LcCnt++] = LcExtrecordNum;         /* DTCExtendedDataRecordNumber */
                if(LcExtrecordNum == 0x01)
                {
                    LcData[LcCnt++] = 0x01;
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcDTCOccurrenceCounter;
                    *LwLen = LcCnt;
                }
                else if(LcExtrecordNum == 0x02)
                {
                    LcData[LcCnt++] = 0x02;
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcAgingCounter;
                    *LwLen = LcCnt;             
                }
                else if(LcExtrecordNum == 0xFF)
                {
                    LcData[LcCnt++] = 0x01;
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcDTCOccurrenceCounter;
                    LcData[LcCnt++] = 0x02;
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcAgingCounter;            
                    *LwLen = LcCnt;             
                }   
                else
                {
                    *LwLen = 0; /* 无效的拓展码*/
                }
            }
            else
            {
                if((LcExtrecordNum != 0x01) && (LcExtrecordNum != 0x02) && (LcExtrecordNum != 0xFF))
                {
                    *LwLen = 0; /* 无效的拓展码*/
                }
                else
                {
                    /*The DTC was not recorded, there no extended data to report*/
                    LcData[LcCnt++] = (uint8)(GwDTCNumberCfg[LcDTCOrder]>>16);
                    LcData[LcCnt++] = (uint8)(GwDTCNumberCfg[LcDTCOrder]>>8);
                    LcData[LcCnt++] = (uint8)(GwDTCNumberCfg[LcDTCOrder]);
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].DTCSTATUS.GcDTCStatus;
                    *LwLen = LcCnt;
                }
            }
        }
        else
        {
            /*The DTC could not record, there no extended data to report*/
            LcData[LcCnt++] = (uint8)(GwDTCNumberCfg[LcDTCOrder]>>16);
            LcData[LcCnt++] = (uint8)(GwDTCNumberCfg[LcDTCOrder]>>8);
            LcData[LcCnt++] = (uint8)(GwDTCNumberCfg[LcDTCOrder]);
            LcData[LcCnt++] = 0;
            *LwLen = LcCnt;
        }
    }
    else
    {
        *LwLen = 0; /* 无效的DTC请求 */
    }
}

/*********************************************************************
 * 函数名称:  ReportDTCSnapshotRecordByDTCNumber
 *                                  
 * 功能描述:  报告系统支持的DTC    
 *                  
 * 入口参数:  void
 *
 * 输出参数:  无      
 *          
 * 其它说明:  无
 *
 * 修改日期     版本号       修改人       修改内容
 * 
 ********************************************************************/
void ReportDTCSnapshotRecordByDTCNumber(uint8 LcRecordNum,uint32 LwDTCNumber,uint16 *LwLen,uint8 *LcData)
{
    uint8 LcDTCOrder;       /* DTC的序号 */
    uint8 LcCnt = 0;
    uint8 LcCntSnap = 0;
    LcDTCOrder = DTCNumberJudge(LwDTCNumber);

    if( LcDTCOrder < TOTAL_DTC_NUMBER )         /* 有效的DTCNumber&&有效的扩展数据序号 */
    {
        if(LcDTCOrder < TOTAL_DTC_RECORD_NUMBER86)
        {
            /*Check if the DTC was record or not*/
            if((GsDTCInfomationStr[LcDTCOrder].DTCSTATUS.GcDTCStatus & 0x09) != 0)
            {
                if(LcRecordNum == 1)
                {
                    LcData[LcCnt++] = (uint8)(GwDTCNumberCfg[LcDTCOrder]>>16);
                    LcData[LcCnt++] = (uint8)(GwDTCNumberCfg[LcDTCOrder]>>8);
                    LcData[LcCnt++] = (uint8)(GwDTCNumberCfg[LcDTCOrder]);
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].DTCSTATUS.GcDTCStatus;
                    LcData[LcCnt++] = LcRecordNum;
                    
                    LcData[LcCnt++] = SNAPSHOT_NUM;     /* DTC SnapshotRecord 的个数 */

                    LcData[LcCnt++] = 0xF1;        /* 车速及其有效性*/
                    LcData[LcCnt++] = 0x00;
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];   
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];

                    LcData[LcCnt++] = 0xF1;        /* 工作电压*/
                    LcData[LcCnt++] = 0x01;

                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];
                    
                    LcData[LcCnt++] = 0xF1;        /* 电源模式*/
                    LcData[LcCnt++] = 0x02;

                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];

                    LcData[LcCnt++] = 0xF1;        /* 总里程*/
                    LcData[LcCnt++] = 0x03;

                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];   

                    LcData[LcCnt++] = 0xF1;        /* 时间*/
                    LcData[LcCnt++] = 0x04;

                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];               
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++]; 
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];               
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++]; 
                    
                    LcData[LcCnt++] = 0xF1;        /*逻辑档位*/
                    LcData[LcCnt++] = 0x05;

                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];        

                    LcData[LcCnt++] = 0xF1;        /*Event id*/
                    LcData[LcCnt++] = 0x06;

                    LcData[LcCnt++] = 0;
                    LcData[LcCnt++] = 0;
                    LcData[LcCnt++] = 0;
                    LcData[LcCnt++] = 0;
                    LcData[LcCnt++] = 0;
                    LcData[LcCnt++] = 0;
                    LcData[LcCnt++] = 0;
                    LcData[LcCnt++] = 0;
                    LcData[LcCnt++] = 0;
                    LcData[LcCnt++] = 0;

                    *LwLen = LcCnt;
                }
                else if(LcRecordNum == 2) 
                {
                    LcData[LcCnt++] = (uint8)(GwDTCNumberCfg[LcDTCOrder]>>16);
                    LcData[LcCnt++] = (uint8)(GwDTCNumberCfg[LcDTCOrder]>>8);
                    LcData[LcCnt++] = (uint8)(GwDTCNumberCfg[LcDTCOrder]);
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].DTCSTATUS.GcDTCStatus;
                    LcData[LcCnt++] = LcRecordNum;
                    
                    LcData[LcCnt++] = SNAPSHOT_NUM;     /* DTC SnapshotRecord 的个数 */

                    LcData[LcCnt++] = 0xF1;        /* 车速及其有效性*/
                    LcData[LcCnt++] = 0x00;
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];   
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];

                    LcData[LcCnt++] = 0xF1;        /* 工作电压*/
                    LcData[LcCnt++] = 0x01;

                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];
                    
                    LcData[LcCnt++] = 0xF1;        /* 电源模式*/
                    LcData[LcCnt++] = 0x02;

                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];

                    LcData[LcCnt++] = 0xF1;        /* 总里程*/
                    LcData[LcCnt++] = 0x03;

                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];   

                    LcData[LcCnt++] = 0xF1;        /* 时间*/
                    LcData[LcCnt++] = 0x04;

                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];               
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++]; 
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];               
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++]; 
                    
                    LcData[LcCnt++] = 0xF1;        /*逻辑档位*/
                    LcData[LcCnt++] = 0x05;

                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];      

                    LcData[LcCnt++] = 0xF1;        /*Event id*/
                    LcData[LcCnt++] = 0x06;

                    LcData[LcCnt++] = 0;
                    LcData[LcCnt++] = 0;
                    LcData[LcCnt++] = 0;
                    LcData[LcCnt++] = 0;
                    LcData[LcCnt++] = 0;
                    LcData[LcCnt++] = 0;
                    LcData[LcCnt++] = 0;
                    LcData[LcCnt++] = 0;
                    LcData[LcCnt++] = 0;
                    LcData[LcCnt++] = 0;

                    *LwLen = LcCnt;
                }
                else if(LcRecordNum == 0xFF)
                {
                    LcData[LcCnt++] = (uint8)(GwDTCNumberCfg[LcDTCOrder]>>16);
                    LcData[LcCnt++] = (uint8)(GwDTCNumberCfg[LcDTCOrder]>>8);
                    LcData[LcCnt++] = (uint8)(GwDTCNumberCfg[LcDTCOrder]);
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].DTCSTATUS.GcDTCStatus;
                    LcData[LcCnt++] = 0x01;
                    /*01*/
                    LcData[LcCnt++] = SNAPSHOT_NUM;     /* DTC SnapshotRecord 的个数 */

                    LcData[LcCnt++] = 0xF1;        /* 车速及其有效性*/
                    LcData[LcCnt++] = 0x00;
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];   
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];

                    LcData[LcCnt++] = 0xF1;        /* 工作电压*/
                    LcData[LcCnt++] = 0x01;

                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];
                    
                    LcData[LcCnt++] = 0xF1;        /* 电源模式*/
                    LcData[LcCnt++] = 0x02;

                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];

                    LcData[LcCnt++] = 0xF1;        /* 总里程*/
                    LcData[LcCnt++] = 0x03;

                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];   

                    LcData[LcCnt++] = 0xF1;        /* 时间*/
                    LcData[LcCnt++] = 0x04;

                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];               
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++]; 
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];               
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++]; 
                    
                    LcData[LcCnt++] = 0xF1;        /*逻辑档位*/
                    LcData[LcCnt++] = 0x05;

                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData_First[LcCntSnap++];               
                    /*02*/
                    LcData[LcCnt++] = 0x02;
                    
                    LcData[LcCnt++] = SNAPSHOT_NUM;     /* DTC SnapshotRecord 的个数 */
                    
                    LcCntSnap = 0; /*Array index should be 0 for DTC record number 02*/

                    LcData[LcCnt++] = 0xF1;        /* 车速及其有效性*/
                    LcData[LcCnt++] = 0x00;
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];   
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];

                    LcData[LcCnt++] = 0xF1;        /* 工作电压*/
                    LcData[LcCnt++] = 0x01;

                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];
                    
                    LcData[LcCnt++] = 0xF1;        /* 电源模式*/
                    LcData[LcCnt++] = 0x02;

                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];

                    LcData[LcCnt++] = 0xF1;        /* 总里程*/
                    LcData[LcCnt++] = 0x03;

                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];   

                    LcData[LcCnt++] = 0xF1;        /* 时间*/
                    LcData[LcCnt++] = 0x04;

                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];               
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++]; 
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];               
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++]; 
                    
                    LcData[LcCnt++] = 0xF1;        /*逻辑档位*/
                    LcData[LcCnt++] = 0x05;

                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].GcSnapshotData[LcCntSnap++];    

                    LcData[LcCnt++] = 0xF1;        /*Event id*/
                    LcData[LcCnt++] = 0x06;

                    LcData[LcCnt++] = 0;
                    LcData[LcCnt++] = 0;
                    LcData[LcCnt++] = 0;
                    LcData[LcCnt++] = 0;
                    LcData[LcCnt++] = 0;
                    LcData[LcCnt++] = 0;
                    LcData[LcCnt++] = 0;
                    LcData[LcCnt++] = 0;
                    LcData[LcCnt++] = 0;
                    LcData[LcCnt++] = 0;

                    *LwLen = LcCnt;
                }
                else
                {
                    *LwLen = 0;     /* DTC记录序号超出范围 */
                }
            }
            else
            {
                if((LcRecordNum != 0x01) && (LcRecordNum != 0x02) && (LcRecordNum != 0xFF))
                {
                    *LwLen = 0; /* 无效的拓展码*/
                }
                else
                {
                    /*The DTC was not recorded, there no extended data to report*/
                    LcData[LcCnt++] = (uint8)(GwDTCNumberCfg[LcDTCOrder]>>16);
                    LcData[LcCnt++] = (uint8)(GwDTCNumberCfg[LcDTCOrder]>>8);
                    LcData[LcCnt++] = (uint8)(GwDTCNumberCfg[LcDTCOrder]);
                    LcData[LcCnt++] = GsDTCInfomationStr[LcDTCOrder].DTCSTATUS.GcDTCStatus;
                    *LwLen = LcCnt;
                }
            }
        }
        else
        {
            /*The DTC could not record, there no extended data to report*/
            LcData[LcCnt++] = (uint8)(GwDTCNumberCfg[LcDTCOrder]>>16);
            LcData[LcCnt++] = (uint8)(GwDTCNumberCfg[LcDTCOrder]>>8);
            LcData[LcCnt++] = (uint8)(GwDTCNumberCfg[LcDTCOrder]);
            LcData[LcCnt++] = 0;
            *LwLen = LcCnt;
        }
    }
    else 
    {
        *LwLen = 0;     /* 无效的DTC码 */
    }
}

