/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * ApaCalCarCoor.c: 
 * Created on: 2020-12-07 19:09
 * Original designer: 22554
 ******************************************************************************/

#ifndef   __APACALCARCOOR_TYPES_H__
#define   __APACALCARCOOR_TYPES_H__

/******************************************************************************
 * Inclusion of private header files
 ******************************************************************************/
#include "types.h"
#include "CAN_AppSignal_CommonTypes.h"


#ifdef __cplusplus
extern "C"{
#endif
/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/
#define RELATION_MOD_NUM_COOR           10


#define CAR_RUN_FRONT_COOR               0
#define CAR_RUN_REAR_COOR                1
#define CAR_STOP_COOR                    2

#define WHEEL_TURN_LEFT_COOR             0
#define WHEEL_TURN_RIGHT_COOR            1

#define  ANGLE_TO_RADIAN_COOR            (57.2957795 * 10.0)  /* 精确到0.1度 */

/* 自标定次数 */
#define AUTO_CALI_MAX_CNT                5u


typedef enum
{
    CALI_PARAM_INVALID = 0u,
    CALI_PARAM_VALID,
}CPOS_CaliParamValidType;


/* 沿车子前进方向为X正方向，车身左手边Y轴正方向
      角度-pi  ~  pi，角度沿中心轴往左边打前进为++ */
typedef struct
{
    float fX;
    float fY;
    float fAngle;
    float fDrvDis;
	float TurnRadius;
	float fCosAngle;
	float fSinAngle;
    uint8 u8VehDir;
}CPOS_CurCarPointDataType;

/* 定义计算坐标所需要的数据 */
typedef struct
{
    uint16 u16HLWheelPulse;
    uint16 u16HRWheelPulse;
    uint16 u16HLWheelPulseBak;
    uint16 u16HRWheelPulseBak;

    uint16 u16HLWheelPulseValid;
    uint16 u16HRWheelPulseValid;

    bool u16SteerWheelAngleVaild;
    bool u8StraightFlag;

    uint8 u8RLWheelDir;
    uint8 u8RRWheelDir;
    uint8 u8WheelRunDir;

    uint8 u8EnableCalFlg[RELATION_MOD_NUM_COOR];
    uint8 u8EnableCalFlgID;

    uint8 u8SelectCalWay;
    
    float fLeftDis;
    float fRightDis;
    float fMiddleDis;

    float fCarTurnRadius;          /* 整车转弯半径 */

    uint16 u16EpsAngle;
    float fEpsAnglebak;
    uint8 u8EpsDirection;

    uint16 u16VehicleSpeed;
    Car_GearType enuVehicleGear;
    uint8 u8GearSaveBak;

    float fXChange;        /* X轴的坐标变化量 */
    float fYChange;        /* Y轴的坐标变化量 */
    float fCarBodyAngle[RELATION_MOD_NUM_COOR];   /* 车身角度 */
    float fCarBodyAngleChange;   /* 车身角度的变化量 */

    float fCar_YawAngleBak[RELATION_MOD_NUM_COOR];
    float fCarBodyOriginalAngle[RELATION_MOD_NUM_COOR];

	Wheel_SpeedType u16WheelSpeedRL, u16WheelSpeedRR;
	uint8 u8CaliStatus;
}CPOS_CalPointNeedDataType;



/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/







/**
 * end of group   APAWORKMANAGE_TYPES
 * @}
 */
#ifdef __cplusplus
}
#endif

#endif /* end of __APAWORKMANAGE_TYPES_H__ */
