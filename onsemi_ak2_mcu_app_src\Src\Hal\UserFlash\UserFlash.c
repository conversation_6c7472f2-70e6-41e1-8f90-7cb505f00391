/**********************************************************************
*   版权所有    : 2018 深圳市豪恩汽车电子装备股份有限公司
*   项目名称    : Mahindra Z101 APA
*   M  C  U     : S32K146
*   编译环境    : S32 Design Studio for ARM Version 2018.R1
*   文件名称    : UserEeprom.c
*   其它说明    : 用户使用EEPROM配置,--HAL Layer
*   当前版本    : V0.1
*   作    者    : 20159
*   完成日期    : 2019-07-04
*   内容摘要    : 
*   修改记录    :
*   修改日期    :
*   版 本 号    :
***********************************************************************/

/********************************************************************** 
* Includes 
************************************************************************/ 
#include "Fls.h"
#include "fee.h"
#include "UserFlash.h"
#include "debug.h"
/****************************************************************************** 
Pragma directive 
*************** *****************************************************************/


/****************************************************************************** 
* Global objects 
*************** *****************************************************************/
#if (CALIBRATION_EN == STD_ON)
/******************************************************************
* 函数名称: EraseFlash
*
* 功能描述:
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 无
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
uint8 EraseFlash(uint32 Lu32StartBlockIdx, uint16 Lu16AppBlockSize)
{
    uint8 LcEraseState = FALSE;
    ErrorType LeRetStatus;
    
    LeRetStatus = Fls_Erase(Lu32StartBlockIdx, Lu16AppBlockSize);
    if(LeRetStatus == IS_OK)
    {
        LcEraseState = TRUE;
    }
    return LcEraseState;
}

/******************************************************************
* 函数名称: WriteToFlash
*
* 功能描述:
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明:
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
uint8_t WriteToCodeFlash(uint32_t Lu32WriteStartAddress, uint32_t Lu32WriteDataLength, const uint8_t* pData)
{
	uint32* Lpu32BlockAVal;
	//uint32* Lpu32BlockBVal;
	uint8 Lu8aRWBuf[256];
	uint16 idx = 0u;
	uint8_t LcWriteState = FALSE;
	uint16 temp;
	uint16 Lu16PackageOffset = (Lu32WriteStartAddress >> 8);
	uint16 Lu16ByteOffset = Lu32WriteStartAddress%256;
	uint16 Lu16WDataPackageNum = 0u;
	uint16 Lu16WDataOffset = 0u;
	uint32 Lu32SrcAddress = 0u;
	uint32 Lu32DesAddress = 0u;
	uint16 Lu16LastPackageNum = 0u;
	DI();
	Lpu32BlockAVal = (uint32*)CAL_FLASH_A_FLAG_ADDRESS;
	//Lpu32BlockBVal = (uint32*)CAL_FLASH_B_FLAG_ADDRESS;

	if ((*Lpu32BlockAVal) == CAL_FLASH_VALID_BLOCK_VAL)
	{
		PLFM_DEBUG_PRINT("AAAA");
		Lu32SrcAddress = CAL_AREA_A_START;
		Lu32DesAddress = CAL_AREA_B_START;
		/* 写过A区，A区为备份，数据更新到B区 */
		if (Lu16PackageOffset != 0u)
		{
			for (idx = 0;idx < Lu16PackageOffset;idx++)
			{
				memcpy((void*)&Lu8aRWBuf[0], (const void*)(Lu32SrcAddress + 0x100 * idx), 256u);
				if (IS_OK == Fls_Write((Lu32DesAddress + idx * 0x100), (uint8*)Lu8aRWBuf, 1))
				{
					LcWriteState = TRUE;
				}
				else
				{
					PLFM_DEBUG_PRINT("2 Write Error");
					break;
				}
			}
			Lu32SrcAddress += (0x100 * Lu16PackageOffset);
			Lu32DesAddress += (0x100 * Lu16PackageOffset);
		}
	}
	else
	{
		PLFM_DEBUG_PRINT("BBBB");
		/* 写过B区，B区为备份，数据更新到A区 */
		Lu32SrcAddress = CAL_AREA_B_START;
		Lu32DesAddress = CAL_AREA_A_START;
		/* 写过A区，A区为备份，数据更新到B区 */
		if (Lu16PackageOffset != 0u)
		{
			for (idx = 0;idx < Lu16PackageOffset;idx++)
			{
				memcpy((void*)&Lu8aRWBuf[0], (const void*)(Lu32SrcAddress + 0x100 * idx), 256u);
				if (IS_OK == Fls_Write((Lu32DesAddress + idx * 0x100), (uint8*)Lu8aRWBuf, 1))
				{
					LcWriteState = TRUE;
				}
				else
				{
					PLFM_DEBUG_PRINT("2 Write Error");
					break;
				}
			}
			Lu32SrcAddress += (0x100 * Lu16PackageOffset);
			Lu32DesAddress += (0x100 * Lu16PackageOffset);
		}
	}

	PLFM_DEBUG_PRINT("Src:0x%x Des:0x%x\r\n", Lu32SrcAddress, Lu32DesAddress);

	/* 先根据起始地址，将前面不需更新的数据先缓存到buffer里面 */
	memcpy((void*)&Lu8aRWBuf[0], (const void*)(Lu32SrcAddress), Lu16ByteOffset);
	if (Lu32WriteDataLength > (256 - Lu16ByteOffset))
	{
		temp = 256 - Lu16ByteOffset;
		/* 拷贝原数据到缓存 */
		memcpy((void*)&Lu8aRWBuf[Lu16ByteOffset], (const void*)pData, temp);
		Fls_Write((Lu32DesAddress), (uint8*)Lu8aRWBuf, 1);
		Lu32WriteDataLength -= temp;
		Lu16WDataPackageNum = Lu32WriteDataLength >> 8u;
		Lu16WDataOffset = Lu32WriteDataLength % 256;
		for (idx = 0;idx < Lu16WDataPackageNum;idx++)
		{
			memcpy((void*)&Lu8aRWBuf[0], (const void*)(pData + 0x100 * idx + temp), 256u);
			if (IS_OK == Fls_Write((Lu32DesAddress + 0x100 + idx * 0x100), (uint8*)Lu8aRWBuf, 1))
			{
				LcWriteState = TRUE;
			}
			else
			{
				PLFM_DEBUG_PRINT("2 Write Error");
				break;
			}
		}
		if (Lu16WDataOffset != 0)
		{
			memcpy((void*)&Lu8aRWBuf[0], (const void*)(pData + 0x100 * Lu16WDataPackageNum + temp), Lu16WDataOffset);
			memcpy((void*)&Lu8aRWBuf[Lu16WDataOffset], (const void*)(Lu32SrcAddress + 0x100 * (Lu16WDataPackageNum + 1u) + Lu16WDataOffset), 256 - Lu16WDataOffset);
			Fls_Write((Lu32DesAddress + 0x100 * (Lu16WDataPackageNum + 1)), (uint8*)Lu8aRWBuf, 1);
			/* 拷贝剩余的X区备份数据 */
			Lu16LastPackageNum = BLOCK_256B_PER_32K_PACKAGE_NUM - (Lu16WDataPackageNum + 2) - Lu16PackageOffset - 1;
			for (idx = 0;idx < Lu16LastPackageNum;idx++)
			{
				memcpy((void*)&Lu8aRWBuf[0], (const void*)(Lu32SrcAddress + (Lu16WDataPackageNum + 2 + idx) * 0x100), 256u);
				if (IS_OK == Fls_Write((Lu32DesAddress + (Lu16WDataPackageNum + 2 + idx) * 0x100), (uint8*)Lu8aRWBuf, 1))
				{
					LcWriteState = TRUE;
				}
				else
				{
					PLFM_DEBUG_PRINT("2 Write Error");
					break;
				}
			}
		}
	}
	else
	{
		PLFM_DEBUG_PRINT("step2\r\n");
		/* 拷贝原数据到缓存 */
		memcpy((void*)&Lu8aRWBuf[Lu16ByteOffset], (const void*)pData, Lu32WriteDataLength);
		PLFM_DEBUG_PRINT("Lu16ByteOffset:%d\r\n", Lu16ByteOffset);
		temp = 256 - Lu32WriteDataLength - Lu16ByteOffset;
		PLFM_DEBUG_PRINT("temp:%d\r\n", temp);
		/* 判定该包数据是否结束 */
		if (temp != 0)//该包数据结束
		{
			memcpy((void*)&Lu8aRWBuf[256 - temp], (const void*)(Lu32SrcAddress + 256 - temp), temp);
		}

		PLFM_DEBUG_PRINT("write rst:%d\r\n", Fls_Write((Lu32DesAddress), (uint8*)Lu8aRWBuf, 1));
		/* 拷贝剩余的X区备份数据 */
		Lu16LastPackageNum = BLOCK_256B_PER_32K_PACKAGE_NUM - 1 - Lu16PackageOffset - 1;
		PLFM_DEBUG_PRINT("PackageNum:%d\r\n", Lu16LastPackageNum);
		for (idx = 0;idx < Lu16LastPackageNum;idx++)
		{
			memcpy((void*)&Lu8aRWBuf[0], (const void*)(Lu32SrcAddress + 0x100 + idx * 0x100), 256u);
			if (IS_OK == Fls_Write((Lu32DesAddress + 0x100 + idx * 0x100), (uint8*)Lu8aRWBuf, 1))
			{
				LcWriteState = TRUE;
			}
			else
			{
				PLFM_DEBUG_PRINT("2 Write Error");
				break;
			}
		}
	}

	if (LcWriteState == FALSE)
	{
		if ((Lu32SrcAddress - (0x100 * Lu16PackageOffset)) == CAL_AREA_A_START)
		{
			 Fls_Erase(CAL_FLASH_B_START_BLOCK, CAL_FLASH_B_BLOCK_LEN);
			 PLFM_DEBUG_PRINT("1Erase B");
		}
		else
		{
			 Fls_Erase(CAL_FLASH_A_START_BLOCK, CAL_FLASH_A_BLOCK_LEN);
			 PLFM_DEBUG_PRINT("1Erase A");
		}
		PLFM_DEBUG_PRINT("3 Write Error");
	}
	else
	{
		if ((Lu32SrcAddress - (0x100 * Lu16PackageOffset)) == CAL_AREA_A_START)
		{
			Lu8aRWBuf[0] = (uint8)CAL_FLASH_VALID_BLOCK_VAL;
			Lu8aRWBuf[1] = (uint8)(CAL_FLASH_VALID_BLOCK_VAL >> 8);
			Lu8aRWBuf[2] = (uint8)(CAL_FLASH_VALID_BLOCK_VAL >> 16);
			Lu8aRWBuf[3] = (uint8)(CAL_FLASH_VALID_BLOCK_VAL >> 24);
			Fls_Write(CAL_FLASH_B_FLAG_ADDRESS, (uint8*)Lu8aRWBuf, 1);
			PLFM_DEBUG_PRINT("Erase A rst:%d\r\n", Fls_Erase(CAL_FLASH_A_START_BLOCK, CAL_FLASH_A_BLOCK_LEN));
			PLFM_DEBUG_PRINT("Write B flag %x:%x\r\n", CAL_FLASH_B_FLAG_ADDRESS, *((uint32*)CAL_FLASH_B_FLAG_ADDRESS));
		}
		else
		{
			Lu8aRWBuf[0] = (uint8)CAL_FLASH_VALID_BLOCK_VAL;
			Lu8aRWBuf[1] = (uint8)(CAL_FLASH_VALID_BLOCK_VAL >> 8);
			Lu8aRWBuf[2] = (uint8)(CAL_FLASH_VALID_BLOCK_VAL >> 16);
			Lu8aRWBuf[3] = (uint8)(CAL_FLASH_VALID_BLOCK_VAL >> 24);
			Fls_Write(CAL_FLASH_A_FLAG_ADDRESS, (uint8*)Lu8aRWBuf, 1);
			PLFM_DEBUG_PRINT("Erase B rst:%d\r\n", Fls_Erase(CAL_FLASH_B_START_BLOCK, CAL_FLASH_B_BLOCK_LEN));
			PLFM_DEBUG_PRINT("Write A flag %x:%x\r\n", CAL_FLASH_A_FLAG_ADDRESS, *((uint32*)CAL_FLASH_A_FLAG_ADDRESS));
		}
	}
	EI();
	return LcWriteState;
}
/******************************************************************************
* 函数名称: ReadToCodeFlash
* 设计作者:
* 设计日期: 2023-11-24 14:17
* 功能描述:
* 参数描述: 无
* 返回类型: TRUE:read success FALSE:read fail
* 修改人员: 无
* 修改日期: 无
* 修改内容: 无
*******************************************************************************/
uint8_t ReadToCodeFlash(uint32_t Lu32ReadStartAddress, uint32_t Lu32ReadDataLength, uint8_t* pData)
{
	uint8_t LcReadState = FALSE;
	uint8_t* pReadAddr = (uint8_t*)Lu32ReadStartAddress;

    if ((NULL == pData) )
	{
		return LcReadState;
	}

	LcReadState = TRUE;
	memcpy((void*)pData, (const void*)&pReadAddr[0], Lu32ReadDataLength);

	return LcReadState;
}
#endif
/*****************************************************END EEPROM.c*********************************************************/
