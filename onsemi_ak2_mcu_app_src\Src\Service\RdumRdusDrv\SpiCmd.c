/******************************************************************************
 * @file       SpiCmd.c
 * @brief
 * @date       2025-03-21 14:19:49
 * <AUTHOR>
 * @copyright  Longhorn Automotive Electronics Technology (Xiamen) Co., Ltd.
 *****************************************************************************/


/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "SpiCmd.h"
#include "BaseDrv.h"
#include "RdumRdusCrm.h"


/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/
/* 创建solt数组表，可以通过不同slot选择不同初始值 */
const uint8 CRC8_C2_INIT_TABLE[MAX_SLOT_COUNT] =
    {
        CRC8_C2_INIT_SLOT1,
        CRC8_C2_INIT_SLOT2,
        CRC8_C2_INIT_SLOT3,
        CRC8_C2_INIT_SLOT4,
        CRC8_C2_INIT_SLOT5,
        CRC8_C2_INIT_SLOT6,
        CRC8_C2_INIT_SLOT7,
        CRC8_C2_INIT_SLOT8,
};

/******************************************************************************
 * @Type Definitions
 *****************************************************************************/



/******************************************************************************
 * @Function Declaration
 *****************************************************************************/



/******************************************************************************
 * @Const Definitions
 *****************************************************************************/



/******************************************************************************
 * @Variable Definitions
 *****************************************************************************/
#pragma location = "R_DMA_DATA"
uint8 gCmdRxBuf[6] = {0};



/******************************************************************************
 * @Function Definitions
 *****************************************************************************/


/******************************************************************************
 * @brief      无操作命令 - 命令0x01
 * @param[in]  Dsi3Ch
 * @return
 * <AUTHOR>
 * @date       2025-03-24 14:33:00
 * @note       主要用于读取返回数据
 *****************************************************************************/
SpiTransReturn_t SpiCmd_NoOperation(Dsi3Channel_t Dsi3Ch, uint8 Addr, uint8 *UserData)
{
    SpiProtocol_t SpiTxData = {0};
    SpiTxSeqId_t TxSeqIndex;

    SpiTxData.ReadAndWrite_t.Channel = Dsi3Ch;
    SpiTxData.ReadAndWrite_t.Command = NO_OPERATION;
    SpiTxData.ReadAndWrite_t.Addr = Addr;
    SpiTxData.ReadAndWrite_t.Crc = BaseDrv_Crc8Calculate(SpiTxData.SpiCmdData, SPI_CMD_CRC_LENS, CRC8_C2_INIT_SPI_AND_CRC_SD);

    TxSeqIndex = SpiCom_AsyncTransferEx(SpiTxData.SpiCmdData, SPI_CMD_LENS,
                                        1, 0, 3, 0, UserData);
    if (TxSeqIndex == SPI_TX_SEQ_NULL)
    {
        return SPI_TRANS_INVALID;
    }
    else
    {
        return SPI_TRANS_SUCC;
    }
}


/******************************************************************************
 * @brief      往地址写数据指令 - 命令0x02
 * @param[in]  Dsi3Ch
 * @param[in]  Addr
 * @param[in]  Data
 * @return
 * <AUTHOR>
 * @date       2025-03-24 14:34:24
 * @note
 *****************************************************************************/
SpiTransReturn_t SpiCmd_WriteByAddress(Dsi3Channel_t Dsi3Ch, uint8 Addr, uint8 *TxData)
{
    SpiProtocol_t SpiTxData = {0};
    SpiTxSeqId_t TxSeqIndex;

    SpiTxData.ReadAndWrite_t.Kac = 0;
    SpiTxData.ReadAndWrite_t.Channel = Dsi3Ch;
    SpiTxData.ReadAndWrite_t.Command = WRITE_BY_ADDRESS;
    SpiTxData.ReadAndWrite_t.Addr = Addr;
    memcpy(&SpiTxData.ReadAndWrite_t.Payload, TxData, sizeof(SpiTxData.ReadAndWrite_t.Payload));
    SpiTxData.ReadAndWrite_t.Crc = BaseDrv_Crc8Calculate(SpiTxData.SpiCmdData, SPI_CMD_CRC_LENS, CRC8_C2_INIT_SPI_AND_CRC_SD);

    TxSeqIndex = SpiCom_AsyncTransferEx(SpiTxData.SpiCmdData, SPI_CMD_LENS,
                                        1, 0, 3, 1, NULL);
    if (TxSeqIndex == SPI_TX_SEQ_NULL)
    {
        return SPI_TRANS_INVALID;
    }
    else
    {
        return SPI_TRANS_SUCC;
    }
}

/******************************************************************************
 * @brief      读取地址数据指令 - 命令0x03
 * @param[in]  Dsi3Ch
 * @param[in]  Addr
 * @return
 * <AUTHOR>
 * @date       2025-03-24 14:34:58
 * @note
 *****************************************************************************/
SpiTransReturn_t SpiCmd_ReadByAddress(Dsi3Channel_t Dsi3Ch, uint8 Addr)
{
    SpiProtocol_t SpiTxData = {0};
    SpiTxSeqId_t TxSeqIndex;

    SpiTxData.ReadAndWrite_t.Channel = Dsi3Ch;
    SpiTxData.ReadAndWrite_t.Command = READ_BY_ADDRESS;
    SpiTxData.ReadAndWrite_t.Addr = Addr;
    SpiTxData.ReadAndWrite_t.Crc = BaseDrv_Crc8Calculate(SpiTxData.SpiCmdData, SPI_CMD_CRC_LENS, CRC8_C2_INIT_SPI_AND_CRC_SD);

    TxSeqIndex = SpiCom_AsyncTransferEx(SpiTxData.SpiCmdData, SPI_CMD_LENS,
                                        1, 0, 3, 1, NULL);
    if (TxSeqIndex == SPI_TX_SEQ_NULL)
    {
        return SPI_TRANS_INVALID;
    }
    else
    {
        return SPI_TRANS_SUCC;
    }
}

/******************************************************************************
 * @brief      获取CRM的响应指令 - 命令0x04
 * @param[in]  Dsi3Ch
 * @param[in]  IsResponseNeeded
 * @param[in]  DataLen 需要接收的RDUS寄存器数据长度
 * @param[in]  UserData
 * @return
 * <AUTHOR>
 * @date       2025-03-24 14:35:41
 * @note       最大20字节解析后的数据
 *****************************************************************************/
SpiTransReturn_t SpiCmd_GetCrmResp(Dsi3Channel_t Dsi3Ch, uint8 IsResponseNeeded, uint8 DataLen, uint8 *UserData)
{
    SpiProtocol_t SpiTxData = {0};
    SpiTxSeqId_t TxSeqIndex;
    uint8 TxData[SPI_CMD_LENS + CRM_MAX_DATA_SIZE] = {0};

    /* 构建GET_CRM_RESP命令，发送命令示例: 0x04 0x00 0x00 0x00 0x00 0x00 0x1f 0x00 0x00 0x00 0x00根据前面发送命令需要读取rdus哪个寄存器值，数量多长就补几个零 */
    SpiTxData.ReadAndWrite_t.Channel = Dsi3Ch;
    SpiTxData.ReadAndWrite_t.Command = GET_CRM_RESP;
    SpiTxData.ReadAndWrite_t.Crc = BaseDrv_Crc8Calculate(SpiTxData.SpiCmdData, SPI_CMD_CRC_LENS, CRC8_C2_INIT_SPI_AND_CRC_SD);

    /* 接收几个数据末尾补偿几个数据（最大20个字节），补偿数据可为零 */
    memcpy(TxData, SpiTxData.SpiCmdData, SPI_CMD_LENS);

    TxSeqIndex = SpiCom_AsyncTransferEx(TxData, SPI_CMD_LENS + DataLen,
                                        1, 0, 3, IsResponseNeeded, UserData);
    if (TxSeqIndex == SPI_TX_SEQ_NULL)
    {
        return SPI_TRANS_INVALID;
    }
    else
    {
        return SPI_TRANS_SUCC;
    }
}


/******************************************************************************
 * @brief      获取PDCM指令-命令0x05
 * @param[in]  Dsi3Ch
 * @param[in]  SlotIndex
 * @return
 * <AUTHOR>
 * @date       2025-03-24 14:36:11
 * @note
 *****************************************************************************/
SpiTransReturn_t SpiCmd_GetPdcmResp(Dsi3Channel_t Dsi3Ch, uint8 SlotIndex, uint16 DelayMs, uint8 *UserData)
{
    SpiProtocol_t SpiTxData = {0};
    SpiTxSeqId_t TxSeqIndex;
    uint8 TxData[SPI_CMD_LENS + SLOT_DATA_SIZE + 1] = {0};

    SpiTxData.ReadAndWrite_t.Channel = Dsi3Ch;
    SpiTxData.ReadAndWrite_t.Command = GET_PDCM_RESP;
    SpiTxData.GetPdcmResp_t.SlotIndex = SlotIndex;
    SpiTxData.ReadAndWrite_t.Crc = BaseDrv_Crc8Calculate(SpiTxData.SpiCmdData, SPI_CMD_CRC_LENS, CRC8_C2_INIT_SPI_AND_CRC_SD);

    /* 固定接收20+1crc诊断个数据末尾补偿数据，补偿数据可为零 */
    memcpy(TxData, SpiTxData.SpiCmdData, SPI_CMD_LENS);

    TxSeqIndex = SpiCom_AsyncTransferEx(TxData, sizeof(TxData),
                                        1, DelayMs, 3, 0, UserData);
    if (TxSeqIndex == SPI_TX_SEQ_NULL)
    {
        return SPI_TRANS_INVALID;
    }
    else
    {
        return SPI_TRANS_SUCC;
    }
}


/******************************************************************************
 * @brief      启动定时器指令 - 命令0x06
 * @param[in]  Dsi3Ch
 * @param[in]  NewTime
 * @return
 * <AUTHOR>
 * @date       2025-03-24 14:37:25
 * @note
 *****************************************************************************/
SpiTransReturn_t SpiCmd_RunPauseCycle(Dsi3Channel_t Dsi3Ch, uint16 NewTime)
{
    SpiProtocol_t SpiTxData = {0};
    SpiTxSeqId_t TxSeqIndex;

    SpiTxData.ReadAndWrite_t.Channel = Dsi3Ch;
    SpiTxData.ReadAndWrite_t.Command = RUN_PAUSE_CYCLE;
    SpiTxData.RunPauseCycle_t.NewTimerHigh = NewTime >> 1;
    SpiTxData.RunPauseCycle_t.NewTimerLow = NewTime & 0x01;
    SpiTxData.ReadAndWrite_t.Crc = BaseDrv_Crc8Calculate(SpiTxData.SpiCmdData, SPI_CMD_CRC_LENS, CRC8_C2_INIT_SPI_AND_CRC_SD);

    TxSeqIndex = SpiCom_AsyncTransferEx(SpiTxData.SpiCmdData, SPI_CMD_LENS,
                                        1, 0, 3, 1, NULL);
    if (TxSeqIndex == SPI_TX_SEQ_NULL)
    {
        return SPI_TRANS_INVALID;
    }
    else
    {
        return SPI_TRANS_SUCC;
    }
}

/******************************************************************************
 * @brief      获取BRC指令 - 命令0x07
 * <AUTHOR>
 * @date       2025-03-24 14:03:73
 * @note       获取最大8个slot
 *****************************************************************************/
SpiTransReturn_t SpiCmd_GetAllBrc(Dsi3Channel_t Dsi3Ch, uint8 SlotCount, uint16 DelayMs, uint8 *UserData)
{
    SpiProtocol_t SpiTxData = {0};
    SpiTxSeqId_t TxSeqIndex;
    uint8 TxData[SPI_DATA_BUFF_MAX_NUM] = {0};
    uint8 AllSlotDuration[2 * MAX_SLOT_COUNT] = {0};
    uint16 TxDataLen;
    SlotData_t SlotData = {0};
    uint8 i;

    /* 构建GET_ALL_BRC命令的SPI头 */
    SpiTxData.ReadAndWrite_t.Channel = Dsi3Ch;
    SpiTxData.ReadAndWrite_t.Command = GET_ALL_BRC;
    SpiTxData.ReadAndWrite_t.Crc = BaseDrv_Crc8Calculate(SpiTxData.SpiCmdData, SPI_CMD_CRC_LENS, CRC8_C2_INIT_SPI_AND_CRC_SD);

    /* 根据发波需要几个探头有收发数据就补偿几个数据，一个探头20字节数据，最后加一个SD_CRC8 */
    TxDataLen = SlotCount * 20 + 1 + SPI_CMD_LENS;
    memcpy(TxData, SpiTxData.SpiCmdData, SPI_CMD_LENS);

    /* 填充slot数据 */
    for (i = 0; i < SlotCount; i++)
    {
        SlotData.SlotCrc = BaseDrv_Crc8Calculate((const uint8 *)&SlotData, SLOT_DATA_SIZE - sizeof(SlotData.SlotDuration) - 1, CRC8_C2_INIT_TABLE[i]);
        SlotData.Kac = i * 2;
        AllSlotDuration[i * 2] = 0xA6;
        AllSlotDuration[i * 2 + 1] = 0x0C;
        memcpy(&TxData[SPI_CMD_LENS + i * SLOT_DATA_SIZE], &SlotData, SLOT_DATA_SIZE);
    }
    /* 计算最后的CRC_SD:为所有SlotCount中的SlotDuration一起计算*/
    TxData[TxDataLen - 1] = BaseDrv_Crc8Calculate(AllSlotDuration, SlotCount * 2, CRC8_C2_INIT_SPI_AND_CRC_SD);

    TxSeqIndex = SpiCom_AsyncTransferEx(TxData, TxDataLen,
                                            1, DelayMs, 3, 0, UserData);
    if (TxSeqIndex == SPI_TX_SEQ_NULL)
    {
        return SPI_TRANS_INVALID;
    }
    else
    {
        return SPI_TRANS_SUCC;
    }
}
