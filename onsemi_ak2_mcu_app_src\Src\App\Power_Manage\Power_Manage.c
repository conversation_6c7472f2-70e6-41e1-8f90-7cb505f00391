/******************************************************************************
* MODIFICATION LOG :                                                           
*******************************************************************************
*                                                                              
* !Designed by     : Fris                            !Date : 2010.10.30        
* !Coded by        : Fris                            !Date : 2010.10.30        
*                                                                              
* Rev 1.0    2020.10.30                              Initial release           
*******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "Power_Manage.h"
#include "PowerSingalManage.h"
#include "Power_ManageCfg.h"
#include "debug.h"

#define AD_VALUE_FILTER_NUM			(5U)

/******************************************************************************/
/**
* @brief: Global definition
**/
/******************************************************************************/
volatile uint16 GwADMeanValue = 0;
volatile uint16 Gu16ADMomentaryValue = 0;
uint16 Gu16PwrADVal[PowerAdcIdxNum] = {0};
PowerMonitorCfg_t *GpPowerData = NULL;

/******************************************************************
* 函数名称:	PowerManage_Init
*
* 功能描述: 
*
* 输入参数:	无
*
* 输出参数:	无
*
* 返 回 值:	无
*
* 其它说明: 无
*
* 修改日期		版本号		修改人		修改内容

**********************************************************************/
void PowerManage_Init(void)
{
    GpPowerData = &GstrPM_Config[0];
}

/******************************************************************
* 函数名称:	PowerManageFunc
*
* 功能描述: 
*
* 输入参数:	无
*
* 输出参数:	无
*
* 返 回 值:	无
*
* 其它说明: 无
*
* 修改日期		版本号		修改人		修改内容

**********************************************************************/ 
static void PowerManageFunc(uint16 Lu16PwrAdcValue)
{
    uint8 i = 0;
	PowerMonitorCfg_t *LpPowerData = NULL;
    Signal_VoltageStatusType LenuVoltageStatusTemp = NORMAL_VOLTAGE;
	static uint16 ADValueBuf[AD_VALUE_FILTER_NUM] = {0};
	static uint8  ADValueIdx = 0;	
	static uint8  ADValueValidNum = 0;
	uint32 ADValueSum = 0;
	
    /* 采集电压AD值 */
    Gu16ADMomentaryValue = Lu16PwrAdcValue;

	ADValueBuf[ADValueIdx] = Gu16ADMomentaryValue;
	ADValueIdx++;
	ADValueValidNum++;

	if(ADValueValidNum > AD_VALUE_FILTER_NUM)
	{
		ADValueValidNum = AD_VALUE_FILTER_NUM;
	}
	
	if(AD_VALUE_FILTER_NUM == ADValueIdx)
	{
		ADValueIdx = 0;
	}

	for(i = 0;i < ADValueValidNum; i++)
	{
		ADValueSum += ADValueBuf[i];
	}

	if(ADValueValidNum != 0)
	{
		GwADMeanValue = ADValueSum / ADValueValidNum;
	}

	for (i = PM_APP; i < PM_TOTAL; i++)
    {
	    LpPowerData = &GpPowerData[i];
	    ReadPwrManage_GroupStatus(i,&LenuVoltageStatusTemp);
	    
	    if(GwADMeanValue < LpPowerData->u16Normal2LowVoltage)/*当前采集AD值小于欠压阈值，则置为欠压*/             
	    {
	        LenuVoltageStatusTemp = UNDER_VOLTAGE;
			#if 0
			DEBUG_PRINT("当前欠压值：%d\r\n",GwADMeanValue);
			DEBUG_PRINT("欠压阈值：%d\r\n",LpPowerData->u16Normal2LowVoltage);
			#endif
	    }
	    else if(GwADMeanValue > LpPowerData->u16Normal2HighVoltage)  /*当前采集AD值大于过压阈值，则置为过压*/       
	    {
	        LenuVoltageStatusTemp = OVER_VOLTAGE;
			#if 0
			DEBUG_PRINT("当前过压值：%d\r\n",GwADMeanValue);
			DEBUG_PRINT("过压阈值：%d\r\n",LpPowerData->u16Normal2HighVoltage);
			#endif
	    }
	    else if((GwADMeanValue >= LpPowerData->u16Low2NormalVoltage)/*当前采集AD值大于等于欠压恢复阈值且小于等于过压恢复阈值，则置为正常*/
	        && (GwADMeanValue <= LpPowerData->u16High2NormalVoltage))
	    {
	        LenuVoltageStatusTemp = NORMAL_VOLTAGE;
	    }
	    else
	    {
	        /*1.当前采集AD值大于欠压阈值且小于欠压恢复阈值；
	        2.当前采集AD值大于过压恢复阈值且小于过压阈值；
	        以上两种情况下，维持之前电压状态*/ 
	    }        
        WritePwrManage_GroupStatus(i,&LenuVoltageStatusTemp);
		#if 0
		DEBUG_PRINT("正常值：%d\r\n",GwADMeanValue);
		#endif
    }
}

/******************************************************************
* 函数名称:	PowerDiagFunc
*
* 功能描述: 
*
* 输入参数:	无
*
* 输出参数:	无
*
* 返 回 值:	无
*
* 其它说明: 无
*
* 修改日期		版本号		修改人		修改内容

**********************************************************************/ 
static void PowerDiagFunc(uint16 Lu16PwrAdcValue[])
{
    SnsPowerDiagIdx_e LenuIdx = 0;
	
	for(LenuIdx = FrontSnsGroupPwrDiagIdx; LenuIdx < SnsGroupPwrDiagNum; LenuIdx++)
	{
		Gu16PwrADVal[SnsPwrPinDiagCtrl[LenuIdx].PwrAdcValIdx] = Lu16PwrAdcValue[SnsPwrPinDiagCtrl[LenuIdx].PwrAdcValIdx];
		if(1 == SnsPwrPinDiagCtrl[LenuIdx].ReadPwrEnPinSts())
		{		
			/*check short to GND*/
			SnsPwrPinDiagCtrl[LenuIdx].ShortToPwrFilCtrl.Count = 0;
			if(Lu16PwrAdcValue[SnsPwrPinDiagCtrl[LenuIdx].PwrAdcValIdx] < AD_CALIBRATION_1_5V)
			{
				SnsPwrPinDiagCtrl[LenuIdx].ShortToGndFilCtrl.Input = TRUE;
			}
			else if(Lu16PwrAdcValue[SnsPwrPinDiagCtrl[LenuIdx].PwrAdcValIdx] > AD_CALIBRATION_2_5V)
			{
				SnsPwrPinDiagCtrl[LenuIdx].ShortToGndFilCtrl.Input = FALSE;
			}
			else
			{}

			PubAI_Filter(&SnsPwrPinDiagCtrl[LenuIdx].ShortToGndFilCtrl);

			if(TRUE == SnsPwrPinDiagCtrl[LenuIdx].ShortToGndFilCtrl.Output)
			{
				WritePwrManage_SnsPwrDiagStatus(LenuIdx,PWR_PIN_SHORT_TO_GND);
			}
			else
			{
				WritePwrManage_SnsPwrDiagStatus(LenuIdx,PWR_PIN_NORMAL);
			}
			
		}
		else
		{	
			/*check short to Power*/
			SnsPwrPinDiagCtrl[LenuIdx].ShortToGndFilCtrl.Count = 0;
			if(Lu16PwrAdcValue[SnsPwrPinDiagCtrl[LenuIdx].PwrAdcValIdx] > AD_CALIBRATION_7V)
			{
				SnsPwrPinDiagCtrl[LenuIdx].ShortToPwrFilCtrl.Input = TRUE;
			}
			else if(Lu16PwrAdcValue[SnsPwrPinDiagCtrl[LenuIdx].PwrAdcValIdx] < AD_CALIBRATION_6V)
			{
				SnsPwrPinDiagCtrl[LenuIdx].ShortToPwrFilCtrl.Input = FALSE;
			}
			else
			{}

			PubAI_Filter(&SnsPwrPinDiagCtrl[LenuIdx].ShortToPwrFilCtrl);

			if(TRUE == SnsPwrPinDiagCtrl[LenuIdx].ShortToPwrFilCtrl.Output)
			{
				WritePwrManage_SnsPwrDiagStatus(LenuIdx,PWR_PIN_SHORT_TO_PWR);
			}
			else
			{
				WritePwrManage_SnsPwrDiagStatus(LenuIdx,PWR_PIN_NORMAL);
			}

		}
	}
}

/******************************************************************************
* 函数名称: PowerManageTask
*
* 功能描述: 电源管理任务
*
* 输入参数: 无
*
* 返回参数: 无
*
* 设计日期: 2021.4.26
*
* 修改内容: 初版
*
* 其它说明: 10ms调一次
*******************************************************************************/
void PowerManage_Task(void)
{
	uint8 Lu8Idx = 0;
	uint16 Lu16PwrAdcRawValue[PowerAdcIdxNum];
	
	memset((void*)&Lu16PwrAdcRawValue[0],0,sizeof(Lu16PwrAdcRawValue));
	
	for(Lu8Idx = SupplyPwrAdcIdx; Lu8Idx < PowerAdcIdxNum; Lu8Idx++)
	{
		Lu16PwrAdcRawValue[Lu8Idx] = AdcHal_GetAdcResult(GstrPwrAdcCfg[Lu8Idx].AdcUint,GstrPwrAdcCfg[Lu8Idx].AdcChannel);
	}
	
    PowerManageFunc(Lu16PwrAdcRawValue[SupplyPwrAdcIdx]);
	PowerDiagFunc(&Lu16PwrAdcRawValue[0]);
}


/*END PwrMan.c*****************************************************************/

