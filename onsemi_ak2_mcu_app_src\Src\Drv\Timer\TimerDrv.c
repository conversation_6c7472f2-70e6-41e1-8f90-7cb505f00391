/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: TimerDrv.c
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/
#include "types.h"
#include "TimerRegCfg.h"
#include "TimerDrv.h"

/******************************************************************************/
/**<pre>
 *函数名称: TimerDrvInit
 *功能描述: 定时器初始化,配置定时中断
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void TimerDrvInit(void)
{
    /* Disable OSTM0 operation */
    OSTM0.TT = _OSTM_COUNTER_STOP;
    /* Disable OSTM0 interrupt operation and clear request */
    INTC2.ICOSTM0.BIT.MKOSTM0 = _INT_PROCESSING_DISABLED;
    INTC2.ICOSTM0.BIT.RFOSTM0 = _INT_REQUEST_NOT_OCCUR;
    /* Set OSTM0 interrupt setting */
    INTC2.ICOSTM0.BIT.TBOSTM0 = _INT_TABLE_VECTOR;
    INTC2.ICOSTM0.UINT16 &= _INT_PRIORITY_LEVEL1;
    /* Set OSTM0 control setting */
    OSTM0.CTL = _OSTM_MODE_INTERVAL_TIMER | _OSTM_START_INTERRUPT_ENABLE;
    OSTM0.CMP = _OSTM0_COMPARING_COUNTER;

    /* Enable OSTM0 operation */
    /* Clear OSTM0 interrupt request and enable operation */
    INTC2.ICOSTM0.BIT.RFOSTM0 = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICOSTM0.BIT.MKOSTM0 = _INT_PROCESSING_ENABLED;
    OSTM0.TS = _OSTM_COUNTER_START;
}

/******************************************************************************/
/**<pre>
 *函数名称: TimerClose
 *功能描述: 关闭定时器中断
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void TimerClose(void)
{
    /* Disable OSTM0 operation */
    OSTM0.TT = _OSTM_COUNTER_STOP;
    /* Disable OSTM0 interrupt operation and clear request */
    INTC2.ICOSTM0.BIT.MKOSTM0 = _INT_PROCESSING_DISABLED;
    INTC2.ICOSTM0.BIT.RFOSTM0 = _INT_REQUEST_NOT_OCCUR;
}

/******************************************************************************/
/**<pre>
 *函数名称: WDTInit
 *功能描述: 看门狗初始化
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void WDTInit(void)
{
    /* Disable WDT0 interrupt operation and clear request */
    INTC2.ICWDTA0.BIT.MKWDTA0 = _INT_PROCESSING_DISABLED;
    INTC2.ICWDTA0.BIT.RFWDTA0 = _INT_REQUEST_NOT_OCCUR;
    /* Set WDT0 control setting */
    WDTA0.MD = _WDT_OVERFLOW_WDTACKI_2_12 | _WDT_INTERRUPT_DISABLED | _WDT_RESET_MODE | _WDT_PERIOD_100;

    /* FeedDog */
    WDTA0.WDTE = _WDT_START_CODE;
}

/******************************************************************************/
/**<pre>
 *函数名称: WDTFeedDog
 *功能描述: 喂狗
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void WDTFeedDog(uint8 *Lu8TimerCnt)
{
    if(0u == *Lu8TimerCnt)
    {
        *Lu8TimerCnt = TASK_10MS_COUNT;
        WDTA0.WDTE = _WDT_START_CODE;
    }
}

/******************************************************************************/
/**<pre>
 *函数名称: WDTTriggerReset
 *功能描述: 触发复位
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void WDTTriggerReset(void)
{
    WDTA0.WDTE = _WDT_RESET_CODE;
}

/******************************************************************************/
/**<pre>
 *函数名称: SetTimeDelay
 *功能描述: 设置延时
 *输入参数: 延时时间
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void SetTimeDelay(uint8 Lu8DelayMs)
{
    uint32 Lu32DelayTimer = 20000ul*Lu8DelayMs;

    while(Lu32DelayTimer > 0u)
    {
        Lu32DelayTimer--;
    }
}


