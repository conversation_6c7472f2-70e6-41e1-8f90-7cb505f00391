import sys

def calculate_crc8(data, polynomial, init_value):
    crc = init_value
    
    # 初始化CRC表
    crc_table = []
    for i in range(256):
        crc_value = i
        for j in range(8):
            if crc_value & 0x80:
                crc_value = (crc_value << 1) ^ polynomial
            else:
                crc_value <<= 1
            crc_value &= 0xFF  # 确保结果是8位
        crc_table.append(crc_value)
    
    # 计算CRC
    for byte in data:
        crc = crc_table[crc ^ byte]
    
    return crc

def test_init_value(data_with_crc, polynomial):
    # 分离数据和CRC
    data = data_with_crc[:-1]
    expected_crc = data_with_crc[-1]
    
    # 尝试所有可能的初始值
    for init_value in range(256):
        crc = calculate_crc8(data, polynomial, init_value)
        if crc == expected_crc:
            return init_value
    
    return None

# 已知多项式
polynomial = 0x2F  # x8+x5+x3+x2+x+1

# 解析示例数据
def parse_hex_string(hex_string):
    # 移除所有空白字符
    hex_string = hex_string.strip()
    
    # 将字符串转换为字节列表
    return [int(hex_string[i:i+2], 16) for i in range(0, len(hex_string), 2)]

# 示例数据
examples = [
    'FF00EFFE',
    'FFC30D00',
    '02120000'
]

print('尝试推导CRC8初始值...')
print(f'多项式: 0x{polynomial:02X} (x8+x5+x3+x2+x+1)')

for example in examples:
    data_with_crc = parse_hex_string(example)
    init_value = test_init_value(data_with_crc, polynomial)
    
    if init_value is not None:
        print(f'示例: {example}')
        print(f'数据: {[f"0x{b:02X}" for b in data_with_crc[:-1]]}')
        print(f'CRC8: 0x{data_with_crc[-1]:02X}')
        print(f'找到匹配的初始值: 0x{init_value:02X}')
        print()
    else:
        print(f'示例: {example}')
        print('未找到匹配的初始值')
        print()

# 验证所有示例是否使用相同的初始值
common_init_values = []
for init_value in range(256):
    all_match = True
    
    for example in examples:
        data_with_crc = parse_hex_string(example)
        data = data_with_crc[:-1]
        expected_crc = data_with_crc[-1]
        
        crc = calculate_crc8(data, polynomial, init_value)
        if crc != expected_crc:
            all_match = False
            break
    
    if all_match:
        common_init_values.append(init_value)

if common_init_values:
    print('所有示例共同的初始值:')
    for init_value in common_init_values:
        print(f'0x{init_value:02X}')
else:
    print('没有找到所有示例共同的初始值')

# 如果找到了共同的初始值，验证一下
if common_init_values:
    init_value = common_init_values[0]
    print(f'\n使用初始值 0x{init_value:02X} 验证所有示例:')
    
    for example in examples:
        data_with_crc = parse_hex_string(example)
        data = data_with_crc[:-1]
        expected_crc = data_with_crc[-1]
        
        crc = calculate_crc8(data, polynomial, init_value)
        print(f'示例: {example}')
        print(f'计算CRC8: 0x{crc:02X}')
        print(f'预期CRC8: 0x{expected_crc:02X}')
        print(f'验证结果: {"成功" if crc == expected_crc else "失败"}')
        print()
