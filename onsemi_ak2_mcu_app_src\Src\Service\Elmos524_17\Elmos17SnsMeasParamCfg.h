/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * Elmos17SnsMeasParamCfg: 
 * Created on: 
 * Original designer: 
 ******************************************************************************/
#ifndef _ELMOS17_SNS_MEAS_PARAM_CFG_H_
#define _ELMOS17_SNS_MEAS_PARAM_CFG_H_

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "Elmos17SnsMeasParamType.h"

/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/
#define MEASURE_TIME_STEP               (256U)

#define STD_PROFILEA_MEASURE_TIME		(60U)
#define STD_PROFILEB_MEASURE_TIME		(118U)
#define STD_PROFILEC_MEASURE_TIME		(24U)
#define ADV_PROFILEA_MEASURE_TIME       (150U)
#define ADV_PROFILEB_MEASURE_TIME       (141U)

/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
extern Meas_sGeneralSettings FLS_FRS_SnsGeneralSettings;
extern Meas_sGeneralSettings FL_FR_SnsGeneralSettings;
extern Meas_sGeneralSettings FLM_FRM_SnsGeneralSettings;
extern Meas_sGeneralSettings RLS_RRS_SnsGeneralSettings;
extern Meas_sGeneralSettings RL_RR_SnsGeneralSettings;
extern Meas_sGeneralSettings RLM_RRM_SnsGeneralSettings;
extern Meas_sStandardPathProfile FLS_FRS_STDProfile_A;
extern Meas_sStandardPathProfile FL_FR_STDProfile_A;
extern Meas_sStandardPathProfile FLM_FRM_STDProfile_A;
extern Meas_sStandardPathProfile RLS_RRS_STDProfile_A;
extern Meas_sStandardPathProfile RL_RR_STDProfile_A;
extern Meas_sStandardPathProfile RLM_RRM_STDProfile_A;
extern Meas_sStandardPathProfile FLS_FRS_STDProfile_B;
extern Meas_sStandardPathProfile FL_FR_STDProfile_B;
extern Meas_sStandardPathProfile FLM_FRM_STDProfile_B;
extern Meas_sStandardPathProfile RLS_RRS_STDProfile_B;
extern Meas_sStandardPathProfile RL_RR_STDProfile_B;
extern Meas_sStandardPathProfile RLM_RRM_STDProfile_B;
extern Meas_sStandardPathProfile FLS_FRS_STDProfile_C;
extern Meas_sStandardPathProfile FL_FR_STDProfile_C;
extern Meas_sStandardPathProfile FLM_FRM_STDProfile_C;
extern Meas_sStandardPathProfile RLS_RRS_STDProfile_C;
extern Meas_sStandardPathProfile RL_RR_STDProfile_C;
extern Meas_sStandardPathProfile RLM_RRM_STDProfile_C;
extern Meas_sAdvancedPathProfile FLS_FRS_ADVProfile_A;
extern Meas_sAdvancedPathProfile FL_FR_ADVProfile_A;
extern Meas_sAdvancedPathProfile FLM_FRM_ADVProfile_A;
extern Meas_sAdvancedPathProfile RLS_RRS_ADVProfile_A;
extern Meas_sAdvancedPathProfile RL_RR_ADVProfile_A;	
extern Meas_sAdvancedPathProfile RLM_RRM_ADVProfile_A;
extern Meas_sAdvancedPathProfile FLS_FRS_ADVProfile_B;
extern Meas_sAdvancedPathProfile FL_FR_ADVProfile_B;
extern Meas_sAdvancedPathProfile FLM_FRM_ADVProfile_B;
extern Meas_sAdvancedPathProfile RLS_RRS_ADVProfile_B;
extern Meas_sAdvancedPathProfile RL_RR_ADVProfile_B;	
extern Meas_sAdvancedPathProfile RLM_RRM_ADVProfile_B;
extern Meas_sTofCompensation FLS_FRS_TofComp;
extern Meas_sTofCompensation FL_FR_TofComp;
extern Meas_sTofCompensation FLM_FRM_TofComp;
extern Meas_sTofCompensation RLS_RRS_TofComp;
extern Meas_sTofCompensation RL_RR_TofComp;
extern Meas_sTofCompensation RLM_RRM_TofComp;
extern const uint8 MeasParamDefaultVal[Elmos17MEAS_Param_Check_u16Len*2];

/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


#endif /* end of _ELMOS17_SNS_MEAS_PARAM_CFG_H_ */

