/*==================================================================================================
*   Project              : RTD AUTOSAR 4.4
*   Platform             : CORTEXM
*   Peripheral           : 
*   Dependencies         : 
*
*   Autosar Version      : 4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : 0.8.0
*   Build Version        : S32K3_RTD_0_8_0_D2303_ASR_REL_4_4_REV_0000_20230310
*
*   (c) Copyright 2020 - 2022 NXP Semiconductors
*   All Rights Reserved.
*
*   NXP Confidential. This software is owned or controlled by NXP and may only be
*   used strictly in accordance with the applicable license terms. By expressly
*   accepting such terms or by downloading, installing, activating and/or otherwise
*   using the software, you are agreeing that you have read, and that you agree to
*   comply with and are bound by, such license terms. If you do not agree to be
*   bound by the applicable license terms, then you may not retain, install,
*   activate or otherwise use the software.
==================================================================================================*/

/**
*   @file       CanTrcv_fs23_Ip.c
*
*   @addtogroup CANTRCV_FS23_DRIVER
*   @{
*/

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                                        INCLUDE FILES
* 1) system and project includes
* 2) needed interfaces from external units
* 3) internal and external interfaces from this unit
==================================================================================================*/
#include "CanTrcv_fs23_Ip.h"
#include "CanTrcv_43_fs23_Cfg.h"
//#include "SchM_CanTrcv_43_fs23.h"

/*==================================================================================================
*                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
*                                      LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                      GLOBAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      GLOBAL VARIABLES
==================================================================================================*/
#define CANTRCV_43_FS23_START_SEC_VAR_CLEARED_UNSPECIFIED
//#include "CanTrcv_43_fs23_MemMap.h"

/**
* @brief Internal array of Transceiver units connected to MCU
*
*/
static CanTrcv_fs23_ConfigurationDataType g_drivers[CANTRCV_43_FS23_MAX_DEVICES_NUM];

#define CANTRCV_43_FS23_STOP_SEC_VAR_CLEARED_UNSPECIFIED
//#include "CanTrcv_43_fs23_MemMap.h"

/*==================================================================================================
*                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
*                                       GLOBAL FUNCTIONS
==================================================================================================*/
#define CANTRCV_43_FS23_START_SEC_CODE
//#include "CanTrcv_43_fs23_MemMap.h"

/**
* @brief        Initializes the FS23 CAN Transceiver driver and configures device.
*
* @details      Stores pointer to device configuration and initializes internal data structure.
*
* @param[in]    Transceiver     Which device to address.
* @param[in]    ConfigData      Pointer to configuration of the device and SPI.
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
/** @implements   CanTrcv_fs23_Init_Activity */
Std_ReturnType CanTrcv_fs23_Init(uint8 Transceiver, const CanTrcv_fs23_ConfigurationDataType* ConfigData)
{
    uint16 RegData = 0U;
    Std_ReturnType Status = (Std_ReturnType)E_OK;
    if ((Transceiver < CANTRCV_43_FS23_MAX_DEVICES_NUM) && (ConfigData != NULL_PTR))
    {
        g_drivers[Transceiver] = *ConfigData;

        switch(g_drivers[Transceiver].initState)
        {
            case CANTRCV_TRCVMODE_SLEEP:
                /*Pass through*/

            case CANTRCV_TRCVMODE_STANDBY:
                Status |= CanTrcv_fs23_SetMode(Transceiver,CANTRCV_FS23_TRCVMODE_OFF);
                break;

            case CANTRCV_TRCVMODE_NORMAL:
                Status |= CanTrcv_fs23_SetMode(Transceiver,CANTRCV_FS23_TRCVMODE_NORMAL);
                break;

            default:
                Status |= (Std_ReturnType)E_NOT_OK;
                break;
        }
        /* Enable wake up by CAN bus */
        if(g_drivers[Transceiver].canBusWuEnable == TRUE)
        {
            /* Read WU1_EN register. */
            Status |= Sbc_fs23_ReadRegister(SBC_FS23_M_WU1_EN_ADDR, &RegData);
            /* Set new register value. */
            RegData |= SBC_FS23_M_CAN_WUEN_WAKEUP_MASK;
            Status |= Sbc_fs23_WriteRegister(SBC_FS23_M_WU1_EN_ADDR, RegData);
        }
    }

    return Status;
}

/*================================================================================================*/

/**
* @brief        Sets the Mode of the Transceiver to the value OpMode.
*
* @details      Sets Mode of FS23 CAN Transceiver, operational Mode of the SBC itself is controlled separately.
*
* @param[in]    Transceiver     Which device to address.
* @param[in]    OpMode          Desired operating Mode.
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
/** @implements   CanTrcv_fs23_SetMode_Activity */
Std_ReturnType CanTrcv_fs23_SetMode(uint8 Transceiver, CanTrcv_fs23_TrcvModeType OpMode)
{
    uint16 RegData = 0U;
    Std_ReturnType Status;
    CanTrcv_fs23_TrcvModeType NewHwOpMode = CANTRCV_FS23_TRCVMODE_INVALID;
    /* Read M_CAN register. */
    Status = Sbc_fs23_ReadRegister(SBC_FS23_M_CAN_ADDR, &RegData);

    if ((Std_ReturnType)E_OK == Status)
    {
        /* Set new register value. */
        RegData &= ~(SBC_FS23_M_CAN_MODE_MASK | SBC_FS23_M_CAN_FS_DIS_MASK);
        if(g_drivers[Transceiver].canFailsafeDisable == FALSE)
        {
            RegData |= SBC_FS23_M_CAN_FS_DIS_KEEP;
        }
        RegData |= ((uint16)OpMode << SBC_FS23_M_CAN_MODE_SHIFT);

        Status = Sbc_fs23_WriteRegister(SBC_FS23_M_CAN_ADDR, RegData);
        /*Check the real hardware state*/
        Status |= CanTrcv_fs23_GetMode(Transceiver, &NewHwOpMode);
        if(((Std_ReturnType)E_OK != Status) || (NewHwOpMode != OpMode))
        {
            Status = (Std_ReturnType)E_NOT_OK;
        }
    }

    return Status;
}

/*================================================================================================*/

/**
* @brief        Gets the Mode of the Transceiver and returns it in OpMode.
*
* @details      Reads CAN_LIN_MODE register.
*
* @param[in]    Transceiver     Which Transceiver to address.
* @param[out]   OpMode          Stores the Mode of the Transceiver.
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
/** @implements   CanTrcv_fs23_GetMode_Activity */
Std_ReturnType CanTrcv_fs23_GetMode(uint8 Transceiver, CanTrcv_fs23_TrcvModeType* OpMode)
{
    uint16 Mode = 0U;
    Std_ReturnType Status;

    (void)Transceiver;

    //SchM_Enter_CanTrcv_43_fs23_CANTRCV_EXCLUSIVE_AREA_00();
    /* Read CAN_LIN_MODE register. */
    Status = Sbc_fs23_ReadRegister(SBC_FS23_M_CAN_LIN_MSK_ADDR, &Mode);

    if ((Std_ReturnType)E_OK == Status)
    {
        switch (Mode & SBC_FS23_M_CAN_FSM_STATE_S_MASK)
        {
            case SBC_FS23_M_CAN_FSM_STATE_S_OFF:
                /* Pass through */

            case SBC_FS23_M_CAN_FSM_STATE_S_OFFLINE:
                /* Pass through */

            case SBC_FS23_M_CAN_FSM_STATE_S_OFFLINEVCCNOK:
                /* Pass through */

            case SBC_FS23_M_CAN_FSM_STATE_S_GOACTIVE:
                *OpMode = CANTRCV_FS23_TRCVMODE_OFF;
                break;

            case SBC_FS23_M_CAN_FSM_STATE_S_LISTEN:
                *OpMode = CANTRCV_FS23_TRCVMODE_LISTENONLY;
                break;

            case SBC_FS23_M_CAN_FSM_STATE_S_NORMAL:
                *OpMode = CANTRCV_FS23_TRCVMODE_NORMAL;
                break;

            default:
                *OpMode = CANTRCV_FS23_TRCVMODE_INVALID;
                break;
        }
    }
    //SchM_Exit_CanTrcv_43_fs23_CANTRCV_EXCLUSIVE_AREA_00();

    return Status;
}

/*================================================================================================*/

/**
* @brief        Retrieves the wake up reason.
*
* @details
*
* @param[in]    Transceiver     Which Transceiver to address.
* @param[out]   WuReason        Stores the wake-up reason.
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
/** @implements   CanTrcv_fs23_GetWuReason_Activity */
Std_ReturnType CanTrcv_fs23_GetWuReason(uint8 Transceiver, CanTrcv_fs23_WuReasonType* WuReason)
{
    uint16 RegData = 0U;
    Std_ReturnType Status = (Std_ReturnType)E_OK;

    (void)Transceiver;
    //SchM_Enter_CanTrcv_43_fs23_CANTRCV_EXCLUSIVE_AREA_01();

    *WuReason = CANTRCV_FS23_WU_NO_EVENT;

    Status |= Sbc_fs23_ReadRegister(SBC_FS23_M_IOWU_FLG_ADDR, &RegData);

    if(((uint16)0U != (RegData & (SBC_FS23_M_WK1_WU_I_MASK | SBC_FS23_M_WK2_WU_I_MASK | SBC_FS23_M_HVIO1_WU_I_MASK | SBC_FS23_M_HVIO2_WU_I_MASK | SBC_FS23_M_LVIO3_WU_I_MASK | SBC_FS23_M_LVIO4_WU_I_MASK | SBC_FS23_M_LVI5_WU_I_MASK))))
    {
        *WuReason = CANTRCV_FS23_WU_PIN_WAKEUP;
    }
    else
    {
        Status |= Sbc_fs23_ReadRegister(SBC_FS23_M_WU1_FLG_ADDR, &RegData);
        if ((Std_ReturnType)E_OK == Status)
        {
            if ((SBC_FS23_M_CAN_WU_I_WAKEUP == (RegData & SBC_FS23_M_CAN_WU_I_MASK)))
            {
                *WuReason = CANTRCV_FS23_WU_CAN_BUS_WAKEUP;
            }
            else if(((uint16)0U != (RegData & (SBC_FS23_M_LIN_WU_I_MASK | SBC_FS23_M_LDT_WU_I_MASK | SBC_FS23_M_GO2NORMAL_WU_MASK | SBC_FS23_M_INT_TO_WU_MASK | SBC_FS23_M_V1_UVLP_WU_MASK | SBC_FS23_M_WD_OFL_WU_MASK | SBC_FS23_M_EXT_RSTB_WU_MASK))))
            {
                *WuReason = CANTRCV_FS23_WU_INTERNAL_WAKEUP;
            }
            else
            {
                *WuReason = CANTRCV_FS23_WU_NO_EVENT;
            }
        }
    }

    //SchM_Exit_CanTrcv_43_fs23_CANTRCV_EXCLUSIVE_AREA_01();

    return Status;
}

/*================================================================================================*/

/**
* @brief        Clears all wake-up flag.
*
* @details      Clears all wake-up flags in WU_SOURCE register.
*
* @param[in]    Transceiver     Which Transceiver to address.
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
/** @implements   CanTrcv_fs23_ClearWuFlags_Activity */
Std_ReturnType CanTrcv_fs23_ClearWuFlags(uint8 Transceiver)
{
    Std_ReturnType Status = (Std_ReturnType)E_OK;

    (void)Transceiver;
    /*Clear wake up flags*/
    Status |= Sbc_fs23_WriteRegister(SBC_FS23_M_WU1_FLG_ADDR, (uint16)0xFFFFU);
    Status |= Sbc_fs23_WriteRegister(SBC_FS23_M_IOWU_FLG_ADDR, (uint16)0xFFFFU);

    return Status;
}

/*================================================================================================*/

/**
* @brief        Retrieves the Status of the CAN Transceiver.
*
* @details      Retrieves CAN related Status data of the FS23 device, such as VCAN regulator Status
*               (OV, UV, ILIM), CAN Transceiver and CAN pins Status (short, OC, OT).
*
* @param[in]    Transceiver     Which Transceiver to address.
* @param[in]    CanStatus       Status data of the Transceiver.
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
/** @implements   CanTrcv_fs23_GetCanStatus */
Std_ReturnType CanTrcv_fs23_GetCanStatus(uint8 Transceiver, uint16* CanStatus)
{
    uint16 RegData = 0U;
    Std_ReturnType Status = (Std_ReturnType)E_OK;

    (void)Transceiver;
    /* Read M_CAN register. */
    Status |= Sbc_fs23_ReadRegister(SBC_FS23_M_CAN_ADDR, &RegData);

    if ((Std_ReturnType)E_OK == Status)
    {
        *CanStatus = (uint16)(RegData & (SBC_FS23_M_CAN_TSD_I_MASK | SBC_FS23_M_CAN_TXD_TO_I_MASK));

    }

    return Status;
}

/*================================================================================================*/
/**
* @brief        Deinitializes the Cantrcv_fs23 driver.
*
* @details      Clears the internal run-time configuration, puts the device to normal Mode and clears
*               all pending wake-ups.
*
* @param[in]    Transceiver     Which Transceiver to address.
*
* @return       Std_ReturnType  Return code.
*
* @api
*/
/** @implements   CanTrcv_fs23_Deinit_Activity*/
Std_ReturnType CanTrcv_fs23_Deinit(uint8 Transceiver)
{
    Std_ReturnType Status = (Std_ReturnType)E_OK;

    /* Set hardware state to OFF mode. */

    Status |= CanTrcv_fs23_SetMode(Transceiver, CANTRCV_FS23_TRCVMODE_OFF);

    if ((Std_ReturnType)E_OK == Status)
    {
        g_drivers[Transceiver].canBusWuEnable = TRUE;
        g_drivers[Transceiver].canFailsafeDisable = TRUE;
        g_drivers[Transceiver].initState = CANTRCV_TRCVMODE_STANDBY;
    }

    return Status;
}

#define CANTRCV_43_FS23_STOP_SEC_CODE
//#include "CanTrcv_43_fs23_MemMap.h"

#ifdef __cplusplus
}
#endif

/** @} */
