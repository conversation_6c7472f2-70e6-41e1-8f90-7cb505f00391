/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * APA_CalibPara: 
 * Created on: 2022-11-25 17:30
 * Original designer: 22866
 ******************************************************************************/
#ifndef __SNS_INSTALL_COORDINATE_TYPES_H__
#define __SNS_INSTALL_COORDINATE_TYPES_H__

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "types.h"




/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/







/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************************
* 设计描述 : 探头安装参数
* 设计索引 : 
*******************************************************************************/

typedef struct
{
	float cRadarX;       	/* 探头x坐标 */
	float cRadarY;       	/* 探头Y坐标 */

}SnsCoorSturctType;



typedef struct
{
	float cOuterAngle;//探头外张夹角	
	                  //探头上扬夹角预留	
}SnsAngleSturctType;



typedef struct
{    
    uint16 u16RSSensorBackWheelDis;    /* 后侧探头到后轴的距离  */
    uint16 u16RSSensorCarCenterlDis;    /* 后侧探头到车身中心（车宽方向）的距离  */
    uint16 u16RSSensorInDis;          /* 后侧探头到车身侧面的距离  */

    uint16 u16FSSensorBackWheelDis;    /* 前侧探头到后轴的距离  */
    uint16 u16FSSensorCarCenterlDis;    /* 前侧探头到车身中心的距离  */
    uint16 u16FSSensorInDis;             /* 前侧探头到车身侧面（车宽减去探头到车身中心距离*2再除2）的距离  */

    uint16 u16FRSBackSensorDis;          /* 前后侧探头的距离  */
    uint16 u16RSSensorCarTailDis;         /* 后侧探头到车尾的距离  */
}Sns_Install_ParameterType;

typedef struct
{
    float fStartX;
    float fStartY;
    float fEndX;
    float fEndY;
    
}Sns_Install_PDCZoneCoorType;



typedef enum
{
    SNS_INSTALL_GROUP_NONE  = -1,
    SNS_INSTALL_GROUP_FRONT , 
    SNS_INSTALL_GROUP_REAR  , 
    SNS_INSTALL_GROUP_NUM   ,    
}SnsInstallGroupType;

typedef enum
{
    /** @brief: 左侧雷达 */
    SNS_CH_FLS  = 0,
    SNS_CH_RLS  = 0,
    SNS_CH_FRLS  = 0,
    /** @brief: 左角雷达 */
    SNS_CH_FLC  = 1,
    SNS_CH_RLC  = 1,
    SNS_CH_FRLC  = 1,
    /** @brief: 左中雷达 */
    SNS_CH_FLM  = 2,
    SNS_CH_RLM  = 2,
    SNS_CH_FRLM  = 2,
    /** @brief: 右中雷达 */
    SNS_CH_FRM  = 3,
    SNS_CH_RRM  = 3,
    SNS_CH_FRRM  = 3,
    /** @brief: 右角雷达 */
    SNS_CH_FRC  = 4,
    SNS_CH_RRC  = 4,
    SNS_CH_FRRC  = 4,
    /** @brief: 右侧雷达 */
    SNS_CH_FRS  = 5,
    SNS_CH_RRS  = 5,
    SNS_CH_FRRS  = 5,
    
    SNS_CH_NUM,
}eSnsChannelType;

typedef enum
{
    ZONE_BORDER_1 =0,
    ZONE_BORDER_2 ,
    ZONE_BORDER_3 ,
    ZONE_BORDER_4 ,
    ZONE_BORDER_5 ,
    ZONE_BORDER_NUM ,
        

}eSnsPdcZoneIndexType;



typedef struct
{
    float fAngle[SNS_CH_NUM];
    float fRightCosA[SNS_CH_NUM];
    float fRightSinA[SNS_CH_NUM];
    float fLeftCosA[SNS_CH_NUM];
    float fLeftSinA[SNS_CH_NUM];
}SnsCoorConvAngleType;

typedef struct
{
    float fAngle[SNS_CH_NUM];
    float fCosA[SNS_CH_NUM];
    float fSinA[SNS_CH_NUM];
}SnsOutAngleType;

typedef struct
{
	float fRadarLeftDis[SNS_CH_NUM];   /* 探头到左侧探头距离 */
	float fRadarRightDis[SNS_CH_NUM];  /* 探头到右侧探头距离 */

}SnsDisSturctType;


/**********************************Map 细节分区定义************************************  */
/* 定义探头分区界限类型 */
typedef enum
{
    MAP_LINE0_1 = 0,
    MAP_LINE1_2,
    MAP_LINE2_3,
    MAP_LINE3_4,
    MAP_LINE4_5,
    MAP_LINE5_6,
    MAP_LINE6_7,
    MAP_LINE7_8,
    MAP_LINE_NUM
}ObjMapAreaLineType;


/* 定义探头测量类型 */
typedef enum
{
    MAP_AREA_NONE  = -1,
    MAP_AREA0  = 0,
    MAP_AREA1,
    MAP_AREA2, 
    MAP_AREA3,
    MAP_AREA4,
    MAP_AREA5,
    MAP_AREA6,
    MAP_AREA7,
    MAP_AREA8,
    MAP_AREA_NUM
}ObjMAPAreaType;

/* 定义障碍物区域划分数据类型 */
typedef struct
{
    float fAreaStart_X;
    float fAreaStart_Y;
    float fAreaEnd_X;
    float fAreaEnd_Y;
    float fSnsToCarAngle;
}MapAreaType;


/* 重新分区定义点云的大区域，用于缓存点云的分区 */
/* 定义障碍物点云的分区类型 */
typedef enum
{
    POINT_CLOUD_NONE  = -1,
    POINT_CLOUD_AREA0  = 0,     /* 障碍物点云在车身的左边沿往左 */
    POINT_CLOUD_AREA1,          /* 障碍物点云在车身的左边沿和FML探头坐标延长线之间 */
    POINT_CLOUD_AREA2,          /* 障碍物点云在FML探头和FMR探头坐标延长线之间 */
    POINT_CLOUD_AREA3,          /* 障碍物点云在FMR探头坐标延长线之间和车身右边沿之间 */
    POINT_CLOUD_AREA4,          /* 障碍物点云在车身右边沿往右 */
    POINT_CLOUD_AREA_NUM
}PointCloudAreaType;


#endif
