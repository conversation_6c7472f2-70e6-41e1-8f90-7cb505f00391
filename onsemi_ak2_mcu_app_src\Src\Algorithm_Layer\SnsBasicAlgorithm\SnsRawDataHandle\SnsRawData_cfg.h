/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * SnsRawData_cfg: 
 * Created on: 2022-12-16 10:36
 * Original designer: AntonyFang
 ******************************************************************************/

#ifndef SnsRawData_cfg_H
#define SnsRawData_cfg_H

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "SnsRawData_Type.h"
#include "Elmos_524_17_SnsCtrl.h"
#include "AdvEchoDet.h"

/******************************************************************************/
/**
 * @brief   调试打印
 */
/******************************************************************************/
#define   PDC_DATA_PRINT_SWITCH              (STD_OFF)


#if ( PDC_DATA_PRINT_SWITCH == STD_ON)
#define PDC_DATA_PRINT       Lhprintf
#else
#define PDC_DATA_PRINT(...)
#endif



#define   SNS_RAW_DATA_PRINT_SWITCH              (STD_OFF)
#define   SNS_DEBUG_DATA_SEND_SWITCH             (STD_ON)


#if (SNS_RAW_DATA_PRINT_SWITCH == STD_ON)
#define SNS_RAW_DATA_PRINT       Lhprintf
#else
#define SNS_RAW_DATA_PRINT(...)  
#endif

/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/
typedef struct
{
    PDCSnsGroupType enuPDCSnsGroup;
    PDCSnsChannelType enuSnsChannel;
}SnsLocCfg_str;

/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/
#define SNS_INVALID_RING_TIME    0u
#define SNS_INVALID_DIS          0xFFFF
#define SNS_INVALID_WIDTH        0u
#define SNS_INVALID_HEIGHT       0u
#define SNS_INVALID_CONFIDENCE   0u
#define SNS_INVALID_ECHO_CNT     0u

#define SNS_MAX_RING_TIME        0x1FFFu
#define SNS_MAX_DIS              0xFFFFu
#define SNS_MAX_WIDTH            5115    /* 精度5，发送至CAN总线最大值1023 */
#define SNS_MAX_HEIGHT           0xFFFFu /* 精度1，发送至CAN总线最大 65535*/
#define SNS_MAX_CONFIDENCE       15u     /* 置信度 精度为1，最大到15 */
#define SNS_MAX_ECHO_CNT         4u      /* 置信度 精度为1，最大到15 */
#define SNS_ODO_ACTIVE_SPD       3100    /* 原始数据采集模块Odo激活车速是30km/h */

#define SNS_INVALID_OBJ_COOR     0.0
#define SNS_MAX_MASTER_LISTEN_SUB 800

/* 权重得分调节如下参数 */
#define SNS_MASTER_WIDTH_WEIGHT  0.5
#define SNS_MASTER_HEIGHT_WEIGHT 1.0

#define SNS_LISTEN_WIDTH_WEIGHT  0.5
#define SNS_LISTEN_HEIGHT_WEIGHT 1.0

#define SNS_TABLE_STEP           100   /* 单位mm，实际5cm */
#define SNS_HIGH_TABLE_STEP      100   /* 单位mm，实际10cm，障碍物高度分类按照10cm步进进行处理 */
#define SNS_TABLE_MAX_DIS        4000  /* 单位mm，实际300cm,感度表和阈值暂设置到4000以内，以外统一使用3000的值 */

#define F_R_ONLY_MASTER_COOR_DIS 1000
#define F_R_LISTEN_OBJ_Y_LIMIT   650
#define SNS_ODO_CHANGE_SUB       10   /* 1cm以上 */

#define SNS_FILTER_PVC_NOISE_DIS 220
#define SNS_FILTER_PVC_LISTEN_DIS 400
#define SNS_FILTER_FAR_NOISE_DIS  600


#define SNS_FILTER_PVC_NOISE_HEIGHT 10
#define SNS_FILTER_BIG_WALL_HEIGHT  220
#define SNS_FILTER_FAR_NOISE_HEIGHT 100


#define SNS_MASTER_SECOND_ECHO_DIS  250  /* 第二回波主发距离差值 */
#define SNS_MASTER_SECOND_ECHO_DIS2 300  /* 第二回波主发距离差值-远距离 */

#define SNS_LISTEN_SECOND_ECHO_DIS  255  /* 第二回波侦听距离 */
#define SNS_LISTEN_SECOND_ECHO_DIS2 300  /* 第二回波侦听距离 */

#define SNS_LEFT_RIGHT_SAME_X_SUB   150   /* 车辆X坐标差值在8cm以内 */
#define SNS_LEFT_RIGHT_SAME_Y_SUB   90    /* 车辆Y坐标差值在12cm以内 */
#define SNS_LEFT_RIGHT_Y_SUB_MIN    40    /* 判断为直线的最小的Y的差值 */

#define SNS_BIG_WALL_SECOND_HEIGHT  85

#define SNS_NEAR_PVC_JUDGE_DIS      800 /* 近距离600以内一定是高的障碍物 */

#define SNS_ECHO_HEGHT_COMPARE      50    /* 用于筛选高路沿，第二回波明显大于第一回波的类型 */
#define SNS_FILTER_FAR_ECHO_HEIGHT  40    /* 用于过滤第一回波后的第二回波 */   
#define SNS_FIRST_SECOND_HEIGHT_SUB 100

#define SNS_MAX_DE_DIS              5110
#define SNS_MAX_CE_DIS              5110
#define SNS_INVALID_DE_CE_DIS       0x3FF

#define DE_GRAVEL_FILTER_DIS          1000
#define DE_SIDE_GRAVEL_FILTER_MIN_DIS 1000
#define DE_SIDE_GRAVEL_FILTER_MAX_DIS 3200
#define KALMAN_R                    0.001
/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
extern SnsLocCfg_str SnsLocCfg[SNSIDNum];

/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


#endif /* end of SnsRawData_cfg_H */

