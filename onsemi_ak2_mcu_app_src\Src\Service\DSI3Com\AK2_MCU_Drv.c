/*
 * AK2_MCU_Drv.c
 *
 *  Created on: 2023年11月29日
 *      Author: 6000023281
 */
#include "AK2_MCU_Drv.h"
#include "TAU_COM.h"

/**********************************变量定义************************************/
IO_Ctrl_Str IO_Table[eCTRL_PIN_Num] =
{
    [eSNS_GROUP1_POWER_EN_PIN] = {.port = Port8, .pin = 1,},
    [eSNS_GROUP2_POWER_EN_PIN] = {.port = Port8, .pin = 12,},
    [eSNS_GROUP3_POWER_EN_PIN] = {.port = Port8, .pin = 11,},
};

/******************************************************************************/
/**
 * @brief   滤波函数接口
 */
/*****************************************************************************/
void AK2_PubAI_Filter(AK2_Pub_FilterCtrl_t *pCtrl)
{
	if(TRUE == pCtrl->Input)
	{
		pCtrl->Count += pCtrl->UpStep;
		if(pCtrl->Count > pCtrl->UpperLimit)
		{
			pCtrl->Count = pCtrl->UpperLimit;
		}
	}
	else
	{
		if(pCtrl->Count >= pCtrl->DownStep)
		{
			pCtrl->Count -= pCtrl->DownStep;
		}
		else
		{
			pCtrl->Count = pCtrl->LowerLimit;
		}
	}

	if(pCtrl->Count == pCtrl->UpperLimit)
	{
		pCtrl->Output = TRUE; 
	}
	else if(pCtrl->Count == pCtrl->LowerLimit)
	{
		pCtrl->Output = FALSE;
	}
	else
	{}
}

/******************************************************************************/
/**
 * @brief   清除滤波数据
 */
/*****************************************************************************/
void AK2_PubAI_ClrFilterData(AK2_Pub_FilterCtrl_t *pCtrl)
{
	if(NULL != pCtrl)
	{
		pCtrl->Count = 0;
		pCtrl->Input = FALSE;
		pCtrl->Output = FALSE;
	}
}

/******************************************************************************/
/**<pre>
 *函数名称: WriteIOOutPin
 *功能描述: 设置IO状态
 *输入参数: eIOPin:电源管脚枚举 value:IO Level
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
uint8 AK2_WriteIOOutPin(eIOPin_Ctrl eIOPin,uint8 value)
{
    if(eIOPin >= eCTRL_PIN_Num)
    {
        return 0xFF;
    }
    if(value == 0)
    {
        R_PORT_SetGpioOutput(IO_Table[eIOPin].port, IO_Table[eIOPin].pin, Low); 
    }
    else
    {
		R_PORT_SetGpioOutput(IO_Table[eIOPin].port,IO_Table[eIOPin].pin, High); 
    }
    return 0;
}

/******************************************************************************/
/**<pre>
 *函数名称: AK2_WriteEnSnsPin
 *功能描述: 配置SNS电源管脚状态
 *输入参数: value:三路电源管脚状态，每一个bit代表一路电源。0：关闭电源，1：使能电源
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void AK2_WriteEnSnsPin(uint8 value)
{
    uint8 LcIOCnt = 0;
    uint8 IOvalue = 0;
    for(LcIOCnt=0;LcIOCnt< eCTRL_PIN_Num;LcIOCnt++)
    {
        IOvalue = (value >> LcIOCnt) & 0x01;
        AK2_WriteIOOutPin(LcIOCnt,IOvalue);
    }
}

/******************************************************************************/
/**
 * @brief   DSIMaster0异步传输接口
 */
/*****************************************************************************/
DSISPI_TransReturn_en DSIMaster0_Spi_AsyncTransmitFunc(const uint8 * sendBuffer,uint8 * receiveBuffer,uint16 transferByteCount)
{
#if 0
    uint16 status;
    status = CSIH_DRV_MasterSendReceive((const uint8 *)sendBuffer,(uint8 *)receiveBuffer,transferByteCount,CSIH2_MASTER);
    if(status != 0)
    {
        return eDSISPI_TransReturn_N_OK;
    }
    else
    {
        /* DO NOTHING */
    }
#endif
    return eDSISPI_TransReturn_OK;
}

/******************************************************************************/
/**
 * @brief   DSIMaster1异步传输接口
 */
/*****************************************************************************/
DSISPI_TransReturn_en DSIMaster1_Spi_AsyncTransmitFunc(const uint8 * sendBuffer,uint8 * receiveBuffer,uint16 transferByteCount)
{
#if 0
    uint16 status;
    status = CSIH_DRV_MasterSendReceive((const uint8 *)sendBuffer,(uint8 *)receiveBuffer,transferByteCount,CSIH3_MASTER);
    if(status != 0)
    {
        return eDSISPI_TransReturn_N_OK;
    }
    else
    {
        /* DO NOTHING */
    }
#endif
    return eDSISPI_TransReturn_OK;
}

/******************************************************************************/
/**
 * @brief   CRC计算接口
 */
/*****************************************************************************/
uint16 DSI_SPI_CRC16Calculate(const uint8 *LcDataPtr,uint16 LwDataLen)
{
    return CRC16CalculateByMCU(LcDataPtr,LwDataLen);
}

/******************************************************************************/
/**
 * @brief   系统毫秒计时器接口
 */
/*****************************************************************************/
uint32 AK2_GetSys1msTickFunc(void)
{
    return GetSystemUseTimerMsCnt();
}

/******************************************************************************/
/**
 * @brief   系统1.6微秒计时器接口
 */
/*****************************************************************************/
uint32 AK2_GetSys1p6usTickFunc(void)
{
	return  (uint32)READTAUDCnt_1600ns();
}

/******************************************************************************/
/**
 * @brief   CAN外部车温度接口
 */
/*****************************************************************************/
AK2_Car_AmbientTempType AK2_ReadCAN_AppSignal_Car_AmbientTempType(void)
{
    AK2_Car_AmbientTempType LenuCar_AmbientTemp = 25;
    ReadCAN_AppSignal_Car_AmbientTempType(&LenuCar_AmbientTemp);
    return LenuCar_AmbientTemp;
}

/******************************************************************************/
/**
 * @brief   读电压状态接口
 */
/*****************************************************************************/
AK2_Signal_VoltageStatusType AK2_ReadPwrManage_GroupStatus(void)
{
    AK2_Signal_VoltageStatusType WorkVolSts = NORMAL_VOLTAGE;
    ReadPwrManage_GroupStatus(PM_APP,&WorkVolSts);
    return WorkVolSts;
}

/******************************************************************************/
/**
 * @brief   读探头状态接口
 */
/*****************************************************************************/
void AK2_ReadPwrManage_SnsPwrDiagStatus(AK2_SnsPowerDiagIdx_e LenuIdx, AK2_SnsPwrDiagStatus_e *pLenuPwrSts)
{
    ReadPwrManage_SnsPwrDiagStatus(LenuIdx, pLenuPwrSts);
}

/******************************************************************************/
/**
 * @brief   使能Master0_DCR1B外部中断
 */
/*****************************************************************************/
uint8 DSIMaster0_EnDCR1BFallInt(void)
{
    DSI_MASTER0_DCR1B_EnInt;
    return 0;
}

/******************************************************************************/
/**
 * @brief   使能Master0_DCR2B外部中断
 */
/*****************************************************************************/
uint8 DSIMaster0_EnDCR2BFallInt(void)
{
    DSI_MASTER0_DCR2B_EnInt;
    return 0;
}

/******************************************************************************/
/**
 * @brief   失能Master0_DCR1B外部中断
 */
/*****************************************************************************/
uint8 DSIMaster0_DisDCR1BInt(void)
{
    DSI_MASTER0_DCR1B_DisInt;
    return 0;
}

/******************************************************************************/
/**
 * @brief   失能Master0_DCR2B外部中断
 */
/*****************************************************************************/
uint8 DSIMaster0_DisDCR2BInt(void)
{
    DSI_MASTER0_DCR2B_DisInt;
    return 0;
}

/******************************************************************************/
/**
 * @brief   使能Master0_RFC外部中断
 */
/*****************************************************************************/
uint8 DSIMaster0_EnRFCInt(void)
{ 
    return 0;
}

/******************************************************************************/
/**
 * @brief   使能Master0_INTB外部中断
 */
/*****************************************************************************/
uint8 DSIMaster0_EnINTBInt(void)
{
    return 0;
}

/******************************************************************************/
/**
 * @brief   失能Master0_RFC外部中断
 */
/*****************************************************************************/
uint8 DSIMaster0_DisRFCInt(void)
{  
    return 0;
}

/******************************************************************************/
/**
 * @brief   失能Master0_INTB外部中断
 */
/*****************************************************************************/
uint8 DSIMaster0_DisINTBInt(void)
{
    return 0;
}

/******************************************************************************/
/**
 * @brief   获取MASTER0_INTB电平状态
 */
/*****************************************************************************/
uint8 DSIMaster0_ReadINTB_Pin(void)
{
    return DSI_MASTER0_INTB_IN_PIN;
}

/******************************************************************************/
/**
 * @brief   获取MASTER0_RFC电平状态
 */
/*****************************************************************************/
uint8 DSIMaster0_ReadRFC_Pin(void)
{
    return DSI_MASTER0_RFC_IN_PIN;
}

/******************************************************************************/
/**
 * @brief   Master0复位
 */
/*****************************************************************************/
uint8 DSIMaster0_SetRESB_Pin(void)
{
    DSI_MASTER0_RESB_PIN_SET;
    return 0;
}

/******************************************************************************/
/**
 * @brief   Master0正常工作
 */
/*****************************************************************************/
uint8 DSIMaster0_ClearRESB_Pin(void)
{
    DSI_MASTER0_RESB_PIN_CLEAR;
    return 0;
}

/******************************************************************************/
/**
 * @brief   使能Master1_DCR1B外部中断
 */
/*****************************************************************************/
uint8 DSIMaster1_EnDCR1BFallInt(void)
{
    DSI_MASTER1_DCR1B_EnInt;
    return 0;
}

/******************************************************************************/
/**
 * @brief   使能Master1_DCR2B外部中断
 */
/*****************************************************************************/
uint8 DSIMaster1_EnDCR2BFallInt(void)
{
    DSI_MASTER1_DCR2B_EnInt;
    return 0;
}

/******************************************************************************/
/**
 * @brief   失能Master1_DCR1B外部中断
 */
/*****************************************************************************/
uint8 DSIMaster1_DisDCR1BInt(void)
{
    DSI_MASTER1_DCR1B_DisInt;
    return 0;
}

/******************************************************************************/
/**
 * @brief   失能Master1_DCR2B外部中断
 */
/*****************************************************************************/
uint8 DSIMaster1_DisDCR2BInt(void)
{
    DSI_MASTER1_DCR2B_DisInt;
    return 0;
}

/******************************************************************************/
/**
 * @brief   使能Master1_RFC外部中断
 */
/*****************************************************************************/
uint8 DSIMaster1_EnRFCInt(void)
{ 
    return 0;
}

/******************************************************************************/
/**
 * @brief   使能Master1_INTB外部中断
 */
/*****************************************************************************/
uint8 DSIMaster1_EnINTBInt(void)
{
    return 0;
}

/******************************************************************************/
/**
 * @brief   失能Master1_RFC外部中断
 */
/*****************************************************************************/
uint8 DSIMaster1_DisRFCInt(void)
{  
    return 0;
}

/******************************************************************************/
/**
 * @brief   失能Master1_INTB外部中断
 */
/*****************************************************************************/
uint8 DSIMaster1_DisINTBInt(void)
{
    return 0;
}

/******************************************************************************/
/**
 * @brief   获取Master1_INTB电平状态
 */
/*****************************************************************************/
uint8 DSIMaster1_ReadINTB_Pin(void)
{
    return DSI_MASTER1_INTB_IN_PIN;
}

/******************************************************************************/
/**
 * @brief   获取Master1_RFC电平状态
 */
/*****************************************************************************/
uint8 DSIMaster1_ReadRFC_Pin(void)
{
    return DSI_MASTER1_RFC_IN_PIN;
}

/******************************************************************************/
/**
 * @brief   Master1复位
 */
/*****************************************************************************/
uint8 DSIMaster1_SetRESB_Pin(void)
{
    DSI_MASTER1_RESB_PIN_SET;
    return 0;
}

/******************************************************************************/
/**
 * @brief   Master1正常工作
 */
/*****************************************************************************/
uint8 DSIMaster1_ClearRESB_Pin(void)
{
    DSI_MASTER1_RESB_PIN_CLEAR;
    return 0;
}

