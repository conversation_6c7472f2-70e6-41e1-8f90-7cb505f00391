"""
打包脚本，用于将命令解析工具打包成可执行文件
"""

import os
import sys
import shutil
import subprocess

def build_exe():
    """
    使用PyInstaller打包程序
    """
    print("开始打包程序...")
    
    # 打包命令
    cmd = [
        "pyinstaller",
        "--name=命令解析工具",  # 可执行文件名称
        "--windowed",  # 使用窗口模式，不显示控制台
        "--onefile",  # 打包成单个可执行文件
        "--clean",  # 清理临时文件
        "--noconfirm",  # 不询问确认
        # "--icon=icon.ico",  # 图标文件，如果有的话
        "--add-data=command_history.json;.",  # 添加数据文件
        "command_analyzer_gui.py"  # 入口脚本
    ]
    
    # 执行打包命令
    subprocess.call(cmd)
    
    print("打包完成！")
    print(f"可执行文件位于: {os.path.abspath('dist/命令解析工具.exe')}")

if __name__ == "__main__":
    build_exe()
