/*
 * DSI3_COM.h
 *
 *  Created on: 2021年4月1日
 *      Author: 6000021992
 */

#ifndef _DSI3_COM_H_
#define _DSI3_COM_H_

#include "DSI_521_42.h"
#include "AK2_MCU_Drv.h"

#define PRINTF_DSI3COM              DSI3COM_DEBUG_PRINT



#define ChirpTime 4

#if (ChirpTime == 3)

#define SnsRCCChipTime e_RCCChipTime_3us

#if (DSISlaveSnsNum == 6)

#define TDMA_PDCM_PERIOD (uint16)1257


#else

#define TDMA_PDCM_PERIOD (uint16)831

#endif


#define TDMA_PDCM_S1_StartTime (uint16)50
#define TDMA_PDCM_S2_StartTime (uint16)221
#define TDMA_PDCM_S3_StartTime (uint16)400
#define TDMA_PDCM_S4_StartTime (uint16)590
#define TDMA_PDCM_S5_StartTime (uint16)790
#define TDMA_PDCM_S6_StartTime (uint16)1003



#define TDMA_PDCM_S1_ET (uint16)(45)
#define TDMA_PDCM_S2_ET (uint16)(210)
#define TDMA_PDCM_S3_ET (uint16)(383)
#define TDMA_PDCM_S4_ET (uint16)(567)
#define TDMA_PDCM_S5_ET (uint16)(762)
#define TDMA_PDCM_S6_ET (uint16)(968)

#define TDMA_PDCM_S1_LT (uint16)(60)
#define TDMA_PDCM_S2_LT (uint16)(233)
#define TDMA_PDCM_S3_LT (uint16)(417)
#define TDMA_PDCM_S4_LT (uint16)(612)
#define TDMA_PDCM_S5_LT (uint16)(818)
#define TDMA_PDCM_S6_LT (uint16)(1038)

#else

#define SnsRCCChipTime e_RCCChipTime_4us

#if (DSISlaveSnsNum == 6)

#define TDMA_PDCM_PERIOD (uint16)1600
#define TDMA_PDCM_S1_StartTime (uint16)50
#define TDMA_PDCM_S2_StartTime (uint16)278
#define TDMA_PDCM_S3_StartTime (uint16)519
#define TDMA_PDCM_S4_StartTime (uint16)776
#define TDMA_PDCM_S5_StartTime (uint16)1048
#define TDMA_PDCM_S6_StartTime (uint16)1338


#define TDMA_PDCM_S1_ET (uint16)(45)
#define TDMA_PDCM_S2_ET (uint16)(268)
#define TDMA_PDCM_S3_ET (uint16)(504)
#define TDMA_PDCM_S4_ET (uint16)(756)
#define TDMA_PDCM_S5_ET (uint16)(1018)
#define TDMA_PDCM_S6_ET (uint16)(1298)

#define TDMA_PDCM_S1_LT (uint16)(60)
#define TDMA_PDCM_S2_LT (uint16)(298)
#define TDMA_PDCM_S3_LT (uint16)(541)
#define TDMA_PDCM_S4_LT (uint16)(801)
#define TDMA_PDCM_S5_LT (uint16)(1078)
#define TDMA_PDCM_S6_LT (uint16)(1378)

#elif (DSISlaveSnsNum == 3)

#define TDMA_PDCM_PERIOD (uint16)1600
#define TDMA_PDCM_S1_StartTime (uint16)50
#define TDMA_PDCM_S2_StartTime (uint16)278
#define TDMA_PDCM_S3_StartTime (uint16)519

#define TDMA_PDCM_S1_ET (uint16)(45)
#define TDMA_PDCM_S2_ET (uint16)(268)
#define TDMA_PDCM_S3_ET (uint16)(504)

#define TDMA_PDCM_S1_LT (uint16)(60)
#define TDMA_PDCM_S2_LT (uint16)(298)
#define TDMA_PDCM_S3_LT (uint16)(541)

#endif

#endif


/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/


typedef enum
{
    DSI_MASTER_Init_Check = 0,
    DSI_MASTER_Init_Active,
    DSI_MASTER_Init_Finish,
    DSI_MASTER_READY,
    DSI_MASTER_DIAG,

    DSI_MASTER_ToSleep,
    DSI_MASTER_Sleep,
    DSI_MASTER_StatusNum
}DSI_MASTER_Status;

typedef enum
{
    DSICHL_INIT = 0,
    DSICHL_DIAG,
    DSICHL_IDLE,

    DSICHL_Sleep,
    DSICHL_CMD_STOP2IDLE,
    DSICHL_CMD_DONE,

    DSICHL_DM,
    DSICHL_CRM,
    DSICHL_PDCM,

    DSICHL_WorkStatus_NUM,
    DSICHL_WorkStatus_NULL = 0xFF,

}DSICHL_WorkStatus_en;

typedef enum
{
    DSICHL_WaiteDCRB_NULL = 0,
    DSICHL_WaiteDCRB2READCRMRes,
    DSICHL_WaiteDCRB2AUTOBRC,
    DSICHL_WaiteDCRB2READPDCM,
    DSICHL_WaiteDCRB2CRMTXEND,
    DSICHL_WaiteDCRB2DMEND,
    DSICHL_SubStatus_NUM,

}DSICHLDCRB_SubStatus_en;

typedef enum
{
    CRM2PDCM,
    CRM_NResponse,
    CRM2Response,
    DM,
    AUTO_BRC,
    ReadCRMResponse,
    ReadPDCMResponse,
    CRMCMD_Type_Num,
}CRMCMD_Type_en;

typedef enum
{
    DSIChl1 = 0,
    DSIChl2,
    DSIChlNum,
}DSIChlID_en;

typedef enum
{
    DSIPinNor = 0,
    DSIPinShortToPwr,
    DSIPinShortToGnd
}DSIPinSts_en;

typedef uint8 (*DSI_IO_IntFunc)(void);

typedef struct
{
    DSI_FCCBitTime_e FCCBitTime;  /** @brief FCC Bit Time */
    DSI_RCCChipTime_e RCCChipTime; /** @brief RCC Chip Time */
    uint8 DSI_Timing_Offset;          /** @brief DSI时间补偿 */
    uint8 DM_Start_Time_BC;           /** @brief 寻址首个脉冲时间 */
    FRAME_TDMA_SCHEME_str *pFRAME_TDMA_SCHEME; /** @brief TDMA 参数 */
    

    
}DSIShl_SetParam_st;


typedef struct
{
    DSI_VDSI_e     VDSI;/** @brief VDSI 电压 */
    uint8          Wave_Fall;   
    uint8          Wave_Rise; 
    DSIShl_SetParam_st  DSIShl_SetParam[DSIChlNum];

    DSI_IO_IntFunc EnDCR1BFallIntFunc;  /** @brief 使能DCR1B下降沿中断接口 */
    DSI_IO_IntFunc EnDCR2BFallIntFunc;  /** @brief 使能DCR2B下降沿中断接口 */
    DSI_IO_IntFunc EnRFCFallIntFunc;    /** @brief 使能RFC下降沿中断接口 */
    DSI_IO_IntFunc EnINTBFallIntFunc;   /** @brief 使能INTB下降沿中断接口 */
    
    DSI_IO_IntFunc DisDCR1BIntFunc;     /** @brief 关闭DCR1B中断接口 */
    DSI_IO_IntFunc DisDCR2BIntFunc;     /** @brief 关闭DCR2B中断接口 */
    DSI_IO_IntFunc DisRFCFallIntFunc;   /** @brief 关闭RFC下降沿中断接口 */
    DSI_IO_IntFunc DisINTBFallIntFunc;  /** @brief 关闭INTB下降沿中断接口 */

    DSI_IO_IntFunc ReadINTB_PinFunc;    /** @brief 读取INTB Pin电平接口 */
    DSI_IO_IntFunc ReadRFC_PinFunc;     /** @brief 读取RFC Pin电平接口 */
    
    DSI_IO_IntFunc SetRESB_PinFunc;     /** @brief 设置RESB Pin为高电平接口 */
    DSI_IO_IntFunc ClearRESB_PinFunc;   /** @brief 设置RESB Pin为低电平接口 */

    DSI_SPI_Interface_str DSI_SPI_Interface;
}DSICOM_SetParam_st;




typedef struct
{
    uint16 wFrameCnt;

    uint16 AutoBRCMaxCnt;
    
    uint16 ExpectBRCTotal;

}CRM_Status_str;


typedef struct
{

    CRMCMD_Type_en CMD_type;
    DSIMasterID_en DSIMasterID;
    DSIChlSel_en   DSIChlSEL;

}ActiveCRM_Msg_str;

typedef enum 
{
    eDCRBFlg_NONE = 0,
    eDCRBFlg_StartBRC,
    eDCRBFlg_StopBRC,
}DCRBFlg_e;

typedef struct
{
    DSICHL_WorkStatus_en DSICHLWorkStatus;

    DSICHLDCRB_SubStatus_en DSIDCRBSubStatus;


    ActiveCRM_Msg_str ActiveCRM_Msg;

    CRM_Status_str ActiveCRMCMDStatus;

    uint16 DSI_STAT;

    DCRBFlg_e DCRBFlg;
    uint16 DCRBIntCnt;
    uint8 StartBRCTimeCnt;

    uint16 DCRBTimeOutCnt;
    uint16 DSIWorkTimeOutCnt;
    uint8 DCRBTimeOutFlgCnt;
    uint8 DSIWorkTimeOutFlgCnt;


}DSI_Chl_Ctrl_STATUS_Str;



typedef struct
{
    E521_42WRRegData_un ICStatusReg;    /** @brief 521.42IC状态寄存器 */
                        /*bit15  OT, bit14 DSI_1_CMD_OVR ,bit13 DSI_1_UV, bit12 DSI_1_PDCMREC,
                          bit11  DSI_1_CRMREC, bit10 DSI_1_TDMA_SCHEME_DEFINED ,bit9 VCCUV_RFCB, bit8 DSI_0_CMD_OVR*/
                        /*bit7  DSI_0_UV, bit6 DSI_0_PDCMREC ,bit5 DSI_0_CRMREC, bit4 DSI_0_TDMA_SCHEME_DEFINED,
                          bit3  CLKREF_ERROR, bit2 CMD_INC ,bit1 CRC_REEOR, bit0 UND_CMD*/
    DSI_MASTER_Status TaskStatus;  /** @brief 521.42 工作模式 */
    uint32 SPIRxCrcErrCnt;
    uint32 SPITxErrCnt;
    DSI_Chl_Ctrl_STATUS_Str DSICHLCtrlStatus[DSIChlNum];
	uint8 WaiteDCRB2READPDCMFlg;

}DSI_MASTER_CTRL_Str;

typedef struct
{
    uint8 SlaveSnsNum;
    
    uint16 cPdcmFrameSize;  /*cPdcmFrameSize = FrameStatusSize + (PacketStatussize + PacketDataSize) * PacketCount*/
}DSI_Chl_Cfg_str;

typedef enum
{
    IN_Init = 0,    /* IC正在初始化      */
    OK_Resume,      /* 故障恢复/未发生故障 */
    IN_Resume,      /* 故障恢复中     */
    NOT_Resume,     /* 故障恢复失败      */
    Resume_Num,  
}
ERR_Resume_en;

/* Elmos521.42 故障计数 */
typedef struct
{
    uint16 OT_Cnt;
    uint16 CLKERR_Cnt;
    uint16 DSIUV_OC_Cnt;
    uint16 RESET_ERR_Cnt;
    uint16 CMD_ERR_Cnt;
    uint16 VCC_UV_Cnt;
    uint16 VDSI_UV_Cnt;
    uint16 SLEF_TEST_Cnt;

    uint16 GWait_RFCCnt;
	uint16 RFCErrCnt;		/* RFC拉高失败计数*/
    uint16 GINIT_ErrCnt;	/* 初始化错误计数*/ 
	uint16 INTB_LowCnt;
    uint16 GUV_Resume_Cnt;
    uint16 Check_Time;/* 用于InitActive上电初始化超时复位，Ready状态下IC检测*/
}Elmos42ERR_Cnt_str;


/* Elmos521.42 故障类型 */
typedef struct
{
    uint8 VCC_UV_Flg;     /* VCC欠压标志       */
    uint8 DSI_UV_Flg;     /* DSI欠压标志       */
    uint8 DSI_OC_Flg;     /* 过流标志          */
    uint8 VDSI_UV_Flg;    /* VDSI欠压标志      */
    uint8 OT_Flg;         /* 过温标志          */
    uint8 CMD_ERR_Flg;    /* 命令故障标志        */
    uint8 RESET_Flg;      /* 复位标志          */
    uint8 CLK_ERR_Flg;    /* 参考时钟错误        */
    
}Elmos42ERR_Flg_str;

typedef struct
{
   uint8 DSIChl_ErrFlg; /* 通道发生故障标志   */
   uint8 DSI_UV_Flg;    /* 通道欠压标志位           */
   uint8 DSI_OC_Flg;    /* 通道过流标志位           */
   
}DSI_ChlErr_str;

typedef struct
{
    ERR_Resume_en ERR_ResumeFlg;            /* 故障恢复标志            */
    DSI_ChlErr_str DSI_ChlErr[DSIChlNum];   /* DSI通道故障           */ 
    uint8 Self_Test_Flg;                       /* IC自检错误标志          */
    uint8 CLK_ERR_Flg;                         /* IC参考时钟错误标志        */
    uint8 RegCfg_Flg;                          /* 寄存器数据回读错误标志 */
    uint8 VCC_UV_Flg;                          /* VCC欠压标志           */
    uint8 VDSI_UV_Flg;                         /* VDSI欠压标志          */
    uint8 OT_Flg;                              /* IC过温标志            */ 
    uint8 RFC_TimeOut;                         /* 等待RFC超时           */     
    uint8 CMD_ERR_Flg;                         /* 命令故障标志            */
 
    
    //uint8 Init_Fail_Flg;      /* IC初始化失败标志         */
    
}IC_ERRStatus_str;

typedef struct
{
	SPIDataTransSts_en SPIDataTransSts;
	uint16 u16TransTimeOut;    /*uint: ms*/ 
	uint16 u16TransTimeOutCnt;
}DSIMasterSPITransCtrl_str;

typedef struct
{
    DSIMasterID_en DSIMasterID;
    DSICOM_SetParam_st DSICOM_SetParam;

    DSI_Chl_Cfg_str DSICfg[DSIChlNum];

    DSI_MASTER_CTRL_Str MasterCtrl;
    
    Elmos42ERR_Flg_str  ERR42Flg;
    IC_ERRStatus_str    IC_ERRStatus;
    Elmos42ERR_Cnt_str  ERR_Cnt;

    uint8 DSI3_CfgStep;
    WRReg_en DSI3_CfgWRRegCnt;

    uint16 *pWRRegDataGroup;
	DSIMasterSPITransCtrl_str SPITransCtrl;
}DSI_MASTER_Cfg_str;


typedef struct
{
    DSI_Status_str DSIStatus;
    SnsCRMRES_Data_str Data;

}CRM_DSIChlRES_str;

typedef struct
{
    DSIMasterID_en DSIMasterID;
    DSIChlID_en DSIChlID;
    CRM_DSIChlRES_str ResData;

}CRM_RESPONSE_Data_str;

typedef struct
{
    DSI_Status_str DSIStatus;
    SnsPDCMRES_Data_str Data;

}PDCM_RES_Packet_str;


typedef struct
{
    PDCM_FRAME_STATUS_Str FRAME_STATUS;
    PDCM_RES_Packet_str Data[DSISlaveSnsNum];

}PDCM_DSIChlRES_str;


typedef struct
{
    DSIMasterID_en DSIMasterID;
    DSIChlID_en DSIChlID;
    PDCM_DSIChlRES_str PDCMResData;
}PDCM_RESPONSE_Data_str;

typedef enum
{
	DSIMasterIC_Temperature_Normal = 0,
	DSIMasterIC_Temperature_Over	
}DSIMasterICTempSts_e;

/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/
#define BIT_MASK_U8(n)  ((uint8)1U << (n))


/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************************/
/**
 * @brief   DCRB事件超时时间 ms
 */
/*****************************************************************************/
#define TimeOutDCRB2AUTOBRC 5
#define TimeOutDCRB2CRMTXEND 5
#define TimeOutDCRB2READCRMRes 5
#define TimeOutDCRB2READPDCM 5
#define TimeOutDCRB2DMEND    5

/******************************************************************************/
/**
 * @brief   DSI工作模式超时时间 ms
 */
/*****************************************************************************/
#define TimeOutDSI_DM  5
#define TimeOutDSI_CRM  5
#define TimeOutDSI_PDCM  5
#define TimeOutDSI_DONE  10
#define TimeOutDSI_STOP2IDLE  2


#define DSI_SC_CR_SE_ErrMASK (uint8)0x34

#define DSI_RES_ERRORBIT_EE_MASK (uint8)0x80
#define DSI_RES_ERRORBIT_LE_MASK (uint8)0x40
#define DSI_RES_ERRORBIT_SC_MASK (uint8)0x20
#define DSI_RES_ERRORBIT_CR_MASK (uint8)0x10
#define DSI_RES_ERRORBIT_SP_MASK (uint8)0x08
#define DSI_RES_ERRORBIT_SE_MASK (uint8)0x04
#define DSI_RES_ERRORBIT_UV_MASK (uint8)0x02
#define DSI_RES_ERRORBIT_CE_MASK (uint8)0x01

/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
extern DSI_MASTER_Cfg_str G_DSI_MASTER[DSIMasterNum];
/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/

extern DSIReturnType_en InitDSIWork(DSIMasterID_en DSIMasterID,DSICOM_SetParam_st * pDSICfgParam);
extern DSIReturnType_en DSIComWorkMainFunc();
extern DSI_MASTER_Status GetDSIComWorkStatus(DSIMasterID_en DSIMasterID);
extern DSIReturnType_en SetDSIDCRBSubStatus(DSIMasterID_en DSIMasterID,DSIChlID_en DSIChlID,DSICHLDCRB_SubStatus_en DCRB_SubStatus);
extern DSICHLDCRB_SubStatus_en GetDSIDCRBSubStatus(DSIMasterID_en DSIMasterID,DSIChlID_en DSIChlID);
extern DSIReturnType_en SetDSICHLWorkStatus(DSIMasterID_en DSIMasterID,DSIChlID_en DSIChlID,DSICHL_WorkStatus_en DSICHL_WorkStatus);
extern DSICHL_WorkStatus_en GetDSICHLWorkStatus(DSIMasterID_en DSIMasterID,DSIChlID_en DSIChlID);
extern uint16 GetExpectBRCNum(DSIMasterID_en DSIMasterID,DSIChlID_en DSIChlID);
extern IC_ERRStatus_str GetICErrStatus(DSIMasterID_en DSIMasterID);


extern DSIReturnType_en TransCRMCmd(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL,CRMCMD_Type_en CMD_type,TRANS_CRM_DM_Str *pInCRMData,uint32 *pTransCompleteTime);
extern DSIReturnType_en TransCRMCmdSPIAsyn(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL,CRMCMD_Type_en CMD_type,TRANS_CRM_DM_Str *pInCRMData);
extern DSIReturnType_en TransReadCRMResp(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL);
extern DSIReturnType_en TransReadPDCMResp(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL);
extern DSIReturnType_en TransStopDSI(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL);
extern DSIChlSel_en SetAutoBRCPart(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL,uint16 MaxBRCNum);
extern DSIReturnType_en TransAddAutoBRCNum(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL,uint16 LwAddBRCNum);

extern void DSI_Master_DCRB_ISR(DSIMasterID_en DSIMasterID,DSIChlID_en DSIChlID);
extern void ReadDSIMasterICTempSts(DSIMasterICTempSts_e *pSts);
extern DSIPinSts_en GetDSIPinSts(DSIMasterID_en LeDSIMasterID,DSIChlID_en LeDSIChlID);
extern void ClearSPIRxCrcErrCnt(DSIMasterID_en DSIMasterID);
extern uint32 GetSPIRxCrcErrCnt(DSIMasterID_en DSIMasterID);
extern bool GetDSIMasterErrSts(void);
extern const DSICOM_SetParam_st DSICOM_Param[DSIMasterNum];

#endif /* _DSI3_COM_H_ */
