/******************************************************************************
 * @file       System_Schedule_types.h
 * @brief
 * @date       2025-03-04 13:33:34
 * <AUTHOR>
 * @copyright  Longhorn Automotive Electronics Technology (Xiamen) Co., Ltd.
 *****************************************************************************/


#ifndef __SYSTEM_SCHEDULE_TYPES__
#define __SYSTEM_SCHEDULE_TYPES__
/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "Types.h"


/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/
typedef void (*SCHED_tpfTask)(void);

typedef enum
{
    SCHED_TASK1_ID = 0u,

    SCHED_TASK2_ID,

    SCHED_TASK3_ID,

    SCHED_TASK4_ID,

    SCHED_TASK5_ID,

    SCHED_TASK6_ID,

    SCHED_TASK7_ID,

    SCHED_TASK8_ID,

    SCHED_TASK9_ID,

    SCHED_TASK10_ID,

    SCHED_TASK11_ID,

    SCHED_TASK12_ID,

    SCHED_TASK13_ID,

    SCHED_TASK14_ID,

    SCHED_TASK_ID_NUM
} SCHED_TASKINDEX_TYPE;

typedef enum
{
    SCHED_TASK_ACTIVE = 0,
    SCHED_TASK_READY,
    SCHED_TASK_BLOCKED,
    SCHED_UNDEFINED
} SCHED_tenuTaskStatus;


typedef struct
{
    SCHED_tenuTaskStatus    enuTaskStatus;
    uint16                   u16TaskCounter;
} TaskManageTypes;



/******************************************************************************
 * @Const Declaration
 *****************************************************************************/



/******************************************************************************
 * @Variable Declaration
 *****************************************************************************/



/******************************************************************************
 * @Function Declaration
 *****************************************************************************/



#endif
