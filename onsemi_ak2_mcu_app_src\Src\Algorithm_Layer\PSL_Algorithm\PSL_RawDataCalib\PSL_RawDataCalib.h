/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * SnsRawDataCalib: 
 * Created on: 2024-02-23 14:29
 * Original designer: 22866
 ******************************************************************************/
#ifndef PSL_RawDataCalib_H
#define PSL_RawDataCalib_H

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "Types.h"
#include "SnsRawData_Type.h"

/******************************************************************************
 * Compiler option used to distinguish inclusion done by the owner
 *****************************************************************************/

/******************************************************************************
 * Inclusion of private header files
 *****************************************************************************/

/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/

/* 定义原始回波筛选的高度类型 */
typedef struct
{
    uint16 u16PVC_PileHeight;
    uint16 u16BigWallHeight;
    uint16 u16SecondWallHeight;
    uint16 u16CurbHeight;
}PSLSnsCalibHeightType;

/* 定义ECU端阈值的梯度，5cm一个阶梯*/
typedef enum
{
    SNS_PSL_DIS_5cm = 0,
    SNS_PSL_DIS_10cm, 
    SNS_PSL_DIS_15cm,
    SNS_PSL_DIS_20cm,
    SNS_PSL_DIS_25cm, 
    SNS_PSL_DIS_30cm,
    SNS_PSL_DIS_35cm,
    SNS_PSL_DIS_40cm, 
    SNS_PSL_DIS_45cm,
    SNS_PSL_DIS_50cm,

    SNS_PSL_DIS_55cm,
    SNS_PSL_DIS_60cm, 
    SNS_PSL_DIS_65cm,
    SNS_PSL_DIS_70cm,
    SNS_PSL_DIS_75cm, 
    SNS_PSL_DIS_80cm,
    SNS_PSL_DIS_85cm,
    SNS_PSL_DIS_90cm, 
    SNS_PSL_DIS_95cm,
    SNS_PSL_DIS_100cm,

    SNS_PSL_DIS_105cm,
    SNS_PSL_DIS_110cm, 
    SNS_PSL_DIS_115cm,
    SNS_PSL_DIS_120cm,
    SNS_PSL_DIS_125cm, 
    SNS_PSL_DIS_130cm,
    SNS_PSL_DIS_135cm,
    SNS_PSL_DIS_140cm, 
    SNS_PSL_DIS_145cm,
    SNS_PSL_DIS_150cm,

    SNS_PSL_DIS_155cm,
    SNS_PSL_DIS_160cm, 
    SNS_PSL_DIS_165cm,
    SNS_PSL_DIS_170cm,
    SNS_PSL_DIS_175cm, 
    SNS_PSL_DIS_180cm,
    SNS_PSL_DIS_185cm,
    SNS_PSL_DIS_190cm, 
    SNS_PSL_DIS_195cm,
    SNS_PSL_DIS_200cm,

    SNS_PSL_DIS_205cm,
    SNS_PSL_DIS_210cm, 
    SNS_PSL_DIS_215cm,
    SNS_PSL_DIS_220cm,
    SNS_PSL_DIS_225cm, 
    SNS_PSL_DIS_230cm,
    SNS_PSL_DIS_235cm,
    SNS_PSL_DIS_240cm, 
    SNS_PSL_DIS_245cm,
    SNS_PSL_DIS_250cm,
    
    SNS_PSL_DIS_255cm,
    SNS_PSL_DIS_260cm, 
    SNS_PSL_DIS_265cm,
    SNS_PSL_DIS_270cm,
    SNS_PSL_DIS_275cm, 
    SNS_PSL_DIS_280cm,
    SNS_PSL_DIS_285cm,
    SNS_PSL_DIS_290cm, 
    SNS_PSL_DIS_295cm,
    SNS_PSL_DIS_300cm,

	SNS_PSL_DIS_305cm,
    SNS_PSL_DIS_310cm, 
    SNS_PSL_DIS_315cm,
    SNS_PSL_DIS_320cm,
    SNS_PSL_DIS_325cm, 
    SNS_PSL_DIS_330cm,
    SNS_PSL_DIS_335cm,
    SNS_PSL_DIS_340cm, 
    SNS_PSL_DIS_345cm,
    SNS_PSL_DIS_350cm,
    
    SNS_PSL_DIS_355cm,
    SNS_PSL_DIS_360cm, 
    SNS_PSL_DIS_365cm,
    SNS_PSL_DIS_370cm,
    SNS_PSL_DIS_375cm, 
    SNS_PSL_DIS_380cm,
    SNS_PSL_DIS_385cm,
    SNS_PSL_DIS_390cm, 
    SNS_PSL_DIS_395cm,
    SNS_PSL_DIS_400cm,
    SNS_PSL_DIS_TOTAL_NUM
}PSLSnsDisStepType;

/* 重新定义ECU端判断高度阈值的梯度，10cm一个阶梯*/
typedef enum
{
    SNS_PSL_DIS_HIGH_10cm =0, 
    SNS_PSL_DIS_HIGH_20cm,
    SNS_PSL_DIS_HIGH_30cm,
    SNS_PSL_DIS_HIGH_40cm, 
    SNS_PSL_DIS_HIGH_50cm,

    SNS_PSL_DIS_HIGH_60cm, 
    SNS_PSL_DIS_HIGH_70cm,
    SNS_PSL_DIS_HIGH_80cm,
    SNS_PSL_DIS_HIGH_90cm, 
    SNS_PSL_DIS_HIGH_100cm,

    SNS_PSL_DIS_HIGH_110cm, 
    SNS_PSL_DIS_HIGH_120cm,
    SNS_PSL_DIS_HIGH_130cm,
    SNS_PSL_DIS_HIGH_140cm, 
    SNS_PSL_DIS_HIGH_150cm,

    SNS_PSL_DIS_HIGH_160cm, 
    SNS_PSL_DIS_HIGH_170cm,
    SNS_PSL_DIS_HIGH_180cm,
    SNS_PSL_DIS_HIGH_190cm, 
    SNS_PSL_DIS_HIGH_200cm,

    SNS_PSL_DIS_HIGH_210cm, 
    SNS_PSL_DIS_HIGH_220cm,
    SNS_PSL_DIS_HIGH_230cm,
    SNS_PSL_DIS_HIGH_240cm, 
    SNS_PSL_DIS_HIGH_250cm,
    
    SNS_PSL_DIS_HIGH_260cm, 
    SNS_PSL_DIS_HIGH_270cm,
    SNS_PSL_DIS_HIGH_280cm,
    SNS_PSL_DIS_HIGH_290cm, 
    SNS_PSL_DIS_HIGH_300cm,

    SNS_PSL_DIS_HIGH_310cm, 
    SNS_PSL_DIS_HIGH_320cm,
    SNS_PSL_DIS_HIGH_330cm,
    SNS_PSL_DIS_HIGH_340cm, 
    SNS_PSL_DIS_HIGH_350cm,
    
    SNS_PSL_DIS_HIGH_360cm, 
    SNS_PSL_DIS_HIGH_370cm,
    SNS_PSL_DIS_HIGH_380cm,
    SNS_PSL_DIS_HIGH_390cm, 
    SNS_PSL_DIS_HIGH_400cm,
    SNS_PSL_DIS_HIGH_TOTAL_NUM
}PSLSnsDisHighStepType;


/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
extern const uint16 Gu16FLS_FRS_StandardThresholdTableForPSL[SNS_PSL_DIS_TOTAL_NUM];
extern const uint16 Gu16FLS_FRS_ChirpThresholdTableForPSL[SNS_PSL_DIS_TOTAL_NUM];


extern const uint16 Gu16RLS_RRS_StandardThresholdTableForPSL[SNS_PSL_DIS_TOTAL_NUM];
extern const uint16 Gu16RLS_RRS_ChirpThresholdTableForPSL[SNS_PSL_DIS_TOTAL_NUM];


extern const uint16 *Gpu16PSLSnsThresholdTable[PDC_SNS_GROUP_NUM][2];

/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/

extern uint16 Gu16FLS_FRS_StandardThresholdTableInRAMForPSL[SNS_PSL_DIS_TOTAL_NUM];
extern uint16 Gu16FLS_FRS_ChirpThresholdTableInRAMForPSLForPSL[SNS_PSL_DIS_TOTAL_NUM];


extern uint16 Gu16RLS_RRS_StandardThresholdTableInRAMForPSL[SNS_PSL_DIS_TOTAL_NUM];
extern uint16 Gu16RLS_RRS_ChirpThresholdTableInRAMForPSL[SNS_PSL_DIS_TOTAL_NUM];

extern PSLSnsCalibHeightType GStrPSLObjJudgeStandardThresholdTableInRAM[SNS_PSL_DIS_HIGH_TOTAL_NUM];
extern PSLSnsCalibHeightType GStrPSLObjJudgeChirpThresholdTableInRAM[SNS_PSL_DIS_HIGH_TOTAL_NUM];


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
void CopySnsPSLCalibWidthAndThresholdToRam(void);



#endif /* end of SnsRawDataCalib_H */

