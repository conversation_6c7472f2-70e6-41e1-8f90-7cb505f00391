/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: STBCDrv.c
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/
#include "types.h"
#include "STBCDrv.h"

/**********************************宏定义**************************************/
/* 低功耗模式 */
#define STOP_MODE                       0u
#define DEEP_STOP_MODE                  1u
#define LOW_POWER_MODE                  DEEP_STOP_MODE

/* 复位原因 */
#define RESF_DEEP_RESET                ( 0x00000400UL )
#define RESF_POWER_ON                  ( 0x00000200UL )
#define RESF_EXT_RESET                 ( 0x00000100UL )
#define RESF_CVM_RESET                 ( 0x00000080UL )
#define RESF_LVI_RESET                 ( 0x00000040UL )
#define RESF_CLMA3_RESET               ( 0x00000020UL )
#define RESF_CLMA1_RESET               ( 0x00000010UL )
#define RESF_CLMA0_RESET               ( 0x00000008UL )
#define RESF_WDTA1_RESET               ( 0x00000004UL )
#define RESF_WDTA0_RESET               ( 0x00000002UL )
#define RESF_SOFT_RESET                ( 0x00000001UL )


/******************************************************************************/
/**<pre>
 *函数名称: STBC_EnterStopMode
 *功能描述: 进入停止模式
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void STBC_EnterStopMode(void)
{
    /* Disable interrupts */
    DI();
    #if(LOW_POWER_MODE == STOP_MODE)/* Stop mode：唤醒后继续运行 */
    /* Clear wake-up factor */
    STBC_WUF0WUFC0 = _STBC_WUF0_CLEAR;
    STBC_WUF1WUFC1 = _STBC_WUF1_CLEAR;
    STBC_WUFISOWUFC_ISO0 = _STBC_WUF_ISO0_CLEAR;
    /* Enable wake-up factor */
    STBC_WUF0WUFMSK0 = _STBC_FACTOR_DEFAULT_VALUE & _STBC_WUF0_FACTOR_INTP0;
    STBC_WUF1WUFMSK1 = _STBC_FACTOR_DEFAULT_VALUE;
    STBC_WUFISOWUFMSK_ISO0 = _STBC_FACTOR_DEFAULT_VALUE;
    /* Start stop mode */
    WPROTRPROTCMD0 = _WRITE_PROTECT_COMMAND;
    STBC0STPT = _STBC_STOP_MODE_ENTERED;
    STBC0STPT = (uint32) ~_STBC_STOP_MODE_ENTERED;
    STBC0STPT = _STBC_STOP_MODE_ENTERED;
    while (STBC0STPT == _STBC_STOP_MODE_ENTERED)
    {
        NOP();
    }
    #else   /* DeepStop mode：唤醒后直接复位 */
    STBC_WUF0.WUFC0 = _STBC_WUF0_CLEAR;
    STBC_WUF20.WUFC20 = _STBC_WUF20_CLEAR;
    STBC_WUF0.WUFMSK0 = _STBC_FACTOR_DEFAULT_VALUE & _STBC_WUF0_FACTOR_INTP0;
    STBC_WUF20.WUFMSK20 = _STBC_FACTOR_DEFAULT_VALUE;
    RESCTL.RESFC |= _RESFC_RESET_FLAG_CLEAR;
    WPROTR.PROTCMD0 = _WRITE_PROTECT_COMMAND;
    STBC0.PSC = _STBC_DEEP_STOP_MODE_ENTERED;
    STBC0.PSC = (uint32) ~_STBC_DEEP_STOP_MODE_ENTERED;
    STBC0.PSC = _STBC_DEEP_STOP_MODE_ENTERED;
    while (STBC0PSC == _STBC_DEEP_STOP_MODE_ENTERED)
    {
        NOP();
    }
    #endif
}

/******************************************************************************/
/**<pre>
 *函数名称: CheckSysResetFactor
 *功能描述: 检测系统复位原因
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void CheckSysResetFactor(void)
{
    uint32 Lu32RegResfVal = 0ul;
    uint32 Lu32RegResfrVal = 0ul;
    uint32 Lu32RegVal = 0u;
    /* 获取复位原因 */
    Lu32RegResfVal = RESCTLRESF;
    Lu32RegResfrVal = RESCTLRESFR;
    /* 清除复位原因 */
    RESCTLRESFC = Lu32RegResfVal;
    RESCTLRESFCR = Lu32RegResfrVal;
    /* 检查复位原因 */
    if(Lu32RegResfVal == Lu32RegResfrVal)
    {
        if(Lu32RegResfVal&RESF_DEEP_RESET)
        {
            do
            {
                Lu32RegVal = 0x00000000UL;
                WPROTRPROTCMD0 = 0x000000A5UL;
                STBC_IOHOLDIOHOLD = Lu32RegVal;
                STBC_IOHOLDIOHOLD = ~Lu32RegVal;
                STBC_IOHOLDIOHOLD = Lu32RegVal;
            }
            while(PORTPPROTS0 != 0x00000000UL);

            STBC_WUF0.WUFMSK0 = 0xFFFFFFFFul;
            STBC_WUF0.WUFC0 = 0xFFFFFFFFul;
        }
    }
}


