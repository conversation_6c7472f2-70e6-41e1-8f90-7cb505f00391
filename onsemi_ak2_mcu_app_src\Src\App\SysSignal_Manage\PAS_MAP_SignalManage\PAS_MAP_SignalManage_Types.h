/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * PDCSignalManage.h: 
 * Created on: 2022-04-14
 * Original designer: 
 ******************************************************************************/

#ifndef __PAS_MAP_SIGNALMANAGE_TYPES_H__
#define __PAS_MAP_SIGNALMANAGE_TYPES_H__

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "types.h"

/******************************************************************************
 * Compiler option used to distinguish inclusion done by the owner
 *****************************************************************************/


/******************************************************************************
 * Inclusion of private header files
 *****************************************************************************/


/*点云数据发送最多数据个数*/
#define POINTCLOUD_MAX_SIZE    12
#define DEFAULT_INVALID_DISTANCE_TO_CAN      (uint16)511

/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/
typedef struct 
{
	uint16 u16PDCSignal_DE1;
	uint16 u16PDCSignal_DE2;
	uint16 u16PDCSignal_DE3;
	uint16 u16PDCSignal_DE4;
	uint16 u16PDCSignal_DE5;
	uint16 u16PDCSignal_DE6;
}PDCSignal_DEStructType;

typedef struct 
{
	uint16 u16PDCSignal_CE1_2;
	uint16 u16PDCSignal_CE2_3;
	uint16 u16PDCSignal_CE3_4;
	uint16 u16PDCSignal_CE4_5;
	uint16 u16PDCSignal_CE5_6;
}PDCSignal_CEStructType;


typedef enum
{
	FRONT_GROUP = 0u,
    REAR_GROUP,
	PDC_GROUP_NUM,
}PDCSignal_SnsGroupType;
    
typedef enum
{	
    PDCSIGNAL_LEFT_SIDE = 0U,
	PDCSIGNAL_LEFT_CORNER ,
	PDCSIGNAL_LEFT_MIDDLE,
	PDCSIGNAL_RIGHT_MIDDLE,
	PDCSIGNAL_RIGHT_CORNER,
	PDCSIGNAL_RIGHT_SIDE,
    PDCSIGNAL_CHANNEL_NUM,
}PDCSignal_SnsChannelType;

typedef enum
{
	PDC_ZONE_LeftCorner = 0,
	PDC_ZONE_LeftMiddle,
	PDC_ZONE_RightMiddle,
	PDC_ZONE_RightCorner,
	PDC_ZONE_ZoneChannel,
}PDCSignal_ZoneChannelType;

typedef enum
{
	PDC_Work_off = 0,
	PDC_Work_Standby,
	PDC_Work_Front_And_Rear_Active,
	PDC_Work_FrontActive_and_RearFailure,
	PDC_Work_FrontFailure_and_RearActive,
	PDC_Work_SystemFailure,  //前后系统均错误  
}PdcSignal_PDCWorkStatusType;



typedef enum
{
	MapObj_None,
	MapObj_Point,
	MapObj_Straight0_Corner,
	MapObj_Straight1_Corner,
	MapObj_Straight2_Corner,
}PdcSiagnal_MapObjType;


typedef struct
{
    bool    bObjWriteLockFlg;
    uint8   u8PDCSignal_MapObjIndex;
	uint8   u8PDCSignal_MapObjHeightProb; 
    uint8   u8PDCSignal_MapObjHeight;
    uint8   u8PDCSignal_MapObjType;
    uint8   u8PDCSignal_MapObjProb;
	sint16  s16PDCSignal_MapObjP1X;       		
    sint16  s16PDCSignal_MapObjP1Y;
    sint16  s16PDCSignal_MapObjP2X;       		
    sint16  s16PDCSignal_MapObjP2Y;
    uint32  u32PDCSignal_PAS_MapObjTimestamp;
}PdcSignal_MapObjInfoType;

/*新增点云数据结构体*/
typedef struct
{
    bool    bPointCloudLockFLg;
	uint8   u8PointCloud_ObjHeightProb; 
    uint8   u8PointCloud_ObjHeight;
    uint8   u8PointCloud_ObjType;
    uint8   u8PointCloud_ObjProb;
	sint16  s16PointCloud_ObjPX;       		
    sint16  s16PointCloud_ObjPY;
}PdcPointCloud_ObjInfoType;



typedef struct 
{
	PdcSignal_PDCWorkStatusType 	PdcSignal_PDCWorkStatus;
	uint16							PdcSignal_DE_Distance[PDC_GROUP_NUM][PDCSIGNAL_CHANNEL_NUM];
	uint16							PdcSignal_CE_Distance[PDC_GROUP_NUM][PDCSIGNAL_CHANNEL_NUM];
	uint32							PdcSignal_MapObjTimeStamp[PDC_GROUP_NUM][PDCSIGNAL_CHANNEL_NUM];
	uint8							PdcSignal_MapObjIndex[PDC_GROUP_NUM][PDCSIGNAL_CHANNEL_NUM];
	PdcSignal_MapObjInfoType		PdcSignal_MapObjInfo[PDC_GROUP_NUM][PDCSIGNAL_CHANNEL_NUM];
}PdcSignal_DataStructType;

typedef enum
{
	TrailerHitchDetecte_No = 0,
	TrailerHitchDetecte_Yes ,
	TrailerHitchDetecte_Unknow,
}PdcSignal_TrailerHitchDetectedType;


/***************************end of LX Project*****************************/

typedef enum
{
    SIDE_ZONE1 = 0u,
    SIDE_ZONE2,
    SIDE_ZONE3,
    SIDE_ZONE4,
    SIDE_ZONE5,
    SIDE_ZONE6,
    SIDE_ZONE7,
    SIDE_ZONE8,
    SIDE_PDC_ZONE_NUM,
}PDCSignal_SideSnsZoneType;

typedef enum
{
    SIDE_LEFT = 0u,
    SIDE_RIGHT,
    SIDE_PDC_DIR_NUM,
}PDCSignal_SideSnsDirType;


typedef enum
{
    PDC_ZONE1 = 0u,
    PDC_ZONE2,
    PDC_ZONE_NUM,
}PDCSignal_SnsZoneType;


typedef struct
{
	uint16 FL1_Distance; 
	uint16 FL2_Distance; 
	uint16 FML1_Distance; 
	uint16 FML2_Distance;
	uint16 FMR1_Distance; 
	uint16 FMR2_Distance; 
	uint16 FR1_Distance; 
	uint16 FR2_Distance;
	
	uint16 RL1_Distance; 
	uint16 RL2_Distance; 
	uint16 RML1_Distance; 
	uint16 RML2_Distance;
	uint16 RMR1_Distance; 
	uint16 RMR2_Distance; 
	uint16 RR1_Distance; 
	uint16 RR2_Distance;
	
	uint16 LSF1_Distance; 
	uint16 LSF2_Distance; 
	uint16 LSFM1_Distance; 
	uint16 LSFM2_Distance;
	uint16 LSRM1_Distance; 
	uint16 LSRM2_Distance;
	uint16 LSR1_Distance; 
	uint16 LSR2_Distance;

	uint16 RSF1_Distance; 
	uint16 RSF2_Distance; 
	uint16 RSFM1_Distance; 
	uint16 RSFM2_Distance;
	uint16 RSRM1_Distance; 
	uint16 RSRM2_Distance;
	uint16 RSR1_Distance; 
	uint16 RSR2_Distance;
}PDCSignal_ZoneDisType;


typedef enum
{
    BUMPER_AREA1 = 0u,
    BUMPER_AREA2,
    BUMPER_AREA3,
    BUMPER_AREA4,
    BUMPER_AREA5,
    BUMPER_AREA6,    
    BUMPER_AREA_NUM,
}PDCSignal_BumperAreaType;

typedef enum
{
    PDC_SNS_NO_ERROR = 0u,
    PDC_SNS_ERROR,
    PDC_SNS_ERROR_NUM,
}PDCSignal_SnsErrorType;


typedef enum
{
	PdcSignal_SDW_CLOSE = 0,
	PdcSignal_SDW_OPEN,
}PdcSignal_SDWStatusType;

/* 为给DTC 模块提供车衣检测DTC，新增探头标号索引值 */
typedef enum
{
	SNS_INX_NONE = -1,
	SNS_INX_01 = 0,
	SNS_INX_02,
	SNS_INX_03,
	SNS_INX_04,
    SNS_INX_05,
    SNS_INX_06,
    
    SNS_INX_07,
    SNS_INX_08,
    SNS_INX_09,
    SNS_INX_10,
    SNS_INX_11,
    SNS_INX_12,
    SNS_INX_NUM
}PdcSiagnal_SnsInxType;



#endif /* end of __PAS_MAP_SIGNALMANAGE_TYPES_H__ */


