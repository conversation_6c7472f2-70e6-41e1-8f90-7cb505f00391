#ifndef __PDCToCan_H__
#define __PDCToCan_H__
/********************************************************************** 
* Includes 
************************************************************************/ 
#include "types.h"
#include "CAN_AppSignalManage.h"
#include "PAS_MAP_SignalManage_Types.h"


/******************************************************************************
* Constants and macros
*******************************************************************************/
#define PASLogicOpen 1

#define PAS_POINTCLOUD_DATA_TEST  0

#define INVALID_PP_DIS     0x1FF
#define INIT_PP_DIS        0xFB
#define MAX_PP_DIS_MM      2500

#define MAP_INVALID_INDEX  31
#define MAP_MAX_TO_CAN_NUM 20

/* 移植AK1 车衣检测逻辑 */
#define PDC_COVER_CHECK_START_SPD              100
#define PDC_COVER_CHECK_END_SPD                1450
#define PDC_COVER_CHECK_START_DIS              50
#define PDC_COVER_CHECK_END_DIS                220   /* 检测距离距离由40cm改为20cm */
#define PDC_COVER_CHECK_DIS_SUB                20    /* DE变化值由±5cm改为±2cm  */
#define PDC_COVER_EXIT_CHECK_DIS_SUB           100

#define PDC_COVER_BUFFER_CNT                   5
#define PDC_COVER_360_ANGLE                  6.2832
#define PDC_COVER_180_ANGLE                  3.1416
#define PDC_COVER_145_ANGLE                  2.5307
#define PDC_COVER_130_ANGLE                  2.2689
#define PDC_COVER_120_ANGLE                  2.0944
#define PDC_COVER_35_ANGLE                   0.6109
#define PDC_COVER_30_ANGLE                   0.5236
#define PDC_COVER_MOV_DIS_10_M               10000

#define PAS_HIGH_TO_LOW_ACTIVE_SPD           1200
#define PAS_LOW_TO_HIGH_CLOSE_SPD            1500


/* PAS工作状态  */
typedef enum
{
	Work_off = 0,
	Work_Standby,
	Work_Front_And_Rear_Active,
	Work_FrontActive_and_RearFailure,
	Work_FrontFailure_and_RearActive,
	Work_SystemFailure,  //前后系统均错误
}PAS_WorkStatus;

/******************************************************************************
* Typedef definitions
*******************************************************************************/



typedef struct 
{
	PAS_WorkStatus	          enuPAS_WorkStatus;
	LowVolPwrMdType           enuLowPowerMode;
	ADAS_PAS_EnableStsType    u8PAS_Enable;
	ADAS_APAStatusType        enuAPAWorkStatus;
    uint8                     u8VoltageNormalFlag;
}PDCToCAN_PASDataType;

typedef enum
{
	Vehicle_Valid = 0,
	Vehicle_InValid,
}VehicleValidType;

typedef struct
{
	
	Car_SpeedVildType u8VehicleValid;
	uint16 u8VehicleSpeed;	
}PDCInfoFromCanType;

typedef struct
{
	uint8 u8FrontSystem_FailureFlag;
	uint8 u8RearSystem_FailureFlag;
	uint8 u8Front_and_RearSystem_FailureFlag;

}PDCErrorFlagType;


/******************************************************************************
* External objects
*******************************************************************************/
extern uint16 Gu16SideDis[4];


/******************************************************************************
* Global Functions
*******************************************************************************/
extern void PAS_MAP_StateUpdateAndToCan_Task(void);
extern void PAS_MAP_To_CANInit(void);
PDCErrorFlagType PAS_GetErrorFlag(void);
PDCErrorFlagType PAS_GetErrorFlagForTask(void);


#endif /* __PDCToCan_H__ */



