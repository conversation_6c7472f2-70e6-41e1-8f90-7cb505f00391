/*==================================================================================================
*   Project              : RTD AUTOSAR 4.4
*   Platform             : CORTEXM
*   Peripheral           : Sbc_fs23
*   Dependencies         : 
*
*   Autosar Version      : 4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : 0.8.0
*   Build Version        : S32K3_RTD_0_8_0_D2303_ASR_REL_4_4_REV_0000_20230310
*
*   (c) Copyright 2020 - 2022 NXP Semiconductors
*   All Rights Reserved.
*
*   NXP Confidential. This software is owned or controlled by NXP and may only be
*   used strictly in accordance with the applicable license terms. By expressly
*   accepting such terms or by downloading, installing, activating and/or otherwise
*   using the software, you are agreeing that you have read, and that you agree to
*   comply with and are bound by, such license terms. If you do not agree to be
*   bound by the applicable license terms, then you may not retain, install,
*   activate or otherwise use the software.
==================================================================================================*/

#ifndef CDD_SBC_FS23_CFG_H
#define CDD_SBC_FS23_CFG_H

/**
*   @file    CDD_SBC_FS23_Cfg.h
*
*   @addtogroup CDD_SBC_FS23_DRIVER_CONFIGURATION SBC_FS23 Driver Configurations
*   @{
*/

#ifdef __cplusplus
extern "C"
{
#endif

/*==================================================================================================
*                                        INCLUDE FILES
* 1) system and project includes
* 2) needed interfaces from external units
* 3) internal and external interfaces from this unit
==================================================================================================*/
//#include "Mcal.h"
//#include "Dem.h"
//#include "CDD_Sbc_fs23_BOARD_InitPeripherals_PBcfg.h"

/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/
/**
* @brief            Create defines with the IDs assigned to Sbc_fs23 Long duration timer configurations.
*                   These IDs will be transmitted as input parameters to Sbc_fs23_ConfigureLdt() API.
*/
/**
* @brief          The symbolic names of all the available devices (given in a particular configuration).
*/
#define SbcConf_LdtConf_LdtConfig_0    ((uint32)0U)
/**
* @brief   Collection of all configuration structures declarations.
*/
#define SBC_FS23_CONFIG_EXT 

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/
#define SBC_FS23_SPI_TRANSMIT                   (0U)

#define SBC_FS23_I2C_TRANSMIT                   (1U)
/**
* @brief          Precompile Support On.
* @details        VARIANT-PRE-COMPILE: Only parameters with "Pre-compile time"
*                 configuration are allowed in this variant.
*/
#define SBC_FS23_PRECOMPILE_SUPPORT         (STD_ON)
/**
* @brief   Switches the Development Error Detection and Notification ON or OFF.
*
*/
#define SBC_FS23_DEV_ERROR_DETECT         (STD_OFF)
/**
* @brief   Switch to globaly enable/disable the production error reporting.
*/
#define SBC_FS23_DISABLE_DEM_REPORT_ERROR_STATUS         (STD_ON)
/**
* @brief          Support for version info API.
*/
#define SBC_FS23_VERSION_INFO_API         (STD_OFF)

#define SBC_FS23_COMM_METHOD       (SBC_FS23_SPI_TRANSMIT)
#define SBC_FS23_SPI_LEVEL_DELIVERED    (2U)

/**
* @brief           Enable/Disable the API for setting the Long Duration Timer.
*/
#define SBC_FS23_LDT_API         (STD_ON)
/**
* @brief           Enable/Disable the APIs for Wake-up functionality.
*/
#define SBC_FS23_WAKEUP_API         (STD_ON)
/**
* @brief           Enable/Disable the APIs for High-side driver functionality.
*/
#define SBC_FS23_HSX_API         (STD_ON)
/**
* @brief           Enable/Disable the use of Sbc_fs23_SetAmux API.
*/
#define SBC_FS23_SET_AMUX_API         (STD_ON)
/**
* @brief           Enable/Disable the use of Sbc_fs23_SetRegulatorState API
*/
#define SBC_FS23_SET_REGULATOR_API         (STD_ON)
/**
* @brief           Enable/Disable the use of Sbc_fs23_SetOperatingMode API.
*/
#define SBC_FS23_SET_OPMODE_API         (STD_ON)

/**
* @brief            Maximum duration before returning SBC_FS23_E_TIMEOUT. The unit of measurement is given by SbcTimeoutMechanism.
*/
#define SBC_FS23_TIMEOUT_DURATION                (2U)

/**
* @brief            Enable/Disable the Cantrcv_43_fs23 usage.
*/
#define SBC_FS23_SBC_FS23_CANTRCV_SUPPORT        (STD_OFF)

/**
* @brief            Enable/Disable the Lintrcv_43_fs23 usage.
*/
#define SBC_FS23_SBC_FS23_LINTRCV_SUPPORT         (STD_OFF)

/**
* @brief            OsIf counter type used in timeout
*/
//#define SBC_FS23_TIMEOUT_OSIF_COUNTER_TYPE         (OSIF_COUNTER_DUMMY)
/**
* @brief            OsIf counter system used for timeout mechnism
*/
#define SBC_FS23_OSIF_COUNTER_SYSTEM_USED          (STD_OFF)

/**
* @brief            Support for the user mode.
*/
#define SBC_FS23_ENABLE_USER_MODE_SUPPORT         (STD_OFF)
/**
* @brief            Enable/Disable the External Watchdog Primitives.
*/
#define SBC_FS23_EXTERNAL_WATCHDOG_API           (STD_OFF)


/*==================================================================================================
*                                             ENUMS
==================================================================================================*/

/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/
#ifdef __cplusplus
}
#endif

/** @} */

#endif /* CDD_SBC_FS23_CFG_H */

