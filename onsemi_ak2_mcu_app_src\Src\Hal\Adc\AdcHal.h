/******************************************************************************
 * Copyright (c) Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * All Rights Reserved.
 *
 * FileName : AdcHal.h
 * Brief    : None
 * Date     : 2021-07-08 18:41
 * Author   : 22446
 ******************************************************************************/
#ifndef   ADCHAL_H
#define   ADCHAL_H

/******************************************************************************/
/* Inclusion of other module header files ------------------------------------*/
/******************************************************************************/

/******************************************************************************/
/* Inclusion of private header files -----------------------------------------*/
/******************************************************************************/
#include "LhRule.h"

/**
 * defgroup   AdcDete
 * @{
 */
#ifdef __cplusplus
extern "C"{
#endif
/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/
/******************************************************************************/
/**
 * @brief   ADC电压检测通道组索引
 */
/*****************************************************************************/
typedef enum
{
    ADCDETE_GROUP_ADC0_GROUP1 = 0,
    ADCDETE_GROUP_NUM
}AdcDete_GroupType;

/******************************************************************************/
/**
 * @brief   ADC0通道组1索引
 */
/*****************************************************************************/
typedef enum
{
    ADC0_GROUP1_SUPPLY_POWER = 0,
    ADC0_GROUP1_FRONT_SNS_POWER,
    ADC0_GROUP1_REAR_SNS_POWER,
    ADC0_GROUP1_SIDE_SNS_POWER,
    ADC0_SBC_AMUX_TEMP,
    ADC0_GROUP1_NUM
}AdcDete_ChlIdType;

/*****************************************************************************/
/**
 * @brief   ADC检测工作结构体类型
 */
/*****************************************************************************/
typedef struct
{
    boolean vDetectBusy;   /* 检测忙 */
    uint8   u8ChannelNum;
    uint32  u32ResultLastOffset;
    uint16  *pAdcDeteValue;
    uint8   u8AdcDeteTimeOut;
    uint8   u8AdcDeteTimeOutCnt;
}AdcDete_WorkType;

/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************************/
/**
 * @brief           初始化
 * @param           None
 * @return          None
 */
/******************************************************************************/
extern void AdcHalInit(void);

/******************************************************************************/
/**
 * @brief           反初始化
 * @param           None
 * @return          None
 */
/******************************************************************************/
extern void AdcHal_Deinit(void);

/******************************************************************************/
/**
 * @brief           主函数
 * @param           None
 * @return          None
 */
/******************************************************************************/
extern void AdcHal_MainFunc(void);

/******************************************************************************/
/**
 * @brief           ADC 检测完成回调
 * @param[in]       LenuGroupId : 通道索引
 * @param[in]       callbackInfo: 回调信息
 * @param[in]       userData    : 用户句柄
 * @return          None
 */
/******************************************************************************/
extern void AdcHal_Adc0Group1CallBack(void);

/******************************************************************************/
/**
* @brief 		  获取ADC结果
* @param[in] 	  LenuGroupId : ADC模块索引
* @param[in] 	  Lu8AdcChannel : ADC通道索引
* @return		  ADC结果
*/
/******************************************************************************/
uint16 AdcHal_GetAdcResult(AdcDete_GroupType LenuGroupId,AdcDete_ChlIdType Lu8AdcChannel);

/**
 * end of group   AdcHal
 * @}
 */
#ifdef __cplusplus
}
#endif

#endif   /* end of ADCHAL_H */
