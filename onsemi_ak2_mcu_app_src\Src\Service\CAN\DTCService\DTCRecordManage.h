
#ifndef   __DTCRECORDMANAGE_H__
#define   __DTCRECORDMANAGE_H__

/******************************************************************************* 
* Includes 
********************************************************************************/ 
#include "Types.h"

/****************************************************************************** 
* Constants and macros 
*******************************************************************************/

/****************************************************************************** 
* External objects 
********************************************************************************/ 


/******************************************************************************* 
* Global Functions 
********************************************************************************/ 
void InitDTCStatus(void);
void IGNONDTCStatus(void);
void DTCTestingResultManage(uint8 LcDTCNum,uint8 LcTestResult);
#endif
