"""
命令解析工具主程序
"""

import re
import sys
from command_parser import (
    CommandParserFactory,
    ResponseParserFactory
)

class CommandAnalyzer:
    """命令解析工具"""
    
    def __init__(self):
        """初始化命令解析工具"""
        self.command_history = []
        self.current_command = None
        self.current_response = None
    
    def parse_hex_string(self, hex_string):
        """
        解析十六进制字符串
        
        Args:
            hex_string: 十六进制字符串，如"02 50 ff ff ff ff 7a"
            
        Returns:
            字节列表
        """
        # 移除所有空白字符
        hex_string = re.sub(r'\s+', '', hex_string)
        
        # 检查字符串长度是否为偶数
        if len(hex_string) % 2 != 0:
            raise ValueError("十六进制字符串长度必须为偶数")
        
        # 检查是否包含非十六进制字符
        if not all(c in "0123456789abcdefABCDEF" for c in hex_string):
            raise ValueError("十六进制字符串只能包含0-9、a-f、A-F")
        
        # 将字符串转换为字节列表
        return [int(hex_string[i:i+2], 16) for i in range(0, len(hex_string), 2)]
    
    def analyze_command(self, hex_string):
        """
        解析命令
        
        Args:
            hex_string: 十六进制字符串，如"02 50 ff ff ff ff 7a"
            
        Returns:
            解析结果字典
        """
        try:
            # 解析十六进制字符串
            data = self.parse_hex_string(hex_string)
            
            # 创建命令解析器
            parser = CommandParserFactory.create_parser(data)
            
            # 解析命令
            result = parser.parse(data)
            
            # 保存当前命令
            self.current_command = result
            self.command_history.append({"command": result, "response": None})
            
            return result
        except Exception as e:
            return {"error": str(e)}
    
    def analyze_response(self, hex_string):
        """
        解析响应
        
        Args:
            hex_string: 十六进制字符串，如"00 00 06 45 a5 a1 a6"
            
        Returns:
            解析结果字典
        """
        try:
            # 解析十六进制字符串
            data = self.parse_hex_string(hex_string)
            
            # 如果没有当前命令，无法确定响应类型
            if not self.current_command:
                raise ValueError("没有当前命令，无法确定响应类型")
            
            # 创建响应解析器
            parser = ResponseParserFactory.create_parser(self.current_command["type"])
            
            # 解析响应
            result = parser.parse(data)
            
            # 保存当前响应
            self.current_response = result
            self.command_history[-1]["response"] = result
            
            return result
        except Exception as e:
            return {"error": str(e)}
    
    def analyze_interaction(self, command_hex, response_hex):
        """
        解析命令和响应交互
        
        Args:
            command_hex: 命令十六进制字符串
            response_hex: 响应十六进制字符串
            
        Returns:
            解析结果字典
        """
        command_result = self.analyze_command(command_hex)
        response_result = self.analyze_response(response_hex)
        
        return {
            "command": command_result,
            "response": response_result
        }
    
    def get_command_history(self):
        """
        获取命令历史
        
        Returns:
            命令历史列表
        """
        return self.command_history
    
    def clear_history(self):
        """清除命令历史"""
        self.command_history = []
        self.current_command = None
        self.current_response = None


def parse_input(input_text):
    """
    解析用户输入
    
    Args:
        input_text: 用户输入文本
        
    Returns:
        解析结果元组 (命令类型, 命令数据)
    """
    # 移除前导和尾随空白字符
    input_text = input_text.strip()
    
    # 检查是否为退出命令
    if input_text.lower() in ["exit", "quit", "q"]:
        return ("exit", None)
    
    # 检查是否为帮助命令
    if input_text.lower() in ["help", "h", "?"]:
        return ("help", None)
    
    # 检查是否为历史命令
    if input_text.lower() in ["history", "hist"]:
        return ("history", None)
    
    # 检查是否为清除历史命令
    if input_text.lower() in ["clear", "cls"]:
        return ("clear", None)
    
    # 尝试解析为命令和响应
    parts = input_text.split("->")
    if len(parts) == 2:
        command = parts[0].strip()
        response = parts[1].strip()
        return ("interaction", (command, response))
    
    # 默认为单个命令
    return ("command", input_text)


def print_help():
    """打印帮助信息"""
    print("命令解析工具帮助：")
    print("  输入格式：")
    print("    1. 单个命令：02 50 ff ff ff ff 7a")
    print("    2. 命令和响应：02 50 ff ff ff ff 7a -> 00 00 06 45 a5 a1 a6")
    print("  特殊命令：")
    print("    help, h, ? - 显示帮助信息")
    print("    history, hist - 显示命令历史")
    print("    clear, cls - 清除命令历史")
    print("    exit, quit, q - 退出程序")


def main():
    """主函数"""
    analyzer = CommandAnalyzer()
    
    print("命令解析工具")
    print("输入 'help' 获取帮助，输入 'exit' 退出程序")
    
    while True:
        try:
            # 获取用户输入
            user_input = input("> ")
            
            # 解析用户输入
            command_type, command_data = parse_input(user_input)
            
            # 处理特殊命令
            if command_type == "exit":
                break
            elif command_type == "help":
                print_help()
            elif command_type == "history":
                history = analyzer.get_command_history()
                if not history:
                    print("没有命令历史")
                else:
                    for i, item in enumerate(history):
                        print(f"[{i+1}] 命令: {item['command']}")
                        print(f"    响应: {item['response']}")
            elif command_type == "clear":
                analyzer.clear_history()
                print("命令历史已清除")
            elif command_type == "command":
                result = analyzer.analyze_command(command_data)
                print(f"解析结果: {result}")
            elif command_type == "interaction":
                command, response = command_data
                result = analyzer.analyze_interaction(command, response)
                print(f"命令解析结果: {result['command']}")
                print(f"响应解析结果: {result['response']}")
        except Exception as e:
            print(f"错误: {e}")


if __name__ == "__main__":
    main()
