import sys

def calculate_crc8(data, polynomial, init_value):
    crc = init_value
    
    # 初始化CRC表
    crc_table = []
    for i in range(256):
        crc_value = i
        for j in range(8):
            if crc_value & 0x80:
                crc_value = (crc_value << 1) ^ polynomial
            else:
                crc_value <<= 1
            crc_value &= 0xFF  # 确保结果是8位
        crc_table.append(crc_value)
    
    # 计算CRC
    for byte in data:
        crc = crc_table[crc ^ byte]
    
    return crc

def solve_init_value(data, expected_crc, polynomial):
    """
    通过反向计算推导初始值
    """
    # 创建反向CRC表
    reverse_table = [None] * 256
    for i in range(256):
        reverse_table[calculate_crc8([0], polynomial, i)] = i
    
    # 从最后一个字节开始反向计算
    current_crc = expected_crc
    for byte in reversed(data):
        # 找到能够产生current_crc的前一个CRC值
        for prev_crc in range(256):
            if calculate_crc8([byte], polynomial, prev_crc) == current_crc:
                current_crc = prev_crc
                break
    
    return current_crc

# 已知多项式
polynomial = 0x2F  # x8+x5+x3+x2+x+1

# 解析示例数据
def parse_hex_string(hex_string):
    # 移除所有空白字符
    hex_string = hex_string.strip()
    
    # 将字符串转换为字节列表
    return [int(hex_string[i:i+2], 16) for i in range(0, len(hex_string), 2)]

# 示例数据
examples = [
    'FF00EFFE',
    'FFC30D00',
    '02120000'
]

print('尝试推导CRC8初始值...')
print(f'多项式: 0x{polynomial:02X} (x8+x5+x3+x2+x+1)')
print()

# 解析示例数据并尝试推导初始值
for example in examples:
    data_with_crc = parse_hex_string(example)
    data = data_with_crc[:-1]
    expected_crc = data_with_crc[-1]
    
    print(f'示例: {example}')
    print(f'数据: {[f"0x{b:02X}" for b in data]}')
    print(f'CRC8: 0x{expected_crc:02X}')
    
    # 尝试推导初始值
    init_value = solve_init_value(data, expected_crc, polynomial)
    print(f'推导的初始值: 0x{init_value:02X}')
    
    # 验证
    calculated_crc = calculate_crc8(data, polynomial, init_value)
    print(f'验证CRC8: 0x{calculated_crc:02X} ({"成功" if calculated_crc == expected_crc else "失败"})')
    print()

# 尝试找到一个适用于所有示例的初始值
print('尝试找到适用于所有示例的初始值...')
for init_value in range(256):
    all_match = True
    
    for example in examples:
        data_with_crc = parse_hex_string(example)
        data = data_with_crc[:-1]
        expected_crc = data_with_crc[-1]
        
        calculated_crc = calculate_crc8(data, polynomial, init_value)
        if calculated_crc != expected_crc:
            all_match = False
            break
    
    if all_match:
        print(f'找到适用于所有示例的初始值: 0x{init_value:02X}')
        
        # 验证
        print('\n验证所有示例:')
        for example in examples:
            data_with_crc = parse_hex_string(example)
            data = data_with_crc[:-1]
            expected_crc = data_with_crc[-1]
            
            calculated_crc = calculate_crc8(data, polynomial, init_value)
            print(f'示例: {example}')
            print(f'计算CRC8: 0x{calculated_crc:02X}')
            print(f'预期CRC8: 0x{expected_crc:02X}')
            print(f'验证结果: {"成功" if calculated_crc == expected_crc else "失败"}')
            print()
        
        break
else:
    print('未找到适用于所有示例的初始值')
