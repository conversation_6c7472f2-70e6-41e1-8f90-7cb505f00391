/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * APASignalManage.c: 
 * Created on: 2022-3-22 19:03
 * Original designer: 22866
 ******************************************************************************/

#ifndef __PSL_STATE_MANAGE_H__
#define __PSL_STATE_MANAGE_H__


 /******************************************************************************* 
 * Includes 
 ********************************************************************************/ 
#include "CAN_AppSignalManage.h"
#include "PSL_AppSignalManage.h"




/* ============================================================================================== */
/* Type definitions																				  */
/* ============================================================================================== */




/*PSL工作状态*/
typedef enum
{
	PSL_Work_OFF,
	PSL_Work_Standby,
	PSL_Work_Searching,
	PSL_Work_Guidance,
	PSL_Work_Failure,
}ApaSignal_PSLWorkStatusType;


/******************************************************************************
 * 描述 : PSL工作处理数据
 * 设计索引:
 *****************************************************************************/

typedef struct
{
    uint8 u8Ecuerror; 
    uint8 u8Rensorerror; 
    uint8 u8CommRelatederror; 
    uint8 u8VoltageRelatederror;
    uint8 u8Etcerror;
}APAWorkManage_error_record;

/******************************************************************************
 * 描述 : PSL工作处理数据
 * 设计索引:
 *****************************************************************************/
typedef struct
{
    
    /* 请求申请车身坐标ID号标志 */
    uint8 u8ApprCurCarCoorIdFlg;
    Boolean vPSLFuncDG;
    LowVolPwrMdType u8BCMpowermode;
    Car_SpeedType u16VehicleSpeedValue;
    uint8 u8VehicleValidValue;
    ADAS_PSL_EnableStsType ADAS_PSL_ENABLE_FLAG;
    ADAS_APAStatusType enuPSL_APA_Status;
    ADAS_slot_ID_SelectedType u8ADASslotIDSelected;
    ApaSignal_PSLWorkStatusType u8APACantx_PSLWorkstatus;          
    
}PSLWorkManage_TrigDataType;


extern PSLWorkManage_TrigDataType GstrPSLWorkMagTrigData;
/***************************************************************************/
/* function prototypes                                  */
/***************************************************************************/
/*******************PSL工作状态反馈接口*************/
extern void PSLSignal_WritePSLWorkState(ApaSignal_PSLWorkStatusType LenuPSLWorkStatus);
extern void PSLSignal_ReadPSLWorkState(ApaSignal_PSLWorkStatusType *LenuPSLWorkStatus);


extern void PSLWorkStatusManageInit(void);
extern void PSLStateManageMain(void);


#endif

