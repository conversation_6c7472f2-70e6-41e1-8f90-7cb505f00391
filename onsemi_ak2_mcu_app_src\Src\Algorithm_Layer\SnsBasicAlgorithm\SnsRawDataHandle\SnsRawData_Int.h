/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * SnsRawData_Int: 
 * Created on: 2022-12-16 10:43
 * Original designer: AntonyFang
 ******************************************************************************/

#ifndef SnsRawData_Int_H
#define SnsRawData_Int_H

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "SnsRawData_cfg.h"


/******************************************************************************
 * Compiler option used to distinguish inclusion done by the owner
 *****************************************************************************/


/******************************************************************************
 * Inclusion of private header files
 *****************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
extern PDCSnsRawDataType GstrPDCSnsRawData[PDC_SNS_GROUP_NUM];
extern PDCSnsUseOdoType  GstrPDCSnsUseOdo;
#if VS_SIMULATE_ENABLE
extern float GfMessageTime;
extern uint32 GdSystemMsTimer; 
#endif
extern PDCSnsDE_CE_DataType GstrSnsDE_CE_Data[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];
extern StdDEFilterCtrlType StdDEFilterCtrl[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];
extern PDCSnsCarMovStsType  GstrSnsCarMovSts[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];
extern uint16 Gu16SnsPdcDistance[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];
extern uint16 Gu16SnsPdcMapDistance[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];
extern uint8  Gu8OptEstimateCosineFlg[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];

extern SnsSigGroupDataCacheType GstrSnsSigGroupDataCache[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];
extern SnsSigGroupDisFollowType GstrSnsSigGroupDisFollow[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];

extern uint8  Gu8CompleteFirsRoundDataFlg;
extern uint16 Gu16CarSpdForSnsUse; 
extern sint16 Gs16CarStrAngleForSnsUse;

/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
void PDCSnsRawDataClear(PDCSnsGroupType LenuSnsGroup,PDCSnsChannelType LenuSnsCh);
void PDCSnsRawDataInit(PDCSnsGroupType LenuSnsGroup);
void PDCSnsRawDataPowerOnInit(void);
void PDCSnsOdoSendToCAN(void);
#if VS_SIMULATE_ENABLE
void PDCSnsRawDataUpdate(PDCSnsGroupType LenuSnsGroup,PDCSnsChannelType LenuSnsCh, uint8 *Lu8DataPtr);
#else
void PDCSnsRawDataUpdate(void);
#endif
void PDCSnsGetOdoInf(float LfX_Coor, float LfY_Coor, float LfYawAngle,float LfDrvDis);
void PDCSnsClearUpdateFlag(PDCSnsGroupType LenuSnsGroup,PDCSnsChannelType LenuSnsCh);
uint8 ReadPDCSnsUpdateFlag(PDCSnsGroupType LenuSnsGroup,PDCSnsChannelType LenuSnsCh);
void PDCSnsCarMovStsInit(PDCSnsGroupType LenuSnsGroup);
void JudgeCarMovSts(PDCSnsGroupType LenuSnsGroup,PDCSnsChannelType LenuSnsCh);
void PDCSnsCarMovSts_PDC_Sns_Init(PDCSnsGroupType LenuSnsGroup);
void UpdateSnsOdoCoor(void);

#endif /* end of SnsRawData_Int_H */

