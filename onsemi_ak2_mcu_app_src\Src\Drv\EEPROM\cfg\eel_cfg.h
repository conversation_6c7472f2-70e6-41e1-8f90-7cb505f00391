/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: EEL_Cfg.h
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/
#ifndef __EEL_CFG_H
#define __EEL_CFG_H
#include "DTCMonitor.h"
/******************************************************************************/
/**
* @brief: 标定数据开关
**/
/******************************************************************************/
#define CALIBRATION_EN                STD_OFF//仅用于研发标定；正式软件该宏不会使能
/* 存储数据的长度 */
/* Section_1 */
/* Section_2 */ 
/* Section_3 */
#define ONE_DTC_STORGE_LEN                            (37u)

/* 数据相对地址 */
/* Section_1 boot能读,app能读写 */
#define EEL_START_CHECK_ADDRESS                     0u    /**< EEL库检测状态的虚拟地址 *//*是否第一次上电*/
#define EEL_START_CHECK_STATE_LEN                   4u    /**< EEL库检测状态的数据长度 */

#define DID_F195_ADDRESS                        (EEL_START_CHECK_ADDRESS+EEL_START_CHECK_STATE_LEN) /**< APP软件版本的虚拟地址 */
#define DID_F195_EEPROM_LEN                     (2u)/**< APP软件版本的数据长度 */

#define DID_F187_ADDRESS                        (DID_F195_ADDRESS+DID_F195_EEPROM_LEN) /**< 汽车生产单元号零件号的虚拟地址 */
#define DID_F187_EEPROM_LEN                     (15u)/**< 汽车生产单元号零件号的数据长度 */

#define DID_F18A_ADDRESS                        (DID_F187_ADDRESS+DID_F187_EEPROM_LEN) /**< 系统供应商标识号的虚拟地址 */
#define DID_F18A_EEPROM_LEN                     (10u)/**< 系统供应商标识号的数据长度 */

#define DID_F026_ADDRESS                        (DID_F18A_ADDRESS+DID_F18A_EEPROM_LEN) /**< 系统供应商软件ID的虚拟地址 */
#define DID_F026_EEPROM_LEN                     (2u)/**< 系统供应商软件ID的数据长度 */

#define DID_F197_ADDRESS                        (DID_F026_ADDRESS+DID_F026_EEPROM_LEN) /**< 系统供应商软件ID的虚拟地址 */
#define DID_F197_EEPROM_LEN                     (10u)/**< 系统供应商软件ID的数据长度 */

/* Section 2 boot能读写,app都读写即可 */
#define APP_JUMPBOOT_ADDRESS                    (DID_F197_ADDRESS+DID_F197_EEPROM_LEN)             /**< 升级标志的虚拟地址 */
#define APP_JUMPBOOT_LEN                        (4u)    /**< 升级标志的数据长度 */

#define ERRORKEYCNT_ADDRESS                     (APP_JUMPBOOT_ADDRESS+APP_JUMPBOOT_LEN)             /**< 安全访问错误计数的虚拟地址 */
#define ERRORKEYCNT_EEPROM_LEN                  (1u)   /**< 安全访问错误计数的数据长度 */
/* Section 3 boot能读写,app只能读 */
#define DID_F14A_ADDRESS                        (ERRORKEYCNT_ADDRESS+ERRORKEYCNT_EEPROM_LEN)     /**< Boot版本号的虚拟地址 */
#define DID_F14A_EEPROM_LEN                     (2u)   /**< Boot版本号的数据长度 */

#define DID_F193_ADDRESS                        (DID_F14A_ADDRESS+DID_F14A_EEPROM_LEN)     /**< 硬件版本号的虚拟地址 */
#define DID_F193_EEPROM_LEN                     (2u)   /**< 硬件版本号的数据长度 */

#define DID_F15A_ADDRESS                        (DID_F193_ADDRESS+DID_F193_EEPROM_LEN )     /**< 指纹的虚拟地址 */
#define DID_F15A_EEPROM_LEN                     (21u)/**< 指纹的数据长度 */

#define DID_F021_ADDRESS                        (DID_F15A_ADDRESS+DID_F15A_EEPROM_LEN)     /**< 编程尝试计数器的虚拟地址 */
#define DID_F021_EEPROM_LEN                     (2u)/**< 编程尝试计数器的数据长度 */

#define DID_F022_ADDRESS                        (DID_F021_ADDRESS+DID_F021_EEPROM_LEN)     /**< 编程成功计数器的虚拟地址 */
#define DID_F022_EEPROM_LEN                     (2u)/**< 编程成功计数器的数据长度 */
#define SBL_STATE_ADDRESS                       (DID_F022_ADDRESS+DID_F022_EEPROM_LEN)     /**< SBL状态地址 */
#define SBL_STATE_LEN                           (4u)

#define SBL_BAK_STATE_ADDRESS                   (SBL_STATE_ADDRESS+SBL_STATE_LEN)           /**< SBL备份状态地址 */
#define SBL_BAK_STATE_LEN                       (4u)

#define SBL_UPDATE_STATE_ADDRESS                (SBL_BAK_STATE_ADDRESS+SBL_BAK_STATE_LEN)  /**< SBL更新状态地址 */
#define SBL_UPDATE_STATE_LEN                    (4u)

#define ACTIVE_NEW_SBL_ADDRESS                  (SBL_UPDATE_STATE_ADDRESS+SBL_UPDATE_STATE_LEN )/*SBL激活指令*/
#define ACTIVE_NEW_SBL_LEN                      (4u)

/* Section 4 boot不能读写，app能读写 */
#define DID_F190_ADDRESS                        (ACTIVE_NEW_SBL_ADDRESS+ACTIVE_NEW_SBL_LEN) /*Vin码的虚拟地址*/
#define DID_F190_EEPROM_LEN                     (17u)/**< Vin码的数据长度 */

#define DID_F1A0_ADDRESS                        (DID_F190_ADDRESS+DID_F190_EEPROM_LEN) /*车辆制造日期的虚拟地址*/
#define DID_F1A0_EEPROM_LEN                     (3u)/**< 车辆制造日期的数据长度 */

#define DID_F010_ADDRESS                        (DID_F1A0_ADDRESS+DID_F1A0_EEPROM_LEN) /* 车辆功能配置的虚拟地址 */
#define DID_F010_EEPROM_LEN                     (8u)/**< 车辆功能配置的数据长度 */

#define DID_F1A1_ADDRESS                        (DID_F010_ADDRESS+DID_F010_EEPROM_LEN)/**< 车型配置信息的虚拟地址 */
#define DID_F1A1_EEPROM_LEN                     (17u)/**< 车型配置信息的数据长度 */

#define DID_F18C_ADDRESS                        (DID_F1A1_ADDRESS+DID_F1A1_EEPROM_LEN)/**< 供应商生产流水号的虚拟地址 */
#define DID_F18C_EEPROM_LEN                     (16u)/**< 供应商生产流水号的数据长度 */

#define DID_F18B_ADDRESS                        (DID_F18C_ADDRESS+DID_F18C_EEPROM_LEN)/**< 供应商生产日期的虚拟地址 */
#define DID_F18B_EEPROM_LEN                     (4u)/**< 供应商生产日期的数据长度 */

/** @brief: 配置字校验 */
#define ECU_CFG_BYTE_CHECK_ADDRESS              (DID_F18B_ADDRESS+DID_F18B_EEPROM_LEN)/**< 配置字检测的虚拟地址 */
#define ECU_CFG_BYTE_CHECK_EEPROM_LEN           (4u)/**< 配置字检测的数据长度 */

/** @brief: 标定数据校验 */
#define CALIB_DATA_FLAG_ADDRESS                 (ECU_CFG_BYTE_CHECK_ADDRESS + ECU_CFG_BYTE_CHECK_EEPROM_LEN)/**< 标定数据检验标志的虚拟地址 */
#define CALIB_DATA_FLAG_EEPROM_LEN              (4u)/**< 标定数据检验标志的数据长度 */

#define DID_F1A1_BACKUP_ADDRESS                (CALIB_DATA_FLAG_ADDRESS + CALIB_DATA_FLAG_EEPROM_LEN)/**< 车型配置字备份的虚拟地址 */
#define DID_F1A1_BACKUP_EEPROM_LEN             (20u)
/** @brief: 配置字校验 */
#define ECU_F1A1_CHECK_BACKUP_ADDRESS           (DID_F1A1_BACKUP_ADDRESS + CALIB_DATA_FLAG_EEPROM_LEN)/**< 车型配置字备份的虚拟地址 */
#define ECU_F1A1_CHECK_BACKUP_EEPROM_LEN        (4u)/**< 车型配置字备份的数据长度 */

#define DTC_ODO_FLAG_ADDRESS                    (ECU_F1A1_CHECK_BACKUP_ADDRESS + ECU_F1A1_CHECK_BACKUP_EEPROM_LEN)/**< ODO故障检测的虚拟地址 */
#define DTC_ODO_FLAG_EEPROM_LEN                 (4U)/**< ODO孤战检测的数据长度 */



/* Section 5 ~86 boot不能读写，app能读写 用于存储DTC,一个DTC是一个section*/
#define DTC_RECORD_1_ADDRESS                    (DTC_ODO_FLAG_ADDRESS+DTC_ODO_FLAG_EEPROM_LEN)//DTC数据缓存
#define DTC_RECORD_ONESECTION_LEN               (1u*ONE_DTC_STORGE_LEN)//(TOTAL_DTC_RECORD_NUMBER82*ONE_DTC_STORGE_LEN)
/* Section 87 boot能读写，app不能读，app可读 *//*升级log存储长度*/
#define UPDATALOG_LEN                           (0x80u)



/* Section 6~82*/
#define      DTC_RECORD_2_ADDRESS                    (DTC_RECORD_1_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_3_ADDRESS                    (DTC_RECORD_2_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_4_ADDRESS                    (DTC_RECORD_3_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_5_ADDRESS                    (DTC_RECORD_4_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_6_ADDRESS                    (DTC_RECORD_5_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_7_ADDRESS                    (DTC_RECORD_6_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_8_ADDRESS                    (DTC_RECORD_7_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_9_ADDRESS                    (DTC_RECORD_8_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_10_ADDRESS                    (DTC_RECORD_9_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_11_ADDRESS                    (DTC_RECORD_10_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_12_ADDRESS                    (DTC_RECORD_11_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_13_ADDRESS                    (DTC_RECORD_12_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_14_ADDRESS                    (DTC_RECORD_13_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_15_ADDRESS                    (DTC_RECORD_14_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_16_ADDRESS                    (DTC_RECORD_15_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_17_ADDRESS                    (DTC_RECORD_16_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_18_ADDRESS                    (DTC_RECORD_17_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_19_ADDRESS                    (DTC_RECORD_18_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_20_ADDRESS                    (DTC_RECORD_19_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_21_ADDRESS                    (DTC_RECORD_20_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_22_ADDRESS                    (DTC_RECORD_21_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_23_ADDRESS                    (DTC_RECORD_22_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_24_ADDRESS                    (DTC_RECORD_23_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_25_ADDRESS                    (DTC_RECORD_24_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_26_ADDRESS                    (DTC_RECORD_25_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_27_ADDRESS                    (DTC_RECORD_26_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_28_ADDRESS                    (DTC_RECORD_27_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_29_ADDRESS                    (DTC_RECORD_28_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_30_ADDRESS                    (DTC_RECORD_29_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_31_ADDRESS                    (DTC_RECORD_30_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_32_ADDRESS                    (DTC_RECORD_31_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_33_ADDRESS                    (DTC_RECORD_32_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_34_ADDRESS                    (DTC_RECORD_33_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_35_ADDRESS                    (DTC_RECORD_34_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_36_ADDRESS                    (DTC_RECORD_35_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_37_ADDRESS                    (DTC_RECORD_36_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_38_ADDRESS                    (DTC_RECORD_37_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_39_ADDRESS                    (DTC_RECORD_38_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_40_ADDRESS                    (DTC_RECORD_39_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_41_ADDRESS                    (DTC_RECORD_40_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_42_ADDRESS                    (DTC_RECORD_41_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_43_ADDRESS                    (DTC_RECORD_42_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_44_ADDRESS                    (DTC_RECORD_43_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_45_ADDRESS                    (DTC_RECORD_44_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_46_ADDRESS                    (DTC_RECORD_45_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_47_ADDRESS                    (DTC_RECORD_46_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_48_ADDRESS                    (DTC_RECORD_47_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_49_ADDRESS                    (DTC_RECORD_48_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_50_ADDRESS                    (DTC_RECORD_49_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_51_ADDRESS                    (DTC_RECORD_50_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_52_ADDRESS                    (DTC_RECORD_51_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_53_ADDRESS                    (DTC_RECORD_52_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_54_ADDRESS                    (DTC_RECORD_53_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_55_ADDRESS                    (DTC_RECORD_54_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_56_ADDRESS                    (DTC_RECORD_55_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_57_ADDRESS                    (DTC_RECORD_56_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_58_ADDRESS                    (DTC_RECORD_57_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_59_ADDRESS                    (DTC_RECORD_58_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_60_ADDRESS                    (DTC_RECORD_59_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_61_ADDRESS                    (DTC_RECORD_60_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_62_ADDRESS                    (DTC_RECORD_61_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_63_ADDRESS                    (DTC_RECORD_62_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_64_ADDRESS                    (DTC_RECORD_63_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_65_ADDRESS                    (DTC_RECORD_64_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_66_ADDRESS                    (DTC_RECORD_65_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_67_ADDRESS                    (DTC_RECORD_66_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_68_ADDRESS                    (DTC_RECORD_67_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_69_ADDRESS                    (DTC_RECORD_68_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_70_ADDRESS                    (DTC_RECORD_69_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_71_ADDRESS                    (DTC_RECORD_70_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_72_ADDRESS                    (DTC_RECORD_71_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_73_ADDRESS                    (DTC_RECORD_72_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_74_ADDRESS                    (DTC_RECORD_73_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_75_ADDRESS                    (DTC_RECORD_74_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_76_ADDRESS                    (DTC_RECORD_75_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_77_ADDRESS                    (DTC_RECORD_76_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_78_ADDRESS                    (DTC_RECORD_77_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_79_ADDRESS                    (DTC_RECORD_78_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_80_ADDRESS                    (DTC_RECORD_79_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_81_ADDRESS                    (DTC_RECORD_80_ADDRESS  +DTC_RECORD_ONESECTION_LEN)//未使用
#define      DTC_RECORD_82_ADDRESS                    (DTC_RECORD_81_ADDRESS  +DTC_RECORD_ONESECTION_LEN)//未使用
#define      UPDATALOG_ADDRESS                        (DTC_RECORD_82_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
/*SECTION 88 boot不能读写，app能读写*/
/*标定数据存储*/
#define      CALIBRATION_1_ADDRESS                    (UPDATALOG_ADDRESS  + UPDATALOG_LEN)
#define      CALIBRATION_1_LEN                         (0x1u)

#define      DTC_RECORD_83_ADDRESS                    (CALIBRATION_1_ADDRESS  +CALIBRATION_1_LEN)
#define      DTC_RECORD_84_ADDRESS                    (DTC_RECORD_83_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DTC_RECORD_85_ADDRESS                    (DTC_RECORD_84_ADDRESS  +DTC_RECORD_ONESECTION_LEN)//end 112
//section 88,预留16字节,刚好用于ECU功能配置16个字节
#define      DID_F1B1_ADDRESS                         (DTC_RECORD_85_ADDRESS  +DTC_RECORD_ONESECTION_LEN)
#define      DID_F1B1_EEPROM_LEN                      (16U)
/*SECTION 89 boot不能读写，app能读写*/
#define      DTC_RECORD_86_ADDRESS                    (CALIBRATION_1_ADDRESS  + 0x80)

/*BLOCK 88 END*/
/*section 88还有127字节可使用*/
/**********************************************************************/

/***EEL目前使用总数量（byte）**************************************************************************************************/
#define EEPROM_DATA_END_ADDRESS                     (DTC_RECORD_86_ADDRESS+0x80 )    /**< EEL的虚拟结束地址 */
#define EEPROM_DATA_NUM                              EEPROM_DATA_END_ADDRESS     /**< EEL库总使用的数据长度 */

/*****************************************************************************************************/
/* 存储数据段 */
#define EEL_DATA_SECTION_1_START                    EEL_START_CHECK_ADDRESS     /**< EEL片段1的虚拟起始地址 */
#define EEL_DATA_SECTION_1_END                      APP_JUMPBOOT_ADDRESS    /**< EEL片段1的虚拟结束地址 */
#define EEL_DATA_SECTION_1_ID                       0x0001ul                    /**< EEL片段1的对应内容的片段1ID号 */
#define EEL_DATA_SECTION_1_LEN                      ((EEL_DATA_SECTION_1_END)-(EEL_DATA_SECTION_1_START))   /**< EEL片段1的数据长度 */

#define EEL_DATA_SECTION_2_START                    APP_JUMPBOOT_ADDRESS    /**< EEL片段2的虚拟起始地址 */
#define EEL_DATA_SECTION_2_END                      DID_F14A_ADDRESS        /**< EEL片段2的虚拟结束地址 */
#define EEL_DATA_SECTION_2_ID                       0x0002ul                    /**< EEL片段2的对应内容的片段2ID号 */
#define EEL_DATA_SECTION_2_LEN                      ((EEL_DATA_SECTION_2_END)-(EEL_DATA_SECTION_2_START))   /**< EEL片段2的数据长度 */

#define EEL_DATA_SECTION_3_START                    DID_F14A_ADDRESS        /**< EEL片段3的虚拟起始地址 */
#define EEL_DATA_SECTION_3_END                      DID_F190_ADDRESS     /**< EEL片段3的虚拟结束地址 */
#define EEL_DATA_SECTION_3_ID                       0x0003ul                    /**< EEL片段3的对应内容的片段3ID号 */
#define EEL_DATA_SECTION_3_LEN                      ((EEL_DATA_SECTION_3_END)-(EEL_DATA_SECTION_3_START))   /**< EEL片段3的数据长度 */

#define EEL_DATA_SECTION_4_START                    DID_F190_ADDRESS     /**< EEL片段4的虚拟起始地址 */ 
#define EEL_DATA_SECTION_4_END                      DTC_RECORD_1_ADDRESS        /**< EEL片段4的虚拟结束地址 */
#define EEL_DATA_SECTION_4_ID                       0x0004ul                    /**< EEL片段4的对应内容的片段4ID号 */
#define EEL_DATA_SECTION_4_LEN                      ((EEL_DATA_SECTION_4_END)-(EEL_DATA_SECTION_4_START))   /**< EEL片段4的数据长度 */

/****************************************EEL片段的虚拟起始地址********************************************************************/
#define EEL_DATA_SECTION_5_START                    DTC_RECORD_1_ADDRESS         /**< EEL片段5的虚拟起始地址 */
#define EEL_DATA_SECTION_6_START                    DTC_RECORD_2_ADDRESS         /**< EEL片段6的虚拟起始地址 */
#define EEL_DATA_SECTION_7_START                    DTC_RECORD_3_ADDRESS         /**< EEL片段7的虚拟起始地址 */
#define EEL_DATA_SECTION_8_START                    DTC_RECORD_4_ADDRESS         /**< EEL片段8的虚拟起始地址 */
#define EEL_DATA_SECTION_9_START                    DTC_RECORD_5_ADDRESS         /**< EEL片段9的虚拟起始地址 */
#define EEL_DATA_SECTION_10_START                    DTC_RECORD_6_ADDRESS         /**< EEL片段10的虚拟起始地址 */
#define EEL_DATA_SECTION_11_START                    DTC_RECORD_7_ADDRESS         /**< EEL片段11的虚拟起始地址 */
#define EEL_DATA_SECTION_12_START                    DTC_RECORD_8_ADDRESS         /**< EEL片段12的虚拟起始地址 */
#define EEL_DATA_SECTION_13_START                    DTC_RECORD_9_ADDRESS         /**< EEL片段13的虚拟起始地址 */
#define EEL_DATA_SECTION_14_START                    DTC_RECORD_10_ADDRESS         /**< EEL片段14的虚拟起始地址 */
#define EEL_DATA_SECTION_15_START                    DTC_RECORD_11_ADDRESS         /**< EEL片段15的虚拟起始地址 */
#define EEL_DATA_SECTION_16_START                    DTC_RECORD_12_ADDRESS         /**< EEL片段16的虚拟起始地址 */
#define EEL_DATA_SECTION_17_START                    DTC_RECORD_13_ADDRESS         /**< EEL片段17的虚拟起始地址 */
#define EEL_DATA_SECTION_18_START                    DTC_RECORD_14_ADDRESS         /**< EEL片段18的虚拟起始地址 */
#define EEL_DATA_SECTION_19_START                    DTC_RECORD_15_ADDRESS         /**< EEL片段19的虚拟起始地址 */
#define EEL_DATA_SECTION_20_START                    DTC_RECORD_16_ADDRESS         /**< EEL片段20的虚拟起始地址 */
#define EEL_DATA_SECTION_21_START                    DTC_RECORD_17_ADDRESS         /**< EEL片段21的虚拟起始地址 */
#define EEL_DATA_SECTION_22_START                    DTC_RECORD_18_ADDRESS         /**< EEL片段22的虚拟起始地址 */
#define EEL_DATA_SECTION_23_START                    DTC_RECORD_19_ADDRESS         /**< EEL片段23的虚拟起始地址 */
#define EEL_DATA_SECTION_24_START                    DTC_RECORD_20_ADDRESS         /**< EEL片段24的虚拟起始地址 */
#define EEL_DATA_SECTION_25_START                    DTC_RECORD_21_ADDRESS         /**< EEL片段25的虚拟起始地址 */
#define EEL_DATA_SECTION_26_START                    DTC_RECORD_22_ADDRESS         /**< EEL片段26的虚拟起始地址 */
#define EEL_DATA_SECTION_27_START                    DTC_RECORD_23_ADDRESS         /**< EEL片段27的虚拟起始地址 */
#define EEL_DATA_SECTION_28_START                    DTC_RECORD_24_ADDRESS         /**< EEL片段28的虚拟起始地址 */
#define EEL_DATA_SECTION_29_START                    DTC_RECORD_25_ADDRESS         /**< EEL片段29的虚拟起始地址 */
#define EEL_DATA_SECTION_30_START                    DTC_RECORD_26_ADDRESS         /**< EEL片段30的虚拟起始地址 */
#define EEL_DATA_SECTION_31_START                    DTC_RECORD_27_ADDRESS         /**< EEL片段31的虚拟起始地址 */
#define EEL_DATA_SECTION_32_START                    DTC_RECORD_28_ADDRESS         /**< EEL片段32的虚拟起始地址 */
#define EEL_DATA_SECTION_33_START                    DTC_RECORD_29_ADDRESS         /**< EEL片段33的虚拟起始地址 */
#define EEL_DATA_SECTION_34_START                    DTC_RECORD_30_ADDRESS         /**< EEL片段34的虚拟起始地址 */
#define EEL_DATA_SECTION_35_START                    DTC_RECORD_31_ADDRESS         /**< EEL片段35的虚拟起始地址 */
#define EEL_DATA_SECTION_36_START                    DTC_RECORD_32_ADDRESS         /**< EEL片段36的虚拟起始地址 */
#define EEL_DATA_SECTION_37_START                    DTC_RECORD_33_ADDRESS         /**< EEL片段37的虚拟起始地址 */
#define EEL_DATA_SECTION_38_START                    DTC_RECORD_34_ADDRESS         /**< EEL片段38的虚拟起始地址 */
#define EEL_DATA_SECTION_39_START                    DTC_RECORD_35_ADDRESS         /**< EEL片段39的虚拟起始地址 */
#define EEL_DATA_SECTION_40_START                    DTC_RECORD_36_ADDRESS         /**< EEL片段40的虚拟起始地址 */
#define EEL_DATA_SECTION_41_START                    DTC_RECORD_37_ADDRESS         /**< EEL片段41的虚拟起始地址 */
#define EEL_DATA_SECTION_42_START                    DTC_RECORD_38_ADDRESS         /**< EEL片段42的虚拟起始地址 */
#define EEL_DATA_SECTION_43_START                    DTC_RECORD_39_ADDRESS         /**< EEL片段43的虚拟起始地址 */
#define EEL_DATA_SECTION_44_START                    DTC_RECORD_40_ADDRESS         /**< EEL片段44的虚拟起始地址 */
#define EEL_DATA_SECTION_45_START                    DTC_RECORD_41_ADDRESS         /**< EEL片段45的虚拟起始地址 */
#define EEL_DATA_SECTION_46_START                    DTC_RECORD_42_ADDRESS         /**< EEL片段46的虚拟起始地址 */
#define EEL_DATA_SECTION_47_START                    DTC_RECORD_43_ADDRESS         /**< EEL片段47的虚拟起始地址 */
#define EEL_DATA_SECTION_48_START                    DTC_RECORD_44_ADDRESS         /**< EEL片段48的虚拟起始地址 */
#define EEL_DATA_SECTION_49_START                    DTC_RECORD_45_ADDRESS         /**< EEL片段49的虚拟起始地址 */
#define EEL_DATA_SECTION_50_START                    DTC_RECORD_46_ADDRESS         /**< EEL片段50的虚拟起始地址 */
#define EEL_DATA_SECTION_51_START                    DTC_RECORD_47_ADDRESS         /**< EEL片段51的虚拟起始地址 */
#define EEL_DATA_SECTION_52_START                    DTC_RECORD_48_ADDRESS         /**< EEL片段52的虚拟起始地址 */
#define EEL_DATA_SECTION_53_START                    DTC_RECORD_49_ADDRESS         /**< EEL片段53的虚拟起始地址 */
#define EEL_DATA_SECTION_54_START                    DTC_RECORD_50_ADDRESS         /**< EEL片段54的虚拟起始地址 */
#define EEL_DATA_SECTION_55_START                    DTC_RECORD_51_ADDRESS         /**< EEL片段55的虚拟起始地址 */
#define EEL_DATA_SECTION_56_START                    DTC_RECORD_52_ADDRESS         /**< EEL片段56的虚拟起始地址 */
#define EEL_DATA_SECTION_57_START                    DTC_RECORD_53_ADDRESS         /**< EEL片段57的虚拟起始地址 */
#define EEL_DATA_SECTION_58_START                    DTC_RECORD_54_ADDRESS         /**< EEL片段58的虚拟起始地址 */
#define EEL_DATA_SECTION_59_START                    DTC_RECORD_55_ADDRESS         /**< EEL片段59的虚拟起始地址 */
#define EEL_DATA_SECTION_60_START                    DTC_RECORD_56_ADDRESS         /**< EEL片段60的虚拟起始地址 */
#define EEL_DATA_SECTION_61_START                    DTC_RECORD_57_ADDRESS         /**< EEL片段61的虚拟起始地址 */
#define EEL_DATA_SECTION_62_START                    DTC_RECORD_58_ADDRESS         /**< EEL片段62的虚拟起始地址 */
#define EEL_DATA_SECTION_63_START                    DTC_RECORD_59_ADDRESS         /**< EEL片段63的虚拟起始地址 */
#define EEL_DATA_SECTION_64_START                    DTC_RECORD_60_ADDRESS         /**< EEL片段64的虚拟起始地址 */
#define EEL_DATA_SECTION_65_START                    DTC_RECORD_61_ADDRESS         /**< EEL片段65的虚拟起始地址 */
#define EEL_DATA_SECTION_66_START                    DTC_RECORD_62_ADDRESS         /**< EEL片段66的虚拟起始地址 */
#define EEL_DATA_SECTION_67_START                    DTC_RECORD_63_ADDRESS         /**< EEL片段67的虚拟起始地址 */
#define EEL_DATA_SECTION_68_START                    DTC_RECORD_64_ADDRESS         /**< EEL片段68的虚拟起始地址 */
#define EEL_DATA_SECTION_69_START                    DTC_RECORD_65_ADDRESS         /**< EEL片段69的虚拟起始地址 */
#define EEL_DATA_SECTION_70_START                    DTC_RECORD_66_ADDRESS         /**< EEL片段70的虚拟起始地址 */
#define EEL_DATA_SECTION_71_START                    DTC_RECORD_67_ADDRESS         /**< EEL片段71的虚拟起始地址 */
#define EEL_DATA_SECTION_72_START                    DTC_RECORD_68_ADDRESS         /**< EEL片段72的虚拟起始地址 */
#define EEL_DATA_SECTION_73_START                    DTC_RECORD_69_ADDRESS         /**< EEL片段73的虚拟起始地址 */
#define EEL_DATA_SECTION_74_START                    DTC_RECORD_70_ADDRESS         /**< EEL片段74的虚拟起始地址 */
#define EEL_DATA_SECTION_75_START                    DTC_RECORD_71_ADDRESS         /**< EEL片段75的虚拟起始地址 */
#define EEL_DATA_SECTION_76_START                    DTC_RECORD_72_ADDRESS         /**< EEL片段76的虚拟起始地址 */
#define EEL_DATA_SECTION_77_START                    DTC_RECORD_73_ADDRESS         /**< EEL片段77的虚拟起始地址 */
#define EEL_DATA_SECTION_78_START                    DTC_RECORD_74_ADDRESS         /**< EEL片段78的虚拟起始地址 */
#define EEL_DATA_SECTION_79_START                    DTC_RECORD_75_ADDRESS         /**< EEL片段79的虚拟起始地址 */
#define EEL_DATA_SECTION_80_START                    DTC_RECORD_76_ADDRESS         /**< EEL片段80的虚拟起始地址 */
#define EEL_DATA_SECTION_81_START                    DTC_RECORD_77_ADDRESS         /**< EEL片段81的虚拟起始地址 */
#define EEL_DATA_SECTION_82_START                    DTC_RECORD_78_ADDRESS         /**< EEL片段82的虚拟起始地址 */
#define EEL_DATA_SECTION_83_START                    DTC_RECORD_79_ADDRESS         /**< EEL片段83的虚拟起始地址 */
#define EEL_DATA_SECTION_84_START                    DTC_RECORD_80_ADDRESS         /**< EEL片段84的虚拟起始地址 */
#define EEL_DATA_SECTION_85_START                    DTC_RECORD_81_ADDRESS         /**< EEL片段85的虚拟起始地址 */
#define EEL_DATA_SECTION_86_START                    DTC_RECORD_82_ADDRESS         /**< EEL片段86的虚拟起始地址 */
#define EEL_DATA_SECTION_87_START                    UPDATALOG_ADDRESS             /**< EEL片段87的虚拟起始地址 */
#define EEL_DATA_SECTION_88_START                    CALIBRATION_1_ADDRESS             /**< EEL片段88的虚拟起始地址 */

#define EEL_DATA_SECTION_89_START                    DTC_RECORD_86_ADDRESS             /**< EEL片段88的虚拟起始地址 */

#define EEL_DATA_SECTION_5_END                      DTC_RECORD_2_ADDRESS      /**< EEL片段5的虚拟结束地址 */
#define EEL_DATA_SECTION_6_END                      DTC_RECORD_3_ADDRESS      /**< EEL片段6的虚拟结束地址 */
#define EEL_DATA_SECTION_7_END                      DTC_RECORD_4_ADDRESS      /**< EEL片段7的虚拟结束地址 */
#define EEL_DATA_SECTION_8_END                      DTC_RECORD_5_ADDRESS      /**< EEL片段8的虚拟结束地址 */
#define EEL_DATA_SECTION_9_END                      DTC_RECORD_6_ADDRESS      /**< EEL片段9的虚拟结束地址 */
#define EEL_DATA_SECTION_10_END                      DTC_RECORD_7_ADDRESS      /**< EEL片段10的虚拟结束地址 */
#define EEL_DATA_SECTION_11_END                      DTC_RECORD_8_ADDRESS      /**< EEL片段11的虚拟结束地址 */
#define EEL_DATA_SECTION_12_END                      DTC_RECORD_9_ADDRESS      /**< EEL片段12的虚拟结束地址 */
#define EEL_DATA_SECTION_13_END                      DTC_RECORD_10_ADDRESS      /**< EEL片段13的虚拟结束地址 */
#define EEL_DATA_SECTION_14_END                      DTC_RECORD_11_ADDRESS      /**< EEL片段14的虚拟结束地址 */
#define EEL_DATA_SECTION_15_END                      DTC_RECORD_12_ADDRESS      /**< EEL片段15的虚拟结束地址 */
#define EEL_DATA_SECTION_16_END                      DTC_RECORD_13_ADDRESS      /**< EEL片段16的虚拟结束地址 */
#define EEL_DATA_SECTION_17_END                      DTC_RECORD_14_ADDRESS      /**< EEL片段17的虚拟结束地址 */
#define EEL_DATA_SECTION_18_END                      DTC_RECORD_15_ADDRESS      /**< EEL片段18的虚拟结束地址 */
#define EEL_DATA_SECTION_19_END                      DTC_RECORD_16_ADDRESS      /**< EEL片段19的虚拟结束地址 */
#define EEL_DATA_SECTION_20_END                      DTC_RECORD_17_ADDRESS      /**< EEL片段20的虚拟结束地址 */
#define EEL_DATA_SECTION_21_END                      DTC_RECORD_18_ADDRESS      /**< EEL片段21的虚拟结束地址 */
#define EEL_DATA_SECTION_22_END                      DTC_RECORD_19_ADDRESS      /**< EEL片段22的虚拟结束地址 */
#define EEL_DATA_SECTION_23_END                      DTC_RECORD_20_ADDRESS      /**< EEL片段23的虚拟结束地址 */
#define EEL_DATA_SECTION_24_END                      DTC_RECORD_21_ADDRESS      /**< EEL片段24的虚拟结束地址 */
#define EEL_DATA_SECTION_25_END                      DTC_RECORD_22_ADDRESS      /**< EEL片段25的虚拟结束地址 */
#define EEL_DATA_SECTION_26_END                      DTC_RECORD_23_ADDRESS      /**< EEL片段26的虚拟结束地址 */
#define EEL_DATA_SECTION_27_END                      DTC_RECORD_24_ADDRESS      /**< EEL片段27的虚拟结束地址 */
#define EEL_DATA_SECTION_28_END                      DTC_RECORD_25_ADDRESS      /**< EEL片段28的虚拟结束地址 */
#define EEL_DATA_SECTION_29_END                      DTC_RECORD_26_ADDRESS      /**< EEL片段29的虚拟结束地址 */
#define EEL_DATA_SECTION_30_END                      DTC_RECORD_27_ADDRESS      /**< EEL片段30的虚拟结束地址 */
#define EEL_DATA_SECTION_31_END                      DTC_RECORD_28_ADDRESS      /**< EEL片段31的虚拟结束地址 */
#define EEL_DATA_SECTION_32_END                      DTC_RECORD_29_ADDRESS      /**< EEL片段32的虚拟结束地址 */
#define EEL_DATA_SECTION_33_END                      DTC_RECORD_30_ADDRESS      /**< EEL片段33的虚拟结束地址 */
#define EEL_DATA_SECTION_34_END                      DTC_RECORD_31_ADDRESS      /**< EEL片段34的虚拟结束地址 */
#define EEL_DATA_SECTION_35_END                      DTC_RECORD_32_ADDRESS      /**< EEL片段35的虚拟结束地址 */
#define EEL_DATA_SECTION_36_END                      DTC_RECORD_33_ADDRESS      /**< EEL片段36的虚拟结束地址 */
#define EEL_DATA_SECTION_37_END                      DTC_RECORD_34_ADDRESS      /**< EEL片段37的虚拟结束地址 */
#define EEL_DATA_SECTION_38_END                      DTC_RECORD_35_ADDRESS      /**< EEL片段38的虚拟结束地址 */
#define EEL_DATA_SECTION_39_END                      DTC_RECORD_36_ADDRESS      /**< EEL片段39的虚拟结束地址 */
#define EEL_DATA_SECTION_40_END                      DTC_RECORD_37_ADDRESS      /**< EEL片段40的虚拟结束地址 */
#define EEL_DATA_SECTION_41_END                      DTC_RECORD_38_ADDRESS      /**< EEL片段41的虚拟结束地址 */
#define EEL_DATA_SECTION_42_END                      DTC_RECORD_39_ADDRESS      /**< EEL片段42的虚拟结束地址 */
#define EEL_DATA_SECTION_43_END                      DTC_RECORD_40_ADDRESS      /**< EEL片段43的虚拟结束地址 */
#define EEL_DATA_SECTION_44_END                      DTC_RECORD_41_ADDRESS      /**< EEL片段44的虚拟结束地址 */
#define EEL_DATA_SECTION_45_END                      DTC_RECORD_42_ADDRESS      /**< EEL片段45的虚拟结束地址 */
#define EEL_DATA_SECTION_46_END                      DTC_RECORD_43_ADDRESS      /**< EEL片段46的虚拟结束地址 */
#define EEL_DATA_SECTION_47_END                      DTC_RECORD_44_ADDRESS      /**< EEL片段47的虚拟结束地址 */
#define EEL_DATA_SECTION_48_END                      DTC_RECORD_45_ADDRESS      /**< EEL片段48的虚拟结束地址 */
#define EEL_DATA_SECTION_49_END                      DTC_RECORD_46_ADDRESS      /**< EEL片段49的虚拟结束地址 */
#define EEL_DATA_SECTION_50_END                      DTC_RECORD_47_ADDRESS      /**< EEL片段50的虚拟结束地址 */
#define EEL_DATA_SECTION_51_END                      DTC_RECORD_48_ADDRESS      /**< EEL片段51的虚拟结束地址 */
#define EEL_DATA_SECTION_52_END                      DTC_RECORD_49_ADDRESS      /**< EEL片段52的虚拟结束地址 */
#define EEL_DATA_SECTION_53_END                      DTC_RECORD_50_ADDRESS      /**< EEL片段53的虚拟结束地址 */
#define EEL_DATA_SECTION_54_END                      DTC_RECORD_51_ADDRESS      /**< EEL片段54的虚拟结束地址 */
#define EEL_DATA_SECTION_55_END                      DTC_RECORD_52_ADDRESS      /**< EEL片段55的虚拟结束地址 */
#define EEL_DATA_SECTION_56_END                      DTC_RECORD_53_ADDRESS      /**< EEL片段56的虚拟结束地址 */
#define EEL_DATA_SECTION_57_END                      DTC_RECORD_54_ADDRESS      /**< EEL片段57的虚拟结束地址 */
#define EEL_DATA_SECTION_58_END                      DTC_RECORD_55_ADDRESS      /**< EEL片段58的虚拟结束地址 */
#define EEL_DATA_SECTION_59_END                      DTC_RECORD_56_ADDRESS      /**< EEL片段59的虚拟结束地址 */
#define EEL_DATA_SECTION_60_END                      DTC_RECORD_57_ADDRESS      /**< EEL片段60的虚拟结束地址 */
#define EEL_DATA_SECTION_61_END                      DTC_RECORD_58_ADDRESS      /**< EEL片段61的虚拟结束地址 */
#define EEL_DATA_SECTION_62_END                      DTC_RECORD_59_ADDRESS      /**< EEL片段62的虚拟结束地址 */
#define EEL_DATA_SECTION_63_END                      DTC_RECORD_60_ADDRESS      /**< EEL片段63的虚拟结束地址 */
#define EEL_DATA_SECTION_64_END                      DTC_RECORD_61_ADDRESS      /**< EEL片段64的虚拟结束地址 */
#define EEL_DATA_SECTION_65_END                      DTC_RECORD_62_ADDRESS      /**< EEL片段65的虚拟结束地址 */
#define EEL_DATA_SECTION_66_END                      DTC_RECORD_63_ADDRESS      /**< EEL片段66的虚拟结束地址 */
#define EEL_DATA_SECTION_67_END                      DTC_RECORD_64_ADDRESS      /**< EEL片段67的虚拟结束地址 */
#define EEL_DATA_SECTION_68_END                      DTC_RECORD_65_ADDRESS      /**< EEL片段68的虚拟结束地址 */
#define EEL_DATA_SECTION_69_END                      DTC_RECORD_66_ADDRESS      /**< EEL片段69的虚拟结束地址 */
#define EEL_DATA_SECTION_70_END                      DTC_RECORD_67_ADDRESS      /**< EEL片段70的虚拟结束地址 */
#define EEL_DATA_SECTION_71_END                      DTC_RECORD_68_ADDRESS      /**< EEL片段71的虚拟结束地址 */
#define EEL_DATA_SECTION_72_END                      DTC_RECORD_69_ADDRESS      /**< EEL片段72的虚拟结束地址 */
#define EEL_DATA_SECTION_73_END                      DTC_RECORD_70_ADDRESS      /**< EEL片段73的虚拟结束地址 */
#define EEL_DATA_SECTION_74_END                      DTC_RECORD_71_ADDRESS      /**< EEL片段74的虚拟结束地址 */
#define EEL_DATA_SECTION_75_END                      DTC_RECORD_72_ADDRESS      /**< EEL片段75的虚拟结束地址 */
#define EEL_DATA_SECTION_76_END                      DTC_RECORD_73_ADDRESS      /**< EEL片段76的虚拟结束地址 */
#define EEL_DATA_SECTION_77_END                      DTC_RECORD_74_ADDRESS      /**< EEL片段77的虚拟结束地址 */
#define EEL_DATA_SECTION_78_END                      DTC_RECORD_75_ADDRESS      /**< EEL片段78的虚拟结束地址 */
#define EEL_DATA_SECTION_79_END                      DTC_RECORD_76_ADDRESS      /**< EEL片段79的虚拟结束地址 */
#define EEL_DATA_SECTION_80_END                      DTC_RECORD_77_ADDRESS      /**< EEL片段80的虚拟结束地址 */
#define EEL_DATA_SECTION_81_END                      DTC_RECORD_78_ADDRESS      /**< EEL片段81的虚拟结束地址 */
#define EEL_DATA_SECTION_82_END                      DTC_RECORD_79_ADDRESS      /**< EEL片段82的虚拟结束地址 */
#define EEL_DATA_SECTION_83_END                      DTC_RECORD_80_ADDRESS      /**< EEL片段83的虚拟结束地址 */
#define EEL_DATA_SECTION_84_END                      DTC_RECORD_81_ADDRESS      /**< EEL片段84的虚拟结束地址 */
#define EEL_DATA_SECTION_85_END                      DTC_RECORD_82_ADDRESS      /**< EEL片段85的虚拟结束地址 */
#define EEL_DATA_SECTION_86_END                      UPDATALOG_ADDRESS     /**< EEL片段86的虚拟结束地址 */
#define EEL_DATA_SECTION_87_END                      CALIBRATION_1_ADDRESS    /**< EEL片段87的虚拟结束地址 */
#define EEL_DATA_SECTION_88_END                      DTC_RECORD_86_ADDRESS    /**< EEL片段88的虚拟结束地址 */
#define EEL_DATA_SECTION_89_END                      EEPROM_DATA_END_ADDRESS    /**< EEL片段88的虚拟结束地址 */

#define EEL_DATA_SECTION_5_ID                       0x0005ul                    /**< EEL片段5的对应内容的片段ID号*/
#define EEL_DATA_SECTION_6_ID                       0x0006ul                    /**< EEL片段6的对应内容的片段ID号*/
#define EEL_DATA_SECTION_7_ID                       0x0007ul                    /**< EEL片段7的对应内容的片段ID号*/
#define EEL_DATA_SECTION_8_ID                       0x0008ul                    /**< EEL片段8的对应内容的片段ID号*/
#define EEL_DATA_SECTION_9_ID                       0x0009ul                    /**< EEL片段9的对应内容的片段ID号*/
#define EEL_DATA_SECTION_10_ID                       0x000Aul                    /**< EEL片段10的对应内容的片段ID号*/
#define EEL_DATA_SECTION_11_ID                       0x000Bul                    /**< EEL片段11的对应内容的片段ID号*/
#define EEL_DATA_SECTION_12_ID                       0x000Cul                    /**< EEL片段12的对应内容的片段ID号*/
#define EEL_DATA_SECTION_13_ID                       0x000Dul                    /**< EEL片段13的对应内容的片段ID号*/
#define EEL_DATA_SECTION_14_ID                       0x000Eul                    /**< EEL片段14的对应内容的片段ID号*/
#define EEL_DATA_SECTION_15_ID                       0x000Ful                    /**< EEL片段15的对应内容的片段ID号*/
#define EEL_DATA_SECTION_16_ID                       0x0010ul                    /**< EEL片段16的对应内容的片段ID号*/
#define EEL_DATA_SECTION_17_ID                       0x0011ul                    /**< EEL片段17的对应内容的片段ID号*/
#define EEL_DATA_SECTION_18_ID                       0x0012ul                    /**< EEL片段18的对应内容的片段ID号*/
#define EEL_DATA_SECTION_19_ID                       0x0013ul                    /**< EEL片段19的对应内容的片段ID号*/
#define EEL_DATA_SECTION_20_ID                       0x0014ul                    /**< EEL片段20的对应内容的片段ID号*/
#define EEL_DATA_SECTION_21_ID                       0x0015ul                    /**< EEL片段21的对应内容的片段ID号*/
#define EEL_DATA_SECTION_22_ID                       0x0016ul                    /**< EEL片段22的对应内容的片段ID号*/
#define EEL_DATA_SECTION_23_ID                       0x0017ul                    /**< EEL片段23的对应内容的片段ID号*/
#define EEL_DATA_SECTION_24_ID                       0x0018ul                    /**< EEL片段24的对应内容的片段ID号*/
#define EEL_DATA_SECTION_25_ID                       0x0019ul                    /**< EEL片段25的对应内容的片段ID号*/
#define EEL_DATA_SECTION_26_ID                       0x001Aul                    /**< EEL片段26的对应内容的片段ID号*/
#define EEL_DATA_SECTION_27_ID                       0x001Bul                    /**< EEL片段27的对应内容的片段ID号*/
#define EEL_DATA_SECTION_28_ID                       0x001Cul                    /**< EEL片段28的对应内容的片段ID号*/
#define EEL_DATA_SECTION_29_ID                       0x001Dul                    /**< EEL片段29的对应内容的片段ID号*/
#define EEL_DATA_SECTION_30_ID                       0x001Eul                    /**< EEL片段30的对应内容的片段ID号*/
#define EEL_DATA_SECTION_31_ID                       0x001Ful                    /**< EEL片段31的对应内容的片段ID号*/
#define EEL_DATA_SECTION_32_ID                       0x0020ul                    /**< EEL片段32的对应内容的片段ID号*/
#define EEL_DATA_SECTION_33_ID                       0x0021ul                    /**< EEL片段33的对应内容的片段ID号*/
#define EEL_DATA_SECTION_34_ID                       0x0022ul                    /**< EEL片段34的对应内容的片段ID号*/
#define EEL_DATA_SECTION_35_ID                       0x0023ul                    /**< EEL片段35的对应内容的片段ID号*/
#define EEL_DATA_SECTION_36_ID                       0x0024ul                    /**< EEL片段36的对应内容的片段ID号*/
#define EEL_DATA_SECTION_37_ID                       0x25ul                    /**< EEL片段37的对应内容的片段ID号*/
#define EEL_DATA_SECTION_38_ID                       0x26ul                    /**< EEL片段38的对应内容的片段ID号*/
#define EEL_DATA_SECTION_39_ID                       0x27ul                    /**< EEL片段39的对应内容的片段ID号*/
#define EEL_DATA_SECTION_40_ID                       0x28ul                    /**< EEL片段40的对应内容的片段ID号*/
#define EEL_DATA_SECTION_41_ID                       0x29ul                    /**< EEL片段41的对应内容的片段ID号*/
#define EEL_DATA_SECTION_42_ID                       0x2Aul                    /**< EEL片段42的对应内容的片段ID号*/
#define EEL_DATA_SECTION_43_ID                       0x2Bul                    /**< EEL片段43的对应内容的片段ID号*/
#define EEL_DATA_SECTION_44_ID                       0x2Cul                    /**< EEL片段44的对应内容的片段ID号*/
#define EEL_DATA_SECTION_45_ID                       0x2Dul                    /**< EEL片段45的对应内容的片段ID号*/
#define EEL_DATA_SECTION_46_ID                       0x2Eul                    /**< EEL片段46的对应内容的片段ID号*/
#define EEL_DATA_SECTION_47_ID                       0x2Ful                    /**< EEL片段47的对应内容的片段ID号*/
#define EEL_DATA_SECTION_48_ID                       0x30ul                    /**< EEL片段48的对应内容的片段ID号*/
#define EEL_DATA_SECTION_49_ID                       0x31ul                    /**< EEL片段49的对应内容的片段ID号*/
#define EEL_DATA_SECTION_50_ID                       0x32ul                    /**< EEL片段50的对应内容的片段ID号*/
#define EEL_DATA_SECTION_51_ID                       0x33ul                    /**< EEL片段51的对应内容的片段ID号*/
#define EEL_DATA_SECTION_52_ID                       0x34ul                    /**< EEL片段52的对应内容的片段ID号*/
#define EEL_DATA_SECTION_53_ID                       0x35ul                    /**< EEL片段53的对应内容的片段ID号*/
#define EEL_DATA_SECTION_54_ID                       0x36ul                    /**< EEL片段54的对应内容的片段ID号*/
#define EEL_DATA_SECTION_55_ID                       0x37ul                    /**< EEL片段55的对应内容的片段ID号*/
#define EEL_DATA_SECTION_56_ID                       0x38ul                    /**< EEL片段56的对应内容的片段ID号*/
#define EEL_DATA_SECTION_57_ID                       0x39ul                    /**< EEL片段57的对应内容的片段ID号*/
#define EEL_DATA_SECTION_58_ID                       0x3Aul                    /**< EEL片段58的对应内容的片段ID号*/
#define EEL_DATA_SECTION_59_ID                       0x3Bul                    /**< EEL片段59的对应内容的片段ID号*/
#define EEL_DATA_SECTION_60_ID                       0x3Cul                    /**< EEL片段60的对应内容的片段ID号*/
#define EEL_DATA_SECTION_61_ID                       0x3Dul                    /**< EEL片段61的对应内容的片段ID号*/
#define EEL_DATA_SECTION_62_ID                       0x3Eul                    /**< EEL片段62的对应内容的片段ID号*/
#define EEL_DATA_SECTION_63_ID                       0x3Ful                    /**< EEL片段63的对应内容的片段ID号*/
#define EEL_DATA_SECTION_64_ID                       0x40ul                    /**< EEL片段64的对应内容的片段ID号*/
#define EEL_DATA_SECTION_65_ID                       0x41ul                    /**< EEL片段65的对应内容的片段ID号*/
#define EEL_DATA_SECTION_66_ID                       0x42ul                    /**< EEL片段66的对应内容的片段ID号*/
#define EEL_DATA_SECTION_67_ID                       0x43ul                    /**< EEL片段67的对应内容的片段ID号*/
#define EEL_DATA_SECTION_68_ID                       0x44ul                    /**< EEL片段68的对应内容的片段ID号*/
#define EEL_DATA_SECTION_69_ID                       0x45ul                    /**< EEL片段69的对应内容的片段ID号*/
#define EEL_DATA_SECTION_70_ID                       0x46ul                    /**< EEL片段70的对应内容的片段ID号*/
#define EEL_DATA_SECTION_71_ID                       0x47ul                    /**< EEL片段71的对应内容的片段ID号*/
#define EEL_DATA_SECTION_72_ID                       0x48ul                    /**< EEL片段72的对应内容的片段ID号*/
#define EEL_DATA_SECTION_73_ID                       0x49ul                    /**< EEL片段73的对应内容的片段ID号*/
#define EEL_DATA_SECTION_74_ID                       0x4Aul                    /**< EEL片段74的对应内容的片段ID号*/
#define EEL_DATA_SECTION_75_ID                       0x4Bul                    /**< EEL片段75的对应内容的片段ID号*/
#define EEL_DATA_SECTION_76_ID                       0x4Cul                    /**< EEL片段76的对应内容的片段ID号*/
#define EEL_DATA_SECTION_77_ID                       0x4Dul                    /**< EEL片段77的对应内容的片段ID号*/
#define EEL_DATA_SECTION_78_ID                       0x4Eul                    /**< EEL片段78的对应内容的片段ID号*/
#define EEL_DATA_SECTION_79_ID                       0x4Ful                    /**< EEL片段79的对应内容的片段ID号*/
#define EEL_DATA_SECTION_80_ID                       0x50ul                    /**< EEL片段80的对应内容的片段ID号*/
#define EEL_DATA_SECTION_81_ID                       0x51ul                    /**< EEL片段81的对应内容的片段ID号*/
#define EEL_DATA_SECTION_82_ID                       0x52ul                    /**< EEL片段82的对应内容的片段ID号*/
#define EEL_DATA_SECTION_83_ID                       0x53ul                    /**< EEL片段83的对应内容的片段ID号*/
#define EEL_DATA_SECTION_84_ID                       0x54ul                    /**< EEL片段84的对应内容的片段ID号*/
#define EEL_DATA_SECTION_85_ID                       0x55ul                    /**< EEL片段85的对应内容的片段ID号*/
#define EEL_DATA_SECTION_86_ID                       0x56ul                    /**< EEL片段86的对应内容的片段ID号*/
#define EEL_DATA_SECTION_87_ID                       0x57ul                    /**< EEL片段87的对应内容的片段ID号*/
#define EEL_DATA_SECTION_88_ID                       0x58ul                    /**< EEL片段88的对应内容的片段ID号*/
#define EEL_DATA_SECTION_89_ID                       0x59ul                    /**< EEL片段89的对应内容的片段ID号*/
/*************************预留****************预留*********************预留*******************预留*******************************************/
#define EEL_DATA_SECTION_90_ID                       0x5Aul                    /**< EEL片段90的对应内容的片段ID号*/
#define EEL_DATA_SECTION_91_ID                       0x5Bul                    /**< EEL片段91的对应内容的片段ID号*/
#define EEL_DATA_SECTION_92_ID                       0x5Cul                    /**< EEL片段92的对应内容的片段ID号*/
#define EEL_DATA_SECTION_93_ID                       0x5Dul                    /**< EEL片段93的对应内容的片段ID号*/
#define EEL_DATA_SECTION_94_ID                       0x5Eul                    /**< EEL片段94的对应内容的片段ID号*/
#define EEL_DATA_SECTION_95_ID                       0x5Ful                    /**< EEL片段95的对应内容的片段ID号*/
#define EEL_DATA_SECTION_96_ID                       0x60ul                    /**< EEL片段96的对应内容的片段ID号*/
#define EEL_DATA_SECTION_97_ID                       0x61ul                    /**< EEL片段97的对应内容的片段ID号*/
#define EEL_DATA_SECTION_98_ID                       0x62ul                    /**< EEL片段98的对应内容的片段ID号*/
#define EEL_DATA_SECTION_99_ID                       0x63ul                    /**< EEL片段99的对应内容的片段ID号*/






#define EEL_DATA_SECTION_5_LEN                      ((EEL_DATA_SECTION_5_END)-(EEL_DATA_SECTION_5_START))   /**< EEL片段5的数据长度 */
#define EEL_DATA_SECTION_6_LEN                      ((EEL_DATA_SECTION_6_END)-(EEL_DATA_SECTION_6_START))   /**< EEL片段6的数据长度 */
#define EEL_DATA_SECTION_7_LEN                      ((EEL_DATA_SECTION_7_END)-(EEL_DATA_SECTION_7_START))   /**< EEL片段7的数据长度 */
#define EEL_DATA_SECTION_8_LEN                      ((EEL_DATA_SECTION_8_END)-(EEL_DATA_SECTION_8_START))   /**< EEL片段8的数据长度 */
#define EEL_DATA_SECTION_9_LEN                      ((EEL_DATA_SECTION_9_END)-(EEL_DATA_SECTION_9_START))   /**< EEL片段9的数据长度 */
#define EEL_DATA_SECTION_10_LEN                      ((EEL_DATA_SECTION_10_END)-(EEL_DATA_SECTION_10_START))   /**< EEL片段10的数据长度 */
#define EEL_DATA_SECTION_11_LEN                      ((EEL_DATA_SECTION_11_END)-(EEL_DATA_SECTION_11_START))   /**< EEL片段11的数据长度 */
#define EEL_DATA_SECTION_12_LEN                      ((EEL_DATA_SECTION_12_END)-(EEL_DATA_SECTION_12_START))   /**< EEL片段12的数据长度 */
#define EEL_DATA_SECTION_13_LEN                      ((EEL_DATA_SECTION_13_END)-(EEL_DATA_SECTION_13_START))   /**< EEL片段13的数据长度 */
#define EEL_DATA_SECTION_14_LEN                      ((EEL_DATA_SECTION_14_END)-(EEL_DATA_SECTION_14_START))   /**< EEL片段14的数据长度 */
#define EEL_DATA_SECTION_15_LEN                      ((EEL_DATA_SECTION_15_END)-(EEL_DATA_SECTION_15_START))   /**< EEL片段15的数据长度 */
#define EEL_DATA_SECTION_16_LEN                      ((EEL_DATA_SECTION_16_END)-(EEL_DATA_SECTION_16_START))   /**< EEL片段16的数据长度 */
#define EEL_DATA_SECTION_17_LEN                      ((EEL_DATA_SECTION_17_END)-(EEL_DATA_SECTION_17_START))   /**< EEL片段17的数据长度 */
#define EEL_DATA_SECTION_18_LEN                      ((EEL_DATA_SECTION_18_END)-(EEL_DATA_SECTION_18_START))   /**< EEL片段18的数据长度 */
#define EEL_DATA_SECTION_19_LEN                      ((EEL_DATA_SECTION_19_END)-(EEL_DATA_SECTION_19_START))   /**< EEL片段19的数据长度 */
#define EEL_DATA_SECTION_20_LEN                      ((EEL_DATA_SECTION_20_END)-(EEL_DATA_SECTION_20_START))   /**< EEL片段20的数据长度 */
#define EEL_DATA_SECTION_21_LEN                      ((EEL_DATA_SECTION_21_END)-(EEL_DATA_SECTION_21_START))   /**< EEL片段21的数据长度 */
#define EEL_DATA_SECTION_22_LEN                      ((EEL_DATA_SECTION_22_END)-(EEL_DATA_SECTION_22_START))   /**< EEL片段22的数据长度 */
#define EEL_DATA_SECTION_23_LEN                      ((EEL_DATA_SECTION_23_END)-(EEL_DATA_SECTION_23_START))   /**< EEL片段23的数据长度 */
#define EEL_DATA_SECTION_24_LEN                      ((EEL_DATA_SECTION_24_END)-(EEL_DATA_SECTION_24_START))   /**< EEL片段24的数据长度 */
#define EEL_DATA_SECTION_25_LEN                      ((EEL_DATA_SECTION_25_END)-(EEL_DATA_SECTION_25_START))   /**< EEL片段25的数据长度 */
#define EEL_DATA_SECTION_26_LEN                      ((EEL_DATA_SECTION_26_END)-(EEL_DATA_SECTION_26_START))   /**< EEL片段26的数据长度 */
#define EEL_DATA_SECTION_27_LEN                      ((EEL_DATA_SECTION_27_END)-(EEL_DATA_SECTION_27_START))   /**< EEL片段27的数据长度 */
#define EEL_DATA_SECTION_28_LEN                      ((EEL_DATA_SECTION_28_END)-(EEL_DATA_SECTION_28_START))   /**< EEL片段28的数据长度 */
#define EEL_DATA_SECTION_29_LEN                      ((EEL_DATA_SECTION_29_END)-(EEL_DATA_SECTION_29_START))   /**< EEL片段29的数据长度 */
#define EEL_DATA_SECTION_30_LEN                      ((EEL_DATA_SECTION_30_END)-(EEL_DATA_SECTION_30_START))   /**< EEL片段30的数据长度 */
#define EEL_DATA_SECTION_31_LEN                      ((EEL_DATA_SECTION_31_END)-(EEL_DATA_SECTION_31_START))   /**< EEL片段31的数据长度 */
#define EEL_DATA_SECTION_32_LEN                      ((EEL_DATA_SECTION_32_END)-(EEL_DATA_SECTION_32_START))   /**< EEL片段32的数据长度 */
#define EEL_DATA_SECTION_33_LEN                      ((EEL_DATA_SECTION_33_END)-(EEL_DATA_SECTION_33_START))   /**< EEL片段33的数据长度 */
#define EEL_DATA_SECTION_34_LEN                      ((EEL_DATA_SECTION_34_END)-(EEL_DATA_SECTION_34_START))   /**< EEL片段34的数据长度 */
#define EEL_DATA_SECTION_35_LEN                      ((EEL_DATA_SECTION_35_END)-(EEL_DATA_SECTION_35_START))   /**< EEL片段35的数据长度 */
#define EEL_DATA_SECTION_36_LEN                      ((EEL_DATA_SECTION_36_END)-(EEL_DATA_SECTION_36_START))   /**< EEL片段36的数据长度 */
#define EEL_DATA_SECTION_37_LEN                      ((EEL_DATA_SECTION_37_END)-(EEL_DATA_SECTION_37_START))   /**< EEL片段37的数据长度 */
#define EEL_DATA_SECTION_38_LEN                      ((EEL_DATA_SECTION_38_END)-(EEL_DATA_SECTION_38_START))   /**< EEL片段38的数据长度 */
#define EEL_DATA_SECTION_39_LEN                      ((EEL_DATA_SECTION_39_END)-(EEL_DATA_SECTION_39_START))   /**< EEL片段39的数据长度 */
#define EEL_DATA_SECTION_40_LEN                      ((EEL_DATA_SECTION_40_END)-(EEL_DATA_SECTION_40_START))   /**< EEL片段40的数据长度 */
#define EEL_DATA_SECTION_41_LEN                      ((EEL_DATA_SECTION_41_END)-(EEL_DATA_SECTION_41_START))   /**< EEL片段41的数据长度 */
#define EEL_DATA_SECTION_42_LEN                      ((EEL_DATA_SECTION_42_END)-(EEL_DATA_SECTION_42_START))   /**< EEL片段42的数据长度 */
#define EEL_DATA_SECTION_43_LEN                      ((EEL_DATA_SECTION_43_END)-(EEL_DATA_SECTION_43_START))   /**< EEL片段43的数据长度 */
#define EEL_DATA_SECTION_44_LEN                      ((EEL_DATA_SECTION_44_END)-(EEL_DATA_SECTION_44_START))   /**< EEL片段44的数据长度 */
#define EEL_DATA_SECTION_45_LEN                      ((EEL_DATA_SECTION_45_END)-(EEL_DATA_SECTION_45_START))   /**< EEL片段45的数据长度 */
#define EEL_DATA_SECTION_46_LEN                      ((EEL_DATA_SECTION_46_END)-(EEL_DATA_SECTION_46_START))   /**< EEL片段46的数据长度 */
#define EEL_DATA_SECTION_47_LEN                      ((EEL_DATA_SECTION_47_END)-(EEL_DATA_SECTION_47_START))   /**< EEL片段47的数据长度 */
#define EEL_DATA_SECTION_48_LEN                      ((EEL_DATA_SECTION_48_END)-(EEL_DATA_SECTION_48_START))   /**< EEL片段48的数据长度 */
#define EEL_DATA_SECTION_49_LEN                      ((EEL_DATA_SECTION_49_END)-(EEL_DATA_SECTION_49_START))   /**< EEL片段49的数据长度 */
#define EEL_DATA_SECTION_50_LEN                      ((EEL_DATA_SECTION_50_END)-(EEL_DATA_SECTION_50_START))   /**< EEL片段50的数据长度 */
#define EEL_DATA_SECTION_51_LEN                      ((EEL_DATA_SECTION_51_END)-(EEL_DATA_SECTION_51_START))   /**< EEL片段51的数据长度 */
#define EEL_DATA_SECTION_52_LEN                      ((EEL_DATA_SECTION_52_END)-(EEL_DATA_SECTION_52_START))   /**< EEL片段52的数据长度 */
#define EEL_DATA_SECTION_53_LEN                      ((EEL_DATA_SECTION_53_END)-(EEL_DATA_SECTION_53_START))   /**< EEL片段53的数据长度 */
#define EEL_DATA_SECTION_54_LEN                      ((EEL_DATA_SECTION_54_END)-(EEL_DATA_SECTION_54_START))   /**< EEL片段54的数据长度 */
#define EEL_DATA_SECTION_55_LEN                      ((EEL_DATA_SECTION_55_END)-(EEL_DATA_SECTION_55_START))   /**< EEL片段55的数据长度 */
#define EEL_DATA_SECTION_56_LEN                      ((EEL_DATA_SECTION_56_END)-(EEL_DATA_SECTION_56_START))   /**< EEL片段56的数据长度 */
#define EEL_DATA_SECTION_57_LEN                      ((EEL_DATA_SECTION_57_END)-(EEL_DATA_SECTION_57_START))   /**< EEL片段57的数据长度 */
#define EEL_DATA_SECTION_58_LEN                      ((EEL_DATA_SECTION_58_END)-(EEL_DATA_SECTION_58_START))   /**< EEL片段58的数据长度 */
#define EEL_DATA_SECTION_59_LEN                      ((EEL_DATA_SECTION_59_END)-(EEL_DATA_SECTION_59_START))   /**< EEL片段59的数据长度 */
#define EEL_DATA_SECTION_60_LEN                      ((EEL_DATA_SECTION_60_END)-(EEL_DATA_SECTION_60_START))   /**< EEL片段60的数据长度 */
#define EEL_DATA_SECTION_61_LEN                      ((EEL_DATA_SECTION_61_END)-(EEL_DATA_SECTION_61_START))   /**< EEL片段61的数据长度 */
#define EEL_DATA_SECTION_62_LEN                      ((EEL_DATA_SECTION_62_END)-(EEL_DATA_SECTION_62_START))   /**< EEL片段62的数据长度 */
#define EEL_DATA_SECTION_63_LEN                      ((EEL_DATA_SECTION_63_END)-(EEL_DATA_SECTION_63_START))   /**< EEL片段63的数据长度 */
#define EEL_DATA_SECTION_64_LEN                      ((EEL_DATA_SECTION_64_END)-(EEL_DATA_SECTION_64_START))   /**< EEL片段64的数据长度 */
#define EEL_DATA_SECTION_65_LEN                      ((EEL_DATA_SECTION_65_END)-(EEL_DATA_SECTION_65_START))   /**< EEL片段65的数据长度 */
#define EEL_DATA_SECTION_66_LEN                      ((EEL_DATA_SECTION_66_END)-(EEL_DATA_SECTION_66_START))   /**< EEL片段66的数据长度 */
#define EEL_DATA_SECTION_67_LEN                      ((EEL_DATA_SECTION_67_END)-(EEL_DATA_SECTION_67_START))   /**< EEL片段67的数据长度 */
#define EEL_DATA_SECTION_68_LEN                      ((EEL_DATA_SECTION_68_END)-(EEL_DATA_SECTION_68_START))   /**< EEL片段68的数据长度 */
#define EEL_DATA_SECTION_69_LEN                      ((EEL_DATA_SECTION_69_END)-(EEL_DATA_SECTION_69_START))   /**< EEL片段69的数据长度 */
#define EEL_DATA_SECTION_70_LEN                      ((EEL_DATA_SECTION_70_END)-(EEL_DATA_SECTION_70_START))   /**< EEL片段70的数据长度 */
#define EEL_DATA_SECTION_71_LEN                      ((EEL_DATA_SECTION_71_END)-(EEL_DATA_SECTION_71_START))   /**< EEL片段71的数据长度 */
#define EEL_DATA_SECTION_72_LEN                      ((EEL_DATA_SECTION_72_END)-(EEL_DATA_SECTION_72_START))   /**< EEL片段72的数据长度 */
#define EEL_DATA_SECTION_73_LEN                      ((EEL_DATA_SECTION_73_END)-(EEL_DATA_SECTION_73_START))   /**< EEL片段73的数据长度 */
#define EEL_DATA_SECTION_74_LEN                      ((EEL_DATA_SECTION_74_END)-(EEL_DATA_SECTION_74_START))   /**< EEL片段74的数据长度 */
#define EEL_DATA_SECTION_75_LEN                      ((EEL_DATA_SECTION_75_END)-(EEL_DATA_SECTION_75_START))   /**< EEL片段75的数据长度 */
#define EEL_DATA_SECTION_76_LEN                      ((EEL_DATA_SECTION_76_END)-(EEL_DATA_SECTION_76_START))   /**< EEL片段76的数据长度 */
#define EEL_DATA_SECTION_77_LEN                      ((EEL_DATA_SECTION_77_END)-(EEL_DATA_SECTION_77_START))   /**< EEL片段77的数据长度 */
#define EEL_DATA_SECTION_78_LEN                      ((EEL_DATA_SECTION_78_END)-(EEL_DATA_SECTION_78_START))   /**< EEL片段78的数据长度 */
#define EEL_DATA_SECTION_79_LEN                      ((EEL_DATA_SECTION_79_END)-(EEL_DATA_SECTION_79_START))   /**< EEL片段79的数据长度 */
#define EEL_DATA_SECTION_80_LEN                      ((EEL_DATA_SECTION_80_END)-(EEL_DATA_SECTION_80_START))   /**< EEL片段80的数据长度 */
#define EEL_DATA_SECTION_81_LEN                      ((EEL_DATA_SECTION_81_END)-(EEL_DATA_SECTION_81_START))   /**< EEL片段81的数据长度 */
#define EEL_DATA_SECTION_82_LEN                      ((EEL_DATA_SECTION_82_END)-(EEL_DATA_SECTION_82_START))   /**< EEL片段82的数据长度 */
#define EEL_DATA_SECTION_83_LEN                      ((EEL_DATA_SECTION_83_END)-(EEL_DATA_SECTION_83_START))   /**< EEL片段83的数据长度 */
#define EEL_DATA_SECTION_84_LEN                      ((EEL_DATA_SECTION_84_END)-(EEL_DATA_SECTION_84_START))   /**< EEL片段84的数据长度 */
#define EEL_DATA_SECTION_85_LEN                      ((EEL_DATA_SECTION_85_END)-(EEL_DATA_SECTION_85_START))   /**< EEL片段85的数据长度 */
#define EEL_DATA_SECTION_86_LEN                      ((EEL_DATA_SECTION_86_END)-(EEL_DATA_SECTION_86_START))   /**< EEL片段86的数据长度 */
#define EEL_DATA_SECTION_87_LEN                      0x80ul                                                    /*用于升级log回读*/ /**< EEL片段87的对应内容的片段ID号*/
#define EEL_DATA_SECTION_88_LEN                      0x80ul                                                    /*用于标定数据存储，DTC存储*//**< EEL片段88的对应内容的片段ID号*/
#define EEL_DATA_SECTION_89_LEN                      0x80ul                    /**< EEL片段89的对应内容的片段ID号*/
/****************************预留*****************************************预留******************************************预留**************************************************/

#define EEL_DATA_SECTION_90_LEN                      0x80ul                    /**< EEL片段90的对应内容的片段ID号*/
#define EEL_DATA_SECTION_91_LEN                      0x80ul                    /**< EEL片段91的对应内容的片段ID号*/
#define EEL_DATA_SECTION_92_LEN                      0x80ul                    /**< EEL片段92的对应内容的片段ID号*/
#define EEL_DATA_SECTION_93_LEN                      0x80ul                    /**< EEL片段93的对应内容的片段ID号*/
#define EEL_DATA_SECTION_94_LEN                      0x80ul                    /**< EEL片段94的对应内容的片段ID号*/
#define EEL_DATA_SECTION_95_LEN                      0x80ul                    /**< EEL片段95的对应内容的片段ID号*/
#define EEL_DATA_SECTION_96_LEN                      0x80ul                    /**< EEL片段96的对应内容的片段ID号*/
#define EEL_DATA_SECTION_97_LEN                      0x80ul                    /**< EEL片段97的对应内容的片段ID号*/
#define EEL_DATA_SECTION_98_LEN                      0x80ul                    /**< EEL片段98的对应内容的片段ID号*/
#define EEL_DATA_SECTION_99_LEN                      0x80ul                    /**< EEL片段98的对应内容的片段ID号*/
#endif
