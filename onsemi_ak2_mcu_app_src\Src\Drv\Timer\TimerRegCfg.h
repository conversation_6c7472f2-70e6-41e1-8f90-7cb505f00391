/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: TimerRegCfg.h
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/
#ifndef __TIMERCFG_H
#define __TIMERCFG_H

/**********************************宏定义**************************************/
/*
    WDT start control and trigger register (WDTAnWDTE)
*/
/* Enables/disables the WDT (WDTAnWDTE[7-0]) */
#define _WDT_START_CODE                           (0xACU) /* WDT enabled */
#define _WDT_RESET_CODE                           (0xFFU) /* other than 0xAC will trigger an error */

/*
    WDT mode setting register (WDTAnMD)
*/
/* Select the overflow time (WDTAnOVF2,WDTAnOVF1,WDTAnOVF0,) */
#define _WDT_OVERFLOW_WDTACKI_2_9                 (0x00U)
#define _WDT_OVERFLOW_WDTACKI_2_10                (0x10U)
#define _WDT_OVERFLOW_WDTACKI_2_11                (0x20U)
#define _WDT_OVERFLOW_WDTACKI_2_12                (0x30U)
#define _WDT_OVERFLOW_WDTACKI_2_13                (0x40U)
#define _WDT_OVERFLOW_WDTACKI_2_14                (0x50U)
#define _WDT_OVERFLOW_WDTACKI_2_15                (0x60U)
#define _WDT_OVERFLOW_WDTACKI_2_16                (0x70U)
/* Enables/disables the 75% interrupt request WDTAnTIT (WDTAnWIE) */
#define _WDT_INTERRUPT_DISABLED                   (0x00U) /* WDTAnTIT disabled */
#define _WDT_INTERRUPT_ENABLED                    (0x08U) /* WDTAnTIT enabled */
/* Specifies the error mode (WDTAnERM) */
#define _WDT_NMI_REQUEST_MODE                     (0x00U) /* NMI request mode */
#define _WDT_RESET_MODE                           (0x04U) /* Reset mode */
/* Select the period over which the window is open (WDTAnWS1,WDTAnWS0) */
#define _WDT_PERIOD_25                            (0x00U) /* Window-open period 25%  */
#define _WDT_PERIOD_50                            (0x01U) /* Window-open period 50% */
#define _WDT_PERIOD_75                            (0x02U) /* Window-open period 75% */
#define _WDT_PERIOD_100                           (0x03U) /* Window-open period 100% */

/*
    OSTM Count Start Trigger Register (OSTMnTS)
*/
/* Starts the counter (OSTMnTS) */
#define _OSTM_COUNTER_START                       (0x01U) /* Starts the counter */

/*
    OSTM Count Stop Trigger Register (OSTMnTT)
*/
/* Stops the counter (OSTMnTT) */
#define _OSTM_COUNTER_STOP                        (0x01U) /* Stops the counter */

/*
    OSTM Control Register (OSTMnCTL)
*/
/* Specifies the operating mode for the counter (OSTMnMD1) */
#define _OSTM_MODE_INTERVAL_TIMER                 (0x00U) /* Interval timer mode */
#define _OSTM_MODE_FREE_RUNNING                   (0x02U) /* Free-running comparison mode */
/* Specifies the operating mode for the counter (OSTMnMD0) */
#define _OSTM_START_INTERRUPT_DISABLE             (0x00U) /* Disables the interrupts when counting starts */
#define _OSTM_START_INTERRUPT_ENABLE              (0x01U) /* Enables the interrupts when counting starts */

/* OSTMn Compare Register (OSTMnCMP) */
#define _OSTM0_COMPARING_COUNTER                  (0x0000EA5FUL) /* Compare match value:1ms */


#endif
