/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: UartHal.c
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/
#include "types.h"
#include "AppQueue.h"
#include "UartHal.h"
#include "UartDrv.h"
#include "debug.h"

/**********************************宏定义**************************************/

/********************************数据类型定义**********************************/

/**********************************变量定义************************************/
uint8 Gu8InitFlag = FALSE;
boolean GbIsBusy = FALSE;
/********************************函数声明定义**********************************/

/********************************全局函数定义**********************************/
void UartTriggerTx (void);

/******************************************************************************/
/**<pre>
 *函数名称: UartHalInit
 *功能描述: Uart抽象层初始化
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void UartHalInit(void)
{
#if(UART_PRINT_EN == STD_ON)
    UartDrvInit();    /**<pre> 调用函数UartDrvInit()初始化UART驱动 </pre>*/
    Gu8InitFlag = TRUE;
    GbIsBusy = FALSE;
    PLFM_DEBUG_PRINT("-----System Init Successful !-----\r\n");
#endif
}

/******************************************************************************/
/**<pre>
 *函数名称: UartHalClose
 *功能描述: Uart模块关闭
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void UartHalClose(void)
{
#if(UART_PRINT_EN == STD_ON)
    UartDrvClose();    /**<pre> 调用函数UartDrvClose()关闭UART驱动 </pre>*/
    Gu8InitFlag = FALSE;
    GbIsBusy = FALSE;
#endif
}

/******************************************************************************/
/**<pre>
 *函数名称: Uart_Mainfunction_5ms
 *功能描述: 5ms周期打印任务
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void Uart_Mainfunction_5ms(void)
{
#if(UART_PRINT_EN == STD_ON)
    DEBUG_RX_SCAN();
    UartTriggerTx();
#endif
}

/******************************************************************************/
/**<pre>
 *函数名称: UartTxIntCallBack
 *功能描述: Uart发送中断回调处理函数
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void UartTxIntCallBack(void)
{
    uint8 Lu8Data = 0u;
    if(AppQueue_Dequeue(QUEUE_UART_PRINT, &Lu8Data, 1u) == TRUE)
    {
        UartSetTxData(Lu8Data);
    }
    else
    {
        GbIsBusy = FALSE;
    }
}

/******************************************************************************/
/**<pre>
 *函数名称: UartRxIntCallBack
 *功能描述: Uart接收中断回调处理函数
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void UartRxIntCallBack(void)
{
    uint8 Lu8Data = UartGetRxData();
    (void)AppQueue_Enqueue(QUEUE_UART_DEBUG_RX, &Lu8Data, 1u);
}

/******************************************************************************/
/**<pre>
 *函数名称: UartTriggerTx
 *功能描述: Uart触发发送
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void UartTriggerTx (void)
{
    uint8 Lu8TxData;
    if(Gu8InitFlag == TRUE)
    {
        if(FALSE == GbIsBusy)
        {
            if(AppQueue_Dequeue(QUEUE_UART_PRINT, &Lu8TxData, 1u))
            {
                UartSetTxData(Lu8TxData);
                GbIsBusy = TRUE;
            }
        }
    }
}
