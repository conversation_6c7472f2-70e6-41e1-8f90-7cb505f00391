/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * CAN_AppSignalManage:
 * Created on: 2022-11-24 19:43
 * Original designer: 22866
 ******************************************************************************/


#ifndef __PSL_APPSIGNALMANAGE_H__
#define __PSL_APPSIGNALMANAGE_H__

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "PSL_AppSignalManage_Types.h"



/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/
typedef enum
{
    SLOT_STATUS_PSNONE = 0,
    SLOT_STATUS_PSOK,
}OUTPUT_SLOT_STATUS;


/* 定义输出车位类型         */
typedef enum
{
    SLOT_TYPE_LEFT_H = 0,
    SLOT_TYPE_RIGHT_H,
    SLOT_TYPE_LEFT_V,
    SLOT_TYPE_RIGHT_V,
    SLOT_TYPE_RESERVED_FIRS,
    SLOT_TYPE_RESERVED_SEC,
    SLOT_TYPE_RESERVED_THIR,
    SLOT_TYPE_RESERVED_INVALID,
}PSL_SLOT_TYPE;

typedef struct
{
    float dFirstObjWx;
    float dFirstObjWy;
    float dSecObjWx;
    float dSecObjWy;
    float dObj1Slope;
    float dObj2Slope;
}PSLCalSlotCoorType;

/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/
#define PSLSLOTNUM    8


/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/

extern PSLCalSlotCoorType PSLCalSlotcoor[8];
extern PSLOutputinfoType GstrAPAslotInfo[8];

/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/






/******************************************************************************
* 设计描述 : PSL 输出车位信息
* 设计索引 : 
*******************************************************************************/
extern void PSLOutputtoCANSlotinfoInit(void);
extern void PSLOutputReadinfo(PSLOutputinfoType *LstrPSLOutputInfo,uint8 wslotcnt);
extern void PSLCoordinateSwitch(void);



#endif /* end of __PSL_APPSIGNALMANAGE_H__ */

