/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * SDW_int: 
 * Created on: 2020-12-30 15:34
 * Original designer: Fangleyun
 ******************************************************************************/

#ifndef SDW_int_H
#define SDW_int_H

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "SDW_Cfg.h"
#include "SnsRawData_Int.h"


/******************************************************************************
 * Compiler option used to distinguish inclusion done by the owner
 *****************************************************************************/
#ifdef   SYS_USE_SDW

/******************************************************************************
 * Inclusion of private header files
 *****************************************************************************/


#ifdef __cplusplus
extern "C"{
#endif

/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
extern SDWCurCarPointDataType GstrSDWCurCarPointData;
extern SDW_SnsOriginalDataType GstrSDWSnsOriginalData;
extern SDW_SnsBufferDataType GstrSDWSnsBufferData;
extern SDW_CarCoorType GstrCarCoor;
extern SDWOriginalDataType GstrSDWOriginalData[3];
extern uint16 Gu16SDW_VirtualDis[SIDE_OBJ_NUM][SIDE_AREA_NUM];
extern uint8 GstrSDWOriDataIndex[4];
extern SDW_ObjCoorType GstrObjCoorBuffer[SIDE_OBJ_NUM][SDW_MAX_BUFFER_CNT];
extern SDW_ValidObjCoorType GstrSDWValidObjCoor;
extern uint8 u8StatusDataFlg[SDW_SNS_CHANNEL_NUM];

/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
void SDWAlgorithm_Task(void);
void SDWDataInit(void);
extern void SDW_GetOriginalData(PDCSnsGroupType LeGroup,PDCSnsChannelType LenuSnsCh);
extern uint16 ReturnSDWSideVirtualDis(SDW_tenuSIDE_INDEX LenuSideIndex,SDW_tenuSIDE_AREA LenuSideArea);


#ifdef __cplusplus
}
#endif

#endif /* end of SYS_USE_XXX */

#endif /* end of SDW_int_H */

