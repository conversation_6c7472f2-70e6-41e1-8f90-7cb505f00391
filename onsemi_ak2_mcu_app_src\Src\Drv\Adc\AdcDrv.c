/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: AdcDrv.c
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/
#include "types.h"
#include "AdcRegCfg.h"
#include "AdcDrv.h"

/******************************************************************************/
/**<pre>
 *函数名称: ADCDrvInit
 *功能描述: ADC初始化配置
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void ADCDrvInit(void)
{
    /* Disable ADC0 error interrupt (INTADCA0ERR) operation and clear request */
    INTC2.ICADCA0ERR.BIT.MKADCA0ERR = _INT_PROCESSING_DISABLED;
    INTC2.ICADCA0ERR.BIT.RFADCA0ERR = _INT_REQUEST_NOT_OCCUR;
    /* Disable ADC0 SG1 end interrupt (INTADCA0I0) operation and clear request */
    INTC1.ICADCA0I0.BIT.MKADCA0I0 = _INT_PROCESSING_DISABLED;
    INTC1.ICADCA0I0.BIT.RFADCA0I0 = _INT_REQUEST_NOT_OCCUR;
    /* Disable ADC0 SG2 end interrupt (INTADCA0I1) operation and clear request */
    INTC1.ICADCA0I1.BIT.MKADCA0I1 = _INT_PROCESSING_DISABLED;
    INTC1.ICADCA0I1.BIT.RFADCA0I1 = _INT_REQUEST_NOT_OCCUR;
    /* Disable ADC0 SG3 end interrupt (INTADCA0I2) operation and clear request */
    INTC1.ICADCA0I2.BIT.MKADCA0I2 = _INT_PROCESSING_DISABLED;
    INTC1.ICADCA0I2.BIT.RFADCA0I2 = _INT_REQUEST_NOT_OCCUR;
    /* Set ADC0 error interrupt (INTADCA0ERR) setting */
    INTC2.ICADCA0ERR.BIT.TBADCA0ERR = _INT_TABLE_VECTOR;
    INTC2.ICADCA0ERR.UINT16 &= _INT_PRIORITY_LOWEST;
    /* Set ADC0 SG1 end interrupt (INTADCA0I0) setting */
    INTC1.ICADCA0I0.BIT.TBADCA0I0 = _INT_TABLE_VECTOR;
    INTC1.ICADCA0I0.UINT16 &= _INT_PRIORITY_LOWEST;
    /* Set ADC0 global setting */
    ADCA0.ADCR.UINT32 = _ADC_12_BIT_MODE;
    ADCA0.SMPCR.UINT32 = _ADC_24_CYCLES;
    ADCA0.SFTCR.UINT32 = _ADC_READ_CLEAR_DISABLED | _ADC_LIMIT_ERROR_INT_ENABLE | _ADC_OVERWRITE_ERROR_INT_ENABLE;
    ADCA0.ULLMTBR0.UINT32 = _ADC0_UPPER_LIMIT_TABLE0 | _ADC0_LOWER_LIMIT_TABLE0;
    ADCA0.ULLMTBR1.UINT32 = _ADC0_UPPER_LIMIT_TABLE1 | _ADC0_LOWER_LIMIT_TABLE1;
    ADCA0.ULLMTBR2.UINT32 = _ADC0_UPPER_LIMIT_TABLE2 | _ADC0_LOWER_LIMIT_TABLE2;
    /* Set ADC0 scan group setting */
    ADCA0.SGVCSP1.UINT32 = _ADC0_SG1_START_POINTER;
    ADCA0.SGVCEP1.UINT32 = _ADC0_SG1_END_POINTER;
    ADCA0.SGMCYCR1.UINT32 = _ADC_SG_MULTICYCLE_NUMBER_1;
    ADCA0.SGCR1.UINT32 = _ADC_SG_SCAN_MODE_MULTICYCLE | _ADC_SG_SCAN_END_INT_ENABLE | _ADC_SG_HW_TRIGGER_DISABLE;
    /* Set ADC0 vitrual channel setting */
    ADCA0.VCR00.UINT32 = _ADC_VIRTUAL_CHANNEL_END_INT_DISABLE | _ADC_LIMIT_TABLE_SELECT_NONE | 
                         _ADC_PHYSICAL_CHANNEL_AN00;
    ADCA0.VCR01.UINT32 = _ADC_VIRTUAL_CHANNEL_END_INT_DISABLE | _ADC_LIMIT_TABLE_SELECT_NONE | 
                         _ADC_PHYSICAL_CHANNEL_AN01;
    ADCA0.VCR02.UINT32 = _ADC_VIRTUAL_CHANNEL_END_INT_DISABLE | _ADC_LIMIT_TABLE_SELECT_NONE | 
                         _ADC_PHYSICAL_CHANNEL_AN02;
    ADCA0.VCR03.UINT32 = _ADC_VIRTUAL_CHANNEL_END_INT_DISABLE | _ADC_LIMIT_TABLE_SELECT_NONE | 
                         _ADC_PHYSICAL_CHANNEL_AN03;
    ADCA0.VCR04.UINT32 = _ADC_VIRTUAL_CHANNEL_END_INT_DISABLE | _ADC_LIMIT_TABLE_SELECT_NONE | 
                         _ADC_PHYSICAL_CHANNEL_AN04;
    /* Set T&H setting */
    ADCA0.THCR.UINT32 = _ADC_TH_SAMPLING_MANUAL;
    ADCA0.THER.UINT32 = _ADC_TH4_DISABLED | _ADC_TH3_DISABLED | _ADC_TH2_DISABLED | _ADC_TH1_DISABLED | 
                        _ADC_TH0_DISABLED;

    INTC1.ICADCA0I0.BIT.MKADCA0I0 = _INT_PROCESSING_ENABLED;					
}

/******************************************************************************/
/**<pre>
 *函数名称: ADCDrvClose
 *功能描述: ADC模块关闭-停止采样
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void ADCDrvClose(void)
{
    ADCA0.ADHALTR.UINT32 = _ADC_HALT;
}

/******************************************************************************/
/**<pre>
 *函数名称: ADC0_ScanGroup1_Start
 *功能描述:	启动ADCO ScanGroup1转换
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void ADC0_ScanGroup1_Start(void)
{
    /* Enable ADC0 SG1 operation */
    ADCA0.SGSTCR1.UINT32 = _ADC_START;
}

/******************************************************************************/
/**<pre>
 *函数名称: ADC0_ScanGroup1_GetResult
 *功能描述: ADC0模块-获取采样结果
 *输入参数: 无
 *输出参数: Lu16Buffer 采样结果缓存
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void ADC0_ScanGroup1_GetResult(uint16 * const Lpu16Buffer)
{
    uint8 count_num, count;
    volatile uint32 *addr;
    uint32 convert_data;

    count_num = ADCA0SGVCEP1LL;
    count_num = count_num - ADCA0SGVCSP1LL + 1u;
    addr = (volatile uint32 *)(_ADC0_VCHANNEL_DATA_BASE + (2u * ADCA0SGVCSP1));

    for (count = 0u; count < count_num; count = count + 2u)
    {
        convert_data = (uint32)*addr;
        Lpu16Buffer[count] = (uint16)(convert_data & 0x0000FFFFul);
        if ( count_num - count > 1u)
        {
            Lpu16Buffer[count + 1u] = (uint16)((convert_data & 0xFFFF0000ul) >> 16u);
        }
        addr++;
    }
}

