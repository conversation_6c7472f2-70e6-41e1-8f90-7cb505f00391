/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * SnsRawDataCalib: 
 * Created on: 2023-02-27 14:29
 * Original designer: AntonyFang
 ******************************************************************************/
#ifndef SnsRawDataCalib_H
#define SnsRawDataCalib_H

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "types.h"
#include "SnsRawData_Type.h"

/******************************************************************************
 * Compiler option used to distinguish inclusion done by the owner
 *****************************************************************************/

/******************************************************************************
 * Inclusion of private header files
 *****************************************************************************/

/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/
#define DEMO_THRESHOLD      0

/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/

/* 定义原始回波筛选的高度类型 */
typedef struct
{
    uint16 u16PVC_PileHeight;
    uint16 u16BigWallHeight;
    uint16 u16SecondWallHeight;
    uint16 u16CurbHeight;
}SnsCalibHeightType;

/* 定义ECU端阈值的梯度，10cm一个阶梯*/
typedef enum
{
    SNS_DIS_10cm = 0,
    SNS_DIS_20cm, 
    SNS_DIS_30cm,
    SNS_DIS_40cm,
    SNS_DIS_50cm, 
    SNS_DIS_60cm,
    SNS_DIS_70cm,
    SNS_DIS_80cm, 
    SNS_DIS_90cm,
    SNS_DIS_100cm,

    SNS_DIS_110cm, 
    SNS_DIS_120cm, 
    SNS_DIS_130cm,
    SNS_DIS_140cm, 
    SNS_DIS_150cm,
    SNS_DIS_160cm, 
    SNS_DIS_170cm, 
    SNS_DIS_180cm,
    SNS_DIS_190cm, 
    SNS_DIS_200cm,

    SNS_DIS_210cm, 
    SNS_DIS_220cm, 
    SNS_DIS_230cm,
    SNS_DIS_240cm, 
    SNS_DIS_250cm,
    SNS_DIS_260cm, 
    SNS_DIS_270cm, 
    SNS_DIS_280cm,
    SNS_DIS_290cm, 
    SNS_DIS_300cm,

    SNS_DIS_310cm, 
    SNS_DIS_320cm,
    SNS_DIS_330cm,
    SNS_DIS_340cm, 
    SNS_DIS_350cm,
    SNS_DIS_360cm, 
    SNS_DIS_370cm,
    SNS_DIS_380cm,
    SNS_DIS_390cm, 
    SNS_DIS_400cm,
    SNS_DIS_TOTAL_NUM
}PDCSnsDisStepType;

/* 重新定义ECU端判断高度阈值的梯度，10cm一个阶梯*/
typedef enum
{
    SNS_DIS_HIGH_10cm =0, 
    SNS_DIS_HIGH_20cm,
    SNS_DIS_HIGH_30cm,
    SNS_DIS_HIGH_40cm, 
    SNS_DIS_HIGH_50cm,

    SNS_DIS_HIGH_60cm, 
    SNS_DIS_HIGH_70cm,
    SNS_DIS_HIGH_80cm,
    SNS_DIS_HIGH_90cm, 
    SNS_DIS_HIGH_100cm,

    SNS_DIS_HIGH_110cm, 
    SNS_DIS_HIGH_120cm,
    SNS_DIS_HIGH_130cm,
    SNS_DIS_HIGH_140cm, 
    SNS_DIS_HIGH_150cm,

    SNS_DIS_HIGH_160cm, 
    SNS_DIS_HIGH_170cm,
    SNS_DIS_HIGH_180cm,
    SNS_DIS_HIGH_190cm, 
    SNS_DIS_HIGH_200cm,

    SNS_DIS_HIGH_210cm, 
    SNS_DIS_HIGH_220cm,
    SNS_DIS_HIGH_230cm,
    SNS_DIS_HIGH_240cm, 
    SNS_DIS_HIGH_250cm,
    
    SNS_DIS_HIGH_260cm, 
    SNS_DIS_HIGH_270cm,
    SNS_DIS_HIGH_280cm,
    SNS_DIS_HIGH_290cm, 
    SNS_DIS_HIGH_300cm,

    SNS_DIS_HIGH_310cm, 
    SNS_DIS_HIGH_320cm,
    SNS_DIS_HIGH_330cm,
    SNS_DIS_HIGH_340cm, 
    SNS_DIS_HIGH_350cm,
    
    SNS_DIS_HIGH_360cm, 
    SNS_DIS_HIGH_370cm,
    SNS_DIS_HIGH_380cm,
    SNS_DIS_HIGH_390cm, 
    SNS_DIS_HIGH_400cm,
    SNS_DIS_HIGH_TOTAL_NUM
}PDCSnsDisHighStepType;


/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
extern const uint16 Gu16FLS_FRS_MasStandardThresholdTable[SNS_DIS_TOTAL_NUM];
extern const uint16 Gu16FLS_FRS_MasChirpThresholdTable[SNS_DIS_TOTAL_NUM];
extern const uint16 Gu16FL_FR_MasStandardThresholdTable[SNS_DIS_TOTAL_NUM];
extern const uint16 Gu16FL_FR_MasChirpThresholdTable[SNS_DIS_TOTAL_NUM];
extern const uint16 Gu16FML_FMR_MasStandardThresholdTable[SNS_DIS_TOTAL_NUM];
extern const uint16 Gu16FML_FMR_MasChirpThresholdTable[SNS_DIS_TOTAL_NUM];
extern const uint16 Gu16FLS_FRS_LisStandardThresholdTable[SNS_DIS_TOTAL_NUM];
extern const uint16 Gu16FLS_FRS_LisChirpThresholdTable[SNS_DIS_TOTAL_NUM];
extern const uint16 Gu16FL_FR_LisStandardThresholdTable[SNS_DIS_TOTAL_NUM];
extern const uint16 Gu16FL_FR_LisChirpThresholdTable[SNS_DIS_TOTAL_NUM];
extern const uint16 Gu16FML_FMR_LisStandardThresholdTable[SNS_DIS_TOTAL_NUM];
extern const uint16 Gu16FML_FMR_LisChirpThresholdTable[SNS_DIS_TOTAL_NUM];

extern const uint16 Gu16RLS_RRS_MasStandardThresholdTable[SNS_DIS_TOTAL_NUM];
extern const uint16 Gu16RLS_RRS_MasChirpThresholdTable[SNS_DIS_TOTAL_NUM];
extern const uint16 Gu16RL_RR_MasStandardThresholdTable[SNS_DIS_TOTAL_NUM];
extern const uint16 Gu16RL_RR_MasChirpThresholdTable[SNS_DIS_TOTAL_NUM];
extern const uint16 Gu16RML_RMR_MasStandardThresholdTable[SNS_DIS_TOTAL_NUM];
extern const uint16 Gu16RML_RMR_MasChirpThresholdTable[SNS_DIS_TOTAL_NUM];
extern const uint16 Gu16RLS_RRS_LisStandardThresholdTable[SNS_DIS_TOTAL_NUM];
extern const uint16 Gu16RLS_RRS_LisChirpThresholdTable[SNS_DIS_TOTAL_NUM];
extern const uint16 Gu16RL_RR_LisStandardThresholdTable[SNS_DIS_TOTAL_NUM];
extern const uint16 Gu16RL_RR_LisChirpThresholdTable[SNS_DIS_TOTAL_NUM];
extern const uint16 Gu16RML_RMR_LisStandardThresholdTable[SNS_DIS_TOTAL_NUM];
extern const uint16 Gu16RML_RMR_LisChirpThresholdTable[SNS_DIS_TOTAL_NUM];
extern const SnsCalibHeightType GStrObjJudgeStandardThresholdTable[SNS_DIS_HIGH_TOTAL_NUM];
extern const SnsCalibHeightType GStrObjJudgeChirpThresholdTable[SNS_DIS_HIGH_TOTAL_NUM];

extern const uint16 *Gpu16SnsMasThresholdTable[PDC_SNS_GROUP_NUM][6];
extern const uint16 *Gpu16SnsLisThresholdTable[PDC_SNS_GROUP_NUM][6];

/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/

extern uint16 Gu16FLS_FRS_MasStandardThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
extern uint16 Gu16FLS_FRS_MasChirpThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
extern uint16 Gu16FL_FR_MasStandardThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
extern uint16 Gu16FL_FR_MasChirpThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
extern uint16 Gu16FML_FMR_MasStandardThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
extern uint16 Gu16FML_FMR_MasChirpThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
extern uint16 Gu16FLS_FRS_LisStandardThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
extern uint16 Gu16FLS_FRS_LisChirpThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
extern uint16 Gu16FL_FR_LisStandardThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
extern uint16 Gu16FL_FR_LisChirpThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
extern uint16 Gu16FML_FMR_LisStandardThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
extern uint16 Gu16FML_FMR_LisChirpThresholdTableInRAM[SNS_DIS_TOTAL_NUM];

extern uint16 Gu16RLS_RRS_MasStandardThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
extern uint16 Gu16RLS_RRS_MasChirpThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
extern uint16 Gu16RL_RR_MasStandardThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
extern uint16 Gu16RL_RR_MasChirpThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
extern uint16 Gu16RML_RMR_MasStandardThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
extern uint16 Gu16RML_RMR_MasChirpThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
extern uint16 Gu16RLS_RRS_LisStandardThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
extern uint16 Gu16RLS_RRS_LisChirpThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
extern uint16 Gu16RL_RR_LisStandardThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
extern uint16 Gu16RL_RR_LisChirpThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
extern uint16 Gu16RML_RMR_LisStandardThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
extern uint16 Gu16RML_RMR_LisChirpThresholdTableInRAM[SNS_DIS_TOTAL_NUM];
extern SnsCalibHeightType GStrObjJudgeStandardThresholdTableInRAM[SNS_DIS_HIGH_TOTAL_NUM];
extern SnsCalibHeightType GStrObjJudgeChirpThresholdTableInRAM[SNS_DIS_HIGH_TOTAL_NUM];


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
void CopySnsCalibWidthAndThresholdToRam(void);

void BigLittleEndianSwap(uint8_t *p, uint16_t size);


#endif /* end of SnsRawDataCalib_H */

