/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: CANDrv.h
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/
#ifndef __CANDRV_H
#define __CANDRV_H
#include "types.h"
#include "CANRegCfg.h"

/************************************宏定义************************************/
/* CAN CHANNEL */
#define CAN_CHANNEL_0                       0u
#define CAN_CHANNEL_1                       1u
#define CAN_CHANNEL_2                       2u
#define CAN_CHANNEL_3                       3u
#define CAN_CHANNEL_4                       4u
#define CAN_CHANNEL_5                       5u
/* 选择的CAN通道 */
#define CAN_CHANNEL_USE                     CAN_CHANNEL_4

/******************************CAN总线设置 Start*******************************/
#define CAN_CLOCK_MHZ                       80u
/* CAN仲裁域 */
#define CAN_BAUD_RATE                       500u
#define CAN_CLOCK_PRESCALER                 1u
#define CAN_SAMPLE_POINT                    80u
#define CAN_NSJW                            16u
#define CAN_NTQ_NUM                         ((CAN_CLOCK_MHZ >> CAN_CLOCK_PRESCALER)*1000u/CAN_BAUD_RATE)
#define CAN_NTSEG1_NUM                      ((CAN_SAMPLE_POINT*CAN_NTQ_NUM/100u)-1u)
#define CAN_NTSEG2_NUM                      (CAN_NTQ_NUM - CAN_NTSEG1_NUM - 1u)
/* 波特率寄存器配置 */
#define _CAN_NSJW                           (CAN_NSJW-1u)
#define _CAN_NTSEG2                         (CAN_NTSEG2_NUM-1u)
#define _CAN_NTSEG1                         (CAN_NTSEG1_NUM-1u)
#define _CAN_NBRP                           (CAN_CLOCK_PRESCALER-1u)
#define _CFG_RCFDCnCFDCmNCFG                ((_CAN_NTSEG2<<24u)|(_CAN_NTSEG1<<16u)|(_CAN_NSJW<<11u)|_CAN_NBRP)
/* CAN数据域 */
#define CAN_FD_BAUD_RATE                    2000u
#define CAN_FD_CLOCK_PRESCALER              1u
#define CAN_FD_SAMPLE_POINT                 80u
#define CAN_DSJW                            4u
#define CAN_DTQ_NUM                         ((CAN_CLOCK_MHZ >> CAN_FD_CLOCK_PRESCALER)*1000u/CAN_FD_BAUD_RATE)
#define CAN_DTSEG1_NUM                      ((CAN_FD_SAMPLE_POINT*CAN_DTQ_NUM/100u)-1u)
#define CAN_DTSEG2_NUM                      (CAN_DTQ_NUM - CAN_DTSEG1_NUM - 1u)
/* 波特率寄存器配置 */
#define _CAN_DSJW                           (CAN_DSJW-1u)
#define _CAN_DTSEG2                         (CAN_DTSEG2_NUM-1u)
#define _CAN_DTSEG1                         (CAN_DTSEG1_NUM-1u)
#define _CAN_DBRP                           (CAN_FD_CLOCK_PRESCALER-1u)
#define _CFG_RCFDCnCFDCmDCFG                ((_CAN_DSJW<<24u)|(_CAN_DTSEG2<<20u)|(_CAN_DTSEG1<<16u)|_CAN_DBRP)
/******************************CAN总线设置 End*******************************/

/* Channel 0 configuration */
/* Bus Error Interrupt */
#define CANCFG_CH_IE_BUS                   CAN_DISABLE
/* Error Warning Interrupt */
#define CANCFG_CH_IE_EW                    CAN_DISABLE
/* Error Passive Interrupt */
#define CANCFG_CH_IE_EP                    CAN_DISABLE
/* Bus Off Entry Interrupt */
#define CANCFG_CH_IE_BOE                   CAN_ENABLE
/* Bus Off Recovery Interrupt  */
#define CANCFG_CH_IE_BOR                   CAN_ENABLE
/* Overload Frame Transmit Interrupt  */
#define CANCFG_CH_IE_OL                    CAN_DISABLE
/* Bus Lock Interrupt */
#define CANCFG_CH_IE_BL                    CAN_DISABLE
/* Arbitration Lost Interrupt  */
#define CANCFG_CH_IE_AL                    CAN_DISABLE
/* Transmit Abort Interrupt  */
#define CANCFG_CH_IE_TA                    CAN_DISABLE
/* Bus Off Recovery Mode Select */
#define CANCFG_CH_BOFF_MODE                CAN_BOFF_RECOVERY_ISO11898
/* Error Display Mode Select */
#define CANCFG_CH_ER_DISP                  CAN_ERRDISP_FIRST_ERR_ONLY

#define CANCFG_CH_CTR                      ((CANCFG_CH_ER_DISP<<23u)|(CANCFG_CH_BOFF_MODE<<21u)| \
                                            (CANCFG_CH_IE_TA<<16u)|(CANCFG_CH_IE_AL<<15u)|       \
                                            (CANCFG_CH_IE_BL<<14u)|(CANCFG_CH_IE_OL<<13u)|       \
                                            (CANCFG_CH_IE_BOR<<12u)|(CANCFG_CH_IE_BOE<<11u)|     \
                                            (CANCFG_CH_IE_EP<<10u)|(CANCFG_CH_IE_EW<<9u)|        \
                                            (CANCFG_CH_IE_BUS<<8u))

/* CAN接收缓存buffer数 */
#define CANCFG_RBNUM                        16u

/* CAN数据缓存数量 */
#define CANDATA_NUM                         64u

#define CANTXBUF_REG_LEN                    (3u + CANDATA_NUM/8u)

/* CAN Channel 0 接收规则数量 */
#define CAN_RX_RULES_NUM                    2u

/* 发送相关寄存器 */
#define RSCFD0CFDCmCTR(C)                      (*(volatile uint32 *)(0xFFD00004 + 0x10*((C))))
#define RSCFD0CFDCmSTS(C)                      (*(volatile uint32 *)(0xFFD00008 + 0x10*((C))))
#define RSCFD0CFDCmFDCFG(C)                    (*(volatile uint32 *)(0xFFD00504 + 0x20*((C))))
#define RSCFD0CFDTXQCCm(C)                     (*(volatile uint32 *)(0xFFD003A0 + 0x4*((C))))
#define RSCFD0CFDCmNCFG(C)                     (*(volatile uint32 *)(0xFFD00000 + 0x10*((C))))
#define RSCFD0CFDCmDCFG(C)                     (*(volatile uint32 *)(0xFFD00500 + 0x20*((C))))
#define RSCFD0CFDCFCCk(C, X)                   (*(volatile uint32 *)(0xFFD00118 + 0x4*(((C)*3u)+X)))
#define RCFDC0CFDTMIDp(C, P)                   (*(volatile uint32 *)(0xFFD04000 + 0x20*((P)+(C)*16u)))
#define RCFDC0CFDTMPTRp(C, P)                  (*(volatile uint32 *)(0xFFD04004 + 0x20*((P)+(C)*16u)))
#define RCFDC0CFDTMFDCTRp(C, P)                (*(volatile uint32 *)(0xFFD04008 + 0x20*((P)+(C)*16u)))
#define RCFDC0CFDTMDFb_p(C, B, P)              (*(volatile uint32 *)(0xFFD0400C + 0x04*(B) + 0x20*((P)+(C)*16u)))
#define RCFDC0CFDTMCp(C, P)                    (*(volatile uint32 *)(0xFFD00250 + ((P)+(C)*16u)))
#define RCFDC0CFDTMSTSp(C, P)                  (*(volatile uint32 *)(0xFFD002D0 + ((P)+(C)*16u)))
#define RSCFD0CFDTMIECy(C)                     (*(volatile uint32 *)(0xFFD00390 + (((C)>>1)*4u)))
#define RSCFD0CFDTMTCSTSy(C, P)                ((*(volatile uint32 *)(0xFFD00370 + (((C)>>1)*4u)))>>((((C)&0x1)*16u)+(P)))

#define GetAddress_RCFDC0CFDTMIDp(C, P)        (0xFFD04000 + 0x20*((P)+(C)*16u))

/* 使能CANBuffer发送完成中断(使能CAN通道内所有Buffer中断) */
#define SET_TXBUFFER_INT_ALL_EN(C)             RSCFD0CFDTMIECy((C)) = (0xFFFF << (((C)&0x1)*16u))

/* 打开/关闭发送FIFO缓存 */
#define SET_TX_FIFO_BUFFER_EN(C, TrueOrFalse)  {    \
    RSCFD0CFDCFCCk((C),0) = TrueOrFalse;            \
    RSCFD0CFDCFCCk((C),1) = TrueOrFalse;            \
    RSCFD0CFDCFCCk((C),2) = TrueOrFalse;            \
}

/* CAN接收缓存状态 */
#define CAN_RX_BUF_NULL                     0u
#define CAN_RX_BUF_NOT_NULL                 1u


#define CANFD_RULE_Type canfd_cre_type
/********************************数据类型定义**********************************/

/******************************************************************************/
/**
 *  @brief CAN Frame类型,用于发送
 */
/******************************************************************************/
/* ---- CANFD frame ----- */
typedef union
{
    struct
    {
        uint32 ID :29;
        uint32 THLEN :1;
        uint32 RTR :1;
        uint32 IDE :1;

        uint32 res0:28;
        uint32 DLC :4;

        uint32 ESI :1;
        uint32 BRS :1;
        uint32 FDF :1;
        uint32 res1 :29;

        uint8 DB[CANDATA_NUM];
    }bit;

    uint32 Reg[CANTXBUF_REG_LEN];
}Canfd_FrameType;


typedef union {
    struct
    {
        uint32 GAFLID:29;
        uint32 GAFLLB:1;
        uint32 GAFLRTR:1;
        uint32 GAFLIDE:1;

        uint32 GAFLIDM:29;
        uint32 Reserved1:1;
        uint32 GAFLRTRM:1;
        uint32 GAFLIDEM:1;

        //uint32 GAFLDLC : 4;       /* Receive Rule DLC */
        //uint32 Reserved2 : 4;
        //uint32 GAFLRMDP : 7;      /* Receive Buffer Number Select */
        //uint32 GAFLRMV : 1;       /* Receive Buffer Enable */
        //uint32 GAFLPTR : 16;      /* Receive Rule Label */
        uint32 Reserved2 : 8;     /*Reserved*/
        uint32 GAFLRMDP : 7;      /* Receive Buffer Number Select */  
        uint32 GAFLRMV : 1;       /* Receive Buffer Enable */
        uint32 GAFLPTR : 12;      /* Receive Rule Label */
        uint32 GAFLDLC:4;         /* Receive Rule DLC */

        uint32 GAFLFDPx:8;      /* Receive FIFO Buffer x Select */
        uint32 GAFLFDPk:24;     /* Transmit/Receive FIFO Buffer k Select */
    }bit;
    uint32 u32aRegs[4];
}CANFDRxRuleType;
/*******************************全局变量声明***********************************/
/*******************************全局函数声明***********************************/
extern void CANDrvInit(void);
extern void CANDrvClose(void);
extern void CANDrvReset(void);
extern void CANTransmitFrame(uint8 Lu8TxBufNum, uint8* pData, uint8 Lu8DLC, uint32 Lu32ID);
extern uint8 CANReceiveFrame(uint8* LptrData, uint8 Lu8Dlc, uint8 FrameIdx);
extern uint8 CANGetTMCIdx(void);
extern uint16 CANGetRMCIdx(void);
extern uint8 CANGetRxBufStatus(void);
extern void CAN_BusOffInd(void);
#endif
