"""
测试CRM数据跟踪功能
"""

from command_parser import (
    CommandParserFactory,
    ResponseParserFactory,
    crm_data_tracker
)

def parse_hex_string(hex_string):
    """
    解析十六进制字符串为字节列表
    
    Args:
        hex_string: 十六进制字符串，如"02 50 ff ff ff ff 7a"
        
    Returns:
        字节列表
    """
    # 移除所有空白字符
    hex_string = "".join(hex_string.split())
    
    # 检查字符串长度是否为偶数
    if len(hex_string) % 2 != 0:
        raise ValueError("十六进制字符串长度必须为偶数")
    
    # 将字符串转换为字节列表
    return [int(hex_string[i:i+2], 16) for i in range(0, len(hex_string), 2)]

def test_crm_data_tracker():
    """测试CRM数据跟踪功能"""
    # 重置CRM数据跟踪器
    crm_data_tracker.reset()
    
    # 测试特殊映射地址操作
    command_str1 = "02 03 FF 00 EF FE 0D"
    command_str2 = "02 04 FF C3 0D 00 58"
    command_str3 = "02 05 02 12 00 00 36"
    
    # 解析第一条命令
    command_bytes1 = parse_hex_string(command_str1)
    command_parser1 = CommandParserFactory.create_parser(command_bytes1)
    command_result1 = command_parser1.parse(command_bytes1)
    
    print("命令1解析结果:")
    print(command_result1)
    print("\nCRM数据状态:")
    print(command_result1.get("crm_data_status", {}))
    
    # 解析第二条命令
    command_bytes2 = parse_hex_string(command_str2)
    command_parser2 = CommandParserFactory.create_parser(command_bytes2)
    command_result2 = command_parser2.parse(command_bytes2)
    
    print("\n命令2解析结果:")
    print(command_result2)
    print("\nCRM数据状态:")
    print(command_result2.get("crm_data_status", {}))
    
    # 解析第三条命令
    command_bytes3 = parse_hex_string(command_str3)
    command_parser3 = CommandParserFactory.create_parser(command_bytes3)
    command_result3 = command_parser3.parse(command_bytes3)
    
    print("\n命令3解析结果:")
    print(command_result3)
    print("\nCRM数据状态:")
    print(command_result3.get("crm_data_status", {}))

if __name__ == "__main__":
    test_crm_data_tracker()
