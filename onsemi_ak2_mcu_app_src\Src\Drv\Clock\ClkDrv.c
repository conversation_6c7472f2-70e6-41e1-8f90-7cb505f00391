/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: ClkDrv.c
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/
#include "types.h"
#include "ClkRegCfg.h"
#include "ClkDrv.h"

/*********************************函数声明定义*********************************/
void R_CLKC_PllInit(void);
void R_CLKC_SetRscanClockDomain(uint32 RscanModuleClockDomain, uint32 RscanComClockDomain);
void R_SYSTEM_ClockInit(void);
/*===========================================================================*/
/* Functions */
/*===========================================================================*/
/*****************************************************************************
** Function:    R_CLKC_PllInit
** Description: This function generates 120MHz CPU CLK and 80MHz PeripheralPLL CLK.
** Parameter:   None
** Return:      None
******************************************************************************/
void R_CLKC_PllInit(void)
{ 
    /* Prepare 16MHz MainOsc */
  if((CLKCTLMOSCS&0x04u) != 0x4u)                                       /* Check if MainOsc needs to be started */
  {
   CLKCTLMOSCC=0x06;                                                    /* Set MainOSC gain for 16MHz */
   CLKCTLMOSCST=0x00008000;                                             /* Set MainOSC stabilization time to ~4.1ms */
   protected_write(WPROTRPROTCMD0,WPROTRPROTS0,CLKCTLMOSCE,0x01u);      /* Trigger Enable (protected write) */
   while ((CLKCTLMOSCS&0x04u) != 0x04u);                                /* Wait for active MainOSC */
  }

  if((CLKCTLPLLS&0x04u) != 0x04u)                                       /* Check if PLL needs to be started */
  {
    /* Prepare PLL*/
    CLKCTLPLLC=0x00010B3B;                                              /* 16 MHz MainOSC -> 80MHz PLL */
    protected_write(WPROTRPROTCMD1,WPROTRPROTS1,CLKCTLPLLE,0x01u);      /* Enable PLL */
    while((CLKCTLPLLS&0x04u) != 0x04u){}                                /* Wait for active PLL */
  }
  
  /* CPLLOUT = VCOOUT ?1/4 = 120 MHz (for Premium Device) */
  protected_write(WPROTRPROTCMD1,WPROTRPROTS1,CLKCTLCKSC_CPUCLKD_CTL,0x11);
  while(CLKCTLCKSC_CPUCLKD_ACT!=0x11);  
  
  /* CPLLOUT -> CPU Clock */ 
  protected_write(WPROTRPROTCMD1,WPROTRPROTS1,CLKCTLCKSC_CPUCLKS_CTL,0x03u);
  while(CLKCTLCKSC_CPUCLKS_ACT!=0x03u);
  
  /* PPLLOUT -> PPLLCLK = 80MHz */
  protected_write(WPROTRPROTCMD1,WPROTRPROTS1,CLKCTLCKSC_PPLLCLKS_CTL,0x3);
  while(CLKCTLCKSC_PPLLCLKS_ACT!=0x3);  
}

/*****************************************************************************
** Function:    R_CLKC_SetRscanClockDomain
** Description: Select the RS-CAN Module Clock and the Communication Clock.
** Parameter:   RscanModuleClockDomain - Clock for operation of the CAN module itself.
**              RscanComClockDomain - Clock used for the communication speed setting.
** Return:      None
******************************************************************************/
void R_CLKC_SetRscanClockDomain(uint32 RscanModuleClockDomain, uint32 RscanComClockDomain)
{
  /* Select RS-CAN Module Clock */
  protected_write(WPROTRPROTCMD1, WPROTRPROTS1, CLKCTLCKSC_ICANS_CTL, RscanModuleClockDomain);
  while (CLKCTLCKSC_ICANS_ACT !=RscanModuleClockDomain);

  /* Select RS-CAN Communication Clock */
  protected_write(WPROTRPROTCMD1, WPROTRPROTS1, CLKCTLCKSC_ICANOSCD_CTL, RscanComClockDomain);
  while (CLKCTLCKSC_ICANOSCD_CTL != RscanComClockDomain);
}

/*****************************************************************************
** Function:    R_CLKC_SetAdca0ClockDomain
** Description: Select the ADCA0 Clock domain.
** Parameter:   None
** Return:      None
******************************************************************************/
void R_CLKC_SetAdca0ClockDomain(uint32 Adca0ClockDomain)
{
    protected_write(WPROTRPROTCMD0, WPROTRPROTS0, CLKCTLCKSC_AADCAS_CTL, Adca0ClockDomain);
    while (CLKCTLCKSC_AADCAS_ACT != Adca0ClockDomain);
}

/*****************************************************************************
** Function:    R_CLKC_SetTaujClockDomain
** Description: Select the TAUJ Clock domain.
** Parameter:   None
** Return:      None
******************************************************************************/
void R_CLKC_SetTaujClockDomain(uint32 TaujClockDomain)
{
    protected_write(WPROTRPROTCMD0, WPROTRPROTS0, CLKCTLCKSC_ATAUJS_CTL, TaujClockDomain);
    while (CLKCTLCKSC_ATAUJS_CTL != TaujClockDomain);
}
/******************************************************************************/
/**<pre>
 *函数名称: R_SYSTEM_ClockInit
 *功能描述: 时钟初始化
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void R_SYSTEM_ClockInit(void)
{
  R_CLKC_PllInit();                                    /* CPUCLK = 120MHz, PPLLCLK = 80MHz */
  R_CLKC_SetAdca0ClockDomain(3);    /* ADCA0 Clock = PPLLCLK/2 */
  R_CLKC_SetTaujClockDomain(4);     /* TAUJ Clock = PPLLCLK/2 */

  /*  Module Clock = PPLLCLK, Communication Clock = MainOSC */
  
  R_CLKC_SetRscanClockDomain(_CGC_RSCAN_CLK_SOURCE_PPLLCLK,
                               1);
}
/******************************************************************************/
/**<pre>
 *函数名称: ClkDrvInit
 *功能描述: 时钟初始化
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void ClkDrvInit(void)
{
    R_SYSTEM_ClockInit();
  
}

