/*
 * DSI_Queue.h
 *
 *  Created on: 2021年3月30日
 *      Author: 6000021992
 */

#ifndef DSI_QUEUE_H_
#define DSI_QUEUE_H_

#include "AK2_TYPES.h"

/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/

typedef enum
{
    QueueData_IDLE = 0,
    QueueData_WAIT,
    QueueData_READY,

    QueueData_NULL,
}QueueDataStatus_en;

typedef uint8 (*QuequTimeOutCbk)(void *pData);
typedef struct
{
    uint16 wQueSize;/** @brief 队列长度 */
    uint16 wDataSize;/** @brief 数据长度 */
    uint16 wTimeOutValue;/** @brief 超时时间 */
    
    uint16 WriteIndexCnt;/** @brief 写入索引计数 */
    uint16 ReadIndexCnt;/** @brief 读出索引计数 */
    
    void *pDataBuf;/** @brief 数据保存Buff */
    QueueDataStatus_en *pQueDataStatus;/** @brief 队列数据状态 */
    uint16 *pQueueTimeOutCnt;/** @brief 队列数据超时计数 */
    
    QuequTimeOutCbk QuequTimeOutCbkFunc;
    void *pCbkFuncData;
}Queue_Str;

/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/




/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************************/
/**
 * @brief             初始化队列
 * @param[in]         pQueue            队列地址
 
 * @return            0 OK,else ERR
 */
/******************************************************************************/

uint8 InitQueue(Queue_Str *pQueue);

/******************************************************************************/
/**
 * @brief             队列数据清零
 * @param[in]         pQueue            队列地址
 
 * @return            0 OK,else ERR
 */
/******************************************************************************/

uint8 ClearQueue(Queue_Str *pQueue);

/******************************************************************************/
/**
 * @brief             获取队列空闲数据buf地址
 * @param[in]         pQueue     队列
 * @param[in]         pOutBufNum 输出buf编号

 * @return            NULL 无空闲buf,else 空闲buff地址，
 */
/******************************************************************************/

void * GetQueue_IdleBufAddr(Queue_Str *pQueue,uint8 *pOutBufNum);

/******************************************************************************/
/**
 * @brief             设置队列数据Ready
 * @param[in]         pQueue     
 * @param[in]         QueNum
 
 * @return            0 OK,else ERR
 */
/******************************************************************************/

uint8 SetQueue_Data_Ready(Queue_Str *pQueue,uint16 QueNum);

/******************************************************************************/
/**
 * @brief             写入队列数据，并将数据状态置为Ready
 * @param[in]         pQueue     队列
 * @param[in]         pInData    写入数据
 * @param[in]         InDataLen  写入数据长度
 
 * @return            0xFFFF ERR ，else OK
 */
/******************************************************************************/

uint16 PutQueue_BufData(Queue_Str *pQueue,void *pInData,uint16 InDataLen);

/******************************************************************************/
/**
 * @brief             获取队列数据Ready个数
 * @param[in]         pQueue     队列

 
 * @return            Ready数据个数
 */
/******************************************************************************/

uint16 GetQueue_ReadyCnt(Queue_Str *pQueue);

/******************************************************************************/
/**
 * @brief             写入队列数据，并将数据状态置为Ready
 * @param[in]         pQueue     队列
 * @param[in]         pOutData   输出数据保存地址
 
 * @return            0xFFFF 无Ready数据 ，else OK
 */
/******************************************************************************/

uint16 GetQueue_ReadyData(Queue_Str *pQueue,void *pOutData);

/******************************************************************************/
/**
 * @brief             队列1ms超时监控任务
 * @param[in]         pQueue     队列
 
 * @return             无
 */
/******************************************************************************/

uint8 Queue1msHook(Queue_Str *pQueue);

#endif /* DSI_QUEUE_H_ */

