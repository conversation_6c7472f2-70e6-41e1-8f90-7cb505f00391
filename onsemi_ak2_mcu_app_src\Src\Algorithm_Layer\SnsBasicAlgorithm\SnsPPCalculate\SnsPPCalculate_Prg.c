/******************************************************************************
 * @file       SnsPPCalculate_Prg.c
 * @brief      
 * @date       2025-03-04 14:31:56
 * <AUTHOR>
 * @copyright  Longhorn Automotive Electronics Technology (Xiamen) Co., Ltd.
 *****************************************************************************/


/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "SnsRawData_Int.h"
#include "PublicCalAlgorithm_Int.h"
#include "SnsPPCalculate_Int.h"
#include "Sns_install_Coordinate.h"
#include "MapBuild_Int.h"
#include "CAN_AppSignalManage.h"



/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/



/******************************************************************************
 * @Function Declaration
 *****************************************************************************/



/******************************************************************************
 * @Const Definitions
 *****************************************************************************/



/******************************************************************************
 * @Variable Definitions
 *****************************************************************************/
SnsDisDataType GstrSnsoringinData[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];
SystemObjInfoTypedef GstrSystemObjInfo[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];

PPDis_ZONETye GstrPPZone[PDC_SNS_GROUP_NUM];
SysDisInfoType GstrSysDisInfo[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];



/******************************************************************************
 * @Function Definitions
 *****************************************************************************/


/******************************************************************************
 * @brief      SysDis值初始化
 * @param[in]  LenuSnsGroup 
 * <AUTHOR>
 * @date       2025-03-04 14:34:13
 * @note       
 *****************************************************************************/
void SysDisDataInit(PDCSnsGroupType LenuSnsGroup)
{
    uint8 i;
    for(i = PDC_SNS_CH_FLS_RLS; i < PDC_SNS_CH_NUM; i++)
    {
        GstrSysDisInfo[LenuSnsGroup][i].u8CarDiret = 0xFF;
        GstrSysDisInfo[LenuSnsGroup][i].u8BlindKeepFlag = 0;
        GstrSysDisInfo[LenuSnsGroup][i].u16SysDistance = 0;
        GstrSysDisInfo[LenuSnsGroup][i].u16MoveStaCnt = 0;
        GstrSysDisInfo[LenuSnsGroup][i].u8MoveSta = 0;
        for (uint8 j = 0; j < OBJ_MOVE_TOTAL_STATUS; j++)
        {
            GstrSysDisInfo[LenuSnsGroup][i].u8SysDisUpdateCnt[j] = 0;
        }
    }
}


/******************************************************************************
 * @brief      Snsoringi值初始化
 * @param[in]  LenuSnsGroup 
 * <AUTHOR>
 * @date       2025-03-04 14:34:17
 * @note       
 *****************************************************************************/
void GstrSnsoringinDataInit(PDCSnsGroupType LenuSnsGroup)
{
    uint8 i;
    for(i = PDC_SNS_CH_FLS_RLS; i < PDC_SNS_CH_NUM; i++)
    {
        GstrSnsoringinData[LenuSnsGroup][i].u16MasterDis = SNS_INVALID_DIS;
        GstrSnsoringinData[LenuSnsGroup][i].u16MasterActualDis = SNS_INVALID_DIS;
        GstrSnsoringinData[LenuSnsGroup][i].u16MasterEchoHeight = SNS_INVALID_HEIGHT;

        GstrSnsoringinData[LenuSnsGroup][i].u16LeftListenDis = SNS_INVALID_DIS;
        GstrSnsoringinData[LenuSnsGroup][i].u16LeftLisActualDis = SNS_INVALID_DIS;
        GstrSnsoringinData[LenuSnsGroup][i].u16LeftLisEchoHeight = SNS_INVALID_HEIGHT;

        GstrSnsoringinData[LenuSnsGroup][i].u16RightListenDis = SNS_INVALID_DIS;
        GstrSnsoringinData[LenuSnsGroup][i].u16RightLisActualDis = SNS_INVALID_DIS;
        GstrSnsoringinData[LenuSnsGroup][i].u16RightLisEchoHeight = SNS_INVALID_HEIGHT;
    }
}


/******************************************************************************
 * @brief      SystemObjInf值初始化
 * @param[in]  LenuSnsGroup 
 * <AUTHOR>
 * @date       2025-03-04 14:34:42
 * @note       
 *****************************************************************************/
void SystemObjInfoDataInit(PDCSnsGroupType LenuSnsGroup)
{
    uint8 i;
    for(i = PDC_SNS_CH_FLS_RLS; i < PDC_SNS_CH_NUM; i++)
    {
        GstrSystemObjInfo[LenuSnsGroup][i].fObjTOBumperDis = SNS_INVALID_DIS;
        GstrSystemObjInfo[LenuSnsGroup][i].strObjCarCoord.fObjX = SNS_INVALID_DIS;
        GstrSystemObjInfo[LenuSnsGroup][i].strObjCarCoord.fObjY = SNS_INVALID_DIS;
        GstrSystemObjInfo[LenuSnsGroup][i].strObjSnsCoord.fObjX = SNS_INVALID_DIS;
        GstrSystemObjInfo[LenuSnsGroup][i].strObjSnsCoord.fObjY = SNS_INVALID_DIS;
        GstrSystemObjInfo[LenuSnsGroup][i].u8Area = PP_AREA_NONE;
        GstrSystemObjInfo[LenuSnsGroup][i].u8AreaUpdateCnt = 0;
        GstrSystemObjInfo[LenuSnsGroup][i].u8MasterBurstValidFlag = INVALID;
        GstrSystemObjInfo[LenuSnsGroup][i].u8ObjListenFlag = SENSOR_MASTER_BURST;
        GstrSystemObjInfo[LenuSnsGroup][i].u8SnsCoordValidFlag = INVALID;
    }
}


/******************************************************************************
 * @brief      PPzone值初始化
 * @param[in]  LenuSnsGroup 
 * <AUTHOR>
 * @date       2025-03-04 14:35:11
 * @note       
 *****************************************************************************/
void PP_Zone_DataInit(PDCSnsGroupType LenuSnsGroup)
{
    GstrPPZone[LenuSnsGroup].u16L1_Distance = NO_OBJECT;
    GstrPPZone[LenuSnsGroup].u16L2_Distance = NO_OBJECT;
    GstrPPZone[LenuSnsGroup].u16ML1_Distance = NO_OBJECT;
    GstrPPZone[LenuSnsGroup].u16ML2_Distance = NO_OBJECT;
    GstrPPZone[LenuSnsGroup].u16MR1_Distance = NO_OBJECT;
    GstrPPZone[LenuSnsGroup].u16MR2_Distance = NO_OBJECT;
    GstrPPZone[LenuSnsGroup].u16R1_Distance = NO_OBJECT;
    GstrPPZone[LenuSnsGroup].u16R2_Distance = NO_OBJECT;
}


/******************************************************************************
 * @brief      上电初始化Zone数据
 * <AUTHOR>
 * @date       2025-03-04 14:35:20
 * @note       
 *****************************************************************************/
void PPDataPowerOnInit(void)
{
    PP_Zone_DataInit(PDC_SNS_GROUP_FRONT);
    PP_Zone_DataInit(PDC_SNS_GROUP_REAR);
    SysDisDataInit(PDC_SNS_GROUP_FRONT);
    SysDisDataInit(PDC_SNS_GROUP_REAR);
    GstrSnsoringinDataInit(PDC_SNS_GROUP_FRONT);
    GstrSnsoringinDataInit(PDC_SNS_GROUP_REAR);
    SystemObjInfoDataInit(PDC_SNS_GROUP_FRONT);
    SystemObjInfoDataInit(PDC_SNS_GROUP_REAR);
}


/******************************************************************************
 * @brief      Get the Original Data object
 * @param[in]  LenuSnsGroup 
 * @param[in]  LenuSnsCh 
 * <AUTHOR>
 * @date       2025-03-04 14:35:34
 * @note       
 *****************************************************************************/
void GetOriginalData(PDCSnsGroupType LenuSnsGroup, PDCSnsChannelType LenuSnsCh)
{
    SnsDisDataType * LpSnsDisData;
    SnsSigGroupDisFollowType * LpFollowDis;
    SnsSigGroupDataCacheType * LpSigGroupData;
    LpSnsDisData = &GstrSnsoringinData[LenuSnsGroup][LenuSnsCh];
    LpFollowDis = &GstrSnsSigGroupDisFollow[LenuSnsGroup][LenuSnsCh];
    LpSigGroupData = &GstrSnsSigGroupDataCache[LenuSnsGroup][LenuSnsCh];

    LpSnsDisData->u16MasterDis = LpFollowDis->MasDisData.u16FollowDis;
    LpSnsDisData->u16MasterEchoHeight = LpFollowDis->MasDisData.u16EchoHeightDetect_New;
    LpSnsDisData->u16LeftListenDis = ((LpFollowDis->LeftLisDisData.u16FollowDis * 2) - LpFollowDis->MasDisData.u16FollowDis);
    LpSnsDisData->u16RightListenDis = ((LpFollowDis->RightLisDisData.u16FollowDis * 2) - LpFollowDis->MasDisData.u16FollowDis);
    LpSnsDisData->u16MasterActualDis = LpSigGroupData->MasterBuf[SNS_CYCLE_0].u16ActualDis[0];
    LpSnsDisData->u16LeftLisActualDis = LpSigGroupData->LeftBuf[SNS_CYCLE_0].u16ActualDis[0];
    LpSnsDisData->u16RightLisActualDis = LpSigGroupData->RightBuf[SNS_CYCLE_0].u16ActualDis[0];
    if (ABS(LpSnsDisData->u16LeftListenDis, LpSnsDisData->u16MasterDis) > 300)
    {
        LpSnsDisData->u16LeftListenDis = SNS_INVALID_DIS;
    }
    if (ABS(LpSnsDisData->u16RightListenDis, LpSnsDisData->u16MasterDis) > 300)
    {
        LpSnsDisData->u16RightListenDis = SNS_INVALID_DIS;
    }
#if 0
    /* 边角探头不用侧边探头的侦听 */
    if (LenuSnsCh == PDC_SNS_CH_FL_RL)
    {
        LpSnsDisData->u16LeftListenDis = SNS_INVALID_DIS;
    }
    if (LenuSnsCh == PDC_SNS_CH_FR_RR)
    {
        LpSnsDisData->u16RightListenDis = SNS_INVALID_DIS;
    }
#endif
}


/******************************************************************************
 * @brief      判断侦听标志位
 * @param[in]  LenuSnsGroup --探头分组信息
 * @param[in]  LenuSnsCh --探头通道信息
 * <AUTHOR>
 * @date       2025-03-04 14:03:85
 * @note       
 *****************************************************************************/
void JudgeListenFlag(PDCSnsGroupType LenuSnsGroup, PDCSnsChannelType LenuSnsCh)
{
    uint8 Lu8ListenFlag = 0;
    SnsDisDataType * LpSnsDisData;
    SystemObjInfoTypedef * LpstrSysObjInfo;
    LpstrSysObjInfo = &GstrSystemObjInfo[LenuSnsGroup][LenuSnsCh];
    LpSnsDisData = &GstrSnsoringinData[LenuSnsGroup][LenuSnsCh];

    if (LpSnsDisData->u16LeftListenDis != SNS_INVALID_DIS && LpSnsDisData->u16RightListenDis != SNS_INVALID_DIS)
    {
        if ((ABS(LpSnsDisData->u16LeftListenDis, LpSnsDisData->u16MasterDis) < 300u) && (ABS(LpSnsDisData->u16MasterDis, LpSnsDisData->u16RightListenDis) < 300u)) /* 主发侦听距离30CM以内侦听有效 */
        {
            if (((LpSnsDisData->u16LeftListenDis) - ((uint16)GstrSnsAdjacentDis[LenuSnsGroup].fRadarLeftDis[LenuSnsCh])) <
                ((LpSnsDisData->u16RightListenDis) - ((uint16)GstrSnsAdjacentDis[LenuSnsGroup].fRadarRightDis[LenuSnsCh])))
            {
                Lu8ListenFlag = (uint8)SENSOR_LISTEN_LEFT;
            }
            else
            {
                Lu8ListenFlag = (uint8)SENSOR_LISTEN_RIGHT;
            }
        }
        else if (ABS(LpSnsDisData->u16MasterDis, LpSnsDisData->u16RightListenDis) < 300u) /* 主发侦听距离30CM以内侦听有效 */
        {
            Lu8ListenFlag = (uint8)SENSOR_LISTEN_RIGHT;
        }
        else if (ABS(LpSnsDisData->u16LeftListenDis, LpSnsDisData->u16MasterDis) < 300u) /* 主发侦听距离30CM以内侦听有效 */
        {
            Lu8ListenFlag = (uint8)SENSOR_LISTEN_LEFT;
        }
    }
    else if (LpSnsDisData->u16LeftListenDis != SNS_INVALID_DIS)
    {
        if (LpSnsDisData->u16MasterDis != SNS_INVALID_DIS)
        {
            Lu8ListenFlag = (uint8)SENSOR_LISTEN_LEFT;
        }
        else if (GstrSnsoringinData[LenuSnsGroup][LenuSnsCh - 1].u16RightListenDis != SNS_INVALID_DIS)
        {
            Lu8ListenFlag = (uint8)SENSOR_LISTEN_LEFT;
        }
    }
    else if (LpSnsDisData->u16RightListenDis != SNS_INVALID_DIS)
    {
        if (LpSnsDisData->u16MasterDis != SNS_INVALID_DIS)
        {
            Lu8ListenFlag = (uint8)SENSOR_LISTEN_RIGHT;
        }
        else if (GstrSnsoringinData[LenuSnsGroup][LenuSnsCh + 1].u16LeftListenDis != SNS_INVALID_DIS)
        {
            Lu8ListenFlag = (uint8)SENSOR_LISTEN_RIGHT;
        }
    }

#if 0
    /* 边角探头侦听大于主发不用 */
    if (LenuSnsCh == PDC_SNS_CH_FL_RL)
    {
        if (Lu8ListenFlag == SENSOR_LISTEN_RIGHT)
        {
            if (LpSnsDisData->u16MasterDis < LpSnsDisData->u16RightListenDis)
            {
                Lu8ListenFlag = SENSOR_MASTER_BURST;
            }
        }
    }
    if (LenuSnsCh == PDC_SNS_CH_FR_RR)
    {
        if (Lu8ListenFlag == SENSOR_LISTEN_LEFT)
        {
            if (LpSnsDisData->u16MasterDis < LpSnsDisData->u16LeftListenDis)
            {
                Lu8ListenFlag = SENSOR_MASTER_BURST;
            }
        }
    }
#endif

    LpstrSysObjInfo->u8ObjListenFlag = Lu8ListenFlag;
}


/******************************************************************************
 * @brief      已知三角形三边长，求夹角的余弦值cosθ的值
 * @param[in]  LenuSnsGroup 
 * @param[in]  LenuSnsCh 
 * @param[in]  Lu16SnsDis 
 * @param[in]  Lu16MasterDis 
 * @param[in]  Lu16OriLisDis 
 * @param[out] LfCosAngle --对应角度的cosθ的值
 * @return     
 * <AUTHOR>
 * @date       2025-03-04 14:37:13
 * @note       
 *****************************************************************************/
float CalObjCosAngle(PDCSnsGroupType LenuSnsGroup, PDCSnsChannelType LenuSnsCh, uint16 Lu16SnsDis, uint16 Lu16MasterDis, uint16 Lu16OriLisDis)
{
    float LfCosAngle;
    float LfDisSub = 0.0;
    uint32 Lu32SnsDisSquare;         /* 传感器安装间距平方 */
    uint32 Lu32MasterDisSquare;      /* 传感器主发距离平方 */
    uint32 Lu32OppositeLisDisSquare; /* 传感器侦听距离平方 */

    if ((ABS(Lu16OriLisDis, Lu16MasterDis) < Lu16SnsDis) && ((Lu16OriLisDis + Lu16MasterDis) > Lu16SnsDis))
    {
        Lu32SnsDisSquare = Lu16SnsDis * Lu16SnsDis;
        Lu32MasterDisSquare = Lu16MasterDis * Lu16MasterDis;
        Lu32OppositeLisDisSquare = Lu16OriLisDis * Lu16OriLisDis;
        LfDisSub = Lu32SnsDisSquare + Lu32MasterDisSquare;
        LfDisSub = LfDisSub - Lu32OppositeLisDisSquare;
        LfCosAngle = LfDisSub / (Lu16MasterDis * Lu16SnsDis * 2);
        GstrSystemObjInfo[LenuSnsGroup][LenuSnsCh].u8SnsCoordValidFlag = VALID;
    }
    else
    {
        LfCosAngle = 0;
        GstrSystemObjInfo[LenuSnsGroup][LenuSnsCh].u8SnsCoordValidFlag = INVALID;
    }
    return(LfCosAngle);
}


/******************************************************************************
 * @brief      计算相对于探头的坐标
 * @param[in]  LenuSnsGroup 
 * @param[in]  LenuSnsCh 
 * <AUTHOR>
 * @date       2025-03-04 14:37:36
 * @note       
 *****************************************************************************/
void CalcObjSnsCoord(PDCSnsGroupType LenuSnsGroup, PDCSnsChannelType LenuSnsCh)
{
    uint16 Lu16SnsDis;
    float LfCosA = 0.0, LfSinA, LfObjAngle;
    SnsDisDataType * LpSnsDisData;
    SystemObjInfoTypedef * LpstrSysObjInfo;
    LpstrSysObjInfo = &GstrSystemObjInfo[LenuSnsGroup][LenuSnsCh];
    LpSnsDisData = &GstrSnsoringinData[LenuSnsGroup][LenuSnsCh];
    LpstrSysObjInfo->u8SnsCoordValidFlag = INVALID;
    LpstrSysObjInfo->u8MasterBurstValidFlag = INVALID;

    if (LpstrSysObjInfo->u8ObjListenFlag)
    {
        if (LpstrSysObjInfo->u8ObjListenFlag == SENSOR_LISTEN_LEFT)
        {
            Lu16SnsDis = (uint16)GstrSnsAdjacentDis[LenuSnsGroup].fRadarLeftDis[LenuSnsCh];
            LfCosA = CalObjCosAngle(LenuSnsGroup, LenuSnsCh,
                                    Lu16SnsDis, LpSnsDisData->u16MasterDis, 
                                    LpSnsDisData->u16LeftListenDis);
        }
        else
        {
            Lu16SnsDis = (uint16)GstrSnsAdjacentDis[LenuSnsGroup].fRadarRightDis[LenuSnsCh];
            LfCosA = CalObjCosAngle(LenuSnsGroup, LenuSnsCh,
                                    Lu16SnsDis, LpSnsDisData->u16MasterDis, 
                                    LpSnsDisData->u16RightListenDis);
        }
    }
    else
    {
        LpstrSysObjInfo->u8SnsCoordValidFlag = INVALID;
    }
#if 0
    LfSinA = sqrt(1- LfCosA * LfCosA);
#endif
    if ((LfCosA >= -1.0) && (LfCosA <= 1.0))
    {
        LfObjAngle = acosf(LfCosA);
        LfSinA = sinf(LfObjAngle);
    }
#if 0
    /* 边角探头不适用钝角三角形进行定位 */
    if (LenuSnsCh == PDC_SNS_CH_FL_RL || LenuSnsCh == PDC_SNS_CH_FR_RR)
    {
        if (LfCosA < 0.0)
        {
            LpstrSysObjInfo->u8SnsCoordValidFlag = INVALID;
        }
    }
#endif

    if ((LpSnsDisData->u16MasterDis != SNS_INVALID_DIS) && (LpstrSysObjInfo->u8SnsCoordValidFlag == VALID))
    {
        if (LenuSnsGroup == PDC_SNS_GROUP_FRONT)
        {
            if (LpstrSysObjInfo->u8ObjListenFlag == SENSOR_LISTEN_LEFT)
            {
                LpstrSysObjInfo->strObjSnsCoord.fObjY = LfCosA * LpSnsDisData->u16MasterDis;
                LpstrSysObjInfo->strObjSnsCoord.fObjX = LfSinA * LpSnsDisData->u16MasterDis;
            }
            else if (LpstrSysObjInfo->u8ObjListenFlag == SENSOR_LISTEN_RIGHT)
            {
                LpstrSysObjInfo->strObjSnsCoord.fObjY = -LfCosA * LpSnsDisData->u16MasterDis;
                LpstrSysObjInfo->strObjSnsCoord.fObjX = LfSinA * LpSnsDisData->u16MasterDis;
            }
        }
        else
        {
            if (LpstrSysObjInfo->u8ObjListenFlag == SENSOR_LISTEN_LEFT)
            {
                LpstrSysObjInfo->strObjSnsCoord.fObjY = LfCosA * LpSnsDisData->u16MasterDis;
                LpstrSysObjInfo->strObjSnsCoord.fObjX = -LfSinA * LpSnsDisData->u16MasterDis;
            }
            else if (LpstrSysObjInfo->u8ObjListenFlag == SENSOR_LISTEN_RIGHT)
            {
                LpstrSysObjInfo->strObjSnsCoord.fObjY = -LfCosA * LpSnsDisData->u16MasterDis;
                LpstrSysObjInfo->strObjSnsCoord.fObjX = -LfSinA * LpSnsDisData->u16MasterDis;
            }
        }
        /* 对侦听进行限制 */

    }
    /* 单主发策略 相邻探头有距离则单主发限制在85cm否则限制在65cm */
    else if ((LpSnsDisData->u16MasterDis != SNS_INVALID_DIS) && (LpstrSysObjInfo->u8SnsCoordValidFlag == INVALID) && (LpSnsDisData->u16MasterDis < NO_LISTEN_DISTANCE))
#if 0
    {
        if (LenuSnsCh == PDC_SNS_CH_FML_RML || LenuSnsCh == PDC_SNS_CH_FMR_RMR)
        {
            if (GstrSnsoringinData[LenuSnsGroup][LenuSnsCh + 1].u16MasterDis < ADJACENT_SNS_DISTANCE || GstrSnsoringinData[LenuSnsGroup][LenuSnsCh - 1].u16MasterDis < ADJACENT_SNS_DISTANCE)
            {
                LpstrSysObjInfo->u8MasterBurstValidFlag = VALID;
            }
            else
            {
                if (LpSnsDisData->u16MasterDis < NO_LISTEN_DISTANCE_ONLY)
                {
                    LpstrSysObjInfo->u8MasterBurstValidFlag = VALID;
                }
                else
                {
                    LpstrSysObjInfo->u8MasterBurstValidFlag = INVALID;
                }
            }
        }
        else if (LenuSnsCh == PDC_SNS_CH_FL_RL)
        {
            if (GstrSnsoringinData[LenuSnsGroup][LenuSnsCh + 1].u16MasterDis < ADJACENT_SNS_DISTANCE)
            {
                LpstrSysObjInfo->u8MasterBurstValidFlag = VALID;
            }
            else
            {
                if (LpSnsDisData->u16MasterDis < NO_LISTEN_DISTANCE_ONLY)
                {
                    LpstrSysObjInfo->u8MasterBurstValidFlag = VALID;
                }
                else
                {
                    LpstrSysObjInfo->u8MasterBurstValidFlag = INVALID;
                }
            }
        }
        else
        {
            if (GstrSnsoringinData[LenuSnsGroup][LenuSnsCh - 1].u16MasterDis < ADJACENT_SNS_DISTANCE)
            {
                LpstrSysObjInfo->u8MasterBurstValidFlag = VALID;
            }
            else
            {
                if (LpSnsDisData->u16MasterDis < NO_LISTEN_DISTANCE_ONLY)
                {
                    LpstrSysObjInfo->u8MasterBurstValidFlag = VALID;
                }
                else
                {
                    LpstrSysObjInfo->u8MasterBurstValidFlag = INVALID;
                }
            }
        }

        if (LpstrSysObjInfo->u8MasterBurstValidFlag == VALID)
        {
            if (LenuSnsGroup == PDC_SNS_GROUP_FRONT)
            {
                LpstrSysObjInfo->strObjSnsCoord.fObjX = LpSnsDisData->u16MasterDis;
            }
            else
            {
                LpstrSysObjInfo->strObjSnsCoord.fObjX = -LpSnsDisData->u16MasterDis;
            }
            LpstrSysObjInfo->strObjSnsCoord.fObjY = 0;
        }
        else
        {
            LpstrSysObjInfo->strObjSnsCoord.fObjY = SNS_INVALID_DIS;
            LpstrSysObjInfo->strObjSnsCoord.fObjX = SNS_INVALID_DIS;
        }
    }
#else
    {
        if (LenuSnsGroup == PDC_SNS_GROUP_FRONT)
        {
            /* 争对中间探头盲区做特殊处理 无侦听情况下报警距离比实际距离差别大 */
            if ((LenuSnsCh == PDC_SNS_CH_FML_RML || LenuSnsCh == PDC_SNS_CH_FMR_RMR) && (LpSnsDisData->u16MasterDis < BLIND_NO_LISTEN_DIS))
            {
                LpstrSysObjInfo->strObjSnsCoord.fObjX = LpSnsDisData->u16MasterDis * 0.707;
            }
            else
            {
                /* 中间探头不限制侦听以免尖角墙不能识别 */
                LpstrSysObjInfo->strObjSnsCoord.fObjX = LpSnsDisData->u16MasterDis;
            }
        }
        else
        {
            if ((LenuSnsCh == PDC_SNS_CH_FML_RML || LenuSnsCh == PDC_SNS_CH_FMR_RMR) && (LpSnsDisData->u16MasterDis < BLIND_NO_LISTEN_DIS))
            {
                LpstrSysObjInfo->strObjSnsCoord.fObjX = -LpSnsDisData->u16MasterDis * 0.707;
            }
            else
            {
                LpstrSysObjInfo->strObjSnsCoord.fObjX = -LpSnsDisData->u16MasterDis;
            }
        }
        LpstrSysObjInfo->strObjSnsCoord.fObjY = 0;
        LpstrSysObjInfo->u8MasterBurstValidFlag = VALID;
    }
#endif
    /* 无主发距离无效 */
    else
    {
        LpstrSysObjInfo->strObjSnsCoord.fObjY = SNS_INVALID_DIS;
        LpstrSysObjInfo->strObjSnsCoord.fObjX = SNS_INVALID_DIS;
    }
}


/******************************************************************************
 * @brief      将障碍物相对于探头的坐标系转化为车身坐标系
 * @param[in]  LenuSnsGroup 
 * @param[in]  LenuSnsCh 
 * <AUTHOR>
 * @date       2025-03-04 14:37:54
 * @note       
 *****************************************************************************/
void SnsCoorConvertToCarCoor(PDCSnsGroupType LenuSnsGroup, PDCSnsChannelType LenuSnsCh)
{
    SystemObjInfoTypedef * LpstrSysObjInfo;
    LpstrSysObjInfo = &GstrSystemObjInfo[LenuSnsGroup][LenuSnsCh];

#if 1
    if (LpstrSysObjInfo->u8ObjListenFlag == SENSOR_LISTEN_LEFT)
    {
        PubAI_UpdateSnsInCarCoor(GstrRamSnsCoor[LenuSnsGroup][LenuSnsCh].cRadarX, GstrRamSnsCoor[LenuSnsGroup][LenuSnsCh].cRadarY,
                                 GstrSnsCoorConvAngle[LenuSnsGroup].fLeftSinA[LenuSnsCh], GstrSnsCoorConvAngle[LenuSnsGroup].fLeftCosA[LenuSnsCh]);
    }
    else if (LpstrSysObjInfo->u8ObjListenFlag == SENSOR_LISTEN_RIGHT)
    {
        PubAI_UpdateSnsInCarCoor(GstrRamSnsCoor[LenuSnsGroup][LenuSnsCh].cRadarX, GstrRamSnsCoor[LenuSnsGroup][LenuSnsCh].cRadarY,
                                 GstrSnsCoorConvAngle[LenuSnsGroup].fRightSinA[LenuSnsCh], GstrSnsCoorConvAngle[LenuSnsGroup].fRightCosA[LenuSnsCh]);
    }
    else
    {
        PubAI_UpdateSnsInCarCoor(GstrRamSnsCoor[LenuSnsGroup][LenuSnsCh].cRadarX,GstrRamSnsCoor[LenuSnsGroup][LenuSnsCh].cRadarY,
                                 GstrSnsOutAngle[LenuSnsGroup].fSinA[LenuSnsCh],GstrSnsOutAngle[LenuSnsGroup].fCosA[LenuSnsCh]);
    }
#else
    PubAI_UpdateSnsInCarCoor(GstrRamSnsCoor[LenuSnsGroup][LenuSnsCh].cRadarX, GstrRamSnsCoor[LenuSnsGroup][LenuSnsCh].cRadarY,
                                GstrSnsCoorConvAngle[LenuSnsGroup].fLeftSinA[LenuSnsCh], GstrSnsCoorConvAngle[LenuSnsGroup].fLeftCosA[LenuSnsCh]);
#endif
    PubAI_TransObjSnsCoorToCarCoor(&LpstrSysObjInfo->strObjSnsCoord.fObjX, &LpstrSysObjInfo->strObjSnsCoord.fObjY,
                                   &LpstrSysObjInfo->strObjCarCoord.fObjX, &LpstrSysObjInfo->strObjCarCoord.fObjY);
}


/******************************************************************************
 * @brief      判断坐标点分区位置
 * @param[in]  LenuSnsGroup 
 * @param[in]  LfObjX 
 * @param[in]  LfObjY 
 * @return     
 * <AUTHOR>
 * @date       2025-03-04 14:38:17
 * @note       
 *****************************************************************************/
ObjAreaType JudgeObjArea(PDCSnsGroupType LenuSnsGroup,float LfObjX,float LfObjY)
{
    ObjAreaType LenuObjArea = PP_AREA_NONE;
    Pub_Point2LineType LenuPoint2LineRelation;

    ObjPPAreaLineType LenuStartLine;
    ObjPPAreaLineType LenuEndLine;
    ObjPPAreaLineType LenuLineInx;
    float P1_X;
    float P1_Y;
    float P2_X;
    float P2_Y;
    
    if(LfObjY > 0)
    {   
        /* 障碍物在保杠的左侧，从中间往左边找 */
        LenuStartLine = PP_LINE2_3;
        LenuEndLine = PP_LINE0_1;
        for(LenuLineInx = PP_LINE2_3; LenuLineInx >= PP_LINE0_1; LenuLineInx--)
        {
            if(LenuSnsGroup == PDC_SNS_GROUP_FRONT)
            {
                /* 此处左右的相对视角是以P1点指向P2点来看的 */
                P2_X = GstrPDCZoneCoor_Ram[LenuSnsGroup][LenuLineInx].fEndX;
                P2_Y = GstrPDCZoneCoor_Ram[LenuSnsGroup][LenuLineInx].fEndY;
                P1_X = GstrPDCZoneCoor_Ram[LenuSnsGroup][LenuLineInx].fStartX;
                P1_Y = GstrPDCZoneCoor_Ram[LenuSnsGroup][LenuLineInx].fStartY;
            }
            else
            {
                /* 此处左右的相对视角是以P1点指向P2点来看的 */
                P1_X = GstrPDCZoneCoor_Ram[LenuSnsGroup][LenuLineInx].fEndX;
                P1_Y = GstrPDCZoneCoor_Ram[LenuSnsGroup][LenuLineInx].fEndY;
                P2_X = GstrPDCZoneCoor_Ram[LenuSnsGroup][LenuLineInx].fStartX;
                P2_Y = GstrPDCZoneCoor_Ram[LenuSnsGroup][LenuLineInx].fStartY;
            }
            
            LenuPoint2LineRelation = PubAI_CalPoint2LineRelation(P1_X,P1_Y,P2_X,P2_Y,LfObjX,LfObjY);
#if 0
            printf("P1_X:%.3f,P1_Y:%.3f,P2_X:%.3f,P2_Y:%.3f,LfObjX:%.3f,LfObjY:%.3f,Point2LineRelation:%d\r\n",P1_X,P1_Y,P2_X,P2_Y,LfObjX,LfObjY,LenuPoint2LineRelation);
#endif

            if(LenuLineInx == LenuEndLine)
            {
                if((LenuPoint2LineRelation == PUB_POINT_ON_LINE_RIGHT)||(LenuPoint2LineRelation == PUB_POINT_ON_LINE))
                {
                    return PP_AREA1;
                }
                else
                {
                    return PP_AREA_NONE;
                }
            }
            else
            {
                if((LenuPoint2LineRelation == PUB_POINT_ON_LINE_RIGHT)||(LenuPoint2LineRelation == PUB_POINT_ON_LINE))
                {
                    LenuObjArea = (ObjAreaType)(LenuLineInx);
                    return LenuObjArea;
                }
            }
        }
    }
    else
    {
        /* 障碍物在保杠的右侧，从中间往右边找 */
        LenuStartLine = PP_LINE2_3;
        LenuEndLine = PP_LINE4_5;
        
        for(LenuLineInx = PP_LINE2_3; LenuLineInx <= PP_LINE4_5; LenuLineInx++)
        {
            if(LenuSnsGroup == PDC_SNS_GROUP_FRONT)
            {
                /* 此处左右的相对视角是以P1点指向P2点来看的 */
                P2_X = GstrPDCZoneCoor_Ram[LenuSnsGroup][LenuLineInx].fEndX;
                P2_Y = GstrPDCZoneCoor_Ram[LenuSnsGroup][LenuLineInx].fEndY;
                P1_X = GstrPDCZoneCoor_Ram[LenuSnsGroup][LenuLineInx].fStartX;
                P1_Y = GstrPDCZoneCoor_Ram[LenuSnsGroup][LenuLineInx].fStartY;
            }
            else
            {
                /* 此处左右的相对视角是以P1点指向P2点来看的 */
                P1_X = GstrPDCZoneCoor_Ram[LenuSnsGroup][LenuLineInx].fEndX;
                P1_Y = GstrPDCZoneCoor_Ram[LenuSnsGroup][LenuLineInx].fEndY;
                P2_X = GstrPDCZoneCoor_Ram[LenuSnsGroup][LenuLineInx].fStartX;
                P2_Y = GstrPDCZoneCoor_Ram[LenuSnsGroup][LenuLineInx].fStartY;
            }

            LenuPoint2LineRelation = PubAI_CalPoint2LineRelation(P1_X,P1_Y,P2_X,P2_Y,LfObjX,LfObjY);

            if(LenuLineInx == LenuEndLine)
            {
                if(LenuPoint2LineRelation < PUB_POINT_ON_LINE_RIGHT)
                {
                    return PP_AREA4;
                }
                else
                {
                    return PP_AREA_NONE;
                }
            }
            else
            {
                if(LenuPoint2LineRelation < PUB_POINT_ON_LINE_RIGHT)
                {
                    LenuObjArea = (ObjAreaType)(LenuLineInx - 1);
                    return LenuObjArea;
                }
            }
        }
    }

    return LenuObjArea;
}


/******************************************************************************
 * @brief      计算障碍物到保杠的距离及分区
 * @param[in]  LenuSnsGroup 
 * @param[in]  LenuSnsCh 
 * <AUTHOR>
 * @date       2025-03-04 14:38:42
 * @note       
 *****************************************************************************/
void DtmZoneAndDtmToBumperDis(PDCSnsGroupType LenuSnsGroup, PDCSnsChannelType LenuSnsCh)
{
    uint8 Lu8CurZone;
    uint8 Lu8PreZone;
    float LfBumperCompensate = 0.0;
    VehicleTypeType LpenuVEH_Type;
    ObjCoorType LstrBumperStartPoint;
    ObjCoorType LstrBumperEndPoint;
    ObjCoorType LstrBumperPoint;
    SystemObjInfoTypedef * LpstrSysObjInfo;
    LpstrSysObjInfo = &GstrSystemObjInfo[LenuSnsGroup][LenuSnsCh];
    
    ReadCAN_AppSignal_VEH_Type(&LpenuVEH_Type);

    Lu8PreZone = LpstrSysObjInfo->u8Area;
    if (LpstrSysObjInfo->u8SnsCoordValidFlag == VALID)
    {
        if(LpstrSysObjInfo->strObjCarCoord.fObjX > (GstrPDCZoneCoor_Ram[PDC_SNS_GROUP_FRONT][ZONE_BORDER_1].fStartX))
        {
            Lu8CurZone = JudgeObjArea(LenuSnsGroup,LpstrSysObjInfo->strObjCarCoord.fObjX ,LpstrSysObjInfo->strObjCarCoord.fObjY);
            
        }
        else if (LpstrSysObjInfo->strObjCarCoord.fObjX < (GstrPDCZoneCoor_Ram[PDC_SNS_GROUP_REAR][ZONE_BORDER_1].fStartX))
        {
            Lu8CurZone = JudgeObjArea(LenuSnsGroup,LpstrSysObjInfo->strObjCarCoord.fObjX ,LpstrSysObjInfo->strObjCarCoord.fObjY);

        }
        else
        {
            Lu8CurZone = PP_AREA_NONE;
        }

        /* 中间区域分区回滞5cm */
        if (ABS_VALUE(LpstrSysObjInfo->strObjCarCoord.fObjY) <= MID_HYSTERESIS_DIS)
        {
            Lu8CurZone = LenuSnsCh - 1;
        }

        if (LpstrSysObjInfo->fObjTOBumperDis == SNS_INVALID_DIS)
        {
            LpstrSysObjInfo->u8Area = Lu8CurZone;
        }
        else
        {
            if (Lu8CurZone != Lu8PreZone)
            {
                LpstrSysObjInfo->u8AreaUpdateCnt++;
                if (LpstrSysObjInfo->u8AreaUpdateCnt > AREA_UPDATE_CNT)
                {
                    LpstrSysObjInfo->u8Area = Lu8CurZone;
                    LpstrSysObjInfo->u8AreaUpdateCnt = 0;
                }
                else
                {
                    LpstrSysObjInfo->u8Area = Lu8PreZone;
                }
            }
            else
            {
                LpstrSysObjInfo->u8Area = Lu8CurZone;
                LpstrSysObjInfo->u8AreaUpdateCnt = 0;
            }
        }

        if (LpstrSysObjInfo->u8Area == LenuSnsCh - 1)
        {
            if (LenuSnsGroup == PDC_SNS_GROUP_FRONT)
            {
                if(ABS_VALUE(LpstrSysObjInfo->strObjCarCoord.fObjY) < PLATE_WIDTH)
                {
                    LpstrSysObjInfo->fObjTOBumperDis = ABS_VALUE(LpstrSysObjInfo->strObjSnsCoord.fObjX) - PLATE_COMPENSATE;
                }
                else if (ABS_VALUE(LpstrSysObjInfo->strObjCarCoord.fObjY) < BUMPER_LEFT_Y)
                {
                    if (LenuSnsCh == PDC_SNS_CH_FL || LenuSnsCh == PDC_SNS_CH_FML)
                    {
                        LstrBumperStartPoint.fObjX = GstrRamSnsCoor[LenuSnsGroup][PDC_SNS_CH_FML].cRadarX;
                        LstrBumperStartPoint.fObjY = PLATE_WIDTH;
                        LstrBumperEndPoint.fObjX = GstrRamSnsCoor[LenuSnsGroup][PDC_SNS_CH_FML].cRadarX - 50.0;
                        LstrBumperEndPoint.fObjY = BUMPER_LEFT_Y;
                    }
                    else if (LenuSnsCh == PDC_SNS_CH_FMR || LenuSnsCh == PDC_SNS_CH_FR)
                    {
                        LstrBumperStartPoint.fObjX = GstrRamSnsCoor[LenuSnsGroup][PDC_SNS_CH_FMR].cRadarX;
                        LstrBumperStartPoint.fObjY = -PLATE_WIDTH;
                        LstrBumperEndPoint.fObjX = GstrRamSnsCoor[LenuSnsGroup][PDC_SNS_CH_FMR].cRadarX - 50.0;
                        LstrBumperEndPoint.fObjY = -BUMPER_LEFT_Y;
                    }
                    LpstrSysObjInfo->fObjTOBumperDis = PubAI_CalOnePointToSegmentDis(LpstrSysObjInfo->strObjCarCoord.fObjX,LpstrSysObjInfo->strObjCarCoord.fObjY,
                                                                                     LstrBumperStartPoint.fObjX,LstrBumperStartPoint.fObjY,LstrBumperEndPoint.fObjX,LstrBumperEndPoint.fObjY);
                }
                else if(ABS_VALUE(LpstrSysObjInfo->strObjCarCoord.fObjY) < CAR_WIDTH)
                {
                    if (LenuSnsCh == PDC_SNS_CH_FL || LenuSnsCh == PDC_SNS_CH_FML)
                    {
                        LstrBumperPoint.fObjX = GstrRamSnsCoor[LenuSnsGroup][PDC_SNS_CH_FML].cRadarX -50.0;
                        LstrBumperPoint.fObjY = BUMPER_LEFT_Y;
                    }
                    else if (LenuSnsCh == PDC_SNS_CH_FMR || LenuSnsCh == PDC_SNS_CH_FR)
                    {
                        LstrBumperPoint.fObjX = GstrRamSnsCoor[LenuSnsGroup][PDC_SNS_CH_FMR].cRadarX - 50.0;
                        LstrBumperPoint.fObjY = BUMPER_RINHT_Y;
                    }
                    
                    LpstrSysObjInfo->fObjTOBumperDis = PubAI_CalTwoPointDis(LpstrSysObjInfo->strObjCarCoord.fObjX, LpstrSysObjInfo->strObjCarCoord.fObjY,
                                                                            LstrBumperPoint.fObjX, LstrBumperPoint.fObjY);
                }
                else
                {
                    if ((LpstrSysObjInfo->u8Area == PP_AREA2) || (LpstrSysObjInfo->u8Area == PP_AREA3))
                    {
                        if (LenuSnsCh == PDC_SNS_CH_FL || LenuSnsCh == PDC_SNS_CH_FML)
                        {
                            LstrBumperPoint.fObjX = GstrRamSnsCoor[LenuSnsGroup][PDC_SNS_CH_FML].cRadarX -50.0;
                            LstrBumperPoint.fObjY = BUMPER_LEFT_Y;
                        }
                        else if (LenuSnsCh == PDC_SNS_CH_FMR || LenuSnsCh == PDC_SNS_CH_FR)
                        {
                            LstrBumperPoint.fObjX = GstrRamSnsCoor[LenuSnsGroup][PDC_SNS_CH_FMR].cRadarX - 50.0;
                            LstrBumperPoint.fObjY = BUMPER_RINHT_Y;
                        }
                        
                        LpstrSysObjInfo->fObjTOBumperDis = PubAI_CalTwoPointDis(LpstrSysObjInfo->strObjCarCoord.fObjX, LpstrSysObjInfo->strObjCarCoord.fObjY,
                                                                                LstrBumperPoint.fObjX, LstrBumperPoint.fObjY);
                    }
                    else
                    {
#if 1
                        if (LenuSnsCh == PDC_SNS_CH_FL || LenuSnsCh == PDC_SNS_CH_FML)
                        {
                            LstrBumperStartPoint.fObjX = GstrRamSnsCoor[LenuSnsGroup][PDC_SNS_CH_FML].cRadarX -50.0;
                            LstrBumperStartPoint.fObjY = BUMPER_LEFT_Y;
                            LstrBumperEndPoint.fObjX =  GstrRamSnsCoor[LenuSnsGroup][PDC_SNS_CH_FLS].cRadarX + 150.0;
                            LstrBumperEndPoint.fObjY = GstrRamSnsCoor[LenuSnsGroup][PDC_SNS_CH_FLS].cRadarY -50.0;
                        }
                        else if (LenuSnsCh == PDC_SNS_CH_FMR || LenuSnsCh == PDC_SNS_CH_FR)
                        {
                            LstrBumperStartPoint.fObjX = GstrRamSnsCoor[LenuSnsGroup][PDC_SNS_CH_FMR].cRadarX - 50.0;
                            LstrBumperStartPoint.fObjY = BUMPER_RINHT_Y;
                            LstrBumperEndPoint.fObjX = GstrRamSnsCoor[LenuSnsGroup][PDC_SNS_CH_FRS].cRadarX + 150.0;
                            LstrBumperEndPoint.fObjY = GstrRamSnsCoor[LenuSnsGroup][PDC_SNS_CH_FRS].cRadarY -50.0;
                        }
                        LpstrSysObjInfo->fObjTOBumperDis = PubAI_CalOnePointToSegmentDis(LpstrSysObjInfo->strObjCarCoord.fObjX,LpstrSysObjInfo->strObjCarCoord.fObjY,
                                                                                        LstrBumperStartPoint.fObjX,LstrBumperStartPoint.fObjY,LstrBumperEndPoint.fObjX,LstrBumperEndPoint.fObjY);
#endif
#if 0
#if 0
                        if (LenuSnsCh == PDC_SNS_CH_FL || LenuSnsCh == PDC_SNS_CH_FML)
                        {
                            LstrBumperPoint.fObjX = GstrRamSnsCoor[LenuSnsGroup][PDC_SNS_CH_FLS].cRadarX + 150.0;
                            LstrBumperPoint.fObjY = GstrRamSnsCoor[LenuSnsGroup][PDC_SNS_CH_FLS].cRadarY;
                        }
                        else if (LenuSnsCh == PDC_SNS_CH_FMR || LenuSnsCh == PDC_SNS_CH_FR)
                        {
                            LstrBumperPoint.fObjX = GstrRamSnsCoor[LenuSnsGroup][PDC_SNS_CH_FRS].cRadarX + 150.0;
                            LstrBumperPoint.fObjY = GstrRamSnsCoor[LenuSnsGroup][PDC_SNS_CH_FRS].cRadarY;
                        }
                        LpstrSysObjInfo->fObjTOBumperDis = PubAI_CalTwoPointDis(LpstrSysObjInfo->strObjCarCoord.fObjX, LpstrSysObjInfo->strObjCarCoord.fObjY,
                                                                                LstrBumperPoint.fObjX, LstrBumperPoint.fObjY);
#else
                        LpstrSysObjInfo->fObjTOBumperDis = ABS_VALUE(LpstrSysObjInfo->strObjSnsCoord.fObjX);
#endif
#endif
                    }
                }
            }
            else
            {
                if(ABS_VALUE(LpstrSysObjInfo->strObjCarCoord.fObjY) < CAR_WIDTH)
                {
                    LpstrSysObjInfo->fObjTOBumperDis = ABS_VALUE(LpstrSysObjInfo->strObjSnsCoord.fObjX);
                }
                else
                {
                    if (LpenuVEH_Type <= VEHTYPE_X04)
                    {
                        if (LenuSnsCh == PDC_SNS_CH_RL || LenuSnsCh == PDC_SNS_CH_RML)
                        {
                            LstrBumperPoint.fObjX = GstrRamSnsCoor[LenuSnsGroup][PDC_SNS_CH_RL].cRadarX + 150.0;
                            LstrBumperPoint.fObjY = GstrRamSnsCoor[LenuSnsGroup][PDC_SNS_CH_RLS].cRadarY;
                        }
                        else if (LenuSnsCh == PDC_SNS_CH_RMR || LenuSnsCh == PDC_SNS_CH_RR)
                        {
                            LstrBumperPoint.fObjX = GstrRamSnsCoor[LenuSnsGroup][PDC_SNS_CH_RR].cRadarX + 150.0;
                            LstrBumperPoint.fObjY = GstrRamSnsCoor[LenuSnsGroup][PDC_SNS_CH_RRS].cRadarY;
                        }
                    }
                    /* W平台 */
                    else
                    {
                        if (LenuSnsCh == PDC_SNS_CH_RL || LenuSnsCh == PDC_SNS_CH_RML)
                        {
                            LstrBumperPoint.fObjX = GstrRamSnsCoor[LenuSnsGroup][PDC_SNS_CH_RL].cRadarX + 70.0;
                            LstrBumperPoint.fObjY = GstrRamSnsCoor[LenuSnsGroup][PDC_SNS_CH_RLS].cRadarY;
                        }
                        else if (LenuSnsCh == PDC_SNS_CH_RMR || LenuSnsCh == PDC_SNS_CH_RR)
                        {
                            LstrBumperPoint.fObjX = GstrRamSnsCoor[LenuSnsGroup][PDC_SNS_CH_RR].cRadarX + 70.0;
                            LstrBumperPoint.fObjY = GstrRamSnsCoor[LenuSnsGroup][PDC_SNS_CH_RRS].cRadarY;
                        }
                    }
                    LpstrSysObjInfo->fObjTOBumperDis = PubAI_CalTwoPointDis(LpstrSysObjInfo->strObjCarCoord.fObjX, LpstrSysObjInfo->strObjCarCoord.fObjY,
                                                                            LstrBumperPoint.fObjX, LstrBumperPoint.fObjY);
                }
            }
        }
        else
        {
            LpstrSysObjInfo->fObjTOBumperDis = SNS_INVALID_DIS;
        }
    }
    else if (LpstrSysObjInfo->u8MasterBurstValidFlag == VALID)
    {
        LpstrSysObjInfo->fObjTOBumperDis = ABS_VALUE(LpstrSysObjInfo->strObjSnsCoord.fObjX);
    }
    else
    {
        LpstrSysObjInfo->fObjTOBumperDis = SNS_INVALID_DIS;
    }
}


/******************************************************************************
 * @brief      更新探头距离
 * @param[in]  LenuSnsGroup 
 * @param[in]  LenuSnsCh 
 * @param[in]  Lu16Distance 
 * <AUTHOR>
 * @date       2025-03-04 14:38:58
 * @note       
 *****************************************************************************/
void UpdateSnsDistance(PDCSnsGroupType LenuSnsGroup, PDCSnsChannelType LenuSnsCh, uint16 Lu16Distance)
{
    SnsDisDataType * LpSnsDisData;
    SysDisInfoType * LpstrSysDisInfo;
    PDCSnsCarMovStsType *LpstrCarMoveSts;
    SystemObjInfoTypedef * LpstrSysObjInfo;
    SnsSigGroupDisFollowType * LpstrSnsSigGroupDisFollow;
    LpSnsDisData = &GstrSnsoringinData[LenuSnsGroup][LenuSnsCh];
    LpstrSysDisInfo = &GstrSysDisInfo[LenuSnsGroup][LenuSnsCh];
    LpstrSysObjInfo = &GstrSystemObjInfo[LenuSnsGroup][LenuSnsCh];
    LpstrCarMoveSts = &GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh];
    LpstrSnsSigGroupDisFollow = &GstrSnsSigGroupDisFollow[LenuSnsGroup][LenuSnsCh];

    if (Lu16Distance == SNS_INVALID_DIS || Lu16Distance == 0x00 || Lu16Distance > WARNING_MAX_DIS)
    {
        LpstrSysDisInfo->u8MoveSta = 1;
        LpstrSysDisInfo->u16MoveStaCnt = 0;
        LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_FAR] = 0;
        LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_NEAR] = 0;
        LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_KEEP] = 0;
        /* 两个角探头都有数据退出时间长一点保证窄道起环视能起来 */
        if (((LenuSnsGroup == PDC_SNS_GROUP_FRONT) && (LenuSnsCh == PDC_SNS_CH_FL_RL)) || ((LenuSnsCh == PDC_SNS_CH_FR_RR) && (LenuSnsGroup == PDC_SNS_GROUP_FRONT)))
        {
            if (GstrSnsCarMovSts[LenuSnsGroup][LenuSnsCh].eCarDir == SNS_CAR_FORWARD)
            {
                if ((GstrSysDisInfo[LenuSnsGroup][PDC_SNS_CH_FL_RL].u16SysDistance < 1500) && (GstrSysDisInfo[LenuSnsGroup][PDC_SNS_CH_FR_RR].u16SysDistance < 1500))
                {
                    if (LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_FADE]++ > OBJ_CORNER_FADE_CNT)
                    {
                        LpstrSysDisInfo->u16SysDistance = SNS_INVALID_DIS;
                        LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_FADE] = 0;
                    }
                }
                else
                {
                    LpstrSysDisInfo->u16SysDistance = SNS_INVALID_DIS;
                }
            }
            else
            {
                LpstrSysDisInfo->u16SysDistance = SNS_INVALID_DIS;
            }
        }
#if 0
        /* 稳定前保中间探头锥桶探测不稳定 */
        else if (((LenuSnsGroup == PDC_SNS_GROUP_FRONT) && (LenuSnsCh == PDC_SNS_CH_FML_RML)) || ((LenuSnsCh == PDC_SNS_CH_FMR_RMR) && (LenuSnsGroup == PDC_SNS_GROUP_FRONT)))
        {
            if (LpstrSysDisInfo->u16SysDistance < 500)
            {
#if 0
                if (ABS(Gu16FrontArea4MapToBumperDis, LpstrSysDisInfo->u16SysDistance) > 150)
                {
                    LpstrSysDisInfo->u16SysDistance  = SNS_INVALID_DIS;
                }
                else
                {
                    
                }
#else
                /* 平滑处理避免跳动过大 */
                if (ABS(LpstrSysDisInfo->u16SysDistance,Gu16FrontArea4MapToBumperDis) > 50)
                {
                    LpstrSysDisInfo->u16SysDistance = (Gu16FrontArea4MapToBumperDis + LpstrSysDisInfo->u16SysDistance) >> 1;
                }
                else
                {
                    LpstrSysDisInfo->u16SysDistance = Gu16FrontArea4MapToBumperDis;
                }
#endif
            }
            else
            {
                LpstrSysDisInfo->u16SysDistance = SNS_INVALID_DIS;
            }
        }
#else
        /* 争对中间盲区保持长鸣 */
        else if ((LpstrSysDisInfo->u16SysDistance < MIN_WARNING) && (LpstrSnsSigGroupDisFollow->MasDisData.u8KeepBeepFlag))
        {
            LpstrSysDisInfo->u8BlindKeepFlag = 1;
            /* 第一次进入 */
            if (LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_FADE] == 0)
            {
                LpstrSysDisInfo->u8CarDiret = LpstrCarMoveSts->eCarDir;
            }
            
            if (LpstrCarMoveSts->eCarDir == SNS_CAR_STOP)
            {
                LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_FADE]++;
                if (LpSnsDisData->u16MasterActualDis < BLIND_KEEP_DIS)
                {
                    LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_FADE] = 1;
                }
                if (LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_FADE] > OBJ_BLIND_FADE_CNT)
                {
                    LpstrSysDisInfo->u16SysDistance = SNS_INVALID_DIS;
                    LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_FADE] = 0;
                    LpstrSysDisInfo->u8BlindKeepFlag = 0;
                    LpstrSysDisInfo->u8CarDiret = 0xff;
                    LpstrSnsSigGroupDisFollow->MasDisData.u8KeepBeepFlag = 0;
                }
            }
            else
            {
                if (LpstrSysDisInfo->u8CarDiret != LpstrCarMoveSts->eCarDir)
                {
                    LpstrSysDisInfo->u16SysDistance = SNS_INVALID_DIS;
                    LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_FADE] = 0;
                    LpstrSysDisInfo->u8BlindKeepFlag = 0;
                    LpstrSnsSigGroupDisFollow->MasDisData.u8KeepBeepFlag = 0;
                    LpstrSysDisInfo->u8CarDiret = 0xff;
                }
                else
                {
                    LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_FADE]++;
                    if (LpSnsDisData->u16MasterActualDis < BLIND_KEEP_DIS)
                    {
                        LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_FADE] = 1;
                    }
                    if (LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_FADE] > OBJ_BLIND_FADE_CNT)
                    {
                        LpstrSysDisInfo->u16SysDistance = SNS_INVALID_DIS;
                        LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_FADE] = 0;
                        LpstrSysDisInfo->u8BlindKeepFlag = 0;
                        LpstrSnsSigGroupDisFollow->MasDisData.u8KeepBeepFlag = 0;
                        LpstrSysDisInfo->u8CarDiret = 0xff;
                    }
                }
            }
        }
#endif
        else
        {
            LpstrSysDisInfo->u16SysDistance = SNS_INVALID_DIS;
        }
#if 0
        /* 跟踪模块已有退出计数此处不再累加 */
        if (LpstrSysDisInfo->u16SysDistance  != SNS_INVALID_DIS)
        {
            if (LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_KEEP]++ > OBJ_KEEP_CNT)
            {
                LpstrSysDisInfo->u16SysDistance  = SNS_INVALID_DIS;
            }
        }
#endif
    }
    else
    {
        /* 确认障碍物 */
        if (LpstrSysDisInfo->u16SysDistance == SNS_INVALID_DIS)
        {
#if 0
            /* 争对单主发且为实际探测到的障碍物 */
            if (LpstrSysObjInfo->u8MasterBurstValidFlag == VALID)
            {
                if (LpSnsDisData->u16MasterEchoHeight != 0)
                {
                    LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_KEEP]++;
                    LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_FAR] = 0;
                    LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_NEAR] = 0;
                    LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_FADE] = 0;
                    if (LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_KEEP] >= OBJ_KEEP_CNT)
                    {
                        LpstrSysDisInfo->u16SysDistance = Lu16Distance;
                        LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_KEEP] = 0;
                    }
                }
                else
                {
                    LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_KEEP] = 0;
                }
            }
            else
            {
                LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_FAR] = 0;
                LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_NEAR] = 0;
                LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_FADE] = 0;
                LpstrSysDisInfo->u16SysDistance = Lu16Distance;
            }
#else
            LpstrSysDisInfo->u8MoveSta = 0;
            LpstrSysDisInfo->u16MoveStaCnt = 0;
            LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_FAR] = 0;
            LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_NEAR] = 0;
            LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_FADE] = 0;
            LpstrSysDisInfo->u16SysDistance = Lu16Distance;
#endif
        }
        /* 障碍物远离 */
        else if (Lu16Distance > LpstrSysDisInfo->u16SysDistance)
        {
            LpstrSysDisInfo->u8MoveSta = 0;
            LpstrSysDisInfo->u16MoveStaCnt = 0;
            LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_KEEP] = 0;
            LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_NEAR] = 0;
            LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_FAR]++;
            /* 防止远离时盲区跟踪不退出 */
            if (LpstrSysDisInfo->u8BlindKeepFlag)
            {
                LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_FADE] = OBJ_BLIND_FADE_CNT + 1;
            }
            else
            {
                LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_FADE] = 0;
            }
            
            if ((Lu16Distance - LpstrSysDisInfo->u16SysDistance) > (uint8)50)
            {
                if (LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_FAR] >= OBJ_FAR_CNT)
                {
                    LpstrSysDisInfo->u16SysDistance = Lu16Distance;
                    LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_FAR] = 0;
                }
            }
            else
            {
                LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_FAR] = 0;
            }
        }
        /* 障碍物靠近 */
        else
        {
            LpstrSysDisInfo->u8MoveSta = 1;
            LpstrSysDisInfo->u16MoveStaCnt++;
            LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_KEEP] = 0;
            LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_FAR] = 0;
            LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_FADE] = 0;
            LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_NEAR]++;
            if (LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_NEAR] >= OBJ_NEAR_CNT)
            {
                LpstrSysDisInfo->u16SysDistance = Lu16Distance;
                LpstrSysDisInfo->u8SysDisUpdateCnt[OBJ_MOVE_NEAR] = 0;
            }
        }
    }
}


/******************************************************************************
 * @brief      障碍物分区
 * @param[in]  LenuSnsGroup 
 * @param[in]  LenuSnsCh 
 * <AUTHOR>
 * @date       2025-03-04 14:39:24
 * @note       
 *****************************************************************************/
void OutputDis(PDCSnsGroupType LenuSnsGroup, PDCSnsChannelType LenuSnsCh)
{
    PPDis_ZONETye * LpPPZoneInfo;
    SysDisInfoType * LpDisInfo;
    LpPPZoneInfo = &GstrPPZone[LenuSnsGroup];
    LpDisInfo = &GstrSysDisInfo[LenuSnsGroup][LenuSnsCh];

    if ((LpDisInfo->u16SysDistance != SNS_INVALID_DIS) && (LpDisInfo->u16SysDistance <= WARNING_MAX_DIS) && (LpDisInfo->u16SysDistance != 0))
    {
        if (LenuSnsCh == PDC_SNS_CH_FML_RML)
        {
            LpPPZoneInfo->u16ML1_Distance = LpDisInfo->u16SysDistance / 10;
            LpPPZoneInfo->u16ML2_Distance = LpDisInfo->u16SysDistance / 10;
        }
        if (LenuSnsCh == PDC_SNS_CH_FMR_RMR)
        {
            LpPPZoneInfo->u16MR1_Distance = LpDisInfo->u16SysDistance / 10;
            LpPPZoneInfo->u16MR2_Distance = LpDisInfo->u16SysDistance / 10;
        }
        if (LenuSnsCh == PDC_SNS_CH_FL_RL)
        {
            LpPPZoneInfo->u16L1_Distance = LpDisInfo->u16SysDistance / 10;
            LpPPZoneInfo->u16L2_Distance = LpDisInfo->u16SysDistance / 10;
        }
        if (LenuSnsCh == PDC_SNS_CH_FR_RR)
        {
            LpPPZoneInfo->u16R1_Distance = LpDisInfo->u16SysDistance / 10;
            LpPPZoneInfo->u16R2_Distance = LpDisInfo->u16SysDistance / 10;
        }
    }
    else
    {
        if (LenuSnsCh == PDC_SNS_CH_FL_RL)
        {
            LpPPZoneInfo->u16L1_Distance = NO_OBJECT;
            LpPPZoneInfo->u16L2_Distance = NO_OBJECT;
        }
        else if (LenuSnsCh == PDC_SNS_CH_FML_RML)
        {
            LpPPZoneInfo->u16ML1_Distance = NO_OBJECT;
            LpPPZoneInfo->u16ML2_Distance = NO_OBJECT;
        }
        else if (LenuSnsCh == PDC_SNS_CH_FMR_RMR)
        {
            LpPPZoneInfo->u16MR1_Distance = NO_OBJECT;
            LpPPZoneInfo->u16MR2_Distance = NO_OBJECT;
        }
        else if (LenuSnsCh == PDC_SNS_CH_FR_RR)
        {
            LpPPZoneInfo->u16R1_Distance = NO_OBJECT;
            LpPPZoneInfo->u16R2_Distance = NO_OBJECT;
        }
    }
}


/******************************************************************************
 * @brief      计算障碍物距离
 * @param[in]  LenuSnsGroup 
 * @param[in]  LenuSnsCh 
 * <AUTHOR>
 * @date       2025-03-04 14:39:49
 * @note       
 *****************************************************************************/
void SnsCalObjDistance(PDCSnsGroupType LenuSnsGroup, PDCSnsChannelType LenuSnsCh)
{
    uint16 Lu16SnsDistanceMin;
    SystemObjInfoTypedef * LpstrSysObjInfo;
    LpstrSysObjInfo = &GstrSystemObjInfo[LenuSnsGroup][LenuSnsCh];

    if ((LenuSnsCh == PDC_SNS_CH_FL_RL) || (LenuSnsCh == PDC_SNS_CH_FML_RML) || (LenuSnsCh == PDC_SNS_CH_FMR_RMR) || (LenuSnsCh == PDC_SNS_CH_FR_RR))
    {
        GetOriginalData(LenuSnsGroup, LenuSnsCh);
        JudgeListenFlag(LenuSnsGroup, LenuSnsCh);
        CalcObjSnsCoord(LenuSnsGroup, LenuSnsCh);
        SnsCoorConvertToCarCoor(LenuSnsGroup, LenuSnsCh);
        DtmZoneAndDtmToBumperDis(LenuSnsGroup, LenuSnsCh);
        Lu16SnsDistanceMin  = (uint16)LpstrSysObjInfo->fObjTOBumperDis;
        UpdateSnsDistance(LenuSnsGroup, LenuSnsCh, Lu16SnsDistanceMin);
        OutputDis(LenuSnsGroup, LenuSnsCh);
    }
    else
    {
        /* do nothing */
    }
}
