/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: TimerDrv.h
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/
#ifndef __TIMERDRV_H
#define __TIMERDRV_H

/*******************************全局函数声明***********************************/
extern void TimerDrvInit(void);
extern void TimerClose(void);
extern void WDTInit(void);
extern void WDTFeedDog(uint8 *Lu8TimerCnt);
extern void WDTTriggerReset(void);
extern void SetTimeDelay(uint8 Lu8DelayMs);

#endif
