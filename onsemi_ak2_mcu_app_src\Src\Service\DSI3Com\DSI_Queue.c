/*
 * DSI_Queue.c
 *
 *  Created on: 2021年3月23日
 *      Author: 6000021992
 */
#include "DSI_Queue.h"

/******************************************************************************/
/**
 * @brief             初始化队列
 * @param[in]         pQueue            队列地址
 
 * @return            0 OK,else ERR
 */
/******************************************************************************/

uint8 InitQueue(Queue_Str *pQueue)
{
    if(pQueue == NULL)
    {
        return 0xFF;
    }
    /** @brief 数据清0 */
    pQueue->WriteIndexCnt = 0;
    pQueue->ReadIndexCnt = 0;
    
    memset(pQueue->pDataBuf,0,pQueue->wQueSize*pQueue->wDataSize);
    
    memset(pQueue->pQueDataStatus,0,sizeof(QueueDataStatus_en)*pQueue->wQueSize);
    
    memset(pQueue->pQueueTimeOutCnt,0,sizeof(uint16)*pQueue->wQueSize);

    return 0;
}


/******************************************************************************/
/**
 * @brief             队列数据清零
 * @param[in]         pQueue            队列地址
 
 * @return            0 OK,else ERR
 */
/******************************************************************************/

uint8 ClearQueue(Queue_Str *pQueue)
{
    if(pQueue == NULL || pQueue->pDataBuf == NULL || pQueue->pQueDataStatus == NULL || pQueue->pQueueTimeOutCnt == NULL)
    {
        return 0xFF;
    }
    
    /** @brief 数据清0 */
    
    pQueue->WriteIndexCnt = 0;
    pQueue->ReadIndexCnt = 0;
    memset(pQueue->pDataBuf,0,pQueue->wQueSize*pQueue->wDataSize);
    
    memset(pQueue->pQueDataStatus,0,sizeof(QueueDataStatus_en)*pQueue->wQueSize);
    
    memset(pQueue->pQueueTimeOutCnt,0,sizeof(uint16)*pQueue->wQueSize);
    return 0;
}
/******************************************************************************/
/**
 * @brief             获取队列空闲数据buf地址
 * @param[in]         pQueue     队列
 * @param[in]         pOutBufNum 输出buf编号

 * @return            NULL 无空闲buf,else 空闲buff地址，
 */
/******************************************************************************/

void * GetQueue_IdleBufAddr(Queue_Str *pQueue,uint8 *pOutBufNum)
{
    QueueDataStatus_en *pQueDataStatus;
    void *pRet = NULL;
    uint16 *pTimeCnt;
    uint16 LQueSize = 0;
    uint8 FlgCnt = 0xFF;
    
    if((pQueue == NULL) || (pOutBufNum == NULL))
    {
        return NULL;
    }
    pQueDataStatus = pQueue->pQueDataStatus;
    
    LQueSize = pQueue->wQueSize;
    pTimeCnt = pQueue->pQueueTimeOutCnt;

    if(pQueue->WriteIndexCnt >= LQueSize)
    {
        pQueue->WriteIndexCnt = 0;
    }

    if(pQueDataStatus[pQueue->WriteIndexCnt] == QueueData_IDLE)
    {

        pQueDataStatus[pQueue->WriteIndexCnt] = QueueData_WAIT;
        
        FlgCnt = pQueue->WriteIndexCnt;
        
        pTimeCnt[FlgCnt] = 0;

        if(pQueue->WriteIndexCnt < LQueSize)
        {
            pQueue->WriteIndexCnt ++;
        }
        else
        {
            pQueue->WriteIndexCnt = 0;
        }

        /** @brief 地址偏移 */
        pRet = &pQueue->pDataBuf + (FlgCnt * pQueue->wDataSize);

    }
    else
    {

    }

    *pOutBufNum = FlgCnt;

    return pRet;
}


/******************************************************************************/
/**
 * @brief             设置队列数据Ready
 * @param[in]         pQueue     
 * @param[in]         QueNum
 
 * @return            0 OK,else ERR
 */
/******************************************************************************/

uint8 SetQueue_Data_Ready(Queue_Str *pQueue,uint16 QueNum)
{
    QueueDataStatus_en *pQueDataStatus;
    
    if(pQueue == NULL)
    {
        return 0;
    }
    pQueDataStatus = pQueue->pQueDataStatus;

    if((QueNum < pQueue->wQueSize))
    {
        pQueDataStatus[QueNum] = QueueData_READY;
        return 0;
    }
    else
    {
        return 0xFF;
    }

}

/******************************************************************************/
/**
 * @brief             写入队列数据，并将数据状态置为Ready
 * @param[in]         pQueue     队列
 * @param[in]         pInData    写入数据
 * @param[in]         InDataLen  写入数据长度
 
 * @return            0xFFFF ERR ，else OK
 */
/******************************************************************************/

uint16 PutQueue_BufData(Queue_Str *pQueue,void *pInData,uint16 InDataLen)
{
    QueueDataStatus_en *pQueDataStatus;
    
    uint16 *pTimeCnt;
    uint16 LQueSize = 0;
    uint16 FlgCnt = 0xFFFF;
    void *pQueueDataBuff;
    if((pQueue == NULL) || (pInData == NULL))
    {
        return 0xFFFF;
    }
    pQueDataStatus = pQueue->pQueDataStatus;
    
    LQueSize = pQueue->wQueSize;
    pTimeCnt = pQueue->pQueueTimeOutCnt;

    if(pQueue->WriteIndexCnt >= LQueSize)
    {
        pQueue->WriteIndexCnt = 0;
    }

    if(pQueDataStatus[pQueue->WriteIndexCnt] == QueueData_IDLE)
    {

        FlgCnt = pQueue->WriteIndexCnt;
        
        pTimeCnt[FlgCnt] = 0;

        if(pQueue->WriteIndexCnt < LQueSize)
        {
            pQueue->WriteIndexCnt ++;
        }
        else
        {
            pQueue->WriteIndexCnt = 0;
        }
        
        pQueueDataBuff = &pQueue->pDataBuf + (FlgCnt * pQueue->wDataSize);
        
        memcpy(&pQueueDataBuff,pInData,InDataLen);
        
        pQueDataStatus[FlgCnt] = QueueData_READY;

    }
    else
    {

    }
    
    return FlgCnt;
}

/******************************************************************************/
/**
 * @brief             获取队列数据Ready个数
 * @param[in]         pQueue     队列

 
 * @return            Ready数据个数
 */
/******************************************************************************/

uint16 GetQueue_ReadyCnt(Queue_Str *pQueue)
{
    uint16 LwCnt = 0;
    uint16 ReadyCnt = 0;
    uint16 wQueSize = 0;
    
    if(pQueue == NULL)
    {
        return 0;
    }
    QueueDataStatus_en *pQueDataStatus;    
    wQueSize = pQueue->wQueSize;
    pQueDataStatus = pQueue->pQueDataStatus;

    for(LwCnt = 0;LwCnt < wQueSize;LwCnt ++)
    {
        if(pQueDataStatus[LwCnt] == QueueData_READY)
        {
            ReadyCnt ++;
        }
    }

    return ReadyCnt;
    

}

/******************************************************************************/
/**
 * @brief             写入队列数据，并将数据状态置为Ready
 * @param[in]         pQueue     队列
 * @param[in]         pOutData   输出数据保存地址
 
 * @return            0xFFFF 无Ready数据 ，else OK
 */
/******************************************************************************/

uint16 GetQueue_ReadyData(Queue_Str *pQueue,void *pOutData)
{
    void *pQueueDataBuff;
    QueueDataStatus_en *pQueDataStatus;
    uint16 *pTimeCnt;
    uint16 LcwQueSize = 0;
    uint16 FlgCnt = 0xFFFF;

    if((pQueue == NULL) || (pOutData == NULL))
    {
        return 0xFFFF;
    }
    pQueDataStatus = pQueue->pQueDataStatus;
    pQueueDataBuff = pQueue->pDataBuf;
    LcwQueSize = pQueue->wQueSize;
    pTimeCnt = pQueue->pQueueTimeOutCnt;


   
    /** @brief 查找Ready数据 */
    if(pQueDataStatus[pQueue->ReadIndexCnt] == QueueData_READY)
    {
        FlgCnt = pQueue->ReadIndexCnt;
        
        pQueue->ReadIndexCnt ++;
        
    }
    
    if(pQueue->ReadIndexCnt >= LcwQueSize)
    {
        pQueue->ReadIndexCnt = 0;
    }

    if(FlgCnt != 0xFFFF)
    {
    
        pQueueDataBuff = &pQueue->pDataBuf + (FlgCnt * pQueue->wDataSize);
        
        memcpy(pOutData,pQueueDataBuff,pQueue->wDataSize);
        
        pQueDataStatus[FlgCnt] = QueueData_IDLE;
        pTimeCnt[FlgCnt] = 0;

        return FlgCnt;
    }
    else
    {
        return 0xFFFF;
    }


}

/******************************************************************************/
/**
 * @brief             队列1ms超时监控任务
 * @param[in]         pQueue     队列
 
 * @return             无
 */
/******************************************************************************/

uint8 Queue1msHook(Queue_Str *pQueue)
{
    uint16 LwCnt = 0;
    QueueDataStatus_en *pQueDataStatus;
    uint16 *pTimeCnt;
    uint16 LcwQueSize = 0;
    uint16 wTimeOutValue = 0;
    if(pQueue == NULL)
    {
        return 0xFF;
    }

    pQueDataStatus = pQueue->pQueDataStatus;
    LcwQueSize = pQueue->wQueSize;
    pTimeCnt = pQueue->pQueueTimeOutCnt;
    wTimeOutValue = pQueue->wTimeOutValue;

    for(LwCnt = 0;LwCnt < LcwQueSize;LwCnt ++)
    {
        if((pQueDataStatus[LwCnt] == QueueData_READY )||(pQueDataStatus[LwCnt] == QueueData_WAIT) )
        {
            
            if(pTimeCnt[LwCnt] < wTimeOutValue)
            {
                pTimeCnt[LwCnt]++;

            }
            else
            {
                /*超时处理*/
                if(pQueue->QuequTimeOutCbkFunc != NULL)
                {
                    pQueue->QuequTimeOutCbkFunc(pQueue->pCbkFuncData);
                }
            }
        }
    }
    return 0;
}

