/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * Created on: 2024-01-25 08:55
 * Original designer: 23374
 ******************************************************************************/

#include "MapFromAllPoints.h"

extern VehiclePointCloudBufType GstrVehiclePointCloudBuf;
extern ObjMapInfoType           GstrObjMap;
extern ObjMapCoorType           GstrObjMapCoorInit;
extern uint32                   GdSystemMsTimer;
extern SnsAreaMapType           GstrAreaMapInit;
extern PDCSnsCarMovStsType      GstrSnsCarMovSts[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];
extern PDCSnsUseOdoType  GstrPDCSnsUseOdo;
uint32 G_TimeStampPoint = .0;

int             G_parrle_parking_flag = 0;
StackElem       G_StackElem;
ListElem        G_ListElem;
SnsAreaMapType  G_FourSnsDeMap[4];
uint8           G_FourSnsDeMap_init = 0;
SnsRealDist     G_SnsRealDist[12];
uint8           G_SnsRealDistInit = 0;

const SnsRealDist SnsRealDistInit =
{
    .real_dist=0,
    .u32Timestamp = 0
};

const ObjCoorInfoType ObjCoorInfoInit =
{
    .fObjX = 0,
    .fObjY = 0,
    .u32Timestamp = 0,
    .enuObjType = 0,
    .eMeasType = 0,
    .u16MasterHeight = 0,
    .u16MasterDis = 0,
    .enuSnsCh = -1,
};

/************************** Stack Data Struct Operation *******************************/

/* 初始化 Stack 数据结构 */
void InitStackElem(void)
{
    StackElem* pStackElem;
    pStackElem = &G_StackElem;
    pStackElem->u8StackSize = STACK_INIT_SIZE;
    for (int i = 0; i < pStackElem->u8StackSize; i++)
    {
        pStackElem->Buf[i] = ObjCoorInfoInit;
    }
    pStackElem->u8BufCnt = 0;
}

/* 往 Stack 添加数据 */
int StackElemPush(ObjCoorInfoType elem)
{
    int result_status = 0;

    StackElem* pStackElem;
    pStackElem = &G_StackElem;
    if (pStackElem->u8BufCnt < pStackElem->u8StackSize)
    {
        result_status = 1;
        pStackElem->Buf[pStackElem->u8BufCnt].eMeasType = elem.eMeasType;
        pStackElem->Buf[pStackElem->u8BufCnt].enuObjType = elem.enuObjType;
        pStackElem->Buf[pStackElem->u8BufCnt].fObjX = elem.fObjX;
        pStackElem->Buf[pStackElem->u8BufCnt].fObjY = elem.fObjY;
        pStackElem->Buf[pStackElem->u8BufCnt].u32Timestamp = elem.u32Timestamp;
        pStackElem->Buf[pStackElem->u8BufCnt].u16MasterHeight = elem.u16MasterHeight;
        pStackElem->Buf[pStackElem->u8BufCnt].u16MasterDis = elem.u16MasterDis;
        pStackElem->Buf[pStackElem->u8BufCnt].enuSnsCh = elem.enuSnsCh;

        pStackElem->u8BufCnt++;
    }

    return result_status;
}

/* 判定 Stack 是否为空 */
int StackElemEmpty(void)
{
    int result_status = 0;

    StackElem* pStackElem;
    pStackElem = &G_StackElem;
    if (pStackElem->u8BufCnt == 0)
    {
        result_status = 1;
    }

    return result_status;
}

/* 获取 Stack 顶部元素 */
void GetStackElemTop(ObjCoorInfoType *elem)
{
    StackElem* pStackElem;
    pStackElem = &G_StackElem;

    elem->eMeasType = pStackElem->Buf[pStackElem->u8BufCnt - 1].eMeasType;
    elem->enuObjType = pStackElem->Buf[pStackElem->u8BufCnt - 1].enuObjType;
    elem->fObjX = pStackElem->Buf[pStackElem->u8BufCnt - 1].fObjX;
    elem->fObjY = pStackElem->Buf[pStackElem->u8BufCnt - 1].fObjY;
    elem->u32Timestamp = pStackElem->Buf[pStackElem->u8BufCnt - 1].u32Timestamp;
    elem->u16MasterHeight = pStackElem->Buf[pStackElem->u8BufCnt - 1].u16MasterHeight;
    elem->u16MasterDis = pStackElem->Buf[pStackElem->u8BufCnt - 1].u16MasterDis;
    elem->enuSnsCh = pStackElem->Buf[pStackElem->u8BufCnt - 1].enuSnsCh;
}

/* 删除 Stack 顶部元素 */
int StackElemPop(void)
{
    int result_status = 0;

    StackElem* pStackElem;
    pStackElem = &G_StackElem;

    if (pStackElem->u8BufCnt > 0)
    {
        result_status = 1;
        pStackElem->Buf[pStackElem->u8BufCnt - 1] = ObjCoorInfoInit;
        pStackElem->u8BufCnt--;
    }

    return result_status;
}

/************************** List Data Struct Operation *******************************/

/* 初始化 List 数据结构 */
void InitListElem(void)
{
    ListElem* pListElem;
    pListElem = &G_ListElem;

    pListElem->u8ListSize = LIST_INIT_SIZE;
    for (int i = 0; i < pListElem->u8ListSize; i++)
    {
        pListElem->Buf[i] = ObjCoorInfoInit;
    }

    pListElem->u8BufCnt = 0;
}

/* 往 List 添加数据 */
int ListElemInsert(ObjCoorInfoType elem)
{
    int result_status = 0;

    ListElem* pListElem;
    pListElem = &G_ListElem;
    if (pListElem->u8BufCnt < pListElem->u8ListSize)
    {
        result_status = 1;
        pListElem->Buf[pListElem->u8BufCnt].eMeasType = elem.eMeasType;
        pListElem->Buf[pListElem->u8BufCnt].enuObjType = elem.enuObjType;
        pListElem->Buf[pListElem->u8BufCnt].fObjX = elem.fObjX;
        pListElem->Buf[pListElem->u8BufCnt].fObjY = elem.fObjY;
        pListElem->Buf[pListElem->u8BufCnt].u32Timestamp = elem.u32Timestamp;
        pListElem->Buf[pListElem->u8BufCnt].u16MasterHeight = elem.u16MasterHeight;
        pListElem->Buf[pListElem->u8BufCnt].u16MasterDis = elem.u16MasterDis;
        pListElem->Buf[pListElem->u8BufCnt].enuSnsCh = elem.enuSnsCh;

        pListElem->u8BufCnt++;
    }

    return result_status;
}

/* 获取 List 长度 */
int ListElemLength(void)
{
    ListElem* pListElem;
    pListElem = &G_ListElem;

    return pListElem->u8BufCnt;
}

/* 获取 List 指定位置数据 */
int GetListElemDate(int index, ObjCoorInfoType *elem)
{
    int result_status = 0;
    ListElem* pListElem;
    pListElem = &G_ListElem;
    if (index >= 0 && index < pListElem->u8BufCnt)
    {
        result_status = 1;

        elem->eMeasType = pListElem->Buf[index].eMeasType;
        elem->enuObjType = pListElem->Buf[index].enuObjType;
        elem->fObjX = pListElem->Buf[index].fObjX;
        elem->fObjY = pListElem->Buf[index].fObjY;
        elem->u32Timestamp = pListElem->Buf[index].u32Timestamp;
        elem->u16MasterHeight = pListElem->Buf[index].u16MasterHeight;
        elem->u16MasterDis = pListElem->Buf[index].u16MasterDis;
        elem->enuSnsCh = pListElem->Buf[index].enuSnsCh;
    }

    return result_status;
}

static void JudgeBigWallType(SnsAreaMapType *mapInfo)
{
    float dist = mapInfo->u16MasterDis;
    float big_wall_height_thd;
    if (dist > 2000)
    {
        return;
    }
    if (dist > 1750)
    {
        big_wall_height_thd = 5000;
    }
    else if (dist > 1500)
    {
        big_wall_height_thd = 6500;
    }
    else if (dist > 1250)
    {
        big_wall_height_thd = 9000;
    }
    else if (dist > 1000)
    {
        big_wall_height_thd = 10000;
    }
    else
    {
        big_wall_height_thd = 13000;
    }

    if (mapInfo->u16StdMasterHeight > big_wall_height_thd)
    {
        mapInfo->enuMapType = OBJ_BIGWALL_TYPE;
    }

    return;
}

/******************************************************************************
 * 函数名称: PCADirCal
 *
 * 功能描述: PCA 主成分分析计算离散点的方向
 *
 * 输入参数: 聚类后的点云数据列表
 *
 * 输出参数: 对应的 map 信息
 *
 * 返回值: 无
 *
 * 其它说明: 无
 *
 * 修改日期              版本号        修改人          修改内容
 * 2024-01-27 21:23      V0.1          wk_zhong        初次发布
 ******************************************************************************/
void PCADirCal(ObjCoorInfoType *points, SnsAreaMapType *mapInfo)
{
    //计算 X Y 的期望
    float xMean = 0;
    float yMean = 0;
    float xMax = -99999999999.0;
    float xMin = 99999999999.0;
    float yMax = -99999999999.0;
    float yMin = 99999999999.0;
    float StdMasterHeight = 0;
    float ChirpMasterHeight = 0;
    float MasterDist = 0;
    int chrip_cnt = 0;
    int std_cnt = 0;
    int middle_channle_point_cnt = 0;
    uint32 timestamp = 0.0;
    int pointCnt = ListElemLength();
    int map_type[7];
    memset(map_type, 0, sizeof(int) * 7);

    if (pointCnt <= 0 || pointCnt >= STACK_INIT_SIZE)
    {
        return;
    }

    for (int i = 0; i < pointCnt; i++)
    {
        ObjCoorInfoType curPoint;
        GetListElemDate(i, &curPoint);
        
        if (curPoint.enuObjType >= 0 && curPoint.enuObjType < 7)
        {
            map_type[curPoint.enuObjType]++;
        }
        xMean += curPoint.fObjX;
        yMean += curPoint.fObjY;
        if (xMax < curPoint.fObjX) { xMax = curPoint.fObjX; }
        if (xMin > curPoint.fObjX) { xMin = curPoint.fObjX; }
        if (yMax < curPoint.fObjY) { yMax = curPoint.fObjY; }
        if (yMin > curPoint.fObjY) { yMin = curPoint.fObjY; }
        if (curPoint.u32Timestamp > timestamp) { timestamp = curPoint.u32Timestamp; }
        
        if (curPoint.eMeasType == PDC_SNS_MEAS_ADV_CHIRP_DOWN || curPoint.eMeasType == PDC_SNS_MEAS_ADV_CHIRP_UP) 
        { 
            chrip_cnt++;
            ChirpMasterHeight += (float)(curPoint.u16MasterHeight);
        }
        else
        {
            std_cnt++;
            StdMasterHeight += (float)(curPoint.u16MasterHeight);
        }
        MasterDist += (float)(curPoint.u16MasterDis);

        if (curPoint.enuSnsCh == PDC_SNS_CH_FML_RML || curPoint.enuSnsCh == PDC_SNS_CH_FMR_RMR)
        {
            middle_channle_point_cnt++;
        }

    }

    if (chrip_cnt != 0)
    {
        ChirpMasterHeight = ChirpMasterHeight / chrip_cnt;
    }

    if (std_cnt != 0)
    {
        StdMasterHeight = StdMasterHeight / std_cnt;
    }

    MasterDist = MasterDist / pointCnt;

    mapInfo->u16MasterDis = MasterDist;
    mapInfo->u16StdMasterHeight = StdMasterHeight;
    mapInfo->u16ChirpMasterHeight = ChirpMasterHeight;

    /* 根据点云类型统计结果，确定 map 高低属性类型 */
    //DEBUG_PRINT("timestamp = %u,MasterDist = %f, point_cnt = %d, pca big wall type cnt = %d\n", GdSystemMsTimer, MasterDist, pointCnt, map_type[OBJ_BIGWALL_TYPE]);
    int max_value = -1;
    for (int i = 0; i < 7; i++)
    {
        if (map_type[i] > max_value) 
        {
            max_value = map_type[i];
            mapInfo->enuMapType = i;
        }
    }

    xMean /= pointCnt;
    yMean /= pointCnt;

    mapInfo->cirRect.centerX = xMean;
    mapInfo->cirRect.centerY = yMean;
    mapInfo->cirRect.minX = xMin;
    mapInfo->cirRect.minY = yMin;
    mapInfo->cirRect.maxX = xMax;
    mapInfo->cirRect.maxY = yMax;

    // 计算 X Y 的协方差
    float covXX = 0;
    float covXY = 0;
    float covYY = 0;

    for (int i = 0; i < pointCnt; i++)
    {
        ObjCoorInfoType curPoint;
        GetListElemDate(i, &curPoint);
        covXX += (curPoint.fObjX - xMean)*(curPoint.fObjX - xMean);
        covXY += (curPoint.fObjX - xMean)*(curPoint.fObjY - yMean);
        covYY += (curPoint.fObjY - yMean)*(curPoint.fObjY - yMean);
    }
    
    covXX /= pointCnt;
    covXY /= pointCnt;
    covYY /= pointCnt;

    //计算矩阵最大特征值和对应的特征向量
    /*
        [covXX covXY]
    A = [covXY covYY] => |labmda * E - A| = 0
    */
    float m = (covXX + covYY) / 2;
    float p = covXX * covYY - covXY * covXY;
    float delt1 = sqrt(m*m - p);
    float labda1 = m + delt1;
    float labda2 = m - delt1;
    float k;
    if (labda1 > labda2)
    {
        k = -(covXX - labda1) / covXY;
    }
    else
    {
        k = -(covXX - labda2) / covXY;
    }

    /* 2024-03-26：增加点云方差限制来判定点线 map */
    /* 方差能够反应点云的离散情况，对于 point_map 点云较密集（方差小），line_map 点云离散（方差大）*/
    //DEBUG_PRINT("timestamp = %u, pca point_size = %d, covXX = %f,covYY = %f\n", GdSystemMsTimer, pointCnt, covXX, covYY);
    int is_point_map = 0;
    if (covXX < 20000 && covYY < 20000)
    {
        is_point_map = 1;
    }

    if (is_point_map==1)
    {
        mapInfo->MapSharp = 0;
        mapInfo->P1_OdoCoor.fObjX = xMean;
        mapInfo->P1_OdoCoor.fObjY = yMean;
        mapInfo->P2_OdoCoor.fObjX = xMean;
        mapInfo->P2_OdoCoor.fObjY = yMean;
    }
    else
    {
        Line lineMaster;
        lineMaster.A = -k;
        lineMaster.B = 1;
        lineMaster.C = k * xMean - yMean;

        //将每一个点投影到主方向对应的直线上，并找出两个端点
        ObjCoorInfoType startPoint;
        ObjCoorInfoType endPoint;
        mapInfo->MapSharp = 1;

        /*
        比较 X 或者 Y 的方差，可以大致判定 map 的方向是沿着哪个轴
        如果 X 的方差较大，则表明点在 X 轴上分布比较离散
        如果 Y 的方差较大，则表明点在 Y 轴上分布比较离散
        */
        if (covXX > covYY)
        {
            if (ABS_VALUE(k) < 0.002)
            {
                mapInfo->P1_OdoCoor.fObjX = xMin;
                mapInfo->P1_OdoCoor.fObjY = yMean;
                mapInfo->P2_OdoCoor.fObjX = xMax;
                mapInfo->P2_OdoCoor.fObjY = yMean;
            }
            else
            {
                mapInfo->P1_OdoCoor.fObjX = xMin;
                mapInfo->P1_OdoCoor.fObjY = k * (xMin - xMean) + yMean;
                mapInfo->P2_OdoCoor.fObjX = xMax;
                mapInfo->P2_OdoCoor.fObjY = k * (xMax - xMean) + yMean;
            }
        }
        else
        {
            if (ABS_VALUE(1 / k) < 0.002)
            {
                mapInfo->P1_OdoCoor.fObjX = xMean;
                mapInfo->P1_OdoCoor.fObjY = yMin;
                mapInfo->P2_OdoCoor.fObjX = xMean;
                mapInfo->P2_OdoCoor.fObjY = yMax;
            }
            else
            {
                mapInfo->P1_OdoCoor.fObjX = (yMin - (yMean - k * xMean)) / k;
                mapInfo->P1_OdoCoor.fObjY = yMin;
                mapInfo->P2_OdoCoor.fObjX = (yMax - (yMean - k * xMean)) / k;
                mapInfo->P2_OdoCoor.fObjY = yMax;
            }
        }
    }

    float map_dist = PubAI_CalTwoPointDis(mapInfo->P1_OdoCoor.fObjX, mapInfo->P1_OdoCoor.fObjY, mapInfo->P2_OdoCoor.fObjX, mapInfo->P2_OdoCoor.fObjY);
    if (map_dist < 200 || pointCnt < 10 )
    {
        mapInfo->MapSharp = 0;
        mapInfo->P1_OdoCoor.fObjX = xMean;
        mapInfo->P1_OdoCoor.fObjY = yMean;
        mapInfo->P2_OdoCoor.fObjX = xMean;
        mapInfo->P2_OdoCoor.fObjY = yMean;
    }
    else
    {
        float odo_center_x = (mapInfo->P1_OdoCoor.fObjX + mapInfo->P2_OdoCoor.fObjX) / 2;
        float odo_center_y = (mapInfo->P1_OdoCoor.fObjY + mapInfo->P2_OdoCoor.fObjY) / 2;
        float diff_dist = PubAI_CalTwoPointDis(odo_center_x, odo_center_y, xMean, yMean);
        if (diff_dist / map_dist > 0.25)
        {
            mapInfo->MapSharp = 0;
            mapInfo->P1_OdoCoor.fObjX = xMean;
            mapInfo->P1_OdoCoor.fObjY = yMean;
            mapInfo->P2_OdoCoor.fObjX = xMean;
            mapInfo->P2_OdoCoor.fObjY = yMean;
        }
    }

    /*
    将世界坐标系下的 map 点转到车辆坐标系下面
    */
    PubAI_TransObjOdoCoorToCarCoor(&mapInfo->P1_OdoCoor.fObjX, &mapInfo->P1_OdoCoor.fObjY, &mapInfo->P1_CarCoor.fObjX, &mapInfo->P1_CarCoor.fObjY);
    PubAI_TransObjOdoCoorToCarCoor(&mapInfo->P2_OdoCoor.fObjX, &mapInfo->P2_OdoCoor.fObjY, &mapInfo->P2_CarCoor.fObjX, &mapInfo->P2_CarCoor.fObjY);
    mapInfo->Timestamp = timestamp;

    if (mapInfo->MapSharp == 1)
    {
        if (map_type[OBJ_BIGWALL_TYPE] * 3 > pointCnt)
        {
            mapInfo->enuMapType = OBJ_BIGWALL_TYPE;
        }

        if (map_type[OBJ_BIGWALL_TYPE] == 0)
        {
            mapInfo->enuMapType = OBJ_LOW_CURB_TYPE;
        }

        JudgeBigWallType(mapInfo);
    }

    //靠近中间探头的map，必要有中间探头生成的点云
    if (mapInfo->MapSharp == 0 && ABS_VALUE(mapInfo->P1_CarCoor.fObjY) < 700)
    {
        if (middle_channle_point_cnt < 2)
        {
            mapInfo->u8BeFusionToRealMapFlag = 0;
        }
    }

    /* 同频干扰下，没有扫频数据点，则认为是噪点 */
    if (mapInfo->P1_CarCoor.fObjX < 0)// 后保
    {
        if (GstrParkingGuidenceData.u8SameFreqNoiseFlag[1] == 1)
        {
            if (chrip_cnt > 2)
            {
                mapInfo->u8BeFusionToRealMapFlag = 1;
            }
            else
            {
                mapInfo->u8BeFusionToRealMapFlag = 0;
            }
        }

        if (GstrParkingGuidenceData.u8SameFreqNoiseFlag[1] == 0)
        {
            if (chrip_cnt == 0)
            {
                mapInfo->u8BeFusionToRealMapFlag = 0;
            }
            else
            {
                mapInfo->u8BeFusionToRealMapFlag = 1;
            }
        }
    }
    else //前保
    {
        if (GstrParkingGuidenceData.u8SameFreqNoiseFlag[0] == 1)
        {
            if (chrip_cnt > 2)
            {
                mapInfo->u8BeFusionToRealMapFlag = 1;
            }
            else
            {
                mapInfo->u8BeFusionToRealMapFlag = 0;
            }
        }

        if (GstrParkingGuidenceData.u8SameFreqNoiseFlag[0] == 0)
        {
            if (chrip_cnt == 0)
            {
                mapInfo->u8BeFusionToRealMapFlag = 0;
            }
            else
            {
                mapInfo->u8BeFusionToRealMapFlag = 1;
            }
        }
    }

    /*DEBUG_PRINT("timestamp = %u, point_size = %d, map_dist = %f, MasterDist = %f, StdMasterHeight = %f, ChirpMasterHeight = %f, mapInfo->enuMapType = %u\n",
        GdSystemMsTimer, pointCnt, map_dist, MasterDist, StdMasterHeight, ChirpMasterHeight, mapInfo->enuMapType);*/
    /*DEBUG_PRINT("timestamp = %u, map_info master_dist = %u, master_height = %u, p1OdoX = %f,p1Odoy = %f\n",
        GdSystemMsTimer, mapInfo->u16MasterDis, mapInfo->u16StdMasterHeight, mapInfo->P1_OdoCoor.fObjX, mapInfo->P1_OdoCoor.fObjY);*/
}

/******************************************************************************
 * 函数名称: GetVechBufferData
 *
 * 功能描述: 拷贝全局点云数据到临时 buffer
 *
 * 输入参数: 无
 *
 * 输出参数: 临时 buffer 数据集合
 *
 * 返回值: buffer 里面数据的数量
 *
 * 其它说明: 无
 *
 * 修改日期              版本号        修改人          修改内容
 * 2024-01-27 21:23      V0.1          wk_zhong        初次发布
 ******************************************************************************/
int GetVechBufferOdoCoorData(ObjCoorInfoType *buffer_data, uint32 *time_stamp)
{
    VehiclePointCloudBufType *LpstrVehiclePointCloudBuf;
    uint8 u8StartInx, u8EndInx;

    LpstrVehiclePointCloudBuf = &GstrVehiclePointCloudBuf;
    u8StartInx = LpstrVehiclePointCloudBuf->u8StartInx;
    u8EndInx = LpstrVehiclePointCloudBuf->u8EndInx;
    int indexCnt = 0;

    if (u8StartInx <= u8EndInx)
    {
        for (int i = u8StartInx; i < u8EndInx; i++)
        {
            buffer_data[indexCnt].fObjX = LpstrVehiclePointCloudBuf->Buf[i].strObjOdoCoor.fObjX;
            buffer_data[indexCnt].fObjY = LpstrVehiclePointCloudBuf->Buf[i].strObjOdoCoor.fObjY;
            buffer_data[indexCnt].u32Timestamp = LpstrVehiclePointCloudBuf->Buf[i].u32DetSysTime;
            buffer_data[indexCnt].enuObjType = LpstrVehiclePointCloudBuf->Buf[i].enuObjType;
            buffer_data[indexCnt].eMeasType = LpstrVehiclePointCloudBuf->Buf[i].eMeasType;
            buffer_data[indexCnt].u16MasterHeight = LpstrVehiclePointCloudBuf->Buf[i].u16MasterHeight;
            buffer_data[indexCnt].u16MasterDis = LpstrVehiclePointCloudBuf->Buf[i].u16MasterDis;
            buffer_data[indexCnt].enuSnsCh = LpstrVehiclePointCloudBuf->Buf[i].enuSnsCh;
            
            if (buffer_data[indexCnt].u32Timestamp > *time_stamp)
            {
                *time_stamp = buffer_data[indexCnt].u32Timestamp;
            }
            indexCnt++;

            if (indexCnt > 100) { return indexCnt; }
        }
    }
    else
    {
        for (int i = u8StartInx; i < VHE_POINT_CLOUD_BUF_NUM; i++)
        {
            buffer_data[indexCnt].fObjX = LpstrVehiclePointCloudBuf->Buf[i].strObjOdoCoor.fObjX;
            buffer_data[indexCnt].fObjY = LpstrVehiclePointCloudBuf->Buf[i].strObjOdoCoor.fObjY;
            buffer_data[indexCnt].u32Timestamp = LpstrVehiclePointCloudBuf->Buf[i].u32DetSysTime;
            buffer_data[indexCnt].enuObjType = LpstrVehiclePointCloudBuf->Buf[i].enuObjType;
            buffer_data[indexCnt].eMeasType = LpstrVehiclePointCloudBuf->Buf[i].eMeasType;
            buffer_data[indexCnt].u16MasterHeight = LpstrVehiclePointCloudBuf->Buf[i].u16MasterHeight;
            buffer_data[indexCnt].u16MasterDis = LpstrVehiclePointCloudBuf->Buf[i].u16MasterDis;
            buffer_data[indexCnt].enuSnsCh = LpstrVehiclePointCloudBuf->Buf[i].enuSnsCh;
            
            if (buffer_data[indexCnt].u32Timestamp > *time_stamp)
            {
                *time_stamp = buffer_data[indexCnt].u32Timestamp;
            }
            indexCnt++;

            if (indexCnt > 100) { return indexCnt; }
        }
        for (int i = 0; i < u8EndInx; i++)
        {
            buffer_data[indexCnt].fObjX = LpstrVehiclePointCloudBuf->Buf[i].strObjOdoCoor.fObjX;
            buffer_data[indexCnt].fObjY = LpstrVehiclePointCloudBuf->Buf[i].strObjOdoCoor.fObjY;
            buffer_data[indexCnt].u32Timestamp = LpstrVehiclePointCloudBuf->Buf[i].u32DetSysTime;
            buffer_data[indexCnt].enuObjType = LpstrVehiclePointCloudBuf->Buf[i].enuObjType;
            buffer_data[indexCnt].eMeasType = LpstrVehiclePointCloudBuf->Buf[i].eMeasType;
            buffer_data[indexCnt].u16MasterHeight = LpstrVehiclePointCloudBuf->Buf[i].u16MasterHeight;
            buffer_data[indexCnt].u16MasterDis = LpstrVehiclePointCloudBuf->Buf[i].u16MasterDis;
            buffer_data[indexCnt].enuSnsCh = LpstrVehiclePointCloudBuf->Buf[i].enuSnsCh;
            
            if (buffer_data[indexCnt].u32Timestamp > *time_stamp)
            {
                *time_stamp = buffer_data[indexCnt].u32Timestamp;
            }
            indexCnt++;

            if (indexCnt > 100) { return indexCnt; }
        }
    }

    return indexCnt;
}

/* 拷贝全局点云 car 坐标数据到临时 buffer */
int GetVechBufferCarCoorData(ObjCoorType *buffer_data, int front_rear)
{
    VehiclePointCloudBufType *LpstrVehiclePointCloudBuf;
    uint8 u8StartInx, u8EndInx;
    int biasY = 800;
    LpstrVehiclePointCloudBuf = &GstrVehiclePointCloudBuf;
    u8StartInx = LpstrVehiclePointCloudBuf->u8StartInx;
    u8EndInx = LpstrVehiclePointCloudBuf->u8EndInx;
    int indexCnt = 0;

    if (u8StartInx <= u8EndInx)
    {
        for (int i = u8StartInx; i < u8EndInx; i++)
        {
            if (front_rear == 0 && LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjX > 0)
            {
                if (LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjY < biasY &&
                    LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjX > -biasY)
                {
                    buffer_data[indexCnt].fObjX = LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjX;
                    buffer_data[indexCnt].fObjY = LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjY;
                    indexCnt++;

                    if (indexCnt > 100) { return indexCnt; }
                }
            }

            if (front_rear == 1 && LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjX < 0)
            {
                if (LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjY < biasY &&
                    LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjX > -biasY)
                {
                    buffer_data[indexCnt].fObjX = LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjX;
                    buffer_data[indexCnt].fObjY = LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjY;
                    indexCnt++;

                    if (indexCnt > 100) { return indexCnt; }
                }
            }
        }
    }
    else
    {
        for (int i = u8StartInx; i < VHE_POINT_CLOUD_BUF_NUM; i++)
        {
            if (front_rear == 0 && LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjX > 0)
            {
                if (LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjY < biasY &&
                    LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjX > -biasY)
                {
                    buffer_data[indexCnt].fObjX = LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjX;
                    buffer_data[indexCnt].fObjY = LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjY;
                    indexCnt++;

                    if (indexCnt > 100) { return indexCnt; }
                }
            }

            if (front_rear == 1 && LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjX < 0)
            {
                if (LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjY < biasY &&
                    LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjX > -biasY)
                {
                    buffer_data[indexCnt].fObjX = LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjX;
                    buffer_data[indexCnt].fObjY = LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjY;
                    indexCnt++;

                    if (indexCnt > 100) { return indexCnt; }
                }
            }
        }

        for (int i = 0; i < u8EndInx; i++)
        {
            if (front_rear == 0 && LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjX > 0)
            {
                if (LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjY < biasY &&
                    LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjX > -biasY)
                {
                    buffer_data[indexCnt].fObjX = LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjX;
                    buffer_data[indexCnt].fObjY = LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjY;
                    indexCnt++;

                    if (indexCnt > 100) { return indexCnt; }
                }
            }

            if (front_rear == 1 && LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjX < 0)
            {
                if (LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjY < biasY &&
                    LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjX > -biasY)
                {
                    buffer_data[indexCnt].fObjX = LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjX;
                    buffer_data[indexCnt].fObjY = LpstrVehiclePointCloudBuf->Buf[i].strObjCarCoor.fObjY;
                    indexCnt++;

                    if (indexCnt > 100) { return indexCnt; }
                }
            }
        }
    }

    return indexCnt;
}

/******************************************************************************
 * 函数名称: FindFarthestMapIndex
 *
 * 功能描述: 查找 map 缓存里面距离车身最远的map所对应的索引
 *
 * 输入参数: 无
 *
 * 输出参数: 无
 *
 * 返回值: 返回对应map缓存的id索引号
 *
 * 其它说明: 无
 *
 * 修改日期              版本号        修改人          修改内容
 * 2024-01-27 21:23      V0.1          wk_zhong        初次发布
 ******************************************************************************/
int FindFarthestMapIndex(int *is_replace)
{
    eMapObjIdType map_Id;
    ObjMapInfoType *LstrObjMap;
    LstrObjMap = &GstrObjMap;
    int index = 0;
    *is_replace = 0;
    float max_dist = -1;
    for (map_Id = MAP_OBJ_0; map_Id < MAP_OBJ_NUM; map_Id++)
    {
        if (LstrObjMap->ObjMapCoor[map_Id].u8MapExistFlag)
        {
            if (ABS_VALUE(LstrObjMap->ObjMapCoor[map_Id].fMapToBumper_Dis) > max_dist)
            {
                max_dist =ABS_VALUE(LstrObjMap->ObjMapCoor[map_Id].fMapToBumper_Dis);
                index = map_Id;
                *is_replace = 1;
            }
        }
        else
        {
            *is_replace = 0;
            index = map_Id;
            break;
        }
    }

    return index;
}

void UpdateRealDistBuffer(void)
{
    if (G_SnsRealDistInit == 0)
    {
        for (int i = 0; i < 12; i++)
        {
            G_SnsRealDist[i] = SnsRealDistInit;
        }

        G_SnsRealDistInit = 1;
    }

    int index = 0;
    for (int group = 0; group < 2; group++)
    {
        for (int channle = 0; channle < 6; channle++)
        {
            if (GstrMapSnsRealTimeDis[group][channle].u8RealDisUpdateFlag == 1)
            {
                index = group * 6 + channle;
                G_SnsRealDist[index].real_dist = GstrMapSnsRealTimeDis[group][channle].u16MasDis;
                G_SnsRealDist[index].u32Timestamp = GdSystemMsTimer;
            }
        }
    }
}

/* 近距离仅有DE的情况下，生成对应的点 map 信息 */
uint8 DEGenerateMap(SnsAreaMapType *temp_map,PDCSnsGroupType group, PDCSnsChannelType channle, float dist_threshold, float *real_DE_dist)
{
    uint8 result = 0;
    *temp_map = GstrAreaMapInit;
    VehiclePointCloudBufType* g_vheBuffer;
    g_vheBuffer = &GstrVehiclePointCloudBuf;

    if (GstrMapSnsRealTimeDis[group][channle].u8RealDisUpdateFlag == 1)
    {
        float real_dist = (float)GstrMapSnsRealTimeDis[group][channle].u16MasDis;
        float master_height = (float)GstrMapSnsRealTimeDis[group][channle].u16MasterHeight;
        PDCSnsMeasTypeType meas_type = GstrMapSnsRealTimeDis[group][channle].eMeasType;

        if (real_dist < dist_threshold)
        {
            *real_DE_dist = real_dist;
            result = 1;
        }
        else
        {
            return result;
        }

        /* 通过单 DE 值生成 point_map 的算法流程 */
        {
            ObjCoorType strObjSnsCoor;
            ObjCoorType strObjCarCoor;
            ObjCoorType strObjOdoCoor;

            /* 生成 pos_sns */
            if (group == PDC_SNS_GROUP_FRONT)
            {
                strObjSnsCoor.fObjX = real_dist;
            }
            if (group == PDC_SNS_GROUP_REAR)
            {
                strObjSnsCoor.fObjX = -real_dist;
            }
            strObjSnsCoor.fObjY = 0;

            /* 获取 sns 到 car 的标定外参 */
            float fSnsCoorX = GstrRamSnsCoor[group][channle].cRadarX;
            float fSnsCoorY = GstrRamSnsCoor[group][channle].cRadarY;
            float fSnsCoorCosAngle = GstrSnsOutAngle[group].fCosA[channle];
            float fSnsCoorSinAngle = GstrSnsOutAngle[group].fSinA[channle];

            /* pos_sns 转 pos_car */
            strObjCarCoor.fObjX = fSnsCoorX + (strObjSnsCoor.fObjX)*fSnsCoorCosAngle - (strObjSnsCoor.fObjY)*fSnsCoorSinAngle;
            strObjCarCoor.fObjY = fSnsCoorY + (strObjSnsCoor.fObjY)*fSnsCoorCosAngle + (strObjSnsCoor.fObjX)*fSnsCoorSinAngle;

            /* pos_car 转 pos_odo */
            PubAI_TransObjCarCoorToOdoCoor(&strObjCarCoor.fObjX, &strObjCarCoor.fObjY, &strObjOdoCoor.fObjX, &strObjOdoCoor.fObjY);

            /* 生成但主发 DE 的 point-map 信息 */
            if (GdSystemMsTimer > 50)
            {
                temp_map->Timestamp = GdSystemMsTimer - 48;
            }
            else
            {
                temp_map->Timestamp = GdSystemMsTimer;
            }
            temp_map->MapSharp = 0;
            temp_map->P1_CarCoor.fObjX = strObjCarCoor.fObjX;
            temp_map->P1_CarCoor.fObjY = strObjCarCoor.fObjY;
            temp_map->P2_CarCoor.fObjX = strObjCarCoor.fObjX;
            temp_map->P2_CarCoor.fObjY = strObjCarCoor.fObjY;
            temp_map->P1_OdoCoor.fObjX = strObjOdoCoor.fObjX;
            temp_map->P1_OdoCoor.fObjY = strObjOdoCoor.fObjY;
            temp_map->P2_OdoCoor.fObjX = strObjOdoCoor.fObjX;
            temp_map->P2_OdoCoor.fObjY = strObjOdoCoor.fObjY;
            temp_map->enuMapType = OBJ_PVC_PIPE_TYPE;
            temp_map->enuMeasType = meas_type;
        }
    }

    return result;
}


/******************************************************************************
 * 函数名称: AllPointCloudGenerateMap
 *
 * 功能描述: 由前后全局点云聚类生成对应的 map 信息
 *
 * 输入参数: 无
 *
 * 输出参数: 无
 *
 * 返回值: 无
 *
 * 其它说明: 无
 *
 * 修改日期              版本号        修改人          修改内容
 * 2024-01-27 21:23      V0.1          wk_zhong        初次发布
 ******************************************************************************/
void AllPointCloudGenerateMap(void)
{
    VehiclePointCloudBufType* g_vheBuffer;
    g_vheBuffer = &GstrVehiclePointCloudBuf;
    ObjCoorInfoType buffer_data[101];
    
    uint32 old_time;
    old_time = G_TimeStampPoint;
    int indexCnt = GetVechBufferOdoCoorData(buffer_data, &G_TimeStampPoint);
    
    if (old_time == G_TimeStampPoint)
    {
        return;
    }

#if 0
    static int current_times = 0;
    FILE *file = NULL;
    const char *filename = "D:\\LI_XIANG\\Log_LiXiang_test\\000c\\2\\point1.zwk";
    const char *mode = "a";
  
    fopen_s(&file, filename, mode);
    fprintf(file, "sys_timestamp = %u point_size = %d current_times = %d odo_x = %f odo_y = %f yaw_angle = %f\n", 
        GdSystemMsTimer, indexCnt, current_times, GstrPDCSnsUseOdo.fCar_X_Coor, GstrPDCSnsUseOdo.fCar_Y_Coor, GstrPDCSnsUseOdo.fCar_YawAngle);
    for (int i = 0; i < indexCnt; i++)
    {
        fprintf(file, "%f %f\n",buffer_data[i].fObjX, buffer_data[i].fObjY);
    }
    fprintf(file,"\n");
    fclose(file);
    current_times++;
#endif

    uint8 flags[101];
    for (int i = 0; i < 101; i++)
    {
        flags[i] = 0;
    }

    int cluster_count = 0;
    
    /* 全局点云聚类 */
    for (size_t i = 0; i < indexCnt; i++)
    {
        if (flags[i]) continue;

        flags[i] = 1;
        InitListElem();
        InitStackElem();

        ObjCoorInfoType elm;
        elm.fObjX = buffer_data[i].fObjX;
        elm.fObjY = buffer_data[i].fObjY;
        elm.u32Timestamp = buffer_data[i].u32Timestamp;
        elm.enuObjType = buffer_data[i].enuObjType;
        elm.eMeasType = buffer_data[i].eMeasType;
        elm.u16MasterHeight = buffer_data[i].u16MasterHeight;
        elm.u16MasterDis = buffer_data[i].u16MasterDis;
        elm.enuSnsCh = buffer_data[i].enuSnsCh;

        StackElemPush(elm);
        while (!StackElemEmpty())
        {
            ObjCoorInfoType elm;
            GetStackElemTop(&elm);
            StackElemPop();
            ListElemInsert(elm);

            float target[] = { elm.fObjX, elm.fObjY };
            for (int j = 0; j < indexCnt; j++)
            {
                if (flags[j] == 1)
                {
                    continue;
                }

                float dist_square = (target[0] - buffer_data[j].fObjX)*(target[0] - buffer_data[j].fObjX) +
                                    (target[1] - buffer_data[j].fObjY)*(target[1] - buffer_data[j].fObjY);

                /* 聚类的类间距离设置为 300mm,此参数可以根据后期实际效果做调整 */ 
                if (dist_square < 300 * 300)
                {
                    StackElemPush(buffer_data[j]);
                    flags[j] = 1;
                }
            }
        }

        /* 聚类后的点集数量 < 5,则认为该类别为噪点；*/
        int pointCnt = ListElemLength();
        if (pointCnt < 5) { continue; }

        /* 每一个聚类点云，会被计算出一个 map,限制 map 的数量不能超过 20 个时 */
        if (cluster_count < VHE_MAX_MAP_NUM - 1)
        {
            PCADirCal(G_ListElem.Buf, &g_vheBuffer->VheMap[cluster_count]);
            cluster_count++;
            g_vheBuffer->u8VheMapNum = cluster_count;
        }
    }
}



/******************************************************************************
 * 函数名称: UpdateMapCarCoor
 *
 * 功能描述: 更新 map 缓存中障碍物的车辆坐标位置
 *
 * 输入参数: 无
 *
 * 输出参数: 无
 *
 * 返回值: 无
 *
 * 其它说明: 解决在可视化中，map 随车一同移动的问题
 *
 * 修改日期              版本号        修改人          修改内容
 * 2024-01-27 21:23      V0.1          wk_zhong        初次发布
 ******************************************************************************/
void UpdateMapCarCoor(void)
{
    VehiclePointCloudBufType* g_vheBuffer;
    g_vheBuffer = &GstrVehiclePointCloudBuf;

    for (int i = 0; i < g_vheBuffer->u8VheMapNum; i++)
    {
        PubAI_TransObjOdoCoorToCarCoor(&g_vheBuffer->VheMap[i].P1_OdoCoor.fObjX, &g_vheBuffer->VheMap[i].P1_OdoCoor.fObjY,
                                       &g_vheBuffer->VheMap[i].P1_CarCoor.fObjX, &g_vheBuffer->VheMap[i].P1_CarCoor.fObjY);

        PubAI_TransObjOdoCoorToCarCoor(&g_vheBuffer->VheMap[i].P2_OdoCoor.fObjX, &g_vheBuffer->VheMap[i].P2_OdoCoor.fObjY,
                                       &g_vheBuffer->VheMap[i].P2_CarCoor.fObjX, &g_vheBuffer->VheMap[i].P2_CarCoor.fObjY);
    }
}

/******************************************************************************
 * 函数名称: CalTwoSegLineDist
 *
 * 功能描述: 计算两个线段之间的距离，相交为“0”
 *
 * 输入参数: 线段 1 的 X 坐标、线段 1 的 Y 坐标、线段 2 的 X 坐标、线段 2 的 Y 坐标
 *
 * 输出参数: 无
 *
 * 返回值: 两线段的距离
 *
 * 其它说明: 无
 *
 * 修改日期              版本号        修改人          修改内容
 * 2024-01-27 21:23      V0.1          wk_zhong        初次发布
 ******************************************************************************/
float CalTwoSegLineDist(ObjCoorType line1_p1, ObjCoorType line1_p2, ObjCoorType line2_p1, ObjCoorType line2_p2)
{
    int is_cross;
    float dist;
    Pub_Point2LineType type1, type2, type3, type4;
    type1 = PubAI_CalPoint2LineRelation(line1_p1.fObjX, line1_p1.fObjY, line1_p2.fObjX, line1_p2.fObjY, line2_p1.fObjX, line2_p1.fObjY);
    type2 = PubAI_CalPoint2LineRelation(line1_p1.fObjX, line1_p1.fObjY, line1_p2.fObjX, line1_p2.fObjY, line2_p2.fObjX, line2_p2.fObjY);
    type3 = PubAI_CalPoint2LineRelation(line2_p1.fObjX, line2_p1.fObjY, line2_p2.fObjX, line2_p2.fObjY, line1_p1.fObjX, line1_p1.fObjY);
    type4 = PubAI_CalPoint2LineRelation(line2_p1.fObjX, line2_p1.fObjY, line2_p2.fObjX, line2_p2.fObjY, line1_p2.fObjX, line1_p2.fObjY);

    if (type1 != type2 && type3 != type4)
    {
        is_cross = 1;
        dist = 0;
    }
    else
    {
        is_cross = 0;
    }

    if (is_cross == 0)
    {
        float dist1 = PubAI_CalOnePointToSegmentDis(line1_p1.fObjX, line1_p1.fObjY, line2_p1.fObjX, line2_p1.fObjY, line2_p2.fObjX, line2_p2.fObjY);
        float dist2 = PubAI_CalOnePointToSegmentDis(line1_p2.fObjX, line1_p2.fObjY, line2_p1.fObjX, line2_p1.fObjY, line2_p2.fObjX, line2_p2.fObjY);
        float dist3 = PubAI_CalOnePointToSegmentDis(line2_p1.fObjX, line2_p1.fObjY, line1_p1.fObjX, line1_p1.fObjY, line1_p2.fObjX, line1_p2.fObjY);
        float dist4 = PubAI_CalOnePointToSegmentDis(line2_p2.fObjX, line2_p2.fObjY, line1_p1.fObjX, line1_p1.fObjY, line1_p2.fObjX, line1_p2.fObjY);
        dist = MIN_VALUE(MIN_VALUE(MIN_VALUE(dist1, dist2), dist3), dist4);
    }

    return dist;
}

/******************************************************************************
 * 函数名称: CalTwoMapDist
 *
 * 功能描述: 计算两个map之间的距离值
 *
 * 输入参数: map1 的 X 坐标、map1 的 Y 坐标、map2 的 X 坐标、map2 的 Y 坐标
 *
 * 输出参数: 无
 *
 * 返回值: 两 map 的距离
 *
 * 其它说明: 无
 *
 * 修改日期              版本号        修改人          修改内容
 * 2024-03-21 21:23      V0.1          wk_zhong        初次发布
 ******************************************************************************/
float CalTwoMapDist(ObjCoorType map1_p1, ObjCoorType map1_p2, ObjCoorType map2_p1, ObjCoorType map2_p2)
{
    float map_dist = 0;

    if ((map1_p1.fObjX == map1_p2.fObjX && map1_p1.fObjY == map1_p2.fObjY) &&           // map1为点状map
        (map2_p1.fObjX == map2_p2.fObjX && map2_p1.fObjY == map2_p2.fObjY))             // map2为点状map
    {
        map_dist = PubAI_CalTwoPointDis(map1_p1.fObjX, map1_p1.fObjY, map2_p1.fObjX, map2_p1.fObjY);
    }
    else if (!(map1_p1.fObjX == map1_p2.fObjX && map1_p1.fObjY == map1_p2.fObjY) &&     // map1为线状map
              (map2_p1.fObjX == map2_p2.fObjX && map2_p1.fObjY == map2_p2.fObjY))       // map2为点状map
    {
        map_dist = PubAI_CalOnePointToSegmentDis(map2_p1.fObjX, map2_p1.fObjY, map1_p1.fObjX, map1_p1.fObjY, map1_p2.fObjX, map1_p2.fObjY);
    }
    else if ( (map1_p1.fObjX == map1_p2.fObjX && map1_p1.fObjY == map1_p2.fObjY) &&     // map1为点状map
             !(map2_p1.fObjX == map2_p2.fObjX && map2_p1.fObjY == map2_p2.fObjY))       // map2为线状map
    {
        map_dist = PubAI_CalOnePointToSegmentDis(map1_p1.fObjX, map1_p1.fObjY, map2_p1.fObjX, map2_p1.fObjY, map2_p2.fObjX, map2_p2.fObjY);
    }
    else // map1 和 map2 均为线状map
    {
        map_dist = CalTwoSegLineDist(map1_p1, map1_p2, map2_p1, map2_p2);
    }

    return map_dist;
}

/* 判定map所属保杠的对应位置区域 */
static ObjMAPAreaType JudgeObjInArea(PDCSnsGroupType LenuSnsGroup, float LfObjX, float LfObjY)
{
    ObjMAPAreaType LenuObjArea = MAP_AREA_NONE;
    Pub_Point2LineType LenuPoint2LineRelation;

    ObjMapAreaLineType LenuStartLine;
    ObjMapAreaLineType LenuEndLine;
    ObjMapAreaLineType LenuLineInx;
    float P1_X;
    float P1_Y;
    float P2_X;
    float P2_Y;

    if (LfObjY > 0)
    {
        /* 障碍物在保杠的左侧，从中间往左边找 */
        LenuStartLine = MAP_LINE3_4;
        LenuEndLine = MAP_LINE0_1;
        for (LenuLineInx = MAP_LINE3_4; LenuLineInx >= MAP_LINE0_1; LenuLineInx--)
        {
            if (LenuSnsGroup == PDC_SNS_GROUP_FRONT)
            {
                /* 此处左右的相对视角是以P1点指向P2点来看的 */
                P2_X = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaEnd_X;
                P2_Y = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaEnd_Y;
                P1_X = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaStart_X;
                P1_Y = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaStart_Y;
            }
            else
            {
                /* 此处左右的相对视角是以P1点指向P2点来看的 */
                P1_X = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaEnd_X;
                P1_Y = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaEnd_Y;
                P2_X = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaStart_X;
                P2_Y = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaStart_Y;
            }

            LenuPoint2LineRelation = PubAI_CalPoint2LineRelation(P1_X, P1_Y, P2_X, P2_Y, LfObjX, LfObjY);

            if (LenuLineInx == LenuEndLine)
            {
                if ((LenuPoint2LineRelation == PUB_POINT_ON_LINE_RIGHT) || (LenuPoint2LineRelation == PUB_POINT_ON_LINE))
                {
                    return MAP_AREA1;
                }
                else
                {
                    return MAP_AREA0;
                }
            }
            else
            {
                if ((LenuPoint2LineRelation == PUB_POINT_ON_LINE_RIGHT) || (LenuPoint2LineRelation == PUB_POINT_ON_LINE))
                {
                    LenuObjArea = (ObjMAPAreaType)(LenuLineInx + 1);
                    return LenuObjArea;
                }
            }
        }
    }
    else
    {
        /* 障碍物在保杠的右侧，从中间往右边找 */
        LenuStartLine = MAP_LINE4_5;
        LenuEndLine = MAP_LINE7_8;

        for (LenuLineInx = MAP_LINE4_5; LenuLineInx <= MAP_LINE7_8; LenuLineInx++)
        {
            if (LenuSnsGroup == PDC_SNS_GROUP_FRONT)
            {
                /* 此处左右的相对视角是以P1点指向P2点来看的 */
                P2_X = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaEnd_X;
                P2_Y = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaEnd_Y;
                P1_X = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaStart_X;
                P1_Y = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaStart_Y;
            }
            else
            {
                /* 此处左右的相对视角是以P1点指向P2点来看的 */
                P1_X = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaEnd_X;
                P1_Y = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaEnd_Y;
                P2_X = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaStart_X;
                P2_Y = GstrMapAreaRam[LenuSnsGroup][LenuLineInx].fAreaStart_Y;
            }

            LenuPoint2LineRelation = PubAI_CalPoint2LineRelation(P1_X, P1_Y, P2_X, P2_Y, LfObjX, LfObjY);

            if (LenuLineInx == LenuEndLine)
            {
                if (LenuPoint2LineRelation < PUB_POINT_ON_LINE_RIGHT)
                {
                    return MAP_AREA7;
                }
                else
                {
                    return MAP_AREA8;
                }
            }
            else
            {
                if (LenuPoint2LineRelation < PUB_POINT_ON_LINE_RIGHT)
                {
                    LenuObjArea = (ObjMAPAreaType)(LenuLineInx);
                    return LenuObjArea;
                }
            }
        }
    }

    return LenuObjArea;
}


/******************************************************************************
 * 函数名称: CalPointToBumperRelationAndDis
 *
 * 功能描述: 计算一个点到保杠的关系以及距离
 *
 * 输入参数:LpstrPointCarCoor--待计算点的坐标指针；LpstrPointToBumperDis--待返回Bumper距离的指针
 *
 * 输出参数:无
 *
 * 返回值:无
 *
 * 其它说明:无
 *
 * 修改日期              版本号        修改人          修改内容
 * 2023-04-23 11:33   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static ObjMAPAreaType CalPointToBumperRelationAndDis(ObjCoorType *LpstrPointCarCoor, float *LpstrPointToBumperDis)
{
    ObjMAPAreaType LenuMapDetailArea = MAP_AREA_NONE;
    ObjCoorType LstrBumperStartPoint;
    ObjCoorType LstrBumperEndPoint;
    float LfMinAreaX;

    if (LpstrPointCarCoor->fObjX > (GstrMapAreaRam[PDC_SNS_GROUP_FRONT][MAP_LINE0_1].fAreaStart_X))
    {
        LenuMapDetailArea = JudgeObjInArea(PDC_SNS_GROUP_FRONT, LpstrPointCarCoor->fObjX, LpstrPointCarCoor->fObjY);
        if ((LenuMapDetailArea < MAP_AREA1))
        {
            *LpstrPointToBumperDis = LpstrPointCarCoor->fObjY - GstrMapAreaRam[PDC_SNS_GROUP_FRONT][MAP_LINE0_1].fAreaStart_Y;
        }
        else if (LenuMapDetailArea > MAP_AREA7)
        {
            *LpstrPointToBumperDis = -(LpstrPointCarCoor->fObjY - GstrMapAreaRam[PDC_SNS_GROUP_FRONT][MAP_LINE7_8].fAreaStart_Y);
        }
        else
        {
            LstrBumperStartPoint.fObjX = GstrMapAreaRam[PDC_SNS_GROUP_FRONT][LenuMapDetailArea - 1].fAreaStart_X;
            LstrBumperStartPoint.fObjY = GstrMapAreaRam[PDC_SNS_GROUP_FRONT][LenuMapDetailArea - 1].fAreaStart_Y;
            LstrBumperEndPoint.fObjX = GstrMapAreaRam[PDC_SNS_GROUP_FRONT][LenuMapDetailArea].fAreaStart_X;
            LstrBumperEndPoint.fObjY = GstrMapAreaRam[PDC_SNS_GROUP_FRONT][LenuMapDetailArea].fAreaStart_Y;

            *LpstrPointToBumperDis = PubAI_CalOnePointToSegmentDis(LpstrPointCarCoor->fObjX, LpstrPointCarCoor->fObjY,
                                                                   LstrBumperStartPoint.fObjX, LstrBumperStartPoint.fObjY,
                                                                   LstrBumperEndPoint.fObjX, LstrBumperEndPoint.fObjY);

            if ((LenuMapDetailArea > MAP_AREA2) && (LenuMapDetailArea < MAP_AREA6))
            {
                LfMinAreaX = GstrMapAreaRam[PDC_SNS_GROUP_FRONT][LenuMapDetailArea - 1].fAreaStart_X;
                if (LfMinAreaX > GstrMapAreaRam[PDC_SNS_GROUP_FRONT][LenuMapDetailArea].fAreaStart_X)
                {
                    LfMinAreaX = GstrMapAreaRam[PDC_SNS_GROUP_FRONT][LenuMapDetailArea].fAreaStart_X;
                }

                if (LpstrPointCarCoor->fObjX < (LfMinAreaX - 40))
                {
                    *LpstrPointToBumperDis = 0;
                }
            }
        }
    }
    else if (LpstrPointCarCoor->fObjX < (GstrMapAreaRam[PDC_SNS_GROUP_REAR][MAP_LINE0_1].fAreaStart_X))
    {
        LenuMapDetailArea = JudgeObjInArea(PDC_SNS_GROUP_REAR, LpstrPointCarCoor->fObjX, LpstrPointCarCoor->fObjY);
        if ((LenuMapDetailArea < MAP_AREA1))
        {
            *LpstrPointToBumperDis = LpstrPointCarCoor->fObjY - GstrMapAreaRam[PDC_SNS_GROUP_REAR][MAP_LINE0_1].fAreaStart_Y;
        }
        else if (LenuMapDetailArea > MAP_AREA7)
        {
            *LpstrPointToBumperDis = -(LpstrPointCarCoor->fObjY - GstrMapAreaRam[PDC_SNS_GROUP_REAR][MAP_LINE7_8].fAreaStart_Y);
        }
        else
        {
            LstrBumperStartPoint.fObjX = GstrMapAreaRam[PDC_SNS_GROUP_REAR][LenuMapDetailArea - 1].fAreaStart_X;
            LstrBumperStartPoint.fObjY = GstrMapAreaRam[PDC_SNS_GROUP_REAR][LenuMapDetailArea - 1].fAreaStart_Y;
            LstrBumperEndPoint.fObjX = GstrMapAreaRam[PDC_SNS_GROUP_REAR][LenuMapDetailArea].fAreaStart_X;
            LstrBumperEndPoint.fObjY = GstrMapAreaRam[PDC_SNS_GROUP_REAR][LenuMapDetailArea].fAreaStart_Y;

            *LpstrPointToBumperDis = PubAI_CalOnePointToSegmentDis(LpstrPointCarCoor->fObjX, LpstrPointCarCoor->fObjY,
                                                                   LstrBumperStartPoint.fObjX, LstrBumperStartPoint.fObjY,
                                                                   LstrBumperEndPoint.fObjX, LstrBumperEndPoint.fObjY);

            if ((LenuMapDetailArea > MAP_AREA2) && (LenuMapDetailArea < MAP_AREA6))
            {
                LfMinAreaX = GstrMapAreaRam[PDC_SNS_GROUP_REAR][LenuMapDetailArea - 1].fAreaStart_X;
                if (LfMinAreaX < GstrMapAreaRam[PDC_SNS_GROUP_REAR][LenuMapDetailArea].fAreaStart_X)
                {
                    LfMinAreaX = GstrMapAreaRam[PDC_SNS_GROUP_REAR][LenuMapDetailArea].fAreaStart_X;
                }

                if (LpstrPointCarCoor->fObjX > (LfMinAreaX + 40))
                {
                    *LpstrPointToBumperDis = 0;
                }
            }
            if ((LenuMapDetailArea == MAP_AREA3) || (LenuMapDetailArea == MAP_AREA5))
            {
                if (*LpstrPointToBumperDis > REAR_MAP_TO_BUMPER_DIS_CMP_CONNER)
                {
                    *LpstrPointToBumperDis = *LpstrPointToBumperDis - REAR_MAP_TO_BUMPER_DIS_CMP_CONNER;
                }
                else
                {
                    *LpstrPointToBumperDis = 0;
                }
            }
        }
    }
    else
    {
        if (LpstrPointCarCoor->fObjY > 900)
        {
            LenuMapDetailArea = MAP_AREA0;
            *LpstrPointToBumperDis = LpstrPointCarCoor->fObjY - GstrMapAreaRam[PDC_SNS_GROUP_FRONT][MAP_LINE0_1].fAreaStart_Y;
        }
        else if (LpstrPointCarCoor->fObjY < -900)
        {
            LenuMapDetailArea = MAP_AREA8;
            *LpstrPointToBumperDis = -(LpstrPointCarCoor->fObjY - GstrMapAreaRam[PDC_SNS_GROUP_FRONT][MAP_LINE7_8].fAreaStart_Y);
        }
        else
        {
            LenuMapDetailArea = MAP_AREA_NONE;
            *LpstrPointToBumperDis = 0;
        }
    }

    return LenuMapDetailArea;
}

static void JudgeMapBumperArea(ObjMapCoorType *LpstrMapCoor)
{
    /* 更新Map在前后侧保杠的位置区域 */
    float LfFrontBumperX_Line, LfRearBumperX_Line;

    LfFrontBumperX_Line = GstrRamSnsCoor[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FLS].cRadarX - MAP_JUDGE_F_BUMPER_AREA_X_LINE_LIMIT;
    LfRearBumperX_Line = GstrRamSnsCoor[PDC_SNS_GROUP_REAR][PDC_SNS_CH_RLS].cRadarX + MAP_JUDGE_R_BUMPER_AREA_X_LINE_LIMIT;

    if ((LpstrMapCoor->P1_CarCoor.fObjX > LfFrontBumperX_Line) && (LpstrMapCoor->P2_CarCoor.fObjX > LfFrontBumperX_Line))
    {
        LpstrMapCoor->eObjBumperArea = OBJ_FRONT_BUMP;
    }
    else if ((LpstrMapCoor->P1_CarCoor.fObjX < LfRearBumperX_Line) && (LpstrMapCoor->P2_CarCoor.fObjX < LfRearBumperX_Line))
    {
        LpstrMapCoor->eObjBumperArea = OBJ_REAR_BUMP;
    }
    else
    {
        if (LpstrMapCoor->P1_CarCoor.fObjY > 0)
        {
            LpstrMapCoor->eObjBumperArea = OBJ_LEFT_SIDE_BUMP;
        }
        else
        {
            LpstrMapCoor->eObjBumperArea = OBJ_RIGHT_SIDE_BUMP;
        }
    }

    float front_x_throsheld = GstrRamSnsCoor[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FLS].cRadarX - 100;
    float rear_x_throsheld = GstrRamSnsCoor[PDC_SNS_GROUP_REAR][PDC_SNS_CH_RLS].cRadarX + 100;
    if ((LpstrMapCoor->P1_CarCoor.fObjX < front_x_throsheld && LpstrMapCoor->P1_CarCoor.fObjX >rear_x_throsheld) ||
        (LpstrMapCoor->P2_CarCoor.fObjX < front_x_throsheld && LpstrMapCoor->P2_CarCoor.fObjX >rear_x_throsheld))
    {
        LpstrMapCoor->eObjBumperArea = OBJ_NONE_BUMP;
    }
}

/* 判定map是否有对应的DE值与之相匹配 */
int JudgeMapMatchedSnsDe(ObjMapCoorType *LpstrMapCoor, float *real_de_dist, int *matched_channel)
{
    int result = 0;
    int index_group;
    int index_ch1;
    int index_ch2;
    int final_index;
    ObjCoorType map_car_coor;
    float throsheld = 200;

    // 获取传感器所在组的索引
    if (LpstrMapCoor->eObjBumperArea == OBJ_FRONT_BUMP)
    {
        index_group = 0;
    }
    else if (LpstrMapCoor->eObjBumperArea == OBJ_REAR_BUMP)
    {
        index_group = 1;
    }
    else//如果 map 在侧面，则直接不进行DE的匹配
    {
        return result;
    }

    float map_to_bump_dist = 0;

    // 获取传感器所在通道的索引
    ObjMAPAreaType map_area;
    if (LpstrMapCoor->eMapObjType == OBJ_TYPE_POINT)
    {
        map_to_bump_dist = LpstrMapCoor->fMapToBumper_Dis;
        map_area = LpstrMapCoor->eP1_DetailArea;
        map_car_coor.fObjX = LpstrMapCoor->P1_CarCoor.fObjX;
        map_car_coor.fObjY = LpstrMapCoor->P1_CarCoor.fObjY;
    }
    else if (LpstrMapCoor->eMapObjType == OBJ_TYPE_STRAIGHT2_CORNER)
    {
        map_car_coor.fObjX = (LpstrMapCoor->P1_CarCoor.fObjX + LpstrMapCoor->P2_CarCoor.fObjX) / 2;
        map_car_coor.fObjY = (LpstrMapCoor->P1_CarCoor.fObjY + LpstrMapCoor->P2_CarCoor.fObjY) / 2;
        map_area = CalPointToBumperRelationAndDis(&map_car_coor, &map_to_bump_dist);
    }
    else
    {
        return result;
    }

    if (map_area < 2 || map_area > 6)
    {
        return result;
    }

    if (map_area == MAP_AREA2)
    {
        final_index = PDC_SNS_CH_FL_RL;
    }

    if (map_area == MAP_AREA6)
    {
        final_index = PDC_SNS_CH_FR_RR;
    }

    if (map_area == MAP_AREA3)
    {
        index_ch1 = PDC_SNS_CH_FL_RL;
        index_ch2 = PDC_SNS_CH_FML_RML;
    }

    if (map_area == MAP_AREA4)
    {
        index_ch1 = PDC_SNS_CH_FML_RML;
        index_ch2 = PDC_SNS_CH_FMR_RMR;
    }

    if (map_area == MAP_AREA5)
    {
        index_ch1 = PDC_SNS_CH_FMR_RMR;
        index_ch2 = PDC_SNS_CH_FR_RR;
    }

    int matched_flag1 = 0;
    int matched_flag2 = 0;
    if (map_area == MAP_AREA3 || map_area == MAP_AREA4 || map_area == MAP_AREA5)
    {
        if (GstrMapSnsRealTimeDis[index_group][index_ch1].u8RealDisUpdateFlag == 1)
        {
            float dist_diff = 9999;
            float real_dist = 9999;
            real_dist = (float)GstrMapSnsRealTimeDis[index_group][index_ch1].u16MasDis;
            dist_diff = (real_dist - map_to_bump_dist);
            //printf("timestamp = %u,index_group = %d, index_ch = %d, dist_diff = %f, real_dist = %f\n", GdSystemMsTimer, index_group, index_ch1, dist_diff, real_dist);
            if (ABS_VALUE(dist_diff) < throsheld)
            {
                matched_flag1 = 1;
                *real_de_dist = real_dist;
                *matched_channel = index_ch1;
                if (real_dist < LpstrMapCoor->fNearestDE)
                {
                    LpstrMapCoor->fNearestDE = real_dist;
                }

                /* 记录map二次回波的个数 */
                if (real_dist < 2000 &&
                    (GstrMapSnsRealTimeDis[index_group][index_ch1].u8MasSecondFlag == 1 ||
                        GstrMapSnsRealTimeDis[index_group][index_ch1].u8LeftSecondFlag == 1 ||
                        GstrMapSnsRealTimeDis[index_group][index_ch1].u8RightSecondFlag == 1))
                {
                    if (LpstrMapCoor->u8TwoEchoDeMatchedCnt < 250)
                    {
                        LpstrMapCoor->u8TwoEchoDeMatchedCnt++;
                    }
                }
            }
            else
            {
                matched_flag1 = 2;
            }
        }

        if (GstrMapSnsRealTimeDis[index_group][index_ch2].u8RealDisUpdateFlag == 1)
        {
            float dist_diff = 9999;
            float real_dist = 9999;
            real_dist = (float)GstrMapSnsRealTimeDis[index_group][index_ch2].u16MasDis;
            dist_diff = (real_dist - map_to_bump_dist);
            //printf("timestamp = %u,index_group = %d, index_ch = %d, dist_diff = %f, real_dist = %f\n", GdSystemMsTimer, index_group, index_ch2, dist_diff, real_dist);
            if (ABS_VALUE(dist_diff) < throsheld)
            {
                matched_flag2 = 1;
                if (matched_flag1 == 1)
                {
                    if (*real_de_dist > real_dist)
                    {
                        *real_de_dist = real_dist;
                        *matched_channel = index_ch2;
                        if (real_dist < LpstrMapCoor->fNearestDE)
                        {
                            LpstrMapCoor->fNearestDE = real_dist;
                        }
                    }
                }
                else
                {
                    *real_de_dist = real_dist;
                    *matched_channel = index_ch2;
                    if (real_dist < LpstrMapCoor->fNearestDE)
                    {
                        LpstrMapCoor->fNearestDE = real_dist;
                    }
                }

                /* 记录map二次回波的个数 */
                if (real_dist < 2000 &&
                    (GstrMapSnsRealTimeDis[index_group][index_ch2].u8MasSecondFlag == 1 ||
                        GstrMapSnsRealTimeDis[index_group][index_ch2].u8LeftSecondFlag == 1 ||
                        GstrMapSnsRealTimeDis[index_group][index_ch2].u8RightSecondFlag == 1))
                {
                    if (LpstrMapCoor->u8TwoEchoDeMatchedCnt < 250)
                    {
                        LpstrMapCoor->u8TwoEchoDeMatchedCnt++;
                    }
                }
            }
            else
            {
                matched_flag2 = 2;
            }
        }
    }
    else if (map_area == MAP_AREA2 || map_area == MAP_AREA6)
    {
        // 比较保险杠到map的距离，和当前时刻DE的值。
        float dist_diff = 9999;
        float real_dist = 9999;
        if (GstrMapSnsRealTimeDis[index_group][final_index].u8RealDisUpdateFlag == 1)
        {
            real_dist = (float)GstrMapSnsRealTimeDis[index_group][final_index].u16MasDis;
            dist_diff = (real_dist - map_to_bump_dist);
            //printf("timestamp = %u,index_group = %d, index_ch = %d, dist_diff = %f, real_dist = %f\n", GdSystemMsTimer, index_group, final_index, dist_diff, real_dist);
            if (ABS_VALUE(dist_diff) < throsheld)
            {
                matched_flag1 = 1;
                matched_flag2 = 1;
                *real_de_dist = real_dist;
                *matched_channel = final_index;
                if (real_dist < LpstrMapCoor->fNearestDE)
                {
                    LpstrMapCoor->fNearestDE = real_dist;
                }

                /* 记录map二次回波的个数 */
                if (real_dist < 2000 &&
                    (GstrMapSnsRealTimeDis[index_group][final_index].u8MasSecondFlag == 1 ||
                        GstrMapSnsRealTimeDis[index_group][final_index].u8LeftSecondFlag == 1 ||
                        GstrMapSnsRealTimeDis[index_group][final_index].u8RightSecondFlag == 1))
                {
                    if (LpstrMapCoor->u8TwoEchoDeMatchedCnt < 250)
                    {
                        LpstrMapCoor->u8TwoEchoDeMatchedCnt++;
                    }
                }
            }
            else
            {
                matched_flag1 = 2;
                matched_flag2 = 2;
            }
        }
        else
        {
            return result;
        }
    }
    else
    {
        return result;
    }

    if (matched_flag1 == 1 || matched_flag2 == 1)
    {
        LpstrMapCoor->u8Map_DE_NoMatchCnt = 0;
        result = 1;
    }
    else if (matched_flag1 == 2 || matched_flag2 == 2)
    {
        LpstrMapCoor->u8Map_DE_NoMatchCnt++;
    }
    else
    {
        return result;
    }

    return result;
}

/******************************************************************************
 * 函数名称: JudgeMapMoveSts
 *
 * 功能描述: 判断Map和车辆的运动关系
 *
 * 输入参数:LpstrMapCoor--Map结构体指针
 *
 * 输出参数:无
 *
 * 返回值:无
 *
 * 其它说明:无
 *
 * 修改日期              版本号        修改人          修改内容
 * 2023-11-29 10:36   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void JudgeMapMoveSts(ObjMapCoorType *LpstrMapCoor)
{
    /* 更新障碍物和车辆的相对运动状态 */
    if (LpstrMapCoor->eObjBumperArea == OBJ_FRONT_BUMP)
    {
        if (GstrSnsCarMovSts[0][2].eCarDir == SNS_CAR_FORWARD)
        {
            LpstrMapCoor->eObjMoveSts = OBJ_TO_CAR_MOVE_CLOSE;
            if (!LpstrMapCoor->u8CarMoveCloseMapFlag)
            {
                LpstrMapCoor->u8CarMoveCloseMapCnt++;
            }
            LpstrMapCoor->u32MapToCarStopTime = GdSystemMsTimer;
        }
        else if (GstrSnsCarMovSts[0][2].eCarDir == SNS_CAR_BACKWARD)
        {
            LpstrMapCoor->eObjMoveSts = OBJ_TO_CAR_MOVE_FARAWAY;
            LpstrMapCoor->u8CarMoveCloseMapFlag = 0;
            LpstrMapCoor->u8CarMoveCloseMapCnt = 0;
            LpstrMapCoor->u32MapToCarStopTime = GdSystemMsTimer;
        }
        else
        {
            if (GdSystemMsTimer > (LpstrMapCoor->u32MapToCarStopTime + MAP_MOVE_TO_STOP_HOLD_TIME))
            {
                LpstrMapCoor->eObjMoveSts = OBJ_TO_CAR_STOP_STOP;
            }
        }
    }
    else if (LpstrMapCoor->eObjBumperArea == OBJ_REAR_BUMP)
    {
        if (GstrSnsCarMovSts[1][2].eCarDir == SNS_CAR_FORWARD)
        {
            LpstrMapCoor->eObjMoveSts = OBJ_TO_CAR_MOVE_FARAWAY;
            LpstrMapCoor->u8CarMoveCloseMapFlag = 0;
            LpstrMapCoor->u8CarMoveCloseMapCnt = 0;
            LpstrMapCoor->u32MapToCarStopTime = GdSystemMsTimer;
        }
        else if (GstrSnsCarMovSts[1][2].eCarDir == SNS_CAR_BACKWARD)
        {
            LpstrMapCoor->eObjMoveSts = OBJ_TO_CAR_MOVE_CLOSE;
            if (!LpstrMapCoor->u8CarMoveCloseMapFlag)
            {
                LpstrMapCoor->u8CarMoveCloseMapCnt++;
            }
            LpstrMapCoor->u32MapToCarStopTime = GdSystemMsTimer;
        }
        else
        {
            if (GdSystemMsTimer > (LpstrMapCoor->u32MapToCarStopTime + MAP_MOVE_TO_STOP_HOLD_TIME))
            {
                LpstrMapCoor->eObjMoveSts = OBJ_TO_CAR_STOP_STOP;
            }
        }
    }
    else
    {
        LpstrMapCoor->eObjMoveSts = OBJ_IN_SIDE_MOVE;
        LpstrMapCoor->u8CarMoveCloseMapFlag = 0;
        LpstrMapCoor->u8CarMoveCloseMapCnt = 0;
        LpstrMapCoor->u32MapToCarStopTime = GdSystemMsTimer;
    }

    /* 判断Map靠近跟踪的标志 */
    if (LpstrMapCoor->u8CarMoveCloseMapCnt > F_R_MAP_CAR_MOVE_CLOSE_CNT)
    {
        LpstrMapCoor->u8CarMoveCloseMapCnt = 0;
        LpstrMapCoor->u8CarMoveCloseMapFlag = 1;
    }
}

/******************************************************************************
 * 函数名称: MapInBlindAreaExistObjJudge
 *
 * 功能描述: 判断盲区内是否存在障碍物
 *
 * 输入参数:无
 *
 * 输出参数:无
 *
 * 返回值:无
 *
 * 其它说明:无
 *
 * 修改日期              版本号        修改人          修改内容
 * 2023-12-16 15:06   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void MapInBlindAreaExistObjJudge(ObjMapCoorType *LpstrMapCoor)
{
    PDCSnsChannelType LenuLeftCh, LenuRightCh;
    PDCSnsGroupType LenuSnsGroup;
    uint8 Lu8MasterHaveObjFlag = 0;
    uint8 Lu8ListenHaveObjFlag = 0;
    ObjMAPAreaType LenuNearPointDetailArea;

    if (LpstrMapCoor->eMapObjType == OBJ_TYPE_POINT)
    {
        LenuNearPointDetailArea = LpstrMapCoor->eP1_DetailArea;
    }
    else
    {
        if (LpstrMapCoor->fP1_ToBumper_Dis < LpstrMapCoor->fP2_ToBumper_Dis)
        {
            LenuNearPointDetailArea = LpstrMapCoor->eP1_DetailArea;
        }
        else
        {
            LenuNearPointDetailArea = LpstrMapCoor->eP2_DetailArea;
        }
    }

    if ((LenuNearPointDetailArea == MAP_AREA1) || (LenuNearPointDetailArea == MAP_AREA2))
    {
        LenuLeftCh = PDC_SNS_CH_FLS_RLS;
        LenuRightCh = LenuLeftCh + 1;
    }
    else if (LenuNearPointDetailArea == MAP_AREA3)
    {
        LenuLeftCh = PDC_SNS_CH_FL_RL;
        LenuRightCh = LenuLeftCh + 1;
    }
    else if (LenuNearPointDetailArea == MAP_AREA4)
    {
        LenuLeftCh = PDC_SNS_CH_FML_RML;
        LenuRightCh = LenuLeftCh + 1;
    }
    else if (LenuNearPointDetailArea == MAP_AREA5)
    {
        LenuLeftCh = PDC_SNS_CH_FMR_RMR;
        LenuRightCh = LenuLeftCh + 1;
    }
    else if ((LenuNearPointDetailArea == MAP_AREA6) || (LenuNearPointDetailArea == MAP_AREA7))
    {
        LenuLeftCh = PDC_SNS_CH_FR_RR;
        LenuRightCh = LenuLeftCh + 1;
    }
    else
    {
        LpstrMapCoor->u8BlindNoObjFlag = 0;
        return;
    }

    if (LpstrMapCoor->eObjBumperArea == OBJ_FRONT_BUMP)
    {
        LenuSnsGroup = PDC_SNS_GROUP_FRONT;
    }
    else if (LpstrMapCoor->eObjBumperArea == OBJ_REAR_BUMP)
    {
        LenuSnsGroup = PDC_SNS_GROUP_REAR;
    }
    else
    {
        LpstrMapCoor->u8BlindNoObjFlag = 0;
        return;
    }

    if ((GstrMapSnsRealTimeDis[LenuSnsGroup][LenuLeftCh].u16MasDis < F_R_MAP_JUDGE_EXIST_OBJ_DIS) || \
        (GstrMapSnsRealTimeDis[LenuSnsGroup][LenuRightCh].u16MasDis < F_R_MAP_JUDGE_EXIST_OBJ_DIS))
    {
        Lu8MasterHaveObjFlag = 1;
    }
    if ((GstrMapSnsRealTimeDis[LenuSnsGroup][LenuLeftCh].u16RightDis < F_R_MAP_JUDGE_EXIST_OBJ_LISTEN_DIS) || \
        (GstrMapSnsRealTimeDis[LenuSnsGroup][LenuRightCh].u16LeftDis < F_R_MAP_JUDGE_EXIST_OBJ_LISTEN_DIS))
    {
        Lu8ListenHaveObjFlag = 1;
    }
#if 0
    printf("Time:%.3f,Near Obj,Id:%d,LeftMaster:%d,RightMaster:%d,Left:%d,Right:%d,LeftCh:%d,RightCh:%d\r\n", GfMessageTime, LpstrMapCoor->eMapObjId, \
        GstrMapSnsRealTimeDis[LenuSnsGroup][LenuLeftCh].u16MasDis, GstrMapSnsRealTimeDis[LenuSnsGroup][LenuRightCh].u16MasDis, \
        GstrMapSnsRealTimeDis[LenuSnsGroup][LenuLeftCh].u16RightDis, GstrMapSnsRealTimeDis[LenuSnsGroup][LenuRightCh].u16LeftDis, \
        LenuLeftCh, LenuRightCh);
#endif
    if (!LpstrMapCoor->u8BlindNoObjFlag)
    {
        if ((Lu8MasterHaveObjFlag == 0) && (Lu8ListenHaveObjFlag == 0))
        {
            if ((GdSystemMsTimer - LpstrMapCoor->u32BlindNoObjTime) > MAP_BLIND_NO_OBJ_TIME)
            {
                LpstrMapCoor->u32BlindNoObjTime = GdSystemMsTimer;
                LpstrMapCoor->u8BlindNoObjFlag = 1;
            }
        }
        else
        {
            LpstrMapCoor->u32BlindNoObjTime = GdSystemMsTimer;
        }
    }
}

/******************************************************************************
 * 函数名称: JudgeMapUpdateSts
 *
 * 功能描述: 判断Map的更新状态
 *
 * 输入参数:LpstrMapCoor--Map结构体指针
 *
 * 输出参数:无
 *
 * 返回值:无
 *
 * 其它说明:无
 *
 * 修改日期              版本号        修改人          修改内容
 * 2023-11-29 10:42   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void JudgeMapUpdateSts(ObjMapCoorType *LpstrMapCoor)
{
    float LfLeftSideBumper_Y_Line, LfRightSideBumper_Y_Line;
    uint16 Lu16BlindEnterDis, Lu16BlindExitDis;
    ObjMAPAreaType LenuLineNearPointArea;

    LfLeftSideBumper_Y_Line = GstrRamSnsCoor[0][0].cRadarY + MAP_JUDGE_BUMPER_AREA_Y_LINE_LIMIT;
    LfRightSideBumper_Y_Line = GstrRamSnsCoor[0][5].cRadarY - MAP_JUDGE_BUMPER_AREA_Y_LINE_LIMIT;

    if (LpstrMapCoor->eMapObjType == OBJ_TYPE_POINT)
    {
        LenuLineNearPointArea = LpstrMapCoor->eP1_DetailArea;
    }
    else
    {
        if (LpstrMapCoor->fP1_ToBumper_Dis < LpstrMapCoor->fP2_ToBumper_Dis)
        {
            LenuLineNearPointArea = LpstrMapCoor->eP1_DetailArea;
        }
        else
        {
            LenuLineNearPointArea = LpstrMapCoor->eP2_DetailArea;
        }
    }

    /* 区分召唤模式和非召唤模式 */
    if (GstrParkingGuidenceData.eParking_Sts == PARKING_SUMMON)
    {
        if ((LenuLineNearPointArea > MAP_AREA2) && (LenuLineNearPointArea < MAP_AREA6))
        {
            if (LenuLineNearPointArea == MAP_AREA4)
            {
                Lu16BlindEnterDis = MAP_BLIND_SUMMON_AREA4_ENTER_DIS;
                Lu16BlindExitDis = MAP_BLIND_SUMMON_AREA4_EXIT_DIS;
            }
            else
            {
                Lu16BlindEnterDis = MAP_BLIND_SUMMON_MID_ENTER_DIS;
                Lu16BlindExitDis = MAP_BLIND_SUMMON_MID_EXIT_DIS;
            }
        }
        else
        {
            Lu16BlindEnterDis = MAP_BLIND_SUMMON_CORNER_ENTER_DIS;
            Lu16BlindExitDis = MAP_BLIND_SUMMON_CORNER_EXIT_DIS;
        }
    }
    else
    {
        if ((LenuLineNearPointArea > MAP_AREA2) && (LenuLineNearPointArea < MAP_AREA6))
        {
            if (LenuLineNearPointArea == MAP_AREA4)
            {
                Lu16BlindEnterDis = MAP_BLIND_AREA4_ENTER_DIS;
                Lu16BlindExitDis = MAP_BLIND_AREA4_EXIT_DIS;
            }
            else
            {
                Lu16BlindEnterDis = MAP_BLIND_MIDDLE_ENTER_DIS;
                Lu16BlindExitDis = MAP_BLIND_MIDDLE_EXIT_DIS;
            }
        }
        else
        {
            Lu16BlindEnterDis = MAP_BLIND_CORNER_ENTER_DIS;
            Lu16BlindExitDis = MAP_BLIND_CORNER_EXIT_DIS;
        }
    }

    /* 更新Map的更新状态，分为更新区、远离记忆区、近距离盲区跟踪 */
    if ((LpstrMapCoor->eObjBumperArea == OBJ_FRONT_BUMP) || (LpstrMapCoor->eObjBumperArea == OBJ_REAR_BUMP))
    {
        if (LpstrMapCoor->eObjFollowSts == OBJ_DET_UPDATE)
        {
            if (LpstrMapCoor->eObjMoveSts == OBJ_TO_CAR_MOVE_CLOSE)
            {
                if (LpstrMapCoor->fMapToBumper_Dis < Lu16BlindEnterDis)
                {
                    LpstrMapCoor->eObjFollowSts = OBJ_BLIND_FOLLOW;
                    LpstrMapCoor->u8CarCloseToMapBlindFlag = 1;
                    LpstrMapCoor->u32BlindNoObjTime = GdSystemMsTimer;
                }
                else
                {
                    LpstrMapCoor->eObjFollowSts = OBJ_DET_UPDATE;
                }
            }
            else if (LpstrMapCoor->eObjMoveSts == OBJ_TO_CAR_MOVE_FARAWAY)
            {
                if (LpstrMapCoor->fMapToBumper_Dis < Lu16BlindEnterDis)
                {
                    LpstrMapCoor->eObjFollowSts = OBJ_BLIND_FOLLOW;
                }
                else if (LpstrMapCoor->fMapToBumper_Dis > MAP_FAR_AWAY_NO_UPDATE_DIS)
                {
                    LpstrMapCoor->eObjFollowSts = OBJ_OUT_DET_FOLLOW;
                }
                else
                {
                    LpstrMapCoor->eObjFollowSts = OBJ_DET_UPDATE;
                }
            }
            else
            {
                if (LpstrMapCoor->fMapToBumper_Dis < MAP_DETECT_ENTER_DIS)
                {
                    LpstrMapCoor->eObjFollowSts = OBJ_DET_UPDATE;
                }
            }
        }
        else if (LpstrMapCoor->eObjFollowSts == OBJ_OUT_DET_FOLLOW)
        {
            if (LpstrMapCoor->eObjMoveSts == OBJ_TO_CAR_MOVE_CLOSE)
            {
                if (LpstrMapCoor->fMapToBumper_Dis < MAP_CLOSE_DETECT_ENTER_DIS)
                {
                    LpstrMapCoor->eObjFollowSts = OBJ_DET_UPDATE;
                }
                else
                {
                    LpstrMapCoor->eObjFollowSts = OBJ_OUT_DET_FOLLOW;
                }
            }
            else if (LpstrMapCoor->eObjMoveSts == OBJ_TO_CAR_MOVE_FARAWAY)
            {
                LpstrMapCoor->eObjFollowSts = OBJ_OUT_DET_FOLLOW;
            }
            else
            {
                if (LpstrMapCoor->fMapToBumper_Dis < MAP_DETECT_ENTER_DIS)
                {
                    LpstrMapCoor->eObjFollowSts = OBJ_DET_UPDATE;
                }
            }
        }
        else if (LpstrMapCoor->eObjFollowSts == OBJ_BLIND_FOLLOW)
        {
            if (LpstrMapCoor->eObjMoveSts == OBJ_TO_CAR_MOVE_CLOSE)
            {
                LpstrMapCoor->eObjFollowSts = OBJ_BLIND_FOLLOW;
                LpstrMapCoor->u8CarCloseToMapBlindFlag = 1;
                LpstrMapCoor->u32BlindNoObjTime = GdSystemMsTimer;
            }
            else if (LpstrMapCoor->eObjMoveSts == OBJ_TO_CAR_MOVE_FARAWAY)
            {
                LpstrMapCoor->u8CarCloseToMapBlindFlag = 0;
                if (LpstrMapCoor->fMapToBumper_Dis > Lu16BlindExitDis)
                {
                    if (LpstrMapCoor->fMapToBumper_Dis < MAP_BLIND_FARAWAY_DETECT_DIS)
                    {
                        LpstrMapCoor->eObjFollowSts = OBJ_DET_UPDATE;
                    }
                    else
                    {
                        LpstrMapCoor->eObjFollowSts = OBJ_OUT_DET_FOLLOW;
                    }
                }
                else
                {
                    LpstrMapCoor->eObjFollowSts = OBJ_BLIND_FOLLOW;
                }
                LpstrMapCoor->u32BlindNoObjTime = GdSystemMsTimer;
            }
            else
            {
                /* 需添加判断近距离有无障碍物的函数 */
                MapInBlindAreaExistObjJudge(LpstrMapCoor);
                if (!LpstrMapCoor->u8BlindNoObjFlag)
                {
                    LpstrMapCoor->eObjFollowSts = OBJ_BLIND_FOLLOW;
                }
                else
                {
                    if (LpstrMapCoor->u8CarCloseToMapBlindFlag)
                    {
                        LpstrMapCoor->u8CarCloseToMapBlindFlag = 0;
                        LpstrMapCoor->u32BlindWaitDisappearTime = GdSystemMsTimer;
                    }
                    if ((GdSystemMsTimer - LpstrMapCoor->u32BlindWaitDisappearTime) < MAP_BLIND_DISAPPERA_WAIT_TIME)
                    {
                        LpstrMapCoor->eObjFollowSts = OBJ_BLIND_FOLLOW;
                    }
                    else
                    {
                        LpstrMapCoor->eObjFollowSts = OBJ_DET_UPDATE;
                        LpstrMapCoor->u8BlindNoObjFlag = 0;
                    }
                }
            }
        }
        else if (LpstrMapCoor->eObjFollowSts == OBJ_SIDE_FOLLOW)
        {
            if (((LpstrMapCoor->P1_CarCoor.fObjY > LfRightSideBumper_Y_Line) && (LpstrMapCoor->P1_CarCoor.fObjY < LfLeftSideBumper_Y_Line)) || \
                ((LpstrMapCoor->P2_CarCoor.fObjY > LfRightSideBumper_Y_Line) && (LpstrMapCoor->P2_CarCoor.fObjY < LfLeftSideBumper_Y_Line)))
            {
                if (LpstrMapCoor->fMapToBumper_Dis < MAP_SIDE_DETECT_ENTER_DIS)
                {
                    LpstrMapCoor->eObjFollowSts = OBJ_DET_UPDATE;
                }
                else
                {
                    LpstrMapCoor->eObjFollowSts = OBJ_OUT_DET_FOLLOW;
                }
            }
        }
    }
    else
    {
        LpstrMapCoor->eObjFollowSts = OBJ_SIDE_FOLLOW;
    }
}

/******************************************************************************
 * 函数名称: UpdateMapDetTimeAndJudgeDelete
 *
 * 功能描述: 更新Map的探测时间，并判断Map是否满足删除条件
 *
 * 输入参数:无
 *
 * 输出参数:无
 *
 * 返回值:无
 *
 * 其它说明:无
 *
 * 修改日期              版本号        修改人          修改内容
 * 2023-11-29 10:47   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static uint8 UpdateMapDetTimeAndJudgeDelete(ObjMapCoorType *LpstrMapCoor)
{
    uint8 Lu8MapNeedDeleteFlag = 0;
    uint16 Lu16MapNoUpdateNeedDeleTimeSet;
    float LfMapP1_X_ABS_Value;
    float LfMapP2_X_ABS_Value;
    float LfMapP1_Y_ABS_Value;
    float LfMapP2_Y_ABS_Value;
    uint8 Lu8SnsCh1, Lu8SnsCh2;
    uint16 Lu16ToBumperDisAndDE_Sub;

    float LfNearCarPointX_Coor;
    float LfNearCarPointY_Coor;
    float LfNearCarPointY_CoorAbs;

    LfMapP1_X_ABS_Value = ABS_VALUE(LpstrMapCoor->P1_CarCoor.fObjX);
    LfMapP1_Y_ABS_Value = ABS_VALUE(LpstrMapCoor->P1_CarCoor.fObjY);
    LfMapP2_X_ABS_Value = ABS_VALUE(LpstrMapCoor->P2_CarCoor.fObjX);
    LfMapP2_Y_ABS_Value = ABS_VALUE(LpstrMapCoor->P2_CarCoor.fObjY);

    LpstrMapCoor->u16NoUpdateHoldTime = GdSystemMsTimer - LpstrMapCoor->u32UpdateSysTime;
    //DEBUG_PRINT("timestap = %u,LpstrMapCoor->u16NoUpdateHoldTime = %u\n", GdSystemMsTimer, LpstrMapCoor->u16NoUpdateHoldTime);
    if ((LpstrMapCoor->fMapToBumper_Dis > SIDE_MAP_FAR_AWAY_DELETE_DIS))
    {
#if DEBUG_DELETE_MAP
        DEBUG_PRINT("timeStamp = %u,map_id = %d, Delete map D_001\n", GdSystemMsTimer, LpstrMapCoor->eMapObjId);
#endif
        Lu8MapNeedDeleteFlag = 1;
    }
    else
    {
        if ((LfMapP1_X_ABS_Value > MAP_TO_CAN_X_LIMIT_MAX) && (LfMapP2_X_ABS_Value > MAP_TO_CAN_X_LIMIT_MAX))
        {
#if DEBUG_DELETE_MAP
            DEBUG_PRINT("timeStamp = %u,map_id = %d, Delete map D_002\n", GdSystemMsTimer, LpstrMapCoor->eMapObjId);
#endif
            Lu8MapNeedDeleteFlag = 1;
        }
    }

    if (LpstrMapCoor->eObjFollowSts != OBJ_DET_UPDATE  && LpstrMapCoor->eObjFollowSts != OBJ_BLIND_FOLLOW)
    {
        /* 非更新区域内，需要实时更新其刷新时间，以保证再次进入到更新区时，不会立马被删除 */
        if (LpstrMapCoor->u8Map_SFR_AreaFlag == 2 )
        {
            if (LpstrMapCoor->u8MapGrenerateType == 1 && LpstrMapCoor->eObjExistProb <= OBJ_PROB_LV2)
            {
                //do nothing...
            }
            else
            {
                LpstrMapCoor->u32UpdateSysTime = GdSystemMsTimer;
            }
        }
    }

    if (GstrSnsCarMovSts[0][2].eCarDir == SNS_CAR_STOP)
    {
        if (LpstrMapCoor->fMapToBumper_Dis < 750)
        {
            Lu16MapNoUpdateNeedDeleTimeSet = MAP_NO_UPDATE_DELETE_NEAR_STOP_TIME;
        }
        else if (LpstrMapCoor->fMapToBumper_Dis < 2000)
        {
            Lu16MapNoUpdateNeedDeleTimeSet = MAP_NO_UPDATE_DELETE_MIDDLE_STOP_TIME;
        }
        else
        {
            Lu16MapNoUpdateNeedDeleTimeSet = MAP_NO_UPDATE_DELETE_FAR_STOP_TIME;
        }
    }
    else
    {
        if (LpstrMapCoor->fMapToBumper_Dis < 750)
        {
            Lu16MapNoUpdateNeedDeleTimeSet = MAP_NO_UPDATE_DELETE_NEAR_MOVE_TIME;
        }
        else if (LpstrMapCoor->fMapToBumper_Dis < 2000)
        {
            Lu16MapNoUpdateNeedDeleTimeSet = MAP_NO_UPDATE_DELETE_MIDDLE_MOVE_TIME;
        }
        else
        {
            Lu16MapNoUpdateNeedDeleTimeSet = MAP_NO_UPDATE_DELETE_FAR_MOVE_TIME*2;
        }
    }

    if (LpstrMapCoor->u8Map_SFR_AreaFlag == 2 && LpstrMapCoor->u8HeightLockFlag != HEIGHT_LOCK_LEVEL2)
    {
        if ((LpstrMapCoor->u16NoUpdateHoldTime > Lu16MapNoUpdateNeedDeleTimeSet) && LpstrMapCoor->eObjFollowSts != OBJ_BLIND_FOLLOW)
        {
#if DEBUG_DELETE_MAP
            DEBUG_PRINT("timeStamp = %u,map_id = %d, Delete map D_003\n", GdSystemMsTimer, LpstrMapCoor->eMapObjId);
#endif
            Lu8MapNeedDeleteFlag = 1;
        }
    }

    /* 针对前后保点状Map,在非更新区后，Y值偏离太大的删除策略 */
    if ((LpstrMapCoor->u8Map_SFR_AreaFlag == 2) && (LpstrMapCoor->eMapObjType == OBJ_TYPE_POINT))
    {
        if (LpstrMapCoor->eObjFollowSts != OBJ_DET_UPDATE)
        {
            if (LfMapP1_Y_ABS_Value > F_R_MAP_OUT_Y_DELE_DIS)
            {
#if DEBUG_DELETE_MAP
                DEBUG_PRINT("timeStamp = %u,map_id = %d, Delete map D_004\n", GdSystemMsTimer, LpstrMapCoor->eMapObjId);
#endif
                Lu8MapNeedDeleteFlag = 1;
            }
        }
    }

    return Lu8MapNeedDeleteFlag;
}

/******************************************************************************
 * 函数名称: UpdateMapInf
 *
 * 功能描述: 更新Map的细节信息
 *
 * 输入参数:无
 *
 * 输出参数:无
 *
 * 返回值:无
 *
 * 其它说明:无
 *
 * 修改日期              版本号        修改人          修改内容
 * 2023-11-22 19:15   V0.1      AntonyFang   初次发布
 ******************************************************************************/
static void UpdateMapInf(ObjMapCoorType *LpstrMapCoor)
{
    ObjMapInfoType *LstrObjMap;
    LstrObjMap = &GstrObjMap;

    float LstrP1_ToBumperDis = 65535;
    float LstrP2_ToBumperDis = 65535;
    ObjMAPAreaType LenuMapP1_DetailArea = MAP_AREA_NONE;
    ObjMAPAreaType LenuMapP2_DetailArea = MAP_AREA_NONE;
    float LfMapP1_X_ABS_Value, LfMapP1_Y_ABS_Value;
    float LfMapP2_X_ABS_Value, LfMapP2_Y_ABS_Value;

    LfMapP1_X_ABS_Value = ABS_VALUE(LpstrMapCoor->P1_CarCoor.fObjX);
    LfMapP1_Y_ABS_Value = ABS_VALUE(LpstrMapCoor->P1_CarCoor.fObjY);
    LfMapP2_X_ABS_Value = ABS_VALUE(LpstrMapCoor->P2_CarCoor.fObjX);
    LfMapP2_Y_ABS_Value = ABS_VALUE(LpstrMapCoor->P2_CarCoor.fObjY);

    if (LpstrMapCoor->eMapObjType == OBJ_TYPE_POINT)
    {
        LpstrMapCoor->fP1_P2_Dis = 0;
        LpstrMapCoor->fP1_P2ToCarX_Angle = 0;
        LenuMapP1_DetailArea = CalPointToBumperRelationAndDis(&LpstrMapCoor->P1_CarCoor, &LstrP1_ToBumperDis);
        LpstrMapCoor->fP1_ToBumper_Dis = LstrP1_ToBumperDis;
        LpstrMapCoor->eP1_DetailArea = LenuMapP1_DetailArea;
        LpstrMapCoor->fP2_ToBumper_Dis = LstrP1_ToBumperDis;
        LpstrMapCoor->eP2_DetailArea = LenuMapP1_DetailArea;
    }
    else
    {
        LpstrMapCoor->fP1_P2_Dis = PubAI_CalTwoPointDis(LpstrMapCoor->P1_CarCoor.fObjX, LpstrMapCoor->P1_CarCoor.fObjY,
                                                        LpstrMapCoor->P2_CarCoor.fObjX, LpstrMapCoor->P2_CarCoor.fObjY);

        LpstrMapCoor->fP1_P2ToCarX_Angle = PubAI_CalP1_P2_LineToX_AxleAngle(LpstrMapCoor->P1_CarCoor.fObjX, LpstrMapCoor->P1_CarCoor.fObjY,
                                                                            LpstrMapCoor->P2_CarCoor.fObjX, LpstrMapCoor->P2_CarCoor.fObjY);

        LenuMapP1_DetailArea = CalPointToBumperRelationAndDis(&LpstrMapCoor->P1_CarCoor, &LstrP1_ToBumperDis);
        LenuMapP2_DetailArea = CalPointToBumperRelationAndDis(&LpstrMapCoor->P2_CarCoor, &LstrP2_ToBumperDis);
        LpstrMapCoor->fP1_ToBumper_Dis = LstrP1_ToBumperDis;
        LpstrMapCoor->eP1_DetailArea = LenuMapP1_DetailArea;
        LpstrMapCoor->fP2_ToBumper_Dis = LstrP2_ToBumperDis;
        LpstrMapCoor->eP2_DetailArea = LenuMapP2_DetailArea;

        if ((LpstrMapCoor->enuSideMapUpdateSts == SIDE_MAP_MEMORY_P1_UPDATE_P2) && (LpstrMapCoor->enuSideMapSns != SIDE_SNS_NONE))
        {
            if (LpstrMapCoor->fP1_P2_Dis > 10000)
            {
                LpstrMapCoor->enuSideMapUpdateSts = SIDE_MAP_MEMORY_P1_MEMORY_P2;
                LpstrMapCoor->eMapObjType = OBJ_TYPE_STRAIGHT2_CORNER;
            }
        }
    }

    if (LstrP1_ToBumperDis < LstrP2_ToBumperDis)
    {
        LpstrMapCoor->fMapToBumper_Dis = LstrP1_ToBumperDis;
    }
    else
    {
        LpstrMapCoor->fMapToBumper_Dis = LstrP2_ToBumperDis;
    }

    /* 判断Map在保杠中的大致分区 */
    JudgeMapBumperArea(LpstrMapCoor);
    if (LpstrMapCoor->eObjBumperArea == -1 && LpstrMapCoor->u8HeightLockFlag != HEIGHT_LOCK_LEVEL2)
    {
#if DEBUG_DELETE_MAP
        DEBUG_PRINT("timeStamp = %u,map_id = %d, Delete map D_0013\n", GdSystemMsTimer, LpstrMapCoor->eMapObjId);
#endif
        *LpstrMapCoor = GstrObjMapCoorInit;
        LstrObjMap->u8MapNum--;
        return;
    }

    /*  判断Map和车辆的运动关系 */
    JudgeMapMoveSts(LpstrMapCoor);

    /* 判断Map的更新状态 */
    JudgeMapUpdateSts(LpstrMapCoor);

    uint8 result = UpdateMapDetTimeAndJudgeDelete(LpstrMapCoor);
    if (result == 1 && LpstrMapCoor->u8HeightLockFlag != HEIGHT_LOCK_LEVEL2)
    {
#if DEBUG_DELETE_MAP
        DEBUG_PRINT("timeStamp = %u,map_id = %d, Delete map D_005\n", GdSystemMsTimer, LpstrMapCoor->eMapObjId);
#endif
        LstrObjMap->u8MapNum--;
        *LpstrMapCoor = GstrObjMapCoorInit;
        return;
    }

    /* 对于兜底置低的map如果距离车辆位置超过4.5m的时候，将其删除 */
    if (result == 1 && LpstrMapCoor->u8HeightLockFlag == HEIGHT_LOCK_LEVEL2 && LpstrMapCoor->fMapToBumper_Dis > 4500)
    {
#if DEBUG_DELETE_MAP
        DEBUG_PRINT("timeStamp = %u,map_id = %d, Delete map D_005\n", GdSystemMsTimer, LpstrMapCoor->eMapObjId);
#endif
        LstrObjMap->u8MapNum--;
        *LpstrMapCoor = GstrObjMapCoorInit;
        return;
    }

    float matched_real_de;
    int matched_channel;
    int is_matched_de = JudgeMapMatchedSnsDe(LpstrMapCoor, &matched_real_de, &matched_channel);

    //增加的DE 和map的匹配 ： 2024-04-30
    {
        if (is_matched_de == 1)
        {
            if (LpstrMapCoor->eObjFollowSts == OBJ_BLIND_FOLLOW)
            {
                LpstrMapCoor->u32UpdateSysTime = GdSystemMsTimer - 50;
            }

            /* 根据前后保杠不同分区，设定不同的阈值信息 */
            float threshold = 0;
            if (LpstrMapCoor->eObjBumperArea == OBJ_FRONT_BUMP)
            {
                if (matched_channel == PDC_SNS_CH_RL || matched_channel == PDC_SNS_CH_FR)
                {
                    threshold = FRONT_RL_HEIGHT_DIS;
                }

                if (matched_channel == PDC_SNS_CH_FML || matched_channel == PDC_SNS_CH_FMR)
                {
                    threshold = FRONT_MRL_HEIGHT_DIS;
                }
            }

            if (LpstrMapCoor->eObjBumperArea == OBJ_REAR_BUMP)
            {
                if (matched_channel == PDC_SNS_CH_RL || matched_channel == PDC_SNS_CH_FR)
                {
                    threshold = REAR_RL_HEIGHT_DIS;
                }

                if (matched_channel == PDC_SNS_CH_FML || matched_channel == PDC_SNS_CH_FMR)
                {
                    threshold = REAR_MRL_HEIGHT_DIS;
                }
            }

            if (LpstrMapCoor->u32FirstNearDeMatchedTime != 0 && GdSystemMsTimer - LpstrMapCoor->u32FirstNearDeMatchedTime > 1000)
            {
                LpstrMapCoor->u32FirstNearDeMatchedTime = 0;
            }

            /* 兜底置高策略： 550mm内，能匹配到DE值，将map快速置高 */
            if (matched_real_de < threshold)
            {
                if (LpstrMapCoor->u32FirstNearDeMatchedTime == 0)
                {
                    LpstrMapCoor->u32FirstNearDeMatchedTime = GdSystemMsTimer;
                }
                else
                {
                    if (LpstrMapCoor->u8HeightLockFlag < HEIGHT_LOCK_LEVEL3)
                    {
                        if (GstrParkingGuidenceData.u8SameFreqNoiseFlag[0] == 0 && GstrParkingGuidenceData.u8SameFreqNoiseFlag[1] == 0)
                        {
#if DEBUG_HIGH_LOW
                            DEBUG_PRINT("timestamp = %u, map_id = %d, point map set to be High H_L_000\n", GdSystemMsTimer, LpstrMapCoor->eMapObjId);
#endif
                            LpstrMapCoor->u8HeightLockFlag = HEIGHT_LOCK_LEVEL3;
                            LpstrMapCoor->eMapObjHeight = OBJ_HIGH;
                            if (LpstrMapCoor->eObjExistProb < OBJ_PROB_LV6 && LpstrMapCoor->eObjExistProb > OBJ_PROB_LV1)
                            {
                                LpstrMapCoor->eObjExistProb = OBJ_PROB_LV6;
                            }
                        }
                        else
                        {
                            if (LpstrMapCoor->eObjExistProb > OBJ_PROB_LV3)
                            {
                                LpstrMapCoor->u8HeightLockFlag = HEIGHT_LOCK_LEVEL3;
                                LpstrMapCoor->eMapObjHeight = OBJ_HIGH;
                            }
                        }

                        /* 显示角雷达 DE_Map */
                        if (LpstrMapCoor->u8MapGrenerateType == 2 && LpstrMapCoor->u8MapDisplayFlag == 0 && LpstrMapCoor->eObjExistProb > OBJ_PROB_LV1)
                        {
                            LpstrMapCoor->u8MapDisplayFlag = 1;
                        }
                    }
                }
            }
        }

        /* 兜底置低策略 */
        //if (LpstrMapCoor->u8MapGrenerateType == 3)
        {
            float low_map_dist_thr = 0;
            if (LpstrMapCoor->eObjBumperArea == OBJ_FRONT_BUMP)
            {
                low_map_dist_thr = FRONT_BUMP_LOW_DIS;
            }

            if (LpstrMapCoor->eObjBumperArea == OBJ_REAR_BUMP)
            {
                low_map_dist_thr = REAR_BUMP_LOW_DIS;
            }

            if (LpstrMapCoor->fNearestDE > low_map_dist_thr && LpstrMapCoor->fNearestDE < 1100
                && LpstrMapCoor->eObjMoveSts == OBJ_TO_CAR_MOVE_CLOSE && LpstrMapCoor->fMapToBumper_Dis < 1000)
            {
                if (LpstrMapCoor->u8HeightLockFlag < HEIGHT_LOCK_LEVEL2)
                {
                    if (LpstrMapCoor->u8Map_DE_NoMatchCnt >= 3)
                    {
#if DEBUG_HIGH_LOW
                        DEBUG_PRINT("timestamp = %u, map_id = %d, point map set to be Low H_L_009\n", GdSystemMsTimer, LpstrMapCoor->eMapObjId);
#endif
                        LpstrMapCoor->u8HeightLockFlag = HEIGHT_LOCK_LEVEL2;
                        LpstrMapCoor->eMapObjHeight = OBJ_LOW;
                    }
                }
            }
        }
    }
}

static void SetGlobalMapCoor(int matched_id, SnsAreaMapType *new_map)
{
    ObjMapInfoType *LstrObjMap;
    LstrObjMap = &GstrObjMap;

    LstrObjMap->ObjMapCoor[matched_id].P1_OdoCoor.fObjX = new_map->P1_OdoCoor.fObjX;
    LstrObjMap->ObjMapCoor[matched_id].P1_OdoCoor.fObjY = new_map->P1_OdoCoor.fObjY;
    LstrObjMap->ObjMapCoor[matched_id].P2_OdoCoor.fObjX = new_map->P2_OdoCoor.fObjX;
    LstrObjMap->ObjMapCoor[matched_id].P2_OdoCoor.fObjY = new_map->P2_OdoCoor.fObjY;
    LstrObjMap->ObjMapCoor[matched_id].P1_CarCoor.fObjX = new_map->P1_CarCoor.fObjX;
    LstrObjMap->ObjMapCoor[matched_id].P1_CarCoor.fObjY = new_map->P1_CarCoor.fObjY;
    LstrObjMap->ObjMapCoor[matched_id].P2_CarCoor.fObjX = new_map->P2_CarCoor.fObjX;
    LstrObjMap->ObjMapCoor[matched_id].P2_CarCoor.fObjY = new_map->P2_CarCoor.fObjY;
}

static float GetMapBlindDist(ObjMapCoorType *LpstrMapCoor)
{
    float Lu16BlindEnterDis, Lu16BlindExitDis;
    ObjMAPAreaType LenuLineNearPointArea;

    if (LpstrMapCoor->eMapObjType == OBJ_TYPE_POINT)
    {
        LenuLineNearPointArea = LpstrMapCoor->eP1_DetailArea;
    }
    else
    {
        if (LpstrMapCoor->fP1_ToBumper_Dis < LpstrMapCoor->fP2_ToBumper_Dis)
        {
            LenuLineNearPointArea = LpstrMapCoor->eP1_DetailArea;
        }
        else
        {
            LenuLineNearPointArea = LpstrMapCoor->eP2_DetailArea;
        }
    }

    /* 区分召唤模式和非召唤模式 */
    if (GstrParkingGuidenceData.eParking_Sts == PARKING_SUMMON)
    {
        if ((LenuLineNearPointArea > MAP_AREA2) && (LenuLineNearPointArea < MAP_AREA6))
        {
            if (LenuLineNearPointArea == MAP_AREA4)
            {
                Lu16BlindEnterDis = MAP_BLIND_SUMMON_AREA4_ENTER_DIS;
                Lu16BlindExitDis = MAP_BLIND_SUMMON_AREA4_EXIT_DIS;
            }
            else
            {
                Lu16BlindEnterDis = MAP_BLIND_SUMMON_MID_ENTER_DIS;
                Lu16BlindExitDis = MAP_BLIND_SUMMON_MID_EXIT_DIS;
            }
        }
        else
        {
            Lu16BlindEnterDis = MAP_BLIND_SUMMON_CORNER_ENTER_DIS;
            Lu16BlindExitDis = MAP_BLIND_SUMMON_CORNER_EXIT_DIS;
        }
    }
    else
    {
        if ((LenuLineNearPointArea > MAP_AREA2) && (LenuLineNearPointArea < MAP_AREA6))
        {
            if (LenuLineNearPointArea == MAP_AREA4)
            {
                Lu16BlindEnterDis = MAP_BLIND_AREA4_ENTER_DIS;
                Lu16BlindExitDis = MAP_BLIND_AREA4_EXIT_DIS;
            }
            else
            {
                Lu16BlindEnterDis = MAP_BLIND_MIDDLE_ENTER_DIS;
                Lu16BlindExitDis = MAP_BLIND_MIDDLE_EXIT_DIS;
            }
        }
        else
        {
            Lu16BlindEnterDis = MAP_BLIND_CORNER_ENTER_DIS;
            Lu16BlindExitDis = MAP_BLIND_CORNER_EXIT_DIS;
        }
    }

    /* 没进入盲区时的盲区设定阈值 */
    if (LpstrMapCoor->u8CarCloseToMapBlindFlag == 0)
    {
        if (LpstrMapCoor->fMapToBumper_Dis < Lu16BlindEnterDis)
        {
            LpstrMapCoor->u8CarCloseToMapBlindFlag = 1;
        }
        return Lu16BlindEnterDis;
    }

    /* 进入盲区后，盲区回滞阈值 */
    if (LpstrMapCoor->u8CarCloseToMapBlindFlag == 1)
    {
        if (LpstrMapCoor->fMapToBumper_Dis > Lu16BlindExitDis)
        {
            LpstrMapCoor->u8CarCloseToMapBlindFlag = 0;
        }
        return Lu16BlindEnterDis;
    }
}

void Delete_DE_Map(int matched_id)
{
    ObjMapInfoType *LstrObjMap;
    LstrObjMap = &GstrObjMap;

    for (int i = MAP_OBJ_0; i < MAP_OBJ_NUM; i++)
    {
        if (LstrObjMap->ObjMapCoor[i].u8MapExistFlag)
        {
            if (LstrObjMap->ObjMapCoor[i].u8Map_SFR_AreaFlag != 2 ||
                LstrObjMap->ObjMapCoor[i].eMapObjType != OBJ_TYPE_POINT ||
                LstrObjMap->ObjMapCoor[i].u8MapGrenerateType != 1)
                continue;

            float map_dist = CalTwoMapDist(LstrObjMap->ObjMapCoor[matched_id].P1_OdoCoor, LstrObjMap->ObjMapCoor[matched_id].P2_OdoCoor,
                                           LstrObjMap->ObjMapCoor[i].P1_OdoCoor, LstrObjMap->ObjMapCoor[i].P2_OdoCoor);
            
            if (LstrObjMap->ObjMapCoor[i].fMapToBumper_Dis > LstrObjMap->ObjMapCoor[matched_id].fMapToBumper_Dis &&
                 map_dist < 1000 && LstrObjMap->ObjMapCoor[matched_id].eP1_DetailArea == MAP_AREA4)
            {
#if DEBUG_DELETE_MAP
                DEBUG_PRINT("timeStamp = %u,map_id = %d, Delete map D_013\n", GdSystemMsTimer, LstrObjMap->ObjMapCoor[i].eMapObjId);
#endif
                LstrObjMap->ObjMapCoor[i] = GstrObjMapCoorInit;
                LstrObjMap->u8MapNum--;
                if (LstrObjMap->ObjMapCoor[matched_id].u8HeightLockFlag < HEIGHT_LOCK_LEVEL1)
                {
                    LstrObjMap->ObjMapCoor[matched_id].eMapObjHeight = OBJ_HIGH;
                    LstrObjMap->ObjMapCoor[matched_id].u8HeightLockFlag = HEIGHT_LOCK_LEVEL1;
                }
            }
        }
    }
}

int Judge_Point_Map_Delete(int matched_id)
{
    int result = 0;
    ObjMapInfoType *LstrObjMap;
    LstrObjMap = &GstrObjMap;

    for (int i = MAP_OBJ_0; i < MAP_OBJ_NUM; i++)
    {
        if (LstrObjMap->ObjMapCoor[i].u8MapExistFlag)
        {
            if (LstrObjMap->ObjMapCoor[i].u8Map_SFR_AreaFlag != 2 ||
                LstrObjMap->ObjMapCoor[i].eMapObjType != OBJ_TYPE_STRAIGHT2_CORNER )
                continue;

            float diff_car_x = ABS_VALUE(LstrObjMap->ObjMapCoor[i].P1_CarCoor.fObjX - LstrObjMap->ObjMapCoor[i].P2_CarCoor.fObjX);
            if (diff_car_x > 10)
            {
                continue;
            }

            float center_map_car_x = (LstrObjMap->ObjMapCoor[i].P1_CarCoor.fObjX + LstrObjMap->ObjMapCoor[i].P2_CarCoor.fObjX) / 2;
            
            if (ABS_VALUE(center_map_car_x - LstrObjMap->ObjMapCoor[matched_id].P1_CarCoor.fObjX) < 50)
            {
                if (ABS_VALUE(LstrObjMap->ObjMapCoor[matched_id].P1_CarCoor.fObjY) < GstrRamSnsCoor[0][0].cRadarY)
                {
                    result = 1;
                }
            }
        }
    }

    return result;
}

/* 通过历史回波高度变化趋势，来判定map的高低属性 */
void JudgeMapHeightByEchoHeight(int matched_id)
{
    ObjMapInfoType *LstrObjMap;
    LstrObjMap = &GstrObjMap;

    //DEBUG_PRINT("timestamp = %u, matched_id = %d, u8HeightLockFlag = %d\n", GdSystemMsTimer, matched_id, LstrObjMap->ObjMapCoor[matched_id].u8HeightLockFlag);

    /* case1: 10cm路沿在700cm开始，检测不到回波，故置低 */
    if (LstrObjMap->ObjMapCoor[matched_id].fMapToBumper_Dis < 500 && LstrObjMap->ObjMapCoor[matched_id].fSideMapToSelfSnsDis > 1500)
    {
        if (LstrObjMap->ObjMapCoor[matched_id].u8HeightLockFlag == HEIGHT_LOCK_LEVEL0)
        {
            if (LstrObjMap->ObjMapCoor[matched_id].eMapObjType == OBJ_TYPE_STRAIGHT2_CORNER &&
                LstrObjMap->ObjMapCoor[matched_id].MapHeightBuf[8].u16MasterHeight == 0 &&
                LstrObjMap->ObjMapCoor[matched_id].MapHeightBuf[9].u16MasterHeight == 0)
            {
#if DEBUG_HIGH_LOW
                DEBUG_PRINT("timestamp = %u, map_id = %d, point map set to be low H_L_002\n", GdSystemMsTimer, LstrObjMap->ObjMapCoor[matched_id].eMapObjId);
#endif
                LstrObjMap->ObjMapCoor[matched_id].u8HeightLockFlag = HEIGHT_LOCK_LEVEL1;
                LstrObjMap->ObjMapCoor[matched_id].eMapObjHeight = OBJ_LOW;
            }
            else
            {
#if DEBUG_HIGH_LOW
                DEBUG_PRINT("timestamp = %u, map_id = %d, point map set to be High H_L_003\n", GdSystemMsTimer, LstrObjMap->ObjMapCoor[matched_id].eMapObjId);
#endif
                LstrObjMap->ObjMapCoor[matched_id].u8HeightLockFlag = HEIGHT_LOCK_LEVEL1;
                LstrObjMap->ObjMapCoor[matched_id].eMapObjHeight = OBJ_HIGH;
            }
        }
        
    }

    /* case2: 如果全程单调递增，则置高 */
    if (LstrObjMap->ObjMapCoor[matched_id].fMapToBumper_Dis < 500 && LstrObjMap->ObjMapCoor[matched_id].fSideMapToSelfSnsDis > 1500)
    {
        int is_increase = 1;
        for (int index = 0; index < 9; index++)
        {
            if (LstrObjMap->ObjMapCoor[matched_id].MapHeightBuf[index + 1].u16MasterHeight < LstrObjMap->ObjMapCoor[matched_id].MapHeightBuf[index].u16MasterHeight)
            {
                is_increase = 0;
            }
        }

        if (is_increase == 1)
        {
            if (LstrObjMap->ObjMapCoor[matched_id].u8HeightLockFlag == HEIGHT_LOCK_LEVEL0)
            {
#if DEBUG_HIGH_LOW
                DEBUG_PRINT("timestamp = %u, map_id = %d, point map set to be High H_L_004\n", GdSystemMsTimer, LstrObjMap->ObjMapCoor[matched_id].eMapObjId);
#endif
                LstrObjMap->ObjMapCoor[matched_id].u8HeightLockFlag = HEIGHT_LOCK_LEVEL1;
                LstrObjMap->ObjMapCoor[matched_id].eMapObjHeight = OBJ_HIGH;
            }
        }
    }

//    /* case3: 限位块1100的回波高度比1200要低 */
//    if (LstrObjMap->ObjMapCoor[matched_id].fMapToBumper_Dis < 850 && LstrObjMap->ObjMapCoor[matched_id].fSideMapToSelfSnsDis > 1500)
//    {
//        if (LstrObjMap->ObjMapCoor[matched_id].eMapObjType == OBJ_TYPE_POINT)
//        {
//            if (LstrObjMap->ObjMapCoor[matched_id].MapHeightBuf[4].u16MasterHeight > 0 && 
//                LstrObjMap->ObjMapCoor[matched_id].MapHeightBuf[5].u16MasterHeight > 0)
//            {
//                if (LstrObjMap->ObjMapCoor[matched_id].MapHeightBuf[4].u16MasterHeight < LstrObjMap->ObjMapCoor[matched_id].MapHeightBuf[3].u16MasterHeight ||
//                    LstrObjMap->ObjMapCoor[matched_id].MapHeightBuf[5].u16MasterHeight < LstrObjMap->ObjMapCoor[matched_id].MapHeightBuf[3].u16MasterHeight)
//                {
//                    if (LstrObjMap->ObjMapCoor[matched_id].u8HeightLockFlag == HEIGHT_LOCK_LEVEL0)
//                    {
//#if DEBUG_HIGH_LOW
//                        DEBUG_PRINT("timestamp = %u, map_id = %d, point map set to be Low H_L_005\n", GdSystemMsTimer, LstrObjMap->ObjMapCoor[matched_id].eMapObjId);
//#endif
//                        LstrObjMap->ObjMapCoor[matched_id].u8HeightLockFlag = HEIGHT_LOCK_LEVEL1;
//                        LstrObjMap->ObjMapCoor[matched_id].eMapObjHeight = OBJ_LOW;
//                    }
//                }
//            }
//        }
//    }
}

/* 更新全局 global_map_buffer 的信息，map 的更新策略（是否删除及保留）*/
void UpdateMapBufferInfo(void )
{
    ObjMapInfoType *LstrObjMap;
    LstrObjMap = &GstrObjMap;

    UpdateMapCarCoor();

    for (int map_index = MAP_OBJ_0; map_index < MAP_OBJ_NUM; map_index++)
    {
        if (LstrObjMap->ObjMapCoor[map_index].u8MapExistFlag)
        {
            if (LstrObjMap->ObjMapCoor[map_index].u8Map_SFR_AreaFlag != 2)
                continue;

            UpdateMapInf(&LstrObjMap->ObjMapCoor[map_index]);

            if (!LstrObjMap->ObjMapCoor[map_index].u8MapExistFlag)
                continue;

            /* 单主发和同频干扰的时候，降低 map 的生成速度 */
            if ((LstrObjMap->ObjMapCoor[map_index].eObjBumperArea == OBJ_FRONT_BUMP && GstrParkingGuidenceData.u8SameFreqNoiseFlag[0] == 1) ||
                (LstrObjMap->ObjMapCoor[map_index].eObjBumperArea == OBJ_REAR_BUMP && GstrParkingGuidenceData.u8SameFreqNoiseFlag[1] == 1))
            {
                if (LstrObjMap->ObjMapCoor[map_index].eObjExistProb < OBJ_PROB_LV2 &&
                    (LstrObjMap->ObjMapCoor[map_index].u8MapGrenerateType == 1 || LstrObjMap->ObjMapCoor[map_index].u8MapGrenerateType == 2))
                {
                    if (GdSystemMsTimer - LstrObjMap->ObjMapCoor[map_index].u32UpdateSysTime > 400)
                    {
#if DEBUG_DELETE_MAP
                        DEBUG_PRINT("timeStamp = %u,map_id = %d, Delete map D_000\n", GdSystemMsTimer, LstrObjMap->ObjMapCoor[map_index].eMapObjId);
#endif
                        LstrObjMap->ObjMapCoor[map_index] = GstrObjMapCoorInit;
                        LstrObjMap->u8MapNum--;
                        continue;
                    }
                }
                else
                {
                    if (LstrObjMap->ObjMapCoor[map_index].u8MapDisplayFlag == 0)
                    {
                        if (LstrObjMap->ObjMapCoor[map_index].u8MapGrenerateType == 3)
                        {
                            LstrObjMap->ObjMapCoor[map_index].u8MapDisplayFlag = 1;
                        }

                        if (LstrObjMap->ObjMapCoor[map_index].u8MapGrenerateType == 1 || LstrObjMap->ObjMapCoor[map_index].u8MapGrenerateType == 2)
                        {

                            if (LstrObjMap->ObjMapCoor[map_index].eObjExistProb > OBJ_PROB_LV1 && LstrObjMap->ObjMapCoor[map_index].u8DEMapChripCnt > 1)
                            {
                                LstrObjMap->ObjMapCoor[map_index].u8MapDisplayFlag = 1;
                            }
                        }
                    }
                }
            }
            else
            {
                if (LstrObjMap->ObjMapCoor[map_index].eObjExistProb < OBJ_PROB_LV2 &&
                    (LstrObjMap->ObjMapCoor[map_index].u8MapGrenerateType == 1 || LstrObjMap->ObjMapCoor[map_index].u8MapGrenerateType == 2))
                {
                    if (GdSystemMsTimer - LstrObjMap->ObjMapCoor[map_index].u32UpdateSysTime > 400)
                    {
#if DEBUG_DELETE_MAP
                        DEBUG_PRINT("timeStamp = %u,map_id = %d, Delete map D_000\n", GdSystemMsTimer, LstrObjMap->ObjMapCoor[map_index].eMapObjId);
#endif
                        LstrObjMap->ObjMapCoor[map_index] = GstrObjMapCoorInit;
                        LstrObjMap->u8MapNum--;
                        continue;
                    }
                }
                else
                {
                    if (LstrObjMap->ObjMapCoor[map_index].u8MapDisplayFlag == 0)
                    {
                        if (LstrObjMap->ObjMapCoor[map_index].u8MapGrenerateType == 3)
                        {
                            LstrObjMap->ObjMapCoor[map_index].u8MapDisplayFlag = 1;
                        }

                        if (LstrObjMap->ObjMapCoor[map_index].u8MapGrenerateType == 1 || LstrObjMap->ObjMapCoor[map_index].u8MapGrenerateType == 2)
                        {

                            if (LstrObjMap->ObjMapCoor[map_index].eObjExistProb > OBJ_PROB_LV1)
                            {
                                LstrObjMap->ObjMapCoor[map_index].u8MapDisplayFlag = 1;
                            }
                        }
                    }
                }
            }

            JudgeMapHeightByEchoHeight(map_index);

            if (LstrObjMap->ObjMapCoor[map_index].eObjBumperArea == OBJ_FRONT_BUMP || LstrObjMap->ObjMapCoor[map_index].eObjBumperArea == OBJ_REAR_BUMP)
            {
                /* 当有点云生成map的时候，删除对应的近距离DE_map */
                if (LstrObjMap->ObjMapCoor[map_index].eMapObjType == OBJ_TYPE_POINT && LstrObjMap->ObjMapCoor[map_index].u8MapGrenerateType == 3)
                {
                    Delete_DE_Map(map_index);

                    int result = Judge_Point_Map_Delete(map_index);
                    if (result == 1 && GstrParkingGuidenceData.eCurrentAdasSts == APA_SEARCH)
                    {
#if DEBUG_DELETE_MAP
                        DEBUG_PRINT("timeStamp = %u,map_id = %d, Delete map D_006\n", GdSystemMsTimer, LstrObjMap->ObjMapCoor[map_index].eMapObjId);
#endif
                        LstrObjMap->ObjMapCoor[map_index] = GstrObjMapCoorInit;
                        LstrObjMap->u8MapNum--;
                        continue;
                    }
                }

                /* 侧面的点map在运动过程中，跟踪到侧面才删除 */
                float car_y_limit = ABS_VALUE(GstrRamSnsCoor[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FLS].cRadarY);
                if (LstrObjMap->ObjMapCoor[map_index].eMapObjType == OBJ_TYPE_POINT &&
                    LstrObjMap->ObjMapCoor[map_index].fSideMapToSelfSnsDis > 1800 &&
                    (ABS_VALUE(LstrObjMap->ObjMapCoor[map_index].P1_CarCoor.fObjY) > car_y_limit + 100 && ABS_VALUE(LstrObjMap->ObjMapCoor[map_index].P1_CarCoor.fObjY) < car_y_limit + 350) &&
                    LstrObjMap->ObjMapCoor[map_index].u8PointMatchPointCnt > 10 )
                {
                    continue;
                }
                
                /* 根据map所在保险杠不同区域，设定不同的盲区阈值 */
                float blindDist = GetMapBlindDist(&LstrObjMap->ObjMapCoor[map_index]);

                /* 车辆往前走的过程中，后保的 map 不删除 */
                if (GstrSnsCarMovSts[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FMR].eCarDir == SNS_CAR_FORWARD &&
                    LstrObjMap->ObjMapCoor[map_index].eObjBumperArea == OBJ_REAR_BUMP)
                {
                    continue;
                }

                /* 车辆往后走的过程中，前保的 map 不删除 */
                if (GstrSnsCarMovSts[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FMR].eCarDir == SNS_CAR_BACKWARD &&
                    LstrObjMap->ObjMapCoor[map_index].eObjBumperArea == OBJ_FRONT_BUMP)
                {
                    continue;
                }
                
                /* 盲区内 (0mm - 500mm) map 维持 30s ，超过 30s 后没有更新，则删除 */
                if (LstrObjMap->ObjMapCoor[map_index].fMapToBumper_Dis < blindDist && GdSystemMsTimer - LstrObjMap->ObjMapCoor[map_index].u32UpdateSysTime > 30000)
                {
#if DEBUG_DELETE_MAP
                    DEBUG_PRINT("timeStamp = %u,map_id = %d, Delete map D_016\n", GdSystemMsTimer, LstrObjMap->ObjMapCoor[map_index].eMapObjId);
#endif
                    LstrObjMap->ObjMapCoor[map_index] = GstrObjMapCoorInit;
                    LstrObjMap->u8MapNum--;
                    continue;
                }

                /* 盲区内，行人绕行（动态障碍物）的跟踪时间，降级为3000ms */
                if (LstrObjMap->ObjMapCoor[map_index].u8MapCreateCarSts == 1 &&
                    GstrSnsCarMovSts[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FMR].eCarDir == SNS_CAR_STOP &&
                    GdSystemMsTimer - LstrObjMap->ObjMapCoor[map_index].u32UpdateSysTime > 3000)
                {
#if DEBUG_DELETE_MAP
                    DEBUG_PRINT("timeStamp = %u,map_id = %d, Delete map D_015\n", GdSystemMsTimer, LstrObjMap->ObjMapCoor[map_index].eMapObjId);
#endif
                    LstrObjMap->ObjMapCoor[map_index] = GstrObjMapCoorInit;
                    LstrObjMap->u8MapNum--;
                    continue;
                }

                /* 更新区内（700mm - 1800mm），500ms没有更新，且匹配次数小于20次的map，则删除map信息 */
                if ((LstrObjMap->ObjMapCoor[map_index].fMapToBumper_Dis > 700 && LstrObjMap->ObjMapCoor[map_index].fMapToBumper_Dis < 1800) &&
                    (LstrObjMap->ObjMapCoor[map_index].u8PointMatchPointCnt < 15 && LstrObjMap->ObjMapCoor[map_index].u8LineMatchLineCnt < 15) &&
                    GdSystemMsTimer - LstrObjMap->ObjMapCoor[map_index].u32UpdateSysTime > 500 && LstrObjMap->ObjMapCoor[map_index].u8HeightLockFlag != HEIGHT_LOCK_LEVEL2)
                {
#if DEBUG_DELETE_MAP
                    DEBUG_PRINT("timeStamp = %u,map_id = %d, Delete map D_007\n", GdSystemMsTimer, LstrObjMap->ObjMapCoor[map_index].eMapObjId);
#endif
                    LstrObjMap->ObjMapCoor[map_index] = GstrObjMapCoorInit;
                    LstrObjMap->u8MapNum--;
                    continue;
                }

                /* 更新区内（700mm - 1800mm），1000ms没有更新，且匹配次数大于20次的map，则删除map信息 */
                if ((LstrObjMap->ObjMapCoor[map_index].fMapToBumper_Dis > 700 && LstrObjMap->ObjMapCoor[map_index].fMapToBumper_Dis < 1800) &&
                    (LstrObjMap->ObjMapCoor[map_index].u8PointMatchPointCnt > 15 || LstrObjMap->ObjMapCoor[map_index].u8LineMatchLineCnt > 15) &&
                    GdSystemMsTimer - LstrObjMap->ObjMapCoor[map_index].u32UpdateSysTime > 1000 && LstrObjMap->ObjMapCoor[map_index].u8HeightLockFlag != HEIGHT_LOCK_LEVEL2)
                {
#if DEBUG_DELETE_MAP
                    DEBUG_PRINT("timeStamp = %u,map_id = %d, Delete map D_008\n", GdSystemMsTimer, LstrObjMap->ObjMapCoor[map_index].eMapObjId);
#endif
                    LstrObjMap->ObjMapCoor[map_index] = GstrObjMapCoorInit;
                    LstrObjMap->u8MapNum--;
                    continue;
                }

                /* 远距离更新区内（1800mm - 3000mm），少了一帧定频数据，2000ms没有更新，且匹配次数大于20次的map，则删除map信息 */ 
                if ((LstrObjMap->ObjMapCoor[map_index].fMapToBumper_Dis > 1800 && LstrObjMap->ObjMapCoor[map_index].fMapToBumper_Dis < 3000) &&
                    (LstrObjMap->ObjMapCoor[map_index].u8PointMatchPointCnt > 20 || LstrObjMap->ObjMapCoor[map_index].u8LineMatchLineCnt > 20) &&
                    GdSystemMsTimer - LstrObjMap->ObjMapCoor[map_index].u32UpdateSysTime > 2000 && LstrObjMap->ObjMapCoor[map_index].u8HeightLockFlag != HEIGHT_LOCK_LEVEL2)
                {
#if DEBUG_DELETE_MAP
                    DEBUG_PRINT("timeStamp = %u,map_id = %d, Delete map D_009\n", GdSystemMsTimer, LstrObjMap->ObjMapCoor[map_index].eMapObjId);
#endif
                    LstrObjMap->ObjMapCoor[map_index] = GstrObjMapCoorInit;
                    LstrObjMap->u8MapNum--;
                    continue;
                }

                /* 非盲区内，如果持续 5s 都没有更新障碍物的最新系统时间，则认为障碍物已被人为移除 */
                if (LstrObjMap->ObjMapCoor[map_index].fMapToBumper_Dis > 500 &&
                   (LstrObjMap->ObjMapCoor[map_index].u8LineMatchLineCnt < 50 &&LstrObjMap->ObjMapCoor[map_index].u8PointMatchPointCnt < 50) &&
                    GdSystemMsTimer - LstrObjMap->ObjMapCoor[map_index].u32UpdateSysTime > 5000 && LstrObjMap->ObjMapCoor[map_index].u8HeightLockFlag != HEIGHT_LOCK_LEVEL2)
                {
#if DEBUG_DELETE_MAP
                    DEBUG_PRINT("timeStamp = %u,map_id = %d, Delete map D_010\n", GdSystemMsTimer, LstrObjMap->ObjMapCoor[map_index].eMapObjId);
#endif
                    LstrObjMap->ObjMapCoor[map_index] = GstrObjMapCoorInit;
                    LstrObjMap->u8MapNum--;
                    continue;
                }
            }
            else
            {
#if DEBUG_DELETE_MAP
            DEBUG_PRINT("timeStamp = %u,map_id = %d, Delete map D_011\n", GdSystemMsTimer, LstrObjMap->ObjMapCoor[map_index].eMapObjId);
#endif
                LstrObjMap->ObjMapCoor[map_index] = GstrObjMapCoorInit;
                LstrObjMap->u8MapNum--;
                continue;
            }

            /* map 入侵到车内超过 10cm 时，清空入侵车内的 map 信息 */
            if (LstrObjMap->ObjMapCoor[map_index].eObjBumperArea == OBJ_NONE_BUMP)
            {
#if DEBUG_DELETE_MAP
                DEBUG_PRINT("timeStamp = %u,map_id = %d, Delete map D_012\n", GdSystemMsTimer, LstrObjMap->ObjMapCoor[map_index].eMapObjId);
#endif
                LstrObjMap->ObjMapCoor[map_index] = GstrObjMapCoorInit;
                LstrObjMap->u8MapNum--;
                continue;
            }
        }
    }
}

static int DeleteVoerLapedLine(int matched_id, SnsAreaMapType *new_map)
{
    ObjMapInfoType *LstrObjMap;
    LstrObjMap = &GstrObjMap;
    int resulut = 1;

    for (int i = MAP_OBJ_0; i < MAP_OBJ_NUM; i++)
    {
        if (LstrObjMap->ObjMapCoor[i].u8MapExistFlag)
        {
            if (LstrObjMap->ObjMapCoor[i].u8Map_SFR_AreaFlag != 2 || LstrObjMap->ObjMapCoor[i].eMapObjType != OBJ_TYPE_STRAIGHT2_CORNER)
                continue;

            float map_dist = CalTwoMapDist(new_map->P1_OdoCoor,new_map->P2_OdoCoor, LstrObjMap->ObjMapCoor[i].P1_OdoCoor, LstrObjMap->ObjMapCoor[i].P2_OdoCoor);
            if (map_dist < 200)
            {
#if DEBUG_DELETE_MAP
                DEBUG_PRINT("timeStamp = %u,map_id = %d, Delete map D_014\n", GdSystemMsTimer, LstrObjMap->ObjMapCoor[matched_id].eMapObjId);
#endif
                LstrObjMap->ObjMapCoor[matched_id] = GstrObjMapCoorInit;
                LstrObjMap->u8MapNum--;
                resulut = 0;
                break;
            }
        }
    }

    return resulut;
}

/* 记录map回波高度随距离变化趋势 */
void RecordMapHeightHistory(int matched_id, SnsAreaMapType *new_map)
{
    ObjMapInfoType *LstrObjMap;
    LstrObjMap = &GstrObjMap;

    float master_dist = new_map->u16MasterDis;
    
    if (master_dist > 1540 || master_dist < 560)
    {
        return;
    }

    int buffer_index = 0;
    buffer_index = (master_dist - 550) / 100;
    buffer_index = 9 - buffer_index;

    if (buffer_index < 0 || buffer_index > MAP_TYPE_CNT_BUF_CNT - 1)
    {
        return;
    }

    if (LstrObjMap->ObjMapCoor[matched_id].MapHeightBuf[buffer_index].u16MasterDist == 0)
    {
        LstrObjMap->ObjMapCoor[matched_id].MapHeightBuf[buffer_index].u16MasterDist = new_map->u16MasterDis;
        LstrObjMap->ObjMapCoor[matched_id].MapHeightBuf[buffer_index].u16MasterHeight = new_map->u16StdMasterHeight;
    }
    else
    {
        if (ABS_VALUE(master_dist - (1500 - buffer_index * 100)) < 
            ABS_VALUE(LstrObjMap->ObjMapCoor[matched_id].MapHeightBuf[buffer_index].u16MasterDist - (1500 - buffer_index * 100)))
        {
            LstrObjMap->ObjMapCoor[matched_id].MapHeightBuf[buffer_index].u16MasterDist = new_map->u16MasterDis;
            LstrObjMap->ObjMapCoor[matched_id].MapHeightBuf[buffer_index].u16MasterHeight = new_map->u16StdMasterHeight;
        }
    }
}

/* 最新一帧 map 与匹配上的历史 map 做信息融合 */
void FusionFrontRearMap(int matched_id, SnsAreaMapType *new_map, int is_de_map)
{
    ObjMapInfoType *LstrObjMap;
    LstrObjMap = &GstrObjMap;
    
    if (new_map->MapSharp == 1)
    {
        float dx = ABS_VALUE(new_map->P1_CarCoor.fObjX - new_map->P2_CarCoor.fObjX);
        float dy = ABS_VALUE(new_map->P1_CarCoor.fObjY - new_map->P2_CarCoor.fObjY);
        if (dx / dy > 0.577)
        {
            return;
        }
    }

    if (new_map->Timestamp > LstrObjMap->ObjMapCoor[matched_id].u32UpdateSysTime)
    {
        /* 更新 map 的最新系统时间戳和存在的置信度 */
        LstrObjMap->ObjMapCoor[matched_id].u32UpdateSysTime = new_map->Timestamp;
        if (LstrObjMap->ObjMapCoor[matched_id].eObjExistProb < OBJ_PROB_LV6)
        {
            LstrObjMap->ObjMapCoor[matched_id].eObjExistProb++;
        }

        if (is_de_map == 0)
        {
            /* DEBUG_PRINT("timestamp = %u, new_map dist = %u, std_height = %u, chirp_height = %u\n",
                GdSystemMsTimer, new_map->u16MasterDis, new_map->u16StdMasterHeight, new_map->u16ChirpMasterHeight);*/

            RecordMapHeightHistory(matched_id, new_map);

            /*if ( new_map->u16MasterDis < 1500)
            {
                DEBUG_PRINT("**********\n");
                for (int i = 0; i < 10; i++)
                {
                    DEBUG_PRINT("timestamp = %u, matched_id = %d, history map height info, master_dist = %u,master_height = %u\n",
                        GdSystemMsTimer, matched_id,
                        LstrObjMap->ObjMapCoor[matched_id].MapHeightBuf[i].u16MasterDist,
                        LstrObjMap->ObjMapCoor[matched_id].MapHeightBuf[i].u16MasterHeight);
                }
                DEBUG_PRINT("\n");
            }*/
        }

        if (LstrObjMap->ObjMapCoor[matched_id].u8MapGrenerateType != 3 && is_de_map != 0)
        {
            if (new_map->enuMeasType == PDC_SNS_MEAS_STD)
            {
                LstrObjMap->ObjMapCoor[matched_id].u8DEMapStdCnt++;
            }

            if (new_map->enuMeasType == PDC_SNS_MEAS_ADV_CHIRP_DOWN || new_map->enuMeasType == PDC_SNS_MEAS_ADV_CHIRP_UP)
            {
                LstrObjMap->ObjMapCoor[matched_id].u8DEMapChripCnt++;
            }
        }
    }
    else
    {
        return;
    }

    if (GstrParkingGuidenceData.eCurrentAdasSts != APA_GUIDANCE)
    {
        G_parrle_parking_flag = 0;
    }
    else
    {
        G_parrle_parking_flag = 1;
    }

    /* 确定障碍物形状（point-map 或 line-map）的算法部分 */
    if(is_de_map == 0)
    {
        /* 统计线map匹配到线map的计数 */
        if (LstrObjMap->ObjMapCoor[matched_id].eMapObjType == OBJ_TYPE_STRAIGHT2_CORNER && new_map->MapSharp == 1)
        {
            //DEBUG_PRINT("time_stamp = %u,line-line matched\n", GdSystemMsTimer);
            if (LstrObjMap->ObjMapCoor[matched_id].u8LineMatchLineCnt < 250)
            {
                LstrObjMap->ObjMapCoor[matched_id].u8LineMatchLineCnt++;
            }
        }

        /* 统计点map匹配到点map的计数*/
        if (LstrObjMap->ObjMapCoor[matched_id].eMapObjType == OBJ_TYPE_POINT && new_map->MapSharp == 0 &&
            LstrObjMap->ObjMapCoor[matched_id].fMapToBumper_Dis > 750 && LstrObjMap->ObjMapCoor[matched_id].fMapToBumper_Dis < 2000)
        {
            if (LstrObjMap->ObjMapCoor[matched_id].u8PointMatchPointCnt < 250)
            {
                LstrObjMap->ObjMapCoor[matched_id].u8PointMatchPointCnt++;
            }

            if (LstrObjMap->ObjMapCoor[matched_id].u8P_P_MatchContiCnt < 250)
            {
                LstrObjMap->ObjMapCoor[matched_id].u8P_P_MatchContiCnt++;
            }
        }
        else
        {
            LstrObjMap->ObjMapCoor[matched_id].u8P_P_MatchContiCnt = 0;
        }

        if (new_map->MapSharp == 1)
        {
            LstrObjMap->ObjMapCoor[matched_id].u8PVC_Cnt = 0;
            if (LstrObjMap->ObjMapCoor[matched_id].u8BigWallCnt < 250)
            {
                LstrObjMap->ObjMapCoor[matched_id].u8BigWallCnt++;
            }

            if (LstrObjMap->ObjMapCoor[matched_id].u8BigWallCnt > 5 && 
                LstrObjMap->ObjMapCoor[matched_id].fMapToBumper_Dis > 750 &&
                LstrObjMap->ObjMapCoor[matched_id].eMapObjType == OBJ_TYPE_POINT)
            {
                int result = DeleteVoerLapedLine(matched_id, new_map);
                if (result == 1)
                {
                    LstrObjMap->ObjMapCoor[matched_id].eMapObjType = OBJ_TYPE_STRAIGHT2_CORNER;
                    SetGlobalMapCoor(matched_id, new_map);
                    LstrObjMap->ObjMapCoor[matched_id].u8HeightLockFlag = HEIGHT_LOCK_LEVEL0;
                }
            }
        }

        if (new_map->MapSharp == 0)
        {
            LstrObjMap->ObjMapCoor[matched_id].u8BigWallCnt = 0;
            if (LstrObjMap->ObjMapCoor[matched_id].u8PVC_Cnt < 250)
            {
                LstrObjMap->ObjMapCoor[matched_id].u8PVC_Cnt++;
            }

            if (LstrObjMap->ObjMapCoor[matched_id].u8PVC_Cnt > 20 && 
                LstrObjMap->ObjMapCoor[matched_id].u8LineMatchLineCnt < 10 && 
                LstrObjMap->ObjMapCoor[matched_id].eMapObjType == OBJ_TYPE_STRAIGHT2_CORNER &&
                LstrObjMap->ObjMapCoor[matched_id].fMapToBumper_Dis > 750)
            {
                LstrObjMap->ObjMapCoor[matched_id].eMapObjType = OBJ_TYPE_POINT;
                SetGlobalMapCoor(matched_id, new_map);
            }
        }

        float dist1 = PubAI_CalTwoPointDis(new_map->P1_OdoCoor.fObjX, new_map->P1_OdoCoor.fObjY, new_map->P2_OdoCoor.fObjX, new_map->P2_OdoCoor.fObjY);

        float dist2 = PubAI_CalTwoPointDis(LstrObjMap->ObjMapCoor[matched_id].P1_OdoCoor.fObjX, LstrObjMap->ObjMapCoor[matched_id].P1_OdoCoor.fObjY,
            LstrObjMap->ObjMapCoor[matched_id].P2_OdoCoor.fObjX, LstrObjMap->ObjMapCoor[matched_id].P2_OdoCoor.fObjY);

        if (ABS_VALUE(dist1 - dist2) < 1000 && dist2 < 2000 && 
            (new_map->MapSharp == 1 && LstrObjMap->ObjMapCoor[matched_id].eMapObjType == OBJ_TYPE_STRAIGHT2_CORNER || 
             new_map->MapSharp == 0 && LstrObjMap->ObjMapCoor[matched_id].eMapObjType == OBJ_TYPE_POINT))
        {
            int matched_time;
            float temp_dist1 = 0;
            float temp_dist2 = 0;
            float g_map_p1_odo_x, g_map_p1_odo_y, g_map_p2_odo_x, g_map_p2_odo_y;
            float n_map_p1_odo_x, n_map_p1_odo_y, n_map_p2_odo_x, n_map_p2_odo_y;
            g_map_p1_odo_x = LstrObjMap->ObjMapCoor[matched_id].P1_OdoCoor.fObjX;
            g_map_p1_odo_y = LstrObjMap->ObjMapCoor[matched_id].P1_OdoCoor.fObjY;
            g_map_p2_odo_x = LstrObjMap->ObjMapCoor[matched_id].P2_OdoCoor.fObjX;
            g_map_p2_odo_y = LstrObjMap->ObjMapCoor[matched_id].P2_OdoCoor.fObjY;

            n_map_p1_odo_x = new_map->P1_OdoCoor.fObjX;
            n_map_p1_odo_y = new_map->P1_OdoCoor.fObjY;
            n_map_p2_odo_x = new_map->P2_OdoCoor.fObjX;
            n_map_p2_odo_y = new_map->P2_OdoCoor.fObjY;
            if (new_map->MapSharp == 1 && LstrObjMap->ObjMapCoor[matched_id].eMapObjType == OBJ_TYPE_STRAIGHT2_CORNER)
            {
                matched_time = LstrObjMap->ObjMapCoor[matched_id].u8LineMatchLineCnt;
            }

            if (new_map->MapSharp == 0 && LstrObjMap->ObjMapCoor[matched_id].eMapObjType == OBJ_TYPE_POINT)
            {
                matched_time = LstrObjMap->ObjMapCoor[matched_id].u8PointMatchPointCnt;
            }

            temp_dist1 += PubAI_CalTwoPointDis(g_map_p1_odo_x, g_map_p1_odo_y, n_map_p1_odo_x, n_map_p1_odo_y);
            temp_dist1 += PubAI_CalTwoPointDis(g_map_p2_odo_x, g_map_p2_odo_y, n_map_p2_odo_x, n_map_p2_odo_y);
            temp_dist2 += PubAI_CalTwoPointDis(g_map_p2_odo_x, g_map_p2_odo_y, n_map_p1_odo_x, n_map_p1_odo_y);
            temp_dist2 += PubAI_CalTwoPointDis(g_map_p1_odo_x, g_map_p1_odo_y, n_map_p2_odo_x, n_map_p2_odo_y);

            if (temp_dist1 > temp_dist2)
            {
                LstrObjMap->ObjMapCoor[matched_id].P1_OdoCoor.fObjX = (g_map_p1_odo_x*matched_time + n_map_p2_odo_x) / (matched_time+1);
                LstrObjMap->ObjMapCoor[matched_id].P1_OdoCoor.fObjY = (g_map_p1_odo_y*matched_time + n_map_p2_odo_y) / (matched_time+1);
                LstrObjMap->ObjMapCoor[matched_id].P2_OdoCoor.fObjX = (g_map_p2_odo_x*matched_time + n_map_p1_odo_x) / (matched_time+1);
                LstrObjMap->ObjMapCoor[matched_id].P2_OdoCoor.fObjY = (g_map_p2_odo_y*matched_time + n_map_p1_odo_y) / (matched_time+1);
            }
            else
            {
                LstrObjMap->ObjMapCoor[matched_id].P1_OdoCoor.fObjX = (g_map_p1_odo_x*matched_time + n_map_p1_odo_x) / (matched_time+1);
                LstrObjMap->ObjMapCoor[matched_id].P1_OdoCoor.fObjY = (g_map_p1_odo_y*matched_time + n_map_p1_odo_y) / (matched_time+1);
                LstrObjMap->ObjMapCoor[matched_id].P2_OdoCoor.fObjX = (g_map_p2_odo_x*matched_time + n_map_p2_odo_x) / (matched_time+1);
                LstrObjMap->ObjMapCoor[matched_id].P2_OdoCoor.fObjY = (g_map_p2_odo_y*matched_time + n_map_p2_odo_y) / (matched_time+1);
            }

            PubAI_TransObjOdoCoorToCarCoor(&LstrObjMap->ObjMapCoor[matched_id].P1_OdoCoor.fObjX, &LstrObjMap->ObjMapCoor[matched_id].P1_OdoCoor.fObjY,
                &LstrObjMap->ObjMapCoor[matched_id].P1_CarCoor.fObjX, &LstrObjMap->ObjMapCoor[matched_id].P1_CarCoor.fObjY);
            PubAI_TransObjOdoCoorToCarCoor(&LstrObjMap->ObjMapCoor[matched_id].P2_OdoCoor.fObjX, &LstrObjMap->ObjMapCoor[matched_id].P2_OdoCoor.fObjY,
                &LstrObjMap->ObjMapCoor[matched_id].P2_CarCoor.fObjX, &LstrObjMap->ObjMapCoor[matched_id].P2_CarCoor.fObjY);
        }
    }

    /* 障碍物高低属性 */
    {
        float degree = GstrParkingGuidenceData.fCarAngleSub / 3.1415926 * 180;
        float dist = LstrObjMap->ObjMapCoor[matched_id].fMapToBumper_Dis;
        eMapBumperAreaType area = LstrObjMap->ObjMapCoor[matched_id].eObjBumperArea;

        /* 垂直泊车过程中，车尾对障碍车车头时，车尾障碍物置高 */
        /*if (GstrParkingGuidenceData.eSlotType == SLOT_VERTICAL && degree < 70 && dist <1500 && area == OBJ_REAR_BUMP)
        {
            if (LstrObjMap->ObjMapCoor[matched_id].u8HeightLockFlag == HEIGHT_LOCK_LEVEL0)
            {
                LstrObjMap->ObjMapCoor[matched_id].eMapObjHeight = OBJ_HIGH;
                LstrObjMap->ObjMapCoor[matched_id].u8HeightLockFlag = HEIGHT_LOCK_LEVEL1;
            }

            return;
        }*/

        /* 水平泊车过程中，车前对车尾，车前障碍物置高 */
        if (GstrParkingGuidenceData.eSlotType == SLOT_PARALLEL && degree > 30)
        {
            G_parrle_parking_flag = 2;
        }

        if (GstrParkingGuidenceData.eSlotType == SLOT_PARALLEL && dist < 1500 && area == OBJ_FRONT_BUMP && G_parrle_parking_flag == 2 && degree < 15)
        {
            if (LstrObjMap->ObjMapCoor[matched_id].u8HeightLockFlag == HEIGHT_LOCK_LEVEL0)
            {
                LstrObjMap->ObjMapCoor[matched_id].eMapObjHeight = OBJ_HIGH;
                LstrObjMap->ObjMapCoor[matched_id].u8HeightLockFlag = HEIGHT_LOCK_LEVEL1;
            }
            
            return;
        }

        if (LstrObjMap->ObjMapCoor[matched_id].u8HeightLockFlag == HEIGHT_LOCK_LEVEL0)
        {
#if 0
            DEBUG_PRINT("timestamp = %u,g_vheBuffer->VheMap[i].enuMapType = %d\n",GdSystemMsTimer, new_map->enuMapType);
#endif
            if (LstrObjMap->ObjMapCoor[matched_id].eMapObjType == OBJ_TYPE_STRAIGHT2_CORNER)
            {
                if (LstrObjMap->ObjMapCoor[matched_id].fMapToBumper_Dis < 2200)
                {
                    if (new_map->enuMapType == OBJ_HIGH_CURB_TYPE ||
                        new_map->enuMapType == OBJ_PVC_PIPE_TYPE ||
                        new_map->enuMapType == OBJ_BIGWALL_TYPE)
                    {
                        if (new_map->enuMapType == OBJ_BIGWALL_TYPE && LstrObjMap->ObjMapCoor[matched_id].u8HighCurbCnt < 250 && LstrObjMap->ObjMapCoor[matched_id].eObjMoveSts != OBJ_TO_CAR_STOP_STOP)
                        {
                            LstrObjMap->ObjMapCoor[matched_id].u8HighCurbCnt += 2;
                        }
                        if (LstrObjMap->ObjMapCoor[matched_id].u8HighCurbCnt < 250 && LstrObjMap->ObjMapCoor[matched_id].eObjMoveSts != OBJ_TO_CAR_STOP_STOP)
                        {
                            LstrObjMap->ObjMapCoor[matched_id].u8HighCurbCnt++;// = OBJ_HIGH;
                        }
                    }
                    else
                    {
                        if (LstrObjMap->ObjMapCoor[matched_id].u8LowCurbCnt < 250 && LstrObjMap->ObjMapCoor[matched_id].eObjMoveSts != OBJ_TO_CAR_STOP_STOP)
                        {
                            LstrObjMap->ObjMapCoor[matched_id].u8LowCurbCnt++;// = OBJ_LOW;
                        }
                    }

                    if (LstrObjMap->ObjMapCoor[matched_id].eObjExistProb < OBJ_PROB_LV4 && LstrObjMap->ObjMapCoor[matched_id].u8MapGrenerateType != 1)
                    {
                        LstrObjMap->ObjMapCoor[matched_id].eMapObjHeight = OBJ_LOW;
                    }
                    else
                    {
                        if (LstrObjMap->ObjMapCoor[matched_id].u8LowCurbCnt > LstrObjMap->ObjMapCoor[matched_id].u8HighCurbCnt &&
                            LstrObjMap->ObjMapCoor[matched_id].u8HeightLockFlag == HEIGHT_LOCK_LEVEL0)
                        {
                            LstrObjMap->ObjMapCoor[matched_id].eMapObjHeight = OBJ_LOW;
                        }
                        else
                        {
                            LstrObjMap->ObjMapCoor[matched_id].eMapObjHeight = OBJ_HIGH;
                        }

                        /* 60cm 以内还能匹配到 map，且执行都大于 LV3， 将 map 置高 */
                        if (LstrObjMap->ObjMapCoor[matched_id].fMapToBumper_Dis < 600 && LstrObjMap->ObjMapCoor[matched_id].eObjExistProb > OBJ_PROB_LV3 &&
                            LstrObjMap->ObjMapCoor[matched_id].eMapObjType == OBJ_TYPE_POINT)
                        {
                            //DEBUG_PRINT("timestamp = %u, map_id = %d, 60cm has also matched new map,set map to be high\n", GdSystemMsTimer, matched_id);
                            LstrObjMap->ObjMapCoor[matched_id].eMapObjHeight = OBJ_HIGH;
                        }
                        }
                    }
                else
                {
                    LstrObjMap->ObjMapCoor[matched_id].eMapObjHeight = OBJ_LOW;
                }
            }
            else
            {
                if (LstrObjMap->ObjMapCoor[matched_id].fMapToBumper_Dis < 2000)
                {
                    if (LstrObjMap->ObjMapCoor[matched_id].u8TwoEchoDeMatchedCnt > 0)
                    {
#if DEBUG_HIGH_LOW
                        DEBUG_PRINT("timestamp = %u, map_id = %d, point map set to be High H_L_003, to bump dist = %f\n",
                            GdSystemMsTimer, LstrObjMap->ObjMapCoor[matched_id].eMapObjId, LstrObjMap->ObjMapCoor[matched_id].fMapToBumper_Dis);
#endif
                        LstrObjMap->ObjMapCoor[matched_id].u8HeightLockFlag = HEIGHT_LOCK_LEVEL1;
                        LstrObjMap->ObjMapCoor[matched_id].eMapObjHeight = OBJ_HIGH;
                    }
                    else
                    {
                        LstrObjMap->ObjMapCoor[matched_id].eMapObjHeight = OBJ_LOW;
                    }
                }
                else
                {
                    LstrObjMap->ObjMapCoor[matched_id].eMapObjHeight = OBJ_LOW;
                }
                
            }
        }
    }
#if debug_print
    DEBUG_PRINT("timstamp = %.3u, Map_id = %d, heightCnt = %d, lowCnt = %d, bump_dist = %f, map_height = %d, height_lock =%u \n",
        LstrObjMap->ObjMapCoor[matched_id].u32UpdateSysTime,
        matched_id,
        LstrObjMap->ObjMapCoor[matched_id].u8HighCurbCnt,
        LstrObjMap->ObjMapCoor[matched_id].u8LowCurbCnt,
        LstrObjMap->ObjMapCoor[matched_id].fMapToBumper_Dis,
        LstrObjMap->ObjMapCoor[matched_id].eMapObjHeight,
        LstrObjMap->ObjMapCoor[matched_id].u8HeightLockFlag);
#endif
}



/* 生成新的全局 map 信息 */
void CreateNewGlobalMap(SnsAreaMapType *new_map, uint8 generate_type)
{
    ObjMapInfoType *LstrObjMap;
    LstrObjMap = &GstrObjMap;

    {
        if (new_map->MapSharp == 1)
        {
            float dx = ABS_VALUE(new_map->P1_CarCoor.fObjX - new_map->P2_CarCoor.fObjX);
            float dy = ABS_VALUE(new_map->P1_CarCoor.fObjY - new_map->P2_CarCoor.fObjY);

            if (dx / dy > 0.577)
            {
                return;
            }
        }

        int is_replace;
        eMapObjIdType LeMapId = FindFarthestMapIndex(&is_replace);
        if (is_replace == 0)
        {
            LstrObjMap->u8MapNum++;
        }

        LstrObjMap->ObjMapCoor[LeMapId] = GstrObjMapCoorInit;
        LstrObjMap->ObjMapCoor[LeMapId].u8Map_SFR_AreaFlag = 2;
        LstrObjMap->ObjMapCoor[LeMapId].u8MapExistFlag = 1;
        LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb = OBJ_PROB_LV1;
        LstrObjMap->ObjMapCoor[LeMapId].eMapObjId = LeMapId;
        LstrObjMap->ObjMapCoor[LeMapId].eObjFollowSts = OBJ_DET_UPDATE;
        SetGlobalMapCoor(LeMapId, new_map);
        LstrObjMap->ObjMapCoor[LeMapId].u32CreatMapTime = new_map->Timestamp;
        LstrObjMap->ObjMapCoor[LeMapId].u32UpdateSysTime = new_map->Timestamp;
        if (new_map->MapSharp == 0)
        {
            LstrObjMap->ObjMapCoor[LeMapId].eMapObjType = OBJ_TYPE_POINT;
        }
        else
        {
            LstrObjMap->ObjMapCoor[LeMapId].eMapObjType = OBJ_TYPE_STRAIGHT2_CORNER;
        }

        /* 初次登场，障碍默认为低 */
        LstrObjMap->ObjMapCoor[LeMapId].eMapObjHeight = OBJ_LOW;
        LstrObjMap->ObjMapCoor[LeMapId].eObjHeightProb = OBJ_PROB_LV6;
        LstrObjMap->ObjMapCoor[LeMapId].u8MapDisplayFlag = 0;
        if (LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjX > 0)
        {
            LstrObjMap->ObjMapCoor[LeMapId].eObjBumperArea = OBJ_FRONT_BUMP;
        }
        else
        {
            LstrObjMap->ObjMapCoor[LeMapId].eObjBumperArea = OBJ_REAR_BUMP;
        }

        /* map 生成类型判定（聚类、单主发、双主发） */
        LstrObjMap->ObjMapCoor[LeMapId].u8MapGrenerateType = generate_type;
        UpdateMapInf(&LstrObjMap->ObjMapCoor[LeMapId]);
        LstrObjMap->ObjMapCoor[LeMapId].fSideMapToSelfSnsDis = LstrObjMap->ObjMapCoor[LeMapId].fMapToBumper_Dis;
        if (GstrSnsCarMovSts[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FMR].eCarDir == SNS_CAR_STOP)
        {
            LstrObjMap->ObjMapCoor[LeMapId].u8MapCreateCarSts = 1;
        }
        else
        {
            LstrObjMap->ObjMapCoor[LeMapId].u8MapCreateCarSts = 2;
        }

        if (generate_type != 3)
        {
            if (new_map->enuMeasType == PDC_SNS_MEAS_STD)
            {
                LstrObjMap->ObjMapCoor[LeMapId].u8DEMapStdCnt++;
            }

            if (new_map->enuMeasType == PDC_SNS_MEAS_ADV_CHIRP_DOWN || new_map->enuMeasType == PDC_SNS_MEAS_ADV_CHIRP_UP)
            {
                LstrObjMap->ObjMapCoor[LeMapId].u8DEMapChripCnt++;
            }
        }
#if DEBUG_DELETE_MAP
        DEBUG_PRINT("timestamp = %u, create map_id = %d, generate type = %u, map sharp = %u\n", GdSystemMsTimer, LeMapId, generate_type, new_map->MapSharp);
#endif

    }
}

uint8 JudgeMapIsLine(int front_rear, float *real_dist)
{
    uint8 result = 0;

    ObjCoorType temp_buffer[100];
    int indexCnt = GetVechBufferCarCoorData(temp_buffer, front_rear);
    
    float y_mean = 0;
    float x_mean = 0;
   
    //DEBUG_PRINT("time_stamp = %u, insert_point size = %d\n", GdSystemMsTimer, indexCnt);
    if (indexCnt < 5)
        return result;

    for (int i = 0; i < indexCnt; i++)
    {
        x_mean += temp_buffer[i].fObjX;
        y_mean += temp_buffer[i].fObjY;
    }

    x_mean = x_mean / indexCnt;
    y_mean = y_mean / indexCnt;
    *real_dist = x_mean;

    float covYY = 0;
    float covXX = 0;
    for (int i = 0; i < indexCnt; i++)
    {
        covYY += (temp_buffer[i].fObjY - y_mean)*(temp_buffer[i].fObjY - y_mean);
        covXX += (temp_buffer[i].fObjX - x_mean)*(temp_buffer[i].fObjX - x_mean);
    }
    covYY /= indexCnt;
    covXX /= indexCnt;

    //DEBUG_PRINT("time_stamp = %u, covXX = %f, covYY = %f\n", GdSystemMsTimer, covXX, covYY);
    /* 通过 Y 轴方差大小来判定当前全局点云的数据是否为线 map 类型 */ 
    if (covYY > 20000 && covXX < 2000 )
    {
        result = 1;
    }

    return result;
}

static void DeleteDeMapNoise(uint8 *result)
{
    UpdateRealDistBuffer();
    /* FML 探头有效性 DE_MAP 判定 */
    if (result[0] == 1)
    {
        if (GdSystemMsTimer - G_SnsRealDist[1].u32Timestamp < 200)
        {
            if (G_SnsRealDist[1].real_dist < G_SnsRealDist[2].real_dist)
            {
                result[0] = 0;
            }
        }
    }

    /* FMR 探头有效性 DE_MAP 判定 */
    if (result[1] == 1)
    {
        if (GdSystemMsTimer - G_SnsRealDist[4].u32Timestamp < 200)
        {
            if (G_SnsRealDist[4].real_dist < G_SnsRealDist[3].real_dist)
            {
                result[1] = 0;
            }
        }
    }

    /* RML 探头有效性 DE_MAP 判定 */
    if (result[2] == 1)
    {
        if (GdSystemMsTimer - G_SnsRealDist[7].u32Timestamp < 200)
        {
            if (G_SnsRealDist[7].real_dist < G_SnsRealDist[8].real_dist)
            {
                result[2] = 0;
            }
        }
    }

    /* RMR 探头有效性 DE_MAP 判定 */
    if (result[3] == 1)
    {
        if (GdSystemMsTimer - G_SnsRealDist[10].u32Timestamp < 200)
        {
            if (G_SnsRealDist[10].real_dist < G_SnsRealDist[9].real_dist)
            {
                result[3] = 0;
            }
        }
    }
}

static void CopySnsMap2GSnsMap(SnsAreaMapType *FourSnsDeMap,int index, int G_index)
{
    G_FourSnsDeMap[G_index].Timestamp = FourSnsDeMap[index].Timestamp;
    G_FourSnsDeMap[G_index].MapSharp = FourSnsDeMap[index].MapSharp;
    G_FourSnsDeMap[G_index].P1_CarCoor.fObjX = FourSnsDeMap[index].P1_CarCoor.fObjX;
    G_FourSnsDeMap[G_index].P1_CarCoor.fObjY = FourSnsDeMap[index].P1_CarCoor.fObjY;
    G_FourSnsDeMap[G_index].P2_CarCoor.fObjX = FourSnsDeMap[index].P2_CarCoor.fObjX;
    G_FourSnsDeMap[G_index].P2_CarCoor.fObjY = FourSnsDeMap[index].P2_CarCoor.fObjY;
    G_FourSnsDeMap[G_index].P1_OdoCoor.fObjX = FourSnsDeMap[index].P1_OdoCoor.fObjX;
    G_FourSnsDeMap[G_index].P1_OdoCoor.fObjY = FourSnsDeMap[index].P1_OdoCoor.fObjY;
    G_FourSnsDeMap[G_index].P2_OdoCoor.fObjX = FourSnsDeMap[index].P2_OdoCoor.fObjX;
    G_FourSnsDeMap[G_index].P2_OdoCoor.fObjY = FourSnsDeMap[index].P2_OdoCoor.fObjY;
    G_FourSnsDeMap[G_index].enuMapType = FourSnsDeMap[index].enuMapType;
}

/* 近距离情况下，DE 生成 map 信息 */
void Fusion_DE_Map(void)
{
    ObjMapInfoType *LstrObjMap;
    LstrObjMap = &GstrObjMap;

    /* 初始化 G_FourSnsDeMap 数据 */ 
    if (G_FourSnsDeMap_init == 0)
    {
        G_FourSnsDeMap_init = 1;
        for (int i = 0; i < 4; i++)
        {
            G_FourSnsDeMap[i] = GstrAreaMapInit;
        }
    }

    SnsAreaMapType FourSnsDeMap[8];
    float SnsRealDist[8];
    uint8 result[8] = {0, 0, 0, 0, 0, 0, 0, 0};
    result[0] = DEGenerateMap(&FourSnsDeMap[0], PDC_SNS_GROUP_FRONT, PDC_SNS_CH_FML, 880, &SnsRealDist[0]);   // 左前雷达
    result[1] = DEGenerateMap(&FourSnsDeMap[1], PDC_SNS_GROUP_FRONT, PDC_SNS_CH_FMR, 880, &SnsRealDist[1]);   // 右前雷达
    result[2] = DEGenerateMap(&FourSnsDeMap[2], PDC_SNS_GROUP_REAR, PDC_SNS_CH_RML, 880, &SnsRealDist[2]);    // 左后雷达
    result[3] = DEGenerateMap(&FourSnsDeMap[3], PDC_SNS_GROUP_REAR, PDC_SNS_CH_RMR, 880, &SnsRealDist[3]);    // 右后雷达

    float threshold_dist = 350;
    if (GstrParkingGuidenceData.eParking_Sts == PARKING_SUMMON)
    {
        threshold_dist = 280;
    }

    result[4] = DEGenerateMap(&FourSnsDeMap[4], PDC_SNS_GROUP_FRONT, PDC_SNS_CH_FL, threshold_dist, &SnsRealDist[4]);    // 左角前雷达
    result[5] = DEGenerateMap(&FourSnsDeMap[5], PDC_SNS_GROUP_FRONT, PDC_SNS_CH_FR, threshold_dist, &SnsRealDist[5]);    // 右角前雷达
    result[6] = DEGenerateMap(&FourSnsDeMap[6], PDC_SNS_GROUP_REAR, PDC_SNS_CH_RL, threshold_dist, &SnsRealDist[6]);     // 左角后雷达
    result[7] = DEGenerateMap(&FourSnsDeMap[7], PDC_SNS_GROUP_REAR, PDC_SNS_CH_RR, threshold_dist, &SnsRealDist[7]);     // 右角后雷达
    
    

    for (int i = 4; i < 8; i++)
    {
        if (result[i] == 1)
        {
            result[i] = 2;
        }
    }

    /* 角雷达在远离过程中不画 */
    if (GstrSnsCarMovSts[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FL].eCarDir == SNS_CAR_BACKWARD)
    {
        result[4] = 0;
    }

    if (GstrSnsCarMovSts[PDC_SNS_GROUP_FRONT][PDC_SNS_CH_FR].eCarDir == SNS_CAR_BACKWARD)
    {
        result[5] = 0;
    }

    if (GstrSnsCarMovSts[PDC_SNS_GROUP_REAR][PDC_SNS_CH_RL].eCarDir == SNS_CAR_FORWARD)
    {
        result[6] = 0;
    }

    if (GstrSnsCarMovSts[PDC_SNS_GROUP_REAR][PDC_SNS_CH_RR].eCarDir == SNS_CAR_FORWARD)
    {
        result[7] = 0;
    }

    /* 删除中间雷达误生成的噪点map */
    DeleteDeMapNoise(result);

    /* 调试同频干扰问题 2024-05-21 */
    if (GstrParkingGuidenceData.u8SameFreqNoiseFlag[0] == 1)
    {
        //DEBUG_PRINT("front bump same fre noise \n");
        result[0] = 0;
        result[1] = 0;
        result[4] = 0;
        result[5] = 0;
    }

    if (GstrParkingGuidenceData.u8SameFreqNoiseFlag[1] == 1)
    {
        result[2] = 0;
        result[3] = 0;
        result[6] = 0;
        result[7] = 0;
    }

    for (int i = 0; i < 8; i++)
    {
        if (result[i] > 0)
        {
            float min_dist = 65535;
            int matched_id = -1;
            for (int j = MAP_OBJ_0; j < MAP_OBJ_NUM; j++)
            {
                if (LstrObjMap->ObjMapCoor[j].u8Map_SFR_AreaFlag == 2)
                {
                    float dist = CalTwoMapDist(FourSnsDeMap[i].P1_OdoCoor, FourSnsDeMap[i].P2_OdoCoor,
                        LstrObjMap->ObjMapCoor[j].P1_OdoCoor, LstrObjMap->ObjMapCoor[j].P2_OdoCoor);
                    if (dist < min_dist)
                    {
                        min_dist = dist;
                        matched_id = j;
                    }
                }
            }

            if (matched_id != -1)
            {
                if (LstrObjMap->ObjMapCoor[matched_id].eMapObjType == OBJ_TYPE_POINT)
                {
                    float abs_dy = ABS_VALUE(LstrObjMap->ObjMapCoor[matched_id].P1_CarCoor.fObjY - FourSnsDeMap[i].P1_CarCoor.fObjY);
                    float abs_dx = ABS_VALUE(LstrObjMap->ObjMapCoor[matched_id].P1_CarCoor.fObjX - FourSnsDeMap[i].P1_CarCoor.fObjX);

                    if (abs_dx < 200 && abs_dy < 500)
                    {
                        if (SnsRealDist[i] < 400)
                        {
                            /* 近距离DEMap匹配时，兜底置高策略 */
                            if (LstrObjMap->ObjMapCoor[matched_id].u8HeightLockFlag < HEIGHT_LOCK_LEVEL3)
                            {
#if DEBUG_HIGH_LOW
                                DEBUG_PRINT("timestamp = %u, map_id = %d, point map set to be High H_L_001\n", GdSystemMsTimer, LstrObjMap->ObjMapCoor[matched_id].eMapObjId);
#endif
                                LstrObjMap->ObjMapCoor[matched_id].u8HeightLockFlag = HEIGHT_LOCK_LEVEL3;
                                LstrObjMap->ObjMapCoor[matched_id].eMapObjHeight = OBJ_HIGH;
                                if (LstrObjMap->ObjMapCoor[matched_id].eObjExistProb < OBJ_PROB_LV6)
                                {
                                    LstrObjMap->ObjMapCoor[matched_id].eObjExistProb = OBJ_PROB_LV6;
                                }
                            }
                        }

                        if (LstrObjMap->ObjMapCoor[matched_id].u8Map_SFR_AreaFlag == 2)
                        {
                            FusionFrontRearMap(matched_id, &FourSnsDeMap[i], 1);
                        }
                    }
                    else
                    {
                        if (GdSystemMsTimer - FourSnsDeMap[i].Timestamp < 200)
                        {
                            CreateNewGlobalMap(&FourSnsDeMap[i], result[i]);
                        }
                    }
                }

                if (LstrObjMap->ObjMapCoor[matched_id].eMapObjType == OBJ_TYPE_STRAIGHT2_CORNER)
                {
                    if (min_dist < 300)
                    {
                        if (SnsRealDist[i] < 400)
                        {
                            /* 近距离DEMap匹配时，兜底置高策略 */
                            if (LstrObjMap->ObjMapCoor[matched_id].u8HeightLockFlag < HEIGHT_LOCK_LEVEL3)
                            {
#if DEBUG_HIGH_LOW
                                DEBUG_PRINT("timestamp = %u, map_id = %d, point map set to be High H_L_008\n", GdSystemMsTimer, LstrObjMap->ObjMapCoor[matched_id].eMapObjId);
#endif
                                LstrObjMap->ObjMapCoor[matched_id].u8HeightLockFlag = HEIGHT_LOCK_LEVEL3;
                                LstrObjMap->ObjMapCoor[matched_id].eMapObjHeight = OBJ_HIGH;
                                if (LstrObjMap->ObjMapCoor[matched_id].eObjExistProb < OBJ_PROB_LV6)
                                {
                                    LstrObjMap->ObjMapCoor[matched_id].eObjExistProb = OBJ_PROB_LV6;
                                }
                            }
                        }
                        
                    }
                }
            }
            else
            {
                if (GdSystemMsTimer - FourSnsDeMap[i].Timestamp < 200)
                {
                    CreateNewGlobalMap(&FourSnsDeMap[i], result[i]);
                }
            }

        }
    }
}

void FusionGVheMap(void)
{
    VehiclePointCloudBufType* g_vheBuffer;
    g_vheBuffer = &GstrVehiclePointCloudBuf;

    int vhe_map_num = g_vheBuffer->u8VheMapNum;
    if (vhe_map_num < 2)
        return;
    int delete_index = 0;
    int break_flag = 0;
    for (int i = 0; i < vhe_map_num; i++)
    {
        for (int j = 0; j < vhe_map_num; j++)
        {
            if (i == j)
            {
                continue;
            }

            if (g_vheBuffer->VheMap[i].MapSharp == 0 && g_vheBuffer->VheMap[j].MapSharp == 0)
            {
                if (ABS_VALUE(g_vheBuffer->VheMap[i].P1_CarCoor.fObjX - g_vheBuffer->VheMap[j].P1_CarCoor.fObjX) < 50 &&
                    ABS_VALUE(g_vheBuffer->VheMap[i].P1_CarCoor.fObjY) < 600 && ABS_VALUE(g_vheBuffer->VheMap[j].P1_CarCoor.fObjY) < 600)
                {
                    g_vheBuffer->VheMap[i].MapSharp = 1;
                    g_vheBuffer->VheMap[i].P2_CarCoor.fObjX = g_vheBuffer->VheMap[j].P1_CarCoor.fObjX;
                    g_vheBuffer->VheMap[i].P2_CarCoor.fObjY = g_vheBuffer->VheMap[j].P1_CarCoor.fObjY;
                    g_vheBuffer->VheMap[i].P2_OdoCoor.fObjX = g_vheBuffer->VheMap[j].P1_OdoCoor.fObjX;
                    g_vheBuffer->VheMap[i].P2_OdoCoor.fObjY = g_vheBuffer->VheMap[j].P1_OdoCoor.fObjY;
                    delete_index = j;
                    break_flag = 1;
                    g_vheBuffer->VheMap[j] = GstrAreaMapInit;
                    break;
                }
            }
        }
    }

    if (delete_index == 0)
        return;

    if (delete_index != (vhe_map_num - 1))
    {
        g_vheBuffer->VheMap[delete_index].MapSharp                  = g_vheBuffer->VheMap[vhe_map_num - 1].MapSharp;
        g_vheBuffer->VheMap[delete_index].P1_CarCoor.fObjX          = g_vheBuffer->VheMap[vhe_map_num - 1].P1_CarCoor.fObjX;
        g_vheBuffer->VheMap[delete_index].P1_CarCoor.fObjY          = g_vheBuffer->VheMap[vhe_map_num - 1].P1_CarCoor.fObjY;
        g_vheBuffer->VheMap[delete_index].P1_OdoCoor.fObjX          = g_vheBuffer->VheMap[vhe_map_num - 1].P1_OdoCoor.fObjX;
        g_vheBuffer->VheMap[delete_index].P1_OdoCoor.fObjY          = g_vheBuffer->VheMap[vhe_map_num - 1].P1_OdoCoor.fObjY;
        g_vheBuffer->VheMap[delete_index].P2_CarCoor.fObjX          = g_vheBuffer->VheMap[vhe_map_num - 1].P2_CarCoor.fObjX;
        g_vheBuffer->VheMap[delete_index].P2_CarCoor.fObjY          = g_vheBuffer->VheMap[vhe_map_num - 1].P2_CarCoor.fObjY;
        g_vheBuffer->VheMap[delete_index].P2_OdoCoor.fObjX          = g_vheBuffer->VheMap[vhe_map_num - 1].P2_OdoCoor.fObjX;
        g_vheBuffer->VheMap[delete_index].P2_OdoCoor.fObjY          = g_vheBuffer->VheMap[vhe_map_num - 1].P2_OdoCoor.fObjY;
        g_vheBuffer->VheMap[delete_index].Timestamp                 = g_vheBuffer->VheMap[vhe_map_num - 1].Timestamp;
        g_vheBuffer->VheMap[delete_index].enuMapType                = g_vheBuffer->VheMap[vhe_map_num - 1].enuMapType;
        g_vheBuffer->VheMap[delete_index].u16MasterDis              = g_vheBuffer->VheMap[vhe_map_num - 1].u16MasterDis;
        g_vheBuffer->VheMap[delete_index].u16ChirpMasterHeight      = g_vheBuffer->VheMap[vhe_map_num - 1].u16ChirpMasterHeight;
        g_vheBuffer->VheMap[delete_index].u16StdMasterHeight        = g_vheBuffer->VheMap[vhe_map_num - 1].u16StdMasterHeight;
        g_vheBuffer->VheMap[delete_index].u8BeFusionToRealMapFlag   = g_vheBuffer->VheMap[vhe_map_num - 1].u8BeFusionToRealMapFlag;
        g_vheBuffer->VheMap[vhe_map_num - 1] = GstrAreaMapInit;
        g_vheBuffer->u8VheMapNum--;
    }

    if(delete_index == (vhe_map_num - 1))
    {
        g_vheBuffer->VheMap[vhe_map_num - 1] = GstrAreaMapInit;
        g_vheBuffer->u8VheMapNum--;
    }
}

/* 基于全局点云聚类生成 map 的融合算法主函数 */
void VheMapFusion(void)
{
    /* 更新车辆当前状态 */
    ParkingGuidenceDataUpdate();
    ObjMapInfoType *LstrObjMap;
    VehiclePointCloudBufType* g_vheBuffer;
    LstrObjMap = &GstrObjMap;
    g_vheBuffer = &GstrVehiclePointCloudBuf;

    FusionGVheMap();
    int vhe_map_num = g_vheBuffer->u8VheMapNum;

#if 0  //for debug test the map trans for can 
    ObjMapInit();
    eMapObjIdType LeMapId = 0;
    LstrObjMap->u8MapNum = vhe_map_num;
    UpdateMapCarCoor();
    for (int i = 0; i < vhe_map_num; i++)
    {
        if (GdSystemMsTimer - g_vheBuffer->VheMap[i].Timestamp > 200)
        {
            continue;
        }
        //printf("trans map to can ,map index = %d\n", i);
        LstrObjMap->ObjMapCoor[LeMapId].eMapObjId = i;// g_vheBuffer->VheMap[i].MapId;
        LstrObjMap->ObjMapCoor[LeMapId].P1_OdoCoor.fObjX = g_vheBuffer->VheMap[i].P1_OdoCoor.fObjX;
        LstrObjMap->ObjMapCoor[LeMapId].P1_OdoCoor.fObjY = g_vheBuffer->VheMap[i].P1_OdoCoor.fObjY;
        LstrObjMap->ObjMapCoor[LeMapId].P2_OdoCoor.fObjX = g_vheBuffer->VheMap[i].P2_OdoCoor.fObjX;
        LstrObjMap->ObjMapCoor[LeMapId].P2_OdoCoor.fObjY = g_vheBuffer->VheMap[i].P2_OdoCoor.fObjY;
        LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjX = g_vheBuffer->VheMap[i].P1_CarCoor.fObjX;
        LstrObjMap->ObjMapCoor[LeMapId].P1_CarCoor.fObjY = g_vheBuffer->VheMap[i].P1_CarCoor.fObjY;
        LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjX = g_vheBuffer->VheMap[i].P2_CarCoor.fObjX;
        LstrObjMap->ObjMapCoor[LeMapId].P2_CarCoor.fObjY = g_vheBuffer->VheMap[i].P2_CarCoor.fObjY;
        if (g_vheBuffer->VheMap[i].MapSharp == 0)
        {
            LstrObjMap->ObjMapCoor[LeMapId].eMapObjType = OBJ_TYPE_POINT;
        }
        else
        {
            LstrObjMap->ObjMapCoor[LeMapId].eMapObjType = OBJ_TYPE_STRAIGHT2_CORNER;
        }
        LstrObjMap->ObjMapCoor[LeMapId].eObjExistProb = OBJ_PROB_LV6;

        if (g_vheBuffer->VheMap[i].enuMapType == OBJ_HIGH_CURB_TYPE ||
            g_vheBuffer->VheMap[i].enuMapType == OBJ_PVC_PIPE_TYPE ||
            g_vheBuffer->VheMap[i].enuMapType == OBJ_BIGWALL_TYPE)
        {
            LstrObjMap->ObjMapCoor[LeMapId].eMapObjHeight = OBJ_HIGH;
        }
        else
        {
            LstrObjMap->ObjMapCoor[LeMapId].eMapObjHeight = OBJ_LOW;
        }
        
        LstrObjMap->ObjMapCoor[LeMapId].eObjHeightProb = OBJ_PROB_LV6;

        LstrObjMap->ObjMapCoor[LeMapId].u8MapDisplayFlag = 1;
        LeMapId++;
    }
#else 
    UpdateMapBufferInfo();

    /* 添加90cm内，主发生成map的功能： 2024-05-11 */ 
    Fusion_DE_Map();

    float min_dist;
    int matched_id = -1;
    for (int i = 0; i < vhe_map_num; i++)
    {
        min_dist = 65535;
        for (int j = MAP_OBJ_0; j < MAP_OBJ_NUM; j++)
        {
            if (LstrObjMap->ObjMapCoor[j].u8MapExistFlag && LstrObjMap->ObjMapCoor[j].u8Map_SFR_AreaFlag == 2)
            {
                float dist = CalTwoMapDist(g_vheBuffer->VheMap[i].P1_OdoCoor, g_vheBuffer->VheMap[i].P2_OdoCoor,
                                           LstrObjMap->ObjMapCoor[j].P1_OdoCoor, LstrObjMap->ObjMapCoor[j].P2_OdoCoor);
                if (dist < min_dist)
                {
                    min_dist = dist;
                    matched_id = j;
                }
            }
        }

        if (min_dist < 250) // 在全局 map 中匹配到对应的 map，并更新对应的 map 信息。目前更新方案采用直接替换的方式，后续需要融合处理
        {
            //侧面map已存在的情况下，前后map不生成 2024-04-16 -> map在车身侧面的时候，不绘制，只绘制前后的map 2024-04-23
            if (LstrObjMap->ObjMapCoor[matched_id].u8Map_SFR_AreaFlag == 2 && g_vheBuffer->VheMap[i].u8BeFusionToRealMapFlag == 1)
            {
                FusionFrontRearMap(matched_id, &g_vheBuffer->VheMap[i],0);
            }
        }
        else /* 没有匹配到对应的 map 信息。需要在全局 map 中查找对应的空 map 位置，并将其更新到里面 */ 
        {
            if (GdSystemMsTimer - g_vheBuffer->VheMap[i].Timestamp > 200)
            {
                continue;
            }

            /* 匹配失败，生成新的 map */ 
            if (g_vheBuffer->VheMap[i].u8BeFusionToRealMapFlag == 1)
            {
                CreateNewGlobalMap(&g_vheBuffer->VheMap[i], 3);
            }
        }
    }

#endif
}