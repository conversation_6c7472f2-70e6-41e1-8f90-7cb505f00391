/*===========================================================================*/
/*                                                                           */
/* Project     = F1L StarterKit Sample Software                              */
/* Module      = interrupt_table.c                                           */
/* Version     = $Revision: 7257 $                                           */
/*                                                                           */
/*                                                                           */
/*===========================================================================*/
/*                                  COPYRIGHT                                */
/*===========================================================================*/
/* Copyright (c) 2014 by Renesas Electronics Europe GmbH,                    */
/*               a company of the Renesas Electronics Corporation            */
/*===========================================================================*/
/*                                                                           */
/* Warranty Disclaimer                                                       */
/*                                                                           */
/* Because the Product(s) is licensed free of charge, there is no warranty   */
/* of any kind whatsoever and expressly disclaimed and excluded by Renesas,  */
/* either expressed or implied, including but not limited to those for       */
/* non-infringement of intellectual property, merchantability and/or         */
/* fitness for the particular purpose.                                       */
/* Renesas shall not have any obligation to maintain, service or provide bug */
/* fixes for the supplied Product(s) and/or the Application.                 */
/*                                                                           */
/* Each User is solely responsible for determining the appropriateness of    */
/* using the Product(s) and assumes all risks associated with its exercise   */
/* of rights under this Agreement, including, but not limited to the risks   */
/* and costs of program errors, compliance with applicable laws, damage to   */
/* or loss of data, programs or equipment, and unavailability or             */
/* interruption of operations.                                               */
/*                                                                           */
/* Limitation of Liability                                                   */
/*                                                                           */
/* In no event shall Renesas be liable to the User for any incidental,       */
/* consequential, indirect, or punitive damage (including but not limited    */
/* to lost profits) regardless of whether such liability is based on breach  */
/* of contract, tort, strict liability, breach of warranties, failure of     */
/* essential purpose or otherwise and even if advised of the possibility of  */
/* such damages. Renesas shall not be liable for any services or products    */
/* provided by third party vendors, developers or consultants identified or  */
/* referred to the User by Renesas in connection with the Product(s) and/or  */
/* the Application.                                                          */
/*                                                                           */
/*                                                                           */
/*========================================================================== */
/*                                                                           */
/* Source code for the Interrupt table                                       */
/*                                                                           */
/*========================================================================== */



/*===========================================================================*/
/* includes                                                                  */
/*===========================================================================*/
#include <intrinsics.h>
#include <assert.h>
/* application includes                                                      */
#include "types.h"
#include "Interrupt.h"


/*===========================================================================*/
/* macros                                                                    */
/*===========================================================================*/
/*===========================================================================*/
/* Prototypes                                                                */
/*===========================================================================*/

/*===========================================================================*/
/* global variables                                                          */
/*===========================================================================*/
extern uint16 table;

/*===========================================================================*/
/* Using table method (ICx.TB=1)                                             */
/*===========================================================================*/


typedef void (__interrupt* isr_T)(void) ;

__interrupt void unused_isr(void)
{
    while(1)
    {
        NOP();
    }
}

static __root const isr_T irq_vector_table[TABLE_ENTRIES] @ IRQ_TABLE_START =    /**<pre> 中断向量表 </pre>*/
{
    &unused_isr,      /* EIINT_CH_0 */
    &unused_isr,      /* EIINT_CH_1 */
    &unused_isr,      /* EIINT_CH_2 */
    &unused_isr,      /* EIINT_CH_3 */
    &unused_isr,      /* EIINT_CH_4 */
    &unused_isr,      /* EIINT_CH_5 */
    &unused_isr,      /* EIINT_CH_6 */
    &unused_isr,      /* EIINT_CH_7 */
    &unused_isr,      /* EIINT_CH_8 */
    &unused_isr,      /* EIINT_CH_9 */
    &unused_isr,      /* EIINT_CH_10 */
    &unused_isr,      /* EIINT_CH_11 */
    &unused_isr,      /* EIINT_CH_12 */
    &unused_isr,      /* EIINT_CH_13 */
    &unused_isr,      /* EIINT_CH_14 */
    &unused_isr,      /* EIINT_CH_15 */
    &unused_isr,      /* EIINT_CH_16 */
    &unused_isr,      /* EIINT_CH_17 */
    &INTADC0SG1,      /* EIINT_CH_18 */
    &unused_isr,      /* EIINT_CH_19 */
    &unused_isr,      /* EIINT_CH_20 */
    &unused_isr,      /* EIINT_CH_21 */
    &unused_isr,      /* EIINT_CH_22 */
    &INTRCANGRECC0,   /* EIINT_CH_23 CAN Rx FIFO中断 */
    &unused_isr,     /* EIINT_CH_24 CAN 错误中断 */
    &unused_isr,      /* EIINT_CH_25 */
    & unused_isr,      /* EIINT_CH_26 */
    &unused_isr,      /* EIINT_CH_27 */
    &unused_isr,      /* EIINT_CH_28 */
    &unused_isr,      /* EIINT_CH_29 */
    &unused_isr,      /* EIINT_CH_30 */
    &INTCSIH0IRE,     /* EIINT_CH_31 */
    &unused_isr,      /* EIINT_CH_32 */
    &unused_isr,      /* EIINT_CH_33 */
    &unused_isr,    /* EIINT_CH_34 UART发送中断*/
    &unused_isr,    /* EIINT_CH_35 UART接收中断*/
    &unused_isr,      /* EIINT_CH_36 */
    &INTP0,           /* EIINT_CH_37 CAN 唤醒中断*/
    &unused_isr,      /* EIINT_CH_38 */
    &INTINTP2,        /* EIINT_CH_39 */
    &unused_isr,      /* EIINT_CH_40 */
    &unused_isr,      /* EIINT_CH_41 */
    &unused_isr,      /* EIINT_CH_42 */
    &INTP3,           /* EIINT_CH_43 BAT掉电中断*/
    &unused_isr,      /* EIINT_CH_44 */
    &unused_isr,      /* EIINT_CH_45 */
    &unused_isr,      /* EIINT_CH_46 */
    &INTINTP11,       /* EIINT_CH_47 */
    &unused_isr,      /* EIINT_CH_48 */
    &unused_isr,      /* EIINT_CH_49 */
    &unused_isr,      /* EIINT_CH_50 */
    &unused_isr,      /* EIINT_CH_51 */
    &unused_isr,      /* EIINT_CH_52 */
    &unused_isr,      /* EIINT_CH_53 */
    &unused_isr,      /* EIINT_CH_54 */
    &unused_isr,      /* EIINT_CH_55 */
    &unused_isr,      /* EIINT_CH_56 */
    &unused_isr,      /* EIINT_CH_57 */
    &unused_isr,      /* EIINT_CH_58 */
    &unused_isr,      /* EIINT_CH_59 */
    &unused_isr,      /* EIINT_CH_60 */
    &INTDMA1,      /* EIINT_CH_61 */
    &unused_isr,      /* EIINT_CH_62 */
    &INTDMA3,      /* EIINT_CH_63 */
    &unused_isr,      /* EIINT_CH_64 */
    &INTDMA5,      /* EIINT_CH_65 */
    &unused_isr,      /* EIINT_CH_66 */
    &unused_isr,      /* EIINT_CH_67 */
    &unused_isr,      /* EIINT_CH_68 */
    &unused_isr,      /* EIINT_CH_69 */
    &unused_isr,      /* EIINT_CH_70 */
    &unused_isr,      /* EIINT_CH_71 */
    &unused_isr,      /* EIINT_CH_72 */
    &unused_isr,      /* EIINT_CH_73 */
    &unused_isr,      /* EIINT_CH_74 */
    &unused_isr,      /* EIINT_CH_75 */
    &unused_isr,      /* EIINT_CH_76 */
    &unused_isr,      /* EIINT_CH_77 */
    &unused_isr,      /* EIINT_CH_78 */
    &unused_isr,      /* EIINT_CH_79 */
    &unused_isr,      /* EIINT_CH_80 */
    &unused_isr,      /* EIINT_CH_81 */
    &unused_isr,      /* EIINT_CH_82 */
    &unused_isr,      /* EIINT_CH_83 */
    &INTOSTM0,        /* EIINT_CH_84 OS Timer Int */
    &unused_isr,      /* EIINT_CH_85 */
    &unused_isr,      /* EIINT_CH_86 */
    &unused_isr,      /* EIINT_CH_87 */
    &unused_isr,      /* EIINT_CH_88 */
    &unused_isr,      /* EIINT_CH_89 */
    &unused_isr,      /* EIINT_CH_90 */
    &unused_isr,      /* EIINT_CH_91 */
    &unused_isr,      /* EIINT_CH_92 */
    &unused_isr,      /* EIINT_CH_93 */
    &unused_isr,      /* EIINT_CH_94 */
    &unused_isr,      /* EIINT_CH_95 */
    &unused_isr,      /* EIINT_CH_96 */
    &unused_isr,      /* EIINT_CH_97 */
    &unused_isr,      /* EIINT_CH_98 */
    &unused_isr,      /* EIINT_CH_99 */
    &unused_isr,      /* EIINT_CH_100 */
    &unused_isr,      /* EIINT_CH_101 */
    &unused_isr,      /* EIINT_CH_102 */
    &unused_isr,      /* EIINT_CH_103 */
    &unused_isr,      /* EIINT_CH_104 */
    &unused_isr,      /* EIINT_CH_105 */
    &unused_isr,      /* EIINT_CH_106 */
    &unused_isr,      /* EIINT_CH_107 */
    &unused_isr,      /* EIINT_CH_108 */
    &unused_isr,      /* EIINT_CH_109 */
    &unused_isr,      /* EIINT_CH_110 */
    &unused_isr,      /* EIINT_CH_111 */
    &unused_isr,      /* EIINT_CH_112 */
    &unused_isr,      /* EIINT_CH_113 */
    &unused_isr,      /* EIINT_CH_114 */
    &unused_isr,      /* EIINT_CH_115 */
    &unused_isr,      /* EIINT_CH_116 */
    &unused_isr,      /* EIINT_CH_117 */
    &unused_isr,      /* EIINT_CH_118 */
    &unused_isr,      /* EIINT_CH_119 */
    &unused_isr,      /* EIINT_CH_120 */
    &unused_isr,      /* EIINT_CH_121 */
    &unused_isr,      /* EIINT_CH_122 */
    &unused_isr,      /* EIINT_CH_123 */
    &unused_isr,      /* EIINT_CH_124 */
    &unused_isr,      /* EIINT_CH_125 */
    &unused_isr,      /* EIINT_CH_126 */
    &unused_isr,      /* EIINT_CH_127 */
    &unused_isr,      /* EIINT_CH_128 */
    &INTINTP7,        /* EIINT_CH_129 */
    &INTINTP8,        /* EIINT_CH_130 */
    &unused_isr,      /* EIINT_CH_131 */
    &unused_isr,      /* EIINT_CH_132 */
    &unused_isr,      /* EIINT_CH_133 */
    &INTCSIH2IRE,      /* EIINT_CH_134 */
    &unused_isr,      /* EIINT_CH_135 */
    &unused_isr,      /* EIINT_CH_136 */
    &unused_isr,      /* EIINT_CH_137 */
    &unused_isr,      /* EIINT_CH_138 */
    &unused_isr,      /* EIINT_CH_139 */
    &unused_isr,      /* EIINT_CH_140 */
    &unused_isr,      /* EIINT_CH_141 */
    &unused_isr,      /* EIINT_CH_142 */
    &unused_isr,      /* EIINT_CH_143 */
    &unused_isr,      /* EIINT_CH_144 */
    &unused_isr,      /* EIINT_CH_145 */
    &unused_isr,      /* EIINT_CH_146 */
    &unused_isr,      /* EIINT_CH_147 */
    &unused_isr,      /* EIINT_CH_148 */
    &unused_isr,      /* EIINT_CH_149 */
    &unused_isr,      /* EIINT_CH_150 */
    &unused_isr,      /* EIINT_CH_151 */
    &unused_isr,      /* EIINT_CH_152 */
    &unused_isr,      /* EIINT_CH_153 */
    &unused_isr,      /* EIINT_CH_154 */
    &unused_isr,      /* EIINT_CH_155 */
    &unused_isr,      /* EIINT_CH_156 */
    &unused_isr,      /* EIINT_CH_157 */
    &unused_isr,      /* EIINT_CH_158 */
    &unused_isr,      /* EIINT_CH_159 */
    &INTCSIH3IRE,      /* EIINT_CH_160 */
    &unused_isr,      /* EIINT_CH_161 */
    &unused_isr,      /* EIINT_CH_162 */
    &unused_isr,      /* EIINT_CH_163 */
    &unused_isr,      /* EIINT_CH_164 */
    &INTRLIN32UR0,    /* EIINT_CH_165 UART发送中断*/
    &INTRLIN32UR1,    /* EIINT_CH_166 UART接收中断*/
    &unused_isr,      /* EIINT_CH_167 */
    &unused_isr,      /* EIINT_CH_168 */
    &unused_isr,      /* EIINT_CH_169 */
    &unused_isr,      /* EIINT_CH_170 */
    &unused_isr,      /* EIINT_CH_171 */
    &unused_isr,      /* EIINT_CH_172 */
    &unused_isr,      /* EIINT_CH_173 */
    &unused_isr,      /* EIINT_CH_174 */
    &unused_isr,      /* EIINT_CH_175 */
    &unused_isr,      /* EIINT_CH_176 */
    &unused_isr,      /* EIINT_CH_177 */
    &unused_isr,      /* EIINT_CH_178 */
    &unused_isr,      /* EIINT_CH_179 */
    &unused_isr,      /* EIINT_CH_180 */
    &unused_isr,      /* EIINT_CH_181 */
    &unused_isr,      /* EIINT_CH_182 */
    &unused_isr,      /* EIINT_CH_183 */
    &unused_isr,      /* EIINT_CH_184 */
    &unused_isr,      /* EIINT_CH_185 */
    &unused_isr,      /* EIINT_CH_186 */
    &unused_isr,      /* EIINT_CH_187 */
    &unused_isr,      /* EIINT_CH_188 */
    &unused_isr,      /* EIINT_CH_189 */
    &unused_isr,      /* EIINT_CH_190 */
    &unused_isr,      /* EIINT_CH_191 */
    &unused_isr,      /* EIINT_CH_192 */
    &unused_isr,      /* EIINT_CH_193 */
    &unused_isr,      /* EIINT_CH_194 */
    &unused_isr,      /* EIINT_CH_195 */
    &unused_isr,      /* EIINT_CH_196 */
    &unused_isr,      /* EIINT_CH_197 */
    &unused_isr,      /* EIINT_CH_198 */
    &unused_isr,      /* EIINT_CH_199 */
    &unused_isr,      /* EIINT_CH_200 */
    &unused_isr,      /* EIINT_CH_201 */
    &unused_isr,      /* EIINT_CH_202 */
    &unused_isr,      /* EIINT_CH_203 */
    &unused_isr,      /* EIINT_CH_204 */
    &unused_isr,      /* EIINT_CH_205 */
    &unused_isr,      /* EIINT_CH_206 */
    &unused_isr,      /* EIINT_CH_207 */
    &unused_isr,      /* EIINT_CH_208 */
    &unused_isr,      /* EIINT_CH_209 */
    &unused_isr,      /* EIINT_CH_210 */
    &unused_isr,      /* EIINT_CH_211 */
    &unused_isr,      /* EIINT_CH_212 */
    &unused_isr,      /* EIINT_CH_213 */
    &unused_isr,      /* EIINT_CH_214 */
    &unused_isr,      /* EIINT_CH_215 */
    &unused_isr,      /* EIINT_CH_216 */
    &unused_isr,      /* EIINT_CH_217 */
    &unused_isr,      /* EIINT_CH_218 */
    &unused_isr,      /* EIINT_CH_219 */
    &unused_isr,      /* EIINT_CH_220 CAN3错误中断 */
    &unused_isr,      /* EIINT_CH_221 CAN3接收中断 */
    &unused_isr,      /* EIINT_CH_222 CAN3发送中断 */
    &unused_isr,      /* EIINT_CH_223 */
    &unused_isr,      /* EIINT_CH_224 */
    &unused_isr,      /* EIINT_CH_225 */
    &unused_isr,      /* EIINT_CH_226 */
    &unused_isr,      /* EIINT_CH_227 */
    &unused_isr,      /* EIINT_CH_228 */
    &unused_isr,      /* EIINT_CH_229 */
    &unused_isr,      /* EIINT_CH_230 */
    &unused_isr,      /* EIINT_CH_231 */
    &unused_isr,      /* EIINT_CH_232 */
    &unused_isr,      /* EIINT_CH_233 */
    &unused_isr,      /* EIINT_CH_234 */
    &unused_isr,      /* EIINT_CH_235 */
    &unused_isr,      /* EIINT_CH_236 */
    &unused_isr,      /* EIINT_CH_237 */
    &unused_isr,      /* EIINT_CH_238 */
    &unused_isr,      /* EIINT_CH_239 */
    &unused_isr,      /* EIINT_CH_240 */
    &unused_isr,      /* EIINT_CH_241 */
    &unused_isr,      /* EIINT_CH_242 */
    &unused_isr,      /* EIINT_CH_243 */
    &unused_isr,      /* EIINT_CH_244 */
    &unused_isr,      /* EIINT_CH_245 */
    &unused_isr,      /* EIINT_CH_246 */
    &unused_isr,      /* EIINT_CH_247 */
    &unused_isr,      /* EIINT_CH_248 */
    &unused_isr,      /* EIINT_CH_249 */
    &unused_isr,      /* EIINT_CH_250 */
    &unused_isr,      /* EIINT_CH_251 */
    &unused_isr,      /* EIINT_CH_252 */
    &unused_isr,      /* EIINT_CH_253 */
    &unused_isr,      /* EIINT_CH_254 */
    &unused_isr,      /* EIINT_CH_255 */
    &unused_isr,      /* EIINT_CH_256 */
    &unused_isr,      /* EIINT_CH_257 */
    &unused_isr,      /* EIINT_CH_258 */
    &unused_isr,      /* EIINT_CH_259 */
    &unused_isr,      /* EIINT_CH_260 */
    &unused_isr,      /* EIINT_CH_261 */
    &unused_isr,      /* EIINT_CH_262 */
    &unused_isr,      /* EIINT_CH_263 */
    &unused_isr,      /* EIINT_CH_264 */
    &unused_isr,      /* EIINT_CH_265 */
    &unused_isr,      /* EIINT_CH_266 */
    &unused_isr,      /* EIINT_CH_267 */
    &unused_isr,      /* EIINT_CH_268 */
    &unused_isr,      /* EIINT_CH_269 */
    &unused_isr,      /* EIINT_CH_270 */
    &unused_isr,      /* EIINT_CH_271 */
    &INTRCAN4ERR,     /* EIINT_CH_272 */
    &unused_isr,      /* EIINT_CH_273 */
    &INTRCAN4TRX,     /* EIINT_CH_274 */
    &unused_isr,      /* EIINT_CH_275 */
    &unused_isr,      /* EIINT_CH_276 */
    &unused_isr,      /* EIINT_CH_277 */
    &unused_isr,      /* EIINT_CH_278 */
    &unused_isr,      /* EIINT_CH_279 */
    &unused_isr,      /* EIINT_CH_280 */
    &unused_isr       /* EIINT_CH_281 */
};