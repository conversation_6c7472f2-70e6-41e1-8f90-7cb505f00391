import sys

def reverse_bits(value, width=8):
    """反转位顺序"""
    result = 0
    for i in range(width):
        if (value >> i) & 1:
            result |= 1 << (width - 1 - i)
    return result

def calculate_crc8_variants(data, polynomial, init_value, reflect_in=False, reflect_out=False, xor_out=0):
    """计算CRC8，支持不同的变体"""
    crc = init_value
    
    # 计算CRC
    for byte in data:
        byte_processed = byte if not reflect_in else reverse_bits(byte)
        crc ^= byte_processed
        
        for _ in range(8):
            if crc & 0x80:
                crc = (crc << 1) ^ polynomial
            else:
                crc = crc << 1
            crc &= 0xFF  # 确保结果是8位
    
    # 最终处理
    if reflect_out:
        crc = reverse_bits(crc)
    
    crc ^= xor_out
    return crc & 0xFF

# 已知多项式
polynomial = 0x2F  # x8+x5+x3+x2+x+1

# 解析示例数据
def parse_hex_string(hex_string):
    # 移除所有空白字符
    hex_string = hex_string.strip()
    
    # 将字符串转换为字节列表
    return [int(hex_string[i:i+2], 16) for i in range(0, len(hex_string), 2)]

# 示例数据
examples = [
    'FF00EFFE',
    'FFC30D00',
    '02120000'
]

# 解析示例数据
parsed_examples = []
for example in examples:
    data_with_crc = parse_hex_string(example)
    data = data_with_crc[:-1]
    expected_crc = data_with_crc[-1]
    parsed_examples.append((data, expected_crc, example))

print('尝试推导CRC8算法变体...')
print(f'多项式: 0x{polynomial:02X} (x8+x5+x3+x2+x+1)')
print()

# 尝试不同的变体和初始值
for reflect_in in [False, True]:
    for reflect_out in [False, True]:
        for xor_out in [0x00, 0xFF]:
            for init_value in range(256):
                all_match = True
                
                for data, expected_crc, _ in parsed_examples:
                    calculated_crc = calculate_crc8_variants(data, polynomial, init_value, reflect_in, reflect_out, xor_out)
                    if calculated_crc != expected_crc:
                        all_match = False
                        break
                
                if all_match:
                    print('找到匹配的CRC8算法配置:')
                    print(f'多项式: 0x{polynomial:02X}')
                    print(f'初始值: 0x{init_value:02X}')
                    print(f'反转输入: {reflect_in}')
                    print(f'反转输出: {reflect_out}')
                    print(f'异或输出: 0x{xor_out:02X}')
                    print()
                    
                    # 验证
                    print('验证所有示例:')
                    for data, expected_crc, example in parsed_examples:
                        calculated_crc = calculate_crc8_variants(data, polynomial, init_value, reflect_in, reflect_out, xor_out)
                        print(f'示例: {example}')
                        print(f'计算CRC8: 0x{calculated_crc:02X}')
                        print(f'预期CRC8: 0x{expected_crc:02X}')
                        print(f'验证结果: {"成功" if calculated_crc == expected_crc else "失败"}')
                        print()
                    
                    # 退出所有循环
                    sys.exit(0)

print('未找到匹配的CRC8算法配置')
