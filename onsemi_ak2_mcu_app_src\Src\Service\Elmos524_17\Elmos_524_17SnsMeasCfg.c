/*
 * Elmos_524_17SnsMeasCfg.c
 *
 *  Created on: 20210325
 *      Author: 6000021992
 */
#include "DSI3_COM.h"
#include <Elmos_524_17SnsMeasCfg.h>
#include "Elmos17SnsMeasParamCfg.h"

/** @brief 探测列表 */

const MEAS_CRM_Seq_str MeasSeq[CMDSeq_Num] =
{

        /*StdMeasSeq0*/
        [StdMeasSeq0].MeasType = STANDARD_MEAS,

        [StdMeasSeq0].SNsMeasType = {MEAS_STD_LISTEN,MEAS_STD_LISTEN,MEAS_STD_EMIT,MEAS_STD_LISTEN,MEAS_STD_LISTEN,MEAS_STD_LISTEN},

        /*StdMeasSeq1*/
        [StdMeasSeq1].MeasType = STANDARD_MEAS,

        [StdMeasSeq1].SNsMeasType = {MEAS_STD_LISTEN,MEAS_STD_LISTEN,MEAS_STD_LISTEN,MEAS_STD_EMIT,MEAS_STD_LISTEN,MEAS_STD_LISTEN},

        /*StdMeasSeq2*/
        [StdMeasSeq2].MeasType = STANDARD_MEAS,

        [StdMeasSeq2].SNsMeasType = {MEAS_STD_LISTEN,MEAS_STD_LISTEN,MEAS_STD_EMIT,MEAS_STD_LISTEN,MEAS_STD_LISTEN,MEAS_STD_EMIT},

        /*StdMeasSeq3*/
        [StdMeasSeq3].MeasType = STANDARD_MEAS,

        [StdMeasSeq3].SNsMeasType = {MEAS_STD_EMIT,MEAS_STD_LISTEN,MEAS_STD_LISTEN,MEAS_STD_EMIT,MEAS_STD_LISTEN,MEAS_STD_LISTEN},
        
        /*StdMeasSeq4*/
        [StdMeasSeq4].MeasType = STANDARD_MEAS,

        [StdMeasSeq4].SNsMeasType = {MEAS_STD_EMIT,MEAS_STD_LISTEN,MEAS_IDLE,MEAS_IDLE,MEAS_IDLE,MEAS_IDLE},

        

        /*AdvMeasSeq0*/
        [AdvMeasSeq0].MeasType = ADVANCED_MEAS,

        [AdvMeasSeq0].SNsMeasType = {MEAS_ADV_EMIT_ChirpDown,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpUp,MEAS_ADV_LISTEN},

        /*AdvMeasSeq1*/
        [AdvMeasSeq1].MeasType = ADVANCED_MEAS,
        
        [AdvMeasSeq1].SNsMeasType = {MEAS_ADV_EMIT_ChirpUp,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpDown,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN},


        /*AdvMeasSeq2*/
        [AdvMeasSeq2].MeasType = ADVANCED_MEAS,
        
        [AdvMeasSeq2].SNsMeasType = {MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpUp,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpDown},



        /*AdvMeasSeq3*/
        [AdvMeasSeq3].MeasType = ADVANCED_MEAS,
        
        [AdvMeasSeq3].SNsMeasType = {MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpDown,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpUp},

        /*AdvMeasSeq4*/
        [AdvMeasSeq4].MeasType = ADVANCED_MEAS,
        
        [AdvMeasSeq4].SNsMeasType = {MEAS_ADV_EMIT_ChirpUp,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpDown,MEAS_ADV_LISTEN},

        /*AdvMeasSeq5*/
        [AdvMeasSeq5].MeasType = ADVANCED_MEAS,
        
        [AdvMeasSeq5].SNsMeasType = {MEAS_ADV_EMIT_ChirpDown,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpUp,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN},

        /*AdvMeasSeq6*/
        [AdvMeasSeq6].MeasType = ADVANCED_MEAS,

        [AdvMeasSeq6].SNsMeasType = {MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpDown,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpUp},


        /*AdvMeasSeq7*/
        [AdvMeasSeq7].MeasType = ADVANCED_MEAS,

        [AdvMeasSeq7].SNsMeasType = {MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpDown,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN},

        /*AdvMeasSeq8*/
        [AdvMeasSeq8].MeasType = ADVANCED_MEAS,

        [AdvMeasSeq8].SNsMeasType = {MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpDown,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN},


        /*AdvMeasSeq9*/
        [AdvMeasSeq9].MeasType = ADVANCED_MEAS,

        [AdvMeasSeq9].SNsMeasType = {MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpUp,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN},


        /*AdvMeasSeq10*/
        [AdvMeasSeq10].MeasType = ADVANCED_MEAS,

        [AdvMeasSeq10].SNsMeasType = {MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpUp,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN},


        /*AdvMeasSeq11*/
        [AdvMeasSeq11].MeasType = ADVANCED_MEAS,

        [AdvMeasSeq11].SNsMeasType = {MEAS_ADV_EMIT_ChirpDown,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpUp,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpDown,MEAS_ADV_LISTEN},

        /*AdvMeasSeq12*/
        [AdvMeasSeq12].MeasType = ADVANCED_MEAS,

        [AdvMeasSeq12].SNsMeasType = {MEAS_ADV_EMIT_ChirpDown,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpUp},

        /*AdvMeasSeq13*/
        [AdvMeasSeq13].MeasType = ADVANCED_MEAS,

        [AdvMeasSeq13].SNsMeasType = {MEAS_ADV_EMIT_ChirpUp,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpDown},

		/*AdvMeasSeq14*/
        [AdvMeasSeq14].MeasType = ADVANCED_MEAS,

        [AdvMeasSeq14].SNsMeasType = {MEAS_ADV_EMIT_ChirpUp,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpDown,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN},

		/*AdvMeasSeq15*/
        [AdvMeasSeq15].MeasType = ADVANCED_MEAS,

        [AdvMeasSeq15].SNsMeasType = {MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpDown,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpUp},

		/*AdvMeasSeq16*/
        [AdvMeasSeq16].MeasType = ADVANCED_MEAS,

        [AdvMeasSeq16].SNsMeasType = {MEAS_ADV_EMIT_ChirpDown,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpUp,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN},

		/*AdvMeasSeq17*/
        [AdvMeasSeq17].MeasType = ADVANCED_MEAS,

        [AdvMeasSeq17].SNsMeasType = {MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpUp,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpDown},

		/*AdvMeasSeq3-0*/
        [AdvMeasSeq3_0].MeasType = ADVANCED_MEAS,

        [AdvMeasSeq3_0].SNsMeasType = {MEAS_ADV_EMIT_ChirpUp,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpDown,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpUp},

		/*AdvMeasSeq3-1*/
        [AdvMeasSeq3_1].MeasType = ADVANCED_MEAS,

        [AdvMeasSeq3_1].SNsMeasType = {MEAS_ADV_EMIT_ChirpUp,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpDown,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpUp},

		/*AdvMeasSeq3-2*/
        [AdvMeasSeq3_2].MeasType = ADVANCED_MEAS,

        [AdvMeasSeq3_2].SNsMeasType = {MEAS_ADV_EMIT_ChirpDown,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpUp,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpDown},

		/*AdvMeasSeq3-3*/
        [AdvMeasSeq3_3].MeasType = ADVANCED_MEAS,

        [AdvMeasSeq3_3].SNsMeasType = {MEAS_ADV_EMIT_ChirpDown,MEAS_ADV_LISTEN,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpUp,MEAS_ADV_LISTEN,MEAS_ADV_EMIT_ChirpDown},

		/*StdMeasSeq2_0*/
        [StdMeasSeq2_0].MeasType = STANDARD_MEAS,

        [StdMeasSeq2_0].SNsMeasType = {MEAS_STD_LISTEN,MEAS_STD_LISTEN,MEAS_STD_EMIT,MEAS_STD_LISTEN,MEAS_STD_LISTEN,MEAS_STD_EMIT},

        /*StdMeasSeq2_1*/
        [StdMeasSeq2_1].MeasType = STANDARD_MEAS,

        [StdMeasSeq2_1].SNsMeasType = {MEAS_STD_LISTEN,MEAS_STD_EMIT,MEAS_STD_LISTEN,MEAS_STD_LISTEN,MEAS_STD_LISTEN,MEAS_STD_EMIT},
        
        /*StdMeasSeq2_2*/
        [StdMeasSeq2_2].MeasType = STANDARD_MEAS,

        [StdMeasSeq2_2].SNsMeasType = {MEAS_STD_EMIT,MEAS_STD_LISTEN,MEAS_STD_LISTEN,MEAS_STD_LISTEN,MEAS_STD_EMIT,MEAS_STD_LISTEN},
        
        /*StdMeasSeq2_3*/
        [StdMeasSeq2_3].MeasType = STANDARD_MEAS,
        
        [StdMeasSeq2_3].SNsMeasType = {MEAS_STD_EMIT,MEAS_STD_LISTEN,MEAS_STD_LISTEN,MEAS_STD_EMIT,MEAS_STD_LISTEN,MEAS_STD_LISTEN},

        /*StdMeasSeq2_4*/
        [StdMeasSeq2_4].MeasType = STANDARD_MEAS,
        
        [StdMeasSeq2_4].SNsMeasType = {MEAS_STD_EMIT,MEAS_STD_LISTEN,MEAS_STD_LISTEN,MEAS_STD_LISTEN,MEAS_STD_LISTEN,MEAS_STD_EMIT},


        /*StdALLListenMeasSeq*/
        [CMDSeq_ALLLISTEN].MeasType = STANDARD_MEAS,

        [CMDSeq_ALLLISTEN].SNsMeasType = {MEAS_STD_LISTEN,MEAS_STD_LISTEN,MEAS_STD_LISTEN,MEAS_STD_LISTEN,MEAS_STD_LISTEN,MEAS_STD_LISTEN},

        /*StdALLEmitMeasSeq*/
        [CMDSeq_ALLEmit].MeasType = STANDARD_MEAS,

        [CMDSeq_ALLEmit].SNsMeasType = {MEAS_STD_EMIT,MEAS_STD_EMIT,MEAS_STD_EMIT,MEAS_STD_EMIT,MEAS_STD_EMIT,MEAS_STD_EMIT},

};

/** @brief 探测模式及profile对应测量时间 */

uint16 MeasTime[MeasType_num][SnsMeasProfile_num] =
{
        [STANDARD_MEAS] = {30208,STD_PROFILEA_MEASURE_TIME*MEASURE_TIME_STEP,STD_PROFILEB_MEASURE_TIME*MEASURE_TIME_STEP,STD_PROFILEC_MEASURE_TIME*MEASURE_TIME_STEP},
        [ADVANCED_MEAS] = {37888,ADV_PROFILEA_MEASURE_TIME*MEASURE_TIME_STEP,ADV_PROFILEB_MEASURE_TIME*MEASURE_TIME_STEP,37888},
        [IMPEDANCE_MEAS] = {3000,3000,3000,3000},
};



const CMDSeq_en RearGroupAdvStdSeqList[MeasSeqList0Num] = 
{
    [0]  = AdvMeasSeq0,
    [1]  = StdMeasSeq2_1,
    [2]  = AdvMeasSeq14,
    [3]  = StdMeasSeq2,  
    
    [4]  = StdMeasSeq2_2,
    [5]  = AdvMeasSeq2,
    [6]  = StdMeasSeq3,
    [7]  = AdvMeasSeq15, 

    [8]  = AdvMeasSeq4,
    [9]  = StdMeasSeq2_1,
    [10] = AdvMeasSeq16,
    [11] = StdMeasSeq2,  
    
    [12] = StdMeasSeq2_2,
    [13] = AdvMeasSeq6,
    [14] = StdMeasSeq3,
    [15] = AdvMeasSeq17, 
};

const CMDSeq_en FrontGroupAdvStdSeqList[MeasSeqList0Num] = 
{
    [0]  = AdvMeasSeq0,
    [1]  = StdMeasSeq2_1,
    [2]  = AdvMeasSeq14,
    [3]  = StdMeasSeq2,  
    
    [4]  = StdMeasSeq2_2,
    [5]  = AdvMeasSeq2,
    [6]  = StdMeasSeq3,
    [7]  = AdvMeasSeq15, 

    [8]  = AdvMeasSeq4,
    [9]  = StdMeasSeq2_1,
    [10] = AdvMeasSeq16,
    [11] = StdMeasSeq2,  
    
    [12] = StdMeasSeq2_2,
    [13] = AdvMeasSeq6,
    [14] = StdMeasSeq3,
    [15] = AdvMeasSeq17, 
};



const CMDSeq_en RearGroupAdvSeqList[MeasSeqList1Num] = 
{
    [0]  = AdvMeasSeq14,
    [1]  = AdvMeasSeq15,
    [2]  = AdvMeasSeq4,
    [3]  = AdvMeasSeq6, 
    
    [4]  = AdvMeasSeq16,
    [5]  = AdvMeasSeq17, 
    [6]  = AdvMeasSeq0,
    [7]  = AdvMeasSeq2, 
};

const CMDSeq_en FrontGroupAdvSeqList[MeasSeqList1Num] = 
{
    [0]  = AdvMeasSeq14,
    [1]  = AdvMeasSeq15,
    [2]  = AdvMeasSeq4,
    [3]  = AdvMeasSeq6, 
    
    [4]  = AdvMeasSeq16,
    [5]  = AdvMeasSeq17, 
    [6]  = AdvMeasSeq0,
    [7]  = AdvMeasSeq2, 
};


const CMDSeq_en RearGroupStdSeqList[MeasSeqList2Num] = 
{
    [0]  = StdMeasSeq2_2,
    [1]  = StdMeasSeq2_1,  
    [2]  = StdMeasSeq2_3,
    [3]  = StdMeasSeq2_0,

};

const CMDSeq_en FrontGroupStdSeqList[MeasSeqList2Num] = 
{
    [0]  = StdMeasSeq2_2,
    [1]  = StdMeasSeq2_1,
    [2]  = StdMeasSeq2_3,
    [3]  = StdMeasSeq2_0, 
};

const CMDSeq_en RearGroupFourSeqList[MeasSeqList3Num] = 
{
	[0]  = AdvMeasSeq3_0,
    [1]  = AdvMeasSeq3_1,
    [2]  = AdvMeasSeq3_2,
    [3]  = AdvMeasSeq3_3,
};

const CMDSeq_en FrontGroupFourSeqList[MeasSeqList3Num] = 
{
	[0]  = AdvMeasSeq3_0,
    [1]  = AdvMeasSeq3_1,
    [2]  = AdvMeasSeq3_2,
    [3]  = AdvMeasSeq3_3,
};

const CMDSeq_en RearGroupOneSeqList[MeasSeqList4Num] = 
{
    [0]  = StdMeasSeq2_4,
};

const CMDSeq_en FrontGroupOneSeqList[MeasSeqList4Num] = 
{
    [0]  = StdMeasSeq2_4, 
};

const CMDSeq_en RearGuidanceSeqList[MeasSeqList4_Guidance] =
{
	[0]  = AdvMeasSeq0,
	[1]  = StdMeasSeq2_1,
	[2]  = AdvMeasSeq7,
	[3]  = StdMeasSeq0,  
 
	[4]  = StdMeasSeq2_2,
	[5]  = AdvMeasSeq2,
	[6]  = StdMeasSeq1,
	[7]  = AdvMeasSeq8, 

	[8]  = AdvMeasSeq4,
	[9]  = StdMeasSeq2_1,
 	[10] = AdvMeasSeq9,
	[11] = StdMeasSeq0,  
 
 	[12] = StdMeasSeq2_2,
 	[13] = AdvMeasSeq6,
 	[14] = StdMeasSeq1,
 	[15] = AdvMeasSeq10, 
};

const CMDSeq_en FrontGuidanceSeqList[MeasSeqList4_Guidance] =
{
	[0]  = AdvMeasSeq0,
	[1]  = StdMeasSeq2_1,
	[2]  = AdvMeasSeq7,
	[3]  = StdMeasSeq0,  
 
	[4]  = StdMeasSeq2_2,
	[5]  = AdvMeasSeq2,
	[6]  = StdMeasSeq1,
	[7]  = AdvMeasSeq8, 

	[8]  = AdvMeasSeq4,
	[9]  = StdMeasSeq2_1,
 	[10] = AdvMeasSeq9,
	[11] = StdMeasSeq0,  
 
 	[12] = StdMeasSeq2_2,
 	[13] = AdvMeasSeq6,
 	[14] = StdMeasSeq1,
 	[15] = AdvMeasSeq10, 

};

const SnsGroupMeasSeq_str RearSnsGroupMeasSeqList[MeasSeqList_TotalNum] =
{
    [MeasSeqList0] = {.pSeqlist = &RearGroupAdvStdSeqList[0],.SeqListNum = MeasSeqList0Num,},
    [MeasSeqList1] = {.pSeqlist = &RearGroupAdvSeqList[0],   .SeqListNum = MeasSeqList1Num,},
    [MeasSeqList2] = {.pSeqlist = &RearGroupStdSeqList[0],   .SeqListNum = MeasSeqList2Num,},
    [MeasSeqList3] = {.pSeqlist = &RearGroupFourSeqList[0],  .SeqListNum = MeasSeqList3Num,},
    [MeasSeqList4] = {.pSeqlist = &RearGroupOneSeqList[0],   .SeqListNum = MeasSeqList4Num,},
    [MeasSeq_Guidance] = {.pSeqlist = &RearGuidanceSeqList[0],   .SeqListNum = MeasSeqList4_Guidance,},
};

const SnsGroupMeasSeq_str FrontSnsGroupMeasSeqList[MeasSeqList_TotalNum] =
{
    [MeasSeqList0] = {.pSeqlist = &FrontGroupAdvStdSeqList[0],.SeqListNum = MeasSeqList0Num,},
    [MeasSeqList1] = {.pSeqlist = &FrontGroupAdvSeqList[0],   .SeqListNum = MeasSeqList1Num,},
    [MeasSeqList2] = {.pSeqlist = &FrontGroupStdSeqList[0],   .SeqListNum = MeasSeqList2Num,},
    [MeasSeqList3] = {.pSeqlist = &FrontGroupFourSeqList[0],  .SeqListNum = MeasSeqList3Num,},
    [MeasSeqList4] = {.pSeqlist = &FrontGroupOneSeqList[0],   .SeqListNum = MeasSeqList4Num,},
    [MeasSeq_Guidance] = {.pSeqlist = &FrontGuidanceSeqList[0],   .SeqListNum = MeasSeqList4_Guidance,},
};

#define DSIChlSEL_R (DSIChlSel_en)(DSIChl_R + 1)

#define DSIChlSEL_F (DSIChlSel_en)(DSIChl_F + 1)


/** @brief SnsID 对应物理地址索引 */

const SnsAddrCfg_str SnsAddCfg[SNSIDNum] =
{



        [SNS_FL_S]   = {.DSIMasterID = DSIMaster0,
                      .DSIChlSEL = DSIChlSEL2,
                      .cSnsPhysAddr = 0x03,
                      .Sns_Ori = SNS_F_S,},
                      
        [SNS_FL]   = {.DSIMasterID = DSIMaster0,
                      .DSIChlSEL = DSIChlSEL2,
                      .cSnsPhysAddr = 0x01,
                      .Sns_Ori = SNS_F_C,},
                      
        [SNS_FML]  = {.DSIMasterID = DSIMaster0,
                      .DSIChlSEL = DSIChlSEL1,
                      .cSnsPhysAddr = 0x01,
                      .Sns_Ori = SNS_F_M,},
                      
        [SNS_FMR]  = {.DSIMasterID = DSIMaster1,
                      .DSIChlSEL = DSIChlSEL2,
                      .cSnsPhysAddr = 0x01,
                      .Sns_Ori = SNS_F_M,},
                      
        [SNS_FR]   = {.DSIMasterID = DSIMaster1,
                      .DSIChlSEL = DSIChlSEL1,
                      .cSnsPhysAddr = 0x01,
                      .Sns_Ori = SNS_F_C,},   
    
        [SNS_FR_S]  = {.DSIMasterID = DSIMaster1,
                      .DSIChlSEL = DSIChlSEL1,
                      .cSnsPhysAddr = 0x03,
                      .Sns_Ori = SNS_F_S,},
                      
    
        [SNS_RR_S]   = {.DSIMasterID = DSIMaster1,
                      .DSIChlSEL = DSIChlSEL2,
                      .cSnsPhysAddr = 0x03,
                      .Sns_Ori = SNS_R_S,},
                      
        [SNS_RR]   = {.DSIMasterID = DSIMaster1,
                      .DSIChlSEL = DSIChlSEL1,
                      .cSnsPhysAddr = 0x02,
                      .Sns_Ori = SNS_R_C,},
                      
        [SNS_RMR]  = {.DSIMasterID = DSIMaster1,
                      .DSIChlSEL = DSIChlSEL2,
                      .cSnsPhysAddr = 0x02,
                      .Sns_Ori = SNS_R_M,},
                      
        [SNS_RML]  = {.DSIMasterID = DSIMaster0,
                      .DSIChlSEL = DSIChlSEL1,
                      .cSnsPhysAddr = 0x02,
                      .Sns_Ori = SNS_R_M,},
                      
        [SNS_RL]   = {.DSIMasterID = DSIMaster0,
                      .DSIChlSEL = DSIChlSEL2,
                      .cSnsPhysAddr = 0x02,
                      .Sns_Ori = SNS_R_C,},
    
        [SNS_RL_S]   = {.DSIMasterID = DSIMaster0,
                      .DSIChlSEL = DSIChlSEL1,
                      .cSnsPhysAddr = 0x03,
                      .Sns_Ori = SNS_R_S,},


        [SNS_M0_ALL] = {.DSIMasterID = DSIMaster0,
                       .DSIChlSEL = DSIChlSEL1And2,
                       .cSnsPhysAddr = SNS_BROADCAST},

        [SNS_M1_ALL] = {.DSIMasterID = DSIMaster1,
                       .DSIChlSEL = DSIChlSEL1And2,
                       .cSnsPhysAddr = SNS_BROADCAST},
};
        

const SnsMeasGroup_en SnsMeasGroupList[DSIMasterNum][DSIChlNum] = 
{
    
    [DSIMaster0] = {[DSIChl_R] = RearSnsMeasGroup,[DSIChl_F] = FrontSnsMeasGroup},
};

const DSIChlID_en SnsMeasGroupDSIChlIDList[SnsMeasGroup_Num] = 
{
    [RearSnsMeasGroup]  = DSIChl_R,
    [FrontSnsMeasGroup] = DSIChl_F,
};
    
const uint8 ECHOMsgIndexList[DSIChlNum][DSISlaveSnsNum] = 
{
    [DSIChl1] = {0,1,2},
    [DSIChl2] = {3,4,5},
};

const SnsID_en MasterSnsSelect[DSIMasterNum] = 
{
    [DSIMaster0] = SNS_M0_ALL,
    [DSIMaster1] = SNS_M1_ALL,
};
	
DistanceType_en DistanceType[5][2]=
{
    [MEAS_STD_EMIT] = {STD_Mas,STD_Mas},
    [MEAS_ADV_EMIT_ChirpDown] = {ADV_Mas_A,ADV_Mas_A_RecB},
    [MEAS_ADV_EMIT_ChirpUp] = {ADV_Mas_B_RecA,ADV_Mas_B},

    [MEAS_STD_LISTEN] = {STD_Lis,STD_Lis},

    [MEAS_ADV_LISTEN] = {ADV_Lis_A,ADV_Lis_B},
};


SnsMeasGroup_en GetSnsMeasGroupID(DSIMasterID_en DSIMasterID,DSIChlID_en DSIChlID)
{
    return SnsMeasGroupList[DSIMasterID][DSIChlID];
}

uint8 GetMeasMsgIndex(DSIChlID_en DSIChlID,uint8 SlotID)
{
    return ECHOMsgIndexList[DSIChlID][SlotID];
}



SnsID_en GetSnsID(DSIMasterID_en DSIMasterID,DSIChlID_en DSIChlID,uint8 SlaveID)
{
    SnsID_en SnsID;

    for(SnsID=SNSID_Start;SnsID < SNSNum;SnsID++)
    {
        if((SnsAddCfg[SnsID].DSIMasterID == DSIMasterID)
            &&((SnsAddCfg[SnsID].DSIChlSEL - 1) == DSIChlID)
            &&((SnsAddCfg[SnsID].cSnsPhysAddr) == SlaveID))
        {
            return SnsID;
        }
    }

    return SNSID_NONE;
}


uint8 GetSns_PhyAddr(SnsID_en SnsID)
{
    return SnsAddCfg[SnsID].cSnsPhysAddr;
}

DSIMasterID_en GetSnsDSIMasterID(SnsID_en SnsID)
{
    return SnsAddCfg[SnsID].DSIMasterID;
}
DSIChlSel_en GetSnsDSIChlSEL(SnsID_en SnsID)
{
    return SnsAddCfg[SnsID].DSIChlSEL;
}

Sns_Ori_en Elmos17_GetSns_Ori(SnsID_en SnsID)
{
    return SnsAddCfg[SnsID].Sns_Ori;
}


DistanceType_en Elmos17_GetDisType(SNS_MEAS_TYPE_en  SNS_MEAS_TYPE, uint8 filterNum)
{
    return DistanceType[SNS_MEAS_TYPE][filterNum];
}






/** @brief  获取探测时间*/
uint16 GetSeqMeasTime_us(MeasType_en MeasType, SnsMeasProfile_en profile)
{

    if((MeasType >= MeasType_num) || (profile >= SnsMeasProfile_num))
    {
        return 0;
    }
    return MeasTime[MeasType][profile];
}


/** @brief 获取接收PDCM 帧个??*/

uint16 CalcSeqMeasPDCMNum(uint16 MeasTime_us,uint16 PdcmPeriod_us,SnsMeasDataMode_en DataMode)
{
    uint16 BRCCnt = 0;
    if(PdcmPeriod_us == 0)
    {
        return 0;
    }
    BRCCnt = (MeasTime_us + PdcmPeriod_us) / PdcmPeriod_us;
    if(DataMode != APP_Mode)
    {
        BRCCnt += 500;/** @brief LAB/LAB&APP模式 需要接收监控数据，PDCM个数需要根据选择的监控通道计算*/
    }
    return BRCCnt;
}

/** @brief 获取对应探测序列SlaveID对应工作模式 */

SNS_MEAS_TYPE_en GetMeasSeqSNsMeasType(CMDSeq_en cMeasSeq,uint8 SlotID)
{
    if((SlotID >= sizeof(MeasSeq[cMeasSeq].SNsMeasType)) || (cMeasSeq >= CMDSeq_Num))
    {
        return SNS_MEAS_TYPE_NONE;
    }

    return MeasSeq[cMeasSeq].SNsMeasType[SlotID];


}

MeasType_en GetSeqMeasType(CMDSeq_en cMeasSeq)
{
    if(cMeasSeq >= CMDSeq_Num)
    {
        return MeasType_None;
    }

    return MeasSeq[cMeasSeq].MeasType;


}

