/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * APA_CalibPara: 
 * Created on: 2022-11-25 17:01
 * Original designer: 22866
 ******************************************************************************/
#ifndef __VEHICLE_GEOMETRY_PARAMETER_TYPES_H__
#define __VEHICLE_GEOMETRY_PARAMETER_TYPES_H__

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "types.h"





/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************
* 设计描述 : 车身参数
* 设计索引 : 
*******************************************************************************/
typedef struct
{
    uint16 u16CarWidth;
    uint16 u16CarHalfWidth;           /* 车辆半宽  */
    uint16 u16CarLength;
    uint16 u16FrontWheelBackWheelDis; /* 前轴到后轴的距离  */
    uint16 u16BackWheelCarTailDis;    /* 后轴到车尾的距离  */
    uint16 u16FrontWheelCarHeadDis;   /* 前轴轴到车头的距离  */
    uint16 u16BackWheelCarHeadDis;    /* 后轴轴到车头的距离  */
  
}Vehicl_Geometry_ParameterType;



#endif
