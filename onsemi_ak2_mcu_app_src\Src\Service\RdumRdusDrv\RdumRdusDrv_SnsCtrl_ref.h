/******************************************************************************
 * @file      RdumRdusDrv_SnsCtrl.h
 * @brief     安森美超声波探头控制接口
 * <AUTHOR>
 * @date      2025-05-14
 * @note      
 *****************************************************************************/

#ifndef __RDUM_RDUS_DRV_SNS_CTRL_H__
#define __RDUM_RDUS_DRV_SNS_CTRL_H__
/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "types.h"
#include "RdumRdusAddr.h"
#include "RdumRdusCrm.h"

/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/
#define RDUM_RDUS_SNS_TEMPCOMP_PERIOD     1000    /* 温度补偿周期 */
#define RDUM_RDUS_SNS_DM_RETRY_CNT        3       /* 探头发现重试次数 */
#define RDUM_RDUS_SNS_WAIT_MEAS_TIMEOUT   500     /* 等待测量超时时间 */
#define RDUM_RDUS_SNS_WAIT_SELFTEST_TIME  50      /* 等待自检时间 */
#define RDUM_RDUS_SNS_WAIT_STANDBY_TIME   10      /* 等待待机时间 */
#define RDUM_RDUS_SNS_WAIT_WAKEUP_TIME    10      /* 等待唤醒时间 */



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/
/* 探头控制组状态 */
typedef enum
{
    RdumRdusGroupStatus_SNS_DM = 0,         /* 探头发现模式 */
    RdumRdusGroupStatus_SNS_SELFTEST,       /* 探头自检 */
    RdumRdusGroupStatus_START_MEAS,         /* 开始测量 */
    RdumRdusGroupStatus_WAIT_MEAS_COMPLETE, /* 等待测量完成 */
    RdumRdusGroupStatus_STANDBY,            /* 待机模式 */
    RdumRdusGroupStatus_WAKEUP,             /* 唤醒模式 */
    RdumRdusGroupStatus_STOP_MEAS,          /* 停止测量 */
    RdumRdusGroupStatus_WAIT_STOP_MEAS,     /* 等待停止测量 */
    RdumRdusGroupStatus_WAIT_STANDBY,       /* 等待进入待机 */
    RdumRdusGroupStatus_WAIT_WAKEUP,        /* 等待唤醒完成 */
    RdumRdusGroupStatus_SNS_PARAM_CFG,      /* 探头参数配置 */
    RdumRdusGroupStatus_WAIT_SNS_PARAM_CFG, /* 等待探头参数配置 */
    RdumRdusGroupStatus_WAIT_SELFTEST,      /* 等待自检完成 */
    RdumRdusGroupStatus_WAIT_TEMP_COMP,     /* 等待温度补偿 */
    RdumRdusGroupStatus_TEMP_COMP,          /* 温度补偿 */
    RdumRdusGroupStatus_WAIT_REDM,          /* 等待重新发现探头 */
    RdumRdusGroupStatus_REDM,               /* 重新发现探头 */
} RdumRdusGroupStatus_en;

/* 探头测量状态 */
typedef enum
{
    RdumRdusGroupMeas_IDLE = 0,             /* 空闲 */
    RdumRdusGroupMeas_BUSY_MEAS,            /* 忙于测量 */
} RdumRdusGroupMeasStatus_en;

/* 探头控制组配置 */
typedef struct
{
    RdumRdusGroupStatus_en GroupStatus;          /* 控制组状态 */
    RdumRdusGroupStatus_en NextGroupStatus;      /* 下一个状态 */
    uint32 Time1msCnt;                           /* 时间计数器 */
    uint32 TempCompTime1msCnt;                   /* 温度补偿时间计数器 */
    bool StartMeasFlg;                           /* 开始测量标志 */
    RdumRdusGroupMeasStatus_en SnsGroupMeasStatus[DSI3_CHANNEL_NUM]; /* 测量状态 */
    uint32 SnsGroupMeasTime[DSI3_CHANNEL_NUM];                       /* 测量时间 */
} RdumRdusGroup_Cfg;

/* 探头发现状态机 */
typedef enum
{
    eRdumRdusDMSeq_Init = 0,                /* 初始化 */
    eRdumRdusDMSeq_StartAutoDM,             /* 开始自动发现 */
    eRdumRdusDMSeq_TransChangeID,           /* 传输改变ID命令 */
    eRdumRdusDMSeq_TransReadEEPROMID,       /* 读取EEPROM ID */
    eRdumRdusDMSeq_PrepareNextDM,           /* 准备下一次发现 */
    eRdumRdusDMSeq_End,                     /* 结束发现 */
} RdumRdusDMSeq_en;



/******************************************************************************
 * @Const Declaration
 *****************************************************************************/



/******************************************************************************
 * @Variable Declaration
 *****************************************************************************/



/******************************************************************************
 * @Function Declaration
 *****************************************************************************/
/* 初始化探头控制组 */
void RdumRdusDrv_InitSnsCtrlGroup(void);

/* 探头控制主函数 */
void RdumRdusDrv_SnsCtrlMainFunc(void);

/* 更新时间计数 */
void RdumRdusDrv_UpdateTimeCnt(void);

/* 设置下一个控制组状态 */
void RdumRdusDrv_SetNextSnsCtrlGroupStatus(RdumRdusGroupStatus_en NextStatus);

/* 获取下一个控制组状态 */
RdumRdusGroupStatus_en RdumRdusDrv_GetNextSnsCtrlGroupStatus(void);



#endif /* __RDUM_RDUS_DRV_SNS_CTRL_H__ */
