/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * PSL_Algorithm_Types.h: 
 * Created on: 2023-2-22 17:30
 * Original designer: 22866
 ******************************************************************************/
#ifndef PSL_Algorithm_Types_H
#define PSL_Algorithm_Types_H



/* ============================================================================================== */
/* Header files																				      */
/* ============================================================================================== */
//#include "Std_Types.h"
#include "Types.h"

#include "PublicCalAlgorithm_Int.h"


/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/
#define PSL_ECHO_INVALID_VALUE  0xFFFF


/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/

/* 定义探头通道类型 */
typedef enum
{
    PSL_SNS_CH_FLS = 0,
    PSL_SNS_CH_FRS,
    PSL_SNS_CH_RLS,
    PSL_SNS_CH_RRS,   
    PSL_SNS_CH_NUM,
}PSLSnsChannelType;

/*最多处理两个障碍物的坐标*/
typedef enum
{
    PSL_OBJ_NONE  = -1,
    PSL_OBJ_FIRST, 
    PSL_OBJ_SECON,
    PSL_OBJ_NUM,   
}PSL_OBJNumType; 

/* 定义障碍物在Odo坐标的坐标 */
typedef struct
{
    float fObjX;
    float fObjY;
}PSLObjCoorType;

/* 定义探头测量类型 */
typedef enum
{
    PSL_SNS_MEAS_STD  = 0,
    PSL_SNS_MEAS_ADV_CHIRP_DOWN, 
    PSL_SNS_MEAS_ADV_CHIRP_UP,
    PSL_SNS_MEAS_IMPEDANCE = 0x5,
    PSL_SNS_MEAS_IDLE = 0x6
}PSLSnsMeasTypeType;


typedef struct
{
    PSLObjCoorType strSnsLeftObj[PSL_SNS_CH_NUM];
    PSLObjCoorType strSnsMasterObj[PSL_SNS_CH_NUM];
    PSLObjCoorType strSnsRightObj[PSL_SNS_CH_NUM];
    uint8 u8SnsActiveFlag[PSL_SNS_CH_NUM];
    uint16 u16MasterDis[PSL_SNS_CH_NUM];
    uint16 u16MasterHeight[PSL_SNS_CH_NUM];
    PSLSnsMeasTypeType enuMeasMode[PSL_SNS_CH_NUM];
}PSLObjPositionCoorType;


typedef struct
{
    PSLObjCoorType strSnsLeftObj[PSL_SNS_CH_NUM][PSL_OBJ_NUM];
    PSLObjCoorType strSnsMasterObj[PSL_SNS_CH_NUM][PSL_OBJ_NUM];
    PSLObjCoorType strSnsRightObj[PSL_SNS_CH_NUM][PSL_OBJ_NUM];
	PSLObjCoorType strCarLeftObj[PSL_SNS_CH_NUM][PSL_OBJ_NUM];
    PSLObjCoorType strCarMasterObj[PSL_SNS_CH_NUM][PSL_OBJ_NUM];
    PSLObjCoorType strCarRightObj[PSL_SNS_CH_NUM][PSL_OBJ_NUM];
    uint16 u16MasterDis[PSL_SNS_CH_NUM][PSL_OBJ_NUM];
    uint16 u16LeftDis[PSL_SNS_CH_NUM][PSL_OBJ_NUM];
    uint16 u16RightDis[PSL_SNS_CH_NUM][PSL_OBJ_NUM];
    uint16 u16MasterHeight[PSL_SNS_CH_NUM][PSL_OBJ_NUM];
    uint16 u16LeftHeight[PSL_SNS_CH_NUM][PSL_OBJ_NUM];
    uint16 u16RightHeight[PSL_SNS_CH_NUM][PSL_OBJ_NUM];
}PSLCalObjCoorType;

/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
#endif
