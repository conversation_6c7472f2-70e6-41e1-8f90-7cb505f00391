###############################################################################
#
# IAR Assembler V2.10.1.1473 for RH850                    30/May/2025  14:07:26
# Copyright 2012-2018 IAR Systems AB.
#
#    Core         =  g3kh
#    Source file  =  
#        D:\zhang9591_project\AK2\tools\onsemi\AnalysisTool\onsemi_ak2_mcu_app_src\Src\Drv\FlashDrv\FCL\lib\r_fcl_hw_access_asm.s
#    Command line =  
#        -f C:\Users\<USER>\AppData\Local\Temp\EW5974.tmp
#        (D:\zhang9591_project\AK2\tools\onsemi\AnalysisTool\onsemi_ak2_mcu_app_src\Src\Drv\FlashDrv\FCL\lib\r_fcl_hw_access_asm.s
#        --core g3kh --code_model normal --data_model medium --fpu single
#        --double=64 -o
#        D:\zhang9591_project\AK2\tools\onsemi\AnalysisTool\onsemi_ak2_mcu_app_src\IarProject_1581\Debug\Obj
#        -M<> -r -ld
#        D:\zhang9591_project\AK2\tools\onsemi\AnalysisTool\onsemi_ak2_mcu_app_src\IarProject_1581\Debug\List\)
#    List file    =  
#        D:\zhang9591_project\AK2\tools\onsemi\AnalysisTool\onsemi_ak2_mcu_app_src\IarProject_1581\Debug\List\r_fcl_hw_access_asm.lst
#    Object file  =  
#        D:\zhang9591_project\AK2\tools\onsemi\AnalysisTool\onsemi_ak2_mcu_app_src\IarProject_1581\Debug\Obj\r_fcl_hw_access_asm.o
#
###############################################################################

##############################################################################
# Module r_fcl_hw_access_asm                                                 #
##############################################################################

    388                         
 
 152 bytes in section R_FCL_CODE_RAM           , module r_fcl_hw_access_asm
  80 bytes in section R_FCL_CODE_RAM_EX_PROT   , module r_fcl_hw_access_asm
 156 bytes in section R_FCL_CODE_ROM           , module r_fcl_hw_access_asm
 
 388 bytes of CODE memory  in module r_fcl_hw_access_asm

Errors: none
Warnings: none
