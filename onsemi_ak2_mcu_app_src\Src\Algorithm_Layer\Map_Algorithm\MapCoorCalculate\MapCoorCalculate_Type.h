/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * SnsCoorCalculate_Type: 
 * Created on: 2023-08-02 14:56
 * Original designer: AntonyFang
 ******************************************************************************/

#ifndef MapCoorCalculate_Type_H
#define MapCoorCalculate_Type_H

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "types.h"
#include "SnsRawData_Type.h"
#include "Sns_install_Coordinate.h"



/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions ******************************/
/******************************************************************************/
/******************************************************************************/
#define SNS_FRONT_NEAR_MASTER_DIS  580
#define SNS_REAR_NEAR_MASTER_DIS   480
#define OBJ_KALMAN_R               0.01

#define NEAR_CAR_X_SUB             120
#define NEAR_CAR_Y_SUB             200

#define POINT_TO_POINT_CONFIRM_CNT 3
#define POINT_TO_LINE_CONFIRM_CNT  3
#define LINE_TO_POINT_CONFIRM_CNT  3
#define POINT_DISAPEARE_CNT        3
#define LINE_DISAPEARE_CNT         3

#define OBJ_KALMAN_NO_MATCH_CNT    4
#define OBJ_KALMAN_FAST_EXIT_CNT   2

#define OBJ_NO_MATCH_X_SUB         600
#define OBJ_NO_MATCH_Y_SUB         600

#define OBJ_NO_SAME_X_SUB          250
#define OBJ_NO_SAME_Y_SUB          250
#define OBJ_COOR_NO_SAME_CNT       3


#define STD_SECOND_MORE_THAN_FIRST_HEIGHT     400
#define CHIRP_SECOND_MORE_THAN_FIRST_HEIGHT   400
#define STANDARD_ONE_ECHO_FAST_WALL_HEIGHT    210
#define STANDARD_NEAR_FAST_HIGH_DIS           750
#define CHIRP_NO_USE_TYPE_DIS                 800
#define START_ANALYSIS_HEIGHT_TREAD_DIS       1900
#define END_ANALYSIS_HEIGHT_TREAD_DIS         400
#define STD_NEAR_DIR_JUDGE_HIGH_DIS           800
#define STD_START_HEIGHT                      160

#define CHIRP_START_ANALYSIS_HEIGHT_TREAD_DIS 2700
#define CHIRP_END_ANALYSIS_HEIGHT_TREAD_DIS   800
#define CHIRP_START_HEIGHT                    350
#define CHIRP_USE_HEIGHT_JUDGE_TYPE_DIS       2200



#define ONLY_MASTER_FAST_COOR_DIS             400
#define SNS_DIFF_COOR_Y_LIMIT                 550

#define CHIRP_HEIGHT_STRONG_OBJ_TO_HIGH_2KM   3
#define CHIRP_HEIGHT_STRONG_OBJ_TO_HIGH_4KM   2
#define CHIRP_HEIGHT_STRONG_OBJ_TO_HIGH_5KM   1
#define CHIRP_HEIGHT_STRONG_OBJ_TO_HIGH_7KM   0

#define STD_HEIGHT_STRONG_OBJ_TO_HIGH_2KM   3
#define STD_HEIGHT_STRONG_OBJ_TO_HIGH_4KM   2
#define STD_HEIGHT_STRONG_OBJ_TO_HIGH_5KM   1
#define STD_HEIGHT_STRONG_OBJ_TO_HIGH_7KM   0

#define CHIRP_HEIGHT_FLAG_SET_CNT_1KM       8
#define CHIRP_HEIGHT_FLAG_SET_CNT_3KM       5
#define CHIRP_HEIGHT_FLAG_SET_CNT_5KM       3
#define CHIRP_HEIGHT_FLAG_SET_CNT_7KM       2

#define MIDDLE_SNS_LEFT_COOR_Y_LIMIT_MIN    -250
#define MIDDLE_SNS_LEFT_COOR_Y_LIMIT_MAX    650


#define MIDDLE_SNS_RIGHT_COOR_Y_LIMIT_MIN   -650
#define MIDDLE_SNS_RIGHT_COOR_Y_LIMIT_MAX   250


#define CORNER_SNS_LEFT_COOR_Y_LIMIT_MIN    -450
#define CORNER_SNS_LEFT_COOR_Y_LIMIT_MAX    700

#define CORNER_SNS_RIGHT_COOR_Y_LIMIT_MIN   -700
#define CORNER_SNS_RIGHT_COOR_Y_LIMIT_MAX   450


#define SIDE_SNS_LEFT_COOR_Y_LIMIT_MIN      -500
#define SIDE_SNS_LEFT_COOR_Y_LIMIT_MAX      850

#define SIDE_SNS_RIGHT_COOR_Y_LIMIT_MIN     -850
#define SIDE_SNS_RIGHT_COOR_Y_LIMIT_MAX     500


/*****************************************************2023-11-04 探头端原始点云信息相关定义*********************************  */

#define OBJ_COOR_POINT_NUM                  2     /* 每个探头记忆最多两个回波的障碍物坐标 */
#define OBJ_COOR_INVALID_DIS                65535
#define OBJ_COOR_INVALID_HEIGHT             0
#define OBJ_SIDE_SNS_COOR_SUB_ALLOW         750  /* 侧边雷达通过主发定位和侦听定位的差值对比，过滤掉一部分的障碍物 */


#define OBJ_COOR_BUF_NUM                    3
#define POINT_CLOUD_BUF_NUM                 37
#define MAX_POINT_CLOUD_CNT                 36

#define MAX_AREA_MAP_NUM                    4     /* 每个区域最多保留4个区域Map */
#define AREA_0_4_HOLD_TIME                  600   /* 侧边点云维持周期600ms 侧边刷新周期80ms*/

#define AREA_0_4_HOLD_TIME_LOW_SPD          400   /* 侧边点云低速下用400*/

#define AREA_1_3_HOLD_TIME                  500   /* 边角区域点云维持周期暂定500ms */
#define AREA_2_HOLD_TIME                    500   /* 中间区域点云周期暂定500ms */

#define AREA_CLUSTER_POINT_NUM              30    /* 一个区域中,同一类型点的最大数量 */
#define AREA_CLUSTER_NUM                    6     /* 一个区域中,最多聚类簇的个数 */
#define AREA_CLUSTER_RIDUS                  150   /* 聚类区域半径是15cm */
#define AREA_SIDE_CLUSTER_Y_SUB             100   /* 80修改为150 */


#define AREA_CLUSTER_0_4_X_SUB              200
#define AREA_CLUSTER_0_4_Y_SUB              80

#define AREA_CLUSTER_1_3_X_SUB              80
#define AREA_CLUSTER_1_3_Y_SUB              180


#define AREA_MAP_POINT_NEAR_X_SUB           100
#define AREA_MAP_POINT_NEAR_Y_SUB           180

#define POINT_CLOUD_VALID_TO_BUMPER_DIS     3950


/*******************************单独定义宏，供整车Map构建使用*********************************  */
#define VHE_POINT_CLOUD_SIMULATE            1      /* 用于选择是否开启整车点云接口 */
#define VHE_POINT_CLOUD_BUF_NUM             101
#define VHE_MAX_POINT_CLOUD_CNT             100
#define VHE_MAX_MAP_NUM                     20     /* 整车合计最多构建20个Map */

#define SIDE_ONLY_MASTER_CREAT_MAP_DIS      2500


#define FRONT_SIDE_NEAR_ONLY_MASTER_CREAT_MAP_DIS      400
#define REAR_SIDE_NEAR_ONLY_MASTER_CREAT_MAP_DIS       400

#define FRONT_CORNER_NEAR_ONLY_MASTER_CREAT_MAP_DIS      400
#define REAR_CORNER_NEAR_ONLY_MASTER_CREAT_MAP_DIS       300
#define FRONT_MIDDLE_NEAR_ONLY_MASTER_CREAT_MAP_DIS      550
#define REAR_MIDDLE_NEAR_ONLY_MASTER_CREAT_MAP_DIS       500

#define FRONT_CORNER_SUMMON_ONLY_MASTER_CREAT_MAP_DIS    300


#define FRONT_MIDDLE_NEAR_ONLY_MASTER_CREAT_MAP_DIS2     950


#define SAME_FREQ_NOISE_MIN_DIS            250
#define SAME_FREQ_NOISE_MIN_DIS_SUMMON     850
#define SAME_FREQ_NOISE_MIN_DIS_GUIDANCE   300

#define CHIRP_NOISE_MIN_DIS                800

#define SAME_FREQ_NOISE_MAX_DIS            2500
#define FRONT_SIDE_SNS_CLUSTER_LINE_DIS    1000
#define REAR_SIDE_SNS_CLUSTER_LINE_DIS     1000


#define  CAR_SEARCH_TO_GUIDANCE_91_ANGLE                        1.588
#define  CAR_SEARCH_TO_GUIDANCE_90_ANGLE                        1.571
#define  CAR_SEARCH_TO_GUIDANCE_89_ANGLE                        1.553
#define  CAR_SEARCH_TO_GUIDANCE_85_ANGLE                        1.4835
#define  CAR_SEARCH_TO_GUIDANCE_80_ANGLE                        1.3963
#define  CAR_SEARCH_TO_GUIDANCE_75_ANGLE                        1.309
#define  CAR_SEARCH_TO_GUIDANCE_70_ANGLE                        1.2217
#define  CAR_SEARCH_TO_GUIDANCE_65_ANGLE                        1.1345
#define  CAR_SEARCH_TO_GUIDANCE_60_ANGLE                        1.0472
#define  CAR_SEARCH_TO_GUIDANCE_55_ANGLE                        0.9599
#define  CAR_SEARCH_TO_GUIDANCE_50_ANGLE                        0.8727
#define  CAR_SEARCH_TO_GUIDANCE_45_ANGLE                        0.7854
#define  CAR_SEARCH_TO_GUIDANCE_40_ANGLE                        0.6981
#define  CAR_SEARCH_TO_GUIDANCE_35_ANGLE                        0.6109
#define  CAR_SEARCH_TO_GUIDANCE_30_ANGLE                        0.5236
#define  CAR_SEARCH_TO_GUIDANCE_25_ANGLE                        0.4363
#define  CAR_SEARCH_TO_GUIDANCE_20_ANGLE                        0.3491
#define  CAR_SEARCH_TO_GUIDANCE_15_ANGLE                        0.2618
#define  CAR_SEARCH_TO_GUIDANCE_10_ANGLE                        0.1745
#define  CAR_SEARCH_TO_GUIDANCE_8_ANGLE                         0.14
#define  CAR_SEARCH_TO_GUIDANCE_5_ANGLE                         0.0873
#define  CAR_SEARCH_TO_GUIDANCE_3_ANGLE                         0.035

#define  AREA_POINT_BUF_CNT     16    /* 保留每个侧边探头最新15帧的原始数据(含有效点云和无效值)，存于循环队列，开辟16个buf */
#define  AREA_POINT_BUF_ID_CNT  15    /* 用于对缓存中新旧数据进行由新到旧的Id排序 */

#define  REAL_TIME_MIN_VALUE    8

#define  REAR_GUIDANCE_MAP_DIS  1000  /* 后侧雷达在Guidance前阶段画Map的起始距离 */
#define  REAR_GUIDANCE_MAP_ANGLE  CAR_SEARCH_TO_GUIDANCE_40_ANGLE

/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions *****************************/
/******************************************************************************/
/******************************************************************************/

/* 定义两个车辆坐标点的关系 */
typedef enum
{
    TWO_POINT_NONE = 0,
    TWO_POINT_SAME,                     /* 两个点相同，可以融合 */
    TWO_POINT_ONE_LINE,                 /* 两个点在同一条直线上 */
    TWO_POINT_DIFF,                     /* 两个点不同 */
}eTwoPointRelationType;


typedef enum
{
    SNS_COOR_MASTER   = 0,
    SNS_COOR_LEFT, 
    SNS_COOR_RIGHT,   
}SnsCoorWorkType;

/* 主发侦听探头的位置关系 */
typedef enum
{
    SNS_SIDE_CORNER   = 0,
    SNS_CORNER_MIDDLE, 
    SNS_MIDDLE_MIDDLE,   
}SnsMasterListenPosType;


/* 定义探头原始点云信息单元,包含障碍物坐标，类型(多障碍物时，通过3维坐标进行区分)
   障碍物主发、侦听距离(用于前期分析)，障碍物回波高度，用于后续通过回波高度变化判断障碍物高低*/
typedef struct
{
    ObjCoorType          strObjSnsCoor;                                    /* 探测障碍物相对于探头的坐标 */
    ObjCoorType          strObjCarCoor;                                    /* 探测障碍物的车辆坐标系坐标 */
    ObjCoorType          strObjOdoCoor;                                    /* 探测障碍物的Odo坐标系坐标 */
    float                fObjCarCoor_Sub;                                  /* 障碍物车辆坐标和主发坐标定位的差值，主要用于侧雷达Map构建使用 */
    float                fObjToBumperDis;                                  /* 障碍物到保杠的距离 */
    uint16               u16MasterDis;                                     /* 探测障碍物对应的主发距离 */
    uint16               u16ListenDis;                                     /* 探测障碍物对应的侦听距离 */
    uint16               u16MasterHeight;                                  /* 探测障碍物对应的主发回波高度 */
    uint16               u16ListenHeight;                                  /* 探测障碍物对应的侦听回波高度 */
    uint16               u16MasterSecondHeight;                            /* 主发第二回波高度 */
    uint16               u16ListenSecondHeight;                            /* 侦听第二回波高度 */
    #if VS_SIMULATE_ENABLE
    uint32               enuObjType;                                       /* 障碍物的初步判断类型 */
    #else
    OriginalObjType      enuObjType;                                       /* 障碍物的初步判断类型 */
    #endif
    
    #if VS_SIMULATE_ENABLE
    sint32               enuObjArea;                                       /* 障碍物所属分区的详细位置 */
    #else
    ObjMAPAreaType       enuObjArea;                                       /* 障碍物所属分区的详细位置 */
    #endif
    
    uint8                u8CoorValidFlag;                                  /* 障碍物坐标有效标志 */
    uint8                u8SnsNoiseCnt;                                    /* 探头端上报噪音计数 */
}ObjCoorPointUnitType;


typedef struct
{
    #if VS_SIMULATE_ENABLE
    uint32                eMeasType;                                        /* 探头的工作模式 */
    #else
    PDCSnsMeasTypeType    eMeasType;                                        /* 探头的工作模式 */
    #endif
    uint32                u32MeasTime;                                      /* 引入时间戳，该时间戳在信号组层级进行获取 */
    ObjCoorPointUnitType  LeftData[OBJ_COOR_POINT_NUM];                     /* 左侦听障碍物坐标点信息 */
    ObjCoorPointUnitType  RightData[OBJ_COOR_POINT_NUM];                    /* 右侦听障碍物坐标点信息 */
    ObjCoorPointUnitType  MasterData[OBJ_COOR_POINT_NUM];                   /* 主发障碍物坐标点信息 */
    uint8                 u8LeftObjCoorCnt;                                 /* 左侦听障碍物个数 */
    uint8                 u8RightObjCoorCnt;                                /* 右侦听障碍物个数 */
    uint8                 u8MasterObjCoorCnt;                               /* 主发障碍物个数 */
    uint8                 u8LeftAlredayToBufFlag;                           /* 左侦听点云数据已经更新进入点云Buf标志位 */
    uint8                 u8RightAlredayToBufFlag;                          /* 左侦听点云数据已经更新进入点云Buf标志位 */
    uint8                 u8MasterAlredayToBufFlag;                         /* 左侦听点云数据已经更新进入点云Buf标志位 */
    
}SnsObjCoorPointType;


/* 坐标缓存周期 */
typedef enum
{
    OBJ_COOR_CYCLE0 = 0,
    OBJ_COOR_CYCLE1, 
    OBJ_COOR_CYCLE2, 
    OBJ_COOR_CYCLE3,
    OBJ_COOR_CYCLE4,
    OBJ_COOR_CYCLE_NUM, 
}ObjCoorBufCycleType;

/* 点云产生的类型 */
typedef enum
{
    POINT_CLOUD_DET_NONE   = 0,
    POINT_CLOUD_DET_BY_LEFT,
    POINT_CLOUD_DET_BY_RIGHT,
    POINT_CLOUD_DET_BY_MASTER,  
}SnsPointCloudDetType;

/* 点云产生的类型 */
typedef enum
{
    AREA_MAP_SHAPE_NONE   = 0,
    AREA_MAP_SHAPE_POINT,
    AREA_MAP_SHAPE_LINE,  
}SnsAreaMapSharpType;

/* 点云产生的类型 */
typedef enum
{
    AREA_MAP_ID_NONE   = -1,
    AREA_MAP_ID_0,
    AREA_MAP_ID_1, 
    AREA_MAP_ID_2,
    AREA_MAP_ID_3, 
    AREA_MAP_NUM, 
}SnsAreaMapIdType;


#if 0
/* 定义探头端的缓存，包含过去三个周期的点云数据，和该探头与相邻探头之间组成的实时Map */
typedef struct
{
    ObjCoorPointUnitType  LeftData[OBJ_COOR_POINT_NUM][OBJ_COOR_CYCLE_NUM];    /* 左侦听障碍物坐标点信息 */
    ObjCoorPointUnitType  RightData[OBJ_COOR_POINT_NUM][OBJ_COOR_CYCLE_NUM];   /* 右侦听障碍物坐标点信息 */
    ObjCoorPointUnitType  MasterData[OBJ_COOR_POINT_NUM][OBJ_COOR_CYCLE_NUM];  /* 主发障碍物坐标点信息 */
    uint8                 u8LeftObjCoorCnt[OBJ_COOR_CYCLE_NUM];                /* 左侦听障碍物个数 */
    uint8                 u8RightObjCoorCnt[OBJ_COOR_CYCLE_NUM];               /* 右侦听障碍物个数 */
    uint8                 u8MasterObjCoorCnt[OBJ_COOR_CYCLE_NUM];              /* 主发障碍物个数 */
    ObjCoorBufCycleType   enuDetCycle;                                         /* 探测点云是对应的索引值 */
}SnsObjCoorPointBufType;
#endif


/* 定义探头端点云单元的数据类型，用于在Empty Map缓存中进行数据处理 */
typedef struct
{
    ObjCoorType          strObjCarCoor;                                    /* 探测障碍物的车辆坐标系坐标 */
    ObjCoorType          strObjOdoCoor;                                    /* 探测障碍物的Odo坐标系坐标 */
    #if VS_SIMULATE_ENABLE
    uint32               enuObjType;                                       /* 障碍物类型，用于聚类前的初步判断，及障碍物高低判断 */
    uint32               enuDetType;                                       /* 点云产生的类型--左侦听or右侦听 */
    sint32               enuSnsCh;                                         /* 点云对应的探头通道 */
    uint32               eMeasType;                                        /* 点云产生对应的工作模式 */
    #else
    OriginalObjType      enuObjType;                                       /* 障碍物类型，用于聚类前的初步判断，及障碍物高低判断 */
    SnsPointCloudDetType enuDetType;                                       /* 点云产生的类型--左侦听or右侦听 */
    PDCSnsChannelType    enuSnsCh;                                         /* 点云对应的探头通道 */
    PDCSnsMeasTypeType   eMeasType;                                        /* 点云产生对应的工作模式 */
    #endif
    uint32               u32DetSysTime;                                    /* 点云更新时对应的系统时间 */
    uint16               u16ExistTime;                                     /* 点云已经存在的时间周期 */
    uint16               u16MasterDis;                                     /* 点云对应的主发距离 */
    uint16               u16MasterHeight;                                  /* 点云对应的主发回波高度 */
    uint8                u8ValidFlag;                                      /* 点云有效标志 */
    uint8                u8TypeAlredayBeUseFlag;                           /* 点云的坐标类型已经被提取的标志 */
    uint8                u8CoorAlredayBeUseFlag;                           /* 坐标已经被提取的标志位 */
    uint8                u8SnsNoiseCnt;                                    /* 探头端上报噪音计数 */
}SnsPointCloudUnitType;

#if VHE_POINT_CLOUD_SIMULATE
typedef struct
{
    float   centerX;
    float   centerY;
    float   minX;
    float   minY;
    float   maxX;
    float   maxY;
}ObjRect;
#endif

/* 区域内小Map的数据类型 */
typedef struct
{ 
    ObjTypeCntType       ObjTypeCnt;                                    /* 区域Map中不同类型障碍物计数 */
    ObjCoorType          P1_CarCoor;                                    /* P1点车辆坐标 */
    ObjCoorType          P2_CarCoor;                                    /* P2点车辆坐标 */
    ObjCoorType          P1_OdoCoor;                                    /* P1点Odo坐标 */
    ObjCoorType          P2_OdoCoor;                                    /* P2点Odo坐标 */
    #if VS_SIMULATE_ENABLE
    uint32               MapSharp;                                      /* 区域Map的类型，点or线 */
    sint32               MapId;                                         /* 区域Map对应的ID */
    uint32               enuMapType;                                    /* 区域Map的初步类型 */
    sint32               enuSideMapSns;                                 /* 侧边Map产生对应的探头 */
    uint32               enuMeasType;                                   /* 针对DE生成map时，记录探头的工作类型 */
    #else
    SnsAreaMapSharpType  MapSharp;                                      /* 区域Map的类型，点or线 */
    SnsAreaMapIdType     MapId;                                         /* 区域Map对应的ID */
    OriginalObjType      enuMapType;                                    /* 区域Map的初步类型 */
    SideSnsIndexType     enuSideMapSns;                                 /* 侧边Map产生对应的探头 */
    PDCSnsMeasTypeType   enuMeasType;                                   /* 针对DE生成map时，记录探头的工作类型 */
    #endif
#if VHE_POINT_CLOUD_SIMULATE
    ObjRect              cirRect;
    uint32               Timestamp;
#endif
    uint32               u32MapTime;                                    /* 区域Map产生的时间，从信号组递延过来 */
    uint16               u16MasterDis;                                  /* 区域Map中主发距离 */
    uint16               u16StdMasterHeight;                            /* 区域Map中定频主发回波高度 */
    uint16               u16ChirpMasterHeight;                          /* 区域Map中扫频主发回波高度 */
    uint8                u8BeFusionToRealMapFlag;                       /* 被融合到实时Map的标志位 */   
}SnsAreaMapType;

/* 定义探头端点云单元的数据类型，用于在Empty Map缓存中进行数据处理 */
typedef struct
{
    SnsPointCloudUnitType Buf[POINT_CLOUD_BUF_NUM];                        /* 区域点云的缓存 */
    SnsAreaMapType        AreaMap[MAX_AREA_MAP_NUM];                       /* 区域Map缓存 */
    uint16                u16ExistTimeMax;                                 /* 最大生存的时间周期设置 */
    uint8                 u8BufCnt;                                        /* 该区域点云缓存的个数 */
    uint8                 u8BufValidPointCnt;                              /* 该区域有效点云的个数 */
    uint8                 u8StartInx;                                      /* 循环队列的开始索引 */
    uint8                 u8EndInx;                                        /* 循环队列的结束索引 */
    uint8                 u8AreaMapNum;                                    /* 区域Map的个数 */
}SnsPointCloudBufType;


/* 定义区域聚类数据类型 */
typedef struct
{
    ObjTypeCntType        ObjTypeCnt;                                      /* 聚类Cluster中不同类型障碍物计数 */
    ObjCoorType           ClusterCoor;                                     /* 聚类中心点的坐标 */
#if VS_SIMULATE_ENABLE
    uint32                enuClusterType;                                  /* 聚类对象的类型 */
#else
    OriginalObjType       enuClusterType;                                  /* 聚类对向的类型 */
#endif
    uint32                u32ClusterTime;                                  /* 聚类对象的时间戳，从信号组层递延过来 */
    uint16                u16MasterDis;                                    /* 聚类Cluster中主发距离 */
    uint16                u16StdMasterHeight;                              /* 聚类Cluster中定频主发回波高度 */
    uint16                u16ChirpMasterHeight;                            /* 聚类Cluster中扫频主发回波高度 */
}ClusterType;

/* 定义区域聚类缓存数据类型 */
typedef struct
{
    ClusterType           Cluster[AREA_CLUSTER_NUM];                       /* 聚类簇的坐标等信息 */
    uint8                 u8PointUnitId[AREA_CLUSTER_POINT_NUM];           /* 聚类前对应区域的点云ID，即不需要数据直接透传，只需要Id反向获取即可 */
    uint8                 TotalPointCnt;                                   /* 总共的个数 */
    uint8                 TotalClusterCnt;                                 /* 总共聚类的簇个数 */
}AreaClusterBufType;




/* 定义不同位置左右侦听的坐标限制设置 */
typedef struct
{
    sint16 s16LeftListenObj_Y_Min;
    sint16 s16LeftListenObj_Y_Max;
    sint16 s16RightListenObj_Y_Min;
    sint16 s16RightListenObj_Y_Max;
}SnsObjCoorLimitType;


/*********************针对侧边区域，单独建立缓存，用于侧雷达Map构建********************************  */


/******************************************************************************
* 设计描述 : 侧边探头探测障碍物的状态
* 设计索引 : 
*******************************************************************************/
typedef enum
{
    SIDE_OBJ_STS_NONE = 0,
    SIDE_OBJ_STS_UPDATING,
    SIDE_OBJ_STS_NEW_OCCURED,
    SIDE_OBJ_STS_DISAPPEAR,
    SIDE_OBJ_STS_NUM,
}eSideObjStsType;



/* 记录侧边区域Map点的缓存，用于侧边Map画长后的矫正 */
typedef struct
{
    ObjCoorType          PointOdoCoor;       /* 坐标点世界坐标 */
    uint32               u32DetSysTime;      /* 该帧数据产生时的系统时间 */
#if VS_SIMULATE_ENABLE
    uint32               enuObjType;         /* 障碍物的原始判断类型 */
    uint32               enuDetType;         /* 点云产生的类型--左侦听or右侦听 */
#else
    OriginalObjType      enuObjType;         /* 障碍物的原始判断类型 */
    SnsPointCloudDetType enuDetType;         /* 点云产生的类型--左侦听or右侦听 */
#endif
    uint16               u16MasterDis;       /* 探测障碍物对应的主发距离 */
    uint16               u16ListenDis;       /* 探测障碍物对应的侦听距离 */
    uint16               u16MasterHeight;    /* 探测障碍物对应的主发回波高度 */
    uint16               u16ListenHeight;    /* 探测障碍物对应的侦听回波高度 */
    uint8                u8CloudValidFlag;   /* 该帧数据点云有效标志 */
    uint8                u8TypeBeUsedFlag;
}SideAreaPointUnit;


/* 记录侧边区域Map点的缓存，用于侧边Map画长后的矫正 */
typedef struct
{
    SideAreaPointUnit   Buf[AREA_POINT_BUF_CNT];
    uint8               u8IdBuf[AREA_POINT_BUF_ID_CNT];
    uint8               u8ValidBufCnt;
    uint8               u8StartInx;             /* 循环队列的开始索引 */
    uint8               u8EndInx;               /* 循环队列的结束索引 */
    uint8               u8MapStartInx;          /* 对应于当前Map起始点的索引值 */
    uint8               u8MapEndInx;            /* 对应于Map结束点的索引值 */
    uint8               u8AfterStartCnt;
    uint8               u8MapEndInxNoUpdateCnt; /* Map结束时最后一帧点云匹配后，未更新的持续计数 */
    uint8               u8FirstUseMasterInx;    /* 首次使用主发对应的索引值 */
    uint8               u8ContinueUseMasterCnt; /* 连续使用主发画Map的计数 */
    uint8               u8StartModifyFlag;      /* 起始点被矫正的标志 */

    uint8               u8TotalTypeCnt;         /* 一个Map开始，总共的类型计数 */
    uint8               u8HighTypeCnt;          /* 一个Map开始，高类型计数 */
    uint8               u8NoUpdateCnt;          /* 不更新计数 */
    
}SideAreaPointBufType;


typedef struct
{
    ObjTypeCntType       ObjTypeCnt;                                    /* 侧边区域Map中不同类型障碍物计数 */
    ObjCoorType          P1_CarCoor;                                    /* P1点车辆坐标 */
    ObjCoorType          P2_CarCoor;                                    /* P2点车辆坐标 */
    ObjCoorType          P1_OdoCoor;                                    /* P1点Odo坐标 */
    ObjCoorType          P2_OdoCoor;                                    /* P2点Odo坐标 */
#if VS_SIMULATE_ENABLE
    uint32               MapSharp;                                      /* 区域Map的类型，点or线 */
    uint32               enuMapType;                                    /* 区域Map的初步类型 */
#else
    SnsAreaMapSharpType  MapSharp;                                      /* 区域Map的类型，点or线 */
    OriginalObjType      enuMapType;                                    /* 区域Map的初步类型 */
#endif
    SnsCarMoveDirType    eSnsMovDir;
    float                fNearestMapToSnsDis;
    uint32               u32SideUpdateTime;                             /* 侧边区域Map对应的产生时间 */
    uint16               u16ValidMasterDis;
    uint16               u16MasterStdHeight;
    uint16               u16MasterChirpHeight;
    uint8                u8NearExistMapFlag;
    uint8                u8ValidObjCoorFlag;
    uint8                u8CurbNeedMasterCoorFlag;                      /* 弥补路沿需要使用主发定位的标志 */
    uint8                u8FarDisNoNeedMasterCoorFlag;                  /* 3000cm远距离，识别为柱子后，不需要使用单主发定位标志，防止后续画超 */
    uint8                u8NearstMapId;
    uint8                u8MasterDisNoMatchCnt;
    uint8                u8NewObjCoorFlag;
    uint8                u8SnsFarawayLineFlag;                          /* 用于记录探头远离线状Map的标志 */
}SideAreaMapType;



/**********************************************单独建立所有12个探头的点云Buffer，用于整体构建Map使用***********************/

typedef struct
{
    SnsPointCloudUnitType Buf[VHE_POINT_CLOUD_BUF_NUM];                    /* 12个探头过去5轮周期的点云缓存 */
    SnsAreaMapType        VheMap[VHE_MAX_MAP_NUM];                         /* 12个探头一起构建的Map点 */
    uint16                u16ExistTimeMax;                                 /* 最大生存的时间周期设置 */
    uint8                 u8BufCnt;                                        /* 该区域点云缓存的个数 */
    uint8                 u8StartInx;                                      /* 循环队列的开始索引 */
    uint8                 u8EndInx;                                        /* 循环队列的结束索引 */
    uint8                 u8VheMapNum;                                     /* 区域Map的个数 */
}VehiclePointCloudBufType;


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/








#endif /* end of SnsCoorCalculate_Type_H */

