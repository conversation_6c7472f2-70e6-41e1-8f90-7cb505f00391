/*********************************************************************************************************************
 * File Name     : $Source: eel_descriptor.h $
 * Mod. Revision : $Revision: 1.3 $
 * Mod. Date     : $Date: 2014/09/10 11:54:37MESZ $
 * Device(s)     : RV40 Flash based RH850 microcontroller
 * Description   : EEL run-time configuration descriptor variable related defines. 
 *                 To be configured according to the application needs
 *********************************************************************************************************************/

/*********************************************************************************************************************
 * DISCLAIMER 
 * This software is supplied by Renesas Electronics Corporation and is only  intended for use with Renesas products. 
 * No other uses are authorized. This software is owned by Renesas Electronics Corporation and is protected under all 
 * applicable laws, including copyright laws. 
 * THIS SOFTWARE IS PROVIDED "AS IS" AND RENESAS MAKES NO WARRANTIES REGARDING THIS SOFTWARE, WHETHER EXPRESS, IMPLIED 
 * OR STATUTORY, INCLUDING BUT NOT LIMITED TO WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND 
 * NON-INFRINGEMENT. ALL SUCH WARRANTIES ARE EXPRESSLY DISCLAIMED. 
 * TO THE MAXIMUM EXTENT PERMITTED NOT PROHIBITED BY LAW, NEITHER RENESAS ELECTRONICS CORPORATION NOR ANY OF ITS 
 * AFFILIATED COMPANIES SHALL BE LIABLE FOR ANY DIRECT, INDIRECT, SPECIAL, INCIDENTAL OR CONSEQUENTIAL DAMAGES FOR ANY 
 * REASON RELATED TO THIS SOFTWARE, EVEN IF RENESAS OR ITS AFFILIATES HAVE BEEN ADVISED OF THE POSSIBILITY OF SUCH 
 * DAMAGES.
 * Renesas reserves the right, without notice, to make changes to this software and to discontinue the availability of 
 * this software. By using this software, you agree to the additional terms and conditions found by accessing the
 * following link: 
 * http://www.renesas.com/disclaimer 
 * 
 * Copyright (C) 2014 Renesas Electronics Corporation. All rights reserved.     
 *********************************************************************************************************************/


#ifndef R_EEL_DESCRIPTOR_H
#define R_EEL_DESCRIPTOR_H

    
/*********************************************************************************************************************
 * User changable section
 *********************************************************************************************************************/

    /******************************************************************************************************************
    * Important definitions for run-time configuration of the EEL
    ******************************************************************************************************************/
    #define EEL_CONFIG_VBLK_SIZE                    96          /**< Virtual block size (# physical Flash blocks) */
    #define EEL_CONFIG_VBLK_CNT_REFRESH_THRESHOLD    3           /**< Threshold for minimum no. of prepared blocks */
    #define EEL_CONFIG_ERASE_SUSPEND_THRESHOLD      10           /**< Threshold for erase suspend cnt. Exceeding this 
                                                                      results in a warning */
    
    /******************************************************************************************************************
    * R_EEL_CONFIG_IDL_TABLE
    * Descriptor table containing data set identifier and data set length
    * as: 
    * { { <16-bit ID>, <16-bit length in bytes> }, {...}, {...}, .... }
    ******************************************************************************************************************/
    #define EEL_CONFIG_IDL_TABLE          {                       \
                                             { EEL_DATA_SECTION_1_ID, EEL_DATA_SECTION_1_LEN },\
                                             { EEL_DATA_SECTION_2_ID, EEL_DATA_SECTION_2_LEN },\
                                             { EEL_DATA_SECTION_3_ID, EEL_DATA_SECTION_3_LEN },\
                                             { EEL_DATA_SECTION_4_ID, EEL_DATA_SECTION_4_LEN },\
                                             { EEL_DATA_SECTION_5_ID, EEL_DATA_SECTION_5_LEN }, \
                                             { EEL_DATA_SECTION_6_ID, EEL_DATA_SECTION_6_LEN }, \
                                             { EEL_DATA_SECTION_7_ID, EEL_DATA_SECTION_7_LEN }, \
                                             { EEL_DATA_SECTION_8_ID, EEL_DATA_SECTION_8_LEN }, \
                                             { EEL_DATA_SECTION_9_ID, EEL_DATA_SECTION_9_LEN }, \
                                             { EEL_DATA_SECTION_10_ID, EEL_DATA_SECTION_10_LEN }, \
                                             { EEL_DATA_SECTION_11_ID, EEL_DATA_SECTION_11_LEN }, \
                                             { EEL_DATA_SECTION_12_ID, EEL_DATA_SECTION_12_LEN }, \
                                             { EEL_DATA_SECTION_13_ID, EEL_DATA_SECTION_13_LEN }, \
                                             { EEL_DATA_SECTION_14_ID, EEL_DATA_SECTION_14_LEN }, \
                                             { EEL_DATA_SECTION_15_ID, EEL_DATA_SECTION_15_LEN }, \
                                             { EEL_DATA_SECTION_16_ID, EEL_DATA_SECTION_16_LEN }, \
                                             { EEL_DATA_SECTION_17_ID, EEL_DATA_SECTION_17_LEN }, \
                                             { EEL_DATA_SECTION_18_ID, EEL_DATA_SECTION_18_LEN }, \
                                             { EEL_DATA_SECTION_19_ID, EEL_DATA_SECTION_19_LEN }, \
                                             { EEL_DATA_SECTION_20_ID, EEL_DATA_SECTION_20_LEN }, \
                                             { EEL_DATA_SECTION_21_ID, EEL_DATA_SECTION_21_LEN }, \
                                             { EEL_DATA_SECTION_22_ID, EEL_DATA_SECTION_22_LEN }, \
                                             { EEL_DATA_SECTION_23_ID, EEL_DATA_SECTION_23_LEN }, \
                                             { EEL_DATA_SECTION_24_ID, EEL_DATA_SECTION_24_LEN }, \
                                             { EEL_DATA_SECTION_25_ID, EEL_DATA_SECTION_25_LEN }, \
                                             { EEL_DATA_SECTION_26_ID, EEL_DATA_SECTION_26_LEN }, \
                                             { EEL_DATA_SECTION_27_ID, EEL_DATA_SECTION_27_LEN }, \
                                             { EEL_DATA_SECTION_28_ID, EEL_DATA_SECTION_28_LEN }, \
                                             { EEL_DATA_SECTION_29_ID, EEL_DATA_SECTION_29_LEN }, \
                                             { EEL_DATA_SECTION_30_ID, EEL_DATA_SECTION_30_LEN }, \
                                             { EEL_DATA_SECTION_31_ID, EEL_DATA_SECTION_31_LEN }, \
                                             { EEL_DATA_SECTION_32_ID, EEL_DATA_SECTION_32_LEN }, \
                                             { EEL_DATA_SECTION_33_ID, EEL_DATA_SECTION_33_LEN }, \
                                             { EEL_DATA_SECTION_34_ID, EEL_DATA_SECTION_34_LEN }, \
                                             { EEL_DATA_SECTION_35_ID, EEL_DATA_SECTION_35_LEN }, \
                                             { EEL_DATA_SECTION_36_ID, EEL_DATA_SECTION_36_LEN }, \
                                             { EEL_DATA_SECTION_37_ID, EEL_DATA_SECTION_37_LEN }, \
                                             { EEL_DATA_SECTION_38_ID, EEL_DATA_SECTION_38_LEN }, \
                                             { EEL_DATA_SECTION_39_ID, EEL_DATA_SECTION_39_LEN }, \
                                             { EEL_DATA_SECTION_40_ID, EEL_DATA_SECTION_40_LEN }, \
                                             { EEL_DATA_SECTION_41_ID, EEL_DATA_SECTION_41_LEN }, \
                                             { EEL_DATA_SECTION_42_ID, EEL_DATA_SECTION_42_LEN }, \
                                             { EEL_DATA_SECTION_43_ID, EEL_DATA_SECTION_43_LEN }, \
                                             { EEL_DATA_SECTION_44_ID, EEL_DATA_SECTION_44_LEN }, \
                                             { EEL_DATA_SECTION_45_ID, EEL_DATA_SECTION_45_LEN }, \
                                             { EEL_DATA_SECTION_46_ID, EEL_DATA_SECTION_46_LEN }, \
                                             { EEL_DATA_SECTION_47_ID, EEL_DATA_SECTION_47_LEN }, \
                                             { EEL_DATA_SECTION_48_ID, EEL_DATA_SECTION_48_LEN }, \
                                             { EEL_DATA_SECTION_49_ID, EEL_DATA_SECTION_49_LEN }, \
                                             { EEL_DATA_SECTION_50_ID, EEL_DATA_SECTION_50_LEN }, \
                                             { EEL_DATA_SECTION_51_ID, EEL_DATA_SECTION_51_LEN }, \
                                             { EEL_DATA_SECTION_52_ID, EEL_DATA_SECTION_52_LEN }, \
                                             { EEL_DATA_SECTION_53_ID, EEL_DATA_SECTION_53_LEN }, \
                                             { EEL_DATA_SECTION_54_ID, EEL_DATA_SECTION_54_LEN }, \
                                             { EEL_DATA_SECTION_55_ID, EEL_DATA_SECTION_55_LEN }, \
                                             { EEL_DATA_SECTION_56_ID, EEL_DATA_SECTION_56_LEN }, \
                                             { EEL_DATA_SECTION_57_ID, EEL_DATA_SECTION_57_LEN }, \
                                             { EEL_DATA_SECTION_58_ID, EEL_DATA_SECTION_58_LEN }, \
                                             { EEL_DATA_SECTION_59_ID, EEL_DATA_SECTION_59_LEN }, \
                                             { EEL_DATA_SECTION_60_ID, EEL_DATA_SECTION_60_LEN }, \
                                             { EEL_DATA_SECTION_61_ID, EEL_DATA_SECTION_61_LEN }, \
                                             { EEL_DATA_SECTION_62_ID, EEL_DATA_SECTION_62_LEN }, \
                                             { EEL_DATA_SECTION_63_ID, EEL_DATA_SECTION_63_LEN }, \
                                             { EEL_DATA_SECTION_64_ID, EEL_DATA_SECTION_64_LEN }, \
                                             { EEL_DATA_SECTION_65_ID, EEL_DATA_SECTION_65_LEN }, \
                                             { EEL_DATA_SECTION_66_ID, EEL_DATA_SECTION_66_LEN }, \
                                             { EEL_DATA_SECTION_67_ID, EEL_DATA_SECTION_67_LEN }, \
                                             { EEL_DATA_SECTION_68_ID, EEL_DATA_SECTION_68_LEN }, \
                                             { EEL_DATA_SECTION_69_ID, EEL_DATA_SECTION_69_LEN }, \
                                             { EEL_DATA_SECTION_70_ID, EEL_DATA_SECTION_70_LEN }, \
                                             { EEL_DATA_SECTION_71_ID, EEL_DATA_SECTION_71_LEN }, \
                                             { EEL_DATA_SECTION_72_ID, EEL_DATA_SECTION_72_LEN }, \
                                             { EEL_DATA_SECTION_73_ID, EEL_DATA_SECTION_73_LEN }, \
                                             { EEL_DATA_SECTION_74_ID, EEL_DATA_SECTION_74_LEN }, \
                                             { EEL_DATA_SECTION_75_ID, EEL_DATA_SECTION_75_LEN }, \
                                             { EEL_DATA_SECTION_76_ID, EEL_DATA_SECTION_76_LEN }, \
                                             { EEL_DATA_SECTION_77_ID, EEL_DATA_SECTION_77_LEN }, \
                                             { EEL_DATA_SECTION_78_ID, EEL_DATA_SECTION_78_LEN }, \
                                             { EEL_DATA_SECTION_79_ID, EEL_DATA_SECTION_79_LEN }, \
                                             { EEL_DATA_SECTION_80_ID, EEL_DATA_SECTION_80_LEN }, \
                                             { EEL_DATA_SECTION_81_ID, EEL_DATA_SECTION_81_LEN }, \
                                             { EEL_DATA_SECTION_82_ID, EEL_DATA_SECTION_82_LEN }, \
                                             { EEL_DATA_SECTION_83_ID, EEL_DATA_SECTION_83_LEN }, \
                                             { EEL_DATA_SECTION_84_ID, EEL_DATA_SECTION_84_LEN }, \
                                             { EEL_DATA_SECTION_85_ID, EEL_DATA_SECTION_85_LEN }, \
                                             { EEL_DATA_SECTION_86_ID, EEL_DATA_SECTION_86_LEN }, \
                                             { EEL_DATA_SECTION_87_ID, EEL_DATA_SECTION_87_LEN }, \
                                             { EEL_DATA_SECTION_88_ID, EEL_DATA_SECTION_88_LEN }, \
                                             { EEL_DATA_SECTION_89_ID, EEL_DATA_SECTION_89_LEN }, \
                                             { EEL_DATA_SECTION_90_ID, EEL_DATA_SECTION_90_LEN }, \
                                             { EEL_DATA_SECTION_91_ID, EEL_DATA_SECTION_91_LEN }, \
                                             { EEL_DATA_SECTION_92_ID, EEL_DATA_SECTION_92_LEN }, \
                                             { EEL_DATA_SECTION_93_ID, EEL_DATA_SECTION_93_LEN }, \
                                             { EEL_DATA_SECTION_94_ID, EEL_DATA_SECTION_94_LEN }, \
                                             { EEL_DATA_SECTION_95_ID, EEL_DATA_SECTION_95_LEN }, \
                                             { EEL_DATA_SECTION_96_ID, EEL_DATA_SECTION_96_LEN }, \
                                             { EEL_DATA_SECTION_97_ID, EEL_DATA_SECTION_97_LEN }, \
                                             { EEL_DATA_SECTION_98_ID, EEL_DATA_SECTION_98_LEN }, \
                                             { EEL_DATA_SECTION_99_ID, EEL_DATA_SECTION_99_LEN }, \
                                        }  
    

/*********************************************************************************************************************
 * Even if possible, this section should not b echanged by the user
 *********************************************************************************************************************/
    
    /*****************************************************************************************************************
     * Descriptor variable declaration
     *****************************************************************************************************************/
    extern const r_eel_descriptor_t eelConfig_enu;


/*********************************************************************************************************************/
#endif  /* #ifndef R_EEL_DESCRIPTOR_H */
