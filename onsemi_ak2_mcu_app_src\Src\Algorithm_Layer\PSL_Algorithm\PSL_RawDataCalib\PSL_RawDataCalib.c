/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * SnsRawDataCalib: 
 * Created on: 2024-02-23 14:29
 * Original designer: 22866

 ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "PSL_RawDataCalib.h"


/******************************************************************************/
/******************************************************************************/
/****************************** Public Definition *****************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************前雷达ECU端定频阈值筛选*****************************************************  */
/* 定频情况下，FLS和FRS阈值表，用于筛选有效障碍物回波，定频模式修改为14bit后，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16FLS_FRS_StandardThresholdTableForPSL[SNS_PSL_DIS_TOTAL_NUM] = 
{
    50000u,   /* [0,10cm] */
    50000u,   /* (10,20cm] */
    50000u,   /* (10,15cm] */
    35000u,   /* (15,20cm] */
    18000u,   /* (20,25cm] */
    18000u,   /* (25,30cm] */
    13000u,   /* (30,35cm] */
    12000u,   /* (35,40cm] */
    10000u,   /* (40,45cm] */
    8000u,    /* (45,50cm] */

    3500u,    /* (50,55cm] */
    3500u,    /* (55,60cm] */
    3500u,    /* (60,65cm] */
    3500u,    /* (65,70cm] */
    3500u,    /* (70,75cm] */
    3500u,    /* (75,80cm] */
    3500u,    /* (80,85cm] */
    3500u,    /* (85,90cm] */
    3500u,    /* (90,95cm] */
    2300u,    /* (95,100cm] */

    2300u,    /* (100,105cm] */
    2200u,    /* (105,110cm] */
    2000u,    /* (110,115cm] */
    1800u,    /* (115,120cm] */
    1800u,    /* (120,125cm] */
    1600u,    /* (125,130cm] */
    1600u,    /* (130,135cm] */
    1500u,    /* (135,140cm] */
    1400u,    /* (140,145cm] */
    1400u,    /* (145,150cm] */

    1400u,    /* (150,155cm] */
    1350u,    /* (155,160cm] */
    1350u,    /* (160,165cm] */
    1300u,    /* (165,170cm] */
    1300u,    /* (170,175cm] */
    1250u,    /* (175,180cm] */
    1250u,    /* (180,185cm] */
    1200u,    /* (185,190cm] */
    1200u,    /* (190,195cm] */
    1200u,    /* (195,200cm] */

    950u,    /* (200,205cm] */
    900u,    /* (205,210cm] */
    700u,    /* (210,215cm] */
    700u,    /* (215,220cm] */
    650u,    /* (220,225cm] */
    650u,    /* (225,230cm] */
    600u,    /* (230,235cm] */
    600u,    /* (235,240cm] */
    550u,    /* (240,245cm] */
    500u,    /* (245,250cm] */

    500u,    /* (250,255cm] */
    500u,    /* (255,260cm] */
    500u,    /* (260,265cm] */
    500u,    /* (265,270cm] */
    500u,    /* (270,275cm] */
    500u,    /* (275,280cm] */
    300u,    /* (280,285cm] */
    300u,    /* (285,290cm] */
    300u,    /* (290,295cm] */
    300u,    /* (295,300cm] */

	300u,    /* (300,305cm] */
    300u,    /* (305,310cm] */
    300u,    /* (310,315cm] */
    300u,    /* (315,320cm] */
    300u,    /* (320,325cm] */
    300u,    /* (325,330cm] */
    300u,    /* (330,335cm] */
    300u,    /* (335,340cm] */
    300u,    /* (340,345cm] */
    300u,    /* (345,350cm] */

    300u,    /* (350,355cm] */
    300u,    /* (355,360cm] */
    300u,    /* (360,365cm] */
    300u,    /* (365,370cm] */
    300u,    /* (370,375cm] */
    300u,    /* (375,380cm] */
    300u,    /* (380,385cm] */
    300u,    /* (385,390cm] */
    300u,    /* (390,395cm] */
    300u,    /* (395,400cm] */
};

/* 扫频情况下，FLS和FRS阈值表，用于筛选有效障碍物回波，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16FLS_FRS_ChirpThresholdTableForPSL[SNS_PSL_DIS_TOTAL_NUM] = 
{
    65535u,    /* [0,5cm] */
    65535u,    /* (5,10cm] */
    65535u,    /* (10,15cm] */
    65535u,    /* (15,20cm] */
    20000u,    /* (20,25cm] */
    18000u,    /* (25,30cm] */
    16000u,    /* (30,35cm] */
    12000u,    /* (35,40cm] */
    11000u,    /* (40,45cm] */
    4500u,     /* (45,50cm] 扫频余震分叉，40cm以内暂定4500*/

    3000u,    /* (50,55cm] */
    3000u,    /* (55,60cm] */
    2500u,    /* (60,65cm] */
    2500u,    /* (65,70cm] */
    2500u,    /* (70,75cm] */
    2400u,    /* (75,80cm] */
    2100u,    /* (80,85cm] */
    2100u,    /* (85,90cm] */
    2000u,    /* (90,95cm] */
    2000u,    /* (95,100cm] */

    1900u,    /* (100,105cm] */
    1900u,    /* (105,110cm] */
    1800u,    /* (110,115cm] */
    1800u,    /* (115,120cm] */
    1800u,    /* (120,125cm] */
    1600u,    /* (125,130cm] */
    1600u,    /* (130,135cm] */
    1500u,    /* (135,140cm] */
    1400u,    /* (140,145cm] */
    1400u,    /* (145,150cm] */

    1300u,    /* (150,155cm] */
    1300u,    /* (155,160cm] */
    1250u,    /* (160,165cm] */
    1250u,    /* (165,170cm] */
    1200u,    /* (170,175cm] */
    1200u,    /* (175,180cm] */
    1100u,    /* (180,185cm] */
    1100u,    /* (185,190cm] */
    1000u,    /* (190,195cm] */
    1000u,    /* (195,200cm] */

    850u,    /* (200,205cm] */
    850u,    /* (205,210cm] */
    800u,    /* (210,215cm] */
    750u,    /* (215,220cm] */
    750u,    /* (220,225cm] */
    700u,    /* (225,230cm] */
    700u,    /* (230,235cm] */
    650u,    /* (235,240cm] */
    600u,    /* (240,245cm] */
    550u,    /* (245,250cm] */

    550u,    /* (250,255cm] */
    500u,    /* (255,260cm] */
    500u,    /* (260,265cm] */
    420u,    /* (265,270cm] */
    400u,    /* (270,275cm] */
    380u,    /* (280,285cm] */
    360u,    /* (285,290cm] */
    350u,    /* (290,295cm] */
    350u,    /* (295,300cm] */
    
    330u,    /* (300,305cm] */
    300u,    /* (305,310cm] */
    300u,    /* (310,315cm] */
    300u,    /* (315,320cm] */
    300u,    /* (320,325cm] */
    300u,    /* (325,330cm] */
    300u,    /* (330,335cm] */
    300u,    /* (335,340cm] */
    300u,    /* (340,345cm] */
    300u,    /* (345,350cm] */


    300u,    /* (350,355cm] */
    300u,    /* (355,360cm] */
    300u,    /* (360,365cm] */
    300u,    /* (365,370cm] */
    300u,    /* (370,375cm] */
    300u,    /* (375,380cm] */
    300u,    /* (380,385cm] */
    300u,    /* (385,390cm] */
    300u,    /* (390,395cm] */
    300u     /* (395,400cm] */
};


/* 定频情况下，RLS和RRS阈值表，用于筛选有效障碍物回波，定频模式修改为14bit后，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16RLS_RRS_StandardThresholdTableForPSL[SNS_PSL_DIS_TOTAL_NUM] = 
{
    5000u,    /* [0,5cm] */
    5000u,    /* (5,10cm] */
    5000u,    /* (10,15cm] */
    5000u,    /* (15,20cm] */
    4000u,    /* (20,25cm] */
    4000u,    /* (25,30cm] */
    3000u,    /* (30,35cm] */
    3000u,    /* (35,40cm] */
    2500u,    /* (40,45cm] */
    2500u,    /* (45,50cm] */

    2000u,    /* (50,55cm] */
    2000u,    /* (55,60cm] */
    1800u,    /* (60,65cm] */
    1800u,    /* (65,70cm] */
    1600u,    /* (70,75cm] */
    1600u,    /* (75,80cm] */
    1400u,    /* (80,85cm] */
    1400u,    /* (85,90cm] */
    1200u,    /* (90,95cm] */
    1200u,    /* (95,100cm] */

    1100u,    /* (100,105cm] */
    1100u,    /* (105,110cm] */
    1000u,    /* (110,115cm] */
    1000u,    /* (115,120cm] */
    900u,    /* (120,125cm] */
    900u,    /* (125,130cm] */
    800u,    /* (130,135cm] */
    800u,    /* (135,140cm] */
    750u,    /* (140,145cm] */
    750u,    /* (145,150cm] */

    700u,    /* (150,155cm] */
    700u,    /* (155,160cm] */
    650u,    /* (160,165cm] */
    650u,    /* (165,170cm] */
    650u,    /* (170,175cm] */
    650u,    /* (175,180cm] */
    620u,    /* (180,185cm] */
    620u,    /* (185,190cm] */
    600u,    /* (190,195cm] */
    600u,    /* (195,200cm] */

    600u,    /* (200,205cm] */
    600u,    /* (205,210cm] */
    600u,    /* (210,215cm] */
    600u,    /* (215,220cm] */
    550u,    /* (220,225cm] */
    550u,    /* (225,230cm] */
    550u,    /* (230,235cm] */
    550u,    /* (235,240cm] */
    500u,    /* (240,245cm] */
    500u,    /* (245,250cm] */

    500u,    /* (250,255cm] */
    500u,    /* (255,260cm] */
    500u,    /* (260,265cm] */
    500u,    /* (265,270cm] */
    500u,    /* (270,275cm] */
    500u,    /* (275,280cm] */
    500u,    /* (280,285cm] */
    500u,    /* (285,290cm] */
    500u,    /* (290,295cm] */
    500u,    /* (295,300cm] */

	500u,    /* (300,305cm] */
    500u,    /* (305,310cm] */
    500u,    /* (310,315cm] */
    500u,    /* (315,320cm] */
    500u,    /* (320,325cm] */
    500u,    /* (325,330cm] */
    500u,    /* (330,335cm] */
    500u,    /* (335,340cm] */
    500u,    /* (340,345cm] */
    500u,    /* (345,350cm] */

    500u,    /* (350,355cm] */
    500u,    /* (355,360cm] */
    500u,    /* (360,365cm] */
    500u,    /* (365,370cm] */
    500u,    /* (370,375cm] */
    500u,    /* (375,380cm] */
    500u,    /* (380,385cm] */
    500u,    /* (385,390cm] */
    500u,    /* (390,395cm] */
    500u,    /* (395,400cm] */
};

/* 扫频情况下，RLS和RRS阈值表，用于筛选有效障碍物回波，统一使用uint16类型，软件使用的即为实际值 */
const uint16 Gu16RLS_RRS_ChirpThresholdTableForPSL[SNS_PSL_DIS_TOTAL_NUM] = 
{
    65535u,    /* [0,5cm] */
    65535u,    /* (5,10cm] */
    65535u,    /* (10,15cm] */
    65535u,    /* (15,20cm] */
    20000u,    /* (20,25cm] */
    18000u,    /* (25,30cm] */
    16000u,    /* (30,35cm] */
    12000u,    /* (35,40cm] */
    2000u,     /* (40,45cm] */
    2000u,     /* (45,50cm] 扫频余震分叉，40cm以内暂定4500*/

    2000u,    /* (50,55cm] */
    2000u,    /* (55,60cm] */
    1500u,    /* (60,65cm] */
    1500u,    /* (65,70cm] */
    1500u,    /* (70,75cm] */
    1500u,    /* (75,80cm] */
    1500u,    /* (80,85cm] */
    1000u,    /* (85,90cm] */
    1000u,    /* (90,95cm] */
    1000u,    /* (95,100cm] */

    900u,    /* (100,105cm] */
    900u,    /* (105,110cm] */
    900u,    /* (110,115cm] */
    900u,    /* (115,120cm] */
    850u,    /* (120,125cm] */
    850u,    /* (125,130cm] */
    800u,    /* (130,135cm] */
    800u,    /* (135,140cm] */
    750u,    /* (140,145cm] */
    750u,    /* (145,150cm] */

    700u,    /* (150,155cm] */
    700u,    /* (155,160cm] */
    650u,    /* (160,165cm] */
    650u,    /* (165,170cm] */
    600u,    /* (170,175cm] */
    600u,    /* (175,180cm] */
    550u,    /* (180,185cm] */
    550u,    /* (185,190cm] */
    500u,    /* (190,195cm] */
    500u,    /* (195,200cm] */

    450u,    /* (200,205cm] */
    450u,    /* (205,210cm] */
    400u,    /* (210,215cm] */
    400u,    /* (215,220cm] */
    350u,    /* (220,225cm] */
    350u,    /* (225,230cm] */
    330u,    /* (230,235cm] */
    330u,    /* (235,240cm] */
    300u,    /* (240,245cm] */
    300u,    /* (245,250cm] */

    250u,    /* (250,255cm] */
    250u,    /* (255,260cm] */
    220u,    /* (260,265cm] */
    220u,    /* (265,270cm] */
    200u,    /* (270,275cm] */
    200u,    /* (275,280cm] */
    200u,    /* (280,285cm] */
    200u,    /* (285,290cm] */
    200u,    /* (290,295cm] */
    200u,    /* (295,300cm] */

    180u,    /* (300,305cm] */
    180u,    /* (305,310cm] */
    170u,    /* (310,315cm] */
    170u,    /* (315,320cm] */
    160u,    /* (320,325cm] */
    160u,    /* (325,330cm] */
    150u,    /* (330,335cm] */
    150u,    /* (335,340cm] */
    150u,    /* (340,345cm] */
    150u,    /* (345,350cm] */

    150u,    /* (350,355cm] */
    150u,    /* (355,360cm] */
    300u,    /* (360,365cm] */
    300u,    /* (365,370cm] */
    300u,    /* (370,375cm] */
    300u,    /* (375,380cm] */
    300u,    /* (380,385cm] */
    300u,    /* (385,390cm] */
    300u,    /* (390,395cm] */
    300u     /* (395,400cm] */
};

/* 定频模式下障碍物回波高度判断障碍物类型的阈值 */
const PSLSnsCalibHeightType GStrObjJudgeStandardThresholdTableForPSL[SNS_PSL_DIS_HIGH_TOTAL_NUM] = 
{
    /* 需要根据路砍等低矮障碍物的高度过滤10~80cm的PVC阈值 */
    [SNS_PSL_DIS_HIGH_10cm] = 
    {
        .u16PVC_PileHeight = 2500,
        .u16BigWallHeight = 3500,
        .u16SecondWallHeight = 2000,
        .u16CurbHeight = 1400,
    },
    [SNS_PSL_DIS_HIGH_20cm] = 
    {
        .u16PVC_PileHeight = 2500,
        .u16BigWallHeight = 3500,
        .u16SecondWallHeight = 2000,
        .u16CurbHeight = 1400,
    },
    [SNS_PSL_DIS_HIGH_30cm] = 
    {
        .u16PVC_PileHeight = 2500,
        .u16BigWallHeight = 3500,
        .u16SecondWallHeight = 2000,
        .u16CurbHeight = 1400,
    },
    [SNS_PSL_DIS_HIGH_40cm] = 
    {
        .u16PVC_PileHeight = 2500,
        .u16BigWallHeight = 3500,
        .u16SecondWallHeight = 2000,
        .u16CurbHeight = 1400,
    },
    [SNS_PSL_DIS_HIGH_50cm] = 
    {
        .u16PVC_PileHeight = 3000,
        .u16BigWallHeight = 3000,
        .u16SecondWallHeight = 2000,
        .u16CurbHeight = 1400,
    },
    [SNS_PSL_DIS_HIGH_60cm] = 
    {
        .u16PVC_PileHeight = 2000,
        .u16BigWallHeight = 2800,
        .u16SecondWallHeight = 1800,
        .u16CurbHeight = 1400,
    },
    [SNS_PSL_DIS_HIGH_70cm] = 
    {
        .u16PVC_PileHeight = 1800,
        .u16BigWallHeight = 2500,
        .u16SecondWallHeight = 1600,
        .u16CurbHeight = 1400,
    },
    [SNS_PSL_DIS_HIGH_80cm] = 
    {
        .u16PVC_PileHeight = 1600,
        .u16BigWallHeight = 2000,
        .u16SecondWallHeight = 1500,
        .u16CurbHeight = 1400,
    },
    [SNS_PSL_DIS_HIGH_90cm] = 
    {
        .u16PVC_PileHeight = 1400,
        .u16BigWallHeight = 1900,
        .u16SecondWallHeight = 1500,
        .u16CurbHeight = 1400,
    },
    [SNS_PSL_DIS_HIGH_100cm] = 
    {
        .u16PVC_PileHeight = 1200,
        .u16BigWallHeight = 1850,
        .u16SecondWallHeight = 1450,
        .u16CurbHeight = 1400,
    },
    [SNS_PSL_DIS_HIGH_110cm] = 
    {
        .u16PVC_PileHeight = 1100,
        .u16BigWallHeight = 1800,
        .u16SecondWallHeight = 1400,
        .u16CurbHeight = 1400,
    },
    [SNS_PSL_DIS_HIGH_120cm] = 
    {
        .u16PVC_PileHeight = 1000,
        .u16BigWallHeight = 1800,
        .u16SecondWallHeight = 1300,
        .u16CurbHeight = 1400,
    },
    [SNS_PSL_DIS_HIGH_130cm] = 
    {
        .u16PVC_PileHeight = 900,
        .u16BigWallHeight = 1750,
        .u16SecondWallHeight = 1300,
        .u16CurbHeight = 1400,
    },
    [SNS_PSL_DIS_HIGH_140cm] = 
    {
        .u16PVC_PileHeight = 900,
        .u16BigWallHeight = 1700,
        .u16SecondWallHeight = 1300,
        .u16CurbHeight = 1400,
    },
    [SNS_PSL_DIS_HIGH_150cm] = 
    {
        .u16PVC_PileHeight = 800,
        .u16BigWallHeight = 1650,
        .u16SecondWallHeight = 1300,
        .u16CurbHeight = 1400,
    },
    [SNS_PSL_DIS_HIGH_160cm] = 
    {
        .u16PVC_PileHeight = 750,
        .u16BigWallHeight = 1600,
        .u16SecondWallHeight = 1300,
        .u16CurbHeight = 1400,
    },
    [SNS_PSL_DIS_HIGH_170cm] = 
    {
        .u16PVC_PileHeight = 700,
        .u16BigWallHeight = 1550,
        .u16SecondWallHeight = 1300,
        .u16CurbHeight = 1400,
    },
    [SNS_PSL_DIS_HIGH_180cm] = 
    {
        .u16PVC_PileHeight = 650,
        .u16BigWallHeight = 1500,
        .u16SecondWallHeight = 1300,
        .u16CurbHeight = 1400,
    },
    [SNS_PSL_DIS_HIGH_190cm] = 
    {
        .u16PVC_PileHeight = 620,
        .u16BigWallHeight = 1450,
        .u16SecondWallHeight = 1250,
        .u16CurbHeight = 1400,
    },
    [SNS_PSL_DIS_HIGH_200cm] = 
    {
        .u16PVC_PileHeight = 600,
        .u16BigWallHeight = 1400,
        .u16SecondWallHeight = 1200,
        .u16CurbHeight = 1400,
    },
    [SNS_PSL_DIS_HIGH_210cm] = 
    {
        .u16PVC_PileHeight = 600,
        .u16BigWallHeight = 1350,
        .u16SecondWallHeight = 1150,
        .u16CurbHeight = 1400,
    },
    [SNS_PSL_DIS_HIGH_220cm] = 
    {
        .u16PVC_PileHeight = 600,
        .u16BigWallHeight = 1300,
        .u16SecondWallHeight = 1100,
        .u16CurbHeight = 1400,
    },
    [SNS_PSL_DIS_HIGH_230cm] = 
    {
        .u16PVC_PileHeight = 550,
        .u16BigWallHeight = 1250,
        .u16SecondWallHeight = 1050,
        .u16CurbHeight = 1450,
    },
    [SNS_PSL_DIS_HIGH_240cm] = 
    {
        .u16PVC_PileHeight = 550,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_PSL_DIS_HIGH_250cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_PSL_DIS_HIGH_260cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_PSL_DIS_HIGH_270cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_PSL_DIS_HIGH_280cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_PSL_DIS_HIGH_290cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_PSL_DIS_HIGH_300cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },    
    [SNS_PSL_DIS_HIGH_310cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_PSL_DIS_HIGH_320cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_PSL_DIS_HIGH_330cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_PSL_DIS_HIGH_340cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_PSL_DIS_HIGH_350cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_PSL_DIS_HIGH_360cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_PSL_DIS_HIGH_370cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_PSL_DIS_HIGH_380cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_PSL_DIS_HIGH_390cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    },
    [SNS_PSL_DIS_HIGH_400cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 1500,
    }, 

};


/* 扫频模式下障碍物回波高度判断障碍物类型的阈值 */
const PSLSnsCalibHeightType GStrObjJudgeChirpThresholdTableForPSL[SNS_PSL_DIS_HIGH_TOTAL_NUM] = 
{
    [SNS_PSL_DIS_HIGH_10cm] = 
    {
        .u16PVC_PileHeight = 4500,
        .u16BigWallHeight = 5000,
        .u16SecondWallHeight = 1200,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_20cm] = 
    {
        .u16PVC_PileHeight = 4500,
        .u16BigWallHeight = 5000,
        .u16SecondWallHeight = 1200,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_30cm] = 
    {
        .u16PVC_PileHeight = 4500,
        .u16BigWallHeight = 5000,
        .u16SecondWallHeight = 1200,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_40cm] = 
    {
        .u16PVC_PileHeight = 4500,
        .u16BigWallHeight = 5000,
        .u16SecondWallHeight = 1200,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_50cm] = 
    {
        .u16PVC_PileHeight = 4500,
        .u16BigWallHeight = 5000,
        .u16SecondWallHeight = 1200,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_60cm] = 
    {
        .u16PVC_PileHeight = 3000,
        .u16BigWallHeight = 3200,
        .u16SecondWallHeight = 1200,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_70cm] = 
    {
        .u16PVC_PileHeight = 2500,
        .u16BigWallHeight = 3000,
        .u16SecondWallHeight = 1100,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_80cm] = 
    {
        .u16PVC_PileHeight = 1500,
        .u16BigWallHeight = 2800,
        .u16SecondWallHeight = 1100,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_90cm] = 
    {
        .u16PVC_PileHeight = 1000,
        .u16BigWallHeight = 2700,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_100cm] = 
    {
        .u16PVC_PileHeight = 900,
        .u16BigWallHeight = 2600,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_110cm] = 
    {
        .u16PVC_PileHeight = 900,
        .u16BigWallHeight = 2500,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_120cm] = 
    {
        .u16PVC_PileHeight = 850,
        .u16BigWallHeight = 2400,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_130cm] = 
    {
        .u16PVC_PileHeight = 800,
        .u16BigWallHeight = 2300,
        .u16SecondWallHeight = 1100,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_140cm] = 
    {
        .u16PVC_PileHeight = 800,
        .u16BigWallHeight = 2200,
        .u16SecondWallHeight = 1100,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_150cm] = 
    {
        .u16PVC_PileHeight = 750,
        .u16BigWallHeight = 2100,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_160cm] = 
    {
        .u16PVC_PileHeight = 700,
        .u16BigWallHeight = 2000,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_170cm] = 
    {
        .u16PVC_PileHeight = 650,
        .u16BigWallHeight = 1800,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_180cm] = 
    {
        .u16PVC_PileHeight = 600,
        .u16BigWallHeight = 2000,
        .u16SecondWallHeight = 1000,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_190cm] = 
    {
        .u16PVC_PileHeight = 550,
        .u16BigWallHeight = 2100,
        .u16SecondWallHeight = 900,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_200cm] = 
    {
        .u16PVC_PileHeight = 500,
        .u16BigWallHeight = 2300,
        .u16SecondWallHeight = 900,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_210cm] = 
    {
        .u16PVC_PileHeight = 450,
        .u16BigWallHeight = 2300,
        .u16SecondWallHeight = 900,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_220cm] = 
    {
        .u16PVC_PileHeight = 400,
        .u16BigWallHeight = 2300,
        .u16SecondWallHeight = 900,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_230cm] = 
    {
        .u16PVC_PileHeight = 350,
        .u16BigWallHeight = 2300,
        .u16SecondWallHeight = 900,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_240cm] = 
    {
        .u16PVC_PileHeight = 330,
        .u16BigWallHeight = 2200,
        .u16SecondWallHeight = 900,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_250cm] = 
    {
        .u16PVC_PileHeight = 300,
        .u16BigWallHeight = 2100,
        .u16SecondWallHeight = 900,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_260cm] = 
    {
        .u16PVC_PileHeight = 250,
        .u16BigWallHeight = 2000,
        .u16SecondWallHeight = 800,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_270cm] = 
    {
        .u16PVC_PileHeight = 250,
        .u16BigWallHeight = 1800,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_280cm] = 
    {
        .u16PVC_PileHeight = 200,
        .u16BigWallHeight = 1700,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_290cm] = 
    {
        .u16PVC_PileHeight = 200,
        .u16BigWallHeight = 1550,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_300cm] = 
    {
        .u16PVC_PileHeight = 180,
        .u16BigWallHeight = 1500,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 900,
    },
    /* 待补充 */
    [SNS_PSL_DIS_HIGH_310cm] = 
    {
        .u16PVC_PileHeight = 170,
        .u16BigWallHeight = 1350,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_320cm] = 
    {
        .u16PVC_PileHeight = 160,
        .u16BigWallHeight = 1300,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_330cm] = 
    {
        .u16PVC_PileHeight = 150,
        .u16BigWallHeight = 1250,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_340cm] = 
    {
        .u16PVC_PileHeight = 150,
        .u16BigWallHeight = 1200,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_350cm] = 
    {
        .u16PVC_PileHeight = 150,
        .u16BigWallHeight = 1150,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_360cm] = 
    {
        .u16PVC_PileHeight = 150,
        .u16BigWallHeight = 1100,
        .u16SecondWallHeight = 700,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_370cm] = 
    {
        .u16PVC_PileHeight = 300,
        .u16BigWallHeight = 1050,
        .u16SecondWallHeight = 650,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_380cm] = 
    {
        .u16PVC_PileHeight = 300,
        .u16BigWallHeight = 1000,
        .u16SecondWallHeight = 600,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_390cm] = 
    {
        .u16PVC_PileHeight = 300,
        .u16BigWallHeight = 950,
        .u16SecondWallHeight = 550,
        .u16CurbHeight = 900,
    },
    [SNS_PSL_DIS_HIGH_400cm] = 
    {
        .u16PVC_PileHeight = 300,
        .u16BigWallHeight = 900,
        .u16SecondWallHeight = 500,
        .u16CurbHeight = 900,
    }, 
};

/* 在RAM中存储的数据，用于实际应用使用 */
uint16 Gu16FLS_FRS_StandardThresholdTableInRAMForPSL[SNS_PSL_DIS_TOTAL_NUM];
uint16 Gu16FLS_FRS_ChirpThresholdTableInRAMForPSL[SNS_PSL_DIS_TOTAL_NUM];



uint16 Gu16RLS_RRS_StandardThresholdTableInRAMForPSL[SNS_PSL_DIS_TOTAL_NUM];
uint16 Gu16RLS_RRS_ChirpThresholdTableInRAMForPSL[SNS_PSL_DIS_TOTAL_NUM];

/* 在RAM中定义用于障碍物类型区分的阈值数据 */

PSLSnsCalibHeightType GStrPSLObjJudgeStandardThresholdTableInRAM[SNS_PSL_DIS_HIGH_TOTAL_NUM];
PSLSnsCalibHeightType GStrPSLObjJudgeChirpThresholdTableInRAM[SNS_PSL_DIS_HIGH_TOTAL_NUM];

const uint16 *Gpu16PSLSnsThresholdTable[PDC_SNS_GROUP_NUM][2] = 
{
    [PDC_SNS_GROUP_FRONT] = 
    {
        Gu16FLS_FRS_StandardThresholdTableInRAMForPSL,
        Gu16FLS_FRS_ChirpThresholdTableInRAMForPSL,
    },
    
    [PDC_SNS_GROUP_REAR] = 
    {
        Gu16RLS_RRS_StandardThresholdTableInRAMForPSL,
        Gu16RLS_RRS_ChirpThresholdTableInRAMForPSL,
    }
};

/******************************************************************************/
/******************************************************************************/
/****************************** Private Function ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/******************************* Public Function ******************************/
/******************************************************************************/
/******************************************************************************/
void CopySnsPSLCalibWidthAndThresholdToRam(void)
{
    memcpy(&Gu16FLS_FRS_StandardThresholdTableInRAMForPSL[0],&Gu16FLS_FRS_StandardThresholdTableForPSL[0],SNS_PSL_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16RLS_RRS_StandardThresholdTableInRAMForPSL[0],&Gu16RLS_RRS_StandardThresholdTableForPSL[0],SNS_PSL_DIS_TOTAL_NUM*sizeof(uint16));
  
    memcpy(&Gu16FLS_FRS_ChirpThresholdTableInRAMForPSL[0],&Gu16FLS_FRS_ChirpThresholdTableForPSL[0],SNS_PSL_DIS_TOTAL_NUM*sizeof(uint16));
    memcpy(&Gu16RLS_RRS_ChirpThresholdTableInRAMForPSL[0],&Gu16RLS_RRS_ChirpThresholdTableForPSL[0],SNS_PSL_DIS_TOTAL_NUM*sizeof(uint16));
     
    memcpy(&GStrPSLObjJudgeStandardThresholdTableInRAM[0],&GStrObjJudgeStandardThresholdTableForPSL[0],SNS_PSL_DIS_HIGH_TOTAL_NUM*sizeof(PSLSnsCalibHeightType));
    memcpy(&GStrPSLObjJudgeChirpThresholdTableInRAM[0],&GStrObjJudgeChirpThresholdTableForPSL[0],SNS_PSL_DIS_HIGH_TOTAL_NUM*sizeof(PSLSnsCalibHeightType));
}





