/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: STBCDrv.h
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/
#ifndef __STBC_DRV_H
#define __STBC_DRV_H

/**********************************宏定义**************************************/
/* Cycle time */
#define _STBC_WAITTIME                                          (0x5U)
#define _STBC_FACTOR_DEFAULT_VALUE                              (0xFFFFFFFFUL)

/*
    Wake up factor register (WUF0)
*/
/* Indicates the generation of a wake-up event */
#define _STBC_WUF0_CLEAR_100PIN                                 (0xF3FFFFE7UL) /* Wake-up event is not generated */
#define _STBC_WUF0_CLEAR                                        (0xFFFFFFE7UL) /* Wake-up event is not generated */
/*
Wake up factor register (WUFC1)
*/
/* Indicates the generation of a wake-up event */
#define _STBC_WUF1_CLEAR_100PIN	                                (0x00000F00UL) /* Wake-up event is not generated */
#define _STBC_WUF1_CLEAR                                        (0x00000FFFUL) /* Wake-up event is not generated */
/*
    Wake up factor register (WUF_ISO0)
*/
/* Indicates the generation of a wake-up event */
#define _STBC_WUF_ISO0_CLEAR                                    (0x00000FFEUL) /* Wake-up event is not generated */
/*
    Wake up factor register (WUF20)
*/
/* Indicates the generation of a wake-up event */
#define _STBC_WUF20_CLEAR_100PIN                                (0x003C1FFFUL) /* Wake-up event is not generated */
#define _STBC_WUF20_CLEAR                                       (0x003FFFFFUL) /* Wake-up event is not generated */
/*
    Wake up factor register (WUF0)
*/
/* Indicates the generation of a wake-up event (WUF00) */
#define _STBC_WUF0_FACTOR_TNMI                                  (0xFFFFFFFEUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF01) */
#define _STBC_WUF0_FACTOR_WDTA0NMI                              (0xFFFFFFFDUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF02) */
#define _STBC_WUF0_FACTOR_INTLVIL                               (0xFFFFFFFBUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF05) */
#define _STBC_WUF0_FACTOR_INTP0                                 (0xFFFFFFDFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF06) */
#define _STBC_WUF0_FACTOR_INTP1                                 (0xFFFFFFBFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF07) */
#define _STBC_WUF0_FACTOR_INTP2                                 (0xFFFFFF7FUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF08) */
#define _STBC_WUF0_FACTOR_INTWDTA0                              (0xFFFFFEFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF09) */
#define _STBC_WUF0_FACTOR_INTP3                                 (0xFFFFFDFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF10) */
#define _STBC_WUF0_FACTOR_INTP4                                 (0xFFFFFBFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF11) */
#define _STBC_WUF0_FACTOR_INTP5                                 (0xFFFFF7FFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF12) */
#define _STBC_WUF0_FACTOR_INTP10                                (0xFFFFEFFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF13) */
#define _STBC_WUF0_FACTOR_INTP11                                (0xFFFFDFFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF14) */
#define _STBC_WUF0_FACTOR_WUTRG1                                (0xFFFFBFFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF15) */
#define _STBC_WUF0_FACTOR_INTTAUJ0I0                            (0xFFFF7FFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF16) */
#define _STBC_WUF0_FACTOR_INTTAUJ0I1                            (0xFFFEFFFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF17) */
#define _STBC_WUF0_FACTOR_INTTAUJ0I2                            (0xFFFDFFFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF18) */
#define _STBC_WUF0_FACTOR_INTTAUJ0I3                            (0xFFFBFFFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF19) */
#define _STBC_WUF0_FACTOR_WUTRG0                                (0xFFF7FFFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF20) */
#define _STBC_WUF0_FACTOR_INTP6                                 (0xFFEFFFFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF21) */
#define _STBC_WUF0_FACTOR_INTP7                                 (0xFFDFFFFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF22) */
#define _STBC_WUF0_FACTOR_INTP8                                 (0xFFBFFFFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF23) */
#define _STBC_WUF0_FACTOR_INTP12                                (0xFF7FFFFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF24) */
#define _STBC_WUF0_FACTOR_INTP9                                 (0xFEFFFFFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF25) */
#define _STBC_WUF0_FACTOR_INTP13                                (0xFDFFFFFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF26) */
#define _STBC_WUF0_FACTOR_INTP14                                (0xFBFFFFFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF27) */
#define _STBC_WUF0_FACTOR_INTP15                                (0xF7FFFFFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF28) */
#define _STBC_WUF0_FACTOR_INTRTCA01S                            (0xEFFFFFFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF29) */
#define _STBC_WUF0_FACTOR_INTRTCA0AL                            (0xDFFFFFFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF30) */
#define _STBC_WUF0_FACTOR_INTRTCA0R                             (0xBFFFFFFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF31) */
#define _STBC_WUF0_FACTOR_INTDCUTDI                             (0x7FFFFFFFUL) /* Wake-up event is generated */
/*
Wake up factor register (WUFMSK1)
*/
/* Indicates the generation of a wake-up event (WUF10) */
#define _STBC_WUF1_FACTOR_INTP16                                (0xFFFFFFFEUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF11) */
#define _STBC_WUF1_FACTOR_INTP17                                (0xFFFFFFFDUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF12) */
#define _STBC_WUF1_FACTOR_INTP18                                (0xFFFFFFFBUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF13) */
#define _STBC_WUF1_FACTOR_INTP19                                (0xFFFFFFF7UL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF14) */
#define _STBC_WUF1_FACTOR_INTP20                                (0xFFFFFFEFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF15) */
#define _STBC_WUF1_FACTOR_INTP21                                (0xFFFFFFDFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF16) */
#define _STBC_WUF1_FACTOR_INTP22                                (0xFFFFFFBFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF17) */
#define _STBC_WUF1_FACTOR_INTP23                                (0xFFFFFF7FUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF18) */
#define _STBC_WUF1_FACTOR_INTTAUJ2I0                            (0xFFFFFEFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF19) */
#define _STBC_WUF1_FACTOR_INTTAUJ2I1                            (0xFFFFFDFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF110) */
#define _STBC_WUF1_FACTOR_INTTAUJ2I2                            (0xFFFFFBFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF111) */
#define _STBC_WUF1_FACTOR_INTTAUJ2I3                            (0xFFFFF7FFUL) /* Wake-up event is generated */
/*
    Wake up factor register (WUF_ISO0)
*/
/* Indicates the generation of a wake-up event (WUF01) */
#define _STBC_WUF_ISO0_FACTOR_INTKR0                            (0xFFFFFFFDUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF02) */
#define _STBC_WUF_ISO0_FACTOR_INTRCANGRECC0                     (0xFFFFFFFBUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF03) */
#define _STBC_WUF_ISO0_FACTOR_INTRCAN0REC                       (0xFFFFFFF7UL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF04) */
#define _STBC_WUF_ISO0_FACTOR_INTRCAN1REC                       (0xFFFFFFEFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF05) */
#define _STBC_WUF_ISO0_FACTOR_INTRCAN2REC                       (0xFFFFFFDFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF06) */
#define _STBC_WUF_ISO0_FACTOR_INTRCAN3REC                       (0xFFFFFFBFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF07) */
#define _STBC_WUF_ISO0_FACTOR_INTRCAN4REC                       (0xFFFFFF7FUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF08) */
#define _STBC_WUF_ISO0_FACTOR_INTRCAN5REC                       (0xFFFFFEFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF10) */
#define _STBC_WUF_ISO0_FACTOR_INTRCAN6REC                       (0xFFFFFBFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF11) */
#define _STBC_WUF_ISO0_FACTOR_INTRCAN7REC                       (0xFFFFF7FFUL) /* Wake-up event is generated */
/*
    Wake up factor register (WUF20)
*/
/* Indicates the generation of a wake-up event (WUF00) */
#define _STBC_WUF20_FACTOR_INTADCA0I0                           (0xFFFFFFFEUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF01) */
#define _STBC_WUF20_FACTOR_INTADCA0I1                           (0xFFFFFFFDUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF02) */
#define _STBC_WUF20_FACTOR_INTADCA0I2                           (0xFFFFFFFBUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF03) */
#define _STBC_WUF20_FACTOR_INTRLIN30                            (0xFFFFFFF7UL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF04) */
#define _STBC_WUF20_FACTOR_INTTAUJ0I0                           (0xFFFFFFEFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF05) */
#define _STBC_WUF20_FACTOR_INTTAUJ0I1                           (0xFFFFFFDFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF06) */
#define _STBC_WUF20_FACTOR_INTTAUJ0I2                           (0xFFFFFFBFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF07) */
#define _STBC_WUF20_FACTOR_INTTAUJ0I3                           (0xFFFFFF7FUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF08) */
#define _STBC_WUF20_FACTOR_INTRLIN31                            (0xFFFFFEFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF09) */
#define _STBC_WUF20_FACTOR_INTRLIN32                            (0xFFFFFDFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF10) */
#define _STBC_WUF20_FACTOR_INTRTCA01S                           (0xFFFFFBFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF11) */
#define _STBC_WUF20_FACTOR_INTRTCA0AL                           (0xFFFFF7FFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF12) */
#define _STBC_WUF20_FACTOR_INTRTCA0R                            (0xFFFFEFFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF13) */
#define _STBC_WUF20_FACTOR_INTRLIN33                            (0xFFFFDFFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF14) */
#define _STBC_WUF20_FACTOR_INTRLIN34                            (0xFFFFBFFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF15) */
#define _STBC_WUF20_FACTOR_INTRLIN35                            (0xFFFF7FFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF16) */
#define _STBC_WUF20_FACTOR_INTRLIN36                            (0xFFFEFFFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF17) */
#define _STBC_WUF20_FACTOR_INTRLIN37                            (0xFFFDFFFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF18) */
#define _STBC_WUF20_FACTOR_INTTAUJ2I0                           (0xFFFBFFEFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF19) */
#define _STBC_WUF20_FACTOR_INTTAUJ2I1                           (0xFFF7FFDFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF20) */
#define _STBC_WUF20_FACTOR_INTTAUJ2I2                           (0xFFEFFFFFUL) /* Wake-up event is generated */
/* Indicates the generation of a wake-up event (WUF21) */
#define _STBC_WUF20_FACTOR_INTTAUJ2I3                           (0xFFDFFFFFUL) /* Wake-up event is generated */

/*
    Power save control register (STBC0PSC)
*/
/* Power save control (STBC0DISTRG) */
#define _STBC_DEEP_STOP_MODE_DISABLE                            (0x00000000UL) /* Writing 0 has no effect */
#define _STBC_DEEP_STOP_MODE_ENTERED                            (0x00000002UL) /* DeepSTOP mode is entered */

/*
    Power stop trigger register (STBC0STPT)
*/
/* Power stop trigger (STBC0STPTRG) */
#define _STBC_STOP_MODE_DISABLE                                 (0x00000000UL) /* Writing 0 has no effect */
#define _STBC_STOP_MODE_ENTERED                                 (0x00000001UL) /* Stop mode is entered */

/*
    reset factor clear register (RESFC)
*/
/* Reset flag clear by DeepSTOP mode (RESFC10) */
#define _RESFC_RESET_FLAG_CLEAR                                 (0x00000400UL) /* Clear reset flag */

/*******************************全局函数声明***********************************/
extern void STBC_EnterStopMode(void);
extern void CheckSysResetFactor(void);

#endif
