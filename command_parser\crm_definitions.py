"""
CRM命令定义模块，存储所有CRM命令的定义
"""

class CRMCommand:
    """表示一个CRM命令"""

    def __init__(self, code, name, data_length):
        """
        初始化CRM命令

        Args:
            code: 命令代码
            name: 命令名称
            data_length: 数据长度（字节）
        """
        self.code = code
        self.name = name
        self.data_length = data_length
        self.bit_fields = []

    def add_bit_field(self, name, msb, lsb, description=""):
        """
        添加位域

        Args:
            name: 位域名称
            msb: 最高有效位
            lsb: 最低有效位
            description: 位域描述
        """
        self.bit_fields.append({
            "name": name,
            "msb": msb,
            "lsb": lsb,
            "bits": msb - lsb + 1,
            "description": description
        })

    def parse_data(self, data):
        """
        解析数据，提取所有位域的值

        Args:
            data: 字节列表或字节数组

        Returns:
            包含所有位域值的字典
        """
        result = {"command": self.name, "code": self.code}

        # 将字节数组转换为位数组
        bits = []
        for byte in data:
            for i in range(7, -1, -1):
                bits.append((byte >> i) & 1)

        # 提取每个位域的值
        for field in self.bit_fields:
            msb = field["msb"]
            lsb = field["lsb"]
            value = 0
            # 从高位到低位提取位域值
            for i in range(msb, lsb - 1, -1):
                if i < len(bits):
                    value = (value << 1) | bits[len(bits) - 1 - i]
            result[field["name"]] = value

        return result


# 定义所有CRM命令
CRM_COMMANDS = {}

# 初始化CRM命令定义
def init_crm_commands():
    """初始化所有CRM命令定义"""
    # CRM命令F (MEASURE)
    cmd_f = CRMCommand(0xF, "MEASURE", 10)
    cmd_f.add_bit_field("sid", 79, 76, "从节点标识符 | Slave node identifier")
    cmd_f.add_bit_field("command", 75, 72, "命令代码 | Command code")
    cmd_f.add_bit_field("mode", 67, 65, "模式[2:0] | Mode[2:0]")
    cmd_f.add_bit_field("SID1 - BEH_CH_L", 63, 62, "SID1行为 - 低通道行为[1:0]")
    cmd_f.add_bit_field("SID1 - BEH_CH_H", 61, 60, "SID1行为 - 高通道行为[1:0]")
    cmd_f.add_bit_field("SID2 - BEH_CH_L", 59, 58, "SID2行为 - 低通道行为[1:0]")
    cmd_f.add_bit_field("SID2 - BEH_CH_H", 57, 56, "SID2行为 - 高通道行为[1:0]")
    cmd_f.add_bit_field("SID3 - BEH_CH_L", 55, 54, "SID3行为 - 低通道行为[1:0]")
    cmd_f.add_bit_field("SID3 - BEH_CH_H", 53, 52, "SID3行为 - 高通道行为[1:0]")
    cmd_f.add_bit_field("SID4 - BEH_CH_L", 51, 50, "SID4行为 - 低通道行为[1:0]")
    cmd_f.add_bit_field("SID4 - BEH_CH_H", 49, 48, "SID4行为 - 高通道行为[1:0]")
    cmd_f.add_bit_field("SID5 - BEH_CH_L", 47, 46, "SID5行为 - 低通道行为[1:0]")
    cmd_f.add_bit_field("SID5 - BEH_CH_H", 45, 44, "SID5行为 - 高通道行为[1:0]")
    cmd_f.add_bit_field("SID6 - BEH_CH_L", 43, 42, "SID6行为 - 低通道行为[1:0]")
    cmd_f.add_bit_field("SID6 - BEH_CH_H", 41, 40, "SID6行为 - 高通道行为[1:0]")
    # cmd_f.add_bit_field("sid1_beh", 63, 60, "SID1行为 - 低通道行为[1:0]，高通道行为[1:0] | SID1 - BEH_CH_L[1:0], BEH_CH_H[1:0]")
    # cmd_f.add_bit_field("sid2_beh", 59, 56, "SID2行为 - 低通道行为[1:0]，高通道行为[1:0] | SID2 - BEH_CH_L[1:0], BEH_CH_H[1:0]")
    # cmd_f.add_bit_field("sid3_beh", 55, 52, "SID3行为 - 低通道行为[1:0]，高通道行为[1:0] | SID3 - BEH_CH_L[1:0], BEH_CH_H[1:0]")
    # cmd_f.add_bit_field("sid4_beh", 51, 48, "SID4行为 - 低通道行为[1:0]，高通道行为[1:0] | SID4 - BEH_CH_L[1:0], BEH_CH_H[1:0]")
    # cmd_f.add_bit_field("sid5_beh", 47, 44, "SID5行为 - 低通道行为[1:0]，高通道行为[1:0] | SID5 - BEH_CH_L[1:0], BEH_CH_H[1:0]")
    # cmd_f.add_bit_field("sid6_beh", 43, 40, "SID6行为 - 低通道行为[1:0]，高通道行为[1:0] | SID6 - BEH_CH_L[1:0], BEH_CH_H[1:0]")
    cmd_f.add_bit_field("doppler_rx_inner", 39, 38, "内部多普勒接收[1:0] | DOPPLER_RX_INNER[1:0]")
    cmd_f.add_bit_field("doppler_tx_inner", 37, 34, "内部多普勒发送[3:0] | DOPPLER_TX_INNER[3:0]")
    cmd_f.add_bit_field("doppler_rx_outer", 33, 32, "外部多普勒接收[1:0] | DOPPLER_RX_OUTER[1:0]")
    cmd_f.add_bit_field("doppler_tx_outer", 31, 28, "外部多普勒发送[3:0] | DOPPLER_TX_OUTER[3:0]")
    cmd_f.add_bit_field("doppler_rx_side", 27, 26, "侧面多普勒接收[1:0] | DOPPLER_RX_SIDE[1:0]")
    cmd_f.add_bit_field("doppler_tx_side", 25, 24, "侧面多普勒发送[1:0] | DOPPLER_TX_SIDE[1:0]")
    cmd_f.add_bit_field("rev_sh_ena", 23, 23, "反向移位使能 | REV_SH_ENA")
    cmd_f.add_bit_field("rev_sh", 22, 21, "反向移位 | REV_SH")
    cmd_f.add_bit_field("crm_crc8", 7, 0, "CRM命令CRC8校验 | CRM_CRC8")
    CRM_COMMANDS[0xF] = cmd_f

    # CRM命令2 (SW_RST)
    cmd_2 = CRMCommand(0x2, "SW_RST", 4)
    cmd_2.add_bit_field("sid", 31, 28, "从节点标识符 | Slave node identifier")
    cmd_2.add_bit_field("command", 27, 24, "命令代码 | Command code")
    cmd_2.add_bit_field("data", 23, 8, "16位数据 – 0x0000h | 16 bits data – 0x0000h")
    cmd_2.add_bit_field("crm_crc8", 7, 0, "CRM命令CRC8校验 | CRM_CRC8")
    CRM_COMMANDS[0x2] = cmd_2

    # CRM命令3 (DISCOVER)
    cmd_3 = CRMCommand(0x3, "DISCOVER", 4)
    cmd_3.add_bit_field("sid", 31, 28, "从节点标识符 – 0xFh (广播) | Slave node identifier – 0xFh (broadcast)")
    cmd_3.add_bit_field("command", 27, 24, "命令代码 – 0x3h | Command code – 0x3h")
    cmd_3.add_bit_field("data", 23, 8, "16位数据 – 0x0000h | 16 bits data – 0x0000h")
    cmd_3.add_bit_field("crm_crc8", 7, 0, "CRM命令CRC8校验 | CRM_CRC8")
    CRM_COMMANDS[0x3] = cmd_3

    # CRM命令4 (WRITE_BLK_P)
    cmd_4 = CRMCommand(0x4, "WRITE_BLK_P", 14)
    cmd_4.add_bit_field("sid", 111, 108, "从节点标识符 | Slave node identifier")
    cmd_4.add_bit_field("command", 107, 104, "命令代码 | Command code")
    cmd_4.add_bit_field("data0", 103, 88, "第一个16位数据，写入内存 | First 16 bits data to be written into memory")
    cmd_4.add_bit_field("data1", 87, 72, "第二个16位数据，写入内存 | Second 16 bits data to be written into memory")
    cmd_4.add_bit_field("data2", 71, 56, "第三个16位数据，写入内存 | Third 16 bits data to be written into memory")
    cmd_4.add_bit_field("data3", 55, 40, "第四个16位数据，写入内存 | Fourth 16 bits data to be written into memory")
    cmd_4.add_bit_field("data4", 39, 24, "第五个16位数据，写入内存 | Fifth 16 bits data to be written into memory")
    cmd_4.add_bit_field("data5", 23, 8, "第六个16位数据，写入内存 | Sixth 16 bits data to be written into memory")
    cmd_4.add_bit_field("crm_crc8", 7, 0, "CRM命令CRC8校验 | CRM_CRC8")
    CRM_COMMANDS[0x4] = cmd_4

    # CRM命令5 (WRITE_P)
    cmd_5 = CRMCommand(0x5, "WRITE_P", 4)
    cmd_5.add_bit_field("sid", 31, 28, "从节点标识符 | Slave node identifier")
    cmd_5.add_bit_field("command", 27, 24, "命令代码 | Command code")
    cmd_5.add_bit_field("data", 23, 8, "16位数据，写入内存 | 16 bits data to be written into memory")
    cmd_5.add_bit_field("crm_crc8", 7, 0, "CRM命令CRC8校验 | CRM_CRC8")
    CRM_COMMANDS[0x5] = cmd_5

    # CRM命令6 (WRITE)
    cmd_6 = CRMCommand(0x6, "WRITE", 4)
    cmd_6.add_bit_field("sid", 31, 28, "从节点标识符 | Slave node identifier")
    cmd_6.add_bit_field("command", 27, 24, "命令代码 | Command code")
    cmd_6.add_bit_field("data", 23, 8, "16位数据，写入内存 | 16 bits data to be written into memory")
    cmd_6.add_bit_field("crm_crc8", 7, 0, "CRM命令CRC8校验 | CRM_CRC8")
    CRM_COMMANDS[0x6] = cmd_6

    # CRM命令7 (WRITE_8)
    cmd_7 = CRMCommand(0x7, "WRITE_8", 4)
    cmd_7.add_bit_field("sid", 31, 28, "从节点标识符 | Slave node identifier")
    cmd_7.add_bit_field("command", 27, 24, "命令代码 | Command code")
    cmd_7.add_bit_field("w_idx", 23, 16, "内存索引 – 任何字节对齐的内存地址 | Index of memory – any byte-aligned memory address")
    cmd_7.add_bit_field("data", 15, 8, "8位数据，写入内存 | 8 bits data to be written into memory")
    cmd_7.add_bit_field("crm_crc8", 7, 0, "CRM命令CRC8校验 | CRM_CRC8")
    CRM_COMMANDS[0x7] = cmd_7

    # CRM命令8 (WRITE_RWP)
    cmd_8 = CRMCommand(0x8, "WRITE_RWP", 4)
    cmd_8.add_bit_field("sid", 31, 28, "从节点标识符 | Slave node identifier")
    cmd_8.add_bit_field("command", 27, 24, "命令代码 | Command code")
    cmd_8.add_bit_field("pdcm_upd", 23, 22, "PDCM读指针更新模式 | Update mode of PDCM read pointer")
    cmd_8.add_bit_field("new_pdcm_r_pg", 21, 20, "写入PDCM读指针页的值 | Value to be written into page of PDCM read pointer")
    cmd_8.add_bit_field("crm_upd", 19, 19, "CRM写指针更新模式 | Update mode of CRM write pointer")
    cmd_8.add_bit_field("new_crm_w_pg", 17, 16, "写入CRM写指针页的值 | Value to be written into page of CRM write pointer")
    cmd_8.add_bit_field("new_crm_w_idx", 15, 8, "写入CRM写指针索引的值 | Value to be written into index of CRM write pointer")
    cmd_8.add_bit_field("crm_crc8", 7, 0, "CRM命令CRC8校验 | CRM_CRC8")
    CRM_COMMANDS[0x8] = cmd_8

    # CRM命令9 (READ)
    cmd_9 = CRMCommand(0x9, "READ", 4)
    cmd_9.add_bit_field("sid", 31, 28, "从节点标识符 | Slave node identifier")
    cmd_9.add_bit_field("command", 27, 24, "命令代码 | Command code")
    cmd_9.add_bit_field("rd_pg", 17, 16, "要读取的内存页 | Memory page to be read")
    cmd_9.add_bit_field("rd_idx", 15, 8, "页内要读取的内存索引 | Memory index within page to be read")
    cmd_9.add_bit_field("crm_crc8", 7, 0, "CRM命令CRC8校验 | CRM_CRC8")
    CRM_COMMANDS[0x9] = cmd_9

    # CRM命令A (PROGRAM)
    cmd_a = CRMCommand(0xA, "PROGRAM", 4)
    cmd_a.add_bit_field("sid", 31, 28, "从节点标识符 | Slave node identifier")
    cmd_a.add_bit_field("command", 27, 24, "命令代码 | Command code")
    cmd_a.add_bit_field("reserved", 23, 8, "保留 | Reserved")
    cmd_a.add_bit_field("crm_crc8", 7, 0, "CRM命令CRC8校验 | CRM_CRC8")
    CRM_COMMANDS[0xA] = cmd_a

# 初始化CRM命令
init_crm_commands()

def get_crm_command(code):
    """
    获取指定代码的CRM命令

    Args:
        code: CRM命令代码

    Returns:
        CRM命令对象，如果不存在则返回None
    """
    return CRM_COMMANDS.get(code)
