/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * DSI_Spi_Callback.c:
 * Created on: 2021-04-6
 * Original designer:
 * Detail designer:
 ******************************************************************************/

/******************************************************************************
 * Includes
 *****************************************************************************/
#include "DSI3_COM.h"
#include "DSI3_SPI.h"
#include "Queue_CRMResponse.h"
#include "Elmos_524_17_Private.h"
//#include "Port.h"

/******************************************************************************/
/******************************************************************************/
/********************************* 回调事件 ***********************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************************/
/**
 * @brief   
 */
/*****************************************************************************/




/******************************************************************************/
/**
 * @brief   521.42 DCR1/2B 下降沿外部中断处理
 */
/*****************************************************************************/


void DSI_Master0_DCR1B_Fall_Cbk(void)
{
    DSI_Master_DCRB_ISR(DSIMaster0,DSIChl1);
}

void DSI_Master0_DCR2B_Fall_Cbk(void)
{
    DSI_Master_DCRB_ISR(DSIMaster0,DSIChl2);
}

void DSI_Master1_DCR1B_Fall_Cbk(void)
{
    DSI_Master_DCRB_ISR(DSIMaster1,DSIChl1);
}

void DSI_Master1_DCR2B_Fall_Cbk(void)
{
    DSI_Master_DCRB_ISR(DSIMaster1,DSIChl2);
}




void DetSPIDSI_ReadCRMRes_CMD(DSIMasterID_en DSIMasterID,uint8 *pSPIRecvData,uint8 SPIRecvDataLen)
{
    uint8 CMD_Code = 0;

    DSIChlSel_en chlSel;
    DSIChlID_en DSIChlID;

    CMD_Code = pSPIRecvData[2] & 0xF0;
    chlSel = (DSIChlSel_en)(pSPIRecvData[3] & 0x03);

    if(ReadCRMRes_CMD_Code == CMD_Code)
    {
        if((chlSel == DSIChlSEL1) || (chlSel == DSIChlSEL2))
        {
        
            DSIChlID = (DSIChlID_en)(chlSel - 1);
            
            PutCRMRESData(DSIMasterID,DSIChlID,(CRM_DSIChlRES_str *)&pSPIRecvData[4]);
        }
        else if(chlSel == DSIChlSEL1And2)
        {
            PutCRMRESData(DSIMasterID,DSIChl1,(CRM_DSIChlRES_str *)&pSPIRecvData[4]);
            PutCRMRESData(DSIMasterID,DSIChl2,(CRM_DSIChlRES_str *)&pSPIRecvData[10]);
        }
        else
        {
            /** @brief ERR */
        }
    }
}

extern uint8 DetPDCMResFrameMSg(DSIMasterID_en DSIMasterID,DSIChlID_en DSIChlID,PDCM_DSIChlRES_str *pPDCMResData);

void DetSPIDSI_ReadPDCMRes_CMD(DSIMasterID_en DSIMasterID,uint8 *pSPIRecvData,uint8 SPIRecvDataLen)
{
    uint8 CMD_Code = 0;
    DSIChlSel_en chlSel;
    DSIChlID_en DSIChlID;

    CMD_Code = pSPIRecvData[2] & 0xF0;
    chlSel = (DSIChlSel_en)(pSPIRecvData[3] & 0x03);
    

    if(ReadPDCMRes_CMD_Code == CMD_Code)
    {
        
        if((chlSel == DSIChlSEL1) || (chlSel == DSIChlSEL2))
        {
            DSIChlID = (DSIChlID_en)(chlSel - 1);

            DetPDCMResFrameMSg(DSIMasterID,DSIChlID,(PDCM_DSIChlRES_str *)&pSPIRecvData[6]);
            
        }
        else if(chlSel == DSIChlSEL1And2)
        {
            DetPDCMResFrameMSg(DSIMasterID,DSIChl1,(PDCM_DSIChlRES_str *)&pSPIRecvData[6]);
            DetPDCMResFrameMSg(DSIMasterID,DSIChl2,(PDCM_DSIChlRES_str *)&pSPIRecvData[6 + sizeof(PDCM_DSIChlRES_str)]);
            
        }
        else
        {
            /** @brief ERR */
        }

    }

}



/******************************************************************************/
/**
 * @brief            DSI数据处理
 * @param[in]        DSIMasterID
 * @param[in]        SpiRecevData
 * @param[in]        LcLen
 *
 * @return           E_OK  
 */
/*****************************************************************************/
uint8 DSI_DataHandleCbk(DSI_SPI_RX_Data_str *pDSI_SPIData,const uint8* SpiTransData)
{
	DSIMasterID_en DSIMasterID = pDSI_SPIData->DSIMasterID;
	uint8 *SpiRecevData = &pDSI_SPIData->SPIResData[0];
	uint16 LcLen = pDSI_SPIData->DataLen;
	uint32 ReceiveTime = pDSI_SPIData->RecTime;
	uint32 ADASSYNTime = pDSI_SPIData->ADASSYNTime;
    uint8 CMD_Code = 0;
    uint8 chlSel;
    DSIChlID_en DSIChl;
    uint16 CalcCRC16 = 0;
    uint16 RxCrc16 = 0xFFFF;
    uint32 Idx = 0; 
		
    DSI_MASTER_Cfg_str *pDSI_MASTER;
    DSI_Chl_Ctrl_STATUS_Str *pDSICHLCtrlStatus;
    PDCM_FRAME_STATUS_Str * pPDCM_FRAME_STATUS;
    if((DSIMasterID >= DSIMasterNum) || (NULL == SpiTransData))
    {
        return 1;
    }
    pDSI_MASTER = &G_DSI_MASTER[DSIMasterID];

    /** @brief 数据长度 >4 则进行CRC校验及数据提取               */
    if(LcLen > 4)
    {
        CalcCRC16 = DSI_SPI_CRC16Calculate(&SpiRecevData[2],LcLen - 4);
        RxCrc16 = ((SpiRecevData[LcLen - 2] << 8) | SpiRecevData[LcLen - 1]);
        if(CalcCRC16 != RxCrc16)
        {
			chlSel = SpiRecevData[3] & 0x03;
			for(DSIChl=DSIChl1;DSIChl<DSIChlNum;DSIChl++)
			{
				if((chlSel >> DSIChl) & 0x01)
				{
					/*清除DSI通道监控超时计数器*/
					pDSI_MASTER->MasterCtrl.DSICHLCtrlStatus[DSIChl].DSIWorkTimeOutCnt = 0;
				}
			}
			
            /** @brief SPI CRC16错误 */
            pDSI_MASTER->MasterCtrl.SPIRxCrcErrCnt++;
            PRINTF_DSI3COM("DSI_DataHandleCbk SPIRxCrcErr %d \n",pDSI_MASTER->MasterCtrl.SPIRxCrcErrCnt);
            return 2;
        }
        else
        {
            /** @brief 提取IC状态 */
            pDSI_MASTER->MasterCtrl.ICStatusReg.u16Data = (SpiRecevData[0] << 8) | SpiRecevData[1];
            
            /** @brief 提取命令头及DSI通道选择 */
            CMD_Code = SpiRecevData[2] & 0xF0;
            chlSel = SpiRecevData[3] & 0x03;

			if(READ_MasterReg_CMD_Code == (CMD_Code & 0xC0))
			{
				if((SpiTransData[0] == SpiRecevData[2]) && (SpiTransData[1] == SpiRecevData[3]))
				{
					//((SpiRecevData[4] << 8) | SpiRecevData[5]);
				}
			}
			else if(WRITE_MasterReg_CMD_Code == (CMD_Code & 0xC0))
			{
				for(Idx = 0;Idx < (LcLen - 2U); Idx++)
				{
					if(SpiTransData[Idx] != SpiRecevData[Idx + 2])
					{
						//fail
					}
				}
			}
			else
			{
	            switch(CMD_Code)
	            {	
					case CRM_CMD_Code:						
					{
						pDSI_MASTER->SPITransCtrl.SPIDataTransSts = SPIDataTransSuccess;						
						pDSI_MASTER->SPITransCtrl.u16TransTimeOutCnt = 0;
						for(Idx = 0;Idx < (LcLen-2U);Idx++)
						{
							if(SpiTransData[Idx] != SpiRecevData[Idx + 2])
							{
								pDSI_MASTER->SPITransCtrl.SPIDataTransSts = SPIDataTransFail;
							}
						}

  						if((SPIDataTransSuccess == pDSI_MASTER->SPITransCtrl.SPIDataTransSts) 
							&& (((SpiTransData[2] & 0x0F) == 0x01) || ((SpiTransData[2] & 0x0F) == 0x02)))
  						{
							/*0x01 std cmd,0x02 adv cmd*/
							//SetTransMeasCmdTime(DSIMasterID,DSIChl1,ReceiveTime);							
							//SetTransMeasCmdTime(DSIMasterID,DSIChl2,ReceiveTime);							
  						}
						
						break;
					}
					
			        case ReadCRMRes_CMD_Code:
	                {
	                    if((GetDSICHLWorkStatus(DSIMasterID,DSIChl1) == DSICHL_CRM) || (GetDSICHLWorkStatus(DSIMasterID,DSIChl2) == DSICHL_CRM))
	                    {
	                        /** @brief CRM 应答数据保存 */
	                        DetSPIDSI_ReadCRMRes_CMD(DSIMasterID,SpiRecevData,LcLen);
	                    }
	                    for(DSIChl=DSIChl1;DSIChl<DSIChlNum;DSIChl++)
	                    {
	                        if((chlSel >> DSIChl) & 0x01)
	                        {
	                            if(GetDSICHLWorkStatus(DSIMasterID,DSIChl) == DSICHL_CRM)
	                            {
	                                /** @brief 设置为DSICHL模式为IDLE */
	                                SetDSICHLWorkStatus(DSIMasterID,DSIChl,DSICHL_CMD_DONE);

	                                SetDSIDCRBSubStatus(DSIMasterID,DSIChl,DSICHL_WaiteDCRB_NULL);
	                            }
	                            else
	                            {

	                            }
	                        }
	                    }

	                    break;
	                }

					case UpLoadTDMA_CMD_Code: 
					{
						for(Idx = 0;Idx < (LcLen-2U);Idx++)
						{
							if(SpiTransData[Idx] != SpiRecevData[Idx + 2])
							{
								//fail
							}
						}
						break;
					}
					
					case DM_CMD_Code:
					{
						for(Idx = 0;Idx < (LcLen-2U);Idx++)
						{
							if(SpiTransData[Idx] != SpiRecevData[Idx + 2])
							{
								//fail
							}
						}
						break;
					}
					
					case AUTO_BRC_CMD_Code:             
					{
						for(Idx = 0;Idx < (LcLen-2U);Idx++)
						{
							if(SpiTransData[Idx] != SpiRecevData[Idx + 2])
							{
								//fail
							}
						}
						break;
					}
					
	                case ReadPDCMRes_CMD_Code:
	                {
	                    
	                    pPDCM_FRAME_STATUS = (PDCM_FRAME_STATUS_Str *) &SpiRecevData[6];

	                    if(((pPDCM_FRAME_STATUS->Status) & 0x20) == 0)
	                    {
	                        if(GetDSICHLWorkStatus(DSIMasterID,DSIChl1) == DSICHL_PDCM || GetDSICHLWorkStatus(DSIMasterID,DSIChl2) == DSICHL_PDCM )
	                        {
	                            /** @brief PDCM应答数据保存 */

	                            DetSPIDSI_ReadPDCMRes_CMD(DSIMasterID,SpiRecevData,LcLen);
	                        }
	                        else
	                        {

	                        }

	                        for(DSIChl=DSIChl1;DSIChl<DSIChlNum;DSIChl++)
	                        {
	                            pDSICHLCtrlStatus = &pDSI_MASTER->MasterCtrl.DSICHLCtrlStatus[DSIChl];
	                            
	                            if((chlSel >> DSIChl) & 0x01)
	                            {
	                                if(GetDSICHLWorkStatus(DSIMasterID,DSIChl) == DSICHL_PDCM)
	                                {

	                                    pDSICHLCtrlStatus->ActiveCRMCMDStatus.wFrameCnt++;
	                                    
	                                    if(pDSICHLCtrlStatus->ActiveCRMCMDStatus.wFrameCnt   \
	                                    >= pDSICHLCtrlStatus->ActiveCRMCMDStatus.AutoBRCMaxCnt)
	                                    {


	                                        if(pDSICHLCtrlStatus->ActiveCRMCMDStatus.AutoBRCMaxCnt <= 255)
	                                        {	
												/** @brief  获取数据接收完成时间和ADAS同步时间*/
												SetDataRecEndTime(DSIMasterID,DSIChl,ReceiveTime);
												SetADASYNTime(DSIMasterID,DSIChl,ADASSYNTime);
	                                            /** @brief 接收BRC个数小于255个,接收帧完成 */
	                                            SetDSICHLWorkStatus(DSIMasterID,DSIChl,DSICHL_CMD_DONE);
	                                            SetDSIDCRBSubStatus(DSIMasterID,DSIChl,DSICHL_WaiteDCRB_NULL);
	                                            PRINTF_DSI3COM("CHL%d wFrameCnt  %d\n\r",DSIChl,pDSICHLCtrlStatus->ActiveCRMCMDStatus.wFrameCnt);
	                                        }
	                                        else
	                                        {
	                                            /** @brief 接收帧完成，接收BRC个数大于255个,DCRB中断服务中请求                     STOPDSI，结束AUTOBRC */
	                                        }
	                                    }
	                                    else
	                                    {
	                                        SetDSICHLWorkStatus(DSIMasterID,DSIChl,DSICHL_PDCM);
	                                    }

	                                }
	                                else
	                                {
	                                    /** @brief ERR */

	                                }
	                            }
	                        }
	                    }

	                    break;
	                }

	                default:
	                {
	                    /**/
	                    break;
	                }
	            }
			}
        }
    }
    else
    {
        if(LcLen == 2)
        {
			/*READ_ICStatus_CMD_Code:*/
			//((SpiRecevData[0] << 8) | SpiRecevData[1])
		
        }
        else
        {
            return 3;
        }
    }

    return 0;
}


/******************************************************************************/
/**
 * @brief            DSI SPI数据处理
 * @param[in]        
 *
 * @return           无   
 */
/*****************************************************************************/

void DSI_SPI_DataHandle()
{
    uint16 Flg = 0xFFFF;
    uint8 LcCnt = 0;
    DSI_SPI_RX_Data_str GDSI_SPIData;
	const uint8* TxDataAddr = NULL; 
    
    if(GetDSI_SPI_RxReadyCnt() != 0)	/* 准备接收状态 */
    {
        for(LcCnt = 0;LcCnt < 10;LcCnt ++)
        {
            GDSI_SPIData.DataLen = 0;
            Flg = GetDSI_SPI_RxReadyData(&GDSI_SPIData);
            TxDataAddr = GetDSI_SPI_SeqTransData(GDSI_SPIData.DSIMasterID,Flg);
			
            if((Flg != 0xFFFF) && (GDSI_SPIData.DataLen != 0))
            {   
                DSI_DataHandleCbk(&GDSI_SPIData,TxDataAddr);
            }
            else
            {
                break;
            }

        }
    }
}

void DSIMaster0_SPI_CompleteTransfer_cbk(void)
{
    DSI_SPI_CompleteTransfer_HandleCbk(DSIMaster0);
}

void DSIMaster1_SPI_CompleteTransfer_cbk(void)
{
    DSI_SPI_CompleteTransfer_HandleCbk(DSIMaster1);
}


