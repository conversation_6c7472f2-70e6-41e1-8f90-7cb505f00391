 #ifndef   __DID_CALIBRATION_H__
 #define   __DID_CALIBRATION_H__
  
/******************************************************************************* 
* Includes 
********************************************************************************/ 
#include "Types.h"
#include "EELHal.h"
#include "UserFlash.h"
#include "Did_Cali_Cbk.h"
/******************************************************************************
* Constants and macros
*******************************************************************************/
#define LH_SECURITY_ID_61    0x61
#define LH_SECURITY_ID_62    0x62


#if (CALIBRATION_EN == STD_ON)

typedef struct tag_Did_Data
{
    uint16 GwDID;
    volatile uint8* GcADDR;
    uint16 GwDataLen;
    uint8 GcDir;
    volatile uint32 GdwADDR;    /*数据对应EEP地址*/
    uint8_t(*pfReadData)(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Dst);
    uint8_t(*pfWriteData)(uint16_t u16Handle, uint16_t u16Did, uint16_t u16Size, uint8_t* pu8Src);

}DataIdentifier_Cail;//标定DID数据类型;
typedef enum
{
    SPARE_PART_INDEX = 0,
    PAS_SWNumber_INDEX,
    PAS_BTVerNum_INDEX,
    PAS_HWVerNum_INDEX,
    PAS_SWID_INDEX,
    ECUManufac_INDEX,
    VehicleConfig_INDEX,
//  探头增益 
    CALIBRATION_GAIN,
//回波高度
    FLS_FRS_ST,
    FLS_FRS_CT,
    FL_FR_ST,
    FL_FR_CT,
    FML_FMR_ST,
    FML_FMR_CT,
    RLS_RRS_ST,
    RLS_RRS_CT,
    RL_RR_ST,
    RL_RR_CT,
    RML_RMR_ST,
    RML_RMR_CT,
    OBJ_JUDGE_ST,
    OBJ_JUDGE_CT,

    CAL_DATA_FD00,
    CAL_DATA_FD20,
    CAL_DATA_FD21,
    CAL_DATA_FD22,
    CAL_DATA_FD23,

    CAL_DATA_FD24,
    CAL_DATA_FD25,
    CAL_DATA_FD26,
    CAL_DATA_FD27,
    CAL_DATA_FD28,

    CAL_DATA_FD29,
    CAL_DATA_FD2A,
    CAL_DATA_FD2B,
    CAL_DATA_FD2C,
    CAL_DATA_FD2D,

    CAL_DATA_FD2E,
    CAL_DATA_FD2F,

    CAL_DATA_FD40,
    CAL_DATA_FD41,
    CAL_DATA_FD42,
    CAL_DATA_FD43,
    CAL_DATA_FD44,

    CAL_DATA_FD45,
    CAL_DATA_FD46,
    CAL_DATA_FD47,
    CAL_DATA_FD48,
    CAL_DATA_FD49,

    CAL_DATA_FD50,
    CAL_DATA_FD51,
    CAL_DATA_FD52,
    CAL_DATA_FD53,
    CAL_DATA_FD54,

    CAL_DATA_FD55,
    CAL_DATA_FD56,
    CAL_DATA_FD57,
    CAL_DATA_FD58,
    CAL_DATA_FD59,

    CAL_DATA_FD5A,
    CAL_DATA_FD5B,
    CAL_DATA_FD5C,
    CAL_DATA_FD5D,
    CAL_DATA_FD5E,

    CAL_DATA_FD5F,
    CAL_DATA_FD60,
    CAL_DATA_FD61,
    CAL_DATA_FD62,
    CAL_DATA_FD63,

    CAL_DATA_FD64,
    CAL_DATA_FD65,
    CAL_DATA_FD66,
    CAL_DATA_FD67,
    CAL_DATA_FD70,

    CAL_DATA_FDA0,
    CAL_DATA_FDA1,
    CAL_DATA_FDA2,
    CAL_DATA_FDA3,
    CAL_DATA_FDA4,

    DID_CALIB_NUM
}CalibDIDNumType;

#endif

/******************************************************************************
* Typedef definitions
*******************************************************************************/


/******************************************************************************
* External objects
*******************************************************************************/
#if (CALIBRATION_EN == STD_ON)
//extern const struct DataIdentifier CalibrationDIDDefine[DID_CALIB_NUM];
extern const DataIdentifier_Cail CalibrationDIDDefine[DID_CALIB_NUM];
#endif
/******************************************************************************
* Global Functions
*******************************************************************************/
#if (CALIBRATION_EN == STD_ON)
uint32 LonghornSecurityAlgorithm(uint32 Lu32Seed);
void InitCalibrationDIDToEeprom(void);
void CalibrationDIDPowerOnEepromInit(void);
#endif
#endif



