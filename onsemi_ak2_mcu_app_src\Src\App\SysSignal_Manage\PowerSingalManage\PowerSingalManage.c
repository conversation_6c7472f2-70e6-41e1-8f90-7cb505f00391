/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * MEBSignalManage.c: 
 * Created on: 2021-03-18 14:29
 * Original designer: 22546
 ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "PowerSingalManage.h"
#include "Power_Manage.h"

/*============================================================================*/
/*==================                                      ====================*/
/*==================    信      号      输      出      区     ====================*/
/*==================                                      ====================*/
/*============================================================================*/
static Signal_VoltageStatusType GeunGroupVoltageStatus[PM_TOTAL] =
{
	[PM_APP] = NORMAL_VOLTAGE,
    [PM_CAN_TX_RX] = NORMAL_VOLTAGE,
    [PM_CAN_UDS] = NORMAL_VOLTAGE,
};

static SnsPwrDiagStatus_e GeunSnsPwrDiagStatus[SnsGroupPwrDiagNum] = 
{
	[FrontSnsGroupPwrDiagIdx] = PWR_PIN_NORMAL,
	[RearSnsGroupPwrDiagIdx] = PWR_PIN_NORMAL,
	[SideSnsGroupPwrDiagIdx] = PWR_PIN_NORMAL,
};

/******************************************************************************/
/**
* @brief: 电源物理电压平均值输出，单位0.1V
**/
/******************************************************************************/
uint8 ReadPwrMonitorSing_VoltageValue(void)
{
    uint8 Lu8VoltageValueTem;
    Lu8VoltageValueTem = (uint8)(GwADMeanValue/20.3 + 8.3);
    return Lu8VoltageValueTem;
}

/******************************************************************************/
/**
* @brief: AD采样瞬时值输出
**/
/******************************************************************************/
uint16 ReadPwrMonitorSignal_ADMomentaryValue(void)
{
	return Gu16ADMomentaryValue;
}

/******************************************************************************/
/**
* @brief: Sensor 电源AD采样瞬时值输出
**/
/******************************************************************************/
uint16 ReadSnsPwrADVal(PowerAdcIdx_e AdIdx)
{
	return Gu16PwrADVal[AdIdx];
}

/******************************************************************************/
/**
* @brief: 写电压等级
**/
/******************************************************************************/
void WritePwrManage_GroupStatus(Signal_PowerManageGroupType LenuGroup,Signal_VoltageStatusType *LenuVoltageStatus)
{
   	if(LenuGroup < PM_TOTAL)
   	{
    	GeunGroupVoltageStatus[LenuGroup] = *LenuVoltageStatus;
   	}
}

/******************************************************************************/
/**
* @brief: 读电压状态
**/
/******************************************************************************/
void ReadPwrManage_GroupStatus(Signal_PowerManageGroupType LenuGroup,Signal_VoltageStatusType *LenuVoltageStatus)
{
   	if(LenuGroup < PM_TOTAL)
   	{
    	*LenuVoltageStatus = GeunGroupVoltageStatus[LenuGroup];
   	}
}

/******************************************************************************/
/**
* @brief: 写Sensor电压诊断状态
**/
/******************************************************************************/
void WritePwrManage_SnsPwrDiagStatus(SnsPowerDiagIdx_e LenuIdx,SnsPwrDiagStatus_e LenuPwrSts)
{
	if(LenuIdx < SnsGroupPwrDiagNum)
	{
    	GeunSnsPwrDiagStatus[LenuIdx] = LenuPwrSts;
	}
}

/******************************************************************************/
/**
* @brief: 读Sensor电压诊断状态
**/
/******************************************************************************/
void ReadPwrManage_SnsPwrDiagStatus(SnsPowerDiagIdx_e LenuIdx,SnsPwrDiagStatus_e *pLenuPwrSts)
{
	if(LenuIdx < SnsGroupPwrDiagNum)
	{
    	*pLenuPwrSts = GeunSnsPwrDiagStatus[LenuIdx];
	}
}


