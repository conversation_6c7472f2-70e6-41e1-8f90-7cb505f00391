# 命令解析工具

这是一个用于解析命令和响应的工具，可以通过输入一条或多条命令+回复的方式，解析出当前命令的具体发送和回复内容的定义。

## 功能特点

- 支持解析写操作、读操作、特殊映射地址操作和特别操作
- 支持CRC8校验
- 支持解析响应数据
- 模块化设计，便于扩展
- 支持命令历史记录

## 安装

```bash
# 克隆仓库
git clone https://github.com/yourusername/command-parser.git
cd command-parser

# 安装依赖
pip install -r requirements.txt
```

## 使用方法

### 命令行界面

```bash
python command_analyzer.py
```

### 输入格式

1. 单个命令：
   ```
   02 50 ff ff ff ff 7a
   ```

2. 命令和响应：
   ```
   02 50 ff ff ff ff 7a -> 00 00 06 45 a5 a1 a6
   ```

### 特殊命令

- `help`, `h`, `?` - 显示帮助信息
- `history`, `hist` - 显示命令历史
- `clear`, `cls` - 清除命令历史
- `exit`, `quit`, `q` - 退出程序

## 示例

### 写操作

```
> 02 50 ff ff ff ff 7a
解析结果: {'type': 'write', 'address': 80, 'data': [255, 255, 255, 255], 'crc8': 122}
```

### 读操作

```
> 03 11 00 00 00 00 e8
解析结果: {'type': 'read', 'operation': 3, 'address': 17, 'crc8': 232, 'operation_name': 'read_request'}
```

### 命令和响应

```
> 02 50 ff ff ff ff 7a -> 00 00 06 45 a5 a1 a6
命令解析结果: {'type': 'write', 'address': 80, 'data': [255, 255, 255, 255], 'crc8': 122}
响应解析结果: {'type': 'non_read_response', 'BRC_RECEIVED_CHA': 0, 'BRC_RECEIVED_CHB': 0, ...}
```

## 项目结构

```
command_parser/
├── __init__.py
├── address_definitions.py  # 地址定义模块
├── command_parser.py       # 命令解析器模块
├── crc8.py                 # CRC8计算模块
├── crm_definitions.py      # CRM命令定义模块
├── response_parser.py      # 响应解析模块
└── utils.py                # 工具函数模块
command_analyzer.py         # 主程序
README.md                   # 说明文档
```

## 扩展

### 添加新的地址定义

在 `address_definitions.py` 中的 `init_registers()` 函数中添加新的寄存器定义：

```python
reg_new = Register(address, "REG_NAME")
reg_new.add_bit_field("FIELD_NAME", msb, lsb)
REGISTERS[address] = reg_new
```

### 添加新的CRM命令定义

在 `crm_definitions.py` 中的 `init_crm_commands()` 函数中添加新的CRM命令定义：

```python
cmd_new = CRMCommand(code, "COMMAND_NAME", data_length)
cmd_new.add_bit_field("FIELD_NAME", msb, lsb, "Description")
CRM_COMMANDS[code] = cmd_new
```

## 许可证

MIT
