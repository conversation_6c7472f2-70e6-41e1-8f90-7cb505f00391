/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * DebugSignal_CommonTypes: 
 * Created on: 
 * Original designer: 
 ******************************************************************************/

#ifndef DEBUGSIGNAL_COMMONTYPES_H
#define DEBUGSIGNAL_COMMONTYPES_H

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "Types.h"
#include "CAN_COM.h"

/* 设置调试报文使能 */
#define DEBUG_MSG_TX_EN          DEBUG_FRAME_TX_EN
/* 设置调试报文ID */
#define DEBUG_FRAME_ID_6F2           0x6F2u
#define DEBUG_FRAME_ID_6F3           0x6F3u
#define DEBUG_FRAME_ID_6F4           0x6F4u
#define DEBUG_FRAME_ID_6F5           0x6F5u
#define DEBUG_FRAME_ID_6F6           0x6F6u
#define DEBUG_FRAME_ID_6F7           0x6F7u
#define DEBUG_FRAME_ID_6F8           0x6F8u
#define DEBUG_FRAME_ID_6F9           0x6F9u
#define DEBUG_FRAME_ID_6FA           0x6FAu
#define DEBUG_FRAME_ID_6FB           0x6FBu
#define DEBUG_FRAME_ID_6FC           0x6FCu
#define DEBUG_FRAME_ID_6FD           0x6FDu
#define DEBUG_FRAME_ID_6FE           0x6FEu
#define DEBUG_FRAME_ID_6FF           0x6FFu

/**********************************DEBUG MASSAGE*************************************************/
typedef union
{
    uint8 byte[32];
    struct
    {
        uint8 FLS_Distance_2 : 8;
        uint8 FLS_Distance_1 : 8;
        uint8 FL_Distance_2 : 8;
        uint8 FL_Distance_1 : 8;
        uint8 FML_Distance_2 : 8;
        uint8 FML_Distance_1 : 8;
        uint8 FMR_Distance_2 : 8;
        uint8 FMR_Distance_1 : 8;
        uint8 FR_Distance_2 : 8;
        uint8 FR_Distance_1 : 8;
        uint8 FRS_Distance_2 : 8;
        uint8 FRS_Distance_1 : 8;
        uint8 RRS_Distance_2 : 8;
        uint8 RRS_Distance_1 : 8;
        uint8 RR_Distance_2 : 8;
        uint8 RR_Distance_1 : 8;
        uint8 RMR_Distance_2 : 8;
        uint8 RMR_Distance_1 : 8;
        uint8 RML_Distance_2 : 8;
        uint8 RML_Distance_1 : 8;
        uint8 RL_Distance_2 : 8;
        uint8 RL_Distance_1 : 8;
        uint8 RLS_Distance_2 : 8;
        uint8 RLS_Distance_1 : 8;
        uint8 FMR_HeightType : 2;
        uint8 FML_HeightType : 2;
        uint8 FL_HeightType : 2;
        uint8 FLS_HeightType : 2;
        uint8 RR_HeightType : 2;
        uint8 RRS_HeightType : 2;
        uint8 FRS_HeightType : 2;
        uint8 FR_HeightType : 2;
        uint8 RLS_HeightType : 2;
        uint8 RL_HeightType : 2;
        uint8 RML_HeightType : 2;
        uint8 RMR_HeightType : 2;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
    }bits;
}Radar_EchoData_6F2_ecu0_0_MSGType;
typedef union
{
    uint8 byte[16];
    struct
    {
        uint8 Car_Odo_X_4 : 8;
        uint8 Car_Odo_X_3 : 8;
        uint8 Car_Odo_X_2 : 8;
        uint8 Car_Odo_X_1 : 8;
        uint8 Car_Odo_Y_4 : 8;
        uint8 Car_Odo_Y_3 : 8;
        uint8 Car_Odo_Y_2 : 8;
        uint8 Car_Odo_Y_1 : 8;
        uint8 Car_Odo_YawAngle_2 : 8;
        uint8 Car_Odo_YawAngle_1 : 8;
        uint8 Vehicle_Type : 4;
        uint8 Vehicle_Cfg : 4;
        uint8 Vehicle_Platform : 2;
        uint8 : 6;
        uint8 Car_Dirve_Dis_4 : 8;
        uint8 Car_Dirve_Dis_3 : 8;
        uint8 Car_Dirve_Dis_2 : 8;
        uint8 Car_Dirve_Dis_1 : 8;
    }bits;
}VehicelInf_6F3_ecu0_0_MSGType;
typedef union
{
    uint8 byte[64];
    struct
    {
        uint8 UpdateFlg : 1;
        uint8 SnsWorkSts : 2;
        uint8 Master_Meas_Type : 3;
        uint8 EepromErrFlg : 1;
        uint8 : 1;
        uint8 Master_Rtm_2 : 8;
        uint8 Master_Rtm_1 : 8;
        uint8 Master_Distance_0_2 : 8;
        uint8 Master_Distance_0_1 : 8;
        uint8 Master_Distance_1_2 : 8;
        uint8 Master_Distance_1_1 : 8;
        uint8 Master_Distance_2_2 : 8;
        uint8 Master_Distance_2_1 : 8;
        uint8 Master_Distance_3_2 : 8;
        uint8 Master_Distance_3_1 : 8;
        uint8 Master_EchoHigh_0_2 : 8;
        uint8 Master_EchoHigh_0_1 : 8;
        uint8 Master_EchoHigh_1_2 : 8;
        uint8 Master_EchoHigh_1_1 : 8;
        uint8 Master_EchoHigh_2_2 : 8;
        uint8 Master_EchoHigh_2_1 : 8;
        uint8 Master_EchoHigh_3_2 : 8;
        uint8 Master_EchoHigh_3_1 : 8;
        uint8 LeftListen_Distance_0_2 : 8;
        uint8 LeftListen_Distance_0_1 : 8;
        uint8 LeftListen_Distance_1_2 : 8;
        uint8 LeftListen_Distance_1_1 : 8;
        uint8 LeftListen_Distance_2_2 : 8;
        uint8 LeftListen_Distance_2_1 : 8;
        uint8 LeftListen_Distance_3_2 : 8;
        uint8 LeftListen_Distance_3_1 : 8;
        uint8 LeftListen_EchoHigh_0_2 : 8;
        uint8 LeftListen_EchoHigh_0_1 : 8;
        uint8 LeftListen_EchoHigh_1_2 : 8;
        uint8 LeftListen_EchoHigh_1_1 : 8;
        uint8 LeftListen_EchoHigh_2_2 : 8;
        uint8 LeftListen_EchoHigh_2_1 : 8;
        uint8 LeftListen_EchoHigh_3_2 : 8;
        uint8 LeftListen_EchoHigh_3_1 : 8;
        uint8 RightListen_Distance_0_2 : 8;
        uint8 RightListen_Distance_0_1 : 8;
        uint8 RightListen_Distance_1_2 : 8;
        uint8 RightListen_Distance_1_1 : 8;
        uint8 RightListen_Distance_2_2 : 8;
        uint8 RightListen_Distance_2_1 : 8;
        uint8 RightListen_Distance_3_2 : 8;
        uint8 RightListen_Distance_3_1 : 8;
        uint8 RightListen_EchoHigh_0_2 : 8;
        uint8 RightListen_EchoHigh_0_1 : 8;
        uint8 RightListen_EchoHigh_1_2 : 8;
        uint8 RightListen_EchoHigh_1_1 : 8;
        uint8 RightListen_EchoHigh_2_2 : 8;
        uint8 RightListen_EchoHigh_2_1 : 8;
        uint8 RightListen_EchoHigh_3_2 : 8;
        uint8 RightListen_EchoHigh_3_1 : 8;
        uint8 MasterEchoCnt : 3;
        uint8 LeftEchoCnt : 3;
        uint8 : 2;
        uint8 RightEchoCnt : 3;
        uint8 NFD_Flag : 1;
        uint8 : 4;
        uint8 NFD_Dis_2 : 8;
        uint8 NFD_Dis_1 : 8;
        uint8 Ring_Fre_2 : 8;
        uint8 Ring_Fre_1 : 8;
        uint8 NoiseCnt : 8;
        uint8 NoiseSum_2 : 8;
        uint8 : 4;
        uint8 NoiseSum_1 : 4;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
    }bits;
}FLS_EchoData_6F4_ecu0_0_MSGType;
typedef union
{
    uint8 byte[64];
    struct
    {
        uint8 UpdateFlg : 1;
        uint8 SnsWorkSts : 2;
        uint8 Master_Meas_Type : 3;
        uint8 EepromErrFlg : 1;
        uint8 : 1;
        uint8 Master_Rtm_2 : 8;
        uint8 Master_Rtm_1 : 8;
        uint8 Master_Distance_0_2 : 8;
        uint8 Master_Distance_0_1 : 8;
        uint8 Master_Distance_1_2 : 8;
        uint8 Master_Distance_1_1 : 8;
        uint8 Master_Distance_2_2 : 8;
        uint8 Master_Distance_2_1 : 8;
        uint8 Master_Distance_3_2 : 8;
        uint8 Master_Distance_3_1 : 8;
        uint8 Master_EchoHigh_0_2 : 8;
        uint8 Master_EchoHigh_0_1 : 8;
        uint8 Master_EchoHigh_1_2 : 8;
        uint8 Master_EchoHigh_1_1 : 8;
        uint8 Master_EchoHigh_2_2 : 8;
        uint8 Master_EchoHigh_2_1 : 8;
        uint8 Master_EchoHigh_3_2 : 8;
        uint8 Master_EchoHigh_3_1 : 8;
        uint8 LeftListen_Distance_0_2 : 8;
        uint8 LeftListen_Distance_0_1 : 8;
        uint8 LeftListen_Distance_1_2 : 8;
        uint8 LeftListen_Distance_1_1 : 8;
        uint8 LeftListen_Distance_2_2 : 8;
        uint8 LeftListen_Distance_2_1 : 8;
        uint8 LeftListen_Distance_3_2 : 8;
        uint8 LeftListen_Distance_3_1 : 8;
        uint8 LeftListen_EchoHigh_0_2 : 8;
        uint8 LeftListen_EchoHigh_0_1 : 8;
        uint8 LeftListen_EchoHigh_1_2 : 8;
        uint8 LeftListen_EchoHigh_1_1 : 8;
        uint8 LeftListen_EchoHigh_2_2 : 8;
        uint8 LeftListen_EchoHigh_2_1 : 8;
        uint8 LeftListen_EchoHigh_3_2 : 8;
        uint8 LeftListen_EchoHigh_3_1 : 8;
        uint8 RightListen_Distance_0_2 : 8;
        uint8 RightListen_Distance_0_1 : 8;
        uint8 RightListen_Distance_1_2 : 8;
        uint8 RightListen_Distance_1_1 : 8;
        uint8 RightListen_Distance_2_2 : 8;
        uint8 RightListen_Distance_2_1 : 8;
        uint8 RightListen_Distance_3_2 : 8;
        uint8 RightListen_Distance_3_1 : 8;
        uint8 RightListen_EchoHigh_0_2 : 8;
        uint8 RightListen_EchoHigh_0_1 : 8;
        uint8 RightListen_EchoHigh_1_2 : 8;
        uint8 RightListen_EchoHigh_1_1 : 8;
        uint8 RightListen_EchoHigh_2_2 : 8;
        uint8 RightListen_EchoHigh_2_1 : 8;
        uint8 RightListen_EchoHigh_3_2 : 8;
        uint8 RightListen_EchoHigh_3_1 : 8;
        uint8 MasterEchoCnt : 3;
        uint8 LeftEchoCnt : 3;
        uint8 : 2;
        uint8 RightEchoCnt : 3;
        uint8 NFD_Flag : 1;
        uint8 : 4;
        uint8 NFD_Dis_2 : 8;
        uint8 NFD_Dis_1 : 8;
        uint8 Ring_Fre_2 : 8;
        uint8 Ring_Fre_1 : 8;
        uint8 NoiseCnt : 8;
        uint8 NoiseSum_2 : 8;
        uint8 : 4;
        uint8 NoiseSum_1 : 4;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
    }bits;
}FL_EchoData_6F5_ecu0_0_MSGType;
typedef union
{
    uint8 byte[64];
    struct
    {
        uint8 UpdateFlg : 1;
        uint8 SnsWorkSts : 2;
        uint8 Master_Meas_Type : 3;
        uint8 EepromErrFlg : 1;
        uint8 : 1;
        uint8 Master_Rtm_2 : 8;
        uint8 Master_Rtm_1 : 8;
        uint8 Master_Distance_0_2 : 8;
        uint8 Master_Distance_0_1 : 8;
        uint8 Master_Distance_1_2 : 8;
        uint8 Master_Distance_1_1 : 8;
        uint8 Master_Distance_2_2 : 8;
        uint8 Master_Distance_2_1 : 8;
        uint8 Master_Distance_3_2 : 8;
        uint8 Master_Distance_3_1 : 8;
        uint8 Master_EchoHigh_0_2 : 8;
        uint8 Master_EchoHigh_0_1 : 8;
        uint8 Master_EchoHigh_1_2 : 8;
        uint8 Master_EchoHigh_1_1 : 8;
        uint8 Master_EchoHigh_2_2 : 8;
        uint8 Master_EchoHigh_2_1 : 8;
        uint8 Master_EchoHigh_3_2 : 8;
        uint8 Master_EchoHigh_3_1 : 8;
        uint8 LeftListen_Distance_0_2 : 8;
        uint8 LeftListen_Distance_0_1 : 8;
        uint8 LeftListen_Distance_1_2 : 8;
        uint8 LeftListen_Distance_1_1 : 8;
        uint8 LeftListen_Distance_2_2 : 8;
        uint8 LeftListen_Distance_2_1 : 8;
        uint8 LeftListen_Distance_3_2 : 8;
        uint8 LeftListen_Distance_3_1 : 8;
        uint8 LeftListen_EchoHigh_0_2 : 8;
        uint8 LeftListen_EchoHigh_0_1 : 8;
        uint8 LeftListen_EchoHigh_1_2 : 8;
        uint8 LeftListen_EchoHigh_1_1 : 8;
        uint8 LeftListen_EchoHigh_2_2 : 8;
        uint8 LeftListen_EchoHigh_2_1 : 8;
        uint8 LeftListen_EchoHigh_3_2 : 8;
        uint8 LeftListen_EchoHigh_3_1 : 8;
        uint8 RightListen_Distance_0_2 : 8;
        uint8 RightListen_Distance_0_1 : 8;
        uint8 RightListen_Distance_1_2 : 8;
        uint8 RightListen_Distance_1_1 : 8;
        uint8 RightListen_Distance_2_2 : 8;
        uint8 RightListen_Distance_2_1 : 8;
        uint8 RightListen_Distance_3_2 : 8;
        uint8 RightListen_Distance_3_1 : 8;
        uint8 RightListen_EchoHigh_0_2 : 8;
        uint8 RightListen_EchoHigh_0_1 : 8;
        uint8 RightListen_EchoHigh_1_2 : 8;
        uint8 RightListen_EchoHigh_1_1 : 8;
        uint8 RightListen_EchoHigh_2_2 : 8;
        uint8 RightListen_EchoHigh_2_1 : 8;
        uint8 RightListen_EchoHigh_3_2 : 8;
        uint8 RightListen_EchoHigh_3_1 : 8;
        uint8 MasterEchoCnt : 3;
        uint8 LeftEchoCnt : 3;
        uint8 : 2;
        uint8 RightEchoCnt : 3;
        uint8 NFD_Flag : 1;
        uint8 : 4;
        uint8 NFD_Dis_2 : 8;
        uint8 NFD_Dis_1 : 8;
        uint8 Ring_Fre_2 : 8;
        uint8 Ring_Fre_1 : 8;
        uint8 NoiseCnt : 8;
        uint8 NoiseSum_2 : 8;
        uint8 : 4;
        uint8 NoiseSum_1 : 4;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
    }bits;
}FML_EchoData_6F6_ecu0_0_MSGType;
typedef union
{
    uint8 byte[64];
    struct
    {
        uint8 UpdateFlg : 1;
        uint8 SnsWorkSts : 2;
        uint8 Master_Meas_Type : 3;
        uint8 EepromErrFlg : 1;
        uint8 : 1;
        uint8 Master_Rtm_2 : 8;
        uint8 Master_Rtm_1 : 8;
        uint8 Master_Distance_0_2 : 8;
        uint8 Master_Distance_0_1 : 8;
        uint8 Master_Distance_1_2 : 8;
        uint8 Master_Distance_1_1 : 8;
        uint8 Master_Distance_2_2 : 8;
        uint8 Master_Distance_2_1 : 8;
        uint8 Master_Distance_3_2 : 8;
        uint8 Master_Distance_3_1 : 8;
        uint8 Master_EchoHigh_0_2 : 8;
        uint8 Master_EchoHigh_0_1 : 8;
        uint8 Master_EchoHigh_1_2 : 8;
        uint8 Master_EchoHigh_1_1 : 8;
        uint8 Master_EchoHigh_2_2 : 8;
        uint8 Master_EchoHigh_2_1 : 8;
        uint8 Master_EchoHigh_3_2 : 8;
        uint8 Master_EchoHigh_3_1 : 8;
        uint8 LeftListen_Distance_0_2 : 8;
        uint8 LeftListen_Distance_0_1 : 8;
        uint8 LeftListen_Distance_1_2 : 8;
        uint8 LeftListen_Distance_1_1 : 8;
        uint8 LeftListen_Distance_2_2 : 8;
        uint8 LeftListen_Distance_2_1 : 8;
        uint8 LeftListen_Distance_3_2 : 8;
        uint8 LeftListen_Distance_3_1 : 8;
        uint8 LeftListen_EchoHigh_0_2 : 8;
        uint8 LeftListen_EchoHigh_0_1 : 8;
        uint8 LeftListen_EchoHigh_1_2 : 8;
        uint8 LeftListen_EchoHigh_1_1 : 8;
        uint8 LeftListen_EchoHigh_2_2 : 8;
        uint8 LeftListen_EchoHigh_2_1 : 8;
        uint8 LeftListen_EchoHigh_3_2 : 8;
        uint8 LeftListen_EchoHigh_3_1 : 8;
        uint8 RightListen_Distance_0_2 : 8;
        uint8 RightListen_Distance_0_1 : 8;
        uint8 RightListen_Distance_1_2 : 8;
        uint8 RightListen_Distance_1_1 : 8;
        uint8 RightListen_Distance_2_2 : 8;
        uint8 RightListen_Distance_2_1 : 8;
        uint8 RightListen_Distance_3_2 : 8;
        uint8 RightListen_Distance_3_1 : 8;
        uint8 RightListen_EchoHigh_0_2 : 8;
        uint8 RightListen_EchoHigh_0_1 : 8;
        uint8 RightListen_EchoHigh_1_2 : 8;
        uint8 RightListen_EchoHigh_1_1 : 8;
        uint8 RightListen_EchoHigh_2_2 : 8;
        uint8 RightListen_EchoHigh_2_1 : 8;
        uint8 RightListen_EchoHigh_3_2 : 8;
        uint8 RightListen_EchoHigh_3_1 : 8;
        uint8 MasterEchoCnt : 3;
        uint8 LeftEchoCnt : 3;
        uint8 : 2;
        uint8 RightEchoCnt : 3;
        uint8 NFD_Flag : 1;
        uint8 : 4;
        uint8 NFD_Dis_2 : 8;
        uint8 NFD_Dis_1 : 8;
        uint8 Ring_Fre_2 : 8;
        uint8 Ring_Fre_1 : 8;
        uint8 NoiseCnt : 8;
        uint8 NoiseSum_2 : 8;
        uint8 : 4;
        uint8 NoiseSum_1 : 4;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
    }bits;
}FMR_EchoData_6F7_ecu0_0_MSGType;
typedef union
{
    uint8 byte[64];
    struct
    {
        uint8 UpdateFlg : 1;
        uint8 SnsWorkSts : 2;
        uint8 Master_Meas_Type : 3;
        uint8 EepromErrFlg : 1;
        uint8 : 1;
        uint8 Master_Rtm_2 : 8;
        uint8 Master_Rtm_1 : 8;
        uint8 Master_Distance_0_2 : 8;
        uint8 Master_Distance_0_1 : 8;
        uint8 Master_Distance_1_2 : 8;
        uint8 Master_Distance_1_1 : 8;
        uint8 Master_Distance_2_2 : 8;
        uint8 Master_Distance_2_1 : 8;
        uint8 Master_Distance_3_2 : 8;
        uint8 Master_Distance_3_1 : 8;
        uint8 Master_EchoHigh_0_2 : 8;
        uint8 Master_EchoHigh_0_1 : 8;
        uint8 Master_EchoHigh_1_2 : 8;
        uint8 Master_EchoHigh_1_1 : 8;
        uint8 Master_EchoHigh_2_2 : 8;
        uint8 Master_EchoHigh_2_1 : 8;
        uint8 Master_EchoHigh_3_2 : 8;
        uint8 Master_EchoHigh_3_1 : 8;
        uint8 LeftListen_Distance_0_2 : 8;
        uint8 LeftListen_Distance_0_1 : 8;
        uint8 LeftListen_Distance_1_2 : 8;
        uint8 LeftListen_Distance_1_1 : 8;
        uint8 LeftListen_Distance_2_2 : 8;
        uint8 LeftListen_Distance_2_1 : 8;
        uint8 LeftListen_Distance_3_2 : 8;
        uint8 LeftListen_Distance_3_1 : 8;
        uint8 LeftListen_EchoHigh_0_2 : 8;
        uint8 LeftListen_EchoHigh_0_1 : 8;
        uint8 LeftListen_EchoHigh_1_2 : 8;
        uint8 LeftListen_EchoHigh_1_1 : 8;
        uint8 LeftListen_EchoHigh_2_2 : 8;
        uint8 LeftListen_EchoHigh_2_1 : 8;
        uint8 LeftListen_EchoHigh_3_2 : 8;
        uint8 LeftListen_EchoHigh_3_1 : 8;
        uint8 RightListen_Distance_0_2 : 8;
        uint8 RightListen_Distance_0_1 : 8;
        uint8 RightListen_Distance_1_2 : 8;
        uint8 RightListen_Distance_1_1 : 8;
        uint8 RightListen_Distance_2_2 : 8;
        uint8 RightListen_Distance_2_1 : 8;
        uint8 RightListen_Distance_3_2 : 8;
        uint8 RightListen_Distance_3_1 : 8;
        uint8 RightListen_EchoHigh_0_2 : 8;
        uint8 RightListen_EchoHigh_0_1 : 8;
        uint8 RightListen_EchoHigh_1_2 : 8;
        uint8 RightListen_EchoHigh_1_1 : 8;
        uint8 RightListen_EchoHigh_2_2 : 8;
        uint8 RightListen_EchoHigh_2_1 : 8;
        uint8 RightListen_EchoHigh_3_2 : 8;
        uint8 RightListen_EchoHigh_3_1 : 8;
        uint8 MasterEchoCnt : 3;
        uint8 LeftEchoCnt : 3;
        uint8 : 2;
        uint8 RightEchoCnt : 3;
        uint8 NFD_Flag : 1;
        uint8 : 4;
        uint8 NFD_Dis_2 : 8;
        uint8 NFD_Dis_1 : 8;
        uint8 Ring_Fre_2 : 8;
        uint8 Ring_Fre_1 : 8;
        uint8 NoiseCnt : 8;
        uint8 NoiseSum_2 : 8;
        uint8 : 4;
        uint8 NoiseSum_1 : 4;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
    }bits;
}FR_EchoData_6F8_ecu0_0_MSGType;
typedef union
{
    uint8 byte[64];
    struct
    {
        uint8 UpdateFlg : 1;
        uint8 SnsWorkSts : 2;
        uint8 Master_Meas_Type : 3;
        uint8 EepromErrFlg : 1;
        uint8 : 1;
        uint8 Master_Rtm_2 : 8;
        uint8 Master_Rtm_1 : 8;
        uint8 Master_Distance_0_2 : 8;
        uint8 Master_Distance_0_1 : 8;
        uint8 Master_Distance_1_2 : 8;
        uint8 Master_Distance_1_1 : 8;
        uint8 Master_Distance_2_2 : 8;
        uint8 Master_Distance_2_1 : 8;
        uint8 Master_Distance_3_2 : 8;
        uint8 Master_Distance_3_1 : 8;
        uint8 Master_EchoHigh_0_2 : 8;
        uint8 Master_EchoHigh_0_1 : 8;
        uint8 Master_EchoHigh_1_2 : 8;
        uint8 Master_EchoHigh_1_1 : 8;
        uint8 Master_EchoHigh_2_2 : 8;
        uint8 Master_EchoHigh_2_1 : 8;
        uint8 Master_EchoHigh_3_2 : 8;
        uint8 Master_EchoHigh_3_1 : 8;
        uint8 LeftListen_Distance_0_2 : 8;
        uint8 LeftListen_Distance_0_1 : 8;
        uint8 LeftListen_Distance_1_2 : 8;
        uint8 LeftListen_Distance_1_1 : 8;
        uint8 LeftListen_Distance_2_2 : 8;
        uint8 LeftListen_Distance_2_1 : 8;
        uint8 LeftListen_Distance_3_2 : 8;
        uint8 LeftListen_Distance_3_1 : 8;
        uint8 LeftListen_EchoHigh_0_2 : 8;
        uint8 LeftListen_EchoHigh_0_1 : 8;
        uint8 LeftListen_EchoHigh_1_2 : 8;
        uint8 LeftListen_EchoHigh_1_1 : 8;
        uint8 LeftListen_EchoHigh_2_2 : 8;
        uint8 LeftListen_EchoHigh_2_1 : 8;
        uint8 LeftListen_EchoHigh_3_2 : 8;
        uint8 LeftListen_EchoHigh_3_1 : 8;
        uint8 RightListen_Distance_0_2 : 8;
        uint8 RightListen_Distance_0_1 : 8;
        uint8 RightListen_Distance_1_2 : 8;
        uint8 RightListen_Distance_1_1 : 8;
        uint8 RightListen_Distance_2_2 : 8;
        uint8 RightListen_Distance_2_1 : 8;
        uint8 RightListen_Distance_3_2 : 8;
        uint8 RightListen_Distance_3_1 : 8;
        uint8 RightListen_EchoHigh_0_2 : 8;
        uint8 RightListen_EchoHigh_0_1 : 8;
        uint8 RightListen_EchoHigh_1_2 : 8;
        uint8 RightListen_EchoHigh_1_1 : 8;
        uint8 RightListen_EchoHigh_2_2 : 8;
        uint8 RightListen_EchoHigh_2_1 : 8;
        uint8 RightListen_EchoHigh_3_2 : 8;
        uint8 RightListen_EchoHigh_3_1 : 8;
        uint8 MasterEchoCnt : 3;
        uint8 LeftEchoCnt : 3;
        uint8 : 2;
        uint8 RightEchoCnt : 3;
        uint8 NFD_Flag : 1;
        uint8 : 4;
        uint8 NFD_Dis_2 : 8;
        uint8 NFD_Dis_1 : 8;
        uint8 Ring_Fre_2 : 8;
        uint8 Ring_Fre_1 : 8;
        uint8 NoiseCnt : 8;
        uint8 NoiseSum_2 : 8;
        uint8 : 4;
        uint8 NoiseSum_1 : 4;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
    }bits;
}FRS_EchoData_6F9_ecu0_0_MSGType;
typedef union
{
    uint8 byte[64];
    struct
    {
        uint8 UpdateFlg : 1;
        uint8 SnsWorkSts : 2;
        uint8 Master_Meas_Type : 3;
        uint8 EepromErrFlg : 1;
        uint8 : 1;
        uint8 Master_Rtm_2 : 8;
        uint8 Master_Rtm_1 : 8;
        uint8 Master_Distance_0_2 : 8;
        uint8 Master_Distance_0_1 : 8;
        uint8 Master_Distance_1_2 : 8;
        uint8 Master_Distance_1_1 : 8;
        uint8 Master_Distance_2_2 : 8;
        uint8 Master_Distance_2_1 : 8;
        uint8 Master_Distance_3_2 : 8;
        uint8 Master_Distance_3_1 : 8;
        uint8 Master_EchoHigh_0_2 : 8;
        uint8 Master_EchoHigh_0_1 : 8;
        uint8 Master_EchoHigh_1_2 : 8;
        uint8 Master_EchoHigh_1_1 : 8;
        uint8 Master_EchoHigh_2_2 : 8;
        uint8 Master_EchoHigh_2_1 : 8;
        uint8 Master_EchoHigh_3_2 : 8;
        uint8 Master_EchoHigh_3_1 : 8;
        uint8 LeftListen_Distance_0_2 : 8;
        uint8 LeftListen_Distance_0_1 : 8;
        uint8 LeftListen_Distance_1_2 : 8;
        uint8 LeftListen_Distance_1_1 : 8;
        uint8 LeftListen_Distance_2_2 : 8;
        uint8 LeftListen_Distance_2_1 : 8;
        uint8 LeftListen_Distance_3_2 : 8;
        uint8 LeftListen_Distance_3_1 : 8;
        uint8 LeftListen_EchoHigh_0_2 : 8;
        uint8 LeftListen_EchoHigh_0_1 : 8;
        uint8 LeftListen_EchoHigh_1_2 : 8;
        uint8 LeftListen_EchoHigh_1_1 : 8;
        uint8 LeftListen_EchoHigh_2_2 : 8;
        uint8 LeftListen_EchoHigh_2_1 : 8;
        uint8 LeftListen_EchoHigh_3_2 : 8;
        uint8 LeftListen_EchoHigh_3_1 : 8;
        uint8 RightListen_Distance_0_2 : 8;
        uint8 RightListen_Distance_0_1 : 8;
        uint8 RightListen_Distance_1_2 : 8;
        uint8 RightListen_Distance_1_1 : 8;
        uint8 RightListen_Distance_2_2 : 8;
        uint8 RightListen_Distance_2_1 : 8;
        uint8 RightListen_Distance_3_2 : 8;
        uint8 RightListen_Distance_3_1 : 8;
        uint8 RightListen_EchoHigh_0_2 : 8;
        uint8 RightListen_EchoHigh_0_1 : 8;
        uint8 RightListen_EchoHigh_1_2 : 8;
        uint8 RightListen_EchoHigh_1_1 : 8;
        uint8 RightListen_EchoHigh_2_2 : 8;
        uint8 RightListen_EchoHigh_2_1 : 8;
        uint8 RightListen_EchoHigh_3_2 : 8;
        uint8 RightListen_EchoHigh_3_1 : 8;
        uint8 MasterEchoCnt : 3;
        uint8 LeftEchoCnt : 3;
        uint8 : 2;
        uint8 RightEchoCnt : 3;
        uint8 NFD_Flag : 1;
        uint8 : 4;
        uint8 NFD_Dis_2 : 8;
        uint8 NFD_Dis_1 : 8;
        uint8 Ring_Fre_2 : 8;
        uint8 Ring_Fre_1 : 8;
        uint8 NoiseCnt : 8;
        uint8 NoiseSum_2 : 8;
        uint8 : 4;
        uint8 NoiseSum_1 : 4;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
    }bits;
}RRS_EchoData_6FA_ecu0_0_MSGType;
typedef union
{
    uint8 byte[64];
    struct
    {
        uint8 UpdateFlg : 1;
        uint8 SnsWorkSts : 2;
        uint8 Master_Meas_Type : 3;
        uint8 EepromErrFlg : 1;
        uint8 : 1;
        uint8 Master_Rtm_2 : 8;
        uint8 Master_Rtm_1 : 8;
        uint8 Master_Distance_0_2 : 8;
        uint8 Master_Distance_0_1 : 8;
        uint8 Master_Distance_1_2 : 8;
        uint8 Master_Distance_1_1 : 8;
        uint8 Master_Distance_2_2 : 8;
        uint8 Master_Distance_2_1 : 8;
        uint8 Master_Distance_3_2 : 8;
        uint8 Master_Distance_3_1 : 8;
        uint8 Master_EchoHigh_0_2 : 8;
        uint8 Master_EchoHigh_0_1 : 8;
        uint8 Master_EchoHigh_1_2 : 8;
        uint8 Master_EchoHigh_1_1 : 8;
        uint8 Master_EchoHigh_2_2 : 8;
        uint8 Master_EchoHigh_2_1 : 8;
        uint8 Master_EchoHigh_3_2 : 8;
        uint8 Master_EchoHigh_3_1 : 8;
        uint8 LeftListen_Distance_0_2 : 8;
        uint8 LeftListen_Distance_0_1 : 8;
        uint8 LeftListen_Distance_1_2 : 8;
        uint8 LeftListen_Distance_1_1 : 8;
        uint8 LeftListen_Distance_2_2 : 8;
        uint8 LeftListen_Distance_2_1 : 8;
        uint8 LeftListen_Distance_3_2 : 8;
        uint8 LeftListen_Distance_3_1 : 8;
        uint8 LeftListen_EchoHigh_0_2 : 8;
        uint8 LeftListen_EchoHigh_0_1 : 8;
        uint8 LeftListen_EchoHigh_1_2 : 8;
        uint8 LeftListen_EchoHigh_1_1 : 8;
        uint8 LeftListen_EchoHigh_2_2 : 8;
        uint8 LeftListen_EchoHigh_2_1 : 8;
        uint8 LeftListen_EchoHigh_3_2 : 8;
        uint8 LeftListen_EchoHigh_3_1 : 8;
        uint8 RightListen_Distance_0_2 : 8;
        uint8 RightListen_Distance_0_1 : 8;
        uint8 RightListen_Distance_1_2 : 8;
        uint8 RightListen_Distance_1_1 : 8;
        uint8 RightListen_Distance_2_2 : 8;
        uint8 RightListen_Distance_2_1 : 8;
        uint8 RightListen_Distance_3_2 : 8;
        uint8 RightListen_Distance_3_1 : 8;
        uint8 RightListen_EchoHigh_0_2 : 8;
        uint8 RightListen_EchoHigh_0_1 : 8;
        uint8 RightListen_EchoHigh_1_2 : 8;
        uint8 RightListen_EchoHigh_1_1 : 8;
        uint8 RightListen_EchoHigh_2_2 : 8;
        uint8 RightListen_EchoHigh_2_1 : 8;
        uint8 RightListen_EchoHigh_3_2 : 8;
        uint8 RightListen_EchoHigh_3_1 : 8;
        uint8 MasterEchoCnt : 3;
        uint8 LeftEchoCnt : 3;
        uint8 : 2;
        uint8 RightEchoCnt : 3;
        uint8 NFD_Flag : 1;
        uint8 : 4;
        uint8 NFD_Dis_2 : 8;
        uint8 NFD_Dis_1 : 8;
        uint8 Ring_Fre_2 : 8;
        uint8 Ring_Fre_1 : 8;
        uint8 NoiseCnt : 8;
        uint8 NoiseSum_2 : 8;
        uint8 : 4;
        uint8 NoiseSum_1 : 4;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
    }bits;
}RR_EchoData_6FB_ecu0_0_MSGType;
typedef union
{
    uint8 byte[64];
    struct
    {
        uint8 UpdateFlg : 1;
        uint8 SnsWorkSts : 2;
        uint8 Master_Meas_Type : 3;
        uint8 EepromErrFlg : 1;
        uint8 : 1;
        uint8 Master_Rtm_2 : 8;
        uint8 Master_Rtm_1 : 8;
        uint8 Master_Distance_0_2 : 8;
        uint8 Master_Distance_0_1 : 8;
        uint8 Master_Distance_1_2 : 8;
        uint8 Master_Distance_1_1 : 8;
        uint8 Master_Distance_2_2 : 8;
        uint8 Master_Distance_2_1 : 8;
        uint8 Master_Distance_3_2 : 8;
        uint8 Master_Distance_3_1 : 8;
        uint8 Master_EchoHigh_0_2 : 8;
        uint8 Master_EchoHigh_0_1 : 8;
        uint8 Master_EchoHigh_1_2 : 8;
        uint8 Master_EchoHigh_1_1 : 8;
        uint8 Master_EchoHigh_2_2 : 8;
        uint8 Master_EchoHigh_2_1 : 8;
        uint8 Master_EchoHigh_3_2 : 8;
        uint8 Master_EchoHigh_3_1 : 8;
        uint8 LeftListen_Distance_0_2 : 8;
        uint8 LeftListen_Distance_0_1 : 8;
        uint8 LeftListen_Distance_1_2 : 8;
        uint8 LeftListen_Distance_1_1 : 8;
        uint8 LeftListen_Distance_2_2 : 8;
        uint8 LeftListen_Distance_2_1 : 8;
        uint8 LeftListen_Distance_3_2 : 8;
        uint8 LeftListen_Distance_3_1 : 8;
        uint8 LeftListen_EchoHigh_0_2 : 8;
        uint8 LeftListen_EchoHigh_0_1 : 8;
        uint8 LeftListen_EchoHigh_1_2 : 8;
        uint8 LeftListen_EchoHigh_1_1 : 8;
        uint8 LeftListen_EchoHigh_2_2 : 8;
        uint8 LeftListen_EchoHigh_2_1 : 8;
        uint8 LeftListen_EchoHigh_3_2 : 8;
        uint8 LeftListen_EchoHigh_3_1 : 8;
        uint8 RightListen_Distance_0_2 : 8;
        uint8 RightListen_Distance_0_1 : 8;
        uint8 RightListen_Distance_1_2 : 8;
        uint8 RightListen_Distance_1_1 : 8;
        uint8 RightListen_Distance_2_2 : 8;
        uint8 RightListen_Distance_2_1 : 8;
        uint8 RightListen_Distance_3_2 : 8;
        uint8 RightListen_Distance_3_1 : 8;
        uint8 RightListen_EchoHigh_0_2 : 8;
        uint8 RightListen_EchoHigh_0_1 : 8;
        uint8 RightListen_EchoHigh_1_2 : 8;
        uint8 RightListen_EchoHigh_1_1 : 8;
        uint8 RightListen_EchoHigh_2_2 : 8;
        uint8 RightListen_EchoHigh_2_1 : 8;
        uint8 RightListen_EchoHigh_3_2 : 8;
        uint8 RightListen_EchoHigh_3_1 : 8;
        uint8 MasterEchoCnt : 3;
        uint8 LeftEchoCnt : 3;
        uint8 : 2;
        uint8 RightEchoCnt : 3;
        uint8 NFD_Flag : 1;
        uint8 : 4;
        uint8 NFD_Dis_2 : 8;
        uint8 NFD_Dis_1 : 8;
        uint8 Ring_Fre_2 : 8;
        uint8 Ring_Fre_1 : 8;
        uint8 NoiseCnt : 8;
        uint8 NoiseSum_2 : 8;
        uint8 : 4;
        uint8 NoiseSum_1 : 4;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
    }bits;
}RMR_EchoData_6FC_ecu0_0_MSGType;
typedef union
{
    uint8 byte[64];
    struct
    {
        uint8 UpdateFlg : 1;
        uint8 SnsWorkSts : 2;
        uint8 Master_Meas_Type : 3;
        uint8 EepromErrFlg : 1;
        uint8 : 1;
        uint8 Master_Rtm_2 : 8;
        uint8 Master_Rtm_1 : 8;
        uint8 Master_Distance_0_2 : 8;
        uint8 Master_Distance_0_1 : 8;
        uint8 Master_Distance_1_2 : 8;
        uint8 Master_Distance_1_1 : 8;
        uint8 Master_Distance_2_2 : 8;
        uint8 Master_Distance_2_1 : 8;
        uint8 Master_Distance_3_2 : 8;
        uint8 Master_Distance_3_1 : 8;
        uint8 Master_EchoHigh_0_2 : 8;
        uint8 Master_EchoHigh_0_1 : 8;
        uint8 Master_EchoHigh_1_2 : 8;
        uint8 Master_EchoHigh_1_1 : 8;
        uint8 Master_EchoHigh_2_2 : 8;
        uint8 Master_EchoHigh_2_1 : 8;
        uint8 Master_EchoHigh_3_2 : 8;
        uint8 Master_EchoHigh_3_1 : 8;
        uint8 LeftListen_Distance_0_2 : 8;
        uint8 LeftListen_Distance_0_1 : 8;
        uint8 LeftListen_Distance_1_2 : 8;
        uint8 LeftListen_Distance_1_1 : 8;
        uint8 LeftListen_Distance_2_2 : 8;
        uint8 LeftListen_Distance_2_1 : 8;
        uint8 LeftListen_Distance_3_2 : 8;
        uint8 LeftListen_Distance_3_1 : 8;
        uint8 LeftListen_EchoHigh_0_2 : 8;
        uint8 LeftListen_EchoHigh_0_1 : 8;
        uint8 LeftListen_EchoHigh_1_2 : 8;
        uint8 LeftListen_EchoHigh_1_1 : 8;
        uint8 LeftListen_EchoHigh_2_2 : 8;
        uint8 LeftListen_EchoHigh_2_1 : 8;
        uint8 LeftListen_EchoHigh_3_2 : 8;
        uint8 LeftListen_EchoHigh_3_1 : 8;
        uint8 RightListen_Distance_0_2 : 8;
        uint8 RightListen_Distance_0_1 : 8;
        uint8 RightListen_Distance_1_2 : 8;
        uint8 RightListen_Distance_1_1 : 8;
        uint8 RightListen_Distance_2_2 : 8;
        uint8 RightListen_Distance_2_1 : 8;
        uint8 RightListen_Distance_3_2 : 8;
        uint8 RightListen_Distance_3_1 : 8;
        uint8 RightListen_EchoHigh_0_2 : 8;
        uint8 RightListen_EchoHigh_0_1 : 8;
        uint8 RightListen_EchoHigh_1_2 : 8;
        uint8 RightListen_EchoHigh_1_1 : 8;
        uint8 RightListen_EchoHigh_2_2 : 8;
        uint8 RightListen_EchoHigh_2_1 : 8;
        uint8 RightListen_EchoHigh_3_2 : 8;
        uint8 RightListen_EchoHigh_3_1 : 8;
        uint8 MasterEchoCnt : 3;
        uint8 LeftEchoCnt : 3;
        uint8 : 2;
        uint8 RightEchoCnt : 3;
        uint8 NFD_Flag : 1;
        uint8 : 4;
        uint8 NFD_Dis_2 : 8;
        uint8 NFD_Dis_1 : 8;
        uint8 Ring_Fre_2 : 8;
        uint8 Ring_Fre_1 : 8;
        uint8 NoiseCnt : 8;
        uint8 NoiseSum_2 : 8;
        uint8 : 4;
        uint8 NoiseSum_1 : 4;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
    }bits;
}RML_EchoData_6FD_ecu0_0_MSGType;
typedef union
{
    uint8 byte[64];
    struct
    {
        uint8 UpdateFlg : 1;
        uint8 SnsWorkSts : 2;
        uint8 Master_Meas_Type : 3;
        uint8 EepromErrFlg : 1;
        uint8 : 1;
        uint8 Master_Rtm_2 : 8;
        uint8 Master_Rtm_1 : 8;
        uint8 Master_Distance_0_2 : 8;
        uint8 Master_Distance_0_1 : 8;
        uint8 Master_Distance_1_2 : 8;
        uint8 Master_Distance_1_1 : 8;
        uint8 Master_Distance_2_2 : 8;
        uint8 Master_Distance_2_1 : 8;
        uint8 Master_Distance_3_2 : 8;
        uint8 Master_Distance_3_1 : 8;
        uint8 Master_EchoHigh_0_2 : 8;
        uint8 Master_EchoHigh_0_1 : 8;
        uint8 Master_EchoHigh_1_2 : 8;
        uint8 Master_EchoHigh_1_1 : 8;
        uint8 Master_EchoHigh_2_2 : 8;
        uint8 Master_EchoHigh_2_1 : 8;
        uint8 Master_EchoHigh_3_2 : 8;
        uint8 Master_EchoHigh_3_1 : 8;
        uint8 LeftListen_Distance_0_2 : 8;
        uint8 LeftListen_Distance_0_1 : 8;
        uint8 LeftListen_Distance_1_2 : 8;
        uint8 LeftListen_Distance_1_1 : 8;
        uint8 LeftListen_Distance_2_2 : 8;
        uint8 LeftListen_Distance_2_1 : 8;
        uint8 LeftListen_Distance_3_2 : 8;
        uint8 LeftListen_Distance_3_1 : 8;
        uint8 LeftListen_EchoHigh_0_2 : 8;
        uint8 LeftListen_EchoHigh_0_1 : 8;
        uint8 LeftListen_EchoHigh_1_2 : 8;
        uint8 LeftListen_EchoHigh_1_1 : 8;
        uint8 LeftListen_EchoHigh_2_2 : 8;
        uint8 LeftListen_EchoHigh_2_1 : 8;
        uint8 LeftListen_EchoHigh_3_2 : 8;
        uint8 LeftListen_EchoHigh_3_1 : 8;
        uint8 RightListen_Distance_0_2 : 8;
        uint8 RightListen_Distance_0_1 : 8;
        uint8 RightListen_Distance_1_2 : 8;
        uint8 RightListen_Distance_1_1 : 8;
        uint8 RightListen_Distance_2_2 : 8;
        uint8 RightListen_Distance_2_1 : 8;
        uint8 RightListen_Distance_3_2 : 8;
        uint8 RightListen_Distance_3_1 : 8;
        uint8 RightListen_EchoHigh_0_2 : 8;
        uint8 RightListen_EchoHigh_0_1 : 8;
        uint8 RightListen_EchoHigh_1_2 : 8;
        uint8 RightListen_EchoHigh_1_1 : 8;
        uint8 RightListen_EchoHigh_2_2 : 8;
        uint8 RightListen_EchoHigh_2_1 : 8;
        uint8 RightListen_EchoHigh_3_2 : 8;
        uint8 RightListen_EchoHigh_3_1 : 8;
        uint8 MasterEchoCnt : 3;
        uint8 LeftEchoCnt : 3;
        uint8 : 2;
        uint8 RightEchoCnt : 3;
        uint8 NFD_Flag : 1;
        uint8 : 4;
        uint8 NFD_Dis_2 : 8;
        uint8 NFD_Dis_1 : 8;
        uint8 Ring_Fre_2 : 8;
        uint8 Ring_Fre_1 : 8;
        uint8 NoiseCnt : 8;
        uint8 NoiseSum_2 : 8;
        uint8 : 4;
        uint8 NoiseSum_1 : 4;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
    }bits;
}RL_EchoData_6FE_ecu0_0_MSGType;
typedef union
{
    uint8 byte[64];
    struct
    {
        uint8 UpdateFlg : 1;
        uint8 SnsWorkSts : 2;
        uint8 Master_Meas_Type : 3;
        uint8 EepromErrFlg : 1;
        uint8 : 1;
        uint8 Master_Rtm_2 : 8;
        uint8 Master_Rtm_1 : 8;
        uint8 Master_Distance_0_2 : 8;
        uint8 Master_Distance_0_1 : 8;
        uint8 Master_Distance_1_2 : 8;
        uint8 Master_Distance_1_1 : 8;
        uint8 Master_Distance_2_2 : 8;
        uint8 Master_Distance_2_1 : 8;
        uint8 Master_Distance_3_2 : 8;
        uint8 Master_Distance_3_1 : 8;
        uint8 Master_EchoHigh_0_2 : 8;
        uint8 Master_EchoHigh_0_1 : 8;
        uint8 Master_EchoHigh_1_2 : 8;
        uint8 Master_EchoHigh_1_1 : 8;
        uint8 Master_EchoHigh_2_2 : 8;
        uint8 Master_EchoHigh_2_1 : 8;
        uint8 Master_EchoHigh_3_2 : 8;
        uint8 Master_EchoHigh_3_1 : 8;
        uint8 LeftListen_Distance_0_2 : 8;
        uint8 LeftListen_Distance_0_1 : 8;
        uint8 LeftListen_Distance_1_2 : 8;
        uint8 LeftListen_Distance_1_1 : 8;
        uint8 LeftListen_Distance_2_2 : 8;
        uint8 LeftListen_Distance_2_1 : 8;
        uint8 LeftListen_Distance_3_2 : 8;
        uint8 LeftListen_Distance_3_1 : 8;
        uint8 LeftListen_EchoHigh_0_2 : 8;
        uint8 LeftListen_EchoHigh_0_1 : 8;
        uint8 LeftListen_EchoHigh_1_2 : 8;
        uint8 LeftListen_EchoHigh_1_1 : 8;
        uint8 LeftListen_EchoHigh_2_2 : 8;
        uint8 LeftListen_EchoHigh_2_1 : 8;
        uint8 LeftListen_EchoHigh_3_2 : 8;
        uint8 LeftListen_EchoHigh_3_1 : 8;
        uint8 RightListen_Distance_0_2 : 8;
        uint8 RightListen_Distance_0_1 : 8;
        uint8 RightListen_Distance_1_2 : 8;
        uint8 RightListen_Distance_1_1 : 8;
        uint8 RightListen_Distance_2_2 : 8;
        uint8 RightListen_Distance_2_1 : 8;
        uint8 RightListen_Distance_3_2 : 8;
        uint8 RightListen_Distance_3_1 : 8;
        uint8 RightListen_EchoHigh_0_2 : 8;
        uint8 RightListen_EchoHigh_0_1 : 8;
        uint8 RightListen_EchoHigh_1_2 : 8;
        uint8 RightListen_EchoHigh_1_1 : 8;
        uint8 RightListen_EchoHigh_2_2 : 8;
        uint8 RightListen_EchoHigh_2_1 : 8;
        uint8 RightListen_EchoHigh_3_2 : 8;
        uint8 RightListen_EchoHigh_3_1 : 8;
        uint8 MasterEchoCnt : 3;
        uint8 LeftEchoCnt : 3;
        uint8 : 2;
        uint8 RightEchoCnt : 3;
        uint8 NFD_Flag : 1;
        uint8 : 4;
        uint8 NFD_Dis_2 : 8;
        uint8 NFD_Dis_1 : 8;
        uint8 Ring_Fre_2 : 8;
        uint8 Ring_Fre_1 : 8;
        uint8 NoiseCnt : 8;
        uint8 NoiseSum_2 : 8;
        uint8 : 4;
        uint8 NoiseSum_1 : 4;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
    }bits;
}RLS_EchoData_6FF_ecu0_0_MSGType;
typedef union
{
    uint8 byte[24];
    struct
    {
        uint8 FLS_ImpData_2 : 8;
        uint8 FLS_ImpData_1 : 8;
        uint8 FL_ImpData_2 : 8;
        uint8 FL_ImpData_1 : 8;
        uint8 FML_ImpData_2 : 8;
        uint8 FML_ImpData_1 : 8;
        uint8 FMR_ImpData_2 : 8;
        uint8 FMR_ImpData_1 : 8;
        uint8 FR_ImpData_2 : 8;
        uint8 FR_ImpData_1 : 8;
        uint8 FRS_ImpData_2 : 8;
        uint8 FRS_ImpData_1 : 8;
        uint8 RRS_ImpData_2 : 8;
        uint8 RRS_ImpData_1 : 8;
        uint8 RR_ImpData_2 : 8;
        uint8 RR_ImpData_1 : 8;
        uint8 RMR_ImpData_2 : 8;
        uint8 RMR_ImpData_1 : 8;
        uint8 RML_ImpData_2 : 8;
        uint8 RML_ImpData_1 : 8;
        uint8 RL_ImpData_2 : 8;
        uint8 RL_ImpData_1 : 8;
        uint8 RLS_ImpData_2 : 8;
        uint8 RLS_ImpData_1 : 8;
    }bits;
}Sns_ImpData_6EE_ecu0_0_MSGType;
typedef union
{
    uint8 byte[64];
    struct
    {
        uint8 AK2Upper_SnsId : 8;
        uint8 AK2Upper_LabData0_8 : 8;
        uint8 AK2Upper_LabData0_7 : 8;
        uint8 AK2Upper_LabData0_6 : 8;
        uint8 AK2Upper_LabData0_5 : 8;
        uint8 AK2Upper_LabData0_4 : 8;
        uint8 AK2Upper_LabData0_3 : 8;
        uint8 AK2Upper_LabData0_2 : 8;
        uint8 AK2Upper_LabData0_1 : 8;
        uint8 AK2Upper_LabData1_8 : 8;
        uint8 AK2Upper_LabData1_7 : 8;
        uint8 AK2Upper_LabData1_6 : 8;
        uint8 AK2Upper_LabData1_5 : 8;
        uint8 AK2Upper_LabData1_4 : 8;
        uint8 AK2Upper_LabData1_3 : 8;
        uint8 AK2Upper_LabData1_2 : 8;
        uint8 AK2Upper_LabData1_1 : 8;
        uint8 AK2Upper_LabData2_8 : 8;
        uint8 AK2Upper_LabData2_7 : 8;
        uint8 AK2Upper_LabData2_6 : 8;
        uint8 AK2Upper_LabData2_5 : 8;
        uint8 AK2Upper_LabData2_4 : 8;
        uint8 AK2Upper_LabData2_3 : 8;
        uint8 AK2Upper_LabData2_2 : 8;
        uint8 AK2Upper_LabData2_1 : 8;
        uint8 AK2Upper_LabData3_8 : 8;
        uint8 AK2Upper_LabData3_7 : 8;
        uint8 AK2Upper_LabData3_6 : 8;
        uint8 AK2Upper_LabData3_5 : 8;
        uint8 AK2Upper_LabData3_4 : 8;
        uint8 AK2Upper_LabData3_3 : 8;
        uint8 AK2Upper_LabData3_2 : 8;
        uint8 AK2Upper_LabData3_1 : 8;
        uint8 AK2Upper_LabData4_8 : 8;
        uint8 AK2Upper_LabData4_7 : 8;
        uint8 AK2Upper_LabData4_6 : 8;
        uint8 AK2Upper_LabData4_5 : 8;
        uint8 AK2Upper_LabData4_4 : 8;
        uint8 AK2Upper_LabData4_3 : 8;
        uint8 AK2Upper_LabData4_2 : 8;
        uint8 AK2Upper_LabData4_1 : 8;
        uint8 AK2Upper_LabData5_8 : 8;
        uint8 AK2Upper_LabData5_7 : 8;
        uint8 AK2Upper_LabData5_6 : 8;
        uint8 AK2Upper_LabData5_5 : 8;
        uint8 AK2Upper_LabData5_4 : 8;
        uint8 AK2Upper_LabData5_3 : 8;
        uint8 AK2Upper_LabData5_2 : 8;
        uint8 AK2Upper_LabData5_1 : 8;
        uint8 AK2Upper_LabData6_8 : 8;
        uint8 AK2Upper_LabData6_7 : 8;
        uint8 AK2Upper_LabData6_6 : 8;
        uint8 AK2Upper_LabData6_5 : 8;
        uint8 AK2Upper_LabData6_4 : 8;
        uint8 AK2Upper_LabData6_3 : 8;
        uint8 AK2Upper_LabData6_2 : 8;
        uint8 AK2Upper_LabData6_1 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
    }bits;
}AK2Upper_LabData_6EF_ecu0_0_MSGType;
typedef union
{
    uint8 byte[8];
    struct
    {
        uint8 AK2Upper_Command : 8;
        uint8 AK2Upper_Version : 8;
        uint8 AK2Upper_DM_2 : 8;
        uint8 AK2Upper_DM_1 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
        uint8 : 8;
    }bits;
}AK2Upper_Mcu2Pc_6F1_ecu0_0_MSGType;
typedef union
{
    uint8 byte[8];
    struct
    {
        uint8 AK2Upper_Command : 8;
        uint8 AK2Upper_CH0 : 8;
        uint8 AK2Upper_CH1 : 8;
        uint8 AK2Upper_CH2 : 8;
        uint8 AK2Upper_CH3 : 8;
        uint8 AK2Upper_WorkMode_1 : 8;
        uint8 AK2Upper_MaesMode : 8;
        uint8 AK2Upper_Profile : 8;
    }bits;
}AK2Upper_Pc2Mcu_6F0_ecu0_0_MSGType;
#endif /* end of DEBUGSIGNAL_COMMONTYPES_H */

