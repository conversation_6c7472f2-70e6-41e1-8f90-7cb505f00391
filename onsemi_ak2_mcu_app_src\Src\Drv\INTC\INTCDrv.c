/***********************************************************************************************************************
* DISCLAIMER
* This software is supplied by Renesas Electronics Corporation and is only 
* intended for use with Renesas products. No other uses are authorized. This 
* software is owned by Renesas Electronics Corporation and is protected under 
* all applicable laws, including copyright laws.
* THIS SOFTWARE IS PROVIDED "AS IS" AND R<PERSON><PERSON>AS MAKES NO WARRANTIES REGARDING 
* THIS SOFTWARE, WHETHER EXPRESS, IMPLIED OR STATUTORY, INCLUDING BUT NOT 
* LIMITED TO WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE 
* AND NON-INFRINGEMENT.  ALL SUCH WARRANTIES ARE EXPRESSLY DISCLAIMED.
* TO THE MAXIMUM EXTENT PERMITTED NOT PROHIBITED BY LAW, NEITHER RENESAS 
* ELECTRONICS CORPORATION NOR ANY OF ITS AFFILIATED COMPANIES SHALL BE LIABLE 
* FOR ANY DIRECT, INDIRECT, SPECIAL, INCIDENTAL OR CONSEQUENTIAL DAMAGES FOR 
* ANY REASON RELATED TO THIS SOFTWARE, EVEN IF RENESAS OR ITS AFFILIATES HAVE 
* BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.
* Renesas reserves the right, without notice, to make changes to this software 
* and to discontinue the availability of this software.  By using this software, 
* you agree to the additional terms and conditions found by accessing the 
* following link:
* http://www.renesas.com/disclaimer
*
* Copyright (C) 2015, 2018 Renesas Electronics Corporation. All rights reserved.
***********************************************************************************************************************/

/***********************************************************************************************************************
* File Name    : r_cg_intc.c
* Version      : Applilet4 for RH850/F1K V1.01.02.02 [08 May 2018]
* Device(s)    : R7F701581(LQFP100pin)
* Tool-Chain   : CCRH
* Description  : This file implements device driver for INTC module.
* Creation Date: 2023/10/24
***********************************************************************************************************************/

/***********************************************************************************************************************
Pragma directive
***********************************************************************************************************************/
/* Start user code for pragma. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */

/***********************************************************************************************************************
Includes
***********************************************************************************************************************/
#include "INTCDrv.h"

/* Start user code for include. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */

/***********************************************************************************************************************
Global variables and functions
***********************************************************************************************************************/

/* Start user code for global. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */

/******************************************************************************/
/**<pre>
 *函数名称: INIPInit
 *功能描述: INTP外设初始化
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void INTPInit(void)
{
    R_INTC_Create();

    R_INTP2_Start();
    R_INTP7_Start();
    R_INTP8_Start();
    R_INTP11_Start();
}
/******************************************************************************/
/**<pre>
 *函数名称: INTCStop
 *功能描述: 关闭INTP外设
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void INTPStop(void)
{
    R_INTP2_Stop();
    R_INTP7_Stop();
    R_INTP8_Stop();
    R_INTP11_Stop();
}
/***********************************************************************************************************************
* Function Name: R_INTC_Create
* Description  : This function initializes INTP module.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_INTC_Create(void)
{		
    /* Disable INTP2 operation and clear request */
    INTC2.ICP2.BIT.MKP2 = _INT_PROCESSING_DISABLED;
    INTC2.ICP2.BIT.RFP2 = _INT_REQUEST_NOT_OCCUR;
    /* Disable INTP7 operation and clear request */
    INTC2.ICP7.BIT.MKP7 = _INT_PROCESSING_DISABLED;
    INTC2.ICP7.BIT.RFP7 = _INT_REQUEST_NOT_OCCUR;
    /* Disable INTP8 operation and clear request */
    INTC2.ICP8.BIT.MKP8 = _INT_PROCESSING_DISABLED;
    INTC2.ICP8.BIT.RFP8 = _INT_REQUEST_NOT_OCCUR;
    /* Disable INTP10 operation and clear request */
    INTC2.ICP10.BIT.MKP10 = _INT_PROCESSING_DISABLED;
    INTC2.ICP10.BIT.RFP10 = _INT_REQUEST_NOT_OCCUR;
    /* Disable INTP11 operation and clear request */
    INTC2.ICP11.BIT.MKP11 = _INT_PROCESSING_DISABLED;
    INTC2.ICP11.BIT.RFP11 = _INT_REQUEST_NOT_OCCUR;
    /* Disable INTP12 operation and clear request */
    INTC2.ICP12.BIT.MKP12 = _INT_PROCESSING_DISABLED;
    INTC2.ICP12.BIT.RFP12 = _INT_REQUEST_NOT_OCCUR;
    /* Set INTP2 setting */
    INTC2.ICP2.BIT.TBP2 = _INT_TABLE_VECTOR;
    INTC2.ICP2.UINT16 &= _INT_PRIORITY_LOWEST;
    FCLA0.CTL2_INTPL = _INTC_EDGE_FALLING;
    /* Set INTP7 setting */
    INTC2.ICP7.BIT.TBP7 = _INT_TABLE_VECTOR;
    INTC2.ICP7.UINT16 &= _INT_PRIORITY_LOWEST;
    FCLA0.CTL7_INTPL = _INTC_EDGE_FALLING;
    /* Set INTP8 setting */
    INTC2.ICP8.BIT.TBP8 = _INT_TABLE_VECTOR;
    INTC2.ICP8.UINT16 &= _INT_PRIORITY_LOWEST;
    FCLA0.CTL0_INTPH = _INTC_EDGE_FALLING;
    /* Set INTP11 setting */
    INTC2.ICP11.BIT.TBP11 = _INT_TABLE_VECTOR;
    INTC2.ICP11.UINT16 &= _INT_PRIORITY_LOWEST;
    FCLA0.CTL3_INTPH = _INTC_EDGE_FALLING;
    /* Set INTP2 pin */
    PORT.PIBC0 &= _PORT_CLEAR_BIT6;
    PORT.PBDC0 &= _PORT_CLEAR_BIT6;
    PORT.PM0 |= _PORT_SET_BIT6;  
    PORT.PMC0 &= _PORT_CLEAR_BIT6;
    PORT.PIPC0 &= _PORT_CLEAR_BIT6;
    PORT.PFC0 &= _PORT_CLEAR_BIT6;
    PORT.PFCE0 &= _PORT_CLEAR_BIT6;
    PORT.PFCAE0 &= _PORT_CLEAR_BIT6;
    PORT.PMC0 |= _PORT_SET_BIT6;  
    /* Set INTP7 pin */
    PORT.PIBC8 &= _PORT_CLEAR_BIT3;
    PORT.PBDC8 &= _PORT_CLEAR_BIT3;
    PORT.PM8 |= _PORT_SET_BIT3;  
    PORT.PMC8 &= _PORT_CLEAR_BIT3;
    PORT.PFC8 &= _PORT_CLEAR_BIT3;
    PORT.PFCE8 |= _PORT_SET_BIT3;  
    PORT.PMC8 |= _PORT_SET_BIT3;  
    /* Set INTP8 pin */
    PORT.PIBC8 &= _PORT_CLEAR_BIT4;
    PORT.PBDC8 &= _PORT_CLEAR_BIT4;
    PORT.PM8 |= _PORT_SET_BIT4;  
    PORT.PMC8 &= _PORT_CLEAR_BIT4;
    PORT.PFC8 &= _PORT_CLEAR_BIT4;
    PORT.PFCE8 |= _PORT_SET_BIT4;  
    PORT.PMC8 |= _PORT_SET_BIT4;  
    /* Set INTP11 pin */
    PORT.PIBC9 &= _PORT_CLEAR_BIT1;
    PORT.PBDC9 &= _PORT_CLEAR_BIT1;
    PORT.PM9 |= _PORT_SET_BIT1;  
    PORT.PMC9 &= _PORT_CLEAR_BIT1;
    PORT.PFC9 &= _PORT_CLEAR_BIT1;
    PORT.PFCE9 &= _PORT_CLEAR_BIT1;
    PORT.PMC9 |= _PORT_SET_BIT1;   
}
/***********************************************************************************************************************
* Function Name: R_INTP2_Start
* Description  : This function clears INTP2 interrupt flag and enables interrupt.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_INTP2_Start(void)
{
    /* Clear INTP2 request and enable operation */
    INTC2.ICP2.BIT.RFP2 = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICP2.BIT.MKP2 = _INT_PROCESSING_ENABLED;    
}
/***********************************************************************************************************************
* Function Name: R_INTP2_Stop
* Description  : This function disables INTP2 interrupt and clears interrupt flag.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_INTP2_Stop(void)
{
    /* Disable INTP2 operation and clear request */
    INTC2.ICP2.BIT.MKP2 = _INT_PROCESSING_DISABLED;
    INTC2.ICP2.BIT.RFP2 = _INT_REQUEST_NOT_OCCUR;
    /* Synchronization processing */
}
/***********************************************************************************************************************
* Function Name: R_INTP7_Start
* Description  : This function clears INTP7 interrupt flag and enables interrupt.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_INTP7_Start(void)
{
    /* Clear INTP7 request and enable operation */
    INTC2.ICP7.BIT.RFP7 = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICP7.BIT.MKP7 = _INT_PROCESSING_ENABLED;    
}
/***********************************************************************************************************************
* Function Name: R_INTP7_Stop
* Description  : This function disables INTP7 interrupt and clears interrupt flag.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_INTP7_Stop(void)
{
    /* Disable INTP7 operation and clear request */
    INTC2.ICP7.BIT.MKP7 = _INT_PROCESSING_DISABLED;
    INTC2.ICP7.BIT.RFP7 = _INT_REQUEST_NOT_OCCUR;
    /* Synchronization processing */
}
/***********************************************************************************************************************
* Function Name: R_INTP8_Start
* Description  : This function clears INTP8 interrupt flag and enables interrupt.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_INTP8_Start(void)
{
    /* Clear INTP8 request and enable operation */
    INTC2.ICP8.BIT.RFP8 = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICP8.BIT.MKP8 = _INT_PROCESSING_ENABLED;    
}
/***********************************************************************************************************************
* Function Name: R_INTP8_Stop
* Description  : This function disables INTP8 interrupt and clears interrupt flag.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_INTP8_Stop(void)
{
    /* Disable INTP8 operation and clear request */
    INTC2.ICP8.BIT.MKP8 = _INT_PROCESSING_DISABLED;
    INTC2.ICP8.BIT.RFP8 = _INT_REQUEST_NOT_OCCUR;
    /* Synchronization processing */
}
/***********************************************************************************************************************
* Function Name: R_INTP11_Start
* Description  : This function clears INTP11 interrupt flag and enables interrupt.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_INTP11_Start(void)
{
    /* Clear INTP11 request and enable operation */
    INTC2.ICP11.BIT.RFP11 = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICP11.BIT.MKP11 = _INT_PROCESSING_ENABLED;    
}
/***********************************************************************************************************************
* Function Name: R_INTP11_Stop
* Description  : This function disables INTP11 interrupt and clears interrupt flag.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_INTP11_Stop(void)
{
    /* Disable INTP11 operation and clear request */
    INTC2.ICP11.BIT.MKP11 = _INT_PROCESSING_DISABLED;
    INTC2.ICP11.BIT.RFP11 = _INT_REQUEST_NOT_OCCUR;
    /* Synchronization processing */
}

/* Start user code for adding. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */
