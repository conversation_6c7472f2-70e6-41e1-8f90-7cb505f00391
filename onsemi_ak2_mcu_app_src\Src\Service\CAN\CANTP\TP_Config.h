#ifndef __TP_CONFIG_H__
#define __TP_CONFIG_H__


/*******************************************************************************
* Includes
********************************************************************************/
#include "Types.h"
#include "LhRule.h"
/******************************************************************************
* Constants and macros
*******************************************************************************/
#define TP_MANAGE_PERIOD                    5            /* TP层扫描的时间，注TP层扫描时间必须时ms的整数倍 */

#define TPPHYRXDATAMAX                      500          /* TP层物理寻址接收缓冲区大小 */
#define TPFUNCRXDATAMAX                     8            /* TP层功能寻址接收缓冲区大小 */

#define P2CAN_SERVICE                       50           /* 服务器接收到请求到响应的最大时间 */
#define P2CAN_EXTENDSERVICE                 5000         /* 服务器发送NRC78到响应的最大时间  */
#define uNAs                                25u            /* 发送方发送诊断帧(首帧/连续帧)的时间(开始发送到完成) */
#define uNAr                                25u            /* 接收方发送诊断帧(流控制帧)的时间(开始发送到完成) */
#define uNBs                                75          /* 发送方等待流控制帧的时间 */
#define uNCr                                150          /* 接收方等待后续帧的时间   */
#define STMIN                               25           /* 服务器接收后续帧的最小时间间隔 */
#define BS                                  8            /* 服务器允许连续接收的后续帧次数 */
#define FCWAITMAX                           0            /* 最大的等待流控制次数           */


#define TP_PADDING_BYTE_VALUE               0x55         /* CANFD 填充字节 0xCC*/

#define COM_FRAME_STATUS_TX_REQ             0x01U
#define COM_FRAME_STATUS_RX_LOST_FLAG       0x01U
#define COM_FRAME_STATUS_TX_PROGRESS        0x02U
#define COM_FRAME_STATUS_RX_FLAG            0x02U
#define COM_FRAME_STATUS_PERIOD_EN          0x04U
#define COM_FRAME_STATUS_TX_CONF            0x08U
#define COM_MON_NODE_RELOAD                 0x08U
#define COM_MON_NODE_TASK                   0x10U
#define COM_MON_NODE_STATUS_OK              0x20U
#define COM_MON_NODE_STATUS_NOK             0x00U
#define COM_FRAME_STATUS_TX_DATA            0x80U


/****************************************************************************/
/* Transport Protocol message configuration, stored in Read-Only memory */
typedef struct
{
    uint8  *pRxData[2];        /* Pointer on the reception data buffer */
    uint16  GwBuffMax[2];       /* Maximum data number for a transfer */
    uint16  GwuNAs;             /* Timeout between Tx FF/CF start and Tx FF/CF end */
    uint16  GwuNAr;             /* Timeout between Tx FC start and Tx FC end */
    uint16  GwuNBs;             /* Timeout between (Tx FF or Tx CF) and Rx FC */
    uint16  GwuNCr;             /* Timeout between (Tx FC or Rx CF) and Rx CF */
    uint8  GcRxSTmin;          /* STmin value of the transmitted FC */
    uint16  GwRxBs;             /* BS value of the transmitted FC */
    uint8  GcTxFCWaitMax;      /* Maximum number of FCWait frame received in a row */
    uint8  GcPaddingByte;
} TpCfgStr;

/***************************************************************************/
typedef struct
{
    uint8 *pFunctionDiagFrameState;          /* 功能寻址诊断报文接收标志 */
    uint8 *pPhysicalDiagFrameState;          /* 物理寻址诊断报文接收标志 */ 
    uint8 *pRespondDiagFrameState;           /* 诊断报文响应请求标志     */
    uint8 *pFunctionDiagComBuf;              /* 功能寻址ID接收缓冲区     */
    uint8 *pPhysicalDiagComBuf;              /* 物理寻址ID接收缓冲区     */
    uint8 *pRespondDiagComBuff;              /* 诊断响应ID发送缓冲区     */
    uint8 *pFunctionRxDLC;
    uint8 *pPhysicalRxDLC;
}TPCOMStr;

/**************************************************************************/
typedef struct
{
    uint8  *GcPhysicalChannelRxCompleteFlag;   /* 物理通道接收完成标志 */
    uint8  *GcFunctionChannelRxCompleteFlag;   /* 功能通道接收完成标志 */
    uint16  *GwPhysicalChannelRxLen;            /* 物理寻址接受的数据长度 */
    uint8  *GcFunctionChannelRxLen;            /* 功能寻址接受的数据长度 */
    uint8  GcP2CAN_Server;                     /* 对于服务器接收到请求消息后发出响应消息时间的性能要求 */
    uint16  GwP2ExtendCAN_Server;               /* 当服务器在发送否定响应码为 0x78 的否定响应后， 到服务器发出响应消息时间的性能要求 */
    uint32 *GdSystemTimer;                      /* 系统时间，单位ms */
} TPUDSStr;





/******************************************************************************
* External objects
********************************************************************************/
extern uint8 GcTP_LibTimer;
/**********TP层与上层应用数据接口***********************************************/
 
 extern uint8 GcTpPhyChannelRxBuff[TPPHYRXDATAMAX];           /* Tp层接收缓冲区，应用层与TP层的数据接口 */
 extern uint8 GcTpFuncChannelRxBuff[TPFUNCRXDATAMAX];             /* 功能寻址地址接收缓冲区最大设置为8      */
 extern uint8 GcPhysicalChannelRxCompleteFlag;                /* TP层物理寻址是否接收到完成的数据包标志 */
 extern uint8 GcFunctionChannelRxCompleteFlag;                /* TP层功能寻址是否接收到完成的数据包标志 */
 extern uint16 GwPhysicalChannelRxLen;
 extern uint8 GcFunctionChannelRxLen;

/**********TP层与底层数据接口***************************************************/
extern TpCfgStr GsTpMsgCfg;
extern TPCOMStr GsTPCOMParaCfg;
extern TPUDSStr GsTPUDSParaCfg;

/*******************************************************************************
* Global Functions
********************************************************************************/

#endif










