###############################################################################
#
# IAR Assembler V2.10.1.1473 for RH850                    29/May/2025  11:11:42
# Copyright 2012-2018 IAR Systems AB.
#
#    Core         =  g3kh
#    Source file  =  
#        D:\zhang9591_project\AK2\tools\onsemi\AnalysisTool\onsemi_ak2_mcu_app_src\Src\device\cstartup.s
#    Command line =  
#        -f C:\Users\<USER>\AppData\Local\Temp\EWCAFC.tmp
#        (D:\zhang9591_project\AK2\tools\onsemi\AnalysisTool\onsemi_ak2_mcu_app_src\Src\device\cstartup.s
#        --core g3kh --code_model normal --data_model medium --fpu single
#        --double=64 -o
#        D:\zhang9591_project\AK2\tools\onsemi\AnalysisTool\onsemi_ak2_mcu_app_src\IarProject_1581\Debug\Obj
#        -M<> -r -ld
#        D:\zhang9591_project\AK2\tools\onsemi\AnalysisTool\onsemi_ak2_mcu_app_src\IarProject_1581\Debug\List\)
#    List file    =  
#        D:\zhang9591_project\AK2\tools\onsemi\AnalysisTool\onsemi_ak2_mcu_app_src\IarProject_1581\Debug\List\cstartup.lst
#    Object file  =  
#        D:\zhang9591_project\AK2\tools\onsemi\AnalysisTool\onsemi_ak2_mcu_app_src\IarProject_1581\Debug\Obj\cstartup.o
#
###############################################################################

##############################################################################
# Module CSTARTUP                                                            #
##############################################################################

    507                        
    508                        ;-----------------------------------------------------------------------------
 
 248 <USER> <GROUP> section .text   , module CSTARTUP
 
 248 bytes of CODE memory  in module CSTARTUP

Errors: none
Warnings: none
