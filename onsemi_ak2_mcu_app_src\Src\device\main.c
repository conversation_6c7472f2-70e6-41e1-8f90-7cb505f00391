/******************************************************************************
 * @file       main.c
 * @brief      
 * @date       2025-03-04 11:28:43
 * <AUTHOR>
 * @copyright  Longhorn Automotive Electronics Technology (Xiamen) Co., Ltd.
 *****************************************************************************/


/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "types.h"
#include "SystemService.h"
#include "AdcHal.h"
#include "IOHal.h"
#include "CANStack.h"
#include "CAN_COM.h"
#include "DTCMonitor.h"
#include "TimerManage.h"
#include "System_Schedule_int.h"



/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/



/******************************************************************************
 * @Function Declaration
 *****************************************************************************/



/******************************************************************************
 * @Const Definitions
 *****************************************************************************/



/******************************************************************************
 * @Variable Definitions
 *****************************************************************************/



/******************************************************************************
 * @Function Definitions
 *****************************************************************************/


/******************************************************************************
 * @brief      
 * <AUTHOR>
 * @date       2025-03-04 11:28:48
 * @note       
 *****************************************************************************/
void main(void)
{
    SystemDrvInit();
    SystemAppInit();
    SystemAlgorithmInit();
    System_Sched_vidInit();
    System_Sched_Management();
}


/******************************************************************************
 * @brief      
 * <AUTHOR>
 * @date       2025-03-04 11:29:09
 * @note       
 *****************************************************************************/
void TimerIntCallBack(void)
{
    UserTimerSet_Ms_ISR();
}
