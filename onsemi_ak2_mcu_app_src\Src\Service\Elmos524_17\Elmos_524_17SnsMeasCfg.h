/*
 * Elmos_524_17SnsMeasCfg.h
 *
 *  Created on: 2021 
 *      Author: 6000021992
 */

#ifndef ELMOS_524_17SNSMEASCFG_H_
#define ELMOS_524_17SNSMEASCFG_H_

#include "Elmos_524_17.h"

#define DSIChl_F DSIChl1
#define DSIChl_R DSIChl2

#define MeasSeqList0Num          16      
#define MeasSeqList1Num          8
#define MeasSeqList2Num			 4
#define MeasSeqList3Num			 4	
#define MeasSeqList4Num			 1
#define MeasSeqList4_Guidance    16

typedef enum
{
    MeasMode_IDLE = 0,

    Meas_Near_Mode,
    Meas_Far_Mode,
    Meas_Far_Near_Alternate_Mode,
    Meas_ALL_STD_LISTEN_Mode,
    Meas_ALL_STD_EMIT_Mode,
    MeasMode_ImpedanceMeas,
    Meas_ALL_AVD_LISTEN_Mode,
    Meas_ALL_AVD_EMIT_Mode,


    MeasMode_Num,

}MeasMode_en;





typedef enum
{
    SNS_R_C = 0, /*Rear Corner*/
    SNS_R_M ,    /*Rear Middle*/
    SNS_F_C ,    /*Front Corner*/
    SNS_F_M ,    /*Front Middle*/
    SNS_F_S,     /*Front Side*/
    SNS_R_S,     /*Read Side*/
    SNSOriNum,
}Sns_Ori_en;

typedef enum
{
    StdMeasSeq0 = 0,
    StdMeasSeq1,
    StdMeasSeq2,
    StdMeasSeq3,
    StdMeasSeq4,

    AdvMeasSeq0,
    AdvMeasSeq1,
    AdvMeasSeq2,
    AdvMeasSeq3,
    AdvMeasSeq4,
    AdvMeasSeq5,
    AdvMeasSeq6,
    AdvMeasSeq7,
    
    AdvMeasSeq8,
    AdvMeasSeq9,
    AdvMeasSeq10,
    AdvMeasSeq11,
    AdvMeasSeq12,
    AdvMeasSeq13,
	AdvMeasSeq14,
	AdvMeasSeq15,
	AdvMeasSeq16,
	AdvMeasSeq17,
	
	AdvMeasSeq3_0,	
	AdvMeasSeq3_1,
	AdvMeasSeq3_2,
	AdvMeasSeq3_3,
	
    StdMeasSeq2_0,
    StdMeasSeq2_1,
    StdMeasSeq2_2,
    StdMeasSeq2_3,
    StdMeasSeq2_4,
    
    CMDSeq_ALLLISTEN,

    CMDSeq_CntMax = CMDSeq_ALLLISTEN,

    CMDSeq_ALLEmit,



    CMDSeq_Num,
    CMDSeq_NONE = CMDSeq_Num,

}CMDSeq_en;

typedef enum
{
    MeasSeqList0 = 0,
    MeasSeqList1,
    MeasSeqList2,
    MeasSeqList3,
    MeasSeqList4,
    MeasSeq_Guidance,
    MeasSeqList_TotalNum
}MeasSeqListIdx_en;

typedef struct
{
    DSIMasterID_en DSIMasterID;
    DSIChlSel_en DSIChlSEL;
    uint8 cSnsPhysAddr;
    Sns_Ori_en Sns_Ori;
}SnsAddrCfg_str;

typedef struct
{
    uint8 StartSeq;
    uint8 SeqStep;
    uint8 EndSeq;
}MeasCMDSeqStepCfg_Str;


typedef enum
{
    STD_Mas = 0,
    STD_Lis,

    ADV_Mas_A ,
    ADV_Mas_B ,

    ADV_Mas_B_RecA,
    ADV_Mas_A_RecB,

    ADV_Lis_A,
    ADV_Lis_B,

    DistanceType_Num,
    DistanceType_NULL,
}DistanceType_en;

typedef struct
{
    MeasType_en MeasType;
    
    SNS_MEAS_TYPE_en SNsMeasType[6];
}MEAS_CRM_Seq_str;

typedef struct
{
    const CMDSeq_en *pSeqlist;
    uint8 SeqListNum;
}SnsGroupMeasSeq_str;




extern const MEAS_CRM_Seq_str MeasSeq[CMDSeq_Num];

extern const MeasCMDSeqStepCfg_Str MeasCMDSeqStepCfg[MeasMode_Num];
extern const SnsAddrCfg_str SnsAddCfg[SNSIDNum];

extern const DSIChlID_en SnsMeasGroupDSIChlIDList[SnsMeasGroup_Num];

extern const SnsGroupMeasSeq_str RearSnsGroupMeasSeqList[MeasSeqList_TotalNum];
extern const SnsGroupMeasSeq_str FrontSnsGroupMeasSeqList[MeasSeqList_TotalNum];
extern const SnsID_en MasterSnsSelect[DSIMasterNum];
	
extern uint8 GetSns_PhyAddr(SnsID_en SnsID);
extern DSIMasterID_en GetSnsDSIMasterID(SnsID_en SnsID);
extern DSIChlSel_en GetSnsDSIChlSEL(SnsID_en SnsID);

extern SnsID_en GetSnsID(DSIMasterID_en DSIMasterID,DSIChlID_en DSIChlID,uint8 SlotSnsID);
extern SnsMeasGroup_en GetSnsMeasGroupID(DSIMasterID_en DSIMasterID,DSIChlID_en DSIChlID);
extern uint8 GetMeasMsgIndex(DSIChlID_en DSIChlID,uint8 SlotID);
extern Sns_Ori_en Elmos17_GetSns_Ori(SnsID_en SnsID);
extern DistanceType_en Elmos17_GetDisType(SNS_MEAS_TYPE_en  SNS_MEAS_TYPE, uint8 filterNum);
extern MeasType_en GetSeqMeasType(CMDSeq_en cMeasSeq);
extern SNS_MEAS_TYPE_en GetMeasSeqSNsMeasType(CMDSeq_en cMeasSeq,uint8 SlotID);

extern uint16 CalcSeqMeasPDCMNum(uint16 MeasTime_us,uint16 PdcmPeriod_us,SnsMeasDataMode_en DataMode);

extern uint16 GetSeqMeasTime_us(MeasType_en MeasType, SnsMeasProfile_en profile);

#endif /* ELMOS_524_17SNSMEASCFG_H_ */
