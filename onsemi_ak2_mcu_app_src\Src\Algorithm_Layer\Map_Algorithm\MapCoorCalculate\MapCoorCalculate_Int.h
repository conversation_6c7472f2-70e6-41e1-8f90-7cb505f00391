/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * SnsCoorCalculate_Int: 
 * Created on: 2023-08-02 14:56
 * Original designer: AntonyFang
 ******************************************************************************/

#ifndef MapCoorCalculate_Int_H
#define MapCoorCalculate_Int_H

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "SnsRawData_Type.h"
#include "MapCoorCalculate_Type.h"

/******************************************************************************
 * Inclusion of private header files
 *****************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions*****************************/
/******************************************************************************/
/******************************************************************************/




/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
extern Cloud_ObjMapToCANType GstrCloud_ObjMap[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];
extern SnsObjCoorPointType   GstrObjCoorPoint[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];
extern SnsPointCloudBufType  GstrPointCloudBuf[PDC_SNS_GROUP_NUM][POINT_CLOUD_AREA_NUM];

extern VehiclePointCloudBufType GstrVehiclePointCloudBuf;
extern SideAreaMapType       GstrSideAreaMap[SIDE_SNS_NUM];
extern const ObjTypeCntType GstrObjTypeCntInit;
extern SideAreaPointBufType  GstrSideAreaPointBuf[SIDE_SNS_NUM];
extern const uint8* Gu8ObjTypeString[7];


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
void SnsObjCoorDataPowerOnInit(void);
void ObjSnsCoorCalAndFollow(PDCSnsGroupType LenuGroup,PDCSnsChannelType LenuCh);
void SnsObjCoorDataInit(PDCSnsGroupType LenuGroup);
void AnalysisSideAreaBufDataAndPrintf(SideAreaPointBufType  *LpstrSideAreaPointBuf,uint8 Lu8MapId);
uint8 AnalysisSideAreaBufDataWhenMapUpdate(SideAreaPointBufType  *LpstrSideAreaPointBuf,uint8 Lu8MapId,uint16 Lu16MapMasterDis);
uint8 AnalysisSideAreaBufDataWhenMapNoUpdate(SideAreaPointBufType  *LpstrSideAreaPointBuf,uint8 Lu8MapId,SideSnsIndexType LenuSideSnsIndex, uint16 Lu16MapMasterDis);

void UpdateSideAreaPointMapEndInx(SideAreaPointBufType  *LpstrSideAreaPointBuf);
void CreatNewMapSideAreaPointBufInit(SideAreaPointBufType  *LpstrSideAreaPointBuf);
void GetSideAreaBufId(SideAreaPointBufType  *LpstrSideAreaPointBuf);


#endif /* end of SnsCoorCalculate_Int_H */

