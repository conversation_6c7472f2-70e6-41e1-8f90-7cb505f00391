/******************************************************************************
 * @file       TimerManage.c
 * @brief      
 * @date       2025-03-04 13:03:52
 * <AUTHOR>
 * @copyright  Longhorn Automotive Electronics Technology (Xiamen) Co., Ltd.
 *****************************************************************************/


/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "TimerManage.h"
#include "CANDrv.h"
#include "CAN_Com.h"
#include "PowerSingalManage.h"
#include "Debug.h"
#include "System_Schedule_int.h"
#include "CSIHDrv.h"
#include "DMADrv.h"
#include "UartDrv.h"
#include "Elmos_524_17_SnsCtrl.h"
#include "IOHal.h"



/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/



/******************************************************************************
 * @Function Declaration
 *****************************************************************************/



/******************************************************************************
 * @Const Definitions
 *****************************************************************************/



/******************************************************************************
 * @Variable Definitions
 *****************************************************************************/
uint32 GdSystemMsTimer = 0u;

/** @brief 时间单位MS */
uint32 Gu32CANTimeSysn_GlobalTimeBase = 0u;



/******************************************************************************
 * @Function Definitions
 *****************************************************************************/


/******************************************************************************
 * @brief      用户定时器Ms中断处理
 * <AUTHOR>
 * @date       2025-03-04 13:55:29
 * @note       
 *****************************************************************************/
void UserTimerSet_Ms_ISR(void)
{
    /** @note 系统毫秒计数 */
    GdSystemMsTimer++;
    Gu32CANTimeSysn_GlobalTimeBase++;
	Elmos17SnsCtrl_UpdateTimeCnt();
    /** @note 任务切换计数器 */
    Sched_TickCounterHandler();

    if ((GdSystemMsTimer > 5)&&(ReadPwrMonitorSignal_ADMomentaryValue() >= AD_CALIBRATION_7V))
    {
        COM_TxManage();
        CAN_SendManage();
    }
}


/******************************************************************************
 * @brief      用户延时函数
 * @param[in]  delay 
 * <AUTHOR>
 * @date       2025-03-04 13:55:53
 * @note       
 *****************************************************************************/
void UserTimerDelayMs(const uint32 delay)
{
	uint32 Ld_Start = GdSystemMsTimer;
	uint32 Ld_End = GdSystemMsTimer;
	uint32 Ld_TimeLen = 0;
    Ld_TimeLen = Ld_End- Ld_Start;
    while (Ld_TimeLen < delay)
    {
        Ld_End = GdSystemMsTimer;
        Ld_TimeLen = Ld_End- Ld_Start;
    }
}


/******************************************************************************
 * @brief      Get the System Use Timer Ms Cnt object
 * @return     
 * <AUTHOR>
 * @date       2025-03-04 13:56:28
 * @note       
 *****************************************************************************/
uint32 GetSystemUseTimerMsCnt(void)
{
    return GdSystemMsTimer;
}


/******************************************************************************
 * @brief      系统复位
 * <AUTHOR>
 * @date       2025-03-04 13:56:52
 * @note       
 *****************************************************************************/
void SystemSoftwareReset(void)
{
    __DI();
    AdcHal_Deinit();
    UartDrvClose();
    CANDrvClose();
    R_DMAC00_Stop();
    R_DMAC01_Stop();
    R_DMAC02_Stop();
    R_DMAC03_Stop();
	R_DMAC04_Stop();
    R_DMAC05_Stop();
    R_CSIH2_Stop();
    R_CSIH3_Stop();
	IOHalClose();

#if 0
    RESCTLRESFC = 0x7FF;
    do
    {
       WPROTRPROTCMD0 = 0x000000A5;
       RESCTLSWRESA = 0x00000001;
       RESCTLSWRESA = 0xFFFFFFFE;
       RESCTLSWRESA = 0x00000001;
    } while (WPROTRPROTS0);
#endif

    (*((void(*)(void))0x0))();
}


/******************************************************************************
 * @brief      
 * <AUTHOR>
 * @date       2025-03-04 13:57:26
 * @note       
 *****************************************************************************/
void SystemJumpToSBL(void)
{
    __DI();
    AdcHal_Deinit();
    UartDrvClose();
    CANDrvClose();
    R_DMAC00_Stop();
    R_DMAC01_Stop();
    R_DMAC02_Stop();
    R_DMAC03_Stop();
    R_DMAC04_Stop();
    R_DMAC05_Stop();
    R_CSIH2_Stop();
    R_CSIH3_Stop();
	IOHalClose();

#if 0
    RESCTLRESFC = 0x7FF;
    do
    {
       WPROTRPROTCMD0 = 0x000000A5;
       RESCTLSWRESA = 0x00000001;
       RESCTLSWRESA = 0xFFFFFFFE;
       RESCTLSWRESA = 0x00000001;
    } while (WPROTRPROTS0);
#endif

    (*((void(*)(void))0x10010))();
}
