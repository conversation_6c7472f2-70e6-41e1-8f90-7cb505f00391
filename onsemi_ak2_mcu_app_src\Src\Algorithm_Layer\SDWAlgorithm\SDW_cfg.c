/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * SDW_cfg: 
 * Created on: 2020-12-30 15:33
 * Original designer: <PERSON>leyun
 ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "SDW_Cfg.h"
#include "SnsRawData_Int.h"
#include "ODO_AppSignalManage.h"


/******************************************************************************/
/******************************************************************************/
/******************************* Local Definition *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Public Definition *****************************/
/******************************************************************************/
/******************************************************************************/
SDW_tenuWorkStatus GenuSDWWorkStatus = SDW_CLOSE;
SDW_WheelType GstrSDW_InputWheel;
SDWCurCarPointDataType GstrSDWCurCarPointData;  //GetSDWCurCarPointData();在这里获取值最新
uint16 Gu16SDW_Speed;
Car_GearType GenuSDW_Gear;

/******************************************************************************/
/******************************************************************************/
/****************************** Private Function ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/******************************* Public Function ******************************/
/******************************************************************************/
/******************************************************************************/




/******************************************************************
* 函数名称: GetSDW_WorkStatus
*
* 功能描述: 获取SDW的工作状态,SDW工作状态的逻辑在此处理，或者直接由其他模块传入
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明:
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
void GetSDW_WorkStatus(void)
{
    //if(GstrPDCSnsUseOdo.u8SnsRawDataUseID != 0xFF)
    if(Gu16SDW_Speed < 1000)
    {
        GenuSDWWorkStatus = SDW_OPEN;     /* 正确获取到ID才进入开启状态 */
    }
    else if(Gu16SDW_Speed > 1200)
    {
        GenuSDWWorkStatus = SDW_CLOSE;     /* 正确获取到ID才进入开启状态 */
    }    
}



/******************************************************************
* 函数名称: GetSDWCurCarPointData
*
* 功能描述: 获取车身坐标
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明:
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
void GetSDWCurCarPointData(void)
/*  保留和PDC模块一致的坐标ID*/
{
    CPOS_CurCarPointDataType LstrCurCarPointData;

    if(GstrPDCSnsUseOdo.u8SnsRawDataUseID < RELATION_MOD_NUM_COOR)
    {
        CPOSSgnMag_ReadCarCoorData(&LstrCurCarPointData,GstrPDCSnsUseOdo.u8SnsRawDataUseID);

        GstrSDWCurCarPointData.fX = LstrCurCarPointData.fX;
        GstrSDWCurCarPointData.fY = LstrCurCarPointData.fY;
        GstrSDWCurCarPointData.fAngle = LstrCurCarPointData.fAngle;
    }
    else
    {
        GstrSDWCurCarPointData.fX = 0;
        GstrSDWCurCarPointData.fY = 0;
        GstrSDWCurCarPointData.fAngle = 0;

    }
}


/******************************************************************
* 函数名称: GetSDW_CAN_Signal
*
* 功能描述: 获取整车CAN信号
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明:
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
void GetSDW_CAN_Signal(void)
{
    Car_WheelPulseType LstrCar_RLWheelInf;
    Car_WheelPulseType LstrCar_RRWheelInf;
    
	ReadCAN_AppSignal_Car_WheelPulseAndDir(&LstrCar_RLWheelInf,CAR_RL_WHEEL);
    ReadCAN_AppSignal_Car_WheelPulseAndDir(&LstrCar_RRWheelInf,CAR_RR_WHEEL);


    GstrSDW_InputWheel.enuRLWheelDir = LstrCar_RLWheelInf.enuCar_WheelPulseDir;
    GstrSDW_InputWheel.enuRRWheelDir = LstrCar_RRWheelInf.enuCar_WheelPulseDir;;

    ReadCAN_AppSignal_Car_Speed(&Gu16SDW_Speed);
    ReadCAN_AppSignal_Car_Gear(&GenuSDW_Gear);
}





