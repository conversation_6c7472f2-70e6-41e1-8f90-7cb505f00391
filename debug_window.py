"""
调试窗口模块
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import datetime

class DebugWindow(tk.Toplevel):
    """调试窗口类"""

    def __init__(self, master=None):
        """初始化调试窗口"""
        super().__init__(master)

        # 设置窗口标题
        self.title("调试窗口")

        # 设置窗口大小
        self.geometry("800x600")

        # 设置窗口为模态
        self.transient(master)
        self.grab_set()

        # 创建主框架
        self.main_frame = ttk.Frame(self)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建标签
        self.label = ttk.Label(self.main_frame, text="调试信息：")
        self.label.pack(side=tk.TOP, anchor=tk.W, padx=5, pady=5)

        # 创建文本框
        self.text = scrolledtext.ScrolledText(self.main_frame, wrap=tk.WORD, width=80, height=30)
        self.text.pack(side=tk.TOP, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建按钮框架
        self.buttons_frame = ttk.Frame(self.main_frame)
        self.buttons_frame.pack(side=tk.TOP, fill=tk.X, padx=5, pady=5)

        # 创建清除按钮
        self.clear_button = ttk.Button(self.buttons_frame, text="清除", command=self.clear)
        self.clear_button.pack(side=tk.LEFT, padx=5, pady=5)

        # 创建关闭按钮
        self.close_button = ttk.Button(self.buttons_frame, text="关闭", command=self.destroy)
        self.close_button.pack(side=tk.RIGHT, padx=5, pady=5)

        # 添加一些调试信息
        self.add_debug_info("调试窗口已启动")
        self.add_debug_info("可以在这里显示各种调试信息")
        self.add_debug_info("例如：命令解析过程、响应解析过程等")

    def add_debug_info(self, info):
        """添加调试信息"""
        # 获取当前时间
        now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 添加调试信息
        self.text.insert(tk.END, f"[{now}] {info}\n")
        self.text.see(tk.END)

    def clear(self):
        """清除调试信息"""
        self.text.delete(1.0, tk.END)
        self.add_debug_info("调试信息已清除")
