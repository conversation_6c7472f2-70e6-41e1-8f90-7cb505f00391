/******************************************************************************
 * @file      example_print_usage.c
 * @brief     使用打印函数的示例代码
 * <AUTHOR>
 * @date      2025-01-27 10:00:00
 * @note      展示如何使用RdumRdusWaveProcess_PrintDecompressedEnvelopes函数
 *****************************************************************************/

/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include <stdio.h>
#include "RdumRdusWaveProcess.h"

/******************************************************************************
 * @Function Implementation
 *****************************************************************************/

/******************************************************************************
 * @brief      示例：如何使用打印函数
 * <AUTHOR>
 * @date       2025-01-27 10:00:00
 * @note       展示在发波流程完成后如何打印解压缩数据
 *****************************************************************************/
void ExampleUsage(void)
{
    /* 初始化发波流程 */
    RdumRdusWaveProcess_Init();
    
    /* 启动发波流程 */
    if (RdumRdusWaveProcess_Start())
    {
        printf("发波流程启动成功\n");
        
        /* 等待发波流程完成 */
        while (RdumRdusWaveProcess_GetStatus() == RDUM_RDUS_WAVE_PROCESS_BUSY)
        {
            /* 处理发波流程 */
            RdumRdusWaveProcess_Process();
            
            /* 这里可以添加延时或其他处理 */
        }
        
        /* 检查发波流程状态 */
        if (RdumRdusWaveProcess_GetStatus() == RDUM_RDUS_WAVE_PROCESS_COMPLETE)
        {
            printf("发波流程完成，开始打印解压缩数据\n");
            
            /* 打印所有解压缩后的包络数据 */
            RdumRdusWaveProcess_PrintDecompressedEnvelopes();
        }
        else
        {
            printf("发波流程失败，状态: %d\n", RdumRdusWaveProcess_GetStatus());
        }
    }
    else
    {
        printf("发波流程启动失败\n");
    }
}

/******************************************************************************
 * @brief      主函数示例
 * @return     程序退出码
 * <AUTHOR>
 * @date       2025-01-27 10:00:00
 * @note       程序入口点
 *****************************************************************************/
int main(void)
{
    printf("=== 解压缩包络数据打印示例 ===\n");
    
    /* 运行示例 */
    ExampleUsage();
    
    printf("=== 示例结束 ===\n");
    
    return 0;
}
