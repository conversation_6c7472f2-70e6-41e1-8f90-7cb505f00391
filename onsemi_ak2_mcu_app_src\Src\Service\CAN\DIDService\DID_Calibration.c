
/**********************************************************************
* Includes 
************************************************************************/
#include "DID_Calibration.h"
#include "DID.h"
#include "Did_Cali_Cbk.h"
#include "UserFlash.h"
#include "SnsRawDataCalib.h"
#include "ODO_CalibPara_Types.h"
/**********************************************************************************/

/* 豪恩自定义的安全算法 */
const uint32 Gu32L<PERSON>hornSecuritMask[16] = 
{0xF549506A,0x47152AB0,0x2E31DA76,0x53266FE2,0x874318C0,0x2FE296BF,0xCB84F0FE,0xE806EC53,
0x018F28ED,0x487E89DE,0xCDC1B660,0xFC0829D3,0x847FCE7C,0x17E8DD4D,0xC666FF1C,0xBDC5DBE9};

extern uint8 GuGainDataBuf[24];

uint8 Gu8TestBuffer[1024];
#if (CALIBRATION_EN == STD_ON)//仅用于研发标定；正式软件该宏不会使能
const DataIdentifier_Cail CalibrationDIDDefine[DID_CALIB_NUM] =
{
    {0xF187,(uint8 *)DID_F187_Buff,15,OnlyRead,0,NULL,NULL},
    {0xF195,(uint8 *)DID_F195_Buff,2,OnlyRead,0,NULL,NULL},
    {0xF14A,(uint8 *)DID_F14A_Buff,2,OnlyRead,0,NULL,NULL},
    {0xF193,(uint8 *)DID_F193_Buff,2,OnlyRead,0,NULL,NULL},
    {0xF026,(uint8 *)DID_F026_Buff,2,OnlyRead,0,NULL,NULL},
    {0xF18B,(uint8 *)DID_F18B_Buff,3,OnlyRead,0,NULL,NULL},
    {0xF1A1,(uint8 *)DID_F1A1_Buff,17,OnlyRead,0,NULL,NULL},
    //探头增益
    {0xFDC0,(uint8 *)GuGainDataBuf,24,ReadWrite,NULL,NULL,NULL},
    //ECU 回波高度标定阈值 ;;161=数据长度+CRC；2E写数据时校验多一个字节，该字节仅参与校验，不会写入CODEFLASH；22服务读取数据时，只返回数据，不包含CRC字节
    {0xFD01,(uint8*)Gu16FLS_FRS_StandardThresholdTableInRAM,161,ReadWrite,CAL_DATA_FD01_FLS_FRS_ST_ADD,NULL,NULL},
    {0xFD02,(uint8*)Gu16FLS_FRS_ChirpThresholdTableInRAM,161,ReadWrite,CAL_DATA_FD02_FLS_FRS_CT_ADD,NULL,NULL},
    {0xFD03,(uint8*)Gu16FL_FR_StandardThresholdTableInRAM,161,ReadWrite,CAL_DATA_FD03_FL_FR_ST_ADD,NULL,NULL},
    {0xFD04,(uint8*)Gu16FL_FR_ChirpThresholdTableInRAM,161,ReadWrite,CAL_DATA_FD04_FL_FR_CT_ADD,NULL,NULL},
    {0xFD05,(uint8*)Gu16FML_FMR_StandardThresholdTableInRAM,161,ReadWrite,CAL_DATA_FD05_FML_FMR_ST_ADD,NULL,NULL},
    {0xFD06,(uint8*)Gu16FML_FMR_ChirpThresholdTableInRAM,161,ReadWrite,CAL_DATA_FD06_FML_FMR_CT_ADD,NULL,NULL},
    {0xFD07,(uint8*)Gu16RLS_RRS_StandardThresholdTableInRAM,161,ReadWrite,CAL_DATA_FD07_RLS_RRS_ST_ADD,NULL,NULL},
    {0xFD08,(uint8*)Gu16RLS_RRS_ChirpThresholdTableInRAM,161,ReadWrite,CAL_DATA_FD08_RLS_RRS_CT_ADD,NULL,NULL},
    {0xFD09,(uint8*)Gu16RL_RR_StandardThresholdTableInRAM,161,ReadWrite,CAL_DATA_FD09_RL_RR_ST_ADD,NULL,NULL},
    {0xFD0A,(uint8*)Gu16RL_RR_ChirpThresholdTableInRAM,161,ReadWrite,CAL_DATA_FD0A_RL_RR_CT_ADD,NULL,NULL},
    {0xFD0B,(uint8*)Gu16RML_RMR_StandardThresholdTableInRAM,161,ReadWrite,CAL_DATA_FD0B_RML_RMR_ST_ADD,NULL,NULL},
    {0xFD0C,(uint8*)Gu16RML_RMR_ChirpThresholdTableInRAM,161,ReadWrite,CAL_DATA_FD0C_RML_RMR_CT_ADD,NULL,NULL},
    {0xFD0D,(uint8*)GStrObjJudgeStandardThresholdTableInRAM,321,ReadWrite,CAL_DATA_FD0D_OBJ_JUDGE_ST_ADD,NULL,NULL},
    {0xFD0E,(uint8*)GStrObjJudgeChirpThresholdTableInRAM,321,ReadWrite,CAL_DATA_FD0E_OBJ_JUDGE_CT_ADD,NULL,NULL},

    {0xFD00,(uint8*)&GstrAPA_OdomCarPara_Ram,(CAL_DATA_FD00_SIZE + 1),ReadWrite,CAL_DATA_FD00_ADD,Did_ReadData_FD00,Did_WriteData_FD00},
    {0xFD20,(uint8*)Gu8TestBuffer,(CAL_DATA_FD20_SIZE + 1),ReadWrite,CAL_DATA_FD20_ADD,NULL,NULL},
    {0xFD21,(uint8*)Gu8TestBuffer,(CAL_DATA_FD21_SIZE + 1),ReadWrite,CAL_DATA_FD21_ADD,NULL,NULL},
    {0xFD22,(uint8*)Gu8TestBuffer,(CAL_DATA_FD22_SIZE + 1),ReadWrite,CAL_DATA_FD22_ADD,NULL,NULL},
    {0xFD23,(uint8*)Gu8TestBuffer,(CAL_DATA_FD23_SIZE + 1),ReadWrite,CAL_DATA_FD23_ADD,NULL,NULL},

    {0xFD24,(uint8*)Gu8TestBuffer,(CAL_DATA_FD24_SIZE + 1),ReadWrite,CAL_DATA_FD24_ADD,NULL,NULL},
    {0xFD25,(uint8*)Gu8TestBuffer,(CAL_DATA_FD25_SIZE + 1),ReadWrite,CAL_DATA_FD25_ADD,NULL,NULL},
    {0xFD26,(uint8*)Gu8TestBuffer,(CAL_DATA_FD26_SIZE + 1),ReadWrite,CAL_DATA_FD26_ADD,NULL,NULL},
    {0xFD27,(uint8*)Gu8TestBuffer,(CAL_DATA_FD27_SIZE + 1),ReadWrite,CAL_DATA_FD27_ADD,NULL,NULL},
    {0xFD28,(uint8*)Gu8TestBuffer,(CAL_DATA_FD28_SIZE + 1),ReadWrite,CAL_DATA_FD28_ADD,NULL,NULL},
    {0xFD29,(uint8*)Gu8TestBuffer,(CAL_DATA_FD29_SIZE + 1),ReadWrite,CAL_DATA_FD29_ADD,NULL,NULL},
    {0xFD2A,(uint8*)Gu8TestBuffer,(CAL_DATA_FD2A_SIZE + 1),ReadWrite,CAL_DATA_FD2A_ADD,NULL,NULL},
    {0xFD2B,(uint8*)Gu8TestBuffer,(CAL_DATA_FD2B_SIZE + 1),ReadWrite,CAL_DATA_FD2B_ADD,NULL,NULL},
    {0xFD2C,(uint8*)Gu8TestBuffer,(CAL_DATA_FD2C_SIZE + 1),ReadWrite,CAL_DATA_FD2C_ADD,NULL,NULL},
    {0xFD2D,(uint8*)Gu8TestBuffer,(CAL_DATA_FD2D_SIZE + 1),ReadWrite,CAL_DATA_FD2D_ADD,NULL,NULL},
    {0xFD2E,(uint8*)Gu8TestBuffer,(CAL_DATA_FD2E_SIZE + 1),ReadWrite,CAL_DATA_FD2E_ADD,NULL,NULL},
    {0xFD2F,(uint8*)Gu8TestBuffer,(CAL_DATA_FD2F_SIZE + 1),ReadWrite,CAL_DATA_FD2F_ADD,NULL,NULL},

    {0xFD40,(uint8*)&GstrMapAreaRam[0][0],(CAL_DATA_FD40_SIZE + 1),ReadWrite,CAL_DATA_FD40_ADD,Did_ReadData_FD40,Did_WriteData_FD40},
    {0xFD41,(uint8*)&GstrMapAreaRam[0][2],(CAL_DATA_FD41_SIZE + 1),ReadWrite,CAL_DATA_FD41_ADD,Did_ReadData_FD41,Did_WriteData_FD41},
    {0xFD42,(uint8*)&GstrMapAreaRam[0][4],(CAL_DATA_FD42_SIZE + 1),ReadWrite,CAL_DATA_FD42_ADD,Did_ReadData_FD42,Did_WriteData_FD42},
    {0xFD43,(uint8*)&GstrMapAreaRam[0][6],(CAL_DATA_FD43_SIZE + 1),ReadWrite,CAL_DATA_FD43_ADD,Did_ReadData_FD43,Did_WriteData_FD43},
    {0xFD44,(uint8*)&GstrMapAreaRam[1][0],(CAL_DATA_FD44_SIZE + 1),ReadWrite,CAL_DATA_FD44_ADD,Did_ReadData_FD44,Did_WriteData_FD44},
    {0xFD45,(uint8*)&GstrMapAreaRam[1][2],(CAL_DATA_FD45_SIZE + 1),ReadWrite,CAL_DATA_FD45_ADD,Did_ReadData_FD45,Did_WriteData_FD45},
    {0xFD46,(uint8*)&GstrMapAreaRam[1][4],(CAL_DATA_FD46_SIZE + 1),ReadWrite,CAL_DATA_FD46_ADD,Did_ReadData_FD46,Did_WriteData_FD46},
    {0xFD47,(uint8*)&GstrMapAreaRam[1][6],(CAL_DATA_FD47_SIZE + 1),ReadWrite,CAL_DATA_FD47_ADD,Did_ReadData_FD47,Did_WriteData_FD47},
    {0xFD48,(uint8*)&GfPointCloudArea_X_Coor_Ram[0][0],(CAL_DATA_FD48_SIZE + 1),ReadWrite,CAL_DATA_FD48_ADD,Did_ReadData_FD48,Did_WriteData_FD48},
    {0xFD49,(uint8*)&GfPointCloudArea_X_Coor_Ram[1][0],(CAL_DATA_FD49_SIZE + 1),ReadWrite,CAL_DATA_FD49_ADD,Did_ReadData_FD49,Did_WriteData_FD49},
    {0xFD50,(uint8*)Gu16FLS_FRS_MasStdThresTableForMapInRAM,(CAL_DATA_FD50_SIZE + 1),ReadWrite,CAL_DATA_FD50_ADD,Did_ReadData_FD50,Did_WriteData_FD50},
    {0xFD51,(uint8*)Gu16FLS_FRS_MasChirpThresTableForMapInRAM,(CAL_DATA_FD51_SIZE + 1),ReadWrite,CAL_DATA_FD51_ADD,Did_ReadData_FD51,Did_WriteData_FD51},
    {0xFD52,(uint8*)Gu16FL_FR_MasStdThresTableForMapInRAM,(CAL_DATA_FD52_SIZE + 1),ReadWrite,CAL_DATA_FD52_ADD,Did_ReadData_FD52,Did_WriteData_FD52},
    {0xFD53,(uint8*)Gu16FL_FR_MasChirpThresTableForMapInRAM,(CAL_DATA_FD53_SIZE + 1),ReadWrite,CAL_DATA_FD53_ADD,Did_ReadData_FD53,Did_WriteData_FD53},
    {0xFD54,(uint8*)Gu16FML_FMR_MasStdThresTableForMapInRAM,(CAL_DATA_FD54_SIZE + 1),ReadWrite,CAL_DATA_FD54_ADD,Did_ReadData_FD54,Did_WriteData_FD54},
    {0xFD55,(uint8*)Gu16FML_FMR_MasChirpThresTableForMapInRAM,(CAL_DATA_FD55_SIZE + 1),ReadWrite,CAL_DATA_FD55_ADD,Did_ReadData_FD55,Did_WriteData_FD55},
    {0xFD56,(uint8*)Gu16RLS_RRS_MasStdThresTableForMapInRAM,(CAL_DATA_FD56_SIZE + 1),ReadWrite,CAL_DATA_FD56_ADD,Did_ReadData_FD56,Did_WriteData_FD56},
    {0xFD57,(uint8*)Gu16RLS_RRS_MasChirpThresTableForMapInRAM,(CAL_DATA_FD57_SIZE + 1),ReadWrite,CAL_DATA_FD57_ADD,Did_ReadData_FD57,Did_WriteData_FD57},
    {0xFD58,(uint8*)Gu16RL_RR_MasStdThresTableForMapInRAM,(CAL_DATA_FD58_SIZE + 1),ReadWrite,CAL_DATA_FD58_ADD,Did_ReadData_FD58,Did_WriteData_FD58},
    {0xFD59,(uint8*)Gu16RL_RR_MasChirpThresTableForMapInRAM,(CAL_DATA_FD59_SIZE + 1),ReadWrite,CAL_DATA_FD59_ADD,Did_ReadData_FD59,Did_WriteData_FD59},
    {0xFD5A,(uint8*)Gu16RML_RMR_MasStdThresTableForMapInRAM,(CAL_DATA_FD5A_SIZE + 1),ReadWrite,CAL_DATA_FD5A_ADD,Did_ReadData_FD5A,Did_WriteData_FD5A},
    {0xFD5B,(uint8*)Gu16RML_RMR_MasChirpThresTableForMapInRAM,(CAL_DATA_FD5B_SIZE + 1),ReadWrite,CAL_DATA_FD5B_ADD,Did_ReadData_FD5B,Did_WriteData_FD5B},
    {0xFD5C,(uint8*)Gu16FLS_FRS_LisStdThresTableForMapInRAM,(CAL_DATA_FD5C_SIZE + 1),ReadWrite,CAL_DATA_FD5C_ADD,Did_ReadData_FD5C,Did_WriteData_FD5C},
    {0xFD5D,(uint8*)Gu16FLS_FRS_LisChirpThresTableForMapInRAM,(CAL_DATA_FD5D_SIZE + 1),ReadWrite,CAL_DATA_FD5D_ADD,Did_ReadData_FD5D,Did_WriteData_FD5D},
    {0xFD5E,(uint8*)Gu16FL_FR_LisStdThresTableForMapInRAM,(CAL_DATA_FD5E_SIZE + 1),ReadWrite,CAL_DATA_FD5E_ADD,Did_ReadData_FD5E,Did_WriteData_FD5E},
    {0xFD5F,(uint8*)Gu16FL_FR_LisChirpThresTableForMapInRAM,(CAL_DATA_FD5F_SIZE + 1),ReadWrite,CAL_DATA_FD5F_ADD,Did_ReadData_FD5F,Did_WriteData_FD5F},
    {0xFD60,(uint8*)Gu16FML_FMR_LisStdThresTableForMapInRAM,(CAL_DATA_FD60_SIZE + 1),ReadWrite,CAL_DATA_FD60_ADD,Did_ReadData_FD60,Did_WriteData_FD60},
    {0xFD61,(uint8*)Gu16FML_FMR_LisChirpThresTableForMapInRAM,(CAL_DATA_FD61_SIZE + 1),ReadWrite,CAL_DATA_FD61_ADD,Did_ReadData_FD61,Did_WriteData_FD61},
    {0xFD62,(uint8*)Gu16RLS_RRS_LisStdThresTableForMapInRAM,(CAL_DATA_FD62_SIZE + 1),ReadWrite,CAL_DATA_FD62_ADD,Did_ReadData_FD62,Did_WriteData_FD62},
    {0xFD63,(uint8*)Gu16RLS_RRS_LisChirpThresTableForMapInRAM,(CAL_DATA_FD63_SIZE + 1),ReadWrite,CAL_DATA_FD63_ADD,Did_ReadData_FD63,Did_WriteData_FD63},
    {0xFD64,(uint8*)Gu16RL_RR_LisStdThresTableForMapInRAM,(CAL_DATA_FD64_SIZE + 1),ReadWrite,CAL_DATA_FD64_ADD,Did_ReadData_FD64,Did_WriteData_FD64},
    {0xFD65,(uint8*)Gu16RL_RR_LisChirpThresTableForMapInRAM,(CAL_DATA_FD65_SIZE + 1),ReadWrite,CAL_DATA_FD65_ADD,Did_ReadData_FD65,Did_WriteData_FD65},
    {0xFD66,(uint8*)Gu16RML_RMR_LisStdThresTableForMapInRAM,(CAL_DATA_FD66_SIZE + 1),ReadWrite,CAL_DATA_FD66_ADD,Did_ReadData_FD66,Did_WriteData_FD66},
    {0xFD67,(uint8*)Gu16RML_RMR_LisChirpThresTableForMapInRAM,(CAL_DATA_FD67_SIZE + 1),ReadWrite,CAL_DATA_FD67_ADD,Did_ReadData_FD67,Did_WriteData_FD67},
    {0xFD70,(uint8*)Gu8TestBuffer,(CAL_DATA_FD70_SIZE + 1),ReadWrite,CAL_DATA_FD70_ADD,NULL,NULL},
    {0xFDA0,(uint8*)Gu8TestBuffer,(CAL_DATA_FDA0_SIZE + 1),ReadWrite,CAL_DATA_FDA0_ADD,NULL,NULL},
    {0xFDA1,(uint8*)Gu8TestBuffer,(CAL_DATA_FDA1_SIZE + 1),ReadWrite,CAL_DATA_FDA1_ADD,NULL,NULL},
    {0xFDA2,(uint8*)Gu8TestBuffer,(CAL_DATA_FDA2_SIZE + 1),ReadWrite,CAL_DATA_FDA2_ADD,NULL,NULL},
    {0xFDA3,(uint8*)Gu8TestBuffer,(CAL_DATA_FDA3_SIZE + 1),ReadWrite,CAL_DATA_FDA3_ADD,NULL,NULL},
    {0xFDA4,(uint8*)Gu8TestBuffer,(CAL_DATA_FDA4_SIZE + 1),ReadWrite,CAL_DATA_FDA4_ADD,NULL,NULL},

};

#endif

/******************************************************************
* 函数名称: LonghornSecurityAlgorithm
*
* 功能描述: 豪恩自定义的安全访问算法，用于所有平台的标定使用
* 输入参数: Lu32Seed--4字节的种子(高字节在前，低字节在后)
*
* 输出参数: 无
*
* 返 回 值: Lu32Key--最终计算的4字节秘钥(高字节在前，低字节在后)
*
* 其它说明: 
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
uint32 LonghornSecurityAlgorithm(uint32 Lu32Seed)
{
    uint32 Lu32Key;
    uint8 Lu8SeedMaskIndex;
    uint32 Lu32SeedTemp;
    
    Lu32SeedTemp = Lu32Seed;
    Lu8SeedMaskIndex = (uint8)(Lu32SeedTemp & 0x000F);
    Lu32Key = (Lu32SeedTemp ^(Gu32LonghornSecuritMask[Lu8SeedMaskIndex]));
	return Lu32Key;
}


/******************************************************************
* 函数名称: CalibrationDIDPowerOnEepromInit
*
* 功能描述: ECU上电后从EEPROM中加载标定数据到RAM中
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 仅用于研发标定；正式软件该宏不会使能
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
#if (CALIBRATION_EN == STD_ON)
void CalibrationDIDPowerOnEepromInit(void)
{
    uint8 i;

    for(i = 0; i < DID_CALIB_NUM; i++)
    {
        if((CalibrationDIDDefine[i].GcDir == ReadWrite)&&((uint8*)CalibrationDIDDefine[i].GcADDR != NULL))
        {
            ReadToCodeFlash(CalibrationDIDDefine[i].GdwADDR, CalibrationDIDDefine[i].GwDataLen, (uint8*)CalibrationDIDDefine[i].GcADDR);
        }
    }
}
#endif
/******************************************************************
* 函数名称: InitCalibrationDIDToEeprom
*
* 功能描述: MCU第一次上电，初始化系统DID到EEPROM中
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 仅用于研发标定；正式软件该宏不会使能
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
#if (CALIBRATION_EN == STD_ON)
void InitCalibrationDIDToEeprom(void)
{
    uint32* Lpu32BlockAVal = (uint32*)CAL_FLASH_A_FLAG_ADDRESS;
    uint32* Lpu32BlockBVal = (uint32*)CAL_FLASH_B_FLAG_ADDRESS;;
    uint8 i;
    uint8 Lu8DataTem[360];

    if ((*Lpu32BlockAVal) == CAL_FLASH_VALID_BLOCK_VAL)
    {
        //从A分区读取标定数据到Ram
        for (i = FLS_FRS_ST; i < CAL_DATA_FD00; i++)
        {
            if ((CalibrationDIDDefine[i].GcDir == ReadWrite) && ((uint8*)CalibrationDIDDefine[i].GcADDR != NULL))
            {
                ReadToCodeFlash((CalibrationDIDDefine[i].GdwADDR + CAL_AREA_A_START), (CalibrationDIDDefine[i].GwDataLen - 1), (uint8*)CalibrationDIDDefine[i].GcADDR);
            }
        }
        for (i = CAL_DATA_FD00; i < DID_CALIB_NUM; i++)
        {
            if ((CalibrationDIDDefine[i].GcDir == ReadWrite) && ((uint8*)CalibrationDIDDefine[i].GdwADDR != NULL) && ((uint8*)CalibrationDIDDefine[i].pfWriteData != NULL))
            {
                ReadToCodeFlash((CalibrationDIDDefine[i].GdwADDR + CAL_AREA_A_START), (CalibrationDIDDefine[i].GwDataLen - 1), &Lu8DataTem[0]);
                (void)Did_WriteData(i, 0xFF, (CalibrationDIDDefine[i].GwDataLen - 1), &Lu8DataTem[0]);
            }
        }
    }
    else if ((*Lpu32BlockBVal) == CAL_FLASH_VALID_BLOCK_VAL)
    {
        //从B分区读取标定数据到Ram
        for (i = FLS_FRS_ST; i < CAL_DATA_FD00; i++)
        {
            if ((CalibrationDIDDefine[i].GcDir == ReadWrite) && ((uint8*)CalibrationDIDDefine[i].GcADDR != NULL))
            {
                ReadToCodeFlash((CalibrationDIDDefine[i].GdwADDR + CAL_AREA_B_START), (CalibrationDIDDefine[i].GwDataLen-1), (uint8*)CalibrationDIDDefine[i].GcADDR);
            }
        }

        /*若无标定数据则从默认值接口读到Ram中*/
        for (i = CAL_DATA_FD00; i < DID_CALIB_NUM; i++)
        {
            if ((CalibrationDIDDefine[i].GcDir == ReadWrite) && ((uint8*)CalibrationDIDDefine[i].GdwADDR != NULL) && ((uint8*)CalibrationDIDDefine[i].pfWriteData != NULL))
            {
                ReadToCodeFlash((CalibrationDIDDefine[i].GdwADDR + CAL_AREA_B_START), (CalibrationDIDDefine[i].GwDataLen - 1), &Lu8DataTem[0]);
                (void)Did_WriteData(i, 0xFFFF, (CalibrationDIDDefine[i].GwDataLen - 1), &Lu8DataTem[0]);
            }
        }
    }
    else
    {
        //Fls_Erase(CAL_FLASH_B_START_BLOCK, CAL_FLASH_B_BLOCK_LEN);
        //Fls_Erase(CAL_FLASH_A_START_BLOCK, CAL_FLASH_A_BLOCK_LEN);
        /*若无标定数据则从默认值接口读到Ram中*/
        //CopySnsCalibWidthAndThresholdToRam();
        //for (i = FLS_FRS_ST; i < CAL_DATA_FD40; i++)//将默认值写入CodeFlash；
        //{
        //    if ((CalibrationDIDDefine[i].GcDir == ReadWrite) && ((uint8*)CalibrationDIDDefine[i].GcADDR != NULL))
        //    {
        //         //Did_ReadData(i, 0xFFFF, (CalibrationDIDDefine[i].GwDataLen-1), &Lu8DataTem[0]);
        //         WriteToCodeFlash(CalibrationDIDDefine[i].GdwADDR, CalibrationDIDDefine[i].GwDataLen, (uint8*)CalibrationDIDDefine[i].GcADDR);
        //    }
        //}
        ///**/
        //for (i = CAL_DATA_FD40; i < DID_CALIB_NUM; i++)//将默认值写入CodeFlash；
        //{
        //    if ((CalibrationDIDDefine[i].GcDir == ReadWrite) && ((uint8*)CalibrationDIDDefine[i].GcADDR != NULL) && ((uint8*)CalibrationDIDDefine[i].pfReadData != NULL))
        //    {
        //        Did_ReadData(i, 0xFFFF, (CalibrationDIDDefine[i].GwDataLen - 1), &Lu8DataTem[0]);
        //        WriteToCodeFlash(CalibrationDIDDefine[i].GdwADDR, CalibrationDIDDefine[i].GwDataLen, (const uint8_t*)&Lu8DataTem[0]);
        //    }
        //}
    }
}

#endif
