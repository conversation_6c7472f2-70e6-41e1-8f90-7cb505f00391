
#ifndef __UDS_TP_INTERFACE_H__
#define __UDS_TP_INTERFACE_H__


/*******************************************************************************
* Includes
********************************************************************************/
#include "Types.h"
#include "TP_Config.h"


/******************************************************************************
* Constants and macros
*******************************************************************************/
#define UDS_LEN_NRC                     3U
#define UDS_FALSE                       (uint8)0x00U
#define UDS_TRUE                        (uint8)0x01U
#define UDS_NRC                         (uint8)0x7FU
#define UDS_ACK                         (uint8) 0x40U    /* Positive answer */
#define UDS_NRC_11                      (uint8) 0x11U    /* Non-supported service */
#define UDS_NRC_12                      (uint8) 0x12U    /* Non-supported sub-service */
#define UDS_NRC_13                      (uint8) 0x13U    /* Invalid request length or invalid format */
#define UDS_NRC_14                      (uint8) 0x14U	 /* Exceeded the maximum number of DID */
#define UDS_NRC_22                      (uint8) 0x22U    /* Unsatisfied conditions */
#define UDS_NRC_24                      (uint8) 0x24U    /* Links not respected */
#define UDS_NRC_31                      (uint8) 0x31U    /* Invalid parameters */
#define UDS_NRC_33                      (uint8) 0x33U    /* ECU is locked */
#define UDS_NRC_35                      (uint8) 0x35U    /* Invalid key */
#define UDS_NRC_36                      (uint8) 0x36U    /* Unlocked attempt overrun */
#define UDS_NRC_37                      (uint8) 0x37U    /* delay between 2 unlocked attempt is elapsed */
#define UDS_NRC_72                      (uint8) 0x72U    /* Erasing or writing error volatil memory */
#define UDS_NRC_73                      (uint8) 0x73U    /* Error in erasing or writing sequence  */
#define UDS_NRC_78                      (uint8) 0x78U    /* Request well received / Answer constitution in progress */
#define UDS_NRC_83                      (uint8) 0x83U    /* engineIsRunning */
#define UDS_NRC_88                      (uint8) 0x88U    /* vehicleSpeedTooHigh */
#define UDS_NRC_7E                      (uint8) 0x7EU    /* Sub-service supported by the ECU but not in the active session */
#define UDS_NRC_7F                      (uint8) 0x7FU    /* Service supported by the ECU but not in the active session */
#define UDS_ERR_COHE                    (uint8) 0xFFU    /* SDF UDS error */


#define UDS_P2TIME                      3000                      

#define UDS_LONG_REQ_OFF                0U    /* No NACK_78 running */
#define UDS_LONG_REQ_ON                 1U    /* NACK_78 is sent every UDS_NRC78_PERIOD ms */




/******************************************************************************
* Global objects 
******************************************************************************/
#ifdef UDS_TP_OBJ
  TPUDSStr GsUDS_TP_Interface;
  uint16 GwWaitRespondTimer;                /* TP层接收到数据之后，等待UDS层响应的时间 */
#else
  extern TPUDSStr GsUDS_TP_Interface;
  extern uint16 GwWaitRespondTimer;                 /* TP层接收到数据之后，等待UDS层响应的时间 */
  
#endif

extern volatile uint8 GcUdsACKType;

/****************************************************************************
* Global Functions
****************************************************************************/
 uint8 TP_UDSParaInit(TPUDSStr *LsTPUDSParaCfg);







#endif




