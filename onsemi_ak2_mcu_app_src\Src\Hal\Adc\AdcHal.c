/******************************************************************************
 * Copyright (c) Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * All Rights Reserved.
 *
 * FileName : AdcHal.c
 * Brief    : None
 * Date     : 2021-07-08 17:20
 * Author   : 22446
 ******************************************************************************/

/******************************************************************************/
/* Includes ------------------------------------------------------------------*/
/******************************************************************************/
#include "AdcHal.h"
#include "AdcDrv.h"

/******************************************************************************/
/******************************************************************************/
/****************************** Private Definition ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Public Definition *****************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************************
 * 描     述: ADC 检测工作结构体
 * 设计索引:
 *****************************************************************************/
AdcDete_WorkType GstrAdcDeteWork[ADCDETE_GROUP_NUM];
uint16 ADC_reseult[ADC0_GROUP1_NUM] = {0};

/******************************************************************************/
/******************************************************************************/
/****************************** Private Function ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/******************************* Public Function ******************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************************/
/**
 * @brief           初始化
 * @param           None
 * @return          None
 */
/******************************************************************************/
void AdcHalInit(void)
{
    AdcDete_WorkType *LpstrWork;

	ADCDrvInit();
	
    LpstrWork = &GstrAdcDeteWork[ADCDETE_GROUP_ADC0_GROUP1];	
	LpstrWork->vDetectBusy 		   = TRUE;
    LpstrWork->u8ChannelNum        = ADC0_GROUP1_NUM;
    LpstrWork->u32ResultLastOffset = 0;
    LpstrWork->pAdcDeteValue       = &ADC_reseult[0];
	LpstrWork->u8AdcDeteTimeOutCnt = 0;
	LpstrWork->u8AdcDeteTimeOut    = 1; /* 1*5ms = 5ms(5ms AdcDete period)*/
}

/******************************************************************************/
/**
 * @brief           反初始化
 * @param           None
 * @return          None
 */
/******************************************************************************/
void AdcHal_Deinit(void)
{
    AdcDete_WorkType* LpstrWork;

    LpstrWork = &GstrAdcDeteWork[ADCDETE_GROUP_ADC0_GROUP1];
    LpstrWork->vDetectBusy         = TRUE;
    LpstrWork->u8ChannelNum        = ADC0_GROUP1_NUM;
    LpstrWork->u32ResultLastOffset = 0;
	ADCDrvClose();
}

/******************************************************************************/
/**
 * @brief           主函数
 * @param           None
 * @return          None
 */
/******************************************************************************/
void AdcHal_MainFunc(void)
{
    uint8 Lu8GroupId;
    AdcDete_WorkType *LpstrWork;
	
    for(Lu8GroupId = 0u; Lu8GroupId < ADCDETE_GROUP_NUM; Lu8GroupId++)
    {
        LpstrWork = &GstrAdcDeteWork[Lu8GroupId];
        if(LpstrWork->vDetectBusy == FALSE)
        {
            LpstrWork->u8AdcDeteTimeOutCnt = 0;
            LpstrWork->vDetectBusy = TRUE; 
            ADC0_ScanGroup1_Start();
        }
        else
        {
            LpstrWork->u8AdcDeteTimeOutCnt++;
            if(LpstrWork->u8AdcDeteTimeOutCnt >= LpstrWork->u8AdcDeteTimeOut)
            {
                    LpstrWork->vDetectBusy = FALSE; 
            }
        }
    }
}

/******************************************************************************/
/**
 * @brief           ADC 检测完成回调
 * @param[in]       None
 * @return          None
 */
/******************************************************************************/
void AdcHal_Adc0Group1CallBack(void)
{
    AdcDete_WorkType* LpstrWork;

    LpstrWork = &GstrAdcDeteWork[ADCDETE_GROUP_ADC0_GROUP1];
	
    ADC0_ScanGroup1_GetResult((uint16 * const)LpstrWork->pAdcDeteValue);
	
    LpstrWork->vDetectBusy = FALSE;
}

/******************************************************************************/
/**
* @brief		   获取ADC结果
* @param[in]	   LenuGroupId : ADC模块索引
* @param[in]	   Lu8AdcChannel : ADC通道索引
* @return		   ADC结果
*/
/******************************************************************************/
uint16 AdcHal_GetAdcResult(AdcDete_GroupType LenuGroupId,AdcDete_ChlIdType Lu8AdcChannel)
{
	AdcDete_WorkType* LpstrWork;

	if((LenuGroupId >= ADCDETE_GROUP_NUM) || (Lu8AdcChannel >= ADC0_GROUP1_NUM))
	{
		return 0;
	}
	
   	LpstrWork = &GstrAdcDeteWork[LenuGroupId];

   	return LpstrWork->pAdcDeteValue[Lu8AdcChannel];

	
}

