/******************************************************************************
* MODIFICATION LOG :                                                           
*******************************************************************************
*                                                                              
* !Designed by     : Fris                            !Date : 2010.10.30        
* !Coded by        : Fris                            !Date : 2010.10.30        
*                                                                              
* Rev 1.0    2020.10.30                              Initial release           
*******************************************************************************/
#ifndef __POWER_MANAGE_H__
#define __POWER_MANAGE_H__
/* Includes ------------------------------------------------------------------*/
#include "Power_ManageCfg.h"

/******************************************************************************
*
* Macro definition
*
*******************************************************************************/	

/******************************************************************************
* 设计描述 : 对外接口声明
* 设计索引 : 
*******************************************************************************/
extern volatile uint16 GwADMeanValue;
extern volatile uint16 Gu16ADMomentaryValue;
extern uint16 Gu16PwrADVal[PowerAdcIdxNum];

/******************************************************************************
* 设计描述 : 函数对外声明
* 设计索引 : 
*******************************************************************************/
void PowerManage_Init(void);
void PowerManage_Task(void);

#endif

/*END __POWER_MANAGE_H__*******************************************************/

