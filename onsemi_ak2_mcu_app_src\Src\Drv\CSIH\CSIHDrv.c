/***********************************************************************************************************************
* DISCLAIMER
* This software is supplied by Renesas Electronics Corporation and is only 
* intended for use with Renesas products. No other uses are authorized. This 
* software is owned by Renesas Electronics Corporation and is protected under 
* all applicable laws, including copyright laws.
* THIS SOFTWARE IS PROVIDED "AS IS" AND R<PERSON><PERSON>AS MAKES NO WARRANTIES REGARDING 
* THIS SOFTWARE, WHETHER EXPRESS, IMPLIED OR STATUTORY, INCLUDING BUT NOT 
* LIMITED TO WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE 
* AND NON-INFRINGEMENT.  ALL SUCH WARRANTIES ARE EXPRESSLY DISCLAIMED.
* TO THE MAXIMUM EXTENT PERMITTED NOT PROHIBITED BY LAW, NEITHER RENESAS 
* ELECTRONICS CORPORATION NOR ANY OF ITS AFFILIATED COMPANIES SHALL BE LIABLE 
* FOR ANY DIRECT, INDIRECT, SPECIAL, INCIDENTAL OR CONSEQUENTIAL DAMAGES FOR 
* ANY REASON RELATED TO THIS SOFTWARE, EVEN IF RENESAS OR ITS AFFILIATES HAVE 
* BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.
* Renesas reserves the right, without notice, to make changes to this software 
* and to discontinue the availability of this software.  By using this software, 
* you agree to the additional terms and conditions found by accessing the 
* following link:
* http://www.renesas.com/disclaimer
*
* Copyright (C) 2015, 2018 Renesas Electronics Corporation. All rights reserved.
***********************************************************************************************************************/

/***********************************************************************************************************************
* File Name    : r_cg_csih.c
* Version      : Applilet4 for RH850/F1K V1.01.02.02 [08 May 2018]
* Device(s)    : R7F701581(LQFP100pin)
* Tool-Chain   : CCRH
* Description  : This file implements device driver for CSIH module.
* Creation Date: 2023/10/12
***********************************************************************************************************************/

/***********************************************************************************************************************
Pragma directive
***********************************************************************************************************************/
/* Start user code for pragma. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */

/***********************************************************************************************************************
Includes
***********************************************************************************************************************/
#include "CSIHDrv.h"
#include "IODrv.h"
/* Start user code for include. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */

/***********************************************************************************************************************
Global variables and functions
***********************************************************************************************************************/
volatile uint32 g_cg_sync_read;
volatile uint16  g_csih0_tx_num;                        /* csih0 transmit data number */
volatile uint16  g_csih0_rx_num;                        /* csih0 receive data number */
volatile uint16  g_csih0_rx_total_num;                  /* csih0 receive data total times */
volatile uint16 * gp_csih0_tx_address;                   /* csih0 transmit buffer address */
volatile uint16 * gp_csih0_rx_address;                   /* csih0 receive buffer address */
volatile uint16  g_csih2_tx_num;                        /* csih2 transmit data number */
volatile uint16  g_csih2_rx_num;                        /* csih2 receive data number */
volatile uint16  g_csih2_rx_total_num;                  /* csih2 receive data total times */
volatile uint16 * gp_csih2_tx_address;                   /* csih2 transmit buffer address */
volatile uint16 * gp_csih2_rx_address;                   /* csih2 receive buffer address */
volatile uint16  g_csih3_tx_num;                        /* csih3 transmit data number */
volatile uint16  g_csih3_rx_num;                        /* csih3 receive data number */
volatile uint16  g_csih3_rx_total_num;                  /* csih3 receive data total times */
volatile uint16 * gp_csih3_tx_address;                   /* csih3 transmit buffer address */
volatile uint16 * gp_csih3_rx_address;                   /* csih3 receive buffer address */

/* Start user code for global. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */
/******************************************************************************/
/**<pre>
 *函数名称: CSIHDrvInit
 *功能描述: CSIH外设初始化
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void CSIHDrvInit(void)
{
    R_CSIH0_Create();
    R_CSIH2_Create();
    R_CSIH3_Create();
    R_CSIH0_Start();
    R_CSIH2_Start();
    R_CSIH3_Start();
}
/******************************************************************************/
/**<pre>
 *函数名称: CSIHDrvStop
 *功能描述: 关闭CSIH外设
 *输入参数: 无
 *输出参数: 无
 *返回数据: 无
 *修改记录: 无
*********************************************************************** </pre>*/
void CSIHDrvStop(void)
{
    R_CSIH0_Stop();
    R_CSIH2_Stop();
    R_CSIH3_Stop();
}

/***********************************************************************************************************************
* Function Name: R_CSIH0_Create
* Description  : This function initializes the CSIH0 module.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_CSIH0_Create(void)
{
    uint32 tmp_port;

    /* Disable CSIH0 operation */
    CSIH0.CTL0 = _CSIH_OPERATION_CLOCK_STOP;
    /* Disable INTCSIH0IC operation and clear request */
    INTC1.ICCSIH0IC.BIT.MKCSIH0IC = _INT_PROCESSING_DISABLED;
    INTC1.ICCSIH0IC.BIT.RFCSIH0IC = _INT_REQUEST_NOT_OCCUR;
    /* Disable INTCSIH0IR operation and clear request */
    INTC1.ICCSIH0IR.BIT.MKCSIH0IR = _INT_PROCESSING_DISABLED;
    INTC1.ICCSIH0IR.BIT.RFCSIH0IR = _INT_REQUEST_NOT_OCCUR;
    /* Disable INTCSIH0IRE operation and clear request */
    INTC1.ICCSIH0IRE.BIT.MKCSIH0IRE = _INT_PROCESSING_DISABLED;
    INTC1.ICCSIH0IRE.BIT.RFCSIH0IRE = _INT_REQUEST_NOT_OCCUR;
    /* Set CSIH0 interrupt(INTCSIH0IC) setting */
    INTC1.ICCSIH0IC.BIT.TBCSIH0IC = _INT_TABLE_VECTOR;
    INTC1.ICCSIH0IC.UINT16 &= _INT_PRIORITY_LOWEST;
    /* Set CSIH0 interrupt(INTCSIH0IR) setting */
    INTC1.ICCSIH0IR.BIT.TBCSIH0IR = _INT_TABLE_VECTOR;
    INTC1.ICCSIH0IR.UINT16 &= _INT_PRIORITY_LOWEST;
    /* Set CSIH0 interrupt(INTCSIH0IRE) setting */
    INTC1.ICCSIH0IRE.BIT.TBCSIH0IRE = _INT_TABLE_VECTOR;
    INTC1.ICCSIH0IRE.UINT16 &= _INT_PRIORITY_LOWEST;
    /* Set CSIH0 control setting */
    CSIH0.CTL1 = _CSIH_CLOCK_INVERTING_LOW | _CSIH_INTERRUPT_TIMING_TRANSFERRED | _CSIH_CHIPSELECT0_ACTIVE_LOW | _CSIH_DATA_CONSISTENCY_CHECK_DISABLE | 
                 _CSIH_CHIPSELECT_SIGNAL_HOLD_INACTIVE | _CSIH_NO_DELAY | _CSIH_HANDSHAKE_DISABLE | 
                 _CSIH_SLAVE_SELECT_ENABLE;
    CSIH0.CTL2 = _CSIH0_SELECT_BASIC_CLOCK;
    CSIH0.BRS0 = _CSIH0_BAUD_RATE_0;
    CSIH0.BRS1 = _CSIH0_BAUD_RATE_1;
    CSIH0.BRS2 = _CSIH0_BAUD_RATE_2;
    CSIH0.BRS3 = _CSIH0_BAUD_RATE_3;
    /* Set CSIH0 configuration setting */
    CSIH0.CFG0 = _CSIH_USED_BAUDRATE_0 | _CSIH_PARITY_NO | _CSIH_DATA_LENGTH_8 | _CSIH_DATA_DIRECTION_MSB | 
                 _CSIHn_MASTER_PHASE_SELECTION_TYPE2 | _CSIH_IDLE_INSERTED_NOT_ALWAYS | _CSIH_IDLE_TIME_0 | 
                 _CSIH_HOLD_TIME_0 | _CSIH_INTER_DATA_DELAY_TIME_0 | _CSIH_SETUP_TIME_0;
    /* Synchronization processing */
    g_cg_sync_read = CSIH0.CTL1;

    /* Set CSIH0SC pin */
    PORT.PIBC0 &= _PORT_CLEAR_BIT2;
    PORT.PBDC0 &= _PORT_CLEAR_BIT2;
    PORT.PM0 |= _PORT_SET_BIT2;
    PORT.PMC0 &= _PORT_CLEAR_BIT2;
    PORT.PIPC0 &= _PORT_CLEAR_BIT2;
    tmp_port = PORT.PDSC0;
    PORT.PPCMD0 = _WRITE_PROTECT_COMMAND;
    PORT.PDSC0 = (tmp_port | _PORT_SET_BIT2);
    PORT.PDSC0 = (uint32) ~(tmp_port | _PORT_SET_BIT2);
    PORT.PDSC0 = (tmp_port | _PORT_SET_BIT2);
    PORT.PFC0 |= _PORT_SET_BIT2;
    PORT.PFCE0 |= _PORT_SET_BIT2;
    PORT.PFCAE0 &= _PORT_CLEAR_BIT2;
    PORT.PIPC0 |= _PORT_SET_BIT2;
    PORT.PMC0 |= _PORT_SET_BIT2;
    /* Set CSIH0SO pin */
    PORT.PIBC0 &= _PORT_CLEAR_BIT3;
    PORT.PBDC0 &= _PORT_CLEAR_BIT3;
    PORT.PM0 |= _PORT_SET_BIT3;
    PORT.PMC0 &= _PORT_CLEAR_BIT3;
    PORT.PIPC0 &= _PORT_CLEAR_BIT3;
    tmp_port = PORT.PDSC0;
    PORT.PPCMD0 = _WRITE_PROTECT_COMMAND;
    PORT.PDSC0 = (tmp_port | _PORT_SET_BIT3);
    PORT.PDSC0 = (uint32) ~(tmp_port | _PORT_SET_BIT3);
    PORT.PDSC0 = (tmp_port | _PORT_SET_BIT3);
    PORT.PFC0 |= _PORT_SET_BIT3;
    PORT.PFCE0 |= _PORT_SET_BIT3;
    PORT.PFCAE0 &= _PORT_CLEAR_BIT3;
    PORT.PIPC0 |= _PORT_SET_BIT3;
    PORT.PMC0 |= _PORT_SET_BIT3;
    /* Set CSIH0SI pin */
    PORT.PIBC0 &= _PORT_CLEAR_BIT1;
    PORT.PBDC0 &= _PORT_CLEAR_BIT1;
    PORT.PM0 |= _PORT_SET_BIT1;  
    PORT.PMC0 &= _PORT_CLEAR_BIT1;
    PORT.PFC0 |= _PORT_SET_BIT1;  
    PORT.PFCE0 |= _PORT_SET_BIT1;  
    PORT.PFCAE0 &= _PORT_CLEAR_BIT1;
    PORT.PMC0 |= _PORT_SET_BIT1;  
    /* Set CSIH0CSS0 pin */
    PORT.PIBC8 &= _PORT_CLEAR_BIT2;
    PORT.PBDC8 &= _PORT_CLEAR_BIT2;
    PORT.PM8 |= _PORT_SET_BIT2;  
    PORT.PMC8 &= _PORT_CLEAR_BIT2;
    PORT.PFC8 |= _PORT_SET_BIT2;
    PORT.PFCE8 &= _PORT_CLEAR_BIT2;  
    PORT.PMC8 |= _PORT_SET_BIT2;  
    PORT.PM8 &= _PORT_CLEAR_BIT2;
}
/***********************************************************************************************************************
* Function Name: R_CSIH0_Start
* Description  : This function starts the CSIH0 module operation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_CSIH0_Start(void)
{
    /* Enable CSIH0 operation */
    CSIH0.CTL0 = _CSIH_OPERATION_CLOCK_PROVIDE | _CSIH_TRANSMISSION_PERMIT | _CSIH_RECEPTION_PERMIT | 
                 _CSIH_DIRECTACCESS;
    /* Clear CSIH0 interrupt request and enable operation */
    INTC1.ICCSIH0IC.BIT.RFCSIH0IC = _INT_REQUEST_NOT_OCCUR;
    INTC1.ICCSIH0IR.BIT.RFCSIH0IR = _INT_REQUEST_NOT_OCCUR;
    INTC1.ICCSIH0IRE.BIT.RFCSIH0IRE = _INT_REQUEST_NOT_OCCUR;
    INTC1.ICCSIH0IC.BIT.MKCSIH0IC = _INT_PROCESSING_DISABLED;
    INTC1.ICCSIH0IR.BIT.MKCSIH0IR = _INT_PROCESSING_DISABLED;
    INTC1.ICCSIH0IRE.BIT.MKCSIH0IRE = _INT_PROCESSING_ENABLED;
}
/***********************************************************************************************************************
* Function Name: R_CSIH0_Stop
* Description  : This function stops the CSIH0 module operation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_CSIH0_Stop(void)
{
    /* Disable CSIH0 interrupt operation */
    INTC1.ICCSIH0IC.BIT.MKCSIH0IC = _INT_PROCESSING_DISABLED;
    INTC1.ICCSIH0IR.BIT.MKCSIH0IR = _INT_PROCESSING_DISABLED;
    INTC1.ICCSIH0IRE.BIT.MKCSIH0IRE = _INT_PROCESSING_DISABLED;
    /* Disable CSIH0 operation */
    CSIH0.CTL0 &= (uint8) ~_CSIH_RECEPTION_PERMIT;
    CSIH0.CTL0 &= (uint8) ~_CSIH_TRANSMISSION_PERMIT;
    CSIH0.CTL0 &= (uint8) ~_CSIH_OPERATION_CLOCK_PROVIDE;
    /* Synchronization processing */
    g_cg_sync_read = CSIH0.CTL0;

    /* Clear CSIH0 interrupt request */
    INTC1.ICCSIH0IC.BIT.RFCSIH0IC = _INT_REQUEST_NOT_OCCUR;
    INTC1.ICCSIH0IR.BIT.RFCSIH0IR = _INT_REQUEST_NOT_OCCUR;
    INTC1.ICCSIH0IRE.BIT.RFCSIH0IRE = _INT_REQUEST_NOT_OCCUR;
    /* Synchronization processing */
    g_cg_sync_read = INTC1.ICCSIH0IC.UINT16;

}
/***********************************************************************************************************************
* Function Name: R_CSIH0_Master_Receive
* Description  : This function receives CSIH0 data.
* Arguments    : rx_buf -
*                    receive buffer pointer
*                rx_num -
*                    buffer size
*                chipId -
*                    the chip_id that Receving from
* Return Value : status -
*                    MD_OK or MD_ARGERROR
***********************************************************************************************************************/
uint16 R_CSIH0_Master_Receive(uint16* rx_buf, uint16 rx_num, uint32 chip_id)
{
    uint16 status = MD_OK;
    uint32 regValue = _CSIH_RECEIVE_SETTING_INIT;
    
    if ((rx_num < 1U) | (chip_id < _CSIH_SELECT_CHIP_0) | (chip_id > _CSIH_SELECT_CHIP_7))
    {
        status = MD_ARGERROR;
    }
    else
    {
        /* Set select chip id */
        regValue &= ~(chip_id);
        CSIH0.RX0W = regValue; 
        /* Set receive setting */
        gp_csih0_rx_address = rx_buf;
        g_csih0_rx_total_num = rx_num; 
        g_csih0_rx_num = 0U;
    }

    return (status);
}
/***********************************************************************************************************************
* Function Name: R_CSIH0_Master_Send
* Description  : This function sends CSIH0 data.
* Arguments    : tx_buf -
*                    send buffer pointer
*                tx_num -
*                    buffer size
*                chipId -
*                    set chip select id
* Return Value : status -
*                    MD_OK or MD_ARGERROR
***********************************************************************************************************************/
uint16 R_CSIH0_Master_Send(const uint16 * tx_buf, uint16 tx_num, uint32 chip_id)
{
    uint16 status = MD_OK;
    uint32 regValue = _CSIH_TRANSMIT_SETTING_INIT;

    if ((tx_num < 1U) | (chip_id < _CSIH_SELECT_CHIP_0) | (chip_id > _CSIH_SELECT_CHIP_SUM_0))
    {
        status = MD_ARGERROR;
    }
    else
    {
        /* Set select chip id */
        regValue &= ~(chip_id);
        /* Set transmit setting */
        gp_csih0_tx_address = (uint16 *)tx_buf; 
        g_csih0_tx_num = tx_num; 
        regValue |= *gp_csih0_tx_address;
        /* Disable CSIH0 interrupt operation */
        INTC1.ICCSIH0IC.BIT.MKCSIH0IC = _INT_PROCESSING_DISABLED;
        /* Synchronization processing */
        g_cg_sync_read = INTC1.ICCSIH0IC.UINT16;

        /* Set transmit data */
        CSIH0.TX0W = regValue;
        gp_csih0_tx_address++;
        g_csih0_tx_num--;
        /* Synchronization processing */
        g_cg_sync_read = CSIH0.CTL1;
 
        /* Enable CSIH0 interrupt operation */
        INTC1.ICCSIH0IC.BIT.MKCSIH0IC = _INT_PROCESSING_ENABLED;
        /* Synchronization processing */
        g_cg_sync_read = INTC1.ICCSIH0IC.UINT16;

    }

    return status;
}

/***********************************************************************************************************************
* Function Name: R_CSIH2_Create
* Description  : This function initializes the CSIH2 module.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_CSIH2_Create(void)
{
    uint32 tmp_port;

    /* Disable CSIH2 operation */
    CSIH2.CTL0 = _CSIH_OPERATION_CLOCK_STOP;
    /* Disable INTCSIH2IC operation and clear request */
    INTC2.ICCSIH2IC.BIT.MKCSIH2IC = _INT_PROCESSING_DISABLED;
    INTC2.ICCSIH2IC.BIT.RFCSIH2IC = _INT_REQUEST_NOT_OCCUR;
    /* Disable INTCSIH2IR operation and clear request */
    INTC2.ICCSIH2IR.BIT.MKCSIH2IR = _INT_PROCESSING_DISABLED;
    INTC2.ICCSIH2IR.BIT.RFCSIH2IR = _INT_REQUEST_NOT_OCCUR;
    /* Disable INTCSIH2IRE operation and clear request */
    INTC2.ICCSIH2IRE.BIT.MKCSIH2IRE = _INT_PROCESSING_DISABLED;
    INTC2.ICCSIH2IRE.BIT.RFCSIH2IRE = _INT_REQUEST_NOT_OCCUR;
    /* Set CSIH2 interrupt(INTCSIH2IC) setting */
    INTC2.ICCSIH2IC.BIT.TBCSIH2IC = _INT_TABLE_VECTOR;
    INTC2.ICCSIH2IC.UINT16 &= _INT_PRIORITY_LEVEL12;
    /* Set CSIH2 interrupt(INTCSIH2IR) setting */
    INTC2.ICCSIH2IR.BIT.TBCSIH2IR = _INT_TABLE_VECTOR;
    INTC2.ICCSIH2IR.UINT16 &= _INT_PRIORITY_LOWEST;
    /* Set CSIH2 interrupt(INTCSIH2IRE) setting */
    INTC2.ICCSIH2IRE.BIT.TBCSIH2IRE = _INT_TABLE_VECTOR;
    INTC2.ICCSIH2IRE.UINT16 &= _INT_PRIORITY_LOWEST;
    /* Set CSIH2 control setting */
    CSIH2.CTL1 = _CSIH_CLOCK_INVERTING_HIGH | _CSIH_INTERRUPT_TIMING_TRANSFERRED | _CSIH_CHIPSELECT0_ACTIVE_LOW | _CSIH_DATA_CONSISTENCY_CHECK_DISABLE | 
                 _CSIH_CHIPSELECT_SIGNAL_HOLD_INACTIVE | _CSIH_NO_DELAY | _CSIH_HANDSHAKE_DISABLE | 
                 _CSIH_SLAVE_SELECT_ENABLE;
    CSIH2.CTL2 = _CSIH2_SELECT_BASIC_CLOCK;
    CSIH2.BRS0 = _CSIH2_BAUD_RATE_0;
    CSIH2.BRS1 = _CSIH2_BAUD_RATE_1;
    CSIH2.BRS2 = _CSIH2_BAUD_RATE_2;
    CSIH2.BRS3 = _CSIH2_BAUD_RATE_3;
    /* Set CSIH2 configuration setting */
    CSIH2.CFG0 = _CSIH_USED_BAUDRATE_0 | _CSIH_PARITY_NO | _CSIH_DATA_LENGTH_8 | _CSIH_DATA_DIRECTION_MSB | 
                 _CSIHn_MASTER_PHASE_SELECTION_TYPE1 | _CSIH_IDLE_INSERTED_NOT_ALWAYS | _CSIH_IDLE_TIME_0 | 
                 _CSIH_HOLD_TIME_0 | _CSIH_INTER_DATA_DELAY_TIME_0 | _CSIH_SETUP_TIME_0;
    /* Synchronization processing */
    g_cg_sync_read = CSIH2.CTL1;

    /* Set CSIH2SC pin */
    PORT.PIBC11 &= _PORT_CLEAR_BIT3;
    PORT.PBDC11 &= _PORT_CLEAR_BIT3;
    PORT.PM11 |= _PORT_SET_BIT3;
    PORT.PMC11 &= _PORT_CLEAR_BIT3;
    PORT.PIPC11 &= _PORT_CLEAR_BIT3;
    tmp_port = PORT.PDSC11;
    PORT.PPCMD11 = _WRITE_PROTECT_COMMAND;
    PORT.PDSC11 = (tmp_port | _PORT_SET_BIT3);
    PORT.PDSC11 = (uint32) ~(tmp_port | _PORT_SET_BIT3);
    PORT.PDSC11 = (tmp_port | _PORT_SET_BIT3);
    PORT.PFC11 &= _PORT_CLEAR_BIT3;
    PORT.PFCE11 &= _PORT_CLEAR_BIT3;
    PORT.PFCAE11 &= _PORT_CLEAR_BIT3;
    PORT.PIPC11 |= _PORT_SET_BIT3;
    PORT.PMC11 |= _PORT_SET_BIT3;
    /* Set CSIH2SO pin */
    PORT.PIBC11 &= _PORT_CLEAR_BIT2;
    PORT.PBDC11 &= _PORT_CLEAR_BIT2;
    PORT.PM11 |= _PORT_SET_BIT2;
    PORT.PMC11 &= _PORT_CLEAR_BIT2;
    PORT.PIPC11 &= _PORT_CLEAR_BIT2;
    tmp_port = PORT.PDSC11;
    PORT.PPCMD11 = _WRITE_PROTECT_COMMAND;
    PORT.PDSC11 = (tmp_port | _PORT_SET_BIT2);
    PORT.PDSC11 = (uint32) ~(tmp_port | _PORT_SET_BIT2);
    PORT.PDSC11 = (tmp_port | _PORT_SET_BIT2);
    PORT.PFC11 &= _PORT_CLEAR_BIT2;
    PORT.PFCE11 &= _PORT_CLEAR_BIT2;
    PORT.PFCAE11 &= _PORT_CLEAR_BIT2;
    PORT.PIPC11 |= _PORT_SET_BIT2;
    PORT.PMC11 |= _PORT_SET_BIT2; 
    /* Set CSIH2SI pin */
    PORT.PIBC11 &= _PORT_CLEAR_BIT4;
    PORT.PBDC11 &= _PORT_CLEAR_BIT4;
    PORT.PM11 |= _PORT_SET_BIT4;  
    PORT.PMC11 &= _PORT_CLEAR_BIT4;
    PORT.PFC11 &= _PORT_CLEAR_BIT4;
    PORT.PFCE11 &= _PORT_CLEAR_BIT4;
    PORT.PMC11 |= _PORT_SET_BIT4;  
    /* Set CSIH2CSS0 pin */
    PORT.PIBC9 &= _PORT_CLEAR_BIT0;
    PORT.PBDC9 &= _PORT_CLEAR_BIT0;
    PORT.PM9 |= _PORT_SET_BIT0;  
    PORT.PMC9 &= _PORT_CLEAR_BIT0;
    PORT.PFC9 &= _PORT_CLEAR_BIT0;
    PORT.PFCE9 |= _PORT_SET_BIT0;  
    PORT.PMC9 |= _PORT_SET_BIT0;  
    PORT.PM9 &= _PORT_CLEAR_BIT0;
}
/***********************************************************************************************************************
* Function Name: R_CSIH2_Start
* Description  : This function starts the CSIH2 module operation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_CSIH2_Start(void)
{
    /* Enable CSIH2 operation */
    CSIH2.CTL0 = _CSIH_OPERATION_CLOCK_PROVIDE | _CSIH_TRANSMISSION_PERMIT | _CSIH_RECEPTION_PERMIT | 
                 _CSIH_DIRECTACCESS;
    /* Clear CSIH2 interrupt request and enable operation */
    INTC2.ICCSIH2IC.BIT.RFCSIH2IC = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICCSIH2IR.BIT.RFCSIH2IR = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICCSIH2IRE.BIT.RFCSIH2IRE = _INT_REQUEST_NOT_OCCUR;
    #if CSIH_DMA_START_ENABLE==0
    INTC2.ICCSIH2IC.BIT.MKCSIH2IC = _INT_PROCESSING_ENABLED;
    INTC2.ICCSIH2IR.BIT.MKCSIH2IR = _INT_PROCESSING_ENABLED;
    #endif
    INTC2.ICCSIH2IRE.BIT.MKCSIH2IRE = _INT_PROCESSING_ENABLED;
}
/***********************************************************************************************************************
* Function Name: R_CSIH2_Stop
* Description  : This function stops the CSIH2 module operation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_CSIH2_Stop(void)
{
    /* Disable CSIH2 interrupt operation */
    INTC2.ICCSIH2IC.BIT.MKCSIH2IC = _INT_PROCESSING_DISABLED;
    INTC2.ICCSIH2IR.BIT.MKCSIH2IR = _INT_PROCESSING_DISABLED;
    INTC2.ICCSIH2IRE.BIT.MKCSIH2IRE = _INT_PROCESSING_DISABLED;
    /* Disable CSIH2 operation */
    CSIH2.CTL0 &= (uint8) ~_CSIH_RECEPTION_PERMIT;
    CSIH2.CTL0 &= (uint8) ~_CSIH_TRANSMISSION_PERMIT;
    CSIH2.CTL0 &= (uint8) ~_CSIH_OPERATION_CLOCK_PROVIDE;
    /* Synchronization processing */
    g_cg_sync_read = CSIH2.CTL0;

    /* Clear CSIH2 interrupt request */
    INTC2.ICCSIH2IC.BIT.RFCSIH2IC = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICCSIH2IR.BIT.RFCSIH2IR = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICCSIH2IRE.BIT.RFCSIH2IRE = _INT_REQUEST_NOT_OCCUR;
    /* Synchronization processing */
    g_cg_sync_read = INTC2.ICCSIH2IC.UINT16;
}
/***********************************************************************************************************************
* Function Name: R_CSIH2_Master_Receive
* Description  : This function receives CSIH2 data.
* Arguments    : rx_buf -
*                    receive buffer pointer
*                rx_num -
*                    buffer size
*                chipId -
*                    the chip_id that Receving from
* Return Value : status -
*                    MD_OK or MD_ARGERROR
***********************************************************************************************************************/
uint16 R_CSIH2_Master_Receive(uint16* rx_buf, uint16 rx_num, uint32 chip_id)
{
    uint16 status = MD_OK;
    uint32 regValue = _CSIH_RECEIVE_SETTING_INIT;
    
    if ((rx_num < 1U) | (chip_id < _CSIH_SELECT_CHIP_0) | (chip_id > _CSIH_SELECT_CHIP_3))
    {
        status = MD_ARGERROR;
    }
    else
    {
        /* Set select chip id */
        regValue &= ~(chip_id);
        CSIH2.RX0W = regValue;
        /* Set receive setting */
        gp_csih2_rx_address = rx_buf;
        g_csih2_rx_total_num = rx_num; 
        g_csih2_rx_num = 0U;
    }

    return (status);
}

/***********************************************************************************************************************
* Function Name: R_CSIH2_Master_Send
* Description  : This function sends CSIH2 data.
* Arguments    : tx_buf -
*                    send buffer pointer
*                tx_num -
*                    buffer size
*                chipId -
*                    set chip select id
* Return Value : status -
*                    MD_OK or MD_ARGERROR
***********************************************************************************************************************/
uint16 R_CSIH2_Master_Send(const uint16 * tx_buf, uint16 tx_num, uint32 chip_id)
{
    uint16 status = MD_OK;
    uint32 regValue = _CSIH_TRANSMIT_SETTING_INIT;

    if ((tx_num < 1U) | (chip_id < _CSIH_SELECT_CHIP_0) | (chip_id > _CSIH_SELECT_CHIP_SUM_2))
    {
        status = MD_ARGERROR;
    }
    else
    {
        /* Set select chip id */
        regValue &= ~(chip_id);
        /* Set transmit setting */
        gp_csih2_tx_address = (uint16 *)tx_buf; 
        g_csih2_tx_num = tx_num; 
        regValue |= *gp_csih2_tx_address;
        /* Disable CSIH2 interrupt operation */
        INTC2.ICCSIH2IC.BIT.MKCSIH2IC = _INT_PROCESSING_DISABLED;
        /* Synchronization processing */
        g_cg_sync_read = INTC2.ICCSIH2IC.UINT16;

        /* Set transmit data */
        CSIH2.TX0W = regValue;
        gp_csih2_tx_address++;
        g_csih2_tx_num--;
        /* Synchronization processing */
        g_cg_sync_read = CSIH2.CTL1;

        /* Enable CSIH2 interrupt operation */
        #if CSIH_DMA_START_ENABLE==0
        INTC2.ICCSIH2IC.BIT.MKCSIH2IC = _INT_PROCESSING_ENABLED;
        #endif
        /* Synchronization processing */
        g_cg_sync_read = INTC2.ICCSIH2IC.UINT16;
    }

    return status;
}

/***********************************************************************************************************************
* Function Name: R_CSIH3_Create
* Description  : This function initializes the CSIH3 module.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_CSIH3_Create(void)
{
    uint32 tmp_port;

    /* Disable CSIH3 operation */
    CSIH3.CTL0 = _CSIH_OPERATION_CLOCK_STOP;
    /* Disable INTCSIH3IC operation and clear request */
    INTC2.ICCSIH3IC.BIT.MKCSIH3IC = _INT_PROCESSING_DISABLED;
    INTC2.ICCSIH3IC.BIT.RFCSIH3IC = _INT_REQUEST_NOT_OCCUR;
    /* Disable INTCSIH3IR operation and clear request */
    INTC2.ICCSIH3IR.BIT.MKCSIH3IR = _INT_PROCESSING_DISABLED;
    INTC2.ICCSIH3IR.BIT.RFCSIH3IR = _INT_REQUEST_NOT_OCCUR;
    /* Disable INTCSIH3IRE operation and clear request */
    INTC2.ICCSIH3IRE.BIT.MKCSIH3IRE = _INT_PROCESSING_DISABLED;
    INTC2.ICCSIH3IRE.BIT.RFCSIH3IRE = _INT_REQUEST_NOT_OCCUR;
    /* Set CSIH3 interrupt(INTCSIH3IC) setting */
    INTC2.ICCSIH3IC.BIT.TBCSIH3IC = _INT_TABLE_VECTOR;
    INTC2.ICCSIH3IC.UINT16 &= _INT_PRIORITY_LOWEST;
    /* Set CSIH3 interrupt(INTCSIH3IR) setting */
    INTC2.ICCSIH3IR.BIT.TBCSIH3IR = _INT_TABLE_VECTOR;
    INTC2.ICCSIH3IR.UINT16 &= _INT_PRIORITY_LOWEST;
    /* Set CSIH3 interrupt(INTCSIH3IRE) setting */
    INTC2.ICCSIH3IRE.BIT.TBCSIH3IRE = _INT_TABLE_VECTOR;
    INTC2.ICCSIH3IRE.UINT16 &= _INT_PRIORITY_LOWEST;
    /* Set CSIH3 control setting */
    CSIH3.CTL1 = _CSIH_CLOCK_INVERTING_LOW | _CSIH_INTERRUPT_TIMING_TRANSFERRED | _CSIH_CHIPSELECT0_ACTIVE_LOW | _CSIH_DATA_CONSISTENCY_CHECK_DISABLE | 
                 _CSIH_CHIPSELECT_SIGNAL_HOLD_INACTIVE | _CSIH_NO_DELAY | _CSIH_HANDSHAKE_DISABLE | 
                 _CSIH_SLAVE_SELECT_ENABLE;
    CSIH3.CTL2 = _CSIH3_SELECT_BASIC_CLOCK;
    CSIH3.BRS0 = _CSIH3_BAUD_RATE_0;
    CSIH3.BRS1 = _CSIH3_BAUD_RATE_1;
    CSIH3.BRS2 = _CSIH3_BAUD_RATE_2;
    CSIH3.BRS3 = _CSIH3_BAUD_RATE_3;
    /* Set CSIH3 configuration setting */
    CSIH3.CFG3 = _CSIH_USED_BAUDRATE_0 | _CSIH_PARITY_NO | _CSIH_DATA_LENGTH_8 | _CSIH_DATA_DIRECTION_MSB | 
                 _CSIHn_MASTER_PHASE_SELECTION_TYPE2 | _CSIH_IDLE_INSERTED_NOT_ALWAYS | _CSIH_IDLE_TIME_0 | 
                 _CSIH_HOLD_TIME_0 | _CSIH_INTER_DATA_DELAY_TIME_0 | _CSIH_SETUP_TIME_0;
    /* Synchronization processing */
    g_cg_sync_read = CSIH3.CTL1;

    /* Set CSIH3SC pin */
    PORT.PIBC11 &= _PORT_CLEAR_BIT7;
    PORT.PBDC11 &= _PORT_CLEAR_BIT7;
    PORT.PM11 |= _PORT_SET_BIT7;
    PORT.PMC11 &= _PORT_CLEAR_BIT7;
    PORT.PIPC11 &= _PORT_CLEAR_BIT7;
    tmp_port = PORT.PDSC11;
    PORT.PPCMD11 = _WRITE_PROTECT_COMMAND;
    PORT.PDSC11 = (tmp_port | _PORT_SET_BIT7);
    PORT.PDSC11 = (uint32) ~(tmp_port | _PORT_SET_BIT7);
    PORT.PDSC11 = (tmp_port | _PORT_SET_BIT7);
    PORT.PFC11 &= _PORT_CLEAR_BIT7;
    PORT.PFCE11 |= _PORT_SET_BIT7;
    PORT.PIPC11 |= _PORT_SET_BIT7;
    PORT.PMC11 |= _PORT_SET_BIT7;
    /* Set CSIH3SO pin */
    PORT.PIBC11 &= _PORT_CLEAR_BIT6;
    PORT.PBDC11 &= _PORT_CLEAR_BIT6;
    PORT.PM11 |= _PORT_SET_BIT6;
    PORT.PMC11 &= _PORT_CLEAR_BIT6;
    PORT.PIPC11 &= _PORT_CLEAR_BIT6;
    tmp_port = PORT.PDSC11;
    PORT.PPCMD11 = _WRITE_PROTECT_COMMAND;
    PORT.PDSC11 = (tmp_port | _PORT_SET_BIT6);
    PORT.PDSC11 = (uint32) ~(tmp_port | _PORT_SET_BIT6);
    PORT.PDSC11 = (tmp_port | _PORT_SET_BIT6);
    PORT.PFC11 &= _PORT_CLEAR_BIT6;
    PORT.PFCE11 |= _PORT_SET_BIT6;
    PORT.PFCAE11 &= _PORT_CLEAR_BIT6;
    PORT.PIPC11 |= _PORT_SET_BIT6;
    PORT.PMC11 |= _PORT_SET_BIT6;  
    /* Set CSIH3SI pin */
    PORT.PIBC11 &= _PORT_CLEAR_BIT5;
    PORT.PBDC11 &= _PORT_CLEAR_BIT5;
    PORT.PM11 |= _PORT_SET_BIT5;  
    PORT.PMC11 &= _PORT_CLEAR_BIT5;
    PORT.PFC11 &= _PORT_CLEAR_BIT5;
    PORT.PFCE11 |= _PORT_SET_BIT5;  
    PORT.PFCAE11 &= _PORT_CLEAR_BIT5;
    PORT.PMC11 |= _PORT_SET_BIT5;
    /* Set CSIH3CSS3 pin */
    PORT.PIBC8 &= _PORT_CLEAR_BIT10;
    PORT.PBDC8 &= _PORT_CLEAR_BIT10;
    PORT.PM8 |= _PORT_SET_BIT10;  
    PORT.PMC8 &= _PORT_CLEAR_BIT10;
    PORT.PFC8 &= _PORT_CLEAR_BIT10;
    PORT.PMC8 |= _PORT_SET_BIT10;  /*加上这句由硬件控制引脚*/
    PORT.PM8 &= _PORT_CLEAR_BIT10;
}
/***********************************************************************************************************************
* Function Name: R_CSIH3_Start
* Description  : This function starts the CSIH3 module operation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_CSIH3_Start(void)
{
    /* Enable CSIH3 operation */
    CSIH3.CTL0 = _CSIH_OPERATION_CLOCK_PROVIDE | _CSIH_TRANSMISSION_PERMIT | _CSIH_RECEPTION_PERMIT | 
                 _CSIH_DIRECTACCESS;
    /* Clear CSIH3 interrupt request and enable operation */
    INTC2.ICCSIH3IC.BIT.RFCSIH3IC = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICCSIH3IR.BIT.RFCSIH3IR = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICCSIH3IRE.BIT.RFCSIH3IRE = _INT_REQUEST_NOT_OCCUR;
    #if CSIH_DMA_START_ENABLE==0
    INTC2.ICCSIH3IC.BIT.MKCSIH3IC = _INT_PROCESSING_ENABLED;
    INTC2.ICCSIH3IR.BIT.MKCSIH3IR = _INT_PROCESSING_ENABLED;
    #endif
    INTC2.ICCSIH3IRE.BIT.MKCSIH3IRE = _INT_PROCESSING_ENABLED;
}
/***********************************************************************************************************************
* Function Name: R_CSIH3_Stop
* Description  : This function stops the CSIH3 module operation.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_CSIH3_Stop(void)
{
    /* Disable CSIH3 interrupt operation */
    INTC2.ICCSIH3IC.BIT.MKCSIH3IC = _INT_PROCESSING_DISABLED;
    INTC2.ICCSIH3IR.BIT.MKCSIH3IR = _INT_PROCESSING_DISABLED;
    INTC2.ICCSIH3IRE.BIT.MKCSIH3IRE = _INT_PROCESSING_DISABLED;
    /* Disable CSIH3 operation */
    CSIH3.CTL0 &= (uint8) ~_CSIH_RECEPTION_PERMIT;
    CSIH3.CTL0 &= (uint8) ~_CSIH_TRANSMISSION_PERMIT;
    CSIH3.CTL0 &= (uint8) ~_CSIH_OPERATION_CLOCK_PROVIDE;
    /* Synchronization processing */
    g_cg_sync_read = CSIH3.CTL0;
    /* Clear CSIH3 interrupt request */
    INTC2.ICCSIH3IC.BIT.RFCSIH3IC = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICCSIH3IR.BIT.RFCSIH3IR = _INT_REQUEST_NOT_OCCUR;
    INTC2.ICCSIH3IRE.BIT.RFCSIH3IRE = _INT_REQUEST_NOT_OCCUR;
    /* Synchronization processing */
    g_cg_sync_read = INTC2.ICCSIH3IC.UINT16;
}
/***********************************************************************************************************************
* Function Name: R_CSIH3_Master_Receive
* Description  : This function receives CSIH3 data.
* Arguments    : rx_buf -
*                    receive buffer pointer
*                rx_num -
*                    buffer size
*                chipId -
*                    the chip_id that Receving from
* Return Value : status -
*                    MD_OK or MD_ARGERROR
***********************************************************************************************************************/
uint16 R_CSIH3_Master_Receive(uint16* rx_buf, uint16 rx_num, uint32 chip_id)
{
    uint16 status = MD_OK;
    uint32 regValue = _CSIH_RECEIVE_SETTING_INIT;
    
    if ((rx_num < 1U) | (chip_id < _CSIH_SELECT_CHIP_0) | (chip_id > _CSIH_SELECT_CHIP_3))
    {
        status = MD_ARGERROR;
    }
    else
    {
        /* Set select chip id */
        regValue &= ~(chip_id);
        CSIH3.RX0W = regValue; 
        /* Set receive setting */
        gp_csih3_rx_address = rx_buf;
        g_csih3_rx_total_num = rx_num; 
        g_csih3_rx_num = 0U;
    }

    return (status);
}
/***********************************************************************************************************************
* Function Name: R_CSIH3_Master_Send
* Description  : This function sends CSIH3 data.
* Arguments    : tx_buf -
*                    send buffer pointer
*                tx_num -
*                    buffer size
*                chipId -
*                    set chip select id
* Return Value : status -
*                    MD_OK or MD_ARGERROR
***********************************************************************************************************************/
uint16 R_CSIH3_Master_Send(const uint16 * tx_buf, uint16 tx_num, uint32 chip_id)
{
    uint16 status = MD_OK;
    uint32 regValue = _CSIH_TRANSMIT_SETTING_INIT;

    if ((tx_num < 1U) | (chip_id < _CSIH_SELECT_CHIP_0) | (chip_id > _CSIH_SELECT_CHIP_SUM_2))
    {
        status = MD_ARGERROR;
    }
    else
    {
        /* Set select chip id */
        regValue &= ~(chip_id);
        /* Set transmit setting */
        gp_csih3_tx_address = (uint16 *)tx_buf; 
        g_csih3_tx_num = tx_num; 
        regValue |= *gp_csih3_tx_address;
        /* Disable CSIH3 interrupt operation */
        INTC2.ICCSIH3IC.BIT.MKCSIH3IC = _INT_PROCESSING_DISABLED;
        /* Synchronization processing */
        g_cg_sync_read = INTC2.ICCSIH3IC.UINT16;
        /* Set transmit data */
        CSIH3.TX0W = regValue;
        gp_csih3_tx_address++;
        g_csih3_tx_num--;
        /* Synchronization processing */
        g_cg_sync_read = CSIH3.CTL1;
        /* Enable CSIH3 interrupt operation */
        #if CSIH_DMA_START_ENABLE==0
        INTC2.ICCSIH3IC.BIT.MKCSIH3IC = _INT_PROCESSING_ENABLED;
        #endif
        /* Synchronization processing */
        g_cg_sync_read = INTC2.ICCSIH3IC.UINT16;
    }

    return status;
}

/* Start user code for adding. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */
