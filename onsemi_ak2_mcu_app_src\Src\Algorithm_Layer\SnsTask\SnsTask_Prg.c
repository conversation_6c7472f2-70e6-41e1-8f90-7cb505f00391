/******************************************************************************
 * @file       SnsTask_Prg.c
 * @brief      
 * @date       2025-03-04 14:16:39
 * <AUTHOR>
 * @copyright  Longhorn Automotive Electronics Technology (Xiamen) Co., Ltd.
 *****************************************************************************/


/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "SnsTask_Type.h"
#include "SnsTask_Int.h"
#include "SnsRawData_Int.h"
#include "Elmos_524_17_SnsCtrl.h"
#include "PowerSingalManage.h"
#include "CAN_AppSignalManage.h"
#include "PAS_MAP_StateHandle.h"
#include "SDW_Int.h"
#include "SnsPPCalculate_Int.h"
#include "TAU_COM.h"
#include "TimerManage.h"
#include "debug.h"
#include "MapEchoFilterAndSigGroup_int.h"
#include "PSL_EchoFilterAndSigGroup_int.h"
#include "MapCoorCalculate_Type.h"
#if VHE_POINT_CLOUD_SIMULATE
#include "MapFromAllPoints.h"
#endif


/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/



/******************************************************************************
 * @Const Definitions
 *****************************************************************************/



/******************************************************************************
 * @Private Variable Definitions
 *****************************************************************************/
PDCErrorFlagType GstrPDCErrorFlag;
static eSnsTaskStepType GenuSnsTaskStep = SNS_TASK_IDLE;

uint32 Gu32SnsTaskTime[4];



/******************************************************************************
 * @Global Variable Definitions
 *****************************************************************************/
eSnsSysWorkStsType GeSnsSysWorkSts = SNS_SYS_STANDBY;




/******************************************************************************
 * @Private Function Declaration
 *****************************************************************************/



/******************************************************************************
 * @Function Definitions
 *****************************************************************************/


/******************************************************************************
 * @brief      探头系统工作状态转换
 * <AUTHOR>
 * @date       2025-03-04 14:24:15
 * @note       
 *****************************************************************************/
static void SnsSysWorkStsHandle(void)
{
    static uint8 Lu8SnsCheckCnt = 0;
    uint8 Lu8PDCFunctionDGFlag = 0;
	Signal_VoltageStatusType LenuWorkVolSts = NORMAL_VOLTAGE;
    Car_GearType LpCar_Gear;
    

    if(Lu8SnsCheckCnt == 0)
    {   
        Lu8SnsCheckCnt = 3;
        UpdateSnsOdoCoor();/* 确保Odo在高速时可以被清零 */
        ReadCAN_AppSignal_Car_Speed(&Gu16CarSpdForSnsUse);
        ReadCAN_AppSignal_Car_Gear(&LpCar_Gear);
        ReadCAN_AppSignal_Car_StrAngle(&Gs16CarStrAngleForSnsUse);
        
        ReadPwrManage_GroupStatus(PM_APP,&LenuWorkVolSts);
        Lu8PDCFunctionDGFlag = Read_PASFunctionDGFlag();
        GstrPDCErrorFlag = PAS_GetErrorFlagForTask();
        /* 功能降级或电压不在正常状态就按系统故障处理 */
        if((Lu8PDCFunctionDGFlag)||(LenuWorkVolSts != NORMAL_VOLTAGE))
        {
            GstrPDCErrorFlag.u8Front_and_RearSystem_FailureFlag = 1;
        }
        switch(GeSnsSysWorkSts)
        {
            case SNS_SYS_STANDBY:
            {
                if(GstrPDCErrorFlag.u8Front_and_RearSystem_FailureFlag)
                {
                    GeSnsSysWorkSts = SNS_SYS_ALL_FAILURE;
                    
                    SNS_TASK_DATA_PRINT("Standby To Failure,PowerSts:%d,PDCFunctionDGFlag:%d,CarSpd:%d,SysWorkSts:%d\r\n",LenuWorkVolSts,\
                        Lu8PDCFunctionDGFlag,Gu16CarSpdForSnsUse,GeSnsSysWorkSts);
                }
                else
                {
                    if(Gu16CarSpdForSnsUse < SNS_SYS_FRONT_REAR_SIDE_ACTIVE_SPD_START)
                    {
                        GeSnsSysWorkSts = SNS_SYS_FRONT_REAR_SIDE_ACTIVE;
                        SNS_TASK_DATA_PRINT("Standby To Front_Rear_Side Active,PowerSts:%d,PDCFunctionDGFlag:%d,CarSpd:%d,SysWorkSts:%d\r\n",LenuWorkVolSts,\
                            Lu8PDCFunctionDGFlag,Gu16CarSpdForSnsUse,GeSnsSysWorkSts);
                    }
                    else if(Gu16CarSpdForSnsUse < SNS_SYS_ONLY_SIDE_ACTIVE_SPD_START)
                    {
                        GeSnsSysWorkSts = SNS_SYS_ONLY_SIDE_ACTIVE;
                        SNS_TASK_DATA_PRINT("Standby To Only_Side Active,PowerSts:%d,PDCFunctionDGFlag:%d,CarSpd:%d,SysWorkSts:%d\r\n",LenuWorkVolSts,\
                            Lu8PDCFunctionDGFlag,Gu16CarSpdForSnsUse,GeSnsSysWorkSts);
                    }
                    else
                    {
                        GeSnsSysWorkSts = SNS_SYS_STANDBY;
                    }
                }
                break;
            }

            case SNS_SYS_FRONT_REAR_SIDE_ACTIVE:
            {
                if(GstrPDCErrorFlag.u8Front_and_RearSystem_FailureFlag)
                {
                    GeSnsSysWorkSts = SNS_SYS_ALL_FAILURE;
                    SNS_TASK_DATA_PRINT("Front_Rear_Side Active To Failure,PowerSts:%d,PDCFunctionDGFlag:%d,CarSpd:%d,SysWorkSts:%d\r\n",LenuWorkVolSts,\
                        Lu8PDCFunctionDGFlag,Gu16CarSpdForSnsUse,GeSnsSysWorkSts);
                }
                else
                {
                    if(Gu16CarSpdForSnsUse > SNS_SYS_ONLY_SIDE_ACTIVE_SPD_END)
                    {
                        GeSnsSysWorkSts = SNS_SYS_STANDBY;
                        SNS_TASK_DATA_PRINT("Front_Rear_Side Active To Standby,PowerSts:%d,PDCFunctionDGFlag:%d,CarSpd:%d,SysWorkSts:%d\r\n",LenuWorkVolSts,\
                            Lu8PDCFunctionDGFlag,Gu16CarSpdForSnsUse,GeSnsSysWorkSts);
                    }
                    else if(Gu16CarSpdForSnsUse > SNS_SYS_FRONT_REAR_SIDE_ACTIVE_SPD_END)
                    {
                        GeSnsSysWorkSts = SNS_SYS_ONLY_SIDE_ACTIVE;
                        SNS_TASK_DATA_PRINT("Front_Rear_Side Active To Only Side Active,PowerSts:%d,PDCFunctionDGFlag:%d,CarSpd:%d,SysWorkSts:%d\r\n",LenuWorkVolSts,\
                            Lu8PDCFunctionDGFlag,Gu16CarSpdForSnsUse,GeSnsSysWorkSts);
                    }
                    else
                    {
                        GeSnsSysWorkSts = SNS_SYS_FRONT_REAR_SIDE_ACTIVE;
                    }
                }
                break;
            }

            case SNS_SYS_ONLY_SIDE_ACTIVE:
            {
                if(GstrPDCErrorFlag.u8Front_and_RearSystem_FailureFlag)
                {
                    GeSnsSysWorkSts = SNS_SYS_ALL_FAILURE;
                    SNS_TASK_DATA_PRINT("Only_Side Active To Failure,PowerSts:%d,PDCFunctionDGFlag:%d,CarSpd:%d,SysWorkSts:%d\r\n",LenuWorkVolSts,\
                        Lu8PDCFunctionDGFlag,Gu16CarSpdForSnsUse,GeSnsSysWorkSts);
                }
                else
                {
                    if(Gu16CarSpdForSnsUse > SNS_SYS_ONLY_SIDE_ACTIVE_SPD_END)
                    {
                        GeSnsSysWorkSts = SNS_SYS_STANDBY;
                        SNS_TASK_DATA_PRINT("Only_Side Active To Satadby,PowerSts:%d,PDCFunctionDGFlag:%d,CarSpd:%d,SysWorkSts:%d\r\n",LenuWorkVolSts,\
                            Lu8PDCFunctionDGFlag,Gu16CarSpdForSnsUse,GeSnsSysWorkSts);
                    }
                    else if(Gu16CarSpdForSnsUse < SNS_SYS_FRONT_REAR_SIDE_ACTIVE_SPD_START)
                    {
                        GeSnsSysWorkSts = SNS_SYS_FRONT_REAR_SIDE_ACTIVE;
                        SNS_TASK_DATA_PRINT("Only_Side Active To Front_Rear_Side Active,PowerSts:%d,PDCFunctionDGFlag:%d,CarSpd:%d,SysWorkSts:%d\r\n",LenuWorkVolSts,\
                            Lu8PDCFunctionDGFlag,Gu16CarSpdForSnsUse,GeSnsSysWorkSts);
                    }
                    else
                    {
                        GeSnsSysWorkSts = SNS_SYS_ONLY_SIDE_ACTIVE;
                    }
                }
                break;
            }

            case SNS_SYS_ALL_FAILURE:
            {
                if(!GstrPDCErrorFlag.u8Front_and_RearSystem_FailureFlag)
                {
                    GeSnsSysWorkSts = SNS_SYS_STANDBY;
                    SNS_TASK_DATA_PRINT("Failure To Standby,PowerSts:%d,PDCFunctionDGFlag:%d,CarSpd:%d,SysWorkSts:%d\r\n",LenuWorkVolSts,\
                        Lu8PDCFunctionDGFlag,Gu16CarSpdForSnsUse,GeSnsSysWorkSts);
                }
                break;
            }

            default:
            {
                GeSnsSysWorkSts = SNS_SYS_STANDBY;
                break;
            }
        }
#if 0
        SNS_TASK_DATA_PRINT("SnsSysWorkSts First Handle,PowerSts:%d,PDCFunctionDGFlag:%d,CarSpd:%d,SysWorkSts:%d,Time:%ld\r\n",LenuWorkVolSts,\
            Lu8PDCFunctionDGFlag,Gu16CarSpdForSnsUse,GeSnsSysWorkSts,GetSystemTimeCnt_Us());
#endif  
    }
    else
    {
        Lu8SnsCheckCnt--;
    }
}


/******************************************************************************
 * @brief      探头数据获取后的任务调度，1ms调度周期
 * <AUTHOR>
 * @date       2025-03-04 14:03:26
 * @note       
 *****************************************************************************/
void SnsTask_1ms_StepRun(void)
{
    PDCSnsGroupType LenuSnsGroup;
    PDCSnsChannelType LenuSnsCh;
    PDCSnsRawDataType *LpStrPDCSnsRawData;
    uint8 Lu8SeqFinishFlg = 0;
    static uint8 Lu8FrontInitFlag = 0;
    static uint8 Lu8RearInitFlag = 0;
    static uint8 Lu8Front_PDC_SnsInitFlag = 0;
    static uint8 Lu8Rear_PDC_SnsInitFlag = 0;
    
    switch(GenuSnsTaskStep)
    {
        case SNS_TASK_IDLE:
        {
            SnsSysWorkStsHandle();
            if((GeSnsSysWorkSts == SNS_SYS_STANDBY)||(GeSnsSysWorkSts == SNS_SYS_ALL_FAILURE))
            {
                /** @note 进入故障状态或standby需要重新初始化对应的系统变量 */
                if(!Lu8FrontInitFlag)
                {
                    Lu8FrontInitFlag = 1;
                    Lu8Front_PDC_SnsInitFlag = 1;
                    SnsSigGroupDataCacheInit(PDC_SNS_GROUP_FRONT);
                    MapSigGroupDataCacheInit(PDC_SNS_GROUP_FRONT);
                    SnsCarCovrDataInit();
                    PSLSnsSigGroupDataCacheInit(PDC_SNS_GROUP_FRONT);
                    MapSnsRealTimeDisInit(PDC_SNS_GROUP_FRONT);
                    PDCSnsCarMovStsInit(PDC_SNS_GROUP_FRONT);
                    SnsSigGroupFollowDataInit(PDC_SNS_GROUP_FRONT);
                    PDCSnsDE_CE_DataInit(PDC_SNS_GROUP_FRONT);
                    SnsObjCoorDataInit(PDC_SNS_GROUP_FRONT);
                    PP_Zone_DataInit(PDC_SNS_GROUP_FRONT);
                    ObjMapInit();
                    ObjMapToCANInit();
                }
                if(!Lu8RearInitFlag)
                {
                    Lu8RearInitFlag = 1;
                    Lu8Rear_PDC_SnsInitFlag = 1;
                    SnsSigGroupDataCacheInit(PDC_SNS_GROUP_REAR);
                    PSLSnsSigGroupDataCacheInit(PDC_SNS_GROUP_REAR);
                    MapSigGroupDataCacheInit(PDC_SNS_GROUP_REAR);
                    SnsCarCovrDataInit();
                    MapSnsRealTimeDisInit(PDC_SNS_GROUP_REAR);
                    PDCSnsCarMovStsInit(PDC_SNS_GROUP_REAR);
                    SnsSigGroupFollowDataInit(PDC_SNS_GROUP_REAR);
                    PDCSnsDE_CE_DataInit(PDC_SNS_GROUP_REAR);
                    SnsObjCoorDataInit(PDC_SNS_GROUP_REAR);
                    PP_Zone_DataInit(PDC_SNS_GROUP_REAR);
                    ObjMapInit();
                    ObjMapToCANInit();
                }
            }
            else if(GeSnsSysWorkSts == SNS_SYS_ONLY_SIDE_ACTIVE)
            {
                if(!Lu8Front_PDC_SnsInitFlag)
                {
                    Lu8Front_PDC_SnsInitFlag = 1;
                    SnsSigGroupDataCache_PDC_Sns_Init(PDC_SNS_GROUP_FRONT);
                    MapSigGroupDataCache_PDC_Sns_Init(PDC_SNS_GROUP_FRONT);
                    PSLSnsSigGroupDataCacheInit(PDC_SNS_GROUP_FRONT);
                    PDCSnsCarMovSts_PDC_Sns_Init(PDC_SNS_GROUP_FRONT);
                    SnsSigGroupFollowDataPDC_Sns_Init(PDC_SNS_GROUP_FRONT);
                    PDCSnsDE_CE_DataPDC_Sns_Init(PDC_SNS_GROUP_FRONT);
#if 0
                    SnsObjCoorDataInit(PDC_SNS_GROUP_FRONT);
                    ObjMapInit();
                    ObjMapToCANInit();
#endif
                }
                if(!Lu8Rear_PDC_SnsInitFlag)
                {
                    Lu8Rear_PDC_SnsInitFlag = 1;
                    SnsSigGroupDataCache_PDC_Sns_Init(PDC_SNS_GROUP_REAR);
                    MapSigGroupDataCache_PDC_Sns_Init(PDC_SNS_GROUP_REAR);
                    PSLSnsSigGroupDataCacheInit(PDC_SNS_GROUP_REAR);
                    PDCSnsCarMovSts_PDC_Sns_Init(PDC_SNS_GROUP_REAR);
                    SnsSigGroupFollowDataPDC_Sns_Init(PDC_SNS_GROUP_REAR);
                    PDCSnsDE_CE_DataPDC_Sns_Init(PDC_SNS_GROUP_REAR);
#if 0
                    SnsObjCoorDataInit(PDC_SNS_GROUP_REAR);
                    ObjMapInit();
                    ObjMapToCANInit();
#endif
                }
            }
            else
            {
                if(GstrPDCErrorFlag.u8FrontSystem_FailureFlag)
                {
                    if(!Lu8FrontInitFlag)
                    {
                        Lu8FrontInitFlag = 1;
                        Lu8Front_PDC_SnsInitFlag = 1;
                        SnsSigGroupDataCacheInit(PDC_SNS_GROUP_FRONT);
                        PSLSnsSigGroupDataCacheInit(PDC_SNS_GROUP_FRONT);
                        MapSigGroupDataCacheInit(PDC_SNS_GROUP_FRONT);
                        SnsCarCovrDataInit();
                        MapSnsRealTimeDisInit(PDC_SNS_GROUP_FRONT);
                        PDCSnsCarMovStsInit(PDC_SNS_GROUP_FRONT);
                        SnsSigGroupFollowDataInit(PDC_SNS_GROUP_FRONT);
                        PDCSnsDE_CE_DataInit(PDC_SNS_GROUP_FRONT);
                        SnsObjCoorDataInit(PDC_SNS_GROUP_FRONT);
                        PP_Zone_DataInit(PDC_SNS_GROUP_FRONT);
                        ObjMapInit();
                        ObjMapToCANInit();
                    }
                }
                else
                {
                    Lu8FrontInitFlag = 0;
                    Lu8Front_PDC_SnsInitFlag = 0;
                }
                
                if(GstrPDCErrorFlag.u8RearSystem_FailureFlag)
                {
                    if(!Lu8RearInitFlag)
                    {
                        Lu8RearInitFlag = 1;
                        Lu8Rear_PDC_SnsInitFlag = 1;
                        SnsSigGroupDataCacheInit(PDC_SNS_GROUP_REAR);
                        PSLSnsSigGroupDataCacheInit(PDC_SNS_GROUP_REAR);
                        MapSigGroupDataCacheInit(PDC_SNS_GROUP_REAR);
                        SnsCarCovrDataInit();
                        MapSnsRealTimeDisInit(PDC_SNS_GROUP_REAR);
                        PDCSnsCarMovStsInit(PDC_SNS_GROUP_REAR);
                        SnsSigGroupFollowDataInit(PDC_SNS_GROUP_REAR);
                        PDCSnsDE_CE_DataInit(PDC_SNS_GROUP_REAR);
                        SnsObjCoorDataInit(PDC_SNS_GROUP_REAR);
                        PP_Zone_DataInit(PDC_SNS_GROUP_REAR);
                        ObjMapInit();
                        ObjMapToCANInit();
                    }
                }
                else
                {
                    Lu8RearInitFlag = 0;
                    Lu8Rear_PDC_SnsInitFlag = 0;
                }
            }
            Lu8SeqFinishFlg = Elmos17SnsCtrl_GetSeqFinishFlg();
            if(Lu8SeqFinishFlg)
            {
                Elmos17SnsCtrl_ClearSeqFinishFlg();
                GenuSnsTaskStep = SNS_TASK_RAW_DATA_GET;
            }
            break;
        }

        case SNS_TASK_RAW_DATA_GET:
        {
#if 0
            Gu32SnsTaskTime[0] = GetSystemTimeCnt_Us();
#endif
            PDCSnsRawDataUpdate();
#if 0
            Gu32SnsTaskTime[1] = GetSystemTimeCnt_Us();
            Gu32SnsTaskTime[1] = Gu32SnsTaskTime[1] - Gu32SnsTaskTime[0];
            SNS_TASK_DATA_PRINT("Raw Data Time:%ld,Cycle Time:%d\r\n",Gu32SnsTaskTime[1],Gu32SnsTaskTime[0]-Gu32SnsTaskTime[2]);
            Gu32SnsTaskTime[2] = Gu32SnsTaskTime[0];
#endif
            
            GenuSnsTaskStep = SNS_TASK_DIS_FILTER_FOLLOW;
            break;
        }

        case SNS_TASK_DIS_FILTER_FOLLOW:
        {
#if 0
            Gu32SnsTaskTime[0] = READTAUDCnt_1600ns();
            Gu32SnsTaskTime[2] = GetSystemTimeCnt_Ms();
#endif
            if((GeSnsSysWorkSts == SNS_SYS_FRONT_REAR_SIDE_ACTIVE)||(GeSnsSysWorkSts == SNS_SYS_ONLY_SIDE_ACTIVE))
            {
                /** @note 更新探头模块使用的Odo坐标 */
                UpdateSnsOdoCoor();
                if(!GstrPDCErrorFlag.u8FrontSystem_FailureFlag)
                {
                    LenuSnsGroup = PDC_SNS_GROUP_FRONT;
                    LpStrPDCSnsRawData = &GstrPDCSnsRawData[LenuSnsGroup];
                    for(LenuSnsCh = PDC_SNS_CH_FLS_RLS;LenuSnsCh < PDC_SNS_CH_NUM;LenuSnsCh++)
                    {
#if 0
                        if((GeSnsSysWorkSts == SNS_SYS_ONLY_SIDE_ACTIVE)&&(LenuSnsCh > PDC_SNS_CH_FLS_RLS)&&(LenuSnsCh < PDC_SNS_CH_FRS_RRS))
                        {
                            continue;
                        }
#endif
                        if(LpStrPDCSnsRawData->u8UpdateFlg[LenuSnsCh])
                        {
                            JudgeCarMovSts(LenuSnsGroup,LenuSnsCh);
                            SnsEchoFilterAndSigGroupDataGet(LenuSnsGroup,LenuSnsCh);
                            PSLSnsEchoFilterAndSigGroupDataGet(LenuSnsGroup,LenuSnsCh);
                            MapEchoFilterAndSigGroupDataGet(LenuSnsGroup,LenuSnsCh);
                            SnsSigGroupDisFollowAndOutputHandle(LenuSnsGroup,LenuSnsCh);
                            ObjSnsCoorCalAndFollow(LenuSnsGroup,LenuSnsCh);
                            SnsCalObjDistance(LenuSnsGroup,LenuSnsCh);
                            if((LenuSnsCh == 0x00)||(LenuSnsCh == 0x05))
                            {
                                SDW_GetOriginalData(LenuSnsGroup,LenuSnsCh);
                            }
                        }
                    }
                }
                
                if(!GstrPDCErrorFlag.u8RearSystem_FailureFlag)
                {
                    LenuSnsGroup = PDC_SNS_GROUP_REAR;
                    LpStrPDCSnsRawData = &GstrPDCSnsRawData[LenuSnsGroup];
                    for(LenuSnsCh = PDC_SNS_CH_FLS_RLS;LenuSnsCh < PDC_SNS_CH_NUM;LenuSnsCh++)
                    {
#if 0
                        if((GeSnsSysWorkSts == SNS_SYS_ONLY_SIDE_ACTIVE)&&(LenuSnsCh > PDC_SNS_CH_FLS_RLS)&&(LenuSnsCh < PDC_SNS_CH_FRS_RRS))
                        {
                            continue;
                        }
#endif
                        if(LpStrPDCSnsRawData->u8UpdateFlg[LenuSnsCh])
                        {
                            JudgeCarMovSts(LenuSnsGroup,LenuSnsCh);
                            SnsEchoFilterAndSigGroupDataGet(LenuSnsGroup,LenuSnsCh);
                            MapEchoFilterAndSigGroupDataGet(LenuSnsGroup,LenuSnsCh);
                            PSLSnsEchoFilterAndSigGroupDataGet(LenuSnsGroup,LenuSnsCh);
                            SnsSigGroupDisFollowAndOutputHandle(LenuSnsGroup,LenuSnsCh);
                            ObjSnsCoorCalAndFollow(LenuSnsGroup,LenuSnsCh);
                            SnsCalObjDistance(LenuSnsGroup,LenuSnsCh);
                            if((LenuSnsCh == 0x00)||(LenuSnsCh == 0x05))
                            {
                                SDW_GetOriginalData(LenuSnsGroup,LenuSnsCh);
                            }
                        }
                    }
                }
            }            
#if 0
            Gu32SnsTaskTime[1] = READTAUDCnt_1600ns();
            if(Gu32SnsTaskTime[1] > Gu32SnsTaskTime[0])
            {
                Gu32SnsTaskTime[1] = Gu32SnsTaskTime[1] - Gu32SnsTaskTime[0];
            }
            else
            {
                Gu32SnsTaskTime[1] = Gu32SnsTaskTime[1] + (65535 - Gu32SnsTaskTime[0]);
            }
            SNS_TASK_DEBUG_PRINT("Filter Sns Coor Time:%ld us ,Cycle Time:%d ms\r\n",Gu32SnsTaskTime[1],Gu32SnsTaskTime[2] - Gu32SnsTaskTime[3]);
            Gu32SnsTaskTime[3] = Gu32SnsTaskTime[2];
#endif

            
            GenuSnsTaskStep = SNS_TASK_F_R_MAP_BUILD;
            break;
        }
        

        case SNS_TASK_F_R_MAP_BUILD:
        {
#if 0
            Gu32SnsTaskTime[0] = READTAUDCnt_1600ns();
            Gu32SnsTaskTime[2] = GetSystemTimeCnt_Ms();

#endif
            if((GeSnsSysWorkSts == SNS_SYS_FRONT_REAR_SIDE_ACTIVE)||(GeSnsSysWorkSts == SNS_SYS_ONLY_SIDE_ACTIVE))
            {
                /** @note 更新探头模块使用的Odo坐标 */
                UpdateSnsOdoCoor();
                TransMapOdoCoorToCarCoor();

#if VHE_POINT_CLOUD_SIMULATE
                VheMapFusion();
#endif

                if(!GstrPDCErrorFlag.u8FrontSystem_FailureFlag)
                {
                    LenuSnsGroup = PDC_SNS_GROUP_FRONT;
                    LpStrPDCSnsRawData = &GstrPDCSnsRawData[LenuSnsGroup];
                    for(LenuSnsCh = PDC_SNS_CH_FLS_RLS;LenuSnsCh < PDC_SNS_CH_NUM;LenuSnsCh++)
                    {
                        if(LpStrPDCSnsRawData->u8UpdateFlg[LenuSnsCh])
                        {
                            LpStrPDCSnsRawData->u8UpdateFlg[LenuSnsCh] = 0;
                        }
                    }
                    ObjMapBuildAndUpdate(LenuSnsGroup);
                }
                
                if(!GstrPDCErrorFlag.u8RearSystem_FailureFlag)
                {
                    LenuSnsGroup = PDC_SNS_GROUP_REAR;
                    LpStrPDCSnsRawData = &GstrPDCSnsRawData[LenuSnsGroup];
                    for(LenuSnsCh = PDC_SNS_CH_FLS_RLS;LenuSnsCh < PDC_SNS_CH_NUM;LenuSnsCh++)
                    {
                        if(LpStrPDCSnsRawData->u8UpdateFlg[LenuSnsCh])
                        {
                            LpStrPDCSnsRawData->u8UpdateFlg[LenuSnsCh] = 0;
                        }
                    }
                    ObjMapBuildAndUpdate(LenuSnsGroup);
                }
                UpdateMapInforAndDele();
                SideMapFusionHandle();
                ObjMapToCAN(); 
                ParkingGuidenceDataUpdate();
            }
#if 0
            Gu32SnsTaskTime[1] = READTAUDCnt_1600ns();
            if(Gu32SnsTaskTime[1] > Gu32SnsTaskTime[0])
            {
                Gu32SnsTaskTime[1] = Gu32SnsTaskTime[1] - Gu32SnsTaskTime[0];
            }
            else
            {
                Gu32SnsTaskTime[1] = Gu32SnsTaskTime[1] + (65535 - Gu32SnsTaskTime[0]);
            }
            SNS_TASK_DEBUG_PRINT("F_R Map Time:%ld us,Cycle Time:%d ms\r\n",Gu32SnsTaskTime[1],Gu32SnsTaskTime[2] - Gu32SnsTaskTime[3]);
            Gu32SnsTaskTime[3] = Gu32SnsTaskTime[2];
#endif

            GenuSnsTaskStep = SNS_TASK_IDLE;
            break;
        }

        default:
        {
            GenuSnsTaskStep = SNS_TASK_IDLE;
            break;
        }
    }
}

