/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * SnsDisFollow_type: 
 * Created on: 2023-07-01 16:37
 * Original designer: AntonyFang
 ******************************************************************************/

#ifndef SnsDisFollow_type_H
#define SnsDisFollow_type_H

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "types.h"


/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/
#define PDC_ECHO_DIS_DIFF_ALLOW     10
#define PDC_OBJ_FOLLOW_INVALID_DIS  0xFFFF

/** @brief: 车辆运动距离与探测距离差 */
#define PDC_CAR_MOVE_WITH_OBJ_DIS_SUB    	        100
#define PDC_CAR_MOVE_WITH_OBJ_DIS_SUB_THREE         60
#define PDC_CAR_MOVE_WITH_OBJ_DIS_SUB_THREE_CORBER  80

#define PDC_CAR_MOVE_WITH_OBJ_DIS_DIFF   	   100//80
#define PDC_CAR_MOVE_WITH_OBJ_DIS_SUB_ALLOW    60//50

#define SIDE_PDC_CAR_MOVE_WITH_OBJ_DIS_SUB    	   100//100
#define SIDE_PDC_CAR_MOVE_WITH_OBJ_DIS_DIFF   	   100//80
#define SIDE_PDC_CAR_MOVE_WITH_OBJ_DIS_SUB_ALLOW    100//50



#define SIDE_OBJ_FOLLOW_CAR_SPEED_LOW        250
#define SIDE_OBJ_FOLLOW_CAR_SPEED_MID        400
#define SIDE_OBJ_FOLLOW_CAR_SPEED_HIGH       700

#define CORNER_OBJ_FOLLOW_CAR_SPEED_LIMIT    700



/** @brief: 速度单位毫米/秒 */
#define PDC_OBJ_SPEED_5KM_H_TO_MM     (sint32)1388
#define PDC_OBJ_SPEED_4KM_H_TO_MM     (sint32)1111
#define PDC_OBJ_SPEED_3KM_H_TO_MM     (sint32)833
#define PDC_OBJ_SPEED_2KM_H_TO_MM     (sint32)555
#define PDC_OBJ_SPEED_1KM_H_TO_MM     (sint32)277
#define PDC_OBJ_SPEED_0_5KM_H_TO_MM   (sint32)139
#define PDC_OBJ_SPEED_0_2KM_H_TO_MM   (sint32)55

#define PDC_SIDE_SNS_MOVEDIS_IN_1KM_H_SPEED   22

/** @brief: 障碍物回波抖动计算出的速度 */
#define PDC_OBJ_SPEED_SHAKE_LIMIT_MM   (sint32)50


#define PDC_OBJ_DISTANCE_SUB_INVALID   0x7FFF


#define PDC_ECHO_FOLLOW_SPEED_INVALID       0x7FFFFFFF
#define PDC_ECHO_FOLLOW_SPEED_STOP          0

#define PDC_ECHO_FOLLOW_DISTANCE_INVALID    0xFFFF
#define PDC_ECHO_FOLLOW_TIME_INVALID        0
#define PDC_ECHO_FOLLOW_HEIGHT_INVALID      0
#define PDC_ECHO_FOLLOW_VPEAK_INVALID       0

#define PDC_FOLLOW_ECHO_DIS_BACKUP_NUM     5

#define FOLLOW_INVALID_CNT                  (uint8)(3)

/* 定义不同距离类型，用于区分定扫置信度递减 */
#define STANDARD_PROB_SUB_DIS              2000
#define CHIRP_PROB_SUB_DIS                 800
#define STANDARD_CHIRP_CONFIRM_START_DIS   1800
#define STANDARD_CHIRP_CONFIRM_END_DIS     800
#define CHIRP_BLIND_DIS                    500      /* 同频干扰修改，50cm采用阈值过滤，50cm后采用定扫确认 */
#define STANDARD_BLIND_DIS                 2500
#define FOLLOW_DIS_SUB_INVALID             32767
#define FOLLOW_STOP_VALID_DIS_SUB          100
#define FOLLOW_STOP_FAST_CONFIRM_DIS_SUB   50
#define FOLLOW_STOP_POWER_ON_FAST_CONFIRM_DIS 2000
#define FOLLOW_STOP_1_3_CONFIRM_DIS        900

#define FOLLOW_STOP_STAND_CHIP_SUB          200
#define FOLLOW_STOP_STAND_CHIP_NOISE_CNT    5
#define FOLLOW_PERIOD                       10
#define FOLLOW_STOP_STAND_CHIP_COMFIRM_STAR_DISTANCE    500     /* 定扫确认起始距离 */
#define FOLLOW_STOP_STAND_CHIP_COMFIRM_END_DISTANCE     2500    /* 定扫确认终止距离 */
#define FOLLOW_MOVE_STAND_CHIP_COMFIRM_STAR_DISTANCE    400     /* 定扫确认起始距离 */
#define FOLLOW_MOVE_STAND_CHIP_COMFIRM_END_DISTANCE     2600    /* 定扫确认终止距离 */

#define SEARCH_FAST_COMFIRM_DIS            500      /* 静止状态下，定扫两次比对确认距离 */

#define FOLLOW_CHIRP_FAST_CONFIRM_DIS      2000     /* 扫频模式下，可以快速确认的起始距离 1800 改为1300 */
#define FOLLOW_STANDARD_FAST_CONFIRM_DIS   900      /* 定频模式下，可以快速确认的起始距离 */
#define FOLLOW_STANDARD_SLOW_COMFIRM_DIS   500
#define FOLLOW_HIGH_CONFIRDENCE_DIS        300      /* 定频模式下，快速上到满置信度的距离 */
#define FOLLOW_STOP_EXIT_CNT               3
#define SIDE_OBJ_FOLLOW_CAR_SPEED_LIMIT    1000
#define FOLLOW_BLIND_DIS                   400      /* 盲区跟踪距离 */
#define FOLLOW_STOP_BLIND_EXIT_CNT         6

#define SIDE_CHIRP_FAST_CONFIRM_DIS        2200     /* 扫频模式下，可以快速确认的起始距离 */
#define SIDE_STANDARD_FAST_CONFIRM_DIS     2000     /* 定频模式下，可以快速确认的起始距离--同时考虑灌木丛场景，需要扩大750 到2000 */
#define SIDE_FOLLOW_FAST_CONFIRM_DIS_SUB   50
#define SIDE_FOLLOW_EXIT_CNT               1        /* 侧雷达不要跟踪那么久，容易跟踪错误--2023-11-07 */

#define MIDDLE_CHIRP_FAST_CONFIRM_DIS      2200     /* 扫频模式下，可以快速确认的起始距离 */
#define MIDDLE_STANDARD_FAST_CONFIRM_DIS   2500     /* 定频模式下，可以快速确认的起始距离 800 改为2500(优化灌木丛输出晚的问题)*/
#define MIDDLE_HIGH_SPD_FAST_CONFIRM_DIS   2500     /* 高速模式下，可以快速确认的起始距离 */

#define MIDDLE_FOLLOW_EXIT_CNT             2
#define MIDDLE_WALL_FAST_EXIT_CNT          1
#define MIDDLE_MOVE_BLIND_FOLLOW_EXIT_CNT  3
#define MIDDLE_MOVE_BLIND_FOLLOW_DIS       500

#define CORNER_CHIRP_FAST_CONFIRM_DIS      2200     /* 扫频模式下，可以快速确认的起始距离 */
#define CORNER_STANDARD_FAST_CONFIRM_DIS   800      /* 定频模式下，可以快速确认的起始距离 */
#define CORNER_FOLLOW_EXIT_CNT             2
#define CORNER_FOLLOW_FAST_EXIT_CNT        1

#define MIDDLE_CORNER_MOVE_DIS_FOR_5KM     370      /* 车速小于5km/h时探测距离差值 */  
#define MIDDLE_CORNER_MOVE_DIS_FOR_10KM    600      /* 车速小于10km/h时探测距离差值 */  
#define MIDDLE_CORNER_MOVE_DIS_FOR_15KM    800      /* 车速小于15km/h时探测距离差值 */

#define MIDDLE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO   60      /* 障碍物移动距离和车辆移动距离的差值较小时，置信度首发置高 */  
#define MIDDLE_OBJ_CAR_MOVE_DIS_FOR_5KM    100      /* 车速小于5km/h时障碍物移动距离和车辆移动距离的差值 */  
#define MIDDLE_OBJ_CAR_MOVE_DIS_FOR_10KM   120      /* 车速小于10km/h时障碍物移动距离和车辆移动距离的差值 */  
#define MIDDLE_OBJ_CAR_MOVE_DIS_FOR_15KM   150      /* 车速小于15km/h时障碍物移动距离和车辆移动距离的差值 */  
#define MIDDLE_OBJ_USE_PARALLEL_DIS        1200


#define CORNER_CORNER_MOVE_DIS_FOR_5KM     370      /* 车速小于5km/h时探测距离差值 */  
#define CORNER_CORNER_MOVE_DIS_FOR_10KM    600      /* 车速小于10km/h时探测距离差值 */  
#define CORNER_CORNER_MOVE_DIS_FOR_15KM    800      /* 车速小于15km/h时探测距离差值 */

#define CORNER_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO   60   /* 障碍物移动距离和车辆移动距离的差值较小时，置信度首发置高 */  
#define CORNER_OBJ_CAR_MOVE_DIS_FOR_5KM    80       /* 车速小于5km/h时障碍物移动距离和车辆移动距离的差值 */  
#define CORNER_OBJ_CAR_MOVE_DIS_FOR_10KM   100      /* 车速小于10km/h时障碍物移动距离和车辆移动距离的差值 */  
#define CORNER_OBJ_CAR_MOVE_DIS_FOR_15KM   120      /* 车速小于15km/h时障碍物移动距离和车辆移动距离的差值 */  
#define CORNER_OBJ_PARALLEL_WALL_DIS_SUB   50       /* 角雷达在平行墙场景下，距离变化差值 */  
#define CORNER_OBJ_DIRECT_WALL_DIS_SUB     30       /* 角雷达在平行墙场景下，距离变化差值小于3cm的直接认为是平行墙场景 */  
#define CORNER_DIRECT_WALL_CAR_DIS_LIMIT   35       /* 角雷达在平行墙场景下，距离变化差值小于3cm的直接认为是平行墙场景(车辆移动距离限制) */  
#define CORNER_DIRECT_WALL_DETECT_DIS_MAX  2500     /* 角雷达在平行墙场景下，距离变化差值小于3cm的直接认为是平行墙场景(探测距离的限制设置250cm) */  
#define CORNER_HIGH_SPD_FAST_CONFIRM_DIS   2000     /* 角雷达在高速场景下，快速出DE CE的距离限制值 */  
#define CORNER_PARALLEL_WALL_CNT           2        /* 角雷达平行墙场景 */  

#define SIDE_HIGH_SPD_FAST_CONFIRM_DIS     3000     /* 侧边雷达在高速场景下，快速出DE CE的距离限制值 */  
#define SIDE_OBJ_CAR_MOVE_DIS_FOR_HIGH_PRO 60       /* 障碍物移动距离和车辆移动距离的差值较小时，置信度首发置高 */  
#define SIDE_HIGH_SPD_FAST_CONFIRM_SPD     600      /* 侧雷达在快速模式下，快速出DE CE的车速 */ 

#define MIDDLE_STAND_CURB_START_DIS           4000
#define MIDDLE_STAND_CURB_END_DIS             700
#define MIDDLE_STAND_CURB_HEIGHT_MIN_DIS      700
#define MIDDLE_STAND_CURB_HEIGHT_MAX_DIS      2800
#define MIDDLE_STAND_CURB_MASTER_LISTEN_SUB   500
#define STAND_CURB_STD_HEIGHT_SUB             150
#define STAND_CURB_CHIRP_HEIGHT_SUB           600
#define STAND_CURB_STD_NEAR_HEIGHT_SUB        50
#define STAND_CURB_CHIRP__NEAR_HEIGHT_SUB     200
#define STAND_CURB_VALID_CNT                  3
#define STAND_CURB_VALID_FAST_CNT             1
#define STAND_CURB_INVALID_CNT                5
#define STAND_CURB_VALID_FAST_DIS             1000
#define STAND_CURB_USE_HEIGHT_JUDGE_DIS       1200

#define STAND_CURB_LEFT_RIGHT_DIS_SUB_1_KM    100
#define STAND_CURB_LEFT_RIGHT_DIS_SUB_3_KM    250
#define STAND_CURB_LEFT_RIGHT_DIS_SUB_5_KM    350
#define STAND_CURB_LEFT_RIGHT_DIS_SUB_7_KM    450



#define REAR_CORNER_INNER_DIS                 250
#define FRONT_CORNER_INNER_DIS                135


/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/

typedef enum
{
    SNS_FOLLOW_MASTER   = 0,
    SNS_FOLLOW_LEFT_LISTEN , 
    SNS_FOLLOW_RIGHT_LISTEN,   
}SnsFollowWorkType;


/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/








#endif /* end of SnsDisFollow_type_H */

