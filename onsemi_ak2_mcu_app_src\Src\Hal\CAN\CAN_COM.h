/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: CAN_COM.h
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/
#ifndef __CAN_COM_H__
#define __CAN_COM_H__

#include "types.h"
#include "CANCfg.h"
#include "DebugSignalManage.h"
/*************************************宏定义***********************************/
#define COM_TIMEOUT_RECOVER_COUNT           5U    /* CAN 接收超时标志置位后，重新恢复的时间，一般5次 */

typedef enum
{
    FRAME_ID_6F2 = 0,
    FRAME_ID_6F3,
    FRAME_ID_6F4,
    FRAME_ID_6F5,
    FRAME_ID_6F6,
    FRAME_ID_6F7,
    FRAME_ID_6F8,
    FRAME_ID_6F9,
    FRAME_ID_6FA,
    FRAME_ID_6FB,
    FRAME_ID_6FC,
    FRAME_ID_6FD,
    FRAME_ID_6FE,
    FRAME_ID_6FF,
    DEBUG_MASSAGE_NUM
}DebugMassageType;

#define ENABLE_RX_TX                        0x00u
#define ENABLE_RX_DISABLE_TX                0x01u
#define DISABLE_RX_ENABLE_TX                0x02u
#define DISABLE_RX_TX                       0x03u
#define APP_MSG                             0x01u
#define NM_MSG                              0x02u
#define APP_NM_MSG                          0x03u

#define COM_FRAME_STATUS_TX_REQ             0x01U    /* 发送请求使能 */
#define COM_FRAME_STATUS_RX_LOST_FLAG       0x01U    /* 接收数据丢失标志 */
#define COM_FRAME_STATUS_TX_PROGRESS        0x02U    /* 正在发送中......*/
#define COM_FRAME_STATUS_RX_FLAG            0x02U    /* 开启接收标志位 */
#define COM_FRAME_STATUS_PERIOD_EN          0x04U    /* 周期性发送使能  */
#define COM_FRAME_STATUS_TX_CONF            0x08U    /* 发送完成确认 */
#define COM_FRAME_STATUS_TX_DISABLE         0x10U    /* 发送禁止 */
#define COM_FRAME_STATUS_RX_DISABLE         0x10U    /* 接收禁止 */
#define COM_FRAME_STATUS_RX_TIMEOUT         0x20U    /* 接收超时 */
#define COM_FRAME_STATUS_RX_OK              0x40U    /* 接收数据OK */
#define COM_FRAME_STATUS_TX_DATA            0x80U    /* 发送数据状态 */

#define NM_CTRL_ENABLE_TXRX                 0x0u
#define NM_CTRL_DISABLE_TXRX                0x1u
#define NM_CTRL_DISABLETX_ENABLERX          0x2u

#define COM_FRAME_NMCTRL_STATUS_TX_DISABLE  0x01u    /* 网络管理控制报文发送关闭 */
#define COM_FRAME_NMCTRL_STATUS_RX_DISABLE  0x10u    /* 网络管理控制报文接收关闭 */

/* Protocol handler Mailbox number used for transmission */
/* Number of transmission mailboxes is imposed by protocol handler hardware */

/* Protocol handler mailbox status */
#define COM_MAILBOX_FREE                    0U
#define COM_MAILBOX_BUSY                    1U

/* COM functions status */
#define COM_ERR_BASE                        0x10U
#define COM_ERR_OK                          0x00U
#define COM_ERR_OFF                        ( COM_ERR_BASE + 0x01U )
#define COM_ERR_IDX                        ( COM_ERR_BASE + 0x03U )
#define COM_ERR_FRAME_MODE                 ( COM_ERR_BASE + 0x04U )
#define COM_ERR_TX_MSG_LOST                ( COM_ERR_BASE + 0x06U )
#define COM_ERR_NULL                       ( COM_ERR_BASE + 0x09U )

#define CAN_BUS_OFF                     0x01
#define CAN_BUS_ON                      0x02
#define CAN_BUS_ERROR_PASSTIVE          0x04
#define CAN_BUS_ERROR_ACTIVE            0x08
#define CAN_BUS_NO_ERROR                0x08

#define BUSOFF_ERROR_LIMIT              5u
#define BUSOFF_TIMER_PERIOD             1u
#define BUSOFF_QUICK_RECOVER_TIME       (100u/COM_MANAGE_PERIOD)
#define BUSOFF_SLOW_RECOVER_TIME        (1000u/COM_MANAGE_PERIOD)
#define RX_MONITOR_RECOVER_TIME         (950u/COM_MANAGE_PERIOD)

#define DELAYDETECT5A4TIME60S               (60000U)/*延时60S检测5A4同步时间报文*/

#define DISABLEDETECT5A4               (0U)/*延时60S检测5A4同步时间报文*/
#define ENABLEDETECT5A4               (1U)/*延时60S检测5A4同步时间报文*/
/******************************************************************************** 
* External objects 
********************************************************************************/
extern volatile uint8 GcCANBusState;
extern volatile uint16 GwBusoffRecoverTimer;
extern volatile uint8 GcBusoffContinueCnt;
extern volatile uint8 Gu8BusoffRecoverCnt;             /* 连续发生Busoff的次数，发送成功函数对其清零 */

extern uint8 GcTxMailboxStatus[TXMAILBOXNUM];
extern uint8 GcTxFrameIdx[TXMAILBOXNUM];
extern uint8 GcFrameStatus[RXANDTXFRAMEMAXNUM]; 
extern uint8 GcComDataBuffer[RXANDTXFRAMEMAXNUM][COM_DATA_MAX];
extern uint16 GwComFramePeriodCnt[RXANDTXFRAMEMAXNUM];
extern uint8 Gu8ComDataDLC[RXANDTXFRAMEMAXNUM];
extern uint16 Gu16DelayDetect5A4Timer_60S;
extern uint8 Gu8StartDetect5A4Flag;
extern volatile uint32 Gu32CANTimeSysn_RxInt;

//extern uint8 Gu8DebugMsg[DEBUG_MASSAGE_NUM][64];                /* 调试报文内容指针 */
/****************************************************************************** 
* Global Functions 
********************************************************************************/
/******************************************************************************
* 设计描述 : 对外设计函数
* 设计索引 : 
*******************************************************************************/
extern void COM_Init (void);
extern void COM_Activate (void);
extern void COM_RxMonitor(void);
extern void COM_TxManage (void);
extern void COM_NMAndAPPMsgControl_RX(uint8 LcControlType, uint8 LcMsgType);
extern void COM_NMAndAPPMsgControl_TX(uint8 LcControlType, uint8 LcMsgType);
extern void BusOffRecoverMonitor(void);
extern void COM_CancelDiagTx(void);
extern void COM_RxMonitorInit(void);
extern void COMSigMgr_RxMonitorInit(void);
extern void CAN_SendManage(void);
extern void COMSigMgr_InitSYNCMomiter(void);
extern void InitCAN(void);
//extern void COM_SendDebugFrame(uint16 Lu16FrameID, uint8 Lu8DLC, uint8* LptrDebugMsg,uint8 DebugIdx);
#endif

