/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * PublicCalAlgorithm_Prg: 
 * Created on: 2022-11-26 09:56
 * Original designer: AntonyFang
 ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "PublicCalAlgorithm_Int.h"


/******************************************************************************/
/******************************************************************************/
/****************************** Private Definition ****************************/
/******************************************************************************/
/******************************************************************************/
static Pub_CalAiCarOdoType GstrPub_CalAiCarOdo;
static Pub_CalAiSnsCarType GstrPub_CalAiSnsCarCoor;


/******************************************************************************/
/******************************************************************************/
/****************************** Public Definition *****************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/******************************* Public Function ******************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************************
 * 函数名称: CalTwoPointDis
 * 
 * 功能描述: 计算2点之间的距离,由于为了保证精度，输入输出都采用浮点型，实际调用时在各自模块做强制类型转换即可
 * 
 * 输入参数: LfP1_X_Coor--P1点X坐标， LfP1_Y_Coor--P1点Y坐标，LfP2_X_Coor--P2点X坐标， LfP2_Y_Coor--P2点Y坐标
 * 
 * 输出参数:无 
 * 
 * 返回值:LfTwoPointDis--返回两点之间的距离
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-11-26 10:02   V0.1      AntonyFang   初次发布
 ******************************************************************************/
float PubAI_CalTwoPointDis(float LfP1_X_Coor,float LfP1_Y_Coor,float LfP2_X_Coor,float LfP2_Y_Coor)
{
    float LfTwoPointDis = 0.0;
    float LfXDis,LfYDis;
    float LfXSub,LfYSub;
    
    LfXSub = ABS(LfP1_X_Coor,LfP2_X_Coor);
    LfYSub = ABS(LfP1_Y_Coor,LfP2_Y_Coor);
    
    LfXDis = LfXSub*LfXSub;
    LfYDis = LfYSub*LfYSub;

    LfTwoPointDis = powf(LfXDis+LfYDis,0.5);

    return LfTwoPointDis;
}


/******************************************************************************
 * 函数名称: PubAI_CalP1_P2_LineToX_AxleAngle
 * 
 * 功能描述: 计算一个坐标系中，由P1,P2点连成的直线，与该坐标系X轴的夹角
 * 
 * 输入参数:LfP1_X_Coor--P1点X坐标， LfP1_Y_Coor--P1点Y坐标，LfP2_X_Coor--P2点X坐标， LfP2_Y_Coor--P2点Y坐标
 * 
 * 输出参数:无 
 * 
 * 返回值:LfAngleToX_Axle--两点连线到X轴的夹角，弧度制，范围[-pi,pi]
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-11-26 10:15   V0.1      AntonyFang   初次发布
 ******************************************************************************/
float PubAI_CalP1_P2_LineToX_AxleAngle(float LfP1_X_Coor,float LfP1_Y_Coor,float LfP2_X_Coor,float LfP2_Y_Coor)
{
    float LfAngleToX_Axle = 0.0;
    float LfX_Sub = 0.0;
    float LfY_Sub = 0.0;
    //float LfX_SubAbs = 0.0;
    /* 首先判断哪个是起始点，哪个是终止点 */
    if(LfP1_X_Coor < LfP2_X_Coor)
    {
        /* 哪一个点小，哪一个是起始点,计算角度是用终点减去起始点 */
        LfX_Sub = LfP2_X_Coor - LfP1_X_Coor;
        LfY_Sub = LfP2_Y_Coor - LfP1_Y_Coor;
    }
    else
    {
        LfX_Sub = LfP1_X_Coor - LfP2_X_Coor;
        LfY_Sub = LfP1_Y_Coor - LfP2_Y_Coor;
    }

    LfAngleToX_Axle = atan2f(LfY_Sub,LfX_Sub);
        
    return LfAngleToX_Axle;
}


/******************************************************************************
 * 函数名称: PubAI_UpdateCarOdoCoor
 * 
 * 功能描述: 在调用Odo相关坐标转换时，首先需要更新Odo坐标至PubAI模块
 * 
 * 输入参数:LfCarRearAxle_X--车辆后轴中心X坐标，LfCarRearAxle_Y--车辆后轴中心Y坐标，LfCarRearAxle_Angle--车身斜度
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-11-28 09:24   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PubAI_UpdateCarOdoCoor(float LfCarRearAxle_X,float LfCarRearAxle_Y,float LfCarRearAxle_SinAngle,float LfCarRearAxle_CosAngle)
{
    GstrPub_CalAiCarOdo.fCarRearAxle_X = LfCarRearAxle_X;
    GstrPub_CalAiCarOdo.fCarRearAxle_Y = LfCarRearAxle_Y;
    GstrPub_CalAiCarOdo.fCarRearAxle_SinAngle = LfCarRearAxle_SinAngle;
    GstrPub_CalAiCarOdo.fCarRearAxle_CosAngle = LfCarRearAxle_CosAngle;
}


/******************************************************************************
 * 函数名称: PubAI_TransObjCarCoorToOdoCoor
 * 
 * 功能描述: 将障碍物在车辆坐标系中的坐标转换到Odo世界坐标系中；调用前需要先调用PubAI_UpdateCarOdoCoor更新对应的后轴中心坐标
 * 
 * 输入参数:LfObjCarCoorX--当前障碍物在车辆坐标系中的X坐标指针，LfObjCarCoorY--当前障碍物在车辆坐标系中的Y坐标指针
 * 
 * 输出参数:LfObjOdoCoorX--当前障碍物转换到Odo的X坐标指针，LfObjOdoCoorY--当前障碍物转换到Odo的Y坐标指针；
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-11-28 09:29   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PubAI_TransObjCarCoorToOdoCoor(float *LfObjCarCoorX,float *LfObjCarCoorY,float *LfObjOdoCoorX,float *LfObjOdoCoorY)
{
    *LfObjOdoCoorX = GstrPub_CalAiCarOdo.fCarRearAxle_X + (*LfObjCarCoorX)*GstrPub_CalAiCarOdo.fCarRearAxle_CosAngle - (*LfObjCarCoorY)*GstrPub_CalAiCarOdo.fCarRearAxle_SinAngle;
    *LfObjOdoCoorY = GstrPub_CalAiCarOdo.fCarRearAxle_Y + (*LfObjCarCoorY)*GstrPub_CalAiCarOdo.fCarRearAxle_CosAngle + (*LfObjCarCoorX)*GstrPub_CalAiCarOdo.fCarRearAxle_SinAngle;
}



/******************************************************************************
 * 函数名称: PubAI_TransObjOdoCoorToCarCoor
 * 
 * 功能描述: 将障碍物在Odo坐标系中的坐标转换到车辆坐标系中；调用前需要先调用PubAI_UpdateCarOdoCoor更新对应的后轴中心坐标
 * 
 * 输入参数:LfObjOdoCoorX--当前障碍在Odo的X坐标指针，LfObjOdoCoorY--当前障碍物在Odo的Y坐标指针；
 * 
 * 输出参数:LfObjCarCoorX--当前障碍物转换到车辆坐标系中的X坐标指针，LfObjCarCoorY--当前障碍物转换到车辆坐标系中的Y坐标指针
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-11-28 09:40   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PubAI_TransObjOdoCoorToCarCoor(float *LfObjOdoCoorX,float *LfObjOdoCoorY,float *LfObjCarCoorX,float *LfObjCarCoorY)
{
    *LfObjCarCoorX = (*LfObjOdoCoorX - GstrPub_CalAiCarOdo.fCarRearAxle_X)*GstrPub_CalAiCarOdo.fCarRearAxle_CosAngle +\
                               (*LfObjOdoCoorY - GstrPub_CalAiCarOdo.fCarRearAxle_Y)*GstrPub_CalAiCarOdo.fCarRearAxle_SinAngle;
    *LfObjCarCoorY = (*LfObjOdoCoorY - GstrPub_CalAiCarOdo.fCarRearAxle_Y)*GstrPub_CalAiCarOdo.fCarRearAxle_CosAngle -\
                               (*LfObjOdoCoorX - GstrPub_CalAiCarOdo.fCarRearAxle_X)*GstrPub_CalAiCarOdo.fCarRearAxle_SinAngle;
}



/******************************************************************************
 * 函数名称: PubAI_UpdateSnsInCarCoor
 * 
 * 功能描述: 更新探头在车辆坐标系中的坐标
 * 
 * 输入参数:LfSnsCoorX--探头在车辆坐标系中的X坐标；LfSnsCoorY--探头在车辆坐标系中的Y坐标；LfSnsCoorSinAngle--探头在车辆坐标系中的斜度sin值；LfSnsCoorCosAngle--探头在车辆坐标系中的斜度cos值；
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-12-13 17:00   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PubAI_UpdateSnsInCarCoor(float LfSnsCoorX,float LfSnsCoorY,float LfSnsCoorSinAngle,float LfSnsCoorCosAngle)
{
    GstrPub_CalAiSnsCarCoor.fSnsCoorX = LfSnsCoorX;
    GstrPub_CalAiSnsCarCoor.fSnsCoorY = LfSnsCoorY;
    GstrPub_CalAiSnsCarCoor.fSnsCoorSinAngle = LfSnsCoorSinAngle;
    GstrPub_CalAiSnsCarCoor.fSnsCoorCosAngle = LfSnsCoorCosAngle;
}

/******************************************************************************
 * 函数名称: PubAI_TransObjSnsCoorToCarCoor
 * 
 * 功能描述: 将障碍物在探头坐标系中的坐标转换到车辆坐标系中；调用前需要先调用PubAI_UpdateSnsInCarCoor来更新探头在车辆坐标系中的坐标
 * 
 * 输入参数:LfObjSnsCoorX--当前障碍物在探头坐标系中的X坐标指针；LfObjSnsCoorY--当前障碍物在探头坐标系中的Y坐标指针；
 * 
 * 输出参数:LfObjCarCoorX--当前障碍物在车辆坐标系中的X坐标指针；LfObjCarCoorY--当前障碍物在车辆坐标系中的Y坐标指针；
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-12-13 16:33   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PubAI_TransObjSnsCoorToCarCoor(float *LfObjSnsCoorX,float *LfObjSnsCoorY,float *LfObjCarCoorX,float *LfObjCarCoorY)
{
    *LfObjCarCoorX = GstrPub_CalAiSnsCarCoor.fSnsCoorX + (*LfObjSnsCoorX)*GstrPub_CalAiSnsCarCoor.fSnsCoorCosAngle - (*LfObjSnsCoorY)*GstrPub_CalAiSnsCarCoor.fSnsCoorSinAngle;
    *LfObjCarCoorY = GstrPub_CalAiSnsCarCoor.fSnsCoorY + (*LfObjSnsCoorY)*GstrPub_CalAiSnsCarCoor.fSnsCoorCosAngle + (*LfObjSnsCoorX)*GstrPub_CalAiSnsCarCoor.fSnsCoorSinAngle;
}

/******************************************************************************
 * 函数名称: PubAI_CalObjCosAngle
 * 
 * 功能描述: 已知三边边长，计算由其中一个角度的Cos角度值
 * 
 * 输入参数:Lu16OppositeSideDis--该夹角的对边长度，Lu16FirstNearSideDis--该角的第一个临边长度，Lu16SecondNearSideDis--该角的第二个临边长度
 * 
 * 输出参数:无 
 * 
 * 返回值:LfCosAngle--返回该角度弧度值的的余弦值
 * 
 * 其它说明:该函数不做三角形有效性判断，各模块使用时，自行校验三角形的有效性
 *           余弦定理说明：a^2 = b^2 + c^2 - 2*b*c*cosA
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-11-28 09:52   V0.1      AntonyFang   初次发布
 ******************************************************************************/
float PubAI_CalObjCosAngle(uint16 Lu16OppositeSideDis,uint16 Lu16FirstNearSideDis,uint16 Lu16SecondNearSideDis)
#if 1
{
    float LfCosAngle;
    float LfDisSub = 0.0;
    
    uint32 Lu32OppositeSideDisSquare;
    uint32 Lu32FirstNearSideDisSquare;
    uint32 Lu32SecondNearSideDisSquare;

    Lu32OppositeSideDisSquare = Lu16OppositeSideDis*Lu16OppositeSideDis;
    Lu32FirstNearSideDisSquare = Lu16FirstNearSideDis*Lu16FirstNearSideDis;
    Lu32SecondNearSideDisSquare = Lu16SecondNearSideDis*Lu16SecondNearSideDis;


#if 1
    LfDisSub = Lu32FirstNearSideDisSquare+Lu32SecondNearSideDisSquare;
    LfDisSub = LfDisSub - Lu32OppositeSideDisSquare;

#else
    LfDisSub = (float)(Lu32FirstNearSideDisSquare+Lu32SecondNearSideDisSquare - Lu32OppositeSideDisSquare);
#endif
    

#if 1
    LfCosAngle = LfDisSub/(Lu16FirstNearSideDis*Lu16SecondNearSideDis*2); 
#else
    LfCosAngle = LfDisSub/(Lu32FirstNearSideDisSquare*Lu32SecondNearSideDisSquare*2); 
#endif
    
    return(LfCosAngle);
}
#else
{
    float LfCosAngle;
    float LfDisSub = 0.0;
    
    uint32 Lu32OppositeSideDisSquare;
    uint32 Lu32FirstNearSideDisSquare;
    uint32 Lu32SecondNearSideDisSquare;

    Lu32OppositeSideDisSquare = Lu16OppositeSideDis*Lu16OppositeSideDis;
    Lu32FirstNearSideDisSquare = Lu16FirstNearSideDis*Lu16FirstNearSideDis;
    Lu32SecondNearSideDisSquare = Lu16SecondNearSideDis*Lu16SecondNearSideDis;


    LfDisSub = (float)(Lu32FirstNearSideDisSquare+Lu32SecondNearSideDisSquare - Lu32OppositeSideDisSquare);
    

    LfCosAngle = LfDisSub/(Lu32FirstNearSideDisSquare*Lu32SecondNearSideDisSquare*2); 
    
    return(LfCosAngle);
}
#endif


/******************************************************************************
 * 函数名称: PubAI_BubbleSortFor_uint8_Type
 * 
 * 功能描述: uint8 类型数据的冒泡排序
 * 
 * 输入参数:Lu16DataLen--数据长度，Lpu8DataPtr--待排序数据指针，LenuPub_SortType--排序类型
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-11-28 11:00   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PubAI_BubbleSortFor_uint8_Type(uint16 Lu16DataLen,uint8 *Lpu8DataPtr,Pub_SortTypeType LenuPub_SortType)
{
    uint8 Lu8DataTemp;
    uint16 i,j;
    
    for (i = 0; i < Lu16DataLen; i++) 
    {
        for (j = 0; j + 1 < Lu16DataLen - i; j++) 
        {
            if(LenuPub_SortType == SORT_ASCENDING)
            {
                if(Lpu8DataPtr[j] > Lpu8DataPtr[j+1]) 
                {
                    Lu8DataTemp = Lpu8DataPtr[j];
                    Lpu8DataPtr[j] = Lpu8DataPtr[j+1];
                    Lpu8DataPtr[j+1] = Lu8DataTemp;
                }
            }
            else
            {
                if(Lpu8DataPtr[j] < Lpu8DataPtr[j+1]) 
                {
                    Lu8DataTemp = Lpu8DataPtr[j];
                    Lpu8DataPtr[j] = Lpu8DataPtr[j+1];
                    Lpu8DataPtr[j+1] = Lu8DataTemp;
                }
            }
        }
    }
}


/******************************************************************************
 * 函数名称: PubAI_BubbleSortFor_sint8_Type
 * 
 * 功能描述: sint8 类型数据的冒泡排序
 * 
 * 输入参数:Lu16DataLen--数据长度，Lps8DataPtr--待排序数据指针，LenuPub_SortType--排序类型
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-11-28 11:02   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PubAI_BubbleSortFor_sint8_Type(uint16 Lu16DataLen,sint8 *Lps8DataPtr,Pub_SortTypeType LenuPub_SortType)
{
    sint8 Ls8DataTemp;
    uint16 i,j;
    
    for (i = 0; i < Lu16DataLen; i++) 
    {
        for (j = 0; j + 1 < Lu16DataLen - i; j++) 
        {
            if(LenuPub_SortType == SORT_ASCENDING)
            {
                if(Lps8DataPtr[j] > Lps8DataPtr[j+1]) 
                {
                    Ls8DataTemp = Lps8DataPtr[j];
                    Lps8DataPtr[j] = Lps8DataPtr[j+1];
                    Lps8DataPtr[j+1] = Ls8DataTemp;
                }
            }
            else
            {
                if(Lps8DataPtr[j] < Lps8DataPtr[j+1]) 
                {
                    Ls8DataTemp = Lps8DataPtr[j];
                    Lps8DataPtr[j] = Lps8DataPtr[j+1];
                    Lps8DataPtr[j+1] = Ls8DataTemp;
                }
            }
        }
    }
}


/******************************************************************************
 * 函数名称: PubAI_BubbleSortFor_uint16_Type
 * 
 * 功能描述: uint16 类型数据的冒泡排序
 * 
 * 输入参数:Lu16DataLen--数据长度，Lpu16DataPtr--待排序数据指针，LenuPub_SortType--排序类型
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-11-28 11:04   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PubAI_BubbleSortFor_uint16_Type(uint16 Lu16DataLen,uint16 *Lpu16DataPtr,Pub_SortTypeType LenuPub_SortType)
{
    uint16 Lu16DataTemp;
    uint16 i,j;
    
    for (i = 0; i < Lu16DataLen; i++) 
    {
        for (j = 0; j + 1 < Lu16DataLen - i; j++) 
        {
            if(LenuPub_SortType == SORT_ASCENDING)
            {
                if(Lpu16DataPtr[j] > Lpu16DataPtr[j+1]) 
                {
                    Lu16DataTemp = Lpu16DataPtr[j];
                    Lpu16DataPtr[j] = Lpu16DataPtr[j+1];
                    Lpu16DataPtr[j+1] = Lu16DataTemp;
                }
            }
            else
            {
                if(Lpu16DataPtr[j] < Lpu16DataPtr[j+1]) 
                {
                    Lu16DataTemp = Lpu16DataPtr[j];
                    Lpu16DataPtr[j] = Lpu16DataPtr[j+1];
                    Lpu16DataPtr[j+1] = Lu16DataTemp;
                }
            }
        }
    }
}


/******************************************************************************
 * 函数名称: PubAI_BubbleSortFor_sint16_Type
 * 
 * 功能描述: sint16 类型数据的冒泡排序
 * 
 * 输入参数:Lu16DataLen--数据长度，Lps16DataPtr--待排序数据指针，LenuPub_SortType--排序类型
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-11-28 11:06   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PubAI_BubbleSortFor_sint16_Type(uint16 Lu16DataLen,sint16 *Lps16DataPtr,Pub_SortTypeType LenuPub_SortType)
{
    sint16 Ls16DataTemp;
    uint16 i,j;
    
    for (i = 0; i < Lu16DataLen; i++) 
    {
        for (j = 0; j + 1 < Lu16DataLen - i; j++) 
        {
            if(LenuPub_SortType == SORT_ASCENDING)
            {
                if(Lps16DataPtr[j] > Lps16DataPtr[j+1]) 
                {
                    Ls16DataTemp = Lps16DataPtr[j];
                    Lps16DataPtr[j] = Lps16DataPtr[j+1];
                    Lps16DataPtr[j+1] = Ls16DataTemp;
                }
            }
            else
            {
                if(Lps16DataPtr[j] < Lps16DataPtr[j+1]) 
                {
                    Ls16DataTemp = Lps16DataPtr[j];
                    Lps16DataPtr[j] = Lps16DataPtr[j+1];
                    Lps16DataPtr[j+1] = Ls16DataTemp;
                }
            }
        }
    }
}


/******************************************************************************
 * 函数名称: PubAI_BubbleSortFor_uint32_Type
 * 
 * 功能描述: uint32 类型数据的冒泡排序
 * 
 * 输入参数:Lu16DataLen--数据长度，Lpu32DataPtr--待排序数据指针，LenuPub_SortType--排序类型
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-11-28 11:09   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PubAI_BubbleSortFor_uint32_Type(uint16 Lu16DataLen,uint32 *Lpu32DataPtr,Pub_SortTypeType LenuPub_SortType)
{
    uint32 Lu32DataTemp;
    uint16 i,j;
    
    for (i = 0; i < Lu16DataLen; i++) 
    {
        for (j = 0; j + 1 < Lu16DataLen - i; j++) 
        {
            if(LenuPub_SortType == SORT_ASCENDING)
            {
                if(Lpu32DataPtr[j] > Lpu32DataPtr[j+1]) 
                {
                    Lu32DataTemp = Lpu32DataPtr[j];
                    Lpu32DataPtr[j] = Lpu32DataPtr[j+1];
                    Lpu32DataPtr[j+1] = Lu32DataTemp;
                }
            }
            else
            {
                if(Lpu32DataPtr[j] < Lpu32DataPtr[j+1]) 
                {
                    Lu32DataTemp = Lpu32DataPtr[j];
                    Lpu32DataPtr[j] = Lpu32DataPtr[j+1];
                    Lpu32DataPtr[j+1] = Lu32DataTemp;
                }
            }
        }
    }
}

/******************************************************************************
 * 函数名称: PubAI_BubbleSortFor_sint32_Type
 * 
 * 功能描述: sint32 类型数据的冒泡排序
 * 
 * 输入参数:Lu16DataLen--数据长度，Lps32DataPtr--待排序数据指针，LenuPub_SortType--排序类型
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-11-28 11:11   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PubAI_BubbleSortFor_sint32_Type(uint16 Lu16DataLen,sint32 *Lps32DataPtr,Pub_SortTypeType LenuPub_SortType)
{
    sint32 Ls32DataTemp;
    uint16 i,j;
    
    for (i = 0; i < Lu16DataLen; i++) 
    {
        for (j = 0; j + 1 < Lu16DataLen - i; j++) 
        {
            if(LenuPub_SortType == SORT_ASCENDING)
            {
                if(Lps32DataPtr[j] > Lps32DataPtr[j+1]) 
                {
                    Ls32DataTemp = Lps32DataPtr[j];
                    Lps32DataPtr[j] = Lps32DataPtr[j+1];
                    Lps32DataPtr[j+1] = Ls32DataTemp;
                }
            }
            else
            {
                if(Lps32DataPtr[j] < Lps32DataPtr[j+1]) 
                {
                    Ls32DataTemp = Lps32DataPtr[j];
                    Lps32DataPtr[j] = Lps32DataPtr[j+1];
                    Lps32DataPtr[j+1] = Ls32DataTemp;
                }
            }
        }
    }
}

/******************************************************************************
 * 函数名称: PubAI_BubbleSortFor_float_Type
 * 
 * 功能描述: float 类型数据的冒泡排序
 * 
 * 输入参数:Lu16DataLen--数据长度，LpfDataPtr--待排序数据指针，LenuPub_SortType--排序类型
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-11-28 11:13   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PubAI_BubbleSortFor_float_Type(uint16 Lu16DataLen,float *LpfDataPtr,Pub_SortTypeType LenuPub_SortType)
{
    float LfDataTemp;
    uint16 i,j;
    
    for (i = 0; i < Lu16DataLen; i++) 
    {
        for (j = 0; j + 1 < Lu16DataLen - i; j++) 
        {
            if(LenuPub_SortType == SORT_ASCENDING)
            {
                if(LpfDataPtr[j] > LpfDataPtr[j+1]) 
                {
                    LfDataTemp = LpfDataPtr[j];
                    LpfDataPtr[j] = LpfDataPtr[j+1];
                    LpfDataPtr[j+1] = LfDataTemp;
                }
            }
            else
            {
                if(LpfDataPtr[j] < LpfDataPtr[j+1]) 
                {
                    LfDataTemp = LpfDataPtr[j];
                    LpfDataPtr[j] = LpfDataPtr[j+1];
                    LpfDataPtr[j+1] = LfDataTemp;
                }
            }
        }
    }
}


/******************************************************************************
 * 函数名称: PubAI_ArrayDataHandle_uint8_Type
 * 
 * 功能描述: 对一组数组进行处理，获取数组和，平均值、最大值、最小值、中位值
 * 
 * 输入参数:Lu16DataLen--数据长度，Lpu8DataPtr--待处理数据指针
 * 
 * 输出参数:LPstrArrayDataHandle--处理后数据的指针，调用时通过指针传入,输出类型为浮点型，使用时根据需要自行做数据转换
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-12-14 09:01   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PubAI_ArrayDataHandle_uint8_Type(uint16 Lu16DataLen,uint8 *Lpu8DataPtr,Pub_CalAiArrayDataHandleType *LPstrArrayDataHandle)
{
    uint16 i;
    uint16 Lu16MiddleIndex;
    if(Lu16DataLen > 0)
    {
        PubAI_BubbleSortFor_uint8_Type(Lu16DataLen,Lpu8DataPtr,SORT_DESCENDING);
        LPstrArrayDataHandle->fMaxValue = Lpu8DataPtr[0];
        LPstrArrayDataHandle->fMinValue = Lpu8DataPtr[Lu16DataLen-1];
        Lu16MiddleIndex = Lu16DataLen/2;
        LPstrArrayDataHandle->fMiddleValue = Lpu8DataPtr[Lu16MiddleIndex];

        LPstrArrayDataHandle->fSumValue = 0;
        for (i = 0; i < Lu16DataLen; i++) 
        {
            LPstrArrayDataHandle->fSumValue += Lpu8DataPtr[i];
        }
        LPstrArrayDataHandle->fAverageValue = LPstrArrayDataHandle->fSumValue/Lu16DataLen;
    }
    else
    {
        LPstrArrayDataHandle->fSumValue = 0;
        LPstrArrayDataHandle->fAverageValue = 0;
        LPstrArrayDataHandle->fMaxValue = 0;
        LPstrArrayDataHandle->fMinValue = 0;
        LPstrArrayDataHandle->fMiddleValue = 0;
    }
}


/******************************************************************************
 * 函数名称: PubAI_ArrayDataHandle_sint8_Type
 * 
 * 功能描述: 对一组数组进行处理，获取数组和，平均值、最大值、最小值、中位值
 * 
 * 输入参数:Lu16DataLen--数据长度，Lps8DataPtr--待处理数据指针
 * 
 * 输出参数:LPstrArrayDataHandle--处理后数据的指针，调用时通过指针传入,输出类型为浮点型，使用时根据需要自行做数据转换
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-12-14 09:04   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PubAI_ArrayDataHandle_sint8_Type(uint16 Lu16DataLen,sint8 *Lps8DataPtr,Pub_CalAiArrayDataHandleType *LPstrArrayDataHandle)
{
    uint16 i;
    uint16 Lu16MiddleIndex;
    if(Lu16DataLen > 0)
    {
        PubAI_BubbleSortFor_sint8_Type(Lu16DataLen,Lps8DataPtr,SORT_DESCENDING);
        LPstrArrayDataHandle->fMaxValue = Lps8DataPtr[0];
        LPstrArrayDataHandle->fMinValue = Lps8DataPtr[Lu16DataLen-1];
        Lu16MiddleIndex = Lu16DataLen/2;
        LPstrArrayDataHandle->fMiddleValue = Lps8DataPtr[Lu16MiddleIndex];

        LPstrArrayDataHandle->fSumValue = 0;
        for (i = 0; i < Lu16DataLen; i++) 
        {
            LPstrArrayDataHandle->fSumValue += Lps8DataPtr[i];
        }
        LPstrArrayDataHandle->fAverageValue = LPstrArrayDataHandle->fSumValue/Lu16DataLen;
    }
    else
    {
        LPstrArrayDataHandle->fSumValue = 0;
        LPstrArrayDataHandle->fAverageValue = 0;
        LPstrArrayDataHandle->fMaxValue = 0;
        LPstrArrayDataHandle->fMinValue = 0;
        LPstrArrayDataHandle->fMiddleValue = 0;
    }
}

/******************************************************************************
 * 函数名称: PubAI_ArrayDataHandle_uint16_Type
 * 
 * 功能描述: 对一组数组进行处理，获取数组和，平均值、最大值、最小值、中位值
 * 
 * 输入参数:Lu16DataLen--数据长度，Lpu16DataPtr--待处理数据指针
 * 
 * 输出参数:LPstrArrayDataHandle--处理后数据的指针，调用时通过指针传入,输出类型为浮点型，使用时根据需要自行做数据转换
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-12-14 09:06   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PubAI_ArrayDataHandle_uint16_Type(uint16 Lu16DataLen,uint16 *Lpu16DataPtr,Pub_CalAiArrayDataHandleType *LPstrArrayDataHandle)
{
    uint16 i;
    uint16 Lu16MiddleIndex;
    if(Lu16DataLen > 0)
    {
        PubAI_BubbleSortFor_uint16_Type(Lu16DataLen,Lpu16DataPtr,SORT_DESCENDING);
        LPstrArrayDataHandle->fMaxValue = Lpu16DataPtr[0];
        LPstrArrayDataHandle->fMinValue = Lpu16DataPtr[Lu16DataLen-1];
        Lu16MiddleIndex = Lu16DataLen/2;
        LPstrArrayDataHandle->fMiddleValue = Lpu16DataPtr[Lu16MiddleIndex];

        LPstrArrayDataHandle->fSumValue = 0;
        for (i = 0; i < Lu16DataLen; i++) 
        {
            LPstrArrayDataHandle->fSumValue += Lpu16DataPtr[i];
        }
        LPstrArrayDataHandle->fAverageValue = LPstrArrayDataHandle->fSumValue/Lu16DataLen;
    }
    else
    {
        LPstrArrayDataHandle->fSumValue = 0;
        LPstrArrayDataHandle->fAverageValue = 0;
        LPstrArrayDataHandle->fMaxValue = 0;
        LPstrArrayDataHandle->fMinValue = 0;
        LPstrArrayDataHandle->fMiddleValue = 0;
    }
}


/******************************************************************************
 * 函数名称: PubAI_ArrayDataHandle_sint16_Type
 * 
 * 功能描述: 对一组数组进行处理，获取数组和，平均值、最大值、最小值、中位值
 * 
 * 输入参数:Lu16DataLen--数据长度，Lpu16DataPtr--待处理数据指针
 * 
 * 输出参数:LPstrArrayDataHandle--处理后数据的指针，调用时通过指针传入,输出类型为浮点型，使用时根据需要自行做数据转换
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-12-14 09:08   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PubAI_ArrayDataHandle_sint16_Type(uint16 Lu16DataLen,sint16 *Lps16DataPtr,Pub_CalAiArrayDataHandleType *LPstrArrayDataHandle)
{
    uint16 i;
    uint16 Lu16MiddleIndex;
    if(Lu16DataLen > 0)
    {
        PubAI_BubbleSortFor_sint16_Type(Lu16DataLen,Lps16DataPtr,SORT_DESCENDING);
        LPstrArrayDataHandle->fMaxValue = Lps16DataPtr[0];
        LPstrArrayDataHandle->fMinValue = Lps16DataPtr[Lu16DataLen-1];
        Lu16MiddleIndex = Lu16DataLen/2;
        LPstrArrayDataHandle->fMiddleValue = Lps16DataPtr[Lu16MiddleIndex];

        LPstrArrayDataHandle->fSumValue = 0;
        for (i = 0; i < Lu16DataLen; i++) 
        {
            LPstrArrayDataHandle->fSumValue += Lps16DataPtr[i];
        }
        LPstrArrayDataHandle->fAverageValue = LPstrArrayDataHandle->fSumValue/Lu16DataLen;
    }
    else
    {
        LPstrArrayDataHandle->fSumValue = 0;
        LPstrArrayDataHandle->fAverageValue = 0;
        LPstrArrayDataHandle->fMaxValue = 0;
        LPstrArrayDataHandle->fMinValue = 0;
        LPstrArrayDataHandle->fMiddleValue = 0;
    }
}


/******************************************************************************
 * 函数名称: PubAI_ArrayDataHandle_uint32_Type
 * 
 * 功能描述: 对一组数组进行处理，获取数组和，平均值、最大值、最小值、中位值
 * 
 * 输入参数:Lu16DataLen--数据长度，Lpu32DataPtr--待处理数据指针
 * 
 * 输出参数:LPstrArrayDataHandle--处理后数据的指针，调用时通过指针传入,输出类型为浮点型，使用时根据需要自行做数据转换
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-12-14 09:11   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PubAI_ArrayDataHandle_uint32_Type(uint16 Lu16DataLen,uint32 *Lpu32DataPtr,Pub_CalAiArrayDataHandleType *LPstrArrayDataHandle)
{
    uint16 i;
    uint16 Lu16MiddleIndex;
    if(Lu16DataLen > 0)
    {
        PubAI_BubbleSortFor_uint32_Type(Lu16DataLen,Lpu32DataPtr,SORT_DESCENDING);
        LPstrArrayDataHandle->fMaxValue = Lpu32DataPtr[0];
        LPstrArrayDataHandle->fMinValue = Lpu32DataPtr[Lu16DataLen-1];
        Lu16MiddleIndex = Lu16DataLen/2;
        LPstrArrayDataHandle->fMiddleValue = Lpu32DataPtr[Lu16MiddleIndex];

        LPstrArrayDataHandle->fSumValue = 0;
        for (i = 0; i < Lu16DataLen; i++) 
        {
            LPstrArrayDataHandle->fSumValue += Lpu32DataPtr[i];
        }
        LPstrArrayDataHandle->fAverageValue = LPstrArrayDataHandle->fSumValue/Lu16DataLen;
    }
    else
    {
        LPstrArrayDataHandle->fSumValue = 0;
        LPstrArrayDataHandle->fAverageValue = 0;
        LPstrArrayDataHandle->fMaxValue = 0;
        LPstrArrayDataHandle->fMinValue = 0;
        LPstrArrayDataHandle->fMiddleValue = 0;
    }
}


/******************************************************************************
 * 函数名称: PubAI_ArrayDataHandle_sint32_Type
 * 
 * 功能描述: 对一组数组进行处理，获取数组和，平均值、最大值、最小值、中位值
 * 
 * 输入参数:Lu16DataLen--数据长度，Lps32DataPtr--待处理数据指针
 * 
 * 输出参数:LPstrArrayDataHandle--处理后数据的指针，调用时通过指针传入,输出类型为浮点型，使用时根据需要自行做数据转换
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-12-14 09:14   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PubAI_ArrayDataHandle_sint32_Type(uint16 Lu16DataLen,sint32 *Lps32DataPtr,Pub_CalAiArrayDataHandleType *LPstrArrayDataHandle)
{
    uint16 i;
    uint16 Lu16MiddleIndex;
    if(Lu16DataLen > 0)
    {
        PubAI_BubbleSortFor_sint32_Type(Lu16DataLen,Lps32DataPtr,SORT_DESCENDING);
        LPstrArrayDataHandle->fMaxValue = Lps32DataPtr[0];
        LPstrArrayDataHandle->fMinValue = Lps32DataPtr[Lu16DataLen-1];
        Lu16MiddleIndex = Lu16DataLen/2;
        LPstrArrayDataHandle->fMiddleValue = Lps32DataPtr[Lu16MiddleIndex];

        LPstrArrayDataHandle->fSumValue = 0;
        for (i = 0; i < Lu16DataLen; i++) 
        {
            LPstrArrayDataHandle->fSumValue += Lps32DataPtr[i];
        }
        LPstrArrayDataHandle->fAverageValue = LPstrArrayDataHandle->fSumValue/Lu16DataLen;
    }
    else
    {
        LPstrArrayDataHandle->fSumValue = 0;
        LPstrArrayDataHandle->fAverageValue = 0;
        LPstrArrayDataHandle->fMaxValue = 0;
        LPstrArrayDataHandle->fMinValue = 0;
        LPstrArrayDataHandle->fMiddleValue = 0;
    }
}


/******************************************************************************
 * 函数名称: PubAI_ArrayDataHandle_float_Type
 * 
 * 功能描述: 对一组数组进行处理，获取数组和，平均值、最大值、最小值、中位值
 * 
 * 输入参数:Lu16DataLen--数据长度，LpfDataPtr--待处理数据指针
 * 
 * 输出参数:LPstrArrayDataHandle--处理后数据的指针，调用时通过指针传入 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2022-12-14 08:38   V0.1      AntonyFang   初次发布
 ******************************************************************************/
void PubAI_ArrayDataHandle_float_Type(uint16 Lu16DataLen,float *LpfDataPtr,Pub_CalAiArrayDataHandleType *LPstrArrayDataHandle)
{
    uint16 i;
    uint16 Lu16MiddleIndex;
    if(Lu16DataLen > 0)
    {
        PubAI_BubbleSortFor_float_Type(Lu16DataLen,LpfDataPtr,SORT_DESCENDING);
        LPstrArrayDataHandle->fMaxValue = LpfDataPtr[0];
        LPstrArrayDataHandle->fMinValue = LpfDataPtr[Lu16DataLen-1];
        Lu16MiddleIndex = Lu16DataLen/2;
        LPstrArrayDataHandle->fMiddleValue = LpfDataPtr[Lu16MiddleIndex];

        LPstrArrayDataHandle->fSumValue = 0;
        for (i = 0; i < Lu16DataLen; i++) 
        {
            LPstrArrayDataHandle->fSumValue += LpfDataPtr[i];
        }
        LPstrArrayDataHandle->fAverageValue = LPstrArrayDataHandle->fSumValue/Lu16DataLen;
    }
    else
    {
        LPstrArrayDataHandle->fSumValue = 0;
        LPstrArrayDataHandle->fAverageValue = 0;
        LPstrArrayDataHandle->fMaxValue = 0;
        LPstrArrayDataHandle->fMinValue = 0;
        LPstrArrayDataHandle->fMiddleValue = 0;
    }
}





/******************************************************************************
 * 函数名称: PubAI_CalPoint2LineRelation
 * 
 * 功能描述: 计算一个点和另外两个点构成直线之间的关系；其中P1点是起始点，P2点是终止点，P3是待计算相对于P1P2关系的点；
 *           此处左右的相对视角是以P1点指向P2点来看的。
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:LenuPoint2Line--返回P3点相对于P1 P2点的关系
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-02-16 20:18   V0.1      AntonyFang   初次发布
 ******************************************************************************/
Pub_Point2LineType PubAI_CalPoint2LineRelation(float P1_X,float P1_Y,float P2_X,float P2_Y,float P3_X,float P3_Y)
{
    Pub_Point2LineType LenuPoint2Line;
    float LfVectorP1_P2_X;
    float LfVectorP1_P2_Y;
    float LfVectorP1_P3_X;
    float LfVectorP1_P3_Y;
    float LfCrossMultiply;

    LfVectorP1_P2_X = P2_X - P1_X;
    LfVectorP1_P2_Y = P2_Y - P1_Y;
    
    LfVectorP1_P3_X = P3_X - P1_X;
    LfVectorP1_P3_Y = P3_Y - P1_Y;
    LfCrossMultiply = LfVectorP1_P2_X*LfVectorP1_P3_Y;
    LfCrossMultiply = LfCrossMultiply - (LfVectorP1_P2_Y*LfVectorP1_P3_X);

    if(LfCrossMultiply > 0)
    {
        LenuPoint2Line = PUB_POINT_ON_LINE_LEFT;
    }
    else if(LfCrossMultiply < 0)
    {
        LenuPoint2Line = PUB_POINT_ON_LINE_RIGHT;
    }
    else
    {
        LenuPoint2Line = PUB_POINT_ON_LINE;
    }

    return LenuPoint2Line;
}


/******************************************************************************
 * 函数名称: PubAI_CalOnePointToOtherPointDis
 * 
 * 功能描述: 已知三个点坐标，求其中一点到另外两个点组成直线的距离(对应于该公式的点(X0,Y0)到(X1,Y1)、(X2,Y2)组成直线的距离)
 * 
 * 输入参数:LfX0--第一个点的X坐标；LfY0--第一个点的Y坐标；也就是待求点到直线距离的点的坐标
 *          LfX1--直线第一个点的X坐标；LfY1--直线第一个点的Y坐标；LfX2--直线第二个点的X坐标；LfY2--直线第二个点的Y坐标；
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:先求直线：已知两点坐标(x1,y1)，(x2,y2)则直线方程为(x-x1)/(x2-x1)=(y-y1)/(y2-y1)；化简得(x2-x1)*x+(y2-y1)*y+y1*(y2-y1)-x1*(x2-x1)=0 
 *          再求点到直线距离，设直线方程为A*x+B*y+C=0, 点P坐标为(x0,y0)则点P到直线的距离为D=  |A*x0+B*y0+C|/(√(A^2 )+B^2)
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-03-24 10:33   V0.1      AntonyFang   初次发布
 ******************************************************************************/
float PubAI_CalOnePointToOtherPointDis(float LfX0,float LfY0,float LfX1,float LfY1,float LfX2,float LfY2)
{
    float LfDis;
    float LfA,LfB,LfC;
    float LfDis1,LfDis2;
    
    LfA = LfY2 - LfY1;
    LfB = LfX1 - LfX2;
    LfDis1 = LfY1*(LfX2 - LfX1);
    LfDis2 = LfX1*(LfY2 - LfY1);
    LfC = LfDis1 - LfDis2;

    LfDis1 = LfA*LfX0 + LfB*LfY0 + LfC;
    LfDis1 = ABS_VALUE(LfDis1);
    
    LfDis2 = LfA*LfA+LfB*LfB;
    LfDis2 = powf(LfDis2,0.5);

    LfDis = LfDis1/LfDis2;
    return LfDis;
}


/******************************************************************************
 * 函数名称: PubAI_CalOnePointToSegmentDis
 * 
 * 功能描述: 计算一个点到另外两个点构成线段的距离，分为三种情况；
 *           1.点在线段(X1,Y1)这一侧外边，此时点到线段的距离即为点到(X1,Y1)的距离
 *           2.点在线段(X2,Y2)这一侧外边，此时点到线段的距离即为点到(X2,Y2)的距离
 *           3.点在线段中间位置，此时点到线段的距离即为点到(X1,Y1)和(X2,Y2)构成的直线的距离
 * 
 * 输入参数:P0点坐标(LfX0,LfY0);线段起始点P1坐标(LfX1,LfY1);线段终止点P2坐标(LfX2,LfY2);
 *          目的是求P0点到线段P1 P2的距离
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-04-25 08:32   V0.1      AntonyFang   初次发布
 ******************************************************************************/
float PubAI_CalOnePointToSegmentDis(float LfX0,float LfY0,float LfX1,float LfY1,float LfX2,float LfY2)
{
    Pub_CalAiPointType LstrVectorP1_P0;
    Pub_CalAiPointType LstrVectorP1_P2;
    float LfVectorP1_P0_DotProduct_P1_P2;
    float LfVectorP1_P2_Square;
    float LfRadius;
    float LfPointToSegmentDis;

    LstrVectorP1_P0.fPoint_X = LfX0 - LfX1;
    LstrVectorP1_P0.fPoint_Y = LfY0 - LfY1;

    LstrVectorP1_P2.fPoint_X = LfX2 - LfX1;
    LstrVectorP1_P2.fPoint_Y = LfY2 - LfY1;

    LfVectorP1_P0_DotProduct_P1_P2 = (LstrVectorP1_P0.fPoint_X*LstrVectorP1_P2.fPoint_X)+(LstrVectorP1_P0.fPoint_Y*LstrVectorP1_P2.fPoint_Y);
    LfVectorP1_P2_Square = (LstrVectorP1_P2.fPoint_X*LstrVectorP1_P2.fPoint_X)+(LstrVectorP1_P2.fPoint_Y*LstrVectorP1_P2.fPoint_Y);
    LfRadius = LfVectorP1_P0_DotProduct_P1_P2/LfVectorP1_P2_Square;

    if(LfRadius < 0.0)
    {
        LfPointToSegmentDis = PubAI_CalTwoPointDis(LfX0,LfY0,LfX1,LfY1);
    }
    else if(LfRadius > 1.0)
    {
        LfPointToSegmentDis = PubAI_CalTwoPointDis(LfX0,LfY0,LfX2,LfY2);
    }
    else
    {
        LfPointToSegmentDis = PubAI_CalOnePointToOtherPointDis(LfX0,LfY0,LfX1,LfY1,LfX2,LfY2);
    }

    return LfPointToSegmentDis;
}


/******************************************************************************
 * 函数名称: PubAI_CalOnePointToSegmentDisAndRelation
 * 
 * 功能描述: 计算一个点到另外两个点构成线段的距离,并返回关系
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2024-07-09 10:21   V0.1      AntonyFang   初次发布
 ******************************************************************************/
float PubAI_CalOnePointToSegmentDisAndRelation(float LfX0,float LfY0,float LfX1,float LfY1,float LfX2,float LfY2,uint8* Lpu8PointInMiddleFlag)
{
    uint8 Lu8PointInMiddleFlag = 0;
    Pub_CalAiPointType LstrVectorP1_P0;
    Pub_CalAiPointType LstrVectorP1_P2;
    float LfVectorP1_P0_DotProduct_P1_P2;
    float LfVectorP1_P2_Square;
    float LfRadius;
    float LfPointToSegmentDis;

    LstrVectorP1_P0.fPoint_X = LfX0 - LfX1;
    LstrVectorP1_P0.fPoint_Y = LfY0 - LfY1;

    LstrVectorP1_P2.fPoint_X = LfX2 - LfX1;
    LstrVectorP1_P2.fPoint_Y = LfY2 - LfY1;

    LfVectorP1_P0_DotProduct_P1_P2 = (LstrVectorP1_P0.fPoint_X*LstrVectorP1_P2.fPoint_X)+(LstrVectorP1_P0.fPoint_Y*LstrVectorP1_P2.fPoint_Y);
    LfVectorP1_P2_Square = (LstrVectorP1_P2.fPoint_X*LstrVectorP1_P2.fPoint_X)+(LstrVectorP1_P2.fPoint_Y*LstrVectorP1_P2.fPoint_Y);
    LfRadius = LfVectorP1_P0_DotProduct_P1_P2/LfVectorP1_P2_Square;

    if(LfRadius < 0.0)
    {
        Lu8PointInMiddleFlag = 0;
        LfPointToSegmentDis = PubAI_CalTwoPointDis(LfX0,LfY0,LfX1,LfY1);
    }
    else if(LfRadius > 1.0)
    {
        Lu8PointInMiddleFlag = 0;
        LfPointToSegmentDis = PubAI_CalTwoPointDis(LfX0,LfY0,LfX2,LfY2);
    }
    else
    {
        Lu8PointInMiddleFlag = 1;
        LfPointToSegmentDis = PubAI_CalOnePointToOtherPointDis(LfX0,LfY0,LfX1,LfY1,LfX2,LfY2);
    }
    *Lpu8PointInMiddleFlag = Lu8PointInMiddleFlag;

    return LfPointToSegmentDis;
}



/******************************************************************************/
/**
* @brief: 滤波函数
**/
/******************************************************************************/
void PubAI_Filter(Pub_FilterCtrl_t *pCtrl)
{
	if(TRUE == pCtrl->Input)
	{
		pCtrl->Count += pCtrl->UpStep;
		if(pCtrl->Count > pCtrl->UpperLimit)
		{
			pCtrl->Count = pCtrl->UpperLimit;
		}
	}
	else
	{
		if(pCtrl->Count >= pCtrl->DownStep)
		{
			pCtrl->Count -= pCtrl->DownStep;
		}
		else
		{
			pCtrl->Count = pCtrl->LowerLimit;
		}
	}

	if(pCtrl->Count == pCtrl->UpperLimit)
	{
		pCtrl->Output = TRUE; 
	}
	else if(pCtrl->Count == pCtrl->LowerLimit)
	{
		pCtrl->Output = FALSE;
	}
	else
	{}
}

