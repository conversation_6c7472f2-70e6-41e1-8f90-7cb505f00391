/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * Elmos17SnsMeasParam.c:
 * Created on: 2021-12-27 15:04
 * Original designer:
 ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "Elmos17SnsMeasParamType.h"
#include "Elmos_524_17_Private.h"
#include "Queue_CRMResponse.h"
#include "Elmos17SnsMeasParamCfg.h"
#include "AK2_MCU_Drv.h"

typedef enum
{
    eWRSnsMeasParamStep_None = 0,
    eTransWriteAddReq ,
    eHandleWriteAddReqResp,
    eTransWriteData,
    eHandleWriteDataResp,
    
    eTransReadAddReq,
    eHandleReadAddResp,

    eTransReadStatusReq,
    eHandleReadStatusResp,

    
}WRSnsMeasParamStep_e;


typedef struct
{
    WRSnsMeasParam_str InParam;
    
    DSIMasterID_en DSIMasterID;
    DSIChlSel_en DSIChlSEL;
    WRSnsMeasParamStep_e WRSnsMeasParamStep;

    uint8 WRu16Cnt;
    uint8 TriggerFlg;
    WRSnsMeasParamStatus_e Status;
}WRSnsMeasParamCtrl_Str;

WRSnsMeasParamCtrl_Str WRSnsMeasParamCtrl[DSIMasterNum];

Elmos17RetType_en InitWRSnsMeasParam_str(WRSnsMeasParam_str *pWRSnsMeasParam)
{
    if(pWRSnsMeasParam == NULL)
    {
        return eRet_InDat_ERR;
    }
    memset(pWRSnsMeasParam,0,sizeof(WRSnsMeasParam_str));
    return eRet_OK;
}
Elmos17RetType_en TriggerWRSnsMsg(DSIMasterID_en LeDSIMasterID,SnsCtrlGroup_Cfg *pSnsCtrlGroup,WRSnsMeasParam_str *pWRSnsMeasParam)
{
    WRSnsMeasParamCtrl_Str *pWRSnsMeasParamCtrl;
    DSIMasterID_en DSIMasterID = LeDSIMasterID;
	
    pWRSnsMeasParamCtrl = &WRSnsMeasParamCtrl[DSIMasterID];
   
    if((pWRSnsMeasParam == NULL )
        || ((pWRSnsMeasParam->pInOutDataBuf == NULL) && (pWRSnsMeasParam->eWRFlg != eRALLSnsSelfTestResult))
        || (pWRSnsMeasParam->pResultflg == NULL))
    {
        return eRet_InDat_ERR;
    }
    if(((pWRSnsMeasParamCtrl->Status != WRSnsMeasParamStatus_IDLE ) && (pWRSnsMeasParamCtrl->Status != WRSnsMeasParamStatus_DONE ))
      ||(pSnsCtrlGroup->GroupStatus == GroupStatus_READY_MEAS) 
      /*||(pSnsCtrlGroup->GroupStatus == GroupStatus_BUSY_MEAS)*/)
    {
        return eRet_N_OK;
    }
    else
    {
        
        pSnsCtrlGroup->GroupStatus = GroupStatus_BUSY_RWSNSStatus;
        pWRSnsMeasParamCtrl->TriggerFlg = 1;
        memcpy(&pWRSnsMeasParamCtrl->InParam,pWRSnsMeasParam,sizeof(WRSnsMeasParam_str));
    }
    return eRet_OK;
}



Elmos17RetType_en AbortWRMeasParam(DSIMasterID_en LeDSIMasterID)
{
    WRSnsMeasParamCtrl_Str *pWRSnsMeasParamCtrl;
    
    pWRSnsMeasParamCtrl = &WRSnsMeasParamCtrl[LeDSIMasterID];
   
    if((pWRSnsMeasParamCtrl->Status != WRSnsMeasParamStatus_IDLE ) && (pWRSnsMeasParamCtrl->Status != WRSnsMeasParamStatus_DONE ))
    {
        pWRSnsMeasParamCtrl->Status = WRSnsMeasParamStatus_ABORT;
        
    }
    else
    {

    }
    return eRet_OK;
}

WRSnsMeasParamStatus_e GetWRSnsMeasParamStatus(DSIMasterID_en LeDSIMasterID)
{
    return WRSnsMeasParamCtrl[LeDSIMasterID].Status;
}

SnsID_en GetWRSnsMeasParamSnsID(DSIMasterID_en LeDSIMasterID)
{
	return WRSnsMeasParamCtrl[LeDSIMasterID].InParam.SnsID; 
}

void PrintfCRMRes(CRM_RESPONSE_Data_str *pResOutData)
{
   
    PRINTF_ELMOS17Param("CRM Resp DSIChlID %d \n\r",pResOutData->DSIChlID);
    
    PRINTF_ELMOS17Param("DSIStat 0x%x SymbolCnt %d \n\r",pResOutData->ResData.DSIStatus.Status,pResOutData->ResData.DSIStatus.SymbolCount);
    
    PRINTF_ELMOS17Param("PhysAddr %d Status 0x%x Dat[0] 0x%x Dat[1] 0x%x CRC 0x%x \n\r", \
        (pResOutData->ResData.Data.PhysAddr_Status >> 4),    \
        (pResOutData->ResData.Data.PhysAddr_Status & 0x0F),  \
        pResOutData->ResData.Data.Data[0], \
        pResOutData->ResData.Data.Data[1],\
        pResOutData->ResData.Data.DSICRC); \
    
}



uint8 WRStep = 0;
uint32 TestWRErrCntSeq[15];
uint32 TestRStatusErrCntSeq[DSIMasterNum][DSIChlNum][DSISlaveSnsNum];

uint8 WRSnsMeasParam_WriteParam(WRSnsMeasParamCtrl_Str *pWRCtrl,DSIMasterID_en DSIMasterID,uint8 DSIIDLESts,DSIChlSel_en DSIChlSel)
{
	uint8 RetFlg = 1;
	uint16 Flg = 0xFFFF;
	DSIChlID_en DSIChlIdx = DSIChl1;
    CRM_RESPONSE_Data_str ResOutData;
	
	if(pWRCtrl->WRSnsMeasParamStep == eTransWriteAddReq)
	{
		/** @brief DSI3 IDLE */
		if((DSIIDLESts & DSIChlSel) == DSIChlSel)
		{
			/** @brief Meas Write addr Req */
			Flg = TransElmos17WriteMeasCfgReq(pWRCtrl->InParam.SnsID,pWRCtrl->InParam.ElmosMeasParam_StartAddr + pWRCtrl->WRu16Cnt * 2);
			
			if(Flg != 0xFF)
			{
				pWRCtrl->WRSnsMeasParamStep = eHandleWriteAddReqResp;
				PRINTF_ELMOS17Param("SnsID :%d eTransWriteAddReq \n\r",pWRCtrl->InParam.SnsID);
			}
			else
			{
				*pWRCtrl->InParam.pResultflg |= 0x08;
				if(pWRCtrl->InParam.WRSnsMeasParamCallBak != NULL)
				{
					pWRCtrl->InParam.WRSnsMeasParamCallBak(eRSnsMeasParamStatus_TransSPI_ERR);
				}

				pWRCtrl->Status = WRSnsMeasParamStatus_ABORT;
				TestWRErrCntSeq[0]++;
				PRINTF_ELMOS17Param("Time %d \n\r",AK2_GetSys1msTickFunc());
				PRINTF_ELMOS17Param("Trans WriteCfgReq Err %d \n\r",TestWRErrCntSeq[0]);
				RetFlg = 2;

			}
			
		}
		else
		{
		}

	}
	else if(pWRCtrl->WRSnsMeasParamStep == eHandleWriteAddReqResp)
	{
		/** @brief DSI3 IS IDLE ,READ CRM Response Trans Finish */
		if((DSIIDLESts & DSIChlSel) == DSIChlSel)
		{
			for(DSIChlIdx=DSIChl1;DSIChlIdx<DSIChlNum;DSIChlIdx++)
			{
				if((DSIChlSel >> DSIChlIdx) & 0x01)
				{
					/** @brief 获取CRM应答数据 */
					Flg = GetCRMRESData(DSIMasterID,&ResOutData);
					
					if(Flg != 0xFFFF)
					{
						/** @brief 检查应答数据是否有效 */
						if(CheckCRMResStatus(&ResOutData,GetSns_PhyAddr(pWRCtrl->InParam.SnsID)) == eRet_OK)
						{
							if(ResOutData.ResData.Data.Data[1] == (pWRCtrl->InParam.ElmosMeasParam_StartAddr + pWRCtrl->WRu16Cnt * 2))
							{
								/** @brief 应答正确 */
								Flg = TransElmos17WriteMeasCfgData(pWRCtrl->InParam.SnsID,pWRCtrl->InParam.pInOutDataBuf[pWRCtrl->WRu16Cnt]);
								
								if(Flg != 0xFF)
								{
									pWRCtrl->WRSnsMeasParamStep = eHandleWriteDataResp;
									PRINTF_ELMOS17Param("SnsID :%d eHandleWriteAddReqResp \n\r",pWRCtrl->InParam.SnsID);
								}
								else
								{
									*pWRCtrl->InParam.pResultflg |= 0x08;
									
									if(pWRCtrl->InParam.WRSnsMeasParamCallBak != NULL)
									{
										pWRCtrl->InParam.WRSnsMeasParamCallBak(eRSnsMeasParamStatus_TransSPI_ERR);
									}
									pWRCtrl->Status = WRSnsMeasParamStatus_ABORT;
									TestWRErrCntSeq[4]++;
									
									PRINTF_ELMOS17Param("TransWriteData Err %d \n\r",TestWRErrCntSeq[4]);
									RetFlg = 2;
								}

							}
							else
							{
								/** @brief 应答错误 */
								*pWRCtrl->InParam.pResultflg |= 0x01;
								
								if(pWRCtrl->InParam.WRSnsMeasParamCallBak != NULL)
								{
									pWRCtrl->InParam.WRSnsMeasParamCallBak(eRSnsMeasParamStatus_SnsResp_ERR);
								}
								pWRCtrl->Status = WRSnsMeasParamStatus_ABORT;
								TestWRErrCntSeq[1]++;
								
								PRINTF_ELMOS17Param("WriteCfgReq Resp WAddr Err :%d \n\r",TestWRErrCntSeq[1]);
								PRINTF_ELMOS17Param("SnsID :%d \n\r",pWRCtrl->InParam.SnsID);
								PrintfCRMRes(&ResOutData);
								PRINTF_ELMOS17Param("expect Data[1] %d	Resp %d\n\r",(pWRCtrl->InParam.ElmosMeasParam_StartAddr + pWRCtrl->WRu16Cnt * 2),ResOutData.ResData.Data.Data[1]);
								RetFlg = 2;

							}
						}
						else
						{
							/** @brief 应答错误 */
							*pWRCtrl->InParam.pResultflg |= 0x02;
							if(pWRCtrl->InParam.WRSnsMeasParamCallBak != NULL)
							{
								pWRCtrl->InParam.WRSnsMeasParamCallBak(eRSnsMeasParamStatus_SnsResp_ERR);
							}

							pWRCtrl->Status = WRSnsMeasParamStatus_ABORT;
							TestWRErrCntSeq[2]++;
							
							PRINTF_ELMOS17Param("WriteCfgReq Resp DSI Status Err :%d \n\r",TestWRErrCntSeq[2]);
							PRINTF_ELMOS17Param("SnsID :%d \n\r",pWRCtrl->InParam.SnsID);
							PrintfCRMRes(&ResOutData);
							RetFlg = 2;

						}
					}
					else
					{
						if(GetSns_PhyAddr(pWRCtrl->InParam.SnsID) == 0)
						{
							/** @brief 广播地址,无CRM应答 */
						}
						else
						{
							/** @brief 非广播地址 */
							*pWRCtrl->InParam.pResultflg |= 0x04;
							
							if(pWRCtrl->InParam.WRSnsMeasParamCallBak != NULL)
							{
								pWRCtrl->InParam.WRSnsMeasParamCallBak(eRSnsMeasParamStatus_Sns_NoResp_ERR);
							}
							
							pWRCtrl->Status = WRSnsMeasParamStatus_ABORT;
							TestWRErrCntSeq[3]++;
							
							PRINTF_ELMOS17Param("SnsID :%d \n\r",pWRCtrl->InParam.SnsID);
							PRINTF_ELMOS17Param("WriteCfgReq NResp	\n\r");
							RetFlg = 2;

						}
					}

				
				}
			}
			

		}
		else
		{

		}
	}
	else if(pWRCtrl->WRSnsMeasParamStep == eHandleWriteDataResp)
	{
		/** @brief DSI3 IS IDLE ,Trans Finish */
		if((DSIIDLESts & DSIChlSel) == DSIChlSel)
		{
			
			for(DSIChlIdx=DSIChl1;DSIChlIdx<DSIChlNum;DSIChlIdx++)
			{
				if((DSIChlSel >> DSIChlIdx) & 0x01)
				{
					/** @brief 获取CRM应答数据 */
					Flg = GetCRMRESData(DSIMasterID,&ResOutData);
					
					if(Flg != 0xFFFF)
					{
						/** @brief 检查应答数据是否有效 */
						if(CheckCRMResStatus(&ResOutData,GetSns_PhyAddr(pWRCtrl->InParam.SnsID)) == eRet_OK)
						{
							if (((ResOutData.ResData.Data.Data[0] << 8) | ResOutData.ResData.Data.Data[1]) == pWRCtrl->InParam.pInOutDataBuf[pWRCtrl->WRu16Cnt])
							{
								/** @brief 应答正确 */
								PRINTF_ELMOS17Param("SnsID :%d eHandleWriteDataResp \n\r",pWRCtrl->InParam.SnsID);

							}
							else
							{
								/** @brief 应答错误 */
								*pWRCtrl->InParam.pResultflg |= 0x01;
								
								if(pWRCtrl->InParam.WRSnsMeasParamCallBak != NULL)
								{
									pWRCtrl->InParam.WRSnsMeasParamCallBak(eRSnsMeasParamStatus_SnsResp_ERR);
								}
								
								pWRCtrl->Status = WRSnsMeasParamStatus_ABORT;
								TestWRErrCntSeq[5]++;
								RetFlg = 2;

							}
						}
						else
						{
							/** @brief 应答错误 */
							*pWRCtrl->InParam.pResultflg |= 0x02;
							
							if(pWRCtrl->InParam.WRSnsMeasParamCallBak != NULL)
							{
								pWRCtrl->InParam.WRSnsMeasParamCallBak(eRSnsMeasParamStatus_SnsResp_ERR);
							}
							
							pWRCtrl->Status = WRSnsMeasParamStatus_ABORT;
							TestWRErrCntSeq[6]++;
							RetFlg = 2;
							PRINTF_ELMOS17Param("WriteBUSY Check Resp DSI Status Err :%d \n\r",TestWRErrCntSeq[2]);
							PRINTF_ELMOS17Param("SnsID :%d \n\r",pWRCtrl->InParam.SnsID);
							PrintfCRMRes(&ResOutData);

							
						}
					}
					else
					{
						if(GetSns_PhyAddr(pWRCtrl->InParam.SnsID) == 0)
						{
							/** @brief 广播地址,无CRM应答 */
						}
						else
						{
							/** @brief 非广播地址 */
							*pWRCtrl->InParam.pResultflg |= 0x04;
							
							if(pWRCtrl->InParam.WRSnsMeasParamCallBak != NULL)
							{
								pWRCtrl->InParam.WRSnsMeasParamCallBak(eRSnsMeasParamStatus_Sns_NoResp_ERR);
							}
							
							pWRCtrl->Status = WRSnsMeasParamStatus_ABORT;
							TestWRErrCntSeq[7]++;
							RetFlg = 2;
							PRINTF_ELMOS17Param("WriteCfgDataReq NResp	\n\r");

							

						}
					}
				}
			}
			
			
			pWRCtrl->WRu16Cnt ++;
			if(pWRCtrl->WRu16Cnt >= pWRCtrl->InParam.u16Len)
			{
				
				pWRCtrl->Status = WRSnsMeasParamStatus_DONE;
				
				pWRCtrl->WRSnsMeasParamStep = eWRSnsMeasParamStep_None;
				PRINTF_ELMOS17Param("SnsID :%d write success \n\r",pWRCtrl->InParam.SnsID);
			}
			else
			{

				pWRCtrl->WRSnsMeasParamStep = eTransWriteAddReq;
			}
		}
		else
		{

		}
	}
	else
	{
	}
	
	return RetFlg;
}

uint8 WRSnsMeasParam_ReadParam(WRSnsMeasParamCtrl_Str *pWRCtrl,DSIMasterID_en DSIMasterID,uint8 DSIIDLESts,DSIChlSel_en DSIChlSel)
{
	uint8 RetFlg = 1;
	uint16 Flg = 0xFFFF;
	DSIChlID_en DSIChlIdx = DSIChl1;
    CRM_RESPONSE_Data_str ResOutData;
	
	if(pWRCtrl->WRSnsMeasParamStep == eTransReadAddReq)
	{
		/** @brief DSI3 IS IDLE */
		if((DSIIDLESts & DSIChlSel) == DSIChlSel)
		{
			Flg = TransElmos17ReadMeasCfgReq(pWRCtrl->InParam.SnsID,pWRCtrl->InParam.ElmosMeasParam_StartAddr + pWRCtrl->WRu16Cnt * 2);
			
			if(Flg != 0xFF)
			{
				pWRCtrl->WRSnsMeasParamStep = eHandleReadAddResp;
			}
			else
			{
				*pWRCtrl->InParam.pResultflg |= 0x08;
				
				if(pWRCtrl->InParam.WRSnsMeasParamCallBak != NULL)
				{
					pWRCtrl->InParam.WRSnsMeasParamCallBak(eRSnsMeasParamStatus_TransSPI_ERR);
				}
				
				pWRCtrl->Status = WRSnsMeasParamStatus_ABORT;
				TestWRErrCntSeq[8]++;
				
				PRINTF_ELMOS17Param("Trans ReadMeasCfgReq Err %d \n\r",TestWRErrCntSeq[8]);
				RetFlg = 2;
			}
		}
		else
		{
		}

	}
	else 
	{
		/** @brief DSI3 IS IDLE */
		if((DSIIDLESts & DSIChlSel) == DSIChlSel)
		{
			for(DSIChlIdx=DSIChl1;DSIChlIdx<DSIChlNum;DSIChlIdx++)
			{
				if((DSIChlSel >> DSIChlIdx) & 0x01)
				{
					/** @brief 获取CRM应答数据 */
					Flg = GetCRMRESData(DSIMasterID,&ResOutData);
					
					if(Flg != 0xFFFF)
					{
						/** @brief 检查应答数据是否有效 */
						if(CheckCRMResStatus(&ResOutData,GetSns_PhyAddr(pWRCtrl->InParam.SnsID)) == eRet_OK)
						{
							/** @brief 应答正确 */
							pWRCtrl->InParam.pInOutDataBuf[pWRCtrl->WRu16Cnt] = ResOutData.ResData.Data.Data[0] << 8 | ResOutData.ResData.Data.Data[1];
							
						}
						else
						{
							/** @brief 应答错误 */
							*pWRCtrl->InParam.pResultflg |= 0x02;
							
							if(pWRCtrl->InParam.WRSnsMeasParamCallBak != NULL)
							{
								pWRCtrl->InParam.WRSnsMeasParamCallBak(eRSnsMeasParamStatus_SnsResp_ERR);
							}
							
							pWRCtrl->Status = WRSnsMeasParamStatus_ABORT;
							TestWRErrCntSeq[9]++;
							RetFlg = 2;

						}
					}
					else
					{
						if(GetSns_PhyAddr(pWRCtrl->InParam.SnsID) == 0)
						{
							/** @brief 广播地址,无CRM应答 */
						}
						else
						{
							/** @brief 非广播地址 */
						
							if(pWRCtrl->WRu16Cnt >= pWRCtrl->InParam.u16Len)
							{
								
							}
							else
							{
								*pWRCtrl->InParam.pResultflg |= 0x04;
								
								if(pWRCtrl->InParam.WRSnsMeasParamCallBak != NULL)
								{
									pWRCtrl->InParam.WRSnsMeasParamCallBak(eRSnsMeasParamStatus_Sns_NoResp_ERR);
								}
								
								pWRCtrl->Status = WRSnsMeasParamStatus_ABORT;
								TestWRErrCntSeq[10]++;
								RetFlg = 2;
								
								PRINTF_ELMOS17Param("SnsID :%d \n\r",pWRCtrl->InParam.SnsID);
								PRINTF_ELMOS17Param("ReadCfgReq NResp  \n\r");
							}
						}
					}
				}
			}
			
			pWRCtrl->WRu16Cnt ++;
			
			if(pWRCtrl->WRu16Cnt >= pWRCtrl->InParam.u16Len)
			{
			   
				pWRCtrl->Status = WRSnsMeasParamStatus_DONE;
				
				pWRCtrl->WRSnsMeasParamStep = eWRSnsMeasParamStep_None;
			}
			else
			{
			
				pWRCtrl->WRSnsMeasParamStep = eTransReadAddReq;
			}
		}
		else
		{

		}
	}

	return RetFlg;
}

uint8 WRSnsMeasParam_ReadSelfTestStatus(WRSnsMeasParamCtrl_Str *pWRCtrl,DSIMasterID_en DSIMasterID,uint8 DSIIDLESts,DSIChlSel_en DSIChlSel)
{
	uint8 RetFlg = 1;
	uint16 Flg = 0xFFFF;
	DSIChlID_en DSIChlIdx = DSIChl1;
    CRM_RESPONSE_Data_str ResOutData;
	uint8 LcCnt = 0;
	
	if(pWRCtrl->WRSnsMeasParamStep == eTransReadStatusReq)
	{
		/** @brief DSI3 IS IDLE */
		if((DSIIDLESts & DSIChlSel) == DSIChlSel)
		{
			Clear_SnsPDCMMsg_Status(pWRCtrl->InParam.SnsID);
			
			Flg = TransElmos17ReadStatusInPDCMReq(pWRCtrl->InParam.SnsID,0xFFFF);
			if(Flg != 0xFF)
			{
				pWRCtrl->WRSnsMeasParamStep = eHandleReadStatusResp;
			}
			else
			{
				/** @brief 请求传输错误 */
				*pWRCtrl->InParam.pResultflg |= 0x08;
				
				if(pWRCtrl->InParam.WRSnsMeasParamCallBak != NULL)
				{
					pWRCtrl->InParam.WRSnsMeasParamCallBak(eRSnsMeasParamStatus_TransSPI_ERR);
				}
				
				pWRCtrl->Status = WRSnsMeasParamStatus_ABORT;
				TestWRErrCntSeq[11]++;
				RetFlg = 2;
				PRINTF_ELMOS17Param("Trans ReadStatusInPDCMReq Err%d \n\r",TestWRErrCntSeq[11]);
			}
		}
		else
		{
		}

	}
	else 
	{				
		/*PDCM传输完成	*/
		if((DSIIDLESts & DSIChlSel) == DSIChlSel)
		{
			SnsSelfTestStatusMsg_str *pSnsSelfTestStatusMsg = NULL;
			for(DSIChlIdx = 0; DSIChlIdx < DSIChlNum; DSIChlIdx++)
			{
				for(LcCnt = 0; LcCnt < DSISlaveSnsNum; LcCnt++)
				{
					pSnsSelfTestStatusMsg = GetSnsSelfTestStatusAddr(DSIMasterID,DSIChlIdx,LcCnt);

					if(pSnsSelfTestStatusMsg != NULL)
					{
						if(pSnsSelfTestStatusMsg->RecvFrameCnt != 16 || pSnsSelfTestStatusMsg->DataUpdataFlg != 0xFFFF)
						{
							TestWRErrCntSeq[13]++;
							TestRStatusErrCntSeq[DSIMasterID][DSIChlIdx][LcCnt]++;
							*pWRCtrl->InParam.pResultflg |= 0x10;
							
							if(pWRCtrl->InParam.WRSnsMeasParamCallBak != NULL)
							{
								pWRCtrl->InParam.WRSnsMeasParamCallBak(eRSnsMeasParamStatus_SnsResp_ERR);
							}
							RetFlg = 2;
							PRINTF_ELMOS17Param("DSIMasterID %d DSIChlIdx %d SnsID %d LostFrame RecvCnt %d	\n\r",DSIMasterID,DSIChlIdx,LcCnt,pSnsSelfTestStatusMsg->RecvFrameCnt);
						}
					}
				}

			}
			
				
		   pWRCtrl->Status = WRSnsMeasParamStatus_DONE;
		   
		   pWRCtrl->WRSnsMeasParamStep = eWRSnsMeasParamStep_None;
		}
		else
		{

		} 
	}

	return RetFlg;
}

uint8 WRSnsMeasParamTask(DSIMasterID_en LeDSIMasterID)
{
    uint8 ReturnFlg = 1;
    WRSnsMeasParamCtrl_Str *pWRSnsMeasParamCtrl = NULL;
	uint8 DSIIDLEStatus = 0;
    DSIChlSel_en DSIChlSEL = DSI_CHL_NONE;
    DSIMasterID_en DSIMasterID = LeDSIMasterID;
	
    pWRSnsMeasParamCtrl = &WRSnsMeasParamCtrl[DSIMasterID];
    
    /** @brief 获取DSI 状态 */
    if(GetDSICHLWorkStatus(DSIMasterID,DSIChl1) == DSICHL_IDLE)
    {
        DSIIDLEStatus |= 0x01; 
    }
    
    if(GetDSICHLWorkStatus(DSIMasterID,DSIChl2) == DSICHL_IDLE)
    {
        DSIIDLEStatus |= 0x02; 
    }

	DSIChlSEL = pWRSnsMeasParamCtrl->DSIChlSEL;
	
    switch(pWRSnsMeasParamCtrl->Status)
    {
        case WRSnsMeasParamStatus_IDLE:
        {
            /** @brief  */
            if(pWRSnsMeasParamCtrl->TriggerFlg)
            {
                /** @brief 清CRM RES buf */
                InitCRMRESQueue(DSIMasterID);
                
                /** @brief 清读写标志 */
                pWRSnsMeasParamCtrl->TriggerFlg = 0;
                pWRSnsMeasParamCtrl->WRu16Cnt = 0;
                
                pWRSnsMeasParamCtrl->WRSnsMeasParamStep = eWRSnsMeasParamStep_None;
                
                pWRSnsMeasParamCtrl->DSIMasterID = DSIMasterID;//GetSnsDSIMasterID(pWRSnsMeasParamCtrl->InParam.SnsID);
                
                pWRSnsMeasParamCtrl->DSIChlSEL = GetSnsDSIChlSEL(pWRSnsMeasParamCtrl->InParam.SnsID);
                *pWRSnsMeasParamCtrl->InParam.pResultflg = 0;

                DSIChlSEL = pWRSnsMeasParamCtrl->DSIChlSEL;

                /** @brief 根据输入读写标志切换状态 */
                if(pWRSnsMeasParamCtrl->InParam.eWRFlg == eRSnsMeasParam)
                {
                    memset(pWRSnsMeasParamCtrl->InParam.pInOutDataBuf,0,2*pWRSnsMeasParamCtrl->InParam.u16Len);
                    pWRSnsMeasParamCtrl->Status = WRSnsMeasParamStatus_ReadParam;
                    
                    pWRSnsMeasParamCtrl->WRSnsMeasParamStep = eTransReadAddReq;

                }
                else if(pWRSnsMeasParamCtrl->InParam.eWRFlg == eWSnsMeasParam)
                {
                    pWRSnsMeasParamCtrl->Status = WRSnsMeasParamStatus_WriteParam;
                    
                    pWRSnsMeasParamCtrl->WRSnsMeasParamStep = eTransWriteAddReq;

                }
                else if(pWRSnsMeasParamCtrl->InParam.eWRFlg == eRALLSnsSelfTestResult)
                {
                    Clear_SnsSelfTestStatusMsg(pWRSnsMeasParamCtrl->InParam.SnsID);
                    
                    pWRSnsMeasParamCtrl->Status = WRSnsMeasParamStatus_ReadSelfTestStatus;
                    
                    pWRSnsMeasParamCtrl->WRSnsMeasParamStep = eTransReadStatusReq;

                }
                else  
                {

                }
            }
            else
            {
                ReturnFlg = 0;

            }
            break;
        }
      
        case WRSnsMeasParamStatus_WriteParam:
        {
			ReturnFlg = WRSnsMeasParam_WriteParam(pWRSnsMeasParamCtrl,DSIMasterID,DSIIDLEStatus,DSIChlSEL);
            break;
        }
        
        case WRSnsMeasParamStatus_ReadParam:
        {
			ReturnFlg = WRSnsMeasParam_ReadParam(pWRSnsMeasParamCtrl,DSIMasterID,DSIIDLEStatus,DSIChlSEL);
            break;
        }

        case WRSnsMeasParamStatus_ReadSelfTestStatus:
        {
            ReturnFlg = WRSnsMeasParam_ReadSelfTestStatus(pWRSnsMeasParamCtrl,DSIMasterID,DSIIDLEStatus,DSIChlSEL);
            break;
        }
 
        case WRSnsMeasParamStatus_DONE:
        {
           
            *pWRSnsMeasParamCtrl->InParam.pResultflg |= 0x80;
            
            if(pWRSnsMeasParamCtrl->InParam.WRSnsMeasParamCallBak != NULL)
            {
                pWRSnsMeasParamCtrl->InParam.WRSnsMeasParamCallBak(eRSnsMeasParamStatus_Finish);
            }
            pWRSnsMeasParamCtrl->Status = WRSnsMeasParamStatus_IDLE;
            
            ReturnFlg = 0;
            break;
        }
        
        case WRSnsMeasParamStatus_ABORT:
        {
            if((DSIIDLEStatus & DSIChlSEL) == DSIChlSEL)
            {
            
                if(pWRSnsMeasParamCtrl->InParam.WRSnsMeasParamCallBak != NULL)
                {
                    pWRSnsMeasParamCtrl->InParam.WRSnsMeasParamCallBak(eRSnsMeasParamStatus_ABORT);
                }
            
                pWRSnsMeasParamCtrl->Status = WRSnsMeasParamStatus_IDLE;
            }
            ReturnFlg = 0;
            break;
        }

		default:
		break;
        
    }
    

    return ReturnFlg;

}




#if 1

uint16 ConvertMeas_sGeneralSettingsToU16(void *pInConvertStruct ,uint16 *pOutU16Data)
{
    if(pOutU16Data == NULL || pInConvertStruct == NULL)
    {
        return 0;
    }
    Meas_sGeneralSettings *pInGeneralSettings = (Meas_sGeneralSettings *)pInConvertStruct;

    pOutU16Data[0] = pInGeneralSettings->SamplingFrequency & 0x7FFF ;        /**< BRG burst base frequency */
    
    pOutU16Data[1] = (pInGeneralSettings->TdrCurrent & 0x1F)                   \
                 |((pInGeneralSettings->AnalogGain & 0x07) << 5)            \
                 |((pInGeneralSettings->AnalogLp & 0x01) << 8)              \
                 |((pInGeneralSettings->CalibrationGain & 0x1F) << 9)  ;
                 
    pOutU16Data[2] = (pInGeneralSettings->RingFreqStart & 0x1F)                \
                 |((pInGeneralSettings->RingFreqWidth & 0x1F) << 5) ;
    
    pOutU16Data[3] = ((pInGeneralSettings->Dac1Ctrl & 0x03) << 4)              \
                 |((pInGeneralSettings->Dac0Ctrl & 0x03) << 6) ;
    
    pOutU16Data[4] = (pInGeneralSettings->UnderVoltageThreshold & 0x3FF) ;
    
    return Elmos17MEAS_GeneralSettings_u16Len;
}


uint16 ConvertU16ToMeas_sGeneralSettings(uint16 *pInU16Data ,void *pOutConvertStruct)
{
    if(pInU16Data == NULL || pOutConvertStruct == NULL)
    {
        return 0;
    }
    Meas_sGeneralSettings *pOutGeneralSettings = (Meas_sGeneralSettings *)pOutConvertStruct;

    pOutGeneralSettings->SamplingFrequency = pInU16Data[0] & 0x7FFF ;        /**< BRG burst base frequency */

    
    pOutGeneralSettings->TdrCurrent = (pInU16Data[1] & 0x1F);
    pOutGeneralSettings->AnalogGain = (pInU16Data[1] >> 5) & 0x07;
    pOutGeneralSettings->AnalogLp = (pInU16Data[1] >> 8) & 0x01;
    pOutGeneralSettings->CalibrationGain = (pInU16Data[1] >> 9) & 0x1F;

    pOutGeneralSettings->RingFreqStart = pInU16Data[2] & 0x1F;
    pOutGeneralSettings->RingFreqWidth = (pInU16Data[2] >> 5) & 0x1F;

    
    pOutGeneralSettings->Dac1Ctrl = (pInU16Data[3] >> 4) & 0x03;
    pOutGeneralSettings->Dac0Ctrl = (pInU16Data[3] >> 6) & 0x03;

    
    pOutGeneralSettings->UnderVoltageThreshold = (pInU16Data[4] & 0x03FF);
    
    return Elmos17MEAS_GeneralSettings_u16Len;
}


uint16 ConvertNoiseMeasurementToU16(void *pInConvertStruct ,uint16 *pOutU16Data)
{
    if(pOutU16Data == NULL || pInConvertStruct == NULL)
    {
        return 0;
    }
    Meas_sNoiseMeasurement *pInNoiseMeasurement = (Meas_sNoiseMeasurement *)pInConvertStruct;

    pOutU16Data[0] = (pInNoiseMeasurement->NoiseMeasTime & 0xFF)        \
                |((pInNoiseMeasurement->NoiseMeasThreshold & 0xFF) << 8)  ;
    
    pOutU16Data[1] = (pInNoiseMeasurement->NoiseMeasGain & 0x7F)                   \
                 |((pInNoiseMeasurement->NoiseMeasFilterBw & 0x07) << 7)        \
                 |((pInNoiseMeasurement->NoiseAtgThreshold & 0x3F) << 10)  ;
                 
    return Elmos17MEAS_NoiseMeasurement_u16Len;
}

uint16 ConvertU16ToNoiseMeasurement(uint16 *pInU16Data ,void *pOutConvertStruct)
{
    if(pInU16Data == NULL || pOutConvertStruct == NULL)
    {
        return 0;
    }
    Meas_sNoiseMeasurement *pOutNoiseMeasurement = (Meas_sNoiseMeasurement *)pOutConvertStruct;

    pOutNoiseMeasurement->NoiseMeasTime         = pInU16Data[0] & 0xFF;
    pOutNoiseMeasurement->NoiseMeasThreshold    = (pInU16Data[0] >> 8)& 0xFF;

    pOutNoiseMeasurement->NoiseMeasGain         = pInU16Data[1] & 0x7F;
    pOutNoiseMeasurement->NoiseMeasFilterBw     = (pInU16Data[1] >> 7) & 0x07;
    pOutNoiseMeasurement->NoiseAtgThreshold     = (pInU16Data[1] >> 10) & 0x3F;
    
                 
    return Elmos17MEAS_NoiseMeasurement_u16Len;
}

uint16 ConvStdProfStrToU16(void *pInConvertStruct ,uint16 *pOutU16Data)
{
    if(pOutU16Data == NULL || pInConvertStruct == NULL)
    {
        return 0;
    }
    Meas_sStandardPathProfile *pInStandardPathProfile = (Meas_sStandardPathProfile *)pInConvertStruct ;

    pOutU16Data[0] = pInStandardPathProfile->SpxBurstFreq & 0x7FFF ;        /**< BRG burst base frequency */
    
    pOutU16Data[1] = (pInStandardPathProfile->SpxBurstPulses & 0x7F)           \
                 |((pInStandardPathProfile->SpxMeasFilterBw & 0x07) << 7)   \
                 |((pInStandardPathProfile->SpxDs & 0x01) << 10)            \
                 |((pInStandardPathProfile->SpxFtcCfg & 0x07) << 11)        \
                 |((pInStandardPathProfile->SpxFtcEn & 0x01) << 14)   ;
                 
    pOutU16Data[2] = (pInStandardPathProfile->SpxStcGain & 0x7F)               \
                 |((pInStandardPathProfile->SpxStcMod & 0x01) << 7)         \
                 |((pInStandardPathProfile->SpxStcStart & 0xFF) << 8) ;


    pOutU16Data[3] = pInStandardPathProfile->SpxStcTb & 0xFFF;        /**< STC autoincrement time base */

    
    pOutU16Data[4] = (pInStandardPathProfile->SpxStgHeight1 & 0xFF)            \
                 |((pInStandardPathProfile->SpxStgInterval1 & 0xFF) << 8) ;

    pOutU16Data[5] = (pInStandardPathProfile->SpxStgHeight2 & 0xFF)            \
                 |((pInStandardPathProfile->SpxStgInterval2 & 0xFF) << 8) ;
    
    pOutU16Data[6] = (pInStandardPathProfile->SpxStgHeight3 & 0xFF)            \
                 |((pInStandardPathProfile->SpxStgInterval3 & 0xFF) << 8) ;

    pOutU16Data[7] = (pInStandardPathProfile->SpxStgHeight4 & 0xFF)            \
                 |((pInStandardPathProfile->SpxStgInterval4 & 0xFF) << 8) ;

    pOutU16Data[8] = (pInStandardPathProfile->SpxAtgTau & 0x07)                \
                 |((pInStandardPathProfile->SpxAtgAlpha & 0x07) << 3)       \
                 |((pInStandardPathProfile->SpxAtgIni & 0x07) << 6)         \
                 |((pInStandardPathProfile->SpxAtgCfg & 0x01) << 9) 		\
                 |((pInStandardPathProfile->SpxStgSet & 0x01) << 12) ;
    
    pOutU16Data[9] = (pInStandardPathProfile->SpxEevalSel & 0x0F)              \
                 |((pInStandardPathProfile->SpxEevalSens & 0x07) << 4)      \
                 |((pInStandardPathProfile->SpxEevalVal & 0x01) << 7)       \
                 |((pInStandardPathProfile->SpxMeasGain & 0x3F) << 8) ;
    
    pOutU16Data[10] = (pInStandardPathProfile->SpxMeasMaskDirect & 0x7F)            \
                 |((pInStandardPathProfile->SpxMeasMaskIndirect & 0x7F) << 7) ;    

    return Elmos17MEAS_STDProf_u16Len;
}

uint16 ConvU16ToStdProfStr(uint16 *pInU16Data,void *pOutConvertStruct)
{
    if(pInU16Data == NULL || pOutConvertStruct == NULL)
    {
        return 0;
    }
    Meas_sStandardPathProfile *pOutStandardPathProfile = (Meas_sStandardPathProfile *)pOutConvertStruct;

    pOutStandardPathProfile->SpxBurstFreq = pInU16Data[0] & 0x7FFF ;        /**< BRG burst base frequency */
    
    pOutStandardPathProfile->SpxBurstPulses     =  pInU16Data[1] & 0x7F;
    pOutStandardPathProfile->SpxMeasFilterBw    = (pInU16Data[1] >> 7) & 0x07;
    pOutStandardPathProfile->SpxDs              = (pInU16Data[1] >> 10) & 0x01;
    pOutStandardPathProfile->SpxFtcCfg          = (pInU16Data[1] >> 11) & 0x07;
    pOutStandardPathProfile->SpxFtcEn           = (pInU16Data[1] >> 14) & 0x01;



    
    pOutStandardPathProfile->SpxStcGain         =  pInU16Data[2] & 0x7F;
    pOutStandardPathProfile->SpxStcMod          = (pInU16Data[2] >> 7) & 0x01;
    pOutStandardPathProfile->SpxStcStart        = (pInU16Data[2] >> 8) & 0xFF;


    pOutStandardPathProfile->SpxStcTb           = pInU16Data[3] & 0xFFF;        /**< STC autoincrement time base */

    

    pOutStandardPathProfile->SpxStgHeight1      =  pInU16Data[4] & 0xFF;
    pOutStandardPathProfile->SpxStgInterval1    = (pInU16Data[4] >> 8) & 0xFF;

    pOutStandardPathProfile->SpxStgHeight2      =  pInU16Data[5] & 0xFF;
    pOutStandardPathProfile->SpxStgInterval2    = (pInU16Data[5] >> 8) & 0xFF;

    pOutStandardPathProfile->SpxStgHeight3      =  pInU16Data[6] & 0xFF;
    pOutStandardPathProfile->SpxStgInterval3    = (pInU16Data[6] >> 8) & 0xFF;

    pOutStandardPathProfile->SpxStgHeight4      =  pInU16Data[7] & 0xFF;
    pOutStandardPathProfile->SpxStgInterval4    = (pInU16Data[7] >> 8) & 0xFF;

    pOutStandardPathProfile->SpxAtgTau          =  pInU16Data[8] & 0x07;
    pOutStandardPathProfile->SpxAtgAlpha        = (pInU16Data[8] >> 3) & 0x07;
    pOutStandardPathProfile->SpxAtgIni          = (pInU16Data[8] >> 6) & 0x07;
    pOutStandardPathProfile->SpxAtgCfg          = (pInU16Data[8] >> 9) & 0x01;
	pOutStandardPathProfile->SpxStgSet			= (pInU16Data[8] >> 12) & 0x01;
		
    pOutStandardPathProfile->SpxEevalSel        =  pInU16Data[9] & 0x0F;
    pOutStandardPathProfile->SpxEevalSens       = (pInU16Data[9] >> 4) & 0x07;
    pOutStandardPathProfile->SpxEevalVal        = (pInU16Data[9] >> 7) & 0x01;
    pOutStandardPathProfile->SpxMeasGain        = (pInU16Data[9] >> 8) & 0x3F;
    
    pOutStandardPathProfile->SpxMeasMaskDirect  =  pInU16Data[10] & 0x07F;
    pOutStandardPathProfile->SpxMeasMaskIndirect= (pInU16Data[10] >> 7) & 0x7f;
    

    return Elmos17MEAS_STDProf_u16Len;
}


uint16 ConvStdProf_SEG_ToU16(void *pInConvertStruct ,uint16 *pOutU16Data)
{
    if(pOutU16Data == NULL || pInConvertStruct == NULL)
    {
        return 0;
    }
    Meas_sStandardPathProfile_STC_STG *pInStandardPathSTC_STGProfile = (Meas_sStandardPathProfile_STC_STG *)pInConvertStruct;

    pOutU16Data[0] = pInStandardPathSTC_STGProfile->SpxBurstFreq & 0x7FFF ;        /**< BRG burst base frequency */
    
    pOutU16Data[1] = (pInStandardPathSTC_STGProfile->SpxBurstPulses & 0x7F)          \
                |((pInStandardPathSTC_STGProfile->SpxMeasFilterBw & 0x07) << 7)   \
                |((pInStandardPathSTC_STGProfile->SpxDs & 0x01) << 10)            \
                |((pInStandardPathSTC_STGProfile->SpxFtcCfg & 0x07) << 11)        \
                |((pInStandardPathSTC_STGProfile->SpxFtcEn & 0x01) << 14)
                |((0x01) << 15)   ;                                                    /**< STC SET  1:use Step STC */

    pOutU16Data[2] = (pInStandardPathSTC_STGProfile->SpxStcSeg_Pos0 & 0x07)            \
                |((pInStandardPathSTC_STGProfile->SpxStcSeg_Pos1 & 0x07) << 3 )     \
                |((pInStandardPathSTC_STGProfile->SpxStcSeg_Pos2 & 0x07) << 6 )     \
                |((pInStandardPathSTC_STGProfile->SpxStcSeg_Pos3 & 0x07) << 9 ) 
                |((pInStandardPathSTC_STGProfile->SpxStcSeg_Pos4 & 0x07) << 12) ;


    pOutU16Data[3] = (pInStandardPathSTC_STGProfile->SpxStcSeg_Gain1 & 0x0F)             \
                |((pInStandardPathSTC_STGProfile->SpxStcSeg_Gain2 & 0x0F) << 4 )      \
                |((pInStandardPathSTC_STGProfile->SpxStcSeg_Gain3 & 0x0F) << 8 )      \
                |((pInStandardPathSTC_STGProfile->SpxStcSeg_Gain4 & 0x0F) << 12) ;

    
    pOutU16Data[4] = (pInStandardPathSTC_STGProfile->SpxStgSeg_Height1   & 0x0F)          \
                |((pInStandardPathSTC_STGProfile->SpxStgSeg_Interval1 & 0x0F) << 4 ) 
                |((pInStandardPathSTC_STGProfile->SpxStgSeg_Height2   & 0x0F) << 8 )   \
                |((pInStandardPathSTC_STGProfile->SpxStgSeg_Interval2 & 0x0F) << 12) ;

    pOutU16Data[5] = (pInStandardPathSTC_STGProfile->SpxStgSeg_Height3   & 0x0F)          \
                |((pInStandardPathSTC_STGProfile->SpxStgSeg_Interval3 & 0x0F) << 4 ) 
                |((pInStandardPathSTC_STGProfile->SpxStgSeg_Height4   & 0x0F) << 8 )   \
                |((pInStandardPathSTC_STGProfile->SpxStgSeg_Interval4 & 0x0F) << 12) ;
    
    pOutU16Data[6] = (pInStandardPathSTC_STGProfile->SpxStgSeg_Height5   & 0x0F)          \
                |((pInStandardPathSTC_STGProfile->SpxStgSeg_Interval5 & 0x0F) << 4 ) 
                |((pInStandardPathSTC_STGProfile->SpxStgSeg_Height6   & 0x0F) << 8 )   \
                |((pInStandardPathSTC_STGProfile->SpxStgSeg_Interval6 & 0x0F) << 12) ;

    pOutU16Data[7] = (pInStandardPathSTC_STGProfile->SpxStgSeg_Height7   & 0x0F)          \
                |((pInStandardPathSTC_STGProfile->SpxStgSeg_Interval7 & 0x0F) << 4 )   \
                |((pInStandardPathSTC_STGProfile->SpxStgSeg_Height8   & 0x0F) << 8 )   \
                |((pInStandardPathSTC_STGProfile->SpxStgSeg_Interval8 & 0x0F) << 12) ;
    
    pOutU16Data[8] = (pInStandardPathSTC_STGProfile->SpxAtgTau & 0x07)               \
                |((pInStandardPathSTC_STGProfile->SpxAtgAlpha & 0x07) << 3)       \
                |((pInStandardPathSTC_STGProfile->SpxAtgIni & 0x07) << 6)         \
                |((pInStandardPathSTC_STGProfile->SpxAtgCfg & 0x01) << 9)         \
                |((pInStandardPathSTC_STGProfile->SPxSTG_recv_Thres_DS & 0x03) << 10) \
                |((pInStandardPathSTC_STGProfile->SPxSTG_recv_Thres_DS_EN & 0x01) << 13)\
                |((0x01) << 12)  ;/**< STG SET  1:use 8 Step STG */
    
    
    pOutU16Data[9] = (pInStandardPathSTC_STGProfile->SpxEevalSel & 0x0F)              \
                |((pInStandardPathSTC_STGProfile->SpxEevalSens & 0x07) << 4)      \
                |((pInStandardPathSTC_STGProfile->SpxEevalVal & 0x01) << 7)       \
                |((pInStandardPathSTC_STGProfile->SpxMeasGain & 0x3F) << 8) ;
    
    pOutU16Data[10] = (pInStandardPathSTC_STGProfile->SpxMeasMaskDirect & 0x7F)            \
                |((pInStandardPathSTC_STGProfile->SpxMeasMaskIndirect & 0x7F) << 7) ;    

    return Elmos17MEAS_STDProf_u16Len;
}

uint16 ConvertU16ToStdProf_SEG(uint16 *pInU16Data,void *pOutConvertStruct)
{
    if(pInU16Data == NULL || pOutConvertStruct == NULL)
    {
        return 0;
    }
    Meas_sStandardPathProfile_STC_STG *pOutStandardPathProfileSTC_STG = (Meas_sStandardPathProfile_STC_STG *)pOutConvertStruct;

    pOutStandardPathProfileSTC_STG->SpxBurstFreq = pInU16Data[0] & 0x7FFF ;        /**< BRG burst base frequency */
    
    pOutStandardPathProfileSTC_STG->SpxBurstPulses     =  pInU16Data[1] & 0x7F;
    pOutStandardPathProfileSTC_STG->SpxMeasFilterBw    = (pInU16Data[1] >> 7) & 0x07;
    pOutStandardPathProfileSTC_STG->SpxDs              = (pInU16Data[1] >> 10) & 0x01;
    pOutStandardPathProfileSTC_STG->SpxFtcCfg          = (pInU16Data[1] >> 11) & 0x07;
    pOutStandardPathProfileSTC_STG->SpxFtcEn           = (pInU16Data[1] >> 14) & 01;
    pOutStandardPathProfileSTC_STG->SpxStcSet          = (pInU16Data[1] >> 15) & 01;    /**< STC SET  1:use Step STC */

    
    pOutStandardPathProfileSTC_STG->SpxStcSeg_Pos0        =  pInU16Data[2] & 0x07;
    pOutStandardPathProfileSTC_STG->SpxStcSeg_Pos1        = (pInU16Data[2] >> 3) & 0x07;
    pOutStandardPathProfileSTC_STG->SpxStcSeg_Pos2        = (pInU16Data[2] >> 6) & 0x07;
    pOutStandardPathProfileSTC_STG->SpxStcSeg_Pos3        = (pInU16Data[2] >> 9) & 0x07;
    pOutStandardPathProfileSTC_STG->SpxStcSeg_Pos4        = (pInU16Data[2] >> 12) & 0x07;
    
    pOutStandardPathProfileSTC_STG->SpxStcSeg_Gain1       =  pInU16Data[3] & 0x0F;
    pOutStandardPathProfileSTC_STG->SpxStcSeg_Gain2       = (pInU16Data[3] >> 4) & 0x0F;
    pOutStandardPathProfileSTC_STG->SpxStcSeg_Gain3       = (pInU16Data[3] >> 8) & 0x0F;
    pOutStandardPathProfileSTC_STG->SpxStcSeg_Gain4       = (pInU16Data[3] >> 12) & 0x0F;
    
    pOutStandardPathProfileSTC_STG->SpxStgSeg_Height1     =  pInU16Data[4] & 0x0F;
    pOutStandardPathProfileSTC_STG->SpxStgSeg_Interval1   = (pInU16Data[4] >> 4) & 0x0F;
    pOutStandardPathProfileSTC_STG->SpxStgSeg_Height2     = (pInU16Data[4] >> 8) & 0x0F;
    pOutStandardPathProfileSTC_STG->SpxStgSeg_Interval2   = (pInU16Data[4] >> 12) & 0x0F;

    pOutStandardPathProfileSTC_STG->SpxStgSeg_Height3     =  pInU16Data[5] & 0x0F;
    pOutStandardPathProfileSTC_STG->SpxStgSeg_Interval3   = (pInU16Data[5] >> 4) & 0x0F;
    pOutStandardPathProfileSTC_STG->SpxStgSeg_Height4     = (pInU16Data[5] >> 8) & 0x0F;
    pOutStandardPathProfileSTC_STG->SpxStgSeg_Interval4   = (pInU16Data[5] >> 12) & 0x0F;

    pOutStandardPathProfileSTC_STG->SpxStgSeg_Height5     =  pInU16Data[6] & 0x0F;
    pOutStandardPathProfileSTC_STG->SpxStgSeg_Interval5   = (pInU16Data[6] >> 4) & 0x0F;
    pOutStandardPathProfileSTC_STG->SpxStgSeg_Height6     = (pInU16Data[6] >> 8) & 0x0F;
    pOutStandardPathProfileSTC_STG->SpxStgSeg_Interval6   = (pInU16Data[6] >> 12) & 0x0F;

    pOutStandardPathProfileSTC_STG->SpxStgSeg_Height7     =  pInU16Data[7] & 0x0F;
    pOutStandardPathProfileSTC_STG->SpxStgSeg_Interval7   = (pInU16Data[7] >> 4) & 0x0F;
    pOutStandardPathProfileSTC_STG->SpxStgSeg_Height8     = (pInU16Data[7] >> 8) & 0x0F;
    pOutStandardPathProfileSTC_STG->SpxStgSeg_Interval8   = (pInU16Data[7] >> 12) & 0x0F;

    pOutStandardPathProfileSTC_STG->SpxAtgTau          =  pInU16Data[8] & 0x07;
    pOutStandardPathProfileSTC_STG->SpxAtgAlpha        = (pInU16Data[8] >> 3) & 0x07;
    pOutStandardPathProfileSTC_STG->SpxAtgIni          = (pInU16Data[8] >> 6) & 0x07;
    pOutStandardPathProfileSTC_STG->SpxAtgCfg          = (pInU16Data[8] >> 9) & 0x01;
    pOutStandardPathProfileSTC_STG->SPxSTG_recv_Thres_DS = (pInU16Data[8] >> 10) & 0x03;
    pOutStandardPathProfileSTC_STG->SpxStgSet          = (pInU16Data[8] >> 12) & 0x01;     
    pOutStandardPathProfileSTC_STG->SPxSTG_recv_Thres_DS_EN = (pInU16Data[8] >> 13) & 0x01;

    pOutStandardPathProfileSTC_STG->SpxEevalSel        =  pInU16Data[9] & 0x0F;
    pOutStandardPathProfileSTC_STG->SpxEevalSens       = (pInU16Data[9] >> 4) & 0x07;
    pOutStandardPathProfileSTC_STG->SpxEevalVal        = (pInU16Data[9] >> 7) & 0x01;
    pOutStandardPathProfileSTC_STG->SpxMeasGain        = (pInU16Data[9] >> 8) & 0x3F;
    
    pOutStandardPathProfileSTC_STG->SpxMeasMaskDirect  =  pInU16Data[10] & 0x07F;
    pOutStandardPathProfileSTC_STG->SpxMeasMaskIndirect= (pInU16Data[10] >> 7) & 0x7f;
    

    return Elmos17MEAS_STDProf_u16Len;
}


uint16 ConvAdvProfStrToU16(void *pInConvertStruct ,uint16 *pOutU16Data)
{
    if(pOutU16Data == NULL || pInConvertStruct == NULL)
    {
        return 0;
    }
    Meas_sAdvancedPathProfile *pInAdvancedPathProfile = (Meas_sAdvancedPathProfile *)pInConvertStruct;

    pOutU16Data[0] = pInAdvancedPathProfile->ApxBurstFreqLo & 0x7FFF ;        /**< BRG chirp low frequency */
    pOutU16Data[1] = pInAdvancedPathProfile->ApxBurstFreqHi & 0x7FFF ;        /**< BRG chirp high frequency */
    
    pOutU16Data[2] = (pInAdvancedPathProfile->ApxBurstFreqDelta & 0xFF)           \
                |((pInAdvancedPathProfile->ApxFilterLength & 0x3F) << 8) ;
    
    pOutU16Data[3] = (pInAdvancedPathProfile->ApxBurstPulses & 0x7F)              \
                |((pInAdvancedPathProfile->ApxCodeLength & 0x03) << 7)         \
                |((pInAdvancedPathProfile->ApxCode & 0x0F) << 9)               \
                |((pInAdvancedPathProfile->ApxDs & 0x01) << 13) ;              

    pOutU16Data[4]  = (pInAdvancedPathProfile->ApxEnvpCoeff0 & 0x0F) 
                    | ((pInAdvancedPathProfile->ApxEnvpCoeff1 & 0x0F) << 4) 
                    | ((pInAdvancedPathProfile->ApxEnvpCoeff2 & 0x0F) << 8) 
                    | ((pInAdvancedPathProfile->ApxEnvpCoeff3 & 0x0F) << 12);                 /**< ENVP filter coefficients in the advanced signal path 0 to 3 */

    pOutU16Data[5]  = (pInAdvancedPathProfile->ApxEnvpCoeff4 & 0x0F) 
                    | ((pInAdvancedPathProfile->ApxEnvpCoeff5 & 0x0F) << 4) 
                    | ((pInAdvancedPathProfile->ApxEnvpCoeff6 & 0x0F) << 8) 
                    | ((pInAdvancedPathProfile->ApxEnvpCoeff7 & 0x0F) << 12);                 /**< ENVP filter coefficients in the advanced signal path 4 to 7 */

    pOutU16Data[6]  = (pInAdvancedPathProfile->ApxEnvpCoeff8 & 0x0F) 
                    | ((pInAdvancedPathProfile->ApxEnvpCoeff9 & 0x0F) << 4) 
                    | ((pInAdvancedPathProfile->ApxEnvpCoeff10 & 0x0F) << 8) 
                    | ((pInAdvancedPathProfile->ApxEnvpCoeff11 & 0x0F) << 12);                 /**< ENVP filter coefficients in the advanced signal path 8 to 11 */

    pOutU16Data[7]  = (pInAdvancedPathProfile->ApxEnvpCoeff12 & 0x0F) 
                    | ((pInAdvancedPathProfile->ApxEnvpCoeff13 & 0x0F) << 4) 
                    | ((pInAdvancedPathProfile->ApxEnvpCoeff14 & 0x0F) << 8) 
                    | ((pInAdvancedPathProfile->ApxEnvpCoeff15 & 0x0F) << 12);                 /**< ENVP filter coefficients in the advanced signal path 12 to 15 */

    pOutU16Data[8]  = (pInAdvancedPathProfile->ApxEnvpCoeff16 & 0x0F) 
                    | ((pInAdvancedPathProfile->ApxEnvpCoeff17 & 0x0F) << 4) 
                    | ((pInAdvancedPathProfile->ApxEnvpCoeff18 & 0x0F) << 8) 
                    | ((pInAdvancedPathProfile->ApxEnvpCoeff19 & 0x0F) << 12);                 /**< ENVP filter coefficients in the advanced signal path 16 to 19 */
    
    pOutU16Data[9]  = (pInAdvancedPathProfile->ApxEnvpCoeff20 & 0x0F) 
                    | ((pInAdvancedPathProfile->ApxEnvpCoeff21 & 0x0F) << 4) 
                    | ((pInAdvancedPathProfile->ApxEnvpCoeff22 & 0x0F) << 8) 
                    | ((pInAdvancedPathProfile->ApxEnvpCoeff23 & 0x0F) << 12);                 /**< ENVP filter coefficients in the advanced signal path 20 to 23 */

    pOutU16Data[10]  = (pInAdvancedPathProfile->ApxEnvpCoeff24 & 0x0F) 
                    | ((pInAdvancedPathProfile->ApxEnvpCoeff25 & 0x0F) << 4) 
                    | ((pInAdvancedPathProfile->ApxEnvpCoeff26 & 0x0F) << 8) 
                    | ((pInAdvancedPathProfile->ApxEnvpCoeff27 & 0x0F) << 12);                 /**< ENVP filter coefficients in the advanced signal path 24 to 27 */
    
    pOutU16Data[11]  = (pInAdvancedPathProfile->ApxEnvpCoeff28 & 0x0F) 
                    | ((pInAdvancedPathProfile->ApxEnvpCoeff29 & 0x0F) << 4) 
                    | ((pInAdvancedPathProfile->ApxEnvpCoeff30 & 0x0F) << 8) 
                    | ((pInAdvancedPathProfile->ApxEnvpCoeff31 & 0x0F) << 12);                 /**< ENVP filter coefficients in the advanced signal path 28 to 31 */

    pOutU16Data[12] = (pInAdvancedPathProfile->ApxAatgCn & 0x07)              \
                |((pInAdvancedPathProfile->ApxAatgCw & 0x07) << 3)              \
                |((pInAdvancedPathProfile->ApxAatgAlpha & 0x03) << 6)           \
                |((pInAdvancedPathProfile->ApxAatgOff & 0x01) << 8)               
                |((pInAdvancedPathProfile->ApxEevalSens & 0x07) << 9)               
                |((pInAdvancedPathProfile->ApxMinConf & 0x0F) << 12) ;              

    pOutU16Data[13] = (pInAdvancedPathProfile->ApxMeasMaskDirect & 0x7F)              \
                |((pInAdvancedPathProfile->ApxMeasMaskIndirect & 0x7F) << 7)       \
                |((pInAdvancedPathProfile->Apx_FtcCfg & 0x03) << 14)  ;              

    return Elmos17MEAS_ADVProf_u16Len;
}

uint16 ConvU16ToAdvProfStr(uint16 *pInU16Data,void *pOutConvertStruct)
{
    if(pInU16Data == NULL || pOutConvertStruct == NULL)
    {
        return 0;
    }
    
    Meas_sAdvancedPathProfile *pOutAdvancedPathProfile = (Meas_sAdvancedPathProfile *)pOutConvertStruct;
    
    pOutAdvancedPathProfile->ApxBurstFreqLo = pInU16Data[0] & 0x7FFF ;        /**< BRG chirp low frequency */
    pOutAdvancedPathProfile->ApxBurstFreqHi = pInU16Data[1] & 0x7FFF ;        /**< BRG chirp high frequency */
    
    pOutAdvancedPathProfile->ApxBurstFreqDelta  =  pInU16Data[2] & 0xFF;
    pOutAdvancedPathProfile->ApxFilterLength    = (pInU16Data[2] >> 8) & 0x3F;

    pOutAdvancedPathProfile->ApxBurstPulses     =  pInU16Data[3] & 0x7F;
    pOutAdvancedPathProfile->ApxCodeLength      = (pInU16Data[3] >> 7) & 0x03;
    pOutAdvancedPathProfile->ApxCode            = (pInU16Data[3] >> 9) & 0x0F;
    pOutAdvancedPathProfile->ApxDs              = (pInU16Data[3] >> 13) & 0x01;

    
    pOutAdvancedPathProfile->ApxEnvpCoeff0    = (pInU16Data[4] & 0x0F);                                /**< ENVP filter coefficients in the advanced signal path  */
    pOutAdvancedPathProfile->ApxEnvpCoeff1    = ((pInU16Data[4] >> 4) & 0x0F);                         /**< ENVP filter coefficients in the advanced signal path  */
    pOutAdvancedPathProfile->ApxEnvpCoeff2    = ((pInU16Data[4] >> 8) & 0x0F);                         /**< ENVP filter coefficients in the advanced signal path  */
    pOutAdvancedPathProfile->ApxEnvpCoeff3    = ((pInU16Data[4] >> 12) & 0x0F);                        /**< ENVP filter coefficients in the advanced signal path  */
 
    pOutAdvancedPathProfile->ApxEnvpCoeff4    = (pInU16Data[5] & 0x0F);                                /**< ENVP filter coefficients in the advanced signal path  */
    pOutAdvancedPathProfile->ApxEnvpCoeff5    = ((pInU16Data[5] >> 4) & 0x0F);                         /**< ENVP filter coefficients in the advanced signal path  */
    pOutAdvancedPathProfile->ApxEnvpCoeff6    = ((pInU16Data[5] >> 8) & 0x0F);                         /**< ENVP filter coefficients in the advanced signal path  */
    pOutAdvancedPathProfile->ApxEnvpCoeff7    = ((pInU16Data[5] >> 12) & 0x0F);                        /**< ENVP filter coefficients in the advanced signal path  */
 
    pOutAdvancedPathProfile->ApxEnvpCoeff8    = (pInU16Data[6] & 0x0F);                                /**< ENVP filter coefficients in the advanced signal path  */
    pOutAdvancedPathProfile->ApxEnvpCoeff9    = ((pInU16Data[6] >> 4) & 0x0F);                         /**< ENVP filter coefficients in the advanced signal path  */
    pOutAdvancedPathProfile->ApxEnvpCoeff10    = ((pInU16Data[6] >> 8) & 0x0F);                         /**< ENVP filter coefficients in the advanced signal path  */
    pOutAdvancedPathProfile->ApxEnvpCoeff11    = ((pInU16Data[6] >> 12) & 0x0F);                        /**< ENVP filter coefficients in the advanced signal path  */

    pOutAdvancedPathProfile->ApxEnvpCoeff12    = (pInU16Data[7] & 0x0F);                                /**< ENVP filter coefficients in the advanced signal path  */
    pOutAdvancedPathProfile->ApxEnvpCoeff13    = ((pInU16Data[7] >> 4) & 0x0F);                         /**< ENVP filter coefficients in the advanced signal path  */
    pOutAdvancedPathProfile->ApxEnvpCoeff14    = ((pInU16Data[7] >> 8) & 0x0F);                         /**< ENVP filter coefficients in the advanced signal path  */
    pOutAdvancedPathProfile->ApxEnvpCoeff15    = ((pInU16Data[7] >> 12) & 0x0F);                        /**< ENVP filter coefficients in the advanced signal path  */
 
    pOutAdvancedPathProfile->ApxEnvpCoeff16    = (pInU16Data[8] & 0x0F);                                /**< ENVP filter coefficients in the advanced signal path  */
    pOutAdvancedPathProfile->ApxEnvpCoeff17    = ((pInU16Data[8] >> 4) & 0x0F);                         /**< ENVP filter coefficients in the advanced signal path  */
    pOutAdvancedPathProfile->ApxEnvpCoeff18    = ((pInU16Data[8] >> 8) & 0x0F);                         /**< ENVP filter coefficients in the advanced signal path  */
    pOutAdvancedPathProfile->ApxEnvpCoeff19    = ((pInU16Data[8] >> 12) & 0x0F);                        /**< ENVP filter coefficients in the advanced signal path  */
 
    pOutAdvancedPathProfile->ApxEnvpCoeff20    = (pInU16Data[9] & 0x0F);                                /**< ENVP filter coefficients in the advanced signal path  */
    pOutAdvancedPathProfile->ApxEnvpCoeff21    = ((pInU16Data[9] >> 4) & 0x0F);                         /**< ENVP filter coefficients in the advanced signal path  */
    pOutAdvancedPathProfile->ApxEnvpCoeff22    = ((pInU16Data[9] >> 8) & 0x0F);                         /**< ENVP filter coefficients in the advanced signal path  */
    pOutAdvancedPathProfile->ApxEnvpCoeff23    = ((pInU16Data[9] >> 12) & 0x0F);                        /**< ENVP filter coefficients in the advanced signal path  */

    pOutAdvancedPathProfile->ApxEnvpCoeff24    = (pInU16Data[10] & 0x0F);                                /**< ENVP filter coefficients in the advanced signal path  */
    pOutAdvancedPathProfile->ApxEnvpCoeff25    = ((pInU16Data[10] >> 4) & 0x0F);                         /**< ENVP filter coefficients in the advanced signal path  */
    pOutAdvancedPathProfile->ApxEnvpCoeff26    = ((pInU16Data[10] >> 8) & 0x0F);                         /**< ENVP filter coefficients in the advanced signal path  */
    pOutAdvancedPathProfile->ApxEnvpCoeff27    = ((pInU16Data[10] >> 12) & 0x0F);                        /**< ENVP filter coefficients in the advanced signal path  */
 
    pOutAdvancedPathProfile->ApxEnvpCoeff28    = (pInU16Data[11] & 0x0F);                                /**< ENVP filter coefficients in the advanced signal path  */
    pOutAdvancedPathProfile->ApxEnvpCoeff29    = ((pInU16Data[11] >> 4) & 0x0F);                         /**< ENVP filter coefficients in the advanced signal path  */
    pOutAdvancedPathProfile->ApxEnvpCoeff30    = ((pInU16Data[11] >> 8) & 0x0F);                         /**< ENVP filter coefficients in the advanced signal path  */
    pOutAdvancedPathProfile->ApxEnvpCoeff31    = ((pInU16Data[11] >> 12) & 0x0F);                        /**< ENVP filter coefficients in the advanced signal path  */
 
    pOutAdvancedPathProfile->ApxAatgCn          =  pInU16Data[12] & 0x07;
    pOutAdvancedPathProfile->ApxAatgCw          = (pInU16Data[12] >> 3) & 0x07;
    pOutAdvancedPathProfile->ApxAatgAlpha       = (pInU16Data[12] >> 6) & 0x03;
    pOutAdvancedPathProfile->ApxAatgOff         = (pInU16Data[12] >> 8) & 0x01;
    pOutAdvancedPathProfile->ApxEevalSens       = (pInU16Data[12] >> 9) & 0x07;
    pOutAdvancedPathProfile->ApxMinConf         = (pInU16Data[12] >> 12) & 0x0F;
    
    pOutAdvancedPathProfile->ApxMeasMaskDirect  =  pInU16Data[13] & 0x7F;
    pOutAdvancedPathProfile->ApxMeasMaskIndirect= (pInU16Data[13] >> 7) & 0x7F;
    pOutAdvancedPathProfile->Apx_FtcCfg         = (pInU16Data[13] >> 14) & 0x03;
       
    return Elmos17MEAS_ADVProf_u16Len;
}



uint16 ConvTofCompToU16(void *pInConvertStruct ,uint16 *pOutU16Data)
{
    if(pOutU16Data == NULL || pInConvertStruct == NULL)
    {
        return 0;
    }
    Meas_sTofCompensation *Meas_sTofComp = (Meas_sTofCompensation *)pInConvertStruct;

    pOutU16Data[0] = (Meas_sTofComp->SPA_TOF_compensation_time & 0xFF)     \
                    |((Meas_sTofComp->SPA_meas_time & 0xFF) << 8);         
    pOutU16Data[1] = (Meas_sTofComp->SPB_TOF_compensation_time & 0xFF)     \
                    |((Meas_sTofComp->SPB_meas_time & 0xFF) << 8);         
    pOutU16Data[2] = (Meas_sTofComp->SPC_TOF_compensation_time & 0xFF)     \
                    |((Meas_sTofComp->SPC_meas_time & 0xFF) << 8) ;        
    pOutU16Data[3] = (Meas_sTofComp->APA_TOF_compensation_time & 0xFF)     \
                    |((Meas_sTofComp->APA_meas_time & 0xFF) << 8);         
    pOutU16Data[4] = (Meas_sTofComp->APB_TOF_compensation_time & 0xFF)     \
                    |((Meas_sTofComp->APB_meas_time & 0xFF) << 8);         
    return Elmos17MEAS_TOFCompensation_u16Len;
}

uint16 ConvU16ToTofComp(uint16 *pInU16Data,void *pOutConvertStruct)
{
    if(pInU16Data == NULL || pOutConvertStruct == NULL)
    {
        return 0;
    }
    Meas_sTofCompensation *pOutMeas_sTofComp = (Meas_sTofCompensation *)pOutConvertStruct;

    pOutMeas_sTofComp->SPA_TOF_compensation_time = pInU16Data[0] & 0xFF;
    pOutMeas_sTofComp->SPA_meas_time             = (pInU16Data[0] >> 8)& 0xFF;
    
    pOutMeas_sTofComp->SPB_TOF_compensation_time = pInU16Data[1] & 0xFF;
    pOutMeas_sTofComp->SPB_meas_time             = (pInU16Data[1] >> 8)& 0xFF;

    pOutMeas_sTofComp->SPC_TOF_compensation_time = pInU16Data[2] & 0xFF;
    pOutMeas_sTofComp->SPC_meas_time             = (pInU16Data[2] >> 8)& 0xFF;
    
    pOutMeas_sTofComp->APA_TOF_compensation_time = pInU16Data[3] & 0xFF;
    pOutMeas_sTofComp->APA_meas_time             = (pInU16Data[3] >> 8)& 0xFF;
    
    pOutMeas_sTofComp->APB_TOF_compensation_time = pInU16Data[4] & 0xFF;
    pOutMeas_sTofComp->APB_meas_time             = (pInU16Data[4] >> 8)& 0xFF;
    return Elmos17MEAS_TOFCompensation_u16Len;
}



/** @brief elmos17 测量地址及数据转换表*/

const Elmos17MeasParamCfg_str Elmos17SMeasParamCfg[eWRSnsDataType_NUM] = 
{

    [eWRSnsDataType_GeneralSet]     = {.ParamAddr = Elmos17MEAS_GeneralSettings_StartAddr,
                                       .ParamU16Len = Elmos17MEAS_GeneralSettings_u16Len,
                                       .Structu8Len = sizeof(Meas_sGeneralSettings),
                                       .pCfgStr = 
    									{
											[SNS_FL_S] = (void*)&FLS_FRS_SnsGeneralSettings,
											[SNS_FL] = (void*)&FL_FR_SnsGeneralSettings,
										 	[SNS_FML] = (void*)&FLM_FRM_SnsGeneralSettings,
										 	[SNS_FMR] = (void*)&FLM_FRM_SnsGeneralSettings,
										 	[SNS_FR] = (void*)&FL_FR_SnsGeneralSettings,
										 	[SNS_FR_S] = (void*)&FLS_FRS_SnsGeneralSettings,
										 	[SNS_RR_S] = (void*)&RLS_RRS_SnsGeneralSettings,
										 	[SNS_RR] = (void*)&RL_RR_SnsGeneralSettings,
										 	[SNS_RMR] = (void*)&RLM_RRM_SnsGeneralSettings,
										 	[SNS_RML] = (void*)&RLM_RRM_SnsGeneralSettings,
										 	[SNS_RL] = (void*)&RL_RR_SnsGeneralSettings,
											[SNS_RL_S] = (void*)&RLS_RRS_SnsGeneralSettings,
										},
                                       .ConvU16DataToStructFunc = ConvertU16ToMeas_sGeneralSettings,
                                       .ConvStructToU16DataFunc = ConvertMeas_sGeneralSettingsToU16,},
                                       
                                       
    [eWRSnsDataType_NoiseMeasSet]   = {.ParamAddr = Elmos17MEAS_NoiseMeasurement_StartAddr,
                                       .ParamU16Len = Elmos17MEAS_NoiseMeasurement_u16Len,
                                       .Structu8Len = sizeof(Meas_sNoiseMeasurement),
                                       .pCfgStr = 
    									{
											[SNS_FL_S] = NULL,
											[SNS_FL] = NULL,
										 	[SNS_FML] = NULL,
										 	[SNS_FMR] = NULL,
										 	[SNS_FR] = NULL, 
										 	[SNS_FR_S] = NULL, 
										 	[SNS_RR_S] = NULL, 
										 	[SNS_RR] = NULL,
										 	[SNS_RMR] = NULL,
										 	[SNS_RML] = NULL, 
										 	[SNS_RL] = NULL,
											[SNS_RL_S] = NULL, 
										},
                                       .ConvU16DataToStructFunc = ConvertU16ToNoiseMeasurement,
                                       .ConvStructToU16DataFunc = ConvertNoiseMeasurementToU16,},
    
    [eWRSnsDataType_Std_ProfA]      = {.ParamAddr = Elmos17MEAS_STDProf_A_StartAddr,
                                       .ParamU16Len = Elmos17MEAS_STDProf_u16Len,
                                       .Structu8Len = sizeof(Meas_sStandardPathProfile),
                                       .pCfgStr = 
    									{
											[SNS_FL_S] = (void*)&FLS_FRS_STDProfile_A,
											[SNS_FL] = (void*)&FL_FR_STDProfile_A,
										 	[SNS_FML] = (void*)&FLM_FRM_STDProfile_A,
										 	[SNS_FMR] = (void*)&FLM_FRM_STDProfile_A,
										 	[SNS_FR] = (void*)&FL_FR_STDProfile_A, 
										 	[SNS_FR_S] = (void*)&FLS_FRS_STDProfile_A, 
										 	[SNS_RR_S] = (void*)&RLS_RRS_STDProfile_A, 
										 	[SNS_RR] = (void*)&RL_RR_STDProfile_A,
										 	[SNS_RMR] = (void*)&RLM_RRM_STDProfile_A,
										 	[SNS_RML] = (void*)&RLM_RRM_STDProfile_A, 
										 	[SNS_RL] = (void*)&RL_RR_STDProfile_A,
											[SNS_RL_S] = (void*)&RLS_RRS_STDProfile_A,
										},
                                       .ConvU16DataToStructFunc = ConvU16ToStdProfStr,
                                       .ConvStructToU16DataFunc = ConvStdProfStrToU16,},
                                       
    [eWRSnsDataType_Std_ProfB]      = {.ParamAddr = Elmos17MEAS_STDProf_B_StartAddr,
                                       .ParamU16Len = Elmos17MEAS_STDProf_u16Len,
                                       .Structu8Len = sizeof(Meas_sStandardPathProfile),
                                       .pCfgStr = 
    									{
											[SNS_FL_S] = (void*)&FLS_FRS_STDProfile_B,
											[SNS_FL] = (void*)&FL_FR_STDProfile_B,
										 	[SNS_FML] = (void*)&FLM_FRM_STDProfile_B,
										 	[SNS_FMR] = (void*)&FLM_FRM_STDProfile_B,
										 	[SNS_FR] = (void*)&FL_FR_STDProfile_B, 
										 	[SNS_FR_S] = (void*)&FLS_FRS_STDProfile_B, 
										 	[SNS_RR_S] = (void*)&RLS_RRS_STDProfile_B, 
										 	[SNS_RR] = (void*)&RL_RR_STDProfile_B,
										 	[SNS_RMR] = (void*)&RLM_RRM_STDProfile_B,
										 	[SNS_RML] = (void*)&RLM_RRM_STDProfile_B, 
										 	[SNS_RL] = (void*)&RL_RR_STDProfile_B,
											[SNS_RL_S] = (void*)&RLS_RRS_STDProfile_B,
										},
                                       .ConvU16DataToStructFunc = ConvU16ToStdProfStr,
                                       .ConvStructToU16DataFunc = ConvStdProfStrToU16,},
                                       
    [eWRSnsDataType_Std_ProfC]      = {.ParamAddr = Elmos17MEAS_STDProf_C_StartAddr,
                                       .ParamU16Len = Elmos17MEAS_STDProf_u16Len,
                                       .Structu8Len = sizeof(Meas_sStandardPathProfile),
                                       .pCfgStr = 
    									{
											[SNS_FL_S] = (void*)&FLS_FRS_STDProfile_C,
											[SNS_FL] = (void*)&FL_FR_STDProfile_C,
										 	[SNS_FML] = (void*)&FLM_FRM_STDProfile_C,
										 	[SNS_FMR] = (void*)&FLM_FRM_STDProfile_C,
										 	[SNS_FR] = (void*)&FL_FR_STDProfile_C, 
										 	[SNS_FR_S] = (void*)&FLS_FRS_STDProfile_C, 
										 	[SNS_RR_S] = (void*)&RLS_RRS_STDProfile_C, 
										 	[SNS_RR] = (void*)&RL_RR_STDProfile_C,
										 	[SNS_RMR] = (void*)&RLM_RRM_STDProfile_C,
										 	[SNS_RML] = (void*)&RLM_RRM_STDProfile_C, 
										 	[SNS_RL] = (void*)&RL_RR_STDProfile_C,
											[SNS_RL_S] = (void*)&RLS_RRS_STDProfile_C, 
										},
                                       .ConvU16DataToStructFunc = ConvU16ToStdProfStr,
                                       .ConvStructToU16DataFunc = ConvStdProfStrToU16,},
    
    [eWRSnsDataType_Adv_ProfA]      = {.ParamAddr = Elmos17MEAS_ADVProf_A_StartAddr,
                                       .ParamU16Len = Elmos17MEAS_ADVProf_u16Len,
                                       .Structu8Len = sizeof(Meas_sAdvancedPathProfile),
                                       .pCfgStr = 
    									{
											[SNS_FL_S] = (void*)&FLS_FRS_ADVProfile_A,
											[SNS_FL] = (void*)&FL_FR_ADVProfile_A,
										 	[SNS_FML] = (void*)&FLM_FRM_ADVProfile_A,
										 	[SNS_FMR] = (void*)&FLM_FRM_ADVProfile_A,
										 	[SNS_FR] = (void*)&FL_FR_ADVProfile_A, 
										 	[SNS_FR_S] = (void*)&FLS_FRS_ADVProfile_A, 
										 	[SNS_RR_S] = (void*)&RLS_RRS_ADVProfile_A, 
										 	[SNS_RR] = (void*)&RL_RR_ADVProfile_A,
										 	[SNS_RMR] = (void*)&RLM_RRM_ADVProfile_A,
										 	[SNS_RML] = (void*)&RLM_RRM_ADVProfile_A, 
										 	[SNS_RL] = (void*)&RL_RR_ADVProfile_A,
											[SNS_RL_S] = (void*)&RLS_RRS_ADVProfile_A, 
										},
                                       .ConvU16DataToStructFunc = ConvU16ToAdvProfStr,
                                       .ConvStructToU16DataFunc = ConvAdvProfStrToU16,},
                                       
    [eWRSnsDataType_Adv_ProfB]      = {.ParamAddr = Elmos17MEAS_ADVProf_B_StartAddr,
                                       .ParamU16Len = Elmos17MEAS_ADVProf_u16Len,
                                       .Structu8Len = sizeof(Meas_sAdvancedPathProfile),
                                       .pCfgStr = 
    									{
											[SNS_FL_S] = (void*)&FLS_FRS_ADVProfile_B,
											[SNS_FL] = (void*)&FL_FR_ADVProfile_B,
										 	[SNS_FML] = (void*)&FLM_FRM_ADVProfile_B,
										 	[SNS_FMR] = (void*)&FLM_FRM_ADVProfile_B,
										 	[SNS_FR] = (void*)&FL_FR_ADVProfile_B, 
										 	[SNS_FR_S] = (void*)&FLS_FRS_ADVProfile_B, 
										 	[SNS_RR_S] = (void*)&RLS_RRS_ADVProfile_B, 
										 	[SNS_RR] = (void*)&RL_RR_ADVProfile_B,
										 	[SNS_RMR] = (void*)&RLM_RRM_ADVProfile_B,
										 	[SNS_RML] = (void*)&RLM_RRM_ADVProfile_B, 
										 	[SNS_RL] = (void*)&RL_RR_ADVProfile_B,
											[SNS_RL_S] = (void*)&RLS_RRS_ADVProfile_B, 

										},
                                       .ConvU16DataToStructFunc = ConvU16ToAdvProfStr,
                                       .ConvStructToU16DataFunc = ConvAdvProfStrToU16,},
    

                                       
    [eWRSnsDataType_TOFComp]        = {.ParamAddr = Elmos17MEAS_TOFCompensation_StartAddr,
                                       .ParamU16Len = Elmos17MEAS_TOFCompensation_u16Len,
                                       .Structu8Len = sizeof(Meas_sTofCompensation),
                                       .pCfgStr = 
    									{
											[SNS_FL_S] = (void*)&FLS_FRS_TofComp,
											[SNS_FL] = (void*)&FL_FR_TofComp,
										 	[SNS_FML] = (void*)&FLM_FRM_TofComp,
										 	[SNS_FMR] = (void*)&FLM_FRM_TofComp,
										 	[SNS_FR] = (void*)&FL_FR_TofComp, 
										 	[SNS_FR_S] = (void*)&FLS_FRS_TofComp, 
										 	[SNS_RR_S] = (void*)&RLS_RRS_TofComp, 
										 	[SNS_RR] = (void*)&RL_RR_TofComp,
										 	[SNS_RMR] = (void*)&RLM_RRM_TofComp,
										 	[SNS_RML] = (void*)&RLM_RRM_TofComp, 
										 	[SNS_RL] = (void*)&RL_RR_TofComp,
											[SNS_RL_S] = (void*)&RLS_RRS_TofComp,
										},
                                       .ConvU16DataToStructFunc = ConvU16ToTofComp,
                                       .ConvStructToU16DataFunc = ConvTofCompToU16,},
                    
    [eWRSnsDataType_Std_SEG_ProfA]      = {.ParamAddr = Elmos17MEAS_STDProf_A_StartAddr,
                                       .ParamU16Len = Elmos17MEAS_STDProf_u16Len,
                                       .Structu8Len = sizeof(Meas_sStandardPathProfile_STC_STG),
                                       .pCfgStr = 
    									{
											[SNS_FL_S] = NULL,
											[SNS_FL] = NULL,
										 	[SNS_FML] = NULL,
										 	[SNS_FMR] = NULL,
										 	[SNS_FR] = NULL, 
										 	[SNS_FR_S] = NULL, 
										 	[SNS_RR_S] = NULL, 
										 	[SNS_RR] = NULL,
										 	[SNS_RMR] = NULL,
										 	[SNS_RML] = NULL, 
										 	[SNS_RL] = NULL,
											[SNS_RL_S] = NULL, 
										},
                                       .ConvU16DataToStructFunc = ConvertU16ToStdProf_SEG,
                                       .ConvStructToU16DataFunc = ConvStdProf_SEG_ToU16,},
                                       
    [eWRSnsDataType_Std_SEG_ProfB]      = {.ParamAddr = Elmos17MEAS_STDProf_B_StartAddr,
                                       .ParamU16Len = Elmos17MEAS_STDProf_u16Len,
                                       .Structu8Len = sizeof(Meas_sStandardPathProfile_STC_STG),
                                       .pCfgStr = 
    									{
											[SNS_FL_S] = NULL,
											[SNS_FL] = NULL,
										 	[SNS_FML] = NULL,
										 	[SNS_FMR] = NULL,
										 	[SNS_FR] = NULL, 
										 	[SNS_FR_S] = NULL, 
										 	[SNS_RR_S] = NULL, 
										 	[SNS_RR] = NULL,
										 	[SNS_RMR] = NULL,
										 	[SNS_RML] = NULL, 
										 	[SNS_RL] = NULL,
											[SNS_RL_S] = NULL, 
										},
                                       .ConvU16DataToStructFunc = ConvertU16ToStdProf_SEG,
                                       .ConvStructToU16DataFunc = ConvStdProf_SEG_ToU16,},
                                       
    [eWRSnsDataType_Std_SEG_ProfC]      = {.ParamAddr = Elmos17MEAS_STDProf_C_StartAddr,
                                       .ParamU16Len = Elmos17MEAS_STDProf_u16Len,
                                       .Structu8Len = sizeof(Meas_sStandardPathProfile_STC_STG),
                                       .pCfgStr = 
    									{
											[SNS_FL_S] = NULL,
											[SNS_FL] = NULL,
										 	[SNS_FML] = NULL,
										 	[SNS_FMR] = NULL,
										 	[SNS_FR] = NULL, 
										 	[SNS_FR_S] = NULL, 
										 	[SNS_RR_S] = NULL, 
										 	[SNS_RR] = NULL,
										 	[SNS_RMR] = NULL,
										 	[SNS_RML] = NULL, 
										 	[SNS_RL] = NULL,
											[SNS_RL_S] = NULL, 
										},
                                       .ConvU16DataToStructFunc = ConvertU16ToStdProf_SEG,
                                       .ConvStructToU16DataFunc = ConvStdProf_SEG_ToU16,},
};

void WriteCalAndAnaGain(uint16 InData,Meas_sGeneralSettings *pOutGeneralSet)
{
	if(NULL != pOutGeneralSet)
	{
		pOutGeneralSet->CalibrationGain = (InData >> 9) & 0x001F;
		pOutGeneralSet->AnalogLp = (InData >> 8) & 0x0001;
		//pOutGeneralSet->AnalogGain = (InData >> 5) & 0x0007;
		//pOutGeneralSet->TdrCurrent = (InData & 0x001F);
	}
}

const Elmos17MeasParamCfg_str* GetMeasParamCfg(void)
{
	return &Elmos17SMeasParamCfg[0]; 
}

const uint8* GetMeasParamDefaultVal(void)
{
	return &MeasParamDefaultVal[0];
}

#endif
