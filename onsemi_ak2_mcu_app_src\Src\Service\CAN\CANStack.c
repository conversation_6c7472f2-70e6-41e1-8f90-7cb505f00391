/**********************************************************************
*	版权所有	: 2017 深圳市豪恩汽车电子装备有限公司
*   项目名称	: TATA Furuto AVM
*   M C U       : R5F10BBG
*   编译环境	: IAR
*	文件名称	: CANStack.c
*	其它说明	: CAN协议栈
*	当前版本	: V0.1
*	作    者	: 20460
*	完成日期	:
*	内容摘要	:
*	修改记录1	:
*	修改日期	:
*	版 本 号	:
*	修 改 人	:
*	修改内容	:
*	修改记录2	:

***********************************************************************/
/***********************************************************************
* Includes
***********************************************************************/
#include "CANStack.h"
#include "CAN_COM.h"
#include "CAN_IL.h"
#include "CAN_AppSignalManage.h"
#include "CAN_UDS.h"
#include "AdcHal.h"
#include "TP_Manage.h"
#include "TimerManage.h"
#include "PowerSingalManage.h"
#include "DebugSignalManage.h"
#include "DID_Calibration.h"
/*****************************************************************************
* Global objects
******************************************************************************/

/*********************************************************************
 * 函数名称:  CANStackInit
 *
 * 功能描述:  CAN协议栈的初始化
 *
 * 入口参数:  无
 *
 * 输出参数:  无
 *
 * 其它说明:  无
 *
 * 修改日期		版本号		修改人		修改内容
 *
 ********************************************************************/
void CANStackInit(void)
{
    COM_Init();

    /* PDC APA 标定使用EEPROM与DTC错开使用 */
#if (CALIBRATION_EN == STD_ON)
    Fls_Init();
    InitCalibrationDIDToEeprom();
    generate_crc8_table();
#endif
    DTCVariableInit();/*DTC初始化，初次上电会写入一些did数据*/


    /*DIDInit（）必须放在CAN_UDSInit（）前调用,因为安全解锁*/
    DIDInit();

    CAN_UDSInit();

    COM_Activate();

    TP_Init(&GsTpMsgCfg, &GsTPCOMParaCfg, &GsTPUDSParaCfg);

    CanIL_Init();
}
/*********************************************************************
 * 函数名称:  CANStackManage
 *
 * 功能描述:  CAN协议栈管理
 *
 * 入口参数:  无
 *
 * 输出参数:  无
 *
 * 其它说明:  无
 *
 * 修改日期		版本号		修改人		修改内容
 *
 ********************************************************************/
void CANStackManage(void)
{

    if (ReadPwrMonitorSignal_ADMomentaryValue() >= AD_CALIBRATION_7V)
    {
        //TP_Manage();

        HandleNRC78Event();              /* 处理NRC78事件 */

        UDS_ServiceManage();             /*各诊断服务的处理*/

        UDS_Manage();                    /* 主要是处理诊断忙响应(78) */

        //COM_TxManage();                 /* 报文发送处理 */

        IL_MessageCombine_RX();            /*处理接收到的应用报文*/

        COM_RxMonitor();                /* 报文接收管理，主要监控周期报文接收是否超时 */
    }
    /*busoff故障检测*/
    BusOffRecoverMonitor();
    InitCAN();

}
/*********************************************************************
 * 函数名称:  CAN_Task50ms
 *
 * 功能描述:  CAN50ms任务调度
 *
 *
 * 入口参数:  无
 *
 * 输出参数:  无
 *
 * 其它说明:  无
 *
 * 修改日期		版本号		修改人		修改内容
 *
 ********************************************************************/
void CAN_Task50ms(void)
{
    Updata_DTCWindow();
}
