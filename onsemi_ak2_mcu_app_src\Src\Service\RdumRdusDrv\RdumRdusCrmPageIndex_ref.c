/******************************************************************************
 * @file      RdumRdusCrmPageIndex.c
 * @brief     安森美超声波探头CRM命令页面索引支持
 * <AUTHOR>
 * @date      2025-05-15
 * @note      
 *****************************************************************************/

/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "RdumRdusCrm.h"



/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/



/******************************************************************************
 * @Const Declaration
 *****************************************************************************/



/******************************************************************************
 * @Variable Declaration
 *****************************************************************************/



/******************************************************************************
 * @Function Definitions
 *****************************************************************************/

/******************************************************************************
 * @brief      通过页面索引写入
 * @param[in]  Dsi3Ch DSI3通道
 * @param[in]  Dsi3Id 从机ID
 * @param[in]  PageIndex 页面索引
 * @param[in]  Data 数据
 * @param[in]  DataSize 数据大小
 * @return     传输状态
 * <AUTHOR>
 * @date       2025-05-15
 * @note       
 *****************************************************************************/
Dsi3TxStatus_t RdumRdusCrm_WriteByPageIndex(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, RdumRdusPageIndex_t PageIndex, uint8 *Data, uint8 DataSize)
{
    uint8 Address;
    uint16 Data16;
    
    /* 检查页面索引是否有效 */
    if (!RdumRdusPageIndex_IsValid(PageIndex))
    {
        return DIS3_TRANS_FAIL;
    }
    
    /* 计算地址 */
    Address = (PageIndex.Page << 6) | (PageIndex.Index & 0x3F);
    
    Data16 = (Data[0] << 8) | Data[1];
    
    /* 调用写入函数 */
    return RdumRdusCrm_Write(Dsi3Ch, Dsi3Id, Data16, NULL);
}

/******************************************************************************
 * @brief      通过页面索引8位写入
 * @param[in]  Dsi3Ch DSI3通道
 * @param[in]  Dsi3Id 从机ID
 * @param[in]  PageIndex 页面索引
 * @param[in]  Data 数据
 * @return     传输状态
 * <AUTHOR>
 * @date       2025-05-15
 * @note       
 *****************************************************************************/
Dsi3TxStatus_t RdumRdusCrm_Write8BitByPageIndex(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, RdumRdusPageIndex_t PageIndex, uint8 Data)
{
    uint8 Address;
    
    /* 检查页面索引是否有效 */
    if (!RdumRdusPageIndex_IsValid(PageIndex))
    {
        return DIS3_TRANS_FAIL;
    }
    
    /* 计算地址 */
    Address = (PageIndex.Page << 6) | (PageIndex.Index & 0x3F);
    
    /* 调用8位写入函数 */
    return RdumRdusCrm_Write8Bit(Dsi3Ch, Dsi3Id, Address, Data);
}

/******************************************************************************
 * @brief      通过页面索引读取
 * @param[in]  Dsi3Ch DSI3通道
 * @param[in]  Dsi3Id 从机ID
 * @param[in]  PageIndex 页面索引
 * @return     传输状态
 * <AUTHOR>
 * @date       2025-05-15
 * @note       
 *****************************************************************************/
Dsi3TxStatus_t RdumRdusCrm_ReadByPageIndex(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, RdumRdusPageIndex_t PageIndex)
{
    /* 检查页面索引是否有效 */
    if (!RdumRdusPageIndex_IsValid(PageIndex))
    {
        return DIS3_TRANS_FAIL;
    }
    
    /* 调用读取函数 */
    return RdumRdusCrm_Read(Dsi3Ch, Dsi3Id, PageIndex.Page, PageIndex.Index);
}

/******************************************************************************
 * @brief      读取传感器ID
 * @param[in]  Dsi3Ch DSI3通道
 * @param[in]  Dsi3Id 从机ID
 * @return     传输状态
 * <AUTHOR>
 * @date       2025-05-15
 * @note       
 *****************************************************************************/
Dsi3TxStatus_t RdumRdusCrm_ReadSensorId(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id)
{
    RdumRdusPageIndex_t PageIndex;
    
    /* 获取传感器ID的页面索引 */
    PageIndex = RdumRdusPageIndex_Get(PAGE_0, PAGE0_IDX_SENSOR_ID);
    
    /* 读取传感器ID */
    return RdumRdusCrm_ReadByPageIndex(Dsi3Ch, Dsi3Id, PageIndex);
}

/******************************************************************************
 * @brief      写入测量模式参数
 * @param[in]  Dsi3Ch DSI3通道
 * @param[in]  Dsi3Id 从机ID
 * @param[in]  Mode 模式
 * @param[in]  Data 数据
 * @param[in]  DataSize 数据大小
 * @return     传输状态
 * <AUTHOR>
 * @date       2025-05-15
 * @note       
 *****************************************************************************/
Dsi3TxStatus_t RdumRdusCrm_WriteModeConfig(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, uint8 Mode, uint8 *Data, uint8 DataSize)
{
    RdumRdusPageIndex_t PageIndex;
    uint8 BaseIndex;
    
    /* 检查模式是否有效 */
    if (Mode > 5)
    {
        return DIS3_TRANS_FAIL;
    }
    
    /* 计算基础索引 */
    BaseIndex = 0x30 + (Mode * 0x22);
    
    /* 获取模式配置的页面索引 */
    PageIndex = RdumRdusPageIndex_Get(PAGE_1, BaseIndex);
    
    /* 写入模式配置 */
    return RdumRdusCrm_WriteByPageIndex(Dsi3Ch, Dsi3Id, PageIndex, Data, DataSize);
}

/******************************************************************************
 * @brief      读取测量模式参数
 * @param[in]  Dsi3Ch DSI3通道
 * @param[in]  Dsi3Id 从机ID
 * @param[in]  Mode 模式
 * @return     传输状态
 * <AUTHOR>
 * @date       2025-05-15
 * @note       
 *****************************************************************************/
Dsi3TxStatus_t RdumRdusCrm_ReadModeConfig(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, uint8 Mode)
{
    RdumRdusPageIndex_t PageIndex;
    uint8 BaseIndex;
    
    /* 检查模式是否有效 */
    if (Mode > 5)
    {
        return DIS3_TRANS_FAIL;
    }
    
    /* 计算基础索引 */
    BaseIndex = 0x30 + (Mode * 0x22);
    
    /* 获取模式配置的页面索引 */
    PageIndex = RdumRdusPageIndex_Get(PAGE_1, BaseIndex);
    
    /* 读取模式配置 */
    return RdumRdusCrm_ReadByPageIndex(Dsi3Ch, Dsi3Id, PageIndex);
}
