[{"timestamp": 1747122876.4734383, "datetime": "2025-05-13 15:54:36", "command_bytes": [2, 8, 0, 0, 128, 0, 52], "response_bytes": [0, 0, 0, 207, 165, 161, 200], "command_hex": "02 08 00 00 80 00 34", "response_hex": "00 00 00 CF A5 A1 C8"}, {"timestamp": 1747122921.4953847, "datetime": "2025-05-13 15:55:21", "command_bytes": [2, 81, 0, 117, 0, 0, 97], "response_bytes": [0, 0, 3, 69, 165, 161, 153], "command_hex": "02 51 00 75 00 00 61", "response_hex": "00 00 03 45 A5 A1 99"}, {"timestamp": 1747122965.8022916, "datetime": "2025-05-13 15:56:05", "command_bytes": [1, 17, 0, 0, 0, 0, 64], "response_bytes": [0, 131, 0, 0, 0, 0, 146], "command_hex": "01 11 00 00 00 00 40", "response_hex": "00 83 00 00 00 00 92"}, {"timestamp": 1747123001.795706, "datetime": "2025-05-13 15:56:41", "command_bytes": [1, 18, 0, 0, 0, 0, 152], "response_bytes": [0, 129, 217, 129, 0, 1, 197], "command_hex": "01 12 00 00 00 00 98", "response_hex": "00 81 D9 81 00 01 C5"}, {"timestamp": 1747123039.639511, "datetime": "2025-05-13 15:57:19", "command_bytes": [1, 19, 0, 0, 0, 0, 53], "response_bytes": [0, 131, 16, 148, 76, 0, 53], "command_hex": "01 13 00 00 00 00 35", "response_hex": "00 83 10 94 4C 00 35"}, {"timestamp": 1747123079.7039776, "datetime": "2025-05-13 15:57:59", "command_bytes": [1, 20, 0, 0, 0, 0, 7], "response_bytes": [0, 129, 10, 226, 185, 15, 200], "command_hex": "01 14 00 00 00 00 07", "response_hex": "00 81 0A E2 B9 0F C8"}, {"timestamp": 1747123106.1845264, "datetime": "2025-05-13 15:58:26", "command_bytes": [1, 21, 0, 0, 0, 0, 170], "response_bytes": [0, 131, 17, 132, 73, 19, 131], "command_hex": "01 15 00 00 00 00 AA", "response_hex": "00 83 11 84 49 13 83"}, {"timestamp": 1747123131.386548, "datetime": "2025-05-13 15:58:51", "command_bytes": [1, 22, 0, 0, 0, 0, 114], "response_bytes": [0, 129, 0, 0, 53, 52, 213], "command_hex": "01 16 00 00 00 00 72", "response_hex": "00 81 00 00 35 34 D5"}, {"timestamp": 1747123154.6103148, "datetime": "2025-05-13 15:59:14", "command_bytes": [1, 23, 0, 0, 0, 0, 223], "response_bytes": [0, 131, 0, 1, 58, 14, 62], "command_hex": "01 17 00 00 00 00 DF", "response_hex": "00 83 00 01 3A 0E 3E"}, {"timestamp": 1747123209.0727746, "datetime": "2025-05-13 16:00:09", "command_bytes": [1, 24, 0, 0, 0, 0, 22], "response_bytes": [0, 129, 0, 0, 8, 195, 22], "command_hex": "01 18 00 00 00 00 16", "response_hex": "00 81 00 00 08 C3 16"}, {"timestamp": 1747123275.879428, "datetime": "2025-05-13 16:01:15", "command_bytes": [2, 0, 24, 1, 32, 50, 66], "response_bytes": [0, 0, 4, 142, 129, 161, 34], "command_hex": "02 00 18 01 20 32 42", "response_hex": "00 00 04 8E 81 A1 22"}, {"timestamp": 1747123331.1249006, "datetime": "2025-05-13 16:02:11", "command_bytes": [2, 1, 192, 56, 17, 3, 145], "response_bytes": [0, 0, 7, 6, 129, 161, 111], "command_hex": "02 01 C0 38 11 03 91", "response_hex": "00 00 07 06 81 A1 6F"}, {"timestamp": 1747123393.2406814, "datetime": "2025-05-13 16:03:13", "command_bytes": [2, 9, 0, 64, 0, 0, 111], "response_bytes": [0, 0, 1, 130, 129, 161, 85], "command_hex": "02 09 00 40 00 00 6F", "response_hex": "00 00 01 82 81 A1 55"}, {"timestamp": 1747123393.2436655, "datetime": "2025-05-13 16:03:13", "command_bytes": [2, 2, 55, 241, 0, 26, 199], "response_bytes": [0, 0, 2, 0, 129, 161, 116], "command_hex": "02 02 37 F1 00 1A C7", "response_hex": "00 00 02 00 81 A1 74"}, {"timestamp": 1747123433.7265892, "datetime": "2025-05-13 16:03:53", "command_bytes": [2, 12, 0, 0, 0, 0, 217], "response_bytes": [0, 0, 4, 126, 129, 161, 17], "command_hex": "02 0C 00 00 00 00 D9", "response_hex": "00 00 04 7E 81 A1 11"}, {"timestamp": 1747123465.0000248, "datetime": "2025-05-13 16:04:25", "command_bytes": [2, 14, 0, 0, 0, 0, 172], "response_bytes": [0, 0, 6, 250, 129, 161, 161], "command_hex": "02 0E 00 00 00 00 AC", "response_hex": "00 00 06 FA 81 A1 A1"}, {"timestamp": 1747123496.2778318, "datetime": "2025-05-13 16:04:56", "command_bytes": [2, 11, 0, 0, 0, 0, 235], "response_bytes": [0, 0, 1, 120, 129, 161, 10], "command_hex": "02 0B 00 00 00 00 EB", "response_hex": "00 00 01 78 81 A1 0A"}, {"timestamp": 1747123606.692683, "datetime": "2025-05-13 16:06:46", "command_bytes": [2, 3, 248, 10, 0, 214, 162], "response_bytes": [0, 0, 2, 32, 129, 161, 155], "command_hex": "02 03 F8 0A 00 D6 A2", "response_hex": "00 00 02 20 81 A1 9B"}, {"timestamp": 1747123807.2205899, "datetime": "2025-05-13 16:10:07", "command_bytes": [2, 2, 151, 241, 0, 17, 204], "response_bytes": [0, 0, 3, 234, 129, 161, 126], "command_hex": "02 02 97 F1 00 11 CC", "response_hex": "00 00 03 EA 81 A1 7E"}, {"timestamp": 1747123912.9047558, "datetime": "2025-05-13 16:11:52", "command_bytes": [2, 3, 244, 0, 0, 0, 54], "response_bytes": [0, 0, 1, 134, 161, 161, 39], "command_hex": "02 03 F4 00 00 00 36", "response_hex": "00 00 01 86 A1 A1 27"}, {"timestamp": 1747123912.9077215, "datetime": "2025-05-13 16:11:52", "command_bytes": [2, 4, 0, 0, 0, 0, 34], "response_bytes": [0, 0, 3, 228, 161, 161, 96], "command_hex": "02 04 00 00 00 00 22", "response_hex": "00 00 03 E4 A1 A1 60"}, {"timestamp": 1747123912.9107108, "datetime": "2025-05-13 16:11:52", "command_bytes": [2, 5, 0, 0, 0, 0, 143], "response_bytes": [0, 0, 4, 89, 161, 161, 158], "command_hex": "02 05 00 00 00 00 8F", "response_hex": "00 00 04 59 A1 A1 9E"}, {"timestamp": 1747123912.913701, "datetime": "2025-05-13 16:11:52", "command_bytes": [2, 6, 0, 248, 0, 0, 20], "response_bytes": [0, 0, 6, 213, 161, 161, 94], "command_hex": "02 06 00 F8 00 00 14", "response_hex": "00 00 06 D5 A1 A1 5E"}, {"timestamp": 1747126545.1842592, "datetime": "2025-05-13 16:55:45", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747126561.8194747, "datetime": "2025-05-13 16:56:01", "command_bytes": [2, 3, 255, 0, 239, 254, 13], "response_bytes": [0, 0, 3, 125, 165, 161, 230], "command_hex": "02 03 FF 00 EF FE 0D", "response_hex": "00 00 03 7D A5 A1 E6"}, {"timestamp": 1747126561.8234613, "datetime": "2025-05-13 16:56:01", "command_bytes": [2, 4, 255, 195, 13, 0, 88], "response_bytes": [0, 0, 5, 233, 165, 161, 60], "command_hex": "02 04 FF C3 0D 00 58", "response_hex": "00 00 05 E9 A5 A1 3C"}, {"timestamp": 1747126561.8264515, "datetime": "2025-05-13 16:56:01", "command_bytes": [2, 5, 2, 18, 0, 0, 54], "response_bytes": [0, 0, 6, 97, 165, 161, 113], "command_hex": "02 05 02 12 00 00 36", "response_hex": "00 00 06 61 A5 A1 71"}, {"timestamp": 1747126613.23476, "datetime": "2025-05-13 16:56:53", "command_bytes": [4, 0, 0, 0, 0, 0, 31, 0, 0, 0, 0], "response_bytes": [0, 0, 3, 66, 129, 161, 44, 32, 129, 36, 35], "command_hex": "04 00 00 00 00 00 1F 00 00 00 00", "response_hex": "00 00 03 42 81 A1 2C 20 81 24 23"}, {"timestamp": 1747128032.289021, "datetime": "2025-05-13 17:20:32", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747128295.6542196, "datetime": "2025-05-13 17:24:55", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747128313.153213, "datetime": "2025-05-13 17:25:13", "command_bytes": [2, 3, 244, 0, 0, 0, 54], "response_bytes": [0, 0, 1, 134, 161, 161, 39], "command_hex": "02 03 F4 00 00 00 36", "response_hex": "00 00 01 86 A1 A1 27"}, {"timestamp": 1747128313.157181, "datetime": "2025-05-13 17:25:13", "command_bytes": [2, 4, 0, 0, 0, 0, 34], "response_bytes": [0, 0, 3, 228, 161, 161, 96], "command_hex": "02 04 00 00 00 00 22", "response_hex": "00 00 03 E4 A1 A1 60"}, {"timestamp": 1747128313.1606088, "datetime": "2025-05-13 17:25:13", "command_bytes": [2, 5, 0, 0, 0, 0, 143], "response_bytes": [0, 0, 4, 89, 161, 161, 158], "command_hex": "02 05 00 00 00 00 8F", "response_hex": "00 00 04 59 A1 A1 9E"}, {"timestamp": 1747128313.1655924, "datetime": "2025-05-13 17:25:13", "command_bytes": [2, 6, 0, 248, 0, 0, 20], "response_bytes": [0, 0, 6, 213, 161, 161, 94], "command_hex": "02 06 00 F8 00 00 14", "response_hex": "00 00 06 D5 A1 A1 5E"}, {"timestamp": 1747128319.8077664, "datetime": "2025-05-13 17:25:19", "command_bytes": [2, 3, 244, 0, 0, 0, 54], "response_bytes": [0, 0, 1, 134, 161, 161, 39], "command_hex": "02 03 F4 00 00 00 36", "response_hex": "00 00 01 86 A1 A1 27"}, {"timestamp": 1747128319.8117533, "datetime": "2025-05-13 17:25:19", "command_bytes": [2, 4, 0, 0, 0, 0, 34], "response_bytes": [0, 0, 3, 228, 161, 161, 96], "command_hex": "02 04 00 00 00 00 22", "response_hex": "00 00 03 E4 A1 A1 60"}, {"timestamp": 1747128319.8147426, "datetime": "2025-05-13 17:25:19", "command_bytes": [2, 5, 0, 0, 0, 0, 143], "response_bytes": [0, 0, 4, 89, 161, 161, 158], "command_hex": "02 05 00 00 00 00 8F", "response_hex": "00 00 04 59 A1 A1 9E"}, {"timestamp": 1747128319.8177333, "datetime": "2025-05-13 17:25:19", "command_bytes": [2, 6, 0, 248, 0, 0, 20], "response_bytes": [0, 0, 6, 213, 161, 161, 94], "command_hex": "02 06 00 F8 00 00 14", "response_hex": "00 00 06 D5 A1 A1 5E"}, {"timestamp": 1747128340.6090634, "datetime": "2025-05-13 17:25:40", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747128349.6468475, "datetime": "2025-05-13 17:25:49", "command_bytes": [2, 3, 244, 0, 0, 0, 54], "response_bytes": [0, 0, 1, 134, 161, 161, 39], "command_hex": "02 03 F4 00 00 00 36", "response_hex": "00 00 01 86 A1 A1 27"}, {"timestamp": 1747128349.6518233, "datetime": "2025-05-13 17:25:49", "command_bytes": [2, 4, 0, 0, 0, 0, 34], "response_bytes": [0, 0, 3, 228, 161, 161, 96], "command_hex": "02 04 00 00 00 00 22", "response_hex": "00 00 03 E4 A1 A1 60"}, {"timestamp": 1747128349.6558096, "datetime": "2025-05-13 17:25:49", "command_bytes": [2, 5, 0, 0, 0, 0, 143], "response_bytes": [0, 0, 4, 89, 161, 161, 158], "command_hex": "02 05 00 00 00 00 8F", "response_hex": "00 00 04 59 A1 A1 9E"}, {"timestamp": 1747128349.6597972, "datetime": "2025-05-13 17:25:49", "command_bytes": [2, 6, 0, 248, 0, 0, 20], "response_bytes": [0, 0, 6, 213, 161, 161, 94], "command_hex": "02 06 00 F8 00 00 14", "response_hex": "00 00 06 D5 A1 A1 5E"}, {"timestamp": 1747129159.4108539, "datetime": "2025-05-13 17:39:19", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747129200.107717, "datetime": "2025-05-13 17:40:00", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747129225.9705064, "datetime": "2025-05-13 17:40:25", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747274813.2947087, "datetime": "2025-05-15 10:06:53", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747275117.777256, "datetime": "2025-05-15 10:11:57", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747275166.6538258, "datetime": "2025-05-15 10:12:46", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747275956.5952134, "datetime": "2025-05-15 10:25:56", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747277370.3542318, "datetime": "2025-05-15 10:49:30", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747355818.1829085, "datetime": "2025-05-16 08:36:58", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747356080.2774105, "datetime": "2025-05-16 08:41:20", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747356120.61825, "datetime": "2025-05-16 08:42:00", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747356510.1372707, "datetime": "2025-05-16 08:48:30", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747356535.9281805, "datetime": "2025-05-16 08:48:55", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747356613.017574, "datetime": "2025-05-16 08:50:13", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747356981.4989898, "datetime": "2025-05-16 08:56:21", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747357232.7419558, "datetime": "2025-05-16 09:00:32", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747357460.9751756, "datetime": "2025-05-16 09:04:20", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747357529.6268609, "datetime": "2025-05-16 09:05:29", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747357609.882518, "datetime": "2025-05-16 09:06:49", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747357623.5381877, "datetime": "2025-05-16 09:07:03", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747357680.110592, "datetime": "2025-05-16 09:08:00", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747357720.1555822, "datetime": "2025-05-16 09:08:40", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747357808.1608772, "datetime": "2025-05-16 09:10:08", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747357923.0884814, "datetime": "2025-05-16 09:12:03", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747357937.7488313, "datetime": "2025-05-16 09:12:17", "command_bytes": [2, 8, 0, 0, 128, 0, 52], "response_bytes": [0, 0, 0, 207, 165, 161, 200], "command_hex": "02 08 00 00 80 00 34", "response_hex": "00 00 00 CF A5 A1 C8"}, {"timestamp": 1747358164.5565696, "datetime": "2025-05-16 09:16:04", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747358323.7224693, "datetime": "2025-05-16 09:18:43", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747358367.856319, "datetime": "2025-05-16 09:19:27", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747358376.1496809, "datetime": "2025-05-16 09:19:36", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747358630.2326648, "datetime": "2025-05-16 09:23:50", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747358638.28443, "datetime": "2025-05-16 09:23:58", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747358863.9412024, "datetime": "2025-05-16 09:27:43", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747361965.8487127, "datetime": "2025-05-16 10:19:25", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747362775.78785, "datetime": "2025-05-16 10:32:55", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747362861.706805, "datetime": "2025-05-16 10:34:21", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747362938.4424617, "datetime": "2025-05-16 10:35:38", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747363025.223823, "datetime": "2025-05-16 10:37:05", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747363127.6848273, "datetime": "2025-05-16 10:38:47", "command_bytes": [2, 8, 0, 0, 128, 0, 52], "response_bytes": [0, 0, 0, 207, 165, 161, 200], "command_hex": "02 08 00 00 80 00 34", "response_hex": "00 00 00 CF A5 A1 C8"}, {"timestamp": 1747363152.4381852, "datetime": "2025-05-16 10:39:12", "command_bytes": [2, 8, 0, 0, 128, 0, 52], "response_bytes": [0, 0, 0, 207, 165, 161, 200], "command_hex": "02 08 00 00 80 00 34", "response_hex": "00 00 00 CF A5 A1 C8"}, {"timestamp": 1747363173.3968453, "datetime": "2025-05-16 10:39:33", "command_bytes": [2, 8, 0, 0, 128, 0, 52], "response_bytes": [0, 0, 0, 207, 165, 161, 200], "command_hex": "02 08 00 00 80 00 34", "response_hex": "00 00 00 CF A5 A1 C8"}, {"timestamp": 1747363510.0537317, "datetime": "2025-05-16 10:45:10", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747363539.0200953, "datetime": "2025-05-16 10:45:39", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747364111.752162, "datetime": "2025-05-16 10:55:11", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747365247.5039062, "datetime": "2025-05-16 11:14:07", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747365284.831282, "datetime": "2025-05-16 11:14:44", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747365627.2034776, "datetime": "2025-05-16 11:20:27", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747369035.285195, "datetime": "2025-05-16 12:17:15", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747370207.769152, "datetime": "2025-05-16 12:36:47", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747370331.8966177, "datetime": "2025-05-16 12:38:51", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747374737.3873296, "datetime": "2025-05-16 13:52:17", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747382861.6593895, "datetime": "2025-05-16 16:07:41", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747384067.6992314, "datetime": "2025-05-16 16:27:47", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747384094.585028, "datetime": "2025-05-16 16:28:14", "command_bytes": [2, 3, 249, 1, 2, 23, 162], "response_bytes": [0, 0, 7, 198, 161, 161, 25], "command_hex": "02 03 F9 01 02 17 A2", "response_hex": "00 00 07 C6 A1 A1 19"}, {"timestamp": 1747384218.4892888, "datetime": "2025-05-16 16:30:18", "command_bytes": [1, 18, 0, 0, 0, 0, 152], "response_bytes": [0, 129, 62, 33, 0, 1, 71], "command_hex": "01 12 00 00 00 00 98", "response_hex": "00 81 3E 21 00 01 47"}, {"timestamp": 1747384316.3804991, "datetime": "2025-05-16 16:31:56", "command_bytes": [2, 3, 249, 1, 2, 23, 162], "response_bytes": [0, 0, 7, 198, 161, 161, 25], "command_hex": "02 03 F9 01 02 17 A2", "response_hex": "00 00 07 C6 A1 A1 19"}, {"timestamp": 1747384405.3321316, "datetime": "2025-05-16 16:33:25", "command_bytes": [2, 3, 247, 0, 0, 244, 162], "response_bytes": [0, 0, 5, 137, 161, 161, 247], "command_hex": "02 03 F7 00 00 F4 A2", "response_hex": "00 00 05 89 A1 A1 F7"}, {"timestamp": 1747384806.1219614, "datetime": "2025-05-16 16:40:06", "command_bytes": [2, 3, 25, 1, 46, 92, 43], "response_bytes": [0, 0, 4, 224, 165, 161, 247], "command_hex": "02 03 19 01 2E 5C 2B", "response_hex": "00 00 04 E0 A5 A1 F7"}, {"timestamp": 1747385099.9856873, "datetime": "2025-05-16 16:44:59", "command_bytes": [4, 0, 0, 0, 0, 0, 31, 0, 0, 0, 0], "response_bytes": [0, 0, 6, 18, 129, 161, 2, 32, 251, 0, 111], "command_hex": "04 00 00 00 00 00 1F 00 00 00 00", "response_hex": "00 00 06 12 81 A1 02 20 FB 00 6F"}, {"timestamp": 1747387186.2267966, "datetime": "2025-05-16 17:19:46", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747387331.3516324, "datetime": "2025-05-16 17:22:11", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747387562.0767183, "datetime": "2025-05-16 17:26:02", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747387832.4793684, "datetime": "2025-05-16 17:30:32", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747387872.6601226, "datetime": "2025-05-16 17:31:12", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747387905.411559, "datetime": "2025-05-16 17:31:45", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747389006.6401536, "datetime": "2025-05-16 17:50:06", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747391879.1716404, "datetime": "2025-05-16 18:37:59", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747392684.2622838, "datetime": "2025-05-16 18:51:24", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747393674.598698, "datetime": "2025-05-16 19:07:54", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747394148.880555, "datetime": "2025-05-16 19:15:48", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747394856.690763, "datetime": "2025-05-16 19:27:36", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747395320.5398285, "datetime": "2025-05-16 19:35:20", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747396094.8612442, "datetime": "2025-05-16 19:48:14", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747396199.6107874, "datetime": "2025-05-16 19:49:59", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747396740.498315, "datetime": "2025-05-16 19:59:00", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747396811.795297, "datetime": "2025-05-16 20:00:11", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747396883.2005816, "datetime": "2025-05-16 20:01:23", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747396997.071451, "datetime": "2025-05-16 20:03:17", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747397305.4640722, "datetime": "2025-05-16 20:08:25", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747397435.0600796, "datetime": "2025-05-16 20:10:35", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747397510.456908, "datetime": "2025-05-16 20:11:50", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747397631.575981, "datetime": "2025-05-16 20:13:51", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747397714.7365088, "datetime": "2025-05-16 20:15:14", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747397803.068639, "datetime": "2025-05-16 20:16:43", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747397854.80414, "datetime": "2025-05-16 20:17:34", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747397905.2756374, "datetime": "2025-05-16 20:18:25", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747397922.5529346, "datetime": "2025-05-16 20:18:42", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747397949.6201115, "datetime": "2025-05-16 20:19:09", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747398071.7865732, "datetime": "2025-05-16 20:21:11", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747398130.2714596, "datetime": "2025-05-16 20:22:10", "command_bytes": [2, 8, 0, 0, 128, 0, 52], "response_bytes": [0, 0, 0, 207, 165, 161, 200], "command_hex": "02 08 00 00 80 00 34", "response_hex": "00 00 00 CF A5 A1 C8"}, {"timestamp": 1747399268.2603872, "datetime": "2025-05-16 20:41:08", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747615710.2414498, "datetime": "2025-05-19 08:48:30", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747615749.4801612, "datetime": "2025-05-19 08:49:09", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747615847.8905942, "datetime": "2025-05-19 08:50:47", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747618197.796146, "datetime": "2025-05-19 09:29:57", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747619416.8228328, "datetime": "2025-05-19 09:50:16", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747619596.591137, "datetime": "2025-05-19 09:53:16", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747620351.5052562, "datetime": "2025-05-19 10:05:51", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747620373.2798803, "datetime": "2025-05-19 10:06:13", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747620611.599894, "datetime": "2025-05-19 10:10:11", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747620660.5748858, "datetime": "2025-05-19 10:11:00", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747621007.0094633, "datetime": "2025-05-19 10:16:47", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747621686.6167212, "datetime": "2025-05-19 10:28:06", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747626143.2824647, "datetime": "2025-05-19 11:42:23", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747626419.3594599, "datetime": "2025-05-19 11:46:59", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747633604.7308621, "datetime": "2025-05-19 13:46:44", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747962733.9235213, "datetime": "2025-05-23 09:12:13", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1747962752.9338531, "datetime": "2025-05-23 09:12:32", "command_bytes": [2, 2, 55, 241, 0, 26, 199], "response_bytes": [0, 0, 2, 0, 129, 161, 116], "command_hex": "02 02 37 F1 00 1A C7", "response_hex": "00 00 02 00 81 A1 74"}, {"timestamp": 1747964324.2409453, "datetime": "2025-05-23 09:38:44", "command_bytes": [1, 24, 0, 0, 0, 0, 22], "response_bytes": [0, 129, 0, 0, 8, 195, 22], "command_hex": "01 18 00 00 00 00 16", "response_hex": "00 81 00 00 08 C3 16"}, {"timestamp": 1747964350.8871212, "datetime": "2025-05-23 09:39:10", "command_bytes": [2, 2, 151, 241, 0, 17, 204], "response_bytes": [0, 0, 3, 234, 129, 161, 126], "command_hex": "02 02 97 F1 00 11 CC", "response_hex": "00 00 03 EA 81 A1 7E"}, {"timestamp": 1747964462.4233189, "datetime": "2025-05-23 09:41:02", "command_bytes": [1, 18, 0, 0, 0, 0, 152], "response_bytes": [0, 129, 217, 129, 0, 1, 197], "command_hex": "01 12 00 00 00 00 98", "response_hex": "00 81 D9 81 00 01 C5"}, {"timestamp": 1747964835.002618, "datetime": "2025-05-23 09:47:15", "command_bytes": [1, 18, 0, 0, 0, 0, 152], "response_bytes": [0, 131, 43, 37, 0, 1, 67], "command_hex": "01 12 00 00 00 00 98", "response_hex": "00 83 2B 25 00 01 43"}, {"timestamp": 1748311705.553771, "datetime": "2025-05-27 10:08:25", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1748311719.7171113, "datetime": "2025-05-27 10:08:39", "command_bytes": [2, 8, 0, 0, 128, 0, 52], "response_bytes": [0, 0, 0, 207, 165, 161, 200], "command_hex": "02 08 00 00 80 00 34", "response_hex": "00 00 00 CF A5 A1 C8"}, {"timestamp": 1748317485.4072394, "datetime": "2025-05-27 11:44:45", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1748317547.50874, "datetime": "2025-05-27 11:45:47", "command_bytes": [2, 3, 248, 9, 32, 177, 162], "response_bytes": [0, 0, 6, 141, 128, 161, 156], "command_hex": "02 03 F8 09 20 B1 A2", "response_hex": "00 00 06 8D 80 A1 9C"}, {"timestamp": 1748317547.52139, "datetime": "2025-05-27 11:45:47", "command_bytes": [2, 4, 246, 228, 7, 59, 144], "response_bytes": [0, 0, 1, 38, 128, 161, 166], "command_hex": "02 04 F6 E4 07 3B 90", "response_hex": "00 00 01 26 80 A1 A6"}, {"timestamp": 1748320466.278704, "datetime": "2025-05-27 12:34:26", "command_bytes": [2, 3, 248, 9, 32, 177, 162], "response_bytes": [0, 0, 6, 141, 128, 161, 156], "command_hex": "02 03 F8 09 20 B1 A2", "response_hex": "00 00 06 8D 80 A1 9C"}, {"timestamp": 1748320466.286677, "datetime": "2025-05-27 12:34:26", "command_bytes": [2, 4, 246, 228, 7, 59, 144], "response_bytes": [0, 0, 1, 38, 128, 161, 166], "command_hex": "02 04 F6 E4 07 3B 90", "response_hex": "00 00 01 26 80 A1 A6"}, {"timestamp": 1748320466.293654, "datetime": "2025-05-27 12:34:26", "command_bytes": [1, 18, 0, 0, 0, 0, 152], "response_bytes": [0, 130, 245, 33, 0, 1, 98], "command_hex": "01 12 00 00 00 00 98", "response_hex": "00 82 F5 21 00 01 62"}, {"timestamp": 1748320606.2431586, "datetime": "2025-05-27 12:36:46", "command_bytes": [2, 3, 248, 9, 32, 177, 162], "response_bytes": [0, 0, 6, 141, 128, 161, 156], "command_hex": "02 03 F8 09 20 B1 A2", "response_hex": "00 00 06 8D 80 A1 9C"}, {"timestamp": 1748320606.2501276, "datetime": "2025-05-27 12:36:46", "command_bytes": [2, 4, 246, 228, 7, 59, 144], "response_bytes": [0, 0, 1, 38, 128, 161, 166], "command_hex": "02 04 F6 E4 07 3B 90", "response_hex": "00 00 01 26 80 A1 A6"}, {"timestamp": 1748320730.0547602, "datetime": "2025-05-27 12:38:50", "command_bytes": [2, 3, 248, 9, 34, 239, 162], "response_bytes": [0, 0, 6, 145, 161, 161, 151], "command_hex": "02 03 F8 09 22 EF A2", "response_hex": "00 00 06 91 A1 A1 97"}, {"timestamp": 1748320730.0617287, "datetime": "2025-05-27 12:38:50", "command_bytes": [2, 4, 246, 255, 168, 133, 144], "response_bytes": [0, 0, 1, 82, 161, 161, 195], "command_hex": "02 04 F6 FF A8 85 90", "response_hex": "00 00 01 52 A1 A1 C3"}, {"timestamp": 1748503693.6748693, "datetime": "2025-05-29 15:28:13", "command_bytes": [2, 80, 255, 255, 255, 255, 122], "response_bytes": [0, 0, 6, 69, 165, 161, 166], "command_hex": "02 50 FF FF FF FF 7A", "response_hex": "00 00 06 45 A5 A1 A6"}, {"timestamp": 1748503766.6603572, "datetime": "2025-05-29 15:29:26", "command_bytes": [2, 3, 255, 0, 214, 131, 241], "response_bytes": [0, 0, 4, 8, 161, 161, 129], "command_hex": "02 03 FF 00 D6 83 F1", "response_hex": "00 00 04 08 A1 A1 81"}, {"timestamp": 1748503766.6703403, "datetime": "2025-05-29 15:29:26", "command_bytes": [2, 4, 222, 0, 0, 0, 150], "response_bytes": [0, 0, 6, 92, 161, 161, 237], "command_hex": "02 04 DE 00 00 00 96", "response_hex": "00 00 06 5C A1 A1 ED"}, {"timestamp": 1748503766.6753373, "datetime": "2025-05-29 15:29:26", "command_bytes": [2, 5, 0, 150, 0, 0, 134], "response_bytes": [0, 0, 0, 217, 161, 161, 217], "command_hex": "02 05 00 96 00 00 86", "response_hex": "00 00 00 D9 A1 A1 D9"}, {"timestamp": 1748509268.9650884, "datetime": "2025-05-29 17:01:08", "command_bytes": [2, 3, 255, 0, 214, 131, 241], "response_bytes": [0, 0, 0, 224, 165, 161, 125], "command_hex": "02 03 FF 00 D6 83 F1", "response_hex": "00 00 00 E0 A5 A1 7D"}, {"timestamp": 1748509268.9750729, "datetime": "2025-05-29 17:01:08", "command_bytes": [2, 4, 222, 64, 1, 128, 109], "response_bytes": [0, 0, 3, 60, 165, 161, 25], "command_hex": "02 04 DE 40 01 80 6D", "response_hex": "00 00 03 3C A5 A1 19"}, {"timestamp": 1748509268.9850652, "datetime": "2025-05-29 17:01:08", "command_bytes": [2, 5, 3, 25, 0, 0, 225], "response_bytes": [0, 0, 5, 183, 165, 161, 121], "command_hex": "02 05 03 19 00 00 E1", "response_hex": "00 00 05 B7 A5 A1 79"}, {"timestamp": 1748511400.8201191, "datetime": "2025-05-29 17:36:40", "command_bytes": [2, 2, 55, 241, 0, 26, 199], "response_bytes": [0, 0, 2, 0, 129, 161, 116], "command_hex": "02 02 37 F1 00 1A C7", "response_hex": "00 00 02 00 81 A1 74"}]