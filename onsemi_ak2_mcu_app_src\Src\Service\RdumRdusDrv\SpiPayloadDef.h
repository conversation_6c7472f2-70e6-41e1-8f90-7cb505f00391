/******************************************************************************
 * @file      SpiPayloadDef.h
 * @brief     SPI命令PayloadData位定义
 * <AUTHOR>
 * @date      2025-05-14
 * @note
 *****************************************************************************/

#ifndef __SPI_PAYLOAD_DEF_H__
#define __SPI_PAYLOAD_DEF_H__
/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "types.h"



/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/
/* 地址0x00的PayloadData位定义 */
typedef union
{
    uint32 Value;
    uint8 Bytes[4];
    struct
    {
        uint8 PDCM_RSP_START_TIME : 6;  /* [29:24] PDCM响应开始时间 */
        uint8 TCHIP : 1;                /* [30] TCHIP */
        uint8 Reserved3 : 1;            /* [31] 保留 */

        uint8 Reserved2 : 1;            /* [23] 保留 */
        uint8 CLKIN_DET : 4;            /* [22:19] CLKIN检测 */
        uint8 Reserved1 : 2;            /* [18:17] 保留 */
        uint8 PDCM_RSP_FRM_LEN_H : 1;   /* [16] PDCM响应帧长度高位 */

        uint8 PDCM_RSP_FRM_LEN_L : 4;   /* [15:12] PDCM响应帧长度低位 */
        uint8 BRC_DELAYED : 4;          /* [11:8] BRC延迟 */

        uint8 CRM_END_TIME : 8;         /* [7:0] CRM结束时间 */
    } Bits;
} SpiPayload_Addr0x00_t;

/* 地址0x01的PayloadData位定义 */
typedef union
{
    uint32 Value;
    uint8 Bytes[4];
    struct
    {
        uint8 REV_ID : 8;               /* [31:24] 修订ID */

        uint8 VBSTOV_MASK : 1;          /* [23] VBSTOV掩码 */
        uint8 SLAVE_RX_FILTER_ENA : 2;  /* [22:21] 从机接收滤波使能 */
        uint8 DATA_REF : 1;             /* [20] 数据参考 */
        uint8 DATA_AMP : 1;             /* [19] 数据幅度 */
        uint8 HW_INT_MASK : 1;          /* [18] 硬件中断掩码 */
        uint8 CLKIN_FREQ_H : 2;         /* [17:16] CLKIN频率高位 */

        uint8 CLKIN_FREQ_L : 4;         /* [15:12] CLKIN频率低位 */
        uint8 Reserved1 : 3;            /* [11:9] 保留 */
        uint8 CRM_RSP_START_TIME_H : 1; /* [8] CRM响应开始时间高位 */

        uint8 CRM_RSP_START_TIME_L : 8; /* [7:0] CRM响应开始时间低位 */
    } Bits;
} SpiPayload_Addr0x01_t;

/* 地址0x02的PayloadData位定义 */
typedef union
{
    uint32 Value;
    uint8 Bytes[4];
    struct
    {
        uint8 BRC_INT_MASK : 8;         /* [27:20] BRC中断掩码 */
        uint8 CRM_INT_MASK : 1;         /* [19] CRM中断掩码 */
        uint8 PDCM_NUM_SLOTS : 3;       /* [18:16] PDCM时隙数 */
        uint8 TIMER_H : 4;              /* [15:12] 定时器高位 */

        uint8 TIMER_M : 4;              /* [11:8] 定时器中位 */
        uint8 TIMER_L : 1;              /* [7] 定时器低位 */
        uint8 Reserved1 : 3;            /* [6:4] 保留 */

        uint8 N_FRAMES : 4;             /* [3:0] 帧数 */
        uint8 BUS_DRV_ENA : 1;          /* [28] 总线驱动使能 */
        uint8 BURST_ENA : 1;            /* [29] 突发使能 */
        uint8 BRC_ENA : 1;              /* [30] BRC使能 */
        uint8 TX_START : 1;             /* [31] 发送开始 */
    } Bits;
} SpiPayload_Addr0x02_t;

/* 地址0x03-0x07的PayloadData位定义 */
typedef union
{
    uint32 Value;
    uint8 Bytes[4]; /* 4字节数组，用于按字节访问 */
    struct
    {
        uint8 TX_DATA_BYTE3 : 8;        /* [31:24] 发送数据字节3 */
        uint8 TX_DATA_BYTE2 : 8;        /* [23:16] 发送数据字节2 */
        uint8 TX_DATA_BYTE1 : 8;        /* [15:8] 发送数据字节1 */
        uint8 TX_DATA_BYTE0 : 8;        /* [7:0] 发送数据字节0 */
    } Bits;
} SpiPayload_AddrTxData_t;

/* 地址0x08的PayloadData位定义 */
typedef union
{
    uint32 Value;
    uint8 Bytes[4];
    struct
    {
        uint8 Reserved3_H : 6;          /* [29:24] 保留高位 */
        uint8 PSW_SLP : 2;              /* [31:30] PSW睡眠 */

        uint8 Reserved3_L : 8;          /* [23:16] 保留低位 */

        uint8 Reserved2 : 7;            /* [14:8] 保留 */
        uint8 PWRENA_CHA : 1;           /* [15] 通道A电源使能 */

        uint8 Reserved1 : 7;            /* [6:0] 保留 */
        uint8 PWRENA_CHB : 1;           /* [7] 通道B电源使能 */
    } Bits;
} SpiPayload_Addr0x08_t;

/* 地址0x09的PayloadData位定义 */
typedef union
{
    uint32 Value;
    uint8 Bytes[4];
    struct
    {
        uint8 Reserved_H1 : 8;          /* [31:24] 保留高位1 */
        uint8 Reserved_H2 : 8;          /* [23:16] 保留高位2 */
        uint8 Reserved_H3 : 8;          /* [15:8] 保留高位3 */
        uint8 Reserved_L : 6;           /* [7:2] 保留低位 */
        uint8 N_FRAMES_MSB : 2;         /* [1:0] 帧数MSB */
    } Bits;
} SpiPayload_Addr0x09_t;

/* 地址0x0B的PayloadData位定义 */
typedef union
{
    uint32 Value;
    uint8 Bytes[4];
    struct
    {
        uint8 VBST_UV_THR : 8;          /* [27:20] VBST欠压阈值 */
        uint8 Reserved : 3;             /* [30:28] 保留 */
        uint8 BST_ENA : 1;              /* [31] BST使能 */
        uint8 BST_FREQ_H : 4;           /* [19:16] BST频率高位 */

        uint8 BST_FREQ_L : 1;           /* [15] BST频率低位 */
        uint8 BST_VSETPOINT : 7;        /* [14:8] BST电压设定点 */

        uint8 BST_VLIM_THR : 8;         /* [7:0] BST电压限制阈值 */
    } Bits;
} SpiPayload_Addr0x0B_t;

/* 地址0x0C的PayloadData位定义 */
typedef union
{
    uint32 Value;
    uint8 Bytes[4];
    struct
    {
        uint8 BST_OV_SD : 3;            /* [26:24] BST过压关断 */
        uint8 Reserved6 : 1;            /* [27] 保留 */
        uint8 BST_OV_REACT : 2;         /* [29:28] BST过压反应 */
        uint8 Reserved7 : 2;            /* [31:30] 保留 */

        uint8 BST_SLP_CTRL : 3;         /* [22:20] BST睡眠控制 */
        uint8 Reserved5 : 1;            /* [23] 保留 */
        uint8 BST_DRV_SLP : 2;          /* [19:18] BST驱动睡眠 */
        uint8 BST_SKCL_THR : 2;         /* [17:16] BST SKCL阈值 */

        uint8 BST_OTA_GAIN : 2;         /* [13:12] BST OTA增益 */
        uint8 Reserved4 : 2;            /* [15:14] 保留 */
        uint8 BST_MIN_TON : 2;          /* [9:8] BST最小导通时间 */
        uint8 Reserved3 : 2;            /* [11:10] 保留 */

        uint8 BST_COMP_DIV : 3;         /* [2:0] BST比较器分频 */
        uint8 Reserved1 : 1;            /* [3] 保留 */
        uint8 BST_MIN_TOFF : 3;         /* [6:4] BST最小关断时间 */
        uint8 Reserved2 : 1;            /* [7] 保留 */
    } Bits;
} SpiPayload_Addr0x0C_t;

/* 地址0x0E的PayloadData位定义 */
typedef union
{
    uint32 Value;
    uint8 Bytes[4];
    struct
    {
        uint8 BST_SKCL_BEH : 3;         /* [26:24] BST SKCL行为 */
        uint8 Reserved5 : 5;            /* [31:27] 保留 */

        uint8 BST_SOFTSTART : 3;        /* [18:16] BST软启动 */
        uint8 Reserved4 : 5;            /* [23:19] 保留 */

        uint8 BSTSS_SEL : 2;            /* [9:8] BSTSS选择 */
        uint8 Reserved3 : 6;            /* [15:10] 保留 */

        uint8 BSTSS_PROFILE : 2;        /* [1:0] BSTSS配置文件 */
        uint8 Reserved1 : 2;            /* [3:2] 保留 */
        uint8 BSTSS_PERIOD : 1;         /* [4] BSTSS周期 */
        uint8 Reserved2 : 3;            /* [7:5] 保留 */
    } Bits;
} SpiPayload_Addr0x0E_t;

/* 地址0x10的PayloadData位定义 */
typedef union
{
    uint32 Value;
    uint8 Bytes[4];
    struct
    {
        uint8 Reserved3_H : 5;          /* [31:27] 保留高位 */
        uint8 Reserved3_M : 8;          /* [26:19] 保留中位 */

        uint8 BST_REGSTATUS : 2;        /* [17:16] BST调节状态 */
        uint8 BST_RUNNING : 1;          /* [18] BST运行 */
        uint8 Reserved3_L : 5;          /* [23:19] 保留低位 */

        uint8 Reserved2_H : 3;          /* [15:13] 保留高位 */
        uint8 Reserved2_L : 8;          /* [12:5] 保留低位 */

        uint8 BST_SKCL : 1;             /* [0] BST SKCL */
        uint8 BST_OSCFAIL : 1;          /* [1] BST振荡器失败 */
        uint8 BST_FBFAIL : 1;           /* [2] BST反馈失败 */
        uint8 Reserved1 : 1;            /* [3] 保留 */
        uint8 BST_VDRV_UV : 1;          /* [4] BST VDRV欠压 */
        uint8 Reserved2_M : 3;          /* [7:5] 保留中位 */
    } Bits;
} SpiPayload_Addr0x10_t;

/* 地址0x11的PayloadData位定义 */
typedef union
{
    uint32 Value;
    uint8 Bytes[4];
    struct
    {
        uint8 VDDAUV : 1;               /* [24] VDDA欠压 */
        uint8 OVCR1B : 1;               /* [25] OVCR1B */
        uint8 OVCR1T : 1;               /* [26] OVCR1T */
        uint8 OVCR2B : 1;               /* [27] OVCR2B */
        uint8 OVCR2T : 1;               /* [28] OVCR2T */
        uint8 OVCPW1 : 1;               /* [29] OVCPW1 */
        uint8 OVCPW2 : 1;               /* [30] OVCPW2 */
        uint8 HW_ERR : 1;               /* [31] 硬件错误 */

        uint8 VDDUV : 1;                /* [16] VDD欠压 */
        uint8 VDDOV : 1;                /* [17] VDD过压 */
        uint8 VDDA1UV : 1;              /* [18] VDDA1欠压 */
        uint8 VDDA1OV : 1;              /* [19] VDDA1过压 */
        uint8 VDDA2UV : 1;              /* [20] VDDA2欠压 */
        uint8 VDDA2OV : 1;              /* [21] VDDA2过压 */
        uint8 VDRVUV : 1;               /* [22] VDRV欠压 */
        uint8 VDRVOV : 1;               /* [23] VDRV过压 */

        uint8 VIOUV : 1;                /* [14] VIO欠压 */
        uint8 VIOOV : 1;                /* [15] VIO过压 */
        uint8 VBSTUV : 1;               /* [12] VBST欠压 */
        uint8 VBSTOV : 1;               /* [13] VBST过压 */
        uint8 TSD1 : 1;                 /* [10] 热关断1 */
        uint8 TSD2 : 1;                 /* [11] 热关断2 */
        uint8 Reserved1_H : 2;          /* [9:8] 保留高位 */

        uint8 HWR : 1;                  /* [0] 硬件复位 */
        uint8 GNDL : 1;                 /* [1] 接地丢失 */
        uint8 OSCFAIL : 1;              /* [2] 振荡器失败 */
        uint8 OSCLCK_FAIL : 1;          /* [3] 振荡器锁定失败 */
        uint8 TSD : 1;                  /* [4] 热关断 */
        uint8 TW : 1;                   /* [5] 热警告 */
        uint8 BSTFAIL : 1;              /* [6] BST失败 */
        uint8 Reserved1_L : 1;          /* [7] 保留低位 */
    } Bits;
} SpiPayload_Addr0x11_t;

/* 地址0x12的PayloadData位定义 */
typedef union
{
    uint32 Value;
    uint8 Bytes[4];
    struct
    {
        uint8 CLKIN_CNT_H : 1;          /* [31] CLKIN计数高位 */
        uint8 CLKIN_CNT_L : 8;          /* [30:23] CLKIN计数低位 */

        uint8 SYMBOL_ERR : 1;           /* [22] 符号错误 */
        uint8 FRAME_TO : 1;             /* [21] 帧超时 */
        uint8 RX_TO : 1;                /* [20] 接收超时 */
        uint8 HW_ERR_2 : 1;             /* [19] 硬件错误2 */
        uint8 BURST_DONE : 1;           /* [18] 突发完成 */
        uint8 TIMER_INT : 1;            /* [17] 定时器中断 */
        uint8 CRM_RCVD : 1;             /* [16] CRM接收 */
        uint8 CLKIN_CNT_M : 1;          /* [23] CLKIN计数中位 */

        uint8 BRC_RCVD : 8;             /* [15:8] BRC接收 */

        uint8 BUS_AVAIL : 1;            /* [0] 总线可用 */
        uint8 Reserved1 : 7;            /* [7:1] 保留 */
    } Bits;
} SpiPayload_Addr0x12_t;

/* 地址0x13的PayloadData位定义 */
typedef union
{
    uint32 Value;
    uint8 Bytes[4];
    struct
    {
        uint8 VBST_H : 1;               /* [28] VBST电压高位 */
        uint8 Reserved3 : 3;            /* [31:29] 保留 */
        uint8 VBST_M : 4;               /* [27:24] VBST电压中位 */

        uint8 VBST_L : 4;               /* [23:20] VBST电压低位 */
        uint8 Reserved2 : 1;            /* [19] 保留 */
        uint8 VDD_H : 3;                /* [18:16] VDD电压高位 */

        uint8 VDD_M : 6;                /* [15:10] VDD电压中位 */
        uint8 Reserved1 : 1;            /* [9] 保留 */
        uint8 VDRV_H : 1;               /* [8] VDRV电压高位 */

        uint8 VDRV_L : 8;               /* [7:0] VDRV电压低位 */
    } Bits;
} SpiPayload_Addr0x13_t;

/* 地址0x14的PayloadData位定义 */
typedef union
{
    uint32 Value;
    uint8 Bytes[4];
    struct
    {
        uint8 VDATA1_H : 1;             /* [28] VDATA1电压高位 */
        uint8 Reserved3 : 3;            /* [31:29] 保留 */
        uint8 VDATA1_M : 4;             /* [27:24] VDATA1电压中位 */

        uint8 VDATA1_L : 4;             /* [23:20] VDATA1电压低位 */
        uint8 Reserved2 : 1;            /* [19] 保留 */
        uint8 VDATA2_H : 3;             /* [18:16] VDATA2电压高位 */

        uint8 VDATA2_M : 6;             /* [15:10] VDATA2电压中位 */
        uint8 Reserved1 : 1;            /* [9] 保留 */
        uint8 VIO_H : 1;                /* [8] VIO电压高位 */

        uint8 VIO_L : 8;                /* [7:0] VIO电压低位 */
    } Bits;
} SpiPayload_Addr0x14_t;

/* 地址0x15的PayloadData位定义 */
typedef union
{
    uint32 Value;
    uint8 Bytes[4];
    struct
    {
        uint8 TEMP_H : 1;               /* [28] 温度高位 */
        uint8 Reserved3 : 3;            /* [31:29] 保留 */
        uint8 TEMP_M : 4;               /* [27:24] 温度中位 */

        uint8 TEMP_L : 4;               /* [23:20] 温度低位 */
        uint8 Reserved2 : 1;            /* [19] 保留 */
        uint8 VDDA_D1_H : 3;            /* [18:16] VDDA_D1电压高位 */

        uint8 VDDA_D1_M : 6;            /* [15:10] VDDA_D1电压中位 */
        uint8 Reserved1 : 1;            /* [9] 保留 */
        uint8 VDDA_D2_H : 1;            /* [8] VDDA_D2电压高位 */

        uint8 VDDA_D2_L : 8;            /* [7:0] VDDA_D2电压低位 */
    } Bits;
} SpiPayload_Addr0x15_t;

/* 地址0x16的PayloadData位定义 */
typedef union
{
    uint32 Value;
    uint8 Bytes[4];
    struct
    {
        uint8 Reserved_H : 8;           /* [31:24] 保留高位 */
        uint8 Reserved_L : 8;           /* [23:16] 保留低位 */
        uint8 IDATA1 : 8;               /* [15:8] IDATA1电流 */
        uint8 IDATA2 : 8;               /* [7:0] IDATA2电流 */
    } Bits;
} SpiPayload_Addr0x16_t;

/* 地址0x17的PayloadData位定义 */
typedef union
{
    uint32 Value;
    uint8 Bytes[4];
    struct
    {
        uint8 DEVID_LOT_H : 4;          /* [27:24] 设备ID LOT高位 */
        uint8 DEVID_FAB : 2;            /* [29:28] 设备ID FAB */
        uint8 Reserved : 2;             /* [31:30] 保留 */

        uint8 DEVID_LOT_L : 8;          /* [23:16] 设备ID LOT低位 */
        uint8 DEVID_Y : 8;              /* [15:8] 设备ID Y */
        uint8 DEVID_X : 8;              /* [7:0] 设备ID X */
    } Bits;
} SpiPayload_Addr0x17_t;

/* 地址0x18的PayloadData位定义 */
typedef union
{
    uint32 Value;
    uint8 Bytes[4];
    struct
    {
        uint8 Reserved_H : 8;           /* [31:24] 保留高位 */
        uint8 Reserved_L : 8;           /* [23:16] 保留低位 */

        uint8 DEVID_WAF : 5;            /* [15:11] 设备ID WAF */
        uint8 DEVID_YEAR_H : 3;         /* [10:8] 设备ID年高位 */

        uint8 DEVID_YEAR_L : 2;         /* [7:6] 设备ID年低位 */
        uint8 DEVID_WEEK : 6;           /* [5:0] 设备ID周 */
    } Bits;
} SpiPayload_Addr0x18_t;

/* 地址0x4F的PayloadData位定义 */
typedef union
{
    uint32 Value;
    uint8 Bytes[4];
    struct
    {
        uint8 Reserved2_H1 : 8;         /* [31:24] 保留高位1 */
        uint8 Reserved2_H2 : 8;         /* [23:16] 保留高位2 */
        uint8 Reserved2_H3 : 8;         /* [15:8] 保留高位3 */

        uint8 CLR_BST_SKCL : 1;         /* [0] 清除BST SKCL */
        uint8 CLR_BST_OSCFAIL : 1;      /* [1] 清除BST振荡器失败 */
        uint8 CLR_BST_FBFAIL : 1;       /* [2] 清除BST反馈失败 */
        uint8 Reserved1 : 1;            /* [3] 保留 */
        uint8 CLR_BST_VDRV_UV : 1;      /* [4] 清除BST VDRV欠压 */
        uint8 Reserved2_L : 3;          /* [7:5] 保留低位 */
    } Bits;
} SpiPayload_Addr0x4F_t;

/* 地址0x50的PayloadData位定义 */
typedef union
{
    uint32 Value;
    uint8 Bytes[4];
    struct
    {
        uint8 CLR_VDDAUV : 1;           /* [24] 清除VDDA欠压 */
        uint8 CLR_OVCR1B : 1;           /* [25] 清除OVCR1B */
        uint8 CLR_OVCR1T : 1;           /* [26] 清除OVCR1T */
        uint8 CLR_OVCR2B : 1;           /* [27] 清除OVCR2B */
        uint8 CLR_OVCR2T : 1;           /* [28] 清除OVCR2T */
        uint8 CLR_OVCPW1 : 1;           /* [29] 清除OVCPW1 */
        uint8 CLR_OVCPW2 : 1;           /* [30] 清除OVCPW2 */
        uint8 Reserved2 : 1;            /* [31] 保留 */

        uint8 CLR_VDDUV : 1;            /* [16] 清除VDD欠压 */
        uint8 CLR_VDDOV : 1;            /* [17] 清除VDD过压 */
        uint8 CLR_VDDA1UV : 1;          /* [18] 清除VDDA1欠压 */
        uint8 CLR_VDDA1OV : 1;          /* [19] 清除VDDA1过压 */
        uint8 CLR_VDDA2UV : 1;          /* [20] 清除VDDA2欠压 */
        uint8 CLR_VDDA2OV : 1;          /* [21] 清除VDDA2过压 */
        uint8 CLR_VDRVUV : 1;           /* [22] 清除VDRV欠压 */
        uint8 CLR_VDRVOV : 1;           /* [23] 清除VDRV过压 */

        uint8 CLR_VIOUV : 1;            /* [14] 清除VIO欠压 */
        uint8 CLR_VIOOV : 1;            /* [15] 清除VIO过压 */
        uint8 CLR_VBSTUV : 1;           /* [12] 清除VBST欠压 */
        uint8 CLR_VBSTOV : 1;           /* [13] 清除VBST过压 */
        uint8 CLR_TSD1 : 1;             /* [10] 清除热关断1 */
        uint8 CLR_TSD2 : 1;             /* [11] 清除热关断2 */
        uint8 Reserved1_H : 2;          /* [9:8] 保留高位 */

        uint8 CLR_HWR : 1;              /* [0] 清除硬件复位 */
        uint8 CLR_GNDL : 1;             /* [1] 清除接地丢失 */
        uint8 CLR_OSCFAIL : 1;          /* [2] 清除振荡器失败 */
        uint8 CLR_OSCLCK_FAIL : 1;      /* [3] 清除振荡器锁定失败 */
        uint8 CLR_TSD : 1;              /* [4] 清除热关断 */
        uint8 CLR_TW : 1;               /* [5] 清除热警告 */
        uint8 Reserved1_L : 2;          /* [7:6] 保留低位 */
    } Bits;
} SpiPayload_Addr0x50_t;

/* 地址0x51的PayloadData位定义 */
typedef union
{
    uint32 Value;
    uint8 Bytes[4];
    struct
    {
        uint8 Reserved4_H : 1;          /* [31] 保留高位 */
        uint8 Reserved4_L : 8;          /* [30:23] 保留低位 */

        uint8 CLR_SYMBOL_ERR : 1;       /* [22] 清除符号错误 */
        uint8 CLR_FRAME_TO : 1;         /* [21] 清除帧超时 */
        uint8 CLR_RX_TO : 1;            /* [20] 清除接收超时 */
        uint8 Reserved3 : 1;            /* [19] 保留 */
        uint8 CLR_BURST_DONE : 1;       /* [18] 清除突发完成 */
        uint8 Reserved2 : 1;            /* [17] 保留 */
        uint8 CLR_CRM_RCVD : 1;         /* [16] 清除CRM接收 */
        uint8 Reserved4_M : 1;          /* [23] 保留中位 */

        uint8 Reserved1_H : 8;          /* [15:8] 保留高位 */
        uint8 Reserved1_L : 8;          /* [7:0] 保留低位 */
    } Bits;
} SpiPayload_Addr0x51_t;

/* 统一的PayloadData位定义 */
typedef union
{
    uint32 Value;                       /* 32位值 */
    uint8 Bytes[4];                     /* 4字节数组 */
    SpiPayload_Addr0x00_t Addr0x00;     /* 地址0x00的位定义 */
    SpiPayload_Addr0x01_t Addr0x01;     /* 地址0x01的位定义 */
    SpiPayload_Addr0x02_t Addr0x02;     /* 地址0x02的位定义 */
    SpiPayload_AddrTxData_t AddrTxData; /* 地址0x03-0x07的位定义 */
    SpiPayload_Addr0x08_t Addr0x08;     /* 地址0x08的位定义 */
    SpiPayload_Addr0x09_t Addr0x09;     /* 地址0x09的位定义 */
    SpiPayload_Addr0x0B_t Addr0x0B;     /* 地址0x0B的位定义 */
    SpiPayload_Addr0x0C_t Addr0x0C;     /* 地址0x0C的位定义 */
    SpiPayload_Addr0x0E_t Addr0x0E;     /* 地址0x0E的位定义 */
    SpiPayload_Addr0x10_t Addr0x10;     /* 地址0x10的位定义 */
    SpiPayload_Addr0x11_t Addr0x11;     /* 地址0x11的位定义 */
    SpiPayload_Addr0x12_t Addr0x12;     /* 地址0x12的位定义 */
    SpiPayload_Addr0x13_t Addr0x13;     /* 地址0x13的位定义 */
    SpiPayload_Addr0x14_t Addr0x14;     /* 地址0x14的位定义 */
    SpiPayload_Addr0x15_t Addr0x15;     /* 地址0x15的位定义 */
    SpiPayload_Addr0x16_t Addr0x16;     /* 地址0x16的位定义 */
    SpiPayload_Addr0x17_t Addr0x17;     /* 地址0x17的位定义 */
    SpiPayload_Addr0x18_t Addr0x18;     /* 地址0x18的位定义 */
    SpiPayload_Addr0x4F_t Addr0x4F;     /* 地址0x4F的位定义 */
    SpiPayload_Addr0x50_t Addr0x50;     /* 地址0x50的位定义 */
    SpiPayload_Addr0x51_t Addr0x51;     /* 地址0x51的位定义 */
} SpiPayloadData_t;

// 统一SpiPayloadData_t的所有地址定义为宏
#define SPI_CMD_ADDR_R0 0x00
#define SPI_CMD_ADDR_R1 0x01
#define SPI_CMD_ADDR_R2 0x02
#define SPI_CMD_ADDR_TX_DATA0 0x03
#define SPI_CMD_ADDR_TX_DATA1 0x04
#define SPI_CMD_ADDR_TX_DATA2 0x05
#define SPI_CMD_ADDR_TX_DATA3 0x06
#define SPI_CMD_ADDR_TX_DATA4 0x07
#define SPI_CMD_ADDR_PCR 0x08
#define SPI_CMD_ADDR_AOD 0x09
#define SPI_CMD_ADDR_BSTCTRL1 0x0B
#define SPI_CMD_ADDR_BSTCTRL2 0x0C
#define SPI_CMD_ADDR_BSTSSCTRL 0x0E
#define SPI_CMD_ADDR_BSTFLAG 0x10
#define SPI_CMD_ADDR_HWFLAG 0x11
#define SPI_CMD_ADDR_STATUS 0x12
#define SPI_CMD_ADDR_ADC_VDD 0x13
#define SPI_CMD_ADDR_ADC_VDATA 0x14
#define SPI_CMD_ADDR_ADC_VDDA 0x15
#define SPI_CMD_ADDR_ADC_IDATA 0x16
#define SPI_CMD_ADDR_DEVID_CODE 0x17
#define SPI_CMD_ADDR_DEVID_DATA 0x18
#define SPI_CMD_ADDR_CLR_BSTFLAG 0x4F
#define SPI_CMD_ADDR_CLR_HWFLAG 0x50
#define SPI_CMD_ADDR_CLR_STATUS 0x51


/******************************************************************************
 * @Const Declaration
 *****************************************************************************/



/******************************************************************************
 * @Variable Declaration
 *****************************************************************************/



/******************************************************************************
 * @Function Declaration
 *****************************************************************************/



#endif /* __SPI_PAYLOAD_DEF_H__ */
