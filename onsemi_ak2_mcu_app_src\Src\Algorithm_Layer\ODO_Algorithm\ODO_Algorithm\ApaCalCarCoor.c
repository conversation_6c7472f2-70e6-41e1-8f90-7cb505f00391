/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * ApaCalCarCoor.c: 
 * Created on: 2020-12-07 19:09
 * Original designer: 22554
 ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "ApaCalCarCoor.h"
#include "ApaCalCarCoor_Privated.h"
#include "ODO_CalibPara.h"
#include "CAN_AppSignalManage.h"
#include "ODO_AppSignalManage.h"
#include "SnsRawData_Int.h"
#include "eel_cfg.h"

/******************************************************************************/
/******************************************************************************/
/****************************** Private Definition ****************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************************/
/******************************************************************************/
/****************************** Public Definition *****************************/
/******************************************************************************/
/******************************************************************************/
static CPOS_CaliParamValidType GeCaliParamValidSts = CALI_PARAM_INVALID;



/******************************************************************************/
/******************************************************************************/
/****************************** Private Function ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/******************************* Public Function ******************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************************
 * 描     述: 初始化
 * 设计索引:
 *****************************************************************************/
void CPOS_Init(void)
{
    /* 获取配置参数有效性 */
	if(CALIBRATION_EN == STD_OFF){
	ODO_Car_CalibPara_Config_Init();
	}
    CPOS_GetCaliParamValidSts(&GeCaliParamValidSts);
    CPOS_Privated_Init(&GstrAPA_OdomCarPara_Ram);
}


/******************************************************************
* 函数名称: CPOS_DataUpdate
*
* 功能描述: Apa车身坐标定位模块数据更新 ，10ms更新一次
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 无
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
void CPOS_DataUpdate(void)
{
    CPOS_CalPointNeedDataType LvpCalPointNeedData;
    Car_WheelPulseType LpstrCar_WheelInf;

	
    /* get the Steering wheel angle, positive: left.  Negative:right*/
    Car_StrAngleType Ls16Car_StrAngle;
    ReadCAN_AppSignal_Car_StrAngle(&Ls16Car_StrAngle);
	if(Ls16Car_StrAngle == CAN_SIG_INVALID_STR_ANGLE)  // invalid  Steering wheel angle	
	{
		LvpCalPointNeedData.u16SteerWheelAngleVaild = FALSE;
		return;
	}
	LvpCalPointNeedData.u16SteerWheelAngleVaild = TRUE;
	if (Ls16Car_StrAngle > 0 )
	{	
		/* Left : 0   right: 1 */
		LvpCalPointNeedData.u8EpsDirection = 0;
		LvpCalPointNeedData.u16EpsAngle  =	Ls16Car_StrAngle*10;
	}
	else
	{
		/*  right: 1      */
		LvpCalPointNeedData.u8EpsDirection = 1;
		LvpCalPointNeedData.u16EpsAngle  = 0 - Ls16Car_StrAngle*10;
	}
	
    /* get vehicle speed */
    LvpCalPointNeedData.u16VehicleSpeed = CPOS_GetCar_Speed();
	if (LvpCalPointNeedData.u16VehicleSpeed == CAN_SIG_INVALID_SPD_VALUE) //invalid speed.
	{
		return;
	}

    /* get vehicle Gear */
    Car_GearType LenuCar_Gear;  
    ReadCAN_AppSignal_Car_Gear(&LenuCar_Gear);
	if (LenuCar_Gear  == CAN_CAR_GEAR_NONE )  // invalid gear
	{
		return;
	}
    LvpCalPointNeedData.enuVehicleGear = LenuCar_Gear;

	
	/* get the wheel pulse direction, count  &  validity*/
	ReadCAN_AppSignal_Car_WheelPulseAndDir(&LpstrCar_WheelInf,CAR_RL_WHEEL);
	// if(LpstrCar_WheelInf.u16Car_WheelPulseCnt == CAN_SIG_INVALID_PULSE_CNT)
	// {
	// 		LvpCalPointNeedData.u16HLWheelPulseValid = FALSE;
	// 		return; // invalid wheel pulse
	// }
	LvpCalPointNeedData.u8RLWheelDir = LpstrCar_WheelInf.enuCar_WheelPulseDir;
	LvpCalPointNeedData.u16HLWheelPulse = LpstrCar_WheelInf.u16Car_WheelPulseCnt;
	LvpCalPointNeedData.u16HLWheelPulseValid = TRUE;
	
	ReadCAN_AppSignal_Car_WheelPulseAndDir(&LpstrCar_WheelInf,CAR_RR_WHEEL);
	// if(LpstrCar_WheelInf.u16Car_WheelPulseCnt == CAN_SIG_INVALID_PULSE_CNT)
	// {
	// 	    LvpCalPointNeedData.u16HRWheelPulseValid = FALSE; 
	//    	    return; // invalid wheel pulse
	// }
	LvpCalPointNeedData.u8RRWheelDir = LpstrCar_WheelInf.enuCar_WheelPulseDir;
	LvpCalPointNeedData.u16HRWheelPulse = LpstrCar_WheelInf.u16Car_WheelPulseCnt;
	LvpCalPointNeedData.u16HRWheelPulseValid = TRUE; 

    /* get the straight flag */
    LvpCalPointNeedData.u8StraightFlag = CPOS_GetStraight();
    CPOS_Privated_DataUpdate(&LvpCalPointNeedData);
}


/******************************************************************************
 * 描     述:执行车身坐标计算主函数入口，10ms执行一次
 * 设计索引:
 *****************************************************************************/
void CPOS_Task(void)
{
    /* 配置参数有效时才做数据处理 */
    if(CALI_PARAM_VALID == GeCaliParamValidSts)
    {
        CPOS_DataUpdate();       /* 获取接口数据 */
        CPOS_Privated_CalCurCarPointFunc();
		CalODOCoordinate();
    }
    PDCSnsOdoSendToCAN();
}


/******************************************************************************
 * 描     述:使能车身坐标计算，获取ID号
 * 设计索引:
 *****************************************************************************/
uint8 CPOS_SetEnableCalFlg(void)
{
    return CPOS_Privated_SetEnableCalFlg();
}


/******************************************************************************
 * 描     述:清除车身坐标计算，输入ID号
 * 设计索引:
 *****************************************************************************/
void CPOS_ClrEnableCalFlg(uint8 id)
{
    CPOS_Privated_ClrEnableCalFlg(id);
}



