/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * All Rights Reserved.
 *
 * FileName : SbcCtrl.h
 * Date     : 2021-07-13 10:42:09
 * Author   : Fris
 ******************************************************************************/
#ifndef __SBC_CTRL_H__
#define __SBC_CTRL_H__
/******************************************************************************
 * Inclusion of other module header files
 ******************************************************************************/
#include "types.h"
#include "CanTrcv_fs23_Ip.h"

/*----------------------------------------------------------------------------*/
typedef enum
{
	Sbc_Temperature_Normal = 0,
	Sbc_Temperature_Over	
}SbcTempSts_e;

typedef enum
{
    SBC_NO_WU_EVENT = 0,
    SBC_WK1_WU,
    SBC_WK2_WU,
    SBC_HVIO1_WU,
    SBC_HVIO2_WU,
    SBC_LVIO3_WU,
    SBC_LVIO4_WU,
    SBC_LVIO5_WU,
    SBC_CAN_WU,
    SBC_LIN_WU,
    SBC_LDT_WU,
    SBC_GO_TO_NORMAL_WU,
    SBC_GO_INT_TO_WU,
    SBC_V1_UVLP_WU,
    SBC_WD_OFL_WU,
    SBC_EXT_RSTB_WU
}Sbc_WakeupReasonType;

typedef enum
{
    SBC_IO_LOW = 0,
	SBC_IO_HIGH,
	SBC_IO_NONE
}Sbc_IOSts;

typedef enum
{
	SBC_TRCVMODE_OFF = 0,
	SBC_TRCVMODE_LISTENONLY,    
	SBC_TRCVMODE_NORMAL, 
    SBC_TRCVMODE_INVALID
} Sbc_TrcvModeType;

/******************************************************************************/
/**
* @brief: 全局变量对外声明
**/
/******************************************************************************/


/******************************************************************************/
/**
* @brief: 函数对外声明
**/
/******************************************************************************/
extern void SbcCtrl_Init(void);
extern void SbcCtrl_NormalFeedWatchdog(void);
extern Std_ReturnType SbcCtrl_DisableWatchdog(void);
extern void SbcCtrl_Task(void);
extern void SbcCtrl_SetSbcGoToLPOFF(void);
extern void SbcCtrl_SetCANMode(Sbc_TrcvModeType TrcvMode);
extern Sbc_TrcvModeType SbcCtrl_GetCANMode(void);
extern Sbc_WakeupReasonType SbcCtrl_GetWakeUpReason(void);
extern Sbc_IOSts SbcCtrl_GetWake1IOSts(void);
extern void SbcCtrl_ReadTempSts(SbcTempSts_e *pSts);
extern void SbcCtrl_ReadWDErrFlg(uint8_t* Lu8WDErrFlg);

#endif 
/******************************************************************************/
/*END SbcCtrl.h*/

