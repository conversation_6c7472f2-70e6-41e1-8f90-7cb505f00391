/*
 * Queue_CRMResponse.c
 *
 *  Created on: 2021年3月30日
 *      Author: 6000021992
 */

#include "Queue_CRMResponse.h"
#include <string.h>

CRM_RES_Que_Str CRM_RES_Que[DSIMasterNum];
#define CRM_RES_Que_TimeOut 10u
/***************************************************************************//**
 * @brief
 * 初始化CRM应答消息队列
 *
 * @param       pQueue        消息队列地址
 * @return      0          数据有效，且写入成功
 *              0xFFFF               else
 *
 * ****************************************************************************/
uint16 InitCRMRESQueue(DSIMasterID_en LeDSIMasterID)
{
    CRM_RES_Que_Str *pQueue;

    pQueue = &CRM_RES_Que[LeDSIMasterID];
    memset(pQueue,0,sizeof(CRM_RES_Que_Str));
    pQueue->wQueSize = CRM_RES_QUEUQ_SIZE;
    return 0;
}


uint16 PutCRMRESData(DSIMasterID_en DSIMasterID,DSIChlID_en DSIChlID,CRM_DSIChlRES_str *pInData)
{

    CRM_RESPONSE_Data_str *pQueueDataBuff;
    QueueDataStatus_en *pQueDataStatus;
    uint16 *pTimeCnt;
    uint16 LcwQueSize = 0;
    uint16 FlgCnt = 0xFFFF;
    CRM_RES_Que_Str *pQueue;
    if((pInData == NULL))
    {
        return 0xFFFF;
    }
    pQueue = &CRM_RES_Que[DSIMasterID];
    pQueDataStatus = pQueue->QueDataStatus;
    pQueueDataBuff = pQueue->QueueDataBuff;
    LcwQueSize = pQueue->wQueSize;
    pTimeCnt = pQueue->QueueTimeOutCnt;

    if(pQueue->WriteCnt >= LcwQueSize)
    {
        pQueue->WriteCnt = 0;
    }

    if(pQueDataStatus[pQueue->WriteCnt] == QueueData_IDLE)
    {
        FlgCnt = pQueue->WriteCnt;
        
        pQueueDataBuff[FlgCnt].DSIMasterID = DSIMasterID;
        pQueueDataBuff[FlgCnt].DSIChlID = DSIChlID;
        memcpy(&pQueueDataBuff[FlgCnt].ResData,pInData,sizeof(CRM_DSIChlRES_str));

        pQueDataStatus[FlgCnt] = QueueData_READY;

        pTimeCnt[FlgCnt] = 0;

        if(pQueue->WriteCnt < LcwQueSize)
        {
            pQueue->WriteCnt ++;
        }
        else
        {
            pQueue->WriteCnt = 0;
        }


        return FlgCnt;

    }
    else
    {

    }

    return 0xFFFF;





}

QueueDataStatus_en GetCRMRESQueueStatus(DSIMasterID_en LeDSIMasterID,uint16 wQueNum)
{
    if(wQueNum >= CRM_RES_Que[LeDSIMasterID].wQueSize)
    {
        return QueueData_NULL;
    }
    return CRM_RES_Que[LeDSIMasterID].QueDataStatus[wQueNum];
}

uint16 GetCRMRESData(DSIMasterID_en LeDSIMasterID,CRM_RESPONSE_Data_str *pOutData)
{
    CRM_RESPONSE_Data_str *pQueueDataBuff;
    QueueDataStatus_en *pQueDataStatus;
    uint16 *pTimeCnt;
    uint16 LcwQueSize = 0;
    uint16 FlgCnt = 0xFFFF;
    CRM_RES_Que_Str *pQueue;

    if(pOutData == NULL)
    {
        return 0xFFFF;
    }
    pQueue = &CRM_RES_Que[LeDSIMasterID];
    pQueDataStatus = pQueue->QueDataStatus;
    pQueueDataBuff = pQueue->QueueDataBuff;
    LcwQueSize = pQueue->wQueSize;
    pTimeCnt = pQueue->QueueTimeOutCnt;


    if(pQueue->ReadCnt >= LcwQueSize)
    {
        pQueue->ReadCnt = 0;
    }
    if(pQueDataStatus[pQueue->ReadCnt] == QueueData_READY)
    {
        FlgCnt = pQueue->ReadCnt;
        pQueue->ReadCnt ++;
    }
    

    if(FlgCnt != 0xFFFF)
    {
        memcpy(pOutData,&pQueueDataBuff[FlgCnt],sizeof(CRM_RESPONSE_Data_str));

        pQueDataStatus[FlgCnt] = QueueData_IDLE;
        pTimeCnt[FlgCnt] = 0;

        return FlgCnt;
    }
    else
    {
        return 0xFFFF;
    }


}

uint8 CRMRESQueue1msHook(CRM_RES_Que_Str *pQueue)
{
    uint16 LwCnt = 0;
    QueueDataStatus_en *pQueDataStatus;
    uint16 *pTimeCnt;
    uint16 LcwQueSize = 0;
    if(pQueue == NULL)
    {
        return 0xFF;
    }

    pQueDataStatus = pQueue->QueDataStatus;
    LcwQueSize = pQueue->wQueSize;
    pTimeCnt = pQueue->QueueTimeOutCnt;

    for(LwCnt = 0;LwCnt < LcwQueSize;LwCnt ++)
    {
        if(pQueDataStatus[LwCnt] == QueueData_READY)
        {
            
            if(pTimeCnt[LwCnt] < CRM_RES_Que_TimeOut)
            {
                pTimeCnt[LwCnt]++;

            }
            else
            {
                /*超时处理*/

            }
            
            
        }
    }
    return 0;
}
