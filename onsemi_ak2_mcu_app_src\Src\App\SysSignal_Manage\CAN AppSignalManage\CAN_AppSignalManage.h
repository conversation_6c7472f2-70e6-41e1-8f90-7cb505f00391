/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * CAN_AppSignalManage:
 * Created on: 2022-11-22 16:32
 * Original designer: AntonyFang
 ******************************************************************************/
#ifndef CAN_AppSignalManage_H
#define CAN_AppSignalManage_H

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "CAN_AppSignal_CommonTypes.h"


/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
void ReadCAN_AppSignal_Car_Gear(Car_GearType *LpvCar_Gear);
void WriteCAN_AppSignal_Car_Gear(Car_GearType *LpvCar_Gear);

void ReadCAN_AppSignal_Car_Speed(Car_SpeedType *LpvCar_Speed);
void WriteCAN_AppSignal_Car_Speed(Car_SpeedType *LpvCar_Speed);
void ReadSysSignal_Car_SpeedVild(Car_SpeedVildType *LpvCar_SpeedVild);
void WriteSysSignal_Car_SpeedVild(Car_SpeedVildType *LpvCar_SpeedVild);
void ReadSysSignal_Car_Speed_phy(Car_SpeedType_Phy *LpvCar_Speed);
void WriteSysSignal_Car_Speed_phy(Car_SpeedType_Phy *LpvCar_Speed);
void ReadSysSignal_Car_SpeedForSnapshotData(Car_SpeedType *LpvCar_SpeedForSnapshotData);
void WriteSysSignal_Car_SpeedForSnapshotData(Car_SpeedType *LpvCar_SpeedForSnapshotData);


void ReadCAN_AppSignal_Car_WheelPulseAndDir(Car_WheelPulseType *LpstrCar_WheelInf,Car_WheelIndexType LenuWheelInx);
void WriteCAN_AppSignal_Car_WheelPulseAndDir(Car_WheelPulseType *LpstrCar_WheelInf,Car_WheelIndexType LenuWheelInx);
void WriteCAN_AppSignal_Car_WheelPulse(Car_WheelPulseCntType *Lpu16WheelPulseCnt,Car_WheelIndexType LenuWheelInx);
void WriteCAN_AppSignal_Car_WheelDir(Car_WheelPulseDirType *LpenuWheelPulseDir,Car_WheelIndexType LenuWheelInx);

void ReadCAN_AppSignal_Car_StrAngle(Car_StrAngleType *LpvCar_StrAngle);
void WriteCAN_AppSignal_Car_StrAngle(Car_StrAngleType *LpvCar_StrAngle);

void ReadCAN_AppSignal_Car_AmbientTempType(Car_AmbientTempType *LpvCar_AmbientTemp);
void WriteCAN_AppSignal_Car_AmbientTempType(Car_AmbientTempType *LpvCar_AmbientTemp);

void ReadCAN_AppSignal_Car_Time(Car_TimeType *LpvCar_Time);
void WriteCAN_AppSignal_Car_Time(Car_TimeType *LpvCar_Time);

void ReadCAN_AppSignal_Car_EPB_Sts(Car_EPB_StsType *LpvEPB_Sts);
void WriteCAN_AppSignal_Car_EPB_Sts(Car_EPB_StsType *LpvEPB_Sts);

void ReadCAN_AppSignal_Wheel_Speed(Wheel_SpeedType *LpvWheel_Speed,Car_WheelIndexType LenuWheelInx);
void WriteCAN_AppSignal_Wheel_Speed(Wheel_SpeedType *LpvWheel_Speed,Car_WheelIndexType LenuWheelInx);

void KalmanFilter_Car_Yaw_Rate(Car_Yaw_RateType *LpfCar_YawRate, float Lfu, float interval);
void KalmanFilter_Init(VehicleTypeType LenuVEH_Type, VehicleWheelType LenuVehicleWheel);
void ReadCAN_AppSignal_Car_Yaw_Angle(Car_Yaw_AngleType *LpfCar_YawAngle);
void ReadCAN_AppSignal_Car_Yaw_Rate(Car_Yaw_RateType *LpfCar_YawRate);
void WriteCAN_AppSignal_Car_Yaw_Rate(Car_Yaw_RateType *LpfCar_YawRate);


/*************************以下为项目级信号，仅适用于比亚迪APA项目***********************************************************  */
void ReadCAN_AppSignal_ADCU_WorkModReq(ADCU_WorkModReqType *LpenuADCU_WorkModReq);
void WriteCAN_AppSignal_ADCU_WorkModReq(ADCU_WorkModReqType LenuADCU_WorkModReq);

void ReadCAN_AppSignal_APA_APA_Statms(APA_APA_StatmsType *LpenuAPA_APA_Statms);
void WriteCAN_AppSignal_APA_APA_Statms(APA_APA_StatmsType LenuAPA_APA_Statms);

void ReadCAN_AppSignal_APA_PAS_Function_Req(APA_PAS_Function_ReqType *LpenuAPA_PAS_Function_Req);
void WriteCAN_AppSignal_APA_PAS_Function_Req(APA_PAS_Function_ReqType LenuAPA_PAS_Function_Req);

void ReadCAN_AppSignal_ADCU_PSL_ParkingSlotID_Selected(ADCU_PSL_ParkingSlotID_SelectedType *Lpu8SlotID_Selected);
void WriteCAN_AppSignal_ADCU_PSL_ParkingSlotID_Selected(ADCU_PSL_ParkingSlotID_SelectedType Lu8SlotID_Selected);

void ReadCAN_AppSignal_ADCU_Work_TimeStamp(ADCU_Work_TimeStampType *Lpu64ADCU_Work_TimeStamp);
void WriteCAN_AppSignal_ADCU_Work_TimeStamp(ADCU_Work_TimeStampType Lu64ADCU_Work_TimeStamp);


void ReadCAN_AppSignal_BCMPower_Gear(BCMPower_GearType *LpenuBCMPower_Gear);
void WriteCAN_AppSignal_BCMPower_Gear(BCMPower_GearType LenuBCMPower_Gear);

void ReadCAN_AppSignal_SDWReq_Switch_State(SDWReq_Switch_StateType *LpenuSDWReq_Switch_State);
void WriteCAN_AppSignal_SDWReq_Switch_State(SDWReq_Switch_StateType LenuSDWReq_Switch_State);

void ReadCAN_AppSignal_Enter_OTA_Mode_Req(Medium_Enter_OTA_Mode_ReqType *LpenuEnter_OTA_Mode_Req);
void WriteCAN_AppSignal_Enter_OTA_Mode_Req(Medium_Enter_OTA_Mode_ReqType LenuEnter_OTA_Mode_Req);

void ReadCAN_AppSignal_ADAS_SYNC_Time(ADAS_SYNC_TimeType *LpvADAS_SYNC_Time);
void WriteCAN_AppSignal_ADAS_SYNC_Time(ADAS_SYNC_TimeType *LpvADAS_SYNC_Time);

void ReadCAN_AppSignal_Total_Distance(uint32* LpvTotal_Distance);
void WriteCAN_AppSignal_Total_Distance(uint32* LpvTotal_Distance);

void ReadCAN_AppSignal_ADAS_CAN_SYNC_Time(uint32* LpvADAS_SYNC_Time);
void WriteCAN_AppSignal_ADAS_CAN_SYNC_Time(uint32* LpvADAS_SYNC_Time);

void ReadCAN_AppSignal_Park_Auxi_Swch_Soft_Key(Park_Auxi_Swch_Soft_KeyType *LpenuPark_Auxi_Swch_Soft_Key);
void WriteCAN_AppSignal_Park_Auxi_Swch_Soft_Key(Park_Auxi_Swch_Soft_KeyType LenuPark_Auxi_Swch_Soft_Key);

void ReadCAN_AppSignal_ADAS_PSL_EnableSts(ADAS_PSL_EnableStsType *LpenuADAS_PSL_EnableSts);
void WriteCAN_AppSignal_ADAS_PSL_EnableSts(ADAS_PSL_EnableStsType LenuADAS_PSL_EnableSts);

void ReadCAN_AppSignal_ADAS_PAS_EnableSts(ADAS_PAS_EnableStsType *LpenuADAS_PAS_EnableSts);
void WriteCAN_AppSignal_ADAS_PAS_EnableSts(ADAS_PAS_EnableStsType LenuADAS_PAS_EnableSts);

void ReadCAN_AppSignal_ADAS_APAStatus(ADAS_APAStatusType *LpenuADAS_APAStatus);
void WriteCAN_AppSignal_ADAS_APAStatus(ADAS_APAStatusType LenuADAS_APAStatus);

void ReadCAN_AppSignal_ADAS_slot_ID_Selected(ADAS_slot_ID_SelectedType *Lpu8Slot_ID_Selected);
void WriteCAN_AppSignal_ADAS_slot_ID_Selected(ADAS_slot_ID_SelectedType Lu8Slot_ID_Selected);

void ReadCAN_AppSignal_LowVolPwrMdFlag(LowVolPwrMdFlagType *LpenuLowVolPwrMdFlag);
void WriteCAN_AppSignal_LowVolPwrMdFlag(LowVolPwrMdFlagType LenuLowVolPwrMdFlag);

void ReadCAN_AppSignal_LowVolPwrMd(LowVolPwrMdType *LpLowVolPwrMd);
void WriteCAN_AppSignal_LowVolPwrMd(LowVolPwrMdType LenuLowVolPwrMd);

void ReadCAN_AppSignal_LowVolPwrMd_DTCDiag(LowVolPwrMdType* LpLowVolPwrMd_DTCDiag);/* For DTC diagnostic judgement only */
void WriteCAN_AppSignal_LowVolPwrMd_DTCDiag(LowVolPwrMdType LenuLowVolPwrMd_DTCDiag);/* For DTC diagnostic judgement only */

void ReadCAN_AppSignal_VEH_Type(VehicleTypeType *LpenuVEH_Type);
void WriteCAN_AppSignal_VEH_Type(VehicleTypeType LenuVEH_Type);

void ReadCAN_AppSignal_VehicleCfgLevel(VehicleCfgLevelType *LpenuVehicleCfgLevel);
void WriteCAN_AppSignal_VehicleCfgLevel(VehicleCfgLevelType LenuVehicleCfgLevel);

void ReadCAN_AppSignal_VehicleWheel(VehicleWheelType *LpenuVehicleWheel);
void WriteCAN_AppSignal_VehicleWheel(VehicleWheelType LenuVehicleWheel);


Boolean Read_PASFunctionDGFlag(void);
void Write_PASFunctionDGFlag(Boolean Value);

Boolean Read_PSLFunctionDGFlag(void);
void Write_PSLFunctionDGFlag(Boolean Value);


void ReadComSignal_LowVolPwrMdFlag2(LowVolPwrMdFlag2_Type* LpvSts);
void WriteComSignal_LowVolPwrMdFlag2(LowVolPwrMdFlag2_Type* LpvSts);

Boolean Read_DTCWindow_Enable(void);
void Write_DTCWindow_Enable(Boolean Value);

/*************************************************************理想项目级信号14：车型平台*****************************************
*/
void ReadCAN_AppSignal_VehiclePlatform(VehiclePlatformType* LpenuVehiclePlatform);
void WriteCAN_AppSignal_VehiclePlatform(VehiclePlatformType LenuVehiclePlatform);
#endif /* end of CANSignalManage_H */

