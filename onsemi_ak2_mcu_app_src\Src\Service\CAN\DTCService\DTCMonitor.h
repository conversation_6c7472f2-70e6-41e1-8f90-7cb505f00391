 #ifndef   __DTCMONITOR_H__
 #define   __DTCMONITOR_H__
  
 /******************************************************************************* 
 * Includes 
 ********************************************************************************/ 
#include "Types.h"
#include "DTC_Cfg.h"

 /****************************************************************************/
 /****************************Type Define Sensor Fault count*****************/
#define NUMBER_OF_SENSOR_GROUP 3
#define NUMBER_OF_GROUP_DTC 4 /*每一组雷达有六个故障，只监控每一组的前四个故障*/
#define NUMBER_OF_GROUP_SENSOR 4

#define DELAY_1600MS    1600U
#define DELAY_1500MS    1500U
#define DELAY_500MS     400U//根据客户提供测试模板中判定条件修改为400--20231122
#define DELAY_200MS     200U
#define DELAY_800MS     800U
#define DELAY_1200MS    1200U

#define VotageConfimDTCTime_200MS     200U

#define LOWVotageConfimDTCTime_2000MS     2000U
 /* type define sensor fault flag */
 typedef struct
 {
     uint8 cOpen[NUMBER_OF_GROUP_SENSOR];
     uint8 cEyewinker[NUMBER_OF_GROUP_SENSOR];
     uint8 cShortToGnd[NUMBER_OF_GROUP_SENSOR];
     uint8 cShortToPower[NUMBER_OF_GROUP_SENSOR];
 }SnsFaultFlagType_DTC;

 enum ERRORSTS
 {
     NORMAL = 0,
     FAULT = 1,    
 };

 enum SnsSequence
 {
     FRONT_SNS = 0,
     REAR_SNS,
     SIDE_SNS,
     
     FL_RADAR = 0,
     FML_RADAR,  
     FMR_RADAR,
     FR_RADAR,
 
     RL_RADAR = 0,
     RML_RADAR,  
     RMR_RADAR,
     RR_RADAR, 
 
     RRS_RADAR = 0,
     RLS_RADAR,  
     FRS_RADAR,
     FLS_RADAR, 
 };
 
 /****************************************************************************** 
 * Constants and macros 
 *******************************************************************************/
 
 
 /****************************************************************************** 
 * External objects 
 ********************************************************************************/ 
 
 extern volatile uint8 GcBusOffTestResult;      
 
 extern SnsFaultFlagType_DTC SnsFaultFlag[NUMBER_OF_SENSOR_GROUP];
 
 extern uint16 GwWaitStartDiagDTCTimer;       
 extern uint8 Gu8SendDTC_Buffer[TOTAL_DTC_RECORD_NUMBER86];
 extern uint8 Gu8SendDTC_Cnt;
 extern uint8 Gu8DTC_send_complete_flag;
 /******************************************************************************* 
 * Global Functions 
 ********************************************************************************/ 
 void DTCStatusManage_Task(void);
 void Updata_DTCWindow(void);
#endif
 
