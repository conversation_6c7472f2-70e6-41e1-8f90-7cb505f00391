/******************************************************************************
 * @file       SpiCom_Prg.c
 * @brief
 * @date       2025-03-21 10:54:37
 * <AUTHOR>
 * @copyright  Longhorn Automotive Electronics Technology (Xiamen) Co., Ltd.
 *****************************************************************************/


/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include <string.h>
#include "SpiCom_Prg.h"
#include "BaseDrv.h"
#include "SpiCmd.h"

/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/



/******************************************************************************
 * @Function Declaration
 *****************************************************************************/
/* 内部数据缓冲区管理函数 */
static SpiDataBuffer_t* SpiCom_AllocateDataBuffer(void);
static void SpiCom_ReleaseDataBuffer(SpiDataBuffer_t* Buffer);
static SpiDataBuffer_t* SpiCom_GetReadyDataBuffer(uint8 ReadyCnt);
static uint8 SpiCom_GetReadyDataBufferCount(void);

/* 内部数据处理函数 */
static void SpiCom_ProcessReadyDataBuffers(void);
static void SpiCom_CheckTimeouts(void);

/* 内部回调处理函数 */
static uint8 SpiCom_HandleCallbackRetry(SpiTxSeqId_t TxSeqId, SpiCallbackReturn_t CallbackRet);

/* 内部命令处理函数 */
static void SpiCom_AbortRemainingCommands(void);
static SpiTransReturn_t SpiCom_DoSendData(SpiTxSeqId_t TxSeqId, uint8 *RxBuffer);
static SpiTransReturn_t SpiCom_SendNextCommand(SpiTxSeqId_t TxSeqId);
static SpiTransReturn_t SpiCom_HandleCommandCompletion(SpiTxSeqId_t TxSeqId);
static void SpiCom_ProcessWaitingCommands(void);



/******************************************************************************
 * @Const Definitions
 *****************************************************************************/



/******************************************************************************
 * @Variable Definitions
 *****************************************************************************/
SpiComSeq_t SpiComSeq;

/* 默认配置值 */
#define SPI_COM_DEFAULT_TIMEOUT_MS       1       /* 默认超时时间1ms */
#define SPI_COM_DEFAULT_DELAY_MS         1       /* 默认命令间延时1ms */
#define SPI_COM_DEFAULT_MAX_RETRY_COUNT  3       /* 默认最大重试次数3次 */
#define SPI_COM_DEFAULT_ENABLE_RETRY     1       /* 默认启用重试 */
#define SPI_COM_DEFAULT_ENABLE_DELAY     1       /* 默认启用延时 */
#define SPI_COM_DEFAULT_ENABLE_RESP_VAL  1       /* 默认启用响应验证 */



/******************************************************************************
 * @Function Definitions
 *****************************************************************************/

/******************************************************************************
 * @brief      分配数据缓冲区
 * @return     数据缓冲区指针，NULL表示分配失败
 * <AUTHOR> @date       2025-05-20
 * @note       从数据缓冲区池中分配一个空闲的缓冲区
 *****************************************************************************/
static SpiDataBuffer_t* SpiCom_AllocateDataBuffer(void)
{
    SpiComSeq_t* ComSeq = &SpiComSeq;
    uint8 i;

    /* 检查是否已经达到最大缓冲区数量 */
    if (ComSeq->DataBufferCount >= SPI_DATA_QUEUE_NUM)
    {
        return NULL;
    }

    /* 从写入索引开始查找空闲缓冲区 */
    for (i = 0; i < SPI_DATA_QUEUE_NUM; i++)
    {
        uint8 index = (ComSeq->DataBufferWriteIndex + i) % SPI_DATA_QUEUE_NUM;

        if (ComSeq->DataBuffers[index].DataBuffStatus == SPI_TX_IDLE)
        {
            /* 找到空闲缓冲区，设置为等待状态 */
            ComSeq->DataBuffers[index].DataBuffStatus = SPI_TX_WAIT;

            /* 更新写入索引和计数 */
            ComSeq->DataBufferWriteIndex = (index + 1) % SPI_DATA_QUEUE_NUM;
            ComSeq->DataBufferCount++;

            return &ComSeq->DataBuffers[index];
        }
    }

    /* 没有找到空闲缓冲区 */
    return NULL;
}

/******************************************************************************
 * @brief      释放数据缓冲区
 * @param[in]  Buffer 数据缓冲区指针
 * <AUTHOR> @date       2025-05-20
 * @note       将数据缓冲区状态设置为空闲
 *****************************************************************************/
static void SpiCom_ReleaseDataBuffer(SpiDataBuffer_t* Buffer)
{
    SpiComSeq_t* ComSeq = &SpiComSeq;

    /* 检查参数是否有效 */
    if (Buffer == NULL)
    {
        return;
    }

    /* 检查缓冲区是否已经是空闲状态 */
    if (Buffer->DataBuffStatus == SPI_TX_IDLE)
    {
        return;
    }

    /* 设置为空闲状态 */
    Buffer->DataBuffStatus = SPI_TX_IDLE;

    /* 更新计数 */
    if (ComSeq->DataBufferCount > 0)
    {
        ComSeq->DataBufferCount--;
    }

    /* 清除命令中的数据缓冲区指针 */
    if (Buffer->SpiTxSeqId < SPI_TX_SEQ_NUM)
    {
        ComSeq->ComMsg[Buffer->SpiTxSeqId].DataBuffer = NULL;
    }
}

/******************************************************************************
 * @brief      获取就绪的数据缓冲区
 * @return     就绪的数据缓冲区指针，NULL表示没有就绪的缓冲区
 * <AUTHOR> @date       2025-05-20
 * @note       从数据缓冲区池中获取一个就绪的缓冲区
 *****************************************************************************/
static SpiDataBuffer_t* SpiCom_GetReadyDataBuffer(uint8 ReadyCnt)
{
    SpiComSeq_t* ComSeq = &SpiComSeq;

    uint8 index = (ComSeq->DataBufferReadIndex + ReadyCnt) % SPI_DATA_QUEUE_NUM;

    if (ComSeq->DataBuffers[index].DataBuffStatus == SPI_TX_READY)
    {
        /* 找到就绪缓冲区，更新读取索引 */
        ComSeq->DataBufferReadIndex = (index + 1) % SPI_DATA_QUEUE_NUM;

        return &ComSeq->DataBuffers[index];
    }

    /* 没有找到就绪缓冲区 */
    return NULL;
}

/******************************************************************************
 * @brief      获取就绪的数据缓冲区数量
 * @return     就绪的数据缓冲区数量
 * <AUTHOR> @date       2025-05-20
 * @note       统计数据缓冲区池中就绪的缓冲区数量
 *****************************************************************************/
static uint8 SpiCom_GetReadyDataBufferCount(void)
{
    SpiComSeq_t* ComSeq = &SpiComSeq;
    uint8 count = 0;
    uint8 i;

    /* 统计就绪缓冲区数量 */
    for (i = 0; i < SPI_DATA_QUEUE_NUM; i++)
    {
        if (ComSeq->DataBuffers[i].DataBuffStatus == SPI_TX_READY)
        {
            count++;
        }
    }

    return count;
}


/******************************************************************************
 * @brief      SPI通信模块初始化
 * <AUTHOR> @date       2025-05-15 10:00:00
 * @note       初始化SPI通信模块，设置默认参数
 *****************************************************************************/
void SpiCom_Init(SpiComCallback_t *Callback)
{
    uint8 i;
    SpiComSeq_t * ComSeq;

    /* 初始化SPI通信序列 */
    ComSeq = &SpiComSeq;
    memset(ComSeq, 0, sizeof(SpiComSeq_t));

    /* 设置默认参数 */
    ComSeq->Status = SPI_TX_IDLE;
    ComSeq->CurrentIndex = SPI_TX_SEQ_NULL;
    ComSeq->NextIndex = SPI_TX_SEQ_START;
    ComSeq->DefaultTimeoutMs = SPI_COM_DEFAULT_TIMEOUT_MS;
    ComSeq->DefaultDelayMs = SPI_COM_DEFAULT_DELAY_MS;
    ComSeq->DefaultMaxRetryCount = SPI_COM_DEFAULT_MAX_RETRY_COUNT;
    ComSeq->EnableRetry = SPI_COM_DEFAULT_ENABLE_RETRY;
    ComSeq->EnableDelay = SPI_COM_DEFAULT_ENABLE_DELAY;
    ComSeq->AbortRemaining = 0;  /* 默认不中止剩余命令 */

    ComSeq->Callback = Callback;

    /* 初始化数据缓冲区池 */
    ComSeq->DataBufferReadIndex = 0;
    ComSeq->DataBufferWriteIndex = 0;
    ComSeq->DataBufferCount = 0;

    for (i = 0; i < SPI_DATA_QUEUE_NUM; i++)
    {
        ComSeq->DataBuffers[i].DataBuffStatus = SPI_TX_IDLE;
        ComSeq->DataBuffers[i].DataLens = 0;
        ComSeq->DataBuffers[i].SpiTxSeqId = SPI_TX_SEQ_NULL;
    }

    /* 初始化所有命令状态 */
    for (i = 0; i < SPI_TX_SEQ_NUM; i++)
    {
        ComSeq->ComMsg[i].TxStatus = SPI_TX_IDLE;
        ComSeq->ComMsg[i].RetryCount = 0;
        ComSeq->ComMsg[i].MaxRetryCount = ComSeq->DefaultMaxRetryCount;
        ComSeq->ComMsg[i].TimeoutMs = ComSeq->DefaultTimeoutMs;
        ComSeq->ComMsg[i].DelayMs = ComSeq->DefaultDelayMs;
        ComSeq->ComMsg[i].UserData = NULL;
        ComSeq->ComMsg[i].IsResponseValid = 0;
        ComSeq->ComMsg[i].IsResponseNeeded = 0;
        ComSeq->ComMsg[i].DataBuffer = NULL;
    }

    PRINTF_RDUM("SpiCom_Init: Initialized\r\n");
}


/******************************************************************************
 * @brief      设置默认超时时间
 * @param[in]  TimeoutMs 超时时间(毫秒)
 * <AUTHOR> @date       2025-05-15 10:10:00
 * @note       设置SPI通信的默认超时时间
 *****************************************************************************/
void SpiCom_SetDefaultTimeout(uint16 TimeoutMs)
{
    SpiComSeq.DefaultTimeoutMs = TimeoutMs;
}


/******************************************************************************
 * @brief      设置默认命令延时
 * @param[in]  DelayMs 延时时间(毫秒)
 * <AUTHOR> @date       2025-05-15 10:15:00
 * @note       设置SPI命令之间的默认延时时间
 *****************************************************************************/
void SpiCom_SetDefaultDelay(uint16 DelayMs)
{
    SpiComSeq.DefaultDelayMs = DelayMs;
}


/******************************************************************************
 * @brief      设置默认重试次数
 * @param[in]  MaxRetryCount 最大重试次数
 * <AUTHOR> @date       2025-05-15 10:20:00
 * @note       设置SPI通信的默认最大重试次数
 *****************************************************************************/
void SpiCom_SetDefaultRetryCount(uint8 MaxRetryCount)
{
    SpiComSeq.DefaultMaxRetryCount = MaxRetryCount;
}


/******************************************************************************
 * @brief      启用/禁用重试功能
 * @param[in]  Enable 1:启用 0:禁用
 * <AUTHOR> @date       2025-05-15 10:25:00
 * @note       启用或禁用SPI通信的重试功能
 *****************************************************************************/
void SpiCom_EnableRetry(uint8 Enable)
{
    SpiComSeq.EnableRetry = Enable;
}


/******************************************************************************
 * @brief      启用/禁用延时功能
 * @param[in]  Enable 1:启用 0:禁用
 * <AUTHOR> @date       2025-05-15 10:30:00
 * @note       启用或禁用SPI命令之间的延时功能
 *****************************************************************************/
void SpiCom_EnableDelay(uint8 Enable)
{
    SpiComSeq.EnableDelay = Enable;
}


/******************************************************************************
 * @brief      获取SPI传输序号
 * @return     传输序号
 * <AUTHOR>
 * @date       2025-04-01 14:17:05
 * @note
 *****************************************************************************/
SpiTxSeqId_t SpiCom_GetTxSeqId(void)
{
    SpiComSeq_t * ComSeq;
    SpiTxSeqId_t RetIndex;

    ComSeq = &SpiComSeq;
    for (RetIndex = SPI_TX_SEQ_START; RetIndex < SPI_TX_SEQ_NUM; RetIndex++)
    {
        /** @note 获取空闲、已完成传输序号 */
        if (ComSeq->ComMsg[RetIndex].TxStatus == SPI_TX_IDLE)
        {
            PRINTF_RDUM("SpiCom_GetTxSeqId: %d\r\n", RetIndex);
            return RetIndex;
        }
    }
    return SPI_TX_SEQ_NULL;
}


/******************************************************************************
 * @brief      设置SPI传输状态
 * @param[in]  TxSeqIndex
 * @param[in]  Status
 * <AUTHOR>
 * @date       2025-04-01 16:01:35
 * @note
 *****************************************************************************/
void SpiCom_SetTxStatus(SpiTxSeqId_t TxSeqIndex, SpiTxStatus_t Status)
{
    SpiComSeq_t * ComSeq;

    ComSeq = &SpiComSeq;
    /** @note 设置传输状态 */
    ComSeq->ComMsg[TxSeqIndex].TxStatus = Status;
}


/******************************************************************************
 * @brief      获取SPI传输状态
 * @param[in]  TxSeqIndex
 * @return
 * <AUTHOR>
 * @date       2025-04-01 17:02:27
 * @note
 *****************************************************************************/
SpiTxStatus_t SpiCom_GetTxStatus(SpiTxSeqId_t TxSeqIndex)
{
    SpiTxStatus_t RetStatus;
    SpiComSeq_t * ComSeq;

    ComSeq = &SpiComSeq;
    if (TxSeqIndex >= SPI_TX_SEQ_NUM)
    {
        return SPI_TX_ERROR;
    }

    /** @note 获取传输状态 */
    RetStatus = ComSeq->ComMsg[TxSeqIndex].TxStatus;
    // if (RetStatus == SPI_TX_READY)
    // {
    //     /** @note 将传输状态置为空闲 */
    //     ComSeq->ComMsg[TxSeqIndex].TxStatus = SPI_TX_IDLE;
    //     RetStatus = SPI_TX_READY;
    // }

    return RetStatus;
}

/******************************************************************************
 * @brief      实际发送SPI数据
 * @param[in]  TxSeqId 传输序号
 * @param[in]  RxBuffer 接收缓冲区，如果为NULL则使用DataBuffer中的RxBuff
 * @return     传输结果
 * <AUTHOR>
 * @date       2025-05-20
 * @note       通用的SPI数据发送函数
 *****************************************************************************/
static SpiTransReturn_t SpiCom_DoSendData(SpiTxSeqId_t TxSeqId, uint8 *RxBuffer)
{
    SpiComSeq_t * ComSeq = &SpiComSeq;
    SpiDataBuffer_t* DataBuffer;
    uint16 TxLens;
    SpiTransReturn_t TxStatus;
    uint8 *RxBuff;

    /* 获取传输参数 */
    TxLens = ComSeq->ComMsg[TxSeqId].DataBuffer->DataLens;
    DataBuffer = ComSeq->ComMsg[TxSeqId].DataBuffer;

    /* 检查数据缓冲区是否有效 */
    if (DataBuffer == NULL)
    {
        PRINTF_RDUM("SpiCom_DoSendData: Invalid data buffer\r\n");
        return SPI_TRANS_ERROR;
    }

    /* 确定接收缓冲区 */
    RxBuff = (RxBuffer != NULL) ? RxBuffer : DataBuffer->RxBuff;

    /* 发送SPI数据 */
    TxStatus = BaseDrv_Master0SpiAsyncTransmit(DataBuffer->TxBuff, RxBuff, TxLens);
    if (TxStatus != SPI_TRANS_SUCC)
    {
        PRINTF_RDUM("SpiCom_DoSendData: Tx failed\r\n");
        return SPI_TRANS_ERROR;
    }


    /* 设置下一个传输序号(只有响应正确时才设置下一个传输序号，超时也属于响应不正确) */
    // if (ComSeq->ComMsg[TxSeqId].IsResponseValid)
    {
        ComSeq->NextIndex = (SpiTxSeqId_t)(TxSeqId + 1);
        if (ComSeq->NextIndex >= SPI_TX_SEQ_NUM)
        {
            ComSeq->NextIndex = SPI_TX_SEQ_START;
        }
    }

    return SPI_TRANS_SUCC;
}

/******************************************************************************
 * @brief      增强的SPI异步方式传输数据
 * @param[in]  TxData 发送数据
 * @param[in]  TxLens 发送数据长度
 * @param[in]  TimeoutMs 超时时间(毫秒)
 * @param[in]  DelayMs 命令延时时间(毫秒)
 * @param[in]  MaxRetryCount 最大重试次数
 * @param[in]  Callback 完成回调函数
 * @param[in]  UserData 用户数据
 * @return     传输序号
 * <AUTHOR> @date       2025-05-15 11:00:00
 * @note       支持超时、重试、延时和回调的异步传输
 *****************************************************************************/
SpiTxSeqId_t SpiCom_AsyncTransferEx(uint8 *TxData, uint16 TxLens,
                                   uint16 TimeoutMs, uint16 DelayMs,
                                   uint8 MaxRetryCount, uint8 IsResponseNeeded, void* UserData)
{
    SpiTxSeqId_t TxSeqIndex;
    SpiComSeq_t * ComSeq;
    SpiDataBuffer_t* DataBuffer;

    ComSeq = &SpiComSeq;

    /* 获取空闲传输序号 */
    TxSeqIndex = SpiCom_GetTxSeqId();
#ifdef DEBUG_RDUM_RDUS
    PRINTF_RDUM("SpiCom_AsyncTransferEx: TxData: ");
    for (int i = 0; i < TxLens; i++)
    {
        PRINTF_RDUM("%02X ", TxData[i]);
    }
    PRINTF_RDUM("\r\n, Index: %d\r\n", TxSeqIndex);
#endif
    /* 检查参数是否有效 */
    if ((TxSeqIndex >= SPI_TX_SEQ_NUM) || (TxData == NULL) || (TxLens > SPI_DATA_BUFF_MAX_NUM))
    {
        PRINTF_RDUM("SpiCom_AsyncTransferEx: Invalid parameters\r\n");
        return SPI_TX_SEQ_NULL;
    }

    /* 分配数据缓冲区 */
    DataBuffer = SpiCom_AllocateDataBuffer();
    if (DataBuffer == NULL)
    {
        PRINTF_RDUM("SpiCom_AsyncTransferEx: Failed to allocate data buffer\r\n");
        return SPI_TX_SEQ_NULL;
    }

    /* 设置数据缓冲区参数 */
    DataBuffer->DataLens = TxLens;
    DataBuffer->SpiTxSeqId = TxSeqIndex;

    /* 复制发送数据 */
    memcpy(DataBuffer->TxBuff, TxData, TxLens);

    /* 设置命令参数 */
    ComSeq->ComMsg[TxSeqIndex].DataBuffer = DataBuffer;
    ComSeq->ComMsg[TxSeqIndex].TimeoutMs = TimeoutMs;
    ComSeq->ComMsg[TxSeqIndex].DelayMs = DelayMs;
    ComSeq->ComMsg[TxSeqIndex].MaxRetryCount = MaxRetryCount;
    ComSeq->ComMsg[TxSeqIndex].RetryCount = 0;
    ComSeq->ComMsg[TxSeqIndex].UserData = UserData;
    ComSeq->ComMsg[TxSeqIndex].IsResponseNeeded = IsResponseNeeded;
    ComSeq->ComMsg[TxSeqIndex].IsResponseValid = 0;

    /* 检查SPI总线状态 */
    if (ComSeq->Status == SPI_TX_IDLE && ComSeq->ComMsg[ComSeq->CurrentIndex].TxStatus == SPI_TX_IDLE)
    {
        /* SPI总线空闲，可以立即发送 */
        if (SpiCom_SendNextCommand(TxSeqIndex) == SPI_TRANS_ERROR)
        {
            return SPI_TX_SEQ_NULL;
        }
    }
    else
    {
        /* 只装载数据，发送数据统一在中断和SpiCom_DataHandle中处理，此处统一设置为等待状态 */
        PRINTF_RDUM("SpiCom_AsyncTransferEx: SPI busy, set to wait\r\n");
        ComSeq->ComMsg[TxSeqIndex].TxStatus = SPI_TX_WAIT;
    }

    return TxSeqIndex;
}

/******************************************************************************
 * @brief      获取SPI传输状态
 * @param[in]  TxSeqId 传输序号
 * @return     传输状态
 * <AUTHOR> @date       2025-05-15 12:00:00
 * @note       获取指定SPI传输的状态
 *****************************************************************************/
SpiTxStatus_t SpiCom_GetTransferStatus(SpiTxSeqId_t TxSeqId)
{
    SpiComSeq_t * ComSeq;

    ComSeq = &SpiComSeq;

    /* 检查传输序号是否有效 */
    if (TxSeqId >= SPI_TX_SEQ_NUM)
    {
        return SPI_TX_IDLE;
    }

    return ComSeq->ComMsg[TxSeqId].TxStatus;
}


/******************************************************************************
 * @brief      设置响应验证函数
 * @param[in]  TxSeqId 传输序号
 * @return     操作结果
 * <AUTHOR> @date       2025-05-15 12:15:00
 * @note       为指定的SPI传输设置响应验证函数
 *****************************************************************************/
SpiTransReturn_t SpiCom_SetResponseValidation(SpiTxSeqId_t TxSeqId)
{
    SpiComSeq_t * ComSeq;

    ComSeq = &SpiComSeq;

    /* 检查传输序号是否有效 */
    if (TxSeqId >= SPI_TX_SEQ_NUM)
    {
        return SPI_TRANS_INVALID;
    }

    /* 设置响应验证 */
    ComSeq->ComMsg[TxSeqId].IsResponseValid = 1;

    return SPI_TRANS_SUCC;
}


/******************************************************************************
 * @brief      SPI同步方式传输数据
 * @param[in]  TxData
 * @param[in]  TxLens
 * @param[in]  RxData
 * @param[in]  Timeout
 * @return
 * <AUTHOR>
 * @date       2025-03-24 13:47:25
 * @note
 *****************************************************************************/
SpiTxSeqId_t SpiCom_SyncTransfer(uint8 *TxData, uint16 TxLens, uint8 *RxData, uint8 Timeout)
{
    uint32 SysTickTime;
    SpiTxSeqId_t TxSeqIndex;
    SpiComSeq_t * ComSeq;
    SpiTransReturn_t TxStatus;
    SpiDataBuffer_t* DataBuffer;

    ComSeq = &SpiComSeq;

    /* 获取空闲传输序号 */
    TxSeqIndex = SpiCom_GetTxSeqId();
    if ((TxSeqIndex >= SPI_TX_SEQ_NUM) || (TxData == NULL) || (TxLens > SPI_DATA_BUFF_MAX_NUM))
    {
        return SPI_TX_SEQ_NULL;
    }

    /* 分配数据缓冲区 */
    DataBuffer = SpiCom_AllocateDataBuffer();
    if (DataBuffer == NULL)
    {
        PRINTF_RDUM("SpiCom_SyncTransfer: Failed to allocate data buffer\r\n");
        return SPI_TX_SEQ_NULL;
    }

    /* 设置数据缓冲区参数 */
    DataBuffer->DataLens = TxLens;
    DataBuffer->SpiTxSeqId = TxSeqIndex;

    /* 复制发送数据 */
    memcpy(DataBuffer->TxBuff, TxData, TxLens);

    /* 设置命令参数 */
    ComSeq->ComMsg[TxSeqIndex].DataBuffer = DataBuffer;

    if (ComSeq->Status == SPI_TX_IDLE)
    {
        /* SPI总线空闲，可以立即发送 */
        ComSeq->Status = SPI_TX_BUSY;
        ComSeq->CurrentIndex = TxSeqIndex;

        /* 设置传输状态 */
        ComSeq->ComMsg[TxSeqIndex].TxStatus = SPI_TX_BUSY;

        /* 发送SPI数据 */
        TxStatus = SpiCom_DoSendData(TxSeqIndex, RxData);
        if (TxStatus != SPI_TRANS_SUCC)
        {
            /* 发送失败，释放数据缓冲区 */
            SpiCom_ReleaseDataBuffer(DataBuffer);
            ComSeq->ComMsg[TxSeqIndex].DataBuffer = NULL;

            /* 恢复SPI总线状态 */
            ComSeq->Status = SPI_TX_IDLE;

            return SPI_TX_SEQ_NULL;
        }
    }
    else
    {
        /* SPI总线忙，设置为等待状态 */
        ComSeq->ComMsg[TxSeqIndex].TxStatus = SPI_TX_WAIT;
    }

    /* 等待传输完成或超时 */
    SysTickTime = BaseDrv_GetSys1MsTick();
    while (SpiCom_GetTxStatus(TxSeqIndex) != SPI_TX_IDLE)
    {
        if (ABS(SysTickTime, BaseDrv_GetSys1MsTick()) > Timeout)
        {
            /* 超时，释放数据缓冲区 */
            if (DataBuffer != NULL)
            {
                SpiCom_ReleaseDataBuffer(DataBuffer);
                ComSeq->ComMsg[TxSeqIndex].DataBuffer = NULL;
            }

            return SPI_TX_SEQ_NULL;
        }
    }

    /* 传输完成，复制接收数据 */
    if (RxData != NULL && DataBuffer != NULL)
    {
        memcpy(RxData, DataBuffer->RxBuff, TxLens);
    }

    /* 释放数据缓冲区 */
    if (DataBuffer != NULL)
    {
        SpiCom_ReleaseDataBuffer(DataBuffer);
        ComSeq->ComMsg[TxSeqIndex].DataBuffer = NULL;
    }

    return TxSeqIndex;
}


/******************************************************************************
 * @brief      RDUM DMA完成传输回调函数
 * @param[in]  RdumId RDUM主机ID
 * <AUTHOR>
 * @date       2025-03-22 20:05:11
 * @note       处理SPI传输完成事件
 *****************************************************************************/
void SpiCom_TransferComplete(RdumMaster_t RdumId)
{
    SpiComSeq_t * ComSeq;
    SpiTxSeqId_t TxSeqIndex;
    SpiDataBuffer_t* DataBuffer;
    SpiCallbackReturn_t CallbackRet;

    ComSeq = &SpiComSeq;
    /* 保存当前传输序号 */
    TxSeqIndex = ComSeq->CurrentIndex;

    /* 检查当前传输序号和缓存是否有效 */
    if (TxSeqIndex >= SPI_TX_SEQ_NUM)
    {
        PRINTF_RDUM("SpiCom_TransferComplete: Invalid TxSeqIndex=%d\r\n", TxSeqIndex);
        return;
    }

    /* 获取数据缓冲区 */
    DataBuffer = ComSeq->ComMsg[TxSeqIndex].DataBuffer;
    if (DataBuffer != NULL)
    {
        PRINTF_RDUM("SpiCom_TransferComplete: DataBuffer is NULL\r\n");
        return;
    }

    /* 设置数据缓冲区为就绪状态 */
    DataBuffer->DataBuffStatus = SPI_TX_READY;

    /* 设置传输完成时间 */
    ComSeq->ComMsg[TxSeqIndex].TxCompleteTime = BaseDrv_GetSys1MsTick();

    /* 检查响应是否有效 */
    if (ComSeq->ComMsg[TxSeqIndex].IsResponseNeeded)
    {
        /* 调用响应验证回调函数 */
        CallbackRet = ComSeq->Callback(&ComSeq->ComMsg[TxSeqIndex]);

        /* 释放数据缓冲区 */
        SpiCom_ReleaseDataBuffer(DataBuffer);

        if (CallbackRet == SPI_CALLBACK_OK)
        {
            ComSeq->ComMsg[TxSeqIndex].IsResponseValid = 1;
        }  
    }
    else
    {
        /* 不需要验证响应，直接设置为有效 */
        ComSeq->ComMsg[TxSeqIndex].IsResponseValid = 1;
    }

    /* 处理命令完成, 如果响应不正确，在周期循环里重试 */
    if (SpiCom_HandleCommandCompletion(TxSeqIndex))
    {
        return;
    }

    PRINTF_RDUM("SpiCom_TransferComplete: CurrentIndex=%d\r\n", ComSeq->CurrentIndex);

    /* 检查是否需要中止剩余命令 */
    // if (ComSeq->AbortRemaining)
    // {
    //     SpiCom_AbortRemainingCommands();
    //     return;
    // }

    /* 处理等待的命令 */
    SpiCom_ProcessWaitingCommands();
}


/******************************************************************************
 * @brief      DMA完成传输回调函数 - 主机0
 * <AUTHOR>
 * @date       2025-03-22 19:58:27
 * @note       SPI主机0 DMA传输完成回调函数
 *****************************************************************************/
void SpiCom_DSIMaster0_CompleteTransfer_Cbk(void)
{
    /* 调用传输完成处理函数 */
    SpiCom_TransferComplete(RDUM_MASTER0);
}


/******************************************************************************
 * @brief      DMA完成传输回调函数 - 主机1
 * <AUTHOR>
 * @date       2025-03-22 21:59:15
 * @note       SPI主机1 DMA传输完成回调函数
 *****************************************************************************/
void SpiCom_DSIMaster1_CompleteTransfer_Cbk(void)
{
    /* 调用传输完成处理函数 */
    SpiCom_TransferComplete(RDUM_MASTER1);
}

/******************************************************************************
 * @brief      处理回调函数重试请求
 * @param[in]  TxSeqId 传输序号
 * @param[in]  CallbackRet 回调函数返回值
 * @return     是否需要返回（1:需要返回，0:不需要返回）
 * <AUTHOR>
 * @date       2025-04-03 15:35:10
 * @note       处理回调函数重试请求
 *****************************************************************************/
static uint8 SpiCom_HandleCallbackRetry(SpiTxSeqId_t TxSeqId, SpiCallbackReturn_t CallbackRet)
{
    SpiComSeq_t * ComSeq = &SpiComSeq;

    /* 检查传输序号是否有效 */
    if (TxSeqId >= SPI_TX_SEQ_NUM)
    {
        return 0;
    }

    /* 处理重试请求 */
    if (CallbackRet == SPI_CALLBACK_RETRY)
    {
        /* 检查是否可以重试 */
        if (ComSeq->ComMsg[TxSeqId].RetryCount < ComSeq->ComMsg[TxSeqId].MaxRetryCount)
        {
            /* 增加重试计数 */
            ComSeq->ComMsg[TxSeqId].RetryCount++;

            /* 设置为等待状态，等待重试 */
            ComSeq->ComMsg[TxSeqId].TxStatus = SPI_TX_WAIT;

            PRINTF_RDUM("SpiCom_HandleCallbackRetry: Retry %d/%d\r\n",
                       ComSeq->ComMsg[TxSeqId].RetryCount,
                       ComSeq->ComMsg[TxSeqId].MaxRetryCount);

            /* 数据缓冲区保持不变，等待重试 */
            return 1;
        }
        else
        {
            /* 超过最大重试次数，设置为错误状态 */
            ComSeq->ComMsg[TxSeqId].TxStatus = SPI_TX_ERROR;
            PRINTF_RDUM("SpiCom_HandleCallbackRetry: Max retry reached\r\n");
        }
    }

    return 0;
}

/******************************************************************************
 * @brief      中止剩余命令
 * <AUTHOR>
 * @date       2025-04-03 15:45:20
 * @note       中止所有等待的命令
 *****************************************************************************/
static void SpiCom_AbortRemainingCommands(void)
{
    SpiComSeq_t * ComSeq = &SpiComSeq;
    SpiTxSeqId_t TxSeqIndex;

    /* 中止所有等待的命令 */
    for (TxSeqIndex = SPI_TX_SEQ_START; TxSeqIndex < SPI_TX_SEQ_NUM; TxSeqIndex++)
    {
        if (ComSeq->ComMsg[TxSeqIndex].TxStatus == SPI_TX_WAIT)
        {
            /* 设置为取消状态 */
            ComSeq->ComMsg[TxSeqIndex].TxStatus = SPI_TX_CANCELED;

            /* 调用回调函数 */
            // SpiCom_HandleCallback(TxSeqIndex, SPI_TX_CANCELED);

            /* 释放数据缓冲区 */
            if (ComSeq->ComMsg[TxSeqIndex].DataBuffer != NULL)
            {
                SpiCom_ReleaseDataBuffer(ComSeq->ComMsg[TxSeqIndex].DataBuffer);
                ComSeq->ComMsg[TxSeqIndex].DataBuffer = NULL;
            }

            PRINTF_RDUM("SpiCom_AbortRemainingCommands: Command canceled TxSeqIndex=%d\r\n", TxSeqIndex);
        }
    }

    /* 重置中止标志 */
    ComSeq->AbortRemaining = 0;
}

/******************************************************************************
 * @brief      发送下一个命令
 * @param[in]  TxSeqId 传输序号
 * @return     传输结果
 * <AUTHOR>
 * @date       2025-04-03 15:50:30
 * @note       发送指定的SPI命令
 *****************************************************************************/
static SpiTransReturn_t SpiCom_SendNextCommand(SpiTxSeqId_t TxSeqId)
{
    SpiComSeq_t * ComSeq = &SpiComSeq;
    SpiDataBuffer_t* DataBuffer;
    uint16 TxLens;
    SpiTransReturn_t TxStatus;
    uint32 CurrentTime = BaseDrv_GetSys1MsTick();
    uint16 DelayMs = ComSeq->ComMsg[TxSeqId].DelayMs;
    uint32 LastCompleteTime;
    SpiCallbackReturn_t CallbackRet;

    /* 检查是否需要延时 */
    if (DelayMs != 0 && ComSeq->EnableDelay)
    {  
        /* 获取上一个或者上一次(重试情况下)命令的完成时间, 判断当前需要延时的指令延时时间是否到 */     
        LastCompleteTime = ComSeq->ComMsg[ComSeq->CurrentIndex].TxCompleteTime;

        if ((CurrentTime - LastCompleteTime) < DelayMs)
        {
            /* 需要延时，暂不发送 */
            ComSeq->ComMsg[TxSeqId].TxStatus = SPI_TX_WAIT;
            PRINTF_RDUM("SpiCom_SendNextCommand: Need delay %dms\r\n", DelayMs);
            return SPI_TRANS_DELAY;
        }
    }

    /* 设置传输状态 */
    ComSeq->Status = SPI_TX_BUSY;
    ComSeq->CurrentIndex = TxSeqId;
    ComSeq->ComMsg[TxSeqId].TxStatus = SPI_TX_BUSY;

    /* 设置传输开始时间 */
    ComSeq->ComMsg[TxSeqId].TxStartTime = CurrentTime;

    /* 发送SPI数据 */
    TxStatus = SpiCom_DoSendData(TxSeqId, NULL);
    if (TxStatus != SPI_TRANS_SUCC)
    {
        /* 发送失败，设置为错误状态 */
        ComSeq->ComMsg[TxSeqId].TxStatus = SPI_TX_ERROR;

        /* 恢复SPI总线状态 */
        ComSeq->Status = SPI_TX_IDLE;

        /* 调用响应验证回调函数 */
        CallbackRet = ComSeq->Callback(&ComSeq->ComMsg[TxSeqId]);

        PRINTF_RDUM("SpiCom_SendNextCommand: Tx failed\r\n");
        return SPI_TRANS_ERROR;
    }

    PRINTF_RDUM("SpiCom_SendNextCommand: Command sent TxSeqIndex=%d\r\n", TxSeqId);

    return SPI_TRANS_SUCC;
}

/******************************************************************************
 * @brief      处理命令完成
 * @param[in]  TxSeqId 传输序号
 * @param[in]  IsResponseValid 响应是否有效
 * @return     传输结果
 * <AUTHOR>
 * @date       2025-04-03 16:00:40
 * @note       处理命令完成后的操作
 *****************************************************************************/
static SpiTransReturn_t SpiCom_HandleCommandCompletion(SpiTxSeqId_t TxSeqId)
{
    SpiComSeq_t * ComSeq = &SpiComSeq;

    if (!ComSeq->ComMsg[TxSeqId].IsResponseValid)
    {
        /* 恢复SPI总线状态 */
        ComSeq->Status = SPI_TX_IDLE;

        /* 检查是否需要重试 */
        if (ComSeq->EnableRetry && ComSeq->ComMsg[TxSeqId].RetryCount < ComSeq->ComMsg[TxSeqId].MaxRetryCount)
        {
            /* 增加重试计数 */
            ComSeq->ComMsg[TxSeqId].RetryCount++;

            /* 设置为等待状态，等待重试 */
            ComSeq->ComMsg[TxSeqId].TxStatus = SPI_TX_WAIT;

            PRINTF_RDUM("SpiCom_HandleCommandCompletion: Response invalid, retry %d/%d\r\n",
                        ComSeq->ComMsg[TxSeqId].RetryCount,
                        ComSeq->ComMsg[TxSeqId].MaxRetryCount);
            return SPI_TRANS_ERROR;
        }

        /* 超过最大重试次数，设置为错误状态 */
        ComSeq->ComMsg[TxSeqId].TxStatus = SPI_TX_ERROR;

        /* 中止剩余命令 */
        SpiCom_AbortRemainingCommands();

        PRINTF_RDUM("SpiCom_HandleCommandCompletion: Response invalid, max retry reached\r\n");

        return SPI_TRANS_ERROR;
    }
    else
    {
        ComSeq->ComMsg[TxSeqId].TxStatus = SPI_TX_IDLE;

        return SPI_TRANS_SUCC;
    }
}

/******************************************************************************
 * @brief      处理等待的命令
 * <AUTHOR>
 * @date       2025-04-03 16:10:50
 * @note       处理等待队列中的命令
 *****************************************************************************/
static void SpiCom_ProcessWaitingCommands(void)
{
    SpiComSeq_t * ComSeq = &SpiComSeq;
    SpiTxSeqId_t TxSeqIndex;
    SpiTxSeqId_t WaitIndex[SPI_TX_SEQ_NUM];
    uint8 WaitTxCnt = 0;

    /* 获取等待传输的命令 */
    for (TxSeqIndex = SPI_TX_SEQ_START; TxSeqIndex < SPI_TX_SEQ_NUM; TxSeqIndex++)
    {
        if (ComSeq->ComMsg[TxSeqIndex].TxStatus == SPI_TX_WAIT)
        {
            /* 如果当前命令已经完成，优先处理下一个命令 */
            if (ComSeq->ComMsg[ComSeq->CurrentIndex].IsResponseValid)
            {
                /* 优先处理NextIndex指向的命令 */
                if (TxSeqIndex == ComSeq->NextIndex)
                {
                    /* 发送下一个命令 */
                    SpiCom_SendNextCommand(TxSeqIndex);
                    return;
                }
            }
            else
            {
                /* 如果当前命令未完成(超时或者数据无效)， 则重发发送当前命令 */
                SpiCom_SendNextCommand(ComSeq->CurrentIndex);
                return;
            }

            WaitIndex[WaitTxCnt] = TxSeqIndex;
            WaitTxCnt++;
        }
    }

    /* 如果有等待的命令，处理下一个命令 */
    if (WaitTxCnt > 0)
    {
        /* 如果NextIndex没有等待的命令，选择第一个等待的命令 */
        SpiCom_SendNextCommand(WaitIndex[0]);
    }
}

/******************************************************************************
 * @brief      SPI响应数据处理
 * <AUTHOR>
 * @date       2025-03-26 17:33:29
 * @note       处理SPI响应数据和超时检测
 *****************************************************************************/
void SpiCom_DataHandle(void)
{
    SpiComSeq_t * ComSeq;

    ComSeq = &SpiComSeq;

    /* 处理就绪的数据缓冲区 */
    SpiCom_ProcessReadyDataBuffers();

    /* 检查超时，超时过程中ComSeq->Status会维持SPI_TX_BUSY状态直到超时时间到处理 */ 
    SpiCom_CheckTimeouts();

    /* 处理等待的命令 */
    if (ComSeq->Status == SPI_TX_IDLE)
    {
        SpiCom_ProcessWaitingCommands();
    }
}

/******************************************************************************
 * @brief      处理就绪的数据缓冲区
 * <AUTHOR>
 * @date       2025-04-03 16:30:10
 * @note       处理就绪的数据缓冲区
 *****************************************************************************/
static void SpiCom_ProcessReadyDataBuffers(void)
{
    uint8 ReadyCnt;
    SpiComSeq_t* ComSeq = &SpiComSeq;
    SpiDataBuffer_t* DataBuffer;
    SpiCallbackReturn_t CallbackRet;

    /* 处理就绪的数据缓冲区 */
    // if (SpiCom_GetReadyDataBufferCount() != 0)
    {
        for (ReadyCnt = 0; ReadyCnt < ComSeq->DataBufferCount; ReadyCnt++)
        {
            DataBuffer = SpiCom_GetReadyDataBuffer(ReadyCnt);
            if (DataBuffer != NULL && DataBuffer->DataLens != 0)
            {
                /* 分析接收到的数据 */
                if (ComSeq->Callback != NULL)
                {
                    CallbackRet = ComSeq->Callback(&ComSeq->ComMsg[DataBuffer->SpiTxSeqId]);
                }
                else
                {
                    PRINTF_RDUM("Error: Callback function not set\r\n");
                    CallbackRet = SPI_CALLBACK_ABORT;
                }
                /* 释放数据缓冲区 */
                SpiCom_ReleaseDataBuffer(DataBuffer);
                
                if (CallbackRet == SPI_CALLBACK_OK)
                {
                    ComSeq->ComMsg[DataBuffer->SpiTxSeqId].IsResponseValid = 1;
                }
            }
            else
            {
                break;
            }
        }
    }
}

/******************************************************************************
 * @brief      检查超时
 * <AUTHOR>
 * @date       2025-04-03 16:35:20
 * @note       检查命令超时
 *****************************************************************************/
static void SpiCom_CheckTimeouts(void)
{
    SpiComSeq_t * ComSeq = &SpiComSeq;
    SpiTxSeqId_t TxSeqIndex;
    uint32 CurrentTime = BaseDrv_GetSys1MsTick();
    SpiCallbackReturn_t CallbackRet;

    /* 检查超时 */
    for (TxSeqIndex = SPI_TX_SEQ_START; TxSeqIndex < SPI_TX_SEQ_NUM; TxSeqIndex++)
    {
        /* 只检查正在传输中的命令 */
        if (ComSeq->ComMsg[TxSeqIndex].TxStatus == SPI_TX_BUSY)
        {
            uint32 StartTime = ComSeq->ComMsg[TxSeqIndex].TxStartTime;
            uint16 TimeoutMs = ComSeq->ComMsg[TxSeqIndex].TimeoutMs;

            /* 检查是否超时 */
            if ((CurrentTime - StartTime) > TimeoutMs)
            {
                PRINTF_RDUM("SpiCom_CheckTimeouts: Command timeout TxSeqIndex=%d\r\n", TxSeqIndex);

                /* 恢复SPI总线状态 */
                ComSeq->Status = SPI_TX_IDLE;

                /* 检查是否需要重试 */
                if (ComSeq->EnableRetry &&
                    ComSeq->ComMsg[TxSeqIndex].RetryCount < ComSeq->ComMsg[TxSeqIndex].MaxRetryCount)
                {
                    /* 增加重试计数 */
                    ComSeq->ComMsg[TxSeqIndex].RetryCount++;

                    /* 设置为等待状态，等待重试 */
                    ComSeq->ComMsg[TxSeqIndex].TxStatus = SPI_TX_WAIT;

                    PRINTF_RDUM("SpiCom_CheckTimeouts: Retry %d/%d\r\n",
                               ComSeq->ComMsg[TxSeqIndex].RetryCount,
                               ComSeq->ComMsg[TxSeqIndex].MaxRetryCount);
                }
                else
                {
                    /* 超过最大重试次数，设置为超时状态 */
                    ComSeq->ComMsg[TxSeqIndex].TxStatus = SPI_TX_TIMEOUT;

                    // /* 调用回调函数 */
                    // CallbackRet = SpiCom_HandleCallback(TxSeqIndex, SPI_TX_TIMEOUT);

                    // /* 处理回调函数重试请求 */
                    // if (SpiCom_HandleCallbackRetry(TxSeqIndex, CallbackRet))
                    // {
                    //     continue;
                    // }

                    /* 中止剩余命令 */
                    SpiCom_AbortRemainingCommands();

                    PRINTF_RDUM("SpiCom_CheckTimeouts: Max retry reached\r\n");

                    return;
                }

                /* 如果当前正在传输的命令超时，恢复SPI总线状态 */
                // if (ComSeq->CurrentIndex == TxSeqIndex)
                // {
                //     ComSeq->Status = SPI_TX_IDLE;
                // }
            }
        }
    }
}
