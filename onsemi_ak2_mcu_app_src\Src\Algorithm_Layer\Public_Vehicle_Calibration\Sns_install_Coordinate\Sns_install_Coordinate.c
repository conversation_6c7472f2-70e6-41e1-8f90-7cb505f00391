/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * APA_CalibPara: 
 * Created on: 2022-11-25 17:30
 * Original designer: 22866
 ******************************************************************************/

/* ============================================================================================== */
/* Header files																				      */
/* ============================================================================================== */
#include "Sns_install_Coordinate.h"
#include "PublicCalAlgorithm_Int.h"
#include "CAN_AppSignalManage.h"
#include "eel_cfg.h"
#include "types.h"



/******************************************************************************/
/****************************** Public Definition *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************
 ******************************************************* 车型配置 : LI XIANG X02 ************************************
 *******************************************************************************/
/* 配置参数1：探头安装位置 */
const SnsCoorSturctType GstrFlashSnsCoor_X02[SNS_INSTALL_GROUP_NUM][SNS_CH_NUM] = 
{
     [SNS_INSTALL_GROUP_FRONT] = 
     {
         [SNS_CH_FLS] =
         {
             .cRadarX   = 3531.2,        /* 左侧边探头x坐标 */
             .cRadarY   = 954.3,         /* 左侧边探头Y坐标 */
         },
         
         [SNS_CH_FLC] =
         {
             .cRadarX = 3715.2,        /* 左角探头x坐标 */
             .cRadarY = 795.2,         /* 左角探头Y坐标 */
         },
 
         [SNS_CH_FLM] =
         {
             .cRadarX    = 3906.8,       /* 左中探头x坐标 */
             .cRadarY    = 337.5,        /* 左中探头Y坐标 */
         },
 
         [SNS_CH_FRM] =
         {
             .cRadarX   = 3906.8,       /* 右中探头x坐标 */    
             .cRadarY   = -337.5,       /* 右中探头Y坐标 */    
         },
 
         [SNS_CH_FRC] =
         {
             .cRadarX= 3715.2,        /* 右角探头x坐标 */
             .cRadarY= -795.2,        /* 右角探头Y坐标 */
         },
 
         [SNS_CH_FRS] =
         {
             .cRadarX  = 3531.2,        /* 右侧边探头x坐标 */
             .cRadarY  = -954.3,        /* 右侧边探头Y坐标 */
         }
     },
         
     [SNS_INSTALL_GROUP_REAR] = 
     {
 
         [SNS_CH_RLS] =
         {
             .cRadarX   = -691.9,          /* 左侧边探头x坐标 */
             .cRadarY   = 926.2,         /* 左侧边探头Y坐标 */
         },
         
         [SNS_CH_RLC] =
         {
             .cRadarX = -1008.3,       /* 左角探头x坐标 */
             .cRadarY = 730.7,         /* 左角探头Y坐标 */
         },
 
         [SNS_CH_RLM] =
         {
             .cRadarX    = -1088.6,       /* 左中探头x坐标 */
             .cRadarY    = 299.7,        /* 左中探头Y坐标 */
         },
 
         [SNS_CH_RRM] =
         {
             .cRadarX   = -1088.6,       /* 右中探头Y坐标 */
             .cRadarY   = -299.7,        /* 右中探头x坐标 */
         },
 
         [SNS_CH_RRC] =
         {
             .cRadarX= -1008.3,       /* 右角探头x坐标 */ 
             .cRadarY= -730.7,        /* 右角探头Y坐标 */
         },
 
         [SNS_CH_RRS] =
         {
             .cRadarX  = -691.9,        /* 右侧边探头x坐标 */
             .cRadarY  = -926.2,      /* 右侧边探头Y坐标 */  
         }
     }
};

/* 配置参数2：探头安装角度，与X轴的角度，弧度值；侧雷达强制改为90° */
const SnsAngleSturctType GstrFlashSnsAngle_X02[SNS_INSTALL_GROUP_NUM][SNS_CH_NUM] = 
{
    /*与竖直x轴正的夹角弧度值*/
    [SNS_INSTALL_GROUP_FRONT] = 
    {
        [SNS_CH_FLS] =
        {
            .cOuterAngle = 1.571,/*角度值为 82.1 1.433  ；强制改为90°*/
        },
        
        [SNS_CH_FLC] =
        {
            .cOuterAngle = 0.476,/*角度值为 27.3*/
        },

        [SNS_CH_FLM] =
        {
            .cOuterAngle = 0.096,/*角度值为 5.5*/
        },

        [SNS_CH_FRM] =
        {
            .cOuterAngle = -0.096,/*角度值为 5.5*/   
        },

        [SNS_CH_FRC] =
        {
            .cOuterAngle = -0.476,/*角度值为 27.3*/
        },

        [SNS_CH_FRS] =
        {
            .cOuterAngle  = -1.571,/*角度值为 82.1 1.433； 强制改为90°*/
        }
   
    },
    /*与竖直x轴负的夹角弧度值*/
    [SNS_INSTALL_GROUP_REAR] = 
    {
        [SNS_CH_RLS] =
        {
            .cOuterAngle = -1.571,/*角度值为 81.5 -1.422 强制改为90°*/
        },
        
        [SNS_CH_RLC] =
        {
            .cOuterAngle = -0.422,/*角度值为 24.2*/
        },

        [SNS_CH_RLM] =
        {
            .cOuterAngle = -0.080,/*角度值为 4.6*/
        },

        [SNS_CH_RRM] =
        {
            .cOuterAngle = 0.080,/*角度值为 4.6*/  
        },

        [SNS_CH_RRC] =
        {
            .cOuterAngle = 0.422,/*角度值为 24.2*/
        },

        [SNS_CH_RRS] =
        {
            .cOuterAngle = 1.571,/*角度值为 81.5 1.422 强制改为90°*/
        }  
    }
};
    
/* 配置参数3：Map To PDC模块的分区坐标*/
const Sns_Install_PDCZoneCoorType GstrPDCZoneCoor_X02_Flash[SNS_INSTALL_GROUP_NUM][ZONE_BORDER_NUM] =
{
    [SNS_INSTALL_GROUP_FRONT] = 
     {
         /*前左侧分区边界阈值起始*/
         [ZONE_BORDER_1] = 
         {
             .fStartX =  3531, 
             .fStartY =  954,
             .fEndX   =  3531, 
             .fEndY   =  2454,
         }, 
         [ZONE_BORDER_2] = 
         {
             .fStartX =  3811, 
             .fStartY =  566,
             .fEndX   =  5194, 
             .fEndY   =  1145,
         }, 
         [ZONE_BORDER_3] = 
         {
             .fStartX =  3906, 
             .fStartY =  0,
             .fEndX   =  5406, 
             .fEndY   =  0,
         },
         [ZONE_BORDER_4] = 
         {
             .fStartX =  3811, 
             .fStartY =  -566,
             .fEndX   =  5194, 
             .fEndY   =  -1145,
         },
         [ZONE_BORDER_5] = 
         {
             .fStartX =  3531, 
             .fStartY =  -954,
             .fEndX   =  3531, 
             .fEndY   =  -2454,
         },
     },

     [SNS_INSTALL_GROUP_REAR] = 
     {
         /*后左侧分区边界阈值起始*/
         [ZONE_BORDER_1] = 
         {
             .fStartX =  -691.9, 
             .fStartY =  926,
             .fEndX   =  -691.9, 
             .fEndY   =  2426,
         }, 
         [ZONE_BORDER_2] = 
         {
             .fStartX =  -1048, 
             .fStartY =  515,
             .fEndX   =  -2523, 
             .fEndY   =  789,
         }, 
         [ZONE_BORDER_3] = 
         {
             .fStartX =  -1088, 
             .fStartY =  0,
             .fEndX   =  -2588, 
             .fEndY   =  0,
         },
         [ZONE_BORDER_4] = 
         {
             .fStartX =  -1048, 
             .fStartY =  -515,
             .fEndX   =  -2523, 
             .fEndY   =  -789,
         },
         [ZONE_BORDER_5] = 
         {
             .fStartX =  -691.9, 
             .fStartY =  -926,
             .fEndX   =  -691.9, 
             .fEndY   =  -2426,
         },  
     }
};

/* 配置参数4：Map模块内部细节分区；用于判断Map点到保杠的距离及相对关系--2024-02-27矫正 */
const MapAreaType GstrMapArea_X02_Flash[SNS_INSTALL_GROUP_NUM][MAP_LINE_NUM] = 
{
    [SNS_INSTALL_GROUP_FRONT] = 
    {
        [MAP_LINE0_1] =
        {
            .fAreaStart_X = 3531.2,
            .fAreaStart_Y = 954.3,
            .fAreaEnd_X = 3531.2,
            .fAreaEnd_Y = 5954.3,
            .fSnsToCarAngle = 1.571,/*角度值为 90*/
        },

        [MAP_LINE1_2] =
        {
            .fAreaStart_X = 3677.2,
            .fAreaStart_Y = 900.2,
            .fAreaEnd_X = 6178.055,
            .fAreaEnd_Y = 5229.833,
            .fSnsToCarAngle = 1.047,/*角度值为 60*/
        },

        [MAP_LINE2_3] =
        {
            .fAreaStart_X = 3715.2,
            .fAreaStart_Y = 795.2,
            .fAreaEnd_X = 8159.38,
            .fAreaEnd_Y = 3086.34,
            .fSnsToCarAngle = 0.476,/*角度值为 27.3*/
        },
            
        [MAP_LINE3_4] =
        {
            .fAreaStart_X = 3906.8,
            .fAreaStart_Y = 337.5,
            .fAreaEnd_X = 8883.78,
            .fAreaEnd_Y = 816.763,
            .fSnsToCarAngle = 0.096,/*角度值为 5.5*/
        },
        
        [MAP_LINE4_5] =
        {
            .fAreaStart_X = 3906.8,
            .fAreaStart_Y = -337.5,
            .fAreaEnd_X = 8883.78,
            .fAreaEnd_Y = -816.763,
            .fSnsToCarAngle = -0.096,/*角度值为 -5.5*/
        },

        [MAP_LINE5_6] =
        {
            .fAreaStart_X = 3715.2,
            .fAreaStart_Y = -795.2,
            .fAreaEnd_X = 8159.38,
            .fAreaEnd_Y = -3086.34,
            .fSnsToCarAngle = -0.476,/*角度值为 -27.3*/
        },

        [MAP_LINE6_7] =
        {
            .fAreaStart_X = 3677.2,
            .fAreaStart_Y = -900.2,
            .fAreaEnd_X = 6178.055,
            .fAreaEnd_Y = -5229.833,
            .fSnsToCarAngle = -1.047,/*角度值为 60*/
        },

        [MAP_LINE7_8] =
        {
            .fAreaStart_X = 3531.2,
            .fAreaStart_Y = -954.3,
            .fAreaEnd_X = 3531.2,
            .fAreaEnd_Y = -5954.3,
            .fSnsToCarAngle = -1.571,/*角度值为 -90*/
        },
    },

    [SNS_INSTALL_GROUP_REAR] = 
    {
        [MAP_LINE0_1] =
        {
            .fAreaStart_X = -691.9,
            .fAreaStart_Y = 926.2,
            .fAreaEnd_X = -691.9,
            .fAreaEnd_Y = 5926.2,
            .fSnsToCarAngle = -1.571,/*角度值为 -90*/
        },

        [MAP_LINE1_2] =
        {
            .fAreaStart_X = -890.4,
            .fAreaStart_Y = 873.4,
            .fAreaEnd_X = -3391.255,
            .fAreaEnd_Y = 5203.033,
            .fSnsToCarAngle = -1.047,/*角度值为 -60*/
        },

        [MAP_LINE2_3] =
        {
            .fAreaStart_X = -1008.3,
            .fAreaStart_Y = 730.7,
            .fAreaEnd_X = -5569.66,
            .fAreaEnd_Y = 2778.63,
            .fSnsToCarAngle = -0.422,/*角度值为 -24.2*/
        },
            
        [MAP_LINE3_4] =
        {
            .fAreaStart_X = -1088.6,
            .fAreaStart_Y = 299.7,
            .fAreaEnd_X = -6072.61,
            .fAreaEnd_Y = 699.27,
            .fSnsToCarAngle = -0.080,/*角度值为 -4.6*/
        },
        
        [MAP_LINE4_5] =
        {
            .fAreaStart_X = -1088.6,
            .fAreaStart_Y = -299.7,
            .fAreaEnd_X = -6072.61,
            .fAreaEnd_Y = -699.27,
            .fSnsToCarAngle = 0.080,/*角度值为 4.6*/
        },

        [MAP_LINE5_6] =
        {
            .fAreaStart_X = -1008.3,
            .fAreaStart_Y = -730.7,
            .fAreaEnd_X = -5569.66,
            .fAreaEnd_Y = -2778.63,
            .fSnsToCarAngle = 0.422,/*角度值为 24.2*/
        },

        [MAP_LINE6_7] =
        {
            .fAreaStart_X = -890.4,
            .fAreaStart_Y = -873.4,
            .fAreaEnd_X = -3391.255,
            .fAreaEnd_Y = -5203.033,
            .fSnsToCarAngle = 1.047,/*角度值为 -60*/
        },

        [MAP_LINE7_8] =
        {
            .fAreaStart_X = -691.9,
            .fAreaStart_Y = -926.2,
            .fAreaEnd_X = -691.9,
            .fAreaEnd_Y = -5926.2,
            .fSnsToCarAngle = 1.571,/*角度值为 -90*/
        }
    }
};

/* 配置参数5：点云的分区；用于判断点云的对应分区 */
const float GfPointCloudArea_X_Coor_X02_Flash[SNS_INSTALL_GROUP_NUM][4] = 
#if 1
{
    [SNS_INSTALL_GROUP_FRONT] = 
    {
        954,338,-338,-954,       
    },
    [SNS_INSTALL_GROUP_REAR] = 
    {
        926,300,-300,-926,       
    },
};

#else
{
    [SNS_INSTALL_GROUP_FRONT] = 
    {
        1104,338,-338,-1104,       
    },
    [SNS_INSTALL_GROUP_REAR] = 
    {
        1076,300,-300,-1076,       
    },
};
#endif

/* 配置参数6：探头的安装高度，主要用于信号组匹配使用 */
const uint16 Gu16SnsInstallHeight_X02[SNS_INSTALL_GROUP_NUM][SNS_CH_NUM] = 
{
    [SNS_INSTALL_GROUP_FRONT] = 
    {
        463,475,517,517,475,463,
    },
    [SNS_INSTALL_GROUP_REAR] = 
    {
        456,473,404,404,473,456,
    }
};


/******************************************************************************
 ******************************************************* 车型配置 : LI XIANG X03 ************************************
 *******************************************************************************/
/* 配置参数1：探头安装位置 */
const SnsCoorSturctType GstrFlashSnsCoor_X03[SNS_INSTALL_GROUP_NUM][SNS_CH_NUM] = 
{
     [SNS_INSTALL_GROUP_FRONT] = 
     {
         [SNS_CH_FLS] =
         {
             .cRadarX   = 3535.8,        /* 左侧边探头x坐标 */
             .cRadarY   = 962.8,         /* 左侧边探头Y坐标 */
         },
         
         [SNS_CH_FLC] =
         {
             .cRadarX = 3718.7,        /* 左角探头x坐标 */
             .cRadarY = 795.2,         /* 左角探头Y坐标 */
         },
 
         [SNS_CH_FLM] =
         {
             .cRadarX    = 3910.4,       /* 左中探头x坐标 */
             .cRadarY    = 337.5,        /* 左中探头Y坐标 */
         },
 
         [SNS_CH_FRM] =
         {
             .cRadarX   = 3910.4,       /* 右中探头x坐标 */    
             .cRadarY   = -337.5,       /* 右中探头Y坐标 */    
         },
 
         [SNS_CH_FRC] =
         {
             .cRadarX= 3718.7,        /* 右角探头x坐标 */
             .cRadarY= -795.2,        /* 右角探头Y坐标 */
         },
 
         [SNS_CH_FRS] =
         {
             .cRadarX  = 3535.8,        /* 右侧边探头x坐标 */
             .cRadarY  = -962.8,        /* 右侧边探头Y坐标 */
         }
     },
         
     [SNS_INSTALL_GROUP_REAR] = 
     {
 
         [SNS_CH_RLS] =
         {
             .cRadarX   = -556.9,        /* 左侧边探头x坐标 */
             .cRadarY   = 939.9,         /* 左侧边探头Y坐标 */
         },
         
         [SNS_CH_RLC] =
         {
             .cRadarX = -954.1,       /* 左角探头x坐标 */
             .cRadarY = 717.5,         /* 左角探头Y坐标 */
         },
 
         [SNS_CH_RLM] =
         {
             .cRadarX    = -1045,       /* 左中探头x坐标 */
             .cRadarY    = 301,          /* 左中探头Y坐标 */
         },
 
         [SNS_CH_RRM] =
         {
             .cRadarX   = -1045,       /* 右中探头Y坐标 */
             .cRadarY   = -301,          /* 右中探头x坐标 */
         },
 
         [SNS_CH_RRC] =
         {
             .cRadarX= -954.1,       /* 右角探头x坐标 */ 
             .cRadarY= -717.5,        /* 右角探头Y坐标 */
         },
 
         [SNS_CH_RRS] =
         {
             .cRadarX  = -556.9,        /* 右侧边探头x坐标 */
             .cRadarY  = -939.9,      /* 右侧边探头Y坐标 */  
         }
     }
};

/* 配置参数2：探头安装角度，与X轴的角度，弧度值；侧雷达强制改为90° */
const SnsAngleSturctType GstrFlashSnsAngle_X03[SNS_INSTALL_GROUP_NUM][SNS_CH_NUM] = 
{
    /*与竖直x轴正的夹角弧度值*/
    [SNS_INSTALL_GROUP_FRONT] = 
    {
        [SNS_CH_FLS] =
        {
            .cOuterAngle = 1.571,/*角度值为 81.7 1.259；强制改为90°*/
        },
        
        [SNS_CH_FLC] =
        {
            .cOuterAngle = 0.476,/*角度值为 27.3*/
        },

        [SNS_CH_FLM] =
        {
            .cOuterAngle = 0.096,/*角度值为 5.5*/
        },

        [SNS_CH_FRM] =
        {
            .cOuterAngle = -0.096,/*角度值为 -5.5*/   
        },

        [SNS_CH_FRC] =
        {
            .cOuterAngle = -0.476,/*角度值为 -27.3*/
        },

        [SNS_CH_FRS] =
        {
            .cOuterAngle  = -1.571,/*角度值为 81.7 1.259； 强制改为90°*/
        }
   
    },
    /*与竖直x轴负的夹角弧度值*/
    [SNS_INSTALL_GROUP_REAR] = 
    {
        [SNS_CH_RLS] =
        {
            .cOuterAngle = -1.571,/*角度值为 81 -1.414 强制改为90°*/
        },
        
        [SNS_CH_RLC] =
        {
            .cOuterAngle = -0.510,/*角度值为 29.2 0.5096 */
        },

        [SNS_CH_RLM] =
        {
            .cOuterAngle = -0.093,/*角度值为 5.3 0.0925*/
        },

        [SNS_CH_RRM] =
        {
            .cOuterAngle = 0.093,/*角度值为 5.3*/  
        },

        [SNS_CH_RRC] =
        {
            .cOuterAngle = 0.510,/*角度值为 29.2*/
        },

        [SNS_CH_RRS] =
        {
            .cOuterAngle = 1.571,/*角度值为 81 1.414 强制改为90°*/
        }  
    }
};
  
/* 配置参数3：Map To PDC模块的分区坐标*/
const Sns_Install_PDCZoneCoorType GstrPDCZoneCoor_X03_Flash[SNS_INSTALL_GROUP_NUM][ZONE_BORDER_NUM] =
{
    [SNS_INSTALL_GROUP_FRONT] = 
     {
         /*前左侧分区边界阈值起始*/
         [ZONE_BORDER_1] = 
         {
             .fStartX =  3535,
             .fStartY =  962,
             .fEndX   =  3535,
             .fEndY   =  2462,
         }, 
         [ZONE_BORDER_2] = 
         {
             .fStartX =  3814,
             .fStartY =  566,
             .fEndX   =  5197,
             .fEndY   =  1145,
         }, 
         [ZONE_BORDER_3] = 
         {
             .fStartX =  3910,
             .fStartY =  0,
             .fEndX   =  5410,
             .fEndY   =  0,
         },
         [ZONE_BORDER_4] = 
         {
             .fStartX =  3814,
             .fStartY =  -566,
             .fEndX   =  5197,
             .fEndY   =  -1145,
         },
         [ZONE_BORDER_5] = 
         {
             .fStartX =  3535,
             .fStartY =  -962,
             .fEndX   =  3535,
             .fEndY   =  -2462,
         },
     },

     [SNS_INSTALL_GROUP_REAR] = 
     {
         /*后左侧分区边界阈值起始*/
         [ZONE_BORDER_1] = 
         {
             .fStartX =  -556,
             .fStartY =  939,
             .fEndX   =  -556,
             .fEndY   =  2439,
         }, 
         [ZONE_BORDER_2] = 
         {
             .fStartX =  -999,
             .fStartY =  509,
             .fEndX   =  -2474,
             .fEndY   =  783,
         }, 
         [ZONE_BORDER_3] = 
         {
             .fStartX =  -999,
             .fStartY =  0,
             .fEndX   =  -2545,
             .fEndY   =  0,
         },
         [ZONE_BORDER_4] = 
         {
             .fStartX =  -999,
             .fStartY =  -509,
             .fEndX   =  -2474,
             .fEndY   =  -783,
         },
         [ZONE_BORDER_5] = 
         {
             .fStartX =  -556, 
             .fStartY =  -939,
             .fEndX   =  -556, 
             .fEndY   =  -2439,
         },  
     }
};
/* 配置参数4：Map模块内部细节分区；用于判断Map点到保杠的距离及相对关系,已修改OK--2023-12-13   */
const MapAreaType GstrMapArea_X03_Flash[SNS_INSTALL_GROUP_NUM][MAP_LINE_NUM] = 
{
    [SNS_INSTALL_GROUP_FRONT] = 
    {
        [MAP_LINE0_1] =
        {
            .fAreaStart_X = 3535.8,
            .fAreaStart_Y = 962.8,
            .fAreaEnd_X = 3535.8,
            .fAreaEnd_Y = 5962.8,
            .fSnsToCarAngle = 1.571,/*角度值为 90*/
        },

        [MAP_LINE1_2] =
        {
            .fAreaStart_X = 3686,
            .fAreaStart_Y = 889.4,
            .fAreaEnd_X = 6186,
            .fAreaEnd_Y = 5219.5,
            .fSnsToCarAngle = 1.047,/*角度值为 60*/
        },

        [MAP_LINE2_3] =
        {
            .fAreaStart_X = 3718.7,
            .fAreaStart_Y = 795.2,
            .fAreaEnd_X = 8162.87,
            .fAreaEnd_Y = 3086.34,
            .fSnsToCarAngle = 0.476,/*角度值为 31.9*/
        },
            
        [MAP_LINE3_4] =
        {
            .fAreaStart_X = 3910.4,
            .fAreaStart_Y = 337.5,
            .fAreaEnd_X = 8887.38,
            .fAreaEnd_Y = 816.763,
            .fSnsToCarAngle = 0.096,/*角度值为 5.2*/
        },
        
        [MAP_LINE4_5] =
        {
            .fAreaStart_X = 3910.4,
            .fAreaStart_Y = -337.5,
            .fAreaEnd_X = 8887.38,
            .fAreaEnd_Y = -816.763,
            .fSnsToCarAngle = -0.096,/*角度值为 -5.2*/
        },

        [MAP_LINE5_6] =
        {
            .fAreaStart_X = 3718.7,
            .fAreaStart_Y = -795.2,
            .fAreaEnd_X = 8162.87,
            .fAreaEnd_Y = -3086.34,
            .fSnsToCarAngle = -0.476,/*角度值为 -31.9*/
        },

        [MAP_LINE6_7] =
        {
            .fAreaStart_X = 3686,
            .fAreaStart_Y = -889.4,
            .fAreaEnd_X = 6186,
            .fAreaEnd_Y = -5219.5,
            .fSnsToCarAngle = -1.047,/*角度值为 -60*/
        },

        [MAP_LINE7_8] =
        {
            .fAreaStart_X = 3535.8,
            .fAreaStart_Y = -962.8,
            .fAreaEnd_X = 3535.8,
            .fAreaEnd_Y = -5962.8,
            .fSnsToCarAngle = -1.571,/*角度值为 -90*/
        },
    },
    
    [SNS_INSTALL_GROUP_REAR] = 
    {
        [MAP_LINE0_1] =
        {
            .fAreaStart_X = -556.9,
            .fAreaStart_Y = 940,
            .fAreaEnd_X = -556.9,
            .fAreaEnd_Y = 5940,
            .fSnsToCarAngle = -1.571,/*角度值为 -90*/
        },

        [MAP_LINE1_2] =
        {
            .fAreaStart_X = -815,
            .fAreaStart_Y = 862.7,
            .fAreaEnd_X = -3315,
            .fAreaEnd_Y = 5193,
            .fSnsToCarAngle = -1.047,/*角度值为 -60*/
        },

        [MAP_LINE2_3] =
        {
            .fAreaStart_X = -954.1,
            .fAreaStart_Y = 717.5,
            .fAreaEnd_X = -5317.82,
            .fAreaEnd_Y = 3158.39,
            .fSnsToCarAngle = -0.510,/*角度值为 -29.2*/
        },
            
        [MAP_LINE3_4] =
        {
            .fAreaStart_X = -1045,
            .fAreaStart_Y = 301,
            .fAreaEnd_X = -6023.39,
            .fAreaEnd_Y = 765.33,
            .fSnsToCarAngle = -0.093,/*角度值为 -5.3*/
        },
        
        [MAP_LINE4_5] =
        {
            .fAreaStart_X = -1045,
            .fAreaStart_Y = -301,
            .fAreaEnd_X = -6023.39,
            .fAreaEnd_Y = -765.33,
            .fSnsToCarAngle = 0.093,/*角度值为 5.3*/
        },

        [MAP_LINE5_6] =
        {
            .fAreaStart_X = -954.1,
            .fAreaStart_Y = -717.5,
            .fAreaEnd_X = -5317.82,
            .fAreaEnd_Y = -3158.39,
            .fSnsToCarAngle = 0.510,/*角度值为 29.2*/
        },

        [MAP_LINE6_7] =
        {
            .fAreaStart_X = -815,
            .fAreaStart_Y = -862.7,
            .fAreaEnd_X = -3315,
            .fAreaEnd_Y = -5193,
            .fSnsToCarAngle = 1.047,/*角度值为 60*/
        },

        [MAP_LINE7_8] =
        {
            .fAreaStart_X = -556.9,
            .fAreaStart_Y = -940,
            .fAreaEnd_X = -556.9,
            .fAreaEnd_Y = -5940,
            .fSnsToCarAngle = 1.571,/*角度值为 90*/
        }
    }
};

/* 配置参数5：点云的分区；用于判断点云的对应分区,已修改OK--2023-12-13   */
const float GfPointCloudArea_X_Coor_X03_Flash[SNS_INSTALL_GROUP_NUM][4] = 
#if 1
{
    [SNS_INSTALL_GROUP_FRONT] = 
    {
        963,338,-338,-963,       
    },
    [SNS_INSTALL_GROUP_REAR] = 
    {
        940,300,-300,-940,       
    },
};

#else
{
    [SNS_INSTALL_GROUP_FRONT] = 
    {
        1113,338,-338,-1113,       
    },
    [SNS_INSTALL_GROUP_REAR] = 
    {
        1090,300,-300,-1090,       
    },
};
#endif

/* 配置参数6：探头的安装高度，主要用于信号组匹配使用 */
const uint16 Gu16SnsInstallHeight_X03[SNS_INSTALL_GROUP_NUM][SNS_CH_NUM] = 
{
    [SNS_INSTALL_GROUP_FRONT] = 
    {
        459,472,513,513,472,459,
    },
    [SNS_INSTALL_GROUP_REAR] = 
    {
        436,460,407,407,460,436,
    }
};


/******************************************************************************
 ******************************************************* 车型配置 : LI XIANG X04 ************************************
 *******************************************************************************/
/* 配置参数1：探头安装位置,已修改OK--2023-12-13   */
const SnsCoorSturctType GstrFlashSnsCoor_X04[SNS_INSTALL_GROUP_NUM][SNS_CH_NUM] = 
{
     [SNS_INSTALL_GROUP_FRONT] = 
     {
         [SNS_CH_FLS] =
         {
             .cRadarX   = 3414.8,        /* 左侧边探头x坐标 */
             .cRadarY   = 943.1,         /* 左侧边探头Y坐标 */
         },
         
         [SNS_CH_FLC] =
         {
             .cRadarX = 3683,        /* 左角探头x坐标 */
             .cRadarY = 683.1,         /* 左角探头Y坐标 */
         },
 
         [SNS_CH_FLM] =
         {
             .cRadarX    = 3809.8,       /* 左中探头x坐标 */
             .cRadarY    = 324.9,        /* 左中探头Y坐标 */
         },
 
         [SNS_CH_FRM] =
         {
             .cRadarX   = 3809.8,       /* 右中探头x坐标 */    
             .cRadarY   = -324.9,       /* 右中探头Y坐标 */    
         },
 
         [SNS_CH_FRC] =
         {
             .cRadarX= 3683,        /* 右角探头x坐标 */
             .cRadarY= -683.1,        /* 右角探头Y坐标 */
         },
 
         [SNS_CH_FRS] =
         {
             .cRadarX  = 3414.8,        /* 右侧边探头x坐标 */
             .cRadarY  = -943.1,        /* 右侧边探头Y坐标 */
         }
     },
         
     [SNS_INSTALL_GROUP_REAR] = 
     {
 
         [SNS_CH_RLS] =
         {
             .cRadarX   = -594.8,        /* 左侧边探头x坐标 */
             .cRadarY   = 926.4,         /* 左侧边探头Y坐标 */
         },
         
         [SNS_CH_RLC] =
         {
             .cRadarX = -929.4,       /* 左角探头x坐标 */
             .cRadarY = 725,         /* 左角探头Y坐标 */
         },
 
         [SNS_CH_RLM] =
         {
             .cRadarX    = -1018.6,       /* 左中探头x坐标 */
             .cRadarY    = 280,          /* 左中探头Y坐标 */
         },
 
         [SNS_CH_RRM] =
         {
             .cRadarX   = -1018.8,       /* 右中探头Y坐标 */
             .cRadarY   = -280,          /* 右中探头x坐标 */
         },
 
         [SNS_CH_RRC] =
         {
             .cRadarX= -929.4,       /* 右角探头x坐标 */ 
             .cRadarY= -725,        /* 右角探头Y坐标 */
         },
 
         [SNS_CH_RRS] =
         {
             .cRadarX  = -594.8,        /* 右侧边探头x坐标 */
             .cRadarY  = -926.4,      /* 右侧边探头Y坐标 */  
         }
     }
};

/* 配置参数2：探头安装角度，与X轴的角度，弧度值；侧雷达强制改为90° */
const SnsAngleSturctType GstrFlashSnsAngle_X04[SNS_INSTALL_GROUP_NUM][SNS_CH_NUM] = 
{
    /*与竖直x轴正的夹角弧度值*/
    [SNS_INSTALL_GROUP_FRONT] = 
    {
        [SNS_CH_FLS] =
        {
            .cOuterAngle = 1.571,/*角度值为83.1 1.4504；强制改为90°*/
        },
        
        [SNS_CH_FLC] =
        {
            .cOuterAngle = 0.609,/*角度值为 34.9*/
        },

        [SNS_CH_FLM] =
        {
            .cOuterAngle = 0.106,/*角度值为 6.1 0.106*/
        },

        [SNS_CH_FRM] =
        {
            .cOuterAngle = -0.106,/*角度值为 6.1*/   
        },

        [SNS_CH_FRC] =
        {
            .cOuterAngle = -0.609,/*角度值为 34.9*/
        },

        [SNS_CH_FRS] =
        {
            .cOuterAngle  = -1.571,/*角度值为83.1 1.4504；强制改为90°*/
        }
   
    },
    /*与竖直x轴负的夹角弧度值*/
    [SNS_INSTALL_GROUP_REAR] = 
    {
        [SNS_CH_RLS] =
        {
            .cOuterAngle = -1.571,/*角度值为 76.2      1.3299强制改为90°*/
        },
        
        [SNS_CH_RLC] =
        {
            .cOuterAngle = -0.3875,/*角度值为 22.2 0.3875 */
        },

        [SNS_CH_RLM] =
        {
            .cOuterAngle = -0.096,/*角度值为 5.5       0.096*/
        },

        [SNS_CH_RRM] =
        {
            .cOuterAngle = 0.096,/*角度值为 5.5*/  
        },

        [SNS_CH_RRC] =
        {
            .cOuterAngle = 0.3875,/*角度值为 22.2*/
        },

        [SNS_CH_RRS] =
        {
            .cOuterAngle = 1.571,/*角度值为 76.2      1.3299强制改为90°*/
        }  
    }
};
  
/* 配置参数3：Map To PDC模块的分区坐标--待修改*/
const Sns_Install_PDCZoneCoorType GstrPDCZoneCoor_X04_Flash[SNS_INSTALL_GROUP_NUM][ZONE_BORDER_NUM] =
{
    [SNS_INSTALL_GROUP_FRONT] = 
     {
         /*前左侧分区边界阈值起始*/
         [ZONE_BORDER_1] = 
         {
             .fStartX =  3414,
             .fStartY =  943,
             .fEndX   =  3414,
             .fEndY   =  2443,
         }, 
         [ZONE_BORDER_2] = 
         {
             .fStartX =  3746,
             .fStartY =  504,
             .fEndX   =  5129,
             .fEndY   =  1083,
         }, 
         [ZONE_BORDER_3] = 
         {
             .fStartX =  3809,
             .fStartY =  0,
             .fEndX   =  5309,
             .fEndY   =  0,
         },
         [ZONE_BORDER_4] = 
         {
             .fStartX =  3746,
             .fStartY =  -504,
             .fEndX   =  5129,
             .fEndY   =  -1083,
         },
         [ZONE_BORDER_5] = 
         {
             .fStartX =  3414,
             .fStartY =  -943,
             .fEndX   =  3414,
             .fEndY   =  -2443,
         },
     },

     [SNS_INSTALL_GROUP_REAR] = 
     {
         /*后左侧分区边界阈值起始*/
         [ZONE_BORDER_1] = 
         {
             .fStartX =  -594, 
             .fStartY =  926,
             .fEndX   =  -594,
             .fEndY   =  2426,
         }, 
         [ZONE_BORDER_2] = 
         {
             .fStartX =  -974,
             .fStartY =  502,
             .fEndX   =  -2449,
             .fEndY   =  776,
         }, 
         [ZONE_BORDER_3] = 
         {
             .fStartX =  -1018,
             .fStartY =  0,
             .fEndX   =  -2518,
             .fEndY   =  0,
         },
         [ZONE_BORDER_4] = 
         {
             .fStartX =  -974,
             .fStartY =  -502,
             .fEndX   =  -2449,
             .fEndY   =  -776,
         },
         [ZONE_BORDER_5] = 
         {
             .fStartX =  -594,
             .fStartY =  -926,
             .fEndX   =  -594, 
             .fEndY   =  -2426,
         },  
     }
};


/* 配置参数4：Map模块内部细节分区；用于判断Map点到保杠的距离及相对关系--已修改OK--2023-12-13 */
const MapAreaType GstrMapArea_X04_Flash[SNS_INSTALL_GROUP_NUM][MAP_LINE_NUM] = 
{
     [SNS_INSTALL_GROUP_FRONT] = 
     {
         [MAP_LINE0_1] =
         {
             .fAreaStart_X = 3414.8,
             .fAreaStart_Y = 943.1,
             .fAreaEnd_X = 3413.78,
             .fAreaEnd_Y = 5943.1,
             .fSnsToCarAngle = 1.571,/*角度值为 90*/
         },

         [MAP_LINE1_2] =
         {
             .fAreaStart_X = 3526,
             .fAreaStart_Y = 892,
             .fAreaEnd_X = 6026,
             .fAreaEnd_Y = 5222,
             .fSnsToCarAngle = 1.0472,/*角度值为 60*/
         },

         [MAP_LINE2_3] =
         {
             .fAreaStart_X = 3683,
             .fAreaStart_Y = 683,
             .fAreaEnd_X = 7784,
             .fAreaEnd_Y = 3543.3,
             .fSnsToCarAngle = 0.609,/*角度值为 34.9*/
         },
             
         [MAP_LINE3_4] =
         {
             .fAreaStart_X = 3809.8,
             .fAreaStart_Y = 324.9,
             .fAreaEnd_X = 8781.2,
             .fAreaEnd_Y = 858.88,
             .fSnsToCarAngle = 0.107,/*角度值为 6.1 0.1065*/
         },
         
         [MAP_LINE4_5] =
         {
             .fAreaStart_X = 3809.8,
             .fAreaStart_Y = -324.9,
             .fAreaEnd_X = 8781.2,
             .fAreaEnd_Y = -858.88,
             .fSnsToCarAngle = 0.107,/*角度值为 -6.1 0.1065*/
         },

         [MAP_LINE5_6] =
         {
             .fAreaStart_X = 3683,
             .fAreaStart_Y = -683,
             .fAreaEnd_X = 7784,
             .fAreaEnd_Y = -3543.3,
             .fSnsToCarAngle = -0.609,/*角度值为 -34.9*/
         },

         [MAP_LINE6_7] =
         {
             .fAreaStart_X = 3526,
             .fAreaStart_Y = -892,
             .fAreaEnd_X = 6026,
             .fAreaEnd_Y = -5222,
             .fSnsToCarAngle = -1.0472,/*角度值为 60*/
         },

         [MAP_LINE7_8] =
         {
             .fAreaStart_X = 3414.8,
             .fAreaStart_Y = -943.1,
             .fAreaEnd_X = 3413.78,
             .fAreaEnd_Y = -5943.1,
             .fSnsToCarAngle = -1.571,/*角度值为 -90*/
         },
     },             

     [SNS_INSTALL_GROUP_REAR] = 
     {
         [MAP_LINE0_1] =
         {
             .fAreaStart_X = -594.8,
             .fAreaStart_Y = 926.4,
             .fAreaEnd_X = -594.8,
             .fAreaEnd_Y = 5926.4,
             .fSnsToCarAngle = -1.571,/*角度值为 -90*/
         },

         [MAP_LINE1_2] =
         {
             .fAreaStart_X = -827.5,
             .fAreaStart_Y = 859,
             .fAreaEnd_X = -3327.5,
             .fAreaEnd_Y = 5189,
             .fSnsToCarAngle = -1.0472,/*角度值为 -60*/
         },

         [MAP_LINE2_3] =
         {
             .fAreaStart_X = -929.4,
             .fAreaStart_Y = 725,
             .fAreaEnd_X = -5558.68,
             .fAreaEnd_Y = 2614.37,
             .fSnsToCarAngle = -0.3875,/*角度值为 -22.2 0.3875 */
         },
             
         [MAP_LINE3_4] =
         {
             .fAreaStart_X = -1018.6,
             .fAreaStart_Y = 280,
             .fAreaEnd_X = -5995.59,
             .fAreaEnd_Y = 759.26,
             .fSnsToCarAngle = -0.096,/*角度值为 -5.5       0.096*/
         },
         
         [MAP_LINE4_5] =
         {
             .fAreaStart_X = -1018.6,
             .fAreaStart_Y = -280,
             .fAreaEnd_X = -5995.59,
             .fAreaEnd_Y = -759.26,
             .fSnsToCarAngle = 0.096,/*角度值为 5.5       0.096*/
         },

         [MAP_LINE5_6] =
         {
             .fAreaStart_X = -929.4,
             .fAreaStart_Y = -725,
             .fAreaEnd_X = -5558.68,
             .fAreaEnd_Y = -2614.37,
             .fSnsToCarAngle = 0.3875,/*角度值为 22.2 0.3875 */
         },

         [MAP_LINE6_7] =
         {
             .fAreaStart_X = -827.5,
             .fAreaStart_Y = -859,
             .fAreaEnd_X = -3327.5,
             .fAreaEnd_Y = -5189,
             .fSnsToCarAngle = 1.0472,/*角度值为 60*/
         },

         [MAP_LINE7_8] =
         {
             .fAreaStart_X = -594.8,
             .fAreaStart_Y = -926.4,
             .fAreaEnd_X = -594.8,
             .fAreaEnd_Y = -5926.4,
             .fSnsToCarAngle = 1.571,/*角度值为 90*/
         }
     }
};

/* 配置参数5：点云的分区；用于判断点云的对应分区,已修改OK--2023-12-13  */
const float GfPointCloudArea_X_Coor_X04_Flash[SNS_INSTALL_GROUP_NUM][4] = 
#if 1
{
    [SNS_INSTALL_GROUP_FRONT] = 
    {
        943,325,-325,-943,       
    },
    [SNS_INSTALL_GROUP_REAR] = 
    {
        926,280,-280,-926,       
    },
};

#else
{
    [SNS_INSTALL_GROUP_FRONT] = 
    {
        1093,325,-325,-1093,       
    },
    [SNS_INSTALL_GROUP_REAR] = 
    {
        1076,280,-280,-1076,       
    },
};
#endif


/* 配置参数6：探头的安装高度，主要用于信号组匹配使用 */
const uint16 Gu16SnsInstallHeight_X04[SNS_INSTALL_GROUP_NUM][SNS_CH_NUM] = 
{
    [SNS_INSTALL_GROUP_FRONT] = 
    {
        456,542,531,531,542,456,
    },
    [SNS_INSTALL_GROUP_REAR] = 
    {
        479,500,410,410,500,479,
    }
};



/******************************************************************************
 ******************************************************* 车型配置 : LI XIANG W02 ************************************
 *******************************************************************************/
/* 配置参数1：探头安装位置,已修改OK--2024-2-27   */
const SnsCoorSturctType GstrFlashSnsCoor_W02[SNS_INSTALL_GROUP_NUM][SNS_CH_NUM] = 
{
     [SNS_INSTALL_GROUP_FRONT] = 
     {
         [SNS_CH_FLS] =
         {
             .cRadarX   = 3561.8,        /* 左侧边探头x坐标 */
             .cRadarY   = 939.9,         /* 左侧边探头Y坐标 */
         },
         
         [SNS_CH_FLC] =
         {
             .cRadarX = 3765.2,        /* 左角探头x坐标 */
             .cRadarY = 746.2,         /* 左角探头Y坐标 */
         },
 
         [SNS_CH_FLM] =
         {
             .cRadarX    = 3896.5,       /* 左中探头x坐标 */
             .cRadarY    = 324.7,        /* 左中探头Y坐标 */
         },
 
         [SNS_CH_FRM] =
         {
             .cRadarX    = 3896.5,       /* 左中探头x坐标 */
             .cRadarY    = -324.7,        /* 左中探头Y坐标 */
         },
 
         [SNS_CH_FRC] =
         {
             .cRadarX = 3765.2,        /* 左角探头x坐标 */
             .cRadarY = -746.2,         /* 左角探头Y坐标 */
         },
 
         [SNS_CH_FRS] =
         {
             .cRadarX   = 3561.8,        /* 左侧边探头x坐标 */
             .cRadarY   = -939.9,         /* 左侧边探头Y坐标 */
         }
     },
         
     [SNS_INSTALL_GROUP_REAR] = 
     {
 
         [SNS_CH_RLS] =
         {
             .cRadarX   = -616.2,      /* 左侧边探头x坐标 */
             .cRadarY   = 917,         /* 左侧边探头Y坐标 */
         },
         
         [SNS_CH_RLC] =
         {
             .cRadarX = -1024,         /* 左角探头x坐标 */
             .cRadarY = 732.7,         /* 左角探头Y坐标 */
         },
 
         [SNS_CH_RLM] =
         {
             .cRadarX    = -1131.6,     /* 左中探头x坐标 */
             .cRadarY    = 299.7,       /* 左中探头Y坐标 */
         },
 
         [SNS_CH_RRM] =
         {
             .cRadarX    = -1131.6,     /* 左中探头x坐标 */
             .cRadarY    = -299.7,       /* 左中探头Y坐标 */
         },
 
         [SNS_CH_RRC] =
         {
             .cRadarX = -1024,         /* 左角探头x坐标 */
             .cRadarY = -732.7,         /* 左角探头Y坐标 */
         },
 
         [SNS_CH_RRS] =
         {
             .cRadarX   = -616.2,      /* 左侧边探头x坐标 */
             .cRadarY   = -917,         /* 左侧边探头Y坐标 */
         }
     }
};

/* 配置参数2：探头安装角度，与X轴的角度，弧度值；侧雷达强制改为90° */
const SnsAngleSturctType GstrFlashSnsAngle_W02[SNS_INSTALL_GROUP_NUM][SNS_CH_NUM] = 
{
    /*与竖直x轴正的夹角弧度值*/
    [SNS_INSTALL_GROUP_FRONT] = 
    {
        [SNS_CH_FLS] =
        {
            .cOuterAngle = 1.571,/*角度值为83.1 1.4504；强制改为90°*/
        },
        
        [SNS_CH_FLC] =
        {
            .cOuterAngle = 0.606,/*角度值为 34.7*/
        },

        [SNS_CH_FLM] =
        {
            .cOuterAngle = 0.075,/*角度值为 4.3*/
        },

        [SNS_CH_FRM] =
        {
            .cOuterAngle = -0.075,/*角度值为 -4.3*/
        },

        [SNS_CH_FRC] =
        {
            .cOuterAngle = -0.606,/*角度值为 -34.7*/
        },

        [SNS_CH_FRS] =
        {
            .cOuterAngle  = -1.571,/*角度值为83.1 1.4504；强制改为90°*/
        }
   
    },
    /*与竖直x轴负的夹角弧度值*/
    [SNS_INSTALL_GROUP_REAR] = 
    {
        [SNS_CH_RLS] =
        {
            .cOuterAngle = -1.571,/*角度值为 76.2      1.3299强制改为90°*/
        },
        
        [SNS_CH_RLC] =
        {
            .cOuterAngle = -0.558,/*角度值为 32 */
        },

        [SNS_CH_RLM] =
        {
            .cOuterAngle = -0.098,/*角度值为 5.6 */
        },

        [SNS_CH_RRM] =
        {
            .cOuterAngle = 0.098,/*角度值为 5.6 */  
        },

        [SNS_CH_RRC] =
        {
            .cOuterAngle = 0.558,/*角度值为 32 */
        },

        [SNS_CH_RRS] =
        {
            .cOuterAngle = 1.571,/*角度值为 76.2      1.3299强制改为90°*/
        }  
    }
};
  
/* 配置参数3：Map To PDC模块的分区坐标*/
const Sns_Install_PDCZoneCoorType GstrPDCZoneCoor_W02_Flash[SNS_INSTALL_GROUP_NUM][ZONE_BORDER_NUM] =
{
    [SNS_INSTALL_GROUP_FRONT] = 
     {
         /*前左侧分区边界阈值起始*/
         [ZONE_BORDER_1] = 
         {
             .fStartX =  3561,
             .fStartY =  939,
             .fEndX   =  3561,
             .fEndY   =  2439,
         }, 
         [ZONE_BORDER_2] = 
         {
             .fStartX =  3830,
             .fStartY =  535,
             .fEndX   =  5213,
             .fEndY   =  1114,
         }, 
         [ZONE_BORDER_3] = 
         {
             .fStartX =  3896,
             .fStartY =  0,
             .fEndX   =  5396,
             .fEndY   =  0,
         },
         [ZONE_BORDER_4] = 
         {
             .fStartX =  3830,
             .fStartY =  -535,
             .fEndX   =  5213,
             .fEndY   =  -1114,
         },
         [ZONE_BORDER_5] = 
         {
             .fStartX =  3561, 
             .fStartY =  -939,
             .fEndX   =  3561, 
             .fEndY   =  -2439,
         },
     },

     [SNS_INSTALL_GROUP_REAR] = 
     {
         /*后左侧分区边界阈值起始*/
         [ZONE_BORDER_1] = 
         {
             .fStartX =  -616, 
             .fStartY =  917,
             .fEndX   =  -616, 
             .fEndY   =  2417,
         }, 
         [ZONE_BORDER_2] = 
         {
             .fStartX =  -1077, 
             .fStartY =  518,
             .fEndX   =  -2552, 
             .fEndY   =  792,
         }, 
         [ZONE_BORDER_3] = 
         {
             .fStartX =  -1131,
             .fStartY =  0,
             .fEndX   =  -2631,
             .fEndY   =  0,
         },
         [ZONE_BORDER_4] = 
         {
             .fStartX =  -1077, 
             .fStartY =  -518,
             .fEndX   =  -2552, 
             .fEndY   =  -792,
         },
         [ZONE_BORDER_5] = 
         {
             .fStartX =  -616, 
             .fStartY =  -917,
             .fEndX   =  -616, 
             .fEndY   =  -2417,
         },  
     }
};


/* 配置参数4：Map模块内部细节分区；用于判断Map点到保杠的距离及相对关系--已修改OK--2024-2-27 */
const MapAreaType GstrMapArea_W02_Flash[SNS_INSTALL_GROUP_NUM][MAP_LINE_NUM] = 
{
     [SNS_INSTALL_GROUP_FRONT] = 
     {
         [MAP_LINE0_1] =
         {
             .fAreaStart_X = 3561.8,
             .fAreaStart_Y = 939.9,
             .fAreaEnd_X = 3561.8,
             .fAreaEnd_Y = 5939.9,
             .fSnsToCarAngle = 1.571,/*角度值为 90*/
         },

         [MAP_LINE1_2] =
         {
             .fAreaStart_X = 3712.3,
             .fAreaStart_Y = 854.5,
             .fAreaEnd_X = 6213.155,
             .fAreaEnd_Y = 5184.133,
             .fSnsToCarAngle = 1.0472,/*角度值为 60*/
         },

         [MAP_LINE2_3] =
         {
             .fAreaStart_X = 3765.2,
             .fAreaStart_Y = 746.2,
             .fAreaEnd_X = 7874.865,
             .fAreaEnd_Y = 3594.121,
             .fSnsToCarAngle = 0.606,/*角度值为 34.7*/
         },
             
         [MAP_LINE3_4] =
         {
             .fAreaStart_X = 3896.5,
             .fAreaStart_Y = 324.7,
             .fAreaEnd_X = 8882.444,
             .fAreaEnd_Y = 699.349,
             .fSnsToCarAngle = 0.075,/*角度值为 4.3*/
         },
         
         [MAP_LINE4_5] =
         {
             .fAreaStart_X = 3896.5,
             .fAreaStart_Y = -324.7,
             .fAreaEnd_X = 8882.444,
             .fAreaEnd_Y = -699.349,
             .fSnsToCarAngle = -0.075,/*角度值为 -4.3*/
         },

         [MAP_LINE5_6] =
         {
             .fAreaStart_X = 3765.2,
             .fAreaStart_Y = -746.2,
             .fAreaEnd_X = 7874.865,
             .fAreaEnd_Y = -3594.121,
             .fSnsToCarAngle = -0.606,/*角度值为 -34.7*/
         },

         [MAP_LINE6_7] =
         {
             .fAreaStart_X = 3712.3,
             .fAreaStart_Y = -854.5,
             .fAreaEnd_X = 6213.155,
             .fAreaEnd_Y = -5184.133,
             .fSnsToCarAngle = -1.0472,/*角度值为 -60*/
         },

         [MAP_LINE7_8] =
         {
             .fAreaStart_X = 3561.8,
             .fAreaStart_Y = -939.9,
             .fAreaEnd_X = 3561.8,
             .fAreaEnd_Y = -5939.9,
             .fSnsToCarAngle = -1.571,/*角度值为 90*/
         },
     },             

     [SNS_INSTALL_GROUP_REAR] = 
     {
         [MAP_LINE0_1] =
         {
             .fAreaStart_X = -616.2,
             .fAreaStart_Y = 917,
             .fAreaEnd_X = -615.182,
             .fAreaEnd_Y = 5917,
             .fSnsToCarAngle = -1.571,/*角度值为 -90*/
         },

         [MAP_LINE1_2] =
         {
             .fAreaStart_X = -866.2,
             .fAreaStart_Y = 859.6,
             .fAreaEnd_X = -3367.055,
             .fAreaEnd_Y = 5189.233,
             .fSnsToCarAngle = -1.0472,/*角度值为 -60*/
         },

         [MAP_LINE2_3] =
         {
             .fAreaStart_X = -1024,
             .fAreaStart_Y = 732.7,
             .fAreaEnd_X = -5265.579,
             .fAreaEnd_Y = 3380.153,
             .fSnsToCarAngle = -0.558,/*角度值为 -32 */
         },
             
         [MAP_LINE3_4] =
         {
             .fAreaStart_X = -1131.6,
             .fAreaStart_Y = 299.7,
             .fAreaEnd_X = -6107.609,
             .fAreaEnd_Y = 788.916,
             .fSnsToCarAngle = -0.098,/*角度值为 -5.6 */
         },
         
         [MAP_LINE4_5] =
         {
             .fAreaStart_X = -1131.6,
             .fAreaStart_Y = -299.7,
             .fAreaEnd_X = -6107.609,
             .fAreaEnd_Y = -788.916,
             .fSnsToCarAngle = 0.098,/*角度值为 5.6 */
         },

         [MAP_LINE5_6] =
         {
             .fAreaStart_X = -1024,
             .fAreaStart_Y = -732.7,
             .fAreaEnd_X = -5265.579,
             .fAreaEnd_Y = -3380.153,
             .fSnsToCarAngle = 0.558,/*角度值为 32 */
         },

         [MAP_LINE6_7] =
         {
             .fAreaStart_X = -866.2,
             .fAreaStart_Y = -859.6,
             .fAreaEnd_X = -3367.055,
             .fAreaEnd_Y = -5189.233,
             .fSnsToCarAngle = 1.0472,/*角度值为 60*/
         },

         [MAP_LINE7_8] =
         {
             .fAreaStart_X = -616.2,
             .fAreaStart_Y = -917,
             .fAreaEnd_X = -615.182,
             .fAreaEnd_Y = -5917,
             .fSnsToCarAngle = 1.571,/*角度值为 90*/
         }
     }
};

/* 配置参数5：点云的分区；用于判断点云的对应分区,已修改OK--2023-2-27  */
const float GfPointCloudArea_X_Coor_W02_Flash[SNS_INSTALL_GROUP_NUM][4] = 
{
    [SNS_INSTALL_GROUP_FRONT] = 
    {
        940,325,-325,-940,       
    },
    [SNS_INSTALL_GROUP_REAR] = 
    {
        917,300,-300,-917,       
    },
};

/* 配置参数6：探头的安装高度，主要用于信号组匹配使用 */
const uint16 Gu16SnsInstallHeight_W02[SNS_INSTALL_GROUP_NUM][SNS_CH_NUM] = 
{
    [SNS_INSTALL_GROUP_FRONT] = 
    {
        467,415,500,500,415,467,
    },
    [SNS_INSTALL_GROUP_REAR] = 
    {
        483,518,437,437,518,483,
    }
};


/******************************APA标定参数_存储在Ram中--*********************************************/
SnsCoorSturctType  GstrRamSnsCoor[SNS_INSTALL_GROUP_NUM][SNS_CH_NUM];
Sns_Install_ParameterType GstrSns_Install_Parameter_Ram;
SnsAngleSturctType   GstrRamSnsAngle_Ram[SNS_INSTALL_GROUP_NUM][SNS_CH_NUM];
SnsCoorConvAngleType GstrSnsCoorConvAngle[SNS_INSTALL_GROUP_NUM];
SnsDisSturctType  GstrSnsAdjacentDis[SNS_INSTALL_GROUP_NUM];
SnsOutAngleType  GstrSnsOutAngle[SNS_INSTALL_GROUP_NUM];//探头外张角输出

Sns_Install_PDCZoneCoorType GstrPDCZoneCoor_Ram[SNS_INSTALL_GROUP_NUM][ZONE_BORDER_NUM];
MapAreaType GstrMapAreaRam[SNS_INSTALL_GROUP_NUM][MAP_LINE_NUM];
float GfPointCloudArea_X_Coor_Ram[SNS_INSTALL_GROUP_NUM][4];
uint16 Gu16SnsInstallHeight_Ram[SNS_INSTALL_GROUP_NUM][SNS_CH_NUM];



/******************************************************************************
* 函数名称: SnsCoorAngleCalc
* 
* 功能描述: 传感器坐标系偏转角度计算
* 
* 输入参数: 无
* 
* 输出参数: 无
* 
* 返 回 值: 无
* 
* 其它说明: 无
* 
* 修改日期              版本号        修改人          修改内容
* 2022-06-21 14:24  V0.1       Yaojie.Cai   初次发布
*******************************************************************************/
void SnsCoorAngleCalc(SnsInstallGroupType LeGroup)
{
    float LfAngle;
    eSnsChannelType LeCh;
    

    SnsCoorConvAngleType *LptrPDCCoorConvAngle;
    LptrPDCCoorConvAngle = &GstrSnsCoorConvAngle[LeGroup];
    SnsCoorSturctType *LptrSnsCoor;
    LptrSnsCoor = GstrRamSnsCoor[LeGroup];

    LptrPDCCoorConvAngle->fLeftCosA[SNS_CH_FRLS] = 0;
    LptrPDCCoorConvAngle->fLeftSinA[SNS_CH_FRLS] = 0;

    for(LeCh = SNS_CH_FRLS;LeCh<=SNS_CH_FRRC;LeCh++)
    {
        LfAngle = atanf(-(LptrSnsCoor[LeCh].cRadarX-LptrSnsCoor[LeCh+1].cRadarX)/(LptrSnsCoor[LeCh].cRadarY-LptrSnsCoor[LeCh+1].cRadarY));
        LptrPDCCoorConvAngle->fAngle[LeCh] = LfAngle;
        
        LptrPDCCoorConvAngle->fRightCosA[LeCh] = cosf(LfAngle);
        LptrPDCCoorConvAngle->fRightSinA[LeCh] = sinf(LfAngle);

        LptrPDCCoorConvAngle->fLeftCosA[LeCh+1] = LptrPDCCoorConvAngle->fRightCosA[LeCh];
        LptrPDCCoorConvAngle->fLeftSinA[LeCh+1] = LptrPDCCoorConvAngle->fRightSinA[LeCh];
    }
    LptrPDCCoorConvAngle->fRightCosA[SNS_CH_FRRS] = 0;
    LptrPDCCoorConvAngle->fRightSinA[SNS_CH_FRRS] = 0; 
#if 0
    if(LeGroup == 0x00)
    {
        for(LeCh = 0;LeCh < 6;LeCh++)
        {
            printf("F-SnsAngle,Ch:%d,Angle:%.3f,LeftSinA:%.3f,LeftCosA:%.3f,RightSinA:%.3f,RightCosA:%.3f\r\n",LeCh,\
                LptrPDCCoorConvAngle->fAngle[LeCh]*57.3,LptrPDCCoorConvAngle->fLeftSinA[LeCh],LptrPDCCoorConvAngle->fLeftCosA[LeCh],\
                LptrPDCCoorConvAngle->fRightSinA[LeCh],LptrPDCCoorConvAngle->fRightCosA[LeCh]);
        }
    }

    if(LeGroup == 0x01)
    {
        for(LeCh = 0;LeCh < 6;LeCh++)
        {
            printf("R-SnsAngle,Ch:%d,Angle:%.3f,LeftSinA:%.3f,LeftCosA:%.3f,RightSinA:%.3f,RightCosA:%.3f\r\n",LeCh,\
                LptrPDCCoorConvAngle->fAngle[LeCh]*57.3,LptrPDCCoorConvAngle->fLeftSinA[LeCh],LptrPDCCoorConvAngle->fLeftCosA[LeCh],\
                LptrPDCCoorConvAngle->fRightSinA[LeCh],LptrPDCCoorConvAngle->fRightCosA[LeCh]);
        }
    }
#endif
}


/******************************************************************************
* 函数名称: SnsAdjacentDisCal
* 
* 功能描述: 左右两个探头间距离计算
* 
* 输入参数: 无
* 
* 输出参数: 无
* 
* 返 回 值: 无
* 
* 其它说明: 无
* 
*******************************************************************************/
void SnsAdjacentDisCal(SnsInstallGroupType LeGroup)
{
    float LfRadarDis;
    eSnsChannelType LeCh;
    
    SnsDisSturctType *LptrSnsAdjacentDis;
    LptrSnsAdjacentDis = &GstrSnsAdjacentDis[LeGroup];
    SnsCoorSturctType *LptrSnsCoor;
    LptrSnsCoor = GstrRamSnsCoor[LeGroup];

    LptrSnsAdjacentDis->fRadarLeftDis[SNS_CH_FRLS] = 0;

    for(LeCh = SNS_CH_FRLS;LeCh <= SNS_CH_FRRC;LeCh++)
    {
        LfRadarDis = PubAI_CalTwoPointDis(LptrSnsCoor[LeCh].cRadarX,LptrSnsCoor[LeCh].cRadarY, \
            LptrSnsCoor[LeCh + 1].cRadarX,LptrSnsCoor[LeCh + 1].cRadarY);
        
        LptrSnsAdjacentDis->fRadarRightDis[LeCh] = LfRadarDis;
        LptrSnsAdjacentDis->fRadarLeftDis[LeCh + 1] = LptrSnsAdjacentDis->fRadarRightDis[LeCh];
    }

    LptrSnsAdjacentDis->fRadarRightDis[SNS_CH_FRRS] = 0;

#if 0
    if(LeGroup == 0x00)
    {
        for(LeCh = 0;LeCh < 6;LeCh++)
        {
            printf("F-SnsAdjacentDis,Ch:%d,Left:%.3f,Right:%.3f\r\n",LeCh,GstrSnsAdjacentDis[0].fRadarLeftDis[LeCh],GstrSnsAdjacentDis[0].fRadarRightDis[LeCh]);
        }
    }
    if(LeGroup == 0x01)
    {
        for(LeCh = 0;LeCh < 6;LeCh++)
        {
            printf("R-SnsAdjacentDis,Ch:%d,Left:%.3f,Right:%.3f\r\n",LeCh,GstrSnsAdjacentDis[0].fRadarLeftDis[LeCh],GstrSnsAdjacentDis[0].fRadarRightDis[LeCh]);
        }
    }
#endif
}


/******************************************************************************
* 函数名称: SnsOutAngleCal
* 
* 功能描述: 探头外张角输出
* 
* 输入参数: 无
* 
* 输出参数: 无
* 
* 返 回 值: 无
* 
* 其它说明: 无
* 
*******************************************************************************/
void SnsOutAngleCal(SnsInstallGroupType LeGroup)
{
    SnsAngleSturctType *LptrSnsAngle;
    eSnsChannelType LeCh;
    
    for(LeCh = SNS_CH_FRLS;LeCh <= SNS_CH_FRRS;LeCh++)
    {
        LptrSnsAngle = &GstrRamSnsAngle_Ram[LeGroup][LeCh];
        GstrSnsOutAngle[LeGroup].fAngle[LeCh] = LptrSnsAngle->cOuterAngle;
        GstrSnsOutAngle[LeGroup].fCosA[LeCh] = cosf(LptrSnsAngle->cOuterAngle);
        GstrSnsOutAngle[LeGroup].fSinA[LeCh] = sinf(LptrSnsAngle->cOuterAngle);
    }
}

/******************************************************************************
* 设计描述 : 车型平台参数区分
* 设计索引 : 
*******************************************************************************/
void APP_Car_Sns_Config_Init(void)
{
    VehicleTypeType LpenuVEH_Type;
    
    ReadCAN_AppSignal_VEH_Type(&LpenuVEH_Type);
    //printf("Vehicle Type:%d\r\n",LpenuVEH_Type);
    if(LpenuVEH_Type == VEHTYPE_X02)
    {
        memcpy(&GstrRamSnsCoor[0][0],&GstrFlashSnsCoor_X02[0][0],sizeof(GstrFlashSnsCoor_X02));
        memcpy(&GstrRamSnsAngle_Ram,&GstrFlashSnsAngle_X02,sizeof(GstrFlashSnsAngle_X02));
        memcpy(&GstrPDCZoneCoor_Ram,&GstrPDCZoneCoor_X02_Flash,SNS_INSTALL_GROUP_NUM*ZONE_BORDER_NUM*sizeof(Sns_Install_PDCZoneCoorType));
    #if (CALIBRATION_EN == STD_ON)
                  /* 在DID_Calibration初始化 */
    #else
        memcpy(&GstrMapAreaRam[0][0],&GstrMapArea_X02_Flash[0][0],sizeof(GstrMapArea_X02_Flash));
        memcpy(&GfPointCloudArea_X_Coor_Ram[0][0],&GfPointCloudArea_X_Coor_X02_Flash[0][0],sizeof(GfPointCloudArea_X_Coor_X02_Flash));
    #endif
        memcpy(&Gu16SnsInstallHeight_Ram[0][0],&Gu16SnsInstallHeight_X02[0][0],sizeof(Gu16SnsInstallHeight_X02));
    }
    else if(LpenuVEH_Type == VEHTYPE_X03)
    {
        memcpy(&GstrRamSnsCoor[0][0],&GstrFlashSnsCoor_X03[0][0],sizeof(GstrFlashSnsCoor_X03));
        memcpy(&GstrRamSnsAngle_Ram,&GstrFlashSnsAngle_X03,sizeof(GstrFlashSnsAngle_X03));
        memcpy(&GstrPDCZoneCoor_Ram,&GstrPDCZoneCoor_X03_Flash,SNS_INSTALL_GROUP_NUM*ZONE_BORDER_NUM*sizeof(Sns_Install_PDCZoneCoorType));
    #if (CALIBRATION_EN == STD_ON)
                  /* 在DID_Calibration初始化 */
    #else
        memcpy(&GstrMapAreaRam[0][0],&GstrMapArea_X03_Flash[0][0],sizeof(GstrMapArea_X03_Flash));
        memcpy(&GfPointCloudArea_X_Coor_Ram[0][0],&GfPointCloudArea_X_Coor_X03_Flash[0][0],sizeof(GfPointCloudArea_X_Coor_X03_Flash));
    #endif
        memcpy(&Gu16SnsInstallHeight_Ram[0][0],&Gu16SnsInstallHeight_X03[0][0],sizeof(Gu16SnsInstallHeight_X03));
    }
    else if(LpenuVEH_Type == VEHTYPE_X04)
    {
        memcpy(&GstrRamSnsCoor[0][0],&GstrFlashSnsCoor_X04[0][0],sizeof(GstrFlashSnsCoor_X04));
        memcpy(&GstrRamSnsAngle_Ram,&GstrFlashSnsAngle_X04,sizeof(GstrFlashSnsAngle_X04));
        memcpy(&GstrPDCZoneCoor_Ram,&GstrPDCZoneCoor_X04_Flash,SNS_INSTALL_GROUP_NUM*ZONE_BORDER_NUM*sizeof(Sns_Install_PDCZoneCoorType));
    #if (CALIBRATION_EN == STD_ON)
                  /* 在DID_Calibration初始化 */
    #else
        memcpy(&GstrMapAreaRam[0][0],&GstrMapArea_X04_Flash[0][0],sizeof(GstrMapArea_X04_Flash));
        memcpy(&GfPointCloudArea_X_Coor_Ram[0][0],&GfPointCloudArea_X_Coor_X04_Flash[0][0],sizeof(GfPointCloudArea_X_Coor_X04_Flash));
    #endif
        memcpy(&Gu16SnsInstallHeight_Ram[0][0],&Gu16SnsInstallHeight_X04[0][0],sizeof(Gu16SnsInstallHeight_X04));
    }
    else if(LpenuVEH_Type == VEHTYPE_W02)
    {
        memcpy(&GstrRamSnsCoor[0][0],&GstrFlashSnsCoor_W02[0][0],sizeof(GstrFlashSnsCoor_W02));
        memcpy(&GstrRamSnsAngle_Ram,&GstrFlashSnsAngle_W02,sizeof(GstrFlashSnsAngle_W02));
        memcpy(&GstrPDCZoneCoor_Ram,&GstrPDCZoneCoor_W02_Flash,SNS_INSTALL_GROUP_NUM*ZONE_BORDER_NUM*sizeof(Sns_Install_PDCZoneCoorType));
    #if (CALIBRATION_EN == STD_ON)
                  /* 在DID_Calibration初始化 */
    #else
        memcpy(&GstrMapAreaRam[0][0],&GstrMapArea_W02_Flash[0][0],sizeof(GstrMapArea_W02_Flash));
        memcpy(&GfPointCloudArea_X_Coor_Ram[0][0],&GfPointCloudArea_X_Coor_W02_Flash[0][0],sizeof(GfPointCloudArea_X_Coor_W02_Flash));
    #endif
        memcpy(&Gu16SnsInstallHeight_Ram[0][0],&Gu16SnsInstallHeight_W02[0][0],sizeof(Gu16SnsInstallHeight_W02));
    }
    else
    {
        /* 车型读取错误时，默认使用X02的车型 */
        memcpy(&GstrRamSnsCoor[0][0],&GstrFlashSnsCoor_X02[0][0],sizeof(GstrFlashSnsCoor_X02));
        memcpy(&GstrRamSnsAngle_Ram,&GstrFlashSnsAngle_X02,sizeof(GstrFlashSnsAngle_X02));
        memcpy(&GstrPDCZoneCoor_Ram,&GstrPDCZoneCoor_X02_Flash,SNS_INSTALL_GROUP_NUM*ZONE_BORDER_NUM*sizeof(Sns_Install_PDCZoneCoorType));
    #if (CALIBRATION_EN == STD_ON)
                  /* 在DID_Calibration初始化 */
    #else
        memcpy(&GstrMapAreaRam[0][0],&GstrMapArea_X02_Flash[0][0],sizeof(GstrMapArea_X02_Flash));
        memcpy(&GfPointCloudArea_X_Coor_Ram[0][0],&GfPointCloudArea_X_Coor_X02_Flash[0][0],sizeof(GfPointCloudArea_X_Coor_X02_Flash));
    #endif
        memcpy(&Gu16SnsInstallHeight_Ram[0][0],&Gu16SnsInstallHeight_X02[0][0],sizeof(Gu16SnsInstallHeight_X02));
    }
#if 0
    uint8 i;
    for(i = 0; i < 8; i++)
    {
        printf("Front Line Start,Line[%d] ,X:%.3f,Y:%.3f\r\n",i,GstrMapAreaRam[0][i].fAreaStart_X,GstrMapAreaRam[0][i].fAreaStart_Y);
    }
    for(i = 0; i < 8; i++)
    {
        printf("Rear Line Start,Line[%d] ,X:%.3f,Y:%.3f\r\n",i,GstrMapAreaRam[1][i].fAreaStart_X,GstrMapAreaRam[1][i].fAreaStart_Y);
    }
#endif


    SnsCoorAngleCalc(SNS_INSTALL_GROUP_FRONT);
    SnsCoorAngleCalc(SNS_INSTALL_GROUP_REAR);
    SnsAdjacentDisCal(SNS_INSTALL_GROUP_FRONT);
    SnsAdjacentDisCal(SNS_INSTALL_GROUP_REAR);
    SnsOutAngleCal(SNS_INSTALL_GROUP_FRONT);
    SnsOutAngleCal(SNS_INSTALL_GROUP_REAR);
}



