/**********************************************************************
*   版权所有    : 2017 深圳市豪恩汽车电子装备有限公司
*   项目名称    : TP_Lib
*   M C U       : R5F10BBG
*   编译环境    : IAR
*   文件名称    : TP_Config.c
*   其它说明    : TP配置
*   当前版本    : V0.1
*   作    者  : 20460
*   完成日期    :
*   内容摘要    :
*   修改记录1   :
*   修改日期    :
*   版 本 号 :
*   修 改 人 :
*   修改内容    :
*   修改记录2   :

***********************************************************************/
/***********************************************************************
* Includes
***********************************************************************/
#include "TP_Config.h"
#include "CAN_COM.h"
#include "TimerManage.h"


/***********************************************************************
* Global objects
***********************************************************************/
uint8 GcTpPhyChannelRxBuff[TPPHYRXDATAMAX];     /* Tp层接收缓冲区，应用层与TP层的数据接口 */
uint8 GcTpFuncChannelRxBuff[TPFUNCRXDATAMAX];   /* 功能寻址地址接收缓冲区最大设置为8      */
uint8 GcPhysicalChannelRxCompleteFlag;          /* TP层物理寻址是否接收到完成的数据包标志 */
uint8 GcFunctionChannelRxCompleteFlag;          /* TP层功能寻址是否接收到完成的数据包标志 */

uint16 GwPhysicalChannelRxLen;
uint8 GcFunctionChannelRxLen;


/**********TP配置参数***********************************************************/
TpCfgStr GsTpMsgCfg =
{
    {GcTpPhyChannelRxBuff,GcTpFuncChannelRxBuff},
    {TPPHYRXDATAMAX,TPFUNCRXDATAMAX},
    uNAs,
    uNAr,
    uNBs,
    uNCr,
    STMIN,
    BS,
    FCWAITMAX,
    TP_PADDING_BYTE_VALUE
};

TPCOMStr GsTPCOMParaCfg =
{
    &GcFrameStatus[ID7DFIdx],
    &GcFrameStatus[ID7D2Idx],
    &GcFrameStatus[ID7DAIdx],
    GcComDataBuffer[ID7DFIdx],
    GcComDataBuffer[ID7D2Idx],
    GcComDataBuffer[ID7DAIdx],
	& Gu8ComDataDLC[ID7DFIdx],
	& Gu8ComDataDLC[ID7D2Idx]
};                  /* 需要给指针赋值 */
TPUDSStr GsTPUDSParaCfg =
{
    &GcPhysicalChannelRxCompleteFlag,
    &GcFunctionChannelRxCompleteFlag,
    &GwPhysicalChannelRxLen,
    &GcFunctionChannelRxLen,
    P2CAN_SERVICE,
    P2CAN_EXTENDSERVICE,
    &GdSystemMsTimer,
};

