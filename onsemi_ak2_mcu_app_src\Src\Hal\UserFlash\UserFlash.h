#ifndef __USERFLASH_H__
#define __USERFLASH_H__
/********************************************************************** 
* Includes 
************************************************************************/ 
#include "LhRule.h"
#include "DID_Calibration.h"
/******************************************************************************
* Constants and macros
*******************************************************************************/

/******************************************************************************
* Constants and macros
*******************************************************************************/
#if (CALIBRATION_EN == STD_ON)
/** @brief: 1581 Flash Block:8 blocks(8K size),62 blocks(32K size), total 2M space*/
#define BLOCK_8K_SIZE                   0x2000u
#define BLOCK_8K_NUM                    8u
#define BLOCK_32K_SIZE                  0x8000u
#define BLOCK_32K_NUM                   62u
#define BLOCK_32K_START_ADDRESS         (BLOCK_32K_SIZE * BLOCK_8K_NUM)

#define BLOCK_256B_PER_32K_PACKAGE_NUM   (BLOCK_32K_SIZE >> 8u)

#define CAL_FLASH_VALID_BLOCK_VAL         0x55555555ul

/** @brief: Calibration A size: 32K */
#define CAL_FLASH_A_START_BLOCK           68u
#define CAL_FLASH_A_BLOCK_LEN             1u
#define CAL_AREA_A_START                  (0x001F0000u)
#define CAL_AREA_A_SIZE                   BLOCK_32K_SIZE*CAL_FLASH_A_BLOCK_LEN
#define CAL_AREA_A_END                    (CAL_AREA_A_START + CAL_AREA_A_SIZE - 1)
#define CAL_FLASH_A_FLAG_ADDRESS          (CAL_AREA_A_START + BLOCK_32K_SIZE - 256u)

/** @brief: Calibration B  32K */
#define CAL_FLASH_B_START_BLOCK           69u
#define CAL_FLASH_B_BLOCK_LEN             1u
#define CAL_AREA_B_START                  0x001F8000u
#define CAL_AREA_B_SIZE                   BLOCK_32K_SIZE*CAL_FLASH_B_BLOCK_LEN
#define CAL_AREA_B_END                    (CAL_AREA_B_START + CAL_AREA_B_SIZE - 1)
#define CAL_FLASH_B_FLAG_ADDRESS          (CAL_AREA_B_START + BLOCK_32K_SIZE - 256u)

/** @brief: Block check */
#if (CAL_FLASH_B_START_BLOCK<(CAL_FLASH_A_START_BLOCK+CAL_FLASH_A_BLOCK_LEN))
    #error "Block size conflict"
#endif

/** @brief: Start address check */
#if ((CAL_AREA_B_START <= CAL_AREA_A_START)||(CAL_AREA_B_END<=CAL_AREA_A_END))
    #error "Block size conflict"
#endif
#define CAL_DATA_FD01_FLS_FRS_ST_ADD                  0U
#define CAL_DATA_FD01_FLS_FRS_ST_SIZE                 (160u)

#define CAL_DATA_FD02_FLS_FRS_CT_ADD                  (CAL_DATA_FD01_FLS_FRS_ST_ADD + CAL_DATA_FD01_FLS_FRS_ST_SIZE)
#define CAL_DATA_FD02_FLS_FRS_CT_SIZE                 (160u)

#define CAL_DATA_FD03_FL_FR_ST_ADD                  (CAL_DATA_FD02_FLS_FRS_CT_ADD + CAL_DATA_FD02_FLS_FRS_CT_SIZE)
#define CAL_DATA_FD03_FL_FR_ST_SIZE                 (160u)

#define CAL_DATA_FD04_FL_FR_CT_ADD                  (CAL_DATA_FD03_FL_FR_ST_ADD + CAL_DATA_FD03_FL_FR_ST_SIZE)
#define CAL_DATA_FD04_FL_FR_CT_SIZE                 (160u)

#define CAL_DATA_FD05_FML_FMR_ST_ADD                  (CAL_DATA_FD04_FL_FR_CT_ADD + CAL_DATA_FD04_FL_FR_CT_SIZE)
#define CAL_DATA_FD05_FML_FMR_ST_SIZE                 (160u)

#define CAL_DATA_FD06_FML_FMR_CT_ADD                  (CAL_DATA_FD05_FML_FMR_ST_ADD + CAL_DATA_FD05_FML_FMR_ST_SIZE)
#define CAL_DATA_FD06_FML_FMR_CT_SIZE                 (160u)

#define CAL_DATA_FD07_RLS_RRS_ST_ADD                  (CAL_DATA_FD06_FML_FMR_CT_ADD + CAL_DATA_FD06_FML_FMR_CT_SIZE)
#define CAL_DATA_FD07_RLS_RRS_ST_SIZE                 (160u)

#define CAL_DATA_FD08_RLS_RRS_CT_ADD                  (CAL_DATA_FD07_RLS_RRS_ST_ADD + CAL_DATA_FD07_RLS_RRS_ST_SIZE)
#define CAL_DATA_FD08_RLS_RRS_CT_SIZE                 (160u)

#define CAL_DATA_FD09_RL_RR_ST_ADD                  (CAL_DATA_FD08_RLS_RRS_CT_ADD + CAL_DATA_FD08_RLS_RRS_CT_SIZE)
#define CAL_DATA_FD09_RL_RR_ST_SIZE                 (160u)

#define CAL_DATA_FD0A_RL_RR_CT_ADD                    (CAL_DATA_FD09_RL_RR_ST_ADD + CAL_DATA_FD09_RL_RR_ST_SIZE)
#define CAL_DATA_FD0A_RL_RR_CT_SIZE                   (160u)

#define CAL_DATA_FD0B_RML_RMR_ST_ADD                  (CAL_DATA_FD0A_RL_RR_CT_ADD + CAL_DATA_FD0A_RL_RR_CT_SIZE)
#define CAL_DATA_FD0B_RML_RMR_ST_SIZE                 (160u)

#define CAL_DATA_FD0C_RML_RMR_CT_ADD                  (CAL_DATA_FD0B_RML_RMR_ST_ADD + CAL_DATA_FD0B_RML_RMR_ST_SIZE)
#define CAL_DATA_FD0C_RML_RMR_CT_SIZE                 (160u)

#define CAL_DATA_FD0D_OBJ_JUDGE_ST_ADD                  (CAL_DATA_FD0C_RML_RMR_CT_ADD + CAL_DATA_FD0C_RML_RMR_CT_SIZE)
#define CAL_DATA_FD0D_OBJ_JUDGE_ST_SIZE                 (320u)

#define CAL_DATA_FD0E_OBJ_JUDGE_CT_ADD                  (CAL_DATA_FD0D_OBJ_JUDGE_ST_ADD + CAL_DATA_FD0D_OBJ_JUDGE_ST_SIZE)
#define CAL_DATA_FD0E_OBJ_JUDGE_CT_SIZE                 (320u)

#define CAL_DATA_FD00_ADD                             (CAL_DATA_FD0E_OBJ_JUDGE_CT_ADD + CAL_DATA_FD0E_OBJ_JUDGE_CT_SIZE)
#define CAL_DATA_FD00_SIZE                            (44u)

#define CAL_DATA_FD22_ADD                             (CAL_DATA_FD00_ADD + CAL_DATA_FD00_SIZE)
#define CAL_DATA_FD22_SIZE                            (80u)

#define CAL_DATA_FD23_ADD                             (CAL_DATA_FD22_ADD + CAL_DATA_FD22_SIZE)
#define CAL_DATA_FD23_SIZE                            (80u)

#define CAL_DATA_FD24_ADD                             (CAL_DATA_FD23_ADD + CAL_DATA_FD23_SIZE)
#define CAL_DATA_FD24_SIZE                            (160u)

#define CAL_DATA_FD25_ADD                             (CAL_DATA_FD24_ADD + CAL_DATA_FD24_SIZE)
#define CAL_DATA_FD25_SIZE                            (160u)

#define CAL_DATA_FD26_ADD                             (CAL_DATA_FD25_ADD + CAL_DATA_FD25_SIZE)
#define CAL_DATA_FD26_SIZE                            (160u)

#define CAL_DATA_FD27_ADD                             (CAL_DATA_FD26_ADD + CAL_DATA_FD26_SIZE)
#define CAL_DATA_FD27_SIZE                            (160u)
#define CAL_DATA_FD28_ADD                             (CAL_DATA_FD27_ADD + CAL_DATA_FD27_SIZE)
#define CAL_DATA_FD28_SIZE                            (160u)

#define CAL_DATA_FD29_ADD                             (CAL_DATA_FD28_ADD + CAL_DATA_FD28_SIZE)
#define CAL_DATA_FD29_SIZE                            (160u)
#define CAL_DATA_FD2A_ADD                             (CAL_DATA_FD29_ADD + CAL_DATA_FD29_SIZE)
#define CAL_DATA_FD2A_SIZE                            (160u)

#define CAL_DATA_FD2B_ADD                             (CAL_DATA_FD2A_ADD + CAL_DATA_FD2A_SIZE)
#define CAL_DATA_FD2B_SIZE                            (160u)

#define CAL_DATA_FD2C_ADD                             (CAL_DATA_FD2B_ADD + CAL_DATA_FD2B_SIZE)
#define CAL_DATA_FD2C_SIZE                            (160u)

#define CAL_DATA_FD2D_ADD                             (CAL_DATA_FD2C_ADD + CAL_DATA_FD2C_SIZE)
#define CAL_DATA_FD2D_SIZE                            (160u)
#define CAL_DATA_FD2E_ADD                             (CAL_DATA_FD2D_ADD + CAL_DATA_FD2D_SIZE)
#define CAL_DATA_FD2E_SIZE                            (160u)

#define CAL_DATA_FD2F_ADD                             (CAL_DATA_FD2E_ADD + CAL_DATA_FD2E_SIZE)
#define CAL_DATA_FD2F_SIZE                            (160u)

#define CAL_DATA_FD20_ADD                             (CAL_DATA_FD2F_ADD + CAL_DATA_FD2F_SIZE)
#define CAL_DATA_FD20_SIZE                            (59u)

#define CAL_DATA_FD21_ADD                             (CAL_DATA_FD20_ADD  + CAL_DATA_FD20_SIZE)
#define CAL_DATA_FD21_SIZE                            (4u)

#define CAL_DATA_FD40_ADD                             (CAL_DATA_FD21_ADD  + CAL_DATA_FD21_SIZE )
#define CAL_DATA_FD40_SIZE                            (40u)

#define CAL_DATA_FD41_ADD                             (CAL_DATA_FD40_ADD  + CAL_DATA_FD40_SIZE )
#define CAL_DATA_FD41_SIZE                            (40u)

#define CAL_DATA_FD42_ADD                             (CAL_DATA_FD41_ADD  + CAL_DATA_FD41_SIZE )
#define CAL_DATA_FD42_SIZE                            (40u)

#define CAL_DATA_FD43_ADD                             (CAL_DATA_FD42_ADD  + CAL_DATA_FD42_SIZE )
#define CAL_DATA_FD43_SIZE                            (40u)

#define CAL_DATA_FD44_ADD                             (CAL_DATA_FD43_ADD  + CAL_DATA_FD43_SIZE )
#define CAL_DATA_FD44_SIZE                            (40u)

#define CAL_DATA_FD45_ADD                             (CAL_DATA_FD44_ADD  + CAL_DATA_FD44_SIZE )
#define CAL_DATA_FD45_SIZE                            (40u)

#define CAL_DATA_FD46_ADD                             (CAL_DATA_FD45_ADD  + CAL_DATA_FD45_SIZE )
#define CAL_DATA_FD46_SIZE                            (40u)

#define CAL_DATA_FD47_ADD                             (CAL_DATA_FD46_ADD  + CAL_DATA_FD46_SIZE )
#define CAL_DATA_FD47_SIZE                            (40u)

#define CAL_DATA_FD48_ADD                             (CAL_DATA_FD47_ADD  + CAL_DATA_FD47_SIZE )
#define CAL_DATA_FD48_SIZE                            (16u)

#define CAL_DATA_FD49_ADD                             (CAL_DATA_FD48_ADD  + CAL_DATA_FD48_SIZE )
#define CAL_DATA_FD49_SIZE                            (16u)

#define CAL_DATA_FD50_ADD                             (CAL_DATA_FD49_ADD  + CAL_DATA_FD49_SIZE )
#define CAL_DATA_FD50_SIZE                            (80u)

#define CAL_DATA_FD51_ADD                             (CAL_DATA_FD50_ADD  + CAL_DATA_FD50_SIZE )
#define CAL_DATA_FD51_SIZE                            (80u)
#define CAL_DATA_FD52_ADD                             (CAL_DATA_FD51_ADD  + CAL_DATA_FD51_SIZE )
#define CAL_DATA_FD52_SIZE                            (80u)
#define CAL_DATA_FD53_ADD                             (CAL_DATA_FD52_ADD  + CAL_DATA_FD52_SIZE )
#define CAL_DATA_FD53_SIZE                            (80u)
#define CAL_DATA_FD54_ADD                             (CAL_DATA_FD53_ADD  + CAL_DATA_FD53_SIZE )
#define CAL_DATA_FD54_SIZE                            (80u)
#define CAL_DATA_FD55_ADD                             (CAL_DATA_FD54_ADD  + CAL_DATA_FD54_SIZE )
#define CAL_DATA_FD55_SIZE                            (80u)
#define CAL_DATA_FD56_ADD                             (CAL_DATA_FD55_ADD  + CAL_DATA_FD55_SIZE )
#define CAL_DATA_FD56_SIZE                            (80u)
#define CAL_DATA_FD57_ADD                             (CAL_DATA_FD56_ADD  + CAL_DATA_FD56_SIZE )
#define CAL_DATA_FD57_SIZE                            (80u)
#define CAL_DATA_FD58_ADD                             (CAL_DATA_FD57_ADD  + CAL_DATA_FD57_SIZE )
#define CAL_DATA_FD58_SIZE                            (80u)
#define CAL_DATA_FD59_ADD                             (CAL_DATA_FD58_ADD  + CAL_DATA_FD58_SIZE )
#define CAL_DATA_FD59_SIZE                            (80u)
#define CAL_DATA_FD5A_ADD                             (CAL_DATA_FD59_ADD  + CAL_DATA_FD59_SIZE )
#define CAL_DATA_FD5A_SIZE                            (80u)
#define CAL_DATA_FD5B_ADD                             (CAL_DATA_FD5A_ADD  + CAL_DATA_FD5A_SIZE )
#define CAL_DATA_FD5B_SIZE                            (80u)
#define CAL_DATA_FD5C_ADD                             (CAL_DATA_FD5B_ADD  + CAL_DATA_FD5B_SIZE )
#define CAL_DATA_FD5C_SIZE                            (80u)
#define CAL_DATA_FD5D_ADD                             (CAL_DATA_FD5C_ADD  + CAL_DATA_FD5C_SIZE )
#define CAL_DATA_FD5D_SIZE                            (80u)
#define CAL_DATA_FD5E_ADD                             (CAL_DATA_FD5D_ADD  + CAL_DATA_FD5D_SIZE )
#define CAL_DATA_FD5E_SIZE                            (80u)
#define CAL_DATA_FD5F_ADD                             (CAL_DATA_FD5E_ADD  + CAL_DATA_FD5E_SIZE )
#define CAL_DATA_FD5F_SIZE                            (80u)
#define CAL_DATA_FD60_ADD                             (CAL_DATA_FD5F_ADD  + CAL_DATA_FD5F_SIZE )
#define CAL_DATA_FD60_SIZE                            (80u)
#define CAL_DATA_FD61_ADD                             (CAL_DATA_FD60_ADD  + CAL_DATA_FD60_SIZE )
#define CAL_DATA_FD61_SIZE                            (80u)
#define CAL_DATA_FD62_ADD                             (CAL_DATA_FD61_ADD  + CAL_DATA_FD61_SIZE )
#define CAL_DATA_FD62_SIZE                            (80u)
#define CAL_DATA_FD63_ADD                             (CAL_DATA_FD62_ADD  + CAL_DATA_FD62_SIZE )
#define CAL_DATA_FD63_SIZE                            (80u)
#define CAL_DATA_FD64_ADD                             (CAL_DATA_FD63_ADD  + CAL_DATA_FD63_SIZE )
#define CAL_DATA_FD64_SIZE                            (80u)
#define CAL_DATA_FD65_ADD                             (CAL_DATA_FD64_ADD  + CAL_DATA_FD64_SIZE )
#define CAL_DATA_FD65_SIZE                            (80u)
#define CAL_DATA_FD66_ADD                             (CAL_DATA_FD65_ADD  + CAL_DATA_FD65_SIZE )
#define CAL_DATA_FD66_SIZE                            (80u)
#define CAL_DATA_FD67_ADD                             (CAL_DATA_FD66_ADD  + CAL_DATA_FD66_SIZE )
#define CAL_DATA_FD67_SIZE                            (80u)

#define CAL_DATA_FD70_ADD                             (CAL_DATA_FD67_ADD  + CAL_DATA_FD67_SIZE )
#define CAL_DATA_FD70_SIZE                            (21u)

#define CAL_DATA_FDA0_ADD                             (CAL_DATA_FD70_ADD  + CAL_DATA_FD70_SIZE )
#define CAL_DATA_FDA0_SIZE                            (16u)

#define CAL_DATA_FDA1_ADD                             (CAL_DATA_FDA0_ADD  + CAL_DATA_FDA0_SIZE )
#define CAL_DATA_FDA1_SIZE                            (48u)

#define CAL_DATA_FDA2_ADD                             (CAL_DATA_FDA1_ADD  + CAL_DATA_FDA1_SIZE )
#define CAL_DATA_FDA2_SIZE                            (48u)

#define CAL_DATA_FDA3_ADD                             (CAL_DATA_FDA2_ADD  + CAL_DATA_FDA2_SIZE )
#define CAL_DATA_FDA3_SIZE                            (24u)

#define CAL_DATA_FDA4_ADD                             (CAL_DATA_FDA3_ADD  + CAL_DATA_FDA3_SIZE )
#define CAL_DATA_FDA4_SIZE                            (16u)

/******************************************************************************
* Typedef definitions
*******************************************************************************/



/******************************************************************************
* External objects
*******************************************************************************/


/******************************************************************************
* Global Functions
*******************************************************************************/
extern uint8 EraseFlash(uint32 Lu32StartBlockIdx, uint16 Lu16AppBlockSize);
extern uint8_t WriteToCodeFlash(uint32_t Lu32WriteStartAddress, uint32_t Lu32WriteDataLength, const uint8_t* pData);
extern uint8_t ReadToCodeFlash(uint32_t Lu32ReadStartAddress, uint32_t Lu32ReadDataLength, uint8_t* pData);
#endif//(CALIBRATION_EN == STD_ON)
#endif
/*****************************************************END EEPROM.h*********************************************************/
 
