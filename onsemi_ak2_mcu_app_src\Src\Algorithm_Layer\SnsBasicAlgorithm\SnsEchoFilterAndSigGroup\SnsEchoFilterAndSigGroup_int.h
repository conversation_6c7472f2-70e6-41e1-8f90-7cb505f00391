/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * SnsEchoFilterAndSigGroup_int: 
 * Created on: 2023-07-11 10:29
 * Original designer: AntonyFang
 ******************************************************************************/

#ifndef SnsEchoFilterAndSigGroup_int_H
#define SnsEchoFilterAndSigGroup_int_H

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "SnsEchoFilterAndSigGroup_type.h"
#include "SnsRawData_Type.h"


/******************************************************************************
 * Compiler option used to distinguish inclusion done by the owner
 *****************************************************************************/


/******************************************************************************
 * Inclusion of private header files
 *****************************************************************************/
 

/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
//extern SnsSigGroupDataCacheType GstrSnsSigGroupDataCache[PDC_SNS_GROUP_NUM][PDC_SNS_CH_NUM];


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
void SnsSigGroupDataPowerOnInit(void);
void SnsEchoFilterAndSigGroupDataGet(PDCSnsGroupType LeGroup,PDCSnsChannelType LePDCSnsCh);
void ClearSnsSigGroupDataUpdateFlag(PDCSnsGroupType LeGroup,PDCSnsChannelType LePDCSnsCh);
void SnsSigGroupDataCacheInit(PDCSnsGroupType LeGroup);
void SnsSigGroupDataCache_PDC_Sns_Init(PDCSnsGroupType LeGroup);
void PDCSnsDE_CE_DataPDC_Sns_Init(PDCSnsGroupType LenuSnsGroup);
void PDCSnsDE_CE_DataInit(PDCSnsGroupType LenuSnsGroup);




#endif /* end of SnsEchoFilterAndSigGroup_int_H */

