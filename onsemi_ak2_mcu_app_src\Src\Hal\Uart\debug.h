#ifndef     __PRINTF_H_
#define     __PRINTF_H_

#include "types.h"
#include <stdio.h>

/*****************************  Module ID **********************************/
#define UNLIMIT_PRINT_ID                                0xFFFEu
#define MODULE_PRINT_CTRL_ID                            0xFFFFu

#define SPI_MODULE_IDX                                  0u
#define APA_MANAGE_MODULE_IDX                           1u
#define CAN_MODULE_IDX                                  2u
#define ODO_MODULE_IDX                                  3u
#define PDC_MODULE_IDX                                  4u
#define APA_SOLT_MODULE_IDX                             5u
#define PLFM_MODULE_IDX                                 6u
#define MCAL_MODULE_IDX                                 7u
#define SBC_MODULE_IDX                                  8u
#define SNS_TASK_MODULE_IDX                             9u
#define MAP_MODULE_IDX                                  10u
#define DSI3COM_MODULE_IDX                              11u
#define ELMOS42_MODULE_IDX                              12u
#define ELMOS17_MODULE_IDX                              13u
#define SNSDIAG_MODULE_IDX                              14u
#define RDUS_MODULE_IDX                                 15u
#define RDUM_MODULE_IDX                                 16u


/*****************************Print Enable**********************************/
#define UART_PRINT_EN                                   STD_ON
#define SPI_PRINT_EN                                    STD_ON
#define DEBUG_PRINT_EN                                  STD_ON

#define TEMP_DEBUG_PRINT_EN                             STD_OFF

#define SPI_DEBUG_PRINT_EN                              STD_OFF
#define APA_MANAGE_DEBUG_PRINT_EN                       STD_OFF
#define CAN_DEBUG_PRINT_EN                              STD_OFF
#define ODO_DEBUG_PRINT_EN                              STD_OFF
#define PDC_DEBUG_PRINT_EN                              STD_OFF
#define APA_SOLT_DEBUG_PRINT_EN                         STD_OFF
#define PLFM_DEBUG_PRINT_EN                             STD_OFF
#define MCAL_DEBUG_PRINT_EN                             STD_OFF
#define SBC_DEBUG_PRINT_EN                              STD_OFF
#define SNS_TASK_DEBUG_PRINT_EN                         STD_OFF
#define MAP_DEBUG_PRINT_EN                              STD_OFF
#define DSI3COM_DEBUG_PRINT_EN                          STD_OFF
#define ELMOS42_DEBUG_PRINT_EN                          STD_OFF
#define ELMOS17_DEBUG_PRINT_EN                          STD_OFF
#define SNSDIAG_DEBUG_PRINT_EN                          STD_OFF
#define RDUS_DEBUG_PRINT_EN                             STD_ON
#define RDUM_DEBUG_PRINT_EN                             STD_ON


#if (DEBUG_PRINT_EN == STD_ON)
#define DEBUG_PRINT(...)                                debug_log(UNLIMIT_PRINT_ID, "DEBUG", __VA_ARGS__)
#define DEBUG_RX_SCAN                                   ScanDebugRx
#else
#define DEBUG_PRINT(...)                                (void)0
#define DEBUG_RX_SCAN(...)                              (void)0
#endif

#if (DEBUG_PRINT_EN == STD_ON)
extern void ScanDebugRx(void);
extern void debug_log(uint16 ModuleID, const char *ModuleName, const char *log, ...);
extern void DebugExitFunc(void);
extern void Debug_SetModulePrintEn(uint16 ModuleIdx, boolean en);
#endif

#if ((SPI_DEBUG_PRINT_EN == STD_ON) && (UART_PRINT_EN == STD_ON))
#define SPI_DEBUG_PRINT(...)                            debug_log(SPI_MODULE_IDX, "SPI", __VA_ARGS__)
#else
#define SPI_DEBUG_PRINT(...)                            (void)0
#endif

#if ((CAN_DEBUG_PRINT_EN == STD_ON) && (UART_PRINT_EN == STD_ON))
#define CAN_DEBUG_PRINT(...)                            debug_log(CAN_MODULE_IDX, "CAN", __VA_ARGS__)
#else
#define CAN_DEBUG_PRINT(...)                            (void)0
#endif

#if ((APA_MANAGE_DEBUG_PRINT_EN == STD_ON) && (UART_PRINT_EN == STD_ON))
#define APA_MANAGE_DEBUG_PRINT(...)                     debug_log(APA_MANAGE_MODULE_IDX, "ApaManage", __VA_ARGS__)
#else
#define APA_MANAGE_DEBUG_PRINT(...)                     (void)0
#endif

#if ((ODO_DEBUG_PRINT_EN == STD_ON) && (UART_PRINT_EN == STD_ON))
#define ODO_DEBUG_PRINT(...)                            debug_log(ODO_MODULE_IDX, "ODO", __VA_ARGS__)
#else
#define ODO_DEBUG_PRINT(...)                            (void)0
#endif

#if ((PDC_DEBUG_PRINT_EN == STD_ON) && (UART_PRINT_EN == STD_ON))
#define PDC_DEBUG_PRINT(...)                            debug_log(PDC_MODULE_IDX, "PDC", __VA_ARGS__)
#else
#define PDC_DEBUG_PRINT(...)                            (void)0
#endif

#if ((APA_SOLT_DEBUG_PRINT_EN == STD_ON) && (UART_PRINT_EN == STD_ON))
#define APA_SOLT_DEBUG_PRINT(...)                       debug_log(APA_SOLT_MODULE_IDX, "ApaSolt", __VA_ARGS__)
#else
#define APA_SOLT_DEBUG_PRINT(...)                       (void)0
#endif

#if ((PLFM_DEBUG_PRINT_EN == STD_ON) && (UART_PRINT_EN == STD_ON))
#define PLFM_DEBUG_PRINT(...)                           debug_log(PLFM_MODULE_IDX, "PLFM", __VA_ARGS__)
#else
#define PLFM_DEBUG_PRINT(...)                           (void)0
#endif

#if ((MCAL_DEBUG_PRINT_EN == STD_ON) && (UART_PRINT_EN == STD_ON))
#define MCAL_DEBUG_PRINT(...)                           debug_log(MCAL_MODULE_IDX, "MCAL", __VA_ARGS__)
#else
#define MCAL_DEBUG_PRINT(...)                           (void)0
#endif

#if ((SBC_DEBUG_PRINT_EN == STD_ON) && (UART_PRINT_EN == STD_ON))
#define SBC_DEBUG_PRINT(...)                            debug_log(SBC_MODULE_IDX, "SBC", __VA_ARGS__)
#else
#define SBC_DEBUG_PRINT(...)                            (void)0
#endif

#if ((SNS_TASK_DEBUG_PRINT_EN == STD_ON) && (UART_PRINT_EN == STD_ON))
#define SNS_TASK_DEBUG_PRINT(...)                            debug_log(SNS_TASK_MODULE_IDX, "Sns Task", __VA_ARGS__)
#else
#define SNS_TASK_DEBUG_PRINT(...)                            (void)0
#endif

#if ((MAP_DEBUG_PRINT_EN == STD_ON) && (UART_PRINT_EN == STD_ON))
#define MAP_DEBUG_PRINT(...)                            debug_log(MAP_MODULE_IDX, "Map", __VA_ARGS__)
#else
#define MAP_DEBUG_PRINT(...)                            (void)0
#endif

#if ((DSI3COM_DEBUG_PRINT_EN == STD_ON) && (UART_PRINT_EN == STD_ON))
#define DSI3COM_DEBUG_PRINT(...)                        debug_log(DSI3COM_MODULE_IDX, "DSI3COM", __VA_ARGS__)
#else
#define DSI3COM_DEBUG_PRINT(...)                        (void)0
#endif

#if ((ELMOS42_DEBUG_PRINT_EN == STD_ON) && (UART_PRINT_EN == STD_ON))
#define ELMOS42_DEBUG_PRINT(...)                        debug_log(ELMOS42_MODULE_IDX, "ELMOS42", __VA_ARGS__)
#else
#define ELMOS42_DEBUG_PRINT(...)                        (void)0
#endif

#if ((ELMOS17_DEBUG_PRINT_EN == STD_ON) && (UART_PRINT_EN == STD_ON))
#define ELMOS17_DEBUG_PRINT(...)                        debug_log(ELMOS17_MODULE_IDX, "ELMOS17", __VA_ARGS__)
#else
#define ELMOS17_DEBUG_PRINT(...)                        (void)0
#endif

#if ((SNSDIAG_DEBUG_PRINT_EN == STD_ON) && (UART_PRINT_EN == STD_ON))
#define SNSDIAG_DEBUG_PRINT(...)                        debug_log(SNSDIAG_MODULE_IDX, "SNSDIAG", __VA_ARGS__)
#else
#define SNSDIAG_DEBUG_PRINT(...)                        (void)0
#endif

#if ((RDUS_DEBUG_PRINT_EN == STD_ON) && (UART_PRINT_EN == STD_ON))
#define RDUS_DEBUG_PRINT(...)                           debug_log(RDUS_MODULE_IDX, "RDUS", __VA_ARGS__)
#else
#define RDUS_DEBUG_PRINT(...)                           (void)0
#endif

#if ((RDUM_DEBUG_PRINT_EN == STD_ON) && (UART_PRINT_EN == STD_ON))
#define RDUM_DEBUG_PRINT(...)                           debug_log(RDUM_MODULE_IDX, "RDUM", __VA_ARGS__)
#else
#define RDUM_DEBUG_PRINT(...)                           (void)0
#endif



extern void UartTriggerRx (void);

#endif    /*#ifndef __PRINT_H_*/

