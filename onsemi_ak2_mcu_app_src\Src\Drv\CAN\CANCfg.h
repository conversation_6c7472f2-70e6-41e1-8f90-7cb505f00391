#ifndef   __CAN_CFG_H__
#define   __CAN_CFG_H__
 
/******************************************************************************* 
* Includes 
********************************************************************************/ 
#include "LhRule.h"
/****************************************************************************** 
* Constants and macros 
*******************************************************************************/

/******************************************************************************
* 设计描述 : CAN有效数据长度类型定义
* 设计索引 : 
*******************************************************************************/
/* 调试报文试能标志 */
#define DEBUG_FRAME_TX_EN                   STD_ON//STD_OFF


/*can stack运行周期*/
#define COM_MANAGE_PERIOD   TASK_1MS_COUNT

#define COM_DATA_MAX        64U             /* 一帧报文的最大字节个数   */

#define DIAG_MSG_RX         SDF_U8_VALUE0
#define DIAG_MSG_TX         SDF_U8_VALUE1
#define APP_MSG_RX          SDF_U8_VALUE2
#define APP_MSG_TX          SDF_U8_VALUE3
#define NM_MSG_RX           SDF_U8_VALUE4
#define NM_MSG_TX           SDF_U8_VALUE5

/********RX Frame****************************************/


/********Tx Frame*****************************************/

#if(DEBUG_FRAME_TX_EN==STD_OFF)
/* 消息ID编号顺序，映射底层与COM缓冲 */
typedef enum
{
    /* RX */
    ID7D2Idx = 0,
    ID7DFIdx,
    ID236Idx,
    ID237Idx,

    ID395Idx,
    ID384Idx,
    ID2D4Idx,
    ID5A4Idx,

    ID504Idx,
    ID176Idx,
    ID086Idx,
    ID096Idx,

    ID0D6Idx,
    ID209Idx,
    ID39DIdx,
    ID09EIdx,

    ID397Idx,
    ID0C2Idx,
    ID388Idx,
    RXFRAMEMAXNUM, /* 接收数据大于16个的时候需要注意接收buffer数量在S1只有16个 */

    /* TX */
    ID7DAIdx = RXFRAMEMAXNUM,
    ID085Idx,
    ID238Idx,
    ID244Idx,

    ID245Idx,

    ID254Idx,
    ID2E4Idx,
    ID2F4Idx,


    ID2D5Idx,
    ID284Idx,
    ID285Idx,
    ID286Idx,
    ID287Idx,

    ID2A4Idx,
    ID2A5Idx,
    ID2B4Idx,
    ID2B5Idx,
    ID394Idx,
    ID387Idx,
    ID665Idx,
    ID685Idx,

    RXANDTXFRAMEMAXNUM,
}Can_MessageIdNoType;
#else
/* 消息ID编号顺序，映射底层与COM缓冲 */
typedef enum
{
    /* RX */
    ID7D2Idx = 0,
    ID7DFIdx,
    ID236Idx,
    ID237Idx,

    ID395Idx,
    ID384Idx,
    ID2D4Idx,
    ID5A4Idx,

    ID504Idx,
    ID176Idx,
    ID086Idx,
    ID096Idx,

    ID0D6Idx,
    ID209Idx,
    ID39DIdx,
    ID09EIdx,

    ID397Idx,
    ID0C2Idx,
    ID388Idx,
    ID6F0Idx,
    RXFRAMEMAXNUM, /* 接收数据大于16个的时候需要注意接收buffer数量在S1只有16个 */

    /* TX */
    ID7DAIdx = RXFRAMEMAXNUM,
    ID085Idx,
    ID238Idx,
    ID244Idx,

    ID245Idx,

    ID254Idx,
    ID2E4Idx,
    ID2F4Idx,


    ID2D5Idx,
    ID284Idx,
    ID285Idx,
    ID286Idx,
    ID287Idx,

    ID2A4Idx,
    ID2A5Idx,
    ID2B4Idx,
    ID2B5Idx,
    ID394Idx,
    ID387Idx,
    ID665Idx,
    ID685Idx,

    //debug id
    ID6F2Idx,
    ID6F3Idx,
    ID6F4Idx,
    ID6F5Idx,
    ID6F6Idx,
    ID6F7Idx,
    ID6F8Idx,
    ID6F9Idx,
    ID6FAIdx,
    ID6FBIdx,
    ID6FCIdx,
    ID6FDIdx,
    ID6FEIdx,
    ID6FFIdx,

    ID6EEIdx,
    ID6EFIdx,
    ID6F1Idx,
    RXANDTXFRAMEMAXNUM,
}Can_MessageIdNoType;
#endif

/*整车配置参数*/
typedef enum
{
    NOT_CONFIGURED = 0x0u,/*没有装配*/
    CONFIGURED,/*有装配*/
}VehNetworkCfg;

/* 帧类型 */
typedef enum
{
    STANDARD_ID = 0u,    /**< 标准帧ID */
    EXTENDED_ID,    /**< 扩展帧ID */
}CAN_FrameType;    /**< CAN帧类型 */
#define TXMAILBOXNUM    ((uint8)6u)//每个CAN通道有6个合并的buffer可以使用//(RXANDTXFRAMEMAXNUM - RXFRAMEMAXNUM)
/****************************************************************************** 
* External objects 
********************************************************************************/ 
extern const uint32 GcComFrameIdent[];
extern const uint32 GcComFrameFilter[];
extern uint8 GcComFrameDLC[];
extern const volatile uint8 GcComFrameType[];
extern const volatile uint16 GwComFramePeriod[];
extern const volatile uint16 GwComFrameTimeout[];
extern const uint32 GcComRegisterID[];
extern volatile VehNetworkCfg GwComFrameCfgSts[RXANDTXFRAMEMAXNUM];
extern const volatile CAN_FrameType GcComFrameTypeCfg[RXANDTXFRAMEMAXNUM];
/******************************************************************************* 
* Global Functions 
********************************************************************************/ 

#endif
