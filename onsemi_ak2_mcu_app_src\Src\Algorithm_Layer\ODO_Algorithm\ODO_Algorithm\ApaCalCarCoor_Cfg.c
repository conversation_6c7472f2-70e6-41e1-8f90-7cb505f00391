/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * ApaCalCarCoor.c: 
 * Created on: 2020-12-07 19:09
 * Original designer: 22554
 ******************************************************************************/

/******************************************************************************
 * Includes
 *****************************************************************************/
#include "ApaCalCarCoor.h"
#include "CAN_AppSignalManage.h"




/******************************************************************************/
/**
 * @brief   填入      获取车速        的函数
 */
/******************************************************************************/
uint16 CPOS_GetCar_Speed(void)
{
	Car_SpeedType Lu16Car_Speed;
    ReadCAN_AppSignal_Car_Speed(&Lu16Car_Speed);
    return Lu16Car_Speed;
}

/******************************************************************************/
/**
 * @brief   填入      获取轮速直行判断结果            的函数
 */
/******************************************************************************/
bool CPOS_GetStraight(void)
{
    bool Lu8Rtn = FALSE;
    Wheel_SpeedType Lu16FLSpd, Lu16FRSpd, Lu16RLSpd, Lu16RRSpd;
    ReadCAN_AppSignal_Wheel_Speed(&Lu16FLSpd,CAR_FL_WHEEL);
    ReadCAN_AppSignal_Wheel_Speed(&Lu16FRSpd,CAR_FR_WHEEL);
    ReadCAN_AppSignal_Wheel_Speed(&Lu16RLSpd,CAR_RL_WHEEL);
    ReadCAN_AppSignal_Wheel_Speed(&Lu16RRSpd,CAR_RR_WHEEL);
    if((Lu16FLSpd == Lu16FRSpd)&&(Lu16RLSpd == Lu16RRSpd)&&(Lu16FLSpd == Lu16RLSpd))
    {
        Lu8Rtn = TRUE;
    }
    return Lu8Rtn;
}
/******************************************************************************/
/**
 * @brief   填入      获取左后轮轮速脉冲方向            的函数
 */
/******************************************************************************/
Car_WheelPulseDirType CPOS_GetCar_RLWheelDir(void)
{
    Car_WheelPulseType LstrCar_RLWheelInf;
	ReadCAN_AppSignal_Car_WheelPulseAndDir(&LstrCar_RLWheelInf,CAR_RL_WHEEL);
	return LstrCar_RLWheelInf.enuCar_WheelPulseDir;
}
/******************************************************************************/
/**
 * @brief   填入      获取右后轮轮速脉冲方向            的函数
 */
/******************************************************************************/
Car_WheelPulseDirType CPOS_GetCar_RRWheelDir(void)
{
    Car_WheelPulseType LstrCar_RRWheelInf;
   	ReadCAN_AppSignal_Car_WheelPulseAndDir(&LstrCar_RRWheelInf,CAR_RL_WHEEL);
   	return LstrCar_RRWheelInf.enuCar_WheelPulseDir;
}
/******************************************************************************/
/**
 * @brief   填入      获取当前配置参数类型         的函数 0xFF为无效值
 */
/******************************************************************************/
void CPOS_GetCaliParamValidSts(CPOS_CaliParamValidType *LpSts)
{
#if 0
    uint8 Lu8CarType;
    Lu8CarType = Read_VEH_Type();
    if(Lu8CarType == 0xFFu)
    {
        *LpSts = CALI_PARAM_INVALID;
    }
    else
    {
        *LpSts = CALI_PARAM_VALID;
    }
#endif
	*LpSts = CALI_PARAM_VALID;

}

