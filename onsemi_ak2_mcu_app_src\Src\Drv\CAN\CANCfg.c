/**********************************************************************
*   版权所有    : 2017 深圳市豪恩汽车电子装备有限公司
*   项目名称    : 
*   M C U       : 
*   编译环境    : 
*   文件名称    : CANCfg.c
*   其它说明    : CAN参数配置
*   当前版本    : V0.1
*   作    者    : 
*   完成日期    :
*   内容摘要    :
*   修改记录1   :
*   修改日期    :
*   版 本 号 :
*   修 改 人 :
*   修改内容    :
*   修改记录2   :
***********************************************************************/

/***********************************************************************
* Includes 
***********************************************************************/

#include "CANCfg.h"

/***************************************************************************** 
* Global objects 
******************************************************************************/
#if(DEBUG_FRAME_TX_EN==STD_OFF)
/* 定义每个报文的ID，通过宏定义进行选择 */
const uint32 GcComFrameIdent[RXANDTXFRAMEMAXNUM] = 
{
    /* RX */
    [ID7D2Idx] = 0x7D2,
    [ID7DFIdx] = 0x7DF,

    [ID236Idx] = 0x236u,
    [ID237Idx] = 0x237u,
    [ID395Idx] = 0x395u,
    [ID384Idx] = 0x384u,
    [ID2D4Idx] = 0x2D4u,

    [ID5A4Idx] = 0x5A4u,
    [ID504Idx] = 0x504u,
    [ID176Idx] = 0x176u,
    [ID086Idx] = 0x086u,
    [ID096Idx] = 0x096u,

    [ID0D6Idx] = 0x0D6u,
    [ID209Idx] = 0x209u,
    [ID39DIdx] = 0x39Du,
    [ID09EIdx] = 0x09Eu,
    [ID397Idx] = 0x397u,

    [ID0C2Idx] = 0x0C2u,
    [ID388Idx] = 0x388u,
    /* TX */
    [ID7DAIdx] = 0x7DA,
    [ID085Idx] = 0x085u,
    [ID238Idx] = 0x238u,

    [ID244Idx] = 0x244u,

    [ID245Idx] = 0x245u,
    [ID254Idx] = 0x254u,

    [ID2E4Idx] = 0x2E4u,
    [ID2F4Idx] = 0x2F4u,

    [ID2D5Idx] = 0x2D5u,
    [ID284Idx] = 0x284u,
    [ID285Idx] = 0x285u,

    [ID286Idx] = 0x286u,
    [ID287Idx] = 0x287u,
    [ID2A4Idx] = 0x2A4u,
    [ID2A5Idx] = 0x2A5u,

    [ID2B4Idx] = 0x2B4u,
    [ID2B5Idx] = 0x2B5u,
    [ID394Idx] = 0x394u,

    [ID387Idx] = 0x387u,
    [ID665Idx] = 0x665u,
    [ID685Idx] = 0x685u,
};
/*各邮箱的接收掩码，如不够用需要复用*/
const uint32 GcComFrameFilter[RXANDTXFRAMEMAXNUM] =
{
    /* RX */
    [ID7D2Idx] = 0x7D2,
    [ID7DFIdx] = 0x7DF,

    [ID236Idx] = 0x236u,
    [ID237Idx] = 0x237u,
    [ID395Idx] = 0x395u,
    [ID384Idx] = 0x384u,
    [ID2D4Idx] = 0x2D4u,

    [ID5A4Idx] = 0x5A4u,
    [ID504Idx] = 0x504u,
    [ID176Idx] = 0x176u,
    [ID086Idx] = 0x086u,
    [ID096Idx] = 0x096u,

    [ID0D6Idx] = 0x0D6u,
    [ID209Idx] = 0x209u,
    [ID39DIdx] = 0x39Du,
    [ID09EIdx] = 0x09Eu,
    [ID397Idx] = 0x397u,

    [ID0C2Idx] = 0x0C2u,
    [ID388Idx] = 0x388u,
    /* TX */
    [ID7DAIdx] = 0x7DA,
    [ID085Idx] = 0x085u,
    [ID238Idx] = 0x238u,

    [ID244Idx] = 0x244u,

    [ID245Idx] = 0x245u,
    [ID254Idx] = 0x254u,

    [ID2E4Idx] = 0x2E4u,
    [ID2F4Idx] = 0x2F4u,

    [ID2D5Idx] = 0x2D5u,
    [ID284Idx] = 0x284u,
    [ID285Idx] = 0x285u,

    [ID286Idx] = 0x286u,
    [ID287Idx] = 0x287u,
    [ID2A4Idx] = 0x2A4u,
    [ID2A5Idx] = 0x2A5u,

    [ID2B4Idx] = 0x2B4u,
    [ID2B5Idx] = 0x2B5u,
    [ID394Idx] = 0x394u,

    [ID387Idx] = 0x387u,
    [ID665Idx] = 0x665u,
    [ID685Idx] = 0x685u,

};
/*各邮箱的屏蔽器掩码，如不够用需要复用*/
const uint32 GcComRegisterID[RXANDTXFRAMEMAXNUM] =
{
    /* RX */
    [ID7D2Idx] = 0x7FFul,
    [ID7DFIdx] = 0x7FFul,

    [ID236Idx] = 0x7FFul,
    [ID237Idx] = 0x7FFul,
    [ID395Idx] = 0x7FFul,
    [ID384Idx] = 0x7FFul,
    [ID2D4Idx] = 0x7FFul,

    [ID5A4Idx] = 0x7FFul,
    [ID504Idx] = 0x7FFul,
    [ID176Idx] = 0x7FFul,
    [ID086Idx] = 0x7FFul,
    [ID096Idx] = 0x7FFul,

    [ID0D6Idx] = 0x7FFul,
    [ID209Idx] = 0x7FFul,
    [ID39DIdx] = 0x7FFul,
    [ID09EIdx] = 0x7FFul,
    [ID397Idx] = 0x7FFul,

    [ID0C2Idx] = 0x7FFul,
    [ID388Idx] = 0x7FFul,
    /* TX */
    [ID7DAIdx] = 0x7FFul,
    [ID085Idx] = 0x7FFul,
    [ID238Idx] = 0x7FFul,

    [ID244Idx] = 0x7FFul,
    [ID245Idx] = 0x7FFul,
    [ID254Idx] = 0x7FFul,

    [ID2E4Idx] = 0x7FFul,
    [ID2F4Idx] = 0x7FFul,
    [ID2D5Idx] = 0x7FFul,

    [ID284Idx] = 0x7FFul,
    [ID285Idx] = 0x7FFul,
    [ID286Idx] = 0x7FFul,

    [ID287Idx] = 0x7FFul,
    [ID2A4Idx] = 0x7FFul,
    [ID2A5Idx] = 0x7FFul,

    [ID2B4Idx] = 0x7FFul,
    [ID2B5Idx] = 0x7FFul,
    [ID394Idx] = 0x7FFul,

    [ID387Idx] = 0x7FFul,
    [ID665Idx] = 0x7FFul,
    [ID685Idx] = 0x7FFul,

};

/*定义报文DLC的长度 ,接收报文DLC暂时未应用*/
uint8 GcComFrameDLC[RXANDTXFRAMEMAXNUM] =
{
    /* RX */
    [ID7D2Idx] = 15,
    [ID7DFIdx] = 8,

    [ID236Idx] = 8,
    [ID237Idx] = 8,
    [ID395Idx] = 8,
    [ID384Idx] = 8,
    [ID2D4Idx] = 8,

    [ID5A4Idx] = 8,
    [ID504Idx] = 8,
    [ID176Idx] = 13,
    [ID086Idx] = 13,
    [ID096Idx] = 8,

    [ID0D6Idx] = 13,
    [ID209Idx] = 8,
    [ID39DIdx] = 8,
    [ID09EIdx] = 8,
    [ID397Idx] = 8,

    [ID0C2Idx] = 8,
    [ID388Idx] = 15,
    /* TX */
    [ID7DAIdx] = 15,
    [ID085Idx] = 13,
    [ID238Idx] = 13,

    [ID244Idx] = 13,

    [ID245Idx] = 13,
    [ID254Idx] = 13,

    [ID2E4Idx] = 13,
    [ID2F4Idx] = 13,

    [ID2D5Idx] = 15,
    [ID284Idx] = 13,
    [ID285Idx] = 13,

    [ID286Idx] = 13,
    [ID287Idx] = 13,
    [ID2A4Idx] = 13,
    [ID2A5Idx] = 13,

    [ID2B4Idx] = 13,
    [ID2B5Idx] = 13,
    [ID394Idx] = 13,
    [ID387Idx] = 13,

    [ID665Idx] = 8,
    [ID685Idx] = 8,
};

/* 定义报文的类型  */
const volatile uint8 GcComFrameType[RXANDTXFRAMEMAXNUM] =
{
    /* RX */
    [ID7D2Idx] = DIAG_MSG_RX,
    [ID7DFIdx] = DIAG_MSG_RX,

    [ID236Idx] = APP_MSG_RX,
    [ID237Idx] = APP_MSG_RX,
    [ID395Idx] = APP_MSG_RX,
    [ID384Idx] = APP_MSG_RX,
    [ID2D4Idx] = APP_MSG_RX,

    [ID5A4Idx] = APP_MSG_RX,
    [ID504Idx] = APP_MSG_RX,
    [ID176Idx] = APP_MSG_RX,
    [ID086Idx] = APP_MSG_RX,
    [ID096Idx] = APP_MSG_RX,

    [ID0D6Idx] = APP_MSG_RX,
    [ID209Idx] = APP_MSG_RX,
    [ID39DIdx] = APP_MSG_RX,
    [ID09EIdx] = APP_MSG_RX,
    [ID397Idx] = APP_MSG_RX,

    [ID0C2Idx] = APP_MSG_RX,
    [ID388Idx] = APP_MSG_RX,
    /* TX */
    [ID7DAIdx] = DIAG_MSG_TX,
    [ID085Idx] = APP_MSG_TX,
    [ID238Idx] = APP_MSG_TX,
    [ID244Idx] = APP_MSG_TX,

    [ID245Idx] = APP_MSG_TX,

    [ID254Idx] = APP_MSG_TX,
    [ID2E4Idx] = APP_MSG_TX,
    [ID2F4Idx] = APP_MSG_TX,

    [ID2D5Idx] = APP_MSG_TX,
    [ID284Idx] = APP_MSG_TX,
    [ID285Idx] = APP_MSG_TX,

    [ID286Idx] = APP_MSG_TX,
    [ID287Idx] = APP_MSG_TX,
    [ID2A4Idx] = APP_MSG_TX,
    [ID2A5Idx] = APP_MSG_TX,

    [ID2B4Idx] = APP_MSG_TX,
    [ID2B5Idx] = APP_MSG_TX,
    [ID394Idx] = APP_MSG_TX,

    [ID387Idx] = APP_MSG_TX,
    [ID665Idx] = APP_MSG_TX,
    [ID685Idx] = APP_MSG_TX,
};

/* 定义报文的周期 ,对于非周期应用型报文设置为0  */
const volatile uint16 GwComFramePeriod[RXANDTXFRAMEMAXNUM] = 
{
    /* RX */
    [ID7D2Idx] = (0 / COM_MANAGE_PERIOD),
    [ID7DFIdx] = (0 / COM_MANAGE_PERIOD),

    [ID236Idx] = (20 / COM_MANAGE_PERIOD),
    [ID237Idx] = (20 / COM_MANAGE_PERIOD),
    [ID395Idx] = (100 / COM_MANAGE_PERIOD),
    [ID384Idx] = (100 / COM_MANAGE_PERIOD),
    [ID2D4Idx] = (20 / COM_MANAGE_PERIOD),

    [ID5A4Idx] = (500 / COM_MANAGE_PERIOD),
    [ID504Idx] = (100 / COM_MANAGE_PERIOD),
    [ID176Idx] = (10 / COM_MANAGE_PERIOD),
    [ID086Idx] = (10 / COM_MANAGE_PERIOD),
    [ID096Idx] = (10 / COM_MANAGE_PERIOD),

    [ID0D6Idx] = (10 / COM_MANAGE_PERIOD),
    [ID209Idx] = (20 / COM_MANAGE_PERIOD),
    [ID39DIdx] = (100 / COM_MANAGE_PERIOD),
    [ID09EIdx] = (10 / COM_MANAGE_PERIOD),
    [ID397Idx] = (100 / COM_MANAGE_PERIOD),

    [ID0C2Idx] = (10 / COM_MANAGE_PERIOD),
    [ID388Idx] = (100 / COM_MANAGE_PERIOD),
    /* TX */
    [ID7DAIdx] = (0U / COM_MANAGE_PERIOD),
    [ID085Idx] = (10U / COM_MANAGE_PERIOD),

    [ID238Idx] = (20U / COM_MANAGE_PERIOD),
    [ID244Idx] = (20U / COM_MANAGE_PERIOD),

    [ID245Idx] = (20U / COM_MANAGE_PERIOD),

    [ID254Idx] = (20U / COM_MANAGE_PERIOD),
    [ID2E4Idx] = (20U / COM_MANAGE_PERIOD),
    [ID2F4Idx] = (20U / COM_MANAGE_PERIOD),

    [ID2D5Idx] = (20U / COM_MANAGE_PERIOD),
    [ID284Idx] = (20U / COM_MANAGE_PERIOD),
    [ID285Idx] = (20U / COM_MANAGE_PERIOD),

    [ID286Idx] = (20U / COM_MANAGE_PERIOD),
    [ID287Idx] = (20U / COM_MANAGE_PERIOD),
    [ID2A4Idx] = (20U / COM_MANAGE_PERIOD),
    [ID2A5Idx] = (20U / COM_MANAGE_PERIOD),

    [ID2B4Idx] = (20U / COM_MANAGE_PERIOD),
    [ID2B5Idx] = (20U / COM_MANAGE_PERIOD),
    [ID394Idx] = (100U / COM_MANAGE_PERIOD),

    [ID387Idx] = (100U / COM_MANAGE_PERIOD),
    [ID665Idx] = (1000U / COM_MANAGE_PERIOD),
    [ID685Idx] = (5000U / COM_MANAGE_PERIOD),
};

/*超时周期10倍，最大5s*/ 
const volatile uint16 GwComFrameTimeout[RXANDTXFRAMEMAXNUM] =
{
    /* RX */
    [ID7D2Idx] = (0 / COM_MANAGE_PERIOD),
    [ID7DFIdx] = (0 / COM_MANAGE_PERIOD),

    [ID236Idx] = (200 / COM_MANAGE_PERIOD),
    [ID237Idx] = (200 / COM_MANAGE_PERIOD),
    [ID395Idx] = (1000 / COM_MANAGE_PERIOD),
    [ID384Idx] = (1000 / COM_MANAGE_PERIOD),
    [ID2D4Idx] = (200 / COM_MANAGE_PERIOD),

    [ID5A4Idx] = (3000 / COM_MANAGE_PERIOD),
    [ID504Idx] = (1000 / COM_MANAGE_PERIOD),
    [ID176Idx] = (100 / COM_MANAGE_PERIOD),
    [ID086Idx] = (100 / COM_MANAGE_PERIOD),
    [ID096Idx] = (100 / COM_MANAGE_PERIOD),

    [ID0D6Idx] = (100 / COM_MANAGE_PERIOD),
    [ID209Idx] = (200 / COM_MANAGE_PERIOD),
    [ID39DIdx] = (1000 / COM_MANAGE_PERIOD),
    [ID09EIdx] = (100 / COM_MANAGE_PERIOD),
    [ID397Idx] = (1000 / COM_MANAGE_PERIOD),

    [ID0C2Idx] = (100 / COM_MANAGE_PERIOD),
    [ID388Idx] = (1000 / COM_MANAGE_PERIOD),
    /* TX */
    [ID7DAIdx] = (0U / COM_MANAGE_PERIOD),
    [ID085Idx] = (10U / COM_MANAGE_PERIOD),
    [ID238Idx] = (20U / COM_MANAGE_PERIOD),
    [ID244Idx] = (20U / COM_MANAGE_PERIOD),

    [ID245Idx] = (20U / COM_MANAGE_PERIOD),

    [ID254Idx] = (20U / COM_MANAGE_PERIOD),
    [ID2E4Idx] = (20U / COM_MANAGE_PERIOD),
    [ID2F4Idx] = (20U / COM_MANAGE_PERIOD),

    [ID2D5Idx] = (20U / COM_MANAGE_PERIOD),
    [ID284Idx] = (20U / COM_MANAGE_PERIOD),
    [ID285Idx] = (20U / COM_MANAGE_PERIOD),

    [ID286Idx] = (20U / COM_MANAGE_PERIOD),
    [ID287Idx] = (20U / COM_MANAGE_PERIOD),
    [ID2A4Idx] = (20U / COM_MANAGE_PERIOD),
    [ID2A5Idx] = (20U / COM_MANAGE_PERIOD),

    [ID2B4Idx] = (20U / COM_MANAGE_PERIOD),
    [ID2B5Idx] = (20U / COM_MANAGE_PERIOD),
    [ID394Idx] = (100U / COM_MANAGE_PERIOD),

    [ID387Idx] = (100U / COM_MANAGE_PERIOD),
    [ID665Idx] = (1000U / COM_MANAGE_PERIOD),
    [ID685Idx] = (5000U / COM_MANAGE_PERIOD),

};

/**< 收发报文的帧类型配置数组 */
const volatile CAN_FrameType GcComFrameTypeCfg[RXANDTXFRAMEMAXNUM] = 
{
    [ID7D2Idx] = STANDARD_ID,
    [ID7DFIdx] = STANDARD_ID,

    [ID236Idx] = STANDARD_ID,
    [ID237Idx] = STANDARD_ID,
    [ID395Idx] = STANDARD_ID,
    [ID384Idx] = STANDARD_ID,
    [ID2D4Idx] = STANDARD_ID,

    [ID5A4Idx] = STANDARD_ID,
    [ID504Idx] = STANDARD_ID,
    [ID176Idx] = STANDARD_ID,
    [ID086Idx] = STANDARD_ID,
    [ID096Idx] = STANDARD_ID,

    [ID0D6Idx] = STANDARD_ID,
    [ID209Idx] = STANDARD_ID,
    [ID39DIdx] = STANDARD_ID,
    [ID09EIdx] = STANDARD_ID,
    [ID397Idx] = STANDARD_ID,

    [ID0C2Idx] = STANDARD_ID,
    [ID388Idx] = STANDARD_ID,
  /* TX */
    [ID7DAIdx] = STANDARD_ID,
    [ID085Idx] = STANDARD_ID,

    [ID238Idx] = STANDARD_ID,
    [ID244Idx] = STANDARD_ID,
    [ID245Idx] = STANDARD_ID,
    [ID254Idx] = STANDARD_ID,
    [ID2E4Idx] = STANDARD_ID,

    [ID2F4Idx] = STANDARD_ID,
    [ID2D5Idx] = STANDARD_ID,
    [ID284Idx] = STANDARD_ID,
    [ID285Idx] = STANDARD_ID,

    [ID286Idx] = STANDARD_ID,
    [ID287Idx] = STANDARD_ID,
    [ID2A4Idx] = STANDARD_ID,
    [ID2A5Idx] = STANDARD_ID,

    [ID2B4Idx] = STANDARD_ID,
    [ID2B5Idx] = STANDARD_ID,
    [ID394Idx] = STANDARD_ID,

    [ID387Idx] = STANDARD_ID,
    [ID665Idx] = STANDARD_ID,
    [ID685Idx] = STANDARD_ID,

};
#else
/* 定义每个报文的ID，通过宏定义进行选择 */
const uint32 GcComFrameIdent[RXANDTXFRAMEMAXNUM] =
{
    /* RX */
    [ID7D2Idx] = 0x7D2,
    [ID7DFIdx] = 0x7DF,

    [ID236Idx] = 0x236u,
    [ID237Idx] = 0x237u,
    [ID395Idx] = 0x395u,
    [ID384Idx] = 0x384u,
    [ID2D4Idx] = 0x2D4u,

    [ID5A4Idx] = 0x5A4u,
    [ID504Idx] = 0x504u,
    [ID176Idx] = 0x176u,
    [ID086Idx] = 0x086u,
    [ID096Idx] = 0x096u,

    [ID0D6Idx] = 0x0D6u,
    [ID209Idx] = 0x209u,
    [ID39DIdx] = 0x39Du,
    [ID09EIdx] = 0x09Eu,
    [ID397Idx] = 0x397u,

    [ID0C2Idx] = 0x0C2u,
    [ID388Idx] = 0x388u,

    [ID6F0Idx] = 0x6F0u,
    /* TX */
    [ID7DAIdx] = 0x7DA,
    [ID085Idx] = 0x085u,
    [ID238Idx] = 0x238u,

    [ID244Idx] = 0x244u,

    [ID245Idx] = 0x245u,
    [ID254Idx] = 0x254u,

    [ID2E4Idx] = 0x2E4u,
    [ID2F4Idx] = 0x2F4u,

    [ID2D5Idx] = 0x2D5u,
    [ID284Idx] = 0x284u,
    [ID285Idx] = 0x285u,

    [ID286Idx] = 0x286u,
    [ID287Idx] = 0x287u,
    [ID2A4Idx] = 0x2A4u,
    [ID2A5Idx] = 0x2A5u,

    [ID2B4Idx] = 0x2B4u,
    [ID2B5Idx] = 0x2B5u,
    [ID394Idx] = 0x394u,

    [ID387Idx] = 0x387u,
    [ID665Idx] = 0x665u,
    [ID685Idx] = 0x685u,
    //debug id
    [ID6F2Idx] = 0x6F2u,
    [ID6F3Idx] = 0x6F3u,
    [ID6F4Idx] = 0x6F4u,
    [ID6F5Idx] = 0x6F5u,
    [ID6F6Idx] = 0x6F6u,
    [ID6F7Idx] = 0x6F7u,
    [ID6F8Idx] = 0x6F8u,
    [ID6F9Idx] = 0x6F9u,
    [ID6FAIdx] = 0x6FAu,
    [ID6FBIdx] = 0x6FBu,
    [ID6FCIdx] = 0x6FCu,
    [ID6FDIdx] = 0x6FDu,
    [ID6FEIdx] = 0x6FEu,
    [ID6FFIdx] = 0x6FFu,

    [ID6EEIdx] = 0x6EEu,
    [ID6EFIdx] = 0x6EFu,
    [ID6F1Idx] = 0x6F1u,
};
/*各邮箱的接收掩码，如不够用需要复用*/
const uint32 GcComFrameFilter[RXANDTXFRAMEMAXNUM] =
{
    /* RX */
    [ID7D2Idx] = 0x7D2,
    [ID7DFIdx] = 0x7DF,

    [ID236Idx] = 0x236u,
    [ID237Idx] = 0x237u,
    [ID395Idx] = 0x395u,
    [ID384Idx] = 0x384u,
    [ID2D4Idx] = 0x2D4u,

    [ID5A4Idx] = 0x5A4u,
    [ID504Idx] = 0x504u,
    [ID176Idx] = 0x176u,
    [ID086Idx] = 0x086u,
    [ID096Idx] = 0x096u,

    [ID0D6Idx] = 0x0D6u,
    [ID209Idx] = 0x209u,
    [ID39DIdx] = 0x39Du,
    [ID09EIdx] = 0x09Eu,
    [ID397Idx] = 0x397u,

    [ID0C2Idx] = 0x0C2u,
    [ID388Idx] = 0x388u,
    [ID6F0Idx] = 0x6F0u,
    /* TX */
    [ID7DAIdx] = 0x7DA,
    [ID085Idx] = 0x085u,
    [ID238Idx] = 0x238u,

    [ID244Idx] = 0x244u,

    [ID245Idx] = 0x245u,
    [ID254Idx] = 0x254u,

    [ID2E4Idx] = 0x2E4u,
    [ID2F4Idx] = 0x2F4u,

    [ID2D5Idx] = 0x2D5u,
    [ID284Idx] = 0x284u,
    [ID285Idx] = 0x285u,

    [ID286Idx] = 0x286u,
    [ID287Idx] = 0x287u,
    [ID2A4Idx] = 0x2A4u,
    [ID2A5Idx] = 0x2A5u,

    [ID2B4Idx] = 0x2B4u,
    [ID2B5Idx] = 0x2B5u,
    [ID394Idx] = 0x394u,

    [ID387Idx] = 0x387u,
    [ID665Idx] = 0x665u,
    [ID685Idx] = 0x685u,
    //debug id
    [ID6F2Idx] = 0x6F2u,
    [ID6F3Idx] = 0x6F3u,
    [ID6F4Idx] = 0x6F4u,
    [ID6F5Idx] = 0x6F5u,
    [ID6F6Idx] = 0x6F6u,
    [ID6F7Idx] = 0x6F7u,
    [ID6F8Idx] = 0x6F8u,
    [ID6F9Idx] = 0x6F9u,
    [ID6FAIdx] = 0x6FAu,
    [ID6FBIdx] = 0x6FBu,
    [ID6FCIdx] = 0x6FCu,
    [ID6FDIdx] = 0x6FDu,
    [ID6FEIdx] = 0x6FEu,
    [ID6FFIdx] = 0x6FFu,

    [ID6EEIdx] = 0x6EEu,
    [ID6EFIdx] = 0x6EFu,
    [ID6F1Idx] = 0x6F1u,
};
/*各邮箱的屏蔽器掩码，如不够用需要复用*/
const uint32 GcComRegisterID[RXANDTXFRAMEMAXNUM] =
{
    /* RX */
    [ID7D2Idx] = 0x7FFul,
    [ID7DFIdx] = 0x7FFul,

    [ID236Idx] = 0x7FFul,
    [ID237Idx] = 0x7FFul,
    [ID395Idx] = 0x7FFul,
    [ID384Idx] = 0x7FFul,
    [ID2D4Idx] = 0x7FFul,

    [ID5A4Idx] = 0x7FFul,
    [ID504Idx] = 0x7FFul,
    [ID176Idx] = 0x7FFul,
    [ID086Idx] = 0x7FFul,
    [ID096Idx] = 0x7FFul,

    [ID0D6Idx] = 0x7FFul,
    [ID209Idx] = 0x7FFul,
    [ID39DIdx] = 0x7FFul,
    [ID09EIdx] = 0x7FFul,
    [ID397Idx] = 0x7FFul,

    [ID0C2Idx] = 0x7FFul,
    [ID388Idx] = 0x7FFul,

    [ID6F0Idx] = 0x7FFu,
    /* TX */
    [ID7DAIdx] = 0x7FFul,
    [ID085Idx] = 0x7FFul,
    [ID238Idx] = 0x7FFul,

    [ID244Idx] = 0x7FFul,
    [ID245Idx] = 0x7FFul,
    [ID254Idx] = 0x7FFul,

    [ID2E4Idx] = 0x7FFul,
    [ID2F4Idx] = 0x7FFul,

    [ID2D5Idx] = 0x7FFul,
    [ID284Idx] = 0x7FFul,
    [ID285Idx] = 0x7FFul,

    [ID286Idx] = 0x7FFul,
    [ID287Idx] = 0x7FFul,
    [ID2A4Idx] = 0x7FFul,
    [ID2A5Idx] = 0x7FFul,

    [ID2B4Idx] = 0x7FFul,
    [ID2B5Idx] = 0x7FFul,
    [ID394Idx] = 0x7FFul,

    [ID387Idx] = 0x7FFul,
    [ID665Idx] = 0x7FFul,
    [ID685Idx] = 0x7FFul,
    //debug id
    [ID6F2Idx] = 0x7FFul,
    [ID6F3Idx] = 0x7FFul,
    [ID6F4Idx] = 0x7FFul,
    [ID6F5Idx] = 0x7FFul,
    [ID6F6Idx] = 0x7FFul,
    [ID6F7Idx] = 0x7FFul,
    [ID6F8Idx] = 0x7FFul,
    [ID6F9Idx] = 0x7FFul,
    [ID6FAIdx] = 0x7FFul,
    [ID6FBIdx] = 0x7FFul,
    [ID6FCIdx] = 0x7FFul,
    [ID6FDIdx] = 0x7FFul,
    [ID6FEIdx] = 0x7FFul,
    [ID6FFIdx] = 0x7FFul,
    [ID6EEIdx] = 0x7FFu,
    [ID6EFIdx] = 0x7FFu,
    [ID6F1Idx] = 0x7FFu,
};

/*定义报文DLC的长度 ,接收报文DLC暂时未应用*/
uint8 GcComFrameDLC[RXANDTXFRAMEMAXNUM] =
{
    /* RX */
    [ID7D2Idx] = 15,
    [ID7DFIdx] = 8,

    [ID236Idx] = 8,
    [ID237Idx] = 8,
    [ID395Idx] = 8,
    [ID384Idx] = 8,
    [ID2D4Idx] = 8,

    [ID5A4Idx] = 8,
    [ID504Idx] = 8,
    [ID176Idx] = 13,
    [ID086Idx] = 13,
    [ID096Idx] = 8,

    [ID0D6Idx] = 13,
    [ID209Idx] = 8,
    [ID39DIdx] = 8,
    [ID09EIdx] = 8,
    [ID397Idx] = 8,

    [ID0C2Idx] = 8,
    [ID388Idx] = 15,
    [ID6F0Idx] = 8,
    /* TX */
    [ID7DAIdx] = 15,
    [ID085Idx] = 13,
    [ID238Idx] = 13,

    [ID244Idx] = 13,

    [ID245Idx] = 13,
    [ID254Idx] = 13,

    [ID2E4Idx] = 13,
    [ID2F4Idx] = 13,

    [ID2D5Idx] = 15,
    [ID284Idx] = 13,
    [ID285Idx] = 13,

    [ID286Idx] = 13,
    [ID287Idx] = 13,
    [ID2A4Idx] = 13,
    [ID2A5Idx] = 13,

    [ID2B4Idx] = 13,
    [ID2B5Idx] = 13,
    [ID394Idx] = 13,
    [ID387Idx] = 13,

    [ID665Idx] = 8,
    [ID685Idx] = 8,


    [ID6F2Idx] = 9,
    [ID6F3Idx] = 10,
    [ID6F4Idx] = 15,
    [ID6F5Idx] = 15,
    [ID6F6Idx] = 15,
    [ID6F7Idx] = 15,
    [ID6F8Idx] = 15,
    [ID6F9Idx] = 15,
    [ID6FAIdx] = 15,
    [ID6FBIdx] = 15,
    [ID6FCIdx] = 15,
    [ID6FDIdx] = 15,
    [ID6FEIdx] = 15,
    [ID6FFIdx] = 15,
    [ID6EEIdx] = 12,
    [ID6EFIdx] = 15,
    [ID6F1Idx] = 8,
};

/* 定义报文的类型  */
const volatile uint8 GcComFrameType[RXANDTXFRAMEMAXNUM] =
{
    /* RX */
    [ID7D2Idx] = DIAG_MSG_RX,
    [ID7DFIdx] = DIAG_MSG_RX,

    [ID236Idx] = APP_MSG_RX,
    [ID237Idx] = APP_MSG_RX,
    [ID395Idx] = APP_MSG_RX,
    [ID384Idx] = APP_MSG_RX,
    [ID2D4Idx] = APP_MSG_RX,

    [ID5A4Idx] = APP_MSG_RX,
    [ID504Idx] = APP_MSG_RX,
    [ID176Idx] = APP_MSG_RX,
    [ID086Idx] = APP_MSG_RX,
    [ID096Idx] = APP_MSG_RX,

    [ID0D6Idx] = APP_MSG_RX,
    [ID209Idx] = APP_MSG_RX,
    [ID39DIdx] = APP_MSG_RX,
    [ID09EIdx] = APP_MSG_RX,
    [ID397Idx] = APP_MSG_RX,

    [ID0C2Idx] = APP_MSG_RX,
    [ID388Idx] = APP_MSG_RX,
    [ID6F0Idx] = APP_MSG_RX,
    /* TX */
    [ID7DAIdx] = DIAG_MSG_TX,
    [ID085Idx] = APP_MSG_TX,
    [ID238Idx] = APP_MSG_TX,
    [ID244Idx] = APP_MSG_TX,

    [ID245Idx] = APP_MSG_TX,

    [ID254Idx] = APP_MSG_TX,
    [ID2E4Idx] = APP_MSG_TX,
    [ID2F4Idx] = APP_MSG_TX,

    [ID2D5Idx] = APP_MSG_TX,
    [ID284Idx] = APP_MSG_TX,
    [ID285Idx] = APP_MSG_TX,

    [ID286Idx] = APP_MSG_TX,
    [ID287Idx] = APP_MSG_TX,
    [ID2A4Idx] = APP_MSG_TX,
    [ID2A5Idx] = APP_MSG_TX,

    [ID2B4Idx] = APP_MSG_TX,
    [ID2B5Idx] = APP_MSG_TX,
    [ID394Idx] = APP_MSG_TX,

    [ID387Idx] = APP_MSG_TX,
    [ID665Idx] = APP_MSG_TX,
    [ID685Idx] = APP_MSG_TX,

    [ID6F2Idx] = APP_MSG_TX,
    [ID6F3Idx] = APP_MSG_TX,
    [ID6F4Idx] = APP_MSG_TX,
    [ID6F5Idx] = APP_MSG_TX,
    [ID6F6Idx] = APP_MSG_TX,
    [ID6F7Idx] = APP_MSG_TX,
    [ID6F8Idx] = APP_MSG_TX,
    [ID6F9Idx] = APP_MSG_TX,
    [ID6FAIdx] = APP_MSG_TX,
    [ID6FBIdx] = APP_MSG_TX,
    [ID6FCIdx] = APP_MSG_TX,
    [ID6FDIdx] = APP_MSG_TX,
    [ID6FEIdx] = APP_MSG_TX,
    [ID6FFIdx] = APP_MSG_TX,

    [ID6EEIdx] = APP_MSG_TX,
    [ID6EFIdx] = APP_MSG_TX,
    [ID6F1Idx] = APP_MSG_TX,
};

/* 定义报文的周期 ,对于非周期应用型报文设置为0  */
const volatile uint16 GwComFramePeriod[RXANDTXFRAMEMAXNUM] =
{
    /* RX */
    [ID7D2Idx] = (0 / COM_MANAGE_PERIOD),
    [ID7DFIdx] = (0 / COM_MANAGE_PERIOD),

    [ID236Idx] = (20 / COM_MANAGE_PERIOD),
    [ID237Idx] = (20 / COM_MANAGE_PERIOD),
    [ID395Idx] = (100 / COM_MANAGE_PERIOD),
    [ID384Idx] = (100 / COM_MANAGE_PERIOD),
    [ID2D4Idx] = (20 / COM_MANAGE_PERIOD),

    [ID5A4Idx] = (500 / COM_MANAGE_PERIOD),
    [ID504Idx] = (100 / COM_MANAGE_PERIOD),
    [ID176Idx] = (10 / COM_MANAGE_PERIOD),
    [ID086Idx] = (10 / COM_MANAGE_PERIOD),
    [ID096Idx] = (10 / COM_MANAGE_PERIOD),

    [ID0D6Idx] = (10 / COM_MANAGE_PERIOD),
    [ID209Idx] = (20 / COM_MANAGE_PERIOD),
    [ID39DIdx] = (100 / COM_MANAGE_PERIOD),
    [ID09EIdx] = (10 / COM_MANAGE_PERIOD),
    [ID397Idx] = (100 / COM_MANAGE_PERIOD),

    [ID0C2Idx] = (10 / COM_MANAGE_PERIOD),
    [ID388Idx] = (100 / COM_MANAGE_PERIOD),
    [ID6F0Idx] = (0U / COM_MANAGE_PERIOD),
    /* TX */
    [ID7DAIdx] = (0U / COM_MANAGE_PERIOD),
    [ID085Idx] = (10U / COM_MANAGE_PERIOD),

    [ID238Idx] = (20U / COM_MANAGE_PERIOD),
    [ID244Idx] = (20U / COM_MANAGE_PERIOD),

    [ID245Idx] = (20U / COM_MANAGE_PERIOD),

    [ID254Idx] = (20U / COM_MANAGE_PERIOD),
    [ID2E4Idx] = (20U / COM_MANAGE_PERIOD),
    [ID2F4Idx] = (20U / COM_MANAGE_PERIOD),

    [ID2D5Idx] = (20U / COM_MANAGE_PERIOD),
    [ID284Idx] = (20U / COM_MANAGE_PERIOD),
    [ID285Idx] = (20U / COM_MANAGE_PERIOD),

    [ID286Idx] = (20U / COM_MANAGE_PERIOD),
    [ID287Idx] = (20U / COM_MANAGE_PERIOD),
    [ID2A4Idx] = (20U / COM_MANAGE_PERIOD),
    [ID2A5Idx] = (20U / COM_MANAGE_PERIOD),

    [ID2B4Idx] = (20U / COM_MANAGE_PERIOD),
    [ID2B5Idx] = (20U / COM_MANAGE_PERIOD),
    [ID394Idx] = (100U / COM_MANAGE_PERIOD),

    [ID387Idx] = (100U / COM_MANAGE_PERIOD),
    [ID665Idx] = (1000U / COM_MANAGE_PERIOD),
    [ID685Idx] = (5000U / COM_MANAGE_PERIOD),

    [ID6F2Idx] = (0U / COM_MANAGE_PERIOD),
    [ID6F3Idx] = (0U / COM_MANAGE_PERIOD),
    [ID6F4Idx] = (0U / COM_MANAGE_PERIOD),
    [ID6F5Idx] = (0U / COM_MANAGE_PERIOD),
    [ID6F6Idx] = (0U / COM_MANAGE_PERIOD),
    [ID6F7Idx] = (0U / COM_MANAGE_PERIOD),
    [ID6F8Idx] = (0U / COM_MANAGE_PERIOD),
    [ID6F9Idx] = (0U / COM_MANAGE_PERIOD),
    [ID6FAIdx] = (0U / COM_MANAGE_PERIOD),
    [ID6FBIdx] = (0U / COM_MANAGE_PERIOD),
    [ID6FCIdx] = (0U / COM_MANAGE_PERIOD),
    [ID6FDIdx] = (0U / COM_MANAGE_PERIOD),
    [ID6FEIdx] = (0U / COM_MANAGE_PERIOD),
    [ID6FFIdx] = (0U / COM_MANAGE_PERIOD),

    [ID6EEIdx] = (0U / COM_MANAGE_PERIOD),
    [ID6EFIdx] = (0U / COM_MANAGE_PERIOD),
    [ID6F1Idx] = (0U / COM_MANAGE_PERIOD),
};

/*超时周期10倍，最大5s*/
const volatile uint16 GwComFrameTimeout[RXANDTXFRAMEMAXNUM] =
{
    /* RX */
    [ID7D2Idx] = (0 / COM_MANAGE_PERIOD),
    [ID7DFIdx] = (0 / COM_MANAGE_PERIOD),

    [ID236Idx] = (200 / COM_MANAGE_PERIOD),
    [ID237Idx] = (200 / COM_MANAGE_PERIOD),
    [ID395Idx] = (1000 / COM_MANAGE_PERIOD),
    [ID384Idx] = (1000 / COM_MANAGE_PERIOD),
    [ID2D4Idx] = (200 / COM_MANAGE_PERIOD),

    [ID5A4Idx] = (3000 / COM_MANAGE_PERIOD),
    [ID504Idx] = (1000 / COM_MANAGE_PERIOD),
    [ID176Idx] = (100 / COM_MANAGE_PERIOD),
    [ID086Idx] = (100 / COM_MANAGE_PERIOD),
    [ID096Idx] = (100 / COM_MANAGE_PERIOD),

    [ID0D6Idx] = (100 / COM_MANAGE_PERIOD),
    [ID209Idx] = (200 / COM_MANAGE_PERIOD),
    [ID39DIdx] = (1000 / COM_MANAGE_PERIOD),
    [ID09EIdx] = (100 / COM_MANAGE_PERIOD),
    [ID397Idx] = (1000 / COM_MANAGE_PERIOD),

    [ID0C2Idx] = (100 / COM_MANAGE_PERIOD),
    [ID388Idx] = (1000 / COM_MANAGE_PERIOD),
    [ID6F0Idx] = (0U / COM_MANAGE_PERIOD),
    /* TX */
    [ID7DAIdx] = (0U / COM_MANAGE_PERIOD),
    [ID085Idx] = (10U / COM_MANAGE_PERIOD),
    [ID238Idx] = (20U / COM_MANAGE_PERIOD),
    [ID244Idx] = (20U / COM_MANAGE_PERIOD),

    [ID245Idx] = (20U / COM_MANAGE_PERIOD),

    [ID254Idx] = (20U / COM_MANAGE_PERIOD),
    [ID2E4Idx] = (20U / COM_MANAGE_PERIOD),
    [ID2F4Idx] = (20U / COM_MANAGE_PERIOD),

    [ID2D5Idx] = (20U / COM_MANAGE_PERIOD),
    [ID284Idx] = (20U / COM_MANAGE_PERIOD),
    [ID285Idx] = (20U / COM_MANAGE_PERIOD),

    [ID286Idx] = (20U / COM_MANAGE_PERIOD),
    [ID287Idx] = (20U / COM_MANAGE_PERIOD),
    [ID2A4Idx] = (20U / COM_MANAGE_PERIOD),
    [ID2A5Idx] = (20U / COM_MANAGE_PERIOD),

    [ID2B4Idx] = (20U / COM_MANAGE_PERIOD),
    [ID2B5Idx] = (20U / COM_MANAGE_PERIOD),
    [ID394Idx] = (100U / COM_MANAGE_PERIOD),

    [ID387Idx] = (100U / COM_MANAGE_PERIOD),
    [ID665Idx] = (1000U / COM_MANAGE_PERIOD),
    [ID685Idx] = (5000U / COM_MANAGE_PERIOD),

    [ID6F2Idx] = (0U / COM_MANAGE_PERIOD),
    [ID6F3Idx] = (0U / COM_MANAGE_PERIOD),
    [ID6F4Idx] = (0U / COM_MANAGE_PERIOD),
    [ID6F5Idx] = (0U / COM_MANAGE_PERIOD),
    [ID6F6Idx] = (0U / COM_MANAGE_PERIOD),
    [ID6F7Idx] = (0U / COM_MANAGE_PERIOD),
    [ID6F8Idx] = (0U / COM_MANAGE_PERIOD),
    [ID6F9Idx] = (0U / COM_MANAGE_PERIOD),
    [ID6FAIdx] = (0U / COM_MANAGE_PERIOD),
    [ID6FBIdx] = (0U / COM_MANAGE_PERIOD),
    [ID6FCIdx] = (0U / COM_MANAGE_PERIOD),
    [ID6FDIdx] = (0U / COM_MANAGE_PERIOD),
    [ID6FEIdx] = (0U / COM_MANAGE_PERIOD),
    [ID6FFIdx] = (0U / COM_MANAGE_PERIOD),

    [ID6EEIdx] = (0U / COM_MANAGE_PERIOD),
    [ID6EFIdx] = (0U / COM_MANAGE_PERIOD),
    [ID6F1Idx] = (0U / COM_MANAGE_PERIOD),
};

/**< 收发报文的帧类型配置数组 */
const volatile CAN_FrameType GcComFrameTypeCfg[RXANDTXFRAMEMAXNUM] =
{
    [ID7D2Idx] = STANDARD_ID,
    [ID7DFIdx] = STANDARD_ID,

    [ID236Idx] = STANDARD_ID,
    [ID237Idx] = STANDARD_ID,
    [ID395Idx] = STANDARD_ID,
    [ID384Idx] = STANDARD_ID,
    [ID2D4Idx] = STANDARD_ID,

    [ID5A4Idx] = STANDARD_ID,
    [ID504Idx] = STANDARD_ID,
    [ID176Idx] = STANDARD_ID,
    [ID086Idx] = STANDARD_ID,
    [ID096Idx] = STANDARD_ID,

    [ID0D6Idx] = STANDARD_ID,
    [ID209Idx] = STANDARD_ID,
    [ID39DIdx] = STANDARD_ID,
    [ID09EIdx] = STANDARD_ID,
    [ID397Idx] = STANDARD_ID,

    [ID0C2Idx] = STANDARD_ID,
    [ID388Idx] = STANDARD_ID,
    [ID6F0Idx] = STANDARD_ID,
    /* TX */
    [ID7DAIdx] = STANDARD_ID,
    [ID085Idx] = STANDARD_ID,

    [ID238Idx] = STANDARD_ID,
    [ID244Idx] = STANDARD_ID,
    [ID245Idx] = STANDARD_ID,
    [ID254Idx] = STANDARD_ID,
    [ID2E4Idx] = STANDARD_ID,

    [ID2F4Idx] = STANDARD_ID,
    [ID2D5Idx] = STANDARD_ID,
    [ID284Idx] = STANDARD_ID,
    [ID285Idx] = STANDARD_ID,

    [ID286Idx] = STANDARD_ID,
    [ID287Idx] = STANDARD_ID,
    [ID2A4Idx] = STANDARD_ID,
    [ID2A5Idx] = STANDARD_ID,

    [ID2B4Idx] = STANDARD_ID,
    [ID2B5Idx] = STANDARD_ID,
    [ID394Idx] = STANDARD_ID,

    [ID387Idx] = STANDARD_ID,
    [ID665Idx] = STANDARD_ID,
    [ID685Idx] = STANDARD_ID,

    [ID6F2Idx] = STANDARD_ID,
    [ID6F3Idx] = STANDARD_ID,
    [ID6F4Idx] = STANDARD_ID,
    [ID6F5Idx] = STANDARD_ID,
    [ID6F6Idx] = STANDARD_ID,
    [ID6F7Idx] = STANDARD_ID,
    [ID6F8Idx] = STANDARD_ID,
    [ID6F9Idx] = STANDARD_ID,
    [ID6FAIdx] = STANDARD_ID,
    [ID6FBIdx] = STANDARD_ID,
    [ID6FCIdx] = STANDARD_ID,
    [ID6FDIdx] = STANDARD_ID,
    [ID6FEIdx] = STANDARD_ID,
    [ID6FFIdx] = STANDARD_ID,

    [ID6EEIdx] = STANDARD_ID,
    [ID6EFIdx] = STANDARD_ID,
    [ID6F1Idx] = STANDARD_ID,
};
#endif