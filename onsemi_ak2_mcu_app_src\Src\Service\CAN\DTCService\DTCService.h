
 #ifndef   __DTCSERVICE_H__
 #define   __DTCSERVICE_H__
 
/******************************************************************************* 
* Includes 
********************************************************************************/ 
#include "Types.h"
#include "LhRule.h"

/****************************************************************************** 
* Constants and macros 
*******************************************************************************/

/****************************************************************************** 
* External objects 
********************************************************************************/ 

/******************************************************************************* 
* Global Functions 
********************************************************************************/ 
uint8 DTCNumberJudge(uint32 LcDTCNumber);
uint16 ReportNumberOfDTCByStatusMask(uint8 StatusMask);
void ReportDTCByStatusMask(uint8 StatusMask,uint16 *LwLen,uint8 *Buffer);
void ReportDTCSnapshotRecordByDTCNumber(uint8 LcRecordNum,uint32 LwDTCNumber,uint16 *LwLen,uint8 *LcData);
void ReportDTCExtendedDataByDTCNumber(uint8 LcExtrecordNum,uint32 LwDTCNumber,uint16 *LwLen,uint8 *LcData);
void ReportSupportedDTC(uint16 *LwLen,uint8 *Buffer);

#endif
