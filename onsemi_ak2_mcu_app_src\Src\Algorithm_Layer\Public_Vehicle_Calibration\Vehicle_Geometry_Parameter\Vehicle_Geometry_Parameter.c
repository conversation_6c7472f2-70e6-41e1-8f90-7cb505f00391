/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * APA_CalibPara: 
 * Created on: 2022-11-25 17:01
 * Original designer: 22866
 ******************************************************************************/
#include "Vehicle_Geometry_Parameter.h"
#include "CAN_AppSignalManage.h"

/******************************************************************************
* 车型配置 : LIXIANG X02
*******************************************************************************/

/* 虚拟区域的起始、终止点位置，需要根据客户需求进行调整--X02 */
const Vehicl_Geometry_ParameterType Vehicl_Geometry_CarPara_X02 = 
{
    .u16CarWidth = 1998,
    .u16CarHalfWidth = 999,
    .u16CarLength = 5060,
    .u16FrontWheelBackWheelDis = 3000,
    .u16BackWheelCarTailDis = 1110,
    .u16FrontWheelCarHeadDis = 950,
    .u16BackWheelCarHeadDis = 3950, 
};

/* 虚拟区域的起始、终止点位置，需要根据客户需求进行调整--X03 */
const Vehicl_Geometry_ParameterType Vehicl_Geometry_CarPara_X03 = 
{
    .u16CarWidth = 1995,
    .u16CarHalfWidth = 998,
    .u16CarLength = 5000,
    .u16FrontWheelBackWheelDis = 3000,
    .u16BackWheelCarTailDis = 1055,
    .u16FrontWheelCarHeadDis = 945,
    .u16BackWheelCarHeadDis = 3945, 
};

/* 虚拟区域的起始、终止点位置，需要根据客户需求进行调整--X04 */
const Vehicl_Geometry_ParameterType Vehicl_Geometry_CarPara_X04 = 
{
    .u16CarWidth = 1960,
    .u16CarHalfWidth = 980,
    .u16CarLength = 4900,
    .u16FrontWheelBackWheelDis = 2900,
    .u16BackWheelCarTailDis = 1040,
    .u16FrontWheelCarHeadDis = 960,
    .u16BackWheelCarHeadDis = 3860, 
};

/* 虚拟区域的起始、终止点位置，需要根据客户需求进行调整--W02 */
const Vehicl_Geometry_ParameterType Vehicl_Geometry_CarPara_W02 = 
{
    .u16CarWidth = 1945,
    .u16CarHalfWidth = 973,
    .u16CarLength = 5080,
    .u16FrontWheelBackWheelDis = 3045,
    .u16BackWheelCarTailDis = 1149,
    .u16FrontWheelCarHeadDis = 886,
    .u16BackWheelCarHeadDis = 3931, 
};


/******************************Vehicl标定参数_存储在Ram中--*********************************************/
Vehicl_Geometry_ParameterType Vehicl_Geometry_Para_Ram;

void APP_Car_Geometry_Config_Init(void)
{
    VehicleTypeType LpenuVEH_Type;
    
    ReadCAN_AppSignal_VEH_Type(&LpenuVEH_Type);
    if(LpenuVEH_Type == VEHTYPE_X02)
    {
        memcpy(&Vehicl_Geometry_Para_Ram,&Vehicl_Geometry_CarPara_X02,sizeof(Vehicl_Geometry_CarPara_X02));
    }
    else if(LpenuVEH_Type == VEHTYPE_X03)
    {
        memcpy(&Vehicl_Geometry_Para_Ram,&Vehicl_Geometry_CarPara_X03,sizeof(Vehicl_Geometry_CarPara_X03));
    }
    else if(LpenuVEH_Type == VEHTYPE_X04)
    {
        memcpy(&Vehicl_Geometry_Para_Ram,&Vehicl_Geometry_CarPara_X04,sizeof(Vehicl_Geometry_CarPara_X04));
    }
    else if(LpenuVEH_Type == VEHTYPE_W02)
    {
        memcpy(&Vehicl_Geometry_Para_Ram,&Vehicl_Geometry_CarPara_W02,sizeof(Vehicl_Geometry_CarPara_W02));
    }
    else
    {
        /* 车型读取错误时，默认使用X02的车型 */
        memcpy(&Vehicl_Geometry_Para_Ram,&Vehicl_Geometry_CarPara_X02,sizeof(Vehicl_Geometry_CarPara_X02));
    }
}


