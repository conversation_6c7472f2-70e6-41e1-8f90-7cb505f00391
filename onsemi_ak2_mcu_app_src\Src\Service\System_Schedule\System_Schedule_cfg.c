/******************************************************************************
 * @file       System_Schedule_cfg.c
 * @brief
 * @date       2025-03-04 13:35:35
 * <AUTHOR>
 * @copyright  Longhorn Automotive Electronics Technology (Xiamen) Co., Ltd.
 *****************************************************************************/


/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "System_Schedule_cfg.h"
#include "ApaCalCarCoor.h"
#include "CANStack.h"
#include "CAN_UDS.h"
#include "DTCMonitor.h"
#include "Elmos_524_17_SnsCtrl.h"
#include "PAS_MAP_StateHandle.h"
#include "PSL_Algorithm.h"
#include "Power_Manage.h"
#include "SDW_Int.h"
#include "SbcCtrl.h"
#include "SnsTask_Int.h"
#include "TP_Manage.h"
#include "UartHal.h"
#include "SpiCom_Prg.h"
#include "SpiCmd.h"
#include "DMA_COM.h"
#include "RdumRdusDrv.h"



/******************************************************************************
 * @Include Files
 *****************************************************************************/



/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/



/******************************************************************************
 * @Function Declaration
 *****************************************************************************/



/******************************************************************************
 * @Const Definitions
 *****************************************************************************/
const SCHED_tstrTaskControlBlockConf GastrPeriodSchedConfig[SCHED_TASK_ID_NUM] =
    {
        [SCHED_TASK1_ID] = {&Elmos17SnsCtrl_1msTASK, 100u, 1u},

        [SCHED_TASK2_ID] = {&AdcHal_MainFunc, 0u, 5u},

        [SCHED_TASK3_ID] = {&CANStackManage, 0u, 1u},

        [SCHED_TASK4_ID] = {&SnsTask_1ms_StepRun, 200u, 1u},

        [SCHED_TASK5_ID] = {&DTCStatusManage_Task, 0u, 10u},

        [SCHED_TASK6_ID] = {&CAN_Task50ms, 20u, 50u},

        [SCHED_TASK7_ID] = {&PowerManage_Task, 20u, 5u},

        [SCHED_TASK8_ID] = {&Uart_Mainfunction_5ms, 0u, 5u},

        [SCHED_TASK9_ID] = {&PAS_MAP_StateUpdateAndToCan_Task, 20u, 5u},

        [SCHED_TASK10_ID] = {&CPOS_Task, 8u, 10u},

        [SCHED_TASK11_ID] = {&SDWAlgorithm_Task, 200u, 20u},

        [SCHED_TASK12_ID] = {&PSLAlgorithm_Task, 500u, 10u},

        [SCHED_TASK13_ID] = {&SbcCtrl_Task, 100, 50u},

        [SCHED_TASK14_ID] = {&RdumRdusDrv_1msTASK, 100u, 1u}
};

/******************************************************************************
 * @Variable Definitions
 *****************************************************************************/



/******************************************************************************
 * @Function Definitions
 *****************************************************************************/


/******************************************************************************
 * @brief
 * <AUTHOR>
 * @date       2025-03-04 13:37:38
 * @note
 *****************************************************************************/
void NoperiodFunc(void)
{
    TP_Manage();
    Gu16RandSeedCnt++;
    Elmos17SnsCtrl_NoPeriod_TASK();
    RdumRdusDrv_NoPeriod_TASK();
}
