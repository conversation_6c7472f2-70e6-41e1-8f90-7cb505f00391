/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * APASignalManage.c: 
 * Created on: 2022-12-15 14:05
 * Original designer: 22866
 ******************************************************************************/

/* ============================================================================================== */
/* Header files																				      */
/* ============================================================================================== */
#include "PSL_Output_Manage.h"
#include "PSL_State_Manage.h"
#include "PublicCalAlgorithm_Int.h"
#include "PSL_AppSignalManage.h"
#include "PSL_Algorithm.h"
#include "CAN_AppSignalManage.h"
#include "TimerManage.h"







/***************************************************************************/
/* local data definitions                                                  */
/***************************************************************************/

PSLSlotOutputStructType GsPSLOutputSlotData;




/*******************************************************************
***********************Private Function****************************
*********************************************************************/


/* ============================================================================================== */
/* Functions																													    */
/* ============================================================================================== */
/* ---------------------------------------------------------------------------------------------- */

/******************************************************************
* 函数名称:PSLOutputRemoveOneSlot
*
* 功能描述:
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 
*
* 修改日期      版本号      修改人      修改内容

*********************************************************************/
void PSLOutputRemoveOneSlot(uint8 LcNumber)
{
    uint8 LcTemp = 0;

     if(((GsPSLOutputSlotData.wSlotCnt >= 1) 
        && (GsPSLOutputSlotData.wSlotCnt < OUTPUT_SLOT_MAX_NUM)         
        && (LcNumber < GsPSLOutputSlotData.wSlotCnt))
        || ((GsPSLOutputSlotData.wSlotCnt == OUTPUT_SLOT_MAX_NUM) && (LcNumber < GsPSLOutputSlotData.wSlotCnt - 1)))
    {
        if(GsPSLOutputSlotData.wSlotCnt == 1)
        {
            GsPSLOutputSlotData.dFirobjDis[LcNumber] = DEPTH_NULL;
            GsPSLOutputSlotData.dSecobjDis[LcNumber] = DEPTH_NULL;
            GsPSLOutputSlotData.dThrObjDis[LcNumber] = DEPTH_NULL;
            GsPSLOutputSlotData.cObjState[LcNumber] = PSL_Objste_None;
            GsPSLOutputSlotData.cFirstObjType[LcNumber] = PSL_Objtype_None;
            GsPSLOutputSlotData.cSecondObjType[LcNumber] = PSL_Objtype_None;
            GsPSLOutputSlotData.cSlotType[LcNumber] = PSL_SLOT_NONE;
            GsPSLOutputSlotData.cSlotDir[LcNumber] = PSL_Slotdir_None;
            GsPSLOutputSlotData.wSlotWidth[LcNumber] = DIS_NULL;
            GsPSLOutputSlotData.dFirstObjWx[LcNumber] = DIS_NULL;
            GsPSLOutputSlotData.dFirstObjWy[LcNumber] = DIS_NULL;
            GsPSLOutputSlotData.dSecObjWx[LcNumber] = DIS_NULL;
            GsPSLOutputSlotData.dSecObjWy[LcNumber] = DIS_NULL;
            GsPSLOutputSlotData.dThrObjWx[LcNumber] = DIS_NULL;
            GsPSLOutputSlotData.dThrObjWy[LcNumber] = DIS_NULL;
            GsPSLOutputSlotData.dSlotDepthDis[LcNumber] = DEPTH_NULL; 
            GsPSLOutputSlotData.dSlotDepthWx[LcNumber] = DIS_NULL;    
            GsPSLOutputSlotData.dSlotDepthWy[LcNumber] = DIS_NULL;   
            GsPSLOutputSlotData.dObj1Slope[LcNumber]  = APA_360_ANGLE;
            GsPSLOutputSlotData.dObj2Slope[LcNumber] = APA_360_ANGLE;
            PSLCalSlotcoor[LcNumber].dObj1Slope = APA_360_ANGLE;
            PSLCalSlotcoor[LcNumber].dObj2Slope = APA_360_ANGLE;
            GsPSLOutputSlotData.dCurbSlope[LcNumber] = APA_360_ANGLE;
            GsPSLOutputSlotData.dOutputSlope[LcNumber]  = APA_360_ANGLE;
            GsPSLOutputSlotData.Type_T_Slot_flag[LcNumber]  = FALSE;
            GsPSLOutputSlotData.dInSlotObjWx[LcNumber] = DIS_NULL;
            GsPSLOutputSlotData.dInSlotObjWy[LcNumber] = DIS_NULL;
            GsPSLOutputSlotData.fRecordCarSlope[LcNumber] = APA_360_ANGLE;  
            GsPSLOutputSlotData.cSlotdepthRef[LcNumber]  = DEPTH_REF_NONE;
            GsPSLOutputSlotData.cFirObjType[LcNumber] = PSL_OBJ_TYPE_NONE;
            GsPSLOutputSlotData.cSecObjType[LcNumber] = PSL_OBJ_TYPE_NONE;
            GsPSLOutputSlotData.Lu32slotsynctime[LcNumber] = 0;
            GsPSLOutputSlotData.wSlotCnt--;
        }
        else
        {
            for(LcTemp = LcNumber; LcTemp < GsPSLOutputSlotData.wSlotCnt - 1; LcTemp++)
            {
                if(LcTemp + 1 < OUTPUT_SLOT_MAX_NUM)
                {
                    GsPSLOutputSlotData.cSlotID[LcTemp] = GsPSLOutputSlotData.cSlotID[LcTemp + 1];
                    GsPSLOutputSlotData.cObjState[LcTemp] = GsPSLOutputSlotData.cObjState[LcTemp + 1];
                    GsPSLOutputSlotData.cFirstObjType[LcTemp] = GsPSLOutputSlotData.cFirstObjType[LcTemp + 1];
                    GsPSLOutputSlotData.cSecondObjType[LcTemp] = GsPSLOutputSlotData.cSecondObjType[LcTemp + 1];
                    GsPSLOutputSlotData.cSlotType[LcTemp] = GsPSLOutputSlotData.cSlotType[LcTemp + 1];
                    GsPSLOutputSlotData.cSlotDir[LcTemp] = GsPSLOutputSlotData.cSlotDir[LcTemp + 1];
                    GsPSLOutputSlotData.wSlotWidth[LcTemp] = GsPSLOutputSlotData.wSlotWidth[LcTemp + 1];
                    GsPSLOutputSlotData.dFirstObjWx[LcTemp] = GsPSLOutputSlotData.dFirstObjWx[LcTemp + 1];             
                    GsPSLOutputSlotData.dFirstObjWy[LcTemp] = GsPSLOutputSlotData.dFirstObjWy[LcTemp + 1];
                    GsPSLOutputSlotData.dFirobjDis[LcTemp] = GsPSLOutputSlotData.dFirobjDis[LcTemp + 1];
                    GsPSLOutputSlotData.dSecobjDis[LcTemp] = GsPSLOutputSlotData.dSecobjDis[LcTemp + 1];
                    GsPSLOutputSlotData.dSecObjWx[LcTemp]  = GsPSLOutputSlotData.dSecObjWx[LcTemp + 1];
                    GsPSLOutputSlotData.dSecObjWy[LcTemp]  = GsPSLOutputSlotData.dSecObjWy[LcTemp + 1];
                    GsPSLOutputSlotData.dThrObjDis[LcTemp] = GsPSLOutputSlotData.dThrObjDis[LcTemp + 1];
                    GsPSLOutputSlotData.dThrObjWx[LcTemp]  = GsPSLOutputSlotData.dThrObjWx[LcTemp + 1];
                    GsPSLOutputSlotData.dThrObjWy[LcTemp]  = GsPSLOutputSlotData.dThrObjWy[LcTemp + 1];
                    GsPSLOutputSlotData.dSlotDepthDis[LcTemp]  = GsPSLOutputSlotData.dSlotDepthDis[LcTemp + 1]; 
                    GsPSLOutputSlotData.dSlotDepthWx[LcTemp]  = GsPSLOutputSlotData.dSlotDepthWx[LcTemp + 1];    
                    GsPSLOutputSlotData.dSlotDepthWy[LcTemp]  = GsPSLOutputSlotData.dSlotDepthWy[LcTemp + 1];   
                    GsPSLOutputSlotData.dObj1Slope[LcTemp]  = GsPSLOutputSlotData.dObj1Slope[LcTemp + 1];
                    GsPSLOutputSlotData.dObj2Slope[LcTemp] = GsPSLOutputSlotData.dObj2Slope[LcTemp + 1];
                    PSLCalSlotcoor[LcNumber].dObj1Slope = PSLCalSlotcoor[LcNumber + 1].dObj1Slope;
                    PSLCalSlotcoor[LcNumber].dObj2Slope = PSLCalSlotcoor[LcNumber + 1].dObj2Slope;
                    GsPSLOutputSlotData.dCurbSlope[LcTemp] = GsPSLOutputSlotData.dCurbSlope[LcTemp + 1];
                    GsPSLOutputSlotData.dOutputSlope[LcTemp]  = GsPSLOutputSlotData.dOutputSlope[LcTemp + 1];
                    GsPSLOutputSlotData.Type_T_Slot_flag[LcTemp]  = GsPSLOutputSlotData.Type_T_Slot_flag[LcTemp + 1];
                    GsPSLOutputSlotData.dInSlotObjWx[LcTemp]  = GsPSLOutputSlotData.dInSlotObjWx[LcTemp + 1];
                    GsPSLOutputSlotData.dInSlotObjWy[LcTemp]  = GsPSLOutputSlotData.dInSlotObjWy[LcTemp + 1];
                    GsPSLOutputSlotData.fRecordCarSlope[LcTemp]  = GsPSLOutputSlotData.fRecordCarSlope[LcTemp + 1];  
                    GsPSLOutputSlotData.cSlotdepthRef[LcTemp]  = GsPSLOutputSlotData.cSlotdepthRef[LcTemp + 1];
                    GsPSLOutputSlotData.cFirObjType[LcNumber] = GsPSLOutputSlotData.cFirObjType[LcTemp + 1];
                    GsPSLOutputSlotData.cSecObjType[LcNumber] = GsPSLOutputSlotData.cSecObjType[LcTemp + 1];
                    GsPSLOutputSlotData.Lu32slotsynctime[LcTemp]  = GsPSLOutputSlotData.Lu32slotsynctime[LcTemp + 1];
                }
            }
            
            GsPSLOutputSlotData.dFirobjDis[GsPSLOutputSlotData.wSlotCnt - 1] = DEPTH_NULL;
            GsPSLOutputSlotData.dSecobjDis[GsPSLOutputSlotData.wSlotCnt - 1] = DEPTH_NULL;
            GsPSLOutputSlotData.dThrObjDis[GsPSLOutputSlotData.wSlotCnt - 1] = DEPTH_NULL;
            GsPSLOutputSlotData.cObjState[GsPSLOutputSlotData.wSlotCnt - 1] = PSL_Objste_None;
            GsPSLOutputSlotData.cFirstObjType[GsPSLOutputSlotData.wSlotCnt - 1] = PSL_Objtype_None;
            GsPSLOutputSlotData.cSecondObjType[GsPSLOutputSlotData.wSlotCnt - 1] = PSL_Objtype_None;
            GsPSLOutputSlotData.cSlotType[GsPSLOutputSlotData.wSlotCnt - 1] = PSL_SLOT_NONE;
            GsPSLOutputSlotData.cSlotDir[GsPSLOutputSlotData.wSlotCnt - 1] = PSL_Slotdir_None;
            GsPSLOutputSlotData.wSlotWidth[GsPSLOutputSlotData.wSlotCnt - 1] = DIS_NULL;
            GsPSLOutputSlotData.dFirstObjWx[GsPSLOutputSlotData.wSlotCnt - 1] = DIS_NULL;
            GsPSLOutputSlotData.dFirstObjWy[GsPSLOutputSlotData.wSlotCnt - 1] = DIS_NULL;
            GsPSLOutputSlotData.dSecObjWx[GsPSLOutputSlotData.wSlotCnt - 1] = DIS_NULL;
            GsPSLOutputSlotData.dSecObjWy[GsPSLOutputSlotData.wSlotCnt - 1] = DIS_NULL;
            GsPSLOutputSlotData.dThrObjWx[GsPSLOutputSlotData.wSlotCnt - 1] = DIS_NULL;
            GsPSLOutputSlotData.dThrObjWy[GsPSLOutputSlotData.wSlotCnt - 1] = DIS_NULL;
            GsPSLOutputSlotData.dSlotDepthDis[GsPSLOutputSlotData.wSlotCnt - 1] = DEPTH_NULL; 
            GsPSLOutputSlotData.dSlotDepthWx[GsPSLOutputSlotData.wSlotCnt - 1] = DIS_NULL;    
            GsPSLOutputSlotData.dSlotDepthWy[GsPSLOutputSlotData.wSlotCnt - 1] = DIS_NULL;   
            GsPSLOutputSlotData.dObj1Slope[GsPSLOutputSlotData.wSlotCnt - 1]  = APA_360_ANGLE;
            GsPSLOutputSlotData.dObj2Slope[GsPSLOutputSlotData.wSlotCnt - 1] = APA_360_ANGLE;
            PSLCalSlotcoor[GsPSLOutputSlotData.wSlotCnt - 1].dObj1Slope = APA_360_ANGLE;
            PSLCalSlotcoor[GsPSLOutputSlotData.wSlotCnt - 1].dObj2Slope = APA_360_ANGLE;
            GsPSLOutputSlotData.dCurbSlope[GsPSLOutputSlotData.wSlotCnt - 1] = APA_360_ANGLE;
            GsPSLOutputSlotData.dOutputSlope[GsPSLOutputSlotData.wSlotCnt - 1]  = APA_360_ANGLE;
            GsPSLOutputSlotData.Type_T_Slot_flag[GsPSLOutputSlotData.wSlotCnt - 1]  = FALSE;
            GsPSLOutputSlotData.dInSlotObjWx[GsPSLOutputSlotData.wSlotCnt - 1] = DIS_NULL;
            GsPSLOutputSlotData.dInSlotObjWy[GsPSLOutputSlotData.wSlotCnt - 1] = DIS_NULL;
            GsPSLOutputSlotData.fRecordCarSlope[GsPSLOutputSlotData.wSlotCnt - 1] = APA_360_ANGLE;  
            GsPSLOutputSlotData.cSlotdepthRef[GsPSLOutputSlotData.wSlotCnt - 1]  = DEPTH_REF_NONE;
            GsPSLOutputSlotData.cFirObjType[GsPSLOutputSlotData.wSlotCnt - 1] = PSL_OBJ_TYPE_NONE;
            GsPSLOutputSlotData.cSecObjType[GsPSLOutputSlotData.wSlotCnt - 1] = PSL_OBJ_TYPE_NONE;
            GsPSLOutputSlotData.Lu32slotsynctime[GsPSLOutputSlotData.wSlotCnt - 1] = 0;
            GsPSLOutputSlotData.wSlotCnt--;
        }
    }
    else if((GsPSLOutputSlotData.wSlotCnt == OUTPUT_SLOT_MAX_NUM) && (LcNumber == GsPSLOutputSlotData.wSlotCnt - 1))
    {
        GsPSLOutputSlotData.dFirobjDis[LcNumber] = DEPTH_NULL;
        GsPSLOutputSlotData.dSecobjDis[LcNumber] = DEPTH_NULL;
        GsPSLOutputSlotData.dThrObjDis[LcNumber] = DEPTH_NULL;
        GsPSLOutputSlotData.cObjState[LcNumber] = PSL_Objste_None;
        GsPSLOutputSlotData.cFirstObjType[LcNumber] = PSL_Objtype_None;
        GsPSLOutputSlotData.cSecondObjType[LcNumber] = PSL_Objtype_None;
        GsPSLOutputSlotData.cSlotType[LcNumber] = PSL_SLOT_NONE;
        GsPSLOutputSlotData.cSlotDir[LcNumber] = PSL_Slotdir_None;
        GsPSLOutputSlotData.wSlotWidth[LcNumber] = DIS_NULL;
        GsPSLOutputSlotData.dFirstObjWx[LcNumber] = DIS_NULL;
        GsPSLOutputSlotData.dFirstObjWy[LcNumber] = DIS_NULL;
        GsPSLOutputSlotData.dSecObjWx[LcNumber] = DIS_NULL;
        GsPSLOutputSlotData.dSecObjWy[LcNumber] = DIS_NULL;
        GsPSLOutputSlotData.dThrObjWx[LcNumber] = DIS_NULL;
        GsPSLOutputSlotData.dThrObjWy[LcNumber] = DIS_NULL;
        GsPSLOutputSlotData.dSlotDepthDis[LcNumber] = DEPTH_NULL; 
        GsPSLOutputSlotData.dSlotDepthWx[LcNumber] = DIS_NULL;    
        GsPSLOutputSlotData.dSlotDepthWy[LcNumber] = DIS_NULL;   
        GsPSLOutputSlotData.dObj1Slope[LcNumber]  = APA_360_ANGLE;
        GsPSLOutputSlotData.dObj2Slope[LcNumber] = APA_360_ANGLE;
        PSLCalSlotcoor[LcNumber].dObj1Slope = APA_360_ANGLE;
        PSLCalSlotcoor[LcNumber].dObj2Slope = APA_360_ANGLE;
        GsPSLOutputSlotData.dCurbSlope[LcNumber] = APA_360_ANGLE;
        GsPSLOutputSlotData.dOutputSlope[LcNumber]  = APA_360_ANGLE;
        GsPSLOutputSlotData.Type_T_Slot_flag[LcNumber]  = FALSE;
        GsPSLOutputSlotData.dInSlotObjWx[LcNumber] = DIS_NULL;
        GsPSLOutputSlotData.dInSlotObjWy[LcNumber] = DIS_NULL;
        GsPSLOutputSlotData.fRecordCarSlope[LcNumber] = APA_360_ANGLE;  
        GsPSLOutputSlotData.cSlotdepthRef[LcNumber]  = DEPTH_REF_NONE;
        GsPSLOutputSlotData.cFirObjType[LcNumber] = PSL_OBJ_TYPE_NONE;
        GsPSLOutputSlotData.cSecObjType[LcNumber] = PSL_OBJ_TYPE_NONE;
        GsPSLOutputSlotData.Lu32slotsynctime[LcNumber] = 0;
        GsPSLOutputSlotData.wSlotCnt--;
    }
    else
    {
    }     
}

/******************************************************************
* 函数名称:PSLOutputRemoveAllSlot
*
* 功能描述:
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 
*
* 修改日期      版本号      修改人      修改内容

*********************************************************************/
void PSLOutputRemoveAllSlot(void)
{
    uint8 LcTemp;

    for(LcTemp = 0; LcTemp < GsPSLOutputSlotData.wSlotCnt; LcTemp++)
    {
        GsPSLOutputSlotData.dFirobjDis[LcTemp] = DEPTH_NULL;
        GsPSLOutputSlotData.dSecobjDis[LcTemp] = DEPTH_NULL;
        GsPSLOutputSlotData.dThrObjDis[LcTemp] = DEPTH_NULL;
        GsPSLOutputSlotData.cObjState[LcTemp] = PSL_Objste_None;
        GsPSLOutputSlotData.cFirstObjType[LcTemp] = PSL_Objtype_None;
        GsPSLOutputSlotData.cSecondObjType[LcTemp] = PSL_Objtype_None;
        GsPSLOutputSlotData.cSlotType[LcTemp] = PSL_SLOT_NONE;
        GsPSLOutputSlotData.cSlotDir[LcTemp] = PSL_Slotdir_None;
        GsPSLOutputSlotData.wSlotWidth[LcTemp] = DIS_NULL;
        GsPSLOutputSlotData.dFirstObjWx[LcTemp] = DIS_NULL;
        GsPSLOutputSlotData.dFirstObjWy[LcTemp] = DIS_NULL;
        GsPSLOutputSlotData.dSecObjWx[LcTemp] = DIS_NULL;
        GsPSLOutputSlotData.dSecObjWy[LcTemp] = DIS_NULL;
        GsPSLOutputSlotData.dThrObjWx[LcTemp] = DIS_NULL;
        GsPSLOutputSlotData.dThrObjWy[LcTemp] = DIS_NULL;
        GsPSLOutputSlotData.dSlotDepthDis[LcTemp] = DEPTH_NULL; 
        GsPSLOutputSlotData.dSlotDepthWx[LcTemp] = DIS_NULL;    
        GsPSLOutputSlotData.dSlotDepthWy[LcTemp] = DIS_NULL;   
        GsPSLOutputSlotData.dObj1Slope[LcTemp] = APA_360_ANGLE;
        GsPSLOutputSlotData.dObj2Slope[LcTemp] = APA_360_ANGLE;             
        GsPSLOutputSlotData.dCurbSlope[LcTemp] = APA_360_ANGLE;
        GsPSLOutputSlotData.dOutputSlope[LcTemp] = APA_360_ANGLE;
        GsPSLOutputSlotData.Type_T_Slot_flag[LcTemp] = FALSE;
        GsPSLOutputSlotData.dInSlotObjWx[LcTemp] = DIS_NULL;
        GsPSLOutputSlotData.dInSlotObjWy[LcTemp] = DIS_NULL;
        GsPSLOutputSlotData.fRecordCarSlope[LcTemp] = APA_360_ANGLE;  
        GsPSLOutputSlotData.cSlotdepthRef[LcTemp] = DEPTH_REF_NONE;
        GsPSLOutputSlotData.cFirObjType[LcTemp] = PSL_OBJ_TYPE_NONE;
        GsPSLOutputSlotData.cSecObjType[LcTemp] = PSL_OBJ_TYPE_NONE;
        GsPSLOutputSlotData.Lu32slotsynctime[LcTemp] = 0;                     
    }     

    GsPSLOutputSlotData.wSlotCnt = 0;       
}

/******************************************************************
* 函数名称:PSLOutputRetainOneSlotRemoveOther
*
* 功能描述:
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 
*
* 修改日期      版本号      修改人      修改内容

*********************************************************************/
void PSLOutputRetainOneSlotRemoveOther(uint8 LcNumberId)
{
    uint8 LcTemp;
    if(GsPSLOutputSlotData.wSlotCnt > 1
        && GsPSLOutputSlotData.wSlotCnt <= OUTPUT_SLOT_MAX_NUM)
    {
        for(LcTemp = 0;LcTemp < GsPSLOutputSlotData.wSlotCnt;LcTemp++)
        {
            if(GsPSLOutputSlotData.cSlotID[LcTemp] == LcNumberId)
            {
                GsPSLOutputSlotData.cSlotID[0] = GsPSLOutputSlotData.cSlotID[LcTemp];
                GsPSLOutputSlotData.cObjState[0] = GsPSLOutputSlotData.cObjState[LcTemp];
                GsPSLOutputSlotData.cFirstObjType[0] = GsPSLOutputSlotData.cFirstObjType[LcTemp];
                GsPSLOutputSlotData.cSecondObjType[0] = GsPSLOutputSlotData.cSecondObjType[LcTemp];
                GsPSLOutputSlotData.cSlotType[0] = GsPSLOutputSlotData.cSlotType[LcTemp];
                GsPSLOutputSlotData.cSlotDir[0] = GsPSLOutputSlotData.cSlotDir[LcTemp];
                GsPSLOutputSlotData.wSlotWidth[0] = GsPSLOutputSlotData.wSlotWidth[LcTemp];
                GsPSLOutputSlotData.dFirstObjWx[0] = GsPSLOutputSlotData.dFirstObjWx[LcTemp];             
                GsPSLOutputSlotData.dFirstObjWy[0] = GsPSLOutputSlotData.dFirstObjWy[LcTemp];
                GsPSLOutputSlotData.dFirobjDis[0] = GsPSLOutputSlotData.dFirobjDis[LcTemp];
                GsPSLOutputSlotData.dSecobjDis[0] = GsPSLOutputSlotData.dSecobjDis[LcTemp];
                GsPSLOutputSlotData.dSecObjWx[0]  = GsPSLOutputSlotData.dSecObjWx[LcTemp];
                GsPSLOutputSlotData.dSecObjWy[0]  = GsPSLOutputSlotData.dSecObjWy[LcTemp];
                GsPSLOutputSlotData.dThrObjDis[0] = GsPSLOutputSlotData.dThrObjDis[LcTemp];
                GsPSLOutputSlotData.dThrObjWx[0]  = GsPSLOutputSlotData.dThrObjWx[LcTemp];
                GsPSLOutputSlotData.dThrObjWy[0]  = GsPSLOutputSlotData.dThrObjWy[LcTemp];
                GsPSLOutputSlotData.dSlotDepthDis[0]  = GsPSLOutputSlotData.dSlotDepthDis[LcTemp]; 
                GsPSLOutputSlotData.dSlotDepthWx[0]  = GsPSLOutputSlotData.dSlotDepthWx[LcTemp];    
                GsPSLOutputSlotData.dSlotDepthWy[0]  = GsPSLOutputSlotData.dSlotDepthWy[LcTemp];   
                GsPSLOutputSlotData.dObj1Slope[0]  = GsPSLOutputSlotData.dObj1Slope[LcTemp];
                GsPSLOutputSlotData.dObj2Slope[0] = GsPSLOutputSlotData.dObj2Slope[LcTemp];            
                GsPSLOutputSlotData.dCurbSlope[0] = GsPSLOutputSlotData.dCurbSlope[LcTemp];
                GsPSLOutputSlotData.dOutputSlope[0]  = GsPSLOutputSlotData.dOutputSlope[LcTemp];
                GsPSLOutputSlotData.Type_T_Slot_flag[0]  = GsPSLOutputSlotData.Type_T_Slot_flag[LcTemp];
                GsPSLOutputSlotData.dInSlotObjWx[0]  = GsPSLOutputSlotData.dInSlotObjWx[LcTemp];
                GsPSLOutputSlotData.dInSlotObjWy[0]  = GsPSLOutputSlotData.dInSlotObjWy[LcTemp];
                GsPSLOutputSlotData.fRecordCarSlope[0]  = GsPSLOutputSlotData.fRecordCarSlope[LcTemp];  
                GsPSLOutputSlotData.cSlotdepthRef[0]  = GsPSLOutputSlotData.cSlotdepthRef[LcTemp];
                GsPSLOutputSlotData.cFirObjType[0] = GsPSLOutputSlotData.cFirObjType[LcTemp];
                GsPSLOutputSlotData.cSecObjType[0] = GsPSLOutputSlotData.cSecObjType[LcTemp];
                GsPSLOutputSlotData.Lu32slotsynctime[0]  = GsPSLOutputSlotData.Lu32slotsynctime[LcTemp];

                for(LcTemp = 1;LcTemp < GsPSLOutputSlotData.wSlotCnt;LcTemp++)
                {
                    GsPSLOutputSlotData.dFirobjDis[LcTemp] = DEPTH_NULL;
                    GsPSLOutputSlotData.dSecobjDis[LcTemp] = DEPTH_NULL;
                    GsPSLOutputSlotData.dThrObjDis[LcTemp] = DEPTH_NULL;
                    GsPSLOutputSlotData.cObjState[LcTemp] = PSL_Objste_None;
                    GsPSLOutputSlotData.cFirstObjType[LcTemp] = PSL_Objtype_None;
                    GsPSLOutputSlotData.cSecondObjType[LcTemp] = PSL_Objtype_None;
                    GsPSLOutputSlotData.cSlotType[LcTemp] = PSL_SLOT_NONE;
                    GsPSLOutputSlotData.cSlotDir[LcTemp] = PSL_Slotdir_None;
                    GsPSLOutputSlotData.wSlotWidth[LcTemp] = DIS_NULL;
                    GsPSLOutputSlotData.dFirstObjWx[LcTemp] = DIS_NULL;
                    GsPSLOutputSlotData.dFirstObjWy[LcTemp] = DIS_NULL;
                    GsPSLOutputSlotData.dSecObjWx[LcTemp] = DIS_NULL;
                    GsPSLOutputSlotData.dSecObjWy[LcTemp] = DIS_NULL;
                    GsPSLOutputSlotData.dThrObjWx[LcTemp] = DIS_NULL;
                    GsPSLOutputSlotData.dThrObjWy[LcTemp] = DIS_NULL;
                    GsPSLOutputSlotData.dSlotDepthDis[LcTemp] = DEPTH_NULL; 
                    GsPSLOutputSlotData.dSlotDepthWx[LcTemp] = DIS_NULL;    
                    GsPSLOutputSlotData.dSlotDepthWy[LcTemp] = DIS_NULL;   
                    GsPSLOutputSlotData.dObj1Slope[LcTemp] = APA_360_ANGLE;
                    GsPSLOutputSlotData.dObj2Slope[LcTemp] = APA_360_ANGLE;             
                    GsPSLOutputSlotData.dCurbSlope[LcTemp] = APA_360_ANGLE;
                    GsPSLOutputSlotData.dOutputSlope[LcTemp] = APA_360_ANGLE;
                    GsPSLOutputSlotData.Type_T_Slot_flag[LcTemp] = FALSE;
                    GsPSLOutputSlotData.dInSlotObjWx[LcTemp] = DIS_NULL;
                    GsPSLOutputSlotData.dInSlotObjWy[LcTemp] = DIS_NULL;
                    GsPSLOutputSlotData.fRecordCarSlope[LcTemp] = APA_360_ANGLE;  
                    GsPSLOutputSlotData.cSlotdepthRef[LcTemp] = DEPTH_REF_NONE;
                    GsPSLOutputSlotData.cFirObjType[LcTemp] = PSL_OBJ_TYPE_NONE;
                    GsPSLOutputSlotData.cSecObjType[LcTemp] = PSL_OBJ_TYPE_NONE;
                    GsPSLOutputSlotData.Lu32slotsynctime[LcTemp] = 0;
                }
                GsPSLOutputSlotData.wSlotCnt = 1;
            }
        }
    }
}

/******************************************************************
* 函数名称:PSLSlotOutputSlopeCheck
*
* 功能描述:
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 
*
* 修改日期      版本号      修改人      修改内容

*********************************************************************/
void PSLSlotOutputSlopeCheck(void)
{
    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;
    if(GsPSLOutputSlotData.wSlotCnt)
    {
        uint8 Lu8Temp = 0;
        for(Lu8Temp = 0; Lu8Temp < GsPSLOutputSlotData.wSlotCnt; Lu8Temp++)
        {      
            if(ABS(LpStrPSLUseOdo->fCarYawAngle,GsPSLOutputSlotData.fRecordCarSlope[Lu8Temp]) > APA_80_ANGLE)
            {
                PSLOutputRemoveOneSlot(Lu8Temp);
            }
        
        }
    }
}

/******************************************************************
* 函数名称:PSLSlotOutputMoveDisCheck
*
* 功能描述:
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 
*
* 修改日期      版本号      修改人      修改内容

*********************************************************************/
void PSLSlotOutputMoveDisCheck(void)
{
    float LfRWheelToFirstobjDis = 0.0;
    float LfRWheelToSecobjDis = 0.0;
    float LfRWheelToSlotDis = 0.0;
    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;
    if(GsPSLOutputSlotData.wSlotCnt)
    {
        uint8 Lu8Temp = 0;
        for(Lu8Temp = 0; Lu8Temp < GsPSLOutputSlotData.wSlotCnt; Lu8Temp++)
        {   

            LfRWheelToFirstobjDis = PubAI_CalTwoPointDis(LpStrPSLUseOdo->fCarCoorX,LpStrPSLUseOdo->fCarCoorY,\
                GsPSLOutputSlotData.dFirstObjWx[Lu8Temp],GsPSLOutputSlotData.dFirstObjWy[Lu8Temp]);
            LfRWheelToSecobjDis = PubAI_CalTwoPointDis(LpStrPSLUseOdo->fCarCoorX,LpStrPSLUseOdo->fCarCoorY,\
                GsPSLOutputSlotData.dSecObjWx[Lu8Temp],GsPSLOutputSlotData.dSecObjWy[Lu8Temp]);
            LfRWheelToSlotDis = MIN_VALUE(LfRWheelToFirstobjDis,LfRWheelToSecobjDis);
            
            if(GsPSLOutputSlotData.cSlotType[Lu8Temp] == PSL_SLOT_LEFT_HORIZONTAL 
                || GsPSLOutputSlotData.cSlotType[Lu8Temp] == PSL_SLOT_RIGHT_HORIZONTAL)
            {
                if(LfRWheelToSlotDis > DIS_PARALLELSLOT)
                    PSLOutputRemoveOneSlot(Lu8Temp);
            }
            else if(GsPSLOutputSlotData.cSlotType[Lu8Temp] == PSL_SLOT_LEFT_VERTICAL 
                || GsPSLOutputSlotData.cSlotType[Lu8Temp] == PSL_SLOT_RIGHT_VERTICAL)
            {
                if(LfRWheelToSlotDis > DIS_CROSSSLOT)
                    PSLOutputRemoveOneSlot(Lu8Temp);
            }
            else
            {

            }
        
        }
    }
}

/******************************************************************
* 函数名称:PSLGuidanceRemoveOtherSlot
*
* 功能描述:
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 
*
* 修改日期      版本号      修改人      修改内容

*********************************************************************/
void PSLGuidanceRemoveOtherSlot(void)
{
    ApaSignal_PSLWorkStatusType PSLWorkStatus;
    PSLSignal_ReadPSLWorkState(&PSLWorkStatus);
}

/******************************************************************************
 * 函数名称: PSLResetCoorSlopeCheck
 * 
 * 功能描述: PSL大角度转弯重置坐标系
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-6-12 11:25    V0.1       22866        初次发布
 ******************************************************************************/

void PSLResetCoorSlopeCheck(void)
{
    uint8 i;
    float LfSlopeDec = 0.0;
    static float PSLCarCurrentSlopeBck = 0.0;
    static uint8 Lu8SlopeNeedBck;
    static float fCarOdoWxBak = 0.0;
    static float fCarOdoWyBak = 0.0;
    static float GfCarSlopeBck = 0.0;
    float fPointOneWx = 0.0;
    float fPointOneWy = 0.0;
    float GfCalcCarMoveDis = 0.0;
    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;
    
    LfSlopeDec = PSLCalSlopeDec(PSLCarCurrentSlopeBck,LpStrPSLUseOdo->fCarYawAngle);
    if(LfSlopeDec < 0)
    {
        LfSlopeDec = -LfSlopeDec;
    }

    
    if(Lu8SlopeNeedBck)
    {
        PSLCarCurrentSlopeBck = LpStrPSLUseOdo->fCarYawAngle;
        Lu8SlopeNeedBck = FALSE;
    }
    else
    {
        
    }
    //PSL_DEBUG_PRINTF("Car Slope sum:%.1f\r\n",LfSlopeDec * PSL_ANGLE_CONVERT_PARAMETER);
    if((LfSlopeDec > APA_30_ANGLE)
      &&(GsPSLOutputSlotData.wSlotCnt == 0))
    {
        PSL_DEBUG_PRINTF("Car Slope is big:%.1f\r\n",LfSlopeDec * PSL_ANGLE_CONVERT_PARAMETER);
        PSLResetOdoCoor();
        PSLCarCurrentSlopeBck = 0.0;
        GfCarSlopeBck = 0.0;
        Lu8SlopeNeedBck = TRUE;
    }
      

    fPointOneWx = LpStrPSLUseOdo->fCarCoorX;
    fPointOneWy = LpStrPSLUseOdo->fCarCoorY;
    GfCalcCarMoveDis = PubAI_CalTwoPointDis(fPointOneWx,fPointOneWy,fCarOdoWxBak,fCarOdoWyBak);
    if(GfCalcCarMoveDis > 5000)
    {       
        LfSlopeDec = PSLCalSlopeDec(GfCarSlopeBck,LpStrPSLUseOdo->fCarYawAngle);       
        if(LfSlopeDec < 0)
        {
            LfSlopeDec = -LfSlopeDec;
        }
        PSL_DEBUG_PRINTF("5M Car Slope sum:%.1f\r\n",LfSlopeDec * PSL_ANGLE_CONVERT_PARAMETER);
        if(LfSlopeDec < APA_10_ANGLE)
        {
            Lu8SlopeNeedBck = TRUE;
        }
        fCarOdoWxBak = LpStrPSLUseOdo->fCarCoorX;
        fCarOdoWyBak = LpStrPSLUseOdo->fCarCoorY;
        GfCarSlopeBck = LpStrPSLUseOdo->fCarYawAngle;
    }
}

/******************************************************************************
 * 函数名称: PSL_Gear_R_SlotManage
 * 
 * 功能描述: PSL更新档位车位管理及坐标重置
 * 
 * 输入参数:无 
 * 
 * 输出参数:无 
 * 
 * 返回值:无 
 * 
 * 其它说明:无 
 * 
 * 修改日期              版本号        修改人          修改内容  
 * 2023-6-12 11:25    V0.1       22866        初次发布
 ******************************************************************************/
void PSL_Gear_SlotManage(void)
{
    
    double LdRmovedis = 0;
    static double LdCarOdoWxBak = 0;
    static double LdCarOdoWyBak = 0;
    static uint8 Gu8GEAR_RClearALL_flag = 0;
    double LdDistX,LdDistY;
    PSLUseOdoType *LpStrPSLUseOdo;
    LpStrPSLUseOdo = &GstrPSLUseOdoData;
    Car_GearType  LenuParkGearPosition = CAN_CAR_GEAR_P;
    
    ReadCAN_AppSignal_Car_Gear(&LenuParkGearPosition);
    switch(LenuParkGearPosition)
    {
        case    CAN_CAR_GEAR_N:
        case    CAN_CAR_GEAR_P:
        {
            LenuParkGearPosition = CAN_CAR_GEAR_P;
            break;
        }

        case    CAN_CAR_GEAR_D:
        {
            LenuParkGearPosition = CAN_CAR_GEAR_D;
            break;
        }

        default:break;
    }

    if(LenuParkGearPosition == CAN_CAR_GEAR_D)
    {
        LdCarOdoWxBak = LpStrPSLUseOdo->fCarCoorX;
        LdCarOdoWyBak = LpStrPSLUseOdo->fCarCoorY;
        Gu8GEAR_RClearALL_flag = 0;
        PSLResetCoorSlopeCheck();
    }
    
    if(LenuParkGearPosition == CAN_CAR_GEAR_R)
    {
    
        if(!GsPSLOutputSlotData.wSlotCnt)
        {
            //if(Gu8GEAR_RClearALL_flag == 0)
            {
                PSLResetOdoCoor();
                Gu8GEAR_RClearALL_flag = 1;
            }
        }
      
        
        LdDistX = LpStrPSLUseOdo->fCarCoorX - LdCarOdoWxBak;
        LdDistY = LpStrPSLUseOdo->fCarCoorY - LdCarOdoWxBak;           
       
        LdRmovedis = sqrt(LdDistX * LdDistX + LdDistY * LdDistY);
  
        //if((LdRmovedis > GEAR_R_CLEARSLOTDIS) && (GsPSLOutputSlotData.wSlotCnt != 0))
        if(GsPSLOutputSlotData.wSlotCnt != 0)
        {   
            PSL_DEBUG_PRINTF("R Gear Remove all slot\r\n");
            PSLOutputRemoveAllSlot();
        }
    }
}

/******************************************************************
* 函数名称:PSLSlotOutputManage
*
* 功能描述:
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 
*
* 修改日期      版本号      修改人      修改内容

*********************************************************************/

void PSLSlotOutputManage(void)
{
    uint8 u8SlotCnt;
    uint32 Lu32synctime = 0;
    PSLSlotOutputSlopeCheck();
    PSLSlotOutputMoveDisCheck();
    PSL_Gear_SlotManage();
    //PSLGuidanceRemoveOtherSlot();

    
    Lu32synctime = GetCANTimeSysn_GlobalTimeBase();
    for(u8SlotCnt = 0;u8SlotCnt < OUTPUT_SLOT_MAX_NUM;u8SlotCnt++)
    {
        if(GsPSLOutputSlotData.wSlotCnt == 0)
        {
            GstrAPAslotInfo[u8SlotCnt].u32slotsynctime = Lu32synctime;
        }
        else if(u8SlotCnt >= GsPSLOutputSlotData.wSlotCnt)
        {
            GstrAPAslotInfo[u8SlotCnt].u32slotsynctime = Lu32synctime;
        }
    }
}



