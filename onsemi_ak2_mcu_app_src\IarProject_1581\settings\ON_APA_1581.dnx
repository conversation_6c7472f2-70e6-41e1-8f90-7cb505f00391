<?xml version="1.0"?>
<settings>
    <DebugChecksum>
        <Checksum>4234686160</Checksum>
    </DebugChecksum>
    <PlDriver>
        <MemConfigValue>D:\Program Files (x86)\IAR Systems\Embedded Workbench 8.1\rh850\config\debugger\ior7f701581.ddf</MemConfigValue>
        <FirstRun>0</FirstRun>
    </PlDriver>
    <Stack>
        <FillEnabled>0</FillEnabled>
        <OverflowWarningsEnabled>1</OverflowWarningsEnabled>
        <WarningThreshold>90</WarningThreshold>
        <SpWarningsEnabled>1</SpWarningsEnabled>
        <WarnLogOnly>1</WarnLogOnly>
        <UseTrigger>1</UseTrigger>
        <TriggerName>main</TriggerName>
        <LimitSize>0</LimitSize>
        <ByteLimit>50</ByteLimit>
    </Stack>
    <CallStack>
        <ShowArgs>0</ShowArgs>
    </CallStack>
    <Disassembly>
        <MixedMode>1</MixedMode>
    </Disassembly>
    <DataSample>
        <LogEnabled>0</LogEnabled>
        <GraphEnabled>0</GraphEnabled>
        <ShowTimeLog>1</ShowTimeLog>
    </DataSample>
    <InterruptLog>
        <LogEnabled>0</LogEnabled>
        <GraphEnabled>0</GraphEnabled>
        <ShowTimeLog>1</ShowTimeLog>
        <SumEnabled>0</SumEnabled>
        <ShowTimeSum>1</ShowTimeSum>
        <SumSortOrder>0</SumSortOrder>
    </InterruptLog>
    <Breakpoints2>
        <Bp0>_ 0 "STD_CODE2" "{$PROJ_DIR$\..\Src\Service\RdumRdusDrv\SpiCom_Prg.h}.122.1" 0 0 1 "" 0 ""</Bp0>
        <Bp1>_ 1 "STD_CODE2" "{$PROJ_DIR$\..\Src\Service\RdumRdusDrv\RdumRdusDrv.c}.348.5" 0 0 1 "" 0 ""</Bp1>
        <Bp2>_ 1 "STD_CODE2" "{$PROJ_DIR$\..\Src\Service\RdumRdusDrv\SpiCom_Prg.c}.720.5" 0 0 1 "" 0 ""</Bp2>
        <Bp3>_ 1 "STD_CODE2" "{$PROJ_DIR$\..\Src\Service\RdumRdusDrv\RdumRdusDrv.c}.616.9" 0 0 1 "" 0 ""</Bp3>
        <Bp4>_ 1 "STD_CODE2" "{$PROJ_DIR$\..\Src\Service\RdumRdusDrv\RdumRdusDrv.c}.643.9" 0 0 1 "" 0 ""</Bp4>
        <Count>5</Count>
    </Breakpoints2>
    <TermIOLog>
        <LoggingEnabled>_ 0</LoggingEnabled>
        <LogFile>_ ""</LogFile>
    </TermIOLog>
    <LogFile>
        <LoggingEnabled>_ 0</LoggingEnabled>
        <LogFile>_ ""</LogFile>
        <Category>_ 0</Category>
    </LogFile>
    <Aliases>
        <Count>0</Count>
        <SuppressDialog>0</SuppressDialog>
    </Aliases>
    <TraceHelper>
        <Enabled>0</Enabled>
        <ShowSource>1</ShowSource>
    </TraceHelper>
    <OCD>
        <HardwareSettings>5,16411,1,1,FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF,NA,NA</HardwareSettings>
        <ClockSettings>1,16000,0,0</ClockSettings>
        <TraceSettings>0,1,256,0,2151685632</TraceSettings>
        <Leave_target_running>0</Leave_target_running>
        <Operating_Frequency>16000000</Operating_Frequency>
    </OCD>
</settings>
