# 解压缩包络数据打印函数说明

## 概述

新增了一个专门用于打印 `WaveProcessCtrl.DecompressedEnvelopes` 数组所有内容的函数：`RdumRdusWaveProcess_PrintDecompressedEnvelopes()`。

## 函数信息

### 函数声明
```c
void RdumRdusWaveProcess_PrintDecompressedEnvelopes(void);
```

### 函数位置
- **头文件**: `Src\Service\RdumRdusDrv\RdumRdusWaveProcess.h`
- **实现文件**: `Src\Service\RdumRdusDrv\RdumRdusWaveProcess.c`

### 函数功能
- 打印 `WaveProcessCtrl.DecompressedEnvelopes` 三维数组的所有内容
- 按照 SLOT -> BRC组 -> 点位 的层次结构进行打印
- 使用十六进制格式显示每个点位的数值
- 包含状态检查，确保只在数据完成解压缩后才进行打印

## 数组结构

`DecompressedEnvelopes` 是一个三维数组：
```c
uint16 DecompressedEnvelopes[MAX_SLOT_COUNT][WAVE_PROCESS_BRC_COUNT * ENVELOPE_GROUPS][ENVELOPE_POINTS_PER_GROUP];
```

### 维度说明
- **第一维 (MAX_SLOT_COUNT)**: SLOT数量，最大为8
- **第二维 (WAVE_PROCESS_BRC_COUNT * ENVELOPE_GROUPS)**: BRC组数量，13 * 4 = 52
- **第三维 (ENVELOPE_POINTS_PER_GROUP)**: 每组的点位数量，为9

## 输出格式

### 示例输出
```
[WaveProcess]开始打印解压缩包络数据...
[WaveProcess]BRC数量: 12, 数组维度: [8][52][9]
[WaveProcess]=== SLOT 0 ===
[WaveProcess]  BRC组 0: 0000 0012 0034 0056 0078 009A 00BC 00DE 00FF
[WaveProcess]  BRC组 1: 0001 0013 0035 0057 0079 009B 00BD 00DF 0100
...
[WaveProcess]=== SLOT 1 ===
...
[WaveProcess]解压缩包络数据打印完成
```

### 输出说明
- 每个SLOT单独显示
- 每个BRC组在一行显示，包含9个十六进制数值
- 数值格式为4位十六进制（如：0000, 00FF, 1234）
- 数值之间用空格分隔

## 使用方法

### 基本使用
```c
#include "RdumRdusWaveProcess.h"

// 在发波流程完成后调用
if (RdumRdusWaveProcess_GetStatus() == RDUM_RDUS_WAVE_PROCESS_COMPLETE)
{
    RdumRdusWaveProcess_PrintDecompressedEnvelopes();
}
```

### 完整示例
参见 `example_print_usage.c` 文件中的完整使用示例。

## 注意事项

1. **状态检查**: 函数会自动检查发波流程状态，只有在 `WAVE_PROCESS_COMPLETE` 状态下才会打印数据
2. **调试输出**: 使用项目的 `debug_log` 系统进行输出，需要确保调试系统已正确配置
3. **数据量**: 完整的数组包含大量数据（8 * 52 * 9 = 3744个数值），输出内容较多
4. **性能影响**: 打印大量数据可能影响系统性能，建议仅在调试时使用

## 调用位置

函数已自动集成到发波流程中，在解压缩完成后会自动调用：
```c
/* 在 RdumRdusWaveProcess.c 的第423行 */
/* 打印所有的解压结果数组WaveProcessCtrl.DecompressedEnvelopes */
RdumRdusWaveProcess_PrintDecompressedEnvelopes();
```

## 依赖项

- `debug.h`: 用于调试输出
- `BrcSlotDef.h`: 包含相关的宏定义
- `RdumRdusWaveProcess.h`: 包含函数声明

## 修改记录

- **2025-01-27**: 新增打印函数，支持完整的解压缩数据输出
