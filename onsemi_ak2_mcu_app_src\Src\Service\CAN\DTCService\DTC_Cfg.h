#ifndef __DTC_CFG_H__
#define __DTC_CFG_H__


/****************************************************************************** 
* Includes 
*******************************************************************************/ 
#include "Types.h"
#include "CAN_AppSignalManage.h"
#include "DID.h"
/****************************************************************************** 
* Constants and macros 
*******************************************************************************/
/* 诊断调查表中定义的快照字节数*/
#define SNAPSHOT_BYTE               17U
/* 诊断调查表中定义的快照DID个数*/
#define SNAPSHOT_NUM                6U


#define PASPSLDTCNUM                (31U)
#define PSLDTCNUM                   (4U)
#define PSLALLDTCNUM                (PASPSLDTCNUM +PSLDTCNUM )
#define DTC_WINDOW_NUM                (52u)
/*******************************************************************************/
typedef struct
{
    uint8 GcSnapshotData[SNAPSHOT_BYTE];            /* DTC快照数据          SnapshotDataRecord*/
    uint8 GcSnapshotData_First[SNAPSHOT_BYTE];            /* DTC快照数据          SnapshotDataRecord*/
    uint8 GcAgingCounter;                           /* 当前故障已老化周期   ExtendDataRecord*/
    uint8 GcDTCOccurrenceCounter;                   /* DTC故障发生次数        ExtendDataRecord*/  
    union 
    {
        uint8 GcDTCStatus;
        struct
        {
            uint8 b0:1;
            uint8 b1:1;
            uint8 b2:1;
            uint8 b3:1;
            uint8 b4:1; 
            uint8 b5:1;
            uint8 b6:1;
            uint8 b7:1;
        } DTCSTATUSbits;
    } DTCSTATUS;
}DTCInfomationStruct;
/*******************************************************************************/
enum DTC_Identifier
{
    /**********************定义DTC在数组中的位置**************************/
    DTC_CAN_BUSOFF_FAIL = 0,
    DTC_VOLTAGE_LOW_FAIL,//1
    DTC_VOLTAGE_HIGH_FAIL,
    DTC_MISSING_ACU,
    DTC_MISSING_ADAS,//4

    DTC_MISSING_FBCM,
    DTC_MISSING_EPS,
    DTC_MISSING_ESP,
    DTC_MISSING_HU,
    DTC_MISSING_XCU,/*9*/

    DTC_INVALID_ACU_DATA,
    DTC_INVALID_ADAS_DATA,
    DTC_INVALID_EPS_DATA,
    DTC_INVALID_ESP_DATA,
    /*DTC_INVALID_HU_DATA,*//*此DTC现不需要监控，为不影响整体的存储方式，及上报顺序，这里不做修改*/
    DTC_INVALID_XCU_DATA,/*14*/

    DTC_INVALID_FBCM_CHECK,
    DTC_INVALID_FBCM_COUNTER,
    DTC_INVALID_ACU_CHECK,
    DTC_INVALID_ACU_COUNTER,
    DTC_INVALID_ADAS_CHECK,//19

    DTC_INVALID_ADAS_COUNTER,
    DTC_INVALID_EPS_CHECK,
    DTC_INVALID_EPS_COUNTER,
    DTC_INVALID_ESP_CHECK,
    DTC_INVALID_ESP_COUNTER,//24

    DTC_INVALID_XCU_CHECK,/*25*/
    DTC_INVALID_XCU_COUNTER,/*26*/
    DTC_FLS_SHORT_UBATOR,/*USS01*/
    DTC_FLS_SHORT_GND_OR_OC,
    DTC_FLS_RINGTIME_FAILED,//29

    DTC_FLS_TYPE_MISMATCH,
    DTC_FL_SHORT_UBATOR,/*USS02*/
    DTC_FL_SHORT_GND_OR_OC,
    DTC_FL_RINGTIME_FAILED,
    DTC_FL_TYPE_MISMATCH,//34

    DTC_FML_SHORT_UBATOR,/*USS03*/
    DTC_FML_SHORT_GND_OR_OC,
    DTC_FML_RINGTIME_FAILED,
    DTC_FML_TYPE_MISMATCH,//38

    DTC_FMR_SHORT_UBATOR,/*USS04*/
    DTC_FMR_SHORT_GND_OR_OC,
    DTC_FMR_RINGTIME_FAILED,
    DTC_FMR_TYPE_MISMATCH,//42

    DTC_FR_SHORT_UBATOR,/*USS05*/
    DTC_FR_SHORT_GND_OR_OC,
    DTC_FR_RINGTIME_FAILED,
    DTC_FR_TYPE_MISMATCH,//46

    DTC_FRS_SHORT_UBATOR,/*USS06*/
    DTC_FRS_SHORT_GND_OR_OC,
    DTC_FRS_RINGTIME_FAILED,
    DTC_FRS_TYPE_MISMATCH,//50

    DTC_RRS_SHORT_UBATOR,/*USS07*/
    DTC_RRS_SHORT_GND_OR_OC,
    DTC_RRS_RINGTIME_FAILED,
    DTC_RRS_TYPE_MISMATCH,//54

    DTC_RR_SHORT_UBATOR,/*USS08*/
    DTC_RR_SHORT_GND_OR_OC,
    DTC_RR_RINGTIME_FAILED,
    DTC_RR_TYPE_MISMATCH,//58

    DTC_RMR_SHORT_UBATOR,/*USS09*/
    DTC_RMR_SHORT_GND_OR_OC,
    DTC_RMR_RINGTIME_FAILED,
    DTC_RMR_TYPE_MISMATCH,//62

    DTC_RML_SHORT_UBATOR,/*USS10*/
    DTC_RML_SHORT_GND_OR_OC,
    DTC_RML_RINGTIME_FAILED,
    DTC_RML_TYPE_MISMATCH,//66

    DTC_RL_SHORT_UBATOR,/*USS11*/
    DTC_RL_SHORT_GND_OR_OC,
    DTC_RL_RINGTIME_FAILED,
    DTC_RL_TYPE_MISMATCH,//70

    DTC_RLS_SHORT_UBATOR,/*USS12*/
    DTC_RLS_SHORT_GND_OR_OC,
    DTC_RLS_RINGTIME_FAILED,
    DTC_RLS_TYPE_MISMATCH,//74


    DTC_ADAS_SYNCTIME_FAILED,
    DTC_UNCOMPLETE_CONFIG,
    DTC_Missing_CAL,
    DTC_ECU_INTERNAL_FAIL,
    DTC_WDG_SA_UC_FAIL,//79

    DTC_FSC_SHORT_TO_BAT,//80
    DTC_FSC_SHORT_TO_GND,//81
    TOTAL_DTC_RECORD_NUMBER82 = 82,

    DTC_RSC_SHORT_TO_BAT = 82,//82
    DTC_RSC_SHORT_TO_GND,//83
    DTC_SSC_SHORT_TO_BAT,//84
    DTC_SSC_SHORT_TO_GND,//85
    TOTAL_DTC_RECORD_NUMBER86 = 86,

    /*Below DTC will not be recorded due to EEP memory limitation and those DTC will not be monitored in LEEA_X01 project*/

    DTC_FLS_STATUS_FAILED = 86,
    DTC_FLS_BLINDNESS,
    DTC_FL_STATUS_FAILED,
    DTC_FL_BLINDNESS,
    DTC_FML_STATUS_FAILED,

    DTC_FML_BLINDNESS,
    DTC_FMR_STATUS_FAILED,
    DTC_FMR_BLINDNESS,
    DTC_FR_STATUS_FAILED,
    DTC_FR_BLINDNESS,

    DTC_FRS_STATUS_FAILED,
    DTC_FRS_BLINDNESS,
    DTC_RRS_STATUS_FAILED,
    DTC_RRS_BLINDNESS,
    DTC_RR_STATUS_FAILED,

    DTC_RR_BLINDNESS,
    DTC_RMR_STATUS_FAILED,
    DTC_RMR_BLINDNESS,
    DTC_RML_STATUS_FAILED,
    DTC_RML_BLINDNESS,

    DTC_RL_STATUS_FAILED,
    DTC_RL_BLINDNESS,
    DTC_RLS_STATUS_FAILED,
    DTC_RLS_BLINDNESS,
    //DTC_FSC_SHORT_TO_BAT,

    //DTC_FSC_SHORT_TO_GND,
    //DTC_RSC_SHORT_TO_BAT,
    //DTC_RSC_SHORT_TO_GND,

    /*新增12个DTC*/
    DTC_USS01_DELAMINATION,
    DTC_USS02_DELAMINATION,
    DTC_USS03_DELAMINATION,
    DTC_USS04_DELAMINATION,
    DTC_USS05_DELAMINATION,
    DTC_USS06_DELAMINATION,
    DTC_USS07_DELAMINATION,
    DTC_USS08_DELAMINATION,
    DTC_USS09_DELAMINATION,
    DTC_USS10_DELAMINATION,
    DTC_USS11_DELAMINATION,
    DTC_USS12_DELAMINATION,

    TOTAL_DTC_NUMBER,    /**/
};

/*******************************************************************************/
typedef enum 
{
    TESTINIT = 0,
    TESTFAIL,   
    TESTPASS,   
}DTC_StatusType;
/*******************************************************************************/
typedef enum 
{
    DTC_INIT_00 = (uint8)0x00, 
    DTC_INPUTE_PARA_ERROR_01,
}DTC_MoudleErrorType;
/*******************************************************************************/
/*******************************************************************************/
#define DTC_INIT_UNFINISH                   SDF_U8_VALUE0  
#define DTC_INIT_FINISH                     SDF_U8_VALUE1

/****************************************************************************** 
* External objects 
********************************************************************************/ 

extern DTCInfomationStruct GsDTCInfomationStr[TOTAL_DTC_RECORD_NUMBER86];

extern DTCInfomationStruct GsDTCInfomationStrBak[TOTAL_DTC_RECORD_NUMBER86];

extern volatile uint32 GwDTCNumberCfg[TOTAL_DTC_NUMBER];

extern volatile uint8 GcDTCDetectionStatus[TOTAL_DTC_RECORD_NUMBER86];

extern uint8 GcDTCAgingCntEnaFlg[TOTAL_DTC_RECORD_NUMBER86];

extern boolean GcDTCDetectionInitStatusFlag;
extern  uint8 Gu8PAS_PSLFuncAssocDTC[PASPSLDTCNUM];
extern  uint8 Gu8PSLFuncAssocDTC[PSLDTCNUM];
extern uint8 Gu8DTCWindow_List[DTC_WINDOW_NUM];
/******************************************************************************* 
* Global Functions 
********************************************************************************/ 
void SaveDTCInformationToDataFlash(void);

void DTCVariableInit(void);
void SaveDTCInforToDataFlash(uint8 LcDTC_INDEX);
void SaveALLDTCImmediately(void);

#endif

