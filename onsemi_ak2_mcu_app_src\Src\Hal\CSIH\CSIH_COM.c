/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: IOHal.c
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/
#include "CSIH_COM.h"
#include "CSIHDrv.h"

static void r_csih0_callback_sendend(void);
static void r_csih0_callback_error(uint32 err_type);
static void r_csih0_callback_receiveend(void);
static void r_csih2_callback_sendend(void);
static void r_csih2_callback_error(uint32 err_type);
static void r_csih2_callback_receiveend(void);
static void r_csih3_callback_sendend(void);
static void r_csih3_callback_error(uint32 err_type);
static void r_csih3_callback_receiveend(void);

/*************************************宏定义***********************************/

/********************************数据类型定义**********************************/
extern volatile uint16  g_csih0_tx_num;                        /* csih0 send data count */
extern volatile uint16  g_csih0_rx_num;                        /* csih0 receive data count */
extern volatile uint16  g_csih0_rx_total_num;                  /* csih0 receive data total times */
extern volatile uint16 * gp_csih0_rx_address;                   /* csih0 receive buffer address */
extern volatile uint16 * gp_csih0_tx_address;                   /* csih0 send buffer address */

extern volatile uint16  g_csih1_tx_num;                        /* csih1 send data count */
extern volatile uint16  g_csih1_rx_num;                        /* csih1 receive data count */
extern volatile uint16  g_csih1_rx_total_num;                  /* csih1 receive data total times */
extern volatile uint16 * gp_csih1_rx_address;                   /* csih1 receive buffer address */
extern volatile uint16 * gp_csih1_tx_address;                   /* csih1 send buffer address */

extern volatile uint16  g_csih2_tx_num;                        /* csih2 send data count */
extern volatile uint16  g_csih2_rx_num;                        /* csih2 receive data count */
extern volatile uint16  g_csih2_rx_total_num;                  /* csih2 receive data total times */
extern volatile uint16 * gp_csih2_rx_address;                   /* csih2 receive buffer address */
extern volatile uint16 * gp_csih2_tx_address;                   /* csih2 send buffer address */

extern volatile uint16  g_csih3_tx_num;                        /* csih3 send data count */
extern volatile uint16  g_csih3_rx_num;                        /* csih3 receive data count */
extern volatile uint16  g_csih3_rx_total_num;                  /* csih3 receive data total times */
extern volatile uint16 * gp_csih3_rx_address;                   /* csih3 receive buffer address */
extern volatile uint16 * gp_csih3_tx_address;                   /* csih3 send buffer address */
/* Start user code for global. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */
///**********************************变量定义************************************/
/***********************************************************************************************************************
* Function Name: r_csih0_interrupt_receive
* Description  : None
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void CSIH0RxIntCallBack(void)
{
    uint8 err_type;
    uint16 temp;
    err_type = (CSIH0.STR0 & (_CSIH_CONSISTENCY_ERROR_DETECTED | _CSIH_PARITY_ERROR_DETECTED | _CSIH_OVERRUN_ERROR_DETECTED));
    CSIH0.STCR0 |= (_CSIH_CONSISTENCY_ERROR_CLEAR | _CSIH_PARITY_ERROR_CLEAR | _CSIH_OVERRUN_ERROR_CLEAR);
    if (err_type != 0U)
    {
        r_csih0_callback_error(err_type);
    }
    else
    {
        temp = g_csih0_rx_total_num;
        if (temp > g_csih0_rx_num)
        {
            *gp_csih0_rx_address = CSIH0.RX0W;
            gp_csih0_rx_address++;
            g_csih0_rx_num++;
        }
        temp = g_csih0_rx_total_num;
        if (temp == g_csih0_rx_num)
        {
            r_csih0_callback_receiveend();
        }
    }
}
/***********************************************************************************************************************
* Function Name: r_csih0_interrupt_error
* Description  : None
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void CSIH0ErrIntCallBack(void)
{
    uint32 err_type;
    err_type = (CSIH0.STR0 & (_CSIH_CONSISTENCY_ERROR_DETECTED | _CSIH_PARITY_ERROR_DETECTED | _CSIH_OVERRUN_ERROR_DETECTED));
    CSIH0.STCR0 |= (_CSIH_CONSISTENCY_ERROR_CLEAR | _CSIH_PARITY_ERROR_CLEAR | _CSIH_OVERRUN_ERROR_CLEAR);
    *gp_csih0_rx_address = CSIH0.RX0W;
    if (err_type != 0U)
    {
        r_csih0_callback_error(err_type);
    }
}
/***********************************************************************************************************************
* Function Name: r_csih0_interrupt_send
* Description  : None
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void CSIH0TxIntCallBack(void)
{
    uint32 regValue = CSIH0.TX0W & _CSIH_TRANSMIT_SETTING_INIT;
    if (g_csih0_tx_num > 0U)
    {
        regValue =  *gp_csih0_tx_address | regValue;
        CSIH0.TX0W = regValue;
        gp_csih0_tx_address++;
        g_csih0_tx_num--;
    }
    else
    {
        r_csih0_callback_sendend();
    }
}
/***********************************************************************************************************************
* Function Name: r_csih0_callback_sendend
* Description  : This function is a callback function when TCSS0 finishes transmission.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
static void r_csih0_callback_sendend(void)
{
    /* Start user code. Do not edit comment generated here */
    /* End user code. Do not edit comment generated here */
}
/***********************************************************************************************************************
* Function Name: r_csih0_callback_error
* Description  : This function is a callback function when TCSS0 reception error occurs.
* Arguments    : err_type -
*                    error type value
* Return Value : None
***********************************************************************************************************************/
static void r_csih0_callback_error(uint32 err_type)
{
    /* Start user code. Do not edit comment generated here */
    /* End user code. Do not edit comment generated here */
}
/***********************************************************************************************************************
* Function Name: r_csih0_callback_receiveend
* Description  : This function is a callback function when TCSS0 finishes reception.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
static void r_csih0_callback_receiveend(void)
{
    /* Start user code. Do not edit comment generated here */
    /* End user code. Do not edit comment generated here */
}

/***********************************************************************************************************************
* Function Name: r_csih2_interrupt_error
* Description  : None
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void CSIH2ErrIntCallBack(void)
{
    uint32 err_type;
    err_type = (CSIH2.STR0 & (_CSIH_CONSISTENCY_ERROR_DETECTED | _CSIH_PARITY_ERROR_DETECTED | _CSIH_OVERRUN_ERROR_DETECTED));
    CSIH2.STCR0 |= (_CSIH_CONSISTENCY_ERROR_CLEAR | _CSIH_PARITY_ERROR_CLEAR | _CSIH_OVERRUN_ERROR_CLEAR);
    *gp_csih2_rx_address = CSIH2.RX0W;
    if (err_type != 0U)
    {
        r_csih2_callback_error(err_type);
    }
}

/* Start user code for adding. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */
/***********************************************************************************************************************
* Function Name: r_csih2_callback_error
* Description  : This function is a callback function when TCSS2 reception error occurs.
* Arguments    : err_type -
*                    error type value
* Return Value : None
***********************************************************************************************************************/
static void r_csih2_callback_error(uint32 err_type)
{
    /* Start user code. Do not edit comment generated here */
    /* End user code. Do not edit comment generated here */
}
/***********************************************************************************************************************
* Function Name: r_csih3_interrupt_error
* Description  : None
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
// #pragma interrupt r_csih3_interrupt_error(enable=false, channel=160, fpu=true, callt=false)
void CSIH3ErrIntCallBack(void)
{
    uint32 err_type;
    err_type = (CSIH3.STR0 & (_CSIH_CONSISTENCY_ERROR_DETECTED | _CSIH_PARITY_ERROR_DETECTED | _CSIH_OVERRUN_ERROR_DETECTED));
    CSIH3.STCR0 |= (_CSIH_CONSISTENCY_ERROR_CLEAR | _CSIH_PARITY_ERROR_CLEAR | _CSIH_OVERRUN_ERROR_CLEAR);
    *gp_csih3_rx_address = CSIH3.RX0W;
    if (err_type != 0U)
    {
        r_csih3_callback_error(err_type);
    }
}
/***********************************************************************************************************************
* Function Name: r_csih3_callback_error
* Description  : This function is a callback function when TCSS3 reception error occurs.
* Arguments    : err_type -
*                    error type value
* Return Value : None
***********************************************************************************************************************/
static void r_csih3_callback_error(uint32 err_type)
{
    /* Start user code. Do not edit comment generated here */
    /* End user code. Do not edit comment generated here */
}

