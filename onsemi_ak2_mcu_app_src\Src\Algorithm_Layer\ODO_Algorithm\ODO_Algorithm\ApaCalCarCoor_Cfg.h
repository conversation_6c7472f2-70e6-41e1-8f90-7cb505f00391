/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * ApaCalCarCoor.c: 
 * Created on: 2020-12-07 19:09
 * Original designer: 22554
 ******************************************************************************/

#ifndef   __APACALCARCOOR_CFG_H__
#define   __APACALCARCOOR_CFG_H__

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
//#include "./../../../../Std_Types.h"

//包含其它模块的CFG文件。
/** @brief 调用CAN信号管理获取档位、车速、轮速脉冲、转角等信息 */
//#include "./../../../../Application_Layer/App Signal_Manage/CAN AppSignalManage/CAN_AppSignal_CommonTypes.h"
#include "ApaCalCarCoor_Types.h"
#include "CAN_AppSignal_CommonTypes.h"
#include "types.h"

/******************************************************************************
 * Compiler option used to distinguish inclusion done by the owner
 *****************************************************************************/
#if 1


/**
 * \defgroup   APAWORKMANAGE_CFG
 * @{
 */
#ifdef __cplusplus
extern "C"{
#endif
/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/

/* 轮速脉冲最大值 */
#define WHEEL_PULSE_MAX_VALUE                               65535u


/* 直线自标定轮速关系 */
/* 四轮直线高速标定角度限制,默认方向盘在6度以内运行为直线行驶 unit:0.1° */
#define STRAIGHT_CALI_ANGLE                                 (6u*10u)
/* 直线姿态航向角变化限制 3度以内 rad */
#define STRAIGHT_CALI_CAR_ANGLE                             (3/57.295779513)
/* 四轮直线标定速度下限(0.01km/h) */
#define STRAIGHT_CALI_SPEED                                 2500u
/* 满足直线标定条件的进入所需维持时间ms */
#define STRAIGHT_CALI_ENTER_NEED_TIME                       1000u
/* 判断满足用于直线标定计算的运行距离mm */
#define STRAIGHT_CALI_RUN_DIS_FOR_SINGLE_CAL                50000
/* 直线标定数据量记录的上限时间ms */
#define STRAIGHT_CALI_TIME_LIMIT                            60000u
/* 执行轮速脉冲比例计算所需的总运行距离mm */
#define STRAIGHT_CALI_RUN_DIS_FOR_CAL                       200000


/* 180度掉头自标定轮速脉冲距离 */
/* 判断直行姿态的行驶速度 */
#define TURN_CALI_SPEED_FOR_STRAIGHT                        2500u
/* 判断直线姿态行驶时方向盘角度限制 unit:0.1° */
#define TURN_CALI_WHEEL_ANGLE_FOR_STRAIGHT                  (6u*10u)
/* 判断直线姿态航向角变化限制 4度以内 rad */
#define TURN_CALI_CAR_ANGLE_FOR_STRAIGHT                    (4/57.295779513)
/* 确定沿线直行的时间 ms */
#define TURN_CALI_STRAIGHT_VERIFY_TIME                      4000u

/* 用于判断180°转弯的及参数纠正的角度误差下限值 unit:0.1° */
#define TURN_CALI_ANGLE_ERR_LIMIT_MIN                       70
/* 用于判断180°转弯的及参数纠正的角度误差上限值 unit:0.1° */
#define TURN_CALI_ANGLE_ERR_LIMIT_MAX                       150
/* 转弯标定结束时所需的角度限制,默认6度以内运行为直线行驶 unit:0.1° */
#define TURN_CALI_FINISH_ANGLE                              (6u*10u)
/* 转弯标定结束时所需的速度下限(0.01km/h) */
#define TURN_CALI_FINISH_SPEED                              2500u
/* 转弯标定结束所需直线维持时间ms */
#define TURN_CALI_FINISH_QUIT_TIME                          3000u
/* 判定掉头的两直线间的距离限值m,设定为最大3车道 */
#define TURN_CALI_VALID_DIS                                 9


/******************************************************************************/
/**
 * @brief   以下为调试部分。需要时打开。默认关闭。
 */
/******************************************************************************/
/******************************************************************************/
/**
 * @brief   调试断言设置
 */
/*****************************************************************************/
#if (CPOS_DEVASSERT == STD_ON)
static inline void ApaCalCarCoorAssert(volatile bool x)
{
    if(x) { } else { BKPT_ASM; for(;;) {} }
}
    #define CPOS_ASSERT(x) ApaCalCarCoorAssert(x)
#else
    /* Assert macro does nothing */
    #define CPOS_ASSERT(x) ((void)0)
#endif

/******************************************************************************/
/**
 * @brief   打印
 */
/*****************************************************************************/
#if (CPOS_DEBUG_PRINT == STD_ON)
    #define CPOS_DEBUG_PRINTF              Lhprintf
#else
    #define CPOS_DEBUG_PRINTF(...)         (void)0
#endif

/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
uint16 CPOS_GetCar_Speed(void);

bool CPOS_GetStraight(void);


Car_WheelPulseDirType CPOS_GetCar_RLWheelDir(void);
Car_WheelPulseDirType CPOS_GetCar_RRWheelDir(void);



void CPOS_GetCaliParamValidSts(CPOS_CaliParamValidType *LpSts);



/**
 * end of group   MAX2008x_CFG
 * @}
 */
#ifdef __cplusplus
}
#endif

#endif /* end of SYS_USE_XXX */

#endif /* end of */
