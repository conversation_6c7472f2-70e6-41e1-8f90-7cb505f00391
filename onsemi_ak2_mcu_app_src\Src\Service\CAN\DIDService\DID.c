
/**********************************************************************
* Includes 
************************************************************************/
#include "DID.h"
#include "CAN_UDS.h"
#include "EELHal.h"
#include "Elmos_524_17_SnsCtrl.h"
#include "PowerSingalManage.h"
#include "CAN_IL.h"
#include "McuGitVersion.h"
/********应用  软件版本号 （实际代码地址见Linker File文件）********/

/***************************出厂定死的DID******************************/

#if(DIDTYPE == DIDTYPE_X02)//X平台特有信息
    /********汽车生产单元号零件号********/
    uint8 DID_F187_Buff[15] = "X02-65011003   ";
    /********系统供应商标识号********/
    uint8 DID_F18A_Buff[10] = "8CG       ";
    /********系统供应商软件ID********/
    uint8 DID_F026_Buff[2] = { 0x00,0x12 };/*客户定义*/
    /********客户端App软件版本********/
    uint8 DID_F195_Buff[2] = { 0x00,0x00 };
#else//W平台特有信息
    /********汽车生产单元号零件号********/
    uint8 DID_F187_Buff[15] = "LAC-65010042   ";
    /********系统供应商标识号********/
    uint8 DID_F18A_Buff[10] = "8CG       ";
    /********系统供应商软件ID********/
    uint8 DID_F026_Buff[2] = { 0x00,0x04 };/*客户定义*/
    /********客户端App软件版本********/
    uint8 DID_F195_Buff[2] = { 0x10,0x01 };
#endif // (DIDTYPE==DIDTYPE_X02)



/********系统供应商ID********/
uint8 DID_F197_Buff[10] = "PAS       ";
/***************************根据变量改变的DID**************************/
/********ECU制造日期 3********/
uint8 DID_F18B_Buff[4] = {0x17,0x0A,0x19,0};
/********电控单元序列号 14Byte + Eeprom 2Byte补充，最后一个字节默认值是‘0’，当通过EEPROM写入后该值为‘1’********/
/*
Byte0:Year(hight byte) 年（高字节）
Byte1:Year(low byte) 年（低字节）
Byte2: Month 月（高字节）
Byte3: Month 月（低字节）
Byte4: Day 日（高字节）
Byte5: Day 日（低字节）
Byte6-Byte10:SerialNumber 序号
"21092800001"表示2021年9月28号，流水号00001*/
uint8 DID_F18C_Buff[16] ="231026000010    ";

/***************************APP只读DID**************************/
/********指纹  *****/
uint8 DID_F15A_Buff[21] = "                     ";
/********会话模式********/
uint8 DID_F186_Buff[1] = {0};
/********编程尝试计数器*****/
uint8 DID_F021_Buff[2] = {0,1};
/*********编程成功计数器*******/
uint8 DID_F022_Buff[2] = {0,1};
/********系统供应商硬件版本号********/
uint8 DID_F193_Buff[2] = {0,1};/*BYTE 0:ECU硬件版本号{字节;BYTE1:ECU硬件版本号低字节*/
/***************************APP只读DID；动态更新**************************/
/********当前车速********/
uint8 DID_F100_Buff[3] = { 0 };
/********当前电压********/
uint8 DID_F101_Buff[2] = { 0 };
/********当前低压电源模式********/
uint8 DID_F102_Buff[2] = { 0 };
/********当前总里程********/
uint8 DID_F103_Buff[3] = { 0 };
/********当前UTC时间********/
uint8 DID_F104_Buff[7] = { 0 };
/********逻辑档位信号********/
uint8 DID_F105_Buff[1] = { 0 };
/********故障事件ID********/
uint8 DID_F106_Buff[10] = { 0 };
/***************************可写入的DID**************************/
/********汽车统一编号 17BYTE********/
uint8 DID_F190_Buff[17] = "ABCDEFG0123456789";
/********车辆制造日期3Byte********/
uint8 DID_F1A0_Buff[3] = {0};
/********SBL引导程序版本号2Byte ********/
uint8 DID_F14A_Buff[2] = {0};
/********FBL引导程序版本号2Byte ********/
uint8 DID_F14B_Buff[1] = { 0 };
/********ECU功能配置********/
uint8 DID_F010_Buff[8] = {0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF};
/********ECU功能配置 17********/
uint8 DID_F1A1_Buff[17] = {0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF};
/********ECU功能配置2 16********/
uint8 DID_F1B1_Buff[16] = { 0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF};
/********豪恩内部版本号********/
uint8 DID_FE00_Buff[34];

uint8 DID_FEFF_Buff[4] = { 0xFF,0xFF,0xFF,0xFF};
/*升级log回读DID*/
uint8 DID_FEFE_Buff[128] = { 0 };

/*定频扫频数据*/
//uint8 DID_FEFD_Buff[1] = { 0 };
/*boot启动参数*/
uint8 DID_F15F_Buff[17] = { 0 };

static void Updata_F186_SessionMode(void);
static void Updata_FEFE_UpdataLog(void);
static void Updata_F15F(void);
static void Updata_F100_VehicleSpeedOut(void);
static void Updata_F101_OperatingVoltage(void);

static void Updata_F102_LowVoltagePowerModeStatus(void);
static void Updata_F103_TotalOdometer(void);
static void Updata_F104_UTCTime(void);
static void Updata_F105_ActualLogicGearShift(void);
static void Updata_F106_InternalDemFaultEventId(void);

const struct DataIdentifier DIDDefinition[DID_TOTAL_NUM] =
{
    {0xFE00,(uint8*)DID_FE00_Buff,34,OnlyRead,0},
    {0xF187,(uint8*)DID_F187_Buff,15,OnlyRead,0},
    {0xF18A,(uint8*)DID_F18A_Buff,10,OnlyRead,0},
    {0xF197,(uint8*)DID_F197_Buff,10,OnlyRead,0},
    {0xF193,(uint8*)DID_F193_Buff,2,OnlyRead,0},
    {0xF195,(uint8*)DID_F195_Buff,2,OnlyRead,0},

    {0xF18C,(uint8*)DID_F18C_Buff,14,ReadWrite,DID_F18C_ADDRESS},
    {0xF190,(uint8*)DID_F190_Buff,17,ReadWrite,DID_F190_ADDRESS},
    {0xF18B,(uint8*)DID_F18B_Buff,3,ReadWrite,DID_F18B_ADDRESS},
    {0xF1A0,(uint8*)DID_F1A0_Buff,3,ReadWrite,DID_F1A0_ADDRESS},
    {0xF14A,(uint8*)DID_F14A_Buff,2,OnlyRead,0},

    {0xF14B,(uint8*)DID_F14B_Buff,1,OnlyRead,0},

    {0xF1A1,(uint8*)DID_F1A1_Buff,17,ReadWrite,DID_F1A1_ADDRESS},
    {0xF15A,(uint8*)DID_F15A_Buff,21,OnlyRead,0},
    {0xF021,(uint8*)DID_F021_Buff,2,OnlyRead,0},
    {0xF022,(uint8*)DID_F022_Buff,2,OnlyRead,0},
    {0xF026,(uint8*)DID_F026_Buff,2,OnlyRead,0},

    {0xF186,(uint8*)DID_F186_Buff,1,OnlyRead,0},
    {0xF15F,(uint8*)DID_F15F_Buff,17,OnlyRead,0},
    {0xFEFF,(uint8*)DID_FEFF_Buff,4,OnlyRead,0},
    {0xFEFE,(uint8*)DID_FEFE_Buff,128,OnlyRead,0},
    //{0xFEFD,(uint8*)DID_FEFD_Buff,1,ReadWrite,CALIBRATION_1_ADDRESS},

    {0xF100,(uint8*)DID_F100_Buff,3,OnlyRead,0},
    {0xF101,(uint8*)DID_F101_Buff,2,OnlyRead,0},
    {0xF102,(uint8*)DID_F102_Buff,1,OnlyRead,0},
    {0xF103,(uint8*)DID_F103_Buff,3,OnlyRead,0},

    {0xF104,(uint8*)DID_F104_Buff,7,OnlyRead,0},
    {0xF105,(uint8*)DID_F105_Buff,1,OnlyRead,0},
    {0xF106,(uint8*)DID_F106_Buff,10,OnlyRead,0},
    {0xF1B1,(uint8*)DID_F1B1_Buff,16,ReadWrite,DID_F1B1_ADDRESS},
};
const struct UDS_Updata_DID_Type DIDDefinition_Needupdata[DID_NEED_UPDATA_NUM] =
{
    {0xF186,Updata_F186_SessionMode},
    {0xF15F,Updata_F15F},
    {0xFEFE,Updata_FEFE_UpdataLog},
    {0xF100,Updata_F100_VehicleSpeedOut},
    {0xF101, Updata_F101_OperatingVoltage},

    {0xF102,Updata_F102_LowVoltagePowerModeStatus},
    {0xF103,Updata_F103_TotalOdometer},
    {0xF104,Updata_F104_UTCTime},
    {0xF105,Updata_F105_ActualLogicGearShift},
    {0xF106,Updata_F106_InternalDemFaultEventId},
};

uint8 GcEOLEndCfgFlg = INVALID;

/******************************************************************
* 函数名称: CRC16
*
* 功能描述: 接收的数据进行CRC校验函数
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: CRC16值
*
* 其它说明: 无
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
uint16 CRC16(volatile uint8* LcPtr, uint16 LwLen)
{
    volatile uint8 i;
    volatile uint16 LwCRC16 = (uint16)0;

    while (LwLen)
    {
        LwLen--;
        for (i = (uint8)0x80; i != (uint8)0; i >>= 1u)
        {
            if ((LwCRC16 & (uint16)0x8000) != (uint16)0)
            {
                LwCRC16 <<= 1u;
                LwCRC16 ^= (uint16)0x1021;
            }
            else
            {
                LwCRC16 <<= 1u;
            }
            if ((*LcPtr & i) != (uint8)0)
            {
                LwCRC16 ^= (uint16)0x1021;
            }
        }
        LcPtr++;
    }
    return(LwCRC16);
}
/**********************************************************************
*   函数名称:   XOR_LRCalorithm
*
*   功能描述:F1C5校验算法
*
*   输入参考:   无
*
*   输出参数:   无
*
*   返 回 值:    无
*
*   其它说明: 
*
*   修改日期        版本号       修改人       修改内容
*
**********************************************************************/
void XOR_LRCalorithm(uint8 *LpcData, uint8*LcLRCH,uint8*LcLRCL)
{
    uint8 i;
    uint8 LcLRCTemp = 0;
    uint8 LcH,LcL;
    for(i=0;i<18;i++)
    {
        LcLRCTemp ^= LpcData[i];
    }
    LcH = LcLRCTemp>>4u;
    LcL = LcLRCTemp&0x0F;
    /** @brief: 十六进制转为ASCII */
    if(LcH<=0x09u)
    {
        LcH += '0';
    }
    else
    {
        LcH = LcH -10;
        LcH += 'A';
    }
    if(LcL<=0x09u)
    {
        LcL += '0';
    }
    else
    {
        LcL = LcL -10;
        LcL += 'A';
    }
    
    *LcLRCH = LcH;
    *LcLRCL = LcL;
}

uint8 memcmp1(uint8 *LcDataPtr1, uint8 *LcDataPtr2,uint16 LwWriteDataLength)
{
    uint8 Ret=0;
    uint8 i;
    for(i = 0;i < LwWriteDataLength;i++)
    {
        if(LcDataPtr1[i] != LcDataPtr2[i])
        {
            Ret = 1;
            return Ret; 
        }
        else
        {

        }
    }
    return Ret;
}
void DID_init_F1B1(void)
{

    uint8 LcBuffer[DID_F1B1_EEPROM_LEN];
    uint8 LcECUCfgCheckBuff[ECU_CFG_BYTE_CHECK_EEPROM_LEN];
    uint16 LwCRC16;
    uint16 LwCRC16_EEPROM;
    uint16 LwECUCfgFlag;
    uint8 LcCmpFlag;
    /*读取配置字校验信息*/
    EELReadData(&LcECUCfgCheckBuff[0], CALIB_DATA_FLAG_ADDRESS, CALIB_DATA_FLAG_EEPROM_LEN);
    EELReadData(&LcBuffer[0], DID_F1B1_ADDRESS, DID_F1B1_EEPROM_LEN);

    LwCRC16 = CRC16((uint8*)LcBuffer, DID_F1B1_EEPROM_LEN);

    LwECUCfgFlag = (uint32)LcECUCfgCheckBuff[1];
    LwECUCfgFlag |= (uint32)LcECUCfgCheckBuff[0] << 8;

    LwCRC16_EEPROM = (uint32)LcECUCfgCheckBuff[3];
    LwCRC16_EEPROM |= (uint32)LcECUCfgCheckBuff[2] << 8;

    if ((LwCRC16 == LwCRC16_EEPROM) && (LwECUCfgFlag == ECU_CONFIG_BYTE_FLAG))
    {
        /** @brief: 配置字检测Ok */
        memcpy((char*)DID_F1B1_Buff, (char*)LcBuffer, DID_F1B1_EEPROM_LEN);
    }
    else/*校验未通过，写入全FF*/
    {
        LcCmpFlag = memcmp1(&DID_F1B1_Buff[0], LcBuffer, DID_F1B1_EEPROM_LEN);
        if (LcCmpFlag != 0)/*如果不是FF,则写入全FF*/
        {
            (void)EELWriteDataImmediate(&DID_F1B1_Buff[0], DID_F1B1_ADDRESS, DID_F1B1_EEPROM_LEN);
        }
    }
}
void DID_init_F187(void)
{
    uint8 LcBuffer[DID_F187_EEPROM_LEN];
    uint8 LcCmpFlag = 0;
    EELReadData(&LcBuffer[0], DID_F187_ADDRESS, DID_F187_EEPROM_LEN);
    LcCmpFlag = memcmp1(&DID_F187_Buff[0],LcBuffer,DID_F187_EEPROM_LEN);
    if(LcCmpFlag != 0)
    {
        (void)EELWriteDataImmediate(&DID_F187_Buff[0], DID_F187_ADDRESS, DID_F187_EEPROM_LEN);
    }
    else
    {
        /*数据校验相等，则不需要重写*/
    }
}
void DID_init_F18A(void)
{
    uint8 LcBuffer[DID_F18A_EEPROM_LEN];
    uint8 LcCmpFlag = 0;
    EELReadData(&LcBuffer[0], DID_F18A_ADDRESS, DID_F18A_EEPROM_LEN);
    LcCmpFlag = memcmp1(&DID_F18A_Buff[0],LcBuffer,DID_F18A_EEPROM_LEN);
    if(LcCmpFlag != 0)
    {
        (void)EELWriteDataImmediate(&DID_F18A_Buff[0], DID_F18A_ADDRESS, DID_F18A_EEPROM_LEN);
    }
    else
    {
        /*数据校验相等，则不需要重写*/
    }
}
void DID_init_F197(void)
{
    uint8 LcBuffer[DID_F197_EEPROM_LEN];
    uint8 LcCmpFlag = 0;
    EELReadData(&LcBuffer[0], DID_F197_ADDRESS, DID_F197_EEPROM_LEN);
    LcCmpFlag = memcmp1(&DID_F197_Buff[0],LcBuffer,DID_F197_EEPROM_LEN);
    if(LcCmpFlag != 0)
    {
        (void)EELWriteDataImmediate(&DID_F197_Buff[0], DID_F197_ADDRESS, DID_F197_EEPROM_LEN);
    } 
    else
    {
        /*数据校验相等，则不需要重写*/
    }
}
void DID_init_F195(void)
{
    uint8 LcBuffer[DID_F195_EEPROM_LEN];
    uint8 LcCmpFlag = 0;
    EELReadData(&LcBuffer[0], DID_F195_ADDRESS,DID_F195_EEPROM_LEN);
    LcCmpFlag = memcmp1(&DID_F195_Buff[0],LcBuffer,DID_F195_EEPROM_LEN);
    if(LcCmpFlag != 0)
    {
        (void)EELWriteDataImmediate(&DID_F195_Buff[0], DID_F195_ADDRESS,DID_F195_EEPROM_LEN);
    }
    else
    {
        /*数据校验相等，则不需要重写*/
    }
}
void DID_init_F026(void)
{
    uint8 LcBuffer[DID_F026_EEPROM_LEN];
    uint8 LcCmpFlag = 0;
    EELReadData(&LcBuffer[0], DID_F026_ADDRESS,DID_F026_EEPROM_LEN);
    LcCmpFlag = memcmp1(&DID_F026_Buff[0],LcBuffer,DID_F026_EEPROM_LEN);
    if(LcCmpFlag != 0)
    {
        (void)EELWriteDataImmediate(&DID_F026_Buff[0], DID_F026_ADDRESS,DID_F026_EEPROM_LEN);
    }
    else
    {
        /*数据校验相等，则不需要重写*/
    }
}
void DID_init_F18C(void)
{
    uint8 Lu8F18C_Buff[DID_F18C_EEPROM_LEN];
    
    EELReadData(&Lu8F18C_Buff[0], DID_F18C_ADDRESS,DID_F18C_EEPROM_LEN);
    if(Lu8F18C_Buff[15] == 0x55)
    {
        /* 流水号已写入，则从EEPROM中加载数据到DID_F18C_Buff，否则使用默认值 */
        memcpy(DID_F18C_Buff,Lu8F18C_Buff,DID_F18C_EEPROM_LEN);
    }
}
void DID_init_F18B(void)
{
    uint8 Lu8F18B_Buff[DID_F18B_EEPROM_LEN];
    
    EELReadData(&Lu8F18B_Buff[0],DID_F18B_ADDRESS,DID_F18B_EEPROM_LEN);
    if(Lu8F18B_Buff[3] == 0x55)
    {
        /* 制造日期已写入，则从EEPROM中加载数据到DID_F18B_Buff，否则使用默认值 */
        memcpy(DID_F18B_Buff,Lu8F18B_Buff,DID_F18B_EEPROM_LEN);
    }
}
void DID_init_ErrorKeyCnt(void)
{
    uint8 Lu8HisErrCnt[1]={0};
    EELReadData(&Lu8HisErrCnt[0],ERRORKEYCNT_ADDRESS,ERRORKEYCNT_EEPROM_LEN);
    /*初次上电写安全访问计数为0*/
    if(Lu8HisErrCnt[0] > 0x03)
    {
        Lu8HisErrCnt[0] =0;
        (void)EELWriteDataImmediate(&Lu8HisErrCnt[0], ERRORKEYCNT_ADDRESS,ERRORKEYCNT_EEPROM_LEN);
        GcErrorKeyCnt = 0;
    }
    else
    {
        GcErrorKeyCnt = Lu8HisErrCnt[0];
    }
}

/**********************************************************************
*   函数名称:   DID_IN_Boot_init
*
*   功能描述:向EEPROM写入F189、F1C1、F1C0，以供Boot读取
*
*   输入参考:   无
*
*   输出参数:   无
*
*   返 回 值:    无
*
*   其它说明: 
*             
*   修改日期        版本号       修改人       修改内容
*
**********************************************************************/
//uint8 TestData[16] = {1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16};
//r_eel_status_t EELsts;
void DID_IN_Boot_init(void)
{
    uint8* DataPtr = (uint8*)(0xFFF3);
    /*读取FBL版本号：*/
    DID_F14B_Buff[0] = *DataPtr;
    DID_init_F1B1();/*从EEP读取当前DID内容,并放入缓存buffer*/
    DID_init_F187();/*从EEP读取当前DID内容,并放入缓存buffer*/
    DID_init_F18A();/*从EEP读取当前DID内容,并放入缓存buffer*/
    DID_init_F197();/*从EEP读取当前DID内容,并放入缓存buffer*/
    DID_init_F195();/*从EEP读取当前DID内容,并放入缓存buffer*/
    DID_init_F026();/*从EEP读取当前DID内容,并放入缓存buffer*/
    DID_init_F18C();/*从EEP读取当前DID内容,并放入缓存buffer*/
    DID_init_F18B();/*从EEP读取当前DID内容,并放入缓存buffer*/
    DID_init_ErrorKeyCnt();/*从EEP读取安全访问错误计数内容,并放入缓存buffer*/
    
    /*读取boot中存储的DID信息*/
    EELReadData(&DID_F14A_Buff[0], DID_F14A_ADDRESS, DID_F14A_EEPROM_LEN);
    EELReadData(&DID_F15A_Buff[0],DID_F15A_ADDRESS,DID_F15A_EEPROM_LEN);
    EELReadData(&DID_F021_Buff[0],DID_F021_ADDRESS,DID_F021_EEPROM_LEN);
    EELReadData(&DID_F022_Buff[0],DID_F022_ADDRESS,DID_F022_EEPROM_LEN);
    EELReadData(&DID_F193_Buff[0],DID_F193_ADDRESS,DID_F193_EEPROM_LEN);
    EELReadData(&DID_F190_Buff[0],DID_F190_ADDRESS,DID_F190_EEPROM_LEN);
    
    /*从EEP读取当前整车配置信息,并放入缓存buffer*/
    EELReadData(&DID_F1A0_Buff[0],DID_F1A0_ADDRESS,DID_F1A0_EEPROM_LEN); /**/
 //   EELReadData(&DID_FEFD_Buff[0], CALIBRATION_1_ADDRESS, CALIBRATION_1_LEN);
	//Elmos17SnsCtrl_SetDVWorkFlg(DID_FEFD_Buff[0]);

    /*从EEL读取升级log*/
    /*不在初始化时读取，只有在DID读取时，才从EEL获取数据*/
    /*get internal vesion*/
    memcpy(DID_FE00_Buff, (uint8_t*)MCU_APP_SRC_VER, 30);
}


/**********************************************************************
*   函数名称:   DID_F1B1_ECUConfigByteFlag_Init
*
*   功能描述:ECU配置字标志初始化
*
*   输入参考:   无
*
*   输出参数:   无
*
*   返 回 值:    无
*
*   其它说明: 
*             
*   修改日期        版本号       修改人       修改内容
*
**********************************************************************/

//uint8 DID_F1A1_BackUp_Init(void)
//{
//    uint8 LcBuffer[DID_F1A1_BACKUP_EEPROM_LEN];
//    uint8 LcECUCfgCheckBuff[ECU_F1A1_CHECK_BACKUP_EEPROM_LEN];
//    uint16 LwCRC16;
//    uint16 LwCRC16_EEPROM;
//    uint16 LwECUCfgFlag;
//    uint8 ReturnValue= INVALID;
//    /*读取配置字校验信息*/
//    EELReadData(&LcECUCfgCheckBuff[0], ECU_F1A1_CHECK_BACKUP_ADDRESS, ECU_F1A1_CHECK_BACKUP_EEPROM_LEN);
//    EELReadData(&LcBuffer[0], DID_F1A1_BACKUP_ADDRESS, DID_F1A1_BACKUP_EEPROM_LEN);
//    LwCRC16 = CRC16((uint8*)LcBuffer,DID_F1A1_BACKUP_EEPROM_LEN);
//    
//    LwECUCfgFlag  = (uint32)LcECUCfgCheckBuff[1];
//    LwECUCfgFlag |= (uint32)LcECUCfgCheckBuff[0]<<8;
//    
//    LwCRC16_EEPROM  = (uint32)LcECUCfgCheckBuff[3];
//    LwCRC16_EEPROM |= (uint32)LcECUCfgCheckBuff[2]<<8;
//
//    if((LwCRC16 == LwCRC16_EEPROM) && (LwECUCfgFlag == ECU_CONFIG_BYTE_FLAG))
//    {
//        /** @brief: 配置字检测Ok */
//        memcpy((char*)DID_F1A1_Buff,(char*)LcBuffer,(DID_F1A1_BACKUP_EEPROM_LEN-3));
//        /** @brief: 配置字的有效状态还需检测配置字内容，由于目前配置字还未定义，先直接置有效 */
//        ReturnValue = VALID;
//    }
//    else
//    {
//        /** @brief: 配置字未刷写，或刷写异常 */
//        ReturnValue = INVALID;
//    }
//    return ReturnValue;
//}

void DID_F1A1_ECUConfigByteFlag_Init(void)
{
    uint8 LcBuffer[DID_F1A1_EEPROM_LEN];
    uint8 LcECUCfgCheckBuff[ECU_CFG_BYTE_CHECK_EEPROM_LEN];
    uint16 LwCRC16;
    uint16 LwCRC16_EEPROM;
    uint16 LwECUCfgFlag;
    /*读取配置字校验信息*/
    EELReadData(&LcECUCfgCheckBuff[0], ECU_CFG_BYTE_CHECK_ADDRESS, ECU_CFG_BYTE_CHECK_EEPROM_LEN);
    EELReadData(&LcBuffer[0], DID_F1A1_ADDRESS, DID_F1A1_EEPROM_LEN);
    
    LwCRC16 = CRC16((uint8*)LcBuffer,DID_F1A1_EEPROM_LEN);
    
    LwECUCfgFlag  = (uint32)LcECUCfgCheckBuff[1];
    LwECUCfgFlag |= (uint32)LcECUCfgCheckBuff[0]<<8;
    
    LwCRC16_EEPROM  = (uint32)LcECUCfgCheckBuff[3];
    LwCRC16_EEPROM |= (uint32)LcECUCfgCheckBuff[2]<<8;

    if((LwCRC16 == LwCRC16_EEPROM) && (LwECUCfgFlag == ECU_CONFIG_BYTE_FLAG))
    {
        /** @brief: 配置字检测Ok */
        memcpy((char*)DID_F1A1_Buff,(char*)LcBuffer,DID_F1A1_EEPROM_LEN);
        /** @brief: 配置字的有效状态还需检测配置字内容，由于目前配置字还未定义，先直接置有效 */
        GcEOLEndCfgFlg = VALID;
    }
    else
    {
        /** @brief: 配置字未刷写，或刷写异常 */
        //GcEOLEndCfgFlg = DID_F1A1_BackUp_Init();
    }
}


/**********************************************************************
*   函数名称:   DID_F1B1_ECUConfigByteFlag_Save
*
*   功能描述:ECU配置字标志保存
*
*   输入参考:   无
*
*   输出参数:   无
*
*   返 回 值:    无
*
*   其它说明: 
*             
*   修改日期        版本号       修改人       修改内容
*
**********************************************************************/
void DID_F1A1_ECUConfigByteFlag_Save(void)
{
    uint8 LcECUCfgCheckBuff[ECU_CFG_BYTE_CHECK_EEPROM_LEN];
    uint16 LwCRC16;   
    LwCRC16 = CRC16((uint8*)DID_F1A1_Buff,DID_F1A1_EEPROM_LEN);
    /** @brief: 配置字标志 */
    LcECUCfgCheckBuff[0] = (uint8)(ECU_CONFIG_BYTE_FLAG>>8u);
    LcECUCfgCheckBuff[1] = (uint8)ECU_CONFIG_BYTE_FLAG;
    /** @brief: 配置字校验和 */
    LcECUCfgCheckBuff[2] = (uint8)(LwCRC16>>8u);
    LcECUCfgCheckBuff[3] = (uint8)LwCRC16;

    /** @brief: 写入配置字写入标志 */
    EELWriteDataImmediate(&LcECUCfgCheckBuff[0], ECU_CFG_BYTE_CHECK_ADDRESS, ECU_CFG_BYTE_CHECK_EEPROM_LEN);
    /*备份配置：CRC校验值写入 ECU_F1A1_CHECK_BACKUP_EEPROM_LEN = ECU_CFG_BYTE_CHECK_EEPROM_LEN=4*/
    EELWriteDataImmediate(&LcECUCfgCheckBuff[0], ECU_F1A1_CHECK_BACKUP_ADDRESS, ECU_F1A1_CHECK_BACKUP_EEPROM_LEN);
}
/**********************************************************************
*   函数名称:   DID_F1B1_Save
*
*   功能描述:ECU配置字标志保存
*
*   输入参考:   无
*
*   输出参数:   无
*
*   返 回 值:    无
*
*   其它说明:
*
*   修改日期        版本号       修改人       修改内容
*
**********************************************************************/
void DID_F1B1_Save(void)
{
    uint8 LcECUCfgCheckBuff[CALIB_DATA_FLAG_EEPROM_LEN];
    uint16 LwCRC16;
    LwCRC16 = CRC16((uint8*)DID_F1B1_Buff, DID_F1B1_EEPROM_LEN);
    /** @brief: 配置字标志 */
    LcECUCfgCheckBuff[0] = (uint8)(ECU_CONFIG_BYTE_FLAG >> 8u);
    LcECUCfgCheckBuff[1] = (uint8)ECU_CONFIG_BYTE_FLAG;
    /** @brief: 配置字校验和 */
    LcECUCfgCheckBuff[2] = (uint8)(LwCRC16 >> 8u);
    LcECUCfgCheckBuff[3] = (uint8)LwCRC16;

    /** @brief: 写入配置字写入标志 */
    EELWriteDataImmediate(&LcECUCfgCheckBuff[0], CALIB_DATA_FLAG_ADDRESS, CALIB_DATA_FLAG_EEPROM_LEN);
}
/**********************************************************************
*   函数名称:   EOLEndECUConfigByteStatus
*
*   功能描述:
*
*   输入参考:   无
*
*   输出参数:   无
*
*   返 回 值:    无
*
*   其它说明: 
*             
*   修改日期        版本号       修改人       修改内容
*
**********************************************************************/
uint8 EOLEndECUConfigByteStatus(void)
{
#if (ECU_CONFIG_BYTE_CHECK_ENABLE == STD_ON)
    return GcEOLEndCfgFlg;
#else
    return VALID;
#endif

}


void DIDInit(void)
{
    VehicleTypeType Lu8VEH_Type = VEHTYPE_NOT_CFG;
    VehicleWheelType WheelType = VEH_WHEEL_20INCH;   /*默认20英寸全涂轮毂*/
    VehiclePlatformType LenuVehiclePlatform;
    DID_F1A1_ECUConfigByteFlag_Init();
#if(DIDTYPE == DIDTYPE_X02)
    if(0 == (DID_F1A1_Buff[1] & 0x1F))
    {
        Lu8VEH_Type = VEHTYPE_X01;
    }
    else if(1 == (DID_F1A1_Buff[1] & 0x1F))
    {
        Lu8VEH_Type = VEHTYPE_X02;
    }
    else if(2 == (DID_F1A1_Buff[1] & 0x1F))
    {
        Lu8VEH_Type = VEHTYPE_X03;
    }
    else if (3 == (DID_F1A1_Buff[1] & 0x1F))
    {
        Lu8VEH_Type = VEHTYPE_X04;
    }
    else/*默认为X01*/
    {
        Lu8VEH_Type = VEHTYPE_NOT_CFG;
    }
    LenuVehiclePlatform = VEH_X_SERIES;
#else
    if (0x10 == (DID_F1A1_Buff[1] & 0x1F))
    {
        Lu8VEH_Type = VEHTYPE_W01;
    }
    else if (0x11 == (DID_F1A1_Buff[1] & 0x1F))
    {
        Lu8VEH_Type = VEHTYPE_W02;
    }
    else if (0x12 == (DID_F1A1_Buff[1] & 0x1F))
    {
        Lu8VEH_Type = VEHTYPE_W03;
    }
    else if (0x13 == (DID_F1A1_Buff[1] & 0x1F))
    {
        Lu8VEH_Type = VEHTYPE_W04;
    }
    else if (0x14 == (DID_F1A1_Buff[1] & 0x1F))
    {
        Lu8VEH_Type = VEHTYPE_W05;
    }
    else
    {
        Lu8VEH_Type = VEHTYPE_NOT_CFG;
    }
    LenuVehiclePlatform = VEH_W_SERIES;
#endif
    WriteCAN_AppSignal_VEH_Type(Lu8VEH_Type);
    WriteCAN_AppSignal_VehiclePlatform(LenuVehiclePlatform);
    if ((0 == (DID_F1A1_Buff[3] & 0x0F))||(1 == (DID_F1A1_Buff[3] & 0x0F))||(0x08 == (DID_F1A1_Buff[3] & 0x0F))||(0x09 == (DID_F1A1_Buff[3] & 0x0F)))
    {
        WheelType = VEH_WHEEL_21INCH;   /*21英寸全涂轮毂*/
    }
    else if ((6 == (DID_F1A1_Buff[3] & 0x0F))||(7 == (DID_F1A1_Buff[3] & 0x0F)))
    {
        WheelType = VEH_WHEEL_18INCH;   /*18英寸精车轮毂*/
    }
    else//(((2 <= (DID_F1A1_Buff[3] & 0x0F)) && (5 >=(DID_F1A1_Buff[3] & 0x0F)))||((0x0A == (DID_F1A1_Buff[3] & 0x0F))|| (0x0B ==(DID_F1A1_Buff[3] & 0x0F))))或者预留都是20英寸
    {
        WheelType = VEH_WHEEL_20INCH;   /*20英寸全涂轮毂*/
    }
    WriteCAN_AppSignal_VehicleWheel(WheelType);

    VehicleCfgLevelType VehCfgLev = VEH_NO_MAX;
    /*判断车型等级配置，AK2平台该信息识别，由byte1，bit5~bit7改为识别byte2，bit0~bit1--20240130，Lynn*/
    if (0x01 == (DID_F1A1_Buff[2] & 0x03))
    {
        /*MAX*/
        VehCfgLev = VEH_MAX;
    }
    else
    {
        /*NMAX*/
        VehCfgLev = VEH_NO_MAX;
    }

    WriteCAN_AppSignal_VehicleCfgLevel(VehCfgLev);

    DID_IN_Boot_init(); 
}
/**********************************************************************
*   函数名称:   Updata_FEFE_UpdataLog
*
*   功能描述:  更新升级log
*
*   输入参考:   无
*
*   输出参数:   无
*
*   返 回 值:    无
*
*   其它说明:
*
*   修改日期        版本号       修改人       修改内容
*
**********************************************************************/
void Updata_F186_SessionMode(void)
{
    DID_F186_Buff[0] = GsUdsService.SessionMode;
}
/**********************************************************************
*   函数名称:   Updata_FEFE_UpdataLog
*
*   功能描述:  更新升级log
*
*   输入参考:   无
*
*   输出参数:   无
*
*   返 回 值:    无
*
*   其它说明:
*
*   修改日期        版本号       修改人       修改内容
*
**********************************************************************/
void Updata_FEFE_UpdataLog(void)
{
    EELReadData(&DID_FEFE_Buff[0], UPDATALOG_ADDRESS, EEL_DATA_SECTION_87_LEN);/*ECU功能配置*/
}
/**********************************************************************
*   函数名称:   Updata_F15F
*
*   功能描述:  更新boot状态
*
*   输入参考:   无
*
*   输出参数:   无
*
*   返 回 值:    无
*
*   其它说明:
*
*   修改日期        版本号       修改人       修改内容
*
**********************************************************************/
void Updata_F15F(void)
{
    uint8 Lu8Updata[4] = { 0 };
    uint32 Lu32SBLUpdataFlag = 0xFFFFFFFF;
    uint32 Lu32SBLBakFlag = 0xFFFFFFFF;
    uint32 Lu32APPValidFlag = 0xFFFFFFFF;
    uint32 Lu32SBLActiveFlag = 0xFFFFFFFF;
    /*更新boot升级参数*/
    DID_F15F_Buff[0] = 0x03;// Upgrade_Architecture BK-A : 3
    DID_F15F_Buff[1] = 0x02;//BootBCTL_Version updata 0x02
    DID_F15F_Buff[2] = 0x00;//BootBCTL_ActiveSlot 当前正在运行 A 分区 AppBoot 程序；
    EELReadData(&Lu8Updata[0], SBL_UPDATE_STATE_ADDRESS, SBL_UPDATE_STATE_LEN);/*读取之前的存储信息*/
    Lu32SBLUpdataFlag = (uint32)(((uint32)Lu8Updata[0]) | ((uint32)Lu8Updata[1] << 8) | ((uint32)Lu8Updata[2] << 16) | ((uint32)Lu8Updata[0] << 24));
    if (Lu32SBLUpdataFlag == SBL_UPDATE_NEED)
    {
        DID_F15F_Buff[3] = 0x02;//当前需要从 BK 分区搬运到运行分区
    }
    else
    {
        DID_F15F_Buff[3] = 0x00;//0x00: 启动时引导 A 分区 AppBoot;
    }
    DID_F15F_Buff[4] = 1;//0x01: 代表 A 分区有可以运行的 AppBoot 程序；能运行到这，说明A区
    EELReadData(&Lu8Updata[0], SBL_BAK_STATE_ADDRESS, SBL_BAK_STATE_LEN);/*读取之前的存储信息*/
    Lu32SBLBakFlag = (uint32)(((uint32)Lu8Updata[0]) | ((uint32)Lu8Updata[1] << 8) | ((uint32)Lu8Updata[2] << 16) | ((uint32)Lu8Updata[0] << 24));

    if (Lu32SBLBakFlag == SBL_BAK_VALID)
    {
        DID_F15F_Buff[5] = 0x01;//0x00: 启动时引导 A 分区 AppBoot;
    }
    else
    {
        DID_F15F_Buff[5] = 0x00;//当前需要从 BK 分区搬运到运行分区
    }
    EELReadData(&Lu8Updata[0], ACTIVE_NEW_SBL_ADDRESS, ACTIVE_NEW_SBL_LEN);/*读取之前的存储信息*/
    Lu32SBLActiveFlag = (uint32)(((uint32)Lu8Updata[0]) | ((uint32)Lu8Updata[1] << 8) | ((uint32)Lu8Updata[2] << 16) | ((uint32)Lu8Updata[0] << 24));

    if (Lu32SBLActiveFlag == ACTIVE_NEW_SBL_VALID)
    {
        DID_F15F_Buff[6] = 1;//0: 未使能激活新AppBoot流程;1: 使能激活新AppBoot流程;
    }
    else
    {
        DID_F15F_Buff[6] = 0;//0: 未使能激活新AppBoot流程;1: 使能激活新AppBoot流程;
    }

    DID_F15F_Buff[7] = 0;//BOOTBCTLbitstring0 0x00: 未使能激活新BOOT流程
    DID_F15F_Buff[8] = 0;//AppBCTLVersion  0x00: AppBCTL参数分区版本号，目前版本号为0x00
    DID_F15F_Buff[9] = 0;//AppBCTLActiveSlot 0x00: 当前正在运行 A 分区 App 程序
    DID_F15F_Buff[10] = 0;//AppBCTLBootMode 0x00: 启动时引导 A 分区 App
    EELReadData(&Lu8Updata[0], APP_JUMPBOOT_ADDRESS, APP_JUMPBOOT_LEN);/*读取之前的存储信息*/
    Lu32APPValidFlag = (uint32)(((uint32)Lu8Updata[0]) | ((uint32)Lu8Updata[1] << 8) | ((uint32)Lu8Updata[2] << 16) | ((uint32)Lu8Updata[0] << 24));

    if ((Lu32APPValidFlag == FBL_UPDATE_FLAG) || (Lu32APPValidFlag == FBL_APP_VALID))
    {
        DID_F15F_Buff[11] = 1;//AppBCTLBootableA 0x01: 代表 A 分区有可以运行的 App 程序；
    }
    else
    {
        DID_F15F_Buff[11] = 0;//AppBCTLBootableA 0x00: 代表 A 分区没有可以运行的 App 程序；
    }

    DID_F15F_Buff[12] = 0;//AppBCTLBootableB 0x00: 代表 B 分区没有可以运行的 App 程序， 一般 B 分区开始刷写前会设置为 0x00 
    DID_F15F_Buff[13] = 0;//Reserved
    DID_F15F_Buff[14] = 0;//Reserved
    DID_F15F_Buff[15] = 0;//Reserved
    DID_F15F_Buff[16] = 0;//Reserved
}

//
/**********************************************************************
*   函数名称:   Updata_F100_VehicleSpeedOut
*
*   功能描述:  更新F100车速信息
*
*   输入参考:   无
*
*   输出参数:   无
*
*   返 回 值:    无
*
*   其它说明:
*
*   修改日期        版本号       修改人       修改内容
*
**********************************************************************/
void Updata_F100_VehicleSpeedOut(void)
{
    Car_SpeedType LwVehicleSpeedTem;
    Car_SpeedVildType LvCar_SpeedVild;

    ReadSysSignal_Car_SpeedForSnapshotData(&LwVehicleSpeedTem);
    ReadSysSignal_Car_SpeedVild(&LvCar_SpeedVild);
    /*车速*/
    DID_F100_Buff[0] = (uint8)(LwVehicleSpeedTem >> 8);
    DID_F100_Buff[1] = (uint8)(LwVehicleSpeedTem);
    /*车速有效性*/
    DID_F100_Buff[2] = (uint8)(LvCar_SpeedVild);
}
/**********************************************************************
*   函数名称:   pdata_F101_OperatingVoltage
*
*   功能描述:  更新F101电压值（0.1V）
*
*   输入参考:   无
*
*   输出参数:   无
*
*   返 回 值:    无
*
*   其它说明:
*
*   修改日期        版本号       修改人       修改内容
*
**********************************************************************/
void Updata_F101_OperatingVoltage(void)
{
    uint8 Lu8VoltageValue;
    Lu8VoltageValue = ReadPwrMonitorSing_VoltageValue();

    if (Lu8VoltageValue < 30)
    {
        Lu8VoltageValue = 30;
    }
    else
    {
        Lu8VoltageValue -= 30;
    }
    /*工作电压*/
    DID_F101_Buff[0] = 0;
    DID_F101_Buff[1] = Lu8VoltageValue;
}
/**********************************************************************
*   函数名称:   Updata_F102_LowVoltagePowerModeStatus
*
*   功能描述:  更新F102电源模式
*
*   输入参考:   无
*
*   输出参数:   无
*
*   返 回 值:    无
*
*   其它说明:
*
*   修改日期        版本号       修改人       修改内容
*
**********************************************************************/
void Updata_F102_LowVoltagePowerModeStatus(void)
{
    LowVolPwrMdType LenuLowVolPwrMd = POWER_OFF;
    LowVolPwrMdFlagType LenuLowVolPwrMdFlag = LOCAL_MODE;
    ReadCAN_AppSignal_LowVolPwrMd(&LenuLowVolPwrMd);
    ReadCAN_AppSignal_LowVolPwrMdFlag(&LenuLowVolPwrMdFlag);
    /*电源模式*/
    DID_F102_Buff[0] = (uint8)((LenuLowVolPwrMd & 0x03) | (LenuLowVolPwrMdFlag << 2));
}

/**********************************************************************
*   函数名称:   Updata_F103_TotalOdometer
*
*   功能描述:  更新F103里程信息
*
*   输入参考:   无
*
*   输出参数:   无
*
*   返 回 值:    无
*
*   其它说明:
*
*   修改日期        版本号       修改人       修改内容
*
**********************************************************************/
void Updata_F103_TotalOdometer(void)
{
    uint32 LdwTotalDistanceTem;
    LdwTotalDistanceTem = (uint32)GsIntputParaStruct.Bytes.GdwVehTotDistanceVal;
    /*总里程*/
    DID_F103_Buff[0] = (uint8)(LdwTotalDistanceTem >> 16);
    DID_F103_Buff[1] = (uint8)(LdwTotalDistanceTem >> 8);
    DID_F103_Buff[2] = (uint8)(LdwTotalDistanceTem);
}
/**********************************************************************
*   函数名称:   Updata_F104_UTCTime
*
*   功能描述:  更新F104 UTC时间
*
*   输入参考:   无
*
*   输出参数:   无
*
*   返 回 值:    无
*
*   其它说明:
*
*   修改日期        版本号       修改人       修改内容
*
**********************************************************************/
void Updata_F104_UTCTime(void)
{
    /*时间*/
    DID_F104_Buff[0] = GsIntputParaStruct.Bytes.GsSettingTime.GcTimeset[0];
    DID_F104_Buff[1] = GsIntputParaStruct.Bytes.GsSettingTime.GcTimeset[1];
    DID_F104_Buff[2] = GsIntputParaStruct.Bytes.GsSettingTime.GcTimeset[2];
    DID_F104_Buff[3] = GsIntputParaStruct.Bytes.GsSettingTime.GcTimeset[3];
    DID_F104_Buff[4] = GsIntputParaStruct.Bytes.GsSettingTime.GcTimeset[4];
    DID_F104_Buff[5] = GsIntputParaStruct.Bytes.GsSettingTime.GcTimeset[5];
    /*“TimeValidFlg/时间有效性标志”和“EastOrWestTimeZone/东/西时区"的两个信号和DBC上的不一致，需要按照快照数据的定义进行转换*/
    DID_F104_Buff[6] = (GsIntputParaStruct.Bytes.GsSettingTime.Bits.TimeZoneNum << 4) | (GsIntputParaStruct.Bytes.GsSettingTime.Bits.TimeZone << 2) | (GsIntputParaStruct.Bytes.GsSettingTime.Bits.UTCTimeValidFlag);
}
/**********************************************************************
*   函数名称:   Updata_F105_ActualLogicGearShift
*
*   功能描述:  更新F105 UTC时间
*
*   输入参考:   无
*
*   输出参数:   无
*
*   返 回 值:    无
*
*   其它说明:
*
*   修改日期        版本号       修改人       修改内容
*
**********************************************************************/
void Updata_F105_ActualLogicGearShift(void)
{
    Car_GearType LvCar_Gear;
    ReadCAN_AppSignal_Car_Gear(&LvCar_Gear);
    /*时间*/
    DID_F105_Buff[0] = (uint8)LvCar_Gear;;
}

/**********************************************************************
*   函数名称:   Updata_F106_InternalDemFaultEventId
*
*   功能描述:  更新F106 内部故障状态
*
*   输入参考:   无
*
*   输出参数:   无
*
*   返 回 值:    无
*
*   其它说明:
*
*   修改日期        版本号       修改人       修改内容
*
**********************************************************************/
void Updata_F106_InternalDemFaultEventId(void)
{
    /*内部故障状态，预留*/
    DID_F106_Buff[0] = 0;
    DID_F106_Buff[1] = 0;

    DID_F106_Buff[2] = 0;
    DID_F106_Buff[3] = 0;

    DID_F106_Buff[4] = 0;
    DID_F106_Buff[5] = 0;

    DID_F106_Buff[6] = 0;
    DID_F106_Buff[7] = 0;

    DID_F106_Buff[8] = 0;
    DID_F106_Buff[9] = 0;
}
