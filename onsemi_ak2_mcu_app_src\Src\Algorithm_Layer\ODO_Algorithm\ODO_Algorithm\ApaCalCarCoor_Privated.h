/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * ApaCalCarCoor.c: 
 * Created on: 2020-12-07 19:09
 * Original designer: 22554
 ******************************************************************************/

#ifndef   __APACALCARCOOR_PRIVATED_H__
#define   __APACALCARCOOR_PRIVATED_H__

/******************************************************************************
 * Inclusion of private header files
 ******************************************************************************/
#include "ApaCalCarCoor_Types.h"
#include "ApaCalCarCoor_Cfg.h"
#include "ODO_CalibPara_Types.h"


/******************************************************************************
 * Compiler option used to distinguish inclusion done by the owner
 *****************************************************************************/
//#ifdef SYS_USE_APAWORKMANAGE
/**
 * \defgroup   APAWORKMANAGE_PRIVATED
 * @{
 */
#ifdef __cplusplus
extern "C"{
#endif
/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/






/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
extern void CPOS_Privated_Init(APA_Odom_CarParameterType *LvpCarParaData);
extern void CPOS_Privated_CalCurCarPointFunc(void);
extern uint8 CPOS_Privated_SetEnableCalFlg(void);
extern void CPOS_Privated_ClrEnableCalFlg(uint8 id);
extern void CPOS_Privated_DataUpdate(CPOS_CalPointNeedDataType *LvpCalPointNeedData);


/**
 * end of group   APAWORKMANAGE_PRIVATED
 * @}
 */
#ifdef __cplusplus
}
#endif


#endif /* end of __APAWORKMANAGE_PRIVATED_H__ */
