/*
 * DSI_SPI_Callback.h
 *
 *  Created on: 2021年3月23日
 *      Author: 6000021992
 */

#ifndef DSI_SPI_Callback_H_
#define DSI_SPI_Callback_H_

#include "DSI3_COM.h"

/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions *****************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/***************************** Symbol Definitions *****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Constants Declaration ****************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/*************************** Variables Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/


extern void DSI_Master0_DCR1B_Fall_Cbk(void);
extern void DSI_Master0_DCR2B_Fall_Cbk(void);
extern void DSI_Master1_DCR1B_Fall_Cbk(void);
extern void DSI_Master1_DCR2B_Fall_Cbk(void);

/******************************************************************************/
/**
 * @brief            DSI SPI数据处理
 * @param[in]        
 *
 * @return           无   
 */
/*****************************************************************************/
extern void DSI_SPI_DataHandle();
extern void DSIMaster0_SPI_CompleteTransfer_cbk(void);
extern void DSIMaster1_SPI_CompleteTransfer_cbk(void);


#endif /* DSI_SPI_Callback_H_ */
