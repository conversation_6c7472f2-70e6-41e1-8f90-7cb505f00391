/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * MEBSignalManage.h: 
 * Created on: 2021-03-18 14:27
 * Original designer: 22546
 ******************************************************************************/

#ifndef __POWERSIGNALMANAGE_H__
#define __POWERSIGNALMANAGE_H__

/******************************************************************************
 * Inclusion of other module header files
 *****************************************************************************/
#include "Power_ManageCfg.h"

/******************************************************************************
 * Compiler option used to distinguish inclusion done by the owner
 *****************************************************************************/
//#ifdef   SYS_USE_XXX

/******************************************************************************
 * Inclusion of private header files
 *****************************************************************************/

#ifdef __cplusplus
extern "C"{
#endif

/******************************************************************************/
/******************************************************************************/
/****************************** Macro Definitions ******************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************************
* 设计描述 : 断言
* 设计索引 : 
*******************************************************************************/
#if 0
    #define PowerManage_Assert(x) LhDevAssert(x)
#else
    #define PowerManage_Assert(x) ((void)0)
#endif

/******************************************************************************
 * Enum
 *****************************************************************************/

/******************************************************************************/
/******************************************************************************/
/****************************** Type Definitions ******************************/
/******************************************************************************/
/******************************************************************************/


/******************************************************************************/
/******************************************************************************/
/****************************** Extern Definitions *****************************/
/******************************************************************************/
/******************************************************************************/



/******************************************************************************/
/******************************************************************************/
/*************************** Functions Declaration ****************************/
/******************************************************************************/
/******************************************************************************/
/* 输出功能及信号接口 */
extern uint8 ReadPwrMonitorSing_VoltageValue(void);
extern uint16 ReadPwrMonitorSignal_ADMomentaryValue(void);
extern uint16 ReadSnsPwrADVal(PowerAdcIdx_e AdIdx);
extern void WritePwrManage_GroupStatus(Signal_PowerManageGroupType LenuGroup,Signal_VoltageStatusType *LenuVoltageStatus);
extern void ReadPwrManage_GroupStatus(Signal_PowerManageGroupType LenuGroup,Signal_VoltageStatusType *LenuVoltageStatus);
extern void WritePwrManage_SnsPwrDiagStatus(SnsPowerDiagIdx_e LenuIdx,SnsPwrDiagStatus_e LenuPwrSts);
extern void ReadPwrManage_SnsPwrDiagStatus(SnsPowerDiagIdx_e LenuIdx,SnsPwrDiagStatus_e *pLenuPwrSts);


#ifdef __cplusplus
}
#endif


#endif /* end of __MEBSIGNALMANAGE_H__ */


