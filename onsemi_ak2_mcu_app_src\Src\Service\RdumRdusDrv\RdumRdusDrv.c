/******************************************************************************
 * @file      RdumRdusDrv.c
 * @brief     安森美超声波探头驱动实现
 * <AUTHOR>
 * @date      2025-05-14
 * @note
 *****************************************************************************/

/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include <string.h>
#include "debug.h"
#include "RdumRdusDrv.h"
#include "SpiCom_Prg.h"
#include "BaseDrv.h"
#include "RdumRdusPageIndex.h"
#include "RdumRdusCrm.h"
#include "SpiCmd.h"
#include "SpiRespDef.h"

/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/
#define RDUM_RDUS_WAVE_PROCESS_PERIOD   100     /* 发波流程周期 */

/* 探头控制相关时间定义 */
#define RDUM_RDUS_SNS_WAIT_SELFTEST_TIME    1000    /* 等待自检完成时间 */
#define RDUM_RDUS_SNS_WAIT_MEAS_TIMEOUT     5000    /* 等待测量超时时间 */
#define RDUM_RDUS_SNS_TEMPCOMP_PERIOD       30000   /* 温度补偿周期 */
#define RDUM_RDUS_SNS_WAIT_STANDBY_TIME     100     /* 等待待机时间 */
#define RDUM_RDUS_SNS_WAIT_WAKEUP_TIME      100     /* 等待唤醒时间 */
#define RDUM_RDUS_SNS_DM_RETRY_CNT          3       /* 探头发现重试次数 */

/* DSI3相关定义 */
#define DSI3_ID_ALL                         0x0F    /* 广播某个通道的DSI3设备ID */

/* 测量模式定义 */
#define MEAS_MODE_NEAR_RANGE_2PERIOD        0x00    /* 近距离2周期测量模式 */

/******************************************************************************
 * @Type Definitions
 *****************************************************************************/
/* 探头发现序列枚举 */
typedef enum
{
    eRdumRdusDMSeq_Init = 0,
    eRdumRdusDMSeq_StartAutoDM,
    eRdumRdusDMSeq_WaitAutoDM,
    eRdumRdusDMSeq_PrepareNextDM,
    eRdumRdusDMSeq_End,
} RdumRdusDMSeq_en;

/* 初始化流程类型枚举 */
typedef enum
{
    RDUM_RDUS_INIT_TYPE_RDUM = 0, /* RDUM初始化 */
    RDUM_RDUS_INIT_TYPE_RDUS,     /* RDUS初始化 */
    RDUM_RDUS_INIT_TYPE_BOTH,     /* 两者都初始化 */
} RdumRdusInitType_t;

// /* 初始化流程状态枚举 */
// typedef enum
// {
//     RDUM_RDUS_INIT_PROCESS_IDLE = 0, /* 空闲 */
//     RDUM_RDUS_INIT_PROCESS_BUSY,     /* 忙 */
//     RDUM_RDUS_INIT_PROCESS_COMPLETE, /* 完成 */
//     RDUM_RDUS_INIT_PROCESS_ERROR,    /* 错误 */
// } RdumRdusInitProcessStatus_t;

// /* 发波流程状态枚举 */
// typedef enum
// {
//     RDUM_RDUS_WAVE_PROCESS_IDLE = 0, /* 空闲 */
//     RDUM_RDUS_WAVE_PROCESS_BUSY,     /* 忙 */
//     RDUM_RDUS_WAVE_PROCESS_COMPLETE, /* 完成 */
//     RDUM_RDUS_WAVE_PROCESS_ERROR,    /* 错误 */
// } RdumRdusWaveProcessStatus_t;

/* 探头控制组状态 */
typedef enum
{
    RdumRdusGroupStatus_SNS_DM = 0,         /* 探头发现模式 */
    RdumRdusGroupStatus_SNS_SELFTEST,       /* 探头自检 */
    RdumRdusGroupStatus_START_MEAS,         /* 开始测量 */
    RdumRdusGroupStatus_WAIT_MEAS_COMPLETE, /* 等待测量完成 */
    RdumRdusGroupStatus_STANDBY,            /* 待机模式 */
    RdumRdusGroupStatus_WAKEUP,             /* 唤醒模式 */
    RdumRdusGroupStatus_STOP_MEAS,          /* 停止测量 */
    RdumRdusGroupStatus_WAIT_STOP_MEAS,     /* 等待停止测量 */
    RdumRdusGroupStatus_WAIT_STANDBY,       /* 等待进入待机 */
    RdumRdusGroupStatus_WAIT_WAKEUP,        /* 等待唤醒完成 */
    RdumRdusGroupStatus_SNS_PARAM_CFG,      /* 探头参数配置 */
    RdumRdusGroupStatus_WAIT_SNS_PARAM_CFG, /* 等待探头参数配置 */
    RdumRdusGroupStatus_WAIT_SELFTEST,      /* 等待自检完成 */
    RdumRdusGroupStatus_WAIT_TEMP_COMP,     /* 等待温度补偿 */
    RdumRdusGroupStatus_TEMP_COMP,          /* 温度补偿 */
    RdumRdusGroupStatus_WAIT_REDM,          /* 等待重新发现探头 */
    RdumRdusGroupStatus_REDM,               /* 重新发现探头 */
} RdumRdusGroupStatus_en;

/* 探头测量状态 */
typedef enum
{
    RdumRdusGroupMeas_IDLE = 0,             /* 空闲 */
    RdumRdusGroupMeas_BUSY_MEAS,            /* 忙于测量 */
} RdumRdusGroupMeasStatus_en;

/* 探头控制组配置 */
typedef struct
{
    RdumRdusGroupStatus_en GroupStatus;          /* 控制组状态 */
    RdumRdusGroupStatus_en NextGroupStatus;      /* 下一个状态 */
    uint32 Time1msCnt;                           /* 时间计数器 */
    uint32 TempCompTime1msCnt;                   /* 温度补偿时间计数器 */
    uint8 StartMeasFlg;                          /* 开始测量标志 */
    RdumRdusGroupMeasStatus_en SnsGroupMeasStatus[DSI3_CHANNEL_NUM]; /* 测量状态 */
    uint32 SnsGroupMeasTime[DSI3_CHANNEL_NUM];                       /* 测量时间 */
} RdumRdusGroup_Cfg;

/* 驱动控制结构体 */
typedef struct
{
    RdumRdusDrv_Status_t Status;     /* 驱动状态 */
    uint8 WaveProcessFlag;           /* 发波流程标志 */
    uint8 InitProcessFlag;           /* 初始化流程标志 */
} RdumRdusDrv_Control_t;

/* SLOT回波数据的诊断数据联合体定义 */
typedef union
{
    uint16 value; /* 16位原始值 */
    uint8 Bytes[2];

    /* 诊断字0位域定义 */
    struct
    {
        uint8 cwNoiseValueH_H : 2;         /* [9:8]连续波噪声值H的高位 */
        uint8 safetyFlags : 1;           /* [10]安全标志 */
        uint8 txOverOrUnderVoltage : 1;  /* [11]TX电压异常 */
        uint8 wbNoiseAboveThreshold : 1; /* [12]宽带噪声 */
        uint8 measModeCrcError : 1;      /* [13]CRC错误 */
        uint8 thermalWarning : 1;        /* [14]热警告 */
        uint8 thermalShutdown : 1;       /* [15]热关断 */

        uint8 reserved : 2;             /* [1:0]保留位 */
        uint8 cwNoiseValueL : 4;        /* [5:2]连续波噪声值L */
        uint8 cwNoiseValueH_L : 2;       /* [7:6]连续波噪声值H的低位 */
    } diagWord0;

    /* 诊断字1位域定义 */
    struct
    {
        uint8 reverbPeriod_H : 8; /* [15:8]余震周期高位(1 LSB = 25 ns) */

        uint8 reverbTime : 6;     /* [5:0]余震时间(1 LSB = 51.2 us) */
        uint8 reverbPeriod_L : 2; /* [7:6]余震周期低位(1 LSB = 25 ns) */
    } diagWord1;

    /* 诊断字2位域定义 */
    struct
    {
        uint8 dieTemp : 8;              /* [15:8]温度传感器温度 */

        uint8 freqTrimActual : 7;       /* [6:0]实际OSC微调代码 */
        uint8 txOverOrUnderVoltage : 1; /* [7]TX电压异常 */
    } diagWord2;
} DiagWord_t;

/******************************************************************************
 * @Global Variables
 *****************************************************************************/
static RdumRdusDrv_Control_t RdumRdusDrvCtrl;
static WaveProcessControl_t WaveProcessCtrl;

/* 探头控制组 */
static RdumRdusGroup_Cfg RdumRdusGroup;

/* 探头发现状态机 */
static RdumRdusDMSeq_en RdumRdusDMSeq = eRdumRdusDMSeq_Init;

/* 探头发现重试计数 */
static uint8 DMRetryCnt = RDUM_RDUS_SNS_DM_RETRY_CNT;

/* 待机标志 */
static uint8 StandbyFlg = 0;

/* 探头发现完成标志 */
static uint8 DMDone = 0;

/******************************************************************************
 * @Function Declaration
 *****************************************************************************/
/* 内部函数声明 */
static void RdumRdusDrv_InitSnsCtrlGroup(void);
static uint8 RdumRdusDrv_DiscoveryMode(void);
static RdumRdusGroupStatus_en RdumRdusDrv_GetNextSnsCtrlGroupStatus(void);
static void RdumRdusDrv_UpdateTimeCnt(void);
static SpiCallbackReturn_t ReadCommandCallback(SpiCom_t *SpiComData);
static SpiCallbackReturn_t NonReadCommandCallback(SpiCom_t *SpiComData);

/* 模拟DSI3命令函数 */
static uint8 Dsi3Cmd_InitRdumRdus(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id);
static uint8 Dsi3Cmd_RdumRdusStartMeasurement(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, uint8 MeasMode);
static uint8 Dsi3Cmd_RdumRdusIcModeSet(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, uint8 IcMode);
static uint8 Dsi3Cmd_RdumRdusReadStatus(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, uint8 StatusAddr);

/******************************************************************************
 * @Function Implementation
 *****************************************************************************/

/******************************************************************************
 * @brief      打印所有解压缩后的包络数组
 * <AUTHOR>
 * @date       2025-01-27 10:00:00
 * @note       打印WaveProcessCtrl.DecompressedEnvelopes数组的所有内容
 *****************************************************************************/
void RdumRdusWaveProcess_PrintDecompressedEnvelopes(void)
{
    uint8 slot_idx, brc_idx, point_idx;

    /* 检查是否有有效数据 */
    if (WaveProcessCtrl.Status != WAVE_PROCESS_COMPLETE)
    {
        PRINTF_RDUM(0, "WaveProcess", "解压缩数据尚未完成，当前状态: %d\n", WaveProcessCtrl.Status);
        return;
    }

    PRINTF_RDUM(0, "WaveProcess", "开始打印解压缩包络数据...\n");
    PRINTF_RDUM(0, "WaveProcess", "BRC数量: %d, 数组维度: [%d][%d][%d]\n",
              WaveProcessCtrl.BrcCount, MAX_SLOT_COUNT,
              WAVE_PROCESS_BRC_COUNT * ENVELOPE_GROUPS, ENVELOPE_POINTS_PER_GROUP);

    /* 遍历所有slot */
    for (slot_idx = 0; slot_idx < WaveProcessCtrl.BrcResults[0].SlotCount; slot_idx++)
    {
        PRINTF_RDUM(0, "WaveProcess", "=== SLOT %d ===\n", slot_idx);

        /* 遍历所有BRC组 */
        for (brc_idx = 0; brc_idx < WaveProcessCtrl.BrcCount; brc_idx++)
        {
            PRINTF_RDUM(0, "WaveProcess", "  BRC组 %d: ", brc_idx);

            /* 打印该组的所有点位 */
            for (point_idx = 0; point_idx < ENVELOPE_POINTS_PER_GROUP; point_idx++)
            {
                if (point_idx == 0)
                {
                    PRINTF_RDUM(0, "WaveProcess", "%04X", WaveProcessCtrl.DecompressedEnvelopes[slot_idx][brc_idx][point_idx]);
                }
                else
                {
                    PRINTF_RDUM(0, "WaveProcess", " %04X", WaveProcessCtrl.DecompressedEnvelopes[slot_idx][brc_idx][point_idx]);
                }
            }
            PRINTF_RDUM(0, "WaveProcess", "\n");
        }
        PRINTF_RDUM(0, "WaveProcess", "\n");
    }

    PRINTF_RDUM(0, "WaveProcess", "解压缩包络数据打印完成\n");
}

/******************************************************************************
 * @brief      解码BRC结果
 * @param[in,out] Result BRC结果
 * @return     BRC状态
 * <AUTHOR>
 * @date       2025-05-16
 * @note
 *****************************************************************************/
BrcStatus_t RdumRdusBrc_DecodeBrcResult(BrcResult_t *Result)
{
    uint8 i, j;
    uint8 DecodedData[SLOT_PACKAGE_SIZE] = {0};
    uint8 RetVal;

    /* 参数检查 */
    if (Result == NULL)
    {
        return BRC_STATUS_INVALID;
    }

    /* 解码每个slot */
    for (i = 0; i < Result->SlotCount; i++)
    {
        /* 解码slot数据 */
        RetVal = BrcSlotDef_DecodeSlot(i, (uint8 *)&Result->SlotData[i], DecodedData);
        if (RetVal != 0)
        {
            Result->Status = SLOT_STATUS_ERROR;
            PRINTF_RDUM("解码slot%d数据失败！\n", i);
            return BRC_STATUS_ERROR;
        }

#ifdef DEBUG_RDUM_RDUS
        PRINTF_RDUM("解码后slot%d数据:\n", i);
        for (j = 0; j < SLOT_PACKAGE_SIZE; j++)
        {
            PRINTF_RDUM("0x%02X ", DecodedData[j]);
        }
        PRINTF_RDUM("\n");
#endif

        /* 把解码后数据存入slot数据 */
        memcpy((uint8 *)&Result->SlotData[i], DecodedData, SLOT_PACKAGE_SIZE);
        // Result->SlotData[i].Kac = DecodedData[0] >> 4;
        // Result->SlotData[i].Status = DecodedData[0] & 0x0F;
        // Result->SlotData[i].SlotCrc = DecodedData[SLOT_PACKAGE_SIZE - 1];
        if (Result->SlotData[i].Status != 0)
        {
            PRINTF_RDUM("slot%d状态错误！\n", i);
            return BRC_STATUS_ERROR;
        }

        /* 解压缩包络点位(这里不解压缩，需要后面接收所有数据后再解压缩) */
        // RetVal = BrcSlotDef_DecompressEnvelope(&DecodedData[1], &Result->DecompressedEnvelopes[i]);
        // if (RetVal != 0)
        // {
        //     Result->Status = BRC_STATUS_ERROR;
        //     return BRC_STATUS_ERROR;
        // }
    }

    return BRC_STATUS_OK;
}

/******************************************************************************
 * @brief      RDUM SPI命令回调函数
 * @param[in]  SpiComData SPI命令数据
 * @param[in]  Status 传输状态
 * <AUTHOR>
 * @date       2025-05-20
 * @note       处理RDUM SPI命令回调
 *****************************************************************************/
static SpiCallbackReturn_t RdumRdus_SpiCmdCallback(SpiCom_t *SpiComData)
{
    SpiDataBuffer_t *DataBuffer = SpiComData->DataBuffer;
    uint8 *RxData = DataBuffer->RxBuff;
    uint8 DataLens = DataBuffer->DataLens;
    SpiProtocol_t *SendCmdProtocol = (SpiProtocol_t *)DataBuffer->TxBuff;
    uint8 Cmd = SendCmdProtocol->ReadAndWrite_t.Command;
    SpiCallbackReturn_t result = SPI_CALLBACK_OK;
    uint8 CrmRespData[CRM_MAX_DATA_SIZE] = {0};
    RdumRdusPageIndex_u *PageIndexData;
    RdumRdusPageIndex_t PageIndex = {0};
    uint8 AllSlotDuration[2 * MAX_SLOT_COUNT] = {0};
    // 所有压缩前的数据临时存储
    uint8 AllUncompressedData[MAX_SLOT_COUNT][WAVE_PROCESS_BRC_COUNT][ENVELOPE_DATA_SIZE] = {0};
    DiagWord_t DiagWord1 = {0}, DiagWord2 = {0}, DiagWord3 = {0};
    uint8 Total4BytePackageCount = 0;
    uint8 i, j, k;

    if (SpiComData->TxStatus == SPI_TX_TIMEOUT)
    {
        /* 处理超时(结束当前操作，返回同时超时错误) */

        return SPI_CALLBACK_ABORT;
    }

    switch (Cmd)
    {
    case NO_OPERATION:
        result = ReadCommandCallback(SpiComData);
        break;

    case READ_BY_ADDRESS:
        result = NonReadCommandCallback(SpiComData);
        break;

    case WRITE_BY_ADDRESS: // 需要中断及时响应，判断状态
        result = NonReadCommandCallback(SpiComData);
        break;

    case RUN_PAUSE_CYCLE:
        result = NonReadCommandCallback(SpiComData);
        break;

    case GET_CRM_RESP:
        result = NonReadCommandCallback(SpiComData);
        if (result == SPI_CALLBACK_OK)
        {
            /* 处理CRM响应数据，根据前面命令配置的页面信息解析数据，可读取多个页面索引其中一个页面索引2个字节，最多可连续读取20字节 */
            /* 例如：读取IC制造商ID---PAGE_0 PAGE0_IDX_ICMFGID*/
            memcpy(CrmRespData, (RxData + SPI_CMD_LENS), DataBuffer->DataLens - SPI_CMD_LENS);
            switch (PageIndex.Page)
            {
            case PAGE_0:
                switch (PageIndex.Index)
                {
                case PAGE0_IDX_ICMFGID:
                    PageIndexData = (RdumRdusPageIndex_u *)CrmRespData;
                    if (PageIndexData->page0_idx_icmfgid_t.icmfgid == 0)
                    {
                        /* 处理IC制造商ID */
                    }
                    break;

                default:
                    break;
                }
                break;

            default:
                break;
            }
        }

        break;

    case GET_PDCM_RESP:
        result = NonReadCommandCallback(SpiComData);
        if (result == SPI_CALLBACK_OK)
        {
            /* 处理PDCM响应数据 */
        }
        break;

    case GET_ALL_BRC:
        result = NonReadCommandCallback(SpiComData);
        if (result == SPI_CALLBACK_OK)
        {
            /* 处理BRC响应数据 */
            WaveProcessCtrl.BrcResults[WaveProcessCtrl.BrcIndex].SlotCount = (DataLens - SPI_CMD_LENS) / SLOT_DATA_SIZE;
            WaveProcessCtrl.BrcResults[WaveProcessCtrl.BrcIndex].SlotSDCrc = RxData[DataLens - 1];

            for (i = 0; i < WaveProcessCtrl.BrcResults[WaveProcessCtrl.BrcIndex].SlotCount; i++)
            {
                memcpy(&WaveProcessCtrl.BrcResults[WaveProcessCtrl.BrcIndex].SlotData[i], &RxData[SPI_CMD_LENS + i * SLOT_DATA_SIZE], SLOT_DATA_SIZE);
                AllSlotDuration[i * 2] = WaveProcessCtrl.BrcResults[WaveProcessCtrl.BrcIndex].SlotData[i].SlotDuration[0];
                AllSlotDuration[i * 2 + 1] = WaveProcessCtrl.BrcResults[WaveProcessCtrl.BrcIndex].SlotData[i].SlotDuration[1];
            }
            if (WaveProcessCtrl.BrcResults[WaveProcessCtrl.BrcIndex].SlotSDCrc != BaseDrv_Crc8Calculate(AllSlotDuration, WaveProcessCtrl.BrcResults[WaveProcessCtrl.BrcIndex].SlotCount * 2, CRC8_C2_INIT_SPI_AND_CRC_SD))
            {
                /* CRC校验失败 */
                WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
                return SPI_CALLBACK_ABORT;
            }
            /* 解码BRC结果并且校验 */
            if (RdumRdusBrc_DecodeBrcResult(&WaveProcessCtrl.BrcResults[WaveProcessCtrl.BrcIndex]))
            {
                /* 解码失败 */
                WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
                return SPI_CALLBACK_ABORT;
            }
            /* 判断KAC */
            if (WaveProcessCtrl.BrcResults[WaveProcessCtrl.BrcIndex].SlotData[i].Kac != (((i + 1) * 2) % 16))
            {
                /* KAC错误 */
                WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
                return SPI_CALLBACK_ABORT;
            }
            WaveProcessCtrl.BrcIndex++;
            if (WaveProcessCtrl.BrcIndex == WaveProcessCtrl.BrcCount)
            {
                /* 所有回波包络获取完成 */
                WaveProcessCtrl.CurrentStep = WAVE_STEP_COMPLETE;
                WaveProcessCtrl.Status = WAVE_PROCESS_COMPLETE;

                /* 处理回波包络数据,把BrcResults中所有18字节的SlotData的值合并到一个数组中 */
                for (i = 0; i < WaveProcessCtrl.BrcCount; i++)
                {
                    for (j = 0; j < WaveProcessCtrl.BrcResults[i].SlotCount; j++)
                    {
                        memcpy(&AllUncompressedData[j][i], WaveProcessCtrl.BrcResults[i].SlotData[j].SlotData, ENVELOPE_DATA_SIZE);
                    }
                }

                /* 计算出需要取出压缩的4字节包有多少个(测量持续时间配置值meas_duration*512us发波时间+7373us噪声监测和发波的总时间) */
                Total4BytePackageCount = (uint8)((60 * 512 + 7373) / POINT_INTERVAL_US / 9 + 1); // 小数位时舍去、还是四舍五入、还是+1？？？

                /* 以上数据中前2个字节为诊断码1，后面每4个字节为一个压缩包，最后4个字节为诊断码2和诊断码3 */
                for (i = 0; i < WaveProcessCtrl.BrcCount; i++)
                {
                    for (j = 0; j < WaveProcessCtrl.BrcResults[i].SlotCount; j++)
                    {
                        DiagWord1.value = (uint16)(AllUncompressedData[j][i][0] << 8 | AllUncompressedData[j][i][1]);
                        /*接下来每4个字节为一个压缩包，进行解压缩*/
                        for (k = 0; k < ENVELOPE_GROUPS; k++)
                        {
                            BrcSlotDef_DecompressSingleEnvelope(AllUncompressedData[j][i][k * ENVELOPE_GROUPS + 2] << 24 | AllUncompressedData[j][i][k * ENVELOPE_GROUPS + 3] << 16 | AllUncompressedData[j][i][k * ENVELOPE_GROUPS + 4] << 8 | AllUncompressedData[j][i][k * ENVELOPE_GROUPS + 5],
                                                                (EnvelopeGroup_t *)&WaveProcessCtrl.DecompressedEnvelopes[j][i]);

                            if (j * ENVELOPE_GROUPS + k == Total4BytePackageCount)
                            {
                                /* 已经解压完所有需要的包络数据,取最后四个字节的诊断码 */
                                DiagWord2.value = (uint16)(AllUncompressedData[j][i][ENVELOPE_DATA_SIZE - 2] << 8 | AllUncompressedData[j][i][ENVELOPE_DATA_SIZE - 1]);
                                DiagWord3.value = (uint16)(AllUncompressedData[j][i][ENVELOPE_DATA_SIZE - 4] << 8 | AllUncompressedData[j][i][ENVELOPE_DATA_SIZE - 3]);
                                break;
                            }
                        }
                    }
                }

                /* 打印所有的解压结果数组WaveProcessCtrl.DecompressedEnvelopes */
                RdumRdusWaveProcess_PrintDecompressedEnvelopes();
            }
        }
        break;

    default:
        break;
    }

    return result;
}

/******************************************************************************
 * @brief      获取发波流程状态
 * @return     发波流程状态
 * <AUTHOR>
 * @date       2025-04-04 10:15:00
 * @note       获取当前发波流程状态
 *****************************************************************************/
WaveProcessStatus_t RdumRdusWaveProcess_GetStatus(void)
{
    return WaveProcessCtrl.Status;
}

/******************************************************************************
 * @brief      初始化流程模块初始化
 * <AUTHOR>
 * @date       2025-05-20
 * @note       初始化流程控制结构体
 *****************************************************************************/
void RdumRdusInitProcess_Init(void)
{
    // /* 初始化流程控制结构体 */
    // memset(&InitProcessCtrl, 0, sizeof(InitProcessControl_t));
    // InitProcessCtrl.Status = RDUM_RDUS_INIT_PROCESS_IDLE;

    // /* 重置步骤表指针 */
    // CurrentStepTable = NULL;
    // CurrentStepTableSize = 0;
}

/******************************************************************************
 * @brief      初始化发波流程
 * <AUTHOR>
 * @date       2025-04-04 10:00:00
 * @note       初始化发波流程控制结构体
 *****************************************************************************/
void RdumRdusWaveProcess_Init(void)
{
    /* 初始化发波流程控制结构体 */
    memset(&WaveProcessCtrl, 0, sizeof(WaveProcessControl_t));
    WaveProcessCtrl.CurrentStep = WAVE_STEP_CONFIG_MODE;
    WaveProcessCtrl.Status = WAVE_PROCESS_IDLE;
}

/******************************************************************************
 * @brief      初始化驱动
 * <AUTHOR>
 * @date       2025-05-14
 * @note
 *****************************************************************************/
void RdumRdusDrv_Init(void)
{
    /* 初始化驱动控制结构体 */
    memset(&RdumRdusDrvCtrl, 0, sizeof(RdumRdusDrv_Control_t));
    RdumRdusDrvCtrl.Status = RDUM_RDUS_DRV_WAVE_PROCESS;

    /* 初始化SPI通信 */
    SpiCom_Init(&RdumRdus_SpiCmdCallback);

    /* 初始化探头控制组 */
    // RdumRdusDrv_InitSnsCtrlGroup();

    /* 初始化初始化流程 */
    RdumRdusInitProcess_Init();

    /* 初始化发波流程 */
    RdumRdusWaveProcess_Init();

    /* 启动初始化流程 */
    // if (RdumRdusInitProcess_Start(RDUM_RDUS_INIT_TYPE_BOTH))
    // {
    //     PRINTF_RDUM(0, "RdumRdusDrv", "初始化流程启动成功\n");
    //     RdumRdusDrvCtrl.InitProcessFlag = 1;
    // }
    // else
    // {
    //     PRINTF_RDUM(0, "RdumRdusDrv", "初始化流程启动失败\n");
    //     RdumRdusDrvCtrl.Status = RDUM_RDUS_DRV_ERROR;
    // }
}

/******************************************************************************
 * @brief      处理发波流程
 * <AUTHOR>
 * @date       2025-04-04 10:10:00
 * @note       周期性调用此函数处理发波流程
 *****************************************************************************/
void RdumRdusWaveProcess_Process(void)
{
    RdumRdusCrm_MeasureFrame_t Frame = {0};
    SpiPayloadData_t Payload  = {0};
    uint8 i, DelayMs;
    uint16 Temp;

    switch (WaveProcessCtrl.CurrentStep)
    {
    case WAVE_STEP_CONFIG_MODE:
        Frame.Bit.Sid = DSI3_ID_ALL;
        Frame.Bit.Mode = MEAS_MODE_NEAR_RANGE_2PERIOD & 0x07;
        // 0为主发，1为左侦听，2为右侦听，3为不动作(最多只支持同时8个通道主发和侦听数据)
        Frame.Bit.SID1_BEH_CH_H = 0x01;
        Frame.Bit.SID1_BEH_CH_L = 0x03;
        Frame.Bit.SID2_BEH_CH_H = 0x02;
        Frame.Bit.SID2_BEH_CH_L = 0x01;
        Frame.Bit.SID3_BEH_CH_H = 0x00;
        Frame.Bit.SID3_BEH_CH_L = 0x02;
        Frame.Bit.SID4_BEH_CH_H = 0x03;
        Frame.Bit.SID4_BEH_CH_L = 0x00;
        Frame.Bit.SID5_BEH_CH_H = 0x01;
        Frame.Bit.SID5_BEH_CH_L = 0x03;
        Frame.Bit.SID6_BEH_CH_H = 0x02;
        Frame.Bit.SID6_BEH_CH_L = 0x03;
        Frame.Bit.REV_SH_ENA = 0x01;
        Frame.Bit.DOPPLER_RX_INNER = 0x01;
        Frame.Bit.DOPPLER_TX_SIDE = 0x01;

        if (RdumRdusCrm_StartMeasurement(DSI3_CHANNEL_A, &Frame))
        {
            WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
            break;
        }
        else
        {
            WaveProcessCtrl.CurrentStep = WAVE_STEP_CONFIG_PARAM;
        }
        break;

    case WAVE_STEP_CONFIG_PARAM:
        /* 配置寄存器9: WRITE_BYADDR 02 09 00 00 00 00 6F */
        Payload.Addr0x09.N_FRAMES_MSB = 0x00;
        if (SpiCmd_WriteByAddress(DSI3_CHANNEL_A, 0x09, (uint8 *)Payload.Bytes))
        {
            WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
            break;
        }

        /* 配置寄存器2: WRITE_BYADDR 02 02 37 F1 00 1A C7 */
        memset(&Payload, 0, sizeof(SpiPayloadData_t));
        Payload.Addr0x02.BURST_ENA = 0x01;
        Payload.Addr0x02.BUS_DRV_ENA = 0x01;
        Payload.Addr0x02.BRC_INT_MASK_H = 0x07;
        Payload.Addr0x02.BRC_INT_MASK_L = 0x0F;
        Payload.Addr0x02.PDCM_NUM_SLOTS = 0x01;
        Payload.Addr0x02.N_FRAMES = 0x0A;
        if (SpiCmd_WriteByAddress(DSI3_CHANNEL_A, SPI_CMD_ADDR_R2, Payload.Bytes))
        {
            WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
            break;
        }
        else
        {
            WaveProcessCtrl.CurrentStep = WAVE_STEP_READ_STATUS;
        }
        break;

    case WAVE_STEP_READ_STATUS:
        /* 读取状态寄存器: READ_BYADDR 03 12 00 00 00 00 30 */
        if (SpiCmd_ReadByAddress(DSI3_CHANNEL_A, SPI_CMD_ADDR_STATUS))
        {
            WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
            break;
        }

        /* 获取状态寄存器值: NO_OPERATION 01 12 00 00 00 00 98 */
        if (SpiCmd_NoOperation(DSI3_CHANNEL_A, SPI_CMD_ADDR_STATUS, WaveProcessCtrl.RegValues))
        {
            WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
            break;
        }
        else
        {
            WaveProcessCtrl.CurrentStep = WAVE_STEP_GET_PARAM;
        }
        break;
    case WAVE_STEP_GET_PARAM:
        /* 读取寄存器09: READ_BYADDR 03 09 00 00 00 00 CA */
        if (SpiCmd_ReadByAddress(DSI3_CHANNEL_A, SPI_CMD_ADDR_AOD))
        {
            WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
            break;
        }

        /* 获取寄存器09值: NO_OPERATION 01 09 00 00 00 00 60 */
        if (SpiCmd_NoOperation(DSI3_CHANNEL_A, SPI_CMD_ADDR_AOD, WaveProcessCtrl.RegValues))
        {
            WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
            break;
        }

        /* 读取寄存器02: READ_BYADDR 03 02 00 00 00 00 E9 */
        if (SpiCmd_ReadByAddress(DSI3_CHANNEL_A, SPI_CMD_ADDR_R2))
        {
            WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
            break;
        }

        /* 获取寄存器02值: NO_OPERATION 01 02 00 00 00 00 60 */
        if (SpiCmd_NoOperation(DSI3_CHANNEL_A, SPI_CMD_ADDR_R2, WaveProcessCtrl.RegValues))
        {
            WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
            break;
        }
        else
        {
            WaveProcessCtrl.CurrentStep = WAVE_STEP_CONFIG_PARAM1;
        }
        break;

    case WAVE_STEP_CONFIG_PARAM1:
        /* 配置寄存器9: WRITE_BYADDR 02 09 00 00 00 00 6F */
        memset(&Payload, 0, sizeof(SpiPayloadData_t));
        Payload.Addr0x09.N_FRAMES_MSB = 0x00;
        if (SpiCmd_WriteByAddress(DSI3_CHANNEL_A, SPI_CMD_ADDR_AOD, (uint8 *)Payload.Bytes))
        {
            WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
            break;
        }

        /* 配置寄存器02: WRITE_BYADDR 02 02 B7 F1 00 1A 06 */
        memset(&Payload, 0, sizeof(SpiPayloadData_t));
        Payload.Addr0x02.TX_START = 0x01;
        Payload.Addr0x02.BURST_ENA = 0x01;
        Payload.Addr0x02.BUS_DRV_ENA = 0x01;
        Payload.Addr0x02.BRC_INT_MASK_H = 0x07;
        Payload.Addr0x02.BRC_INT_MASK_L = 0x0F;
        Payload.Addr0x02.PDCM_NUM_SLOTS = 0x01;
        Payload.Addr0x02.N_FRAMES = 0x0A;
        if (SpiCmd_WriteByAddress(DSI3_CHANNEL_A, SPI_CMD_ADDR_R2, (uint8 *)Payload.Bytes))
        {
            WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
            break;
        }
        else
        {
            WaveProcessCtrl.CurrentStep = WAVE_STEP_CONFIG_PARAM2;
        }
        break;

    case WAVE_STEP_CONFIG_PARAM2:
        /* 配置寄存器9: WRITE_BYADDR 02 09 00 00 00 00 6F */
        memset(&Payload, 0, sizeof(SpiPayloadData_t));
        Payload.Addr0x09.N_FRAMES_MSB = 0x00;
        if (SpiCmd_WriteByAddress(DSI3_CHANNEL_A, SPI_CMD_ADDR_AOD, (uint8 *)Payload.Bytes))
        {
            WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
            break;
        }

        /* 配置寄存器02: WRITE_BYADDR 02 02 D7 F1 13 9C 2D */
        memset(&Payload, 0, sizeof(SpiPayloadData_t));
        Payload.Addr0x02.TX_START = 0x01;
        Payload.Addr0x02.BRC_ENA = 0x01;
        Payload.Addr0x02.BUS_DRV_ENA = 0x01;
        Payload.Addr0x02.BRC_INT_MASK_H = 0x07;
        Payload.Addr0x02.BRC_INT_MASK_L = 0x0F;
        Payload.Addr0x02.PDCM_NUM_SLOTS = 0x01; // 单次GET_ALL_BRC需要接收几个slot插槽，PDCM_NUM_SLOTS + 1  表示实际接收的slot插槽数量
        Temp = 39;
        Payload.Addr0x02.TIMER_H = Temp >> 1;            // 当和TX_START=1和BRC_ENA=1一起时用于设置GET_ALL_BRC间隔读取的值，= 39/10 = 3.9ms
        Payload.Addr0x02.TIMER_L = Temp & 0x01;
        Payload.Addr0x02.N_FRAMES = 0x0C;       // 根据发波的周期确定需要收几次slot包才可以获取所有的回波包络
        WaveProcessCtrl.BrcCount = Payload.Addr0x02.N_FRAMES;
        if (SpiCmd_WriteByAddress(DSI3_CHANNEL_A, SPI_CMD_ADDR_R2, (uint8 *)Payload.Bytes))
        {
            WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
            break;
        }
        else
        {
            WaveProcessCtrl.CurrentStep = WAVE_STEP_GET_BRC;
        }
        break;

    case WAVE_STEP_GET_BRC:
        /* 获取回波包络: GET_ALL_BRC */
        for (i = 0; i < Payload.Addr0x02.N_FRAMES; i++)
        {
            if (i == 0)
            {
                /* 根据发波模式不同，第一次延时时间不一样，为了通用适配通用使用6ms */
                DelayMs = 6;
            }
            else
            {
                DelayMs = ( Payload.Addr0x02.TIMER_H << 1 | Payload.Addr0x02.TIMER_L ) / 10 + 1;
            }
            if (SpiCmd_GetAllBrc(DSI3_CHANNEL_A, Payload.Addr0x02.PDCM_NUM_SLOTS + 1, DelayMs, NULL))
            {
                WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
                break;
            }
        }

        WaveProcessCtrl.CurrentStep = WAVE_STEP_COMPLETE;
        WaveProcessCtrl.Status = WAVE_PROCESS_COMPLETE;
        break;

    default:
        WaveProcessCtrl.CurrentStep = WAVE_STEP_COMPLETE;
        WaveProcessCtrl.Status = WAVE_PROCESS_ERROR;
        break;
    }
}

/******************************************************************************
 * @brief      1ms周期任务
 * <AUTHOR>
 * @date       2025-05-14
 * @note
 *****************************************************************************/
void RdumRdusDrv_1msTASK(void)
{
    /* 更新时间计数 */
    // RdumRdusDrv_UpdateTimeCnt();

    /* 处理SPI数据 */
    SpiCom_DataHandle();
}

/******************************************************************************
 * @brief      探头控制主函数
 * <AUTHOR>
 * @date       2025-05-14
 * @note
 *****************************************************************************/
void RdumRdusDrv_SnsCtrlMainFunc(void)
{
#if 0
    RdumRdusGroup_Cfg *pRdumRdusGroup = &RdumRdusGroup;
    Dsi3Channel_t Dsi3Ch;
    static uint8 SelfTestFlg = 0;

    switch (pRdumRdusGroup->GroupStatus)
    {
    case RdumRdusGroupStatus_SNS_DM:
    {
        /* 探头发现模式 */
        if (DMDone == 1)
        {
            /* 探头发现完成，进入自检状态 */
            pRdumRdusGroup->GroupStatus = RdumRdusGroupStatus_SNS_SELFTEST;
            pRdumRdusGroup->Time1msCnt = 0;
            SelfTestFlg = 1;
            PRINTF_RDUM(0, "RdumRdusDrv", "探头发现完成，进入自检状态\n");
        }
        else
        {
            /* 执行探头发现 */
            RdumRdusDrv_DiscoveryMode();
        }
        break;
    }

    case RdumRdusGroupStatus_SNS_SELFTEST:
    {
        /* 探头自检 */
        if (SelfTestFlg == 1)
        {
            /* 发送自检命令 */
            for (Dsi3Ch = DSI3_CHANNEL_A; Dsi3Ch < DSI3_CHANNEL_NUM; Dsi3Ch++)
            {
                Dsi3Cmd_InitRdumRdus(Dsi3Ch, DSI3_ID_ALL);
            }

            SelfTestFlg = 0;
            pRdumRdusGroup->GroupStatus = RdumRdusGroupStatus_WAIT_SELFTEST;
            pRdumRdusGroup->Time1msCnt = 0;
        }
        break;
    }

    case RdumRdusGroupStatus_WAIT_SELFTEST:
    {
        /* 等待自检完成 */
        if (pRdumRdusGroup->Time1msCnt > RDUM_RDUS_SNS_WAIT_SELFTEST_TIME)
        {
            /* 自检完成，进入开始测量状态 */
            pRdumRdusGroup->GroupStatus = RdumRdusGroupStatus_START_MEAS;
            pRdumRdusGroup->Time1msCnt = 0;
        }
        break;
    }

    case RdumRdusGroupStatus_START_MEAS:
    {
        /* 开始测量 */
        for (Dsi3Ch = DSI3_CHANNEL_A; Dsi3Ch < DSI3_CHANNEL_NUM; Dsi3Ch++)
        {
            /* 发送开始测量命令 */
            Dsi3Cmd_RdumRdusStartMeasurement(Dsi3Ch, DSI3_ID_ALL, MEAS_MODE_NEAR_RANGE_2PERIOD);

            /* 更新测量状态 */
            pRdumRdusGroup->SnsGroupMeasStatus[Dsi3Ch] = RdumRdusGroupMeas_BUSY_MEAS;
            pRdumRdusGroup->SnsGroupMeasTime[Dsi3Ch] = 0;
        }

        pRdumRdusGroup->GroupStatus = RdumRdusGroupStatus_WAIT_MEAS_COMPLETE;
        pRdumRdusGroup->Time1msCnt = 0;
        break;
    }

    case RdumRdusGroupStatus_WAIT_MEAS_COMPLETE:
    {
        /* 等待测量完成 */
        if (pRdumRdusGroup->Time1msCnt > RDUM_RDUS_SNS_WAIT_MEAS_TIMEOUT)
        {
            /* 测量超时，停止测量 */
            for (Dsi3Ch = DSI3_CHANNEL_A; Dsi3Ch < DSI3_CHANNEL_NUM; Dsi3Ch++)
            {
                pRdumRdusGroup->SnsGroupMeasStatus[Dsi3Ch] = RdumRdusGroupMeas_IDLE;
            }

            /* 检查是否需要温度补偿 */
            if (pRdumRdusGroup->TempCompTime1msCnt >= RDUM_RDUS_SNS_TEMPCOMP_PERIOD)
            {
                pRdumRdusGroup->GroupStatus = RdumRdusGroupStatus_TEMP_COMP;
                pRdumRdusGroup->TempCompTime1msCnt = 0;
            }
            else
            {
                /* 继续测量 */
                pRdumRdusGroup->GroupStatus = RdumRdusGroupStatus_START_MEAS;
            }

            pRdumRdusGroup->Time1msCnt = 0;
        }
        break;
    }

    case RdumRdusGroupStatus_TEMP_COMP:
    {
        /* 温度补偿 */
        for (Dsi3Ch = DSI3_CHANNEL_A; Dsi3Ch < DSI3_CHANNEL_NUM; Dsi3Ch++)
        {
            /* 读取温度状态 */
            Dsi3Cmd_RdumRdusReadStatus(Dsi3Ch, DSI3_ID_ALL, STATUS_ADDR_TEMP);
        }

        pRdumRdusGroup->GroupStatus = RdumRdusGroupStatus_WAIT_TEMP_COMP;
        pRdumRdusGroup->Time1msCnt = 0;
        break;
    }

    case RdumRdusGroupStatus_WAIT_TEMP_COMP:
    {
        /* 等待温度补偿完成 */
        if (pRdumRdusGroup->Time1msCnt > 10)
        {
            /* 温度补偿完成，继续测量 */
            pRdumRdusGroup->GroupStatus = RdumRdusGroupStatus_START_MEAS;
            pRdumRdusGroup->Time1msCnt = 0;
        }
        break;
    }

    case RdumRdusGroupStatus_STANDBY:
    {
        /* 待机模式 */
        if (StandbyFlg == 0)
        {
            /* 发送待机命令 */
            for (Dsi3Ch = DSI3_CHANNEL_A; Dsi3Ch < DSI3_CHANNEL_NUM; Dsi3Ch++)
            {
                Dsi3Cmd_RdumRdusIcModeSet(Dsi3Ch, DSI3_ID_ALL, IC_MODE_STANDBY);
            }

            StandbyFlg = 1;
            pRdumRdusGroup->GroupStatus = RdumRdusGroupStatus_WAIT_STANDBY;
            pRdumRdusGroup->Time1msCnt = 0;
        }
        break;
    }

    case RdumRdusGroupStatus_WAIT_STANDBY:
    {
        /* 等待进入待机 */
        if (pRdumRdusGroup->Time1msCnt > RDUM_RDUS_SNS_WAIT_STANDBY_TIME)
        {
            /* 待机完成 */
            pRdumRdusGroup->GroupStatus = RdumRdusDrv_GetNextSnsCtrlGroupStatus();
            pRdumRdusGroup->Time1msCnt = 0;
        }
        break;
    }

    case RdumRdusGroupStatus_WAKEUP:
    {
        /* 唤醒模式 */
        if (StandbyFlg == 1)
        {
            /* 发送唤醒命令 */
            for (Dsi3Ch = DSI3_CHANNEL_A; Dsi3Ch < DSI3_CHANNEL_NUM; Dsi3Ch++)
            {
                Dsi3Cmd_RdumRdusIcModeSet(Dsi3Ch, DSI3_ID_ALL, IC_MODE_WAKE_UP);
            }

            StandbyFlg = 0;
            pRdumRdusGroup->GroupStatus = RdumRdusGroupStatus_WAIT_WAKEUP;
            pRdumRdusGroup->Time1msCnt = 0;
        }
        break;
    }

    case RdumRdusGroupStatus_WAIT_WAKEUP:
    {
        /* 等待唤醒完成 */
        if (pRdumRdusGroup->Time1msCnt > RDUM_RDUS_SNS_WAIT_WAKEUP_TIME)
        {
            /* 唤醒完成 */
            pRdumRdusGroup->GroupStatus = RdumRdusDrv_GetNextSnsCtrlGroupStatus();
            pRdumRdusGroup->Time1msCnt = 0;
        }
        break;
    }

    default:
        break;
    }
#endif

    /* 根据驱动状态处理不同流程 */
    switch (RdumRdusDrvCtrl.Status)
    {
    case RDUM_RDUS_DRV_INIT:
        // /* 处理初始化流程 */
        // RdumRdusInitProcess_Process();

        // /* 检查初始化是否完成 */
        // if (RdumRdusInitProcess_GetStatus() == RDUM_RDUS_INIT_PROCESS_COMPLETE)
        // {
        //     PRINTF_RDUM(0, "RdumRdusDrv", "初始化流程完成，进入就绪状态\n");
        //     RdumRdusDrvCtrl.Status = RDUM_RDUS_DRV_READY;
        //     RdumRdusDrvCtrl.InitProcessFlag = 0;
        // }
        // else if (RdumRdusInitProcess_GetStatus() == RDUM_RDUS_INIT_PROCESS_ERROR)
        // {
        //     PRINTF_RDUM(0, "RdumRdusDrv", "初始化流程失败\n");
        //     RdumRdusDrvCtrl.Status = RDUM_RDUS_DRV_ERROR;
        //     RdumRdusDrvCtrl.InitProcessFlag = 0;
        // }
        break;

    case RDUM_RDUS_DRV_READY:
        /* 就绪状态，可以开始发波流程 */
        // if (RdumRdusGroup.GroupStatus == RdumRdusGroupStatus_START_MEAS &&
        //     RdumRdusDrvCtrl.WaveProcessFlag == 0)
        // {
        //     /* 启动发波流程 */
        //     if (RdumRdusWaveProcess_Start())
        //     {
        //         PRINTF_RDUM(0, "RdumRdusDrv", "发波流程启动成功\n");
        //         RdumRdusDrvCtrl.Status = RDUM_RDUS_DRV_WAVE_PROCESS;
        //         RdumRdusDrvCtrl.WaveProcessFlag = 1;
        //     }
        // }
        break;

    case RDUM_RDUS_DRV_WAVE_PROCESS:
        /* 处理发波流程 */
        RdumRdusWaveProcess_Process();

        /* 检查发波流程是否完成 */
        if (RdumRdusWaveProcess_GetStatus() == WAVE_PROCESS_COMPLETE)
        {
            PRINTF_RDUM(0, "RdumRdusDrv", "发波流程完成\n");
            RdumRdusDrvCtrl.Status = RDUM_RDUS_DRV_READY;
            RdumRdusDrvCtrl.WaveProcessFlag = 0;
        }
        else if (RdumRdusWaveProcess_GetStatus() == WAVE_PROCESS_ERROR)
        {
            PRINTF_RDUM(0, "RdumRdusDrv", "发波流程失败\n");
            RdumRdusDrvCtrl.Status = RDUM_RDUS_DRV_ERROR;
            RdumRdusDrvCtrl.WaveProcessFlag = 0;
        }
        break;

    case RDUM_RDUS_DRV_ERROR:
        /* 错误状态，可以考虑重新初始化 */
        break;

    default:
        break;
    }
}

/******************************************************************************
 * @brief      非周期任务
 * <AUTHOR>
 * @date       2025-05-14
 * @note       把所有的spi需要发送命令全部放入spi发送队列中，然后由spi发送任务处理
 *****************************************************************************/
void RdumRdusDrv_NoPeriod_TASK(void)
{
    /* 探头控制主函数 */
    RdumRdusDrv_SnsCtrlMainFunc();
}

/******************************************************************************
 * @brief      获取驱动状态
 * @return     驱动状态
 * <AUTHOR>
 * @date       2025-05-14
 * @note
 *****************************************************************************/
uint8 RdumRdusDrv_GetStatus(void)
{
    return RdumRdusDrvCtrl.Status;
}

/******************************************************************************
 * @brief      获取发波流程标志
 * @return     发波流程标志
 * <AUTHOR>
 * @date       2025-05-14
 * @note
 *****************************************************************************/
uint8 RdumRdusDrv_GetWaveProcessFlag(void)
{
    return RdumRdusDrvCtrl.WaveProcessFlag;
}

/******************************************************************************
 * @brief      初始化探头控制组
 * <AUTHOR>
 * @date       2025-05-14
 * @note
 *****************************************************************************/
static void RdumRdusDrv_InitSnsCtrlGroup(void)
{
    Dsi3Channel_t Dsi3Ch;

    memset(&RdumRdusGroup, 0, sizeof(RdumRdusGroup_Cfg));
    RdumRdusGroup.GroupStatus = RdumRdusGroupStatus_SNS_DM;
    RdumRdusGroup.Time1msCnt = 0;
    RdumRdusGroup.TempCompTime1msCnt = RDUM_RDUS_SNS_TEMPCOMP_PERIOD;
    RdumRdusGroup.StartMeasFlg = 1;

    for (Dsi3Ch = DSI3_CHANNEL_A; Dsi3Ch < DSI3_CHANNEL_NUM; Dsi3Ch++)
    {
        RdumRdusGroup.SnsGroupMeasStatus[Dsi3Ch] = RdumRdusGroupMeas_IDLE;
        RdumRdusGroup.SnsGroupMeasTime[Dsi3Ch] = 0;
    }

    PRINTF_RDUM(0, "RdumRdusDrv", "探头控制组初始化完成\n");
}

/******************************************************************************
 * @brief      探头发现模式
 * @return     1: 发现完成, 0: 发现中
 * <AUTHOR>
 * @date       2025-05-14
 * @note
 *****************************************************************************/
static uint8 RdumRdusDrv_DiscoveryMode(void)
{
    RdumRdusGroup_Cfg *pRdumRdusGroup = &RdumRdusGroup;
    Dsi3Channel_t Dsi3Ch;
    static uint8 ScIOCnt = 0;

    switch (RdumRdusDMSeq)
    {
        case eRdumRdusDMSeq_Init:
        {
            /* 初始化发现序列 */
            ScIOCnt = 0;
            DMRetryCnt = RDUM_RDUS_SNS_DM_RETRY_CNT;
            RdumRdusDMSeq = eRdumRdusDMSeq_StartAutoDM;
            pRdumRdusGroup->Time1msCnt = 0;
            PRINTF_RDUM(0, "RdumRdusDrv", "开始探头发现\n");
            break;
        }

        case eRdumRdusDMSeq_StartAutoDM:
        {
            /* 开始自动发现 */
            if (pRdumRdusGroup->Time1msCnt > 10)
            {
                /* 发送地址发现命令 */
                for (Dsi3Ch = DSI3_CHANNEL_A; Dsi3Ch < DSI3_CHANNEL_NUM; Dsi3Ch++)
                {
                    RdumRdusCrm_AddressDiscovery(Dsi3Ch, DSI3_ID_ALL);
                }

                RdumRdusDMSeq = eRdumRdusDMSeq_WaitAutoDM;
                pRdumRdusGroup->Time1msCnt = 0;
            }
            break;
        }

        case eRdumRdusDMSeq_WaitAutoDM:
        {
            /* 等待自动发现完成 */
            if (pRdumRdusGroup->Time1msCnt > 100)
            {
                RdumRdusDMSeq = eRdumRdusDMSeq_PrepareNextDM;
                pRdumRdusGroup->Time1msCnt = 0;
            }
            break;
        }

        case eRdumRdusDMSeq_PrepareNextDM:
        {
            /* 准备下一次发现 */
            if (pRdumRdusGroup->Time1msCnt > 10)
            {
                ScIOCnt++;
                if (ScIOCnt >= 4) /* 假设最多支持4个探头 */
                {
                    ScIOCnt = 0;
                    RdumRdusDMSeq = eRdumRdusDMSeq_End;
                }
                else
                {
                    RdumRdusDMSeq = eRdumRdusDMSeq_StartAutoDM;
                }

                pRdumRdusGroup->Time1msCnt = 0;
            }
            break;
        }

        case eRdumRdusDMSeq_End:
        {
            /* 结束发现 */
            RdumRdusDMSeq = eRdumRdusDMSeq_Init;
            ScIOCnt = 0;
            DMDone = 1;
            PRINTF_RDUM(0, "RdumRdusDrv", "探头发现完成\n");
            return 1;
        }

        default:
            break;
    }

    return 0;
}

/******************************************************************************
 * @brief      获取下一个控制组状态
 * @return     下一个状态
 * <AUTHOR>
 * @date       2025-05-14
 * @note
 *****************************************************************************/
static RdumRdusGroupStatus_en RdumRdusDrv_GetNextSnsCtrlGroupStatus(void)
{
    /* 简单实现，返回开始测量状态 */
    return RdumRdusGroupStatus_START_MEAS;
}

/******************************************************************************
 * @brief      更新时间计数
 * <AUTHOR>
 * @date       2025-05-14
 * @note
 *****************************************************************************/
static void RdumRdusDrv_UpdateTimeCnt(void)
{
    Dsi3Channel_t Dsi3Ch;

    /* 更新探头控制组时间计数 */
    RdumRdusGroup.Time1msCnt++;
    RdumRdusGroup.TempCompTime1msCnt++;

    /* 更新各通道测量时间 */
    for (Dsi3Ch = DSI3_CHANNEL_A; Dsi3Ch < DSI3_CHANNEL_NUM; Dsi3Ch++)
    {
        if (RdumRdusGroup.SnsGroupMeasStatus[Dsi3Ch] == RdumRdusGroupMeas_BUSY_MEAS)
        {
            RdumRdusGroup.SnsGroupMeasTime[Dsi3Ch]++;
        }
    }
}

/******************************************************************************
 * @brief      读命令回调函数
 * @param[in]  SpiComData SPI命令数据
 * @return     回调返回值
 * <AUTHOR>
 * @date       2025-05-14
 * @note       处理读命令的回调
 *****************************************************************************/
static SpiCallbackReturn_t ReadCommandCallback(SpiCom_t *SpiComData)
{
    SpiReadResp_t ReadResp;
    SpiDataBuffer_t *DataBuffer = SpiComData->DataBuffer;
    uint8 *RxData = DataBuffer->RxBuff;
    uint8 DataLens = DataBuffer->DataLens;
    SpiProtocol_t *SendCmdProtocol = (SpiProtocol_t *)DataBuffer->TxBuff;
    uint8 Addr = SendCmdProtocol->ReadAndWrite_t.Addr;
    uint8 CalcCrc;

    if (DataLens < SPI_CMD_LENS)
    {
        PRINTF_RDUM("SpiCom_DataAnalysis: Invalid data length %d\r\n", DataLens);
        return SPI_CALLBACK_CONTINUE;
    }

    /* 计算CRC */
    CalcCrc = BaseDrv_Crc8Calculate(RxData, SPI_CMD_CRC_LENS, CRC8_C2_INIT_SPI_AND_CRC_SD);

    if (CalcCrc != RxData[SPI_CMD_CRC_LENS])
    {
        PRINTF_RDUM("SpiCom_DataAnalysis: ReadCommandCallback CRC error, CalcCrc=%02X, Crc=%02X\r\n", CalcCrc, RxData[SPI_CMD_CRC_LENS]);
        return SPI_CALLBACK_CONTINUE;
    }

    /* 复制原始响应数据 */
    memcpy(ReadResp.RespData, RxData, SPI_CMD_LENS);

    // if (ReadResp.Bit.CRM_RCVD)
    // {
    //     PRINTF_RDUM("SpiCom_DataAnalysis: ReadCommandCallback CRM_RCVD\r\n");
    //     return SPI_CALLBACK_CONTINUE;
    // }

    // if (ReadResp.Bit.TIMER_INT)
    // {
    //     PRINTF_RDUM("SpiCom_DataAnalysis: ReadCommandCallback TIMER_INT\r\n");
    //     return SPI_CALLBACK_CONTINUE;
    // }

    // if (ReadResp.Bit.SPIERR)
    // {
    //     PRINTF_RDUM("SpiCom_DataAnalysis: ReadCommandCallback SPIERR\r\n");
    //     return SPI_CALLBACK_CONTINUE;
    // }

    if (ReadResp.Bit.HW_ERR)
    {
        PRINTF_RDUM("SpiCom_DataAnalysis: ReadCommandCallback HW_ERR\r\n");
        return SPI_CALLBACK_CONTINUE;
    }

    /*根据地址识别返回的数据RxData定义*/
    switch (Addr)
    {
    case SPI_CMD_ADDR_STATUS:
        if (!ReadResp.Bit.Data.Addr0x12.BUS_AVAIL)
        {
            PRINTF_RDUM("SpiCom_DataAnalysis: ReadCommandCallback BUS_AVAIL\r\n");
            return SPI_CALLBACK_CONTINUE;
        }
        break;

    default:
        break;
    }

    return SPI_CALLBACK_OK;
}

/******************************************************************************
 * @brief      非读命令回调函数
 * @param[in]  SpiComData SPI命令数据
 * @return     回调返回值
 * <AUTHOR>
 * @date       2025-05-14
 * @note       处理非读命令的回调
 *****************************************************************************/
static SpiCallbackReturn_t NonReadCommandCallback(SpiCom_t *SpiComData)
{
    SpiNonReadResp_t NonReadResp;
    SpiDataBuffer_t *DataBuffer = SpiComData->DataBuffer;
    uint8 *RxData = DataBuffer->RxBuff;
    uint8 DataLens = DataBuffer->DataLens;
    uint8 CalcCrc;

    if (DataLens < SPI_CMD_LENS)
    {
        PRINTF_RDUM("SpiCom_DataAnalysis: Invalid data length %d\r\n", DataLens);
        return SPI_CALLBACK_CONTINUE;
    }

    /* 计算CRC */
    CalcCrc = BaseDrv_Crc8Calculate(RxData, SPI_CMD_CRC_LENS, CRC8_C2_INIT_SPI_AND_CRC_SD);

    if (CalcCrc != RxData[SPI_CMD_CRC_LENS])
    {
        PRINTF_RDUM("SpiCom_DataAnalysis: NonReadCommandCallback CRC error, CalcCrc=%02X, Crc=%02X\r\n", CalcCrc, RxData[SPI_CMD_CRC_LENS]);
        return SPI_CALLBACK_CONTINUE;
    }

    /* 复制原始响应数据 */
    memcpy(NonReadResp.RespData, RxData, SPI_CMD_LENS);

    // if (NonReadResp.Bit.SPIERR)
    // {
    //     PRINTF_RDUM("SpiCom_DataAnalysis: NonReadCommandCallback SPIERR\r\n");
    //     return SPI_CALLBACK_CONTINUE;
    // }

    if (NonReadResp.Bit.HW_ERR)
    {
        PRINTF_RDUM("SpiCom_DataAnalysis: NonReadCommandCallback HW_ERR\r\n");
        return SPI_CALLBACK_CONTINUE;
    }

    return SPI_CALLBACK_OK;
}

/******************************************************************************
 * @brief      DSI3初始化命令
 * @param[in]  Dsi3Ch DSI3通道
 * @param[in]  Dsi3Id DSI3设备ID
 * @return     0: 成功, 非0: 失败
 * <AUTHOR>
 * @date       2025-05-14
 * @note       发送DSI3初始化命令
 *****************************************************************************/
static uint8 Dsi3Cmd_InitRdumRdus(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id)
{
    /* 发送软件复位命令 */
    if (RdumRdusCrm_SoftwareReset(Dsi3Ch, Dsi3Id) != SPI_TRANS_SUCC)
    {
        PRINTF_RDUM(0, "RdumRdusDrv", "发送软件复位命令失败\n");
        return 1;
    }

    PRINTF_RDUM(0, "RdumRdusDrv", "发送DSI3初始化命令成功\n");
    return 0;
}

/******************************************************************************
 * @brief      DSI3开始测量命令
 * @param[in]  Dsi3Ch DSI3通道
 * @param[in]  Dsi3Id DSI3设备ID
 * @param[in]  MeasMode 测量模式
 * @return     0: 成功, 非0: 失败
 * <AUTHOR>
 * @date       2025-05-14
 * @note       发送DSI3开始测量命令
 *****************************************************************************/
static uint8 Dsi3Cmd_RdumRdusStartMeasurement(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, uint8 MeasMode)
{
    RdumRdusCrm_MeasureFrame_t MeasureFrame;

    /* 配置测量帧 */
    memset(&MeasureFrame, 0, sizeof(RdumRdusCrm_MeasureFrame_t));
    MeasureFrame.Bit.Sid = Dsi3Id;
    MeasureFrame.Bit.Mode = MeasMode & 0x07;

    /* 配置各个传感器的行为，0为主发，1为左侦听，2为右侦听，3为不动作(最多只支持同时8个通道主发和侦听数据) */
    MeasureFrame.Bit.SID1_BEH_CH_H = 0x01;
    MeasureFrame.Bit.SID1_BEH_CH_L = 0x03;
    MeasureFrame.Bit.SID2_BEH_CH_H = 0x02;
    MeasureFrame.Bit.SID2_BEH_CH_L = 0x01;
    MeasureFrame.Bit.SID3_BEH_CH_H = 0x00;
    MeasureFrame.Bit.SID3_BEH_CH_L = 0x02;
    MeasureFrame.Bit.SID4_BEH_CH_H = 0x03;
    MeasureFrame.Bit.SID4_BEH_CH_L = 0x00;
    MeasureFrame.Bit.SID5_BEH_CH_H = 0x01;
    MeasureFrame.Bit.SID5_BEH_CH_L = 0x03;
    MeasureFrame.Bit.SID6_BEH_CH_H = 0x02;
    MeasureFrame.Bit.SID6_BEH_CH_L = 0x03;
    /* 发送开始测量命令 */
    if (RdumRdusCrm_StartMeasurement(Dsi3Ch, &MeasureFrame) != SPI_TRANS_SUCC)
    {
        PRINTF_RDUM(0, "RdumRdusDrv", "发送开始测量命令失败\n");
        return 1;
    }

    PRINTF_RDUM(0, "RdumRdusDrv", "发送开始测量命令成功\n");
    return 0;
}

/******************************************************************************
 * @brief      DSI3 IC模式设置命令
 * @param[in]  Dsi3Ch DSI3通道
 * @param[in]  Dsi3Id DSI3设备ID
 * @param[in]  IcMode IC模式
 * @return     0: 成功, 非0: 失败
 * <AUTHOR>
 * @date       2025-05-14
 * @note       发送DSI3 IC模式设置命令
 *****************************************************************************/
static uint8 Dsi3Cmd_RdumRdusIcModeSet(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, uint8 IcMode)
{
    /* 这里应该发送IC模式设置命令，暂时用简单的实现 */
    PRINTF_RDUM(0, "RdumRdusDrv", "设置IC模式: %d\n", IcMode);
    return 0;
}

/******************************************************************************
 * @brief      DSI3读取状态命令
 * @param[in]  Dsi3Ch DSI3通道
 * @param[in]  Dsi3Id DSI3设备ID
 * @param[in]  StatusAddr 状态地址
 * @return     0: 成功, 非0: 失败
 * <AUTHOR>
 * @date       2025-05-14
 * @note       发送DSI3读取状态命令
 *****************************************************************************/
static uint8 Dsi3Cmd_RdumRdusReadStatus(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, uint8 StatusAddr)
{
    /* 这里应该发送读取状态命令，暂时用简单的实现 */
    PRINTF_RDUM(0, "RdumRdusDrv", "读取状态地址: 0x%02X\n", StatusAddr);
    return 0;
}
