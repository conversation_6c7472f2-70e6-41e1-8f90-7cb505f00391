/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * Elmos_524_17.h:
 * Created on: 2019-11-01 15:05
 * Original designer: Lvqv Lin
 ***************************************************************************** */
#ifndef __Elmos_524_17_H__
#define __Elmos_524_17_H__

/** @brief  Includes ------------------------------------------------------------------ */
//#define ULT_USE_Elmos_524_17

#include "DSI3_COM.h"

#define PRINTF_ELMOS17                          ELMOS17_DEBUG_PRINT

/***************************************************************************** */
/***************************************************************************** */
/****************************** Type Definitions ***************************** */
/***************************************************************************** */
/***************************************************************************** */

typedef enum
{
    SnsMeasGroup_Start = 0,

    RearSnsMeasGroup = SnsMeasGroup_Start,
    FrontSnsMeasGroup,
    SnsMeasGroup_Num,
}SnsMeasGroup_en;

typedef enum
{
    SNSID_Start = 0,
    
    SNS_FL_S = SNSID_Start,
    SNS_FL ,
    SNS_FML,
    SNS_FMR,
    SNS_FR,
    SNS_FR_S,
    
    SNS_F_ID_OFFSET,
    SNS_GROUP_SIZE = SNS_F_ID_OFFSET,

    SNS_RR_S = SNS_F_ID_OFFSET,
    SNS_RR,
    SNS_RMR,
    SNS_RML,
    SNS_RL,
    SNS_RL_S,



    SNSNum,
    SNS_M0_ALL = SNSNum,
    SNS_M1_ALL,
    SNSIDNum,
    SNSID_NONE = 0xFF,
}SnsID_en;


typedef enum
{
    STANDARD_MEAS = 0x01,
    ADVANCED_MEAS,
    IMPEDANCE_MEAS,
    MeasType_num,
    MeasType_None = 0xFF,
}MeasType_en;


typedef enum
{
    MEAS_STD_EMIT = 0,
    MEAS_ADV_EMIT_ChirpDown,
    MEAS_ADV_EMIT_ChirpUp,

    MEAS_STD_LISTEN,

    MEAS_ADV_LISTEN,
    MEAS_IMPEDANCE,
    MEAS_IDLE,
    SNS_MEAS_TYPE_Num,
    SNS_MEAS_TYPE_NONE = 0xFF,
}SNS_MEAS_TYPE_en;

typedef enum
{
    res = 0,
    Profile_A,
    Profile_B,
    Profile_C, /* 仅 Standard模式*/
    SnsMeasProfile_num,
}SnsMeasProfile_en;

typedef enum
{
    APP_Mode = 0,
    LAB_Mode,
    APP_LAB_Mode,
    SnsMeasDataMode_Num,
}SnsMeasDataMode_en;

typedef enum
{
    eRecvMeasTimePDCMNum = 0,
    eRecvMeasALLEcho,
}RecvECHODataMode_en;

typedef struct
{
    MeasType_en MeasType;
    SnsMeasProfile_en profile;
    SnsMeasDataMode_en MeasDataMode;
    SNS_MEAS_TYPE_en SNsMeasType[DSISlaveSnsNum];

}Elmos17MeasCMDMsg_st;

typedef struct
{
    uint16 ECHOMeasTime_us; /** @brief 探测时间 */
    RecvECHODataMode_en RecvECHODataMode;
}Elmos17MeasRecvECHOSet_st;




typedef enum
{
    Elmos17EEPROMToRAM = 0,
    Elmos17EnEEPProg ,
    Elmos17RAMToEEPROM ,
    Elmos17ProgSLAVE_ID ,

    Elmos17EEPROMOperation_Num

}Elmos17EEPROMOperation;


typedef enum
{
    ePDCMDataType_None    = 0x0,
    ePDCMDataType_Echo    = 0x1,
    ePDCMDataType_Lab_Imp = 0x2,
    ePDCMDataType_Status  = 0x3,
}PDCMDataType_en;/** @brief  = Type */

typedef enum
{
    eECHODataSubType_NOInfo    = 0x0,
    eECHODataSubType_DiagData1 = 0x1,
    eECHODataSubType_DiagData2 = 0x2,
    eECHODataSubType_RTData    = 0x3,
    eECHODataSubType_STD_RAW   = 0x4,
    eECHODataSubType_ADV       = 0x5,
    eECHODataSubType_NFDData   = 0x6,
    eECHODataSubType_STD_AMPD  = 0x7,

}ECHODataSubType_en;/** @brief  = Type<<4 | SubType */

typedef enum
{
    e_LabImpSubType_NOInf     = 0x0,
    e_LabImpSubType_Lab_Type1 = 0x1,
    e_LabImpSubType_Lab_Type2 = 0x2,
    e_LabImpSubType_Impedance = 0xF,

}Lab_ImpDataSubType_en;/** @brief  = Type<<4 | SubType */

#define PDCMDataTypeMASK 0x03
#define PDCMDataSubTypeMASK 0x0F

#define DetPDCM_DataType(Status_Type)    (PDCMDataType_en)((Status_Type >> 4) & PDCMDataTypeMASK)
#define DetPDCM_DataSubType(Status_Type) (ECHODataSubType_en)(Status_Type & PDCMDataSubTypeMASK)

//#pragma pack(1)

typedef struct
{
    uint8 Temperature;  /** @brief  Temp = Temperature - 40 */
    uint16 VSUP;        /** @brief  VSUP(V) = VSUP *0.0390625 */
    uint16 NoiseSum;    /** @brief  噪音累加值  */
    uint8 NoiseCount;   /** @brief  超过噪音阈值个数 */

}Sns_PDCM_DiagData1;

typedef struct
{
    uint8 BurstLength;  /** @brief  发波时长 ~= BurstLength * 32us */
    uint16 VTANK2;      /** @brief  发波结束 VTANK2(V) = VTANK2 *0.0390625 */
    uint16 VTANK1;      /** @brief  发波起始 VTANK1(V) = VTANK1 *0.0390625 */

}Sns_PDCM_DiagData2;
typedef struct
{
    uint8 RingingCount;  
    uint16 RingingSamples;/** @brief   余震频率 = (8*RingingCount * Fc) / RingingSamples */
    uint16 RTM;           /** @brief   放大前级，比较输出的余震时间 */

}Sns_PDCM_RingTimeData;

typedef struct
{
    uint8 NFD1_2Flg;          /** @brief   bit1 Nfd1 Valid flag, bit0 Nfd2 Valid flag */
    uint8 NFD1_ECHO1;         /** @brief   NFD1 ECHO1 时间 *8us */
    uint8 NFD1_Delta_ECHO2;   /** @brief   NFD1 ECHO2 至 ECHO1 时间间隔   *8us */
    uint8 NFD1_Delta_ECHO3;   /** @brief   NFD1 ECHO3 至 ECHO2 时间间隔   *8us */
    uint8 NFD2_Result;        /** @brief   NFD2 NFD2 = NFD2_Result * 2 */

}Sns_PDCM_NFDData;

typedef enum
{
    STD_ECHO_Positive_Thres_Event = 0,   /** @brief 定频回波上升沿事件 */
    STD_ECHO_Negative_Thres_Event = 1,   /** @brief 定频回波下降沿事件 */
    STD_ECHO_MIN_Above_Thres_Event = 2,  /** @brief 定频回波波谷事件 */
    STD_ECHO_MAX_Above_Thres_Event = 3,  /** @brief 定频回波波峰事件 */
    STD_ECHO_Event_enNum,
}STD_ECHO_Event_en;

typedef struct
{
    uint8 EchoHeightType;              /** @brief  定频回波幅值类型 0:8bit AMP_ENV,1:16bit ENVP_ENV_RAW  */
    STD_ECHO_Event_en ECHO_Event;   /** @brief  定频回波事件类型 上升、下降、波谷、波峰 */
    uint16 EchoHeightAmpd_RAW;         /** @brief  回波事件幅值  */
    uint16 TimeStamp;                  /** @brief  回波事件时间戳 */
}Sns_PDCM_StdECHOData_Str;


typedef enum
{
    ADV_ECHO_Filter1_ChirpDown = 0,  /** @brief 扫频回波滤波器0        ，下扫频 */
    ADV_ECHO_Filter2_ChirpUp = 1,  /** @brief 扫频回波滤波器1        ，上扫频 */
    ADV_ECHO_FilterNum,
}ADV_ECHO_Filter_en;

typedef enum
{
    ECHO_Type_STD = 0,  /** @brief 定频 */
    ECHO_Type_ADV = 1,  /** @brief 扫频 */
    ECHO_Type_Num,
}ECHO_Type_en;


typedef struct
{

    ADV_ECHO_Filter_en filterNum;   /** @brief  扫频回波类型 0:filter0(CodeA); 1filter1 (CodeB) */
    uint8 Confidence;                  /** @brief  扫频回波置信度 0-15 */
    uint16 FilterAmpd;                 /** @brief  扫频回波幅值 */
    uint16 TimeStamp;                  /** @brief  扫频回波时间戳 */
}Sns_PDCM_AvdECHOData_Str;


typedef union
{
    Sns_PDCM_AvdECHOData_Str AdvECHOData;  /** @brief  ADV扫频回波数据结构 */
    Sns_PDCM_StdECHOData_Str StdECHOData;  /** @brief  STD定频回波数据结构 */

}Sns_ECHOData_un;



#define ECHO_MAXNUM 30

typedef struct
{
    Sns_ECHOData_un ECHOBuf[ECHO_MAXNUM];  /** @brief  单个探头回波事件buffer */
    uint8 SaveECHODataCnt;                    /** @brief  保存的回波个数 */
    uint8 TotalECHODataCnt;                   /** @brief  总回波个数 */

}Sns_ECHOMsg_Str;

#define DiagData1UpDateFlgMask  0x01
#define DiagData2UpDateFlgMask  0x02
#define DiagRTDataUpDateFlgMask 0x04
#define NFDDataUpDateFlgMask    0x08

typedef struct
{


    uint8 NoSymbolErr;                    /** @brief   0 SymbolCnt  */
    uint8 SymbolCntErr;                    /** @brief  SymbolCntErr  */
    uint8 PhyAddErr;
    uint8 CRC_SC_SE_Err;                    /** @brief  SymbolCntErr  */
    uint8 PacketDsiErrStatus;
    

    uint16 RecvValidFrameCnt;                  /** @brief  Sensor 接收帧个数 */
    uint16 LostValidFrameCnt;                  /** @brief  Sensor 接收帧个数 */

}Sns_PDCMMsg_Status_Str;


typedef struct
{
    SNS_MEAS_TYPE_en   SNS_MEAS_TYPE;  /** @brief  Sensor 探测类型     */

    Sns_PDCM_DiagData1 DiagData1;      /** @brief  Sensor 诊断数据1     VSUP/Temperatur/noise  */
    Sns_PDCM_DiagData2 DiagData2;      /** @brief  Sensor 诊断数据2 VTANK、发波驱动时长  */
    Sns_PDCM_RingTimeData RTData;      /** @brief  Sensor 放大前级余震时间，余震频率 */
    Sns_PDCM_NFDData      NFDData;     /** @brief  Sensor  NFD 数据 */
    uint8 DiagDatUpDateFlg;               /** @brief  Sensor  诊断数据更新标志 */

    Sns_ECHOMsg_Str ECHOMsgData;       /** @brief  Sensor 回波数据 */

    uint8 EchoBufFlg;

    uint8 ComHwErrorCnt[2];               /** @brief  data0 ComErrCnt ,data1 HwErrCnt   */
    
    uint8 RollCntErr;
    uint8 FrameRollCnt;                   /** @brief  Sensor Roll conut */
    uint16 RecvFrameCnt;                  /** @brief  Sensor 接收帧个数 */
    
}Sns_PDCM_MEASMsg_Str;




typedef struct
{
    MeasType_en MeasType;                    /** @brief 测量类型  */
    SnsMeasProfile_en MeasProfile;           /** @brief profile  */
    uint8 MeasSeq;                       /** @brief 探测序列ID  */

    Sns_PDCM_MEASMsg_Str ECHOMsg[DSISlaveSnsNum];    /** @brief DSI3总线上探头探测数据buf  */
    
    uint32 TransMeasCmdTime;                 	/** @brief  SPI命令传输时间*/
    uint32 UpdateTime;                 		/** @brief  数据更新时间*/
	uint32 ConsumptTime;					/** @brief  发波到数据解包完成时间*/
	uint32 DataRecEndTime;				/** @brief  回波数据接收完成时间*/
	uint32 ADASSYNTime;				    /** @brief  数据接收完成后获取的同步时间*/
}MeasChlECHOData_Str;    /** @brief   DSI3总线上探头回波数据 */

typedef struct
{
    uint16 CHIP_ID_H;
    uint16 CHIP_ID_L;
    uint16 HWErrStatus2;
    uint16 HWErrStatus1;
    uint16 ComErrStatus;
    
    uint16 Resverd1;
    
    uint16 VSUP;     /** @brief VSUP(V) = VSUP *0.0390625 */
    uint16 VTANK;    /** @brief VTANK(V) = VTANK *0.0390625 */
    uint16 VDDD;     /** @brief VDDD(V) = VDDD *0.004882813 */
    uint16 VREF;     /** @brief VREF(V) = VREF *0.004882813 */

    uint16 VTemp_LSB;
    uint16 Temp;  /** @brief Temp = Temp - 40 */
    uint16 CapVDDAUnload_LSB;
    uint16 CapVDDDUnload_LSB;
    uint16 DigitalSigPathCheckRes;
    uint16 InternalSigPathCheckRes;
    
    uint16 Resverd2[2]; /** @brief 0:阻抗频率  1:阻抗峰值 */

    uint16 RingTtimeFreq;/** @brief RingingFrequency [Hz] = RingingFrequency_LSB * 4  */
    uint16 RingTime;
    uint16 DSI_CLK_1us;           /** @brief CLK_1US [MHz] = 768 /DSI3.CLK_1US;tDSI_BIT [us]= 1 / CLK_1US */
    uint16 DSI_SYNC_CNT;          /** @brief DSI SYNC CNT */
    uint16 DSI_PDCM_IntervalMeas; /** @brief DSI 时间间隔 */

    uint16 Resverd3[7];

    uint16 HardwareVer;
    uint16 SoftwareVer;

    uint16 DataUpdataFlg;
    uint8 ComHwErrorCnt[2];               /** @brief  data0 ComErrCnt ,data1 HwErrCnt   */
    
    uint8 RollCntErr;
    uint8 FrameRollCnt;                   /** @brief  Sensor Roll conut */
    uint16 RecvFrameCnt;                  /** @brief  Sensor 接收帧个数 */


}SnsSelfTestStatusMsg_str;



typedef enum
{
    eElmos17_CRMReadStatusAddr_PUDIN_H = 0,
    eElmos17_CRMReadStatusAddr_PUDIN_L ,
    eElmos17_CRMReadStatusAddr_HWErrInf2 ,
    eElmos17_CRMReadStatusAddr_HWErrInf1 ,
    eElmos17_CRMReadStatusAddr_ComErrInf ,
    
    eElmos17_CRMReadStatusAddr_VSUP = 6 ,
    eElmos17_CRMReadStatusAddr_VTANK ,
    eElmos17_CRMReadStatusAddr_VDDD ,
    eElmos17_CRMReadStatusAddr_VREF ,
    eElmos17_CRMReadStatusAddr_VTemp ,
    eElmos17_CRMReadStatusAddr_Temp ,
    eElmos17_CRMReadStatusAddr_CVDDA ,
    eElmos17_CRMReadStatusAddr_CVDDD ,
    eElmos17_CRMReadStatusAddr_DigitalSignalPathCheck ,
    eElmos17_CRMReadStatusAddr_internalSignalPathCheck ,

    eElmos17_CRMReadStatusAddr_RingFreq = 18,
    eElmos17_CRMReadStatusAddr_RingTime ,
    eElmos17_CRMReadStatusAddr_DSI3Clk1us ,
    eElmos17_CRMReadStatusAddr_DSI3SyncCnt ,
    eElmos17_CRMReadStatusAddr_PDCMInterval ,

    eElmos17_CRMReadStatusAddr_HardwareVersion = 30,
    eElmos17_CRMReadStatusAddr_SoftwareVersion ,
    
}Elmos17_CRMReadStatusAddr_e;


typedef enum
{
   eElmos17IcMode_SelfTest      = 0x02,
   eElmos17IcMode_SoftwareReset = 0x04,
   eElmos17IcMode_Standby       = 0x08,
   eElmos17IcMode_WakeUp        = 0x10,
   eElmos17IcMode_DspOn         = 0x20,
   eElmos17IcMode_DspOff        = 0x40,
       
}Elmos17IcMode_en;

typedef enum
{
    eElmos17_Adj_Gain = 0,
    eElmos17_Adj_Idrv ,

    
}Elmos17_AdjSel_e;

typedef enum
{
    eElmos17_Adj_Positive = 0,
    eElmos17_Adj_Negative ,   
}Elmos17_AdjSymbol_e;

typedef enum
{
   eRet_OK      = 0,
    
   eRet_N_OK,
   eRet_ReqSPI_ERR,
   eRet_Resp_ERR,
   eRet_InDat_ERR,   
   eRet_Other_ERR,    
}Elmos17RetType_en;



/***************************************************************************** */
/** @brief  \Description :                                                        */
/***************************************************************************** */


/***************************************************************************** */
/** @brief  \Description :                                                     */
/***************************************************************************** */


/***************************************************************************** */
/***************************************************************************** */
/****************************** Macro Definitions **************************** */
/***************************************************************************** */
/***************************************************************************** */
#define EEPROM_MODE_ADDR (0x08)
#define EEPROM_ID_ADDR   (0xCE)

#define SNS_BROADCAST  0

/***************************************************************************** */
/***************************************************************************** */
/***************************** Symbol Definitions **************************** */
/***************************************************************************** */
/***************************************************************************** */

/***************************************************************************** */
/***************************************************************************** */
/*************************** Constants Declaration *************************** */
/***************************************************************************** */
/***************************************************************************** */



/***************************************************************************** */
/***************************************************************************** */
/*************************** Variables Declaration *************************** */
/***************************************************************************** */
/***************************************************************************** */



/***************************************************************************** */
/***************************************************************************** */
/*************************** Functions Declaration *************************** */
/***************************************************************************** */
/***************************************************************************** */

extern Elmos17RetType_en TransElmos17DMReq(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL);
extern Elmos17RetType_en TransElmos17AdjustSensitivity(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL,uint8 SnsSel,Elmos17_AdjSel_e AdjustSel,Elmos17_AdjSymbol_e DataSymbol,uint8 AdjustData);
extern Elmos17RetType_en TransElmos17ChangeSlaveID(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL,uint8 CurrentSlaveID,uint8 NewSlaveID);
extern Elmos17RetType_en TransElmos17WriteMeasCfgReq(SnsID_en SnsID,uint8 cDataAddr);
extern Elmos17RetType_en TransElmos17WriteMeasCfgData(SnsID_en SnsID,uint16 wData);
extern Elmos17RetType_en TransElmos17ReadMeasCfgReq(SnsID_en SnsID,uint8 cDataAddr);
extern Elmos17RetType_en TransElmos17EEPROMAccessReq(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL,uint8 SlaveID,Elmos17EEPROMOperation Operation);
extern Elmos17RetType_en TransElmos17ReadICRegReq(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL,uint8 SlaveID,uint8 cModAddr, uint8 cDataAddr);
extern Elmos17RetType_en TransElmos17WriteICRegReq(SnsID_en SnsID,uint8 cModAddr, uint8 cDataAddr);
extern Elmos17RetType_en TransElmos17WriteICRegData(SnsID_en SnsID,uint16 wData);
extern Elmos17RetType_en TransElmos17ReadStatusInCRMReq(DSIMasterID_en DSIMasterID,DSIChlSel_en DSIChlSEL,uint8 SlaveID,Elmos17_CRMReadStatusAddr_e StatusAddr);
extern Elmos17RetType_en TransElmos17ReadStatusInPDCMReq(SnsID_en SnsID,uint16 wStatusSel);
extern Elmos17RetType_en TransElmos17DSIClockSyncData(DSIMasterID_en DSIMasterID);
extern Elmos17RetType_en TransElmos17StopMeas(SnsID_en SnsID);
extern Elmos17RetType_en TransElmos17ICModeReq(SnsID_en SnsID,Elmos17IcMode_en IcMode);
extern Elmos17RetType_en TransElmos17MeasCmd(DSIMasterID_en DSIMasterID ,Elmos17MeasCMDMsg_st *pChl1Elmos17MeasCMDMsg,Elmos17MeasCMDMsg_st *pChl2Elmos17MeasCMDMsg);
extern Elmos17RetType_en TransElmos17MeasCmdSPIAsyn(DSIMasterID_en DSIMasterID ,Elmos17MeasCMDMsg_st *pChl1Elmos17MeasCMDMsg,Elmos17MeasCMDMsg_st *pChl2Elmos17MeasCMDMsg);
extern Elmos17RetType_en CheckCRMResStatus(CRM_RESPONSE_Data_str *pResOutData,uint8 SlaveID);


#endif /** @brief  __Elmos_524_17_H__  */
/*****************************************************END Sns_int.h******************************************************** */

