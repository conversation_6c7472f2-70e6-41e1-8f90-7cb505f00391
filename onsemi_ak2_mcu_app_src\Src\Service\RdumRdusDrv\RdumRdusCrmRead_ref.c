/******************************************************************************
 * @file      RdumRdusCrmRead.c
 * @brief     安森美超声波探头CRM命令读操作实现
 * <AUTHOR>
 * @date      2025-05-16
 * @note      
 *****************************************************************************/

/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include <string.h>
#include "RdumRdusCrm.h"
#include "SpiCmd.h"



/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/
#define READ_REQUEST_CMD            0x03    /* 读请求命令 */
#define READ_DATA_CMD               0x01    /* 读数据命令 */



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/



/******************************************************************************
 * @Const Declaration
 *****************************************************************************/



/******************************************************************************
 * @Variable Declaration
 *****************************************************************************/



/******************************************************************************
 * @Function Definitions
 *****************************************************************************/

/******************************************************************************
 * @brief      特殊读操作 - 发送读请求
 * @param[in]  Dsi3Ch DSI3通道
 * @param[in]  Dsi3Id 从机ID
 * @param[in]  Address 地址
 * @param[out] NonReadResp 非读操作响应
 * @return     传输状态
 * <AUTHOR>
 * @date       2025-05-16
 * @note       
 *****************************************************************************/
Dsi3TxStatus_t RdumRdusCrm_ReadRequest(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, uint8 Address, SpiNonReadResp_t *NonReadResp)
{
    uint8 TxData[7];
    uint8 RxData[7];
    uint8 RetVal;
    
    if (NonReadResp == NULL)
    {
        return DIS3_TRANS_FAIL;
    }
    
    /* 构建读请求命令 */
    TxData[0] = READ_REQUEST_CMD;
    TxData[1] = Address;
    TxData[2] = 0x00;
    TxData[3] = 0x00;
    TxData[4] = 0x00;
    TxData[5] = 0x00;
    
    /* 计算CRC */
    TxData[6] = BaseDrv_Crc8Calculate(TxData, 6);
    
    /* 发送命令 */
    RetVal = SpiCmd_TransCrm(Dsi3Ch, TxData, 7, RxData, 7);
    if (RetVal != 0)
    {
        return DIS3_TRANS_FAIL;
    }
    
    /* 解析非读操作响应 */
    SpiRespDef_ParseNonReadResp(RxData, NonReadResp);
    
    return DIS3_TRANS_SUCC;
}

/******************************************************************************
 * @brief      特殊读操作 - 读取数据
 * @param[in]  Dsi3Ch DSI3通道
 * @param[in]  Dsi3Id 从机ID
 * @param[in]  Address 地址
 * @param[out] ReadResp 读操作响应
 * @return     传输状态
 * <AUTHOR>
 * @date       2025-05-16
 * @note       
 *****************************************************************************/
Dsi3TxStatus_t RdumRdusCrm_ReadData(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, uint8 Address, SpiReadResp_t *ReadResp)
{
    uint8 TxData[7];
    uint8 RxData[7];
    uint8 RetVal;
    
    if (ReadResp == NULL)
    {
        return DIS3_TRANS_FAIL;
    }
    
    /* 构建读数据命令 */
    TxData[0] = READ_DATA_CMD;
    TxData[1] = Address;
    TxData[2] = 0x00;
    TxData[3] = 0x00;
    TxData[4] = 0x00;
    TxData[5] = 0x00;
    
    /* 计算CRC */
    TxData[6] = BaseDrv_Crc8Calculate(TxData, 6);
    
    /* 发送命令 */
    RetVal = SpiCmd_TransCrm(Dsi3Ch, TxData, 7, RxData, 7);
    if (RetVal != 0)
    {
        return DIS3_TRANS_FAIL;
    }
    
    /* 解析读操作响应 */
    SpiRespDef_ParseReadResp(RxData, ReadResp);
    
    return DIS3_TRANS_SUCC;
}

/******************************************************************************
 * @brief      特殊读操作 - 完整读取流程
 * @param[in]  Dsi3Ch DSI3通道
 * @param[in]  Dsi3Id 从机ID
 * @param[in]  Address 地址
 * @param[out] Data 读取的数据
 * @return     传输状态
 * <AUTHOR>
 * @date       2025-05-16
 * @note       
 *****************************************************************************/
Dsi3TxStatus_t RdumRdusCrm_ReadComplete(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, uint8 Address, uint32 *Data)
{
    SpiNonReadResp_t NonReadResp;
    SpiReadResp_t ReadResp;
    Dsi3TxStatus_t Status;
    
    if (Data == NULL)
    {
        return DIS3_TRANS_FAIL;
    }
    
    /* 发送读请求 */
    Status = RdumRdusCrm_ReadRequest(Dsi3Ch, Dsi3Id, Address, &NonReadResp);
    if (Status != DIS3_TRANS_SUCC)
    {
        return Status;
    }
    
    /* 读取数据 */
    Status = RdumRdusCrm_ReadData(Dsi3Ch, Dsi3Id, Address, &ReadResp);
    if (Status != DIS3_TRANS_SUCC)
    {
        return Status;
    }
    
    /* 组合数据 */
    *Data = (ReadResp.Bit.Data[0] << 24) |
            (ReadResp.Bit.Data[1] << 16) |
            (ReadResp.Bit.Data[2] << 8) |
            (ReadResp.Bit.Data[3]);
    
    return DIS3_TRANS_SUCC;
}

/******************************************************************************
 * @brief      通过页面索引完整读取流程
 * @param[in]  Dsi3Ch DSI3通道
 * @param[in]  Dsi3Id 从机ID
 * @param[in]  PageIndex 页面索引
 * @param[out] Data 读取的数据
 * @return     传输状态
 * <AUTHOR>
 * @date       2025-05-16
 * @note       
 *****************************************************************************/
Dsi3TxStatus_t RdumRdusCrm_ReadCompleteByPageIndex(Dsi3Channel_t Dsi3Ch, uint8 Dsi3Id, RdumRdusPageIndex_t PageIndex, uint32 *Data)
{
    uint8 Address;
    
    /* 检查页面索引是否有效 */
    if (!RdumRdusPageIndex_IsValid(PageIndex))
    {
        return DIS3_TRANS_FAIL;
    }
    
    /* 计算地址 */
    Address = (PageIndex.Page << 6) | (PageIndex.Index & 0x3F);
    
    /* 完整读取流程 */
    return RdumRdusCrm_ReadComplete(Dsi3Ch, Dsi3Id, Address, Data);
}
