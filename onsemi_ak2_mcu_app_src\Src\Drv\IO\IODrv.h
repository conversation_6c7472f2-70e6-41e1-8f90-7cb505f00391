/******************************************************************************/
/**@file
 *<pre>
 * 版权所有: 深圳市豪恩汽车电子装备有限公司
 * 文件名称: IODrv.h
 * 其它说明: 
 * 当前版本: 
 * 作    者: 
 * 完成日期: 
 * 修改记录: 
 * 修改日期: 
*********************************************************************** </pre>*/
#ifndef __IODRV_H
#define __IODRV_H

#include "types.h"

/********************************数据类型定义**********************************/
/******************************************************************************/
/**
 *  @brief 端口定义
 */
/******************************************************************************/
/*===========================================================================*/
/* Defines */
/*===========================================================================*/
/* Defines for filter settings */
#define R_FCLA_LEVEL_DETECTION  0x00
#define R_FCLA_LOW_LEVEL        0x00
#define R_FCLA_HIGH_LEVEL       0x01
#define R_FCLA_EDGE_DETECTION   0x04
#define R_FCLA_FALLING_EDGE     0x02
#define R_FCLA_RISING_EDGE      0x01

/* Enumeration for alternative functions of port pins */
enum alt_t
{
    Alt1,
    Alt2,
    Alt3,
    Alt4,
    Alt5,
    Alt6,
    Alt7
};

enum io_t
{
    Input,
    Output
};

enum level_t
{
    Low = 0,
    High = 1
};

/* Enumeration for analog filter signals */
enum fcla_signal_t
{
    R_FCLA_INTP0 = 0,
    R_FCLA_INTP1,
    R_FCLA_INTP2,
    R_FCLA_INTP3,
    R_FCLA_INTP4,
    R_FCLA_INTP5,
    R_FCLA_INTP6,
    R_FCLA_INTP7,
    R_FCLA_INTP8,
    R_FCLA_INTP9,
    R_FCLA_INTP10,
    R_FCLA_INTP11,
    R_FCLA_INTP12,
    R_FCLA_INTP13,
    R_FCLA_INTP14,
    R_FCLA_INTP15,
    R_FCLA_NMI = 0x10
};

/* Structure of registers for each port */
struct pregs_t
{
    volatile uint16 * P_Reg;
    volatile uint16 * PNOT_Reg;
    volatile uint16 * PM_Reg;
    volatile uint16 * PMC_Reg;
    volatile uint16 * PFC_Reg;
    volatile uint16 * PFCE_Reg;
    volatile uint16 * PFCAE_Reg;
    volatile uint16 * PIPC_Reg;
    volatile uint16 * PIBC_Reg;
    volatile const uint16 * PPR_Reg;
    volatile uint16 * PD_Reg;
    volatile uint16 * PU_Reg;
    volatile uint32 * PODC_Reg;
};

/* Enumeration for ports corresponding to the PortList */
enum port_t
{
    Port0 = 0,
    Port8,
    Port9,
    Port10,
    Port11,
    APort0
};

/* Array with available registers for the available ports. Registers that are not available */
/* for a port are 0.                                                                        */
static const struct pregs_t PortList[]=
{
  {(volatile uint16 *)&PORTP0,(volatile uint16 *) &PORTPNOT0,(volatile uint16 *) &PORTPM0,(volatile uint16 *) &PORTPMC0, (volatile uint16 *)&PORTPFC0, (volatile uint16 *)&PORTPFCE0, (volatile uint16 *)&PORTPFCAE0, (volatile uint16 *)&PORTPIPC0, (volatile uint16 *)&PORTPIBC0,(volatile uint16 *) &PORTPPR0, (volatile uint16 *)&PORTPD0, (volatile uint16 *)&PORTPU0,(volatile uint32 *)&PORTPODC0},
//  {(volatile uint16 *)&PORTP1,(volatile uint16 *) &PORTPNOT1, (volatile uint16 *)&PORTPM1, (volatile uint16 *)&PORTPMC1, (volatile uint16 *)&PORTPFC1, (volatile uint16 *)&PORTPFCE1, (volatile uint16 *)&PORTPFCAE1, 0, (volatile uint16 *)&PORTPIBC1, (volatile uint16 *)&PORTPPR1, (volatile uint16 *)&PORTPD1, (volatile uint16 *)&PORTPU1, (volatile uint32 *)&PORTPODC1},
//  {(volatile uint16 *)&PORTP2, (volatile uint16 *)&PORTPNOT2, (volatile uint16 *)&PORTPM2, (volatile uint16 *)&PORTPMC2, (volatile uint16 *)&PORTPFC2, (volatile uint16 *)&PORTPFCE2, (volatile uint16 *)&PORTPFCAE2, 0, (volatile uint16 *)&PORTPIBC2, (volatile uint16 *)&PORTPPR2, (volatile uint16 *)&PORTPD2, (volatile uint16 *)&PORTPU2, (volatile uint32 *)&PORTPODC2},
  {(volatile uint16 *)&PORTP8, (volatile uint16 *)&PORTPNOT8, (volatile uint16 *)&PORTPM8, (volatile uint16 *)&PORTPMC8, (volatile uint16 *)&PORTPFC8, (volatile uint16 *)&PORTPFCE8, 0, 0, (volatile uint16 *)&PORTPIBC8, (volatile uint16 *)&PORTPPR8, (volatile uint16 *)&PORTPD8, (volatile uint16 *)&PORTPU8, (volatile uint32 *)&PORTPODC8},
  {(volatile uint16 *)&PORTP9, (volatile uint16 *)&PORTPNOT9, (volatile uint16 *)&PORTPM9, (volatile uint16 *)&PORTPMC9, (volatile uint16 *)&PORTPFC9, (volatile uint16 *)&PORTPFCE9, 0, 0, (volatile uint16 *)&PORTPIBC9, (volatile uint16 *)&PORTPPR9, (volatile uint16 *)&PORTPD9, (volatile uint16 *)&PORTPU9, (volatile uint32 *)&PORTPODC9},
  {(volatile uint16 *)&PORTP10, (volatile uint16 *)&PORTPNOT10, (volatile uint16 *)&PORTPM10, (volatile uint16 *)&PORTPMC10, (volatile uint16 *)&PORTPFC10, (volatile uint16 *)&PORTPFCE10, (volatile uint16 *)&PORTPFCAE10, (volatile uint16 *)&PORTPIPC10, (volatile uint16 *)&PORTPIBC10, (volatile uint16 *)&PORTPPR10, (volatile uint16 *)&PORTPD10, (volatile uint16 *)&PORTPU10, (volatile uint32 *)&PORTPODC10},
  {(volatile uint16 *)&PORTP11, (volatile uint16 *)&PORTPNOT11, (volatile uint16 *)&PORTPM11, (volatile uint16 *)&PORTPMC11, (volatile uint16 *)&PORTPFC11, (volatile uint16 *)&PORTPFCE11, (volatile uint16 *)&PORTPFCAE11, (volatile uint16 *)&PORTPIPC11, (volatile uint16 *)&PORTPIBC11, (volatile uint16 *)&PORTPPR11, (volatile uint16 *)&PORTPD11, (volatile uint16 *)&PORTPU11, (volatile uint32 *)&PORTPODC11},
//  {(volatile uint16 *)&PORTP12, (volatile uint16 *)&PORTPNOT12, (volatile uint16 *)&PORTPM12, (volatile uint16 *)&PORTPMC12, (volatile uint16 *)&PORTPFC12, (volatile uint16 *)&PORTPFCE12, (volatile uint16 *)&PORTPFCAE12, 0, (volatile uint16 *)&PORTPIBC12, (volatile uint16 *)&PORTPPR12, (volatile uint16 *)&PORTPD12, (volatile uint16 *)&PORTPU12, (volatile uint32 *)&PORTPODC12},
//  {(volatile uint16 *)&PORTP18, (volatile uint16 *)&PORTPNOT18, (volatile uint16 *)&PORTPM18, (volatile uint16 *)&PORTPMC18, (volatile uint16 *)&PORTPFC18, 0, 0, 0, (volatile uint16 *)&PORTPIBC18, (volatile uint16 *)&PORTPPR18, (volatile uint16 *)&PORTPD18, (volatile uint16 *)&PORTPU18, (volatile uint32 *)&PORTPODC18},
//  {(volatile uint16 *)&PORTP20, (volatile uint16 *)&PORTPNOT20, (volatile uint16 *)&PORTPM20, (volatile uint16 *)&PORTPMC20, (volatile uint16 *)&PORTPFC20, (volatile uint16 *)&PORTPFCE20, (volatile uint16 *)&PORTPFCAE20, 0, (volatile uint16 *)&PORTPIBC20, (volatile uint16 *)&PORTPPR20, (volatile uint16 *)&PORTPD20, (volatile uint16 *)&PORTPU20, (volatile uint32 *)&PORTPODC20},
  {(volatile uint16 *)&PORTAP0, (volatile uint16 *)&PORTAPNOT0, (volatile uint16 *)&PORTAPM0, 0, 0, 0, 0, 0, (volatile uint16 *)&PORTAPIBC0, (volatile uint16 *)&PORTAPPR0, 0, 0, 0},
//  {(volatile uint16 *)&PORTAP1, (volatile uint16 *)&PORTAPNOT1, (volatile uint16 *)&PORTAPM1, 0, 0, 0, 0, 0, (volatile uint16 *)&PORTAPIBC1, (volatile uint16 *)&PORTAPPR1, 0, 0, 0}
};

/*===========================================================================*/
/* Function defines */
/*===========================================================================*/
void R_PORT_SetGpioOutput(enum port_t Port, uint32 Pin, enum level_t Level);
void R_PORT_ToggleGpioOutput(enum port_t Port, uint32 Pin);
void R_PORT_SetGpioInput(enum port_t Port, uint32 Pin);
void R_PORT_SetGpioHighZ(enum port_t Port, uint32 Pin);
void R_PORT_SetAltFunc(enum port_t Port, uint32 Pin, enum alt_t Alt, enum io_t IO);
uint32 R_PORT_GetLevel(enum port_t Port, uint32 Pin);
void R_PORT_SetOpenDrain(enum port_t Port, uint32 Pin);
void R_PORT_SetPushPull(enum port_t Port, uint32 Pin);
void R_PORT_SetAnalogFilter(enum fcla_signal_t InputSignal, uint8 FilterSetting);


//
//typedef enum
//{
//    /* JPORT0 */
//    PORT_PIN_JP0_0 = 0u,
//    PORT_PIN_JP0_1,
//    PORT_PIN_JP0_2,
//    PORT_PIN_JP0_3,
//    PORT_PIN_JP0_4,
//    PORT_PIN_JP0_5,
//    /* APORT0 */
//    PORT_PIN_AP0_0,
//    PORT_PIN_AP0_1,
//    PORT_PIN_AP0_2,
//    PORT_PIN_AP0_3,
//    PORT_PIN_AP0_4,
//    PORT_PIN_AP0_5,
//    PORT_PIN_AP0_6,
//    PORT_PIN_AP0_7,
//    /* PORT0 */
//    PORT_PIN_P0_0,
//    PORT_PIN_P0_1,
//    PORT_PIN_P0_2,
//    PORT_PIN_P0_3,
//    /* PORT8 */
//    PORT_PIN_P8_0,
//    PORT_PIN_P8_1,
//    /* PORT9 */
//    PORT_PIN_P9_0,
//    PORT_PIN_P9_1,
//    /* PORT10 */
//    PORT_PIN_P10_0,
//    PORT_PIN_P10_1,
//    PORT_PIN_P10_2,
//    PORT_PIN_P10_3,
//    PORT_PIN_P10_4,
//    PORT_PIN_P10_5,
//    PORT_PIN_P10_6,
//    PORT_PIN_P10_7,
//    PORT_PIN_P10_8,
//    PORT_PIN_P10_9,
//    PORT_PIN_P10_10,
//}IO_PinNumType;
//
///************************************宏定义************************************/
//#define PORT_LOW_LEVEL                              0u      /*低电平*/
//#define PORT_HIGH_LEVEL                             1u      /*高电平*/
//
///* SOC DEBUG PIN */
//#define DETECT_VBUS_PINNUM                          PORT_PIN_AP0_1
///* ACC PIN */
//#define ACC_DET_PINNUM                              PORT_PIN_P8_1
///* CAMERA 12V INPUT PIN */
//#define PWR_12V_EN_PINNUM                           PORT_PIN_JP0_3
///* CAMERA DIAG PIN */
//#define CAMPOW_DIAG_EN_PINNUM                       PORT_PIN_P10_3
///* CAMERA POWER PIN */
//#define DVR_PWR_EN_PINNUM                           PORT_PIN_P0_2
//#define OMS_PWR_EN_PINNUM                           PORT_PIN_P0_3
//#define DMS_PWR_EN_PINNUM                           PORT_PIN_AP0_6
//#define FACE_PWR_EN_PINNUM                          PORT_PIN_AP0_7
///* CAMERA STATUS PIN */
//#define DVR_CAM_ST_PINNUM                           PORT_PIN_AP0_5
//#define OMS_CAM_ST_PINNUM                           PORT_PIN_AP0_3
//#define DMS_CAM_ST_PINNUM                           PORT_PIN_AP0_2
//#define FACE_CAM_ST_PINNUM                          PORT_PIN_AP0_4
///* SOC POWER CTRL PIN */
//#define VDD_SYS1V2_EN_PINNUM                        PORT_PIN_P10_0
//#define VDD_SYS3V3_EN_PINNUM                        PORT_PIN_P10_1
//#define MCU_SYS5V_EN_PINNUM                         PORT_PIN_P10_2
//#define VDD_SYS0V8_EN_PINNUM                        PORT_PIN_P10_8
//#define DSP_RST_EN_PINNUM                           PORT_PIN_JP0_4
///* CAN CTRL PIN */
//#define CAN_STB_PINNUM                              PORT_PIN_P10_6
//#define CAN_5V_EN_PINNUM                            PORT_PIN_P10_7
///* BAT SAMPLE EN */
//#define BAT_SAMPLE_EN_PINNUM                        PORT_PIN_P10_4
//
///*******************************全局函数声明**********************************/
//extern void IODrvInit(void);
//extern void IODrvClose(void);
//extern uint8 GetGpioPinLvl(IO_PinNumType LenuPinNum);
//extern void SetGpioPinLvl(IO_PinNumType LenuPinNum, uint8 Lu8OutputLvl);

#endif
