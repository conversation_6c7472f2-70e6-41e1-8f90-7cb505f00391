/******************************************************************************
 * @file       BaseDrv.c
 * @brief      
 * @date       2025-03-22 20:38:20
 * <AUTHOR>
 * @copyright  Longhorn Automotive Electronics Technology (Xiamen) Co., Ltd.
 *****************************************************************************/


/******************************************************************************
 * @Include Files
 *****************************************************************************/
#include "BaseDrv.h"
#include "CrcDrv.h"
#include "DMADrv.h"
#include "TimerManage.h"



/******************************************************************************
 * @Macro Definitions
 *****************************************************************************/



/******************************************************************************
 * @Type Definitions
 *****************************************************************************/



/******************************************************************************
 * @Function Declaration
 *****************************************************************************/



/******************************************************************************
 * @Const Definitions
 *****************************************************************************/



/******************************************************************************
 * @Variable Definitions
 *****************************************************************************/



/******************************************************************************
 * @Function Definitions
 *****************************************************************************/


/******************************************************************************
 * @brief      RDUM_0传输SPI数据
 * @param[in]  SendBuffer 
 * @param[in]  ReceiveBuffer 
 * @param[in]  TransferByteCnt 
 * @return     
 * <AUTHOR>
 * @date       2025-03-22 20:53:25
 * @note       
 *****************************************************************************/
SpiTransReturn_t BaseDrv_Master0SpiAsyncTransmit(const uint8 *SendBuffer, uint8 *ReceiveBuffer, uint16 TransferByteCnt)
{
    uint16 Ret;

    Ret = CSIH_DRV_MasterSendReceive(SendBuffer, ReceiveBuffer, TransferByteCnt, CSIH2_MASTER);
    if (Ret != 0)
    {
        return SPI_TRANS_INVALID;
    }
    else
    {
        return SPI_TRANS_SUCC;
    }
}


/******************************************************************************
 * @brief      RDUM_1传输SPI数据
 * @param[in]  SendBuffer 
 * @param[in]  ReceiveBuffer 
 * @param[in]  TransferByteCnt 
 * @return     
 * <AUTHOR>
 * @date       2025-03-22 21:00:25
 * @note       
 *****************************************************************************/
SpiTransReturn_t BaseDrv_Master1SpiAsyncTransmit(const uint8 *SendBuffer, uint8 *ReceiveBuffer, uint16 TransferByteCnt)
{
    uint16 Ret;

    Ret = CSIH_DRV_MasterSendReceive(SendBuffer, ReceiveBuffer, TransferByteCnt, CSIH3_MASTER);
    if (Ret != 0)
    {
        return SPI_TRANS_INVALID;
    }
    else
    {
        return SPI_TRANS_SUCC;
    }
}


/******************************************************************************
 * @brief      CRC8计算
 * @param[in]  Data 
 * @param[in]  Len 
 * <AUTHOR>
 * @date       2025-03-25 08:45:27
 * @note       The CRC polynomial shall be the CRC8-C2 polynomial 
 *             x8+x5+x3+x2+x+1.
 *****************************************************************************/
uint8 BaseDrv_Crc8Calculate(const uint8 *Data, uint32 Len, uint8 crc_init)
{
    return CRC8_C2_CalculateBySw(Data, Len, crc_init);
}


/******************************************************************************
 * @brief      获取毫秒计时器
 * @return     
 * <AUTHOR>
 * @date       2025-04-01 16:59:59
 * @note       
 *****************************************************************************/
uint32 BaseDrv_GetSys1MsTick(void)
{
    return GetSystemUseTimerMsCnt();
}
