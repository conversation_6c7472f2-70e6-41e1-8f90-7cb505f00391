/**********************************************************************
*   版权所有    : 2017 深圳市豪恩汽车电子装备有限公司
*   项目名称    : TP_Lib
*   M C U       : R5F10BBG
*   编译环境    : IAR
*   文件名称    : TP_COM_Interface.c
*   其它说明    : TP_COM接口
*   当前版本    : V0.1
*   作    者  : 20460
*   完成日期    :   
*   内容摘要    :
*   修改记录1   :
*   修改日期    :
*   版 本 号 :
*   修 改 人 :
*   修改内容    :
*   修改记录2   :

***********************************************************************/
/***********************************************************************
* Define macro 
***********************************************************************/
#define TP_COM_FUNCTION

/***********************************************************************
* Includes 
***********************************************************************/
#include "TP_Config.h"
#include "TP_Manage.h"
#include "TP_COM_Interface.h"


/*********************************************************************
* 函数名称: TP_COM_SendFrameDataReq
*
* 功能描述: TP把需要发送的数据存放至COM层,并请求发送
*
* 输入参数: 无
*
* 输出参数: 返回请求发送状态
*
* 返 回 值:  无
*
* 其它说明: 
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/
uint8 TP_COM_SendFrameDataReq (const uint8 *pData)
{
    uint8 LcDataIdx;
    uint8 LcStatus;

    LcStatus = COM_ERR_OK;

    for (LcDataIdx = 0U; LcDataIdx < COM_DATA_MAX_DIAG; LcDataIdx ++)
    {
         GsTpComStr.pRespondDiagComBuff[LcDataIdx] = pData[LcDataIdx];        /* 把TP层数据移至COM层接口 */
    }

    GsTpComStr.pRespondDiagFrameState[0] |= COM_FRAME_STATUS_TX_REQ;        /* Flag set to request a transmission */
    
    return (LcStatus);
}

/*********************************************************************
* 函数名称: COM_GetFrameData
*
* 功能描述: 读取COM层接收到的CAN报文数据
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值:  无
*
* 其它说明: 
*
* 修改日期      版本号       修改人       修改内容
*
**********************************************************************/
uint8 COM_GetFrameData (uint8 LcChannel,uint8 *pData)
{
    uint8 LcCnt;
    uint8 LcDlc = 0;

    if(LcChannel == PHYSICALCHANNEL)
    {
        GsTpComStr.pPhysicalDiagFrameState[0] &= COM_FRAME_STATUS_RX_FLAG^(uint8)0xff;   /* 清接收标志 */
        LcDlc = GsTpComStr.pPhysicalRxDLC[0];
        for(LcCnt = 0;LcCnt < COM_DATA_MAX_DIAG;LcCnt++)
        {
            pData[LcCnt] = GsTpComStr.pPhysicalDiagComBuf[LcCnt];
        }
    }
    else if(LcChannel == FUNCTIONALCHANNEL)
    {
        GsTpComStr.pFunctionDiagFrameState[0] &= COM_FRAME_STATUS_RX_FLAG^(uint8)0xff;   /* 清接收标志 */
        LcDlc = GsTpComStr.pFunctionRxDLC[0];
        
        for(LcCnt = 0;LcCnt < COM_DATA_MAX_DIAG;LcCnt++)
        {
            pData[LcCnt] = GsTpComStr.pFunctionDiagComBuf[LcCnt];
        }
    }   
    else
    {
        
    }
    return LcDlc;
}



