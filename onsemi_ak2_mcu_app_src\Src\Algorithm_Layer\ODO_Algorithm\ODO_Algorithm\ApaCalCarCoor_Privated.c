/******************************************************************************
 * Shenzhen Longhorn Automotive Electronic Equipment Co.,Ltd.
 * ApaCalCarCoor.c: 
 * Created on: 2020-12-07 19:09
 * Original designer: 22554
 ******************************************************************************/


/******************************************************************************
 * Inclusion of private header files
 ******************************************************************************/
#include "math.h"
#include "ApaCalCarCoor_Privated.h"
#include "ApaCalCarCoor.h"
#include "TimerManage.h"
#include "ODO_AppSignalManage.h"
#include "ODO_CalibPara.h"
#include "CAN_AppSignalManage.h"
#include "types.h"
#include "TAU_COM.h"


/******************************************************************************
 * Compiler option used to distinguish inclusion done by the owner
 *****************************************************************************/
/******************************************************************************/
/******************************************************************************/
///****************************** Private Definition ****************************/
/******************************************************************************/
/******************************************************************************/
static CPOS_CurCarPointDataType GstrCurCarPointData[RELATION_MOD_NUM_COOR];
static CPOS_CalPointNeedDataType GstrCalPointNeedData;
static APA_Odom_CarParameterType * GpstrCarParaData;
static float GfCarTurnRadian = 5000000.0;

void CPOS_Privated_CalDataFunc(uint8 id);
void CPOS_Privated_GetWheelRunDist(void);

/******************************************************************************/
/******************************************************************************/
/****************************** Public Definition *****************************/
/******************************************************************************/
/******************************************************************************/




/******************************************************************************/
/******************************************************************************/
/****************************** Privated Function *****************************/
/******************************************************************************/
/******************************************************************************/

/******************************************************************
* 函数名称: CPOS_Privated_Init
*
* 功能描述: 初始化数据内容
*
* 输入参数: LvpCarParaData：车身参数
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 无
*
* 修改日期      版本号      修改人      修改内容

*********************************************************************/
void CPOS_Privated_Init(APA_Odom_CarParameterType *LvpCarParaData)
{
    uint8 i;
    VehicleTypeType LenuVEH_Type;
    VehicleWheelType LenuVehicleWheel;
    
    for(i = 0;i < RELATION_MOD_NUM_COOR;i++)
    {
        GstrCurCarPointData[i].fX = 0;
        GstrCurCarPointData[i].fY = 0;
        GstrCurCarPointData[i].fAngle = 0;
        GstrCurCarPointData[i].fDrvDis = 0;
        GstrCurCarPointData[i].TurnRadius = GfCarTurnRadian;
        GstrCurCarPointData[i].fCosAngle = cosf( GstrCurCarPointData[i].fAngle);
        GstrCurCarPointData[i].fSinAngle = sinf( GstrCurCarPointData[i].fAngle);




        GstrCalPointNeedData.u8EnableCalFlg[i] = 0;
        GstrCalPointNeedData.fCarBodyAngle[i] = 0;
        GstrCalPointNeedData.fCarBodyOriginalAngle[i] = 0;
        GstrCalPointNeedData.fCar_YawAngleBak[i] = 0xff;
    }

    GfCarTurnRadian = 5000000.0;
    //0-纯脉冲计算        1-弧长方式计算
    GstrCalPointNeedData.u8SelectCalWay = 1;
    GstrCalPointNeedData.u16HLWheelPulse = 0xFFFFu;
    GstrCalPointNeedData.u16HRWheelPulse = 0xFFFFu;
    GstrCalPointNeedData.u16HLWheelPulseBak = 0xFFFFu;
    GstrCalPointNeedData.u16HRWheelPulseBak = 0xFFFFu;

    GstrCalPointNeedData.u16HLWheelPulseValid = FALSE;
    GstrCalPointNeedData.u16HRWheelPulseValid = FALSE;

    GstrCalPointNeedData.u16SteerWheelAngleVaild = FALSE;

    GstrCalPointNeedData.u8RLWheelDir = 0;
    GstrCalPointNeedData.u8RRWheelDir = 0;

    GstrCalPointNeedData.u8WheelRunDir = 0;

    GstrCalPointNeedData.fLeftDis = 0;
    GstrCalPointNeedData.fRightDis = 0;
    GstrCalPointNeedData.fMiddleDis = 0;

    GstrCalPointNeedData.fCarTurnRadius = 0;
    GstrCalPointNeedData.u16EpsAngle = 0;
    GstrCalPointNeedData.fEpsAnglebak = 0x7FFF;
    GstrCalPointNeedData.u8EpsDirection = 0;

    GstrCalPointNeedData.u16VehicleSpeed = 0;
    GstrCalPointNeedData.enuVehicleGear = CAN_CAR_GEAR_P;
    GstrCalPointNeedData.u8GearSaveBak = 0;

    GstrCalPointNeedData.fXChange = 0;
    GstrCalPointNeedData.fYChange = 0;
    GstrCalPointNeedData.fCarBodyAngleChange = 0;

    /*根据车型初始化卡尔曼滤波的参数*/
    ReadCAN_AppSignal_VEH_Type(&LenuVEH_Type);
    ReadCAN_AppSignal_VehicleWheel(&LenuVehicleWheel);
    KalmanFilter_Init(LenuVEH_Type, LenuVehicleWheel);


    //车身数据初始化
    GpstrCarParaData = LvpCarParaData;
}


/******************************************************************
* 函数名称: CPOS_Privated_DataUpdate
*
* 功能描述: Apa车身坐标定位模块数据更新 ，10ms更新一次
*
* 输入参数: CalPointNeedDataType *LvpCalPointNeedData
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 无
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
void CPOS_Privated_DataUpdate(CPOS_CalPointNeedDataType *LvpCalPointNeedData)
{
    /* 获取当前EPS转角 */
    /* 左正右负 */
    GstrCalPointNeedData.u8EpsDirection = LvpCalPointNeedData->u8EpsDirection;
	
    GstrCalPointNeedData.u16EpsAngle = LvpCalPointNeedData->u16EpsAngle;

    /** @brief 防止转角数据无效被置成0xFFFF数据过大导致后面除数为0问题 */
    if(GstrCalPointNeedData.u16EpsAngle >= 6000)
    {
        GstrCalPointNeedData.u16EpsAngle = 6000;
    }
    /* 直行没角度 */
    if(LvpCalPointNeedData->u8StraightFlag == TRUE)
    {
        //GstrCalPointNeedData.u16EpsAngle = 0u;
    }

    /* 获取车速 */
    GstrCalPointNeedData.u16VehicleSpeed = LvpCalPointNeedData->u16VehicleSpeed;

    /* 获取档位 */
    GstrCalPointNeedData.enuVehicleGear = LvpCalPointNeedData->enuVehicleGear;

    /* 获取左后轮轮速脉冲 */
    /* 获取右后轮轮速脉冲 */
    GstrCalPointNeedData.u16HLWheelPulse = LvpCalPointNeedData->u16HLWheelPulse;
    GstrCalPointNeedData.u16HRWheelPulse = LvpCalPointNeedData->u16HRWheelPulse;

    /* 获取轮速脉冲方向 */
    GstrCalPointNeedData.u8RLWheelDir = LvpCalPointNeedData->u8RLWheelDir;
    GstrCalPointNeedData.u8RRWheelDir = LvpCalPointNeedData->u8RRWheelDir;

    GstrCalPointNeedData.u16HLWheelPulseValid = LvpCalPointNeedData->u16HLWheelPulseValid;
    GstrCalPointNeedData.u16HRWheelPulseValid = LvpCalPointNeedData->u16HRWheelPulseValid;
    GstrCalPointNeedData.u16SteerWheelAngleVaild = LvpCalPointNeedData->u16SteerWheelAngleVaild;

    if(GstrCalPointNeedData.u16HLWheelPulseBak == 0xFFFFu)
    {
        GstrCalPointNeedData.u16HLWheelPulseBak = GstrCalPointNeedData.u16HLWheelPulse;  //获取轮速脉冲
    }
    
    if(GstrCalPointNeedData.u16HRWheelPulseBak == 0xFFFFu)
    {
        GstrCalPointNeedData.u16HRWheelPulseBak = GstrCalPointNeedData.u16HRWheelPulse;  //获取轮速脉冲
    }

	/*获取轮速*/
	ReadCAN_AppSignal_Wheel_Speed(&GstrCalPointNeedData.u16WheelSpeedRL, CAR_RL_WHEEL);
	ReadCAN_AppSignal_Wheel_Speed(&GstrCalPointNeedData.u16WheelSpeedRR, CAR_RR_WHEEL);
}


/*********************************************************************
 * 函数名称: CPOS_Privated_CalDataFunc
 *
 * 功能描述: 计算坐标数据函数
 *
 * 输入参数: id
 *
 * 输出参数: 无
 *
 * 其它说明: 无
 *
 * 修改日期     版本号      修改人      修改内容
 *
 ********************************************************************/
void CPOS_Privated_CalDataFunc(uint8 id)
{
    float LfCar_YawAngle;

	ReadCAN_AppSignal_Car_Yaw_Angle(&LfCar_YawAngle);

    if(GstrCalPointNeedData.fCar_YawAngleBak[id] == 0xff)
    {
        GstrCalPointNeedData.fCarBodyAngleChange = 0;
    }
    else
    {
        GstrCalPointNeedData.fCarBodyAngleChange = (LfCar_YawAngle - GstrCalPointNeedData.fCar_YawAngleBak[id]);
    }
	
	GstrCalPointNeedData.fCarBodyOriginalAngle[id] += GstrCalPointNeedData.fCarBodyAngleChange;
	GstrCalPointNeedData.fCar_YawAngleBak[id] = LfCar_YawAngle;

	GstrCalPointNeedData.fCarBodyAngle[id] = GstrCalPointNeedData.fCarBodyOriginalAngle[id];

	while (GstrCalPointNeedData.fCarBodyAngle[id] > 3.141592)
	{
		GstrCalPointNeedData.fCarBodyAngle[id] -= 6.283184;
	}
	while (GstrCalPointNeedData.fCarBodyAngle[id] < -3.141592)
	{
		GstrCalPointNeedData.fCarBodyAngle[id] += 6.283184;
	}

    GstrCalPointNeedData.u16HLWheelPulseBak = GstrCalPointNeedData.u16HLWheelPulse;
    GstrCalPointNeedData.u16HRWheelPulseBak = GstrCalPointNeedData.u16HRWheelPulse;

    if(GstrCalPointNeedData.u8WheelRunDir == CAR_WHEEL_DIR_BACKWARD)
    {
        GstrCalPointNeedData.fXChange = -GstrCalPointNeedData.fMiddleDis * cosf(GstrCalPointNeedData.fCarBodyAngle[id]);
        GstrCalPointNeedData.fYChange = -GstrCalPointNeedData.fMiddleDis * sinf(GstrCalPointNeedData.fCarBodyAngle[id]);
    }
    else
    {
        GstrCalPointNeedData.fXChange = GstrCalPointNeedData.fMiddleDis * cosf(GstrCalPointNeedData.fCarBodyAngle[id]);
        GstrCalPointNeedData.fYChange = GstrCalPointNeedData.fMiddleDis * sinf(GstrCalPointNeedData.fCarBodyAngle[id]);
    }
}


/******************************************************************
* 函数名称: CPOS_Privated_GetWheelRunDist
*
* 功能描述: 获取车轮运行的距离。
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 无
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
void CPOS_Privated_GetWheelRunDist(void)
{
    uint16 LwLWhleePuleTemp,LwRWhleePuleTemp;

    /* 左轮   */
    if(GstrCalPointNeedData.u16HLWheelPulse < GstrCalPointNeedData.u16HLWheelPulseBak)
    {
        LwLWhleePuleTemp = 1 + GstrCalPointNeedData.u16HLWheelPulse + (WHEEL_PULSE_MAX_VALUE - GstrCalPointNeedData.u16HLWheelPulseBak);
    }
    else
    {
        LwLWhleePuleTemp = GstrCalPointNeedData.u16HLWheelPulse - GstrCalPointNeedData.u16HLWheelPulseBak;
    }

    /* 右轮 */
    if(GstrCalPointNeedData.u16HRWheelPulse < GstrCalPointNeedData.u16HRWheelPulseBak)
    {
        LwRWhleePuleTemp = 1 + GstrCalPointNeedData.u16HRWheelPulse + (WHEEL_PULSE_MAX_VALUE - GstrCalPointNeedData.u16HRWheelPulseBak);
    }
    else
    {
        LwRWhleePuleTemp = GstrCalPointNeedData.u16HRWheelPulse - GstrCalPointNeedData.u16HRWheelPulseBak;
    }
    if(GstrCalPointNeedData.u8WheelRunDir == CAR_WHEEL_DIR_BACKWARD)
    {
        GstrCalPointNeedData.fLeftDis = GpstrCarParaData->fOneWheelPulseDisAtRLBackward * ((float)LwLWhleePuleTemp);
        GstrCalPointNeedData.fRightDis = GpstrCarParaData->fOneWheelPulseDisAtRRBackward * ((float)LwRWhleePuleTemp);
    }
    else
    {
        GstrCalPointNeedData.fLeftDis = GpstrCarParaData->fOneWheelPulseDisAtRLForward * ((float)LwLWhleePuleTemp);
        GstrCalPointNeedData.fRightDis = GpstrCarParaData->fOneWheelPulseDisAtRRForward * ((float)LwRWhleePuleTemp);
    }

    if (ABS(GstrCalPointNeedData.u16WheelSpeedRR, GstrCalPointNeedData.u16WheelSpeedRL) > 310)
	{
		GstrCalPointNeedData.fMiddleDis = (GstrCalPointNeedData.fLeftDis < GstrCalPointNeedData.fRightDis ? GstrCalPointNeedData.fLeftDis : GstrCalPointNeedData.fRightDis);
	}
	else
	{
		GstrCalPointNeedData.fMiddleDis = GstrCalPointNeedData.fLeftDis + GstrCalPointNeedData.fRightDis;
		GstrCalPointNeedData.fMiddleDis = GstrCalPointNeedData.fMiddleDis / 2;
	}
    // GstrCalPointNeedData.fMiddleDis = GstrCalPointNeedData.fLeftDis + GstrCalPointNeedData.fRightDis;
    // GstrCalPointNeedData.fMiddleDis = GstrCalPointNeedData.fMiddleDis / 2;
}


/*高速直线自动标定轮速脉冲系数*/
void CPOS_Privated_AutoCaliWheelPulse(void)
{
	static float LfFirstYaw, LfRLDis = 0, LfRRDis = 0;
	static uint16 Lu16RLTotalPulse = 0, Lu16RRTotalPulse = 0;
	static uint16 Lu16RLLastPulse, Lu16RRLastPulse;
	static uint16 Lu16WheelSpeedRLLast, Lu16WheelSpeedRRLast;
	static uint8 Lu8InitFlag = 0;
    static uint16 Lu16AutoCaliTime[2] = {0};
    float LfWheelSpeedDt;

	switch (GstrCalPointNeedData.u8CaliStatus)
	{
	case 0:
		if (GstrCalPointNeedData.u16EpsAngle < 30)
		{
			if (GstrCalPointNeedData.u8EnableCalFlg[0])
			{
				GstrCalPointNeedData.u8CaliStatus = 1;
				LfFirstYaw = GstrCalPointNeedData.fCarBodyAngle[0];
			}
		}
		break;
	case 1:
		if (GstrCalPointNeedData.u16EpsAngle < 30
			&& ABS(LfFirstYaw, GstrCalPointNeedData.fCarBodyAngle[0]) < (3 / 57.2957795)
			&& GstrCalPointNeedData.u16WheelSpeedRL > 3000
			&& GstrCalPointNeedData.u16WheelSpeedRR > 3000)
		{
			if (!Lu8InitFlag)
			{
				Lu8InitFlag = 1;
				Lu16RLLastPulse = GstrCalPointNeedData.u16HLWheelPulse;
				Lu16RRLastPulse = GstrCalPointNeedData.u16HRWheelPulse;
				Lu16WheelSpeedRLLast = GstrCalPointNeedData.u16WheelSpeedRL;
				Lu16WheelSpeedRRLast = GstrCalPointNeedData.u16WheelSpeedRR;
                Lu16AutoCaliTime[1] = READTAUDCnt_1600ns();
				break;
			}
            
            Lu16AutoCaliTime[0] = READTAUDCnt_1600ns();
            LfWheelSpeedDt = (float)(Lu16AutoCaliTime[0] - Lu16AutoCaliTime[1]) * 1.6 / 1000000;
            
			/*单位是cm*/
			LfRLDis += (float)(Lu16WheelSpeedRLLast + GstrCalPointNeedData.u16WheelSpeedRL) / 2 / 3.6 * LfWheelSpeedDt;
			LfRRDis += (float)(Lu16WheelSpeedRRLast + GstrCalPointNeedData.u16WheelSpeedRR) / 2 / 3.6 * LfWheelSpeedDt;

            Lu16AutoCaliTime[1] = Lu16AutoCaliTime[0];

			if (GstrCalPointNeedData.u16HLWheelPulse >= Lu16RLLastPulse)
			{
				Lu16RLTotalPulse += GstrCalPointNeedData.u16HLWheelPulse - Lu16RLLastPulse;
			}
			else
			{
				Lu16RLTotalPulse += 65535u - Lu16RLLastPulse + 1 + GstrCalPointNeedData.u16HLWheelPulse;
			}

			if (GstrCalPointNeedData.u16HRWheelPulse >= Lu16WheelSpeedRRLast)
			{
				Lu16RRTotalPulse += GstrCalPointNeedData.u16HRWheelPulse - Lu16RRLastPulse;
			}
			else
			{
				Lu16RRTotalPulse += 65535u - Lu16RRLastPulse + 1 + GstrCalPointNeedData.u16HRWheelPulse;
			}

			Lu16RLLastPulse = GstrCalPointNeedData.u16HLWheelPulse;
			Lu16RRLastPulse = GstrCalPointNeedData.u16HRWheelPulse;
			Lu16WheelSpeedRLLast = GstrCalPointNeedData.u16WheelSpeedRL;
			Lu16WheelSpeedRRLast = GstrCalPointNeedData.u16WheelSpeedRR;
		}
		else if (LfRLDis > 20000 && LfRRDis > 20000)
		{
			GstrCalPointNeedData.u8CaliStatus = 2;
		}
		else
		{
			GstrCalPointNeedData.u8CaliStatus = 0;
			LfRLDis = 0;
			LfRRDis = 0;
			Lu16RLTotalPulse = 0;
			Lu16RRTotalPulse = 0;
			Lu8InitFlag = 0;
		}
		break;
	case 2:
		GpstrCarParaData->fOneWheelPulseDisAtRLForward = LfRLDis * 10 / Lu16RLTotalPulse;
		GpstrCarParaData->fOneWheelPulseDisAtRRForward = LfRRDis * 10 / Lu16RRTotalPulse;
		GstrCalPointNeedData.u8CaliStatus = 0;
		LfRLDis = 0;
		LfRRDis = 0;
		Lu16RLTotalPulse = 0;
		Lu16RRTotalPulse = 0;
		Lu8InitFlag = 0;
		break;
	default:
		GstrCalPointNeedData.u8CaliStatus = 0;
		LfRLDis = 0;
		LfRRDis = 0;
		Lu16RLTotalPulse = 0;
		Lu16RRTotalPulse = 0;
		Lu8InitFlag = 0;
	}
}


/******************************************************************
* 函数名称: CPOS_Privated_CalCurCarPointFunc
*
* 功能描述: 计算当前车辆坐标 主循环调度 10ms运行一次
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 无
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
void CPOS_Privated_CalCurCarPointFunc(void)
{
    uint8 i;
    uint64 Lu64TimeStamp;
    static uint8 LcCheckErrOnceInit = FALSE;

    Lu64TimeStamp = GetCANTimeSysn_GlobalTimeBase();
    CPOSSgnMag_WriteCalTimeStamp(Lu64TimeStamp);


    /* 校验轮速脉冲和方向盘角度参数有效性 */
    if((GstrCalPointNeedData.u16HLWheelPulseValid == FALSE)||(GstrCalPointNeedData.u16HRWheelPulseValid == FALSE)
        || (GstrCalPointNeedData.u16SteerWheelAngleVaild == FALSE))
    {
        if(!LcCheckErrOnceInit)
        {
            CPOS_Init();
            LcCheckErrOnceInit = TRUE;
        }
        return;
    }

    LcCheckErrOnceInit = FALSE;

    if(((GstrCalPointNeedData.u8RLWheelDir == CAR_WHEEL_DIR_FORWARD)&&
        (GstrCalPointNeedData.u8RRWheelDir == CAR_WHEEL_DIR_BACKWARD))
      ||((GstrCalPointNeedData.u8RLWheelDir == CAR_WHEEL_DIR_BACKWARD)&&
        (GstrCalPointNeedData.u8RRWheelDir == CAR_WHEEL_DIR_FORWARD)))
    {
        /*不处理*/
        GstrCalPointNeedData.u8WheelRunDir = CAR_WHEEL_DIR_STOP;
        CPOSSgnMag_WriteCarMoveDirect(CAR_WHEEL_DIR_STOP);
        return;
    }
    else if((GstrCalPointNeedData.u8RLWheelDir == CAR_WHEEL_DIR_FORWARD)
          ||(GstrCalPointNeedData.u8RRWheelDir == CAR_WHEEL_DIR_FORWARD))
    {
        GstrCalPointNeedData.u8WheelRunDir = CAR_WHEEL_DIR_FORWARD;
    }
    else if((GstrCalPointNeedData.u8RLWheelDir == CAR_WHEEL_DIR_BACKWARD)
          ||(GstrCalPointNeedData.u8RRWheelDir == CAR_WHEEL_DIR_BACKWARD))
    {
        GstrCalPointNeedData.u8WheelRunDir = CAR_WHEEL_DIR_BACKWARD;
    }
    else
    {
        if(GstrCalPointNeedData.enuVehicleGear == CAN_CAR_GEAR_R)
        {
            GstrCalPointNeedData.u8WheelRunDir = CAR_WHEEL_DIR_BACKWARD;
        }
        else if(GstrCalPointNeedData.enuVehicleGear == CAN_CAR_GEAR_D)
        {
            GstrCalPointNeedData.u8WheelRunDir = CAR_WHEEL_DIR_FORWARD;
        }
        else
        {
            /*不处理*/
            GstrCalPointNeedData.u8WheelRunDir = CAR_WHEEL_DIR_STOP;
            CPOSSgnMag_WriteCarMoveDirect(CAR_WHEEL_DIR_STOP);
            return;
        }
    }

    CPOSSgnMag_WriteCarMoveDirect(GstrCalPointNeedData.u8WheelRunDir);
    
    CPOSSgnMag_WriteCarTurnRadian((uint32)GfCarTurnRadian);

    CPOS_Privated_GetWheelRunDist();  /* 计算距离和角度 */

    for(i = 0;i < RELATION_MOD_NUM_COOR;i++)
    {
        if(GstrCalPointNeedData.u8EnableCalFlg[i])
        {
            CPOS_Privated_CalDataFunc(i);
            
            GstrCurCarPointData[i].fX += GstrCalPointNeedData.fXChange;
            GstrCurCarPointData[i].fY += GstrCalPointNeedData.fYChange;
            GstrCurCarPointData[i].fAngle = GstrCalPointNeedData.fCarBodyAngle[i];//弧度   * ANGLE_TO_RADIAN_COOR;
            GstrCurCarPointData[i].fDrvDis += GstrCalPointNeedData.fMiddleDis;
            GstrCurCarPointData[i].TurnRadius = GfCarTurnRadian;
            GstrCurCarPointData[i].fCosAngle = cosf(GstrCalPointNeedData.fCarBodyAngle[i]);
            GstrCurCarPointData[i].fSinAngle = sinf(GstrCalPointNeedData.fCarBodyAngle[i]);
            GstrCurCarPointData[i].u8VehDir = GstrCalPointNeedData.u8WheelRunDir;

            CPOSSgnMag_WriteCarCoorData(GstrCurCarPointData[i],i);
        }
        /* 计算坐标未使能，坐标清零 */
        else
        {
            GstrCurCarPointData[i].fX = 0;
            GstrCurCarPointData[i].fY = 0;
            GstrCurCarPointData[i].fAngle = 0;
            GstrCurCarPointData[i].fDrvDis = 0;
            GstrCalPointNeedData.fCarBodyAngle[i] = 0;
            GstrCalPointNeedData.fCarBodyOriginalAngle[i] = 0;
            GstrCalPointNeedData.fCar_YawAngleBak[i] = 0xff;
            GstrCurCarPointData[i].TurnRadius = 0;
            GstrCurCarPointData[i].fCosAngle = cosf(GstrCalPointNeedData.fCarBodyAngle[i]);
            GstrCurCarPointData[i].fSinAngle = sinf(GstrCalPointNeedData.fCarBodyAngle[i]);
            GstrCurCarPointData[i].u8VehDir = 0;
        }
    }
    // CPOS_Privated_AutoCaliWheelPulse();
}


/******************************************************************
* 函数名称: CPOS_Privated_SetEnableCalFlg
*
* 功能描述: 获取使能句柄id
*
* 输入参数: 无
*
* 输出参数: 无
*
* 返 回 值: 成功:ID / 失败:0xFF
*
* 其它说明: 无
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
uint8 CPOS_Privated_SetEnableCalFlg(void)
{
    uint8 i;
    
    if((GstrCalPointNeedData.u16HLWheelPulseValid == FALSE)||(GstrCalPointNeedData.u16HRWheelPulseValid == FALSE)
            || (GstrCalPointNeedData.u16SteerWheelAngleVaild == FALSE))
    {
        return 0xFF;
    }

    for(i = 0;i < RELATION_MOD_NUM_COOR;i++)
    {
        if(0 == GstrCalPointNeedData.u8EnableCalFlg[i])
        {
            GstrCalPointNeedData.u8EnableCalFlg[i] = 1;
            return i; 
        }
    }

    return 0xFF;
}


/******************************************************************
* 函数名称: CPOS_Privated_ClrEnableCalFlg
*
* 功能描述: 清除句柄id
*
* 输入参数: id
*
* 输出参数: 无
*
* 返 回 值: 无
*
* 其它说明: 无
*
* 修改日期      版本号      修改人      修改内容

**********************************************************************/
void CPOS_Privated_ClrEnableCalFlg(uint8 id)
{
    if(id >= RELATION_MOD_NUM_COOR)
    {
        return;
    }

    GstrCurCarPointData[id].fX = 0;
    GstrCurCarPointData[id].fY = 0;
    GstrCurCarPointData[id].fAngle = 0;
    GstrCurCarPointData[id].fDrvDis = 0;
    GstrCalPointNeedData.u8EnableCalFlg[id] = 0;
    GstrCalPointNeedData.fCarBodyAngle[id] = 0;
    GstrCalPointNeedData.fCarBodyOriginalAngle[id] = 0;
    GstrCalPointNeedData.fCar_YawAngleBak[id] = 0xff;
}

/******************************************************************************/
/******************************************************************************/
/******************************* Public Function ******************************/
/******************************************************************************/
/******************************************************************************/
